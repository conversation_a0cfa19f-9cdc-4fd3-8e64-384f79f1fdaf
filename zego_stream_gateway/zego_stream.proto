syntax = "proto3";

package zego_stream;

option go_package = "golang.52tt.com/protocol/services/rcmd/zego_stream_gateway/zego_stream";

enum EventType {
  EventTypeNone    = 0;

  // common: 1-10
  ZegoError      = 1;
  ServerError    = 2;
  ClientError    = 3;
  Audio          = 4;

  // client action: 11-99
  Exit            = 10; // exit room
  Login           = 11; // login room
  StartPullStream = 12; // start pull stream
  StopPullStream  = 13; // stop pull stream
  SendTextMessage = 14; // send broadcast message

  // zego callback: 100-199
  LoginResult    = 101; // login result
  StreamUpdate   = 102; // stream update
  RoomStateUpdate = 103; // room state update

  // business: 200-299
  Heartbeat      = 200;
  HeartbeatAck   = 201;
  GetStreamList  = 202;
  GetStreamListResult = 203;

}

enum ZegoErrCode {
  ZegoErrCommonSuccess = 0;

  // error code defined by zego proxy
  ZegoErrConnIdleTooLong = 1000;


  // error code defined by zego sdk
  // please refer: libs/zego_lib/include/internal/include/zego-express-errcode.h

  // Description: Room login authentication failed.
  // Cause: login set token error or token expired. 
  // Solutions: set new token.
  ZegoErrRoomAuthenticationFailed = 1002033;

  // Description: The user was kicked out of the room. 
  // <br>Cause: Possibly because the same user ID is logged in on another device. 
  // <br>Solutions: Use a unique user ID.
  ZegoErrRoomKickOut   = 1002050;
}

message EventWrap {
  EventType  event = 1;
  string     json  = 2;
  bytes      data  = 3;
}

service ZegoStream {
  rpc StreamTransfer(stream EventWrap) returns (stream EventWrap);
}
