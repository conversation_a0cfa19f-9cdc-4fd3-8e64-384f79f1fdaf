syntax = "proto3";

package ga.coin_proxy;

import "ga_base.proto";

option go_package = "golang.52tt.com/protocol/app/coin_proxy";
option java_package = "com.yiyou.ga.model.proto";

message GetRiskResultRequest {
  ga.BaseReq base_req = 1;
  // 风控场景
  RiskScene risk_scene = 2;
  // 用户ID
  uint64 uid = 3;
}

message GetRiskResultResponse {
  ga.BaseResp base_resp = 1;
}

// 风控场景
enum RiskScene {
  RISK_SCENE_UNSPECIFIED = 0; // 未知场景
  RISK_SCENE_HALFCHARGE = 1; // 端内安卓半屏充值
  RISK_SCENE_FULLCHARGE = 2; //  安卓全屏充值
  RISK_SCENE_IOSCHARGE = 3; // 端内iOS充值
  RISK_SCENE_WECHATPUBLICCHARGE = 4; // 端外微信公众号充值
  RISK_SCENE_ALIPAYWAPCHARGE = 5; // 端外支付宝网页充值
}
