syntax = "proto2";

package ga.im;

import "ga_base.proto";
import "userpresent/userpresent_.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/im";

//IM 消息
enum IM_MSG_TYPE {
    //1文本 2图片 3语音 4视频 5可扩展的信息(名片、网页、功能跳转链接等)
    TEXT_MSG = 1;
    IMG_MSG = 2;
    VOICE_MSG = 3;
    VIDEO_MSG = 4;
    EXTENDED_MSG = 5;
    // GUILD_APPLY = 6;		// 会长收到入会申请
    SYSTEM_NOTIFY = 7;        // 系统通知
    GUILD_QUIT = 8;            // 用户退会通知
    EXPRESSION_MSG = 9; //表情包表情消息
    GUILD_BC = 10;        // 公会广播
    GUILD_JOIN = 11;    //用户加入工会
    GUILD_ADMIN = 12;   //副会长任命
    GUILD_GROUP_JOIN = 13;          //用户加入群
    GUILD_GROUP_OWNER = 14;    //群主任命
    GUILD_GROUP_ADMIN = 15;    //群管理任命
    GUILD_ASST_NOMINATE = 16;            //公会消息助手 -- 任命
    GUILD_ASST_APPLY_JOIN_GUILD = 17;    //公会消息助手 -- 入会申请
    GUILD_ASST_APPLY_JOIN_GROUP = 18;    //公会消息助手 -- 入群申请
    GUILD_ASST_GIFT_PKG_RESULT = 19;     //公会消息助手 -- 礼包申请结果
    GUILD_ASST_QUIT_GUILD = 20;          //公会消息助手 -- 退会通知
    NEW_EXTENDED = 21;                   //新的扩展消息
    OFFICAIL_MESSAGE_SINGLE = 22; //公众号 单消息  -- v1.5, NewMessageSync.ext对应OfficialMessageSingle协议.
    OFFICAIL_MESSAGE_BUNCH = 23;  //公众号 消息簇  -- v1.5, NewMessageSync.ext对应OfficialMessageBunch

    // seesion 临时群实时语音版本 该功能已经下线
    CALL_IN = 24;                    // 召集令 （该功能已经下线）
    SESSION_CREATE_MSG = 25;        // 创建开黑房间，NewMessageSync.ext对应SessionCreateMsg （该功能已经下线）
    SESSION_CLOSE_MSG = 26;            // 关闭开黑房间，NewMessageSync.ext对应SessionCloseMsg  （该功能已经下线）
    ACCEPT_CALL_IN = 27;            // 接受召集令 （该功能已经下线）
    CALL_IN_END = 28;                // 召集令结束 （该功能已经下线）

    //v1.8.0 ----群组 ---- add by hjinw
    GROUP_ASST_APPLY_JOIN_GROUP = 29;   // 群消息助手 -- 入群申请
    GROUP_ASST_QUIT_GROUP = 30;         // 群消息助手 -- 退群通知
    TGROUP_JOIN_GROUP = 31;                // 群组：加群
    AIR_TICKET = 32; //频道飞机票
    GUILD_ASST_NOTIFY = 33;                // 公会消息助手 -- 通用通知
    AT_SOMEONE_MSG = 34; //群聊@Xxx的信息类型
    GUILD_MEMBER_TITLE = 35; // 公会成员称号
    GUILD_ASST_COMMON_URL_NOTIFY = 36;                // 公会消息助手 -- 带客户端跳转链接的通用通知样式
    TT_COMMON_TEXT_NOTIFY = 37;                        // TT OFFICAL 消息助手 -- 带跳转链接和高亮展示的通用通知样式
    AT_EVERYONE_MSG = 38; //群聊@all的信息类型
    CUSTOM_EMOTICON_MSG = 39; // 自定义表情
    GUILD_CIRCLE_ANNOUNCEMENT = 40; // 公会圈的公告
    CANCEL_MSG = 41;    //撤销消息
    GAME_PREORDER_TEXT_NOTIFY = 42;        // 公会总群--带客户端跳转链接的公会成员预约通知样式
    TGROUP_INVITE_MSG = 43;       // 游戏兴趣群邀请加群消息。
    FIND_FRIENDS_MATED_UP_MSG = 44;        // 扩圈配对成功消息
    FIND_FRIENDS_LIKED_NOTIFY = 45;        // 扩圈被喜欢时的通知消息
    GAME_CENTER_POP_GAME_NOTIFY = 46;   // 游戏中心游戏推荐
    CHANNEL_DATING_GAME_LIKE_USER_NOTIFY = 47; // 相亲房 心动消息推送(配对成功)(ext消息里面包含了channel_dating_game_.proto里 GamePushLikeUserRecord定义的内容)

    FIND_FRIENDS_ICEBREAK_MATCH = 48; // 破冰交友 配对信息(ext消息里面包含了客户端自定义的透传数据)

    SENSITIVE_NOTIFY_MSG = 49;        // 敏感提示通知

    INTERACTIVE_UNREAD_NOTIFY = 50; //异步内容新增未读互动推送

    CHANNEL_PK_RESULT_NOTIFY = 51; // 房间PK结果信息 (ext 消息里面包含了 channel_.proto里 ChannelVotePKRankInfo)

    KNOCK_INROOM_NOTIFY = 52; //  敲门进房通知

    CIVILIZATION_CONVENTION = 53; // 文明公约

    TT_URL_TEXT_NOTIFY = 54;                     // TT OFFICAL 消息助手 -- 带多个跳转链接

    STRANGER_INFO_CARD = 55; // 陌生人信息卡片，客户端本地插入
    UGC_SHARE_POST = 56; // 分享帖子消息，客户端从分享消息转换
    SEND_POST_TIPS = 57; // 提示发送动态消息，客户端本地插入
    INVITE_FOLLOW_BACK = 58; // 邀请关注消息
    FOLLOW_TIPS = 59; // 关注对方提示，客户端收到对方第一条消息时本地插入

    RICH_TEXT_MSG = 60;                //富文本消息 不可使用
    PERSONAL_RICH_TEXT_MSG = 61;    //个性化富文本消息
    CUE_MSG = 63; // cue
    SEND_PLAY_TOGETHER = 64;//发送一起进房消息
    ACCEPT_PLAY_TOGETHER = 65;//接受一起进房的邀请
    NEW_POST_TIPS = 66; // 聊天列表新动态提醒，客户端本地插入

    INTERACTION_INTIMACY_GIFT = 67; // 礼物显示
    IM_PRESENT = 68; // IM送礼

    RICH_TEXT_MSG_WITH_NOTIFY = 69; //带提醒的富文本消息
    PERSONAL_RICH_TEXT_MSG_WITH_NOTIFY = 70; //带提醒的个性化富文本消息

    MASTER_APPRENTICE_INVITE_MSG = 71; // 师徒关系邀请消息 MasterApprenticeInviteMsg
    MASTER_APPRENTICE_ESTABLISH_MSG = 72; // 师徒关系建立消息 MasterApprenticeEstablishMsg
    MASTER_APPRENTICE_INTRODUCTION_MSG = 73; // 徒弟收到邀请说明消息 MasterApprenticeIntroductionMsg

    CHANNEL_OPEN_GAME_INVITE_MSG = 75; // 游戏服务，游戏im内邀请消息 ChannelOpenGameInviteMsg

    IM_PRESENT_NEW = 76; // 亲密度以外的新版本IM送礼消息
    PLAYMATE_CARD = 77; // 玩伴卡片展示
    DEL_MSG = 78; // 删除消息FELLOW_INVITE_MSG
    CHAT_CARD = 79; // 扩列卡片展示
    CHAT_CARD_HI_MSG = 80; // 扩列卡片打招呼消息
    SUPER_PLAYER_CUSTOM_EMOTICON_MSG = 81; // 会员专属自定义表情
    SLIP_NOTE_MSG = 82; // 来自小纸条
    CHANNEL_CP_GAME_RESULT_USER_NOTIFY = 83; // CP战，im结果推送 channel-cp-game-logic_.proto CpGameWinImOpt
    FELLOW_INVITE_MSG = 84; // 亲密关系邀请函
    FELLOW_UPGRADE_MSG = 85; // 亲密关系升级消息
    CHANNEL_GUIDE_APPOINMENT = 86; //预约
    GAIN_RARE = 87; // 获得稀缺关系
    GROUP_ANNOUNCEMENT = 88;//新版群公告
    GROUP_JOIN_CHECK = 89;//用户帶答案審批的消息
    GROUP_JOIN_ADMIN_ACCEPT = 90;//用戶進群管理員收到的消息
    GROUP_ANNOUNCEMENT_CHECK_REJECT = 91;//群公告审核不过的
    GROUP_JOIN_VERIFY_QUESTION_SET = 92;//用戶进群设置的回调消息
    BRAND_SHARE = 93;//厂牌分享
    WERWOLF_WIN = 94; //狼人杀胜利消息 见channel-open-game-logic_.proto WerwolfWinMsg
    SHARE_GAME_MSG = 95; //分享游戏消息，见ShareGameMsg
    SHARE_SCENARIO_MSG = 96; //分享剧本消息，见mystery-place-logic_.proto ShareScenarioMsg
    PGC_CHANNEL_PK_MVP_MSG = 97;    // 娱乐房跨房pk mvp信息
    MUSE_POST_INTERACTIVE_LIKE_MSG = 98; // 碎碎念点赞互动消息，见muse-post-logic_.proto MuseInteractiveImMsg
    MUSE_POST_INTERACTIVE_COMMENT_MSG = 99; // 碎碎念评论互动消息，见muse-post-logic_.proto MuseInteractiveImMsg
    MUSE_POST_INTERACTIVE_COMMENT_AT_MSG = 100; // 碎碎念评论互动消息,带at，见muse-post-logic_.proto MuseInteractiveImMsg
    MUSE_POST_INTERACTIVE_LIKE_AT_MSG = 101; // 碎碎念点赞互动消息,带at，见muse-post-logic_.proto MuseInteractiveImMsg
    MIJING_GOOD_COMMENT = 102; // 谜境好评
    PERFECT_MATCH_GAME_RESULT_USER_NOTIFY = 103; // 天配房间玩法，im结果推送 perfect_couple_match_logic.proto PerfectCpGameResultImOpt
    MIJING_INVITE_CARD_MSG = 104; //谜境密室逃脱邀请卡消息
    GLORY_CELEBRITY_SHARE_MSG = 105;       //名流殿堂分享消息
    SCENARIO_SHARE_MSG = 106; // 剧本详情页分享消息
    CHANNEL_LIVE_MULTI_PK_BEST_PARTNER_MSG = 107;  // 直播多人pk最佳拍档信息
    SOCIAL_COMMUNITY_INVITE_KERNEL_MEMBER = 108; //社群邀请核心成员
    USER_RECALL_NOTIFY_MSG = 109; // 用户召回im消息推送
    GAME_PAL_CARD_GAME_NICKNAME_MSG = 110; // 游戏搭子卡发送游戏昵称
    GAME_PAL_CARD_FIRST_IM_CARD_MSG = 111; // 游戏搭子卡首次im后发送的卡片
    GAME_PAL_CARD_IM_GUIDE_MSG = 112;   // 游戏搭子 im 引导
    MIJING_PARTNER_CARD_INVITE_MSG = 113; // 谜境找搭子消息
    ESPORT_ORDER_MSG = 114; // 电竞指导订单消息  see esport-logic.proto EsportOrderImMsg
    ESPORT_SYS_MSG = 115;   // 电竞指导系统消息  see esport-logic.proto EsportImSysMsg
    SCENARIO_AI_PROMPT_AUTO_REPLY_MSG = 116; // 剧本相关AI对接泼墨体自动回复
    SOCIAL_COMMUNITY_POST_SHARE_MSG = 117; //社群讨论贴分享   see ugc_non_public.proto   NonPublicPostShareMsg
    SOCIAL_COMMUNITY_MEMBER_CARD = 118; // 社团成员名片 see social-community-logic.proto SocialCommunityMemberCard
    ESPORT_GAME_CARD = 119; // 电竞指导游戏卡片消息  see esport-logic.proto EsportGameCardImMsg
    ESPORT_GAME_SELF_INTRO_TO_VISITOR = 120; // 电竞大神游戏简介消息 to 访客  see esport-logic.proto EsportCoachSelfIntroIMMsg
    SOCIAL_COMMUNITY_ASSISTANT_POST_PUSH_MESSAGE = 121; // 社团助手的讨论帖推送消息 see muse_social_community_logic/muse_social_community_logic.proto SocialCommunityAssistantPostPushMessage
    GUILD_MANAGE_ROLE_INVITE_MSG = 122; // 会长经营后台角色邀请消息 see revenue_base.proto GuildManageRoleInviteMsg
    GAME_INVITE_ROOM_MSG = 123; //游戏开黑邀请
    ESPORT_GAME_SELF_INTRO_TO_VISITOR_V2 = 124; // 电竞大神游戏简介消息 to 访客（v2版本）  see esport-logic.proto EsportCoachSelfIntroIMMsg
    RICHER_BIRTHDAY_NOTICE_MSG = 125; // 大R生日提醒im
    VIRTUAL_IMAGW_BIND_INVITE_MSG = 126; // 虚拟形象绑定邀请消息 see virtual-image-logic.proto VirtualImageBindInviteMsg
    ASSISTANT_PUSH_GAME_PAL = 127; // TT助手搭子卡推荐推送消息 see game_pal_logic.proto AssistantPushGamePalData
    ASSISTANT_PUSH_GAME_PAL_ZONE_POPUP = 128; // 开黑专区签到弹窗和通知-提醒弹窗和推送
    CommonCardMsg = 129; // 通用卡片消息 // see IMCommonCardMsg
    XML_MSG_CENTER= 130; // yoga的通用消息 see IMCommonXmlMsg,居中消息。类似官方号消息
    AI_PLAY_HUB_IM_Msg = 131; // AI红娘消息  see muse_ai_play_hub_logic.proto AIPlayHubIMMsg
    XML_MSG_NORMAL = 132; // yoga的通用消息 see IMCommonXmlMsg,没有气泡， 跟随左右
    XML_MSG_BUBBLE = 133; // yoga的通用消息 see IMCommonXmlMsg,气泡消息，跟随左右
    AI_INSPIRATION_IM_MSG = 134; // ai灵感回复
    WEDDING_PROPOSE_IM_MSG = 135; //求婚IM消息   see channel_wedding_logic.proto WeddingProposeInfo
    TWIN_MSG = 136;// 马甲包与TT通用的消息 see IMTwinMsg
    MUSE_ROLE_PLAY_IM_MSG = 137; //角色扮演Im消息  see muse_role_play_logic.proto MuseRolePlayIMMsg
    WEDDING_CONSULT_RESERVE_IM_MSG = 138; // 婚礼咨询IM消息 see channel_wedding_logic.proto ConsultWeddingReserveIMMsg
    WEDDING_ARRANGE_RESERVE_IM_MSG = 139; // 婚礼安排IM消息 see channel_wedding_logic.proto ArrangeWeddingReserveIMMsg

    SNAP_IM_MSG = 140; // 阅后即焚(闪图)IM消息 see SnapMsg
}

message RichTextWords {
    required string text = 1;    //文本
}
message RichTextImage {
    required string url = 1;    //图片地址
    optional float width = 2;    //图片宽度
    optional float height = 3;    //图片高度
}
message RichTextLink {
    required string text = 1;        //文本
    optional int32 text_color = 2;    //文本颜色
    optional string jump_url = 3;    //跳转地址
    optional bytes jump_url_pb_content = 4; // see LinkJumpURL
}

message RichTextElement {
    oneof content {
        RichTextWords words = 1;
        RichTextImage image = 2;
        RichTextLink link = 3;
    }
}
message RichTextMsg {
    repeated RichTextElement in_value = 1;    //im页显示
    repeated RichTextElement out_value = 2;    //im列表显示

    enum RichTextMsgLevel {
        TIPS = 0;
        GUIDE = 1;
    }
    optional RichTextMsgLevel level = 3;
    optional RichTextMsgType type = 4;
    enum RichTextMsgType {
        NORMAL = 0;
        INTERACTION_INTIMACY = 1;
    }
}

message PersonalRichTextElement {
    repeated string account = 1;
    required RichTextMsg msg = 2;
}

message PersonalRichTextMsg {
    repeated PersonalRichTextElement personal = 1;    //个性化
    optional RichTextMsg default = 2;                //默认
}

message LinkJumpURL {
    map<string, string> jump_url_map = 1; // 跳转链接map（根据market_id、clienttype），proto序列化后放在富文本的RichTextLink，jump_url_pb_content
}

// 1v1实时语音--房间创建
message SessionCreateMsg {
    required uint32 session_id = 1;
}

// 1V1实时语音--房间关闭
message SessionCloseMsg {
    required uint32 session_id = 1;
}

//Cue消息客户端透传的消息结构 服务端透明
message CueMsg {
    required uint32 cue_id = 1;
    required string msg_pic_left = 2;
    required string msg_pic_right = 3;
    required string preview_text = 4;
    required string lottie_url = 5;
}

//文本消息扩展
message TextMsgExt {
    optional uint32 dress_id = 1;//消息气泡装扮id
}

//声音消息扩展
message VoiceMsgExt {
    optional uint32 dress_id = 1;//消息气泡装扮id
}

enum MsgSourceType {
    NORMAL_SOURCE = 0;
    CHANNEL_ROOM = 1; //  语音房
    NEARBY_PEOPLE = 2; // 附近的人
    MSG_SOURCE_FINDFRIEND_EXAM = 3;  // 破冰匹配 测试结果分享 findfriend_exam
    MSG_SOURCE_FINDFRIEND_MATCH = 4; // 破冰匹配 匹配 findfriend_mating
    MSG_SOURCE_USERTAG_MATCH = 5;           // 用户标签匹配 匹配 usertag_mating 卡片
    MSG_SOURCE_USERTAG_MATCH_NORMAL = 6;    // 用户标签匹配 匹配 usertag_mating 普通消息
    MSG_SOURCE_DEL_FROM_USER_BLACK_LIST = 7; // 用户移出黑名单
    MSG_SOURCE_CHANNEL_USER_CARD = 8;//来自房间用户卡片
    MSG_SOURCE_FIND_PLAYER = 9;//来自找人玩
    MSG_SOURCE_SEND_PLAYER = 10;//来自发放玩伴
    MSG_SOURCE_FROM_UGC_POST = 11;//来自动态
    MSG_SOURCE_FROM_IM_PRESENT = 12; //来自IM送礼
    MSG_SOURCE_FROM_MASTER_APPRENTICE_INVITE = 13; //来自师徒关系的师父邀请消息
    MSG_SOURCE_FROM_GAME_RADAR = 14; //来自游戏雷达
    MSG_SOURCE_FROM_CHANNEL_DETAIL_GANG_UP_LIST = 15; //来自房间详情页开过黑的人
    MSG_SOURCE_FROM_INVITE_FROM_CHANNEL = 16;//发自房间的邀请消息
    MSG_SOURCE_FROM_CHAT_CARD = 17; // 来自扩列墙
    MSG_SOURCE_FROM_SYS_AUTO_HEY = 18; // 来自系统自动im消息
    MSG_SOURCE_FROM_SLIP_NOTE = 19; // 来自小纸条
    MSG_SOURCE_FROM_CHAT_CARD_DIY_MSG = 20; // 来自扩列墙（自定义消息）
    MSG_SOURCE_FROM_CHAT_CARD_A_GROUP_LIKE = 21; // 来自扩列墙（A组“我可”按钮）
    MSG_SOURCE_FROM_FELLOW_INVITE = 22; // 来自亲密关系邀请
    MSG_SOURCE_FROM_FELLOW_UPGRADE = 23; // 来自亲密升级
    MSG_SOURCE_FROM_FELLOW_GAIN_RARE = 24; // 来自获取稀缺关系
    MSG_SOURCE_FROM_CHATGPT_BOT_IM = 25; // 来自 chat-gpt 机器人的 IM 消息
    MSG_SOURCE_FROM_MIJING_HOMEPAGE_CHAT_A_CHAT = 26; //谜境首页聊一聊
    MSG_SOURCE_FROM_USER_RECALL_COMEBACK = 27; //用户召回 好友回归消息 see UserRecallComeback
    MSG_SOURCE_FROM_GAME_PAL_CARD = 28;  // 来自游戏搭子卡
    MSG_SOURCE_FROM_NPC_AUTO_IM = 29; // 谜境NPC自动回复IM
    MSG_SOURCE_FROM_ESPORT = 30;      // 电竞指导
    MSG_SOURCE_FROM_PAL_CARD_TO_PERSON_CENTER = 31;  // 搭子卡到个人中心页
    MSG_SOURCE_FROM_AI_PROMPT_AUTO_IM = 32; // 谜境剧本AI根据Prompt自动回复
    MSG_SOURCE_FROM_GAME_CARD_TO_PERSON_CENTER = 33;             // 游戏卡页到个人中心页
    MSG_SOURCE_FROM_INVITE_ROOM_ENTRANCE = 34; // 来自房间邀请进房入口
    MSG_SOURCE_FROM_NON_MIC_RECALL_USER_AUTO_IM = 35; // 非上麦拉新召回用户IM自动消息
    MSG_SOURCE_FROM_PAL_CARD_INVITE_ROOM = 36; // 来自搭子卡邀请进房

    MSG_SOURCE_FROM_MIJING_SERVER = 100; // 谜境独立版消息中转
    MSG_SOURCE_FROM_ESPORT_GAME_CARD_2_IM_PAGE = 101; // 游戏卡页到大神IM页
    MSG_SOURCE_FROM_E_SPORT_ZONE_HALF_SCREEN = 102;  // 电竞专区消息半屏
    MSG_SOURCE_FROM_GAME_HALL_TEAM_LIST = 103; // 组队信号玩家列表

    MSG_SOURCE_FROM_INVITE_FROM_CHANNEL_V2 = 106; // 邀请进房优化2期
    MSG_SOURCE_FROM_GAME_IM_TEAM_GROUP = 107; //来自开黑专区组队大厅
    MSG_SOURCE_FROM_GAME_FEED = 108; //来自开黑专区帖子
    MSG_SOURCE_FROM_RICHER_BIRTHDAY = 109; //来自大R生日
    MSG_SOURCE_FROM_GAME_PAL_ZONE_POPUP = 110; // 开黑专区签到弹窗和通知-提醒弹窗和推送
    MSG_SOURCE_FROM_TODAY_CP = 111; // 来自今日奇缘
    MSG_SOURCE_FROM_ESPORT_BACK_RECALL = 112;      // 电竞专区退出挽留弹窗
    MSG_SOURCE_FROM_FLASH_CHAT = 113;      // 即时闪聊
    MSG_SOURCE_FROM_ROLE_PLAY = 114;      //角色扮演
    MSG_SOURCE_FROM_AIGC_ACCOUNT = 115; // aigc 账号
}

enum MsgSensitiveType {
    NORMAL_SENSTIVIVE = 0;
    MONEY_SENSTIVIVE = 1; //  涉及金钱
}

enum MsgLabel {
    NO_LABEL = 0;
    NEW_FOLLOWER = 1; // 新关注者的系统消息
    MSG_LABEL_SUPER_PUBLISH = 2; // 超级发布
    MSG_LABEL_GAME_PAL_CARD = 3; // 搭子卡滑卡打招呼
}

//发送消息
message SendMsgReq {
    required BaseReq base_req = 1;
    required string target_name = 2;
    required uint32 type = 3;    //消息类型列表, 文本,图片,语音
    required string content = 4;// 文本和表情字符
    required uint32 client_msg_id = 5; //
    required uint32 client_msg_time = 6;

    optional bytes thumb = 7; // 缩略图10 k 以内
    required bool has_attachment = 8;        // 是否有附件
    required string my_login_key = 9;        // 登录时的标识key
    optional uint32 origin = 10;    //消息来源,0/1：App， 2：语音球
    optional bytes ext = 11;        // 扩展
    optional uint32 msg_source_type = 12; //消息来源类型
    optional bool do_not_check_text = 13; // 是否进行反垃圾文本检测,true：不检测；false：检测
    optional uint32 msg_label = 14; // see enum MsgLabel
}

message SendMsgResp {
    required BaseResp base_resp = 1;
    required uint32 client_msg_id = 2;        //
    required uint32 svr_msg_id = 3;
    required uint32 svr_msg_time = 4;
    optional string attachment_key = 5;        // 附件key
    required string target_name = 6;
    required string my_login_key = 7;        // 登录时的标识key
    optional uint32 exceed_time = 8;        // 召集令过期时间（剩余多久可发召集令）
    optional uint32 origin = 9;                //消息来源,0/1：App， 2：语音球
    optional uint32 target_msg_id = 10;        //对方的svr_msg_id,消息撤销用
    optional uint32 target_uid = 11;        //对方的UID
}

//上传附件
message UploadAttachmentReq {
    required BaseReq base_req = 1;
    required uint32 svr_msg_id = 2;
    required string attachment_key = 3;        // 附件key
    required bytes attachment = 4; // 附件
    required bytes attachment_property = 5;        // 附件附加属性
}

message UploadAttachmentResp {
    required BaseResp base_resp = 1;
    required uint32 svr_msg_id = 2;
}

message DownloadAttachmentReq {
    required BaseReq base_req = 1;
    required uint32 svr_msg_id = 2;
    required string attachment_key = 3;        //
}

message DownloadAttachmentResp {
    required BaseResp base_resp = 1;
    required uint32 svr_msg_id = 2;
    required string attachment_key = 3;
    required bytes attachment = 4; // 附件
    required string target_account = 5;        // 发消息的对象
}

//删除消息
message DeleteMessageReq {
    required BaseReq base_req = 1;
    required uint32 msg_id = 2; // 消息id seq_id一个意思
}

message DeleteMessageResp {
    required BaseResp base_resp = 1;
}

//批量设置已读
message MarkMsgReadReq {
    required BaseReq base_req = 1;
    required string target_name = 2; //和谁的聊天
    required uint32 svr_msg_id = 3; // 服务消息id(最大的那条id)
    optional uint32 peer_svr_msg_id = 4;    // 对端的消息id
}

message MarkMsgReadResp {
    required BaseResp base_resp = 1;
    required string target_name = 2;
    required uint32 svr_msg_id = 3;
    optional uint32 peer_read_svr_msg_id = 4;
}

// 消息接收设定

enum MsgReceiveSetting {
    MSG_RECEIVE_WITH_NOTIFICATION = 0;      // Default
    MSG_RECEIVE_WITHOUT_NOTIFICATION = 1;   // 接收但不通知
    MSG_DO_NOT_RECEIVE = 2;                 // 不接收
}

message UpdateMsgSettingReq {
    required BaseReq base_req = 1;
    required string account = 2;
    required uint32 receive_setting = 3;
}

message UpdateMsgSettingResp {
    required BaseResp base_resp = 1;
}

message QueryMsgSettingReq {
    required BaseReq base_req = 1;
    optional string account = 2;
}

message MsgReceiveSettingItem {
    required string account = 1;
    required uint32 receive_setting = 2;
}

message QueryMsgSettingResp {
    required BaseResp base_resp = 1;
    repeated MsgReceiveSettingItem setting_item_list = 2;
}

// 检查用户是否有AT所有人的权限和剩余次数
message CheckSendAtEveryoneGroupMsgReq {
    required BaseReq base_req = 1;
    required uint32 group_id = 2;
}

message CheckSendAtEveryoneGroupMsgResp {
    required BaseResp base_resp = 1;
    required uint32 remain_cnt = 2; //剩余次数
    required bool is_allow_at_everyone = 3;
}

message CancelMsgReq {
    required BaseReq base_req = 1;
    required string target_name = 2;
    required uint32 svr_msg_id = 3;    //我的服务器msg_id
    required uint32 target_msg_id = 4;    //对方的服务器msg_id
    required uint32 client_msg_id = 5;
    required uint32 client_msg_time = 6;
    required string my_login_key = 7;    // 登录时的标识key
}

message CancelMsgResp {
    required BaseResp base_resp = 1;
}

//用户设置

// 消息已读推送
message MessageReadByPeerMessage {
    required string peer_name = 1;
    required uint32 svr_msg_id = 2;
}

message GetMessagePeerReadStatusReq {
    required BaseReq base_req = 1;
    repeated string account_list = 2;
}

message MessagePeerReadStatus {
    required string account = 1;
    required uint32 svr_msg_id = 2;
}

message GetMessagePeerReadStatusResp {
    required BaseResp base_resp = 1;
    repeated MessagePeerReadStatus peer_read_status_list = 2;
}



//删除信息
message DelMsg {
    required string target_name = 1;            //接收消息的account
    required uint32 svr_msg_id = 2;        //我的服务器msg_id
    required uint32 target_msg_id = 3;            //对方的服务器msg_id
    required uint32 client_msg_id = 4;
    required uint32 client_msg_time = 5;
    required string my_login_key = 6;        //登录时的标识key
    required string from_name = 7;              //发送消息的account
}
//批量删除信息
message BatchDelMsgReq {
    required BaseReq base_req = 1;
    repeated DelMsg del_msg = 2;   //删除的信息编号列表
}

message BatchDelMsgResp {
    required BaseResp base_resp = 1;
}


////游戏中心好友热玩推送
message PopGameJumpMsg {

    enum JumpType {
        DOWNLOAD_GAME = 1;
        ORDER_GAME = 2;
    }
    required uint32 msg_type = 1; //see JumpType
    required uint32 game_id = 2;
    optional string area_url = 3;    //游戏专区或游戏圈跳转地址
    optional string icon_url = 4;    //图标地址
    optional string download_url = 5;    //下载链接
    optional string game_size = 6;    //大小
    optional string game_pkt_name = 7;    //游戏包名
    optional string preorder_url = 8;   //预约活动页
    optional string game_name = 9;
}

//表情消息
message EmojiMsg {
    enum EmojiType {
        // 默认
        EMMOJI_TYPE_UNSPECIFIED = 0;
        // 推荐表情包
        EMOJI_TYPE_RECOMMEND = 1;
        // 自动联想表情包
        EMOJI_TYPE_KEYWORD = 2;
    }
    required string id = 1;        //表情id
    required string url = 2;    //表情下载url，若有优先使用，若无使用id拼接
    required uint32 height = 3;    //表情高像素(px)
    required uint32 width = 4;    //表情宽像素(px)
    // 表情类型 see EmojiType
    optional uint32 emoji_type = 5;
    // 自动联想表情包关键词
    optional string keyword = 6;
    // MD5
    optional string md5 = 7;
    // 第三方表情包缩略图高
    optional uint32 thumb_h = 8;
    // 第三方表情包缩略图宽
    optional uint32 thumb_w = 9;
    // 是否obs链接
    optional bool is_obs = 10;
    // obskey
    optional string obs_key = 11;
}

message InteractiveUnreadMsg {
    enum InteractiveType {
        NONE = 0;
        NEW_ATTITUDE = 1;   //‘喜欢’
        NEW_COMMENT = 2;    //‘评论’
    }
    //显示最新一条信息用
    required InteractiveType type = 1;          //1‘喜欢’，2‘评论’
    required int64 time = 2;                    //通知时间
    required uint32 from_user_id = 3;           //用户id
    required string from_user_nickname = 4;     //用户名

    required int32 comment_count = 5;        //新增未读新评论数，与本地的叠加
    required int32 attitude_count = 6;       //新增未读新赞数，与本地的叠加
}

message RichTextPresent {
    required uint32 id = 1; // 礼物id
    required uint32 type = 2; // 礼物类型
    required string url = 3; // 礼物图标url -->IM送礼时只填此字段，大图的url
    required string head = 4; // 头部文案
    required string text = 5; // 文案
    required uint32 source_type = 6; // 来源，背包还是购买的礼物
    required bool show_send_btn = 7; // 是否显示发送礼物给对方的按钮
    required bool is_double = 8; // 是否双倍礼物
    required uint32 target_uid = 9; // 送礼目标
    optional string name = 10; // 礼物名
    optional uint32 price_type = 11; //  0:未知  1：红钻 2：t豆
    optional uint32 price = 12; //  价值
}

// 对应 INTERACTION_INTIMACY_GIFT，IM_PRESENT ,IM_PRESENT_NEW
message RichTextWithPresentMsg {
    enum IM_PRESENT_TYPE {
        NORMAL = 0;   // 普通礼物
        FELLOW = 1;   // 挚友信物
    }
    required PersonalRichTextMsg msg = 1;
    required RichTextPresent present = 2;
    optional ga.userpresent.PresentSendItemInfo item_info = 3;
    optional uint32 im_present_type = 4;  // see IM_PRESENT_TYPE
}


message MasterApprenticeInviteMsg {
    optional string master_inner_text = 1;
    optional string master_outer_text = 2;
    optional string apprentice_inner_text = 3;
    optional string apprentice_outer_text = 4;
}

message MasterApprenticeEstablishMsg {
    optional string master_inner_text = 1;
    optional string master_outer_text = 2;
    optional string apprentice_inner_text = 3;
    optional string apprentice_outer_text = 4;
}

message MasterApprenticeIntroductionMsg {
    required string text_1 = 1;
    required string text_2 = 2;
}

message ChatCardMsg {
    required string hi_text = 1; // 打招呼
    required string topic_text = 2; // 破冰话题
    required bytes card = 3; // 卡片信息，用protobuf编码ChatCard（chat-card-loigc_.proto）
    optional uint32 dress_id = 4;//消息气泡装扮id
}

message ChannelOpenGameInviteMsg {
    required string icon_url = 1;
    required string name = 2;
    required string desc = 3;
    required string img_url = 4;
    required string jump_url = 5;
    optional string from_out_msg = 6;
    optional string to_out_msg = 7;
}

message FellowInviteMsg {
    required string invite_id = 1; // 邀请函id
    required uint32 invite_status = 2; // 邀请函状态
    required uint32 present_id = 3;  // 信物id
    required bool is_unlock = 4; // 是否解锁坑位
    required uint32 fellow_bind_type = 5; // 关系绑定类型
    required uint32 fellow_type = 6; // 关系类型
    required string fellow_name = 7; // 关系名称
    required string present_url = 8; // 礼物图标
    optional uint32 from_bind_type = 9; // 原关系绑定类型
    optional uint32 from_fellow_type = 10; // 原关系类型
    optional string from_fellow_name = 11; // 原来关系名称

}

// LevelAwardItemType 废弃
enum LevelAwardItemType {
    LEVEL_AWARD_ITEM_UNSPECIFIED = 0;         // 无效值
    LEVEL_AWARD_ITEM_HEAD_WEAR = 1;             // 麦位框
    LEVEL_AWARD_ITEM_HORSE = 2;               // 坐骑
    LEVEL_AWARD_ITEM_MEDAL = 3;               // 勋章
    LEVEL_AWARD_ITEM_DECORATION = 4;          // 主页飘
    LEVEL_AWARD_ITEM_Nameplate = 5;          // 个人铭牌
    LEVEL_AWARD_ITEM_MIC_OfficialCert = 6;  // 大V认证
    LEVEL_AWARD_ITEM_INFORMATION_CARD = 7;    // 房间资料卡
    LEVEL_AWARD_ITEM_PACKAGE = 100;             // 包裹礼物
}

enum FellowLevelAwardItemType {
    FELLOW_LEVEL_AWARD_ITEM_UNSPECIFIED = 0;         // 默认值，正常情况不应该传该值
    FELLOW_LEVEL_AWARD_ITEM_Package = 1; // 包裹奖励
    FELLOW_LEVEL_AWARD_ITEM_OfficialCert = 2; // 大V认证
    FELLOW_LEVEL_AWARD_ITEM_Nameplate = 3; // 个人铭牌
    FELLOW_LEVEL_AWARD_ITEM_HeadWear = 4; // 麦位框
    FELLOW_LEVEL_AWARD_ITEM_Horse = 5; // 坐骑
    FELLOW_LEVEL_AWARD_ITEM_Float = 6; // 主页飘
    FELLOW_LEVEL_AWARD_ITEM_ChannelInfoCard = 7; // 房间资料卡
}

message LevelAwardItem {
    required uint32 item_type = 1;    //物品类型 see FellowLevelAwardItemType
    required string item_name = 2;    //物品名称
    required string item_id = 3;      //物品id
    optional uint32 item_count = 4;   //物品数量or天数
    optional uint32 day_count = 5;   //物品数量or天数
    required string item_icon = 6;    // 物品icon
    optional string item_count_info = 7; // 物品数量描述 [x天/xx豆]
    optional string item_type_name = 8;  //物品类型名称
}


//挚友下一个等级奖励信息
message FellowNextLevelAwardMsg {
    required string next_award_level = 1; //下一奖励等级描述
    repeated LevelAwardItem award_list = 2; //奖励列表
}

message FellowUpgradeMsg {
    required uint32 fellow_bind_type = 1; // 关系绑定类型
    required string fellow_name = 2; // 关系名称
    required uint32 level = 3; // 升级后等级
    optional FellowNextLevelAwardMsg next_award_msg = 4;
}

message GainRareUser {
    required uint32 uid = 1;
    required string account = 2; // ttid
    required string nickname = 3; // 昵称
    required string rare_name = 4; // 稀缺关系名称
    required bool is_mvp = 5; // 是否mvp
    required bool is_charm = 6; // 是否收礼最多
}
message GainRareMsg {
    repeated GainRareUser users = 1; // 用户头像
    required bool is_bind = 2; // 是否绑定
    required string thumb_background = 3; // 缩略背景图
    required string background = 4; // 主背景图
    required string rare_days = 5; // 关系天数
    required string rare_name = 6; // 关系名称
    required uint32 timestamp = 7; // 获得时间
    required uint32 channel_id = 8; // 房间id
}


message SystemNotifyMsg {
    required string highlight_content = 1; // 高亮内容
    required string jump_url = 2; // 高亮部分跳转
}

message ShareGameMsg {
    required string game_name = 1;
    required string img_url = 2;
    required string jump_url = 3;
}

message MiJingUserTag {
    required string matching_degree = 1;
}


message UserRecallComeback {
    required string content = 1;
    required string hlight = 2;
    required string url = 3;
}

message GetImGuideTriggerInfoReq {
    required BaseReq base_req = 1;
    required string account = 2;
    required string target_account = 3;  // 对方
}

message GetImGuideTriggerInfoResp {
    required BaseResp base_resp = 1;
    repeated TriggerItem trigger_item_list = 2;  // 触发条件所需信息
}

message TriggerItem {
    required uint32 uid = 1;            // uid
    required string account = 2;        // 账号
    required uint32 cid = 3;            // 房间id，0 则为不在房
    required uint32 creator = 4;        // 房主 uid
    required uint32 channel_type = 5;   // 房间类型
    optional uint32 tab_id = 6;         // 玩法 tab_id
    optional string game_play_name = 7; // 玩法名字
    optional bool is_online = 8;        // 只需要对方的在线状态， 即 target_uid

}

// IMCommonCardMsg 通用卡片消息
message IMCommonCardMsg {
  optional string title = 1; // 卡片标题
  optional string content = 2; // 卡片消息
  optional string jump_url = 3; // 跳转链接
  optional string img_url = 4; // 卡片的小图链接（也可以传account）
}


enum XmlMsgDidsplayType {
    UNSPECIFIED = 0;
    CENTER = 1;         // 居中消息。类似官方号消息
    BUBBLE = 2;         // 气泡消息，跟随左右
    NORMAL = 3;         // 没有气泡， 跟随左右
}

enum IMXMLPayLoadType {
    IM_XML_PAYLOAD_TYPE_UNSPECIFIED = 0;
    IM_XML_PAYLOAD_TYPE_WEDDING_GROUP_PHOTO_LIST = 1; // 影集 
    IM_XML_PAYLOAD_TYPE_WEDDING_CLIP_LIST = 2 ; //  婚礼片段  channel_wedding_logic.proto WeddingScenePicOpt
}

// 通用的Yoga的xml的消息
message IMCommonXmlMsg {
    required uint32 display_type = 1 [deprecated = true];    //xml的展示类型 see XmlMsgDidsplayType
    required string xml_content = 2; // xml的字符串
    required uint32 dress_id = 3;    //气泡id，带气泡的消息使用
    optional bytes payload = 4; // 一些业务数据， 各端在xml短链的时候通过 ?payload=xxxx 将payload带到自己的路由去处理。
    optional IMXMLPayLoadType payload_type = 5; // 这个其实就是标记一下这个payload是哪message。加个注释的能力。理论上，每个短链是能自己知道传的是什么message。    
}

enum SnapRevStatus {
    SNAP_REV_STATUS_UNSPECIFIED = 0;
    SNAP_REV_STATUS_UNREAD = 1; // 未查看
    SNAP_REV_STATUS_DESTROYED = 2; // 已销毁
}

message SnapMsg{
    required string snap_id = 1;  // 闪照id
    required SnapRevStatus rev_status = 2;  // 接收方的查看状态
}