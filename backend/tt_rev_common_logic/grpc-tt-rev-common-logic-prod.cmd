# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

39371:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 39371 --source api/tt_rev_common_logic/grpc_tt_rev_common_logic.proto --lang go --method /ga.api.tt_rev_common_logic.TTRevCommonLogic/GetNewRechargeActEntryInfo
39372:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 39372 --source api/tt_rev_common_logic/grpc_tt_rev_common_logic.proto --lang go --method /ga.api.tt_rev_common_logic.TTRevCommonLogic/GetNewRechargeActPopupInfo
39373:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 39373 --source api/tt_rev_common_logic/grpc_tt_rev_common_logic.proto --lang go --method /ga.api.tt_rev_common_logic.TTRevCommonLogic/GetRechargeBannerInfo
39374:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 39374 --source api/tt_rev_common_logic/grpc_tt_rev_common_logic.proto --lang go --method /ga.api.tt_rev_common_logic.TTRevCommonLogic/CheckCanModifySex
39375:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 39375 --source api/tt_rev_common_logic/grpc_tt_rev_common_logic.proto --lang go --method /ga.api.tt_rev_common_logic.TTRevCommonLogic/GetChannelActivityEntry
51540:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51540 --source api/tt_rev_common_logic/grpc_tt_rev_common_logic.proto --lang go --method /ga.api.tt_rev_common_logic.TTRevCommonLogic/GetSnapMetaInfo
51541:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51541 --source api/tt_rev_common_logic/grpc_tt_rev_common_logic.proto --lang go --method /ga.api.tt_rev_common_logic.TTRevCommonLogic/SnapUpload
51542:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51542 --source api/tt_rev_common_logic/grpc_tt_rev_common_logic.proto --lang go --method /ga.api.tt_rev_common_logic.TTRevCommonLogic/SnapReceive
51543:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51543 --source api/tt_rev_common_logic/grpc_tt_rev_common_logic.proto --lang go --method /ga.api.tt_rev_common_logic.TTRevCommonLogic/SnapViewReport
