# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

3056:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3056 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/ShowTopicChannelTabList
3080:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3080 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/ListTopicChannel
3081:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3081 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetSecondaryFilter
3082:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3082 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetSecondaryFilterByCategory
3083:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3083 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetDefaultRoomNameList
3084:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3084 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/PublishGangupChannel
3085:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3085 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/CancelGangupChannelPublish
3086:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3086 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetHomePageHeadConfig
3087:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3087 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetHotMiniGames
3088:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3088 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetQuickMiniGames
3089:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3089 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetPlayQuestions
3090:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3090 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GameInsertFlowConfig
3091:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3091 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetHomePageGuide
3092:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3092 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetMoreTabConfig
3093:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3093 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetFilterItemByEntrance
3094:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3094 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/SetDIYFilterByEntrance
3095:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3095 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetDIYFilterByEntrance
3096:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3096 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetNegativeFeedBackInRoom
3097:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3097 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/ReportNegativeFeedBackInRoom
3098:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3098 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetPublishOptionGuide
3099:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3099 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetNewQuickMatchConfig
3100:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3100 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetTopicChannelCfgInfo
3101:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3101 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/SetUgcChannelPlayMode
3102:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3102 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/TypingStatusBroadcast
3103:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3103 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetChannelPlayModeGuide
3104:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3104 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/ReportDailyTask
3105:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3105 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/HomePageHeadConfigEnterCheck
3106:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3106 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetChannelListGuideConfigs
3107:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3107 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetTabInfos
3112:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3112 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetRecommendGames
3113:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3113 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/RefreshGameLabel
3114:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3114 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetSupportTabList
3115:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3115 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetChannelMicVolSet
3116:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3116 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/SetChannelMicVol
3117:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3117 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/SetChannelTruthOrDareStatus
3118:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3118 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetTruthOrDareQuestionList
31500:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31500 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/CreateHobbyChannel
31510:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31510 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetGameHomePageDIYFilter
31511:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31511 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/SetGameHomePageDIYFilter
31512:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 31512 --source api/channel_play/grpc_channel_play.proto --lang go --method /ga.api.channel_play.ChannelPlayLogic/GetGameHomePageFilter
