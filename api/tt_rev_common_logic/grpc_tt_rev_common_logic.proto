syntax = "proto3";

package ga.api.tt_rev_common_logic;

import "api/extension/extension.proto";
import "tt_rev_common_logic/tt_rev_common_logic.proto";

option go_package = "golang.52tt.com/protocol/app/api/tt_rev_common_logic;tt_rev_common_logic";
option java_package = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";


service TTRevCommonLogic {
  option (ga.api.extension.logic_service_language) = "go";
  option (ga.api.extension.logic_service_name) = "tt-rev-common-logic";

  // 获取充值(首充)活动入口信息
  rpc  GetNewRechargeActEntryInfo(ga.tt_rev_common_logic.GetNewRechargeActEntryInfoRequest) returns (ga.tt_rev_common_logic.GetNewRechargeActEntryInfoResponse) {
    option (ga.api.extension.command) = {
      id: 39371;
    };
  };

  // 获取充值(首充)活动弹窗信息
  rpc  GetNewRechargeActPopupInfo(ga.tt_rev_common_logic.GetNewRechargeActPopupInfoRequest) returns (ga.tt_rev_common_logic.GetNewRechargeActPopupInfoResponse) {
    option (ga.api.extension.command) = {
      id: 39372;
    };
  };

  // 获取充值页banner信息
  rpc  GetRechargeBannerInfo(ga.tt_rev_common_logic.GetRechargeBannerInfoRequest) returns (ga.tt_rev_common_logic.GetRechargeBannerInfoResponse) {
    option (ga.api.extension.command) = {
      id: 39373;
    };
  };

  // 检查能否切换性别
  rpc CheckCanModifySex (ga.tt_rev_common_logic.CheckCanModifySexRequest) returns (ga.tt_rev_common_logic.CheckCanModifySexResponse) {
    option (ga.api.extension.command) = {
      id: 39374;
    };
  }

  // 获取房间活动入口信息
  rpc GetChannelActivityEntry (ga.tt_rev_common_logic.GetChannelActivityEntryReq) returns (ga.tt_rev_common_logic.GetChannelActivityEntryResp) {
    option (ga.api.extension.command) = {
      id: 39375;
    };
  }

   // 获取闪照配置
  rpc GetSnapMetaInfo (ga.tt_rev_common_logic.GetSnapMetaInfoRequest) returns (ga.tt_rev_common_logic.GetSnapMetaInfoResponse) {
    option (ga.api.extension.command) = {
      id: 51540;
    };
  }

  // 上传闪照
  rpc SnapUpload (ga.tt_rev_common_logic.SnapUploadRequest) returns (ga.tt_rev_common_logic.SnapUploadResponse) {
    option (ga.api.extension.command) = {
      id: 51541;
    };
  }

  // 查看闪照
  rpc SnapReceive (ga.tt_rev_common_logic.SnapReceiveRequest) returns (ga.tt_rev_common_logic.SnapReceiveResponse) {
    option (ga.api.extension.command) = {
      id: 51542;
    };
  }

  // 上报已查看状态
  rpc SnapViewReport (ga.tt_rev_common_logic.SnapViewReportRequest) returns (ga.tt_rev_common_logic.SnapViewReportResponse) {
    option (ga.api.extension.command) = {
      id: 51543;
    };
  }

}