syntax = "proto3";
package ga.api.minigame_proxy;

import "minigame_proxy/minigame_proxy.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/minigame_proxy;minigame_proxy";

service MinigameProxyLogic {
  option (ga.api.extension.logic_service_name) = "minigame-proxy-logic";
  option (ga.api.extension.logic_service_language) = "go";
  option (ga.api.extension.logic_service_uri_rewrite) = "/logic.minigame_proxy.MinigameProxyLogic/";

  rpc SubmitGameCmd(ga.minigame_proxy.SubmitGameCmdRequest) returns (ga.minigame_proxy.SubmitGameCmdResponse) {
    option (ga.api.extension.command) = {
        id: 30303;
    };
  }
}