// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.you_know_who;


 
import "youknowwhologic/you-know-who-logic_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/you_know_who;you_know_who";

service YouKnowWhoLogic {
    option (ga.api.extension.logic_service_name) = "you-know-who-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.YouKnowWhoLogic/";
    rpc ChangeUKWSwitch(ga.youknowwhologic.ChangeUKWSwitchReq) returns (ga.youknowwhologic.ChangeUKWSwitchResp) {
        option (ga.api.extension.command) = {
             id: 3790
        };
    }
    rpc GetUKWInfo(ga.youknowwhologic.GetUKWInfoReq) returns (ga.youknowwhologic.GetUKWInfoResp) {
        option (ga.api.extension.command) = {
             id: 3791
        };
    }
    rpc GetUKWUserProfile(ga.youknowwhologic.GetUKWUserProfileReq) returns (ga.youknowwhologic.GetUKWUserProfileResp) {
        option (ga.api.extension.command) = {
             id: 3792
        };
    }
    rpc ChangeRankSwitch(ga.youknowwhologic.ChangeRankSwitchReq) returns (ga.youknowwhologic.ChangeRankSwitchResp) {
        option (ga.api.extension.command) = {
             id: 3793
        };
    }
    rpc SendShowUpMsg(ga.youknowwhologic.SendShowUpMsgReq) returns (ga.youknowwhologic.SendShowUpMsgResp) {
        option (ga.api.extension.command) = {
             id: 3794
        };
    }
    rpc GetShowUpMsgList(ga.youknowwhologic.GetShowUpMsgListReq) returns (ga.youknowwhologic.GetShowUpMsgListResp) {
        option (ga.api.extension.command) = {
             id: 3795
        };
    }
    rpc GetShowUpTextList(ga.youknowwhologic.GetShowUpTextListReq) returns (ga.youknowwhologic.GetShowUpTextListResp) {
        option (ga.api.extension.command) = {
             id: 3796
        };
    }
    rpc ExposureUKW(ga.youknowwhologic.ExposureUKWReq) returns (ga.youknowwhologic.ExposureUKWResp) {
        option (ga.api.extension.command) = {
             id: 3797
        };
    }
    rpc UserChangeUKWEnterNotice(ga.youknowwhologic.UserChangeUKWEnterNoticeReq) returns (ga.youknowwhologic.UserChangeUKWEnterNoticeResp) {
        option (ga.api.extension.command) = {
             id: 3798
        };
    }
}

