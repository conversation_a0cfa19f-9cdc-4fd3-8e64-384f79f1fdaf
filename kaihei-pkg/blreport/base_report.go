package blreport

import (
	"context"
	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/pkg/datacenter"
	"golang.52tt.com/pkg/log"
)

const (
	BizCreate       = "310000020002"
	BizNewCreate    = "310000020008"
	BylinkNewCreate = "room_publish_all_log"
	// bizDismiss      = "310000020020"
	TimeLayout               = "2006-01-02 15:04:05"
	BylinkCreateHobbyChannel = "user_create_room_log"

	AigcCommunityPostLike = "aigc_community_post_like_log"
	AigcCommunityPost     = "aigc_community_post_log"

	AigcMsgReport = "aigc_partner_msg_data_report"

	// ProcessPcUserSetting pc进程展示开关
	ProcessPcUserSetting = "pc_user_setting_log"
)

func (b *BlReport) Report2Datacenter(ctx context.Context, bizId string, reportKV map[string]interface{}) {
	if b.<PERSON><PERSON> {
		//log.DebugWithCtx(ctx, "Report2Datacenter bizId %v, reportKV: %v", bizId, reportKV)
		datacenter.StdReportKV(ctx, bizId, reportKV)
	}
}

func (b *BlReport) Report2bylink(ctx context.Context, userIdentifier uint64, eventName string, reportKV map[string]interface{}, isLoginIdOpt bool) {
	if b.IsOpen {
		//log.DebugWithCtx(ctx, "Report2bylink userId %v, eventName %v, reportKV: %v", userIdentifier, eventName, reportKV)

		err := bylink.TrackMap(ctx, userIdentifier, eventName, reportKV, isLoginIdOpt)
		if err != nil {
			log.ErrorWithCtx(ctx, "error Report2bylink userId %v, eventName %v, reportKV: %v", userIdentifier, eventName, reportKV)
		}
		bylink.Flush()
	}
}
