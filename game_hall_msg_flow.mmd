sequenceDiagram
    participant Client as 📱 客户端
    participant GameHallLogic as 🎮 game-hall-logic
    participant RiskService as 🛡️ 风控&频率服务
    participant GameHallService as 🏛️ game-hall服务
    participant PushD as 📡 推送服务

    Note over Client, PushD: 游戏大厅消息系统主要流程

    %% 发送消息流程
    rect rgb(240, 248, 255)
        Note over Client, PushD: 📤 发送消息流程
        
        Client->>GameHallLogic: SendGameImMsg请求
        Note over Client, GameHallLogic: 消息类型、内容、频道路径
        
        GameHallLogic->>GameHallLogic: 基础验证
        Note over GameHallLogic: 用户信息、频道解析、禁言检查
        
        GameHallLogic->>RiskService: 风险&频率检查
        Note over RiskService: 黑产检测、发送频率限制
        RiskService-->>GameHallLogic: 检查通过
        
        GameHallLogic->>GameHallLogic: 生成消息内容
        Note over GameHallLogic: 根据消息类型构建消息体
        
        GameHallLogic->>GameHallService: 记录消息
        GameHallService-->>GameHallLogic: 记录成功
        
        GameHallLogic->>PushD: 广播消息
        PushD-->>GameHallLogic: 推送成功
        
        GameHallLogic-->>Client: 返回消息对象
    end

    %% 分隔线
    Note over Client, PushD: ═══════════════════════════════════

    %% 拉取历史消息流程
    rect rgb(248, 255, 248)
        Note over Client, PushD: 📥 拉取历史消息流程
        
        Client->>GameHallLogic: GetGameImMsgList请求
        Note over Client, GameHallLogic: 玩法ID、分页参数、消息类型
        
        GameHallLogic->>GameHallLogic: 参数验证
        Note over GameHallLogic: 用户权限、参数校验
        
        GameHallLogic->>GameHallService: 查询历史消息
        Note over GameHallService: 分页查询、消息过滤
        GameHallService-->>GameHallLogic: 返回消息列表
        
        GameHallLogic->>GameHallLogic: 消息处理
        Note over GameHallLogic: 用户信息填充、消息状态处理
        
        GameHallLogic-->>Client: 返回历史消息列表
        Note over Client, GameHallLogic: 消息列表、分页信息
    end

    Note over Client, PushD: 流程完成
