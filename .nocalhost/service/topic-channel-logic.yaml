- name: topic-channel-logic # 服务deploy名称
  serviceType: deployment
  containers:
    - name: service
      dev:
        ${_INCLUDE_:- ../base/dev-config.yaml | nindent 8}
        command:
          run: # run参数
            - go run ./services/topic-channel-logic/main.go
            - --server.configFile=/config/topic-channel-logic.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
          debug:
            - dlv # debug参数
            - --headless
            - --log
            - --listen :9999
            - --api-version 2
            - --accept-multiclient
            - debug
            - ./services/topic-channel-logic/main.go
            - -- --server.configFile=/config/topic-channel-logic.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
        hotReload: false
        sync:
          mode: "pattern"
          type: "send"
          filePattern:
            - "clients"
            - "protocol"
            - "pkg"
            - "kaihei-pkg"
            - "go.mod"
            - "go.sum"
            - "services/topic-channel-logic" # 服务代码目录
            - "services/notify"
            - "services/runtime/v2"
            - "services/runtime/gwcontext"
            - "services/game-server-v2"
            - "services/topic-channel/notify"
            - "services/topic-channel/channel-server/confz"
          ignoreFilePattern: [ ]
        portForward: []