- name: channel-play-tab # 服务deploy名称
  serviceType: deployment
  containers:
    - name: service
      dev:
        ${_INCLUDE_:- ../base/dev-config.yaml | nindent 8}
        command:
          run: # run参数
            - go run ./services/channel-play-tab/main.go
            - --server.configFile=/config/channel-play-tab.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
          debug:
            - dlv # debug参数
            - --headless
            - --log
            - --listen :9999
            - --api-version 2
            - --accept-multiclient
            - debug
            - ./services/channel-play-tab/main.go
            - -- --server.configFile=/config/channel-play-tab.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
        hotReload: false
        sync:
          mode: "pattern"
          type: "send"
          filePattern:
            - "clients"
            - "protocol"
            - "pkg"
            - "kaihei-pkg"
            - "go.mod"
            - "go.sum"
            - "services/channel-play-tab" # 服务代码目录
            - "services/game-server-v2/client"
            - "services/runtime"
            - "services/notify"
            - "dev-conf"
          ignoreFilePattern: [ ]
        portForward: []