- name: game-play-logic # 服务deploy名称
  serviceType: deployment
  containers:
    - name: service
      dev:
        ${_INCLUDE_:- ../base/dev-config.yaml | nindent 8}
        command:
          run: # run参数
            - go run ./services/game-play-logic/main.go
            - --server.configFile=/config/game-play-logic.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
          debug:
            - dlv # debug参数
            - --headless
            - --log
            - --listen :9999
            - --api-version 2
            - --accept-multiclient
            - debug
            - ./services/game-play-logic/main.go
            - -- --server.configFile=/config/game-play-logic.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
        hotReload: false
        sync:
          mode: "pattern"
          type: "send"
          filePattern:
            - "clients"
            - "protocol"
            - "pkg"
            - "kaihei-pkg"
            - "go.mod"
            - "go.sum"
            - "services/game-play-logic" # 服务代码目录
            - "services/bots/userline-robot" # 服务代码目录
            - "services/user/mystery-box" # 服务代码目录
            - "services/game-server-v2" # 服务代码目录
            - "services/runtime"
            - "services/notify"
            - "dev-conf"
          ignoreFilePattern: [ ]
        portForward: []