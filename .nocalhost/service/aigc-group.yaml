- name: aigc-group # 服务deploy名称
  serviceType: deployment
  containers:
    - name: service
      dev:
        ${_INCLUDE_:- ../base/dev-config.yaml | nindent 8}
        command:
          run: # run参数
            - go run ./services/aigc/aigc-group/main.go
            - --server.configFile=/config/aigc-group.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
          debug:
            - dlv # debug参数
            - --headless
            - --log
            - --listen :9999
            - --api-version 2
            - --accept-multiclient
            - debug
            - ./services/aigc/aigc-group/main.go
            - -- --server.configFile=/config/aigc-group.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
        hotReload: false
        sync:
          mode: "pattern"
          type: "send"
          filePattern:
            - "clients"
            - "protocol"
            - "pkg"
            - "go.mod"
            - "go.sum"
            - "services/aigc/aigc-group" # 服务代码目录
            - "dev-conf"
          ignoreFilePattern: [ ]
        portForward: []
        env:
          - name: DYEING_ENVIRONMENT_MARK
            value: 