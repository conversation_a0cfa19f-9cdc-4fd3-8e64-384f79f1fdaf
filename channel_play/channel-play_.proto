syntax = "proto3";

package ga.channel_play;

import "ga_base.proto";
import "topic_channel/topic_channel_.proto";
import "channel_play/channel-play-view_.proto";
import "hobby_channel/hobby-channel_.proto";
import "game_pal_logic/game_pal_logic.proto";


option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel-play";

enum RCMDLabelEnum {
  RCMDLabel_None = 0;
  RCMDLabel_GangUpWithHomeOwner = 1;
  RCMDLabel_ChatWithHomeOwner = 2;
}
message TopicChannelView{
  oneof channel_view{
    channel_play.ChannelViewDefault view_default = 1;
    channel_play.ChannelViewMOBA view_moba = 2;
    channel_play.ChannelViewKtv view_ktv = 3; //旧版本才会返回ktv view
    channel_play.ChannelViewMysteryEscape view_mystery_escape = 4;
    channel_play.ChannelViewMarshal view_marshal = 5; //新增通用view
    channel_play.ChannelViewNewMusic view_new_music = 6; // 音乐view
  }
}

message PersonalCert{
  string icon = 1;
  string text = 2;
  repeated string color = 3;
  string text_shadow_color = 4;
}

enum ChannelLabel {
  ChannelLabelNone = 0;
  ChannelLabelQuality = 1;//优质
  ChannelLabelHot = 2;//热门
}

message TopicChannelItem{
  uint32 channel_id = 1; //房间Id
  string channel_name = 2; //房间名

  uint32 owner_uid = 3;
  string channel_owner_username = 4; //房主用户名
  int32 channel_owner_sex = 5; //房主性别,男1女2

  uint32 member_count = 6; //在房人数

  TopicChannelView topic_channel_view = 7;
  RCMDLabelEnum rcmd_label = 8; //推荐行为标签,6.48版本后使用under_head_img_label

  string geo_info = 9; // 地理位置信息 6.48版本后使用rcmd_display_label

  uint32 level_id = 10;      //房间等级id
  topic_channel.RecommendationTraceInfo trace_info = 11; // 用于推荐系统与数据中心跟踪统计用户行为，没具体业务意义(透穿)

  uint32 tab_id = 12;
  string tab_name = 13;

  bool owner_mismatch_version = 14; // 房主是否不符合版本(服务端指定)，true表示房主不符合版本要求

  channel_play.GenViewSource source = 15; //业务方标识

  repeated PersonalCert person_cert = 16; //用户认证标签

  ChannelLabel label = 17;//房间标签(优质房间，热门房间)

  string under_head_img_label = 18; //用户头像下方标签文案，关注的人＞一起玩过＞同城标签
  string middle_position_label = 19; //卡片中部位置标签文案，房间内用户头像后方标签文案。关注的人在房>最近一起玩过在房

  string in_channel_text = 20;
  message FriendInfo {
    string account = 1;
    string nickname = 2;
    uint32 uid = 3;
  }
  FriendInfo friend_info = 21;
  string delivery_type = 22;//强摇同逻辑房间需要的类型
  repeated string relationship_labels = 23;  // 用来上报的标签:[客户端的relationship_label]

  uint32 on_mic_member_count = 24; //上麦人数
  uint32 category_type = 25;   // 标识当前tab所属的分类类别，1 一起开黑, see enum topic_channel_.proto CategoryType
}

//男1女2
enum RcmdSex{
  All = 0;
  Female = 2;
  Male = 1;
}

//房间列表入口
enum ChannelListEnterSource{
  GangupHomePage = 0;
  MysteryHomePage = 1;
  ScenarioPage = 2;
  MysteryHomeScreen = 3; // 谜境主页半屏房间列表
  MysteryChannelScreen = 4; // 谜境密逃房半屏房间列表
  MysteryHomeCarousel = 5; // 谜境首页轮播头像房间列表

  MixHomePageSource = 6; // 2023新版首页
  GameZoneSource = 7; // 游戏专区
  MiniGameZoneSource = 8; // 小游戏专区
  PcFastHomePageSource = 9; // PC极速版首页
  PcFastHallSource = 10; // PC极速版大厅
}

message FilterBlockOption {
  uint32 block_id = 1;           //块id
  uint32 elem_id = 2;            //块里面的元素id
  FilterType filter_type = 3;    //筛选器字段类型
}

enum FilterType{
  Base_Block_Filter = 0;       //基础筛选项，tab关联的发布字段
  Mystery_Game_Condition_Filter = 1;  //迷境游戏属性相关过滤项，目前包括房间模式、对局状态
}

enum ChannelListStyleType{
  Default_Group = 0;//默认对照组，跟TT首页对照组一致,样式见：https://www.tapd.cn/32571206/prong/stories/view/1132571206001064770
  HuanYou_Experiment_Group = 1; //欢游实验组，2023.3.16 样式见：https://q9jvw0u5f5.feishu.cn/docx/Iw4TdpqV4oYDgixyWqecdZAYnjg
  Mix_Business_Home_Page = 2; // 2023.4.4 首页改版 样式见：https://q9jvw0u5f5.feishu.cn/docx/RxqBdU4FLoOXIOxWNn7c69zcnQc
}



message ListTopicChannelReq{
  ga.BaseReq base_req = 1;
  uint32 count = 2;    //获取几个, 默认10
  repeated FilterBlockOption block_option = 3; //用户所选筛选信息, eg: [<block_id:1, elem_id:100>,<block_id:1, elem_id:101>,<block_id:2, elem_id:200>]
  uint32 tab_id = 4; //0开黑首页为推荐，迷境首页为其他
  RcmdSex sex = 5; //性别0：不限，1：男，女：2

  uint32 get_mode = 6; //1代表下一页，2代表刷新。替换了旧版本的loadmore

  string channel_package_id = 7;  //渠道包id

  repeated uint32 no_browse_list = 8; //请求列表曝光channelid列表

  repeated uint32 category_ids = 9; //分类id，用于全选小游戏 为了兼容以后万一有多选分类的，就设置成数组
  repeated uint32 tab_ids = 10;  //用于多选小游戏

  // 用户询问, 喜好游戏及标签
  repeated topic_channel.PrefGame pref_games = 11;

  //repeated uint32 all_selected_bids = 12;    //已全选的blockId

  //玩法标签数组
  repeated topic_channel.GameLabel labels = 12; // 标签筛选
  //区分首页入口
  ChannelListEnterSource channel_list_enter_source = 13;
  //房间列表样式
  ChannelListStyleType list_style_type = 14;
  //提供view的来源标识，MUSIC代表由音乐业务方提供ktv view，MYSTERY代表由迷境业务方提供mystery view,不填或则填GAME则走旧逻辑所有view由开黑提供
  repeated channel_play.GenViewSource gen_view_source = 15;
  //是否开启热门玩法
  bool enable_game_label = 16;
  //兴趣标签
  repeated string interest_labels = 17;
  string delivery_type = 18;//强摇同逻辑房间需要的类型
  // 用户设置的屏蔽词
  repeated string shield_filter_words = 19;
  // 分类标签筛选
  repeated ClassifyLabelList classify_labels = 20; // 分类标签

  // 音乐玩法房间列表传参
  string filter_id = 21;//一级筛选
  repeated string sub_filter_ids = 22;//二级筛选
  bool is_user_location_auth_open = 23; // 用户定位授权是否开启
  enum FilterModel {
    FILTER_MODEL_MT = 0; // mt筛选，默认筛选方式
    FILTER_MODE_KH = 1; // 开黑筛选方式
  }
  FilterModel filter_model = 24; // 筛选方式
  GameLocationItem location_item = 25; // 请求用户的地理位置信息
}

message ListTopicChannelResp{
  ga.BaseResp base_resp = 1;
  repeated TopicChannelItem items = 2;
  bool load_finish = 3;                        //返回true表示没有下一页了
  // 预约开黑相关配置
  topic_channel.GangConf gang_conf = 4;
  // 用户询问, 喜好游戏及标签
  repeated topic_channel.PrefGame pref_games = 5;
  // 用户喜好游戏询问插入下标
  uint32 pref_game_pos = 6;

  //仅用于客户端数据上报字段
  message DataReport{
    string footprint = 1; //推荐trace id
  }
  DataReport report_data = 7;
  bool is_recommend_doudi = 8; //是否推荐兜底房间
}

message GetSecondaryFilterReq{
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2;
  enum Mode {
    PUBCHANNEL = 0;             // 发布房间
    MATCHTEAMMATE = 1;          // 速配队友
    FILTER = 2;                 // 筛选，6.28版本以前使用，返回不限，单选，只返回运营后台配置的6.28版本以前可以展示的筛选项
    QUESTION = 3;    // 问题弹窗
    PubChannelAfter628 = 4; // 6.28版本后，发布条件弹窗
    FilterAfter628 = 5; // 6.28版本标识，首页筛选弹窗，返回不限，单选，返回所有筛选项
    QuestionAfter628 = 6; // 6.28版本后，问题弹窗
    FilterWithMultiOption = 7; // 旧首页筛选弹窗，不返回不限，返回多选，返回所有筛选项
    PubChannelAfter640 = 8; // 6.40版本后，发布条件弹窗场景，新增数值类型填写block
    PC_HOME_PAGE = 9; // PC首页筛选面板
    HUAN_YOU_MIX_LABEL = 10; //欢游首页筛选混合标签（其它游戏）
  }
  Mode mode = 3; // 根据筛选模式返回筛选字段

  enum Source {
    SourceHome = 0; // 首页
    SourceScreen = 1; // 半屏
  }

  Source source = 4; // 请求来源
  topic_channel.BrowseLabel browse_labels = 5; // 已曝光玩法标签
}

message GetSecondaryFilterResp {
  ga.BaseResp base_resp = 1;
  repeated topic_channel.Block blocks = 2; // 跟tab发布字段相关的筛选器
  uint32 tab_id = 3;
  enum TabType {
    NORMAL = 0; //普通分类。
    GAME = 1;   //游戏分类。
    MINI_GAME = 2; // 小游戏
  }
  TabType tab_type = 4;
  enum MatchType {
    QUICK = 0; // 快速匹配，即旧匹配。
    TEMPORARYHOUSE = 1; // 临时房匹配
  }
  MatchType match_type = 5;
  uint32 channel_source = 6;
  uint32 tab_version = 7; //版本号
  bool fresh_option = 8; //优先匹配萌新显示开关，待推荐接入后开启
  bool is_hidden_geo_option = 9;  //是否隐藏地理位置选项
  repeated BusinessFilterItem business_blocks = 10; //跟业务相关的筛选器
  bool need_highlight = 11; //发布房间字段，规范用户行为，true-需要高亮提醒展示，false-不需要高亮提醒展示
  repeated DisplayBlockInfo display_block_infos = 12; //客户端外显的发布项及关联关系
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  string GuidingForNewBie = 13; //萌新军引导语，当用户是萌新军时返回配置的文案，不是的话则返回空
  //玩法标签数组
  repeated topic_channel.GameLabel labels = 14; // 标签筛选
  string notice_msg = 15; //提示信息
  // 是否展示用户设置的屏蔽词
  bool show_filter_words = 16;
  repeated ClassifyLabelList classify_labels = 17; // 分类标签
  GameLocationItem user_ip_loc = 18; // 发布面板返回用户IP的位置信息
}

//发布二级字段绑定的一级字段id
message ElemBindBlockInfo {
  uint32 elem_id = 1; //二级字段id
  repeated topic_channel.Block bind_blocks = 2;//二级字段绑定的一级字段信息，客户端选中该二级字段后，需要展示的关联的一级字段部分筛选项
}

//客户端外显发布字段关系
message DisplayBlockInfo {
  topic_channel.Block block = 1; //一级字段id
  repeated ElemBindBlockInfo elem_bind_block_infos = 2; //该block下的二级字段的关联信息
}

message BusinessFilterItem{
  FilterType filter_type = 1;
  uint32 id = 2;
  string title = 3;
  // Mode 是 Block 的选择模式
  enum Mode {
    SINGLE = 0;     //单选
    MULTI = 1;      //不限
    SETUP_NUMBER = 2;    //最多选N个，扩展用
  }
  Mode mode = 4;
  repeated BusinessFilterElem elems = 5;
  uint32 most_select_num = 6; //当Mode=SETUP_NUMBER时有效
}

// 业务筛选器Elem 元素，多个元素构成一个栏目
message BusinessFilterElem {
  uint32 id = 1;
  string title = 2;
  // Mode 是 Elem 的模式
  enum BusinessFilterMode {
    NORMAL = 0;    // 普通
    RECOMMEND = 1; // 推荐
    INFINITE = 2;  // 不限
  }
  BusinessFilterMode mode = 3;
}

message GetSecondaryFilterByCategoryReq{
  ga.BaseReq base_req = 1;
  uint32 category_id = 2;
  string channel_pkg = 3; // 用户渠道号
}

message GetSecondaryFilterByCategoryResp{
  ga.BaseResp base_resp = 1;
  repeated ga.hobby_channel.GameHomePageFilterItem items = 2;
  repeated BusinessFilterItem business_blocks = 3; //跟业务相关的筛选器
}

message DefaultRoomNameConfig{
  string name = 1;
  uint32 elem_id = 2;
  // 透传给客户端埋点上报
  uint32 source = 3;
}

message GetDefaultRoomNameListReq{
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2;
  string room_name = 3;
  uint32 page_index = 4;
  enum GetMode {
    UNSPECIFIED = 0;
    NEXT_PAGE = 1;
    REFRESH = 2;
  }
  // 请求推荐用:1-加载更多,2-刷新
  uint32 get_mode = 5;
  enum ReqSource {
    DEFAULT = 0;
    AFTER_661_VERSION = 1;
  }
  uint32 req_source = 6;
}

message GetDefaultRoomNameListResp{
  ga.BaseResp base_resp = 1;
  repeated DefaultRoomNameConfig default_room_name_list = 2;
  uint32 next_page_index = 3;
  bool load_finish = 4; // 返回true表示没有下一页了
}

//发布房间
message PublishGangupChannelReq {
  BaseReq base_req = 1;

  uint32 channel_id = 2;

  uint32 tab_id = 3;               // 主题房所选分类id

  repeated topic_channel.BlockOption block_options = 4;  // 用户所选的标签信息, see topic-channel_.proto

  string channel_name = 5;     // 主题房名

  bool is_want_fresh = 6;      // 是否优先匹配萌新
  bool is_show_geo_info = 7;   // 是否允许同城匹配

  string diy_hobby_name = 12;  // 自定义兴趣名称，用于其他游戏自定义游戏名

  repeated uint32 all_selected_bids = 13;
  bool need_check_channel_name = 14; //true-需要检查房名匹配玩法，false-默认，不检查

  UgcChannelPlayMode ugc_channel_play_mode = 15; //房间玩法模式（语音or文字）

  repeated topic_channel.GameLabel game_labels = 16; // 用户选中的推荐三级标签上报

  // 发布block后续带的按钮处理
  repeated BlockButtonOpt block_button_opts = 17; // 发布block后续带的按钮处理

  // 跳转tabId, 兼容MT玩法的发布的特殊情况（热聊）
  uint32 jump_tab_id = 18;
  // 定位信息（暂定经纬度）
  GameLocationItem location_item = 19;
}

message GameLocationItem{
  string country = 1;
  string country_code = 2;
  string province =3;
  uint32 province_code = 4;
  string city = 5;
  uint32 city_code = 6;
  double latitude = 7;
  double longitude = 8;
}

message BlockButtonOpt {
  uint32 type = 2; // see topic_channel_.proto enum BlockButtonType
  uint32 block_id = 3;
  repeated uint32 elem_id = 4;
}

message PublishGangupChannelResp {
  BaseResp base_resp = 1;
  uint32 change_cool_down = 2;         // 房间发布信息修改cd
  uint32 freeze_duration = 3;         // 房间发布信息cd
  uint32 auto_dismiss_duration = 4;   // 自动取消时长
  repeated topic_channel.ShowTopicChannelTabSecondaryItem secondary_item = 5; // 返回的推荐玩法详情, see topic-channel_.proto
}

//房间发布取消
message CancelGangupChannelPublishReq {
  BaseReq base_req = 1;

  uint32 channel_id = 2;

  uint32 tab_id = 3; //客户端带tab_id，做校验
}

message CancelGangupChannelPublishResp {
  BaseResp base_resp = 1;
}

// 首页请求来源
enum HomePageHeadConfigReqType {
  // 不传默认为 6.45版本前的TT新版首页
  HOME_PAGE_HEAD_CONFIG_REQ_TYPE_UNSPECIFIED = 0;
  // 6.45版本后的TT新版首页
  HOME_PAGE_HEAD_CONFIG_REQ_TYPE_TT_NEW = 1;
  // 旧版TT首页
  HOME_PAGE_HEAD_CONFIG_REQ_TYPE_TT_OLD = 2;
  // 欢游首页
  HOME_PAGE_HEAD_CONFIG_REQ_TYPE_HUANYOU = 3;
  // PC首页
  HOME_PAGE_HEAD_CONFIG_REQ_TYPE_PC = 4;
  // 6.56.5版本后的TT首页
  HOME_PAGE_HEAD_CONFIG_REQ_TYPE_TT_656 = 5;
}

// 首页金刚区请求
message HomePageHeadConfigReq {
  BaseReq base_req = 1;
  repeated uint32 self_game_ids = 2; // 用户手机有的 gameId
  repeated uint32 active_tab_ids = 3; // 用户最近进过房的玩法id
  uint32 req_type = 4;  // 首页请求来源，see HomePageHeadConfigReqType
}

message HomePageHeadConfigResp {
  BaseResp base_resp = 1;
  repeated HomePageHeadConfigItem configs = 2;
}

enum HomePageHeadConfigEnum {
  CONFIG_GAME_TYPE = 0; // 游戏专区
  CONFIG_CASUAL_GAME_TYPE = 1; // 休闲互动游戏专区
  CONFIG_ESCAPE_ROOM_TYPE = 2; // 密室逃脱
  CONFIG_CHAT_TYPE = 3; // 扩列聊天
  CONFIG_MUSIC_TYPE = 4; // 音乐
  CONFIG_SOUND_SCENE_TYPE = 5; // 声动现场，pia戏
  CONFIG_ACTIVITY_CENTER_TYPE = 6; // 活动中心
  CONFIG_COMPETITION_CENTER_TYPE = 7; // 赛事中心
  CONFIG_ESPORTS_TYPE = 8; // 电竞专区
  CONFIG_MUSE_FLASH_CHAT_TYPE = 9; // 即刻闪聊
  CONFIG_ROLE_PLAY_TYPE = 10; // 角色扮演
}

message HomePageHeadConfigItems {
  repeated HomePageHeadConfigItem items = 1;
}

message HomePageHeadConfigItem {
  // 配置id，用于识别新用户引导匹配
  string config_id = 1;
  // 专区类型
  HomePageHeadConfigEnum config_type = 2;
  // 主标题
  string title = 3;
  // 副标题
  string sub_title = 4;
  // 底图， 旧版代表展示两个一行的大图
  string background = 5;
  // 动画效果配置
  Vap vap = 6;
  // 广告标签，字数-最长配置7个字
  string ad_tag = 7;
  // 游戏专区入口取房间当前组队用户头像，成组切换，提示xxx场组队
  GameOverview game_overview = 8;
  // 专区客户端跳转短链
  string jump_link = 9;
  // 是否可以被替换赛事中心，6.45废弃
  bool is_replace = 10;
  // 金刚区收起时展示的小图， 旧版代表展示三个一行的小图
  string small_background = 11;
  // 根据用户的游戏标签，轮播对应配置文案
  repeated TagConfigInfo tag_config_info = 12;
  // 是否需要检查入口
  bool check_enter = 13;
  // 6.56.5版本后的TT首页，轮播配置
  RichTagConfigInfo rich_tag_config_info = 14;
  // 轮播配置类型，见TagConfigType
  uint32 tag_config_type = 15;
  oneof extra_data {
    // 赛事列表  config_type = 7有用
    HomePageHeadCompetitionConfig competition_config = 50;
    // 闪聊额外展示数据 config_type = 9有用
    HomePageHeadFlashChatConfig flash_chat_config = 51;
    HomePageHeadMultipleBannersConfig multiple_banners_config=52;
  }
}

message HomePageHeadMultipleBannersConfig{
  bytes data = 1;   //HomePageHeadConfigItems
}

enum TagConfigType {
  TAG_CONFIG_TYPE_UNSPECIFIED = 0;
  TAG_CONFIG_TYPE_DEFAULT = 1; // 取TagConfigInfo字段的轮播配置
  TAG_CONFIG_TYPE_RICH = 2; // 取rich_tag_config_info字段的轮播配置
}

message Vap {
  string url = 1;
  string md5 = 2;
}

message GameOverview {
  repeated string account = 1; // 返回随机多个用户账号，客户端再取头像，轮播头像
  string text = 2;  // xxx场组队 文案
}

message TagConfigInfo {
  string tag_title = 3;// 轮播对应配置文案 第一行
  string tag_sub_title = 4;// 根据用户的游戏标签，轮播对应配置文案 第二行
  string pic = 5; // 小图片url
  uint32 tab_id = 6;
  // bool need_link_tab_position = 7; // 首页跳转后是否需要定位到对应的玩法
  uint32 config_type = 8; // 轮播配置的样式类型，纯文本（默认）、按钮等, 见HomePageGuideStyle
  string button_text = 9; // 按钮外显文案
}

message RichTagInfo {
  string background_color = 1; // 底色
  string label_text = 2; // 标签文案
  string main_tag_text = 3; // 主标文案
  uint32 tab_id = 4; // 跳转到的玩法id
  string config_tab_id = 5; //跳转到的config_tab_id
}

message RichTagConfigInfo {
  repeated RichTagInfo rich_tag_info = 1; // 轮播的配置
  uint32 config_type = 2; // 轮播配置的样式类型，纯文本（默认）、按钮等, 见HomePageGuideStyle
  string button_text = 3; // 按钮外显文案
}

message HomePageHeadCompetitionConfig {
  // 小副标题，旧版tt首页特有，旧版代表展示三个一行时展示
  string small_sub_title = 1;
  repeated HomePageHeadCompetitionList competition_list = 3;
}

message HomePageHeadCompetitionList {
  // 赛事主标题
  string title = 1;
  // 赛事副标题
  string subtitle = 2;
  // 赛事背景图
  string background = 3;
  // 跳转链接
  string link = 4;
  // 赛事标签词语
  string tag_text = 5;
  // 赛事标签颜色
  string tag_color = 6;
}

message HomePageHeadFlashChatConfig {
  // 目前都是MT那边接口返回数据，为了跨团队合作方便，使用字节流处理，使用 muse_interest_hub_logic.proto HomePageHeadMuseExtraInfo解析
  bytes data = 1;
}

message HotMiniGame {
  // 玩法id
  uint32 tab_id = 1;
  // 外显游戏名
  string display_name = 2;
  // 介绍文案
  string intro_text = 3;
  // 背景图 url
  string bg_url = 4;
  // 背景颜色
  string bg_color = 5;
  // 按钮文案
  string button_text = 6;
  // 按钮颜色
  string button_color = 7;
  // 按钮跳转
  string button_link = 8;
  // 小游戏配置
  topic_channel.MiniGameConfig config = 9;
  // 动图 lotties url
  string lottie_url = 10;
  // 按钮颜色组
  repeated string button_colors = 11;
}

message GetHotMiniGamesReq {
  ga.BaseReq base_req = 1;

  // 渠道包
  string channel_pkg = 2;
}

message GetHotMiniGamesResp {
  BaseResp base_resp = 1;

  // 区域名
  string area_name = 2;
  // 默认的玩法id
  uint32 default_tab_id = 3;
  // 重点小游戏列表
  repeated HotMiniGame list = 4;
}

enum ButtonEffect{
  BUTTON_INVALID = 0;
  BUTTON_MATCH = 1; //快速匹配
  BUTTON_LINK = 2; //短链跳转
}

message QuickMiniGame {
  // 玩法id
  uint32 tab_id = 1;
  // tab对应的游戏icon
  string game_icon = 2;
  // 游戏名
  string game_name = 3;
  // 背景色值
  repeated string bg_colors = 4;

  // 小游戏配置
  topic_channel.MiniGameConfig config = 5;

  //按钮效果，快速匹配or短链,见ButtonEffect
  uint32 button_effect = 6;

  //跳转短链，当button_effect=2时有效
  string jump_link = 7;
}

message GetQuickMiniGamesReq {
  enum Source {
    SourceUnknown = 0;
    // 外显
    SourceExpose = 1;
    // 更多
    SourceMore = 2;
  }

  ga.BaseReq base_req = 1;

  // 请求来源
  Source src = 2;
  // 渠道包
  string channel_pkg = 3;
}

message GetQuickMiniGamesResp {
  ga.BaseResp base_resp = 1;

  // 快速匹配小游戏玩法列表
  repeated QuickMiniGame list = 2;
  // 快速匹配区域名
  string area_name = 3;
}

message GameInsertFlowConfigReq {
  ga.BaseReq base_req = 1;
}

message GameInsertFlowConfigResp {
  ga.BaseResp base_resp = 1;
  enum ConfigType {
    QUICK_MATCH = 0;
    CREATE_ROOM = 1;
  }
  QuickMatchConfig quick_match_conf = 2;
  CreateRoomConfig create_room_conf = 3;
}

message QuickMatchConfig{
  string title = 1;
  string sub_title = 2;
  string background = 3;
  string button_text = 4;
}

message CreateRoomConfig {
  string title = 1;
  string background = 2;
  string button_text = 3;
}

enum HomePageGuideStyle {
  HOME_PAGE_GUIDE_STYLE_UNSPECIFIED = 0; // 返回0默认走纯文本样式
  HOME_PAGE_GUIDE_STYLE_TEXT = 1; // 纯文本样式
  HOME_PAGE_GUIDE_STYLE_BUTTON = 2; // 带按钮样式
}

// 3091
message GetHomePageGuideReq {
  ga.BaseReq base_req = 1;
  string home_page_config_id = 2; // 金刚区配置id
}

message GuideContent {
  string title = 1;    // 引导文案:主标题
  string sub_title = 2;    // 引导文案:副标题，为空不展示
  uint32 style = 3;    // 引导样式，see HomePageGuideStyle
}

message HomePageSubmodule {
  GuideContent guide_content = 1;
}

message GetHomePageGuideResp {
  ga.BaseResp base_resp = 1;
  string home_page_config_id = 2;
  GuideContent guide_content = 3;           // 展示的引导配置
  HomePageSubmodule submodule = 4;    // 子模块的配置
}

// 3092
message GetMoreTabConfigReq {
  ga.BaseReq base_req = 1;
  FilterEntrance filter_entrance = 2;
  string channel_pkg = 3; // 用户渠道号
  //  repeated uint32 self_game_list = 4; //手机内有装游戏的列表
  bool is_need_minority_game = 4; // 是否需要返回小众游戏外显
}

message GetMoreTabConfigResp {
  ga.BaseResp base_resp = 1;
  repeated MoreTabItem items = 2;
}

message MoreTabItem {
  CommonBusinessFlag common_business_flag = 1; //业务标识
  CategoryInfo category_info = 2;              // FilterItemType:GAME_CATEGORY
  MoreTabMusicInfo music_info = 3;                    // FilterItemType:MUSIC_ITEM
  MixInfo mix_info = 4;                        // FilterItemType:MIX_CATEGORY
}

message CategoryInfo {
  uint32 category_id = 1; // 分类id
  string category_name = 2; // 分类名称
  message CategoryInfoItem {
    CommonBusinessFlag common_business_flag = 1; //业务标识，FilterItemType:GAME_TAB
    topic_channel.Tab tab_detail = 2;  // 具体开黑玩法信息
    topic_channel.TabAlias tab_alias = 3;
  }
  repeated CategoryInfoItem category_info_item = 3; // 具体玩法内容
  uint32 can_select_num = 4; // 可选数量 0为可全选
}



message MoreTabMusicInfo {
  string item_id = 1;         // 客户端根据该id去请求音乐接口，返回一级和二级子类的信息
  uint32 can_select_num = 2; // 可选数量，不可全选
  //  message MusicInfoItem {
  //    CommonBusinessFlag common_business_flag = 1; // FilterItemType:MUSIC_ITEM
  //    string item_id = 2;
  //  }
  //  repeated MusicInfoItem music_info_item = 2;   // 一级下的二级子类
}

message MixInfo {
  string id = 1;       // 自定义的虚拟混合id
  string name = 2;       // 名称，"热门分类"
  message MixCategoryItem {
    CommonBusinessFlag common_business_flag = 1; //业务标识
    string item_id = 2;                 // FilterItemType:MUSIC_ITEM
    topic_channel.Tab tab_detail = 3;   // FilterItemType:GAME_TAB
    topic_channel.TabAlias tab_alias = 4;
  }
  repeated MixCategoryItem mix_item = 3;
  uint32 can_select_num = 4; // 可选数量，不可全选
}

// 玩法问题
message PlayQuestion {
  string title = 1;
  repeated topic_channel.GameLabel labels = 2;
}

message GetPlayQuestionsReq {
  enum Source {
    SOURCE_UNSPECIFIED = 0;
    // 房间列表获客人群来源
    SOURCE_CHANNEL_LIST_NEW = 1;
    // 房间列表活跃人群来源
    SOURCE_CHANNEL_LIST_ACTIVE = 2;
    // 房间内反馈
    SOURCE_FEEDBACK_IN_ROOM = 3;
  }

  ga.BaseReq base_req = 1;

  // 玩法id
  uint32 tab_id = 2;
  // 来源
  uint32 source = 3;
}

message GetPlayQuestionsResp {
  ga.BaseResp base_resp = 1;

  // 玩法问题
  repeated PlayQuestion questions = 2;
  // 玩法发布属性
  repeated topic_channel.Block blocks = 3;
  // 年龄人群标签
  repeated string age_group_labels = 4;
}

// item_id的类型，开黑包括（tab_id, category_id），音乐
enum FilterItemType{
  UNKNOWN_ITEM_TYPE = 0;
  GAME_TAB = 1;
  GAME_CATEGORY = 2;
  MUSIC_ITEM = 3;
  MIX_CATEGORY = 4; // 热门混合一级分类，子类混合音乐开黑
  MUSIC_FILTER = 5; //新板音乐筛选
}

//标识业务
message CommonBusinessFlag{
  uint32 game_business_id = 1; //当filter_item_type=1或2时有效， game_business_id = 0 代表推荐筛选项
  string music_business_id = 2; //当filter_item_type=3时有效
  FilterItemType filter_item_type = 3; //id的类型
}

message FilterItem {
  CommonBusinessFlag common_business_flag = 1; //业务标识
  string display_name = 2; //筛选器外显名
  TabItem tab_filter_item = 3; //当filter_item_type=1时有效，内容为tab的id，名称以及二级筛选项
  CategoryItem category_filter_item = 4; //当filter_item_type=2时有效，内容为开黑category下的玩法信息
  string tab_icon=5;  //首页玩法图标
}

//游戏专区，玩法筛选器
message GameLabelFilter{
  string label_display_name = 1; //玩法外显名
  repeated topic_channel.GameLabel labels = 2; // 关联的玩法标签
  bool show_hot = 3; //true展示hot标识，false不展示
  bool is_rcmd = 4; //是否是推荐项
}

message TabItem{
  uint32 tab_id = 1;
  string tab_name = 2; //tab名称
  repeated topic_channel.Block blocks = 3; // 跟tab发布字段相关的筛选器

  repeated topic_channel.GameLabel labels = 4; // 热门玩法标签
  bool enable = 5; // true 表示 玩法功能开启， false：玩法功能关闭

  repeated GameLabelFilter game_label_filter = 6; //玩法筛选器
  string tab_display_image_url = 7; //筛选器外显图片url

  GameZoneTabType game_zone_tab_type = 8; // filter_item_type=1时有效，开黑专区下tab类型，0-普通玩法，1-综合频道
  uint32 category_type = 9; // 标识当前tab所属的分类类别，1 一起开黑, see enum topic_channel_.proto CategoryType
  repeated ClassifyLabelList classify_labels = 10; // 分类标签
  repeated uint32 tab_ids = 11; // tab_ids 混合tabid列表,只有音乐在用
}

message ClassifyLabelList {
  string classify_name = 1; // 分类名称
  repeated topic_channel.GameLabel classify_labels = 2; // 分类下的标签列表
}

message NewMusicFilterItem{
  string filter_id = 1;
  repeated topic_channel.Block blocks = 2; // 跟tab发布字段相关的筛选器

  repeated topic_channel.GameLabel labels = 3; // 热门玩法标签
  bool is_label_on = 4; //是否开启热门玩法
  repeated ClassifyLabelList classify_labels = 5; // 分类标签
  repeated uint32 tab_ids = 6; // tab_ids 混合tabid列表
}

// 区分游戏专区普通玩法和综合频道tab
enum GameZoneTabType{
  GameZoneTabType_Game = 0;
  GameZoneTabType_ComprehensiveChannel = 1;
}

message CategoryItem{
  uint32 category_id = 1;
  string category_name = 2; //category名称
  repeated TabItem tab_items = 3; //指定category下的tab列表
}

enum FilterEntrance{
  UNKNOWN_ENTRANCE = 0;
  HOME_PAGE = 1; // 首页
  GAME_ZONE = 2; // 游戏专区
  MINIGAME_ZONE = 3; // 小游戏专区
  GAME_PAL = 4; // 游戏搭子
  FILTER_ENTRANCE_REGISTER = 5; // 新用户注册页，自动设置首页常玩
  HOME_PAGE_ADD_MUSIC_FILTER = 6; // 增加音乐筛选的首页
}

enum TabItemVersion {
  Default_Version = 0; //6.28版本以前，返回不限，单选，后台配置了能够在6.28版本以前展示的筛选项
  After628_Version = 1; //6.28版本标识，返回不限，单选，展示所有筛选项
  TAB_ITEM_VERSION_MULTI_OPTION= 2; // 不返回不限，多选
}

//命令号3093
message GetFilterItemByEntranceReq{
  BaseReq base_req = 1;
  repeated uint32 self_game_ids = 2; // 用户手机有的 gameId
  repeated CommonBusinessFlag active_filter_item = 3; // 需要强插的筛选项
  FilterEntrance filter_entrance = 4;
  string channel_pkg = 5; // 用户渠道号（旧逻辑）
  TabItemVersion tab_item_version = 6; //筛选项版本控制
  map<uint32, topic_channel.BrowseLabel> browse_labels_map = 7; // tab_id -> BrowseLabel

}

message GetFilterItemByEntranceResp{
  BaseResp base_resp = 1;
  repeated FilterItem items = 2;
  repeated FilterItem comprehensive_items = 3;  // 游戏专区综合频道列表，filter_entrance=2时生效
  // 是否展示用户设置的屏蔽词
  bool show_filter_words = 4;
}

//命令号3094
message SetDIYFilterByEntranceReq{
  BaseReq base_req = 1;
  FilterEntrance filter_entrance = 2;
  repeated CommonBusinessFlag items = 3; // 用户保存的筛选项
}

message SetDIYFilterByEntranceResp{
  BaseResp base_resp = 1;
  bool is_in_abtest = 2; // 是否在实验组，FILTER_ENTRANCE_REGISTER有效
  uint32 need_jump_tab_id = 3; // 需要跳转的tab_id，FILTER_ENTRANCE_REGISTER有效
}

//命令号3095
message GetDIYFilterByEntranceReq{
  BaseReq base_req = 1;
  FilterEntrance filter_entrance = 2;
  string channel_pkg = 3; // 用户渠道号,用于未成年监管（旧逻辑）
}

message GetDIYFilterByEntranceResp{
  BaseResp base_resp = 1;
  repeated DIYItem diy_item = 2; // 用户自定义筛选项
}
message DIYItem {
  CommonBusinessFlag diy_item = 1; //筛选项标签
  string display_name = 2; //筛选项名称
}

enum FeedBackTypeInRoom {
  Unknown_FeedBackType = 0;
  KnockOut_FeedBackType = 1;
  VisitorList_FeedBackType = 2;
  RoomVisitor_FeedBackType = 3;
  // 退房不感兴趣反馈
  QuitUninterested_FeedBackType = 4;
}

//房间内负反馈列表
message GetNegativeFeedBackInRoomReq {
  BaseReq base_req = 1;
  FeedBackTypeInRoom feed_back_type = 2; //房间内负反馈入口
}
message GetNegativeFeedBackInRoomResp {
  BaseResp base_resp = 1;
  repeated string reasons = 2; //运营后台配置的反馈原因
  string mask_title = 3; //屏蔽勾选的文案，运营后台可配
}

//上报房间内负反馈
message ReportNegativeFeedBackInRoomReq{
  BaseReq base_req = 1;
  FeedBackTypeInRoom feed_back_type = 2; //房间内负反馈入口
  uint32 reporter_uid = 3; // 上报者uid
  uint32 black_uid = 4;  // 被反馈的用户uid
  repeated string reasons = 5; // 反馈进房用户原因
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  uint32 channelId = 6;// 所在房间id
  bool enable_filter = 7; // 是否开启过滤被反馈的用户
  uint32 tab_id = 8; // 玩法ID 
}

message ReportNegativeFeedBackInRoomResp{
  BaseResp base_resp = 1;
  enum HitType {
    HIT_TYPE_UNSPECIFIED = 0;
    HIT_TYPE_AGE = 1; // 反馈原因触发年龄弹窗
  }
  uint32 hit_type = 2;
}

//房间发布选项引导
message GetPublishOptionGuideReq{
  BaseReq base_req = 1;
  uint32 uid = 2; //请求的用户uid
  uint32 tab_id = 3; //当前房间玩法id
  string room_name = 4; //房间标题
}

message GetPublishOptionGuideResp{
  BaseResp base_resp = 1;
  // 6.60及之前版本的发布选项引导
  repeated PublishOptionElem publish_option_elems = 2;
  // 6.61之后版本推荐选择标签
  repeated RecommendationElem rcmd_option_elems = 3;
  // 6.61之后版本推荐填写标签
  repeated RecommendationElem rcmd_input_elems = 4;
}

message PublishOptionElem {
  uint32 block_id = 1;
  uint32 elem_id = 2;
  string elem_title = 3;
}

message RecommendationElem {
  enum Type{
    TYPE_UNSPECIFIED = 0; // 默认
    TYPE_Publish = 1; // 发布条件
    TYPE_ThirdLabel = 2; // 三级标签
  }
  uint32 type = 1; // 类型
  oneof data {
    PublishOptionElem publish_option_elem = 2;
    topic_channel.GameLabel game_label = 3;
  }
}

message NewQuickMatchConfig {
  uint32 tab_id = 1;  // 玩法id, 每个玩法只有一个配置
  string tab_name = 2;
  string title = 3; // 标题
  string button_text = 4; // 按钮文案
  uint32 position = 5; // 展示位置, 快速匹配入口展示在列表第position个位置
}

message GetNewQuickMatchConfigReq {
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2;
}

message GetNewQuickMatchConfigResp {
  ga.BaseResp base_resp = 1;
  NewQuickMatchConfig config = 2;
}

message GetTopicChannelCfgInfoReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  uint32 channel_type = 3; // 房间类型,see enum channel_.proto ChannelType
}

message GetTopicChannelCfgInfoResp {
  ga.BaseResp base_resp = 1;
  uint32 channel_id = 2;
  uint32 tab_id = 3;                     // 主题房所选分类id
  string tab_name = 4;                   // 主题房的标签名
  bool is_in_ground = 5;                 //
  repeated uint32 playing_option = 6;      // 找陌生人玩&找好友玩，see enum topic_channel_.proto PlayingOption
  topic_channel.SwitchPlayInfo switch_play_info = 7;   // 切换玩法需要的玩法和玩法对应房间模式
  enum TabType {
    NORMAL = 0; //普通分类
    GAME = 1; //游戏分类
    MINI_GAME = 2; // 小游戏
  }
  TabType tab_type = 8;              // 主题房标签类型
  string welcome_text = 9;           // 欢迎语
  // 从房间内跳到指定外部第三方游戏相关配置(即下载游戏)
  topic_channel.ThirdPartyGame third_party_game = 10;
  string team_desc = 11;            //小队被移除但房间的小队信息要在房间内保留（[区服][模式]-[人数]，正在找：[想找分路1]、[想找分路2]，地图：[地图1]、[地图2]）
  bool show_team_desc = 12;         //是否该有小队信息，没有小队服务了只能自己判断了
  bool show_publish_button = 13;    //是否展示发布按钮
  uint32 category_type = 14;        //标识当前tab所属的分类类别，1 一起开黑, see enum topic_channel_.proto CategoryType
  uint32 mini_game_id = 15;         //小游戏id
  uint32 game_card_id = 16;         //游戏卡id
  uint32 tag_id = 17;               //旧字段客户端兼容
  UgcChannelPlayMode channel_play_mode = 18[deprecated = true]; //房间玩法模式（0语音，1文字）
  uint32 game_pal_entrance_status = 19;  // 房间搭子卡入口展示状态，详见GamePalEntranceStatus
  FastPcCfgTabInfo fast_pc_cfg_tab_info = 20; // 极速PC相关配置
  bool enable_truth_or_dare = 21; // 是否开启真心话大冒险
}

message FastPcCfgTabInfo{
  message FastPcPublishDesc {
    string title = 1; // block name
    string elem_name = 2; // block下的元素名拼装
  }
  repeated FastPcPublishDesc fast_pc_publish_desc = 1;
  // 新增PC极速版大厅房间背景图
  string room_background_img_url = 2;
  // 房间发布信息修改cd
  uint32 change_cool_down = 3;
  // 房间发布信息cd
  uint32 freeze_duration = 4;
  // 自动取消时长
  uint32 auto_dismiss_duration = 5;
}

// 房间搭子入口展示状态
enum GamePalEntranceStatus {
  GAME_PAL_ENTRANCE_STATUS_UNSPECIFIED = 0; // 无效参数
  GAME_PAL_ENTRANCE_STATUS_SHOW = 1;  // 展示
  GAME_PAL_ENTRANCE_STATUS_HIDE = 2;  // 隐藏
}

enum UgcChannelPlayMode {
  DEFAULT_VOICE_MODE = 0; //默认是语音房模式
  WORD_MODE = 1; //文字房模式
}

//设置房间玩法模式（语音，文字）
message SetUgcChannelPlayModeReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  UgcChannelPlayMode channel_play_mode = 3;//需要设置的房间模式
  uint32 uid = 4; //请求者uid
  uint32 tab_id = 5;//房间当前的玩法id
}

message SetUgcChannelPlayModeResp{
  ga.BaseResp base_resp = 1;
  string toast_title = 2 [deprecated = true]; //toast文案，不为空时需要toast提示
}

//文字房用户输入状态广播
message TypingStatusBroadcastReq{
  ga.BaseReq base_req = 1;
  uint32 uid = 2; //触发输入状态的用户
  uint32 cid = 3; //在房id
}

message TypingStatusBroadcastResp{
  ga.BaseResp base_resp = 1;
}

//文字房工具栏引导气泡文案
message GetChannelPlayModeGuideReq{
  ga.BaseReq base_req = 1;
  uint32 version = 2;//客户端本地引导文案版本号
  uint32 tab_id = 3;//当前房间玩法id
}

message GetChannelPlayModeGuideResp{
  ga.BaseResp base_resp = 1;
  uint32 version = 2; //当前最新版本号，若为0则代表配置的文案已过期，客户端不需要更新本地版本号
  string guide_words = 3; //引导文案
}

//输入状态推送
message TypeStatusPushMsg {
  repeated uint32 uid = 1; //打字中的uid
  uint32 cid = 2; //推送的房间id
}

//房间模式变化推送
message UgcChannelPlayModeChangeMsg {
  UgcChannelPlayMode channel_mode = 1; //房间玩法模式
  uint64 change_time = 2; //修改房间模式时间戳
}

//每日任务完成上报
enum DailyTaskType {
  INVALID_TASK_TYPE = 0;
  ENTER_GAME_ZONE_TASK = 1; //每日第一次进入游戏专区时上报
  CONFIG_TAB_VIEW_DATA = 2; // 用户在xxxx主题xxxxtab的停留的时长 和 对应元素的数量
}

//活动任务上报
message ReportDailyTaskReq{
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  DailyTaskType task_type = 3;
  uint32 tab_id = 4; // 主题玩法id
  string config_tab_id = 5; // xxxtab动态tab id
  uint32 stay_duration = 6; // 停留时长, 单位：秒
  uint32 view_type = 7; // 浏览数据类型， 1-开黑房，2-动态帖子， 3-搭子卡片
  uint32 view_count = 8; // 浏览元素数量
}

message ReportDailyTaskResp{
  ga.BaseResp base_resp = 1;
}


message GetCacheReq {
  ga.BaseReq base_req = 1;
}

message CacheData {
  repeated uint32 ids = 1;
}

message CacheBlockData {
  uint32 block_id = 1;
  repeated uint32 elem_ids = 2;
}

message CacheTabData {
  uint32 tab_id = 1;
  repeated CacheBlockData block_datas = 2;
}

message GetCacheResp {
  ga.BaseResp base_resp = 1;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  repeated uint32 categoryIds = 2;
  map<uint32, CacheData> category_tabs_map = 3;
  repeated uint32 tab_ids = 4;
  repeated uint32 min_game_tab_ids = 5;
  repeated CacheTabData tab_display_block_ids = 6;
  repeated CacheTabData tab_block_ids = 7;
  repeated CacheTabData tab_base_block_ids = 8;
  map<uint32, CacheData> game_card_tabs_map = 9;
}

// 专区入口检查，目前只有电竞有这个检查
// CMD_HomePageHeadConfigEnterCheck = 3105;   //首页专区是否能进入检查判断
message HomePageHeadConfigEnterCheckReq{
  ga.BaseReq base_req = 1;
  string config_id = 2;

}

message HomePageHeadConfigEnterCheckResp{
  ga.BaseResp base_resp = 1;
  bool is_can_enter = 2; // 是否可以进入专区
  string msg = 3;    // 不能进入的文案
}

message GetChannelListGuideConfigsReq{
  ga.BaseReq base_req = 1;
  message HomePageGuideReq{
    uint32 tab_id = 1;
    bool is_satisfy_expose_stay_condition = 2; // 是否满足用户今日连续2次曝光列表快速匹配，且至少有过一次通过快速进房后进房停留时长>=2分钟
    string play_black_ab_test = 3; // 开黑专区的ab实验结果
    string game_pal_ab_test = 4; // 游戏搭子ab实验结果
    uint32 quick_match_cfg_expose_count = 5; // 快速匹配引导曝光次数
  }

  oneof guide_req{
    HomePageGuideReq home_page_guide_req = 2;
  }
}

message GetChannelListGuideConfigsResp{
  ga.BaseResp base_resp = 1;
  message GuideConfig{
    oneof config {
      NewQuickMatchConfig new_quick_match_config = 1;
      PalCardGuideConfig pal_card_guide_config = 2;
      GameContentGuideConfig game_content_guide_config = 3;
    }
  }
  GuideConfig guide_config = 2;
}

message PalCardGuideConfig {
  repeated uint32 position = 1; // 展示位置, 专区搭子卡入口展示在列表那几个位置
  message GamePalCardGuideInfo {
    string card_id = 1; // 搭子卡id
    string account = 2; // 用户账号信息
    string content = 3; // 搭子卡宣言
    repeated string display_labels = 4; // 引导标签
  }
  repeated GamePalCardGuideInfo game_pal_items = 2; // 展示的搭子卡内容及跳转信息
  repeated ga.game_pal_logic.GamePalItem game_pal_item = 3[deprecated = true]; // 新版搭子卡内容，协议兼容问题废弃
  repeated ga.game_pal_logic.GamePalItem game_pal_list_info = 4; // 6.58.5新UI搭子卡内容，搭子比较全的数据
}

message GameContentGuideConfig {
  repeated uint32 position = 1; // 展示位置, 专区帖子入口展示在列表那几个位置
  message GameFeedGuideInfo {
    string post_id = 1; // 帖子id
    string content = 2; // 帖子内容
    repeated string img_urls = 3; //帖子图片,只展示图文类型的帖子
    string config_tab_id = 4; // 帖子动态tab id
    string account = 5; // 用户账号信息
    string nick_name = 6; // 用户昵称
    uint32 uid = 7; // 用户uid
  }
  repeated GameFeedGuideInfo feeds = 2; //展示的帖子内容及跳转信息
}

message GetTabInfosReq {
  ga.BaseReq base_req = 1;
  repeated uint32 tab_ids = 2;
}

message GetTabInfosResp {
  ga.BaseResp base_resp = 1;
  map<uint32, topic_channel.Tab> tab_infos = 2; // key: tab_id, value: Tab
}

// 推荐游戏配置信息
message GameTabConfig {
  uint32 tab_id = 1; // 玩法id
  string tab_name = 2; // 玩法名称
  string background_img = 3; // 背景图
  string icon_begin_color = 4; // 角标 渐变色值
  string icon_end_color = 5; // 角标 渐变色值
  string icon_text = 6; // 角标文案
  string show_team_text = 7; // xx人在玩文案
  repeated string accounts = 8; // 轮播用户账号
  repeated Button buttons = 9; // hover的按钮类型
  string mask_layer = 10; // tab图片遮罩颜色
  string cards_img_url = 11; // 游戏图标，跟后台命名一致
}

// 按钮跳转类型
enum ButtonType {
  BUTTON_TYPE_UNSPECIFIED = 0;
  BUTTON_TYPE_ENTER_ZONE = 1; // 跳转进专区
  BUTTON_TYPE_QUICK_MATCH = 2; // 跳转到快速匹配
}

message Button {
  uint32 button_type = 1; // 按钮跳转类型
  string button_text = 2; // 按钮文案
}

// 配置类型
enum CfgType {
  CFG_TYPE_UNSPECIFIED = 0;
  CFG_TYPE_GAME_TAB = 1; // 游戏玩法
}

message RecommendConfig {
  uint32 config_type = 1; // 配置类型
  GameTabConfig game_tab_item = 2; // 官方推荐配置内容
}

// 获取官方推荐游戏
message GetRecommendGamesReq {
  ga.BaseReq base_req = 1;
}

message GetRecommendGamesResp{
  ga.BaseResp base_resp = 1;
  repeated RecommendConfig configs = 2; // 官方推荐列表
}

// 更新标签缓存
message RefreshGameLabelReq {
  ga.BaseReq base_req = 1;
  uint32 tab_id = 2;
  repeated topic_channel.GameLabel labels = 3; // 标签筛选
  string filter_id = 4; // 音乐多品类玩法
}

message RefreshGameLabelResp {
  ga.BaseResp base_resp = 1;
  repeated topic_channel.GameLabel labels = 2; // 更新后的标签
}

message GetSupportTabListReq {
  ga.BaseReq base_req = 1;
}

message GetSupportTabListResp {
  ga.BaseResp base_resp = 1;
  repeated uint32 tab_ids = 2; // 支持的玩法列表
}

message GetChannelMicVolSetReq {
  ga.BaseReq base_req = 1;
  uint32 cid = 2;
}

message ChannelMicVolSetItem {
  uint32 mic_id = 1;
  uint32 max_vol = 3; // 最大音量
}

message GetChannelMicVolSetResp {
  ga.BaseResp base_resp = 1;
  repeated ChannelMicVolSetItem mic_vol_set = 2; // 麦位音量设置
  bool sync_zero_mic_set = 3; // 是否同步0号麦位设置
  uint32 cid = 4; // 房间id
}

message SetChannelMicVolReq {
  ga.BaseReq base_req = 1;
  repeated ChannelMicVolSetItem mic_vol_set = 2; // 麦位音量设置
  bool sync_zero_mic_set = 3; // 是否同步0号麦位设置
}

message SetChannelMicVolResp {
  ga.BaseResp base_resp = 1;
}

message ChannelMicVolSetChangePushMsg{
  repeated ChannelMicVolSetItem mic_vol_set = 1; // 麦位音量设置
  bool sync_zero_mic_set = 2; // 是否同步0号麦位设置
}

// 麦位用户扩展信息
message MicUserExtInfo {
  message AIAccount {
    // 标识
    string identity = 1;
    // 说明
    string desc = 2;
  }

  // ai账号信息
  AIAccount ai_account = 1;
}

enum TruthOrDareStatus {
  TRUTH_OR_DARE_STATUS_UNSPECIFIED = 0;
  // 打开
  TRUTH_OR_DARE_STATUS_OPEN = 1;
  // 关闭
  TRUTH_OR_DARE_STATUS_CLOSE = 2;
}

// 真心话大冒险问题内容
message TruthOrDareQuestion {
  // 问题内容
  string content = 2;
}

// 按分类聚合的真心话大冒险问题
message TruthOrDareCategoryQuestion {
  // 分类名称
  string categoy_name = 1;
  // 问题列表
  repeated TruthOrDareQuestion questions = 2;
}

message GetTruthOrDareQuestionListRequest {
  ga.BaseReq base_req = 1;
}
message GetTruthOrDareQuestionListResponse {
  ga.BaseResp base_resp = 1;

  repeated TruthOrDareCategoryQuestion list = 2;
}

message SetChannelTruthOrDareStatusRequest {
  ga.BaseReq base_req = 1;
      
  TruthOrDareStatus status = 2;
}
message SetChannelTruthOrDareStatusResponse {
  ga.BaseResp base_resp = 1;
}
