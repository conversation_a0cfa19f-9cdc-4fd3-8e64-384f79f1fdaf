syntax = "proto3";

package rcmd.rcmd_mt_proxy;

option go_package = "golang.52tt.com/protocol/services/rcmd/aigc_apps/rcmd_mt_proxy";



// ---------- cmd: AIAccountCommentPost ----------
message AIAccountCommentPostPlaceholder {
    string user_nickname = 1; // 用户昵称
    string user_sex = 2; // 用户性别
    uint32 user_age = 3; // 用户年龄
    string user_avatar = 4; // 用户头像 url
    string user_ip_location = 5; // 用户 IP 归属地
    repeated string user_hobby_tags = 6; // 用户兴趣卡片

    string ai_nickname = 7; // AI 昵称
    string ai_ip_location = 8; // AI IP 归属地
    string ai_signature = 9; // AI 签名
    string ai_birthday = 10; // AI 生日
    string ai_prologue = 11; // AI 开场白
    repeated string ai_photo_img_urls = 12; // AI 相册图片 url
    string ai_avatar = 13; // AI 头像 url
}
message AIAccountCommentPostReq {
    uint32 post_type = 1;  // 1：文本、2：图片、3：视频
    string post_content = 2;  // 贴文内容
    string post_tab = 3;  // 帖子所属哪个 tab
    string post_topic = 4;  // 帖子话题，选填
    repeated string post_tags = 5;  // 帖子 tags，选填
    repeated string image_urls = 6;  // 图片链接，可能多张（post_type 为 2 时必填）
    string video_url = 7; // 视频链接
    uint32 prompt_id = 8; // 泼墨体 ID
    string placeholder = 9; // 需要传给模型理解的额外参数，定义见：AIAccountCommentPostPlaceholder
}

message AIAccountCommentPostRsp {
    string comment_content = 1; // 评论内容
    string err_msg = 2; // 错误信息
}

// ---------- cmd: GetAIShoutOutUserContent ---------- AI 接待文生文
// 被接待的用户信息
message UserInfo {
    uint32 uid = 1;
    string nickname = 2;  // 用户昵称
}
// 发起接待的 AI 的信息
message AIInfo {
    string nickname = 1;  //
    string ip_location = 2;  // 
    string signature = 3;  // 
    string birthday = 4;  // 
    string ttid = 5;  //
    string prologue = 6;  //
    string phone = 7;  //
    string sex = 8;  //
    uint32 uid = 9;  //
}

// req data 内容
message GetAIShoutOutUserContentReq {
    uint32 channel_id = 1;  // 房间 id
    string query = 2;  // 请求 GPT 的文本
    AIInfo ai_info = 3;  // AI 信息
    UserInfo user_info = 4;  // 用户信息
    string chat_history = 5;  // 聊天记录
}

// resp data 内容
message GetAIShoutOutUserContentResp {
    string content = 1;  // 生成的接待内容
}