syntax = "proto3";

package aigc.aigc_voice_room;

option go_package = "golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_voice_room";

// 聊天服务定义
service AigcVoiceRoomService {
  rpc RecvAudioStreamInRoom(stream RecvAudioStreamInRoomReq) returns (stream RecvAudioStreamInRoomResp);
}

message RecvAudioStreamInRoomReq {
  uint32 channel_id = 1;  // 房间ID
  uint32 uid = 2;  // 用户ID
  bytes user_audio = 3;  // 音频流
  bool ping = 4;  // 心跳包
  map<string,string> extra = 5;  // 额外字段
  bool connection_create = 6; // 是否是连接创建的第一个包
}

message RecvAudioStreamInRoomResp {
  uint32 channel_id = 1;  // 房间ID
  uint32 ai_uid = 2;  // AI用户ID
  bytes tts_audio = 3;  // TTS音频流
  string pod_name = 4; // pod名称(环境变量获取)
  bool pong = 5;  // 心跳包响应
}
