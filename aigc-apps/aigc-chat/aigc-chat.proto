syntax = "proto3";

package aigc.aigc_chat;

option go_package = "golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_chat";

// 聊天服务定义
service AigcChatService {
  rpc AIAccountRecvUserMsg(AIAccountRecvUserMsgReq) returns (AIAccountRecvUserMsgResp);
  rpc SendAIAccountTrigger(AIAccountTriggerReq) returns (AIAccountTriggerResp);
  // 触发 AI 接待
  rpc AIShoutOutUserInRoom(AIShoutOutUserInRoomReq) returns (AIShoutOutUserInRoomResp);
}


// AI账号聊天消息请求
message AIAccountRecvUserMsgReq {
  uint32 uid = 1;
  uint32 ai_account_id = 2; // AI账号ID
  string content = 3; // 用户输入的消息内容
  uint32 msg_id = 4; // 消息ID
  uint32 msg_type = 5; // 消息类型，定义见：TT-Protocols/app/im/im.proto.IM_MSG_TYPE (1文本、63CUE)
  bytes extra = 6; // 扩展字段
  uint32 target_msg_id = 7; // 目标消息ID, 用于标记已读
  uint32 msg_source_type = 8; // 消息来源类型，定义见：TT-Protocols/app/im/im.proto.IM_MSG_SOURCE_TYPE
  bool is_exactly_reach_limit = 9; // 是否达到回复上限(刚好达到上限的那一次, 发结束语)
}

// 聊天消息响应
message AIAccountRecvUserMsgResp {
}

// AI账号主动触发消息请求
message AIAccountTriggerReq {
  uint32 uid = 1; //用户ID
  uint32 ai_account_id = 2; // AI账号ID
  string user_content = 3; // 用户输入的消息内容
  string ai_content = 4; // AI自动发消息内容，如果是空，走模型生成
  string ai_content_type = 5; // AI自动发消息类型，text/meme，文本/表情
}

// 主动触发消息响应
message AIAccountTriggerResp {
  uint32 code = 1; //返回码，0成功，1失败
  string msg = 2; //默认为空，失败时为失败原因
}

// 触发 AI 接待
message AIShoutOutUserInRoomReq {
    uint32 channel_id = 1;  // 房间 id
    uint32 ai_uid = 2;  // AI 账号 id
    string content = 3;  // 接待说的内容
}

// 触发 AI 接待响应
message AIShoutOutUserInRoomResp {
    
} 