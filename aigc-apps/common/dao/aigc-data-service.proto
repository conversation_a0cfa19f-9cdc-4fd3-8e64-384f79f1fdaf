syntax = "proto3";

package aigc.common.dao.aigc_data_service;

option go_package = "golang.52tt.com/protocol/services/rcmd/aigc_apps/common/dao/aigc_data_service";

service AIGCDataService {
  // 获取聊天历史记录
  rpc GetGroupChatHistory(GetGroupChatHistoryReq) returns (GetGroupChatHistoryResp);
  // 创建/更新 聊天历史记录
  rpc UpdateGroupChatHistory(UpdateGroupChatHistoryReq) returns (UpdateGroupChatHistoryResp);
  // 根据句子id移除聊天中的某几句历史记录
  rpc RemoveGroupChatHistory(RemoveGroupChatHistoryReq) returns (RemoveGroupChatHistoryResp);
  // 批量获取频道内AI的状态
  rpc BatchGetChannelState(BatchGetChannelStateReq) returns (BatchGetChannelStateResp);
  // 批量获取AI房间的Pod信息
  rpc BatchGetPodInfoWithChannel(BatchGetPodInfoWithChannelReq) returns (BatchGetPodInfoWithChannelResp);
  // 更新用户最后说话时间
  rpc UpdateUserLastSpeakTimes(UpdateUserLastSpeakTimesReq) returns (UpdateUserLastSpeakTimesResp);
  // 更新AI状态
  rpc UpdateAIState(UpdateAIStateReq) returns (UpdateAIStateResp);
  // 更新pod信息
  rpc UpdatePodInfo(UpdatePodInfoReq) returns (UpdatePodInfoResp);
}

message ChatHistory {
    enum Role {
        ROLE_DEFAULT = 0; // 默认
        USER = 1; // 用户
        ASSISTANT = 2; // AI
    }
    string sentence_id = 1; // 句子ID
    Role role = 2;
    uint32 uid = 3;
    ChatScene chat_scene = 4;
    string content = 5; // 句子内容
    int64 sort_value = 6; // 存进历史中，用来作为历史排序的值，不填默认使用当前时间戳，毫秒级
    map<string, string> extra = 7; // 透传字段
}

message GetGroupChatHistoryReq {
    string group_id = 1; // 群组ID，必填
    Source source = 2; // 来源，必填
    ChatScene chat_scene = 3; // 聊天场景，不填则查询所有scene的聊天记录
    int32 start_index = 4; // 起始下标（从0开始，包含），从最新的第几条开始
    int32 end_index = 5; // 结束下标（包含，-1为不限制），到最新的第几条结束
}

message GetGroupChatHistoryResp {
    repeated ChatHistory chat_history = 1;
    int32 code = 2;
    string msg = 3;
}

message UpdateGroupChatHistoryReq {
    string group_id = 1; // 群组ID
    Source source = 2; // 来源
    repeated ChatHistory chat_history = 3; // 聊天记录
}

message UpdateGroupChatHistoryResp {
    int32 code = 1;
    string msg = 2;
}

message RemoveGroupChatHistoryReq {
    string group_id = 1; // 群组ID
    Source source = 2; // 来源
    repeated string sentence_ids = 3; // 句子id
}

message RemoveGroupChatHistoryResp {
    int32 code = 1;
    string msg = 2;
}

// 查询时，填了ChatScene则根据ChatScene进行筛选
enum ChatScene {
    DEFAULT = 0; // 全部拉取
    ON_MIC_ASR = 1; // 麦上ASR
    SCREEN = 2; // 公屏
    NOTIFY = 3; // 通知
}

enum Source {
    INVALID = 0;
    AI_ROOM = 1;
}

enum QueryType {
    QUERY_TYPE_INVALID = 0;
    AI_STATE = 1;
    USER_LAST_SPEAK_TIME = 2;
}
message AIUids {
    repeated uint32 ids = 1;
}
message BatchGetChannelStateReq {
    repeated uint32 channel_ids = 1;  // 房间 ids
    repeated QueryType query_types = 2;  // 要查询的类别
}
enum AIState {
    AVAILABLE = 0; // 空闲
    HANDLING = 1; // 忙碌
}
message AIStates {
    map<uint32, AIState> aiuid_2_state = 1;  // key: ai_uid, value: AI 当前的状态
}
message UserLastSpeakTimes {
    map<uint32, int64> uid_2_ts = 1;  // key: uid, value: 用户最后说话的时间
}
message BatchGetChannelStateResp {
    map<uint32, AIStates> channel_2_ai_states = 1;  // key: 房间 id，value：各个 AI 的状态
    map<uint32, UserLastSpeakTimes> channel_2_user_times = 2;  // key: 房间 id，value：各个用户最后识别到有效音频的时间戳（ms）
}

// 更新AI状态
message UpdateAIStateReq {
    enum Operation {
        UPDATE = 0;  // 更新
        DELETE = 1;  // 删除
    }
    Operation operation = 1;  // 操作类型
    uint32 ai_uid = 2;  // AI UID
    uint32 channel_id = 3;  // 房间 ID
    AIState ai_state = 4;  // AI 状态
}
message UpdateAIStateResp {
    int32 code = 1;
    string msg = 2;
}

// 更新用户最后说话时间
message UpdateUserLastSpeakTimesReq {
    enum Operation {
        UPDATE = 0;  // 更新
        DELETE = 1;  // 删除
    }
    Operation operation = 1;  // 操作类型
    uint32 uid = 2;  // 用户 UID
    uint32 channel_id = 3;  // 房间 ID
    int64 time_stamp = 4;  // 时间戳（ms）
}

message UpdateUserLastSpeakTimesResp {
    int32 code = 1;
    string msg = 2;
}

message PodInfo {
    string pod_name = 1;  // pod 名称
    string pod_ip = 2;  // pod ip
}
message BatchGetPodInfoWithChannelReq {
    repeated uint32 channel_ids = 1;  // 要查询的 channel_id 数组
}
message BatchGetPodInfoWithChannelResp {
    map<uint32, PodInfo> channel_id_2_pod_info = 1;
}

message UpdatePodInfoReq {
    enum Operation {
        UPDATE = 0;  // 更新
        DELETE = 1;  // 删除
    }
    Operation operation = 1;  // 操作类型
    uint32 channel_id = 2;  // 房间 ID
    PodInfo pod_info = 3;  // Pod 信息
}
message UpdatePodInfoResp {
    int32 code = 1;
    string msg = 2;
}
