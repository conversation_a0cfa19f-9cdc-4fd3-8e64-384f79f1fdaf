syntax = "proto3";

package ga.present_go_logic;

import "ga_base.proto";
import "userpresent/userpresent_.proto";
import "sync/sync.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/present-go-logic";

message AchievementArea {
  string title = 1;        // 成就标题
  string jump_url = 2;     // 跳转url
  string pic_url = 3;      // 图片url
  uint32 status = 4;       // 状态 0-未点亮 1-点亮
}

message ActPresentDetail {
  uint32 item_id = 1;      // 礼物id
  uint32 num = 2;          // 数量
}

message ActPresentArea {
  string title = 1;                       // 成就标题
  string jump_url = 2;                    // 跳转url
  repeated ActPresentDetail detail = 3;   // 活动礼物详情
}

message ActFrameConf {
  string title = 1;
  string resources_url = 2;   // 资源url(图片)
  //uint32 resources_type = 3;  // 资源类型 0-图片 1-视频
  //string resources_md5 = 4;   // 资源md5值
}

message ActRemindConf {
  uint32 begin_ts = 1;    // 开始时间
  uint32 end_ts = 2;      // 结束时间
  string remind_text = 3; // 提示文案
  uint32 duration = 4;    // 展示时长
}

message GetUserActPresentAreaReq {
  BaseReq base_req = 1;
  uint32 uid = 2;
}

message GetUserActPresentAreaResp {
  BaseResp base_resp = 1;
  ActFrameConf frame = 3;           // 边框
  ActPresentArea present_area = 4;  // 活动礼物
  AchievementArea achievement = 5;  // 活动成就
  ActRemindConf remind = 6;         // 活动提醒
  bool area_switch = 7;             // 区域开关 0-关 1-开
}

// 获取送礼需要弹窗的礼物列表
message GetNeedPopUpPresentListReq {
  BaseReq base_req = 1;
}
message GetNeedPopUpPresentListResp {
  BaseResp base_resp = 1;
  repeated uint32 id_list = 2;
}

// 获取礼物的额外配置
message GetPresentExtraConfigReq {
  BaseReq base_req = 1;
}

message GetPresentExtraConfigResp {
  BaseResp base_resp = 1;
  repeated PresentFloatLayer layer_infos = 2;
  repeated PresentFlashEffect flash_effects = 3;
  repeated FlashEffectConfig flash_effect_configs = 4;
  uint32 last_update_time = 5; // 额外信息(浮层/闪光共用)的最后更新时间
  repeated PresentEffectTime present_effect_time_infos = 6; // 延迟下架的礼物信息
  repeated PrivilegePresentInfo privilege_present_info = 7;   //  权限礼物相关信息
  
}

message PrivilegePresentInfo {
  uint32 gift_id = 1;
  uint32 end_time = 2; // 权限到期时间
}

message PresentFloatLayer {
  uint32 gift_id = 1;
  string float_image_url = 2; // 浮层图片地址
  string jump_url = 3; // 跳转链接
  bool is_activity_url = 4; // 是否是活动链接
  repeated uint32 show_channel_type = 5; // 需要展示的房间类型
  bool is_show_ugc_locked = 6; // 是否在UGC锁房时展示
}

message PresentFlashEffect {
  uint32 gift_id = 1;
  uint32 flash_id = 2; // 光效的id
}

message FlashEffectConfig {
  uint32 flash_id = 1;
  string flash_name = 2; // 特效名
  string flash_url = 3; // 资源包地址
  string flash_md5 = 4; // 资源包md5
}

message PresentEffectTimePush {
  uint32 gift_id = 1;  // 礼物id
  uint32 effect_end = 2; // 新的下架时间, 0代表无限期
  PresentEffectTimeInfo effect_info = 3; // 限时礼物延期的相关信息
  bool is_new_level = 4; // 是否为新等级（新等级需要弹窗）
  uint32 new_level_day_count = 5; // 新等级的延长天数
}


message PresentEffectTime {
  uint32 gift_id = 1;  // 礼物id
  uint32 effect_end = 2; // 新的下架时间, 0代表无限期
  PresentEffectTimeInfo effect_info = 3; // 限时礼物延期的相关信息
}

message PresentEffectTimeInfo{
  uint32 now_count = 1;  // 目前已送个数
  uint32 next_level_send_count = 2; // 下个等级所需赠送个数
  uint32 next_level_day_count = 3; // 下个等级延长天数，0为无限期
  uint32 max_level_send_count = 4;  // 最高级所需赠送个数
  bool   is_max_level = 5;  // 是否已经达到最高级
  uint32 no_limit_expire_day_count = 6; // 无限期礼物多久不送会消失
  uint32 last_send_ts = 7; // 最后一次送该礼物的时间戳
  uint32 max_level_day_count = 8;  // 最高级延长天数，0为无限期
  uint32 effect_end_on_shelf = 9;  // 礼物架上展示的下架时间
  uint32 now_level_day_count = 10; // 当前等级的延长天数
  bool notice_no_limit_expire = 11; // 是否提醒用户无限礼物即将过期
}

message PresentEffectTimeLevelInfo{
  uint32 level = 1;  // 级别
  uint32 level_send_count = 2; // 需要送多少个达到该等级
  uint32 level_day_count = 3; // 达到该等级延长多久下架时长; 0代表无限期
}

message GetPresentEffectTimeDetailReq{
  BaseReq base_req = 1;
  uint32 gift_id = 2;  // 礼物id
}

message GetPresentEffectTimeDetailResp{
  BaseResp base_resp = 1;
  uint32 gift_id = 2;  // 礼物id
  uint32 now_count = 3; // 目前已送个数
  repeated PresentEffectTimeLevelInfo level_info = 4; // 等级信息
  uint32 no_limit_expire_day_count = 5;  // 如果最高级是无限的，多久不送会过期
}

message GetCustomizedPresentListReq{
  BaseReq base_req = 1;
}

message GetCustomizedPresentListResp{
  BaseResp base_resp = 1;
  repeated CustomizedPresentInfo present_info = 2; // 定制礼物相关信息
  uint32 last_update_ts = 3; // 配置最后一次更新的时间
  repeated PresentEffectAppend present_effect_append = 4; // 权限发放
}

// 自定义礼物的信息，部分礼物本身的配置信息可以直接从礼物缓存中获取
message CustomizedPresentInfo{
  uint32 id = 1;  // 定制礼物id，也是其主礼物的礼物id
  repeated string custom_option = 2; // 当前使用的组件名
  uint32 present_id = 3; // 目前组件对应的子礼物id
  string cms_url = 4; // 定制礼物的cms链接地址
  bool has_authority = 5; // 用户是否有此定制礼物权限
  bool has_new_custom = 6; // 用户是否有新的样式可用（显示红点）
  uint32 effect_begin = 7; // 上架时间，因为有权限因素，定制礼物的上下架需要用单独的时间控制
  uint32 effect_end = 8; // 下架时间，因为有权限因素，定制礼物的上下架需要单独的时间控制
}


message GetCustomizedPresentDetailReq{
  BaseReq base_req = 1;
  uint32 id = 2; // 定制礼物id
}

message GetCustomizedPresentDetailResp{
  BaseResp base_resp = 1;
  CustomizedPresentDetail present_detail = 2; // 定制礼物详情
}

message CustomizedPresentDetail{
  uint32 id = 1;  // 定制礼物id，也是其主礼物的礼物id
  repeated CustomOption custom_option = 2; // 可用的组件
  //uint32 present_id = 3; // 子礼物id
  // 另一个做法，客户端本地计算，好处延迟低，坏处是逻辑不方便更改：
  map<string, uint32> custom_method = 3;
  //组件选择与子礼物id的对应公式, key: 类似 1_2_3 的字符串，下划线分割，代表组件id1、2、3分别选择样式1、2、3; value: 对应的子礼物id
  uint32 user_level = 4; // 自己拥有的权限等级
  string level_text = 5; // 专属礼物的等级说明
  string colorful_text = 6; // 用于匹配level_text，将匹配到的部分变色
  map<uint32, CustomPresentPreview> preview_map = 7;  // 预览图，key：礼物id value: 预览的类型和url
}

message CustomPresentPreview{
  uint32 preview_type = 1;  // see CustomPreviewType
  string preview_url = 2;
  string preview_md5 = 3;
}

enum CustomPreviewType {
  CustomPreviewImg = 0;  // 图片
  CustomPreviewMp4 = 1;  // 视频
}


message CustomOption {
  uint32 custom_id = 1;  // 部件id
  string custom_name = 2;  // 部件名
  repeated OptionInfo option_info = 3; // 可选样式
  string custom_text = 4;  // 部件说明
}

message OptionInfo {
  uint32 option_id = 1;  // 样式id
  string option_name = 2;  // 样式名
  uint32 option_level = 3; // 样式所需的权限等级
  bool is_new = 4;  // 是否是新的样式（显示红点）
  bool is_active = 5; // 是否选中
}

message ReportCustomOptionChooseReq{
  BaseReq base_req = 1;
  uint32 id = 2;  // 定制礼物的id
  repeated CustomPair custom_pair = 3; // 目前的部件样式选择
}

message ReportCustomOptionChooseResp{
  BaseResp base_resp = 1;
}

message CustomPair{
  uint32 custom_id = 1;  // 部件id
  uint32 option_id = 2;  // 样式id
}

//message CustomizedPresentChange{
//  CustomizedPresentInfo present_info = 1;  // 礼物架上的定制礼物信息
//  CustomizedPresentDetail present_detail = 2; // 定制礼物详情
//}

// 礼物权限发放
message PresentEffectAppend{
  uint32 gift_id = 1;
  uint32 effect_begin = 2;
  uint32 effect_end = 3;
}

message CommonSendPresentReq
{
  BaseReq base_req = 1;
  uint32 item_id = 2;
  uint32 channel_id = 3;  // 通过房间赠送礼物时才需要填
  uint32 count = 4;  // 礼物数量
  uint32 send_source = 5;  // 赠送时的点击来源 ga.userpresent.PresentSendSourceType 0.默认类型(兼容旧版本) 1.送礼转盘 2.礼物架（点击头像或成员列表） 3.语音球
  uint32 item_source = 6;  // 礼物来源 PresentSourceType
  uint32 source_id = 7;    // 如果是背包里的礼物，就是背包礼物对应的唯一id
  uint32 send_type = 8;     // 送礼类型 PresentSendType
  ga.userpresent.DrawPresentPicture draw_present_pic = 9;       // 涂鸦礼物图
  repeated uint32 uid_list = 10;       // 送礼id列表
  uint32 business_type = 11; // 如果是背包礼物，业务类型 userpresent_.proto PresentBusinessType
}

message CommonSendPresentResp
{
  BaseResp base_resp = 1;
  ga.userpresent.PresentBatchInfoMsg batch_msg_info = 2;  // ga.userpresent.PresentSendMsg 和 ga.userpresent.PresentBatchInfoMsg 只会有一个
  ga.userpresent.PresentSendMsg msg_info = 3;
  uint64 cur_tbeans = 4; // 当前T豆余额
  uint32 item_source = 5;  // 礼物来源 PresentSourceType
  uint32 source_id = 6;    // 如果是背包里的礼物，就是背包礼物对应的唯一id
  uint32 source_remain = 7;    // 如果是背包里的礼物，就是背包礼物的剩余数量
  repeated ga.userpresent.PresentTargetUserInfo target_list = 8;  // 收礼对象列表
  ga.userpresent.PresentSendItemInfo item_info = 9;    // 礼物信息
  uint32 expire_time = 10;  // 根据 item_source 来决定取值。如果是背包物品，就是背包物品的更新后的过期时间
  ga.userpresent.PresentBoxDetail box_detail = 11; // 开盒信息，如果是前置特效的全服礼物会用到
}

message TreasurePrivilegeChannelMsg
{
  UserProfile user = 1;   // 用户信息
  string gift_name = 2;  // 礼物名
  string gift_price = 3;  // 礼物价值
  string button_text = 4; // 按钮文案
  string jump_url = 5;    // 跳转短链
}

message PresentConfigSyncReq
{
  BaseReq base_req = 1;
  uint32 last_update_time = 2;
  uint32 flow_update_time = 3;
  uint32 dynamic_update_time = 4;
  bool only_present = 5; // 是否只拉取礼物配置 (不影响帝王套，只影响礼物、流光、动效配置)
  uint32 emperor_set_update_time = 6; // 帝王套配置更新时间
  bool with_emperor_set = 7; // 是否拉取帝王套配置
}

message PresentConfigSyncResp
{
  BaseResp base_resp = 1;
  uint32 last_update_time = 2;
  repeated PresentItemConfig config_list = 3;
  repeated PresentEnterBlacklist enter_black_list = 4;
  ga.sync.AdvancedConfigPresentFlowSync flow_sync = 5; // 高级配置-礼物流光-全量
  ga.sync.AdvancedConfigPresentDynamicTemplateSync dynamic_template_sync = 6; // 高级配置-动态模板-全量
  EmperorSetConfigSync emperor_set_config = 7;  // 帝王套配置
}

enum PresentEnterType
{
  PresentEnterTypeUnknown = 0;
  PresentEnterTypeShelf = 1; // 礼物架 （挚友/贵族等礼物都包含在这部分）
  PresentEnterTypeLottery = 2; // 抽奖
  PresentEnterTypeWishList = 3; // 心愿单
}

message PresentEnterBlacklist
{
  PresentEnterType enter_type = 1;
  repeated uint32 gift_item_list = 2;
}

message GetPresentSetInfoReq{
  BaseReq base_req = 1;
}

message GetPresentSetInfoResp{
  BaseResp base_resp = 1;
  repeated PresentSetInfo set_list = 2;
  uint32 last_update_time = 3; // 礼物套组配置最后更新时间
}

message GetPresentSetDetailReq{
  BaseReq base_req = 1;
  uint32 set_id = 2;
}

message GetPresentSetDetailResp{
  BaseResp base_resp = 1;
  PresentSetDetail set_detail = 2;
}

message PresentSetInfo{
  uint32 set_id = 1;
  repeated PresentSetItem set_item_list = 2;
  uint32 default_item_id = 3; // 默认选中的礼物id
  uint32 begin_time = 4; // 开始时间
  uint32 end_time = 5; // 结束时间
  bool is_permanent = 6; // 是否长期活动
  float rank = 7; // 排序
  bool is_batch = 8; // 是否能够批量
  bool is_new = 9; // 是否有新获得
}

message PresentSetItem{
  uint32 item_id = 1;   // 对应礼物配置的礼物id
  PresentSetItemRareLevel rare_level = 2; // 稀有度
  bool is_unlocked = 3;  // 是否解锁
  string extend_text = 4;  // 扩展文案
  bool is_new = 5; // 是否是新获得
  string background_url = 6; // 背景图(与稀有度绑定)
}

// 礼物套组礼物的稀有度等级
enum PresentSetItemRareLevel{
  PresentSetItemRareLevelUnknown = 0;
  PresentSetItemRareLevelRare = 1; // R
  PresentSetItemRareLevelSuperRare = 2; // SR
  PresentSetItemRareLevelSuperiorSuperRare = 3; // SSR
}

message PresentSetDetail{
  uint32 set_id = 1;
  string set_name = 2;
  uint32 begin_time = 3; // 开始时间
  uint32 end_time = 4; // 结束时间
  bool is_permanent = 5; // 是否长期活动
  repeated PresentSetUserBroadcast user_broadcast = 6; // 中奖用户播报
  PresentSetCollectionAward collection_award = 7; // 收集奖励
  uint32 collect_count = 8; // 收集数量
  uint32 total_count = 9; // 总数量
  repeated PresentSetItem set_item_list = 10; // 礼物列表
  string activity_url = 11; // 活动链接
  string activity_icon = 12; // 活动图标
  string cms_url = 13; // cms规则页的链接
  uint32 default_item_id = 14; // 默认选中的礼物id
  PresentSetDetailResource source = 15; // 资源
  PresentSetActivityUrlType activity_url_type = 16; // 活动链接类型
  uint32 emperor_set_id = 17; // 帝王套id
  string send_all_emperor_set_text = 18; // 按照帝王套赠送全部礼物的文案
  uint32 send_all_emperor_set_price = 19; // 按照帝王套赠送全部礼物的价格
}

message PresentSetDetailResource{
  string background_url = 1; // 背景图
  string frame_url = 2 ; // 常规状态下的边框
  string no_award_frame_url = 3; // 无奖励状态下的边框
  string award_preview_icon_url = 4; // 奖励预览图标
  string introduction_icon_url = 5; // 介绍图标
  string award_background_url = 6; // 奖励背景图
  string award_frame_shadow = 7; // 奖励超过一页时，奖励框的阴影
  string background_url_nine = 8;  // 背景图.9
  string frame_url_nine = 9; // 常规状态下的边框.9
  string send_emperor_set_frame_url = 10; // 按照帝王套赠送全部礼物的边框
}

enum PresentSetActivityUrlType{
  PresentSetActivityTypeUnknown = 0;
  PresentSetActivityTypeFullScreen = 1; // 全屏
  PresentSetActivityTypeHalfScreen = 2; // 半屏
}

message PresentSetUserBroadcast{
  uint32 uid = 1;
  string account = 2;
  string nickname = 3;
  string head_md5 = 4;
  uint32 item_id = 5;
}

message  PresentSetCollectionAward{
  string award_text = 1;  // 集齐奖励文案
  repeated PresentSetCollectionAwardItem award_list = 2; // 奖励列表
  PresentPreviewType preview_type = 3; // 预览类型
  string preview_url = 4; // 预览url
  string preview_md5 = 5; // 预览md5
}

message PresentSetCollectionAwardItem{
  string icon_url = 1;  // 奖励图标
  PresentSetCollectionAwardType award_type = 2; // 奖励类型
  string award_name = 3;  // 奖励名称
  string award_desc = 4;  // 奖励说明（奖励类型文案 or 价值）
  string mark_text = 5; // 标记文案
}

// 奖励类型枚举， 包括包裹，大V认证、个人铭牌、麦位框、坐骑、主页飘、房间资料卡
enum PresentSetCollectionAwardType{
  PresentSetCollectionAwardTypeUnknown = 0;
  PresentSetCollectionAwardTypePackage = 1; // 包裹
  PresentSetCollectionAwardTypeOfficialCert = 2; // 大V认证
  PresentSetCollectionAwardTypeNamePlate = 3; // 个人铭牌
  PresentSetCollectionAwardTypeHeadWear = 4; // 麦位框
  PresentSetCollectionAwardTypeChannelPersonalization = 5; // 坐骑
  PresentSetCollectionAwardTypeFloat = 6; // 主页飘
  PresentSetCollectionAwardTypeChannelInfoCard = 7; // 房间资料卡
}

// 预览类型枚举， 无/链接/视频
enum PresentPreviewType{
  PresentPreviewTypeNone = 0; // 无预览
  PresentPreviewTypeUrl = 1; // Url
  PresentPreviewTypeVideo = 2; // 视频
}

message PresentSetUnlockMsg{
  repeated PresentSetUnlockItem unlock_list = 1; // 解锁的物品列表
  uint32 collect_count = 2; // 收集数量
  uint32 total_count = 3; // 总数量
  bool is_all = 4; // 是否全收集
  repeated PresentSetUnlockItem all_unlock_list = 5; // 如果全解锁，会填这个，包括套组内所有的礼物
  repeated PresentSetUserBroadcast broad_cast_list = 6; // 最新的播报信息，一定会包括自己
  uint32 set_id = 7; // 套组id
}


message PresentSetUnlockItem{
  uint32 set_id = 1;
  uint32 item_id = 2;
  PresentSetItemRareLevel rare_level = 3; // 稀有度
  string unlock_text = 4;  // 解锁文案
  string extend_text = 5;  // 替换套组中礼物的扩展文案
  string background_url = 6; // 背景图(与稀有度绑定)
  string get_all_background_url = 7; // 全收集弹窗里显示的背景图
}


message EmperorSetSendReq
{
  BaseReq base_req = 1;
  uint32 emperor_set_id = 2;
  uint32 channel_id = 3;    // 通过房间赠送礼物时才需要填
  uint32 send_source = 4;   // 赠送时的点击来源 ga.userpresent.PresentSendSourceType 0.默认类型(兼容旧版本) 1.送礼转盘 2.礼物架（点击头像或成员列表） 3.语音球
  uint32 send_type = 5;     // 送礼类型 PresentSendType
  uint32 to_uid = 6;        // 收礼的uid
}

message EmperorSetSendResp
{
  BaseResp base_resp = 1;
  EmperorSetPresentMsg msg_info = 2;
  uint64 cur_tbeans = 3; // 当前T豆余额
  uint32 item_source = 4;  // 礼物来源 PresentSourceType
  ga.userpresent.PresentTargetUserInfo target_info = 5;  // 收礼对象
  EmperorBoxDetail box_detail = 6; // 开盒信息，如果是前置特效的全服礼物会用到
}

message EmperorSetConfigSync{
    repeated EmperorSetConfig emperor_set_config = 1;
    uint32 last_update_time = 2;
}

message EmperorSetConfig {
  uint32 emperor_set_id = 1;
  string emperor_set_name = 2;
  string emperor_set_icon = 3;
  uint32 effect_begin = 4;
  uint32 effect_end = 5;
  bool is_show = 6; // 是否展示在礼物架上
  bool show_effect_end = 7; // 展示特效结束时间
  float rank = 8; // 排序
  EmperorSetJumpType float_jump_type = 9; // 帝王套特效地址
  enum EmperorSetJumpType{
    EmperorSetJumpTypeNone = 0; // 无跳转
    EmperorSetJumpTypeUrl = 1; // 跳转链接
    EmperorSetJumpTypeActivity = 2; // 活动链接
  }
  string float_image_url = 10; // 浮层图片地址
  string float_jump_url = 11; // 浮层跳转链接
  uint32 total_price = 12; // 价格
  string emperor_effect_url = 13; // 帝王套特效地址
  string emperor_effect_md5 = 14; // 帝王套特效md5
  repeated uint32 present_id_list = 15; // 礼物id列表
  uint32 update_time = 16;
  bool is_delete = 17; // 是否删除
  bool is_box_breaking = 18; // 是否是开盒特效

  string emperor_viewing_source_url = 19; // 帝王套观景台资源地址
  string emperor_viewing_source_md5 = 20; // 帝王套观景台资源MD5
  string emperor_flow_source_url = 21; // 帝王套流光资源地址
  string emperor_flow_source_md5 = 22; // 帝王套流光资源MD5
}


message EmperorSetPresentInfo{
  repeated ga.userpresent.PresentSendItemInfo present_list = 1;
  uint32 emperor_set_id = 2;
  string emperor_effect_json = 3; // 帝王套前置特效对应的json
  string viewing_effect_json = 4; // 帝王套观景台对应的json
}

message EmperorSetPresentMsg{
  repeated ga.userpresent.PresentSendItemInfo present_list = 1;
  uint64 send_time = 2;
  uint32 channel_id = 3;
  uint32 send_uid = 4;
  string send_account = 5;
  string send_nickname = 6;
  uint32 target_uid = 7;
  string target_account = 8;
  string target_nickname = 9;
  string extend_json = 10;
  UserProfile from_user_profile = 11;
  UserProfile to_user_profile = 12;
  uint32 emperor_set_id = 13;
  string emperor_effect_json = 14; // 帝王套前置特效对应的json
  bool is_box_breaking = 15; // 是否是开盒
  uint64 delay_time = 16;  // 开盒推迟的时间
  string viewing_effect_json = 17; // 帝王套观景台对应的json
}

message GetEmperorSetConfigByIdReq
{
  BaseReq base_req = 1;
  uint32 emperor_set_id = 2;
}

message GetEmperorSetConfigByIdResp
{
  BaseResp base_resp = 1;
  EmperorSetConfig config_list = 2;
}

// 展示帝王套礼物盒子会用到的信息
message EmperorBoxInfo{
  EmperorSetPresentMsg item_msg = 1;  // 原本的礼物结构
  EmperorBoxDetail box_detail = 2;  // 开盒相关信息
}

// 帝王套的开盒信息，为了兼容单独定义
message EmperorBoxDetail{
  string box_id = 1; // 与礼物的共用一套box_id
  UserProfile from_user_profile = 2;
  UserProfile to_user_profile = 3;
  uint32 emperor_set_id = 4;
  string emperor_set_name = 5;
  uint64 send_time = 6;
  string extend_json = 7; // 客户端用来构造特效的通用json
  uint64 delay_time = 8;  // 开盒推迟的时间
  bool   is_visible_to_sender = 9; // 是否送礼者可见（抽奖 - true, 普通送礼 - false）
}


// 全服礼物前置特效 - 开盒
message UnpackEmperorBoxReq{
  BaseReq base_req = 1;
  string box_id = 2;
  uint32 channel_id = 3;
}

message UnpackEmperorBoxResp{
  BaseResp base_resp = 1;
  EmperorBoxInfo box_info = 2;
}


// 展示礼物盒会用到的信息
message EmperorBoxOpenMsg{
  string box_id = 1;
}

message GetGiftReorderInfoReq{
  BaseReq base_req = 1;
  uint32 channel_id = 2;
  uint32 channel_type = 3; // 用来区分ugc/pgc房，see enum ChannelType
}

message GetGiftReorderInfoResp{
  enum GiftReorderItemType{
    GIFT_REORDER_ITEM_TYPE_UNSPECIFIED = 0;
    GIFT_REORDER_ITEM_TYPE_COMMON_GIFT = 1; // 普通礼物
    GIFT_REORDER_ITEM_TYPE_EMPEROR_SET = 2; // 帝王套
    GIFT_REORDER_ITEM_TYPE_LUCKY_GIFT = 3; // 幸运礼物
  }
  message GiftReorderInfo{
    string id = 1; // 物品id，需要自行从string转换为对应类型
    GiftReorderItemType type = 2; // 物品类型，本期虽然仅有普通礼物，但还是预留其它类型如帝王套
    uint32 pos = 3; // 强插位置，仅强插礼物用到，第1位传1而不是0
  }
  BaseResp base_resp = 1;
  repeated GiftReorderInfo freq_use_gift_list = 2; // 常用礼物列表
  uint32 freq_use_gift_show_num = 3; // 常用礼物展示个数
  uint32 freq_use_gift_start_pos = 4; // 常用礼物开始展示的位置，第1位传1而不是0
  repeated GiftReorderInfo recommend_gift_list = 5; // 推荐礼物列表
  repeated GiftReorderInfo force_insert_gift_list = 6; // 强插礼物列表
  string user_attribute = 7; // 用户属性，是一个json，用于埋点上报
}
