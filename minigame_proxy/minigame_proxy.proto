syntax = "proto3";

package ga.minigame_proxy;

import "ga_base.proto";
option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/minigame-proxy";

message SubmitGameCmdRequest {
  ga.BaseReq base_req = 1;
  string cmd_data = 2; //游戏的json格式cmd，{cmd:"UserGameStatusReq", param:{}},param根据不同cmd定义不同
}

message SubmitGameCmdResponse {
  ga.BaseResp base_resp = 1;
  string cmd_data = 2; //游戏的json格式cmd，{cmd:"UserGameStatusResp", data:{}}，data根据不同cmd定义不同
}
