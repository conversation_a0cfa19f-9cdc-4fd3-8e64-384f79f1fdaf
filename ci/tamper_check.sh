#!/bin/bash

run() {
    echo "+ $*"
    "$@"
}

echo "current branch: $1"

CI_COMMIT_BRANCH=$1

echo "git fetch origin $CI_COMMIT_BRANCH"
git fetch origin $CI_COMMIT_BRANCH > /dev/null
echo "git fetch origin develop"
git fetch origin develop > /dev/null

# 获取当前分支和 develop 分支的共同祖先提交
CHANGED_FILES=$(git diff --diff-filter=d --name-only $(git merge-base origin/$CI_COMMIT_BRANCH origin/develop) | grep '\.proto$')

[ -z "$CHANGED_FILES" ] && { echo ">> ✅ 无协议改动"; exit 0; }

CHANGED_DIRS=$(echo "$CHANGED_FILES" | xargs -n1 dirname | sort -u)

FAILED=false
for file in $CHANGED_FILES; do
    OUTPUT=$(run /home/<USER>/buf lint --config tt-proto-lint.yaml --path "$file") 2>&1
    echo "$OUTPUT"

    MESSAGE=$(echo "$OUTPUT" | grep "(tt-proto-lint)")
    if [ "$MESSAGE" ];
    then
      FAILED=true
    fi
done

if [ "$FAILED" == "true" ]
then
    echo ">> ❌ tt-proto-lint 检测不通过，请联系 leader 进行审核"
    exit 1
fi