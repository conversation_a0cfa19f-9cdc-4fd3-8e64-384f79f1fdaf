syntax = "proto3";

/***************虚拟二期形象logic*****************/

package ga.virtual_image_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/virtual_image_logic";

// =========== 虚拟形象二期 商品管理相关 ===========

// 推荐是否也放品类
enum VirtualImageResourceCategoryType {
  VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_UNSPECIFIED = 0;
  VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_RECOMMEND = 1;   // 推荐
  VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_SUIT = 2;        // 套装
  VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_FACE = 3;         // 脸部
  VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_OTHER = 100;       // 其他
}

enum VirtualImageResourceSubCategoryType {
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_UNSPECIFIED = 0 ;
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_WRAP = 1;         // 环身
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_SIT= 2;     // 坐姿
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_SEAT = 3;     // 座椅
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_BACK = 4;     // 背景
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_EFFECTS = 5;     // 进房特效
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_OTHER = 100;       // 其他
}

// 一级品类信息
message VirtualImageParentCategoryInfo {
  uint32 category = 1;          // 品类
  string category_name = 2;     // 品类名称
  uint32  category_type = 3 ;   // VirtualImageResourceSubCategoryType
}
// 二级品类信息
message  VirtualImageSubCategoryInfo {
  uint32 sub_category = 1;          // 品类
  string sub_category_name = 2;     // 子品类名称
  string sub_category_img_url = 3;  // 子品类图片 未选中情况
  uint32  category_type = 4 ;   // VirtualImageResourceSubCategoryType
  string sub_category_img_preview_url = 5;  // 子品类图片预览 选中情况
}
// 资源品类信息
message VirtualImageResourceCategoryInfo {
  VirtualImageParentCategoryInfo parent_category_info = 1; // 父品类信息
  repeated VirtualImageSubCategoryInfo sub_category_info_list = 2; // 子品类信息
}

// 获取默认形象资源列表
message GetDefaultResourceListRequest {
  ga.BaseReq base_req = 1;
}

message MutualExclusion {
  repeated uint32 category = 1;
}

 message GetDefaultResourceListResponse {
  ga.BaseResp base_resp = 1;
  repeated uint32 male_resources = 2; // 男性默认资源列表
  repeated uint32 female_resources = 3; // 女性资源列表
  repeated uint32 out_fit = 4;   // 全身装替换默认男女上下装id
  map<uint32, uint32>  male_animation_map = 5; // category/sub_category -> resource_id
  map<uint32, uint32>  female_animation_map = 6; // category/sub_category -> resource_id
  map<uint32, uint32>  dating_hat_map = 7;   // hat_id -> resource_id
  repeated uint32 un_show_list = 8;   // 默认男女不展示图标的素体资源 男女都一起
  map<uint32, MutualExclusion>  map_mutual_exclusion = 9;  // category 互斥资源列表
  string guide_url = 10; // 新手引导地址
  string guide_url_md5  = 11; // 新手引导url
  repeated uint32 special_pose_show_category_list = 12; // 支持与特姿同时外显的品类列表， 例如背景、进房特效
 }

// 获取资源品类列表
message GetVirtualImageResourceCategoryRequest {
  ga.BaseReq base_req = 1;
}

message GetVirtualImageResourceCategoryResponse {
  ga.BaseResp base_resp = 1;
  repeated VirtualImageResourceCategoryInfo resource_category_info_list = 2; // 资源品类信息
  bool is_new = 3; // 是否需要新手引导
}


enum CommodityGainPath {
  COMMODITY_GAIN_PATH_UNSPECIFIED = 0;
  COMMODITY_GAIN_PATH_PURCHASE = 1;  // 购买
  COMMODITY_GAIN_PATH_GIFT = 2;      // 礼物 [没用到]
  COMMODITY_GAIN_PATH_ACTIVITY = 3;  // 活动
  COMMODITY_GAIN_PATH_REWARD = 4;    // 奖励 [没用到]
  COMMODITY_GAIN_PATH_EXCHANGE = 5;  // 兑换 [没用到]
  COMMODITY_GAIN_PATH_OTHER = 6;     // 其他 [没用到]
  COMMODITY_GAIN_PATH_INFINITE_CHANGE_CARD = 7; // 无限换装卡权益
}

message CommodityDataPackage {
  uint32 package_id = 1;              // 套餐ID
  uint32 price = 2;                   // 价格
  uint32 discount_price = 3;          // 折扣价格
  uint32 discount_rate = 4;           // 折扣率
  uint32 effective_day = 5;           // 有效天数
  string description = 6;             // 套餐描述
  uint32 expire_time = 7;             // 下架时间 当过期时间为0时表示永久
  bool is_perpetual = 8;              // 有效天数 是否永久
  bool show_expire_time = 9;                        // 是否显示过期时间 true 显示 false 不显示
}

enum CommodityType {
  COMMODITY_TYPE_UNSPECIFIED = 0;
  COMMODITY_TYPE_SINGLE = 1;  // 单类
  COMMODITY_TYPE_SUIT = 2;    // 套装

}

// 活动信息
message VirtualImageActivityInfo {
  string desc = 1;       // 活动描述
  string url = 2;        // 活动链接
  string desc_color = 3;  //描述色值
}

// 运营自定义标识
message CustomizeLogotype {
  string logotype = 1;                // 自定义标识
  uint32 shelf_time = 2;              // 上架时间
  uint32 expire_time = 3;             // 下架时间
  bool is_perpetual = 4;              // 是否永久
}

message CommodityData {
  uint32 commodity_id = 1;                            // 商品ID
  string level_icon = 2;                              // 等级图标
  uint32 level = 3;                                   // 商品等级
  string logotype = 4;                                // 自定义标识
  string commodity_icon = 5;                          // 商品图标
  string commodity_animation = 6;                     // 商品动画
  string commodity_name = 7;                          // 商品名称
  uint32 gain_path = 8;                               // 获得方式 see CommodityGainPath
  repeated VirtualImageResourceInfo resource_list = 9;               // 资源列表
  uint32 category = 10;                               // 资源品类 see virtual_image_resource VirtualImageResourceCategory
  uint32 sub_category = 11;                           // 资源子分类
  uint32 commodity_type = 12;                         // 商品类型 see CommodityType
  uint32 rank = 13;                                   // 排序
  uint32 resource_sex = 16;                           // 资源性别     1: 男  2: 女 3: 通用 和 virtual_image_resource sex定义一致
  repeated CommodityDataPackage price_package_list = 17;        // 价格套餐列表
  VirtualImageActivityInfo act_info = 18;                         // 活动信息
  uint32 expire_time = 19;                          // 下架时间 当过期时间为0时表示永久
  string spine_animation = 20;                     // 体态动作 跟产品沟通的
  bool is_get = 21;                                // 是否已经获得
  string commodity_animation_top = 22;                     // 商品动画top
  string commodity_animation_top_md5 = 23;                     // 商品动画top MD5
  string commodity_animation_back = 24;                     // 商品动画背景
  string commodity_animation_back_md5 = 25;                     // 商品动画背景 MD5
  uint32 user_expire_time = 26;                          // 用户获取商品过期时间
  bool user_is_perpetual = 27;                      // 用户获取商品是否永久
  uint32 user_buy_time = 28;                          // 用户购买时间
  uint32 shelf_time = 29;                            // 上架时间
  bool show_expire_time = 30 [deprecated = true];                 // 是否显示过期时间 true 显示 false 不显示
  uint32 red_dot_version = 31;                       // 红点版本号
  uint32 promotional_video_id = 32;                   // 宣传片id
  string level_webp = 33;                             // 等级图标webp
  bool is_recommend = 34;                             // 是否推荐

  string suit_unique_id = 35; // 套装唯一标识
  uint32 special_pose_id = 36; // 特姿物品id
}

// 获取数量变化时的商品总价
message ComputeCommodityPriceRequest {
  ga.BaseReq base_req = 1;
  repeated ShoppingItemBasic commodity_item_list = 2;       // 商品列表 填写商品ID 套餐ID 购买数量

}

message ComputeCommodityPriceResponse {
  ga.BaseResp base_resp = 1;
  repeated ShoppingItemBasic commodity_item_list = 2; // 商品列表
  uint32 total_price = 3;              // 总价格
  repeated ShoppingItemBasic expire_commodity_item_list = 4;       // 下架商品列表
}

// 获取商品列表 category和sub_category 都填0时 获取全量商品列表
message GetCommodityDataListRequest {
  ga.BaseReq base_req = 1;
  uint32 category = 2;
  uint32 sub_category = 3;
  repeated uint32 resource_id_list = 4; // 物品ID列表， 如果不传则获取所有商品
  uint32 commodity_type = 5; // 商品类型 see CommodityType, 不传则获取所有商品
}

message GetCommodityDataListResponse {
  ga.BaseResp base_resp = 1;
  repeated CommodityData commodity_data_list = 2;       // 商品列表
}

// 获取推荐商品列表
message GetRecommendCommodityDataListRequest {
  ga.BaseReq base_req = 1;
}

message GetRecommendCommodityDataListResponse {
  ga.BaseResp base_resp = 1;
  repeated CommodityData commodity_data_list = 2;       // 商品列表
}

// 购物车商品基础数据
message ShoppingItemBasic {
  //uint32 shopping_item_id = 1;        // 购物车商品ID
  uint32 commodity_id = 2;            // 商品ID
  uint32 package_id = 3;              // 套餐ID
  uint32 count = 4;                   // 购买数量
  uint32 total_price = 5;              // 总价格
  uint32 avg_price = 6;                // 均价
  uint32 effective_day = 7;            // 有效天数
}


// 购买商品
message BuyCommodityDataRequest {
  ga.BaseReq base_req = 1;
  repeated ShoppingItemBasic commodity_item_list = 2;       // 商品列表 填写商品ID 套餐ID 购买数量
}

message BuyCommodityDataResponse {
  ga.BaseResp base_resp = 1;
  repeated ShoppingItemBasic expire_commodity_item_list = 4 [deprecated = true];       // 下架商品列表
  string order_id = 2;              // 订单号
}

// 根据id获取商品详情
message GetCommodityDataListByIdRequest {
  ga.BaseReq base_req = 1;
  repeated ShoppingItemBasic commodity_item_list = 2;       // 商品列表 填写商品ID 套餐ID
}

message GetCommodityDataListByIdResponse {
  ga.BaseResp base_resp = 1;
  repeated CommodityData commodity_data_list = 2;       // 商品列表
}

// FreeCommodityGainNotify 免费获得商品弹窗
message FreeCommodityGainNotify {
  uint32 uid = 1;      // 用户uid
  uint32 cnt = 2;      // 数量
  repeated CommodityData commodity_data_list = 3;       // 商品列表
}

// =========== 虚拟形象二期 资源管理相关 ===========

//获取资源列表
message GetResourceListRequest {
  ga.BaseReq base_req = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  uint32 latest_version = 4; //资源列表最新版本号,客户端有缓存则带上
}

enum VirtualImageResourceType {
  VIRTUAL_IMAGE_RESOURCE_TYPE_UNSPECIFIED = 0;
  VIRTUAL_IMAGE_RESOURCE_TYPE_SKELETON = 1;    // 骨骼&纹理
  VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN = 2;        // 皮肤
  VIRTUAL_IMAGE_RESOURCE_TYPE_BACKGROUND = 3;   // 背景VAP
  VIRTUAL_IMAGE_RESOURCE_TYPE_FOLLOW = 4;   // 跟随进房VAP
  VIRTUAL_IMAGE_RESOURCE_TYPE_SHOW_VAP = 5;   // 宣传片VAP
  VIRTUAL_IMAGE_RESOURCE_TYPE_DEFAULT_BACKGROUND_PNG = 6;   // 默认背景PNG资源
  VIRTUAL_IMAGE_RESOURCE_TYPE_ANIMATION = 7;     // 动作
  VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN_FULL_OUTFIT = 8;        // 特殊皮肤-全身装
  VIRTUAL_IMAGE_RESOURCE_TYPE_INFORMATION_CARD_VAP = 9;    //资料卡主页动画
  VIRTUAL_IMAGE_RESOURCE_TYPE_CP_SKELETON = 10;        // 双人骨骼
  VIRTUAL_IMAGE_RESOURCE_TYPE_SIDE_SKELETON = 11;        // 侧身骨骼
  VIRTUAL_IMAGE_RESOURCE_TYPE_CP_SKIN = 12;        // 双人皮肤
  VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_SKELETON = 13;        // 分皮肤的基础骨架，区别于原来SKELETON
  VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_SIDE_SKELETON = 14;   // 分皮肤的基础侧身骨架，区别于原来 SKELETON
  VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_CP_SKELETON = 15;     // 分皮肤的基础双人骨架，区别于原来 SKELETON
  VIRTUAL_IMAGE_RESOURCE_TYPE_TEZI = 16;                 // 特姿
}

message VirtualImageResourceInfo {
  uint32 id = 1;             // 资源ID
  string skin_name = 2;      // 皮肤名[唯一标识]
  string resource_url = 3;   // 资源地址
  uint32 version = 4;        // 资源版本号
  bool essential = 5;        // 是否必须的
  uint32 shelf_time = 6;     //上架时间
  uint32 expire_time = 7;    //下架时间
  string md5 = 8;
  string encrypt_key = 9;
  string resource_name = 10;  // 资源名称
  uint32 resource_type = 11;  // 资源类型 see VirtualImageResourceType
  string display_name = 12;   // 显示名称
  string icon_url = 13;       // 图标地址
  uint32 category = 14;       // 资源品类
  uint32 sub_category = 15;   // 资源二级品类
  string level_icon = 16;     // 等级图标
  uint32 level = 17;          // 等级
  uint32 sex = 18;            // 性别   1: 男  2: 女 3: 通用
  string level_webp = 19;     // 等级图标webp
  bool   scale_able  = 20;     // 是否支持缩放
  string default_animation = 21; // 默认动作
   uint32 skin_facing = 22;       // 朝向 0:正面 1:侧面45度 --已废弃
  string resource_prefix = 23;   // 资源前缀
  map<string, string> custom_map = 24; // ios资源地址map eg: ios_015:http://xxx.png ios_030:http://xxx.atlas
  //string statement = 25;          // json资源描述 包含资源描述信息下载地址，最小版本号等 ｛url:xx, version:xxx, min_bones_version：xxx｝

  // 按照骨骼名称作为key区分皮肤资源信息
  // 例如:
  // 1. 正身皮肤资源信息, key: base_boy.zip, base_girl.zip
  // 2. 侧身皮肤资源信息, key: base_cboy.zip, base_cgirl.zip
  // 3. 双人正正皮肤资源信息, key: base_zboy_zgirl.zip
  // 4. 双人正侧皮肤资源信息, key: base_zboy_cgirl.zip
  // 5. 双人侧侧皮肤资源信息, key: base_cboy_cgirl.zip
  map<string, SkinInfo> skin_map = 26;
  map<string, SkinInfo> ios_skin_map = 27;
  string ios_resource_url = 28;   // ios资源地址
  uint32 ios_version = 29;   // ios资源版本号
  bool is_new_resource = 30;   // 是否新资源
  // texture资源地址map    key                           : value
  //                    ios_low                        : url - astc(zip)
  //                    ios_high
  //                    android_low                    : url -  astc(zip)
  //                    pc_low                         : url -  ktx2
  map<string, string> texture_url_map = 31; // texture资源地址
}

message SkinInfo {
  string url = 1; //zip: skin.json/skin.skel, skin.atlas; skin与atlas是一一对应的（CI阶段保证）。
  uint32 min_bones_version = 2; // required min skeleton version
  string md5 = 3; // md5
}

message GetResourceListResponse {
  ga.BaseResp base_resp = 1;
  uint32 offset = 2;
  uint32 limit = 3;
  repeated VirtualImageResourceInfo resources = 4;
  uint32 latest_version = 5;  //资源列表最新版本号
  bool is_end = 6;            //是否结束
  string download_url = 7;   // cdn下载地址
  string download_md5 = 8;  //cdn资源md5
}


enum RedDotAlertType {
  RED_DOT_ALERT_TYPE_UNSPECIFIED = 0;
  RED_DOT_ALERT_TYPE_COMMODITY_TAB_PARENT = 1     [deprecated=true];  // 商品tab父级  弃用 因为合并代码不能删除字段 所以只能保留
  RED_DOT_ALERT_TYPE_COMMODITY_TAB_SUB = 2        [deprecated=true];     // 商品tab子级 弃用 因为合并代码不能删除字段 所以只能保留
  RED_DOT_ALERT_TYPE_PERSONAL_IMAGE = 3;        // 个人主页多功能侧边栏【我的形象】入口
  RED_DOT_ALERT_TYPE_VIRTUAL_IMAGE = 4            [deprecated=true];         // 虚拟形象 弃用 因为合并代码不能删除字段 所以只能保留
  RED_DOT_ALERT_TYPE_COMMODITY = 5                [deprecated=true];             // 商品 弃用 因为合并代码不能删除字段 所以只能保留

  RED_DOT_ALERT_TYPE_COMMODITY_TAB = 6;           // 商品tab
  RED_DOT_ALERT_TYPE_ROOM_VIRTUAL_IMAGE = 7;         //   房间多功能页面【虚拟形象】入口
}


// 获取红点状态
message GetRedDotAlertStatusRequest {
  ga.BaseReq base_req = 1;
  uint32 red_dot_aler_type = 2;  // 红点类型 see RedDotAlertType
  uint32 category = 3         [deprecated = true]; // 品类 弃用 因为合并代码不能删除字段 所以只能保留
  uint32 sub_category = 4     [deprecated = true];  // 子品类 弃用 因为合并代码不能删除字段 所以只能保留
}

// 商品tab红点信息
message CommodityTabRedDotInfo {
  uint32 category = 1;          // 品类
  uint32 sub_category = 2;      // 子品类
  bool has_red_dot = 3;         // 是否有红点
}

// 获取虚拟形象红点状态
message GetRedDotAlertStatusResponse {
  ga.BaseResp base_resp = 1;
  uint32 red_dot_aler_type = 2;  // 红点类型 see RedDotAlertType
  bool has_red_dot = 3;          // 是否有红点 非商城的红点
  repeated CommodityTabRedDotInfo commodity_tab_red_dot_list = 4; // 商品tab红点信息

}


// 红点已读
message RedDotAlertReadedRequest {
  ga.BaseReq base_req = 1;
  uint32 red_dot_aler_type = 2;  // 红点类型 see RedDotAlertType
  uint32 category = 3;          // 品类 只有在商品tab时才需要
  uint32 sub_category = 4;      // 子品类 只有在商品tab时才需要
}

message RedDotAlertReadedResponse {
  ga.BaseResp base_resp = 1;
}


// =========== 虚拟形象二期-用户拥有组件 相关 ===========

message UserVirtualImageItem {
  uint32 resource_id = 1; // 组件资源配置id
  int64 expire_ts = 2; // 过期时间
  bool inuse = 3;      // 是否正在使用
  int64 update_ts = 4; // 更新时间
  uint32 use_rights_type = 5 ; // 使用权益类型 see VirtualImageRightsType
  uint32 remain_days = 6;      // 剩余天数
}

message VirtualImageItemTab {
  string tab_name = 1; // tab名称
  string tab_icon = 2; // tab图标
  string tab_select_icon = 3; // tab选中图标
  repeated UserVirtualImageItem items = 4; // 用户拥有的组件
  uint32 category_type = 5; // see VirtualImageResourceCategoryType
  uint32 sub_category_type = 6; // see VirtualImageResourceSubCategoryType
  uint32 category = 7; // 资源品类
  uint32 sub_category = 8; // 资源子分类
}

// 用户的组件套装
message UserVirtualImageSuit {
  string name = 1;
  string icon = 2;
  repeated UserVirtualImageItem items = 3; // 用户拥有的组件
  int64 expire_ts = 4; // 过期时间
  bool inuse = 5;      // 是否正在使用
  int64 update_ts = 6; // 更新时间
  string level_icon = 7; // 等级图标
  uint32 suit_id = 8; // 套装id
  uint32 promotion_resource_id = 9; // 宣传片资源id

  string suit_unique_id = 10; // 套装唯一标识
  uint32 special_pose_id = 11; // 特姿物品id
  uint32 remain_days = 12;      // 剩余天数
}

message VirtualImageSuitTab {
  string tab_name = 1; // tab名称
  string tab_icon = 2; // tab图标
  string tab_select_icon = 3; // tab选中图标
  repeated UserVirtualImageSuit suits = 4; // 用户拥有的套装
}

message VirtualImageAllTabCfg {
  string tab_name = 1; // tab名称
  string tab_icon = 2; // tab图标
  string tab_select_icon = 3; // tab选中图标
}

// 获取用户拥有的虚拟形象
message GetUserVirtualImageRequest {
  ga.BaseReq base_req = 1;
}

message GetUserVirtualImageResponse {
  ga.BaseResp base_resp = 1;
  repeated VirtualImageItemTab item_tabs = 2;     // 用户拥有的组件tab
  VirtualImageSuitTab suit_tab = 3;     // 用户拥有的套装tab
  int64 last_update_ts = 4; // 最后更新时间, 用于客户端判断红点展示
  VirtualImageAllTabCfg all_tab_cfg = 5; // 全部tab配置
  repeated UserVirtualImageItem inuse_rights_items = 6; // 用户权益组件列表，有可能是未拥有的物品
  repeated uint32 inuse_resource_ids = 7; // 正在使用的组件id列表(所有)
}

// 获取用户的虚拟形象姿势组件
message GetUserVirtualImagePoseRequest {
  ga.BaseReq base_req = 1;
  uint32 cid = 2;
}

message GetUserVirtualImagePoseResponse {
  ga.BaseResp base_resp = 1;
  repeated UserVirtualImageItem items = 2; // 用户拥有的姿势组件
  uint32 orientation = 3; // 朝向 see VirtualImageOrientation
  string jump_url = 4;    // 跳转地址,短链
  uint32 sub_category = 5; // 资源子分类
}

// 切换用户房间麦上姿势
message SetUserChannelMicPoseRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2;
    SetUseItem use_pose = 3; // 需要切换的姿势, 默认坐姿则resource_id传0
}

message SetUserChannelMicPoseResponse {
    ga.BaseResp base_resp = 1;
}

enum VirtualImagePoseType {
  VIRTUAL_IMAGE_POSE_TYPE_UNSPECIFIED = 0;
  VIRTUAL_IMAGE_POSE_TYPE_STAND = 1; // 站姿
  VIRTUAL_IMAGE_POSE_TYPE_SIT = 2; // 坐姿
  VIRTUAL_IMAGE_POSE_TYPE_SPECIAL = 3; // 特姿, 仅用于麦位场景下
}

message UserVirtualImageInuse {
  uint32 uid = 1;      // 用户uid
  repeated UserVirtualImageItem items = 2; // 正在使用的组件列表
  uint32 orientation = 3; // 朝向 see VirtualImageOrientation
  uint32 pose_type = 4; // 姿态 see VirtualImagePoseType
}

// 用户的虚拟形象变更通知
message UserVirtualImageChangeOpt {
  uint32 uid = 1;      // 用户uid
  uint32 cid = 2;      // 房间id
  repeated UserVirtualImageItem items = 3; // 组件列表
  uint32 orientation = 4; // 朝向 see VirtualImageOrientation
}

// 批量获取用户正在使用的虚拟形象
message BatchGetUserVirtualImageInuseRequest {
  ga.BaseReq base_req = 1;
  repeated uint32 uid_list = 2; // 用户uid列表
  uint32 scope = 3; // 外显场景 see VirtualImageDisplaySwitch， 预览设置页传0
  uint32 cid = 4;
}

message BatchGetUserVirtualImageInuseResponse {
  ga.BaseResp base_resp = 1;
  repeated UserVirtualImageInuse use_list = 2;
}

// 获取用户正在使用的虚拟形象
message GetUserVirtualImageDisplayRequest {
  ga.BaseReq base_req = 1;
  uint32 target_uid = 2; // 用户uid
  uint32 scope = 3; // 外显场景 see VirtualImageDisplaySwitch
}

message VirtualImageCpImageInfo {
  uint32 resource_id = 1; // 骨骼id
  uint32 action_id = 2;  // 动作id
}

message GetUserVirtualImageDisplayResponse {
  ga.BaseResp base_resp = 1;
  UserVirtualImageInuse inuse_info = 2; // 用户正在使用的虚拟形象信息
  UserProfile user_profile = 3; // 用户信息
  repeated VirtualImageDisplaySwitchInfo switch_info = 4;  // 用户的外显开关状态

  UserVirtualImageInuse relation_inuse_info = 5; // 关系对象正在使用的虚拟形象信息
  UserProfile relation_user_profile = 6; // 关系对象用户信息
  VirtualImageCpImageInfo cp_resource = 7; // 双人骨骼外显,非空则优先展示
}

// 虚拟形象权益
enum VirtualImageRightsType {
  VIRTUAL_IMAGE_RIGHTS_TYPE_UNSPECIFIED = 0;
  VIRTUAL_IMAGE_RIGHTS_TYPE_INFINITE_CHANGE_CARD = 1;   // 无限换装卡
}

message SetUseItem {
  uint32 sub_category = 1;  // 组件品类
  uint32 resource_id = 2; // 组件资源配置id
  uint32 rights_type = 3; // 权益类型 see VirtualImageRightsType
}

// 设置用户使用的虚拟形象
message SetUserVirtualImageInuseRequest {
  ga.BaseReq base_req = 1;
  repeated SetUseItem use_items = 2;     // 使用的组件
  bool incremental_update = 3; // 是否增量更新，true-仅更新传入的组件，false-覆盖更新所有组件，即会先清空原有组件
}

message SetUserVirtualImageInuseResponse {
  ga.BaseResp base_resp = 1;
}

enum VirtualImageOrientation {
  VIRTUAL_IMAGE_ORIENTATION_UNSPECIFIED = 0;
  VIRTUAL_IMAGE_ORIENTATION_RIGHT = 1;  // 朝右
  VIRTUAL_IMAGE_ORIENTATION_LEFT = 2;   // 朝左
}

// 设置用户虚拟形象朝向
message SetUserVirtualImageOrientationRequest {
  ga.BaseReq base_req = 1;
  uint32 orientation = 2; // 朝向， see VirtualImageOrientation
}

message SetUserVirtualImageOrientationResponse {
  ga.BaseResp base_resp = 1;
}

// 外显场景开关类型
enum VirtualImageDisplaySwitch {
  VIRTUAL_IMAGE_DISPLAY_SWITCH_UNSPECIFIED = 0;
  VIRTUAL_IMAGE_DISPLAY_SWITCH_MAIN = 1;  // 总开关
  VIRTUAL_IMAGE_DISPLAY_SWITCH_MIC = 2;  // 麦位
  VIRTUAL_IMAGE_DISPLAY_SWITCH_PERSONAL_PAGE = 3;  // 个人主页
  VIRTUAL_IMAGE_DISPLAY_SWITCH_PROFILE_CARD = 4;  // 资料卡
  VIRTUAL_IMAGE_DISPLAY_SWITCH_ROOM_ENTER_EFFECT = 5;  // 进房特效
  VIRTUAL_IMAGE_DISPLAY_SWITCH_FELLOW_SPACE = 6;  // 挚友柜
}

message VirtualImageDisplaySwitchInfo {
  uint32 switch_type = 1;  // 外显开关类型 see VirtualImageDisplaySwitch
  bool switch_status = 2;  // 外显开关状态， true-开启，false-关闭
}

// 设置外显开关状态
message SetVirtualImageDisplaySwitchRequest {
  ga.BaseReq base_req = 1;
  VirtualImageDisplaySwitchInfo switch_info = 2;  // 外显开关状态
}

message SetVirtualImageDisplaySwitchResponse {
  ga.BaseResp base_resp = 1;
}

message UserRelationInfo {
  uint32 relation_uid_in_use = 1;         // 0-单人模式；非0则为对应关系cpUid
  repeated UserProfile bind_list = 2;     // 已绑定
  repeated UserProfile inviting_list = 3; // 申请中
  uint32 max_bind_count = 4;              // 最大绑定数
}

// 获取用户的外显设置
message GetVirtualImageDisplayCfgRequest {
  ga.BaseReq base_req = 1;
}

message GetVirtualImageDisplayCfgResponse {
  ga.BaseResp base_resp = 1;
  repeated VirtualImageDisplaySwitchInfo switch_info = 2;  // 外显开关状态
  UserRelationInfo user_relation_info = 3; // 用户关系信息
  uint32 auto_play_sec = 4; // 自动播放时长,单位秒
  string my_expire_remind_text = 5;     // 我的形象失效提醒文案
  string target_expire_remind_text = 6; // 对象形象失效提醒文案
  repeated VirtualImagePoseTypeInfo pose_type_info = 7; // 姿态类型
}

message VirtualImagePoseTypeInfo {
  uint32 scene = 1; // see VirtualImageDisplaySwitch
  uint32 pose_type = 2; // see VirtualImagePoseType
}

// 外显状态提醒弹窗
message VirtualImageDisplayStatusPop {
  uint32 uid = 1;
  string content = 2;
  string high_light_content = 3;
  string jump_url = 4;
}

// 外显状态提醒房间公屏
message VirtualImageDisplayStatusChannelNotify {
  uint32 uid = 1;
  uint32 cid = 2;
  string content = 3;
  string high_light_content = 4;
  string jump_url = 5;
}

// =========== 虚拟形象二期-绑定双人关系 相关 ===========

// 1. 获取可邀请列表
message GetBindInvitableListRequest {
    ga.BaseReq base_req = 1;
    string offset = 2;      // 分页offset，第一次传空，后续传上次返回的next_offset
    uint32 limit = 3;       // 每页数量
  }
  
message InvitableUser{
    UserProfile info = 1;
    bool is_online = 2;   // 是否在线
}

  message GetBindInvitableListResponse {
    ga.BaseResp base_resp = 1;
    repeated InvitableUser user_list = 2;
    string next_offset = 3; // 下一页的offset，空表示没有下一页
  }
 
  enum InviteAction {
    INVITE_ACTION_UNSPECIFIED = 0;
    INVITE_ACTION_SEND = 1;   // 发起邀请
    INVITE_ACTION_CANCEL = 2; // 取消邀请
  }
  
  // 2. 发起/取消 关系绑定邀请
  message SetBindInviteRequest {
    ga.BaseReq base_req = 1;
    uint32 target_uid = 2; // 目标用户uid
    uint32 action = 3;     // 操作 see InviteAction
  }
  
  message SetBindInviteResponse {
    ga.BaseResp base_resp = 1;
  }
  
  // 3. 确认/拒绝绑定
  enum BindConfirmAction {
    BIND_CONFIRM_ACTION_UNSPECIFIED = 0;
    BIND_CONFIRM_ACTION_CONFIRM = 1; // 确认
    BIND_CONFIRM_ACTION_REJECT = 2;  // 拒绝
  }
  
  message BindConfirmActionRequest {
    ga.BaseReq base_req = 1;
    string invite_id = 2;  // 邀请id
    uint32 action = 3;     // 操作 see BindConfirmAction
  }
  
  message BindConfirmActionResponse {
    ga.BaseResp base_resp = 1;
  }
  
  enum BindInviteStatus {
      BIND_INVITE_STATUS_UNSPECIFIED = 0; // 无效值，不展示弹窗
      BIND_INVITE_STATUS_UNHANDLED = 1; // 未处理
      BIND_INVITE_STATUS_ACCEPTED = 2;  // 已同意
      BIND_INVITE_STATUS_REJECTED = 3;  // 已拒绝
      BIND_INVITE_STATUS_EXPIRED = 4;   // 已失效
    }
  
  // 4. 获取关系绑定请求状态
  message GetBindInviteStatusRequest {
    ga.BaseReq base_req = 1;
    string invite_id = 2;  // 邀请id
  }
  
  message GetBindInviteStatusResponse {
    ga.BaseResp base_resp = 1;
    InviteInfo invite_info = 2; // 邀请信息
  }

  // 5. 解除绑定
message UnbindVirtualImageRequest {
    ga.BaseReq base_req = 1;
    uint32 target_uid = 2; // 目标用户uid
}

message UnbindVirtualImageResponse {
    ga.BaseResp base_resp = 1;
}

// 6. 指定使用双人关系
message SetVirtualBindInUseRequest {
    ga.BaseReq base_req = 1;
    uint32 target_uid = 2; // 目标用户target_uid,为0时表示使用单人模式
}

message SetVirtualBindInUseResponse {
    ga.BaseResp base_resp = 1;
}

message InviteInfo{
    string invite_id = 1;  // 邀请id
    UserProfile inviter = 2; // 发起人信息
    uint32 status = 3; // see BindInviteStatus
}

// 获取用户待处理的关系绑定邀请列表
message GetBindBeInvitedListRequest {
    ga.BaseReq base_req = 1;
}

message GetBindBeInvitedListResponse {
    ga.BaseResp base_resp = 1;
    repeated InviteInfo invite_list = 2; // 邀请列表
}

// VirtualImageBindInviteMsg, 废弃
message VirtualImageBindInviteMsg {
  UserProfile inviter = 1; // 发起人信息
  string invite_id = 2;  // 邀请id
  string picture_url = 3;	//图片地址
  string text = 4;		//文本
  string highlight_text = 5;	//高亮文本
  string text_color = 6;	//文本颜色
  string jump_url = 7;	//跳转地址
}

message ChannelNoticeCfg {
  int64 begin_time = 1; // 开始时间, 秒级时间戳
  int64 end_time = 2;   // 结束时间, 秒级时间戳

  string public_content = 3; // 公屏内容， 为空则不展示
  string public_content_color = 4; // 公屏内容颜色
  string public_content_jump_url = 5; // 公屏调整链接

  string float_content = 6; // 浮层文案， 为空则不展示
  uint32 float_content_duration = 7; // 浮层显示时长, 单位秒

  bool has_red_dot = 8; // 是否有红点
}

// 虚拟形象入口权限
message CheckUserVirtualImageEntranceRequest {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}

message CheckUserVirtualImageEntranceResponse {
  ga.BaseResp base_resp = 1;
  bool has_entrance = 2; // 是否有虚拟形象入口权限
  bool game_enable = 3;  // 功能总开关, 若关闭则不展示虚拟形象相关的内容
  ChannelNoticeCfg channel_notice_cfg = 4; // 房间提醒配置
}


message GetVirtualImageCommodityRedDotRequest {
  ga.BaseReq base_req = 1;
}

message GetVirtualImageCommodityRedDotResponse {
  ga.BaseResp base_resp = 1;
  uint32 global_version = 2; // 全局红点版本号
}

message CommodityRedDotInfo {
  uint32 commodity_id = 1; // 商品ID
  uint32 red_dot_version = 2; // 红点版本号
  uint32 category = 3; // 品类
  uint32 sub_category = 4; // 子品类
  string commodity_name = 5; // 商品名称
  bool is_recommend = 6; // 是否推荐

}
message GetVirtualImageCommodityRedDotDetailRequest {
  ga.BaseReq base_req = 1;
  uint32 max_version = 2; // 最大红点版本号
}

message GetVirtualImageCommodityRedDotDetailResponse {
  ga.BaseResp base_resp = 1;
  repeated CommodityRedDotInfo commodity_red_dot_infos = 2; // 商品tab红点信息
  uint32 max_version = 3; // 最大红点版本号
}

// 获取虚拟形象卡片通用配置 cfg_version 变大时调用
message GetVirtualImageCardCommonCfgRequest {
  ga.BaseReq base_req = 1;
}

message AboutToExpireCfg {
  string icon = 1; // 即将过期图标
  uint32 expire_alert_time = 15; // 过期提醒时间，单位小时（24/48/72小时）
}

message GetVirtualImageCardCommonCfgResponse {
  ga.BaseResp base_resp = 1;
  string wait_to_buy_icon = 2; // 从未购买/已过期购买动画 【1、房间挂件】
  string already_buy_icon = 3; // 已购买图标 【1、房间挂件】
  repeated AboutToExpireCfg about_to_expire_cfg_list = 4; // 即将过期动画 【1、房间挂件】, 列表顺序从近到远(一天、两天、三天)
  string first_enter_card_store_url = 5; // 首次进入无限卡商城弹窗vap 【2、商城弹窗】
  string ad_text = 6[deprecated=true]; // 广告文案 如：海量商品免费使用   【4、商城商品列表】
  uint32 n_day_show_once = 7; // 【4、商城商品列表】 n天只展示一次 用户处于从未购买或已过期状态时，选中无限换装卡权益内的商品后会有浮层提示购买卡，n天内最多出现1次
  uint32 cfg_version = 8; // 配置版本号
  string wait_to_buy_bg = 9; // 从未购买/已过期购买底图 【4、商城商品列表】
  string already_buy_bg = 10; // 已购买底图 【4、商城商品列表】
  string about_to_expire_bg = 11; // 即将过期底图 【4、商城商品列表】
  string store_resident_entry_icon = 12; // 商城常驻入口图标 【3、商城预览区】
  string store_tab_icon_selected = 13; // 商城【无限换装tab】选中图标
  string store_tab_icon_unselected = 14; // 商城【无限换装tab】未选中图标
  uint32 expire_alert_time = 15; // 过期提醒时间，单位小时（72小时）
  string first_enter_card_store_md5 = 16; // 首次进入无限卡商城弹窗md5 【2、商城弹窗】
  string pc_wait_to_buy_bg = 17; // pc 端从未购买/已过期购买背景图
  string pc_already_buy_bg = 18; // pc 端已购买背景图
  string pc_about_to_expire_bg = 19; // pc 端即将过期背景图
}

// 获取虚拟形象卡片入口状态
message GetVirtualImageCardEntryStatusRequest {
  ga.BaseReq base_req = 1;
}

// 虚拟形象卡片入口状态
message GetVirtualImageCardEntryStatusResponse {
  ga.BaseResp base_resp = 1;
  uint32 expire_time = 2; // 无限卡过期时间，未购买则为0，结束时间戳
  string low_price_text = 3; // 【4、商城商品列表】 低价文案 低至10豆/天
  uint32 ad_idx = 4;  // 【1、房间挂件】房间资源位顺序，配置0时无限卡最后,非0时配置第几个就是在第几个,不管有没有打龙;如果超过了最大的个数就是最后
  uint32 cfg_version = 5; // 配置版本号, 版本号变大，则请求GetVirtualImageCardCommonCfg
  bool switch = 6; // 功能是否开启
  int64 trial_effect_ts = 7; // 体验卡生效时间戳。没用过则为0
  int64 trial_expire_ts = 8; // 体验卡过期时间戳。没用过则为0
  int64 buy_effect_ts = 9; // 购买生效时间戳。没买过则为0
  int64 buy_expire_ts = 10; // 购买过期时间戳。没买过则为0
}

// 无限换装卡变更推送，开通/自动续费/退款都会推，用于客户端更新卡的状态
message VirtualImageCardStatusChangeNotify {
  uint32 expire_time = 1; // 无限卡过期时间，结束时间戳
  int64 trial_effect_ts = 2; // 体验卡生效时间戳。没用过则为0
  int64 trial_expire_ts = 3; // 体验卡过期时间戳。没用过则为0
  int64 buy_effect_ts = 4; // 购买生效时间戳。没买过则为0
  int64 buy_expire_ts = 5; // 购买过期时间戳。没买过则为0
}

// 无限换装卡支付结果扩展信息
message VirtualImageCardPayResultExt {
  enum ResultType {
    RESULT_TYPE_UNSPECIFIED = 0; // 正常，无任何附加信息
    RESULT_TYPE_ALREADY_CONTRACT = 1; // 单买套餐成功了，但前面已签约了，弹窗提醒
    RESULT_TYPE_PAY_FOR_OTHER = 2; // 成功了，但是给别的帐号充了，弹窗提醒
  }
  ResultType result_type = 1;
  string popup_info = 2; // 弹窗信息
}

message SetVirtualImagePoseTypeRequest {
  ga.BaseReq base_req = 1;
  uint32 pose_type = 2; // see VirtualImagePoseType
  uint32 scene = 3; // see VirtualImageDisplaySwitch
}

message SetVirtualImagePoseTypeResponse {
  ga.BaseResp base_resp = 1;
}

message GetVirtualImageBeginnerGuideRequest {
  ga.BaseReq base_req = 1;
}

message GetVirtualImageBeginnerGuideResponse {
  ga.BaseResp base_resp = 1;
  bool has_guide = 2; // 是否有新手引导
  uint32 wait_source_time = 3; // 等待资源加载时间
}

message MarkVirtualImageBeginnerGuideDoneRequest {
  ga.BaseReq base_req = 1;
}

message MarkVirtualImageBeginnerGuideDoneResponse {
  ga.BaseResp base_resp = 1;
}

message GetAvailableVirtualImageTrialCardCountRequest {
  ga.BaseReq base_req = 1;
}

message GetAvailableVirtualImageTrialCardCountResponse {
  ga.BaseResp base_resp = 1;
  uint32 count = 2; // 可用体验卡数量
}

message GetFirstBuyRewardEntryInfoRequest {
  ga.BaseReq base_req = 1;
}

message GetAvailableVirtualImageTrialCardDetailRequest {
  ga.BaseReq base_req = 1;
  string trial_cards_info = 2; // 客户端本地缓存的体验卡信息
}

message GetAvailableVirtualImageTrialCardDetailResponse {
  ga.BaseResp base_resp = 1;
  uint32 count = 2; // 可用体验卡数量
  bool has_new_trial_card = 3; // 是否有新发放的体验卡未查看
  string trial_cards_info = 4; // 最新的体验卡信息，客户端收到后更新到本地，后续请求带上来比对，用于判断是否有新卡
}

message GetFirstBuyRewardEntryInfoResponse {
  ga.BaseResp base_resp = 1;
  bool show_entry = 2; // 是否显示入口
  string entry_url = 3; // 入口链接，动画
  string entry_url_md5 = 4; // 入口链接md5
  string window_url = 5; // 弹窗链接，图片
  string jump_url = 6; // 跳转短链
}
