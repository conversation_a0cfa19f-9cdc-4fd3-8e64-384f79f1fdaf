sequenceDiagram
    participant Client as 📱 客户端
    participant GameHallLogic as 🎮 game-hall-logic
    participant RiskM<PERSON> as 🛡️ 风控服务
    participant Fre<PERSON>ervice as ⏱️ 频率限制服务
    participant AccountService as 👤 账号服务
    participant GameHallService as 🏛️ game-hall服务
    participant Push<PERSON> as 📡 推送服务(PushD)
    participant General<PERSON><PERSON> as 📝 消息管理器

    Note over Client, PushD: 游戏大厅消息发送流程

    %% 客户端发送消息
    Client->>GameHallLogic: 发消息
    %% 基础验证
    GameHallLogic->>GameHallLogic: 检查禁言状态(checkBanSay)

    %% 消息类型分发处理
    GameHallLogic->>GameHallLogic: 根据消息类型分发进行业务处理

    %% 记录消息
    GameHallLogic->>GameHallService: 添加消息记录(AddMsgRecord)
    GameHallService-->>GameHallLogic: 记录添加成功

    %% 推送消息
    GameHallLogic->>PushD: 推送消息到PushD
    Note over GameHallLogic, PushD: 通过multi_publisher广播消息
    PushD-->>GameHallLogic: 推送成功响应

    %% 异步更新频率统计
    GameHallLogic->>GameHallLogic: 异步更新发送频率统计
    Note over GameHallLogic: 根据消息类型更新对应的频率计数

    %% 返回结果
    GameHallLogic-->>Client: 返回发送结果
    Note over GameHallLogic, Client: 包含完整的消息对象

    Note over Client, PushD: 消息发送流程完成
