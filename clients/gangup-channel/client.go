package gangup_channel

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/gangup-channel"
	"google.golang.org/grpc"
)

const (
	serviceName = "gangup-channel"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewGangupChannelClient(cc)
			},
			dopts...,
		),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.GangupChannelClient {
	return c.Stub().(pb.GangupChannelClient)
}

func (c *Client) SetGangupChannelReleaseInfo(ctx context.Context, in *pb.SetGangupChannelReleaseInfoReq) (out *pb.SetGangupChannelReleaseInfoResp, err error) {
	return c.typedStub().SetGangupChannelReleaseInfo(ctx, in)
}

func (c *Client) DismissGangupChannel(ctx context.Context, in *pb.DismissGangupChannelReq) (out *pb.DismissGangupChannelResp, err error) {
	return c.typedStub().DismissGangupChannel(ctx, in)
}

func (c *Client) GetGangupChannelList(ctx context.Context, in *pb.GetGangupChannelListReq) (out *pb.GetGangupChannelListResp, err error) {
	return c.typedStub().GetGangupChannelList(ctx, in)
}

// 可根据Types筛选房间，Types为空或则returnAll为true返回所有房间信息
func (c *Client) GetGangupChannelByIds(ctx context.Context, in *pb.GetGangupChannelByIdsReq) (out *pb.GetGangupChannelByIdsResp, err error) {
	return c.typedStub().GetGangupChannelByIds(ctx, in)
}

func (c *Client) GetChannelRoomUserNumber(ctx context.Context, in *pb.GetChannelRoomUserNumberReq) (out *pb.GetChannelRoomUserNumberResp, err error) {
	return c.typedStub().GetChannelRoomUserNumber(ctx, in)
}

func (c *Client) GetChannelRoomUserNumberMap(ctx context.Context, tabIds []uint32) (map[uint32]int64, error) {
	resp, err := c.typedStub().GetChannelRoomUserNumber(ctx, &pb.GetChannelRoomUserNumberReq{
		TabId: tabIds,
	})
	if err != nil {
		return nil, err
	}

	roomMap := make(map[uint32]int64, len(resp.GetRoomUserInfo()))
	for _, roomUserInfo := range resp.GetRoomUserInfo() {
		roomMap[roomUserInfo.GetTabId()] = roomUserInfo.GetTotalUserNumber()
	}

	return roomMap, nil
}

func (c *Client) AddTemporaryChannel(ctx context.Context, in *pb.AddTemporaryChannelReq) (out *pb.AddTemporaryChannelResp, err error) {
	return c.typedStub().AddTemporaryChannel(ctx, in)
}

func (c *Client) SwitchChannelTab(ctx context.Context, in *pb.SwitchChannelTabReq) (out *pb.SwitchChannelTabResp, err error) {
	return c.typedStub().SwitchChannelTab(ctx, in)
}

func (c *Client) GetOnlineInfo(ctx context.Context, in *pb.GetOnlineInfoReq) (out *pb.GetOnlineInfoResp, err error) {
	return c.typedStub().GetOnlineInfo(ctx, in)
}

func (c *Client) NegativeFeedBack(ctx context.Context, in *pb.NegativeFeedBackReq) (out *pb.NegativeFeedBackResp, err error) {
	return c.typedStub().NegativeFeedBack(ctx, in)
}

func (c *Client) CleanChannelInfo(ctx context.Context, in *pb.CleanChannelInfoReq) (out *pb.CleanChannelInfoResp, err error) {
	return c.typedStub().CleanChannelInfo(ctx, in)
}

func (c *Client) GetChannelIdsByTabId(ctx context.Context, in *pb.GetChannelIdsByTabIdReq) (out *pb.GetChannelIdsByTabIdResp, err error) {
	return c.typedStub().GetChannelIdsByTabId(ctx, in)
}

func (c *Client) GetGameHomePageDIYFilter(ctx context.Context, in *pb.GetGameHomePageDIYFilterReq) (*pb.GetGameHomePageDIYFilterResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGameHomePageDIYFilter(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetGameHomePageDIYFilter(ctx context.Context, in *pb.SetGameHomePageDIYFilterReq) (*pb.SetGameHomePageDIYFilterResp, protocol.ServerError) {
	resp, err := c.typedStub().SetGameHomePageDIYFilter(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetGangupChannelDenoiseMode(ctx context.Context, in *pb.SetGangupChannelDenoiseModeReq) (*pb.SetGangupChannelDenoiseModeResp, error) {
	return c.typedStub().SetGangupChannelDenoiseMode(ctx, in)
}

func (c *Client) GetGangupChannelExtraInfo(ctx context.Context, in *pb.GetGangupChannelExtraInfoReq) (*pb.GetGangupChannelExtraInfoResp, error) {
	return c.typedStub().GetGangupChannelExtraInfo(ctx, in)
}

func (c *Client) GetGangupExtraHistory(ctx context.Context, in *pb.GetGangupExtraHistoryReq) (*pb.GetGangupExtraHistoryResp, error) {
	return c.typedStub().GetGangupExtraHistory(ctx, in)
}

func (c *Client) SetGangupExtraHistory(ctx context.Context, in *pb.SetGangupExtraHistoryReq) (*pb.SetGangupExtraHistoryResp, error) {
	return c.typedStub().SetGangupExtraHistory(ctx, in)
}

func (c *Client) GetGangupPublishCountHistory(ctx context.Context, in *pb.GetGangupPublishCountHistoryReq) (*pb.GetGangupPublishCountHistoryResp, error) {
	return c.typedStub().GetGangupPublishCountHistory(ctx, in)
}

func (c *Client) SetGangupPublishCountHistory(ctx context.Context, in *pb.SetGangupPublishCountHistoryReq) (*pb.SetGangupPublishCountHistoryResp, error) {
	return c.typedStub().SetGangupPublishCountHistory(ctx, in)
}

func (c *Client) GetDIYFilterByEntrance(ctx context.Context, in *pb.GetDIYFilterByEntranceReq) (*pb.GetDIYFilterByEntranceResp, protocol.ServerError) {
	resp, err := c.typedStub().GetDIYFilterByEntrance(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetDIYFilterByEntrance(ctx context.Context, in *pb.SetDIYFilterByEntranceReq) (*pb.SetDIYFilterByEntranceResp, protocol.ServerError) {
	resp, err := c.typedStub().SetDIYFilterByEntrance(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetReleasingChannelCountByTabIds(ctx context.Context,
	in *pb.GetReleasingChannelCountByTabIdsReq) (*pb.GetReleasingChannelCountByTabIdsResp, error) {
	return c.typedStub().GetReleasingChannelCountByTabIds(ctx, in)
}

func (c *Client) GetReleasingChannelRandomCountByTabIds(ctx context.Context,
	in *pb.GetReleasingChannelCountByTabIdsReq) (*pb.GetReleasingChannelCountByTabIdsResp, error) {
	return c.typedStub().GetReleasingChannelRandomCountByTabIds(ctx, in)
}

func (c *Client) GetNegativeFeedbackOption(ctx context.Context, in *pb.GetNegativeFeedbackOptionReq) (*pb.GetNegativeFeedbackOptionResp, error) {
	resp, err := c.typedStub().GetNegativeFeedbackOption(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelListFilterRecord(ctx context.Context, in *pb.GetChannelListFilterRecordReq) (*pb.GetChannelListFilterRecordResp, error) {
	resp, err := c.typedStub().GetChannelListFilterRecord(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateChannelListFilterRecord(ctx context.Context, in *pb.UpdateChannelListFilterRecordReq) (*pb.UpdateChannelListFilterRecordResp, error) {
	resp, err := c.typedStub().UpdateChannelListFilterRecord(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelMicVolSet(ctx context.Context, in *pb.GetChannelMicVolSetReq) (*pb.GetChannelMicVolSetResp, error) {
	resp, err := c.typedStub().GetChannelMicVolSet(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChannelMicVol(ctx context.Context, in *pb.SetChannelMicVolReq) (*pb.SetChannelMicVolResp, error) {
	resp, err := c.typedStub().SetChannelMicVol(ctx, in)
	return resp, protocol.ToServerError(err)
}

