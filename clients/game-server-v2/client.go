package game_server_v2

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/game-server-v2"
	"google.golang.org/grpc"
)

const (
	serviceName = "game-server-v2"
)

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewGameServerV2Client(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.GameServerV2Client {
	return c.Stub().(pb.GameServerV2Client)
}

func (c *Client) GetAllScanGameListConf(ctx context.Context) (*pb.GetAllScanGameListConfResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAllScanGameListConf(ctx, &pb.GetAllScanGameListConfReq{})

	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return resp, nil
}

func (c *Client) GetAllPcScanGameListConf(ctx context.Context) (*pb.GetAllPcScanGameListConfResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAllPcScanGameListConf(ctx, &pb.GetAllPcScanGameListConfReq{})

	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return resp, nil
}

func (c *Client) BatGetScanGameConfByUGameId(ctx context.Context, gameIds []uint32) (map[uint32]*pb.ScanGameInfo, protocol.ServerError) {
	resp, err := c.typedStub().BatGetScanGameConfByUGameId(ctx, &pb.BatGetScanGameConfByUGameIdReq{UGameId: gameIds})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	confMap := make(map[uint32]*pb.ScanGameInfo, 0)
	for _, v := range resp.ScanGameList {
		if v == nil {
			continue
		}
		confMap[v.UGameId] = v
	}

	return confMap, nil
}

func (c *Client) GetAllScanGameListConfMap(ctx context.Context) (map[uint32]*pb.ScanGameInfo, protocol.ServerError) {
	resp, err := c.typedStub().GetAllScanGameListConf(ctx, &pb.GetAllScanGameListConfReq{})

	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	confMap := make(map[uint32]*pb.ScanGameInfo, 0)
	for _, v := range resp.ScanGameList {
		confMap[v.UGameId] = v
	}

	return confMap, nil
}

func (c *Client) ReportGameScanResult(ctx context.Context, req *pb.ReportGameScanResultReq) protocol.ServerError {
	_, err := c.typedStub().ReportGameScanResult(ctx, req)

	return protocol.ToServerError(err)
}
func (c *Client) GetGameScanResult(ctx context.Context, uid uint32) (*pb.GetGameScanResultResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGameScanResult(ctx, &pb.GetGameScanResultReq{Uid: uid})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}

func (c *Client) GetMinorityGame(ctx context.Context) (out *pb.GetMinorityGameResp, err error) {
	in := &pb.GetMinorityGameReq{}
	return c.typedStub().GetMinorityGameWithCache(ctx, in)
}

func (c *Client) ReportFastPcShowProcessInfo(ctx context.Context, item *pb.FastPcShowProcessInfoItem) (out *pb.ReportFastPcShowProcessInfoResp, err error) {
	in := &pb.ReportFastPcShowProcessInfoReq{
		Item: item,
	}
	return c.typedStub().ReportFastPcShowProcessInfo(ctx, in)
}

func (c *Client) GetFastPcShowProcessInfo(ctx context.Context, uids []uint32) (out *pb.GetFastPcShowProcessInfoResp, err error) {
	in := &pb.GetFastPcShowProcessInfoReq{
		Uids: uids,
	}
	return c.typedStub().GetFastPcShowProcessInfo(ctx, in)
}

// 运营后台配置client-------------------------------------------------------------------------------------------------------------------
func NewConfClient(dopts ...grpc.DialOption) (*ConfClient, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newConfClient(dopts...)
}

type ConfClient struct {
	client.BaseClient
}

func newConfClient(dopts ...grpc.DialOption) (*ConfClient, error) {
	return &ConfClient{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewGameConfMgrClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *ConfClient) typedStubConf() pb.GameConfMgrClient {
	return c.Stub().(pb.GameConfMgrClient)
}

func (c *ConfClient) AddScanGameList(ctx context.Context, gameName string, score uint32, iosPack, androidPackage []string) (uint32, protocol.ServerError) {
	resp, err := c.typedStubConf().AddScanGameList(ctx, &pb.AddScanGameListReq{
		GameName:       gameName,
		UGameId:        0,
		Score:          int32(score),
		IosPackage:     iosPack,
		AndroidPackage: androidPackage,
	})
	if err != nil {
		return 0, protocol.ToServerError(err)
	}

	return resp.Id, nil
}

func (c *ConfClient) GetScanGameList(ctx context.Context, gameName string, offset, limit uint32) (*pb.GetScanGameListResp, protocol.ServerError) {
	resp, err := c.typedStubConf().GetScanGameList(ctx, &pb.GetScanGameListReq{
		GameName: gameName,
		Offset:   offset,
		Limit:    limit,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return resp, nil
}

func (c *ConfClient) CreateGame(ctx context.Context, gameName string, gameType uint32) (uint32, error) {
	resp, err := c.typedStubConf().CreateGame(ctx, &pb.CreateGameReq{
		GameName: gameName,
		Type:     pb.GameType(gameType),
	})

	if err != nil {
		return 0, protocol.ToServerError(err)
	}

	return resp.UGameId, nil
}

func (c *ConfClient) GetGameByName(ctx context.Context, gameName string, offSet, limit uint32) (*pb.GetGameByNameResp, protocol.ServerError) {
	resp, err := c.typedStubConf().GetGameByName(ctx, &pb.GetGameByNameReq{
		GameName: gameName,
		Offset:   offSet,
		Limit:    limit,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return resp, nil
}

func (c *ConfClient) GetGameMapByGameCardIds(ctx context.Context, gameCardIds []uint32, categoryGameCardId uint32) (map[uint32][]*pb.GameInfo, protocol.ServerError) {
	resp, err := c.typedStubConf().GetGameByGameCardId(ctx, &pb.GetGameByGameCardIdReq{
		GameCardIds:        gameCardIds,
		CategoryGameCardId: categoryGameCardId,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	res := make(map[uint32][]*pb.GameInfo, len(resp.GetGameList()))
	for _, t := range resp.GetGameList() {
		if v, ok := res[t.GetGameCardId()]; ok {
			v = append(v, t)
			res[t.GetGameCardId()] = v
		} else {
			res[t.GetGameCardId()] = []*pb.GameInfo{{
				UGameId:  t.GetUGameId(),
				GameName: t.GetGameName(),
				Type:     t.GetType(),
			}}
		}
	}

	return res, nil
}

func (c *ConfClient) GetGameByUGameIds(ctx context.Context, uGameIds []uint32) (map[uint32]*pb.GameInfo, protocol.ServerError) {
	resp, err := c.typedStubConf().GetGameByUGameIds(ctx, &pb.GetGameByUGameIdsReq{
		UGameIds: uGameIds,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.GetGameInfoMap(), nil
}

func (c *ConfClient) UpdateGameByUGameId(ctx context.Context, req *pb.ModifyGameReq) (*pb.ModifyGameResp, protocol.ServerError) {
	resp, err := c.typedStubConf().ModifyGame(ctx, req)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp, nil
}

func (c *ConfClient) ModifyUGameIdForScanGameList(ctx context.Context, id, uGameId uint32) error {
	_, err := c.typedStubConf().ModifyUGameIdForScanGameList(ctx, &pb.ModifyUGameIdForScanGameListReq{
		Id:      id,
		UGameId: uGameId,
	})

	return err
}

func (c *ConfClient) AddMinorityGame(ctx context.Context, in *pb.AddMinorityGameReq) (out *pb.AddMinorityGameResp, err error) {
	return c.typedStubConf().AddMinorityGame(ctx, in)
}

func (c *ConfClient) RemoveMinorityGame(ctx context.Context, uGameId uint32) (out *pb.RemoveMinorityGameResp, err error) {
	in := &pb.RemoveMinorityGameReq{UGameId: uGameId}
	return c.typedStubConf().RemoveMinorityGame(ctx, in)
}

func (c *ConfClient) GetMinorityGame(ctx context.Context) (out *pb.GetMinorityGameResp, err error) {
	in := &pb.GetMinorityGameReq{}
	return c.typedStubConf().GetMinorityGame(ctx, in)
}

func (c *ConfClient) ChangeMinorityGame(ctx context.Context, uGameId, tabId uint32, gameIcon string) (out *pb.ChangeMinorityGameResp, err error) {
	in := &pb.ChangeMinorityGameReq{UGameId: uGameId, GameIcon: gameIcon, TabId: tabId}
	return c.typedStubConf().ChangeMinorityGame(ctx, in)
}
