package missionTL

import (
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/missiontimelinesvr"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

//go:generate mockgen -destination=../mocks/missiontimeline/iclient.go -package=missiontimeline golang.52tt.com/clients/missiontimeline IClient
type IClient interface {
	client.BaseClient
	ExpCurrencyChanged(ctx context.Context, uid uint32, seq uint32, msg *pb.GrowInfoMessage) protocol.ServerError
	NumericChanged(ctx context.Context, uid uint32, seq uint32, msg *pb.NumericInfoMessage) protocol.ServerError
	ScoreChanged(ctx context.Context, uid uint32, seq uint32, msg *pb.UserScoreMessage) protocol.ServerError
	WriteTimeLineMsg(ctx context.Context, uid uint32, seq uint32, typ uint32, msgBin []byte) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
