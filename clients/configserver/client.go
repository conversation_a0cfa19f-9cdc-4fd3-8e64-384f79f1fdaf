package configserver

import (
	"context"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/configserver"
	"google.golang.org/grpc"
)

const (
	serviceName = "configserver"
)

type Client struct {
	client.BaseClient
}

type ConfigKeyType string

// key请在这里增加防止重复
const (
	CloseSwitch = "false"
	OpenSwitch  = "true"

	AttitudeType           ConfigKeyType = "attitude"
	CommentType            ConfigKeyType = "comment"
	FollowType             ConfigKeyType = "follow"
	CarefulType            ConfigKeyType = "careful"
	LiveStart              ConfigKeyType = "livestart"
	DiyRecommendType       ConfigKeyType = "diyrecommend"
	EnterRoomNotifyType    ConfigKeyType = "enter_room_notify"
	InviteRoomType         ConfigKeyType = "invite_room"
	PalOnlineNotifyType    ConfigKeyType = "pal_online_notify"
	PostAttitudeNotifyType ConfigKeyType = "fast_pc_post_attitude" //动态被赞通知
	FastPcShowProcessType  ConfigKeyType = "fast_pc_show_process"  // 展示进程中的游戏/音乐
)

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewConfigServerClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ConfigServerClient { return c.Stub().(pb.ConfigServerClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) BatchGetUserConfig(ctx context.Context, req pb.BatchGetUserConfigReq) (*pb.BatchGetUserConfigRsp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetUserConfig(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchSaveConfig(ctx context.Context, req pb.BatchSaveConfigReq) (*pb.BatchSaveConfigRsp, protocol.ServerError) {
	resp, err := c.typedStub().BatchSaveConfig(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetConfig(ctx context.Context, req pb.GetConfigReq) (*pb.GetConfigRsp, protocol.ServerError) {
	resp, err := c.typedStub().GetConfig(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUsersConfig(ctx context.Context, req pb.GetUsersConfigReq) (*pb.GetUsersConfigRsp, protocol.ServerError) {
	resp, err := c.typedStub().GetUsersConfig(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUgcMoreConfigList(ctx context.Context, req pb.GetUgcMoreConfigListReq) (*pb.GetUgcMoreConfigListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUgcMoreConfigList(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetFallBackSwitchByTypes(ctx context.Context, req pb.GetFallBackSwitchByTypesReq) (
	*pb.GetFallBackSwitchByTypesResp, protocol.ServerError) {
	resp, err := c.typedStub().GetFallBackSwitchByTypes(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetSwitchStatusByBusinessType(ctx context.Context, req pb.SetSwitchStatusByBusinessTypeReq) (
	*pb.SetSwitchStatusByBusinessTypeResp, protocol.ServerError) {
	resp, err := c.typedStub().SetSwitchStatusByBusinessType(ctx, &req)
	return resp, protocol.ToServerError(err)
}
