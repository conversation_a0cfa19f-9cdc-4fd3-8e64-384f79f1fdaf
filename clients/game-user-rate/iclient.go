// Code generated by quicksilver-cli. DO NOT EDIT.
package game_user_rate

import (
	context "context"
	protocol "golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/game-user-rate"
	"google.golang.org/grpc"
)

type IClient interface {
	AddOtherRate(ctx context.Context, req *pb.AddOtherRateReq) (*pb.AddOtherRateResp, protocol.ServerError)
	AdminGetRateLabels(ctx context.Context, req *pb.AdminGetRateLabelsReq) (*pb.AdminGetRateLabelsResp, protocol.ServerError)
	GetGameDimensionConf(ctx context.Context, req *pb.GetGameDimensionConfReq) (*pb.GetGameDimensionConfResp, protocol.ServerError)
	GetGameUserBeRateList(ctx context.Context, req *pb.GetGameUserBeRateListReq) (*pb.GetGameUserBeRateListResp, protocol.ServerError)
	GetGameUserPersonalImage(ctx context.Context, req *pb.GetGameUserPersonalImageReq) (*pb.GetGameUserPersonalImageResp, protocol.ServerError)
	GetGameUserRateById(ctx context.Context, req *pb.GetGameUserRateByIdReq) (*pb.GetGameUserRateByIdResp, protocol.ServerError)
	GetGameUserRateList(ctx context.Context, req *pb.GetGameUserRateListReq) (*pb.GetGameUserRateListResp, protocol.ServerError)
	GetGameUserSelfRateQuestions(ctx context.Context, req *pb.GetGameUserSelfRateQuestionsReq) (*pb.GetGameUserSelfRateQuestionsResp, protocol.ServerError)
	GetTabByDimensionIds(ctx context.Context, req *pb.GetTabByDimensionIdsReq) (*pb.GetTabByDimensionIdsResp, protocol.ServerError)
	SubmitGameUserRate(ctx context.Context, req *pb.SubmitGameUserRateReq) (*pb.SubmitGameUserRateResp, protocol.ServerError)
	SubmitGameUserSelfRate(ctx context.Context, req *pb.SubmitGameUserSelfRateReq) (*pb.SubmitGameUserSelfRateResp, protocol.ServerError)
	ReorderRateTags(ctx context.Context, req *pb.ReorderRateTagsReq) (*pb.ReorderRateTagsResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
