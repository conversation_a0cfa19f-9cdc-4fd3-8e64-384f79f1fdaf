package game_user_rate

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/game-user-rate"
	"google.golang.org/grpc"
)

const serviceName = "game-user-rate"

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewGameUserRateClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.GameUserRateClient {
	return c.Stub().(pb.GameUserRateClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetGameUserPersonalImage(ctx context.Context, req *pb.GetGameUserPersonalImageReq) (*pb.GetGameUserPersonalImageResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGameUserPersonalImage(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGameDimensionConf(ctx context.Context, req *pb.GetGameDimensionConfReq) (*pb.GetGameDimensionConfResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGameDimensionConf(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGameUserSelfRateQuestions(ctx context.Context, req *pb.GetGameUserSelfRateQuestionsReq) (*pb.GetGameUserSelfRateQuestionsResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGameUserSelfRateQuestions(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SubmitGameUserSelfRate(ctx context.Context, req *pb.SubmitGameUserSelfRateReq) (*pb.SubmitGameUserSelfRateResp, protocol.ServerError) {
	resp, err := c.typedStub().SubmitGameUserSelfRate(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGameUserBeRateList(ctx context.Context, req *pb.GetGameUserBeRateListReq) (*pb.GetGameUserBeRateListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGameUserBeRateList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGameUserRateById(ctx context.Context, req *pb.GetGameUserRateByIdReq) (*pb.GetGameUserRateByIdResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGameUserRateById(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGameUserRateList(ctx context.Context, req *pb.GetGameUserRateListReq) (*pb.GetGameUserRateListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGameUserRateList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SubmitGameUserRate(ctx context.Context, req *pb.SubmitGameUserRateReq) (*pb.SubmitGameUserRateResp, protocol.ServerError) {
	resp, err := c.typedStub().SubmitGameUserRate(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddOtherRate(ctx context.Context, req *pb.AddOtherRateReq) (*pb.AddOtherRateResp, protocol.ServerError) {
	resp, err := c.typedStub().AddOtherRate(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetTabByDimensionIds(ctx context.Context, req *pb.GetTabByDimensionIdsReq) (*pb.GetTabByDimensionIdsResp, protocol.ServerError) {
	resp, err := c.typedStub().GetTabByDimensionIds(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AdminGetRateLabels(ctx context.Context, req *pb.AdminGetRateLabelsReq) (*pb.AdminGetRateLabelsResp, protocol.ServerError) {
	resp, err := c.typedStub().AdminGetRateLabels(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ReorderRateTags(ctx context.Context, req *pb.ReorderRateTagsReq) (*pb.ReorderRateTagsResp, protocol.ServerError) {
	resp, err := c.typedStub().ReorderRateTags(ctx, req)
	return resp, protocol.ToServerError(err)
}
