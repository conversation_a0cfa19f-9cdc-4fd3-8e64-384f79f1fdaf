// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/missiontimeline (interfaces: IClient)

// Package missiontimeline is a generated GoMock package.
package missiontimeline

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	terrors "gitlab.ttyuyin.com/tyr/x/terrors"
	client "golang.52tt.com/pkg/client"
	Mission "golang.52tt.com/protocol/services/missiontimelinesvr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// ExpCurrencyChanged mocks base method.
func (m *MockIClient) ExpCurrencyChanged(arg0 context.Context, arg1, arg2 uint32, arg3 *Mission.GrowInfoMessage) terrors.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExpCurrencyChanged", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(terrors.ServerError)
	return ret0
}

// ExpCurrencyChanged indicates an expected call of ExpCurrencyChanged.
func (mr *MockIClientMockRecorder) ExpCurrencyChanged(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExpCurrencyChanged", reflect.TypeOf((*MockIClient)(nil).ExpCurrencyChanged), arg0, arg1, arg2, arg3)
}

// NumericChanged mocks base method.
func (m *MockIClient) NumericChanged(arg0 context.Context, arg1, arg2 uint32, arg3 *Mission.NumericInfoMessage) terrors.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NumericChanged", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(terrors.ServerError)
	return ret0
}

// NumericChanged indicates an expected call of NumericChanged.
func (mr *MockIClientMockRecorder) NumericChanged(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NumericChanged", reflect.TypeOf((*MockIClient)(nil).NumericChanged), arg0, arg1, arg2, arg3)
}

// ScoreChanged mocks base method.
func (m *MockIClient) ScoreChanged(arg0 context.Context, arg1, arg2 uint32, arg3 *Mission.UserScoreMessage) terrors.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScoreChanged", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(terrors.ServerError)
	return ret0
}

// ScoreChanged indicates an expected call of ScoreChanged.
func (mr *MockIClientMockRecorder) ScoreChanged(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScoreChanged", reflect.TypeOf((*MockIClient)(nil).ScoreChanged), arg0, arg1, arg2, arg3)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// WriteTimeLineMsg mocks base method.
func (m *MockIClient) WriteTimeLineMsg(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 []byte) terrors.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WriteTimeLineMsg", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(terrors.ServerError)
	return ret0
}

// WriteTimeLineMsg indicates an expected call of WriteTimeLineMsg.
func (mr *MockIClientMockRecorder) WriteTimeLineMsg(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteTimeLineMsg", reflect.TypeOf((*MockIClient)(nil).WriteTimeLineMsg), arg0, arg1, arg2, arg3, arg4)
}
