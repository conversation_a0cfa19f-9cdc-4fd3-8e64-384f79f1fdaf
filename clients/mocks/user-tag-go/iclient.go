// Code generated by MockGen. DO NOT EDIT.
// Source: E:\TT\gitmaven\userplay\clients\user-tag-go\iclient.go

// Package user_tag_go is a generated GoMock package.
package user_tag_go

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	protocol "golang.52tt.com/pkg/protocol"
	user_tag_go "golang.52tt.com/protocol/services/user-tag-go"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatGetUserTag mocks base method.
func (m *MockIClient) BatGetUserTag(ctx context.Context, uids []uint32, isNeedTagExt bool) ([]*user_tag_go.UserTagList, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetUserTag", ctx, uids, isNeedTagExt)
	ret0, _ := ret[0].([]*user_tag_go.UserTagList)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatGetUserTag indicates an expected call of BatGetUserTag.
func (mr *MockIClientMockRecorder) BatGetUserTag(ctx, uids, isNeedTagExt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetUserTag", reflect.TypeOf((*MockIClient)(nil).BatGetUserTag), ctx, uids, isNeedTagExt)
}

// GetPersonalTagClassifyConfFromCache mocks base method.
func (m *MockIClient) GetPersonalTagClassifyConfFromCache(ctx context.Context) ([]*user_tag_go.UserOptPersonalTagClassify, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonalTagClassifyConfFromCache", ctx)
	ret0, _ := ret[0].([]*user_tag_go.UserOptPersonalTagClassify)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetPersonalTagClassifyConfFromCache indicates an expected call of GetPersonalTagClassifyConfFromCache.
func (mr *MockIClientMockRecorder) GetPersonalTagClassifyConfFromCache(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalTagClassifyConfFromCache", reflect.TypeOf((*MockIClient)(nil).GetPersonalTagClassifyConfFromCache), ctx)
}

// GetUserTag mocks base method.
func (m *MockIClient) GetUserTag(ctx context.Context, uid uint32, isNeedTagExt bool) (*user_tag_go.UserTagList, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserTag", ctx, uid, isNeedTagExt)
	ret0, _ := ret[0].(*user_tag_go.UserTagList)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserTag indicates an expected call of GetUserTag.
func (mr *MockIClientMockRecorder) GetUserTag(ctx, uid, isNeedTagExt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTag", reflect.TypeOf((*MockIClient)(nil).GetUserTag), ctx, uid, isNeedTagExt)
}

// GetUserTagConfigListFromCache mocks base method.
func (m *MockIClient) GetUserTagConfigListFromCache(ctx context.Context, tagType uint32) ([]*user_tag_go.UserTagConf, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserTagConfigListFromCache", ctx, tagType)
	ret0, _ := ret[0].([]*user_tag_go.UserTagConf)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserTagConfigListFromCache indicates an expected call of GetUserTagConfigListFromCache.
func (mr *MockIClientMockRecorder) GetUserTagConfigListFromCache(ctx, tagType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTagConfigListFromCache", reflect.TypeOf((*MockIClient)(nil).GetUserTagConfigListFromCache), ctx, tagType)
}

// SetRegistTag mocks base method.
func (m *MockIClient) SetRegistTag(ctx context.Context, in *user_tag_go.SetRegistTagReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRegistTag", ctx, in)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// SetRegistTag indicates an expected call of SetRegistTag.
func (mr *MockIClientMockRecorder) SetRegistTag(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRegistTag", reflect.TypeOf((*MockIClient)(nil).SetRegistTag), ctx, in)
}

// SetUserTag mocks base method.
func (m *MockIClient) SetUserTag(ctx context.Context, uid, tagType, SettingCnt uint32, tagList []*user_tag_go.UserTagBase) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserTag", ctx, uid, tagType, SettingCnt, tagList)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// SetUserTag indicates an expected call of SetUserTag.
func (mr *MockIClientMockRecorder) SetUserTag(ctx, uid, tagType, SettingCnt, tagList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserTag", reflect.TypeOf((*MockIClient)(nil).SetUserTag), ctx, uid, tagType, SettingCnt, tagList)
}

// MockIConfClient is a mock of IConfClient interface.
type MockIConfClient struct {
	ctrl     *gomock.Controller
	recorder *MockIConfClientMockRecorder
}

// MockIConfClientMockRecorder is the mock recorder for MockIConfClient.
type MockIConfClientMockRecorder struct {
	mock *MockIConfClient
}

// NewMockIConfClient creates a new mock instance.
func NewMockIConfClient(ctrl *gomock.Controller) *MockIConfClient {
	mock := &MockIConfClient{ctrl: ctrl}
	mock.recorder = &MockIConfClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIConfClient) EXPECT() *MockIConfClientMockRecorder {
	return m.recorder
}

// GetAllClassifyTagConf mocks base method.
func (m *MockIConfClient) GetAllClassifyTagConf(ctx context.Context, tagType uint32) ([]*user_tag_go.ClassifyTagInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllClassifyTagConf", ctx, tagType)
	ret0, _ := ret[0].([]*user_tag_go.ClassifyTagInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllClassifyTagConf indicates an expected call of GetAllClassifyTagConf.
func (mr *MockIConfClientMockRecorder) GetAllClassifyTagConf(ctx, tagType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllClassifyTagConf", reflect.TypeOf((*MockIConfClient)(nil).GetAllClassifyTagConf), ctx, tagType)
}

// GetAllRegistGameRelatedTagConf mocks base method.
func (m *MockIConfClient) GetAllRegistGameRelatedTagConf(ctx context.Context) ([]*user_tag_go.RegistGameRelatedTagConf, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllRegistGameRelatedTagConf", ctx)
	ret0, _ := ret[0].([]*user_tag_go.RegistGameRelatedTagConf)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllRegistGameRelatedTagConf indicates an expected call of GetAllRegistGameRelatedTagConf.
func (mr *MockIConfClientMockRecorder) GetAllRegistGameRelatedTagConf(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllRegistGameRelatedTagConf", reflect.TypeOf((*MockIConfClient)(nil).GetAllRegistGameRelatedTagConf), ctx)
}

// GetAllRegistMyTagConf mocks base method.
func (m *MockIConfClient) GetAllRegistMyTagConf(ctx context.Context) ([]*user_tag_go.ClassifyTagInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllRegistMyTagConf", ctx)
	ret0, _ := ret[0].([]*user_tag_go.ClassifyTagInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllRegistMyTagConf indicates an expected call of GetAllRegistMyTagConf.
func (mr *MockIConfClientMockRecorder) GetAllRegistMyTagConf(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllRegistMyTagConf", reflect.TypeOf((*MockIConfClient)(nil).GetAllRegistMyTagConf), ctx)
}
