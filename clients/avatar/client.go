package avatar

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/avatar"
	"google.golang.org/grpc"
)

const (
	serviceName = "avatar"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewAvatarClient(cc)
			},
			dopts...,
		),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.AvatarClient {
	return c.Stub().(pb.AvatarClient)
}

func (c *Client) SaveAvatar(ctx context.Context, in *pb.SaveAvatarReq) (*pb.SaveAvatarResp, error) {
	resp, err := c.typedStub().SaveAvatar(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAvatar(ctx context.Context, in *pb.GetAvatarReq) (*pb.GetAvatarResp, error) {
	const maxRecvMsgSize = 16 * 1024 * 1024
	resp, err := c.typedStub().GetAvatar(ctx, in, grpc.MaxCallRecvMsgSize(maxRecvMsgSize))
	return resp, protocol.ToServerError(err)
}

func (c *Client) DeleteAvatar(ctx context.Context, in *pb.DeleteAvatarReq) (*pb.DeleteAvatarResp, error) {
	resp, err := c.typedStub().DeleteAvatar(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UploadAvatar(ctx context.Context, in *pb.UploadAvatarReq) (*pb.UploadAvatarResp, error) {
	resp, err := c.typedStub().UploadAvatar(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAvatarVersionByAccount(ctx context.Context, account string) (*pb.GetAvatarVersionByAccountResp, error) {
	resp, err := c.typedStub().GetAvatarVersionByAccount(ctx, &pb.GetAvatarVersionByAccountReq{
		Account: account,
	})
	return resp, protocol.ToServerError(err)
}
