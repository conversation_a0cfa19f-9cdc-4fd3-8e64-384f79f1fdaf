package PushNotification

import (
	"context"
	"fmt"

	"golang.52tt.com/pkg/log"
	gaPush "golang.52tt.com/protocol/app/push"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"

	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/push-notification/v2"
)

const (
	serviceName = "push-notification-v2"

	// DEFAULT - 服务器不保证到达
	DEFAULT = pb.ProxyNotification_DEFAULT
	// RELIABLE - 服务器在一定的时间范围内保证到达
	RELIABLE = pb.ProxyNotification_RELIABLE
)

const (
	// group
	DefaultPolicyGroup = "default"
	PolicyAllGroup     = "all"

	// name
	DefaultPolicyName = "tt"
	PolicyCustomName  = "custom"
	PolicyAndroidName = "android"
	PolicyIosName     = "ios"
	PolicyHarmonyName = "harmony"
	PolicyMobileName  = "mobile"
)

// 默认策略，全发送，包括所有移动端及PC端等
var DefaultPolicy = &pb.TerminalTypePolicy{PolicyGroup: DefaultPolicyGroup, PolicyName: DefaultPolicyName}

// 自定义策略，使用该策略只看请求中的TerminalTypeList
var PolicyCustom = &pb.TerminalTypePolicy{PolicyGroup: DefaultPolicyGroup, PolicyName: PolicyCustomName}

// 只发送所有安卓终端
var PolicyOnlyAndroid = &pb.TerminalTypePolicy{PolicyGroup: PolicyAllGroup, PolicyName: PolicyAndroidName}

// 只发送所有IOS终端
var PolicyOnlyIos = &pb.TerminalTypePolicy{PolicyGroup: PolicyAllGroup, PolicyName: PolicyIosName}

// 只发送所有鸿蒙终端
var PolicyOnlyHarmony = &pb.TerminalTypePolicy{PolicyGroup: PolicyAllGroup, PolicyName: PolicyHarmonyName}

// 发送所有移动端，包括安卓，IOS，鸿蒙
var PolicyAllMobile = &pb.TerminalTypePolicy{PolicyGroup: PolicyAllGroup, PolicyName: PolicyMobileName}

// Client of PushNotification service v2
type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewPushNotificationClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.PushNotificationClient { return c.Stub().(pb.PushNotificationClient) }

// NewTracedClient creates a Client integrated with tracing
func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

// NewClient ...
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

// NewPushToUsersStream creates a stream for pushing notifications to list of specific userIDs
func (c *Client) NewPushToUsersStream(ctx context.Context, block bool) (pb.PushNotification_PushToUsersClient, error) {
	var header, trailer metadata.MD
	return c.typedStub().PushToUsers(ctx, grpc.Header(&header), grpc.Trailer(&trailer), grpc.FailFast(!block))
}

// NewPushMulticastStream creates a stream for pushing notifications with a multicast account
func (c *Client) NewPushMulticastStream(ctx context.Context, block bool) (pb.PushNotification_PushMulticastClient, error) {
	var header, trailer metadata.MD
	return c.typedStub().PushMulticast(ctx, grpc.Header(&header), grpc.Trailer(&trailer), grpc.FailFast(!block))
}

// PushToUsers pushes notifications to list of specific userIDs
func (c *Client) PushToUserMap(ctx context.Context, uidMap map[uint32]*pb.CompositiveNotification) (*pb.PushToUserMapResp, error) {
	resp, err := c.typedStub().UnaryPushToUserMap(ctx, &pb.PushToUserMapReq{
		UidMap: uidMap,
	})

	return resp, protocol.ToServerError(err)
}

// PushToUsers pushes notifications to list of specific userIDs
func (c *Client) PushToUsers(ctx context.Context, userIDList []uint32, notification *pb.CompositiveNotification) error {
	_, err := c.typedStub().UnaryPushToUsers(ctx, &pb.PushToUsersReq{
		UidList:      userIDList,
		Notification: notification,
	})

	return protocol.ToServerError(err)
}

func (c *Client) PushToUsers2(ctx context.Context, userIDList []uint32, notification *pb.CompositiveNotification) (*pb.PushToUsersResp, protocol.ServerError) {
	r, err := c.typedStub().UnaryPushToUsers(ctx, &pb.PushToUsersReq{
		UidList:      userIDList,
		Notification: notification,
	})

	return r, protocol.ToServerError(err)
}

func (c *Client) PushToPresenceList(ctx context.Context, presenceList []*pb.Presence, notification *pb.CompositiveNotification) (*pb.PushToUsersResp, protocol.ServerError) {
	r, err := c.typedStub().UnaryPushToPresenceList(ctx, &pb.PushToPresenceListReq{
		PresenceList: presenceList,
		Notification: notification,
	})
	return r, protocol.ToServerError(err)
}

// PushMulticast pushes notifications to all users subscribing the specific `multicast account`
func (c *Client) PushMulticast(ctx context.Context, multicastID uint64, multicastAccount string, skipUserIDList []uint32, notification *pb.CompositiveNotification) protocol.ServerError {
	_, err := c.typedStub().UnaryPushMulticast(ctx, &pb.PushMulticastReq{
		MulticastAccount: &pb.MulticastAccount{Id: multicastID, Account: multicastAccount},
		Notification:     notification,
		SkipUids:         skipUserIDList,
	})

	return protocol.ToServerError(err)
}

// PushMulticasts pushes notifications to all users subscribing the specific `multicast account`
func (c *Client) PushMulticasts(ctx context.Context, multicastMap map[uint64]string, skipUserIDList []uint32, notification *pb.CompositiveNotification) protocol.ServerError {
	multicastAccounts := make([]*pb.MulticastAccount, 0, len(multicastMap))
	for multicastID, multicastAccount := range multicastMap {
		multicastAccounts = append(multicastAccounts, &pb.MulticastAccount{Id: multicastID, Account: multicastAccount})
	}
	_, err := c.typedStub().UnaryPushMulticast(ctx, &pb.PushMulticastReq{
		Notification:      notification,
		SkipUids:          skipUserIDList,
		MulticastAccounts: multicastAccounts,
	})
	return protocol.ToServerError(err)
}

func (c *Client) PushMulticasts2(ctx context.Context, multicastMap map[uint64]string, skipUserIDList []uint32, notification *pb.CompositiveNotification) (*pb.PushMulticastResp, protocol.ServerError) {
	multicastAccounts := make([]*pb.MulticastAccount, 0, len(multicastMap))
	for multicastID, multicastAccount := range multicastMap {
		multicastAccounts = append(multicastAccounts, &pb.MulticastAccount{Id: multicastID, Account: multicastAccount})
	}
	rsp, err := c.typedStub().UnaryPushMulticast(ctx, &pb.PushMulticastReq{
		Notification:      notification,
		SkipUids:          skipUserIDList,
		MulticastAccounts: multicastAccounts,
	})
	return rsp, protocol.ToServerError(err)
}

func (c *Client) PushMulticastSync(ctx context.Context, multicastID uint64, multicastAccount string, skipUserIDList []uint32, notification *pb.CompositiveNotification) protocol.ServerError {
	_, err := c.typedStub().UnaryPushMulticast(ctx, &pb.PushMulticastReq{
		MulticastAccount: &pb.MulticastAccount{Id: multicastID, Account: multicastAccount},
		Notification:     notification,
		SkipUids:         skipUserIDList,
		Sync:             true,
	})
	return protocol.ToServerError(err)
}

// GetReliableProxyNotifications ...
func (c *Client) GetReliableProxyNotifications(ctx context.Context, uid, seqBegin, seqEnd, count uint32) ([]*pb.ReliableProxyNotification, protocol.ServerError) {
	r, err := c.typedStub().GetReliableProxyNotifications(ctx, &pb.GetReliableProxyNotificationsReq{
		Uid: uid, SequenceBegin: seqBegin, SequenceEnd: seqEnd, Count: count,
	})
	return r.GetNotifications(), protocol.ToServerError(err)
}

// MessageReceivedAck ...
func (c *Client) MessageReceivedAck(ctx context.Context, uid uint32, seqList []uint32) protocol.ServerError {
	_, err := c.typedStub().MessageReceivedAck(ctx, &pb.MessageReceivedAckReq{
		Uid:          uid,
		SequenceList: seqList,
	})
	return protocol.ToServerError(err)
}

func (c *Client) PushRelationRegisterEvent(ctx context.Context, event *pb.RelationRegisterEvent, opts ...grpc.CallOption) (*pb.PushRelationEventResult, error) {
	resp, err := c.typedStub().PushRelationRegisterEvent(ctx, &pb.PushRelationRegisterEventReq{Event: event}, opts...)
	return resp.GetResult(), err
}

func (c *Client) BatchPushRelationRegisterEvents(ctx context.Context, events []*pb.RelationRegisterEvent, opts ...grpc.CallOption) (map[uint32]*pb.PushRelationEventResult, error) {
	resp, err := c.typedStub().BatchPushRelationRegisterEvents(ctx, &pb.BatchPushRelationRegisterEventReq{Events: events}, opts...)
	return resp.GetResults(), err
}

// Deprecated: use HandlePushMsgWithLabel instead
func (c *Client) HandlePushMsg(uid uint32, uidList []uint32, cmdType gaPush.PushMessage_CMD_TYPE, pushInfo []byte) error {
	pushMessage := &gaPush.PushMessage{
		Cmd:     uint32(cmdType),
		Content: pushInfo,
	}
	pushMessageBytes, _ := pushMessage.Marshal()

	err := c.PushToUsers(context.Background(), uidList, &pb.CompositiveNotification{
		Sequence:           uid,
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pb.ProxyNotification{
			Type:      uint32(pb.ProxyNotification_PUSH),
			Payload:   pushMessageBytes,
			PushLabel: fmt.Sprintf("CMD_%s", cmdType.String()),
		},
	})
	if err != nil {
		log.Errorf("Failed to PushToUsers  %s", err.Error())
		return err
	}

	return nil
}

func (c *Client) HandlePushMsgWithLabel(uid uint32, uidList []uint32, cmdType gaPush.PushMessage_CMD_TYPE, pushInfo []byte, pushLabel string) error {
	pushMessage := &gaPush.PushMessage{
		Cmd:     uint32(cmdType),
		Content: pushInfo,
	}
	pushMessageBytes, _ := pushMessage.Marshal()

	err := c.PushToUsers(context.Background(), uidList, &pb.CompositiveNotification{
		Sequence:           uid,
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pb.ProxyNotification{
			Type:      uint32(pb.ProxyNotification_PUSH),
			Payload:   pushMessageBytes,
			PushLabel: pushLabel, // 标识不同业务场景，要求一定要带上，用于统计和控制各业务场景
		},
	})
	if err != nil {
		log.Errorf("Failed to PushToUsers  %s", err.Error())
		return err
	}

	return nil
}

// Deprecated: use HandlePushMsgV2WithLabel instead
func (c *Client) HandlePushMsgV2(ctx context.Context, uid uint32, uidList []uint32, cmdType gaPush.PushMessage_CMD_TYPE, pushInfo []byte) error {
	pushMessage := &gaPush.PushMessage{
		Cmd:     uint32(cmdType),
		Content: pushInfo,
	}
	pushMessageBytes, _ := pushMessage.Marshal()

	err := c.PushToUsers(ctx, uidList, &pb.CompositiveNotification{
		Sequence:           uid,
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pb.ProxyNotification{
			Type:      uint32(pb.ProxyNotification_PUSH),
			Payload:   pushMessageBytes,
			PushLabel: fmt.Sprintf("CMD_%s", cmdType.String()),
		},
	})
	if err != nil {
		log.Errorf("Failed to PushToUsers  %s", err.Error())
		return err
	}

	return nil
}

func (c *Client) HandlePushMsgWithLabelV2(ctx context.Context, uid uint32, uidList []uint32, cmdType gaPush.PushMessage_CMD_TYPE, pushInfo []byte, pushLabel string) error {
	pushMessage := &gaPush.PushMessage{
		Cmd:     uint32(cmdType),
		Content: pushInfo,
	}
	pushMessageBytes, _ := pushMessage.Marshal()

	err := c.PushToUsers(ctx, uidList, &pb.CompositiveNotification{
		Sequence:           uid,
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pb.ProxyNotification{
			Type:      uint32(pb.ProxyNotification_PUSH),
			Payload:   pushMessageBytes,
			PushLabel: pushLabel, // 标识不同业务场景，要求一定要带上，用于统计和控制各业务场景
		},
	})
	if err != nil {
		log.Errorf("Failed to PushToUsers  %s", err.Error())
		return err
	}

	return nil
}

func (c *Client) UnaryPushToUsersWithReqId(ctx context.Context, in *pb.PushToUsersReq) (string, protocol.ServerError) {
	resp, err := c.typedStub().UnaryPushToUsers(ctx, in)
	return resp.GetRequestId(), protocol.ToServerError(err)
}

func (c *Client) UnaryPushToUserMapWithReqId(ctx context.Context, in *pb.PushToUserMapReq) (string, protocol.ServerError) {
	resp, err := c.typedStub().UnaryPushToUserMap(ctx, in)
	return resp.GetRequestId(), protocol.ToServerError(err)
}

func (c *Client) UnaryPushMulticastWithReqId(ctx context.Context, in *pb.PushMulticastReq) (string, protocol.ServerError) {
	resp, err := c.typedStub().UnaryPushMulticast(ctx, in)
	return resp.GetRequestId(), protocol.ToServerError(err)
}

func (c *Client) UnaryPushToPresenceListWithReqId(ctx context.Context, in *pb.PushToPresenceListReq) (string, protocol.ServerError) {
	resp, err := c.typedStub().UnaryPushToPresenceList(ctx, in)
	return resp.GetRequestId(), protocol.ToServerError(err)
}
