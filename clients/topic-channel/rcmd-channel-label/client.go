package rcmd_channel_label

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	"google.golang.org/grpc"
)

const (
	serviceName = "rcmd-channel-label"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewRCMDChannelLabelClient(cc)
			},
			dopts...,
		),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.RCMDChannelLabelClient {
	return c.Stub().(pb.RCMDChannelLabelClient)
}

func (c *Client) LabelSearch(ctx context.Context, in *pb.LabelSearchReq) (*pb.LabelSearchResp, error) {
	resp, err := c.typedStub().LabelSearch(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGameLabels(ctx context.Context, in *pb.GetGameLabelsReq) (*pb.GetGameLabelsResp, error) {
	resp, err := c.typedStub().GetGameLabels(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) CutWord(ctx context.Context, in *pb.CutWordReq) (*pb.CutWordResp, error) {
	resp, err := c.typedStub().CutWord(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSearchHint(ctx context.Context, in *pb.GetSearchHintReq) (*pb.GetSearchHintResp, error) {
	resp, err := c.typedStub().GetSearchHint(ctx, in)
	return resp, protocol.ToServerError(err)
}

// 玩法标签映射
func (c *Client) ConvertGameLabels(ctx context.Context, in *pb.ConvertGameLabelsReq) (*pb.ConvertGameLabelsResp, error) {
	resp, err := c.typedStub().ConvertGameLabels(ctx, in)
	return resp, protocol.ToServerError(err)
}

// 批量获取热门玩法
func (c *Client) BatchHotGameLabels(ctx context.Context, in *pb.BatchHotGameLabelsReq) (*pb.BatchHotGameLabelsResp, error) {
	resp, err := c.typedStub().BatchHotGameLabels(ctx, in)
	return resp, protocol.ToServerError(err)
}

// 获取游戏发布条件玩法
func (c *Client) GetPublishGameLabels(ctx context.Context, in *pb.GetPublishGameLabelsReq) (*pb.GetPublishGameLabelsResp, error) {
	resp, err := c.typedStub().GetPublishGameLabels(ctx, in)
	return resp, protocol.ToServerError(err)
}

// 游戏名称搜索
func (c *Client) SearchGameName(ctx context.Context, in *pb.SearchGameNameReq) (*pb.SearchGameNameResp, error) {
	resp, err := c.typedStub().SearchGameName(ctx, in)
	return resp, protocol.ToServerError(err)
}

// 获取房间名称匹配的关联发布条件的标签
func (c *Client) GetRelatedPublishLabels(ctx context.Context, in *pb.GetRelatedPublishLabelsReq) (*pb.GetRelatedPublishLabelsResp, error) {
	resp, err := c.typedStub().GetRelatedPublishLabels(ctx, in)
	return resp, protocol.ToServerError(err)
}

// 更新标签缓存
func (c *Client) RefreshGameLabel(ctx context.Context, in *pb.RefreshGameLabelReq) (*pb.RefreshGameLabelResp, error) {
	resp, err := c.typedStub().RefreshGameLabel(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetFilterBlockOptionList(ctx context.Context, in *pb.GetFilterBlockOptionListReq) (*pb.GetFilterBlockOptionListResp, error) {
	resp, err := c.typedStub().GetFilterBlockOptionList(ctx, in)
	return resp, protocol.ToServerError(err)
}