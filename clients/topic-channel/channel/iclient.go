// Code generated by quicksilver-cli. DO NOT EDIT.
package channel

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	channel "golang.52tt.com/protocol/services/topic_channel/channel"
	context "context"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	AddChannel(ctx context.Context, in *channel.AddChannelReq) (*channel.AddChannelResp,protocol.ServerError)
	AddTemporaryChannel(ctx context.Context, in *channel.AddTemporaryChannelReq) (*channel.AddTemporaryChannelResp,protocol.ServerError)
	DisappearChannel(ctx context.Context, in *channel.DisappearChannelReq) (*channel.DisappearChannelResp,protocol.ServerError)
	DismissChannel(ctx context.Context, in *channel.DismissChannelReq) (*channel.DismissChannelResp,protocol.ServerError)
	DismissTab(ctx context.Context, in *channel.DismissTabReq) (*channel.DismissTabResp,protocol.ServerError)
	FreezeChannel(ctx context.Context, in *channel.FreezeChannelReq) (*channel.FreezeChannelResp,protocol.ServerError)
	GetChannelByIds(ctx context.Context, in *channel.GetChannelByIdsReq) (*channel.GetChannelByIdsResp,protocol.ServerError)
	GetChannelFreezeInfo(ctx context.Context, in *channel.GetChannelFreezeInfoReq) (*channel.GetChannelFreezeInfoResp,protocol.ServerError)
	GetChannelPlayModel(ctx context.Context, in *channel.GetChannelPlayModelReq) (*channel.GetChannelPlayModelResp,protocol.ServerError)
	GetChannelRoomUserNumber(ctx context.Context, in *channel.GetChannelRoomUserNumberReq) (*channel.GetChannelRoomUserNumberResp,protocol.ServerError)
	GetExtraHistory(ctx context.Context, in *channel.GetExtraHistoryReq) (*channel.GetExtraHistoryResp,protocol.ServerError)
	GetLastEnterRoomTabIdByUid(ctx context.Context, in *channel.GetLastEnterRoomTabIdByUidReq) (*channel.GetLastEnterRoomTabIdByUidResp,protocol.ServerError)
	GetLastEnterRoomTimeByUid(ctx context.Context, in *channel.GetLastEnterRoomTimeByUidReq) (*channel.GetLastEnterRoomTimeByUidResp,protocol.ServerError)
	GetOnlineInfo(ctx context.Context, in *channel.GetOnlineInfoReq) (*channel.GetOnlineInfoResp,protocol.ServerError)
	GetRecommendChannelList(ctx context.Context, in *channel.GetRecommendChannelListReq) (*channel.GetRecommendChannelListResp,protocol.ServerError)
	GetRecommendChannelListByTab(ctx context.Context, in *channel.GetRecommendChannelListByTabReq) (*channel.GetRecommendChannelListByTabResp,protocol.ServerError)
	KeepChannelAlive(ctx context.Context, in *channel.KeepChannelAliveReq) (*channel.KeepChannelAliveResp,protocol.ServerError)
	SetExtraHistory(ctx context.Context, in *channel.SetExtraHistoryReq) (*channel.SetExtraHistoryResp,protocol.ServerError)
	SwitchChannelTab(ctx context.Context, in *channel.SwitchChannelTabReq) (*channel.SwitchChannelTabResp,protocol.ServerError)
	SwitchChannelTabMq(ctx context.Context, in *channel.SwitchChannelTabMqReq) (*channel.SwitchChannelTabMqResp,protocol.ServerError)
	UnfreezeChannel(ctx context.Context, in *channel.UnfreezeChannelReq) (*channel.UnfreezeChannelResp,protocol.ServerError)
	UpdateChannelInfo(ctx context.Context, in *channel.UpdateChannelInfoReq) (*channel.UpdateChannelInfoResp,protocol.ServerError)
	UpdateLastEnterRoomTabIdByUid(ctx context.Context, in *channel.UpdateLastEnterRoomTabIdByUidReq) (*channel.UpdateLastEnterRoomTabIdByUidResp,protocol.ServerError)
	UpdateLastEnterRoomTimeByUid(ctx context.Context, in *channel.UpdateLastEnterRoomTimeByUidReq) (*channel.UpdateLastEnterRoomTimeByUidResp,protocol.ServerError)
	UpdateTopicChannelInfo(ctx context.Context, in *channel.UpdateTopicChannelInfoReq) (*channel.UpdateTopicChannelInfoResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
