// Code generated by quicksilver-cli. DO NOT EDIT.
package tab

import (
	context "context"
	"golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/topic_channel/tab"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	AddCategoryTitle(ctx context.Context, in *pb.AddCategoryTitleReq) (*pb.AddCategoryTitleResp, protocol.ServerError)
	AddMultilevelTitle(ctx context.Context, in *pb.AddMultilevelTitleReq) (*pb.AddMultilevelTitleResp, error)
	BatchGetBlockRelations(ctx context.Context, in *pb.BatchGetBlockRelationsReq) (*pb.BatchGetBlockRelationsResp, protocol.ServerError)
	BatchGetBlocks(ctx context.Context, in *pb.BatchGetBlocksReq) (*pb.BatchGetBlocksResp, protocol.ServerError)
	BatchGetBusinessBlockInfo(ctx context.Context, in *pb.BatchGetBusinessBlockInfoReq) (out *pb.BatchGetBusinessBlockInfoResp, err error)
	BatchGetGameLabelItems(ctx context.Context, in *pb.BatchGetGameLabelItemsReq) (*pb.BatchGetGameLabelItemsResp, error)
	BatchGetOfficialRoomNameConfig(ctx context.Context, in *pb.BatchGetOfficialRoomNameConfigReq) (out *pb.BatchGetOfficialRoomNameConfigResp, err error)
	BatchGetShieldSwitchByTabIds(ctx context.Context, req *pb.BatchGetShieldSwitchByTabIdsReq) (*pb.BatchGetShieldSwitchByTabIdsResp, protocol.ServerError)
	Blocks(ctx context.Context, in *pb.BlocksReq) (*pb.BlocksResp, protocol.ServerError)
	DeleteCategoryTitle(ctx context.Context, in *pb.DeleteCategoryTitleReq) (*pb.DeleteCategoryTitleResp, protocol.ServerError)
	DeleteMultilevelTitle(ctx context.Context, in *pb.DeleteMultilevelTitleReq) (*pb.DeleteMultilevelTitleResp, error)
	DeleteTab(ctx context.Context, id uint32) (bool, protocol.ServerError)
	FiniteTabs(ctx context.Context, in *pb.FiniteTabsReq) (*pb.FiniteTabsResp, protocol.ServerError)
	FiniteTabsByTags(ctx context.Context, in *pb.FiniteTabsByTagsReq) (*pb.FiniteTabsByTagsResp, protocol.ServerError)
	GetAllTabInfoExt(ctx context.Context, req *pb.GetAllTabInfoExtReq) (*pb.GetAllTabInfoExtResp, protocol.ServerError)
	GetAllTabOfCategory(ctx context.Context, in *pb.GetAllTabOfCategoryReq) (*pb.GetAllTabOfCategoryResp, protocol.ServerError)
	GetCache(ctx context.Context, req *pb.GetCacheReq) (*pb.GetCacheResp, protocol.ServerError)
	GetCategoryTitleList(ctx context.Context, in *pb.GetCategoryTitleReq) (*pb.GetCategoryTitleResp, protocol.ServerError)
	GetHomePageHeadConfig(ctx context.Context, in *pb.HomePageHeadConfigReq) (*pb.HomePageHeadConfigResp, error)
	GetMinorityGameTabs(ctx context.Context, in *pb.GetMinorityGameTabsReq) (out *pb.GetMinorityGameTabsResp, err error)
	GetMultilevelTitleForTT(ctx context.Context, in *pb.GetMultilevelTitleForTTReq) (*pb.GetMultilevelTitleForTTResp, error)
	GetNewQuickMatchConfig(ctx context.Context, in *pb.GetNewQuickMatchConfigReq) (*pb.GetNewQuickMatchConfigResp, protocol.ServerError)
	GetQuickMatchConfig(ctx context.Context, req *pb.GetQuickMatchConfigReq) (*pb.GetQuickMatchConfigResp, protocol.ServerError)
	GetRelationOfEtChannel(ctx context.Context) (*pb.GetRelationOfEtChannelResp, protocol.ServerError)
	GetRoomNameConfigure(ctx context.Context, in *pb.GetRoomNameConfigureReq) (*pb.GetRoomNameConfigureResp, protocol.ServerError)
	GetSimpleBanner(ctx context.Context, in *pb.GetSimpleBannerReq) (*pb.GetSimpleBannerResp, protocol.ServerError)
	GetTabById(ctx context.Context, tabId uint32) (*pb.Tab, protocol.ServerError)
	GetTabByName(ctx context.Context, in *pb.GetTabByNameReq) (*pb.GetTabByNameResp, protocol.ServerError)
	GetTabByUGameId(ctx context.Context, in *pb.GetTabByUGameIdReq) (*pb.GetTabByUGameIdResp, protocol.ServerError)
	GetTabsByCategoryEnum(ctx context.Context, req *pb.GetTabsByCategoryEnumReq) (*pb.GetTabsByCategoryEnumResp, protocol.ServerError)
	GetTabsByCategoryIds(ctx context.Context, in *pb.GetTabsByCategoryIdsReq) (out *pb.GetTabsByCategoryIdsResp, err error)
	GetTabsByIds(ctx context.Context, tabIds []uint32) (map[uint32]*pb.Tab, protocol.ServerError)
	InsertBlock(ctx context.Context, in *pb.InsertBlockReq) (*pb.InsertBlockResp, protocol.ServerError)
	InsertRelationOfEtChannel(ctx context.Context, in *pb.InsertRelationOfEtChannelReq) (*pb.InsertRelationOfEtChannelResp, protocol.ServerError)
	ListReleaseCondition(ctx context.Context, in *pb.ListReleaseConditionReq) (out *pb.ListReleaseConditionResp, err error)
	ReSortCategoryTitle(ctx context.Context, in *pb.ReSortCategoryTitleReq) (*pb.ReSortCategoryTitleResp, protocol.ServerError)
	RearrangeTabs(ctx context.Context, in *pb.RearrangeTabsReq) (bool, protocol.ServerError)
	SearchTabMap(ctx context.Context, in *pb.SearchTabsReq) (map[uint32]*pb.Tab, error)
	SearchTabs(ctx context.Context, in *pb.SearchTabsReq) (*pb.SearchTabsResp, error)
	Tabs(ctx context.Context, skip uint32, limit uint32) (*pb.TabsResp, protocol.ServerError)
	TabsForTT(ctx context.Context, skip uint32, limit uint32) (*pb.TabsForTTResp, protocol.ServerError)
	UpdateBlock(ctx context.Context, in *pb.UpdateBlockReq) (*pb.UpdateBlockResp, protocol.ServerError)
	UpdateCategoryTitle(ctx context.Context, in *pb.UpdateCategoryTitleReq) (*pb.UpdateCategoryTitleResp, protocol.ServerError)
	UpdateMultilevelTitle(ctx context.Context, in *pb.UpdateMultilevelTitleReq) (*pb.UpdateMultilevelTitleResp, error)
	UpdateQuickMatchConfig(ctx context.Context, req *pb.UpdateQuickMatchConfigReq) (*pb.UpdateQuickMatchConfigResp, protocol.ServerError)
	UpdateTabMiniGameId(ctx context.Context, in *pb.UpdateTabMiniGameIdReq) (*pb.UpdateTabMiniGameIdResp, protocol.ServerError)
	UpdateTabUGameId(ctx context.Context, in *pb.UpdateTabUGameIdReq) (*pb.UpdateTabUGameIdResp, protocol.ServerError)
	GetTabQuestionConfig(ctx context.Context, req *pb.GetTabQuestionConfigReq) (*pb.GetTabQuestionConfigResp, protocol.ServerError)
	FindFilterMixTabIds(ctx context.Context, req *pb.FindFilterMixTabIdsReq) (*pb.FindFilterMixTabIdsResp, protocol.ServerError)
	FindFilterIdsByMixTabIds(ctx context.Context, req *pb.FindFilterIdsByMixTabIdsReq) (*pb.FindFilterIdsByMixTabIdsResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
