package tab

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"

	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	pb "golang.52tt.com/protocol/services/topic_channel/tab"
	"google.golang.org/grpc"
)

const (
	serviceName = "topic-channel-tab"
	// serviceName = "topic-channel-tab-local"
)

// Client 是用于实现tab客户端的类型。
type Client struct {
	client.BaseClient
}

// newClient 用于创建tab客户端。
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewTopicChannelTabClient(cc)
			},
			dopts...,
		),
	}, nil
}

// NewTracedClient 用于创建一个包含Tracer的tab客户端，它是一个包装函数。
func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

// NewClient 用于创建tab客户端，它是一个包装函数。
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

// typedStub
func (c *Client) typedStub() pb.TopicChannelTabClient {
	return c.Stub().(pb.TopicChannelTabClient)
}

// DeleteTab 用于删除某个类型。
func (c *Client) DeleteTab(ctx context.Context, id uint32) (bool, protocol.ServerError) {

	in := &pb.DeleteTabReq{
		Tab: &pb.Tab{
			Id: id,
		},
	}

	resp, err := c.typedStub().DeleteTab(ctx, in)

	return resp.GetResult(), protocol.ToServerError(err)
}

// FiniteTabs 用于获取指定的类型。
func (c *Client) FiniteTabs(ctx context.Context, in *pb.FiniteTabsReq) (*pb.FiniteTabsResp, protocol.ServerError) {

	out := &pb.FiniteTabsResp{}

	resp, err := c.typedStub().FiniteTabs(ctx, in)

	out.Tabs = resp.GetTabs()

	return out, protocol.ToServerError(err)
}

func (c *Client) GetTabsByIds(ctx context.Context, tabIds []uint32) (map[uint32]*pb.Tab, protocol.ServerError) {
	tabMap := make(map[uint32]*pb.Tab)

	tabInfos := make([]*pb.Tab, 0, len(tabIds))
	for _, tabId := range tabIds {
		tabInfos = append(tabInfos, &pb.Tab{
			Id: tabId,
		})
	}
	in := &pb.FiniteTabsReq{
		Tabs: tabInfos,
	}

	resp, err := c.typedStub().FiniteTabs(ctx, in)

	for _, tab := range resp.GetTabs() {
		tabMap[tab.Id] = tab
	}

	return tabMap, protocol.ToServerError(err)
}

func (c *Client) GetTabById(ctx context.Context, tabId uint32) (*pb.Tab, protocol.ServerError) {
	tab := &pb.Tab{}

	resp, err := c.typedStub().FiniteTabs(ctx, &pb.FiniteTabsReq{Tabs: []*pb.Tab{{Id: tabId}}})

	if len(resp.GetTabs()) == 0 {
		return nil, protocol.ToServerError(err)
	} else {
		tab = resp.GetTabs()[0]
		return tab, protocol.ToServerError(err)
	}
}

// FiniteTabsByTags 通过指定tagid获取相应的类型。 只能是游戏卡id
func (c *Client) FiniteTabsByTags(ctx context.Context, in *pb.FiniteTabsByTagsReq) (
	*pb.FiniteTabsByTagsResp, protocol.ServerError) {

	out := &pb.FiniteTabsByTagsResp{}

	resp, err := c.typedStub().FiniteTabsByTags(ctx, in)

	out.Tabs = resp.GetTabs()

	return out, protocol.ToServerError(err)
}

// Tabs 用于获取所有的类型。
func (c *Client) Tabs(ctx context.Context, skip uint32, limit uint32) (*pb.TabsResp, protocol.ServerError) {

	in := &pb.TabsReq{
		Skip:  skip,
		Limit: limit,
	}

	resp, err := c.typedStub().Tabs(ctx, in)

	return resp, protocol.ToServerError(err)
}

// RearrangeTabs 用于重新排序所有的类型。
func (c *Client) RearrangeTabs(ctx context.Context, in *pb.RearrangeTabsReq) (bool, protocol.ServerError) {

	resp, err := c.typedStub().RearrangeTabs(ctx, in)

	return resp.GetResult(), protocol.ToServerError(err)
}

// TabsForTT 用于获取所有的类型。尽量使用tabs接口
func (c *Client) TabsForTT(ctx context.Context, skip uint32, limit uint32) (*pb.TabsForTTResp, protocol.ServerError) {

	in := &pb.TabsForTTReq{
		Skip:  skip,
		Limit: limit,
	}

	resp, err := c.typedStub().TabsForTT(ctx, in)

	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRoomNameConfigure(ctx context.Context, in *pb.GetRoomNameConfigureReq) (*pb.GetRoomNameConfigureResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRoomNameConfigure(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSimpleBanner(ctx context.Context, in *pb.GetSimpleBannerReq) (*pb.GetSimpleBannerResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSimpleBanner(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetBlocks(ctx context.Context, in *pb.BatchGetBlocksReq) (*pb.BatchGetBlocksResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetBlocks(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) Blocks(ctx context.Context, in *pb.BlocksReq) (*pb.BlocksResp, protocol.ServerError) {
	resp, err := c.typedStub().Blocks(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAllTabOfCategory(ctx context.Context, in *pb.GetAllTabOfCategoryReq) (*pb.GetAllTabOfCategoryResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAllTabOfCategory(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) InsertBlock(ctx context.Context, in *pb.InsertBlockReq) (*pb.InsertBlockResp, protocol.ServerError) {
	resp, err := c.typedStub().InsertBlock(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateBlock(ctx context.Context, in *pb.UpdateBlockReq) (*pb.UpdateBlockResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateBlock(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddCategoryTitle(ctx context.Context, in *pb.AddCategoryTitleReq) (*pb.AddCategoryTitleResp, protocol.ServerError) {
	resp, err := c.typedStub().AddCategoryTitle(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateCategoryTitle(ctx context.Context, in *pb.UpdateCategoryTitleReq) (*pb.UpdateCategoryTitleResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateCategoryTitle(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetCategoryTitleList(ctx context.Context, in *pb.GetCategoryTitleReq) (*pb.GetCategoryTitleResp, protocol.ServerError) {
	resp, err := c.typedStub().GetCategoryTitleList(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ReSortCategoryTitle(ctx context.Context, in *pb.ReSortCategoryTitleReq) (*pb.ReSortCategoryTitleResp, protocol.ServerError) {
	resp, err := c.typedStub().ReSortCategoryTitle(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DeleteCategoryTitle(ctx context.Context, in *pb.DeleteCategoryTitleReq) (*pb.DeleteCategoryTitleResp, protocol.ServerError) {
	resp, err := c.typedStub().DeleteCategoryTitle(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) InsertRelationOfEtChannel(ctx context.Context, in *pb.InsertRelationOfEtChannelReq) (*pb.InsertRelationOfEtChannelResp, protocol.ServerError) {
	resp, err := c.typedStub().InsertRelationOfEtChannel(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRelationOfEtChannel(ctx context.Context) (*pb.GetRelationOfEtChannelResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRelationOfEtChannel(ctx, &pb.GetRelationOfEtChannelReq{})
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddMultilevelTitle(ctx context.Context, in *pb.AddMultilevelTitleReq) (*pb.AddMultilevelTitleResp, error) {
	resp, err := c.typedStub().AddMultilevelTitle(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateMultilevelTitle(ctx context.Context, in *pb.UpdateMultilevelTitleReq) (*pb.UpdateMultilevelTitleResp, error) {
	resp, err := c.typedStub().UpdateMultilevelTitle(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetMultilevelTitleForTT(ctx context.Context, in *pb.GetMultilevelTitleForTTReq) (*pb.GetMultilevelTitleForTTResp, error) {
	resp, err := c.typedStub().GetMultilevelTitleForTT(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DeleteMultilevelTitle(ctx context.Context, in *pb.DeleteMultilevelTitleReq) (*pb.DeleteMultilevelTitleResp, error) {
	resp, err := c.typedStub().DeleteMultilevelTitle(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SearchTabs(ctx context.Context, in *pb.SearchTabsReq) (*pb.SearchTabsResp, error) {
	resp, err := c.typedStub().SearchTabs(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetMinorityGameTabs(ctx context.Context, in *pb.GetMinorityGameTabsReq) (out *pb.GetMinorityGameTabsResp, err error) {
	resp, err := c.typedStub().GetMinorityGameTabs(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetTabsByCategoryIds(ctx context.Context, in *pb.GetTabsByCategoryIdsReq) (out *pb.GetTabsByCategoryIdsResp, err error) {
	resp, err := c.typedStub().GetTabsByCategoryIds(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListReleaseCondition(ctx context.Context, in *pb.ListReleaseConditionReq) (out *pb.ListReleaseConditionResp, err error) {
	resp, err := c.typedStub().ListReleaseCondition(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SearchTabMap(ctx context.Context, in *pb.SearchTabsReq) (map[uint32]*pb.Tab, error) {
	resp, err := c.typedStub().SearchTabs(ctx, in)
	if err != nil {
		return nil, err
	}
	tabMap := make(map[uint32]*pb.Tab)
	for _, t := range resp.Tabs {
		tabMap[t.Id] = t
	}
	return tabMap, protocol.ToServerError(err)
}

func (c *Client) BatchGetBusinessBlockInfo(ctx context.Context, in *pb.BatchGetBusinessBlockInfoReq) (out *pb.BatchGetBusinessBlockInfoResp, err error) {
	resp, err := c.typedStub().BatchGetBusinessBlockInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetOfficialRoomNameConfig(ctx context.Context, in *pb.BatchGetOfficialRoomNameConfigReq) (out *pb.BatchGetOfficialRoomNameConfigResp, err error) {
	resp, err := c.typedStub().BatchGetOfficialRoomNameConfig(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetHomePageHeadConfig(ctx context.Context, in *pb.HomePageHeadConfigReq) (*pb.HomePageHeadConfigResp, error) {
	resp, err := c.typedStub().HomePageHeadConfig(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetGameLabelItems(ctx context.Context, in *pb.BatchGetGameLabelItemsReq) (*pb.BatchGetGameLabelItemsResp, error) {
	resp, err := c.typedStub().BatchGetGameLabelItems(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateTabUGameId(ctx context.Context, in *pb.UpdateTabUGameIdReq) (*pb.UpdateTabUGameIdResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateTabUGameId(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetTabByUGameId(ctx context.Context, in *pb.GetTabByUGameIdReq) (*pb.GetTabByUGameIdResp, protocol.ServerError) {
	resp, err := c.typedStub().GetTabByUGameId(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetTabByName(ctx context.Context, in *pb.GetTabByNameReq) (*pb.GetTabByNameResp, protocol.ServerError) {
	resp, err := c.typedStub().GetTabByName(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateTabMiniGameId(ctx context.Context, in *pb.UpdateTabMiniGameIdReq) (*pb.UpdateTabMiniGameIdResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateTabMiniGameId(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetBlockRelations(ctx context.Context, in *pb.BatchGetBlockRelationsReq) (*pb.BatchGetBlockRelationsResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetBlockRelations(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateQuickMatchConfig(ctx context.Context, req *pb.UpdateQuickMatchConfigReq) (*pb.UpdateQuickMatchConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateQuickMatchConfig(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetQuickMatchConfig(ctx context.Context, req *pb.GetQuickMatchConfigReq) (*pb.GetQuickMatchConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().GetQuickMatchConfig(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetNewQuickMatchConfig(ctx context.Context, in *pb.GetNewQuickMatchConfigReq) (*pb.GetNewQuickMatchConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().GetNewQuickMatchConfig(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAllTabInfoExt(ctx context.Context, req *pb.GetAllTabInfoExtReq) (*pb.GetAllTabInfoExtResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAllTabInfoExt(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetCache(ctx context.Context, req *pb.GetCacheReq) (*pb.GetCacheResp, protocol.ServerError) {
	resp, err := c.typedStub().GetCache(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetShieldSwitchByTabIds(ctx context.Context, req *pb.BatchGetShieldSwitchByTabIdsReq) (
	*pb.BatchGetShieldSwitchByTabIdsResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetShieldSwitchByTabIds(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetTabsByCategoryEnum(ctx context.Context, req *pb.GetTabsByCategoryEnumReq) (
	*pb.GetTabsByCategoryEnumResp, protocol.ServerError) {
	resp, err := c.typedStub().GetTabsByCategoryEnum(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetTabQuestionConfig(ctx context.Context, req *pb.GetTabQuestionConfigReq) (
	*pb.GetTabQuestionConfigResp, protocol.ServerError) {
	resp, err := c.typedStub().GetTabQuestionConfig(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) FindFilterMixTabIds(ctx context.Context, req *pb.FindFilterMixTabIdsReq) (
	*pb.FindFilterMixTabIdsResp, protocol.ServerError) {
	resp, err := c.typedStub().FindFilterMixTabIds(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) FindFilterIdsByMixTabIds(ctx context.Context, req *pb.FindFilterIdsByMixTabIdsReq) (
	*pb.FindFilterIdsByMixTabIdsResp, protocol.ServerError) {
	resp, err := c.typedStub().FindFilterIdsByMixTabIds(ctx, req)
	return resp, protocol.ToServerError(err)
}
