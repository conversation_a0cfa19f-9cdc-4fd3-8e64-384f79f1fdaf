package recommendation_mini_game_gen

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	mini_game "golang.52tt.com/protocol/services/topic_channel/recommendation_mini_game_gen"
	"google.golang.org/grpc"
)

const (
	serviceName = "topic-channel-recommendation-mini-game-gen"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return mini_game.NewRecommendationMiniGameGenClient(cc)
			},
			dopts...,
		),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func (c *Client) typedStub() mini_game.RecommendationMiniGameGenClient {
	return c.Stub().(mini_game.RecommendationMiniGameGenClient)
}

func (c *Client) GetQuickMatchChannel(ctx context.Context, in *mini_game.GetQuickMatchChannelReq) (*mini_game.GetQuickMatchChannelResp, error) {
	resp, err := c.typedStub().GetQuickMatchChannel(ctx, in)
	return resp, protocol.ToServerError(err)
}
