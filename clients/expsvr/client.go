package expsvr

import (
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/log"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"
	expPB "golang.52tt.com/protocol/services/expsvr"
)

const (
	serviceName = "expsvr"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return expPB.NewExpSvrClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() expPB.ExpSvrClient { return c.Stub().(expPB.ExpSvrClient) }

func (c *Client) GetUserExp(ctx context.Context, uid uint32) (int32, uint32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetUserExp(ctx, &expPB.GetUserExpReq{
		Uid: uid,
	})
	log.Debugf("GetUserExp uid:%d, %+v, %+v", uid, r, err)
	return r.GetExp(), r.GetLevel(), protocol.ToServerError(err)
}

func (c *Client) GetLevelExpScope(ctx context.Context, uid, lv uint32) (uint32, uint32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetLevelExpScope(ctx, &expPB.GetLevelExpScopeReq{
		Level: lv,
	})
	return r.GetStartExp(), r.GetEndExp(), protocol.ToServerError(err)
}

func (c *Client) AddUserExp(ctx context.Context, uid, exp uint32) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().AddUserExp(ctx, &expPB.AddUserExpReq{
		Exp: int32(exp),
		Uid: uid,
	})
	log.Debugf("GetUserExp uid:%d, exp:%d, %+v", uid, exp, err)
	return protocol.ToServerError(err)
}

func (c *Client) AddUserExpV2(ctx context.Context, uid uint32, in *expPB.AddUserExpReq) (*expPB.AddUserExpResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().AddUserExp(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetUserExpLog(ctx context.Context, uid uint32, in *expPB.GetUserExpLogReq) (*expPB.GetUserExpLogResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetUserExpLog(ctx, in)
	return r, protocol.ToServerError(err)
}

func (c *Client) GetSuperPlayerExpSpeedUpStatus(ctx context.Context, uid uint32) (*expPB.GetSuperPlayerExpSpeedUpStatusResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetSuperPlayerExpSpeedUpStatus(ctx, &expPB.GetSuperPlayerExpSpeedUpStatusReq{Uid: uid})
	return r, protocol.ToServerError(err)
}

func (c *Client) SetSuperPlayerExpSpeedUpStatus(ctx context.Context, uid, status uint32) (*expPB.SetSuperPlayerExpSpeedUpStatusResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().SetSuperPlayerExpSpeedUpStatus(ctx, &expPB.SetSuperPlayerExpSpeedUpStatusReq{
		Uid:           uid,
		SpeedUpStatus: status,
	})
	return r, protocol.ToServerError(err)
}

func (c *Client) BatGetUserExp(ctx context.Context, uid uint32, uidList []uint32) (map[uint32]*expPB.UserExp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().BatGetUserExp(ctx, &expPB.BatGetUserExpReq{
		UidList: uidList,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	userExpMap := make(map[uint32]*expPB.UserExp)
	for _, userExp := range r.GetUserExpList() {
		userExpMap[userExp.GetUid()] = userExp
	}
	return userExpMap, nil
}
