package expsvr

import (
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	expPB "golang.52tt.com/protocol/services/expsvr"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

//go:generate mockgen --destination=../mocks/expsvr/iclient.go --package=expsvr golang.52tt.com/clients/expsvr IClient
type IClient interface {
	client.BaseClient
	GetUserExp(ctx context.Context, uid uint32) (int32, uint32, protocol.ServerError)
	GetLevelExpScope(ctx context.Context, uid, lv uint32) (uint32, uint32, protocol.ServerError)
	AddUserExp(ctx context.Context, uid, exp uint32) protocol.ServerError
	AddUserExpV2(ctx context.Context, uid uint32, in *expPB.AddUserExpReq) (*expPB.AddUserExpResp, protocol.ServerError)
	GetUserExpLog(ctx context.Context, uid uint32, in *expPB.GetUserExpLogReq) (*expPB.GetUserExpLogResp, protocol.ServerError)
	GetSuperPlayerExpSpeedUpStatus(ctx context.Context, uid uint32) (*expPB.GetSuperPlayerExpSpeedUpStatusResp, protocol.ServerError)
	SetSuperPlayerExpSpeedUpStatus(ctx context.Context, uid, status uint32) (*expPB.SetSuperPlayerExpSpeedUpStatusResp, protocol.ServerError)
	BatGetUserExp(ctx context.Context, uid uint32, uidList []uint32) (map[uint32]*expPB.UserExp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
