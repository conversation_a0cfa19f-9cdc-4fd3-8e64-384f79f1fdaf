syntax = "proto3";

package ga.muse_ai_play_hub_logic;

import "ga_base.proto";
import "muse_ai_play_hub_logic/muse_role_play_logic.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/muse-ai-play-hub-logic";

// AI玩法IM消息
message AIPlayHubIMMsg{
  AIPlayHubIMMsgBase base = 1;
  bytes msg_content = 2; // 消息内容
}

message AIPlayHubIMMsgBase{
  uint32 msg_type = 1; // AIPlayHubIMMsgType
  bool is_present_out = 2; // 是否显示在消息列表
}

enum AIPlayHubIMMsgType{
  AI_PLAY_HUB_IM_MSG_TYPE_UNSPECIFIED = 0;
  AI_PLAY_HUB_IM_MSG_TYPE_AI_CUPID = 1; // AI红娘消息 -->AICupidMsg
  AI_PLAY_HUB_IM_MSG_TYPE_AI_CUPID_GUIDE_FOLLOW_MSG = 2; // AI红娘引导关注消息 -->AICupidGuideFollowMsg
  AI_PLAY_HUB_IM_MSG_TYPE_QUIT_MSG = 3; //ai红娘告别消息 -->AICupidMsg
  AI_PLAY_HUB_IM_MSG_TYPE_SYSTEM = 4;// 系统消息 -->AIPlayHubRichTextIMMsg
  AI_PLAY_HUB_IM_MSG_TYPE_AI_CUPID_RECALL = 5; //AI红娘次日引导召回
}

message AIPlayHubRichTextElement{
  oneof content{
      AIPlayHubRichTextWord text = 1; // 文本
      AIPlayHubRichTextImg img = 2; // 图片
  }
}

message AIPlayHubRichTextWord{
  string text = 1; // 文本内容
  uint32 text_type = 2; // 文本类型 AIPlayHubRichTextWordType
}

enum AIPlayHubRichTextWordType{
  AI_PLAY_HUB_RICH_TEXT_WORD_TYPE_UNSPECIFIED = 0;
  AI_PLAY_HUB_RICH_TEXT_WORD_TYPE_AI_CUPID_NICKNAME = 1; // AI红娘昵称
}

message AIPlayHubRichTextImg{
  string url = 1; // 图片url
  uint32 url_type = 2; // 图片类型 AIPlayHubRichTextImgType
}

enum AIPlayHubRichTextImgType{
  AI_PLAY_HUB_RICH_TEXT_IMG_TYPE_UNSPECIFIED = 0;
  AI_PLAY_HUB_RICH_TEXT_IMG_TYPE_AI_CUPID_AVATAR = 1; // AI红娘头像
}

// AIPlayHubRichTextIMMsg 富文本消息
message AIPlayHubRichTextIMMsg{
  repeated AIPlayHubRichTextElement msg_content = 1; // 消息内容
}

// AI红娘消息
message AICupidMsg{
  string avatar = 1; // 头像
  string nickname = 2; // 昵称
  string content = 3; // 内容
  AICupidNickname nickname_info = 4; // 昵称
  string report_content = 5; // 上报内容
}

// AI红娘资料卡
message AICupidProfile{
  string avatar = 1; // 头像
  AICupidNickname nickname = 2; // 昵称
  string description = 3; // 描述
  uint32 status = 4; // 状态 AICupidStatus
  uint32 gender = 5; // 性别
}

message AICupidBaseConfig{
  uint32 status = 1; // 状态 AICupidStatus
  string chat_bg_url = 2; // 聊天背景
}

enum AICupidStatus{
  AI_CUPID_STATUS_UNSPECIFIED = 0;
  AI_CUPID_STATUS_NORMAL = 1; // 正常
  AI_CUPID_STATUS_QUIT = 2; // 退出
}

enum AIInspirationStatus{
  AI_INSPIRATION_STATUS_UNSPECIFIED = 0;
  AI_INSPIRATION_STATUS_NORMAL = 1; // 正常
  AI_INSPIRATION_STATUS_CLOSE = 2; // 关闭
}

message AICupidNickname{
  string nickname = 1; // 昵称：AI红娘小桃
  string little_nickname = 2; // 小昵称：红娘小桃
  string short_nickname = 3; // 短昵称：红娘
}

// 灵感回复推送消息
message AIInspirationNotify{
  uint32 type = 1; // AIInspirationType
  repeated string msg_content = 2; // 消息内容
  int32 remain_times = 3; // 剩余次数
  string target_account = 4; // 对方用户account
  string report_content = 5; // 上报内容
  string trace_id = 6; // 客户端请求标识
  bool multi_reply_editable_switch = 7; // 分段回复是否可编辑开关,动态配置
  string create_type = 8; // 区分首次打开/刷新,客户端上报用
}

enum AIInspirationType{
  AI_INSPIRATION_TYPE_UNSPECIFIED = 0;
  AI_INSPIRATION_TYPE_POPUP = 1; // 弹窗
  AI_INSPIRATION_TYPE_INPUT = 2; // 输入框
  AI_INSPIRATION_TYPE_INTEGRATED = 3; // 整合类型,6.70.5统一用这个类型
}

// AIPlayHubFunctionStatus 功能状态（开关）
message AIPlayHubFunctionStatusRequest{
  BaseReq base_req = 1;
  string target_account = 2; // 对方用户account
}

message AIPlayHubFunctionStatusResponse{
  BaseResp base_resp = 1;
  AICupidBaseConfig cupid_status = 2; // AI红娘状态 AICupidProfile
  uint32 inspiration_status = 3; // 灵感回复状态 AIInspirationStatus
  AIInspirationConfig inspiration_config = 4;
  RolePlayCPStarTag cp_star_tag = 5; // 点亮CP关系标识
  RolePlayCPBaseConfig role_play_cp_config = 6;
}

message AIInspirationConfig{
  uint32 inspiration_status = 1; // 灵感回复状态 AIInspirationStatus
  uint32 popup_remain_times = 2; // 弹窗剩余次数
  uint32 input_remain_times = 3; // 输入框剩余次数
  uint32 countdown_interval = 4;  // 倒计时间隔时间
}


// GetAIInspirationRequest 获取灵感回复请求
message GetAIInspirationRequest{
  BaseReq base_req = 1;
  string target_account = 2; // 对方用户account
  uint32 type = 3; // AIInspirationType
  string report_content = 4; // 上报内容
  string trace_id = 5; // 客户端请求标识，在推送时透传回去
  string create_type = 6; // 区分首次打开/刷新,在推送时透传回去
  repeated string last_msg_contents = 7; // 上次生成的消息内容
}

message GetAIInspirationResponse{
  BaseResp base_resp = 1;
  uint32 remain_times = 3; // 剩余次数
}

//获取红娘资料卡
message GetAICupidDetailRequest{
  BaseReq base_req = 1;
  string target_account = 2; // 对方用户account
}

message GetAICupidDetailResponse{
  BaseResp base_resp = 1;
  AICupidProfile profile = 2;
}

//ChangeAICupidStatusRequest 退出红娘/留下红娘
message ChangeAICupidStatusRequest{
  BaseReq base_req = 1;
  string target_account = 2;
  uint32 status = 3; // 状态 AICupidStatus
}

message ChangeAICupidStatusResponse{
  BaseResp base_resp = 1;
  string result_msg = 2;
}

//AI红娘引导关注消息
message AICupidGuideFollowMsg{
  AICupidMsg ai_cupid_msg=1;
  string already_chatted_numbers=2;  //已对话次数
  string report_content=3;  //上报内容
}

message AICupidCardOnlineNotify{
  string avatar = 1; // 头像
  string target_account=2;
  string title = 3;    //标题
  string sub_title = 4;   //副标题
  repeated string common_tags = 5;  //通用标签
}


message AICupidChangeStatusPush{
  string target_account = 1;
  uint32 status = 2; // 状态 AICupidStatus
  string chat_bg_url = 3; // 聊天背景
}

// MuseGreetingNotify 问候推送
message MuseGreetingNotify{
  uint32 type = 1; // MuseGreetingType
  bytes msg_content = 2; // 消息内容
}

enum MuseGreetingType{
  MUSE_GREETING_TYPE_UNSPECIFIED = 0;
  MUSE_GREETING_TYPE_ROLE_PLAY_CONTINUE_CHAT = 1; // 角色扮演持续引导聊天弹窗 ContinueGuideChatNotify
}
