package server

import (
	"context"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"

	channel_play "golang.52tt.com/protocol/app/channel-play"
	Account "golang.52tt.com/protocol/services/accountsvr"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	"golang.52tt.com/protocol/services/demo/echo"
	game_card "golang.52tt.com/protocol/services/game-card"
	pb "golang.52tt.com/protocol/services/userline-common-api"
	"golang.52tt.com/services/userline-common-api/internal/mgr"
	"golang.52tt.com/services/userline-common-api/internal/rpc"
)

type StartConfig struct {
	// [optional] from startup arguments

	// from config file

}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	err := rpc.InitClient()
	if err != nil {
		return nil, err
	}

	s := &Server{}

	return s, nil
}

type Server struct {
}

func (s *Server) ShutDown() {

}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

// GetUserTag usertag服务迁移的接口
func (s *Server) GetUserTag(ctx context.Context, req *pb.GetUserTagReq) (*pb.GetUserTagResp, error) {
	out := &pb.GetUserTagResp{}
	if req.GetTargetUid() == 0 {
		return out, nil
	}
	gameCardList, gameCardErr := rpc.GameCardClient.GetGameCard(ctx, req.GetTargetUid())
	if gameCardErr != nil {
		log.ErrorWithCtx(ctx, "GetGameCard fail, err:%s, uid:%d", gameCardErr.Error(), req.GetTargetUid())
		return nil, gameCardErr
	}
	userTagList, err := rpc.UserTagGoClient.GetUserTag(ctx, req.GetTargetUid(), req.GetIsNeedTagExt())
	if err != nil {
		log.ErrorWithCtx(ctx, "userTagMgr.GetUserTag failed %v, uid %d", err, req.GetTargetUid())
		return out, err
	}
	tagList := make([]*pb.UserTagBase, 0, len(gameCardList)+len(userTagList.GetTagList()))
	for _, gameCardInfo := range gameCardList {
		if gameCardInfo.CardScopeType == game_card.CardScopeType_CARD_SCORE_TYPE_CATEGORY {
			continue
		}
		info := transGameCardExt(ctx, gameCardInfo)
		if info == nil {
			continue
		}
		tagList = append(tagList, info)
	}

	for _, tag := range userTagList.GetTagList() {
		tagList = append(tagList, &pb.UserTagBase{
			TagType: tag.GetTagType(),
			TagName: tag.GetTagName(),
			TagId:   tag.GetTagId(),
			TagInfo: tag.GetTagInfo(),
			IsDel:   tag.GetIsDel(),
		})
	}

	out.UserTaglist = &pb.UserTagList{
		Uid:     req.GetTargetUid(),
		TagList: tagList,
	}
	return out, nil
}

func (s *Server) BatGetUserTag(ctx context.Context, req *pb.BatGetUserTagReq) (*pb.BatGetUserTagResp, error) {
	out := &pb.BatGetUserTagResp{}

	gameCardInfoMap, err := rpc.GameCardClient.BatGetGameCardMap(ctx, req.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatGetGameCard fail, err:%s, uidList:%v", err.Error(), req.GetUidList())
		return out, err
	}

	userTagResp, tagErr := rpc.UserTagGoClient.BatGetUserTag(ctx, req.GetUidList(), req.GetIsNeedTagExt())
	if tagErr != nil {
		log.ErrorWithCtx(ctx, "userTagMgr.BatGetUserTag failed %v, uidList %v", tagErr, req.GetUidList())
		return out, tagErr
	}

	userTagList := make([]*pb.UserTagList, 0, len(req.GetUidList()))
	userTagMap := make(map[uint32]*pb.UserTagList, len(userTagResp))
	for _, item := range userTagResp {
		tempTagBase := make([]*pb.UserTagBase, 0, len(item.GetTagList()))
		for _, tempTagInfo := range item.GetTagList() {
			tempTagBase = append(tempTagBase, &pb.UserTagBase{
				TagType: tempTagInfo.GetTagType(),
				TagName: tempTagInfo.GetTagName(),
				TagId:   tempTagInfo.GetTagId(),
				TagInfo: tempTagInfo.GetTagInfo(),
				IsDel:   tempTagInfo.GetIsDel(),
			})
		}
		tempTagList := &pb.UserTagList{
			Uid:     item.GetUid(),
			TagList: tempTagBase,
		}
		userTagMap[item.Uid] = tempTagList
		userTagList = append(userTagList, tempTagList)
	}

	for uid, gameCardInfos := range gameCardInfoMap {
		tagList := make([]*pb.UserTagBase, 0, len(gameCardInfos))
		for _, gameCardInfo := range gameCardInfos {
			if gameCardInfo.CardScopeType == game_card.CardScopeType_CARD_SCORE_TYPE_CATEGORY {
				continue
			}
			tagList = append(tagList, transGameCardExt(ctx, gameCardInfo))
		}

		if tag, ok := userTagMap[uid]; ok {
			tag.TagList = append(tag.TagList, tagList...)
		} else {
			userTagList = append(userTagList, &pb.UserTagList{
				Uid:     uid,
				TagList: tagList,
			})
		}
	}
	out.UsertaglistList = userTagList
	return out, nil

}

func (s *Server) GetSimpleGameTag(ctx context.Context, req *pb.GetSimpleGameTagReq) (*pb.GetSimpleGameTagResp, error) {
	out := &pb.GetSimpleGameTagResp{
		GameExtList: make([]*pb.SimpleGameTagExt, 0),
	}

	if len(req.GetUidList()) == 0 {
		log.DebugWithCtx(ctx, "req.UidList or req.GameName == 0")
		return out, nil
	}

	simpleGameTagLists, err := rpc.GameCardClient.BatGetSimpleGameTag(ctx, req.GetUidList(), []string{req.GetGameName()})
	if err != nil {
		log.ErrorWithCtx(ctx, "mgr.BatGetSimpleGameTag failed %v", err)
		return out, nil
	}

	for _, tagInfo := range simpleGameTagLists {
		for _, extInfo := range tagInfo.GetExtList() {
			var gameScreenshot string
			for _, screen := range extInfo.GetGameScreenshotList() {
				if len(screen) > 0 {
					gameScreenshot = screen
					break
				}
			}
			out.GameExtList = append(out.GameExtList, &pb.SimpleGameTagExt{
				//GameId:           extInfo.GetUGameId(),   // 旧接口返回都是0
				GameName:         extInfo.GetGameName(),
				GameNickname:     extInfo.GetGameNickname(),
				GameArea:         extInfo.GetGameArea(),
				GameDan:          extInfo.GetGameLevel(),
				Uid:              extInfo.GetUid(),
				GameDanUrlForMic: extInfo.GetGameLevelUrlForMic(),
				GameRole:         extInfo.GetGameRole(),
				GamePosition:     extInfo.GetGamePositionList(),
				GameHeroList:     extInfo.GetGameHeroList(),
				GameScreenshot:   gameScreenshot,
				GameStyle:        extInfo.GetGameStyleList(),
				GameLevelUrl:     extInfo.GetGameLevelUrl(),
				TagId:            extInfo.GetGameCardId(),
				IsCompleted:      extInfo.GetIsCompleted(),
			})
		}
	}
	return out, nil
}

func (s *Server) GetTagConfigList(ctx context.Context, req *pb.GetTagConfigListReq) (*pb.GetTagConfigListResp, error) {
	out := &pb.GetTagConfigListResp{}

	tagList, err := mgr.GetTagConfigListByType(ctx, req.GetTagType())
	if err != nil {
		log.ErrorWithCtx(ctx, "confMgr.GetTagConfigList failed %v, tagType %d", err, req.GetTagType())
		return out, err
	}
	out.TagList = tagList

	return out, nil
}

func (s *Server) GetMicUserExtInfo(ctx context.Context, req *pb.GetMicUserExtInfoRequest) (*pb.GetMicUserExtInfoResponse, error) {
	resp := new(pb.GetMicUserExtInfoResponse)

	reqUser := req.GetUser()
	if reqUser == nil || reqUser.GetUid() == 0 || reqUser.GetType() != uint32(Account.USER_TYPE_USER_TYPE_ROBOT) {
		return resp, nil
	}

	aiAccountReq := &aigc_account.GetAIAccountRequest{
		Uid:       reqUser.GetUid(),
		ReqSource: aigc_account.GetAIAccountSource_ON_MIC,
	}
	aiAccountResp, err := rpc.AigcAccountClient.GetAIAccount(ctx, aiAccountReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMicUserExtInfo GetAIAccount req(%+v) err: %v", aiAccountReq, err)
		return resp, err
	}

	micUserInfo := &channel_play.MicUserExtInfo{}
	if aiAccount := aiAccountResp.GetAccount(); aiAccount != nil {
		micUserInfo.AiAccount = &channel_play.MicUserExtInfo_AIAccount{
			Identity: aiAccount.GetIdentity(),
			Desc:     aiAccount.GetDesc(),
		}
	}

	bin, _ := proto.Marshal(micUserInfo)
	resp.MicUserInfo = bin

	return resp, nil
}

func (s *Server) BatchGetMicUserExtInfo(ctx context.Context, req *pb.BatchGetMicUserExtInfoRequest) (*pb.BatchGetMicUserExtInfoResponse, error) {
	resp := &pb.BatchGetMicUserExtInfoResponse{
		MicUserInfos: make(map[uint32][]byte),
	}

	if len(req.GetUsers()) == 0 {
		return resp, nil
	}

	var (
		robotUids  = make([]uint32, 0, len(req.GetUsers()))
		robotUsers = make([]*pb.MicUser, 0, len(req.GetUsers()))
	)
	for _, user := range req.GetUsers() {
		if user.GetType() == uint32(Account.USER_TYPE_USER_TYPE_ROBOT) {
			robotUids = append(robotUids, user.GetUid())
			robotUsers = append(robotUsers, user)
		}
	}

	// ai账号信息
	aiAccountMap := make(map[uint32]*aigc_account.AIAccount)
	if len(robotUsers) > 0 {
		// 查询ai账号信息
		aiAccountReq := &aigc_account.BatchGetAIAccountRequest{
			UidList:   robotUids,
			ReqSource: aigc_account.GetAIAccountSource_ON_MIC,
		}
		aiAccountResp, err := rpc.AigcAccountClient.BatchGetAIAccount(ctx, aiAccountReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetMicUserExtInfo BatchGetAIAccount req(%+v) err: %v", aiAccountReq, err)
			return resp, err
		}

		for _, aiAccount := range aiAccountResp.GetAccountList() {
			aiAccountMap[aiAccount.GetUid()] = aiAccount
		}
	}

	for _, user := range req.GetUsers() {
		micUserInfo := &channel_play.MicUserExtInfo{}
		if aiAccount, ok := aiAccountMap[user.GetUid()]; ok {
			micUserInfo.AiAccount = &channel_play.MicUserExtInfo_AIAccount{
				Identity: aiAccount.GetIdentity(),
				Desc:     aiAccount.GetDesc(),
			}
		}

		bin, _ := proto.Marshal(micUserInfo)
		resp.MicUserInfos[user.GetUid()] = bin
	}

	return resp, nil
}

func transGameCardExt(ctx context.Context, gameCardInfo *game_card.GameCardInfo) *pb.UserTagBase {
	screenList := make([]*pb.GameScreenShot, 0, len(gameCardInfo.GetScreenshotList()))
	for _, screen := range gameCardInfo.GetScreenshotList() {
		screenList = append(screenList, &pb.GameScreenShot{
			AuditStatus:             screen.GetAuditStatus(),
			ImgUrl:                  screen.GetImgUrl(),
			ImgUrlInvisibleGamenick: screen.GetMosaicImgUrl(),
			BeginAuditTime:          screen.GetBeginAuditTime(),
			Index:                   screen.GetIndex(),
		})
	}

	opList := make([]*pb.UserGameTagOpt, 0, len(gameCardInfo.GetOptList()))
	for _, opt := range gameCardInfo.GetOptList() {
		opList = append(opList, &pb.UserGameTagOpt{
			OptName:          opt.GetOptName(),
			OptId:            opt.GetOptId(),
			ValueUsersetList: opt.ValueList,
		})
	}
	userGameTagExt := &pb.UserGameTagExt{
		OptList:            opList,
		GameNickname:       gameCardInfo.GameNickname,
		GameScreenshotList: screenList,
	}

	tagInfo, marErr := proto.Marshal(userGameTagExt)
	if marErr != nil {
		log.ErrorWithCtx(ctx, "proto.Marshal fail, err:%s", marErr.Error())
		return nil
	}
	return &pb.UserTagBase{
		TagType: mgr.TagTypeGameCard,
		TagName: gameCardInfo.GetGameCardName(),
		TagId:   gameCardInfo.GetGameCardId(),
		TagInfo: tagInfo,
		IsDel:   false,
	}
}
