package albumhelper

import (
	"fmt"
	qiniuQbox "github.com/qiniu/api.v7/auth/qbox"
	qiniuStorage "github.com/qiniu/api.v7/storage"
	"golang.52tt.com/pkg/marketid_helper"
	"golang.52tt.com/pkg/protocol"
	obs_helper "golang.52tt.com/services/helper-from-cpp/obs-helper"
	"strings"
)

const (
	qiniuAccessKey = "i8m3lz89N5AGghEJC5n_cFIZ9QamW4U28Oxgdj7r"
	qiniuSecretKey = "BKI2xDmiyUOACaEsiTCqYS3JvxOekzxr623x2jxi"
)

const (
	PRESENT_MSG_ICON_SUFFIX     = "?imageView2/0/w/24/h/24"
	PRESENT_MSG_ICON_SUFFIX_AND = "&imageView2/0/w/24/h/24"

	ALBUM_URL_PREFIX = "http://ga-album-cdnqn.52tt.com/"
	PHOTO_URL_PREFIX = ALBUM_URL_PREFIX
)

func CreateAlbumToken() string {
	putPolicy := qiniuStorage.PutPolicy{
		Scope:   "ga-album",
		Expires: 10 * 60,
	}
	mac := qiniuQbox.NewMac(qiniuAccessKey, qiniuSecretKey)
	return putPolicy.UploadToken(mac)
}

func CreateCrashLogToken(clientType uint16) string {
	// 上传策略
	putPolicy := qiniuStorage.PutPolicy{
		Expires: 10 * 60,
	}

	if clientType == protocol.ClientTypeANDROID || clientType == protocol.ClientTypePcASSISTANT {
		putPolicy.Scope = "tt-crash-logs-android"
	} else if clientType == protocol.ClientTypeIOS {
		putPolicy.Scope = "tt-crash-logs-ios"
	}

	// 生成upload token
	mac := qiniuQbox.NewMac(qiniuAccessKey, qiniuSecretKey)
	return putPolicy.UploadToken(mac)
}

func CreateReportImgToken() string {
	// 上传策略
	putPolicy := qiniuStorage.PutPolicy{
		Scope:   "ga-album",
		Expires: 10 * 60,
	}

	// 生成upload token
	mac := qiniuQbox.NewMac(qiniuAccessKey, qiniuSecretKey)
	return putPolicy.UploadToken(mac)
}

func ToPresentMsgIconUrl(presentIconUrl string) string {
	if len(presentIconUrl) <= 0 {
		return ""
	}

	if strings.Contains(presentIconUrl, "?") {
		return presentIconUrl + PRESENT_MSG_ICON_SUFFIX_AND
	} else {
		return presentIconUrl + PRESENT_MSG_ICON_SUFFIX
	}
}

func ToLongPhotoUrl(photoShortUrl string, marketId, clientType uint32) string {
	if len(photoShortUrl) == 0 {
		return ""
	}

	out := getUrlPrefix(marketId, clientType, PHOTO_URL_PREFIX)
	out += photoShortUrl
	return out
}

func getUrlPrefix(marketId, clientType uint32, defaultPrefix string) string {
	domain := marketid_helper.Get("ga-album", marketId, clientType)
	if len(domain) == 0 {
		return defaultPrefix
	}

	return fmt.Sprintf("https://%s/", domain)
}

func ToLongPhotoObsUrl(scope, imgKey string, marketId, clientType uint32) string {
	return obs_helper.GetObsUrl(scope, imgKey, marketId, clientType)
}
