package internal

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/device_id"
	"golang.52tt.com/pkg/protocol"
	protoGrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	game_play_logic "golang.52tt.com/protocol/app/game-play-logic"
	"golang.52tt.com/protocol/common/status"
	game_pal "golang.52tt.com/protocol/services/game-pal"
	userRatePb "golang.52tt.com/protocol/services/game-user-rate"
	im_api "golang.52tt.com/protocol/services/im-api"
	"golang.52tt.com/services/game-play-logic/internal/rpc"
	"time"
)

// UserGameRateReport 用户对开黑用户评价触发上报
func (s *Server) UserGameRateReport(ctx context.Context, req *game_play_logic.UserGameRateReportReq) (*game_play_logic.UserGameRateReportResp, error) {
	out := &game_play_logic.UserGameRateReportResp{
		BaseResp: new(app.BaseResp),
	}
	log.InfoWithCtx(ctx, "UserGameRateReport, req: %+v", req)

	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取用户信息失败")
	}
	if len(req.GetItems()) == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	var err error
	out.BaseResp.ErrInfo, _, err = s.userRateMgr.SenderRiskCheck(ctx, serviceInfo.UserID, req.GetBaseReq())
	if err != nil {
		log.ErrorWithCtx(ctx, "SenderRiskCheck fail, err: %+v, req: %+v", err, req)
		return out, err
	}

	reqItems := make([]*userRatePb.AddGameUserRateItem, 0, len(req.GetItems()))
	for _, item := range req.GetItems() {
		if item.GetSource() == uint32(game_play_logic.GameUserRateSourceType_GAME_USER_RATE_SOURCE_TYPE_IM_CHAT) {
			// 聊天来源的需要用account获取rateUid
			rateUid, _, err := rpc.AccountClient.GetUidByName(ctx, item.GetRateAccount())
			if err != nil || rateUid == 0 {
				log.ErrorWithCtx(ctx, "GetUidByName fail, err: %+v, rateUid: %d, req: %+v", err, rateUid, req)
				continue
			}
			item.RateUid = rateUid
		}
		reqItems = append(reqItems, &userRatePb.AddGameUserRateItem{
			Source:       item.GetSource(),
			RateUid:      item.GetRateUid(),
			InteractTime: item.GetInteractTime(),
			TabId:        item.GetTabId(),
		})
	}

	deviceId := device_id.ToDeviceHexId(serviceInfo.DeviceID, true)
	successAddItems, err := s.userRateMgr.AddGameUserRate(ctx, serviceInfo.UserID, deviceId, reqItems)
	if err != nil {
		return out, err
	}
	// 推送
	if len(successAddItems) == 0 {
		return out, nil
	}

	// 在线推送和TT语音助手推送
	asyncCtx, asyncCancel := protoGrpc.NewContextWithInfoTimeout(ctx, 3*time.Second)
	go func() {
		defer asyncCancel()
		// 在线推送
		_ = s.userRateMgr.PushGameUserRate(asyncCtx, serviceInfo.UserID, successAddItems)

		// 只有6.48版本才推送这个消息
		if serviceInfo.ClientVersion != protocol.FormatClientVersion(6, 48, 0) {
			return
		}

		isNeedPush, rpcErr := s.userRateMgr.IsNeedPushAssist(asyncCtx, serviceInfo.UserID)
		if rpcErr != nil {
			return
		}
		if !isNeedPush {
			log.InfoWithCtx(asyncCtx, "UserGameRateReport, isNeedPushAssist is false")
			return
		}

		notRateCount, _, _, notRateErr := s.userRateMgr.GetGameUserNotRateCount(asyncCtx, serviceInfo.UserID, "", 0)
		if notRateErr != nil {
			return
		}
		_ = s.userRateMgr.SendTTAsstMsg(asyncCtx, serviceInfo.UserID, &im_api.Text{
			Content:   fmt.Sprintf("您有%d条评价未处理，每天参与3条评价可获得账号经验，去看看", notRateCount),
			Url:       fmt.Sprintf("tt://m.52tt.com/evaluate_detail_page?source=%d", uint32(game_play_logic.GameUserRateSourceType_GAME_USER_RATE_SOURCE_TYPE_ASSISTANT_PUSH)),
			Highlight: "去看看",
		})
	}()

	return out, nil
}

// GetUserNotRateCount IM页面获取未评价的数量，展示红点
func (s *Server) GetUserNotRateCount(ctx context.Context, req *game_play_logic.GetUserNotRateCountReq) (*game_play_logic.GetUserNotRateCountResp, error) {
	out := &game_play_logic.GetUserNotRateCountResp{}
	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取用户信息失败")
	}
	if serviceInfo.UserID == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "用户ID不能为空")
	}

	var err error
	out.NotRateCount, out.NotReadCount, out.FirstRecordTime, err = s.userRateMgr.GetGameUserNotRateCount(ctx, serviceInfo.UserID, req.GetRateId(), req.GetCreateTime())

	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserNotRateCount, req: %+v, resp: %+v, err: %+v", req, out, err)
	} else {
		log.InfoWithCtx(ctx, "GetUserNotRateCount, req: %+v, resp: %+v", req, out)
	}

	return out, err
}

func (s *Server) SetFirstChatToken(ctx context.Context, req *game_play_logic.SetFirstChatTokenReq) (*game_play_logic.SetFirstChatTokenResp, error) {
	out := &game_play_logic.SetFirstChatTokenResp{}

	log.InfoWithCtx(ctx, "SetFirstChatToken, req: %+v", req)

	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取用户信息失败")
	}
	if serviceInfo.UserID == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "用户ID不能为空")
	}

	if req.GetSource() != uint32(game_play_logic.GameUserRateSourceType_GAME_USER_RATE_SOURCE_TYPE_IM_CHAT) {
		log.InfoWithCtx(ctx, "SetFirstChatToken, req: %+v, source not IM CHAT", req)
		return out, nil
	}

	rateUid, _, rpcErr := rpc.AccountClient.GetUidByName(ctx, req.GetRateAccount())
	if rpcErr != nil || rateUid == 0 {
		log.ErrorWithCtx(ctx, "GetUidByName fail, err: %+v, req: %+v", rpcErr, req)
		return out, nil
	}
	tabId := req.GetTabId()
	if tabId == 0 && req.GetIsSuperPublish() {
		// 获取超级发布玩法
		rsp, gamePalErr := rpc.GamePalClient.GetUserSuperPublishInfo(ctx, &game_pal.GetUserSuperPublishInfoReq{
			Uid: serviceInfo.UserID,
		})
		if gamePalErr != nil {
			log.ErrorWithCtx(ctx, "GetUserSuperPublishInfo fail, err: %+v, req: %+v", gamePalErr, req)
			return out, gamePalErr
		}
		tabId = rsp.GetPublishInfo().GetTabId()
	}
	if tabId == 0 {
		log.InfoWithCtx(ctx, "SetFirstChatToken tabId is 0, rateUid:%d, req:%+v", rateUid, req)
		return out, nil
	}

	selfDeviceId := device_id.ToDeviceHexId(serviceInfo.DeviceID, true)
	err := s.userRateMgr.SetFirstChatToken(ctx, serviceInfo.UserID, selfDeviceId, rateUid, tabId, req.GetSource())
	return out, err
}

/*
GetRateReputationInfo 获取用户评价标签和信誉分
请求时机：用户查看他人的个人主页时和房间内点击用户头像卡片时展示，在切换个人中心页面下关于我的字tab时会重新请求，或者下拉刷新时重新请求
qps预估：300内(对比类似接口)
*/
func (s *Server) GetRateReputationInfo(ctx context.Context, req *game_play_logic.GetRateReputationInfoReq) (*game_play_logic.GetRateReputationInfoResp, error) {
	log.InfoWithCtx(ctx, "GetRateReputationInfo, req: %+v", req)
	out := &game_play_logic.GetRateReputationInfoResp{
		ReputationInfo: &game_play_logic.RateReputation{},
		TagInfo:        &game_play_logic.UserRateTagItem{},
	}
	if req.GetTargetUid() == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	// 获取展示规则配置
	confResp, err := rpc.GameUserRateClient.GetShowRateAndPunishConfWithCache(ctx, &userRatePb.GetShowRateAndPunishConfReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetShowRateAndPunishConf fail, err: %+v", err)
		return out, err
	}
	showConf := confResp.GetConf()

	if showConf.GetShowRateTag() {
		tagData, tErr := s.userRateMgr.GetUserRateTagInfo(ctx, req.GetTargetUid(), showConf)
		if tErr == nil {
			out.TagInfo.IsShow = showConf.GetShowRateTag()
			out.TagInfo.TagInfo = tagData
		}
	}

	if showConf.GetShowReputation() {
		reputationScore, sErr := s.userRateMgr.GetUserReputationScore(ctx, req.GetTargetUid())
		if sErr == nil {
			out.ReputationInfo.Score = reputationScore
			out.ReputationInfo.IsShow = showConf.GetShowReputation()
		}
	}

	return out, nil
}
