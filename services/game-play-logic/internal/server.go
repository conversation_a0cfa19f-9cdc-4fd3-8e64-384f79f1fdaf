package internal

import (
	"context"
	"golang.52tt.com/kaihei-pkg/asr"
	"strconv"
	"time"

	"github.com/gookit/goutil/timex"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/device_id"

	device_info_service "golang.52tt.com/clients/datacenter/device-info-service"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/abtest"
	"golang.52tt.com/pkg/marketid_helper"
	"golang.52tt.com/pkg/protocol"
	protoGrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/speedlimit"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	game_play_logic "golang.52tt.com/protocol/app/game-play-logic"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	device_pb "golang.52tt.com/protocol/datacenter/device-info-service"
	channel_play_index "golang.52tt.com/protocol/services/channel-play-index"
	"golang.52tt.com/protocol/services/demo/echo"
	gameCardPb "golang.52tt.com/protocol/services/game-card"
	"golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	"golang.52tt.com/services/game-play-logic/internal/cache"
	config "golang.52tt.com/services/game-play-logic/internal/config/ttconfig/game_play_logic"
	"golang.52tt.com/services/game-play-logic/internal/config/ttconfig/public_switch_conf"
	"golang.52tt.com/services/game-play-logic/internal/mgr/block_mgr"
	"golang.52tt.com/services/game-play-logic/internal/mgr/game_time_mgr"
	game_time_mgr_impl "golang.52tt.com/services/game-play-logic/internal/mgr/game_time_mgr/impl"
	"golang.52tt.com/services/game-play-logic/internal/mgr/user_rate_mgr"
	"golang.52tt.com/services/game-play-logic/internal/rpc"
)

const (
	getUserInfoErrorMsg = "获取用户信息失败"
)

type StartConfig struct {
	AudioToTextConf *asr.AsrConfig `json:"audio_to_text_conf"`
}

type AigcConfig struct {
	AppId  string `json:"app_id"`
	AppKey string `json:"app_key"`
	Url    string `json:"url"`
	Model  string `json:"model"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)
	err := public_switch_conf.Parse(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "Setup config fail: %v ", err)
		return nil, err
	}

	err = config.InitGamePlayLogicConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "InitGamePlayLogicConfig fail: %v", err)
		return nil, err
	}

	err = rpc.InitClients(ctx)
	if err != nil {
		return nil, err
	}

	err = cache.NewCache(ctx)
	if err != nil {
		log.Errorf("NewCache err")
		return nil, err
	}

	blockMgr := block_mgr.NewBlockMgr()
	userRateMgr := user_rate_mgr.NewUserRateMgr()
	gameTimeMgr := game_time_mgr_impl.NewManager(rpc.GameTimeClient, rpc.FriendshipCli)

	supervisorInst, err := supervision.NewSupervisory(rpc.AccountGoClient, rpc.ExpClient, rpc.RealNameClient, nil,
		rpc.AccountClient, rpc.ChannelPlayTabClient, public_switch_conf.GetEnv())
	if err != nil {
		return nil, err
	}

	gameTimeLimiter := speedlimit.NewSpeedLimit(int64(config.GetGamePlayLogicConfig().GetGameTimeLimiterTimeout()), int64(config.GetGamePlayLogicConfig().GetGameTimeLimiterCount()))

	asrHandle := asr.NewAsrHandle()
	return &Server{
		startConfig: cfg,

		blockMgr:    blockMgr,
		userRateMgr: userRateMgr,
		gameTimeMgr: gameTimeMgr,

		supervisor: supervisorInst,

		gameTimeLimiter: gameTimeLimiter,
		asrHandle:       asrHandle,
	}, nil
}

type Server struct {
	startConfig *StartConfig

	blockMgr    *block_mgr.BlockMgr
	userRateMgr *user_rate_mgr.UserRateMgr
	gameTimeMgr game_time_mgr.IManager

	supervisor *supervision.Supervisory

	gameTimeLimiter speedlimit.ISpeedLimit

	asrHandle *asr.AsrHandle
}

func (s *Server) ShutDown() {}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) isPopUp(ctx context.Context, serviceInfo *protoGrpc.ServiceInfo) (bool, error) {
	deviceId, err := device_id.ToClientDeviceId(device_id.ToDeviceHexId(serviceInfo.DeviceID, true), uint32(serviceInfo.ClientType))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecallPopUp device_id.ToClientDeviceId err:%v, serviceInfo:%s", err, serviceInfo.String())
		return false, nil
	}
	userType, err := rpc.DeviceInfoCli.GetUserTypeByDeviceState(ctx, &device_pb.DeviceInfoRequestData{
		AppId:    marketid_helper.GetAppName(serviceInfo.MarketID),
		DeviceId: deviceId,
		Uid:      strconv.FormatInt(int64(serviceInfo.UserID), 10),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecallPopUp GetUserTypeByDeviceState err:%v, serviceInfo:%s", err, serviceInfo.String())
		return false, nil
	}
	log.DebugWithCtx(ctx, "GetRecallPopUp user %d usertype:%d", serviceInfo.UserID, userType)
	if userType != device_info_service.Recall {
		//log.DebugWithCtx(ctx, "GetRecallPopUp user %d skip not recall usertype:%s", serviceInfo.UserID, userType)
		return false, nil
	}
	return true, nil
}

func (s *Server) GetRecallPopUp(ctx context.Context, req *game_play_logic.GetRecallPopUpReq) (*game_play_logic.GetRecallPopUpResp, error) {
	out := &game_play_logic.GetRecallPopUpResp{}

	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok || serviceInfo.UserID == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}

	isPopUp, err := s.isPopUp(ctx, serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "isPopUp err:%v, serviceInfo:%s", err, serviceInfo.String())
		return out, err
	}
	if !isPopUp {
		return out, nil
	}

	gameScanResultResp, err := rpc.GameServerV2Client.GetGameScanResult(ctx, serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecallPopUp GetGameScanResult err:%v, serviceInfo:%s", err, serviceInfo.String())
		return out, err
	}
	log.DebugWithCtx(ctx, "GetRecallPopUp gameServerV2Client.GetGameScanResult resp:%s", gameScanResultResp.String())

	popUpTabInfos := make([]*game_play_logic.PopUpTabInfo, 0, 300)

	//var bEnd bool
	uGameIds := make([]uint32, 0, 300)
	for _, game := range gameScanResultResp.GetGameList() {
		uGameIds = append(uGameIds, game.GetUGameId())
	}
	log.DebugWithCtx(ctx, "GetRecallPopUp uGameIds:%v", uGameIds)

	gameCardRsp, gameCardErr := rpc.GameCardCli.GetGameCard(ctx, &gameCardPb.GetGameCardReq{
		Uid: serviceInfo.UserID,
	})
	if gameCardErr != nil {
		log.ErrorWithCtx(ctx, "GetRecallPopUp GetGameCard err:%v, serviceInfo:%s", gameCardErr, serviceInfo.String())
		return out, gameCardErr
	}

	var gameCardIds []uint32
	for _, gameCard := range gameCardRsp.GetGameCardList() {
		if gameCard.GetUGameId() != 0 {
			uGameIds = append(uGameIds, gameCard.GetUGameId())
		} else {
			gameCardIds = append(gameCardIds, gameCard.GetGameCardId())
		}
	}
	log.DebugWithCtx(ctx, "GetRecallPopUp GetGameCard resp:%s, uGameIds:%v", gameCardRsp.String(), uGameIds)

	var tabIds []uint32
	if len(uGameIds) != 0 {
		gameInfoMap := cache.GetTabInfoCache().GetUGameIdTabMapCache()
		for _, uGameId := range uGameIds {
			if gameInfo, exists := gameInfoMap[uGameId]; exists {
				if gameInfo.GetId() != 0 {
					tabIds = append(tabIds, gameInfo.GetId())
				} else {
					log.ErrorWithCtx(ctx, "gameInfoMap get ugameid err: nil, ugameid:%d", uGameId)
				}
			}
		}
		log.DebugWithCtx(ctx, "GetRecallPopUp tabInfo tabIds:%v", tabIds)
	}

	if len(gameCardIds) != 0 {
		gameCardIdTabMap := cache.GetTabInfoCache().GetGameCardIdTabMap()
		for _, gameCardId := range gameCardIds {
			if tmpTabInfos, ok := gameCardIdTabMap[gameCardId]; ok {
				if len(tmpTabInfos) != 0 {
					tabIds = append(tabIds, tmpTabInfos[0].GetId())
				}
				log.DebugWithCtx(ctx, "GetRecallPopUp gameCardId get tabIds:%v", tabIds)
			} else {
				log.ErrorWithCtx(ctx, "GetRecallPopUp gameCardIdTabMap get gameCardId err: nil, gameCardId:%d", gameCardId)
			}
		}
	}

	yesterdayTime := time.Now().AddDate(0, 0, -1).Unix()
	if config.GetGamePlayLogicConfig().IsPullTodayPublish() {
		yesterdayTime = time.Now().Unix()
	}
	pulishTabListRsp, publishErr := rpc.IndexCli.GetPublishTabCntList(ctx, &channel_play_index.GetPublishTabCntListReq{
		QueryTime:   yesterdayTime,
		LimitCnt:    300,
		PublishType: channel_play_index.PublishRecordType_CATE_TYPE_KAIHEI,
	})
	if publishErr != nil {
		log.ErrorWithCtx(ctx, "GetRecallPopUp indexCli.GetPublishTabCntList err: %v, req:%s", publishErr, req.String())
		return out, publishErr
	}

	if len(pulishTabListRsp.GetTabIds()) != 0 {
		tabIds = append(tabIds, pulishTabListRsp.GetTabIds()...)
	}

	log.DebugWithCtx(ctx, "GetRecallPopUp publish tabids:%v, all tabIds:%v, yesterdayTime:%d", pulishTabListRsp.GetTabIds(), tabIds, yesterdayTime)

	out.GameTitle = config.GetGamePlayLogicConfig().GetRecallPopupGameTitle()
	out.LimitCnt = config.GetGamePlayLogicConfig().GetRecallPopupGameLimitCnt()
	if len(tabIds) == 0 {
		log.ErrorWithCtx(ctx, "GetRecallPopUp err:tabids 0, in:%s", req.String())
		return out, nil
	}

	tabInfoMap, err := rpc.TabClient.GetTabsByIds(ctx, tabIds)
	if err != nil {
		return out, err
	}

	excludeTabIdMap := make(map[uint32]bool, len(tabIds))
	var index uint32
	for _, tabId := range tabIds {
		if _, ok := excludeTabIdMap[tabId]; !ok {
			if tab, tabOk := tabInfoMap[tabId]; tabOk {
				if tabInfoMap[tabId].GetCategoryMapping() != uint32(topic_channel.CategoryType_Gangup_type) {
					continue
				}

				if s.supervisor.NewHomePageFilterStrategy(tab, serviceInfo, cache.GetWhiteList()) {
					log.WarnWithCtx(ctx, "NewHomePageFilterStrategy filter:%d", tabId)
					continue
				}
				excludeTabIdMap[tabId] = true
				index++
				popUpTabInfos = append(popUpTabInfos, &game_play_logic.PopUpTabInfo{
					TabId: tabId,
					Name:  tabInfoMap[tabId].GetName(),
					Icon:  tabInfoMap[tabId].GetCardsImageUrl(),
				})
				if tabInfoMap[tabId].GetName() == "" || tabInfoMap[tabId].GetCardsImageUrl() == "" {
					log.ErrorWithCtx(ctx, "tabinfo elem is empty, tabId:%d, name:%s, url:%s", tabId, tabInfoMap[tabId].GetName(), tabInfoMap[tabId].GetCardsImageUrl())
				}
				log.DebugWithCtx(ctx, "index:%d, config.GetGamePlayLogicConfig().GetRecallPopupGameCnt():%d, limitCnt:%d", index, config.GetGamePlayLogicConfig().GetRecallPopupGameCnt(), config.GetGamePlayLogicConfig().GetRecallPopupGameLimitCnt())
				if index >= config.GetGamePlayLogicConfig().GetRecallPopupGameCnt() {
					break
				}
			}
		}
	}

	out.TabInfos = popUpTabInfos

	log.InfoWithCtx(ctx, "GetRecallPopUp in:%s, out:%s", req.String(), out.String())

	return out, nil
}

func (s *Server) SubmitRecallPopUp(ctx context.Context, req *game_play_logic.SubmitRecallPopUpReq) (*game_play_logic.SubmitRecallPopUpResp, error) {
	out := &game_play_logic.SubmitRecallPopUpResp{}
	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok || serviceInfo.UserID == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	tabMap := cache.GetTabInfoCache().GetTabIdCache()
	gameCardInfo := &gameCardPb.OfficialUpsertGameCardOptsReq{
		Uid:      serviceInfo.UserID,
		AppId:    req.GetBaseReq().GetAppId(),
		MarketId: serviceInfo.MarketID,
	}
	for _, tabId := range req.GetTabIds() {
		if tab, ok := tabMap[tabId]; !ok {
			log.ErrorWithCtx(ctx, "SumitRecallPopUp err: tabId not exist, tabId:%d", tabId)
			continue
		} else {
			if tab.GetGameInfo().GetGameCardId() != 0 {
				gameCardInfo.Infos = append(gameCardInfo.Infos, &gameCardPb.OfficialUpsertGameCardOptsReq_UpsertCardInfo{
					GameCardId: tab.GetGameInfo().GetGameCardId(),
					UGameId:    tab.GetGameInfo().GetUGameId(),
				})
			} else {
				log.ErrorWithCtx(ctx, "SumitRecallPopUp gamecardid not exist, tab:%s", tab.String())
			}
		}
	}
	if len(gameCardInfo.Infos) == 0 {
		log.ErrorWithCtx(ctx, "SubmitRecallPopUp game cnt 0, in:%s", req.String())
		return out, nil //protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请求参数错误")
	}

	_, err := rpc.GameCardCli.OfficialUpsertGameCardOpts(ctx, gameCardInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "gameCardCli.OfficialUpsertGameCardOpts err:%v, gameCardInfo:%s", err, gameCardInfo.String())
		return out, err
	}
	log.InfoWithCtx(ctx, "SumitRecallPopUp in:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) GetRecallTeamUp(ctx context.Context, req *game_play_logic.GetRecallTeamUpReq) (*game_play_logic.GetRecallTeamUpResp, error) {
	out := &game_play_logic.GetRecallTeamUpResp{}
	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok || serviceInfo.UserID == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}

	blockInfos, err := s.blockMgr.GetBlockByEntrance(ctx, req.GetTabId(), uint32(channel_play.GetSecondaryFilterReq_QUESTION), serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRecallTeamUp GetBlockByEntrance err:%v, serviceInfo:%s", err, serviceInfo.String())
		return out, err
	}
	out.Blocks = blockInfos
	log.InfoWithCtx(ctx, "GetRecallTeamUp in:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) SubmitRecallTeamUp(ctx context.Context, req *game_play_logic.SubmitRecallTeamUpReq) (*game_play_logic.SubmitRecallTeamUpResp, error) {
	out := &game_play_logic.SubmitRecallTeamUpResp{}
	log.InfoWithCtx(ctx, "SubmitRecallTeamUp, req: %+v", req)

	switch game_play_logic.SubmitTeamUpSource(req.GetSource()) {
	case game_play_logic.SubmitTeamUpSource_SUBMIT_TEAM_UP_SOURCE_RECALL:
		rcmdGameCardInfo, lowErr := s.reportRcmd(ctx, req)
		if lowErr != nil {
			log.WarnWithCtx(ctx, "SubmitRecallTeamUp reportRcmd err:%v, req:%s", lowErr, req.String())
		}
		lowErr = s.syncGameCardInfo(ctx, req, rcmdGameCardInfo)
		if lowErr != nil {
			log.WarnWithCtx(ctx, "SubmitRecallTeamUp syncGameCardInfo err:%v, req:%s", lowErr, req.String())
		}
	case game_play_logic.SubmitTeamUpSource_SUBMIT_TEAM_UP_SOURCE_QUESTION:
		rcmdGameCardInfo, lowErr := s.reportRcmd(ctx, req)
		if lowErr != nil {
			log.WarnWithCtx(ctx, "SubmitRecallTeamUp reportRcmd err:%v, req:%s", lowErr, req.String())
		}
		if len(req.GetQuestions()) > 0 || len(req.GetBlocks()) > 0 {
			lowErr = s.syncGameCardInfo(ctx, req, rcmdGameCardInfo)
			if lowErr != nil {
				log.WarnWithCtx(ctx, "SubmitRecallTeamUp syncGameCardInfo err:%v, req:%s", lowErr, req.String())
			}
		}
	case game_play_logic.SubmitTeamUpSource_SUBMIT_TEAM_UP_SOURCE_ROOM_PREFER_PEOPLE:
		_, lowErr := s.reportRcmd(ctx, req)
		if lowErr != nil {
			log.WarnWithCtx(ctx, "SubmitRecallTeamUp reportRcmd err:%v, req:%s", lowErr, req.String())
		}
	default:
		log.WarnWithCtx(ctx, "SubmitRecallTeamUp source not support, req:%s", req.String())
	}

	return out, nil
}

func (s *Server) reportRcmd(ctx context.Context, req *game_play_logic.SubmitRecallTeamUpReq) (
	[]*rcmd_channel_label.GameCardInfo, error) {
	svcInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)

	var rcmdGameCards []*rcmd_channel_label.GameCardInfo
	if !ok || svcInfo.UserID == 0 {
		return rcmdGameCards, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	if elemVals, textPair := genRcmdParams(req); len(textPair) > 0 || len(elemVals) > 0 {
		rcmdReq := &rcmd_channel_label.MatchGameCardInfoReq{
			Uid:          svcInfo.UserID,
			TabId:        req.GetTabId(),
			Texts:        elemVals,
			TextPairList: textPair,
		}
		rcmdResp, err := rpc.RcmdChannelLabelCli.MatchGameCardInfo(ctx, rcmdReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "reportRcmd MatchGameCardInfo serviceInfo:%s req:%s rcmdReq:%s lowErr:%v",
				svcInfo.String(), req.String(), rcmdReq.String(), err)
			return rcmdGameCards, err
		}

		rcmdGameCards = rcmdResp.GetGameCardInfo()
		log.InfoWithCtx(ctx, "reportRcmd req:%s rcmdReq:%s rcmdRsp:%s ", req.String(), rcmdReq.String(), rcmdResp.String())
	}
	return rcmdGameCards, nil
}

func (s *Server) syncGameCardInfo(ctx context.Context, req *game_play_logic.SubmitRecallTeamUpReq,
	rcmdGameCards []*rcmd_channel_label.GameCardInfo) error {
	svcInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok || svcInfo.UserID == 0 {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}

	tabInfo := cache.GetTabInfoCache().GetTabInfoCacheById(req.GetTabId())
	if tabInfo == nil {
		log.WarnWithCtx(ctx, "SyncGameCardInfo tabInfo is nil, tabId:%d", req.GetTabId())
		return nil
	}

	confResp, err := rpc.GameCardCli.GetGameCardConfByCardIdFromCache(ctx, &gameCardPb.GetGameCardConfByCardIdFromCacheReq{
		GameCardId: []uint32{tabInfo.GetGameInfo().GetGameCardId()},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SyncGameCardInfo GetGameCardConfByCardIdFromCach err:%v", err)
		return err
	}
	if len(confResp.GetGameCardConfList()) == 0 {
		log.WarnWithCtx(ctx, "SyncGameCardInfo no gameCardCOnf serviceInfo:%s req:%s", svcInfo.String(), req.String())
		return nil
	}

	gameCardConfig := confResp.GetGameCardConfList()[0]
	if gameCardConfig.GetCardScopeType() == gameCardPb.CardScopeType_CARD_SCORE_TYPE_CATEGORY {
		// 通用游戏卡配置不同步配置
		return nil
	}

	upsertCardReq := &gameCardPb.OfficialUpsertGameCardOptsReq{
		Uid: svcInfo.UserID,
		Infos: []*gameCardPb.OfficialUpsertGameCardOptsReq_UpsertCardInfo{
			{
				GameCardId: tabInfo.GetGameInfo().GetGameCardId(),
				OptList:    genOptList(gameCardConfig, rcmdGameCards),
				UGameId:    tabInfo.GetGameInfo().GetUGameId(),
			},
		},
		AppId:    req.GetBaseReq().GetAppId(),
		MarketId: svcInfo.MarketID,
	}
	_, err = rpc.GameCardCli.OfficialUpsertGameCardOpts(ctx, upsertCardReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "SyncGameCardInfo OfficialUpsertGameCardOpts serviceInfo:%s req:%s lowErr:%v",
			svcInfo.String(), req.String(), err)
		return err
	}
	log.InfoWithCtx(ctx, "SubmitRecallTeamUp OfficialUpsertGameCardOpts req(%s) finished", upsertCardReq.String())

	return nil
}

func genRcmdParams(req *game_play_logic.SubmitRecallTeamUpReq) ([]string, []*rcmd_channel_label.TextPair) {
	tabId := req.GetTabId()
	blocks := req.GetBlocks()
	questions := req.GetQuestions()
	ageLabel := req.GetAgeGroupLabels()

	var elemVals []string
	textPair := make([]*rcmd_channel_label.TextPair, 0, len(questions)+1)
	elemMap := cache.GetTabInfoCache().GetElemMapByTabId(tabId)
	if len(elemMap) > 0 && len(blocks) > 0 {
		for _, block := range blocks {
			for _, elem := range block.GetElems() {
				elemVals = append(elemVals, elemMap[elem.GetId()].GetTitle())
			}
		}
		textPair = append(textPair, &rcmd_channel_label.TextPair{
			ValueText: elemVals,
			TextType:  rcmd_channel_label.TextPair_TextTypeMatchTeam,
		})

	}
	if len(questions) > 0 {
		for _, v := range questions {
			var textType rcmd_channel_label.TextPair_TextType
			if len(v.GetLabelVals()) > 0 {
				textType = rcmd_channel_label.TextPair_TextTypeOfficeQuestion
			} else {
				// 用户自定义问题内容
				textType = rcmd_channel_label.TextPair_TextTypeCustomQuestion
			}
			textPair = append(textPair, &rcmd_channel_label.TextPair{
				ValueText: v.GetLabelVals(),
				TitleText: v.GetTitle(),
				TextType:  textType,
			})
		}
	}

	if len(ageLabel) > 0 {
		textPair = append(textPair, &rcmd_channel_label.TextPair{
			ValueText: ageLabel,
			TextType:  rcmd_channel_label.TextPair_TextTypeAgeQuestion,
		})
	}
	return elemVals, textPair
}

func genOptList(confInfo *gameCardPb.GameCardConfInfo, rcmdGameCardInfos []*rcmd_channel_label.GameCardInfo) []*gameCardPb.GameCardOpt {
	var res []*gameCardPb.GameCardOpt

	confOptMap := make(map[uint32]*gameCardPb.GameCardOptConf)
	for _, opt := range confInfo.GetOptConfList() {
		confOptMap[opt.GetOptId()] = opt
	}
	for _, info := range rcmdGameCardInfos {
		optConf := confOptMap[info.GetOptId()]
		res = append(res, &gameCardPb.GameCardOpt{
			OptName:   optConf.GetOptName(),
			ValueList: info.GetOptValueList(),
			OptId:     info.GetOptId(),
			OptType:   optConf.GetOptType(),
		})
	}
	return res
}

func (s *Server) GetUserAcquisitionAndABTestResult(ctx context.Context, req *game_play_logic.GetUserAcquisitionAndABTestResultReq) (*game_play_logic.GetUserAcquisitionAndABTestResultResp, error) {
	out := &game_play_logic.GetUserAcquisitionAndABTestResultResp{}
	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok || serviceInfo.UserID == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}

	user, rpcErr := rpc.AccountGoClient.GetUserByUid(ctx, serviceInfo.UserID)
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "GetUserAcquisitionAndABTestResult GetUserByUid uid(%d) err: %v", serviceInfo.UserID, rpcErr)
		return out, rpcErr
	}

	deviceId, err := device_id.ToClientDeviceId(device_id.ToDeviceHexId(serviceInfo.DeviceID, true), uint32(serviceInfo.ClientType))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserAcquisitionAndABTestResult device_id.ToClientDeviceId deviceID(%s) err:%v", serviceInfo.DeviceID, err)
		return out, err
	}

	// 查询用户获客状态
	userType, err := rpc.DeviceInfoCli.GetUserTypeByDeviceState(ctx, &device_pb.DeviceInfoRequestData{
		AppId:    marketid_helper.GetAppName(serviceInfo.MarketID),
		DeviceId: deviceId,
		Uid:      strconv.FormatInt(int64(serviceInfo.UserID), 10),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserAcquisitionAndABTestResult GetUserTypeByDeviceState err:%v, serviceInfo:%s", err, serviceInfo.String())
		return out, err
	}

	out.AcquisitionStatus = uint32(userType)

	// 查询AB实验结果
	abCfg := config.GetGamePlayLogicConfig().GetABTestCfg(game_play_logic.ABTest(req.GetAbTest()))
	if abCfg == nil {
		log.WarnWithCtx(ctx, "GetUserAcquisitionAndABTestResult abCfg is nil, req.GetAbTest():%d", req.GetAbTest())
		return out, nil
	}

	switch abCfg.Type {
	case config.TestTypeLayer:
		if out.Result, err = rpc.AbTestClient.GetABTestResult(ctx, serviceInfo.UserID, serviceInfo.DeviceID, uint32(serviceInfo.ClientType), abCfg.LayerTag, abCfg.ArgName, abCfg.ClientType); err != nil {
			log.ErrorWithCtx(ctx, "GetUserAcquisitionAndABTestResult GetABTestResult err:%v, serviceInfo:%s, req:%+v, abCfg:%+v", err, serviceInfo.String(), req, abCfg)
			return out, err
		}
	case config.TestTypeRcmd:
		argKey := abCfg.RcmdTestCfg.ActiveKey
		if timex.FromUnix(int64(user.GetRegisteredAt())).After(timex.TodayStart()) || userType == device_info_service.Recall || userType == device_info_service.Pull {
			argKey = abCfg.RcmdTestCfg.NewKey
		}
		if abCfg.ClientType == uint32(abtest.PS_AbtestClientType_PS_AbtestClientType_UID) {
			if out.Result, err = rpc.AbTestClient.GetUidTestArgVal(ctx, serviceInfo.UserID, argKey); err != nil {
				log.ErrorWithCtx(ctx, "GetUserAcquisitionAndABTestResult GetUidTestArgVal uid(%d) argKey(%s) err: %v", serviceInfo.UserID, argKey, err)
				return out, err
			}
			if out.GetResult() == "" {
				log.DebugWithCtx(ctx, "out.GetResult() is nil, abCfg:%+v", abCfg)
				if out.Result, err = rpc.AbTestClient.GetDeviceTestArgVal(ctx, deviceId, argKey); err != nil {
					log.ErrorWithCtx(ctx, "GetUserAcquisitionAndABTestResult GetDeviceTestArgVal deviceId(%s) argKey(%s) err: %v", deviceId, argKey, err)
					return out, err
				}
			}
		} else if abCfg.ClientType == uint32(abtest.PS_AbtestClientType_PS_AbtestClientType_Device) {
			if out.Result, err = rpc.AbTestClient.GetDeviceTestArgVal(ctx, deviceId, argKey); err != nil {
				log.ErrorWithCtx(ctx, "GetUserAcquisitionAndABTestResult GetDeviceTestArgVal deviceId(%s) argKey(%s) err: %v", deviceId, argKey, err)
				return out, err
			}
		} else {
			log.ErrorWithCtx(ctx, "GetUserAcquisitionAndABTestResult invalid abCfg.ClientType err: %d", abCfg.ClientType)
			return out, nil
		}

	default:
		log.WarnWithCtx(ctx, "GetUserAcquisitionAndABTestResult invalid abCfg.Type: %d", abCfg.Type)
	}

	log.InfoWithCtx(ctx, "GetUserAcquisitionAndABTestResult req:%+v, out:%+v, abCfg:%+v", req, out, abCfg)
	return out, nil
}

func (s *Server) GetRegisterPageConfigs(ctx context.Context, req *game_play_logic.GetRegisterPageConfigsReq) (
	*game_play_logic.GetRegisterPageConfigsResp, error) {
	out := &game_play_logic.GetRegisterPageConfigsResp{}
	out.Configs = config.GetGamePlayLogicConfig().GetRegisterPageConfigs()
	log.InfoWithCtx(ctx, "GetRegisterPageConfigs out:%s", out.String())
	return out, nil
}

func (s *Server) CheckUserInRoom(ctx context.Context, req *game_play_logic.CheckUserInRoomReq) (*game_play_logic.CheckUserInRoomResp, error) {
	out := &game_play_logic.CheckUserInRoomResp{}
	log.DebugWithCtx(ctx, "CheckUserInRoom req:%s", req.String())

	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok || serviceInfo.UserID == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}

	pres, err := rpc.PresenceClient.GetUserPres(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckUserInRoom GetUserPres err:%v, req:%s", err, req.String())
		return out, err
	}
	// 用户不在线
	if pres.GetInfoList() == nil {
		out.IsInRoom = false
		out.Toast = "Ta离线了"
	} else {
		// 查询用户是否在房
		cid, err := rpc.ChannelOlClient.GetUserChannelId(ctx, serviceInfo.UserID, req.GetUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckUserInRoom GetUserChannelId err:%v, req:%s", err, req.String())
			return out, err
		}
		log.DebugWithCtx(ctx, "CheckUserInRoom GetUserChannelId uid:%d, cid:%d", req.GetUid(), cid)
		if cid == 0 {
			out.IsInRoom = false
			out.Toast = "Ta当前不在房间，快跟Ta聊聊吧~"
		} else {
			out.IsInRoom = true
		}
	}
	log.InfoWithCtx(ctx, "CheckUserInRoom req:%s, out:%s", req.String(), out.String())
	return out, nil
}
