package rpc

import (
	"context"
	game_server_v2 "golang.52tt.com/clients/game-server-v2"
	risk_mng_api_cli "golang.52tt.com/clients/risk-mng-api"
	game_fre_server "golang.52tt.com/protocol/services/game-fre-server"

	"google.golang.org/grpc"

	"golang.52tt.com/clients/account"
	account_go "golang.52tt.com/clients/account-go"
	channel_play_tab "golang.52tt.com/clients/channel-play-tab"
	"golang.52tt.com/clients/channelol"
	"golang.52tt.com/clients/configserver"
	device_info_service "golang.52tt.com/clients/datacenter/device-info-service"
	"golang.52tt.com/clients/expsvr"
	game_pal "golang.52tt.com/clients/game-pal"
	im_api "golang.52tt.com/clients/im-api"
	presence "golang.52tt.com/clients/presence/v2"
	pushNotificationClient "golang.52tt.com/clients/push-notification/v2"
	rcmd_game_pal_user "golang.52tt.com/clients/rcmd/rcmd-game-pal-user"
	"golang.52tt.com/clients/topic-channel/tab"
	ttc_proxy "golang.52tt.com/clients/ttc-proxy"
	"golang.52tt.com/clients/ugc/friendship"
	user_auth_history "golang.52tt.com/clients/user-auth-history"
	"golang.52tt.com/pkg/abtest"
	"golang.52tt.com/pkg/log"
	channel_play_index "golang.52tt.com/protocol/services/channel-play-index"
	gamecard "golang.52tt.com/protocol/services/game-card"
	game_time "golang.52tt.com/protocol/services/game-time/game-time"
	game_user_rate "golang.52tt.com/protocol/services/game-user-rate"
	"golang.52tt.com/protocol/services/rcmd/rcmd_statistics_query"
	"golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
)

const (
	AbtestUrl = "http://abtestlogic.commonservice.svc.cluster.local:8000/AbtestLogicService/GetUsersAbtestByTag"
)

var (
	AccountClient          account.IClient
	AccountGoClient        account_go.IClient
	GameUserRateClient     game_user_rate.GameUserRateClient
	FriendshipCli          friendship.IClient
	PushClient             pushNotificationClient.IClient
	GamePalClient          game_pal.IClient
	UserAuthHistoryClient  user_auth_history.IClient
	ImApiClient            im_api.IClient
	RcmdStatisticsQueryCli *rcmd_statistics_query.Client
	ExpClient              *expsvr.Client
	RealNameClient         *ttc_proxy.Client
	GameServerV2Client     *game_server_v2.Client
	GameCardCli            gamecard.GameCardClient
	TabClient              *tab.Client
	ChannelPlayTabClient   channel_play_tab.IClient
	DeviceInfoCli          device_info_service.IClient
	IndexCli               *channel_play_index.Client
	RcmdChannelLabelCli    rcmd_channel_label.RCMDChannelLabelClient
	AbTestClient           abtest.IABTestClient
	ConfigServerClient     *configserver.Client
	RcmdGamePalUserClient  rcmd_game_pal_user.IClient
	PresenceClient         presence.IClient
	GameTimeClient         game_time.GameTimeClient
	ChannelOlClient        channelol.IClient

	RiskMngApiClient risk_mng_api_cli.IClient
	GameFreClient    game_fre_server.GameFreServerClient
)

func InitClients(ctx context.Context) error {
	opts := []grpc.DialOption{grpc.WithBlock()}
	var err error
	GameUserRateClient, err = game_user_rate.NewClient(ctx)
	if nil != err {
		log.ErrorWithCtx(ctx, "Failed to new game_user_rate client, err: %+v", err)
		return err
	}

	FriendshipCli, _ = friendship.NewClient()
	PushClient, _ = pushNotificationClient.NewClient()
	AccountClient, err = account.NewClient()
	if err != nil {
		log.Errorf("account.NewClient err: %v", err)
		return err
	}

	AccountGoClient, err = account_go.NewClient()
	if err != nil {
		log.Errorf("account_go.NewClient err: %v", err)
		return err
	}

	GamePalClient, err = game_pal.NewClient(opts...)
	if err != nil {
		log.Errorf("game_pal.NewClient err: %v", err)
		return err
	}

	UserAuthHistoryClient, _ = user_auth_history.NewClient(opts...)

	ImApiClient, err = im_api.NewClient()
	if err != nil {
		log.Errorf("im_api.NewClient err: %v", err)
		return err
	}

	RcmdStatisticsQueryCli, _ = rcmd_statistics_query.NewClient(ctx)

	TabClient, err = tab.NewClient(opts...)
	if err != nil {
		log.Errorf("tab.NewClient err")
		return err
	}

	ChannelPlayTabClient, err = channel_play_tab.NewClient(opts...)
	if err != nil {
		log.Errorf("channel_play_tab.NewClient err: %v", err)
		return err
	}

	GameServerV2Client, err = game_server_v2.NewClient()
	if err != nil {
		log.Errorf("game_server_v2.NewClient err: %v", err)
		return err
	}

	GameCardCli, err = gamecard.NewClient(ctx)
	if err != nil {
		log.Errorf("gamecard.NewClient err: %v", err)
		return err
	}

	ExpClient = expsvr.NewClient(opts...)
	RealNameClient, err = ttc_proxy.NewClient(opts...)
	if err != nil {
		log.Errorf("ttc_proxy.NewClient err: %v", err)
		return err
	}

	DeviceInfoCli, err = device_info_service.NewClientToDeviceService()
	if err != nil {
		log.Errorf("device_info_service.NewClient err: %v", err)
		return err
	}

	IndexCli, err = channel_play_index.NewClient(ctx)
	if err != nil {
		log.Errorf("channel_play_index.NewClient err: %v", err)
		return err
	}

	RcmdChannelLabelCli, err = rcmd_channel_label.NewClient(ctx)
	if err != nil {
		log.Errorf("rcmd_channel_label.NewClient err: %v", err)
		return err
	}

	GameTimeClient, err = game_time.NewClient(ctx)
	if err != nil {
		log.Errorf("game_time.NewClient err: %v", err)
		return err
	}

	AbTestClient = abtest.NewABTestClient(AbtestUrl, uint32(abtest.APPID_TTyuyin), "")

	PresenceClient, _ = presence.NewClient()

	ChannelOlClient = channelol.NewClient()

	RiskMngApiClient, _ = risk_mng_api_cli.NewClient()

	GameFreClient, _ = game_fre_server.NewClient(ctx)

	return nil
}
