package internal

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	account_go "golang.52tt.com/protocol/services/account-go"
	aigc_common "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_ext_content "golang.52tt.com/protocol/services/aigc/aigc-ext-content"
	config "golang.52tt.com/services/user/web-im-logic/internal/config/ttconfig/web_im_logic"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	game_red_dot_logic "golang.52tt.com/protocol/app/game-red-dot-logic"
	pb "golang.52tt.com/protocol/app/web-im-logic"
	"golang.52tt.com/protocol/common/status"
	aigc_group "golang.52tt.com/protocol/services/aigc/aigc-group"
	aigc_push "golang.52tt.com/protocol/services/aigc/aigc-push"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	aigc_soulmate_middle "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	aigc_trigger "golang.52tt.com/protocol/services/aigc/aigc-trigger"
	chat_bot "golang.52tt.com/protocol/services/chat-bot"
	game_red_dot "golang.52tt.com/protocol/services/game-red-dot"
	rcmd_ai_partner_pb "golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner"
	"golang.52tt.com/services/user/web-im-logic/internal/event"

	"google.golang.org/grpc/codes"
)

func (s *Server) getPushUids(ctx context.Context, groupId uint32) ([]uint32, error) {
	groupReq := &aigc_group.GetGroupMemberListRequest{
		GroupId: groupId,
	}

	groupMembemResp, err := s.groupClient.GetGroupMemberList(ctx, groupReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkMsg GetGroupMember err:%v, req:%s", err, groupReq.String())
		return nil, err
	}

	var pushUids []uint32
	for _, member := range groupMembemResp.GetList() {
		if member.GetType() == aigc_group.GroupMemberType_GROUP_MEMBER_TYPE_USER {
			pushUids = append(pushUids, member.GetId())
		}
	}

	if len(pushUids) == 0 {
		log.ErrorWithCtx(ctx, "SendGroupMsg GetGroupMemberList err: pushUid 0, groupReq:%+v, groupMembemResp:%+v", groupReq, groupMembemResp)
		return nil, nil
	}
	return pushUids, nil
}
func (s *Server) SendGroupImMsg(ctx context.Context, in *pb.SendGroupImMsgRequest) (out *pb.SendGroupImMsgResponse, err error) {
	out = &pb.SendGroupImMsgResponse{}

	if in.GetCmd() != pb.WebImCmd_WEB_IM_CMD_AI_GROUP || in.GetMsg() == nil {
		log.ErrorWithCtx(ctx, "SendGroupImMsg err, in:%s", in.String())
		return out, protocol.NewExactServerError(codes.InvalidArgument, status.ErrRequestParamInvalid)
	}
	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.WarnWithCtx(ctx, "SendGroupImMsg invalid user")
		return out, nil
	}

	templateInfoResp, err := s.groupClient.GetTemplateByGroupId(ctx, &aigc_group.GetTemplateByGroupIdRequest{
		GroupId: in.GetGroupId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SendGroupImMsg GetGroupTemplateByIds err:%v, req:%s", err, in.String())
		return out, err
	}

	var uid2NameMap map[uint32]*rcmd_ai_partner_pb.ReceiveGroupMsgFromUserReq_UserInfo
	var aiGroupType rcmd_ai_partner_pb.AIGroupType
	if templateInfoResp.GetGroupTemplate().GetGroupType() == aigc_group.GroupType_GROUP_TYPE_SINGLE_USER {
		aiGroupType = rcmd_ai_partner_pb.AIGroupType_AIGroupType_SingleUser
	} else if templateInfoResp.GetGroupTemplate().GetGroupType() == aigc_group.GroupType_GROUP_TYPE_MULTI_USER {
		aiGroupType = rcmd_ai_partner_pb.AIGroupType_AIGroupType_MultiUser

		groupReq := &aigc_group.GetGroupMemberListRequest{
			GroupId: in.GetGroupId(),
		}
		groupMembemResp, err := s.groupClient.GetGroupMemberList(ctx, groupReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "checkMsg GetGroupMember err:%v, req:%s", err, in.String())
			return out, err
		}

		var chatUids []uint32
		for _, info := range groupMembemResp.GetList() {
			if info.GetType() == aigc_group.GroupMemberType_GROUP_MEMBER_TYPE_USER {
				chatUids = append(chatUids, info.GetId())
			}
		}

		uid2NameMap = make(map[uint32]*rcmd_ai_partner_pb.ReceiveGroupMsgFromUserReq_UserInfo, len(chatUids))
		if userResp, err := s.accountClient.GetUsersByUids(ctx, chatUids); err == nil {
			for _, userInfo := range userResp.GetUserList() {
				uid2NameMap[userInfo.GetUid()] = &rcmd_ai_partner_pb.ReceiveGroupMsgFromUserReq_UserInfo{
					Name: userInfo.GetNickname(),
					Sex:  uint32(userInfo.GetSex()),
				}
			}
		} else {
			log.ErrorWithCtx(ctx, "SendGroupImMsg accountClient.GetUsersByUids err:%v, req:%s", err, in.String())
		}

	} else if templateInfoResp.GetGroupTemplate().GetGroupType() == aigc_group.GroupType_GROUP_TYPE_MULTI_USER_SCRIPT {
		groupRoleInfoResp, err := s.groupClient.GetMultiGroupUserRoleInfo(ctx, &aigc_group.GetMultiGroupUserRoleInfoRequest{GroupId: in.GetGroupId()})
		if err != nil {
			log.ErrorWithCtx(ctx, "SendGroupImMsg groupClient.GetMultiGroupUserRoleInfo err:%v", err)
			return out, err
		}
		uid2NameMap = make(map[uint32]*rcmd_ai_partner_pb.ReceiveGroupMsgFromUserReq_UserInfo, len(groupRoleInfoResp.GetUserSelectRoleMap()))
		for _, userSelectInfo := range groupRoleInfoResp.GetUserSelectRoleMap() {
			uid2NameMap[userSelectInfo.GetUid()] = &rcmd_ai_partner_pb.ReceiveGroupMsgFromUserReq_UserInfo{
				Name:       userSelectInfo.GetUserRoleName(),
				PlayRoleId: userSelectInfo.GetUserRoleId(),
			}
		}
		aiGroupType = rcmd_ai_partner_pb.AIGroupType_AIGroupType_MultiUserScript
	} else {
		log.ErrorWithCtx(ctx, "SendGroupImMsg group type err, type:%d, req:%s", templateInfoResp.GetGroupTemplate().GetGroupType(), in.String())
		return out, err
	}

	timeLineMsg := &aigc_soulmate_middle.GroupTimeLineMsg{
		GroupId:         in.GetGroupId(),
		GroupTemplateId: in.GetGroupTemplateId(),
		RoleIds:         in.GetRoleIds(),
		Msg: &aigc_soulmate_middle.ImMsg{
			Type:        aigc_soulmate_middle.ImMsgType(in.GetMsg().GetType()),
			Content:     in.GetMsg().GetContent(),
			Ext:         in.GetMsg().GetExt(),
			SentAt:      in.GetMsg().GetSentAt(),
			ImBusiType:  in.GetMsg().GetImBusiType(),
			ContentType: in.GetMsg().GetContentType(),
			ImCmdType:   in.GetMsg().GetImCmdType(),
			RcmdContent: in.GetMsg().GetContent(),
		},
		Uid:           uid,
		GroupSendType: aigc_soulmate_middle.GroupSendType_GroupSendTypeUser2AI,
		AtUids:        in.GetAtUids(),
		AtRoleIds:     in.GetRoleIds(),
	}

	isUseUp := false
	extMap := make(map[string]string)
	if templateInfoResp.GetGroupTemplate().GetGroupType() == aigc_group.GroupType_GROUP_TYPE_MULTI_USER ||
		templateInfoResp.GetGroupTemplate().GetGroupType() == aigc_group.GroupType_GROUP_TYPE_MULTI_USER_SCRIPT {
		addResp, err := s.triggleCli.AddBussSentenceCount(ctx, &aigc_trigger.AddBussSentenceCountRequest{
			Uid: uid,
			Entity: &aigc_trigger.Entity{
				Id:   in.GetGroupId(),
				Type: aigc_trigger.Entity_TYPE_MUTI_GROUP,
			},
			Type:         uint32(aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_CUR_DAY),
			BusinessType: aigc_trigger.BusinessType_BUSINESS_TYPE_MUTI_PLAYER,
			MustSendTip:  len(in.GetRoleIds()) > 0,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "SendGroupImMsg triggleCli.AddBussSentenceCount err:%v, req:%s", err, in.String())
			return out, err
		}
		if !addResp.GetSuccess() {
			isUseUp = true
		}
	} else if templateInfoResp.GetGroupTemplate().GetGroupType() == aigc_group.GroupType_GROUP_TYPE_SINGLE_USER {
		abtestCtx, cancel := context.WithTimeout(ctx, 200*time.Millisecond)
		defer cancel()
		cfg := config.GetWebImLogicConfig().GetSentenceAbtest()
		var useTTHint bool
		if cfg == nil || cfg.ArgvName == "" {
			useTTHint = false
		} else {
			if value, err := s.abTestClient.GetUidTestArgVal(abtestCtx, uid, cfg.ArgvName); err != nil {
				log.ErrorWithCtx(abtestCtx, "SendMsg GetUidTestArgVal uid(%d) argvName(%s) err: %v", uid, cfg.ArgvName, err)
				useTTHint = false
			} else {
				useTTHint = value == cfg.ExpectValue
			}
		}

		if useTTHint {
			extMap["soulmate_sentence_refactor_gray_release"] = "tt"
			sentenceRsp, err := s.soulmateMiddleCli.ConsumeSentenceCount(ctx, &aigc_soulmate_middle.ConsumeSentenceCountReq{
				Uid: uid,
				Entity: &aigc_soulmate_middle.Entity{
					Id:   in.GetGroupId(),
					Type: aigc_soulmate_middle.Entity_TYPE_SINGLE_GROUP,
				},
				RoleType:     uint32(aigc_soulmate.AIRoleType_AIRoleTypeGame),
				BusinessType: uint32(aigc_trigger.BusinessType_BUSINESS_TYPE_SINGLE_CHAT),
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "SendGroupImMsg ConsumeSentenceCount err:%v, req:%s", err, in.String())
				return out, err
			}
			if sentenceRsp.GetNeedExtraTip() {
				err = s.pushGroupTipMsg(ctx, uid, in.GetGroupId(), timeLineMsg)
				if err != nil {
					log.ErrorWithCtx(ctx, "SendGroupImMsg pushGroupTipMsg err:%v, req:%s", err, in.String())
					return out, nil
				}
			}
			if !sentenceRsp.GetSuccess() {
				log.InfoWithCtx(ctx, "SendGroupImMsg, in:%s,sentenceRsp:%s", in.String(), sentenceRsp.String())
				return out, nil
			}
			extMap["sentence_type"] = sentenceRsp.GetUsedType().String()
			var tipType int
			if sentenceRsp.GetNeedExtraTip() {
				tipType = int(aigc_push.TipType_TIP_TYPE_EXTRA)
			} else if sentenceRsp.GetNeedCurDayTip() {
				tipType = int(aigc_push.TipType_TIP_TYPE_CUR_DAY)
			} else if sentenceRsp.GetNeedSpecialTip() {
				tipType = int(aigc_push.TipType_TIP_TYPE_ROLE_SPECIAL)
			} else {
				tipType = int(aigc_push.TipType_TIP_TYPE_UNSPECIFIED)
			}
			extMap[aigc_ext_content.ExtMapKey_EXT_MAP_KEY_SENTENCE_TIP.String()] = strconv.Itoa(tipType)

		} else {
			extMap["soulmate_sentence_refactor_gray_release"] = "aigc"
			if config.GetWebImLogicConfig().GetSentenceSwitch() {
				// 扣句数
				sentenceRsp, err := s.soulmateMiddleCli.ConsumeSentenceCount(ctx, &aigc_soulmate_middle.ConsumeSentenceCountReq{
					Uid: uid,
					Entity: &aigc_soulmate_middle.Entity{
						Id:   in.GetGroupId(),
						Type: aigc_soulmate_middle.Entity_TYPE_SINGLE_GROUP,
					},
					RoleType:     uint32(aigc_soulmate.AIRoleType_AIRoleTypeGame),
					BusinessType: uint32(aigc_trigger.BusinessType_BUSINESS_TYPE_SINGLE_CHAT),
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "SendGroupImMsg ConsumeSentenceCount err:%v, req:%s", err, in.String())
				}
				log.InfoWithCtx(ctx, "SendGroupImMsg use aigc hint sentenceRsp:%s", sentenceRsp.String())
			}
		}
	}

	sendMsgReq := &aigc_soulmate_middle.SendGroupImMsgReq{
		TimeLineMsg:  timeLineMsg,
		IsReachLimit: isUseUp,
	}
	sendMsgResp, err := s.soulmateMiddleCli.SendGroupImMsg(ctx, sendMsgReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendGroupImMsg soulmateMiddleCli.SendGroupImMsg err, err:%v", err)
		return out, err
	}
	out.SentAt = sendMsgResp.GetSentAt()
	out.SeqId = sendMsgResp.GetSeqId()
	timeLineMsg.Msg.SentAt = sendMsgResp.GetSentAt()

	if !isUseUp {
		reqRcmd := &rcmd_ai_partner_pb.ReceiveGroupMsgFromUserReq{
			Uid:             uid,
			GroupTemplateId: in.GetGroupTemplateId(),
			GroupInstanceId: in.GetGroupId(),
			MsgType:         rcmd_ai_partner_pb.ReceiveGroupMsgFromUserReq_MsgType(in.GetMsg().GetType()),
			Content:         in.GetMsg().GetContent(),
			Ext:             in.GetMsg().GetExt(),
			SeqId:           sendMsgResp.GetSeqId(),
			AiGroupType:     aiGroupType,
			TargetRoleIds:   in.GetRoleIds(),
			TargetUids:      in.GetAtUids(),
			UidToInfo:       uid2NameMap,
			ExtraMap:        extMap,
		}
		_, err = s.aiClient.ReceiveGroupMsgFromUser(ctx, reqRcmd)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendGroupImMsg aiClient.ReceiveGroupMsgFromUser err:%v", err)
			return out, err
		}
		log.DebugWithCtx(ctx, "ReceiveGroupMsgFromUser req:%s, in:%s", reqRcmd.String(), in.String())
	}

	if templateInfoResp.GetGroupTemplate().GetGroupType() == aigc_group.GroupType_GROUP_TYPE_MULTI_USER ||
		templateInfoResp.GetGroupTemplate().GetGroupType() == aigc_group.GroupType_GROUP_TYPE_MULTI_USER_SCRIPT {
		groupInfoResp, err := s.groupClient.GetGroupInfo(ctx, &aigc_group.GetGroupInfoRequest{Id: in.GetGroupId()})
		if err != nil {
			log.ErrorWithCtx(ctx, "SendGroupImMsg GetGroupInfo err:%v, req:%s", err, in.GetGroupId())
			return out, err
		}
		if groupInfoResp.GetInfo() == nil {
			log.WarnWithCtx(ctx, "SendGroupImMsg GetGroupInfo groupInfoResp nil, uid:%d groupId:%d", uid, in.GetGroupId())
			return out, err
		}

		pushUids, err := s.getPushUids(ctx, in.GetGroupId())
		if err != nil {
			log.ErrorWithCtx(ctx, "SendImMsgAIToUser getPushUids err: %v", err)
			return out, err
		}
		if len(pushUids) == 0 {
			return out, nil
		}

		var (
			msg   = in.GetMsg()
			group = groupInfoResp.GetInfo()
		)
		pushReq := &aigc_push.PushGroupMsgRequest{
			Group: &aigc_push.Group{
				Id:       group.GetId(),
				Type:     uint32(group.GetType()),
				TemplId:  group.GetTemplateId(),
				Name:     group.GetName(),
				Avatar:   group.GetAvatar(),
				ImTabTag: group.GetImTabTag(),
			},
			Sender: &aigc_push.Peer{
				Id:   uid,
				Type: aigc_push.Peer_TYPE_USER,
				Name: uid2NameMap[uid].GetName(),
			},
			MemberUidList: pushUids,
			Msg: &aigc_push.ImMsg{
				Seq:         sendMsgResp.GetSeqId(),
				SentAt:      sendMsgResp.GetSentAt(),
				Type:        msg.GetType(),
				Content:     msg.GetContent(),
				Ext:         msg.GetExt(),
				CmdType:     msg.GetImCmdType(),
				BusiType:    msg.GetImBusiType(),
				ContentType: msg.GetContentType(),
				AtUids:      in.GetAtUids(),
				AtRoleIds:   in.GetRoleIds(),
			},
		}
		if _, err := s.pusher.PushGroupMsg(ctx, pushReq); err != nil {
			log.ErrorWithCtx(ctx, "SendGroupImMsg PushGroupMsg req(%+v) err: %v", pushReq, err)
			// 推送失败不返回错误
		}
	}

	// 群聊消息kafka事件
	_ = s.eventBus.Publish(ctx, event.PubEventAIGroupMsg, strconv.Itoa(int(in.GetGroupId())), &chat_bot.AIGroupMsgEvent{
		Sender: &chat_bot.AIGroupMsgEvent_Sender{
			Id:   uid,
			Type: chat_bot.AIGroupMsgEvent_Sender_TYPE_USER,
		},
		Msg:       timeLineMsg,
		GroupType: uint32(templateInfoResp.GetGroupTemplate().GetGroupType()),
		IsUseUp:   isUseUp,
	})

	log.InfoWithCtx(ctx, "SendGroupImMsg, in:%s, resp:%s, isUseUp:%t, uid2NameMap:%+v", in.String(), sendMsgResp.String(), isUseUp, uid2NameMap)
	return out, nil
}

func (s *Server) pushGroupTipMsg(ctx context.Context, uid, groupId uint32, timeLineMsg *aigc_soulmate_middle.GroupTimeLineMsg) error {
	groupRsp, err := s.groupClient.GetGroupInfo(ctx, &aigc_group.GetGroupInfoRequest{
		Id: groupId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupInfo err:%v, groupId:%d", err, groupId)
		return err
	}
	if groupRsp.GetInfo() == nil {
		log.ErrorWithCtx(ctx, "GetGroupInfo groupInfoResp nil, uid:%d groupId:%d", uid, groupId)
		return nil
	}
	pushReq := &aigc_push.PushSentenceTipToGroupRequest{
		TipType: aigc_push.TipType_TIP_TYPE_EXTRA,
		Uid:     uid,
		Group: &aigc_push.Group{
			Id:       groupRsp.GetInfo().GetId(),
			Type:     uint32(groupRsp.GetInfo().GetType()),
			TemplId:  groupRsp.GetInfo().GetTemplateId(),
			Name:     groupRsp.GetInfo().GetName(),
			Avatar:   groupRsp.GetInfo().GetAvatar(),
			ImTabTag: groupRsp.GetInfo().GetImTabTag(),
		},
	}
	//_, err = s.pusher.PushSentenceTipToGroup(ctx, pushReq)
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "pushGroupTipMsg PushSentenceTipToGroup req(%s) err: %v", pushReq.String(), err)
	//}
	// 走延时
	marshalBytes, err := proto.Marshal(pushReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushGroupTipMsg Marshal pushReq err: %v", err)
		return err
	}

	receiveMsg, err := proto.Marshal(timeLineMsg)
	if err != nil {
		log.WarnWithCtx(ctx, "pushGroupTipMsg Marshal timeLineMsg err: %v", err)
	}
	_, err = s.aigcCommonClient.AddToDelayQueue(ctx, &aigc_common.AddToDelayQueueRequest{
		DelayTime:  2,
		Data:       marshalBytes,
		PushTo:     aigc_common.PushTo_PUSH_TO_GROUP,
		ReceiveMsg: receiveMsg,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "pushMsg AddToDelayQueue pushReq:%s err: %v", pushReq.String(), err)
		return err
	}
	log.InfoWithCtx(ctx, "pushGroupTipMsg AddToDelayQueue req(%s)", pushReq)
	return nil
}
func (s *Server) GetGroupMsgList(ctx context.Context, in *pb.GetGroupMsgListRequest) (out *pb.GetGroupMsgListResponse, err error) {
	out = &pb.GetGroupMsgListResponse{}

	if in.GetCmd() != pb.WebImCmd_WEB_IM_CMD_AI_GROUP {
		log.ErrorWithCtx(ctx, "GetGroupMsgList err, in:%s", in.String())
		return out, nil
	}

	var limit uint32 = 20
	if in.GetLimit() > 0 {
		limit = in.GetLimit()
	}
	uid := metainfo.GetServiceInfo(ctx).UserID()
	getMsgReq := &aigc_soulmate_middle.GetGroupMsgListReq{
		GroupId: in.GetGroupId(),
		SeqId:   in.GetSeqId(),
		Limit:   limit,
		Uid:     uid,
	}
	getMsgResp, err := s.soulmateMiddleCli.GetGroupMsgList(ctx, getMsgReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupMsgList soulmateMiddleCli.GetGroupMsgList err, err:%v", err)
		return out, err
	}
	out.TimeLineMsgs = make([]*pb.GroupTimeLineMsg, 0, len(getMsgResp.GetTimeLineMsgs()))
	for _, msg := range getMsgResp.GetTimeLineMsgs() {
		tmpMsg := &pb.ImMsg{
			Type:        uint32(msg.GetMsg().GetType()),
			Content:     msg.GetMsg().GetContent(),
			Ext:         msg.GetMsg().GetExt(),
			SentAt:      msg.GetMsg().GetSentAt(),
			SeqId:       msg.GetMsg().GetSeqId(),
			ImCmdType:   msg.GetMsg().GetImCmdType(),
			ImBusiType:  msg.GetMsg().GetImBusiType(),
			ContentType: msg.GetMsg().GetContentType(),

			TransparentExtMsg: msg.GetMsg().GetTransparentExtMsg(),
		}
		out.TimeLineMsgs = append(out.TimeLineMsgs, &pb.GroupTimeLineMsg{
			GroupId:         msg.GetGroupId(),
			Msg:             tmpMsg,
			GroupSendType:   pb.GroupSendType(msg.GetGroupSendType()),
			Uid:             msg.GetUid(),
			RoleIds:         msg.GetRoleIds(),
			GroupTemplateId: msg.GetGroupTemplateId(),
			AtUids:          msg.GetAtUids(),
			AtRoleIds:       msg.GetAtRoleIds(),
		})
	}
	log.InfoWithCtx(ctx, "GetGroupMsgList in:%s, out:%s", in.String(), out.String())
	return out, nil
}

func (s *Server) getGroupInfoMap(ctx context.Context, uid uint32) (map[uint32]*aigc_group.Group, error) {
	userGroupResp, err := s.groupClient.GetUserJoinedGroupList(ctx, &aigc_group.GetUserJoinedGroupListRequest{
		Uid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserJoinedGroupList uid(%d) err: %v", uid, err)
		return nil, err
	}
	groupInfoMap := make(map[uint32]*aigc_group.Group, len(userGroupResp.GetList()))
	for _, group := range userGroupResp.GetList() {
		groupInfoMap[group.GetId()] = group
	}
	return groupInfoMap, nil
}

func (s *Server) getSenderName(ctx context.Context, lastMsgs []*aigc_soulmate_middle.GroupLastMsg) (map[uint32]string, map[uint32]string, error) {
	var uids []uint32
	var roleIds []uint32

	uid2NameMap := make(map[uint32]string)
	role2NameMap := make(map[uint32]string)

	for _, lastMsg := range lastMsgs {
		if lastMsg.GetTimeLineMsg().GetGroupSendType() == aigc_soulmate_middle.GroupSendType_GroupSendTypeUser2AI {
			uids = append(uids, lastMsg.GetTimeLineMsg().GetUid())
		} else if lastMsg.GetTimeLineMsg().GetGroupSendType() == aigc_soulmate_middle.GroupSendType_GroupSendTypeAI2User {
			if len(lastMsg.GetTimeLineMsg().GetRoleIds()) != 0 {
				roleIds = append(roleIds, lastMsg.GetTimeLineMsg().GetRoleIds()[0])
			} else {
				log.WarnWithCtx(ctx, "getSenderName roleid err, timeline:%+v", lastMsg.GetTimeLineMsg())
			}
		} else {
			log.WarnWithCtx(ctx, "getSenderName type err, timeline:%+v", lastMsg.GetTimeLineMsg())
		}

	}

	if len(roleIds) != 0 {
		roleInfoResp, err := s.soulmateClient.GetAIRoleList(ctx, &aigc_soulmate.GetAIRoleListReq{
			RoleIdList: roleIds,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "getRoleName GetAIRoleList err:%v, roleIds:%v", err, roleIds)
			return nil, nil, err
		}
		for _, roleInfo := range roleInfoResp.GetRoleList() {
			role2NameMap[roleInfo.GetId()] = roleInfo.GetName()
		}
	}

	log.DebugWithCtx(ctx, "getSenderName roleIds:%v, role2NameMap:%+v", roleIds, role2NameMap)

	if len(uids) != 0 {
		userInfoResp, err := s.accountClient.GetUsersByUids(ctx, uids)
		if err != nil {
			log.ErrorWithCtx(ctx, "getRoleName GetUsersByUids err:%v, uids:%v", err, uids)
			return nil, nil, err
		}
		for _, user := range userInfoResp.GetUserList() {
			uid2NameMap[user.GetUid()] = user.GetNickname()
		}
	}
	return uid2NameMap, role2NameMap, nil
}

func (s *Server) BatchGetGroupLastMsg(ctx context.Context, in *pb.BatchGetGroupLastMsgRequest) (out *pb.BatchGetGroupLastMsgResponse, err error) {
	out = &pb.BatchGetGroupLastMsgResponse{}

	if in.GetCmd() != pb.WebImCmd_WEB_IM_CMD_AI_GROUP {
		log.ErrorWithCtx(ctx, "SendGroupImMsg err, in:%s", in.String())
		return out, nil
	}

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.WarnWithCtx(ctx, "GetWebImMsgList invalid user")
		return out, nil
	}

	groupInfoMap, err := s.getGroupInfoMap(ctx, uid)
	if err != nil {
		return out, err
	}

	getMsgReq := &aigc_soulmate_middle.BatchGetGroupLastMsgListReq{
		GroupIds: in.GetGroupIds(),
	}
	getMsgResp, err := s.soulmateMiddleCli.BatchGetGroupLastMsgList(ctx, getMsgReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupMsgList soulmateMiddleCli.BatchGetGroupLastMsgList err, err:%v", err)
		return out, err
	}

	if len(getMsgResp.GetGroupMsgs()) == 0 {
		log.WarnWithCtx(ctx, "BatchGetGroupLastMsg group not exist, in:%s", in.String())
		return out, nil
	}

	uidNameMap, roleNameMap, err := s.getSenderName(ctx, getMsgResp.GetGroupMsgs())
	if err != nil {
		return nil, nil
	}
	log.DebugWithCtx(ctx, "getSenderName uidNameMap:%+v, roleNameMap:%+v", uidNameMap, roleNameMap)

	out.LastGroupMsgs = make([]*pb.LastGroupMsg, 0, len(getMsgResp.GetGroupMsgs()))

	// 查询用户在群聊中的未读消息数
	getRedDotParams := make([]*game_red_dot.BatchGetRedDotInfoReq_GetRedDotInfoParam, 0, len(getMsgResp.GetGroupMsgs()))
	for _, groupId := range in.GetGroupIds() {
		getRedDotParams = append(getRedDotParams, &game_red_dot.BatchGetRedDotInfoReq_GetRedDotInfoParam{
			BizType: uint32(game_red_dot_logic.RedDotBizType_RED_DOT_BIZ_TYPE_AIGC_GROUP),
			BizKey:  genGroupRedDotBizKey(uid, groupId),
		})
	}
	redDotInfoResp, err := s.redDotClient.BatchGetRedDotInfo(ctx, &game_red_dot.BatchGetRedDotInfoReq{
		Params: getRedDotParams,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetRedDotInfo err:%v", err)
		return out, err
	}
	redDotMap := redDotInfoResp.GetRedDotMap()

	multiPlayerGroupIds := make([]uint32, 0)
	for _, group := range groupInfoMap {
		// 单人群聊头像按配置展示，后续有新增按配置展示头像的群聊类型，需要在这里处理
		if group.GetType() == aigc_group.GroupType_GROUP_TYPE_SINGLE_USER {
			continue
		}
		multiPlayerGroupIds = append(multiPlayerGroupIds, group.GetId())
	}

	groupAvatarMap, err := s.getGroupMemberAvatarMap(ctx, multiPlayerGroupIds)
	if err != nil {
		return nil, err
	}

	for _, groupMsg := range getMsgResp.GetGroupMsgs() {
		msg := &pb.ImMsg{
			Type:        uint32(groupMsg.GetTimeLineMsg().GetMsg().GetType()),
			Content:     groupMsg.GetTimeLineMsg().GetMsg().GetContent(),
			Ext:         groupMsg.GetTimeLineMsg().GetMsg().GetExt(),
			SentAt:      groupMsg.GetTimeLineMsg().GetMsg().GetSentAt(),
			SeqId:       groupMsg.GetTimeLineMsg().GetMsg().GetSeqId(),
			ImCmdType:   groupMsg.GetTimeLineMsg().GetMsg().GetImCmdType(),
			ImBusiType:  groupMsg.GetTimeLineMsg().GetMsg().GetImBusiType(),
			ContentType: groupMsg.GetTimeLineMsg().GetMsg().GetContentType(),
		}
		timelineMsg := &pb.GroupTimeLineMsg{
			GroupId:         groupMsg.GetTimeLineMsg().GetGroupId(),
			Msg:             msg,
			GroupSendType:   pb.GroupSendType(groupMsg.GetTimeLineMsg().GetGroupSendType()),
			Uid:             groupMsg.GetTimeLineMsg().GetUid(),
			RoleIds:         groupMsg.GetTimeLineMsg().GetRoleIds(),
			GroupTemplateId: groupMsg.GetTimeLineMsg().GetGroupTemplateId(),
		}
		redDotKey := genRedDotMapKey(uint32(game_red_dot_logic.RedDotBizType_RED_DOT_BIZ_TYPE_AIGC_GROUP), genGroupRedDotBizKey(uid, groupMsg.GetGroupId()))
		var senderName string
		if groupMsg.GetTimeLineMsg().GetGroupSendType() == aigc_soulmate_middle.GroupSendType_GroupSendTypeUser2AI {
			if name, ok := uidNameMap[groupMsg.GetTimeLineMsg().GetUid()]; ok {
				senderName = name
			} else {
				log.WarnWithCtx(ctx, "get uid name err, timeline:%+v", groupMsg.GetTimeLineMsg())
			}
		} else if groupMsg.GetTimeLineMsg().GetGroupSendType() == aigc_soulmate_middle.GroupSendType_GroupSendTypeAI2User {
			if len(groupMsg.GetTimeLineMsg().GetRoleIds()) != 0 {
				if name, ok := roleNameMap[groupMsg.GetTimeLineMsg().GetRoleIds()[0]]; ok {
					senderName = name
				} else {
					log.WarnWithCtx(ctx, "get role name err, timeline:%+v", groupMsg.GetTimeLineMsg())
				}
			}
		} else {
			log.WarnWithCtx(ctx, "getSenderName type err, timeline:%+v", groupMsg.GetTimeLineMsg())
		}
		out.LastGroupMsgs = append(out.LastGroupMsgs, &pb.LastGroupMsg{
			GroupId:     groupMsg.GetGroupId(),
			TimeLineMsg: timelineMsg,
			GroupBaseInfo: &pb.GroupBaseInfo{
				UnreadRedDotCnt: redDotMap[redDotKey].GetCount(),
				Avatar:          groupInfoMap[groupMsg.GetGroupId()].GetAvatar(),
				GroupName:       groupInfoMap[groupMsg.GetGroupId()].GetName(),
				ImTabTag:        groupInfoMap[groupMsg.GetGroupId()].GetImTabTag(),
				SenderNickName:  senderName,
				MemberAvatars:   groupAvatarMap[groupMsg.GetGroupId()],
			},
		})
	}
	log.InfoWithCtx(ctx, "BatchGetGroupLastMsg in:%s, out:%s", in.String(), out.String())
	return out, nil
}

func (s *Server) getGroupMemberAvatarMap(ctx context.Context, groupIds []uint32) (map[uint32][]*pb.AvatarInfo, error) {
	avatarMap := make(map[uint32][]*pb.AvatarInfo, len(groupIds))
	if len(groupIds) == 0 {
		log.WarnWithCtx(ctx, "getGroupMemberAvatarMap groupIds is empty")
		return avatarMap, nil
	}
	uids := make([]uint32, 0, len(groupIds)*9)
	roleIds := make([]uint32, 0, len(groupIds)*9)

	// 批量获取所有群组的成员列表
	batchResp, err := s.groupClient.BatchGetGroupMemberList(ctx, &aigc_group.BatchGetGroupMemberListRequest{
		GroupIds: groupIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getGroupMemberAvatarMap BatchGetGroupMemberList groupIds(%v) err: %v", groupIds, err)
		return nil, err
	}

	groupMemberMap := batchResp.GetGroupMembersMap()
	// 处理每个群组的成员
	for _, groupId := range groupIds {
		groupMembers := batchResp.GetGroupMembersMap()[groupId]
		if groupMembers == nil {
			log.WarnWithCtx(ctx, "getGroupMemberAvatarMap group members not found for groupId(%d)", groupId)
			continue
		}

		tmpUids := make([]uint32, 0, 9)
		tmpRoleIds := make([]uint32, 0, 9)
		for _, member := range groupMembers.GetMembers() {
			if len(tmpUids) == 9 {
				break
			}
			switch member.GetType() {
			case aigc_group.GroupMemberType_GROUP_MEMBER_TYPE_USER:
				tmpUids = append(tmpUids, member.GetId())
			case aigc_group.GroupMemberType_GROUP_MEMBER_TYPE_ROLE:
				tmpRoleIds = append(tmpRoleIds, member.GetId())
			default:
				log.WarnWithCtx(ctx, "getGroupMemberAvatarMap invalid member type, member:%+v, groupId:%d", member, groupId)
			}
		}
		uids = append(uids, tmpUids...)
		if len(tmpUids) < 9 {
			diff := 9 - len(tmpUids)
			if len(tmpRoleIds) > diff {
				tmpRoleIds = tmpRoleIds[:diff]
			}
			roleIds = append(roleIds, tmpRoleIds...)
		}
	}

	if len(uids) > 1000 {
		log.WarnWithCtx(ctx, "getGroupMemberAvatarMap uids too many, len(uids): %d", len(uids))
	}
	userMap, serverErr := s.accountClient.GetUsersMap(ctx, uids)
	if serverErr != nil {
		log.ErrorWithCtx(ctx, "getGroupMemberAvatarMap GetUsersMap uids(%v) err: %v", uids, serverErr)
		return nil, serverErr
	}

	roleMap := make(map[uint32]*aigc_soulmate.AIRole, len(roleIds))
	if len(roleIds) > 0 {
		roleList, err := s.soulmateClient.GetAIRoleList(ctx, &aigc_soulmate.GetAIRoleListReq{
			RoleIdList: roleIds,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "getGroupMemberAvatarMap GetAIRoleList roleIds(%v) err: %v", roleIds, err)
			return nil, err
		}
		for _, role := range roleList.GetRoleList() {
			roleMap[role.GetId()] = role
		}
	}

	avatarMap = assembleGroupAvatarInfoMap(ctx, groupMemberMap, userMap, roleMap)
	return avatarMap, nil
}

func assembleGroupAvatarInfoMap(ctx context.Context, groupMemberMap map[uint32]*aigc_group.GroupMemberList,
	userMap map[uint32]*account_go.UserResp, roleMap map[uint32]*aigc_soulmate.AIRole) map[uint32][]*pb.AvatarInfo {
	avatarMap := make(map[uint32][]*pb.AvatarInfo, len(groupMemberMap))
	for groupId, members := range groupMemberMap {
		avatars := make([]*pb.AvatarInfo, 0, len(members.GetMembers()))
		// 优先展示用户，如果用户数量不到9个，再用角色头像补齐
		for _, member := range members.GetMembers() {
			if len(avatars) == 9 {
				break
			}
			if member.GetType() == aigc_group.GroupMemberType_GROUP_MEMBER_TYPE_USER {
				if user, ok := userMap[member.GetId()]; ok {
					avatars = append(avatars, &pb.AvatarInfo{
						MemberType: uint32(member.GetType()),
						Account:    user.GetUsername(),
					})
				} else {
					log.WarnWithCtx(ctx, "assembleGroupAvatarInfoMap user(%d) not found", member.GetId())
				}
			}
		}
		for _, member := range members.GetMembers() {
			if len(avatars) == 9 {
				break
			}
			if member.GetType() == aigc_group.GroupMemberType_GROUP_MEMBER_TYPE_ROLE {
				if role, ok := roleMap[member.GetId()]; ok {
					avatars = append(avatars, &pb.AvatarInfo{
						MemberType: uint32(member.GetType()),
						Avatar:     role.GetAvatar(),
					})
				} else {
					log.WarnWithCtx(ctx, "assembleGroupAvatarInfoMap role(%d) not found", member.GetId())
				}
			}
		}
		avatarMap[groupId] = avatars
	}
	return avatarMap
}

func genGroupRedDotBizKey(uid, groupId uint32) string {
	return fmt.Sprintf("%d_%d", uid, groupId)
}

func genRedDotMapKey(bizType uint32, bizKey string) string {
	return fmt.Sprintf("%d#%s", bizType, bizKey)
}
