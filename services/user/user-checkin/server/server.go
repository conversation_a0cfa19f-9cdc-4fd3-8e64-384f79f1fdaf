// Code generated by tyrgo smart generator

package usercheckinserver

import (
	"context"
	"gitlab.ttyuyin.com/tyr/tt-ecosystem/startup/suit/smart"
	"google.golang.org/grpc"

	pb "golang.52tt.com/protocol/services/usercheckin"

	internal "golang.52tt.com/services/user/user-checkin/internal"
)

const serverName = "user-checkin"

type Server struct {
	smartstartup.UnimplementedServer
	smartstartup.UnimplementedGrpcServer

	// original need
	*internal.Server
	cfg internal.StartConfig
}

func (s *Server) Name() string {
	return serverName
}

func (s *Server) Init(ctx context.Context) error {
	server, err := internal.NewServer(ctx, &s.cfg)
	if err != nil {
		return err
	}
	s.Server = server
	return nil
}

func (s *Server) Close(ctx context.Context) {
	s.ShutDown()
}

func (s *Server) RegisterConfig() map[string]*smartstartup.Config {
	return map[string]*smartstartup.Config{
		serverName: {
			Config: &s.cfg,
		},
	}
}

func (s *Server) RegisterGRPC(registrar *grpc.Server) error {
	pb.RegisterUserCheckinServer(registrar, s)
	pb.RegisterUserCheckinCarpool(registrar, s)
	return nil
}

func (s *Server) GRPCServerInterceptor() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{
		smartstartup.ServerInterceptor(),
	}
}
