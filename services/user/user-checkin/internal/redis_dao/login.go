package redis_dao

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
)

func (c *CheckInRedis) initLoginLua() (err error) {
	if _, err = c.redisClient.ScriptLoad(luaGenRecallPushQueue).Result(); err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

const keyDeviceIdExist = "dvId_exist"

/**
 * @name:
 * @msg: 一次设备 判断是否已经存在
 * @param {string} deviceID
 * @return {*}
 */
func (c *CheckInRedis) IsExistDeviceIdHex(deviceID string) bool {
	v := c.redisClient.SIsMember(keyDeviceIdExist, deviceID).Val()
	return v
}

/**
 * @name:
 * @msg: 增加设备的存在
 * @param {string} deviceID
 * @return {*}
 */
func (c *CheckInRedis) SetDeviceIdHex(deviceID string) error {
	err := c.redisClient.SAdd(keyDeviceIdExist, deviceID).Err()
	if err != nil {
		log.Errorf("SetDeviceIdHex:%s", deviceID)
		return err
	}
	return nil
}

/**
 * @name:
 * @msg: 获取领取天数 此处mock
 * @param {time.Time} day
 * @return {*}
 */
func (c *CheckInRedis) GetRecallDay(day time.Time, useMock bool) string {
	if useMock {
		mockMinute := day.Minute() / 6 //10分钟一天
		return day.Format(Layout) + strconv.Itoa(day.Hour()) + strconv.Itoa(mockMinute)
	} else {
		return day.Format(Layout)
	}
}

// 记录当天的领取情况
const keyDayRecallSucc = "day_recall_succ_%s"

/**
 * @name:
 * @msg: 获取某天的记录时间 此处mock
 * @param {time.Time} day
 * @return {*}
 */
func (c *CheckInRedis) GetKeyDayRecallSucc(day time.Time) string {
	return fmt.Sprintf(keyDayRecallSucc, c.GetRecallDay(day, false))
}

/**
 * @name:
 * @msg: 增加当天领取人的集合
 * @param {time.Time} loginTime
 * @param {uint32} uid
 * @return {*}
 */
func (c *CheckInRedis) SetDayRecallSucc(loginTime time.Time, uid uint32) error {
	err := c.redisClient.SAdd(c.GetKeyDayRecallSucc(loginTime), strconv.Itoa(int(uid))).Err()
	return err
}

// 记录已recall的用户
const keyAllRecallSucc = "all_recall"

/**
 * @name:
 * @msg: 增加所有已召回用户的数目
 * @param {uint32} uid
 * @return {*}
 */
func (c *CheckInRedis) SetAllRecall(uid uint32) error {
	err := c.redisClient.SAdd(keyAllRecallSucc, strconv.Itoa(int(uid))).Err()
	return err
}

func (c *CheckInRedis) GetSScanUser(ctx context.Context, key string, uidFilter map[uint32]struct{}) {
	var cursorN uint64 = 0
	var err error
	for {
		log.InfoWithCtx(ctx, "key: %s,%v,%v", key, cursorN, 1000)
		r := c.redisClient.SScan(key, cursorN, "", 1000)
		err = r.Err()
		if err != nil {
			log.Errorf("SScan redis cursor %d failed: %v", cursorN, err)
			return
		}

		var rs []string
		rs, cursorN = r.Val()
		total := len(rs) / 2
		if total == 0 {
			log.InfoWithCtx(ctx, "total 0:%d, val:%v", cursorN, rs)
			if cursorN == 0 {
				break
			} else {
				time.Sleep(10 * time.Microsecond)
				continue
			}
		}

		for _, str := range rs {
			uid, _ := strconv.Atoi(str)
			uidFilter[uint32(uid)] = struct{}{}
		}
		if cursorN == 0 {
			return
		}
	}
}

func (c *CheckInRedis) genShouldPushName(ctx context.Context, resQue, dayRecallSucc string) string {
	var inavaliVerFilter = "recalluser_inavali_cliver"
	var allRecall = "all_recall"
	var finish7card = "finish_7card"
	fOK := fmt.Sprintf("%s_ok", resQue)
	if c.redisClient.Exists(fOK).Val() == 1 {
		return resQue
	}
	if c.redisClient.Exists(resQue).Val() == 1 {
		return resQue
	}
	//1.获取所有应该排除的
	uidFilter := map[uint32]struct{}{}
	c.GetSScanUser(ctx, dayRecallSucc, uidFilter)
	c.GetSScanUser(ctx, inavaliVerFilter, uidFilter)
	c.GetSScanUser(ctx, finish7card, uidFilter)

	//获取今天要发的
	allUidFilter := map[uint32]struct{}{}
	c.GetSScanUser(ctx, allRecall, allUidFilter)
	//diff
	diffUids := make([]uint32, 0, len(allUidFilter))
	for uid := range allUidFilter {
		//不在应该排除的集合的 就是要排除的
		if _, ok := uidFilter[uid]; !ok {
			diffUids = append(diffUids, uid)
		}
	}
	n := len(allUidFilter)
	for i := 0; i < n; i += 1000 {
		l := i + 1000
		var tmp []uint32
		if l > n {
			tmp = diffUids[i:n]
		} else {
			tmp = diffUids[i : i+1000]
		}
		c.redisClient.Pipelined(func(p redis.Pipeliner) error {
			for _, uid := range tmp {
				p.LPush(resQue, uid)
			}
			return nil
		})
	}
	return resQue

}

// 获取某天的召回 存储到队列中进行发送 排除7天的数据
const luaGenRecallPushQueue = `
	local resQue = KEYS[1]
	local dayRecallSucc = KEYS[2]
	local inavaliVerFilter = "recalluser_inavali_cliver"
	local allRecall = "all_recall"
	local finish7card = "finish_7card"
	local resQueOk = resQue .. "_ok"
	-- 直接返回
	local isExist = redis.call("exists",resQueOk)
	if (isExist == 1) then
		return resQue
	end
	-- 有些正在处理
	local isExist2 = redis.call("exists",resQue)
	if (isExist2 == 1) then
		return resQue
	end

	-- 当天需要召回的人  差集 并且与不适合版本的 差集 这里排除完成了7天任务的
	local uids = redis.call("sdiff",allRecall,dayRecallSucc,finish7card,inavaliVerFilter);
	if (uids == false) then
		return resQue
	end 

	for i, v in ipairs(uids) do
		redis.call("lpush",resQue,v)
	end 
	redis.call("set",resQueOk,"1")
	return resQue
`

const keyRecallQueuePrepared = "recall_queue_prepare_%s_%s"

/**
 * @name:
 * @msg: 获取名字推送队列
 * @param {*} day
 * @param {string} sendTime
 * @return {*}
 */
func (c *CheckInRedis) GetPushQueueName(day, sendTime string) string {
	queueName := fmt.Sprintf(keyRecallQueuePrepared, day, sendTime)
	return queueName
}

/**
 * @name:
 * @msg: 生成推送队列
 * @param {string} queueName
 * @param {time.Time} now
 * @return {*}
 */
func (c *CheckInRedis) GenPushQueue(queueName string, now time.Time) (string, error) {
	dayRecallSucc := c.GetKeyDayRecallSucc(now)
	ctx := context.Background()
	pushName := c.genShouldPushName(ctx, queueName, dayRecallSucc)
	//懒删除
	c.redisClient.Unlink(c.GetKeyDayRecallSucc(now.AddDate(0, 0, -1)))
	return pushName, nil
}

const keyLoginCntUid = "login_cnt_uid_v2_%d"

/**
 * @name:
 * @msg: 增加目前发奖次数
 * @param {uint32} uid
 * @return {*}
 */
func (c *CheckInRedis) IncrRecallUserLoginCnt(uid uint32) error {
	return c.redisClient.Incr(fmt.Sprintf(keyLoginCntUid, uid)).Err()
}

/**
 * @name:
 * @msg: 获取目前发奖次数
 * @param {uint32} uid
 * @return {*}
 */
func (c *CheckInRedis) GetRecallUserLoginCnt(uid uint32) (int, error) {
	return c.redisClient.Get(fmt.Sprintf(keyLoginCntUid, uid)).Int()
}

const keyFinish7card = "finish_7card"

/**
 * @name:
 * @msg: 设置以及完成7天的人
 * @param {uint32} uid
 * @return {*}
 */
func (c *CheckInRedis) SetRecallUserFinish7Card(uid uint32) error {
	err := c.redisClient.SAdd(keyFinish7card, uid).Err()
	if err == redis.Nil {
		return nil
	}
	return err
}

const keyRecallCliInavaliVesion = "recalluser_inavali_cliver"

/**
 * @name:
 * @msg: 设置不可用的版本号这一次的获奖用户
 * @param {uint32} uid
 * @return {*}
 */
func (c *CheckInRedis) SetRecallUserCliInavaliVer(uid uint32) error {
	return c.redisClient.SAdd(keyRecallCliInavaliVesion, uid).Err()
}

/**
 * @name:
 * @msg: 删除不可用版本
 * @param {uint32} uid
 * @return {*}
 */
func (c *CheckInRedis) DelRecallUserCliInavaliVer(uid uint32) error {
	return c.redisClient.SRem(keyRecallCliInavaliVesion, uid).Err()
}
