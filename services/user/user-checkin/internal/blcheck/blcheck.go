package blcheck

import (
	"context"
	"time"

	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/pkg/log"
)

const (
	GetAwardFail  = "GetAwardFail"
	SendAwardFail = "SendAwardFail"
	SendAward     = "SendAwardSuc"
)

type GetAwardCode struct {
	Uid       uint32
	DayRounds []uint32
}

type SendAwardCode struct {
	Uid        uint32
	DayRound   uint32
	HappenDay  string
	AwardType  uint32
	AwardName  string
	SourceId   uint32
	SourceKey  string
	TbeanPrice uint32
}

func NewBlCheck(isOpen bool) *BlCheck {
	return &BlCheck{
		IsOpen: isOpen,
	}
}

type BlCheck struct {
	IsOpen bool
}

func (b *BlCheck) CheckGetAward(uid uint32, dayRounds []uint32) {
	if b == nil {
		return
	}
	go func() {
		if b.IsOpen {
			awardCode := GetAwardCode{
				DayRounds: dayRounds,
				Uid:       uid,
			}
			log.Debugf("bylink track awwardCode: %+v", awardCode)

			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()
			// 百灵数据统计
			bylink.Track(ctx, uint64(uid),
				GetAwardFail,
				awardCode,
				true)
			bylink.Flush()
		}
	}()

}

func (b *BlCheck) CheckSendAwardFail(uid, dayRound, awardType, sourceId, tbeanPrice uint32, happenDay, awardName, sourceKey string) {
	if b == nil {
		return
	}
	go func() {
		if b.IsOpen {
			awardCode := SendAwardCode{
				DayRound:   dayRound,
				Uid:        uid,
				HappenDay:  happenDay,
				AwardType:  awardType,
				AwardName:  awardName,
				SourceKey:  sourceKey,
				SourceId:   sourceId,
				TbeanPrice: tbeanPrice,
			}
			log.Debugf("bylink track awwardCode: %+v", awardCode)

			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()
			// 百灵数据统计
			bylink.Track(ctx, uint64(uid),
				SendAwardFail,
				awardCode,
				true)
			bylink.Flush()
		}
	}()

}

func (b *BlCheck) CheckSendAward(uid, dayRound, awardType, sourceId, tbeanPrice uint32, happenDay, awardName, sourceKey string) {
	if b == nil {
		return
	}
	go func() {
		if b.IsOpen {
			awardCode := SendAwardCode{
				DayRound:   dayRound,
				Uid:        uid,
				HappenDay:  happenDay,
				AwardType:  awardType,
				AwardName:  awardName,
				SourceKey:  sourceKey,
				SourceId:   sourceId,
				TbeanPrice: tbeanPrice,
			}
			log.Debugf("bylink track awwardCode: %+v", awardCode)

			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()
			// 百灵数据统计
			bylink.Track(ctx, uint64(uid),
				SendAward,
				awardCode,
				true)
			bylink.Flush()
		}
	}()

}
