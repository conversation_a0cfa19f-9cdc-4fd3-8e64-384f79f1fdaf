package model

import (
	"context"
	"errors"
	"github.com/go-redis/redis"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/guild"
	mockacc "golang.52tt.com/clients/mocks/account"
	mockguild "golang.52tt.com/clients/mocks/guild"
	accountPb "golang.52tt.com/protocol/services/accountsvr"
	pb "golang.52tt.com/protocol/services/channel-guide"
	guildPb "golang.52tt.com/protocol/services/guildsvr"
	"golang.52tt.com/services/user/channel-guide/internal/conf"
	"golang.52tt.com/services/user/channel-guide/internal/db"
	"golang.52tt.com/services/user/channel-guide/internal/mocks"
	"golang.52tt.com/services/user/channel-guide/internal/utils"
	"reflect"
	"testing"
)

func TestGameGroupModel_CreateGameGroupConf(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		store = mocks.NewMockIMongoDao(ctrl)
		cache = mocks.NewMockIRedisDao(ctrl)

		gameId  uint32 = 1
		groupId uint32 = 1
	)

	store.EXPECT().
		CreateGameGroupConf(gomock.Any(), &db.GameGroupConf{GameId: gameId, GroupId: groupId}).
		Return(nil)
	cache.EXPECT().
		DelGameGroupList(gomock.Any(), gameId).
		Return(nil)

	store.EXPECT().
		CreateGameGroupConf(gomock.Any(), &db.GameGroupConf{GameId: gameId, GroupId: groupId}).
		Return(errors.New("test error"))

	store.EXPECT().
		CreateGameGroupConf(gomock.Any(), &db.GameGroupConf{GameId: gameId, GroupId: groupId}).
		Return(nil)
	cache.EXPECT().
		DelGameGroupList(gomock.Any(), gameId).
		Return(errors.New("test error"))

	type fields struct {
		store db.IMongoDao
		cache db.IRedisDao
	}
	type args struct {
		ctx     context.Context
		gameId  uint32
		groupId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "GameGroupModel.CreateGameGroupConf 1",
			fields:  fields{store: store, cache: cache},
			args:    args{ctx: context.Background(), gameId: gameId, groupId: groupId},
			wantErr: false,
		},
		{
			name:    "GameGroupModel.CreateGameGroupConf 2",
			fields:  fields{store: store, cache: cache},
			args:    args{ctx: context.Background(), gameId: gameId, groupId: groupId},
			wantErr: true,
		},
		{
			name:    "GameGroupModel.CreateGameGroupConf 3",
			fields:  fields{store: store, cache: cache},
			args:    args{ctx: context.Background(), gameId: gameId, groupId: groupId},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &GameGroupModel{
				store: tt.fields.store,
				cache: tt.fields.cache,
			}
			if err := m.CreateGameGroupConf(tt.args.ctx, tt.args.gameId, tt.args.groupId); (err != nil) != tt.wantErr {
				t.Errorf("CreateGameGroupConf() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestGameGroupModel_DelGameGroupConf(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		store = mocks.NewMockIMongoDao(ctrl)
		cache = mocks.NewMockIRedisDao(ctrl)

		gameId  uint32 = 1
		groupId uint32 = 1
	)

	store.EXPECT().
		DelGameGroupConf(gomock.Any(), gameId, groupId).
		Return(nil)
	cache.EXPECT().
		DelGameGroupList(gomock.Any(), gameId).
		Return(nil)

	store.EXPECT().
		DelGameGroupConf(gomock.Any(), gameId, groupId).
		Return(errors.New("test error"))

	store.EXPECT().
		DelGameGroupConf(gomock.Any(), gameId, groupId).
		Return(nil)
	cache.EXPECT().
		DelGameGroupList(gomock.Any(), gameId).
		Return(errors.New("test error"))

	type fields struct {
		store db.IMongoDao
		cache db.IRedisDao
	}
	type args struct {
		ctx     context.Context
		gameId  uint32
		groupId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "GameGroupModel.DelGameGroupConf 1",
			fields:  fields{store: store, cache: cache},
			args:    args{ctx: context.Background(), gameId: gameId, groupId: groupId},
			wantErr: false,
		},
		{
			name:    "GameGroupModel.DelGameGroupConf 2",
			fields:  fields{store: store, cache: cache},
			args:    args{ctx: context.Background(), gameId: gameId, groupId: groupId},
			wantErr: true,
		},
		{
			name:    "GameGroupModel.DelGameGroupConf 3",
			fields:  fields{store: store, cache: cache},
			args:    args{ctx: context.Background(), gameId: gameId, groupId: groupId},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &GameGroupModel{
				store: tt.fields.store,
				cache: tt.fields.cache,
			}
			if err := m.DelGameGroupConf(tt.args.ctx, tt.args.gameId, tt.args.groupId); (err != nil) != tt.wantErr {
				t.Errorf("DelGameGroupConf() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestGameGroupModel_GetGameGroupConfList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		store = mocks.NewMockIMongoDao(ctrl)

		gameId  uint32 = 1
		groupId uint32 = 1
		offset  uint32 = 0
		limit   uint32 = 10

		confList = []*db.GameGroupConf{
			{
				GameId:  1,
				GroupId: 1,
			},
			{
				GameId:  2,
				GroupId: 2,
			},
		}
	)

	store.EXPECT().
		GetGameGroupConfList(gomock.Any(), gameId, groupId, offset, limit).
		Return(confList, nil)

	store.EXPECT().
		GetGameGroupConfList(gomock.Any(), gameId, groupId, offset, limit).
		Return(nil, errors.New("test error"))

	type fields struct {
		store db.IMongoDao
	}
	type args struct {
		ctx     context.Context
		gameId  uint32
		groupId uint32
		offset  uint32
		limit   uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*db.GameGroupConf
		wantErr bool
	}{
		{
			name:    "GameGroupModel.GetGameGroupConfList 1",
			fields:  fields{store: store},
			args:    args{ctx: context.Background(), gameId: gameId, groupId: groupId, offset: offset, limit: limit},
			want:    confList,
			wantErr: false,
		},
		{
			name:    "GameGroupModel.GetGameGroupConfList 2",
			fields:  fields{store: store},
			args:    args{ctx: context.Background(), gameId: gameId, groupId: groupId, offset: offset, limit: limit},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &GameGroupModel{
				store: tt.fields.store,
			}
			got, err := m.GetGameGroupConfList(tt.args.ctx, tt.args.gameId, tt.args.groupId, tt.args.offset, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGameGroupConfList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetGameGroupConfList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGameGroupModel_GetGameGroupIdList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		store = mocks.NewMockIMongoDao(ctrl)
		cache = mocks.NewMockIRedisDao(ctrl)

		gameId  uint32 = 1
		groupId uint32 = 0
		offset  uint32 = 0
		limit   uint32 = 500

		groupIdList = []uint32{1, 2}
		confList    = []*db.GameGroupConf{
			{
				GameId:  1,
				GroupId: 1,
			},
			{
				GameId:  1,
				GroupId: 2,
			},
		}
	)

	cache.EXPECT().
		GetGameGroupList(gomock.Any(), gameId).
		Return(groupIdList, nil)

	cache.EXPECT().
		GetGameGroupList(gomock.Any(), gameId).
		Return(nil, errors.New("test error"))

	cache.EXPECT().
		GetGameGroupList(gomock.Any(), gameId).
		Return(nil, redis.Nil)
	store.EXPECT().
		GetGameGroupConfList(gomock.Any(), gameId, groupId, offset, limit).
		Return(confList, nil)
	cache.EXPECT().
		SetGameGroupList(gomock.Any(), gameId, groupIdList).
		Return(nil)

	cache.EXPECT().
		GetGameGroupList(gomock.Any(), gameId).
		Return(nil, redis.Nil)
	store.EXPECT().
		GetGameGroupConfList(gomock.Any(), gameId, groupId, offset, limit).
		Return(nil, errors.New("test error"))

	cache.EXPECT().
		GetGameGroupList(gomock.Any(), gameId).
		Return(nil, redis.Nil)
	store.EXPECT().
		GetGameGroupConfList(gomock.Any(), gameId, groupId, offset, limit).
		Return(confList, nil)
	cache.EXPECT().
		SetGameGroupList(gomock.Any(), gameId, groupIdList).
		Return(errors.New("test error"))

	type fields struct {
		store db.IMongoDao
		cache db.IRedisDao
	}
	type args struct {
		ctx    context.Context
		gameId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []uint32
		wantErr bool
	}{
		{
			name:    "GameGroupModel.GetGameGroupIdList 1",
			fields:  fields{store: store, cache: cache},
			args:    args{ctx: context.Background(), gameId: gameId},
			want:    groupIdList,
			wantErr: false,
		},
		{
			name:    "GameGroupModel.GetGameGroupIdList 2",
			fields:  fields{store: store, cache: cache},
			args:    args{ctx: context.Background(), gameId: gameId},
			want:    nil,
			wantErr: true,
		},
		{
			name:    "GameGroupModel.GetGameGroupIdList 3",
			fields:  fields{store: store, cache: cache},
			args:    args{ctx: context.Background(), gameId: gameId},
			want:    groupIdList,
			wantErr: false,
		},
		{
			name:    "GameGroupModel.GetGameGroupIdList 4",
			fields:  fields{store: store, cache: cache},
			args:    args{ctx: context.Background(), gameId: gameId},
			want:    nil,
			wantErr: true,
		},
		{
			name:    "GameGroupModel.GetGameGroupIdList 5",
			fields:  fields{store: store, cache: cache},
			args:    args{ctx: context.Background(), gameId: gameId},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &GameGroupModel{
				store: tt.fields.store,
				cache: tt.fields.cache,
			}
			got, err := m.GetGameGroupIdList(tt.args.ctx, tt.args.gameId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGameGroupIdList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetGameGroupIdList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGameGroupModel_CloseUserGameGroupGuide(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		cache = mocks.NewMockIRedisDao(ctrl)

		gameId uint32 = 1
		uid    uint32 = 1
	)

	cache.EXPECT().
		CloseUserGameGroupGuide(gomock.Any(), gameId, utils.Today(), uid).
		Return(nil)

	cache.EXPECT().
		CloseUserGameGroupGuide(gomock.Any(), gameId, utils.Today(), uid).
		Return(errors.New("test error"))

	type fields struct {
		cache db.IRedisDao
	}
	type args struct {
		ctx  context.Context
		game uint32
		uid  uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "GameGroupModel.CloseUserGameGroupGuide 1",
			fields:  fields{cache: cache},
			args:    args{ctx: context.Background(), game: gameId, uid: uid},
			wantErr: false,
		},
		{
			name:    "GameGroupModel.CloseUserGameGroupGuide 2",
			fields:  fields{cache: cache},
			args:    args{ctx: context.Background(), game: gameId, uid: uid},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &GameGroupModel{
				cache: tt.fields.cache,
			}
			if err := m.CloseUserGameGroupGuide(tt.args.ctx, tt.args.game, tt.args.uid); (err != nil) != tt.wantErr {
				t.Errorf("CloseUserGameGroupGuide() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestGameGroupModel_GetUserGameGroupGuide(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		cache = mocks.NewMockIRedisDao(ctrl)

		gameId uint32 = 1
		uid    uint32 = 1
	)

	cache.EXPECT().
		GetUserGameGroupGuide(gomock.Any(), gameId, utils.Today(), uid).
		Return(true, nil)

	cache.EXPECT().
		GetUserGameGroupGuide(gomock.Any(), gameId, utils.Today(), uid).
		Return(false, errors.New("test error"))

	type fields struct {
		cache db.IRedisDao
	}
	type args struct {
		ctx  context.Context
		game uint32
		uid  uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name:    "GameGroupModel.GetUserGameGroupGuide 1",
			fields:  fields{cache: cache},
			args:    args{ctx: context.Background(), game: gameId, uid: uid},
			want:    true,
			wantErr: false,
		},
		{
			name:    "GameGroupModel.GetUserGameGroupGuide 2",
			fields:  fields{cache: cache},
			args:    args{ctx: context.Background(), game: gameId, uid: uid},
			want:    false,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &GameGroupModel{
				cache: tt.fields.cache,
			}
			got, err := m.GetUserGameGroupGuide(tt.args.ctx, tt.args.game, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserGameGroupGuide() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetUserGameGroupGuide() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGameGroupModel_GetGameGroupList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		cache = mocks.NewMockIRedisDao(ctrl)
		cfg   = &conf.GameGroupConf{
			MemberNum: 5,
			ReturnNum: 5,
		}

		guildCli = mockguild.NewMockIClient(ctrl)
		accCli   = mockacc.NewMockIClient(ctrl)

		gameId      = uint32(1)
		uid         = uint32(1)
		groupIdList = []uint32{1, 2}
		uidList     = []uint32{1, 2}

		groupInfoList = []*guildPb.MyGroupInfo{
			{
				Groupinfo: &guildPb.GroupResp{
					GroupId:     1,
					GroupType:   4,
					MemberCount: 10,
					MemberLimit: 100,
				},
				Myrole: &guildPb.GroupMemberResp{},
			},
			{
				Groupinfo: &guildPb.GroupResp{
					GroupId:     2,
					GroupType:   4,
					MemberCount: 10,
					MemberLimit: 100,
				},
				Myrole: &guildPb.GroupMemberResp{},
			},
		}

		userMap = map[uint32]*accountPb.UserResp{
			1: {
				Uid: 1,
			},
			2: {
				Uid: 2,
			},
		}

		groupList = []*pb.GameGroup{
			{
				Id:         1,
				Account:    "1@tgroup",
				MemberNum:  10,
				UserJoined: true,
				MemberList: []*pb.GameGroup_Member{{}, {}},
			},
			{
				Id:         2,
				Account:    "2@tgroup",
				MemberNum:  10,
				UserJoined: true,
				MemberList: []*pb.GameGroup_Member{{}, {}},
			},
		}

		groupAccList = []string{"1@tgroup", "2@tgroup"}
	)

	cache.EXPECT().
		GetGameGroupList(gomock.Any(), gameId).
		Return(groupIdList, nil)

	guildCli.EXPECT().
		BatchGetGroup(gomock.Any(), uid, &guildPb.BatchGetGroupReq{
			GroupList:    groupIdList,
			IsNeedmyrole: true,
		}).
		Return(groupInfoList, nil)

	for _, groupId := range groupIdList {
		guildCli.EXPECT().
			GetGroupMemberList(gomock.Any(), uid, groupId, uint32(0), cfg.MemberNum).
			Return(&guildPb.GroupMemberListResp{
				Members: []*guildPb.GroupMemberResp{
					{
						Uid: 1,
					},
					{
						Uid: 2,
					},
				},
			}, nil)
	}

	accCli.EXPECT().
		BatGetUserByUid(gomock.Any(), uidList).
		Return(userMap, nil)

	type fields struct {
		cache    db.IRedisDao
		conf     *conf.GameGroupConf
		guildCli guild.IClient
		accCli   account.IClient
	}
	type args struct {
		ctx    context.Context
		uid    uint32
		gameId uint32
	}
	tests := []struct {
		name               string
		fields             fields
		args               args
		wantList           []*pb.GameGroup
		wantMyGroupAccList []string
		wantErr            bool
	}{
		{
			name:               "GameGroupModel.GetGameGroupList",
			fields:             fields{cache: cache, conf: cfg, guildCli: guildCli, accCli: accCli},
			args:               args{ctx: context.Background(), uid: uid, gameId: gameId},
			wantList:           groupList,
			wantMyGroupAccList: groupAccList,
			wantErr:            false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &GameGroupModel{
				cache:    tt.fields.cache,
				conf:     tt.fields.conf,
				guildCli: tt.fields.guildCli,
				accCli:   tt.fields.accCli,
			}
			gotList, gotMyGroupAccList, err := m.GetGameGroupList(tt.args.ctx, tt.args.uid, tt.args.gameId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGameGroupList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotList, tt.wantList) {
				t.Errorf("GetGameGroupList() gotList = %v, want %v", gotList, tt.wantList)
			}
			if !reflect.DeepEqual(gotMyGroupAccList, tt.wantMyGroupAccList) {
				t.Errorf("GetGameGroupList() gotMyGroupAccList = %v, want %v", gotMyGroupAccList, tt.wantMyGroupAccList)
			}
		})
	}
}

func TestNewGameGroupModel(t *testing.T) {
	type args struct {
		store    db.IMongoDao
		cache    db.IRedisDao
		conf     *conf.GameGroupConf
		guildCli guild.IClient
		accCli   account.IClient
	}
	tests := []struct {
		name string
		args args
		want *GameGroupModel
	}{
		{
			name: "NewGameGroupModel",
			args: args{},
			want: &GameGroupModel{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewGameGroupModel(tt.args.store, tt.args.cache, tt.args.conf, tt.args.guildCli, tt.args.accCli); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewGameGroupModel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGameGroupModel_genGroupAccount(t *testing.T) {
	groupId := uint32(1)

	type args struct {
		groupId   uint32
		groupType uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "GameGroupModel.genGroupAccount 0",
			args: args{groupId: groupId, groupType: 0},
			want: "1@guildgroup",
		},
		{
			name: "GameGroupModel.genGroupAccount 1",
			args: args{groupId: groupId, groupType: 1},
			want: "1@gamegroup",
		},
		{
			name: "GameGroupModel.genGroupAccount 2",
			args: args{groupId: groupId, groupType: 2},
			want: "1@gamegroup",
		},
		{
			name: "GameGroupModel.genGroupAccount 3",
			args: args{groupId: groupId, groupType: 3},
			want: "1@group",
		},
		{
			name: "GameGroupModel.genGroupAccount 4",
			args: args{groupId: groupId, groupType: 4},
			want: "1@tgroup",
		},
		{
			name: "GameGroupModel.genGroupAccount 5",
			args: args{groupId: groupId, groupType: 5},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &GameGroupModel{}
			if got := m.genGroupAccount(tt.args.groupId, tt.args.groupType); got != tt.want {
				t.Errorf("genGroupAccount() = %v, want %v", got, tt.want)
			}
		})
	}
}
