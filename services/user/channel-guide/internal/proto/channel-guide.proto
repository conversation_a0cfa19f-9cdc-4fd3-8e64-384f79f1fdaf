syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-guide";
package channel_guide;

service ChannelGuide {
    /* 获取游戏群组配置列表 */
    rpc GetGameGroupConfList (GetGameGroupConfListReq) returns (GetGameGroupConfListRsp) {}
    /* 创建游戏群组配置 */
    rpc CreateGameGroupConf (CreateGameGroupConfReq) returns (CreateGameGroupConfRsp) {}
    /* 删除游戏群组配置 */
    rpc DelGameGroupConf (DelGameGroupConfReq) returns (DelGameGroupConfRsp) {}
    /* 获取游戏群组列表 */
    rpc GetGameGroupList (GetGameGroupListReq) returns (GetGameGroupListRsp) {}

    /* 获取预约开黑配置 */
    rpc GetBookingGangConf (GetBookingGangConfReq) returns (GetBookingGangConfRsp) {}
    /* 设置预约开黑开关 */
    rpc SetBookingGangConf (SetBookingGangConfReq) returns (SetBookingGangConfRsp) {}
    /* 获取开启配置的游戏tab */
    rpc GetEnabledBookingGang (GetEnabledBookingGangReq) returns (GetEnabledBookingGangRsp) {}

    /* 获取所有开黑配置 */
    rpc GetGangConf (GetGangConfReq) returns (GetGangConfRsp) {}
    /* 开黑动态配置 */
    rpc GetGangDynamicConf(GetGangDynamicConfReq) returns (GetGangDynamicConfRsp) {}

    rpc Appoinment (AppoinmentReq) returns (AppoinmentRsp) {}
    rpc SetTeamNotify (SetTeamNotifyReq) returns (SetTeamNotifyRsp) {}
    rpc JoinCarTeam (JoinCarTeamReq) returns (JoinCarTeamRsp) {}
    rpc GetTeaming (GetTeamingReq) returns (GetTeamingRsp) {}
    rpc SetTeamChannel (SetTeamChannelReq) returns (SetTeamChannelRsp) {}
}

message GameGroupConf {
    uint32 game_id = 2;
    uint32 group_id = 3;
}

message GetGameGroupConfListReq {
    uint32 offset = 1;
    uint32 limit = 2;

    uint32 game_id = 3;
    uint32 group_id = 4;
}

message GetGameGroupConfListRsp {
    repeated GameGroupConf conf_list = 1;
}

message CreateGameGroupConfReq {
    // required
    uint32 game_id = 1;
    // required
    uint32 group_id = 2;
}

message CreateGameGroupConfRsp {

}

message DelGameGroupConfReq {
    uint32 game_id = 1;
    uint32 group_id = 2;
}

message DelGameGroupConfRsp {

}

message GetGameGroupListReq {
    // required
    uint32 game_id = 1;
}

// 游戏群组
message GameGroup {
    // 游戏群组成员
    message Member {
        // 群成员 tt id, 用来拼接群头像链接
        string username = 1;
    }

    // 群id
    uint32 id = 1;
    // 群名称
    string name = 2;
    // 群account
    string account = 3;
    // 群人数
    uint32 member_num = 4;
    // 用户是否已加入了群组
    bool user_joined = 5;
    // 进入群组是否需要验证
    uint32 need_verify = 7;

    // 群成员列表
    repeated Member member_list = 6;
}

message GetGameGroupListRsp {
    repeated uint32 group_id_list = 1;
}

message GetBookingGangConfReq {
    // required
    uint32 game_id = 1;
}

message GetBookingGangConfRsp {
    bool enabled = 1;
}

message SetBookingGangConfReq {
    uint32 game_id = 1;
    bool enabled = 2;
}

message SetBookingGangConfRsp {

}

message GetEnabledBookingGangReq {
}

message GetEnabledBookingGangRsp {
    repeated uint32 tab_id_list = 1;
}

message GetGangConfReq {
    // required
    uint32 game_id = 1;
    // required
    uint32 uid = 2;
}

message GetGangConfRsp {
    // 游戏群组信息
    repeated GameGroup group_list = 1;
    // 是否开启对应游戏预约开黑配置
    bool enabled_booking = 2;
    // 预约开黑时间选项(秒)
    repeated int64 booking_durations = 3;
    // 预约开黑倒计时(秒)
    uint32 booking_countdown = 4;
    // 预约开黑状态   对应 AppointmentType
    uint32 booking_state = 5;
    // 用户所在群组 account
    repeated string my_group_acc_list = 6;
}

message GetGangDynamicConfReq {
}

message GetGangDynamicConfRsp {
    // 等待用户确认进房倒计时(秒)
    uint32 wait_confirm_sec = 1;
    // 用户等待进房倒计时(秒)
    uint32 wait_enter_sec = 2;
}

enum AppoinmentType {
    NoneAppoinment = 0; //没有预约
    FirstAppoinment = 1;    //第一次预约
    SecondAppoinment = 2;   //第二次预约（选游戏卡）
}

message GameCardOpt
{
    string opt_name = 1;               //段位,区服等字段
    repeated string value_list = 2;    // 选项属性，例如段位会有:青铜，白银，王者等
    uint32 opt_id = 3;                 //还是加上这个吧
}

message AppoinmentReq{
    uint32 uid = 1;
    AppoinmentType appoinment_type = 2;    //预约类型
    repeated GameCardOpt  opt_list = 3;   //游戏卡选项
    uint32 time_duration = 4;        //时长杪
    uint32 game_tab_id = 5;     //游戏id
    string game_tab_name = 6;  //游戏名称
    bool is_set = 7;
    int64 over_time = 8;
}

message AppoinmentRsp{
    AppoinmentType appoinment_type = 1;
    repeated int64 select_appoinment_time = 2;
    int64 over_time = 3;
}

message TeamNofifyInfo {
    repeated uint32 team_uids = 1;  //车队候选组成员（若自己自己加上还是服务器加上？）
    uint32 game_tab_id     = 2;//游戏id
    string game_tab_name        = 3;//游戏名称
    string teamid = 4; //车队id
}

message SetTeamNotifyReq{
    repeated TeamNofifyInfo notify_info = 1;
}

message TeamNotifyRspInfo {
    string ori_teamid = 1;
    string dst_teamid = 2;
}
message SetTeamNotifyRsp{
    repeated TeamNotifyRspInfo rsp_info = 1;
    uint32 wait_confirm_sec = 2;
    uint32 wait_enter_sec = 3;
}

message JoinCarTeamReq{
    uint32 game_tab_id = 1; //游戏id
    string game_tab_name = 2;  //游戏名称
    string teamid = 3; //车队id
    uint32 uid = 4;
}

enum JoinStatus {
    JoinStatusGoOn = 0;
    JoinStatusSuc = 1;
    JoinStatusSucExceed = 2;
    JoinStatusFail = 3;
}

message JoinCarTeamRsp{
    JoinStatus join_status = 1;
    uint32  channelid = 2;
    repeated uint32 team_suc_uids = 3;
    repeated uint32 team_uids = 4;
    string ori_teamid = 5;
}

message TeamingInfo {
    uint32 game_tab_id = 1; //游戏id
    string game_tab_name = 2;  //游戏名称
    string teamid = 3; //车队id
    repeated uint32 suc_uids = 4;
    repeated uint32 all_uids = 5;
    string ori_teamid = 6;
}

message GetTeamingReq{
}

message GetTeamingRsp{
    repeated TeamingInfo infos = 1;
}

message SetTeamChannelReq{
    uint32 channelid = 1;
    string teamid = 2;
    repeated uint32 suc_uids = 3;
    uint32 game_tabid = 4;
    bool is_exceed = 5;
}

message SetTeamChannelRsp{
}
