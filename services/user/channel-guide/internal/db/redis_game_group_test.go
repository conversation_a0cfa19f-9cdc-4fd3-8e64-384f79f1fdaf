package db

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis"
	"reflect"
	"testing"
)

func TestRedisDao_SetGameGroupList(t *testing.T) {
	rd, err := miniredis.Run()
	if err != nil {
		t.Fatalf("TestRedisDao_SetGameGroupList err: %v", err)
	}

	defer rd.Close()

	var (
		rc = redis.NewClient(&redis.Options{Addr: rd.Addr()})

		gameId      = uint32(1)
		groupIdList = []uint32{1, 2, 3}
	)

	type fields struct {
		cli *redis.Client
	}
	type args struct {
		ctx         context.Context
		gameId      uint32
		groupIdList []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "RedisDao.SetGameGroupList",
			fields:  fields{cli: rc},
			args:    args{ctx: context.Background(), gameId: gameId, groupIdList: groupIdList},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &RedisDao{
				cli: tt.fields.cli,
			}
			if err := r.SetGameGroupList(tt.args.ctx, tt.args.gameId, tt.args.groupIdList); (err != nil) != tt.wantErr {
				t.Errorf("SetGameGroupList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRedisDao_DelGameGroupList(t *testing.T) {
	rd, err := miniredis.Run()
	if err != nil {
		t.Fatalf("TestRedisDao_DelGameGroupList err: %v", err)
	}

	defer rd.Close()

	var (
		rc = redis.NewClient(&redis.Options{Addr: rd.Addr()})

		gameId = uint32(1)
	)

	type fields struct {
		cli *redis.Client
	}
	type args struct {
		ctx    context.Context
		gameId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "RedisDao.DelGameGroupList",
			fields:  fields{cli: rc},
			args:    args{ctx: context.Background(), gameId: gameId},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &RedisDao{
				cli: tt.fields.cli,
			}
			if err := r.DelGameGroupList(tt.args.ctx, tt.args.gameId); (err != nil) != tt.wantErr {
				t.Errorf("DelGameGroupList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRedisDao_GetGameGroupList(t *testing.T) {
	rd, err := miniredis.Run()
	if err != nil {
		t.Fatalf("TestRedisDao_GetGameGroupList err: %v", err)
	}

	defer rd.Close()

	var (
		rc = redis.NewClient(&redis.Options{Addr: rd.Addr()})

		gameId      = uint32(1)
		groupIdList = []uint32{1, 2, 3}
	)

	data, _ := json.Marshal(groupIdList)
	_ = rd.Set(fmt.Sprintf("game_group_list_%d", gameId), string(data))

	type fields struct {
		cli *redis.Client
	}
	type args struct {
		ctx    context.Context
		gameId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []uint32
		wantErr bool
	}{
		{
			name:    "RedisDao.GetGameGroupList",
			fields:  fields{cli: rc},
			args:    args{ctx: context.Background(), gameId: gameId},
			want:    groupIdList,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &RedisDao{
				cli: tt.fields.cli,
			}
			got, err := r.GetGameGroupList(tt.args.ctx, tt.args.gameId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGameGroupList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetGameGroupList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRedisDao_CloseUserGameGroupGuide(t *testing.T) {
	rd, err := miniredis.Run()
	if err != nil {
		t.Fatalf("TestRedisDao_CloseUserGameGroupGuide err: %v", err)
	}

	var (
		rc = redis.NewClient(&redis.Options{Addr: rd.Addr()})

		game = uint32(1)
		day  = uint32(1)
		uid  = uint32(1)
	)

	defer rd.Close()

	type fields struct {
		cli *redis.Client
	}
	type args struct {
		ctx  context.Context
		game uint32
		day  uint32
		uid  uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "RedisDao.CloseUserGameGroupGuide",
			fields:  fields{cli: rc},
			args:    args{ctx: context.Background(), game: game, day: day, uid: uid},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &RedisDao{
				cli: tt.fields.cli,
			}
			if err := r.CloseUserGameGroupGuide(tt.args.ctx, tt.args.game, tt.args.day, tt.args.uid); (err != nil) != tt.wantErr {
				t.Errorf("CloseUserGameGroupGuide() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRedisDao_GetUserGameGroupGuide(t *testing.T) {
	rd, err := miniredis.Run()
	if err != nil {
		t.Fatalf("TestRedisDao_GetUserGameGroupGuide err: %v", err)
	}

	defer rd.Close()

	var (
		rc = redis.NewClient(&redis.Options{Addr: rd.Addr()})

		game = uint32(1)
		day  = uint32(1)
		uid  = uint32(1)
	)
	type fields struct {
		cli *redis.Client
	}
	type args struct {
		ctx  context.Context
		game uint32
		day  uint32
		uid  uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name:    "RedisDao.GetUserGameGroupGuide",
			fields:  fields{cli: rc},
			args:    args{ctx: context.Background(), game: game, day: day, uid: uid},
			want:    false,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &RedisDao{
				cli: tt.fields.cli,
			}
			got, err := r.GetUserGameGroupGuide(tt.args.ctx, tt.args.game, tt.args.day, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserGameGroupGuide() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetUserGameGroupGuide() got = %v, want %v", got, tt.want)
			}
		})
	}
}
