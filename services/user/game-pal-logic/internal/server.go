package internal

import (
	"context"
	"golang.52tt.com/pkg/speedlimit"
	topicChannelPB "golang.52tt.com/protocol/app/topic-channel"
	game_card "golang.52tt.com/protocol/services/game-card"
	game_pal "golang.52tt.com/protocol/services/game-pal"
	"golang.52tt.com/protocol/services/topic_channel/channel"
	"golang.52tt.com/protocol/services/topic_channel/rcmd_game_pal_card"
	"golang.52tt.com/protocol/services/topic_channel/rcmd_game_pal_user"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	user_online "golang.52tt.com/protocol/services/user-online"
	"golang.52tt.com/services/user/game-pal-logic/internal/convertor"
	"golang.52tt.com/services/user/game-pal-logic/internal/mgr/game_image"
	"golang.52tt.com/services/user/game-pal-logic/internal/mgr/greeting_mgr"
	"golang.52tt.com/services/user/game-pal-logic/internal/mgr/guide_mgr"
	"math/rand"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/game-pal-logic"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/services/user/game-pal-logic/internal/cache"
	"golang.52tt.com/services/user/game-pal-logic/internal/conf"
	config "golang.52tt.com/services/user/game-pal-logic/internal/config/ttconfig/game_pal_logic"
	"golang.52tt.com/services/user/game-pal-logic/internal/mgr/card_mgr"
	"golang.52tt.com/services/user/game-pal-logic/internal/mgr/feed_mgr"
	"golang.52tt.com/services/user/game-pal-logic/internal/rpc"
)

const (
	screenAuditPass   = uint32(2)
	screenAuditReject = uint32(1)
)

func NewServer(ctx context.Context, cfg *conf.StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	err := rpc.Init(ctx)
	if err != nil {
		return nil, err
	}

	err = config.InitGamePalLogicConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer config.InitGamePalLogicConfig err: %v", err)
		return nil, err
	}

	err = cache.NewGamePalCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer cache.NewGamePalCache(ctx) err:%v", err)
		return nil, err
	}
	err = cache.NewTabInfoCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer cache.NewTabInfoCache(ctx) err:%v", err)
		return nil, err
	}
	err = cache.NewGameCardCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer cache.NewGameCardCache(ctx) err:%v", err)
		return nil, err
	}
	duration := cfg.TimeoutDuration
	if duration == 0 {
		duration = 20
	}
	errOverInst := speedlimit.NewErrorOver(duration, cfg.RcmdNilDoudiRatio)
	return &Server{
		cardMgr:     card_mgr.NewManager(),
		greetingMgr: greeting_mgr.NewGreetingMgr(),
		errOverInst: errOverInst,
	}, nil
}

type Server struct {
	cardMgr     *card_mgr.Manager
	greetingMgr *greeting_mgr.GreetingMgr
	errOverInst *speedlimit.ErrorOver
}

func (s *Server) ShutDown() {}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) CheckPublishCondition(ctx context.Context, req *pb.CheckPublishConditionRequest) (*pb.CheckPublishConditionResponse, error) {
	resp := new(pb.CheckPublishConditionResponse)
	log.InfoWithCtx(ctx, "CheckPublishCondition req: %+v", req)

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	var err error
	switch pb.GamePalPublishType(req.GetPublishType()) {
	case pb.GamePalPublishType_GAME_PAL_PUBLISH_TYPE_UNSPECIFIED:
		_, err = card_mgr.CheckPublishCond(ctx, svcInfo.UserID, req.GetTabId())
	case pb.GamePalPublishType_GAME_PAL_PUBLISH_TYPE_SUPER:
		_, err = card_mgr.CheckSuperPublishCond(ctx, svcInfo, req.GetTabId())
	default:
		log.WarnWithCtx(ctx, "CheckPublishCondition invalid publishType %d", req.GetPublishType())
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	if err != nil {
		rpcErr := protocol.ToServerError(err)
		if rpcErr.Code() == status.ErrSys || rpcErr.Code() == status.ErrGamePalCardEditCardAgain {
			return resp, err
		}

		resp.Banned = true
		resp.BanReason = rpcErr.Message()
		return resp, nil
	}

	log.InfoWithCtx(ctx, "CheckPublishCondition resp: %+v", resp)
	return resp, nil
}

// GetGamePalCardProps 获取游戏搭子卡属性
func (s *Server) GetGamePalCardProps(ctx context.Context, req *pb.GetGamePalCardPropsRequest) (resp *pb.GetGamePalCardPropsResponse, err error) {
	log.InfoWithCtx(ctx, "GetGamePalCardProps req: %+v", req)

	var (
		tab   = cache.GetTabInfoById(req.GetTabId())
		decls = cache.GetTabSocialDecls(req.GetTabId())
	)
	rand.Shuffle(len(decls), func(i, j int) {
		decls[i], decls[j] = decls[j], decls[i]
	})

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
		return resp, err
	}

	resp = &pb.GetGamePalCardPropsResponse{
		Props:       cache.GetPalReleaseConditionByTabId(req.GetTabId()),
		SocialDecls: decls,
		Tab: &pb.GamePalTab{
			Id:   tab.GetId(),
			Name: tab.GetName(),
			//ImageUrl: tab.GetCardsImageUrl(),
			ImageUrl: tab.GetSmallCardUrl(),
		},
		AudioConf: &pb.AudioConf{
			DemoTitle:   config.GetGamePalLogicConfig().GetAudioDemoTitle(),
			DemoContent: config.GetGamePalLogicConfig().GetResortAudioDemoContent(),
		},
	}
	imageHandler := game_image.NewGameImageHandler(svcInfo.ClientVersion)
	if imageHandler.CheckNeedImageVersion() {
		s.fillPropPersonImageInfo(ctx, imageHandler, svcInfo, req.GetTabId(), resp)
		s.fillPropGameCardInfo(ctx, svcInfo, tab, resp)
	}
	log.InfoWithCtx(ctx, "GetGamePalCardProps success, tabId:%d, len(props):%d, len(socialDecls):%s, audioDemoTitle:%s, len(audioDemoContent):%d",
		req.GetTabId(), len(resp.GetProps()), len(resp.GetSocialDecls()), resp.GetAudioConf().GetDemoTitle(), len(resp.GetAudioConf().GetDemoContent()))
	return resp, nil
}

// 不影响主流程，不抛出error
func (s *Server) fillPropPersonImageInfo(ctx context.Context, imageHandler *game_image.GameImageHandler, svcInfo *protogrpc.ServiceInfo,
	tabId uint32, resp *pb.GetGamePalCardPropsResponse) {

	imageMap, err := imageHandler.GetPersonImage(ctx, []game_image.ReqParam{{Uid: svcInfo.UserID, TabId: tabId}})
	if err != nil {
		log.WarnWithCtx(ctx, "fillPropPersonImageInfo GetPersonImage svcInfo:%s tabId:%d err:%v", svcInfo.String(), tabId, err)
		return
	}
	resp.PersonImage = imageMap[imageHandler.GenKey(svcInfo.UserID, tabId)]
	//编辑页空形象特殊填充逻辑
	if !resp.GetPersonImage().GetIsImageExist() {
		selfRateInternal := imageHandler.GetSelfRateInternal(ctx, tabId)
		resp.PersonImage.GameKeyWords = config.GetGamePalLogicConfig().GetPropEmptyImageKeyWord()
		resp.PersonImage.GameKeyWordsDesc = config.GetGamePalLogicConfig().GetPropEmptyImageKeyWordsDesc(selfRateInternal)
	}
}

// 不影响主流程，没有不报错，客户端兜底拿主题色
func (s *Server) fillPropGameCardInfo(ctx context.Context, svcInfo *protogrpc.ServiceInfo, tab *tabPB.Tab, resp *pb.GetGamePalCardPropsResponse) {
	if tab == nil {
		log.WarnWithCtx(ctx, "fillPropGameCardInfo tab is nil svcInfo:%s", svcInfo.String())
		return
	}
	resp.Tab.BackColorNum = cache.GetGameCardBackColorNumByTabId(tab.GetId())

	uid := svcInfo.UserID
	cards, err := rpc.GameCardClient.GetGameCard(ctx, uid)
	if err != nil {
		log.WarnWithCtx(ctx, "fillPropGameCardInfo GetGameCard uid:%d tab:%s err:%v", uid, tab.String(), err)
		return
	}
	for _, card := range cards {
		if card.GetUGameId() == tab.GetUGameId() {
			resp.PhotoInfos = genGameCardScreenShotInfo(svcInfo.MarketID, uint32(svcInfo.ClientType), card.GetScreenshotList())
			break
		}
	}
}

func genGameCardScreenShotInfo(marketId, clientType uint32, screenShotList []*game_card.GameScreenshot) []*pb.PhotoInfo {
	if len(screenShotList) == 0 {
		return nil
	}
	var photoInfos = make([]*pb.PhotoInfo, 0, len(screenShotList))
	for _, v := range screenShotList {
		if v.AuditStatus != screenAuditPass {
			continue
		}
		photoInfos = append(photoInfos, &pb.PhotoInfo{
			ImageUrl:    convertor.ReplaceObsDomainByMarketId(marketId, clientType, v.GetImgUrl()),
			ImageSource: uint32(pb.ImageSource_IMAGE_SOURCE_GAME_CARD),
		})
	}
	return photoInfos

}

// PublishGamePalCard 发布游戏搭子卡
func (s *Server) PublishGamePalCard(ctx context.Context, req *pb.PublishGamePalCardRequest) (resp *pb.PublishGamePalCardResponse, err error) {
	resp = new(pb.PublishGamePalCardResponse)
	log.InfoWithCtx(ctx, "PublishGamePalCard req: %+v", req)

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
		return
	}

	resp.BaseResp, err = checkCardRisk(ctx, req.GetBaseReq(), riskSceneGamePalCardPublish, svcInfo.UserID, req.GetCard().GetBaseCard().GetBaseInfo().GetTabId())
	if err != nil {
		return resp, err
	}

	imageHandler := game_image.NewGameImageHandler(svcInfo.ClientVersion)
	err = s.cardMgr.PublishGamePalCard(ctx, svcInfo, req.GetCard(), imageHandler)
	if err != nil {
		log.ErrorWithCtx(ctx, "PublishGamePalCard req:%s err: %v", req.String(), err)
		return
	}

	//log.InfoWithCtx(ctx, "PublishGamePalCard resp: %+v", resp)
	return
}

// LightenGamePalCard 点亮游戏搭子卡
func (s *Server) LightenGamePalCard(ctx context.Context, req *pb.LightenGamePalCardRequest) (*pb.LightenGamePalCardResponse, error) {
	resp := new(pb.LightenGamePalCardResponse)
	log.InfoWithCtx(ctx, "LightenGamePalCard req: %+v", req)

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	switch req.GetOp() {
	case uint32(pb.LightenGamePalCardRequest_OP_SINGLE):
		if len(req.GetGamePalCardId()) == 0 {
			return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
		}

		cardResp, err := rpc.GamePalClient.GetGamePalCard(ctx, &game_pal.GetGamePalCardReq{Id: req.GetGamePalCardId()})
		if err != nil {
			log.ErrorWithCtx(ctx, "LightenGamePalCard GetGamePalCard id(%d) err: %v", req.GetGamePalCardId(), err)
			return resp, err
		}

		card := cardResp.GetCard()
		if card == nil || card.GetUid() != svcInfo.UserID {
			return resp, protocol.NewExactServerError(nil, status.ErrGamePalCardNotFound)
		}

		resp.BaseResp, err = checkCardRisk(ctx, req.GetBaseReq(), riskSceneGamePalCardLighten, svcInfo.UserID, card.GetTabId())
		if err != nil {
			return resp, err
		}

		imageHandler := game_image.NewGameImageHandler(svcInfo.ClientVersion)
		err = s.cardMgr.LightenGamePalCard(ctx, card, imageHandler)
		if err != nil {
			log.ErrorWithCtx(ctx, "LightenGamePalCard LightenGamePalCard card(%d) err: %v", card, err)
			return resp, err
		}
	case uint32(pb.LightenGamePalCardRequest_OP_ALL):
		var err error
		resp.BaseResp, err = checkCardRisk(ctx, req.GetBaseReq(), riskSceneGamePalCardLighten, svcInfo.UserID, 0)
		if err != nil {
			return resp, err
		}

		err = s.cardMgr.LightenAllGamePalCard(ctx, svcInfo.UserID)
		if err != nil {
			log.ErrorWithCtx(ctx, "LightenGamePalCard LightenAllGamePalCard uid(%d) err: %v", svcInfo.UserID, err)
			return resp, err
		}
	default:
		log.WarnWithCtx(ctx, "LightenGamePalCard invalid op %d", req.GetOp())
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	return resp, nil
}

// GetGamePalCardList 获取用户所有游戏主题搭子卡列表
func (s *Server) GetGamePalCardList(ctx context.Context, req *pb.GetGamePalCardListRequest) (resp *pb.GetGamePalCardListResponse, err error) {
	resp = new(pb.GetGamePalCardListResponse)
	//log.DebugWithCtx(ctx, "GetGamePalCardList req: %+v", req)

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
		return resp, err
	}

	uid := req.GetUid()
	if uid == 0 {
		uid = svcInfo.UserID
	}

	imageHandler := game_image.NewGameImageHandler(svcInfo.ClientVersion)
	resp.Cards, err = s.cardMgr.GetGamePalCardList(ctx, svcInfo, uid, imageHandler)
	if err != nil {
		log.ErrorWithCtx(ctx, `GetGamePalCardList req:%s err: %v`, req.String(), err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "GetGamePalCardList req:%s len(cards): %d", req.String(), len(resp.GetCards()))
	return
}

// GetGamePalCard 获取用户单个游戏主题搭子卡
func (s *Server) GetGamePalCard(ctx context.Context, req *pb.GetGamePalCardRequest) (resp *pb.GetGamePalCardResponse, err error) {
	resp = new(pb.GetGamePalCardResponse)
	//log.DebugWithCtx(ctx, "GetGamePalCard req: %+v", req)

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
		return
	}
	imageHandler := game_image.NewGameImageHandler(svcInfo.ClientVersion)
	resp.Card, err = s.cardMgr.GetGamePalCard(ctx, svcInfo, req.GetUid(), req.GetTabId(), imageHandler)
	if err != nil {
		return
	}

	//log.DebugWithCtx(ctx, "GetGamePalCard resp: %+v", resp)
	return
}

// DeleteGamePalCard 删除游戏搭子卡
func (s *Server) DeleteGamePalCard(ctx context.Context, req *pb.DeleteGamePalCardRequest) (resp *pb.DeleteGamePalCardResponse, err error) {
	resp = new(pb.DeleteGamePalCardResponse)
	log.InfoWithCtx(ctx, "DeleteGamePalCard req: %+v", req)

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		err = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
		return
	}

	err = s.cardMgr.DeleteGamePalCard(ctx, svcInfo.UserID, req.GetGamePalCardId())
	if err != nil {
		return
	}

	log.InfoWithCtx(ctx, "DeleteGamePalCard resp: %+v", resp)
	return
}

// GetGamePalFilterByTabId 获取搭子卡筛选项
func (s *Server) GetGamePalFilterByTabId(ctx context.Context, req *pb.GetGamePalFilterByTabIdRequest) (*pb.GetGamePalFilterByTabIdResponse, error) {
	out := &pb.GetGamePalFilterByTabIdResponse{}
	if req.GetTabId() == 0 {
		log.WarnWithCtx(ctx, "GetGamePalFilterByTabId invalid param req:%v", req.String())
		return out, nil
	}
	gamePalBlocks := cache.GetPalReleaseConditionByTabId(req.GetTabId())
	out.GamePalBlocks = make([]*pb.GamePalBlock, 0, len(gamePalBlocks))
	//固定返回不限,安卓复用组件需要将mostSelectNum=0
	for _, v := range gamePalBlocks {
		out.GamePalBlocks = append(out.GamePalBlocks, &pb.GamePalBlock{
			Id:            v.GetId(),
			Name:          v.GetName(),
			MostSelectNum: 0,
			Elems:         v.GetElems(),
			Mode:          uint32(pb.GamePalBlock_MODE_MULTI),
		})
	}
	if req.GetIsShowLabel() {
		rcmdResp, err := rpc.RcmdGamePalClient.GetPalCardLabels(ctx, &rcmd_game_pal_card.GetPalCardLabelsReq{TabId: req.GetTabId()})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGamePalFilterByTabId GetPalCardLabels req:%s err: %v", req.String(), err)
			return out, nil
		}
		out.RcmdLabels = make([]*pb.GamePalCardLabel, 0, len(rcmdResp.GetLabels()))
		for _, v := range rcmdResp.GetLabels() {
			out.RcmdLabels = append(out.RcmdLabels, &pb.GamePalCardLabel{
				Label:     v.GetLabel(),
				LabelType: v.GetLabelType(),
			})
		}
	}
	log.InfoWithCtx(ctx, "GetGamePalFilterByTabId req:%s, len(out.GamePalBlocks):%d, len(out.RcmdLabels):%d",
		req.String(), len(out.GetGamePalBlocks()), len(out.GetRcmdLabels()))
	return out, nil
}

// GetGamePalList 获取搭子卡列表
func (s *Server) GetGamePalList(ctx context.Context, req *pb.GetGamePalListReq) (*pb.GetGamePalListResp, error) {
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return &pb.GetGamePalListResp{}, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取用户信息失败")
	}
	rcmdDataHandler := feed_mgr.NewRcmdGamePalCardData(s.errOverInst)
	imageHandler := game_image.NewGameImageHandler(serviceInfo.ClientVersion)
	out, err := feed_mgr.NewGamePalFeedMgr(rcmdDataHandler).GetGamePalList(ctx, serviceInfo, req, imageHandler)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGamePalList req:%s err: %v", req.String(), err)
	}
	log.InfoWithCtx(ctx, "GetGamePalList  req:%s len(out.GetItems):%d", req.String(), len(out.GetItems()))

	return out, err
}

func (s *Server) GamePalSuperPublish(ctx context.Context, req *pb.GamePalSuperPublishRequest) (*pb.GamePalSuperPublishResponse, error) {
	resp := new(pb.GamePalSuperPublishResponse)
	log.InfoWithCtx(ctx, "GamePalSuperPublish req: %+v", req)

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	if req.GetTabId() == 0 || req.GetGreetingId() == 0 {
		log.WarnWithCtx(ctx, "GamePalSuperPublish invalid req: %+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	var err error
	resp.BaseResp, err = checkCardRisk(ctx, req.GetBaseReq(), riskSceneGamePalSuperPublish, svcInfo.UserID, req.GetTabId())
	if err != nil {
		return resp, err
	}

	card, err := card_mgr.CheckSuperPublishCond(ctx, svcInfo, req.GetTabId())
	if err != nil {
		return resp, err
	}

	resp.UserList, resp.MsgContent, err = s.cardMgr.GamePalSuperPublish(ctx, svcInfo, card, req.GetGender(), req.GetGreetingId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GamePalSuperPublish card(%+v) err: %v", card, err)
		return resp, err
	}

	log.InfoWithCtx(ctx, "GamePalSuperPublish resp: %+v", resp)
	return resp, nil
}

func (s *Server) GetGamePalSuperPublishProps(ctx context.Context, req *pb.GetGamePalSuperPublishPropsRequest) (*pb.GetGamePalSuperPublishPropsResponse, error) {
	resp := new(pb.GetGamePalSuperPublishPropsResponse)
	if req.GetTabId() == 0 {
		log.WarnWithCtx(ctx, "GetGamePalSuperPublishProps invalid req: %+v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	resp.SuperPublishCd = config.GetGamePalLogicConfig().GetSuperPublishCd()
	resp.Greetings = config.GetGamePalLogicConfig().GetSuperPublishGreetings(cache.GetTabInfoById(req.GetTabId()).GetName())

	//log.DebugWithCtx(ctx, "GetGamePalSuperPublishProps resp: %+v", resp)
	return resp, nil
}

func (s *Server) ReportGamePalGreeting(ctx context.Context, req *pb.ReportGamePalGreetingRequest) (*pb.ReportGamePalGreetingResponse, error) {
	resp := new(pb.ReportGamePalGreetingResponse)

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	log.InfoWithCtx(ctx, "ReportGamePalGreeting uid:%d", svcInfo.UserID)

	greetingResp, err := rpc.GamePalClient.RecordUserGreeting(ctx, &game_pal.RecordUserGreetingReq{Uid: svcInfo.UserID})
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportGamePalGreeting IncrUserGreetingCount uid(%d) err: %v", 0, err)
		return resp, err
	}

	greetingInfo := greetingResp.GetGreetingInfo()
	if greetingInfo.GetCount() < greetingInfo.GetLimit() {
		resp.Remain = greetingInfo.GetLimit() - greetingInfo.GetCount()
	}

	return resp, nil
}

func (s *Server) GetGreetingGamePalUserList(ctx context.Context, req *pb.GetGreetingGamePalUserListRequest) (*pb.GetGreetingGamePalUserListResponse, error) {
	resp := new(pb.GetGreetingGamePalUserListResponse)
	//log.InfoWithCtx(ctx, "GetGreetingGamePalUserList req: %+v", req)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.WarnWithCtx(ctx, "GetGreetingGamePalUserList invalid uid")
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	rcmdReq := &rcmd_game_pal_user.GetGreetingUserReq{
		Uid:   uid,
		Scene: uint32(rcmd_game_pal_user.GetGreetingUserReq_GreetHalfScreen),
	}
	rcmdResp, err := rpc.RcmdGamePalUserClient.GetGreetingUser(ctx, rcmdReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGreetingGamePalUserList req(%+v) err: %v", rcmdReq, err)
		return resp, err
	}
	if len(rcmdResp.GetUidList()) == 0 {
		log.WarnWithCtx(ctx, "GetGreetingGamePalUserList rcmd uid empty")
		return resp, nil
	}

	log.InfoWithCtx(ctx, "GetGreetingGamePalUserList rcmdResp: %+v", rcmdResp)

	userMap, err := rpc.AccountClient.GetUsersMap(ctx, rcmdResp.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGreetingGamePalUserList uidList(%+v) err: %v", rcmdResp.GetUidList())
		return resp, err
	}

	onlineMap, rpcErr := rpc.UserOnlineClient.BatchGetLastMobileOnlineInfo(ctx, rcmdResp.GetUidList())
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "GetGreetingGamePalUserList BatchGetLastMobile uid(%+v) err: %v", rcmdResp.GetUidList())
		return resp, rpcErr
	}

	for _, rcmdUid := range rcmdResp.GetUidList() {
		rcmdUser := userMap[rcmdUid]
		if rcmdUser == nil {
			log.WarnWithCtx(ctx, "GetGreetingGamePalUserList user %d not found", rcmdUid)
			continue
		}

		onlineState := pb.GreetingGamePalUser_ONLINE_STATE_OFFLINE
		switch onlineMap[rcmdUid].GetOnlineType() {
		case user_online.OnlineType_ONLINE_TYPE_ONLINE:
			onlineState = pb.GreetingGamePalUser_ONLINE_STATE_ONLINE
		default:
			onlineState = pb.GreetingGamePalUser_ONLINE_STATE_OFFLINE
		}

		resp.UserList = append(resp.UserList, &pb.GreetingGamePalUser{
			Uid:         rcmdUser.GetUid(),
			Sex:         uint32(rcmdUser.GetSex()),
			OnlineState: uint32(onlineState),
			Account:     rcmdUser.GetUsername(),
			Nickname:    rcmdUser.GetNickname(),
		})
	}

	log.InfoWithCtx(ctx, "GetGreetingGamePalUserList resp: %+v", resp)
	return resp, nil
}

func (s *Server) GetIMShowGamePalCard(ctx context.Context, req *pb.GetIMShowGamePalCardRequest) (*pb.GetIMShowGamePalCardResponse, error) {
	resp := new(pb.GetIMShowGamePalCardResponse)
	//log.DebugWithCtx(ctx, "GetIMShowGamePalCard req: %+v", req)

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		err := protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
		return resp, err
	}

	imUid, _, rpcErr := rpc.AccountCli.GetUidByName(ctx, req.GetImAccount())
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "GetIMShowGamePalCard GetUidByName err: %v, req:%s", rpcErr, req.String())
		return resp, rpcErr
	}
	// 拿到首次打招呼的玩法，查询im聊天对方该玩法的搭子卡
	imageHandler := game_image.NewGameImageHandler(svcInfo.ClientVersion)
	item, err := s.cardMgr.GetIMShowGamePalCard(ctx, svcInfo, imUid, imageHandler)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIMShowGamePalCard err: %v, req:%s", err, req.String())
		return resp, err
	}
	resp.Item = item
	log.InfoWithCtx(ctx, "GetIMShowGamePalCard reqUser: %d, imAccount: %s, imUid: %d, gamePalCardId: %d",
		svcInfo.UserID, req.GetImAccount(), imUid, item.GetGamePalCardInfo().GetBaseInfo().GetId())
	return resp, nil
}

func (s *Server) FilterGreetingGamePalUserList(ctx context.Context, req *pb.FilterGreetingGamePalUserListRequest) (*pb.FilterGreetingGamePalUserListResponse, error) {
	resp := new(pb.FilterGreetingGamePalUserListResponse)
	//log.DebugWithCtx(ctx, "FilterGreetingGamePalUserList req: %+v", req)

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		err := protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
		return resp, err
	}

	greetingUserList, err := s.greetingMgr.FilterGreetingGamePalUserList(ctx, svcInfo.UserID, req.GetImAccountList())
	if err != nil {
		log.ErrorWithCtx(ctx, "FilterGreetingGamePalUserList err: %v, req:%s", err, req.String())
		return resp, err
	}

	resp.GreetingAccountList = greetingUserList
	log.InfoWithCtx(ctx, "FilterGreetingGamePalUserList reqUid: %d, req:%s, resp:%s", svcInfo.UserID, req.String(), resp.String())
	return resp, nil
}

func (s *Server) ShowUserGamePalGuide(ctx context.Context, req *pb.ShowUserGamePalGuideRequest) (*pb.ShowUserGamePalGuideResponse, error) {
	resp := new(pb.ShowUserGamePalGuideResponse)

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		err := protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
		return resp, err
	}
	//log.DebugWithCtx(ctx, "ShowUserGamePalGuide reqUid: %d", svcInfo.UserID)

	showGuide, err := guide_mgr.ShowUserGamePalGuide(ctx, svcInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "ShowUserGamePalGuide CheckNeedShowGamePalGuide err: %v", err)
		return resp, err
	}
	resp.ShowGuide = showGuide
	//log.InfoWithCtx(ctx, "ShowUserGamePalGuide reqUid: %d, resp: %+v", svcInfo.UserID, resp)
	return resp, nil
}

func (s *Server) GetImTabGamePalList(ctx context.Context, req *pb.GetImTabGamePalListReq) (*pb.GetImTabGamePalListResp, error) {
	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || svcInfo.UserID == 0 {
		return &pb.GetImTabGamePalListResp{}, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	rcmdDataHandler := feed_mgr.NewRcmdGamePalCardData(s.errOverInst)
	feedMgr := feed_mgr.NewGamePalFeedMgr(rcmdDataHandler)
	resp, err := feedMgr.GetImTabGamePalList(ctx, svcInfo, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetImTabGamePalList GetGamePalList err: %v", err)
		return resp, err
	}
	//log.InfoWithCtx(ctx, "GetImTabGamePalList reqUid: %d, req: %+v, resp: %+v", svcInfo.UserID, req, resp)

	return resp, nil
}

func (s *Server) HandleGamePalCardOnPublishRoom(ctx context.Context, req *pb.HandleGamePalCardOnPublishRoomRequest) (*pb.HandleGamePalCardOnPublishRoomResponse, error) {
	resp := &pb.HandleGamePalCardOnPublishRoomResponse{}
	//log.InfoWithCtx(ctx, "HandleGamePalCardOnPublishRoom req:%+v", req)

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || svcInfo.UserID == 0 {
		log.WarnWithCtx(ctx, "HandleGamePalCardOnPublishRoom invalid svcInfo.UserID == 0")
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	// 后续处理出错不需要有任何提示，不抛出err
	uid := svcInfo.UserID
	if err := s.checkHandleGamePalCardOnPublishRoomParams(ctx, uid, req); err != nil {
		return resp, nil
	}

	// 获取用户该玩法搭子卡
	cardResp, err := rpc.GamePalClient.GetUserGamePalCard(ctx, &game_pal.GetUserGamePalCardReq{
		Uid:   uid,
		TabId: req.GetTabId(),
	})
	if err != nil {
		log.WarnWithCtx(ctx, "HandleGamePalCardOnPublishRoom GetUserGamePalCard err:%v", err)
		return resp, nil
	}

	card := cardResp.GetCard()
	// 用户有该玩法的搭子卡
	if card != nil {
		//log.DebugWithCtx(ctx, "HandleGamePalCardOnPublishRoom user(%d) has tabId(%d) game pal card: %+v", uid, req.GetTabId(), card)
		// 检查是否满足风控限制
		resp.BaseResp, err = checkCardRisk(ctx, req.GetBaseReq(), riskSceneGamePalCardLighten, svcInfo.UserID, card.GetTabId())
		if err != nil {
			log.WarnWithCtx(ctx, "HandleGamePalCardOnPublishRoom lighten checkCardRisk err:%v, uid:%d, tabId:%d", err, uid, req.GetTabId())
			return resp, nil
		}

		// 如果满足擦亮条件限制，帮用户擦亮该搭子卡
		imageHandler := game_image.NewGameImageHandler(svcInfo.ClientVersion)
		err = s.cardMgr.LightenGamePalCard(ctx, card, imageHandler)
		if err != nil {
			log.WarnWithCtx(ctx, "HandleGamePalCardOnPublishRoom LightenGamePalCard card(%s) err:%v", card.GetId(), err)
			return resp, nil
		}
	} else { // 用户没有该玩法的搭子卡
		// 检查是否满足风控限制
		resp.BaseResp, err = checkCardRisk(ctx, req.GetBaseReq(), riskSceneGamePalCardPublish, svcInfo.UserID, req.GetTabId())
		if err != nil {
			log.WarnWithCtx(ctx, "HandleGamePalCardOnPublishRoom publish checkCardRisk err:%v, uid:%d, tabId:%d", err, uid, req.GetTabId())
			return resp, nil
		}
		// 检查搭子卡发布条件限制
		_, err = card_mgr.CheckPublishCond(ctx, uid, req.GetTabId())
		if err != nil {
			log.WarnWithCtx(ctx, "HandleGamePalCardOnPublishRoom CheckPublishCond err:%v, uid:%d, tabId:%d", err, uid, req.GetTabId())
			return resp, nil
		}

		// 可以新增搭子卡，告知客户端发公屏消息展示创建搭子卡引导
		resp.ShowGuide = true
		resp.ShowGuideInternalDays = config.GetGamePalLogicConfig().GetShowGuideInternalDays()
	}

	log.InfoWithCtx(ctx, "HandleGamePalCardOnPublishRoom success, uid:%d, req:%+v, resp:%+v", uid, req, resp)
	return resp, nil
}

func (s *Server) checkHandleGamePalCardOnPublishRoomParams(ctx context.Context, uid uint32, req *pb.HandleGamePalCardOnPublishRoomRequest) error {
	// 获取房间信息，检查用户是否房主
	channelInfo, err := rpc.ChannelCli.GetChannelSimpleInfo(ctx, uid, req.GetCid())
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleGamePalCardOnPublishRoom GetChannelSimpleInfo err: %v", err)
		return err
	}
	//log.DebugWithCtx(ctx, "HandleGamePalCardOnPublishRoom channelInfo: %+v", channelInfo)
	if channelInfo.GetCreaterUid() != uid {
		log.InfoWithCtx(ctx, "HandleGamePalCardOnPublishRoom uid(%d) is not channel(%d) creator", uid, req.GetCid())
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	// 获取房间玩法
	channelTabResp, err := rpc.TopicClient.GetChannelPlayModel(ctx, &channel.GetChannelPlayModelReq{
		ChannelId: req.GetCid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleGamePalCardOnPublishRoom GetChannelPlayModel err: %v", err)
		return err
	}
	if channelTabResp.GetTabId() != req.GetTabId() {
		log.InfoWithCtx(ctx, "HandleGamePalCardOnPublishRoom req.tabId(%d) != channel(%d) current tabId(%d)", req.GetTabId(), req.GetCid(), channelTabResp.GetTabId())
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	// 是否为一起开黑玩法
	tabInfo := cache.GetTabInfoById(req.GetTabId())
	if tabInfo == nil {
		log.WarnWithCtx(ctx, "HandleGamePalCardOnPublishRoom tabId(%d) not found", req.GetTabId())
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	if tabInfo.GetCategoryMapping() != uint32(topicChannelPB.CategoryType_Gangup_type) {
		log.InfoWithCtx(ctx, "HandleGamePalCardOnPublishRoom tabId(%d) is not gangup tab", req.GetTabId())
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	return nil
}

// AssistantPushGamePal 客户端上报触发助手推送搭子卡片，初期有：登陆后n分钟助手推送
func (s *Server) AssistantPushGamePal(ctx context.Context, req *pb.AssistantPushGamePalReq) (*pb.AssistantPushGamePalResp, error) {
	out := &pb.AssistantPushGamePalResp{}
	log.InfoWithCtx(ctx, "AssistantPushGamePal req: %+v", req)

	_, err := rpc.GamePalClient.GamePalRecPushAssistant(ctx, &game_pal.GamePalRecPushAssistantReq{
		PushType: game_pal.GamePalRecPushAssistantReq_AssistantPushGamePalType(req.GetPushType()),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AssistantPushGamePal GamePalRecPushAssistant err: %v", err)
		return out, err
	}
	return out, nil
}
