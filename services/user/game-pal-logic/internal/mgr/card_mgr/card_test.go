package card_mgr

import (
	"context"
	"errors"
	channel_play_tab "golang.52tt.com/protocol/services/channel-play-tab"
	"reflect"
	"testing"

	game_image "golang.52tt.com/services/user/game-pal-logic/internal/mgr/game_image"

	"github.com/golang/mock/gomock"
	account_go "golang.52tt.com/clients/mocks/account-go"
	censoring_proxy "golang.52tt.com/clients/mocks/censoring-proxy"
	cybros_arbiter_v2 "golang.52tt.com/clients/mocks/cybros/arbiter/v2"
	game_pal "golang.52tt.com/clients/mocks/game-pal"
	"golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/game-pal-logic"
	account_pb "golang.52tt.com/protocol/services/account-go"
	cybros_arbiter_v2_pb "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	game_pal_pb "golang.52tt.com/protocol/services/game-pal"
	game_ugc_content_pb "golang.52tt.com/protocol/services/game-ugc-content"
	"golang.52tt.com/services/user/game-pal-logic/internal/cache"
	config "golang.52tt.com/services/user/game-pal-logic/internal/config/ttconfig/game_pal_logic"
	"golang.52tt.com/services/user/game-pal-logic/internal/rpc"
)

func TestManager_GetGamePalCardList(t *testing.T) {
	var (
		ctrl       = gomock.NewController(t)
		gamePalCli = game_pal.NewMockIClient(ctrl)

		uid     uint32 = 1
		svcInfo        = grpc.ServiceInfo{UserID: 2}
	)

	rpc.GamePalClient = gamePalCli

	gamePalCli.EXPECT().
		GetUserGamePalCardList(gomock.Any(), &game_pal_pb.GetUserGamePalCardListReq{Uid: uid, Limiting: true}).
		Return(&game_pal_pb.GetUserGamePalCardListResp{Cards: nil}, nil)

	type args struct {
		ctx          context.Context
		svcInfo      *grpc.ServiceInfo
		uid          uint32
		imageHandler *game_image.GameImageHandler
	}
	tests := []struct {
		name    string
		m       *Manager
		args    args
		want    []*pb.GamePalCard
		wantErr bool
	}{
		{
			name: "GetGamePalCardList",
			m:    &Manager{},
			args: args{ctx: context.Background(), svcInfo: &svcInfo, uid: uid, imageHandler: game_image.NewGameImageHandler(1)},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{}
			got, err := m.GetGamePalCardList(tt.args.ctx, tt.args.svcInfo, tt.args.uid, tt.args.imageHandler)
			if (err != nil) != tt.wantErr {
				t.Errorf("Manager.GetGamePalCardList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Manager.GetGamePalCardList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetGamePalCard(t *testing.T) {
	var (
		ctrl       = gomock.NewController(t)
		gamePalCli = game_pal.NewMockIClient(ctrl)

		uid     uint32 = 1
		tabId   uint32 = 1
		svcInfo        = grpc.ServiceInfo{UserID: 2}
	)

	rpc.GamePalClient = gamePalCli

	gamePalCli.EXPECT().
		GetUserGamePalCard(gomock.Any(), &game_pal_pb.GetUserGamePalCardReq{Uid: uid, TabId: tabId}).
		Return(&game_pal_pb.GetUserGamePalCardResp{Card: nil}, nil)
	type args struct {
		ctx          context.Context
		svcInfo      *grpc.ServiceInfo
		uid          uint32
		tabId        uint32
		imageHandler *game_image.GameImageHandler
	}
	tests := []struct {
		name    string
		m       *Manager
		args    args
		want    *pb.GamePalCard
		wantErr bool
	}{
		{
			name: "GetGamePalCard",
			m:    &Manager{},
			args: args{
				ctx:          context.Background(),
				svcInfo:      &svcInfo,
				uid:          uid,
				tabId:        tabId,
				imageHandler: game_image.NewGameImageHandler(1),
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{}
			got, err := m.GetGamePalCard(tt.args.ctx, tt.args.svcInfo, tt.args.uid, tt.args.tabId, tt.args.imageHandler)
			if (err != nil) != tt.wantErr {
				t.Errorf("Manager.GetGamePalCard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Manager.GetGamePalCard() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_DeleteGamePalCard(t *testing.T) {
	var (
		ctrl       = gomock.NewController(t)
		gamePalCli = game_pal.NewMockIClient(ctrl)

		card = &game_pal_pb.GamePalCard{Id: "1_1", Uid: 2}
	)

	rpc.GamePalClient = gamePalCli

	gamePalCli.EXPECT().
		GetGamePalCard(gomock.Any(), &game_pal_pb.GetGamePalCardReq{Id: card.GetId()}).
		Return(&game_pal_pb.GetGamePalCardResp{Card: card}, nil)

	gamePalCli.EXPECT().
		DeleteGamePalCard(gomock.Any(), &game_pal_pb.DeleteGamePalCardReq{Card: card}).
		Return(&game_pal_pb.DeleteGamePalCardResp{}, nil)
	type args struct {
		ctx context.Context
		uid uint32
		id  string
	}
	tests := []struct {
		name    string
		m       *Manager
		args    args
		wantErr bool
	}{
		{
			name:    "DeleteGamePalCard",
			m:       &Manager{},
			args:    args{ctx: context.Background(), uid: card.GetUid(), id: card.GetId()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{}
			if err := m.DeleteGamePalCard(tt.args.ctx, tt.args.uid, tt.args.id); (err != nil) != tt.wantErr {
				t.Errorf("Manager.DeleteGamePalCard() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_LightenGamePalCard(t *testing.T) {
	var (
		ctrl       = gomock.NewController(t)
		gamePalCli = game_pal.NewMockIClient(ctrl)

		card = &game_pal_pb.GamePalCard{
			Id:         "1_1",
			Uid:        2,
			TabId:      1,
			AuditState: game_pal_pb.GamePalCardAuditState_GamePalCardAuditStatePass,
		}
	)

	rpc.GamePalClient = gamePalCli

	cache.SetTabInfoCache(&cache.TabCache{
		GamePalConfigTabMap: map[uint32]game_ugc_content_pb.ConfigTabInfo{1: {}},
	})

	// gamePalCli.EXPECT().
	// 	GetGamePalCard(gomock.Any(), &game_pal_pb.GetGamePalCardReq{Id: card.GetId()}).
	// 	Return(&game_pal_pb.GetGamePalCardResp{Card: card}, nil)

	gamePalCli.EXPECT().
		BatchPolishGamePalCard(gomock.Any(), &game_pal_pb.BatchPolishGamePalCardReq{
			GamePalCards: []*game_pal_pb.GamePalCard{card},
			PolishType:   game_pal_pb.PolishType_LIGHT,
		}).
		Return(&game_pal_pb.BatchPolishGamePalCardResp{}, nil)

	config.SetGamePalLogicConfig(&config.GamePalLogicConfig{
		DailyLightenLimit: 10,
	})
	type args struct {
		ctx          context.Context
		card         *game_pal_pb.GamePalCard
		imageHandler *game_image.GameImageHandler
	}
	tests := []struct {
		name    string
		m       *Manager
		args    args
		wantErr bool
	}{
		{
			name:    "LightenGamePalCard",
			m:       &Manager{},
			args:    args{ctx: context.Background(), card: card, imageHandler: game_image.NewGameImageHandler(1)},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{}
			if err := m.LightenGamePalCard(tt.args.ctx, tt.args.card, tt.args.imageHandler); (err != nil) != tt.wantErr {
				t.Errorf("Manager.LightenGamePalCard() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_LightenAllGamePalCard(t *testing.T) {
	var (
		ctrl       = gomock.NewController(t)
		gamePalCli = game_pal.NewMockIClient(ctrl)

		card = &game_pal_pb.GamePalCard{
			Id:         "1_1",
			Uid:        2,
			TabId:      1,
			AuditState: game_pal_pb.GamePalCardAuditState_GamePalCardAuditStatePass,
		}
	)

	rpc.GamePalClient = gamePalCli

	cache.SetTabInfoCache(&cache.TabCache{
		GamePalConfigTabMap: map[uint32]game_ugc_content_pb.ConfigTabInfo{1: {}},
	})

	gamePalCli.EXPECT().
		GetUserGamePalCardList(gomock.Any(), &game_pal_pb.GetUserGamePalCardListReq{Uid: card.GetUid()}).
		Return(&game_pal_pb.GetUserGamePalCardListResp{Cards: []*game_pal_pb.GamePalCard{card}}, nil)

	gamePalCli.EXPECT().
		BatchPolishGamePalCard(gomock.Any(), &game_pal_pb.BatchPolishGamePalCardReq{
			GamePalCards: []*game_pal_pb.GamePalCard{card},
			PolishType:   game_pal_pb.PolishType_LIGHT,
		}).
		Return(&game_pal_pb.BatchPolishGamePalCardResp{}, nil)

	config.SetGamePalLogicConfig(&config.GamePalLogicConfig{
		DailyLightenLimit: 10,
	})
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		m       *Manager
		args    args
		wantErr bool
	}{
		{
			name:    "LightenAllGamePalCard",
			m:       &Manager{},
			args:    args{ctx: context.Background(), uid: card.GetUid()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{}
			if err := m.LightenAllGamePalCard(tt.args.ctx, tt.args.uid); (err != nil) != tt.wantErr {
				t.Errorf("Manager.LightenAllGamePalCard() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_PublishGamePalCard(t *testing.T) {
	var (
		ctrl              = gomock.NewController(t)
		accountCli        = account_go.NewMockIClient(ctrl)
		gamePalCli        = game_pal.NewMockIClient(ctrl)
		censoringCli      = cybros_arbiter_v2.NewMockCensoringClient(ctrl)
		censoringProxyCli = censoring_proxy.NewMockIClient(ctrl)
		channelPlayTabCli = channel_play_tab.NewMockChannelPlayTabClient(ctrl)

		uid   uint32 = 1
		tabId uint32 = 1

		card = &pb.GamePalCard{
			BaseCard: &pb.GamePalCardInfo{
				BaseInfo: &pb.GamePalBaseCard{
					TabId:      tabId,
					SocialDecl: "pp约玩吗",
				},
			},
		}
	)

	rpc.AccountClient = accountCli
	rpc.GamePalClient = gamePalCli
	rpc.CensoringClient = censoringProxyCli
	rpc.ChannelPlayTabClient = channelPlayTabCli

	cache.SetTabInfoCache(&cache.TabCache{
		GamePalConfigTabMap: map[uint32]game_ugc_content_pb.ConfigTabInfo{1: {}},
	})

	config.SetGamePalLogicConfig(&config.GamePalLogicConfig{
		CreatedCardLimit: 10,
	})

	channelPlayTabCli.EXPECT().
		GetActiveBanUserConfigWithCache(gomock.Any(), &channel_play_tab.GetActiveBanUserConfigWithCacheReq{
			TabId:       tabId,
			Uid:         uid,
			BanPostType: channel_play_tab.BanPostType_BanPostTypeGamePalCard,
		}).Return(&channel_play_tab.GetActiveBanUserConfigWithCacheResp{}, nil)

	accountCli.EXPECT().
		GetUserByUid(gomock.Any(), uid).
		Return(&account_pb.UserResp{}, nil)

	gamePalCli.EXPECT().
		GetUserGamePalCardList(gomock.Any(), &game_pal_pb.GetUserGamePalCardListReq{Uid: uid}).
		Return(&game_pal_pb.GetUserGamePalCardListResp{}, nil)

	gamePalCli.EXPECT().
		UpsertGamePalCard(gomock.Any(), gomock.Any()).
		Return(&game_pal_pb.UpsertGamePalCardResp{Card: nil}, nil)

	censoringProxyCli.EXPECT().Censoring().Return(censoringCli)
	censoringCli.EXPECT().
		AsyncScanMix(gomock.Any(), gomock.Any()).
		Return(&cybros_arbiter_v2_pb.CensoringResp{}, nil)

	gamePalCli.EXPECT().
		SendEditCardEvent(gomock.Any(), gomock.Any()).
		Return(&game_pal_pb.SendEditCardEventResp{}, nil)
	type args struct {
		ctx          context.Context
		svcInfo      *grpc.ServiceInfo
		card         *pb.GamePalCard
		imageHandler *game_image.GameImageHandler
	}
	tests := []struct {
		name    string
		m       *Manager
		args    args
		wantErr bool
	}{
		{
			name: "PublishGamePalCard",
			m:    &Manager{},
			args: args{
				ctx:          context.Background(),
				svcInfo:      &grpc.ServiceInfo{UserID: uid},
				card:         card,
				imageHandler: game_image.NewGameImageHandler(1),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{}
			if err := m.PublishGamePalCard(tt.args.ctx, tt.args.svcInfo, tt.args.card, tt.args.imageHandler); (err != nil) != tt.wantErr {
				t.Errorf("Manager.PublishGamePalCard() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_GetIMShowGamePalCard(t *testing.T) {
	var (
		ctrl       = gomock.NewController(t)
		gamePalCli = game_pal.NewMockIClient(ctrl)

		uid     uint32 = 1
		svcInfo        = grpc.ServiceInfo{UserID: 2}
	)

	rpc.GamePalClient = gamePalCli

	gamePalCli.EXPECT().
		GetUserGamePalCardList(gomock.Any(), &game_pal_pb.GetUserGamePalCardListReq{Uid: uid, Limiting: true}).
		Return(&game_pal_pb.GetUserGamePalCardListResp{Cards: nil}, nil)
	gamePalCli.EXPECT().
		GetUserGamePalCardList(gomock.Any(), &game_pal_pb.GetUserGamePalCardListReq{Uid: uid, Limiting: true}).
		Return(nil, errors.New("test err"))

	type args struct {
		ctx          context.Context
		svcInfo      *grpc.ServiceInfo
		imUid        uint32
		imageHandler *game_image.GameImageHandler
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.GamePalItem
		wantErr bool
	}{
		{
			name: "GetIMShowGamePalCard",
			args: args{
				ctx:          context.Background(),
				svcInfo:      &svcInfo,
				imUid:        1,
				imageHandler: game_image.NewGameImageHandler(1),
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "GetIMShowGamePalCard",
			args: args{
				ctx:          context.Background(),
				svcInfo:      &svcInfo,
				imUid:        1,
				imageHandler: game_image.NewGameImageHandler(1),
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{}
			got, err := m.GetIMShowGamePalCard(tt.args.ctx, tt.args.svcInfo, tt.args.imUid, tt.args.imageHandler)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetIMShowGamePalCard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetIMShowGamePalCard() got = %v, want %v", got, tt.want)
			}
		})
	}
}
