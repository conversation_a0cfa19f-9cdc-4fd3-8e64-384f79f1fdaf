package entrance

import (
	"context"

	"golang.org/x/sync/errgroup"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/device_id"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/marketid_helper"
	pb "golang.52tt.com/protocol/app/chat-bot-logic"
	web_im_logic "golang.52tt.com/protocol/app/web-im-logic"
	aigc_intimacy "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	rcmd_business "golang.52tt.com/protocol/services/rcmd/business_ai_partner"
	"golang.52tt.com/services/user/chat-bot-logic/internal/config/ttconfig"
)

// MultiRoleLoader 加载多角色入口
func (l *Loader) MultiRoleLoader(withGroup bool) func(ctx context.Context) ([]*pb.AIPartnerEntrance, error) {
	return func(ctx context.Context) ([]*pb.AIPartnerEntrance, error) {
		const (
			layerTag = "soulmate_multi_role_entrance"

			argChatKey   = "soulmate_multi_role_entrance_chat_key"
			argCreateKey = "soulmate_multi_role_entrance_create_key"
		)

		svcInfo := metainfo.GetServiceInfo(ctx)

		deviceID, err := device_id.ToClientDeviceId(device_id.ToDeviceHexId(svcInfo.DeviceID(), true), uint32(svcInfo.ClientType()))
		if err != nil {
			log.ErrorWithCtx(ctx, "loadMultiRoleEntrance ToClientDeviceId DeviceID(%s) err: %v", svcInfo.DeviceID(), err)
			return nil, err
		}

		testList, err := l.ab.GetUserTestListInLayerTagByDeviceId(ctx, deviceID, layerTag)
		if err != nil {
			log.ErrorWithCtx(ctx, "loadMultiRoleEntrance GetUserTestListInLayerTagByDeviceId deviceID(%s) err: %v", deviceID, err)
			return nil, err
		}

		log.InfoWithCtx(ctx, "loadMultiRoleEntrance uid(%d) testList: %+v", svcInfo.UserID(), testList)

		var (
			chatKey   = ttconfig.EntranceKeyMultiRoleChat
			createKey = ttconfig.EntranceKeyMultiRoleCreate
		)
		for _, test := range testList {
			for _, expt := range test.ExptList {
				if expt.ExptArgv[argChatKey] != "" {
					chatKey = expt.ExptArgv[argChatKey]
				}
				if expt.ExptArgv[argCreateKey] != "" {
					createKey = expt.ExptArgv[argCreateKey]
				}
			}
		}

		var (
			eg, egCtx = errgroup.WithContext(ctx)

			partners  []*pb.AIPartner
			groupsMsg []*web_im_logic.LastGroupMsg
		)
		eg.Go(func() error {
			var err error
			partners, err = l.getMultiRolePartners(egCtx, svcInfo.UserID())
			return err
		})
		if withGroup {
			eg.Go(func() error {
				var err error
				groupsMsg, err = l.getGroupsLastMsg(egCtx, svcInfo.UserID())
				return err
			})
		}
		if err := eg.Wait(); err != nil {
			return nil, err
		}

		key := createKey
		if len(partners) > 0 || len(groupsMsg) > 0 {
			key = chatKey
		}
		config, ok := ttconfig.GetChatBotLogicConfig().GetEntranceByKey(key)
		if !ok {
			log.WarnWithCtx(ctx, "loadMultiRoleEntrance GetEntranceByKey(%s) empty", key)
			return nil, nil
		}

		entrances := []*pb.AIPartnerEntrance{
			{
				Type: uint32(pb.AIPartnerEntranceType_AI_PARTNER_ENTRANCE_TYPE_MULTI_ROLE),

				Tag:      config.Tag,
				Title:    config.Title,
				Subtitle: config.Subtitle,

				JumpLink: marketid_helper.Get(config.SceneKey, svcInfo.MarketID(), uint32(svcInfo.ClientType())),

				Foreground: config.Foreground,
				Background: config.Background,

				Partners: partners,
			},
		}
		if withGroup {
			entrances = append(entrances, &pb.AIPartnerEntrance{
				Type: uint32(pb.AIPartnerEntranceType_AI_PARTNER_ENTRANCE_TYPE_GROUP),

				Tag:      config.Tag,
				Title:    config.Title,
				Subtitle: config.Subtitle,

				JumpLink: marketid_helper.Get(config.SceneKey, svcInfo.MarketID(), uint32(svcInfo.ClientType())),

				Foreground: config.Foreground,
				Background: config.Background,

				LastGroupMsgs: groupsMsg,
			})
		}

		return entrances, nil
	}
}

func (l *Loader) getMultiRolePartners(ctx context.Context, uid uint32) ([]*pb.AIPartner, error) {
	baseReq := &aigc_soulmate.GetUserAIPartnerListReq{
		Uid: uid,
		RoleTypes: []aigc_soulmate.AIRoleType{
			aigc_soulmate.AIRoleType_AIRoleTypeGame,
			aigc_soulmate.AIRoleType_AIRoleTypePartner,
		},
	}
	baseResp, err := l.soulmate.GetUserAIPartnerList(ctx, baseReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "getMultiRolePartners GetUserAIPartnerList req(%+v) err: %v", baseReq, err)
		return nil, err
	}

	basePartners := baseResp.GetPartnerList()
	if len(basePartners) == 0 {
		return nil, nil
	}

	var (
		treeHoleIdList     = make([]uint32, 0, len(basePartners))
		relationEntityList = make([]*aigc_intimacy.Entity, 0, len(basePartners))
	)
	for _, basePartner := range basePartners {
		switch basePartner.GetRole().GetType() {
		case aigc_soulmate.AIRoleType_AIRoleTypeGame:
			relationEntityList = append(relationEntityList, &aigc_intimacy.Entity{
				Id:   basePartner.GetId(),
				Type: aigc_intimacy.Entity_TYPE_PARTNER,
			})
		case aigc_soulmate.AIRoleType_AIRoleTypePartner:
			treeHoleIdList = append(treeHoleIdList, basePartner.GetId())
		}
	}

	rcmdPartnerMap := make(map[uint32]*rcmd_business.PartnerInfo)
	if len(treeHoleIdList) > 0 {
		rcmdReq := &rcmd_business.BatchGetPartnerInfoReq{
			Uid:        uid,
			PartnerIds: treeHoleIdList,
		}
		rcmdResp, err := l.rcmdBusiness.BatchGetPartnerInfo(ctx, rcmdReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "getMultiRolePartners BatchGetPartnerInfo req(%+v) err: %v", rcmdReq, err)
			return nil, err
		}
		for _, rcmdPartner := range rcmdResp.GetPartnerInfoList() {
			rcmdPartnerMap[rcmdPartner.GetId()] = rcmdPartner
		}
	}

	relationMap := make(map[uint32]*aigc_intimacy.Relationship)
	if len(relationEntityList) > 0 {
		relationReq := &aigc_intimacy.BatchGetCurRelationRequest{
			EntityList: relationEntityList,
		}
		relationResp, err := l.intimacy.BatchGetCurRelation(ctx, relationReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "getMultiRolePartners BatchGetCurRelation req(%+v) err: %v", relationReq, err)
			return nil, err
		}
		for _, relation := range relationResp.GetCurRelations() {
			relationMap[relation.GetEntity().GetId()] = relation.GetRelation()
		}
	}

	log.InfoWithCtx(ctx, "relationMap: %+v", relationMap)

	partners := make([]*pb.AIPartner, 0, len(basePartners))
	for _, basePartner := range basePartners {
		var partner *pb.AIPartner
		switch basePartner.GetRole().GetType() {
		case aigc_soulmate.AIRoleType_AIRoleTypeGame:
			partner = assembleGamePartner(basePartner, relationMap[basePartner.GetId()])
		case aigc_soulmate.AIRoleType_AIRoleTypePartner:
			partner = assembleTreeHolePartner(basePartner, rcmdPartnerMap[basePartner.GetId()])
		default:
			log.WarnWithCtx(ctx, "getMultiRolePartners invalid partner: %+v", partner)
			continue
		}

		partners = append(partners, partner)
	}

	return partners, nil
}

func assembleGamePartner(basePartner *aigc_soulmate.AIPartner, relation *aigc_intimacy.Relationship) *pb.AIPartner {
	role := basePartner.GetRole()
	partnerPb := &pb.AIPartner{
		Id: basePartner.GetId(),

		Silent: basePartner.GetSilent(),

		Name:     basePartner.GetName(),
		CallName: basePartner.GetCallName(),

		Role: &pb.AIRole{
			Id: role.GetId(),

			Uid:   role.GetUid(),
			Type:  uint32(role.GetType()),
			State: uint32(role.GetState()),

			Sex:    role.GetSex(),
			Name:   role.GetName(),
			Image:  role.GetImage(),
			Avatar: role.GetAvatar(),
		},
	}
	if relation != nil {
		partnerPb.Hint = relation.GetName()
		partnerPb.ShowHint = true
	}

	return partnerPb
}
