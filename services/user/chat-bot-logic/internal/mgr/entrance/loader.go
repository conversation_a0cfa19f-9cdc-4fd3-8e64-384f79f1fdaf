package entrance

import (
	"context"
	"time"

	"github.com/sourcegraph/conc/pool"
	account "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/abtest"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/app/chat-bot-logic"
	aigc_group "golang.52tt.com/protocol/services/aigc/aigc-group"
	aigc_intimacy "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	aigc_soulmate_middle "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	game_red_dot "golang.52tt.com/protocol/services/game-red-dot"
	rcmd_business "golang.52tt.com/protocol/services/rcmd/business_ai_partner"
	rcmd_partner "golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner"
)

type LoadFn func(ctx context.Context) ([]*pb.AIPartnerEntrance, error)

type Loader struct {
	ab      abtest.IABTestClient
	redDot  game_red_dot.GameRedDotClient
	account *account.Client

	group          aigc_group.AigcGroupClient
	soulmate       aigc_soulmate.AigcSoulmateClient
	intimacy       aigc_intimacy.AigcIntimacyClient
	soulmateMiddle aigc_soulmate_middle.AigcSoulmateMiddleClient

	rcmdPartner  rcmd_partner.RCMDAIPartnerClient
	rcmdBusiness rcmd_business.BusinessAIPartnerClient
}

func NewLoader(
	ab abtest.IABTestClient,
	redDot game_red_dot.GameRedDotClient,
	account *account.Client,

	group aigc_group.AigcGroupClient,
	soulmate aigc_soulmate.AigcSoulmateClient,
	intimacy aigc_intimacy.AigcIntimacyClient,
	soulmateMiddle aigc_soulmate_middle.AigcSoulmateMiddleClient,

	rcmdPartner rcmd_partner.RCMDAIPartnerClient,
	rcmdBusiness rcmd_business.BusinessAIPartnerClient,
) *Loader {
	return &Loader{
		ab:      ab,
		redDot:  redDot,
		account: account,

		group:          group,
		soulmate:       soulmate,
		intimacy:       intimacy,
		soulmateMiddle: soulmateMiddle,

		rcmdPartner:  rcmdPartner,
		rcmdBusiness: rcmdBusiness,
	}
}

func (l *Loader) Load(ctx context.Context, loadFn ...LoadFn) ([]*pb.AIPartnerEntrance, error) {
	if len(loadFn) == 0 {
		log.WarnWithCtx(ctx, "Load miss loadFn, skip")
		return nil, nil
	}

	loadCtx, loadCancel := context.WithTimeout(ctx, 2*time.Second)
	defer loadCancel()

	pool := pool.NewWithResults[[]*pb.AIPartnerEntrance]().
		WithContext(loadCtx).
		WithMaxGoroutines(3)

	for _, fn := range loadFn {
		pool.Go(fn)
	}

	results, err := pool.Wait()
	if err != nil {
		return nil, err
	}

	var entrances []*pb.AIPartnerEntrance
	for _, result := range results {
		entrances = append(entrances, result...)
	}

	return entrances, nil
}
