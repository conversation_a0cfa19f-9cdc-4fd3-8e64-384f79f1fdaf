package timer

import (
	"context"
	channelolstatgo "golang.52tt.com/clients/channelol-stat-go"
	tmpchannelalloc "golang.52tt.com/clients/tmp-channel-alloc"
	"golang.52tt.com/pkg/log"
	allocPB "golang.52tt.com/protocol/services/tmp-channel-alloc"
	"golang.52tt.com/services/user/competition-entrance/db"
	"strconv"
	"time"
)

const (
	maxIntervalTime = 10 * time.Hour // 房间管理那边临时房最大存在时间10小时
)

func NewRoomReleaseHandler(db *db.Mongo<PERSON>ao, channelStatGoCli *channelolstatgo.Client, channelAllocCli *tmpchannelalloc.Client) *RoomReleaseHandler {
	return &RoomReleaseHandler{
		mongoDao:         db,
		channelStatGoCli: channelStatGoCli,
		channelAllocCli:  channelAllocCli,
	}
}

type RoomReleaseHandler struct {
	mongoDao         db.IMongoDao
	channelStatGoCli channelolstatgo.IClient
	channelAllocCli  tmpchannelalloc.IClient
}

func (t *RoomReleaseHandler) Handle() {
	now := time.Now()
	defer func() {
		log.Infof("RoomReleaseHandler handle cost:%d ms", time.Since(now).Milliseconds())
	}()

	queryLimit := 100
	maxExpireTimeStamp := now.Add(-maxIntervalTime).Unix()
	for i := 0; i < 10; i++ {
		tmpChannelCfg, err := t.mongoDao.GetExpireGameTmpChannel(context.Background(), now.Unix(), maxExpireTimeStamp, int64(queryLimit*i), int64(queryLimit))
		if err != nil {
			log.Errorf("t.mongoDao.GetExpireGameTmpChannel fail, err:%v", err)
			break
		}
		t.dealRelease(tmpChannelCfg)

		if len(tmpChannelCfg) < queryLimit {
			break
		}
	}
}

func (t *RoomReleaseHandler) dealRelease(tmpChannelCfg []*db.GameTmpChannelCfg) {
	channelIds := make([]uint32, 0, len(tmpChannelCfg))
	for _, cfg := range tmpChannelCfg {
		channelId, _ := strconv.Atoi(cfg.ChannelId)
		if channelId == 0 {
			continue
		}
		channelIds = append(channelIds, uint32(channelId))
	}
	if len(channelIds) == 0 {
		log.Debugf("RoomReleaseHandler dealRelease channelIds is empty")
		return
	}
	log.Debugf("RoomReleaseHandler deal channelIds: %v", channelIds)

	// 查询房间人数
	sizes, err := t.channelStatGoCli.BatchGetChannelMemberSize(context.Background(), 0, channelIds)
	if err != nil {
		log.Errorf("RoomReleaseHandler t.channelStatGoCli.BatchGetChannelMemberSize fail, err:%v, channelIds:%v", err, channelIds)
		return
	}

	for cid, size := range sizes {
		if size > 0 {
			continue
		}
		// 释放房间，
		_, err = t.channelAllocCli.ReleaseV2(context.Background(), &allocPB.ReleaseReq{
			Operator:    "competition-entrance",
			ChannelId:   cid,
			ChannelType: 6,
		})
		if err != nil {
			log.Errorf("RoomReleaseHandler t.channelAllocCli.Release fail, err:%v, cid:%d", err, cid)
			continue
		}
		// 更新数据库状态
		mongoErr := t.mongoDao.UpdateCfgStatus(context.Background(), cid)
		if mongoErr != nil {
			log.Errorf("RoomReleaseHandler UpdateCfgStatus fail, err:%v, cid:%d", mongoErr, cid)
		}
	}

	log.Infof("RoomReleaseHandler BatchGetChannelMemberSize:%v", sizes)

}
