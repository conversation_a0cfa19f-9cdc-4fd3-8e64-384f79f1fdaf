package event

import (
	"context"
	"errors"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/event"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	game_card "golang.52tt.com/pkg/game-card"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	kfkPB "golang.52tt.com/protocol/services/minToolkit/kafka/pb/tt_auth_ev"
	"golang.52tt.com/protocol/services/rcmd/rcmd_statistics_query"
	"golang.52tt.com/services/game-card-middle/internal/cache"
	"golang.52tt.com/services/game-card-middle/internal/rpc/client"
)

type LoginSub struct {
	Sub   subscriber.Subscriber
	topic string
}

const loginSubscriberName = "login_kafka"

func NewLoginSubscriber(ctx context.Context, factory *event.Factory, eventlinkConf *event.Options) (*LoginSub, error) {
	/*
	   if loginKafkaConfig == nil {
	       return nil, errors.New("no login kafka config")
	   }

	   newSubscriber := &LoginSub{
	       topic: loginKafkaConfig.Topics,
	   }
	   //subscriber := kafka.NewSubscriber("user_abtest_dye")
	   kafkaConf := kafka.DefaultConfig()
	   kafkaConf.ClientID = loginKafkaConfig.ClientID
	   kafkaConf.Consumer.Offsets.Initial = kafka.OffsetNewest
	   kafkaConf.Consumer.Return.Errors = true
	   kafkaSub, err := kafka.NewSubscriber(loginKafkaConfig.BrokerList(), kafkaConf)
	   if err != nil {
	       log.ErrorWithCtx(ctx, "NewSubscriber login kafka error: %v", err)
	   }
	   //if err := subscriber.Subscribe(cfg.LoginKafkaConfig, newSubscriber.loginEvent, false); err != nil {
	   err = kafkaSub.SubscribeContext(loginKafkaConfig.GroupID, loginKafkaConfig.TopicList(), subscriber.ProcessorContextFunc(newSubscriber.loginEvent))
	   if err != nil {
	       log.ErrorWithCtx(ctx, "Subscribe channel kafka error: %v", err)
	       return nil, err
	   } else {
	       log.InfoWithCtx(ctx, "Subscribe channel kafka suc:%v", loginKafkaConfig)
	   }
	*/
	if eventlinkConf.Subscriber[loginSubscriberName] == nil {
		return nil, errors.New("no login kafka config")
	}
	newSubscriber := &LoginSub{
		topic: eventlinkConf.Subscriber[loginSubscriberName].Topics[0],
	}

	// step2: 创建消费者
	kafkaSub, err := factory.NewSubscriber(loginSubscriberName, nil, subscriber.ProcessorContextFunc(newSubscriber.loginEvent))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewLoginSubscriber factory.NewSubscriber %s err(%v)", loginSubscriberName, err)
		return nil, err
	}
	newSubscriber.Sub = kafkaSub
	log.InfoWithCtx(ctx, "NewLoginSubscriber factory.NewSubscriber %s success, LoginSub: %+v", loginSubscriberName, newSubscriber)

	return newSubscriber, nil
}

func (m *LoginSub) loginEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (err error, retry bool) {
	if m.topic == "" {
		log.ErrorWithCtx(ctx, "LoginSub loginEvent m.topic is empty")
	}
	switch msg.Topic {
	case m.topic:
		return m.handlerLoginEvent(ctx, msg)
	default:
		return nil, false
	}
}

func (m *LoginSub) handlerLoginEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	eventMsg := kfkPB.TTAuthEvent{}
	err := proto.Unmarshal(msg.Value, &eventMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerLoginEvent fail to proto.Unmarshal, err:%v", err)
		return err, false
	}
	log.DebugWithCtx(ctx, "handlerLoginEvent topic(%s) partition(%d) offset(%d) eventMsg:%s", msg.Topic, msg.Partition, msg.Offset, eventMsg.String())

	if eventMsg.GetEventType() != uint32(kfkPB.ETT_AUTH_EVENT_TYPE_ENUM_TTAUTH_LOGIN) {
		return nil, false
	}

	// 极速PC的登录事件不处理
	if protocol.IsFastPcClientType(eventMsg.GetClientType()) {
		log.DebugWithCtx(ctx, "handlerLoginEvent ClientTypePcLFG, eventMsg:%+v", eventMsg)
		return nil, false
	}

	// 判断条件，是否处理游戏卡
	statRsp, err := client.RcmdStatisticsQueryClient.GetUserGameCardStatData(ctx, &rcmd_statistics_query.GetUserGameCardStatDataReq{
		Uid: eventMsg.GetUid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerLoginEvent GetUserGameCardStatData fail, err:%v, eventMsg:%+v", err, eventMsg)
		return err, false
	}
	log.InfoWithCtx(ctx, "handlerLoginEvent GetUserGameCardStatData info, eventMsg:%+v, rsp:%+v", eventMsg, statRsp)

	// 删除用户所有游戏卡
	if statRsp.GetIsNotEnterGameArea() {
		log.InfoWithCtx(ctx, "handlerLoginEvent del game card eventMsg:%+v, rsp:%+v", eventMsg, statRsp)

		err = client.GameCardClient.DelUserAllGameCard(ctx, eventMsg.GetUid(), eventMsg.GetAppId(), eventMsg.GetMarketId())
		if err != nil {
			log.ErrorWithCtx(ctx, "handlerLoginEvent DelUserAllGameCard fail, err:%v", err, eventMsg, statRsp)
		}
		return nil, false
	}

	// 添加游戏卡
	if len(statRsp.GetEnterChannelTabList()) > 0 || len(statRsp.GetEnterGameAreaTabList()) > 0 {
		log.InfoWithCtx(ctx, "handlerLoginEvent add game card eventMsg:%+v, rsp:%+v", eventMsg, statRsp)

		tabIdMap := make(map[uint32]struct{}, len(statRsp.GetEnterChannelTabList())+len(statRsp.GetEnterGameAreaTabList()))
		tmpTabIds := append(statRsp.GetEnterChannelTabList(), statRsp.GetEnterGameAreaTabList()...)
		for _, tabId := range tmpTabIds {
			tabIdMap[tabId] = struct{}{}
		}

		gameCardIds := make([]uint32, 0, len(tabIdMap))
		uGameIdMap := make(map[uint32]uint32, len(tabIdMap))
		for tabId := range tabIdMap {
			tabInfo := cache.GetTabInfoById(tabId)
			if tabInfo == nil {
				log.InfoWithCtx(ctx, "tabInfo is nil, tabId:%d, eventMsg:%+v, rsp:%+v", tabId, eventMsg, statRsp)
				continue
			}

			gameCardId := cache.GameCardConfMapCache(tabInfo.GetGameInfo().GetGameCardId()).GetGameCardId()
			if gameCardId == 0 || gameCardId == cache.GetGameCategoryGameCardConf().GetGameCardId() {
				gameCardId = game_card.ComposeGameCardId(cache.GetGameCategoryGameCardConf().GetGameCardId(), tabId)
			}
			if gameCardId == 0 {
				log.InfoWithCtx(ctx, "handlerLoginEvent gameCardConf is nil, tabId:%d, eventMsg:%+v, rsp:%+v", tabId, eventMsg, statRsp)
				continue
			}
			gameCardIds = append(gameCardIds, gameCardId)
			uGameIdMap[gameCardId] = tabInfo.GetGameInfo().GetUGameId()
		}
		err = client.GameCardClient.CreateGameCardByGameCardId(ctx, eventMsg.GetUid(), gameCardIds, eventMsg.GetAppId(), eventMsg.GetMarketId(), uGameIdMap)
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateGameCardByTabIds fail, err:%v, eventMsg:%+v, rsp:%+v", err, eventMsg, statRsp)
		}
		log.InfoWithCtx(ctx, "handlerLoginEvent add game card info, eventMsg:%+v, rsp:%+v, gameCardIds:%+v", eventMsg, statRsp, gameCardIds)
	}

	return nil, false
}
