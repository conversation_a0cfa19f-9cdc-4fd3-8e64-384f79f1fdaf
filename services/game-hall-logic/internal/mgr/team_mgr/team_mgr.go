package team_mgr

import (
	"context"
	"fmt"
	bizLabel "gitlab.ttyuyin.com/bizFund/bizFund/pkg/push-notification"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	accountGo "golang.52tt.com/clients/account-go"
	"golang.52tt.com/clients/channel"
	channelscheme "golang.52tt.com/clients/channel-scheme"
	"golang.52tt.com/clients/channelol"
	channelol_stat_go "golang.52tt.com/clients/channelol-stat-go"
	game_card_cli "golang.52tt.com/clients/game-card"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channelPb "golang.52tt.com/protocol/app/channel"
	game_hall_logic "golang.52tt.com/protocol/app/game-hall-logic"
	game_red_dot_logic "golang.52tt.com/protocol/app/game-red-dot-logic"
	gapush "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/common/status"
	channel_base_api "golang.52tt.com/protocol/services/channel-base-api"
	game_card "golang.52tt.com/protocol/services/game-card"
	game_fre_server "golang.52tt.com/protocol/services/game-fre-server"
	game_hall "golang.52tt.com/protocol/services/game-hall"
	game_red_dot "golang.52tt.com/protocol/services/game-red-dot"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/game-hall-logic/internal/cache"
	config "golang.52tt.com/services/game-hall-logic/internal/config/ttconfig/game_hall_logic"
	"golang.52tt.com/services/game-hall-logic/internal/mgr/notify"
	"strings"
	"time"
)

const (
	maxGameCardOptNum      = 3
	maxGameCardOptValueNum = 3
	timeFormat             = "********"

	genTokenScene        = "GameHall"
	tokenEffectiveSecond = 10
)

type TeamMgr struct {
	gameHallClient      game_hall.GameHallClient
	gameFreClient       game_fre_server.GameFreServerClient
	channelOlStatClient *channelol_stat_go.Client
	channelOl           channelol.IClient
	channelCli          channel.IClient
	channelBaseApiCli   channel_base_api.ChannelBaseApiClient
	accountCli          *accountGo.Client
	channelSchemeClient *channelscheme.Client
	msgPusher           *notify.MsgPusher
	redDotClient        game_red_dot.GameRedDotClient
	gameCardClient      game_card_cli.IClient

	localCache *cache.LocalCache
}

func NewTeamMgr(gameHallClient game_hall.GameHallClient, gameFreClient game_fre_server.GameFreServerClient,
	channelOlStatClient *channelol_stat_go.Client, channelOl channelol.IClient, channelCli channel.IClient,
	channelBaseApiCli channel_base_api.ChannelBaseApiClient, accountCli *accountGo.Client, channelSchemeClient *channelscheme.Client,
	msgPusher *notify.MsgPusher, redDotClient game_red_dot.GameRedDotClient, gameCardClient game_card_cli.IClient, localCache *cache.LocalCache) *TeamMgr {

	return &TeamMgr{
		gameHallClient:      gameHallClient,
		gameFreClient:       gameFreClient,
		channelOlStatClient: channelOlStatClient,
		channelOl:           channelOl,
		channelCli:          channelCli,
		channelBaseApiCli:   channelBaseApiCli,
		accountCli:          accountCli,
		channelSchemeClient: channelSchemeClient,
		msgPusher:           msgPusher,
		redDotClient:        redDotClient,
		localCache:          localCache,
		gameCardClient:      gameCardClient,
	}
}

func (m *TeamMgr) GetGameHallTeamList(ctx context.Context, tabId uint32) ([]*game_hall.UserJoinTeamMemInfo, error) {
	resp, err := m.gameHallClient.GetUserJoinTeamRecord(ctx, &game_hall.GetUserJoinTeamRecordReq{
		TabId: tabId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameHallTeamList GetUserJoinTeamRecord err:%v", err)
		return nil, err
	}
	return resp.GetMemInfo(), nil
}

func (m *TeamMgr) GetTeamAndInviteMsg(ctx context.Context, tabId uint32, msgId uint64, limit uint32) ([]uint64, error) {
	resp, err := m.gameHallClient.GetTeamAndInviteMsg(ctx, &game_hall.GetTeamAndInviteMsgReq{
		TabId: tabId,
		MsgId: msgId,
		Limit: int64(limit),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTeamAndInviteMsgId GetTeamAndInviteMsg err:%v, tabId:%d, msgId:%d", err, tabId, msgId)
		return nil, err
	}
	return resp.GetMsgIds(), nil
}

func (m *TeamMgr) CheckSendTeamMsgFre(ctx context.Context, uid uint32) (bool, error) {
	suffix := time.Now().Format(timeFormat)
	defaultErr := protocol.NewExactServerError(nil, status.ErrGameHallNoSendTeamMsg)
	resp, err := m.gameFreClient.GetFreCountBySource(ctx, &game_fre_server.GetFreCountBySourceReq{
		FreCountSource: uint32(game_fre_server.FreCountSource_FRE_COUNT_SOURCE_GAME_HALL_SEND_TEAM_MSG),
		Suffix:         suffix,
		UserId:         uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFreCountBySource fail, err:%v", err)
		return false, defaultErr
	}
	configNum := config.GetGameHallLogicConfig().GetMaxSendTeamMsgNum()
	if configNum == 0 {
		return true, defaultErr
	}
	if resp.GetCount() >= configNum {
		return true, nil
	}
	return false, nil
}

func (m *TeamMgr) setSendTeamMsgFre(ctx context.Context, uid uint32) error {
	suffix := time.Now().Format(timeFormat)
	_, err := m.gameFreClient.IncFreCountBySource(ctx, &game_fre_server.IncFreCountBySourceReq{
		FreCountSource: uint32(game_fre_server.FreCountSource_FRE_COUNT_SOURCE_GAME_HALL_SEND_TEAM_MSG),
		Suffix:         suffix,
		ExpireTime:     uint64(time.Hour * 24),
		UserId:         uid,
	})
	if err != nil {
		return err
	}
	return nil
}

func (m *TeamMgr) CheckSendInviteRoomMsgFre(ctx context.Context, uid uint32) (bool, error) {
	suffix := time.Now().Format(timeFormat)
	resp, err := m.gameFreClient.GetFreCountBySource(ctx, &game_fre_server.GetFreCountBySourceReq{
		FreCountSource: uint32(game_fre_server.FreCountSource_FRE_COUNT_SOURCE_GAME_HALL_SEND_CHANNEL_INVITE_ROOM),
		Suffix:         suffix,
		UserId:         uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFreCountBySource fail, err:%v", err)
		return false, err
	}
	if resp.GetCount() >= config.GetGameHallLogicConfig().GetMaxSendInviteRoomMsgNum() {
		return true, nil
	}
	return false, nil
}

func (m *TeamMgr) setSendInviteRoomMsgFre(ctx context.Context, uid uint32) error {
	suffix := time.Now().Format(timeFormat)
	_, err := m.gameFreClient.IncFreCountBySource(ctx, &game_fre_server.IncFreCountBySourceReq{
		FreCountSource: uint32(game_fre_server.FreCountSource_FRE_COUNT_SOURCE_GAME_HALL_SEND_CHANNEL_INVITE_ROOM),
		Suffix:         suffix,
		ExpireTime:     uint64(time.Hour * 24),
		UserId:         uid,
	})
	if err != nil {
		return err
	}
	return nil
}

func (m *TeamMgr) CheckHallEnterRoom(ctx context.Context, uid, cid uint32) (string, error) {
	// 判断房间是否锁房
	rsp, err := m.channelCli.GetChannelSimpleInfo(ctx, uid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelSimpleInfo fail, uid:%d, cid:%d, err:%s", uid, cid, err.Error())
		return "", err
	}
	if rsp.GetEnterControlType() == uint32(channelPb.EnterControlType_EnterControlType_PASSWD) {
		return "", protocol.NewExactServerError(nil, status.ErrGameHallEnterRoomLockLimit)
	}

	count, err := m.channelOl.GetChannelMemberSize(ctx, uid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMemberSize fail, err:%v, uid:%d, cid:%d", err, uid, cid)
		return "", err
	}
	if count > config.GetGameHallLogicConfig().GetEnterRoomMaxUserCount() {
		log.InfoWithCtx(ctx, "CheckHallEnterRoom reach max user count, uid:%d, cid:%d, count:%d", uid, cid, count)
		return "", protocol.NewExactServerError(nil, status.ErrGameHallEnterRoomLimit)
	}

	// 获取token
	resp, tokenErr := m.channelBaseApiCli.GenChannelEnterToken(ctx, &channel_base_api.GenChannelEnterTokenReq{
		Scene:           genTokenScene,
		Uid:             uid,
		Cid:             cid,
		EffectiveSecond: tokenEffectiveSecond, // token有效期
	})
	if tokenErr != nil {
		log.ErrorWithCtx(ctx, "GenChannelEnterToken fail, err:%v, uid:%d, cid:%d", tokenErr, uid, cid)
		return "", tokenErr
	}

	return resp.GetToken(), nil
}

func (m *TeamMgr) CheckSendInviteRoomCond(ctx context.Context, uid, tabId uint32) (uint32, error) {
	// 查询的是当前用户所在房间，不是用户自己的房间
	cid, err := m.channelOl.GetUserChannelId(ctx, uid, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenInviteRoomMsg GetUserChannelId fail, err:%v, senUid:%d", err, uid)
		return cid, err
	}
	if cid == 0 {
		log.InfoWithCtx(ctx, "CheckSendInviteRoomCond user not in channel filter")
		return cid, protocol.NewExactServerError(nil, status.ErrGameHallSendMsgNotInChannel)
	}

	channelInfo, err := m.channelCli.GetChannelSimpleInfo(ctx, uid, cid)
	if nil != err {
		log.ErrorWithCtx(ctx, "getUserRoomInfo GetChannelSimpleInfo err:%v", err)
		return cid, err
	}
	if channelInfo.GetCreaterUid() != uid {
		log.InfoWithCtx(ctx, "CheckSendInviteRoomCond user not in own channel filter, uid:%d, channelOwner:%d, cid:%d",
			uid, channelInfo.GetCreaterUid(), cid)
		return cid, protocol.NewExactServerError(nil, status.ErrGameHallSendMsgNotInOwnChannel)
	}

	// 获取房间当前玩法
	schemeInfo, err := m.channelSchemeClient.GetCurChannelSchemeInfo(ctx, cid, uint32(channelPb.ChannelType_USER_CHANNEL_TYPE))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommonData GetCurChannelSchemeInfo(%v) err(%v)", cid, err)
		return cid, err
	}

	if schemeInfo.GetSchemeInfo().GetSchemeId() != tabId {
		log.InfoWithCtx(ctx, "CheckSendInviteRoomCond user channel tab not match filter, uid:%d, channelTabId:%d, reqTabId:%d, cid:%d",
			uid, schemeInfo.GetSchemeInfo().GetSchemeId(), tabId, cid)
		tabInfo := m.localCache.GetTabInfoById(tabId)
		msg := fmt.Sprintf("需要您处于%s房才可以发送哦", tabInfo.GetName())
		return cid, protocol.NewExactServerError(nil, status.ErrGameHallSendMsgNotMatchTab, msg)
	}

	count, err := m.channelOl.GetChannelMemberSize(ctx, uid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelMemberSize fail, err:%v, uid:%d, cid:%d", err, uid, cid)
		return cid, err
	}
	if count > config.GetGameHallLogicConfig().GetEnterRoomMaxUserCount() {
		log.InfoWithCtx(ctx, "CheckSendInviteRoomCond reach max user count filter, uid:%d, cid:%d, count:%d", uid, cid, count)
		return cid, protocol.NewExactServerError(nil, status.ErrGameHallSendMsgMuchPeople)
	}
	return cid, nil
}

// JoinTeamStatAndPush 组队报名推送
func (m *TeamMgr) JoinTeamStatAndPush(ctx context.Context, userInfo *accountGo.User, gameImMsg *game_hall_logic.GameImMsg) error {
	// 记录红点
	_, err := m.redDotClient.AddRedDot(ctx, &game_red_dot.AddRedDotReq{
		BizType: uint32(game_red_dot_logic.RedDotBizType_RED_DOT_BIZ_TYPE_GAME_HALL_JOIN_TEAM),
		BizKey:  fmt.Sprintf("%d_%d", gameImMsg.GetSenderInfo().GetUid(), gameImMsg.GetTabId()),
		PushExtraParam: &game_red_dot.PushExtraParam{
			Uid:   gameImMsg.GetSenderInfo().GetUid(),
			TabId: gameImMsg.GetTabId(),
		},
	})
	if err != nil {
		// 红点失败不影响业务
		log.ErrorWithCtx(ctx, "AddRedDot fail, err:%v", err)
	}

	notPushOffline := false
	// 判断用户是否关闭了推送开关
	switchResp, err := m.gameHallClient.GetGameHallNotifyStatus(ctx, &game_hall.GetGameHallNotifyStatusReq{
		Uid:   gameImMsg.GetSenderInfo().GetUid(),
		TabId: gameImMsg.GetTabId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameHallNotifyStatus fail, err:%v", err)
		return err
	}
	if !switchResp.GetIsOpen() {
		log.InfoWithCtx(ctx, "user close notify switch, gameImMsg:%s", gameImMsg.String())
		notPushOffline = true
	}

	// 发送间隔
	if !notPushOffline {
		resp, err := m.gameFreClient.GetFreCountBySource(ctx, &game_fre_server.GetFreCountBySourceReq{
			FreCountSource: uint32(game_fre_server.FreCountSource_FRE_COUNT_SOURCE_GAME_HALL_JOIN_TEAM_PUSH_INTERVAL),
			UserId:         gameImMsg.GetSenderInfo().GetUid(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFreCountBySource fail, err:%v", err)
			return err
		}
		if resp.GetCount() >= 1 {
			log.InfoWithCtx(ctx, "reach send interval filter, gameImMsg:%s", gameImMsg.String())
			notPushOffline = true
		}
	}

	if !notPushOffline {
		// 获取推送次数
		resp, err := m.gameFreClient.GetFreCountBySource(ctx, &game_fre_server.GetFreCountBySourceReq{
			FreCountSource: uint32(game_fre_server.FreCountSource_FRE_COUNT_SOURCE_GAME_HALL_JOIN_TEAM_PUSH_DAY_TOTAL),
			Suffix:         time.Now().Format(timeFormat),
			UserId:         gameImMsg.GetSenderInfo().GetUid(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFreCountBySource fail, err:%v", err)
		}
		if resp.GetCount() >= config.GetGameHallLogicConfig().GetMaxSendTeamPushNum() {
			log.InfoWithCtx(ctx, "reach cur day send limit filter, gameImMsg:%s", gameImMsg.String())
			notPushOffline = true
		}
	}

	// 推送消息
	gameCardInfo, err := m.gameCardClient.GetGameCard(ctx, userInfo.GetUid())
	if err != nil {
		return err
	}
	tabInfo := m.localCache.GetTabInfoById(gameImMsg.GetTabId())
	if tabInfo == nil {
		return nil
	}
	gameCardConfInfo := m.localCache.GetGameCardConfById(tabInfo.GetGameInfo().GetGameCardId())

	pushMsg := &game_hall_logic.JoinGameHallTeamNotify{
		MsgId:        gameImMsg.GetMsgId(),
		RecMsgSwitch: switchResp.GetIsOpen(),
		Title:        fmt.Sprintf("Ta报名了你的%s车队！", tabInfo.GetName()),
		SubTitle:     fmt.Sprintf("%s想和你一起开黑", userInfo.GetNickname()),
		MemInfo: &game_hall_logic.GameHallTeamMemInfo{
			BaseInfo: &game_hall_logic.GameImUserInfo{
				Uid:          userInfo.GetUid(),
				Nickname:     userInfo.GetNickname(),
				GameCardInfo: GenGameCardInfo(tabInfo, gameCardInfo, gameCardConfInfo),
				UserStatus:   uint32(game_hall_logic.GameImUserStatus_GAME_IM_USER_STATUS_ONLINE),
				Account:      userInfo.GetUsername(),
				Sex:          uint32(userInfo.GetSex()),
			},
			JoinTime: uint64(time.Now().UnixMilli()),
		},
		TabId: gameImMsg.GetTabId(),
	}
	content, err := proto.Marshal(pushMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "proto.Marshal failed, err:%v, pushMsg:%s", err, pushMsg.String())
		return err
	}
	payload, err := proto.Marshal(&gapush.PushMessage{
		Cmd:     uint32(gapush.PushMessage_GAME_HALL_JOIN_TEAM_PUSH),
		Content: content,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "proto.Marshal failed, err:%v, pushMsg:%s", err, pushMsg.String())
		return err
	}

	// 在线推送
	log.DebugWithCtx(ctx, "OnlinePush, uid:%d, pushMsg:%s", gameImMsg.GetSenderInfo().GetUid(), pushMsg.String())
	_ = m.msgPusher.OnlinePush(ctx, gameImMsg.GetSenderInfo().GetUid(), bizLabel.LabelGameHallJoinTeamPush, payload)

	// 离线推送
	if !notPushOffline {
		offPushTitle := fmt.Sprintf("有人报名了你的%s车队！", tabInfo.GetName())
		offPushContent := fmt.Sprintf("%s报名了您的开黑组队", userInfo.GetNickname())
		_ = m.msgPusher.OfflinePush(ctx, gameImMsg.GetSenderInfo().GetUid(), gameImMsg.GetTabId(), offPushTitle, offPushContent)

		// 记录离线推送次数
		_, err = m.gameFreClient.IncFreCountBySource(ctx, &game_fre_server.IncFreCountBySourceReq{
			FreCountSource: uint32(game_fre_server.FreCountSource_FRE_COUNT_SOURCE_GAME_HALL_JOIN_TEAM_PUSH_DAY_TOTAL),
			Suffix:         time.Now().Format(timeFormat),
			ExpireTime:     uint64(time.Hour * 24),
			UserId:         gameImMsg.GetSenderInfo().GetUid(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "IncFreCountBySource fail, err:%v", err)
		}
		_, err = m.gameFreClient.IncFreCountBySource(ctx, &game_fre_server.IncFreCountBySourceReq{
			FreCountSource: uint32(game_fre_server.FreCountSource_FRE_COUNT_SOURCE_GAME_HALL_JOIN_TEAM_PUSH_INTERVAL),
			ExpireTime:     uint64(time.Minute * time.Duration(config.GetGameHallLogicConfig().GetMaxSendTeamPushInterval())),
			UserId:         gameImMsg.GetSenderInfo().GetUid(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "IncFreCountBySource fail, err:%v", err)
		}
	}

	return nil
}

func (m *TeamMgr) IncTeamAndInviteRoomSendMsgFre(ctx context.Context, userInfo *accountGo.User, gameImMsg *game_hall_logic.GameImMsg) error {
	switch gameImMsg.GetMsgType() {
	case uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_FORM_TEAM):
		// 记录发送次数
		err := m.setSendTeamMsgFre(ctx, gameImMsg.GetSenderInfo().GetUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "SetSendTeamMsgFre fail, err:%v", err)
		}

	case uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_INVITE_ROOM):
		// 记录发送次数
		err := m.setSendInviteRoomMsgFre(ctx, gameImMsg.GetSenderInfo().GetUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "SetSendInviteRoomMsgFre fail, err:%v", err)
		}
	default:
		return nil
	}

	return nil
}

func GenGameCardInfo(tabInfo *tabPB.Tab, gameCardInfo []*game_card.GameCardInfo, gameCardConfInfo *game_card.GameCardConfInfo) *game_hall_logic.GameImUserInfo_GameCardInfo {
	res := &game_hall_logic.GameImUserInfo_GameCardInfo{}
	if len(gameCardInfo) == 0 || tabInfo == nil || gameCardConfInfo == nil {
		return res
	}

	for _, v := range gameCardInfo {
		if tabInfo.GetGameInfo().GetGameCardId() == v.GetGameCardId() {
			res.GameCardText = genGameCardText(v, gameCardConfInfo)
			res.GameCardId = v.GetGameCardId()
			return res
		}
	}
	return res
}

// 展示用户填写了游戏卡中内前2个字段的选项，每个字段最多展示3个选项用“/”隔开
func genGameCardText(gameCardInfo *game_card.GameCardInfo, gameCardConfInfo *game_card.GameCardConfInfo) string {
	texts := make([]string, 0, maxGameCardOptNum)

	var showNum int
	for _, conf := range gameCardConfInfo.GetOptConfList() {
		if showNum >= maxGameCardOptNum {
			break
		}
		if conf.GetOptType() != game_card.GameCardOptType_GAME_CARD_OPT_COMMON && conf.GetOptType() != game_card.GameCardOptType_GAME_CARD_OPT_LEVEL {
			continue
		}
		optList := getGameCardOptListById(gameCardInfo.GetOptList(), conf)
		if len(optList) == 0 {
			continue
		}
		var text string
		if len(optList) > maxGameCardOptValueNum {
			optList = optList[:maxGameCardOptValueNum]
		}
		text = strings.Join(optList, "/")
		texts = append(texts, text)
		showNum++
	}
	return strings.Join(texts, " ")
}

func getGameCardOptListById(userGameCard []*game_card.GameCardOpt, cardConf *game_card.GameCardOptConf) []string {
	for _, userOpt := range userGameCard {
		if userOpt.GetOptId() != cardConf.GetOptId() {
			continue
		}
		res := make([]string, 0, len(userOpt.GetValueList()))
		for _, v := range userOpt.GetValueList() {
			if cardConf.GetOptType() == game_card.GameCardOptType_GAME_CARD_OPT_COMMON {
				if isContainString(cardConf.GetOptConfValueList(), v) {
					res = append(res, v)
				}
			} else {
				if isContainLevel(cardConf.GetLevelConfValueList(), v) {
					res = append(res, v)
				}
			}
		}
		return res
	}
	return nil
}

func isContainLevel(items []*game_card.GameCardLevelConf, item string) bool {
	for _, eachItem := range items {
		if eachItem.GetLevelName() == item {
			return true
		}
	}
	return false
}

func isContainString(items []string, item string) bool {
	for _, eachItem := range items {
		if eachItem == item {
			return true
		}
	}
	return false
}
