package msg_list

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/channel_path"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	accountGo "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/log"
	game_hall_logic "golang.52tt.com/protocol/app/game-hall-logic"
	game_hall "golang.52tt.com/protocol/services/game-hall"
	multi_publisher "golang.52tt.com/protocol/services/multi-publisher"
	"golang.52tt.com/services/game-hall-logic/internal/mgr/msg_list/list_filter"
	"google.golang.org/protobuf/runtime/protoimpl"
	"time"
)

const (
	maxShowMemCount = 3
	noJoinMemShow   = "等待举手报名"
)

type ListMgr struct {
	gameHallClient       game_hall.GameHallClient
	accountCli           accountGo.IClient
	multiPublisherClient multi_publisher.MultiPublisherClient
}

func NewMsgListMgr(gameHallClient game_hall.GameHallClient, accountCli *accountGo.Client,
	multiPublisherClient multi_publisher.MultiPublisherClient) *ListMgr {
	return &ListMgr{
		gameHallClient:       gameHallClient,
		accountCli:           accountCli,
		multiPublisherClient: multiPublisherClient,
	}
}

func (m *ListMgr) GenGameImMsg(ctx context.Context, tabId uint32, originMsgs []*game_hall_logic.GameImMsg, reqUid uint32,
	gameImUserInfoMap map[uint32]*game_hall_logic.GameImUserInfo, filter *list_filter.Filter) ([]*game_hall_logic.GameImMsg, error) {

	teamMsgId := make([]uint64, 0, len(originMsgs))
	quoteMsgs := make([]*game_hall_logic.GameImMsg, 0, len(originMsgs))
	for _, msgInfo := range originMsgs {
		if msgInfo.GetMsgType() == uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_FORM_TEAM) {
			teamMsgId = append(teamMsgId, msgInfo.GetMsgId())
		}
		if msgInfo.GetMsgType() == uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_QUOTE) {
			quoteMsgs = append(quoteMsgs, msgInfo)
		}
	}
	var (
		teamResp    *game_hall.BatchGetGameHallTeamListResp
		joinUserMap map[uint32]*accountGo.User
		err         error
	)
	if len(teamMsgId) > 0 {
		teamResp, err = m.gameHallClient.BatchGetGameHallTeamList(ctx, &game_hall.BatchGetGameHallTeamListReq{
			MsgIds: teamMsgId,
			TabId:  tabId,
		})
		if err != nil {
			// 组队信息查询失败不返回错误
			log.WarnWithCtx(ctx, "gameHallClient.BatchGetGameHallTeamList err:%v, teamResp:%s", err, teamResp.String())
		}
		joinUids := make([]uint32, 0, len(teamResp.GetMemInfo())+1)
		joinUids = append(joinUids, reqUid)
		for _, v := range teamResp.GetMemInfo() {
			joinUids = append(joinUids, v.GetJoinUid()...)
		}
		if len(joinUids) > 0 {
			joinUserMap, err = m.accountCli.GetUsersMap(ctx, joinUids)
			if err != nil {
				// 组队信息查询失败不返回错误
				log.WarnWithCtx(ctx, "accountCli.GetUsersMap err:%v, joinUids:%+v", err, joinUids)
			}
		}
	}

	gameImMsgs := make([]*game_hall_logic.GameImMsg, 0, len(originMsgs))
	quoteMsgMap := m.handleQuoteMsg(ctx, tabId, quoteMsgs, gameImUserInfoMap, filter)
	for _, msgInfo := range originMsgs {
		if msgInfo.GetMsgType() == uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_QUOTE) && quoteMsgMap[msgInfo.GetMsgId()] != nil {
			gameImMsgs = append(gameImMsgs, quoteMsgMap[msgInfo.GetMsgId()])
		} else {
			if msgInfo.GetMsgType() == uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_FORM_TEAM) {
				if v, ok := teamResp.GetMemInfo()[msgInfo.GetMsgId()]; ok {
					_ = m.UpdateTeamMsgMemberInfo(reqUid, msgInfo, v, joinUserMap)
				}
			}
			if v, ok := gameImUserInfoMap[msgInfo.GetSenderInfo().GetUid()]; ok {
				msgInfo.SenderInfo = v
			}
			gameImMsgs = append(gameImMsgs, msgInfo)
		}

	}
	return gameImMsgs, nil
}

// key 引用消息id， value 被引用的消息
func (m *ListMgr) getQuoteMsgMap(ctx context.Context, tabId uint32, quoteMsgs []*game_hall_logic.GameImMsg) map[uint64]*game_hall_logic.GameImMsg {
	quoteMsgMap := make(map[uint64]*game_hall_logic.GameImMsg)
	if len(quoteMsgs) == 0 {
		return quoteMsgMap
	}
	quoteMsgIds := make([]uint64, 0, len(quoteMsgs))
	quoteMap := make(map[uint64]uint64)
	for _, v := range quoteMsgs {
		msg := game_hall_logic.GameQuoteMsg{}
		err := proto.Unmarshal(v.GetMsgContentBytes(), &msg)
		if err != nil {
			// 降级处理，报错不更新引用的消息即可
			log.WarnWithCtx(ctx, "getQuoteMsgMap Unmarshal tabId:%d quoterMsg:%+v err:%v", tabId, v, err)
			continue
		}
		quoteMsgIds = append(quoteMsgIds, msg.GetQuoteMsg().GetMsgId())
		quoteMap[v.GetMsgId()] = msg.GetQuoteMsg().GetMsgId()
	}
	if len(quoteMsgIds) == 0 {
		return quoteMsgMap
	}
	quoteMsgResp, err := m.multiPublisherClient.GetMessagesBySeqId(ctx, &multi_publisher.GetMessagesBySeqIdReq{
		ChannelPath: channel_path.CreateChannelPath(ctx, channel_path.NAMESPACE_GAME_HALL, tabId),
		SeqIdList:   quoteMsgIds,
	})
	if err != nil {
		// 降级处理，报错不更新引用的消息即可
		log.WarnWithCtx(ctx, "getQuoteMsgMap GetMessagesBySeqId tabId:%d quoterMsgIds:%+v err:%v", tabId, quoteMsgIds, err)
		return quoteMsgMap
	}

	for parent, child := range quoteMap {
		if msg, ok := quoteMsgResp.GetMessageMap()[child]; ok {
			gameImMsg := &game_hall_logic.GameImMsg{}
			err = msg.GetData().UnmarshalTo(protoimpl.X.ProtoMessageV2Of(gameImMsg))
			if err != nil {
				// 降级处理，报错不更新引用的消息即可
				log.WarnWithCtx(ctx, "getQuoteMsgMap UnmarshalTo tabId:%d quoterMsgIds:%+v err:%v", tabId, quoteMsgIds, err)
				return quoteMsgMap
			}
			quoteMsgMap[parent] = gameImMsg
		}
	}
	return quoteMsgMap
}

func (m *ListMgr) handleQuoteMsg(ctx context.Context, tabId uint32, quoteMsgs []*game_hall_logic.GameImMsg,
	gameImUserInfoMap map[uint32]*game_hall_logic.GameImUserInfo, filter *list_filter.Filter) map[uint64]*game_hall_logic.GameImMsg {
	resMap := make(map[uint64]*game_hall_logic.GameImMsg)
	if len(quoteMsgs) == 0 {
		return resMap
	}
	// key 引用消息id， value 被引用的消息
	quoteMsgMap := m.getQuoteMsgMap(ctx, tabId, quoteMsgs)
	curTime := time.Now().Unix()
	for _, v := range quoteMsgs {
		resMap[v.MsgId] = v
		if msg, ok := quoteMsgMap[v.GetMsgId()]; ok {
			quoteMsg := &game_hall_logic.GameQuoteMsg{}
			err := proto.Unmarshal(v.GetMsgContentBytes(), quoteMsg)
			if err != nil {
				log.ErrorWithCtx(ctx, "handleQuoteMsg Unmarshal err:%v, msg:%+v", err, v)
				continue
			}
			retCode := filter.NeedFilter(ctx, msg, curTime)
			if retCode == list_filter.FilterOne || retCode == list_filter.FilterAll {
				msg.MsgStatus = uint32(game_hall_logic.MsgStatus_MSG_STATUS_DELETE)
			}
			if senderInfo, ok := gameImUserInfoMap[msg.GetSenderInfo().GetUid()]; ok {
				msg.SenderInfo = senderInfo
			}
			quoteMsg.QuoteMsg = msg
			marshalBytes, err := proto.Marshal(quoteMsg)
			if err != nil {
				log.ErrorWithCtx(ctx, "handleQuoteMsg Marshal err:%v, msg:%+v", err, v)
				continue
			}
			v.MsgContentBytes = marshalBytes
			if parentMsgSender, ok := gameImUserInfoMap[v.GetSenderInfo().GetUid()]; ok {
				v.SenderInfo = parentMsgSender
			}
			resMap[v.MsgId] = v
		}
	}
	return resMap
}

func (m *ListMgr) UpdateTeamMsgMemberInfo(reqUid uint32, gameImMsg *game_hall_logic.GameImMsg,
	teamInfo *game_hall.GameHallTeamInfo, joinUserMap map[uint32]*accountGo.User) error {

	teamMsg := &game_hall_logic.GameFormTeamMsg{}
	err := proto.Unmarshal(gameImMsg.GetMsgContentBytes(), teamMsg)
	if err != nil {
		return err
	}
	teamMsg.TeamInfo = noJoinMemShow
	if teamInfo != nil && len(teamInfo.GetJoinUid()) > 0 {
		accounts := make([]string, 0, maxShowMemCount)
		if teamInfo.GetIsSelfJoin() {
			teamMsg.JoinUid = reqUid
			accounts = append(accounts, joinUserMap[reqUid].GetUsername())
		}
		for _, joinUid := range teamInfo.GetJoinUid() {
			if joinUid == reqUid {
				continue
			}
			if len(accounts) >= maxShowMemCount {
				break
			}
			// 添加非自己的用户
			if v, ok := joinUserMap[joinUid]; ok {
				accounts = append(accounts, v.GetUsername())
			}
		}
		teamMsg.MemberAccount = accounts
		if teamInfo.GetMemCount() > 0 {
			teamMsg.TeamInfo = fmt.Sprintf("%d人已举手", teamInfo.GetMemCount())
		}
	}
	bytes, err := proto.Marshal(teamMsg)
	if err != nil {
		return err
	}
	gameImMsg.MsgContentBytes = bytes
	return nil
}
