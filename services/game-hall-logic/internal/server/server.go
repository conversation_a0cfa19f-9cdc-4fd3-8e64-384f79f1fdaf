package server

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/channel_path"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	accountGo "golang.52tt.com/clients/account-go"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/clients/channel"
	channelscheme "golang.52tt.com/clients/channel-scheme"
	"golang.52tt.com/clients/channelol"
	channelol_stat_go "golang.52tt.com/clients/channelol-stat-go"
	game_card_cli "golang.52tt.com/clients/game-card"
	game_screenshot "golang.52tt.com/clients/game-screenshot"
	iop "golang.52tt.com/clients/iop-proxy"
	presence "golang.52tt.com/clients/presence/v2"
	pushNotificationClient "golang.52tt.com/clients/push-notification/v2"
	push_proxy "golang.52tt.com/clients/push-notification/v3/push-proxy"
	risk_mng_api_cli "golang.52tt.com/clients/risk-mng-api"
	seqgenV2 "golang.52tt.com/clients/seqgen/v2"
	"golang.52tt.com/clients/topic-channel/tab"
	user_online "golang.52tt.com/clients/user-online"
	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/pkg/cogroup_util"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/game-hall-logic"
	"golang.52tt.com/protocol/common/status"
	channel_base_api "golang.52tt.com/protocol/services/channel-base-api"
	channel_play_tab "golang.52tt.com/protocol/services/channel-play-tab"
	"golang.52tt.com/protocol/services/demo/echo"
	friendolgo "golang.52tt.com/protocol/services/friend-ol-svr-go"
	game_fre_server "golang.52tt.com/protocol/services/game-fre-server"
	game_hall "golang.52tt.com/protocol/services/game-hall"
	game_red_dot "golang.52tt.com/protocol/services/game-red-dot"
	game_ugc_content "golang.52tt.com/protocol/services/game-ugc-content"
	multi_publisher "golang.52tt.com/protocol/services/multi-publisher"
	"golang.52tt.com/services/game-hall-logic/internal/cache"
	config "golang.52tt.com/services/game-hall-logic/internal/config/ttconfig/game_hall_logic"
	"golang.52tt.com/services/game-hall-logic/internal/mgr/general_msg"
	"golang.52tt.com/services/game-hall-logic/internal/mgr/msg_list"
	"golang.52tt.com/services/game-hall-logic/internal/mgr/msg_list/list_filter"
	"golang.52tt.com/services/game-hall-logic/internal/mgr/notify"
	"golang.52tt.com/services/game-hall-logic/internal/mgr/team_mgr"
	"strconv"
	"sync"
	"time"
)

const (
	getUserInfoErrorMsg = "获取用户信息失败"
	InvalidZero         = 0
)

type StartConfig struct {
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.InfoWithCtx(ctx, "server startup with cfg: %+v", *cfg)

	err := config.InitGameHallLogicConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "InitGameHallLogicConfig err:%v", err)
		return nil, err
	}
	multiPublisherCli, err := multi_publisher.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, " multi_publisher.NewClient(ctx) err :%v", err)
		return nil, err
	}
	accountCli, _ := accountGo.NewClient()
	censoringClient := censoring_proxy.NewClient()
	tabClient, _ := tab.NewClient()
	gameCardClient, _ := game_card_cli.NewClient()
	userOlClient, _ := user_online.NewClient()
	riskMngClient, _ := risk_mng_api_cli.NewClient()
	seqgenV2Client, _ := seqgenV2.NewClient()
	gameHallClient, _ := game_hall.NewClient(ctx)
	screenshotCli, _ := game_screenshot.NewClient()
	gameFreCli, _ := game_fre_server.NewClient(ctx)
	channelOlStatCli, _ := channelol_stat_go.NewClient()
	channelOl := channelol.NewIClient()
	channelCli := channel.NewClient()
	channelBaseApiCli, _ := channel_base_api.NewClient(ctx)
	channelSchemeClient := channelscheme.NewClient()
	iopCli := iop.NewIClient()
	pushClient, _ := pushNotificationClient.NewClient()
	pushProxy, _ := push_proxy.NewClient()
	presenceClient, _ := presence.NewClient()
	gameUgcConCli, err := game_ugc_content.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "game_ugc_content.NewClient err:%v", err)
	}
	redDotClient, _ := game_red_dot.NewClient(ctx)
	channelPlayTabClient, _ := channel_play_tab.NewClient(ctx)

	localCache, err := cache.NewLocalInfoCache(ctx, tabClient, gameCardClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewLocalInfoCache err:%v", err)
		return nil, err
	}
	friendOlGoClient, err := friendolgo.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "friendolgo.NewClient err:%v", err)
		return nil, err
	}

	// 百灵数据统计 初始化
	byLinkCollect, err := bylink.NewKfkCollector()
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to new kfk collector: %v", err)
		return nil, err
	}
	bylink.InitGlobalCollector(byLinkCollect)

	msgPush := notify.NewMsgPusher(pushClient, pushProxy, presenceClient)
	generalMsg := general_msg.NewGeneralMsg(censoringClient, multiPublisherCli, screenshotCli, gameHallClient, msgPush, localCache, gameFreCli)
	teamMgr := team_mgr.NewTeamMgr(gameHallClient, gameFreCli, channelOlStatCli, channelOl, channelCli,
		channelBaseApiCli, accountCli, channelSchemeClient, msgPush, redDotClient, gameCardClient, localCache)

	msgListMgr := msg_list.NewMsgListMgr(gameHallClient, accountCli, multiPublisherCli)

	return &Server{
		multiPublisherClient: multiPublisherCli,
		seqgenV2Client:       seqgenV2Client,
		gameHallClient:       gameHallClient,
		accountCli:           accountCli,
		censoringClient:      censoringClient,
		gameCardClient:       gameCardClient,
		userOlClient:         userOlClient,
		riskMngApiClient:     riskMngClient,
		gameUgcConCli:        gameUgcConCli,
		iopCli:               iopCli,
		gameFreClient:        gameFreCli,
		friendOlGoClient:     friendOlGoClient,
		channelPlayTabClient: channelPlayTabClient,

		localCache: localCache,
		listMgr:    msgListMgr,
		generalMsg: generalMsg,
		teamMgr:    teamMgr,
	}, nil
}

type Server struct {
	multiPublisherClient multi_publisher.MultiPublisherClient
	seqgenV2Client       seqgenV2.IClient
	gameHallClient       game_hall.GameHallClient
	accountCli           accountGo.IClient
	censoringClient      censoring_proxy.IClient
	gameCardClient       game_card_cli.IClient
	userOlClient         user_online.IClient
	riskMngApiClient     risk_mng_api_cli.IClient
	gameUgcConCli        game_ugc_content.GameUgcContentClient
	iopCli               iop.IClient
	gameFreClient        game_fre_server.GameFreServerClient
	friendOlGoClient     friendolgo.FriendOlSvrGoClient
	channelPlayTabClient channel_play_tab.ChannelPlayTabClient

	localCache cache.ILocalCache
	listMgr    msg_list.IListMgr
	generalMsg general_msg.IGeneralMsg
	teamMgr    team_mgr.ITeamMgr
}

func (s *Server) ShutDown() {}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

// GetBuildChannelInfo 获取订阅令牌及频道路径
func (s *Server) GetBuildChannelInfo(ctx context.Context, req *game_hall_logic.GetBuildChannelInfoRequest) (*game_hall_logic.GetBuildChannelInfoResponse, error) {
	out := &game_hall_logic.GetBuildChannelInfoResponse{}
	svcInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	if req.GetTabId() == InvalidZero {
		log.WarnWithCtx(ctx, "GetBuildChannelInfo zero tabId svcInfo:%s", svcInfo.String())
		return out, nil
	}
	// 生成频道路径
	channelPath := channel_path.CreateChannelPath(ctx, channel_path.NAMESPACE_GAME_HALL, req.GetTabId())
	resp, err := s.multiPublisherClient.GrantSubscribeToken(ctx, &multi_publisher.GrantSubscribeTokenReq{
		Uid:             svcInfo.UserID,
		ChannelPathList: []string{channelPath},
		ClientInfo: &multi_publisher.ClientInfo{
			DeviceId:     svcInfo.DeviceID,
			ClientType:   uint32(svcInfo.ClientType),
			TerminalType: svcInfo.TerminalType,
			ClientIp:     svcInfo.ClientIPAddr().String(),
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBuildChannelInfo GrantSubscribeToken svcInfo:%s err:%v", svcInfo.String(), err)
		return out, err
	}
	out.ChannelPath = channelPath
	out.Token = resp.GetToken().GetToken()
	log.InfoWithCtx(ctx, "GetBuildChannelInfo success req:%s, out:%s", req.String(), out.String())
	return out, nil
}

// GetMsgList 拉取历史消息
func (s *Server) GetMsgList(ctx context.Context, req *game_hall_logic.GetMsgListRequest) (*game_hall_logic.GetMsgListResponse, error) {
	out := &game_hall_logic.GetMsgListResponse{}
	svcInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	if req.GetTabId() == InvalidZero {
		log.WarnWithCtx(ctx, "GetMsgList zero tabId svcInfo:%s", svcInfo.String())
		return out, nil
	}

	channelPath := channel_path.CreateChannelPath(ctx, channel_path.NAMESPACE_GAME_HALL, req.GetTabId())
	originMsg, err := s.getOriginMsg(ctx, req, channelPath)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMsgList GetOriginMsg req:%s svcInfo:%s err:%v", req.String(), svcInfo.String(), err)
		return out, err
	}
	if len(originMsg) == 0 {
		out.LoadFinish = true
		log.WarnWithCtx(ctx, "GetMsgList len(originMsg)=0 req:%s svcInfo:%s", req.String(), svcInfo.String())
		if req.GetAction() == game_hall_logic.GetMsgListRequest_ACTION_TYPE_JUMP {
			err = protocol.NewExactServerError(nil, status.ErrGameHallJumpMsgExpired, config.GetGameHallLogicConfig().GetJumpMsgExpiredToast(req.GetTabId()))
		}
		return out, err
	}
	msgFilter, err := list_filter.NewFilter(ctx, req, originMsg, s.gameHallClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMsgList NewFilter req:%s originMsg:%+v err:%v", req.String(), originMsg, err)
		return out, err
	}
	// 过滤消息
	effectiveMsg, needMsgNum := msgFilter.DoFilter(ctx)

	if len(effectiveMsg) < needMsgNum {
		out.LoadFinish = true
	}
	if req.GetAction() == game_hall_logic.GetMsgListRequest_ACTION_TYPE_JUMP {
		err = s.checkJumpMsg(req.GetTabId(), req.GetMsgId(), effectiveMsg)
		if err != nil {
			log.WarnWithCtx(ctx, "GetMsgList checkJumpMsg req:%s msg expired err:%v", req.String(), err)
			return out, err
		}
	}
	if len(effectiveMsg) == 0 {
		log.WarnWithCtx(ctx, "GetMsgList DoFilter effectiveMsg len=0 req:%s originMsg:%+v", req.String(), originMsg)
		return out, nil
	}

	userMap, err := s.getUserInfoMap(ctx, effectiveMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMsgList getUserInfoMap in:%s svcInfo:%s err:%v", req.String(), svcInfo.String(), err)
		return out, err
	}

	gameImUserInfoMap, err := s.GenSenderInfo(ctx, userMap, req.GetTabId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMsgList GenSenderInfo req:%s originMsg:%+v err:%v", req.String(), originMsg, err)
		return out, err
	}

	out.MsgList, err = s.listMgr.GenGameImMsg(ctx, req.GetTabId(), effectiveMsg, svcInfo.UserID, gameImUserInfoMap, msgFilter)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMsgList GenGameImMsg in:%s svcInfo:%s err:%v", req.String(), svcInfo.String(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "GetMsgList success req:%s, loadFinish:%v len(out):%d", req.String(), out.GetLoadFinish(), len(out.GetMsgList()))
	return out, nil
}

func (s *Server) getUserInfoMap(ctx context.Context, originMsg []*game_hall_logic.GameImMsg) (map[uint32]*accountGo.User, error) {
	allUids := make([]uint32, 0, len(originMsg))
	for _, msg := range originMsg {
		allUids = append(allUids, msg.GetSenderInfo().GetUid())
		if msg.GetMsgType() == uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_QUOTE) {
			quoteMsg := &game_hall_logic.GameQuoteMsg{}
			err := proto.Unmarshal(msg.GetMsgContentBytes(), quoteMsg)
			if err != nil {
				log.ErrorWithCtx(ctx, "getUserInfoMap Unmarshal quoteMsg err:%v, msg:%+v", err, msg)
				return nil, err
			}
			allUids = append(allUids, quoteMsg.GetQuoteMsg().GetSenderInfo().GetUid())
		}
	}
	userMap, err := s.accountCli.GetUsersMap(ctx, allUids)
	if err != nil {
		log.ErrorWithCtx(ctx, "getUserInfoMap GetUsersMap allUids:%+v err:%v", allUids, err)
		return nil, err
	}
	return userMap, nil
}

func genGetMsgReq(in *game_hall_logic.GetMsgListRequest, channelPath string) *multi_publisher.GetMessagesReq {

	req := &multi_publisher.GetMessagesReq{
		ChannelPath: channelPath,
		Limit:       config.GetGameHallLogicConfig().GetGetOriginMsgLimit(),
	}

	switch in.GetAction() {
	case game_hall_logic.GetMsgListRequest_ACTION_TYPE_UP:
		req.IterType = multi_publisher.GetMessagesReq_ITER_TYPE_FORWARD
		req.StartSeqId = in.GetMsgId() + 1
	case game_hall_logic.GetMsgListRequest_ACTION_TYPE_DOWN:
		req.IterType = multi_publisher.GetMessagesReq_ITER_TYPE_REVERSE
		if in.GetMsgId() != 0 {
			req.StartSeqId = in.GetMsgId() - 1
		}
	case game_hall_logic.GetMsgListRequest_ACTION_TYPE_JUMP:
		req.IterType = multi_publisher.GetMessagesReq_ITER_TYPE_CONTEXT
		req.StartSeqId = in.GetMsgId()
	}
	return req
}

func (s *Server) getOriginMsg(ctx context.Context, in *game_hall_logic.GetMsgListRequest, channelPath string) ([]*multi_publisher.Message, error) {
	var resMes []*multi_publisher.Message
	switch game_hall_logic.ListEntrance(in.GetEntrance()) {
	case game_hall_logic.ListEntrance_LIST_ENTRANCE_FORM_TEAM_MSG:
		limit := config.GetGameHallLogicConfig().GetFirstPageCount() * 2
		seqIdList, err := s.teamMgr.GetTeamAndInviteMsg(ctx, in.GetTabId(), in.GetMsgId(), limit)
		if err != nil {
			log.ErrorWithCtx(ctx, "getOriginMsg GetTeamAndInviteMsgId req:%s err:%+v", in.String(), err)
			return nil, err
		}
		if len(seqIdList) == 0 {
			return nil, nil
		}
		resp, err := s.multiPublisherClient.GetMessagesBySeqId(ctx, &multi_publisher.GetMessagesBySeqIdReq{
			ChannelPath: channelPath,
			SeqIdList:   seqIdList,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetOriginMsg GetMessagesBySeqId req:%s err:%+v", in.String(), err)
			return nil, err
		}

		resMes = make([]*multi_publisher.Message, 0, len(seqIdList))
		for _, seqId := range seqIdList {
			if msg, ok := resp.GetMessageMap()[seqId]; ok {
				resMes = append(resMes, msg)
			} else {
				log.WarnWithCtx(ctx, "GetOriginMsg GetMessagesBySeqId seqId:%d not found, in:%s", seqId, in.String())
			}
		}

	case game_hall_logic.ListEntrance_LIST_ENTRANCE_ALL_MSG:
		req := genGetMsgReq(in, channelPath)
		resp, err := s.multiPublisherClient.GetMessages(ctx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "MsgListHandler GetOriginMsg GetMessages req:%s err:%+v", req.String(), err)
			return nil, err
		}
		resMes = resp.GetMessages()
	case game_hall_logic.ListEntrance_LIST_ENTRANCE_PC_IM_MSG:
		req := &multi_publisher.GetMessagesReq{
			ChannelPath: channelPath,
			StartSeqId:  0,
			Limit:       1,
			IterType:    multi_publisher.GetMessagesReq_ITER_TYPE_REVERSE,
		}
		resp, err := s.multiPublisherClient.GetMessages(ctx, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "PC IM GetOriginMsg GetMessages req:%s err:%+v", req.String(), err)
			return nil, err
		}
		resMes = resp.GetMessages()
	default:
		log.WarnWithCtx(ctx, "MsgListHandler GetOriginMsg invalid in:%s", in.String())
	}

	return resMes, nil
}

func (s *Server) checkJumpMsg(tabId uint32, jumpMsgId uint64, resMes []*game_hall_logic.GameImMsg) error {
	if len(resMes) == 0 {
		return protocol.NewExactServerError(nil, status.ErrGameHallJumpMsgExpired, config.GetGameHallLogicConfig().GetJumpMsgExpiredToast(tabId))
	}
	for _, v := range resMes {
		if v.GetMsgId() == jumpMsgId {
			return nil
		}
	}
	return protocol.NewExactServerError(nil, status.ErrGameHallJumpMsgExpired, config.GetGameHallLogicConfig().GetJumpMsgExpiredToast(tabId))
}

// 校验channelPath
func decodeChannelPath(ctx context.Context, channelPath string) (uint32, error) {
	if len(channelPath) == 0 {
		return 0, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	namespace, identifier, err := channel_path.ParseChannelPath(ctx, channelPath)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkChannelPath ParseChannelPath channelPath:%s err:%v", channelPath, err)
		return 0, err
	}
	if channel_path.Namespace(namespace) != channel_path.NAMESPACE_GAME_HALL {
		log.ErrorWithCtx(ctx, "checkChannelPath invalid namespace channelPath:%s", channelPath)
		return 0, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	tabId, err := strconv.Atoi(identifier)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkChannelPath invalid identifier channelPath:%s", channelPath)
		return 0, err
	}
	if tabId == InvalidZero {
		log.ErrorWithCtx(ctx, "checkChannelPath invalid tabId channelPath:%s", channelPath)
		return 0, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	return uint32(tabId), nil
}

func (s *Server) UpdateGameHallNotifyStatus(ctx context.Context, req *game_hall_logic.UpdateGameHallNotifyStatusReq) (*game_hall_logic.UpdateGameHallNotifyStatusResp, error) {
	out := &game_hall_logic.UpdateGameHallNotifyStatusResp{}
	svcInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	if req.GetTabId() == InvalidZero || svcInfo.UserID == InvalidZero {
		log.WarnWithCtx(ctx, "UpdateGameHallNotifyStatus invalid tabId=0, uid:%d", svcInfo.UserID)
		return out, nil
	}

	_, err := s.gameHallClient.UpdateGameHallNotifyStatus(ctx, &game_hall.UpdateGameHallNotifyStatusReq{
		Uid:        svcInfo.UserID,
		TabId:      req.GetTabId(),
		OpenNotify: req.GetOpenNotify(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateGameHallNotifyStatus req:%s err:%v", req.String(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "UpdateGameHallNotifyStatus success req:%s", req.String())
	return out, nil
}

func (s *Server) GetGameHallNotifyStatus(ctx context.Context, req *game_hall_logic.GetGameHallNotifyStatusReq) (*game_hall_logic.GetGameHallNotifyStatusResp, error) {
	out := &game_hall_logic.GetGameHallNotifyStatusResp{}
	svcInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	if req.GetTabId() == InvalidZero || svcInfo.UserID == InvalidZero {
		log.WarnWithCtx(ctx, "GetGameHallNotifyStatus invalid tabId=0, uid:%d", svcInfo.UserID)
		return out, nil
	}

	statusResp, err := s.gameHallClient.GetGameHallNotifyStatus(ctx, &game_hall.GetGameHallNotifyStatusReq{
		Uid:   svcInfo.UserID,
		TabId: req.GetTabId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameHallNotifyStatus req:%s err:%v", req.String(), err)
		return out, err
	}
	out.IsOpen = statusResp.GetIsOpen()
	log.InfoWithCtx(ctx, "GetGameHallNotifyStatus success req:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) SetShowEntranceSetting(ctx context.Context, req *game_hall_logic.SetShowEntranceSettingReq) (*game_hall_logic.SetShowEntranceSettingResp, error) {
	out := &game_hall_logic.SetShowEntranceSettingResp{}
	svcInfo, ok := grpc.ServiceInfoFromContext(ctx)
	log.InfoWithCtx(ctx, "SetShowEntranceSetting req:%s", req.String())
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	if req.GetTabId() == InvalidZero || svcInfo.UserID == InvalidZero {
		return out, nil
	}

	_, err := s.gameHallClient.SetShowEntranceSetting(ctx, &game_hall.SetShowEntranceSettingReq{
		Uid: svcInfo.UserID,
		EntranceSetting: &game_hall.EntranceSettingItem{
			TabId:          req.GetTabId(),
			IsOpen:         req.GetIsOpen(),
			EntranceSource: game_hall.EntranceSettingItem_ENTRANCE_SOURCE_PC_IM,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetShowEntranceSetting fail, req:%s err:%v", req.String(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "SetShowEntranceSetting success req:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) GetShowEntranceSetting(ctx context.Context, req *game_hall_logic.GetShowEntranceSettingReq) (*game_hall_logic.GetShowEntranceSettingResp, error) {
	out := &game_hall_logic.GetShowEntranceSettingResp{}
	svcInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok || svcInfo.UserID == InvalidZero {
		log.WarnWithCtx(ctx, "GetShowEntranceSetting invalid tabId=0, uid:%d, req:%v", svcInfo.UserID, req.String())
		return out, nil
	}
	resp, err := s.gameHallClient.GetShowEntranceSetting(ctx, &game_hall.GetShowEntranceSettingReq{
		Uid:   svcInfo.UserID,
		TabId: req.GetTabId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetShowEntranceSetting fail, req:%s err:%v", req.String(), err)
		return out, err
	}
	if len(resp.GetEntranceSettingList()) == 0 {
		log.InfoWithCtx(ctx, "GetShowEntranceSetting no entrance setting found, req:%s", req.String())
		return out, nil
	}
	// 过滤组队入口屏蔽的玩法
	tabIds := s.filterValidTabIds(ctx, resp.GetEntranceSettingList(), req)
	if len(tabIds) == 0 {
		return out, nil
	}
	// 获取tabId对应的最新消息
	tabMsgMap, err := s.getTabLatestMsg(tabIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetShowEntranceSetting getTabLatestMsg req:%s err:%v", req.String(), err)
		return out, err
	}
	out.EntranceSettingList = s.buildEntranceSettingList(ctx, tabIds, tabMsgMap)
	log.InfoWithCtx(ctx, "GetShowEntranceSetting success req:%s, len(out.GetEntranceSettingList()):%d", req.String(), len(out.GetEntranceSettingList()))
	return out, nil
}

func (s *Server) filterValidTabIds(ctx context.Context, entranceSettings []*game_hall.EntranceSettingItem, req *game_hall_logic.GetShowEntranceSettingReq) []uint32 {
	tempTabIds := make([]uint32, 0, len(entranceSettings))
	for _, item := range entranceSettings {
		tempTabIds = append(tempTabIds, item.GetTabId())
	}
	gameUgcResp, err := s.gameUgcConCli.BatGetGameConfigTabDetailByTabId(ctx, &game_ugc_content.BatGetGameConfigTabDetailByTabIdReq{
		TabIds: tempTabIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "filterValidTabIds GetGameConfigTabDetailByTabId req:%s err:%v", req.String(), err)
		return nil
	}

	configTabMap := make(map[uint32]bool, len(gameUgcResp.GetConfig()))
	for _, conf := range gameUgcResp.GetConfig() {
		for _, info := range conf.GetConfigTabs() {
			if info.GetConfigTabType() == game_ugc_content.ConfigTabType_ConfigTabType_GameHall {
				configTabMap[conf.GetTabId()] = true
				break
			}
		}
	}
	tabIds := make([]uint32, 0, len(entranceSettings))
	for _, item := range entranceSettings {
		if configTabMap[item.GetTabId()] {
			tabIds = append(tabIds, item.GetTabId())
		} else {
			log.InfoWithCtx(ctx, "filterValidTabIds tabId:%d not found in configTabMap, req:%s", item.GetTabId(), req.String())
		}
	}
	return tabIds
}

func (s *Server) buildEntranceSettingList(ctx context.Context, tabIds []uint32, tabMsgMap *sync.Map) []*game_hall_logic.EntranceSettingItem {
	result := make([]*game_hall_logic.EntranceSettingItem, 0, len(tabIds))
	for _, tabId := range tabIds {
		tabInfo := s.localCache.GetTabInfoById(tabId)
		if tabInfo == nil {
			continue
		}
		var msg *game_hall_logic.GameImMsg
		tempMsg, exist := tabMsgMap.Load(tabInfo.GetId())
		if exist {
			log.InfoWithCtx(ctx, "buildEntranceSettingList tabId:%d found in tabMsgMap, tabId:%d", tabInfo.GetId(), tabId)
			msg = tempMsg.(*game_hall_logic.GameImMsg)
		}
		result = append(result, &game_hall_logic.EntranceSettingItem{
			TabId:   tabInfo.GetId(),
			TabName: tabInfo.GetName() + "・聊天室",
			TabIcon: tabInfo.GetCardsImageUrl(),
			Msg:     msg,
		})
	}
	return result
}

func (s *Server) getTabLatestMsg(tabIds []uint32) (*sync.Map, error) {
	var res sync.Map
	pool := cogroup_util.NewGoroutinePool(5)
	for _, id := range tabIds {
		pool.Add()
		go func(tabId uint32) {
			ctx, cancel := context.WithTimeout(context.Background(), 300*time.Millisecond)
			defer func() {
				pool.Done()
				cancel()
			}()

			channelPath := channel_path.CreateChannelPath(ctx, channel_path.NAMESPACE_GAME_HALL, tabId)
			req := &game_hall_logic.GetMsgListRequest{
				TabId:    tabId,
				Entrance: uint32(game_hall_logic.ListEntrance_LIST_ENTRANCE_PC_IM_MSG),
			}
			originMsg, err := s.getOriginMsg(ctx, req, channelPath)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMsgList GetOriginMsg req:%s svcInfo:%s err:%v", channelPath, err)
				return
			}
			if len(originMsg) == 0 {
				log.InfoWithCtx(ctx, "getMsg originMsg len=0 req:%s tabId:%d", req.String(), tabId)
				return
			}

			msgFilter, err := list_filter.NewFilter(ctx, req, originMsg, s.gameHallClient)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMsgList NewFilter req:%s originMsg:%+v err:%v", req.String(), originMsg, err)
				return
			}
			// 过滤消息
			effectiveMsg, _ := msgFilter.DoFilter(ctx)
			if len(effectiveMsg) == 0 {
				log.InfoWithCtx(ctx, "getMsg effectiveMsg len=0 req:%s originMsg:%+v", req.String(), originMsg)
				return
			}
			res.Store(tabId, effectiveMsg[len(effectiveMsg)-1]) // 取最新一条消息
		}(id)
	}
	pool.Wait()
	return &res, nil
}

func (s *Server) GetGameHallPinConf(ctx context.Context, req *game_hall_logic.GetGameHallPinConfReq) (*game_hall_logic.GetGameHallPinConfResp, error) {
	out := &game_hall_logic.GetGameHallPinConfResp{}
	svcInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.WarnWithCtx(ctx, "GetGameHallPinConf grpc.ServiceInfoFromContext failed, req:%v", req)
		return out, nil
	}
	if req.GetTabId() == InvalidZero || svcInfo.UserID == InvalidZero {
		log.WarnWithCtx(ctx, "GetGameHallPinConf invalid tabId=0, uid:%d, req:%v", svcInfo.UserID, req.String())
		return out, nil
	}
	resp, err := s.gameHallClient.GetActivePinConfByTabId(ctx, &game_hall.GetActivePinConfByTabIdReq{
		TabId: req.GetTabId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameHallPinConf GetActivePinConfByTabId fail, req:%s err:%v", req.String(), err)
		return out, err
	}
	if len(resp.GetPinConfList()) == 0 {
		log.InfoWithCtx(ctx, "GetGameHallPinConf no pin conf found, req:%s", req.String())
		return out, nil
	}
	allUids := make([]uint32, 0, len(resp.GetPinConfList()))
	for _, item := range resp.GetPinConfList() {
		allUids = append(allUids, item.GetUid())
	}
	userMap, err := s.accountCli.GetUsersMap(ctx, allUids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameHallPinConf GetUsersMap allUids:%+v err:%v", allUids, err)
		return out, err
	}

	out.PinConfList = make([]*game_hall_logic.GameHallPinConfItem, 0, len(resp.GetPinConfList()))
	for _, item := range resp.GetPinConfList() {
		highlightText := make([]*game_hall_logic.GameHallPinConfItem_HighlightText, 0, len(item.GetText()))
		for _, text := range item.GetText() {
			highlightText = append(highlightText, &game_hall_logic.GameHallPinConfItem_HighlightText{
				Highlight: text.GetHighlight(),
				Url:       text.GetUrl(),
			})
		}
		out.PinConfList = append(out.PinConfList, &game_hall_logic.GameHallPinConfItem{
			Ttid:     userMap[item.GetUid()].GetUsername(),
			Uid:      item.GetUid(),
			Nickname: userMap[item.GetUid()].GetNickname(),
			Img:      item.GetImg(),
			Content:  item.GetContent(),
			Text:     highlightText,
		})
	}
	return out, nil
}
