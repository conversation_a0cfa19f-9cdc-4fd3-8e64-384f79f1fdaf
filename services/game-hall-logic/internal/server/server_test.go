package server

import (
	"context"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	account_go_mocks "golang.52tt.com/clients/mocks/account-go"
	censoring_mocks "golang.52tt.com/clients/mocks/censoring-proxy"
	game_card_mock "golang.52tt.com/clients/mocks/game-card"
	iop_mocks "golang.52tt.com/clients/mocks/iop-proxy"
	risk_mng_api_mocks "golang.52tt.com/clients/mocks/risk-mng-api"
	seqgen_mock "golang.52tt.com/clients/mocks/seqgen/v2"
	user_online_mocks "golang.52tt.com/clients/mocks/user-online"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	game_hall_logic "golang.52tt.com/protocol/app/game-hall-logic"
	channel_play_tab "golang.52tt.com/protocol/services/channel-play-tab"
	game_hall "golang.52tt.com/protocol/services/game-hall"
	game_ugc_content "golang.52tt.com/protocol/services/game-ugc-content"
	"golang.52tt.com/protocol/services/mocks"
	cache_mocks "golang.52tt.com/services/game-hall-logic/internal/cache/mocks"
	general_mocks "golang.52tt.com/services/game-hall-logic/internal/mgr/general_msg/mocks"
	list_mocks "golang.52tt.com/services/game-hall-logic/internal/mgr/msg_list/mocks"
	team_mocks "golang.52tt.com/services/game-hall-logic/internal/mgr/team_mgr/mocks"

	"testing"
	"time"
)

type HelperForTest struct {
	ctl *gomock.Controller
	*Server
}

func newHelperForTest(t *testing.T) (*HelperForTest, func()) {
	ctl := gomock.NewController(t)
	return &HelperForTest{
			ctl: ctl,
			Server: &Server{
				censoringClient:      censoring_mocks.NewMockIClient(ctl),
				gameFreClient:        mocks.NewMockGameFreServerClient(ctl),
				localCache:           cache_mocks.NewMockILocalCache(ctl),
				multiPublisherClient: mocks.NewMockMultiPublisherClient(ctl),
				gameHallClient:       game_hall.NewMockGameHallClient(ctl),
				accountCli:           account_go_mocks.NewMockIClient(ctl),
				seqgenV2Client:       seqgen_mock.NewMockIClient(ctl),
				gameCardClient:       game_card_mock.NewMockIClient(ctl),
				userOlClient:         user_online_mocks.NewMockIClient(ctl),
				friendOlGoClient:     mocks.NewMockFriendOlSvrGoClient(ctl),
				riskMngApiClient:     risk_mng_api_mocks.NewMockIClient(ctl),
				gameUgcConCli:        game_ugc_content.NewMockGameUgcContentClient(ctl),
				channelPlayTabClient: channel_play_tab.NewMockChannelPlayTabClient(ctl),
				iopCli:               iop_mocks.NewMockIClient(ctl),
				listMgr:              list_mocks.NewMockIListMgr(ctl),
				generalMsg:           general_mocks.NewMockIGeneralMsg(ctl),
				teamMgr:              team_mocks.NewMockITeamMgr(ctl),
			},
		}, func() {
			ctl.Finish()
		}
}

func TestSendGameImMsg_InvalidChannelPath(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	// Define test data
	req := &game_hall_logic.SendGameImMsgRequest{
		BaseReq:     &app.BaseReq{},
		ChannelPath: "invalid_channel_path",
		Request: &game_hall_logic.SendGameImMsgRequest_TextRequest_{
			TextRequest: &game_hall_logic.SendGameImMsgRequest_TextRequest{
				Text: "Test message",
			},
		},
	}

	// Invoke the tested method
	resp, err := cli.SendGameImMsg(context.Background(), req)

	// Check the result
	assert.NotNil(t, err)
	assert.NotNil(t, resp)
}

func TestSendGameImMsg_UserBanned(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	uid := uint32(12345)
	serviceInfo := &grpc.ServiceInfo{
		UserID: uid,
	}
	ctx := context.Background()
	ctx = grpc.WithServiceInfo(ctx, serviceInfo)

	req := &game_hall_logic.SendGameImMsgRequest{
		BaseReq:      &app.BaseReq{},
		ChannelPath:  "/game-hall/1",
		Request:      &game_hall_logic.SendGameImMsgRequest_TextRequest_{},
		ClientMsgTag: 123,
	}

	// Setting up mock responses
	banEndTime := time.Now().Unix() + 3600
	cli.gameHallClient.(*game_hall.MockGameHallClient).EXPECT().GetUserBanStatusMap(ctx,
		&game_hall.GetUserBanStatusMapReq{UidList: []uint32{uid}}).Return(&game_hall.GetUserBanStatusMapResp{
		BanStatusMap: map[uint32]int64{uid: banEndTime},
	}, nil)

	// Invoke the method
	out, err := cli.SendGameImMsg(ctx, req)

	// Check the result
	assert.NotNil(t, err)
	assert.Nil(t, out.Msg)
}
