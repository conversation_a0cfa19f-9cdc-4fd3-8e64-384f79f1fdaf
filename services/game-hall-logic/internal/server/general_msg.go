package server

import (
	"context"
	"encoding/json"
	"fmt"
	push_label "gitlab.ttyuyin.com/bizFund/bizFund/pkg/push-notification"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/seqgen"
	accountGo "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	game_hall_logic "golang.52tt.com/protocol/app/game-hall-logic"
	"golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/common/status"
	channel_play_tab "golang.52tt.com/protocol/services/channel-play-tab"
	game_hall "golang.52tt.com/protocol/services/game-hall"
	multi_publisher "golang.52tt.com/protocol/services/multi-publisher"
	"golang.52tt.com/services/game-hall-logic/internal/mgr/general_msg"
	"google.golang.org/protobuf/runtime/protoimpl"
	"google.golang.org/protobuf/types/known/anypb"
	"time"
)

const (
	banToast        = "由于您近期发布的内容违反了平台规定，暂无法使用该功能，将在%s 恢复使用"
	InCrowdGroup    = "1" // 用户在该人群包中
	defaultBanToast = "暂无法使用该功能"
)

func (s *Server) getMatchGroupInfo(ctx context.Context, uid uint64, crowdGroupIds []string) (map[string]string, error) {
	log.DebugWithCtx(ctx, "getMatchGroupInfo, uid:%d, crowdGroupIds:%v", uid, crowdGroupIds)
	if len(crowdGroupIds) == 0 {
		return nil, nil
	}
	matchGroupResp, err := s.iopCli.MatchGroup(ctx, []uint64{uid}, crowdGroupIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "getMatchGroupInfo IopCli.MatchGroup err: %v", err)
		return nil, err
	}
	if len(matchGroupResp) == 0 {
		log.WarnWithCtx(ctx, "getMatchGroupInfo, uid:%d, crowdGroupIds:%v, matchGroupResp is nil", uid, crowdGroupIds)
		return nil, nil
	}
	log.InfoWithCtx(ctx, "getMatchGroupInfo uid:%d, crowdGroupIds:%+v, MatchGroupResp:%+v", uid, crowdGroupIds, matchGroupResp)
	return matchGroupResp[0].Result, nil
}

func (s *Server) checkBanSay(ctx context.Context, uid, tabId uint32) error {

	// T盾禁言限制
	banResp, err := s.gameHallClient.GetUserBanStatusMap(ctx, &game_hall.GetUserBanStatusMapReq{
		UidList: []uint32{uid},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "checkBanSay GetUserBanStatusMap fail, uid:%d tabID:%d err:%v", uid, tabId, err)
		return err
	}
	curTime := time.Now().Unix()
	if banResp.GetBanStatusMap()[uid] > curTime {
		return protocol.NewExactServerError(nil, status.ErrGameHallSendMsgBan, genBanToast(banResp.GetBanStatusMap()[uid]))
	}

	// 业务后台限制
	banPostInfo, err := s.channelPlayTabClient.GetActiveBanUserConfigWithCache(ctx, &channel_play_tab.GetActiveBanUserConfigWithCacheReq{
		BanPostType: channel_play_tab.BanPostType_BanGameHallSay,
		TabId:       tabId,
		Uid:         uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "channelPlayTabClient.GetActiveBanUserConfigWithCache err:%v", err)
		return protocol.NewExactServerError(nil, status.ErrSys)
	}

	log.DebugWithCtx(ctx, "GetActiveBanUserConfigWithCache banPostConfigs:%+v", banPostInfo.GetConfigs())

	// 没有用户相关的禁止发帖配置
	if len(banPostInfo.GetConfigs()) == 0 {
		return nil
	}

	banUserPostConfigs := banPostInfo.GetConfigs()
	crowdGroupIds := make([]string, 0, len(banUserPostConfigs))
	// key 人群包ID， value对应的禁止发言toast文案
	crowdToastMap := make(map[string]string)
	for _, banUserPostConfig := range banUserPostConfigs {
		switch banUserPostConfig.GetConfigType() {
		case channel_play_tab.BanConfigType_BanConfigTypeCrowdGroup:
			crowdGroupIds = append(crowdGroupIds, banUserPostConfig.GetCrowdGroupId())
			crowdToastMap[banUserPostConfig.GetCrowdGroupId()] = banUserPostConfig.GetBanReason()
		case channel_play_tab.BanConfigType_BanConfigTypeUser:
			return protocol.NewExactServerError(nil, status.ErrGameHallSendMsgBusinessBan, banUserPostConfig.GetBanReason())
		default:
			log.InfoWithCtx(ctx, "CheckUserIsBannedPost invalid BanConfigType:%v", banUserPostConfig.GetConfigType())
		}
	}

	crowdResults, err := s.getMatchGroupInfo(ctx, uint64(uid), crowdGroupIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "getMatchGroupInfo err: %v, crowdGroupIds:%v", err, crowdGroupIds)
		return protocol.NewExactServerError(nil, status.ErrSys)
	}
	if len(crowdResults) > 0 {
		for id, v := range crowdResults {
			if v == InCrowdGroup && len(crowdToastMap[id]) != 0 {
				crowdToast := crowdToastMap[id]
				return protocol.NewExactServerError(nil, status.ErrGameHallSendMsgBusinessBan, crowdToast)
			}
		}
	}
	return nil
}

func genBanToast(releaseTime int64) string {
	fomatTime := time.Unix(releaseTime, 0).Format("2006-01-02 15:04:05")
	return fmt.Sprintf(banToast, fomatTime)
}

// SendGameImMsg 发送消息
func (s *Server) SendGameImMsg(ctx context.Context, req *game_hall_logic.SendGameImMsgRequest) (*game_hall_logic.SendGameImMsgResponse, error) {
	out := &game_hall_logic.SendGameImMsgResponse{
		BaseResp: new(app.BaseResp),
	}
	log.InfoWithCtx(ctx, "SendGameImMsg req:%s", req.String())
	svcInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	uid := svcInfo.UserID
	tabId, err := decodeChannelPath(ctx, req.GetChannelPath())
	if err != nil {
		log.ErrorWithCtx(ctx, "SendGameImMsg checkChannelPath req:%s svcInfo:%s err:%v", req.String(), svcInfo.String(), err)
		return out, err
	}

	if err = s.checkBanSay(ctx, uid, tabId); err != nil {
		return out, err
	}

	// 生成消息id
	var seqId uint64
	var userInfo *accountGo.User
	switch req.GetRequest().(type) {
	case *game_hall_logic.SendGameImMsgRequest_CancelRequest_:
		seqId = req.GetCancelRequest().GetCancelMsgId()
	case *game_hall_logic.SendGameImMsgRequest_DelRequest_:
		seqId = req.GetDelRequest().GetDelMsgId()
	case *game_hall_logic.SendGameImMsgRequest_JoinTeamRequest_:
		seqId = req.GetJoinTeamRequest().GetTeamMsgId()
		userInfo, err = s.accountCli.GetUserByUid(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "genSenderInfo GetUserByUid failed uid:%d err:%v", uid, err)
			return out, err
		}

	default:
		seqId, err = s.seqgenV2Client.GenerateSequence(ctx, tabId, seqgen.NamespaceGameHall, seqgen.KeyIm, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendGameImMsg GenerateSequence req:%s svcInfo:%s err:%v", req.String(), svcInfo.String(), err)
			return out, err
		}
		userInfo, err = s.accountCli.GetUserByUid(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "genSenderInfo GetUserByUid failed uid:%d err:%v", uid, err)
			return out, err
		}
	}

	if seqId == 0 {
		log.ErrorWithCtx(ctx, "Get SeqId err:0, req:%s", req.String())
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	var (
		msgType      uint32
		msgBytes     []byte
		noPush       bool                // 是否不推送
		noStore      bool                // 是否不存储
		isJoinTeam   bool                // 是否是加入队伍
		needHandleAt bool                // 是否需要处理at
		atUidList    []uint32            // at的用户列表
		atInfo       *general_msg.AtInfo // at的消息内容
		gameImMsg    *game_hall_logic.GameImMsg
		cid          uint32 // 邀请房间的cid
	)
	switch req.GetRequest().(type) {
	case *game_hall_logic.SendGameImMsgRequest_TextRequest_:
		msgType = uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_TEXT)
		msgBytes, err = s.handleTextMsg(ctx, seqId, tabId, userInfo, req, out)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleTextMsg err:%v, uid:%d req:%s", err, uid, req.String())
			return out, err
		}
	case *game_hall_logic.SendGameImMsgRequest_GameScreenshotRequest:
		msgType = uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_GAME_SCREENSHOT)
		err = s.handleGameScreenTextMsg(ctx, seqId, tabId, userInfo, req, out)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleGameScreenTextMsg err:%v, uid:%d req:%s", err, uid, req.String())
			return out, err
		}
		go func() {
			if r := recover(); r != nil {
				log.Errorf("SendGameImMsg handleGameScreenTextMsg panic, err:%v", r)
			}
			asyncCtx, cancel := grpc.NewContextWithInfoTimeout(ctx, 3*time.Second)
			defer cancel()
			s.handleSendMsgFre(asyncCtx, msgType, userInfo, nil)
		}()
		return out, nil
	case *game_hall_logic.SendGameImMsgRequest_CancelRequest_:
		gameImMsg, err = s.generalMsg.GenImCancelMsg(ctx, uid, seqId, req.GetChannelPath())
		if err != nil {
			log.ErrorWithCtx(ctx, "SendGameImMsg GenImCancelMsg err:%+v req:%s", err, req.String())
			return out, err
		}
		if gameImMsg != nil {
			err = s.UpdateGameImMsgSenderInfo(ctx, gameImMsg)
			if err != nil {
				log.ErrorWithCtx(ctx, "SendGameImMsg UpdateGameImMsgSenderInfo err:%+v req:%s", err, req.String())
				return out, err
			}
		}
	case *game_hall_logic.SendGameImMsgRequest_DelRequest_:
		noPush = true
		gameImMsg, err = s.generalMsg.GenImDelMsg(ctx, uid, seqId, req.GetChannelPath())
		if err != nil {
			log.ErrorWithCtx(ctx, "SendGameImMsg GenImDelMsg err:%+v req:%s", err, req.String())
			return out, err
		}
	case *game_hall_logic.SendGameImMsgRequest_AtRequest_:
		needHandleAt = true
		msgType = uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_AT_SOMEONE)
		msgBytes, atUidList, atInfo, err = s.handleAtMsg(ctx, seqId, tabId, userInfo, req, out)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleAtMsg err:%v, uid:%d req:%s", err, uid, req.String())
			return out, err
		}
	case *game_hall_logic.SendGameImMsgRequest_QuoteRequest_:
		needHandleAt = true
		msgType = uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_QUOTE)
		msgBytes, atUidList, atInfo, err = s.handleQuoteMsg(ctx, seqId, tabId, userInfo, req, out)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleQuoteMsg err:%v, uid:%d req:%s", err, uid, req.String())
			return out, err
		}
	case *game_hall_logic.SendGameImMsgRequest_FormTeamRequest_:
		msgType = uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_FORM_TEAM)
		msgBytes, err = s.handleSendTeamMsg(ctx, seqId, tabId, userInfo, req, out)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleSendTeamMsg err:%v, uid:%d req:%s", err, uid, req.String())
			return out, err
		}

	case *game_hall_logic.SendGameImMsgRequest_JoinTeamRequest_:
		isJoinTeam = true
		noStore = true
		gameImMsg, err = s.handleJoinTeamMsg(ctx, seqId, tabId, userInfo, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleJoinTeamMsg err:%v, uid:%d req:%s", err, uid, req.String())
			return out, err
		}

	case *game_hall_logic.SendGameImMsgRequest_InviteRoomRequest_:
		msgType = uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_INVITE_ROOM)
		msgBytes, cid, err = s.handleInviteRoomMsg(ctx, seqId, tabId, userInfo, req, out)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleInviteRoomMsg err:%v, uid:%d req:%s", err, uid, req.String())
			return out, err
		}

	case *game_hall_logic.SendGameImMsgRequest_ExpressionRequest_:
		msgType = uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_EXPRESSION)
		msgBytes, err = s.handleExpressionMsg(ctx, tabId, userInfo, req, out)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleExpressionMsg err:%v, uid:%d req:%s", err, uid, req.String())
			return out, err
		}
	default:
		log.ErrorWithCtx(ctx, "HandleMsg invalid msg type uid:%d req:%s", uid, req.String())
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	if gameImMsg == nil {
		senderInfo, err := s.GenSenderInfo(ctx, map[uint32]*accountGo.User{uid: userInfo}, tabId)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleMsg genSenderInfo failed uid:%d req:%s err:%v", uid, req.String(), err)
			return out, err
		}
		gameImMsg = &game_hall_logic.GameImMsg{
			MsgId:           seqId,
			SenderInfo:      senderInfo[uid],
			MsgType:         msgType,
			SendTime:        time.Now().Unix(),
			MsgStatus:       uint32(game_hall_logic.MsgStatus_MSG_STATUS_NORMAL),
			TabId:           tabId,
			MsgAction:       uint32(game_hall_logic.MsgAction_MSG_ACTION_NORMAL),
			MsgContentBytes: msgBytes,
		}
	}
	gameImMsg.ClientMsgTag = req.GetClientMsgTag()
	out.Msg = gameImMsg
	// 记录消息
	_, err = s.gameHallClient.AddMsgRecord(ctx, &game_hall.AddMsgRecordReq{
		MsgId:    seqId,
		SendTime: gameImMsg.GetSendTime(),
		TabId:    tabId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SendGameImMsg AddMsgRecord req:%s svcInfo:%s err:%v", req.String(), svcInfo.String(), err)
		return out, err
	}

	// 推消息到pushD
	broadcastResp, err := s.pushMsgToPushD(ctx, uid, noPush, noStore, req.GetChannelPath(), gameImMsg)
	if err != nil || broadcastResp.GetStatus().Code != multi_publisher.BroadcastStatusCode_BROADCAST_STATUS_CODE_SUCCESS {
		log.ErrorWithCtx(ctx, "SendGameImMsg Broadcast fail, uid:%d  broadcastReq:%s broadcastResp:%s err:%v",
			uid, req.String(), broadcastResp.String(), err)
		return out, err
	}

	// 发送消息成功，需要做一些推送和记录频率统计和推送
	go func() {
		if r := recover(); r != nil {
			log.Errorf("StatSendMsgAndPush panic, err:%v", r)
		}
		asyncCtx, cancel := grpc.NewContextWithInfoTimeout(ctx, 3*time.Second)
		defer cancel()
		if isJoinTeam {
			_ = s.teamMgr.JoinTeamStatAndPush(asyncCtx, userInfo, gameImMsg)
			// 百灵上报
			s.joinTeamBylinkReport(asyncCtx, userInfo.GetUid(), gameImMsg.GetSenderInfo().GetUid(), gameImMsg.GetTabId(), gameImMsg.GetMsgId())
		} else {
			// 百灵上报
			s.sendMsgBylinkReport(asyncCtx, userInfo.GetUid(), cid, gameImMsg.GetTabId(), gameImMsg.GetMsgType(), gameImMsg.GetMsgId(), req.GetTextRequest().GetText())
		}
		if needHandleAt {
			// 记录at消息、发推送
			_ = s.generalMsg.AddAtRecordAndPush(asyncCtx, seqId, tabId, atUidList, userInfo, atInfo)
		}
		s.handleSendMsgFre(asyncCtx, msgType, userInfo, gameImMsg)

	}()

	log.InfoWithCtx(ctx, "SendGameImMsg success req:%s svcInfo:%s broadcastResp:%s", req.String(), svcInfo.String(), broadcastResp.String())
	return out, nil
}

func (s *Server) handleSendMsgFre(ctx context.Context, msgType uint32, userInfo *accountGo.User, gameImMsg *game_hall_logic.GameImMsg) {
	var err error
	switch msgType {
	case uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_TEXT), uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_AT_SOMEONE),
		uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_QUOTE), uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_EXPRESSION):
		err = s.generalMsg.AddNormalMsgFrequency(ctx, userInfo.GetUid())
	case uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_GAME_SCREENSHOT):
		err = s.generalMsg.AddScreenShotMsgFrequency(ctx, userInfo.GetUid())
	case uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_FORM_TEAM), uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_INVITE_ROOM):
		err = s.teamMgr.IncTeamAndInviteRoomSendMsgFre(ctx, userInfo, gameImMsg)
	default:
		return
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "handleSendMsgFre msgType:%d uid:%d gameImMsg:%s", msgType, userInfo.GetUid(), gameImMsg.String())
	}
}

func (s *Server) pushMsgToPushD(ctx context.Context, uid uint32, noPush, noStore bool, channelPath string, gameImMsg *game_hall_logic.GameImMsg) (
	*multi_publisher.BroadcastResp, error) {
	// 推消息到pushD
	anyMsg, err := anypb.New(protoimpl.X.ProtoMessageV2Of(gameImMsg))
	if err != nil {
		log.ErrorWithCtx(ctx, "SendGameImMsg anypb.New err:%v", err)
		return nil, err

	}
	req := &multi_publisher.BroadcastReq{
		Message: &multi_publisher.Message{
			SeqId:       gameImMsg.GetMsgId(),
			ChannelPath: channelPath,
			Sender: &multi_publisher.Sender{
				Uid: uid,
			},
			Data: anyMsg,
		},
		Opt: &multi_publisher.SendOption{
			PushCmdType: uint32(push.PushMessage_GAME_HALL_MSG_PUSH),
			PushLabel:   push_label.LabelGameHallMsgPush,
			NoPush:      noPush,
			NoStore:     noStore,
		},
	}
	broadcastResp, err := s.multiPublisherClient.Broadcast(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendGameImMsg Broadcast uid:%d  broadcastReq:%s broadcastResp:%s err:%v",
			uid, req.String(), broadcastResp.String(), err)
		return broadcastResp, err
	}
	return broadcastResp, nil
}

func (s *Server) handleTextMsg(ctx context.Context, seqId uint64, tabId uint32, userInfo *accountGo.User,
	req *game_hall_logic.SendGameImMsgRequest, out *game_hall_logic.SendGameImMsgResponse) ([]byte, error) {
	var err error
	var riskToken string
	var riskErr error
	if err := s.generalMsg.CheckNormalMsgFrequency(ctx, userInfo.GetUid()); err != nil {
		return nil, err
	}
	out.BaseResp.ErrInfo, riskToken, riskErr = s.SenderRiskCheck(ctx, userInfo.GetUid(), req.GetBaseReq(),
		uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_TEXT), req.GetTextRequest().GetText(), tabId)

	msgBytes, err := s.generalMsg.GenImTextMsg(ctx, seqId, userInfo, req.GetTextRequest().GetText(), riskToken, tabId)
	if riskErr != nil {
		// 风控拦截，但是需要送审
		log.ErrorWithCtx(ctx, "SendGameImMsg SenderRiskCheck riskErr:%v, uid:%d req:%s", riskErr, userInfo.GetUid(), req.String())
		return nil, riskErr
	} else if err != nil {
		log.ErrorWithCtx(ctx, "HandleMsg GenImTextMsg err:%v, uid:%d req:%s", err, userInfo.GetUid(), req.String())
		return nil, err
	}
	return msgBytes, nil
}

func (s *Server) handleGameScreenTextMsg(ctx context.Context, seqId uint64, tabId uint32, userInfo *accountGo.User,
	req *game_hall_logic.SendGameImMsgRequest, out *game_hall_logic.SendGameImMsgResponse) error {
	var err error

	if err := s.generalMsg.CheckScreenShotMsgFrequency(ctx, userInfo.GetUid(), req.GetGameScreenshotRequest().GetTotalSendNum()); err != nil {
		return err
	}

	screenUrl, err := s.generalMsg.GetImScreenShotUrl(ctx, tabId, userInfo.GetUid(), req.GetGameScreenshotRequest().GetPos())
	if err != nil {
		log.ErrorWithCtx(ctx, "handleGameScreenTextMsg GetImScreenShotUrl err:%v, uid:%d req:%s", err, userInfo.GetUid(), req.String())
		return err
	}
	if len(screenUrl) == 0 {
		log.ErrorWithCtx(ctx, "handleGameScreenTextMsg GetImScreenShotUrl screenUrl is empty, uid:%d req:%s", userInfo.GetUid(), req.String())
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	height := req.GetGameScreenshotRequest().GetHeight()
	width := req.GetGameScreenshotRequest().GetWidth()
	var riskToken string
	var riskErr error
	out.BaseResp.ErrInfo, riskToken, riskErr = s.SenderRiskCheck(ctx, userInfo.GetUid(), req.GetBaseReq(),
		uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_GAME_SCREENSHOT), screenUrl, tabId)

	err = s.generalMsg.GenImScreenShotMsg(ctx, seqId, userInfo, screenUrl, height, width, riskToken, tabId, riskErr)
	if riskErr != nil {
		log.ErrorWithCtx(ctx, "SendGameImMsg SenderRiskCheck riskErr:%v, uid:%d req:%s", riskErr, userInfo.GetUid(), req.String())
		return riskErr
	} else if err != nil {
		log.ErrorWithCtx(ctx, "HandleMsg GenImScreenShotMsg err:%v, uid:%d req:%s", err, userInfo.GetUid(), req.String())
		return err
	}
	return nil
}

func (s *Server) handleAtMsg(ctx context.Context, seqId uint64, tabId uint32, userInfo *accountGo.User,
	req *game_hall_logic.SendGameImMsgRequest, out *game_hall_logic.SendGameImMsgResponse) ([]byte, []uint32, *general_msg.AtInfo, error) {
	var err error
	var riskToken string
	var riskErr error
	if err := s.generalMsg.CheckNormalMsgFrequency(ctx, userInfo.GetUid()); err != nil {
		return nil, nil, nil, err
	}
	atInfo := &general_msg.AtInfo{}
	err = json.Unmarshal([]byte(req.GetAtRequest().GetAtInfoJson()), atInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAtMsg json.Unmarshal failed msgId:%d, req:%s, err:%v", seqId, req.String(), err)
		return nil, nil, nil, err
	}

	out.BaseResp.ErrInfo, riskToken, riskErr = s.SenderRiskCheck(ctx, userInfo.GetUid(), req.GetBaseReq(),
		uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_AT_SOMEONE), atInfo.MessageContent, tabId)

	msgBytes, atUidList, err := s.generalMsg.GenAtMsg(ctx, seqId, userInfo, req, atInfo, riskToken, tabId)
	if riskErr != nil {
		log.ErrorWithCtx(ctx, "SendGameImMsg SenderRiskCheck riskErr:%v, uid:%d req:%s", riskErr, userInfo.GetUid(), req.String())
		return nil, nil, nil, riskErr
	} else if err != nil {
		log.ErrorWithCtx(ctx, "HandleMsg GenAtMsg err:%v, uid:%d req:%s", err, userInfo.GetUid(), req.String())
		return nil, nil, nil, err
	} else {
		return msgBytes, atUidList, atInfo, nil
	}
}

func (s *Server) handleQuoteMsg(ctx context.Context, seqId uint64, tabId uint32, userInfo *accountGo.User,
	req *game_hall_logic.SendGameImMsgRequest, out *game_hall_logic.SendGameImMsgResponse) ([]byte, []uint32, *general_msg.AtInfo, error) {
	var err error
	var riskToken string
	var riskErr error
	if err := s.generalMsg.CheckNormalMsgFrequency(ctx, userInfo.GetUid()); err != nil {
		return nil, nil, nil, err
	}
	atInfo := &general_msg.AtInfo{
		MessageContent: req.GetQuoteRequest().GetText(),
	}
	if len(req.GetQuoteRequest().GetAtUidList()) > 0 {
		err = json.Unmarshal([]byte(req.GetQuoteRequest().GetText()), atInfo)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleAtMsg json.Unmarshal failed msgId:%d, req:%s, err:%v", seqId, req.String(), err)
			return nil, nil, nil, err
		}
	}
	quoteTimelineMsg, err := s.generalMsg.GetTimeLineMsgById(ctx, req.GetChannelPath(), req.GetQuoteRequest().GetQuoteMsgId())
	if err != nil {
		log.ErrorWithCtx(ctx, "handleQuoteMsg GetTimeLineMsgById err:%+v req:%s", err, req.String())
		return nil, nil, nil, err
	}
	if quoteTimelineMsg != nil {
		err = s.UpdateGameImMsgSenderInfo(ctx, quoteTimelineMsg)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleQuoteMsg UpdateGameImMsgSenderInfo err:%+v req:%s", err, req.String())
			return nil, nil, nil, err
		}
	}

	out.BaseResp.ErrInfo, riskToken, riskErr = s.SenderRiskCheck(ctx, userInfo.GetUid(), req.GetBaseReq(),
		uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_QUOTE), atInfo.MessageContent, tabId)

	msgBytes, atUidList, err := s.generalMsg.GenQuoteMsg(ctx, seqId, userInfo, req, atInfo, quoteTimelineMsg, riskToken, tabId)
	if riskErr != nil {
		log.ErrorWithCtx(ctx, "handleQuoteMsg SenderRiskCheck err:%v, uid:%d req:%s", riskErr, userInfo.GetUid(), req.String())
		return nil, nil, nil, riskErr
	} else if err != nil {
		log.ErrorWithCtx(ctx, "SendGameImMsg GenQuoteMsg err:%+v req:%s", err, req.String())
		return msgBytes, atUidList, atInfo, err
	} else {
		return msgBytes, atUidList, atInfo, nil
	}
}

func (s *Server) handleExpressionMsg(ctx context.Context, tabId uint32, userInfo *accountGo.User,
	req *game_hall_logic.SendGameImMsgRequest, out *game_hall_logic.SendGameImMsgResponse) ([]byte, error) {
	var err error
	var riskToken string
	if err := s.generalMsg.CheckNormalMsgFrequency(ctx, userInfo.GetUid()); err != nil {
		return nil, err
	}
	screenUrl := req.GetExpressionRequest().GetUrl()
	out.BaseResp.ErrInfo, riskToken, err = s.SenderRiskCheck(ctx, userInfo.GetUid(), req.GetBaseReq(),
		uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_EXPRESSION), screenUrl, tabId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendGameImMsg SenderRiskCheck err:%v, uid:%d req:%s, riskToken:%s",
			err, userInfo.GetUid(), req.String(), riskToken)
		return nil, err
	}

	marshalBytes, err := s.generalMsg.GenExpressionMsg(ctx, req.GetExpressionRequest())
	if err != nil {
		log.ErrorWithCtx(ctx, "handleExpressionMsg GenImScreenShotMsg err:%v, uid:%d req:%s,riskToken:%s",
			err, userInfo.GetUid(), req.String(), riskToken)
		return nil, err
	}
	return marshalBytes, nil
}

func (s *Server) handleJoinTeamMsg(ctx context.Context, msgId uint64, tabId uint32, userInfo *accountGo.User,
	req *game_hall_logic.SendGameImMsgRequest) (*game_hall_logic.GameImMsg, error) {

	gameImMsg, tErr := s.generalMsg.GetTimeLineMsgById(ctx, req.GetChannelPath(), msgId)
	if tErr != nil {
		log.ErrorWithCtx(ctx, "handleJoinTeamMsg GetTimeLineMsgById err:%+v req:%s", tErr, req.String())
		return nil, tErr
	}
	if gameImMsg == nil {
		log.ErrorWithCtx(ctx, "handleJoinTeamMsg GetTimeLineMsgById gameImMsg is nil, msgId:%d, tabId:%d,req:%s", msgId, tabId, req.String())
		return nil, fmt.Errorf("handleJoinTeamMsg GetTimeLineMsgById teamTimelineMsg is nil, req:%s", req.String())
	}

	// 加入队伍
	_, err := s.gameHallClient.JoinGameHallTeam(ctx, &game_hall.JoinGameHallTeamReq{
		MsgId:   gameImMsg.GetMsgId(),
		TabId:   gameImMsg.GetTabId(),
		SendUid: gameImMsg.GetSenderInfo().GetUid(),
		JoinUid: userInfo.GetUid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "JoinGameHallTeam fail, err:%v, gameImMsg:%+v", gameImMsg)
		return nil, err
	}

	// 更新组队消息
	msgBytes, err := s.teamMgr.GenJoinTeamMsgBytes(ctx, userInfo.GetUid(), gameImMsg.GetMsgId(), tabId)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleJoinTeamMsg GenJoinTeamMsgBytes err:%+v req:%s", err, req.String())
		return nil, err
	}
	gameImMsg.MsgContentBytes = msgBytes
	// 举手报名更新消息，更新消息状态，客户端只更新数据，不新插入
	gameImMsg.MsgStatus = uint32(game_hall_logic.MsgStatus_MSG_STATUS_UPDATE)

	return gameImMsg, nil
}

func (s *Server) handleSendTeamMsg(ctx context.Context, msgId uint64, tabId uint32, userInfo *accountGo.User,
	req *game_hall_logic.SendGameImMsgRequest, out *game_hall_logic.SendGameImMsgResponse) ([]byte, error) {

	var err error
	out.BaseResp.ErrInfo, _, err = s.SenderRiskCheck(ctx, userInfo.GetUid(), req.GetBaseReq(),
		uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_FORM_TEAM), "", tabId)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleSendTeamMsg SenderRiskCheck err:%v, uid:%d req:%s", err, userInfo.GetUid(), req.String())
		return nil, err
	}

	// 检查是否可以发送
	isReachMaxSendLimit, err := s.teamMgr.CheckSendTeamMsgFre(ctx, userInfo.GetUid())
	if err != nil {
		return nil, err
	}
	if isReachMaxSendLimit {
		log.InfoWithCtx(ctx, "handleSendTeamMsg CheckSendTeamFre reach max limit, uid:%d req:%s", userInfo.GetUid(), req.String())
		return nil, protocol.NewExactServerError(nil, status.ErrGameHallSendMsgLimit)
	}

	// 添加到消息列表里面，后续筛选这些数据
	_, err = s.gameHallClient.AddTeamAndInviteMsg(ctx, &game_hall.AddTeamAndInviteMsgReq{
		MsgId: msgId,
		TabId: tabId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddTeamAndInviteMsg fail, err:%v, msgId:%d, tabId:%d", msgId, tabId)
		return nil, err
	}

	msgBytes, err := s.teamMgr.GenSendTeamMsgBytes(ctx, tabId)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleSendTeamMsg GenSendTeamMsgBytes err:%+v req:%s", err, req.String())
		return nil, err
	}
	return msgBytes, nil
}

func (s *Server) handleInviteRoomMsg(ctx context.Context, msgId uint64, tabId uint32, userInfo *accountGo.User,
	req *game_hall_logic.SendGameImMsgRequest, out *game_hall_logic.SendGameImMsgResponse) ([]byte, uint32, error) {
	var err error
	out.BaseResp.ErrInfo, _, err = s.SenderRiskCheck(ctx, userInfo.GetUid(), req.GetBaseReq(),
		uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_INVITE_ROOM), "", tabId)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleInviteRoomMsg SenderRiskCheck err:%v, uid:%d, tabId:%d, req:%s", err, userInfo.GetUid(), tabId, req.String())
		return nil, 0, err
	}

	// 检查是否可以发送
	isReachMaxSendLimit, _ := s.teamMgr.CheckSendInviteRoomMsgFre(ctx, userInfo.GetUid())
	if isReachMaxSendLimit {
		log.InfoWithCtx(ctx, "GenInviteRoomMsg reach max limit filter, uid:%d tabId:%d", userInfo.GetUid(), tabId)
		return nil, 0, protocol.NewExactServerError(nil, status.ErrGameHallSendMsgLimit)
	}
	cid, err := s.teamMgr.CheckSendInviteRoomCond(ctx, userInfo.GetUid(), tabId)
	if err != nil {
		return nil, 0, err
	}

	// 添加到消息列表里面，后续筛选这些数据
	_, err = s.gameHallClient.AddTeamAndInviteMsg(ctx, &game_hall.AddTeamAndInviteMsgReq{
		MsgId: msgId,
		TabId: tabId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddTeamAndInviteMsg fail, err:%v, msgId:%d, tabId:%d", msgId, tabId)
		return nil, cid, err
	}

	msgBytes, err := s.teamMgr.GenInviteRoomMsg(ctx, cid, tabId)
	if err != nil {
		return nil, cid, err
	}
	return msgBytes, cid, nil
}

// 举手报名埋点上报
func (s *Server) joinTeamBylinkReport(ctx context.Context, uid, targetUid, tabId uint32, msgId uint64) {
	byLinkKV := map[string]interface{}{
		"uid":        uid,
		"target_uid": targetUid, // 组队发起人
		"tab_id":     tabId,
		"message_id": msgId,
	}
	err := bylink.TrackMap(ctx, uint64(uid), "team_hall_team_up_log", byLinkKV, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "joinTeamBylinkReport report fail, uid:%d, targetUid:%d, tabId:%d, msgId:%d", uid, targetUid, tabId, msgId)
	}
	log.InfoWithCtx(ctx, "joinTeamBylinkReport report success, uid:%d, targetUid:%d, tabId:%d, msgId:%d", uid, targetUid, tabId, msgId)
	bylink.Flush()
}

// 发消息埋点上报
func (s *Server) sendMsgBylinkReport(ctx context.Context, uid, roomId, tabId, msgType uint32, msgId uint64, content string) {
	//switch msgType {
	//case uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_TEXT), uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_AT_SOMEONE),
	//	uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_QUOTE), uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_EXPRESSION),
	//	uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_GAME_SCREENSHOT), uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_FORM_TEAM),
	//	uint32(game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_INVITE_ROOM):
	//
	//default:
	//	return
	//}

	byLinkKV := map[string]interface{}{
		"uid":        uid,
		"room_id":    roomId,
		"tab_id":     tabId,
		"content":    content, // 记录消息的内容。除文本消息类型外，其他的不用传内容
		"me_type":    msgType,
		"message_id": msgId,
	}
	err := bylink.TrackMap(ctx, uint64(uid), "team_hall_message_log", byLinkKV, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendMsgBylinkReport report fail, uid:%d, roomId:%d, tabId:%d, msgId:%d, "+
			"msgType:%d, content:%s", uid, roomId, tabId, msgId, msgType, content)
	}
	log.InfoWithCtx(ctx, "sendMsgBylinkReport report success, uid:%d, roomId:%d, tabId:%d, msgId:%d, "+
		"msgType:%d, content:%s", uid, roomId, tabId, msgId, msgType, content)
	bylink.Flush()
}
