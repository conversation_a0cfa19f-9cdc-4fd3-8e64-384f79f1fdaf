package server

import (
	"context"
	"fmt"
	accountGo "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/log"
	game_hall_logic "golang.52tt.com/protocol/app/game-hall-logic"
	friendolgo "golang.52tt.com/protocol/services/friend-ol-svr-go"
	userOnlinePb "golang.52tt.com/protocol/services/user-online"
	"golang.52tt.com/services/game-hall-logic/internal/mgr/team_mgr"
)

func (s *Server) UpdateGameImMsgSenderInfo(ctx context.Context, gameImMsg *game_hall_logic.GameImMsg) error {
	if gameImMsg == nil {
		return nil
	}

	userInfoMap, err := s.accountCli.GetUsersMap(ctx, []uint32{gameImMsg.GetSenderInfo().GetUid()})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateGameImMsgSenderInfo err:%v, gameImMsg:%s", err, gameImMsg.String())
		return err
	}
	gameImUserInfoMap, err1 := s.GenSenderInfo(ctx, userInfoMap, gameImMsg.GetTabId())
	if err1 != nil {
		log.ErrorWithCtx(ctx, "UpdateGameImMsgSenderInfo GenSenderInfo err:%v, gameImMsg:%s", err1, gameImMsg.String())
		return err1
	}

	if info, ok := gameImUserInfoMap[gameImMsg.GetSenderInfo().GetUid()]; ok {
		gameImMsg.SenderInfo = info
	}
	return nil
}

func (s *Server) GenSenderInfo(ctx context.Context, userInfoMap map[uint32]*accountGo.User, tabId uint32) (map[uint32]*game_hall_logic.GameImUserInfo, error) {
	if len(userInfoMap) == 0 {
		return nil, nil
	}
	uidList := make([]uint32, 0, len(userInfoMap))
	for uid := range userInfoMap {
		uidList = append(uidList, uid)
	}
	gameCardInfoMap, err := s.gameCardClient.BatGetGameCardMap(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "genSenderInfo BatGetGameCardMap failed uid:%v err:%v", uidList, err)
		return nil, err
	}
	tabInfo := s.localCache.GetTabInfoById(tabId)
	if tabInfo == nil {
		return nil, fmt.Errorf("genSenderInfo GetTabById failed tabId:%d", tabId)
	}
	gameCardConfInfo := s.localCache.GetGameCardConfById(tabInfo.GetGameInfo().GetGameCardId())

	onlineInfoMap, err1 := s.userOlClient.BatchGetLatestOnlineInfoMap(ctx, uidList)
	if err1 != nil {
		log.ErrorWithCtx(ctx, "genSenderInfo GetLatestOnlineInfo failed uidList:%v err:%v", uidList, err1)
		return nil, err1
	}

	stealthResp, err1 := s.friendOlGoClient.GetStealthInfo(ctx, &friendolgo.GetStealthInfoReq{
		UidList: uidList,
	})
	if err1 != nil {
		log.ErrorWithCtx(ctx, "genSenderInfo GetStealthInfo failed uidList:%v err:%v", uidList, err1)
		return nil, err1
	}

	gameUserInfoMap := make(map[uint32]*game_hall_logic.GameImUserInfo, len(userInfoMap))
	for _, userInfo := range userInfoMap {
		gameCardInfo := gameCardInfoMap[userInfo.GetUid()]
		onlineInfo := onlineInfoMap[userInfo.GetUid()]
		gameUserInfoMap[userInfo.GetUid()] = &game_hall_logic.GameImUserInfo{
			Uid:          userInfo.GetUid(),
			Nickname:     userInfo.GetNickname(),
			GameCardInfo: team_mgr.GenGameCardInfo(tabInfo, gameCardInfo, gameCardConfInfo),
			UserStatus:   genUserStatusInfo(onlineInfo, stealthResp.GetStealthInfoMap()[userInfo.GetUid()]),
			Account:      userInfo.GetUsername(),
			Sex:          uint32(userInfo.GetSex()),
		}
	}

	return gameUserInfoMap, nil
}

func genUserStatusInfo(onlineStatus *userOnlinePb.OnlineInfo, stealthInfo *friendolgo.StealthInfo) (res uint32) {

	if stealthInfo != nil && stealthInfo.GetStealthType() == friendolgo.StealthType_STEALTH_TYPE_ON {
		return uint32(userOnlinePb.OnlineType_ONLINE_TYPE_OFFLINE)
	}
	if onlineStatus == nil || onlineStatus.GetOnlineType() != userOnlinePb.OnlineType_ONLINE_TYPE_ONLINE {
		res = uint32(userOnlinePb.OnlineType_ONLINE_TYPE_OFFLINE)
	} else {
		res = uint32(userOnlinePb.OnlineType_ONLINE_TYPE_ONLINE)
	}

	return
}
