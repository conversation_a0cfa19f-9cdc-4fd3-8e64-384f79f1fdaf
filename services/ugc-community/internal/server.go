package internal

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"golang.52tt.com/services/ugc-community/internal/mgr/guide_task"
	guide_task_impl "golang.52tt.com/services/ugc-community/internal/mgr/guide_task/impl"
	"golang.52tt.com/services/ugc-community/internal/mgr/subject"
	subject_impl "golang.52tt.com/services/ugc-community/internal/mgr/subject/impl"

	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/services/ugc-community/internal/config/ttconfig"
	"golang.52tt.com/services/ugc-community/internal/event/eventlink"
	"golang.52tt.com/services/ugc-community/internal/infra/db"
	"golang.52tt.com/services/ugc-community/internal/mgr/attitude"
	attitude_impl "golang.52tt.com/services/ugc-community/internal/mgr/attitude/impl"
	"golang.52tt.com/services/ugc-community/internal/mgr/comment"
	"golang.52tt.com/services/ugc-community/internal/mgr/post"
	post_impl "golang.52tt.com/services/ugc-community/internal/mgr/post/impl"
	"golang.52tt.com/services/ugc-community/internal/mgr/topic"
	topic_impl "golang.52tt.com/services/ugc-community/internal/mgr/topic/impl"
	"golang.52tt.com/services/ugc-community/internal/mgr/user_post"
	user_post_impl "golang.52tt.com/services/ugc-community/internal/mgr/user_post/impl"
)

type StartConfig struct {
	Mongo       *config.MongoConfig       `json:"mongo"`
	RedisConfig *redisConnect.RedisConfig `json:"redis"`

	PostKafka           *config.KafkaConfig `json:"post_kafka"`
	TopicKafka          *config.KafkaConfig `json:"topic_kafka"`
	CommentKafka        *config.KafkaConfig `json:"comment_kafka"`
	AttitudeKafka       *config.KafkaConfig `json:"attitude_kafka"`
	OfficialHandleKafka *config.KafkaConfig `json:"official_handle_kafka"`
	SubjectKafka        *config.KafkaConfig `json:"subject_kafka"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	s := &Server{}

	err := ttconfig.InitUgcCommunityConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "InitUgcCommunityConfig err: %v", err)
		return nil, err
	}

	mongoDB, err := db.NewMongoDB(ctx, cfg.Mongo)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewMongoDB cfg(%+v) err: %v", cfg.Mongo, err)
		return nil, err
	}

	redisClient, err := redisConnect.NewClient(ctx, cfg.RedisConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "init redis fail, err: %v", err)
		return nil, err
	}

	eventPublisher, err := eventlink.NewEventPublisher(ctx, cfg.PostKafka, cfg.CommentKafka, cfg.AttitudeKafka,
		cfg.OfficialHandleKafka, cfg.TopicKafka, cfg.SubjectKafka)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewEventPublisher cfg(%+v) err: %v", cfg, err)
		return nil, err
	}

	postMgr, err := post_impl.NewManager(ctx, mongoDB, eventPublisher)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewManager err: %v", err)
		return nil, err
	}

	attitudeMgr, err := attitude_impl.NewManager(ctx, mongoDB, eventPublisher)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer attitude_impl.NewManager err: %v", err)
		return nil, err
	}

	userPostMgr, err := user_post_impl.NewManager(ctx, mongoDB)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer user_post_impl.NewManager err: %v", err)
		return nil, err
	}

	commentMgr, err := comment.NewCommentManager(ctx, mongoDB, redisClient, eventPublisher)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer comment.NewCommentManager err: %v", err)
		return nil, err
	}

	topicMgr, err := topic_impl.NewManager(ctx, mongoDB, eventPublisher)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer topic_impl.NewManager err: %v", err)
		return nil, err
	}

	// 百灵数据统计 初始化
	bylinkCollect, err := bylink.NewKfkCollector()
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to new kfk collector: %v", err)
		return s, err
	}
	bylink.InitGlobalCollector(bylinkCollect)

	subjectMgr, err := subject_impl.NewManager(ctx, mongoDB, eventPublisher)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer subject.NewManager err: %v", err)
		return nil, err
	}

	guideTaskMgr, err := guide_task_impl.NewManager(redisClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer guide_task.NewManager err: %v", err)
		return nil, err
	}

	s.mongoDB = mongoDB

	s.postMgr = postMgr
	s.topicMgr = topicMgr
	s.commentMgr = commentMgr
	s.attitudeMgr = attitudeMgr
	s.userPostMgr = userPostMgr
	s.subjectMgr = subjectMgr
	s.guideTaskMgr = guideTaskMgr

	return s, nil
}

type Server struct {
	mongoDB *db.MongoDB

	postMgr      post.Manager
	topicMgr     topic.Manager
	attitudeMgr  attitude.Manager
	userPostMgr  user_post.Manager
	commentMgr   comment.IManager
	subjectMgr   subject.Manager
	guideTaskMgr guide_task.Manager
}

func (s *Server) ShutDown() {
	if s.mongoDB != nil {
		_ = s.mongoDB.Close(context.Background())
	}
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}
