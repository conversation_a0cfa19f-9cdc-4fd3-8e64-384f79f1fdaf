package entity

import (
	"fmt"
	pb "golang.52tt.com/protocol/services/ugc-community"
	"time"
)

type AigcBizPost struct {
	ID             string `bson:"_id"`
	CreatedAt      int64  `bson:"created_at"`
	Uid            uint32 `bson:"uid"`
	RelateRoleId   uint32 `bson:"relate_role_id"`
	IsExposed      bool   `bson:"is_exposed"`
	ForceInsertPos uint32 `bson:"force_insert_pos"`
	HasAIComment   bool   `bson:"has_ai_comment"`
	HasChatRecord  bool   `bson:"has_chat_record"`
}

type PostSearchOption struct {
	StartTime     int64
	EndTime       int64
	Uid           uint32
	BizState      pb.AigcPostBizState
	Cursor        string
	RelateRoleId  uint32
	HasChatRecord pb.BoolFilter
	HasAIComment  pb.BoolFilter
	PostId        string
}

func (o *PostSearchOption) String() string {
	if o == nil {
		return "opts: nil"
	}
	return fmt.Sprintf("opts: StartTime:%s EndTime:%s BizState:%s Cursor:%s RelateRoleId:%d",
		time.Unix(o.StartTime, 0).Format("2006-01-02"), time.Unix(o.EndTime, 0).Format("2006-01-02"),
		o.BizState.String(), o.Cursor, o.RelateRoleId)
}

type PostBizStateTable struct {
	Id              string         `bson:"_id"`
	PostId          string         `bson:"post_id"`
	ForceInsertPos  uint32         `bson:"force_insert_pos"`
	BizType         pb.PostBizType `bson:"biz_type"`
	IsExposed       bool           `bson:"is_exposed"`
	InsertSubjectId string         `bson:"insert_subject_id"`
}

type PostBizStateSearchOption struct {
	BizState pb.AigcPostBizState
	BizType  pb.PostBizType
	PostIds  []string
}

func (o *PostBizStateSearchOption) String() string {
	if o == nil {
		return "opts: nil"
	}
	return fmt.Sprintf("opts: BizState:%s BizType:%s PostIds:%v ", o.BizState.String(), o.BizType.String(), o.PostIds)
}

type BizPostReportData struct {
	InsertNum int    `json:"insert_num"`
	Shield    int    `json:"shield"`
	SubjectId string `json:"subject_id"`
}
