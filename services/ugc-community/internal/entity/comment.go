package entity

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	pb "golang.52tt.com/protocol/services/ugc-community"
	"time"
)

type Comment struct {
	ID           primitive.ObjectID   `bson:"_id"`
	PostId       string               `bson:"post_id"`
	ParentId     string               `bson:"parent_id"`      // 父评论id
	RootParentId string               `bson:"root_parent_id"` // 根评论id
	ParentUid    uint32               `bson:"parent_uid"`     // 父评论用户id，冗余存储方便查询
	UserId       uint32               `bson:"user_id"`
	Content      string               `bson:"content"`
	Likes        uint32               `bson:"likes"`        // 点赞数，只有根的评论才需要记录展示
	ReplyCount   uint32               `bson:"reply_count"`  // 回复评论数，只有根的评论才需要记录展示
	AuditStatus  pb.AuditResult       `bson:"audit_status"` // 审核状态
	CreateTime   time.Time            `bson:"create_time"`
	PostCreateTs int64                `bson:"post_create_ts"` // 帖子创建时间戳，用于计算评论排序
	SendType     pb.CommentEntityType `bson:"send_type"`      // 评论发送者类型
	ReplyType    pb.CommentEntityType `bson:"reply_type"`     // 评论回复者类型
	RoleId       uint32               `bson:"role_id"`        // 角色id
	ParentRoleId uint32               `bson:"parent_role_id"` // 父评论角色id
	Origin       pb.CommentOrigin     `bson:"origin"`         // 评论来源
}

func NewObjectIDFromHex(id string) primitive.ObjectID {
	if id == "" {
		return primitive.NilObjectID
	}
	_id, _ := primitive.ObjectIDFromHex(id)
	return _id
}

func NewComment(item *pb.CommentSendRequest) *Comment {
	if item == nil || item.PostId == "" {
		return nil
	}
	comment := &Comment{
		ID:           primitive.NewObjectID(),
		PostId:       item.GetPostId(),
		ParentId:     item.GetParentId(),
		RootParentId: item.GetRootParentId(),
		//ParentUid:    item.GetReplyUserId(),
		//UserId:       item.GetUid(),
		Content:      item.GetContent(),
		Likes:        0,
		ReplyCount:   0,
		AuditStatus:  pb.AuditResult_AUDIT_RESULT_PASS,
		CreateTime:   time.Now(),
		PostCreateTs: item.GetPostTime(),
		SendType:     item.GetSendEntity().GetType(),
		ReplyType:    item.GetReplyEntity().GetType(),
		Origin:       item.GetOrigin(),
	}
	switch item.GetSendEntity().GetType() {
	case pb.CommentEntityType_COMMENT_ENTITY_TYPE_USER:
		comment.UserId = item.GetSendEntity().GetId()
	case pb.CommentEntityType_COMMENT_ENTITY_TYPE_ROLE:
		comment.RoleId = item.GetSendEntity().GetId()
	}

	switch item.GetReplyEntity().GetType() {
	case pb.CommentEntityType_COMMENT_ENTITY_TYPE_USER:
		comment.ParentUid = item.GetReplyEntity().GetId()
	case pb.CommentEntityType_COMMENT_ENTITY_TYPE_ROLE:
		comment.ParentRoleId = item.GetReplyEntity().GetId()
	}

	return comment
}
