package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/ugc-community"
	"golang.52tt.com/protocol/services/ugc-community/event"
)

type AttachmentList []Attachment

// Post 帖子详情
type Post struct {
	ID primitive.ObjectID `bson:"_id,omitempty"`

	CreatedAt time.Time `bson:"created_at"`
	UpdatedAt time.Time `bson:"updated_at"`

	Uid    uint32        `bson:"uid"`
	Type   pb.PostType   `bson:"type"`
	State  pb.PostState  `bson:"state"`
	Status pb.PostStatus `bson:"status"`
	Origin pb.PostOrigin `bson:"origin"`

	Content     string         `bson:"content"`
	Attachments AttachmentList `bson:"attachments"`

	BizData PostBizData `bson:"biz_data"`

	AuditAt     time.Time      `bson:"audit_at"`
	AuditResult pb.AuditResult `bson:"audit_result"`

	CommentCount  uint32 `bson:"comment_count"`
	AttitudeCount uint32 `bson:"attitude_count"`

	TopicIDList []string `bson:"topic_id_list"`
}

func (p *Post) Id() string {
	return p.ID.Hex()
}

func (p *Post) UpdateAuditResult(result pb.AuditResult) error {
	if p.Status == pb.PostStatus_POST_STATUS_DELETED {
		return protocol.NewExactServerError(nil, status.ErrUgcCommunityPostNotFound)
	}
	if result != pb.AuditResult_AUDIT_RESULT_PASS && result != pb.AuditResult_AUDIT_RESULT_REJECT {
		return protocol.NewExactServerError(nil, status.ErrUgcCommunityPostInvalidCensorResult)
	}

	p.AuditResult = result
	return nil
}

func (post *Post) Event(isShield bool) *event.PostEvent_Post {
	ev := &event.PostEvent_Post{
		Id:          post.ID.Hex(),
		Uid:         post.Uid,
		PublishedAt: post.CreatedAt.Unix(),
		Type:        post.Type,
		State:       post.State,
		Origin:      post.Origin,
		Content:     post.Content,
		Attachments: make([]*pb.Attachment, 0, len(post.Attachments)),
		BizData: &pb.PostBizData{
			Type:   post.BizData.Type,
			Format: post.BizData.Format,
			Bin:    post.BizData.Bin,
		},
		AuditAt:     post.AuditAt.Unix(),
		AuditResult: post.AuditResult,
		TopicIdList: post.TopicIDList,
		IsShield:    isShield,
	}
	for _, attachment := range post.Attachments {
		ev.Attachments = append(ev.Attachments, &pb.Attachment{
			Type: attachment.Type,

			Key: attachment.Key,
			Url: attachment.Url,
		})
	}

	return ev
}

func NewPost(post pb.CreatePostRequest_Post) (*Post, error) {
	var id primitive.ObjectID
	if post.GetId() == "" {
		id = primitive.NewObjectID()
	} else {
		var err error
		if id, err = primitive.ObjectIDFromHex(post.GetId()); err != nil {
			return nil, err
		}
	}

	now := time.Now()

	info := &Post{
		ID: id,

		CreatedAt: now,
		UpdatedAt: now,

		Uid:    post.GetUid(),
		Type:   post.GetType(),
		State:  post.GetState(),
		Origin: post.GetOrigin(),

		Content:     post.GetContent(),
		Attachments: make([]Attachment, 0, len(post.GetAttachments())),

		BizData: PostBizData{
			Type:   post.GetBizData().GetType(),
			Format: post.GetBizData().GetFormat(),
			Bin:    post.GetBizData().GetBin(),
		},

		AuditAt:     time.Unix(post.GetAuditAt(), 0),
		AuditResult: post.GetAuditResult(),

		TopicIDList: post.GetTopicIdList(),
	}
	for _, attachment := range post.GetAttachments() {
		info.Attachments = append(info.Attachments, Attachment{
			Type: attachment.GetType(),

			Key: attachment.GetKey(),
			Url: attachment.GetUrl(),
		})
	}

	return info, nil
}

type PostList []*Post

func (list PostList) ID() []primitive.ObjectID {
	idList := make([]primitive.ObjectID, 0, len(list))
	for _, post := range list {
		idList = append(idList, post.ID)
	}

	return idList
}

func (list PostList) Event() []*event.PostEvent_Post {
	eventList := make([]*event.PostEvent_Post, 0, len(list))
	for _, post := range list {
		eventList = append(eventList, post.Event(false))
	}

	return eventList
}

// Attachment 附件详情
type Attachment struct {
	Type pb.Attachment_Type `bson:"type"`

	Key string `bson:"key"`
	Url string `bson:"url"`
}

// PostBizData 帖子业务数据
type PostBizData struct {
	Type   pb.PostBizType        `bson:"type"`
	Format pb.PostBizData_Format `bson:"format"`
	Bin    []byte                `bson:"bin"`
}
