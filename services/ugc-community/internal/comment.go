package internal

import (
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/ugc-community"
	eventpb "golang.52tt.com/protocol/services/ugc-community/event"
	"golang.52tt.com/services/ugc-community/internal/entity"
	"golang.org/x/net/context"
)

func (s *Server) CommentSend(ctx context.Context, req *pb.CommentSendRequest) (*pb.CommentSendResponse, error) {
	log.InfoWithCtx(ctx, "CommentSend req:%v", req)
	out := &pb.CommentSendResponse{}
	commentData := entity.NewComment(req)
	if commentData == nil {
		log.WarnWithCtx(ctx, "CommentSend NewComment empty, req:%v", req)
		return out, nil
	}
	newCommentData, isAIRootComment, err := s.commentMgr.CommentSend(ctx, commentData)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommentSend CommentSend err:%v, req:%v", err, req)
		return out, err
	}

	var postCommentCount uint32
	if isAIRootComment {
		// 根评论为ai评论的评论数额外计数
		aiCommentCnt, err := s.commentMgr.IncrPostAICommentCnt(ctx, newCommentData.PostId, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "CommentSend IncrPostAICommentCnt err:%v, req:%v", err, req)
		}
		if aiCommentCnt == 1 {
			err = s.postMgr.SetHasAICommentStatus(ctx, newCommentData.PostId, true)
			if err != nil {
				log.ErrorWithCtx(ctx, "CommentSend SetHasAICommentStatus err:%v", err)
			}
		}
	} else {
		postCommentCount, err = s.postMgr.IncrPostComment(ctx, newCommentData.PostId, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "CommentSend IncrPostComment err:%v, req:%v", err, req)
		}
	}

	post, err := s.postMgr.GetPost(ctx, newCommentData.PostId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommentSend GetPost err:%v, req:%v", err, req)
	}
	if postCommentCount == 0 && post != nil {
		postCommentCount = post.CommentCount
	}
	// ai相关评论数不影响推荐打分，但middle服务需要消费用户回复角色的事件
	_ = s.commentMgr.SendCommentEvent(ctx, newCommentData, postCommentCount, eventpb.CommentEvent_ACTION_ADD)

	// 记录用户每天被ai评论的帖子数和每条帖子的ai评论数
	if newCommentData.SendType == pb.CommentEntityType_COMMENT_ENTITY_TYPE_ROLE && post != nil &&
		newCommentData.Origin != pb.CommentOrigin_COMMENT_ORIGIN_OFFICIAL { // 官方评论不计入次数消耗
		err = s.commentMgr.IncrUserAICommentReplyCnt(ctx, post.Uid, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "CommentSend IncrUserAICommentReplyCnt err:%v, req:%v, uid:%d", err, req, post.Uid)
		}
		err = s.commentMgr.IncrPostAICommentReplyCnt(ctx, newCommentData.PostId)
		if err != nil {
			log.ErrorWithCtx(ctx, "CommentSend IncrPostAICommentReplyCnt err:%v, req:%v, psotId:%s", err, req, newCommentData.PostId)
		}
	}
	return out, nil
}

func (s *Server) CommentFetch(ctx context.Context, req *pb.CommentFetchRequest) (*pb.CommentFetchResponse, error) {
	out := &pb.CommentFetchResponse{}

	datas, isLoadFinish, err := s.commentMgr.CommentFetch(ctx, req.GetLastCommentId(), req.GetPostId())
	if err != nil {
		log.ErrorWithCtx(ctx, "CommentFetch CommentFetch err:%v, req:%v", err, req)
		return out, err
	}
	var aiComments []*pb.CommentItem
	if req.GetFetchAiComment() {
		aiComments, err = s.commentMgr.AICommentFetch(ctx, req.GetPostId())
		if err != nil {
			log.ErrorWithCtx(ctx, "CommentFetch AICommentFetch err:%v, req:%v", err, req)
			return out, err
		}
	}
	out.IsLoadFinish = isLoadFinish
	out.Comments = append(aiComments, datas...)
	log.InfoWithCtx(ctx, "CommentFetch req:%v, isLoadFinish:%v, len(data):%d, len(aiComments):%d", req, isLoadFinish, len(datas), len(aiComments))
	return out, nil
}

func (s *Server) CommentDelete(ctx context.Context, req *pb.CommentDeleteRequest) (*pb.CommentDeleteResponse, error) {
	log.InfoWithCtx(ctx, "CommentDelete req:%v", req)
	data, isAIRootComment, err := s.commentMgr.CommentDelete(ctx, req.GetCommentId())
	if err != nil {
		log.ErrorWithCtx(ctx, "CommentDelete CommentDelete err:%v, req:%v", err, req)
		return nil, err
	}
	var postCommentCount uint32
	if isAIRootComment {
		// 根评论为ai评论的帖子评论数额外计数
		aiCommentCnt, err := s.commentMgr.IncrPostAICommentCnt(ctx, data.PostId, -1)
		if err != nil {
			log.ErrorWithCtx(ctx, "CommentDelete IncrPostAICommentCnt err:%v, req:%v", err, req)
		}
		if aiCommentCnt == 0 {
			err = s.postMgr.SetHasAICommentStatus(ctx, data.PostId, false)
			if err != nil {
				log.ErrorWithCtx(ctx, "CommentDelete SetHasAICommentStatus err:%v", err)
			}
		}
		post, err := s.postMgr.GetPost(ctx, data.PostId)
		if err != nil {
			log.ErrorWithCtx(ctx, "CommentDelete GetPost err:%v, req:%v", err, req)
		}
		if post != nil {
			postCommentCount = post.CommentCount
		}
	} else {
		postCommentCount, err = s.postMgr.IncrPostComment(ctx, data.PostId, -1)
		if err != nil {
			log.ErrorWithCtx(ctx, "CommentDelete IncrPostComment err:%v, req:%v", err, req)
		}
	}
	_ = s.commentMgr.SendCommentEvent(ctx, data, postCommentCount, eventpb.CommentEvent_ACTION_DEL)
	out := &pb.CommentDeleteResponse{}
	return out, nil
}

func (s *Server) GetPostHotComment(ctx context.Context, req *pb.GetPostHotCommentRequest) (*pb.GetPostHotCommentResponse, error) {
	out := &pb.GetPostHotCommentResponse{}
	datas, err := s.commentMgr.GetPostHotComment(ctx, req.GetPostIds())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPostHotComment GetPostHotComment err:%v, req:%v", err, req)
		return out, err
	}
	out.HotComments = datas
	return out, nil
}

// GetCommentInfoById 根据评论id获取评论详情
func (s *Server) GetCommentInfoById(ctx context.Context, req *pb.GetCommentInfoByIdRequest) (*pb.GetCommentInfoByIdResponse, error) {
	out := &pb.GetCommentInfoByIdResponse{}
	if req.GetCommentId() == "" {
		log.WarnWithCtx(ctx, "GetCommentInfoById empty, req:%v", req)
		return out, nil
	}
	datas, err := s.commentMgr.GetCommentItemsByIds(ctx, []string{req.GetCommentId()}, req.GetLoadSubComments())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommentInfoById GetCommentItemsByIds err:%v, req:%v", err, req)
		return out, err
	}
	if len(datas) == 0 {
		log.InfoWithCtx(ctx, "GetCommentInfoById GetCommentItemsByIds empty, req:%v", req)
		return out, nil
	}
	data := datas[0]
	out.Item = data
	return out, nil
}

func (s *Server) GetAICommentCntInfo(ctx context.Context, req *pb.GetAICommentCntInfoRequest) (*pb.GetAICommentCntInfoResponse, error) {
	out := &pb.GetAICommentCntInfoResponse{}
	userAICommentPostCnt, postAICommentCnt, todayUserAiCommentCnt, err := s.commentMgr.GetAICommentCntInfo(ctx, req.GetUid(), req.GetPostId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAICommentCntInfo GetAICommentCntInfo err:%v, req:%v", err, req)
		return out, err
	}
	out.UserAiCommentPostCnt = userAICommentPostCnt
	out.PostAiCommentCnt = postAICommentCnt
	out.TodayUserAiCommentCnt = todayUserAiCommentCnt
	log.InfoWithCtx(ctx, "GetAICommentCntInfo req:%+v, resp:%+v", req, out)
	return out, nil
}

func (s *Server) BatchGetPostAICommentCnt(ctx context.Context, req *pb.BatchGetPostAICommentCntRequest) (*pb.BatchGetPostAICommentCntResponse, error) {
	out := &pb.BatchGetPostAICommentCntResponse{}
	res, err := s.commentMgr.BatchGetPostAICommentCnt(ctx, req.GetPostIds())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetPostAICommentCnt BatchGetPostAICommentCnt err:%v, req:%v", err, req)
		return out, err
	}
	out.CntMap = res
	log.InfoWithCtx(ctx, "BatchGetPostAICommentCnt req:%+v, resp:%+v", req, out)
	return out, nil
}
