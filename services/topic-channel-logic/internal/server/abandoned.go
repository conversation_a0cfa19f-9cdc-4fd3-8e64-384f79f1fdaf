package server

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	gaChannelPB "golang.52tt.com/protocol/app/channel"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/protocol/common/status"
	channel_go "golang.52tt.com/protocol/services/channel-go"
	channelmusic "golang.52tt.com/protocol/services/channelmusicsvr"
	cppChannel "golang.52tt.com/protocol/services/channelsvr"
	v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	channelPB "golang.52tt.com/protocol/services/topic_channel/channel"
	genPB "golang.52tt.com/protocol/services/topic_channel/recommendation_gen"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/topic-channel-logic/internal/report"
	"math/rand"
	"strings"
	"time"
)

func (s *Server) SwitchGamePlay(ctx context.Context, in *topic_channel.SwitchGamePlayReq) (*topic_channel.SwitchGamePlayResp, error) {
	out := &topic_channel.SwitchGamePlayResp{}
	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	log.InfoWithCtx(ctx, "SwitchGamePlay serviceinfo(%s) in(%s)", serviceInfo.String(), in.String())
	userId := serviceInfo.UserID
	channelBaseInfoResp, err := s.channelGoClient.GetChannelSimpleInfo(ctx, &channel_go.GetChannelSimpleInfoReq{
		ChannelId: in.GetChannelId(),
		OpUid:     serviceInfo.UserID,
	})
	if err != nil {
		if strings.Contains(err.Error(), "房间已经被删除") {
			log.WarnWithCtx(ctx, "SwitchGamePlay GetChannelSimpleInfo uid (%+v) channelId(%v) failed, err: %v", serviceInfo.UserID, in.GetChannelId(), err)
		}
		log.ErrorWithCtx(ctx, "SwitchGamePlay GetChannelSimpleInfo uid (%+v) channelId(%v) failed, err: %v", serviceInfo.UserID, in.GetChannelId(), err)
		return out, err
	}
	channelBaseInfo := channelBaseInfoResp.GetChannelSimple()
	log.DebugWithCtx(ctx, "SwitchGamePlay GetChannelSimpleInfo uid (%+v) channel creator (%d)", serviceInfo.UserID, channelBaseInfo.GetCreaterUid())

	// v5.5.0改 发布中只有房主可以切换玩法
	// 获取房间管理员列表
	channelAdmins, derr := s.channelClient.GetChannelAdmin(ctx, 0, in.GetChannelId())
	if derr != nil {
		log.ErrorWithCtx(ctx, "SwitchGamePlay GetChannelAdmin uid (%+v) channelId(%v) failed, err: %v", serviceInfo.UserID, in.GetChannelId(), derr)
		return out, derr
	}
	adminRole := 0
	if len(channelAdmins) > 0 {
		for _, channelAdmin := range channelAdmins {
			if *channelAdmin.Uid == serviceInfo.UserID {
				adminRole = int(*channelAdmin.AdminRole)
			}
		}
	}

	// 检查是不是管理员
	if adminRole != 1 && adminRole != 2 && adminRole != 4 {
		err = protocol.NewExactServerError(nil, status.ErrTopicChannelPermissionDenied)
		return out, err
	}

	cinfo, cerr := s.tcChannelClient.GetChannelByIds(ctx, &channelPB.GetChannelByIdsReq{Ids: []uint32{in.GetChannelId()}, Types: nil, ReturnAll: true})
	if cerr != nil {
		log.ErrorWithCtx(ctx, "SwitchGamePlay GetChannelByIds channelId(%v) err(%v)", in.GetChannelId(), cerr)
		return out, cerr
	}

	//var oldChannelName string

	if len(cinfo.Info) != 0 {
		for _, v := range cinfo.Info[0].DisplayType {
			if v == channelPB.ChannelDisplayType_DISPLAY_AT_MAIN_PAGE && adminRole != 1 {
				err = protocol.NewExactServerError(nil, status.ErrTopicChannelNotOwnerSwitchPlay)
				return out, err
			}
		}
		//oldChannelName = cinfo.Info[0].Name
	} else {
		//oldChannelName = ""
	}

	//var userInfo *account.User
	userInfo, err := s.accountgoClient.GetUserByUid(ctx, serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "SwitchGamePlay GetUsersMap userId(%v) err(%v)", serviceInfo.UserID, err)
		return out, err
	}
	// v5.5.0之后切换玩法可以改名字
	if in.ChannelName != "" {
		// check channel name
		if s.safetyConfig.Enabled() && !s.safetyConfig.InWhiteList(userId) {
			var smDeviceId string
			if in.BaseReq != nil && in.BaseReq.AntispamInfo != nil && in.BaseReq.AntispamInfo.SmAntispam != nil {
				smDeviceId = in.BaseReq.AntispamInfo.SmAntispam.SmDeviceId
			}

			tcReq := &supervision.TextCheckReq{
				ServiceInfo: serviceInfo,
				ChannelID:   in.ChannelId,
				Text:        in.GetChannelName(),
				SmDeviceId:  smDeviceId,
				User:        userInfo,
			}

			checkResult, _, serr := s.supervisor.TextCheck(ctx, tcReq)
			if serr != nil {
				serr = protocol.ToServerError(serr)
				log.ErrorWithCtx(ctx, "CheckChannelName userId(%v) TextCheck failed: %v\n", serviceInfo.UserID, serr)
				return out, serr
			}

			logFunc := log.ErrorWithCtx
			checkPass := false
			if checkResult != v2.Suggestion_REJECT {
				logFunc = log.DebugWithCtx
				checkPass = true
			}

			logFunc(ctx, "SwitchGamePlay service_info=%v channelId=%v ttid=%s content=%s",
				serviceInfo, in.ChannelId, userInfo.Username, in.ChannelName, checkResult)

			if !checkPass {
				return out, protocol.NewExactServerError(nil, status.ErrTopicChannelNameSensitive)
			}
		}
		// end check channel name

		// TODO：修改房间名称 应该使用 channelApi服务 ModifyName 或者 ModifyNameWithAntiCheck
		_, modifyErr := s.channelClient.ModifyChannel(ctx, serviceInfo.UserID,
			&cppChannel.ModifyChannelReq{
				ChannelId: &in.ChannelId,
				OpUid:     &serviceInfo.UserID,
				Name:      &in.ChannelName,
			})
		if modifyErr != nil {
			log.ErrorWithCtx(ctx, "SwitchGamePlay ModifyChannel userId(%v) ChannelName(%v) err(%v)", serviceInfo.UserID, in.ChannelName, modifyErr)
			return out, modifyErr
		}

		cnerr := s.sendChangeNameMsg(ctx, serviceInfo.UserID, in.ChannelName, in.ChannelId, userInfo.GetUsername(),
			userInfo.GetNickname())
		if cnerr != nil {
			log.ErrorWithCtx(ctx, "SwitchGamePlay sendChangeNameMsg failed, userId(%v) err: %v", serviceInfo.UserID, cnerr)
			// return out, merr
		}
	}

	switchChannelTabResp, err := s.tcChannelClient.SwitchChannelTab(ctx, &channelPB.SwitchChannelTabReq{
		Uid:       serviceInfo.UserID,
		Creator:   channelBaseInfo.GetCreaterUid(),
		TabId:     in.TabId,
		ChannelId: in.ChannelId,
		Source:    channelPB.Source_SWITCH,
		AppId:     in.BaseReq.AppId,
		MarketId:  in.BaseReq.MarketId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SwitchGamePlay SwitchChannelTab userId(%v) err: %v", serviceInfo.UserID, err)
		return out, err
	}

	welcomeText := GetWelcomeText(switchChannelTabResp.WelcomeTxtList)

	var thirdPartyGame *topic_channel.ThirdPartyGame
	if otherGameConf, ok := s.thirdPartyGameConfig[in.TabId]; ok {
		thirdPartyGame = convertOtherGameConf(otherGameConf)
		log.DebugWithCtx(ctx, "SwitchGamePlay uid(%v) ThirdPartyGame (%+v)", serviceInfo.UserID, thirdPartyGame)
	}

	log.DebugWithCtx(ctx, "SwitchGamePlay sendChangeTabMsg %v ,%v, %v ,%v,%v ,%v,%v ,%v ", serviceInfo.UserID, in.GetChannelId(), in.GetTabId(), userInfo.Username,
		userInfo.Nickname, switchChannelTabResp.TabName, in.ChannelName, switchChannelTabResp.TagId)
	// 切换玩法成功后，才能通知客户端广播更新房间tab
	merr := s.sendChangeTabMsg(ctx, serviceInfo.UserID, in.GetChannelId(), in.GetTabId(), userInfo.GetUsername(),
		userInfo.GetNickname(), switchChannelTabResp.TabName, false, false, "",
		switchChannelTabResp.MicMod, switchChannelTabResp.TabType, welcomeText, nil, thirdPartyGame, switchChannelTabResp.TagId, "")
	if merr != nil {
		log.ErrorWithCtx(ctx, "SwitchGamePlay sendChangeTabMsg failed, userId(%v) err: %v", serviceInfo.UserID, merr)
		// return out, merr
	}

	// 向房主发消息
	msg := fmt.Sprintf("房间玩法切换为[%s]", switchChannelTabResp.TabName)
	merr = s.sendDismissMsg(ctx, serviceInfo.UserID, in.GetChannelId(), userInfo.GetUsername(), userInfo.GetNickname(), msg,
		gaChannelPB.ChannelMsgType_CHANNEL_SWITCH_PLAY_GAME)
	if merr != nil {
		log.ErrorWithCtx(ctx, "SwitchGamePlay sendDismissMsg userId(%v) channelId(%v) failed, err: %v", serviceInfo.UserID, in.GetChannelId(), merr)
	}

	// 上报到数据中心
	report.ReportCreateTopicChannel(ctx, in.GetChannelId(), channelBaseInfo.GetCreaterUid(), channelBaseInfo.GetCreateTs(), channelBaseInfo.GetDisplayId(),
		time.Now().Unix(), channelBaseInfo.GetChannelType(), in.GetTabId(), switchChannelTabResp.GetTabName(), getChannelPara(nil, nil), []string{}, in.GetChannelName(),
		switchChannelTabResp.GetMicMod())

	// 同步跟随好友
	nerr := s.fanoutFollowNotifyUtil.FanoutFollowLabelUpdate(ctx, serviceInfo.UserID, userInfo.GetUsername(), "", "", "")
	if nerr != nil {
		log.ErrorWithCtx(ctx, "SwitchGamePlay FanoutFollowLabelUpdate userId(%v) channelId(%v) failed, err: %v", serviceInfo.UserID, in.GetChannelId(), nerr)
	}

	return out, nil
}

func (s *Server) ListPlaymateRecommendedChannel(ctx context.Context,
	in *topic_channel.ListPlaymateRecommendedChannelReq) (out *topic_channel.ListPlaymateRecommendedChannelResp, err error) {
	out = new(topic_channel.ListPlaymateRecommendedChannelResp)
	var count uint32 = 20
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	userId := serviceInfo.UserID
	defer log.DebugWithCtx(ctx, "ListPlaymateRecommendedChannel uid:%d in:%v out:%v", userId, in, out)

	// 限流
	over := s.speedLimit.IsOver(0)
	if over {
		return out, nil
	}

	supConfInst := &supervision.SupervisoryConf{
		RegisterLimitTime:      s.serviceConfigT.GetPublicSwitchConfig().GetRegisterLimitTime(),
		UserLevelLimit:         s.serviceConfigT.GetPublicSwitchConfig().GetUserLevelLimit(),
		RealNameStandardStatus: s.serviceConfigT.GetPublicSwitchConfig().GetRealNameStandardStatus(),
	}
	reserve, _ := json.Marshal(&genPB.RecommendationReqReserve{ImListExpGroup: genPB.IMChannelListABGroup_RegLess72Hour_Exp_B}) // 临时方案
	genResp, err := s.genRecommendationClient.GetRecommendationList(ctx, &genPB.GetRecommendationListReq{
		Uid: userId, Limit: count, GetMode: genPB.GetRecommendationListReq_NEXTPAGE,
		ChannelEnterSource: uint32(gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_IM_LIST_RECOMMENDED_CHANNEL_TO_FRESHMAN), // 临时方案
		ClientType:         uint32(serviceInfo.ClientType), ClientVersion: serviceInfo.ClientVersion, MarketId: serviceInfo.MarketID,
		Env:              genPB.GetRecommendationListReq_Environment(s.v2Config.GenServerEnv),
		ChannelPackageId: in.ChannelPackageId,
		Reserve:          string(reserve), // 临时方案
		RegulatoryLevel:  genPB.REGULATORY_LEVEL(s.supervisor.GetUserRegulatoryLevelByUid(ctx, userId, supConfInst)),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ListPlaymateRecommendedChannel gen GetRecommendationList userId(%v) err(%v)", userId, err)
		return
	}
	log.DebugWithCtx(ctx, "genResp.ChannelId: %v", genResp.ChannelId)
	if len(genResp.ChannelId) == 0 {
		return out, nil
	}

	channelResp, err := s.channelGoClient.BatchGetChannelSimpleInfo(ctx, &channel_go.BatchGetChannelSimpleInfoReq{
		OpUid:         userId,
		ChannelIdList: genResp.GetChannelId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ListPlaymateRecommendedChannel list channel simple info by ids(%v) err(%v)", genResp.ChannelId, err)
		return
	}
	channelSimpleInfoResp := make(map[uint32]*channel_go.ChannelSimpleInfo, len(channelResp.GetChannelSimpleList()))
	for _, info := range channelResp.GetChannelSimpleList() {
		channelSimpleInfoResp[info.GetChannelId()] = info
	}
	userIds := make([]uint32, 0)
	for _, info := range channelSimpleInfoResp {
		userIds = append(userIds, info.GetBindId())
	}
	userIds = utils.TrimRepeatUint32Value(userIds)

	userMap, err := s.accountClient.GetUsersMap(ctx, userIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "ListPlaymateRecommendedChannel GetUsersMap by ids(%v) err(%v)", userIds, err)
		return out, err
	}

	resp, err := s.tcChannelClient.GetChannelByIds(ctx, &channelPB.GetChannelByIdsReq{Ids: genResp.ChannelId})
	if err != nil {
		log.ErrorWithCtx(ctx, "ListPlaymateRecommendedChannel GetChannelByIds userId(%v) ids(%v) err(%v)", userId, genResp.ChannelId, err)
		return
	}
	log.DebugWithCtx(ctx, "ListPlaymateRecommendedChannel GetChannelByIds in(%d) out(%d)", len(genResp.ChannelId), len(resp.Info))

	tabs := make([]*tabPB.Tab, len(resp.GetInfo()))
	channelIdToTab := make(map[uint32]*tabPB.Tab)
	for i, info := range resp.GetInfo() {
		tab := &tabPB.Tab{Id: info.GetTabId()}
		tabs[i] = tab
		channelIdToTab[info.GetId()] = tab
	}
	log.DebugWithCtx(ctx, "channelIdToTab: %v", channelIdToTab)

	tabResp, err := s.tcTabClient.FiniteTabs(ctx, &tabPB.FiniteTabsReq{Tabs: tabs})
	if err != nil {
		log.ErrorWithCtx(ctx, "ListPlaymateRecommendedChannel GetOnlineFriends FiniteTabs uid: %d tab %v err: %v", serviceInfo.UserID, tabs, err)
		return
	}
	for _, tab := range tabResp.GetTabs() {
		for cid, t := range channelIdToTab {
			if t.GetId() == tab.GetId() {
				channelIdToTab[cid] = tab
			}
		}
	}

	var items []*topic_channel.PlaymateRecommendedChannelItem
	for _, channelId := range genResp.ChannelId {
		var item = &topic_channel.PlaymateRecommendedChannelItem{
			ChannelInfo:   &topic_channel.TopicChannelInfoV2{ChannelId: channelId},
			OwnerUserInfo: &topic_channel.ChannelOwnerInfo{},
		}

		if channelSimpleInfoResp[channelId] != nil {
			if channelSimpleInfoResp[channelId].GetHasPwd() {
				//被锁房了就不返回
				continue
			}
			item.ChannelInfo.Name = channelSimpleInfoResp[channelId].GetName()
			item.ChannelInfo.DisplayId = channelSimpleInfoResp[channelId].GetDisplayId()
			item.ChannelInfo.AppId = channelSimpleInfoResp[channelId].GetAppId()
			item.ChannelInfo.HasPwd = channelSimpleInfoResp[channelId].GetHasPwd()
			item.ChannelInfo.ChannelType = channelSimpleInfoResp[channelId].GetChannelType()
			item.ChannelInfo.TopicTitle = channelSimpleInfoResp[channelId].GetTopicTitle()
			item.ChannelInfo.IconMd5 = channelSimpleInfoResp[channelId].GetIconMd5()
			item.ChannelInfo.BindId = channelSimpleInfoResp[channelId].GetBindId()

			if _, ok := userMap[channelSimpleInfoResp[channelId].GetCreaterUid()]; !ok {
				continue
			}
			item.OwnerUserInfo.Uid = channelSimpleInfoResp[channelId].GetCreaterUid()
			item.OwnerUserInfo.Account = userMap[channelSimpleInfoResp[channelId].GetCreaterUid()].Username
			item.OwnerUserInfo.Sex = uint32(userMap[channelSimpleInfoResp[channelId].GetCreaterUid()].Sex)
			item.OwnerUserInfo.Nickname = userMap[channelSimpleInfoResp[channelId].GetCreaterUid()].Nickname
		} else {
			//channel info都拿不到，不返回
			continue
		}

		if s.isAppleSuperVisionSkip(serviceInfo.ClientType, item.ChannelInfo) {
			//苹果监管需要临时写死，带安卓，android字眼不返回
			continue
		}
		if serviceInfo.ClientType == protocol.ClientTypeIOS && s.v2Config.IosReplaceAndroidString {
			item = s.playmateRecommendedChannelItemReplaceByIOS(item)
		}

		if tab, ok := channelIdToTab[channelId]; ok {
			// 实际上运营后台只能配置FollowLabelImg
			item.FindPlayingImg = tab.FollowLabelImg
			item.FindPlayingText = tab.FindPlayingText
			items = append(items, item)
		}
	}
	out.Items = items

	return
}

// 对应一行显示两个房间的UI（旧版本）
func (s *Server) ListRecommendTopicChannel(ctx context.Context, in *topic_channel.ListRecommendTopicChannelReq) (out *topic_channel.ListRecommendTopicChannelResp, err error) {
	out = new(topic_channel.ListRecommendTopicChannelResp)
	return out, protocol.NewExactServerError(nil, status.ErrAccountRegLimitedByClientVersion, "请升级至最新版本")
}

// TabTopicChannel 获取约玩主题房标签
func (s *Server) TabTopicChannel(ctx context.Context,
	in *topic_channel.TabTopicChannelReq) (*topic_channel.TabTopicChannelResp, error) {
	out := &topic_channel.TabTopicChannelResp{
		BaseResp: &app.BaseResp{},
		Tabs:     make([]*topic_channel.Tab, 0, 100),
		Index:    0,
	}
	return out, protocol.NewExactServerError(nil, status.ErrAccountRegLimitedByClientVersion, "请升级至最新版本")
}

// CreateTopicChannel 创建主题房间
func (s *Server) CreateTopicChannel(ctx context.Context, in *topic_channel.CreateTopicChannelReq) (*topic_channel.CreateTopicChannelResp, error) {
	out := &topic_channel.CreateTopicChannelResp{}

	return out, protocol.NewExactServerError(nil, status.ErrAccountRegLimitedByClientVersion, "请升级至最新版本")
}

func (s *Server) GetChannelDialog(ctx context.Context, in *topic_channel.GetChannelDialogReq) (*topic_channel.GetChannelDialogResp, error) {
	out := &topic_channel.GetChannelDialogResp{}
	return out, protocol.NewExactServerError(nil, status.ErrAccountRegLimitedByClientVersion, "请升级至最新版本")
}

// GetTabListWhenCreate 创建房间时的选择tab
// deprecated
func (s *Server) GetTabListWhenCreate(ctx context.Context, in *topic_channel.GetTabListWhenCreateReq) (*topic_channel.GetTabListWhenCreateResp, error) {
	out := &topic_channel.GetTabListWhenCreateResp{}
	serviceInfo, _ := grpc.ServiceInfoFromContext(ctx)
	log.InfoWithCtx(ctx, "GetTabListWhenCreate in:%s, serviceinfo:%s", in.String(), serviceInfo.String())
	return out, nil

	/*req := &tabPB.GetCategoryTitleReq{
		Skip:  0,
		Limit: 0,
	}

	categoryList, err := s.tcTabClient.GetCategoryTitleList(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTabListWhenCreate uid (%+v) err(%v)\n", uid, err)
		return out, err
	}
	log.DebugWithCtx(ctx, "GetTabListWhenCreate uid(%v) categoryList(%v)\n", uid, len(categoryList.GetCategoryList()))
	tabList := make([]*topic_channel.GetTabListWhenCreateResp_SpecialCategory, 0)
	specialTabList := make([]*topic_channel.Tab, 0)
	specialSort := make([]int, 0)

	tabFilter, categoryFilter := s.getFilterMap(ctx, serviceInfo, "")

	var tabInfos []*tabPB.Tab
	platform := protocol.NewTerminalType(serviceInfo.TerminalType).Platform()
	for _, b := range categoryList.GetCategoryList() {
		if categoryFilter[b.GetCategoryId()] {
			continue
		}
		categoryPlatformType := b.PlatformType
		if !(categoryPlatformType == tabPB.PlatformType_ALL ||
			(categoryPlatformType == tabPB.PlatformType_ANDROID_IOS && platform == protocol.MOBILE) ||
			(categoryPlatformType == tabPB.PlatformType_PC && platform == protocol.PC)) {
			continue
		}

		ut := &topic_channel.GetTabListWhenCreateResp_SpecialCategory{}

		if uint32(b.CategoryType) != uint32(tabPB.Scene_NO_DEFAULT) {
			continue
		}

		tabOfBlock, err := s.tcTabClient.GetAllTabOfCategory(ctx, &tabPB.GetAllTabOfCategoryReq{
			CategoryId: b.CategoryId,
		})

		if err != nil {
			log.ErrorWithCtx(ctx, "GetTabListWhenCreate GetAllTabOfBlock uid (%+v) err(%v)", uid, err)
			return out, err
		}
		tabInfos = tabOfBlock.GetTabInfo()

		if len(tabInfos) == 0 {
			// 分类下没有游戏，则不显示分类
			continue
		}

		ut.CategoryId = b.CategoryId
		if b.NewCategoryTitle != "" {
			ut.CategoryName = b.NewCategoryTitle
		} else {
			ut.CategoryName = b.Title
		}

		ut.ImageUri = b.ImageUrl
		tabs := make([]*topic_channel.Tab, 0)

		for _, b := range tabInfos {
			if categoryPlatformType == tabPB.PlatformType_ALL {
				tabPlatformType := b.PlatformType
				if !(tabPlatformType == tabPB.PlatformType_ALL ||
					(tabPlatformType == tabPB.PlatformType_ANDROID_IOS && platform == protocol.MOBILE) ||
					(tabPlatformType == tabPB.PlatformType_PC && platform == protocol.PC)) {
					continue
				}
			}

			if s.v2Config.ServerEnv != "staging" {
				//灰度环境不根据策略过滤，为了小游戏测试
				if isFilter := MiniGameStageStrategy(b, serviceInfo, false); isFilter {
					continue
				}
			}

			if _, ok := tabFilter[b.Id]; ok {
				continue
			}

			if b.CategorySort > 0 {
				t := convertTab(b)
				specialTabList = append(specialTabList, t)

				specialSort = append(specialSort, int(b.CategorySort))
				//从更多手游中移到一级列表category中
				continue
			}

			t := convertTab(b)
			tabs = append(tabs, t)
		}
		if len(tabs) == 0 {
			// 分类下没有游戏，则不显示分类
			continue
		}
		ut.TabDetail = tabs
		ut.TabType = topic_channel.GetTabListWhenCreateResp_TabType(tabs[0].TabType)
		tabList = append(tabList, ut)
	}

	//排序特殊排序
	sort.Ints(specialSort)

	for _, v := range specialSort {
		//插入特殊排序的空位
		index := v - 1
		rear := append([]*topic_channel.GetTabListWhenCreateResp_SpecialCategory{}, tabList[index:]...)
		tmp := &topic_channel.GetTabListWhenCreateResp_SpecialCategory{}
		tabList = append(tabList[0:index], tmp)
		tabList = append(tabList, rear...)
	}

	for i, v := range specialTabList {
		//将特殊tab按特殊排序插入列表
		//index := v.CategorySort - 1
		//log.DebugWithCtx(ctx,"index is %d tabList:%v\n", index, tabList)

		tmp := &topic_channel.GetTabListWhenCreateResp_SpecialCategory{}

		tmp.CategoryName = v.Name
		tmp.ImageUri = v.NewTabCategoryUrl
		tmp.TabDetail = []*topic_channel.Tab{v}
		//特殊分类，特殊处理，避免跟普通分类冲突了category_id，暂时想到这种骚操作
		tmp.CategoryId = uint32(999999 + i)

		tabList[v.CategorySort-1] = tmp
	}

	out.SpecialCategory = tabList

	return out, nil*/
}

func (s *Server) GetGuideConfig(ctx context.Context, in *topic_channel.GetGuideConfigReq) (*topic_channel.GetGuideConfigResp, error) {
	out := &topic_channel.GetGuideConfigResp{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	log.InfoWithCtx(ctx, "GetGuideConfig in:%s, serviceinfo:%s", in.String(), serviceInfo.String())
	return out, nil
	/*userId := serviceInfo.UserID
	log.DebugWithCtx(ctx, "GetGuideConfig uid(%v) in(%v)", userId, in)

	user, err := s.accountClient.GetUser(ctx, userId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuideConfig GetUser userId(%v) err(%v)", userId, err)
		return out, err
	}
	var isMiniGameUI bool
	if user.RegisteredAt >= uint32(time.Now().Add(-time.Second*time.Duration(s.v2Config.QuickMatchNewUserDuration)).Unix()) {
		// - 注册3天内，“快速匹配”功能的底图变更为小游戏风格的底图，3天后恢复默认底图
		// - 自然流量倾向玩小游戏的用户的“快速匹配”功能变更为小游戏风格底图
		// - 小游戏渠道包用户的“快速匹配”功能变更为渠道对应的小游戏风格底图
		if strings.ToLower(in.ChannelPackageId) != "" && strings.ToLower(in.ChannelPackageId) != "official" {
			isMiniGameUI = true
		} else {
			tagResp, err := s.userTagClient.GetUserTag(ctx, userId)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetGuideConfig GetUserTag userId(%v) err(%v)", userId, err)
				return out, err
			}
			log.DebugWithCtx(ctx, "GetGuideConfig uid(%v) tag(%v)", userId, usertag.GetTagName(tagResp))
			for _, x := range tagResp {
				// 没有东西可以判断只能用名字...................
				if x.TagName == "社交桌游" {
					isMiniGameUI = true
					break
				}
			}
		}
	}
	_, out.GuideImageUrl, _ = s.quickMatchHandler.getInfo(strings.ToLower(in.ChannelPackageId), isMiniGameUI)

	return out, nil*/
}

func (s *Server) CreateAndReleaseTopicChannel(ctx context.Context, in *topic_channel.CreateAndReleaseTopicChannelReq) (out *topic_channel.CreateAndReleaseTopicChannelResp, err error) {
	out = &topic_channel.CreateAndReleaseTopicChannelResp{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	log.InfoWithCtx(ctx, "CreateAndReleaseTopicChannel in:%s, serviceinfo:%s", in.String(), serviceInfo.String())
	return out, nil
}

func (s *Server) CreateTopicChannelV2(ctx context.Context, in *topic_channel.CreateTopicChannelV2Req) (*topic_channel.CreateTopicChannelV2Resp, error) {
	out := &topic_channel.CreateTopicChannelV2Resp{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	log.InfoWithCtx(ctx, "CreateTopicChannelV2 in:%s, serviceinfo:%s", in.String(), serviceInfo.String())
	return out, nil
}

// ShowTopicChannelTabList deprecated
func (s *Server) ShowTopicChannelTabList(ctx context.Context, in *topic_channel.ShowTopicChannelTabListReq) (out *topic_channel.ShowTopicChannelTabListResp, err error) {
	out = &topic_channel.ShowTopicChannelTabListResp{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	log.InfoWithCtx(ctx, "GetTabListWhenCreate in:%s, serviceinfo:%s", in.String(), serviceInfo.String())
	return out, nil

	/*categoryList, err := s.skipIgnoreCategory(ctx, serviceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "ShowTopicChannelTabList skipIgnoreCategory error : %v", err)
		return out, err
	}

	tabList, err := s.skipStageTab(ctx, serviceInfo, categoryList)
	if err != nil {
		log.ErrorWithCtx(ctx, "ShowTopicChannelTabList skipStageTab error : %v", err)
		return out, err
	}
	//key：categoryId
	primaryItemMap := make(map[uint32][]*topic_channel.ShowTopicChannelTabSecondaryItem)

	//需要外显的小众游戏
	displayMinorityGame := s.getDisplayMinorityGame(in.SelfGameIds)
	for _, v := range displayMinorityGame {
		primaryItemMap[v.tabInfo.CategoryId] = append(primaryItemMap[v.tabInfo.CategoryId], convertTabSecondaryItem(v.tabInfo))
	}

	newTabMap := s.serviceConfigT.GetOption().NewTabMap
	//特殊玩法要插入第一行
	specialTabList := make([]*tabPB.Tab, 0)
	for _, v := range tabList {
		//特殊 单独玩法放在了第一级当分类
		if v.CategorySort > 0 {
			specialTabList = append(specialTabList, v)
			//continue  不再去重
		}
		if v.IsMinorityGame {
			//忽略小众游戏
			continue
		}
		item := convertTabSecondaryItem(v)
		if newTabMap[v.Id] {
			item.SpecialTip = topic_channel.ShowTopicChannelTabSecondaryItem_New
		}
		primaryItemMap[v.CategoryId] = append(primaryItemMap[v.CategoryId], item)
	}

	out.PrimaryItem = make([]*topic_channel.ShowTopicChannelTabPrimaryItem, 0)
	strongInsertItem := make([]*topic_channel.ShowTopicChannelTabPrimaryItem, 0)

	sort.SliceStable(specialTabList, func(i, j int) bool {
		return specialTabList[i].CategorySort < specialTabList[j].CategorySort
	})

	//只是给客户端去重用的数字，无实意
	var specialTabMagicNumStart uint32 = 8888
	for _, v := range specialTabList {
		specialTabMagicNumStart++
		tmp := &topic_channel.ShowTopicChannelTabPrimaryItem{}
		tmp.ItemText = v.Name
		tmp.MaskLayer = v.MaskLayer
		tmp.ItemIcon = v.SmallCardUrl
		tmp.CategoryId = specialTabMagicNumStart
		tmp.SecondaryItem = make([]*topic_channel.ShowTopicChannelTabSecondaryItem, 1)
		tmp.SecondaryItem[0] = convertTabSecondaryItem(v)
		strongInsertItem = append(strongInsertItem, tmp)
	}

	for _, v := range categoryList {
		tmp := &topic_channel.ShowTopicChannelTabPrimaryItem{}
		tmp.ItemText = v.NewCategoryTitle
		tmp.ItemIcon = v.Icon
		tmp.CategoryId = v.CategoryId
		tmp.MaskLayer = v.MaskLayer
		if val, ok := primaryItemMap[v.CategoryId]; ok {
			tmp.SecondaryItem = val
		} else {
			//category下没有tab则跳过展示
			continue
		}

		out.PrimaryItem = append(out.PrimaryItem, tmp)
	}

	out.PrimaryItem = append(strongInsertItem, out.PrimaryItem...)
	log.DebugWithCtx(ctx, "ShowTopicChannelTabList out : %v", out)
	return*/
}

func (s *Server) GetChannelRoomNameConfig(ctx context.Context,
	in *topic_channel.GetChannelRoomNameConfigReq) (*topic_channel.GetChannelRoomNameConfigResp, error) {

	out := &topic_channel.GetChannelRoomNameConfigResp{}
	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	log.InfoWithCtx(ctx, "GetChannelRoomNameConfig in:%s, serviceinfo:%s", in.String(), serviceInfo.String())
	return out, nil

	/*roomConfig, err := s.tcTabClient.GetRoomNameConfigure(ctx, &tabPB.GetRoomNameConfigureReq{TabId: in.TabId, NeedOriginData: true})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelRoomNameConfig GetRoomNameConfigure userId (%v) failed, err: %v", serviceInfo.UserID, err)
		return out, err
	}
	tagOptionContain := make([]*topic_channel.TagOption, 0)
	for _, opt := range roomConfig.GetConfigure().GetTagOption() {
		tagOption := &topic_channel.TagOption{}
		tagItemContain := make([]*topic_channel.TagOption_TagItem, 0)

		tagOption.OptionId = opt.GetOptionId()
		tagOption.SourceTitle = opt.GetSourceTitle()
		tagOption.ClientTitle = opt.GetClientTitle()
		for _, item := range opt.GetTagItems() {
			tagItem := &topic_channel.TagOption_TagItem{}
			tagItem.SrcString = item.SrcString
			tagItem.SpliceString = item.SpliceString
			tagItem.ItemId = item.ItemId
			tagItemContain = append(tagItemContain, tagItem)
		}
		tagOption.TagItems = tagItemContain
		tagOptionContain = append(tagOptionContain, tagOption)
	}

	tagContentContain := make([]*topic_channel.TagContent, 0)
	for _, template := range roomConfig.GetConfigure().GetTagContent() {
		tagContent := &topic_channel.TagContent{}
		tagContent.Text = template.GetText()
		orderRule := make([]*topic_channel.TagContent_SequenceItem, 0)
		for _, v := range template.GetSequence() {
			sequenceItem := &topic_channel.TagContent_SequenceItem{}
			sequenceItem.OptionId = v.OptionId
			sequenceItem.SplitText = v.SplitText
			if int32(v.GetType()) == 0 {
				sequenceItem.Type = topic_channel.TagContent_SequenceItem_SELF
			} else if int32(v.GetType()) == 1 {
				sequenceItem.Type = topic_channel.TagContent_SequenceItem_TAG
			} else if int32(v.GetType()) == 2 {
				sequenceItem.Type = topic_channel.TagContent_SequenceItem_TEXT
			}
			orderRule = append(orderRule, sequenceItem)
		}
		tagContent.Sequence = orderRule
		tagContent.ContentId = template.GetContentId()
		tagContentContain = append(tagContentContain, tagContent)
	}

	roomNameConfig := &topic_channel.RoomNameConfigure{}
	roomNameConfig.TabId = roomConfig.Configure.TabId
	roomNameConfig.Version = roomConfig.Configure.Version
	roomNameConfig.TagOption = tagOptionContain
	roomNameConfig.TagContent = tagContentContain

	out.RoomNameConfigure = roomNameConfig
	return out, nil*/
}

// 对应一行显示一个房间的UI
func (s *Server) ListTopicChannelV2(ctx context.Context, in *topic_channel.ListTopicChannelV2Req) (*topic_channel.ListTopicChannelV2Resp, error) {
	out := &topic_channel.ListTopicChannelV2Resp{}
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	log.InfoWithCtx(ctx, "ListTopicChannelV2 serviceinfo:(%s) in(%s)", serviceInfo.String(), in.String())
	//return out, nil

	if in.Count == 0 {
		in.Count = 10
	}
	userId := serviceInfo.UserID
	log.DebugWithCtx(ctx, "ListTopicChannelV2 uid(%v) ip(%v) clientType(%v) version(%v) in(%v)",
		userId, serviceInfo.ClientIPAddr(), serviceInfo.ClientType, protocol.ClientVersion(serviceInfo.ClientVersion).String(), in)
	var options []*channelPB.BlockOption
	var genOption []*genPB.BlockOption
	for _, o := range in.BlockOption {
		if o.ElemId > 0 && o.ElemId != InfElemID {
			options = append(options, &channelPB.BlockOption{BlockId: o.BlockId, ElemId: o.ElemId})
			genOption = append(genOption, &genPB.BlockOption{BlockId: o.BlockId, ElemId: o.ElemId})
		}
	}
	//先调江正，超时或无返回再调topic channel拿一堆
	var getMode = genPB.GetRecommendationListReq_REFRESH
	var oldNum uint32
	if in.LoadMore != nil && in.LoadMore.RecommendMore != nil {
		getMode = genPB.GetRecommendationListReq_NEXTPAGE
		oldNum = in.LoadMore.RecommendMore.Num
	}

	//!!!江正超时就会走保底!!!
	cx, cancel := context.WithTimeout(ctx, time.Millisecond*2000)
	defer cancel()

	//genResp := &genPB.GetRecommendationListResp{}
	var err error
	supConfInst := &supervision.SupervisoryConf{
		RegisterLimitTime:      s.serviceConfigT.GetPublicSwitchConfig().GetRegisterLimitTime(),
		UserLevelLimit:         s.serviceConfigT.GetPublicSwitchConfig().GetUserLevelLimit(),
		RealNameStandardStatus: s.serviceConfigT.GetPublicSwitchConfig().GetRealNameStandardStatus(),
	}
	genResp, err := s.genRecommendationClient.GetRecommendationList(cx, &genPB.GetRecommendationListReq{
		Uid: userId, Limit: in.Count, TabId: in.TabId, BlockOptions: genOption, GetMode: getMode,
		ChannelEnterSource: uint32(gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_FINDPLAY_TAB),
		ClientType:         uint32(serviceInfo.ClientType), ClientVersion: serviceInfo.ClientVersion, MarketId: serviceInfo.MarketID,
		Env:              genPB.GetRecommendationListReq_Environment(s.v2Config.GenServerEnv),
		ChannelPackageId: in.ChannelPackageId,
		RegulatoryLevel:  genPB.REGULATORY_LEVEL(s.supervisor.GetUserRegulatoryLevelByUid(ctx, userId, supConfInst)),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ListTopicChannelV2 gen GetRecommendationList userId(%v) tabId(%v) err(%v)", userId, in.TabId, err)
	}
	//}

	var listRecommendationInfo = &listRecommendationInfo{
		topicChannelInfo:         map[uint32]*channelPB.ChannelInfo{},
		entertainmentChannelInfo: map[uint32]*entertainmentChannelInfo{},
	}
	if err == nil && len(genResp.ChannelId) > 0 {
		//江正有结果
		listRecommendationInfo.channelIds = genResp.ChannelId
		if len(genResp.ChannelInfoMap) > 0 {
			for channelID, info := range genResp.ChannelInfoMap {
				if info.TagId > 0 {
					var item = &entertainmentChannelInfo{channelId: channelID}
					//if cache := s.entertainmentTagCache.getEntertainmentConfigInfo(info.TagId); cache != nil {
					//	item.tagId, item.tagName, item.listBackgroundUri, item.cardMainColor, item.cardBackgroundUri = info.TagId, cache.GetName(), cache.GetTagUrl(), cache.GetButtonColor(), cache.GetBgUrl()
					//}
					listRecommendationInfo.entertainmentChannelInfo[channelID] = item
				}
			}
		}
		var channelIds []uint32
		for _, id := range genResp.ChannelId {
			if s.roomHandler.isHappyChatChannel(id) {
				listRecommendationInfo.entertainmentChannelInfo[id] = &entertainmentChannelInfo{
					channelId:         id,
					listBackgroundUri: "https://appcdn.52tt.com/emoji/download/5e65da10ba323c40ecbe3605",
					roomLabel:         "https://appcdn.52tt.com/emoji/download/5e9448305dfcac08f9c41906",
				}
				continue
			}
			if genResp.ChannelInfoMap[id] == nil || (genResp.ChannelInfoMap[id] != nil && genResp.ChannelInfoMap[id].TagId == 0) {
				channelIds = append(channelIds, id)
			}
		}
		if len(channelIds) > 0 {
			resp, err := s.tcChannelClient.GetChannelByIds(ctx, &channelPB.GetChannelByIdsReq{Ids: channelIds})
			if err != nil {
				log.ErrorWithCtx(ctx, "ListTopicChannelV2 GetChannelByIds userId(%v) ids(%v) err(%v)", userId, channelIds, err)
				return out, err
			}
			log.DebugWithCtx(ctx, "ListTopicChannelV2 GetChannelByIds in(%d) out(%d)", len(channelIds), len(resp.Info))
			for _, info := range resp.Info {
				if in.TabId > 0 && in.TabId < InfElemID {
					//很遗憾，tab id不一样，会被过滤
					if info.TabId != in.TabId {
						continue
					}
				}
				listRecommendationInfo.topicChannelInfo[info.Id] = info
			}
		}
		log.DebugWithCtx(ctx, "ListTopicChannelV2 hit tab check userId(%v) topic(%v) entertainment(%v) all(%v)",
			userId, len(listRecommendationInfo.topicChannelInfo), len(listRecommendationInfo.entertainmentChannelInfo), len(genResp.ChannelId))
		if !genResp.BottomReached {
			out.LoadMore = &topic_channel.ListRecommendTopicChannelLoadMore{RecommendMore: &topic_channel.ListRecommendTopicChannelLoadMore_RecommendMore{
				BottomReach: genResp.BottomReached, Num: oldNum + uint32(len(listRecommendationInfo.topicChannelInfo))}}
		}
	} else if err != nil {
		byTabLoadMore, listLoadMore := convertServerLoadMore(in.LoadMore)
		if in.TabId > 0 && in.TabId != InfElemID {
			resp, err := s.tcChannelClient.GetRecommendChannelListByTab(ctx, &channelPB.GetRecommendChannelListByTabReq{
				Uid: userId, Limit: in.Count, TabId: in.TabId, BlockOptions: options, LoadMore: byTabLoadMore,
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "ListTopicChannelV2 GetRecommendChannelListByTab userId(%v) err(%v)", userId, err)
				return out, err
			}
			//log.DebugWithCtx(ctx,"ListTopicChannelV2 userId(%v) list by tab(%v) resp(%v)", userId, in.TabId, utils.ToJson(resp))
			for _, info := range resp.ChannelList {
				listRecommendationInfo.channelIds = append(listRecommendationInfo.channelIds, info.Id)
				listRecommendationInfo.topicChannelInfo[info.Id] = info
			}
			out.LoadMore = convertTagLoadMore(resp.LoadMore)
		} else {
			var tabIds []uint32
			if serviceInfo.ClientType == protocol.ClientTypeANDROID && serviceInfo.ClientVersion < protocol.FormatClientVersion(5, 0, 3) ||
				serviceInfo.ClientType == protocol.ClientTypeIOS && serviceInfo.ClientVersion <= protocol.FormatClientVersion(5, 0, 3) {
				tabIds = s.v2Config.NotMiniGameTabIds
			}
			resp, err := s.tcChannelClient.GetRecommendChannelList(ctx, &channelPB.GetRecommendChannelListReq{
				Uid: userId, Limit: in.Count, LoadMore: listLoadMore, TabIdList: tabIds, //ReturnPgc: 1,
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "ListTopicChannelV2 GetRecommendChannelList userId(%v) err(%v)", userId, err)
				return out, err
			}
			for _, info := range resp.ChannelList {
				if info.IsRecommendChannel {
					//pgc房间
					listRecommendationInfo.channelIds = append(listRecommendationInfo.channelIds, info.Id)
					listRecommendationInfo.entertainmentChannelInfo[info.Id] = &entertainmentChannelInfo{
						channelId: info.Id, listBackgroundUri: "https://appcdn.52tt.com/emoji/download/5e65da10ba323c40ecbe3605",
					}
				} else {
					listRecommendationInfo.channelIds = append(listRecommendationInfo.channelIds, info.Id)
					listRecommendationInfo.topicChannelInfo[info.Id] = info
				}
			}
			out.LoadMore = convertListLoadMore(resp.LoadMore, 0, nil)
		}
	}

	tabs := make([]*tabPB.Tab, 0)
	userIds := make([]uint32, 0)
	channelIds := make([]uint32, 0)
	findMusicInfoChannelIds := make([]uint32, 0)
	//fix 代码扫描bug，entertainmentChannelIds SA4010: this result of append is never used, except maybe in other appends
	//entertainmentChannelIds := make([]uint32, 0)

	for channelID, info := range listRecommendationInfo.topicChannelInfo {
		tabs = append(tabs, &tabPB.Tab{Id: info.TabId})
		//userIds = append(userIds, info.Creator)
		channelIds = append(channelIds, channelID)
		if s.v2Config.ShowMusicInfoTab[info.TabId] {
			findMusicInfoChannelIds = append(findMusicInfoChannelIds, info.Id)
		}
	}

	for channelID := range listRecommendationInfo.entertainmentChannelInfo {
		channelIds = append(channelIds, channelID)
		//entertainmentChannelIds = append(entertainmentChannelIds, channelID)
		findMusicInfoChannelIds = append(findMusicInfoChannelIds, channelID)
	}
	//log.DebugWithCtx(ctx,"ListTopicChannelV2 userId(%v) channelIds(%v)", userId, channelIds)

	var tabData = map[uint32]*tabPB.Tab{}
	var tabBlockData = map[uint32]*tabPB.BlocksResp{}
	if len(tabs) > 0 {
		tabResp, err := s.tcTabClient.FiniteTabs(ctx, &tabPB.FiniteTabsReq{Tabs: tabs})
		if err != nil {
			log.ErrorWithCtx(ctx, "ListTopicChannelV2 FiniteTabs by tabIds(%v) err(%v)", tabs, err)
			return out, err
		}

		var tabIds []uint32
		for _, tab_ := range tabResp.GetTabs() {
			tabData[tab_.GetId()] = tab_
			tabIds = append(tabIds, tab_.GetId())
		}

		if len(tabIds) > 0 {
			blockResp, err := s.tcTabClient.BatchGetBlocks(ctx, &tabPB.BatchGetBlocksReq{TabId: tabIds})
			if err != nil {
				log.ErrorWithCtx(ctx, "ListTopicChannelV2 BatchGetBlocks by tabIds(%v) err(%v)", tabIds, err)
				return out, err
			}
			tabBlockData = blockResp.GetData()
		}
	}

	channelSimpleInfoResp, err := s.channelGoClient.BatchGetChannelSimpleInfo(ctx, &channel_go.BatchGetChannelSimpleInfoReq{
		OpUid:         userId,
		ChannelIdList: channelIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ListTopicChannelV2 list channel simple info by ids(%v) err(%v)", channelIds, err)
		return out, err
	}
	channelSimpleInfoMap := make(map[uint32]*channel_go.ChannelSimpleInfo, len(channelSimpleInfoResp.GetChannelSimpleList()))
	for _, info := range channelSimpleInfoResp.GetChannelSimpleList() {
		channelSimpleInfoMap[info.GetChannelId()] = info
	}
	for _, info := range channelSimpleInfoMap {
		userIds = append(userIds, info.GetCreaterUid())
	}
	userIds = utils.TrimRepeatUint32Value(userIds)

	userMap, err := s.accountClient.GetUsersMap(ctx, userIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "ListTopicChannelV2 GetUsersMap by ids(%v) err(%v)", userIds, err)
		return out, err
	}

	var musicInfo = map[uint32]string{}
	if len(findMusicInfoChannelIds) > 0 {
		musicResp, err := s.channelMusicClient.BatGetChannelPlayingMusicInfo(ctx, userId, &channelmusic.BatGetChannelPlayingMusicInfoReq{
			ChannelIdList: findMusicInfoChannelIds,
		})
		if err != nil {
			//报错了不返回就算了，接口不报错
			log.WarnWithCtx(ctx, "ListTopicChannelV2 BatGetChannelPlayingMusicInfo by ids(%v) err(%v)", findMusicInfoChannelIds, err)
		} else {
			for _, item := range musicResp.MusicList {
				musicInfo[item.ChannelId] = item.MusicName + "-" + item.MusicAuthor
			}
		}
	}

	for _, id := range listRecommendationInfo.channelIds {
		var item = &topic_channel.TopicChannelItemV2{
			ChannelInfo:   &topic_channel.TopicChannelInfoV2{ChannelId: id, MusicDesc: musicInfo[id]},
			TabInfo:       &topic_channel.TopicChannelTab{},
			OwnerUserInfo: &topic_channel.ChannelOwnerInfo{},
			TraceInfo:     &topic_channel.RecommendationTraceInfo{},
		}

		if genResp.GetChannelInfoMap() != nil {
			if info := genResp.GetChannelInfoMap()[id]; info != nil {
				item.TraceInfo.RecallFlag = info.GetRecallFlag()
			}
		}
		var owner uint32
		if channelSimpleInfoMap[id] != nil {
			if channelSimpleInfoMap[id].GetHasPwd() {
				//被锁房了就不返回
				continue
			}
			item.ChannelInfo.Name = channelSimpleInfoMap[id].GetName()
			item.ChannelInfo.DisplayId = channelSimpleInfoMap[id].GetDisplayId()
			item.ChannelInfo.AppId = channelSimpleInfoMap[id].GetAppId()
			item.ChannelInfo.HasPwd = channelSimpleInfoMap[id].GetHasPwd()
			item.ChannelInfo.ChannelType = channelSimpleInfoMap[id].GetChannelType()
			item.ChannelInfo.TopicTitle = channelSimpleInfoMap[id].GetTopicTitle()
			item.ChannelInfo.IconMd5 = channelSimpleInfoMap[id].GetIconMd5()
			item.ChannelInfo.BindId = channelSimpleInfoMap[id].GetBindId()
			owner = channelSimpleInfoMap[id].GetBindId()
		} else {
			//channel info都拿不到，不返回
			continue
		}
		if listRecommendationInfo.entertainmentChannelInfo[id] != nil {
			//这个房间是娱乐房
			var info = listRecommendationInfo.entertainmentChannelInfo[id]
			item.TabInfo = &topic_channel.TopicChannelTab{
				Id: info.tagId, Name: info.tagName,
				ListBackgroundUri: info.listBackgroundUri, CardBackgroundUri: info.cardBackgroundUri, CardMainColor: info.cardMainColor, RoomLabel: info.roomLabel,
			}
			if channelSimpleInfoMap[id] != nil && userMap[channelSimpleInfoMap[id].GetCreaterUid()] != nil {
				item.OwnerUserInfo.Uid = channelSimpleInfoMap[id].GetCreaterUid()
				item.OwnerUserInfo.Account = userMap[channelSimpleInfoMap[id].GetCreaterUid()].Username
				item.OwnerUserInfo.Sex = uint32(userMap[channelSimpleInfoMap[id].GetCreaterUid()].Sex)
				item.OwnerUserInfo.Nickname = userMap[channelSimpleInfoMap[id].GetCreaterUid()].Nickname
				//fix bug onwen unused
				//owner = channelSimpleInfoMap[id].GetBindId()
			}
			item.ChannelInfo.OnMicCount = uint32(6 + rand.Intn(3)) //nolint:gosec
			item.ChannelInfo.TopicChannelType = topic_channel.TopicChannelInfoV2Type_PGC
		} else if listRecommendationInfo.topicChannelInfo[id] != nil {
			//这个房间是主题房
			var info = listRecommendationInfo.topicChannelInfo[id]
			item.TabInfo = &topic_channel.TopicChannelTab{Id: info.TabId}
			if tabData[info.TabId] != nil {
				item.TabInfo.Name = tabData[info.TabId].Name
				item.TabInfo.ListBackgroundUri = tabData[info.TabId].ShowImageUri
				item.TabInfo.CardMainColor = tabData[info.TabId].NewcomerColor
				item.TabInfo.CardBackgroundUri = tabData[info.TabId].NewcomerImageUri
				item.TabInfo.CardFontBackgroundUri = tabData[info.TabId].NewcomerFontBackgroundUri
				item.TabInfo.CardFontColor = tabData[info.TabId].NewcomerFontColor
				item.TabInfo.RoomLabel = tabData[info.TabId].RoomLabel
			}
			if userMap[owner] != nil {
				item.OwnerUserInfo.Uid = owner
				item.OwnerUserInfo.Account = userMap[owner].Username
				item.OwnerUserInfo.Sex = uint32(userMap[owner].Sex)
				item.OwnerUserInfo.Nickname = userMap[owner].Nickname
			}
			//主题房的是房间人数, 5.1.0的版本改的
			item.ChannelInfo.OnMicCount = info.TotalCount
			item.ChannelInfo.TopicChannelType = topic_channel.TopicChannelInfoV2Type_UGC
			if tabBlockData[info.GetTabId()] != nil && len(tabBlockData[info.GetTabId()].GetBlocks()) > 0 {
				item.ChannelInfo.HeadDesc, item.ChannelInfo.SecondDesc = getChannelDesc(info.GetBlockOptions(), tabBlockData[info.GetTabId()].GetBlocks())
			}
		} else {
			//又不是娱乐房又不是主题房，不返回
			continue
		}
		if s.isAppleSuperVisionSkip(serviceInfo.ClientType, item.ChannelInfo) {
			//苹果监管需要临时写死，带安卓，android字眼不返回
			continue
		}
		if serviceInfo.ClientType == protocol.ClientTypeIOS && s.v2Config.IosReplaceAndroidString {
			item = s.itemV2ReplaceByIOS(item)
		}
		out.Items = append(out.Items, item)
	}
	out.LoadFinish = out.LoadMore == nil
	log.DebugWithCtx(ctx, "ListTopicChannelV2 uid(%v) channelIds(%v) outLen(%d)", userId, listRecommendationInfo.channelIds, len(out.Items))
	return out, nil
}

func (s *Server) HideTopicChannel(ctx context.Context, in *topic_channel.HideTopicChannelReq) (out *topic_channel.HideTopicChannelResp, err error) {
	out = new(topic_channel.HideTopicChannelResp)

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	log.InfoWithCtx(ctx, "HideTopicChannel serviceInfo(%s) in(%v)", serviceInfo, in)
	return out, nil
	/*
		userId := serviceInfo.UserID
		var channelInfoResp *channelPB.GetChannelByIdsResp
		channelInfoResp, err = s.tcChannelClient.GetChannelByIds(ctx, &channelPB.GetChannelByIdsReq{Ids: []uint32{in.ChannelId}})
		if err != nil {
			log.ErrorWithCtx(ctx, "HideTopicChannel GetChannelSimpleInfo channelId(%v) failed, err: %v", in.GetChannelId(), err)
			return
		}

		if len(channelInfoResp.Info) > 0 {
			channelSimpleInfo, rerr := s.channelClient.GetChannelSimpleInfo(ctx, userId, in.ChannelId)
			if rerr != nil {
				log.ErrorWithCtx(ctx, "HideTopicChannel list channel simple info by ids(%v) err(%v)", in.ChannelId, err)
				return out, rerr
			}

			if userId != channelSimpleInfo.GetBindId() {
				err = protocol.NewExactServerError(nil, status.ErrTopicChannelPermissionDenied)
				return
			}

			_, err = s.tcChannelClient.KeepChannelAlive(ctx, &channelPB.KeepChannelAliveReq{ChannelId: in.ChannelId, Status: channelPB.KeepAliveStatus_DISCONNECTED})
			if err != nil {
				log.ErrorWithCtx(ctx, "HideTopicChannel KeepChannelAlive channelId(%v) failed err(%v)", in.GetChannelId(), err)
				return
			}

			var dismissResp *channelPB.DismissChannelResp
			dismissResp, err = s.tcChannelClient.DismissChannel(ctx, &channelPB.DismissChannelReq{ChannelId: in.ChannelId})
			if err != nil {
				log.ErrorWithCtx(ctx, "HideTopicChannel DismissChannel channelId(%v) failed err(%v)", in.GetChannelId(), err)
				return
			}
			if dismissResp.Dismiss {
				//上报到数据中心
				report.ReportDismissChannel(ctx, userId, in.ChannelId, 2)

				//push
				var userInfo *account.User
				userInfo, err = s.accountClient.GetUser(ctx, userId)
				if err != nil {
					log.ErrorWithCtx(ctx, "CreateTopicChannel GetUsersMap userId(%v) err(%v)", userId, err)
					return
				}

				// 为了兼容老版本
				text := "已取消在首页展示，小伙伴无法在首页看到你的房间"
				if in.PushType == topic_channel.HideTopicChannelReq_FINDFRIEND {
					text = ""
					// 同步跟随好友
					nerr := s.fanoutFollowNotifyUtil.FanoutFollowLabelUpdate(ctx, userId, userInfo.Username, "", "", "")
					if nerr != nil {
						log.WarnWithCtx(ctx, "HideTopicChannel FanoutFollowLabelUpdate userId(%v) tabId(%v) err(%v)", serviceInfo.UserID, channelInfoResp.Info[0].TabId, nerr)
					}

				}
				err = s.sendDismissMsg(ctx, userId, in.GetChannelId(), userInfo.Username, userInfo.Nickname, text,
					gaChannelPB.ChannelMsgType_TOPIC_CHANNEL_HIDDEN)
				if err != nil {
					log.ErrorWithCtx(ctx, "HideTopicChannel sendDismissMsg userId(%v) channelId(%v) failed, err: %v", userId, in.GetChannelId(), err)
				}

			}
		}

		return*/
}

func (s *Server) GetRoomProxyTip(ctx context.Context, in *topic_channel.GetRoomProxyTipReq) (out *topic_channel.GetRoomProxyTipResp, err error) {
	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	log.InfoWithCtx(ctx, "GetRoomProxyTip serviceInfo(%s) in(%v)", serviceInfo.String(), in)
	out = new(topic_channel.GetRoomProxyTipResp)
	return out, nil
	/*conf := s.roomHandler.getTip(in.BaseReq.MarketId)
	out.Title = conf.Title
	out.Url = conf.Url
	if _, ok := topic_channel.GetRoomProxyTipResp_ShowType_name[int32(conf.ShowType)]; ok {
		out.ShowType = topic_channel.GetRoomProxyTipResp_ShowType(conf.ShowType)
	} else {
		out.ShowType = topic_channel.GetRoomProxyTipResp_SHOW_ALWAYS
	}
	return*/
}

func (s *Server) KeepAliveTopicChannel(ctx context.Context, in *topic_channel.KeepAliveTopicChannelReq) (out *topic_channel.KeepAliveTopicChannelResp, err error) {
	out = new(topic_channel.KeepAliveTopicChannelResp)
	out.TheEnd = true
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}
	log.InfoWithCtx(ctx, "KeepAliveTopicChannel serviceInfo(%s) in(%v)", serviceInfo.String(), in)
	return out, nil
	/*userId := serviceInfo.UserID


	var channelInfoResp *channelPB.GetChannelByIdsResp
	channelInfoResp, err = s.tcChannelClient.GetChannelByIds(ctx, &channelPB.GetChannelByIdsReq{Ids: []uint32{in.ChannelId}})
	if err != nil {
		log.ErrorWithCtx(ctx, "KeepAliveTopicChannel GetChannelSimpleInfo channelId(%v) failed, err: %v", in.GetChannelId(), err)
		return out, err
	}

	if len(channelInfoResp.Info) > 0 {
		channelSimpleInfo, err := s.channelClient.GetChannelSimpleInfo(ctx, userId, in.ChannelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "KeepAliveTopicChannel list channel simple info by ids(%v) err(%v)", in.ChannelId, err)
			return out, err
		}

		if userId != channelSimpleInfo.GetBindId() {
			err = protocol.NewExactServerError(nil, status.ErrTopicChannelPermissionDenied)
			return out, err
		}

		var resp *channelPB.KeepChannelAliveResp
		resp, err = s.tcChannelClient.KeepChannelAlive(ctx, &channelPB.KeepChannelAliveReq{ChannelId: in.ChannelId, Status: channelPB.KeepAliveStatus_ALIVE})
		if err != nil {
			log.ErrorWithCtx(ctx, "KeepAliveTopicChannel KeepChannelAlive channelId(%v) failed err(%v)", in.GetChannelId(), err)
			return out, err
		}
		out.TheEnd = !resp.IsAlive
	}

	return*/
}

// 首页广告位卡片列表 (搞得越来越复杂了) deprecated
func (s *Server) GetHomePageCardList(ctx context.Context, in *topic_channel.GetHomePageCardListReq) (*topic_channel.GetHomePageCardListResp, error) {
	out := &topic_channel.GetHomePageCardListResp{}
	return out, protocol.NewExactServerError(nil, status.ErrAccountRegLimitedByClientVersion, "请升级至最新版本")
}
