package server

import (
	"context"
	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/demo/echo"
	pb "golang.52tt.com/protocol/services/gangup-channel"
	eventPB "golang.52tt.com/protocol/services/topic_channel/event"
	serverConfig "golang.52tt.com/services/gangup-channel/internal/config"
	"golang.52tt.com/services/gangup-channel/internal/confz"
	"golang.52tt.com/services/gangup-channel/internal/manager/cache"
	"golang.52tt.com/services/gangup-channel/internal/manager/channel"
	channel_mic "golang.52tt.com/services/gangup-channel/internal/manager/channel-mic"
	"golang.52tt.com/services/gangup-channel/internal/manager/feedback"
	"golang.52tt.com/services/gangup-channel/internal/manager/filter"
	"golang.52tt.com/services/gangup-channel/internal/manager/subscriber"
	"golang.52tt.com/services/gangup-channel/internal/rpc/client"
	"golang.52tt.com/services/gangup-channel/internal/storage/mongo"
	"math/rand"
	"time"
)

type GangupChannelServer struct {
	filterMgr        filter.IManager
	feedbackMgr      feedback.IManager
	gangupChannelMgr channel.IGangupChannelMgr
	channelMicMgr    *channel_mic.Manager

	subscriberMgr *subscriber.Manager
}

func (s *GangupChannelServer) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func NewGangupChannelServer(ctx context.Context, sc *serverConfig.ServiceConfig) (*GangupChannelServer, error) {
	log.InfoWithCtx(ctx, "NewGangupChannelServer ServiceConfig:%v", sc)

	err := confz.Setup()
	if err != nil {
		log.ErrorWithCtx(ctx, "Setup config fail: %v ", err)
		return nil, err
	}
	_ = client.Setup()

	err = cache.NewCache(ctx)
	if err != nil {
		return nil, err
	}

	mongoDao, err := mongo.NewMongoDao(ctx, sc.MongoConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewMongoDao error: %v", err)
		return nil, err
	}

	cmder := redis.Cmdable(redis.NewClient(&redis.Options{
		Network:            sc.RedisConfig.Protocol,
		Addr:               sc.RedisConfig.Addr(),
		PoolSize:           sc.RedisConfig.PoolSize,
		IdleCheckFrequency: sc.RedisConfig.IdleCheckFrequency(),
		DB:                 sc.RedisConfig.DB,
	}))
	extraHistoryRedis := redis.Cmdable(redis.NewClient(&redis.Options{
		Network:            sc.ExtraHistoryConfig.Protocol,
		Addr:               sc.ExtraHistoryConfig.Addr(),
		PoolSize:           sc.ExtraHistoryConfig.PoolSize,
		IdleCheckFrequency: sc.ExtraHistoryConfig.IdleCheckFrequency(),
		DB:                 sc.ExtraHistoryConfig.DB,
	}))

	s := &GangupChannelServer{}
	s.gangupChannelMgr, err = channel.NewGangupChannelMgr(ctx, cmder, extraHistoryRedis, sc.Eventlink)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewGangupChannelMgr fail: %v ", err)
		return nil, err
	}

	s.subscriberMgr, err = subscriber.NewSubscriber(ctx, cmder, extraHistoryRedis, sc.Eventlink)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewSubscriber fail: %v ", err)
		return nil, err
	}

	s.filterMgr, err = filter.NewFilterManager(ctx, mongoDao)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewFilterManager fail: %v ", err)
		return nil, err
	}

	s.feedbackMgr, err = feedback.NewManager(ctx, mongoDao)
	if err != nil {
		log.ErrorWithCtx(ctx, "feedback NewManager fail: %v", err)
		return nil, err
	}

	s.channelMicMgr, err = channel_mic.NewChannelMicManager(mongoDao, extraHistoryRedis)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewChannelMicManager fail: %v ", err)
		return nil, err
	}

	rand.Seed(time.Now().UnixNano())
	return s, nil
}

func (s *GangupChannelServer) ShutDown() {
	s.gangupChannelMgr.ShutDown()
	s.subscriberMgr.Close()
}

// 校验id是否不为0,true 不为0
func (s *GangupChannelServer) isNonZeroId(id uint32) bool {
	return id != 0
}

func (s *GangupChannelServer) SetGangupChannelReleaseInfo(ctx context.Context, in *pb.SetGangupChannelReleaseInfoReq) (out *pb.SetGangupChannelReleaseInfoResp, err error) {
	out = &pb.SetGangupChannelReleaseInfoResp{}
	cid := in.GetGangupChannelReleaseInfo().GetId()
	if !s.isNonZeroId(cid) {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelNotFound)
	}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if ok && in.GetGangupChannelReleaseInfo() != nil {
		in.GangupChannelReleaseInfo.TerminalType = serviceInfo.TerminalType
		in.GangupChannelReleaseInfo.MarketId = serviceInfo.MarketID
	} else {
		log.ErrorWithCtx(ctx, "SetGangupChannelReleaseInfo grpc.ServiceInfoFromContext(ctx) err in:%s", in.String())
	}

	current, err := s.gangupChannelMgr.GetChannelInfoById(ctx, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetGangupChannelReleaseInfo GetChannelInfoById error:%v", err)
		return
	}
	if isReleaseChannel(in.GetGangupChannelReleaseInfo().GetDisplayType()) {
		isChange := isReleaseChannel(current.GetDisplayType())
		if isChange { //当前正在发布中，只修改发布信息，不修改发布时间
			in.GangupChannelReleaseInfo.ReleaseTime = current.GetReleaseTime()
			in.GangupChannelReleaseInfo.PushlishUid = in.GetCreator()
			err = s.gangupChannelMgr.SetChannelInfo(ctx, in.GetGangupChannelReleaseInfo())
			if err != nil {
				log.ErrorWithCtx(ctx, "SetGangupChannelReleaseInfo SetChannelInfo error:%v", err)
				return
			}
		} else {
			in.GangupChannelReleaseInfo.ReleaseTime = time.Now().Unix()
			in.GangupChannelReleaseInfo.PushlishUid = in.GetCreator()
			err = s.gangupChannelMgr.ReleaseChannel(ctx, in.GetGangupChannelReleaseInfo())
			if err != nil {
				log.ErrorWithCtx(ctx, "SetGangupChannelReleaseInfo SetChannelInfo error:%v", err)
				return
			}
		}
		err = s.gangupChannelMgr.SendSetChannelEvent(ctx,
			convertTopicChannelEvent(in.GetGangupChannelReleaseInfo(), in.GetWantFresh(), isChange, in.GetReleaseIp(),
				in.GetChannelName(), in.GetCreator(), in.GetAllSelectedBids(), in.GetUnSelectBlockId(), in.GetUnSelectBlockOptions(),
				in.GetChannelPlayMode(), uint32(serviceInfo.ClientType)))
		if err != nil {
			log.ErrorWithCtx(ctx, "SetGangupChannelReleaseInfo SendSetChannelEvent error:%v", err)
			return
		}
	}
	log.InfoWithCtx(ctx, "SetGangupChannelReleaseInfo in:%s, out:%s", in.String(), out.String())
	return out, nil
}

func isReleaseChannel(displayType []pb.ChannelDisplayType) bool {
	for _, v := range displayType {
		if v == pb.ChannelDisplayType_DISPLAY_AT_MAIN_PAGE {
			return true
		}
	}
	return false
}

func (s *GangupChannelServer) DismissGangupChannel(ctx context.Context, in *pb.DismissGangupChannelReq) (out *pb.DismissGangupChannelResp, err error) {
	out = &pb.DismissGangupChannelResp{}
	if !s.isNonZeroId(in.GetChannelId()) {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelNotFound)
	}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	var uid uint32
	if ok && serviceInfo.UserID != 0 {
		uid = serviceInfo.UserID
	}
	if uid == 0 {
		info, err := client.ChannelClient.GetChannelSimpleInfo(ctx, 0, in.GetChannelId())
		if err != nil {
			log.Errorf("DismissGangupChannel error in:%s, err:%+v", in.String(), err)
			return out, err
		}
		uid = info.GetBindId()
	}

	isDismiss, err := s.gangupChannelMgr.DismissChannelById(ctx, in.GetChannelId(), int(in.GetType()), in.GetTabId(), uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "DismissGangupChannel DismissChannelById error:%v", err)
		return out, err
	}
	out.Dismiss = isDismiss
	log.InfoWithCtx(ctx, "DismissGangupChannel uid:%d success in:%s out:%s", uid, in.String(), out.String())
	return
}

func (s *GangupChannelServer) GetGangupChannelList(ctx context.Context, in *pb.GetGangupChannelListReq) (out *pb.GetGangupChannelListResp, err error) {
	out = &pb.GetGangupChannelListResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok || serviceInfo.UserID == 0 {
		log.ErrorWithCtx(ctx, "GetGangupChannelList err, serviceinfo nil:%v", serviceInfo)
		return out, nil
	}
	out.ChannelList, err = s.gangupChannelMgr.GetChannelList(ctx, in.GetTabIdList(), in.GetLimit()+1, serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGangupChannelList GetChannelList tabIdList(%v) error:%v", in.TabIdList, err)
		return out, err
	}

	return
}

func (s *GangupChannelServer) GetGangupChannelByIds(ctx context.Context, in *pb.GetGangupChannelByIdsReq) (out *pb.GetGangupChannelByIdsResp, err error) {
	out = &pb.GetGangupChannelByIdsResp{}
	out.Info, err = s.gangupChannelMgr.GetChannelInfoByIds(ctx, in.GetIds(), in.GetTypes(), in.GetReturnAll())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGangupChannelByIds GetChannelInfoByIds ids(%v) error:%v", in.Ids, err)
		return out, err
	}
	return
}

func (s *GangupChannelServer) GetChannelRoomUserNumber(ctx context.Context, in *pb.GetChannelRoomUserNumberReq) (out *pb.GetChannelRoomUserNumberResp, err error) {
	out = &pb.GetChannelRoomUserNumberResp{}
	out.RoomUserInfo, err = s.gangupChannelMgr.GetChannelRoomUserNumber(ctx, in.GetTabId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelRoomUserNumber GetChannelRoomUserNumber tabIds(%v) error :%v", in.GetTabId(), err)
		return
	}
	return
}

func (s *GangupChannelServer) AddTemporaryChannel(ctx context.Context, in *pb.AddTemporaryChannelReq) (out *pb.AddTemporaryChannelResp, err error) {
	out = &pb.AddTemporaryChannelResp{}
	if !s.isNonZeroId(in.GetChannel().GetId()) {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelNotFound)
	}
	serviceInfo, _ := grpc.ServiceInfoFromContext(ctx)

	if len(in.GetChannel().GetDisplayType()) == 0 {
		in.Channel.DisplayType = append(in.GetChannel().GetDisplayType(), pb.ChannelDisplayType_TEMPORARY)
	}

	err = s.gangupChannelMgr.SetChannelInfo(ctx, in.GetChannel())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddTemporaryChannel SetChannelInfo Channel(%v) error:%v", in.GetChannel(), err)
		return out, err
	}

	err = s.gangupChannelMgr.SendSetChannelEvent(ctx,
		convertTopicChannelEvent(in.GetChannel(), false, false, "", "", 0, nil,
			nil, nil, uint32(channel_play.UgcChannelPlayMode_DEFAULT_VOICE_MODE), uint32(serviceInfo.ClientType)))
	if err != nil {
		log.ErrorWithCtx(ctx, "AddTemporaryChannel SendSetChannelEvent Channel(%v) error:%v", in.GetChannel(), err)
	}

	return
}

func convertTopicChannelEvent(channelInfo *pb.GangupChannelReleaseInfo, wantFresh, isChange bool, releaseIp, channelName string,
	creator uint32, bids, unselectedBids []uint32, unselectedOptions []*pb.BlockOption, channelPlayMode, clientType uint32) (event *eventPB.TopicChannelEvent) {

	selectedBlockOptions := make([]*eventPB.BlockOption, 0, len(channelInfo.BlockOptions))
	allUnselectedBlockOptions := make([]*eventPB.BlockOption, 0, len(unselectedOptions))

	blockOptions := make([]*eventPB.BlockOption, 0, len(channelInfo.BlockOptions)+len(unselectedOptions))
	for _, o := range channelInfo.GetBlockOptions() {
		blockOptions = append(blockOptions, &eventPB.BlockOption{
			BlockId: o.GetBlockId(),
			ElemId:  o.GetElemId(),
			Val:     o.GetElemVal(),
		})
		selectedBlockOptions = append(selectedBlockOptions, &eventPB.BlockOption{
			BlockId: o.GetBlockId(),
			ElemId:  o.GetElemId(),
			Val:     o.GetElemVal(),
		})
	}
	for _, o := range unselectedOptions {
		blockOptions = append(blockOptions, &eventPB.BlockOption{BlockId: o.GetBlockId(), ElemId: o.GetElemId()})
		allUnselectedBlockOptions = append(allUnselectedBlockOptions, &eventPB.BlockOption{
			BlockId: o.GetBlockId(),
			ElemId:  o.GetElemId(),
			Val:     o.GetElemVal(),
		})
	}

	displayType := make([]eventPB.ChannelDisplayType, len(channelInfo.DisplayType))
	for i, d := range channelInfo.DisplayType {
		displayType[i] = eventPB.ChannelDisplayType(d)
	}

	gameLabels := make([]*eventPB.GameLabel, 0, len(channelInfo.GetGameLabels()))
	for _, l := range channelInfo.GetGameLabels() {
		gameLabels = append(gameLabels, &eventPB.GameLabel{
			Val:         l.GetVal(),
			DisplayName: l.GetDisplayName(),
			Type:        eventPB.GameLabelType(l.GetType()),
		})
	}
	tabInfo := cache.GetTabIdCache()[channelInfo.GetTabId()]
	event = &eventPB.TopicChannelEvent{
		ChannelId:                 channelInfo.GetId(),
		TabId:                     channelInfo.GetTabId(),
		BlockOptions:              blockOptions,
		Action:                    eventPB.TopicChannelEvent_CREATE,
		DisplayType:               displayType,
		WantFresh:                 wantFresh,
		IsChange:                  isChange,
		ReleaseTime:               uint32(channelInfo.ReleaseTime),
		ReleaseIp:                 releaseIp,
		ShowGeoInfo:               channelInfo.ShowGeoInfo,
		Name:                      channelName,
		Creator:                   creator,
		AllSelectedBids:           bids,
		UserUnSelectedBids:        unselectedBids,
		ChannelPlayMode:           channelPlayMode,
		CategoryType:              tabInfo.GetCategoryMapping(),
		GameLabels:                gameLabels,
		ClientType:                clientType,
		SelectedBlockOptions:      selectedBlockOptions,
		AllUnselectedBlockOptions: allUnselectedBlockOptions,
	}
	log.Debugf("convertTopicChannelEvent AllSelectedBids:%v, UserUnSelectedBids:%v, len(blockOptions):%d, len(unselectedOptions):%d, len(options):%d, len(gameLabels):%d",
		bids, unselectedBids, len(channelInfo.GetBlockOptions()), len(unselectedOptions), len(blockOptions), len(gameLabels))
	return
}

func (s *GangupChannelServer) SwitchChannelTab(ctx context.Context, in *pb.SwitchChannelTabReq) (out *pb.SwitchChannelTabResp, err error) {
	out = &pb.SwitchChannelTabResp{}
	if !s.isNonZeroId(in.GetChannelId()) {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelNotFound)
	}
	tabInfo, err := s.gangupChannelMgr.SwitchChannelTab(ctx, in.GetUid(), in.GetChannelId(), in.GetTabId(), in.GetSwitchTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "SwitchChannelTab SwitchChannelTab channelId (%d) tabId(%d) error:%v", in.GetChannelId(), in.GetTabId(), err)
		return out, err
	}

	out.WelcomeTxtList = tabInfo.GetNewcomerWelcome()
	out.TabName = tabInfo.GetName()
	out.TagId = tabInfo.GetTagId()
	out.TabType = uint32(tabInfo.GetTabType())
	out.MicMod = tabInfo.GetMicMod()
	log.InfoWithCtx(ctx, "SwitchChannelTab success in:%s, out:%s", in.String(), out.String())
	return out, nil
}

func (s *GangupChannelServer) GetOnlineInfo(ctx context.Context, in *pb.GetOnlineInfoReq) (out *pb.GetOnlineInfoResp, err error) {
	out = &pb.GetOnlineInfoResp{}
	out.RoomCount, err = s.gangupChannelMgr.GetTotalChannelCount(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOnlineInfo GetTotalChannelCount :%v", err)
		return out, err
	}
	return out, nil
}

func (s *GangupChannelServer) NegativeFeedBack(ctx context.Context, in *pb.NegativeFeedBackReq) (out *pb.NegativeFeedBackResp, err error) {
	log.DebugWithCtx(ctx, "NegativeFeedBack in(%s)", in.String())
	out = &pb.NegativeFeedBackResp{}
	options := make([]*eventPB.BlockOption, len(in.GetBlockOptions()))
	for i, o := range in.GetBlockOptions() {
		options[i] = &eventPB.BlockOption{BlockId: o.GetBlockId(), ElemId: o.GetElemId()}
	}

	typeList := make([]eventPB.NegativeFeedbackType, len(in.GetNegative_FeedbackType()))

	for i, feedbackType := range in.GetNegative_FeedbackType() {
		typeList[i] = eventPB.NegativeFeedbackType(feedbackType)
	}
	event := &eventPB.NegativeFeedbackEvent{
		ChannelId:                    in.GetChannelId(),
		TabId:                        in.GetTabId(),
		Creator:                      in.GetCreator(),
		BlockOptions:                 options,
		Name:                         in.GetName(),
		Negative_FeedbackType:        typeList,
		ReporterUid:                  in.GetReporterUid(),
		ExcludeWordsOfRoom:           in.GetChannelNameKeywords(),
		ReasonsOfBlackingCreator:     in.GetChannelOwnerReasons(),
		BlackChannelUser:             in.GetBlackChannelUser(),
		BlackChannelUserEnableFilter: in.GetBlackChannelUserEnableFilter(),
		ReasonsOfBlackChannelUser:    in.GetReasonsOfBlackChannelUser(),
	}

	err = s.gangupChannelMgr.SendNegativeFeedbackEvent(ctx, event)

	return
}

func (s *GangupChannelServer) CleanChannelInfo(ctx context.Context, in *pb.CleanChannelInfoReq) (out *pb.CleanChannelInfoResp, err error) {
	out = &pb.CleanChannelInfoResp{}
	if !s.isNonZeroId(in.GetChannelId()) {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelNotFound)
	}
	info, err := s.gangupChannelMgr.GetChannelInfoById(ctx, in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "CleanChannelInfo GetChannelInfoById channelId(%d) error:%v", in.GetChannelId(), err)
		return out, err
	}
	err = s.gangupChannelMgr.CleanChannelInfo(ctx, in.GetChannelId(), info.GetTabId())
	if err != nil {
		log.ErrorWithCtx(ctx, "CleanChannelInfo CleanChannelInfo channelId(%d) tabId(%d) error:%v", in.GetChannelId(), info.GetTabId(), err)
		return out, err
	}
	return
}

func (s *GangupChannelServer) GetChannelIdsByTabId(ctx context.Context, in *pb.GetChannelIdsByTabIdReq) (out *pb.GetChannelIdsByTabIdResp, err error) {
	out = &pb.GetChannelIdsByTabIdResp{}
	if !s.isNonZeroId(in.GetTabId()) {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelTabNotFound)
	}
	switch in.GetGetMode() {
	case pb.GetChannelIdsByTabIdReq_ALL:
		out.ChannelIds, err = s.gangupChannelMgr.ListActiveChannelIdsByTabId(ctx, in.GetTabId())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelIdsByTabId ListSinkChannelIdsByTabId tabId:%d error:%v", in.GetTabId(), err)
			return out, err
		}
	case pb.GetChannelIdsByTabIdReq_ONLY_RELEASE:
		out.ChannelIds, err = s.gangupChannelMgr.ListReleaseChannelIdsByTabId(ctx, in.GetTabId())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelIdsByTabId ListReleaseChannelIdsByTabId tabId:%d error:%v", in.GetTabId(), err)
			return out, err
		}
	default:
		return out, nil
	}

	return out, nil
}

func (s *GangupChannelServer) SetGangupChannelDenoiseMode(ctx context.Context, in *pb.SetGangupChannelDenoiseModeReq) (out *pb.SetGangupChannelDenoiseModeResp, err error) {
	out = new(pb.SetGangupChannelDenoiseModeResp)
	log.InfoWithCtx(ctx, "SetGangupChannelDenoiseMode in: %+v", in)
	if !s.isNonZeroId(in.GetChannelId()) {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelNotFound)
	}
	err = s.gangupChannelMgr.SetChannelDenoiseMode(ctx, in.GetChannelId(), in.GetMode())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetGangupChannelDenoiseMode SetChannelDenoiseMode cid(%d) err: %v", in.GetChannelId(), err)
		return
	}

	return
}

func (s *GangupChannelServer) GetGangupChannelExtraInfo(ctx context.Context, in *pb.GetGangupChannelExtraInfoReq) (out *pb.GetGangupChannelExtraInfoResp, err error) {
	out = new(pb.GetGangupChannelExtraInfoResp)
	log.DebugWithCtx(ctx, "GetGangupChannelExtraInfo in: %+v", in)
	if !s.isNonZeroId(in.GetChannelId()) {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelNotFound)
	}
	out.Info, err = s.gangupChannelMgr.GetChannelExtraInfo(ctx, in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGangupChannelExtraInfo GetChannelExtraInfo cid(%d) err: %v", in.GetChannelId(), err)
		return
	}

	log.DebugWithCtx(ctx, "GetGangupChannelExtraInfo out: %+v", out)
	return
}

func (s *GangupChannelServer) GetGangupExtraHistory(ctx context.Context, in *pb.GetGangupExtraHistoryReq) (out *pb.GetGangupExtraHistoryResp, err error) {
	out = new(pb.GetGangupExtraHistoryResp)
	log.DebugWithCtx(ctx, "GetGangupExtraHistory in: %+v", in)
	if !s.isNonZeroId(in.GetCid()) {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelNotFound)
	}
	value, err := s.gangupChannelMgr.GetExtraHistory(ctx, in.GetCid(), in.GetIsRelease())
	if err != nil {
		log.ErrorWithCtx(ctx, " GetGangupExtraHistory in(%s), err(%v)", in.String(), err)
		return out, err
	}
	out.Value = value
	log.DebugWithCtx(ctx, "GetGangupExtraHistory out: %+v", out)

	return out, nil
}

func (s *GangupChannelServer) SetGangupExtraHistory(ctx context.Context, in *pb.SetGangupExtraHistoryReq) (out *pb.SetGangupExtraHistoryResp, err error) {
	out = new(pb.SetGangupExtraHistoryResp)
	log.DebugWithCtx(ctx, "SetGangupExtraHistory in: %+v", in)
	if !s.isNonZeroId(in.GetCid()) {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelNotFound)
	}
	err = s.gangupChannelMgr.SetExtraHistory(ctx, in.GetCid(), in.GetValue(), in.GetExpireAfter(), in.GetIsRelease())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetGangupExtraHistory in(%s) err(%v)", in.String(), err)
		return out, err
	}
	log.DebugWithCtx(ctx, "SetGangupExtraHistory out: %+v", out)

	return out, nil
}

func (s *GangupChannelServer) GetGangupPublishCountHistory(ctx context.Context, in *pb.GetGangupPublishCountHistoryReq) (out *pb.GetGangupPublishCountHistoryResp,
	err error) {
	out = new(pb.GetGangupPublishCountHistoryResp)
	log.DebugWithCtx(ctx, "GetGangupPublishCountHistory in: %+v", in)
	if !s.isNonZeroId(in.GetCid()) {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelNotFound)
	}
	num, err := s.gangupChannelMgr.GetPublishCountHistory(ctx, in.GetCid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGangupPublishCountHistory in(%s) err(%v)", in.String(), err)
		return out, err
	}
	out.PublishCount = uint32(num)
	log.DebugWithCtx(ctx, "GetGangupPublishCountHistory out: %+v", out)

	return out, nil
}

func (s *GangupChannelServer) SetGangupPublishCountHistory(ctx context.Context, in *pb.SetGangupPublishCountHistoryReq) (out *pb.SetGangupPublishCountHistoryResp,
	err error) {
	out = new(pb.SetGangupPublishCountHistoryResp)
	log.DebugWithCtx(ctx, "SetGangupPublishCountHistory in: %+v", in)
	if !s.isNonZeroId(in.GetCid()) {
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelNotFound)
	}
	err = s.gangupChannelMgr.SetPublishCountHistory(ctx, in.GetCid())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetGangupPublishCountHistory in(%s) err(%v)", in.String(), err)
		return out, err
	}
	log.DebugWithCtx(ctx, "SetGangupPublishCountHistory out: %+v", out)

	return out, nil
}

func (s *GangupChannelServer) GetReleasingChannelCountByTabIds(ctx context.Context,
	in *pb.GetReleasingChannelCountByTabIdsReq) (out *pb.GetReleasingChannelCountByTabIdsResp, err error) {

	out = &pb.GetReleasingChannelCountByTabIdsResp{}

	countMap, err := s.gangupChannelMgr.GetReleasingChannelCountByTabIds(ctx, in.GetTabIds())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReleasingChannelCountByTabIds error:%v, in:%v", err, in)
		return
	}
	out.CountMap = countMap
	log.DebugWithCtx(ctx, "GetReleasingChannelCountByTabIds in:%v, out:%+v", in, out)

	return out, nil
}

// GetReleasingChannelRandomCountByTabIds 真实数+随机数
func (s *GangupChannelServer) GetReleasingChannelRandomCountByTabIds(ctx context.Context,
	in *pb.GetReleasingChannelCountByTabIdsReq) (out *pb.GetReleasingChannelCountByTabIdsResp, err error) {

	out = &pb.GetReleasingChannelCountByTabIdsResp{}

	countMap, err := s.gangupChannelMgr.GetReleasingChannelRandomCountByTabIds(ctx, in.GetTabIds())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReleasingChannelCountByTabIds error:%v, in:%v", err, in)
		return
	}
	out.CountMap = countMap
	log.DebugWithCtx(ctx, "GetReleasingChannelCountByTabIds in:%v, out:%+v", in, out)

	return out, nil
}

func (s *GangupChannelServer) ListPublishingChannelIds(ctx context.Context, req *pb.ListPublishingChannelIdsReq) (out *pb.ListPublishingChannelIdsResp, err error) {
	out = &pb.ListPublishingChannelIdsResp{}
	out.Channels, err = s.gangupChannelMgr.ListPublishingChannelIds(req.GetTabId(), req.GetLimit(), req.GetTimeOffset())
	if err != nil {
		log.ErrorWithCtx(ctx, "ListPublishingChannelIds:%+v", err)
		return out, err
	}
	return out, nil
}
