package user_rate

import (
	"context"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/http"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/device_id"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/foundation/http/ttauth"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/seqgen"
	account_go "golang.52tt.com/clients/account-go"
	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/web"
	"golang.52tt.com/pkg/web/middleware"
	"golang.52tt.com/protocol/app/sync"
	"golang.52tt.com/protocol/common/status"
	cybros_arbiter_v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	"golang.52tt.com/protocol/services/expsvr"
	api "golang.52tt.com/protocol/services/game-http-logic"
	game_pal "golang.52tt.com/protocol/services/game-pal"
	pb "golang.52tt.com/protocol/services/game-user-rate"
	missiontimeline "golang.52tt.com/protocol/services/missiontimelinesvr"
	pushPb "golang.52tt.com/protocol/services/push-notification/v2"
	risk_mng_api "golang.52tt.com/protocol/services/risk-mng-api"
	config "golang.52tt.com/services/game-http-logic/internal/config/ttconfig/game_http_logic"
	"golang.52tt.com/services/game-http-logic/internal/models/cache"
	"golang.52tt.com/services/game-http-logic/internal/rpc"
	"io/ioutil"
	"net"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"
)

const (
	sceneCode = "2200"

	oneMinute = 60
	oneHour   = 3600
	twoDay    = 3600 * 48

	// 好评
	goodRate = 1
	badRate  = 2

	addExpMaxNum      = 15
	likeRateAddExpVal = 50
)

func GetGameUserBeRateList(auth *ttauth.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), 3*time.Second)
	defer cancel()

	data, err := ioutil.ReadAll(r.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameUserBeRateList read body err: %v", err)
		_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
		return
	}

	var req api.GetGameUserBeRateListReq
	err = json.Unmarshal(data, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameUserBeRateList unmarshal body err: %v", err)
		_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
		return
	}
	log.InfoWithCtx(ctx, "GetGameUserBeRateList uid:%d, body data:%+v", auth.UserID, req)

	resp, rpcErr := rpc.GameUserRateClient.GetGameUserBeRateList(ctx, &pb.GetGameUserBeRateListReq{
		Uid:            auth.UserID,
		LastRecordTime: req.GetLastRecordTime(),
	})
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "GetGameUserBeRateList GameUserRateClient.GetGameUserBeRateList fail, err:%v, uid:%d", rpcErr, auth.UserID)
		_ = web.ServeAPICodeJson(w, status.ErrSys, rpcErr.Message(), nil)
		return
	}

	out := &api.GetGameUserBeRateListResp{
		Items:      make([]*api.GameUserBeRateItem, 0, len(resp.GetItems())),
		LoadFinish: resp.GetLoadFinish(),
	}

	rateUids := make([]uint32, 0, len(resp.GetItems()))
	for _, v := range resp.GetItems() {
		if v.GetItemType() == 1 {
			rateUids = append(rateUids, v.GetLikeItem().GetUid())
		}
	}
	// 评价用户信息
	userInfoMap, err := rpc.AccountGoClient.GetUsersMap(ctx, rateUids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameUserBeRateList GetUsersMap err:%v, rateUids:%+v", err, rateUids)
		_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
		return
	}

	// 关注状态信息
	userFollowMap, _, err := rpc.FriendshipClient.BatchGetBiFollowingWithCache(ctx, auth.UserID, rateUids, true, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameUserBeRateList BatchGetBiFollowingWithCache fail, err:%v, rateUids:%v", err, rateUids)
		_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
		return
	}

	for _, v := range resp.GetItems() {
		item := &api.GameUserBeRateItem{
			ItemType: v.GetItemType(),
		}
		if v.GetItemType() == 1 {
			item.Content = &api.GameUserBeRateItem_LikeItem_{
				LikeItem: &api.GameUserBeRateItem_LikeItem{
					Id:           v.GetLikeItem().GetId(),
					Uid:          v.GetLikeItem().GetUid(),
					Account:      userInfoMap[v.GetLikeItem().GetUid()].GetUsername(),
					Nickname:     userInfoMap[v.GetLikeItem().GetUid()].GetNickname(),
					Sex:          userInfoMap[v.GetLikeItem().GetUid()].GetSex(),
					IsFollow:     userFollowMap[v.GetLikeItem().GetUid()],
					RateTimeText: genLikeItemRateTimeText(v.GetLikeItem().GetCreateTime()),
					CreateTime:   v.GetLikeItem().GetCreateTime(),
					Tags:         v.GetLikeItem().GetSelectTags(),
					UserRateText: v.GetLikeItem().GetUserRateText(),
				},
			}
		} else {
			var deductReason string
			for _, deductDetail := range v.GetDeductItem().GetDeductDetail() {
				deductReason += fmt.Sprintf("%s-%d，", deductDetail.GetTagName(), deductDetail.GetScore())
			}
			item.Content = &api.GameUserBeRateItem_DeductItem_{
				DeductItem: &api.GameUserBeRateItem_DeductItem{
					Id:         v.GetDeductItem().GetId(),
					Title:      config.GetGameHttpLogicConfig().GetGameBerateDeductTitleText(),
					Subtitle:   fmt.Sprintf(config.GetGameHttpLogicConfig().GetGameBerateDeductSubtitleText(), deductReason, v.GetDeductItem().GetCurScore()),
					CreateTime: v.GetDeductItem().GetCreateTime(),
				},
			}
		}
		out.Items = append(out.Items, item)
	}

	_ = web.ServeAPIJsonV2(w, out)
}

func genLikeItemRateTimeText(rateTimeMs int64) string {
	internalMs := time.Now().UnixMilli() - rateTimeMs
	var timeText string
	if internalMs < time.Minute.Milliseconds() {
		timeText = "刚刚Ta给你点赞了"
	} else if internalMs < time.Hour.Milliseconds() {
		timeText = fmt.Sprintf("%d分钟前Ta给你点赞了", internalMs/time.Minute.Milliseconds())
	} else if internalMs < 12*time.Hour.Milliseconds() {
		timeText = fmt.Sprintf("%d小时前Ta给你点赞了", internalMs/time.Hour.Milliseconds())
	} else {
		timeText = fmt.Sprintf("%sTa给你点赞了", time.UnixMilli(rateTimeMs).Format("1月2日 15:04"))
	}
	return timeText
}

// GetGameUserRateList 获取用户评价列表
func GetGameUserRateList(auth *ttauth.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), 3*time.Second)
	defer cancel()

	data, err := ioutil.ReadAll(r.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameUserRateList read body err: %v", err)
		_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
		return
	}

	var req api.GetGameUserRateListReq
	err = json.Unmarshal(data, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameUserRateList unmarshal body err: %v", err)
		_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
		return
	}

	log.InfoWithCtx(ctx, "GetGameUserRateList req, uid:%d,  body data:%+v", auth.UserID, req)

	resp, err := rpc.GameUserRateClient.GetGameUserRateList(ctx, &pb.GetGameUserRateListReq{
		Uid:     auth.UserID,
		Source:  pb.GameUserRateSourceType(req.GetSource()),
		RateUid: req.GetRateUid(),
		LastId:  req.GetLastId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameUserRateList fail, uid:%d, err:%v, req:%+v", auth.UserID, err, req)
		_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
		return
	}

	rateUids := make([]uint32, 0, len(resp.GetData()))
	for _, t := range resp.GetData() {
		rateUids = append(rateUids, t.GetRateUid())
	}
	userMap, err := rpc.AccountGoClient.GetUsersMap(ctx, rateUids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameUserRateList GetUsersMap err:%v, rateUids:%+v", err, rateUids)
		_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
		return
	}

	// 关注状态信息
	userFollow, _, err := rpc.FriendshipClient.BatchGetBiFollowingWithCache(ctx, auth.UserID, rateUids, true, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGamePalList BatchGetBiFollowingWithCache fail, err:%v, uids:%v", err, rateUids)
		_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
		return
	}

	out := &api.GetGameUserRateListResp{
		LikeText:               config.GetGameHttpLogicConfig().GetGameUserRateLikeText(),
		DislikeText:            config.GetGameHttpLogicConfig().GetGameUserRateDislikeText(),
		LoadFinish:             resp.GetLoadFinish(),
		MaxPositiveSelectCount: cache.GetRateLabelConf().GetMaxPositiveSelectCount(),
		MaxNegativeSelectCount: cache.GetRateLabelConf().GetMaxNegativeSelectCount(),
	}
	out.Data = make([]*api.GameUserRateItem, 0, len(resp.GetData()))
	for _, t := range resp.GetData() {
		user, ok := userMap[t.GetRateUid()]
		if !ok {
			log.InfoWithCtx(ctx, "GetGameUserRateList user not found filter, uid:%d", t.GetRateUid())
			continue
		}
		tags := make([]*api.GameUserRateTag, 0, len(t.GetTags()))
		for _, tag := range t.GetTags() {
			tags = append(tags, &api.GameUserRateTag{
				Id:      tag.GetId(),
				Name:    tag.GetName(),
				TagType: tag.GetTagType(),
			})
		}
		out.Data = append(out.Data, &api.GameUserRateItem{
			Id:           t.GetId(),
			Uid:          t.GetRateUid(),
			Account:      user.GetUsername(),
			Nickname:     user.GetNickname(),
			Sex:          uint32(user.GetSex()),
			IsFollow:     userFollow[t.GetRateUid()],
			IntroTips:    getIntroTipsText(t.GetTabId(), t.GetStatus(), uint32(t.GetSource()), t.GetCreateTime()),
			Status:       uint32(t.GetStatus()),
			Attitude:     t.GetAttitude(),
			Tags:         tags,
			CreateTime:   t.GetCreateTime(),
			UserRateText: t.GetUserRateText(),
		})
	}

	_ = web.ServeAPIJsonV2(w, out)
}

func getIntroTipsText(tabId uint32, status pb.GameUserRateStatus, source uint32, createTimeMx uint64) string {
	now := time.Now()
	internal := (uint64(now.UnixMilli()) - createTimeMx) / 1000

	tabName := cache.GetTabNameById(tabId)
	if source == uint32(pb.GameUserRateSourceType_GAME_USER_RATE_SOURCE_TYPE_IM_CHAT) {
		if status == pb.GameUserRateStatus_GAME_USER_RATE_STATUS_EXPIRED {
			return "之前跟Ta聊过天"
		}

		if internal < oneMinute {
			return "刚刚跟Ta聊过天"

		} else if internal < oneHour {
			return fmt.Sprintf("%d分钟前跟Ta聊过天", internal/60)

		} else if internal < twoDay {
			str := now.Add(-time.Duration(internal) * time.Second).Format("1月2日 15:04")
			return fmt.Sprintf("%s跟Ta聊过天", str)

		} else {
			return "之前跟Ta聊过天"
		}

	} else {
		if status == pb.GameUserRateStatus_GAME_USER_RATE_STATUS_EXPIRED {
			return fmt.Sprintf("之前在%s一起玩过", tabName)
		}

		if internal < oneMinute {
			return fmt.Sprintf("刚刚在%s一起玩过", tabName)

		} else if internal < oneHour {
			return fmt.Sprintf("%d分钟前在%s一起玩过", internal/60, tabName)

		} else if internal < twoDay {
			str := now.Add(-time.Duration(internal) * time.Second).Format("1月2日 15:04")
			return fmt.Sprintf("%s在%s一起玩过", str, tabName)

		} else {
			return fmt.Sprintf("之前在%s一起玩过", tabName)
		}
	}
}

// SubmitGameUserRate 提交用户评价
func SubmitGameUserRate(auth *ttauth.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), 3*time.Second)
	defer cancel()

	out := &api.SubmitGameUserRateResp{}

	data, err := ioutil.ReadAll(r.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "SubmitGameUserRate read body err: %v", err)
		_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
		return
	}

	var req api.SubmitGameUserRateReq
	err = json.Unmarshal(data, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "SubmitGameUserRate unmarshal body err: %v, body:%s", err, string(data))
		_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
		return
	}
	log.InfoWithCtx(ctx, "SubmitGameUserRate req, uid:%d, body data:%+v", auth.UserID, req)

	if req.GetId() == "" || req.GetAttitude() == 0 || (len(req.GetSelectTags()) == 0 && req.GetUserRateText() == "") {
		_ = web.ServeAPICodeJson(w, status.ErrSys, "req fail, param invalid", nil)
		return
	}
	// 用户评价只取前15个字数
	req.UserRateText = getContent(req.GetUserRateText(), 15)

	// 接风控和T盾送审
	ctx = buildServiceInfo(ctx, auth.UserID, req.GetBaseRequest(), r)
	rateInfoRsp, err := rpc.GameUserRateClient.GetGameUserRateById(ctx, &pb.GetGameUserRateByIdReq{Id: req.GetId()})
	if err != nil {
		log.ErrorWithCtx(ctx, "SubmitGameUserRate GetGameUserRateById fail, uid:%d, err:%v, req:%+v", auth.UserID, err, req)
		return
	}
	loginInfo, err := rpc.UserHistoryCli.GetUserLastLoginInfo(ctx, uint64(rateInfoRsp.GetData().GetRateUid()))
	if err != nil {
		log.ErrorWithCtx(ctx, "SubmitGameUserRate GetUserLastLoginInfo fail, uid:%d, err:%v, req:%+v", auth.UserID, err, req)
	}

	hasRisk := senderRiskCheck(ctx, auth.UserID, rateInfoRsp.GetData().GetRateUid(), loginInfo.GetDeviceId(), req.GetUserRateText())
	out.IsShieldPass = true
	if len(req.GetUserRateText()) > 0 {
		accountInfo, aErr := rpc.AccountGoClient.GetUserByUid(ctx, auth.UserID)
		if aErr != nil {
			log.ErrorWithCtx(ctx, "SubmitGameUserRate GetUserByUid fail, uid:%d, err:%v, req:%+v", auth.UserID, aErr, req)
			out.IsShieldPass = false
			out.NotPassText = "您评价Ta的言论存在风险，评价已删除~"
		}
		if syncScanText(ctx, accountInfo, req.GetUserRateText()) {
			out.IsShieldPass = false
			out.NotPassText = "您评价Ta的言论存在风险，评价已删除~"
		}
	}

	selectTags := make([]*pb.GameUserRateTag, 0, len(req.GetSelectTags()))
	for _, t := range req.GetSelectTags() {
		selectTags = append(selectTags, &pb.GameUserRateTag{
			Id:      t.GetId(),
			Name:    t.GetName(),
			TagType: t.GetTagType(),
		})
	}
	rsp, err := rpc.GameUserRateClient.SubmitGameUserRate(ctx, &pb.SubmitGameUserRateReq{
		Id:           req.GetId(),
		Attitude:     req.GetAttitude(),
		SelectTags:   selectTags,
		Uid:          auth.UserID,
		UserRateText: req.GetUserRateText(),
		IsShieldPass: out.IsShieldPass,
		HasRisk:      hasRisk,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SubmitGameUserRate fail, uid:%d, err:%v, req:%+v", auth.UserID, err, req)
		_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
		return
	}
	if rsp.GetIsNeedAddExp() {
		// 发送经验值
		_ = addExp(ctx, req.GetId(), auth.UserID, addExpMaxNum)
	}

	if rsp.GetIsRateUserNeedAddExp() {
		// 给收到好评用户发送经验值
		_ = addExp(ctx, req.GetId(), rsp.GetItem().GetRateUid(), likeRateAddExpVal)
	}

	// 加他评算分
	go func() {
		_ = addOtherRateScore(context.Background(), rsp.GetItem())
	}()

	_ = web.ServeAPIJsonV2(w, out)
}

func getContent(origin string, nickLen int) string {
	c := utf8.RuneCountInString(origin)
	if c > nickLen {
		return fmt.Sprintf("%s...", string([]rune(origin)[:nickLen]))
	} else {
		return origin
	}
}

func addExp(ctx context.Context, id string, uid uint32, addExp int32) error {
	// 发送经验值
	_, rpcErr := rpc.ExpClient.AddUserExpV2(ctx, uid, &expsvr.AddUserExpReq{
		Uid:         uid,
		Exp:         addExp,
		MissionKey:  fmt.Sprintf("game_user_rate_%d_%s", uid, id),
		MissionDesc: "开黑用户评价",
	})
	if rpcErr != nil {
		if rpcErr.Code() != status.ErrGrowExpAdded {
			return nil
		}
		log.ErrorWithCtx(ctx, "AddUserExpV2 uid(%d) fail, err: %v", uid, rpcErr)
	}
	go func() {
		_ = updateGrowTimeline(uid)
	}()

	return nil
}

// UpdateGrowTimeline 经验、红钻变更写入mission timeline
func updateGrowTimeline(uid uint32) error {
	ctx := context.Background()
	defer func() {
		if e := recover(); e != nil {
			log.ErrorWithCtx(ctx, "updateGrowTimeline panic:%+v", e)
		}
	}()

	// 经验、红钻写入mission timeline
	{
		exp, level, err := rpc.ExpClient.GetUserExp(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "updateGrowTimeline GetUserExp uid(%d) err: %v", uid, err)
			return err
		}

		currency, err := rpc.CurrencyClient.GetUserCurrency(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateGrowTimeline GetUserCurrency uid(%d) err: %v", uid, err)
			return err
		}

		minExp, maxExp, err := rpc.ExpClient.GetLevelExpScope(ctx, uid, level)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateGrowTimeline GetLevelExpScope uid(%d) err: %v", uid, err)
			return err
		}

		seq, err := rpc.SeqGenClient.GenerateSequence(ctx, uid, seqgen.NamespaceUser, seqgen.KeyGrowth, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateGrowTimeline GenerateSequence uid(%d) err: %s", uid, err)
			return err
		}

		err = rpc.MissionTLClient.ExpCurrencyChanged(ctx, uid, uint32(seq), &missiontimeline.GrowInfoMessage{
			Exp:                exp,
			Level:              level,
			Currency:           currency,
			CurrentLevelExpMin: minExp,
			CurrentLevelExpMax: maxExp,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateGrowTimeline ExpCurrencyChanged uid(%d) err: %v", uid, err)
			return err
		}
	}

	// push grow sync
	{
		var b [4]byte
		binary.BigEndian.PutUint32(b[:], uint32(sync.SyncReq_GROW))

		pushResp, err := rpc.PushClient.PushToUsers2(ctx, []uint32{uid}, &pushPb.CompositiveNotification{
			Sequence:           pushSeq(),
			TerminalTypePolicy: push.DefaultPolicy,
			ProxyNotification: &pushPb.ProxyNotification{
				Type:    uint32(pushPb.ProxyNotification_NOTIFY),
				Payload: b[:],
			},
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateGrowTimeline PushToUsers2 uid(%d) err: %v", uid, err)
			return err
		}

		log.InfoWithCtx(ctx, "UpdateGrowTimeline PushToUsers2 uid(%d) pushResp(%+v) finished", uid, pushResp)
	}

	return nil
}

func pushSeq() uint32 {
	return uint32((time.Now().UnixMilli()) & 0xffffffff)
}

// 发送用户评价分数变化事件
func addOtherRateScore(ctx context.Context, item *pb.GameUserRateItem) error {
	if item == nil {
		return nil
	}
	defer func() {
		if e := recover(); e != nil {
			log.ErrorWithCtx(ctx, "AddOtherRateScore panic:%+v", e)
		}
	}()

	if item.GetStatus() != pb.GameUserRateStatus_GAME_USER_RATE_STATUS_RATED || item.GetUid() == 0 || item.GetRateUid() == 0 || item.GetTabId() == 0 {
		log.InfoWithCtx(ctx, "AddOtherRateScore item status not rated or uid is 0 or rateUid is 0 or tabId is 0, item:%+v", item)
		return nil
	}

	otherRateMsg := getOtherRateScoreReq(ctx, item)
	log.InfoWithCtx(ctx, "AddOtherRateScore info, item:%+v, otherRateMsg:%+v", item, otherRateMsg)
	if len(otherRateMsg) == 0 {
		return nil
	}
	_, err := rpc.GameUserRateClient.AddOtherRate(ctx, &pb.AddOtherRateReq{
		OtherRateMsg: otherRateMsg,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddOtherRateScore fail, err:%v, req:%+v", err, otherRateMsg)
		return err
	}
	return nil
}

func getOtherRateScoreReq(ctx context.Context, item *pb.GameUserRateItem) []*pb.OtherRateMsg {
	res := make([]*pb.OtherRateMsg, 0, len(item.GetTags()))
	dimensionIds := make([]string, 0, len(item.GetTags()))
	for _, tagInfo := range item.GetTags() {
		if tagInfo.GetId() == "" {
			continue
		}
		dimensionIds = append(dimensionIds, tagInfo.GetId())
	}
	if len(dimensionIds) == 0 {
		log.InfoWithCtx(ctx, "AddOtherRateScore game pal dimensionIds is empty, item:%+v", item)
		return res
	}
	// 获取所有评价的维度所有玩法的情况，都要加分
	rsp, err := rpc.GameUserRateClient.GetTabByDimensionIds(ctx, &pb.GetTabByDimensionIdsReq{
		Ids: dimensionIds,
	})
	if err != nil {
		// 错误不直接返回，因为本身评价的玩法还是要加分的
		log.ErrorWithCtx(ctx, "GetTabByDimensionIds fail, err:%v, dimensionId:%v, item:%+v", err, dimensionIds, item)
	}

	now := time.Now().Unix()
	tabIds := make([]uint32, 0, len(rsp.GetDimensionTabMap()))
	tabIds = append(tabIds, item.GetTabId())
	// 某个玩法下面有哪些维度
	assistTempMap := make(map[uint32][]string, len(rsp.GetDimensionTabMap()))
	assistTempMap[item.GetTabId()] = dimensionIds
	for DimId, rspItem := range rsp.GetDimensionTabMap() {
		for _, tabId := range rspItem.GetTabIds() {
			if tabId == 0 || tabId == item.GetTabId() {
				continue
			}
			if _, ok := assistTempMap[tabId]; !ok {
				tabIds = append(tabIds, tabId)
			}
			assistTempMap[tabId] = append(assistTempMap[tabId], DimId)
		}
	}
	// 获取这些玩法该用户的搭子卡
	gamePalMap := getGamePalFilter(ctx, item.GetRateUid(), tabIds)
	// 获取这些玩法该用户的形象数据
	userRateImageMap := getImageFilter(ctx, item.GetRateUid(), tabIds)

	for tabId, dimIds := range assistTempMap {
		oId := fmt.Sprintf("%d_%d", item.GetRateUid(), tabId)
		gamePalLightenTime, ok := gamePalMap[oId]
		// 没有搭子的不加分
		if !ok {
			log.InfoWithCtx(ctx, "AddOtherRateScore game pal not found, gamePalId:%s, tabId:%d, item:%+v", oId, tabId, item)
			continue
		}
		// 没有自评的不加分
		if _, ok = userRateImageMap[oId]; !ok {
			log.InfoWithCtx(ctx, "AddOtherRateScore userRateImageMap not found, userRateId:%s, tabId:%d, item:%+v", oId, tabId, item)
			continue
		}
		detailDimensionScoreMap := make(map[string]int32)
		for _, dimId := range dimIds {
			score := int32(1)
			// 差评
			if item.GetAttitude() == badRate {
				score = int32(-1)
			}
			detailDimensionScoreMap[dimId] = score
		}

		res = append(res, &pb.OtherRateMsg{
			DimensionScoreMap: detailDimensionScoreMap,
			Uid:               item.GetRateUid(),
			TabId:             tabId,
			RateUid:           item.GetUid(),
			HappenTime:        now,
			GamePalLightTime:  gamePalLightenTime,
		})
	}

	return res
}

func getGamePalFilter(ctx context.Context, uid uint32, tabIds []uint32) map[string]int64 {
	// 用户是否有搭子
	gamePalIds := make([]string, 0, len(tabIds))
	for _, tabId := range tabIds {
		gamePalIds = append(gamePalIds, fmt.Sprintf("%d_%d", uid, tabId))
	}
	gamePalRsp, gamePalErr := rpc.GamePalClient.GetGamePalCardList(ctx, &game_pal.GetGamePalCardListReq{
		IdList: gamePalIds,
	})
	if gamePalErr != nil {
		log.ErrorWithCtx(ctx, "GetGamePalCardList fail, err:%v, gamePalIds:%v", gamePalErr, gamePalIds)
		return nil
	}
	gamePalMap := make(map[string]int64, len(tabIds))
	for _, v := range gamePalRsp.GetCards() {
		gamePalMap[v.GetId()] = v.GetLightenAt()
	}
	return gamePalMap
}

func getImageFilter(ctx context.Context, uid uint32, tabIds []uint32) map[string]bool {
	items := make([]*pb.GameUserPersonalImageReqItem, 0, len(tabIds))
	for _, v := range tabIds {
		items = append(items, &pb.GameUserPersonalImageReqItem{
			Uid:   uid,
			TabId: v,
		})
	}
	rateImageInfos, err := rpc.GameUserRateClient.GetGameUserPersonalImage(ctx, &pb.GetGameUserPersonalImageReq{
		Item: items,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AddOtherRateScore GetGameUserPersonalImage fail, err:%v, items:%+v", err, items)
		return nil
	}

	resMap := make(map[string]bool, len(tabIds))
	for _, v := range tabIds {
		id := fmt.Sprintf("%d_%d", uid, v)
		if _, ok := rateImageInfos.GetItems()[id]; !ok {
			continue
		}
		resMap[id] = true
	}
	return resMap
}

func formatIP(ip string) uint32 {
	nums := strings.Split(ip, ".")
	var iIP uint32 = 0
	var pos uint = 24
	for _, num := range nums {
		temp, _ := strconv.Atoi(num)

		iIP = iIP | (uint32(temp) << pos)
		pos -= 8
	}
	return iIP
}

func getReqIp(r *http.Request) (ip string) {
	if xForwardedFor := strings.TrimSpace(r.Header.Get("X-Forwarded-For")); xForwardedFor != "" {
		parts := strings.Split(xForwardedFor, ",")
		for _, part := range parts {
			if ip = strings.TrimSpace(part); ip != "" {
				return
			}
		}
	}
	if xRealIp := r.Header.Get("X-Real-Ip"); xRealIp != "" {
		if ip = strings.TrimSpace(xRealIp); ip != "" {
			return
		}
	}
	if remoteAddr := strings.TrimSpace(r.RemoteAddr); remoteAddr != "" {
		var err error
		if ip, _, err = net.SplitHostPort(remoteAddr); err == nil {
			return
		}
	}
	return ""
}

func buildServiceInfo(ctx context.Context, uid uint32, baseInfo *api.BaseRequest, r *http.Request) context.Context {
	if baseInfo == nil {
		loginInfo, err := rpc.UserHistoryCli.GetUserLastLoginInfo(ctx, uint64(uid))
		if err != nil {
			log.ErrorWithCtx(ctx, "SubmitGameUserRate GetUserLastLoginInfo fail, uid:%d, err:%v", uid, err)
			return ctx
		}
		serviceInfo := &grpc.ServiceInfo{
			DeviceID:      device_id.ParseStringDeviceId(loginInfo.GetDeviceId()),
			UserID:        uid,
			ClientType:    uint16(loginInfo.GetClientType()), // 0-安卓 1-ios 2-web 5-pc
			ClientIP:      formatIP(loginInfo.GetClientIp()),
			ClientVersion: loginInfo.GetClientVer(),
			MarketID:      loginInfo.GetMarketId(),
		}
		return grpc.WithServiceInfo(ctx, serviceInfo)
	}

	deviceInfo := device_id.ParseStringDeviceId(baseInfo.DeviceId)
	if baseInfo.ClientIp == "" {
		//baseInfo.ClientIp = getReqIp(r)
		baseInfo.ClientIp = middleware.GetRealIP(r)
		log.InfoWithCtx(ctx, "buildServiceInfo getReqIp, uid:%d, baseInfo.ClientIp:%s", uid, baseInfo.ClientIp)
	}
	ip := formatIP(baseInfo.ClientIp)
	serviceInfo := &grpc.ServiceInfo{
		DeviceID:      deviceInfo,
		UserID:        uid,
		ClientType:    uint16(baseInfo.GetClientType()), // 0-安卓 1-ios 2-web 5-pc
		ClientIP:      ip,
		ClientVersion: baseInfo.GetClientVersion(),
		MarketID:      baseInfo.GetMarketId(),
	}
	log.InfoWithCtx(ctx, "buildServiceInfo serviceInfo:%+v", serviceInfo)
	return grpc.WithServiceInfo(ctx, serviceInfo)
}

// 风控
func senderRiskCheck(ctx context.Context, uid uint32, rateUid uint32, rateDeviceId, userRateText string) bool {
	commentType := "1"
	if len(userRateText) > 0 {
		commentType = "2"
	}

	rateDeviceId = strings.ToLower(rateDeviceId)
	checkReq := &risk_mng_api.CheckReq{
		Scene: "GAME_USER_RATE",
		SourceEntity: &risk_mng_api.Entity{
			Uid: uid,
		},
		CustomParams: map[string]string{
			"r_uid":        strconv.Itoa(int(rateUid)),
			"r_did":        rateDeviceId,
			"comment_type": commentType,
			"content":      userRateText,
		},
	}
	checkResp, err := rpc.RiskMngApiClient.Check(ctx, checkReq)
	if err != nil {
		return false
	}
	log.InfoWithCtx(ctx, "SendGameImMsg SenderRiskCheck CheckHelper req:%s, resp:%s", checkReq.String(), checkResp.String())
	if checkResp.GetErrCode() == status.ErrGameUserRateRisk {
		return true
	}
	return false
}

func syncScanText(ctx context.Context, user *account_go.User, text string) bool {
	resp, err := rpc.CensoringClient.Text().SyncScanText(ctx, &cybros_arbiter_v2.SyncTextCheckReq{
		Context: &cybros_arbiter_v2.TaskContext{
			SceneCode:  sceneCode,
			DeviceInfo: &cybros_arbiter_v2.Device{},
			UserInfo: &cybros_arbiter_v2.User{
				Id:       uint64(user.GetUid()),
				Alias:    user.GetAlias(),
				Phone:    user.GetPhone(),
				Nickname: user.GetNickname(),
			},
		},
		Text: text,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "syncScanText SyncScanText uid(%d) sceneCode(%s) text(%s) err: %v", user.GetUid(), sceneCode, text, err)
		return true
	}
	log.InfoWithCtx(ctx, "syncScanText SyncScanText uid(%d) sceneCode(%s) text(%s) result: %d", user.GetUid(), sceneCode, text, resp.GetResult())
	if cybros_arbiter_v2.Suggestion(resp.GetResult()) == cybros_arbiter_v2.Suggestion_REJECT {
		return true
	}
	return false
}

// ReorderRateTags 根据用户自定义输入内容重排标签
func ReorderRateTags(auth *ttauth.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), 3*time.Second)
	defer cancel()

	data, err := ioutil.ReadAll(r.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReorderRateTags read body err: %v", err)
		_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
		return
	}

	var (
		req api.ReorderRateTagsReq
		out api.ReorderRateTagsResp
	)
	err = json.Unmarshal(data, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReorderRateTags unmarshal body err: %v", err)
		_ = web.ServeAPICodeJson(w, status.ErrSys, err.Error(), nil)
		return
	}
	log.InfoWithCtx(ctx, "ReorderRateTags uid:%d, body data:%+v", auth.UserID, req)

	resp, rpcErr := rpc.GameUserRateClient.ReorderRateTags(ctx, &pb.ReorderRateTagsReq{
		Uid:          auth.UserID,
		UserRateText: req.GetUserRateText(),
		RateItemId:   req.GetRateItemId(),
	})
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "ReorderRateTags GameUserRateClient.ReorderRateTags fail, err:%v, uid:%d", rpcErr, auth.UserID)
		_ = web.ServeAPICodeJson(w, status.ErrSys, rpcErr.Message(), nil)
		return
	}

	out.RateTags = make([]*api.GameUserRateTag, 0, len(resp.GetRateTags()))
	for _, tag := range resp.GetRateTags() {
		out.RateTags = append(out.RateTags, &api.GameUserRateTag{
			Id:      tag.GetId(),
			Name:    tag.GetName(),
			TagType: tag.GetTagType(),
		})
	}

	log.DebugWithCtx(ctx, "ReorderRateTags req: %+v, out:%+v", req, out)

	_ = web.ServeAPIJsonV2(w, &out)
}
