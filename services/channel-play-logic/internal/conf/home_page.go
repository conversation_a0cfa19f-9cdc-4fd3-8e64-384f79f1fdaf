package conf

import (
	"context"
	"encoding/json"
	pb "golang.52tt.com/protocol/app/channel-play"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/runtime/v2/filters/pkg"
)

type HomePageConfigLoader struct {
	configLoader *pkg.ConfigLoader
}

func NewHomePageConfigLoader(filename string) (loader *HomePageConfigLoader, err error) {
	loader = &HomePageConfigLoader{}

	loader.configLoader, err = pkg.NewConfigLoaderV2(context.Background(), filename, &HomePageHeadConfigs{}, false, 30*time.Second)
	if err != nil {
		log.Errorf("NewHomePageConfigLoader NewConfigLoaderV2 filename(%s) err: %v", filename, err)
		return nil, err
	}

	return
}

type AdGameConf struct {
	Host string `json:"host"`
}

func (s *AdGameConf) GetHost() string {
	if s == nil {
		return ""
	}
	return s.Host
}

type EntranceConf struct {
	Title    string `json:"title"`
	Subtitle string `json:"subtitle"`
}

func (s *EntranceConf) GetTitle() string {
	if s == nil {
		return ""
	}
	return s.Title
}
func (s *EntranceConf) GetSubTitle() string {
	if s == nil {
		return ""
	}
	return s.Subtitle
}

type HomePageHeadConfigs struct {
	HomePageHeadConfigs     []*HomePageHeadConfig `json:"home_page_head_configs"`
	GameZoneExtInfoMaxLimit int                   `json:"game_zone_ext_info_max_limit"`

	AdGame   *AdGameConf   `json:"ad_game"`
	Entrance *EntranceConf `json:"entrance"`

	GameZoneFallBackExtInfo        []*pb.TagConfigInfo `json:"game_zone_fall_back_ext_info"`       // 649后金刚区开黑专区轮播兜底配置
	GameZoneGuideContentExperiment *GuideContent       `json:"game_zone_guide_content_experiment"` //649首页开黑专区新手引导实验组引导文案及样式
	//金刚区新手引导实验配置
	GameZoneNewbieGuideCfg *AbtestConfig `json:"game_zone_newbie_guide_cfg"`
	//金刚区开黑专区样式实验配置
	GameZoneStyleGuideCfg *AbtestConfig `json:"game_zone_style_guide_cfg"`
	//金刚区开黑专区标题实验配置
	GameZoneTitleExperimentMap map[string]string `json:"game_zone_title_experiment_map"`
	//金刚区开黑专区样式实验配置
	GameZoneRichCfgExperimentCfg *AbtestConfig `json:"game_zone_rich_cfg_experiment_cfg"`
	//开黑专区656版本标题按钮实验
	GameZoneRichCfgExpResultMap map[string]*RichConfig `json:"game_zone_rich_cfg_exp_result_map"`
	//开黑专区656轮播配置最大数量
	GameZone656ExtInfoMaxLimit int `json:"game_zone_656_ext_info_max_limit"`
	//开黑专区656兜底轮播配置
	GameZone656FallBackRichInfo []*pb.RichTagInfo `json:"game_zone_656_fall_back_rich_info"`
	//开黑专区656动画替换配置
	GameZoneVap656 *Vap `json:"game_zone_vap_656"`
	//开黑专区656底图替换配置
	GameZoneBackGround656 string           `json:"game_zone_background_656"`
	MusicZoneConfig       *MusicZoneConfig `json:"music_zone_config"`
	PiaFilterOption       *PiaFilterOption `json:"pia_filter_option"`
}

type PiaFilterOption struct {
	MusicBusinessId string `json:"music_business_id"`
	GameBusinessId  uint32 `json:"game_business_id"`
	TabId           uint32 `json:"tab_id"`
}

type MusicZoneConfig struct {
	NewVersion string `json:"new_version"`
}

type RichConfig struct {
	Title       string `json:"title"`        // 标题文案
	ButtonTitle string `json:"button_title"` // 按钮文案
}

type HomePageHeadConfig struct {
	ConfigId        string `bson:"_id" json:"config_id"`                     // 配置id，主要用于识别新用户引导匹配
	ConfigType      uint32 `bson:"config_type" json:"config_type"`           // 专区类型，枚举参考HomePageHeadConfigEnum
	Title           string `bson:"title" json:"title"`                       // 主标题
	SubTitle        string `bson:"sub_title" json:"sub_title"`               // 副标题
	Background      string `bson:"background" json:"background"`             // 底图
	Vap             *Vap   `bson:"vap" json:"vap"`                           // 动画效果
	AdTag           string `bson:"ad_tag" json:"ad_tag"`                     // 广告标签，字数-最长配置7个字
	JumpLink        string `bson:"jump_link" json:"jump_link"`               // 专区客户端跳转短链
	IsReplace       bool   `bson:"is_replace" json:"is_replace"`             // 是否可以被替换赛事中心
	SmallBackground string `bson:"small_background" json:"small_background"` // 金刚区收起时展示的小图
	//MinVersion     string   `bson:"min_version"`     // 版本概念
	//MaxVersion     string   `bson:"max_version"`     // 版本概念
	GuideContent     *GuideContent      `bson:"guide_content" json:"guide_content"`           // 新用户注册引导配置
	Submodule        *HomePageSubmodule `bson:"submodule" json:"submodule"`                   // 新用户子专区引导配置
	EsportsConfig    *EsportsConfig     `bson:"esports_config" json:"esports_config"`         // 电竞专区的配置
	CasualGameConfig *CasualGameConfig  `bson:"casual_game_config" json:"casual_game_config"` // 休闲专区的配置
}

type CasualGameConfig struct {
	// 休闲专区新旧短链ab实验
	CasualGameLinkCfg *AbtestConfig `json:"casual_game_link_cfg"`
	// 新短链
	NewLink string `json:"new_link"`
}

type EsportsConfig struct {
	BeginTime         string `bson:"begin_time" json:"begin_time"`                   // 开始时间，当天某时间点，格式15:04
	EndTime           string `bson:"end_time" json:"end_time"`                       // 结束时间，当天某时间点，格式15:04，若比开始时间小，当为跨天处理
	CrowedId          string `bson:"crowed_id" json:"crowed_id"`                     // 赛事人群包id
	HuanYouBackground string `bson:"huan_you_background" json:"huan_you_background"` // 欢游展示的图
	TTBackground      string `bson:"tt_background" json:"tt_background"`             // 旧版tt两行时展示的图
	TTSmallBackground string `bson:"tt_small_background" json:"tt_small_background"` // 旧版tt三行时展示的图
}

type Vap struct {
	Url string `bson:"url"`
	Md5 string `bson:"md5"`
}

type GuideContent struct {
	Title    string `bson:"title" json:"title"`         // 引导文案:主标题
	SubTitle string `bson:"sub_title" json:"sub_title"` // 引导文案:副标题，为空不展示
	Style    uint32 `json:"style"`                      //样式，值见channel-play_.proto HomePageGuideStyle
}

type HomePageSubmodule struct {
	GuideContent *GuideContent `bson:"guide_content" json:"guide_content"`
}

func (cfg *HomePageHeadConfigs) UnmarshalBinary(data []byte) error {
	err := json.Unmarshal(data, cfg)
	if err != nil {
		log.Errorf("HomePageHeadConfigs UnmarshalBinary data(%s) err: %v", data, err)
		return err
	}

	return nil
}

func (loader *HomePageConfigLoader) loadConfig() (cfg *HomePageHeadConfigs) {
	cfg = &HomePageHeadConfigs{}

	if loader.configLoader == nil {
		log.Warnf("HomePageConfigLoader LoadConfig configLoader nil")
		return
	}

	e := loader.configLoader.Get()
	if e == nil {
		log.Warnf("HomePageConfigLoader LoadConfig Get nil")
		return
	}

	var ok bool
	cfg, ok = e.(*HomePageHeadConfigs)
	if !ok {
		log.Warnf("HomePageConfigLoader LoadConfig HomePageHeadConfigs nil")
		return
	}

	return
}

func (loader *HomePageConfigLoader) GetHomePageHeadConfig() []*HomePageHeadConfig {
	return loader.loadConfig().HomePageHeadConfigs
}

func (loader *HomePageConfigLoader) GetGameZoneExtInfoMaxLimit() int {
	limit := loader.loadConfig().GameZoneExtInfoMaxLimit
	if limit <= 0 {
		return 5
	}
	return limit
}

func (loader *HomePageConfigLoader) GetAdGame() *AdGameConf {
	return loader.loadConfig().AdGame
}

func (loader *HomePageConfigLoader) GetEntrance() *EntranceConf {
	return loader.loadConfig().Entrance
}

func (loader *HomePageConfigLoader) GetGameZoneFallBackExtInfo() []*pb.TagConfigInfo {
	return loader.loadConfig().GameZoneFallBackExtInfo
}

func (loader *HomePageConfigLoader) GetGameZoneGuideContentExperiment() *GuideContent {
	return loader.loadConfig().GameZoneGuideContentExperiment
}

func (loader *HomePageConfigLoader) GetGameZoneStyleGuideCfg() *AbtestConfig {
	return loader.loadConfig().GameZoneStyleGuideCfg
}

func (loader *HomePageConfigLoader) GetGameZoneNewbieGuideCfg() *AbtestConfig {
	return loader.loadConfig().GameZoneNewbieGuideCfg
}

func (loader *HomePageConfigLoader) GameZoneTitleExperiment(experiment string) string {
	return loader.loadConfig().GameZoneTitleExperimentMap[experiment]
}

func (loader *HomePageConfigLoader) GetGameZoneRichCfgExperimentCfg() *AbtestConfig {
	return loader.loadConfig().GameZoneRichCfgExperimentCfg
}

func (loader *HomePageConfigLoader) GetGameZoneRichCfgExpResult(experiment string) *RichConfig {
	return loader.loadConfig().GameZoneRichCfgExpResultMap[experiment]
}

func (loader *HomePageConfigLoader) GetGameZone656ExtInfoMaxLimit() int {
	return loader.loadConfig().GameZone656ExtInfoMaxLimit
}

func (loader *HomePageConfigLoader) GetGameZone656FallBackRichInfo() []*pb.RichTagInfo {
	return loader.loadConfig().GameZone656FallBackRichInfo
}

func (loader *HomePageConfigLoader) GetGameZoneVap656() *Vap {
	return loader.loadConfig().GameZoneVap656
}

func (loader *HomePageConfigLoader) GetGameZoneBackGround656() string {
	return loader.loadConfig().GameZoneBackGround656
}

func (loader *HomePageConfigLoader) GetMusicZoneConfig() *MusicZoneConfig {
	return loader.loadConfig().MusicZoneConfig
}

func (loader *HomePageConfigLoader) GetPiaFilterOption() *PiaFilterOption {
	return loader.loadConfig().PiaFilterOption
}
