package conf

import (
	"context"
	"encoding/json"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/runtime/v2/filters/pkg"
)

type PCHomePageConfigLoader struct {
	configLoader *pkg.ConfigLoader
}

func NewPCHomePageConfigLoader(filename string) (loader *PCHomePageConfigLoader, err error) {
	loader = &PCHomePageConfigLoader{}

	loader.configLoader, err = pkg.NewConfigLoaderV2(context.Background(), filename, &PcHomePageHeadConfigs{}, false, 30*time.Second)
	if err != nil {
		log.Errorf("NewPCHomePageConfigLoader NewConfigLoaderV2 filename(%s) err: %v", filename, err)
		return nil, err
	}

	return
}

type PcHomePageHeadConfigs struct {
	PcHomePageHeadConfigs []*PcHomePageHeadConfig `json:"pc_home_page_head_configs"`
	// 极速pc游戏筛选tab强插玩法配置
	GameFilterForceTabInfo map[uint32]*GameFilterForcePosInfo `json:"game_filter_force_tab_info"` // key: tabId, value: pos
	// 极速pc游戏筛选分类强插配置
	GameFilterForceCategoryInfo map[uint32]*GameFilterForcePosInfo `json:"game_filter_force_category_info"` // key: categoryId, value: pos
}

type PcHomePageHeadConfig struct {
	ConfigType    uint32         `json:"config_type"`     // 专区类型，枚举参考CfgType
	GameTabConfig *GameTabConfig `json:"game_tab_config"` // 玩法配置
}

type GameTabConfig struct {
	TabId          uint32 `json:"tab_id"`
	IconBeginColor string `json:"icon_begin_color"` // 角标 渐变色值
	IconEndColor   string `json:"icon_end_color"`   // 角标 渐变色值
	IconText       string `json:"icon_text"`        // 角标文案
}

type GameFilterForcePosInfo struct {
	Pos     uint32 `json:"pos"`
	HotText string `json:"hot_text"`
}

func (cfg *PcHomePageHeadConfigs) UnmarshalBinary(data []byte) error {
	err := json.Unmarshal(data, cfg)
	if err != nil {
		log.Errorf("PcHomePageHeadConfigs UnmarshalBinary data(%s) err: %v", data, err)
		return err
	}

	return nil
}

func (loader *PCHomePageConfigLoader) loadConfig() (cfg *PcHomePageHeadConfigs) {
	cfg = &PcHomePageHeadConfigs{}

	if loader.configLoader == nil {
		log.Warnf("PCHomePageConfigLoader LoadConfig configLoader nil")
		return
	}

	e := loader.configLoader.Get()
	if e == nil {
		log.Warnf("PCHomePageConfigLoader LoadConfig Get nil")
		return
	}

	var ok bool
	cfg, ok = e.(*PcHomePageHeadConfigs)
	if !ok {
		log.Warnf("PCHomePageConfigLoader LoadConfig PcHomePageHeadConfigs nil")
		return
	}

	return
}

func (loader *PCHomePageConfigLoader) GetPcHomePageHeadConfigs() []*PcHomePageHeadConfig {
	return loader.loadConfig().PcHomePageHeadConfigs
}

func (loader *PCHomePageConfigLoader) GetGameFilterForcePosMap() map[uint32]*GameFilterForcePosInfo {
	return loader.loadConfig().GameFilterForceTabInfo
}

func (loader *PCHomePageConfigLoader) GetGameFilterForceCategoryPosMap() map[uint32]*GameFilterForcePosInfo {
	return loader.loadConfig().GameFilterForceCategoryInfo
}
