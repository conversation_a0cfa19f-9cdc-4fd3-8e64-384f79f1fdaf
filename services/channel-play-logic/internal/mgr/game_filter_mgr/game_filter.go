package game_filter_mgr

import (
	"context"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/hobby-channel"
	"golang.52tt.com/protocol/common/status"
	channel_play_tab "golang.52tt.com/protocol/services/channel-play-tab"
	gangup_channel "golang.52tt.com/protocol/services/gangup-channel"
	tabpb "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/utils"
	"sort"
	"time"
)

const (
	MaxFilterReturnNum = 16
)

// ForceInsertItem 强插项统一结构
type ForceInsertItem struct {
	Pos  uint32
	Item *pb.GameHomePageFilterItem
}

type GameFilterMgr struct {
	SupervisorInst   *supervision.Supervisory
	DIYFilterInst    *DIYFilterSource
	ConfigFilterInst *ConfigFilterSource
}

func NewGameFilterMgr(supervisorInst *supervision.Supervisory, diyFilterSourceInst *DIYFilterSource,
	configFilterSourceInst *ConfigFilterSource) (*GameFilterMgr, error) {
	return &GameFilterMgr{
		SupervisorInst:   supervisorInst,
		DIYFilterInst:    diyFilterSourceInst,
		ConfigFilterInst: configFilterSourceInst,
	}, nil
}

func (s *GameFilterMgr) SetGameHomePageDIYFilter(ctx context.Context, uid uint32, items []*pb.GameHomePageFilterItem, entryType pb.MysteryEntryType) error {
	err := s.DIYFilterInst.SetDIYFilter(ctx, uid, items, entryType)
	if err != nil {
		log.ErrorWithCtx(ctx, "GameFilterMgr SetGameHomePageDIYFilter uid(%v) entryType(%v) items(%v) err %v", uid, entryType, items, err)
		return err
	}
	return nil
}

func (s *GameFilterMgr) GetGameHomePageDIYFilter(ctx context.Context, entryType pb.MysteryEntryType, channelPkg string) (out []*pb.GameHomePageFilterItem, err error) {
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取用户信息失败")
	}
	uid := serviceInfo.UserID

	items, err := s.DIYFilterInst.GetDIYFilterItems(ctx, uid, entryType)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameHomePageDIYFilter uid(%d) error:%v", uid, err)
		return nil, err
	}

	if len(items) == 0 {
		items = s.ConfigFilterInst.GetDefaultDiyFilterByEntryType(entryType)
	}
	//log.DebugWithCtx(ctx, "GetGameHomePageDIYFilter uid(%d) items: %v", uid, items)

	out = s.skipInvalidItems(ctx, true, nil, items, serviceInfo, channelPkg, entryType)
	return
}

func (s *GameFilterMgr) getDoudiInfo(ctx context.Context, activeTabs []*tabpb.Tab, items []*gangup_channel.GameHomePageFilterItem,
	serviceInfo *grpc.ServiceInfo, entryType pb.MysteryEntryType) []*pb.GameHomePageFilterItem {
	out := make([]*pb.GameHomePageFilterItem, 0)
	emptyFilterMap := make(map[uint32]bool)
	if len(activeTabs) > 0 {
		activeItem := utils.ConvertTabsToHomePageFilterItemSlice(activeTabs, emptyFilterMap, emptyFilterMap, entryType)
		out = append(out, activeItem...)
	}
	tabIdMap := cache.GetTabInfoCache().GetTabIdCache()
	categoryIdMap := cache.GetCategoryIdMap()
	for _, item := range items {
		if item.GetTabId() != 0 {
			if tabInfo, ok := tabIdMap[item.GetTabId()]; ok {
				if entryType == pb.MysteryEntryType_PCHomePageEntry {
					// pc极速版暂时不过滤
				} else {
					if s.SupervisorInst.GamePageFilterStrategy(tabInfo, serviceInfo, cache.GetWhiteList()) {
						log.InfoWithCtx(ctx, "getDoudiInfo GamePageFilterStrategy item%v ", item)
						continue
					}
				}
				out = append(out, utils.ConvertTabToHomePageFilterItem(tabInfo, entryType))
			}
			//continue 原逻辑当tabId不为0只检查tab,修改为tab和category都要过滤
		} else if item.GetCategoryId() != 0 {
			if categoryInfo, ok := categoryIdMap[item.GetCategoryId()]; ok {
				out = append(out, utils.ConvertCategoryToHomePageFilterItem(categoryInfo))
			}
		} else {
			log.WarnWithCtx(ctx, "getDoudiInfo item has no tabId categoryId item%v", item)
		}
	}
	out = s.removeRepeatedGameHomePageFilterItem(out)
	return out
}

func (s *GameFilterMgr) GetGameHomePageFilter(ctx context.Context, gameIds []uint32, activeIds []uint32,
	entryType pb.MysteryEntryType, channelPkg string) (out []*pb.GameHomePageFilterItem, err error) {
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取用户信息失败")
	}
	uid := serviceInfo.UserID

	out = make([]*pb.GameHomePageFilterItem, 0)

	var items []*gangup_channel.GameHomePageFilterItem
	//1,用户上次进房tab,用户有没有设置diyFilter都要展示，若diyFilter里面有则按diyFilter顺序，否则第一位
	activeTabs := s.ConfigFilterInst.GetActiveTabs(ctx, serviceInfo, activeIds, s.SupervisorInst, entryType)

	//根据动态配置配置默认筛选项
	defaultFilter := s.ConfigFilterInst.GetDefaultFilterByEntryType(ctx, entryType)

	doudiOut := s.getDoudiInfo(ctx, activeTabs, defaultFilter, serviceInfo, entryType)

	newCtx2, cancel2 := context.WithTimeout(ctx, 500*time.Millisecond)
	defer cancel2()
	//2,用户自定义筛选器内容
	items, err = s.DIYFilterInst.GetDIYFilterItems(newCtx2, uid, entryType)
	if err != nil || conf.ChannelPlayLogicConfig.GetTestGameFilterFallBack() {
		utils.ErrCntLog(newCtx2, "GetGameHomePageFilter GetDIYFilterItems uid(%d) entryType(%v) error:%v", uid, entryType, err)
		return doudiOut, nil
	}
	if entryType == pb.MysteryEntryType_PCHomePageEntry {
		if len(items) > 0 {
			fixItem := conf.ChannelPlayLogicConfig.LoadConfig().PCFixFilter
			return s.genPcFilterItems(newCtx2, serviceInfo, channelPkg, items, fixItem), nil
		}
		return s.genPcFilterItems(newCtx2, serviceInfo, channelPkg, nil, defaultFilter), nil
	}

	hasDiyFilter := false
	if len(items) > 0 { //用户自定义了筛选器，返回上次进房+自定义
		hasDiyFilter = true
		out = s.skipInvalidItems(newCtx2, hasDiyFilter, activeTabs, items, serviceInfo, channelPkg, entryType)
		return
	}
	//用户没有自定义筛选器走业务配置逻辑，上次进房+userTag+客户端传入gameId+默认动态配置

	//根据userTag配置筛选项
	gameCardTabs, err := s.ConfigFilterInst.GetTabsByGameCard(newCtx2, serviceInfo, uid, 3, s.SupervisorInst)
	if err != nil {
		utils.ErrCntLog(newCtx2, "GetGameHomePageFilter GetTabsByUserTag error: %v", err)
		return doudiOut, nil
	}
	activeTabs = append(activeTabs, gameCardTabs...)

	//根据客户端传入gameId配置筛选项
	gameListTab := s.ConfigFilterInst.GetTabByGameList(gameIds, serviceInfo, s.SupervisorInst)
	activeTabs = append(activeTabs, gameListTab...)

	out = append(out, s.skipInvalidItems(newCtx2, hasDiyFilter, activeTabs, defaultFilter, serviceInfo, channelPkg, entryType)...)

	out = s.removeRepeatedGameHomePageFilterItem(out)

	log.InfoWithCtx(ctx, "GetGameHomePageFilter uid(%d) out %v", uid, len(out))
	return
}

func (s *GameFilterMgr) skipInvalidItems(ctx context.Context, hasDiyFilter bool, activeTabs []*tabpb.Tab, items []*gangup_channel.GameHomePageFilterItem,
	serviceInfo *grpc.ServiceInfo, channelPkg string, entryType pb.MysteryEntryType) (filterItems []*pb.GameHomePageFilterItem) {
	filterItems = make([]*pb.GameHomePageFilterItem, 0, len(items))
	tabIdMap := cache.GetTabInfoCache().GetTabIdCache()
	categoryIdMap := cache.GetCategoryIdMap()
	// 监管配置过滤
	supConfInst := &supervision.SupervisoryConf{
		RegisterLimitTime:      conf.PublicSwitchConfig.GetRegisterLimitTime(),
		UserLevelLimit:         conf.PublicSwitchConfig.GetUserLevelLimit(),
		RealNameStandardStatus: conf.PublicSwitchConfig.GetRealNameStandardStatus(),
	}
	tabFilter, categoryFilter := s.SupervisorInst.GetFilterMap(ctx, serviceInfo, channelPkg, supConfInst)

	for _, item := range items {
		if item.GetTabId() != 0 {
			if tabInfo, ok := tabIdMap[item.GetTabId()]; ok {
				// 监管配置
				if tabFilter[item.GetTabId()] || categoryFilter[tabInfo.GetCategoryId()] {
					continue
				}
				if entryType == pb.MysteryEntryType_PCHomePageEntry {
					//PC请求的不过滤音乐玩法
					if s.SupervisorInst.MiniGameStageStrategy(tabInfo, serviceInfo, cache.GetWhiteList()) {
						log.InfoWithCtx(ctx, "skipInvalidItems item%v, entryType:%v", item, entryType)
						continue
					}

				} else if entryType == pb.MysteryEntryType_FASTPCHomePageEntry {
					if s.SupervisorInst.MiniGameStageStrategy(tabInfo, serviceInfo, cache.GetWhiteList()) {
						log.InfoWithCtx(ctx, "skipInvalidItems item%v, entryType:%v", item, entryType)
						continue
					}
					if s.SupervisorInst.IsFastPcCategoryFilter(serviceInfo, tabInfo.GetId(), cache.GetTabInfoCache().GetFastPCCategoryConfigMap()) {
						log.InfoWithCtx(ctx, "skipInvalidItems item%v, entryType:%v", item, entryType)
						continue
					}
					if s.isTabInFastPcMiniGameCategory(tabInfo.GetId()) {
						log.InfoWithCtx(ctx, "skipInvalidItems item%v, entryType:%v", item, entryType)
						continue
					}

				} else {
					if s.SupervisorInst.GamePageFilterStrategy(tabInfo, serviceInfo, cache.GetWhiteList()) {
						log.InfoWithCtx(ctx, "skipInvalidItems item%v ", item)
						continue
					}
				}
				filterItems = append(filterItems, utils.ConvertTabToHomePageFilterItem(tabInfo, entryType))
			}
			//continue 原逻辑当tabId不为0只检查tab,修改为tab和category都要过滤
		} else if item.GetCategoryId() != 0 {
			if entryType == pb.MysteryEntryType_FASTPCHomePageEntry {
				// 极速PC category
				fastPCCategoryConfigByIdMap := cache.GetTabInfoCache().GetFastPCCategoryConfigByIdMap()
				if categoryInfo, ok := fastPCCategoryConfigByIdMap[item.GetCategoryId()]; ok {
					if !s.isFastPcCategoryFilterValid(ctx, serviceInfo, categoryInfo, tabFilter, categoryFilter) {
						log.InfoWithCtx(ctx, "skipInvalidItems item%v, entryType:%v", item, entryType)
						continue
					}
					filterItems = append(filterItems, utils.ConvertFastPCCategoryToHomePageFilterItem(categoryInfo))
				}
			} else {
				// 监管配置
				if categoryFilter[item.GetCategoryId()] {
					continue
				}

				if categoryInfo, ok := categoryIdMap[item.GetCategoryId()]; ok {
					filterItems = append(filterItems, utils.ConvertCategoryToHomePageFilterItem(categoryInfo))
				}
			}
		} else {
			log.WarnWithCtx(ctx, "skipInvalidItems item has no tabId categoryId item%v", item)
		}
	}
	// 只有上次进房 若diyFilter里面有则按diyFilter顺序，否则第一位
	if hasDiyFilter && len(activeTabs) > 0 {
		shouldAdd := true
		for _, v := range filterItems {
			if v.TabId == activeTabs[0].GetId() {
				shouldAdd = false
				break
			}
		}
		if shouldAdd {
			filterItems = append(utils.ConvertTabsToHomePageFilterItemSlice(activeTabs, tabFilter, categoryFilter, entryType), filterItems...)
		}
		// 都过滤了，返回些默认的
		if len(filterItems) > 0 {
			return
		}
	}
	// 游戏卡/游戏id 监管过滤，
	if len(activeTabs) > 0 {
		filterItems = append(utils.ConvertTabsToHomePageFilterItemSlice(activeTabs, tabFilter, categoryFilter, entryType), filterItems...)
	}

	return filterItems
}

func (s *GameFilterMgr) removeRepeatedGameHomePageFilterItem(items []*pb.GameHomePageFilterItem) (result []*pb.GameHomePageFilterItem) {
	result = make([]*pb.GameHomePageFilterItem, 0, len(items))
	//去重
	tabTmpMap := make(map[uint32]bool, 0)
	categoryTmpMap := make(map[uint32]bool, 0)
	for _, item := range items {
		if item.GetTabId() != 0 {
			if tabTmpMap[item.GetTabId()] {
				continue
			}
			tabTmpMap[item.GetTabId()] = true
			result = append(result, item)
		}
		if item.CategoryId != 0 {
			if categoryTmpMap[item.GetCategoryId()] {
				continue
			}

			categoryTmpMap[item.GetCategoryId()] = true
			result = append(result, item)
		}
	}
	return result
}

// 兜底+自定义
func (s *GameFilterMgr) genPcFilterItems(ctx context.Context, serviceInfo *grpc.ServiceInfo, channelPkg string,
	diyItem []*gangup_channel.GameHomePageFilterItem, fallBackItem []*gangup_channel.GameHomePageFilterItem) []*pb.GameHomePageFilterItem {
	items := make([]*pb.GameHomePageFilterItem, 0, len(fallBackItem)+len(diyItem))

	tabIdMap := cache.GetTabInfoCache().GetTabIdCache()
	categoryIdMap := cache.GetCategoryIdMap()
	fallBackTabMap := make(map[uint32]bool)
	fallBackCategoryMap := make(map[uint32]bool)
	for _, item := range fallBackItem {
		if item.GetTabId() != 0 {
			if _, ok := tabIdMap[item.GetTabId()]; ok {
				fallBackTabMap[item.GetTabId()] = true
			}
		} else if item.GetCategoryId() != 0 {
			if _, ok := categoryIdMap[item.GetCategoryId()]; ok {
				fallBackCategoryMap[item.GetCategoryId()] = true
			}
		} else {
			log.WarnWithCtx(ctx, "genPcFilterItems item has no tabId categoryId item%v", item)
		}
	}
	var allItem []*gangup_channel.GameHomePageFilterItem
	allItem = append(allItem, fallBackItem...)
	allItem = append(allItem, diyItem...)
	filteredItems := s.skipInvalidItems(ctx, true, nil, allItem, serviceInfo, channelPkg,
		pb.MysteryEntryType_PCHomePageEntry)
	items = s.removeRepeatedGameHomePageFilterItem(filteredItems)
	for _, item := range items {
		if item.GetTabId() != 0 && fallBackTabMap[item.GetTabId()] {
			item.IsFallBackItem = true
		} else if item.GetCategoryId() != 0 && fallBackCategoryMap[item.GetCategoryId()] {
			item.IsFallBackItem = true
		}
	}
	return items
}

func (s *GameFilterMgr) GetFastPcGameHomePageFilter(ctx context.Context, serviceInfo *grpc.ServiceInfo) ([]*pb.GameHomePageFilterItem, error) {
	ctx, cancel := context.WithTimeout(ctx, 500*time.Millisecond)
	defer cancel()

	//根据动态配置配置默认筛选项
	defaultFilter := s.ConfigFilterInst.GetDefaultFilterByEntryType(ctx, pb.MysteryEntryType_FASTPCHomePageEntry)
	//2,用户自定义筛选器内容
	diyItems, err := s.DIYFilterInst.GetDIYFilterItems(ctx, serviceInfo.UserID, pb.MysteryEntryType_FASTPCHomePageEntry)
	if err != nil {
		utils.ErrCntLog(ctx, "GetFastPcGameHomePageFilter GetDIYFilterItems fail, err:%v", err)
		doudiOut := s.getDoudiInfo(ctx, nil, defaultFilter, serviceInfo, pb.MysteryEntryType_FASTPCHomePageEntry)
		return doudiOut, nil
	}
	hasDiyFilter := false
	if len(diyItems) > 0 { //用户自定义了筛选器，返回上次进房+自定义+兜底游戏配置 + 强插
		hasDiyFilter = true
	}
	//用户自定义了筛选器，自定义+兜底游戏配置
	diyItems = append(diyItems, defaultFilter...)
	resItems := s.skipInvalidItems(ctx, hasDiyFilter, nil, diyItems, serviceInfo, "", pb.MysteryEntryType_FASTPCHomePageEntry)

	// 监管配置过滤
	supConfInst := &supervision.SupervisoryConf{
		RegisterLimitTime:      conf.PublicSwitchConfig.GetRegisterLimitTime(),
		UserLevelLimit:         conf.PublicSwitchConfig.GetUserLevelLimit(),
		RealNameStandardStatus: conf.PublicSwitchConfig.GetRealNameStandardStatus(),
	}
	tabFilter, categoryFilter := s.SupervisorInst.GetFilterMap(ctx, serviceInfo, "", supConfInst)

	// 统一处理强插项（tab和分类）
	forceInsertItems := make([]*ForceInsertItem, 0)

	// tab强插项
	gameFilterForcePosInfoMap := conf.PCHomePageConfigs.GetGameFilterForcePosMap()
	lenItem := uint32(len(resItems))
	for tabId, v := range gameFilterForcePosInfoMap {
		tabInfo := cache.GetTabInfoCache().GetTabInfoCacheById(tabId)
		if tabInfo == nil {
			log.WarnWithCtx(ctx, "GetFastPcGameHomePageFilter tabInfo is nil, tabId:%d", tabId)
			continue
		}
		// 监管配置
		if tabFilter[tabId] || categoryFilter[tabInfo.GetCategoryId()] {
			log.InfoWithCtx(ctx, "GetFastPcGameHomePageFilter tabFilter or categoryFilter tabId:%d", tabId)
			continue
		}
		if s.SupervisorInst.MiniGameStageStrategy(tabInfo, serviceInfo, cache.GetWhiteList()) {
			log.InfoWithCtx(ctx, "GetFastPcGameHomePageFilter GamePageFilterStrategy tabId:%d", tabId)
			continue
		}
		if s.SupervisorInst.IsFastPcCategoryFilter(serviceInfo, tabInfo.GetId(), cache.GetTabInfoCache().GetFastPCCategoryConfigMap()) {
			log.InfoWithCtx(ctx, "GetFastPcGameHomePageFilter IsFastPcCategoryFilter tabId:%d", tabId)
			continue
		}
		// 如果是小游戏分类下的玩法，不能展示在极速PC首页
		if s.isTabInFastPcMiniGameCategory(tabId) {
			log.InfoWithCtx(ctx, "GetFastPcGameHomePageFilter isTabInFastPcMiniGameCategory tabId:%d", tabId)
			continue
		}

		forceInsertItems = append(forceInsertItems, &ForceInsertItem{
			Pos: v.Pos,
			Item: &pb.GameHomePageFilterItem{
				TabId:       tabId,
				Title:       tabInfo.GetName(),
				TabImageUrl: tabInfo.GetCardsImageUrl(),
				HotText:     v.HotText,
			},
		})
	}

	// 极速PC分类强插项
	gameFilterForceFastPCCategoryPosInfoMap := conf.PCHomePageConfigs.GetGameFilterForceCategoryPosMap()
	fastPCCategoryConfigByIdMap := cache.GetTabInfoCache().GetFastPCCategoryConfigByIdMap()
	for categoryId, v := range gameFilterForceFastPCCategoryPosInfoMap {
		categoryInfo, ok := fastPCCategoryConfigByIdMap[categoryId]
		if !ok {
			log.WarnWithCtx(ctx, "GetFastPcGameHomePageFilter fastPCCategoryConfig is nil, categoryId:%d", categoryId)
			continue
		}

		// 如果分类下的玩法都被过滤掉了，就不展示分类
		if !s.isFastPcCategoryFilterValid(ctx, serviceInfo, categoryInfo, tabFilter, categoryFilter) {
			log.InfoWithCtx(ctx, "GetFastPcGameHomePageFilter isFastPcCategoryFilterValid false, categoryId:%d", categoryId)
			continue
		}

		forceInsertItems = append(forceInsertItems, &ForceInsertItem{
			Pos: v.Pos,
			Item: &pb.GameHomePageFilterItem{
				CategoryId:         categoryId,
				Title:              categoryInfo.GetName(),
				TabImageUrl:        categoryInfo.GetIcon(),
				HotText:            v.HotText,
				PcFastCategoryType: uint32(categoryInfo.GetCategoryType()),
			},
		})
	}

	// 按位置排序强插项
	sort.SliceStable(forceInsertItems, func(i, j int) bool {
		return forceInsertItems[i].Pos < forceInsertItems[j].Pos
	})

	// 统一插入强插项
	for _, forceItem := range forceInsertItems {
		if forceItem.Pos >= lenItem {
			resItems = append(resItems, forceItem.Item)
		} else {
			resItems = append(resItems[:forceItem.Pos], append([]*pb.GameHomePageFilterItem{
				forceItem.Item,
			}, resItems[forceItem.Pos:]...)...)
			lenItem++
		}
	}

	resItems = s.removeRepeatedGameHomePageFilterItem(resItems)
	if len(resItems) > MaxFilterReturnNum {
		resItems = resItems[:MaxFilterReturnNum]
	}

	return resItems, nil
}

func (s *GameFilterMgr) isTabInFastPcMiniGameCategory(tabId uint32) bool {
	categoryConfigMap := cache.GetTabInfoCache().GetFastPCCategoryConfigMap()
	for _, categoryInfo := range categoryConfigMap[tabId] {
		if categoryInfo.GetCategoryType() == channel_play_tab.FastPCCategoryType_FAST_PC_CATEGORY_TYPE_MINI_GAME {
			return true
		}
	}
	return false
}

func (s *GameFilterMgr) isFastPcCategoryFilterValid(ctx context.Context, serviceInfo *grpc.ServiceInfo, categoryInfo *channel_play_tab.FastPCCategoryConfig,
	tabFilter map[uint32]bool, categoryFilter map[uint32]bool) bool {
	isValidCategory := false
	for _, tabId := range categoryInfo.GetTabIds() {
		tabInfo := cache.GetTabInfoCache().GetTabInfoCacheById(tabId)
		if tabInfo == nil {
			continue
		}
		// 监管配置
		if tabFilter[tabId] || categoryFilter[tabInfo.GetCategoryId()] {
			continue
		}
		if s.SupervisorInst.MiniGameStageStrategy(tabInfo, serviceInfo, cache.GetWhiteList()) {
			continue
		}
		isValidCategory = true
		break
	}
	return isValidCategory
}
