package tab_list

import (
	"context"
	channel_play_tab "golang.52tt.com/clients/channel-play-tab"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	channel_play_middle "golang.52tt.com/protocol/services/channel-play-middle"
	channelPlayTabPb "golang.52tt.com/protocol/services/channel-play-tab"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
)

type TabListMgr struct {
	SupervisorInst          *supervision.Supervisory
	channelPlayTabClient    *channel_play_tab.Client
	channelPlayMiddleClient channel_play_middle.ChannelPlayMiddleClient
}

func NewTabListMgr(supervisor *supervision.Supervisory, channelPlayTabClient *channel_play_tab.Client,
	channelPlayMiddleClient channel_play_middle.ChannelPlayMiddleClient) *TabListMgr {

	return &TabListMgr{
		SupervisorInst:          supervisor,
		channelPlayTabClient:    channelPlayTabClient,
		channelPlayMiddleClient: channelPlayMiddleClient,
	}
}
func (t *TabListMgr) ShowTopicChannelTabList(ctx context.Context, serviceInfo *grpc.ServiceInfo,
	in *topic_channel.ShowTopicChannelTabListReq) (primaryItem []*topic_channel.ShowTopicChannelTabPrimaryItem, err error) {
	// 监管过滤
	supConfInst := &supervision.SupervisoryConf{
		RegisterLimitTime:      conf.PublicSwitchConfig.GetRegisterLimitTime(),
		UserLevelLimit:         conf.PublicSwitchConfig.GetUserLevelLimit(),
		RealNameStandardStatus: conf.PublicSwitchConfig.GetRealNameStandardStatus(),
	}
	tabFilter, realNameCategoryFilter := t.SupervisorInst.GetFilterMap(ctx, serviceInfo, in.GetChannelPkg(), supConfInst)
	if len(tabFilter) != 0 || len(realNameCategoryFilter) != 0 {
		log.WarnWithCtx(ctx, "serviceInfo(%s) in(%s) tabFilter(%v) realNameCategoryFilter(%v)",
			serviceInfo.String(), in.String(), tabFilter, realNameCategoryFilter)
	}
	handler := &switchHandler{supervisorInst: t.SupervisorInst}
	handler.InitBaseSwitch()
	switch in.GetReqSource() {
	case uint32(topic_channel.ReqSource_PC_HOME_PAGE_SOURCE):
		handler.publishAbilityFilter = true
		handler.filterMinorityGame = false
		handler.insertMinorityGame = false

	default:
		handler.publishAbilityFilter = false
		handler.filterMinorityGame = !in.GetIsNeedMinorityGame() && protocol.NewTerminalType(serviceInfo.TerminalType).Platform() != protocol.PC
		handler.insertMinorityGame = handler.filterMinorityGame
	}

	primaryItem = handler.GetPrimaryItems(in.GetSelfGameIds(), serviceInfo, tabFilter, realNameCategoryFilter)

	return
}

func (t *TabListMgr) GetFastPcTabList(ctx context.Context, serviceInfo *grpc.ServiceInfo) ([]*topic_channel.FastPcCategoryInfo, error) {
	res := make([]*topic_channel.FastPcCategoryInfo, 0)
	fastCategoryConfigs, err := t.channelPlayTabClient.GetFastPCCategoryConfig(ctx, &channelPlayTabPb.GetFastPCCategoryConfigReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFastPCTabListPC GetFastPCCategoryConfig error: %v", err)
		return res, err
	}
	tabMap := cache.GetTabInfoCache().GetTabIdCache()
	for _, c := range fastCategoryConfigs {
		tabDetail := make([]*topic_channel.Tab, 0)
		for _, tabId := range c.GetTabIds() {
			tabInfo, ok := tabMap[tabId]
			if !ok {
				log.InfoWithCtx(ctx, "GetFastPCTabListPC tabId(%v) not found", tabId)
				continue
			}
			// 玩法过滤
			if t.SupervisorInst.MiniGameStageStrategy(tabInfo, serviceInfo, cache.GetWhiteList()) {
				log.InfoWithCtx(ctx, "GetFastPCTabListPC GamePageFilterStrategy tabId:%d", tabId)
				continue
			}
			tabDetail = append(tabDetail, convertTab(tabInfo, uint32(serviceInfo.ClientType)))
		}
		if len(tabDetail) == 0 {
			log.InfoWithCtx(ctx, "GetFastPCTabListPC category(%v) tabDetail empty", c.GetId())
			continue
		}
		res = append(res, &topic_channel.FastPcCategoryInfo{
			CategoryId:   c.GetId(),
			CategoryName: c.GetName(),
			TabDetail:    tabDetail,
			CategoryType: uint32(c.GetCategoryType()),
		})
	}
	return res, nil
}

func convertTab(tab *tabPB.Tab, clientType uint32) (elem *topic_channel.Tab) {
	if tab != nil {
		tabId := tab.GetId()
		if protocol.IsFastPcClientType(clientType) && tabId == conf.PublicSwitchConfig.GetFastPcChatTabId() {
			// 房间玩法列表当成是房间相关的，所以扩列返回音乐的id
			tabId = conf.PublicSwitchConfig.GetMuseChatTabId()
		}
		elem = &topic_channel.Tab{}
		elem.Id = tabId
		elem.Name = tab.GetName()
		elem.ImageUri = tab.GetImageUri()
		elem.Version = tab.GetVersion()
		elem.TagId = tab.GetTagId()
		elem.FollowLabelImg = tab.FollowLabelImg
		elem.FollowLabelText = tab.FollowLabelText
		elem.CategoryId = tab.GetCategoryId()
		elem.MiniGameNum = tab.GetMiniGameNum()
		if uint32(tab.GetTabType()) == 0 {
			elem.TabType = topic_channel.Tab_NORMAL
		} else if uint32(tab.GetTabType()) == 1 {
			elem.TabType = topic_channel.Tab_GAME
		} else if uint32(tab.GetTabType()) == 2 {
			elem.TabType = topic_channel.Tab_MINI_GAME
		}

		if int32(tab.GetRoomNameType()) == 0 {
			elem.RoomNameType = topic_channel.Tab_DEFAULT
		} else if int32(tab.GetRoomNameType()) == 1 {
			elem.RoomNameType = topic_channel.Tab_SPLICE
		}

		elem.RoomNameVersion = tab.RoomNameVersion
		// 首页卡片相关
		elem.CardsImageUrl = tab.GetCardsImageUrl()
		elem.MaskLayer = tab.GetMaskLayer()
		elem.TabLabel = topic_channel.LabelType(tab.GetTabLabel())
		elem.RoomLabel = tab.GetRoomLabel()
		elem.CategorySort = tab.GetCategorySort()
		elem.DisplayElem = tab.GetDisplayElem()

		// v5.5.0新增小卡片和创建房间列表
		elem.NewTabCategoryUrl = tab.NewTabCategoryUrl
		elem.SmallCardUrl = tab.SmallCardUrl

		if tab.Name == "其他游戏" {
			elem.HasChildList = true
		}
	}
	return elem
}

// GetSupportTabList 获取房间支持的tab列表
func (t *TabListMgr) GetSupportTabList(ctx context.Context) ([]uint32, error) {
	resp, err := t.channelPlayMiddleClient.GetFastPcSupportTabList(ctx, &channel_play_middle.GetFastPcSupportTabListReq{
		NeedTabFilter: true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFastPCTabListPC GetFastPcSupportTabList error: %v", err)
		return nil, err
	}
	tabIds := make([]uint32, 0, len(resp.GetTabs()))
	for _, tabInfo := range resp.GetTabs() {
		tabIds = append(tabIds, tabInfo.GetTabId())
	}
	return tabIds, nil
}
