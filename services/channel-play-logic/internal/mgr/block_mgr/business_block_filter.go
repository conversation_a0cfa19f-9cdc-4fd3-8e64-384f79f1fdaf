package block_mgr

import (
	"context"

	channel_play "golang.52tt.com/protocol/app/channel-play"
	"golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
)

type BusinessBlockFilterMgr struct {
}

func NewBusinessBlockFilterMgr() *BusinessBlockFilterMgr {

	businessBlockMgr := &BusinessBlockFilterMgr{}
	return businessBlockMgr
}

func (m *BusinessBlockFilterMgr) GetBusinessFilterByCategoryId(categoryId uint32) (items []*channel_play.BusinessFilterItem, err error) {
	items = make([]*channel_play.BusinessFilterItem, 0)
	bindCategoryMap := cache.GetBussBindCategoryMapCache()
	if _, ok := bindCategoryMap[categoryId]; !ok {
		return items, nil
	}
	filterItems := bindCategoryMap[categoryId]
	items = convertBusinessFilter(filterItems)
	return items, nil

}

func convertBusinessFilter(businessBlocks []*tab.BusinessBlock) (items []*channel_play.BusinessFilterItem) {
	items = make([]*channel_play.BusinessFilterItem, 0)
	for _, block := range businessBlocks {
		item := &channel_play.BusinessFilterItem{
			FilterType:    channel_play.FilterType(block.GetFilterType()),
			Id:            block.GetId(),
			Title:         block.GetTitle(),
			Mode:          channel_play.BusinessFilterItem_Mode(block.GetMode()),
			Elems:         convertBusinessFilterElem(block.GetElems()),
			MostSelectNum: block.GetMostSelectNum(),
		}
		items = append(items, item)
	}
	return items
}

func convertBusinessFilterElem(tabElem []*tab.BusinessElem) []*channel_play.BusinessFilterElem {
	elems := make([]*channel_play.BusinessFilterElem, 0)

	for _, elem := range tabElem {
		e := &channel_play.BusinessFilterElem{
			Id:    elem.Id,
			Title: elem.Title,
			Mode:  channel_play.BusinessFilterElem_BusinessFilterMode(elem.Mode),
		}
		elems = append(elems, e)
	}
	return elems
}

func (m *BusinessBlockFilterMgr) GetBusinessBlockByEntrance(ctx context.Context, tabInfo *tab.Tab, mode, source, clientType uint32) (
	businessBlocks []*channel_play.BusinessFilterItem, err error) {
	businessBlocks = make([]*channel_play.BusinessFilterItem, 0)
	//根据入口，生成business block, 目前只有首页筛选器，密室逃脱tab，目录有业务block
	if channel_play.GetSecondaryFilterReq_Mode(mode) != channel_play.GetSecondaryFilterReq_FILTER &&
		channel_play.GetSecondaryFilterReq_Mode(mode) != channel_play.GetSecondaryFilterReq_FilterAfter628 &&
		channel_play.GetSecondaryFilterReq_Mode(mode) != channel_play.GetSecondaryFilterReq_FilterWithMultiOption {
		return businessBlocks, nil
	}
	businessBlock := cache.GetBussBindTabMapCache(tabInfo.GetId(), clientType)
	if businessBlock == nil {
		//缓存中，指定tab没有业务block
		return businessBlocks, nil
	}
	businessBlocks = append(businessBlocks, genFilterBusinessBlock(ctx, businessBlock, source)...)
	return businessBlocks, nil
}

func genFilterBusinessBlock(ctx context.Context, businessBlock []*tab.BusinessBlock, source uint32) (blocks []*channel_play.BusinessFilterItem) {
	blocks = make([]*channel_play.BusinessFilterItem, 0)
	for _, v := range businessBlock {
		if source != uint32(v.GetSource()) {
			continue
		}

		item := &channel_play.BusinessFilterItem{
			FilterType:    channel_play.FilterType(v.FilterType),
			Id:            v.Id,
			Title:         v.Title,
			Mode:          channel_play.BusinessFilterItem_Mode(v.Mode),
			MostSelectNum: v.MostSelectNum,
		}
		elems := make([]*channel_play.BusinessFilterElem, 0)
		for _, e := range v.Elems {
			businessElem := &channel_play.BusinessFilterElem{
				Id:    e.Id,
				Title: e.Title,
				Mode:  channel_play.BusinessFilterElem_BusinessFilterMode(e.Mode),
			}
			elems = append(elems, businessElem)
		}
		item.Elems = elems

		blocks = append(blocks, item)
	}
	return blocks
}
