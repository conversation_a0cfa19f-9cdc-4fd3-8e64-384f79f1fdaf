package block_mgr

import (
	"context"
	rcmdChannelLabel "golang.52tt.com/clients/topic-channel/rcmd-channel-label"
	tcTab "golang.52tt.com/clients/topic-channel/tab"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	rcmdPb "golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
	"golang.52tt.com/services/channel-play-logic/internal/utils"
	"math"
	"time"
)

var (
	InfElemID uint32 = math.MaxUint32
)

type BaseBlockHandler struct {
	QuickMatchHandler   IBlockHandler
	HomepageHandler     IBlockHandler
	PubChannelHandler   IBlockHandler
	PlayQuestionHandler IBlockHandler

	rcmdChannelLabelClient *rcmdChannelLabel.Client
	tabCli                 tcTab.IClient
}

func NewBaseBlockHandler(rcmdChannelLabelClient *rcmdChannelLabel.Client, tabCli tcTab.IClient) *BaseBlockHandler {
	handler := &BaseBlockHandler{
		QuickMatchHandler:      NewQuickMatchHandler(),
		HomepageHandler:        NewHomePageHandler(),
		PubChannelHandler:      NewPubChannelHandler(),
		PlayQuestionHandler:    NewPlayQuestionHandler(),
		rcmdChannelLabelClient: rcmdChannelLabelClient,
		tabCli:                 tabCli,
	}
	return handler
}

func (b *BaseBlockHandler) GetBaseBlock(ctx context.Context, mode channel_play.GetSecondaryFilterReq_Mode, tabType tabPB.Tab_TabType,
	baseBlockTmp []*tabPB.Block, serviceInfo *grpc.ServiceInfo) (baseBlocks []*topic_channel.Block) {
	baseBlocks = make([]*topic_channel.Block, 0, len(baseBlockTmp))

	opFunc := make([]BlockOpFunc, 0)
	opFunc = append(opFunc, WithTabType(tabPB.TabType(tabType)), WithInMode(mode))
	for _, blockInfo := range baseBlockTmp {
		if blockInfo.GetOnlyShowAfter_628() && !utils.IsMobileVersionHigherThan(serviceInfo.ClientType, serviceInfo.ClientVersion,
			protocol.FormatClientVersion(6, 28, 0)) {
			//6.28版本前不展示设置了仅在6.28版本后展示的block
			continue
		}
		if blockInfo.GetMode() == tabPB.Block_USER_INPUT {
			continue
		}

		switch mode {
		case channel_play.GetSecondaryFilterReq_PUBCHANNEL:
			block, ok := b.PubChannelHandler.GetBaseBlock(ctx, blockInfo, opFunc...)
			if ok {
				baseBlocks = append(baseBlocks, block)
			}
		case channel_play.GetSecondaryFilterReq_MATCHTEAMMATE:
			block, ok := b.QuickMatchHandler.GetBaseBlock(ctx, blockInfo, opFunc...)
			if ok {
				baseBlocks = append(baseBlocks, block)
			}
		case channel_play.GetSecondaryFilterReq_FILTER, channel_play.GetSecondaryFilterReq_FilterAfter628,
			channel_play.GetSecondaryFilterReq_FilterWithMultiOption, channel_play.GetSecondaryFilterReq_PC_HOME_PAGE,
			channel_play.GetSecondaryFilterReq_HUAN_YOU_MIX_LABEL:
			block, ok := b.HomepageHandler.GetBaseBlock(ctx, blockInfo, opFunc...)
			if ok {
				baseBlocks = append(baseBlocks, block)
			}
		case channel_play.GetSecondaryFilterReq_QUESTION, channel_play.GetSecondaryFilterReq_QuestionAfter628:
			block, ok := b.PlayQuestionHandler.GetBaseBlock(ctx, blockInfo, opFunc...)
			if ok {
				baseBlocks = append(baseBlocks, block)
			}
		case channel_play.GetSecondaryFilterReq_PubChannelAfter628, channel_play.GetSecondaryFilterReq_PubChannelAfter640:
			//6.28版本后的发布弹窗使用DisplayBlockInfos字段
			return
		default:
			log.WarnWithCtx(ctx, "BaseBlockHandler GetBaseBlock error mode %v", mode)
		}
	}
	return baseBlocks
}

func (b *BaseBlockHandler) GetBaseBlockByEntrance(ctx context.Context, tabInfo *tabPB.Tab, mode uint32, serviceInfo *grpc.ServiceInfo) (
	blocks []*topic_channel.Block, err error) {

	blocks = make([]*topic_channel.Block, 0)
	blockInfo := cache.GetBaseBlocksByTabId(tabInfo.GetId(), uint32(serviceInfo.ClientType))
	if blockInfo == nil {
		// 读缓存，没有block
		return blocks, nil
	}
	inMode := channel_play.GetSecondaryFilterReq_Mode(mode)
	blocks = b.GetBaseBlock(ctx, inMode, tabInfo.GetTabType(), blockInfo, serviceInfo)
	return blocks, nil
}

func genElem(blockElems []*tabPB.Elem, mode channel_play.GetSecondaryFilterReq_Mode) (elems []*topic_channel.Elem) {
	elems = make([]*topic_channel.Elem, 0)
	for _, e := range blockElems {
		elem := &topic_channel.Elem{
			Relations: &topic_channel.Relation{},
		}
		// 如果是小游戏并且二级字段默认字段,不显示
		if e.Title == "default_model" {
			continue
		}
		elem.Priority = e.GetPriority()
		elem.Title = e.GetTitle()
		elem.Id = e.GetId()
		elem.Mode = topic_channel.Elem_NORMAL
		if mode == channel_play.GetSecondaryFilterReq_PUBCHANNEL || mode == channel_play.GetSecondaryFilterReq_PubChannelAfter628 {
			elem.Relations.BlockId = e.GetContacts().GetBlockId()
			elem.Relations.Before = e.GetContacts().GetBefore()
			elem.Relations.After = e.GetContacts().GetAfter()
		}
		elem.MiniGameModel = e.MiniGameModel
		elem.TeamSize = e.TeamSize

		elems = append(elems, elem)
	}
	return elems
}

func (b *BaseBlockHandler) GetBlockRelations(clientType uint32, tabInfo *tabPB.Tab, mode channel_play.GetSecondaryFilterReq_Mode) []*channel_play.DisplayBlockInfo {
	if mode == channel_play.GetSecondaryFilterReq_PubChannelAfter628 {
		// 旧版本（6.40之前）要过滤掉输入类型的展示
		allBlocks := cache.GetBlockRelationByTabId(tabInfo.GetId(), clientType)
		rspBlocks := make([]*channel_play.DisplayBlockInfo, 0, len(allBlocks))
		for _, block := range allBlocks {
			if block.GetBlock().GetMode() == topic_channel.Block_USER_INPUT {
				continue
			}
			elemBindBlock := make([]*channel_play.ElemBindBlockInfo, 0, len(block.GetElemBindBlockInfos()))
			for _, tmpBlockInfo := range block.GetElemBindBlockInfos() {
				bindBlocks := make([]*topic_channel.Block, 0, len(tmpBlockInfo.GetBindBlocks()))
				for _, bindBlock := range tmpBlockInfo.GetBindBlocks() {
					if bindBlock.GetMode() == topic_channel.Block_USER_INPUT {
						continue
					}
					bindBlocks = append(bindBlocks, bindBlock)
				}
				if len(bindBlocks) > 0 {
					elemBindBlock = append(elemBindBlock, &channel_play.ElemBindBlockInfo{
						ElemId:     tmpBlockInfo.GetElemId(),
						BindBlocks: bindBlocks,
					})
				}
			}
			rspBlocks = append(rspBlocks, &channel_play.DisplayBlockInfo{
				Block:              block.GetBlock(),
				ElemBindBlockInfos: elemBindBlock,
			})
		}
		return rspBlocks
	}

	if mode == channel_play.GetSecondaryFilterReq_PubChannelAfter640 {
		return cache.GetBlockRelationByTabId(tabInfo.GetId(), clientType)
	}

	return nil
}

func (m *BaseBlockHandler) GetFilterABTestBlockOptionMap(ctx context.Context, tabIds []uint32, serviceInfo *grpc.ServiceInfo) map[uint32]map[uint32]map[uint32]bool {
	filterMap := make(map[uint32]map[uint32]map[uint32]bool)

	if isClose := conf.ChannelPlayLogicConfig.IsABTestFilterClose(); isClose {
		return filterMap
	}
	filterBlockOptionListRsp, err := m.rcmdChannelLabelClient.GetFilterBlockOptionList(ctx, &rcmdPb.GetFilterBlockOptionListReq{
		Uid:    serviceInfo.UserID,
		TabIds: tabIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFilterABTestBlockOptionMap GetFilterBlockOptionList tabIds:%v, err:%v", tabIds, err)
	}
	for _, filterBlockOption := range filterBlockOptionListRsp.GetBlockOptionList() {
		filterBlockElemMap := make(map[uint32]map[uint32]bool)

		for _, blockOption := range filterBlockOption.GetBlockOptions() {
			if _, ok := filterBlockElemMap[blockOption.GetBlockId()]; ok {
				filterBlockElemMap[blockOption.GetBlockId()][blockOption.GetElemId()] = true
			} else {
				filterElemMap := make(map[uint32]bool)
				filterElemMap[blockOption.GetElemId()] = true
				filterBlockElemMap[blockOption.GetBlockId()] = filterElemMap
			}
		}
		filterMap[filterBlockOption.GetTabId()] = filterBlockElemMap
	}
	return filterMap
}

func (m *BaseBlockHandler) GetHomeGameLabelAndBlocksByFilterIds(ctx context.Context, multiTabFilterIds []string,
	serviceInfo *grpc.ServiceInfo, hotLabel map[string]*rcmdPb.HotGameLabel, musicTabIdMap map[uint32]string,
	musicHotGameLabel map[uint32]*rcmdPb.HotGameLabel, filterABTestMap map[uint32]map[uint32]map[uint32]bool) (
	labelsInfoMap map[string]*BlockAndLabel) {
	labelsInfoMap = make(map[string]*BlockAndLabel)

	if len(multiTabFilterIds) == 0 && len(musicTabIdMap) == 0 {
		return labelsInfoMap
	}

	resp, err := m.tabCli.FindFilterMixTabIds(ctx, &tabPB.FindFilterMixTabIdsReq{
		FilterIds: multiTabFilterIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetHomeGameLabelAndBlocksByFilterIds FindFilterMixTabIds multiTabFilterIds:%v, err:%v", multiTabFilterIds, err)
		return labelsInfoMap
	}
	mixTabIdMap := resp.GetMixTabMap()
	// 热门标签，二级筛选项
	for _, filterId := range multiTabFilterIds {
		label := hotLabel[filterId]

		//根据是否开启玩法，拿二级筛选项
		if label.GetEnable() {
			labelsInfoMap[filterId] = &BlockAndLabel{
				HotLabel:      utils.ConvertRcmdGameLabel(label.GetLabels(), topic_channel.GameLabelType_HotLabel),
				IsLabelOn:     true,
				ClassifyLabel: utils.ConvertClassifyLabels(label.GetClassifyLabels()),
				MixTabId:      mixTabIdMap[filterId],
			}
		} else {
			labelsInfoMap[filterId] = &BlockAndLabel{
				IsLabelOn: false,
			}
		}
	}

	for musicTabId, filterId := range musicTabIdMap {
		label := musicHotGameLabel[musicTabId]
		if label.GetEnable() {
			labelsInfoMap[filterId] = &BlockAndLabel{
				HotLabel:      utils.ConvertRcmdGameLabel(label.GetLabels(), topic_channel.GameLabelType_HotLabel),
				IsLabelOn:     true,
				ClassifyLabel: utils.ConvertClassifyLabels(label.GetClassifyLabels()),
				MixTabId:      musicTabId,
				Blocks:        m.GenBlockWhenGameLabelOn(ctx, musicTabId, serviceInfo, filterABTestMap),
			}
		} else {
			labelsInfoMap[filterId] = &BlockAndLabel{
				IsLabelOn: false,
			}
		}
	}
	return labelsInfoMap
}

func (m *BaseBlockHandler) GetHomeGameLabelAndBlocksByTabIds(ctx context.Context, tabIds []uint32, serviceInfo *grpc.ServiceInfo,
	getBlockSource channel_play.GetSecondaryFilterReq_Mode, tabCache map[uint32]*tabPB.Tab, hotLabel map[uint32]*rcmdPb.HotGameLabel,
	needHandleMinorityGame bool, filterABTestMap map[uint32]map[uint32]map[uint32]bool) (
	gameBlockAndLabelMap map[uint32]*BlockAndLabel, err error) {

	gameBlockAndLabelMap = make(map[uint32]*BlockAndLabel)
	if len(tabIds) == 0 {
		return gameBlockAndLabelMap, nil
	}

	//filterABTestMap := m.GetFilterABTestBlockOptionMap(rcmdCtx, tabIds, serviceInfo)
	otherGameTabId := cache.MinorityGameParentId

	// 热门标签，二级筛选项
	for _, tabId := range tabIds {
		// label可能为nil,一定要用get方法
		label := hotLabel[tabId]
		tabInfo := tabCache[tabId]
		gameBlockAndLabelMap[tabId] = &BlockAndLabel{
			HotLabel: utils.ConvertRcmdGameLabel(label.GetLabels(), topic_channel.GameLabelType_HotLabel),
		}
		//根据是否开启玩法，拿二级筛选项
		if label.GetEnable() {
			if !needHandleMinorityGame || otherGameTabId != tabId {
				gameBlockAndLabelMap[tabId].Blocks = m.GenBlockWhenGameLabelOn(ctx, tabId, serviceInfo, filterABTestMap)
			}
			gameBlockAndLabelMap[tabId].ClassifyLabel = utils.ConvertClassifyLabels(label.GetClassifyLabels())
			gameBlockAndLabelMap[tabId].IsLabelOn = true
		} else {
			gameBlockAndLabelMap[tabId].Blocks, err = m.GetBaseBlockByEntrance(ctx, tabInfo, uint32(getBlockSource), serviceInfo)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetHomeGameLabelAndBLocks GetBaseBlockByEntrance tabId:%d, getBlockSource:%v err:%v",
					tabInfo.GetId(), getBlockSource, err)
				return
			}
		}
	}
	return
}

func (m *BaseBlockHandler) splitTabHotGameLabel(musicTabIdMap map[uint32]string, hotLabelMap map[uint32]*rcmdPb.HotGameLabel) (
	musicBlockAndLabelMap map[uint32]*rcmdPb.HotGameLabel, gameBlockAndLabelMap map[uint32]*rcmdPb.HotGameLabel) {
	musicBlockAndLabelMap = make(map[uint32]*rcmdPb.HotGameLabel)
	gameBlockAndLabelMap = make(map[uint32]*rcmdPb.HotGameLabel)

	for hotLabel, hotLabelInfo := range hotLabelMap {
		if _, ok := musicTabIdMap[hotLabel]; ok {
			musicBlockAndLabelMap[hotLabel] = hotLabelInfo
		} else {
			gameBlockAndLabelMap[hotLabel] = hotLabelInfo
		}
	}
	return musicBlockAndLabelMap, gameBlockAndLabelMap
}

func (m *BaseBlockHandler) GetHomeGameLabelAndBlocks(ctx context.Context, tabIds []uint32, multiTabFilterIds []string,
	musicTabIdMap map[uint32]string, serviceInfo *grpc.ServiceInfo, getBlockSource channel_play.GetSecondaryFilterReq_Mode,
	browseLabels map[uint32]*topic_channel.BrowseLabel, needHandleMinorityGame bool) (
	gameBlockAndLabelMap map[uint32]*BlockAndLabel, musicLabelsMap map[string]*BlockAndLabel, err error) {

	if len(tabIds) == 0 && len(multiTabFilterIds) == 0 && len(musicTabIdMap) == 0 {
		log.WarnWithCtx(ctx, "GetHomeGameLabelAndBLocks tabIds && filterIds is empty")
		return
	}
	tabCache := cache.GetTabInfoCache().GetTabIdCache()
	otherGameTabId := cache.MinorityGameParentId

	var mixTabItem []*rcmdPb.TabItem
	for _, tabId := range tabIds {
		if needHandleMinorityGame && tabId == otherGameTabId {
			mixTabItem = append(mixTabItem, &rcmdPb.TabItem{
				TabId:   tabId,
				TabType: uint32(rcmdPb.TabItem_TabTypeGangUpCombTab),
			})
		} else {
			mixTabItem = append(mixTabItem, &rcmdPb.TabItem{
				TabId:   tabId,
				TabType: uint32(rcmdPb.TabItem_TabTypeSingleTab),
			})
		}
	}

	for _, filterId := range multiTabFilterIds {
		mixTabItem = append(mixTabItem, &rcmdPb.TabItem{
			CombId:  filterId,
			TabType: uint32(rcmdPb.TabItem_TabTypeMtCombTab),
		})
	}
	for tabId := range musicTabIdMap {
		mixTabItem = append(mixTabItem, &rcmdPb.TabItem{
			TabId:   tabId,
			TabType: uint32(rcmdPb.TabItem_TabTypeSingleTab),
		})
	}

	var rcmdCtx context.Context
	var cancel context.CancelFunc
	if conf.GetEnv() == conf.Testing {
		rcmdCtx, cancel = context.WithTimeout(ctx, 2000*time.Millisecond)
		defer cancel()
	} else {
		rcmdCtx, cancel = context.WithTimeout(ctx, 500*time.Millisecond)
		defer cancel()
	}

	singleTabBrowseMap, multiTabBrowseMap := m.genBrowseLabelsMap(ctx, browseLabels)
	rcmdReq := &rcmdPb.BatchHotGameLabelsReq{
		Uid: serviceInfo.UserID,
		//TabIds:          tabIds,
		TabList:                mixTabItem,
		BrowseLabelsMap:        singleTabBrowseMap,
		CombTabBrowseLabelsMap: multiTabBrowseMap,
	}
	hotGameLabelResp, lowErr := m.rcmdChannelLabelClient.BatchHotGameLabels(rcmdCtx, rcmdReq)
	if lowErr != nil {
		// 降级处理，不抛出error,报错直接返回所有block
		log.ErrorWithCtx(ctx, "UserLine Rcmd: MutiEntryFilterMgr batchGenTabItems BatchHotGameLabels mixTabItem(%v) err(%v)", mixTabItem, lowErr)
		hotGameLabelResp = &rcmdPb.BatchHotGameLabelsResp{
			HotGameLabelsMap:     make(map[uint32]*rcmdPb.HotGameLabel),
			CombTabGameLabelsMap: make(map[string]*rcmdPb.HotGameLabel),
		}
	}
	if conf.ChannelPlayLogicConfig.GetLogSwitch() {
		log.InfoWithCtx(ctx, "GetHomeGameLabelAndBlocks BatchHotGameLabels rcmdReq:%s rcmdRsp:%s", rcmdReq.String(), hotGameLabelResp.String())
	}

	var musicHotGameLabel map[uint32]*rcmdPb.HotGameLabel // 音乐单品类标签
	var gameHotGameLabel map[uint32]*rcmdPb.HotGameLabel  // 游戏标签
	if len(musicTabIdMap) > 0 {
		musicHotGameLabel, gameHotGameLabel = m.splitTabHotGameLabel(musicTabIdMap, hotGameLabelResp.GetHotGameLabelsMap())
	} else {
		gameHotGameLabel = hotGameLabelResp.GetHotGameLabelsMap()
	}
	needRcmdFilterTabIds := make([]uint32, 0, len(tabIds)+len(musicTabIdMap))
	needRcmdFilterTabIds = append(needRcmdFilterTabIds, tabIds...)
	for tabId := range musicTabIdMap {
		needRcmdFilterTabIds = append(needRcmdFilterTabIds, tabId)
	}
	filterABTestMap := m.GetFilterABTestBlockOptionMap(rcmdCtx, needRcmdFilterTabIds, serviceInfo)

	gameBlockAndLabelMap, err = m.GetHomeGameLabelAndBlocksByTabIds(ctx, tabIds, serviceInfo, getBlockSource,
		tabCache, gameHotGameLabel, needHandleMinorityGame, filterABTestMap)

	musicLabelsMap = m.GetHomeGameLabelAndBlocksByFilterIds(ctx, multiTabFilterIds, serviceInfo, hotGameLabelResp.GetCombTabGameLabelsMap(),
		musicTabIdMap, musicHotGameLabel, filterABTestMap)

	return
}

func (m *BaseBlockHandler) genBrowseLabelsMap(ctx context.Context, browseLabelMap map[uint32]*topic_channel.BrowseLabel) (
	map[uint32]*rcmdPb.BrowseLabel, map[string]*rcmdPb.BrowseLabel) {
	if len(browseLabelMap) == 0 {
		return nil, nil
	}
	browseIds := make([]uint32, 0, len(browseLabelMap))
	for tabId := range browseLabelMap {
		browseIds = append(browseIds, tabId)
	}
	mixTabResp, err := m.tabCli.FindFilterIdsByMixTabIds(ctx, &tabPB.FindFilterIdsByMixTabIdsReq{
		MixTabIds: browseIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "genBrowseLabelsMap FindFilterIdsByMixTabIds browseIds:%v, err:%v", browseIds, err)
		return nil, nil
	}
	singleTabBrowseMap := make(map[uint32]*rcmdPb.BrowseLabel, len(browseLabelMap))
	multiTabBrowseMap := make(map[string]*rcmdPb.BrowseLabel, len(browseLabelMap))
	for tabId, label := range browseLabelMap {
		if filterId, ok := mixTabResp.GetMixTabFilterMap()[tabId]; ok {
			multiTabBrowseMap[filterId] = utils.ConvertBrowseLabel(label)
		} else {
			singleTabBrowseMap[tabId] = utils.ConvertBrowseLabel(label)
		}
	}
	return singleTabBrowseMap, multiTabBrowseMap
}

func (m *BaseBlockHandler) GenBlockWhenGameLabelOn(ctx context.Context, tabId uint32, serviceInfo *grpc.ServiceInfo,
	filterABTestMap map[uint32]map[uint32]map[uint32]bool) []*topic_channel.Block {
	res := make([]*topic_channel.Block, 0)
	filterABTestBlock := filterABTestMap[tabId]
	baseBlocks := cache.GetBaseBlocksByTabId(tabId, uint32(serviceInfo.ClientType))
	for _, block := range baseBlocks {
		if block.GetOnlyShowAfter_628() && !utils.IsMobileVersionHigherThan(serviceInfo.ClientType, serviceInfo.ClientVersion,
			protocol.FormatClientVersion(6, 28, 0)) {
			//6.28版本前不展示设置了仅在6.28版本后展示的block
			continue
		}
		if block.GetMode() == tabPB.Block_USER_INPUT {
			continue
		}

		tempElems := make([]*topic_channel.Elem, 0, len(block.GetElems()))
		for _, elem := range block.GetElems() {
			/*if !isShowElemInHomePage(elem) {
				continue
			}*/
			if filterABTestBlock != nil {
				if filterBlockMap, ok := filterABTestBlock[block.GetId()]; ok {
					if _, ok = filterBlockMap[elem.GetId()]; ok {
						log.DebugWithCtx(ctx, "filter tabId:%d, block:%d elem:%v", tabId, block.GetId(), elem.GetId())
						continue
					}
				}
			}

			tempElem := &topic_channel.Elem{
				Id:            elem.GetId(),
				Title:         elem.GetTitle(),
				MiniGameModel: elem.GetMiniGameModel(),
				TeamSize:      elem.GetTeamSize(),
			}
			tempElems = append(tempElems, tempElem)
		}
		if len(tempElems) == 0 {
			continue
		}
		tempBlock := &topic_channel.Block{
			Id:    block.GetId(),
			Title: block.GetTitle(),
			//玩法开启只返回常规属性，固定为多选
			Mode:  topic_channel.Block_MULTI,
			Elems: tempElems,
		}
		res = append(res, tempBlock)
	}

	return res
}

func isShowElemInHomePage(elem *tabPB.Elem) bool {
	for _, flag := range elem.GetPublicFlags() {
		if flag == tabPB.Elem_PublicFlagFilter {
			return true
		}
	}
	return false
}
