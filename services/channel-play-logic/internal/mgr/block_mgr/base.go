package block_mgr

import (
	channel_play "golang.52tt.com/protocol/app/channel-play"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	tabpb "golang.52tt.com/protocol/services/topic_channel/tab"
)

type BlockOptionData struct {
	TabType tabpb.TabType
	InMode  channel_play.GetSecondaryFilterReq_Mode
}

type BlockOpFunc func(*BlockOptionData)

func WithTabType(tabType tabpb.TabType) BlockOpFunc {
	return func(data *BlockOptionData) {
		data.TabType = tabType
	}
}

func WithInMode(inMode channel_play.GetSecondaryFilterReq_Mode) BlockOpFunc {
	return func(data *BlockOptionData) {
		data.InMode = inMode
	}
}

type BlockAndLabel struct {
	HotLabel      []*topic_channel.GameLabel
	Blocks        []*topic_channel.Block
	ClassifyLabel []*channel_play.ClassifyLabelList
	IsLabelOn     bool
	MixTabId      uint32 // 音乐项虚拟ID
}
