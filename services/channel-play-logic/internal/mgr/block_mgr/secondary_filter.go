package block_mgr

import (
	"context"
	"github.com/gookit/goutil/timex"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/device_id"
	device_info_service "golang.52tt.com/clients/datacenter/device-info-service"
	"golang.52tt.com/clients/expsvr"
	"golang.52tt.com/clients/new-user-reception"
	rcmdChannelLabel "golang.52tt.com/clients/topic-channel/rcmd-channel-label"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	device_pb "golang.52tt.com/protocol/datacenter/device-info-service"
	rcmdPb "golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	"golang.52tt.com/services/channel-play-logic/internal/utils"
	"strconv"
	"time"

	account "golang.52tt.com/clients/account-go"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/abtest"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/marketid_helper"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	appChannelPB "golang.52tt.com/protocol/app/channel"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	hobby_channel "golang.52tt.com/protocol/app/hobby-channel"
	"golang.52tt.com/protocol/common/status"
	receptionpb "golang.52tt.com/protocol/services/new-user-reception"
	tabpb "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-logic/internal/cache"
	"golang.52tt.com/services/channel-play-logic/internal/conf"
)

type SecondaryFilterMgr struct {
	baseBlockHandleMgr *BaseBlockHandler
	businessBlockMgr   IBusinessBlockFilterMgr
	supervisorInst     *supervision.Supervisory

	rcmdLabelClient *rcmdChannelLabel.Client
	receptionClient *new_user_reception.Client
	expClient       expsvr.IClient

	abTestClient  abtest.IABTestClient
	deviceInfoCli device_info_service.IClient
	accountClient account.IClient
}

func NewSecondaryFilterMgr(baseBlockMgr *BaseBlockHandler, businessBlockMgr *BusinessBlockFilterMgr, supervisorInst *supervision.Supervisory,
	rcmdLabelClient *rcmdChannelLabel.Client, receptionClient *new_user_reception.Client, expClient expsvr.IClient, deviceInfoCli device_info_service.IClient, accountClient account.IClient, abTestClient abtest.IABTestClient) (secondaryFilterMgr *SecondaryFilterMgr, err error) {
	secondaryFilterMgr = &SecondaryFilterMgr{
		businessBlockMgr:   businessBlockMgr,
		baseBlockHandleMgr: baseBlockMgr,
		supervisorInst:     supervisorInst,
		rcmdLabelClient:    rcmdLabelClient,
		receptionClient:    receptionClient,
		expClient:          expClient,
		deviceInfoCli:      deviceInfoCli,
		accountClient:      accountClient,
		abTestClient:       abTestClient,
	}

	return secondaryFilterMgr, nil

}

func (m *SecondaryFilterMgr) GetSecondaryFilterByCategory(ctx context.Context, categoryId uint32,
	serviceInfo *grpc.ServiceInfo, channelPkg string) (
	items []*hobby_channel.GameHomePageFilterItem, businessFilter []*channel_play.BusinessFilterItem, err error) {
	items = make([]*hobby_channel.GameHomePageFilterItem, 0)
	businessFilter = make([]*channel_play.BusinessFilterItem, 0)

	//判断是否敏感分类
	supConfInst := &supervision.SupervisoryConf{
		RegisterLimitTime:      conf.PublicSwitchConfig.GetRegisterLimitTime(),
		UserLevelLimit:         conf.PublicSwitchConfig.GetUserLevelLimit(),
		RealNameStandardStatus: conf.PublicSwitchConfig.GetRealNameStandardStatus(),
	}
	tabFilter, categoryFilter := m.supervisorInst.GetFilterMap(ctx, serviceInfo, channelPkg, supConfInst)
	if categoryFilter[categoryId] {
		return items, businessFilter, nil
	}

	tabsOfCategoryMap := cache.GetTabsOfCategoryCache()
	if cacheRes, ok := tabsOfCategoryMap[categoryId]; ok {
		tabInfos := make([]*tabpb.Tab, 0)
		//过滤tabs
		for _, tabInfo := range cacheRes {
			// 监管配置
			if tabFilter[tabInfo.GetId()] {
				continue
			}

			//tips 这里的过滤策略应该与topic-channel-logic的GetTabList接口过滤策略一致.2023/1/28
			if m.supervisorInst.GamePageFilterStrategy(tabInfo, serviceInfo, cache.GetWhiteList()) {
				log.WarnWithCtx(ctx, "GetSecondaryFilterByCategory uid %v tabId %v filtered by MiniGameStageStrategy", serviceInfo.UserID, tabInfo.Id)
				continue
			}
			tabInfos = append(tabInfos, tabInfo)
		}
		items = convertTabsToHomePageFilterItemSlice(tabInfos, serviceInfo)
		filter, _ := m.businessBlockMgr.GetBusinessFilterByCategoryId(categoryId)
		businessFilter = append(businessFilter, filter...)

		return items, businessFilter, nil

	} else {
		log.WarnWithCtx(ctx, "GetSecondaryFilterByCategory uid %v categoryId %v err %v", serviceInfo.UserID, categoryId, err)
		return items, businessFilter, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError)
	}

}

func (m *SecondaryFilterMgr) GetSecondaryFilterByFastPCCategory(ctx context.Context, categoryId uint32,
	serviceInfo *grpc.ServiceInfo, channelPkg string) (
	items []*hobby_channel.GameHomePageFilterItem, businessFilter []*channel_play.BusinessFilterItem, err error) {
	items = make([]*hobby_channel.GameHomePageFilterItem, 0)
	businessFilter = make([]*channel_play.BusinessFilterItem, 0)

	//判断是否敏感分类
	supConfInst := &supervision.SupervisoryConf{
		RegisterLimitTime:      conf.PublicSwitchConfig.GetRegisterLimitTime(),
		UserLevelLimit:         conf.PublicSwitchConfig.GetUserLevelLimit(),
		RealNameStandardStatus: conf.PublicSwitchConfig.GetRealNameStandardStatus(),
	}
	tabFilter, categoryFilter := m.supervisorInst.GetFilterMap(ctx, serviceInfo, channelPkg, supConfInst)

	fastPCCategoryConfigMap := cache.GetTabInfoCache().GetFastPCCategoryConfigByIdMap()
	if categoryInfo, ok := fastPCCategoryConfigMap[categoryId]; ok {
		tabInfos := make([]*tabpb.Tab, 0)
		for _, tabId := range categoryInfo.GetTabIds() {
			tabInfo := cache.GetTabInfoCache().GetTabInfoCacheById(tabId)
			// 监管配置
			if tabFilter[tabInfo.GetId()] || categoryFilter[tabInfo.GetCategoryId()] {
				continue
			}

			if m.supervisorInst.MiniGameStageStrategy(tabInfo, serviceInfo, cache.GetWhiteList()) {
				log.InfoWithCtx(ctx, "GetSecondaryFilterByFastPCCategory uid %v tabId %v filtered by MiniGameStageStrategy", serviceInfo.UserID, tabInfo.Id)
				continue
			}

			tabInfos = append(tabInfos, tabInfo)
		}
		items = convertTabsToHomePageFilterItemSlice(tabInfos, serviceInfo)
		// 极速pc分类暂时没配置businessFilter
		//filter, _ := m.businessBlockMgr.GetBusinessFilterByFastPCCategoryId(categoryId)
		//businessFilter = append(businessFilter, filter...)

		return items, businessFilter, nil

	} else {
		log.WarnWithCtx(ctx, "GetSecondaryFilterByFastPCCategory uid %v categoryId %v err %v", serviceInfo.UserID, categoryId, err)
		return items, businessFilter, protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError)
	}

}

func convertTabsToHomePageFilterItemSlice(tabInfos []*tabpb.Tab, serviceInfo *grpc.ServiceInfo) (itemSlice []*hobby_channel.GameHomePageFilterItem) {
	itemSlice = make([]*hobby_channel.GameHomePageFilterItem, 0, len(tabInfos))
	for _, tabInfo := range tabInfos {
		item := &hobby_channel.GameHomePageFilterItem{
			TabId: tabInfo.GetId(),
			Title: tabInfo.GetName(),
		}
		if protocol.IsFastPcClientType(uint32(serviceInfo.ClientType)) {
			item.TabImageUrl = tabInfo.GetCardsImageUrl()
		} else {
			item.TabImageUrl = tabInfo.GetSmallCardUrl()
		}
		itemSlice = append(itemSlice, item)
	}
	return
}

func (m *SecondaryFilterMgr) userLevelCheck(ctx context.Context, uid, marketId uint32) (isLevelEnough bool, err error) {
	minLevelMap := conf.ChannelPlayLogicConfig.GetPublishChannelMinimumUserLevel()
	if minLevelMap == nil {
		return true, nil
	}
	minLevel := minLevelMap[marketId]
	// 没有配置或者配置为0，代表关闭
	if minLevel == 0 {
		log.DebugWithCtx(ctx, "PublishUserLevelCheck success, minLevel is 0")
		return true, nil
	}
	_, userLevel, err := m.expClient.GetUserExp(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "PublishUserLevelCheck GetUserExp fail, uid:%d, err:%s", uid, err.Error())
		return false, err
	}
	if userLevel <= minLevel {
		log.DebugWithCtx(ctx, "PublishUserLevelCheck fail, uid:%d, userLevel:%d, minLevel:%d", uid, userLevel, minLevel)
		return false, nil
	}
	return true, nil
}

func (m *SecondaryFilterMgr) GetAbResult(ctx context.Context, serviceInfo *grpc.ServiceInfo) (bool, error) {
	newCtx, cancel := context.WithTimeout(ctx, 1000*time.Millisecond)
	defer cancel()
	var isInShieldWordAbtest bool
	deviceId, err := device_id.ToClientDeviceId(device_id.ToDeviceHexId(serviceInfo.DeviceID, true), uint32(serviceInfo.ClientType))
	if err != nil {
		log.ErrorWithCtx(newCtx, "GetAbResult device_id.ToClientDeviceId deviceID(%s) err:%v", serviceInfo.DeviceID, err)
		return isInShieldWordAbtest, err
	}
	// 查询用户获客状态
	userType, err := m.deviceInfoCli.GetUserTypeByDeviceState(newCtx, &device_pb.DeviceInfoRequestData{
		AppId:    marketid_helper.GetAppName(serviceInfo.MarketID),
		DeviceId: deviceId,
		Uid:      strconv.FormatInt(int64(serviceInfo.UserID), 10),
	})
	if err != nil {
		log.ErrorWithCtx(newCtx, "GetAbResult GetUserTypeByDeviceState err:%v, serviceInfo:%s", err, serviceInfo.String())
		return isInShieldWordAbtest, err
	}
	user, err := m.accountClient.GetUserByUid(newCtx, serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAbResult GetUserByUid uid(%d) err: %v", serviceInfo.UserID, err)
		return isInShieldWordAbtest, err
	}

	// 查询AB实验结果 筛选列表
	shieldWordAbCfg := conf.ChannelPlayLogicConfig.GetShieldWordTestConfig()
	if shieldWordAbCfg == nil {
		log.WarnWithCtx(newCtx, "GetAbResult abCfg is nil, uid:%d", serviceInfo.UserID)
		return isInShieldWordAbtest, err
	}
	shieldWordArgKey := shieldWordAbCfg.ActiveKey
	if timex.FromUnix(int64(user.GetRegisteredAt())).After(timex.TodayStart()) || userType == device_info_service.Recall || userType == device_info_service.Pull {
		shieldWordArgKey = shieldWordAbCfg.HuoKeKey
	}
	if result, err := m.abTestClient.GetUidTestArgVal(newCtx, serviceInfo.UserID, shieldWordArgKey); err != nil {
		log.ErrorWithCtx(newCtx, "GetAbResult GetUidTestArgVal uid(%d) abCfg(%v) err: %v", serviceInfo.UserID, shieldWordAbCfg, err)
		return isInShieldWordAbtest, err
	} else {
		isInShieldWordAbtest = result == shieldWordAbCfg.ExpectValue
		log.InfoWithCtx(newCtx, "GetAbResult GetUidTestArgVal uid(%d) abCfg(%v) result: %s", serviceInfo.UserID, shieldWordAbCfg, result)
		return isInShieldWordAbtest, err
	}
}

func (m *SecondaryFilterMgr) GetSecondaryFilter(ctx context.Context, in *channel_play.GetSecondaryFilterReq, serviceInfo *grpc.ServiceInfo) (
	out *channel_play.GetSecondaryFilterResp, err error) {
	out = &channel_play.GetSecondaryFilterResp{}

	if in.GetMode() == channel_play.GetSecondaryFilterReq_PubChannelAfter640 {
		isLeverEnough, err := m.userLevelCheck(ctx, serviceInfo.UserID, serviceInfo.MarketID)
		if err != nil {
			return out, err
		}
		if !isLeverEnough {
			out.NoticeMsg = "您当前等级不足，暂不允许发布房间"
		}
	}
	//缓存获取所有tab信息
	tabInfos := cache.GetTabInfoCache().GetTabIdCache()
	//找到请求的tab的信息
	var tabInfo *tabpb.Tab
	var ok bool
	if tabInfo, ok = tabInfos[in.GetTabId()]; !ok {
		utils.WarnCntLog(ctx, "GetSecondaryFilter tab not found, in:%+v", in)
		return out, protocol.NewExactServerError(nil, status.ErrTopicChannelTabNotFound)
	}

	//二级筛选器统一tab过滤策略
	if !conf.PublicSwitchConfig.GetChannelListNoFilterTabSwitch(tabInfo.GetId()) && m.supervisorInst.GamePageSecondaryFilterStrategy(tabInfo, serviceInfo, cache.GetWhiteList()) {
		log.WarnWithCtx(ctx, "tabId: %d err byGamePageSecondaryFilterStrategy", tabInfo.GetId())
		return out, nil
	}

	//根据tab信息，赋值
	tabType := tabpb.TabType(tabInfo.TabType)
	matchType := tabInfo.MatchType
	tabVer := tabInfo.Version
	// 目前默认只有小游戏走临时房匹配,以后可能会有其他类型房间
	if matchType == tabpb.Tab_TEMPORARYHOUSE {
		out.MatchType = channel_play.GetSecondaryFilterResp_TEMPORARYHOUSE
		out.ChannelSource = uint32(appChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_QUICK_MATCH_V2)
	} else {
		out.MatchType = channel_play.GetSecondaryFilterResp_QUICK
		out.ChannelSource = uint32(appChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_QUICK_MATCH)
	}
	out.TabVersion = tabVer
	out.TabType = channel_play.GetSecondaryFilterResp_TabType(tabType)
	out.TabId = tabInfo.GetId()

	//根据配置文件，赋值
	if conf.Environment == conf.Production {
		out.IsHiddenGeoOption = conf.PublicSwitchConfig.IsHiddenGeoOption()
	} else {
		out.IsHiddenGeoOption = conf.PublicSwitchConfig.IsHiddenGeoOptTest()
	}
	out.FreshOption = conf.PublicSwitchConfig.GetFreshOption()

	//根据入口过滤base block
	if in.GetMode() == channel_play.GetSecondaryFilterReq_FilterWithMultiOption || in.GetMode() == channel_play.GetSecondaryFilterReq_PC_HOME_PAGE ||
		in.GetMode() == channel_play.GetSecondaryFilterReq_HUAN_YOU_MIX_LABEL {
		needHandleMinorityGame := false
		if in.GetMode() == channel_play.GetSecondaryFilterReq_HUAN_YOU_MIX_LABEL {
			needHandleMinorityGame = true
		}
		gameBlockAndLabelMap, _, err := m.baseBlockHandleMgr.GetHomeGameLabelAndBlocks(ctx, []uint32{tabInfo.GetId()}, nil, nil, serviceInfo,
			in.GetMode(), map[uint32]*topic_channel.BrowseLabel{tabInfo.GetId(): in.GetBrowseLabels()}, needHandleMinorityGame)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetSecondaryFilter GetBaseBlockByEntrance get baseBlock err:%v, in:%s", err, in.String())
			return out, err
		}
		//极速pc不做实验
		if protocol.IsFastPcClientType(uint32(serviceInfo.ClientType)) {
			out.ShowFilterWords = true
		} /*else {
			//欢游还没开启屏蔽词
			isInShieldWordAbtest, _ := m.GetAbResult(ctx, serviceInfo)
			if isInShieldWordAbtest {
				out.ShowFilterWords = true
			}
		}*/
		if blockAndLabel, _ := gameBlockAndLabelMap[tabInfo.GetId()]; blockAndLabel != nil {
			out.Blocks = blockAndLabel.Blocks
			out.Labels = blockAndLabel.HotLabel
			out.ClassifyLabels = blockAndLabel.ClassifyLabel
		}
	} else {
		out.Blocks, err = m.baseBlockHandleMgr.GetBaseBlockByEntrance(ctx, tabInfo, uint32(in.GetMode()), serviceInfo)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetSecondaryFilter GetBaseBlockByEntrance get baseBlock err:%v, in:%s", err, in.String())
			return out, err
		}
	}

	//根据入口获取business block
	out.BusinessBlocks, err = m.businessBlockMgr.GetBusinessBlockByEntrance(ctx, tabInfo, uint32(in.GetMode()), uint32(in.GetSource()), uint32(serviceInfo.ClientType))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSecondaryFilter GetBusinessBlockByEntrance get businessBlock err:%v, in:%s", err, in.String())
		return out, err
	}
	//根据入口，返回6.28版本后的发布项及其层级接口
	out.DisplayBlockInfos = m.baseBlockHandleMgr.GetBlockRelations(uint32(serviceInfo.ClientType), tabInfo, in.GetMode())
	//log.DebugWithCtx(ctx, "GetSecondaryFilter out.DisplayBlockInfos:%+v", out.GetDisplayBlockInfos())

	//根据入口以及配置文件，赋值 needHighLight 判断tab是否需要进行用户行为规范
	out.NeedHighlight = judgeHighLight(tabInfo.GetCategoryId())
	out.GuidingForNewBie = m.getNewBieGuideTitle(ctx, serviceInfo.UserID, in.GetMode())
	return out, nil
}

func (m *SecondaryFilterMgr) getNewBieGuideTitle(ctx context.Context, uid uint32, mode channel_play.GetSecondaryFilterReq_Mode) (text string) {
	if mode != channel_play.GetSecondaryFilterReq_PubChannelAfter628 || uid == 0 {
		return
	}
	resp, err := m.receptionClient.GetUserReceptionGroupInfoByUid(ctx, &receptionpb.GetUserReceptionGroupInfoByUidReq{
		Uid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getNewBieGuideTitle uid:%d, mode:%v, err:%v", uid, mode, err)
		return
	}
	if resp.GetInfo().GetUid() == uid {
		text = conf.ChannelPlayLogicConfig.GetNewBieGuideTitle()
	}
	return
}

func judgeHighLight(categoryId uint32) bool {
	cfg := conf.ChannelPlayLogicConfig.LoadConfig()
	if cfg == nil || cfg.NeedHighLightCategory == nil {
		log.Errorf("ChannelPlayConfig LoadConfig NeedHighLightCategory error")
		return false
	}
	categoryIds := cfg.NeedHighLightCategory
	for _, v := range categoryIds {
		if v == categoryId {
			return true
		}
	}
	return false
}

func (m *SecondaryFilterMgr) GetPublishOptionGuide(ctx context.Context, tabId, uid uint32, roomName string) (
	options []*channel_play.PublishOptionElem, optionLabels, inputLabels []*channel_play.RecommendationElem, err error) {
	labels, err := m.rcmdLabelClient.GetRelatedPublishLabels(ctx, &rcmdPb.GetRelatedPublishLabelsReq{
		Uid:   uid,
		TabId: tabId,
		Text:  roomName,
	})
	if err != nil {
		return
	}
	log.InfoWithCtx(ctx, "GetPublishOptionGuide rcmd uid:%d, tabId:%d, roomName:%s, resp:%s", uid, tabId, roomName, labels.String())
	options = make([]*channel_play.PublishOptionElem, 0, len(labels.GetPublishLabel()))
	// 6.61.5版本之前推荐选择区域标签
	for _, label := range labels.GetPublishLabel() {
		option := m.getBlockElemInfo(tabId, label.GetBlockOptions())
		if option == nil {
			log.WarnWithCtx(ctx, "GetPublishOptionGuide blockOption no exist uid:%d, tabId:%d, roomName:%s label:%s",
				uid, tabId, roomName, label.String())
			continue
		}
		options = append(options, option)
	}

	// 6.61.5版本后推荐选择区域标签
	optionLabels = make([]*channel_play.RecommendationElem, 0, len(labels.GetRecommendationOptions()))
	for _, rcmdOption := range labels.GetRecommendationOptions() {
		optionLabels = append(optionLabels, m.convertRcmdOption(tabId, rcmdOption))
	}
	// 6.61.5版本后推荐填写区域标签
	inputLabels = make([]*channel_play.RecommendationElem, 0, len(labels.GetRecommendationInputs()))
	for _, rcmdInput := range labels.GetRecommendationInputs() {
		inputLabels = append(inputLabels, m.convertRcmdInput(tabId, rcmdInput))
	}

	return
}

// 从缓存中获取block elem title信息
func (m *SecondaryFilterMgr) getBlockElemInfo(tabId uint32, option *rcmdPb.BlockOption) *channel_play.PublishOptionElem {
	blocksCache, ok := cache.GetBaseBlocksMapCache()[tabId]
	if !ok {
		return nil
	}
	for _, block := range blocksCache {
		if block.GetId() != option.GetBlockId() {
			continue
		}
		for _, elem := range block.GetElems() {
			if elem.GetId() != option.GetElemId() {
				continue
			}
			return &channel_play.PublishOptionElem{
				BlockId:   block.GetId(),
				ElemId:    elem.GetId(),
				ElemTitle: elem.GetTitle(),
			}
		}
		return nil
	}
	return nil
}

func (m *SecondaryFilterMgr) convertRcmdOption(tabId uint32, option *rcmdPb.RecommendationOption) *channel_play.RecommendationElem {
	out := &channel_play.RecommendationElem{
		Type: option.GetOptionType(),
	}
	switch option.GetOptionType() {
	case uint32(rcmdPb.RecommendationOption_Publish):
		out.Data = &channel_play.RecommendationElem_PublishOptionElem{
			PublishOptionElem: m.getBlockElemInfo(tabId, option.GetBlockOptions()),
		}
	case uint32(rcmdPb.RecommendationOption_ThirdLabel):
		out.Data = &channel_play.RecommendationElem_GameLabel{
			GameLabel: convertGameLabel(option.GetLabel()),
		}
	}
	return out
}

func (m *SecondaryFilterMgr) convertRcmdInput(tabId uint32, input *rcmdPb.RecommendationInput) *channel_play.RecommendationElem {
	return &channel_play.RecommendationElem{
		Type: uint32(channel_play.RecommendationElem_TYPE_Publish),
		Data: &channel_play.RecommendationElem_PublishOptionElem{
			PublishOptionElem: m.getBlockElemInfo(tabId, input.GetBlockOptions()),
		},
	}
}

func convertGameLabel(label *rcmdPb.GameLabel) *topic_channel.GameLabel {
	return &topic_channel.GameLabel{
		ValValue:    label.GetVal(),
		DisplayName: label.GetDisplayName(),
		Type:        topic_channel.GameLabelType(label.GetType()),
		LabelType:   label.GetLabelType(),
	}
}
