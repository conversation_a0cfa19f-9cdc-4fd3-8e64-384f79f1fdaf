package block_mgr

import (
	"context"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
)

type HomePageHandler struct {
}

func NewHomePageHandler() *HomePageHandler {

	homePageHandler := &HomePageHandler{}
	return homePageHandler
}
func (b *HomePageHandler) getBaseBlockInfo(blockInfo *tabPB.Block) *topic_channel.Block {

	block := &topic_channel.Block{
		Id:              blockInfo.GetId(),
		Title:           blockInfo.GetTitle(),
		ControlTeamSize: blockInfo.GetControlTeamSize(),
	}
	return block
}

func (b *HomePageHandler) getBlockMode(mode channel_play.GetSecondaryFilterReq_Mode) topic_channel.Block_Mode {
	if mode == channel_play.GetSecondaryFilterReq_FilterWithMultiOption || mode == channel_play.GetSecondaryFilterReq_HUAN_YOU_MIX_LABEL {
		return topic_channel.Block_MULTI
	}
	return topic_channel.Block_SINGLE
}

func (b *HomePageHandler) getBlockElem(blockInfo *tabPB.Block, mode channel_play.GetSecondaryFilterReq_Mode) []*topic_channel.Elem {
	elems := make([]*topic_channel.Elem, 0, 100)
	if mode == channel_play.GetSecondaryFilterReq_FILTER || mode == channel_play.GetSecondaryFilterReq_FilterAfter628 {
		//6.28版本以前，首页筛选器，服务端加不限
		elemInf := &topic_channel.Elem{
			Relations: &topic_channel.Relation{},
		}
		elemInf.Id = InfElemID
		elemInf.Title = "不限"
		elemInf.Mode = topic_channel.Elem_INFINITE
		elems = append(elems, elemInf)
	}

	elems = append(elems, genElem(blockInfo.GetElems(), mode)...)
	return elems
}

func (b *HomePageHandler) GetBaseBlock(ctx context.Context, blockInfo *tabPB.Block, opFunc ...BlockOpFunc) (block *topic_channel.Block, ok bool) {
	block = &topic_channel.Block{}

	opData := &BlockOptionData{}
	for _, f := range opFunc {
		f(opData)
	}
	elems := b.getBlockElem(blockInfo, opData.InMode)
	if len(elems) == 0 { // 没有elem不显示block二级标题
		return block, false
	}
	resMode := b.getBlockMode(opData.InMode)
	block = b.getBaseBlockInfo(blockInfo)
	block.Mode = resMode
	block.Elems = elems

	return block, true
}
