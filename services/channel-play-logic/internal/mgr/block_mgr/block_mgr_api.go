package block_mgr

import (
	"context"
	"golang.52tt.com/pkg/protocol/grpc"
	channel_play "golang.52tt.com/protocol/app/channel-play"
	hobby_channel "golang.52tt.com/protocol/app/hobby-channel"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	tabpb "golang.52tt.com/protocol/services/topic_channel/tab"
)

type ISecondaryFilterMgr interface {
	GetSecondaryFilter(ctx context.Context, in *channel_play.GetSecondaryFilterReq, serviceInfo *grpc.ServiceInfo) (
		out *channel_play.GetSecondaryFilterResp, err error)
	GetSecondaryFilterByCategory(ctx context.Context, categoryId uint32, serviceInfo *grpc.ServiceInfo, channelPkg string) (
		items []*hobby_channel.GameHomePageFilterItem,
		businessFilter []*channel_play.BusinessFilterItem, err error)
	GetPublishOptionGuide(ctx context.Context, tabId, uid uint32, roomName string) (
		options []*channel_play.PublishOptionElem, optionLabels, inputLabels []*channel_play.RecommendationElem, err error)
	GetSecondaryFilterByFastPCCategory(ctx context.Context, categoryId uint32, serviceInfo *grpc.ServiceInfo, channelPkg string) (
		[]*hobby_channel.GameHomePageFilterItem, []*channel_play.BusinessFilterItem, error)
}

type IBusinessBlockFilterMgr interface {
	GetBusinessBlockByEntrance(ctx context.Context, tabInfo *tabpb.Tab, mode, source, clientType uint32) (businessBlocks []*channel_play.BusinessFilterItem, err error)
	GetBusinessFilterByCategoryId(categoryId uint32) (items []*channel_play.BusinessFilterItem, err error)
}

type IBlockHandler interface {
	GetBaseBlock(ctx context.Context, blockInfo *tabpb.Block, opts ...BlockOpFunc) (block *topic_channel.Block, ok bool)
}
