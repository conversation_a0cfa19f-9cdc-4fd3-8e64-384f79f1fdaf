package confz

import (
	"context"
	"encoding/json"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/marketid_helper"
	config "golang.52tt.com/pkg/dyconfig"
	"golang.52tt.com/pkg/log"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	"golang.52tt.com/services/runtime/v2/filters/pkg"
	"os/user"
	"runtime"
	"time"
)

type ThirdPartyGameConfig struct {
	TabId              uint32 `json:"tab_id"`
	LabelUrl           string `json:"label_url"`
	PublicUrl          string `json:"public_url"`
	IosDownloadUrl     string `json:"ios_download_url"`
	IosJumpUrl         string `json:"ios_jump_url"`
	AndroidDownloadUrl string `json:"android_download_url"`
	AndroidJumpUrl     string `json:"android_jump_url"`
	PackageNameList    string `json:"package_name_list"`
}

type ThirdPartyGameConfigMap struct {
	ConfigMap map[uint32]*ThirdPartyGameConfig `json:"config_map"`
}

func (s *ThirdPartyGameConfigMap) UnmarshalBinary(data []byte) error {
	err := json.Unmarshal(data, s)
	if err != nil {
		return err
	}
	return nil
}

func (s *ThirdPartyGameConfigMap) GetMap() map[uint32]*ThirdPartyGameConfig {
	if s == nil {
		return nil
	}
	return s.ConfigMap
}

type ThirdPartyGame struct {
	dc *config.DynamicConfig
}

func (s *ThirdPartyGame) Get() *ThirdPartyGameConfigMap {
	if s == nil {
		return nil
	}
	return s.dc.Get().(*ThirdPartyGameConfigMap)
}

var ThirdPartyGameConf *ThirdPartyGame

func NewThirdPartyGameConfigDcLoader(filename string) (*ThirdPartyGame, error) {
	ctx := context.Background()
	dc, err := config.NewDynamicConfig(context.Background(), filename, &ThirdPartyGameConfigMap{})
	if nil != err {
		log.ErrorWithCtx(ctx, "new channel member dy cfg mng failed, err: %+v 3", err)
		return nil, err
	}
	return &ThirdPartyGame{
		dc: dc,
	}, nil
}

func Setup() error {
	var baseDir string
	if runtime.GOOS == "windows" {
		if u, err := user.Current(); err == nil {
			baseDir = u.HomeDir
		}
	}

	var err error
	ThirdPartyGameConf, err = NewThirdPartyGameConfigDcLoader(baseDir + "/data/oss/conf-center/tt/third_party_game_config.json")
	if err != nil {
		log.Errorf("NewThirdPartyGameConfigDcLoader err:%v", err)
		//return err
	}

	TopicChannelTabConfigLoader, err = NewTopicChannelTabLoader(baseDir + "/data/oss/conf-center/tt/topic-channel-tab.json")
	if err != nil {
		log.Errorf("NewTopicChannelTabLoader err:%v", err)
		//return err
	}

	// 显示初始化，临时解决bylink上报导致panic，init初始化有时会失败
	err = marketid_helper.InitMarketidHelperConfig()
	if err != nil {
		return err
	}

	return nil
}

type TopicChannelTabConfig struct {
	CategoryMapping map[uint32]topic_channel.CategoryType `json:"category_mapping"`
}

var TopicChannelTabConfigLoader *TopicChannelTabLoader

func (loader *TopicChannelTabConfig) UnmarshalBinary(data []byte) error {
	err := json.Unmarshal(data, loader)
	if err != nil {
		log.Errorf("TopicChannelTabConfig UnmarshalBinary data(%s) err: %v", data, err)
		return err
	}

	return nil
}

type TopicChannelTabLoader struct {
	configLoader *pkg.ConfigLoader
}

func NewTopicChannelTabLoader(filename string) (loader *TopicChannelTabLoader, err error) {
	loader = &TopicChannelTabLoader{}

	loader.configLoader, err = pkg.NewConfigLoaderV2(context.Background(), filename, &TopicChannelTabConfig{}, false, 30*time.Second)
	if err != nil {
		log.Errorf("NewHomePageConfigLoader NewConfigLoaderV2 filename(%s) err: %v", filename, err)
		return nil, err
	}

	return
}

func (loader *TopicChannelTabLoader) loadConfig() (cfg *TopicChannelTabConfig) {
	cfg = &TopicChannelTabConfig{}

	if loader.configLoader == nil {
		log.Warnf("TopicChannelTabLoader LoadConfig configLoader nil")
		return
	}

	e := loader.configLoader.Get()
	if e == nil {
		log.Warnf("TopicChannelTabLoader LoadConfig Get nil")
		return
	}

	var ok bool
	cfg, ok = e.(*TopicChannelTabConfig)
	if !ok {
		log.Warnf("TopicChannelTabLoader LoadConfig TopicChannelTabConfig nil")
		return
	}
	return
}

func (loader *TopicChannelTabLoader) GetCategoryMapping() map[uint32]topic_channel.CategoryType {
	return loader.loadConfig().CategoryMapping
}
