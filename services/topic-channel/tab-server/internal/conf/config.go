package conf

import (
	"context"
	"encoding/json"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	game_pal "golang.52tt.com/protocol/services/game-pal"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/runtime/v2/filters/pkg"
)

const (
	museChatTabId = 564
	fastChatTabId = 609
)

type TopicChannelTabConfig struct {
	OtherGameTabId  uint32                                `json:"other_game_tab_id"`
	CategoryMapping map[uint32]topic_channel.CategoryType `json:"category_mapping"` //key为category_id
	// tab中tag_id是否开启使用新字段逻辑
	IsTagIdUseNewField bool `json:"is_tag_id_use_new_field"`

	IsUsePProf   bool   `json:"is_use_p_prof"`
	MemProfCnt   uint32 `json:"mem_prof_cnt"`
	MemProfValue uint64 `json:"mem_prof_value"`
	ForCnt       uint32 `json:"for_cnt"`

	WarnChatId         string `json:"warn_chat_id"`          //告警群id
	WarnDuration       int64  `json:"warn_duration"`         //告警周期，单位s
	IsSchemeTypeUpdate bool   `json:"is_scheme_type_update"` //从统一玩法布局那里拉玩法模板更新到tab表里，一般用于着次或数据不一致时
	// 搭子卡默认发布项配置
	DefaultGamePalBlocks []*game_pal.GamePalBlock `json:"default_game_pal_blocks"`
	ErrLenCnt            int                      `json:"err_len_cnt"`

	DefaultGangupReleaseDuration uint32 `json:"default_gangup_release_duration"`

	FastPcChatTabId                       uint32 `json:"fast_pc_chat_tab_id"`                          // 快速PC聊天tabId
	MuseChatTabId                         uint32 `json:"muse_chat_tab_id"`                             // Mt扩列tabId
	DefaultFastPcHomePageBackgroundImgUrl string `json:"default_fast_pc_home_page_background_img_url"` // 默认快速PC首页背景图
	DefaultFastPcRoomBackgroundImgUrl     string `json:"default_fast_pc_room_background_img_url"`      // 默认快速PC大厅房间背景图

	NoNeedCheckTabName       map[string]bool `json:"no_need_check_tab_name"`        // 是否需要检查tab名字
	NoNeedCheckTabUniqueName map[string]bool `json:"no_need_check_tab_unique_name"` // 是否需要检查tab唯一名字
}

func (loader *TopicChannelTabConfig) UnmarshalBinary(data []byte) error {
	err := json.Unmarshal(data, loader)
	if err != nil {
		log.Errorf("TopicChannelTabConfig UnmarshalBinary data(%s) err:%v", data, err)
		return err
	}

	return nil
}

type TopicChannelTabLoader struct {
	configLoader *pkg.ConfigLoader
}

func NewTopicChannelTabLoader(ctx context.Context, filename string) (loader *TopicChannelTabLoader, err error) {
	loader = &TopicChannelTabLoader{}

	loader.configLoader, err = pkg.NewConfigLoaderV2(context.Background(), filename, &TopicChannelTabConfig{}, false, 30*time.Second)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewHomePageConfigLoader NewConfigLoaderV2 filename(%s) err: %v", filename, err)
		return nil, err
	}

	return
}

func (loader *TopicChannelTabLoader) loadConfig() (cfg *TopicChannelTabConfig) {
	cfg = &TopicChannelTabConfig{}

	if loader.configLoader == nil {
		log.Warnf("TopicChannelTabLoader LoadConfig configLoader nil")
		return
	}

	e := loader.configLoader.Get()
	if e == nil {
		log.Warnf("TopicChannelTabLoader LoadConfig Get nil")
		return
	}

	var ok bool
	cfg, ok = e.(*TopicChannelTabConfig)
	if !ok {
		log.Warnf("TopicChannelTabLoader LoadConfig TopicChannelTabConfig nil")
		return
	}
	return
}

func (loader *TopicChannelTabLoader) PrintInfo(ctx context.Context) {
	cfg := loader.loadConfig()
	log.InfoWithCtx(ctx, "TopicChannelTabLoader cfg.OtherGameTabId:%v", cfg.OtherGameTabId)
	log.InfoWithCtx(ctx, "TopicChannelTabLoader cfg.CategoryMapping:%v", cfg.CategoryMapping)
	log.InfoWithCtx(ctx, "TopicChannelTabLoader cfg.IsTagIdUseNewField:%v", cfg.IsTagIdUseNewField)
	log.InfoWithCtx(ctx, "TopicChannelTabLoader cfg.IsUsePProf:%v", cfg.IsUsePProf)
	log.InfoWithCtx(ctx, "TopicChannelTabLoader cfg.MemProfCnt:%v", cfg.MemProfCnt)
	log.InfoWithCtx(ctx, "TopicChannelTabLoader cfg.MemProfValue:%v", cfg.MemProfValue)
	log.InfoWithCtx(ctx, "TopicChannelTabLoader cfg.ForCnt:%v", cfg.ForCnt)
}

func (loader *TopicChannelTabLoader) GetOtherGameTabId() uint32 {
	return loader.loadConfig().OtherGameTabId
}

func (loader *TopicChannelTabLoader) GetCategoryMapping() map[uint32]topic_channel.CategoryType {
	return loader.loadConfig().CategoryMapping
}

func (loader *TopicChannelTabLoader) GetDefaultGangupReleaseDuration() uint32 {
	return loader.loadConfig().DefaultGangupReleaseDuration
}

func (loader *TopicChannelTabLoader) GetIsTagIdUseNewField() bool {
	return loader.loadConfig().IsTagIdUseNewField
}

func (loader *TopicChannelTabLoader) GetIsUsePProf() bool {
	return loader.loadConfig().IsUsePProf
}

func (loader *TopicChannelTabLoader) GetMemProfCnt() uint32 {
	return loader.loadConfig().MemProfCnt
}

func (loader *TopicChannelTabLoader) GetMemProfValue() uint64 {
	return loader.loadConfig().MemProfValue
}

func (loader *TopicChannelTabLoader) GetForCnt() uint32 {
	t := loader.loadConfig().ForCnt
	if t == 0 {
		return 300
	}
	return t
}

func (loader *TopicChannelTabLoader) GetErrLenCnt() int {
	t := loader.loadConfig().ErrLenCnt
	if t == 0 {
		return 3000
	}
	return t
}

func (loader *TopicChannelTabLoader) GetFallBackChatId() string {
	config := loader.loadConfig()
	if config != nil && len(config.WarnChatId) != 0 {
		return config.WarnChatId
	}
	//默认告警到云测告警群
	return "oc_7f4c14d35388f3e5b04ddb220f607e9f"
}

func (loader *TopicChannelTabLoader) GetFallBackWarnDuration() int64 {
	config := loader.loadConfig()
	if config != nil {
		return config.WarnDuration
	}
	return 60
}

func (loader *TopicChannelTabLoader) GetDefaultGamePalBlocks() []*game_pal.GamePalBlock {
	return loader.loadConfig().DefaultGamePalBlocks
}

func (loader *TopicChannelTabLoader) GetIsSchemeTypeUpdate() bool {
	config := loader.loadConfig()
	if config != nil {
		return config.IsSchemeTypeUpdate
	}
	return false
}

func (loader *TopicChannelTabLoader) GetCategoryIdByType(cType topic_channel.CategoryType) uint32 {
	mapping := loader.loadConfig().CategoryMapping
	for k, v := range mapping {
		if v == cType {
			return k
		}
	}
	return 0
}

func (loader *TopicChannelTabLoader) GetMuseChatTabId() uint32 {
	config := loader.loadConfig()
	if config != nil {
		return config.MuseChatTabId
	}
	return museChatTabId
}

func (loader *TopicChannelTabLoader) GetFastPcChatTabId() uint32 {
	config := loader.loadConfig()
	if config != nil {
		return config.FastPcChatTabId
	}
	return fastChatTabId
}

func (loader *TopicChannelTabLoader) GetDefaultFastPcHomePageBackgroundImgUrl() string {
	config := loader.loadConfig()
	if config != nil {
		return config.DefaultFastPcHomePageBackgroundImgUrl
	}
	return ""
}

func (loader *TopicChannelTabLoader) GetDefaultFastPcRoomBackgroundImgUrl() string {
	config := loader.loadConfig()
	if config != nil {
		return config.DefaultFastPcRoomBackgroundImgUrl
	}
	return ""
}

func (loader *TopicChannelTabLoader) IsNeedCheckTabName(name string) bool {
	config := loader.loadConfig()
	if config != nil {
		return !config.NoNeedCheckTabName[name]
	}
	return true
}

func (loader *TopicChannelTabLoader) IsNeedCheckTabUniqueName(name string) bool {
	config := loader.loadConfig()
	if config != nil {
		return !config.NoNeedCheckTabUniqueName[name]
	}
	return true
}
