package manager

import (
	"context"
	"fmt"
	channel_play_tab_pb "golang.52tt.com/protocol/services/channel-play-tab"
	"strconv"
	"strings"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/monkey-send-chat/monkey_sender"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	gamePb "golang.52tt.com/protocol/services/game-server-v2"
	pb "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/topic-channel/tab-server/internal/conf"
	"golang.52tt.com/services/topic-channel/tab-server/internal/convertor"
	"golang.52tt.com/services/topic-channel/tab-server/internal/dao"
	"golang.52tt.com/services/topic-channel/tab-server/internal/utils"
)

// cache 用于缓存前1000条tab。
type TabCacheModel struct {
	// TabsMap 以map的形式缓存tabs。
	TabsMap map[uint32]*pb.Tab
	// PbTabsSlice 以slice的形式缓存tabs。
	PbTabsSlice []*pb.Tab
	// gameCardTabMap 将gameCardId作为键，以map的形式缓存tabs。
	gameCardTabMap map[uint32][]*pb.Tab
	// blocksMap 将tabId作为键，以map的形式缓存blocks。
	BlocksMap map[uint32][]*pb.Block
	// categoryMap 将categoryId作为键，以map形式存储这个分类下的所有tab
	CategoryMap map[uint32][]*pb.Tab
	// RoomNameMap 将tabId作为键，以map形式存储这个tab下的所有RoomName
	RoomNameMap map[uint32][]*dao.RoomName

	minorityGameChildTabsInfo []*pb.Tab
	// UgameIdTabMap 将tabInfo中的u_game_id作为键，以map的形式缓存游戏信息GameInfo。
	UgameIdGameInfoMap map[uint32]*gamePb.GameInfo

	tabsReleaseItemSlice []*pb.ReleaseConditionItem

	// 玩法马甲包版本过滤规则配置, key:tabId value:ShieldRuleForTab
	shieldRuleMap map[uint32]*pb.ShieldRuleForTab

	// 玩法问题配置 key:tabId(0表示通用配置)
	tabQuestionMap map[uint32]dao.TabQuestions
}

func (m *TabCacheModel) setTabQuestions(configs []*dao.TabQuestions) {
	tabQuestionMap := make(map[uint32]dao.TabQuestions)
	for _, config := range configs {
		tabQuestionMap[config.TabId] = *config

		// 现在通过单独接口获取玩法问题，为了兼容旧逻辑，logic上线后去掉
		tab := m.TabsMap[config.TabId]
		if tab != nil {
			tab.Questions, tab.ShowQuestion = converToTabQuestionsPb(config)
		}
	}

	m.tabQuestionMap = tabQuestionMap
}

var tabCache = &TabCacheModel{}

var categoryCache *CategoryCacheModel

// category cache
type CategoryCacheModel struct {
	categoryInfo []*dao.Category
}

func setCategoryCache(tmpCategoryCache *CategoryCacheModel) {
	categoryCache = tmpCategoryCache
}

func GetCategoryCache(ctx context.Context) []*dao.Category {
	if categoryCache == nil {
		log.ErrorWithCtx(ctx, "GetCategoryCache err:nil")
		return nil
	}
	return categoryCache.categoryInfo
}

// 业务筛选器
type BusinessBlockCacheModel struct {
	//绑定指定tab筛选项
	BindTabMap map[uint32][]*pb.BusinessBlock
	//绑定指定category的筛选项
	BindCategoryMap map[uint32][]*pb.BusinessBlock
}

var businessFilterCache = struct {
	BusinessBlockCacheModel
}{}

func (s *TabManager) RefreshCache(ctx context.Context) error {
	//ctx, cancel := context.WithTimeout(ct, time.Second*4)
	//defer cancel()
	log.InfoWithCtx(ctx, "refreshTabCache start")
	err := s.refreshTabCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "TabManager RefreshCache err:%v", err)
		s.SendCacheWarning("refreshTabCache", fmt.Sprintf("refreshTabCache  fail:%v", err))
		return err
	}
	log.InfoWithCtx(ctx, "refreshTabCache end")
	return nil
}

func (s *TabManager) RefreshCategoryCache(ctx context.Context) error {
	log.InfoWithCtx(ctx, "RefreshCategoryCache start")
	err := s.refreshCategoryCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "TabManager refreshCategoryCache err:%v", err)
		s.SendCacheWarning("refreshCategoryCache", fmt.Sprintf("refreshCategoryCache err:%v", err))
		return err
	}
	log.InfoWithCtx(ctx, "RefreshCategoryCache end")

	/*log.InfoWithCtx(ctx, "refreshTabReleaseItemCache start")
	err = s.refreshTabReleaseItemCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "TabManager refreshTabReleaseItemCache err:%v", err)
		s.SendCacheWarning("refreshTabReleaseItemCache", fmt.Sprintf("refreshTabReleaseItemCache err:%v", err))

		return err
	}*/
	log.InfoWithCtx(ctx, "refreshBusinessFilterCache start")
	err = s.refreshBusinessFilterCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "TabManager refreshBusinessFilterCache err:%v", err)
		s.SendCacheWarning("refreshBusinessFilterCache", fmt.Sprintf("refreshBusinessFilterCache err:%v", err))

		return err
	}
	log.InfoWithCtx(ctx, "refreshBusinessFilterCache end")
	return nil

}

func (s *TabManager) SendCacheWarning(src, msg string) {
	sendErr := monkey_sender.GetNumMsgSenderByChatId(conf.TopicChannelTabConfigLoader.GetFallBackChatId(),
		conf.TopicChannelTabConfigLoader.GetFallBackWarnDuration()).SendMsg("topic-channel-tab缓存 "+src, msg)
	if sendErr != nil {
		log.Errorf("TabManager monkey_sender  SendCacheWarning err:%v", sendErr)
	}
}

func (s *TabManager) refreshTabCache(ctx context.Context) (err error) {
	daoTabInfos, blockInfos, elemInfos, err := s.store.GetAllTabBlockElemInfo(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabCache GetAllTabBlockElemInfo error: %v", err)
		err = protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError, fmt.Sprintf("GetAllTabBlockElemInfo err:%v", err))
		return
	}
	if len(daoTabInfos) == 0 || len(blockInfos) == 0 || len(elemInfos) == 0 {
		log.ErrorWithCtx(ctx, "refreshTabCache GetAllTabBlockElemInfo daoTabInfos len:%d, blockInfos:%d, elemInfos:%d",
			len(daoTabInfos), len(blockInfos), len(elemInfos))
		err = protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError, fmt.Sprintf("GetAllTabBlockElemInfo daoTabInfos len:%d, blockInfos:%d,"+
			" elemInfos:%d", len(daoTabInfos), len(blockInfos), len(elemInfos)))
		return
	}

	roomNameMap, err := s.RoomNameConfigMap(ctx, nil, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabCache RoomNameConfigMap error: %v", err)
		return
	}

	if len(daoTabInfos) > conf.TopicChannelTabConfigLoader.GetErrLenCnt() {
		log.Errorf("len err:%d", len(daoTabInfos))
	}
	// 获取游戏、游戏卡数据
	uGameIds := make([]uint32, 0, len(daoTabInfos))
	checker := utils.NewForCntChecker("refreshTabCache")
	tabIds := make([]uint32, 0, len(daoTabInfos))
	for _, info := range daoTabInfos {
		tabIds = append(tabIds, info.Id)
		checker.Incr()
		if info.UGameID == 0 {
			continue
		}
		uGameIds = append(uGameIds, info.UGameID)
	}
	ugameIdGameInfoMap, err := s.gameServerCli.GetGameByUGameIds(ctx, uGameIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "UserLine Err: refreshTabCache, GetGameByUGameIds fail, err:%s", err.Error())
		return
	}
	shieldRuleMap, err := s.genShieldRuleCacheMap(ctx, tabIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabCache genShieldRuleCacheMap tabIds:%+v err:%v", tabIds, err)
		return
	}
	tabFastCategoryInfoMap, err := s.channelPlayTabCli.GetFastPCCategoryConfigTabMap(ctx, &channel_play_tab_pb.GetFastPCCategoryConfigReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabCache GetFastPCCategoryConfigTabMap error: %v", err)
	}

	extTabInfoMap, err := s.channelPlayTabCli.GetAllTabInfoExt(ctx, &channel_play_tab_pb.GetAllTabInfoExtReq{
		ExtType: channel_play_tab_pb.TabInfoExtEnum_TAB_INFO_EXT_FAST_PC_CONFIG,
	})

	tabsMap := make(map[uint32]*pb.Tab, 0)
	categoryMap := make(map[uint32][]*pb.Tab, 0)
	gameCardTabMap := make(map[uint32][]*pb.Tab, 0)
	blocksMap := make(map[uint32][]*pb.Block, 0)
	minorityGameTabs := make([]*pb.Tab, 0)
	if len(daoTabInfos) > conf.TopicChannelTabConfigLoader.GetErrLenCnt() {
		log.Errorf("len err:%d", len(daoTabInfos))
	}
	pbTabInfos := make([]*pb.Tab, 0, len(daoTabInfos))

	checker1 := utils.NewForCntChecker("refreshTabCache daoTabInfos")
	for _, info := range daoTabInfos {
		checker1.Incr()
		pbTab := s.convertToTabPb(info)
		pbTab.ShieldRule = shieldRuleMap[pbTab.GetId()]
		if conf.TopicChannelTabConfigLoader.GetIsTagIdUseNewField() {
			if ugameIdGameInfoMap != nil {
				if v, ok := ugameIdGameInfoMap[pbTab.GetUGameId()]; ok && v != nil {
					pbTab.GameInfo = &pb.GameInfoItem{
						UGameId:      v.GetUGameId(),
						GameCardId:   v.GetGameCardId(),
						GameCardName: v.GetGameCardName(),
						GameName:     v.GetGameName(),
					}
					if v.GetGameCardId() > 0 {
						gameCardTabMap[v.GetGameCardId()] = append(gameCardTabMap[v.GetGameCardId()], pbTab)
					}
				}
			}
		} else {
			// 未同步数据前还是用旧的方式
			gameCardTabMap[pbTab.GetTagId()] = append(gameCardTabMap[pbTab.GetTagId()], pbTab)
		}
		if tabFastCategoryInfoMap != nil {
			if fastCategories, ok := tabFastCategoryInfoMap[pbTab.GetId()]; ok {
				pbTab.FastPcCategoryInfos = make([]*pb.FastPCCategoryInfo, 0, len(fastCategories))
				for _, fastCategory := range fastCategories {
					pbTab.FastPcCategoryInfos = append(pbTab.FastPcCategoryInfos, &pb.FastPCCategoryInfo{
						Id:   fastCategory.Id,
						Name: fastCategory.Name,
					})
				}
			}
		}
		if extTabInfoMap != nil {
			tempTabId := pbTab.GetId()
			if tempTabId == conf.TopicChannelTabConfigLoader.GetMuseChatTabId() {
				tempTabId = conf.TopicChannelTabConfigLoader.GetFastPcChatTabId()
			}
			pbTab.FastPcBackgroundImgUrl = extTabInfoMap[tempTabId].GetFastPcHomePageBackgroundImgUrl()
			pbTab.FastPcRoomBackgroundImgUrl = extTabInfoMap[tempTabId].GetFastPcRoomBackgroundImgUrl()
		}
		if pbTab.FastPcBackgroundImgUrl == "" {
			pbTab.FastPcBackgroundImgUrl = conf.TopicChannelTabConfigLoader.GetDefaultFastPcHomePageBackgroundImgUrl()
		}
		if pbTab.FastPcRoomBackgroundImgUrl == "" {
			pbTab.FastPcRoomBackgroundImgUrl = conf.TopicChannelTabConfigLoader.GetDefaultFastPcRoomBackgroundImgUrl()
		}

		pbTabInfos = append(pbTabInfos, pbTab)
		tabsMap[pbTab.GetId()] = pbTab
		categoryMap[pbTab.GetCategoryId()] = append(categoryMap[pbTab.GetCategoryId()], pbTab)
		if pbTab.GetIsMinorityGame() {
			minorityGameTabs = append(minorityGameTabs, pbTab)
		}
	}

	blockElemMap := make(map[uint32][]*dao.Elem, 0)
	checker2 := utils.NewForCntChecker("refreshTabCache elemInfos")
	for _, e := range elemInfos {
		checker2.Incr()
		if _, ok := blockElemMap[e.BlockId]; !ok {
			blockElemMap[e.BlockId] = []*dao.Elem{e}
		} else {
			blockElemMap[e.BlockId] = append(blockElemMap[e.BlockId], e)
		}
	}

	checker3 := utils.NewForCntChecker("refreshTabCache blockInfos")
	for _, b := range blockInfos {
		checker3.Incr()
		if _, ok := blocksMap[b.TabId]; !ok {
			blocksMap[b.TabId] = []*pb.Block{convertBlockPb(b, blockElemMap[b.Id])}
		} else {
			blocksMap[b.TabId] = append(blocksMap[b.TabId], convertBlockPb(b, blockElemMap[b.Id]))
		}
	}

	releaseItems, err := s.refreshTabReleaseItemCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "TabManager refreshTabReleaseItemCache err:%v", err)
		s.SendCacheWarning("refreshTabReleaseItemCache", fmt.Sprintf("refreshTabReleaseItemCache err:%v", err))
		return
	}

	tabQuestionConfigs, err := s.mStore.GetAllTabQuestions(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabCache GetAllTabQuestions err: %v", err)
		return
	}

	tcModel := &TabCacheModel{
		TabsMap:                   tabsMap,
		PbTabsSlice:               pbTabInfos,
		gameCardTabMap:            gameCardTabMap,
		BlocksMap:                 blocksMap,
		CategoryMap:               categoryMap,
		RoomNameMap:               roomNameMap,
		minorityGameChildTabsInfo: minorityGameTabs,
		UgameIdGameInfoMap:        ugameIdGameInfoMap,
		tabsReleaseItemSlice:      releaseItems,
		shieldRuleMap:             shieldRuleMap,
	}

	tcModel.setTabQuestions(tabQuestionConfigs)

	log.InfoWithCtx(ctx, "CntInfo: TabCacheModel, TabsMap:%d, PbTabsSlice:%d, gameCardTabMap:%d, BlocksMap:%d, CategoryMap:%d,"+
		" RoomNameMap:%d, minorityGameChildTabsInfo:%d, UgameIdGameInfoMap:%d",
		len(tabsMap), len(pbTabInfos), len(gameCardTabMap), len(blocksMap), len(categoryMap), len(roomNameMap), len(minorityGameTabs), len(ugameIdGameInfoMap))
	setTabCache(tcModel)

	updateCtx, updateCancel := context.WithTimeout(ctx, 1*time.Second)
	s.setChannelSchemeConfCache(updateCtx, tabsMap)
	updateCancel()
	return nil
}

func (s *TabManager) genShieldRuleCacheMap(ctx context.Context, tabIds []uint32) (map[uint32]*pb.ShieldRuleForTab, error) {
	shieldRules, err := s.BatchGetShieldSwitchByTabIds(ctx, tabIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabCache genShieldRuleCacheMap tabId(%v) err: %v", tabIds, err)
		return nil, err
	}
	return convertor.ConvertShieldRuleToPBMap(shieldRules), nil
}

func (s *TabManager) refreshCategoryCache(ctx context.Context) (err error) {
	categoryInfos, err := s.store.GetAllCategory(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshCategoryCache GetAllCategory error:%v", err)
		return
	}
	if len(categoryInfos) == 0 {
		log.ErrorWithCtx(ctx, "refreshCategoryCache GetAllCategory len(categoryInfos)=0")
		err = protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError, "GetAllCategory len(categoryInfos)=0")
		return
	}

	tmpCategoryCache := &CategoryCacheModel{
		categoryInfo: categoryInfos,
	}

	log.InfoWithCtx(ctx, "CntInfo: refreshCategoryCache, categoryInfo:%d", len(categoryInfos))
	setCategoryCache(tmpCategoryCache)
	return nil
}

func (s *TabManager) refreshTabReleaseItemCache(ctx context.Context) (items []*pb.ReleaseConditionItem, err error) {
	releaseItems, _, tabIds, err := s.ListReleaseConditionForTT(ctx, "", 0, 0, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabReleaseItemCache ListReleaseConditionForTT error:%v", err)
		s.SendCacheWarning("ListReleaseConditionForTT", fmt.Sprintf("refreshTabReleaseItemCache ListReleaseConditionForTT releaseItems len:%d, err:%v",
			len(releaseItems), err))
		return releaseItems, err
	}
	if len(releaseItems) == 0 {
		log.ErrorWithCtx(ctx, "refreshTabReleaseItemCache len(releaseItems) = 0")
		err = protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError, "refreshTabReleaseItemCache len(releaseItems) = 0")
		return releaseItems, err
	}
	log.InfoWithCtx(ctx, "CntInfo: refreshTabReleaseItemCache, releaseItems:%d", len(releaseItems))
	//setReleaseItemsCache(releaseItems)

	err = s.SetDisplayBlockInfo(ctx, tabIds, releaseItems)
	if err != nil {
		return releaseItems, err
	}
	return releaseItems, nil
}

func (s *TabManager) refreshBusinessFilterCache(ctx context.Context) (err error) {
	businessBlockInfo, businessElemInfo, err := s.store.GetAllBusinessBlockElem(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshBusinessFilterCache GetAllBusinessBlockElem error:%v", err)
		return
	}
	bindTabMap := make(map[uint32][]*pb.BusinessBlock)
	bindCategoryMap := make(map[uint32][]*pb.BusinessBlock)
	businessBlockElemMap := make(map[uint32][]*dao.BusinessElem, 0)

	checker := utils.NewForCntChecker("refreshBusinessFilterCache")
	for _, v := range businessElemInfo {
		checker.Incr()
		if _, ok := businessBlockElemMap[v.BlockId]; !ok {
			businessBlockElemMap[v.BlockId] = []*dao.BusinessElem{v}
		} else {
			businessBlockElemMap[v.BlockId] = append(businessBlockElemMap[v.BlockId], v)
		}
	}

	for _, v := range businessBlockInfo {
		checker.Incr()
		handleBusinessBlockInfo(ctx, v, businessBlockElemMap[v.Id], bindTabMap, bindCategoryMap)
	}

	setBusinessFilterCache(bindTabMap, bindCategoryMap)
	return nil
}

func handleBusinessBlockInfo(ctx context.Context, block *dao.BusinessBlock, elem []*dao.BusinessElem, bindTabMap map[uint32][]*pb.BusinessBlock,
	bindCategoryMap map[uint32][]*pb.BusinessBlock) {
	tabIds := make([]uint32, 0)
	categoryIds := make([]uint32, 0)
	if block.TabIdArray != "" {
		tabsString := strings.Split(block.TabIdArray, ",")
		checker := utils.NewForCntChecker("handleBusinessBlockInfo")
		for _, s := range tabsString {
			checker.Incr()
			tId, err := strconv.Atoi(s)
			if err != nil {
				log.WarnWithCtx(ctx, "refreshBusinessFilterCache tabIdArray err %v tid %v", err, s)
				continue
			}
			tabIds = append(tabIds, uint32(tId))
		}
		for _, tabId := range tabIds {
			checker.Incr()
			if _, ok := bindTabMap[tabId]; !ok {
				bindTabMap[tabId] = []*pb.BusinessBlock{convertBusinessBlockPb(block, elem)}
			} else {
				bindTabMap[tabId] = append(bindTabMap[tabId], convertBusinessBlockPb(block, elem))
			}
		}
	}
	if block.CategoryIdArray != "" {
		cidsString := strings.Split(block.CategoryIdArray, ",")
		checker := utils.NewForCntChecker("handleBusinessBlockInfo cidsString")
		for _, s := range cidsString {
			checker.Incr()
			cId, err := strconv.Atoi(s)
			if err != nil {
				log.WarnWithCtx(ctx, "refreshBusinessFilterCache tabIdArray err %v cid %v", err, s)
				continue
			}
			categoryIds = append(categoryIds, uint32(cId))
		}
		for _, categoryId := range categoryIds {
			checker.Incr()
			if _, ok := bindCategoryMap[categoryId]; !ok {
				bindCategoryMap[categoryId] = []*pb.BusinessBlock{convertBusinessBlockPb(block, elem)}
			} else {
				bindCategoryMap[categoryId] = append(bindCategoryMap[categoryId], convertBusinessBlockPb(block, elem))
			}
		}
	}
}

func setBusinessFilterCache(BindTabMap map[uint32][]*pb.BusinessBlock, BindCategoryMap map[uint32][]*pb.BusinessBlock) {
	businessFilterCache.BindTabMap = BindTabMap
	businessFilterCache.BindCategoryMap = BindCategoryMap
}

func setTabCache(tabCM *TabCacheModel) {
	tabCache = tabCM
}

func GetTabsMapCache() (tabsMap map[uint32]*pb.Tab) {
	return tabCache.TabsMap
}

func GetTabsSliceCache() (tabsSlice []*pb.Tab) {
	tabsSlice = tabCache.PbTabsSlice
	return tabsSlice
}

func GetGameCardTabMapCache() (gameCardTabMap map[uint32][]*pb.Tab) {
	gameCardTabMap = tabCache.gameCardTabMap
	return
}

func GetBlocksMapCache() (blocksMap map[uint32][]*pb.Block) {
	blocksMap = tabCache.BlocksMap
	return blocksMap
}

func GetReleaseItemsCache() []*pb.ReleaseConditionItem {
	return tabCache.tabsReleaseItemSlice
}

func GetCategoryMapCache() (categoryMap map[uint32][]*pb.Tab) {
	categoryMap = tabCache.CategoryMap
	return categoryMap
}

func GetTabsByCategoryId(categoryId uint32) (tabs []*pb.Tab) {
	src := tabCache.CategoryMap[categoryId]
	if src == nil {
		return nil
	}
	err := DeepCopy(&tabs, src)
	if err != nil {
		log.Errorf("deepcopy err:%v", err)
		return nil
	}
	return
}

func GetMinorityGameChildTabsInfoCache() (minorityGameChildTabsInfo []*pb.Tab) {
	minorityGameChildTabsInfo = tabCache.minorityGameChildTabsInfo
	return minorityGameChildTabsInfo
}

func GetBusinessBlockCache() (blocks BusinessBlockCacheModel) {
	blocks.BindCategoryMap = businessFilterCache.BindCategoryMap
	blocks.BindTabMap = businessFilterCache.BindTabMap
	return
}

func GetRoomNameMapCache() (tabsMap map[uint32][]*dao.RoomName) {
	tabsMap = tabCache.RoomNameMap
	return tabsMap
}

func GetUgameIdGameInfoMapCache() map[uint32]*gamePb.GameInfo {
	return tabCache.UgameIdGameInfoMap
}

func GetShieldRuleMapCache() map[uint32]*pb.ShieldRuleForTab {
	return tabCache.shieldRuleMap
}

// GetTabQuestionConfig 获取玩法问题配置
func GetTabQuestionConfig(tabId uint32) *dao.TabQuestions {
	config := tabCache.tabQuestionMap[tabId]
	return &config
}

// GetCommonQuestionConfig 获取通用问题配置
func GetCommonQuestionConfig() *dao.TabQuestions {
	config := tabCache.tabQuestionMap[0]
	return &config
}
