package manager

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/topic-channel/tab-server/internal/dao"
	"golang.52tt.com/services/topic-channel/tab-server/internal/utils"
	"math"
)

const (
	NoLimitElem                      = math.MaxUint32
	UpdateBlockErrorInfo             = "%s字段的选项[不限]绑定了二级字段，请删除[不限]的二级字段，再调整为【单选】"
	DelBlockWithRelationErrInfo      = "%s字段在客户端外显管理处是一级字段，请先删除该一级字段"
	DelBlockRelatedByOtherErrInfo    = "%s字段在客户端外显管理处是%s字段[%s]选项的关联，请先删除该关联关系"
	DelElemHasRelationErrInfo        = "%s字段的选项[%s]绑定了二级字段，请删除该绑定关系，再删除该选项"
	EmptyBlockInfoErrInfo            = "全部字段内容为空或未保存，请先在全部字段添加字段"
	BlockModeErrInfo                 = "%s字段为单选，请先修改该字段的模式，再添加该字段[不限]选项的绑定关系"
	BlockInfoNoExist                 = "id为%d 的字段不存在，无法绑定。请刷新页面"
	RepeatedBlockTitle               = "%s字段名称重复"
	DisplayBlockInfoErrInfo          = "%s字段不能同时作为一级和二级字段"
	RepeatedFirstDisplayBlockErrInfo = "%s字段不能重复绑定为一级字段"
	RepeatedBlockShowAfterTabName    = "只能有一个发布字段置顶在玩法名称旁"
)

func (s *TabManager) SetReleaseCondition(ctx context.Context, in *pb.SetReleaseConditionReq) (out *pb.SetReleaseConditionResp, err error) {
	out = &pb.SetReleaseConditionResp{}
	updateBlocks, updateElems, delBlockIds, delElemIds,
		newBlocks, newElems, oldBlockInfoMap, oldElemInfoMap, err := s.getUpdateBlockInfos(ctx, in.GetTabId(), in.GetBlocks())
	if err != nil {
		return
	}
	relationsMap, err := s.mStore.GetBlockElemRelationsMapByTabIds(ctx, []uint32{in.GetTabId()})
	if err != nil {
		return
	}
	err = s.checkRepeatedBlockName(in.GetBlocks())
	if err != nil {
		return
	}
	err = s.checkSetReleaseConditionConflict(oldBlockInfoMap, oldElemInfoMap, updateBlocks, delBlockIds, delElemIds, relationsMap)
	if err != nil {
		return
	}
	err = s.checkBlockShowAfterTabNameConflict(in.GetBlocks())
	if err != nil {
		return
	}
	err = s.store.SetReleaseCondition(ctx, updateBlocks, updateElems, in.GetTabId(), in.ReleaseDuration, delBlockIds,
		delElemIds, newBlocks, newElems, in.GetHideFilter())

	return
}

func (s *TabManager) checkBlockShowAfterTabNameConflict(blocks []*pb.Block) error {
	hasBlockShowAfterTabName := false
	for _, block := range blocks {
		if block.GetShowPosition() == pb.Block_TopAfterTabName {
			if hasBlockShowAfterTabName {
				return protocol.NewExactServerError(nil, status.ErrTopicChannelPublishConfigConflict, RepeatedBlockShowAfterTabName)
			}
			hasBlockShowAfterTabName = true
		}
	}
	return nil
}

// 设置发布项时的校验检查
func (s *TabManager) checkSetReleaseConditionConflict(oldBlockInfoMap map[uint32]*dao.Block, oldElemInfoMap map[uint32]*dao.Elem,
	updateBlocks []*dao.Block, delBlockIds, delElemIds []uint32, relationsMap map[uint32]*dao.DisplayBlockInfo) (err error) {

	//更新的block，如果由不限或则最多选N个设置成单选，判断该block的关联关系中，不限是否有绑定二级字段。有则报错不让修改
	err = s.checkUpdateBlocks(updateBlocks, oldBlockInfoMap, relationsMap)
	if err != nil {
		return
	}
	//删除的block中，判断该block在关联关系表中是否作为一级字段或被关联在二级字段中
	err = s.checkDelBlocks(delBlockIds, oldBlockInfoMap, oldElemInfoMap, relationsMap)
	if err != nil {
		return
	}
	//删除的elem中，判断是否有关联二级字段
	err = s.checkDelElem(delElemIds, oldBlockInfoMap, oldElemInfoMap, relationsMap)
	return
}

// 检验block信息是否能够被更新
func (s *TabManager) checkUpdateBlocks(updateBlocks []*dao.Block, oldBlockInfoMap map[uint32]*dao.Block,
	relationsMap map[uint32]*dao.DisplayBlockInfo) error {
	checker := utils.NewForCntChecker("checkUpdateBlocks")
	for _, updateBlock := range updateBlocks {
		checker.Incr()
		oldBlock, ok := oldBlockInfoMap[updateBlock.Id]
		if !ok {
			continue
		}
		if !((oldBlock.MostSelectNum > 0 || oldBlock.Mode == uint32(pb.Block_MULTI)) &&
			(updateBlock.MostSelectNum == 0 && updateBlock.Mode == uint32(pb.Block_SINGLE))) {
			//block不是 由最多选N项或则是多选,改成单选， 不需要检查改block的不限是否有绑定关系
			continue
		}
		relation, ok := relationsMap[updateBlock.Id]
		if !ok {
			continue
		}
		checker1 := utils.NewForCntChecker("checkUpdateBlocks ElemBindBlockInfos")
		for _, bindRelation := range relation.ElemBindBlockInfos {
			checker1.Incr()
			if bindRelation.ElemId == NoLimitElem && len(bindRelation.BindBlockGroups) > 0 {
				return protocol.NewExactServerError(nil, status.ErrTopicChannelPublishConfigConflict, fmt.Sprintf(UpdateBlockErrorInfo, updateBlock.Title))
			}
		}
	}
	return nil
}

// 校验block信息是否能够被删除
func (s *TabManager) checkDelBlocks(delBlockIds []uint32, oldBlockInfoMap map[uint32]*dao.Block,
	oldElemInfoMap map[uint32]*dao.Elem, relationsMap map[uint32]*dao.DisplayBlockInfo) error {
	if len(delBlockIds) == 0 {
		return nil
	}
	checker := utils.NewForCntChecker("checkDelBlocks")
	for _, bid := range delBlockIds {
		checker.Incr()
		oldBlockInfo, ok := oldBlockInfoMap[bid]
		if !ok {
			continue
		}
		if _, ok = relationsMap[bid]; ok {
			//要删除的block，是客户端外显的一级字段
			return protocol.NewExactServerError(nil, status.ErrTopicChannelPublishConfigConflict, fmt.Sprintf(DelBlockWithRelationErrInfo, oldBlockInfo.Title))
		}
		for _, r := range relationsMap {
			checker.Incr()
			//要删除的block,被关联在二级字段
			bindBlockInfo, ok := oldBlockInfoMap[r.BlockId]
			if !ok {
				continue
			}
			err := s.isBlockRelated(bindBlockInfo, oldBlockInfo, r.ElemBindBlockInfos, oldElemInfoMap)
			if err != nil {
				return err
			}
		}
	}
	return nil

}

// 判断指定block是否被关联了作为二级字段
func (s *TabManager) isBlockRelated(bindBlockInfo *dao.Block, oldBlockInfo *dao.Block, bindInfo []*dao.ElemBindBlockInfo,
	oldElemInfoMap map[uint32]*dao.Elem) error {
	if len(bindInfo) == 0 {
		return nil
	}

	checker := utils.NewForCntChecker("isBlockRelated")
	for _, info := range bindInfo {
		checker.Incr()
		elemInfo, ok := oldElemInfoMap[info.ElemId]
		if !ok {
			continue
		}
		for _, group := range info.BindBlockGroups {
			checker.Incr()
			if oldBlockInfo.Id == group.RelatedBlockId {
				return protocol.NewExactServerError(nil, status.ErrTopicChannelPublishConfigConflict, fmt.Sprintf(DelBlockRelatedByOtherErrInfo, oldBlockInfo.Title,
					bindBlockInfo.Title, elemInfo.Title))
			}
		}
	}
	return nil
}

// 校验elem是否能够被删除
func (s *TabManager) checkDelElem(delElemIds []uint32, oldBlockInfoMap map[uint32]*dao.Block,
	oldElemInfoMap map[uint32]*dao.Elem, relationsMap map[uint32]*dao.DisplayBlockInfo) error {
	if len(delElemIds) == 0 {
		return nil
	}
	checker := utils.NewForCntChecker("checkDelElem")
	for _, elemId := range delElemIds {
		checker.Incr()
		elemInfo, ok := oldElemInfoMap[elemId]
		if !ok {
			continue
		}
		blockRelation, ok := relationsMap[elemInfo.BlockId]
		if !ok {
			continue
		}
		oldBlockInfo, ok := oldBlockInfoMap[elemInfo.BlockId]
		if !ok {
			continue
		}
		for _, bindInfo := range blockRelation.ElemBindBlockInfos {
			checker.Incr()
			if elemId == bindInfo.ElemId && len(bindInfo.BindBlockGroups) > 0 {
				return protocol.NewExactServerError(nil, status.ErrTopicChannelPublishConfigConflict, fmt.Sprintf(DelElemHasRelationErrInfo, oldBlockInfo.Title,
					elemInfo.Title))
			}
		}
	}
	return nil
}

func (s *TabManager) getUpdateBlockInfos(ctx context.Context, tabId uint32, blocks []*pb.Block) (updateBlocks []*dao.Block,
	updateElems []*dao.Elem, delBlockIds, delElemIds []uint32, newBlocks map[string]*dao.Block, newElems map[string][]*dao.Elem,
	oldBlockInfoMap map[uint32]*dao.Block, oldElemInfoMap map[uint32]*dao.Elem, err error) {
	//查找之前的记录
	oldBlockInfos, err := s.store.GetBlocksByTabId(ctx, tabId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetReleaseCondition GetBlocksByTabId tabId(%d) error(%v)", tabId, err)
		return
	}

	oldBlockInfoMap = make(map[uint32]*dao.Block, len(oldBlockInfos))
	oldBlockIds := make([]uint32, len(oldBlockInfos))
	oldBlockIdMap := make(map[uint32]bool, len(oldBlockInfos))
	for i, blockInfo := range oldBlockInfos {
		oldBlockIdMap[blockInfo.Id] = true
		oldBlockIds[i] = blockInfo.Id
		oldBlockInfoMap[blockInfo.Id] = blockInfo
	}

	oldElemInfos, err := s.store.BatchGetElemsByBlockIds(ctx, oldBlockIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetReleaseCondition GetBlocksByTabId tabId(%d) error(%v)", tabId, err)
		return
	}

	oldElemIdMap := make(map[uint32]bool, len(oldElemInfos))
	oldElemInfoMap = make(map[uint32]*dao.Elem, len(oldElemInfos))
	for _, elemInfo := range oldElemInfos {
		oldElemIdMap[elemInfo.Id] = true
		oldElemInfoMap[elemInfo.Id] = elemInfo
	}

	newBlocks = make(map[string]*dao.Block, 0)
	newElems = make(map[string][]*dao.Elem, 0)
	updateBlocks = make([]*dao.Block, 0)
	updateElems = make([]*dao.Elem, 0)

	for i, b := range blocks {
		if b.Id != 0 { //修改
			updateBlocks = append(updateBlocks, convertBlock(b, tabId, uint32(i+1)))
			oldBlockIdMap[b.Id] = false
			for index, e := range b.Elems {
				if e.Id != 0 {
					oldElemIdMap[e.Id] = false
				}
				updateElems = append(updateElems, convertElem(e, b.Id, uint32(index+1)))
			}
		} else { //新增
			newBlocks[b.Title] = convertBlock(b, tabId, uint32(i+1))
			for index, e := range b.Elems {
				newElems[b.Title] = append(newElems[b.Title], convertElem(e, b.Id, uint32(index+1)))
			}
		}
	}
	delBlockIds = make([]uint32, 0)
	delElemIds = make([]uint32, 0)
	for id, v := range oldBlockIdMap {
		if v {
			delBlockIds = append(delBlockIds, id)
		}
	}

	for id, v := range oldElemIdMap {
		if v {
			delElemIds = append(delElemIds, id)
		}
	}
	return
}

func (s *TabManager) GetReleaseConditionForTT(ctx context.Context, in *pb.GetReleaseConditionForTTReq) (out *pb.GetReleaseConditionForTTResp, err error) {
	out = &pb.GetReleaseConditionForTTResp{}

	tabInfo, err := s.store.GetTabById(ctx, in.GetTabId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReleaseConditionForTT GetTabById tabId(%d) error(%v)", in.GetTabId(), err)
		return out, err
	}

	blockPbs, err := s.GetBlocksByTabId(ctx, in.GetTabId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReleaseConditionForTT GetBlocksByTabId tabId(%d) error(%v)", in.GetTabId(), err)
		return out, err
	}

	out.Item = &pb.ReleaseConditionItem{
		TabId:           tabInfo.Id,
		TabName:         tabInfo.Name,
		Blocks:          blockPbs,
		ReleaseDuration: tabInfo.ReleaseDuration,
		TabType:         pb.TabType(tabInfo.TabType),
		CardsImageUrl:   tabInfo.CardImageUrl,
		HideFilter:      tabInfo.HideFilter,
	}

	return
}

func (s *TabManager) ListReleaseConditionForTT(ctx context.Context, tabName string, limit, page int, getCount bool) (
	items []*pb.ReleaseConditionItem, count uint32, tabIds []uint32, err error) {
	log.InfoWithCtx(ctx, "ListReleaseConditionForTT, tabName:%s, page:%d, limit:%d, getCount:%v", tabName, page, limit, getCount)
	if page == 0 && limit != 0 {
		log.ErrorWithCtx(ctx, "ListReleaseConditionForTT page zero, tabName:%s, page:%d, limit:%d", tabName, page, limit)
		page = 1
	}
	if limit > 1000 {
		log.WarnWithCtx(ctx, "ListReleaseConditionForTT limit too big, tabName:%s, page:%d, limit:%d", tabName, page, limit)
		limit = 1000
	}
	skip := limit * (page - 1)
	if skip > 300 {
		log.ErrorWithCtx(ctx, "ListReleaseConditionForTT, tabName:%s, skip:%d, page:%d, limit:%d", tabName, skip, page, limit)
		skip = 0
	}

	tabInfos, blockInfos, elemInfos, countd, err := s.store.ListReleaseCondition(ctx, tabName, limit, skip, getCount)
	if err != nil {
		log.ErrorWithCtx(ctx, "ListReleaseConditionForTT ListReleaseCondition error: %v", err)
		return
	}

	blockElemMap := make(map[uint32][]*dao.Elem, 0)
	checker := utils.NewForCntChecker("ListReleaseConditionForTT")
	for _, e := range elemInfos {
		checker.Incr()
		if _, ok := blockElemMap[e.BlockId]; !ok {
			blockElemMap[e.BlockId] = []*dao.Elem{e}
		} else {
			blockElemMap[e.BlockId] = append(blockElemMap[e.BlockId], e)
		}
	}

	tabBlocksMap := make(map[uint32][]*pb.Block)
	for _, b := range blockInfos {
		if _, ok := tabBlocksMap[b.TabId]; !ok {
			tabBlocksMap[b.TabId] = []*pb.Block{convertBlockPb(b, blockElemMap[b.Id])}
		} else {
			tabBlocksMap[b.TabId] = append(tabBlocksMap[b.TabId], convertBlockPb(b, blockElemMap[b.Id]))
		}
	}

	tabIds = make([]uint32, 0, len(tabInfos))
	items = make([]*pb.ReleaseConditionItem, len(tabInfos))
	for i, info := range tabInfos {
		tabIds = append(tabIds, info.Id)
		items[i] = &pb.ReleaseConditionItem{
			TabId:           info.Id,
			TabName:         info.Name,
			Blocks:          tabBlocksMap[info.Id],
			ReleaseDuration: info.ReleaseDuration,
			TabType:         pb.TabType(info.TabType),
			CardsImageUrl:   info.CardImageUrl,
			HideFilter:      info.HideFilter,
			HomePageType:    pb.HomePageType(info.HomePageType),
		}
	}
	count = uint32(countd)
	return
}

func (s *TabManager) ListReleaseCondition(ctx context.Context) (items []*pb.ReleaseConditionItem) {
	return GetReleaseItemsCache()
}

// 单独写发布字段block及其elems
func (s *TabManager) SetBlockElemInfos(ctx context.Context, tabId uint32, blocks []*pb.Block) error {
	updateBlocks, updateElems, delBlockIds, delElemIds,
		newBlocks, newElems, oldBlockInfoMap, oldElemInfoMap, err := s.getUpdateBlockInfos(ctx, tabId, blocks)
	if err != nil {
		return err
	}
	relationsMap, err := s.mStore.GetBlockElemRelationsMapByTabIds(ctx, []uint32{tabId})
	if err != nil {
		return err
	}
	// 检查block名称是否重复
	err = s.checkRepeatedBlockName(blocks)
	if err != nil {
		return err
	}
	err = s.checkBlockShowAfterTabNameConflict(blocks)
	if err != nil {
		return err
	}
	//校验
	err = s.checkSetReleaseConditionConflict(oldBlockInfoMap, oldElemInfoMap, updateBlocks, delBlockIds, delElemIds, relationsMap)
	if err != nil {
		return err
	}
	err = s.store.SetTabBlockElemInfo(ctx, updateBlocks, updateElems, delBlockIds, delElemIds, newBlocks, newElems)

	return err
}

// 检查重复的block名称
func (s *TabManager) checkRepeatedBlockName(inBlocks []*pb.Block) error {
	repeatedMap := make(map[string]bool)
	checker := utils.NewForCntChecker("checkRepeatedBlockName")
	for _, b := range inBlocks {
		checker.Incr()
		if repeatedMap[b.GetTitle()] {
			return protocol.NewExactServerError(nil, status.ErrTopicChannelPublishConfigConflict, fmt.Sprintf(RepeatedBlockTitle, b.GetTitle()))
		}
	}
	return nil
}

// 获取客户端外显字段关联关系
func (s *TabManager) BatchGetBlockElemRelations(ctx context.Context, tabIds []uint32) (
	relations map[uint32][]*pb.DisplayBlockInfo, err error) {
	relations = make(map[uint32][]*pb.DisplayBlockInfo)
	infos, err := s.mStore.GetBlockElemRelationsByTabIds(ctx, tabIds)
	if err != nil {
		return nil, err
	}
	for _, v := range infos {
		if len(v.BlockInfos) == 0 {
			continue
		}
		relations[v.TabId] = v.ConvertToPb()
	}
	return
}

func (s *TabManager) SetDisplayBlockInfo(ctx context.Context, tabIds []uint32, items []*pb.ReleaseConditionItem) error {
	relationsMap, err := s.BatchGetBlockElemRelations(ctx, tabIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "setDisplayBlockInfo BatchGetBlockElemRelations tabIds:%v, error:%v", tabIds, err)
		return err
	}
	for i, v := range items {
		if relation, ok := relationsMap[v.GetTabId()]; ok {
			items[i].DisplayBlockInfos = relation
		}
	}
	return nil
}

func (s *TabManager) GetDisplayBlockInfo(ctx context.Context, tabIds []uint32) ([]*pb.CacheTabData, error) {
	cacheTabDatas := make([]*pb.CacheTabData, 0, len(tabIds))
	relationsMap, err := s.BatchGetBlockElemRelations(ctx, tabIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "setDisplayBlockInfo BatchGetBlockElemRelations tabIds:%v, error:%v", tabIds, err)
		return nil, err
	}
	for _, tabId := range tabIds {
		tabData := &pb.CacheTabData{
			TabId: tabId,
		}
		if relations, ok := relationsMap[tabId]; ok {
			for _, info := range relations {
				var elemIds []uint32
				for _, elem := range info.GetElemBindBlockInfos() {
					elemIds = append(elemIds, elem.GetElemId())
				}
				blockData := &pb.CacheBlockData{
					BlockId: info.GetBlockId(),
					ElemIds: elemIds,
				}
				tabData.BlockDatas = append(tabData.BlockDatas, blockData)
			}
		}
		if len(tabData.GetBlockDatas()) != 0 {
			cacheTabDatas = append(cacheTabDatas, tabData)
		}
	}

	return cacheTabDatas, nil
}

// 新增、修改客户端外显字段关联关系
func (s *TabManager) SetBlockElemRelations(ctx context.Context, tabId uint32, displayBlockInfos []*pb.DisplayBlockInfo) error {
	var err error
	//校验是否可以修改
	info, err := s.getCheckRelationsNeedInfo(ctx, tabId)
	if err != nil {
		return err
	}
	err = s.checkAddBlockElemRelations(displayBlockInfos, info)
	if err != nil {
		return err
	}
	//转换成dao结构并写入数据库
	if len(displayBlockInfos) > 0 {
		//新增或更新v
		var relationEntity *dao.BlockElemRelations
		relationEntity, err = convertToBlockElemRelationsEntity(tabId, displayBlockInfos)
		if err != nil {
			return err
		}
		err = s.mStore.UpsertBlockElemRelations(ctx, relationEntity)
	} else {
		//删除
		err = s.mStore.DeleteBlockElemRelations(ctx, tabId)
	}
	return err
}

func (s *TabManager) getCheckRelationsNeedInfo(ctx context.Context, tabId uint32) (oldBlockInfoMap map[uint32]*dao.Block, err error) {
	//查找block信息
	oldBlockInfos, err := s.store.GetBlocksByTabId(ctx, tabId)
	if err != nil {
		log.ErrorWithCtx(ctx, "getCheckRelationsNeedInfo tabId(%d) error(%v)", tabId, err)
		return
	}
	oldBlockInfoMap = make(map[uint32]*dao.Block, len(oldBlockInfos))
	checker := utils.NewForCntChecker("getCheckRelationsNeedInfo")
	for _, blockInfo := range oldBlockInfos {
		checker.Incr()
		oldBlockInfoMap[blockInfo.Id] = blockInfo
	}
	return
}

// 校验是否可以新增、修改客户端外显字段关联关系
func (s *TabManager) checkAddBlockElemRelations(displayBlockInfos []*pb.DisplayBlockInfo, oldBlockInfoMap map[uint32]*dao.Block) error {
	if len(displayBlockInfos) == 0 {
		return nil
	}
	if len(oldBlockInfoMap) == 0 {
		return protocol.NewExactServerError(nil, status.ErrTopicChannelPublishConfigConflict, EmptyBlockInfoErrInfo)
	}
	fisrtBlockMap := make(map[uint32]bool)
	secondaryBlockMap := make(map[uint32]bool)
	checker := utils.NewForCntChecker("checkAddBlockElemRelations")
	for _, displayInfo := range displayBlockInfos {
		checker.Incr()
		blockInfo, ok := oldBlockInfoMap[displayInfo.GetBlockId()]
		if !ok {
			return protocol.NewExactServerError(nil, status.ErrTopicChannelPublishConfigConflict, fmt.Sprintf(BlockInfoNoExist, displayInfo.GetBlockId()))
		}
		//一级字段不能重复绑定，已绑定为二级字段的不能再绑定为一级字段。已绑定为一级字段的也不能再绑定为二级字段
		if fisrtBlockMap[displayInfo.GetBlockId()] {
			return protocol.NewExactServerError(nil, status.ErrTopicChannelPublishConfigConflict, fmt.Sprintf(RepeatedFirstDisplayBlockErrInfo, blockInfo.Title))
		}
		if secondaryBlockMap[displayInfo.GetBlockId()] {
			return protocol.NewExactServerError(nil, status.ErrTopicChannelPublishConfigConflict, fmt.Sprintf(DisplayBlockInfoErrInfo, blockInfo.Title))
		}
		fisrtBlockMap[displayInfo.GetBlockId()] = true
		checker1 := utils.NewForCntChecker("checkAddBlockElemRelations GetElemBindBlockInfos")
		for _, elemBindInfo := range displayInfo.GetElemBindBlockInfos() {
			checker1.Incr()
			//给不限绑定了block但是，所属的block是单选，不应该出现不限
			if elemBindInfo.GetElemId() == NoLimitElem && (blockInfo.MostSelectNum == 0 && blockInfo.Mode == uint32(pb.Block_SINGLE)) {
				return protocol.NewExactServerError(nil, status.ErrTopicChannelPublishConfigConflict, fmt.Sprintf(BlockModeErrInfo, blockInfo.Title))
			}
			var isConflict bool
			isConflict, secondaryBlockMap = s.isSecondaryBlockConflict(elemBindInfo.GetBindBlockGroups(), fisrtBlockMap, secondaryBlockMap)
			if isConflict {
				//已绑定为一级字段的也不能再绑定为二级字段
				return protocol.NewExactServerError(nil, status.ErrTopicChannelPublishConfigConflict, fmt.Sprintf(DisplayBlockInfoErrInfo, blockInfo.Title))
			}
		}
	}
	return nil
}

func (s *TabManager) isSecondaryBlockConflict(group []*pb.ElemBindBlockInfo_BlockElemGroup, firstBlockMap, secondaryBlockMap map[uint32]bool) (bool, map[uint32]bool) {
	checker := utils.NewForCntChecker("isSecondaryBlockConflict")
	for _, g := range group {
		checker.Incr()
		if firstBlockMap[g.GetRelatedBlockId()] {
			return true, secondaryBlockMap
		}
		secondaryBlockMap[g.GetRelatedBlockId()] = true
	}
	return false, secondaryBlockMap
}
