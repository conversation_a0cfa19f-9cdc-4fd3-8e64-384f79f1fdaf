package manager

import (
	"bytes"
	"context"
	"encoding/gob"
	"encoding/json"
	"fmt"
	"os"
	"runtime"
	"runtime/pprof"
	"sort"
	"strconv"
	"strings"
	"time"

	"golang.52tt.com/pkg/cost_time_reporter"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/topic-channel/tab-server/internal/conf"
	"golang.52tt.com/services/topic-channel/tab-server/internal/dao"
	"golang.52tt.com/services/topic-channel/tab-server/internal/utils"
)

func convertToTabEntity(tabInfo *pb.Tab) *dao.Tab {
	tabDao := &dao.Tab{
		Id: tabInfo.Id,
		//Weight:                    tabInfo.Weight,
		Name:    tabInfo.Name,
		TabType: uint32(tabInfo.TabType),
		//TagId:                     tabInfo.TagId,
		ImageUri:                  tabInfo.ImageUri,
		RoomName:                  strings.Join(tabInfo.RoomName, "^"),
		Version:                   tabInfo.Version,
		NewcomerImageUri:          tabInfo.NewcomerImageUri,
		NewcomerColor:             tabInfo.NewcomerColor,
		NewcomerWelcome:           strings.Join(tabInfo.NewcomerWelcome, "^"),
		ShowImageUri:              tabInfo.ShowImageUri,
		MicMod:                    tabInfo.MicMod,
		FollowLabelImg:            tabInfo.FollowLabelImg,
		NewcomerFontColor:         tabInfo.NewcomerFontColor,
		NewcomerFontBackgroundUri: tabInfo.NewcomerFontBackgroundUri,
		FollowLabelText:           tabInfo.FollowLabelText,
		CategoryId:                tabInfo.CategoryId,
		MiniGameNum:               tabInfo.MiniGameNum,
		ClientVer:                 tabInfo.ClientVer,
		ClientVerUpperLimit:       tabInfo.ClientVerUpperLimit,
		UidTailNum:                tabInfo.UidTailNum,
		UidWhiteList:              tabInfo.UidWhiteList,
		ChannelPackageId:          strings.Join(tabInfo.ChannelPackageId, "^"),
		FindPlayingText:           tabInfo.FindPlayingText,
		FindPlayingImg:            tabInfo.FindPlayingImg,
		CardImageUrl:              tabInfo.CardsImageUrl,
		MaskLayer:                 tabInfo.MaskLayer,
		//RoomLabel:                 tabInfo.RoomLabel,
		//TabLabel:                  tabInfo.TabLabel,
		MatchType:              tabInfo.MatchType,
		TemMatchPolicy:         tabInfo.TemMatchPolicy,
		LinkId:                 tabInfo.LinkId,
		PlatformType:           uint32(tabInfo.PlatformType),
		CategorySort:           tabInfo.CategorySort,
		DisplayElem:            tabInfo.DisplayElem,
		RadarDistributionImage: tabInfo.RadarDistributionImage,
		RoomDistributionImage:  tabInfo.RoomDistributionImage,
		BottomTextColor:        tabInfo.BottomTextColor,
		WelcomeTextColor:       tabInfo.WelcomeTextColor,
		SmallCardUrl:           tabInfo.SmallCardUrl,
		NewTabCategoryUrl:      tabInfo.NewTabCategoryUrl,
		UGameID:                tabInfo.UGameId,
		IsMinorityGame:         tabInfo.IsMinorityGame,
		Sort:                   tabInfo.Sort,
		PcSort:                 tabInfo.PcSort,
		HomePageType:           uint32(tabInfo.HomePageType),
		//ViewType:                  uint32(tabInfo.ViewType),
		HomeSort:                    tabInfo.HomeSort,
		ShieldMarketIds:             convertUint32SliceToStr(tabInfo.ShieldMarketIds),
		MinorCertificationMarketIds: convertUint32SliceToStr(tabInfo.MinorCertificationMarketIds),
		ShowPublishButton:           tabInfo.GetShowPublishButton(),
		TabAliasName:                tabInfo.GetTabAliasName(),
		MiniGameId:                  tabInfo.GetMiniGameId(),
		SchemeDetailType:            tabInfo.GetSchemeDetailType(),
		UniqueName:                  tabInfo.GetName(),
	}

	return tabDao
}

func convertToTabMap(tabInfo *pb.Tab) map[string]interface{} {
	tabDaoMap := map[string]interface{}{
		"id": tabInfo.Id,
		//Weight:                    tabInfo.Weight,
		"name":     tabInfo.Name,
		"tab_type": uint32(tabInfo.TabType),
		//"tag_id":                       tabInfo.TagId,
		"image_uri":                    tabInfo.ImageUri,
		"room_name":                    strings.Join(tabInfo.RoomName, "^"),
		"version":                      tabInfo.Version,
		"newcomer_image_uri":           tabInfo.NewcomerImageUri,
		"newcomer_color":               tabInfo.NewcomerColor,
		"newcomer_welcome":             strings.Join(tabInfo.NewcomerWelcome, "^"),
		"show_image_uri":               tabInfo.ShowImageUri,
		"mic_mod":                      tabInfo.MicMod,
		"follow_label_img":             tabInfo.FollowLabelImg,
		"newcomer_font_color":          tabInfo.NewcomerFontColor,
		"newcomer_font_background_uri": tabInfo.NewcomerFontBackgroundUri,
		"follow_label_text":            tabInfo.FollowLabelText,
		"category_id":                  tabInfo.CategoryId,
		"mini_game_num":                tabInfo.MiniGameNum,
		"client_ver":                   tabInfo.ClientVer,
		"client_ver_upper_limit":       tabInfo.ClientVerUpperLimit,
		"uid_tail_num":                 tabInfo.UidTailNum,
		"uid_white_list":               tabInfo.UidWhiteList,
		"channel_package_id":           strings.Join(tabInfo.ChannelPackageId, "^"),
		"find_playing_text":            tabInfo.FindPlayingText,
		"find_playing_img":             tabInfo.FindPlayingImg,
		"cards_image_url":              tabInfo.CardsImageUrl,
		"mask_layer":                   tabInfo.MaskLayer,
		//RoomLabel:                 tabInfo.RoomLabel,
		//TabLabel:                  tabInfo.TabLabel,
		"match_type":       tabInfo.MatchType,
		"tem_match_policy": tabInfo.TemMatchPolicy,
		"link_id":          tabInfo.LinkId,
		"platform_type":    uint32(tabInfo.PlatformType),
		"category_sort":    tabInfo.CategorySort,
		//"display_elem":            tabInfo.DisplayElem,
		"radar_distribution_image": tabInfo.RadarDistributionImage,
		"room_distribution_image":  tabInfo.RoomDistributionImage,
		"bottom_text_color":        tabInfo.BottomTextColor,
		"welcome_text_color":       tabInfo.WelcomeTextColor,
		"small_card_url":           tabInfo.SmallCardUrl,
		"new_tab_category_url":     tabInfo.NewTabCategoryUrl,
		"u_game_id":                tabInfo.UGameId,
		"is_minority_game":         tabInfo.IsMinorityGame,
		//"sort":                     tabInfo.Sort, update的时候前端并没有传
		"pc_sort": tabInfo.PcSort,
		//"home_page_type":           uint32(tabInfo.HomePageType),
		//ViewType:                  uint32(tabInfo.ViewType),
		"home_sort":                      tabInfo.HomeSort,
		"shield_market_ids":              convertUint32SliceToStr(tabInfo.ShieldMarketIds),
		"minor_certification_market_ids": convertUint32SliceToStr(tabInfo.MinorCertificationMarketIds),
		"show_publish_button":            tabInfo.GetShowPublishButton(),
		"tab_alias_name":                 tabInfo.GetTabAliasName(),
		"mini_game_id":                   tabInfo.GetMiniGameId(),
		"scheme_detail_type":             tabInfo.GetSchemeDetailType(),
		"update_time":                    time.Now().Unix(),
	}

	return tabDaoMap
}

func convertBlock(blockInfo *pb.Block, tabId, priority uint32) *dao.Block {
	blockDao := &dao.Block{
		Id:    blockInfo.Id,
		TabId: tabId,
		Title: blockInfo.Title,
		Mode:  uint32(blockInfo.Mode),
		//ShowRow:  blockInfo.ShowRow,
		Priority: priority,
		//ControlTeamSize: blockInfo.ControlTeamSize,
		MostSelectNum:    blockInfo.MostSelectNum,
		OnlyShowAfter628: blockInfo.GetOnlyShowAfter_628(),
		SubTitle:         blockInfo.GetSubTitle(),
		ShowPosition:     blockInfo.GetShowPosition(),
		SetMicName:       blockInfo.GetSetMicName(),
	}
	return blockDao
}

func convertElem(elemInfo *pb.Elem, blockId, priority uint32) *dao.Elem {
	elemDao := &dao.Elem{
		Id:            elemInfo.Id,
		BlockId:       blockId,
		Title:         elemInfo.Title,
		Priority:      priority,
		MiniGameModel: elemInfo.MiniGameModel,
		MaxNum:        elemInfo.GetMaxNum(),
		MinNum:        elemInfo.GetMinNum(),
	}
	if elemInfo.Contacts != nil {
		elemDao.ContactBlockID = elemInfo.Contacts.BlockId
		elemDao.Before = elemInfo.Contacts.Before
		elemDao.After = elemInfo.Contacts.After
	}
	checker := utils.NewForCntChecker("convertElem")
	for _, flag := range elemInfo.GetPublicFlags() {
		checker.Incr()
		if _, ok := pb.Elem_PublicFlag_name[int32(flag)]; ok && flag > 0 {
			elemDao.PublicFlag = elemDao.PublicFlag.Set(uint8(flag) - 1)
		}
	}
	return elemDao
}

func convertToCategoryMap(categoryInfo *pb.Category) map[string]interface{} {
	categoryDaoMap := map[string]interface{}{
		"id":                             categoryInfo.CategoryId,
		"title":                          categoryInfo.Title,
		"platform_type":                  uint32(categoryInfo.PlatformType),
		"new_category_title":             categoryInfo.NewCategoryTitle,
		"image_url":                      categoryInfo.ImageUrl,
		"icon":                           categoryInfo.Icon,
		"mask_layer":                     categoryInfo.MaskLayer,
		"can_select_num":                 categoryInfo.CanSelectNum,
		"minor_certification_market_ids": convertUint32SliceToStr(categoryInfo.MinorCertificationMarketIds),
		"category_alias_name":            categoryInfo.GetCategoryAliasName(),
	}
	return categoryDaoMap
}

func convertToCategory(categoryInfo *pb.Category) *dao.Category {
	categoryDao := &dao.Category{
		CategoryId:                  categoryInfo.CategoryId,
		Title:                       categoryInfo.Title,
		CategoryType:                uint32(categoryInfo.CategoryType),
		SortDesc:                    categoryInfo.SortDesc,
		PlatformType:                uint32(categoryInfo.PlatformType),
		NewCategoryTitle:            categoryInfo.NewCategoryTitle,
		ImageUrl:                    categoryInfo.ImageUrl,
		Icon:                        categoryInfo.Icon,
		MaskLayer:                   categoryInfo.MaskLayer,
		CanSelectNum:                categoryInfo.CanSelectNum,
		MinorCertificationMarketIds: convertUint32SliceToStr(categoryInfo.MinorCertificationMarketIds),
		CategoryAliasName:           categoryInfo.GetCategoryAliasName(),
	}
	return categoryDao
}

func convertToCategoryPb(categoryInfo *dao.Category) *pb.Category {
	categoryPb := &pb.Category{
		CategoryId:                  categoryInfo.CategoryId,
		Title:                       categoryInfo.Title,
		SortDesc:                    categoryInfo.SortDesc,
		UpdateTime:                  categoryInfo.UpdateTime,
		CategoryType:                pb.CategoryType(categoryInfo.CategoryType),
		PlatformType:                pb.PlatformType(categoryInfo.PlatformType),
		NewCategoryTitle:            categoryInfo.NewCategoryTitle,
		ImageUrl:                    categoryInfo.ImageUrl,
		Icon:                        categoryInfo.Icon,
		MaskLayer:                   categoryInfo.MaskLayer,
		CanSelectNum:                categoryInfo.CanSelectNum,
		MinorCertificationMarketIds: convertStrToUint32Slice(categoryInfo.MinorCertificationMarketIds),
		CategoryAliasName:           categoryInfo.CategoryAliasName,
		SpecialCategoryMapping:      uint32(conf.TopicChannelTabConfigLoader.GetCategoryMapping()[categoryInfo.CategoryId]),
	}
	return categoryPb
}

func convertToTabPb(tabInfo *dao.Tab) *pb.Tab {
	tabPb := &pb.Tab{
		Id:       tabInfo.Id,
		Name:     tabInfo.Name,
		TabType:  pb.Tab_TabType(tabInfo.TabType),
		TagId:    tabInfo.TagId,
		ImageUri: tabInfo.ImageUri,
		RoomName: strings.Split(tabInfo.RoomName, "^"),
		Version:  tabInfo.Version,
		//NewcomerImageUri:            tabInfo.NewcomerImageUri,
		NewcomerColor: tabInfo.NewcomerColor,
		//NewcomerWelcome:             strings.Split(tabInfo.NewcomerWelcome, "^"),
		ShowImageUri:              tabInfo.ShowImageUri,
		MicMod:                    tabInfo.MicMod,
		FollowLabelImg:            tabInfo.FollowLabelImg,
		NewcomerFontColor:         tabInfo.NewcomerFontColor,
		NewcomerFontBackgroundUri: tabInfo.NewcomerFontBackgroundUri,
		FollowLabelText:           tabInfo.FollowLabelText,
		CategoryId:                tabInfo.CategoryId,
		MiniGameNum:               tabInfo.MiniGameNum,
		ClientVer:                 tabInfo.ClientVer,
		ClientVerUpperLimit:       tabInfo.ClientVerUpperLimit,
		UidTailNum:                tabInfo.UidTailNum,
		UidWhiteList:              tabInfo.UidWhiteList,
		//ChannelPackageId:            strings.Split(tabInfo.ChannelPackageId, "^"),
		FindPlayingText:             tabInfo.FindPlayingText,
		FindPlayingImg:              tabInfo.FindPlayingImg,
		CardsImageUrl:               tabInfo.CardImageUrl,
		MaskLayer:                   tabInfo.MaskLayer,
		RoomLabel:                   tabInfo.RoomLabel,
		TabLabel:                    tabInfo.TabLabel,
		MatchType:                   tabInfo.MatchType,
		TemMatchPolicy:              tabInfo.TemMatchPolicy,
		LinkId:                      tabInfo.LinkId,
		PlatformType:                pb.PlatformType(tabInfo.PlatformType),
		CategorySort:                tabInfo.CategorySort,
		DisplayElem:                 tabInfo.DisplayElem,
		RadarDistributionImage:      tabInfo.RadarDistributionImage,
		RoomDistributionImage:       tabInfo.RoomDistributionImage,
		BottomTextColor:             tabInfo.BottomTextColor,
		WelcomeTextColor:            tabInfo.WelcomeTextColor,
		SmallCardUrl:                tabInfo.SmallCardUrl,
		NewTabCategoryUrl:           tabInfo.NewTabCategoryUrl,
		UGameId:                     tabInfo.UGameID,
		IsMinorityGame:              tabInfo.IsMinorityGame,
		Sort:                        tabInfo.Sort,
		PcSort:                      tabInfo.PcSort,
		HomePageType:                pb.HomePageType(tabInfo.HomePageType),
		ViewType:                    pb.ViewType(tabInfo.ViewType),
		HomeSort:                    tabInfo.HomeSort,
		ShieldMarketIds:             convertStrToUint32Slice(tabInfo.ShieldMarketIds),
		MinorCertificationMarketIds: convertStrToUint32Slice(tabInfo.MinorCertificationMarketIds),
		DefaultChannelCondition:     tabInfo.DefaultCondition,
		BackgroundImgUrl:            tabInfo.BackgroundImgUrl,
		TopicChannelViewType:        tabInfo.TopicChannelViewType,
		ShowPublishButton:           tabInfo.ShowPublishButton,
		HideFilter:                  tabInfo.HideFilter,
		TabAliasName:                tabInfo.TabAliasName,
		MiniGameId:                  tabInfo.MiniGameId,
		SchemeDetailType:            tabInfo.SchemeDetailType,
		UniqueName:                  tabInfo.UniqueName,
	}
	if tabInfo.RoomName != "" {
		tabPb.RoomName = strings.Split(tabInfo.RoomName, "^")
	}
	if tabInfo.NewcomerWelcome != "" {
		tabPb.NewcomerWelcome = strings.Split(tabInfo.NewcomerWelcome, "^")
	}
	if tabInfo.ChannelPackageId != "" {
		tabPb.ChannelPackageId = strings.Split(tabInfo.ChannelPackageId, "^")
	}

	if tabInfo.SpecialCondition != "" {
		specialCondition := map[string]*pb.ChannelCondition{}
		err := json.Unmarshal([]byte(tabInfo.SpecialCondition), &specialCondition)
		if err != nil {
			log.ErrorWithCtx(context.Background(), "specialCondition: %v convertToTabPb error %v", tabInfo.SpecialCondition, err)
			return tabPb
		}
		tabPb.SpecialChannelCondition = convertMapToChannelCondition(specialCondition)
	}

	if conf.TopicChannelTabConfigLoader.GetCategoryMapping() != nil {
		tabPb.CategoryMapping = uint32(conf.TopicChannelTabConfigLoader.GetCategoryMapping()[tabPb.GetCategoryId()])
	}

	return tabPb
}

func convertMapToChannelCondition(specialCondition map[string]*pb.ChannelCondition) map[uint32]*pb.ChannelCondition {
	res := make(map[uint32]*pb.ChannelCondition)
	checker := utils.NewForCntChecker("convertMapToChannelCondition")
	for str1, v := range specialCondition {
		checker.Incr()
		k, _ := strconv.Atoi(str1)
		if _, ok := res[uint32(k)]; ok {
			continue
		}
		res[uint32(k)] = v
	}
	return res
}
func convertStrToUint32Slice(str string) []uint32 {
	checker := utils.NewForCntChecker("convertStrToUint32Slice")
	strSlice := strings.Split(str, ",")
	if len(strSlice) > conf.TopicChannelTabConfigLoader.GetErrLenCnt() {
		log.Errorf("len err:%d", len(strSlice))
	}
	res := make([]uint32, 0, len(strSlice))
	for _, s := range strSlice {
		checker.Incr()
		si, err := strconv.Atoi(s)
		if err != nil {
			continue
		}
		res = append(res, uint32(si))
	}
	return res
}
func convertUint32SliceToStr(slice []uint32) string {
	if len(slice) > conf.TopicChannelTabConfigLoader.GetErrLenCnt() {
		log.Errorf("len err:%d", len(slice))
	}
	res := make([]string, len(slice))
	checker := utils.NewForCntChecker("convertUint32SliceToStr")
	for i, s := range slice {
		checker.Incr()
		res[i] = strconv.Itoa(int(s))
	}
	return strings.Join(res, ",")
}

func convertElemPb(elemInfo *dao.Elem) *pb.Elem {
	elemPb := &pb.Elem{
		Id:    elemInfo.Id,
		Title: elemInfo.Title,
		Contacts: &pb.Contact{
			BlockId: elemInfo.ContactBlockID,
			Before:  elemInfo.Before,
			After:   elemInfo.After,
		},
		MiniGameModel: elemInfo.MiniGameModel,
		Priority:      elemInfo.Priority,
		MinNum:        elemInfo.MinNum,
		MaxNum:        elemInfo.MaxNum,
	}
	checker := utils.NewForCntChecker("convertElemPb")
	for flag := range pb.Elem_PublicFlag_name {
		checker.Incr()
		if flag > 0 && elemInfo.PublicFlag.Get(uint8(flag)-1) {
			elemPb.PublicFlags = append(elemPb.PublicFlags, pb.Elem_PublicFlag(flag))
		}
	}
	sort.Slice(elemPb.PublicFlags, func(i, j int) bool { return elemPb.PublicFlags[i] < elemPb.PublicFlags[j] })
	return elemPb
}

func convertBlockPb(blockInfo *dao.Block, elemInfos []*dao.Elem) *pb.Block {
	blockPb := &pb.Block{
		Id:                blockInfo.Id,
		Title:             blockInfo.Title,
		Mode:              pb.Block_Mode(blockInfo.Mode),
		OnlyShowAfter_628: blockInfo.OnlyShowAfter628,
		SubTitle:          blockInfo.SubTitle,
		ShowPosition:      blockInfo.ShowPosition,
		SetMicName:        blockInfo.SetMicName,
	}

	elemPbs := make([]*pb.Elem, len(elemInfos))
	for i, elem := range elemInfos {
		elemPbs[i] = convertElemPb(elem)
	}
	blockPb.Elems = elemPbs
	blockPb.MostSelectNum = blockInfo.MostSelectNum
	return blockPb
}

func convertBusinessBlockPb(blockInfo *dao.BusinessBlock, elemInfos []*dao.BusinessElem) *pb.BusinessBlock {
	blockPb := &pb.BusinessBlock{
		Id:         blockInfo.Id,
		Title:      blockInfo.Title,
		Mode:       pb.BusinessBlock_Mode(blockInfo.Mode),
		FilterType: pb.BusinessBlock_FilterType(blockInfo.FilterType),
	}
	if len(elemInfos) > conf.TopicChannelTabConfigLoader.GetErrLenCnt() {
		log.Errorf("len err:%d", len(elemInfos))
	}
	elemPbs := make([]*pb.BusinessElem, len(elemInfos))
	checker := utils.NewForCntChecker("convertBusinessBlockPb")
	for i, elem := range elemInfos {
		checker.Incr()
		elemPbs[i] = convertBusinessElemPb(elem)
	}
	blockPb.Elems = elemPbs
	blockPb.MostSelectNum = blockInfo.MostSelectNum
	blockPb.Source = pb.BusinessBlock_Source(blockInfo.Source)
	return blockPb
}

func convertBusinessElemPb(elemInfo *dao.BusinessElem) *pb.BusinessElem {
	elemPb := &pb.BusinessElem{
		Id:    elemInfo.Id,
		Title: elemInfo.Title,
		Mode:  elemInfo.Mode,
	}
	return elemPb
}

func convertOfficialRoomNameConfigListPb(roomNames []*dao.RoomName) []*pb.OfficialRoomNameConfig {
	if len(roomNames) > conf.TopicChannelTabConfigLoader.GetErrLenCnt() {
		log.Errorf("len err:%d", len(roomNames))
	}
	officialRoomNamePbList := make([]*pb.OfficialRoomNameConfig, len(roomNames))
	checker := utils.NewForCntChecker("convertOfficialRoomNameConfigListPb")
	for i, roomName := range roomNames {
		checker.Incr()
		officialRoomNamePbList[i] = &pb.OfficialRoomNameConfig{
			Id:     roomName.Id,
			TabId:  roomName.TabId,
			Name:   roomName.Name,
			ElemId: roomName.ElemId,
		}
	}
	return officialRoomNamePbList
}

func convertOfficialRoomNameConfigListStr(roomNames []*dao.RoomName) []string {
	if len(roomNames) > conf.TopicChannelTabConfigLoader.GetErrLenCnt() {
		log.Errorf("len err:%d", len(roomNames))
	}
	officialRoomNameStrList := make([]string, len(roomNames))
	checker := utils.NewForCntChecker("convertOfficialRoomNameConfigListStr")
	for i, roomName := range roomNames {
		checker.Incr()
		officialRoomNameStrList[i] = roomName.Name
	}
	return officialRoomNameStrList
}

func convertToOfficialRoomNameConfigMap(roomName *pb.OfficialRoomNameConfig) map[string]interface{} {
	roomNameMap := map[string]interface{}{
		"id":      roomName.GetId(),
		"tab_id":  roomName.GetTabId(),
		"name":    roomName.GetName(),
		"elem_id": roomName.GetElemId(),
	}
	return roomNameMap
}
func convertToOfficialRoomNameConfigEntity(roomName *pb.OfficialRoomNameConfig) *dao.RoomName {
	roomNameDao := &dao.RoomName{
		Id:     roomName.GetId(),
		Name:   roomName.GetName(),
		TabId:  roomName.GetTabId(),
		ElemId: roomName.GetElemId(),
	}
	return roomNameDao
}

func convertToTabQuestionsEntity(tabId uint32, questions []*pb.TabQuestion, enabled bool) *dao.TabQuestions {
	q := &dao.TabQuestions{TabId: tabId, Enabled: enabled}
	checker := utils.NewForCntChecker("convertToTabQuestionsEntity")
	for _, question := range questions {
		checker.Incr()
		q.Questions = append(q.Questions, &dao.TabQuestion{
			Title:  question.GetTitle(),
			Labels: question.GetLabels(),
		})
	}

	return q
}

func converToTabQuestionsPb(tabQuestions *dao.TabQuestions) ([]*pb.TabQuestion, bool) {
	if tabQuestions == nil {
		return nil, false
	}

	if len(tabQuestions.Questions) > conf.TopicChannelTabConfigLoader.GetErrLenCnt() {
		log.Errorf("len err:%d", len(tabQuestions.Questions))
	}
	questions := make([]*pb.TabQuestion, 0, len(tabQuestions.Questions))
	checker := utils.NewForCntChecker("converToTabQuestionsPb")
	for _, question := range tabQuestions.Questions {
		checker.Incr()
		questions = append(questions, &pb.TabQuestion{Title: question.Title, Labels: question.Labels})
	}

	return questions, tabQuestions.Enabled
}

func convertToBlockElemRelationsEntity(tabId uint32, infos []*pb.DisplayBlockInfo) (*dao.BlockElemRelations, error) {
	relations := &dao.BlockElemRelations{
		TabId: tabId,
	}
	relations.BlockInfos = make([]*dao.DisplayBlockInfo, 0, len(infos))
	for _, info := range infos {
		relations.BlockInfos = append(relations.BlockInfos, &dao.DisplayBlockInfo{
			BlockId:            info.GetBlockId(),
			ElemBindBlockInfos: convertElemBindBlockInfoToDao(info.GetElemBindBlockInfos()),
		})
	}
	return relations, nil
}

func convertElemBindBlockInfoToDao(in []*pb.ElemBindBlockInfo) []*dao.ElemBindBlockInfo {
	if len(in) > conf.TopicChannelTabConfigLoader.GetErrLenCnt() {
		log.Errorf("len err:%d", len(in))
	}
	res := make([]*dao.ElemBindBlockInfo, 0, len(in))
	checker := utils.NewForCntChecker("convertElemBindBlockInfoToDao")
	for _, v := range in {
		checker.Incr()
		res = append(res, &dao.ElemBindBlockInfo{
			ElemId:          v.GetElemId(),
			BindBlockGroups: convertBindBlockGroupToDao(v.GetBindBlockGroups()),
		})
	}
	return res
}

func convertBindBlockGroupToDao(in []*pb.ElemBindBlockInfo_BlockElemGroup) []*dao.BlockElemGroup {
	if len(in) > conf.TopicChannelTabConfigLoader.GetErrLenCnt() {
		log.Errorf("len err:%d", len(in))
	}
	res := make([]*dao.BlockElemGroup, 0, len(in))
	checker := utils.NewForCntChecker("convertBindBlockGroupToDao")
	for _, v := range in {
		checker.Incr()
		res = append(res, &dao.BlockElemGroup{
			RelatedBlockId: v.GetRelatedBlockId(),
			RelatedElemIds: v.GetRelatedElemIds(),
		})
	}
	return res
}
func saveProfile(name string, debug int) (err error) {
	//runtime.GC()
	os.Mkdir("prof", os.ModePerm)
	podName := cost_time_reporter.GetPodName()
	/*unix := time.Now().Unix() % 10
	  if unix > 3 {
	  	return
	  }*/
	f, err := os.Create(fmt.Sprintf("prof/%s_%s_%s.prof", name, podName, time.Now().Format("2006_01_02_04_05")))
	if err != nil {
		return
	}
	defer func() {
		closeErr := f.Close()
		if err == nil {
			err = closeErr
		}
	}()

	err = pprof.Lookup(name).WriteTo(f, debug)
	return
}

func LocalStartPprof() {
	start := time.Now()
	log.InfoWithCtx(context.Background(), "LocalStartPprof start")
	runtime.SetMutexProfileFraction(1)
	runtime.SetBlockProfileRate(1)
	var kindProfile = []struct {
		name  string
		debug int
	}{
		{
			"heap",
			1,
		},
		{
			"goroutine",
			2,
		},
		{
			"block",
			1,
		},
		{
			"mutex",
			1,
		},
	}
	ctx := context.Background()
	for _, p := range kindProfile {
		err := saveProfile(p.name, p.debug)
		if err != nil {
			log.InfoWithCtx(ctx, "saveProfile fail, err:%s, name:%s \n", err.Error(), p.name)
			continue
		}
	}
	log.InfoWithCtx(context.Background(), "LocalStartPprof end, cost:%d", time.Since(start)/1e6)
}

var memProfCnt uint32 = 0

func IsPrint() bool {
	nowMem := getMem()
	if nowMem >= conf.TopicChannelTabConfigLoader.GetMemProfValue() {
		if memProfCnt < conf.TopicChannelTabConfigLoader.GetMemProfCnt() {
			memProfCnt++
			log.Infof("Total alloc suc: %d MB, prof:%d", nowMem, conf.TopicChannelTabConfigLoader.GetMemProfValue())
			return true
		}
	}

	return false
}

func getMem() uint64 {
	var mem runtime.MemStats
	runtime.ReadMemStats(&mem)
	nowMem := mem.Alloc / 1000 / 1000
	log.Infof("Total alloc: %d MB, %+v", mem.Alloc/1000/1000, mem)
	//fmt.Printf("Total alloc: %d GB, %v\n", mem.Alloc/1000/1000, mem)
	return nowMem
}

func LocalOutPutPprof() {
	start := time.Now()
	if !IsPrint() {
		return
	}
	log.InfoWithCtx(context.Background(), "LocalStartPprof start")
	var kindProfile = []struct {
		name  string
		debug int
	}{
		{
			"heap",
			1,
		},
	}
	ctx := context.Background()
	for _, p := range kindProfile {
		err := saveProfile(p.name, p.debug)
		if err != nil {
			log.InfoWithCtx(ctx, "saveProfile fail, err:%v, name:%s", err.Error(), p.name)
			continue
		}
	}
	log.InfoWithCtx(ctx, "LocalStartPprof end, cost:%d", time.Since(start)/1e6)

}

/*
func SignalService() {
	ctx := context.Background()
	log.InfoWithCtx(ctx, "signalService service start")
	start := time.Now()
	if !IsPrint() {
		return
	}

	// 释放重启信号，进程重启
	err := syscall.Kill(syscall.Getpid(), syscall.SIGKILL)

	log.InfoWithCtx(ctx, "signalService service end, err:%v, cost%:d", err, time.Since(start)/1e6)

	//wait := make(chan os.Signal, 1)
	//signal.Notify(wait, syscall.SIGTERM, syscall.SIGINT, syscall.SIGKILL, syscall.SIGHUP, syscall.SIGQUIT)

}

*/

// DeepCopy src nil will panic
func DeepCopy(dst, src interface{}) error {
	var buf bytes.Buffer
	if err := gob.NewEncoder(&buf).Encode(src); err != nil {
		return err
	}
	return gob.NewDecoder(bytes.NewBuffer(buf.Bytes())).Decode(dst)
}
