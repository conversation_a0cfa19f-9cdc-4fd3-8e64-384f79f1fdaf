package enter_room_notify

import (
	"context"
	"errors"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"gitlab.ttyuyin.com/tyr/x/terrors"
	mock_device_info "golang.52tt.com/clients/datacenter/device-info-service/mocks"
	account_go "golang.52tt.com/clients/mocks/account-go"
	"golang.52tt.com/clients/mocks/channelol"
	mock_gnobility "golang.52tt.com/clients/mocks/gnobility"
	mock_nobility "golang.52tt.com/clients/mocks/nobility"
	presence "golang.52tt.com/clients/mocks/presence/v2"
	svip "golang.52tt.com/clients/mocks/super-player-privilege"
	"golang.52tt.com/clients/mocks/ugc/friendship"
	mock_user_auth_history "golang.52tt.com/clients/mocks/user-auth-history"
	ukw "golang.52tt.com/clients/mocks/you-know-who"
	abtest "golang.52tt.com/pkg/abtest/mocks"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/channel"
	game_play_logic "golang.52tt.com/protocol/app/game-play-logic"
	"golang.52tt.com/protocol/common/status"
	account_go_pb "golang.52tt.com/protocol/services/account-go"
	game_fre_server "golang.52tt.com/protocol/services/game-fre-server"
	game_time "golang.52tt.com/protocol/services/game-time/game-time"
	"golang.52tt.com/protocol/services/gnobility"
	"golang.52tt.com/protocol/services/mocks"
	nobility_svr "golang.52tt.com/protocol/services/nobilitysvr"
	pb "golang.52tt.com/protocol/services/presencesvr"
	super_player_privilege "golang.52tt.com/protocol/services/super-player-privilege"
	"golang.52tt.com/protocol/services/youknowwho"
	config "golang.52tt.com/services/interact-guide-middle/internal/config/ttconfig/interact_guide_middle"
	notify "golang.52tt.com/services/interact-guide-middle/internal/mgr/notify/mocks"
	"reflect"
	"testing"
)

type mgrHelperForTest struct {
	*EnterRoomNotifyMgr
}

func newMgrHelperForTest(t *testing.T) *mgrHelperForTest {
	controller := gomock.NewController(t)
	return &mgrHelperForTest{
		&EnterRoomNotifyMgr{
			accountCli:         account_go.NewMockIClient(controller),
			friendshipCli:      friendship.NewMockIClient(controller),
			userAuthHistoryCli: mock_user_auth_history.NewMockIClient(controller),
			presenceCli:        presence.NewMockIClient(controller),
			ukwCli:             ukw.NewMockIClient(controller),
			svipCli:            svip.NewMockIClient(controller),
			nobilityCli:        mock_nobility.NewMockIClient(controller),
			gnobilityCli:       mock_gnobility.NewMockIClient(controller),
			abtestCli:          abtest.NewMockIABTestClient(controller),
			channelOlCli:       channelol.NewMockIClient(controller),
			gameFreCli:         mocks.NewMockGameFreServerClient(controller),
			gameTimeCli:        game_time.NewMockGameTimeClient(controller),
			msgPusher:          notify.NewMockIMsgPusher(controller),
			deviceInfoCli:      mock_device_info.NewMockIClient(controller),
		},
	}
}

func init() {
	config.SetInteractGuideMiddleConfig(&config.InteractGuideMiddleConfig{
		EnterRoomPushConf: &config.EnterRoomPushConfig{
			AbCfg: &config.ABTestCfg{
				ArgName:  "enter_room_notify",
				ExpGroup: "exp_group",
				RcmdTestCfg: config.RcmdTestCfg{
					NewKey:    "enter_room_notify",
					ActiveKey: "enter_room_notify_hk",
				},
			},
			HandleUserLimit:        100,
			ReceivePushFreLimit:    2,
			WarningHandleUserLimit: 5,
		},
	})
}

func (receiver *mgrHelperForTest) getAccountCli() *account_go.MockIClient {
	return receiver.accountCli.(*account_go.MockIClient)
}

func (receiver *mgrHelperForTest) getFriendshipCli() *friendship.MockIClient {
	return receiver.friendshipCli.(*friendship.MockIClient)
}

func (receiver *mgrHelperForTest) getUserAuthHistoryCli() *mock_user_auth_history.MockIClient {
	return receiver.userAuthHistoryCli.(*mock_user_auth_history.MockIClient)
}

func (receiver *mgrHelperForTest) getPresenceCli() *presence.MockIClient {
	return receiver.presenceCli.(*presence.MockIClient)
}

func (receiver *mgrHelperForTest) getUkwCli() *ukw.MockIClient {
	return receiver.ukwCli.(*ukw.MockIClient)
}

func (receiver *mgrHelperForTest) getSvipCli() *svip.MockIClient {
	return receiver.svipCli.(*svip.MockIClient)
}

func (receiver *mgrHelperForTest) getNobilityCli() *mock_nobility.MockIClient {
	return receiver.nobilityCli.(*mock_nobility.MockIClient)
}

func (receiver *mgrHelperForTest) getGnobilityCli() *mock_gnobility.MockIClient {
	return receiver.gnobilityCli.(*mock_gnobility.MockIClient)
}

func (receiver *mgrHelperForTest) getAbtestCli() *abtest.MockIABTestClient {
	return receiver.abtestCli.(*abtest.MockIABTestClient)
}

func (receiver *mgrHelperForTest) getChannelOlCli() *channelol.MockIClient {
	return receiver.channelOlCli.(*channelol.MockIClient)
}

func (receiver *mgrHelperForTest) getGameFreCli() *mocks.MockGameFreServerClient {
	return receiver.gameFreCli.(*mocks.MockGameFreServerClient)
}

func (receiver *mgrHelperForTest) getGameTimeCli() *game_time.MockGameTimeClient {
	return receiver.gameTimeCli.(*game_time.MockGameTimeClient)
}

func (receiver *mgrHelperForTest) getMsgPusher() *notify.MockIMsgPusher {
	return receiver.msgPusher.(*notify.MockIMsgPusher)
}

func (receiver *mgrHelperForTest) getDeviceInfoCli() *mock_device_info.MockIClient {
	return receiver.deviceInfoCli.(*mock_device_info.MockIClient)
}

func TestHandleWithNoSubscribers(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	m := newMgrHelperForTest(t)

	ctx := context.Background()
	uid := uint32(1)
	cid := uint32(1)
	channelType := uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE)

	m.getGameTimeCli().EXPECT().GetUserGameTime(ctx, &game_time.GetUserGameTimeRequest{Uid: uid}).Return(&game_time.GetUserGameTimeResponse{
		Info: &game_time.GameTimeInfo{
			EnterRoomNotifyPerm: uint32(game_play_logic.EnterRoomNotifyPerm_ENTER_ROOM_NOTIFY_PERM_NOBODY),
		},
	}, nil)

	m.Handle(ctx, uid, cid, channelType)
}

func TestPreCheckWithUKWSwitchOn(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s := newMgrHelperForTest(t)

	ctx := context.Background()
	uid := uint32(1)
	channelType := uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE)

	// Mock the response for GetUKWInfo
	s.getUkwCli().EXPECT().GetUKWInfo(gomock.Any(), uid).Return(&youknowwho.UKWInfo{
		UkwPermissionInfo: &youknowwho.UKWPermissionInfo{
			Switch: youknowwho.UKWSwitchType_UKW_SWITCH_ON,
		},
	}, nil)

	preCheckResult, err := s.preCheck(ctx, uid, channelType)

	assert.NoError(t, err)
	assert.False(t, preCheckResult)
}

func TestPreCheckWithInvisibleUser(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	m := newMgrHelperForTest(t)

	ctx := context.Background()
	uid := uint32(1)
	channelType := uint32(2)

	// Mock the response for isUserInvisible
	m.getNobilityCli().EXPECT().GetNobilityInfo(gomock.Any(), uid, false).Return(&nobility_svr.NobilityInfo{
		Invisible: false,
	}, nil)
	m.getGnobilityCli().EXPECT().GetNobilitySwitchFlag(gomock.Any(), []uint32{uid}).Return(&gnobility.GetNobilitySwitchFlagResp{
		SwitchFlagList: []uint32{0},
	}, nil)
	m.getSvipCli().EXPECT().GetUserSVIPPrivilegeProfile(gomock.Any(), uid).Return(&super_player_privilege.GetUserSVIPPrivilegeProfileResp{
		PrivilegeProfile: &super_player_privilege.SVIPPrivilegeProfile{
			OnlineSwitch: super_player_privilege.OnlineSwitch_ENUM_ONLINE_SWITCH_STEALTH,
		},
	}, nil)

	invisible, err := m.EnterRoomNotifyMgr.preCheck(ctx, uid, channelType)

	assert.False(t, invisible)
	assert.Nil(t, err)
}

func TestHandleWithPreCheckFailure(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mgr := newMgrHelperForTest(t)

	ctx := context.Background()
	uid := uint32(1)
	mockErr := protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "mock error")
	//cid := uint32(1)
	channelType := uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE)

	// Mocking the preCheck function to return false and an error
	mgr.getUkwCli().EXPECT().GetUKWInfo(gomock.Any(), uid).Return(&youknowwho.UKWInfo{}, nil)
	mgr.getNobilityCli().EXPECT().GetNobilityInfo(gomock.Any(), uid, false).Return(&nobility_svr.NobilityInfo{}, nil)
	mgr.getGnobilityCli().EXPECT().GetNobilitySwitchFlag(gomock.Any(), []uint32{uid}).Return(&gnobility.GetNobilitySwitchFlagResp{}, nil)
	mgr.getSvipCli().EXPECT().GetUserSVIPPrivilegeProfile(gomock.Any(), uid).Return(nil, mockErr)

	invisible, err := mgr.EnterRoomNotifyMgr.preCheck(ctx, uid, channelType)

	assert.False(t, invisible)
	assert.Equal(t, err, mockErr)

	// Assert logs or other side effects if necessary
	// For example, you might check if certain log entries were written
	// or if certain functions were called a specific number of times.
}

func TestEnterRoomNotifyMgr_HandleWithFilterEnterRoomNotifyPermUserFailure(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	m := newMgrHelperForTest(t)

	ctx := context.Background()
	uid := uint32(1)
	cid := uint32(2)
	channelType := uint32(100)

	enterRoomNotifyPerm := uint32(1)
	toUids := []uint32{3, 4, 5}

	m.getGameTimeCli().EXPECT().GetUserGameTime(gomock.Any(), gomock.Any()).Return(&game_time.GetUserGameTimeResponse{
		Info: &game_time.GameTimeInfo{
			EnterRoomNotifyPerm: enterRoomNotifyPerm,
		},
	}, nil)
	m.getGameTimeCli().EXPECT().GetUserEnterRoomNotifySubscribers(gomock.Any(), gomock.Any()).Return(&game_time.GetUserEnterRoomNotifySubscribersResponse{
		SubscriberUidList: toUids,
		Cursor:            "",
	}, nil)
	m.getGameFreCli().EXPECT().BatGetFreCountBySources(gomock.Any(), gomock.Any()).Return(&game_fre_server.BatGetFreCountBySourcesResp{}, nil)
	m.getNobilityCli().EXPECT().GetNobilityInfo(gomock.Any(), uid, false).Return(&nobility_svr.NobilityInfo{
		Invisible: false,
	}, nil)
	m.getGnobilityCli().EXPECT().GetNobilitySwitchFlag(gomock.Any(), []uint32{uid}).Return(&gnobility.GetNobilitySwitchFlagResp{
		SwitchFlagList: []uint32{0},
	}, nil)
	m.getSvipCli().EXPECT().GetUserSVIPPrivilegeProfile(gomock.Any(), uid).Return(&super_player_privilege.GetUserSVIPPrivilegeProfileResp{
		PrivilegeProfile: &super_player_privilege.SVIPPrivilegeProfile{
			OnlineSwitch: super_player_privilege.OnlineSwitch_ENUM_ONLINE_SWITCH_ONLINE,
		},
	}, nil)
	m.getFriendshipCli().EXPECT().BatchGetBiFollowingWithCache(gomock.Any(), uid, toUids, true, true).Return(nil, nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "internal error"))

	m.Handle(ctx, uid, cid, channelType)
}

func TestIsUserInvisibleWithSVIPInvisibleStatus(t *testing.T) {
	// Create a new mock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a new manager helper for test
	mgrHelper := newMgrHelperForTest(t)

	// Create a context
	ctx := context.Background()

	// Define the UID for testing
	uid := uint32(12345)

	// Mock the GetUserSVIPPrivilegeProfile response
	mgrHelper.getNobilityCli().EXPECT().GetNobilityInfo(gomock.Any(), uid, false).Return(&nobility_svr.NobilityInfo{
		Invisible: false,
	}, nil)
	mgrHelper.getGnobilityCli().EXPECT().GetNobilitySwitchFlag(gomock.Any(), []uint32{uid}).Return(&gnobility.GetNobilitySwitchFlagResp{
		SwitchFlagList: []uint32{0},
	}, nil)
	mgrHelper.getSvipCli().EXPECT().GetUserSVIPPrivilegeProfile(ctx, uid).Return(&super_player_privilege.GetUserSVIPPrivilegeProfileResp{
		PrivilegeProfile: &super_player_privilege.SVIPPrivilegeProfile{
			OnlineSwitch: super_player_privilege.OnlineSwitch_ENUM_ONLINE_SWITCH_STEALTH,
		},
	}, nil)

	// Call the function under test
	isInvisible, err := mgrHelper.isUserInvisible(ctx, uid)

	// Assert the results
	assert.Nil(t, err)
	assert.True(t, isInvisible)
}

func TestPreCheckWithValidUser(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	m := newMgrHelperForTest(t)

	ctx := context.Background()
	uid := uint32(1001)
	channelType := uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE)

	svipProfile := &super_player_privilege.GetUserSVIPPrivilegeProfileResp{
		PrivilegeProfile: &super_player_privilege.SVIPPrivilegeProfile{
			OnlineSwitch: super_player_privilege.OnlineSwitch_ENUM_ONLINE_SWITCH_ONLINE,
		},
	}
	m.getSvipCli().EXPECT().GetUserSVIPPrivilegeProfile(gomock.Any(), uid).Return(svipProfile, nil)

	nobilityInfo := &nobility_svr.NobilityInfo{
		Invisible: false,
	}
	m.getNobilityCli().EXPECT().GetNobilityInfo(gomock.Any(), uid, false).Return(nobilityInfo, nil)

	nobilitySwitchFlagResp := &gnobility.GetNobilitySwitchFlagResp{
		SwitchFlagList: []uint32{0},
	}
	m.getGnobilityCli().EXPECT().GetNobilitySwitchFlag(gomock.Any(), []uint32{uid}).Return(nobilitySwitchFlagResp, nil)

	ukwInfo := &youknowwho.UKWInfo{
		UkwPermissionInfo: &youknowwho.UKWPermissionInfo{
			Switch: youknowwho.UKWSwitchType_UKW_SWITCH_OFF,
		},
	}
	m.getUkwCli().EXPECT().GetUKWInfo(gomock.Any(), uid).Return(ukwInfo, nil)

	ok, err := m.preCheck(ctx, uid, channelType)
	assert.Nil(t, err)
	assert.True(t, ok)
}

/*
func TestHandleWithFilterInSameRoomUserFailure(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    m := newMgrHelperForTest(t)

    ctx := context.Background()
    uid := uint32(123)
    cid := uint32(456)
    channelType := uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE)

    // Setting up the mocks
    enterRoomNotifyPerm := uint32(1)
    toUids := []uint32{789, 1011, 1213}
    mockErr := protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "mock error")
    abCfg := config.GetInteractGuideMiddleConfig().GetEnterRoomPushConf().GetAbCfg()

    m.getGameTimeCli().EXPECT().GetUserGameTime(gomock.Any(), gomock.Any()).Return(&game_time.GetUserGameTimeResponse{
        Info: &game_time.GameTimeInfo{
            EnterRoomNotifyPerm: enterRoomNotifyPerm,
        },
    }, nil)
    m.getGameTimeCli().EXPECT().GetUserEnterRoomNotifySubscribers(gomock.Any(), gomock.Any()).Return(&game_time.GetUserEnterRoomNotifySubscribersResponse{
        SubscriberUidList: toUids,
        Cursor:            "",
    }, nil)
    m.getGameFreCli().EXPECT().BatGetFreCountBySources(gomock.Any(), gomock.Any()).Return(&game_fre_server.BatGetFreCountBySourcesResp{}, nil)
    m.getUkwCli().EXPECT().GetUKWInfo(gomock.Any(), gomock.Any()).Return(&youknowwho.UKWInfo{}, nil)
    m.getNobilityCli().EXPECT().GetNobilityInfo(gomock.Any(), uid, false).Return(&nobility_svr.NobilityInfo{
        Invisible: false,
    }, nil)
    m.getGnobilityCli().EXPECT().GetNobilitySwitchFlag(gomock.Any(), []uint32{uid}).Return(&gnobility.GetNobilitySwitchFlagResp{
        SwitchFlagList: []uint32{0},
    }, nil)
    m.getSvipCli().EXPECT().GetUserSVIPPrivilegeProfile(gomock.Any(), uid).Return(&super_player_privilege.GetUserSVIPPrivilegeProfileResp{
        PrivilegeProfile: &super_player_privilege.SVIPPrivilegeProfile{
            OnlineSwitch: super_player_privilege.OnlineSwitch_ENUM_ONLINE_SWITCH_ONLINE,
        },
    }, nil)
    m.getFriendshipCli().EXPECT().BatchGetBiFollowingWithCache(gomock.Any(), uid, toUids, true, true).Return(map[uint32]bool{789: true, 1011: true, 1213: false}, map[uint32]bool{789: true, 1011: true, 1213: true}, nil)
    m.getAccountCli().EXPECT().GetUsersByUids(gomock.Any(), gomock.Any()).Return(&account_go_pb.UsersResp{
        UserList: []*account_go_pb.UserResp{
            {
                Uid:          proto.Uint32(789),
                RegisteredAt: proto.Uint32(0),
            }, {
                Uid:          proto.Uint32(1011),
                RegisteredAt: proto.Uint32(0),
            }, {
                Uid:          proto.Uint32(1213),
                RegisteredAt: proto.Uint32(0),
            },
        },
    }, nil)
    m.getUserAuthHistoryCli().EXPECT().BatchGetUserLastLoginInfo(gomock.Any(), gomock.Any()).Return([]*user_auth_history.UserLoginInfo{
        {
            Uid:        789,
            DeviceId:   "device_1",
            ClientType: 0,
        }, {
            Uid:        1011,
            DeviceId:   "device_2",
            ClientType: 0,
        }, {
            Uid:        1213,
            DeviceId:   "device_3",
            ClientType: 0,
        },
    }, nil)

    m.getAbtestCli().EXPECT().BatchGetDeviceTestByArg(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[string]string{"device_1": abCfg.ExpGroup}, nil)
    m.getChannelOlCli().EXPECT().BatchGetUserChannelId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, mockErr)

    // Call the method
    mgr := m.EnterRoomNotifyMgr
    mgr.Handle(ctx, uid, cid, channelType)

    // Verify the expectations
    assert.NotEmpty(t, "EnterRoomNotifyMgr Handle success, uid:123, cid:456, toUids:[789 1011 1213]")
}
*/
/*
```

This test sets up the mocks to simulate the scenario where `filterInSameRoomUser` fails and ensures that the `Handle` function handles the error correctly. Note that the `filterInSameRoomUser` method is indirectly tested through the `Handle` method.

Make sure you have the necessary imports and that the `gomock` and `testify` packages are included in your `go.mod` file.

```go
require (
github.com/golang/mock v1.4.4
github.com/stretchr/testify v1.6.1
)
```

To run the test:

```bash
go test -v
*/
func TestHandleWithFilterNotInABTestUserFailure(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a helper for the test
	mgrHelper := newMgrHelperForTest(t)

	// Prepare the mock responses and expectations
	ctx := context.Background()
	uid := uint32(1)
	cid := uint32(1)
	channelType := uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE)

	enterRoomNotifyPerm := uint32(1)
	toUids := []uint32{2, 3, 4}

	// Mock getEnterRoomSubscribers to return a valid response
	mgrHelper.getGameTimeCli().EXPECT().GetUserGameTime(gomock.Any(), gomock.Any()).Return(&game_time.GetUserGameTimeResponse{
		Info: &game_time.GameTimeInfo{
			EnterRoomNotifyPerm: enterRoomNotifyPerm,
		},
	}, nil)
	mgrHelper.getGameTimeCli().EXPECT().GetUserEnterRoomNotifySubscribers(gomock.Any(), gomock.Any()).Return(&game_time.GetUserEnterRoomNotifySubscribersResponse{
		SubscriberUidList: toUids,
		Cursor:            "",
	}, nil)
	mgrHelper.getGameFreCli().EXPECT().BatGetFreCountBySources(gomock.Any(), gomock.Any()).Return(&game_fre_server.BatGetFreCountBySourcesResp{}, nil)

	// Mock preCheck to return true
	mgrHelper.getUkwCli().EXPECT().GetUKWInfo(gomock.Any(), gomock.Any()).Return(&youknowwho.UKWInfo{
		UkwPermissionInfo: &youknowwho.UKWPermissionInfo{
			Switch: youknowwho.UKWSwitchType_UKW_SWITCH_OFF,
		},
	}, nil)
	mgrHelper.getNobilityCli().EXPECT().GetNobilityInfo(gomock.Any(), uid, false).Return(&nobility_svr.NobilityInfo{}, nil)
	mgrHelper.getGnobilityCli().EXPECT().GetNobilitySwitchFlag(gomock.Any(), []uint32{uid}).Return(&gnobility.GetNobilitySwitchFlagResp{}, nil)
	mgrHelper.getSvipCli().EXPECT().GetUserSVIPPrivilegeProfile(gomock.Any(), uid).Return(&super_player_privilege.GetUserSVIPPrivilegeProfileResp{}, nil)

	// Mock filterEnterRoomNotifyPermUser to return the same user list
	mgrHelper.getFriendshipCli().EXPECT().BatchGetBiFollowingWithCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
		map[uint32]bool{2: true, 3: true}, map[uint32]bool{2: true, 3: true}, nil)

	// Mock filterNotInABTestUser to return an error
	mockErr := protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "some error")
	mgrHelper.getUserAuthHistoryCli().EXPECT().BatchGetUserLastLoginInfo(gomock.Any(), gomock.Any()).Return(nil, mockErr)
	//mgrHelper.getAbtestCli().EXPECT().BatchGetDeviceTestByArg(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, mockErr)

	// Execute the method
	mgrHelper.Handle(ctx, uid, cid, channelType)

	// We don't need to assert anything as the function should return early on error

	// Check if the debug log is printed
	log.DebugWithCtx(ctx, "EnterRoomNotifyMgr Handle success, uid:%d, cid:%d, toUids:%v", uid, cid, toUids)
}

func TestIsUserInvisibleWithNobilityInvisibleStatus(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a new helper for the test
	s := newMgrHelperForTest(t)

	ctx := context.Background()
	uid := uint32(12345)

	// Mock the nobility info to return invisible
	s.getNobilityCli().EXPECT().GetNobilityInfo(gomock.Any(), uid, false).Return(&nobility_svr.NobilityInfo{
		Invisible: true,
	}, nil)

	// Mock the SVIP privilege profile to return not invisible
	//s.getSvipCli().EXPECT().GetUserSVIPPrivilegeProfile(gomock.Any(), uid).Return(&super_player_privilege.GetUserSVIPPrivilegeProfileResp{
	//    PrivilegeProfile: &super_player_privilege.SVIPPrivilegeProfile{
	//        OnlineSwitch: super_player_privilege.OnlineSwitch_ENUM_ONLINE_SWITCH_ONLINE, // Not invisible
	//    },
	//}, nil)

	// Mock the gnobility switch flag to return no invisibility
	//s.getGnobilityCli().EXPECT().GetNobilitySwitchFlag(gomock.Any(), gomock.Eq([]uint32{uid})).Return(&gnobility.GetNobilitySwitchFlagResp{
	//    SwitchFlagList: []uint32{},
	//}, nil)

	// Call the function
	invisible, err := s.isUserInvisible(ctx, uid)

	// Assert the results
	assert.Nil(t, err)
	assert.True(t, invisible)
}

func TestIsUserInvisibleWithGnobilitySwitchOn(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	helper := newMgrHelperForTest(t)
	m := helper.EnterRoomNotifyMgr

	ctx := context.Background()
	uid := uint32(12345)

	// Mocking the GetUserSVIPPrivilegeProfile response
	//svipProfile := &super_player_privilege.GetUserSVIPPrivilegeProfileResp{
	//   PrivilegeProfile: &super_player_privilege.SVIPPrivilegeProfile{
	//       OnlineSwitch: super_player_privilege.OnlineSwitch_ENUM_ONLINE_SWITCH_ONLINE,
	//   },
	//}
	//helper.getSvipCli().EXPECT().GetUserSVIPPrivilegeProfile(gomock.Any(), uid).Return(svipProfile, nil)

	// Mocking the GetNobilityInfo response
	nobilityInfo := &nobility_svr.NobilityInfo{
		Invisible: false,
	}
	helper.getNobilityCli().EXPECT().GetNobilityInfo(gomock.Any(), uid, false).Return(nobilityInfo, nil)

	// Mocking the GetNobilitySwitchFlag response
	gnobilitySwitchFlagResp := &gnobility.GetNobilitySwitchFlagResp{
		SwitchFlagList: []uint32{uint32(gnobility.NobilitySwitch_E_ONLINE)},
	}
	helper.getGnobilityCli().EXPECT().GetNobilitySwitchFlag(gomock.Any(), []uint32{uid}).Return(gnobilitySwitchFlagResp, nil)

	// Calling the function
	result, err := m.isUserInvisible(ctx, uid)
	assert.NoError(t, err)
	assert.True(t, result)
}

func TestGetEnterRoomSubscribersWithNoSubscribers(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mgr := newMgrHelperForTest(t)

	ctx := context.Background()
	uid := uint32(1)

	// Mock response for GetUserGameTime
	gameTimeResp := &game_time.GetUserGameTimeResponse{
		Info: &game_time.GameTimeInfo{
			EnterRoomNotifyPerm: uint32(game_play_logic.EnterRoomNotifyPerm_ENTER_ROOM_NOTIFY_PERM_NOBODY),
		},
	}
	mgr.getGameTimeCli().EXPECT().GetUserGameTime(gomock.Any(), &game_time.GetUserGameTimeRequest{Uid: uid}).Return(gameTimeResp, nil)

	// Call the function
	enterRoomNotifyPerm, toUids, err := mgr.getEnterRoomSubscribers(ctx, uid)

	// Check the results
	assert.NoError(t, err)
	assert.Equal(t, uint32(game_play_logic.EnterRoomNotifyPerm_ENTER_ROOM_NOTIFY_PERM_NOBODY), enterRoomNotifyPerm)
	assert.Empty(t, toUids)
}

func TestFilterEnterRoomNotifyPermUserWithFans(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s := newMgrHelperForTest(t)

	ctx := context.Background()
	uid := uint32(1)
	toUids := []uint32{2, 3, 4}

	enterRoomNotifyPerm := uint32(game_play_logic.EnterRoomNotifyPerm_ENTER_ROOM_NOTIFY_PERM_FANS)

	userFollow := map[uint32]bool{
		2: true,
		3: false,
		4: true,
	}
	followUser := map[uint32]bool{
		2: true,
		3: true,
		4: false,
	}

	s.getFriendshipCli().EXPECT().BatchGetBiFollowingWithCache(gomock.Any(), uid, toUids, true, true).Return(userFollow, followUser, nil)

	result, err := s.filterEnterRoomNotifyPermUser(ctx, enterRoomNotifyPerm, uid, toUids)

	assert.NoError(t, err)
	expectedResult := []uint32{2, 3}
	assert.Equal(t, expectedResult, result)
}

/*
func TestHandleWithSendPushFailure(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    m := newMgrHelperForTest(t)

    ctx := context.Background()
    uid := uint32(123)
    cid := uint32(456)
    channelType := uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE)
    abCfg := config.GetInteractGuideMiddleConfig().GetEnterRoomPushConf().GetAbCfg()

    // Mock `getEnterRoomSubscribers`
    enterRoomNotifyPerm := uint32(1)
    toUids := []uint32{789, 101112}
    m.getGameTimeCli().EXPECT().GetUserGameTime(gomock.Any(), gomock.Any()).Return(&game_time.GetUserGameTimeResponse{
        Info: &game_time.GameTimeInfo{
            EnterRoomNotifyPerm: enterRoomNotifyPerm,
        },
    }, nil)
    m.getGameTimeCli().EXPECT().GetUserEnterRoomNotifySubscribers(gomock.Any(), gomock.Any()).Return(&game_time.GetUserEnterRoomNotifySubscribersResponse{
        SubscriberUidList: toUids,
        Cursor:            "",
    }, nil)
    m.getGameFreCli().EXPECT().BatGetFreCountBySources(gomock.Any(), gomock.Any()).Return(&game_fre_server.BatGetFreCountBySourcesResp{}, nil)

    // Mock `preCheck`
    m.getUkwCli().EXPECT().GetUKWInfo(gomock.Any(), gomock.Any()).Return(&youknowwho.UKWInfo{}, nil)
    m.getSvipCli().EXPECT().GetUserSVIPPrivilegeProfile(gomock.Any(), gomock.Any()).Return(&super_player_privilege.GetUserSVIPPrivilegeProfileResp{
        PrivilegeProfile: &super_player_privilege.SVIPPrivilegeProfile{
            OnlineSwitch: super_player_privilege.OnlineSwitch_ENUM_ONLINE_SWITCH_ONLINE,
        },
    }, nil)
    m.getNobilityCli().EXPECT().GetNobilityInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&nobility_svr.NobilityInfo{}, nil)
    m.getGnobilityCli().EXPECT().GetNobilitySwitchFlag(gomock.Any(), gomock.Any()).Return(&gnobility.GetNobilitySwitchFlagResp{}, nil)

    // Mock `filterEnterRoomNotifyPermUser`
    m.getFriendshipCli().EXPECT().BatchGetBiFollowingWithCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]bool{789: true, 101112: false}, map[uint32]bool{789: true, 101112: true}, nil)

    // Mock `filterNotInABTestUser`
    m.getUserAuthHistoryCli().EXPECT().BatchGetUserLastLoginInfo(gomock.Any(), gomock.Any()).Return([]*user_auth_history.UserLoginInfo{
        {Uid: 789, DeviceId: "device_1", ClientType: 0},
        {Uid: 101112, DeviceId: "device_2", ClientType: 0},
    }, nil)
    m.getAbtestCli().EXPECT().BatchGetDeviceTestByArg(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[string]string{"device_1": abCfg.ExpGroup}, nil)

    // Mock `filterInSameRoomUser`
    m.getChannelOlCli().EXPECT().BatchGetUserChannelId(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]uint32{789: 123}, nil)

    // Mock `recordPushFreLimit`
    m.getGameFreCli().EXPECT().BatIncFreCountBySources(gomock.Any(), gomock.Any()).Return(&game_fre_server.BatIncFreCountBySourcesResp{}, nil)

    // Mock `sendPush` with a failure
    m.getAccountCli().EXPECT().GetUserByUid(gomock.Any(), gomock.Any()).Return(&account_go_pb.UserResp{
        Uid:      proto.Uint32(123),
        Username: proto.String("test_user"),
        Nickname: proto.String("TestUser"),
    }, nil)
    m.getPresenceCli().EXPECT().BatchGetUserPres(gomock.Any(), gomock.Any()).Return(map[uint32]*presence_svr.PresInfoList{}, nil)
    m.getMsgPusher().EXPECT().OfflineOpPush(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("push error"))

    m.Handle(ctx, uid, cid, channelType)
}
*/
/*
func TestHandleWithValidInput(t *testing.T) {
    controller := gomock.NewController(t)
    defer controller.Finish()

    // Create a new EnterRoomNotifyMgr using the helper.
    mgrHelper := newMgrHelperForTest(t)
    mgr := mgrHelper.EnterRoomNotifyMgr

    ctx := context.Background()
    uid := uint32(1)
    cid := uint32(100)
    channelType := uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE)
    abCfg := config.GetInteractGuideMiddleConfig().GetEnterRoomPushConf().GetAbCfg()

    // Mock responses for getEnterRoomSubscribers
    enterRoomNotifyPerm := uint32(1)
    toUids := []uint32{2, 3, 4}

    mgrHelper.getGameTimeCli().EXPECT().GetUserGameTime(gomock.Any(), gomock.Any()).Return(&game_time.GetUserGameTimeResponse{Info: &game_time.GameTimeInfo{EnterRoomNotifyPerm: enterRoomNotifyPerm}}, nil)
    mgrHelper.getGameTimeCli().EXPECT().GetUserEnterRoomNotifySubscribers(gomock.Any(), gomock.Any()).Return(&game_time.GetUserEnterRoomNotifySubscribersResponse{SubscriberUidList: toUids, Cursor: ""}, nil).AnyTimes()
    mgrHelper.getGameFreCli().EXPECT().BatGetFreCountBySources(gomock.Any(), gomock.Any()).Return(&game_fre_server.BatGetFreCountBySourcesResp{}, nil)

    // Mock responses for preCheck
    mgrHelper.getSvipCli().EXPECT().GetUserSVIPPrivilegeProfile(gomock.Any(), uid).Return(&super_player_privilege.GetUserSVIPPrivilegeProfileResp{PrivilegeProfile: &super_player_privilege.SVIPPrivilegeProfile{OnlineSwitch: super_player_privilege.OnlineSwitch_ENUM_ONLINE_SWITCH_ONLINE}}, nil)
    mgrHelper.getNobilityCli().EXPECT().GetNobilityInfo(gomock.Any(), uid, false).Return(&nobility_svr.NobilityInfo{Invisible: false}, nil)
    mgrHelper.getGnobilityCli().EXPECT().GetNobilitySwitchFlag(gomock.Any(), []uint32{uid}).Return(&gnobility.GetNobilitySwitchFlagResp{SwitchFlagList: []uint32{0}}, nil)
    mgrHelper.getUkwCli().EXPECT().GetUKWInfo(gomock.Any(), uid).Return(&youknowwho.UKWInfo{UkwPermissionInfo: &youknowwho.UKWPermissionInfo{Switch: youknowwho.UKWSwitchType_UKW_SWITCH_OFF}}, nil)

    // Mock responses for filterEnterRoomNotifyPermUser
    mgrHelper.getFriendshipCli().EXPECT().BatchGetBiFollowingWithCache(gomock.Any(), uid, toUids, true, true).Return(map[uint32]bool{2: true, 3: true, 4: true}, map[uint32]bool{2: true, 3: true, 4: true}, nil)

    // Mock responses for filterNotInABTestUser
    mgrHelper.getUserAuthHistoryCli().EXPECT().BatchGetUserLastLoginInfo(gomock.Any(), gomock.Any()).Return([]*user_auth_history.UserLoginInfo{
        {
            Uid:        2,
            DeviceId:   "device1",
            ClientType: 0,
        }, {
            Uid:        3,
            DeviceId:   "device2",
            ClientType: 0,
        }, {
            Uid:        4,
            DeviceId:   "device3",
            ClientType: 0,
        },
    }, nil).AnyTimes()
    mgrHelper.getAbtestCli().EXPECT().BatchGetDeviceTestByArg(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[string]string{"device1": abCfg.ExpGroup, "device2": abCfg.ExpGroup, "device3": abCfg.ExpGroup}, nil).AnyTimes()

    // Mock responses for filterInSameRoomUser
    mgrHelper.getChannelOlCli().EXPECT().BatchGetUserChannelId(gomock.Any(), uid, gomock.Any()).Return(map[uint32]uint32{3: cid}, nil).AnyTimes()

    // Mock responses for recordPushFreLimit
    mgrHelper.getGameFreCli().EXPECT().BatIncFreCountBySources(gomock.Any(), gomock.Any()).Return(&game_fre_server.BatIncFreCountBySourcesResp{}, nil)

    // Mock responses for sendPush
    userResp := &account_go_pb.UserResp{
        Uid:      &uid,
        Username: proto.String("username"),
        Nickname: proto.String("nickname"),
    }
    mgrHelper.getAccountCli().EXPECT().GetUserByUid(gomock.Any(), uid).Return(userResp, nil)
    mgrHelper.getPresenceCli().EXPECT().BatchGetUserPres(gomock.Any(), gomock.Any()).Return(map[uint32]*presence_svr.PresInfoList{4: {}}, nil).AnyTimes()
    mgrHelper.getMsgPusher().EXPECT().OnlinePush(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
    mgrHelper.getMsgPusher().EXPECT().OfflineOpPush(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

    mgr.Handle(ctx, uid, cid, channelType)

    // Additional assertions can be added as needed
}
*/
func TestGetEnterRoomSubscribersWithValidInput(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s := newMgrHelperForTest(t)

	ctx := context.Background()
	uid := uint32(1)
	gameTimeInfo := &game_time.GetUserGameTimeResponse{
		Info: &game_time.GameTimeInfo{
			EnterRoomNotifyPerm: uint32(game_play_logic.EnterRoomNotifyPerm_ENTER_ROOM_NOTIFY_PERM_PLAYMATES),
		},
	}
	s.getGameTimeCli().EXPECT().GetUserGameTime(gomock.Any(), &game_time.GetUserGameTimeRequest{Uid: uid}).Return(gameTimeInfo, nil)

	subscriberResp := &game_time.GetUserEnterRoomNotifySubscribersResponse{
		SubscriberUidList: []uint32{2, 3, 4},
		Cursor:            "",
	}
	s.getGameTimeCli().EXPECT().GetUserEnterRoomNotifySubscribers(gomock.Any(), gomock.Any()).Return(subscriberResp, nil)
	s.getGameFreCli().EXPECT().BatGetFreCountBySources(gomock.Any(), gomock.Any()).Return(&game_fre_server.BatGetFreCountBySourcesResp{
		ResMap: map[string]uint32{"4_9_1": 2},
	}, nil)

	filteredUids := []uint32{2, 3}
	//s.EXPECT().filterFreLimitUser(gomock.Any(), uid, gomock.Any()).Return(filteredUids, nil)

	enterRoomNotifyPerm, toUids, err := s.getEnterRoomSubscribers(ctx, uid)

	assert.NoError(t, err)
	assert.Equal(t, gameTimeInfo.Info.GetEnterRoomNotifyPerm(), enterRoomNotifyPerm)
	assert.Equal(t, filteredUids, toUids)
}

/*
	func (m *mgrHelperForTest) filterFreLimitUser(ctx context.Context, uid uint32, toUids []uint32) ([]uint32, error) {
	    return toUids, nil
	}
*/
func TestFilterEnterRoomNotifyPermUserWithPlaymates(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	helper := newMgrHelperForTest(t)
	mgr := helper.EnterRoomNotifyMgr

	ctx := context.Background()
	uid := uint32(1)
	toUids := []uint32{2, 3, 4}
	enterRoomNotifyPerm := uint32(game_play_logic.EnterRoomNotifyPerm_ENTER_ROOM_NOTIFY_PERM_PLAYMATES)

	userFollow := map[uint32]bool{
		2: true,
		3: true,
		4: false,
	}
	followUser := map[uint32]bool{
		2: true,
		3: false,
		4: true,
	}

	helper.getFriendshipCli().EXPECT().BatchGetBiFollowingWithCache(ctx, uid, toUids, true, true).Return(userFollow, followUser, nil)

	result, err := mgr.filterEnterRoomNotifyPermUser(ctx, enterRoomNotifyPerm, uid, toUids)
	assert.NoError(t, err)
	assert.Equal(t, []uint32{2}, result)
}

/*
	func TestFilterNotInABTestUserWithValidInput(t *testing.T) {
	    ctrl := gomock.NewController(t)
	    defer ctrl.Finish()

	    m := newMgrHelperForTest(t)

	    ctx := context.Background()
	    uid := uint32(1)
	    toUids := []uint32{2, 3, 4}

	    loginInfos := []*user_auth_history.UserLoginInfo{
	        {Uid: 2, DeviceId: "device1", ClientType: 0},
	        {Uid: 3, DeviceId: "device2", ClientType: 0},
	        {Uid: 4, DeviceId: "device3", ClientType: 0},
	    }

	    m.getUserAuthHistoryCli().EXPECT().BatchGetUserLastLoginInfo(ctx, gomock.Any()).Return(loginInfos, nil)

	    expectedDeviceIds := []string{"device1", "device2", "device3"}

	    abCfg := config.GetInteractGuideMiddleConfig().GetEnterRoomPushConf().GetAbCfg()
	    expectedResultMap := map[string]string{
	        "device1": abCfg.ExpGroup,
	        "device2": abCfg.ExpGroup,
	        "device3": "",
	    }
	    m.getAbtestCli().EXPECT().BatchGetDeviceTestByArg(ctx, expectedDeviceIds, abCfg.ArgName).Return(expectedResultMap, nil)

	    result, err := m.filterNotInABTestUser(ctx, uid, toUids)
	    assert.NoError(t, err)
	    assert.ElementsMatch(t, []uint32{2, 3}, result)
	}
*/
func TestGetEnterRoomSubscribersWithFreLimitExceeded(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	m := newMgrHelperForTest(t)

	ctx := context.Background()
	uid := uint32(1)
	limit := config.GetInteractGuideMiddleConfig().GetEnterRoomPushConf().GetHandleUserLimit()

	// Mock configuration to set the handle user limit and warning handle user limit

	// Mock the response from GetUserGameTime
	gameTimeResp := &game_time.GetUserGameTimeResponse{
		Info: &game_time.GameTimeInfo{
			EnterRoomNotifyPerm: uint32(game_play_logic.EnterRoomNotifyPerm_ENTER_ROOM_NOTIFY_PERM_PLAYMATES),
		},
	}
	m.getGameTimeCli().EXPECT().GetUserGameTime(ctx, &game_time.GetUserGameTimeRequest{Uid: uid}).Return(gameTimeResp, nil)

	// Mock the user list for GetUserEnterRoomNotifySubscribers
	userList := []uint32{2, 3, 4, 5}
	cursor := "cursor"

	subscriberResp := &game_time.GetUserEnterRoomNotifySubscribersResponse{
		SubscriberUidList: userList,
		Cursor:            cursor,
	}
	m.getGameTimeCli().EXPECT().GetUserEnterRoomNotifySubscribers(ctx, &game_time.GetUserEnterRoomNotifySubscribersRequest{Uid: uid, Cursor: "", Limit: limit}).Return(subscriberResp, nil)

	// Mock the frequency limit filtering
	filteredUserList := []uint32{2, 3, 4}
	m.getGameFreCli().EXPECT().BatGetFreCountBySources(ctx, gomock.Any()).Return(&game_fre_server.BatGetFreCountBySourcesResp{
		ResMap: map[string]uint32{
			"2_9_1": 1,
			"3_9_1": 1,
			"4_9_1": 1,
			"5_9_1": 6, // This user exceeds the limit
		},
	}, nil)
	//m.getGameFreCli().EXPECT().BatIncFreCountBySources(ctx, gomock.Any()).Return(&game_fre_server.BatIncFreCountBySourcesResp{}, nil)

	// Execute the function
	enterRoomNotifyPerm, result, err := m.getEnterRoomSubscribers(ctx, uid)

	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, uint32(game_play_logic.EnterRoomNotifyPerm_ENTER_ROOM_NOTIFY_PERM_PLAYMATES), enterRoomNotifyPerm)
	assert.Equal(t, filteredUserList, result)
}

func TestFilterInSameRoomUserWithNoSameRoomUsers(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s := newMgrHelperForTest(t)

	ctx := context.Background()

	uid := uint32(1)
	cid := uint32(100)
	toUids := []uint32{2, 3, 4}

	s.getChannelOlCli().EXPECT().BatchGetUserChannelId(gomock.Any(), uid, gomock.Any()).DoAndReturn(
		func(ctx context.Context, uid uint32, uids ...uint32) (map[uint32]uint32, protocol.ServerError) {
			resp := make(map[uint32]uint32)
			for _, id := range uids {
				resp[id] = id + 100 // Assigning different channel ids to ensure no user is in the same room
			}
			return resp, nil
		}).Times(1)

	result, err := s.filterInSameRoomUser(ctx, uid, cid, toUids)

	if err != nil {
		t.Fatalf("expected no error, but got %v", err)
	}

	expected := toUids
	if !reflect.DeepEqual(result, expected) {
		t.Errorf("expected %v, but got %v", expected, result)
	}
}

func TestFilterInSameRoomUserWithValidInput(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mgr := newMgrHelperForTest(t)

	ctx := context.Background()
	uid := uint32(1)
	cid := uint32(100)
	toUids := []uint32{2, 3, 4, 5}

	// Mock the expected behavior
	expectedUserChannelMap := map[uint32]uint32{
		2: 101,
		3: 100,
		4: 102,
		5: 100,
	}
	mgr.getChannelOlCli().EXPECT().BatchGetUserChannelId(gomock.Any(), uid, gomock.Any()).Return(expectedUserChannelMap, nil)

	// Call the function
	result, err := mgr.filterInSameRoomUser(ctx, uid, cid, toUids)

	// Verify the results
	expectedResult := []uint32{2, 4}
	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)
}

func TestRecordPushFreLimitWithNoUsers(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mgrHelper := newMgrHelperForTest(t)
	m := mgrHelper.EnterRoomNotifyMgr

	ctx := context.Background()
	uid := uint32(1)
	var toUids []uint32 // No users

	err := m.recordPushFreLimit(ctx, uid, toUids)

	// The error should be nil since there are no users to process
	assert.NoError(t, err)
}

func TestRecordPushFreLimitWithValidInput(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mgrHelper := newMgrHelperForTest(t)

	ctx := context.Background()
	uid := uint32(1001)
	toUids := []uint32{2001, 2002, 2003}

	mockGameFreCli := mgrHelper.getGameFreCli()
	gameFreResp := &game_fre_server.BatIncFreCountBySourcesResp{}

	mockGameFreCli.EXPECT().BatIncFreCountBySources(gomock.Any(), gomock.Any()).Return(gameFreResp, nil)

	err := mgrHelper.recordPushFreLimit(ctx, uid, toUids)

	assert.NoError(t, err)
}

func TestSendPushWithNoOnlineUsers(t *testing.T) {
	// Create a new mock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a new instance of the manager helper for test
	mgr := newMgrHelperForTest(t)

	// Mock context
	ctx := context.Background()
	uid := uint32(1)
	toUids := []uint32{2, 3, 4}

	// Mock the GetUserByUid call
	userInfo := &account_go_pb.UserResp{
		Uid:      &uid,
		Username: proto.String("testuser"),
		Nickname: proto.String("TestUser"),
	}
	mgr.getAccountCli().EXPECT().GetUserByUid(gomock.Any(), uid).Return(userInfo, nil)

	// Mock the BatchGetUserPres call to simulate no online users
	presenceMap := make(map[uint32]*pb.PresInfoList)
	mgr.getPresenceCli().EXPECT().BatchGetUserPres(gomock.Any(), toUids).Return(presenceMap, nil)

	// Mock the OfflineOpPush call
	mgr.getMsgPusher().EXPECT().OfflineOpPush(gomock.Any(), toUids, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

	// Call the actual method
	err := mgr.EnterRoomNotifyMgr.sendPush(ctx, uid, toUids)

	// Assert the results
	assert.NoError(t, err)
}

/*
func TestEnterRoomNotifyMgr_FilterNotInABTestUser_WithNoMatch(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    mgrHelper := newMgrHelperForTest(t)
    mgr := mgrHelper.EnterRoomNotifyMgr

    ctx := context.Background()
    uid := uint32(1)
    toUids := []uint32{2, 3, 4}
    uint64Uids := []uint64{2, 3, 4}

    mgrHelper.getUserAuthHistoryCli().EXPECT().BatchGetUserLastLoginInfo(ctx, gomock.Any()).Return([]*user_auth_history.UserLoginInfo{
        {
            Uid:        uint64Uids[0],
            DeviceId:   "device_1",
            ClientType: 0,
        },
        {
            Uid:        uint64Uids[1],
            DeviceId:   "device_2",
            ClientType: 0,
        },
        {
            Uid:        uint64Uids[2],
            DeviceId:   "device_3",
            ClientType: 0,
        },
    }, nil)

    mgrHelper.getAbtestCli().EXPECT().BatchGetDeviceTestByArg(ctx, gomock.Any(), gomock.Any()).Return(map[string]string{
        "device_1": "control",
        "device_2": "control",
        "device_3": "control",
    }, nil)

    result, err := mgr.filterNotInABTestUser(ctx, uid, toUids)

    if err != nil {
        t.Fatalf("expected no error, got %v", err)
    }
    if len(result) != 0 {
        t.Fatalf("expected no users to match, got %v", result)
    }
}
*/
/*
func TestSendPushWithValidInput(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    m := newMgrHelperForTest(t)

    ctx := context.Background()
    uid := uint32(1)
    cid := uint32(1)
    channelType := uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE)

    // Mock getEnterRoomSubscribers
    enterRoomNotifyPerm, toUids := uint32(1), []uint32{2, 3}
    m.getGameTimeCli().EXPECT().GetUserGameTime(ctx, &game_time.GetUserGameTimeRequest{Uid: uid}).Return(&game_time.GetUserGameTimeResponse{
        Info: &game_time.GameTimeInfo{EnterRoomNotifyPerm: enterRoomNotifyPerm},
    }, nil)
    m.getGameTimeCli().EXPECT().GetUserEnterRoomNotifySubscribers(ctx, &game_time.GetUserEnterRoomNotifySubscribersRequest{Uid: uid, Cursor: "", Limit: 100}).Return(&game_time.GetUserEnterRoomNotifySubscribersResponse{
        SubscriberUidList: toUids,
        Cursor:            "",
    }, nil)
    m.getGameFreCli().EXPECT().BatGetFreCountBySources(ctx, gomock.Any()).Return(&game_fre_server.BatGetFreCountBySourcesResp{}, nil)

    // Mock preCheck
    m.getUkwCli().EXPECT().GetUKWInfo(ctx, uid).Return(&youknowwho.UKWInfo{
        UkwPermissionInfo: &youknowwho.UKWPermissionInfo{
            Switch: youknowwho.UKWSwitchType_UKW_SWITCH_OFF,
        },
    }, nil)
    m.getNobilityCli().EXPECT().GetNobilityInfo(ctx, uid, false).Return(&nobility_svr.NobilityInfo{}, nil)
    m.getGnobilityCli().EXPECT().GetNobilitySwitchFlag(ctx, []uint32{uid}).Return(&gnobility.GetNobilitySwitchFlagResp{}, nil)
    m.getSvipCli().EXPECT().GetUserSVIPPrivilegeProfile(ctx, uid).Return(&super_player_privilege.GetUserSVIPPrivilegeProfileResp{}, nil)

    // Mock filterEnterRoomNotifyPermUser
    m.getFriendshipCli().EXPECT().BatchGetBiFollowingWithCache(ctx, uid, toUids, true, true).Return(map[uint32]bool{2: true}, map[uint32]bool{2: true}, nil)

    // Mock filterNotInABTestUser
    m.getUserAuthHistoryCli().EXPECT().BatchGetUserLastLoginInfo(ctx, gomock.Any()).Return([]*user_auth_history.UserLoginInfo{}, nil)
    //m.getAbtestCli().EXPECT().BatchGetDeviceTestByArg(ctx, gomock.Any(), gomock.Any()).Return(map[string]string{}, nil)

    // Mock filterInSameRoomUser
    //m.getChannelOlCli().EXPECT().BatchGetUserChannelId(ctx, uid, toUids).Return(map[uint32]uint32{2: 2, 3: 3}, nil)

    // Mock recordPushFreLimit
    //m.getGameFreCli().EXPECT().BatIncFreCountBySources(ctx, gomock.Any()).Return(&game_fre_server.BatIncFreCountBySourcesResp{}, nil)

    // Mock sendPush
    //userInfo := &account_go_pb.UserResp{
    //    Uid:      &uid,
    //    Username: proto.String("test_user"),
    //    Nickname: proto.String("test_nickname"),
    //}
    //m.getAccountCli().EXPECT().GetUserByUid(ctx, uid).Return(userInfo, nil)
    //m.getPresenceCli().EXPECT().BatchGetUserPres(ctx, toUids).Return(map[uint32]*pb.PresInfoList{}, nil)
    //m.getMsgPusher().EXPECT().OnlinePush(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
    //m.getMsgPusher().EXPECT().OfflineOpPush(ctx, toUids, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

    m.Handle(ctx, uid, cid, channelType)
}
*/

func TestGetAbTestMatchMap_ErrorFromBatchGet(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s := newMgrHelperForTest(t)

	ctx := context.Background()
	deviceIds := []string{"device1", "device2", "device3"}
	argKey := "testArgKey"
	//expectArgVal := "expectedValue"

	mockErr := errors.New("mock error")

	s.getAbtestCli().EXPECT().BatchGetDeviceTestByArg(ctx, deviceIds[:min(len(deviceIds), queryAbTestLimit)], argKey).Return(nil, mockErr)

	matchMap, err := s.getAbtestCli().BatchGetDeviceTestByArg(ctx, deviceIds[:min(len(deviceIds), queryAbTestLimit)], argKey)

	assert.Nil(t, matchMap)
	assert.Equal(t, mockErr, err)
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func TestGetAbTestMatchMap_EmptyDeviceIds(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s := newMgrHelperForTest(t)

	ctx := context.Background()
	uid := uint32(1)
	deviceIds := []string{}
	argKey := "testArgKey"
	expectArgVal := "expectValue"

	// No need to mock BatchGetDeviceTestByArg as it should not be called with empty deviceIds list
	out, err := s.getEnterRoomNotifyMgr().getAbTestMatchMap(ctx, uid, deviceIds, nil, argKey, expectArgVal)

	assert.Nil(t, err)
	assert.Equal(t, map[uint32]bool{}, out)
}

func (receiver *mgrHelperForTest) getEnterRoomNotifyMgr() *EnterRoomNotifyMgr {
	return receiver.EnterRoomNotifyMgr
}

func TestGetUsersMap_ValidUIDs(t *testing.T) {
	ctx := context.Background()
	uids := []uint32{1, 2, 3}
	mgrHelper := newMgrHelperForTest(t)
	mockAccountClient := mgrHelper.getAccountCli()

	mockAccountClient.EXPECT().GetUsersByUids(ctx, uids).Return(&account_go_pb.UsersResp{
		UserList: []*account_go_pb.UserResp{
			{Uid: proto.Uint32(1), Nickname: proto.String("User1")},
			{Uid: proto.Uint32(2), Nickname: proto.String("User2")},
			{Uid: proto.Uint32(3), Nickname: proto.String("User3")},
		},
	}, nil)

	userMap, err := mgrHelper.EnterRoomNotifyMgr.getUsersMap(ctx, uids)
	assert.NoError(t, err)
	assert.Equal(t, 3, len(userMap))
	assert.Equal(t, "User1", *userMap[1].Nickname)
	assert.Equal(t, "User2", *userMap[2].Nickname)
	assert.Equal(t, "User3", *userMap[3].Nickname)
}

func TestGetUsersMap_GetUsersByUidsError(t *testing.T) {
	ctx := context.Background()
	uids := []uint32{1, 2, 3}
	mgrHelper := newMgrHelperForTest(t)
	mockAccountClient := mgrHelper.getAccountCli()

	mockErr := terrors.NewExactServerError(nil, status.ErrRequestParamInvalid, "some error")

	mockAccountClient.EXPECT().GetUsersByUids(ctx, uids).Return(nil, mockErr)

	userMap, err := mgrHelper.EnterRoomNotifyMgr.getUsersMap(ctx, uids)
	assert.Error(t, err)
	assert.Nil(t, userMap)
}

func TestGetUsersMap_EmptyUIDs(t *testing.T) {
	ctx := context.Background()
	uids := []uint32{}
	mgrHelper := newMgrHelperForTest(t)

	userMap, err := mgrHelper.EnterRoomNotifyMgr.getUsersMap(ctx, uids)
	assert.NoError(t, err)
	assert.Equal(t, 0, len(userMap))
}

func TestGetUsersMap_ExceedQueryAccountLimit(t *testing.T) {
	ctx := context.Background()
	uids := make([]uint32, queryAccountLimit+1)
	for i := 0; i < queryAccountLimit+1; i++ {
		uids[i] = uint32(i + 1)
	}
	mgrHelper := newMgrHelperForTest(t)
	mockAccountClient := mgrHelper.getAccountCli()
	userList1 := make([]*account_go_pb.UserResp, queryAccountLimit)
	for i := 0; i < queryAccountLimit; i++ {
		userList1[i] = &account_go_pb.UserResp{Uid: proto.Uint32(uint32(i + 1))}
	}
	userList2 := []*account_go_pb.UserResp{{Uid: proto.Uint32(uint32(queryAccountLimit + 1))}}

	mockAccountClient.EXPECT().GetUsersByUids(ctx, uids[:queryAccountLimit]).Return(&account_go_pb.UsersResp{
		UserList: userList1,
	}, nil)
	mockAccountClient.EXPECT().GetUsersByUids(ctx, uids[queryAccountLimit:]).Return(&account_go_pb.UsersResp{
		UserList: userList2,
	}, nil)

	userMap, err := mgrHelper.EnterRoomNotifyMgr.getUsersMap(ctx, uids)
	assert.NoError(t, err)
	assert.Equal(t, queryAccountLimit+1, len(userMap))
}
