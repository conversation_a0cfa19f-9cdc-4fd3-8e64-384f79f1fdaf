package enter_room_notify

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/device_id"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/marketid_helper"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/protocol"
	bizLabel "gitlab.ttyuyin.com/bizFund/bizFund/pkg/push-notification"
	"golang.52tt.com/clients/account-go"
	"golang.52tt.com/clients/channelol"
	device_info "golang.52tt.com/clients/datacenter/device-info-service"
	"golang.52tt.com/clients/gnobility"
	"golang.52tt.com/clients/nobility"
	presence "golang.52tt.com/clients/presence/v2"
	super_player_privilege "golang.52tt.com/clients/super-player-privilege"
	"golang.52tt.com/clients/ugc/friendship"
	"golang.52tt.com/clients/user-auth-history"
	ukw "golang.52tt.com/clients/you-know-who"
	"golang.52tt.com/pkg/abtest"
	"golang.52tt.com/pkg/monkey-send-chat/monkey_sender"
	"golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/app/game-play-logic"
	gapush "golang.52tt.com/protocol/app/push"
	device_info_pb "golang.52tt.com/protocol/datacenter/device-info-service"
	"golang.52tt.com/protocol/services/game-fre-server"
	"golang.52tt.com/protocol/services/game-time/game-time"
	gnobility_pb "golang.52tt.com/protocol/services/gnobility"
	svip_pb "golang.52tt.com/protocol/services/super-player-privilege"
	"golang.52tt.com/protocol/services/youknowwho"
	config "golang.52tt.com/services/interact-guide-middle/internal/config/ttconfig/interact_guide_middle"
	"golang.52tt.com/services/interact-guide-middle/internal/mgr/notify"
	"strconv"
	"time"
)

type EnterRoomNotifyMgr struct {
	accountCli         account_go.IClient
	friendshipCli      friendship.IClient
	userAuthHistoryCli user_auth_history.IClient
	presenceCli        presence.IClient
	ukwCli             ukw.IClient
	svipCli            super_player_privilege.IClient
	nobilityCli        nobility.IClient
	gnobilityCli       gnobility.IClient
	abtestCli          abtest.IABTestClient
	channelOlCli       channelol.IClient
	gameFreCli         game_fre_server.GameFreServerClient
	gameTimeCli        game_time.GameTimeClient
	msgPusher          notify.IMsgPusher
	deviceInfoCli      device_info.IClient
}

func NewEnterRoomNotifyMgr(accountCli *account_go.Client, friendshipCli *friendship.Client, userAuthHistoryCli *user_auth_history.Client, presenceCli *presence.Client,
	ukwCli *ukw.Client, svipCli *super_player_privilege.Client, nobilityCli *nobility.Client, gnobilityCli *gnobility.Client, abtestCli *abtest.ABTestClient,
	channelOlCli channelol.IClient, gameFreCli *game_fre_server.Client, gameTimeCli *game_time.Client, msgPusher *notify.MsgPusher, deviceInfoCli *device_info.Client) *EnterRoomNotifyMgr {
	return &EnterRoomNotifyMgr{
		accountCli:         accountCli,
		friendshipCli:      friendshipCli,
		userAuthHistoryCli: userAuthHistoryCli,
		presenceCli:        presenceCli,
		ukwCli:             ukwCli,
		svipCli:            svipCli,
		nobilityCli:        nobilityCli,
		gnobilityCli:       gnobilityCli,
		abtestCli:          abtestCli,
		channelOlCli:       channelOlCli,
		gameFreCli:         gameFreCli,
		gameTimeCli:        gameTimeCli,
		msgPusher:          msgPusher,
		deviceInfoCli:      deviceInfoCli,
	}
}

const (
	queryUserChannelLimit = 100  // 单次查询用户在房信息数量限制
	queryAccountLimit     = 100  // 单次查询用户信息数量限制
	queryAbTestLimit      = 200  // 单次查询ab实验数量限制
	pushUserLimit         = 1000 // 单次推送用户数量限制
	enterRoomPushJumpUrl  = "tt://m.52tt.com/follow_friend_channel?uid=%d&account=%s"
)

func (m *EnterRoomNotifyMgr) Handle(ctx context.Context, uid, cid, channelType uint32) {
	start := time.Now()
	// 获取订阅了当前用户进房提醒的用户列表，如果没有订阅用户，直接返回
	enterRoomNotifyPerm, toUids, err := m.getEnterRoomSubscribers(ctx, uid)
	if err != nil || len(toUids) == 0 {
		return
	}

	// 前置校验，是否需要触发当前用户进房提醒
	if ok, err := m.preCheck(ctx, uid, channelType); !ok || err != nil {
		return
	}

	// 过滤不符合进房提醒权限条件的用户
	toUids, err = m.filterEnterRoomNotifyPermUser(ctx, enterRoomNotifyPerm, uid, toUids)
	if err != nil {
		return
	}

	// 过滤不在实验组的用户
	toUids, err = m.filterNotInABTestUser(ctx, uid, toUids)
	if err != nil {
		return
	}

	// 过滤跟当前用户在同一房间的用户, 被过滤的用户也需要记录频率限制
	pushUids, err := m.filterInSameRoomUser(ctx, uid, cid, toUids)
	if err != nil {
		return
	}

	// 下发推送
	err = m.sendPush(ctx, uid, pushUids)
	if err != nil {
		return
	}

	// 记录用户下发通知频率
	_ = m.recordPushFreLimit(ctx, uid, toUids)

	log.DebugWithCtx(ctx, "EnterRoomNotifyMgr Handle success, uid:%d, cid:%d, toUids:%v", uid, cid, toUids)
	log.InfoWithCtx(ctx, "EnterRoomNotifyMgr Handle success, uid:%d, cid:%d, len(toUids):%d, cost:%dms", uid, cid, len(toUids), time.Since(start).Milliseconds())
	return
}

// 前置校验，是否需要触发当前用户进房提醒
func (m *EnterRoomNotifyMgr) preCheck(ctx context.Context, uid, channelType uint32) (bool, error) {
	// 是否开启了神秘人状态, pgc公会公开房和语音直播房才生效
	if channelType == uint32(channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) || channelType == uint32(channel.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		ukwInfo, err := m.ukwCli.GetUKWInfo(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr ukwCli.GetUKWInfo failed, err:%v, uid:%d", err, uid)
			return false, err
		}
		if ukwInfo.GetUkwPermissionInfo().GetSwitch() == youknowwho.UKWSwitchType_UKW_SWITCH_ON {
			log.InfoWithCtx(ctx, "EnterRoomNotifyMgr user is in you_know_who status, uid:%d", uid)
			return false, nil
		}
	}

	// 是否隐身
	if invisible, err := m.isUserInvisible(ctx, uid); invisible || err != nil {
		return false, err
	}

	return true, nil
}

// 检查用户隐身状态
func (m *EnterRoomNotifyMgr) isUserInvisible(ctx context.Context, uid uint32) (bool, error) {
	// 贵族进房隐身
	nobilityInfo, err := m.nobilityCli.GetNobilityInfo(ctx, uid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr nobilityCli.GetNobilityInfo failed, err:%v, uid:%d", err, uid)
		return false, err
	}
	if nobilityInfo.GetInvisible() {
		log.InfoWithCtx(ctx, "EnterRoomNotifyMgr user is in enter_room invisible status, uid:%d", uid)
		return true, nil
	}

	// 贵族在线隐身
	nobilitySwitch, err := m.gnobilityCli.GetNobilitySwitchFlag(ctx, []uint32{uid})
	if err != nil {
		log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr gnobilityCli.GetNobilitySwitchFlag failed, err:%v, uid:%d", err, uid)
		return false, err
	}
	if len(nobilitySwitch.GetSwitchFlagList()) == 1 {
		flag := nobilitySwitch.GetSwitchFlagList()[0]
		if flag&uint32(gnobility_pb.NobilitySwitch_E_ONLINE) > 0 {
			log.InfoWithCtx(ctx, "EnterRoomNotifyMgr user is in gnobility online invisible status, uid:%d", uid)
			return true, nil
		}
	}

	// svip在线隐身
	svipProfile, err := m.svipCli.GetUserSVIPPrivilegeProfile(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr svipCli.GetUserSVIPPrivilegeProfile failed, err:%v, uid:%d", err, uid)
		return false, err
	}
	if svipProfile.GetPrivilegeProfile().GetOnlineSwitch() == svip_pb.OnlineSwitch_ENUM_ONLINE_SWITCH_STEALTH {
		log.InfoWithCtx(ctx, "EnterRoomNotifyMgr user is in svip online invisible status, uid:%d", uid)
		return true, nil
	}

	return false, nil
}

// 获取订阅了当前用户进房提醒的用户列表
func (m *EnterRoomNotifyMgr) getEnterRoomSubscribers(ctx context.Context, uid uint32) (uint32, []uint32, error) {
	// 获取用户进房提醒权限设置
	gameTimeInfo, err := m.gameTimeCli.GetUserGameTime(ctx, &game_time.GetUserGameTimeRequest{
		Uid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr gameTimeCli.GetUserGameTime failed, err:%v, uid:%d", err, uid)
		return 0, nil, err
	}
	enterRoomNotifyPerm := gameTimeInfo.GetInfo().GetEnterRoomNotifyPerm()
	// 不通知任何人，直接返回
	if enterRoomNotifyPerm == uint32(game_play_logic.EnterRoomNotifyPerm_ENTER_ROOM_NOTIFY_PERM_NOBODY) {
		log.InfoWithCtx(ctx, "EnterRoomNotifyMgr user's(%d) enter_room_notify_perm is nobody", uid)
		return enterRoomNotifyPerm, nil, nil
	}

	// 通知粉丝/玩伴
	var (
		cursor            = ""
		result            = make([]uint32, 0)
		enterRoomPushConf = config.GetInteractGuideMiddleConfig().GetEnterRoomPushConf()
		totalCount        = 0
	)
	for {
		// 拉取订阅了该用户进房提醒的用户列表
		subscriberResp, err := m.gameTimeCli.GetUserEnterRoomNotifySubscribers(ctx, &game_time.GetUserEnterRoomNotifySubscribersRequest{
			Uid:    uid,
			Cursor: cursor,
			Limit:  enterRoomPushConf.GetHandleUserLimit(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr gameTimeCli.GetUserEnterRoomNotifySubscribers failed, err:%v, uid:%d, cursor:%s", err, uid, cursor)
			return enterRoomNotifyPerm, nil, err
		}
		userList := subscriberResp.GetSubscriberUidList()
		cursor = subscriberResp.GetCursor()
		if len(userList) == 0 {
			break
		}
		totalCount += len(userList)
		// 订阅用户数量超过限制，发告警
		if totalCount > int(enterRoomPushConf.GetWarningHandleUserLimit()) {
			log.WarnWithCtx(ctx, "EnterRoomNotifyMgr getEnterRoomSubscribers, subscriber count(%d) exceed warningHandleUserLimit(%d), uid:%d", totalCount, enterRoomPushConf.GetWarningHandleUserLimit(), uid)
			sendCacheWarning(fmt.Sprintf("用户的进房提醒订阅用户数量超过告警阈值, uid(%d) handleCount(%d) > warningHandleUserLimit(%d)", uid, totalCount, enterRoomPushConf.GetWarningHandleUserLimit()))
			break
		}

		// 过滤达到频率限制的用户
		userList, err = m.filterFreLimitUser(ctx, uid, userList)
		if err != nil {
			return enterRoomNotifyPerm, nil, err
		}
		if len(userList) > 0 {
			result = append(result, userList...)
		}
		// 如果配置了不开启循环处理，则只处理一次
		if !enterRoomPushConf.GetOpenLoopHandle() {
			break
		}
	}
	log.DebugWithCtx(ctx, "EnterRoomNotifyMgr getEnterRoomSubscribers, uid:%d, result:%v", uid, result)

	return enterRoomNotifyPerm, result, nil
}

// 过滤不符合进房提醒权限条件的用户
func (m *EnterRoomNotifyMgr) filterEnterRoomNotifyPermUser(ctx context.Context, enterRoomNotifyPerm, uid uint32, toUids []uint32) ([]uint32, error) {
	userFollow, followUser, err := m.friendshipCli.BatchGetBiFollowingWithCache(ctx, uid, toUids, true, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr friendshipCli.BatchGetBiFollowingWithCache failed, err:%v, uid:%d, toUids:%v", err, uid, toUids)
		return nil, err
	}
	log.DebugWithCtx(ctx, "EnterRoomNotifyMgr friendshipCli.BatchGetBiFollowingWithCache, uid:%d, userFollowMap:%v, followUserMap:%v", uid, userFollow, followUser)

	// 筛选满足进房提醒权限条件的用户
	result := make([]uint32, 0, len(toUids))
	switch enterRoomNotifyPerm {
	case uint32(game_play_logic.EnterRoomNotifyPerm_ENTER_ROOM_NOTIFY_PERM_PLAYMATES): // 筛选玩伴
		for _, toUid := range toUids {
			if userFollow[toUid] && followUser[toUid] {
				result = append(result, toUid)
			}
		}
	case uint32(game_play_logic.EnterRoomNotifyPerm_ENTER_ROOM_NOTIFY_PERM_FANS): // 筛选粉丝
		for _, toUid := range toUids {
			if followUser[toUid] {
				result = append(result, toUid)
			}
		}
	default:
		log.WarnWithCtx(ctx, "EnterRoomNotifyMgr filterEnterRoomNotifyPermUser, unknown enterRoomNotifyPerm:%d, uid:%d", enterRoomNotifyPerm, uid)
	}
	log.DebugWithCtx(ctx, "EnterRoomNotifyMgr filterEnterRoomNotifyPermUser, uid:%d, result:%v", uid, result)
	return result, nil
}

// 过滤达到频率限制的用户
func (m *EnterRoomNotifyMgr) filterFreLimitUser(ctx context.Context, uid uint32, toUids []uint32) ([]uint32, error) {
	if len(toUids) == 0 {
		return toUids, nil
	}

	items := make([]*game_fre_server.BatGetFreItem, 0, len(toUids))
	for _, toUid := range toUids {
		items = append(items, &game_fre_server.BatGetFreItem{
			FreCountSource: uint32(game_fre_server.FreCountSource_FRE_COUNT_SOURCE_ENTER_ROOM_PUSH_INTERVAL),
			Suffix:         strconv.Itoa(int(uid)),
			UserId:         toUid,
		})
	}
	resp, err := m.gameFreCli.BatGetFreCountBySources(ctx, &game_fre_server.BatGetFreCountBySourcesReq{
		Items: items,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr gameFreCli.BatGetFreCountBySources failed, err:%v", err)
		return nil, err
	}

	var (
		result = make([]uint32, 0)
		resMap = resp.GetResMap()
	)

	for _, toUid := range toUids {
		freItemKey := fmt.Sprintf("%d_%d_%d", toUid, game_fre_server.FreCountSource_FRE_COUNT_SOURCE_ENTER_ROOM_PUSH_INTERVAL, uid)
		if v, ok := resMap[freItemKey]; ok && v >= config.GetInteractGuideMiddleConfig().GetEnterRoomPushConf().GetReceivePushFreLimit() {
			continue
		}
		result = append(result, toUid)
	}

	log.DebugWithCtx(ctx, "EnterRoomNotifyMgr filterFreLimitUser, uid:%d, toUids:%v, result:%v", uid, toUids, result)
	return result, nil
}

// 过滤不在AB实验组的用户
func (m *EnterRoomNotifyMgr) filterNotInABTestUser(ctx context.Context, uid uint32, toUids []uint32) ([]uint32, error) {
	if len(toUids) == 0 {
		return toUids, nil
	}

	var (
		uids              = make([]uint64, 0, len(toUids))
		result            = make([]uint32, 0, len(toUids))
		clientDeviceIdMap = make(map[string]string, len(toUids))
		abCfg             = config.GetInteractGuideMiddleConfig().GetEnterRoomPushConf().GetAbCfg()
		huokeDeviceIds    = make([]string, 0, len(toUids))
		activeDeviceIds   = make([]string, 0, len(toUids))
		huokeUids         = make([]uint32, 0, len(toUids))
		activeUids        = make([]uint32, 0, len(toUids))
		matchMap1         = make(map[uint32]bool)
		matchMap2         = make(map[uint32]bool)
		ttDeviceInfos     = make([]*device_info_pb.DeviceInfoData, 0, len(toUids))
		hyDeviceInfos     = make([]*device_info_pb.DeviceInfoData, 0, len(toUids))
	)

	for _, toUid := range toUids {
		uids = append(uids, uint64(toUid))
	}
	// 查询用户最近登录信息，获取设备id
	loginInfos, serverErr := m.userAuthHistoryCli.BatchGetUserLastLoginInfo(ctx, uids)
	if serverErr != nil {
		log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr userAuthHistoryCli.BatchGetUserLastLoginInfo failed, err:%v, uids:%v", serverErr, uids)
		return nil, serverErr
	}

	userMap, err := m.getUsersMap(ctx, toUids)
	if err != nil {
		return nil, err
	}

	for _, info := range loginInfos {
		deviceId, err := device_id.ToClientDeviceId(info.GetDeviceId(), info.GetClientType())
		if err != nil {
			log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr device_id.ToClientDeviceId failed, err:%v, info:%+v", err, info)
			continue
		}
		clientDeviceIdMap[info.GetDeviceId()] = deviceId

		switch info.GetMarketId() {
		case uint32(protocol.MarketID_TT):
			ttDeviceInfos = append(ttDeviceInfos, &device_info_pb.DeviceInfoData{
				DeviceId: deviceId,
				Uid:      strconv.FormatUint(info.GetUid(), 10),
			})
		case uint32(protocol.MarketID_HUANYOU):
			hyDeviceInfos = append(hyDeviceInfos, &device_info_pb.DeviceInfoData{
				DeviceId: deviceId,
				Uid:      strconv.FormatUint(info.GetUid(), 10),
			})
		default:
			log.WarnWithCtx(ctx, "EnterRoomNotifyMgr filterNotInABTestUser, unSupport marketId:%d, uid:%d", info.GetMarketId(), info.GetUid())
		}
	}

	deviceStateMap, err := m.getDeviceUserStateMap(ctx, ttDeviceInfos, hyDeviceInfos)
	if err != nil {
		return nil, err
	}

	for _, info := range loginInfos {
		deviceId := clientDeviceIdMap[info.GetDeviceId()]
		key := device_info.GenBatchDeviceInfoKey(deviceId, strconv.FormatUint(info.GetUid(), 10))
		userType := deviceStateMap[key]

		registerTime := int64(userMap[uint32(info.GetUid())].GetRegisteredAt())
		// 当天注册的用户也是获客用户
		if isTodayRegistered(registerTime) || userType == device_info.Pull || userType == device_info.Recall {
			huokeDeviceIds = append(huokeDeviceIds, deviceId)
			huokeUids = append(huokeUids, uint32(info.GetUid()))
		} else {
			activeDeviceIds = append(activeDeviceIds, deviceId)
			activeUids = append(activeUids, uint32(info.GetUid()))
		}
	}

	log.DebugWithCtx(ctx, "EnterRoomNotifyMgr filterNotInABTestUser, uid:%d, len(huokeDeviceIds):%d, len(activeDeviceIds):%d", uid, len(huokeDeviceIds), len(activeDeviceIds))

	if len(activeUids) > 0 {
		matchMap1, err = m.getAbTestMatchMap(ctx, uid, activeDeviceIds, activeUids, abCfg.RcmdTestCfg.ActiveKey, abCfg.ExpGroup)
		if err != nil {
			return nil, err
		}
	}
	if len(huokeUids) > 0 {
		matchMap2, err = m.getAbTestMatchMap(ctx, uid, huokeDeviceIds, huokeUids, abCfg.RcmdTestCfg.NewKey, abCfg.ExpGroup)
		if err != nil {
			return nil, err
		}
	}

	for _, info := range loginInfos {
		//deviceId := clientDeviceIdMap[info.GetDeviceId()]
		if matchMap1[uint32(info.GetUid())] || matchMap2[uint32(info.GetUid())] {
			result = append(result, uint32(info.GetUid()))
		} else {
			log.InfoWithCtx(ctx, "EnterRoomNotifyMgr user(%d) not in enter_room ab_test exp_group", info.GetUid())
		}
	}

	log.DebugWithCtx(ctx, "EnterRoomNotifyMgr filterNotInABTestUser, uid:%d, result:%v", uid, result)
	return result, nil
}

func isTodayRegistered(registerTime int64) bool {
	return time.Unix(registerTime, 0).Format(time.DateOnly) == time.Now().Format(time.DateOnly)
}

func (m *EnterRoomNotifyMgr) getUsersMap(ctx context.Context, uids []uint32) (map[uint32]*account_go.User, error) {
	userMap := make(map[uint32]*account_go.User, len(uids))
	for i := 0; i < len(uids); i += queryAccountLimit {
		checkUids := uids[i:min(i+queryAccountLimit, len(uids))]
		usersResp, err := m.accountCli.GetUsersByUids(ctx, checkUids)
		if err != nil {
			log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr accountCli.BatchGetUserByUid failed, err:%v, checkUids:%v", err, checkUids)
			return nil, err
		}
		for _, userInfo := range usersResp.GetUserList() {
			userMap[userInfo.GetUid()] = userInfo
		}
	}
	return userMap, nil
}

func (m *EnterRoomNotifyMgr) getDeviceUserStateMap(ctx context.Context, ttDeviceInfos, hyDeviceInfos []*device_info_pb.DeviceInfoData) (map[string]device_info.UserType, error) {
	var (
		stateMap = make(map[string]device_info.UserType)
		err      error
	)
	if len(ttDeviceInfos) > 0 {
		stateMap, err = m.deviceInfoCli.BatchGetDeviceUserState(ctx, &device_info_pb.BatchDeviceInfoRequestData{
			AppId:      marketid_helper.GetAppName(uint32(protocol.MarketID_TT)),
			DeviceInfo: ttDeviceInfos,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr deviceInfoCli.BatchGetDeviceUserState failed, err:%v, ttDeviceInfos:%v", err, ttDeviceInfos)
			return nil, err
		}
		log.DebugWithCtx(ctx, "EnterRoomNotifyMgr getDeviceUserStateMap, ttDeviceInfos:%v, stateMap:%v", ttDeviceInfos, stateMap)
	}
	if len(hyDeviceInfos) > 0 {
		extraStateMap, err := m.deviceInfoCli.BatchGetDeviceUserState(ctx, &device_info_pb.BatchDeviceInfoRequestData{
			AppId:      marketid_helper.GetAppName(uint32(protocol.MarketID_HUANYOU)),
			DeviceInfo: hyDeviceInfos,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr deviceInfoCli.BatchGetDeviceUserState failed, err:%v, hyDeviceInfos:%v", err, hyDeviceInfos)
			return nil, err
		}
		log.DebugWithCtx(ctx, "EnterRoomNotifyMgr getDeviceUserStateMap, hyDeviceInfos:%v, extraStateMap:%v", ttDeviceInfos, extraStateMap)
		for k, v := range extraStateMap {
			if _, ok := stateMap[k]; !ok {
				stateMap[k] = v
			}
		}
	}
	return stateMap, nil
}

func (m *EnterRoomNotifyMgr) getAbTestMatchMap(ctx context.Context, uid uint32, deviceIds []string, uids []uint32, argKey, expectArgVal string) (map[uint32]bool, error) {
	matchMap := make(map[uint32]bool, len(uids))

	// 批量判断是否命中AB实验组
	// 每次最多查询200个AB实验结果
	for i := 0; i < len(uids); i += queryAbTestLimit {
		checkUidIds := uids[i:min(i+queryAbTestLimit, len(uids))]
		resultMap, err := m.abtestCli.BatchGetUidTestByArg(ctx, checkUidIds, argKey)
		if err != nil {
			log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr abtestCli.BatchGetAbTestMatchResult failed, err:%v, checkUidIds:%v, argKey:%s", err, checkUidIds, argKey)
			return nil, err
		}
		for resUid, argVal := range resultMap {
			if argVal == expectArgVal {
				matchMap[resUid] = true
			}
		}
		log.DebugWithCtx(ctx, "EnterRoomNotifyMgr filterNotInABTestUser, uid:%d, uids:%v, resultMap:%v, argKey:%s, expectArgVal:%s",
			uid, uids, resultMap, argKey, expectArgVal)
	}
	return matchMap, nil
}

// 过滤与当前用户在同一房间的用户
func (m *EnterRoomNotifyMgr) filterInSameRoomUser(ctx context.Context, uid, cid uint32, toUids []uint32) ([]uint32, error) {
	if len(toUids) == 0 {
		return toUids, nil
	}

	// 判断需要下发的用户跟当前用户在同一房间，同一房间不下发
	result := make([]uint32, 0, len(toUids))
	// 每次最多请求100个用户的在房信息
	for i := 0; i < len(toUids); i += queryUserChannelLimit {
		checkUids := toUids[i:min(i+queryUserChannelLimit, len(toUids))]
		userChannelMap, err := m.channelOlCli.BatchGetUserChannelId(ctx, uid, checkUids...)
		if err != nil {
			log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr channelOlCli.BatchGetUserChannelId failed, err:%v, uid:%d, checkUids:%v", err, uid, checkUids)
			return nil, err
		}
		for _, uid := range checkUids {
			if v, ok := userChannelMap[uid]; ok && v == cid {
				continue
			}
			result = append(result, uid)
		}
		log.DebugWithCtx(ctx, "EnterRoomNotifyMgr filterInSameRoomUser, uid:%d, cid:%d, result:%v", uid, cid, result)
	}
	return result, nil
}

func (m *EnterRoomNotifyMgr) sendPush(ctx context.Context, uid uint32, toUids []uint32) error {
	if len(toUids) == 0 {
		return nil
	}

	userInfo, serverErr := m.accountCli.GetUserByUid(ctx, uid)
	if serverErr != nil {
		log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr accountCli.GetUserByUid failed, err:%v, uid:%d", serverErr, uid)
		return serverErr
	}
	log.DebugWithCtx(ctx, "EnterRoomNotifyMgr accountCli.GetUserByUid, uid:%d, userInfo:%+v", uid, userInfo)

	var (
		enterRoomPushConf = config.GetInteractGuideMiddleConfig().GetEnterRoomPushConf()
		pushTitle         = fmt.Sprintf(enterRoomPushConf.GetPushTitle(), userInfo.GetNickname())
		jumpUrl           = fmt.Sprintf(enterRoomPushJumpUrl, userInfo.GetUid(), userInfo.GetUsername())
	)

	pushMsg := &game_play_logic.UserEnterRoomPush{
		Uid:      userInfo.GetUid(),
		Account:  userInfo.GetUsername(),
		Nickname: userInfo.GetNickname(),
		Title:    pushTitle,
	}
	content, err := proto.Marshal(pushMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr proto.Marshal failed, err:%v, pushMsg:%+v", err, pushMsg)
		return err
	}
	payload, err := proto.Marshal(&gapush.PushMessage{
		Cmd:     uint32(gapush.PushMessage_USER_ENTER_ROOM_PUSH),
		Content: content,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr proto.Marshal failed, err:%v, pushMsg:%+v", err, pushMsg)
		return err
	}

	// 根据用户的在线状态，下发在线/离线推送
	// 在线/离线推送的限制为单次<=1000
	for i := 0; i < len(toUids); i += pushUserLimit {
		pushUids := toUids[i:min(i+pushUserLimit, len(toUids))]
		presenceMap, err := m.presenceCli.BatchGetUserPres(ctx, pushUids)
		if err != nil {
			log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr presenceCli.BatchGetUserPres failed, err:%v, pushUids:%v", err, pushUids)
			return err
		}
		onlineUids := make([]uint32, 0, len(pushUids))
		for _, uid := range pushUids {
			if v, ok := presenceMap[uid]; ok && v != nil {
				onlineUids = append(onlineUids, uid)
			}
		}

		if len(onlineUids) > 0 {
			// 不在线的就不发在线推送了，避免推送服务压力大
			_ = m.msgPusher.OnlinePush(ctx, onlineUids, bizLabel.LabelEnterRoomNotify, payload)
		}
		_ = m.msgPusher.OfflineOpPush(ctx, pushUids, pushTitle, enterRoomPushConf.GetPushContent(), jumpUrl)
	}

	return nil
}

// 记录当前用户下发通知频率
func (m *EnterRoomNotifyMgr) recordPushFreLimit(ctx context.Context, uid uint32, toUids []uint32) error {
	if len(toUids) == 0 {
		return nil
	}

	items := make([]*game_fre_server.BatIncFreItem, 0, len(toUids))
	for _, toUid := range toUids { // 频率限制维度是uid-toUid的
		items = append(items, &game_fre_server.BatIncFreItem{
			FreCountSource: uint32(game_fre_server.FreCountSource_FRE_COUNT_SOURCE_ENTER_ROOM_PUSH_INTERVAL),
			Suffix:         strconv.Itoa(int(uid)),
			ExpireTime:     uint64(config.GetInteractGuideMiddleConfig().GetEnterRoomPushConf().GetReceivePushInterval()),
			UserId:         toUid,
		})
	}
	_, err := m.gameFreCli.BatIncFreCountBySources(ctx, &game_fre_server.BatIncFreCountBySourcesReq{
		Items: items,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "EnterRoomNotifyMgr gameFreCli.BatIncFreCountBySources failed, err:%v", err)
		return err
	}
	return nil
}

func sendCacheWarning(msg string) {
	sendErr := monkey_sender.GetNumMsgSenderByChatId(config.GetInteractGuideMiddleConfig().GetFallBackChatId(),
		config.GetInteractGuideMiddleConfig().GetFallBackWarnDuration()).SendMsg("interact-guide-middle进房提醒", msg)
	if sendErr != nil {
		log.Errorf("EnterRoomNotifyMgr monkey_sender fallback SendMsg err:%v", sendErr)
	}
}
