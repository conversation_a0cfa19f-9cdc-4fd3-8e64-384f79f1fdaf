package redis

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/game-accelerator/internal/infra/db"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_cfg/cache"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_cfg/entity"
	"strconv"
)

//go:generate ifacemaker -f *.go -s acceleratorGameCache -p cache -o ../cache.go -i AcceleratorGameCache
//go:generate mockgen -destination=../../mocks/accelerator_game_cache.go -package=mocks -source ../cache.go AcceleratorGameCache
type acceleratorGameCache struct {
	cmder *db.RedisDB
}

func NewAcceleratorGameCache(database *db.RedisDB) cache.AcceleratorGameCache {
	return &acceleratorGameCache{
		cmder: database,
	}
}

func (c *acceleratorGameCache) getActivePoolKey() string {
	return "accelerator:game_active_pool"
}

// AddToActivePool 将游戏添加到活跃池中
func (c *acceleratorGameCache) AddToActivePool(ctx context.Context, gameScoreMap map[uint32]int64) error {
	if len(gameScoreMap) == 0 {
		return nil
	}
	// 使用 ZAdd 命令将游戏添加到 Redis 有序集合中
	zMembers := make([]*redis.Z, 0, len(gameScoreMap))
	for gameId, score := range gameScoreMap {
		zMembers = append(zMembers, &redis.Z{
			Score:  float64(score),
			Member: strconv.Itoa(int(gameId)),
		})
	}
	_, err := c.cmder.ZAdd(ctx, c.getActivePoolKey(), zMembers...).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddToActivePool ZAdd gameScoreMap:%v error: %v", gameScoreMap, err)
		return err
	}
	return nil
}

// GetGamesByScore 根据score降序从活跃池获取所有游戏
func (c *acceleratorGameCache) GetGamesByScore(ctx context.Context, lastScore string, limit uint32) ([]*entity.GameScoreInfo, error) {
	// 按分数查询活跃池中的游戏，不包含max边界
	maxScore := "+inf"
	if lastScore != "" {
		maxScore = lastScore
	}
	results, err := c.cmder.ZRevRangeByScoreWithScores(ctx, c.getActivePoolKey(), &redis.ZRangeBy{
		Min:    "-inf",
		Max:    maxScore,
		Offset: 0,
		Count:  int64(limit),
	}).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGamesByScore ZRevRangeByScoreWithScores error: %v, lastScore: %s, limit: %d", err, lastScore, limit)
		return nil, err
	}
	gameScoreInfos := make([]*entity.GameScoreInfo, 0, len(results))
	for _, result := range results {
		if result.Member == nil {
			log.WarnWithCtx(ctx, "GetGamesByScore member is nil, result: %v", result)
			continue
		}
		gameIdStr, ok := result.Member.(string)
		if !ok {
			log.WarnWithCtx(ctx, "GetGamesByScore member is not string, result: %v", result)
			continue
		}
		gameId, err := strconv.Atoi(gameIdStr)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGamesByScore ParseUint error: %v, gameIdStr: %s", err, gameIdStr)
			continue
		}
		gameScoreInfos = append(gameScoreInfos, &entity.GameScoreInfo{
			GameId: uint32(gameId),
			Score:  int64(result.Score),
		})
	}
	return gameScoreInfos, nil
}

// GetGameScores 根据游戏id批量获取分数
func (c *acceleratorGameCache) GetGameScores(ctx context.Context, gameIds []uint32) ([]*entity.GameScoreInfo, error) {
	if len(gameIds) == 0 {
		return nil, nil
	}
	// 将游戏ID转换为字符串
	strGameIds := make([]string, 0, len(gameIds))
	for _, gameId := range gameIds {
		strGameIds = append(strGameIds, strconv.Itoa(int(gameId)))
	}
	// 使用 ZScore 获取每个游戏的分数
	scores, err := c.cmder.ZMScore(ctx, c.getActivePoolKey(), strGameIds...).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGamesByIds ZMScore error: %v, gameIds: %v", err, gameIds)
		return nil, err
	}

	gameScoreInfos := make([]*entity.GameScoreInfo, 0, len(gameIds))
	for i, score := range scores {
		gameScoreInfos = append(gameScoreInfos, &entity.GameScoreInfo{
			GameId: gameIds[i],
			Score:  int64(score),
		})
	}
	return gameScoreInfos, nil
}
