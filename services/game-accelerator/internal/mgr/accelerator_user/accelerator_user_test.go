package accelerator_user

import (
	"context"
	"errors"
	accelerator_user "golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_user/mocks"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	pushNotification "golang.52tt.com/clients/push-notification/v2"
	pb "golang.52tt.com/protocol/services/game-accelerator"
	"golang.52tt.com/services/game-accelerator/internal/mgr/accelerator_user/entity"
	third_open_api "golang.52tt.com/services/game-accelerator/internal/third-open-api"
	third_open_api_mock "golang.52tt.com/services/game-accelerator/internal/third-open-api/mocks"
)

type HelperForTest struct {
	ctl *gomock.Controller
	*AcceleratorUserMgr
	mockUserStore    *accelerator_user.MockIAcceleratorUserStore
	mockThirdOpenApi *third_open_api_mock.MockIThirdOpenApi
	mockCache        *accelerator_user.MockAcceleratorTokenCache
}

func newHelperForTest(t *testing.T) (*HelperForTest, func()) {
	ctl := gomock.NewController(t)
	mockUserStore := accelerator_user.NewMockIAcceleratorUserStore(ctl)
	mockThirdOpenApi := third_open_api_mock.NewMockIThirdOpenApi(ctl)
	mockCache := accelerator_user.NewMockAcceleratorTokenCache(ctl)

	mgr := &AcceleratorUserMgr{
		userStore:    mockUserStore,
		appId:        "test_app_id",
		appSecret:    "test_app_secret",
		appUrl:       "http://test.example.com",
		thirdOpenApi: mockThirdOpenApi,
		cache:        mockCache,
		pushClient:   &pushNotification.Client{}, // Use a real client for now, we'll mock the interface
	}

	return &HelperForTest{
			ctl:                ctl,
			AcceleratorUserMgr: mgr,
			mockUserStore:      mockUserStore,
			mockThirdOpenApi:   mockThirdOpenApi,
			mockCache:          mockCache,
		}, func() {
			ctl.Finish()
		}
}

func TestUpsertUser_Success(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	user := &entity.AcceleratorUser{
		Id:           12345,
		UserIdentity: pb.UserIdentity_USER_IDENTITY_FREE,
		UserStatus:   pb.UserStatus_USER_STATUS_NORMAL,
		CreateTime:   time.Now(),
	}

	cli.mockUserStore.EXPECT().
		UpsertUser(gomock.Any(), gomock.Any()).
		Return(nil)

	err := cli.UpsertUser(context.Background(), user)

	assert.NoError(t, err)
}

func TestUpsertUser_Error(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	user := &entity.AcceleratorUser{
		Id:           12345,
		UserIdentity: pb.UserIdentity_USER_IDENTITY_FREE,
		UserStatus:   pb.UserStatus_USER_STATUS_NORMAL,
	}

	expectedError := errors.New("database connection error")
	cli.mockUserStore.EXPECT().
		UpsertUser(gomock.Any(), gomock.Any()).
		Return(expectedError)

	err := cli.UpsertUser(context.Background(), user)

	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
}

func TestGetUsers_Success(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	expectedUsers := []*entity.AcceleratorUser{
		{
			Id:           12345,
			UserIdentity: pb.UserIdentity_USER_IDENTITY_VIP,
			UserStatus:   pb.UserStatus_USER_STATUS_NORMAL,
			CreateTime:   time.Now(),
		},
	}

	cli.mockUserStore.EXPECT().
		GetUsers(gomock.Any(), gomock.Any()).
		Return(expectedUsers, nil)

	users, err := cli.GetUsers(context.Background(), []uint32{12345})

	assert.NoError(t, err)
	assert.Equal(t, expectedUsers, users)
}

func TestGetUsers_Error(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	expectedError := errors.New("users not found")
	cli.mockUserStore.EXPECT().
		GetUsers(gomock.Any(), gomock.Any()).
		Return(nil, expectedError)

	users, err := cli.GetUsers(context.Background(), []uint32{12345})

	assert.Error(t, err)
	assert.Nil(t, users)
	assert.Equal(t, expectedError, err)
}

func TestSearchUsers_DefaultPagination(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	req := &pb.GetAcceleratorUserListReq{
		LastId: "", // Empty for first page
	}

	expectedUsers := []*entity.AcceleratorUser{
		{
			Id:           12345,
			UserIdentity: pb.UserIdentity_USER_IDENTITY_FREE,
			UserStatus:   pb.UserStatus_USER_STATUS_NORMAL,
		},
	}

	cli.mockUserStore.EXPECT().
		CountDocuments(gomock.Any(), gomock.Any()).
		Return(int64(1), nil)
	cli.mockUserStore.EXPECT().
		SearchUsers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		Return(expectedUsers, "", nil)

	users, total, lastId, err := cli.SearchUsers(context.Background(), req)

	assert.NoError(t, err)
	assert.Equal(t, expectedUsers, users)
	assert.Equal(t, int64(1), total)
	assert.Equal(t, "", lastId)
}

func TestSearchUsers_CustomPagination(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	req := &pb.GetAcceleratorUserListReq{
		LastId: "1690000000000_123",
	}

	expectedUsers := []*entity.AcceleratorUser{}

	cli.mockUserStore.EXPECT().
		SearchUsers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		Return(expectedUsers, "", nil)

	users, total, lastId, err := cli.SearchUsers(context.Background(), req)

	assert.NoError(t, err)
	assert.Equal(t, expectedUsers, users)
	assert.Equal(t, int64(0), total)
	assert.Equal(t, "", lastId)
}

func TestSearchUsers_LargePagination(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	req := &pb.GetAcceleratorUserListReq{
		LastId: "",
	}

	expectedUsers := []*entity.AcceleratorUser{}

	cli.mockUserStore.EXPECT().
		CountDocuments(gomock.Any(), gomock.Any()).
		Return(int64(0), nil)
	cli.mockUserStore.EXPECT().
		SearchUsers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		Return(expectedUsers, "", nil)

	users, total, lastId, err := cli.SearchUsers(context.Background(), req)

	assert.NoError(t, err)
	assert.Equal(t, expectedUsers, users)
	assert.Equal(t, int64(0), total)
	assert.Equal(t, "", lastId)
}

func TestSearchUsers_NegativePage(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	req := &pb.GetAcceleratorUserListReq{
		LastId: "",
	}

	expectedUsers := []*entity.AcceleratorUser{}

	cli.mockUserStore.EXPECT().
		CountDocuments(gomock.Any(), gomock.Any()).
		Return(int64(0), nil)
	cli.mockUserStore.EXPECT().
		SearchUsers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		Return(expectedUsers, "", nil)

	users, total, lastId, err := cli.SearchUsers(context.Background(), req)

	assert.NoError(t, err)
	assert.Equal(t, expectedUsers, users)
	assert.Equal(t, int64(0), total)
	assert.Equal(t, "", lastId)
}

func TestSearchUsers_Error(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	req := &pb.GetAcceleratorUserListReq{
		LastId: "",
	}

	expectedError := errors.New("database connection error")
	cli.mockUserStore.EXPECT().
		CountDocuments(gomock.Any(), gomock.Any()).
		Return(int64(0), expectedError)

	users, total, lastId, err := cli.SearchUsers(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, users)
	assert.Equal(t, int64(0), total)
	assert.Equal(t, "", lastId)
	assert.Equal(t, expectedError, err)
}

func TestBanUserUseAccelerator_Success(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()
	cli.mockUserStore.EXPECT().
		GetUser(gomock.Any(), gomock.Any()).Return(&entity.AcceleratorUser{Id: 12345}, nil)
	cli.mockUserStore.EXPECT().
		UpdateUserAcceleratorStatus(gomock.Any(), uint32(12345), pb.UserStatus_USER_STATUS_BAN).
		Return(nil)

	err := cli.AcceleratorUserMgr.UpdateUserAcceleratorStatus(context.Background(), 12345, pb.BanUserUseAcceleratorReq_ACTION_BAN)

	assert.NoError(t, err)
}

func TestBanUserUseAccelerator_Error(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	expectedError := errors.New("database update failed")
	cli.mockUserStore.EXPECT().
		GetUser(gomock.Any(), gomock.Any()).Return(&entity.AcceleratorUser{Id: 12345}, nil)

	cli.mockUserStore.EXPECT().
		UpdateUserAcceleratorStatus(gomock.Any(), uint32(12345), pb.UserStatus_USER_STATUS_BAN).
		Return(expectedError)

	err := cli.AcceleratorUserMgr.UpdateUserAcceleratorStatus(context.Background(), 12345, pb.BanUserUseAcceleratorReq_ACTION_BAN)

	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
}

func TestGetUserAcceleratorLog_Success(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	req := &pb.GetUserAcceleratorLogReq{
		Ttid:      "test_ttid_12345",
		AppIds:    "app1,app2",
		StartTime: time.Now().AddDate(0, 0, -7).Unix(),
		EndTime:   time.Now().Unix(),
		Page:      1,
		PageSize:  10,
	}

	expectedResponse := &third_open_api.LogsResponse{
		Code:    200,
		Message: "success",
		Data: struct {
			Items []struct {
				Imei      string   `json:"imei"`
				AppIds    []string `json:"app_ids"` // 应用ID
				StartTime string   `json:"start_time"`
				EndTime   string   `json:"end_time"`
				FlowUse   int      `json:"flow_use"`
				MaxWidth  int      `json:"max_width"` // 最大宽带
				MinWidth  int      `json:"min_width"` // 最小宽带
				AvgWidth  int      `json:"avg_width"` // 平均宽带
			} `json:"items"` // 日志列表

			Total       int32  `json:"total"`        // 总数
			PerPage     int32  `json:"perPage"`      // 每页数量
			CurrentPage int32  `json:"current_page"` // 当前页码
			ServerTime  string `json:"server_time"`  // 服务器时间

		}{
			Items: []struct {
				Imei      string   `json:"imei"`
				AppIds    []string `json:"app_ids"`
				StartTime string   `json:"start_time"`
				EndTime   string   `json:"end_time"`
				FlowUse   int      `json:"flow_use"`
				MaxWidth  int      `json:"max_width"`
				MinWidth  int      `json:"min_width"`
				AvgWidth  int      `json:"avg_width"`
			}{
				{
					Imei:      "test_imei",
					AppIds:    []string{"app1", "app2"},
					StartTime: "2023-01-01 10:00:00",
					EndTime:   "2023-01-01 11:00:00",
					FlowUse:   1024,
					MaxWidth:  100,
					MinWidth:  50,
					AvgWidth:  75,
				},
			},
			Total:       1,
			PerPage:     10,
			CurrentPage: 1,
			ServerTime:  "2023-01-01 12:00:00",
		},
	}

	cli.mockThirdOpenApi.EXPECT().
		GetAcceleratorLog(gomock.Any(), gomock.Any()).
		Return(expectedResponse, nil)

	response, err := cli.GetUserAcceleratorLog(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, int32(200), response.Code)
	assert.Equal(t, "success", response.Message)
	assert.Len(t, response.Data.Items, 1)
}

func TestGetUserAcceleratorLog_HttpError(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	req := &pb.GetUserAcceleratorLogReq{
		Ttid:      "test_ttid_12345",
		AppIds:    "app1,app2",
		StartTime: time.Now().AddDate(0, 0, -7).Unix(),
		EndTime:   time.Now().Unix(),
		Page:      1,
		PageSize:  10,
	}

	expectedError := errors.New("http request failed")
	cli.mockThirdOpenApi.EXPECT().
		GetAcceleratorLog(gomock.Any(), gomock.Any()).
		Return(nil, expectedError)

	response, err := cli.GetUserAcceleratorLog(context.Background(), req)

	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, response)
}

func TestGetUserAcceleratorLog_UnmarshalError(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	req := &pb.GetUserAcceleratorLogReq{
		Ttid:      "test_ttid_12345",
		AppIds:    "app1,app2",
		StartTime: time.Now().AddDate(0, 0, -7).Unix(),
		EndTime:   time.Now().Unix(),
		Page:      1,
		PageSize:  10,
	}

	// Return an error for unmarshal failure
	expectedError := errors.New("invalid character 'j' looking for beginning of object key string")
	cli.mockThirdOpenApi.EXPECT().
		GetAcceleratorLog(gomock.Any(), gomock.Any()).
		Return(nil, expectedError)

	response, err := cli.GetUserAcceleratorLog(context.Background(), req)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid character")
	assert.Nil(t, response)
}

func TestGetUserAcceleratorLog_ZeroTimestamps(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	req := &pb.GetUserAcceleratorLogReq{
		Ttid:      "test_ttid_12345",
		AppIds:    "app1,app2",
		StartTime: 0, // Should result in empty string
		EndTime:   0, // Should result in empty string
		Page:      1,
		PageSize:  10,
	}

	expectedResponse := &third_open_api.LogsResponse{
		Code:    200,
		Message: "success",
		Data: struct {
			Items []struct {
				Imei      string   `json:"imei"`
				AppIds    []string `json:"app_ids"` // 应用ID
				StartTime string   `json:"start_time"`
				EndTime   string   `json:"end_time"`
				FlowUse   int      `json:"flow_use"`
				MaxWidth  int      `json:"max_width"` // 最大宽带
				MinWidth  int      `json:"min_width"` // 最小宽带
				AvgWidth  int      `json:"avg_width"` // 平均宽带
			} `json:"items"` // 日志列表

			Total       int32  `json:"total"`        // 总数
			PerPage     int32  `json:"perPage"`      // 每页数量
			CurrentPage int32  `json:"current_page"` // 当前页码
			ServerTime  string `json:"server_time"`  // 服务器时间

		}{
			Items: []struct {
				Imei      string   `json:"imei"`
				AppIds    []string `json:"app_ids"`
				StartTime string   `json:"start_time"`
				EndTime   string   `json:"end_time"`
				FlowUse   int      `json:"flow_use"`
				MaxWidth  int      `json:"max_width"`
				MinWidth  int      `json:"min_width"`
				AvgWidth  int      `json:"avg_width"`
			}{},
			Total:       0,
			PerPage:     10,
			CurrentPage: 1,
			ServerTime:  "2023-01-01 12:00:00",
		},
	}

	cli.mockThirdOpenApi.EXPECT().
		GetAcceleratorLog(gomock.Any(), gomock.Any()).
		Return(expectedResponse, nil)

	response, err := cli.GetUserAcceleratorLog(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, int32(200), response.Code)
	assert.Equal(t, "success", response.Message)
	assert.Empty(t, response.Data.Items)
}

func TestGetAcceleratorToken_Success(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	req := &pb.GetAcceleratorTokenReq{
		Uid:   12345,
		Imei:  "test_imei_12345",
		AppId: "app1",
	}

	// 创建一个有效的用户，状态为正常且未过期
	validUser := &entity.AcceleratorUser{
		Id:                  12345,
		UserStatus:          pb.UserStatus_USER_STATUS_NORMAL,
		AuthorizeExpireTime: time.Now().Add(time.Hour * 24), // 24小时后过期
	}

	expectedResponse := &third_open_api.TokenResponse{
		Code:    0,
		Message: "success",
		Data: struct {
			Token string `json:"token"`
		}{
			Token: "test_token_abcdef123456",
		},
	}

	// 按照实际调用顺序设置mock期望
	cli.mockUserStore.EXPECT().
		GetUser(gomock.Any(), uint32(12345)).Return(validUser, nil)

	// 模拟缓存中没有token
	cli.mockCache.EXPECT().
		GetUserAcceleratorToken(gomock.Any(), uint32(12345), "app1").
		Return("", errors.New("cache miss"))

	cli.mockThirdOpenApi.EXPECT().
		GetApiToken(gomock.Any(), gomock.Any()).
		Return(expectedResponse, nil)

	// 模拟设置token到缓存
	cli.mockCache.EXPECT().
		SetUserAcceleratorToken(gomock.Any(), uint32(12345), "app1", "test_token_abcdef123456", gomock.Any()).
		Return(nil)

	token, err := cli.GetAcceleratorToken(context.Background(), req)

	assert.NoError(t, err)
	assert.Equal(t, "test_token_abcdef123456", token)
}

func TestGetAcceleratorToken_HttpError(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	req := &pb.GetAcceleratorTokenReq{
		Uid:   12345,
		Imei:  "test_imei_12345",
		AppId: "app1",
	}

	// 创建一个有效的用户，状态为正常且未过期
	validUser := &entity.AcceleratorUser{
		Id:                  12345,
		UserStatus:          pb.UserStatus_USER_STATUS_NORMAL,
		AuthorizeExpireTime: time.Now().Add(time.Hour * 24), // 24小时后过期
	}

	expectedError := errors.New("token service unavailable")

	// 按照实际调用顺序设置mock期望
	cli.mockUserStore.EXPECT().
		GetUser(gomock.Any(), uint32(12345)).Return(validUser, nil)

	// 模拟缓存中没有token
	cli.mockCache.EXPECT().
		GetUserAcceleratorToken(gomock.Any(), uint32(12345), "app1").
		Return("", errors.New("cache miss"))

	cli.mockThirdOpenApi.EXPECT().
		GetApiToken(gomock.Any(), gomock.Any()).
		Return(nil, expectedError)

	token, err := cli.GetAcceleratorToken(context.Background(), req)

	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Empty(t, token)
}

func TestGetAcceleratorToken_UnmarshalError(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	req := &pb.GetAcceleratorTokenReq{
		Uid:   12345,
		Imei:  "test_imei_12345",
		AppId: "app1",
	}

	// 创建一个有效的用户，状态为正常且未过期
	validUser := &entity.AcceleratorUser{
		Id:                  12345,
		UserStatus:          pb.UserStatus_USER_STATUS_NORMAL,
		AuthorizeExpireTime: time.Now().Add(time.Hour * 24), // 24小时后过期
	}

	// Return an error for unmarshal failure
	expectedError := errors.New("invalid character 'j' looking for beginning of object key string")

	// 按照实际调用顺序设置mock期望
	cli.mockUserStore.EXPECT().
		GetUser(gomock.Any(), uint32(12345)).Return(validUser, nil)

	// 模拟缓存中没有token
	cli.mockCache.EXPECT().
		GetUserAcceleratorToken(gomock.Any(), uint32(12345), "app1").
		Return("", errors.New("cache miss"))

	cli.mockThirdOpenApi.EXPECT().
		GetApiToken(gomock.Any(), gomock.Any()).
		Return(nil, expectedError)

	token, err := cli.GetAcceleratorToken(context.Background(), req)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid character")
	assert.Empty(t, token)
}

func TestGetAcceleratorToken_UserNotActive(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	req := &pb.GetAcceleratorTokenReq{
		Uid:   12345,
		Imei:  "test_imei_12345",
		AppId: "app1",
	}

	// 创建一个未激活的用户
	inactiveUser := &entity.AcceleratorUser{
		Id:         12345,
		UserStatus: pb.UserStatus_USER_STATUS_UNSPECIFIED, // 未激活状态
	}

	cli.mockUserStore.EXPECT().
		GetUser(gomock.Any(), uint32(12345)).Return(inactiveUser, nil)

	token, err := cli.GetAcceleratorToken(context.Background(), req)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "加速器未激活")
	assert.Empty(t, token)
}

func TestGetAcceleratorToken_UserBanned(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	req := &pb.GetAcceleratorTokenReq{
		Uid:   12345,
		Imei:  "test_imei_12345",
		AppId: "app1",
	}

	// 创建一个被禁用的用户
	bannedUser := &entity.AcceleratorUser{
		Id:         12345,
		UserStatus: pb.UserStatus_USER_STATUS_BAN, // 被禁用状态
	}

	cli.mockUserStore.EXPECT().
		GetUser(gomock.Any(), uint32(12345)).Return(bannedUser, nil)

	token, err := cli.GetAcceleratorToken(context.Background(), req)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "已禁用")
	assert.Empty(t, token)
}

func TestGetAcceleratorToken_CacheHit(t *testing.T) {
	cli, f := newHelperForTest(t)
	defer f()

	req := &pb.GetAcceleratorTokenReq{
		Uid:   12345,
		Imei:  "test_imei_12345",
		AppId: "app1",
	}

	// 创建一个有效的用户
	validUser := &entity.AcceleratorUser{
		Id:                  12345,
		UserStatus:          pb.UserStatus_USER_STATUS_NORMAL,
		AuthorizeExpireTime: time.Now().Add(time.Hour * 24),
	}

	cachedToken := "cached_token_123456"

	// 按照实际调用顺序设置mock期望
	cli.mockUserStore.EXPECT().
		GetUser(gomock.Any(), uint32(12345)).Return(validUser, nil)

	// 模拟缓存中有token
	cli.mockCache.EXPECT().
		GetUserAcceleratorToken(gomock.Any(), uint32(12345), "app1").
		Return(cachedToken, nil)

	token, err := cli.GetAcceleratorToken(context.Background(), req)

	assert.NoError(t, err)
	assert.Equal(t, cachedToken, token)
}
