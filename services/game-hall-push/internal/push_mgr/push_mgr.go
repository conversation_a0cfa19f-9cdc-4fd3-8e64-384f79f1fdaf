package push_mgr

import (
	"context"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/channel_path"
	push_label "gitlab.ttyuyin.com/bizFund/bizFund/pkg/push-notification"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/seqgen"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	accountGo "golang.52tt.com/clients/account-go"
	game_card_cli "golang.52tt.com/clients/game-card"
	seqgenV2 "golang.52tt.com/clients/seqgen/v2"
	"golang.52tt.com/clients/topic-channel/tab"
	user_online "golang.52tt.com/clients/user-online"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	game_hall_logic "golang.52tt.com/protocol/app/game-hall-logic"
	"golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/common/status"
	friend_ol_svr_go "golang.52tt.com/protocol/services/friend-ol-svr-go"
	friendolgo "golang.52tt.com/protocol/services/friend-ol-svr-go"
	game_hall "golang.52tt.com/protocol/services/game-hall"
	multi_publisher "golang.52tt.com/protocol/services/multi-publisher"
	olpb "golang.52tt.com/protocol/services/user-online"
	"google.golang.org/protobuf/runtime/protoimpl"

	"google.golang.org/protobuf/types/known/anypb"
	"time"
)

func NewPushMgr(multiPublisherClient multi_publisher.MultiPublisherClient, seqgenV2Client seqgenV2.IClient,
	tabClient tab.IClient, gameCardClient game_card_cli.IClient, userOlClient user_online.IClient,
	gameHallClient game_hall.GameHallClient, accountGoClient accountGo.IClient, friendOlGoClient friendolgo.FriendOlSvrGoClient) *PushMgr {
	return &PushMgr{
		multiPublisherClient: multiPublisherClient,
		seqgenV2Client:       seqgenV2Client,
		tabClient:            tabClient,
		gameCardClient:       gameCardClient,
		userOlClient:         userOlClient,
		gameHallClient:       gameHallClient,
		accountGoClient:      accountGoClient,
		friendOlGoClient:     friendOlGoClient,
	}
}

type PushMgr struct {
	multiPublisherClient multi_publisher.MultiPublisherClient
	seqgenV2Client       seqgenV2.IClient
	gameHallClient       game_hall.GameHallClient
	tabClient            tab.IClient
	gameCardClient       game_card_cli.IClient
	userOlClient         user_online.IClient
	accountGoClient      accountGo.IClient
	friendOlGoClient     friendolgo.FriendOlSvrGoClient
}

func (t *PushMgr) SendCancelAllUidMsg(ctx context.Context, uids []uint32, tabIds []uint32, isNeedSeq bool) error {
	cancelUidMsg := &game_hall_logic.GameBatchCancelMsg{
		UidList: uids,
	}

	marshalMsg, err := proto.Marshal(cancelUidMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "proto.Marshal err:%v, cancelUidMsg:%v", err, cancelUidMsg)
		return err
	}

	for _, tabId := range tabIds {
		channelPath := channel_path.CreateChannelPath(ctx, channel_path.NAMESPACE_GAME_HALL, tabId)
		var seqId uint64
		if isNeedSeq {
			seqId, err = t.seqgenV2Client.GenerateSequence(
				ctx,
				tabId,
				seqgen.NamespaceGameHall,
				seqgen.KeyIm,
				1,
			)
			if err != nil {
				log.ErrorWithCtx(ctx, "seqgenV2Client.GenerateSequence err:%v, tabId:%d", err, tabId)
				return err
			}
		}

		gameImMsg := &game_hall_logic.GameImMsg{
			MsgId:           seqId,
			SendTime:        time.Now().Unix(),
			MsgStatus:       uint32(game_hall_logic.MsgStatus_MSG_STATUS_NORMAL),
			TabId:           tabId,
			MsgAction:       uint32(game_hall_logic.MsgAction_MSG_ACTION_BATCH_CANCEL),
			MsgContentBytes: marshalMsg,
		}

		// 推消息到pushD
		broadcastResp, bcErr := t.pushMsgToPushD(ctx, 0, false, true, channelPath, gameImMsg)

		if bcErr != nil || broadcastResp.GetStatus().Code != multi_publisher.BroadcastStatusCode_BROADCAST_STATUS_CODE_SUCCESS {
			log.ErrorWithCtx(ctx, "SendDelMsg pushMsgToPushD err:%v, resp:%s", bcErr, broadcastResp.String())
			return bcErr
		}
	}

	return nil
}

func (t *PushMgr) SendCleanMsgByTabId(ctx context.Context, tabIds []uint32, isNeedSeq bool) error {
	var err error
	for _, tabId := range tabIds {
		channelPath := channel_path.CreateChannelPath(ctx, channel_path.NAMESPACE_GAME_HALL, tabId)
		var seqId uint64
		if isNeedSeq {
			seqId, err = t.seqgenV2Client.GenerateSequence(
				ctx,
				tabId,
				seqgen.NamespaceGameHall,
				seqgen.KeyIm,
				1,
			)
			if err != nil {
				log.ErrorWithCtx(ctx, "seqgenV2Client.GenerateSequence err:%v, tabId:%d", err, tabId)
				return err
			}
		}

		gameImMsg := &game_hall_logic.GameImMsg{
			MsgId:     seqId,
			SendTime:  time.Now().Unix(),
			MsgStatus: uint32(game_hall_logic.MsgStatus_MSG_STATUS_NORMAL),
			TabId:     tabId,
			MsgAction: uint32(game_hall_logic.MsgAction_MSG_ACTION_CLEAN),
		}

		// 推消息到pushD
		broadcastResp, bcErr := t.pushMsgToPushD(ctx, 0, false, true, channelPath, gameImMsg)
		if bcErr != nil || broadcastResp.GetStatus().Code != multi_publisher.BroadcastStatusCode_BROADCAST_STATUS_CODE_SUCCESS {
			log.ErrorWithCtx(ctx, "SendDelMsg pushMsgToPushD err:%v, resp:%s", bcErr, broadcastResp.String())
			return bcErr
		}
	}

	return nil
}

func (t *PushMgr) SendDelMsg(ctx context.Context, msgId uint64, tabId uint32) error {

	if msgId == 0 || tabId == 0 {
		log.ErrorWithCtx(ctx, "SendDelMsg err param msgId:%d tabId:%s", msgId, tabId)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	channelPath := channel_path.CreateChannelPath(ctx, channel_path.NAMESPACE_GAME_HALL, tabId)
	getMsgReq := &multi_publisher.GetMessagesBySeqIdReq{
		ChannelPath: channelPath,
		SeqIdList:   []uint64{msgId},
	}
	msgRsp, err := t.multiPublisherClient.GetMessagesBySeqId(ctx, getMsgReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendDelMsg multiPublisherClient.GetMessagesBySeqId err:%v, req:%v", getMsgReq.String(), err)
		return err
	}
	msgInfo := &game_hall_logic.GameImMsg{}
	if msg, ok := msgRsp.GetMessageMap()[msgId]; ok {
		err = msg.GetData().UnmarshalTo(protoimpl.X.ProtoMessageV2Of(msgInfo))
		if err != nil {
			log.ErrorWithCtx(ctx, "SendDelMsg msgId:%d channelPath:%s umMarshal err:%+v", msgId, msg.GetChannelPath(), err)
			return err
		}
	} else {
		log.WarnWithCtx(ctx, "SendDelMsg channelPath:%s msgId:%d not found", channelPath, msgId)
		return nil
	}
	msgInfo.MsgStatus = uint32(game_hall_logic.MsgStatus_MSG_STATUS_DELETE)

	resp, err := t.pushMsgToPushD(ctx, msgInfo.GetSenderInfo().GetUid(), false, false, channelPath, msgInfo)
	if err != nil || resp.GetStatus().GetCode() != multi_publisher.BroadcastStatusCode_BROADCAST_STATUS_CODE_SUCCESS {
		log.ErrorWithCtx(ctx, "SendDelMsg pushMsgToPushD err:%v, resp:%s", err, resp.String())
	}
	return err

}

func (t *PushMgr) pushMsgToPushD(ctx context.Context, uid uint32, noPush, noStore bool, channelPath string, gameImMsg *game_hall_logic.GameImMsg) (
	*multi_publisher.BroadcastResp, error) {
	log.InfoWithCtx(ctx, "pushMsgToPushD uid:%d channelPath:%s gameImMsg:%s", uid, channelPath, gameImMsg.String())
	// 推消息到pushD
	anyMsg, err := anypb.New(protoimpl.X.ProtoMessageV2Of(gameImMsg))
	if err != nil {
		log.ErrorWithCtx(ctx, "SendGameImMsg anypb.New err:%v", err)
		return nil, err

	}
	req := &multi_publisher.BroadcastReq{
		Message: &multi_publisher.Message{
			SeqId:       gameImMsg.GetMsgId(),
			ChannelPath: channelPath,
			Sender: &multi_publisher.Sender{
				Uid: uid,
			},
			Data: anyMsg,
		},
		Opt: &multi_publisher.SendOption{
			PushCmdType: uint32(push.PushMessage_GAME_HALL_MSG_PUSH),
			PushLabel:   push_label.LabelGameHallMsgPush,
			NoPush:      noPush,
			NoStore:     noStore,
		},
	}
	broadcastResp, err := t.multiPublisherClient.Broadcast(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushMsgToPushD Broadcast uid:%d  broadcastReq:%s broadcastResp:%s err:%v",
			uid, req.String(), broadcastResp.String(), err)
		return broadcastResp, err
	}
	return broadcastResp, nil
}

func (t *PushMgr) SendScreenShotMsg(ctx context.Context, tabId uint32, msgId uint64) error {
	tempResp, err := t.gameHallClient.GetTempMsg(ctx, &game_hall.GetTempMsgReq{
		MsgId: msgId,
		TabId: tabId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SendScreenShotMsg GetTempMsg err:%v, msgId:%d", err, msgId)
		return err
	}
	if tempResp.GetMsgInfo() == nil || tempResp.GetMsgInfo().GetMsgId() != msgId {
		log.ErrorWithCtx(ctx, "SendScreenShotMsg GetTempMsg msgId:%d not found", msgId)
		return nil
	}
	userMap, err := t.accountGoClient.GetUsersMap(ctx, []uint32{tempResp.GetMsgInfo().GetUid()})
	if err != nil {
		log.ErrorWithCtx(ctx, "SendScreenShotMsg GetUsersMap err:%v, msgId:%d", err, msgId)
		return err
	}
	senderInfoMap, err := t.GenSenderInfo(ctx, userMap, tempResp.GetMsgInfo().GetTabId())
	if err != nil {
		log.ErrorWithCtx(ctx, "SendScreenShotMsg GenSenderInfo err:%v, msgId:%d", err, msgId)
		return err
	}
	gameImMsg := &game_hall_logic.GameImMsg{
		MsgId:           tempResp.GetMsgInfo().GetMsgId(),
		SenderInfo:      senderInfoMap[tempResp.GetMsgInfo().GetUid()],
		MsgType:         tempResp.GetMsgInfo().GetMsgType(),
		SendTime:        tempResp.GetMsgInfo().GetSendTime(),
		MsgStatus:       uint32(game_hall_logic.MsgStatus_MSG_STATUS_NORMAL),
		TabId:           tempResp.GetMsgInfo().GetTabId(),
		MsgAction:       uint32(game_hall_logic.MsgAction_MSG_ACTION_NORMAL),
		MsgContentBytes: tempResp.GetMsgInfo().GetMsgContent(),
	}
	channelPath := channel_path.CreateChannelPath(ctx, channel_path.NAMESPACE_GAME_HALL, gameImMsg.GetTabId())
	broadcastResp, err := t.pushMsgToPushD(ctx, tempResp.GetMsgInfo().GetUid(), false, false, channelPath, gameImMsg)
	if err != nil || broadcastResp.GetStatus().GetCode() != multi_publisher.BroadcastStatusCode_BROADCAST_STATUS_CODE_SUCCESS {
		log.ErrorWithCtx(ctx, "SendDelMsg pushMsgToPushD err:%v, resp:%s", err, broadcastResp.String())
	}
	return err
}

func (t *PushMgr) SendOlStatusMsg(ctx context.Context, tabId uint32, olInfoMap map[uint32]*olpb.OnlineInfo,
	stealthInfoMap map[uint32]*friend_ol_svr_go.StealthInfo) error {
	olInfoMsg := &game_hall_logic.GameOlStatusMsg{
		OlStatusList: make([]*game_hall_logic.GameOlStatusMsg_OlStatusInfo, 0, len(olInfoMap)),
	}
	for uid, olInfo := range olInfoMap {
		olInfoMsg.OlStatusList = append(olInfoMsg.OlStatusList, &game_hall_logic.GameOlStatusMsg_OlStatusInfo{
			Uid:    uid,
			Status: genUserStatusInfo(olInfo, stealthInfoMap[uid]),
		})
	}

	marshalBytes, err := proto.Marshal(olInfoMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendOlStatusMsg Marshal err:%v, olInfoMsg:%v", err, olInfoMsg)
		return err
	}
	gameImMsg := &game_hall_logic.GameImMsg{
		SendTime:        time.Now().Unix(),
		MsgStatus:       uint32(game_hall_logic.MsgStatus_MSG_STATUS_NORMAL),
		TabId:           tabId,
		MsgAction:       uint32(game_hall_logic.MsgAction_MSG_ACTION_ONLINE_STATUS),
		MsgContentBytes: marshalBytes,
	}
	channelPath := channel_path.CreateChannelPath(ctx, channel_path.NAMESPACE_GAME_HALL, tabId)
	broadcastResp, err := t.pushMsgToPushD(ctx, 0, false, true, channelPath, gameImMsg)
	if err != nil || broadcastResp.GetStatus().GetCode() != multi_publisher.BroadcastStatusCode_BROADCAST_STATUS_CODE_SUCCESS {
		log.ErrorWithCtx(ctx, "SendOlStatusMsg pushMsgToPushD err:%v, resp:%s", err, broadcastResp.String())
	}
	return err
}
