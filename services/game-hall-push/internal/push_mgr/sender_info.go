package push_mgr

import (
	"context"
	accountGo "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/log"
	game_hall_logic "golang.52tt.com/protocol/app/game-hall-logic"
	friendolgo "golang.52tt.com/protocol/services/friend-ol-svr-go"
	game_card "golang.52tt.com/protocol/services/game-card"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	userOnlinePb "golang.52tt.com/protocol/services/user-online"
	"strings"
)

const (
	maxGameCardOptNum      = 3
	maxGameCardOptValueNum = 3
)

func (t *PushMgr) GenSenderInfo(ctx context.Context, userInfoMap map[uint32]*accountGo.User, tabId uint32) (map[uint32]*game_hall_logic.GameImUserInfo, error) {
	if len(userInfoMap) == 0 {
		return nil, nil
	}
	uidList := make([]uint32, 0, len(userInfoMap))
	for uid := range userInfoMap {
		uidList = append(uidList, uid)
	}
	gameCardInfoMap, err := t.gameCardClient.BatGetGameCardMap(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "genSenderInfo BatGetGameCardMap failed uid:%v err:%v", uidList, err)
		return nil, err
	}
	tabInfo, err := t.tabClient.GetTabById(ctx, tabId)
	if err != nil {
		log.ErrorWithCtx(ctx, "genSenderInfo GetTabById failed tabId:%d uidList:%v err:%v", tabId, uidList, err)
		return nil, err
	}
	gameCardConfInfos, err1 := t.gameCardClient.GetGameCardConfByCardIdFromCache(ctx, []uint32{tabInfo.GetGameInfo().GetGameCardId()})
	if err1 != nil {
		log.ErrorWithCtx(ctx, "genSenderInfo GetTabById failed tabId:%d err:%v", tabId, err1)
		return nil, err1
	}
	onlineInfoMap, err1 := t.userOlClient.BatchGetLatestOnlineInfoMap(ctx, uidList)
	if err1 != nil {
		log.ErrorWithCtx(ctx, "genSenderInfo GetLatestOnlineInfo failed uidList:%v err:%v", uidList, err1)
		return nil, err1
	}
	stealthResp, err1 := t.friendOlGoClient.GetStealthInfo(ctx, &friendolgo.GetStealthInfoReq{
		UidList: uidList,
	})
	if err1 != nil {
		log.ErrorWithCtx(ctx, "genSenderInfo GetStealthInfo failed uidList:%v err:%v", uidList, err1)
		return nil, err1
	}

	var gameCardConfInfo *game_card.GameCardConfInfo
	if len(gameCardConfInfos) > 0 {
		gameCardConfInfo = gameCardConfInfos[0]
	}
	gameUserInfoMap := make(map[uint32]*game_hall_logic.GameImUserInfo, len(userInfoMap))
	for _, userInfo := range userInfoMap {
		gameCardInfo := gameCardInfoMap[userInfo.GetUid()]
		onlineInfo := onlineInfoMap[userInfo.GetUid()]
		gameUserInfoMap[userInfo.GetUid()] = &game_hall_logic.GameImUserInfo{
			Uid:          userInfo.GetUid(),
			Nickname:     userInfo.GetNickname(),
			GameCardInfo: genGameCardInfo(tabInfo, gameCardInfo, gameCardConfInfo),
			UserStatus:   genUserStatusInfo(onlineInfo, stealthResp.GetStealthInfoMap()[userInfo.GetUid()]),
			Account:      userInfo.GetUsername(),
			Sex:          uint32(userInfo.GetSex()),
		}
	}

	return gameUserInfoMap, nil
}

func genUserStatusInfo(onlineStatus *userOnlinePb.OnlineInfo, stealthInfo *friendolgo.StealthInfo) (res uint32) {

	if stealthInfo != nil && stealthInfo.GetStealthType() == friendolgo.StealthType_STEALTH_TYPE_ON {
		return uint32(userOnlinePb.OnlineType_ONLINE_TYPE_OFFLINE)
	}
	if onlineStatus == nil || onlineStatus.GetOnlineType() != userOnlinePb.OnlineType_ONLINE_TYPE_ONLINE {
		res = uint32(userOnlinePb.OnlineType_ONLINE_TYPE_OFFLINE)
	} else {
		res = uint32(userOnlinePb.OnlineType_ONLINE_TYPE_ONLINE)
	}

	return
}

func genGameCardInfo(tabInfo *tabPB.Tab, gameCardInfo []*game_card.GameCardInfo, gameCardConfInfo *game_card.GameCardConfInfo) *game_hall_logic.GameImUserInfo_GameCardInfo {
	res := &game_hall_logic.GameImUserInfo_GameCardInfo{}
	if len(gameCardInfo) == 0 || tabInfo == nil || gameCardConfInfo == nil {
		return res
	}

	for _, v := range gameCardInfo {
		if tabInfo.GetGameInfo().GetGameCardId() == v.GetGameCardId() {
			res.GameCardText = genGameCardText(v, gameCardConfInfo)
			res.GameCardId = v.GetGameCardId()
			return res
		}
	}
	return res
}

// 展示用户填写了游戏卡中内前2个字段的选项，每个字段最多展示3个选项用“/”隔开
func genGameCardText(gameCardInfo *game_card.GameCardInfo, gameCardConfInfo *game_card.GameCardConfInfo) string {
	texts := make([]string, 0, maxGameCardOptNum)

	var showNum int
	for _, conf := range gameCardConfInfo.GetOptConfList() {
		if showNum >= maxGameCardOptNum {
			break
		}
		if conf.GetOptType() != game_card.GameCardOptType_GAME_CARD_OPT_COMMON && conf.GetOptType() != game_card.GameCardOptType_GAME_CARD_OPT_LEVEL {
			continue
		}
		optList := getGameCardOptListById(gameCardInfo.GetOptList(), conf)
		if len(optList) == 0 {
			continue
		}
		var text string
		if len(optList) > maxGameCardOptValueNum {
			optList = optList[:maxGameCardOptValueNum]
		}
		text = strings.Join(optList, "/")
		texts = append(texts, text)
		showNum++
	}
	return strings.Join(texts, " ")
}

func getGameCardOptListById(userGameCard []*game_card.GameCardOpt, cardConf *game_card.GameCardOptConf) []string {
	for _, userOpt := range userGameCard {
		if userOpt.GetOptId() != cardConf.GetOptId() {
			continue
		}
		res := make([]string, 0, len(userOpt.GetValueList()))
		for _, v := range userOpt.GetValueList() {
			if cardConf.GetOptType() == game_card.GameCardOptType_GAME_CARD_OPT_COMMON {
				if isContainString(cardConf.GetOptConfValueList(), v) {
					res = append(res, v)
				}
			} else {
				if isContainLevel(cardConf.GetLevelConfValueList(), v) {
					res = append(res, v)
				}
			}
		}
		return res
	}
	return nil
}

func isContainString(items []string, item string) bool {
	for _, eachItem := range items {
		if eachItem == item {
			return true
		}
	}
	return false
}

func isContainLevel(items []*game_card.GameCardLevelConf, item string) bool {
	for _, eachItem := range items {
		if eachItem.GetLevelName() == item {
			return true
		}
	}
	return false
}
