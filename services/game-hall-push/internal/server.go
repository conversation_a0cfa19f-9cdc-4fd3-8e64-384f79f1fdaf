package internal

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	accountGo "golang.52tt.com/clients/account-go"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	game_card_cli "golang.52tt.com/clients/game-card"
	im_api "golang.52tt.com/clients/im-api"
	seqgenV2 "golang.52tt.com/clients/seqgen/v2"
	"golang.52tt.com/clients/topic-channel/tab"
	user_online "golang.52tt.com/clients/user-online"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	channel_play_tab "golang.52tt.com/protocol/services/channel-play-tab"
	"golang.52tt.com/protocol/services/demo/echo"
	friendolgo "golang.52tt.com/protocol/services/friend-ol-svr-go"
	game_hall "golang.52tt.com/protocol/services/game-hall"
	game_hall_push "golang.52tt.com/protocol/services/game-hall-push"
	game_screenshot "golang.52tt.com/protocol/services/game-screenshot"
	game_ugc_content "golang.52tt.com/protocol/services/game-ugc-content"
	multi_publisher "golang.52tt.com/protocol/services/multi-publisher"
	"golang.52tt.com/services/game-hall-push/internal/event/sub"
	"golang.52tt.com/services/game-hall-push/internal/event/sub/report"
	"golang.52tt.com/services/game-hall-push/internal/push_mgr"
	"golang.52tt.com/services/game-hall-push/internal/timer"
)

type StartConfig struct {
	// from config file
	ReportCommitKafkaConfig               *config.KafkaConfig       `json:"report_commit_kafka_config"`
	RedisConfig                           *redisConnect.RedisConfig `json:"redis_config"`
	GameHallEntranceLatestMsgPushInterval int                       `json:"game_hall_entrance_latest_msg_push_interval"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	redisClient, err := redisConnect.NewClient(ctx, cfg.RedisConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "init redis fail, err: %v", err)
		return nil, err
	}

	multiPublisherCli, err := multi_publisher.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, " multi_publisher.NewClient(ctx) err :%v", err)
		return nil, err
	}
	seqgenV2Client, err := seqgenV2.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, " seqgenV2.NewClient(ctx) err :%v", err)
		return nil, err
	}
	gameUgcConCli, err := game_ugc_content.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, " game_ugc_content.NewClient(ctx) err :%v", err)
		return nil, err
	}
	gameHallCli, err := game_hall.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, " game_hall.NewClient(ctx) err :%v", err)
		return nil, err
	}
	tabClient, err := tab.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, " tab.NewClient(ctx) err :%v", err)
		return nil, err
	}
	gameCardClient, err := game_card_cli.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, " game_card_cli.NewClient(ctx) err :%v", err)
		return nil, err
	}
	userOlClient, err := user_online.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, " user_online.NewClient(ctx) err :%v", err)
		return nil, err
	}
	accountGoClient, err := accountGo.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, " accountGo.NewClient(ctx) err :%v", err)
		return nil, err
	}
	friendOlGoClient, err := friendolgo.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "friendolgo.NewClient err:%v", err)
		return nil, err
	}
	channelPlayTabCli, err := channel_play_tab.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "channel_play_tab.NewClient() err: %v", err)
		return nil, err
	}
	pushMgr := push_mgr.NewPushMgr(multiPublisherCli, seqgenV2Client, tabClient, gameCardClient, userOlClient,
		gameHallCli, accountGoClient, friendOlGoClient)
	redisTimer := timer.NewRedisTimer(gameUgcConCli, pushMgr, gameHallCli, multiPublisherCli, userOlClient,
		friendOlGoClient, tabClient, channelPlayTabCli, redisClient)
	err = redisTimer.Start(cfg.GameHallEntranceLatestMsgPushInterval)
	if err != nil {
		log.ErrorWithCtx(ctx, "redisTimer.Start err:%v", err)
		return nil, err
	}
	imApiClient, err := im_api.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "im_api.NewClient() err:%v", err)
		return nil, err
	}
	gameScreenShotClient, err := game_screenshot.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "game_screenshot.NewClient() err:%v", err)
		return nil, err
	}
	err = sub.InitAuditResultSubscriber(ctx, pushMgr, imApiClient, tabClient, gameHallCli, gameScreenShotClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitAuditResultSubscriber err:%v", err)
		return nil, err
	}
	censoringClient := censoring_proxy.NewClient()
	multiPublisherClient, err := multi_publisher.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "multi_publisher.NewClient() err:%v", err)
		return nil, err
	}
	reportProcessor := report.NewReportCommitProcessor(censoringClient, multiPublisherClient, tabClient, pushMgr)

	err = sub.InitReportCommitSubscriber(ctx, cfg.ReportCommitKafkaConfig, reportProcessor)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitReportCommitSubscriber err:%v", err)
		return nil, err
	}

	err = sub.InitReportResultSubscriber(ctx, reportProcessor)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitReportResultSubscriber err:%v", err)
		return nil, err
	}

	s := &Server{
		pushMgr:        pushMgr,
		gameHallClient: gameHallCli,
	}

	return s, nil
}

type Server struct {
	pushMgr        push_mgr.IPushMgr
	gameHallClient game_hall.GameHallClient
}

func (s *Server) ShutDown() {

}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) RecallUsersMsg(ctx context.Context, req *game_hall_push.RecallUsersMsgReq) (*game_hall_push.RecallUsersMsgResp, error) {
	out := &game_hall_push.RecallUsersMsgResp{}

	if len(req.GetUidList()) == 0 || len(req.GetTabIds()) == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	err := s.pushMgr.SendCancelAllUidMsg(ctx, req.GetUidList(), req.GetTabIds(), false)
	if err != nil {
		log.ErrorWithCtx(ctx, "RecallUsersMsg SendCancelAllUidMsg req:%s err:%v", req.String(), err)
		return out, err
	}
	records := make([]*game_hall.AddCancelUserMsgRecordReqRecord, 0, len(req.GetUidList())*len(req.GetTabIds()))
	for _, tabId := range req.GetTabIds() {
		for _, uid := range req.GetUidList() {
			records = append(records, &game_hall.AddCancelUserMsgRecordReqRecord{
				Uid:   uid,
				TabId: tabId,
			})
		}
	}
	_, err = s.gameHallClient.AddCancelUserMsgRecord(ctx, &game_hall.AddCancelUserMsgRecordReq{
		Records: records,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "RecallUsersMsg req:%s err:%v", req.String(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "RecallUsersMsg success req:%s", req.String())
	return out, nil
}

func (s *Server) CleanAllMsg(ctx context.Context, req *game_hall_push.CleanAllMsgReq) (*game_hall_push.CleanAllMsgResp, error) {
	out := &game_hall_push.CleanAllMsgResp{}
	log.InfoWithCtx(ctx, "CleanAllMsg req:%s", req.String())
	if len(req.GetTabIds()) == 0 {
		log.WarnWithCtx(ctx, "CleanAllMsg tabIds is empty")
		return out, nil
	}

	_, err := s.gameHallClient.AddCleanMsgRecord(ctx, &game_hall.AddCleanMsgRecordReq{
		TabIds: req.GetTabIds(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CleanAllMsg AddCleanMsgRecord req:%s err:%v", req.String(), err)
		return out, err
	}
	err = s.pushMgr.SendCleanMsgByTabId(ctx, req.GetTabIds(), false)
	if err != nil {
		log.ErrorWithCtx(ctx, "CleanAllMsg SendCleanMsgByTabId req:%s err:%v", req.String(), err)
		return out, err
	}

	return out, nil

}
