package report

import (
	"context"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/audit/report"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/channel_path"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/clients/topic-channel/tab"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	game_hall_logic "golang.52tt.com/protocol/app/game-hall-logic"
	"golang.52tt.com/protocol/common/status"
	v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	game_hall "golang.52tt.com/protocol/services/game-hall"
	multi_publisher "golang.52tt.com/protocol/services/multi-publisher"
	risk_mng_api "golang.52tt.com/protocol/services/risk-mng-api"
	"golang.52tt.com/services/game-hall-push/internal/push_mgr"
	"google.golang.org/protobuf/runtime/protoimpl"
	"google.golang.org/protobuf/types/known/anypb"
	"strconv"
	"strings"
)

type ReportProcessor struct {
	censoringClient      *censoring_proxy.Client
	multiPublisherClient multi_publisher.MultiPublisherClient
	tabClient            *tab.Client

	pushMgr *push_mgr.PushMgr
}

func NewReportCommitProcessor(censoringClient *censoring_proxy.Client, multiPublisherClient multi_publisher.MultiPublisherClient,
	tabClient *tab.Client, pushMgr *push_mgr.PushMgr) *ReportProcessor {
	return &ReportProcessor{
		censoringClient:      censoringClient,
		multiPublisherClient: multiPublisherClient,
		tabClient:            tabClient,
		pushMgr:              pushMgr,
	}
}

func (r *ReportProcessor) HandleReportCommit(ctx context.Context, eventInfo *risk_mng_api.OriginTipOffEvent) error {
	msgId, err := strconv.Atoi(eventInfo.GetBelongObjId())
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleReportCommit invalid id eventInfo:%s err:%+v", eventInfo.String(), err)
		return err
	}
	var tabId uint32
	if tabIdStr, ok := eventInfo.GetExtraParams()["tab_id"]; ok {
		id, err := strconv.Atoi(tabIdStr)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleReportCommit invalid tab_id eventInfo:%s err:%+v", eventInfo.String(), err)
			return err
		}
		tabId = uint32(id)
	} else {
		log.WarnWithCtx(ctx, "HandleReportCommit tab_id not found eventInfo:%s", eventInfo.String())
		return nil
	}

	resp, err := r.multiPublisherClient.GetMessagesBySeqId(ctx, &multi_publisher.GetMessagesBySeqIdReq{
		ChannelPath: channel_path.CreateChannelPath(ctx, channel_path.NAMESPACE_GAME_HALL, tabId),
		SeqIdList:   []uint64{uint64(msgId)},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleReportCommit GetMessagesBySeqId eventInfo:%s err:%+v", eventInfo.String(), err)
		return err
	}
	var msgInfo *multi_publisher.Message
	var ok bool
	if msgInfo, ok = resp.GetMessageMap()[uint64(msgId)]; !ok {
		log.WarnWithCtx(ctx, "HandleReportCommit GetMessagesBySeqId eventInfo:%s msgId:%d not found", eventInfo.String(), msgId)
		return nil
	}
	gameImMsg := &game_hall_logic.GameImMsg{}
	err = msgInfo.GetData().UnmarshalTo(protoimpl.X.ProtoMessageV2Of(gameImMsg))
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleReportCommit msg：%s umMarshal err:%+v", msgInfo.String(), err)
		return err
	}

	asyncScanTipOffReq, err := r.genReportReq(ctx, eventInfo, gameImMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleReportCommit genReportReq eventInfo:%s err:%+v", eventInfo.String(), err)
		return err
	}
	_, err = r.censoringClient.Report().AsyncScanTipOff(ctx, asyncScanTipOffReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleReportCommit AsyncScanTipOff eventInfo:%s asyncScanTipOffReq:%s err:%+v",
			eventInfo.String(), asyncScanTipOffReq.String(), err)
		return err
	}
	log.InfoWithCtx(ctx, "HandleReportCommit event:%s reportReq:%s success", eventInfo.String(), asyncScanTipOffReq.String())
	return nil

}

func (r *ReportProcessor) genReportReq(ctx context.Context, eventInfo *risk_mng_api.OriginTipOffEvent,
	gameImMsg *game_hall_logic.GameImMsg) (*v2.AsyncScanTipOffReq, error) {

	var text string
	var urls []string
	switch game_hall_logic.GameImMsgType(gameImMsg.GetMsgType()) {
	case game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_TEXT:
		textMsg := &game_hall_logic.GameTextMsg{}
		err := proto.Unmarshal(gameImMsg.GetMsgContentBytes(), textMsg)
		if err != nil {
			log.ErrorWithCtx(ctx, "genReportReq Unmarshal textMsg err:%v", err)
			return nil, err
		}
		text = textMsg.GetText()
	case game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_QUOTE:
		quoteMsg := &game_hall_logic.GameQuoteMsg{}
		err := proto.Unmarshal(gameImMsg.GetMsgContentBytes(), quoteMsg)
		if err != nil {
			log.ErrorWithCtx(ctx, "genReportReq Unmarshal quoteMsg err:%v", err)
			return nil, err
		}
		text = quoteMsg.GetText()
	case game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_AT_SOMEONE:
		atMsg := &game_hall_logic.GameAtSomeoneMsg{}
		err := proto.Unmarshal(gameImMsg.GetMsgContentBytes(), atMsg)
		if err != nil {
			log.ErrorWithCtx(ctx, "genReportReq Unmarshal atMsg err:%v", err)
			return nil, err
		}
		text = atMsg.GetText()
	case game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_GAME_SCREENSHOT:
		screenShotMsg := &game_hall_logic.GameScreenshotMsg{}
		err := proto.Unmarshal(gameImMsg.GetMsgContentBytes(), screenShotMsg)
		if err != nil {
			log.ErrorWithCtx(ctx, "genReportReq Unmarshal screenShotMsg err:%v", err)
			return nil, err
		}
		urls = append(urls, screenShotMsg.GetImgUrl())
	case game_hall_logic.GameImMsgType_GAME_IM_MSG_TYPE_EXPRESSION:
		expressionMsg := &game_hall_logic.GameExpressionMsg{}
		err := proto.Unmarshal(gameImMsg.GetMsgContentBytes(), expressionMsg)
		if err != nil {
			log.ErrorWithCtx(ctx, "genReportReq Unmarshal expressionMsg err:%v", err)
			return nil, err
		}
		urls = append(urls, expressionMsg.GetUrl())
	default:
		log.ErrorWithCtx(ctx, "genReportReq invalid unSupport msgType:%d eventInfo:%s msg:%s",
			gameImMsg.GetMsgType(), eventInfo.String(), gameImMsg.String())
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "unSupport msgType")

	}

	tabInfo, serr := r.tabClient.GetTabById(ctx, gameImMsg.GetTabId())
	if serr != nil {
		log.ErrorWithCtx(ctx, "genReportReq GetTabById eventInfo:%s err:%v", eventInfo.String(), serr)
		return nil, serr
	}

	detailInfo, err := anypb.New(protoimpl.X.ProtoMessageV2Of(&game_hall.GameHallReportEventOpt{
		Text:    text,
		ImgUrls: urls,
		TabName: tabInfo.GetName(),
		TabId:   gameImMsg.GetTabId(),
		MsgId:   gameImMsg.GetMsgId(),
	}))
	if err != nil {
		log.ErrorWithCtx(ctx, "genReportReq anypb.New err:%v", err)
		return nil, err
	}
	return &v2.AsyncScanTipOffReq{
		BizCode:     eventInfo.GetBizCode(),
		BelongObjId: eventInfo.GetBelongObjId(),
		ReportUid:   eventInfo.GetReportUid(),
		ReportTime:  eventInfo.GetReportTime(),
		TargetUid:   gameImMsg.GetSenderInfo().GetUid(),
		Context:     eventInfo.GetContext(),
		Detail:      detailInfo,
	}, nil
}

func (r *ReportProcessor) HandleMsgReportResult(ctx context.Context, result *report.ReportResult) error {
	log.InfoWithCtx(ctx, "HandleMsgReportResult result: %s", result.String())
	if v2.ReportStatus(result.GetStatus()) != v2.ReportStatus_REPORT_STATUS_PUNISH {
		return nil
	}

	splitStrings := strings.Split(result.GetContentId(), "_")
	if len(splitStrings) != 2 {
		log.ErrorWithCtx(ctx, "HandleMsgReportResult invalid contentId:%s, result:%s", result.GetContentId(), result.String())
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	msgId, err := strconv.Atoi(splitStrings[0])
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleMsgReportResult invalid msgId result:%s err:%+v", result.String(), err)
		return err
	}
	tabId, err := strconv.Atoi(splitStrings[1])
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleMsgReportResult invalid tabId result:%s err:%+v", result.String(), err)
		return err
	}

	err = r.pushMgr.SendDelMsg(ctx, uint64(msgId), uint32(tabId))
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleMsgReportResult SendDelMsg result:%s err:%+v", result.String(), err)
		return err
	}
	log.InfoWithCtx(ctx, "HandleMsgReportResult result:%s msgId:%d tabId:%d success", result.String(), msgId, tabId)

	return nil
}
