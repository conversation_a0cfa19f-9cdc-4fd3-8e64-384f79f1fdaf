package audit

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	game_hall_logic "golang.52tt.com/protocol/app/game-hall-logic"
	"golang.52tt.com/protocol/common/status"
	censoringPb "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	game_hall "golang.52tt.com/protocol/services/game-hall"
	game_screenshot "golang.52tt.com/protocol/services/game-screenshot"
	im_api "golang.52tt.com/protocol/services/im-api"
	"runtime/debug"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/audit"

	"golang.52tt.com/pkg/log"
)

const (
	screenShotToast    = "很抱歉通知您，您在%s组队大厅发送的图片不符合规则，已删除"
	textToast          = "您在%s组队大厅发送的消息审核不通过，发送失败"
	textAuditThreshold = 3 * 60 * 60
	image              = "image"
	text               = "text"
)

type textAuditParam struct {
	tabId   uint32
	msgId   uint64
	uid     uint32
	auditAt int64
}

type screenShotAuditParam struct {
	tabId uint32
	msgId uint64
	uid   uint32
}

func (p *Processor) genToast(ctx context.Context, tabId uint32, msgType string) (string, error) {
	tabInfo, err := p.tabClient.GetTabById(ctx, tabId)
	if err != nil {
		log.ErrorWithCtx(ctx, "genToast GetTabById tabId:%d err: %v", tabId, err)
		return "", err
	}
	if tabInfo == nil {
		log.WarnWithCtx(ctx, "genToast GetTabById tabId:%d tabInfo nil", tabId)
		return "", protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "tab not found")
	}
	if msgType == image {
		return fmt.Sprintf(screenShotToast, tabInfo.GetName()), nil
	} else {
		return fmt.Sprintf(textToast, tabInfo.GetName()), nil

	}

}

func (p *Processor) HandleTextMsg(ctx context.Context, result *audit.AuditResultInfo) error {
	params, err := parseTextMsgParams(result.Params)
	if err != nil {
		return err
	}
	if result.AuditType != audit.AUDIT_TYPE_MANUAL {
		log.WarnWithCtx(ctx, "HandleTextMsg skip audit type %+v", result)
		return nil
	}
	if result.AuditResult != audit.AUDIT_RESULT_PASS {
		// 撤回、3小时内TT助手推送
		err = p.pushMgr.SendDelMsg(ctx, params.msgId, params.tabId)
		if err != nil {
			return err
		}
		if time.Now().Unix()-params.auditAt <= textAuditThreshold {
			toast, err := p.genToast(ctx, params.tabId, text)
			if err != nil {
				log.ErrorWithCtx(ctx, "HandleTextMsg genToast params:%v result:%+v err: %v", params, result, err)
				return err
			}
			err = p.sendTTAssistantMsg(ctx, params.uid, toast)
			if err != nil {
				log.ErrorWithCtx(ctx, "HandleTextMsg sendTTAssistantMsg params:%v result:%+v err: %v", params, result, err)
				return err
			}
		}
	}

	return nil
}

func (p *Processor) HandleScreenShotMsg(ctx context.Context, result *audit.AuditResultInfo) error {
	params, err := parseScreenShotMsgParams(result.Params)
	if err != nil {
		return err
	}

	if result.AuditResult != audit.AUDIT_RESULT_PASS {
		//TT助手推送
		toast, err := p.genToast(ctx, params.tabId, image)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleTextMsg genToast params:%v result:%+v err: %v", params, result, err)
			return err
		}
		err = p.sendTTAssistantMsg(ctx, params.uid, toast)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleScreenShotMsg sendTTAssistantMsg params:%v result:%+v err: %v", params, result, err)
			return err
		}

		//同步游戏截图审核状态,删记录
		go func() {
			if e := recover(); e != nil {
				log.Errorf("panic errored to DelTempMsg err:%v, stack: %v", e, string(debug.Stack()))
			}
			newCtx, cancel := grpc.NewContextWithInfoTimeout(ctx, 3*time.Second)
			defer cancel()

			tempMsgResp, err := p.gameHallClient.GetTempMsg(newCtx, &game_hall.GetTempMsgReq{
				MsgId: params.msgId,
				TabId: params.tabId,
			})
			if err != nil {
				log.ErrorWithCtx(newCtx, "HandleScreenShotMsg GetTempMsg params:%v result:%+v err: %v", params, result, err)
				return
			}
			msgInfo := &game_hall_logic.GameScreenshotMsg{}
			err = proto.Unmarshal(tempMsgResp.GetMsgInfo().GetMsgContent(), msgInfo)
			if err != nil {
				log.ErrorWithCtx(newCtx, "HandleScreenShotMsg Unmarshal params:%v result:%+v err: %v", params, result, err)
				return
			}
			log.InfoWithCtx(ctx, "HandleScreenShotMsg tempMsgResp:%s msgInfo:%s", tempMsgResp.String(), msgInfo.String())
			_, err = p.gameScreenShotClient.UpdateDIYGameScreenshotStatus(newCtx, &game_screenshot.UpdateDIYGameScreenshotStatusReq{
				Uid:       params.uid,
				TabId:     params.tabId,
				Url:       msgInfo.GetImgUrl(),
				Status:    game_screenshot.GameScreenshotAuditStatus_BANED,
				AuditType: uint32(censoringPb.AuditType_MACHINE),
				TipReason: "组队大厅发送游戏截图审核不通过：" + result.Reason,
			})
			if err != nil {
				log.ErrorWithCtx(newCtx, "HandleScreenShotMsg UpdateDIYGameScreenshotStatus params:%v result:%+v err: %v", params, result, err)
				return
			}

			_, err = p.gameHallClient.DelTempMsg(newCtx, &game_hall.DelTempMsgReq{
				MsgId: params.msgId,
				TabId: params.tabId,
			})
			if err != nil {
				//默认7天过期，删除不成功也问题不大
				log.ErrorWithCtx(newCtx, "HandleScreenShotMsg DelTempMsg params:%v result:%+v err: %v", params, result, err)
				return
			}
		}()
		return nil
	}

	// 审核通过，推消息到pushD
	err = p.pushMgr.SendScreenShotMsg(ctx, params.tabId, params.msgId)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleScreenShotMsg SendScreenShotMsg params:%v result:%+v err: %v", params, result, err)
		return err
	}
	return nil
}

func parseTextMsgParams(params map[string]string) (*textAuditParam, error) {
	if len(params) == 0 {
		log.Errorf("parseTextMsgParams params empty")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "params empty")
	}

	idStr := params["msg_id"]
	if len(idStr) == 0 {
		log.Warnf("parseRoleParams miss id")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss msg_id")
	}
	msgId, err := strconv.Atoi(idStr)
	if err != nil {
		log.Errorf("parseTextMsgParams invalid msg_id %s", idStr)
		return nil, err
	}

	tabIdStr := params["tab_id"]
	if len(tabIdStr) == 0 {
		log.Errorf("parseTextMsgParams miss tab_id")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss tab_id")
	}
	tabId, err := strconv.Atoi(tabIdStr)
	if err != nil {
		log.Errorf("parseTextMsgParams invalid tabId %s", tabIdStr)
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss tab_id")
	}
	uidStr := params["uid"]
	if len(uidStr) == 0 {
		log.Errorf("parseTextMsgParams miss uid")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss uid")
	}
	uid, err := strconv.Atoi(uidStr)
	if err != nil {
		log.Errorf("parseTextMsgParams invalid uid %s", uidStr)
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss uid")
	}

	auditAtStr := params["audit_at"]
	if len(idStr) == 0 {
		log.Warnf("parseRoleParams miss audit_at")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss audit_at")
	}
	auditAt, err := strconv.Atoi(auditAtStr)
	if err != nil {
		log.Errorf("parseTextMsgParams invalid audit_at %s", auditAtStr)
		return nil, err
	}

	return &textAuditParam{
		tabId:   uint32(tabId),
		msgId:   uint64(msgId),
		uid:     uint32(uid),
		auditAt: int64(auditAt),
	}, nil
}

func parseScreenShotMsgParams(params map[string]string) (*screenShotAuditParam, error) {
	if len(params) == 0 {
		log.Errorf("parseTextMsgParams params empty")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "params empty")
	}

	idStr := params["msg_id"]
	if len(idStr) == 0 {
		log.Warnf("parseRoleParams miss id")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss msg_id")
	}
	msgId, err := strconv.Atoi(idStr)
	if err != nil {
		log.Errorf("parseTextMsgParams invalid id %s", idStr)
		return nil, err
	}
	if msgId == 0 {
		log.Errorf("parseTextMsgParams msg_id 0")
		return nil, err
	}

	tabIdStr := params["tab_id"]
	if len(tabIdStr) == 0 {
		log.Errorf("parseTextMsgParams miss tab_id")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss tab_id")
	}
	tabId, err := strconv.Atoi(tabIdStr)
	if err != nil {
		log.Errorf("parseTextMsgParams invalid audit_at %s", tabIdStr)
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss tab_id")
	}
	if tabId == 0 {
		log.Errorf("parseTextMsgParams tabId 0")
		return nil, err
	}

	uidStr := params["uid"]
	if len(uidStr) == 0 {
		log.Errorf("parseTextMsgParams miss uid")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss uid")
	}
	uid, err := strconv.Atoi(uidStr)
	if err != nil {
		log.Errorf("parseTextMsgParams invalid uid %s", uidStr)
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss uid")
	}

	return &screenShotAuditParam{
		tabId: uint32(tabId),
		msgId: uint64(msgId),
		uid:   uint32(uid),
	}, nil
}

func (p *Processor) sendTTAssistantMsg(ctx context.Context, uid uint32, text string) error {
	resp, err := p.imApiClient.SendTTAssistantText(ctx, &im_api.SendTTAssistantTextReq{
		ToUid: uid,
		Text: &im_api.Text{
			Content: text,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "sendTTAssistantMsg SendTTAssistantText uid(%d) text:%s err: %v", uid, text, err)
		return err
	}
	log.InfoWithCtx(ctx, "sendTTAssistantMsg uid:%d success resp:%s", uid, resp.String())
	return nil
}
