package sub

import (
	"context"
	biz_report "gitlab.ttyuyin.com/bizFund/bizFund/pkg/audit/report"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	risk_mng_api "golang.52tt.com/protocol/services/risk-mng-api"
	"golang.52tt.com/services/game-hall-push/internal/event/sub/report"
	"runtime/debug"
)

// ReportCommitSubscriber 提交举报事件消费者
type ReportCommitSubscriber struct {
	processor *report.ReportProcessor
	sub       subscriber.Subscriber
}

func InitReportCommitSubscriber(ctx context.Context, cfg *config.KafkaConfig, processor *report.ReportProcessor) error {

	newSubscriber := &ReportCommitSubscriber{
		processor: processor,
	}
	kafkaConf := kafka.DefaultConfig()
	kafkaConf.ClientID = cfg.ClientID
	kafkaConf.Consumer.Offsets.Initial = kafka.OffsetNewest
	kafkaConf.Consumer.Return.Errors = true
	kafkaSub, err := kafka.NewSubscriber(cfg.BrokerList(), kafkaConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewReportCommitSubscriber channel kafka error: %v", err)
		return err
	}

	err = kafkaSub.SubscribeContext(cfg.GroupID, cfg.TopicList(), subscriber.ProcessorContextFunc(newSubscriber.handleEvent))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewReportCommitSubscriber kafka error: %v", err)
		return err
	} else {
		log.InfoWithCtx(ctx, "NewReportCommitSubscriber suc:%+v", cfg)
	}

	newSubscriber.sub = kafkaSub

	return nil
}

func (r *ReportCommitSubscriber) handleEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (err error, retry bool) {
	defer func() {
		if e := recover(); e != nil {
			log.Errorf("panic errored to handleEvent err:%v, stack: %v", e, string(debug.Stack()))
		}
	}()

	eventInfo := &risk_mng_api.OriginTipOffEvent{}
	err = proto.Unmarshal(msg.Value, eventInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportCommitSubscriber handleEvent Unmarshal error: %v", err)
		return nil, false
	}
	log.InfoWithCtx(ctx, "ReportCommitSubscriber handleEvent eventInfo: %s", eventInfo.String())
	if eventInfo.GetBizCode() != string(biz_report.BIZ_CODE_GAME_HALL_MSG) {
		return nil, false
	}

	err = r.processor.HandleReportCommit(ctx, eventInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportCommitSubscriber HandleReportCommit eventInfo:%s error: %v", eventInfo, err)
		return nil, false
	}
	return nil, false
}
