package timer

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/channel_path"
	bizLabel "gitlab.ttyuyin.com/bizFund/bizFund/pkg/push-notification"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	presence "golang.52tt.com/clients/presence/v2"
	pushNotification "golang.52tt.com/clients/push-notification/v2"
	tab_client "golang.52tt.com/clients/topic-channel/tab"
	user_online "golang.52tt.com/clients/user-online"
	"golang.52tt.com/pkg/cogroup_util"
	"golang.52tt.com/pkg/timer"
	game_hall_logic "golang.52tt.com/protocol/app/game-hall-logic"
	gapush "golang.52tt.com/protocol/app/push"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	channel_play_tab "golang.52tt.com/protocol/services/channel-play-tab"
	friendolgo "golang.52tt.com/protocol/services/friend-ol-svr-go"
	game_hall "golang.52tt.com/protocol/services/game-hall"
	game_ugc_content "golang.52tt.com/protocol/services/game-ugc-content"
	multi_publisher "golang.52tt.com/protocol/services/multi-publisher"
	pushPb "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/game-hall-push/internal/push_mgr"
	"google.golang.org/protobuf/runtime/protoimpl"
	"sync"

	"time"
)

var gameHallEntranceLatestMsgPushProcessing = false

func NewRedisTimer(gameUgcConCli game_ugc_content.GameUgcContentClient, pushMgr *push_mgr.PushMgr, gameHallCli game_hall.GameHallClient,
	multiPublisherClient multi_publisher.MultiPublisherClient, userOlClient user_online.IClient, friendOlGoClient friendolgo.FriendOlSvrGoClient,
	tabClient tab_client.IClient, channelPlayTabCli channel_play_tab.ChannelPlayTabClient, client redis.Client) *RedisTimer {

	pushClient, _ := pushNotification.NewClient()
	presenceClient, _ := presence.NewClient()

	return &RedisTimer{
		gameUgcConCli:        gameUgcConCli,
		pushMgr:              pushMgr,
		gameHallCli:          gameHallCli,
		multiPublisherClient: multiPublisherClient,
		userOlClient:         userOlClient,
		tabClient:            tabClient,
		friendOlGoClient:     friendOlGoClient,
		channelPlayTabCli:    channelPlayTabCli,
		redis:                client,
		pushClient:           pushClient,
		presenceClient:       presenceClient,
	}
}

type RedisTimer struct {
	redis         redis.Client
	gameUgcConCli game_ugc_content.GameUgcContentClient
	pushMgr       push_mgr.IPushMgr
	gameHallCli   game_hall.GameHallClient
	timerD        *timer.Timer

	multiPublisherClient multi_publisher.MultiPublisherClient
	userOlClient         user_online.IClient
	tabClient            tab_client.IClient
	friendOlGoClient     friendolgo.FriendOlSvrGoClient
	channelPlayTabCli    channel_play_tab.ChannelPlayTabClient
	pushClient           *pushNotification.Client
	presenceClient       *presence.Client
}

func (t *RedisTimer) Start(gameHallEntranceLatestMsgPushInterval int) error {
	ctx, cancelFunc := context.WithCancel(context.Background())
	defer cancelFunc()

	timerD, err := timer.NewTimerD(ctx, "game-hall-config")
	if err != nil {
		log.Errorf("NewTimerD err:%v", err)
		return err
	}
	t.timerD = timerD

	every30Sec := "@every 10s"
	err = timerD.AddTask(every30Sec, "GetBanGameHallUser", timer.BuildFromLambda(func(ctx context.Context) {
		_ = t.GetBanGameHallUser(ctx)
	}))
	if err != nil {
		log.Errorf("redis timer GetBanGameHallUser start err:%v", err)
		return err
	}

	every15Sec := "@every 25s"
	err = timerD.AddTask(every15Sec, "UpdateUserOlStatus", timer.BuildFromLambda(func(ctx context.Context) {
		_ = t.HandleUserOlStatus(ctx)
	}))
	if err != nil {
		log.Errorf("redis timer start HandleUserOlStatus err:%v", err)
		return err
	}

	every60Sec := "@every 60s"
	err = timerD.AddTask(every60Sec, "CleanMsgRecord", timer.BuildFromLambda(func(ctx context.Context) {
		_ = t.CleanExpiredMsg(ctx)
	}))
	if err != nil {
		log.Errorf("redis timer start CleanExpiredMsg err:%v", err)
		return err
	}

	if gameHallEntranceLatestMsgPushInterval <= 0 {
		gameHallEntranceLatestMsgPushInterval = 5 // default to 5 seconds
	}
	every5Sec := fmt.Sprintf("@every %ds", gameHallEntranceLatestMsgPushInterval)
	err = timerD.AddTask(every5Sec, "GameHallEntranceLatestMsgPush", timer.BuildFromLambda(func(ctx context.Context) {
		_ = t.GameHallEntranceLatestMsgPush(ctx)
	}))
	if err != nil {
		log.Errorf("redis timer start GameHallEntranceLatestMsgPush err:%v", err)
		return err
	}

	timerD.Start()
	return nil
}

func (t *RedisTimer) Stop() {
	t.timerD.Stop()
}

func (t *RedisTimer) GetBanGameHallUser(ctx context.Context) error {
	now := time.Now()
	log.InfoWithCtx(ctx, "RedisTimer GetNotRateExpiredRecord start")
	defer func() {
		log.InfoWithCtx(ctx, "RedisTimer GetNotRateExpiredRecord end, cost: %v", time.Since(now).String())
	}()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	banGameHallUserRsp, err := t.channelPlayTabCli.GetBanGameHallUser(ctx, &channel_play_tab.GetBanGameHallUserReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "channelPlayTabCli.GetBanGameHallUser err: %v", err)
		return err
	}
	if len(banGameHallUserRsp.GetConfigs()) == 0 {
		return nil
	}
	configTabMapRsp, err := t.gameUgcConCli.GetConfigTabsMapByType(ctx, &game_ugc_content.GetConfigTabsMapByTypeReq{
		ConfigTabType: game_ugc_content.ConfigTabType_ConfigTabType_GameHall})
	if err != nil {
		log.ErrorWithCtx(ctx, "gameUgcConCli.GetConfigTabsMapByType err: %v", err)
		return err
	}

	ids := make([]string, 0, len(banGameHallUserRsp.GetConfigs()))

	addCancelUserReq := &game_hall.AddCancelUserMsgRecordReq{}
	for _, config := range banGameHallUserRsp.GetConfigs() {
		var validTabIds []uint32
		for _, tabId := range config.GetTabIds() {
			if _, ok := configTabMapRsp.GetConfigTabsMap()[tabId]; !ok {
				log.ErrorWithCtx(ctx, "tabId:%d not exist in configTabMap", tabId)
				continue
			} else {
				validTabIds = append(validTabIds, tabId)
				for _, uid := range config.GetUids() {
					record := &game_hall.AddCancelUserMsgRecordReqRecord{
						Uid:   uid,
						TabId: tabId,
					}
					addCancelUserReq.Records = append(addCancelUserReq.Records, record)
				}
			}
		}

		err = t.pushMgr.SendCancelAllUidMsg(ctx, config.GetUids(), validTabIds, false)

		if err == nil {
			_, err = t.gameHallCli.AddCancelUserMsgRecord(ctx, addCancelUserReq)
			if err != nil {
				log.ErrorWithCtx(ctx, "gameHallCli.AddCancelUserMsgRecord err: %v, addCancelUserReq len:%d, "+
					"addCancelUserReq:%v", err, len(addCancelUserReq.GetRecords()), addCancelUserReq.String())
				return err
			}
			ids = append(ids, config.GetId())
		}
	}
	if len(ids) != 0 {
		_, err = t.channelPlayTabCli.UpdateBanGameHallGetStatus(ctx, &channel_play_tab.UpdateBanGameHallGetStatusReq{Ids: ids})
		if err != nil {
			log.ErrorWithCtx(ctx, "channelPlayTabCli.UpdateBanGameHallGetStatus err: %v", err)
			return err
		}
	}

	log.InfoWithCtx(ctx, "GetNotRateExpiredRecord success banGameHallUserRsp:%s addCancelUserReq:%s "+
		"UpdateBanGameHallGetStatus ids:%+v", banGameHallUserRsp.String(), addCancelUserReq.String(), ids)
	return nil
}

func (t *RedisTimer) TestTimer() error {
	go func() {
		ticker := time.NewTicker(time.Second * 10)
		defer func() {
			ticker.Stop()
		}()

		for range ticker.C {
		}

	}()
	return nil
}

func (t *RedisTimer) HandleUserOlStatus(ctx context.Context) error {
	now := time.Now()
	newCtx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()
	log.InfoWithCtx(newCtx, "RedisTimer HandleUserOlStatus start")
	defer func() {
		log.InfoWithCtx(newCtx, "RedisTimer HandleUserOlStatus end, cost: %v", time.Since(now).String())
	}()
	configTabMapRsp, err := t.gameUgcConCli.GetConfigTabsMapByType(newCtx, &game_ugc_content.GetConfigTabsMapByTypeReq{
		ConfigTabType: game_ugc_content.ConfigTabType_ConfigTabType_GameHall,
	})
	if err != nil {
		log.ErrorWithCtx(newCtx, "HandleUserOlStatus gameUgcConCli.GetBanGameHallUser err: %v", err)
		return err
	}
	for tabId := range configTabMapRsp.GetConfigTabsMap() {
		resp, err := t.multiPublisherClient.GetMessages(newCtx, &multi_publisher.GetMessagesReq{
			ChannelPath: channel_path.CreateChannelPath(newCtx, channel_path.NAMESPACE_GAME_HALL, tabId),
			Limit:       100,
			IterType:    multi_publisher.GetMessagesReq_ITER_TYPE_REVERSE,
		})
		if err != nil {
			log.ErrorWithCtx(newCtx, "HandleUserOlStatus multiPublisherClient.GetMessages tabId:%d err: %v", tabId, err)
			continue
		}
		uidList := make([]uint32, 0, len(resp.GetMessages()))
		for _, message := range resp.GetMessages() {
			if message.GetSender().GetUid() != 0 {
				uidList = append(uidList, message.GetSender().GetUid())
			}
		}
		if len(uidList) == 0 {
			log.WarnWithCtx(newCtx, "HandleUserOlStatus uidList is empty tabId:%d", tabId)
			continue
		}
		stealthResp, err := t.friendOlGoClient.GetStealthInfo(newCtx, &friendolgo.GetStealthInfoReq{
			UidList: uidList,
		})
		if err != nil {
			log.ErrorWithCtx(newCtx, "HandleUserOlStatus GetStealthInfo failed uidList:%v err:%v", uidList, err)
			continue
		}

		olInfoMap, err := t.userOlClient.BatchGetLatestOnlineInfoMap(newCtx, uidList)
		if err != nil {
			log.ErrorWithCtx(newCtx, "HandleUserOlStatus userOlClient.BatchGetLatestOnlineInfoMap tabId:%d  err: %v", tabId, err)
			continue
		}
		err = t.pushMgr.SendOlStatusMsg(newCtx, tabId, olInfoMap, stealthResp.GetStealthInfoMap())
		if err != nil {
			log.ErrorWithCtx(newCtx, "HandleUserOlStatus sendOlStatusMsg tabId:%d olInfoMap:%+v err: %v", tabId, olInfoMap, err)
			continue
		}
	}
	return nil
}

func (t *RedisTimer) CleanExpiredMsg(ctx context.Context) error {
	now := time.Now()
	newCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	log.InfoWithCtx(newCtx, "RedisTimer CleanExpiredMsg start")
	defer func() {
		log.InfoWithCtx(newCtx, "RedisTimer CleanExpiredMsg end, cost: %v", time.Since(now).String())
	}()

	tabResp, err := t.tabClient.GetTabsByCategoryEnum(newCtx, &tab.GetTabsByCategoryEnumReq{
		CategoryMapping: uint32(topic_channel.CategoryType_Gangup_type),
	})
	if err != nil {
		log.ErrorWithCtx(newCtx, "CleanExpiredMsg GetTabsByCategoryEnum err:%v", err)
		return err
	}

	tabIds := make([]uint32, 0, len(tabResp.GetTabs()))
	for _, tabInfo := range tabResp.GetTabs() {
		tabIds = append(tabIds, tabInfo.GetId())
	}
	if len(tabIds) == 0 {
		log.ErrorWithCtx(newCtx, "CleanExpiredMsg tabIds is empty")
		return nil
	}
	_, err1 := t.gameHallCli.DelExpireMsgRecordByTabIds(newCtx, &game_hall.DelExpireMsgRecordByTabIdsReq{
		TabIds: tabIds,
	})
	if err1 != nil {
		log.ErrorWithCtx(newCtx, "CleanExpiredMsg DelExpireMsgRecordByTabIds err:%v", err1)
		return err1
	}
	log.InfoWithCtx(newCtx, "CleanExpiredMsg success tabIds:%+v", tabIds)
	return nil
}

func (t *RedisTimer) GameHallEntranceLatestMsgPush(ctx context.Context) error {
	if gameHallEntranceLatestMsgPushProcessing {
		log.InfoWithCtx(ctx, "GameHallEntranceLatestMsgPush is already processing")
		return nil
	}
	now := time.Now()
	gameHallEntranceLatestMsgPushProcessing = true
	newCtx, cancel := context.WithTimeout(ctx, 4500*time.Millisecond)
	defer func() {
		gameHallEntranceLatestMsgPushProcessing = false
		cancel()
		log.InfoWithCtx(newCtx, "GameHallEntranceLatestMsgPush end, cost: %v", time.Since(now).String())
	}()

	// 获取有组队大厅入口的玩法
	configTabMapRsp, err := t.gameUgcConCli.GetConfigTabsMapByType(newCtx, &game_ugc_content.GetConfigTabsMapByTypeReq{
		ConfigTabType: game_ugc_content.ConfigTabType_ConfigTabType_GameHall,
	})
	if err != nil {
		log.ErrorWithCtx(newCtx, "GameHallEntranceLatestMsgPush gameUgcConCli.GetBanGameHallUser err: %v", err)
		return err
	}
	tempTabIds := make([]uint32, 0, len(configTabMapRsp.GetConfigTabsMap()))
	for tabId := range configTabMapRsp.GetConfigTabsMap() {
		tempTabIds = append(tempTabIds, tabId)
	}
	if len(tempTabIds) == 0 {
		log.InfoWithCtx(newCtx, "GameHallEntranceLatestMsgPush tabIds is empty")
		return nil
	}

	// 获取每个玩法的最新一条组队大厅消息，如果最近没有更新则忽略
	latestMsgMap, err := t.getTabLatestMsg(newCtx, tempTabIds)
	if err != nil {
		log.ErrorWithCtx(newCtx, "GameHallEntranceLatestMsgPush getTabLatestMsg err: %v", err)
		return err
	}
	tabIds := make([]uint32, 0)
	latestMsgMap.Range(func(key, value interface{}) bool {
		tabId, ok := key.(uint32)
		if !ok {
			log.WarnWithCtx(newCtx, "GameHallEntranceLatestMsgPush latestMsgMap key is not uint32, key: %v", key)
			return true
		}
		tabIds = append(tabIds, tabId) // 收集所有有最新消息的tabId
		return true
	})

	if len(tabIds) == 0 {
		log.InfoWithCtx(newCtx, "GameHallEntranceLatestMsgPush tabIds is empty after filtering latest messages")
		return nil
	}

	// 获取设置了入口的用户列表，key是tabId，value是该tabId下的用户ID列表
	tabUidsMap, err := t.getUidTabsMap(newCtx, tabIds)
	if err != nil {
		log.ErrorWithCtx(newCtx, "GameHallEntranceLatestMsgPush getUidTabsMap err: %v", err)
		return err
	}
	if len(tabUidsMap) == 0 {
		log.InfoWithCtx(newCtx, "GameHallEntranceLatestMsgPush uidTabsMap is empty")
		return nil
	}

	// 构建用户消息映射，key是用户ID，value是该用户的最新组队大厅消息列表
	userMsgMap := make(map[uint32][]*game_hall_logic.GameImMsg)
	latestMsgMap.Range(func(key, value interface{}) bool {
		tabId, ok := key.(uint32)
		if !ok {
			log.WarnWithCtx(newCtx, "GameHallEntranceLatestMsgPush latestMsgMap key is not uint32, key: %v", key)
			return true
		}
		msg, ok := value.(*game_hall_logic.GameImMsg)
		if !ok {
			log.WarnWithCtx(newCtx, "GameHallEntranceLatestMsgPush latestMsgMap value is not GameImMsg, value: %v", value)
			return true
		}
		if uids, check := tabUidsMap[tabId]; check {
			for _, uid := range uids {
				userMsgMap[uid] = append(userMsgMap[uid], msg) // 添加最新消息
			}
		}
		return true
	})
	if len(userMsgMap) == 0 {
		log.InfoWithCtx(newCtx, "GameHallEntranceLatestMsgPush userMsgMap is empty")
		return nil
	}
	// 推送消息
	_ = t.pushMsg(newCtx, userMsgMap)

	return nil
}

// 用户是否在线业务不处理，推送服务会判断
func (t *RedisTimer) pushMsg(ctx context.Context, userMsgMap map[uint32][]*game_hall_logic.GameImMsg) error {
	now := time.Now()
	defer func() {
		log.InfoWithCtx(ctx, "pushMsg end, cost: %v, len(userMsgMap):%d", time.Since(now).String(), len(userMsgMap))
	}()
	if len(userMsgMap) > 5000 {
		log.WarnWithCtx(ctx, "GameHallEntranceLatestMsgPush pushMsg userMsgMap len:%d is too large, may cause performance issues", len(userMsgMap))
	}
	pushMap := map[uint32]*pushPb.CompositiveNotification{}
	for uid, msgs := range userMsgMap {
		// 在线发送推送
		pushMsg := &game_hall_logic.GameHallShowEntranceNotify{
			Msg: msgs,
		}
		content, err := proto.Marshal(pushMsg)
		if err != nil {
			log.ErrorWithCtx(ctx, "proto.Marshal failed, err:%v, pushMsg:%+v", err, pushMsg)
			return err
		}
		payload, err := proto.Marshal(&gapush.PushMessage{
			Cmd:     uint32(gapush.PushMessage_GAME_HALL_SHOW_ENTRANCE_NOTIFY),
			Content: content,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "proto.Marshal failed, err:%v, pushMsg:%+v", err, pushMsg)
			return err
		}
		msg := &pushPb.CompositiveNotification{
			Sequence:           uint32(time.Now().Unix()),
			TerminalTypePolicy: pushNotification.DefaultPolicy,
			AppId:              0,
			ProxyNotification: &pushPb.ProxyNotification{
				Type:      uint32(pushPb.ProxyNotification_PUSH),
				Payload:   payload,
				PushLabel: bizLabel.LabelGameHallMsgPush,
			},
		}
		pushMap[uid] = msg
		if len(pushMap) >= 1000 {
			resp, err := t.pushClient.PushToUserMap(ctx, pushMap)
			if err != nil {
				log.ErrorWithCtx(ctx, "PushToUserMap failed, err:%v, pushMap:%+v", err, pushMap)
			}
			log.InfoWithCtx(ctx, "PushToUserMap success, resp:%+v, lenPush:%d", resp, len(pushMap))
			pushMap = make(map[uint32]*pushPb.CompositiveNotification)
		}
	}
	if len(pushMap) > 0 {
		resp, err := t.pushClient.PushToUserMap(ctx, pushMap)
		if err != nil {
			log.ErrorWithCtx(ctx, "PushToUserMap failed, err:%v, pushMap:%+v", err, pushMap)
		}
		log.InfoWithCtx(ctx, "PushToUserMap success, resp:%+v, lenPush:%d", resp, len(pushMap))
	}
	return nil
}

// 查询设置了入口的用户，key:tabId, value:uids
func (t *RedisTimer) getUidTabsMap(ctx context.Context, tabIds []uint32) (map[uint32][]uint32, error) {
	now := time.Now()
	defer func() {
		log.InfoWithCtx(ctx, "getUidTabsMap end, cost: %v, len(tabIds):%d", time.Since(now).String(), len(tabIds))
	}()
	resp, err := t.gameHallCli.BatGetShowEntranceSettingByTabIds(ctx, &game_hall.BatGetShowEntranceSettingByTabIdsReq{
		TabIds: tabIds,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPushUids BatGetShowEntranceSettingByTabIds err: %v", err)
		return nil, err
	}
	res := make(map[uint32][]uint32, len(resp.GetEntranceSettingMap()))
	for tabId, info := range resp.GetEntranceSettingMap() {
		res[tabId] = info.GetUids()
	}
	return res, nil
}

func (t *RedisTimer) getOriginMsg(ctx context.Context, channelPath string) ([]*multi_publisher.Message, error) {
	var resMes []*multi_publisher.Message
	req := &multi_publisher.GetMessagesReq{
		ChannelPath: channelPath,
		StartSeqId:  0,
		Limit:       1,
		IterType:    multi_publisher.GetMessagesReq_ITER_TYPE_REVERSE,
	}
	resp, err := t.multiPublisherClient.GetMessages(ctx, req)
	if err != nil {
		return nil, err
	}
	resMes = resp.GetMessages()

	return resMes, nil
}

func (t *RedisTimer) getTabLatestMsg(newCtx context.Context, tabIds []uint32) (*sync.Map, error) {
	now := time.Now()
	defer func() {
		log.InfoWithCtx(newCtx, "getTabLatestMsg end, cost: %v, len(tabIds):%d", time.Since(now).String(), len(tabIds))
	}()

	var res sync.Map
	pool := cogroup_util.NewGoroutinePool(10)
	for _, id := range tabIds {
		pool.Add()
		go func(tabId uint32) {
			ctx, cancel := context.WithTimeout(newCtx, 400*time.Millisecond)
			defer func() {
				pool.Done()
				cancel()
			}()
			channelPath := channel_path.CreateChannelPath(ctx, channel_path.NAMESPACE_GAME_HALL, tabId)
			originMsg, err := t.getOriginMsg(ctx, channelPath)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMsgList GetOriginMsg req:%s, err:%v", channelPath, err)
				return
			}
			if len(originMsg) == 0 {
				return
			}
			latestMsg := originMsg[len(originMsg)-1]
			lastMsg, err := t.getLastMsgFromCache(ctx, tabId)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMsgList GetLastMsgFromCache tabId:%d err:%v", tabId, err)
				return
			}
			if lastMsg.GetSeqId() == latestMsg.GetSeqId() {
				return
			}

			msgInfo := &game_hall_logic.GameImMsg{}
			err = latestMsg.GetData().UnmarshalTo(protoimpl.X.ProtoMessageV2Of(msgInfo))
			if err != nil {
				log.ErrorWithCtx(ctx, "NewFilter UnmarshalTo err:%v, msg:%+v", err, latestMsg)
				return
			}
			log.InfoWithCtx(ctx, "getMsg latestMsg tabId:%d, seqId:%d, msgInfo:%+v", tabId, latestMsg.GetSeqId(), msgInfo)
			res.Store(tabId, msgInfo) // 取最新一条消息
			_ = t.setLastMsgToCache(ctx, tabId, latestMsg)

		}(id)
	}
	pool.Wait()

	return &res, nil
}

func lastMsgKey(tabId uint32) string {
	return fmt.Sprintf("game_hall_latest_msg:%d", tabId)
}

func (t *RedisTimer) getLastMsgFromCache(ctx context.Context, tabId uint32) (*multi_publisher.Message, error) {
	msg := &multi_publisher.Message{}
	key := lastMsgKey(tabId)
	val, err := t.redis.Get(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return msg, nil
		}
		return msg, err
	}
	if val == "" {
		return msg, nil
	}

	err = json.Unmarshal([]byte(val), msg)
	if err != nil {
		return msg, err
	}
	return msg, nil
}

func (t *RedisTimer) setLastMsgToCache(ctx context.Context, tabId uint32, msg *multi_publisher.Message) error {
	key := lastMsgKey(tabId)
	data, err := json.Marshal(msg)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetLastMsgToCache Marshal err:%v, tabId:%d", err, tabId)
		return err
	}
	err = t.redis.Set(ctx, key, data, time.Hour*24*7).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetLastMsgToCache SetCache err:%v, tabId:%d", err, tabId)
		return err
	}
	return nil
}
