package server

import (
	"context"
	"fmt"
	"time"

	"golang.52tt.com/protocol/common/status"

	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/pkg/protocol"

	"golang.52tt.com/clients/currency"
	"golang.52tt.com/clients/exp"
	missionTL "golang.52tt.com/clients/missiontimeline"
	"golang.52tt.com/clients/seqgen/v2"
	Timeline "golang.52tt.com/clients/timeline"
	"golang.52tt.com/pkg/log"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	tlsvr "golang.52tt.com/protocol/services/missiontimelinesvr"
)

type missionHelper struct {
	expCli       *exp.Client
	currCli      *currency.Client
	tlCli        *missionTL.Client
	seqgenCli    *seqgen.Client
	timelineCli  *Timeline.Client
	apicenterCli *apicenter.Client
}

func NewMissionHelperCli(expCli *exp.Client, currCli *currency.Client, tlCli *missionTL.Client, seqgenCli *seqgen.Client, timelineCli *Timeline.Client, apicenterCli *apicenter.Client) *missionHelper {
	return &missionHelper{
		expCli:       expCli,
		currCli:      currCli,
		tlCli:        tlCli,
		seqgenCli:    seqgenCli,
		timelineCli:  timelineCli,
		apicenterCli: apicenterCli,
	}
}

func (m *missionHelper) WriteMissionFinishMessage(ctx context.Context, uid uint32, missionKey string, notifyMask uint32) error {
	//生成seq id
	seq, err := m.seqgenCli.GenerateSequence(ctx, uid, seqgen.NamespaceUser, seqgen.KeyGrowth, 1)
	if err != nil {
		log.Errorf("WriteMissionFinishMessage GenerateSequence err: %s", err.Error())
		return err
	}
	log.Debugf("WriteMissionFinishMessage GenerateSequence seq: %d", seq)

	nowTime := time.Now().Unix()
	msg := &tlsvr.MissionFinishMessage{
		MissionKey: missionKey,
		NotifyMask: notifyMask,
		Timestamp:  uint32(nowTime),
	}
	var msgBin []byte
	if msgBinTmp, err := msg.Marshal(); err == nil {
		msgBin = msgBinTmp
	} else {
		return protocol.NewServerError(status.ErrSys)
	}
	err = m.tlCli.WriteTimeLineMsg(ctx, uid, uint32(tlsvr.MissionTimelineMsg_MISSION_FINISH_MSG), uint32(seq), msgBin)
	if err != nil {
		log.Errorf("WriteMissionFinishMessage ExpCurrencyChanged err: %s", err.Error())
		return err
	}
	return nil
}

func (m *missionHelper) getUserMissionGuild() string {
	return "guild"
}
func (m *missionHelper) WriteMissionGuildMessage(ctx context.Context, uid uint32) error {
	//生成seq id
	seq, err := m.seqgenCli.GenerateSequence(ctx, uid, seqgen.NamespaceUser, seqgen.KeyGrowth, 1)
	if err != nil {
		log.Errorf("WriteMissionGuildMessage GenerateSequence err: %s", err.Error())
		return err
	}
	log.Debugf("WriteMissionGuildMessage GenerateSequence seq: %d", seq)

	msg := &tlsvr.MissionGuideMessage{
		Guide: m.getUserMissionGuild(),
	}
	var msgBin []byte
	if msgBinTmp, err := msg.Marshal(); err == nil {
		msgBin = msgBinTmp
	} else {
		return protocol.NewServerError(status.ErrSys)
	}
	err = m.tlCli.WriteTimeLineMsg(ctx, uid, uint32(tlsvr.MissionTimelineMsg_MISSION_GUIDE_MSG), uint32(seq), msgBin)
	if err != nil {
		log.Errorf("WriteMissionGuildMessage ExpCurrencyChanged err: %s", err.Error())
		return err
	}
	return nil
}

func (m *missionHelper) UpdateUserGrowTimeline(ctx context.Context, uid uint32) error {
	// 获取新的经验等级
	newExp, newLevel, err := m.expCli.GetUserExp(context.Background(), uid)
	if err != nil {
		log.Errorf("GetUserExp err: %s", err.Error())
		return err
	}
	levelStartExp, levelEndExp, _ := m.expCli.GetLevelExpScope(ctx, uid, newLevel)

	// 获取新的红钻
	curr, _ := m.currCli.GetUserCurrency(ctx, uid)

	//生成seq id
	seq, err := m.seqgenCli.GenerateSequence(ctx, uid, seqgen.NamespaceUser, seqgen.KeyGrowth, 1)
	if err != nil {
		log.Errorf("UpdateUserGrowTimeline GenerateSequence err: %s", err.Error())
		return err
	}

	msg := &tlsvr.GrowInfoMessage{
		Exp:                newExp,
		Level:              newLevel,
		CurrentLevelExpMin: levelStartExp,
		CurrentLevelExpMax: levelEndExp,
		Currency:           curr,
	}

	log.Debugf("UpdateUserGrowTimeline GenerateSequence seq: %d", seq)
	err = m.tlCli.ExpCurrencyChanged(ctx, uid, uint32(seq), msg)
	if err != nil {
		log.Errorf("UpdateUserGrowTimeline ExpCurrencyChanged err: %s", err.Error())
		return err
	}
	return nil
}

func (m *missionHelper) WriteOfficalIMMsgToUser(ctx context.Context, toUid, msgtype uint32, toNickname, toUsername, content string) (err error) {
	msg := &apiPB.ImMsg{
		ImType: &apiPB.ImType{
			SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
			ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
			ContentType:  uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT),
		},
		FromUid: uint32(10000),
		ToIdList: []uint32{
			toUid,
		},
		ImContent: &apiPB.ImContent{
			TextNormal: &apiPB.ImTextNormal{Content: content},
		},
		AppPlatform: "all",
		Platform:    apiPB.Platform_UNSPECIFIED,
	}
	log.Debugf("send mission msg %+v", msg)
	err = m.apicenterCli.SendImMsg(context.Background(), toUid, protocol.TT, []*apiPB.ImMsg{msg}, true)
	if err != nil {
		log.Errorf("SendImMsg err: %s", err.Error())
		return err
	}
	return nil
}

func CreateOrderId(userType, uid, maxType, minType uint32, userData string) string {
	return fmt.Sprintf("%d_%d_%d_%d_%s", userType, uid, maxType, minType, userData)
}
