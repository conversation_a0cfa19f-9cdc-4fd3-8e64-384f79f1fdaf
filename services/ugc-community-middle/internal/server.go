package internal

import (
	"context"
	"golang.52tt.com/clients/obsgateway"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	game_red_dot "golang.52tt.com/protocol/services/game-red-dot"
	"golang.52tt.com/protocol/services/rcmd/rcmd_aigc_community"
	"golang.52tt.com/services/ugc-community-middle/internal/breaker"
	"golang.52tt.com/services/ugc-community-middle/internal/event/comment_procesor"
	"golang.52tt.com/services/ugc-community-middle/internal/mgr/comment"
	"golang.52tt.com/services/ugc-community-middle/internal/mgr/notify"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	eventlink_event "gitlab.ttyuyin.com/tt-infra/middleware/event"

	account "golang.52tt.com/clients/account-go"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	"golang.52tt.com/protocol/services/demo/echo"
	ugc_community "golang.52tt.com/protocol/services/ugc-community"
	pb "golang.52tt.com/protocol/services/ugc-community-middle"
	"golang.52tt.com/services/ugc-community-middle/internal/config/ttconfig"
	"golang.52tt.com/services/ugc-community-middle/internal/event"
	"golang.52tt.com/services/ugc-community-middle/internal/event/post_processor"
	"golang.52tt.com/services/ugc-community-middle/internal/mgr"
	"golang.52tt.com/services/ugc-community-middle/internal/mgr/post"
	"golang.52tt.com/services/ugc-community-middle/internal/rpc"
	"golang.52tt.com/services/ugc-community-middle/internal/timer"
	"golang.52tt.com/services/ugc-community-middle/internal/timer/redis"
)

type StartConfig struct {
	// from config file
	Filename string `json:"filename"`

	EventLink *eventlink_event.Options `json:"event_link"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	if err := ttconfig.InitUgcCommunityMiddleConfig(cfg.Filename); err != nil {
		log.ErrorWithCtx(ctx, "InitUgcCommunityMiddleConfig err(%v)", err)
		return nil, err
	}

	factory, err := eventlink_event.NewEventFactory(cfg.EventLink)
	if err != nil {
		log.ErrorWithCtx(ctx, "event.NewEventFactory err(%v)", err)
		return nil, err
	}

	rpcClient, err := rpc.NewClients(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "rpc.NewClients err(%v)", err)
		return nil, err
	}

	commentMgr := comment.NewManager(rpcClient)

	postProcessor := post_processor.NewPostProcessor(rpcClient, commentMgr)
	err = event.InitPostSubscriber(ctx, factory, postProcessor)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitPostSubscriber err(%v)", err)
		return nil, err
	}

	msgPusher := notify.NewMsgPusher(rpcClient)
	postMgr := post.NewManager(rpcClient, msgPusher)
	commonHandle := mgr.NewFeedsCommonHandle(rpcClient.AccountClient, rpcClient.UgcCommunityClient,
		rpcClient.CensoringProxyClient, rpcClient.AigcSoulmateClient, rpcClient.FriendshipClient)

	err = event.StartAuditResultWatcher(ctx, postMgr, rpcClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer StartAuditResultWatcher err(%v)", err)
		return nil, err
	}

	obsgwClient, err := obsgateway.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "obsgateway.NewClient err:%v", err)
		return nil, err
	}

	redisTimer, err := redis.NewRedisTimer(ctx, postMgr, rpcClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewRedisTimer err(%v)", err)
		return nil, err
	}
	redisTimer.Start()

	// 接入熔断
	err = breaker.Setup(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "Setup breaker err(%v)", err)
		return nil, err
	}

	commentProcessor := comment_procesor.NewCommentProcessor(rpcClient, commentMgr)
	err = event.InitCommentSubscriber(ctx, factory, commentProcessor)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitCommentSubscriber err(%v)", err)
		return nil, err
	}

	err = event.InitAIMsgSubscriber(ctx, factory, rpcClient, msgPusher)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitAIMsgSubscriber err(%v)", err)
		return nil, err
	}

	s := &Server{
		timerD: redisTimer,

		accountGoCli:            rpcClient.AccountClient,
		ugcCommunityCli:         rpcClient.UgcCommunityClient,
		aigcSoulmateCli:         rpcClient.AigcSoulmateClient,
		censoringProxyCli:       rpcClient.CensoringProxyClient,
		redDotClient:            rpcClient.GameRedDotClient,
		rcmdAigcCommunityClient: rpcClient.RcmdAigcCommunityClient,
		obsgwClient:             obsgwClient,

		postMgr:           postMgr,
		feedsCommonHandle: commonHandle,
		commentMgr:        commentMgr,
	}

	return s, nil
}

type Server struct {
	timerD timer.Timer

	accountGoCli            account.IClient
	ugcCommunityCli         ugc_community.UgcCommunityClient
	aigcSoulmateCli         aigc_soulmate.AigcSoulmateClient
	censoringProxyCli       censoring_proxy.IClient
	redDotClient            game_red_dot.GameRedDotClient
	rcmdAigcCommunityClient rcmd_aigc_community.RCMDAigcCommunityClient
	obsgwClient             *obsgateway.Client

	postMgr           mgr.PostManager
	feedsCommonHandle *mgr.FeedsCommonHandle
	commentMgr        mgr.CommentManager
}

func (s *Server) ShutDown() {
	if s.timerD != nil {
		s.timerD.Stop()
	}
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) GetNewsFeeds(ctx context.Context, req *pb.GetNewsFeedsReq) (*pb.GetNewsFeedsResp, error) {
	out := &pb.GetNewsFeedsResp{}
	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.ErrorWithCtx(ctx, "GetNewsFeeds err: get uid 0, req:%+v", req)
	}

	var err error
	switch pb.PostSource(req.GetPostSource()) {
	case pb.PostSource_POST_SOURCE_AI_DISTRICT_RECOMMENDATION:
		if ttconfig.GetUgcCommunityMiddleConfig().GetIsSentinelTest(breaker.RcmdFeeds) {
			err = protocol.NewExactServerError(nil, status.ErrSys, breaker.RcmdFeeds+" sentinel test")
			return out, err
		}
		feedsProvider := mgr.NewFeedsRecommend(s.feedsCommonHandle, s.rcmdAigcCommunityClient)
		out.Feeds, out.LoadMore, err = feedsProvider.GetFeeds(ctx, uid, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetNewsFeeds feedsRecommend.GetRecommendFeeds err: %v", err)
			return out, err
		}
	case pb.PostSource_POST_SOURCE_AI_DISTRICT_PERSON:
		feedsProvider := mgr.NewFeedsPerson(s.feedsCommonHandle, s.ugcCommunityCli)
		out.Feeds, out.LoadMore, err = feedsProvider.GetFeeds(ctx, uid, req)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetNewsFeeds feedsPerson.GetPersonFeeds err: %v", err)
			return out, err
		}
	default:
		log.ErrorWithCtx(ctx, "GetNewsFeeds err: invalid post source, req:%+v, uid:%d", req, uid)
		return out, nil
	}

	log.InfoWithCtx(ctx, "GetNewsFeeds req:%+v, resp: %+v", req, out)
	return out, nil
}

func (s *Server) GetPost(ctx context.Context, req *pb.GetPostReq) (*pb.GetPostResp, error) {
	out := &pb.GetPostResp{}

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.ErrorWithCtx(ctx, "GetNewsFeeds err: get uid 0, req:%+v", req)
	}
	commonData, err := s.feedsCommonHandle.GetCommonDatas(ctx, uid, []string{req.GetPostId()}, mgr.GetPostType)
	if err != nil {
		return out, err
	}

	if len(commonData.PostInfos) == 0 {
		log.ErrorWithCtx(ctx, "GetPost err: postInfos len 0, commonData:%+v", commonData)
		return out, protocol.NewExactServerError(nil, status.ErrUgcCommunityPostNotFound)
	}
	pbPostInfo, err := s.feedsCommonHandle.ConvertPost(commonData, req.GetBaseReq(), commonData.PostInfos[0])
	if err != nil {
		return out, err
	}
	out.PostInfo = pbPostInfo

	log.InfoWithCtx(ctx, "GetPost req:%+v, resp: %+v", req, out)
	return out, nil
}
