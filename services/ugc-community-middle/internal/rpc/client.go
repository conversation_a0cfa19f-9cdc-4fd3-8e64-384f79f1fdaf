package rpc

import (
	"context"
	account_go "golang.52tt.com/clients/account-go"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/clients/datacenter"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/ugc/friendship"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	aigc_common "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	game_fre_server "golang.52tt.com/protocol/services/game-fre-server"
	game_red_dot "golang.52tt.com/protocol/services/game-red-dot"
	im_api "golang.52tt.com/protocol/services/im-api"
	"golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner"
	"golang.52tt.com/protocol/services/rcmd/rcmd_aigc_community"
	"golang.52tt.com/protocol/services/rcmd/rcmd_mt_proxy"
	ugc_community "golang.52tt.com/protocol/services/ugc-community"
	"google.golang.org/grpc"
)

type Clients struct {
	ImApiClient             im_api.ImApiClient
	AccountClient           account_go.IClient
	UgcCommunityClient      ugc_community.UgcCommunityClient
	AigcSoulmateClient      aigc_soulmate.AigcSoulmateClient
	CensoringProxyClient    censoring_proxy.IClient
	RcmdAigcCommunityClient rcmd_aigc_community.RCMDAigcCommunityClient
	GameRedDotClient        game_red_dot.GameRedDotClient
	FriendshipClient        friendship.IClient
	RcmdMtProxyClient       rcmd_mt_proxy.RcmdMtProxyClient
	RcmdAiPartnerClient     rcmd_ai_partner.RCMDAIPartnerClient
	AigcCommonClient        aigc_common.AigcCommonClient
	GameFreClient           game_fre_server.GameFreServerClient
	PushClient              PushNotification.IClient
}

func NewClients(ctx context.Context) (*Clients, error) {
	var (
		err     error
		clients = new(Clients)
	)

	clients.ImApiClient, err = im_api.NewClient(ctx)
	if err != nil {
		return nil, err
	}
	clients.AccountClient, err = account_go.NewClient()
	if err != nil {
		return nil, err
	}
	clients.UgcCommunityClient, err = ugc_community.NewClient(ctx)
	if err != nil {
		return nil, err
	}
	clients.AigcSoulmateClient, err = aigc_soulmate.NewClient(ctx)
	if err != nil {
		return nil, err
	}
	clients.RcmdAigcCommunityClient = rcmd_aigc_community.MustNewClientTo(ctx, "rcmd-entrance-server.rcmd-gateway.svc:80") //, grpc.WithAuthority("rcmd-aigc-community.52tt.local")) //, grpc.WithChainUnaryInterceptor(interceptor.NoneStatusAdapterInterceptor()))

	clients.CensoringProxyClient = censoring_proxy.NewClient()

	clients.GameRedDotClient, _ = game_red_dot.NewClient(ctx)

	clients.FriendshipClient, _ = friendship.NewClient()

	clients.RcmdMtProxyClient = rcmd_mt_proxy.MustNewClientTo(ctx, "rcmd-mt-proxy.rcmd-tt.svc:8000", grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor), grpc.WithUnaryInterceptor(datacenter.AdapterInterceptor()))

	clients.RcmdAiPartnerClient, _ = rcmd_ai_partner.NewClient(ctx)

	clients.AigcCommonClient, _ = aigc_common.NewClient(ctx)

	clients.GameFreClient, _ = game_fre_server.NewClient(ctx)

	clients.PushClient = PushNotification.NewIClient()
	return clients, nil
}
