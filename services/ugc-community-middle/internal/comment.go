package internal

import (
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	"golang.52tt.com/pkg/log"
	ugc_community "golang.52tt.com/protocol/services/ugc-community"
	pb "golang.52tt.com/protocol/services/ugc-community-middle"
	"golang.org/x/net/context"
)

func (s *Server) CommentSend(ctx context.Context, req *pb.CommentSendRequest) (*pb.CommentSendResponse, error) {
	out := &pb.CommentSendResponse{}
	err := s.commentMgr.CommentSend(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommentSend CommentSend err:%v, req:%v", err, req)
		return out, err
	}
	return out, nil
}

func (s *Server) CommentFetch(ctx context.Context, req *pb.CommentFetchRequest) (*pb.CommentFetchResponse, error) {
	out := &pb.CommentFetchResponse{}
	comments, loadFinish, err := s.commentMgr.CommentFetch(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommentFetch CommentFetch err:%v, req:%v", err, req)
		return out, err
	}
	out.Comments = comments
	out.IsLoadFinish = loadFinish
	return out, nil
}

func (s *Server) CommentDelete(ctx context.Context, req *pb.CommentDeleteRequest) (*pb.CommentDeleteResponse, error) {
	out := &pb.CommentDeleteResponse{}

	svcInfo := metainfo.GetServiceInfo(ctx)
	log.InfoWithCtx(ctx, "CommentDelete req, svcInfo(%+v) req: %+v", svcInfo, req)

	_, err := s.ugcCommunityCli.CommentDelete(ctx, &ugc_community.CommentDeleteRequest{
		CommentId: req.GetCommentId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CommentDelete CommentDelete err:%v, req:%v", err, req)
		return out, err
	}

	return out, nil
}
