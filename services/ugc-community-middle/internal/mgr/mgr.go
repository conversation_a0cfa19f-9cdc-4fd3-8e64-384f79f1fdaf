package mgr

import (
	"context"
	ugc_community_middle "golang.52tt.com/protocol/services/ugc-community-middle"

	account_go "golang.52tt.com/clients/account-go"
	ugc_community "golang.52tt.com/protocol/services/ugc-community"
)

type PostManager interface {
	CreatePost(ctx context.Context, post *ugc_community.CreatePostRequest_Post) (bool, error)
	ValidateBizData(ctx context.Context, bizData *ugc_community.PostBizData) error

	ScanPost(ctx context.Context, user *account_go.User, post *ugc_community.CreatePostRequest_Post) error
	HandlePostPassed(ctx context.Context, post *ugc_community.Post, isShield bool) error
	HandlePostRejected(ctx context.Context, post *ugc_community.Post) error
	HandlePostGuideTask(ctx context.Context, post *ugc_community.CreatePostRequest_Post, taskToken string)
}

type CommentManager interface {
	CommentSend(ctx context.Context, req *ugc_community_middle.CommentSendRequest) error
	CommentFetch(ctx context.Context, req *ugc_community_middle.CommentFetchRequest) ([]*ugc_community_middle.CommentItem, bool, error)
	GenAIComment(ctx context.Context, uid, roleId uint32, postId, commentId string) (string, error)
}
