package mgr

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/clients/ugc/friendship"
	"strconv"
	"strings"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"

	account "golang.52tt.com/clients/account-go"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	ugc_community "golang.52tt.com/protocol/services/ugc-community"
	pb "golang.52tt.com/protocol/services/ugc-community-middle"
	"golang.52tt.com/services/ugc-community-middle/internal/common"
)

const (
	GetPostType  uint32 = 1
	GetFeedsType uint32 = 2
)

type FeedsCommonHandle struct {
	accountGoCli      account.IClient
	ugcCommunityCli   ugc_community.UgcCommunityClient
	censoringProxyCli censoring_proxy.IClient
	aigcSoulmateCli   aigc_soulmate.AigcSoulmateClient
	friendshipClient  friendship.IClient
}

func NewFeedsCommonHandle(accountGoCli account.IClient, ugcCommunityCli ugc_community.UgcCommunityClient,
	censoringProxyCli censoring_proxy.IClient, aigcSoulmateCli aigc_soulmate.AigcSoulmateClient, friendshipClient friendship.IClient) *FeedsCommonHandle {
	return &FeedsCommonHandle{
		accountGoCli:      accountGoCli,
		ugcCommunityCli:   ugcCommunityCli,
		censoringProxyCli: censoringProxyCli,
		aigcSoulmateCli:   aigcSoulmateCli,
		friendshipClient:  friendshipClient,
	}
}

//type HotComment struct {
//	CommentId  string
//	UserId     uint32
//	Content    string
//	Likes      uint32
//	CreateAt   int64
//	ReplyCount uint32
//}

type FeedsCommonData struct {
	PostInfos       []*ugc_community.Post
	AccountMap      map[uint32]*account.User
	AttitudeMap     map[string]bool
	AiRoleMap       map[uint32]*aigc_soulmate.AIRole
	HotComments     map[string]*ugc_community.CommentItem
	TopicMap        map[string]*ugc_community.TopicInfo
	AICommentCntMap map[string]uint32

	FollowStatusMap map[uint32]bool // key 发帖人uid value 是否关注
}

func (f *FeedsCommonHandle) getSortPosts(postIds []string, rspPosts []*ugc_community.Post) []*ugc_community.Post {
	postMap := make(map[string]*ugc_community.Post)
	for _, post := range rspPosts {
		postMap[post.GetId()] = post
	}
	sortPosts := make([]*ugc_community.Post, 0, len(postIds))
	for _, postId := range postIds {
		sortPosts = append(sortPosts, postMap[postId])
	}
	return sortPosts
}

func (f *FeedsCommonHandle) GetCommonDatas(ctx context.Context, uid uint32, postIds []string, getType uint32) (feedsCommonData *FeedsCommonData, err error) {
	postRsp, err := f.ugcCommunityCli.GetPostList(ctx, &ugc_community.GetPostListRequest{IdList: postIds})
	if err != nil {
		log.Errorf("GetPost err: %v, postids:%v", err, postIds)
		return nil, err
	}
	if len(postRsp.GetList()) == 0 {
		log.Errorf("GetCommonDatas GetPostList idList(%+v) empty", postIds)
		return &FeedsCommonData{}, nil
	}

	attitudeReq := &ugc_community.HadAttitudeRequest{UserId: uid, PostIds: postIds}
	hadAttitudeRsp, err := f.ugcCommunityCli.HadAttitude(ctx, attitudeReq)
	if err != nil {
		log.Errorf("ugcCommunityCli.HadAttitude err: %v, attitudeReq:%+v", err, attitudeReq)
		return nil, err
	}

	roleIds := f.getRoleIds(postRsp.GetList())

	roleInfos, err := f.aigcSoulmateCli.GetAIRoleList(ctx, &aigc_soulmate.GetAIRoleListReq{RoleIdList: roleIds})
	if err != nil {
		log.Errorf("aigcSoulmateCli.GetAIRoleList err: %v, roleIds:%+v", err, roleIds)
		return nil, err
	}

	topicMap, err := f.loadTopic(ctx, postRsp.GetList()...)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommonData loadTopic err: %v", err)
		return nil, err
	}

	sortPosts := f.getSortPosts(postIds, postRsp.GetList())
	feedsCommonData = &FeedsCommonData{
		PostInfos:   sortPosts,
		AttitudeMap: hadAttitudeRsp.GetPostHadAttitudeMap(),
		AiRoleMap:   make(map[uint32]*aigc_soulmate.AIRole),
		TopicMap:    topicMap,
	}

	var uids []uint32
	if getType == GetFeedsType {
		hotCommentRsp, err := f.ugcCommunityCli.GetPostHotComment(ctx, &ugc_community.GetPostHotCommentRequest{PostIds: postIds})
		if err != nil {
			log.Errorf("ugcCommunityCli.GetPostHotComment err: %v, postIds:%+v", err, postIds)
			return nil, err
		}
		feedsCommonData.HotComments = hotCommentRsp.GetHotComments()

		uids = make([]uint32, 0, len(postRsp.GetList())+len(hotCommentRsp.GetHotComments()))
		for _, post := range postRsp.GetList() {
			uids = append(uids, post.GetUid())
		}

		for _, comment := range hotCommentRsp.GetHotComments() {
			uids = append(uids, comment.GetUserId())
		}
	} else {
		uids = make([]uint32, 0, len(postRsp.GetList()))
		for _, post := range postRsp.GetList() {
			uids = append(uids, post.GetUid())
		}
	}

	userMap, err := f.accountGoCli.GetUsersMap(ctx, uids)
	if err != nil {
		log.Errorf("accountGoCli.GetUsersMap err: %v, uids:%v", err, uids)
		return nil, err
	}
	feedsCommonData.AccountMap = userMap
	for _, roleInfo := range roleInfos.GetRoleList() {
		// 审核不过或者私有的角色不展示在帖子列表
		if roleInfo.GetAuditResult() != aigc_soulmate.AuditResult_AuditResultPass || roleInfo.GetState() == aigc_soulmate.AIRoleState_AIRoleStatePrivate {
			continue
		}
		feedsCommonData.AiRoleMap[roleInfo.GetId()] = roleInfo
	}

	followStatusMap, lowErr := f.getFollowStatus(ctx, uid, uids)
	if lowErr != nil {
		// 降级处理
		log.ErrorWithCtx(ctx, "GetCommonDatas getFollowStatus uid:%d uids:%v err: %v", lowErr)
	} else {
		feedsCommonData.FollowStatusMap = followStatusMap
	}

	commentCntMap, lowErr := f.getPostAICommentCntMap(ctx, postRsp.GetList(), uid)
	if lowErr != nil {
		// 降级处理
		log.ErrorWithCtx(ctx, "GetCommonData getPostAICommentCntMap err: %v", err)
	} else {
		feedsCommonData.AICommentCntMap = commentCntMap
	}

	return feedsCommonData, nil
}

func (f *FeedsCommonHandle) getFollowStatus(ctx context.Context, uid uint32, uids []uint32) (map[uint32]bool, error) {

	repeatedMap := make(map[uint32]bool)
	repeatedUids := make([]uint32, 0, len(uids))
	for _, id := range uids {
		if !repeatedMap[id] && id != uid {
			repeatedMap[id] = true
			repeatedUids = append(repeatedUids, id)
		}
	}
	if len(repeatedUids) == 0 {
		return nil, nil
	}
	stateMap := make(map[uint32]bool, len(repeatedUids))
	//get follow status
	followingMap, _, err := f.friendshipClient.BatchGetBiFollowing(ctx, uid, repeatedUids, true, false)
	if err != nil {
		return stateMap, err
	}
	for posterId, following := range followingMap {
		if following != nil {
			stateMap[posterId] = true
		}
	}
	return stateMap, nil
}

func (f *FeedsCommonHandle) getRoleIds(postInfos []*ugc_community.Post) []uint32 {
	var roleIds []uint32
	for _, post := range postInfos {
		if post != nil {
			var aigcCommunityPost = &ugc_community.AigcCommunityPost{}
			if post.GetBizData().GetType() == ugc_community.PostBizType_POST_BIZ_TYPE_AIGC_COMMUNITY {
				if post.GetBizData().GetFormat() == ugc_community.PostBizData_FORMAT_PROTOBUF {
					err := proto.Unmarshal(post.GetBizData().GetBin(), aigcCommunityPost)
					if err != nil {
						log.Errorf("ConvertPost proto.Unmarshal err: %v, post:%+v", err, post)
					} else {
						if aigcCommunityPost.GetRoleId() != 0 {
							roleIds = append(roleIds, aigcCommunityPost.GetRoleId())
						} else {
							log.Errorf("ConvertPost proto.Unmarshal err: aigcCommunityPost.GetRoleId() == 0, post:%+v", post)
						}
					}
				} else if post.GetBizData().GetFormat() == ugc_community.PostBizData_FORMAT_JSON {
					err := json.Unmarshal(post.GetBizData().GetBin(), aigcCommunityPost)
					if err != nil {
						log.Errorf("ConvertPost json.Unmarshal err: %v, post:%+v", err, post)
					} else {
						if aigcCommunityPost.GetRoleId() != 0 {
							roleIds = append(roleIds, aigcCommunityPost.GetRoleId())
						} else {
							log.Errorf("ConvertPost proto.Unmarshal err: aigcCommunityPost.GetRoleId() == 0, post:%+v", post)
						}
					}
				} else {
					log.Warnf("ConvertPost post.GetBizData().GetFormat() err: %v, post:%+v", post.GetBizData().GetFormat(), post)
				}
			}
		}
	}
	return roleIds
}

func (f *FeedsCommonHandle) ConvertPost(feedsCommonData *FeedsCommonData, baseReq *pb.BaseRequest, post *ugc_community.Post) (result *pb.PostInfo, err error) {
	if post != nil {
		if post.GetAuditResult() != ugc_community.AuditResult_AUDIT_RESULT_PASS {
			return nil, protocol.NewExactServerError(nil, status.ErrUgcCommunityPostInvalidCensorResult)
		}
		if post.GetStatus() == ugc_community.PostStatus_POST_STATUS_DELETED {
			return nil, protocol.NewExactServerError(nil, status.ErrUgcCommunityPostNotFound)
		}

		postInfo := &pb.PostInfo{
			PostId:        post.GetId(),
			PostType:      uint32(post.GetType()),
			Content:       post.GetContent(),
			Attachments:   post.GetAttachments(),
			PostTime:      post.GetCreatedAt(),
			CommentCount:  post.GetCommentCount() + feedsCommonData.AICommentCntMap[post.GetId()],
			AttitudeCount: post.GetAttitudeCount(),
			Origin:        uint32(post.GetOrigin()),
			BizType:       uint32(post.GetBizData().GetType()),
			BizData:       make(map[string]string),
			IsAttitude:    feedsCommonData.AttitudeMap[post.GetId()],
			State:         post.GetState(),
			HadFollowed:   feedsCommonData.FollowStatusMap[post.GetUid()],
		}
		if post.GetBizData().GetType() == ugc_community.PostBizType_POST_BIZ_TYPE_AIGC_COMMUNITY {
			var aigcCommunityPost = &ugc_community.AigcCommunityPost{}
			if post.GetBizData().GetFormat() == ugc_community.PostBizData_FORMAT_PROTOBUF {
				err := proto.Unmarshal(post.GetBizData().GetBin(), aigcCommunityPost)
				if err != nil {
					log.Errorf("ConvertPost proto.Unmarshal err: %v, post:%+v", err, post)
				}
			} else if post.GetBizData().GetFormat() == ugc_community.PostBizData_FORMAT_JSON {
				err := json.Unmarshal(post.GetBizData().GetBin(), aigcCommunityPost)
				if err != nil {
					log.Errorf("ConvertPost json.Unmarshal err: %v, post:%+v", err, post)
				}
			} else {
				log.Warnf("ConvertPost post.GetBizData().GetFormat() err: %v, post:%+v", post.GetBizData().GetFormat(), post)
			}

			bizPost := &pb.PostInfo_AigcCommunityBizPost{
				ChatRecords: aigcCommunityPost.GetChatRecords(),
			}
			if aigcCommunityPost.GetRoleId() != 0 && feedsCommonData.AiRoleMap[aigcCommunityPost.GetRoleId()] != nil {
				// 旧协议使用map[string]string，上线后可删除
				postInfo.BizData["role_id"] = strconv.Itoa(int(aigcCommunityPost.GetRoleId()))
				postInfo.BizData["role_name"] = feedsCommonData.AiRoleMap[aigcCommunityPost.GetRoleId()].GetName()
				postInfo.BizData["role_avatar"] = feedsCommonData.AiRoleMap[aigcCommunityPost.GetRoleId()].GetAvatar()
				postInfo.BizData["role_type"] = strconv.Itoa(int(feedsCommonData.AiRoleMap[aigcCommunityPost.GetRoleId()].GetType()))
				postInfo.BizData["role_character"] = feedsCommonData.AiRoleMap[aigcCommunityPost.GetRoleId()].GetCharacter()

				bizPost.RoleId = aigcCommunityPost.GetRoleId()
				bizPost.RoleType = uint32(feedsCommonData.AiRoleMap[aigcCommunityPost.GetRoleId()].GetType())
				bizPost.RoleName = feedsCommonData.AiRoleMap[aigcCommunityPost.GetRoleId()].GetName()
				bizPost.RoleImage = feedsCommonData.AiRoleMap[aigcCommunityPost.GetRoleId()].GetImage()
				bizPost.RoleAvatar = feedsCommonData.AiRoleMap[aigcCommunityPost.GetRoleId()].GetAvatar()
				bizPost.RoleCharacter = feedsCommonData.AiRoleMap[aigcCommunityPost.GetRoleId()].GetCharacter()
			}

			if postInfo.BizBytes, err = json.Marshal(bizPost); err != nil {
				log.Errorf("ConvertPost json.Marshal err: %v, post:%+v", err, post)
			}
		}

		if len(feedsCommonData.HotComments) != 0 {
			commentItem := feedsCommonData.HotComments[post.GetId()]
			if commentItem != nil {
				postInfo.CommentItem = TransCommentToMiddleItem(feedsCommonData.AccountMap[commentItem.GetUserId()], commentItem)
			}
		}
		if postInfo.GetOrigin() != uint32(ugc_community.PostOrigin_POST_ORIGIN_AI_ROLE_HIGH_QUALITY_COMMENT) {
			for _, atta := range postInfo.Attachments {
				atta.Url = common.GetObsUrl(baseReq.GetMarketId(), baseReq.GetClientType(), atta.GetKey())
			}
		}
		postInfo.PostOwner = &pb.UserUgcCommunity{
			Uid:      post.GetUid(),
			Nickname: feedsCommonData.AccountMap[post.GetUid()].GetNickname(),
			Account:  feedsCommonData.AccountMap[post.GetUid()].GetUsername(),
			Alias:    feedsCommonData.AccountMap[post.GetUid()].GetAlias(),
			Gender:   uint32(feedsCommonData.AccountMap[post.GetUid()].GetSex()),
		}

		for _, topicId := range post.GetTopicIdList() {
			if topic, ok := feedsCommonData.TopicMap[topicId]; ok {
				postInfo.TopicList = append(postInfo.TopicList, &pb.TopicInfo{
					Id:   topic.GetId(),
					Type: topic.GetType(),
					Name: topic.GetName(),
				})
			}
		}

		return postInfo, nil
	}
	return nil, protocol.NewExactServerError(nil, status.ErrUgcCommunityPostNotFound)
}

func (f *FeedsCommonHandle) ConvertPosts(feedsCommonData *FeedsCommonData, baseReq *pb.BaseRequest) (result []*pb.PostInfo) {
	if feedsCommonData == nil || len(feedsCommonData.PostInfos) == 0 {
		return
	}
	for _, post := range feedsCommonData.PostInfos {
		postInfo, err := f.ConvertPost(feedsCommonData, baseReq, post)
		if err != nil {
			continue
		}
		result = append(result, postInfo)
	}
	return
}

func (f *FeedsCommonHandle) EncodeTimePostId(time int64, PostId string) string {
	return fmt.Sprintf("%08X-%s", time, PostId)
}

func (f *FeedsCommonHandle) DecodeTimePostId(feedId string) (time int64, postId string) {
	items := strings.Split(feedId, "-")
	if len(items) != 2 {
		return 0, ""
	}
	time, err := strconv.ParseInt(items[0], 16, 64)
	if err != nil {
		return 0, ""
	}
	return time, items[1]
}

func (f *FeedsCommonHandle) loadTopic(ctx context.Context, posts ...*ugc_community.Post) (map[string]*ugc_community.TopicInfo, error) {
	topicMap := make(map[string]*ugc_community.TopicInfo)

	if len(posts) == 0 {
		return topicMap, nil
	}

	var (
		topicIdSet  = make(map[string]bool)
		topicIdList []string
	)
	for _, post := range posts {
		for _, topic := range post.GetTopicIdList() {
			if !topicIdSet[topic] {
				topicIdSet[topic] = true
				topicIdList = append(topicIdList, topic)
			}
		}
	}
	if len(topicIdList) == 0 {
		return topicMap, nil
	}

	req := &ugc_community.BatchGetTopicRequest{
		IdList: topicIdList,
	}
	resp, err := f.ugcCommunityCli.BatchGetTopic(ctx, req)
	if err != nil {
		log.Errorf("loadTopic BatchGetTopic req(%+v) err: %v", req, err)
		return nil, err
	}

	for _, topic := range resp.GetTopics() {
		topicMap[topic.GetId()] = topic
	}

	return topicMap, nil
}

func (f *FeedsCommonHandle) getPostAICommentCntMap(ctx context.Context, posts []*ugc_community.Post, uid uint32) (map[string]uint32, error) {
	resMap := make(map[string]uint32)

	ownPostIds := make([]string, 0)
	for _, post := range posts {
		if post.GetUid() == uid {
			ownPostIds = append(ownPostIds, post.GetId())
		}
	}

	if len(ownPostIds) == 0 {
		return resMap, nil
	}

	resp, err := f.ugcCommunityCli.BatchGetPostAICommentCnt(ctx, &ugc_community.BatchGetPostAICommentCntRequest{PostIds: ownPostIds})
	if err != nil {
		log.ErrorWithCtx(ctx, "getPostAICommentCntMap BatchGetPostAICommentCnt err: %v, ownPostIds:%v, uid:%d", err, ownPostIds, uid)
		return nil, err
	}

	return resp.GetCntMap(), nil
}
