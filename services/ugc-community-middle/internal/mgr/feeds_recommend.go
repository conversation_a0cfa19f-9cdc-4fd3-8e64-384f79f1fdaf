package mgr

import (
	"context"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"golang.52tt.com/protocol/services/rcmd/rcmd_aigc_community"
	pb "golang.52tt.com/protocol/services/ugc-community-middle"
)

type FeedsRecommend struct {
	feedCommonHandle     *FeedsCommonHandle
	aigcCommunityRcmdCli rcmd_aigc_community.RCMDAigcCommunityClient
}

func NewFeedsRecommend(feedCommonHandle *FeedsCommonHandle, aigcCommunityRcmdCli rcmd_aigc_community.RCMDAigcCommunityClient) feedsProvider {
	return &FeedsRecommend{
		feedCommonHandle:     feedCommonHandle,
		aigcCommunityRcmdCli: aigcCommunityRcmdCli,
	}
}

func (f *FeedsRecommend) GetRcmdInfo(ctx context.Context, uid uint32, req *pb.GetNewsFeedsReq) (*rcmd_aigc_community.GetAigcPostsResp, error) {
	var fetMode rcmd_aigc_community.GetAigcPostsReq_FetchMode
	if req.GetGetMode() == uint32(pb.FeedMode_FEED_MODE_NEXT_PAGE) {
		fetMode = rcmd_aigc_community.GetAigcPostsReq_NextPage
	} else {
		fetMode = rcmd_aigc_community.GetAigcPostsReq_Refresh
	}

	aigcPostRsp, err := f.aigcCommunityRcmdCli.GetAigcPosts(ctx, &rcmd_aigc_community.GetAigcPostsReq{
		Uid:           uid,
		FetchMode:     fetMode,
		BrowsePostIds: req.GetBrowsePostIds(),
		PostSubjectId: req.GetSubjectId(),
		RoleId:        req.GetRoleId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNewsFeeds err: %v, req:%+v", err, req)
		return nil, err
	} else if len(aigcPostRsp.GetPostIds()) == 0 {
		if !aigcPostRsp.GetBottomReached() {
			log.ErrorWithCtx(ctx, "GetNewsFeeds err: postids len 0, bottomReached:%t, req:%+v", aigcPostRsp.GetBottomReached(), req)
		} else {
			log.InfoWithCtx(ctx, "GetNewsFeeds: postids len 0, req:%+v, aigcPostRsp:+%v", req, aigcPostRsp)
		}
		return aigcPostRsp, nil
	}
	log.InfoWithCtx(ctx, "GetNewsFeeds GetAigcPosts req(%+v) resp: +%v", req, aigcPostRsp)
	return aigcPostRsp, nil
}

func (f *FeedsRecommend) GetFeeds(ctx context.Context, uid uint32, req *pb.GetNewsFeedsReq) (feeds []*pb.Feed, loadMore *pb.NewFeedsLoadMore, err error) {
	// get recommend feeds
	recommendRsp, err := f.GetRcmdInfo(ctx, uid, req)
	if err != nil {
		return nil, nil, err
	}
	if len(recommendRsp.GetPostIds()) == 0 {
		return feeds, loadMore, nil
	}
	feedsCommonData, err := f.feedCommonHandle.GetCommonDatas(ctx, uid, recommendRsp.GetPostIds(), GetFeedsType)
	if err != nil {
		return nil, nil, err
	}

	var lastFeedId string
	pbPostInfos := f.feedCommonHandle.ConvertPosts(feedsCommonData, req.GetBaseReq())
	for _, post := range pbPostInfos {
		feedId := f.feedCommonHandle.EncodeTimePostId(post.PostTime, post.PostId)
		feeds = append(feeds, &pb.Feed{
			Post:   post,
			FeedId: feedId,
		})
		lastFeedId = feedId
	}

	// 不理会给删除完的帖子出现，并且拉的列表都是这种情况的，len(pbPostInfos)只是为避免推荐返回bottomreached有问题时增加的兜底判断
	if !recommendRsp.GetBottomReached() && len(pbPostInfos) > 0 {
		loadMore = &pb.NewFeedsLoadMore{
			LastFeedId: lastFeedId,
		}
	}

	log.InfoWithCtx(ctx, "GetPost req:%+v, feeds: %+v, loadMore：%v, recommendRsp:%+v", req, feeds, loadMore, recommendRsp)
	return feeds, loadMore, nil
}
