// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-play-tab/internal/dao (interfaces: IMongoDao)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	channel_play_tab "golang.52tt.com/protocol/services/channel-play-tab"
	dao "golang.52tt.com/services/channel-play-tab/internal/dao"
)

// MockIMongoDao is a mock of IMongoDao interface.
type MockIMongoDao struct {
	ctrl     *gomock.Controller
	recorder *MockIMongoDaoMockRecorder
}

// MockIMongoDaoMockRecorder is the mock recorder for MockIMongoDao.
type MockIMongoDaoMockRecorder struct {
	mock *MockIMongoDao
}

// NewMockIMongoDao creates a new mock instance.
func NewMockIMongoDao(ctrl *gomock.Controller) *MockIMongoDao {
	mock := &MockIMongoDao{ctrl: ctrl}
	mock.recorder = &MockIMongoDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIMongoDao) EXPECT() *MockIMongoDaoMockRecorder {
	return m.recorder
}

// AddTabsRealNameConfig mocks base method.
func (m *MockIMongoDao) AddTabsRealNameConfig(arg0 context.Context, arg1 *channel_play_tab.TabsRealNameConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddTabsRealNameConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddTabsRealNameConfig indicates an expected call of AddTabsRealNameConfig.
func (mr *MockIMongoDaoMockRecorder) AddTabsRealNameConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTabsRealNameConfig", reflect.TypeOf((*MockIMongoDao)(nil).AddTabsRealNameConfig), arg0, arg1)
}

// BatchAddBanUserConfig mocks base method.
func (m *MockIMongoDao) BatchAddBanUserConfig(arg0 context.Context, arg1 []*dao.BanUserConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddBanUserConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchAddBanUserConfig indicates an expected call of BatchAddBanUserConfig.
func (mr *MockIMongoDaoMockRecorder) BatchAddBanUserConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddBanUserConfig", reflect.TypeOf((*MockIMongoDao)(nil).BatchAddBanUserConfig), arg0, arg1)
}

// BatchGetMinorSupervisionConfig mocks base method.
func (m *MockIMongoDao) BatchGetMinorSupervisionConfig(arg0 context.Context, arg1, arg2 []uint32) ([]*dao.MinorSupervisionConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetMinorSupervisionConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*dao.MinorSupervisionConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetMinorSupervisionConfig indicates an expected call of BatchGetMinorSupervisionConfig.
func (mr *MockIMongoDaoMockRecorder) BatchGetMinorSupervisionConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetMinorSupervisionConfig", reflect.TypeOf((*MockIMongoDao)(nil).BatchGetMinorSupervisionConfig), arg0, arg1, arg2)
}

// BatchGetNewQuickMatchConfig mocks base method.
func (m *MockIMongoDao) BatchGetNewQuickMatchConfig(arg0 context.Context, arg1 []uint32) ([]*dao.QuickMatchConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetNewQuickMatchConfig", arg0, arg1)
	ret0, _ := ret[0].([]*dao.QuickMatchConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetNewQuickMatchConfig indicates an expected call of BatchGetNewQuickMatchConfig.
func (mr *MockIMongoDaoMockRecorder) BatchGetNewQuickMatchConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetNewQuickMatchConfig", reflect.TypeOf((*MockIMongoDao)(nil).BatchGetNewQuickMatchConfig), arg0, arg1)
}

// BatchGetWhiteUidList mocks base method.
func (m *MockIMongoDao) BatchGetWhiteUidList(arg0 context.Context, arg1 []uint32) ([]*dao.TabWhiteUidListInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetWhiteUidList", arg0, arg1)
	ret0, _ := ret[0].([]*dao.TabWhiteUidListInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetWhiteUidList indicates an expected call of BatchGetWhiteUidList.
func (mr *MockIMongoDaoMockRecorder) BatchGetWhiteUidList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetWhiteUidList", reflect.TypeOf((*MockIMongoDao)(nil).BatchGetWhiteUidList), arg0, arg1)
}

// CreateIndexes mocks base method.
func (m *MockIMongoDao) CreateIndexes(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateIndexes", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateIndexes indicates an expected call of CreateIndexes.
func (mr *MockIMongoDaoMockRecorder) CreateIndexes(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIndexes", reflect.TypeOf((*MockIMongoDao)(nil).CreateIndexes), arg0)
}

// CreateNewTabId mocks base method.
func (m *MockIMongoDao) CreateNewTabId(arg0 context.Context, arg1 string) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNewTabId", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNewTabId indicates an expected call of CreateNewTabId.
func (mr *MockIMongoDaoMockRecorder) CreateNewTabId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNewTabId", reflect.TypeOf((*MockIMongoDao)(nil).CreateNewTabId), arg0, arg1)
}

// DelBanUserConfig mocks base method.
func (m *MockIMongoDao) DelBanUserConfig(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelBanUserConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelBanUserConfig indicates an expected call of DelBanUserConfig.
func (mr *MockIMongoDaoMockRecorder) DelBanUserConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelBanUserConfig", reflect.TypeOf((*MockIMongoDao)(nil).DelBanUserConfig), arg0, arg1)
}

// DelTabInfoExt mocks base method.
func (m *MockIMongoDao) DelTabInfoExt(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelTabInfoExt", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelTabInfoExt indicates an expected call of DelTabInfoExt.
func (mr *MockIMongoDaoMockRecorder) DelTabInfoExt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelTabInfoExt", reflect.TypeOf((*MockIMongoDao)(nil).DelTabInfoExt), arg0, arg1, arg2)
}

// DeleteFastPCCategoryConfig mocks base method.
func (m *MockIMongoDao) DeleteFastPCCategoryConfig(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteFastPCCategoryConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteFastPCCategoryConfig indicates an expected call of DeleteFastPCCategoryConfig.
func (mr *MockIMongoDaoMockRecorder) DeleteFastPCCategoryConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteFastPCCategoryConfig", reflect.TypeOf((*MockIMongoDao)(nil).DeleteFastPCCategoryConfig), arg0, arg1)
}

// DeleteNewTab mocks base method.
func (m *MockIMongoDao) DeleteNewTab(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteNewTab", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteNewTab indicates an expected call of DeleteNewTab.
func (mr *MockIMongoDaoMockRecorder) DeleteNewTab(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNewTab", reflect.TypeOf((*MockIMongoDao)(nil).DeleteNewTab), arg0, arg1)
}

// DeleteQuickMatchConfigById mocks base method.
func (m *MockIMongoDao) DeleteQuickMatchConfigById(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteQuickMatchConfigById", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteQuickMatchConfigById indicates an expected call of DeleteQuickMatchConfigById.
func (mr *MockIMongoDaoMockRecorder) DeleteQuickMatchConfigById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteQuickMatchConfigById", reflect.TypeOf((*MockIMongoDao)(nil).DeleteQuickMatchConfigById), arg0, arg1)
}

// DeleteTabsRealNameConfig mocks base method.
func (m *MockIMongoDao) DeleteTabsRealNameConfig(arg0 context.Context, arg1 uint32, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTabsRealNameConfig", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTabsRealNameConfig indicates an expected call of DeleteTabsRealNameConfig.
func (mr *MockIMongoDaoMockRecorder) DeleteTabsRealNameConfig(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTabsRealNameConfig", reflect.TypeOf((*MockIMongoDao)(nil).DeleteTabsRealNameConfig), arg0, arg1, arg2, arg3)
}

// GetActiveBanUserConfigList mocks base method.
func (m *MockIMongoDao) GetActiveBanUserConfigList(arg0 context.Context, arg1 int64) ([]*dao.BanUserConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveBanUserConfigList", arg0, arg1)
	ret0, _ := ret[0].([]*dao.BanUserConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveBanUserConfigList indicates an expected call of GetActiveBanUserConfigList.
func (mr *MockIMongoDaoMockRecorder) GetActiveBanUserConfigList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveBanUserConfigList", reflect.TypeOf((*MockIMongoDao)(nil).GetActiveBanUserConfigList), arg0, arg1)
}

// GetAllMinorSupervisionConfigs mocks base method.
func (m *MockIMongoDao) GetAllMinorSupervisionConfigs(arg0 context.Context) ([]*dao.MinorSupervisionConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllMinorSupervisionConfigs", arg0)
	ret0, _ := ret[0].([]*dao.MinorSupervisionConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllMinorSupervisionConfigs indicates an expected call of GetAllMinorSupervisionConfigs.
func (mr *MockIMongoDaoMockRecorder) GetAllMinorSupervisionConfigs(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllMinorSupervisionConfigs", reflect.TypeOf((*MockIMongoDao)(nil).GetAllMinorSupervisionConfigs), arg0)
}

// GetAllTabInfoExt mocks base method.
func (m *MockIMongoDao) GetAllTabInfoExt(arg0 context.Context, arg1 uint32) ([]*dao.TabInfoExt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllTabInfoExt", arg0, arg1)
	ret0, _ := ret[0].([]*dao.TabInfoExt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllTabInfoExt indicates an expected call of GetAllTabInfoExt.
func (mr *MockIMongoDaoMockRecorder) GetAllTabInfoExt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTabInfoExt", reflect.TypeOf((*MockIMongoDao)(nil).GetAllTabInfoExt), arg0, arg1)
}

// GetBanGameHallConfig mocks base method.
func (m *MockIMongoDao) GetBanGameHallConfig(arg0 context.Context) ([]*channel_play_tab.BanUserPostConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBanGameHallConfig", arg0)
	ret0, _ := ret[0].([]*channel_play_tab.BanUserPostConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBanGameHallConfig indicates an expected call of GetBanGameHallConfig.
func (mr *MockIMongoDaoMockRecorder) GetBanGameHallConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBanGameHallConfig", reflect.TypeOf((*MockIMongoDao)(nil).GetBanGameHallConfig), arg0)
}

// GetBanUserConfigList mocks base method.
func (m *MockIMongoDao) GetBanUserConfigList(arg0 context.Context, arg1 dao.BanUserConfigFilter) ([]*dao.BanUserConfig, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBanUserConfigList", arg0, arg1)
	ret0, _ := ret[0].([]*dao.BanUserConfig)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetBanUserConfigList indicates an expected call of GetBanUserConfigList.
func (mr *MockIMongoDaoMockRecorder) GetBanUserConfigList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBanUserConfigList", reflect.TypeOf((*MockIMongoDao)(nil).GetBanUserConfigList), arg0, arg1)
}

// GetFastPCCategoryConfig mocks base method.
func (m *MockIMongoDao) GetFastPCCategoryConfig(arg0 context.Context) ([]*dao.FastPCCategoryConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFastPCCategoryConfig", arg0)
	ret0, _ := ret[0].([]*dao.FastPCCategoryConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFastPCCategoryConfig indicates an expected call of GetFastPCCategoryConfig.
func (mr *MockIMongoDaoMockRecorder) GetFastPCCategoryConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFastPCCategoryConfig", reflect.TypeOf((*MockIMongoDao)(nil).GetFastPCCategoryConfig), arg0)
}

// GetFastPCCategoryConfigByType mocks base method.
func (m *MockIMongoDao) GetFastPCCategoryConfigByType(arg0 context.Context, arg1 channel_play_tab.FastPCCategoryType) ([]*dao.FastPCCategoryConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFastPCCategoryConfigByType", arg0, arg1)
	ret0, _ := ret[0].([]*dao.FastPCCategoryConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFastPCCategoryConfigByType indicates an expected call of GetFastPCCategoryConfigByType.
func (mr *MockIMongoDaoMockRecorder) GetFastPCCategoryConfigByType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFastPCCategoryConfigByType", reflect.TypeOf((*MockIMongoDao)(nil).GetFastPCCategoryConfigByType), arg0, arg1)
}

// GetNewTabs mocks base method.
func (m *MockIMongoDao) GetNewTabs(arg0 context.Context, arg1 bool) ([]*dao.NewTab, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewTabs", arg0, arg1)
	ret0, _ := ret[0].([]*dao.NewTab)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewTabs indicates an expected call of GetNewTabs.
func (mr *MockIMongoDaoMockRecorder) GetNewTabs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewTabs", reflect.TypeOf((*MockIMongoDao)(nil).GetNewTabs), arg0, arg1)
}

// GetNewTabsByTabSubType mocks base method.
func (m *MockIMongoDao) GetNewTabsByTabSubType(arg0 context.Context, arg1 uint32) ([]*dao.NewTab, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewTabsByTabSubType", arg0, arg1)
	ret0, _ := ret[0].([]*dao.NewTab)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewTabsByTabSubType indicates an expected call of GetNewTabsByTabSubType.
func (mr *MockIMongoDaoMockRecorder) GetNewTabsByTabSubType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewTabsByTabSubType", reflect.TypeOf((*MockIMongoDao)(nil).GetNewTabsByTabSubType), arg0, arg1)
}

// GetPageTabsRealNameConfigs mocks base method.
func (m *MockIMongoDao) GetPageTabsRealNameConfigs(arg0 context.Context, arg1, arg2 uint32) ([]*dao.TabsRealNameConfig, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPageTabsRealNameConfigs", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*dao.TabsRealNameConfig)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPageTabsRealNameConfigs indicates an expected call of GetPageTabsRealNameConfigs.
func (mr *MockIMongoDaoMockRecorder) GetPageTabsRealNameConfigs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPageTabsRealNameConfigs", reflect.TypeOf((*MockIMongoDao)(nil).GetPageTabsRealNameConfigs), arg0, arg1, arg2)
}

// GetQuickMatchConfig mocks base method.
func (m *MockIMongoDao) GetQuickMatchConfig(arg0 context.Context, arg1, arg2, arg3 uint32) ([]*dao.QuickMatchConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQuickMatchConfig", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*dao.QuickMatchConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQuickMatchConfig indicates an expected call of GetQuickMatchConfig.
func (mr *MockIMongoDaoMockRecorder) GetQuickMatchConfig(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuickMatchConfig", reflect.TypeOf((*MockIMongoDao)(nil).GetQuickMatchConfig), arg0, arg1, arg2, arg3)
}

// GetTabsRealNameConfigs mocks base method.
func (m *MockIMongoDao) GetTabsRealNameConfigs(arg0 context.Context) ([]*dao.TabsRealNameConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabsRealNameConfigs", arg0)
	ret0, _ := ret[0].([]*dao.TabsRealNameConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabsRealNameConfigs indicates an expected call of GetTabsRealNameConfigs.
func (mr *MockIMongoDaoMockRecorder) GetTabsRealNameConfigs(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabsRealNameConfigs", reflect.TypeOf((*MockIMongoDao)(nil).GetTabsRealNameConfigs), arg0)
}

// InsertQuickMatchConfig mocks base method.
func (m *MockIMongoDao) InsertQuickMatchConfig(arg0 context.Context, arg1 *dao.QuickMatchConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertQuickMatchConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertQuickMatchConfig indicates an expected call of InsertQuickMatchConfig.
func (mr *MockIMongoDaoMockRecorder) InsertQuickMatchConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertQuickMatchConfig", reflect.TypeOf((*MockIMongoDao)(nil).InsertQuickMatchConfig), arg0, arg1)
}

// ResortFastPCCategoryConfig mocks base method.
func (m *MockIMongoDao) ResortFastPCCategoryConfig(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResortFastPCCategoryConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResortFastPCCategoryConfig indicates an expected call of ResortFastPCCategoryConfig.
func (mr *MockIMongoDaoMockRecorder) ResortFastPCCategoryConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResortFastPCCategoryConfig", reflect.TypeOf((*MockIMongoDao)(nil).ResortFastPCCategoryConfig), arg0, arg1)
}

// ResortQuickMatchConfig mocks base method.
func (m *MockIMongoDao) ResortQuickMatchConfig(arg0 context.Context, arg1 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResortQuickMatchConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResortQuickMatchConfig indicates an expected call of ResortQuickMatchConfig.
func (mr *MockIMongoDaoMockRecorder) ResortQuickMatchConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResortQuickMatchConfig", reflect.TypeOf((*MockIMongoDao)(nil).ResortQuickMatchConfig), arg0, arg1)
}

// SetWhiteUidList mocks base method.
func (m *MockIMongoDao) SetWhiteUidList(arg0 context.Context, arg1 uint32, arg2 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWhiteUidList", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWhiteUidList indicates an expected call of SetWhiteUidList.
func (mr *MockIMongoDaoMockRecorder) SetWhiteUidList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWhiteUidList", reflect.TypeOf((*MockIMongoDao)(nil).SetWhiteUidList), arg0, arg1, arg2)
}

// UpdateBanGameHallGetStatus mocks base method.
func (m *MockIMongoDao) UpdateBanGameHallGetStatus(arg0 context.Context, arg1 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBanGameHallGetStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateBanGameHallGetStatus indicates an expected call of UpdateBanGameHallGetStatus.
func (mr *MockIMongoDaoMockRecorder) UpdateBanGameHallGetStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBanGameHallGetStatus", reflect.TypeOf((*MockIMongoDao)(nil).UpdateBanGameHallGetStatus), arg0, arg1)
}

// UpdateQuickMatchConfig mocks base method.
func (m *MockIMongoDao) UpdateQuickMatchConfig(arg0 context.Context, arg1 *dao.QuickMatchConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateQuickMatchConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateQuickMatchConfig indicates an expected call of UpdateQuickMatchConfig.
func (mr *MockIMongoDaoMockRecorder) UpdateQuickMatchConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateQuickMatchConfig", reflect.TypeOf((*MockIMongoDao)(nil).UpdateQuickMatchConfig), arg0, arg1)
}

// UpdateTabsRealNameConfig mocks base method.
func (m *MockIMongoDao) UpdateTabsRealNameConfig(arg0 context.Context, arg1 *channel_play_tab.TabsRealNameConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTabsRealNameConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTabsRealNameConfig indicates an expected call of UpdateTabsRealNameConfig.
func (mr *MockIMongoDaoMockRecorder) UpdateTabsRealNameConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTabsRealNameConfig", reflect.TypeOf((*MockIMongoDao)(nil).UpdateTabsRealNameConfig), arg0, arg1)
}

// UpsertBanUserConfig mocks base method.
func (m *MockIMongoDao) UpsertBanUserConfig(arg0 context.Context, arg1 *dao.BanUserConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertBanUserConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertBanUserConfig indicates an expected call of UpsertBanUserConfig.
func (mr *MockIMongoDaoMockRecorder) UpsertBanUserConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertBanUserConfig", reflect.TypeOf((*MockIMongoDao)(nil).UpsertBanUserConfig), arg0, arg1)
}

// UpsertFastPCCategoryConfig mocks base method.
func (m *MockIMongoDao) UpsertFastPCCategoryConfig(arg0 context.Context, arg1 *dao.FastPCCategoryConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertFastPCCategoryConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertFastPCCategoryConfig indicates an expected call of UpsertFastPCCategoryConfig.
func (mr *MockIMongoDaoMockRecorder) UpsertFastPCCategoryConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertFastPCCategoryConfig", reflect.TypeOf((*MockIMongoDao)(nil).UpsertFastPCCategoryConfig), arg0, arg1)
}

// UpsertMinorSupervisionConfig mocks base method.
func (m *MockIMongoDao) UpsertMinorSupervisionConfig(arg0 context.Context, arg1 *dao.MinorSupervisionConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertMinorSupervisionConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertMinorSupervisionConfig indicates an expected call of UpsertMinorSupervisionConfig.
func (mr *MockIMongoDaoMockRecorder) UpsertMinorSupervisionConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertMinorSupervisionConfig", reflect.TypeOf((*MockIMongoDao)(nil).UpsertMinorSupervisionConfig), arg0, arg1)
}

// UpsertNewQuickMatchConfig mocks base method.
func (m *MockIMongoDao) UpsertNewQuickMatchConfig(arg0 context.Context, arg1 *dao.QuickMatchConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertNewQuickMatchConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertNewQuickMatchConfig indicates an expected call of UpsertNewQuickMatchConfig.
func (mr *MockIMongoDaoMockRecorder) UpsertNewQuickMatchConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertNewQuickMatchConfig", reflect.TypeOf((*MockIMongoDao)(nil).UpsertNewQuickMatchConfig), arg0, arg1)
}

// UpsertNewTab mocks base method.
func (m *MockIMongoDao) UpsertNewTab(arg0 context.Context, arg1 *dao.NewTab) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertNewTab", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertNewTab indicates an expected call of UpsertNewTab.
func (mr *MockIMongoDaoMockRecorder) UpsertNewTab(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertNewTab", reflect.TypeOf((*MockIMongoDao)(nil).UpsertNewTab), arg0, arg1)
}

// UpsertTabInfoExt mocks base method.
func (m *MockIMongoDao) UpsertTabInfoExt(arg0 context.Context, arg1, arg2 uint32, arg3 string, arg4 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertTabInfoExt", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertTabInfoExt indicates an expected call of UpsertTabInfoExt.
func (mr *MockIMongoDaoMockRecorder) UpsertTabInfoExt(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertTabInfoExt", reflect.TypeOf((*MockIMongoDao)(nil).UpsertTabInfoExt), arg0, arg1, arg2, arg3, arg4)
}
