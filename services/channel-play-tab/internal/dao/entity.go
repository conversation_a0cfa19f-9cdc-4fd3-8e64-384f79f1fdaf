package dao

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	pb "golang.52tt.com/protocol/services/channel-play-tab"
	"time"
)

type TabWhiteUidListInfo struct {
	TabId        uint32   `json:"tab_id" bson:"_id"`
	WhiteUidList []uint32 `json:"white_uid_list" bson:"white_uid_list"`
	UpdateTime   int64    `json:"update_time" bson:"update_time"`
}

type NewTab struct {
	TabId        uint32 `json:"tab_id" bson:"_id"` // 客户端传参没有tab_sub_type，后续玩法的id可能会跟综合频道冲突，如果有冲突需要删除玩法重新创建
	TabName      string `json:"tab_name" bson:"tab_name"`
	TabSubType   uint32 `json:"tab_sub_type" bson:"tab_sub_type"` // 0-普通玩法，1-综合频道
	ImageUrl     string `json:"image_url" bson:"image_url"`
	UpdateTime   int64  `json:"update_time" bson:"update_time"`
	IsTabVisible bool   `json:"is_tab_visible" bson:"is_tab_visible"`
}

type NewTabIdCreator struct {
	Id    string `bson:"_id"`
	TabId uint32 `bson:"tab_id"`
}

func ConvertTab2Pb(tab *NewTab) *pb.Tab {
	return &pb.Tab{
		TabId:        tab.TabId,
		TabName:      tab.TabName,
		ImageUrl:     tab.ImageUrl,
		TabSubType:   pb.TabSubType(tab.TabSubType),
		IsTabVisible: tab.IsTabVisible,
	}
}

func ConvertPb2Tab(tab *pb.Tab) *NewTab {
	return &NewTab{
		TabId:        tab.GetTabId(),
		TabName:      tab.GetTabName(),
		ImageUrl:     tab.GetImageUrl(),
		TabSubType:   uint32(tab.TabSubType),
		IsTabVisible: tab.GetIsTabVisible(),
	}
}

// 未成年监管开关，拆分成进房和加入游戏
type MinorSupervisionConfig struct {
	Id                    string   `bson:"_id"`
	ConfigType            uint32   `bson:"config_type"`              // 1-玩法的配置,2-分类的配置
	TabId                 uint32   `bson:"tab_id"`                   // 玩法id
	CategoryId            uint32   `bson:"category_id"`              // 分类id,分类和玩法开关冲突时,以玩法的为准
	MarketIds             []uint32 `bson:"market_ids"`               // 需要未成年监管的马甲包id
	SwitchOnScenes        []uint32 `bson:"switch_on_scenes"`         // 开关处于打开状态的场景,1-进房,2-加入游戏
	NeedCheckRegisterTime int64    `bson:"need_check_register_time"` // 单位:ms,这个时间之后注册的用户需要开启未成年监管,为空则不生效
}

func ConvertMinorSupervisionConfigEntity2Pb(config *MinorSupervisionConfig) *pb.MinorSupervisionConfig {
	switchOnScenes := make([]pb.MinorSupervisionScene, 0, len(config.SwitchOnScenes))
	for _, scene := range config.SwitchOnScenes {
		switchOnScenes = append(switchOnScenes, pb.MinorSupervisionScene(scene))
	}
	return &pb.MinorSupervisionConfig{
		Id:                    config.Id,
		TabId:                 config.TabId,
		CategoryId:            config.CategoryId,
		MarketIds:             config.MarketIds,
		SwitchOnScenes:        switchOnScenes,
		NeedCheckRegisterTime: config.NeedCheckRegisterTime,
		ConfigType:            pb.MinorSupervisionConfigType(config.ConfigType),
	}
}

func ConvertMinorSupervisionConfigPb2Entity(config *pb.MinorSupervisionConfig) *MinorSupervisionConfig {
	switchOnScenes := make([]uint32, 0, len(config.GetSwitchOnScenes()))
	for _, scene := range config.GetSwitchOnScenes() {
		switchOnScenes = append(switchOnScenes, uint32(scene))
	}
	return &MinorSupervisionConfig{
		Id:                    config.GetId(),
		TabId:                 config.GetTabId(),
		CategoryId:            config.GetCategoryId(),
		MarketIds:             config.GetMarketIds(),
		SwitchOnScenes:        switchOnScenes,
		NeedCheckRegisterTime: config.GetNeedCheckRegisterTime(),
		ConfigType:            uint32(config.GetConfigType()),
	}
}

type TabsRealNameConfig struct {
	MarketId     uint32   `bson:"market_id"`     // 马甲包id（配置产品）
	CliVer       string   `bson:"cli_ver"`       // 客户端展示的版本，"0.0.0"-全版本
	ChannelPkg   string   `bson:"channel_pkg"`   // 渠道号，"all"-全渠道
	SwitchStatus bool     `bson:"switch_status"` // 实名开关状态，0-关闭（默认），1-开启
	TabIds       []uint32 `bson:"tab_ids"`       // 玩法id
	CategoryIds  []uint32 `bson:"category_ids"`  // 类目id
}

type QuickMatchConfig struct {
	Id         primitive.ObjectID `bson:"_id"`
	TabId      uint32             `bson:"tab_id"`
	CategoryID uint32             `bson:"category_id"`
	TabType    uint32             `bson:"tab_type"`
	Sort       uint32             `bson:"sort"`
	UpdateTime int64              `bson:"update_time"`
	// 1-快速匹配半屏兜底列表；2-快速匹配更多默认列表；3-新版快速匹配配置； 4-休闲互动专区快速匹配外显列表 5-休闲互动专区快速匹配更多列表
	ConfigType uint32 `bson:"config_type"`
	// 新快速匹配入口配置
	TabName    string `bson:"tab_name"`
	Title      string `bson:"title"`       // 快速匹配入口标题
	ButtonText string `bson:"button_text"` // 按钮文案
	Position   uint32 `bson:"position"`    // 在列表的展示位置

	//休闲互动专区快速匹配配置字段
	GameDisplayName string   `bson:"game_display_name"` //快速匹配游戏外显名称
	BgColors        []string `bson:"bg_colors"`         //背景色值
	ButtonEffect    uint32   `bson:"button_effect"`     //按钮效果，快速匹配or短链,1快速匹配，2短链，见channel-play_.proto ButtonEffect
	JumpLink        string   `bson:"jump_link"`         //跳转短链，当button_effect=2时有效

	// 休闲互动专区重点区域配置额外字段
	HotMiniGameExtraInfo *HotMiniGameExtraInfo `bson:"hot_mini_game_extra_info"`
}

type HotMiniGameExtraInfo struct {
	IntroText    string   `bson:"intro_text"`    // 介绍文案
	BgUrl        string   `bson:"bg_url"`        // 背景图 url
	BgColor      string   `bson:"bg_color"`      // 背景颜色
	ButtonText   string   `bson:"button_text"`   // 按钮文案
	ButtonColors []string `bson:"button_colors"` // 按钮颜色组
	LottieUrl    string   `bson:"lottie_url"`    // 动图 lottie url
}

func (c *QuickMatchConfig) CovertToPbSceneInfo() *pb.SceneInfo {
	hotMiniGameExtraInfo := &pb.HotMiniGameExtraInfo{}
	if c.HotMiniGameExtraInfo != nil {
		hotMiniGameExtraInfo = &pb.HotMiniGameExtraInfo{
			IntroText:    c.HotMiniGameExtraInfo.IntroText,
			BgUrl:        c.HotMiniGameExtraInfo.BgUrl,
			BgColor:      c.HotMiniGameExtraInfo.BgColor,
			ButtonText:   c.HotMiniGameExtraInfo.ButtonText,
			ButtonColors: c.HotMiniGameExtraInfo.ButtonColors,
			LottieUrl:    c.HotMiniGameExtraInfo.LottieUrl,
		}
	}
	return &pb.SceneInfo{
		Id:                c.Id.Hex(),
		TabId:             c.TabId,
		TabType:           c.TabType,
		CategoryId:        c.CategoryID,
		ConfigType:        pb.QuickMatchConfigType(c.ConfigType),
		GameDisplayName:   c.GameDisplayName,
		BgColors:          c.BgColors,
		ButtonEffect:      c.ButtonEffect,
		JumpLink:          c.JumpLink,
		MiniGameExtraInfo: hotMiniGameExtraInfo,
	}
}

func (c *QuickMatchConfig) CovertToDao(id primitive.ObjectID, updateTime int64, pbInfo *pb.SceneInfo) {
	c.Id = id
	c.TabId = pbInfo.GetTabId()
	c.CategoryID = pbInfo.GetCategoryId()
	c.TabType = pbInfo.GetTabType()
	c.UpdateTime = updateTime
	c.ConfigType = uint32(pbInfo.GetConfigType())
	c.GameDisplayName = pbInfo.GetGameDisplayName()
	c.BgColors = pbInfo.GetBgColors()
	c.ButtonEffect = pbInfo.GetButtonEffect()
	c.JumpLink = pbInfo.GetJumpLink()
	c.HotMiniGameExtraInfo = &HotMiniGameExtraInfo{
		IntroText:    pbInfo.GetMiniGameExtraInfo().GetIntroText(),
		BgUrl:        pbInfo.GetMiniGameExtraInfo().GetBgUrl(),
		BgColor:      pbInfo.GetMiniGameExtraInfo().GetBgColor(),
		ButtonText:   pbInfo.GetMiniGameExtraInfo().GetButtonText(),
		ButtonColors: pbInfo.GetMiniGameExtraInfo().GetButtonColors(),
		LottieUrl:    pbInfo.GetMiniGameExtraInfo().GetLottieUrl(),
	}
}

type TabInfoExt struct {
	TabId    uint32 `bson:"tab_id" json:"tab_id"`
	ExtType  uint32 `bson:"ext_type" json:"ext_type"`   // see tab.proto TabInfoExtEnum
	ExtInfo  string `bson:"ext_info" json:"ext_info"`   //
	UpdateTs int64  `bson:"update_ts" json:"update_ts"` // 更新时间 ms
	Sort     uint32 `bson:"sort" json:"sort"`           // 排序权重
}

type BanUserConfig struct {
	Id             string   `bson:"_id"`
	BanConfigType  uint32   `bson:"ban_config_type"` // 禁止发帖配置类型: 1-指定用户, 2-指定人群包, 3-指定批量用户
	TTid           string   `bson:"ttid"`            // 用户ttid
	Uid            uint32   `bson:"uid"`             // 用户uid
	CrowdGroupId   string   `bson:"crowd_group_id"`  // 人群包id
	TabIds         []uint32 `bson:"tab_ids"`         // 玩法id列表
	StartTime      int64    `bson:"start_time"`      // 开始时间
	EndTime        int64    `bson:"end_time"`        // 结束时间
	BanReason      string   `bson:"ban_reason"`      // 禁止发帖原因
	OperateTime    int64    `bson:"operate_time"`    // 最后操作时间
	Operator       string   `bson:"operator"`        // 操作人
	BanPostType    uint32   `bson:"ban_post_type"`   // 区分业务类型: 0-禁止专区发帖, 1-禁止发布搭子卡, 2-屏蔽搭子卡, 3-组队大厅，一健撤回
	WarningMessage string   `bson:"warning_message"` // 屏蔽搭子卡配置保存时发tt助手推送警告信息
	IsGet          bool     `bson:"is_get"`          // 是否获取
	TTids          []string `bson:"ttids"`           // 用户ttids
	Uids           []uint32 `bson:"uids"`            // 用户uids
}

func NewBanUserConfig(pbConfig *pb.BanUserPostConfig) *BanUserConfig {
	return &BanUserConfig{
		Id:             pbConfig.GetId(),
		BanConfigType:  uint32(pbConfig.GetConfigType()),
		TTid:           pbConfig.GetTtid(),
		Uid:            pbConfig.GetUid(),
		CrowdGroupId:   pbConfig.GetCrowdGroupId(),
		TabIds:         pbConfig.GetTabIds(),
		StartTime:      pbConfig.GetStartTime(),
		EndTime:        pbConfig.GetEndTime(),
		BanReason:      pbConfig.GetBanReason(),
		OperateTime:    time.Now().UnixMilli(),
		Operator:       pbConfig.GetOperator(),
		BanPostType:    uint32(pbConfig.GetBanPostType()),
		WarningMessage: pbConfig.GetWarningMessage(),
		TTids:          pbConfig.GetTtids(),
		Uids:           pbConfig.GetUids(),
	}
}

func (c *BanUserConfig) ConvertBanUserConfig2Pb() *pb.BanUserPostConfig {
	pbConfig := &pb.BanUserPostConfig{
		Id:             c.Id,
		ConfigType:     pb.BanConfigType(c.BanConfigType),
		Ttid:           c.TTid,
		CrowdGroupId:   c.CrowdGroupId,
		TabIds:         c.TabIds,
		StartTime:      c.StartTime,
		EndTime:        c.EndTime,
		BanReason:      c.BanReason,
		OperateTime:    c.OperateTime,
		Operator:       c.Operator,
		BanPostType:    pb.BanPostType(c.BanPostType),
		Uid:            c.Uid,
		WarningMessage: c.WarningMessage,
		Ttids:          c.TTids,
		Uids:           c.Uids,
	}
	now := time.Now().UnixMilli()

	if now < c.StartTime {
		pbConfig.Status = pb.Status_StatusInEffective
	} else if now > c.EndTime {
		pbConfig.Status = pb.Status_StatusExpired
	} else {
		pbConfig.Status = pb.Status_StatusActive
	}

	return pbConfig
}
