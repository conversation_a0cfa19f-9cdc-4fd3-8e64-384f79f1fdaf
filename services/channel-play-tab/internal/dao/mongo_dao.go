package dao

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channel-play-tab"
	"time"
)

const (
	NewTabIdCreatorType         = "newTabIdCreator"
	FastPcCategoryIdCreatorType = "fastPcCategoryIdCreator"

	initNewTabId = 10000

	StatusActive      = 1 // 禁止配置生效中
	StatusInEffective = 2 // 禁止配置未生效
	StatusExpired     = 3 // 禁止配置已过期
)

type MongoDao struct {
	WhiteUidListCol           *mongo.Collection
	NewTabCol                 *mongo.Collection
	NewTabIdCreatorCol        *mongo.Collection
	MinorSupervisionConfigCol *mongo.Collection
	RealNameConfig            *mongo.Collection
	QuickMatchConfig          *mongo.Collection
	FastPCCategoryConfig      *mongo.Collection
	tabInfoExt                *mongo.Collection
	BanUserConfigCol          *mongo.Collection
}

func NewMongoDao(ctx context.Context, cfg *config.MongoConfig) (*MongoDao, error) {
	client, err := mongo.NewClient(cfg.OptionsForPrimaryPreferred())
	if err != nil {
		log.ErrorWithCtx(ctx, "NewMongoDao NewClient err: (%v)", err)
		return nil, err
	}

	if err = client.Connect(ctx); err != nil {
		log.ErrorWithCtx(ctx, "newServer Connect err: (%v)", err)
		return nil, err
	}

	if err = client.Ping(ctx, readpref.Primary()); err != nil {
		log.ErrorWithCtx(ctx, "newServer Ping err: (%v)", err)
		return nil, err
	}

	whiteUidListCol := client.Database(cfg.Database).Collection("white_uid_list")
	newTabCol := client.Database(cfg.Database).Collection("new_tab")
	newTabIdCreatorCol := client.Database(cfg.Database).Collection("new_tab_id_creator")
	minorSupervisionConfigCol := client.Database(cfg.Database).Collection("minor_supervision_config")
	realNameConfigCol := client.Database(cfg.Database).Collection("tabs_real_name_config")
	quickMatchConfig := client.Database(cfg.Database).Collection("quick_match_config")
	fastPCCategoryConfig := client.Database(cfg.Database).Collection("fast_pc_category_config")
	tabInfoExt := client.Database(cfg.Database).Collection("new_tab_info_ext")
	banUserConfigCol := client.Database(cfg.Database).Collection("ban_post_config")

	mongoDao := &MongoDao{
		WhiteUidListCol:           whiteUidListCol,
		NewTabCol:                 newTabCol,
		NewTabIdCreatorCol:        newTabIdCreatorCol,
		MinorSupervisionConfigCol: minorSupervisionConfigCol,
		RealNameConfig:            realNameConfigCol,
		QuickMatchConfig:          quickMatchConfig,
		FastPCCategoryConfig:      fastPCCategoryConfig,
		tabInfoExt:                tabInfoExt,
		BanUserConfigCol:          banUserConfigCol,
	}

	err = mongoDao.initNewTabIdCreator()
	if err != nil {
		log.Errorf("InitNewTabIdCreator failed %v", err)
		return nil, err
	}

	return mongoDao, nil
}

func (m *MongoDao) CreateIndexes(ctx context.Context) error {
	res, err := m.NewTabCol.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys: bson.D{primitive.E{Key: "tab_sub_type", Value: 1}},
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "NewTabCol CreateIndexes err:%s", err.Error())
		return err
	}
	log.DebugWithCtx(ctx, "NewTabCol createIndexes result:%v", res)
	res, err = m.MinorSupervisionConfigCol.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys: bson.D{primitive.E{Key: "tab_id", Value: 1}},
		},
		{
			Keys: bson.D{primitive.E{Key: "category_id", Value: 1}},
		},
		{
			Keys:    bson.D{primitive.E{Key: "tab_id", Value: 1}, primitive.E{Key: "category_id", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "MinorSupervisionConfigCol CreateIndexes err:%s", err.Error())
		return err
	}
	log.DebugWithCtx(ctx, "MinorSupervisionConfigCol createIndexes result:%v", res)

	realNameConfigRes, err := m.RealNameConfig.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{
			primitive.E{Key: "market_id", Value: 1},
			primitive.E{Key: "channel_pkg", Value: 1},
			primitive.E{Key: "cli_ver", Value: 1},
		},
		Options: options.Index().SetUnique(true),
	})
	if err != nil {
		log.WarnWithCtx(ctx, "realNameConfig createIndexes err: (%v)", err)
	}
	log.DebugWithCtx(ctx, "realNameConfig createIndexes result: (%v)", realNameConfigRes)

	quickMatchConfigRes, err := m.QuickMatchConfig.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys: bson.D{
				primitive.E{Key: "config_type", Value: 1},
				primitive.E{Key: "tab_id", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{
				primitive.E{Key: "config_type", Value: 1},
				primitive.E{Key: "sort", Value: 1},
				primitive.E{Key: "update_time", Value: -1},
			},
			Options: options.Index(),
		},
	})
	if err != nil {
		log.WarnWithCtx(ctx, "quickMatchConfig createIndexes err: (%v)", err)
	}
	log.DebugWithCtx(ctx, "quickMatchConfig createIndexes result: (%v)", quickMatchConfigRes)

	fastPCCategoryConfigRes, err := m.FastPCCategoryConfig.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys: bson.D{
				primitive.E{Key: "category_id", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{
				primitive.E{Key: "name", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
	})
	if err != nil {
		log.WarnWithCtx(ctx, "fast_pc_category_config createIndexes err: (%v)", err)
	}
	log.DebugWithCtx(ctx, "fast_pc_category_config createIndexes result: (%v)", fastPCCategoryConfigRes)

	tabInfoExtRes, err := m.tabInfoExt.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys: bson.D{
				primitive.E{Key: "ext_type", Value: 1},
				primitive.E{Key: "tab_id", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "tabInfoExt createIndexes err: (%v), res:%v", err, tabInfoExtRes)
	}

	banConfigRes, err := m.BanUserConfigCol.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys: bson.D{primitive.E{Key: "ttid", Value: 1}},
		},
		{
			Keys: bson.D{primitive.E{Key: "ttids", Value: 1}},
		},
		{
			Keys: bson.D{primitive.E{Key: "operate_time", Value: -1}},
		},
		{
			Keys: bson.D{primitive.E{Key: "start_time", Value: 1}, primitive.E{Key: "end_time", Value: 1}},
		},
		{
			Keys: bson.D{primitive.E{Key: "start_time", Value: 1}, primitive.E{Key: "end_time", Value: 1}, primitive.E{Key: "ban_post_type", Value: 1}, primitive.E{Key: "ban_config_type", Value: 1}, primitive.E{Key: "get_cnt", Value: 1}},
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BanUserConfigCol createIndexes err: (%v)", err.Error())
		return err
	}
	log.DebugWithCtx(ctx, "BanUserConfigCol createIndexes result: (%v)", banConfigRes)

	return nil
}

func (m *MongoDao) BatchGetWhiteUidList(ctx context.Context, tabIds []uint32) ([]*TabWhiteUidListInfo, error) {
	var filter bson.M
	if len(tabIds) > 0 {
		filter = bson.M{"_id": bson.M{"$in": tabIds}}
	}
	cursor, err := m.WhiteUidListCol.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	infos := make([]*TabWhiteUidListInfo, 0, len(tabIds))
	err = cursor.All(ctx, &infos)
	if err != nil {
		return nil, err
	}
	return infos, nil
}

func (m *MongoDao) SetWhiteUidList(ctx context.Context, tabId uint32, uidList []uint32) error {
	updateData := &TabWhiteUidListInfo{
		TabId:        tabId,
		WhiteUidList: uidList,
		UpdateTime:   time.Now().Unix(),
	}
	res, err := m.WhiteUidListCol.UpdateOne(ctx,
		bson.M{"_id": tabId},
		bson.M{"$set": updateData},
		options.Update().SetUpsert(true))
	if err != nil {
		return err
	}
	log.InfoWithCtx(ctx, "SetWhiteUidList tabId:%d, len(uidList)=%d, res:%v", tabId, len(uidList), res)
	return nil
}

func (m *MongoDao) GetNewTabsByTabSubType(ctx context.Context, tabSubType uint32) ([]*NewTab, error) {
	filter := bson.M{"tab_sub_type": tabSubType}

	cursor, err := m.NewTabCol.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	tabs := make([]*NewTab, 0)
	err = cursor.All(ctx, &tabs)
	if err != nil {
		return nil, err
	}
	return tabs, nil
}

func (m *MongoDao) UpsertNewTab(ctx context.Context, tab *NewTab) error {
	updateData := &NewTab{
		TabId:        tab.TabId,
		TabName:      tab.TabName,
		TabSubType:   tab.TabSubType,
		ImageUrl:     tab.ImageUrl,
		IsTabVisible: tab.IsTabVisible,
		UpdateTime:   time.Now().UnixNano(),
	}

	_, err := m.NewTabCol.UpdateOne(ctx,
		bson.M{"_id": tab.TabId},
		bson.M{"$set": updateData},
		options.Update().SetUpsert(true))

	return err
}

func (m *MongoDao) DeleteNewTab(ctx context.Context, tabId uint32) error {
	filter := bson.M{"_id": tabId}
	_, err := m.NewTabCol.DeleteOne(ctx, filter)
	return err
}

func (m *MongoDao) GetNewTabs(ctx context.Context, onlyVisible bool) ([]*NewTab, error) {
	filter := bson.M{}

	if onlyVisible {
		filter["is_tab_visible"] = true
	}

	cursor, err := m.NewTabCol.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	tabs := make([]*NewTab, 0)
	err = cursor.All(ctx, &tabs)
	if err != nil {
		return nil, err
	}
	return tabs, nil
}

func (m *MongoDao) initNewTabIdCreator() error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	res := m.NewTabIdCreatorCol.FindOne(ctx, bson.M{"_id": NewTabIdCreatorType})
	if err := res.Err(); err != nil {
		if err != mongo.ErrNoDocuments {
			return err
		} else {
			newTabIdInit := &NewTabIdCreator{
				Id:    NewTabIdCreatorType,
				TabId: initNewTabId,
			}
			_, err := m.NewTabIdCreatorCol.InsertOne(ctx, newTabIdInit)
			if err != nil {
				return err
			}
		}
	}

	res = m.NewTabIdCreatorCol.FindOne(ctx, bson.M{"_id": FastPcCategoryIdCreatorType})
	if err := res.Err(); err != nil {
		if err != mongo.ErrNoDocuments {
			return err
		} else {
			newCategoryIdInit := &NewTabIdCreator{
				Id:    FastPcCategoryIdCreatorType,
				TabId: 0,
			}
			_, err = m.NewTabIdCreatorCol.InsertOne(ctx, newCategoryIdInit)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (m *MongoDao) CreateNewTabId(ctx context.Context, creatorType string) (uint32, error) {
	option := options.FindOneAndUpdate()
	option.SetReturnDocument(options.After)

	res := m.NewTabIdCreatorCol.FindOneAndUpdate(ctx, bson.M{"_id": creatorType}, bson.M{"$inc": bson.M{"tab_id": 1}}, option)
	if err := res.Err(); err != nil {
		return 0, err
	}

	newTabId := &NewTabIdCreator{}
	if err := res.Decode(newTabId); err != nil {
		return 0, err
	}

	return newTabId.TabId, nil
}

func (m *MongoDao) UpsertMinorSupervisionConfig(ctx context.Context, config *MinorSupervisionConfig) error {
	_, err := m.MinorSupervisionConfigCol.UpdateOne(ctx,
		bson.M{"_id": config.Id},
		bson.M{"$set": config},
		options.Update().SetUpsert(true))

	return err
}

func (m *MongoDao) BatchGetMinorSupervisionConfig(ctx context.Context, tabIds, categoryIds []uint32) ([]*MinorSupervisionConfig, error) {
	filter := bson.M{}
	if len(tabIds) > 0 && len(categoryIds) > 0 {
		filter = bson.M{"$or": []bson.M{
			{"tab_id": bson.M{"$in": tabIds}},
			{"category_id": bson.M{"$in": categoryIds}},
		}}
	} else if len(tabIds) > 0 {
		filter["tab_id"] = bson.M{"$in": tabIds}
	} else if len(categoryIds) > 0 {
		filter["category_id"] = bson.M{"$in": categoryIds}
	} else {
		return nil, nil
	}
	cursor, err := m.MinorSupervisionConfigCol.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	configs := make([]*MinorSupervisionConfig, 0, len(tabIds))
	err = cursor.All(ctx, &configs)
	if err != nil {
		return nil, err
	}
	return configs, nil
}

func (m *MongoDao) GetAllMinorSupervisionConfigs(ctx context.Context) ([]*MinorSupervisionConfig, error) {
	cursor, err := m.MinorSupervisionConfigCol.Find(ctx, bson.M{})
	if err != nil {
		return nil, err
	}
	configs := make([]*MinorSupervisionConfig, 0)
	err = cursor.All(ctx, &configs)
	if err != nil {
		return nil, err
	}
	return configs, nil
}

// -----------------------------------------未成年人小游戏开关配置相关-----------------------------------------------------------------
func (m *MongoDao) GetTabsRealNameConfigs(ctx context.Context) ([]*TabsRealNameConfig, error) {
	filter := bson.M{}
	cursor, err := m.RealNameConfig.Find(ctx, filter)
	if err != nil {
		log.ErrorWithCtx(ctx, "getTabsRealNameConfigs Find err: (%v)", err)
		return nil, err
	}

	result := make([]*TabsRealNameConfig, 0)
	err = cursor.All(ctx, &result)
	if err != nil {
		log.ErrorWithCtx(ctx, "getTabsRealNameConfigs Decode err: (%v)", err)
		return nil, err
	}

	return result, nil
}

func (m *MongoDao) GetPageTabsRealNameConfigs(ctx context.Context, page uint32, count uint32) ([]*TabsRealNameConfig, uint32, error) {
	filter := bson.M{}
	option := options.Find()
	if count > 0 {
		if count > 1000 {
			log.ErrorWithCtx(ctx, "getPageTabsRealNameConfigs count err:%d", count)
			count = 1000
		}
		option.SetLimit(int64(count))
	}

	skip := int64((page - 1) * count)
	if skip > 0 {
		if skip > 500 {
			log.ErrorWithCtx(ctx, "getPageTabsRealNameConfigs skip err:%d", skip)
			skip = 500
		}
		option.SetSkip(skip)
	}

	total, err := m.RealNameConfig.CountDocuments(ctx, filter)
	if err != nil {
		log.ErrorWithCtx(ctx, "getPageTabsRealNameConfigs CountDocuments err: (%v)", err)
	}

	cursor, err := m.RealNameConfig.Find(ctx, filter, option)
	if err != nil {
		log.ErrorWithCtx(ctx, "getPageTabsRealNameConfigs Find err: (%v)", err)
	}

	result := make([]*TabsRealNameConfig, 0)
	err = cursor.All(ctx, &result)
	if err != nil {
		log.ErrorWithCtx(ctx, "getPageTabsRealNameConfigs Decode err: (%v)", err)
	}
	return result, uint32(total), nil
}

func (m *MongoDao) UpdateTabsRealNameConfig(ctx context.Context, pbConfig *pb.TabsRealNameConfig) error {
	filter := bson.M{
		"market_id":   pbConfig.GetMarketId(),
		"channel_pkg": pbConfig.GetChannelPkg(),
		"cli_ver":     pbConfig.GetCliVer(),
	}

	update := bson.M{
		"$set": bson.M{
			"switch_status": pbConfig.GetSwitchStatus(),
			"tab_ids":       pbConfig.GetTabIds(),
			"category_ids":  pbConfig.GetCategoryIds(),
		},
	}

	_, err := m.RealNameConfig.UpdateOne(ctx, filter, update)
	if err != nil {
		log.ErrorWithCtx(ctx, "updateTabsRealNameConfig UpdateOne err: (%v)", err)
		return err
	}
	return nil
}

func (m *MongoDao) AddTabsRealNameConfig(ctx context.Context, pbConfig *pb.TabsRealNameConfig) error {
	_, err := m.RealNameConfig.InsertOne(ctx, bson.M{
		"market_id":     pbConfig.GetMarketId(),
		"channel_pkg":   pbConfig.GetChannelPkg(),
		"cli_ver":       pbConfig.GetCliVer(),
		"switch_status": pbConfig.GetSwitchStatus(),
		"tab_ids":       pbConfig.GetTabIds(),
		"category_ids":  pbConfig.GetCategoryIds(),
	})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return protocol.NewExactServerError(nil, status.ErrTopicChannelCommonError, "该马甲包的渠道和版本配置已存在")
		}
		log.ErrorWithCtx(ctx, "addTabsRealNameConfig InsertOne err: (%v)", err)
		return err
	}
	return nil
}

func (m *MongoDao) DeleteTabsRealNameConfig(ctx context.Context, marketId uint32, channelPkg string, cliVer string) error {
	filter := bson.M{
		"market_id":   marketId,
		"channel_pkg": channelPkg,
		"cli_ver":     cliVer,
	}
	_, err := m.RealNameConfig.DeleteOne(ctx, filter)
	if err != nil {
		log.ErrorWithCtx(ctx, "deleteTabsRealNameConfig DeleteOne err: (%v)", err)
		return err
	}
	return nil
}

func (m *MongoDao) GetQuickMatchConfig(ctx context.Context, page, limit, configType uint32) ([]*QuickMatchConfig, error) {
	option := options.Find()
	option.SetLimit(int64(limit))
	option.SetSkip(int64((page - 1) * limit))
	option.SetSort(bson.D{
		primitive.E{Key: "sort", Value: 1},
		primitive.E{Key: "update_time", Value: -1},
	})
	cursor, err := m.QuickMatchConfig.Find(ctx, bson.M{"config_type": configType}, option)
	if err != nil {
		return nil, err
	}
	result := make([]*QuickMatchConfig, 0)
	err = cursor.All(ctx, &result)

	return result, err
}

func (m *MongoDao) InsertQuickMatchConfig(ctx context.Context, item *QuickMatchConfig) error {
	_, err := m.QuickMatchConfig.InsertOne(ctx, item)
	return err
}

func (m *MongoDao) UpdateQuickMatchConfig(ctx context.Context, item *QuickMatchConfig) error {
	nowMx := time.Now().UnixNano() / 1e6
	var setting bson.M
	switch pb.QuickMatchConfigType(item.ConfigType) {
	case pb.QuickMatchConfigType_MiniZoneExposeQuickMatchList:
		setting = bson.M{
			"tab_id":            item.TabId,
			"update_time":       nowMx,
			"game_display_name": item.GameDisplayName,
			"bg_colors":         item.BgColors,
			"button_effect":     item.ButtonEffect,
			"jump_link":         item.JumpLink,
		}
	case pb.QuickMatchConfigType_MiniZoneMoreTabList:
		setting = bson.M{
			"tab_id":        item.TabId,
			"update_time":   nowMx,
			"button_effect": item.ButtonEffect,
			"jump_link":     item.JumpLink,
		}
	case pb.QuickMatchConfigType_MiniZoneHotAreaGameList:
		setting = bson.M{
			"tab_id":            item.TabId,
			"update_time":       nowMx,
			"game_display_name": item.GameDisplayName,
			"button_effect":     item.ButtonEffect,
			"jump_link":         item.JumpLink,
			"hot_mini_game_extra_info": bson.M{
				"intro_text":    item.HotMiniGameExtraInfo.IntroText,
				"bg_url":        item.HotMiniGameExtraInfo.BgUrl,
				"bg_color":      item.HotMiniGameExtraInfo.BgColor,
				"button_text":   item.HotMiniGameExtraInfo.ButtonText,
				"button_colors": item.HotMiniGameExtraInfo.ButtonColors,
				"lottie_url":    item.HotMiniGameExtraInfo.LottieUrl,
			},
		}
	default:
		setting = bson.M{
			"tab_id":      item.TabId,
			"category_id": item.CategoryID,
			"tab_type":    item.TabType,
			"update_time": nowMx,
		}
	}
	_, err := m.QuickMatchConfig.UpdateOne(ctx, bson.M{"_id": item.Id},
		bson.M{"$set": setting})
	return err
}

func (m *MongoDao) DeleteQuickMatchConfigById(ctx context.Context, id string) error {
	objectId, _ := primitive.ObjectIDFromHex(id)
	_, err := m.QuickMatchConfig.DeleteOne(ctx, bson.M{"_id": objectId})
	return err
}

func (m *MongoDao) ResortQuickMatchConfig(ctx context.Context, ids []string) error {
	nowMx := time.Now().UnixNano() / 1e6

	models := make([]mongo.WriteModel, len(ids))
	for i, id := range ids {
		objectId, _ := primitive.ObjectIDFromHex(id)
		models[i] = mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": objectId}).
			SetUpdate(bson.M{"$set": bson.M{
				"sort":        i + 1,
				"update_time": nowMx,
			}})
	}
	_, err := m.QuickMatchConfig.BulkWrite(ctx, models, options.BulkWrite().SetOrdered(false))

	return err
}

// -----------------------------------------新版快速匹配入口配置相关-----------------------------------------------------------------
func (m *MongoDao) UpsertNewQuickMatchConfig(ctx context.Context, config *QuickMatchConfig) error {
	config.ConfigType = uint32(pb.QuickMatchConfigType_NewQuickMatch)
	_, err := m.QuickMatchConfig.UpdateOne(ctx,
		bson.M{"_id": config.Id},
		bson.M{"$set": config},
		options.Update().SetUpsert(true))
	return err
}

func (m *MongoDao) BatchGetNewQuickMatchConfig(ctx context.Context, tabIds []uint32) ([]*QuickMatchConfig, error) {
	filter := bson.M{"config_type": uint32(pb.QuickMatchConfigType_NewQuickMatch)}
	if len(tabIds) > 0 {
		filter["tab_id"] = bson.M{"$in": tabIds}
	}
	opts := options.Find().SetSort(bson.M{"update_time": -1})

	configList := make([]*QuickMatchConfig, 0)
	cursor, err := m.QuickMatchConfig.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}

	err = cursor.All(ctx, &configList)
	if err != nil {
		return nil, err
	}

	return configList, nil
}

func (m *MongoDao) GetAllTabInfoExt(ctx context.Context, extType uint32) ([]*TabInfoExt, error) {
	filter := bson.M{
		"ext_type": extType,
	}

	opts := options.Find().SetSort(bson.D{
		primitive.E{Key: "sort", Value: 1},
		primitive.E{Key: "update_ts", Value: -1},
	})
	cursor, err := m.tabInfoExt.Find(ctx, filter, opts)
	if err != nil {
		log.ErrorWithCtx(ctx, "getAllGetTabInfoExt Find err: (%v)", err)
		return nil, err
	}

	result := make([]*TabInfoExt, 0)
	err = cursor.All(ctx, &result)
	if err != nil {
		log.ErrorWithCtx(ctx, "getAllGetTabInfoExt Decode err: (%v)", err)
		return nil, err
	}
	return result, err
}

func (m *MongoDao) UpsertTabInfoExt(ctx context.Context, tabId, extType uint32, extInfo string, ts int64) error {
	_, err := m.tabInfoExt.UpdateOne(ctx,
		bson.M{"tab_id": tabId, "ext_type": extType},
		bson.M{"$set": bson.M{
			"ext_info":  extInfo,
			"update_ts": ts,
		}}, options.Update().SetUpsert(true))
	return err
}

func (m *MongoDao) DelTabInfoExt(ctx context.Context, tabId, extType uint32) error {
	filter := bson.M{
		"tab_id":   tabId,
		"ext_type": extType,
	}
	_, err := m.tabInfoExt.DeleteOne(ctx, filter)
	return err
}

// UpsertBanUserConfig 创建/更新禁止配置
func (m *MongoDao) UpsertBanUserConfig(ctx context.Context, config *BanUserConfig) error {
	_, err := m.BanUserConfigCol.UpdateOne(ctx,
		bson.M{"_id": config.Id},
		bson.M{"$set": config},
		options.Update().SetUpsert(true))
	return err
}

type BanUserConfigFilter struct {
	Page         int64
	Size         int64
	NeedCount    bool
	TTid         string
	Status       int
	BanPostTypes []uint32
}

func (m *MongoDao) GetBanUserConfigList(ctx context.Context, filter BanUserConfigFilter) ([]*BanUserConfig, uint32, error) {
	opts := options.Find().
		SetSkip((filter.Page - 1) * filter.Size).
		SetLimit(filter.Size).
		SetSort(bson.M{"operate_time": -1})
	cond := bson.M{}
	if len(filter.BanPostTypes) > 0 {
		cond["ban_post_type"] = bson.M{"$in": filter.BanPostTypes}
	}
	if filter.TTid != "" {
		cond["$or"] = []bson.M{
			{
				"ttid": filter.TTid,
			},
			{
				"ttids": bson.M{
					"$elemMatch": bson.M{
						"$eq": filter.TTid,
					},
				},
			},
		}
	}

	now := time.Now().UnixNano() / 1e6
	switch filter.Status {
	case StatusInEffective:
		cond["start_time"] = bson.M{"$gt": now}
	case StatusActive:
		cond["start_time"] = bson.M{"$lte": now}
		cond["end_time"] = bson.M{"$gte": now}
	case StatusExpired:
		cond["end_time"] = bson.M{"$lt": now}
	default:
	}
	cursor, err := m.BanUserConfigCol.Find(ctx, cond, opts)
	if err != nil {
		return nil, 0, err
	}
	log.InfoWithCtx(ctx, "cond:%+v", cond)
	configs := make([]*BanUserConfig, 0)
	err = cursor.All(ctx, &configs)
	if err != nil {
		return nil, 0, err
	}

	var total int64
	if filter.NeedCount {
		total, err = m.BanUserConfigCol.CountDocuments(ctx, cond)
		if err != nil {
			log.ErrorWithCtx(ctx, "MongoDao GetTopicList CountDocuments err: %v", err)
		}
	}

	return configs, uint32(total), nil
}

// GetActiveBanUserConfigList 获取生效中的禁止配置
func (m *MongoDao) GetActiveBanUserConfigList(ctx context.Context, now int64) ([]*BanUserConfig, error) {
	// 有多个同时生效的, 以操作时间最新的为准
	opts := options.Find().SetSort(bson.M{"operate_time": -1})
	cond := bson.M{
		"start_time": bson.M{"$lte": now},
		"end_time":   bson.M{"$gte": now},
	}

	cursor, err := m.BanUserConfigCol.Find(ctx, cond, opts)
	if err != nil {
		return nil, err
	}
	configs := make([]*BanUserConfig, 0)
	err = cursor.All(ctx, &configs)
	if err != nil {
		return nil, err
	}

	return configs, nil
}

// DelBanUserConfig 删除BanUserConfig
func (m *MongoDao) DelBanUserConfig(ctx context.Context, id string) error {
	_, err := m.BanUserConfigCol.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (m *MongoDao) BatchAddBanUserConfig(ctx context.Context, banPostConfigs []*BanUserConfig) error {
	models := make([]mongo.WriteModel, len(banPostConfigs))
	for i, banPostConfig := range banPostConfigs {
		models[i] = mongo.NewInsertOneModel().SetDocument(banPostConfig)
	}
	_, err := m.BanUserConfigCol.BulkWrite(ctx, models, options.BulkWrite().SetOrdered(false))

	return err
}

// GetBanGameHallConfig 获取组队大厅 一键禁言
func (m *MongoDao) GetBanGameHallConfig(ctx context.Context) ([]*pb.BanUserPostConfig, error) {
	cond := bson.M{}
	cond["ban_post_type"] = pb.BanPostType_BanGameHallSay
	cond["ban_config_type"] = pb.BanConfigType_BanConfigTypeMultiUser
	now := time.Now().UnixNano() / 1e6
	cond["start_time"] = bson.M{"$lte": now}
	cond["end_time"] = bson.M{"$gte": now}
	cond["is_get"] = false

	var findoptions = &options.FindOptions{}
	findoptions.SetLimit(5)
	cur, err := m.BanUserConfigCol.Find(
		ctx,
		cond,
		findoptions,
	)
	if err != nil {
		log.ErrorWithCtx(ctx, ".BanUserConfigCol.Find: %v", err)
		return nil, err
	}
	banGameHallConfigs := make([]*BanUserConfig, 0, 10)
	err = cur.All(ctx, &banGameHallConfigs)
	if err != nil {
		log.ErrorWithCtx(ctx, "cur.All err: %v", err)
		return nil, err
	}
	log.InfoWithCtx(ctx, "GetBanGameHallConfig :%+v", banGameHallConfigs)
	pbBanGameHallConfigs := make([]*pb.BanUserPostConfig, 0, len(banGameHallConfigs))
	for _, banGameHallConfig := range banGameHallConfigs {
		pbBanGameHallConfigs = append(pbBanGameHallConfigs, &pb.BanUserPostConfig{
			Id:          banGameHallConfig.Id,
			Uid:         banGameHallConfig.Uid,
			OperateTime: banGameHallConfig.OperateTime,
			StartTime:   banGameHallConfig.StartTime,
			EndTime:     banGameHallConfig.EndTime,
			BanPostType: pb.BanPostType(banGameHallConfig.BanPostType),
			TabIds:      banGameHallConfig.TabIds,
			Ttids:       banGameHallConfig.TTids,
			Uids:        banGameHallConfig.Uids,
		})
	}

	return pbBanGameHallConfigs, nil
}

func (m *MongoDao) UpdateBanGameHallGetStatus(ctx context.Context, ids []string) error {

	_, err := m.BanUserConfigCol.UpdateMany(ctx, bson.M{"_id": bson.M{"$in": ids}}, bson.M{"$set": bson.M{"is_get": true}})
	if err != nil {
		log.ErrorWithCtx(ctx, "BanUserConfigCol.UpdateMany err: %v", err)
		return err
	}
	return nil
}
