package internal

import (
	"context"
	"github.com/golang/mock/gomock"
	mockAccount "golang.52tt.com/clients/mocks/account"
	mockApiCenter "golang.52tt.com/clients/mocks/apicenter/apiserver"
	pb "golang.52tt.com/protocol/services/channel-play-tab"
	"golang.52tt.com/services/channel-play-tab/internal/dao"
	"golang.52tt.com/services/channel-play-tab/internal/mgr/ban_config_mgr"
	"golang.52tt.com/services/channel-play-tab/internal/mgr/base_config_mgr"
	"golang.52tt.com/services/channel-play-tab/internal/mgr/fast_pc_mgr"
	"golang.52tt.com/services/channel-play-tab/internal/mgr/minor_supervision_mgr"
	"golang.52tt.com/services/channel-play-tab/internal/mgr/tab_mgr"
	"golang.52tt.com/services/channel-play-tab/internal/mocks"
	"reflect"
	"testing"
	"time"
)

var baseConfigMgr *mocks.MockIBaseConfigMgr
var tabMgr *mocks.MockITabMgr
var minorSupervisionMgr *mocks.MockIMinorSupervisionMgr
var fastPcCategoryMgr *mocks.MockIFastPcCategoryMgr

func genMock(ctl *gomock.Controller) {
	baseConfigMgr = mocks.NewMockIBaseConfigMgr(ctl)
	tabMgr = mocks.NewMockITabMgr(ctl)
	minorSupervisionMgr = mocks.NewMockIMinorSupervisionMgr(ctl)
	fastPcCategoryMgr = mocks.NewMockIFastPcCategoryMgr(ctl)

	mockAccountCli = mockAccount.NewMockIClient(ctl)
	mockApiCenterCli = mockApiCenter.NewMockIClient(ctl)
	mockMongo = mocks.NewMockIMongoDao(ctl)
	mockBanConfigMgr = &ban_config_mgr.BanUserConfigMgr{
		MongoDao:        mockMongo,
		AccountClient:   mockAccountCli,
		ApiCenterClient: mockApiCenterCli,
	}
}

func TestServer_BatchGetWhiteUidListByTabIds(t *testing.T) {
	type fields struct {
		baseConfigMgr base_config_mgr.IBaseConfigMgr
	}
	type args struct {
		ctx context.Context
		req *pb.BatchGetWhiteUidListByTabIdsReq
	}
	ctl := gomock.NewController(t)
	genMock(ctl)
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.BatchGetWhiteUidListByTabIdsResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "1",
			fields: fields{
				baseConfigMgr: baseConfigMgr,
			},
			args: args{
				ctx: context.Background(),
				req: &pb.BatchGetWhiteUidListByTabIdsReq{
					TabIds:  []uint32{1},
					Source:  "test",
					NoCache: false,
				},
			},
			want: &pb.BatchGetWhiteUidListByTabIdsResp{
				TabListMap: map[uint32]*pb.BatchGetWhiteUidListByTabIdsResp_WhiteUidList{
					1: {
						List: []uint32{123, 456},
					},
				},
			},
			wantErr: false,
		},
	}
	baseConfigMgr.EXPECT().BatchGetWhiteUidListByTabIds(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
		map[uint32]*pb.BatchGetWhiteUidListByTabIdsResp_WhiteUidList{
			1: {
				List: []uint32{123, 456},
			}}, nil)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				baseConfigMgr: tt.fields.baseConfigMgr,
			}
			got, err := s.BatchGetWhiteUidListByTabIds(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetWhiteUidListByTabIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetWhiteUidListByTabIds() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_SetWhiteUidListByTabId(t *testing.T) {
	type fields struct {
		baseConfigMgr base_config_mgr.IBaseConfigMgr
	}
	type args struct {
		ctx context.Context
		req *pb.SetWhiteUidListByTabIdReq
	}
	ctl := gomock.NewController(t)
	genMock(ctl)
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.SetWhiteUidListByTabIdResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "1",
			fields: fields{
				baseConfigMgr: baseConfigMgr,
			},
			args: args{
				ctx: context.Background(),
				req: &pb.SetWhiteUidListByTabIdReq{
					TabId:   1,
					UidList: []uint32{123},
					Source:  "test",
				},
			},
			want:    &pb.SetWhiteUidListByTabIdResp{},
			wantErr: false,
		},
	}
	baseConfigMgr.EXPECT().SetWhiteUidListByTabId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				baseConfigMgr: tt.fields.baseConfigMgr,
			}
			got, err := s.SetWhiteUidListByTabId(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetWhiteUidListByTabId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SetWhiteUidListByTabId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_DeleteTab(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	genMock(ctl)

	ctx := context.Background()
	tabId := uint32(1)

	gomock.InOrder(
		tabMgr.EXPECT().DeleteTab(ctx, tabId).Return(nil),
	)

	type fields struct {
		baseConfigMgr base_config_mgr.IBaseConfigMgr
		tabMgr        tab_mgr.ITabMgr
	}
	type args struct {
		ctx context.Context
		req *pb.DeleteTabReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.DeleteTabResp
		wantErr bool
	}{
		{
			name: "normal case",
			fields: fields{
				baseConfigMgr: baseConfigMgr,
				tabMgr:        tabMgr,
			},
			args: args{
				ctx: ctx,
				req: &pb.DeleteTabReq{
					TabId: tabId,
				},
			},
			want:    &pb.DeleteTabResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				baseConfigMgr: tt.fields.baseConfigMgr,
				tabMgr:        tt.fields.tabMgr,
			}
			got, err := s.DeleteTab(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeleteTab() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DeleteTab() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_GetTabsByTabSubType(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	genMock(ctl)

	ctx := context.Background()
	tabSubType := uint32(pb.TabSubType_GAME)
	noCache := false

	tabs := []*pb.Tab{{
		TabId:        1,
		TabName:      "王者荣耀",
		TabSubType:   pb.TabSubType_GAME,
		IsTabVisible: false,
	}}

	gomock.InOrder(
		tabMgr.EXPECT().GetTabsByTabSubType(ctx, tabSubType, "", noCache).Return(tabs, nil),
	)

	type fields struct {
		baseConfigMgr base_config_mgr.IBaseConfigMgr
		tabMgr        tab_mgr.ITabMgr
	}
	type args struct {
		ctx context.Context
		req *pb.GetTabsByTabSubTypeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetTabsByTabSubTypeResp
		wantErr bool
	}{
		{
			name: "normal case",
			fields: fields{
				baseConfigMgr: baseConfigMgr,
				tabMgr:        tabMgr,
			},
			args: args{
				ctx: ctx,
				req: &pb.GetTabsByTabSubTypeReq{
					TabSubType: pb.TabSubType_GAME,
					Source:     "",
					NoCache:    false,
				},
			},
			want: &pb.GetTabsByTabSubTypeResp{
				Tabs: tabs,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				baseConfigMgr: tt.fields.baseConfigMgr,
				tabMgr:        tt.fields.tabMgr,
			}
			got, err := s.GetTabsByTabSubType(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTabsByTabSubType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetTabsByTabSubType() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_UpsertTab(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	genMock(ctl)

	ctx := context.Background()
	tab := &pb.Tab{
		TabId:        1,
		TabName:      "王者荣耀",
		TabSubType:   pb.TabSubType_GAME,
		IsTabVisible: false,
	}

	gomock.InOrder(
		tabMgr.EXPECT().UpsertTab(ctx, tab).Return(nil),
	)
	type fields struct {
		baseConfigMgr base_config_mgr.IBaseConfigMgr
		tabMgr        tab_mgr.ITabMgr
	}
	type args struct {
		ctx context.Context
		req *pb.UpsertTabReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.UpsertTabResp
		wantErr bool
	}{
		{
			name: "normal case",
			fields: fields{
				baseConfigMgr: baseConfigMgr,
				tabMgr:        tabMgr,
			},
			args: args{
				ctx: ctx,
				req: &pb.UpsertTabReq{
					Tab: tab,
				},
			},
			want:    &pb.UpsertTabResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				baseConfigMgr: tt.fields.baseConfigMgr,
				tabMgr:        tt.fields.tabMgr,
			}
			got, err := s.UpsertTab(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpsertTab() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpsertTab() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_GetTabs(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	genMock(ctl)

	ctx := context.Background()
	noCache, onlyVisible := false, false

	tabs := []*pb.Tab{{
		TabId:        1,
		TabName:      "王者荣耀",
		TabSubType:   pb.TabSubType_GAME,
		IsTabVisible: false,
	}}

	gomock.InOrder(
		tabMgr.EXPECT().GetTabs(ctx, noCache, onlyVisible).Return(tabs, nil),
	)

	type fields struct {
		baseConfigMgr base_config_mgr.IBaseConfigMgr
		tabMgr        tab_mgr.ITabMgr
	}
	type args struct {
		ctx context.Context
		req *pb.GetTabsReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetTabsResp
		wantErr bool
	}{
		{
			name: "normal case",
			fields: fields{
				baseConfigMgr: baseConfigMgr,
				tabMgr:        tabMgr,
			},
			args: args{
				ctx: ctx,
				req: &pb.GetTabsReq{
					NoCache:     noCache,
					OnlyVisible: onlyVisible,
				},
			},
			want: &pb.GetTabsResp{
				Tabs: tabs,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				baseConfigMgr: tt.fields.baseConfigMgr,
				tabMgr:        tt.fields.tabMgr,
			}
			got, err := s.GetTabs(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTabs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetTabs() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_BatchGetMinorSupervisionConfig(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	genMock(ctl)

	ctx := context.Background()

	gomock.InOrder(
		minorSupervisionMgr.EXPECT().BatchGetMinorSupervisionConfig(ctx, gomock.Any()).Return(&pb.BatchGetMinorSupervisionConfigResp{}, nil),
	)

	type fields struct {
		baseConfigMgr       base_config_mgr.IBaseConfigMgr
		tabMgr              tab_mgr.ITabMgr
		minorSupervisionMgr minor_supervision_mgr.IMinorSupervisionMgr
	}
	type args struct {
		ctx context.Context
		req *pb.BatchGetMinorSupervisionConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.BatchGetMinorSupervisionConfigResp
		wantErr bool
	}{
		{
			name: "BatchGetMinorSupervisionConfig",
			fields: fields{
				baseConfigMgr:       baseConfigMgr,
				tabMgr:              tabMgr,
				minorSupervisionMgr: minorSupervisionMgr,
			},
			args: args{
				ctx: ctx,
				req: &pb.BatchGetMinorSupervisionConfigReq{},
			},
			want:    &pb.BatchGetMinorSupervisionConfigResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				baseConfigMgr:       tt.fields.baseConfigMgr,
				tabMgr:              tt.fields.tabMgr,
				minorSupervisionMgr: tt.fields.minorSupervisionMgr,
			}
			got, err := s.BatchGetMinorSupervisionConfig(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetMinorSupervisionConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetMinorSupervisionConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_UpsertMinorSupervisionConfig(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	genMock(ctl)

	ctx := context.Background()

	gomock.InOrder(
		minorSupervisionMgr.EXPECT().UpsertMinorSupervisionConfig(ctx, gomock.Any()).Return(nil),
	)

	type fields struct {
		baseConfigMgr       base_config_mgr.IBaseConfigMgr
		tabMgr              tab_mgr.ITabMgr
		minorSupervisionMgr minor_supervision_mgr.IMinorSupervisionMgr
	}
	type args struct {
		ctx context.Context
		req *pb.UpsertMinorSupervisionConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.UpsertMinorSupervisionConfigResp
		wantErr bool
	}{
		{
			name: "UpsertMinorSupervisionConfig",
			fields: fields{
				baseConfigMgr:       baseConfigMgr,
				tabMgr:              tabMgr,
				minorSupervisionMgr: minorSupervisionMgr,
			},
			args: args{
				ctx: ctx,
				req: &pb.UpsertMinorSupervisionConfigReq{},
			},
			want:    &pb.UpsertMinorSupervisionConfigResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{
				baseConfigMgr:       tt.fields.baseConfigMgr,
				tabMgr:              tt.fields.tabMgr,
				minorSupervisionMgr: tt.fields.minorSupervisionMgr,
			}
			got, err := s.UpsertMinorSupervisionConfig(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpsertMinorSupervisionConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpsertMinorSupervisionConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServer_GetFastPCCategoryConfig(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	genMock(ctl)

	ctx := context.Background()
	mockData := []*dao.FastPCCategoryConfig{
		{
			CategoryId:   1,
			Name:         "测试分类1",
			Sort:         1,
			TabIds:       []uint32{101, 102},
			Icon:         "test_icon_1.png",
			CategoryType: pb.FastPCCategoryType_FAST_PC_CATEGORY_TYPE_NORMAL,
			UpdateTime:   time.Now(),
			CreateTime:   time.Now(),
		},
		{
			CategoryId:   2,
			Name:         "测试分类2",
			Sort:         2,
			TabIds:       []uint32{201, 202},
			Icon:         "test_icon_2.png",
			CategoryType: pb.FastPCCategoryType_FAST_PC_CATEGORY_TYPE_MINI_GAME,
			UpdateTime:   time.Now(),
			CreateTime:   time.Now(),
		},
	}

	fastPcCategoryMgr.EXPECT().GetFastPCCategoryConfig(ctx).Return(mockData, nil)

	type fields struct {
		fastPcCategoryMgr fast_pc_mgr.IFastPcCategoryMgr
	}
	type args struct {
		ctx context.Context
		req *pb.GetFastPCCategoryConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "正常获取FastPC分类配置",
			fields: fields{
				fastPcCategoryMgr: fastPcCategoryMgr,
			},
			args: args{
				ctx: ctx,
				req: &pb.GetFastPCCategoryConfigReq{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{}
			s.fastPcCategoryMgr = tt.fields.fastPcCategoryMgr
			got, err := s.GetFastPCCategoryConfig(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFastPCCategoryConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got == nil {
				t.Errorf("GetFastPCCategoryConfig() got = nil")
				return
			}
			// 验证返回的配置包含Icon和CategoryType字段
			if len(got.Configs) != 2 {
				t.Errorf("GetFastPCCategoryConfig() configs length = %v, want 2", len(got.Configs))
				return
			}
			// 验证第一个配置的Icon和CategoryType
			if got.Configs[0].Icon != "test_icon_1.png" {
				t.Errorf("GetFastPCCategoryConfig() config[0].Icon = %v, want test_icon_1.png", got.Configs[0].Icon)
			}
			if got.Configs[0].CategoryType != pb.FastPCCategoryType_FAST_PC_CATEGORY_TYPE_NORMAL {
				t.Errorf("GetFastPCCategoryConfig() config[0].CategoryType = %v, want NORMAL", got.Configs[0].CategoryType)
			}
			// 验证第二个配置的Icon和CategoryType
			if got.Configs[1].Icon != "test_icon_2.png" {
				t.Errorf("GetFastPCCategoryConfig() config[1].Icon = %v, want test_icon_2.png", got.Configs[1].Icon)
			}
			if got.Configs[1].CategoryType != pb.FastPCCategoryType_FAST_PC_CATEGORY_TYPE_MINI_GAME {
				t.Errorf("GetFastPCCategoryConfig() config[1].CategoryType = %v, want MINI_GAME", got.Configs[1].CategoryType)
			}
		})
	}
}

func TestServer_UpsertFastPCCategoryConfig(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	genMock(ctl)

	ctx := context.Background()
	testConfig := &pb.FastPCCategoryConfig{
		Id:           1,
		Name:         "测试分类",
		TabIds:       []uint32{101, 102},
		Sort:         1,
		Icon:         "test_icon.png",
		CategoryType: pb.FastPCCategoryType_FAST_PC_CATEGORY_TYPE_NORMAL,
	}

	// Mock GetNewFastPCCategoryId for new category
	fastPcCategoryMgr.EXPECT().GetNewFastPCCategoryId(ctx).Return(uint32(1001), nil).AnyTimes()
	// Mock UpsertFastPCCategoryConfig
	fastPcCategoryMgr.EXPECT().UpsertFastPCCategoryConfig(ctx, gomock.Any()).DoAndReturn(
		func(ctx context.Context, config *dao.FastPCCategoryConfig) error {
			// 验证传入的配置包含Icon和CategoryType字段
			if config.Icon != "test_icon.png" && config.Icon != "new_icon.png" {
				t.Errorf("UpsertFastPCCategoryConfig() config.Icon = %v, want test_icon.png or new_icon.png", config.Icon)
			}
			if config.CategoryType != pb.FastPCCategoryType_FAST_PC_CATEGORY_TYPE_NORMAL &&
				config.CategoryType != pb.FastPCCategoryType_FAST_PC_CATEGORY_TYPE_MINI_GAME {
				t.Errorf("UpsertFastPCCategoryConfig() config.CategoryType = %v, want NORMAL or MINI_GAME", config.CategoryType)
			}
			return nil
		},
	).AnyTimes()

	type fields struct {
		fastPcCategoryMgr fast_pc_mgr.IFastPcCategoryMgr
	}
	type args struct {
		ctx context.Context
		req *pb.UpsertFastPCCategoryConfigReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "正常更新FastPC分类配置-包含Icon和CategoryType",
			fields: fields{
				fastPcCategoryMgr: fastPcCategoryMgr,
			},
			args: args{
				ctx: ctx,
				req: &pb.UpsertFastPCCategoryConfigReq{
					Config: testConfig,
				},
			},
			wantErr: false,
		},
		{
			name: "新增FastPC分类配置-ID为0时自动生成",
			fields: fields{
				fastPcCategoryMgr: fastPcCategoryMgr,
			},
			args: args{
				ctx: ctx,
				req: &pb.UpsertFastPCCategoryConfigReq{
					Config: &pb.FastPCCategoryConfig{
						Id:           0, // ID为0，应该自动生成
						Name:         "新分类",
						TabIds:       []uint32{301, 302},
						Sort:         3,
						Icon:         "new_icon.png",
						CategoryType: pb.FastPCCategoryType_FAST_PC_CATEGORY_TYPE_MINI_GAME,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Server{}
			s.fastPcCategoryMgr = tt.fields.fastPcCategoryMgr
			got, err := s.UpsertFastPCCategoryConfig(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpsertFastPCCategoryConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got == nil {
				t.Errorf("UpsertFastPCCategoryConfig() got = nil")
			}
		})
	}
}
