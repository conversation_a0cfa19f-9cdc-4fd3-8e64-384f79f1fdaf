package cache

import (
	"context"
	"golang.52tt.com/clients/topic-channel/tab"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channel-play-tab"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-tab/internal/dao"
	"sync"
	"time"
)

type TabCache struct {
	tabSubTypeMap map[uint32][]*pb.Tab // key:tabSubType, value:tabs
	tabList       []*pb.Tab            // 包含所有tab的列表
	lock          sync.RWMutex
	tcTabMap      map[uint32]*tabPB.Tab // key: tabId
}

var tabCache = &TabCache{}
var tcTabCli tab.IClient

func NewTabCache(ctx context.Context) error {
	tcTabCli = tab.NewIClient()

	err := refreshTabCache(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabCache err:%v", err)
		return err
	}
	go func() {
		ticker := time.NewTicker(time.Second * 30)
		defer func() {
			ticker.Stop()
		}()
		for range ticker.C {
			refreshCtx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
			err = scheduleRefreshTabCache(refreshCtx)
			if err != nil {
				log.ErrorWithCtx(ctx, "refreshTabCache err:%v", err)
			}
			cancel()
		}
	}()
	return nil
}

func scheduleRefreshTabCache(ctx context.Context) error {
	defer func() {
		err := recover()
		if err != nil {
			log.ErrorWithCtx(ctx, "refreshTabCache panic, err: %v", err)
		}
	}()
	return refreshTabCache(ctx)
}

func refreshTabCache(ctx context.Context) error {
	tabs, err := mongoDao.GetNewTabs(ctx, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "UserLine Err: refreshTabCache err:%v", err)
		// 非必要数据，不强制启动前加载
		return nil
	}

	tabSubTypeMap := make(map[uint32][]*pb.Tab)
	tabList := make([]*pb.Tab, 0, len(tabs))
	for _, tab := range tabs {
		tabSubTypeMap[tab.TabSubType] = append(tabSubTypeMap[tab.TabSubType], dao.ConvertTab2Pb(tab))
		tabList = append(tabList, dao.ConvertTab2Pb(tab))
	}
	tabCache.tabSubTypeMap = tabSubTypeMap
	tabCache.lock.Lock()
	tabCache.tabList = tabList
	tabCache.lock.Unlock()

	tcTabResp, err := tcTabCli.Tabs(ctx, 0, 1000)
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshTabCache tcTabCli.Tabs err: %v", err)
	}
	tcTabMap := make(map[uint32]*tabPB.Tab, len(tcTabResp.GetTabs()))
	for _, tabInfo := range tcTabResp.GetTabs() {
		tcTabMap[tabInfo.GetId()] = tabInfo
	}
	if len(tcTabMap) != 0 {
		tabCache.tcTabMap = tcTabMap
	}

	return nil
}

func GetTabsByTabSubType(tabSubType uint32) []*pb.Tab {
	if tabCache == nil {
		return nil
	}
	return tabCache.tabSubTypeMap[tabSubType]
}

func GetTabs(onlyVisible bool) []*pb.Tab {
	if tabCache == nil {
		return nil
	}
	tabCache.lock.RLock()
	defer tabCache.lock.RUnlock()
	tabList := make([]*pb.Tab, 0, len(tabCache.tabList))
	if onlyVisible {
		for _, tab := range tabCache.tabList {
			if tab.GetIsTabVisible() {
				tabList = append(tabList, tab)
			}
		}
	} else {
		tabList = tabCache.tabList
	}

	return tabList
}

func GetTabInfoById(tabId uint32) *tabPB.Tab {
	if tabCache == nil || tabCache.tcTabMap == nil {
		return nil
	}
	return tabCache.tcTabMap[tabId]
}
