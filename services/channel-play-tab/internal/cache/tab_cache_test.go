package cache

import (
	"context"
	"github.com/golang/mock/gomock"
	mockTab "golang.52tt.com/clients/mocks/topic-channel/tab"
	pb "golang.52tt.com/protocol/services/channel-play-tab"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/channel-play-tab/internal/dao"
	"reflect"
	"testing"
)

func TestGetTabsByTabSubType(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	genMock(ctl)

	tabCache = &TabCache{
		tabSubTypeMap: map[uint32][]*pb.Tab{
			1: {{
				TabId:        1,
				TabName:      "综合频道",
				TabSubType:   1,
				IsTabVisible: false,
			}},
		},
	}

	type args struct {
		tabSubType uint32
	}
	tests := []struct {
		name string
		args args
		want []*pb.Tab
	}{
		{
			name: "normal case",
			args: args{tabSubType: 1},
			want: []*pb.Tab{{
				TabId:        1,
				TabName:      "综合频道",
				TabSubType:   1,
				IsTabVisible: false,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetTabsByTabSubType(tt.args.tabSubType); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetTabsByTabSubType() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetTabs(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	tabCache = &TabCache{
		tabList: []*pb.Tab{{
			TabId:        1,
			TabName:      "综合频道",
			TabSubType:   1,
			IsTabVisible: false,
		}},
	}
	type args struct {
		onlyVisible bool
	}
	tests := []struct {
		name string
		args args
		want []*pb.Tab
	}{
		{
			name: "normal case",
			args: args{onlyVisible: false},
			want: []*pb.Tab{{
				TabId:        1,
				TabName:      "综合频道",
				TabSubType:   1,
				IsTabVisible: false,
			}},
		},
		{
			name: "only visible",
			args: args{onlyVisible: true},
			want: []*pb.Tab{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetTabs(tt.args.onlyVisible); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetTabs() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_refreshTabCache(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	genMock(ctl)
	ctx := context.Background()
	tabCache = &TabCache{}
	mockTcTabCli := mockTab.NewMockIClient(ctl)
	tcTabCli = mockTcTabCli

	gomock.InOrder(
		mockMongoDao.EXPECT().GetNewTabs(ctx, false).Return([]*dao.NewTab{{
			TabId:        1,
			TabName:      "综合频道",
			TabSubType:   1,
			IsTabVisible: false,
		}}, nil),
		mockTcTabCli.EXPECT().Tabs(ctx, uint32(0), uint32(1000)).Return(&tabPB.TabsResp{}, nil),
	)

	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "normal case",
			args:    args{ctx: ctx},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := refreshTabCache(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("refreshTabCache() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
