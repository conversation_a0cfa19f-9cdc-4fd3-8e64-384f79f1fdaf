{"server.grpcListen": ":80", "server.adminListen": ":8078", "redis_config": {"host": "common-emoji-quicksilver-redis-1-node-1.external.se.cluster.local", "port": 6379, "protocol": "tcp", "ping_interval": 300, "database": 2}, "shine_open_id": 1728464911, "shine_secret": "4778c08a6e16dca39d722511b5d682de", "shine_hot_url": "https://api.open.weshineapp.com/1.0/hot", "event_link": {"subscriber": {"comm_im_event": {"kafka": {"brokers": ["**********:9092", "**********:9092", "**********:9092", "**********:9092"], "clientID": "common-emoji"}, "groupID": "common-emoji", "topics": ["comm_im_event"], "maxRetryTimes": 5, "processWorkerNum": 5}}}}