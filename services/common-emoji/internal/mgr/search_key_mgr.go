package mgr

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app/im"
	"golang.52tt.com/services/common-emoji/internal/mgr/cache"
	"golang.52tt.com/services/common-emoji/internal/mgr/entity"
)

const (
	defaultDelPoolCnt = 50
)

func NewSearchKeyMgr(cacheCli *cache.RedisDao) *SearchKeyMgr {
	return &SearchKeyMgr{
		cacheCli: cacheCli,
	}
}

type SearchKeyMgr struct {
	cacheCli *cache.RedisDao
}

func (s *SearchKeyMgr) SearchByCache(ctx context.Context, keyWord string) ([]*entity.ObsRecord, error) {
	md5s, err := s.cacheCli.GetRandSearchEmoji(ctx, keyWord, 30)
	if err != nil {
		return nil, err
	}
	if len(md5s) == 0 {
		return nil, nil
	}
	emojiList, err := s.cacheCli.BatGetObsRecord(ctx, uint32(im.EmojiMsg_EMOJI_TYPE_KEYWORD), md5s...)
	if err != nil {
		return nil, err
	}
	return emojiList, nil
}

func (s *SearchKeyMgr) delSearchCache(ctx context.Context, keyWord string) error {
	return s.cacheCli.RandDelSearchEmojiCache(ctx, keyWord, defaultDelPoolCnt)
}

func (s *SearchKeyMgr) DelEmojiWithKeyWord(ctx context.Context, keyWord, md5 string) error {
	err := s.cacheCli.DelObsRecord(ctx, md5, uint32(im.EmojiMsg_EMOJI_TYPE_KEYWORD))
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteHotEmoji md5:%s err(%v)", md5, err)
		return err
	}
	err = s.cacheCli.DelSearchEmojiCache(ctx, keyWord, md5)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelSearchEmojiCache md5:%s err(%v)", md5, err)
		return err
	}
	return nil
}

func (s *SearchKeyMgr) IsKeyWordEmojiExist(ctx context.Context, keyWord, md5 string) (bool, error) {
	return s.cacheCli.IsKeyWordEmojiExist(ctx, keyWord, md5)
}

func (s *SearchKeyMgr) BatAddSearchEmojiCache(ctx context.Context, keyWord string, md5s ...string) error {
	if len(md5s) == 0 {
		return nil
	}
	
	cacheSize, err := s.cacheCli. GetSearchCacheSize(ctx, keyWord)
	if err != nil {
		return err
	}
	if cacheSize > 100 {
		err = s.delSearchCache(ctx, keyWord)
		if err != nil {
			log.ErrorWithCtx(ctx, "AddSearchEmojiCache cacheSize:%d delSearchCache keyWord:%s err(%v)", cacheSize, keyWord, err)
		}
	}
	err = s.cacheCli.BatAddToCache(ctx, keyWord, md5s...)
	if err != nil {
		return err
	}
	log.InfoWithCtx(ctx, "BatAddSearchEmojiCache keyWord:%s cacheSize:%d len(add_emojiInfo):%d", keyWord, cacheSize, len(md5s))
	return nil
}

func (s *SearchKeyMgr) BatGetObsInfosMap(ctx context.Context, md5s []string, emojiType uint32) (map[string]*entity.ObsRecord, error) {
	infos, err := s.cacheCli.BatGetObsRecord(ctx, emojiType, md5s...)
	if err != nil {
		return nil, err
	}
	obsMap := make(map[string]*entity.ObsRecord)
	for _, v := range infos {
		obsMap[v.Md5] = v
	}
	return obsMap, nil
}

func (s *SearchKeyMgr) IsInObs(ctx context.Context, md5 string, emojiType uint32) (bool, *entity.ObsRecord) {
	record, err := s.cacheCli.BatGetObsRecord(ctx, emojiType, md5)
	if err != nil {
		log.ErrorWithCtx(ctx, "IsInObs md5:%s err(%v)", md5, err)
		return false, nil
	}
	if len(record) == 0 || len(record[0].ObsUrl) == 0 || len(record[0].Key) == 0 {
		return false, nil
	}
	return true, record[0]
}
