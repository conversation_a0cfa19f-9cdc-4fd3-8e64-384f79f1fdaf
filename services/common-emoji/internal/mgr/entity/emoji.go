package entity

import (
	"fmt"
	"golang.52tt.com/pkg/marketid_helper"
)

const (
	ObsScope = "thirdparty-emoji"
	ObsApp   = "tt"
)

type HotCacheEmoji struct {
	EmojiId   string `json:"emoji_id"`
	Md5       string `json:"md_5"`
	OriginUrl string `json:"origin_url"`
	OriginH   uint32 `json:"origin_h"`
	OriginW   uint32 `json:"origin_w"`
	ThumbH    uint32 `json:"thumb_h"`
	ThumbW    uint32 `json:"thumb_w"`
	Key       string `json:"key"`
}

func (c *HotCacheEmoji) GetThumbUrl(marketId, clientType uint32) string {
	return fmt.Sprintf("https://%s/%s/%s/%s?obs-process=image/resize/m/fixed/w/%d/h/%d",
		marketid_helper.GetObsDomain(marketId, clientType), ObsApp, ObsScope, c.Key, c.<PERSON>hum<PERSON>, c.ThumbH)
}

func (c *HotCacheEmoji) GetOriginUrl(marketId, clientType uint32) string {
	return obsDataUrl(c.Key, marketId, clientType)
}

// ObsDataUrl 图片和音频地址拼接一样
func obsDataUrl(key string, marketId, clientType uint32) string {
	return fmt.Sprintf("https://%s/%s/%s/%s", marketid_helper.GetObsDomain(marketId, clientType), ObsApp, ObsScope, key)
}

//type ObsRecord struct {
//	Md5 string `json:"md_5"`
//	Key string `json:"key"`
//	Url string `json:"url"`
//}
//
//func (o *ObsRecord) GetThumbUrl(marketId, clientType uint32, w, h uint32) string {
//	return fmt.Sprintf("https://%s/%s/%s/%s?obs-process=image/resize/m/fixed/w/%d/h/%d",
//		marketid_helper.GetObsDomain(marketId, clientType), ObsApp, ObsScope, o.Key, w, h)
//}
//
//func (o *ObsRecord) GetOriginUrl(marketId, clientType uint32) string {
//	return obsDataUrl(o.Key, marketId, clientType)
//}

type ObsRecord struct {
	EmojiId   string `json:"emoji_id"`
	Md5       string `json:"md_5"`
	OriginUrl string `json:"origin_url"`
	OriginH   uint32 `json:"origin_h"`
	OriginW   uint32 `json:"origin_w"`
	ThumbH    uint32 `json:"thumb_h"`
	ThumbW    uint32 `json:"thumb_w"`
	Key       string `json:"key"`
	ObsUrl    string `json:"obs_url"`
}

func (c *ObsRecord) GetThumbUrl(marketId, clientType uint32) string {
	return fmt.Sprintf("https://%s/%s/%s/%s?obs-process=image/resize/m/fixed/w/%d/h/%d",
		marketid_helper.GetObsDomain(marketId, clientType), ObsApp, ObsScope, c.Key, c.ThumbW, c.ThumbH)
}

func (c *ObsRecord) GetOriginUrl(marketId, clientType uint32) string {
	return obsDataUrl(c.Key, marketId, clientType)
}
