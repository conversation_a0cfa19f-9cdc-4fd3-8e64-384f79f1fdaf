package cache

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/common-emoji/internal/mgr/entity"
	"time"
)

const (
	hotClickRateKey = "hot_click_rate_%s"
	tempHotEmojiKey = "hot"
	tempEmojiPrefix = "temp_emoji_%s"
)

func getHotClickRateKey(timeStamp int64) string {
	curDayStr := time.Unix(timeStamp, 0).Format("20060102")

	return fmt.Sprintf(hotClickRateKey, curDayStr)
}

func getTempEmojiKey(suffix string) string {
	return fmt.Sprintf(tempEmojiPrefix, suffix)
}

func getHotTempEmojiKey(timeStamp int64) string {
	curDayStr := time.Unix(timeStamp, 0).Format("20060102")
	return getTempEmoji<PERSON>ey(tempHotEmojiKey + "_" + curDayStr)
}

func (c *RedisDao) GetHotPoolSize(ctx context.Context) (int, error) {
	return c.GetPoolSize(ctx, c.getPoolKey(hotEmojiPool))
}

func (c *RedisDao) GetRandHotEmoji(ctx context.Context, count int) ([]*entity.HotCacheEmoji, error) {
	_, emojis, err := c.GetRandEmojiByKey(ctx, c.getPoolKey(hotEmojiPool), count, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRandEmoji GetRandEmojiByKey err:%v", err)
		return nil, err
	}
	return emojis, nil
}

func (c *RedisDao) AddHotClickRate(ctx context.Context, md5 string) error {
	key := getHotClickRateKey(time.Now().Unix())
	err := c.cli.ZIncrBy(ctx, key, float64(1), md5).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddHotClickRate ZIncr md5:%s err:%v", md5, err)
		return err
	}
	return nil
}

// GetTopHotClickRateByDate 获取指定天内点击率TopX的表情
func (c *RedisDao) GetTopHotClickRateByDate(ctx context.Context, timeStamp int64, topN int) ([]string, error) {
	key := getHotClickRateKey(timeStamp)
	members, err := c.cli.ZRevRangeByScore(ctx, key, &redis.ZRangeBy{
		Min:    "0",
		Max:    "+inf",
		Offset: 0,
		Count:  int64(topN),
	}).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTopHotClickRateByWeek ZRevRangeByScore key:%s err:%v", key, err)
		return nil, err
	}
	return members, nil
}

// 记录一天内发送过的第三方表情包信息
func (c *RedisDao) RecordHotTempEmojis(ctx context.Context, emojis *entity.HotCacheEmoji) error {
	return c.RecordEmojisByKey(ctx, getHotTempEmojiKey(time.Now().Unix()), emojis)
}

// 获取当天发送过的第三方表情包信息
func (c *RedisDao) BatGetHotTempEmojis(ctx context.Context, timeStamp int64, md5List []string) ([]*entity.HotCacheEmoji, error) {
	return c.BatGetEmojisByKey(ctx, getHotTempEmojiKey(timeStamp), md5List)
}

// 更新缓存
func (c *RedisDao) BatRefreshHotEmojis(ctx context.Context, emojis []*entity.HotCacheEmoji) error {
	return c.BatRecordEmojiInfos(ctx, c.getPoolKey(hotEmojiPool), emojis)
}

func (c *RedisDao) RandDelHotEmoji(ctx context.Context, count int) ([]*entity.HotCacheEmoji, error) {
	ids, infos, err := c.GetRandEmojiByKey(ctx, c.getPoolKey(hotEmojiPool), count, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "RandDelHotEmoji GetRandEmojiByKey err:%v", err)
		return nil, err
	}

	if len(ids) == 0 {
		log.WarnWithCtx(ctx, "RandDelHotEmoji count:%d ids is empty", count)
		return nil, nil
	}
	_, err = c.cli.HDel(ctx, c.getPoolKey(hotEmojiPool), ids...).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "RandDelHotEmoji HDel ids:%v err:%v", ids, err)
		return nil, err
	}
	return infos, nil
}

func (c *RedisDao) CleanHotMsgByTimeStamp(ctx context.Context, timeStamp int64) error {

	_, err := c.cli.Unlink(ctx, getHotClickRateKey(timeStamp)).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "CleanHotMsgByTimeStamp Rate Unlink err:%v", err)
		return err
	}
	_, err = c.cli.Unlink(ctx, getHotTempEmojiKey(timeStamp)).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "CleanHotMsgByTimeStamp Temp Unlink err:%v", err)
		return err
	}
	return nil
}


func (c *RedisDao) DelHotEmoji(ctx context.Context, md5 []string) error {
	return c.DelEmoji(ctx, hotEmojiPool, md5)
}