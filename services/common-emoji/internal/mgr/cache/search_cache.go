package cache

import (
	"context"
	"encoding/json"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/common-emoji/internal/mgr/entity"
)

func (c *RedisDao) GenKeyWordEmojiKey(keyWord string) string {
	return KeyWordEmojiKey + "_" + keyWord
}

func (c *RedisDao) IsKeyWordEmojiExist(ctx context.Context, keyWord string, md5 string) (bool, error) {
	keyWordKey := c.GenKeyWordEmoji<PERSON>ey(keyWord)
	
	isExist, err := c.cli.SIsMember(ctx, keyWordKey, md5).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "IsKeyWordEmojiExist HExists err:%+v, keyWordKey:%s, md5:%s", err, keyWordKey, md5)
	}
	return isExist, err
	
}

func (c *RedisDao) BatAddToCache(ctx context.Context, keyword string, md5s ...string) error {
	keyWordKey := c.GenKey<PERSON>ordEmoji<PERSON>ey(keyword)
	addedNum, err := c.cli.SAdd(ctx, keyWordKey, md5s).Result()
	if err != nil {
		return err
	}
	log.InfoWithCtx(ctx, "BatAddToCache keyword:%s, addedNum:%d", keyword, addedNum)
	return nil
}

func (c *RedisDao) GetRandSearchEmoji(ctx context.Context, keyword string, count int64) ([]string, error) {
	return c.cli.SRandMemberN(ctx, c.GenKeyWordEmojiKey(keyword), count).Result()
}

func (c *RedisDao) BatGetObsRecord(ctx context.Context, emojiType uint32, md5s ...string) ([]*entity.ObsRecord, error) {
	obsRecordList := make([]*entity.ObsRecord, 0, len(md5s))
	infos, err := c.cli.HMGet(ctx, c.getEmojiInObsKey(emojiType), md5s...).Result()
	if err != nil {
		return nil, err
	}
	for index := range md5s {
		if info := infos[index]; info != nil {
			if strInfo, ok := info.(string); ok {
				record := &entity.ObsRecord{}
				err = json.Unmarshal([]byte(strInfo), record)
				if err != nil {
					log.ErrorWithCtx(ctx, "BatGetObsRecord json.Unmarshal err:%+v, info:%v", err, info)
					continue
				}
				obsRecordList = append(obsRecordList, record)
			} else {
				log.ErrorWithCtx(ctx, "BatGetObsRecord HMGet type err, info:%v", info)
			}
		}
	}
	return obsRecordList, nil
}

func (c *RedisDao) AddObsRecord(ctx context.Context, obsRecord *entity.ObsRecord, emojiType uint32) error {
	
	bytesRecord, err := json.Marshal(obsRecord)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddObsRecord Marshal obsRecord:%v err(%v)", obsRecord, err)
		return err
	}
	_, err = c.cli.HSet(ctx, c.getEmojiInObsKey(emojiType), obsRecord.Md5, bytesRecord).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddObsRecord HSet obsRecord:%v err(%v)", obsRecord, err)
		return err
	}
	return nil
}

func (c *RedisDao) RandDelSearchEmojiCache(ctx context.Context, keyword string, count int64) error {
	// 待优化，使用分布式锁防止多个请求并发删除
	delMd5, err := c.cli.SPopN(ctx, c.GenKeyWordEmojiKey(keyword), count).Result()
	if err != nil {
		return err
	}
	log.InfoWithCtx(ctx, "RandDelSearchEmojiCache keyword:%s, delMd5:%v", keyword, delMd5)
	return nil
}

func (c *RedisDao) DelSearchEmojiCache(ctx context.Context, keyword string, md5s ...string) error {
	return c.cli.SRem(ctx, c.GenKeyWordEmojiKey(keyword), md5s).Err()
}
