package cache

import (
	"context"
	"github.com/smartystreets/goconvey/convey"
	"golang.52tt.com/services/common-emoji/internal/mgr/entity"
	"testing"
	"time"
)

func TestHotEmojiFunctions(t *testing.T) {
	convey.Convey("TestCacheFunctions", t, func() {
		ctx := context.Background()

		// Test GetHotPoolSize
		convey.Convey("GetHotPoolSize", func() {
			emojis := &entity.HotCacheEmoji{
				EmojiId:   "1",
				Md5:       "md5_1",
				OriginUrl: "url_1",
				OriginH:   100,
				OriginW:   100,
				ThumbH:    50,
				ThumbW:    50,
			}
			emojis2 := &entity.HotCacheEmoji{
				EmojiId:   "2",
				Md5:       "md5_2",
				OriginUrl: "url_2",
				OriginH:   200,
				OriginW:   200,
				ThumbH:    100,
				ThumbW:    100,
			}
			err := redisDao.BatRecordEmojiInfos(ctx, redisDao.getPoolKey(hotEmojiPool), []*entity.HotCacheEmoji{emojis, emojis2})
			expectedSize := 2
			result, err := redisDao.GetHotPoolSize(ctx)
			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldEqual, expectedSize)
		})

		// Test GetRandHotEmoji
		convey.Convey("GetRandHotEmoji", func() {
			emojis := &entity.HotCacheEmoji{
				EmojiId:   "1",
				Md5:       "md5_1",
				OriginUrl: "url_1",
				OriginH:   100,
				OriginW:   100,
				ThumbH:    50,
				ThumbW:    50,
			}
			emojis2 := &entity.HotCacheEmoji{
				EmojiId:   "2",
				Md5:       "md5_2",
				OriginUrl: "url_2",
				OriginH:   200,
				OriginW:   200,
				ThumbH:    100,
				ThumbW:    100,
			}
			err := redisDao.BatRecordEmojiInfos(ctx, redisDao.getPoolKey(hotEmojiPool), []*entity.HotCacheEmoji{emojis, emojis2})

			count := 2
			expectedEmojis := []*entity.HotCacheEmoji{
				{EmojiId: "1", Md5: "md5_1", OriginUrl: "url_1", OriginH: 100, OriginW: 100, ThumbH: 50, ThumbW: 50},
				{EmojiId: "2", Md5: "md5_2", OriginUrl: "url_2", OriginH: 200, OriginW: 200, ThumbH: 100, ThumbW: 100},
			}
			result, err := redisDao.GetRandHotEmoji(ctx, count)
			convey.So(err, convey.ShouldBeNil)
			convey.So(len(result), convey.ShouldResemble, len(expectedEmojis))
		})

		// Test AddHotClickRate
		convey.Convey("AddHotClickRate", func() {
			md5 := "md5_1"
			err := redisDao.AddHotClickRate(ctx, md5)
			err = redisDao.AddHotClickRate(ctx, md5)
			md52 := "md5_2"
			err = redisDao.AddHotClickRate(ctx, md52)
			convey.So(err, convey.ShouldBeNil)
		})

		// Test GetTopHotClickRateByDate
		convey.Convey("GetTopHotClickRateByDate", func() {
			timeStamp := time.Now().Unix()
			topN := 2
			expectedMembers := []string{"md5_1", "md5_2"}
			result, err := redisDao.GetTopHotClickRateByDate(ctx, timeStamp, topN)
			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldResemble, expectedMembers)
		})

		// Test RecordHotTempEmojis
		convey.Convey("RecordHotTempEmojis", func() {
			emojis := &entity.HotCacheEmoji{
				EmojiId:   "1",
				Md5:       "md5_1",
				OriginUrl: "url_1",
				OriginH:   100,
				OriginW:   100,
				ThumbH:    50,
				ThumbW:    50,
			}
			err := redisDao.RecordHotTempEmojis(ctx, emojis)
			emojis2 := &entity.HotCacheEmoji{
				EmojiId:   "2",
				Md5:       "md5_2",
				OriginUrl: "url_2",
				OriginH:   200,
				OriginW:   200,
				ThumbH:    100,
				ThumbW:    100,
			}
			err = redisDao.RecordHotTempEmojis(ctx, emojis2)
			convey.So(err, convey.ShouldBeNil)
		})

		// Test BatGetHotTempEmojis
		convey.Convey("BatGetHotTempEmojis", func() {
			timeStamp := time.Now().Unix()
			md5List := []string{"md5_1", "md5_2"}
			expectedEmojis := []*entity.HotCacheEmoji{
				{EmojiId: "1", Md5: "md5_1", OriginUrl: "url_1", OriginH: 100, OriginW: 100, ThumbH: 50, ThumbW: 50},
				{EmojiId: "2", Md5: "md5_2", OriginUrl: "url_2", OriginH: 200, OriginW: 200, ThumbH: 100, ThumbW: 100},
			}
			result, err := redisDao.BatGetHotTempEmojis(ctx, timeStamp, md5List)
			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldResemble, expectedEmojis)
		})

		// Test BatRefreshHotEmojis
		convey.Convey("BatRefreshHotEmojis", func() {
			emojis := []*entity.HotCacheEmoji{
				{EmojiId: "1", Md5: "md5_1", OriginUrl: "url_1", OriginH: 100, OriginW: 100, ThumbH: 50, ThumbW: 50},
				{EmojiId: "2", Md5: "md5_2", OriginUrl: "url_2", OriginH: 200, OriginW: 200, ThumbH: 100, ThumbW: 100},
			}
			err := redisDao.BatRefreshHotEmojis(ctx, emojis)
			convey.So(err, convey.ShouldBeNil)
		})

		// Test RandDelHotEmoji
		convey.Convey("RandDelHotEmoji", func() {
			count := 2
			_,err := redisDao.RandDelHotEmoji(ctx, count)
			convey.So(err, convey.ShouldBeNil)
		})

		// Test CleanHotMsgByTimeStamp
		convey.Convey("CleanHotMsgByTimeStamp", func() {
			timeStamp := time.Now().Unix()
			err := redisDao.CleanHotMsgByTimeStamp(ctx, timeStamp)
			convey.So(err, convey.ShouldBeNil)
		})
	})
}
