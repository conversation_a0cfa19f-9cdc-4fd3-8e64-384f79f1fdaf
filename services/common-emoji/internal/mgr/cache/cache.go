package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app/im"
	"golang.52tt.com/services/common-emoji/internal/mgr/entity"
)

const (
	poolKeyPrefix   = "emoji_pool_%s"
	hotEmojiPool    = "hotkey"
	KeyWordEmojiKey = "keyword_emoji"
	emojiInObsKey   = "emoji_in_obs"
)

func (c *RedisDao) getPoolKey(key string) string {
	return fmt.Sprintf(poolKeyPrefix, key)
}

func (c *RedisDao) GetRandEmojiByKey(ctx context.Context, key string, count int, withValue bool) ([]string, []*entity.HotCacheEmoji, error) {
	valueSlice, err := c.cli.HRandField(ctx, key, count, withValue).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRandEmojiByKey HRandField err:%v", err)
		return nil, nil, err
	}
	if !withValue {
		return valueSlice, nil, nil
	}
	emojis := make([]*entity.HotCacheEmoji, 0, count)
	fieldSlice := make([]string, 0, count)
	for i := 0; i < len(valueSlice)-1; i += 2 {
		k := valueSlice[i]
		data := valueSlice[i+1]
		var emoji *entity.HotCacheEmoji
		err = json.Unmarshal([]byte(data), &emoji)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAllHotEmoji Unmarshal k:%s v:%s err:%v", k, data, err)
			continue
		}
		fieldSlice = append(fieldSlice, k)
		emojis = append(emojis, emoji)
	}
	return fieldSlice, emojis, nil
}

func (c *RedisDao) GetPoolSize(ctx context.Context, key string) (int, error) {
	size, err := c.cli.HLen(ctx, key).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetHotPoolSize HLen err:%v", err)
		return 0, err
	}
	return int(size), nil
}


func (c *RedisDao) GetSearchCacheSize(ctx context.Context, keyword string) (int, error) {
	key := c.GenKeyWordEmojiKey(keyword)
	size, err := c.cli.SCard(ctx, key).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetHotPoolSize HLen err:%v", err)
		return 0, err
	}
	return int(size), nil
}

func (c *RedisDao) DelEmoji(ctx context.Context, keyword string, md5 []string) error {
	_, err := c.cli.HDel(ctx, c.getPoolKey(keyword), md5...).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelEmoji HDel err:%v", err)
		return err
	}
	return nil
}


func (c *RedisDao) RecordEmojisByKey(ctx context.Context, key string, emojis *entity.HotCacheEmoji) error {
	data, err := json.Marshal(emojis)
	if err != nil {
		log.ErrorWithCtx(ctx, "RecordEmojisByKey Marshal key:%s emojis:%+v err:%v", key, emojis, err)
		return err
	}

	err = c.cli.HSetNX(ctx, key, emojis.Md5, data).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "RecordEmojisByKey HSetNX key:%s emojis:%+v err:%v", key, emojis, err)
		return err
	}
	return nil
}

func (c *RedisDao) BatGetEmojisByKey(ctx context.Context, key string, md5List []string) ([]*entity.HotCacheEmoji, error) {
	infos, err := c.cli.HMGet(ctx, key, md5List...).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "BatGetEmojisByKey HMGet suffix:%s md5s:%+v err:%v", key, md5List, err)
		return nil, err
	}

	emojis := make([]*entity.HotCacheEmoji, 0, len(infos))
	for index := range md5List {
		if info := infos[index]; info != nil {
			if strInfo, ok := info.(string); ok {
				emoji := &entity.HotCacheEmoji{}
				err = json.Unmarshal([]byte(strInfo), emoji)
				if err != nil {
					log.ErrorWithCtx(ctx, "BatGetEmojisByKey json.Unmarshal err:%+v, info:%v", err, info)
					continue
				}
				emojis = append(emojis, emoji)
				//log.InfoWithCtx(ctx, "GetInSameRoomUidByUids HMGet info:%+v", info)
			} else {
				log.ErrorWithCtx(ctx, "BatGetEmojisByKey HMGet type err, info:%v", info)
			}
		}
	}
	return emojis, nil
}

func (c *RedisDao) BatRecordEmojiInfos(ctx context.Context, key string, emojis []*entity.HotCacheEmoji) error {
	if len(emojis) == 0 {
		return nil
	}
	fieldMap := make(map[string]interface{}, len(emojis))
	for _, emoji := range emojis {
		data, err := json.Marshal(emoji)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatRecordEmojiInfos Marshal emoji:%+v err:%v", emoji, err)
			continue
		}
		fieldMap[emoji.Md5] = data
	}
	_, err := c.cli.HMSet(ctx, key, fieldMap).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "BatRecordEmojiInfos HMSet key:%s emojis:%+v err:%v", key, emojis, err)
		return err
	}
	return nil
}

func (c *RedisDao) BatGetHotEmojis(ctx context.Context, md5List []string) ([]*entity.HotCacheEmoji, error) {
	return c.BatGetEmojisByKey(ctx, c.getPoolKey(hotEmojiPool), md5List)
}

func (c *RedisDao) getEmojiInObsKey(emojiType uint32) string {
	if im.EmojiMsg_EmojiType(emojiType) == im.EmojiMsg_EMOJI_TYPE_RECOMMEND {
		return emojiInObsKey + "_hot"
	} else if im.EmojiMsg_EmojiType(emojiType) == im.EmojiMsg_EMOJI_TYPE_KEYWORD {
		return emojiInObsKey + "_keyword"
	} else {
		return emojiInObsKey + "_" + im.EmojiMsg_EmojiType(emojiType).String()
	}
}

func (c *RedisDao) GetEmojiObsRecord(ctx context.Context, md5 string, emojiType uint32) (*entity.ObsRecord, error) {

	bytesStr, err := c.cli.HGet(ctx, c.getEmojiInObsKey(emojiType), md5).Result()
	if err != nil {
		if redis.IsNil(err) {
			return nil, nil
		}
		return nil, err
	}
	obsRecord := &entity.ObsRecord{}
	err = json.Unmarshal([]byte(bytesStr), &obsRecord)
	if err != nil {
		log.ErrorWithCtx(ctx, "IsEmojiInObs Unmarshal md5:%s err(%v)", md5, err)
		return nil, err
	}
	return obsRecord, nil

}

func (c *RedisDao) DelObsRecord(ctx context.Context, md5 string, emojiType uint32) error {
	_, err := c.cli.HDel(ctx, c.getEmojiInObsKey(emojiType), md5).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelObsRecord md5:%s err(%v)", md5, err)
		return err
	}
	return nil
}

func (c *RedisDao) BatGetEmojiObsRecord(ctx context.Context, md5s []string, emojiType uint32) ([]*entity.ObsRecord, error) {

	infos, err := c.cli.HMGet(ctx, c.getEmojiInObsKey(emojiType), md5s...).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "BatGetEmojiObsRecord HMGet emojiType:%d md5s:%+v err:%v", emojiType, md5s, err)
		return nil, err
	}
	obsRecords := make([]*entity.ObsRecord, 0, len(md5s))
	

	for index := range md5s {
		if info := infos[index]; info != nil {
			if strInfo, ok := info.(string); ok {
				record := &entity.ObsRecord{}
				err = json.Unmarshal([]byte(strInfo), record)
				if err != nil {
					log.ErrorWithCtx(ctx, "BatGetEmojiObsRecord json.Unmarshal err:%+v, info:%v", err, info)
					continue
				}
				obsRecords = append(obsRecords, record)
			} else {
				log.ErrorWithCtx(ctx, "BatGetEmojiObsRecord HMGet type err, info:%v", info)
			}
		}
	}
	return obsRecords, nil

}
