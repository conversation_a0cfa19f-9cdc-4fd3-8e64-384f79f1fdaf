package cache

import (
	"context"
	"encoding/json"
	"github.com/smartystreets/goconvey/convey"
	redismocks "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/mocks"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app/im"
	"golang.52tt.com/services/common-emoji/internal/mgr/entity"
	"testing"
)

var redisDao *RedisDao

func init() {
	redisCli := redismocks.Mock()
	if redisCli == nil {
		return
	}
	redisDao = &RedisDao{cli: redisCli}
}
func TestCacheFunctions(t *testing.T) {
	convey.Convey("TestCacheFunctions", t, func() {
		ctx := context.Background()

		// Test GetPoolKey
		convey.Convey("GetPoolKey", func() {
			key := "test_key"
			expected := "emoji_pool_test_key"
			result := redisDao.getPoolKey(key)
			convey.So(result, convey.ShouldEqual, expected)
		})

		// Test BatRecordEmojiInfos
		convey.Convey("BatRecordEmojiInfos", func() {
			key := "test_key"
			emojis := []*entity.HotCacheEmoji{
				{EmojiId: "1", Md5: "md5_1", OriginUrl: "url_1", OriginH: 100, OriginW: 100, ThumbH: 50, ThumbW: 50},
				{EmojiId: "2", Md5: "md5_2", OriginUrl: "url_2", OriginH: 200, OriginW: 200, ThumbH: 100, ThumbW: 100},
			}
			err := redisDao.BatRecordEmojiInfos(ctx, key, emojis)
			convey.So(err, convey.ShouldBeNil)
		})

		// Test GetRandEmojiByKey
		convey.Convey("GetRandEmojiByKey", func() {
			key := "test_key"
			count := 2
			withValue := true
			expectedEmojis := []*entity.HotCacheEmoji{
				{EmojiId: "1", Md5: "md5_1", OriginUrl: "url_1", OriginH: 100, OriginW: 100, ThumbH: 50, ThumbW: 50},
				{EmojiId: "2", Md5: "md5_2", OriginUrl: "url_2", OriginH: 200, OriginW: 200, ThumbH: 100, ThumbW: 100},
			}
			_, result, err := redisDao.GetRandEmojiByKey(ctx, key, count, withValue)
			convey.So(err, convey.ShouldBeNil)
			convey.So(len(result), convey.ShouldResemble, len(expectedEmojis))
		})

		// Test BatRecordEmojiInfos
		convey.Convey("BatRecordEmojiInfosByKeyWord", func() {
			key := "keyword"
			keyWordPool := redisDao.getPoolKey(key)
			emojis := []*entity.HotCacheEmoji{
				{EmojiId: "1", Md5: "md5_1", OriginUrl: "url_1", OriginH: 100, OriginW: 100, ThumbH: 50, ThumbW: 50},
				{EmojiId: "2", Md5: "md5_2", OriginUrl: "url_2", OriginH: 200, OriginW: 200, ThumbH: 100, ThumbW: 100},
			}
			err := redisDao.BatRecordEmojiInfos(ctx, keyWordPool, emojis)
			convey.So(err, convey.ShouldBeNil)
		})

		// Test GetPoolSize
		convey.Convey("GetPoolSize", func() {
			key := "keyword"
			keyWordPool := redisDao.getPoolKey(key)
			expectedSize := 2
			result, err := redisDao.GetPoolSize(ctx, keyWordPool)
			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldEqual, expectedSize)
		})
		

		// Test DelEmoji
		convey.Convey("DelEmoji", func() {
			keyword := "test_key"
			md5 := []string{"md5_1", "md5_2"}
			err := redisDao.DelEmoji(ctx, keyword, md5)
			convey.So(err, convey.ShouldBeNil)
		})

		// Test GenKeyWordEmojiKey
		convey.Convey("GenKeyWordEmojiKey", func() {
			keyWord := "test_keyword"
			expected := "keyword_emoji_test_keyword"
			result := redisDao.GenKeyWordEmojiKey(keyWord)
			convey.So(result, convey.ShouldEqual, expected)
		})

		// Test RecordEmojisByKey
		convey.Convey("RecordEmojisByKey", func() {
			key := "test_key"
			emojis := &entity.HotCacheEmoji{
				EmojiId:   "1",
				Md5:       "md5_1",
				OriginUrl: "url_1",
				OriginH:   100,
				OriginW:   100,
				ThumbH:    50,
				ThumbW:    50,
			}
			err := redisDao.RecordEmojisByKey(ctx, key, emojis)
			convey.So(err, convey.ShouldBeNil)
		})

		// Test BatGetEmojisByKey
		convey.Convey("BatGetEmojisByKey", func() {
			key := "test_key"
			md5List := []string{"md5_1", "md5_2"}
			expectedEmojis := []*entity.HotCacheEmoji{
				{EmojiId: "1", Md5: "md5_1", OriginUrl: "url_1", OriginH: 100, OriginW: 100, ThumbH: 50, ThumbW: 50},
				{EmojiId: "2", Md5: "md5_2", OriginUrl: "url_2", OriginH: 200, OriginW: 200, ThumbH: 100, ThumbW: 100},
			}
			result, err := redisDao.BatGetEmojisByKey(ctx, key, md5List)
			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldResemble, expectedEmojis)
		})

		// Test GetEmojiObsRecord
		convey.Convey("AddObsRecord", func() {
			expectedEmojis := &entity.ObsRecord{
				Md5: "md5_1",
				Key: "key",
			}
			err := redisDao.AddObsRecord(ctx, expectedEmojis,1)
			convey.So(err, convey.ShouldBeNil)
		})

		// Test GetEmojiObsRecord
		convey.Convey("GetEmojiObsRecord", func() {
			md5 := "md5_1"
			expectedEmojis := &entity.ObsRecord{
				Md5: "md5_1",
				Key: "key",
			}
			result, err := redisDao.GetEmojiObsRecord(ctx, md5,1)
			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldResemble, expectedEmojis)
		})
		
		
		// Test GetEmojiObsRecord
		convey.Convey("BatGetEmojiObsRecord", func() {
			md5s := []string{"md5_1","md5_2"}
			expectedEmojis := []*entity.ObsRecord{
				{
					Md5: "md5_1",
					Key: "key",
				},
			}
			result, err := redisDao.BatGetEmojiObsRecord(ctx, md5s,1)
			log.InfoWithCtx(ctx, "result:%+v err:%v",result,err)
			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldResemble, expectedEmojis)
		})
		
		emojiMsg := &im.EmojiMsg{
			Id:        "5db6e17f10afa72e6af63f4e64d10bad",
			Url:       "http://dl2.weshineapp.com/gif/20240713/de55e2436b00661ff6b65f542256280e.gif?v=66921f009d08c&f=tt",
			Height:    300,
			Width:     300,
			EmojiType: 1,
			Keyword:   "",
			Md5:       "5db6e17f10afa72e6af63f4e64d10bad",
			ThumbH:    130,
			ThumbW:    130,
			IsObs:     false,
			ObsKey:    "",
		}
		b ,_ := json.Marshal(emojiMsg)
		log.Infof("emojiMsg:%v",string(b))
	})
	
	
}
