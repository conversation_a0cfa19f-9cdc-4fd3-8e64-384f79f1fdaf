package mgr

import (
	"context"
	"golang.52tt.com/clients/obsgateway"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/thirdparty-emoji/shanmeng"
	"golang.52tt.com/protocol/app/im"
	config "golang.52tt.com/services/common-emoji/internal/config/common_emoji"
	"golang.52tt.com/services/common-emoji/internal/mgr/cache"
	"golang.52tt.com/services/common-emoji/internal/mgr/entity"
	"math/rand"
	"time"
)

const (
	shineHotEmojiLimit = 20
)

type ShineEmojiMgr struct {
	// 闪萌openId
	ShineOpenId int64 `json:"shine_open_id"`
	// 闪萌secret
	ShineSecret string `json:"shine_secret"`
	// 闪萌热门推荐表情包url
	ShineHotUrl string `json:"shine_hot_url"`

	shineHotEmojiCall *shanmeng.EmojiCall
	redisDao          *cache.RedisDao
	obsClient         obsgateway.IClient
}

func NewShineEmojiMgr(redisDao *cache.RedisDao, hotEmojiCall *shanmeng.EmojiCall, obsClient obsgateway.IClient) *ShineEmojiMgr {
	return &ShineEmojiMgr{
		shineHotEmojiCall: hotEmojiCall,
		redisDao:          redisDao,
		obsClient:         obsClient,
	}
}

func (m *ShineEmojiMgr) GetShineHotEmoji(ctx context.Context) ([]*shanmeng.DataT, error) {
	offset := rand.Intn(30)

	dataRsp, err := m.shineHotEmojiCall.GetHotEmojis(ctx, offset, shineHotEmojiLimit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetShineHotEmoji err:%v", err)
		return nil, err
	}
	return dataRsp.Datas, nil
}

func (m *ShineEmojiMgr) IsShineHotUseCache(ctx context.Context) bool {
	if !config.GetCommonEmojiConfig().GetUseCacheSwitch() {
		return false
	}
	poolSize, err := m.redisDao.GetHotPoolSize(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "IsShineHotUseCache GetHotPoolSize err:%v", err)
		return false
	}
	return poolSize > config.GetCommonEmojiConfig().GetHotPoolSizeThreshold()
}

func (m *ShineEmojiMgr) GetShineHotEmojiFromCache(ctx context.Context) ([]*entity.HotCacheEmoji, error) {
	emojis, err := m.redisDao.GetRandHotEmoji(ctx, shineHotEmojiLimit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetShineHotEmojiFromCache GetRandHotEmoji err:%v", err)
		return nil, err
	}
	return emojis, nil
}

func (m *ShineEmojiMgr) HotEmojiStatistic(ctx context.Context, emojiMsg *im.EmojiMsg) error {
	// 统计点击率
	err := m.redisDao.AddHotClickRate(ctx, emojiMsg.GetMd5())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddHotClickRate emojiMsg:%s err(%v)", emojiMsg.String(), err)
		return err
	}
	// 记录第三方表情
	err = m.redisDao.RecordHotTempEmojis(ctx, &entity.HotCacheEmoji{
		EmojiId:   emojiMsg.GetId(),
		Md5:       emojiMsg.GetMd5(),
		OriginUrl: emojiMsg.GetUrl(),
		OriginH:   emojiMsg.GetHeight(),
		OriginW:   emojiMsg.GetWidth(),
		ThumbH:    emojiMsg.GetThumbH(),
		ThumbW:    emojiMsg.GetThumbW(),
		Key:       emojiMsg.GetObsKey(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "RecordHotTempEmojis emojiMsg:%s err(%v)", emojiMsg.String(), err)
		return err
	}
	return nil
}

func (m *ShineEmojiMgr) GetTopNHotEmojiInfos(ctx context.Context, timeStamp int64, topN int) ([]string, []*entity.HotCacheEmoji, error) {
	emojiMd5s, err := m.redisDao.GetTopHotClickRateByDate(ctx, timeStamp, topN)
	if err != nil {
		log.ErrorWithCtx(ctx, "RefreshHotEmojiPool GetTopHotClickRateByDate timeStamp:%d err(%v)", timeStamp, err)
		return nil, nil, err
	}
	if len(emojiMd5s) == 0 {
		return nil, nil, nil
	}
	emojis, err := m.redisDao.BatGetHotTempEmojis(ctx, timeStamp, emojiMd5s)
	if err != nil {
		log.ErrorWithCtx(ctx, "RefreshHotEmojiPool BatGetHotTempEmojis timeStamp:%d err(%v)", timeStamp, err)
		return nil, nil, err
	}
	return emojiMd5s, emojis, nil
}

func (m *ShineEmojiMgr) RefreshHotEmojiPool(ctx context.Context, newEmojis []*entity.HotCacheEmoji) ([]*entity.HotCacheEmoji, error) {

	// 判断池子大小，大于阈值则随机删除
	size, err := m.redisDao.GetHotPoolSize(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "RefreshHotEmojiPool newEmojis:%v GetHotPoolSize err(%v)", newEmojis, err)
		return nil, err
	}
	var delInfos []*entity.HotCacheEmoji
	if size >= config.GetCommonEmojiConfig().GetHotEmojiPoolMaxSize() {
		delNum := config.GetCommonEmojiConfig().GetHotEmojiPoolDeleteNum()
		if size < delNum {
			delNum = size
		}
		delInfos, err = m.redisDao.RandDelHotEmoji(ctx, delNum)
		if err != nil {
			log.ErrorWithCtx(ctx, "RefreshHotEmojiPool newEmojis:%v RandDelHotEmoji err(%v)", newEmojis, err)
			return delInfos, err
		}
		log.InfoWithCtx(ctx, "RefreshHotEmojiPool len(newEmojis):%d RandDelHotEmoji delNum:%d", len(newEmojis), delNum)
	}

	// 更新热门池子
	err = m.redisDao.BatRefreshHotEmojis(ctx, newEmojis)
	if err != nil {
		log.ErrorWithCtx(ctx, "RefreshHotEmojiPool BatRefreshHotEmojis newEmojis:%v err(%v)", newEmojis, err)
		return delInfos, err
	}
	return delInfos, nil
}

func (m *ShineEmojiMgr) CleanExpireHotEmoji(ctx context.Context) error {
	last3Day := time.Now().AddDate(0, 0, -3)
	timeStamp := last3Day.Unix()
	err := m.redisDao.CleanHotMsgByTimeStamp(ctx, timeStamp)
	if err != nil {
		log.ErrorWithCtx(ctx, "CleanExpireHotEmoji CleanHotMsgByTimeStamp timeStamp:%d err(%v)", timeStamp, err)
		return err
	}
	log.InfoWithCtx(ctx, "CleanExpireHotEmoji timeStamp:%d clean finish", timeStamp)
	return nil
}

func (m *ShineEmojiMgr) DeleteHotEmoji(ctx context.Context, md5 string) error {
	err := m.redisDao.DelObsRecord(ctx, md5, uint32(im.EmojiMsg_EMOJI_TYPE_RECOMMEND))
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteHotEmoji md5:%s err(%v)", md5, err)
		return err
	}

	err = m.redisDao.DelHotEmoji(ctx, []string{md5})
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteHotEmoji md5:%s err(%v)", md5, err)
		return err
	}
	return nil
}

func (m *ShineEmojiMgr) RecordEmojiObs(ctx context.Context, record *entity.ObsRecord, emojiType uint32) error {
	return m.redisDao.AddObsRecord(ctx, record, emojiType)
}

func (m *ShineEmojiMgr) CopyToObs(ctx context.Context, record *entity.ObsRecord, emojiType uint32) (key, retUrl string, err error) {
	url := record.OriginUrl
	md5 := record.Md5
	key, etag, retUrl, token, err := m.obsClient.CopyUrl(ctx, url, entity.ObsApp, entity.ObsScope,
		obsgateway.WithReturnUrl())
	if err != nil {
		log.ErrorWithCtx(ctx, "CopyToObs CopyUrl failed url:%s md5:%s emojiType:%d err:%v",
			url, md5, emojiType, err)
		return "", "", err
	}
	record.Key = key
	record.ObsUrl = retUrl
	lowErr := m.RecordEmojiObs(ctx, record, emojiType)
	if lowErr != nil {
		log.ErrorWithCtx(ctx, "CopyToObs RecordEmojiObs failed url:%s md5:%s emojiType:%d key:%s etag:%s retUrl:%s token:%s lowErr:%v",
			url, md5, emojiType, key, etag, retUrl, token, lowErr)
	} else {
		log.InfoWithCtx(ctx, "CopyToObs CopyUrl success url:%s md5:%s emojiType:%d key:%s etag:%s retUrl:%s token:%s",
			url, md5, emojiType, key, etag, retUrl, token)
	}
	
	return key, retUrl, nil
}

func (m *ShineEmojiMgr) BatGetObsInfosMap(ctx context.Context, md5s []string, emojiType uint32) (map[string]*entity.ObsRecord, error) {
	infos, err := m.redisDao.BatGetEmojiObsRecord(ctx, md5s, emojiType)
	if err != nil {
		return nil, err
	}
	obsMap := make(map[string]*entity.ObsRecord)
	for _, v := range infos {
		obsMap[v.Md5] = v
	}
	return obsMap, nil
}
