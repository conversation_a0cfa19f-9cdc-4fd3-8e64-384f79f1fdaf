package convertor

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/thirdparty-emoji/shanmeng"
	pb "golang.52tt.com/protocol/services/common-emoji"
	config "golang.52tt.com/services/common-emoji/internal/config/common_emoji"
	"golang.52tt.com/services/common-emoji/internal/mgr/entity"
)

func isFileLimitOk(fileSize uint32, ext string) (uint32, bool) {
	fileLimit := config.GetCommonEmojiConfig().GetFileLimits(ext)
	if fileSize <= fileLimit {
		return fileSize, true
	}
	return fileSize, false
}
func ConvertHttpEmojiToPb(ctx context.Context, data []*shanmeng.DataT) []*pb.CommonEmojiInfo {
	res := make([]*pb.CommonEmojiInfo, 0, len(data))
	if len(data) == 0 {
		return res
	}
	for _, d := range data {
		if pbData := ConvertOneHttpEmojiToPb(ctx, d); pbData != nil {
			res = append(res, pbData)
		}
	}
	return res
}

func ConvertOneHttpEmojiToPb(ctx context.Context, d *shanmeng.DataT) *pb.CommonEmojiInfo {
	// 原图为空,过滤掉
	if len(d.Origin.GetUrl()) == 0 {
		log.WarnWithCtx(ctx, "HttpEmojiConvertor invalid data:%v", d)
		return nil
	}
	fileSize, isOk := isFileLimitOk(d.Filesize, ".gif")
	if !isOk {
		log.WarnWithCtx(ctx, "fileszie not ok, fileSize:%d, data:%+v", fileSize, d)
		return nil
	}
	pbData := &pb.CommonEmojiInfo{
		EmojiId: d.Id,
		Md5:     d.Md5,
		OriginEmojiInfo: &pb.BaseEmojiInfo{
			Url:    d.Origin.GetUrl(),
			Height: uint32(d.Origin.H),
			Width:  uint32(d.Origin.W),
		},
		IsCache: false,
	}
	// 缩略图为空用原图
	if len(d.Thumb.GetUrl()) == 0 {
		pbData.ThumbEmojiInfo = pbData.OriginEmojiInfo
	} else {
		pbData.ThumbEmojiInfo = &pb.BaseEmojiInfo{
			Url:    d.Thumb.GetUrl(),
			Height: uint32(d.Thumb.H),
			Width:  uint32(d.Thumb.W),
		}
	}
	return pbData
	
}
func ConvertCacheEmojiToPb(marketId, clientType uint32, entityData []*entity.HotCacheEmoji) []*pb.CommonEmojiInfo {
	res := make([]*pb.CommonEmojiInfo, 0, len(entityData))
	if len(entityData) == 0 {
		return res
	}
	for _, d := range entityData {
		pbData := &pb.CommonEmojiInfo{
			EmojiId: d.EmojiId,
			Md5:     d.Md5,
			OriginEmojiInfo: &pb.BaseEmojiInfo{
				Url:    d.GetOriginUrl(marketId, clientType),
				Height: d.OriginH,
				Width:  d.OriginW,
			},
			ThumbEmojiInfo: &pb.BaseEmojiInfo{
				Url:    d.GetThumbUrl(marketId, clientType),
				Height: d.ThumbH,
				Width:  d.ThumbW,
			},
			IsCache: true,
			ObsKey:  d.Key,
		}
		res = append(res, pbData)
	}
	return res
}

func ConvertOneSearchEmojiObsRecordToPb(marketId, clientType uint32, record *entity.ObsRecord) *pb.CommonEmojiInfo {
	pbData := &pb.CommonEmojiInfo{
		EmojiId: record.EmojiId,
		Md5:     record.Md5,
		OriginEmojiInfo: &pb.BaseEmojiInfo{
			Url:    record.GetOriginUrl(marketId, clientType),
			Height: record.OriginH,
			Width:  record.OriginW,
		},
		ThumbEmojiInfo: &pb.BaseEmojiInfo{
			Url:    record.GetThumbUrl(marketId, clientType),
			Height: record.ThumbH,
			Width:  record.ThumbW,
		},
		IsCache: true,
		ObsKey:  record.Key,
	}
	return pbData
}

func ConvertSearchEmojiObsRecordsToPb(marketId, clientType uint32, records []*entity.ObsRecord) []*pb.CommonEmojiInfo {
	pbDatas := make([]*pb.CommonEmojiInfo, 0, len(records))
	for _, record := range records {
		pbDatas = append(pbDatas, ConvertOneSearchEmojiObsRecordToPb(marketId, clientType, record))
	}
	return pbDatas
}
