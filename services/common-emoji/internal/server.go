package internal

import (
	"context"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	eventlink_event "gitlab.ttyuyin.com/tt-infra/middleware/event"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	"golang.52tt.com/clients/obsgateway"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/thirdparty-emoji/shanmeng"
	"golang.52tt.com/protocol/app/im"
	"golang.52tt.com/protocol/services/demo/echo"
	config "golang.52tt.com/services/common-emoji/internal/config/common_emoji"
	"golang.52tt.com/services/common-emoji/internal/convertor"
	"golang.52tt.com/services/common-emoji/internal/event"
	"golang.52tt.com/services/common-emoji/internal/mgr"
	"golang.52tt.com/services/common-emoji/internal/mgr/cache"
	"golang.52tt.com/services/common-emoji/internal/mgr/entity"
	"golang.52tt.com/services/common-emoji/internal/timer"
	"math/rand"
	"runtime/debug"
	"time"
	
	pb "golang.52tt.com/protocol/services/common-emoji"
)

type StartConfig struct {
	// from config file
	RedisConfig *redisConnect.RedisConfig `json:"redis_config"`
	// 闪萌openId
	ShineOpenId int64 `json:"shine_open_id"`
	// 闪萌secret
	ShineSecret string `json:"shine_secret"`
	// 闪萌热门推荐表情包url
	ShineHotUrl string `json:"shine_hot_url"`
	SearchUrl   string `json:"search_url"`
	
	EventLink *eventlink_event.Options `json:"event_link"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.InfoWithCtx(ctx, "server startup with cfg: %+v", *cfg)
	
	err := config.InitCommonEmojiConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "InitCommonEmojiConfig err:%v", err)
		return nil, err
	}
	
	redisClient, err := redisConnect.NewClient(ctx, cfg.RedisConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "init redis fail, err: %v", err)
		return nil, err
	}
	
	redisDao := cache.NewRedisDao(redisClient)
	
	emojiCall := shanmeng.NewEmojiCall(cfg.ShineOpenId, cfg.ShineSecret, cfg.SearchUrl, cfg.ShineHotUrl)
	obsClient, err := obsgateway.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "obs_object_gateway.NewClient fail, err: %v", err)
		return nil, err
	}
	shineEmojiMgr := mgr.NewShineEmojiMgr(redisDao, emojiCall, obsClient)
	
	//err = local_cache.NewEmojiLocalCache(ctx, redisDao)
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "NewEmojiLocalCache err:%v", err)
	//	return nil, err
	//}
	factory, err := eventlink_event.NewEventFactory(cfg.EventLink)
	if err != nil {
		log.ErrorWithCtx(ctx, "event.NewEventFactory err(%v)", err)
		return nil, err
	}
	
	searchKeyMgr := mgr.NewSearchKeyMgr(redisDao)
	err = event.InitImMsgSubscriber(ctx, factory, shineEmojiMgr, searchKeyMgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitImMsgSubscriber err(%v)", err)
		return nil, err
	}
	
	err = event.InitAuditSubscriber(ctx, shineEmojiMgr, searchKeyMgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitAuditSubscriber err(%v)", err)
		return nil, err
	}
	emojiTimer := timer.NewEmojiTimer(shineEmojiMgr, searchKeyMgr, redisDao)
	err = emojiTimer.Start()
	if err != nil {
		log.ErrorWithCtx(ctx, "emojiTimer.Start err:%v", err)
		return nil, err
	}
	
	s := &Server{
		shineEmojiMgr: shineEmojiMgr,
		emojiCall:     emojiCall,
		emojiTimer:    emojiTimer,
		searchKeyMgr:  searchKeyMgr,
		obsClient:     obsClient,
	}
	return s, nil
}

type Server struct {
	shineEmojiMgr *mgr.ShineEmojiMgr
	searchKeyMgr  *mgr.SearchKeyMgr
	emojiCall     *shanmeng.EmojiCall
	emojiTimer    *timer.EmojiTimer
	obsClient     obsgateway.IClient
}

func (s *Server) ShutDown() {
	s.emojiTimer.Stop()
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) GetHotEmojis(ctx context.Context, req *pb.GetHotEmojisReq) (*pb.GetHotEmojisResp, error) {
	out := &pb.GetHotEmojisResp{}
	log.InfoWithCtx(ctx, "GetHotEmojis req:%s", req.String())
	marketId := metainfo.GetServiceInfo(ctx).MarketID()
	clientType := metainfo.GetServiceInfo(ctx).ClientType()
	
	useCache := req.GetUserCache()
	if req.GetReqSource() == pb.GetHotEmojisReq_SOURCE_GAME {
		useCache = useCache && s.shineEmojiMgr.IsShineHotUseCache(ctx)
	}
	
	if useCache {
		cacheData, err := s.shineEmojiMgr.GetShineHotEmojiFromCache(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetHotEmojis GetShineHotEmojiFromCache req:%s err:%v", req.String(), err)
			return out, err
		}
		out.Emojis = convertor.ConvertCacheEmojiToPb(marketId, uint32(clientType), cacheData)
	} else {
		shineDatas, err := s.shineEmojiMgr.GetShineHotEmoji(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetHotEmojis GetShineHotEmoji req:%s err:%v", req.String(), err)
			return out, err
		}
		out.Emojis = convertor.ConvertHttpEmojiToPb(ctx, shineDatas)
	}
	
	// 测试非法表情包
	if testEmojis := config.GetCommonEmojiConfig().GetTestIllegalEmoji(); len(testEmojis) > 0 {
		out.Emojis = append(testEmojis, out.GetEmojis()...)
		log.InfoWithCtx(ctx, "GetHotEmojis hit test Data req:%s len:%d", req.String(), len(out.GetEmojis()))
		return out, nil
	}
	log.InfoWithCtx(ctx, "GetHotEmojis success req:%s useCache:%v len(out):%d out:%s", req.String(), useCache,
		len(out.GetEmojis()), out.String())
	return out, nil
}

func (s *Server) GetEmojisByKeyWord(ctx context.Context, req *pb.GetEmojisByKeyWordReq) (*pb.GetEmojisByKeyWordResp, error) {
	out := &pb.GetEmojisByKeyWordResp{}
	
	marketId := metainfo.GetServiceInfo(ctx).MarketID()
	clientType := metainfo.GetServiceInfo(ctx).ClientType()
	switch req.GetEmojiSource() {
	case pb.EmojiSource_EMOJI_SOURCE_SHINE_API:
		out.Emojis, out.NeedAuditToCache = s.getSearchEmojiByShineApi(ctx, req.GetKeyWord(), marketId, uint32(clientType))
	case pb.EmojiSource_EMOJI_SOURCE_CACHE_OR_SHINE:
		if cacheEmojis := s.getSearchEmojiByCache(ctx, req.GetKeyWord(), marketId, uint32(clientType)); len(cacheEmojis) > 0 {
			out.Emojis = cacheEmojis
			out.NeedAuditToCache = false
		} else {
			out.Emojis, out.NeedAuditToCache = s.getSearchEmojiByShineApi(ctx, req.GetKeyWord(), marketId, uint32(clientType))
		}
	case pb.EmojiSource_EMOJI_SOURCE_CACHE:
		out.Emojis = s.getSearchEmojiByCache(ctx, req.GetKeyWord(), marketId, uint32(clientType))
	default:
		if req.GetUseCache() {
			out.Emojis = s.getSearchEmojiByCache(ctx, req.GetKeyWord(), marketId, uint32(clientType))
		} else {
			out.Emojis, out.NeedAuditToCache = s.getSearchEmojiByShineApi(ctx, req.GetKeyWord(), marketId, uint32(clientType))
		}
	}
	
	// 测试非法表情包
	if testEmojis := config.GetCommonEmojiConfig().GetTestIllegalEmoji(); len(testEmojis) > 0 {
		out.Emojis = append(testEmojis, out.GetEmojis()...)
		log.InfoWithCtx(ctx, "GetHotEmojis hit test Data req:%s len:%d", req.String(), len(out.GetEmojis()))
		return out, nil
	}
	log.InfoWithCtx(ctx, "GetEmojisByKeyWord req:%s len(out):%d needAudit:%v", req.String(), len(out.Emojis), out.GetNeedAuditToCache())
	return out, nil
}

func (s *Server) getSearchEmojiByCache(ctx context.Context, keyWord string, marketId, clientType uint32) []*pb.CommonEmojiInfo {
	var cacheEmojis []*pb.CommonEmojiInfo
	emojiList, err := s.searchKeyMgr.SearchByCache(ctx, keyWord)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEmojisByKeyWord SearchByCache req:%s err:%v", keyWord, err)
		return cacheEmojis
	}
	if len(emojiList) >= config.GetCommonEmojiConfig().GetKeyWordEmojiCacheCnt() {
		cacheEmojis = convertor.ConvertSearchEmojiObsRecordsToPb(marketId, clientType, emojiList)
	}
	
	log.InfoWithCtx(ctx, "GetEmojisByKeyWord cache keyWord:%s cacheNum:%d len(returnEmojis):%d, marketId:%d,clientType:%d",
		keyWord, len(emojiList), len(cacheEmojis), marketId, clientType)
	return cacheEmojis
}

func (s *Server) getSearchEmojiByShineApi(ctx context.Context, keyWord string, marketId, clientType uint32) ([]*pb.CommonEmojiInfo, bool) {
	var apiEmojis []*pb.CommonEmojiInfo
	offset := rand.Intn(10)
	emojiRsp, err := s.emojiCall.GetEmoji(ctx, s.emojiCall.SearchUrl, keyWord, offset, 30)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetHotEmojis GetShineHotEmoji req:%s err:%v", keyWord, err)
		return apiEmojis, false
	}
	md5List := make([]string, 0, len(emojiRsp.Datas))
	if len(emojiRsp.Datas) == 0 {
		log.WarnWithCtx(ctx, "GetEmojisByKeyWord http len not enough, v:%s len:%d, offset:%d", keyWord, len(apiEmojis), offset)
		return apiEmojis, false
	}
	
	for _, data := range emojiRsp.Datas {
		md5List = append(md5List, data.Md5)
	}
	obsInfoMap, err := s.searchKeyMgr.BatGetObsInfosMap(ctx, md5List, uint32(im.EmojiMsg_EMOJI_TYPE_KEYWORD))
	if err != nil {
		log.ErrorWithCtx(ctx, "BatGetObsInfos failed, keyWord:%s, md5List:%v, err:%v", keyWord, md5List, err)
	}
	inObsMd5s := make([]string, 0, len(obsInfoMap))
	for _, data := range emojiRsp.Datas {
		if emoji, ok := obsInfoMap[data.Md5]; ok {
			apiEmojis = append(apiEmojis, convertor.ConvertOneSearchEmojiObsRecordToPb(marketId, clientType, emoji))
			inObsMd5s = append(inObsMd5s, data.Md5)
		} else {
			if pbData := convertor.ConvertOneHttpEmojiToPb(ctx, data); pbData != nil {
				apiEmojis = append(apiEmojis, pbData)
			}
		}
	}
	
	log.InfoWithCtx(ctx, "getSearchEmojiByShineApi http keyWord:%s len(obsRecordMap):%d "+
		" len(returnApiEmojis):%d, offset:%d", keyWord, len(obsInfoMap), len(apiEmojis), offset)
	
	var needAudit bool
	if config.GetCommonEmojiConfig().GetSearchEmojiAuditSwitch() &&
		len(apiEmojis) > config.GetCommonEmojiConfig().GetSearchEmojiAuditThreshold() {
		needAudit = true
		go func() {
			asyncCtx, asyncCancel := context.WithTimeout(metadata.NewContext(ctx), 3*time.Second)
			defer asyncCancel()
			// 处理panic
			if err := recover(); err != nil {
				log.ErrorWithCtx(asyncCtx, "AuditEmojis panic: %s", string(debug.Stack()))
			}
			lowErr := s.searchKeyMgr.BatAddSearchEmojiCache(asyncCtx, keyWord, inObsMd5s...)
			if lowErr != nil {
				log.ErrorWithCtx(asyncCtx, "BatAddSearchEmojiCache failed, keyWord:%s, err:%v", keyWord, lowErr)
			}
			
		}()
	}
	return apiEmojis, needAudit
}

func (s *Server) CopyThirdPartyEmojiToObs(ctx context.Context, req *pb.CopyThirdPartyEmojiToObsReq) (*pb.CopyThirdPartyEmojiToObsResp, error) {
	out := &pb.CopyThirdPartyEmojiToObsResp{}
	emojiMsg := &im.EmojiMsg{}
	
	err := proto.Unmarshal(req.GetEmojiMsg(), emojiMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "CopyThirdPartyEmojiToObs Unmarshal emojiMsg failed req:%s err:%v", req.String(), err)
		return out, err
	}
	md5 := emojiMsg.GetMd5()
	emojiType := emojiMsg.GetEmojiType()
	// 查缓存是否以及转obs了
	isInObs, record := s.searchKeyMgr.IsInObs(ctx, md5, emojiType)
	if isInObs {
		out.IsObs = true
		out.ObsKey = record.Key
		out.Url = record.ObsUrl
		log.InfoWithCtx(ctx, "CopyThirdPartyEmojiToObs already in obs req:%s, out:%s", req.String(), out.String())
		return out, nil
	}
	
	obsRecord := &entity.ObsRecord{
		EmojiId:   emojiMsg.GetId(),
		Md5:       emojiMsg.GetMd5(),
		OriginUrl: emojiMsg.GetUrl(),
		OriginH:   emojiMsg.GetHeight(),
		OriginW:   emojiMsg.GetWidth(),
		ThumbH:    emojiMsg.GetThumbH(),
		ThumbW:    emojiMsg.GetThumbW(),
	}
	key, retUrl, err := s.shineEmojiMgr.CopyToObs(ctx, obsRecord, emojiType)
	if err != nil {
		log.ErrorWithCtx(ctx, "CopyThirdPartyEmojiToObs CopyUrl failed req:%s err:%v", req.String(), err)
		return out, err
	}
	
	out.IsObs = true
	out.ObsKey = key
	out.Url = retUrl
	return out, nil
}
