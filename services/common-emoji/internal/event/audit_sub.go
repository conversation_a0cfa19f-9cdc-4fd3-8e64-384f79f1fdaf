package event

import (
	"context"
	"encoding/json"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/audit"
	context_info "golang.52tt.com/pkg/context-info"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app/im"
	common_emoji "golang.52tt.com/protocol/services/common-emoji"
	"golang.52tt.com/services/common-emoji/internal/mgr"
	"golang.52tt.com/services/common-emoji/internal/mgr/entity"
)

type AuditSubscriber struct {
	shineEmojiMgr *mgr.ShineEmojiMgr
	searchKeyMgr  *mgr.SearchKeyMgr
	
}

func InitAuditSubscriber(ctx context.Context, shineEmojiMgr *mgr.ShineEmojiMgr, searchKeyMgr *mgr.SearchKeyMgr) error {
	sub := &AuditSubscriber{
		shineEmojiMgr: shineEmojiMgr,
		searchKeyMgr:  searchKeyMgr,
	}
	
	watcher, err := audit.NewAuditResultEventWatcher(ctx, audit.APP_ID_TT, "common-emoji")
	if err != nil {
		log.ErrorWithCtx(ctx, "InitAuditSubscriber NewAuditResultEventWatcher err: %v", err)
		return err
	}
	
	watcher.AddEventProcess(ctx, []audit.SceneCodeType{
		audit.SCENE_CODE_EMOJI_IM_RECOMMEND,
	}, sub.handleEvent)
	
	err = watcher.Start()
	if err != nil {
		log.ErrorWithCtx(ctx, "InitAuditResultSubscriber Start watcher err: %v", err)
		return err
	}
	
	return nil
}

func (s *AuditSubscriber) handleEvent(result *audit.AuditResultInfo) error {
	ctx := result.Context
	ctx = context_info.GenReqId(ctx)
	log.InfoWithCtx(ctx, "AuditResultSubscriber handleEvent result: %+v", result)
	
	switch result.SceneCode {
	case audit.SCENE_CODE_EMOJI_IM_RECOMMEND:
		stage := result.Params["stage"]
		if stage == common_emoji.AuditStage_AUDIT_STAGE_PULL_EMOJI.String() {
			return s.handlePullEmojiStage(ctx, result)
		} else {
			return s.handleImStage(ctx, result)
		}
	
	default:
		log.WarnWithCtx(ctx, "AuditResultSubscriber handleEvent invalid scene code %d", result.SceneCode)
		return nil
	}
	
}

func (s *AuditSubscriber) handlePullEmojiStage(ctx context.Context, result *audit.AuditResultInfo) error {
	emojiInfo := &common_emoji.CommonEmojiInfo{}
	jsonStr := result.Params["emoji_info"]
	keyWord := result.Params["key_word"]
	err := json.Unmarshal([]byte(jsonStr), emojiInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "AuditResultSubscriber handlePullEmojiStage result:%v jsonStr:%s json.Unmarshal failed, err:%v",
			result, jsonStr, err)
		return err
	}
	if result.AuditResult == audit.AUDIT_RESULT_REJECT {
		err = s.searchKeyMgr.DelEmojiWithKeyWord(ctx, keyWord, emojiInfo.GetMd5())
		if err != nil {
			log.ErrorWithCtx(ctx, "AuditResultSubscriber handlePullEmojiStage result:%v emojiInfo:%s proto.Unmarshal failed, err:%v",
				result, emojiInfo.String())
		}
		return nil
		// 通过、疑似都当作审核通过
	} else {
		isExist, err := s.searchKeyMgr.IsKeyWordEmojiExist(ctx, keyWord, emojiInfo.GetMd5())
		if err != nil {
			log.ErrorWithCtx(ctx, "AuditResultSubscriber handlePullEmojiStage IsKeyWordEmojiExist failed, err:%v", err)
			return err
		}
		if isExist {
			log.DebugWithCtx(ctx, "AuditResultSubscriber IsKeyWordEmojiExist emojiInfo:%s isExist:%v", emojiInfo.String(), isExist)
			return nil
		}
		// 转存obs再存缓存
		isInObs, _ := s.searchKeyMgr.IsInObs(ctx, emojiInfo.GetMd5(), uint32(im.EmojiMsg_EMOJI_TYPE_KEYWORD))
		if !isInObs {
			obsRecord := &entity.ObsRecord{
				EmojiId:   emojiInfo.GetEmojiId(),
				Md5:       emojiInfo.GetMd5(),
				OriginUrl: emojiInfo.GetOriginEmojiInfo().GetUrl(),
				OriginH:   emojiInfo.GetOriginEmojiInfo().GetHeight(),
				OriginW:   emojiInfo.GetOriginEmojiInfo().GetWidth(),
				ThumbH:    emojiInfo.GetThumbEmojiInfo().GetHeight(),
				ThumbW:    emojiInfo.GetThumbEmojiInfo().GetWidth(),
			}
			_, _, err = s.shineEmojiMgr.CopyToObs(ctx, obsRecord, uint32(im.EmojiMsg_EMOJI_TYPE_KEYWORD))
			if err != nil {
				log.ErrorWithCtx(ctx, "handlePullEmojiStage CopyToObs emojiInfo:%s  err:%v", emojiInfo.String(), err)
				return err
			}
		}
		// 存缓存
		err = s.searchKeyMgr.BatAddSearchEmojiCache(ctx, keyWord, emojiInfo.GetMd5())
		if err != nil {
			log.ErrorWithCtx(ctx, "AuditResultSubscriber handlePullEmojiStage RecordEmojiInfos failed, md5:%s err:%v",
				emojiInfo.GetMd5(), err)
			return err
		}
		
	}
	return nil
}

func (s *AuditSubscriber) handleImStage(ctx context.Context, result *audit.AuditResultInfo) error {
	if result.AuditResult != audit.AUDIT_RESULT_REJECT {
		return nil
	}
	emojiMsg := &im.EmojiMsg{}
	jsonStr := result.Params["emoji_msg"]
	
	err := json.Unmarshal([]byte(jsonStr), emojiMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "AuditResultSubscriber handleEvent result:%v jsonStr:%s json.Unmarshal failed, err:%v",
			result, jsonStr, err)
		return err
	}
	err = s.handleEmojiRecommend(ctx, emojiMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "AuditResultSubscriber handleEvent result:%v emojiMsg:%s proto.Unmarshal failed, err:%v",
			result, emojiMsg.String(), err)
		return err
	}
	return nil
}

func (s *AuditSubscriber) handleEmojiRecommend(ctx context.Context, msg *im.EmojiMsg) error {
	// 删除缓存
	switch msg.GetEmojiType() {
	case uint32(im.EmojiMsg_EMOJI_TYPE_RECOMMEND):
		err := s.shineEmojiMgr.DeleteHotEmoji(ctx, msg.GetMd5())
		if err != nil {
			log.ErrorWithCtx(ctx, "handleEmojiRecommend DeleteHotEmoji emojiMsg:%s err:%v", msg.String(), err)
			return err
		}
	case uint32(im.EmojiMsg_EMOJI_TYPE_KEYWORD):
		err := s.searchKeyMgr.DelEmojiWithKeyWord(ctx, msg.GetKeyword(), msg.GetMd5())
		if err != nil {
			log.ErrorWithCtx(ctx, "handleEmojiRecommend DeleteHotEmoji emojiMsg:%s err:%v", msg.String(), err)
			return err
		}
	default:
	}
	
	return nil
	
}
