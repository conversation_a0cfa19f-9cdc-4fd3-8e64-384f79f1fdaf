package event

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tt-infra/middleware/event"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	context_info "golang.52tt.com/pkg/context-info"
	"golang.52tt.com/protocol/app/im"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_im"
	timelineV2Pb "golang.52tt.com/protocol/services/timeline-v2"
	timelinePb "golang.52tt.com/protocol/services/timelinesvr"
	config "golang.52tt.com/services/common-emoji/internal/config/common_emoji"
	"golang.52tt.com/services/common-emoji/internal/mgr"
	"runtime/debug"
	"time"
)

type ImMsgSubscriber struct {
	sub subscriber.Subscriber

	shineEmojiMgr *mgr.ShineEmojiMgr
	searchKeyMgr  *mgr.SearchKeyMgr
}

func InitImMsgSubscriber(ctx context.Context, factory *event.Factory, shineEmojiMgr *mgr.ShineEmojiMgr,
	searchKeyMgr *mgr.SearchKeyMgr) error {
	sub := &ImMsgSubscriber{
		shineEmojiMgr: shineEmojiMgr,
		searchKeyMgr:  searchKeyMgr,
	}

	// step2: 创建消费者
	kafkaSub, err := factory.NewSubscriber("comm_im_event", nil, subscriber.ProcessorContextFunc(sub.onCommonImEvent))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewSubscriber event.NewSubscriber hello-world-event err(%v)", err)
		return err
	}

	sub.sub = kafkaSub
	return nil
}
func (s *ImMsgSubscriber) onCommonImEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (err error, retry bool) {
	ctx, newCancel := context.WithTimeout(ctx, 3*time.Second)
	ctx = context_info.GenReqId(ctx)
	defer func() {
		defer newCancel()
		if e := recover(); e != nil {
			log.Errorf("errored to onImMsg1V1Event err:%v, stack: %v", e, string(debug.Stack()))
		}
	}()

	if !config.GetCommonEmojiConfig().GetUseCacheSwitch() {
		// 缓存开关关闭，不需要处理im消息
		return nil, false
	}
	ev := &kafka_im.CommImEvent{}
	err = proto.Unmarshal(msg.Value, ev)
	if err != nil {
		log.Errorf("CommImEvent proto.Unmarshal failed, err:%v, msg:%+v", err, msg)
		return err, false
	}
	// 只需要发送者私聊消息
	if ev.GetEventType() != kafka_im.CommImEvent_EVENT_TYPE_1V1_RECV_IM {
		return nil, false
	}
	// 忽略非 timeline 消息
	if ev.GetDataType() != kafka_im.CommImEvent_DATA_TYPE_TIMELINE_MSG {
		return nil, false
	}
	// 反序列化 TimelineMsg
	timelineMsg := &timelineV2Pb.TimelineMsg{}
	err = proto.Unmarshal(ev.GetData(), timelineMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "TimelineMsg proto.Unmarshal failed, err:%v, ev:%+v", err, ev)
		return err, false
	}

	// 反序列化 ImMsg
	imMsg := &timelinePb.ImMsg{}
	err = proto.Unmarshal(timelineMsg.MsgBin, imMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "ImMsg proto.Unmarshal failed, err:%v, timelineMsg:%s", err, timelineMsg.String())
		return err, false
	}
	// 只处理自定义表情消息类型
	if imMsg.GetType() != uint32(im.IM_MSG_TYPE_CUSTOM_EMOTICON_MSG) {
		//log.DebugWithCtx(ctx, "imMsg.GetType() wrong:%d, imMsg:%+s", imMsg.GetType(), imMsg.String())
		return nil, false
	}
	emojiMsg := &im.EmojiMsg{}
	err = proto.Unmarshal(imMsg.GetExt(), emojiMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "ImMsg proto.Unmarshal failed, err:%v, imMsg:%+s", err, imMsg.String())
		return err, false
	}
	//log.DebugWithCtx(ctx, "onCommonImEvent emojiMsg:%s", emojiMsg.String())

	if im.EmojiMsg_EmojiType(emojiMsg.GetEmojiType()) != im.EmojiMsg_EMOJI_TYPE_RECOMMEND &&
		im.EmojiMsg_EmojiType(emojiMsg.GetEmojiType()) != im.EmojiMsg_EMOJI_TYPE_KEYWORD {
		//log.DebugWithCtx(ctx, "emojiMsg.GetEmojiType err:%v, imMsg:%+s", err, imMsg.String())
		return nil, false
	}

	err = s.handleEmojiMsg(ctx, emojiMsg, imMsg.GetFromId(), imMsg.GetToId())
	if err != nil {
		log.ErrorWithCtx(ctx, "handleEmojiMsg emojiMsg:%s err:%v", emojiMsg.String(), err)
		return err, false
	}
	return nil, false
}

func (s *ImMsgSubscriber) Close() {
	err := s.sub.Stop()
	log.Errorf("close ImMsgSubscriber err: %v", err)
}

func (s *ImMsgSubscriber) handleEmojiMsg(ctx context.Context, emojiMsg *im.EmojiMsg, fromId, toId uint32) error {
	var err error
	if len(emojiMsg.GetObsKey()) == 0 || len(emojiMsg.GetMd5()) == 0 {
		log.ErrorWithCtx(ctx, "handleHotEmojiMsg emojiMsg:%s ObsKey or md5 is nil", emojiMsg.String())
		return nil
	}
	switch im.EmojiMsg_EmojiType(emojiMsg.GetEmojiType()) {
	case im.EmojiMsg_EMOJI_TYPE_RECOMMEND:
		err = s.handleHotEmojiMsg(ctx, emojiMsg, fromId, toId)

	//case im.EmojiMsg_EMOJI_TYPE_KEYWORD:
	//	err = s.handleKeyWordEmojiMsg(ctx, emojiMsg, fromId, toId)

	default:
		log.WarnWithCtx(ctx, "handleEmojiMsg unknown emojiMsg: %s", emojiMsg.String())
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "handleHotEmojiMsg emojiMsg:%s err(%v)", emojiMsg.String(), err)
		return err
	}
	return nil
}

func (s *ImMsgSubscriber) handleHotEmojiMsg(ctx context.Context, emojiMsg *im.EmojiMsg, fromId, toId uint32) error {
	curWeedDay := time.Now().Weekday()
	if !config.GetCommonEmojiConfig().NeedCalHotRate(int(curWeedDay)) {
		return nil
	}

	err := s.shineEmojiMgr.HotEmojiStatistic(ctx, emojiMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleHotEmojiMsg HotEmojiStatistic emojiMsg:%s err(%v)", emojiMsg.String(), err)
	}
	log.InfoWithCtx(ctx, "handleHotEmojiMsg fromId:%d toId:%d today is %s emojiMsg:%s statistic finish",
		fromId, toId, time.Now().String(), emojiMsg.String())
	return nil
}

