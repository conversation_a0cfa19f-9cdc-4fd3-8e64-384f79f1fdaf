package timer

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"golang.52tt.com/pkg/timer"
	config "golang.52tt.com/services/common-emoji/internal/config/common_emoji"
	"golang.52tt.com/services/common-emoji/internal/mgr"
	"golang.52tt.com/services/common-emoji/internal/mgr/cache"
	"time"
)

func NewEmojiTimer(shineEmojiMgr *mgr.ShineEmojiMgr, searchKeyMgr *mgr.SearchKeyMgr, cacheCli *cache.RedisDao) *EmojiTimer {
	return &EmojiTimer{
		shineEmojiMgr: shineEmojiMgr,
		searchKeyMgr:  searchKeyMgr,
		cacheCli:      cacheCli,
	}
}

type EmojiTimer struct {
	timerD        *timer.Timer
	cacheCli      *cache.RedisDao
	shineEmojiMgr *mgr.ShineEmojiMgr
	searchKeyMgr  *mgr.SearchKeyMgr
}

func (t *EmojiTimer) Start() error {
	ctx, cancelFunc := context.WithCancel(context.Background())
	defer cancelFunc()
	
	timerD, err := timer.NewTimerD(ctx, "common-emoji", timer.WithV8RedisCmdable(t.cacheCli.GetCli()))
	if err != nil {
		log.Errorf("NewTimerD err:%v", err)
		return err
	}
	t.timerD = timerD
	
	//cornStr := "0 0 1 * * *"
	corStr := config.GetCommonEmojiConfig().GetRefreshHotEmojiPoolCorn()
	err = timerD.AddTask(corStr, "RefreshHotEmojiPool", timer.BuildFromLambda(func(ctx context.Context) {
		t.RefreshHotEmojiPool(ctx)
	}))
	if err != nil {
		log.Errorf("redis timer GetBanGameHallUser AddTask err:%v", err)
		return err
	}
	
	err = timerD.AddTask("@hourly", "CleanExpireHotEmoji", timer.BuildFromLambda(func(ctx context.Context) {
		t.CleanExpireHotEmoji(ctx)
	}))
	if err != nil {
		log.Errorf("redis timer CleanExpireHotEmoji AddTask err:%v", err)
		return err
	}
	timerD.Start()
	return nil
}

func (t *EmojiTimer) Stop() {
	t.timerD.Stop()
}

func (t *EmojiTimer) RefreshHotEmojiPool(ctx context.Context) {
	
	// 前一天是周几
	curTime := time.Now()
	
	lastDay := curTime.AddDate(0, 0, config.GetCommonEmojiConfig().GetRefreshHotEmojiPoolDays())
	lastDayWeekDay := int(lastDay.Weekday())
	if !config.GetCommonEmojiConfig().NeedCalHotRate(lastDayWeekDay) {
		// 不需要统计
		return
	}
	timeStamp := lastDay.Unix()
	_, topNInfos, err := t.shineEmojiMgr.GetTopNHotEmojiInfos(ctx, timeStamp, config.GetCommonEmojiConfig().GetHotEmojiRefreshCount())
	if err != nil {
		log.ErrorWithCtx(ctx, "RefreshHotEmojiPool GetTopNHotEmojiInfos err:%v", err)
		return
	}
	if len(topNInfos) == 0 {
		log.WarnWithCtx(ctx, "RefreshHotEmojiPool GetTopNHotEmojiInfos timeStamp:%d emojis is empty", timeStamp)
		return
	}
	
	// 统计前一天的热门表情
	delInfo, err := t.shineEmojiMgr.RefreshHotEmojiPool(ctx, topNInfos)
	if err != nil {
		log.ErrorWithCtx(ctx, "RefreshHotEmojiPool err:%v", err)
		return
	}
	log.InfoWithCtx(ctx, "RefreshHotEmojiPool curTime:%s refresh finish len(topN):%d topNInfos:%v delNum:%d",
		curTime.String(), len(topNInfos), topNInfos, len(delInfo))
}

func (t *EmojiTimer) CleanExpireHotEmoji(ctx context.Context) {
	err := t.shineEmojiMgr.CleanExpireHotEmoji(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "CleanExpireHotEmoji err:%v", err)
		return
	}
}
