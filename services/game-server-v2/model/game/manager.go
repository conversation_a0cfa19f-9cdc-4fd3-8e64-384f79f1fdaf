package game

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	game_card_cli "golang.52tt.com/clients/game-card"
	tcTab "golang.52tt.com/clients/topic-channel/tab"
	game_card "golang.52tt.com/pkg/game-card"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	gameCardPb "golang.52tt.com/protocol/services/game-card"
	pb "golang.52tt.com/protocol/services/game-server-v2"
	tabPb "golang.52tt.com/protocol/services/topic_channel/tab"
	"golang.52tt.com/services/game-server-v2/dao"
	"golang.52tt.com/services/game-server-v2/entity"
	"strings"
)

const (
	gameIdScene    = "tt"
	newGameIdBegin = 500000
)

type GameManager struct {
	dao         *dao.Dao
	tabClient   tcTab.IClient
	gameCardCli *game_card_cli.ConfClient
}

func NewGameManager(dao *dao.Dao) (*GameManager, error) {
	tabClient, _ := tcTab.NewClient()
	gameCardCli, _ := game_card_cli.NewConfClient()
	s := &GameManager{
		dao:         dao,
		tabClient:   tabClient,
		gameCardCli: gameCardCli,
	}

	return s, nil
}

func (s *GameManager) genGameId(ctx context.Context) (gameId uint32, err error) {
	err = s.dao.TxDo(func(tx *sql.Tx) (bool, error) {
		gameId, err = s.dao.GetGameIdForUpdate(ctx, tx, gameIdScene)
		if err != nil {
			return false, err
		}

		if gameId == 0 { //init,有可能死锁，但是就只执行一次，没啥影响
			gameId = newGameIdBegin
			err = s.dao.InitGameId(ctx, tx, gameId, gameIdScene)
			if err != nil {
				return false, err
			}
		} else {
			gameId += 1
			err = s.dao.SetGameId(ctx, tx, gameId, gameIdScene)
			if err != nil {
				return false, err
			}
		}

		return true, nil
	})

	if err != nil {
		log.Errorf("genGameId failed %v", err)
		return 0, err
	}

	log.Debugf("genGameId success, new gameid %d", gameId)

	return gameId, err
}

func (s *GameManager) checkGameType(gameType uint32) bool {
	if gameType != uint32(pb.GameType_GAME_TYPE_EXTERNAL) &&
		gameType != uint32(pb.GameType_GAME_TYPE_TT_GAME) && gameType != uint32(pb.GameType_GAME_TYPE_PC_OTHER_APPLY) {
		return false
	}

	return true
}

func (s *GameManager) CreateGame(ctx context.Context, gameName string, gameType, gameCardId, tabId uint32) (uint32, error) {
	if len(gameName) == 0 {
		return 0, protocol.NewServerError(status.ErrSys, "游戏名称不能为空")
	}

	if !s.checkGameType(gameType) {
		log.Errorf("checkGameType failed, gameType %d", gameType)
		return 0, errors.New("不支持的游戏类型")
	}

	//游戏名不能出现特殊符号
	if ok := strings.ContainsAny(gameName, ",:®・"); ok {
		return 0, errors.New("游戏名不能包含,:®・等符号")
	}

	if tabId > 0 {
		tabInfo, err := s.tabClient.GetTabById(ctx, tabId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetTabById fail, err:%v, tabId:%d", err, tabId)
			return 0, protocol.NewServerError(status.ErrSys, err.Error())
		}
		if tabInfo.GetUGameId() > 0 {
			gameInfo, err := s.dao.GetGameByGameId(ctx, tabInfo.GetUGameId())
			if err != nil && err != sql.ErrNoRows {
				log.ErrorWithCtx(ctx, "dao.GetGameByGameId fail, err:%s, u_game_id:%d", err.Error(), tabInfo.GetUGameId())
				return 0, errors.New("服务器报错，请稍后重试")
			}
			if gameInfo != nil {
				return 0, fmt.Errorf("该房间主题已被（游戏id:%d）绑定，不可重复绑定", tabInfo.GetUGameId())
			}
			log.WarnWithCtx(ctx, "gameid %d not find", tabInfo.GetUGameId())
		}
	}

	newGameId, err := s.genGameId(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "genGameId failed %v", err)
		return 0, err
	}

	err = s.dao.CreateGame(ctx, gameName, newGameId, gameType, gameCardId)
	if err != nil {
		log.ErrorWithCtx(ctx, "dao.CreateGame failed %v", err)
		if strings.Contains(err.Error(), "Duplicate entry") {
			return 0, errors.New("游戏名不可重复")
		}
		return 0, err
	}

	// 修改玩法表
	if tabId > 0 {
		_, err = s.tabClient.UpdateTabUGameId(ctx, &tabPb.UpdateTabUGameIdReq{
			TabId:   tabId,
			UGameId: newGameId,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "UserLine Err:UpdateTabUGameId fail, err:%s,tabId:%d, uGameId:%d", err.Error(), tabId, newGameId)
			return 0, errors.New("绑定房间主题失败，请重新编辑绑定")
		}
	}

	return newGameId, nil
}

func (s *GameManager) CreateGameWithGameId(ctx context.Context, gameName string, gameId, gameType uint32, gameCardId uint32) error {
	if gameId >= newGameIdBegin {
		log.Errorf("CreateGameWithGameId failed, gameId >= newGameIdBegin")
		return errors.New("gameId >= newGameIdBegin")
	}

	err := s.dao.CreateGame(ctx, gameName, gameId, gameType, gameCardId)
	if err != nil {
		log.Errorf("dao.CreateGame failed %v", err)
		return err
	}

	return nil
}

func (s *GameManager) DeleteGame(ctx context.Context, gameId uint32) error {
	// 查看是否有绑定的房间玩法
	tabInfo, err := s.tabClient.GetTabByUGameId(ctx, &tabPb.GetTabByUGameIdReq{
		UGameId: gameId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTabByUGameId fail, err:%v, uGameId:%d", err, gameId)
		return protocol.NewServerError(status.ErrSys, err.Error())
	}
	log.InfoWithCtx(ctx, "DeleteGame bind tab, uGameId:%d, tabId:%d", gameId, tabInfo.GetTab().GetId())

	if tabInfo.GetTab().GetId() > 0 {
		_, err = s.tabClient.UpdateTabUGameId(ctx, &tabPb.UpdateTabUGameIdReq{
			TabId:   tabInfo.GetTab().GetId(),
			UGameId: 0,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "UserLine Err:UpdateTabUGameId fail, err:%s, tabId:%d, uGameId:%d", err.Error(), tabInfo.GetTab().GetId(), 0)
			return protocol.NewServerError(status.ErrGrpcCancelled, "删除解绑房间主题失败，请重试")
		}
	}

	daoErr := s.dao.DeleteGame(ctx, gameId)
	if daoErr != nil {
		log.Errorf("dao.DeleteGame fail, err:%s, gameId:%d", daoErr.Error(), gameId)
		return daoErr
	}

	return nil
}

func (s *GameManager) ModifyGame(ctx context.Context, gameName string, gameId, gameType uint32, gameCardId, tabId uint32) error {
	if !s.checkGameType(gameType) {
		log.Errorf("checkGameType failed, gameType %d", gameType)
		return errors.New("不支持的游戏类型")
	}

	if tabId > 0 {
		tabInfo, err := s.tabClient.GetTabById(ctx, tabId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetTabById fail, err:%v, tabId:%d", err, tabId)
			return errors.New("服务器报错，请稍后重试")
		}
		if tabInfo.GetUGameId() > 0 && tabInfo.GetUGameId() != gameId {
			gameInfo, err := s.dao.GetGameByGameId(ctx, tabInfo.GetUGameId())
			if err != nil && err != sql.ErrNoRows {
				log.ErrorWithCtx(ctx, "dao.GetGameByGameId fail, err:%s, u_game_id:%d", err.Error(), tabInfo.GetUGameId())
				return errors.New("服务器报错，请稍后重试")
			}
			if gameInfo != nil {
				return fmt.Errorf("该房间主题已被（游戏id:%d）绑定，不可重复绑定", tabInfo.GetUGameId())
			}
			log.WarnWithCtx(ctx, "gameid %d not find", tabInfo.GetUGameId())
		}
	}

	err := s.dao.ModifyGame(ctx, gameId, gameName, gameType, gameCardId)
	if err != nil {
		log.Errorf("dao.ModifyGame fail, err:%s", err.Error())
		if strings.Contains(err.Error(), "Duplicate entry") {
			return errors.New("游戏名重复")
		}
		return err
	}

	// 之前已经有绑定过的，要先解绑之前的
	uTabInfo, err := s.tabClient.GetTabByUGameId(ctx, &tabPb.GetTabByUGameIdReq{
		UGameId: gameId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UserLine Err: GetTabByUGameId fail, err:%v, uGameId:%d", err, gameId)
		return errors.New("服务器报错，请稍后重试")
	}
	if uTabInfo.GetTab().GetId() > 0 {
		_, err = s.tabClient.UpdateTabUGameId(ctx, &tabPb.UpdateTabUGameIdReq{
			TabId:   uTabInfo.GetTab().GetId(),
			UGameId: 0,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "UserLine Err:UpdateTabUGameId fail, err:%s, tabId:%d，uGameId:0 ", err.Error(), uTabInfo.GetTab().GetId())
			return protocol.NewServerError(status.ErrGrpcCancelled, "绑定先前房间主题失败，请重新编辑绑定")
		}
	}

	// 修改绑定新的玩法
	if tabId > 0 {
		_, err = s.tabClient.UpdateTabUGameId(ctx, &tabPb.UpdateTabUGameIdReq{
			TabId:   tabId,
			UGameId: gameId,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "UserLine Err:UpdateTabUGameId fail, err:%s,tabId:%d, uGameId:%d", err.Error(), tabId, gameId)
			return protocol.NewServerError(status.ErrGrpcCancelled, "绑定房间主题失败，请重新编辑绑定")
		}
	}
	return nil
}

// GetGameByMultiFilter 多规则匹配返回
func (s *GameManager) GetGameByMultiFilter(ctx context.Context, req *pb.GetGameByNameReq) (uint32, []*entity.GameInfo, error) {
	countQuery, selectQuery := s.buildQuery(ctx, req)
	if countQuery == "" {
		return 0, nil, nil
	}

	count, err := s.dao.GetGameCountByQuery(ctx, countQuery)
	if err != nil {
		log.Errorf("dao.GetGameCountByName fail, err:%v", err)
		return 0, nil, err
	}

	gameList, err := s.dao.GetGameByQuery(ctx, selectQuery)
	if err != nil {
		log.Errorf("dao.GetGameByQuery fail, err:%s, selectQuery:%s", err.Error(), selectQuery)
		return 0, nil, err
	}

	return count, gameList, nil
}

func (s *GameManager) buildQuery(ctx context.Context, req *pb.GetGameByNameReq) (countQuery, selectQuery string) {
	countQuery = "select count(*) from tt_game_mgr where 1=1 "
	selectQuery = "select * from tt_game_mgr where 1=1 "
	if req.GetUGameId() > 0 {
		countQuery += fmt.Sprintf("AND game_id=%d ", req.GetUGameId())
		selectQuery += fmt.Sprintf("AND game_id=%d ", req.GetUGameId())
	}
	if len(req.GetGameName()) > 0 {
		countQuery += fmt.Sprintf("AND game_name like '%%%s%%' ", req.GetGameName())
		selectQuery += fmt.Sprintf("AND game_name like '%%%s%%' ", req.GetGameName())
	}

	if len(req.GetGameCardName()) > 0 {
		gameCardInfos, err := s.gameCardCli.GetGameCardConfByName(ctx, req.GetGameCardName())
		if err != nil {
			log.ErrorWithCtx(ctx, "buildQuery GetGameCardConfByName fail, err:%s, req:%+v", err.Error(), req)
			return "", ""
		}

		var gameCardIds string
		var categoryGameCardId uint32
		for _, info := range gameCardInfos {
			if info.GetCardScopeType() == gameCardPb.CardScopeType_CARD_SCORE_TYPE_CATEGORY {
				categoryGameCardId = info.GetGameCardId()
			} else {
				gameCardIds += fmt.Sprintf("%d,", info.GetGameCardId())
			}
		}
		if len(gameCardIds) > 0 && categoryGameCardId == 0 {
			gameCardIds = strings.TrimRight(gameCardIds, ",")
			countQuery += fmt.Sprintf("AND game_card_id in (%s) ", gameCardIds)
			selectQuery += fmt.Sprintf("AND game_card_id in (%s) ", gameCardIds)

		} else if len(gameCardIds) == 0 && categoryGameCardId > 0 {
			countQuery += fmt.Sprintf("AND game_card_id > %d ", game_card.MinGameCardId)
			selectQuery += fmt.Sprintf("AND game_card_id > %d ", game_card.MinGameCardId)

		} else if len(gameCardIds) > 0 && categoryGameCardId > 0 {
			gameCardIds = strings.TrimRight(gameCardIds, ",")
			countQuery += fmt.Sprintf("AND (game_card_id in (%s) or game_card_id > %d)", gameCardIds, game_card.MinGameCardId)
			selectQuery += fmt.Sprintf("AND (game_card_id in (%s) or game_card_id > %d)", gameCardIds, game_card.MinGameCardId)

		} else {
			return "", ""
		}
	}

	if len(req.GetTabName()) > 0 {
		tabInfos, err := s.tabClient.GetTabByName(ctx, &tabPb.GetTabByNameReq{
			TabName: req.GetTabName(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "buildQuery GetTabByName fail, err:%s, req:%+v", err.Error(), req)
			return "", ""
		}

		var uGameIds string
		for _, info := range tabInfos.GetTabInfos() {
			if info.GetUGameId() == 0 {
				continue
			}
			uGameIds += fmt.Sprintf("%d,", info.GetUGameId())
		}
		if len(uGameIds) > 0 {
			uGameIds = strings.TrimRight(uGameIds, ",")
			countQuery += fmt.Sprintf("AND game_id in (%s) ", uGameIds)
			selectQuery += fmt.Sprintf("AND game_id in (%s) ", uGameIds)
		} else {
			return "", ""
		}
	}

	limit := req.GetLimit()
	if limit == 0 || limit > 100 {
		limit = 100
	}
	selectQuery += fmt.Sprintf("order by update_time desc limit %d offset %d ;", limit, req.GetOffset())

	log.InfoWithCtx(ctx, "GetGameByMultiFilter countQuery:%s, selectQuery:%s", countQuery, selectQuery)

	return
}

// 游戏名严格匹配
func (s *GameManager) GetGameByNameStrict(ctx context.Context, gameName string) ([]*entity.GameInfo, error) {
	gameList, err := s.dao.GetGameByNameStrict(ctx, gameName)
	if err != nil {
		log.Errorf("dao.GetGameByNameStrict failed %v", err)
		return nil, err
	}
	return gameList, nil
}

func (s *GameManager) GetGameByGameId(ctx context.Context, gameId uint32) (*entity.GameInfo, error) {
	gameInfo, err := s.dao.GetGameByGameId(ctx, gameId)
	if err != nil {
		log.Errorf("dao.GetGameByGameId failed %v", err)
		return nil, err
	}

	if gameInfo == nil {
		log.Errorf("gameid %d not find", gameId)
		return nil, errors.New("没有这个游戏")
	}

	return gameInfo, nil
}

func (s *GameManager) GetGameByGameIds(ctx context.Context, gameIds []uint32) ([]*entity.GameInfo, error) {
	gameInfo, err := s.dao.GetGameByGameIds(ctx, gameIds)
	if err != nil {
		log.Errorf("dao.GetGameByGameId fail, err:%s, gameIds:%v", err.Error(), gameIds)
		return nil, err
	}
	return gameInfo, nil
}

func (s *GameManager) GetALLGameInfos(ctx context.Context) (map[uint32]*entity.GameInfo, error) {
	gameInfo, err := s.dao.GetAllGame(ctx)
	if err != nil {
		log.Errorf("dao.GetAllGame fail, err:%s, gameIds:%v", err.Error())
		return nil, err
	}
	gameMap := make(map[uint32]*entity.GameInfo, len(gameInfo))
	for _, info := range gameInfo {
		gameMap[info.GameId] = info
	}
	return gameMap, nil
}

func (s *GameManager) GetTabsMap(ctx context.Context) (uGameIdTabMap map[uint32]*tabPb.Tab, err error) {
	tabInfos, err := s.tabClient.TabsForTT(ctx, 0, 1000)
	if err != nil {
		return nil, err
	}
	uGameIdTabMap = make(map[uint32]*tabPb.Tab, len(tabInfos.GetTabs()))
	for _, t := range tabInfos.GetTabs() {
		if t.GetUGameId() == 0 {
			continue
		}
		uGameIdTabMap[t.GetUGameId()] = t
	}
	return
}

func (s *GameManager) GetAllGameCardNameMap(ctx context.Context) (map[uint32]string, error) {
	gameCardList, err := s.gameCardCli.GetAllGameCardConf(ctx)
	if err != nil {
		return nil, err
	}
	rsp := make(map[uint32]string, len(gameCardList))
	for _, d := range gameCardList {

		rsp[d.GetGameCardId()] = d.GetGameCardName()
	}
	return rsp, nil
}

func (s *GameManager) GetGameByGameCardId(ctx context.Context, gameCardIds []uint32, categoryGameCardId uint32) (gameList []*entity.GameInfo, err error) {
	gameList = make([]*entity.GameInfo, 0)
	if len(gameCardIds) > 0 {
		gameList, err = s.dao.GetGameByGameCardId(ctx, gameCardIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "dao.GetGameByGameCardId fail, err:%v", err)
			return nil, err
		}
	}

	// 如果有类目的游戏卡要特殊查询处理，当前只会有开黑的，先不管其它类型的
	if categoryGameCardId > 0 {
		categoryGameList, err := s.dao.GetGameByGreaterThanGameCardId(ctx, game_card.MinGameCardId)
		if err != nil {
			log.ErrorWithCtx(ctx, "dao.GetGameByGreaterThanGameCardId fail, err:%v", err)

		} else {
			gameList = append(gameList, categoryGameList...)
		}

	}
	return gameList, nil
}
