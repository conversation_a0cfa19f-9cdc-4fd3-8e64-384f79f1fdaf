package internal

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"
	pkgConfig "golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/demo/echo"
	game_hall "golang.52tt.com/protocol/services/game-hall"
	config "golang.52tt.com/services/game-hall/internal/config/ttconfig/game_hall"
	"golang.52tt.com/services/game-hall/internal/mgr/at_msg_mgr"
	"golang.52tt.com/services/game-hall/internal/mgr/conf_mgr"
	"golang.52tt.com/services/game-hall/internal/mgr/entrance_mgr"
	entrance_entity "golang.52tt.com/services/game-hall/internal/mgr/entrance_mgr/store/entity"
	"golang.52tt.com/services/game-hall/internal/mgr/msg"
	"golang.52tt.com/services/game-hall/internal/mgr/pin_mgr"
	"golang.52tt.com/services/game-hall/internal/mgr/pin_mgr/store/entity"
	"golang.52tt.com/services/game-hall/internal/mgr/team_mgr"
	"golang.52tt.com/services/game-hall/internal/timer"
)

type StartConfig struct {
	MongoConfig *pkgConfig.MongoConfig    `json:"mongo"`
	RedisConfig *redisConnect.RedisConfig `json:"redis_config"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	err := config.InitGameHallConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "init game hall config fail, err: %v", err)
		return nil, err

	}

	mongoClient, err := mongo.NewClient(ctx, cfg.MongoConfig.OptionsForPrimaryPreferred())
	if err != nil {
		log.ErrorWithCtx(ctx, "init mongo fail, err: %v", err)
		return nil, err
	}

	redisClient, err := redisConnect.NewClient(ctx, cfg.RedisConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "init redis fail, err: %v", err)
		return nil, err
	}

	msgMgr := msg.NewMgr(redisClient)

	teamMgr, err := team_mgr.NewTeamMgr(redisClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "init team mgr fail, err: %v", err)
		return nil, err
	}

	atMsgMgr, err := at_msg_mgr.NewAtMsgMgr(redisClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "init at msg mgr fail, err: %v", err)
		return nil, err
	}

	confMgr, err := conf_mgr.NewConfMgr(redisClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "init conf mgr fail, err: %v", err)
		return nil, err
	}

	pinMgr, err := pin_mgr.NewGameHallPinConfMgr(ctx, cfg.MongoConfig.Database, mongoClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "init NewGameHallPinConfMgr fail, err: %v", err)
		return nil, err
	}

	showEntranceSettingMgr, err := entrance_mgr.NewShowEntranceSettingMgr(ctx, cfg.MongoConfig.Database, mongoClient, redisClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "init NewShowEntranceSettingMgr fail, err: %v", err)
		return nil, err
	}

	// NewRedisTimer
	redisTimer := timer.NewRedisTimer(redisClient, teamMgr)
	err = redisTimer.Start()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewRedisTimer.Start err: %v", err)
		return nil, err
	}

	s := &Server{
		msgMgr:                 msgMgr,
		teamMgr:                teamMgr,
		atMsgMgr:               atMsgMgr,
		confMgr:                confMgr,
		pinConfMgr:             pinMgr,
		showEntranceSettingMgr: showEntranceSettingMgr,
	}

	return s, nil
}

type Server struct {
	msgMgr                 msg.IMgr
	teamMgr                team_mgr.ITeamMgr
	atMsgMgr               at_msg_mgr.IAtMsgMgr
	confMgr                conf_mgr.IConfMgr
	pinConfMgr             *pin_mgr.PinConfMgr
	showEntranceSettingMgr *entrance_mgr.ShowEntranceSettingMgr
}

func (s *Server) ShutDown() {

}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) AddTeamAndInviteMsg(ctx context.Context, req *game_hall.AddTeamAndInviteMsgReq) (*game_hall.AddTeamAndInviteMsgResp, error) {
	out := &game_hall.AddTeamAndInviteMsgResp{}

	log.InfoWithCtx(ctx, "AddTeamAndInviteMsg req:%v", req)
	err := s.teamMgr.AddSendTeamAndInviteMsgRecord(ctx, req.GetTabId(), req.GetMsgId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddSendTeamAndInviteMsgRecord fail, err: %v, req:%v", err, req)
		return out, err
	}
	return out, nil
}

func (s *Server) GetTeamAndInviteMsg(ctx context.Context, req *game_hall.GetTeamAndInviteMsgReq) (*game_hall.GetTeamAndInviteMsgResp, error) {
	out := &game_hall.GetTeamAndInviteMsgResp{}

	msgIds, err := s.teamMgr.GetTeamAndInviteMsg(ctx, req.GetTabId(), req.GetMsgId(), req.GetLimit())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTeamAndInviteMsg fail, err: %v, req:%v", err, req)
		return out, err
	}
	out.MsgIds = msgIds
	return out, nil
}

func (s *Server) JoinGameHallTeam(ctx context.Context, req *game_hall.JoinGameHallTeamReq) (*game_hall.JoinGameHallTeamResp, error) {
	out := &game_hall.JoinGameHallTeamResp{}

	log.InfoWithCtx(ctx, "JoinGameHallTeam req:%v", req)
	err := s.teamMgr.JoinGameHallTeam(ctx, req.GetSendUid(), req.GetJoinUid(), req.GetTabId(), req.GetMsgId())
	if err != nil {
		log.ErrorWithCtx(ctx, "JoinGameHallTeam fail, err: %v, req:%v", err, req)
		return out, err
	}
	return out, nil
}

func (s *Server) BatchGetGameHallTeamList(ctx context.Context, req *game_hall.BatchGetGameHallTeamListReq) (*game_hall.BatchGetGameHallTeamListResp, error) {
	out := &game_hall.BatchGetGameHallTeamListResp{}

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || svcInfo.UserID == 0 {
		log.WarnWithCtx(ctx, "BatchGetGameHallTeamList uid invalid, uid:%d", svcInfo.UserID)
		return out, nil
	}

	msgMemMap, err := s.teamMgr.BatchGetGameHallTeamList(ctx, req.GetTabId(), req.GetMsgIds(), svcInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetGameHallTeamList fail, err: %v, req:%v", err, req)
		return out, err
	}

	out.MemInfo = make(map[uint64]*game_hall.GameHallTeamInfo, len(msgMemMap))
	for msgId, info := range msgMemMap {
		out.MemInfo[msgId] = &game_hall.GameHallTeamInfo{
			JoinUid:    info.Uids,
			IsSelfJoin: info.SelfJoin,
			MemCount:   uint32(info.Count),
		}
	}

	return out, nil
}

func (s *Server) GetUserJoinTeamRecord(ctx context.Context, req *game_hall.GetUserJoinTeamRecordReq) (*game_hall.GetUserJoinTeamRecordResp, error) {
	out := &game_hall.GetUserJoinTeamRecordResp{}

	svcInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || svcInfo.UserID == 0 {
		log.WarnWithCtx(ctx, "GetUserJoinTeamRecord uid invalid, uid:%d", svcInfo.UserID)
		return out, nil
	}
	records, err := s.teamMgr.GetUserJoinTeamRecord(ctx, req.GetTabId(), svcInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserJoinTeamRecord fail, err: %v, req:%v", err, req)
		return out, err
	}
	out.MemInfo = make([]*game_hall.UserJoinTeamMemInfo, 0, len(records))
	for _, v := range records {
		out.MemInfo = append(out.MemInfo, &game_hall.UserJoinTeamMemInfo{
			Uid:      v.JoinUid,
			JoinTime: v.JoinTime,
		})
	}

	return out, nil
}

func (s *Server) AddUserAtMsgRecord(ctx context.Context, req *game_hall.AddUserAtMsgRecordReq) (*game_hall.AddUserAtMsgRecordResp, error) {
	out := &game_hall.AddUserAtMsgRecordResp{}
	if len(req.GetUids()) == 0 {
		log.InfoWithCtx(ctx, "AddUserAtMsgRecord invalid len(uids)=0, req:%+v", req)
		return out, nil
	}
	if req.GetTabId() == InvalidZero || req.GetMsgId() == InvalidZero || req.GetSendTime() == InvalidZero {
		log.WarnWithCtx(ctx, "AddUserAtMsgRecord invalid tabId=0 or msgId=0 or sendTime=0, req:%+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	err := s.atMsgMgr.AddUserAtMsgRecord(ctx, req.GetUids(), req.GetTabId(), req.GetMsgId(), req.GetSendTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddUserAtMsgRecord fail, err:%v, req:%+v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "AddUserAtMsgRecord success, req:%+v", req)
	return out, nil
}

func (s *Server) GetUserUnreadAtMsg(ctx context.Context, req *game_hall.GetUserUnreadAtMsgReq) (*game_hall.GetUserUnreadAtMsgResp, error) {
	out := &game_hall.GetUserUnreadAtMsgResp{}
	if req.GetUid() == InvalidZero || req.GetTabId() == InvalidZero {
		log.WarnWithCtx(ctx, "GetUserUnreadAtMsg invalid uid=0 or tabId=0, req:%+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	msgIds, err := s.atMsgMgr.GetUserUnreadAtMsg(ctx, req.GetUid(), req.GetTabId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserUnreadAtMsg fail, err:%v, req:%+v", err, req)
		return out, err
	}
	out.MsgIds = msgIds
	log.InfoWithCtx(ctx, "GetUserUnreadAtMsg success, req:%+v, out:%+v", req, out)
	return out, nil
}

func (s *Server) MarkUserAtMsgRead(ctx context.Context, req *game_hall.MarkUserAtMsgReadReq) (*game_hall.MarkUserAtMsgReadResp, error) {
	out := &game_hall.MarkUserAtMsgReadResp{}
	if len(req.GetMsgIds()) == 0 {
		log.InfoWithCtx(ctx, "MarkUserAtMsgRead invalid len(msgIds)=0, req:%+v", req)
		return out, nil
	}
	if req.GetUid() == InvalidZero || req.GetTabId() == InvalidZero {
		log.WarnWithCtx(ctx, "MarkUserAtMsgRead invalid uid=0 or tabId=0, req:%+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	err := s.atMsgMgr.MarkUserAtMsgRead(ctx, req.GetUid(), req.GetTabId(), req.GetMsgIds())
	if err != nil {
		log.ErrorWithCtx(ctx, "MarkUserAtMsgRead fail, err:%v, req:%+v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "MarkUserAtMsgRead success, req:%+v", req)
	return out, nil
}

func (s *Server) UpdateGameHallNotifyStatus(ctx context.Context, req *game_hall.UpdateGameHallNotifyStatusReq) (*game_hall.UpdateGameHallNotifyStatusResp, error) {
	out := &game_hall.UpdateGameHallNotifyStatusResp{}
	if req.GetUid() == InvalidZero || req.GetTabId() == InvalidZero {
		log.WarnWithCtx(ctx, "UpdateGameHallNotifyStatus invalid uid=0 or tabId=0, req:%+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	err := s.confMgr.UpdateGameHallNotifyStatus(ctx, req.GetUid(), req.GetTabId(), req.GetOpenNotify())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateGameHallNotifyStatus fail, err:%v, req:%+v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "UpdateGameHallNotifyStatus success, req:%+v", req)
	return out, nil
}

func (s *Server) GetGameHallNotifyStatus(ctx context.Context, req *game_hall.GetGameHallNotifyStatusReq) (*game_hall.GetGameHallNotifyStatusResp, error) {
	out := &game_hall.GetGameHallNotifyStatusResp{}
	if req.GetUid() == InvalidZero || req.GetTabId() == InvalidZero {
		log.WarnWithCtx(ctx, "GetGameHallNotifyStatus invalid uid=0 or tabId=0, req:%+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	status, err := s.confMgr.GetGameHallNotifyStatus(ctx, req.GetUid(), req.GetTabId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameHallNotifyStatus fail, err:%v, req:%+v", err, req)
		return out, err
	}
	out.IsOpen = status
	log.InfoWithCtx(ctx, "GetGameHallNotifyStatus success, req:%+v, out:%+v", req, out)
	return out, nil
}

func (s *Server) BatchGetGameHallNotifyStatus(ctx context.Context, req *game_hall.BatchGetGameHallNotifyStatusReq) (*game_hall.BatchGetGameHallNotifyStatusResp, error) {
	out := &game_hall.BatchGetGameHallNotifyStatusResp{}
	if len(req.GetUids()) == 0 {
		log.InfoWithCtx(ctx, "BatchGetGameHallNotifyStatus invalid len(uids)=0, req:%+v", req)
		return out, nil
	}
	if req.GetTabId() == InvalidZero {
		log.WarnWithCtx(ctx, "BatchGetGameHallNotifyStatus invalid tabId=0, req:%+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	statusMap, err := s.confMgr.BatchGetGameHallNotifyStatus(ctx, req.GetUids(), req.GetTabId())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetGameHallNotifyStatus fail, err:%v, req:%+v", err, req)
		return out, err
	}
	out.NotifyStatusMap = statusMap
	log.InfoWithCtx(ctx, "BatchGetGameHallNotifyStatus success, req:%+v, out:%+v", req, out)
	return out, nil
}

func (s *Server) SetShowEntranceSetting(ctx context.Context, req *game_hall.SetShowEntranceSettingReq) (*game_hall.SetShowEntranceSettingResp, error) {
	out := &game_hall.SetShowEntranceSettingResp{}
	log.InfoWithCtx(ctx, "SetShowEntranceSetting req:%+v", req)
	if req.GetUid() == 0 || req.GetEntranceSetting().GetTabId() == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	data := entrance_entity.ConvertPbToDao(req.GetUid(), req.GetEntranceSetting())
	err := s.showEntranceSettingMgr.SetShowEntranceSetting(ctx, data)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetShowEntranceSetting fail, err:%v, req:%+v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "SetShowEntranceSetting success, req:%+v", req)
	return out, nil
}

func (s *Server) GetShowEntranceSetting(ctx context.Context, req *game_hall.GetShowEntranceSettingReq) (*game_hall.GetShowEntranceSettingResp, error) {
	out := &game_hall.GetShowEntranceSettingResp{}
	if req.GetUid() == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	data, err := s.showEntranceSettingMgr.GetShowEntranceSetting(ctx, req.GetUid(), req.GetTabId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetShowEntranceSetting fail, err:%v, req:%+v", err, req)
		return out, err
	}
	out.EntranceSettingList = make([]*game_hall.EntranceSettingItem, 0, len(data))
	for _, v := range data {
		out.EntranceSettingList = append(out.EntranceSettingList, entrance_entity.ConvertDaoToPb(v))
	}
	return out, nil
}

func (s *Server) BatGetShowEntranceSettingByTabIds(ctx context.Context, req *game_hall.BatGetShowEntranceSettingByTabIdsReq) (*game_hall.BatGetShowEntranceSettingByTabIdsResp, error) {
	out := &game_hall.BatGetShowEntranceSettingByTabIdsResp{}
	if len(req.GetTabIds()) == 0 {
		return out, nil
	}
	data, err := s.showEntranceSettingMgr.BatGetShowEntranceSettingByTabIds(ctx, req.GetTabIds())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetShowEntranceSetting fail, err:%v, req:%+v", err, req)
		return out, err
	}
	out.EntranceSettingMap = make(map[uint32]*game_hall.BatGetShowEntranceSettingByTabIdsResp_Item, len(data))
	for tabId, uids := range data {
		if len(uids) > 10000 {
			log.WarnWithCtx(ctx, "BatGetShowEntranceSettingByTabIds uids length exceeds 10000, tabId:%d, uids:%v", tabId, uids)
		}
		out.EntranceSettingMap[tabId] = &game_hall.BatGetShowEntranceSettingByTabIdsResp_Item{
			Uids: uids,
		}
	}
	return out, nil
}

func (s *Server) AddGameHallPinConf(ctx context.Context, req *game_hall.AddGameHallPinConfReq) (*game_hall.AddGameHallPinConfResp, error) {
	out := &game_hall.AddGameHallPinConfResp{}
	if (len(req.GetPinConfItem().GetTabIds()) == 0 && !req.GetPinConfItem().GetIsSelectAllTab()) || req.GetPinConfItem().GetStartTime() >= req.GetPinConfItem().GetEndTime() ||
		(req.GetPinConfItem().GetImg() == "" && req.GetPinConfItem().GetContent() == "") {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	data, err := pin_entity.ConvertPbToDao(req.GetPinConfItem(), pin_entity.SourceAdd)
	if err != nil {
		log.ErrorWithCtx(ctx, "ConvertPbToDao error: %v, req:%+v", err, req)
		return out, protocol.NewExactServerError(err, status.ErrRequestParamInvalid)
	}
	err = s.pinConfMgr.AddGameHallPinConf(ctx, data)
	if err != nil {
		return out, err
	}
	return out, nil
}

func (s *Server) UpdateGameHallPinConf(ctx context.Context, req *game_hall.UpdateGameHallPinConfReq) (*game_hall.UpdateGameHallPinConfResp, error) {
	out := &game_hall.UpdateGameHallPinConfResp{}
	if req.GetPinConfItem().GetId() == "" || (len(req.GetPinConfItem().GetTabIds()) == 0 && !req.GetPinConfItem().GetIsSelectAllTab()) ||
		req.GetPinConfItem().GetStartTime() >= req.GetPinConfItem().GetEndTime() && (req.GetPinConfItem().GetImg() == "" && req.GetPinConfItem().GetContent() == "") {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	data, err := pin_entity.ConvertPbToDao(req.GetPinConfItem(), pin_entity.SourceUpdate)
	if err != nil {
		log.ErrorWithCtx(ctx, "ConvertPbToDao error: %v, req:%+v", err, req)
		return out, protocol.NewExactServerError(err, status.ErrRequestParamInvalid)
	}
	err = s.pinConfMgr.UpdateGameHallPinConf(ctx, data)
	if err != nil {
		return out, err
	}
	return out, nil
}

func (s *Server) GetPinConfByAdmin(ctx context.Context, req *game_hall.GetPinConfByAdminReq) (*game_hall.GetPinConfByAdminResp, error) {
	out := &game_hall.GetPinConfByAdminResp{}
	total, data, err := s.pinConfMgr.GetPinConfByAdmin(ctx, req.GetTabId(), req.GetStatus(), req.GetStartTime(), req.GetEndTime(), req.GetPage(), req.GetPageSize())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPinConfByAdmin error: %v, req:%+v", err, req)
		return out, err
	}
	out.TotalCount = total
	out.PinConfList = make([]*game_hall.GameHallPinConfItem, 0, len(data))
	for _, v := range data {
		out.PinConfList = append(out.PinConfList, pin_entity.ConvertDaoToPb(v))
	}
	return out, nil
}

func (s *Server) DelGameHallPinConf(ctx context.Context, req *game_hall.DelGameHallPinConfReq) (*game_hall.DelGameHallPinConfResp, error) {
	out := &game_hall.DelGameHallPinConfResp{}
	if req.GetId() == "" {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	err := s.pinConfMgr.DelGameHallPinConf(ctx, req.GetId())
	if err != nil {
		return out, err
	}
	return out, nil
}

func (s *Server) GetActivePinConfByTabId(ctx context.Context, req *game_hall.GetActivePinConfByTabIdReq) (*game_hall.GetActivePinConfByTabIdResp, error) {
	out := &game_hall.GetActivePinConfByTabIdResp{}
	data, err := s.pinConfMgr.GetActivePinConfByTabId(ctx, req.GetTabId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetActivePinConfByTabId error: %v, req:%+v", err, req)
		return out, err
	}
	out.PinConfList = make([]*game_hall.GameHallPinConfItem, 0, len(data))
	for _, v := range data {
		out.PinConfList = append(out.PinConfList, pin_entity.ConvertDaoToPb(v))
	}
	return out, nil
}
