{"server.grpcListen": ":80", "server.adminListen": ":8078", "redis_config": {"host": "redis-test-tc-bj-tt-go-05.database.svc.cluster.local", "port": 6379, "protocol": "tcp", "ping_interval": 300, "database": 16}, "mongo": {"addrs": "game-user-rate-mongo-01.external.se.cluster.local:27017,game-user-rate-mongo-02.external.se.cluster.local:27017", "database": "game_hall", "user_name": "game_hall_rw", "password": "qtqDquwVY*o9pM5", "max_pool_size": 10, "min_pool_size": 2}}