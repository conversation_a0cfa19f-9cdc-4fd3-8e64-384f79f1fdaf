package internal

import (
	"context"

	"golang.52tt.com/services/game-ugc-middle/internal/attachment_mgr"
	"golang.52tt.com/services/game-ugc-middle/internal/client"

	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/device_id"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/demo/echo"
	pb "golang.52tt.com/protocol/services/game-ugc-middle"
	"golang.52tt.com/services/game-ugc-middle/internal/post_mgr"
)

type StartConfig struct {
	// from config file
	DefaultObsPrefix string `json:"default_obs_prefix"`
	VideoTrans       string `json:"video_trans"`
	OpenBylinkTrack  bool   `json:"open_bylink_track"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	client.InitClients(ctx)

	attachmentMgr := attachment_mgr.NewAttachmentMgr(ctx, cfg.DefaultObsPrefix, cfg.VideoTrans, cfg.OpenBylinkTrack)
	s := &Server{
		attachmentMgr: attachmentMgr,
	}

	return s, nil
}

type Server struct {
	attachmentMgr *attachment_mgr.AttachmentMgr
}

func (s *Server) ShutDown() {

}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

// PostPost 发布帖子
func (s *Server) PostPost(ctx context.Context, req *pb.PostPostRequest) (*pb.PostPostResponse, error) {
	resp := new(pb.PostPostResponse)
	mergeServiceInfo(ctx, req.GetBaseReq())

	svcInfo := metainfo.GetServiceInfo(ctx)
	log.InfoWithCtx(ctx, "PostPost svcInfo(%+v) req: %+v", svcInfo, req)

	if svcInfo.UserID() == 0 {
		log.WarnWithCtx(ctx, "PostPost miss uid")
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	ret, err := post_mgr.DoPost(ctx, req)
	if err != nil {
		log.WarnWithCtx(ctx, "PostPost DoPost req(%+v) err:%v", req, err)
		return resp, err
	}

	resp.PostId = ret.GetPostId()
	resp.ImageToken = ret.GetImageUploadToken()
	resp.VideoToken = ret.GetVideoUploadToken()
	resp.AudioToken = ret.GetAudioUploadToken()
	resp.ImageKeys = ret.GetAttachmentImageKeys()
	resp.VideoKeys = ret.GetAttachmentVideoKeys()
	resp.AudioKeys = ret.GetAttachmentAudioKeys()

	log.InfoWithCtx(ctx, "PostPost req(%+v) resp(%+v)", req, resp)
	return resp, nil
}

func (s *Server) MarkPostAttachmentUploaded(ctx context.Context, req *pb.MarkPostAttachmentUploadedRequest) (*pb.MarkPostAttachmentUploadedResponse, error) {
	resp := new(pb.MarkPostAttachmentUploadedResponse)

	if req.GetUid() == 0 {
		log.WarnWithCtx(ctx, "MarkPostAttachmentUploaded miss uid")
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	err := s.attachmentMgr.MarkPostAttachmentUploaded(ctx, req)
	if err != nil {
		log.WarnWithCtx(ctx, "MarkPostAttachmentUploaded err:%v, req(%+v)", err, req)
		return resp, err
	}

	log.InfoWithCtx(ctx, "MarkPostAttachmentUploaded success, req(%+v)", req)
	return resp, nil
}

func mergeServiceInfo(ctx context.Context, req *pb.BaseRequest) {
	writer, ok := metainfo.GetServiceInfo(ctx).(metainfo.ServiceInfoWriter)
	if ok {
		if req.GetDeviceId() != "" {
			writer.SetDeviceID(device_id.ParseStringDeviceId(req.GetDeviceId()))
		}
	}
}
