package channel

import (
	"context"
	"errors"
	"fmt"
	userline_common_api "golang.52tt.com/protocol/services/userline-common-api"
	"math"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"golang.52tt.com/pkg/bylink"

	"golang.52tt.com/services/topic-channel-logic-v2/internal/module/template"

	channel_ktv "golang.52tt.com/protocol/services/channel-ktv"
	channelListeningPB "golang.52tt.com/protocol/services/channellistening"
	music_song_ktv_pb "golang.52tt.com/protocol/services/rcmd/music_channel"

	user_music_rank "golang.52tt.com/protocol/services/user-music-rank"

	singaroundPB "golang.52tt.com/protocol/services/singaround"

	channelMicPb "golang.52tt.com/protocol/services/channelmicsvr"

	"golang.52tt.com/clients/account"
	cppChannel "golang.52tt.com/protocol/services/channelsvr"

	channelPB "golang.52tt.com/protocol/services/topic_channel/channel"

	game_card "golang.52tt.com/protocol/services/game-card"

	"golang.52tt.com/protocol/services/rcmd/music_channel"

	"golang.52tt.com/services/topic-channel-logic-v2/internal/module/supervision"

	"golang.52tt.com/pkg/foundation/utils"
	genPB "golang.52tt.com/protocol/services/topic_channel/recommendation_gen"
	tabPB "golang.52tt.com/protocol/services/topic_channel/tab"

	"golang.52tt.com/services/topic-channel-logic-v2/internal/confz"

	"golang.52tt.com/pkg/log"
	gaChannelPB "golang.52tt.com/protocol/app/channel"
	gameradarPB "golang.52tt.com/protocol/services/gameradar"
	"golang.52tt.com/services/topic-channel-logic-v2/internal/downstream"

	"golang.52tt.com/pkg/protocol"

	"golang.52tt.com/pkg/protocol/grpc"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	hobbypb "golang.52tt.com/protocol/services/hobby-channel"
)

const (
	InfElemID  uint32 = math.MaxUint32
	KuoLieChat        = "扩列聊天"
	reqCount          = 1
)

func DistributionTopicChannel(ctx context.Context, serviceInfo *grpc.ServiceInfo,
	in *topic_channel.DistributionTopicChannelReq) (friendUid, relationUid, channelId uint32, metaId string, err error) {

	if isMicNewVersion(in.GetBaseReq().GetMarketId(), serviceInfo.ClientVersion) {
		return distributionTopicChannelForMic(ctx, serviceInfo.UserID, in.GetBaseReq().GetMarketId())
	} else {
		if serviceInfo.ClientVersion < protocol.FormatClientVersion(6, 0, 0) {
			channelId, err = distributionTopicChannel(
				ctx,
				serviceInfo.UserID,
				in.GetDistributeSource(),
				in.GetTabId(),
				in.GetBlockOption(),
				uint32(serviceInfo.ClientType),
				serviceInfo.ClientVersion,
				serviceInfo.MarketID,
				in.GetChannelPackageId(),
			)
			return
		} else {
			channelId, err = newDistributionTopicChannel(
				ctx,
				serviceInfo.UserID,
				uint32(serviceInfo.ClientType),
				serviceInfo.ClientVersion,
				serviceInfo.MarketID,
				in.GetChannelPackageId(),
			)
			return
		}
	}
}

func isMicNewVersion(marketId uint32, clientVersion uint32) bool {
	return marketId == uint32(protocol.MIC_MarketID) && clientVersion >= protocol.FormatClientVersion(6, 6, 0)
}

func GetChannelPopUpInfo(ctx context.Context, serviceInfo *grpc.ServiceInfo, channelId uint32, friendUid,
	relationUid uint32, marketId uint32) (*topic_channel.TopicChannelItemV3, error) {

	var err error
	userId := serviceInfo.UserID
	resp, err := downstream.TCChannelClient.GetChannelByIds(ctx, &channelPB.GetChannelByIdsReq{Ids: []uint32{channelId}})
	if err != nil || resp.Info == nil {
		if err != nil {
			log.ErrorWithCtx(ctx, "DistributionTopicChannel GetChannelByIds userId(%v) ids(%d) err(%v)\n", userId, channelId, err)
		} else {
			log.WarnWithCtx(ctx, "DistributionTopicChannel GetChannelByIds userId(%v) ids(%d) get no resp\n", userId, channelId)
		}
		return nil, err
	}

	tcChannelInfo := resp.Info[0]

	cinfo, err := downstream.ChannelClient.GetChannelSimpleInfo(ctx, userId, tcChannelInfo.Id)
	if err != nil {
		log.ErrorWithCtx(ctx, "DistributionTopicChannel GetChannelSimpleInfo userId(%v) tcChannelInfo(%v) err(%v)\n", userId, tcChannelInfo, err)
		return nil, err
	}
	punishUser, err := getPunishUsers(ctx, []uint32{cinfo.GetBindId()})
	if err != nil {
		log.ErrorWithCtx(ctx, "DistributionTopicChannel getPunishUsers userId(%v) tcChannelInfo(%v) err(%v)\n", userId, tcChannelInfo, err)
		return nil, err
	}
	if punishUser[cinfo.GetBindId()] {
		log.InfoWithCtx(ctx, "DistributionTopicChannel userId(%v) get a punishUser(%v)\n", userId, cinfo.GetBindId())
		return nil, nil
	}

	log.DebugWithCtx(ctx, "DistributionTopicChannel hit tab check userId(%d) channel(%d)\n",
		userId, channelId)

	var tabs []*tabPB.Tab
	var userIds []uint32
	info := resp.Info[0]
	tabs = append(tabs, &tabPB.Tab{Id: info.TabId})
	userIds = append(userIds, cinfo.GetBindId())

	var tabData = map[uint32]*tabPB.Tab{}
	//var tabBlockData = map[uint32]*tabPB.BlocksResp{}
	tabResp, err := downstream.TCTabClient.FiniteTabs(ctx, &tabPB.FiniteTabsReq{Tabs: tabs})
	if err != nil {
		log.ErrorWithCtx(ctx, "DistributionTopicChannel FiniteTabs by tabIds(%v) err(%v)\n", tabs, err)
		return nil, err
	}
	if len(tabResp.GetTabs()) == 0 {
		log.WarnWithCtx(ctx, "DistributionTopicChannel FiniteTabs by tabIds(%v) tabs nil", tabs)
		return nil, nil
	}

	for _, tabInfo := range tabResp.GetTabs() {
		tabData[tabInfo.Id] = tabInfo
	}

	blockResp, err := downstream.TCTabClient.BatchGetBlocks(ctx, &tabPB.BatchGetBlocksReq{TabId: []uint32{info.TabId}})
	if err != nil {
		log.ErrorWithCtx(ctx, "DistributionTopicChannel BatchGetBlocks by tabIds(%v) err(%v)\n", info.TabId, err)
		return nil, err
	}
	tabBlockData := blockResp.GetData()

	userIds = utils.TrimRepeatUint32Value(userIds)

	userMap, err := downstream.AccountClient.GetUsersMap(ctx, userIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "DistributionTopicChannel GetUsersMap by userIds(%v) err(%v)\n", userIds, err)
		return nil, err
	}

	getItemReq := &getDistributionTopicChannelItemInfoReq{
		userId:            userId,
		tcChannelInfo:     tcChannelInfo,
		channelSimpleInfo: cinfo,
		tabData:           tabData,
		tabBlockData:      tabBlockData,
		userMap:           userMap,
		clientType:        serviceInfo.ClientType,
	}

	item, err := getDistributionTopicChannelItemInfo(ctx, getItemReq, serviceInfo, friendUid, relationUid, marketId)

	if err != nil {
		log.ErrorWithCtx(ctx, "DistributionTopicChannel getDistributionTopicChannelItemInfo by userId(%v) err(%v)\n", userId, err)
		return nil, err
	}
	if item == nil {
		log.WarnWithCtx(ctx, "DistributionTopicChannel getDistributionTopicChannelItemInfo by userId(%v) item nil", userId)
		return nil, nil
	}

	return item, nil
}

type getDistributionTopicChannelItemInfoReq struct {
	userId            uint32
	tcChannelInfo     *channelPB.ChannelInfo
	channelSimpleInfo *cppChannel.ChannelSimpleInfo
	tabData           map[uint32]*tabPB.Tab
	tabBlockData      map[uint32]*tabPB.BlocksResp
	userMap           map[uint32]*account.User
	clientType        uint16
}

func getDistributionTopicChannelItemInfo(ctx context.Context, req *getDistributionTopicChannelItemInfoReq, serviceInfo *grpc.ServiceInfo, friendUid, relationUid uint32, marketId uint32) (item *topic_channel.TopicChannelItemV3, err error) {
	userId := req.userId
	tcChannelInfo := req.tcChannelInfo
	channelSimpleInfo := req.channelSimpleInfo
	tabData := req.tabData
	tabBlockData := req.tabBlockData
	userMap := req.userMap

	id := channelSimpleInfo.GetChannelId()
	item = &topic_channel.TopicChannelItemV3{
		ChannelInfo:   &topic_channel.TopicChannelInfoV2{ChannelId: id},
		TabInfo:       &topic_channel.TopicChannelTab{},
		OwnerUserInfo: &topic_channel.ChannelOwnerInfo{},
		TraceInfo:     &topic_channel.RecommendationTraceInfo{},
		Type:          topic_channel.TopicChannelItemType_TopicChannelItemTypeNormal,
	}

	if shouldSkipChannel(ctx, id, channelSimpleInfo) {
		//channel info拿不到或锁房了，不返回
		return nil, nil
	} else {
		item.ChannelInfo = convertChannelSimpleInfoToTopicChannelInfoV2(channelSimpleInfo, item.ChannelInfo)
	}

	micResp, err := downstream.ChannelmicClient.GetMicrList(ctx, id, userId)
	if err != nil {
		log.ErrorWithCtx(ctx, "getDistributionTopicChannelItemInfo GetMicList Fail channelId(%d) , userId(%d)\n", id, userId)
		return nil, err
	}

	if tcChannelInfo == nil {
		return nil, nil
	}

	//这个房间是主题房
	log.DebugWithCtx(ctx, "getDistributionTopicChannelItemInfo listRecommendationInfo channelId(%d) is topicChannel \n", id)
	item.TabInfo = &topic_channel.TopicChannelTab{Id: tcChannelInfo.TabId}

	item.TabInfo = convertTabPBToTabInfo(tabData[tcChannelInfo.TabId], item.TabInfo)

	item.OwnerUserInfo = convertAccountToOwner(userMap[channelSimpleInfo.GetBindId()], item.OwnerUserInfo)
	//主题房的是房间人数, 5.1.0的版本改的

	//v5.5.0将房间人数挪动到数组中
	item.ChannelInfo.TopicChannelType = topic_channel.TopicChannelInfoV2Type_UGC
	//扩列聊天用的
	var specialDesc string
	if tabBlockData[tcChannelInfo.TabId] != nil && len(tabBlockData[tcChannelInfo.TabId].Blocks) > 0 {
		//v5.5.0改版列表展示信息
		item.ChannelInfo.HeadDesc, item.ChannelInfo.SecondDesc, item.ChannelInfo.ThirdDesc, specialDesc, _, _ = getChannelDescV2(ctx, tcChannelInfo.BlockOptions, tabBlockData[tcChannelInfo.TabId].Blocks)
	}

	//开黑小队被删除只能通过配置文件确定
	if tcChannelInfo.TabId == confz.TopicChannelLogicConfig.LoadConfig().GloryTabId {
		//王者荣耀
		item.ChannelTeamInfo = forgeTeamInfo(ctx, micResp.AllMicList, item.OwnerUserInfo.Account, 5)

		item.ChannelTeamInfo.UiType = 1
		item.WelcomeTittle = "我的王者房间缺个你，推荐上车！"
	} else if tcChannelInfo.TabId == confz.TopicChannelLogicConfig.LoadConfig().PeaceTabId {
		//吃鸡
		item.ChannelTeamInfo = forgeTeamInfo(ctx, micResp.AllMicList, item.OwnerUserInfo.Account, 4)

		item.ChannelTeamInfo.UiType = 2
		item.WelcomeTittle = "我的吃鸡房间缺个你，推荐上车！\n"
	} else if tcChannelInfo.TabId == confz.TopicChannelLogicConfig.LoadConfig().ChatTabId {
		//扩列聊天
		if specialDesc == "" {
			specialDesc = "聊天"
		}
		item.WelcomeTittle = fmt.Sprintf("他们正在%s，推荐加入吧~", specialDesc)
		item.ChannelInfo.ThirdDesc = specialDesc

		item.ChannelTeamInfo = forgeTeamInfo(ctx, micResp.AllMicList, item.OwnerUserInfo.Account, -1)

		item.ChannelInfo.FourthDesc = append(item.ChannelInfo.FourthDesc, strconv.Itoa(int(tcChannelInfo.TotalCount))+"人")
	} else {
		//其他主题房
		item.WelcomeTittle = "我的房间缺个你，推荐加入吧~"
		item.ChannelTeamInfo = forgeTeamInfo(ctx, micResp.AllMicList, item.OwnerUserInfo.Account, -1)
	}

	if isMicNewVersion(marketId, serviceInfo.ClientVersion) &&
		(tcChannelInfo.GetTabId() == confz.TopicChannelLogicConfig.LoadConfig().SingARoundTabId ||
			tcChannelInfo.GetTabId() == confz.TopicChannelLogicConfig.LoadConfig().KTVTabId ||
			tcChannelInfo.GetTabId() == confz.TopicChannelLogicConfig.LoadConfig().ListeningTabId ||
			tcChannelInfo.GetTabId() == confz.TopicChannelLogicConfig.LoadConfig().OldListeningTabId ||
			tcChannelInfo.GetTabId() == confz.TopicChannelLogicConfig.LoadConfig().RapTabId ||
			tcChannelInfo.GetTabId() == confz.TopicChannelLogicConfig.LoadConfig().OldRapTabId) {
		item.WelcomeTittle = channelSimpleInfo.GetName()
		item.Type = topic_channel.TopicChannelItemType_TopicChannelItemTypeMusic
		if relationUid != 0 {
			item.MemberTag = confz.TopicChannelLogicConfig.LoadConfig().RelationTag
		} else if friendUid != 0 {
			item.MemberTag = confz.TopicChannelLogicConfig.LoadConfig().FriendTag
		}
		item.Member, err = getMemberInfo(ctx, serviceInfo.UserID, friendUid, relationUid, micResp, channelSimpleInfo.GetCreaterUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "getMemberInfo err: %v", err)
			return
		}

		switch tcChannelInfo.GetTabId() {
		case confz.TopicChannelLogicConfig.LoadConfig().SingARoundTabId:
			item.TabInfo.RadarDistributionImage = confz.TopicChannelLogicConfig.LoadConfig().SingARoundBg
			resp, err := downstream.SingClient.GetSingingGameRoundInfo(ctx, &singaroundPB.GetSingingGameRoundInfoReq{ChannelIds: []uint32{id}})
			if err != nil {
				log.ErrorWithCtx(ctx, "GetSingingGameRoundInfo err: %v", err)
				return nil, err
			}

			if len(resp.GetRoundInfos()) == 1 {
				if resp.GetRoundInfos()[0].GetStage() != singaroundPB.SingingGameRoundStage_SingingGameRoundStageUndefined {
					item.WelcomeContent = fmt.Sprintf("你会唱《%s》吗？快来帮我抢", resp.GetRoundInfos()[0].GetSongName())
				} else {
					item.WelcomeContent = "马上开局，赶快上车"
				}
			}
		case confz.TopicChannelLogicConfig.LoadConfig().KTVTabId:
			item.TabInfo.RadarDistributionImage = confz.TopicChannelLogicConfig.LoadConfig().KTVBg
			resp, err := downstream.KTVClient.BatchGetChannelKTVInfo(ctx, &channel_ktv.BatchGetChannelKTVInfoReq{
				ChannelIdList: []uint32{id},
				ClientType:    uint32(serviceInfo.ClientType),
				ClientVersion: serviceInfo.ClientVersion,
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "BatchGetChannelKTVInfo err: %v", err)
				return nil, err
			}

			var songName string
			if len(resp.GetInfoList()) == 1 {
				songName = resp.GetInfoList()[0].GetSingName()
			}
			if songName != "" {
				item.WelcomeContent = fmt.Sprintf("我们正在合唱《%s》，你来吗？", songName)
			} else {
				item.WelcomeContent = "我们正在点歌，你来吗？"
			}

			var uids []uint32
			for _, elem := range micResp.GetAllMicList() {
				if elem.GetMicUid() != 0 {
					uids = append(uids, elem.GetMicUid())
				}
			}

			if len(uids) > 0 {
				resp, err := downstream.UserMusicRankClient.GetMusicRankUserInfo(ctx, &user_music_rank.GetMusicRankUserInfoReq{
					Uids: uids,
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "GetMusicRankUserInfo err: %v", err)
					return nil, err
				}

				var g *user_music_rank.UserGloryInfo
				for _, glory := range resp.GetUserGloryMap() {
					if glory.GetGloryResource().GetLevel() > g.GetGloryResource().GetLevel() ||
						(glory.GetGloryResource().GetLevel() == g.GetGloryResource().GetLevel() &&
							glory.GetGloryResource().GetGloryRank() > g.GetGloryResource().GetGloryRank()) {
						g = glory
					}
				}

				if g.GetGloryResource().GetGloryName() != "" {
					if songName != "" {
						item.WelcomeContent = fmt.Sprintf("%s正在演唱《%s》，真的好听", g.GetGloryResource().GetGloryName(), songName)
					} else {
						item.WelcomeContent = fmt.Sprintf("%s正在选歌，快来围观", g.GetGloryResource().GetGloryName())
					}
				}
			}
		case confz.TopicChannelLogicConfig.LoadConfig().ListeningTabId, confz.TopicChannelLogicConfig.LoadConfig().OldListeningTabId:
			item.TabInfo.RadarDistributionImage = confz.TopicChannelLogicConfig.LoadConfig().ListeningBg
			var err error
			resp, err := downstream.ChannelListeningCli.GetChannelListeningSimpleInfo(ctx, channelListeningPB.GetChannelListeningSimpleInfoReq{ChannelId: id})
			if err != nil {
				log.ErrorWithCtx(ctx, "GetChannelListeningSimpleInfo err: %v", err)
				return nil, err
			}

			rsp, err := downstream.HobbyChannelClient.BatchChannelOwnerDuration(ctx, &hobbypb.BatchChannelOwnerDurationReq{ChannelIds: []uint32{id}})
			if err != nil {
				log.ErrorWithCtx(ctx, "BatchChannelOwnerDuration err: %v", err)
				return nil, err
			}
			var duration int64
			if rsp.GetChannelOwnerDurationMap()[id] != 0 {
				duration = rsp.GetChannelOwnerDurationMap()[id] / 60
				if rsp.GetChannelOwnerDurationMap()[id]%60 != 0 {
					duration++
				}
			}

			num, err := downstream.ChannelOlStat.GetChannelMemberSize(ctx, userId, id)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetChannelMemberSize err: %v", err)
				return nil, err
			}

			item.WelcomeContent = fmt.Sprintf("我和%d人一起%s了%dmin，一起来吗", num, resp.GetTheme().GetName(), duration)
		case confz.TopicChannelLogicConfig.LoadConfig().RapTabId, confz.TopicChannelLogicConfig.LoadConfig().OldRapTabId:
			item.TabInfo.RadarDistributionImage = confz.TopicChannelLogicConfig.LoadConfig().RapBg
			var holdMicCount uint32
			for _, elem := range micResp.GetAllMicList() {
				if elem.GetMicUid() != 0 {
					holdMicCount++
				}
			}

			if holdMicCount > 0 {
				item.WelcomeContent = fmt.Sprintf("%d人正在P麦，太炸了快来Respect", holdMicCount)
			} else {
				item.WelcomeContent = "房内正在说唱，速来Respect"
			}
		}
	}

	return item, nil
}

func getMemberInfo(ctx context.Context, reqUid uint32, friendUid, relationUid uint32, micrListResp *channelMicPb.GetMicrListResp, ownerId uint32) (*topic_channel.TopicChannelItemV3_MemberInfo, error) {
	if relationUid != 0 {
		user, err := downstream.AccountClient.GetUser(ctx, relationUid)
		if err != nil {
			return nil, err
		}

		member := &topic_channel.TopicChannelItemV3_MemberInfo{
			Uid:      relationUid,
			Account:  user.GetUsername(),
			Nickname: user.GetNickname(),
			Sex:      user.GetSex(),
		}

		return member, nil
	} else if friendUid != 0 {
		user, err := downstream.AccountClient.GetUser(ctx, friendUid)
		if err != nil {
			return nil, err
		}

		member := &topic_channel.TopicChannelItemV3_MemberInfo{
			Uid:      friendUid,
			Account:  user.GetUsername(),
			Nickname: user.GetNickname(),
			Sex:      user.GetSex(),
		}

		return member, nil
	}

	var uids []uint32
	for _, elem := range micrListResp.GetAllMicList() {
		if elem.GetMicUid() != 0 {
			uids = append(uids, elem.GetMicUid())
		}
	}

	userMap, err := downstream.AccountClient.GetUsersMap(ctx, append(uids, reqUid, ownerId))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUsersMap err: %v", err)
		return nil, err
	}

	reqUserSex := userMap[reqUid].GetSex()
	for _, uid := range uids {
		if reqUserSex != userMap[uid].GetSex() {
			member := &topic_channel.TopicChannelItemV3_MemberInfo{
				Uid:      uid,
				Account:  userMap[uid].GetUsername(),
				Nickname: userMap[uid].GetNickname(),
				Sex:      userMap[uid].GetSex(),
			}

			return member, nil
		}
	}

	member := &topic_channel.TopicChannelItemV3_MemberInfo{
		Uid:      ownerId,
		Account:  userMap[ownerId].GetUsername(),
		Nickname: userMap[ownerId].GetNickname(),
		Sex:      userMap[ownerId].GetSex(),
	}

	return member, nil
}

func forgeTeamInfo(ctx context.Context, userOnMic []*channelMicPb.MicrSpaceInfo, ownerAccount string, teamLimit int) (channelTeamInfo *topic_channel.TopicChannelItemV3_ChannelTeamInfo) {
	channelTeamInfo = &topic_channel.TopicChannelItemV3_ChannelTeamInfo{}
	uids := make([]uint32, 0)
	if len(userOnMic) == 0 {
		//麦上无人，房主怼上
		tmp := &topic_channel.TopicChannelItemV3_MemberInfo{}
		tmp.Account = ownerAccount
		channelTeamInfo.MemberList = append(channelTeamInfo.MemberList, tmp)
		return channelTeamInfo
	}
	for _, v := range userOnMic {
		if v.MicUid != 0 {
			uids = append(uids, v.MicUid)
		}
	}

	umap, err := downstream.AccountClient.GetUsersMap(ctx, uids)
	if err != nil {
		log.ErrorWithCtx(ctx, "forgeTeamInfo GetUsersMap uids(%v) error(%v)  \n", umap, err)
		return
	}

	tmp := &topic_channel.TopicChannelItemV3_MemberInfo{}
	channelTeamInfo.MemberList = append(channelTeamInfo.MemberList, tmp)
	for _, v := range umap {
		if v.Username == ownerAccount {
			channelTeamInfo.MemberList[0].Uid = v.Uid
			channelTeamInfo.MemberList[0].Account = v.Username
		} else {
			tmp := &topic_channel.TopicChannelItemV3_MemberInfo{}
			tmp.Uid = v.Uid
			tmp.Account = v.Username
			channelTeamInfo.MemberList = append(channelTeamInfo.MemberList, tmp)
		}
	}

	if teamLimit < 0 {
		return channelTeamInfo
	}

	//长度超了要抛弃一部分
	if len(channelTeamInfo.MemberList) > teamLimit {
		channelTeamInfo.MemberList = channelTeamInfo.MemberList[0:teamLimit]
	}

	//短了要补空白
	for i := len(channelTeamInfo.MemberList); i < teamLimit; i++ {
		tmp := &topic_channel.TopicChannelItemV3_MemberInfo{}
		if teamLimit == 4 {
			//吃鸡
			tmp.LocationIcon = confz.TopicChannelLogicConfig.LoadConfig().GameForPeaceTeamIcon
		} else if teamLimit == 5 {
			//王者
			tmp.LocationIcon = confz.TopicChannelLogicConfig.LoadConfig().GloryOfKingsTeamIcon
		}
		channelTeamInfo.MemberList = append(channelTeamInfo.MemberList, tmp)
	}

	return channelTeamInfo
}

// V5.5.0大改版，开黑列表内描述变动巨大，还不让影响旧版逻辑，只好新增个V2
func getChannelDescV2(ctx context.Context, blockOptions []*channelPB.BlockOption, blocks []*tabPB.Block) (headDesc, secondDesc, thirdDesc, specialDesc, teamSize string, locationArray []string) {
	if len(blockOptions) == 0 || len(blocks) == 0 {
		return
	}
	//用户选择的
	var blockIdx = map[uint32]map[uint32]bool{}
	for _, block := range blockOptions {
		if blockIdx[block.BlockId] == nil {
			blockIdx[block.BlockId] = map[uint32]bool{block.ElemId: true}
		} else {
			blockIdx[block.BlockId][block.ElemId] = true
		}
	}
	//系统预设的
	var presetBlockMap = map[uint32]map[uint32]*tabPB.Elem{}
	for _, block := range blocks {
		for _, elem := range block.Elems {
			if presetBlockMap[block.Id] == nil {
				presetBlockMap[block.Id] = map[uint32]*tabPB.Elem{}
			}
			presetBlockMap[block.Id][elem.Id] = elem
		}
	}

	var headTitle, secondTitle, thirdTitle []string
	for _, block := range blocks {
		if len(blockIdx[block.Id]) > 0 {
			var allSelected = true
			if len(blockIdx[block.Id]) == len(presetBlockMap[block.Id]) { //系统预设选项个数与用户勾选个数相同
				for _, elem := range block.Elems { //检查用户勾选的选项是否与预设选项完全相同
					if !blockIdx[block.Id][elem.Id] { //用户未勾选该选项
						allSelected = false
						break
					}
				}
				if allSelected {
					if block.ShowRow == 4 {
						//吃鸡地图特殊处理
						headTitle = append(headTitle, "不限")
					} else if block.ShowRow == 2 {
						secondTitle = append(secondTitle, "不限"+block.Title)
					}
					if block.ShowRow == 5 {
						//分路要全部列出来
						allSelected = false
					}
				}
			} else {
				allSelected = false
			}

			if !allSelected {
				var idx []int
				for i, elem := range block.Elems {
					if blockIdx[block.Id][elem.Id] { //获取当前block下所有选中的elem数组的index（不是elem_id）即这个block都勾选了哪几位elem
						idx = append(idx, i)
					}
				}
				var stringSet [][]string
				var lastIdx = -1
				//地图和分路特殊处理
				if block.ShowRow != 4 && block.ShowRow != 5 {
					for _, i := range idx {
						if lastIdx == -1 { //将第一个elem的tittle存入stringSet的第一位数组，二维数组的x轴上拼入
							stringSet = append(stringSet, []string{block.Elems[i].Title})
						} else if lastIdx+1 == i { //如果该elem与上个存入stringSet的elem是连着的
							if len(stringSet[len(stringSet)-1]) < 2 { //stringSet最后一位的数组长度为0或者1
								stringSet[len(stringSet)-1] = append(stringSet[len(stringSet)-1], block.Elems[i].Title) //二维数组y轴上拼入
							} else {
								stringSet[len(stringSet)-1][1] = block.Elems[i].Title //二维数组y轴上替换第二位
							}
						} else {
							stringSet = append(stringSet, []string{block.Elems[i].Title})
						}
						lastIdx = i
					}
				} else {
					for _, i := range idx {
						stringSet = append(stringSet, []string{block.Elems[i].Title})
					}
				}

				if block.Title == "人数" {
					for _, i := range idx {
						teamSize = block.Elems[i].Title
					}
				}

				var str []string
				for _, slice := range stringSet {
					str = append(str, strings.Join(slice, "-"))
				}
				var title string
				if block.ShowRow == 4 {
					title = strings.Join(str, "、")
				} else {
					title = strings.Join(str, "/")
				}

				switch block.ShowRow {
				case 1:
					//仅扩列聊天用
					if title != "" {
						specialDesc = title
					} else {
						log.WarnWithCtx(ctx, "getChannelDescV2 ShowRow=1 title is null \n")
					}
				case 2:
					if title != "" {
						secondTitle = append(secondTitle, title)
					}
				case 3:
					thirdTitle = append(thirdTitle, title)
				case 4:
					//地图信息
					if title != "" {
						headTitle = append(headTitle, title)
					}
				case 5:
					//分路位置信息
					locationArray = str
				default:
					continue
				}

			}
		}

	}

	headDesc = strings.Join(headTitle, " | ")
	secondDesc = strings.Join(secondTitle, "-")

	var dis, mode string
	for _, v := range thirdTitle {
		if strings.Contains(v, "QQ") {
			dis = "QQ"
		} else if strings.Contains(v, "微信") {
			dis = "微信"
		} else if strings.Contains(v, "国际") {
			dis = "国际"
		} else if strings.Contains(v, "上分") {
			mode = "上分局"
		} else if strings.Contains(v, "娱乐") {
			mode = "娱乐局"
		}
	}
	thirdDesc = dis + mode
	if secondDesc != "" {
		secondDesc = "想找：" + secondDesc
	}

	log.DebugWithCtx(ctx, "getChannelDescV2 headDesc (%v), secondDesc (%v), thirdDesc (%v), specialDesc (%v), teamSize (%v), locationArray (%v)\n", headDesc, secondDesc, thirdDesc, specialDesc, teamSize, locationArray)
	return
}

func convertChannelSimpleInfoToTopicChannelInfoV2(channelSimpleInfo *cppChannel.ChannelSimpleInfo, channelInfo *topic_channel.TopicChannelInfoV2) *topic_channel.TopicChannelInfoV2 {
	channelInfo.Name = channelSimpleInfo.GetName()
	channelInfo.DisplayId = channelSimpleInfo.GetDisplayId()
	channelInfo.AppId = channelSimpleInfo.GetAppId()
	channelInfo.HasPwd = channelSimpleInfo.GetHasPwd()
	channelInfo.ChannelType = channelSimpleInfo.GetChannelType()
	channelInfo.TopicTitle = channelSimpleInfo.GetTopicTitle()
	channelInfo.IconMd5 = channelSimpleInfo.GetIconMd5()
	channelInfo.BindId = channelSimpleInfo.GetBindId()
	return channelInfo
}

func convertTabPBToTabInfo(tab *tabPB.Tab, tabInfo *topic_channel.TopicChannelTab) *topic_channel.TopicChannelTab {
	if tab != nil {
		tabInfo.Name = tab.Name
		tabInfo.ListBackgroundUri = tab.ShowImageUri
		tabInfo.CardMainColor = tab.NewcomerColor
		tabInfo.CardBackgroundUri = tab.NewcomerImageUri
		tabInfo.CardFontBackgroundUri = tab.NewcomerFontBackgroundUri
		tabInfo.CardFontColor = tab.NewcomerFontColor
		tabInfo.RoomLabel = tab.RoomLabel

		//雷达和房间转移相关v5.5.0
		tabInfo.RadarDistributionImage = tab.RadarDistributionImage
		tabInfo.RoomDistributionImage = tab.RoomDistributionImage
		tabInfo.BottomTextColor = tab.BottomTextColor
		tabInfo.WelcomeTextColor = tab.WelcomeTextColor

		tabInfo.FollowLabelImg = tab.FollowLabelImg
		tabInfo.MaskLayer = tab.MaskLayer
	}
	return tabInfo
}

func convertAccountToOwner(accountInfo *account.User, ownerInfo *topic_channel.ChannelOwnerInfo) *topic_channel.ChannelOwnerInfo {
	if accountInfo != nil {
		ownerInfo.Uid = accountInfo.Uid
		ownerInfo.Account = accountInfo.Username
		ownerInfo.Sex = uint32(accountInfo.Sex)
		ownerInfo.Nickname = accountInfo.Nickname
	}
	return ownerInfo
}

func shouldSkipChannel(ctx context.Context, channelId uint32, channelSimpleInfo *cppChannel.ChannelSimpleInfo) bool {
	if channelSimpleInfo == nil {
		log.WarnWithCtx(ctx, "shouldSkipChannel channel(%d) can't get  channel info \n", channelId)

		return true
	} else if channelSimpleInfo.GetHasPwd() {
		//被锁房了就不返回
		log.WarnWithCtx(ctx, "shouldSkipChannel %d HasPwd \n", channelId)

		return true
	} else {
		return false
	}
}

func distributionTopicChannel(
	ctx context.Context,
	userId uint32,
	distributeSource topic_channel.DistributionTopicChannelReq_DistributeSource,
	tabId uint32,
	blockOpts []*topic_channel.BlockOption,
	clientType uint32,
	clientVersion uint32,
	marketId uint32,
	channelPackageId string,
) (uint32, error) {
	var genOption []*genPB.BlockOption
	var channelEnterSource uint32
	var err error
	if distributeSource == topic_channel.DistributionTopicChannelReq_FromRadar {
		//雷达房间下发
		tabId, channelEnterSource, genOption, err = distributionFromRadar(ctx, userId)
		if err != nil {
			if err.Error() == "return nil is ok" {
				log.WarnWithCtx(ctx, "DistributionTopicChannel distributionFromRadar error:%v", err)
				return 0, nil
			}
			log.ErrorWithCtx(ctx, "DistributionTopicChannel distributionFromRadar error:%v", err)
			return 0, err
		}
	} else if distributeSource == topic_channel.DistributionTopicChannelReq_FromRoom {
		//房间转移
		channelEnterSource = uint32(gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_CHANNEL_TRANSFER)
		var shouldShift bool
		genOption, shouldShift = shiftRoom(tabId, blockOpts)
		if !shouldShift {
			return 0, nil
		}
	}

	//房间雷达推荐超时无兜底

	ctx2, err := template.NewContext(ctx, nil)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewContext err: %v", err)
		return 0, err
	}
	//genResp := &genPB.GetRecommendationListResp{}
	getRecommendationListReq := genPB.GetRecommendationListReq{
		Uid: userId, Limit: reqCount, TabId: tabId, BlockOptions: genOption, GetMode: genPB.GetRecommendationListReq_REFRESH,
		ChannelEnterSource: channelEnterSource,
		ClientType:         clientType, ClientVersion: clientVersion, MarketId: marketId,
		Env:              convertEnv(confz.GetEnv()),
		ChannelPackageId: channelPackageId,
		RegulatoryLevel:  genPB.REGULATORY_LEVEL(supervision.GetUserRegulatoryLevel(ctx2)),
	}
	log.DebugWithCtx(ctx, "DistributionTopicChannel GetRecommendationList getRecommendationListReq (%v)\n", utils.ToJson(getRecommendationListReq))

	genResp, err := downstream.GenRecommendationClient.GetRecommendationList(ctx, &getRecommendationListReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "DistributionTopicChannel gen GetRecommendationList userId(%v) tabId(%v) err(%v)\n", userId, tabId, err)
		return 0, err
	}

	if len(genResp.ChannelId) < 1 {
		log.WarnWithCtx(ctx, "DistributionTopicChannel gen GetRecommendationList don't have enough channel userId(%v) tabId(%v) genResp(%v)\n", userId, tabId, genResp)
		return 0, err
	}

	log.DebugWithCtx(ctx, "DistributionTopicChannel GetRecommendationList getRecommendationListResp (%v)\n", utils.ToJson(genResp))

	return genResp.GetChannelId()[0], nil
}

func newDistributionTopicChannel(
	ctx context.Context,
	userId uint32,
	clientType uint32,
	clientVersion uint32,
	marketId uint32,
	channelPackageId string,
) (uint32, error) {
	var genOption []*genPB.BlockOption

	friendCount, err := downstream.FriendshipClient.GetFriendCount(ctx, userId, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "distributionFromRadar GetFriendCount error uid(%v) err: %v\n", userId, err)
		return 0, err
	}
	log.DebugWithCtx(ctx, "distributionFromRadar userId(%d) friendCount %v", userId, friendCount)
	if friendCount >= 10 {
		return 0, nil
	}

	gameCards, err := downstream.GameCardClient.GetGameCard(ctx, userId)
	if err != nil {
		log.ErrorWithCtx(ctx, "distributionFromRadar GetGameCard error userId(%d) err: %v\n", userId, err)
		return 0, err
	}
	gameCard := &game_card.GameCardInfo{}
	rand.Seed(time.Now().Unix())
	index := rand.Intn(len(gameCards) + 1) //nolint:gosec
	log.DebugWithCtx(ctx, "distributionFromRadar userId(%d) index(%d) gameCards %v ", userId, index, gameCards)
	var tabId uint32
	if index == len(gameCards) {
		tabId = confz.TopicChannelLogicConfig.GetChatTabId()
	} else {
		gameCard = gameCards[index]
		if gameCard.CardType == 1 { //游戏卡
			var err error
			tabInfo, err := downstream.TCTabClient.FiniteTabsByTags(ctx, &tabPB.FiniteTabsByTagsReq{TagId: []uint32{gameCard.GameCardId}})
			if err != nil {
				log.ErrorWithCtx(ctx, "distributionFromRadar newDistributionTopicChannel FiniteTabsByTags error TagId(%d) err: %v\n", gameCard.GameCardId, err)
				return 0, err
			} else if len(tabInfo.Tabs) < 1 {
				log.WarnWithCtx(ctx, "distributionFromRadar newDistributionTopicChannel FiniteTabsByTags error TagId(%d) get nil\n", gameCard.GameCardId)
				return 0, nil
			}
			tabId = tabInfo.GetTabs()[0].GetId()
			userTag, err := downstream.UserlineCommonApiClient.GetSimpleGameTag(ctx, []uint32{userId}, gameCard.GameCardName)
			if err != nil {
				log.ErrorWithCtx(ctx, "distributionFromRadar GetSimpleGameTags error uid(%d) err: %v\n", userId, err)
				return 0, err
			}

			if userTag.GameExtList == nil || len(userTag.GameExtList) < 1 {
				return 0, nil
			}

			bs, berr := downstream.TCTabClient.Blocks(ctx, &tabPB.BlocksReq{
				TabId: tabId,
			})
			if berr != nil {
				log.ErrorWithCtx(ctx, "distributionFromRadar ListTabBlocks  uid(%d) tabId (%v) Blocks err(%v)\n", userId, tabId, berr)
				return 0, berr
			}

			for _, b := range bs.GetBlocks() {
				//把请求推荐的参数一点点拼出来
				genOption = appendRadarOptionsAndGenOption(b, userTag.GameExtList[0], nil, genOption)
			}
		}
	}

	if gameCard.CardType == 2 { //音乐卡
		getRecommendationListReq := music_channel.GetMusicChannelRecommendationReq{
			Uid: userId, Limit: reqCount, TabIds: []uint32{confz.TopicChannelLogicConfig.GetRandomMusicRoom()}, BlockOptions: nil,
			ClientType: clientType, ClientVersion: clientVersion, MarketId: marketId,
			ChannelPackageId: channelPackageId,
		}
		genResp, err := downstream.RcmdMusicChannelClient.GetMusicChannelRecommendation(ctx, &getRecommendationListReq)
		log.DebugWithCtx(ctx, "DistributionTopicChannel GetMusicChannelRecommendation getRecommendationListReq:%v  genResp:%v ", getRecommendationListReq, genResp)
		if err != nil {
			log.ErrorWithCtx(ctx, "DistributionTopicChannel gen GetRecommendationList userId(%v) tabId(%v) err(%v)\n", userId, confz.TopicChannelLogicConfig.GetChatTabId(), err)
			return 0, err
		} else if len(genResp.GetChannelIdList()) < 1 {
			return 0, err
		}
		return genResp.GetChannelIdList()[0], nil
	} else { //游戏\
		ctx2, err := template.NewContext(ctx, nil)
		if err != nil {
			log.ErrorWithCtx(ctx, "NewContext err: %v", err)
			return 0, err
		}
		getRecommendationListReq := genPB.GetRecommendationListReq{
			Uid: userId, Limit: 1, TabId: tabId, BlockOptions: nil, GetMode: genPB.GetRecommendationListReq_REFRESH,
			ChannelEnterSource: uint32(gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_RADAR_ISSUE),
			ClientType:         clientType, ClientVersion: clientVersion, MarketId: marketId,
			Env:              convertEnv(confz.GetEnv()),
			ChannelPackageId: channelPackageId,
			RegulatoryLevel:  genPB.REGULATORY_LEVEL(supervision.GetUserRegulatoryLevel(ctx2)),
		}

		genResp, err := downstream.GenRecommendationClient.GetRecommendationList(ctx, &getRecommendationListReq)
		log.DebugWithCtx(ctx, "DistributionTopicChannel GetRecommendationList getRecommendationListReq:%v genResp:%v ", getRecommendationListReq, genResp)
		if err != nil {
			log.ErrorWithCtx(ctx, "DistributionTopicChannel gen GetRecommendationList userId(%d) tabId(%d) err(%v)\n", userId, confz.TopicChannelLogicConfig.GetChatTabId(), err)
			return 0, err
		} else if len(genResp.GetChannelId()) < 1 {
			log.WarnWithCtx(ctx, "GetRecommendationList can not get channelId")
			return 0, err
		}

		return genResp.GetChannelId()[0], nil
	}
}

func distributionTopicChannelForMic(ctx context.Context, reqUid uint32, marketId uint32) (uint32, uint32, uint32, string, error) {
	resp, err := downstream.RcmdMusicChannelClient.GetMusicChannelPopupRcmd(ctx, &music_song_ktv_pb.GetMusicChannelPopupRcmdReq{
		Uid:      reqUid,
		MarketId: marketId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMusicChannelPopupRcmd err: %v", err)
		return 0, 0, 0, "", err
	}

	log.InfoWithCtx(ctx, "GetMusicChannelPopupRcmd uid: %d channelId: %d relationUids: %v", resp.GetFriendUid(), resp.GetChannelId(), resp.GetRelationUids())
	var relationUid uint32
	if len(resp.GetRelationUids()) > 0 {
		relationUid = resp.GetRelationUids()[0]

		var err error
		channelResp, err := downstream.TCChannelClient.GetChannelByIds(ctx, &channelPB.GetChannelByIdsReq{Ids: []uint32{resp.GetChannelId()}})
		if err == nil && len(channelResp.GetInfo()) == 1 {
			tabResp, err := downstream.TCTabClient.SearchTabs(ctx, &tabPB.SearchTabsReq{
				Ids: []uint32{channelResp.GetInfo()[0].GetTabId()},
			})
			if err == nil && len(tabResp.GetTabs()) == 1 {
				uids := make([]string, len(resp.GetRelationUids()))
				for i, uid := range resp.GetRelationUids() {
					uids[i] = strconv.Itoa(int(uid))
				}

				err = bylink.Track(ctx, uint64(reqUid), "room_push_reunion_log", reportData{
					TagName: tabResp.GetTabs()[0].GetName(),
					RoomId:  resp.GetChannelId(),
					Source:  0,
					UidList: strings.Join(uids, ","),
					TraceId: resp.GetMetaId(),
				}, true)
				if err != nil {
					log.ErrorWithCtx(ctx, "Track err: %v", err)
				}
			} else {
				log.ErrorWithCtx(ctx, "SearchTabs err: %v", err)
			}
		} else {
			log.ErrorWithCtx(ctx, "GetChannelByIds info: %d err: %v", len(channelResp.GetInfo()), err)
		}
	}

	return resp.GetFriendUid(), relationUid, resp.GetChannelId(), resp.GetMetaId(), nil
}

type reportData struct {
	TagName string `json:"tag_name"`
	RoomId  uint32 `json:"room_id"`
	Source  uint32 `json:"source"`
	UidList string `json:"uid_list"`
	TraceId string `json:"trace_id"`
}

// 处理被惩罚用户 2021-12-02
func getPunishUsers(ctx context.Context, userIds []uint32) (map[uint32]bool, error) {
	if confz.TopicChannelLogicConfig.GetCheckPunish() == 0 {
		return nil, nil
	}

	punishResp, err := downstream.PunishUserCli.CheckPunishUser(ctx, userIds, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "getPunishUsers by ids(%v) err(%v) \n", userIds, err)
		return nil, err
	}
	return punishResp.UserPunishResult, nil
}

// 房间转移
func shiftRoom(tabId uint32, inBlockOption []*topic_channel.BlockOption) (genOption []*genPB.BlockOption, isShift bool) {
	//目前非王者吃鸡不转移
	if !confz.TopicChannelLogicConfig.IsShiftRoomTab(tabId) {
		return nil, false
	}

	for _, o := range inBlockOption {
		if o.ElemId > 0 && o.ElemId != InfElemID {
			genOption = append(genOption, &genPB.BlockOption{BlockId: o.BlockId, ElemId: o.ElemId})
		}
	}
	return genOption, true
}

func distributionFromRadar(ctx context.Context, userId uint32) (tabId, channelEnterSource uint32, genOption []*genPB.BlockOption, err error) {
	//雷达房间下发
	channelEnterSource = uint32(gaChannelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_RADAR_ISSUE)
	radarInfo, rerr := genRadarInfo(ctx, userId)
	if rerr != nil {
		log.ErrorWithCtx(ctx, "distributionFromRadar genRadarInfo error uid(%v) err: %v\n", userId, rerr)
		return 0, 0, nil, rerr
	}

	log.DebugWithCtx(ctx, "distributionFromRadar radarInfo %v", radarInfo)
	if !radarInfo.RadarOpenStatus {
		friendCount, err := downstream.FriendshipClient.GetFriendCount(ctx, userId, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "distributionFromRadar GetFriendCount error uid(%v) err: %v\n", userId, err)
			return 0, 0, nil, err
		}
		log.DebugWithCtx(ctx, "distributionFromRadar friendCount %v", friendCount)
		if friendCount >= 10 {
			return 0, 0, nil, errors.New("return nil is ok")
		}
	}

	tabInfo, terr := downstream.TCTabClient.FiniteTabsByTags(ctx, &tabPB.FiniteTabsByTagsReq{TagId: []uint32{radarInfo.TagId}})
	if terr != nil {
		log.ErrorWithCtx(ctx, "distributionFromRadar FiniteTabsByTags error TagId(%v) err: %v\n", radarInfo.TagId, terr)
		return 0, 0, nil, terr
	}
	if len(tabInfo.GetTabs()) == 0 {
		log.WarnWithCtx(ctx, "distributionFromRadar FiniteTabsByTags TabNotFound TagId(%v)\n", radarInfo.TagId)
		return 0, 0, nil, errors.New("return nil is ok")
	}
	tabId = tabInfo.GetTabs()[0].GetId()

	userTag, uerr := downstream.UserlineCommonApiClient.GetSimpleGameTag(ctx, []uint32{userId}, radarInfo.GameName)
	if uerr != nil {
		log.ErrorWithCtx(ctx, "distributionFromRadar GetSimpleGameTags error uid(%v) err: %v\n", userId, uerr)
		return 0, 0, nil, uerr
	}

	if userTag.GameExtList == nil || len(userTag.GameExtList) < 1 {
		return
	}

	bs, berr := downstream.TCTabClient.Blocks(ctx, &tabPB.BlocksReq{
		TabId: tabId,
	})
	if berr != nil {
		log.ErrorWithCtx(ctx, "distributionFromRadar ListTabBlocks tabId (%v) Blocks err(%v)\n", tabId, err)
		return 0, 0, nil, berr
	}

	for _, b := range bs.GetBlocks() {
		//把请求推荐的参数一点点拼出来
		genOption = appendRadarOptionsAndGenOption(b, userTag.GameExtList[0], radarInfo, genOption)
	}
	return
}

// 拼接游戏卡信息和开黑房信息对应匹配的信息
func appendRadarOptionsAndGenOption(b *tabPB.Block, userTagExt *userline_common_api.SimpleGameTagExt, radarInfo *gameradarPB.GetUserChannelPushInfoResp, genOption []*genPB.BlockOption) []*genPB.BlockOption {
	targetString := findUserTagFieldByTitle(b.Title, userTagExt)
	if isContains, eid := findContainsStringElem(b.Elems, targetString); isContains {
		genOption = append(genOption, &genPB.BlockOption{BlockId: b.Id, ElemId: eid})
	}

	if strings.Contains(b.Title, "分路") {
		for _, g := range userTagExt.GamePosition {
			if isContains, eid := findContainsStringElem(b.Elems, g); isContains {
				genOption = append(genOption, &genPB.BlockOption{BlockId: b.Id, ElemId: eid})
			}
		}
	}
	if b.Title == radarInfo.GetModelName() {
		genOption = append(genOption, &genPB.BlockOption{BlockId: b.Id, ElemId: radarInfo.GetModelId()})
	}
	return genOption
}

func findUserTagFieldByTitle(title string, userTagExt *userline_common_api.SimpleGameTagExt) string {
	if strings.Contains(title, "段位") {
		return userTagExt.GameDan
	}
	if strings.Contains(title, "区服") {
		return userTagExt.GameArea
	}
	return ""
}

func findContainsStringElem(elems []*tabPB.Elem, targetString string) (isContains bool, elemId uint32) {
	if targetString == "" {
		return false, 0
	}
	for _, e := range elems {
		if e.Title == targetString {
			return true, e.Id
		}
	}
	return false, 0
}

type TagInfoSimple struct {
	tagid   uint32
	tagname string
}

func genRadarInfo(ctx context.Context, uid uint32) (*gameradarPB.GetUserChannelPushInfoResp, error) {
	out := new(gameradarPB.GetUserChannelPushInfoResp)
	/*没有开启的雷达，从游戏卡+扩列，随机一个,模式不填*/
	// 获取用户标签
	userTagBase, err := downstream.UserlineCommonApiClient.GetUserTag(ctx, uid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "genRadarInfo.GetUserTag err: %v", err)
		return nil, err
	}

	tagids := make([]*TagInfoSimple, 0)
	for _, base := range userTagBase {
		if !base.IsDel && base.TagType == uint32(gaChannelPB.UserTagType_USERTAG_TYPE_GAME) {
			tagids = append(tagids, &TagInfoSimple{
				tagid:   base.GetTagId(),
				tagname: base.GetTagName(),
			})
			log.DebugWithCtx(ctx, "getTabByTag >> %d get %d", uid, base.GetTagId())
		}
	}
	gameList := confz.TopicChannelLogicConfig.GetGameList()
	for _, v := range gameList {
		if v.GameTitle == KuoLieChat {
			tagids = append(tagids, &TagInfoSimple{
				tagid:   v.GameID,
				tagname: v.GameTitle,
			})
			break
		}
	}
	if len(tagids) != 0 {
		randid := rand.Intn(len(tagids)) //nolint:gosec
		out.TagId = tagids[randid].tagid
		out.GameName = tagids[randid].tagname
	}

	return out, nil
}

func convertEnv(env string) genPB.GetRecommendationListReq_Environment {
	switch env {
	case confz.Testing:
		return genPB.GetRecommendationListReq_test
	case confz.Staging:
		return genPB.GetRecommendationListReq_staging
	case confz.Production:
		return genPB.GetRecommendationListReq_production
	}

	return 0
}
