package mgr

import (
	"context"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	account_go_mock "golang.52tt.com/clients/mocks/account-go"
	"golang.52tt.com/clients/mocks/channel"
	"golang.52tt.com/clients/mocks/channelmic"
	"golang.52tt.com/clients/mocks/channelol"
	"golang.52tt.com/clients/mocks/configserver"
	game_card "golang.52tt.com/clients/mocks/game-card"
	game_pal "golang.52tt.com/clients/mocks/game-pal"
	gangup_channel "golang.52tt.com/clients/mocks/gangup-channel"
	presence_v2 "golang.52tt.com/clients/mocks/presence/v2"
	topic_channel "golang.52tt.com/clients/mocks/topic-channel/channel"
	"golang.52tt.com/clients/mocks/topic-channel/tab"
	friendshipMock "golang.52tt.com/clients/mocks/ugc/friendship"
	pushNotificationClient "golang.52tt.com/clients/push-notification/v2"
	accountGoPb "golang.52tt.com/protocol/services/account-go"
	channelSvrPb "golang.52tt.com/protocol/services/channelsvr"
	inviteRoomPb "golang.52tt.com/protocol/services/invite-room"
	"golang.52tt.com/protocol/services/mocks"
	presence "golang.52tt.com/protocol/services/presencesvr"
	rcmdMocks "golang.52tt.com/protocol/services/rcmd/mocks"
	"golang.52tt.com/protocol/services/rcmd/rcmd_invite_user"
	topicChannelPb "golang.52tt.com/protocol/services/topic_channel/channel"
	tabPb "golang.52tt.com/protocol/services/topic_channel/tab"
	friendPb "golang.52tt.com/protocol/services/ugc/friendship"
	"golang.52tt.com/services/invite-room-logic/internal/rpc"
	"testing"
)

// helperForTest is a helper struct for testing.
type helperForTest struct {
	mgr *InviteListMgr
}

// newHelperForTest initializes the helperForTest struct.
func newHelperForTest(t *testing.T) *helperForTest {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient(ctrl)

	inviteListDatReq := &InviteListReq{
		Uid:           1,
		TabId:         1,
		Labels:        []string{},
		ForceUid:      []uint32{},
		NoBrowseList:  []uint32{},
		ReqType:       1,
		ClientVersion: 0,
	}
	tMgr := NewInviteListMgr(inviteListDatReq, nil)

	return &helperForTest{mgr: tMgr}
}

func mockClient(ctrl *gomock.Controller) {
	rpc.AccountClient = account_go_mock.NewMockIClient(ctrl)
	rpc.RcmdInviteUserCli = rcmdMocks.NewMockRcmdInviteUserClient(ctrl)
	rpc.FriendshipCli = friendshipMock.NewMockIClient(ctrl)
	rpc.PushClient = pushNotificationClient.NewMockIClient(ctrl)
	rpc.GamePalClient = game_pal.NewMockIClient(ctrl)
	rpc.TCTabClient = tab.NewMockIClient(ctrl)
	rpc.PresenceClient = presence_v2.NewMockIClient(ctrl)
	rpc.ChannelOl = channelol.NewMockIClient(ctrl)
	rpc.TopicClient = topic_channel.NewMockIClient(ctrl)
	rpc.ChannelCli = channel.NewMockIClient(ctrl)
	rpc.InviteRoomClient = mocks.NewMockInviteRoomClient(ctrl)
	rpc.ConfigServerClient = configserver.NewMockIClient(ctrl)
	rpc.GangupChannelClient = gangup_channel.NewMockIClient(ctrl)
	rpc.ChannelMicClient = channelmic.NewMockIClient(ctrl)
	rpc.GameCardClient = game_card.NewMockIClient(ctrl)
}

func (receiver *helperForTest) getAccountClient() *account_go_mock.MockIClient {
	return rpc.AccountClient.(*account_go_mock.MockIClient)
}

func (receiver *helperForTest) getRcmdInviteUserCli() *rcmdMocks.MockRcmdInviteUserClient {
	return rpc.RcmdInviteUserCli.(*rcmdMocks.MockRcmdInviteUserClient)
}

func (receiver *helperForTest) getFriendshipCli() *friendshipMock.MockIClient {
	return rpc.FriendshipCli.(*friendshipMock.MockIClient)
}

func (receiver *helperForTest) getPushClient() *pushNotificationClient.MockIClient {
	return rpc.PushClient.(*pushNotificationClient.MockIClient)
}

func (receiver *helperForTest) getGamePalClient() *game_pal.MockIClient {
	return rpc.GamePalClient.(*game_pal.MockIClient)
}

func (receiver *helperForTest) getTCTabClient() *tab.MockIClient {
	return rpc.TCTabClient.(*tab.MockIClient)
}

func (receiver *helperForTest) getPresenceClient() *presence_v2.MockIClient {
	return rpc.PresenceClient.(*presence_v2.MockIClient)
}

func (receiver *helperForTest) getChannelOl() *channelol.MockIClient {
	return rpc.ChannelOl.(*channelol.MockIClient)
}

func (receiver *helperForTest) getTopicClient() *topic_channel.MockIClient {
	return rpc.TopicClient.(*topic_channel.MockIClient)
}

func (receiver *helperForTest) getChannelCli() *channel.MockIClient {
	return rpc.ChannelCli.(*channel.MockIClient)
}

func (receiver *helperForTest) getInviteRoomClient() *mocks.MockInviteRoomClient {
	return rpc.InviteRoomClient.(*mocks.MockInviteRoomClient)
}

func (receiver *helperForTest) getConfigServerClient() *configserver.MockIClient {
	return rpc.ConfigServerClient.(*configserver.MockIClient)
}

func (receiver *helperForTest) getGangupChannelClient() *gangup_channel.MockIClient {
	return rpc.GangupChannelClient.(*gangup_channel.MockIClient)
}

func (receiver *helperForTest) getChannelMicClient() *channelmic.MockIClient {
	return rpc.ChannelMicClient.(*channelmic.MockIClient)
}

func (receiver *helperForTest) getGameCardClient() *game_card.MockIClient {
	return rpc.GameCardClient.(*game_card.MockIClient)
}

func TestInviteListMgr_GetInviteListData_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s := newHelperForTest(t)

	ctx := context.Background()

	inviteListReq := &InviteListReq{
		Uid:          1,
		TabId:        1,
		Labels:       []string{},
		ForceUid:     []uint32{},
		NoBrowseList: []uint32{},
		ReqType:      1,
	}
	s.mgr.inviteListReq = inviteListReq

	originDatas := []*rcmd_invite_user.UserInfo{
		{
			Uid:           2,
			ShowTabId:     1,
			TitleShowType: rcmd_invite_user.UserInfo_Channel,
			Detail:        &rcmd_invite_user.UserDetail{},
		},
	}

	s.getRcmdInviteUserCli().EXPECT().GetInviteStrangerUser(gomock.Any(), gomock.Any()).Return(&rcmd_invite_user.GetInviteStrangerUserResp{
		BottomReached: false,
		StrangerList:  originDatas,
	}, nil)

	s.getTCTabClient().EXPECT().GetTabsByIds(gomock.Any(), gomock.Any()).Return(map[uint32]*tabPb.Tab{
		1: {
			Name: "Test Tab",
		},
	}, nil).AnyTimes()

	uid := uint32(2)
	s.getAccountClient().EXPECT().GetUsersByUids(gomock.Any(), []uint32{2}).Return(&accountGoPb.UsersResp{
		UserList: []*accountGoPb.UserResp{
			{
				Uid: uid,
			},
		},
	}, nil).AnyTimes()

	userChannelMap := map[uint32]uint32{
		2: 101,
		3: 102,
		4: 0,
	}
	s.getChannelOl().EXPECT().BatchGetUserChannelId(gomock.Any(), gomock.Any(), gomock.Any()).Return(userChannelMap, nil).AnyTimes()

	channelId2Info := map[uint32]*channelSvrPb.ChannelSimpleInfo{
		101: {},
	}
	s.getChannelCli().EXPECT().BatchGetChannelSimpleInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(channelId2Info, nil).AnyTimes()

	tcResp := &topicChannelPb.GetChannelByIdsResp{
		Info: []*topicChannelPb.ChannelInfo{
			{Id: 101},
		},
	}
	s.getTopicClient().EXPECT().GetChannelByIds(gomock.Any(), gomock.Any()).Return(tcResp, nil).AnyTimes()

	blockInfo := &tabPb.BatchGetBlocksResp{
		Data: map[uint32]*tabPb.BlocksResp{
			1: {Blocks: []*tabPb.Block{
				{Id: 1, Title: "Block1"},
				{Id: 2, Title: "Block2"},
			}},
		},
	}
	s.getTCTabClient().EXPECT().BatchGetBlocks(gomock.Any(), &tabPb.BatchGetBlocksReq{TabId: []uint32{1}}).Return(blockInfo, nil).AnyTimes()

	s.getPresenceClient().EXPECT().BatchGetUserPres(gomock.Any(), []uint32{2}).Return(map[uint32]*presence.PresInfoList{
		2: {
			InfoList: []*presence.PresInfo{{
				Key:     nil,
				Val:     nil,
				Offline: true,
			}},
		},
	}, nil).AnyTimes()

	s.getFriendshipCli().EXPECT().BatchGetBiFollowingWithCache(gomock.Any(), uint32(1), []uint32{2}, true, true).Return(map[uint32]bool{
		2: true,
	}, map[uint32]bool{
		2: true,
	}, nil).AnyTimes()

	s.getFriendshipCli().EXPECT().BatchGetFriendInfo(gomock.Any(), &friendPb.BatchGetFriendInfoReq{
		FriendPairs: []*friendPb.BatchGetFriendInfoReq_FriendPair{
			{
				Uid:       1,
				FriendUid: 2,
			},
		},
	}).Return(&friendPb.BatchGetFriendInfoResp{
		FriendInfos: []*friendPb.BatchGetFriendInfoResp_FriendAllInfo{
			{
				Uid: uid,
			},
		},
	}, nil).AnyTimes()

	s.getInviteRoomClient().EXPECT().GetUserInviteStatus(gomock.Any(), gomock.Any()).Return(&inviteRoomPb.GetUserInviteStatusResponse{
		StatusMap: map[uint32]inviteRoomPb.GetUserInviteStatusResponse_InviteStatus{1: inviteRoomPb.GetUserInviteStatusResponse_INVITE_STATUS_INVITED},
	}, nil).AnyTimes()

	//s.getGangupChannelClient().EXPECT().GetChannelListFilterRecord(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	out, err := s.mgr.GetInviteListData(ctx)
	assert.NoError(t, err)
	assert.NotNil(t, out)
}
