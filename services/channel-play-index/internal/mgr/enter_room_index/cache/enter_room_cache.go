package cache

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	channel_play_index "golang.52tt.com/protocol/services/channel-play-index"
	"strconv"
	"time"
)

const rplayCntKeyPrex = "rplay_cnt"
const rplayDurationKeyPrex = "rplay_duation"
const EnterRoom2DayExpireHour int = 24 * 2

// const EnterRoom7DayExpireHour int = 24 * 7
const EnterRoom1DayExpireHour int = 24 * 2
const EnterRoomBeginTime3DayExpireHour int = 24 * 3

func (r *RedisDao) getEnterRoomByPlayCntKey(happenTime int64) string {
	nowDate := time.UnixMilli(happenTime).Format("20060102")
	return fmt.Sprintf("%s_%s", rplayCntKeyPrex, nowDate)
}

func (r *RedisDao) getEnterRoomByPlayDurationKey(happenTime int64) string {
	nowDate := time.UnixMilli(happenTime).Format("20060102")
	return fmt.Sprintf("%s_%s", rplayDurationKeyPrex, nowDate)
}

func (r *RedisDao) getUserEnterTimeKey(cid uint32) string {
	return fmt.Sprintf("enter_r_b_%d", cid)
}

func (r *RedisDao) DelEnterRoomByPlayCntKey(ctx context.Context, happenTime int64) {
	matchKey := r.getEnterRoomByPlayCntKey(happenTime)
	cnt, err := r.playCli.Unlink(ctx, matchKey).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelEnterRoomByPlayCntKey Unlink err:%v, key:%s", err, matchKey)
	}
	if cnt != 0 {
		log.WarnWithCtx(ctx, "DelEnterRoomByPlayCntKey Unlink cnt %d, key:%s", cnt, matchKey)
	}
}

func (r *RedisDao) DelEnterRoomByPlayDurationKey(ctx context.Context, happenTime int64) {
	matchKey := r.getEnterRoomByPlayDurationKey(happenTime)
	cnt, err := r.playCli.Unlink(ctx, matchKey).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelEnterRoomByPlayDurationKey Unlink err:%v, key:%s", err, matchKey)
	}
	if cnt != 0 {
		log.WarnWithCtx(ctx, "DelEnterRoomByPlayDurationKey Unlink cnt %d, key:%s", cnt, matchKey)
	}
}

func (r *RedisDao) getEnterRoomByPlayField(uid, tabId uint32) string {
	return fmt.Sprintf("%d_%d", tabId, uid)
}

func (r *RedisDao) AddEnterRoomByPlayCnt(ctx context.Context, tabId, uid uint32, happenTime int64) (int64, error) {
	playKey := r.getEnterRoomByPlayCntKey(happenTime)
	field := r.getEnterRoomByPlayField(uid, tabId)
	count, err := r.playCli.HIncrBy(ctx, playKey, field, 1).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddEnterRoomByPlayCnt err:%v, playKey:%s, uid:%d", err, playKey, uid)
		return 0, err
	}
	//log.InfoWithCtx(ctx, "AddEnterRoomByPlayCnt key:%s", playKey)
	return count, nil
}

func (r *RedisDao) GetEnterRoomByPlayCnt(ctx context.Context, tabId, uid uint32, happenTime int64) (int64, error) {
	playKey := r.getEnterRoomByPlayCntKey(happenTime)
	field := r.getEnterRoomByPlayField(uid, tabId)
	cnt, err := r.playCli.HGet(ctx, playKey, field).Int64()
	if err != nil {
		if err == redis.Nil {
			log.WarnWithCtx(ctx, "GetEnterRoomByPlayCnt get nil, uid:%d", uid)
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "GetEnterRoomByPlayCnt err:%v, uid:%d", err, uid)
	}
	return cnt, err
}

func (r *RedisDao) AddEnterRoomByPlayDuration(ctx context.Context, tabId, uid uint32, nDuration int64, happenTime int64) error {
	playKey := r.getEnterRoomByPlayDurationKey(happenTime)
	field := r.getEnterRoomByPlayField(uid, tabId)
	err := r.playCli.HIncrBy(ctx, playKey, field, nDuration).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddEnterRoomByPlayDuration HIncrBy err:%v, playKey:%s, uid:%d", err, playKey, uid)
		return err
	}
	return nil
}

func (r *RedisDao) GetEnterRoomByPlayDuration(ctx context.Context, tabId, uid uint32, happenTime int64) (int64, error) {
	playKey := r.getEnterRoomByPlayDurationKey(happenTime)
	field := r.getEnterRoomByPlayField(uid, tabId)
	nDuration, err := r.playCli.HGet(ctx, playKey, field).Int64()
	if err != nil {
		if err == redis.Nil {
			log.WarnWithCtx(ctx, "GetEnterRoomByPlayCnt get nil, uid:%d", uid)
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "GetEnterRoomByPlayCnt err:%v, playKey:%s, uid:%d", err, playKey, uid)
	}
	return nDuration, err
}

func (r *RedisDao) AddEnterTabId(ctx context.Context, cid, uid, tabId uint32) error {
	playKey := r.getUserEnterTimeKey(cid)
	strUid := strconv.FormatInt(int64(uid), 10)
	err := r.cli.HSet(ctx, playKey, strUid, tabId).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddEnterTabId HSet err:%v, playKey:%s, uid:%d", err, playKey, uid)
		return err
	}
	err = r.cli.Expire(ctx, playKey, time.Hour*time.Duration(EnterRoom2DayExpireHour)).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddChannelUid Expire err:%v, channelUidsKey:%s, uid:%d", err, playKey, uid)
	}
	return nil
}

func (r *RedisDao) DelEnterTabId(ctx context.Context, cid, uid uint32) error {
	playKey := r.getUserEnterTimeKey(cid)
	strUid := strconv.FormatInt(int64(uid), 10)
	err := r.cli.HDel(ctx, playKey, strUid).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddEnterTabId HSet err:%v, playKey:%s, uid:%d", err, playKey, uid)
		return err
	}
	return nil
}

func (r *RedisDao) GetEnterRoomTabId(ctx context.Context, cid, uid uint32) (uint32, error) {
	playKey := r.getUserEnterTimeKey(cid)
	strUid := strconv.FormatInt(int64(uid), 10)
	tabId, err := r.cli.HGet(ctx, playKey, strUid).Int64()
	if err != nil {
		if err == redis.Nil {
			log.WarnWithCtx(ctx, "GetEnterRoomTabId tabid 0, uid:%d", uid)
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "GetEnterRoomTabId HGet tabid err:%v, uid:%d", err, uid)
	}
	return uint32(tabId), err
}

func (r *RedisDao) getEnterRoomBeginTimeKey(cid uint32) string {
	return fmt.Sprintf("enter_r_t_%d", cid)
}

func (r *RedisDao) AddEnterRoomBeginTime(ctx context.Context, cid, uid uint32, enterRoomTime int64) error {
	playKey := r.getEnterRoomBeginTimeKey(cid)
	strUid := strconv.FormatInt(int64(uid), 10)
	err := r.cli.HSet(ctx, playKey, strUid, enterRoomTime).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddEnterRoomBeginTime HSet err:%v, playKey:%s, uid:%d", err, playKey, uid)
		return err
	}
	err = r.cli.Expire(ctx, playKey, time.Hour*time.Duration(EnterRoomBeginTime3DayExpireHour)).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddChannelUid Expire err:%v, channelUidsKey:%s, uid:%d", err, playKey, uid)
	}
	return nil
}

func (r *RedisDao) DelEnterRoomBeginTime(ctx context.Context, cid, uid uint32) error {
	playKey := r.getEnterRoomBeginTimeKey(cid)
	strUid := strconv.FormatInt(int64(uid), 10)
	err := r.cli.HDel(ctx, playKey, strUid).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelEnterRoomBeginTime HSet err:%v, playKey:%s, uid:%d", err, playKey, uid)
		return err
	}
	return nil
}

func (r *RedisDao) GetEnterRoomBeginTimeByUids(ctx context.Context, cid uint32, uids []uint32) (map[uint32]int64, error) {
	playKey := r.getEnterRoomBeginTimeKey(cid)

	var strGetUids []string
	for _, uid := range uids {
		strGetUids = append(strGetUids, strconv.FormatUint(uint64(uid), 10))
	}

	infos, err := r.cli.HMGet(ctx, playKey, strGetUids...).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetEnterRoomBeginTime HMGet err:%+v, playKey:%s, cid:%d, strUids:%v", err, playKey, cid, strGetUids)
		return nil, err
	}
	uid2beginTimeMap := make(map[uint32]int64)
	for index, uid := range uids {
		if info := infos[index]; info != nil {
			if strBeginTime, ok := info.(string); ok {
				//log.InfoWithCtx(ctx, "111 test res:%v", infos[index])
				uidTmp, err := strconv.ParseInt(strBeginTime, 10, 0)
				if err == nil {
					uid2beginTimeMap[uid] = uidTmp
				} else {
					log.ErrorWithCtx(ctx, "GetEnterRoomBeginTime ParseInt err, strBeginTime:%v", strBeginTime)
				}

				log.DebugWithCtx(ctx, "GetEnterRoomBeginTime HMGet info:%+v", uid2beginTimeMap)
			} else {
				log.ErrorWithCtx(ctx, "GetEnterRoomBeginTime HMGet type err, info:%v", info)
			}

		}
	}
	return uid2beginTimeMap, nil
}

/*func (r *RedisDao) getUserInRoomKey(uid uint32, happenTime int64) string {
	nowDate := time.UnixMilli(happenTime).Format("20060102")
	return fmt.Sprintf("uid_room_%s_%d", nowDate, uid)
}

type RoomInfo struct {
	EnterRoomTime int64
	ExitRoomTime  int64
	Uid           uint32
	ChannelId     uint32
}

func (r *RedisDao) AddUserInRoomInfo(ctx context.Context, ChannelId, uid uint32, enterRoomTime, exitRoomTime int64) error {
	info := &pb.UserInRoomInfo{
		EnterRoomTs: enterRoomTime,
		ExitRoomTs:  exitRoomTime,
		ChannelId:   ChannelId,
	}
	jsonRoomInfo, err := json.Marshal(info)
	if err != nil {
		return err
	}
	userInRoomKey := r.getUserInRoomKey(uid, exitRoomTime)
	strExitRoomTime := strconv.FormatInt(exitRoomTime, 10)
	err = r.cli.HSet(ctx, userInRoomKey, strExitRoomTime, jsonRoomInfo).Err()
	if err != nil {
		return err
	}
	return nil
}


func (r *RedisDao) BatchGetUserInRoomInfo(ctx context.Context, uids []uint32, happenTime int64) (*pb.GetUserInRoomInfoResp, error) {
	cmds, err := r.cli.Pipelined(ctx, func(pl redis.Pipeliner) error {
		for _, uid := range uids {
			roomInfoKey := r.getUserInRoomKey(uid, happenTime)
			pl.HGetAll(ctx, roomInfoKey)
		}
		return nil
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserInRoomInfo redis.Pipelined HGetAll failed, err:%v", err)
		return nil, err
	}

	user2infoMap := &pb.GetUserInRoomInfoResp{
		UserInRoomMap: make(map[uint32]*pb.UserInRoomInfos),
	}
	for i, cmd := range cmds {
		uid := uids[i]
		kv, err := cmd.(*redis.StringStringMapCmd).Result()
		if err != nil {
			log.ErrorWithCtx(ctx, "HGetAll failed, uid:%d, err:%v", uid, err)
			continue
		}

		infos := &pb.UserInRoomInfos{}
		for _, v := range kv {
			info := &pb.UserInRoomInfo{}
			err = json.Unmarshal([]byte(v), info)
			if err != nil {
				log.ErrorWithCtx(ctx, "json.Unmarshal err, uid:%d, err:%v", uid, err)
				continue
			}
			infos.UserInRoomInfos = append(infos.UserInRoomInfos, info)
		}
		user2infoMap.UserInRoomMap[uid] = infos
	}
	return user2infoMap, nil
}*/

func (r *RedisDao) UpdateTaskStatus(ctx context.Context, uid uint32, taskLabel string, finishTaskTime uint64, deadTime int) error {
	taskKey := r.getTaskKey(uid, taskLabel)
	err := r.cli.Set(ctx, taskKey, finishTaskTime, time.Duration(deadTime)*time.Second).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateTaskStatus taskKey:%s finishTaskTime:%d err:%+v", taskKey, finishTaskTime, err)
		return err
	}
	return nil
}

func (r *RedisDao) getTaskKey(uid uint32, taskLabel string) string {
	return fmt.Sprintf("%s_%d", taskLabel, uid)
}

func (r *RedisDao) GetTaskStatus(ctx context.Context, uid uint32, taskLabel string) (bool, error) {
	key := r.getTaskKey(uid, taskLabel)
	val, err := r.cli.Exists(ctx, key).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTaskStatus Exists key:%s error:%+v", key, err)
		return false, err
	}
	return val > 0, nil
}

func (r *RedisDao) getChannelUidsKey(ChannelId uint32) string {
	return fmt.Sprintf("channel_uids_%d", ChannelId)
}

func (r *RedisDao) AddChannelUid(ctx context.Context, ChannelId, uid uint32) error {
	channelUidsKey := r.getChannelUidsKey(ChannelId)
	err := r.cli.SAdd(ctx, channelUidsKey, uid).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddChannelUid SAdd err:%v, channelUidsKey:%s, uid:%d", err, channelUidsKey, uid)
		return err
	}
	err = r.cli.Expire(ctx, channelUidsKey, time.Hour*time.Duration(EnterRoom2DayExpireHour)).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddChannelUid Expire err:%v, channelUidsKey:%s, uid:%d", err, channelUidsKey, uid)
	}
	return nil
}

func (r *RedisDao) DelChannelUid(ctx context.Context, ChannelId, uid uint32) error {
	channelUidsKey := r.getChannelUidsKey(ChannelId)
	err := r.cli.SRem(ctx, channelUidsKey, uid).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelChannelUid SRem err:%v, channelUidsKey:%s, uid:%d", err, channelUidsKey, uid)
		return err
	}
	err = r.cli.Expire(ctx, channelUidsKey, time.Hour*time.Duration(EnterRoom2DayExpireHour)).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddChannelUid Expire err:%v, channelUidsKey:%s, uid:%d", err, channelUidsKey, uid)
	}
	return nil
}

func (r *RedisDao) DelChannelUids(ctx context.Context, ChannelId uint32) error {
	channelUidsKey := r.getChannelUidsKey(ChannelId)
	err := r.cli.Del(ctx, channelUidsKey).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelChannelUids Del err:%v, channelUidsKey:%s", err, channelUidsKey)
		return err
	}
	return nil
}

func (r *RedisDao) GetChannelUids(ctx context.Context, ChannelId uint32) ([]uint32, error) {
	channelUidsKey := r.getChannelUidsKey(ChannelId)
	infos, err := r.cli.SMembers(ctx, channelUidsKey).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelUids SMembers error:%+v, channelUidsKey:%s", err, channelUidsKey)
		return nil, err
	}
	var uids []uint32
	for _, info := range infos {
		uid, err := strconv.ParseUint(info, 10, 32)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelUids strconv.ParseUint error:%+v, info:%v", err, info)
			continue
		}
		uids = append(uids, uint32(uid))
	}
	return uids, nil
}

func (r *RedisDao) getInSameRoomUidKey(uid uint32, happenTime int64) string {
	nowDate := time.UnixMilli(happenTime).Format("20060102")
	return fmt.Sprintf("sameroom_%s_%d", nowDate, uid)
}

type UidDurationTime struct {
	Uid           uint32
	DurationTime  int64
	EnterRoomTime int64
}

func (r *RedisDao) AddInSameRoomUid(ctx context.Context, exitChannelUid uint32, uidDurationList []*UidDurationTime, happenTime int64, cid, tabId uint32) error {
	if len(uidDurationList) == 0 {
		return nil
	}

	exitChannelInfo := &channel_play_index.ExitRoomInfo{
		TabId:      tabId,
		ExitRoomMs: happenTime,
		ExitUid:    exitChannelUid,
		ChannelId:  cid,
	}

	inSameRoomKey := r.getInSameRoomUidKey(exitChannelUid, happenTime)
	uidMap := make(map[string]interface{})
	for _, uidDurationInfo := range uidDurationList {
		exitChannelInfo.SameRoomUid = uidDurationInfo.Uid
		exitChannelInfo.TogetherTime = uidDurationInfo.DurationTime
		exitChannelInfo.EnterRoomMs = uidDurationInfo.EnterRoomTime
		jsonExitChannelInfo, err := json.Marshal(exitChannelInfo)
		if err != nil {
			log.ErrorWithCtx(ctx, "AddInSameRoomUid json.Marshal err:%+v, exitChannelInfo:%+v", err, exitChannelInfo)
			continue
		}
		strUid := strconv.FormatUint(uint64(uidDurationInfo.Uid), 10)
		uidMap[strUid] = jsonExitChannelInfo
	}

	err := r.cli.HSet(ctx, inSameRoomKey, uidMap).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddInSameRoomUid HSet err:%+v, inSameRoomKey:%s", err, inSameRoomKey)
		return err
	}
	err = r.cli.Expire(ctx, inSameRoomKey, time.Hour*time.Duration(EnterRoom1DayExpireHour)).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddChannelUid Expire err:%v, inSameRoomKey:%s, uid:%d", err, inSameRoomKey, exitChannelUid)
	}
	return nil
}

func (r *RedisDao) GetAllInSameRoomUids(ctx context.Context, exitChannelUid uint32, happenTime int64) (map[string]*channel_play_index.ExitRoomInfo, error) {
	inSameRoomKey := r.getInSameRoomUidKey(exitChannelUid, happenTime)
	infos, err := r.cli.HGetAll(ctx, inSameRoomKey).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllInSameRoomUids HGetAll err:%+v, inSameRoomKey:%s", err, inSameRoomKey)
		return nil, err
	}
	sameRoomInfos := make(map[string]*channel_play_index.ExitRoomInfo)
	for _, info := range infos {
		exitChannelInfo := &channel_play_index.ExitRoomInfo{}
		err := json.Unmarshal([]byte(info), exitChannelInfo)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetInSameRoomUidByUids json.Unmarshal err:%+v, info:%v", err, info)
			continue
		}

		//log.InfoWithCtx(ctx, "test res:%v", res.Val())
		uidGroup := r.getUidGroup(exitChannelInfo.GetExitUid(), exitChannelInfo.GetSameRoomUid())
		sameRoomInfos[uidGroup] = exitChannelInfo
	}
	return sameRoomInfos, nil
}

func (r *RedisDao) getUidGroup(exitRoomUid, sameRoomUid uint32) string {
	uidGroup := strconv.FormatUint(uint64(exitRoomUid), 10) + "_" + strconv.FormatUint(uint64(sameRoomUid), 10)
	return uidGroup
}
func (r *RedisDao) GetInSameRoomUids(ctx context.Context, exitChannelUid uint32, checkUids []uint32, happenTime int64) (map[string]*channel_play_index.ExitRoomInfo, error) {
	if len(checkUids) == 0 {
		return nil, nil
	}
	inSameRoomKey := r.getInSameRoomUidKey(exitChannelUid, happenTime)
	var strCheckUids []string
	for _, uid := range checkUids {
		strCheckUids = append(strCheckUids, strconv.FormatUint(uint64(uid), 10))
	}
	infos, err := r.cli.HMGet(ctx, inSameRoomKey, strCheckUids...).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetInSameRoomUids HMGet err:%+v, inSameRoomKey:%s, uid:%d, strCheckUids:%v", err, inSameRoomKey, exitChannelUid, strCheckUids)
		return nil, err
	}
	sameRoomInfos := make(map[string]*channel_play_index.ExitRoomInfo)
	for index, _ := range checkUids {
		if info := infos[index]; info != nil {
			if strInfo, ok := info.(string); ok {
				//log.InfoWithCtx(ctx, "111 test res:%v", infos[index])
				exitChannelInfo := &channel_play_index.ExitRoomInfo{}
				err := json.Unmarshal([]byte(strInfo), exitChannelInfo)
				if err != nil {
					log.ErrorWithCtx(ctx, "GetInSameRoomUidByUids json.Unmarshal err:%+v, info:%v", err, info)
					continue
				}
				uidGroup := r.getUidGroup(exitChannelInfo.GetExitUid(), exitChannelInfo.GetSameRoomUid())
				sameRoomInfos[uidGroup] = exitChannelInfo
				//log.InfoWithCtx(ctx, "GetInSameRoomUidByUids HMGet info:%+v", info)
			} else {
				log.ErrorWithCtx(ctx, "GetInSameRoomUidByUids HMGet type err, info:%v", info)
			}

		}
	}
	return sameRoomInfos, nil
}

// 通过批量用户查询用户所有房间的其它用户
func (r *RedisDao) GetInSameRoomUidByUids(ctx context.Context, uid uint32, byUids []uint32, happenTime int64) (map[string]*channel_play_index.ExitRoomInfo, error) {
	strExitChannelUid := strconv.FormatUint(uint64(uid), 10)
	cmds, err := r.cli.Pipelined(ctx, func(pl redis.Pipeliner) error {
		for _, byUid := range byUids {
			inSameRoomKey := r.getInSameRoomUidKey(byUid, happenTime)
			pl.HGet(ctx, inSameRoomKey, strExitChannelUid)
		}
		return nil
	})

	if err != nil && err != redis.Nil {
		log.ErrorWithCtx(ctx, "BatchGetUserInRoomInfo redis.Pipelined HGetAll failed, err:%v", err)
		return nil, err
	}

	resChannelInfos := make(map[string]*channel_play_index.ExitRoomInfo)
	for i, cmd := range cmds {
		uid := byUids[i]
		res, ok := cmd.(*redis.StringCmd)
		if !ok {
			log.ErrorWithCtx(ctx, "cmd to StringCmd err, uid:%d, cmd:%v", uid, cmd)
			continue
		}
		if res.Val() != "" {
			exitChannelInfo := &channel_play_index.ExitRoomInfo{}
			err := json.Unmarshal([]byte(res.Val()), exitChannelInfo)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetInSameRoomUidByUids json.Unmarshal err:%+v, res:%v", err, res.Val())
				continue
			}
			uidGroup := r.getUidGroup(exitChannelInfo.GetExitUid(), exitChannelInfo.GetSameRoomUid())
			resChannelInfos[uidGroup] = exitChannelInfo
		}
	}
	return resChannelInfos, nil
}

func (r *RedisDao) getTabRoomKey(tabId uint32) string {
	return fmt.Sprintf("tab_room_%d", tabId)
}

func (r *RedisDao) AddTabRoom(ctx context.Context, tabId, cid uint32) error {
	tabRoomKey := r.getTabRoomKey(tabId)
	err := r.playCli.SAdd(ctx, tabRoomKey, cid).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddTabRoom SAdd err:%v, tabRoomKey:%s, cid:%d", err, tabRoomKey, cid)
		return err
	}
	return nil
}

func (r *RedisDao) DelTabRoom(ctx context.Context, tabId, cid uint32) error {
	tabRoomKey := r.getTabRoomKey(tabId)
	err := r.playCli.SRem(ctx, tabRoomKey, cid).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DelTabRoom SRem err:%v, tabRoomKey:%s, cid:%d", err, tabRoomKey, cid)
		return err
	}
	return nil
}

func (r *RedisDao) GetTabsRoomCount(ctx context.Context, tabIds []uint32) (map[uint32]int64, error) {
	cmds, err := r.playCli.Pipelined(ctx, func(pl redis.Pipeliner) error {
		for _, tabId := range tabIds {
			tabRoomKey := r.getTabRoomKey(tabId)
			pl.SCard(ctx, tabRoomKey)
		}
		return nil
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "GetTabsRoomCount redis.Pipelined SCARD failed, err:%v", err)
		return nil, err
	}

	tabRoomCountMap := make(map[uint32]int64, len(tabIds))
	for i, cmd := range cmds {
		if cmd.Err() != nil && cmd.Err() != redis.Nil {
			log.ErrorWithCtx(ctx, "GetTabsRoomCount cmd failed in pipeline, err:%v", cmd.Err())
			return nil, cmd.Err()
		}
		tabId := tabIds[i]
		res, ok := cmd.(*redis.IntCmd)
		if !ok {
			log.ErrorWithCtx(ctx, "cmd to IntCmd err, tabId:%d, cmd:%v", tabId, cmd)
			continue
		}
		tabRoomCountMap[tabId] = res.Val()
	}
	return tabRoomCountMap, nil
}

func (r *RedisDao) getUserDayRoomDurationKey(uid uint32) string {
	nowDate := time.Now().Format("20060102")
	return fmt.Sprintf("u_day_stay_room:%s:%d", nowDate, uid)
}

func (r *RedisDao) IncrUserDayRoomDuration(ctx context.Context, uid uint32, duration int64) error {
	dayDurationKey := r.getUserDayRoomDurationKey(uid)
	v, err := r.playCli.IncrBy(ctx, dayDurationKey, duration).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "IncrUserDayRoomDuration IncrBy err:%v, dayDurationKey:%s, duration:%d", err, dayDurationKey, duration)
		return err
	}
	if v == duration {
		_, err = r.playCli.Expire(ctx, dayDurationKey, 24*time.Hour).Result()
		if err != nil {
			log.ErrorWithCtx(ctx, "IncrUserDayRoomDuration Expire err:%v, dayDurationKey:%s, duration:%d", err, dayDurationKey, duration)
		}
	}
	return nil
}

func (r *RedisDao) GetUserDayRoomDuration(ctx context.Context, uid uint32) (int64, error) {
	dayDurationKey := r.getUserDayRoomDurationKey(uid)
	c, err := r.playCli.Get(ctx, dayDurationKey).Int64()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return 0, nil
		}
		return 0, err
	}
	return c, nil
}
