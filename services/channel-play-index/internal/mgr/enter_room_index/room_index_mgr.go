package enter_room_index

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/publisher"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	pkgConfig "golang.52tt.com/pkg/config"
	eventlink "golang.52tt.com/pkg/kafka_eventlink"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	channel_play_index "golang.52tt.com/protocol/services/channel-play-index"
	"golang.52tt.com/protocol/services/channel-play-index/event"
	tabCache "golang.52tt.com/services/channel-play-index/internal/cache"
	"golang.52tt.com/services/channel-play-index/internal/mgr/enter_room_index/cache"
	"strconv"
	"time"
)

type RoomIndexMgr struct {
	cache    *cache.RedisDao
	tabCache *tabCache.TabCache

	roomStatPub eventlink.IELKafkaProducer
}

func NewRoomIndexMgr(redisClient redis.Client, playRedisClient redis.Client, tabCache *tabCache.TabCache,
	gameRoomStatKafkaConfig *pkgConfig.KafkaConfig) (*RoomIndexMgr, error) {
	cache_ := cache.NewRedisDao(redisClient, playRedisClient)

	// room stat pub 生产房间统计数据
	roomStatPub, err := eventlink.NewEventLinkKafkaProducer(gameRoomStatKafkaConfig.ClientID,
		gameRoomStatKafkaConfig.Topics, gameRoomStatKafkaConfig.BrokerList())
	if err != nil {
		return nil, err
	}

	return &RoomIndexMgr{
		cache:       cache_,
		tabCache:    tabCache,
		roomStatPub: roomStatPub,
	}, nil
}

func (r *RoomIndexMgr) AddEnterRoomBeginTime(ctx context.Context, cid, uid uint32, enterRoomTime int64) error {
	err := r.cache.AddEnterRoomBeginTime(ctx, cid, uid, enterRoomTime)
	if err != nil {
		return err
	}
	return nil
}

func (r *RoomIndexMgr) DelEnterRoomBeginTime(ctx context.Context, cid, uid uint32) error {
	err := r.cache.DelEnterRoomBeginTime(ctx, cid, uid)
	if err != nil {
		return err
	}
	return nil
}

func (r *RoomIndexMgr) AddRoomPlayCnt(ctx context.Context, tabId, cid, uid uint32, happenTime int64) error {
	if tabCacheInfo := r.tabCache.GetTabInfoCacheById(tabId); tabCacheInfo != nil {
		var count int64
		if topic_channel.CategoryType(tabCacheInfo.GetCategoryMapping()) == topic_channel.CategoryType_Gangup_type {
			err := r.cache.AddEnterTabId(ctx, cid, uid, tabId)
			if err != nil {
				return err
			}

			count, err = r.cache.AddEnterRoomByPlayCnt(ctx, tabId, uid, happenTime)
			if err != nil {
				log.ErrorWithCtx(ctx, "AddRoomPlayCnt err:%v, tabId:%d, uid:%d", err, tabId, uid)
				return err
			}

		} else {
			err := r.cache.DelEnterTabId(ctx, cid, uid)
			if err != nil {
				return err
			}
		}

		// 发送房间统计数据，count为用户当天进入tabId对应玩法的开黑房次数，如果是非开黑房，则count为0
		_ = r.roomStatInfoPub(ctx, uid, tabId, count)
	}
	return nil
}

func (r *RoomIndexMgr) GetRoomPlayCnt(ctx context.Context, cid, uid uint32) (uint32, int64, error) {
	nowTime := time.Now().UnixMilli()
	tabId, err := r.cache.GetEnterRoomTabId(ctx, cid, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRoomPlayCnt err:%v", err)
		return tabId, 0, err
	}
	cnt, err := r.cache.GetEnterRoomByPlayCnt(ctx, tabId, uid, nowTime)
	return tabId, cnt, err
}

func (r *RoomIndexMgr) AddRoomPlayDuration(ctx context.Context, cid, uid uint32, durationTime, happenTime int64) error {
	tabId, err := r.cache.GetEnterRoomTabId(ctx, cid, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRoomPlayCnt err:%v", err)
		return err
	}
	if tabCacheInfo := r.tabCache.GetTabInfoCacheById(tabId); tabCacheInfo != nil {
		if topic_channel.CategoryType(tabCacheInfo.GetCategoryMapping()) == topic_channel.CategoryType_Gangup_type {
			return r.cache.AddEnterRoomByPlayDuration(ctx, tabId, uid, durationTime, happenTime)
		}
	} else {
		log.InfoWithCtx(ctx, "AddRoomPlayDuration no tabCache, tabId:%d", tabId)
		//return r.cache.AddEnterRoomByPlayDuration(ctx, tabId, uid, durationTime, happenTime)
	}
	return nil
}

func (r *RoomIndexMgr) GetRoomPlayDuration(ctx context.Context, cid, uid uint32) (int64, error) {
	nowTime := time.Now().UnixMilli()
	tabId, err := r.cache.GetEnterRoomTabId(ctx, cid, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRoomPlayCnt err:%v", err)
		return 0, err
	}
	return r.cache.GetEnterRoomByPlayDuration(ctx, tabId, uid, nowTime)
}

/*func (r *RoomIndexMgr) AddUserInRoomInfo(ctx context.Context, channelId, uid uint32, enterRoomTime, happenTime int64) error {
	if enterRoomTime == 0 {
		return nil
	}
	if duration := happenTime - enterRoomTime; duration > 0 {
		err := r.cache.AddUserInRoomInfo(ctx, channelId, uid, enterRoomTime, happenTime)
		if err != nil {
			return err
		}
	}
	return nil
}

func (r *RoomIndexMgr) GetUserInRoomInfo(ctx context.Context, uids []uint32) (*pb.GetUserInRoomInfoResp, error) {
	nowTime := time.Now().UnixMilli()
	return r.cache.BatchGetUserInRoomInfo(ctx, uids, nowTime)
}*/

func (r *RoomIndexMgr) GetRoomUids(ctx context.Context, channelId uint32) ([]uint32, error) {
	uids, err := r.cache.GetChannelUids(ctx, channelId)
	if err != nil {
		return uids, err
	}
	return uids, nil
}

func (r *RoomIndexMgr) AddRoomUidIndex(ctx context.Context, channelId, uid uint32) error {
	err := r.cache.AddChannelUid(ctx, channelId, uid)
	if err != nil {
		return err
	}
	return nil
}

func (r *RoomIndexMgr) DelRoomUid(ctx context.Context, channelId, exitUid, remainUidCnt uint32) error {
	err := r.cache.DelChannelUid(ctx, channelId, exitUid)
	if err != nil {
		return err
	}

	roomUids, err := r.cache.GetChannelUids(ctx, channelId)
	if err != nil {
		return err
	}

	if len(roomUids) != int(remainUidCnt) {
		//r.cache.DelChannelUids(ctx, channelId)
		log.InfoWithCtx(ctx, "CheckRoomUid Cnt, channelId:%d, nowUidCnt:%d, remainUidCnt:%d", channelId, len(roomUids), remainUidCnt)
	}
	return nil
}

func (r *RoomIndexMgr) DelRoomUidIndex(ctx context.Context, channelId, exitUid, tabId uint32, happenTime int64) error {
	roomUids, err := r.cache.GetChannelUids(ctx, channelId)
	if err != nil {
		return err
	}
	if len(roomUids) >= 30 {
		log.WarnWithCtx(ctx, "GetChannelUids uids cnt exceed:%d, channelId:%d, uid:%d, tabId:%d", len(roomUids), channelId, exitUid, tabId)
		roomUids = roomUids[:30]
	} else if len(roomUids) == 0 {
		return nil
	}
	var allUids = make([]uint32, 0, len(roomUids)+1)
	allUids = append(allUids, exitUid)
	allUids = append(allUids, roomUids...)
	uid2BeginTimeMap, err := r.cache.GetEnterRoomBeginTimeByUids(ctx, channelId, allUids)
	if err != nil {
		return err
	}
	exitBeginTime := uid2BeginTimeMap[exitUid]
	if exitBeginTime == 0 { //如果退房用户没有进房时间，直接返回
		log.WarnWithCtx(ctx, "DelRoomUidIndex err, exitUid:%d no enter time", exitUid)
		return nil
	}
	uidBeginTimeList := make([]*cache.UidDurationTime, 0, len(uid2BeginTimeMap))
	for uid, beginTime := range uid2BeginTimeMap {
		if uid == exitUid {
			continue
		}
		var duration int64
		var enterRoomTimeMs int64
		if exitBeginTime > beginTime {
			enterRoomTimeMs = exitBeginTime
			duration = (happenTime - exitBeginTime) / 1000
		} else {
			enterRoomTimeMs = beginTime
			duration = (happenTime - beginTime) / 1000
		}
		if duration <= 0 {
			log.WarnWithCtx(ctx, "DelRoomUidIndex err, exitUid:%d, uid:%d, duration:%d, happenTime:%d, exitBeginTime:%d, beginTime:%d", exitUid, uid, duration, happenTime, exitBeginTime, beginTime)
			continue
		}
		uidBeginTimeList = append(uidBeginTimeList, &cache.UidDurationTime{Uid: uid, DurationTime: duration, EnterRoomTime: enterRoomTimeMs})
		log.DebugWithCtx(ctx, "together room, uid:%d, duration:%d, happenTime:%d, exitBeginTime:%d, beginTime:%d", uid, duration, happenTime, exitBeginTime, beginTime)
	}
	err = r.cache.AddInSameRoomUid(ctx, exitUid, uidBeginTimeList, happenTime, channelId, tabId)
	if err != nil {
		return err
	}
	return nil
}

func (r *RoomIndexMgr) GetInSameRoomUids(ctx context.Context, uid uint32, checkUids []uint32) (map[string]*channel_play_index.ExitRoomInfo, error) {
	nowTime := time.Now().UnixMilli()
	exitRoomMap, err := r.cache.GetInSameRoomUids(ctx, uid, checkUids, nowTime)
	if err != nil {
		return nil, err
	}
	exitRoomMapReverse, err := r.cache.GetInSameRoomUidByUids(ctx, uid, checkUids, nowTime)
	if err != nil {
		return nil, err
	}

	for uidGroup, exitRoomInfo := range exitRoomMapReverse {
		exitRoomMap[uidGroup] = exitRoomInfo

	}

	return exitRoomMap, nil
}

func (r *RoomIndexMgr) ClearCache(ctx context.Context, happenTime int64) {
	r.cache.DelEnterRoomByPlayCntKey(ctx, happenTime)
	r.cache.DelEnterRoomByPlayDurationKey(ctx, happenTime)
}

func (r *RoomIndexMgr) roomStatInfoPub(ctx context.Context, uid, tabId uint32, count int64) error {
	log.DebugWithCtx(ctx, "roomStatInfoPub, uid:%d, tabId:%d, count:%d", uid, tabId, count)
	eventData := &event.RoomStatInfoPubEvent{
		Uid:                  uid,
		TabId:                tabId,
		CurDayEnterRoomCount: uint32(count),
	}
	bs, err := proto.Marshal(eventData)
	if err != nil {
		log.ErrorWithCtx(ctx, "RoomStatInfoPub marshal failed, err:%v, eventData:%+v", err, eventData)
		return err
	}
	res := r.roomStatPub.Publish(ctx, &publisher.ProducerMessage{
		Topic: r.roomStatPub.GetTopic(),
		Key:   publisher.StringEncoder(strconv.FormatUint(uint64(eventData.GetUid()), 10)),
		Value: publisher.StringEncoder(bs),
	})
	if res.Err != nil {
		log.ErrorWithCtx(ctx, "RoomStatInfoPub pub failed, err:%v, eventData:%+v", res.Err, eventData)
		return res.Err
	}

	return nil
}
