package event

import (
	"context"
	"github.com/Shopify/sarama"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/channel-msg-api/thirdparty/protocol/app/channel"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	context_info "golang.52tt.com/pkg/context-info"
	protoGrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_channelol"
	"golang.52tt.com/services/channel-play-index/internal/config"
	"golang.52tt.com/services/channel-play-index/internal/mgr/enter_room_index"
	"os"
	"os/signal"
	"runtime/debug"
	"time"
)

type EnterRoomInfoWithCtx struct {
	Ctx           context.Context
	StatQueueInfo *kafka_channelol.ChannelEvent
}
type EnterRoomSub struct {
	Sub               subscriber.Subscriber
	EnterPlayCntQueue chan *EnterRoomInfoWithCtx
	StayInUgcQueue    chan *kafka_channelol.ChannelEvent
	TabRoomCntQueue   chan *kafka_channelol.ChannelEvent
	goroutinesNum     int
	roomIndexMgr      *enter_room_index.RoomIndexMgr
}

func NewEnterRoomSubscriber(ctx context.Context, cfg *conf.StartConfig, playIndexMgr *enter_room_index.RoomIndexMgr) (*EnterRoomSub, error) {
	goroutinesNum := cfg.GoroutineNum
	if goroutinesNum == 0 {
		goroutinesNum = 10
	}
	chanLen := cfg.ChannelLen
	if chanLen == 0 {
		chanLen = 10
	}
	newSubscriber := &EnterRoomSub{
		EnterPlayCntQueue: make(chan *EnterRoomInfoWithCtx, chanLen),
		StayInUgcQueue:    make(chan *kafka_channelol.ChannelEvent, chanLen),
		TabRoomCntQueue:   make(chan *kafka_channelol.ChannelEvent, chanLen),
		goroutinesNum:     goroutinesNum,
		roomIndexMgr:      playIndexMgr,
	}
	kafkaConf := kafka.DefaultConfig()
	kafkaConf.ClientID = cfg.SimpleChannelEvKafkaConfig.ClientID
	kafkaConf.Consumer.Offsets.Initial = sarama.OffsetNewest
	kafkaConf.Consumer.Return.Errors = true
	kafkaSub, err := kafka.NewSubscriber(cfg.SimpleChannelEvKafkaConfig.BrokerList(), kafkaConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewSubscriber channel kafka error: %v", err)
		return nil, err
	}
	if nil != cfg.SimpleChannelEvKafkaConfig {
		err := kafkaSub.SubscribeContext(cfg.SimpleChannelEvKafkaConfig.GroupID, cfg.SimpleChannelEvKafkaConfig.TopicList(), subscriber.ProcessorContextFunc(newSubscriber.onChannelEvent))
		if err != nil {
			log.ErrorWithCtx(ctx, "Subscribe channel kafka error: %v", err)
			return nil, err
		} else {
			log.InfoWithCtx(ctx, "Subscribe channel kafka suc:%v", cfg.SimpleChannelEvKafkaConfig)
		}
	}
	newSubscriber.Sub = kafkaSub

	for i := 0; i < goroutinesNum; i++ {
		go newSubscriber.procEventInChan()
		go newSubscriber.procEventStayInUgc()
		go newSubscriber.procEventTabRoomCnt()
	}
	return newSubscriber, nil
}

func (m *EnterRoomSub) onChannelEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (err error, retry bool) {
	defer func() {
		if e := recover(); e != nil {
			log.Errorf("errored to onChannelEvent err:%v, stack: %v", e, string(debug.Stack()))
		}
	}()
	//if msg.Topic != "simple_channel_ev" {
	//	log.DebugWithCtx(ctx, "expect topic %s, got %s", "simple_channel_ev", msg.Topic)
	//	return nil, false
	//}
	eventInfo := &kafka_channelol.ChannelEvent{}
	err = proto.Unmarshal(msg.Value, eventInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "onChannelEvent Unmarshal error: %v", err)
		return nil, false
	}
	switch eventInfo.GetEvent().(type) {
	case *kafka_channelol.ChannelEvent_EnterEvent:
		if channel.ChannelType(eventInfo.GetEnterEvent().GetBaseInfo().GetChannelType()) != channel.ChannelType_USER_CHANNEL_TYPE {
			return nil, false
		}
	case *kafka_channelol.ChannelEvent_LeaveEvent:
		if channel.ChannelType(eventInfo.GetLeaveEvent().GetBaseInfo().GetChannelType()) != channel.ChannelType_USER_CHANNEL_TYPE {
			return nil, false
		}
	}

	asyncCtx := protoGrpc.NewContextWithInfo(ctx)
	m.pushMsgToQueue(&EnterRoomInfoWithCtx{
		Ctx:           asyncCtx,
		StatQueueInfo: eventInfo,
	})
	m.pushMsgToStayInUgcQueue(eventInfo)
	m.pushMsgToTabRoomCntQueue(eventInfo)
	return
}

func (m *EnterRoomSub) pushMsgToQueue(enterOpt *EnterRoomInfoWithCtx) {

	select {
	case m.EnterPlayCntQueue <- enterOpt:
	default:
		log.Errorf("userReceptionQueue is full, cannot push to queue, enterOpt:%+v", enterOpt)
		//case <-timer.C:
		//    log.Warnf("pushMsgToQueue push user_reception message to queue timeout, uid:%d, cid:%d", userInfo.GetUid(), cid)
	}
}

func (m *EnterRoomSub) pushMsgToStayInUgcQueue(enterOpt *kafka_channelol.ChannelEvent) {

	select {
	case m.StayInUgcQueue <- enterOpt:
	default:
		log.Errorf("StayInUgcQueue is full, cannot push to queue, enterOpt:%+v", enterOpt)
		//case <-timer.C:
		//    log.Warnf("pushMsgToQueue push user_reception message to queue timeout, uid:%d, cid:%d", userInfo.GetUid(), cid)
	}
}

func (m *EnterRoomSub) pushMsgToTabRoomCntQueue(enterOpt *kafka_channelol.ChannelEvent) {
	select {
	case m.TabRoomCntQueue <- enterOpt:
	default:
		log.Errorf("TabRoomCntQueue is full, cannot push to queue, enterOpt:%+v", enterOpt)
	}
}

func (m *EnterRoomSub) procEventInChan() {
	signals := make(chan os.Signal, 1)
	signal.Notify(signals, os.Interrupt)

	for {
		select {
		case roomEventInfo, ok := <-m.EnterPlayCntQueue:
			if !ok {
				log.Errorf("procEventInChan err:channel broken")
				time.Sleep(10 * time.Second)
				continue
			} else {
				m.handleRoomIndex(roomEventInfo)
			}

		case <-signals:
			log.Warnf("procEventInChan signal notify break")
			continue
		}
	}
}

func (m *EnterRoomSub) handleRoomIndex(enterPlayCntQueueInfo *EnterRoomInfoWithCtx) {
	defer func() {
		if e := recover(); e != nil {
			log.Errorf("errored to onChannelEvent err:%v, stack: %v", e, string(debug.Stack()))
		}
	}()
	ctx, cancel := context.WithTimeout(enterPlayCntQueueInfo.Ctx, 3*time.Second)
	ctx = context_info.GenReqId(ctx)
	switch enterPlayCntQueueInfo.StatQueueInfo.GetEvent().(type) {
	case *kafka_channelol.ChannelEvent_EnterEvent:
		log.InfoWithCtx(ctx, "handleRoomIndex enter event:%+v", enterPlayCntQueueInfo.StatQueueInfo.GetEnterEvent())

		m.roomIndexMgr.AddEnterRoomBeginTime(ctx, enterPlayCntQueueInfo.StatQueueInfo.GetEnterEvent().GetBaseInfo().GetChId(),
			enterPlayCntQueueInfo.StatQueueInfo.GetEnterEvent().GetBaseInfo().GetUid(), int64(enterPlayCntQueueInfo.StatQueueInfo.GetEnterEvent().GetBaseInfo().GetTsMs()))

		m.roomIndexMgr.AddRoomPlayCnt(ctx, enterPlayCntQueueInfo.StatQueueInfo.GetEnterEvent().GetSchemeInfo().GetSchemeId(),
			enterPlayCntQueueInfo.StatQueueInfo.GetEnterEvent().GetBaseInfo().GetChId(), enterPlayCntQueueInfo.StatQueueInfo.GetEnterEvent().GetBaseInfo().GetUid(),
			int64(enterPlayCntQueueInfo.StatQueueInfo.GetEnterEvent().GetBaseInfo().GetTsMs()))

		m.roomIndexMgr.AddRoomUidIndex(ctx, enterPlayCntQueueInfo.StatQueueInfo.GetEnterEvent().GetBaseInfo().GetChId(),
			enterPlayCntQueueInfo.StatQueueInfo.GetEnterEvent().GetBaseInfo().GetUid())

	case *kafka_channelol.ChannelEvent_LeaveEvent:
		log.InfoWithCtx(ctx, "handleRoomIndex leave event:%+v", enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent())
		if enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetLastEnterTs() == 0 || enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetOnlineSecond() == 0 {
			log.InfoWithCtx(ctx, "handleRoomIndex leave event time 0, lastents:%d, onlinetime:%d, leave event:%+v",
				enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetLastEnterTs(), enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetOnlineSecond(),
				enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent())
			m.roomIndexMgr.DelRoomUid(ctx, enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetChId(), enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetUid(), enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetRemainMemberCnt())
			m.roomIndexMgr.DelEnterRoomBeginTime(ctx, enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetChId(),
				enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetUid())
		} else {

			m.roomIndexMgr.DelRoomUid(ctx, enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetChId(), enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetUid(), enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetRemainMemberCnt())

			m.roomIndexMgr.AddRoomPlayDuration(ctx, enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetChId(),
				enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetUid(),
				int64(enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetOnlineSecond()), int64(enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetTsMs()))

			if enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetTsMs() != 0 {
				m.roomIndexMgr.DelRoomUidIndex(ctx, enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetChId(),
					enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetUid(), enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetSchemeInfo().GetSchemeId(),
					int64(enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetTsMs()))
			} else {
				log.ErrorWithCtx(ctx, "DelRoomUidIndex time err, ts:%d", enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetTsMs())
			}
			m.roomIndexMgr.DelEnterRoomBeginTime(ctx, enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetChId(),
				enterPlayCntQueueInfo.StatQueueInfo.GetLeaveEvent().GetBaseInfo().GetUid())
		}
	}
	cancel()
	log.Debugf("procEventInChan ok info:%+v", enterPlayCntQueueInfo.StatQueueInfo)
}

func (m *EnterRoomSub) procEventStayInUgc() {
	signals := make(chan os.Signal, 1)
	signal.Notify(signals, os.Interrupt)

	for {
		select {
		case roomEventInfo, ok := <-m.StayInUgcQueue:
			if !ok {
				log.Errorf("procEventStayInUgc err:channel broken")
				time.Sleep(10 * time.Second)
				continue
			} else {
				m.handleStayInUgc(roomEventInfo)
			}

		case <-signals:
			log.Warnf("procEventStayInUgc signal notify break")
			continue
		}
	}
}

func (m *EnterRoomSub) handleStayInUgc(roomEventInfo *kafka_channelol.ChannelEvent) {
	defer func() {
		if e := recover(); e != nil {
			log.Errorf("errored to handleStayInUgc err:%v, stack: %v", e, string(debug.Stack()))
		}
	}()
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	ctx = context_info.GenReqId(ctx)
	switch roomEventInfo.GetEvent().(type) {
	case *kafka_channelol.ChannelEvent_LeaveEvent:
		err := m.roomIndexMgr.RecordFinishStayInRoomTask(ctx, roomEventInfo.GetLeaveEvent())
		if err != nil {
			log.WarnWithCtx(ctx, "procEventStayInUgc RecordFinishStayInRoomTask event:%s err:%+v",
				roomEventInfo.GetLeaveEvent().String(), err)
		}

		if roomEventInfo.GetLeaveEvent().GetLastEnterTs() > 0 && roomEventInfo.GetLeaveEvent().GetOnlineSecond() > 0 {
			err = m.roomIndexMgr.IncrUserDayRoomDuration(ctx, roomEventInfo.GetLeaveEvent().GetBaseInfo().GetUid(),
				int64(roomEventInfo.GetLeaveEvent().GetOnlineSecond()))
			if err != nil {
				log.WarnWithCtx(ctx, "handleStayInUgc IncrUserDayRoomDuration event:%s err:%+v",
					roomEventInfo.GetLeaveEvent().String(), err)
			}
		}
	}

	log.DebugWithCtx(ctx, "procEventStayInUgc ok roomEventInfo:%s", roomEventInfo.String())
}

func (m *EnterRoomSub) procEventTabRoomCnt() {
	signals := make(chan os.Signal, 1)
	signal.Notify(signals, os.Interrupt)

	for {
		select {
		case roomEventInfo, ok := <-m.TabRoomCntQueue:
			if !ok {
				log.Errorf("procEventTabRoomCnt err:channel broken")
				time.Sleep(10 * time.Second)
				continue
			} else {
				m.handleTabRoomCntIndex(roomEventInfo)
			}

		case <-signals:
			log.Warnf("procEventTabRoomCnt signal notify break")
			continue
		}
	}
}

func (m *EnterRoomSub) handleTabRoomCntIndex(roomEventInfo *kafka_channelol.ChannelEvent) {
	defer func() {
		if e := recover(); e != nil {
			log.Errorf("errored to onChannelEvent err:%v, stack: %v", e, string(debug.Stack()))
		}
	}()
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	ctx = context_info.GenReqId(ctx)
	switch roomEventInfo.GetEvent().(type) {
	case *kafka_channelol.ChannelEvent_EnterEvent:
		enterInfo := roomEventInfo.GetEnterEvent()
		log.InfoWithCtx(ctx, "handleTabRoomCntIndex enter event:%+v", enterInfo)
		m.roomIndexMgr.AddTabRoom(ctx, enterInfo.GetSchemeInfo().GetSchemeId(), enterInfo.GetBaseInfo().GetChId())
	case *kafka_channelol.ChannelEvent_LeaveEvent:
		leaveInfo := roomEventInfo.GetLeaveEvent()
		log.InfoWithCtx(ctx, "handleTabRoomCntIndex leave event:%+v", leaveInfo)
		if leaveInfo.GetBaseInfo().GetRemainMemberCnt() == 0 {
			m.roomIndexMgr.DelTabRoom(ctx, leaveInfo.GetSchemeInfo().GetSchemeId(), leaveInfo.GetBaseInfo().GetChId())
		}
	}
	cancel()
	log.Debugf("handleTabRoomCntIndex ok info:%+v", roomEventInfo)
}
