package internal

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	channel_play_index "golang.52tt.com/protocol/services/channel-play-index"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/services/channel-play-index/internal/cache"
	"golang.52tt.com/services/channel-play-index/internal/config"
	config "golang.52tt.com/services/channel-play-index/internal/config/ttconfig/channel_play_index"
	"golang.52tt.com/services/channel-play-index/internal/event"
	"golang.52tt.com/services/channel-play-index/internal/mgr/content_index"
	"golang.52tt.com/services/channel-play-index/internal/mgr/enter_room_index"
	"golang.52tt.com/services/channel-play-index/internal/mgr/follow_index"
	"golang.52tt.com/services/channel-play-index/internal/mgr/game_disctrict_play_index"
	"golang.52tt.com/services/channel-play-index/internal/mgr/im_chat_index"
	"golang.52tt.com/services/channel-play-index/internal/mgr/new_friend_index"
	"golang.52tt.com/services/channel-play-index/internal/mgr/online_index"
	"golang.52tt.com/services/channel-play-index/internal/mgr/room_publish"
	"time"
)

func NewServer(ctx context.Context, cfg *conf.StartConfig) (*Server, error) {
	log.InfoWithCtx(ctx, "server startup with cfg: %+v", *cfg)
	s := &Server{}
	err := config.InitChannelPlayIndexConfig()
	if err != nil {
		return s, err
	}

	tabCache, err := cache.NewTabCache()
	if err != nil {
		return s, err
	}
	err = tabCache.Init(ctx)
	if err != nil {
		return s, err
	}

	redisClient, err := redisConnect.NewClient(ctx, cfg.ChannelIndexRedisConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer redisConnect.NewClient cfg:%+v err: %v", cfg.ChannelIndexRedisConfig, err)
		return nil, err
	}

	playRedisClient, err := redisConnect.NewClient(ctx, cfg.PlayIndexRedisConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer redisConnect.NewClient cfg:%+v err: %v", cfg.PlayIndexRedisConfig, err)
		return nil, err
	}

	roomIndexMgr, err := enter_room_index.NewRoomIndexMgr(redisClient, playRedisClient, tabCache, cfg.GameRoomStatKafkaConfig)
	if err != nil {
		return nil, err
	}
	s.roomIndexMgr = roomIndexMgr

	roomPublishMgr, err := room_publish.NewRoomPublishMgr(playRedisClient)
	s.roomPublishMgr = roomPublishMgr

	contentIndexMgr, err := content_index.NewContentIndexMgr(playRedisClient)
	if err != nil {
		return nil, err
	}
	s.contentIndexMgr = contentIndexMgr

	s.simpleChannelEvSub, err = event.NewEnterRoomSubscriber(ctx, cfg, roomIndexMgr)
	if err != nil {
		return nil, err
	}

	gameDisctrictMgr, err := game_disctrict_play_index.NewGameDistrictPlayPlayIndexMgr(playRedisClient)
	if err != nil {
		return nil, err
	}
	s.gameDistrictEvSub, err = event.NewGameDistrictSubscriber(ctx, cfg, gameDisctrictMgr)
	if err != nil {
		return nil, err
	}
	s.gameDistrictIndexMgr = gameDisctrictMgr

	s.contentEvSub, err = event.NewContentSub(ctx, cfg, contentIndexMgr)
	if err != nil {
		return nil, err
	}
	s.clearCacheTimer()

	imRedisClient, err := redisConnect.NewClient(ctx, cfg.ImMsgIndexRedisConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer redisConnect.NewClient cfg:%+v err: %v", cfg, err)
		return nil, err
	}
	imChatIndexMgr, err := im_chat_index.NewImChatIndexMgr(imRedisClient)
	if err != nil {
		return nil, err
	}
	s.imMsgEvSub, err = event.NewImMsgSubscriber(ctx, cfg, imChatIndexMgr)
	if err != nil {
		return nil, err
	}
	s.imChatIndexMgr = imChatIndexMgr

	newFriendRedisClient, err := redisConnect.NewClient(ctx, cfg.NewFriendIndexRedisConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer redisConnect.NewClient cfg:%+v err: %v", cfg, err)
		return nil, err
	}
	newFriendIndexMgr, err := new_friend_index.NewNewFriendIndexMgr(newFriendRedisClient)
	if err != nil {
		return nil, err
	}
	s.newFriendEvSub, err = event.NewNewFriendSubscriber(ctx, cfg, newFriendIndexMgr)
	if err != nil {
		return nil, err
	}
	s.newFriendIndexMgr = newFriendIndexMgr

	s.switchChannelTabSub, err = event.NewSwitchChannelTabSubscriber(ctx, cfg, roomIndexMgr)
	if err != nil {
		return nil, err
	}

	followIndexMgr, _ := follow_index.NewFollowIndexMgr(newFriendRedisClient)
	followSub, err := event.NewFollowSubscriber(ctx, cfg.FollowKafkaConfig, followIndexMgr)
	if nil != err {
		log.Errorf("event.NewFollowSubscriber err: %v, config:%+v", err, cfg.FollowKafkaConfig)
		return nil, err
	}
	s.followIndexMgr = followIndexMgr
	s.followSub = followSub

	onlineRedisClient, err := redisConnect.NewClient(ctx, cfg.OnlineIndexRedisConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer redisConnect.NewClient cfg:%+v err: %v", cfg.OnlineIndexRedisConfig, err)
		return nil, err
	}
	onlineIndexMgr, _ := online_index.NewOnlineIndexMgr(onlineRedisClient)
	onlineSub, err := event.NewOnlineSubscriber(ctx, cfg.OnlineKafkaConfig, onlineIndexMgr)
	if nil != err {
		log.Errorf("event.NewOnlineSubscriber err: %v, config:%+v", err, cfg.OnlineKafkaConfig)
		return nil, err
	}
	s.onlineIndexMgr = onlineIndexMgr
	s.onlineSub = onlineSub

	return s, nil
}

type Server struct {
	simpleChannelEvSub   *event.EnterRoomSub
	gameDistrictEvSub    *event.GameDistrictSub
	roomIndexMgr         *enter_room_index.RoomIndexMgr
	roomPublishMgr       *room_publish.RoomPublishMgr
	gameDistrictIndexMgr *game_disctrict_play_index.GameDistrictPlayPlayIndexMgr
	followIndexMgr       *follow_index.FollowIndexMgr
	onlineIndexMgr       *online_index.OnlineIndexMgr

	contentEvSub    *event.ContentSub
	contentIndexMgr *content_index.ContentIndexMgr

	imMsgEvSub     *event.ImMsgSub
	imChatIndexMgr *im_chat_index.ImChatIndexMgr

	newFriendEvSub    *event.NewFriendSub
	newFriendIndexMgr *new_friend_index.NewFriendIndexMgr

	switchChannelTabSub *event.SwitchChannelTabSub

	followSub *event.FollowSubscriber
	onlineSub *event.OnlineSub // 上下线
}

func (s *Server) ShutDown() {
	s.simpleChannelEvSub.Sub.Stop()
	s.gameDistrictEvSub.Sub.Stop()
	s.contentEvSub.Sub.Stop()
	s.imMsgEvSub.Sub.Stop()
	s.newFriendEvSub.Sub.Stop()
	s.switchChannelTabSub.Sub.Stop()
	s.followSub.Stop()
	s.onlineSub.Stop()
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) GetTodayUserPlayInfo(ctx context.Context, req *channel_play_index.GetTodayUserPlayInfoReq) (*channel_play_index.GetTodayUserPlayInfoResp, error) {
	out := &channel_play_index.GetTodayUserPlayInfoResp{}
	for _, playType := range req.GetUserPlayTypes() {
		if playType == channel_play_index.UserPlayType_USER_PLAY_TYPE_ROOM_CNT {
			tabId, roomCnt, err := s.roomIndexMgr.GetRoomPlayCnt(ctx, req.GetCid(), req.GetUid())
			if err != nil {
				return out, err
			}
			out.TabId = tabId
			out.UserInRoomCnt = uint32(roomCnt)
		} else if playType == channel_play_index.UserPlayType_USER_PLAY_TYPE_ROOM_DURATION {
			roomDuration, err := s.roomIndexMgr.GetRoomPlayDuration(ctx, req.GetCid(), req.GetUid())
			if err != nil {
				return out, err
			}
			out.UserInRoomDuration = uint32(roomDuration)
		} else {
			log.ErrorWithCtx(ctx, "GetTodayUserPlayInfo wrong type:%d", playType)
		}

	}
	log.InfoWithCtx(ctx, "GetTodayUserPlayInfo in:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) GetTodayDistrictPlayInfo(ctx context.Context, req *channel_play_index.GetTodayDistrictPlayInfoReq) (*channel_play_index.GetTodayDistrictPlayInfoResp, error) {
	out := &channel_play_index.GetTodayDistrictPlayInfoResp{}
	gameDistrictDuration, err := s.gameDistrictIndexMgr.GetPlayStayDuration(ctx, req.GetTabId(), req.GetUid())
	if err != nil {
		return out, err
	}
	out.UserInGameDistrictDuration = uint32(gameDistrictDuration)
	log.InfoWithCtx(ctx, "GetTodayDistrictPlayInfo in:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) CheckUserInRoom(ctx context.Context, req *channel_play_index.CheckUserInRoomReq) (out *channel_play_index.CheckUserInRoomResp, err error) {
	out = &channel_play_index.CheckUserInRoomResp{}
	exitRoomMap, err := s.roomIndexMgr.GetInSameRoomUids(ctx, req.GetUid(), req.GetCheckUids())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserInRoomInfo err:%v, in:%s", err, req.String())
		return out, err
	}
	out.ExitRoomInfoMap = exitRoomMap
	log.InfoWithCtx(ctx, "GetUserInRoomInfo in:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) IsFinishTask(ctx context.Context, req *channel_play_index.IsFinishTaskReq) (*channel_play_index.IsFinishTaskResp, error) {
	out := &channel_play_index.IsFinishTaskResp{}
	if req.GetTaskType() == channel_play_index.TaskEnum_TASK_ENUM_INVALID || req.GetUid() == 0 {
		return out, nil
	}
	var err error
	out.Finish, err = s.roomIndexMgr.IsTaskFinish(ctx, req.GetUid(), req.GetTaskType())
	if err != nil {
		log.ErrorWithCtx(ctx, "IsFinishTask req:%s err:%+v", req.String(), err)
		return out, err
	}
	return out, nil
}

func (s *Server) clearCacheTimer() {

	go func() {
		ticker := time.NewTicker(time.Second * 600)
		defer func() {
			ticker.Stop()
		}()

		for range ticker.C {
			yesterTime := time.Now().AddDate(0, 0, -1)
			yesterTimeTs := yesterTime.Unix()
			yesterTimeMs := yesterTime.UnixMilli()
			ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
			s.gameDistrictIndexMgr.ClearCache(ctx, yesterTimeTs)
			s.roomIndexMgr.ClearCache(ctx, yesterTimeMs)

			yesterBeforeTime := time.Now().AddDate(0, 0, -3).Unix()
			s.roomPublishMgr.ClearCache(ctx, yesterBeforeTime, uint32(channel_play_index.PublishRecordType_CATE_TYPE_KAIHEI))
			log.InfoWithCtx(ctx, "clearCacheTimer yesterTime:%d", yesterBeforeTime)
			cancel()
		}
	}()
}

func (s *Server) CheckUserTodaySendMsg(ctx context.Context, req *channel_play_index.CheckUserTodaySendMsgReq) (out *channel_play_index.CheckUserTodaySendMsgResp, err error) {
	out = &channel_play_index.CheckUserTodaySendMsgResp{}
	uids, err := s.imChatIndexMgr.GetTodaySendMsgUids(ctx, req.GetUid(), req.GetCheckUids())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckUserTodaySendMsg err:%v, in:%s", err, req.String())
		return out, err
	}
	out.ExistUids = uids
	log.InfoWithCtx(ctx, "CheckUserTodaySendMsg in:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) GetUserTodayChatUids(ctx context.Context, req *channel_play_index.GetUserTodayChatUidsReq) (out *channel_play_index.GetUserTodayChatUidsResp, err error) {
	out = &channel_play_index.GetUserTodayChatUidsResp{}
	uids, err := s.imChatIndexMgr.GetTodayChatUids(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserTodayChatUids err:%v, in:%s", err, req.String())
		return out, err
	}
	out.ChatUids = uids
	log.InfoWithCtx(ctx, "GetUserTodayChatUids in:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) IsUserTodayAddNewFriend(ctx context.Context, req *channel_play_index.IsUserTodayAddNewFriendReq) (out *channel_play_index.IsUserTodayAddNewFriendResp, err error) {
	out = &channel_play_index.IsUserTodayAddNewFriendResp{}
	result, err := s.newFriendIndexMgr.IsUserTodayAddNewFriend(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "IsUserTodayAddNewFriend err:%v, in:%s", err, req.String())
		return out, err
	}
	out.IsAddNewFriend = result
	log.InfoWithCtx(ctx, "IsUserTodayAddNewFriend in:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) GetTabsRoomCount(ctx context.Context, req *channel_play_index.GetTabsRoomCountReq) (*channel_play_index.GetTabsRoomCountResp, error) {
	out := &channel_play_index.GetTabsRoomCountResp{}
	countMap, err := s.roomIndexMgr.GetTabsRoomCount(ctx, req.GetTabIds())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTabsRoomCount err:%v, in:%s", err, req.String())
		return out, err
	}
	out.CountMap = countMap
	log.InfoWithCtx(ctx, "GetTabsRoomCount in:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) SetPublishTabCntByOneDay(ctx context.Context, req *channel_play_index.SetPublishTabCntByOneDayReq) (*channel_play_index.SetPublishTabCntByOneDayResp, error) {
	out := &channel_play_index.SetPublishTabCntByOneDayResp{}
	err := s.roomPublishMgr.AddTabCntByOneDay(ctx, req.GetTabId(), uint32(req.GetPublishType()), req.GetPublishTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddTabCntByOneDay err:%v, in:%s", err, req.String())
		return out, err
	}
	log.InfoWithCtx(ctx, "SetPublishTabCntByOneDay in:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) GetPublishTabCntList(ctx context.Context, req *channel_play_index.GetPublishTabCntListReq) (*channel_play_index.GetPublishTabCntListResp, error) {
	out := &channel_play_index.GetPublishTabCntListResp{}
	tabIds, err := s.roomPublishMgr.GetTabCntByOneDay(ctx, uint32(req.GetPublishType()), req.GetLimitCnt(), req.GetQueryTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTabCntByOneDay err:%v, in:%s", err, req.String())
		return out, err
	}
	out.TabIds = tabIds
	log.InfoWithCtx(ctx, "GetPublishTabCntByOneDay in:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) GetUserDayRoomDuration(ctx context.Context, req *channel_play_index.GetUserDayRoomDurationReq) (*channel_play_index.GetUserDayRoomDurationResp, error) {
	out := &channel_play_index.GetUserDayRoomDurationResp{}
	duration, err := s.roomIndexMgr.GetUserDayRoomDuration(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserDayRoomDuration err:%v, in:%s", err, req.String())
		return out, err
	}
	out.RoomDuration = duration
	log.InfoWithCtx(ctx, "GetUserDayRoomDuration in:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) GetUserDayOnlineDuration(ctx context.Context, req *channel_play_index.GetUserDayOnlineDurationReq) (*channel_play_index.GetUserDayOnlineDurationResp, error) {
	out := &channel_play_index.GetUserDayOnlineDurationResp{}
	duration, err := s.onlineIndexMgr.GetUserDayOnlineDuration(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserDayOnlineDuration err:%v, in:%s", err, req.String())
		return out, err
	}
	out.OnlineDuration = duration
	log.InfoWithCtx(ctx, "GetUserDayOnlineDuration in:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) IsTodayHasFollowOrBeFollow(ctx context.Context, req *channel_play_index.IsTodayHasFollowOrBeFollowReq) (*channel_play_index.IsTodayHasFollowOrBeFollowResp, error) {
	out := &channel_play_index.IsTodayHasFollowOrBeFollowResp{}
	followStatus, err := s.followIndexMgr.IsTodayHasFollowOrBeFollow(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "IsTodayHasFollowOrBeFollow err:%v, in:%s", err, req.String())
		return out, err
	}
	out.FollowStatus = followStatus
	log.InfoWithCtx(ctx, "IsTodayHasFollowOrBeFollow in:%s, out:%s", req.String(), out.String())
	return out, nil
}

func (s *Server) GetRoomUids(ctx context.Context,
	request *channel_play_index.GetRoomUidsRequest) (out *channel_play_index.GetRoomUidsResponse, err error) {
	out = &channel_play_index.GetRoomUidsResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "GetRoomUids err:%v, in:%s", err, request.String())
		}
		log.InfoWithCtx(ctx, "GetRoomUids in:%s, out:%s", request.String(), out.String())
	}()
	uids, err := s.roomIndexMgr.GetRoomUids(ctx, request.GetChannelId())
	if err != nil {
		return out, err
	}
	out.Uids = uids
	return out, nil
}
