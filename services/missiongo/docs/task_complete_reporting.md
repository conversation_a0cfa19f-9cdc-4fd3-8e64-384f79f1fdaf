# 任务完成事件上报功能

## 概述

本文档描述了任务系统中新增的任务完成事件上报功能，该功能通过 Bylink 平台上报用户任务完成的相关数据，用于数据分析和业务监控。

## 功能特性

### 上报字段

任务完成事件包含以下字段：

| 字段名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| app_type | STRING | 应用ID | "missiongo" |
| uid | BIGINT | 用户UID | 12345 |
| task_id | STRING | 任务ID | "1001" |
| task_name | STRING | 任务名称 | "每日签到" |
| group_id | STRING | 群组groupid | "10" |
| group_name | STRING | 群组名称 | "每日任务" |
| exp | STRING | 增加的经验分值 | "100" |
| red | STRING | 增加的红钻值 | "50" |

### 事件名称

- **事件名**: `task_complete`
- **用途**: 记录用户完成任务的行为数据

## API 接口

### BylinkReporter 接口

```go
type BylinkReporter interface {
    ReportOnCollectBonus(ctx context.Context, uid, appId, marketId uint32, mission entity.Mission, bonus pb.MissionBonus)
    ReportOnTaskComplete(ctx context.Context, uid, appId, marketId uint32, mission entity.Mission, exp, red string)
}
```

### 实现方法

#### ReportOnTaskComplete

```go
func (reporter *bylinkReporter) ReportOnTaskComplete(ctx context.Context, uid, appId, marketId uint32, mission entity.Mission, exp, red string)
```

**参数说明:**
- `ctx`: 上下文
- `uid`: 用户ID
- `appId`: 应用ID
- `marketId`: 市场ID
- `mission`: 任务实体对象
- `exp`: 经验值奖励（字符串格式）
- `red`: 红钻奖励（字符串格式）

## 使用示例

### 基础使用

```go
// 创建上报器
reporter := report.NewBylinkReporter()

// 创建任务信息
mission := entity.Mission{
    ID:   1001,
    Name: "每日签到",
    Group: entity.MissionGroup{
        ID:   10,
        Name: "每日任务",
    },
}

// 上报任务完成事件
ctx := context.Background()
uid := uint32(12345)
appId := uint32(1)
marketId := uint32(1)
exp := "100"  // 经验值奖励
red := "50"   // 红钻奖励

reporter.ReportOnTaskComplete(ctx, uid, appId, marketId, mission, exp, red)
```

### 在任务完成流程中使用

```go
func (s *Server) onCollectedMissionBonus(ctx context.Context, req *pb.CollectMissionBonusReq, mission entity.Mission, bonus pb.MissionBonus) {
    asyncCtx, asyncCancel := context.WithTimeout(metadata.NewContext(ctx), 3*time.Second)
    go func() {
        defer asyncCancel()

        // ... 其他处理逻辑 ...

        // 上报领取奖励事件
        s.bylinkReporter.ReportOnCollectBonus(asyncCtx, req.GetUid(), req.GetAppId(), req.GetMarketId(), mission, bonus)
        
        // 上报任务完成事件
        expStr := fmt.Sprintf("%d", bonus.GetExperience())
        redStr := fmt.Sprintf("%d", bonus.GetRedDiamonds())
        s.bylinkReporter.ReportOnTaskComplete(asyncCtx, req.GetUid(), req.GetAppId(), req.GetMarketId(), mission, expStr, redStr)
    }()
}
```

### 公共方法使用

```go
// 使用 Server 提供的公共方法
err := server.ReportTaskCompleteEvent(ctx, uid, appId, marketId, missionID, "100", "50")
if err != nil {
    log.ErrorWithCtx(ctx, "Failed to report task complete event: %v", err)
}
```

## 最佳实践

### 1. 异步上报

建议在异步协程中进行上报，避免阻塞主业务流程：

```go
go func() {
    defer func() {
        if r := recover(); r != nil {
            log.ErrorWithCtx(ctx, "ReportOnTaskComplete panic: %v", r)
        }
    }()
    
    reporter.ReportOnTaskComplete(ctx, uid, appId, marketId, mission, exp, red)
}()
```

### 2. 错误处理

上报失败不应影响主业务逻辑：

```go
func safeReportTaskComplete(reporter BylinkReporter, ctx context.Context, uid, appId, marketId uint32, mission entity.Mission, exp, red string) {
    defer func() {
        if r := recover(); r != nil {
            log.ErrorWithCtx(ctx, "ReportOnTaskComplete panic: %v", r)
        }
    }()
    
    reporter.ReportOnTaskComplete(ctx, uid, appId, marketId, mission, exp, red)
}
```

### 3. 数据验证

确保上报数据的有效性：

```go
func validateReportData(uid uint32, mission entity.Mission, exp, red string) error {
    if uid == 0 {
        return fmt.Errorf("invalid uid: %d", uid)
    }
    
    if !mission.Valid() {
        return fmt.Errorf("invalid mission: %+v", mission)
    }
    
    if exp == "" || red == "" {
        return fmt.Errorf("exp and red cannot be empty")
    }
    
    return nil
}
```

## 监控和调试

### 日志记录

上报过程会记录详细日志：

```
INFO ReportOnTaskComplete data: map[app_type:missiongo uid:12345 task_id:1001 task_name:每日签到 group_id:10 group_name:每日任务 exp:100 red:50]
```

### 错误日志

上报失败时的错误日志：

```
ERROR ReportOnTaskComplete uid(12345) mission(1001) err: bylink track failed
```

## 注意事项

1. **性能影响**: 上报操作应在异步协程中执行，避免影响主业务性能
2. **数据格式**: exp 和 red 参数使用字符串格式，便于处理大数值
3. **错误处理**: 上报失败不应中断主业务流程
4. **数据一致性**: 确保上报的任务信息与实际业务数据一致
5. **频率控制**: 避免过于频繁的上报调用

## 相关文件

- `services/missiongo/internal/report/bylink.go` - 上报实现
- `services/missiongo/internal/report/report.go` - 接口定义
- `services/missiongo/internal/report/bylink_test.go` - 测试用例
- `services/missiongo/internal/server.go` - 使用示例
