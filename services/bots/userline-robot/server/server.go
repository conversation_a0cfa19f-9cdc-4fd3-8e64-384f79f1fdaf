package server

import (
	"context"
	"encoding/json"
	"github.com/gin-gonic/gin"
	sdkginext "github.com/larksuite/oapi-sdk-gin"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkevent "github.com/larksuite/oapi-sdk-go/v3/event"
	"github.com/larksuite/oapi-sdk-go/v3/event/dispatcher"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	channel_play_logic "golang.52tt.com/clients/channel-paly-logic"
	game_card "golang.52tt.com/clients/game-card"
	"golang.52tt.com/clients/topic-channel-logic"
	"golang.52tt.com/clients/topic-channel/tab"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
	"golang.52tt.com/services/bots/userline-robot/config"
	"golang.52tt.com/services/bots/userline-robot/feishu"
	"golang.52tt.com/services/bots/userline-robot/mgr"
	"golang.52tt.com/services/bots/userline-robot/model"
	"golang.52tt.com/services/bots/userline-robot/timer"
	"strings"
)

type UserLineRobotServer struct {
	sc      *config.ServiceConfig
	FeiShu  *feishu.FeiShuServer
	manager *mgr.Manager
}

func NewUserLineRobotServer(cfgPath string) (*UserLineRobotServer, error) {
	sc := &config.ServiceConfig{}
	err := sc.Parse(cfgPath)
	if err != nil {
		log.Errorf("Parse config err: %v", err)
		return nil, err
	}
	tabClient, _ := tab.NewClient()
	gameCardCli, _ := game_card.NewConfClient()
	topicChannelLogicClient, _ := topic_channel_logic.NewClient()
	channelPlayLogicClient, _ := channel_play_logic.NewClient()
	rcmdLabelCli, _ := rcmd_channel_label.NewClient(context.Background())

	feishuServer := feishu.NewFeiShuServer(sc.FeiShuConf)

	manager, err := mgr.NewManager(feishuServer, sc.RefreshTabInfoFre, tabClient, rcmdLabelCli, sc.SetUserDeviceStatusBaseUrl)
	if err != nil {
		return nil, err
	}

	_ = manager.RefreshTabInfoCache(context.Background())
	go manager.SyncTabInfo()

	task := timer.NewUGameTabGameCardBindTask(tabClient, gameCardCli)
	go task.DoHandle(context.Background())

	checkTabTask := timer.NewCheckTabCacheTask(tabClient, topicChannelLogicClient, channelPlayLogicClient, feishuServer)
	go checkTabTask.DoHandle(context.Background())

	rcmdLabelTask := timer.NewCheckRcmdLabelTask(tabClient, rcmdLabelCli, feishuServer, manager)
	go rcmdLabelTask.DoHandle(context.Background())

	go timer.StartDailyWorkProcessRemindTask(sc.Environment, sc.DailWorkProcessRemindSwitch)

	s := &UserLineRobotServer{
		sc:      sc,
		FeiShu:  feishuServer,
		manager: manager,
	}
	return s, nil
}

func (s *UserLineRobotServer) Serve() error {
	r := gin.Default()

	// 创建 event 处理器
	eventHandler := dispatcher.NewEventDispatcher(s.FeiShu.Config.VerificationToken, s.FeiShu.Config.EncryptKey).
		OnP2MessageReceiveV1(s.OnReceiveMessage).
		OnP1P2PChatCreatedV1(s.OnChatCreated).
		OnP2ChatMemberBotAddedV1(s.OnGroupAddBot)

	// 注册处理器
	r.POST("/userline-robot/event", sdkginext.NewEventHandlerFunc(eventHandler, larkevent.WithLogLevel(larkcore.LogLevelDebug)))

	return r.Run(s.FeiShu.Config.Port)
}

// 接收信息事件，机器人根据用户输入查询业务数据并返回
func (s *UserLineRobotServer) OnReceiveMessage(ctx context.Context, event *larkim.P2MessageReceiveV1) error {
	chatId := *event.Event.Message.ChatId
	content := *event.Event.Message.Content

	log.DebugWithCtx(ctx, "OnReceiveMessage event receive content: %v from %v", content, chatId)

	// 1. 解析content，获取命令
	msgContent := &model.ReceiveMessageContent{}
	err := json.Unmarshal([]byte(content), msgContent)
	if err != nil {
		log.ErrorWithCtx(ctx, "Unmarshal content err: %v", err)
		return err
	}

	msgText := strings.Trim(msgContent.Text, " ")
	isPrivateChat := false

	log.InfoWithCtx(ctx, utils.ToJson(event.Event.Message))
	if *event.Event.Message.ChatType == "group" { // 群里@机器人，不支持@多个人
		if len(event.Event.Message.Mentions) != 1 {
			return nil
		}
		// 判断@的对象是不是机器人
		if *event.Event.Message.Mentions[0].Id.OpenId != s.FeiShu.Config.RobotOpenId {
			return nil
		} else {
			index1 := strings.Index(msgContent.Text, " ")
			index2 := strings.Index(msgContent.Text, "@")
			if index1 > index2 { //@机器人 xxx
				msgText = msgContent.Text[index1+1:]
			} else if index1 == -1 { //xxx@机器人
				msgText = msgContent.Text[:index2]
			} else { //xxx @机器人
				msgText = msgContent.Text[:index1]
			}
		}
	} else { // 私聊
		isPrivateChat = true
	}
	msgText = strings.Trim(msgText, " ")

	log.InfoWithCtx(ctx, "receive msg %v from %v, msgText: %v", content, chatId, msgText)

	defer func() {
		if err := recover(); err != nil {
			log.ErrorWithCtx(ctx, "QueryBusiness and SendMessage panic, err: %v", err)
		}
	}()
	// QueryBusiness 业务处理超时会导致接收消息事件超时，会重复触发同一事件
	go func() {
		// 2. 查询业务数据
		respContent, msgType := s.manager.QueryBusiness(ctx, msgText, *event.Event.Sender.SenderId.OpenId, chatId, isPrivateChat)
		log.InfoWithCtx(ctx, "response chatId: %v, content: %v", chatId, respContent)

		if len(respContent) == 0 {
			return
		}
		// 3. 机器人发送消息，返回查询结果
		err = s.FeiShu.SendMessage(ctx, feishu.UserType_chat_id, chatId, msgType, respContent)
		if err != nil {
			log.InfoWithCtx(ctx, "SendMessage failed, chatId: %s, msgType: %s, respConent: %s", chatId, msgType, respContent)
		}
	}()
	return nil
}

// 首次创建会话事件，机器人发送使用介绍给用户
func (s *UserLineRobotServer) OnChatCreated(ctx context.Context, event *larkim.P1P2PChatCreatedV1) error {
	// 发起聊天的用户id
	userId := event.Event.User.UserId
	content := model.IntroduceMsg

	log.DebugWithCtx(ctx, "OnChatCreated event triggered, userId: %v", userId)

	err := s.FeiShu.SendMessage(ctx, feishu.UserType_user_id, userId, feishu.MsgType_interactive, content)
	if err != nil {
		return err
	}
	return nil
}

// 机器人被添加进群聊事件
func (s *UserLineRobotServer) OnGroupAddBot(ctx context.Context, event *larkim.P2ChatMemberBotAddedV1) error {
	chatId := *event.Event.ChatId
	content := model.IntroduceMsg

	log.DebugWithCtx(ctx, "OnGroupAddBot event triggered, chatId: %v", chatId)

	err := s.FeiShu.SendMessage(ctx, feishu.UserType_chat_id, chatId, feishu.MsgType_interactive, content)
	if err != nil {
		return err
	}
	return nil
}
