package rpc

import (
	"context"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_base_api"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_go"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_middle"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_go"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_stat_go"
	"gitlab.ttyuyin.com/tyr/tt-ecosystem/grpc/client/interceptor"
	"golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_chat"
	topic_channel "golang.52tt.com/protocol/services/topic_channel/channel"
	"google.golang.org/grpc"

	account_go "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/account_go/v1"
	"golang.52tt.com/pkg/log"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	aigc_data "golang.52tt.com/protocol/services/rcmd/aigc_apps/common/dao/aigc_data_service"
)

type Clients struct {
	AccountGo              account_go.AccountGoClient
	ChannelGoClient        channel_go.ChannelGoClient
	ChannelOlGoClient      channelol_go.ChannelolGoClient
	ChannelOlStatGoClient  channelol_stat_go.ChannelOlStatGoClient
	AigcAccount            aigc_account.AigcAccountClient
	AigcChatClient         aigc_chat.AigcChatServiceClient
	AigcDataClient         aigc_data.AIGCDataServiceClient
	ChannelBaseApi         channel_base_api.ChannelBaseApiClient
	ChannelMicClient       channel_mic.ChannelMicClient
	ChannelMicMiddleClient channel_mic_middle.ChannelMicMiddleClient
	TopicChannelClient     topic_channel.ChannelClient
}

func NewClients() (*Clients, error) {
	c := new(Clients)

	var (
		err error
		ctx = context.Background()
	)

	c.AccountGo, err = account_go.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients account_go.NewClient err: %v", err)
		return nil, err
	}

	c.AigcAccount, err = aigc_account.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients aigc_account.NewClient err: %v", err)
		return nil, err
	}

	c.AigcChatClient = aigc_chat.MustNewClientTo(ctx, "aigc-chat.rcmd-tt.svc:8000",
		grpc.WithChainUnaryInterceptor(interceptor.NoneStatusAdapterInterceptor()))

	c.ChannelGoClient, err = channel_go.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients channel_go.NewClient err: %v", err)
		return nil, err
	}

	c.ChannelOlGoClient, err = channelol_go.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients channelol_go.NewClient err: %v", err)
		return nil, err
	}

	c.ChannelOlStatGoClient, err = channelol_stat_go.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients channelol_stat_go.NewClient err: %v", err)
		return nil, err
	}

	c.AigcDataClient = aigc_data.MustNewClientTo(ctx, "aigc-data-service.rcmd-tt.svc:8000", grpc.WithChainUnaryInterceptor(interceptor.NoneStatusAdapterInterceptor()))

	c.ChannelBaseApi, err = channel_base_api.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients channel_base_api.NewClient err: %v", err)
		return nil, err
	}

	c.ChannelMicClient, err = channel_mic.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients channel_mic.NewClient err: %v", err)
		return nil, err
	}

	c.ChannelMicMiddleClient, err = channel_mic_middle.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients channel_mic_middle.NewClient err: %v", err)
		return nil, err
	}

	c.TopicChannelClient, err = topic_channel.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients topic_channel.NewClient err: %v", err)
		return nil, err
	}
	return c, nil
}
