package rpc

import (
	"context"
	"gitlab.ttyuyin.com/tyr/tt-ecosystem/grpc/client/interceptor"
	"golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_chat"
	"golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_feature_router"
	user_online "golang.52tt.com/protocol/services/user-online"
	"google.golang.org/grpc"

	"golang.52tt.com/clients/account"
	account_go "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/log"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
)

type Clients struct {
	Account          account.IClient
	AccountGo        account_go.IClient
	AigcAccount      aigc_account.AigcAccountClient
	AigcChatClient   aigc_chat.AigcChatServiceClient
	AigcRouterClient aigc_feature_router.AIGCFeatureRouterClient
	UserOnlineClient user_online.UserOnlineClient
}

func NewClients() (*Clients, error) {
	c := new(Clients)

	var (
		err error
		ctx = context.Background()
	)

	c.Account, err = account.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients aigc_account.NewClient err: %v", err)
		return nil, err
	}

	c.AccountGo, err = account_go.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients account_go.NewClient err: %v", err)
		return nil, err
	}

	c.AigcAccount, err = aigc_account.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients aigc_account.NewClient err: %v", err)
		return nil, err
	}

	c.AigcChatClient = aigc_chat.MustNewClientTo(ctx, "aigc-chat.rcmd-tt.svc:8000",
		grpc.WithChainUnaryInterceptor(interceptor.NoneStatusAdapterInterceptor()))

	c.AigcRouterClient = aigc_feature_router.MustNewClientTo(ctx, "aigc-feature-router.rcmd-tt.svc:8000",
		grpc.WithChainUnaryInterceptor(interceptor.NoneStatusAdapterInterceptor()))

	c.UserOnlineClient, err = user_online.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients user_online.NewClient err: %v", err)
		return nil, err
	}

	return c, nil
}
