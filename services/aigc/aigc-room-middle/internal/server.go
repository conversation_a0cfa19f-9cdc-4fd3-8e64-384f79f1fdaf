package internal

import (
	"context"
	middleware_event "gitlab.ttyuyin.com/tt-infra/middleware/event"
	"gitlab.ttyuyin.com/tyr/x/log"
	"golang.52tt.com/services/aigc/aigc-room-middle/internal/config"
	"golang.52tt.com/services/aigc/aigc-room-middle/internal/event"
	"golang.52tt.com/services/aigc/aigc-room-middle/internal/event/eventlink"
	"golang.52tt.com/services/aigc/aigc-room-middle/internal/rpc"
)

type StartConfig struct {
	// from config file
	EventLink *middleware_event.Options `json:"event_link"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	err := config.InitAigcRoomMiddleConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer InitAigcAccountMiddleConfig err: %v", err)
		return nil, err
	}

	clients, err := rpc.NewClients()
	if err != nil {
		log.Errorf("init rpc clients failed: %v", err)
		return nil, err
	}

	eventBus, err := eventlink.NewEventBus(ctx, cfg.EventLink)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewEventBus cfg(%+v) err: %v", cfg, err)
		return nil, err
	}

	s := &Server{
		clients:  clients,
		eventBus: eventBus,
	}

	if err := eventBus.Subscribe(ctx, event.SubNameEnterRoomEvent, s.onEnterRoomEvent); err != nil {
		log.ErrorWithCtx(ctx, "NewServer Subscribe name(%s) err: %v", event.SubNameEnterRoomEvent, err)
		return nil, err
	}

	return s, nil
}

type Server struct {
	clients  *rpc.Clients
	eventBus event.EventBus
}

func (s *Server) ShutDown() {

}
