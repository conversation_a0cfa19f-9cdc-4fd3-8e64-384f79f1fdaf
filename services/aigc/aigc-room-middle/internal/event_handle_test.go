package internal

import (
	"github.com/golang/mock/gomock"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	"golang.52tt.com/services/aigc/aigc-room-middle/internal/rpc"
)

func setupMockClients(ctrl *gomock.Controller) *rpc.Clients {
	clients := &rpc.Clients{}
	clients.AigcAccount = aigc_account.NewMockAigcAccountClient(ctrl)
	return clients
}
/*
func TestServer_onEnterRoomEvent(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	clients := setupMockClients(ctrl)
	server := &Server{
		clients: clients,
	}

	ctx := context.Background()
	err, retry := server.onEnterRoomEvent(ctx, nil)
	if err != nil {
		log.Errorf("onCommImEvent() with non-send event error = %v", err)
	}
	if retry {
		log.Errorf("onCommImEvent() with non-send event retry = %v, want false", retry)
	}
}
*/