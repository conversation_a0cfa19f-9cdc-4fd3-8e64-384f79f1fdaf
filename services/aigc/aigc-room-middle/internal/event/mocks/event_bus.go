// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-room-middle/internal/event (interfaces: EventBus)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	subscriber "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	event "golang.52tt.com/services/aigc/aigc-room-middle/internal/event"
)

// MockEventBus is a mock of EventBus interface.
type MockEventBus struct {
	ctrl     *gomock.Controller
	recorder *MockEventBusMockRecorder
}

// MockEventBusMockRecorder is the mock recorder for MockEventBus.
type MockEventBusMockRecorder struct {
	mock *MockEventBus
}

// NewMockEventBus creates a new mock instance.
func NewMockEventBus(ctrl *gomock.Controller) *MockEventBus {
	mock := &MockEventBus{ctrl: ctrl}
	mock.recorder = &MockEventBusMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEventBus) EXPECT() *MockEventBusMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockEventBus) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockEventBusMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockEventBus)(nil).Close))
}

// Publish mocks base method.
func (m *MockEventBus) Publish(arg0 context.Context, arg1 event.PubEvent, arg2 string, arg3 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Publish", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// Publish indicates an expected call of Publish.
func (mr *MockEventBusMockRecorder) Publish(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockEventBus)(nil).Publish), arg0, arg1, arg2, arg3)
}

// Subscribe mocks base method.
func (m *MockEventBus) Subscribe(arg0 context.Context, arg1 event.SubName, arg2 subscriber.ProcessorContextFunc) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Subscribe", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Subscribe indicates an expected call of Subscribe.
func (mr *MockEventBusMockRecorder) Subscribe(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Subscribe", reflect.TypeOf((*MockEventBus)(nil).Subscribe), arg0, arg1, arg2)
}
