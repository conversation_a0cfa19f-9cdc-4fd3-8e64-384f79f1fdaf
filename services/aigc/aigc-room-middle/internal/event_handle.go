package internal

import (
	"context"
	accountGoPb "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/account_go/v1"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	context_info "golang.52tt.com/pkg/context-info"
	"golang.52tt.com/pkg/log"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	"golang.52tt.com/protocol/services/channelol-go/event"

	channelGoPb "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_go"
	channelolGoPb "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_go"
	channelolStatGoPb "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_stat_go"
	"golang.52tt.com/services/aigc/aigc-room-middle/internal/config"
	"golang.org/x/exp/rand"

	"golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_chat"
	aigc_data "golang.52tt.com/protocol/services/rcmd/aigc_apps/common/dao/aigc_data_service"
	"regexp"
	"strings"
	"time"
)

func (s *Server) onEnterRoomEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	ctx = context_info.GenReqId(ctx)
	ev := &event.ChannelOLEvent{}
	err := proto.Unmarshal(msg.Value, ev)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommImEvent proto.Unmarshal failed, err:%v, msg:%+v", err, msg)
		return err, false
	}

	// 只处理用户进房事件
	if ev.GetEnterEvent() == nil {
		log.WarnWithCtx(ctx, "not enter event, no need process, event:%+v", ev)
		return nil, false
	}

	// 获取房间信息
	channelInfo, err := s.clients.ChannelGoClient.GetChannelSimpleInfo(ctx, &channelGoPb.GetChannelSimpleInfoReq{ChannelId: ev.GetCid()})
	if err != nil {
		log.ErrorWithCtx(ctx, "filterNotAiAccount GetChannelSimpleInfoByDisplayId failed, err:%v, channelId:%d", err, ev.GetCid())
		return nil, false
	}

	// 如果不是ai房间，不做处理
	isAiRoom := s.checkIsAiRoom(ctx, channelInfo)
	if !isAiRoom {
		log.InfoWithCtx(ctx, "not ai room, cid:%d", ev.GetCid())
		return nil, false
	}

	// 查询房间人数
	channelStatInfo, err := s.clients.ChannelOlStatGoClient.GetChannelOLStatInfo(ctx, &channelolStatGoPb.GetChannelOLStatInfoReq{ChannelId: ev.GetCid()})
	if err != nil {
		log.ErrorWithCtx(ctx, "filterNotAiAccount GetChannelMemberSize failed, err:%v, channelId:%d", err, ev.GetCid())
		return nil, false
	}

	if channelStatInfo.GetStatInfo() == nil {
		log.WarnWithCtx(ctx, "channelStatINfo.GetStatInfo() is nil, channelId:%d", ev.GetCid())
		return nil, false
	}

	// 当前房间除了ai房主，只有刚进房的用户，立即接待
	if channelStatInfo.GetStatInfo().GetMemberCnt() <= 2 {
		err = s.doReceptionImmediately(ctx, channelInfo.GetChannelSimple().GetCreaterUid(), ev.GetUid(), ev.GetCid())
		if err != nil {
			log.ErrorWithCtx(ctx, "onEnterRoomEvent doReceptionImmediately failed, err:%v, aiUid:%d, uid:%d, channelId:%d", err, channelInfo.GetChannelSimple().GetCreaterUid(), ev.GetUid(), ev.GetCid())
		}
		return nil, false
	}

	// 根据规则接待
	err = s.doReceptionWithRule(ctx, channelInfo.GetChannelSimple().GetCreaterUid(), ev.GetUid(), ev.GetCid())
	if err != nil {
		log.ErrorWithCtx(ctx, "onEnterRoomEvent doReceptionWithRule failed, err:%v, aiUid:%d, uid:%d, channelId:%d", err, channelInfo.GetChannelSimple().GetCreaterUid(), ev.GetUid(), ev.GetCid())
	}

	return nil, false
}

// 立即执行接待
func (s *Server) doReceptionImmediately(ctx context.Context, aiUid, uid, channelId uint32) error {
	// 房主非空闲状态不处理
	isAiUserAvailable := s.checkIsAiUserAvailable(ctx, aiUid, channelId)
	if !isAiUserAvailable {
		log.InfoWithCtx(ctx, "ai user not available, aiUid:%d, channelId:%d", aiUid, channelId)
		return nil
	}

	// 如果用户不在房，不需要处理
	uidChannelMap, err := s.clients.ChannelOlGoClient.BatchGetUserChannelId(ctx, &channelolGoPb.BatchGetUserChannelIdReq{
		UidList: []uint32{uid},
	})
	if err != nil || uidChannelMap == nil {
		log.ErrorWithCtx(ctx, "doReceptionImmediately BatchGetUserChannelId failed or nil, err:%v, uid:%d, uidChannelMap:%v", err, uid, uidChannelMap)
		return err
	}

	if uidChannelMap.GetResults() == nil {
		log.InfoWithCtx(ctx, "doReceptionImmediately uidChannelMap.GetResults() is nil, uid:%d", uid)
		return nil
	}

	if cid, ok := uidChannelMap.GetResults()[uid]; !ok || uidChannelMap.GetResults()[uid] != channelId {
		log.InfoWithCtx(ctx, "user not in channel, uid:%d, cid:%d", uid, cid)
		return nil
	}

	return s.sendReceptionMessage(ctx, aiUid, channelId, []uint32{uid})
}

// 根据规则接待
func (s *Server) doReceptionWithRule(ctx context.Context, aiUid, uid, channelId uint32) error {
	// 获取上次接待时间
	lastReceptionTimeResp, err := s.clients.AigcAccount.GetLastReceptionTime(ctx, &aigc_account.GetLastReceptionTimeRequest{
		AiUid:     aiUid,
		ChannelId: channelId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "doReceptionWithRule GetLastReceptionTime failed, err:%v, aiUid:%d, channelId:%d", err, aiUid, channelId)
		return err
	}

	// 如果上次接待时间在指定的时间间隔内，记录等待接待用户
	if lastReceptionTimeResp.GetLastReceptionTime() != 0 && time.Now().Unix()-lastReceptionTimeResp.GetLastReceptionTime() < config.GetAigcRoomMiddleConfig().ReceptionInterval {
		log.InfoWithCtx(ctx, "last reception time less than %v, aiUid:%d, channelId:%d, lastReceptionTime:%d", config.GetAigcRoomMiddleConfig().ReceptionInterval, aiUid, channelId, lastReceptionTimeResp.GetLastReceptionTime())

		// 记录等待接待客户
		return s.recordWaitingReceptionUser(ctx, aiUid, channelId, uid)
	}

	// 执行接待逻辑
	return s.executeReception(ctx, aiUid, uid, channelId)
}

// 发送接待
func (s *Server) executeReception(ctx context.Context, aiUid, uid, channelId uint32) error {
	// 获取需要接待的用户列表
	needReceptionUserList, err := s.getNeedReceptionUser(ctx, aiUid, uid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "getNeedReceptionUser failed, err:%v, aiUid:%d, channelId:%d, uid:%d", err, aiUid, channelId, uid)
		return err
	}

	if len(needReceptionUserList) == 0 {
		log.InfoWithCtx(ctx, "no need reception user, aiUid:%d, channelId:%d", aiUid, channelId)
		return nil
	}

	// 房主非空闲状态不处理
	if !s.checkIsAiUserAvailable(ctx, aiUid, channelId) {
		log.InfoWithCtx(ctx, "ai user not available, aiUid:%d, channelId:%d", aiUid, channelId)
		return nil
	}

	// 获取房间接待配置信息并发送接待消息
	return s.sendReceptionMessage(ctx, aiUid, channelId, needReceptionUserList)
}

// 获取需要接待的用户
func (s *Server) getNeedReceptionUser(ctx context.Context, aiUid, uid, channelId uint32) ([]uint32, error) {
	waitingReceptionUserList := []uint32{uid}

	// 获取等待接待用户列表
	waitingUsersResp, err := s.clients.AigcAccount.GetWaitingReceptionUser(ctx, &aigc_account.GetWaitingReceptionUserRequest{
		AiUid:     aiUid,
		ChannelId: channelId,
	})
	if err != nil {
		log.WarnWithCtx(ctx, "getNeedReceptionUser GetWaitingReceptionUser failed, err:%v, aiUid:%d, channelId:%d", err, aiUid, channelId)
		return nil, err
	}

	if waitingUsersResp.GetWaitingUserList() != nil {
		nowTime := time.Now().Unix()
		for _, user := range waitingUsersResp.GetWaitingUserList() {
			// 过滤掉非前x秒进房的用户
			if user.GetRecordTime() < nowTime-config.GetAigcRoomMiddleConfig().ReceptionPreInterval {
				log.InfoWithCtx(ctx, "waiting user record time too old, uid:%d, recordTime:%d, nowTime:%d", user.GetUid(), user.GetRecordTime(), nowTime)
				continue
			}
			waitingReceptionUserList = append(waitingReceptionUserList, user.GetUid())
		}
	}

	// 过滤掉不在房的用户
	uidChannelMap, err := s.clients.ChannelOlGoClient.BatchGetUserChannelId(ctx, &channelolGoPb.BatchGetUserChannelIdReq{
		UidList: waitingReceptionUserList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getNeedReceptionUser BatchGetUserChannelId failed, err:%v, uidList:%v", err, waitingReceptionUserList)
		return nil, err
	}

	needReceptionUidList := make([]uint32, 0, len(waitingReceptionUserList))
	for userId, cid := range uidChannelMap.GetResults() {
		if cid == channelId {
			needReceptionUidList = append(needReceptionUidList, userId)
		}
	}

	return needReceptionUidList, nil
}

// 记录等待接待用户
func (s *Server) recordWaitingReceptionUser(ctx context.Context, aiUid, channelId, uid uint32) error {
	// 如果该用户已经接待过，就不需要记录了，不然可能会把未接待过的进房用户挤掉
	receptionUserResp, err := s.clients.AigcAccount.CheckReceptionUser(ctx, &aigc_account.CheckReceptionUserRequest{
		AiUid:     aiUid,
		ChannelId: channelId,
		UidList:   []uint32{uid},
	})
	if err != nil {
		// 查询失败，继续记录
		log.WarnWithCtx(ctx, "RecordWaitingReceptionUser CheckReceptionUser failed, aiUid:%d, channelId:%d, uid:%d, err:%v", aiUid, channelId, uid, err)
	}

	if receptionUserResp.GetReceptionMap() != nil {
		// 已经接待过了，不需要记录
		if isReception, ok := receptionUserResp.GetReceptionMap()[uid]; ok && isReception {
			log.InfoWithCtx(ctx, "user already reception, no need record waiting, aiUid:%d, channelId:%d, uid:%d", aiUid, channelId, uid)
			return nil
		}
	}

	_, err = s.clients.AigcAccount.RecordWaitingReceptionUser(ctx, &aigc_account.RecordWaitingReceptionUserRequest{
		AiUid:      aiUid,
		ChannelId:  channelId,
		Uid:        uid,
		RecordTime: time.Now().Unix(),
	})
	if err != nil {
		log.WarnWithCtx(ctx, "RecordWaitingReceptionUser RecordWaitingReceptionUser failed, aiUid:%d, channelId:%d, uid:%d, err:%v", aiUid, channelId, uid, err)
		return err
	}
	return nil
}

func (s *Server) checkIsAiUserAvailable(ctx context.Context, aiUid, channelId uint32) bool {
	channelState, err := s.clients.AigcDataClient.BatchGetChannelState(ctx, &aigc_data.BatchGetChannelStateReq{
		ChannelIds: []uint32{channelId},
		QueryTypes: []aigc_data.QueryType{aigc_data.QueryType_AI_STATE},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "checkIsAiUserAvailable BatchGetChannelState failed, err:%v, aiUid:%d, channelId:%d", err, aiUid, channelId)
		return false
	}

	if channelState.GetChannel_2AiStates() == nil {
		log.WarnWithCtx(ctx, "checkIsAiUserAvailable channelState.GetChannel_2AiStates() is nil, aiUid:%d, channelId:%d", aiUid, channelId)
	}

	if aiStates, ok := channelState.GetChannel_2AiStates()[channelId]; ok {
		if aiStates.GetAiuid_2State() == nil {
			log.WarnWithCtx(ctx, "checkIsAiUserAvailable aiStates.GetId_2State() is nil, aiUid:%d, channelId:%d", aiUid, channelId)
			return false
		}
		if state, ok := aiStates.GetAiuid_2State()[aiUid]; ok {
			log.InfoWithCtx(ctx, "ai user not idle, aiUid:%d, channelId:%d, state:%v", aiUid, channelId, state)
			if state == aigc_data.AIState_AVAILABLE {
				return true
			}
		}
	}

	return false
}

func (s *Server) getReceptionContentRandom(ctx context.Context, receptionMsgList []string, uidList []uint32) (string, error) {
	if len(receptionMsgList) == 0 || len(uidList) == 0 {
		return "", nil
	}

	// 随机获取一条接待消息
	index := rand.Intn(len(receptionMsgList))
	receptionContent := receptionMsgList[index]

	// 查询用户昵称
	userInfoResp, err := s.clients.AccountGo.GetUsersByUids(context.Background(), &accountGoPb.UidsReq{
		UidList: uidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getReceptionContentRandom BatGetUserByUid failed, err:%v, uidList:%v", err, uidList)
		return "", err
	}

	nickNameList := s.buildNickNameList(userInfoResp)
	if len(nickNameList) == 0 {
		return "", nil
	}

	// 用nickNameList替换receptionContent中的 {{nick_name}} 占位符
	return strings.ReplaceAll(receptionContent, "{{nick_name}}", nickNameList), nil
}

func (s *Server) buildNickNameList(userInfoResp *accountGoPb.UsersResp) string {
	if userInfoResp.GetUserList() == nil || len(userInfoResp.GetUserList()) == 0 {
		log.InfoWithCtx(context.Background(), "buildNickNameList userInfoResp.GetUserList() is nil or empty")
		return ""
	}

	nickNames := make([]string, 0, len(userInfoResp.GetUserList()))
	reg := regexp.MustCompile(`[\p{Han}a-zA-Z0-9]+`)
	for _, userInfo := range userInfoResp.GetUserList() {
		if nickname := userInfo.GetNickname(); len(nickname) > 0 {
			matches := reg.FindAllString(nickname, -1)
			if cleanNickname := strings.Join(matches, ""); len(cleanNickname) > 0 {
				nickNames = append(nickNames, cleanNickname)
			}
		}
	}

	return strings.Join(nickNames, "、")
}

// 发送接待消息
func (s *Server) sendReceptionMessage(ctx context.Context, aiUid, channelId uint32, uidList []uint32) error {
	// 获取房间接待配置信息
	roomConfig, err := s.clients.AigcAccount.GetAiRoomCfg(ctx, &aigc_account.GetAiRoomCfgRequest{Uid: aiUid})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAiRoomCfg failed, err:%v, aiUid:%d", err, aiUid)
		return err
	}

	if !s.isValidRoomConfig(roomConfig) {
		log.WarnWithCtx(ctx, "invalid room config, aiUid:%d", aiUid)
		return nil
	}

	// 组装接待内容
	receptionContent, err := s.getReceptionContentRandom(ctx, roomConfig.GetRoomCfg().GetBusinessCfg().GetReceptionMsgList(), uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "getReceptionContentRandom failed, aiUid:%d, channelId:%d, err:%v", aiUid, channelId, err)
		return err
	}

	if len(receptionContent) == 0 {
		log.WarnWithCtx(ctx, "reception content is empty, aiUid:%d, channelId:%d", aiUid, channelId)
		return nil
	}

	// 发送接待消息
	if _, err := s.clients.AigcChatClient.AIShoutOutUserInRoom(ctx, &aigc_chat.AIShoutOutUserInRoomReq{
		ChannelId: channelId,
		AiUid:     aiUid,
		Content:   receptionContent,
	}); err != nil {
		log.ErrorWithCtx(ctx, "AIShoutOutUserInRoom failed, aiUid:%d, channelId:%d, err:%v", aiUid, channelId, err)
		return err
	}

	// 记录接待信息
	return s.recordReceptionInfo(ctx, aiUid, channelId, uidList)
}

// 验证房间配置是否有效
func (s *Server) isValidRoomConfig(roomConfig *aigc_account.GetAiRoomCfgResponse) bool {
	return roomConfig.GetRoomCfg() != nil &&
		roomConfig.GetRoomCfg().GetBusinessCfg() != nil &&
		len(roomConfig.GetRoomCfg().GetBusinessCfg().GetReceptionMsgList()) > 0
}

// 记录接待信息
func (s *Server) recordReceptionInfo(ctx context.Context, aiUid, channelId uint32, uidList []uint32) error {
	now := time.Now().Unix()

	// 记录最近接待时间
	if _, err := s.clients.AigcAccount.RecordReceptionTime(ctx, &aigc_account.RecordReceptionTimeRequest{
		AiUid:         aiUid,
		ChannelId:     channelId,
		ReceptionTime: now,
	}); err != nil {
		log.WarnWithCtx(ctx, "RecordReceptionTime failed, aiUid:%d, channelId:%d, err:%v", aiUid, channelId, err)
	}

	// 记录最近接待用户记录
	if _, err := s.clients.AigcAccount.RecordReceptionUser(ctx, &aigc_account.RecordReceptionUserRequest{
		AiUid:     aiUid,
		ChannelId: channelId,
		UidList:   uidList,
	}); err != nil {
		log.WarnWithCtx(ctx, "RecordReceptionUser failed, aiUid:%d, channelId:%d, err:%v", aiUid, channelId, err)
	}

	return nil
}

func (s *Server) checkIsAiRoom(ctx context.Context, channelInfo *channelGoPb.GetChannelSimpleInfoResp) bool {
	if channelInfo.GetChannelSimple() == nil {
		log.WarnWithCtx(ctx, "checkIsAiRoom channelInfo.GetChannelInfo() is nil")
		return false
	}

	aiAccount, errs := s.clients.AigcAccount.GetAIAccount(ctx, &aigc_account.GetAIAccountRequest{
		Uid: channelInfo.GetChannelSimple().GetCreaterUid(),
	})
	if errs != nil {
		log.ErrorWithCtx(ctx, "filterNotAiAccount GetAIAccount failed, uid:%d, err:%v", channelInfo.GetChannelSimple().GetCreaterUid(), errs)
		return false
	}

	if aiAccount.GetAccount() != nil && aiAccount.GetAccount().GetIsUnregister() == false && len(aiAccount.GetAccount().GetIdentity()) > 0 {
		return true
	}

	return false
}
