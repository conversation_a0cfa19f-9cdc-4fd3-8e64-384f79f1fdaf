package enter_room_handler

import (
	"context"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_base_api"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_middle"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_middle_base"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	topic_channel "golang.52tt.com/protocol/services/topic_channel/channel"
	"golang.52tt.com/services/aigc/aigc-room-middle/internal/rpc"
	"time"
)

type EnterRoomHandler struct {
	clients *rpc.Clients
}

func NewEnterRoomHandler(clients *rpc.Clients) *EnterRoomHandler {
	return &EnterRoomHandler{
		clients: clients,
	}
}

func (h *EnterRoomHandler) Running(ctx context.Context, roomCfg *aigc_account.AiRoomCfg) error {

	uid := roomCfg.GetUid()
	cid := roomCfg.GetBaseCfg().GetCid()
	// 切换玩法
	switchRsp, err := h.clients.TopicChannelClient.SwitchChannelTabAndBC(ctx, &topic_channel.SwitchChannelTabReq{
		Uid:       uid,
		TabId:     roomCfg.GetBaseCfg().GetTabId(),
		ChannelId: cid,
		Source:    topic_channel.Source_SWITCH,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "Running uid:%d cid:%d SwitchChannelTabAndBC err: %v", uid, cid, err)
		return err
	}
	log.InfoWithCtx(ctx, "Running uid:%d cid:%d SwitchChannelTabAndBC rsp: %s", uid, cid, switchRsp.String())

	// 进房
	rsp, err := h.clients.ChannelBaseApi.EnterChannel(ctx, &channel_base_api.EnterChannelReq{
		ChannelBaseReq: &channel_middle_base.BaseReq{
			OpSource: "ugc_ai_channel",
			Cid:      cid,
			OpUid:    uid,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "Running uid:%d cid:%d EnterChannel err: %v", uid, cid, err)
		return err
	}
	log.InfoWithCtx(ctx, "Running uid:%d cid:%d EnterChannel rsp: %s", uid, cid, rsp.String())


	// 上麦（踢占麦人）
	holdMicRsp, err := h.clients.ChannelMicMiddleClient.HoldMic(ctx, &channel_mic_middle.HoldMicReq{
		Source:      "ugc_ai_channel",
		Uid:         uid,
		Cid:         cid,
		MicId:       roomCfg.GetBusinessCfg().GetAiUseMicIndex(),
		Permissions: []channel_mic_middle.MicPermission{channel_mic_middle.MicPermission_KickOnMicUser},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "Running uid:%d cid:%d HoldMic err: %v", uid, cid, err)
		return err
	}
	log.InfoWithCtx(ctx, "Running uid:%d cid:%d HoldMic rsp: %s", uid, cid, holdMicRsp.String())
	expiredAt := calcExpireTime(roomCfg)
	if expiredAt == 0 {
		return protocol.NewExactServerError(nil, status.ErrSys, "no operation time")
	}
	_, err = h.clients.AigcAccount.AddRunningRoom(ctx, &aigc_account.AddRunningRoomRequest{
		Cid:       roomCfg.GetBaseCfg().GetCid(),
		Uid:       roomCfg.GetUid(),
		ExpiredAt: expiredAt,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "Running uid:%d cid:%d AddRunningRoom err: %v", uid, cid, err)
		return err
	}
	return nil
}

func (h *EnterRoomHandler) StopRunning(roomCfg *aigc_account.AiRoomCfg) error {

	return nil
}

func calcExpireTime(roomCfg *aigc_account.AiRoomCfg) int64 {
	curTime := time.Now()
	curDayZeroTime := time.Date(curTime.Year(), curTime.Month(), curTime.Day(), 0, 0, 0, 0, curTime.Location()).Unix()
	curDaySec := curTime.Unix() - curDayZeroTime
	for _, v := range roomCfg.GetBusinessCfg().GetOperationTimes() {
		if v.GetStartTime() <= curDaySec && curDaySec <= v.GetEndTime() {
			return curDayZeroTime + v.GetEndTime()
		}
	}

	return 0
}
