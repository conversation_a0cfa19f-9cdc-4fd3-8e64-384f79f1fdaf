package publish_room_handler

import (
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	"golang.52tt.com/services/aigc/aigc-room-middle/internal/rpc"
)

type PublishRoomHandler struct {
	clients *rpc.Clients
}

func NewPublishRoomHandler(clients *rpc.Clients) *PublishRoomHandler {
	return &PublishRoomHandler{
		clients: clients,
	}
}

func (h *PublishRoomHandler) Publish(roomCfg *aigc_account.AiRoomCfg) error {

	return nil
}

func (h *PublishRoomHandler) Dismiss(roomCfg *aigc_account.AiRoomCfg) error {

	return nil
}
