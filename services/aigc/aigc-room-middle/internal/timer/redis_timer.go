package timer

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	context_info "golang.52tt.com/pkg/context-info"
	"golang.52tt.com/pkg/timer"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	"golang.52tt.com/services/aigc/aigc-room-middle/internal/mgr/enter_room_handler"
	"golang.52tt.com/services/aigc/aigc-room-middle/internal/mgr/publish_room_handler"
	"golang.52tt.com/services/aigc/aigc-room-middle/internal/rpc"
	"time"
)

func NewRedisTimer(clients *rpc.Clients, enterRoomHandler *enter_room_handler.EnterRoomHandler,
	publishRoomHandler *publish_room_handler.PublishRoomHandler) *RedisTimer {

	return &RedisTimer{
		clients:            clients,
		enterRoomHandler:   enterRoomHandler,
		publishRoomHandler: publishRoomHandler,
	}
}

type RedisTimer struct {
	timerD             *timer.Timer
	clients            *rpc.Clients
	enterRoomHandler   *enter_room_handler.EnterRoomHandler
	publishRoomHandler *publish_room_handler.PublishRoomHandler
}

func (t *RedisTimer) Start() error {

	ctx, cancelFunc := context.WithCancel(context.Background())
	defer cancelFunc()

	timerD, err := timer.NewTimerD(ctx, "aigc-room-middle")
	if err != nil {
		log.Errorf("NewTimerD err:%v", err)
		return err
	}
	t.timerD = timerD

	// 检测运营中的房间
	timerD.AddIntervalTask("HandleRunningRoom", 30*time.Second, timer.BuildFromLambda(func(ctx context.Context) {
		_ = t.HandleRunningRoom()
	}))

	timerD.Start()
	return nil
}

func (t *RedisTimer) Stop() {
	t.timerD.Stop()
}

func (t *RedisTimer) HandleRunningRoom() error {
	ctx := context.Background()
	ctx = context_info.GenReqId(ctx)
	lastCid := uint32(0)
	for {
		rsp, err := t.clients.AigcAccount.GetRunningAiRooms(ctx, &aigc_account.GetRunningAiRoomsRequest{
			LastCid: lastCid,
			Limit:   100,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleRunningRoom GetRunningAiRooms err: %v", err)
			return err
		}
		roomCfgs := rsp.GetRoomCfgList()
		if len(roomCfgs) == 0 {
			break
		}
		roomList := make([]*aigc_account.RoomInfo, 0, len(roomCfgs))
		for _, roomCfg := range roomCfgs {
			roomList = append(roomList, &aigc_account.RoomInfo{
				Cid: roomCfg.GetBaseCfg().GetCid(),
				Uid: roomCfg.GetUid(),
			})
		}
		statusRsp, err := t.clients.AigcAccount.BatGetRunningRoom(ctx, &aigc_account.BatGetRunningRoomRequest{
			RoomList: roomList,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleRunningRoom BatGetRunningRoom err: %v", err)
			return err
		}
		statusMap := statusRsp.GetRoomRunningMap()
		for _, roomCfg := range roomCfgs {
			if statusMap[roomCfg.GetBaseCfg().GetCid()] {
				// 房间已经在运营中
				continue
			}
			err = t.enterRoomHandler.Running(ctx, roomCfg)
			if err != nil {
				log.ErrorWithCtx(ctx, "HandleRunningRoom enterRoomHandler.Running err: %v, roomCfg: %s", err, roomCfg.String())
				continue
			}
		}

		if rsp.GetLoadFinish() {
			break
		}
		lastCid = roomCfgs[len(roomCfgs)-1].GetBaseCfg().GetCid()
	}
	return nil

}
