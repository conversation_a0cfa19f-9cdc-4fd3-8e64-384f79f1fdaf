package config

import (
	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
	"sync/atomic"
)

type AigcRoomMiddleConfig struct {
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (s *AigcRoomMiddleConfig) Format() error {
	return nil
}

var (
	atomicAigcRoomMiddleConfig *atomic.Value
)

func init() {
	//if err := InitAigcAccountMiddleConfig(); err != nil {
	//    panic(err)
	//}
}

// SetAigcRoomMiddleConfig 单测mock使用
func SetAigcRoomMiddleConfig(cfg *AigcRoomMiddleConfig) {
	if atomicAigcRoomMiddleConfig == nil {
		atomicAigcRoomMiddleConfig = &atomic.Value{}
	}
	if cfg == nil {
		cfg = &AigcRoomMiddleConfig{}
	}
	atomicAigcRoomMiddleConfig.Store(cfg)
}

// InitAigcRoomMiddleConfig
// 可以选择外部初始化或者直接init函数初始化
func InitAigcRoomMiddleConfig() error {
	cfg := &AigcRoomMiddleConfig{}
	atomCfg, err := ttconfig.AtomLoad("aigc-room-middle", cfg)
	if nil != err {
		return err
	}
	atomicAigcRoomMiddleConfig = atomCfg
	return nil
}

func GetAigcRoomMiddleConfig() *AigcRoomMiddleConfig {
	return atomicAigcRoomMiddleConfig.Load().(*AigcRoomMiddleConfig)
}
