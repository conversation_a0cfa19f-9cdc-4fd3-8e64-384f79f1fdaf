package internal

import (
	"context"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"

	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	"golang.52tt.com/services/aigc/aigc-soulmate/internal/config/ttconfig"
	category "golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr/category/entity"
	category_entity "golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr/category/entity"
	chat_template_entity "golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr/chat_template/entity"
	game_entity "golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr/game/entity"
	"golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr/partner/entity"
	partner_entity "golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr/partner/entity"
	role_entity "golang.52tt.com/services/aigc/aigc-soulmate/internal/mgr/role/entity"
)

func (s *Server) assembleAIRoleCategory(_ context.Context, category *category_entity.Category) *pb.AIRoleCategory {
	categoryPb := &pb.AIRoleCategory{
		Id:         category.StringID(),
		Title:      category.Title,
		Sort:       category.Sort,
		UpdateTime: category.UpdateTime,
		Tips:       category.Tips,
		PropScenes: category.PropScene.Unpack(),
		Props:      make([]*pb.AIRoleCategory_Prop, 0, len(category.Props)),
	}

	for _, prop := range category.Props {
		propPb := &pb.AIRoleCategory_Prop{
			Id:               prop.ID,
			Name:             prop.Name,
			Type:             prop.Type,
			Labels:           make([]*pb.AIRoleCategory_Label, 0, len(prop.Labels)),
			LabelSelectLimit: prop.LabelSelectLimit,
		}

		for _, label := range prop.Labels {
			labelPb := &pb.AIRoleCategory_Label{
				Id:     label.ID,
				Name:   label.Name,
				Scenes: label.Scene.Unpack(),
			}

			propPb.Labels = append(propPb.Labels, labelPb)
		}

		categoryPb.Props = append(categoryPb.Props, propPb)
	}

	return categoryPb
}

func (s *Server) assembleAIRole(ctx context.Context, role *role_entity.Role) (*pb.AIRole, error) {
	if role == nil {
		return nil, nil
	}

	aiRoleList, err := s.assembleAIRoleList(ctx, []*role_entity.Role{role})
	if err != nil || len(aiRoleList) == 0 {
		return nil, err
	}

	return aiRoleList[0], nil
}

func (s *Server) assembleAIRoleList(ctx context.Context, roles []*role_entity.Role) ([]*pb.AIRole, error) {
	if len(roles) == 0 {
		return nil, nil
	}

	uid := metainfo.GetServiceInfo(ctx).UserID()

	var (
		categoryIdSet  = make(map[string]struct{}, len(roles))
		categoryIdList = make([]string, 0, len(roles))
	)
	for _, role := range roles {
		if role.CategoryId == "" {
			continue
		}

		if _, ok := categoryIdSet[role.CategoryId]; !ok {
			categoryIdSet[role.CategoryId] = struct{}{}
			categoryIdList = append(categoryIdList, role.CategoryId)
		}
	}

	categoryMap := make(map[string]*category.Category)
	if len(categoryIdList) > 0 {
		var err error
		categoryMap, err = s.category.BatchGetCategoryMap(ctx, categoryIdList)
		if err != nil {
			log.ErrorWithCtx(ctx, "toRolesPb BatchGetCategoryMap id(%+v) err: %v", categoryIdList, err)
			return nil, err
		}
	}

	var list = make([]*pb.AIRole, 0, len(roles))
	for _, role := range roles {
		rolePb := &pb.AIRole{
			Id:              role.ID,
			Avatar:          role.Avatar,
			Style:           role.Style,
			Sex:             role.Sex,
			Image:           role.Image,
			Intro:           role.Intro,
			Type:            role.Type,
			DialogColor:     role.DialogColor,
			Name:            role.Name,
			State:           role.State,
			Character:       role.Character,
			Tags:            role.Tags,
			Prologue:        role.Prologue,
			PrologueAudio:   role.PrologueAudio,
			Timbre:          role.Timbre,
			PromptId:        role.PromptId,
			PromptVersion:   role.PromptVersion,
			EnableRcmdReply: role.EnableRcmdReply,
			CornerIcon:      role.CornerIcon,
			Uid:             role.Uid,
			AuditResult:     role.AuditResult,
			InsertPos:       role.InsertPos,
			Exposed:         role.Exposed,
			AuditAt:         role.AuditAt,
			UserLikeNum:     role.UserLikes,
			ConfigLikeNum:   role.ConfigLikes,
			CreatedAt:       role.CreatedAt,
			StoryId:         role.StoryId,
			StoryMode:       role.StoryMode,
			EntranceTag:     role.EntranceTag,
			CreatorInfoType: role.CreatorInfoType,
			AppointUid:      role.AppointUid,
			UserRoleSetting: role.UserRoleSetting,
			RelationIds:     role.RelationIds,
			UpdatedAt:       role.UpdatedAt,
			Scope:           role.Scope,
		}
		if category := categoryMap[role.CategoryId]; category != nil {
			rolePb.Category = &pb.AIRoleCategory{
				Id:    category.StringID(),
				Title: category.Title,
			}
		}
		if prologue := role.CollectPrologue; prologue != nil {
			rolePb.CollectPrologue = &pb.AIRolePrologue{
				Text:  prologue.Text,
				Audio: prologue.Audio,
			}
		}
		if pb.AuditResult(role.AuditResult) != pb.AuditResult_AuditResultPass && role.Uid == uid {
			rolePb.Name = role.AuditContent.Name
			rolePb.Image = role.AuditContent.Image
			rolePb.Avatar = role.AuditContent.Avatar
			rolePb.Prologue = role.AuditContent.Prologue
			rolePb.Character = role.AuditContent.Character
		}

		if role.Type == pb.AIRoleType_AIRoleTypeGroup && role.GroupConfig != nil {
			rolePb.GroupRoleConfig = &pb.GroupRoleConfig{
				ChatCharacter:     role.GroupConfig.ChatCharacter,
				RelationCharacter: role.GroupConfig.RelationCharacter,
				Desc:              role.GroupConfig.Desc,
			}
			for _, prologue := range role.GroupConfig.Prologues {
				rolePb.GroupRoleConfig.Prologues = append(rolePb.GroupRoleConfig.Prologues, &pb.AIGroupPrologue{
					Text:     prologue.Text,
					Audio:    prologue.Audio,
					Priority: prologue.Priority,
				})
			}
			for _, prologue := range role.GroupConfig.WelcomePrologues {
				rolePb.GroupRoleConfig.WelcomePrologues = append(rolePb.GroupRoleConfig.WelcomePrologues, &pb.AIGroupPrologue{
					Text:     prologue.Text,
					Audio:    prologue.Audio,
					Priority: prologue.Priority,
				})
			}
		}
		list = append(list, rolePb)
	}

	return list, nil
}

func (s *Server) assembleAIPartner(ctx context.Context, partner *partner_entity.Partner) (*pb.AIPartner, error) {
	if partner == nil {
		return nil, nil
	}

	aiPartnerList, err := s.assembleAIPartnerList(ctx, []*entity.Partner{partner})
	if err != nil || len(aiPartnerList) == 0 {
		return nil, err
	}

	return aiPartnerList[0], nil
}

func (s *Server) assembleAIPartnerList(ctx context.Context, partners []*partner_entity.Partner) ([]*pb.AIPartner, error) {
	if len(partners) == 0 {
		return nil, nil
	}

	uid := metainfo.GetServiceInfo(ctx).UserID()

	var (
		roleIdSet  = make(map[uint32]struct{}, len(partners))
		roleIdList = make([]uint32, 0, len(partners))
	)
	for _, partner := range partners {
		if _, ok := roleIdSet[partner.RoleId]; !ok {
			roleIdSet[partner.RoleId] = struct{}{}
			roleIdList = append(roleIdList, partner.RoleId)
		}
	}
	if len(roleIdList) == 0 {
		log.WarnWithCtx(ctx, "toPartnersPb partners miss role")
		return nil, nil
	}

	roleMap, err := s.role.GetRoleMap(ctx, roleIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "toPartnersPb GetRoleMap roleIdList(%+v) err: %v", roleIdList, err)
		return nil, err
	}

	list := make([]*pb.AIPartner, 0, len(partners))
	for _, partner := range partners {
		role := roleMap[partner.RoleId]
		if role == nil {
			log.WarnWithCtx(ctx, "toPartnersPb partner %d role %d not found", partner.ID, partner.RoleId)
			continue
		}

		rolePb := &pb.AIRole{
			Id:              role.ID,
			Avatar:          role.Avatar,
			Style:           role.Style,
			Sex:             role.Sex,
			Image:           role.Image,
			Intro:           role.Intro,
			Type:            role.Type,
			DialogColor:     role.DialogColor,
			Name:            role.Name,
			State:           role.State,
			Character:       role.Character,
			Tags:            role.Tags,
			Prologue:        role.Prologue,
			PrologueAudio:   role.PrologueAudio,
			EnableRcmdReply: role.EnableRcmdReply,
			Uid:             role.Uid,
			AuditResult:     role.AuditResult,
			AuditAt:         role.AuditAt,
			ConfigLikeNum:   role.ConfigLikes,
			UserLikeNum:     role.UserLikes,
			StoryId:         role.StoryId,
			StoryMode:       role.StoryMode,
			Exposed:         role.Exposed,
			EntranceTag:     role.EntranceTag,
			CreatorInfoType: role.CreatorInfoType,
			AppointUid:      role.AppointUid,
			UserRoleSetting: role.UserRoleSetting,
			RelationIds:     role.RelationIds,
			Scope:           role.Scope,
		}
		if role.AuditResult != pb.AuditResult_AuditResultPass && role.Uid == uid {
			rolePb.Name = role.AuditContent.Name
			rolePb.Image = role.AuditContent.Image
			rolePb.Avatar = role.AuditContent.Avatar
			rolePb.Prologue = role.AuditContent.Prologue
			rolePb.Character = role.AuditContent.Character
		}

		partnerPb := &pb.AIPartner{
			Id:            partner.ID,
			Name:          partner.Name,
			CallName:      partner.CallName,
			Role:          rolePb,
			Silent:        partner.Silent,
			Uid:           partner.Uid,
			ChangeRoleCnt: partner.ChangeRoleCnt,
			Source:        pb.AIPartnerSource(partner.Source),
			UnsetName:     partner.UnsetName(),
			UnsetRole:     partner.UnsetRole(),
		}
		if partnerPb.GetUnsetName() {
			switch rolePb.GetType() {
			case pb.AIRoleType_AIRoleTypePartner:
				partnerPb.Name = ttconfig.Config.GetAIGCSoulmateConfig().GetDefaultSoulmateName()
			case pb.AIRoleType_AIRoleTypeGame:
				partnerPb.Name = rolePb.GetName()
			}
		}

		list = append(list, partnerPb)
	}

	return list, nil
}

func (s *Server) assembleInteractiveGame(game *game_entity.InteractiveGame) *pb.InteractiveGame {
	if game == nil {
		return nil
	}

	return &pb.InteractiveGame{
		Id:        game.ID,
		Source:    game.Source,
		Uid:       game.Uid,
		RoleId:    game.RoleID,
		TopicId:   game.TopicID,
		Title:     game.Title,
		Desc:      game.Desc,
		Prologue:  game.Prologue,
		State:     game.State,
		Exposure:  game.Exposure,
		CreatedAt: game.CreatedAt.Unix(),
		UpdatedAt: game.UpdatedAt.Unix(),
	}
}

func (s *Server) assembleAIChatTemplate(chatTemplate *chat_template_entity.AIChatTemplate) *pb.AIChatTemplate {
	if chatTemplate == nil {
		return nil
	}

	return &pb.AIChatTemplate{
		BaseTemplate: s.assembleBaseChatTemplate(chatTemplate),
		BindEntities: s.assembleBindEntityList(chatTemplate.BindEntities),
	}
}

func (s *Server) assembleBaseChatTemplate(chatTemplate *chat_template_entity.AIChatTemplate) *pb.BaseChatTemplate {
	if chatTemplate == nil {
		return nil
	}

	msgs := make([]*pb.TemplateMsg, 0, len(chatTemplate.Msgs))
	for _, msg := range chatTemplate.Msgs {
		msgs = append(msgs, &pb.TemplateMsg{
			Content:    msg.Content,
			SenderType: msg.SenderType,
		})
	}

	return &pb.BaseChatTemplate{
		Id:   chatTemplate.Id,
		Name: chatTemplate.Name,
		Msgs: msgs,
	}
}

func (s *Server) assembleBindEntityList(bindEntityList []*chat_template_entity.BindEntity) []*pb.BindEntity {
	if bindEntityList == nil {
		return nil
	}

	list := make([]*pb.BindEntity, 0, len(bindEntityList))
	for _, bindEntity := range bindEntityList {
		list = append(list, &pb.BindEntity{
			EntityType: bindEntity.Type,
			Id:         bindEntity.Id,
		})
	}

	return list
}
