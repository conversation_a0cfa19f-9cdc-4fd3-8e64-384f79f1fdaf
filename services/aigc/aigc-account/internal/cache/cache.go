package cache

import (
	"context"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
)

//go:generate mockgen -destination=mocks/chat_round.go -package=mocks golang.52tt.com/services/aigc/aigc-account/internal/cache ChatRoundCache
type ChatRoundCache interface {
	IncrChatRound(ctx context.Context, uid, aiUid uint32) error
	GetSpecialChatRound(ctx context.Context, uid, aiUid uint32) (uint32, error)
	GetTotalChatRound(ctx context.Context, uid uint32) (uint32, error)
}

//go:generate mockgen -destination=mocks/chat_ai.go -package=mocks golang.52tt.com/services/aigc/aigc-account/internal/cache ChatAiCache
type ChatAiCache interface {
	RecordImChat(ctx context.Context, fromUid, toUid uint32) error
	GetImChatRecord(ctx context.Context, fromUid, toUid uint32) (int64, error)
	AddInteraction(ctx context.Context, uid, aiUid uint32) error
	GetInteractionAiList(ctx context.Context, uid uint32) ([]uint32, error)
	AddLikePost(ctx context.Context, uid uint32, postId string) error
	GetLikePostCount(ctx context.Context, uid uint32) (int64, error)
	AddCommentPost(ctx context.Context, uid uint32, postId string) error
	GetCommentPostCount(ctx context.Context, uid uint32) (int64, error)
	IncrPostAiCommentCount(ctx context.Context, postId string, aiUid uint32) (int64, error)
	GetPostAiCommentCount(ctx context.Context, postId string, aiUid uint32) (int64, error)
	GetPostCommentAiList(ctx context.Context, postId string) (map[uint32]int64, error)
}

//go:generate mockgen -destination=mocks/ai_reception.go -package=mocks golang.52tt.com/services/aigc/aigc-account/internal/cache AiReception
type AiReception interface {
	SaveLastReceptionTime(ctx context.Context, uid, channelId uint32, receptionTime int64) error
	GetLastReceptionTime(ctx context.Context, aidUid, channelId uint32) (int64, error)
	SaveWaitingReceptionUser(ctx context.Context, aiUid, channelId, uid uint32, recordTime int64) error
	GetWaitingReceptionUser(ctx context.Context, aiUid, channelId uint32) ([]WaitingReceptionUser, error)
	SaveReceptionUser(ctx context.Context, aiUid, channelId uint32, uidList []uint32) error
	IsReceptionUser(ctx context.Context, aiUid, channelId, uid uint32) (bool, error)
	SaveRoomUserNum(ctx context.Context, aiUid, channelId uint32, userNum int) error
	GetRoomUserNum(ctx context.Context, aiUid, channelId uint32) (int, error)
}

//go:generate mockgen -destination=mocks/ai_room_cache.go -package=mocks golang.52tt.com/services/aigc/aigc-account/internal/cache AiRoomCache
type AiRoomCache interface {
	SetRunningRoom(ctx context.Context, room *entity.RoomInfo, expireAt int64) error
	BatDelRunningRoom(ctx context.Context, room []*entity.RoomInfo) error
	BatGetRunningStatus(ctx context.Context, roomInfos []*entity.RoomInfo) (map[uint32]bool, error)
	BatUpdateHeartbeat(ctx context.Context, cid []uint32) error
	GetNeedHeartbeatChannel(ctx context.Context, limit int64) ([]uint32, error)
	BatRemoveHeartbeat(ctx context.Context, cid []uint32) error
}

type WaitingReceptionUser struct {
	Uid        uint32
	RecordTime int64
}
