// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-account/internal/cache (interfaces: AiReception)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	cache "golang.52tt.com/services/aigc/aigc-account/internal/cache"
)

// MockAiReception is a mock of AiReception interface.
type MockAiReception struct {
	ctrl     *gomock.Controller
	recorder *MockAiReceptionMockRecorder
}

// MockAiReceptionMockRecorder is the mock recorder for MockAiReception.
type MockAiReceptionMockRecorder struct {
	mock *MockAiReception
}

// NewMockAiReception creates a new mock instance.
func NewMockAiReception(ctrl *gomock.Controller) *MockAiReception {
	mock := &MockAiReception{ctrl: ctrl}
	mock.recorder = &MockAiReceptionMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAiReception) EXPECT() *MockAiReceptionMockRecorder {
	return m.recorder
}

// GetLastReceptionTime mocks base method.
func (m *MockAiReception) GetLastReceptionTime(arg0 context.Context, arg1, arg2 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastReceptionTime", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastReceptionTime indicates an expected call of GetLastReceptionTime.
func (mr *MockAiReceptionMockRecorder) GetLastReceptionTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastReceptionTime", reflect.TypeOf((*MockAiReception)(nil).GetLastReceptionTime), arg0, arg1, arg2)
}

// GetRoomUserNum mocks base method.
func (m *MockAiReception) GetRoomUserNum(arg0 context.Context, arg1, arg2 uint32) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoomUserNum", arg0, arg1, arg2)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRoomUserNum indicates an expected call of GetRoomUserNum.
func (mr *MockAiReceptionMockRecorder) GetRoomUserNum(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoomUserNum", reflect.TypeOf((*MockAiReception)(nil).GetRoomUserNum), arg0, arg1, arg2)
}

// GetWaitingReceptionUser mocks base method.
func (m *MockAiReception) GetWaitingReceptionUser(arg0 context.Context, arg1, arg2 uint32) ([]cache.WaitingReceptionUser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWaitingReceptionUser", arg0, arg1, arg2)
	ret0, _ := ret[0].([]cache.WaitingReceptionUser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWaitingReceptionUser indicates an expected call of GetWaitingReceptionUser.
func (mr *MockAiReceptionMockRecorder) GetWaitingReceptionUser(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWaitingReceptionUser", reflect.TypeOf((*MockAiReception)(nil).GetWaitingReceptionUser), arg0, arg1, arg2)
}

// IsReceptionUser mocks base method.
func (m *MockAiReception) IsReceptionUser(arg0 context.Context, arg1, arg2, arg3 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsReceptionUser", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsReceptionUser indicates an expected call of IsReceptionUser.
func (mr *MockAiReceptionMockRecorder) IsReceptionUser(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsReceptionUser", reflect.TypeOf((*MockAiReception)(nil).IsReceptionUser), arg0, arg1, arg2, arg3)
}

// SaveLastReceptionTime mocks base method.
func (m *MockAiReception) SaveLastReceptionTime(arg0 context.Context, arg1, arg2 uint32, arg3 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveLastReceptionTime", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveLastReceptionTime indicates an expected call of SaveLastReceptionTime.
func (mr *MockAiReceptionMockRecorder) SaveLastReceptionTime(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveLastReceptionTime", reflect.TypeOf((*MockAiReception)(nil).SaveLastReceptionTime), arg0, arg1, arg2, arg3)
}

// SaveReceptionUser mocks base method.
func (m *MockAiReception) SaveReceptionUser(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveReceptionUser", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveReceptionUser indicates an expected call of SaveReceptionUser.
func (mr *MockAiReceptionMockRecorder) SaveReceptionUser(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveReceptionUser", reflect.TypeOf((*MockAiReception)(nil).SaveReceptionUser), arg0, arg1, arg2, arg3)
}

// SaveRoomUserNum mocks base method.
func (m *MockAiReception) SaveRoomUserNum(arg0 context.Context, arg1, arg2 uint32, arg3 int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveRoomUserNum", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveRoomUserNum indicates an expected call of SaveRoomUserNum.
func (mr *MockAiReceptionMockRecorder) SaveRoomUserNum(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveRoomUserNum", reflect.TypeOf((*MockAiReception)(nil).SaveRoomUserNum), arg0, arg1, arg2, arg3)
}

// SaveWaitingReceptionUser mocks base method.
func (m *MockAiReception) SaveWaitingReceptionUser(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveWaitingReceptionUser", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveWaitingReceptionUser indicates an expected call of SaveWaitingReceptionUser.
func (mr *MockAiReceptionMockRecorder) SaveWaitingReceptionUser(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveWaitingReceptionUser", reflect.TypeOf((*MockAiReception)(nil).SaveWaitingReceptionUser), arg0, arg1, arg2, arg3, arg4)
}
