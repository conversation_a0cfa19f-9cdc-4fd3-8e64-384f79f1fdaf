package redis

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-account/internal/cache"
	"golang.52tt.com/services/aigc/aigc-account/internal/infra/db"
	"strconv"
	"time"
)

type AiReceptionCache struct {
	cmder *db.RedisDB
}

func NewAiReceptionCache(database *db.RedisDB) cache.AiReception {
	return &AiReceptionCache{
		cmder: database,
	}
}

func (r *AiReceptionCache) genKeyOfReceptionTime(uid, channelId uint32) string {
	return fmt.Sprintf("ai_room_last_reception_%d_%d", uid, channelId)
}

func (r *AiReceptionCache) genKeyOfWaitingReceptionUser(uid, channelId uint32) string {
	return fmt.Sprintf("ai_room_waiting_reception_%d_%d", uid, channelId)
}

func (r *AiReceptionCache) genKeyOfReceptionUser(uid, channelId uint32) string {
	return fmt.Sprintf("ai_room_reception_user_%d_%d", uid, channelId)
}

func (r *AiReceptionCache) genKeyOfRoomUserNum(uid, channelId uint32) string {
	return fmt.Sprintf("ai_room_user_num_%d_%d", uid, channelId)
}

// SaveLastReceptionTime 记录最后一次接待时间
func (r *AiReceptionCache) SaveLastReceptionTime(ctx context.Context, uid, channelId uint32, receptionTime int64) error {
	key := r.genKeyOfReceptionTime(uid, channelId)
	err := r.cmder.Set(ctx, key, receptionTime, time.Hour).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SaveLastReceptionTime key:%s err:%v", key, err)
		return errors.WithStack(err)
	}

	return nil
}

// GetLastReceptionTime 获取最后一次接待时间
func (r *AiReceptionCache) GetLastReceptionTime(ctx context.Context, aidUid, channelId uint32) (int64, error) {
	key := r.genKeyOfReceptionTime(aidUid, channelId)
	val, err := r.cmder.Get(ctx, key).Result()
	if err != nil {
		return 0, err
	}
	receptionTime, err := strconv.ParseInt(val, 10, 64)
	if err != nil {
		return 0, err
	}
	return receptionTime, nil
}

// SaveWaitingReceptionUser 记录等待接待用户
func (r *AiReceptionCache) SaveWaitingReceptionUser(ctx context.Context, aiUid, channelId, uid uint32, recordTime int64) error {
	key := r.genKeyOfWaitingReceptionUser(aiUid, channelId)
	pipe := r.cmder.Pipeline()
	defer pipe.Close()

	pipe.ZAdd(ctx, key, &redis.Z{
		Score:  float64(recordTime),
		Member: strconv.Itoa(int(uid)),
	})

	// 只保留最近3个进房等待接待的用户
	pipe.ZRemRangeByRank(ctx, key, 3, -1)
	_, err := pipe.Exec(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "SaveWaitingReceptionUser key:%s err:%v", key, err)
		return errors.WithStack(err)
	}

	// 当天23:59:59过期
	now := time.Now()
	endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	err = r.cmder.ExpireAt(ctx, key, endOfDay).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SaveWaitingReceptionUser Expire key:%s err:%v", key, err)
		return errors.WithStack(err)
	}

	return nil
}

// GetWaitingReceptionUser 获取进房等待接待的用户
func (r *AiReceptionCache) GetWaitingReceptionUser(ctx context.Context, aiUid, channelId uint32) ([]cache.WaitingReceptionUser, error) {
	key := r.genKeyOfWaitingReceptionUser(aiUid, channelId)
	var userList []cache.WaitingReceptionUser
	members, err := r.cmder.ZRevRangeWithScores(ctx, key, 0, -1).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return userList, nil
		}
		log.ErrorWithCtx(ctx, "GetWaitingReceptionUser key:%s err:%v", key, err)
		return nil, errors.WithStack(err)
	}

	for _, member := range members {
		if uid, err := strconv.ParseUint(member.Member.(string), 10, 32); err == nil {
			userList = append(userList, cache.WaitingReceptionUser{
				Uid:        uint32(uid),
				RecordTime: int64(member.Score),
			})
		}
	}

	return userList, nil
}

// SaveReceptionUser 记录已接待用户
func (r *AiReceptionCache) SaveReceptionUser(ctx context.Context, aiUid, channelId, uid uint32) error {
	key := r.genKeyOfReceptionUser(aiUid, channelId)
	err := r.cmder.SAdd(ctx, key, strconv.Itoa(int(uid))).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SaveReceptionUser key:%s err:%v", key, err)
		return errors.WithStack(err)
	}
	err = r.cmder.Expire(ctx, key, time.Hour*24).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SaveReceptionUser Expire key:%s err:%v", key, err)
		return errors.WithStack(err)
	}
	return nil
}

// IsReceptionUser 是否已接待用户
func (r *AiReceptionCache) IsReceptionUser(ctx context.Context, aiUid, channelId, uid uint32) (bool, error) {
	key := r.genKeyOfReceptionUser(aiUid, channelId)
	isMember, err := r.cmder.SIsMember(ctx, key, strconv.Itoa(int(uid))).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return false, nil
		}
		log.ErrorWithCtx(ctx, "IsReceptionUser key:%s err:%v", key, err)
		return false, errors.WithStack(err)
	}
	return isMember, nil
}

// SaveRoomUserNum 保存房间用户数
func (r *AiReceptionCache) SaveRoomUserNum(ctx context.Context, aiUid, channelId uint32, userNum int) error {
	key := r.genKeyOfRoomUserNum(aiUid, channelId)
	err := r.cmder.Set(ctx, key, userNum, time.Hour*24).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "SaveRoomUserNum key:%s err:%v", key, err)
		return errors.WithStack(err)
	}
	return nil
}

// GetRoomUserNum 获取房间用户数
func (r *AiReceptionCache) GetRoomUserNum(ctx context.Context, aiUid, channelId uint32) (int, error) {
	key := r.genKeyOfRoomUserNum(aiUid, channelId)
	userNum, err := r.cmder.Get(ctx, key).Int()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "GetRoomUserNum key:%s err:%v", key, err)
		return 0, errors.WithStack(err)
	}
	return userNum, nil
}
