package redis

import (
	"context"
	"errors"
	"fmt"
	"golang.52tt.com/services/aigc/aigc-account/internal/cache"
	"golang.52tt.com/services/aigc/aigc-account/internal/infra/db"
	"math/rand"
	"strconv"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"golang.52tt.com/pkg/log"
)

type ChatAiCache struct {
	cmder *db.RedisDB
}

func NewChatAiCache(database *db.RedisDB) cache.ChatAiCache {
	return &ChatAiCache{
		cmder: database,
	}
}

func (c *ChatAiCache) genKeyOfImChat(fromUid, toUid uint32) string {
	return fmt.Sprintf("ai_im_%d_%d", fromUid, toUid)
}

func (c *ChatAiCache) genKeyOfAiImInteraction(uid uint32) string {
	return fmt.Sprintf("ai_im_interaction_%d", uid)
}

func (c *ChatAiCache) genKeyOfAiLikePost(uid uint32) string {
	today := time.Now().Format("2006-01-02")
	return fmt.Sprintf("ai_like_post_count_%d_%s", uid, today)
}

func (c *ChatAiCache) genKeyOfAiCommentPost(uid uint32) string {
	today := time.Now().Format("2006-01-02")
	return fmt.Sprintf("ai_comment_post_count_%d_%s", uid, today)
}

func (c *ChatAiCache) genKeyOfPostAiCommentCount(postId string) string {
	return fmt.Sprintf("post_ai_comment_count_%s", postId)
}

// RecordImChat 记录客态用户跟ai用户或者ai用户和客态用户的im聊天记录
func (c *ChatAiCache) RecordImChat(ctx context.Context, fromUid, toUid uint32) error {
	key := c.genKeyOfImChat(fromUid, toUid)
	now := time.Now().Unix()

	err := c.cmder.Set(ctx, key, now, time.Hour*24).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "RecordImChat set key:%s err:%v", key, err)
		return err
	}

	return nil
}

// GetImChatRecord 获取客态用户跟ai用户或者ai用户和客态用户的im聊天记录
func (c *ChatAiCache) GetImChatRecord(ctx context.Context, fromUid, toUid uint32) (int64, error) {
	key := c.genKeyOfImChat(fromUid, toUid)

	// 获取聊天记录的时间戳
	timestamp, err := c.cmder.Get(ctx, key).Int64()
	if errors.Is(err, redis.Nil) {
		return 0, nil // 没有记录
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "GetImChatRecord get key:%s err:%v", key, err)
		return 0, err
	}

	return timestamp, nil
}

// AddInteraction 添加互动记录
func (c *ChatAiCache) AddInteraction(ctx context.Context, uid, aiUid uint32) error {
	key := c.genKeyOfAiImInteraction(uid)
	now := time.Now().Unix()

	err := c.cmder.ZAdd(ctx, key, &redis.Z{
		Score:  float64(now),
		Member: strconv.Itoa(int(aiUid)),
	}).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddInteraction zadd key:%s err:%v", key, err)
		return err
	}

	return c.cmder.Expire(ctx, key, time.Hour*24).Err()
}

// GetInteractionAiList 获取互动过的ai用户列表
func (c *ChatAiCache) GetInteractionAiList(ctx context.Context, uid uint32) ([]uint32, error) {
	key := c.genKeyOfAiImInteraction(uid)

	members, err := c.cmder.ZRevRange(ctx, key, 0, -1).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetInteractionAiList zrevrange key:%s err:%v", key, err)
		return nil, err
	}

	aiUids := make([]uint32, 0, len(members))
	for _, member := range members {
		if aiUid, err := strconv.ParseUint(member, 10, 32); err == nil {
			aiUids = append(aiUids, uint32(aiUid))
		}
	}

	return aiUids, nil
}

// AddLikePost 记录每个用户当天被AI账号点赞的帖子
func (c *ChatAiCache) AddLikePost(ctx context.Context, uid uint32, postId string) error {
	key := c.genKeyOfAiLikePost(uid)
	err := c.cmder.SAdd(ctx, key, postId).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddLikePost sadd key:%s err:%v", key, err)
		return err
	}

	// 设置过期时间到当天23:59:59，需要加一个五分钟内的随机数打散过期时间
	randomOffset := time.Duration(rand.Intn(5)) * time.Minute
	now := time.Now()
	endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location()).Add(randomOffset)
	return c.cmder.ExpireAt(ctx, key, endOfDay).Err()
}

// GetLikePostCount 获取用户今天被ai点赞帖子数
func (c *ChatAiCache) GetLikePostCount(ctx context.Context, uid uint32) (int64, error) {
	key := c.genKeyOfAiLikePost(uid)
	count, err := c.cmder.SCard(ctx, key).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLikePostCount scard key:%s err:%v", key, err)
		return 0, err
	}

	return count, nil
}

// AddCommentPost 记录每个用户当天被AI账号评论的帖子
func (c *ChatAiCache) AddCommentPost(ctx context.Context, uid uint32, postId string) error {
	key := c.genKeyOfAiCommentPost(uid)
	err := c.cmder.SAdd(ctx, key, postId).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddCommentPost sadd key:%s err:%v", key, err)
		return err
	}

	// 设置过期时间到当天23:59:59，需要加一个五分钟内的随机数打散过期时间
	randomOffset := time.Duration(rand.Intn(5)) * time.Minute
	now := time.Now()
	endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location()).Add(randomOffset)
	return c.cmder.ExpireAt(ctx, key, endOfDay).Err()
}

// GetCommentPostCount 获取用户今天被ai评论帖子数
func (c *ChatAiCache) GetCommentPostCount(ctx context.Context, uid uint32) (int64, error) {
	key := c.genKeyOfAiCommentPost(uid)
	count, err := c.cmder.SCard(ctx, key).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommentPostCount scard key:%s err:%v", key, err)
		return 0, err
	}

	return count, nil
}

// IncrPostAiCommentCount 增加ai对帖子的评论次数
func (c *ChatAiCache) IncrPostAiCommentCount(ctx context.Context, postId string, aiUid uint32) (int64, error) {
	key := c.genKeyOfPostAiCommentCount(postId)
	field := strconv.Itoa(int(aiUid))

	count, err := c.cmder.HIncrBy(ctx, key, field, 1).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "IncrPostAiCommentCount hincrby key:%s err:%v", key, err)
		return 0, err
	}

	// 设置15天过期时间
	c.cmder.Expire(ctx, key, time.Hour*24*15)

	return count, nil
}

// GetPostAiCommentCount 获取帖子所有评论的ai账号列表和对应的评论次数
func (c *ChatAiCache) GetPostAiCommentCount(ctx context.Context, postId string, aiUid uint32) (int64, error) {
	key := c.genKeyOfPostAiCommentCount(postId)
	field := strconv.Itoa(int(aiUid))

	count, err := c.cmder.HGet(ctx, key, field).Int64()
	if errors.Is(err, redis.Nil) {
		return 0, nil
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPostAiCommentCount hget key:%s err:%v", key, err)
		return 0, err
	}

	return count, nil
}

// GetPostCommentAiList 获取帖子所有评论的ai账号列表
func (c *ChatAiCache) GetPostCommentAiList(ctx context.Context, postId string) (map[uint32]int64, error) {
	key := c.genKeyOfPostAiCommentCount(postId)

	// 获取所有评论的ai账号及其评论次数
	aiComments, err := c.cmder.HGetAll(ctx, key).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPostCommentAiList hgetall key:%s err:%v", key, err)
		return nil, err
	}

	// 转换为map[uint32]int64格式
	result := make(map[uint32]int64, len(aiComments))
	for aiUidStr, countStr := range aiComments {
		if aiUid, err := strconv.ParseUint(aiUidStr, 10, 32); err == nil {
			if count, err := strconv.ParseInt(countStr, 10, 64); err == nil {
				result[uint32(aiUid)] = count
			}
		}
	}

	return result, nil
}
