package redis

import (
	"context"
	"fmt"
	redis_sdk "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/aigc/aigc-account/internal/cache"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
	"golang.52tt.com/services/aigc/aigc-account/internal/infra/db"
	"strconv"
	"time"
)

type AiRoomCache struct {
	cmder *db.RedisDB
}

func NewAiRoomCache(database *db.RedisDB) cache.AiRoomCache {
	return &AiRoomCache{
		cmder: database,
	}
}

func (s *AiRoomCache) getRunningRoomKey() string {
	return "ai_room_running"
}

// cid + uid 以后可扩展 ai在多个房间运营
func (s *AiRoomCache) genRunningMember(cid, uid uint32) string {
	return fmt.Sprintf("%d_%d", cid, uid)
}

func (s *AiRoomCache) SetRunningRoom(ctx context.Context, room *entity.RoomInfo, expireAt int64) error {
	return s.cmder.ZAdd(ctx, s.getRunningRoomKey(), &redis_sdk.Z{
		Score:  float64(expireAt),
		Member: s.genRunningMember(room.Cid, room.Uid),
	}).Err()
}

func (s *AiRoomCache) BatDelRunningRoom(ctx context.Context, room []*entity.RoomInfo) error {
	members := make([]string, 0, len(room))
	for _, info := range room {
		members = append(members, s.genRunningMember(info.Cid, info.Uid))
	}
	return s.cmder.ZRem(ctx, s.getRunningRoomKey(), members).Err()
}

// 返回map key cid
func (s *AiRoomCache) BatGetRunningStatus(ctx context.Context, roomInfos []*entity.RoomInfo) (map[uint32]bool, error) {
	members := make([]string, 0, len(roomInfos))
	for _, info := range roomInfos {
		members = append(members, s.genRunningMember(info.Cid, info.Uid))
	}
	scores, err := s.cmder.ZMScore(ctx, s.getRunningRoomKey(), members...).Result()
	if err != nil {
		return nil, err
	}
	res := make(map[uint32]bool, len(roomInfos))
	curTime := time.Now().Unix()
	for i, score := range scores {
		if score == 0 || score < float64(curTime) {
			res[roomInfos[i].Cid] = false
		} else {
			res[roomInfos[i].Cid] = true
		}
	}
	return res, nil
}

func (s *AiRoomCache) GetChannelHeartbeatKey() string {
	return "ai_room_channel_heartbeat"
}

func (s *AiRoomCache) BatUpdateHeartbeat(ctx context.Context, cid []uint32) error {
	members := make([]*redis_sdk.Z, 0, len(cid))
	now := time.Now().Unix()
	for _, c := range cid {
		members = append(members, &redis_sdk.Z{
			// 尽量分散写入时间
			Score:  float64(now),
			Member: c,
		})
	}
	return s.cmder.ZAdd(ctx, s.GetChannelHeartbeatKey(), members...).Err()
}
func (s *AiRoomCache) GetNeedHeartbeatChannel(ctx context.Context, limit int64) ([]uint32, error) {
	members, err := s.cmder.ZRangeByScore(ctx, s.GetChannelHeartbeatKey(), &redis_sdk.ZRangeBy{
		Min:    "-inf",
		Max:    strconv.FormatInt(time.Now().Unix(), 10),
		Offset: 0,
		Count:  limit,
	}).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNeedHeartbeatChannel ZRangeByScore err:%v", err)
		return nil, err
	}
	res := make([]uint32, 0, len(members))
	for _, m := range members {
		cid, err := strconv.Atoi(m)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetNeedHeartbeatChannel Atoi err:%v", err)
			continue
		}
		res = append(res, uint32(cid))
	}
	return res, nil
}

func (s *AiRoomCache) BatRemoveHeartbeat(ctx context.Context, cid []uint32) error {
	members := make([]string, 0, len(cid))
	for _, c := range cid {
		members = append(members, strconv.Itoa(int(c)))
	}
	return s.cmder.ZRem(ctx, s.GetChannelHeartbeatKey(), members).Err()
}
