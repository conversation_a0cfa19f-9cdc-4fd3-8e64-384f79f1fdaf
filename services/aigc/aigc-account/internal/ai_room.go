package internal

import (
	"context"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account"
)

func (s *Server) UpdateAiRoomCfg(ctx context.Context, req *pb.UpdateAiRoomCfgRequest) (*pb.UpdateAiRoomCfgResponse, error) {
	resp := &pb.UpdateAiRoomCfgResponse{}

	return resp, nil
}

func (s *Server) GetAiRoomCfg(ctx context.Context, req *pb.GetAiRoomCfgRequest) (*pb.GetAiRoomCfgResponse, error) {
	resp := &pb.GetAiRoomCfgResponse{}

	return resp, nil
}

func (s *Server) GetRunningAiRooms(ctx context.Context, req *pb.GetRunningAiRoomsRequest) (*pb.GetRunningAiRoomsResponse, error) {
	resp := &pb.GetRunningAiRoomsResponse{}

	return resp, nil
}
