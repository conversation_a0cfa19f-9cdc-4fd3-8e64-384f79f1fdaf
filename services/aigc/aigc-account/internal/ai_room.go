package internal

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
)

func (s *Server) UpdateAiRoomCfg(ctx context.Context, req *pb.UpdateAiRoomCfgRequest) (*pb.UpdateAiRoomCfgResponse, error) {
	resp := &pb.UpdateAiRoomCfgResponse{}
	log.InfoWithCtx(ctx, "UpdateAiRoomCfg req: %s", req.String())
	if req.GetRoomCfg().GetUid() == 0 || req.GetRoomCfg().GetBaseCfg().GetCid() == 0 {
		log.ErrorWithCtx(ctx, "UpdateAiRoomCfg req: invalid request")
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	err := s.aiRoomMgr.UpdateAiRoomCfg(ctx, entity.NewEntity(req.GetRoomCfg()))
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAiRoomCfg req: %s, err: %v", req.String(), err)
		return resp, err
	}
	return resp, nil
}

func (s *Server) GetAiRoomCfg(ctx context.Context, req *pb.GetAiRoomCfgRequest) (*pb.GetAiRoomCfgResponse, error) {
	resp := &pb.GetAiRoomCfgResponse{}
	log.InfoWithCtx(ctx, "GetAiRoomCfg req: %s", req.String())
	if req.GetUid() == 0 {
		log.ErrorWithCtx(ctx, "GetAiRoomCfg req: invalid request")
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	cfg, err := s.aiRoomMgr.GetAiRoomCfg(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAiRoomCfg req: %s, err: %v", req.String(), err)
	}
	resp.RoomCfg = cfg.ToPB()
	return resp, nil
}

func (s *Server) GetRunningAiRooms(ctx context.Context, req *pb.GetRunningAiRoomsRequest) (*pb.GetRunningAiRoomsResponse, error) {
	resp := &pb.GetRunningAiRoomsResponse{}
	cfgs, loadFinish, err := s.aiRoomMgr.GetRunningAiRooms(ctx, req.GetLastCid(), req.GetLimit())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRunningAiRooms req: %s, err: %v", req.String(), err)
		return resp, err
	}
	resp.LoadFinish = loadFinish
	resp.RoomCfgList = make([]*pb.AiRoomCfg, 0, len(cfgs))
	for _, cfg := range cfgs {
		resp.RoomCfgList = append(resp.RoomCfgList, cfg.ToPB())
	}
	log.InfoWithCtx(ctx, "GetRunningAiRooms req: %s len(out):%d loadFinish:%v", req.String(),
		len(resp.GetRoomCfgList()), resp.GetLoadFinish())
	return resp, nil
}
