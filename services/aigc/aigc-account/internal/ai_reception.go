package internal

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account"
)

func (s *Server) RecordReceptionTime(ctx context.Context, req *pb.RecordReceptionTimeRequest) (resp *pb.RecordReceptionTimeResponse, err error) {
	resp = &pb.RecordReceptionTimeResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "RecordReceptionTime req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "RecordReceptionTime req:%+v,resp:%+v", req, resp)
		}
	}()

	err = s.aiRoomMgr.SaveLastReceptionTime(ctx, req.GetUid(), req.GetChannelId(), req.GetReceptionTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "RecordReceptionTime error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "记录失败")
	}
	return
}

func (s *Server) GetLastReceptionTime(ctx context.Context, req *pb.GetLastReceptionTimeRequest) (resp *pb.GetLastReceptionTimeResponse, err error) {
	resp = &pb.GetLastReceptionTimeResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "GetLastReceptionTime req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "GetLastReceptionTime req:%+v,resp:%+v", req, resp)
		}
	}()

	lastTime, err := s.aiRoomMgr.GetLastReceptionTime(ctx, req.GetAiUid(), req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLastReceptionTime error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "获取失败")
	}
	resp.LastReceptionTime = lastTime
	return
}

func (s *Server) RecordWaitingReceptionUser(ctx context.Context, req *pb.RecordWaitingReceptionUserRequest) (resp *pb.RecordWaitingReceptionUserResponse, err error) {
	resp = &pb.RecordWaitingReceptionUserResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "RecordWaitingReceptionUser req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "RecordWaitingReceptionUser req:%+v,resp:%+v", req, resp)
		}
	}()
	err = s.aiRoomMgr.SaveWaitingReceptionUser(ctx, req.GetAiUid(), req.GetChannelId(), req.GetUid(), req.GetRecordTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "RecordWaitingReceptionUser error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "记录失败")
	}
	return
}

func (s *Server) GetWaitingReceptionUser(ctx context.Context, req *pb.GetWaitingReceptionUserRequest) (resp *pb.GetWaitingReceptionUserResponse, err error) {
	resp = &pb.GetWaitingReceptionUserResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "GetWaitingReceptionUser req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "GetWaitingReceptionUser req:%+v,resp:%+v", req, resp)
		}
	}()
	uids, err := s.aiRoomMgr.GetWaitingReceptionUser(ctx, req.GetAiUid(), req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWaitingReceptionUser error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "获取失败")
	}
	for _, uid := range uids {
		resp.WaitingUserList = append(resp.WaitingUserList, &pb.WaitingReceptionUser{
			Uid:        uid.Uid,
			RecordTime: uid.RecordTime,
		})
	}
	return resp, nil
}

func (s *Server) RecordReceptionUser(ctx context.Context, req *pb.RecordReceptionUserRequest) (resp *pb.RecordReceptionUserResponse, err error) {
	resp = &pb.RecordReceptionUserResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "RecordReceptionUser req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "RecordReceptionUser req:%+v,resp:%+v", req, resp)
		}
	}()
	err = s.aiRoomMgr.SaveReceptionUser(ctx, req.GetAiUid(), req.GetChannelId(), req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "RecordReceptionUser error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "记录失败")
	}
	return
}

func (s *Server) CheckReceptionUser(ctx context.Context, req *pb.CheckReceptionUserRequest) (resp *pb.CheckReceptionUserResponse, err error) {
	resp = &pb.CheckReceptionUserResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "CheckReceptionUser req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "CheckReceptionUser req:%+v,resp:%+v", req, resp)
		}
	}()
	receptionMap, err := s.aiRoomMgr.IsReceptionUser(ctx, req.GetAiUid(), req.GetChannelId(), req.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckReceptionUser error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "获取失败")
	}
	resp.ReceptionMap = receptionMap
	return resp, nil
}

func (s *Server) RecordRoomUserNum(ctx context.Context, req *pb.RecordRoomUserNumRequest) (resp *pb.RecordRoomUserNumResponse, err error) {
	resp = &pb.RecordRoomUserNumResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "RecordRoomUserNum req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "RecordRoomUserNum req:%+v,resp:%+v", req, resp)
		}
	}()
	err = s.aiRoomMgr.SaveRoomUserNum(ctx, req.GetAiUid(), req.GetChannelId(), int(req.GetUserNum()))
	if err != nil {
		log.ErrorWithCtx(ctx, "RecordRoomUserNum error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "记录失败")
	}
	return
}

func (s *Server) GetRoomUserNum(ctx context.Context, req *pb.GetRoomUserNumRequest) (resp *pb.GetRoomUserNumResponse, err error) {
	resp = &pb.GetRoomUserNumResponse{}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "GetRoomUserNum req:%+v,err:%v", req, err)
		} else {
			log.InfoWithCtx(ctx, "GetRoomUserNum req:%+v,resp:%+v", req, resp)
		}
	}()
	userNum, err := s.aiRoomMgr.GetRoomUserNum(ctx, req.GetAiUid(), req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRoomUserNum error: %v", err)
		return resp, protocol.NewExactServerError(err, status.ErrRequestParamInvalid, "获取失败")
	}
	resp.UserNum = uint32(userNum)
	return resp, nil
}
