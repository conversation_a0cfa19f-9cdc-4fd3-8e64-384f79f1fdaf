package entity

import (
	pb "golang.52tt.com/protocol/services/aigc/aigc-account"
	"time"
)

type RoomConfig struct {
	// 房间基础配置
	Cid                uint32   `bson:"_id"`                  // 房间id
	Uid                uint32   `bson:"uid"`                  // AI账号 uid
	TabId              uint32   `bson:"tab_id"`               // 玩法Id
	ChannelAvatar      string   `bson:"channel_avatar"`       // 房间头像
	ChannelName        string   `bson:"channel_name"`         // 房间名称
	WelcomeMsg         string   `bson:"welcome_msg"`          // 房间欢迎语
	ChannelTopicTitle  string   `bson:"channel_topic_title"`  //  房间话题-标题
	ChannelTopicDetail string   `bson:"channel_topic_detail"` // 房间话题-详情
	ChannelSupervisor  uint32   `bson:"channel_supervisor"`   // 房间超管
	ChannelAdmin       []uint32 `bson:"channel_admin"`        // 房间管理员
	LockMicList        []uint32 `bson:"lock_mic_list"`        // 锁定麦位列表

	// 业务配置信息
	AIUseMicIndex           uint32            `bson:"ai_use_mic_index"`           // AI使用麦位
	PublishUserNumThreshold uint32            `bson:"publish_user_num_threshold"` // 允许发布的用户数阈值
	DismissUserNumThreshold uint32            `bson:"dismiss_user_num_threshold"` // 自动取消发布房间的用户数
	OperationTimes          []TimeRange       `bson:"operation_times"`            // 运营时间段
	PublishTimes            []TimeRange       `bson:"publish_times"`              // 房间发布时间段
	ChannelBgm              string            // 房间BGM
	PublishOptions          []*pb.BlockOption `bson:"publish_options"` // 发布选项

	// AIGC相关配置
	PromptId         string   `bson:"prompt_id"`          // AIGC中台模型ID
	Timbre           string   `bson:"timbre"`             // AIGC中台音色配置ID
	ReceptionMsgList []string `bson:"reception_msg_list"` //进房欢迎内容
	Script           []string `bson:"script"`             // 脚本

	UpdateTime time.Time `bson:"update_time"` // 更新时间
}

type TimeRange struct {
	StartTime int64 `bson:"start_time"` // 开始时间，单位秒
	EndTime   int64 `bson:"end_time"`   // 结束时间，单位秒
}

func (cfg *RoomConfig) ToPB() *pb.AiRoomCfg {

	if cfg == nil {
		return nil
	}
	pbcfg := &pb.AiRoomCfg{
		Uid: cfg.Uid,
		BaseCfg: &pb.RoomBaseCfg{
			Cid:                cfg.Cid,
			TabId:              cfg.TabId,
			ChannelAvatar:      cfg.ChannelAvatar,
			ChannelName:        cfg.ChannelName,
			WelcomeMsg:         cfg.WelcomeMsg,
			ChannelTopicTitle:  cfg.ChannelTopicTitle,
			ChannelTopicDetail: cfg.ChannelTopicDetail,
			ChannelSupervisor:  cfg.ChannelSupervisor,
			ChannelAdmin:       cfg.ChannelAdmin,
			LockMicList:        cfg.LockMicList,
		},
		BusinessCfg: &pb.BusinessCfg{
			AiUseMicIndex:      cfg.AIUseMicIndex,
			AutoDismissUserNum: cfg.DismissUserNumThreshold,
			AutoPublishUserNum: cfg.PublishUserNumThreshold,
			ChannelBgm:         cfg.ChannelBgm,
			PublishOptions:     cfg.PublishOptions,
			PromptId:           cfg.PromptId,
			Timbre:             cfg.Timbre,
			ReceptionMsgList:   cfg.ReceptionMsgList,
			Script:             cfg.Script,
		},
	}
	for _, tr := range cfg.OperationTimes {
		pbcfg.BusinessCfg.OperationTimes = append(pbcfg.BusinessCfg.OperationTimes, &pb.TimeRange{
			StartTime: tr.StartTime,
			EndTime:   tr.EndTime,
		})
	}
	for _, tr := range cfg.PublishTimes {
		pbcfg.BusinessCfg.PublishTimes = append(pbcfg.BusinessCfg.PublishTimes, &pb.TimeRange{
			StartTime: tr.StartTime,
			EndTime:   tr.EndTime,
		})
	}
	return pbcfg
}

func (cfg *RoomConfig) ToEntity(pbInst *pb.AiRoomCfg) {
	if cfg == nil || pbInst == nil {
		return
	}
	cfg.Uid = pbInst.GetUid()
	if pbInst.GetBaseCfg() != nil {
		cfg.Cid = pbInst.GetBaseCfg().GetCid()
		cfg.TabId = pbInst.GetBaseCfg().GetTabId()
		cfg.ChannelAvatar = pbInst.GetBaseCfg().GetChannelAvatar()
		cfg.ChannelName = pbInst.GetBaseCfg().GetChannelName()
		cfg.WelcomeMsg = pbInst.GetBaseCfg().GetWelcomeMsg()
		cfg.ChannelTopicTitle = pbInst.GetBaseCfg().GetChannelTopicTitle()
		cfg.ChannelTopicDetail = pbInst.GetBaseCfg().GetChannelTopicDetail()
		cfg.ChannelSupervisor = pbInst.GetBaseCfg().GetChannelSupervisor()
		cfg.ChannelAdmin = pbInst.GetBaseCfg().GetChannelAdmin()
		cfg.LockMicList = pbInst.GetBaseCfg().GetLockMicList()
	}
	if pbInst.GetBusinessCfg() != nil {
		cfg.AIUseMicIndex = pbInst.GetBusinessCfg().GetAiUseMicIndex()
		cfg.DismissUserNumThreshold = pbInst.GetBusinessCfg().GetAutoDismissUserNum()
		cfg.PublishUserNumThreshold = pbInst.GetBusinessCfg().GetAutoPublishUserNum()
		cfg.OperationTimes = convertPbTimeRanges(pbInst.GetBusinessCfg().GetOperationTimes())
		cfg.PublishTimes = convertPbTimeRanges(pbInst.GetBusinessCfg().GetPublishTimes())
		cfg.ChannelBgm = pbInst.GetBusinessCfg().GetChannelBgm()
		cfg.PublishOptions = pbInst.GetBusinessCfg().GetPublishOptions()
		cfg.PromptId = pbInst.GetBusinessCfg().GetPromptId()
		cfg.Timbre = pbInst.GetBusinessCfg().GetTimbre()
		cfg.ReceptionMsgList = pbInst.GetBusinessCfg().GetReceptionMsgList()
		cfg.Script = pbInst.GetBusinessCfg().GetScript()
	}

	cfg.UpdateTime = time.Now()
}

func NewEntity(pbInst *pb.AiRoomCfg) *RoomConfig {
	if pbInst == nil {
		return nil
	}
	cfg := &RoomConfig{}
	cfg.ToEntity(pbInst)
	return cfg
}

func convertPbTimeRanges(ranges []*pb.TimeRange) []TimeRange {
	if len(ranges) == 0 {
		return nil
	}
	trs := make([]TimeRange, 0, len(ranges))
	for _, tr := range ranges {
		trs = append(trs, TimeRange{
			StartTime: tr.GetStartTime(),
			EndTime:   tr.GetEndTime(),
		})
	}
	return trs
}
