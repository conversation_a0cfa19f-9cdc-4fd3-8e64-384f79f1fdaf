package entity

import pb "golang.52tt.com/protocol/services/aigc/aigc-account"

type RoomConfig struct {
	// 房间基础配置
	Cid                uint32   `bson:"_id"`                  // 房间id
	Uid                uint32   `bson:"uid"`                  // AI账号 uid
	TabId              uint32   `bson:"tab_id"`               // 玩法Id
	ChannelAvatar      string   `bson:"channel_avatar"`       // 房间头像
	ChannelName        string   `bson:"channel_name"`         // 房间名称
	WelcomeMsg         string   `bson:"welcome_msg"`          // 房间欢迎语
	ChannelTopicTitle  string   `bson:"channel_topic_title"`  //  房间话题-标题
	ChannelTopicDetail string   `bson:"channel_topic_detail"` // 房间话题-详情
	ChannelSupervisor  uint32   `bson:"channel_supervisor"`   // 房间超管
	ChannelAdmin       []uint32 `bson:"channel_admin"`        // 房间管理员
	LockMicList        []uint32 `bson:"lock_mic_list"`        // 锁定麦位列表

	// 业务配置信息
	AIUseMicIndex           uint32            `bson:"ai_use_mic_index"`           // AI使用麦位
	PublishUserNumThreshold uint32            `bson:"publish_user_num_threshold"` // 允许发布的用户数阈值
	OperationTimes          []TimeRange       `bson:"operation_times"`            // 运营时间段
	PublishTimes            []TimeRange       `bson:"publish_times"`              // 房间发布时间段
	ChannelBgm              string            // 房间BGM
	PublishOptions          []*pb.BlockOption `bson:"publish_options"` // 发布选项

	// AIGC相关配置
	PromptId         string   `bson:"prompt_id"`          // AIGC中台模型ID
	Timbre           string   `bson:"timbre"`             // AIGC中台音色配置ID
	ReceptionMsgList []string `bson:"reception_msg_list"` //进房欢迎内容
	Script           string   `bson:"script"`             // 脚本

}

type TimeRange struct {
	StartTime int64 `bson:"start_time"` // 开始时间，单位秒
	EndTime   int64 `bson:"end_time"`   // 结束时间，单位秒
}
