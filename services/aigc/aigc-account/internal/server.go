package internal

import (
	"context"
	"golang.52tt.com/services/aigc/aigc-account/internal/cache/redis"
	"golang.52tt.com/services/aigc/aigc-account/internal/mgr/chat"
	"golang.52tt.com/services/aigc/aigc-account/internal/mgr/post"
	"golang.52tt.com/services/aigc/aigc-account/internal/mgr/room"
	"golang.52tt.com/services/aigc/aigc-account/internal/rpc"
	"golang.52tt.com/services/aigc/aigc-account/internal/timer"
	"net"

	"gitlab.ttyuyin.com/tyr/x/log"

	connect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
	"golang.52tt.com/services/aigc/aigc-account/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-account/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-account/internal/mgr/account"
	"golang.52tt.com/services/aigc/aigc-account/internal/store/mongo"
)

type StartConfig struct {
	// from config file
	Mongo *config.MongoConfig  `json:"mongo"`
	Redis *connect.RedisConfig `json:"redis"`

	SyncGenderFrequency uint32 `json:"sync_gender_frequency"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	s := &Server{}

	// 初始化MongoDB连接
	mongoDB, err := db.NewMongoDB(ctx, cfg.Mongo)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewMongoDB cfg(%+v) err: %v", cfg, err)
		return nil, err
	}

	// 初始化Mongo
	aiAccountStore := mongo.NewAIAccountMongoStore(ctx, mongoDB)
	chatTimeStore := mongo.NewChatTimeMongoStore(ctx, mongoDB)
	aiPostStore := mongo.NewAIPostMongoStore(ctx, mongoDB)
	aiRoomStore := mongo.NewAiRoomStore(ctx, mongoDB)

	// 初始化redis
	redisDB, err := db.NewRedisDB(ctx, cfg.Redis)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewRedisDB cfg(%+v) err: %v", cfg, err)
		return nil, err
	}
	chatRoundCache := redis.NewChatRoundRedisCache(redisDB)
	// 初始化ChatAiCache
	chatAiCache := redis.NewChatAiCache(redisDB)
	// 初始化 AiReceptionCache
	aiReceptionCache := redis.NewAiReceptionCache(redisDB)
	// 初始化 AiReceptionCache
	aiRoomCache := redis.NewAiRoomCache(redisDB)

	clients, err := rpc.NewClients()
	if err != nil {
		log.Errorf("init rpc clients failed: %v", err)
		return nil, err
	}

	// 初始化Mgr
	aiAccountMgr := account.NewManager(aiAccountStore, clients)
	// 初始化ChatManager
	chatMgr := chat.NewManager(chatTimeStore, chatRoundCache)
	// 初始化aiPostManager
	aiPostMgr := post.NewManager(aiPostStore, chatAiCache)
	// 初始化AiRoomManager
	aiRoomMgr := room.NewManager(clients, aiReceptionCache, aiRoomStore, aiRoomCache)

	s.mongoDB = mongoDB
	s.aiAccountMgr = aiAccountMgr
	s.chatMgr = chatMgr
	s.aiPostMgr = aiPostMgr
	s.aiRoomMgr = aiRoomMgr

	// 初始化定时任务
	svrTimer, err := timer.InitTimer(ctx, redisDB, aiAccountMgr, cfg.SyncGenderFrequency)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewRedisTimer err: %v", err)
		return nil, err
	}
	svrTimer.Start()

	return s, nil
}

type Server struct {
	mongoDB      *db.MongoDB
	aiAccountMgr mgr.AIAccountManager
	chatMgr      mgr.ChatManager
	aiPostMgr    mgr.AiPostManager
	redisTimer   *timer.RedisTimer
	aiRoomMgr    mgr.AiRoomManager
}

func (s *Server) ShutDown() {
	if s.mongoDB != nil {
		_ = s.mongoDB.Close(context.Background())
	}

	if s.redisTimer != nil {
		s.redisTimer.Stop()
	}
}

// CreateAIAccount 创建AI账号
func (s *Server) CreateAIAccount(ctx context.Context, req *pb.CreateAIAccountRequest) (*pb.CreateAIAccountResponse, error) {
	resp := &pb.CreateAIAccountResponse{}

	logReq := *req.GetAccount()
	logReq.Password = ""
	log.InfoWithCtx(ctx, "CreateAIAccount req: %+v", logReq)

	if err := s.checkAIAccount(ctx, req.GetAccount()); err != nil {
		log.ErrorWithCtx(ctx, "CreateAIAccount checkAIAccount err: %v, req: %+v", err, logReq)
		return resp, err
	}

	aiAccount := entity.NewAIAccountFromPb(req.GetAccount())
	if err := s.aiAccountMgr.CreateAIAccount(ctx, aiAccount); err != nil {
		log.ErrorWithCtx(ctx, "CreateAIAccount err: %v, req: %+v", err, logReq)
		return resp, err
	}

	log.InfoWithCtx(ctx, "CreateAIAccount success, req(%+v)", logReq)
	return resp, nil
}

func (s *Server) checkAIAccount(ctx context.Context, aiAccount *pb.AIAccount) error {
	if aiAccount.GetUid() == 0 || len(aiAccount.GetPassword()) == 0 {
		log.ErrorWithCtx(ctx, "checkCreateAIAccount invalid uid(%d) or len(password):%d == 0", aiAccount.GetUid(), len(aiAccount.GetPassword()))
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "必填字段不能为空")
	}
	if len(aiAccount.GetIp()) > 0 {
		// 检查IP是否合法
		if net.ParseIP(aiAccount.GetIp()) == nil {
			log.ErrorWithCtx(ctx, "checkCreateAIAccount invalid ip(%s)", aiAccount.GetIp())
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "IP格式不正确, 正确格式如: ***********")
		}
	}
	return nil
}

// UpdateAIAccount 更新AI账号
func (s *Server) UpdateAIAccount(ctx context.Context, req *pb.UpdateAIAccountRequest) (*pb.UpdateAIAccountResponse, error) {
	resp := &pb.UpdateAIAccountResponse{}

	logReq := *req.GetAccount()
	logReq.Password = ""
	log.InfoWithCtx(ctx, "UpdateAIAccount req: %+v", logReq)

	if err := s.checkAIAccount(ctx, req.GetAccount()); err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIAccount checkAIAccount err: %v, req: %+v", err, logReq)
		return resp, err
	}

	aiAccount, err := s.aiAccountMgr.GetAIAccount(ctx, req.GetAccount().GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIAccount GetAIAccount err: %v, req: %+v", err, logReq)
		return resp, err
	}
	if aiAccount == nil {
		log.ErrorWithCtx(ctx, "UpdateAIAccount aiAccount(uid:%d) not found, req: %+v", req.GetAccount().GetUid(), logReq)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "账号不存在")
	}

	aiAccount.Update(req.GetAccount())
	if err := s.aiAccountMgr.UpdateAIAccount(ctx, aiAccount); err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIAccount err: %v, req: %+v", err, logReq)
		return resp, err
	}

	log.InfoWithCtx(ctx, "UpdateAIAccount success, req(%+v)", logReq)
	return resp, nil
}

// BatchUnregisterAIAccount 批量注销AI账号
func (s *Server) BatchUnregisterAIAccount(ctx context.Context, req *pb.BatchUnregisterAIAccountRequest) (*pb.BatchUnregisterAIAccountResponse, error) {
	resp := &pb.BatchUnregisterAIAccountResponse{}
	log.InfoWithCtx(ctx, "BatchUnregisterAIAccount req: %+v", req)

	if len(req.GetUidList()) == 0 {
		log.WarnWithCtx(ctx, "BatchUnregisterAIAccount req.UidList is empty")
		return resp, nil
	}

	if err := s.aiAccountMgr.BatchUnregisterAIAccount(ctx, req.GetUidList()); err != nil {
		log.ErrorWithCtx(ctx, "BatchUnregisterAIAccount err: %v, req: %+v", err, req)
		return resp, err
	}

	log.InfoWithCtx(ctx, "BatchUnregisterAIAccount success, req(%+v)", req)
	return resp, nil
}

// GetPageAIAccount 获取AI账号列表（运营后台）
func (s *Server) GetPageAIAccount(ctx context.Context, req *pb.GetPageAIAccountRequest) (*pb.GetPageAIAccountResponse, error) {
	resp := &pb.GetPageAIAccountResponse{}
	log.InfoWithCtx(ctx, "GetPageAIAccount req: %+v", req)

	accountList, total, err := s.aiAccountMgr.GetPageAIAccount(ctx, req.GetPage(), req.GetSize(), req.GetChatCardWallShowStatus())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPageAIAccount err: %v, req: %+v", err, req)
		return resp, err
	}

	resp.AccountList = accountList.ToPbList(pb.GetAIAccountSource_ADMIN_BACKEND)
	resp.Total = total

	log.InfoWithCtx(ctx, "GetPageAIAccount req(%+v) len(resp.AccountList): %d, total: %d", req, len(resp.AccountList), resp.Total)
	return resp, nil
}

// GetAIAccount 根据uid获取AI账号
func (s *Server) GetAIAccount(ctx context.Context, req *pb.GetAIAccountRequest) (*pb.GetAIAccountResponse, error) {
	resp := &pb.GetAIAccountResponse{}
	log.InfoWithCtx(ctx, "GetAIAccount req: %+v", req)

	if req.GetUid() == 0 {
		log.WarnWithCtx(ctx, "GetAIAccount req.Uid is empty")
		return resp, nil
	}

	aiAccount, err := s.aiAccountMgr.GetAIAccount(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIAccount err: %v, req: %+v", err, req)
		return resp, err
	}

	if aiAccount == nil {
		log.WarnWithCtx(ctx, "GetAIAccount aiAccount(uid:%d) not exist", req.GetUid())
		return resp, nil
	}

	resp.Account = aiAccount.ToPb(req.GetReqSource())

	logResp := *resp.GetAccount()
	logResp.Password = ""
	log.InfoWithCtx(ctx, "GetAIAccount req(%+v) resp: %+v", req, logResp)
	return resp, nil
}

// BatchGetAIAccount 批量获取AI账号
func (s *Server) BatchGetAIAccount(ctx context.Context, req *pb.BatchGetAIAccountRequest) (*pb.BatchGetAIAccountResponse, error) {
	resp := &pb.BatchGetAIAccountResponse{}
	log.InfoWithCtx(ctx, "BatchGetAIAccount req: %+v", req)

	if len(req.GetUidList()) == 0 {
		log.WarnWithCtx(ctx, "BatchGetAIAccount req.UidList is empty")
		return resp, nil
	}

	aiAccountList, err := s.aiAccountMgr.BatchGetAIAccount(ctx, req.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAIAccount err: %v, req: %+v", err, req)
		return resp, err
	}

	resp.AccountList = aiAccountList.ToPbList(pb.GetAIAccountSource_DEFAULT)

	log.InfoWithCtx(ctx, "BatchGetAIAccount req(%+v) len(resp.AccountList): %d", req, len(resp.AccountList))
	return resp, nil
}

func (s *Server) GetAIAccountByGender(ctx context.Context, req *pb.GetAIAccountByGenderRequest) (*pb.GetAIAccountByGenderResponse, error) {
	resp := &pb.GetAIAccountByGenderResponse{}
	log.InfoWithCtx(ctx, "GetAIAccountByGender req: %+v", req)

	if req.GetLimit() == 0 {
		req.Limit = 10 // 默认限制为10
	}

	aiAccountList, err := s.aiAccountMgr.GetAIAccountsBySex(ctx, req.GetSex(), int64(req.GetLimit()))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIAccountByGender err: %v, req: %+v", err, req)
		return resp, err
	}
	resp.AccountList = aiAccountList.ToPbList(pb.GetAIAccountSource_DEFAULT)
	log.InfoWithCtx(ctx, "GetAIAccountByGender req(%+v) len(resp.AccountList): %d", req, len(resp.AccountList))
	return resp, nil
}
