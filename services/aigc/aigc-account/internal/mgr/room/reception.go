package room

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-account/internal/cache"
	"golang.52tt.com/services/aigc/aigc-account/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-account/internal/rpc"
	"golang.52tt.com/services/aigc/aigc-account/internal/store"
)

type manager struct {
	aiReception cache.AiReception
	aiRoomStore store.AIRoomStore
	clients     *rpc.Clients
}

func NewManager(clients *rpc.Clients, chatAiCache cache.AiReception, aiRoomStore store.AIRoomStore) mgr.AiRoomManager {
	return &manager{
		clients:     clients,
		aiReception: chatAiCache,
		aiRoomStore: aiRoomStore,
	}
}

// SaveLastReceptionTime 记录最后一次接待时间
func (m *manager) SaveLastReceptionTime(ctx context.Context, uid, channelId uint32, receptionTime int64) error {
	return m.aiReception.SaveLastReceptionTime(ctx, uid, channelId, receptionTime)
}

// GetLastReceptionTime 获取最后一次接待时间
func (m *manager) GetLastReceptionTime(ctx context.Context, aidUid, channelId uint32) (int64, error) {
	return m.aiReception.GetLastReceptionTime(ctx, aidUid, channelId)
}

// SaveWaitingReceptionUser 记录等待接待用户
func (m *manager) SaveWaitingReceptionUser(ctx context.Context, aiUid, channelId, uid uint32, recordTime int64) error {
	return m.aiReception.SaveWaitingReceptionUser(ctx, aiUid, channelId, uid, recordTime)
}

// GetWaitingReceptionUser 获取等待接待用户
func (m *manager) GetWaitingReceptionUser(ctx context.Context, aiUid, channelId uint32) ([]cache.WaitingReceptionUser, error) {
	return m.aiReception.GetWaitingReceptionUser(ctx, aiUid, channelId)
}

// SaveReceptionUser 记录接待用户
func (m *manager) SaveReceptionUser(ctx context.Context, aiUid, channelId, uid uint32) error {
	return m.aiReception.SaveReceptionUser(ctx, aiUid, channelId, uid)
}

// IsReceptionUser 判断是否是接待用户
func (m *manager) IsReceptionUser(ctx context.Context, aiUid, channelId uint32, uidList []uint32) (map[uint32]bool, error) {
	receptionMap := make(map[uint32]bool, len(uidList))
	for _, uid := range uidList {
		receptionStatus, err := m.aiReception.IsReceptionUser(ctx, aiUid, channelId, uid)
		if err != nil {
			log.WarnWithCtx(ctx, "IsReceptionUser IsReceptionUser err:%v, aiUid:%d, channelId:%d, uid:%d", err, aiUid, channelId, uid)
			continue
		}
		receptionMap[uid] = receptionStatus
	}

	return receptionMap, nil
}

// SaveRoomUserNum 记录房间用户数
func (m *manager) SaveRoomUserNum(ctx context.Context, aiUid, channelId uint32, userNum int) error {
	return m.aiReception.SaveRoomUserNum(ctx, aiUid, channelId, userNum)
}

// GetRoomUserNum 获取房间用户数
func (m *manager) GetRoomUserNum(ctx context.Context, aiUid, channelId uint32) (int, error) {
	return m.aiReception.GetRoomUserNum(ctx, aiUid, channelId)
}
