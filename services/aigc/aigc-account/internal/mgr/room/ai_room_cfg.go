package room

import (
	"context"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
)

func (m *manager) UpdateAiRoomCfg(ctx context.Context, cfg *entity.RoomConfig) error {
	return m.aiRoomStore.Update(ctx, cfg)
}

func (m *manager) GetAiRoomCfg(ctx context.Context, uid uint32) (*entity.RoomConfig, error) {
	return m.aiRoomStore.Get(ctx, uid)
}

func (m *manager) GetRunningAiRooms(ctx context.Context, lastCid uint32, limit uint32) ([]*entity.RoomConfig, bool, error) {
	cfgs, err := m.aiRoomStore.GetRunningRooms(ctx, lastCid, limit+1)
	if err != nil {
		return nil, false, err
	}
	if len(cfgs) <= int(limit) {
		return cfgs, true, nil
	} else {
		return cfgs[:limit+1], false, nil
	}

}
