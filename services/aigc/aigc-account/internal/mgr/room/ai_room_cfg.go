package room

import (
	"context"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
)

func (m *manager) UpdateAiRoomCfg(ctx context.Context, cfg *entity.RoomConfig) error {
	return m.aiRoomStore.Update(ctx, cfg)
}

func (m *manager) GetAiRoomCfg(ctx context.Context, uid uint32) (*entity.RoomConfig, error) {
	return m.aiRoomStore.Get(ctx, uid)
}

func (m *manager) GetRunningAiRooms(ctx context.Context, lastCid uint32, limit uint32) ([]*entity.RoomConfig, bool, error) {
	cfgs, err := m.aiRoomStore.GetRunningRooms(ctx, lastCid, limit+1)
	if err != nil {
		return nil, false, err
	}
	if len(cfgs) <= int(limit) {
		return cfgs, true, nil
	} else {
		return cfgs[:limit], false, nil
	}

}

func (m *manager) AddRunningRoom(ctx context.Context, cid, uid uint32, expiredAt int64) error {
	return m.aiRoomCache.SetRunningRoom(ctx, &entity.RoomInfo{
		Cid: cid,
		Uid: uid,
	}, expiredAt)
}

func (m *manager) BatGetRunningStatus(ctx context.Context, roomInfos []*entity.RoomInfo) (map[uint32]bool, error) {
	return m.aiRoomCache.BatGetRunningStatus(ctx, roomInfos)
}

func (m *manager) BatDelRunningRoom(ctx context.Context, roomInfos []*entity.RoomInfo) error {
	return m.aiRoomCache.BatDelRunningRoom(ctx, roomInfos)
}

func (m *manager) BatUpdateHeartbeat(ctx context.Context, cid []uint32) error {
	return m.aiRoomCache.BatUpdateHeartbeat(ctx, cid)
}

func (m *manager) GetNeedHeartbeatChannel(ctx context.Context, limit int64) ([]uint32, error) {
	return m.aiRoomCache.GetNeedHeartbeatChannel(ctx, limit)
}

func (m *manager) BatRemoveHeartbeat(ctx context.Context, cid []uint32) error {
	return m.aiRoomCache.BatRemoveHeartbeat(ctx, cid)
}
