package mgr

import (
	"context"
	"golang.52tt.com/services/aigc/aigc-account/internal/cache"

	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
)

//go:generate mockgen -destination=mocks/account.go -package=mocks golang.52tt.com/services/aigc/aigc-account/internal/mgr AIAccountManager
type AIAccountManager interface {
	// 创建AI账号
	CreateAIAccount(ctx context.Context, aiAccount *entity.AIAccount) error
	// 更新AI账号
	UpdateAIAccount(ctx context.Context, aiAccount *entity.AIAccount) error
	// 批量删除AI账号
	//BatchDeleteAIAccount(ctx context.Context, idList []uint32) error
	// 批量注销AI账号
	BatchUnregisterAIAccount(ctx context.Context, idList []uint32) error

	// 分页获取AI账号列表
	GetPageAIAccount(ctx context.Context, page, size uint32, chatCardWallShowStatus uint32) (entity.AIAccountList, uint32, error)
	// 获取单个AI账号
	GetAIAccount(ctx context.Context, id uint32) (*entity.AIAccount, error)
	// 批量获取AI账号
	BatchGetAIAccount(ctx context.Context, idList []uint32) (entity.AIAccountList, error)
	// SyncAiAccountGender 同步AI账号性别
	SyncAiAccountGender(ctx context.Context) error
	// GetAIAccountsBySex 获取指定性别的AI账号列表
	GetAIAccountsBySex(ctx context.Context, sex int32, limit int64) (entity.AIAccountList, error)
}

//go:generate mockgen -destination=mocks/chat_round.go -package=mocks golang.52tt.com/services/aigc/aigc-account/internal/mgr ChatManager
type ChatManager interface {
	GetLastChatTime(ctx context.Context, uid, aiUid uint32) (int64, error)
	UpdateChatTime(ctx context.Context, uid, aiUid uint32) error
	IncrChatRound(ctx context.Context, uid, aiUid uint32) error
	GetSpecialChatRound(ctx context.Context, uid, aiUid uint32) (uint32, error)
	GetTotalChatRound(ctx context.Context, uid uint32) (uint32, error)
}

//go:generate mockgen -destination=mocks/post.go -package=mocks golang.52tt.com/services/aigc/aigc-account/internal/mgr AiPostManager
type AiPostManager interface {
	AddAiPost(ctx context.Context, uid uint32, postId string) error
	GetAiPostList(ctx context.Context, page, size int64, uid []uint32) ([]*entity.AIPost, int64, error)

	// AI聊天相关接口
	AddUserAiChatRecord(ctx context.Context, fromUid, toUid uint32) error
	GetUserAiChatRecord(ctx context.Context, fromUid, toUid uint32) (int64, error)
	AddInteraction(ctx context.Context, uid, aiUid uint32) error
	GetInteractionAiList(ctx context.Context, uid uint32) ([]uint32, error)
	AddLikePost(ctx context.Context, uid uint32, postId string) error
	GetLikePostCount(ctx context.Context, uid uint32) (int64, error)
	AddCommentPost(ctx context.Context, uid uint32, postId string) error
	GetCommentPostCount(ctx context.Context, uid uint32) (int64, error)
	IncrPostAiCommentCount(ctx context.Context, postId string, aiUid uint32) (int64, error)
	GetPostAiCommentCount(ctx context.Context, postId string, aiUid uint32) (int64, error)
	GetPostCommentAiList(ctx context.Context, postId string) (map[uint32]int64, error)
}

//go:generate mockgen -destination=mocks/ai_room.go -package=mocks golang.52tt.com/services/aigc/aigc-account/internal/mgr AiRoomManager
type AiRoomManager interface {
	SaveLastReceptionTime(ctx context.Context, uid, channelId uint32, receptionTime int64) error
	GetLastReceptionTime(ctx context.Context, aidUid, channelId uint32) (int64, error)
	SaveWaitingReceptionUser(ctx context.Context, aiUid, channelId, uid uint32, recordTime int64) error
	GetWaitingReceptionUser(ctx context.Context, aiUid, channelId uint32) ([]cache.WaitingReceptionUser, error)
	SaveReceptionUser(ctx context.Context, aiUid, channelId uint32, uid []uint32) error
	IsReceptionUser(ctx context.Context, aiUid, channelId uint32, uidList []uint32) (map[uint32]bool, error)
	SaveRoomUserNum(ctx context.Context, aiUid, channelId uint32, userNum int) error
	GetRoomUserNum(ctx context.Context, aiUid, channelId uint32) (int, error)
	UpdateAiRoomCfg(ctx context.Context, cfg *entity.RoomConfig) error
	GetAiRoomCfg(ctx context.Context, uid uint32) (*entity.RoomConfig, error)
	GetRunningAiRooms(ctx context.Context, lastCid uint32, limit uint32) ([]*entity.RoomConfig, bool, error)
	AiReceptionContentGet(ctx context.Context) error
	AddRunningRoom(ctx context.Context, cid, uid uint32, expiredAt int64) error
	BatGetRunningStatus(ctx context.Context, roomInfos []*entity.RoomInfo) (map[uint32]bool, error)
	BatDelRunningRoom(ctx context.Context, roomInfos []*entity.RoomInfo) error
}
