// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-account/internal/mgr (interfaces: AiRoomManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	cache "golang.52tt.com/services/aigc/aigc-account/internal/cache"
	entity "golang.52tt.com/services/aigc/aigc-account/internal/entity"
)

// MockAiRoomManager is a mock of AiRoomManager interface.
type MockAiRoomManager struct {
	ctrl     *gomock.Controller
	recorder *MockAiRoomManagerMockRecorder
}

// MockAiRoomManagerMockRecorder is the mock recorder for MockAiRoomManager.
type MockAiRoomManagerMockRecorder struct {
	mock *MockAiRoomManager
}

// NewMockAiRoomManager creates a new mock instance.
func NewMockAiRoomManager(ctrl *gomock.Controller) *MockAiRoomManager {
	mock := &MockAiRoomManager{ctrl: ctrl}
	mock.recorder = &MockAiRoomManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAiRoomManager) EXPECT() *MockAiRoomManagerMockRecorder {
	return m.recorder
}

// GetAiRoomCfg mocks base method.
func (m *MockAiRoomManager) GetAiRoomCfg(arg0 context.Context, arg1 uint32) (*entity.RoomConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAiRoomCfg", arg0, arg1)
	ret0, _ := ret[0].(*entity.RoomConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiRoomCfg indicates an expected call of GetAiRoomCfg.
func (mr *MockAiRoomManagerMockRecorder) GetAiRoomCfg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiRoomCfg", reflect.TypeOf((*MockAiRoomManager)(nil).GetAiRoomCfg), arg0, arg1)
}

// GetLastReceptionTime mocks base method.
func (m *MockAiRoomManager) GetLastReceptionTime(arg0 context.Context, arg1, arg2 uint32) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastReceptionTime", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastReceptionTime indicates an expected call of GetLastReceptionTime.
func (mr *MockAiRoomManagerMockRecorder) GetLastReceptionTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastReceptionTime", reflect.TypeOf((*MockAiRoomManager)(nil).GetLastReceptionTime), arg0, arg1, arg2)
}

// GetRoomUserNum mocks base method.
func (m *MockAiRoomManager) GetRoomUserNum(arg0 context.Context, arg1, arg2 uint32) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoomUserNum", arg0, arg1, arg2)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRoomUserNum indicates an expected call of GetRoomUserNum.
func (mr *MockAiRoomManagerMockRecorder) GetRoomUserNum(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoomUserNum", reflect.TypeOf((*MockAiRoomManager)(nil).GetRoomUserNum), arg0, arg1, arg2)
}

// GetRunningAiRooms mocks base method.
func (m *MockAiRoomManager) GetRunningAiRooms(arg0 context.Context, arg1, arg2 uint32) ([]*entity.RoomConfig, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRunningAiRooms", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*entity.RoomConfig)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetRunningAiRooms indicates an expected call of GetRunningAiRooms.
func (mr *MockAiRoomManagerMockRecorder) GetRunningAiRooms(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRunningAiRooms", reflect.TypeOf((*MockAiRoomManager)(nil).GetRunningAiRooms), arg0, arg1, arg2)
}

// GetWaitingReceptionUser mocks base method.
func (m *MockAiRoomManager) GetWaitingReceptionUser(arg0 context.Context, arg1, arg2 uint32) ([]cache.WaitingReceptionUser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWaitingReceptionUser", arg0, arg1, arg2)
	ret0, _ := ret[0].([]cache.WaitingReceptionUser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWaitingReceptionUser indicates an expected call of GetWaitingReceptionUser.
func (mr *MockAiRoomManagerMockRecorder) GetWaitingReceptionUser(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWaitingReceptionUser", reflect.TypeOf((*MockAiRoomManager)(nil).GetWaitingReceptionUser), arg0, arg1, arg2)
}

// IsReceptionUser mocks base method.
func (m *MockAiRoomManager) IsReceptionUser(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) (map[uint32]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsReceptionUser", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsReceptionUser indicates an expected call of IsReceptionUser.
func (mr *MockAiRoomManagerMockRecorder) IsReceptionUser(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsReceptionUser", reflect.TypeOf((*MockAiRoomManager)(nil).IsReceptionUser), arg0, arg1, arg2, arg3)
}

// SaveLastReceptionTime mocks base method.
func (m *MockAiRoomManager) SaveLastReceptionTime(arg0 context.Context, arg1, arg2 uint32, arg3 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveLastReceptionTime", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveLastReceptionTime indicates an expected call of SaveLastReceptionTime.
func (mr *MockAiRoomManagerMockRecorder) SaveLastReceptionTime(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveLastReceptionTime", reflect.TypeOf((*MockAiRoomManager)(nil).SaveLastReceptionTime), arg0, arg1, arg2, arg3)
}

// SaveReceptionUser mocks base method.
func (m *MockAiRoomManager) SaveReceptionUser(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveReceptionUser", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveReceptionUser indicates an expected call of SaveReceptionUser.
func (mr *MockAiRoomManagerMockRecorder) SaveReceptionUser(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveReceptionUser", reflect.TypeOf((*MockAiRoomManager)(nil).SaveReceptionUser), arg0, arg1, arg2, arg3)
}

// SaveRoomUserNum mocks base method.
func (m *MockAiRoomManager) SaveRoomUserNum(arg0 context.Context, arg1, arg2 uint32, arg3 int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveRoomUserNum", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveRoomUserNum indicates an expected call of SaveRoomUserNum.
func (mr *MockAiRoomManagerMockRecorder) SaveRoomUserNum(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveRoomUserNum", reflect.TypeOf((*MockAiRoomManager)(nil).SaveRoomUserNum), arg0, arg1, arg2, arg3)
}

// SaveWaitingReceptionUser mocks base method.
func (m *MockAiRoomManager) SaveWaitingReceptionUser(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveWaitingReceptionUser", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveWaitingReceptionUser indicates an expected call of SaveWaitingReceptionUser.
func (mr *MockAiRoomManagerMockRecorder) SaveWaitingReceptionUser(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveWaitingReceptionUser", reflect.TypeOf((*MockAiRoomManager)(nil).SaveWaitingReceptionUser), arg0, arg1, arg2, arg3, arg4)
}

// UpdateAiRoomCfg mocks base method.
func (m *MockAiRoomManager) UpdateAiRoomCfg(arg0 context.Context, arg1 *entity.RoomConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAiRoomCfg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAiRoomCfg indicates an expected call of UpdateAiRoomCfg.
func (mr *MockAiRoomManagerMockRecorder) UpdateAiRoomCfg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAiRoomCfg", reflect.TypeOf((*MockAiRoomManager)(nil).UpdateAiRoomCfg), arg0, arg1)
}
