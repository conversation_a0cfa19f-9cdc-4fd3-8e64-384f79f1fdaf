package store

import (
	"context"

	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
)

//go:generate mockgen -destination=mocks/account.go -package=mocks golang.52tt.com/services/aigc/aigc-account/internal/store AIAccountStore
type AIAccountStore interface {
	// 添加AI账号
	Add(ctx context.Context, account *entity.AIAccount) error
	// 更新AI账号
	Update(ctx context.Context, account *entity.AIAccount) error
	// 批量删除AI账号
	BatchDelete(ctx context.Context, idList []uint32) error
	// 批量注销AI账号
	BatchUnregister(ctx context.Context, idList []uint32) error

	// 获取单个AI账号
	Get(ctx context.Context, id uint32) (*entity.AIAccount, error)
	// 批量获取AI账号
	BatchGet(ctx context.Context, idList []uint32) (entity.AIAccountList, error)
	// 分页获取AI账号列表
	GetByPage(ctx context.Context, page, size int64, chatCardWallShowStatus uint32) (entity.AIAccountList, int64, error)
	UpdateSex(ctx context.Context, uid uint32, sex int32) error
	GetAIAccountsBySex(ctx context.Context, sex int32, limit int64) (entity.AIAccountList, error)
}

//go:generate mockgen -destination=mocks/chat_time.go -package=mocks golang.52tt.com/services/aigc/aigc-account/internal/store ChatTimeStore
type ChatTimeStore interface {
	UpdateChatTime(ctx context.Context, uid, aiUid uint32) error
	GetChatTime(ctx context.Context, uid, aiUid uint32) (*entity.ChatTimeRecord, error)
}

//go:generate mockgen -destination=mocks/ai_post.go -package=mocks golang.52tt.com/services/aigc/aigc-account/internal/store AIPostStore
type AIPostStore interface {
	// Add 添加AI帖子
	Add(ctx context.Context, post *entity.AIPost) error
	// GetList 批量获取AI帖子
	GetList(ctx context.Context, page, size int64, uid []uint32) ([]*entity.AIPost, int64, error)
}

//go:generate mockgen -destination=mocks/ai_room.go -package=mocks golang.52tt.com/services/aigc/aigc-account/internal/store AIRoomStore
type AIRoomStore interface {
	// Update 更新AI房间配置
	Update(ctx context.Context, room *entity.RoomConfig) error
	// Get 根据获取uid获取AI房间配置
	Get(ctx context.Context, uid uint32) (*entity.RoomConfig, error)
	// GetRunningRooms 分页获取正在运行中的AI房间配置，即当前时间处于OperationTimes范围中的房间
	GetRunningRooms(ctx context.Context, lastCid uint32, limit uint32) ([]*entity.RoomConfig, error)
}
