package mongo

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.52tt.com/services/aigc/aigc-account/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-account/internal/store"
)

const collectionAiRoom = "ai_room"

type aiRoomStore struct {
	col *mongo.Collection
}

func NewAiRoomStore(ctx context.Context, mongoDB *db.MongoDB) store.AIRoomStore {
	st := &aiRoomStore{
		col: mongoDB.Database().Collection(collectionAiRoom),
	}

	// 创建索引
	_, err := st.col.Indexes().CreateMany(ctx, []mongo.IndexModel{
		//TODO
	})
	if err != nil {
		log.WarnWithCtx(ctx, "NewAiRoomStore collection(ai_room) CreateMany err: %v", err)
	}

	return st
}

