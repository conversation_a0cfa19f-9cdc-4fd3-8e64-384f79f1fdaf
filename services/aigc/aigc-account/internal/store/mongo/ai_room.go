package mongo

import (
	"context"
	"errors"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.52tt.com/services/aigc/aigc-account/internal/entity"
	"golang.52tt.com/services/aigc/aigc-account/internal/infra/db"
	"golang.52tt.com/services/aigc/aigc-account/internal/store"
)

const collectionAiRoom = "ai_room"

type aiRoomStore struct {
	col *mongo.Collection
}

func NewAiRoomStore(ctx context.Context, mongoDB *db.MongoDB) store.AIRoomStore {
	st := &aiRoomStore{
		col: mongoDB.Database().Collection(collectionAiRoom),
	}

	// 创建索引
	_, err := st.col.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys: bson.D{{Key: "uid", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "operation_times.start_time", Value: 1}, {Key: "operation_times.end_time", Value: 1}},
		},
	})
	if err != nil {
		log.WarnWithCtx(ctx, "NewAiRoomStore collection(ai_room) CreateMany err: %v", err)
	}

	return st
}

// Update 更新AI房间配置
func (s *aiRoomStore) Update(ctx context.Context, room *entity.RoomConfig) error {
	filter := bson.M{"_id": room.Cid}
	update := bson.M{"$set": bson.M{
		"tab_id":                     room.TabId,
		"channel_avatar":             room.ChannelAvatar,
		"channel_name":               room.ChannelName,
		"welcome_msg":                room.WelcomeMsg,
		"channel_topic_title":        room.ChannelTopicTitle,
		"channel_topic_detail":       room.ChannelTopicDetail,
		"channel_supervisor":         room.ChannelSupervisor,
		"channel_admin":              room.ChannelAdmin,
		"lock_mic_list":              room.LockMicList,
		"ai_use_mic_index":           room.AIUseMicIndex,
		"publish_user_num_threshold": room.PublishUserNumThreshold,
		"dismiss_user_num_threshold": room.DismissUserNumThreshold,
		"operation_times":            room.OperationTimes,
		"publish_times":              room.PublishTimes,
		"channel_bgm":                room.ChannelBgm,
		"publish_options":            room.PublishOptions,
		"prompt_id":                  room.PromptId,
		"timbre":                     room.Timbre,
		"reception_msg_list":         room.ReceptionMsgList,
		"script":                     room.Script,
		"update_time":                time.Now(),
	},
		"$setOnInsert": bson.M{
			"uid": room.Uid,
		},
	}

	_, err := s.col.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
	if err != nil {
		log.ErrorWithCtx(ctx, "Update room cid(%d) err: %v", room.Cid, err)
		return err
	}

	return nil
}

// Get 根据uid获取AI房间配置
func (s *aiRoomStore) Get(ctx context.Context, uid uint32) (*entity.RoomConfig, error) {
	filter := bson.M{"uid": uid}

	var room entity.RoomConfig
	err := s.col.FindOne(ctx, filter).Decode(&room)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "Get uid(%d) err: %v", uid, err)
		return nil, err
	}

	return &room, nil
}

// GetRunningRooms 分页获取正在运行中的AI房间配置，即当前时间处于OperationTimes范围中的房间
func (s *aiRoomStore) GetRunningRooms(ctx context.Context, lastCid uint32, limit uint32) ([]*entity.RoomConfig, error) {
	currentHour := time.Now().Hour()
	currentMinute := time.Now().Minute()
	currentSecond := time.Now().Second()
	currentTime := currentHour*3600 + currentMinute*60 + currentSecond

	// 构建查询条件：当前时间在运营时间段内
	// 使用 $elemMatch 确保至少有一个时间段包含当前时间
	// 注意：MongoDB中protobuf字段存储为全小写：starttime, endtime
	filter := bson.M{
		"operation_times": bson.M{
			"$elemMatch": bson.M{
				"start_time": bson.M{"$lte": currentTime},
				"end_time":   bson.M{"$gte": currentTime},
			},
		},
	}

	// 如果提供了lastCid，则查询cid大于lastCid的记录（用于分页）
	if lastCid > 0 {
		filter["_id"] = bson.M{"$gt": lastCid}
	}

	findOpts := options.Find().
		SetLimit(int64(limit)).
		SetSort(bson.D{{Key: "_id", Value: 1}}) // 按cid升序排列

	cursor, err := s.col.Find(ctx, filter, findOpts)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRunningRooms Find err: %v", err)
		return nil, err
	}
	defer cursor.Close(ctx)

	var rooms []*entity.RoomConfig
	if err = cursor.All(ctx, &rooms); err != nil {
		log.ErrorWithCtx(ctx, "GetRunningRooms cursor.All err: %v", err)
		return nil, err
	}
	log.InfoWithCtx(ctx, "GetRunningRooms success filter:%v len(rooms): %d", filter, len(rooms))
	return rooms, nil
}
