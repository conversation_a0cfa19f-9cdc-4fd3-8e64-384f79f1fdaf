package tt_config

import (
	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
	"golang.52tt.com/pkg/log"
	"sync/atomic"
)

type AigcZegoStreamConfig struct {
	TestStreamId string `json:"test_stream_id"`
	ZegoAppId    uint32 `json:"zego_app_id"`

	FallbackHeartbeat int64 `json:"fallback_heartbeat"` // 心跳间隔超过xx秒则需要兜底重连
	FallbackPending   int64 `json:"fallback_pending"`   // pending状态持续超过xx秒则认为需要兜底重连

	FallBackHandleTimeoutCorn     string `json:"fallback_handle_timeout_corn"`     // 处理超时流的定时任务时间表达式
	FallBackDeleteRecordCorn      string `json:"fallback_delete_record_corn"`      // 删除下麦后长时间未删除的记录的定时任务时间表达式
	FallBackDeleteRecordThreshold int64  `json:"fallback_delete_record_threshold"` // 删除下麦后长时间未删除的记录的阈值，单位秒
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (g *AigcZegoStreamConfig) Format() error {
	return nil
}

var (
	atomicInitGameUserRateConfig *atomic.Value
)

func init() {
	//if err := InitGameUserRateConfig(); err != nil {
	//    panic(err)
	//}
}

// 可以选择外部初始化或者直接init函数初始化
func InitAigcZegoStreamConfig() error {
	cfg := &AigcZegoStreamConfig{}
	atomCfg, err := ttconfig.AtomLoad("/data/cicd-dy-conf/ser/aigc-zego-stream.json", cfg)
	if nil != err {
		return err
	}
	atomicInitGameUserRateConfig = atomCfg
	return nil
}

var defaultRateConfig = &AigcZegoStreamConfig{}

func GetAigcZegoStreamConfig() *AigcZegoStreamConfig {
	if atomicInitGameUserRateConfig == nil {
		log.Errorf("GetAigcZegoStreamConfig is nil")
		return defaultRateConfig
	}
	return atomicInitGameUserRateConfig.Load().(*AigcZegoStreamConfig)
}

func (g *AigcZegoStreamConfig) GetTestStreamId() string {
	if g == nil || g.TestStreamId == "" {
		return ""
	}
	return g.TestStreamId
}

func (g *AigcZegoStreamConfig) GetZegoAppId() uint32 {
	if g == nil || g.ZegoAppId == 0 {
		return 1001672063
	}
	return g.ZegoAppId
}

func (g *AigcZegoStreamConfig) GetFallbackHeartbeat() int64 {
	if g == nil || g.FallbackHeartbeat == 0 {
		return 300
	}
	return g.FallbackHeartbeat
}

func (g *AigcZegoStreamConfig) GetFallbackPending() int64 {
	if g == nil || g.FallbackPending == 0 {
		return 300
	}
	return g.FallbackPending
}

func (g *AigcZegoStreamConfig) GetFallBackHandleTimeoutCorn() string {
	if g == nil || g.FallBackHandleTimeoutCorn == "" {
		return "@every 10s"
	}
	return g.FallBackHandleTimeoutCorn
}

func (g *AigcZegoStreamConfig) GetFallBackDeleteRecordCorn() string {
	if g == nil || g.FallBackDeleteRecordCorn == "" {
		return "@every 10s"
	}
	return g.FallBackDeleteRecordCorn
}

func (g *AigcZegoStreamConfig) GetFallBackDeleteRecordThreshold() int64 {
	if g == nil || g.FallBackDeleteRecordThreshold == 0 {
		return 20
	}
	return g.FallBackDeleteRecordThreshold
}
