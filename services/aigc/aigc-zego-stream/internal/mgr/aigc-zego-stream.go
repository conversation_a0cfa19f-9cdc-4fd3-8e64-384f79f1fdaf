package mgr

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic"
	"gitlab.ttyuyin.com/tyr/tt-ecosystem/grpc/client/interceptor"
	"gitlab.ttyuyin.com/tyr/x/log"
	context_info "golang.52tt.com/pkg/context-info"
	pb "golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_voice_room"
	zegoPb "golang.52tt.com/protocol/services/rcmd/zego_stream_gateway/zego_stream"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/entity"
	tt_config "golang.52tt.com/services/aigc/aigc-zego-stream/internal/ttconfig"
	"google.golang.org/grpc"
	"google.golang.org/grpc/backoff"
	"google.golang.org/grpc/keepalive"
	"sync"
	"time"
)

type streamUpdateEvent struct {
	RoomId     string   `json:"room_id"`
	UpdateType string   `json:"update_type"`
	StreamList []string `json:"stream_list"`
}

func sumAudioData(data []byte) int {
	sum := 0
	for _, v := range data {
		sum += int(v)
	}
	return sum
}

type CommonMsg struct {
	StreamId  string
	ZegoMsg   *zegoPb.EventWrap
	AigcMsg   *pb.RecvAudioStreamInRoomResp
	MsgType   uint32
	Uid       uint32
	ChannelId uint32
}

type StreamManager struct {
	aigcClient       *pb.Client
	zegoProxyClient  *zegoPb.Client
	streams          sync.Map // key: streamId
	streamRecord     *StreamRecord
	channelMicClient channel_mic.ChannelMicClient
}

func (m *StreamManager) GetSteamCli(streamId string) *StreamClients {
	if value, exists := m.streams.Load(streamId); !exists {
		return nil
	} else {
		return value.(*StreamClients)
	}
}

func NewStreamManager(ctx context.Context, streamRecord *StreamRecord, channelMicClient channel_mic.ChannelMicClient) (*StreamManager, error) {

	aigcClient := pb.MustNewClientTo(ctx, "aigc-voice-room.rcmd-tt.svc:8000",
		grpc.WithChainUnaryInterceptor(interceptor.NoneStatusAdapterInterceptor()),
		grpc.WithKeepaliveParams(keepalive.ClientParameters{
			Time:    15 * time.Second,
			Timeout: 5 * time.Second,
		}),
		grpc.WithConnectParams(grpc.ConnectParams{
			Backoff: backoff.Config{
				BaseDelay:  1.0 * time.Second,
				Multiplier: 1.6,
				MaxDelay:   120 * time.Second,
			},
			MinConnectTimeout: 20 * time.Second,
		}))

	zegoProxyClient := zegoPb.MustNewClientTo(ctx, "zego-proxy.rcmd-tt.svc.cluster.local:8000",
		grpc.WithChainUnaryInterceptor(interceptor.NoneStatusAdapterInterceptor()),
		grpc.WithKeepaliveParams(keepalive.ClientParameters{
			Time:    15 * time.Second,
			Timeout: 5 * time.Second,
		}),
		grpc.WithConnectParams(grpc.ConnectParams{
			Backoff: backoff.Config{
				BaseDelay:  1.0 * time.Second,
				Multiplier: 1.6,
				MaxDelay:   120 * time.Second,
			},
			MinConnectTimeout: 20 * time.Second,
		}))

	return &StreamManager{
		aigcClient:       aigcClient,
		zegoProxyClient:  zegoProxyClient,
		streamRecord:     streamRecord,
		channelMicClient: channelMicClient,
	}, nil
}

func GetStreamName(uid, channelId uint32) string {
	return fmt.Sprintf("server-%d-Baigcroom-C%d-%d", uid, channelId, time.Now().UnixMilli())
}

func (m *StreamManager) CreateStream(streamType uint32, streamId string, userName string, channelId, uid uint32) error {
	ctx := context.Background()
	ctx = context_info.GenReqId(ctx)
	if streamType == entity.AigcStreamType {
		log.InfoWithCtx(ctx, "CreateStream begin, streamType:%d,channelId:%d, uid:%d", streamType, channelId, uid)
		stream, err := m.aigcClient.RecvAudioStreamInRoom(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateStream RecvAudioStreamInRoom err: %v, streamId:%d, uid:%d, userName:%s, streamType:%d,channelId:%d, uid:%d", err, streamId, uid, userName, streamType, channelId, uid)
			return err
		}
		err = m.streamRecord.CreateStreamSuccess(ctx, streamId, entity.AigcStreamType)
		if err != nil {
			log.ErrorWithCtx(ctx, "Login CreateStreamSuccess aigc err: %v, channelId:%d, userName:%d,channelId:%d, uid:%d", err, streamId, userName, channelId, uid)
			return err
		}
		if s, ok := m.streams.Load(streamId); ok {
			if s.(*StreamClients).aigcStreamClientInfo != nil {
				s.(*StreamClients).aigcStreamClientInfo.CloseChan()
			}
			s.(*StreamClients).aigcStreamClientInfo = NewAigcStreamClientInfo(stream, m, s.(*StreamClients), m.streamRecord)
			log.InfoWithCtx(ctx, "CreateStream Stream:%s already exists, streamType:%d,channelId:%d, uid:%d", streamId, streamType, channelId, uid)

			handleCtx, cancelStream := context.WithCancel(ctx)
			go s.(*StreamClients).aigcStreamClientInfo.monitorStream(handleCtx, uid, channelId, streamId, stream, userName, cancelStream)
			go s.(*StreamClients).aigcStreamClientInfo.handleStream(handleCtx, uid, channelId, streamId, stream, userName, cancelStream)
		} else {
			log.InfoWithCtx(ctx, "CreateStream suc: %s, streamType:%d,channelId:%d, uid:%d", streamId, streamType, channelId, uid)
			sTmp := &StreamClients{
				streamId:         streamId,
				userName:         userName,
				uid:              uid,
				channelMicClient: m.channelMicClient,
			}
			sTmp.aigcStreamClientInfo = NewAigcStreamClientInfo(stream, m, sTmp, m.streamRecord)
			m.streams.Store(streamId, sTmp)
			handleCtx, cancelStream := context.WithCancel(ctx)
			go sTmp.aigcStreamClientInfo.monitorStream(handleCtx, uid, channelId, streamId, stream, userName, cancelStream)
			go sTmp.aigcStreamClientInfo.handleStream(handleCtx, uid, channelId, streamId, stream, userName, cancelStream)
		}

	} else if streamType == entity.ZegoStreamType {
		log.InfoWithCtx(ctx, "CreateStream begin, streamType:%d,channelId:%d, uid:%d", streamType, channelId, uid)
		stream, err := m.zegoProxyClient.StreamTransfer(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateStream StreamTransfer err: %v, streamId:%s, streamType:%d,channelId:%d, uid:%d", err, streamId, streamType, channelId, uid)
			return err
		}

		if streamInfo, ok := m.streams.Load(streamId); ok {
			if streamInfo.(*StreamClients).zegoStreamClientInfo != nil {
				streamInfo.(*StreamClients).zegoStreamClientInfo.CloseChan()
			}
			log.Infof("CreateStream suc:%s already exists, streamType:%d,channelId:%d, uid:%d", streamId, streamType, channelId, uid)
			streamInfo.(*StreamClients).zegoStreamClientInfo = NewZegoStreamClientInfo(stream, m, streamInfo.(*StreamClients), m.streamRecord)
			streamInfo.(*StreamClients).streamId = streamId
			streamInfo.(*StreamClients).uid = uid
			streamInfo.(*StreamClients).userName = userName
			streamInfo.(*StreamClients).channelMicClient = m.channelMicClient

			streamInfo.(*StreamClients).zegoStreamClientInfo.sMgr = m
			handleCtx, cancelStream := context.WithCancel(ctx)
			go streamInfo.(*StreamClients).zegoStreamClientInfo.monitorStream(handleCtx, uid, channelId, streamId, stream, userName, cancelStream) // 启动监控协程
			go streamInfo.(*StreamClients).zegoStreamClientInfo.handleStream(handleCtx, uid, channelId, streamId, stream, userName, cancelStream)
		} else {
			log.ErrorWithCtx(ctx, "CreateStream fail stream: %s, streamType:%d err,channelId:%d, uid:%d", streamId, streamType, channelId, uid) //正常来说走不到这里
			/*sTmp := &StreamClients{
				zegoStreamClientInfo: NewZegoStreamClientInfo(stream, m),
				streamId:             streamId,
				uid:                  uid,
			}
			m.streams.Store(streamId, sTmp)
			go sTmp.zegoStreamClientInfo.monitorStream(ctx, channelId, streamId, stream, uid) // 启动监控协程
			go sTmp.zegoStreamClientInfo.handleStream(ctx, channelId, streamId, stream, uid)*/
		}
	}

	return nil
}

func (m *StreamManager) reconnectStream(ctx context.Context, streamType uint32, userName string, channelId, uid uint32, streamId, bussName string) (isSuccess bool) {
	isSuc := false
	/*if streamType == entity.ZegoStreamType {
		err := m.streamRecord.UpdateStreamStatus(ctx, streamId, 0, entity.ZegoStreamStatusPending)
		if err != nil {
			log.ErrorWithCtx(ctx, "reconnectStream UpdateStreamStatus err: %v, channelId:%d, streamType:%d,channelId:%d, uid:%d, bussName:%s", err, channelId, streamType, channelId, uid, bussName)
			return false
		}
	} else if streamType == entity.AigcStreamType {
		err := m.streamRecord.UpdateStreamStatus(ctx, streamId, entity.AigcStreamStatusPending, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "reconnectStream UpdateStreamStatus err: %v, channelId:%d, streamType:%d,channelId:%d, uid:%d, bussName:%s", err, channelId, streamType, channelId, uid, bussName)
			return false
		}
	} else {
		log.ErrorWithCtx(ctx, "reconnectStream streamType err: %d,channelId:%d, uid:%d, bussName:%s", streamType, channelId, uid, bussName)
		return false
	}*/
	// 指数退避重试逻辑
	backoff := time.Second
	for i := 0; i < 3; i++ {
		if err := m.CreateStream(streamType, streamId, userName, channelId, uid); err == nil {
			log.Infof("reconnect stream %s suc:after retries:%d times,channelId:%d, uid:%d, bussName:%s", streamId, i+1, channelId, uid, bussName)
			isSuc = true
			break
		}
		time.Sleep(backoff)
		backoff *= 2
	}

	if isSuc && streamType == entity.ZegoStreamType {
		streamName := GetStreamName(uid, channelId)
		err := m.LoginRoom(ctx, streamId, userName, streamName, channelId, uid)
		if err != nil {
			return false
		}
		return true
	}

	if isSuc && streamType == entity.AigcStreamType {
		return true
	}

	log.InfoWithCtx(ctx, "reconnect stream %s fail:after retries,channelId:%d, uid:%d, bussName:%s", streamId, channelId, uid, bussName)
	return false
}

func (m *StreamManager) DeleteStreamId(ctx context.Context, streamId string) {
	if streamInfo, ok := m.streams.Load(streamId); ok {
		log.InfoWithCtx(ctx, "DeleteStreamId Stream:%s", streamId)
		streamInfo.(*StreamClients).zegoStreamClientInfo.CloseChan()
		streamInfo.(*StreamClients).aigcStreamClientInfo.CloseChan()
		m.streams.Delete(streamId) // 删除指定的 Stream
	} else {
		log.ErrorWithCtx(ctx, "DeleteStreamId StreamId:%s err: not exist", streamId)
	}
}

func (m *StreamManager) Close() {
	// 关闭所有 Stream
	m.streams.Range(func(key, value interface{}) bool {
		value.(*StreamClients).zegoStreamClientInfo.CloseChan()
		value.(*StreamClients).aigcStreamClientInfo.CloseChan()
		m.streams.Delete(key)
		return true
	})

}

func (m *StreamManager) LoginRoom(ctx context.Context, streamId, userName, streamName string, channelId, uid uint32) error {
	jsonRaw, err := json.Marshal(map[string]any{"app_id": tt_config.GetAigcZegoStreamConfig().GetZegoAppId(),
		"room_id": streamId, "user_id": userName, "stream_id": streamName, "user_name": userName})
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal error: %v, appId: %d,userName: %s, streamId: %s, channelId:%d, uid:%d", err, tt_config.GetAigcZegoStreamConfig().GetZegoAppId(), userName, streamId, channelId, uid)
		return err
	}

	streamClis := m.GetSteamCli(streamId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "streamClis is nil, streamId: %s, channelId:%d, uid:%d", streamId, channelId, uid)
		return nil
	}

	if streamClis.zegoStreamClientInfo == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamCli is nil, streamId: %s, channelId:%d, uid:%d", streamId, channelId, uid)
	} else if streamClis.zegoStreamClientInfo == nil || streamClis.zegoStreamClientInfo.zegoStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.aigcStreamClientInfo.aigcStreamCli is nil, streamId: %s, channelId:%d, uid:%d", streamId, channelId, uid)
	} else {
		err = streamClis.zegoStreamClientInfo.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_Login, Json: string(jsonRaw)})
		if err != nil {
			streamClis.aigcStreamClientInfo.CloseChan()
			streamClis.zegoStreamClientInfo.CloseChan()
			log.ErrorWithCtx(ctx, "loginRoom zegoStreamCli.Send err:%v, streamId: %s, appid:%d, channelId:%d, uid:%d", err, streamId,
				tt_config.GetAigcZegoStreamConfig().GetZegoAppId(), channelId, uid)
			return err
		}
	}
	log.InfoWithCtx(ctx, "loginning streamId:%s, userName:%s, streamName:%s, appid:%d, channelId:%d, uid:%d", streamId, userName,
		streamName, tt_config.GetAigcZegoStreamConfig().GetZegoAppId(), channelId, uid)
	return nil
}

func (m *StreamManager) GetUserOnMicMap(ctx context.Context, channelUidMap map[uint32]uint32) (map[uint32]struct{}, error) {
	if len(channelUidMap) == 0 {
		return nil, nil
	}

	cids := make([]uint32, 0, len(channelUidMap))
	for cid := range channelUidMap {
		cids = append(cids, cid)
	}
	i := 0
	onMicMap := make(map[uint32]struct{})
	for {
		end := i + 20
		if end > len(cids) {
			end = len(cids)
		}
		tempList := cids[i:end]
		i += 20
		rsp, err := m.channelMicClient.BatGetMicrList(ctx, &channel_mic.BatGetMicrListReq{ChannelIdList: tempList})
		if err != nil {
			log.ErrorWithCtx(ctx, "BatGetMicrList err:%v", err)
			continue
		}
		for _, micData := range rsp.GetMicDataList() {
			for _, micInfo := range micData.GetAllMicList() {

				aiUid := channelUidMap[micData.GetChannelId()]
				if micInfo.GetMicUid() != 0 && micInfo.GetMicUid() == aiUid {
					onMicMap[aiUid] = struct{}{}
					break
				}
			}
		}
		if len(tempList) < 20 || i >= len(cids) {
			break
		}
	}
	return onMicMap, nil
}
