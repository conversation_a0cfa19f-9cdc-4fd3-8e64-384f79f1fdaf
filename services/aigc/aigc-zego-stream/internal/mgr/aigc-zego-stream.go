package mgr

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/tyr/tt-ecosystem/grpc/client/interceptor"
	"gitlab.ttyuyin.com/tyr/x/log"
	context_info "golang.52tt.com/pkg/context-info"
	pb "golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_voice_room"
	zegoPb "golang.52tt.com/protocol/services/rcmd/zego_stream_gateway/zego_stream"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/entity"
	tt_config "golang.52tt.com/services/aigc/aigc-zego-stream/internal/ttconfig"
	"google.golang.org/grpc"
	"google.golang.org/grpc/backoff"
	"google.golang.org/grpc/keepalive"
	"strconv"
	"sync"
	"time"
)

type streamUpdateEvent struct {
	RoomId     string   `json:"room_id"`
	UpdateType string   `json:"update_type"`
	StreamList []string `json:"stream_list"`
}

func sumAudioData(data []byte) int {
	sum := 0
	for _, v := range data {
		sum += int(v)
	}
	return sum
}

type CommonMsg struct {
	StreamId  string
	ZegoMsg   *zegoPb.EventWrap
	AigcMsg   *pb.RecvAudioStreamInRoomResp
	MsgType   uint32
	Uid       uint32
	ChannelId uint32
}

type StreamManager struct {
	aigcClient      *pb.Client
	zegoProxyClient *zegoPb.Client
	streams         sync.Map // key: streamId
	streamRecord    *StreamRecord
	zegoAppId       string
}

func (m *StreamManager) GetSteamCli(streamId string) *StreamClients {
	if value, exists := m.streams.Load(streamId); !exists {
		return nil
	} else {
		return value.(*StreamClients)
	}
}

func NewStreamManager(ctx context.Context, streamRecord *StreamRecord, zegoAppId string) (*StreamManager, error) {

	aigcClient := pb.MustNewClientTo(ctx, "aigc-voice-room.rcmd-tt.svc:8000",
		grpc.WithChainUnaryInterceptor(interceptor.NoneStatusAdapterInterceptor()),
		grpc.WithKeepaliveParams(keepalive.ClientParameters{
			Time:    15 * time.Second,
			Timeout: 5 * time.Second,
		}),
		grpc.WithConnectParams(grpc.ConnectParams{
			Backoff: backoff.Config{
				BaseDelay:  1.0 * time.Second,
				Multiplier: 1.6,
				MaxDelay:   120 * time.Second,
			},
			MinConnectTimeout: 20 * time.Second,
		}))

	zegoProxyClient := zegoPb.MustNewClientTo(ctx, "zego-proxy.rcmd-tt.svc.cluster.local:8000",
		grpc.WithChainUnaryInterceptor(interceptor.NoneStatusAdapterInterceptor()),
		grpc.WithKeepaliveParams(keepalive.ClientParameters{
			Time:    15 * time.Second,
			Timeout: 5 * time.Second,
		}),
		grpc.WithConnectParams(grpc.ConnectParams{
			Backoff: backoff.Config{
				BaseDelay:  1.0 * time.Second,
				Multiplier: 1.6,
				MaxDelay:   120 * time.Second,
			},
			MinConnectTimeout: 20 * time.Second,
		}))

	return &StreamManager{
		aigcClient:      aigcClient,
		zegoProxyClient: zegoProxyClient,
		streamRecord:    streamRecord,
		zegoAppId:       zegoAppId,
	}, nil
}

func GetStreamName(uid, channelId uint32) string {
	return fmt.Sprintf("a-%d-P6-T3-M5-C%d-%d", uid, channelId, time.Now().UnixMilli()) //Baigcroom-
}

func (m *StreamManager) CreateStream(ctx context.Context, streamType uint32, streamId string, userName string, uid, channelId uint32) error {
	ctx = context.Background()
	ctx = context_info.GenReqId(ctx)
	if streamType == entity.AigcStreamType {
		log.InfoWithCtx(ctx, "CreateStream, streamType:%d", streamType)
		stream, err := m.aigcClient.RecvAudioStreamInRoom(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateStream RecvAudioStreamInRoom err: %v, streamId:%d, uid:%d, userName:%s, streamType:%d", err, streamId, uid, userName, streamType)
			return err
		}
		err = m.streamRecord.CreateStreamSuccess(ctx, streamId, entity.AigcStreamType)
		if err != nil {
			log.ErrorWithCtx(ctx, "Login CreateStreamSuccess aigc err: %v, channelId:%d, userName:%d", err, streamId, userName)
			return err
		}
		if s, ok := m.streams.Load(streamId); ok {
			if s.(*StreamClients).aigcStreamClientInfo != nil {
				s.(*StreamClients).aigcStreamClientInfo.CloseChan()
			}
			s.(*StreamClients).aigcStreamClientInfo = NewAigcStreamClientInfo(stream, m, s.(*StreamClients), m.streamRecord)
			log.InfoWithCtx(ctx, "CreateStream Stream:%s already exists, streamType:%d", streamId, streamType)

			handleCtx, cancelStream := context.WithCancel(ctx)
			go s.(*StreamClients).aigcStreamClientInfo.monitorStream(handleCtx, uid, channelId, streamId, stream, userName, cancelStream)
			go s.(*StreamClients).aigcStreamClientInfo.handleStream(handleCtx, uid, channelId, streamId, stream, userName, cancelStream)
		} else {
			log.InfoWithCtx(ctx, "Creating new stream: %s, streamType:%d", streamId, streamType)
			sTmp := &StreamClients{
				streamId: streamId,
				userName: userName,
				uid:      uid,
			}
			sTmp.aigcStreamClientInfo = NewAigcStreamClientInfo(stream, m, sTmp, m.streamRecord)
			m.streams.Store(streamId, sTmp)
			handleCtx, cancelStream := context.WithCancel(ctx)
			go sTmp.aigcStreamClientInfo.monitorStream(handleCtx, uid, channelId, streamId, stream, userName, cancelStream) // 启动监控协程
			go sTmp.aigcStreamClientInfo.handleStream(handleCtx, uid, channelId, streamId, stream, userName, cancelStream)
		}

	} else if streamType == entity.ZegoStreamType {
		log.InfoWithCtx(ctx, "CreateStream, streamType:%d", streamType)
		stream, err := m.zegoProxyClient.StreamTransfer(ctx)
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateStream StreamTransfer err: %v, streamId:%s, streamType:%d", err, streamId, streamType)
			return err
		}

		if streamInfo, ok := m.streams.Load(streamId); ok {
			if streamInfo.(*StreamClients).zegoStreamClientInfo != nil {
				streamInfo.(*StreamClients).zegoStreamClientInfo.CloseChan()
			}
			log.Infof("CreateStream Stream:%s already exists, streamType:%d", streamId, streamType)
			streamInfo.(*StreamClients).zegoStreamClientInfo = NewZegoStreamClientInfo(stream, m, streamInfo.(*StreamClients), m.streamRecord)
			streamInfo.(*StreamClients).streamId = streamId
			streamInfo.(*StreamClients).uid = uid
			streamInfo.(*StreamClients).userName = userName

			streamInfo.(*StreamClients).zegoStreamClientInfo.sMgr = m
			handleCtx, cancelStream := context.WithCancel(ctx)
			go streamInfo.(*StreamClients).zegoStreamClientInfo.monitorStream(handleCtx, uid, channelId, streamId, stream, userName, cancelStream) // 启动监控协程
			go streamInfo.(*StreamClients).zegoStreamClientInfo.handleStream(handleCtx, uid, channelId, streamId, stream, userName, cancelStream)
		} else {
			log.ErrorWithCtx(ctx, "Creating new stream: %s, streamType:%d err", streamId, streamType) //正常来说走不到这里
			/*sTmp := &StreamClients{
				zegoStreamClientInfo: NewZegoStreamClientInfo(stream, m),
				streamId:             streamId,
				uid:                  uid,
			}
			m.streams.Store(streamId, sTmp)
			go sTmp.zegoStreamClientInfo.monitorStream(ctx, channelId, streamId, stream, uid) // 启动监控协程
			go sTmp.zegoStreamClientInfo.handleStream(ctx, channelId, streamId, stream, uid)*/
		}
	}

	return nil
}
func (m *StreamManager) reconnectStream(ctx context.Context, streamType uint32, userName string, uid, channelId uint32, streamId string) (isSuccess bool) {
	isSuc := false
	if streamType == entity.ZegoStreamType {
		err := m.streamRecord.UpdateStreamStatus(ctx, streamId, 0, entity.ZegoStreamStatusPending)
		if err != nil {
			log.ErrorWithCtx(ctx, "reconnectStream UpdateStreamStatus err: %v, channelId:%d, streamType:%d", err, channelId, streamType)
			return false
		}
	} else if streamType == entity.AigcStreamType {
		err := m.streamRecord.UpdateStreamStatus(ctx, streamId, entity.AigcStreamStatusPending, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "reconnectStream UpdateStreamStatus err: %v, channelId:%d, streamType:%d", err, channelId, streamType)
			return false
		}
	} else {
		log.ErrorWithCtx(ctx, "reconnectStream streamType err: %d", streamType)
		return false
	}
	// 指数退避重试逻辑
	backoff := time.Second
	for i := 0; i < 3; i++ {
		if err := m.CreateStream(ctx, streamType, streamId, userName, uid, channelId); err == nil {
			log.Infof("reconnect stream %s suc:after retries:%d times", streamId, i+1)
			isSuc = true
			break
		}
		time.Sleep(backoff)
		backoff *= 2
	}

	if isSuc && streamType == entity.ZegoStreamType {
		streamName := GetStreamName(uid, channelId)
		_ = m.LoginRoom(ctx, streamId, strconv.FormatUint(uint64(uid), 10), streamName)
		return true
	}
	if streamType == entity.ZegoStreamType {
		err := m.streamRecord.UpdateStreamStatus(ctx, streamId, 0, entity.ZegoStreamStatusUnhealth)
		if err != nil {
			log.ErrorWithCtx(ctx, "reconnectStream fail UpdateStreamStatus err: %v, channelId:%d, streamType:%d", err, channelId, streamType)
			return
		}
	} else {
		err := m.streamRecord.UpdateStreamStatus(ctx, streamId, entity.AigcStreamStatusUnHealth, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "reconnectStream fail UpdateStreamStatus err: %v, channelId:%d, streamType:%d", err, channelId, streamType)
			return
		}
	}
	log.InfoWithCtx(ctx, "reconnect stream  %s suc:after retries", streamId)
	return false
}

func (m *StreamManager) DeleteStreamId(ctx context.Context, streamId string) {
	if streamInfo, ok := m.streams.Load(streamId); ok {
		log.Infof("CreateStream Stream:%s already exists", streamId)
		streamInfo.(*StreamClients).zegoStreamClientInfo.CloseChan()
		streamInfo.(*StreamClients).aigcStreamClientInfo.CloseChan()
		m.streams.Delete(streamId) // 删除指定的 Stream
	} else {
		log.ErrorWithCtx(ctx, "DeleteStreamId StreamId:%s err: not exist", streamId)
	}
}

func (m *StreamManager) Close() {
	// 关闭所有 Stream
	m.streams.Range(func(key, value interface{}) bool {
		value.(*StreamClients).zegoStreamClientInfo.CloseChan()
		value.(*StreamClients).aigcStreamClientInfo.CloseChan()
		m.streams.Delete(key)
		return true
	})

}

func (m *StreamManager) startPullStream(ctx context.Context, strChannelId, streamId string) error {
	jsonRaw, err := json.Marshal(map[string]string{"stream_id": streamId})
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal error: %v, streamId: %s", err, streamId)
		return err
	}

	streamClis := m.GetSteamCli(strChannelId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "startPullStream streamClis is nil, streamId: %s", streamId)
		return nil
	}
	if streamClis.zegoStreamClientInfo == nil || streamClis.zegoStreamClientInfo.zegoStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamCli is nil, streamId: %s", streamId)
	} else if streamClis.zegoStreamClientInfo.zegoStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamClientInfo.zegoStreamCli is nil, streamId: %s", streamId)
	} else {
		err = streamClis.zegoStreamClientInfo.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_StartPullStream, Json: string(jsonRaw)})
		if err != nil {
			log.ErrorWithCtx(ctx, "startPullStream zegoStreamCli.Send err: %v, streamId: %s", err, streamId)
			return err
		}
	}

	return nil
}

func (m *StreamManager) stopPullStream(ctx context.Context, strChannelId, streamId string) error {
	jsonRaw, err := json.Marshal(map[string]string{"stream_id": streamId})
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal error: %v, streamId: %s", err, streamId)
		return err
	}
	streamClis := m.GetSteamCli(strChannelId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "streamClis is nil, streamId: %s", streamId)
		return nil
	}
	if streamClis.zegoStreamClientInfo == nil || streamClis.zegoStreamClientInfo.zegoStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamCli is nil, streamId: %s", streamId)
	} else if streamClis.zegoStreamClientInfo.zegoStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamClientInfo.zegoStreamCli is nil, streamId: %s", streamId)
	} else {
		err = streamClis.zegoStreamClientInfo.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_StopPullStream, Json: string(jsonRaw)})
		if err != nil {
			log.ErrorWithCtx(ctx, "stopPullStream zegoStreamCli.Send err: %v, streamId: %s", err, streamId)
			return err
		}
	}

	return nil
}

func (m *StreamManager) sendAigcStream(ctx context.Context, streamId string, channelId, uid uint32, data []byte) error {
	streamClis := m.GetSteamCli(streamId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "streamClis is nil, streamId: %s", streamId)
		return nil
	}
	if streamClis.aigcStreamClientInfo == nil || streamClis.aigcStreamClientInfo.aigcStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.aigcStreamClientInfo is nil, streamId: %s", streamId)
	} else if streamClis.aigcStreamClientInfo.aigcStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.aigcStreamClientInfo.aigcStreamCli is nil, streamId: %s", streamId)
	} else {
		err := streamClis.aigcStreamClientInfo.aigcStreamCli.Send(&pb.RecvAudioStreamInRoomReq{
			ChannelId: channelId,
			Uid:       uid,
			UserAudio: data,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "sendAigcStream error: %v, streamId: %s", err, streamId)
			return err
		}
		log.InfoWithCtx(ctx, "sendAigcStream streamId: %s, data len:%d, sum:%d", streamId, len(data), sumAudioData(data))
	}

	return nil
}

func (m *StreamManager) SendAudioData(ctx context.Context, streamId string, data []byte) error {
	streamClis := m.GetSteamCli(streamId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "streamClis is nil, streamId: %s", streamId)
		return nil
	}
	if streamClis.zegoStreamClientInfo == nil || streamClis.zegoStreamClientInfo.zegoStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamCli is nil, streamId: %s", streamId)
	} else if streamClis.zegoStreamClientInfo.zegoStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamClientInfo.zegoStreamCli is nil, streamId: %s", streamId)
	} else {
		err := streamClis.zegoStreamClientInfo.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_Audio, Data: data})
		if err != nil {
			log.ErrorWithCtx(ctx, "sendAudioData error: %v, streamId: %s", err, streamId)
			return err
		}
		log.InfoWithCtx(ctx, "SendAudioData data:%d", len(data))
	}

	return nil
}

func (m *StreamManager) LoginRoom(ctx context.Context, streamId, userName, streamName string) error {
	jsonRaw, err := json.Marshal(map[string]any{"app_id": tt_config.GetAigcZegoStreamConfig().GetZegoAppId(),
		"room_id": streamId, "user_id": userName, "stream_id": streamName, "user_name": userName + "123"})
	// 测试代码
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal error: %v, appId: %d,userName: %s, streamId: %s", err, tt_config.GetAigcZegoStreamConfig().GetZegoAppId(), userName, streamId)
		return err
	}

	streamClis := m.GetSteamCli(streamId)
	if streamClis == nil {
		log.ErrorWithCtx(ctx, "streamClis is nil, streamId: %s", streamId)
		return nil
	}

	if streamClis.zegoStreamClientInfo == nil || streamClis.zegoStreamClientInfo.zegoStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamCli is nil, streamId: %s", streamId)
	} else if streamClis.aigcStreamClientInfo == nil || streamClis.aigcStreamClientInfo.aigcStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.aigcStreamClientInfo.aigcStreamCli is nil, streamId: %s", streamId)
	} else {

		err = streamClis.zegoStreamClientInfo.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_Login, Json: string(jsonRaw)})
		if err != nil {
			streamClis.aigcStreamClientInfo.CloseChan()
			streamClis.zegoStreamClientInfo.CloseChan()
			log.ErrorWithCtx(ctx, "loginRoom zegoStreamCli.Send err:%v, streamId: %s, appid:%d", err, streamId,
				tt_config.GetAigcZegoStreamConfig().GetZegoAppId())
			return err
		}
	}
	log.InfoWithCtx(ctx, "logining streamId:%s, userName:%s, streamName:%s, appid:%d", streamId, userName,
		streamName, tt_config.GetAigcZegoStreamConfig().GetZegoAppId())
	return nil
}
