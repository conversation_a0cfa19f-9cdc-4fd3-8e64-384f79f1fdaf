package mgr

import (
	"context"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/db/mongo"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/entity"
	tt_config "golang.52tt.com/services/aigc/aigc-zego-stream/internal/ttconfig"
	"time"
)

type StreamRecord struct {
	mDao *mongo.MongoDao
}

func NewStreamRecord(mDao *mongo.MongoDao) *StreamRecord {
	return &StreamRecord{
		mDao: mDao,
	}
}

func (s *StreamRecord) UpdateStreamByMicState(ctx context.Context, streamId string, offMic bool) error {
	return s.mDao.UpdateStreamByMicState(ctx, streamId, offMic)
}

func (s *StreamRecord) GetStreamItemsLimitByExitRoom(ctx context.Context, podName string) ([]*entity.StreamItem, error) {
	return s.mDao.GetStreamItemsLimitByExitRoom(ctx, podName)
}

func (s *StreamRecord) UpsertStreamItem(ctx context.Context, item *entity.StreamItem) error {
	return s.mDao.UpsertStreamItem(ctx, item)
}

func (s *StreamRecord) HeartbeatSuccess(ctx context.Context, streamId, podName string, streamType uint32) error {
	return s.mDao.UpdateHeartbeatInfo(ctx, streamId, podName, streamType)
}

func (s *StreamRecord) UpdateStreamStatus(ctx context.Context, streamId string, aigcStatus, zegoStatus uint32) error {
	return s.mDao.UpdateStreamStatus(ctx, streamId, aigcStatus, zegoStatus)
}

func (s *StreamRecord) GetFallbackItemByPage(ctx context.Context, lastCreateTime time.Time, lastStreamId string, limit int64) ([]*entity.StreamItem, error) {
	return s.mDao.GetNeedFallBackStreamItems(ctx, lastCreateTime, lastStreamId, limit)
}

func (s *StreamRecord) DeleteStreamByStreamIds(ctx context.Context, streamIds []string) error {
	return s.mDao.DeleteStreamByStreamIds(ctx, streamIds)
}

func (s *StreamRecord) CreateStreamSuccess(ctx context.Context, streamId string, streamType uint32) error {
	return s.mDao.CreateStreamSuccess(ctx, streamId, streamType)
}

func (s *StreamRecord) GetAbnormalOffMicStream(ctx context.Context) ([]*entity.StreamItem, error) {
	return s.mDao.GetAbnormalOffMicStream(ctx, tt_config.GetAigcZegoStreamConfig().GetFallBackDeleteRecordThreshold())
}
