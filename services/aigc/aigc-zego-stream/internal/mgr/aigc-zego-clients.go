package mgr

import (
	"context"
	"encoding/json"
	"github.com/pkg/errors"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic"
	"gitlab.ttyuyin.com/tyr/x/log"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_voice_room"
	zegoPb "golang.52tt.com/protocol/services/rcmd/zego_stream_gateway/zego_stream"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/entity"
	tt_config "golang.52tt.com/services/aigc/aigc-zego-stream/internal/ttconfig"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"io"
	"strconv"
	"sync"
	"time"
)

const (
	ZegoStreamUpdateTypeAdd = "add"
	ZegoStreamUpdateTypeDel = "del"
)

type BaseStreamClientInfo struct {
	close<PERSON>han     chan struct{}
	once          sync.Once
	sMgr          *StreamManager
	streamClients *StreamClients
	sRecord       *StreamRecord
}

func (m *BaseStreamClientInfo) CloseChan(streamInterface interface{}, bussName string) {
	if stream, ok := streamInterface.(pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient); ok {
		err := stream.CloseSend()
		if err != nil {
			log.Errorf("CloseChan stream.CloseSend err: %v, bussName:%s", err, bussName)
		}
	} else if stream2, ok := streamInterface.(zegoPb.ZegoStream_StreamTransferClient); ok {
		err := stream2.CloseSend()
		if err != nil {
			log.Errorf("CloseChan stream.CloseSend err: %v, bussName:%s", err, bussName)
		}
	}
	m.once.Do(func() {
		if m.closeChan != nil {
			close(m.closeChan)
		}
	})
}

func (m *BaseStreamClientInfo) CloseOnlyChan() {
	m.once.Do(func() {
		if m.closeChan != nil {
			close(m.closeChan)
		}
	})
}

func (m *BaseStreamClientInfo) sendPing(ctx context.Context, uid, channelId uint32, streamId string, streamInterface interface{}, userName, bussName string, cancel context.CancelFunc, isInitail bool) error {
	if stream, ok := streamInterface.(pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient); ok {
		if err := stream.Send(&pb.RecvAudioStreamInRoomReq{
			ChannelId: channelId,
			Uid:       uid,
			//UserAudio: []byte("ping"),
			Ping:             true,
			ConnectionCreate: isInitail,
		}); err != nil {
			log.ErrorWithCtx(ctx, "monitorStream aigc stream.Send err: %v, userName:%s streamId:%s, bussName:%s, uid:%d", err, userName, streamId, bussName, uid)
			if handleRecvError(ctx, bussName, streamId, err) {
				log.InfoWithCtx(ctx, "handleStream Stream %s error: %v, reconnecting...,bussName:%s, uid:%d", streamId, err, bussName, uid)
				cancel()
			}
			return err
		}
		log.InfoWithCtx(ctx, "monitorStream aigc channelID：%d heartbeat send, userName:%s streamId:%s, bussName:%s, uid:%d", channelId, userName, streamId, bussName, uid)
	} else if stream, ok := streamInterface.(zegoPb.ZegoStream_StreamTransferClient); ok {
		if err := stream.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_Heartbeat, Data: []byte("ping")}); err != nil {
			log.ErrorWithCtx(ctx, "monitorStream zego stream.Send err: %v,  aiuid:%s streamId:%s, bussName:%s, uid:%d", err, userName, streamId, bussName, uid)
			//lerr := m.sMgr.HeartBeatFail(ctx, streamId, entity.ZegoStreamType)
			//if lerr != nil {
			//	log.ErrorWithCtx(ctx, "monitorStream zego HeartBeatFail err: %v, aiuid:%s streamId:%s, bussName:%s, uid:%d", lerr, userName, streamId, bussName, uid)
			//}
			if handleRecvError(ctx, bussName, streamId, err) {
				log.InfoWithCtx(ctx, "handleStream Stream %s error: %v, reconnecting...,bussName:%s, uid:%d", streamId, err, bussName, uid)
				cancel()
			}
			return err
		}
		log.InfoWithCtx(ctx, "monitorStream zego heartbeat send, aiuid:%s streamId:%s, bussName:%s, uid:%d", userName, streamId, bussName, uid)
	} else {
		log.ErrorWithCtx(ctx, "monitorStream stream type err, aiuid:%s streamId:%s, bussName:%s, uid:%d", userName, streamId, bussName, uid)
		return errors.New("stream type err")
	}
	return nil
}
func (m *BaseStreamClientInfo) monitorStream(ctx context.Context, uid, channelId uint32, streamId string, streamInterface interface{}, userName string, cancel context.CancelFunc) {
	var bussName string
	var closeChan chan struct{}
	if _, ok := streamInterface.(pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient); ok {
		closeChan = m.closeChan
		bussName = "aigc"
	} else if _, ok := streamInterface.(zegoPb.ZegoStream_StreamTransferClient); ok {
		closeChan = m.closeChan
		bussName = "zego"
	} else {
		log.Errorf("monitorStream stream type err, userName:%s streamId:%s, bussName:%s, uid:%d", userName, streamId, bussName, uid)
		return
	}
	err := m.sendPing(ctx, uid, channelId, streamId, streamInterface, userName, bussName, cancel, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "monitorStream sendInitial err: %v, userName:%s streamId:%s, bussName:%s, uid:%d", err, userName, streamId, bussName, uid)
		return
	}
	heartbeatTicker := time.NewTicker(10 * time.Second)
	defer heartbeatTicker.Stop()
	//ctx := context_info.GenReqId(context.Background())
	for {
		select {
		case <-closeChan:
			log.InfoWithCtx(ctx, "monitorStream close, userName:%s streamId:%s, bussName:%s, uid:%d", userName, streamId, bussName, uid)
			return
		case <-heartbeatTicker.C:
			_ = m.sendPing(ctx, uid, channelId, streamId, streamInterface, userName, bussName, cancel, false)
		}
	}
}

func (m *BaseStreamClientInfo) handleStream(ctx context.Context, uid, channelId uint32, streamId string, streamInterface interface{}, userName string, cancel context.CancelFunc) {

	var bussName string
	var closeChan chan struct{}
	if _, ok := streamInterface.(pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient); ok {
		closeChan = m.closeChan
		bussName = "aigc"
	} else if _, ok := streamInterface.(zegoPb.ZegoStream_StreamTransferClient); ok {
		bussName = "zego"
		closeChan = m.closeChan
	} else {
		log.Errorf("monitorStream stream type err, uid:%d streamId:%s", uid, streamId)
		return
	}
	msgChan := make(chan *CommonMsg, 100) // 带缓冲的channel

	// 在函数内初始化sync.Once（确保每个handleStream实例独立）
	var msgChanCloseOnce sync.Once

	workerCount := 3
	var wg sync.WaitGroup
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func(workerId int) {
			defer wg.Done()
			for msg := range msgChan {
				m.ProcessResponse(ctx, msg, cancel)
			}
			log.InfoWithCtx(ctx, "handleStream Worker:%d finished bussName:%s", workerId, bussName)
		}(i)
	}

	running := true
	for running {
		select {
		case <-closeChan:
			running = false
		default:
			if stream, ok := streamInterface.(pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient); ok {
				resp, err := stream.Recv()
				if err != nil {
					isDelAll := false
					if handleRecvError(ctx, "aigc", streamId, err) {
						log.InfoWithCtx(ctx, "handleStream Stream %s error: %v, reconnecting...,bussName:%s, uid:%d", streamId, err, bussName, uid)
						if !m.sMgr.reconnectStream(ctx, entity.AigcStreamType, userName, uid, resp.GetChannelId(), streamId) {
							isDelAll = true
						} else {
							isDelAll = false
						}
					} else {
						isDelAll = true
					}
					if isDelAll {
						//TODO 重试失败，退出并写kafka
						running = false
						m.CloseChan(streamInterface, bussName)
						if m.streamClients != nil && m.streamClients.zegoStreamClientInfo != nil {
							m.streamClients.zegoStreamClientInfo.CloseChan()
						} else {
							log.ErrorWithCtx(ctx, "zegoStreamClientInfo.CloseChan err:m.streamClients.aigcStreamClientInfo nil, streamId:%s aiUid:%d, bussName:%s", streamId, uid, bussName)
						}
					} else {
						running = false
						m.CloseChan(streamInterface, bussName)
					}
				} else {
					if streamId != strconv.Itoa(int(resp.GetChannelId())) {
						log.ErrorWithCtx(ctx, "handleStream Stream ID mismatch: expected %s, got %d, bussName:%s, uid:%d, resp:%+v", streamId, resp.GetChannelId(), bussName, uid, resp)
					}

					commonMsg := &CommonMsg{
						StreamId:  streamId,
						AigcMsg:   resp,
						MsgType:   entity.AigcStreamType,
						Uid:       uid,
						ChannelId: channelId,
					}
					msgChan <- commonMsg
					log.Infof("aigc [%s] Received: %+v", streamId, resp.String())
				}

			} else if stream, ok := streamInterface.(zegoPb.ZegoStream_StreamTransferClient); ok {
				resp, err := stream.Recv()
				if err != nil {
					isDelAll := false
					if handleRecvError(ctx, "zego-prox", streamId, err) {
						log.InfoWithCtx(ctx, "handleStream Stream %s error: %v, reconnecting..., bussName:%s, uid:%d", streamId, err, bussName, uid)
						if !m.sMgr.reconnectStream(ctx, entity.AigcStreamType, userName, uid, channelId, streamId) {
							isDelAll = true
						} else {
							//重連成功只刪除自己的老連接
							isDelAll = false
						}
					} else {
						isDelAll = true
					}

					if isDelAll {
						//TODO 重试失败，退出并写kafka
						running = false
						m.CloseChan(streamInterface, bussName)

						if m.streamClients != nil && m.streamClients.aigcStreamClientInfo != nil {
							m.streamClients.aigcStreamClientInfo.CloseChan()
						} else {
							log.ErrorWithCtx(ctx, "aigcStreamClientInfo.CloseChan err:m.streamClients.aigcStreamClientInfo nil, streamId:%s aiUid:%d, bussName:%s", streamId, uid, bussName)
						}
					} else {
						log.ErrorWithCtx(ctx, "handleStream aigc Stream %s error: %+v, bussName:%s, uid:%d", streamId, errors.WithStack(err), bussName, uid)
						running = false
						m.CloseChan(streamInterface, bussName)
					}
				} else {
					if resp.GetEvent() == zegoPb.EventType_ClientError {
						m.CloseChan(streamInterface, bussName)
						if m.streamClients != nil && m.streamClients.aigcStreamClientInfo != nil {
							m.streamClients.aigcStreamClientInfo.CloseChan()
						} else {
							log.ErrorWithCtx(ctx, "aigcStreamClientInfo.CloseChan err:m.streamClients.aigcStreamClientInfo nil, streamId:%s aiUid:%d Received: %+v, bussName:%s", streamId, uid, resp.String(), bussName)
						}

						log.InfoWithCtx(ctx, "ProcessResponse zego recv clienterr result, resp: %+v, streamId:%s", resp, streamId)
					} else {
						commonMsg := &CommonMsg{
							StreamId:  streamId,
							ZegoMsg:   resp,
							MsgType:   entity.ZegoStreamType,
							Uid:       uid,
							ChannelId: channelId,
						}
						msgChan <- commonMsg
					}
					if resp.GetEvent() != zegoPb.EventType_Audio {
						log.InfoWithCtx(ctx, "handleStream zego streamId:%s aiUid:%d Received: %+v, bussName:%s", streamId, uid, resp.String(), bussName)
					}
				}

			}

		}
	}

	msgChanCloseOnce.Do(func() {
		close(msgChan)
	})        // 关闭channel，通知工作者没有新消息了
	wg.Wait() // 等待所有工作者处理完剩余消息

}

func handleRecvError(ctx context.Context, bussName string, streamId string, err error) bool {
	if err == io.EOF {
		log.InfoWithCtx(ctx, "handleRecvError streamId:%s Stream  ended normally, bussName:%s", streamId, bussName)
		return false
	}

	// 检查 gRPC 状态码
	if status, ok := status.FromError(err); ok {
		switch status.Code() {
		case codes.Canceled:
			log.InfoWithCtx(ctx, "handleRecvError streamId:%s Stream canceled: %v, bussName:%s", streamId, status.Message(), bussName)
			return true

		case codes.DeadlineExceeded:
			log.InfoWithCtx(ctx, "handleRecvError streamId:%s  Stream timeout, will retry: %v, bussName:%s", streamId, status.Message(), bussName)
			return true // 不可以重试，登陆zego请求失败会报这个报，造成大量重连

		case codes.Unavailable, codes.ResourceExhausted:
			log.InfoWithCtx(ctx, "handleRecvError streamId:%s  Temporary error, will retry: %v, bussName:%s", streamId, status.Message(), bussName)
			time.Sleep(1 * time.Second) // 等待后重试
			return true                 // 可以重试

		case codes.Unimplemented, codes.PermissionDenied:
			log.InfoWithCtx(ctx, "handleRecvError streamId:%s  Permanent error: %v, bussName:%s", streamId, status.Message(), bussName)
			return false

		default:
			log.InfoWithCtx(ctx, "handleRecvError streamId:%s  gRPC Code [%v]: message:%s, bussName:%s", streamId, status.Code(), status.Message(), bussName)
			return false
		}
	}

	log.InfoWithCtx(ctx, "handleRecvError streamId:%s  Non-gRPC error: %v, bussName:%s", streamId, err, bussName)
	return false
}

func (m *BaseStreamClientInfo) ProcessResponse(ctx context.Context, commonMsg *CommonMsg, cancel context.CancelFunc) {
	//ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	//defer cancel()
	if commonMsg.MsgType == entity.AigcStreamType {
		if commonMsg.AigcMsg.GetPong() {
			err := m.sRecord.HeartbeatSuccess(ctx, commonMsg.StreamId, commonMsg.AigcMsg.PodName, entity.AigcStreamType)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcessResponse HeartBeaSuccess update record streamId:%s err: %v", commonMsg.StreamId, err)
			}
			log.InfoWithCtx(ctx, "ProcessResponse aigc received Pong channeldid:%d, commonMsg: %+v", commonMsg.AigcMsg.GetChannelId(), commonMsg)
		} else {
			log.InfoWithCtx(ctx, "ProcessResponse aigc receive channeldid:%d, uid:%d len(tts):%d  ",
				commonMsg.AigcMsg.GetChannelId(), commonMsg.AigcMsg.GetAiUid(), len(commonMsg.AigcMsg.GetTtsAudio()))
			_ = m.streamClients.SendAudioData(ctx, commonMsg.StreamId, commonMsg.AigcMsg.GetTtsAudio(), commonMsg.Uid, cancel)
		}
	} else if commonMsg.MsgType == entity.ZegoStreamType {
		msg := commonMsg.ZegoMsg
		//emptyAudio := 0
		switch msg.Event {
		case zegoPb.EventType_HeartbeatAck:
			heartbeatJson := &entity.ZegoHeartbeatAckJson{}
			err := json.Unmarshal([]byte(commonMsg.ZegoMsg.Json), heartbeatJson)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcessResponse zego streamId:%s msg:%s json.Unmarshal err:%v", commonMsg.StreamId, commonMsg.ZegoMsg.String(), err)
				return
			}
			err = m.sRecord.HeartbeatSuccess(ctx, commonMsg.StreamId, heartbeatJson.PodName, entity.ZegoStreamType)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcessResponse HeartBeaSuccess update record streamId:%s err: %v", commonMsg.StreamId, err)
			}
			log.InfoWithCtx(ctx, "ProcessResponse heartbeat ack, commonMsg: %+v, zegoMsg:%s", commonMsg, commonMsg.ZegoMsg.String())

		case zegoPb.EventType_LoginResult:
			log.InfoWithCtx(ctx, "ProcessResponse zego recv login result, commonMsg: %+v, zegoMsg:%s", commonMsg, commonMsg.ZegoMsg.String())
			loginJson := &entity.ZegoLoginJson{}
			err := json.Unmarshal([]byte(commonMsg.ZegoMsg.Json), loginJson)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcessResponse zego streamId:%s msg:%s json.Unmarshal err:%v", commonMsg.StreamId, commonMsg.ZegoMsg.String(), err)
				return
			}
			if loginJson.Code != 0 {
				m.CloseChan(m.streamClients.zegoStreamClientInfo, "zego")
				if m.streamClients != nil && m.streamClients.aigcStreamClientInfo != nil {
					m.streamClients.aigcStreamClientInfo.CloseChan()
				} else {
					log.ErrorWithCtx(ctx, "aigcStreamClientInfo.CloseChan err:m.streamClients.aigcStreamClientInfo nil, "+
						"commonMsg:%+v a", commonMsg)
				}
				err = m.sRecord.UpdateStreamStatus(ctx, commonMsg.StreamId, 0, entity.ZegoStreamStatusUnhealth)
				if err != nil {
					log.ErrorWithCtx(ctx, "ProcessResponse UpdateStreamStatus update record streamId:%s err: %v", commonMsg.StreamId, err)
				}

			}
			//else {
			//	err = m.sRecord.CreateStreamSuccess(ctx, commonMsg.StreamId, entity.ZegoStreamType)
			//	if err != nil {
			//		log.ErrorWithCtx(ctx, "ProcessResponse UpdateStreamStatus update record streamId:%s err: %v", commonMsg.StreamId, err)
			//	}
			//}

			//失败需要重试
		case zegoPb.EventType_StreamUpdate:
			event := new(streamUpdateEvent)
			err := json.Unmarshal([]byte(msg.Json), event)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcessResponse zego streamId:%s json.Unmarshal err:%v", commonMsg.StreamId, err)
				return
			}
			if event.UpdateType == ZegoStreamUpdateTypeAdd {
				for _, streamId := range event.StreamList {
					err := m.streamClients.startPullStream(ctx, commonMsg.StreamId, streamId, commonMsg.ChannelId, commonMsg.Uid, cancel)
					if err != nil {
						log.ErrorWithCtx(ctx, "ProcessResponse zego startPullStream err: %v, commonMsg: %+v, event:%+v", err, commonMsg, event)
					} else {
						log.InfoWithCtx(ctx, "ProcessResponse startPullStream, streamId:%s, commonMsg: %+v, event:%+v", streamId, commonMsg, event)
					}
				}
			} else if event.UpdateType == ZegoStreamUpdateTypeDel {
				for _, streamId := range event.StreamList {
					err := m.streamClients.stopPullStream(ctx, commonMsg.StreamId, streamId, cancel, commonMsg.Uid)
					if err != nil {
						log.ErrorWithCtx(ctx, "ProcessResponse zego stopPullStream err: %v, commonMsg: %+v, event:%+v", err, commonMsg, event)
					} else {
						log.InfoWithCtx(ctx, "ProcessResponse stopPullStream, streamId:%s, commonMsg: %+v, event:%+v", streamId, commonMsg, event)
					}
				}
			} else {
				log.ErrorWithCtx(ctx, "ProcessResponse zego updatetype err, commonMsg: %+v, event:%+v", commonMsg, event)
			}

		case zegoPb.EventType_Audio:
			if sumLen := sumAudioData(msg.Data); sumLen > 0 {
				err := m.streamClients.sendAigcStream(ctx, commonMsg.StreamId, commonMsg.ChannelId, commonMsg.Uid, msg.Data, cancel)
				if err != nil {
					log.ErrorWithCtx(ctx, "ProcessResponse zego sendAigcStream err: %v, commonMsg: %+v", err, commonMsg)
				}
				log.InfoWithCtx(ctx, "sendAigcStream dataLen:%d, uid:%d", len(msg.Data), commonMsg.Uid)
			}
		case zegoPb.EventType_RoomStateUpdate:
			roomStateUpdateMsg := &entity.RoomStateUpdateJson{}
			err := json.Unmarshal([]byte(commonMsg.ZegoMsg.Json), roomStateUpdateMsg)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcessResponse zego streamId:%s msg:%s json.Unmarshal err:%v", commonMsg.StreamId, commonMsg.ZegoMsg.String(), err)
				return
			}
			if roomStateUpdateMsg.ErrorCode != 0 {

			}
			if roomStateUpdateMsg.State == entity.StreamConnected {
				err = m.sRecord.CreateStreamSuccess(ctx, commonMsg.StreamId, entity.ZegoStreamType)
				if err != nil {
					log.ErrorWithCtx(ctx, "ProcessResponse UpdateStreamStatus update record streamId:%s err: %v", commonMsg.StreamId, err)
				}
			}

		}
	}

}

type AigcStreamClientInfo struct {
	BaseStreamClientInfo
	aigcStreamCli pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient
}

func NewAigcStreamClientInfo(aigcStreamCli pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient, sMgr *StreamManager,
	streamClients *StreamClients, sRecord *StreamRecord) *AigcStreamClientInfo {
	return &AigcStreamClientInfo{
		aigcStreamCli: aigcStreamCli,
		BaseStreamClientInfo: BaseStreamClientInfo{
			closeChan:     make(chan struct{}),
			sMgr:          sMgr,
			streamClients: streamClients,
			sRecord:       sRecord,
		},
	}
}

type ZegoStreamClientInfo struct {
	BaseStreamClientInfo
	zegoStreamCli zegoPb.ZegoStream_StreamTransferClient
}

func NewZegoStreamClientInfo(zegoStreamCli zegoPb.ZegoStream_StreamTransferClient, sMgr *StreamManager, streamClients *StreamClients,
	sRecord *StreamRecord) *ZegoStreamClientInfo {
	return &ZegoStreamClientInfo{
		zegoStreamCli: zegoStreamCli,
		BaseStreamClientInfo: BaseStreamClientInfo{
			closeChan:     make(chan struct{}),
			sMgr:          sMgr,
			streamClients: streamClients,
			sRecord:       sRecord,
		},
	}
}

func (m *AigcStreamClientInfo) CloseChan() {
	if m.aigcStreamCli != nil {
		_ = m.aigcStreamCli.CloseSend()
	}
	m.BaseStreamClientInfo.CloseOnlyChan()
}

func (m *ZegoStreamClientInfo) CloseChan() {
	if m.zegoStreamCli != nil {
		_ = m.zegoStreamCli.CloseSend()
	}
	m.BaseStreamClientInfo.CloseOnlyChan()
}

type StreamClients struct {
	aigcStreamClientInfo *AigcStreamClientInfo
	zegoStreamClientInfo *ZegoStreamClientInfo
	streamId             string
	userName             string
	uid                  uint32
	channelMicClient     channel_mic.ChannelMicClient
}

func (m *StreamClients) startPullStream(ctx context.Context, strChannelId, streamId string, channelId, uid uint32, cancel context.CancelFunc) error {
	isInMic, err := m.isUidInMic(ctx, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "startPullStream isUidInMic error: %v, streamId: %s", err, streamId)
		return err
	}

	if !isInMic {
		log.ErrorWithCtx(ctx, "startPullStream isUidInMic: not in mic, streamId: %s, channelId:%d, uid:%d", streamId, channelId, uid)
		return nil
	}

	jsonRaw, err := json.Marshal(map[string]string{"stream_id": streamId})
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal error: %v, streamId: %s", err, streamId)
		return err
	}

	if m.zegoStreamClientInfo == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamCli is nil, streamId: %s", streamId)
	} else if m.zegoStreamClientInfo.zegoStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamClientInfo.zegoStreamCli is nil, streamId: %s", streamId)
	} else {
		err = m.zegoStreamClientInfo.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_StartPullStream, Json: string(jsonRaw)})
		if err != nil {
			if handleRecvError(ctx, "zego", streamId, err) {
				log.InfoWithCtx(ctx, "startPullStream Stream %s error: %v, reconnecting...,bussName:%s, uid:%d", streamId, err, "zego", uid)
				cancel()
			}
			log.ErrorWithCtx(ctx, "startPullStream zegoStreamCli.Send err: %v, streamId: %s", err, streamId)
			return err
		}
	}

	return nil
}

func (m *StreamClients) stopPullStream(ctx context.Context, strChannelId, streamId string, cancel context.CancelFunc, uid uint32) error {
	jsonRaw, err := json.Marshal(map[string]string{"stream_id": streamId})
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal error: %v, streamId: %s", err, streamId)
		return err
	}

	if m.zegoStreamClientInfo == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamCli is nil, streamId: %s", streamId)
	} else if m.zegoStreamClientInfo.zegoStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamClientInfo.zegoStreamCli is nil, streamId: %s", streamId)
	} else {
		err = m.zegoStreamClientInfo.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_StopPullStream, Json: string(jsonRaw)})
		if err != nil {
			if handleRecvError(ctx, "zego", streamId, err) {
				log.InfoWithCtx(ctx, "stopPullStream Stream %s error: %v, reconnecting...,bussName:%s, uid:%d", streamId, err, "zego", uid)
				cancel()
			}
			log.ErrorWithCtx(ctx, "stopPullStream zegoStreamCli.Send err: %v, streamId: %s", err, streamId)
			return err
		}
	}

	return nil
}

func (m *StreamClients) sendAigcStream(ctx context.Context, streamId string, channelId, uid uint32, data []byte, cancel context.CancelFunc) error {

	if m.aigcStreamClientInfo == nil {
		log.ErrorWithCtx(ctx, "streamClis.aigcStreamClientInfo is nil, streamId: %s", streamId)
	} else if m.aigcStreamClientInfo.aigcStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.aigcStreamClientInfo.aigcStreamCli is nil, streamId: %s", streamId)
	} else {
		err := m.aigcStreamClientInfo.aigcStreamCli.Send(&pb.RecvAudioStreamInRoomReq{
			ChannelId: channelId,
			Uid:       uid,
			UserAudio: data,
		})
		if err != nil {
			if handleRecvError(ctx, "aigc", streamId, err) {
				log.InfoWithCtx(ctx, "sendAigcStream Stream %s error: %v, reconnecting...,bussName:%s, uid:%d", streamId, err, "aigc", uid)
				cancel()
			}
			log.ErrorWithCtx(ctx, "sendAigcStream error: %v, streamId: %s", err, streamId)
			return err
		}
		log.InfoWithCtx(ctx, "sendAigcStream streamId: %s, data len:%d, sum:%d", streamId, len(data), sumAudioData(data))
	}

	return nil
}

func (m *StreamClients) SendAudioData(ctx context.Context, streamId string, data []byte, uid uint32, cancel context.CancelFunc) error {

	if m.zegoStreamClientInfo == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamCli is nil, streamId: %s", streamId)
	} else if m.zegoStreamClientInfo.zegoStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamClientInfo.zegoStreamCli is nil, streamId: %s", streamId)
	} else {
		err := m.zegoStreamClientInfo.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_Audio, Data: data})
		if err != nil {
			if handleRecvError(ctx, "zego", streamId, err) {
				log.InfoWithCtx(ctx, "SendAudioData Stream %s error: %v, reconnecting...,bussName:%s, uid:%d", streamId, err, "zego", uid)
				cancel()
			}
			log.ErrorWithCtx(ctx, "sendAudioData error: %v, streamId: %s", err, streamId)
			return err
		}
		log.InfoWithCtx(ctx, "SendAudioData data:%d", len(data))
	}

	return nil
}

func (m *StreamClients) LoginRoom(ctx context.Context, streamId, userName, streamName string) error {
	jsonRaw, err := json.Marshal(map[string]any{"app_id": tt_config.GetAigcZegoStreamConfig().GetZegoAppId(),
		"room_id": streamId, "user_id": userName, "stream_id": streamName, "user_name": userName + "123"})
	// 测试代码
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal error: %v, appId: %d,userName: %s, streamId: %s", err, tt_config.GetAigcZegoStreamConfig().GetZegoAppId(), userName, streamId)
		return err
	}

	if m.zegoStreamClientInfo == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamCli is nil, streamId: %s", streamId)
	} else if m.zegoStreamClientInfo.zegoStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.aigcStreamClientInfo.aigcStreamCli is nil, streamId: %s", streamId)
	} else {
		err = m.zegoStreamClientInfo.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_Login, Json: string(jsonRaw)})
		if err != nil {
			m.aigcStreamClientInfo.CloseChan()
			m.zegoStreamClientInfo.CloseChan()
			log.ErrorWithCtx(ctx, "loginRoom zegoStreamCli.Send err:%v, streamId: %s, appid:%d", err, streamId,
				tt_config.GetAigcZegoStreamConfig().GetZegoAppId())
			return err
		}
	}
	log.InfoWithCtx(ctx, "logining streamId:%s, userName:%s, streamName:%s, appid:%d", streamId, userName,
		streamName, tt_config.GetAigcZegoStreamConfig().GetZegoAppId())
	return nil
}

func (m *StreamClients) isUidInMic(ctx context.Context, cid, uid uint32) (bool, error) {
	micList, err := m.channelMicClient.GetMicrList(ctx, &channel_mic.GetMicrListReq{
		ChannelId: cid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "client.GetMicrList failed %v, uid %d, cid %d", err, uid, cid)
		return false, protocol.ToServerError(err)
	}
	for _, mic := range micList.GetAllMicList() {
		if uid == mic.MicUid {
			return true, err
		}
	}
	return false, err
}
