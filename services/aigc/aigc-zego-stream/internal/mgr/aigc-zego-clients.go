package mgr

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic"
	"gitlab.ttyuyin.com/tyr/x/log"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_voice_room"
	zegoPb "golang.52tt.com/protocol/services/rcmd/zego_stream_gateway/zego_stream"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/entity"
	tt_config "golang.52tt.com/services/aigc/aigc-zego-stream/internal/ttconfig"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"io"
	"strconv"
	"strings"
	"sync"
	"time"
)

const (
	ZegoStreamUpdateTypeAdd = "add"
	ZegoStreamUpdateTypeDel = "del"
)

type BaseStreamClientInfo struct {
	closeChan     chan struct{}
	once          sync.Once
	sMgr          *StreamManager
	streamClients *StreamClients
	sRecord       *StreamRecord
}

func (m *BaseStreamClientInfo) CloseChan(streamInterface interface{}, bussName string) {
	if stream, ok := streamInterface.(pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient); ok {
		err := stream.CloseSend()
		if err != nil {
			log.Errorf("CloseChan stream.CloseSend err: %v, bussName:%s", err, bussName)
		}
	} else if stream2, ok := streamInterface.(zegoPb.ZegoStream_StreamTransferClient); ok {
		err := stream2.CloseSend()
		if err != nil {
			log.Errorf("CloseChan stream.CloseSend err: %v, bussName:%s", err, bussName)
		}
	}
	m.once.Do(func() {
		if m.closeChan != nil {
			close(m.closeChan)
		}
	})
}

func (m *BaseStreamClientInfo) CloseOnlyChan() {
	m.once.Do(func() {
		if m.closeChan != nil {
			close(m.closeChan)
		}
	})
}

func (m *BaseStreamClientInfo) sendPing(ctx context.Context, uid, channelId uint32, streamId string, streamInterface interface{}, userName, bussName string, cancel context.CancelFunc, isInitail bool) error {
	if stream, ok := streamInterface.(pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient); ok {
		bPing := true
		if isInitail {
			bPing = false
		}
		if err := stream.Send(&pb.RecvAudioStreamInRoomReq{
			ChannelId: channelId,
			Uid:       uid,
			//UserAudio: []byte("ping"),
			Ping:             bPing,
			ConnectionCreate: isInitail,
		}); err != nil {
			log.ErrorWithCtx(ctx, "monitorStream aigc stream.Send err: %v, userName:%s streamId:%s, bussName:%s, uid:%d", err, userName, streamId, bussName, uid)
			if handleRecvError(ctx, bussName, err, channelId, uid) {
				log.InfoWithCtx(ctx, "handleStream Stream %s error: %v, reconnecting...,bussName:%s, uid:%d", streamId, err, bussName, uid)
			}
			cancel()
			return err
		} else {
			log.InfoWithCtx(ctx, "monitorStream aigc channelID：%d heartbeat send, userName:%s streamId:%s, bussName:%s, uid:%d, isInitail:%t", channelId, userName, streamId, bussName, uid, isInitail)
		}
	} else if stream, ok := streamInterface.(zegoPb.ZegoStream_StreamTransferClient); ok {
		if err := stream.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_Heartbeat, Data: []byte("ping")}); err != nil {
			log.ErrorWithCtx(ctx, "monitorStream zego stream.Send err: %v,  aiuid:%s streamId:%s, bussName:%s, uid:%d", err, userName, streamId, bussName, uid)
			//lerr := m.sMgr.HeartBeatFail(ctx, streamId, entity.ZegoStreamType)
			//if lerr != nil {
			//	log.ErrorWithCtx(ctx, "monitorStream zego HeartBeatFail err: %v, aiuid:%s streamId:%s, bussName:%s, uid:%d", lerr, userName, streamId, bussName, uid)
			//}
			if handleRecvError(ctx, bussName, err, channelId, uid) {
				log.InfoWithCtx(ctx, "handleStream Stream %s error: %v, reconnecting...,bussName:%s, uid:%d", streamId, err, bussName, uid)
			}
			cancel()
			return err
		} else {
			log.InfoWithCtx(ctx, "monitorStream zego heartbeat send, aiuid:%s streamId:%s, bussName:%s, uid:%d", userName, streamId, bussName, uid)
		}
	} else {
		log.ErrorWithCtx(ctx, "monitorStream stream type err, aiuid:%s streamId:%s, bussName:%s, uid:%d", userName, streamId, bussName, uid)
		return errors.New("stream type err")
	}
	return nil
}
func (m *BaseStreamClientInfo) monitorStream(ctx context.Context, uid, channelId uint32, streamId string, streamInterface interface{}, userName string, cancel context.CancelFunc) {
	var bussName string
	var closeChan chan struct{}
	if _, ok := streamInterface.(pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient); ok {
		closeChan = m.closeChan
		bussName = "aigc"
	} else if _, ok := streamInterface.(zegoPb.ZegoStream_StreamTransferClient); ok {
		closeChan = m.closeChan
		bussName = "zego"
	} else {
		log.Errorf("monitorStream stream type err, userName:%s streamId:%s, bussName:%s, uid:%d", userName, streamId, bussName, uid)
		return
	}
	if bussName == "aigc" {
		err := m.sendPing(ctx, uid, channelId, streamId, streamInterface, userName, bussName, cancel, true)
		if err != nil {
			log.ErrorWithCtx(ctx, "monitorStream sendInitial err: %v, userName:%s streamId:%s, bussName:%s, uid:%d", err, userName, streamId, bussName, uid)
			return
		}
	}
	heartbeatTicker := time.NewTicker(10 * time.Second)
	defer heartbeatTicker.Stop()
	for {
		select {
		case <-closeChan:
			log.InfoWithCtx(ctx, "monitorStream close, userName:%s streamId:%s, bussName:%s, uid:%d", userName, streamId, bussName, uid)
			return
		case <-heartbeatTicker.C:
			_ = m.sendPing(ctx, uid, channelId, streamId, streamInterface, userName, bussName, cancel, false)
		}
	}
}

func getUidFromStreamName(streamName string) (uint32, error) {
	// 假设流名称格式为 "user_{uid}_channel_{channelId}"
	//var uid uint32
	strNames := strings.Split(streamName, "-")
	if len(strNames) >= 2 {
		uid, err := strconv.Atoi(strNames[1])
		if err != nil {
			return 0, fmt.Errorf("invalid stream name format: %s", streamName)
		}
		return uint32(uid), nil
	} else {
		return 0, fmt.Errorf("invalid stream name format: %s", streamName)
	}
}

func handleRecvError(ctx context.Context, bussName string, err error, channelId, uid uint32) bool {
	if err == io.EOF {
		log.InfoWithCtx(ctx, "handleRecvError Stream  ended normally, bussName:%s,channelId:%d, uid:%d", bussName, channelId, uid)
		time.Sleep(1 * time.Second) // 等待后重试
		return true
	}

	// 检查 gRPC 状态码
	if status, ok := status.FromError(err); ok {
		switch status.Code() {
		case codes.Canceled:
			log.InfoWithCtx(ctx, "handleRecvError Stream canceled: %v, bussName:%s,channelId:%d, uid:%d", status.Message(), bussName, channelId, uid)
			return true

		case codes.DeadlineExceeded:
			log.InfoWithCtx(ctx, "handleRecvError Stream timeout: %v, bussName:%s,channelId:%d, uid:%d", status.Message(), bussName, channelId, uid)
			return true // 不可以重试，登陆zego请求失败会报这个报，造成大量重连

		case codes.Unavailable, codes.ResourceExhausted:
			log.InfoWithCtx(ctx, "handleRecvError  Temporary error, will retry: %v, bussName:%s,channelId:%d, uid:%d", status.Message(), bussName, channelId, uid)
			time.Sleep(1 * time.Second) // 等待后重试
			return true                 // 可以重试

		case codes.Unimplemented, codes.PermissionDenied:
			log.InfoWithCtx(ctx, "handleRecvError Permanent error: %v, bussName:%s,channelId:%d, uid:%d", status.Message(), bussName, channelId, uid)
			return false

		default:
			log.InfoWithCtx(ctx, "handleRecvError gRPC Code [%v]: message:%s, bussName:%s,channelId:%d, uid:%d", status.Code(), status.Message(), bussName, channelId, uid)
			return false
		}
	}

	log.InfoWithCtx(ctx, "handleRecvError Non-gRPC error: %v, bussName:%s,channelId:%d, uid:%d", err, bussName, channelId, uid)
	return false
}

func (m *BaseStreamClientInfo) ProcessResponse(ctx context.Context, commonMsg *CommonMsg, cancel context.CancelFunc) {
	if commonMsg.MsgType == entity.AigcStreamType {
		if commonMsg.AigcMsg == nil {
			log.ErrorWithCtx(ctx, "ProcessResponse AigcMsg is nil, commonMsg: %+v, channelId:%d, uid:%d", commonMsg, commonMsg.ChannelId, commonMsg.Uid)
		} else if commonMsg.AigcMsg.GetPong() {
			err := m.sRecord.HeartbeatSuccess(ctx, commonMsg.StreamId, commonMsg.AigcMsg.PodName, entity.AigcStreamType)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcessResponse HeartBeaSuccess update record streamId:%s err: %v, channelId:%d, uid:%d", commonMsg.StreamId, err, commonMsg.ChannelId, commonMsg.Uid)
			}
			log.InfoWithCtx(ctx, "ProcessResponse aigc received Pong channeldid:%d, commonMsg: %+v, channelId:%d, uid:%d", commonMsg.AigcMsg.GetChannelId(), commonMsg, commonMsg.ChannelId, commonMsg.Uid)
		} else {
			log.InfoWithCtx(ctx, "ProcessResponse aigc, send to zego, channeldid:%d, uid:%d, data:%d, channelId:%d, uid:%d",
				commonMsg.AigcMsg.GetChannelId(), commonMsg.AigcMsg.GetAiUid(), len(commonMsg.AigcMsg.GetTtsAudio()), commonMsg.ChannelId, commonMsg.Uid)
			_ = m.streamClients.SendAudioData(ctx, commonMsg.StreamId, commonMsg.AigcMsg.GetTtsAudio(), commonMsg.ChannelId, commonMsg.Uid, cancel)
		}
	} else if commonMsg.MsgType == entity.ZegoStreamType {
		msg := commonMsg.ZegoMsg
		if msg == nil {
			log.ErrorWithCtx(ctx, "ProcessResponse ZegoMsg is nil, commonMsg: %+v, channelId:%d, uid:%d", commonMsg, commonMsg.ChannelId, commonMsg.Uid)
			return
		}
		switch msg.Event {
		case zegoPb.EventType_HeartbeatAck:
			heartbeatJson := &entity.ZegoHeartbeatAckJson{}
			err := json.Unmarshal([]byte(commonMsg.ZegoMsg.Json), heartbeatJson)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcessResponse zego streamId:%s msg:%s json.Unmarshal err:%v, channelId:%d, uid:%d", commonMsg.StreamId, commonMsg.ZegoMsg.String(), err, commonMsg.ChannelId, commonMsg.Uid)
				return
			}
			err = m.sRecord.HeartbeatSuccess(ctx, commonMsg.StreamId, heartbeatJson.PodName, entity.ZegoStreamType)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcessResponse HeartBeaSuccess update record streamId:%s err: %v, channelId:%d, uid:%d", commonMsg.StreamId, err, commonMsg.ChannelId, commonMsg.Uid)
			}
			log.InfoWithCtx(ctx, "ProcessResponse heartbeat ack, commonMsg: %+v, zegoMsg:%s, channelId:%d, uid:%d", commonMsg, commonMsg.ZegoMsg.String(), commonMsg.ChannelId, commonMsg.Uid)

		case zegoPb.EventType_LoginResult:
			log.InfoWithCtx(ctx, "ProcessResponse zego recv login result, commonMsg: %+v, zegoMsg:%s, channelId:%d, uid:%d", commonMsg, commonMsg.ZegoMsg.String(), commonMsg.ChannelId, commonMsg.Uid)
			loginJson := &entity.ZegoLoginJson{}
			err := json.Unmarshal([]byte(commonMsg.ZegoMsg.Json), loginJson)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcessResponse zego streamId:%s msg:%s json.Unmarshal err:%v, channelId:%d, uid:%d", commonMsg.StreamId, commonMsg.ZegoMsg.String(), err, commonMsg.ChannelId, commonMsg.Uid)
				return
			}
			if loginJson.Code != 0 {
				log.InfoWithCtx(ctx, "ProcessResponse zego recv login err,code:%d, commonMsg: %+v,channelId:%d, uid:%d", loginJson.Code, commonMsg, commonMsg.ChannelId, commonMsg.Uid)
			}

			//失败需要重试
		case zegoPb.EventType_StreamUpdate:
			event := new(streamUpdateEvent)
			err := json.Unmarshal([]byte(msg.Json), event)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcessResponse zego streamId:%s json.Unmarshal err:%v, channelId:%d, uid:%d", commonMsg.StreamId, err, commonMsg.ChannelId, commonMsg.Uid)
				return
			}
			if event.UpdateType == ZegoStreamUpdateTypeAdd {
				for _, streamName := range event.StreamList {
					uid, err := getUidFromStreamName(streamName)
					if err != nil {
						log.ErrorWithCtx(ctx, "ProcessResponse zego getUidFromStreamName err: %v, commonMsg: %+v, event:%+v, channelId:%d, uid:%d", err, commonMsg, event, commonMsg.ChannelId, commonMsg.Uid)
						continue
					}
					err = m.streamClients.startPullStream(ctx, streamName, commonMsg.ChannelId, uid, cancel)
					if err != nil {
						log.ErrorWithCtx(ctx, "ProcessResponse zego startPullStream err: %v, commonMsg: %+v, event:%+v, channelId:%d, uid:%d", err, commonMsg, event, commonMsg.ChannelId, commonMsg.Uid)
					} else {
						log.InfoWithCtx(ctx, "ProcessResponse startPullStream, streamName:%s, commonMsg: %+v, event:%+v, channelId:%d, aiuid:%d, uid:%d", streamName, commonMsg, event, commonMsg.ChannelId, commonMsg.Uid, uid)
					}
				}
			} else if event.UpdateType == ZegoStreamUpdateTypeDel {
				for _, streamName := range event.StreamList {
					err := m.streamClients.stopPullStream(ctx, streamName, cancel, commonMsg.ChannelId, commonMsg.Uid)
					if err != nil {
						log.ErrorWithCtx(ctx, "ProcessResponse zego stopPullStream err: %v, commonMsg: %+v, event:%+v, channelId:%d, uid:%d", err, commonMsg, event, commonMsg.ChannelId, commonMsg.Uid)
					} else {
						log.InfoWithCtx(ctx, "ProcessResponse stopPullStream, streamName:%s, commonMsg: %+v, event:%+v, channelId:%d, uid:%d", streamName, commonMsg, event, commonMsg.ChannelId, commonMsg.Uid)
					}
				}
			} else {
				log.ErrorWithCtx(ctx, "ProcessResponse zego updatetype err, commonMsg: %+v, event:%+v, channelId:%d, uid:%d", commonMsg, event, commonMsg.ChannelId, commonMsg.Uid)
			}

		case zegoPb.EventType_Audio:
			audioJson := &entity.SendAudioMsg{}
			err := json.Unmarshal([]byte(commonMsg.ZegoMsg.Json), audioJson)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcessResponse zego streamId:%s msg:%s json.Unmarshal err:%v, channelId:%d, uid:%d", commonMsg.StreamId, commonMsg.ZegoMsg.String(), err, commonMsg.ChannelId, commonMsg.Uid)
				return
			}
			if audioJson.Code != 0 {
				log.ErrorWithCtx(ctx, "ProcessResponse zego recv audio err,code:%d, commonMsg: %+v, zegoMsg:%s, channelId:%d, uid:%d", audioJson.Code, commonMsg, commonMsg.ZegoMsg.String(), commonMsg.ChannelId, commonMsg.Uid)
				return
			}

			uid, err := getUidFromStreamName(audioJson.StreamName)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcessResponse zego getUidFromStreamName err: %v, commonMsg: %+v, audioJson:%+v, channelId:%d, uid:%d", err, commonMsg, audioJson, commonMsg.ChannelId, commonMsg.Uid)
				return
			}
			if sumLen := sumAudioData(msg.Data); sumLen > 0 {
				err := m.streamClients.sendAigcStream(ctx, commonMsg.StreamId, commonMsg.ChannelId, uid, msg.Data, cancel)
				if err != nil {
					log.ErrorWithCtx(ctx, "ProcessResponse zego, send to aigc err: %v, commonMsg: %+v, channelId:%d, uid:%d", err, commonMsg, commonMsg.ChannelId, commonMsg.Uid)
				} else {
					log.InfoWithCtx(ctx, "ProcessResponse zego, send to aigc, dataLen:%d, sumLen:%d, channelId:%d, aiuid:%d, uid:%d", len(msg.Data), sumLen, commonMsg.ChannelId, commonMsg.Uid, uid)

				}
			}
		case zegoPb.EventType_RoomStateChanged:
			roomStateUpdateMsg := &entity.RoomStateUpdateJson{}
			err := json.Unmarshal([]byte(commonMsg.ZegoMsg.Json), roomStateUpdateMsg)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcessResponse zego streamId:%s msg:%s json.Unmarshal err:%v, channelId:%d, uid:%d", commonMsg.StreamId, commonMsg.ZegoMsg.String(), err, commonMsg.ChannelId, commonMsg.Uid)
				return
			}
			//if roomStateUpdateMsg.ErrorCode != 0 {
			//
			//}
			if roomStateUpdateMsg.State == entity.StreamConnected {
				err = m.sRecord.CreateStreamSuccess(ctx, commonMsg.StreamId, entity.ZegoStreamType)
				if err != nil {
					log.ErrorWithCtx(ctx, "ProcessResponse UpdateStreamStatus update record streamId:%s err: %v, channelId:%d, uid:%d", commonMsg.StreamId, err, commonMsg.ChannelId, commonMsg.Uid)
				}
			}

		}

	}
}

func (m *BaseStreamClientInfo) handleStream(ctx context.Context, uid, channelId uint32, streamId string, streamInterface interface{}, userName string, cancel context.CancelFunc) {

	var bussName string
	var closeChan chan struct{}
	if _, ok := streamInterface.(pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient); ok {
		closeChan = m.closeChan
		bussName = "aigc"
	} else if _, ok := streamInterface.(zegoPb.ZegoStream_StreamTransferClient); ok {
		bussName = "zego"
		closeChan = m.closeChan
	} else {
		log.Errorf("monitorStream stream type err,  streamId:%s, channelId:%d, uid:%d", streamId, channelId, uid)
		return
	}
	msgChan := make(chan *CommonMsg, 100) // 音频带缓冲的channel
	cmdChan := make(chan *CommonMsg, 20)  // 命令带缓冲的channel

	// 在函数内初始化sync.Once（确保每个handleStream实例独立）
	var msgChanCloseOnce sync.Once

	workerCount := tt_config.GetAigcZegoStreamConfig().GetHandleGoroutineNum()
	var wg sync.WaitGroup
	for i := 0; i < workerCount; i++ {
		wg.Add(2)
		go func(workerId int) {
			defer wg.Done()
			for msg := range msgChan {
				m.ProcessResponse(ctx, msg, cancel)
			}
			log.InfoWithCtx(ctx, "handleStream Worker:%d finished bussName:%s, channelId:%d, uid:%d", workerId, bussName, channelId, uid)
		}(i)
		go func(workerId int) {
			defer wg.Done()
			for msg := range cmdChan {
				m.ProcessResponse(ctx, msg, cancel)
			}
			log.InfoWithCtx(ctx, "handleStream Worker:%d finished bussName:%s, channelId:%d, uid:%d", workerId, bussName, channelId, uid)
		}(i)
	}

	running := true
	for running {
		select {
		case <-closeChan:
			running = false
		default:
			if stream, ok := streamInterface.(pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient); ok {
				resp, err := stream.Recv()
				if err != nil {
					isDelAll := false
					if handleRecvError(ctx, bussName, err, channelId, uid) {
						log.InfoWithCtx(ctx, "handleRecvError %s error: %v, reconnecting...,bussName:%s, channelId:%d, uid:%d", streamId, err, bussName, channelId, uid)
						if !m.sMgr.reconnectStream(ctx, entity.AigcStreamType, userName, channelId, uid, streamId, bussName) {
							isDelAll = true
						} else {
							isDelAll = false
						}
					} else {
						isDelAll = true
					}
					if isDelAll {
						running = false
						m.CloseChan(streamInterface, bussName)
						if m.streamClients != nil && m.streamClients.zegoStreamClientInfo != nil {
							m.streamClients.zegoStreamClientInfo.CloseChan()
						} else {
							log.ErrorWithCtx(ctx, "zegoStreamClientInfo.CloseChan err:m.streamClients.aigcStreamClientInfo nil, streamId:%s aiUid:%d, bussName:%s, channelId:%d, uid:%d", streamId, bussName, channelId, uid)
						}
						svrInfo := metainfo.GetServiceInfo(ctx)
						err = m.sRecord.UpdateStreamStatus(svrInfo.RequestID(), streamId, entity.AigcStreamStatusUnHealth, 0)
						if err != nil {
							log.ErrorWithCtx(ctx, "handleStream UpdateStreamStatus err: %v, streamId:%s aiUid:%d, bussName:%s, channelId:%d, uid:%d", err, streamId, bussName, channelId, uid)
						}
					} /*else {
						running = false
						m.CloseChan(streamInterface, bussName)
					}*/
				} else {
					if streamId != strconv.Itoa(int(resp.GetChannelId())) {
						log.ErrorWithCtx(ctx, "handleStream Stream ID mismatch: expected %s, got %d, bussName:%s, resp:%+v, channelId:%d, uid:%d", streamId, resp.GetChannelId(), bussName, resp, channelId, uid)
					}

					commonMsg := &CommonMsg{
						StreamId:  streamId,
						AigcMsg:   resp,
						MsgType:   entity.AigcStreamType,
						Uid:       uid,
						ChannelId: channelId,
					}
					if resp == nil {
						log.ErrorWithCtx(ctx, "handleStream aigc resp is nil, commonMsg: %+v, channelId:%d, uid:%d", commonMsg, commonMsg.ChannelId, commonMsg.Uid)
					} else if resp.GetPong() {
						select {
						case cmdChan <- commonMsg:
						default:
							log.ErrorWithCtx(ctx, "handleStream err: aigc cmdChan is full, commonMsg:%+v", commonMsg)
						}
					} else {
						select {
						case msgChan <- commonMsg:
						default:
							log.ErrorWithCtx(ctx, "handleStream err: aigc msgChan is full, commonMsg:%+v", commonMsg)
						}
					}
					log.Infof("handleStream aigc [%s] Received: %+v, channelId:%d, uid:%d", streamId, resp.String(), channelId, uid)
				}

			} else if stream, ok := streamInterface.(zegoPb.ZegoStream_StreamTransferClient); ok {
				resp, err := stream.Recv()
				if err != nil {
					isDelAll := false
					if handleRecvError(ctx, bussName, err, channelId, uid) {
						log.InfoWithCtx(ctx, "handleStream Stream %s error: %v, reconnecting..., bussName:%s, channelId:%d, uid:%d", streamId, err, bussName, channelId, uid)
						if !m.sMgr.reconnectStream(ctx, entity.ZegoStreamType, userName, channelId, uid, streamId, bussName) {
							isDelAll = true
						} else {
							//重連成功只刪除自己的老連接
							isDelAll = false
						}
					} else {
						isDelAll = true
					}

					if isDelAll {
						//TODO 重试失败，退出并写kafka
						running = false
						m.CloseChan(streamInterface, bussName)

						if m.streamClients != nil && m.streamClients.aigcStreamClientInfo != nil {
							m.streamClients.aigcStreamClientInfo.CloseChan()
						} else {
							log.ErrorWithCtx(ctx, "aigcStreamClientInfo.CloseChan err:m.streamClients.aigcStreamClientInfo nil, streamId:%s, bussName:%s, channelId:%d, uid:%d", streamId, bussName, channelId, uid)
						}
						svrInfo := metainfo.GetServiceInfo(ctx)
						err = m.sRecord.UpdateStreamStatus(svrInfo.RequestID(), streamId, 0, entity.ZegoStreamStatusUnhealth)
						if err != nil {
							log.ErrorWithCtx(ctx, "handleStream UpdateStreamStatus err: %v, streamId:%s aiUid:%d, bussName:%s, channelId:%d, uid:%d", err, streamId, bussName, channelId, uid)
						}
					} /*else {
						log.ErrorWithCtx(ctx, "handleStream aigc Stream %s error: %+v, bussName:%s, uid:%d", streamId, errors.WithStack(err), bussName, uid)
						running = false
						m.CloseChan(streamInterface, bussName)
					}*/
				} else {
					if resp.GetEvent() == zegoPb.EventType_ClientError {
						//不处理，由zego-proxy删除
						/*m.CloseChan(streamInterface, bussName)
						if m.streamClients != nil && m.streamClients.aigcStreamClientInfo != nil {
							m.streamClients.aigcStreamClientInfo.CloseChan()
						} else {
							log.ErrorWithCtx(ctx, "aigcStreamClientInfo.CloseChan err:m.streamClients.aigcStreamClientInfo nil, streamId:%s, Received: %+v, bussName:%s, channelId:%d, uid:%d", streamId, resp.String(), bussName, channelId, uid)
						}*/

						log.ErrorWithCtx(ctx, "ProcessResponse zego recv clienterr result, resp: %+v, streamId:%s, channelId:%d, uid:%d", resp, streamId, channelId, uid)
					} else {
						commonMsg := &CommonMsg{
							StreamId:  streamId,
							ZegoMsg:   resp,
							MsgType:   entity.ZegoStreamType,
							Uid:       uid,
							ChannelId: channelId,
						}

						if resp == nil {
							log.ErrorWithCtx(ctx, "handleStream zego resp is nil, commonMsg: %+v, channelId:%d, uid:%d", commonMsg, commonMsg.ChannelId, commonMsg.Uid)
						} else if resp.GetEvent() == zegoPb.EventType_Audio {
							select {
							case msgChan <- commonMsg:
							default:
								log.ErrorWithCtx(ctx, "handleStream err: zego msgChan is full, commonMsg:%+v", commonMsg)
							}
						} else {
							select {
							case cmdChan <- commonMsg:
								log.InfoWithCtx(ctx, "handleStream zego streamId:%s Received: %+v, bussName:%s, channelId:%d, uid:%d", streamId, resp.String(), bussName, channelId, uid)
							default:
								log.ErrorWithCtx(ctx, "handleStream err: zego cmdChan is full, commonMsg:%+v", commonMsg)
							}
						}
					}
				}

			}

		}
	}

	log.InfoWithCtx(ctx, "handleStream is exiting, bussName:%s, channelId:%d, uid:%d", bussName, channelId, uid)
	msgChanCloseOnce.Do(func() {
		close(msgChan)
		close(cmdChan)
	}) // 关闭channel，通知工作者没有新消息了
	wg.Wait() // 等待所有工作者处理完剩余消息

}

type AigcStreamClientInfo struct {
	BaseStreamClientInfo
	aigcStreamCli pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient
}

func NewAigcStreamClientInfo(aigcStreamCli pb.AigcVoiceRoomService_RecvAudioStreamInRoomClient, sMgr *StreamManager,
	streamClients *StreamClients, sRecord *StreamRecord) *AigcStreamClientInfo {
	return &AigcStreamClientInfo{
		aigcStreamCli: aigcStreamCli,
		BaseStreamClientInfo: BaseStreamClientInfo{
			closeChan:     make(chan struct{}),
			sMgr:          sMgr,
			streamClients: streamClients,
			sRecord:       sRecord,
		},
	}
}

type ZegoStreamClientInfo struct {
	BaseStreamClientInfo
	zegoStreamCli zegoPb.ZegoStream_StreamTransferClient
}

func NewZegoStreamClientInfo(zegoStreamCli zegoPb.ZegoStream_StreamTransferClient, sMgr *StreamManager, streamClients *StreamClients,
	sRecord *StreamRecord) *ZegoStreamClientInfo {
	return &ZegoStreamClientInfo{
		zegoStreamCli: zegoStreamCli,
		BaseStreamClientInfo: BaseStreamClientInfo{
			closeChan:     make(chan struct{}),
			sMgr:          sMgr,
			streamClients: streamClients,
			sRecord:       sRecord,
		},
	}
}

func (m *AigcStreamClientInfo) CloseChan() {
	if m.aigcStreamCli != nil {
		_ = m.aigcStreamCli.CloseSend()
	}
	m.BaseStreamClientInfo.CloseOnlyChan()
}

func (m *ZegoStreamClientInfo) CloseChan() {
	if m.zegoStreamCli != nil {
		_ = m.zegoStreamCli.CloseSend()
	}
	m.BaseStreamClientInfo.CloseOnlyChan()
}

type StreamClients struct {
	aigcStreamClientInfo *AigcStreamClientInfo
	zegoStreamClientInfo *ZegoStreamClientInfo
	streamId             string
	userName             string
	uid                  uint32
	channelMicClient     channel_mic.ChannelMicClient
}

func (m *StreamClients) startPullStream(ctx context.Context, streamName string, channelId, uid uint32, cancel context.CancelFunc) error {
	isInMic, err := m.isUidInMic(ctx, channelId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "startPullStream isUidInMic error: %v, streamName: %s, channelId:%d, uid:%d", err, streamName, channelId, uid)
		return err
	}

	if !isInMic {
		log.ErrorWithCtx(ctx, "startPullStream isUidInMic: not in mic, streamName: %s, channelId:%d, uid:%d", streamName, channelId, uid)
		return nil
	}

	jsonRaw, err := json.Marshal(map[string]string{"stream_id": streamName})
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal error: %v, streamName: %s, channelId:%d, uid:%d", err, streamName, channelId, uid)
		return err
	}

	if m.zegoStreamClientInfo == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamCli is nil, streamName: %s, channelId:%d, uid:%d", streamName, channelId, uid)
	} else if m.zegoStreamClientInfo.zegoStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamClientInfo.zegoStreamCli is nil, streamName: %s, channelId:%d, uid:%d", streamName, channelId, uid)
	} else {
		err = m.zegoStreamClientInfo.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_StartPullStream, Json: string(jsonRaw)})
		if err != nil {
			if handleRecvError(ctx, "zego", err, channelId, uid) {
				log.InfoWithCtx(ctx, "startPullStream streamName %s error: %v, reconnecting...,bussName:%s, channelId:%d, uid:%d", streamName, err, "zego", uid, channelId, uid)
			}

			log.ErrorWithCtx(ctx, "startPullStream zegoStreamCli.Send err: %v, streamName: %s, channelId:%d, uid:%d", err, streamName, channelId, uid)
			cancel()
			return err
		}
	}

	return nil
}

func (m *StreamClients) stopPullStream(ctx context.Context, streamName string, cancel context.CancelFunc, channelId, uid uint32) error {
	jsonRaw, err := json.Marshal(map[string]string{"stream_id": streamName})
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal error: %v, streamId: %s, channelId:%d, uid:%d", err, streamName, channelId, uid)
		return err
	}

	if m.zegoStreamClientInfo == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamCli is nil, streamId: %s, channelId:%d, uid:%d", streamName, channelId, uid)
	} else if m.zegoStreamClientInfo.zegoStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamClientInfo.zegoStreamCli is nil, streamId: %s, channelId:%d, uid:%d", streamName, channelId, uid)
	} else {
		err = m.zegoStreamClientInfo.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_StopPullStream, Json: string(jsonRaw)})
		if err != nil {
			if handleRecvError(ctx, "zego", err, channelId, uid) {
				log.InfoWithCtx(ctx, "stopPullStream Stream %s error: %v, reconnecting...,bussName:%s, channelId:%d, uid:%d", streamName, err, "zego", uid, channelId, uid)
			}

			log.ErrorWithCtx(ctx, "stopPullStream zegoStreamCli.Send err: %v, streamId: %s, channelId:%d, uid:%d", err, streamName, channelId, uid)
			cancel()
			return err
		}
	}

	return nil
}

func (m *StreamClients) sendAigcStream(ctx context.Context, streamId string, channelId, uid uint32, data []byte, cancel context.CancelFunc) error {

	if m.aigcStreamClientInfo == nil {
		log.ErrorWithCtx(ctx, "streamClis.aigcStreamClientInfo is nil, streamId: %s, channelId:%d, uid:%d", streamId, channelId, uid)
	} else if m.aigcStreamClientInfo.aigcStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.aigcStreamClientInfo.aigcStreamCli is nil, streamId: %s, channelId:%d, uid:%d", streamId, channelId, uid)
	} else {
		err := m.aigcStreamClientInfo.aigcStreamCli.Send(&pb.RecvAudioStreamInRoomReq{
			ChannelId: channelId,
			Uid:       uid,
			UserAudio: data,
		})
		if err != nil {
			if handleRecvError(ctx, "aigc", err, channelId, uid) {
				log.InfoWithCtx(ctx, "sendAigcStream Stream %s error: %v, reconnecting...,bussName:%s, channelId:%d, uid:%d", streamId, err, "aigc", channelId, uid)
			}
			log.ErrorWithCtx(ctx, "sendAigcStream error: %v, streamId: %s, data len:%d, sum:%d, channelId:%d, uid:%d", err, streamId, len(data), sumAudioData(data), channelId, uid)
			cancel()
			return err
		}
	}

	return nil
}

func (m *StreamClients) SendAudioData(ctx context.Context, streamId string, data []byte, channelId, uid uint32, cancel context.CancelFunc) error {

	if m.zegoStreamClientInfo == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamCli is nil, streamId: %s, channelId:%d, uid:%d", streamId, channelId, uid)
	} else if m.zegoStreamClientInfo.zegoStreamCli == nil {
		log.ErrorWithCtx(ctx, "streamClis.zegoStreamClientInfo.zegoStreamCli is nil, streamId: %s, channelId:%d, uid:%d", streamId, channelId, uid)
	} else {
		err := m.zegoStreamClientInfo.zegoStreamCli.Send(&zegoPb.EventWrap{Event: zegoPb.EventType_Audio, Data: data})
		if err != nil {
			if handleRecvError(ctx, "zego", err, channelId, uid) {
				log.InfoWithCtx(ctx, "SendAudioData Stream %s error: %v, reconnecting...,bussName:%s, channelId:%d, uid:%d", streamId, err, "zego", channelId, uid)
			}

			log.ErrorWithCtx(ctx, "sendAudioData error: %v, streamId: %s, channelId:%d, uid:%d", err, streamId, channelId, uid)
			cancel()
			return err
		}
	}

	return nil
}

func (m *StreamClients) isUidInMic(ctx context.Context, cid, uid uint32) (bool, error) {
	micList, err := m.channelMicClient.GetMicrList(ctx, &channel_mic.GetMicrListReq{
		ChannelId: cid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "client.GetMicrList failed %v, uid %d, cid %d", err, uid, cid)
		return false, protocol.ToServerError(err)
	}
	for _, mic := range micList.GetAllMicList() {
		if uid == mic.MicUid {
			return true, err
		}
	}
	return false, err
}
