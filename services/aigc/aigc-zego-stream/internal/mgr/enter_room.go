package mgr

import (
	"context"
	accountGo "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/log"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/entity"
	"os"
	"strconv"
	"time"
)

func NewEnterRoom(streamMgr *StreamManager, accountCli *accountGo.Client, aigcAccount aigc_account.AigcAccountClient, streamRecord *StreamRecord) *EnterRoom {
	return &EnterRoom{
		streamMgr:    streamMgr,
		accountCli:   accountCli,
		aigcAccount:  aigcAccount,
		streamRecord: streamRecord,
	}
}

type EnterRoom struct {
	accountCli   *accountGo.Client
	aigcAccount  aigc_account.AigcAccountClient
	streamMgr    *StreamManager
	streamRecord *StreamRecord
}

func (z *EnterRoom) isAiAccount(ctx context.Context, uid uint32) (bool, string, error) {
	userInfo, err := z.accountCli.GetUserByUid(ctx, uid)
	if err != nil {
		return false, "", err
	}

	userName := userInfo.GetUsername()
	if accountPB.USER_TYPE(userInfo.GetUserType()) != accountPB.USER_TYPE_USER_TYPE_ROBOT {
		return false, userName, nil
	}

	aiAccountRsp, accountErr := z.aigcAccount.GetAIAccount(ctx, &aigc_account.GetAIAccountRequest{
		Uid: uid,
	})

	if accountErr != nil {
		log.ErrorWithCtx(ctx, "Login aigcAccount.GetAIAccount err:%v, uid:%d", accountErr, uid)
		return false, userName, accountErr
	}
	if aiAccountRsp.GetAccount() == nil || aiAccountRsp.GetAccount().GetIsUnregister() {
		// AI账号不存在或已注销
		log.InfoWithCtx(ctx, "aiAccount not found or unregister, uid:%d", uid)
		return false, userName, nil
	}
	return true, userName, nil
}

func (z *EnterRoom) Login(ctx context.Context, channelId, uid uint32) error {
	if uid == 0 || channelId == 0 {
		log.WarnWithCtx(ctx, "Login invalid param, channelId:%d, uid:%d", channelId, uid)
		return nil
	}
	isAiAccount, userName, err := z.isAiAccount(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "Login isAiAccount err: %v, channelId:%d, uid:%d", err, channelId, uid)
		return err
	}

	if !isAiAccount {
		return nil
	}

	streamId := strconv.FormatInt(int64(channelId), 10)

	// 建流前写入流记录，建流过程grpc调用失败，走兜底重新建流
	err = z.streamRecord.UpsertStreamItem(ctx, &entity.StreamItem{
		StreamId:         streamId,
		ChannelId:        channelId,
		Uid:              uid,
		PodName:          GetPodName(),
		ZegoStreamStatus: entity.ZegoStreamStatusPending,
		AigcStreamStatus: entity.ZegoStreamStatusPending,
		UpdateTime:       time.Now(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "Logic UpsertStreamItem cid:%d uid:%d err", channelId, uid, err)
		return err
	}

	err = z.streamMgr.CreateStream(ctx, entity.AigcStreamType, streamId, userName, uid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateStream err", err)
		return err
	}
	err = z.streamMgr.CreateStream(ctx, entity.ZegoStreamType, streamId, userName, uid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateStream err", err)
		return err
	}

	streamName := GetStreamName(uid, channelId)
	err = z.streamMgr.LoginRoom(ctx, streamId, userName, streamName)
	if err != nil {
		log.ErrorWithCtx(ctx, "Login loginRoom err: %v, channelId:%d, uid:%d", err, channelId, uid)
		return err
	}
	log.InfoWithCtx(ctx, "logining channelId:%d, uid:%d, streamName:%s, userName:%s", channelId, uid, streamName, userName)
	return nil
}

func (z *EnterRoom) Logout(ctx context.Context, channelId, uid uint32) error {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	isAiAccount, _, err := z.isAiAccount(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "Login isAiAccount err: %v, channelId:%d, uid:%d", err, channelId, uid)
		return err
	}

	if !isAiAccount {
		return nil
	}
	streamId := strconv.FormatInt(int64(channelId), 10)
	err = z.streamRecord.UpdateStreamByExitRoom(ctx, streamId)
	return err
}

func GetPodName() string {
	name := os.Getenv("MY_POD_NAME")
	return name
}

func (z *EnterRoom) DeleteStreamByExitRoom(ctx context.Context) error {
	podName := GetPodName()
	if podName == "" {
		log.Warnf("NewGaugeCounter: pod name is empty")
	}
	roomRecords, err := z.streamRecord.GetStreamItemsLimitByExitRoom(ctx, podName)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteStreamByExitRoom GetStreamItemsLimitByExitRoom err: %v", err)
		return err
	}
	for _, record := range roomRecords {
		if podName == record.PodName {
			z.streamMgr.DeleteStreamId(ctx, record.StreamId)
			err = z.streamRecord.DeleteStreamByStreamId(ctx, record.StreamId)
			if err != nil {
				log.ErrorWithCtx(ctx, "DeleteStreamByExitRoom DeleteStreamByStreamId err: %v, streamId:%s", err, record.StreamId)
			}
		}
	}
	return nil
}

func (z *EnterRoom) LoopDetectExitRoom() {
	ticker := time.NewTicker(time.Second * 10)
	defer func() {
		ticker.Stop()
	}()

	for range ticker.C {
		refreshCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		err := z.DeleteStreamByExitRoom(refreshCtx)
		if err != nil {
			log.ErrorWithCtx(refreshCtx, "LoopDetectExitRoom DeleteStreamByExitRoom err: %v", err)
		}
		cancel()
	}
}
