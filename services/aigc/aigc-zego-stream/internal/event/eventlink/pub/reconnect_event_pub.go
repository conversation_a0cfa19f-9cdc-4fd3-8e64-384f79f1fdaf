package pub

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	pb "golang.52tt.com/protocol/services/aigc/aigc-zego-stream"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/event"
	"strconv"
)

func (eb *eventBus) PublishReconnectEvent(ctx context.Context, ev *pb.AigcStreamReconnectEvent) error {
	if err := eb.Publish(ctx, event.AigcStreamReconnectEv, strconv.Itoa(int(ev.GetChannelId())), ev); err != nil {
		log.ErrorWithCtx(ctx, "PublishReconnectEvent event(%+v) err:%v", ev, err)
		return err
	}
	log.InfoWithCtx(ctx, "PublishReconnectEvent event(%+v) finished", ev)
	return nil
}


