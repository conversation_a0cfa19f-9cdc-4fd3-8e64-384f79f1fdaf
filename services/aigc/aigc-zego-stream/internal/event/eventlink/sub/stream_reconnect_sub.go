package sub

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	context_info "golang.52tt.com/pkg/context-info"
	pb "golang.52tt.com/protocol/services/aigc/aigc-zego-stream"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/event"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/mgr"
)

type StreamReconnectEventSubscriber struct {
	eventBus     event.EventBus
	enterRoomMgr *mgr.EnterRoom
}

func NewStreamReconnectEventSubscriber(ctx context.Context, eventBus event.EventBus, enterRoomMgr *mgr.EnterRoom) (*StreamReconnectEventSubscriber, error) {

	sub := &StreamReconnectEventSubscriber{
		enterRoomMgr: enterRoomMgr,
		eventBus:     eventBus,
	}

	err := eventBus.Subscribe(ctx, event.SubNameStreamReconnectEv, sub.handleEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewStreamReconnectEventSubscriber Subscribe err: %v", err)
		return nil, err
	}
	return sub, nil
}

func (s *StreamReconnectEventSubscriber) handleEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	ctx = context_info.GenReqId(ctx)
	reconnectEvent := &pb.AigcStreamReconnectEvent{}
	err := proto.Unmarshal(msg.Value, reconnectEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleEvent Failed to proto.Unmarshal err:%+v", err)
		return err, false
	}

	log.InfoWithCtx(ctx, "StreamReconnectEvent handleEvent:%s, pid:%d, offset:%d", reconnectEvent.String(), msg.Partition, msg.Offset)
	err = s.enterRoomMgr.Login(ctx, reconnectEvent.GetChannelId(), reconnectEvent.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "StreamReconnectEventSubscriber handleEvent err:%v, event:%s", err, reconnectEvent.String())
		return err, false
	}
	return nil, false
}
