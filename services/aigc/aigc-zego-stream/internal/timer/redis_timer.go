package timer

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/timer"
	pb "golang.52tt.com/protocol/services/aigc/aigc-zego-stream"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/entity"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/event"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/mgr"
	tt_config "golang.52tt.com/services/aigc/aigc-zego-stream/internal/ttconfig"
	"time"
)

func NewRedisTimer(streamManager *mgr.StreamManager, enterRoomMgr *mgr.EnterRoom, streamRecord *mgr.StreamRecord,
	eventBus event.EventBus) *RedisTimer {
	//data, err := os.ReadFile("img/test.wav")
	//if err != nil {
	//	log.Errorf("ReadFile err:%v", err)
	//	return nil
	//}
	//demoAudioData := data[44:]
	//log.Infof("test file len:%d", len(demoAudioData))
	//var demoAudioData []byte
	return &RedisTimer{
		streamManager: streamManager,
		//demoAudioData: demoAudioData,
		enterRoomMgr: enterRoomMgr,
		streamRecord: streamRecord,
		eventBus:     eventBus,
	}
}

type RedisTimer struct {
	timerD        *timer.Timer
	demoAudioData []byte
	streamManager *mgr.StreamManager
	enterRoomMgr  *mgr.EnterRoom
	streamRecord  *mgr.StreamRecord

	eventBus event.EventBus
}

func (t *RedisTimer) Start() error {

	ctx, cancelFunc := context.WithCancel(context.Background())
	defer cancelFunc()

	timerD, err := timer.NewTimerD(ctx, "aigc-zego-stream")
	if err != nil {
		log.Errorf("NewTimerD err:%v", err)
		return err
	}
	t.timerD = timerD

	every30Sec := "@every 10s"
	err = timerD.AddTask(every30Sec, "HandleTimeOutStream", timer.BuildFromLambda(func(ctx context.Context) {
		_ = t.HandleTimeOutStream(ctx)
	}))
	if err != nil {
		log.Errorf("redis timer DeleteStreamByExitRoom start err:%v", err)
		return err
	}

	timerD.Start()
	return nil
}

func (t *RedisTimer) Stop() {
	t.timerD.Stop()
}

func (t *RedisTimer) HandleTimeOutStream(ctx context.Context) error {
	for {
		var lastTime time.Time
		var lastStreamId string
		limit := 100
		items, err := t.streamRecord.GetFallbackItemByPage(ctx, lastTime, lastStreamId, int64(limit))
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFallbackItemByPage lastTime:%v lastStreamId:%s err:%v", lastTime, lastStreamId, err)
			return err
		}

		for _, item := range items {
			err = t.streamRecord.UpdateStreamStatus(ctx, item.StreamId, entity.AigcStreamStatusFallBack, entity.ZegoStreamStatusFallBack)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateStreamStatus streamId:%s err:%v", item.StreamId, err)
				continue
			}
			_ = t.eventBus.PublishReconnectEvent(ctx, &pb.AigcStreamReconnectEvent{
				ChannelId: item.ChannelId,
				Uid:       item.Uid,
			})
		}

		if len(items) < limit {
			break
		}
	}
	return nil
}

func (t *RedisTimer) TestLogin(ctx context.Context) error {
	//_ = t.streamManager.SendAudioData(ctx, tt_config.GetAigcZegoStreamConfig().GetTestStreamId(), t.demoAudioData)
	log.InfoWithCtx(ctx, "TestSend 2638879")
	_ = t.enterRoomMgr.Login(ctx, 2042119, 2638879)

	return nil
}

func (t *RedisTimer) TestSend(ctx context.Context) error {
	_ = t.streamManager.SendAudioData(ctx, tt_config.GetAigcZegoStreamConfig().GetTestStreamId(), t.demoAudioData)
	return nil
}

func (t *RedisTimer) TestTimer() error {
	go func() {
		ticker := time.NewTicker(time.Second * 10)
		defer func() {
			ticker.Stop()
		}()

		for range ticker.C {
			ctx, cancelFunc := context.WithCancel(context.Background())
			//_ = t.enterRoomMgr.DeleteStreamByExitRoom(ctx)
			_ = t.TestLogin(ctx)
			cancelFunc()
			break
		}

	}()
	return nil
}

func (t *RedisTimer) TestTimer2() error {
	go func() {
		ticker := time.NewTicker(time.Second * 10)
		defer func() {
			ticker.Stop()
		}()

		for range ticker.C {
			ctx, cancelFunc := context.WithCancel(context.Background())
			//_ = t.enterRoomMgr.DeleteStreamByExitRoom(ctx)
			_ = t.TestSend(ctx)
			cancelFunc()
		}

	}()
	return nil
}
