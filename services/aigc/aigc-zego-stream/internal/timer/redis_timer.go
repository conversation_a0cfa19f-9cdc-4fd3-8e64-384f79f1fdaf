package timer

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/timer"
	pb "golang.52tt.com/protocol/services/aigc/aigc-zego-stream"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/entity"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/event"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/mgr"
	tt_config "golang.52tt.com/services/aigc/aigc-zego-stream/internal/ttconfig"
	"time"
)

func NewRedisTimer(streamManager *mgr.StreamManager, enterRoomMgr *mgr.EnterRoom, streamRecord *mgr.StreamRecord,
	eventBus event.EventBus) *RedisTimer {
	//data, err := os.ReadFile("img/test.wav")
	//if err != nil {
	//	log.Errorf("ReadFile err:%v", err)
	//	return nil
	//}
	//demoAudioData := data[44:]
	//log.Infof("test file len:%d", len(demoAudioData))
	//var demoAudioData []byte
	return &RedisTimer{
		streamManager: streamManager,
		//demoAudioData: demoAudioData,
		enterRoomMgr:     enterRoomMgr,
		streamRecord:     streamRecord,
		eventBus:         eventBus,
	}
}

type RedisTimer struct {
	timerD *timer.Timer
	//demoAudioData []byte
	streamManager    *mgr.StreamManager
	enterRoomMgr     *mgr.EnterRoom
	streamRecord     *mgr.StreamRecord

	eventBus event.EventBus
}

func (t *RedisTimer) Start() error {

	ctx, cancelFunc := context.WithCancel(context.Background())
	defer cancelFunc()

	timerD, err := timer.NewTimerD(ctx, "aigc-zego-stream")
	if err != nil {
		log.Errorf("NewTimerD err:%v", err)
		return err
	}
	t.timerD = timerD

	// 需要兜底重连的流
	err = timerD.AddTask(tt_config.GetAigcZegoStreamConfig().GetFallBackHandleTimeoutCorn(), "HandleTimeOutStream", timer.BuildFromLambda(func(ctx context.Context) {
		_ = t.HandleTimeOutStream(ctx)
	}))
	if err != nil {
		log.Errorf("redis timer DeleteStreamByExitRoom start err:%v", err)
		return err
	}

	// 清理下麦后长时间未删除记录的流
	err = timerD.AddTask(tt_config.GetAigcZegoStreamConfig().GetFallBackDeleteRecordCorn(), "HandleOffMicStream", timer.BuildFromLambda(func(ctx context.Context) {
		_ = t.HandleOffMicStream(ctx)
	}))
	if err != nil {
		log.Errorf("redis timer DeleteStreamByExitRoom start err:%v", err)
		return err
	}

	timerD.Start()
	return nil
}

func (t *RedisTimer) Stop() {
	t.timerD.Stop()
}

func (t *RedisTimer) HandleTimeOutStream(ctx context.Context) error {
	for {
		var lastTime time.Time
		var lastStreamId string
		limit := 100
		items, err := t.streamRecord.GetFallbackItemByPage(ctx, lastTime, lastStreamId, int64(limit))
		if err != nil {
			log.ErrorWithCtx(ctx, "GetFallbackItemByPage lastTime:%v lastStreamId:%s err:%v", lastTime, lastStreamId, err)
			return err
		}
		channelUidMap := make(map[uint32]uint32, len(items))
		for _, item := range items {
			channelUidMap[item.ChannelId] = item.Uid
		}
		onMicMap, err := t.streamManager.GetUserOnMicMap(ctx, channelUidMap)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserOnMicMap channelUidMap:%v err:%v", channelUidMap, err)
			return err
		}
		for _, item := range items {
			if _, ok := onMicMap[item.Uid]; !ok {
				// 异常情况，用户实际不在麦上了，修改在麦状态
				err = t.streamRecord.UpdateStreamByMicState(ctx, item.StreamId, true)
				if err != nil {
					log.ErrorWithCtx(ctx, "UpdateStreamByMicState streamId:%s err:%v", item.StreamId, err)
				}
				continue
			}
			err = t.streamRecord.UpdateStreamStatus(ctx, item.StreamId, entity.AigcStreamStatusFallBack, entity.ZegoStreamStatusFallBack)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateStreamStatus streamId:%s err:%v", item.StreamId, err)
				continue
			}
			_ = t.eventBus.PublishReconnectEvent(ctx, &pb.AigcStreamReconnectEvent{
				ChannelId: item.ChannelId,
				Uid:       item.Uid,
			})
		}

		if len(items) < limit {
			break
		}
	}
	return nil
}

func (t *RedisTimer) HandleOffMicStream(ctx context.Context) error {
	items, err := t.streamRecord.GetAbnormalOffMicStream(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAbnormalOffMicStream err:%v", err)
		return err
	}
	if len(items) == 0 {
		return nil
	}
	channelUidMap := make(map[uint32]uint32, len(items))
	for _, item := range items {
		channelUidMap[item.ChannelId] = item.Uid
	}
	userOnMicMap, err := t.streamManager.GetUserOnMicMap(ctx, channelUidMap)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserOnMicMap channelUidMap:%v err:%v", channelUidMap, err)
		return err
	}
	streamIds := make([]string, 0, len(items))
	for _, item := range items {
		if _, ok := userOnMicMap[item.Uid]; ok {
			// 异常情况，用户还在麦上，需要修正数据
			err = t.streamRecord.UpdateStreamByMicState(ctx, item.StreamId, false)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateStreamByMicState streamId:%s err:%v", item.StreamId, err)
			}
			continue
		}
		streamIds = append(streamIds, item.StreamId)
	}
	if len(streamIds) == 0 {
		return nil
	}
	err = t.streamRecord.DeleteStreamByStreamIds(ctx, streamIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteRecordByFallbackOffMic err:%v", err)
		return err
	}
	return nil
}


func (t *RedisTimer) TestLogin(ctx context.Context) error {
	//_ = t.streamManager.SendAudioData(ctx, tt_config.GetAigcZegoStreamConfig().GetTestStreamId(), t.demoAudioData)
	log.InfoWithCtx(ctx, "TestSend 2638879")
	_ = t.enterRoomMgr.Login(ctx, 2042119, 2638879)

	return nil
}

func (t *RedisTimer) TestSend(ctx context.Context) error {
	//ctx = context_info.GenReqId(ctx)
	//_ = t.streamManager.SendAudioData(ctx, tt_config.GetAigcZegoStreamConfig().GetTestStreamId(), t.demoAudioData)
	return nil
}

func (t *RedisTimer) TestTimer() error {
	go func() {
		ticker := time.NewTicker(time.Second * 10)
		defer func() {
			ticker.Stop()
		}()

		for range ticker.C {
			ctx, cancelFunc := context.WithCancel(context.Background())
			//_ = t.enterRoomMgr.DeleteStreamByExitRoom(ctx)
			_ = t.TestLogin(ctx)
			cancelFunc()
			break
		}

	}()
	return nil
}

func (t *RedisTimer) TestTimer2() error {
	go func() {
		ticker := time.NewTicker(time.Second * 10)
		defer func() {
			ticker.Stop()
		}()

		for range ticker.C {
			ctx, cancelFunc := context.WithCancel(context.Background())
			//_ = t.enterRoomMgr.DeleteStreamByExitRoom(ctx)
			_ = t.TestSend(ctx)
			cancelFunc()
		}

	}()
	return nil
}
