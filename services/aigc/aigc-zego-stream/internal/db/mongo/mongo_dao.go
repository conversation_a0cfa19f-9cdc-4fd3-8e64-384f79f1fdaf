package mongo

import (
	"context"
	"errors"
	"fmt"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/entity"
	tt_config "golang.52tt.com/services/aigc/aigc-zego-stream/internal/ttconfig"
	"time"
)

type MongoDao struct {
	mongoCli       *mongo.Client
	streamInfoPool *mongo.Collection
}

func NewMongoDao(ctx context.Context, cfg *config.MongoConfig) (*MongoDao, error) {
	//client := &mongo.Client{}
	client, err := mongo.NewClient(cfg.OptionsForPrimaryPreferred())
	if err != nil {
		log.ErrorWithCtx(ctx, "NewMongoDao NewClient err: (%v)", err)
		return nil, err
	}

	if err = client.Connect(ctx); err != nil {
		log.ErrorWithCtx(ctx, "newServer Connect err: (%v)", err)
		return nil, err
	}

	if err = client.Ping(ctx, readpref.Primary()); err != nil {
		log.ErrorWithCtx(ctx, "newServer Ping err: (%v)", err)
		return nil, err
	}

	streamInfoPool := client.Database(cfg.Database).Collection("streaminfo")

	return &MongoDao{
		mongoCli:       client,
		streamInfoPool: streamInfoPool,
	}, nil
}

func (m *MongoDao) Close() error {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	err := m.mongoCli.Disconnect(ctx)
	cancel()
	if err != nil {
		log.Errorf("mongoCli.Disconnect err:%v", err)
		return err
	}
	return nil
}

func (m *MongoDao) CreateIndexes() error {
	var err error
	opts := options.CreateIndexes().SetMaxTime(10 * time.Second)
	_, err = m.streamInfoPool.Indexes().CreateMany(
		context.Background(),
		[]mongo.IndexModel{
			{
				Keys:    bson.D{primitive.E{Key: "channel_id", Value: 1}},
				Options: options.Index().SetUnique(true),
			}, {
				Keys:    bson.D{primitive.E{Key: "zego_stream_status", Value: 1}},
				Options: options.Index().SetUnique(true),
			}, {
				Keys:    bson.D{primitive.E{Key: "is_off_mic", Value: 1}},
				Options: options.Index().SetUnique(true),
			},
		},
		opts,
	)
	if err != nil {
		log.Errorf("createIndexs channelBoxPool err:%v", err)
	}

	return nil
}

func (m *MongoDao) GetStreamItemsLimitByExitRoom(ctx context.Context, podName string) ([]*entity.StreamItem, error) {
	var filter = bson.M{"is_off_mic": true, "pod_name": podName}

	cursor, err := m.streamInfoPool.Find(ctx, filter, options.Find().SetLimit(50))
	if err != nil {
		log.ErrorWithCtx(ctx, "Getentity.StreamItemsLimit Find err:%v", err)
		return nil, err
	}
	var items []*entity.StreamItem
	err = cursor.All(ctx, &items)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetStreamItemsLimitByExitRoom cursor.All err:%v", err)
		return nil, err
	}
	return items, nil
}

func (m *MongoDao) UpdateStreamByMicState(ctx context.Context, streamId string, offMic bool) error {
	curTime := time.Now()
	var updates bson.M
	if offMic {
		updates = bson.M{"is_off_mic": true, "last_off_mic_time": curTime.Unix(), "update_time": curTime}
	} else {
		updates = bson.M{"is_off_mic": false, "last_off_mic_time": 0, "update_time": curTime}
	}

	res := m.streamInfoPool.FindOneAndUpdate(ctx, bson.M{"_id": streamId}, bson.M{"$set": updates},
		options.FindOneAndUpdate().SetReturnDocument(options.Before))
	if err := res.Err(); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			log.WarnWithCtx(ctx, "UpdateStreamByMicState no document matched streamId(%s)", streamId)
			return nil
		}
		log.ErrorWithCtx(ctx, "UpdateStreamByMicState streamInfoPool.FindOneAndUpdate err: %v, channelId:%s", err, streamId)
		return err
	}

	/*var bi entity.StreamItem
	err := res.Decode(&bi)
	if err != nil {
		log.ErrorWithCtx(ctx, "Mongo.UpdateBox res.Decode err: %v", err)
		return nil, err
	}*/

	log.DebugWithCtx(ctx, "MongoDao.UpdateBoxInfo result: %v", res)
	return nil
}

func (m *MongoDao) UpsertStreamItem(ctx context.Context, item *entity.StreamItem) error {
	updates := bson.M{
		"pod_name":           item.PodName,
		"zego_pod_name":      item.ZegoPodName,
		"zego_stream_status": item.ZegoStreamStatus,
		"aigc_pod_name":      item.AigcPodName,
		"aigc_stream_status": item.AigcStreamStatus,
		"update_time":        item.UpdateTime,
	}

	_, err := m.streamInfoPool.UpdateOne(ctx,
		bson.M{"_id": item.StreamId},
		bson.M{
			"$set": updates,
			"$setOnInsert": bson.M{
				"channel_id":        item.ChannelId,
				"is_off_mic":        false,
				"last_off_mic_time": 0,
				"uid":               item.Uid,
				"create_time":       time.Now(),
			},
		},
		options.Update().SetUpsert(true))
	if err != nil {
		log.ErrorWithCtx(ctx, "UpsertStreamItem FindOneAndUpdate item(%v) err: %v", item, err)
		return err
	}
	return nil
}

func (m *MongoDao) UpdateStreamStatus(ctx context.Context, streamId string, aigcStatus, zegoStatus uint32) error {

	updates := bson.M{}
	if zegoStatus != 0 {
		updates["zego_stream_status"] = zegoStatus
	}
	if aigcStatus != 0 {
		updates["aigc_stream_status"] = aigcStatus
	}
	if len(updates) == 0 {
		log.WarnWithCtx(ctx, "UpdateStreamStatus no status to update for streamId(%s)", streamId)
		return nil
	}
	updates["update_time"] = time.Now()

	_, err := m.streamInfoPool.UpdateOne(ctx, bson.M{"_id": streamId}, bson.M{"$set": updates})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateStreamStatus UpdateOne streamId(%s) err: %v", streamId, err)
		return err
	}
	log.InfoWithCtx(ctx, "UpdateStreamStatus  streamId(%s) updates:%v success", streamId, updates)
	return nil
}

func (m *MongoDao) UpdateHeartbeatInfo(ctx context.Context, streamId, podName string, streamType uint32) error {
	now := time.Now()
	var updates bson.M
	if streamType == entity.ZegoStreamType {
		updates = bson.M{
			"zego_stream_heartbeat_time": now.Unix(),
			"zego_pod_name":              podName,
			"update_time":                now,
		}
	} else if streamType == entity.AigcStreamType {
		updates = bson.M{
			"aigc_stream_heartbeat_time": now.Unix(),
			"aigc_pod_name":              podName,
			"update_time":                now,
		}
	} else {
		return fmt.Errorf("unsupported stream type: %d", streamType)
	}

	result, err := m.streamInfoPool.UpdateOne(ctx, bson.M{"_id": streamId}, bson.M{"$set": updates})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateHeartbeatTime UpdateOne streamId(%s) streamType(%d) err: %v", streamId, streamType, err)
		return err
	}

	if result.MatchedCount == 0 {
		log.WarnWithCtx(ctx, "UpdateHeartbeatTime no document matched streamId(%s)", streamId)
		return nil
	}

	log.InfoWithCtx(ctx, "UpdateHeartbeatTime success streamId(%s) streamType(%d) matchedCount(%d) modifiedCount(%d)",
		streamId, streamType, result.MatchedCount, result.ModifiedCount)
	return nil
}

/*
	is_off_mic == false 且
	error后，退避重连失败的情况
	1,zego_stream_status = unhealth 或 aigc_stream_status = unhealth
	或
	心跳停了
    2,当前时间 - zego_heartbeat_time > threshold 或 当前时间 - aigc_heartbeat_time > threshold
	或
	建流长时间卡住
	3,zego_stream_status = pending 且 当前时间 - update_time > threshold 或 aigc_stream_status = pending 且 当前时间 - update_time > threshold
	以上情况都需要兜底重新建流
*/
func (m *MongoDao) GetNeedFallBackStreamItems(ctx context.Context, lastCreateTime time.Time, lastStreamId string, limit int64) ([]*entity.StreamItem, error) {
	now := time.Now()
	heartbeatThreshold := time.Duration(tt_config.GetAigcZegoStreamConfig().GetFallbackHeartbeat()) * time.Second
	pendingThreshold := time.Duration(tt_config.GetAigcZegoStreamConfig().GetFallbackPending()) * time.Second
	heartbeatTimeThreshold := now.Add(-heartbeatThreshold).Unix()
	pendingTimeThreshold := now.Add(-pendingThreshold) // 注意：这里应该是time.Time类型，不是Unix时间戳

	// 构建基础过滤条件：is_off_mic == false
	baseFilter := bson.M{"is_off_mic": false, "zego_stream_status": bson.M{"$ne": entity.ZegoStreamStatusFallBack}}

	// 构建需要兜底的条件
	fallbackConditions := bson.A{
		// 条件1: zego_stream_status = unhealth 或 aigc_stream_status = unhealth
		bson.M{"zego_stream_status": entity.ZegoStreamStatusUnhealth},
		bson.M{"aigc_stream_status": entity.AigcStreamStatusUnHealth},

		// 条件2: 心跳超时
		// zego心跳超时：当前时间 - zego_heartbeat_time > threshold
		bson.M{
			"zego_stream_heartbeat_time": bson.M{
				"$gt": 0, // 确保心跳时间不为0
				"$lt": heartbeatTimeThreshold,
			},
		},
		// aigc心跳超时：当前时间 - aigc_heartbeat_time > threshold
		bson.M{
			"aigc_stream_heartbeat_time": bson.M{
				"$gt": 0, // 确保心跳时间不为0
				"$lt": heartbeatTimeThreshold,
			},
		},

		// 条件3: 建流长时间卡住
		// zego建流卡住：zego_stream_status = pending 且 当前时间 - update_time > threshold
		bson.M{
			"$and": bson.A{
				bson.M{"zego_stream_status": entity.ZegoStreamStatusPending},
				bson.M{"update_time": bson.M{"$lt": pendingTimeThreshold}},
			},
		},
		// aigc建流卡住：aigc_stream_status = pending 且 当前时间 - update_time > threshold
		bson.M{
			"$and": bson.A{
				bson.M{"aigc_stream_status": entity.AigcStreamStatusPending},
				bson.M{"update_time": bson.M{"$lt": pendingTimeThreshold}},
			},
		},
	}

	// 组合所有条件
	filter := bson.M{
		"$and": bson.A{
			baseFilter,
			bson.M{"$or": fallbackConditions},
		},
	}

	// 分页条件：优先以createTime升序排序，createTime相同以streamId升序排序
	if !lastCreateTime.IsZero() || lastStreamId != "" {
		paginationFilter := bson.M{
			"$or": bson.A{
				bson.M{"create_time": bson.M{"$gt": lastCreateTime}},
				bson.M{
					"$and": bson.A{
						bson.M{"create_time": lastCreateTime},
						bson.M{"_id": bson.M{"$gt": lastStreamId}},
					},
				},
			},
		}
		filter["$and"] = append(filter["$and"].(bson.A), paginationFilter)
	}

	// 设置查询选项：排序和限制
	opts := options.Find().
		SetSort(bson.D{
			{Key: "create_time", Value: 1}, // createTime升序
			{Key: "_id", Value: 1},         // streamId升序
		}).
		SetLimit(limit)

	cursor, err := m.streamInfoPool.Find(ctx, filter, opts)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNeedFallBackStreamItems Find err:%v", err)
		return nil, err
	}
	defer cursor.Close(ctx)

	var items []*entity.StreamItem
	err = cursor.All(ctx, &items)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNeedFallBackStreamItems cursor.All err:%v", err)
		return nil, err
	}
	ids := make([]string, 0, len(items))
	for _, item := range items {
		ids = append(ids, item.StreamId)
	}
	log.InfoWithCtx(ctx, "GetNeedFallBackStreamItems found %d streamIds:%v lastCreateTime:%v, lastStreamId:%s",
		len(items), ids, lastCreateTime, lastStreamId)
	return items, nil
}

// 删除流连接记录
func (m *MongoDao) DeleteStreamByStreamIds(ctx context.Context, streamIds []string) error {
	res, err := m.streamInfoPool.DeleteMany(ctx, bson.M{"_id": bson.M{"$in": streamIds}})
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteStreamByStreamIds DeleteMany streamId(%v) err: %v", streamIds, err)
		return err
	}
	if res.DeletedCount == 0 {
		log.WarnWithCtx(ctx, "DeleteStreamByStreamIds no document matched streamId(%v)", streamIds)
		return nil
	}
	log.InfoWithCtx(ctx, "DeleteStreamByStreamIds success streamIds(%v) deletedCount(%d)", streamIds, res.DeletedCount)
	return nil
}

// 重连时，建流成功后需要更新状态以及心跳时间
func (m *MongoDao) CreateStreamSuccess(ctx context.Context, streamId string, streamType uint32) error {
	var updates bson.M
	now := time.Now()
	if streamType == entity.ZegoStreamType {
		updates = bson.M{
			"zego_stream_status":         entity.ZegoStreamStatusHealth,
			"zego_stream_heartbeat_time": now.Unix(),
			"update_time":                now,
		}
	} else if streamType == entity.AigcStreamType {
		updates = bson.M{
			"aigc_stream_status":         entity.AigcStreamStatusHealth,
			"aigc_stream_heartbeat_time": now.Unix(),
			"update_time":                now,
		}
	} else {
		return fmt.Errorf("unsupported stream type: %d", streamType)
	}
	_, err := m.streamInfoPool.UpdateOne(ctx, bson.M{"_id": streamId}, bson.M{"$set": updates})
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateStreamSuccess UpdateOne streamId(%s) streamType(%d) err: %v",
			streamId, streamType, err)
		return nil
	}
	log.InfoWithCtx(ctx, "CreateStreamSuccess  streamId(%s) updates:%v success", streamId, updates)

	return nil
}

func (m *MongoDao) GetAbnormalOffMicStream(ctx context.Context, threshold int64) ([]*entity.StreamItem, error) {
	cutoffTime := time.Now().Add(-time.Duration(threshold) * time.Second).Unix()

	filter := bson.M{
		"is_off_mic":        true,
		"last_off_mic_time": bson.M{"$lt": cutoffTime},
	}

	cursor, err := m.streamInfoPool.Find(ctx, filter)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAbnormalOffMicStream Find filter:%v err: %v", filter, err)
		return nil, err
	}
	defer cursor.Close(ctx)
	var items []*entity.StreamItem
	err = cursor.All(ctx, &items)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAbnormalOffMicStream cursor.All err:%v", err)
		return nil, err
	}
	log.InfoWithCtx(ctx, "GetAbnormalOffMicStream success threshold(%d) num(%d)", threshold, len(items))
	return items, nil
}
