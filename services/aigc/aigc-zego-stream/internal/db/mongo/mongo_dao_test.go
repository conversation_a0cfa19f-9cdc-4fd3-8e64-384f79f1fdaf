package mongo

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/entity"
	"reflect"
	"testing"
	"time"
)

func TestMongoDao_GetNeedFallBackStreamItems(t *testing.T) {
	ctx := context.Background()
	mongoDao, err := NewMongoDao(ctx, &config.MongoConfig{
		Addrs:           "10.34.6.29:27017",
		ReplicaSet:      "",
		Database:        "aigc_zego",
		MaxPoolSize:     0,
		MinPoolSize:     0,
		MaxConnIdleTime: 0,
		UserName:        "aigc_zego_rw",
		Password:        "By*hqYyT63X5UxM",
		Options:         "",
		UserName2:       "",
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "mongo.NewMongoDao err: %v", err)
		return
	}
	type fields struct {
		mongoCli       *mongo.Client
		streamInfoPool *mongo.Collection
	}
	type args struct {
		ctx            context.Context
		lastCreateTime time.Time
		lastStreamId   string
		limit          int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*entity.StreamItem
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "1",
			fields: fields{
				mongoCli:       mongoDao.mongoCli,
				streamInfoPool: mongoDao.streamInfoPool,
			},
			args:    args{
				ctx:            ctx,
				lastCreateTime: time.Time{},
				lastStreamId:   "",
				limit:          10,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				mongoCli:       tt.fields.mongoCli,
				streamInfoPool: tt.fields.streamInfoPool,
			}
			got, err := m.GetNeedFallBackStreamItems(tt.args.ctx, tt.args.lastCreateTime, tt.args.lastStreamId, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNeedFallBackStreamItems() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetNeedFallBackStreamItems() got = %v, want %v", got, tt.want)
			}
		})
	}
}
