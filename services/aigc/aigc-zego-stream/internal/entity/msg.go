package entity

type StreamState int

const (
	StreamConnected    StreamState = 1 // 连接成功
)

type ZegoResponse struct {
	ErrorCode    int        `json:"error_code"`
	ExtendedData *ExtraData `json:"extended_data"`
	PodName      string     `json:"pod_name"`
}

type ExtraData struct {
	RoomSessionId int64 `json:"room_session_id"`
}

type ZegoHeartbeatAckJson struct {
	Message string `json:"message"`
	PodName string `json:"pod_name"`
}

type ZegoLoginJson struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type RoomStateUpdateJson struct {
	ErrorCode    int         `json:"error_code"`
	ExtendedData string      `json:"extended_data"`
	State        StreamState `json:"state"`
	Timestamp    int64       `json:"timestamp"`
	RoomId       string      `json:"room_id"`
	Msg          string      `json:"msg"`
}

type SendAudioMsg struct {
	Code       int    `json:"error_code"`
	StreamName string `json:"stream_id"`
}
