package entity

type StreamState int

const (
	StreamDisconnected StreamState = 0 // 连接断开
	StreamConnecting   StreamState = 1 // 连接中
	StreamConnected    StreamState = 2 // 连接成功
)

type ZegoResponse struct {
	ErrorCode    int        `json:"error_code"`
	ExtendedData *ExtraData `json:"extended_data"`
	PodName      string     `json:"pod_name"`
}

type ExtraData struct {
	RoomSessionId int64 `json:"room_session_id"`
}

type ZegoHeartbeatAckJson struct {
	Message string `json:"message"`
	PodName string `json:"pod_name"`
}

type ZegoLoginJson struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type RoomStateUpdateJson struct {
	ErrorCode    int         `json:"error_code"`
	ExtendedData *ExtraData  `json:"extended_data"`
	State        StreamState `json:"state"`
	StateName    string      `json:"state_name"`
	Timestamp    int64       `json:"timestamp"`
}
