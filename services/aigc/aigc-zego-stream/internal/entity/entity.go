package entity

import "time"

const (
	ZegoStreamStatusHealth   = 1 // zego流状态正常
	ZegoStreamStatusUnhealth = 2 // zego流状态异常
	ZegoStreamStatusFallBack = 3 // zego流状态兜底中
	ZegoStreamStatusPending  = 4 // zego流状态建流中
	AigcStreamStatusHealth   = 1 // aigc流状态正常
	AigcStreamStatusUnHealth = 2 // aigc流状态异常
	AigcStreamStatusFallBack = 3 // aigc流状态兜底中
	AigcStreamStatusPending  = 4 // aigc流状态建流中

	AigcStreamType = 0
	ZegoStreamType = 1
)

type StreamItem struct {
	//Id                 string    `bson:"_id"`
	StreamId                   string    `bson:"_id,omitempty"`
	Uid                        uint32    `bson:"uid,omitempty"`
	ChannelId                  uint32    `bson:"channel_id,omitempty"`
	PodName                    string    `bson:"pod_name,omitempty"`
	ZegoPodName                string    `bson:"zego_pod_name,omitempty"`
	ZegoStreamStatus           uint32    `bson:"zego_stream_status,omitempty"`
	ZegoStreamStatusUpdateTime int64     `bson:"zego_stream_status_update_time,omitempty"`
	ZegoStreamHeartbeatTime    int64     `bson:"zego_stream_heartbeat_time,omitempty"`
	AigcPodName                string    `bson:"aigc_pod_name,omitempty"`
	AigcStreamStatus           uint32    `bson:"aigc_stream_status,omitempty"`
	AigcStreamHeartbeatTime    int64     `bson:"aigc_stream_heartbeat_time,omitempty"`
	AigcStreamStatusUpdateTime int64     `bson:"aigc_stream_status_update_time,omitempty"`
	IsOffMic                   bool      `bson:"is_off_mic,omitempty"`        // 是否退出房间
	LastOffMicTime             int64     `bson:"last_off_mic_time,omitempty"` // 最近一次退出房间时间
	CreateTime                 time.Time `bson:"create_time,omitempty"`
	UpdateTime                 time.Time `bson:"update_time,omitempty"`
}
