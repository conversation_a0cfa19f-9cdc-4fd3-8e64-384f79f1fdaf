package internal

import (
	"context"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic"
	middleware_event "gitlab.ttyuyin.com/tt-infra/middleware/event"
	"gitlab.ttyuyin.com/tyr/x/log"
	account_go "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/config"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	pb "golang.52tt.com/protocol/services/aigc/aigc-zego-stream"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/db/mongo"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/event"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/event/eventlink/pub"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/event/eventlink/sub"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-zego-stream/internal/timer"
	tt_config "golang.52tt.com/services/aigc/aigc-zego-stream/internal/ttconfig"
)

type StartConfig struct {
	// from config file
	ZegoAppId string              `json:"zego_app_id"`
	MongoConf *config.MongoConfig `json:"mongo"`

	EventLink *middleware_event.Options `json:"event_link"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	err := tt_config.InitAigcZegoStreamConfig()
	if err != nil {
		log.Errorf("InitAigcZegoStreamConfig err: %v", err)
		return nil, err
	}

	accountCli, err := account_go.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "account_go.NewClient err: %v", err)
		return nil, err
	}

	aigcAccount, err := aigc_account.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "aigc_account.NewClient err: %v", err)
		return nil, err
	}

	mongoDao, err := mongo.NewMongoDao(ctx, cfg.MongoConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "mongo.NewMongoDao err: %v", err)
		return nil, err
	}
	streamRecord := mgr.NewStreamRecord(mongoDao)

	channelMicClient, err := channel_mic.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "channel_mic.NewClient err: %v", err)
		return nil, err
	}
	streamMgr, err := mgr.NewStreamManager(ctx, streamRecord, channelMicClient)
	if err != nil {
		log.Errorf("mgr.NewStreamManager err: %v", err)
		return nil, err
	}

	enterRoomMgr := mgr.NewEnterRoom(streamMgr, accountCli, aigcAccount, streamRecord)
	go enterRoomMgr.LoopDetectExitRoom()
	eventBus, err := pub.NewEventBus(ctx, cfg.EventLink)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer NewEventBus cfg(%+v) err: %v", cfg, err)
		return nil, err
	}
	kafkaSub, err := sub.NewSimpleMicEventSubscriber(ctx, eventBus, enterRoomMgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewSimpleMicEventSubscriber err %s ", err)
		return nil, err
	}

	streamReconnectSub, err := sub.NewStreamReconnectEventSubscriber(ctx, eventBus, enterRoomMgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewStreamReconnectEventSubscriber err %s ", err)
		return nil, err
	}

	timerInst := timer.NewRedisTimer(streamMgr, enterRoomMgr, streamRecord, eventBus)
	err = timerInst.Start()
	if err != nil {
		log.ErrorWithCtx(ctx, "timer.NewRedisTimer err %s ", err.Error())
		return nil, err
	}
	//timerInst.TestLogin(ctx)
	//timerInst.TestTimer()
	//timerInst.TestTimer2()

	s := &Server{
		streamMgr:          streamMgr,
		roomSub:            kafkaSub,
		streamReconnectSub: streamReconnectSub,
	}

	return s, nil
}

type Server struct {
	streamMgr          *mgr.StreamManager
	roomSub            *sub.SimpleMicEventSubscriber
	streamReconnectSub *sub.StreamReconnectEventSubscriber
	eventBus           event.EventBus
}

func (s *Server) Login(ctx context.Context, req *pb.LoginReq) (*pb.LoginResp, error) {
	resp := &pb.LoginResp{}
	log.InfoWithCtx(ctx, "Login LoginReq: %s, loginResp:%s", req.String(), resp.String())
	return resp, nil
}

func (s *Server) ShutDown() {
	s.streamMgr.Close()
	if s.eventBus != nil {
		s.eventBus.Close()
	}
}
