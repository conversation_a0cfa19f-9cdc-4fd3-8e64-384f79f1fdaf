package eventlink

import (
	"context"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/aigc/aigc-common"
	"strconv"
)

func (eb *eventBus) PublishAttitudeEvent(ctx context.Context, event *pb.AigcAttitudeEvent) error {

	if err := eb.publish(ctx, "aigc_attitude_event", strconv.Itoa(int(event.GetUserId())), event); err != nil {
		return err
	}
	log.InfoWithCtx(ctx, "PublishAttitudeEvent event(%+v) finished", event)
	return nil
}