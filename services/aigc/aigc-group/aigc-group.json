{"server.grpcListen": ":8080", "server.adminListen": ":8078", "mongo": {"addrs": "**********:27017", "database": "aigc_group", "user_name": "aigc_group_rw", "password": "v5cGjQ73K35H*c5", "max_pool_size": 5}, "redis": {"host": "************", "port": 6379, "protocol": "tcp", "ping_interval": 300, "database": 15}, "event_link": {"publisher": {"group": {"kafka": {"clientID": "aigc-group", "brokers": ["hobby-channel-kafka-broker-01.database.svc.cluster.local:9092"]}, "topics": ["aigc_group_event", "group_template_change_event"]}}}, "subscriber": {"group_template_changed": {"kafka": {"brokers": ["hobby-channel-kafka-broker-01.database.svc.cluster.local:9092"], "clientID": "aigc-group"}, "groupID": "aigc-group", "topics": ["group_template_change_event"], "maxRetryTimes": 5, "processWorkerNum": 5}}}