package redis

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/gookit/goutil/strutil"
	"github.com/gookit/goutil/timex"
	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"

	account_go "golang.52tt.com/protocol/services/account-go"
	"golang.52tt.com/services/aigc/aigc-group/internal/entity"
	"golang.52tt.com/services/aigc/aigc-group/internal/infra/db"
)

const (
	// 群内超过2天没人发言就会被删除
	matchGroupTTL = 2*timex.Day + 10*time.Minute
)

type matchGroupsCache struct {
	cmder *db.RedisDB
}

func NewMatchGroupsCache(redisDB *db.RedisDB) *matchGroupsCache {
	return &matchGroupsCache{
		cmder: redisDB,
	}
}

func (c *matchGroupsCache) Add(ctx context.Context, group *entity.MatchGroup, expiredAt time.Time) error {
	if _, err := c.cmder.TxPipelined(ctx, func(pl redis.Pipeliner) error {
		// 群信息
		_ = pl.HSet(ctx, c.keyOfMatchGroup(group.Id), matchGroupMap(group))
		_ = pl.Expire(ctx, c.keyOfMatchGroup(group.Id), matchGroupTTL)

		// 群匹配池
		_ = pl.ZAdd(ctx, c.keyOfMatchGroupPool(group.TemplId), &redis.Z{
			Score:  float64(expiredAt.Unix()),
			Member: strconv.Itoa(int(group.Id)),
		})

		return nil
	}); err != nil {
		return err
	}

	return nil
}

func (c *matchGroupsCache) UpdateExpireTime(ctx context.Context, templId, groupId uint32, expiredAt time.Time) error {
	if _, err := c.cmder.TxPipelined(ctx, func(pl redis.Pipeliner) error {
		// 续期
		_ = pl.Expire(ctx, c.keyOfMatchGroup(groupId), matchGroupTTL)
		_ = pl.ZAdd(ctx, c.keyOfMatchGroupPool(templId), &redis.Z{
			Score:  float64(expiredAt.Unix()),
			Member: strconv.Itoa(int(groupId)),
		})

		return nil
	}); err != nil {
		return err
	}

	return nil
}

func (c *matchGroupsCache) Delete(ctx context.Context, templId, groupId uint32) error {
	if _, err := c.cmder.TxPipelined(ctx, func(pl redis.Pipeliner) error {
		_ = pl.ZRem(ctx, c.keyOfMatchGroupPool(templId), strconv.Itoa(int(groupId)))
		_ = pl.Del(ctx, c.keyOfMatchGroup(groupId))
		return nil
	}); err != nil {
		return err
	}

	return nil
}

func (c *matchGroupsCache) Remove(ctx context.Context, templId, groupId uint32) error {
	_, err := c.cmder.ZRem(ctx, c.keyOfMatchGroupPool(templId), strconv.Itoa(int(groupId))).Result()
	return err
}

func (c *matchGroupsCache) DecrSex(ctx context.Context, groupId uint32, sex account_go.USER_SEX) (*entity.MatchGroup, bool, error) {
	var field string
	switch sex {
	case account_go.USER_SEX_USER_SEX_MALE:
		field = "male"
	case account_go.USER_SEX_USER_SEX_FEMALE:
		field = "female"
	default:
		field = "any"
	}

	var (
		decrCmd  *redis.Cmd
		groupCmd *redis.StringStringMapCmd
	)
	if _, err := c.cmder.TxPipelined(ctx, func(pl redis.Pipeliner) error {
		decrCmd = pl.Eval(
			ctx,
			`
				local key = KEYS[1]
				local field = KEYS[2]

				local n = redis.call('HGET', key, field)
				if tonumber(n) > 0 then
					redis.call('HINCRBY', key, field, -1)
					return 1
				end

				local n = redis.call('HGET', key, 'any')
				if tonumber(n) > 0 then
					redis.call('HINCRBY', key, 'any', -1)
					return 1
				end

				return 0
			`,
			[]string{
				c.keyOfMatchGroup(groupId),
				field,
			},
		)

		groupCmd = pl.HGetAll(ctx, c.keyOfMatchGroup(groupId))

		return nil
	}); err != nil {
		return nil, false, err
	}

	decrResult, err := decrCmd.Int64()
	if err != nil {
		return nil, false, err
	}

	values, err := groupCmd.Result()
	if err != nil {
		return nil, false, err
	}
	if len(values) == 0 {
		return nil, false, nil
	}

	return matchGroupEntity(groupId, values), decrResult > 0, nil
}

func (c *matchGroupsCache) IncrSex(ctx context.Context, groupId uint32, sex account_go.USER_SEX) (*entity.MatchGroup, error) {
	var field string
	switch sex {
	case account_go.USER_SEX_USER_SEX_MALE:
		field = "male"
	case account_go.USER_SEX_USER_SEX_FEMALE:
		field = "female"
	default:
		field = "any"
	}

	var (
		groupCmd *redis.StringStringMapCmd
	)
	if _, err := c.cmder.TxPipelined(ctx, func(pl redis.Pipeliner) error {
		_ = pl.Eval(
			ctx,
			`
				local key = KEYS[1]
				local field = KEYS[2]

				local num = redis.call('HGET', key, field)
				local lim = redis.call('HGET', key, field .. '_lim')
				if tonumber(num) >= tonumber(lim) then
					field = 'any'
				end

				redis.call('HINCRBY', key, field, 1)
				return 1
			`,
			[]string{
				c.keyOfMatchGroup(groupId),
				field,
			},
		)

		groupCmd = pl.HGetAll(ctx, c.keyOfMatchGroup(groupId))
		return nil
	}); err != nil {
		return nil, err
	}

	values, err := groupCmd.Result()
	if err != nil {
		return nil, err
	}
	if len(values) == 0 {
		return nil, nil
	}

	return matchGroupEntity(groupId, values), nil
}

func (c *matchGroupsCache) Get(ctx context.Context, groupId uint32) (*entity.MatchGroup, error) {
	value, err := c.cmder.HGetAll(ctx, c.keyOfMatchGroup(groupId)).Result()
	if err != nil {
		return nil, err
	}
	if len(value) == 0 {
		return nil, nil
	}

	return matchGroupEntity(groupId, value), nil
}

func (c *matchGroupsCache) ScanByTemplId(ctx context.Context, templId uint32, begin, end, offset, limit int64) (entity.MatchGroupList, error) {
	// 移除已过期的，以免池子过大
	if _, err := c.cmder.ZRemRangeByScore(ctx, c.keyOfMatchGroupPool(templId), "-inf", strconv.Itoa(int(time.Now().Unix()-1))).Result(); err != nil {
		return nil, err
	}

	members, err := c.cmder.ZRevRangeByScore(ctx, c.keyOfMatchGroupPool(templId), &redis.ZRangeBy{
		Min:    strconv.Itoa(int(begin)),
		Max:    strconv.Itoa(int(end)),
		Offset: offset,
		Count:  limit,
	}).Result()
	if err != nil {
		return nil, err
	}
	if len(members) == 0 {
		return nil, nil
	}

	var (
		groupIds    []uint32
		groupCmdMap = make(map[uint32]*redis.StringStringMapCmd)
	)
	if _, err := c.cmder.Pipelined(ctx, func(pl redis.Pipeliner) error {
		for _, member := range members {
			id, err := strconv.ParseUint(member, 10, 32)
			if err != nil {
				return err
			}

			groupId := uint32(id)
			groupIds = append(groupIds, groupId)
			groupCmdMap[groupId] = pl.HGetAll(ctx, c.keyOfMatchGroup(groupId))
		}

		return nil
	}); err != nil {
		return nil, err
	}

	list := make(entity.MatchGroupList, 0, len(members))
	for _, id := range groupIds {
		value, err := groupCmdMap[id].Result()
		if err != nil {
			return nil, errors.WithMessagef(err, "Scan cmd.Result id(%d) failed", id)
		}

		list = append(list, matchGroupEntity(id, value))
	}

	return list, nil
}

func (c *matchGroupsCache) keyOfMatchGroup(groupId uint32) string {
	return fmt.Sprintf("match:group:%d", groupId)
}

func (c *matchGroupsCache) keyOfMatchGroupPool(templID uint32) string {
	return fmt.Sprintf("match:group:pool:%d", templID)
}

func matchGroupMap(group *entity.MatchGroup) map[string]any {
	m := map[string]any{
		"templ": group.TemplId,

		"any":    group.Any,
		"male":   group.Male,
		"female": group.Female,

		"any_lim":    group.AnyLimit,
		"male_lim":   group.MaleLimit,
		"female_lim": group.FemaleLimit,
	}

	return m
}

func matchGroupEntity(id uint32, value map[string]string) *entity.MatchGroup {
	mg := &entity.MatchGroup{
		Id:      id,
		TemplId: uint32(strutil.SafeUint(value["templ"])),

		Any:    int32(strutil.SafeInt(value["any"])),
		Male:   int32(strutil.SafeInt(value["male"])),
		Female: int32(strutil.SafeInt(value["female"])),

		AnyLimit:    uint32(strutil.SafeUint(value["any_lim"])),
		MaleLimit:   uint32(strutil.SafeUint(value["male_lim"])),
		FemaleLimit: uint32(strutil.SafeUint(value["female_lim"])),
	}

	return mg
}
