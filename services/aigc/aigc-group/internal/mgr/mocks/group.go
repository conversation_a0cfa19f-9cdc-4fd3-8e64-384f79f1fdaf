// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/aigc/aigc-group/internal/mgr (interfaces: GroupManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	entity "golang.52tt.com/services/aigc/aigc-group/internal/entity"
)

// MockGroupManager is a mock of GroupManager interface.
type MockGroupManager struct {
	ctrl     *gomock.Controller
	recorder *MockGroupManagerMockRecorder
}

// MockGroupManagerMockRecorder is the mock recorder for MockGroupManager.
type MockGroupManagerMockRecorder struct {
	mock *MockGroupManager
}

// NewMockGroupManager creates a new mock instance.
func NewMockGroupManager(ctrl *gomock.Controller) *MockGroupManager {
	mock := &MockGroupManager{ctrl: ctrl}
	mock.recorder = &MockGroupManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGroupManager) EXPECT() *MockGroupManagerMockRecorder {
	return m.recorder
}

// BatchGetActiveGroup mocks base method.
func (m *MockGroupManager) BatchGetActiveGroup(arg0 context.Context, arg1 []uint32) (map[uint32]*entity.ActiveGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetActiveGroup", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*entity.ActiveGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetActiveGroup indicates an expected call of BatchGetActiveGroup.
func (mr *MockGroupManagerMockRecorder) BatchGetActiveGroup(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetActiveGroup", reflect.TypeOf((*MockGroupManager)(nil).BatchGetActiveGroup), arg0, arg1)
}

// BatchGetActiveGroupMembers mocks base method.
func (m *MockGroupManager) BatchGetActiveGroupMembers(arg0 context.Context, arg1 []uint32) (map[uint32]entity.ActiveGroupMemberList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetActiveGroupMembers", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]entity.ActiveGroupMemberList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetActiveGroupMembers indicates an expected call of BatchGetActiveGroupMembers.
func (mr *MockGroupManagerMockRecorder) BatchGetActiveGroupMembers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetActiveGroupMembers", reflect.TypeOf((*MockGroupManager)(nil).BatchGetActiveGroupMembers), arg0, arg1)
}

// BatchGetGroupMember mocks base method.
func (m *MockGroupManager) BatchGetGroupMember(arg0 context.Context, arg1 uint32, arg2 []uint32) (entity.GroupMemberList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetGroupMember", arg0, arg1, arg2)
	ret0, _ := ret[0].(entity.GroupMemberList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGroupMember indicates an expected call of BatchGetGroupMember.
func (mr *MockGroupManagerMockRecorder) BatchGetGroupMember(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGroupMember", reflect.TypeOf((*MockGroupManager)(nil).BatchGetGroupMember), arg0, arg1, arg2)
}

// CleanInactiveGroup mocks base method.
func (m *MockGroupManager) CleanInactiveGroup(arg0 context.Context, arg1 time.Time, arg2 uint32) (entity.GroupList, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CleanInactiveGroup", arg0, arg1, arg2)
	ret0, _ := ret[0].(entity.GroupList)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CleanInactiveGroup indicates an expected call of CleanInactiveGroup.
func (mr *MockGroupManagerMockRecorder) CleanInactiveGroup(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CleanInactiveGroup", reflect.TypeOf((*MockGroupManager)(nil).CleanInactiveGroup), arg0, arg1, arg2)
}

// CreateGroup mocks base method.
func (m *MockGroupManager) CreateGroup(arg0 context.Context, arg1 *entity.Group, arg2 ...uint32) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateGroup", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateGroup indicates an expected call of CreateGroup.
func (mr *MockGroupManagerMockRecorder) CreateGroup(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGroup", reflect.TypeOf((*MockGroupManager)(nil).CreateGroup), varargs...)
}

// DeleteGroup mocks base method.
func (m *MockGroupManager) DeleteGroup(arg0 context.Context, arg1 *entity.Group) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteGroup", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteGroup indicates an expected call of DeleteGroup.
func (mr *MockGroupManagerMockRecorder) DeleteGroup(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteGroup", reflect.TypeOf((*MockGroupManager)(nil).DeleteGroup), arg0, arg1)
}

// GetActiveGroup mocks base method.
func (m *MockGroupManager) GetActiveGroup(arg0 context.Context, arg1 uint32) (*entity.ActiveGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveGroup", arg0, arg1)
	ret0, _ := ret[0].(*entity.ActiveGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveGroup indicates an expected call of GetActiveGroup.
func (mr *MockGroupManagerMockRecorder) GetActiveGroup(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveGroup", reflect.TypeOf((*MockGroupManager)(nil).GetActiveGroup), arg0, arg1)
}

// GetActiveGroupList mocks base method.
func (m *MockGroupManager) GetActiveGroupList(arg0 context.Context, arg1, arg2 int64, arg3, arg4 uint32) (entity.ActiveGroupList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveGroupList", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(entity.ActiveGroupList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveGroupList indicates an expected call of GetActiveGroupList.
func (mr *MockGroupManagerMockRecorder) GetActiveGroupList(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveGroupList", reflect.TypeOf((*MockGroupManager)(nil).GetActiveGroupList), arg0, arg1, arg2, arg3, arg4)
}

// GetActiveGroupMembers mocks base method.
func (m *MockGroupManager) GetActiveGroupMembers(arg0 context.Context, arg1 uint32) (entity.ActiveGroupMemberList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveGroupMembers", arg0, arg1)
	ret0, _ := ret[0].(entity.ActiveGroupMemberList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveGroupMembers indicates an expected call of GetActiveGroupMembers.
func (mr *MockGroupManagerMockRecorder) GetActiveGroupMembers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveGroupMembers", reflect.TypeOf((*MockGroupManager)(nil).GetActiveGroupMembers), arg0, arg1)
}

// GetGroup mocks base method.
func (m *MockGroupManager) GetGroup(arg0 context.Context, arg1 uint32) (*entity.Group, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroup", arg0, arg1)
	ret0, _ := ret[0].(*entity.Group)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroup indicates an expected call of GetGroup.
func (mr *MockGroupManagerMockRecorder) GetGroup(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroup", reflect.TypeOf((*MockGroupManager)(nil).GetGroup), arg0, arg1)
}

// GetGroupMember mocks base method.
func (m *MockGroupManager) GetGroupMember(arg0 context.Context, arg1, arg2 uint32) (*entity.GroupMember, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupMember", arg0, arg1, arg2)
	ret0, _ := ret[0].(*entity.GroupMember)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupMember indicates an expected call of GetGroupMember.
func (mr *MockGroupManagerMockRecorder) GetGroupMember(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupMember", reflect.TypeOf((*MockGroupManager)(nil).GetGroupMember), arg0, arg1, arg2)
}

// GetGroupMemberList mocks base method.
func (m *MockGroupManager) GetGroupMemberList(arg0 context.Context, arg1 uint32) (entity.GroupMemberList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupMemberList", arg0, arg1)
	ret0, _ := ret[0].(entity.GroupMemberList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupMemberList indicates an expected call of GetGroupMemberList.
func (mr *MockGroupManagerMockRecorder) GetGroupMemberList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupMemberList", reflect.TypeOf((*MockGroupManager)(nil).GetGroupMemberList), arg0, arg1)
}

// GetTemplateGroupList mocks base method.
func (m *MockGroupManager) GetTemplateGroupList(arg0 context.Context, arg1, arg2, arg3 uint32) (entity.GroupList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTemplateGroupList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(entity.GroupList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTemplateGroupList indicates an expected call of GetTemplateGroupList.
func (mr *MockGroupManagerMockRecorder) GetTemplateGroupList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTemplateGroupList", reflect.TypeOf((*MockGroupManager)(nil).GetTemplateGroupList), arg0, arg1, arg2, arg3)
}

// GetUserJoinedGroupList mocks base method.
func (m *MockGroupManager) GetUserJoinedGroupList(arg0 context.Context, arg1 uint32) (entity.GroupList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserJoinedGroupList", arg0, arg1)
	ret0, _ := ret[0].(entity.GroupList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserJoinedGroupList indicates an expected call of GetUserJoinedGroupList.
func (mr *MockGroupManagerMockRecorder) GetUserJoinedGroupList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserJoinedGroupList", reflect.TypeOf((*MockGroupManager)(nil).GetUserJoinedGroupList), arg0, arg1)
}

// GetUserJoinedGroupNum mocks base method.
func (m *MockGroupManager) GetUserJoinedGroupNum(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserJoinedGroupNum", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserJoinedGroupNum indicates an expected call of GetUserJoinedGroupNum.
func (mr *MockGroupManagerMockRecorder) GetUserJoinedGroupNum(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserJoinedGroupNum", reflect.TypeOf((*MockGroupManager)(nil).GetUserJoinedGroupNum), arg0, arg1)
}

// GetUserOwnedGroupList mocks base method.
func (m *MockGroupManager) GetUserOwnedGroupList(arg0 context.Context, arg1, arg2 uint32) (entity.GroupList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserOwnedGroupList", arg0, arg1, arg2)
	ret0, _ := ret[0].(entity.GroupList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserOwnedGroupList indicates an expected call of GetUserOwnedGroupList.
func (mr *MockGroupManagerMockRecorder) GetUserOwnedGroupList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserOwnedGroupList", reflect.TypeOf((*MockGroupManager)(nil).GetUserOwnedGroupList), arg0, arg1, arg2)
}

// SendFollowGuide mocks base method.
func (m *MockGroupManager) SendFollowGuide(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendFollowGuide", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendFollowGuide indicates an expected call of SendFollowGuide.
func (mr *MockGroupManagerMockRecorder) SendFollowGuide(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendFollowGuide", reflect.TypeOf((*MockGroupManager)(nil).SendFollowGuide), arg0, arg1, arg2, arg3)
}

// UpdateActiveGroupMember mocks base method.
func (m *MockGroupManager) UpdateActiveGroupMember(arg0 context.Context, arg1 uint32, arg2 *entity.ActiveGroupMember) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateActiveGroupMember", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateActiveGroupMember indicates an expected call of UpdateActiveGroupMember.
func (mr *MockGroupManagerMockRecorder) UpdateActiveGroupMember(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateActiveGroupMember", reflect.TypeOf((*MockGroupManager)(nil).UpdateActiveGroupMember), arg0, arg1, arg2)
}
