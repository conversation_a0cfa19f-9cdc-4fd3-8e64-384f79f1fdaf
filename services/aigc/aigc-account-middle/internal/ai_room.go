package internal

import (
	"context"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_go"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	channelPB "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account-middle"
	channelSvrPB "golang.52tt.com/protocol/services/channelsvr"
)

func (s *Server) UpdateAiRoomCfg(ctx context.Context, request *pb.UpdateAiRoomCfgRequest) (*pb.UpdateAiRoomCfgResponse, error) {
	out := &pb.UpdateAiRoomCfgResponse{}
	if request.GetAiRoomCfg() == nil || request.GetAiRoomCfg().GetUid() == 0 || request.GetAiRoomCfg().GetBaseCfg().GetCid() == 0 {
		log.ErrorWithCtx(ctx, "UpdateAiRoomCfg req: invalid request")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	// 校验逻辑

	_, err := s.clients.AigcAccount.UpdateAiRoomCfg(ctx, &aigc_account.UpdateAiRoomCfgRequest{
		RoomCfg: request.GetAiRoomCfg(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAiRoomCfg req: %s, err: %v", request.String(), err)
		return out, err
	}
	return out, nil
}

func (s *Server) GetAiRoomCfg(ctx context.Context, request *pb.GetAiRoomCfgRequest) (*pb.GetAiRoomCfgResponse, error) {
	out := &pb.GetAiRoomCfgResponse{}
	if request.GetUid() == 0 {
		log.ErrorWithCtx(ctx, "GetAiRoomCfg req:%s invalid request", request.String())
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	resp, err := s.clients.AigcAccount.GetAiRoomCfg(ctx, &aigc_account.GetAiRoomCfgRequest{
		Uid: request.GetUid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAiRoomCfg req: %s, err: %v", request.String(), err)
		return out, err
	}
	if resp.GetRoomCfg() != nil {
		out.AiRoomCfg = resp.GetRoomCfg()
	} else {
		roleListResp, err := s.clients.ChannelGoClient.GetUserChannelRoleList(ctx, &channel_go.GetUserChannelRoleListReq{
			Uid:       request.GetUid(),
			AdminRole: uint32(channelSvrPB.ChannelAdminRole_CHANNEL_OWNER),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAiRoomCfg GetUserChannelRoleList req: %s, err: %v", request.String(), err)
			return out, err
		}
		var channelId uint32
		for _, role := range roleListResp.GetRoleList() {
			if role.GetRole() == uint32(channelSvrPB.ChannelAdminRole_CHANNEL_OWNER) &&
				role.GetChannelInfo().GetChannelBindType() == uint32(channelPB.ChannelType_USER_CHANNEL_TYPE) {
				channelId = role.GetChannelInfo().GetChannelBaseinfo().GetChannelId()
				break
			}
		}
		if channelId == 0 {
			log.ErrorWithCtx(ctx, "GetAiRoomCfg GetUserChannelRoleList req: %s, no owner channel", request.String())
			return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
		}
		out.AiRoomCfg = &aigc_account.AiRoomCfg{
			Uid: request.GetUid(),
			BaseCfg: &aigc_account.RoomBaseCfg{
				Cid: channelId,
			},
		}
	}
	log.DebugWithCtx(ctx, "GetAiRoomCfg req: %s, rsp: %s", request.String(), out.String())
	return out, err
}
