package internal

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	pb "golang.52tt.com/protocol/services/aigc/aigc-account-middle"
)

func (s *Server) UpdateAiRoomCfg(ctx context.Context, request *pb.UpdateAiRoomCfgRequest) (*pb.UpdateAiRoomCfgResponse, error) {
	out := &pb.UpdateAiRoomCfgResponse{}
	if request.GetAiRoomCfg() == nil || request.GetAiRoomCfg().GetUid() == 0 || request.GetAiRoomCfg().GetBaseCfg().GetCid() == 0 {
		log.ErrorWithCtx(ctx, "UpdateAiRoomCfg req: invalid request")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	// 校验逻辑

	_, err := s.clients.AigcAccount.UpdateAiRoomCfg(ctx, &aigc_account.UpdateAiRoomCfgRequest{
		RoomCfg: request.GetAiRoomCfg(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAiRoomCfg req: %s, err: %v", request.String(), err)
		return out, err
	}
	return out, nil
}

func (s *Server) GetAiRoomCfg(ctx context.Context, request *pb.GetAiRoomCfgRequest) (*pb.GetAiRoomCfgResponse, error) {
	out := &pb.GetAiRoomCfgResponse{}
	if request.GetUid() == 0 {
		log.ErrorWithCtx(ctx, "GetAiRoomCfg req:%s invalid request", request.String())
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	resp, err := s.clients.AigcAccount.GetAiRoomCfg(ctx, &aigc_account.GetAiRoomCfgRequest{
		Uid: request.GetUid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAiRoomCfg req: %s, err: %v", request.String(), err)
		return out, err
	}
	if resp.GetRoomCfg() != nil {
		out.AiRoomCfg = resp.GetRoomCfg()
	}else {

	}
return nil, err
}

func (s *Server) GetRunningAiRooms(ctx context.Context, request *pb.GetRunningAiRoomsRequest) (*pb.GetRunningAiRoomsResponse, error) {
	//TODO implement me
	panic("implement me")
}
