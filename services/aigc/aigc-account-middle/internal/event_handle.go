package internal

import (
	"context"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/device_id"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	account_go "golang.52tt.com/clients/account-go"
	cybros_arbiter "golang.52tt.com/clients/censoring-proxy"
	context_info "golang.52tt.com/pkg/context-info"
	"golang.52tt.com/pkg/iplocation"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/im"
	ugcPb "golang.52tt.com/protocol/app/ugc"
	accountGoPB "golang.52tt.com/protocol/services/account-go"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	censoring_proxyPB "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	im_api "golang.52tt.com/protocol/services/im-api"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka_im"
	"golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_chat"
	"golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_feature_router"
	"golang.52tt.com/protocol/services/rcmd/aigc_apps/rcmd_mt_proxy"
	timelineV2Pb "golang.52tt.com/protocol/services/timeline-v2"
	timelinePb "golang.52tt.com/protocol/services/timelinesvr"
	ugcContentPb "golang.52tt.com/protocol/services/ugc/content"
	ugc_event "golang.52tt.com/protocol/services/ugc/event"
	user_online "golang.52tt.com/protocol/services/user-online"
	config "golang.52tt.com/services/aigc/aigc-account-middle/internal/config/ttconfig/aigc_account_middle"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/event"
	"golang.52tt.com/services/aigc/aigc-account-middle/internal/report"
	"golang.org/x/sync/errgroup"
	"math/rand"
	"net"
	"strconv"
	"strings"
	"time"
)

const (
	// 新游推荐
	newGameRecommend = "338@public"
	// "TT语音助手
	ttAssistant = "ttyuyinzhushou"
	// TT语音客服
	keFu = "kefu"
	// 会长服务号
	huiZhang = "huizhangfuwuhao@public"
	// 达人服务号
	daRen = "yuyinzhubofuwuhao@sys"
	// 签约成员服务号
	qianYue = "qianyuechengyuanfuwuhao@sys"
	// 碎碎念手帐
	shuiShuiNian = "shouzhangbenzhushou@sys"
	// 内容助手
	neiRong = "guangchangzhushou"
	// 找搭子小助手
	daZi = "zhaodazixiaozhushou"
	// 社团助手
	sheTuan = "shetuanzhushou"
	// 电竞助手
	dianJing = "dianjingzhushou"
	// 开黑助手
	kaiHei = "kaiheizhushou"

	AbtestClientTypeUID = 0
	AbtestClientType    = 0
	AiCommentPostPrefix = "ai_comment_post_gen"
	GenAiCommentCmd     = "AIAccountCommentPost"
)

func (s *Server) onCommImEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {

	ctx = context_info.GenReqId(ctx)
	ev := &kafka_im.CommImEvent{}
	err := proto.Unmarshal(msg.Value, ev)
	if err != nil {
		log.ErrorWithCtx(ctx, "CommImEvent proto.Unmarshal failed, err:%v, msg:%+v", err, msg)
		return err, false
	}

	//log.InfoWithCtx(ctx, "onCommImEvent event: %+v", ev)

	// 处理发送消息事件
	if ev.GetEventType() != kafka_im.CommImEvent_EVENT_TYPE_1V1_SEND_IM {
		return nil, false
	}

	// 忽略非 timeline 消息
	if ev.GetDataType() != kafka_im.CommImEvent_DATA_TYPE_TIMELINE_MSG {
		return nil, false
	}

	// 反序列化 TimelineMsg
	timelineMsg := &timelineV2Pb.TimelineMsg{}
	err = proto.Unmarshal(ev.GetData(), timelineMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "onCommImEvent TimelineMsg proto.Unmarshal failed, err:%v, ev:%+v", err, ev)
		return err, false
	}

	// 反序列化 ImMsg
	imMsg := &timelinePb.ImMsg{}
	err = proto.Unmarshal(timelineMsg.GetMsgBin(), imMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "onCommImEvent ImMsg proto.Unmarshal failed, err:%v, timelineMsg:%+v", err, timelineMsg)
		return err, false
	}

	if s.filterMsg(ctx, imMsg) {
		return nil, false
	}

	// 处理用户和AI， 或AI和用户之间的互动记录，用于用户广场帖的互动
	s.handleUserAIInteractRecord(ctx, imMsg)

	isUserToAI, accountMap, _, err := s.isUserToAI(ctx, imMsg.GetFromId(), imMsg.GetToId())
	if err != nil {
		log.ErrorWithCtx(ctx, "onCommImEvent isUserToAI ev:%s err: %v", imMsg.String(), err)
		return err, false
	}
	if !isUserToAI {
		return nil, false
	}

	// 处理用户发给AI的消息
	return s.handleUserSendAIMsg(ctx, imMsg, accountMap)

}

func (s *Server) handleUserAIInteractRecord(ctx context.Context, imMsg *timelinePb.ImMsg) {
	log.InfoWithCtx(ctx, "handleUserAIInteractRecord start, imMsg:%s", imMsg.String())

	// 校验是否是用户和AI之间的消息
	aiAccountResp, err := s.clients.AigcAccount.BatchGetAIAccount(ctx, &aigc_account.BatchGetAIAccountRequest{
		UidList: []uint32{imMsg.GetFromId(), imMsg.GetToId()},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleUserAIInteractRecord BatchGetAIAccount failed, err:%v, imMsg:%s", err, imMsg.String())
		return
	}

	// 如果两个都是ai账号，或者都不是ai账号，不处理
	if len(aiAccountResp.GetAccountList()) == 2 || len(aiAccountResp.GetAccountList()) == 0 {
		log.WarnWithCtx(ctx, "handleUserAIInteractRecord account not match, aiAccountResp: %+v, fromUid:%v, toUid: %v", aiAccountResp.GetAccountList(), imMsg.GetFromId(), imMsg.GetToId())
		return
	}

	// 如果ai账号被禁用，不处理
	if aiAccountResp.GetAccountList()[0].GetIsUnregister() {
		log.WarnWithCtx(ctx, "handleUserAIInteractRecord aiAccount is unregister, imMsg:%s", imMsg.String())
		return
	}

	// 区分用户ID和ai账号ID
	var userId uint32
	if aiAccountResp.GetAccountList()[0].GetUid() != imMsg.GetFromId() {
		userId = imMsg.GetFromId()
	} else {
		userId = imMsg.GetToId()
	}

	// 校验用户ID是否命中ai账号实验，如果没命中不处理
	isInAbtest := s.isInAbtest(ctx, userId, aigc_feature_router.Source_SourceIM)
	if !isInAbtest {
		log.InfoWithCtx(ctx, "handleUserAIInteractRecord userId:%d is not in abtest, imMsg:%s", userId, imMsg.String())
		return
	}

	log.InfoWithCtx(ctx, "handleUserAIInteractRecord，aiAccount: $+v, imMsg:%s", aiAccountResp.GetAccountList()[0], imMsg.String())

	// 添加聊天记录
	_, err = s.clients.AigcAccount.AddUserAiChatRecord(ctx, &aigc_account.AddUserAiChatRecordRequest{
		FromUid: imMsg.GetFromId(),
		ToUid:   imMsg.GetToId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleUserAIInteractRecord AddUserAiChatRecord failed, fromUid: %v, toUid: %v, err:%v", imMsg.GetFromId(), imMsg.GetToId(), err)
		return
	}

	log.InfoWithCtx(ctx, "handleUserAIInteractRecord AddUserAiChatRecord success, fromUid: %v, toUid: %v, imMsg:%s", imMsg.GetFromId(), imMsg.GetToId(), imMsg.String())

	// 检查双方是否都有聊天记录
	chatRecord, err := s.clients.AigcAccount.GetUserAiChatRecord(ctx, &aigc_account.GetUserAiChatRecordRequest{
		FromUid: imMsg.GetToId(),
		ToUid:   imMsg.GetFromId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleUserAIInteractRecord GetUserAiChatRecord failed, fromUid: %v, toUid: %v, err:%v", imMsg.GetToId(), imMsg.GetFromId(), err)
		return
	}
	if chatRecord.GetRecordTime() == 0 {
		log.WarnWithCtx(ctx, "handleUserAIInteractRecord GetUserAiChatRecord, toUid: %v， fromUid: %v has not chat record", imMsg.GetToId(), imMsg.GetFromId())
		return
	}

	// 双方都有聊天记录，添加互动记录
	_, err = s.clients.AigcAccount.AddUserAiInteractionRecord(ctx, &aigc_account.AddUserAiInteractionRecordRequest{
		Uid:   userId,
		AiUid: aiAccountResp.GetAccountList()[0].GetUid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleUserAIInteractRecord AddUserAiInteractionRecord failed, uid: %v, aiUid: %v, err:%v", userId, aiAccountResp.GetAccountList()[0].GetUid(), err)
		return
	}

	log.InfoWithCtx(ctx, "handleUserAIInteractRecord AddUserAiInteractionRecord success, userId: %v, aiUid: %v, imMsg:%s", userId, aiAccountResp.GetAccountList()[0].GetUid(), imMsg.String())
}

func isSeeByMyself(msgTagList []uint32) bool {
	for _, tag := range msgTagList {
		if tag == uint32(timelinePb.MsgTag_MSG_TAG_IM_NOT_SEEN) {
			return true
		}
	}
	return false
}

func (s *Server) handleUserSendAIMsg(ctx context.Context, imMsg *timelinePb.ImMsg, accountMap map[uint32]*account_go.User) (error, bool) {
	uid := imMsg.GetFromId()
	aiUid := imMsg.GetToId()

	_, err := s.clients.AigcAccount.UpdateChatTime(ctx, &aigc_account.UpdateChatTimeRequest{
		Uid:   uid,
		AiUid: aiUid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "onCommImEvent UpdateChatTime failed, err:%v, imMsg:%s", err, imMsg.String())
	}

	if len(imMsg.GetMsgTagList()) > 0 && isSeeByMyself(imMsg.GetMsgTagList()) {
		s.reporter.ReportMsg(ctx, report.AIMsgReportData{
			Uid:              uid,
			Content:          imMsg.GetContent(),
			RespTime:         imMsg.GetServerMsgTime(),
			MessageId:        imMsg.GetServerMsgId(),
			Extra:            string(imMsg.GetExt()),
			AIAccount:        accountMap[imMsg.GetToId()],
			AigcBussType:     report.SysBussType,
			AigcMsgType:      report.SeeByMyselfMsgType,
			AigcReplyMsgType: report.SeeByMyselfReplyMsgType,
		})
		log.InfoWithCtx(ctx, "handleUserSendAIMsg see by myself, imMsg:%s", imMsg.String())
		return nil, false
	}

	isInAbtest := s.isInAbtest(ctx, uid, aigc_feature_router.Source_SourceIM)

	if !isInAbtest {
		log.InfoWithCtx(ctx, "onCommImEvent GetIfAIAccountEnabled not enabled uid:%d aiUid:%d isInAbtest:%v", uid,
			aiUid, isInAbtest)
		return nil, false
	}

	// 判断轮次，超过上限AI不回复
	roundRsp, err := s.clients.AigcAccount.GetChatRound(ctx, &aigc_account.GetChatRoundRequest{
		Uid:       uid,
		NeedTotal: true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "onCommImEvent GetChatRound failed, err:%v, imMsg:%s", err, imMsg.String())
		return err, false
	}
	if roundRsp.GetTotalRound() >= config.GetAigcAccountMiddleConfig().GetChatRoundThreshold() {
		// 句数到达上限后的用户消息上报
		s.reporter.ReportMsg(ctx, report.AIMsgReportData{
			Uid:              uid,
			Content:          imMsg.GetContent(),
			RespTime:         imMsg.GetServerMsgTime(),
			MessageId:        imMsg.GetServerMsgId(),
			Extra:            string(imMsg.GetExt()),
			AIAccount:        accountMap[aiUid],
			AigcBussType:     report.UserRelyBussType,
			AigcMsgType:      report.MsgAfterLimitMsgType,
			AigcReplyMsgType: report.MsgAfterLimitReplyMsgType,
		})
		log.InfoWithCtx(ctx, "onCommImEvent uid:%d aiUid:%d totalRound:%d >= threshold:%d, not reply", uid, aiUid,
			roundRsp.GetTotalRound(), config.GetAigcAccountMiddleConfig().GetChatRoundThreshold())
		return nil, false
	}
	isExactReachLimit := roundRsp.GetTotalRound()+1 == config.GetAigcAccountMiddleConfig().GetChatRoundThreshold()

	// 传递消息给中台
	_, err = s.clients.AigcChatClient.AIAccountRecvUserMsg(ctx, &aigc_chat.AIAccountRecvUserMsgReq{
		Uid:                 uid,
		AiAccountId:         aiUid,
		Content:             imMsg.GetContent(),
		MsgId:               imMsg.GetServerMsgId(),
		MsgType:             imMsg.GetType(),
		Extra:               imMsg.GetExt(),
		TargetMsgId:         imMsg.GetTargetMsgId(),
		IsExactlyReachLimit: isExactReachLimit,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "onCommImEvent AIAccountRecvUserMsg failed, err:%v, imMsg:%s", err, imMsg.String())
		return err, false
	}
	log.InfoWithCtx(ctx, "onCommImEvent handleUserSendAIMsg success, imMsg:%s isExactReachLimit:%v", imMsg.String(), isExactReachLimit)
	return nil, false
}

func (s *Server) filterMsg(ctx context.Context, imMsg *timelinePb.ImMsg) bool {
	if imMsg.GetFromId() == 0 || imMsg.GetToId() == 0 {
		log.WarnWithCtx(ctx, "onCommImEvent ImMsg has invalid from/to id, imMsg:%s", imMsg.String())
		return true

	}
	if imMsg.GetLabel() == uint32(im.MsgLabel_NEW_FOLLOWER) {
		return true
	}
	// 过滤官方账号的消息
	if imMsg.GetFromName() == newGameRecommend || imMsg.GetFromName() == ttAssistant || imMsg.GetFromName() == keFu ||
		imMsg.GetFromName() == huiZhang || imMsg.GetFromName() == daRen || imMsg.GetFromName() == qianYue ||
		imMsg.GetFromName() == shuiShuiNian || imMsg.GetFromName() == neiRong || imMsg.GetFromName() == daZi ||
		imMsg.GetFromName() == sheTuan || imMsg.GetFromName() == dianJing || imMsg.GetFromName() == kaiHei {
		return true
	}
	return false
}

func (s *Server) isInAbtest(ctx context.Context, uid uint32, source aigc_feature_router.Source) bool {
	// 查最近登录信息
	onlineRsp, err := s.clients.UserOnlineClient.GetLatestOnlineInfo(ctx, &user_online.GetLatestOnlineInfoReq{
		Uid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "isInAbtest GetLatestOnlineInfo failed, uid:%d err:%v", uid, err)
		return false
	}
	if onlineRsp.GetOnlineInfo() == nil {
		log.WarnWithCtx(ctx, "isInAbtest GetLatestOnlineInfo not found, uid:%d", uid)
		return false
	}

	deviceId, err := device_id.ToClientDeviceId(onlineRsp.GetOnlineInfo().GetDeviceIdHex(), onlineRsp.GetOnlineInfo().GetClientType())
	if err != nil {
		log.ErrorWithCtx(ctx, "isInAbtest ToClientDeviceId failed, uid:%d err:%v", uid, err)
		return false
	}

	// 判断中台实验，对照组AI不回复，使用s.clients.AigcRouterClient
	req := &aigc_feature_router.GetIfAIAccountEnabledReq{
		Uid:         uid,
		DeviceId:    deviceId,
		Source:      source,
		MarketId:    onlineRsp.GetOnlineInfo().GetMarketId(),
		IsNotSubmit: false,
	}
	featureRsp, err := s.clients.AigcRouterClient.GetIfAIAccountEnabled(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "isInAbtest GetIfAIAccountEnabled failed, abReq:%s err:%v", req.String(), err)
		return false
	}
	log.InfoWithCtx(ctx, "isInAbtest GetIfAIAccountEnabled abReq:%s featureRsp:%s", req.String(), featureRsp.String())
	return featureRsp.GetIsAiAccountEnabled()

}

func (s *Server) onUgcFollowingUpdate(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	ev := &ugc_event.FollowEvent{}
	if err := proto.Unmarshal(msg.Value, ev); err != nil {
		log.ErrorWithCtx(ctx, "onUgcFollowingUpdate Unmarshal err: %v", err)
		return err, false
	}

	log.InfoWithCtx(ctx, "onUgcFollowingUpdate event: %s", ev.String())

	if ev.GetIsDeleted() {
		// 取消关注不处理
		return nil, false
	}
	isUserToAI, accountMap, aiAccountRsp, err := s.isUserToAI(ctx, ev.GetFromUserId(), ev.GetToUserId())
	if err != nil {
		log.ErrorWithCtx(ctx, "onUgcFollowingUpdate isUserToAI ev:%s err: %v", ev.String(), err)
		return err, false
	}
	if !isUserToAI {
		return nil, false
	}
	uid := ev.GetFromUserId()
	aiUid := ev.GetToUserId()
	userAccount := accountMap[uid]
	aiAccount := accountMap[aiUid]

	if isInAbtest := s.isInAbtest(ctx, uid, aigc_feature_router.Source_SourceFollow); !isInAbtest {
		return nil, false
	}

	if len(aiAccountRsp.GetPrologue()) == 0 {
		return nil, false
	}

	lastTimeRsp, err := s.clients.AigcAccount.GetLastChatTime(ctx, &aigc_account.GetLastChatTimeRequest{
		Uid:   uid,
		AiUid: aiUid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "onUgcFollowingUpdate GetLastChatTime failed, err:%v, ev:%s", err, ev.String())
		return err, false
	}

	if time.Now().Unix()-lastTimeRsp.GetLastChatTime() <= config.GetAigcAccountMiddleConfig().GetSendPrologueThreshold() {
		log.InfoWithCtx(ctx, "onUgcFollowingUpdate last chat time too short, lastChatTime:%d, now:%d, threshold:%d, ev:%s",
			lastTimeRsp.GetLastChatTime(), time.Now().Unix(), config.GetAigcAccountMiddleConfig().GetSendPrologueThreshold(), ev.String())
		return nil, false
	}

	imRsp, err := s.clients.ImApiClient.Send1V1ExtMsg(ctx, &im_api.Send1V1ExtMsgReq{
		From: &im_api.User{
			Uid:      aiAccount.GetUid(),
			Username: aiAccount.GetUsername(),
			Nickname: aiAccount.GetNickname(),
		},
		To: &im_api.User{
			Uid:      userAccount.GetUid(),
			Username: userAccount.GetUsername(),
			Nickname: userAccount.GetNickname(),
		},
		Msg: &im_api.ExtMsg{
			MsgType:       uint32(im.IM_MSG_TYPE_TEXT_MSG),
			Content:       aiAccountRsp.GetPrologue(),
			MsgSourceType: uint32(im.MsgSourceType_MSG_SOURCE_FROM_AIGC_ACCOUNT),
		},
		Opt: &im_api.SendOption{
			WithOfflinePush: true,
			IsNeedReport:    true,
		},
		Namespace: "aigc_account_middle",
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "onUgcFollowingUpdate Send1V1Text failed, err:%v, ev:%s", err, ev.String())
		return err, false
	}
	if len(imRsp.GetTaskList()) == 0 {
		log.ErrorWithCtx(ctx, "onUgcFollowingUpdate Send1V1Text no task, imRsp:%s, ev:%s", imRsp.String(), ev.String())
		return nil, false
	}
	// 数据上报
	s.reporter.ReportMsg(ctx, report.AIMsgReportData{
		Uid:              uid,
		AIAccount:        aiAccount,
		AigcBussType:     report.SysBussType,
		AigcReplyMsgType: report.PrologueReplyMsgType,
		ReplyMsgTs:       imRsp.GetTaskList()[0].GetSvrMsgTime(),
		ReplyMsgId:       uint32(imRsp.GetTaskList()[0].GetFromSeq().GetSvrMsgId()),
		ReplyMsgContent:  aiAccountRsp.GetPrologue(),
	})
	log.InfoWithCtx(ctx, "onUgcFollowingUpdate Send1V1Text success, imRsp:%s, ev:%s", imRsp.String(), ev.String())
	return nil, false
}

func (s *Server) isUserToAI(ctx context.Context, fromUid, toUid uint32) (bool, map[uint32]*account_go.User, *aigc_account.AIAccount, error) {
	accountMap, aerr := s.clients.AccountGo.GetUsersMap(ctx, []uint32{fromUid, toUid})
	if aerr != nil {
		log.ErrorWithCtx(ctx, "isUserToAI GetUsersMap failed, fromUid:%d toUid:%d err:%v", fromUid, toUid, aerr)
		return false, nil, nil, aerr
	}

	// 只处理正常用户发给机器人的消息
	if accountPB.USER_TYPE(accountMap[fromUid].GetUserType()) != accountPB.USER_TYPE_USER_TYPE_COMMON ||
		accountPB.USER_TYPE(accountMap[toUid].GetUserType()) != accountPB.USER_TYPE_USER_TYPE_ROBOT {
		return false, nil, nil, nil
	}

	aiAccountRsp, err := s.clients.AigcAccount.GetAIAccount(ctx, &aigc_account.GetAIAccountRequest{
		Uid: toUid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "isUserToAI GetAIAccount failed, err:%v, fromUid:%d toUid:%d err:%v", fromUid, toUid, err)
		return false, nil, nil, err
	}
	if aiAccountRsp.GetAccount() == nil || aiAccountRsp.GetAccount().GetIsUnregister() {
		// AI账号不存在或已注销
		log.InfoWithCtx(ctx, "isUserToAI aiAccount not found or unregister, fromUid:%d toUid:%d", fromUid, toUid)
		return false, accountMap, aiAccountRsp.GetAccount(), nil
	}
	return true, accountMap, aiAccountRsp.GetAccount(), nil
}

func (s *Server) handleCybrosArbiterCallbackEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	callbackData := &censoring_proxyPB.CallbackData{}
	err := proto.Unmarshal(msg.Value, callbackData)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleCybrosArbiterCallbackEvent Failed to proto.Unmarshal %+v", err)
		return err, false
	}

	svr := &protogrpc.ServiceInfo{}
	if u, e := uuid.NewRandom(); e == nil {
		svr.RequestID = u.String()
	}

	ctx = protogrpc.WithServiceInfo(context.Background(), svr)
	ctx, cancel := context.WithTimeout(ctx, 6*time.Second)
	defer cancel()

	callbackDataStr, _ := json.Marshal(callbackData)
	log.InfoWithCtx(ctx, "handleCybrosArbiterCallbackEvent callbackData: %+v", string(callbackDataStr))

	// 检查消息是否需要处理
	isNeed := s.checkCybrosArbiterCallbackMessage(ctx, callbackData)
	if !isNeed {
		log.InfoWithCtx(ctx, "handleCybrosArbiterCallbackEvent no need to handle, callbackData: %+v", callbackData.String())
		return nil, false
	}

	postId := callbackData.GetTaskContext().GetSource().Id
	postInfo, err := s.clients.UgcContentClient.GetPostById(ctx, postId)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleCybrosArbiterCallbackEvent GetPostById err: %v, postId: %s", err, postId)
		return err, false
	}

	// 不在ai账号实验中的用户发的帖不需要处理
	isHeatAiAccountTest := s.isInAbtest(ctx, postInfo.GetUserId(), aigc_feature_router.Source_SourceSquareInteract)
	if !isHeatAiAccountTest {
		log.InfoWithCtx(ctx, "handleCybrosArbiterCallbackEvent postId:%s is not in abtest, postInfo: %+v", postId, postInfo)
		return nil, false
	}

	// 如果发帖用户是ai账号，不需要处理
	aiAccount, err := s.clients.AigcAccount.GetAIAccount(ctx, &aigc_account.GetAIAccountRequest{
		Uid: postInfo.GetUserId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleCybrosArbiterCallbackEvent GetAIAccount failed, err: %v, postId: %s", err, postId)
		return err, false
	}
	if aiAccount.GetAccount() != nil {
		log.InfoWithCtx(ctx, "handleCybrosArbiterCallbackEvent postId:%s user: %v is ai account, no need to handle", postId, postInfo.GetUserId())
		return nil, false
	}

	// 判断用户是否命中广场正交互动实验
	isHeatSquareAbTest := s.isHeatSquareAbTest(ctx, postInfo.GetUserId())
	log.InfoWithCtx(ctx, "handleCybrosArbiterCallbackEvent isHeatSquareAbTest uid:%v, postId:%s isHeatSquareAbTest: %v,", postInfo.GetUserId(), postId, isHeatSquareAbTest)

	// 获取用户互动过的AI账号列表
	interactAiList, lastInteractAiUid, err := s.getInteractionAiList(ctx, postInfo.GetUserId())
	if err != nil {
		log.ErrorWithCtx(ctx, "handleCybrosArbiterCallbackEvent getInteractionAiList failed, err: %v, postId: %s, userId: %d", err, postId, postInfo.GetUserId())
		return err, false
	}
	log.InfoWithCtx(ctx, "handleCybrosArbiterCallbackEvent interactAiList: %v, postId: %s, userId: %d", interactAiList, postId, postInfo.GetUserId())

	// 处理帖子点赞
	likeAiUidList, err := s.handleLikePost(ctx, postId, postInfo.GetUserId(), isHeatSquareAbTest, interactAiList)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleCybrosArbiterCallbackEvent handleLikePost failed, err: %v, postId: %s", err, postId)
		return err, false
	}

	// 处理帖子评论
	err = s.handleCommentPost(ctx, postInfo, isHeatSquareAbTest, likeAiUidList, lastInteractAiUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleCybrosArbiterCallbackEvent handleCommentPost failed, err: %v, postId: %s", err, postId)
		return err, false
	}

	return nil, false
}

func (s *Server) handleCommentPost(ctx context.Context, postInfo *ugcContentPb.PostInfo, isHeatSquareAbTest bool, likeAiUidList []uint32, lastInteractAiUid uint32) error {
	// 校验用户当天被ai评论的帖子数是否超过上限
	aiCommentPostCount, err := s.clients.AigcAccount.GetAiCommentPostCount(ctx, &aigc_account.GetAiCommentPostCountRequest{
		Uid: postInfo.GetUserId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleCommentPost GetAiCommentPostCount failed, err: %v, postId: %s, userId: %d", err, postInfo.GetPostId(), postInfo.GetUserId())
		return err
	}
	if aiCommentPostCount.GetCommentPostCount() >= config.GetAigcAccountMiddleConfig().PostAiInteractParam.AiCommentPostNumLimit {
		log.InfoWithCtx(ctx, "handleCommentPost userId:%d has reached ai comment post num(%v) limit, postId: %s", postInfo.GetUserId(), aiCommentPostCount.GetCommentPostCount(), postInfo.GetPostId())
		return nil
	}

	var commentAiUid uint32
	// 如果有互动过的AI账号，取最近互动的AI账号
	if lastInteractAiUid > 0 {
		commentAiUid = lastInteractAiUid
	}

	// 如果没有互动过的AI账号，且用户命中广场互动正交实验，从点赞的AI账号中随机选一个账号
	if commentAiUid == 0 && isHeatSquareAbTest {
		log.InfoWithCtx(ctx, "handleCommentPost no interaction ai account found, postId: %s, userId: %d, isHeatSquareAbTest: %v, likeAiUidList:%v", postInfo.GetPostId(), postInfo.GetUserId(), isHeatSquareAbTest, likeAiUidList)
		if len(likeAiUidList) > 0 {
			rand.New(rand.NewSource(time.Now().UnixNano()))
			commentAiUid = likeAiUidList[rand.Intn(len(likeAiUidList))]
		}
	}

	// 用户 [T-1日,T日] 内没有跟AI账号有IM互动记录，但是用户命中了广场互动正交实验，同时当天已经达到了ai点赞帖子的上限数，这个时候不会点赞，也不会评论
	if commentAiUid == 0 {
		log.InfoWithCtx(ctx, "handleCommentPost no ai account found, postId: %s, userId: %d", postInfo.GetPostId(), postInfo.GetUserId())
		return nil
	}

	// 评论
	err = s.aiCommentPostPublishEvent(ctx, commentAiUid, postInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleCommentPost aiCommentPostPublishEvent failed, err: %v, postId: %s, userId: %d", err, postInfo.GetPostId(), postInfo.GetUserId())
		return err
	}

	return nil
}

func (s *Server) handleLikePost(ctx context.Context, postId string, userId uint32, isHeatSquareAbTest bool, interactAiList []uint32) ([]uint32, error) {
	// 校验用户当天被ai点赞的帖子数是否超过上限
	aiLikePostCount, err := s.clients.AigcAccount.GetAiLikePostCount(ctx, &aigc_account.GetAiLikePostCountRequest{
		Uid: userId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleLikePost GetAiLikePostCount failed, err: %v, postId: %s, userId: %d", err, postId, userId)
		return nil, err
	}
	if aiLikePostCount.GetLikePostCount() >= config.GetAigcAccountMiddleConfig().PostAiInteractParam.AiLikePostNumLimit {
		log.InfoWithCtx(ctx, "handleLikePost userId:%d has reached ai like post num(%v) limit, postId: %s", userId, aiLikePostCount.GetLikePostCount(), postId)
		return nil, nil
	}

	// 如果没有互动过的AI账号，且用户命中广场互动正交实验，随机返回异性ai账号
	if len(interactAiList) == 0 && isHeatSquareAbTest {
		log.InfoWithCtx(ctx, "handleLikePost no interaction ai account found, postId: %s, userId: %d, isHeatSquareAbTest: %v", postId, userId, isHeatSquareAbTest)
		interactAiList, err = s.getRandomAiAccountForLike(ctx, userId)
		if err != nil {
			log.ErrorWithCtx(ctx, "handleLikePost getRandomAiAccountForLike failed, err: %v, postId: %s, userId: %d", err, postId, userId)
			return nil, err
		}
	}
	if len(interactAiList) == 0 {
		log.InfoWithCtx(ctx, "handleLikePost no interaction ai account found, postId: %s, userId: %d", postId, userId)
		return nil, nil
	}

	// 点赞
	var likeSuccess bool
	likeAiUidList := make([]uint32, 0, len(interactAiList))
	for _, aiUid := range interactAiList {
		_, err = s.clients.UgcContentClient.AddAttitude(ctx, &ugcContentPb.AddAttitudeReq{
			PostId:       postId,
			UserId:       aiUid,
			AttitudeType: uint32(ugcPb.Attitude_LIKE),
			TargetUserId: userId,
			IsFirstTime:  true,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "handleLikePost AddAttitude failed, err: %v, postId: %s, userId: %d, aiUid: %d", err, postId, userId, aiUid)
			continue // 继续处理下一个AI账号
		}

		likeSuccess = true
		likeAiUidList = append(likeAiUidList, aiUid)
	}

	if !likeSuccess {
		log.WarnWithCtx(ctx, "handleLikePost no ai account liked the post, postId: %s, userId: %d", postId, userId)
		return nil, err
	}

	log.InfoWithCtx(ctx, "handleLikePost ai accounts liked the post, postId: %s, userId: %d, likeAiUidList: %v", postId, userId, likeAiUidList)

	// 添加帖子ai点赞记录
	_, err = s.clients.AigcAccount.AddAiLikePost(ctx, &aigc_account.AddAiLikePostRequest{
		Uid:    userId,
		PostId: postId,
	})
	if err != nil {
		log.WarnWithCtx(ctx, "handleLikePost AddAiLikePost failed, err: %v, postId: %s, userId: %d", err, postId, userId)
		return likeAiUidList, nil
	}

	return likeAiUidList, nil
}

func (s *Server) getInteractionAiList(ctx context.Context, userId uint32) ([]uint32, uint32, error) {
	// 获取与用户互动过的AI账号列表
	resp, err := s.clients.AigcAccount.GetUserAiInteractionRecord(ctx, &aigc_account.GetUserAiInteractionRecordRequest{
		Uid: userId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getInteractionAiList GetUserAiInteractionRecord failed, userId: %d, err: %v", userId, err)
		return nil, 0, err
	}

	if len(resp.GetAiUidList()) == 0 {
		return nil, 0, nil
	}

	// 最新互动的ai账号，用于评论使用
	lastInteractAiUid := resp.GetAiUidList()[0]

	num := config.GetAigcAccountMiddleConfig().PostAiInteractParam.AiAccountRandomNum
	if num == 0 {
		num = 8 // 默认随机获取8个AI账号
	}

	// 随机获取1-8个互动过的AI账号
	return s.getRandomAiAccountFromList(ctx, resp.GetAiUidList(), int(num)), lastInteractAiUid, nil
}

func (s *Server) getRandomAiAccountForLike(ctx context.Context, userId uint32) ([]uint32, error) {
	// 获取帖子用户信息
	userInfoResp, err := s.clients.AccountGo.GetUserByUid(ctx, userId)
	if err != nil {
		log.ErrorWithCtx(ctx, "getRandomAiAccountForLike AccountGo.GetUserByUid failed, err: %v", err)
		return nil, err
	}

	// 获取指定数量的异性AI账号
	var gender int32
	if userInfoResp.GetSex() == gender {
		gender = 1
	}
	// 获取AI账号列表
	aiAccountList, errs := s.clients.AigcAccount.GetAIAccountByGender(ctx, &aigc_account.GetAIAccountByGenderRequest{
		Sex:   gender,
		Limit: 10,
	})
	if errs != nil {
		log.ErrorWithCtx(ctx, "getRandomAiAccountForLike GetAIAccountByGender failed, err: %v", errs)
		return nil, errs
	}

	if len(aiAccountList.GetAccountList()) == 0 {
		log.WarnWithCtx(ctx, "getRandomAiAccountForLike no abstract sex ai account, target sex: %d", gender)
		return nil, nil
	}

	aiUidList := make([]uint32, 0, len(aiAccountList.GetAccountList()))
	for _, account := range aiAccountList.GetAccountList() {
		aiUidList = append(aiUidList, account.GetUid())
	}

	// 随机选取1-4个账号
	return s.getRandomAiAccountFromList(ctx, aiUidList, 4), nil

}

func (s *Server) getRandomAiAccountFromList(ctx context.Context, aiUidList []uint32, num int) []uint32 {
	if len(aiUidList) == 0 {
		return aiUidList
	}

	// 确定要选取的数量
	selectCount := rand.Intn(num) + 1
	if selectCount >= len(aiUidList) {
		log.InfoWithCtx(ctx, "getRandomAiAccountFromList selected ai uid list: %v", aiUidList)
		return aiUidList
	}

	// 使用rand.Perm生成随机索引
	indices := rand.Perm(len(aiUidList))
	result := make([]uint32, selectCount)
	for i := 0; i < selectCount; i++ {
		result[i] = aiUidList[indices[i]]
	}

	log.InfoWithCtx(ctx, "getRandomAiAccountFromList selected ai uid list: %v, randNum:%v, aiUidList:%v", result, num, aiUidList)

	return result
}

func (s *Server) checkCybrosArbiterCallbackMessage(ctx context.Context, callbackData *censoring_proxyPB.CallbackData) bool {
	if _, ok := callbackData.Params["quicksilver"]; !ok {
		log.WarnWithCtx(ctx, "handleCybrosArbiterCallbackEvent no quicksilver param")
		return false
	}

	// 非ugc广场帖不处理
	if callbackData.GetTaskContext().GetCategory() != cybros_arbiter.CategoryUgcPost {
		log.WarnWithCtx(ctx, "handleCybrosArbiterCallbackEvent not ugc post category, category: %s", callbackData.GetTaskContext().GetCategory())
		return false
	}

	// 帖子审核不通过不处理
	// 帖子审核目前是人工审核模式，因此针对机审结果可忽略
	if len(callbackData.GetScanResult().GetData()) == 0 || callbackData.GetAuditType() == censoring_proxyPB.AuditType_MACHINE || callbackData.GetScanResult().GetData()[0].Suggestion != censoring_proxyPB.Suggestion_PASS {
		log.WarnWithCtx(ctx, "handleCybrosArbiterCallbackEvent post not pass, suggestion: %+v", callbackData.GetScanResult().GetData())
		return false
	}

	return true
}

func (s *Server) isHeatSquareAbTest(ctx context.Context, uid uint32) bool {
	testParamConf := config.GetAigcAccountMiddleConfig().PostAiInteractParam
	res, err := s.clients.AbTestClient.GetABTestResult(ctx, uid, []byte{}, AbtestClientTypeUID, testParamConf.SquareInteractExpParam.AbLayerTag, testParamConf.SquareInteractExpParam.ParamName, AbtestClientType)
	if err != nil {
		log.ErrorWithCtx(ctx, "isHeatSquareAbTest GetABTestResult failed, uid: %d, err: %v", uid, err)
		return false
	}

	return res == testParamConf.SquareInteractExpParam.ParamStrategy
}

// aiCommentPostPublishEvent AI评论帖子内容生成发布事件
func (s *Server) aiCommentPostPublishEvent(ctx context.Context, aiUid uint32, postInfo *ugcContentPb.PostInfo) error {
	var (
		eg, egCtx    = errgroup.WithContext(ctx)
		userInfoList map[uint32]*accountGoPB.UserResp
		aigcUserInfo *aigc_account.AIAccount
	)
	// 获取账号信息
	eg.Go(func() error {
		listMap, err := s.clients.AccountGo.GetUsersMap(egCtx, []uint32{postInfo.GetUserId(), aiUid})
		if err != nil {
			log.ErrorWithCtx(egCtx, "aiCommentPostPublishEvent GetUsersMap failed, err: %v, postInfo: %+v", err, postInfo)
			return err
		}
		userInfoList = listMap
		return nil
	})

	// 获取ai账号信息
	eg.Go(func() error {
		aiAccountResp, err := s.clients.AigcAccount.GetAIAccount(egCtx, &aigc_account.GetAIAccountRequest{
			Uid: aiUid,
		})
		if err != nil {
			log.ErrorWithCtx(egCtx, "aiCommentPostPublishEvent GetAIAccount failed, err: %v, aiUid: %d", err, aiUid)
			return err
		}
		if aiAccountResp.GetAccount() == nil {
			log.WarnWithCtx(egCtx, "aiCommentPostPublishEvent ai account not found, aiUid: %d", aiUid)
			return nil
		}

		aigcUserInfo = aiAccountResp.GetAccount()

		return nil
	})

	if err := eg.Wait(); err != nil {
		log.ErrorWithCtx(ctx, "aiCommentPostPublishEvent errgroup wait failed, err: %v", err)
		return err
	}

	ctxId := s.getCtxIdOfAiCommentPost(postInfo.GetUserId(), AiCommentPostPrefix)

	callbackData := map[string]string{
		"ai_uid":  fmt.Sprintf("%d", aiUid),
		"post_id": postInfo.GetPostId(),
		"ctx_id":  ctxId,
	}

	dataInfo := make(map[string]interface{}, 8)
	dataInfo["post_type"] = postInfo.GetPostType()

	// 如果帖子文本内容包含了@用户，去掉这部分信息，保留文本信息
	newContent := cleanContent(postInfo.GetContent())
	dataInfo["post_content"] = newContent
	dataInfo["post_topic"] = postInfo.GetTopicId()
	dataInfo["post_tags"] = postInfo.GetTags()
	imageUrls := make([]string, 0, len(postInfo.GetAttachments()))
	if len(postInfo.GetAttachments()) > 0 {
		for _, attachment := range postInfo.GetAttachments() {
			if postInfo.GetPostType() == ugcContentPb.PostInfo_IMAGE {
				imageUrls = append(imageUrls, attachment.GetContent())
			}
			if postInfo.GetPostType() == ugcContentPb.PostInfo_VIDEO {
				dataInfo["video_urls"] = attachment.GetContent()
				break
			}
		}
	}
	dataInfo["image_urls"] = imageUrls
	dataInfo["prompt_id"] = aigcUserInfo.GetPromptId()

	// 传给模型的信息
	placeholder := make(map[string]string, 10)
	if _, ok := userInfoList[postInfo.GetUserId()]; ok {
		placeholder["user_nickname"] = userInfoList[postInfo.GetUserId()].GetNickname()
		sex := "女"
		if userInfoList[postInfo.GetUserId()].GetSex() > 0 {
			sex = "男"
		}
		placeholder["user_sex"] = sex
		//placeholder["user_age"] = userInfoList[postInfo.GetUserId()]
		// todo 获取头像
		//placeholder["user_avatar"] = userInfoList[postInfo.GetUserId()]
		// todo 获取IP
	}

	if _, ok := userInfoList[aiUid]; ok {
		placeholder["ai_nickname"] = userInfoList[postInfo.GetUserId()].GetNickname()
		placeholder["ai_signature"] = userInfoList[postInfo.GetUserId()].GetSignature()
		placeholder["ai_prologue"] = aigcUserInfo.GetPrologue()

		province, city, _ := iplocation.GetLocationInfo(ctx, aigcUserInfo.GetIp())
		var location string
		if city != "" {
			location = city
		} else if province != "" {
			location = province
		}
		placeholder["ai_ip_location"] = location
	}

	dataInfo["placeholder"] = placeholder

	data, err := json.Marshal(dataInfo)
	if err != nil {
		log.WarnWithCtx(ctx, "aiCommentPostPublishEvent json.Marshal failed, err: %v, dataInfo: %+v", err, dataInfo)
		return err
	}

	message := &rcmd_mt_proxy.AsyncReq{
		CtxId:        ctxId,
		Uid:          int64(postInfo.GetUserId()),
		Cmd:          GenAiCommentCmd,
		CallbackData: callbackData,
		Data:         string(data),
	}

	reqStr, _ := json.Marshal(message)
	log.InfoWithCtx(ctx, "aiCommentPostPublishEvent publish event, req: %v", string(reqStr))

	err = s.eventBus.Publish(ctx, event.PublisAiCommentGen, ctxId, message)
	if err != nil {
		log.ErrorWithCtx(ctx, "aiCommentPostPublishEvent Publish failed, err: %v", err)
		// 发布失败，可能是网络问题，直接返回错误
		return err
	}
	log.InfoWithCtx(ctx, "aiCommentPostPublishEvent event published successfully")

	return nil
}

func (s *Server) handleAiCommentPostEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	ev := &rcmd_mt_proxy.AsyncRsp{}
	if err := proto.Unmarshal(msg.Value, ev); err != nil {
		log.ErrorWithCtx(ctx, "handleAiCommentPostEvent Unmarshal err: %v", err)
		return err, false
	}

	svr := &protogrpc.ServiceInfo{}
	if u, e := uuid.NewRandom(); e == nil {
		svr.RequestID = u.String()
	}

	ctx = protogrpc.WithServiceInfo(context.Background(), svr)
	ctx, cancel := context.WithTimeout(ctx, 6*time.Second)
	defer cancel()

	log.InfoWithCtx(ctx, "handleAiCommentPostEvent event: %s", ev.String())

	if ev.GetProxyCode() != int32(rcmd_mt_proxy.ProxyCode_ProxyCode_Success) {
		log.ErrorWithCtx(ctx, "handleAiCommentPostEvent event failed, proxyCode: %v, ctxId: %s", ev.GetProxyCode(), ev.GetCtxId())
		return nil, false
	}

	data := &rcmd_mt_proxy.AIAccountCommentPostRsp{}
	if err := json.Unmarshal([]byte(ev.GetData()), data); err != nil {
		log.ErrorWithCtx(ctx, "handleAiCommentPostEvent Unmarshal data err: %v", err)
		return err, false
	}

	log.InfoWithCtx(ctx, "handleAiCommentPostEvent dataInfo: %s", data.String())

	if data.GetErrMsg() != "" {
		log.ErrorWithCtx(ctx, "handleAiCommentPostEvent dataInfo has error, errMsg: %s, ctxId: %s", data.GetErrMsg(), ev.GetCtxId())
		return nil, false
	}

	if data.GetCommentContent() == "" {
		log.WarnWithCtx(ctx, "handleAiCommentPostEvent dataInfo comment content is empty, ctxId: %s", ev.GetCtxId())
		return nil, false
	}

	aiUid, errs := strconv.Atoi(ev.GetCallbackData()["ai_uid"])
	if errs != nil {
		log.ErrorWithCtx(ctx, "handleAiCommentPostEvent strconv.Atoi failed, err: %v", errs)
		return errs, false
	}

	// 获取ai账号信息
	aiAccount, err := s.clients.AigcAccount.GetAIAccount(ctx, &aigc_account.GetAIAccountRequest{
		Uid: uint32(aiUid),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAiCommentPostEvent GetAIAccount failed, err: %v, aiUid: %d", err, aiUid)
		return err, false
	}
	if aiAccount.GetAccount() == nil || aiAccount.GetAccount().GetIsUnregister() {
		log.WarnWithCtx(ctx, "handleAiCommentPostEvent ai account not found or unregister, accountInfo: %v", aiAccount.GetAccount())
		return nil, false
	}

	var ipLoc *ugcContentPb.Location
	var clientIp uint32
	if aiAccount.GetAccount().GetIp() != "" {
		// IP字符串转换为uint32
		clientIp = ipStringToUint32(aiAccount.GetAccount().GetIp())

		// 获取地理位置信息
		loc, err := iplocation.GetIpLocation(ctx, aiAccount.GetAccount().GetIp())
		if err == nil && loc != nil {
			ipLoc = &ugcContentPb.Location{
				CountryCode:  loc.Country.Code,
				Country:      loc.Country.Name,
				ProvinceCode: loc.Province.Code,
				Province:     loc.Province.Name,
				CityCode:     loc.City.Code,
				City:         loc.City.Name,
			}
		} else {
			log.WarnWithCtx(ctx, "handleAiCommentPostEvent GetIpLocation failed, err: %v, loc: %v", err, loc)
		}
	}

	// 评论
	res, err := s.clients.UgcContentClient.AddComment(ctx, &ugcContentPb.AddCommentReq{
		UserId:   uint32(aiUid),
		PostId:   ev.GetCallbackData()["post_id"],
		Content:  data.GetCommentContent(),
		Status:   ugcContentPb.ContentStatus_CONTENT_STATUS_NORMAL,
		ClientIp: clientIp,
		IpLoc:    ipLoc,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAiCommentPostEvent AddComment failed, err: %v, ev: %v", err, ev.String())
		return err, false
	}

	log.InfoWithCtx(ctx, "handleAiCommentPostEvent AddComment success, res: %v", res.String())

	// 记录用户被ai评论的帖子数
	_, errs = s.clients.AigcAccount.AddAiCommentPost(ctx, &aigc_account.AddAiCommentPostRequest{
		Uid:    uint32(ev.GetUid()),
		PostId: ev.GetCallbackData()["post_id"],
	})
	if errs != nil {
		log.ErrorWithCtx(ctx, "handleAiCommentPostEvent AddAiCommentPost failed, err: %v", errs)
		return nil, false
	}

	// 添加评论记录
	_, errs = s.clients.AigcAccount.AddPostAiCommentRecord(ctx, &aigc_account.AddPostAiCommentRecordRequest{
		PostId: ev.GetCallbackData()["post_id"],
		AiUid:  uint32(aiUid),
	})
	if errs != nil {
		log.ErrorWithCtx(ctx, "handleAiCommentPostEvent AddAiCommentPost failed, err: %v", errs)
		return nil, false
	}
	log.InfoWithCtx(ctx, "handleAiCommentPostEvent AigcAccount.AddAiCommentPost success, postId: %s, aiUid: %d", ev.GetCallbackData()["post_id"], aiUid)

	return nil, false
}

func (s *Server) getCtxIdOfAiCommentPost(uid uint32, prefix string) string {
	return fmt.Sprintf("%v_%v_%v", prefix, uid, time.Now().Format("**************"))
}

func ipStringToUint32(ipStr string) uint32 {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return 0
	}

	// 转换为 IPv4
	ipv4 := ip.To4()
	if ipv4 == nil {
		return 0
	}

	return binary.BigEndian.Uint32(ipv4)
}

func cleanContent(content string) string {
	var result strings.Builder
	var i int

	for i < len(content) {
		// 查找 "&^" 的开始位置
		start := strings.Index(content[i:], "&^")
		if start == -1 {
			// 没有找到，添加剩余内容
			result.WriteString(content[i:])
			break
		}

		// 添加 "&^" 之前的内容
		result.WriteString(content[i : i+start])

		// 查找对应的结束 "&^"
		searchStart := i + start + 2
		end := strings.Index(content[searchStart:], "&^")
		if end == -1 {
			// 没有找到结束标记，保留原内容
			result.WriteString(content[i+start:])
			break
		}

		// 跳过整个 "&^...&^" 块
		i = searchStart + end + 2
	}

	return result.String()
}
