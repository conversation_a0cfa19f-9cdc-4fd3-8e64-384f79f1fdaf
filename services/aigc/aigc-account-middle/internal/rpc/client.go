package rpc

import (
	"context"
	"gitlab.ttyuyin.com/tyr/tt-ecosystem/grpc/client/interceptor"
	im_api "golang.52tt.com/clients/im-api"
	ugcContent "golang.52tt.com/clients/ugc/content"
	user_auth_history "golang.52tt.com/clients/user-auth-history"
	"golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_chat"
	"golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_feature_router"
	user_online "golang.52tt.com/protocol/services/user-online"
	"google.golang.org/grpc"

	account_apicenter "golang.52tt.com/protocol/services/account-apicenter"

	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/mvp_proxy"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/photo_album_go"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/security_go"
	"golang.52tt.com/clients/account"
	exp "golang.52tt.com/clients/expsvr"
	user_tag_go "golang.52tt.com/clients/user-tag-go"
	aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"
	"golang.52tt.com/protocol/services/avatar"
	avatar_mng_api "golang.52tt.com/protocol/services/avatar-mng-api"

	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_go"
	account_go "golang.52tt.com/clients/account-go"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/pkg/abtest"
	"golang.52tt.com/pkg/log"
)

const (
	AbtestUrl = "http://abtestlogic.commonservice.svc.cluster.local:8000/AbtestLogicService/GetUsersAbtestByTag"
)

type Clients struct {
	Account          account.IClient
	AccountGo        account_go.IClient
	AccountApiCenter account_apicenter.AccountApicenterClient
	Avatar           avatar.AvatarClient
	AvatarMngApi     avatar_mng_api.AvatarMngApiClient
	Censoring        censoring_proxy.IClient
	MvpProxy         mvp_proxy.MvpProxyClient
	PhotoAlbumGo     photo_album_go.PhotoAlbumGoClient
	UserTagGo        user_tag_go.IClient
	Exp              exp.IClient
	Security         security_go.SecurityGoClient
	AuthHistory      user_auth_history.IClient

	AigcAccount aigc_account.AigcAccountClient

	AigcChatClient   aigc_chat.AigcChatServiceClient
	AigcRouterClient aigc_feature_router.AIGCFeatureRouterClient
	UserOnlineClient user_online.UserOnlineClient
	ImApiClient      im_api.IClient
	UgcContentClient ugcContent.IClient
	AbTestClient     abtest.IABTestClient
	ChannelGoClient  channel_go.ChannelGoClient
}

func NewClients() (*Clients, error) {
	c := new(Clients)

	var (
		err error
		ctx = context.Background()
	)

	c.Account, err = account.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients aigc_account.NewClient err: %v", err)
		return nil, err
	}

	c.AccountGo, err = account_go.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients account_go.NewClient err: %v", err)
		return nil, err
	}

	c.AccountApiCenter, err = account_apicenter.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients account_apicenter.NewClient err: %v", err)
		return nil, err
	}

	c.Avatar, err = avatar.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients avatar.NewClient err: %v", err)
		return nil, err
	}

	c.AvatarMngApi, err = avatar_mng_api.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients avatar_mng_api.NewClient err: %v", err)
		return nil, err
	}

	c.Censoring = censoring_proxy.NewClient()

	c.MvpProxy, err = mvp_proxy.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients mvp_proxy.NewClient err: %v", err)
		return nil, err
	}

	c.PhotoAlbumGo, err = photo_album_go.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients photo_album_go.NewClient err: %v", err)
		return nil, err
	}

	c.UserTagGo, err = user_tag_go.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients user_tag_go.NewClient err: %v", err)
		return nil, err
	}

	c.Exp = exp.NewClient()

	c.Security, err = security_go.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients security_go.NewClient err: %v", err)
		return nil, err
	}

	c.AuthHistory, err = user_auth_history.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients user_auth_history.NewClient err: %v", err)
		return nil, err
	}

	c.AigcAccount, err = aigc_account.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients aigc_account.NewClient err: %v", err)
		return nil, err
	}

	c.AigcChatClient = aigc_chat.MustNewClientTo(ctx, "aigc-chat.rcmd-tt.svc:8000",
		grpc.WithChainUnaryInterceptor(interceptor.NoneStatusAdapterInterceptor()))

	c.AigcRouterClient = aigc_feature_router.MustNewClientTo(ctx, "aigc-feature-router.rcmd-tt.svc:8000",
		grpc.WithChainUnaryInterceptor(interceptor.NoneStatusAdapterInterceptor()))

	c.UserOnlineClient, err = user_online.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients user_online.NewClient err: %v", err)
		return nil, err
	}

	c.ImApiClient, err = im_api.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients im_api.NewClient err: %v", err)
		return nil, err
	}

	c.UgcContentClient, err = ugcContent.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients ugcContent.NewClient err: %v", err)
		return nil, err
	}

	c.AbTestClient = abtest.NewABTestClient(AbtestUrl, uint32(abtest.APPID_TTyuyin), "")

	c.ChannelGoClient, err = channel_go.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients channel_go.NewClient err: %v", err)
		return nil, err
	}

	return c, nil
}
