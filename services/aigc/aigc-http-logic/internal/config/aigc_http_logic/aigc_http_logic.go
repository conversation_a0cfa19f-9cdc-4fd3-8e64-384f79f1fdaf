package config

import (
	"fmt"
	"math/rand"
	"sync/atomic"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
	"golang.52tt.com/kaihei-pkg/asr"
	pb "golang.52tt.com/protocol/services/aigc/aigc-http-logic"
)

const (
	defaultReadHeartThreshold = 5
)

var defaultScriptFilterItems = []*pb.FilterItem{
	{
		Type:        pb.FilterItem_FILTER_TYPE_SEX_ALL,
		DisplayName: "推荐",
	},
	{
		Type:        pb.FilterItem_FILTER_TYPE_SEX_FEMALE,
		DisplayName: "女生玩",
	},
	{
		Type:        pb.FilterItem_FILTER_TYPE_SEX_MALE,
		DisplayName: "男生玩",
	},
}

type AigcHttpLogicConfig struct {
	// 通用读心数
	CommonReadHeartThreshold uint32 `json:"common_read_heart_threshold" `

	// 剧本筛选项
	ScriptFilterItems []*pb.FilterItem `json:"script_filter_items"`
	// 剧本列表轮播头像数量
	ScriptListAvatarNum int `json:"script_list_avatar_num"`
	// 剧本列表轮播兜底头像链接
	ScriptListFallBackAvatars []string `json:"script_list_fall_back_avatars"`
	// 剧本列表随机在玩人数区间配置
	PlayingMemberNumInterval Interval `json:"playing_member_num_interval"`
	// 在玩人数文案模板
	PlayingMemberNumText string `json:"playing_member_num_text"`
	// 剧本banner 标题
	ScriptBannerTitle string `json:"script_banner_title"`

	AudioToTextConf *asr.AsrConfig `json:"audio_to_text_conf"`

	// 互动玩法改造热门列表开关
	InteractiveGameTopSwitch bool `json:"interactive_game_top_switch"`
}

type Interval struct {
	Min int `json:"min"`
	Max int `json:"max"`
}

// Format
// 配置加载时调用Format报错则返回错误
// 配置更新时调用Format报错则放弃此次更新
func (s *AigcHttpLogicConfig) Format() error {
	return nil
}

var (
	atomicAigcHttpLogicConfig *atomic.Value
)

func init() {
	//if err := InitAigcHttpLogicConfig(); err != nil {
	//    panic(err)
	//}
}

// InitAigcHttpLogicConfig
// 可以选择外部初始化或者直接init函数初始化
func InitAigcHttpLogicConfig() error {
	cfg := &AigcHttpLogicConfig{}
	atomCfg, err := ttconfig.AtomLoad("aigc-http-logic", cfg)
	if nil != err {
		return err
	}
	atomicAigcHttpLogicConfig = atomCfg
	return nil
}

func GetAigcHttpLogicConfig() *AigcHttpLogicConfig {
	return atomicAigcHttpLogicConfig.Load().(*AigcHttpLogicConfig)
}

func (s *AigcHttpLogicConfig) GetCommonReadHeartThreshold() uint32 {
	if s == nil {
		return defaultReadHeartThreshold
	}
	return s.CommonReadHeartThreshold
}

func (s *AigcHttpLogicConfig) GetScriptFilterItems() []*pb.FilterItem {
	if s == nil || len(s.ScriptFilterItems) == 0 {
		return defaultScriptFilterItems
	}

	return s.ScriptFilterItems
}

func (s *AigcHttpLogicConfig) GetScriptListAvatarNum() int {
	if s == nil {
		return 0
	}

	return s.ScriptListAvatarNum
}

func (s *AigcHttpLogicConfig) GetFallBackAvatars(cnt int) []string {
	if s == nil || len(s.ScriptListFallBackAvatars) == 0 {
		return nil
	}

	if cnt > len(s.ScriptListFallBackAvatars) {
		cnt = len(s.ScriptListFallBackAvatars)
	}
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	result := make([]string, 0, cnt)
	indices := r.Perm(len(s.ScriptListFallBackAvatars))
	for i := 0; i < cnt; i++ {
		result = append(result, s.ScriptListFallBackAvatars[indices[i]])
	}

	return result
}

func (s *AigcHttpLogicConfig) GetRandomPlayTips() string {
	if s == nil || len(s.PlayingMemberNumText) == 0 || s.PlayingMemberNumInterval.Min >= s.PlayingMemberNumInterval.Max {
		return ""
	}
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	num := r.Intn(s.PlayingMemberNumInterval.Max-s.PlayingMemberNumInterval.Min+1) + s.PlayingMemberNumInterval.Min
	return fmt.Sprintf(s.PlayingMemberNumText, num)
}

func (s *AigcHttpLogicConfig) GetBannerTitle() string {
	if s == nil || len(s.ScriptBannerTitle) == 0 {
		return ""
	}
	return s.ScriptBannerTitle
}

func (s *AigcHttpLogicConfig) GetInteractiveGameTopSwitch() bool {
	if s == nil {
		return false
	}
	return s.InteractiveGameTopSwitch
}
