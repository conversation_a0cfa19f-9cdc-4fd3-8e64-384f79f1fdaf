package internal

import (
	"context"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	aigc_group "golang.52tt.com/protocol/services/aigc/aigc-group"
	api "golang.52tt.com/protocol/services/aigc/aigc-http-logic"
	"golang.52tt.com/services/aigc/aigc-http-logic/internal/assembler"
	config "golang.52tt.com/services/aigc/aigc-http-logic/internal/config/aigc_http_logic"
)

func (s *Server) CreateGroup(ctx context.Context, req *api.CreateGroupRequest) (*api.CreateGroupResponse, error) {
	uid := metainfo.GetServiceInfo(ctx).UserID()
	resp := new(api.CreateGroupResponse)

	listReq := &aigc_group.GetUserOwnedGroupListRequest{
		Uid:        uid,
		TemplateId: req.GetTemplateId(),
	}
	listResp, err := s.clients.AigcGroup.GetUserOwnedGroupList(ctx, listReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateGroup GetUserOwnedGroupList req(%+v) err: %v", listReq, err)
		return nil, err
	}
	if len(listResp.GetList()) > 0 {
		log.WarnWithCtx(ctx, "CreateGroup GetUserOwnedGroupList req(%+v) not empty", listReq)
		return nil, protocol.NewExactServerError(nil, status.ErrAigcGroupGroupExisted)
	}

	createReq := &aigc_group.CreateGroupRequest{
		Type:       aigc_group.GroupType_GROUP_TYPE_SINGLE_USER,
		Source:     aigc_group.GroupSource_GROUP_SOURCE_USER,
		OwnerUid:   uid,
		TemplateId: req.GetTemplateId(),
	}
	createResp, err := s.clients.AigcGroup.CreateGroup(ctx, createReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateGroup CreateGroup req(%+v) err: %v", createReq, err)
		return nil, err
	}

	resp.Id = createResp.GetId()
	return resp, nil
}

func (s *Server) LeaveGroup(ctx context.Context, req *api.LeaveGroupRequest) (*api.LeaveGroupResponse, error) {
	resp := new(api.LeaveGroupResponse)
	log.InfoWithCtx(ctx, "LeaveGroup req: %+v", req)

	if req.GetGroupId() == 0 {
		log.WarnWithCtx(ctx, "LeaveGroup invalid req: %+v", req)
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	uid := metainfo.GetServiceInfo(ctx).UserID()

	user, rpcErr := s.clients.Account.GetUserByUid(ctx, uid)
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "LeaveGroup GetUserByUid uid(%d) err: %v", uid, rpcErr)
		return nil, rpcErr
	}

	leaveReq := &aigc_group.LeaveGroupRequest{
		User: &aigc_group.User{
			Uid: user.GetUid(),
			Sex: user.GetSex(),
		},
		GroupId: req.GetGroupId(),
	}
	_, err := s.clients.AigcGroup.LeaveGroup(ctx, leaveReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "LeaveGroup LeaveGroup req(%+v) err: %v", leaveReq, err)
		return nil, err
	}

	return resp, nil
}

func (s *Server) DeleteGroup(ctx context.Context, req *api.DeleteGroupRequest) (*api.DeleteGroupResponse, error) {
	uid := metainfo.GetServiceInfo(ctx).UserID()
	resp := new(api.DeleteGroupResponse)

	getReq := &aigc_group.GetGroupInfoRequest{
		Id: req.GetId(),
	}
	getResp, err := s.clients.AigcGroup.GetGroupInfo(ctx, getReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteGroup GetGroupInfo req(%+v) err: %v", getReq, err)
		return nil, err
	}
	if getResp.GetInfo() == nil || getResp.GetInfo().GetOwnerUid() != uid {
		log.WarnWithCtx(ctx, "DeleteGroup GetGroupInfo req(%+v) not found or not owner", getReq)
		return nil, protocol.NewExactServerError(nil, status.ErrAigcGroupGroupNotFound)
	}

	deleteReq := &aigc_group.DeleteGroupRequest{
		Id: req.GetId(),
	}
	_, err = s.clients.AigcGroup.DeleteGroup(ctx, deleteReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteGroup DeleteGroup req(%+v) err: %v", deleteReq, err)
		return nil, err
	}

	return resp, nil
}

func (s *Server) GetGroupInfo(ctx context.Context, req *api.GetGroupInfoRequest) (*api.GetGroupInfoResponse, error) {
	uid := metainfo.GetServiceInfo(ctx).UserID()
	resp := new(api.GetGroupInfoResponse)

	var group *aigc_group.Group
	switch {
	case req.GetId() > 0: // 根据群组id查询
		getReq := &aigc_group.GetGroupInfoRequest{
			Id: req.GetId(),
		}
		getResp, err := s.clients.AigcGroup.GetGroupInfo(ctx, getReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGroupInfo GetGroupInfo req(%+v) err: %v", getReq, err)
			return nil, err
		}

		group = getResp.GetInfo()
	case req.GetTemplateId() > 0: // 根据群主uid + 群模板id查询
		listReq := &aigc_group.GetUserOwnedGroupListRequest{
			Uid:        uid,
			TemplateId: req.GetTemplateId(),
		}
		listResp, err := s.clients.AigcGroup.GetUserOwnedGroupList(ctx, listReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGroupInfo GetUserOwnedGroupList req(%+v) err: %v", listReq, err)
			return nil, err
		}

		if len(listResp.GetList()) > 0 {
			group = listResp.GetList()[0]
		}
	default:
		log.WarnWithCtx(ctx, "GetGroupInfo invalid req: %+v", req)
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "miss id or template_id")
	}
	if group == nil {
		return resp, nil
	}

	info, err := s.aiGroupAssembler.BuildGroupInfo(ctx, group)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupInfo BuildGroupInfo group(%+v) err: %v", group, err)
		return nil, err
	}

	resp.Info = info
	return resp, nil
}

func (s *Server) GetGroupList(ctx context.Context, req *api.GetGroupListRequest) (*api.GetGroupListResponse, error) {
	uid := metainfo.GetServiceInfo(ctx).UserID()
	resp := new(api.GetGroupListResponse)

	listReq := &aigc_group.GetUserJoinedGroupListRequest{
		Uid: uid,
	}
	listResp, err := s.clients.AigcGroup.GetUserJoinedGroupList(ctx, listReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupList GetUserJoinedGroupList req(%+v) err: %v", listReq, err)
		return nil, err
	}
	if len(listResp.GetList()) == 0 {
		return resp, nil
	}

	list, err := s.aiGroupAssembler.BuildGroupList(ctx, listResp.GetList())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupList BuildGroupList groups(%+v) err: %v", listResp.GetList(), err)
		return nil, err
	}

	resp.List = list
	return resp, nil
}

func (s *Server) GetGroupTemplateInfo(ctx context.Context, req *api.GetGroupTemplateInfoRequest) (*api.GetGroupTemplateInfoResponse, error) {
	resp := new(api.GetGroupTemplateInfoResponse)

	getReq := &aigc_group.GetGroupTemplateByIdsRequest{
		Ids: []uint32{req.GetId()},
	}
	getResp, err := s.clients.AigcGroup.GetGroupTemplateByIds(ctx, getReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupTemplateInfo GetGroupTemplateByIds req(%+v) err: %v", getReq, err)
		return nil, err
	}
	if len(getResp.GetList()) == 0 {
		log.WarnWithCtx(ctx, "GetGroupTemplateInfo GetGroupTemplateByIds req(%+v) empty", getReq)
		return resp, nil
	}

	info, err := s.aiGroupAssembler.BuildGroupTemplateInfo(ctx, getResp.GetList()[0])
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupTemplateInfo BuildGroupTemplateInfo template(%+v) err: %v", getResp.GetList()[0], err)
		return nil, err
	}

	resp.Info = info
	return resp, nil
}

func (s *Server) getFollowStatus(ctx context.Context, uid uint32, uids []uint32) (map[uint32]bool, error) {

	repeatedMap := make(map[uint32]bool)
	repeatedUids := make([]uint32, 0, len(uids))
	for _, id := range uids {
		if !repeatedMap[id] && id != uid {
			repeatedMap[id] = true
			repeatedUids = append(repeatedUids, id)
		}
	}
	if len(repeatedUids) == 0 {
		return nil, nil
	}
	stateMap := make(map[uint32]bool, len(repeatedUids))
	//get follow status
	followingMap, _, err := s.clients.FriendshipClient.BatchGetBiFollowing(ctx, uid, repeatedUids, true, false)
	if err != nil {
		return stateMap, err
	}
	for posterId, following := range followingMap {
		if following != nil {
			stateMap[posterId] = true
		}
	}
	return stateMap, nil
}

func (s *Server) GetGroupMemberList(ctx context.Context, req *api.GetGroupMemberListRequest) (*api.GetGroupMemberListResponse, error) {
	resp := new(api.GetGroupMemberListResponse)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	listReq := &aigc_group.GetGroupMemberListRequest{
		GroupId: req.GetGroupId(),
	}
	listResp, err := s.clients.AigcGroup.GetGroupMemberList(ctx, listReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupMemberList GetGroupMemberList req(%+v) err: %v", listReq, err)
		return nil, err
	}
	if len(listResp.GetList()) == 0 {
		return resp, nil
	}

	templateInfoResp, err := s.clients.AigcGroup.GetTemplateByGroupId(ctx, &aigc_group.GetTemplateByGroupIdRequest{
		GroupId: req.GetGroupId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMultiGroupUserRoleInfo GetGroupTemplateByIds err:%v, req:%s", err, req.String())
		return resp, err
	}

	activeGroupResp, err := s.clients.AigcGroup.BatchGetActiveGroup(ctx, &aigc_group.BatchGetActiveGroupRequest{
		IdList: []uint32{req.GetGroupId()},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupMemberList BatchGetActiveGroup groupId(%d) err: %v", req.GetGroupId(), err)
		return nil, err
	}

	memberActiveTimeMap := make(map[uint32]int64, len(activeGroupResp.GetGroups()[req.GetGroupId()].GetMembers()))
	for _, member := range activeGroupResp.GetGroups()[req.GetGroupId()].GetMembers() {
		memberActiveTimeMap[member.GetUid()] = member.GetLastActiveAt()
	}

	isMultiGroup := false
	userRoleConfigMap := make(map[uint32]*aigc_group.PlayRole)
	if templateInfoResp.GetGroupTemplate().GetGroupType() == aigc_group.GroupType_GROUP_TYPE_SINGLE_USER {
		list, err := s.aiGroupAssembler.BuildGroupMemberList(ctx, listResp.GetList(), nil, nil, nil, uid, isMultiGroup, memberActiveTimeMap)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGroupMemberList BuildGroupMemberList members(%+v) err: %v", listResp.GetList(), err)
			return nil, err
		}
		resp.List = list
	} else if templateInfoResp.GetGroupTemplate().GetGroupType() == aigc_group.GroupType_GROUP_TYPE_MULTI_USER ||
		templateInfoResp.GetGroupTemplate().GetGroupType() == aigc_group.GroupType_GROUP_TYPE_MULTI_USER_SCRIPT {
		if templateInfoResp.GetGroupTemplate() == nil {
			log.ErrorWithCtx(ctx, "GetMultiGroupUserRoleInfo GetGroupTemplateByIds empty, req:%s", req.String())
			return resp, protocol.NewExactServerError(nil, status.ErrAigcGroupTemplateNotFound)
		}
		isMultiGroup = true
		userRoleSelectResp, err := s.clients.AigcGroup.GetMultiGroupUserRoleInfo(ctx, &aigc_group.GetMultiGroupUserRoleInfoRequest{
			GroupId: req.GetGroupId(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGroupMemberList GetMultiGroupUserRoleInfo err:%v, req:%s", err, req.String())
			return resp, err
		}

		for _, playRole := range templateInfoResp.GetGroupTemplate().GetScriptInfo().GetPlayRoles() {
			userRoleConfigMap[playRole.GetId()] = playRole
			//log.InfoWithCtx(ctx, "==test, playRole:%+v", playRole)
		}

		var groupUids []uint32
		for _, info := range listResp.GetList() {
			if info.GetType() == aigc_group.GroupMemberType_GROUP_MEMBER_TYPE_USER {
				groupUids = append(groupUids, info.GetId())
			}
		}
		followMap := make(map[uint32]bool, len(groupUids))
		if len(groupUids) != 0 {
			followMap, err = s.getFollowStatus(ctx, uid, groupUids)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetMultiGroupUserRoleInfo getFollowStatus err:%v, req:%s", err, req.String())
			}
		}
		//log.InfoWithCtx(ctx, "==test, followMap:%+v", followMap)

		list, err := s.aiGroupAssembler.BuildGroupMemberList(ctx, listResp.GetList(), userRoleSelectResp.GetUserSelectRoleMap(), userRoleConfigMap, followMap, uid, isMultiGroup, memberActiveTimeMap)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGroupMemberList BuildGroupMemberList members(%+v) err: %v", listResp.GetList(), err)
			return nil, err
		}
		resp.List = list
	}
	if isMultiGroup {
		resp.DefaultPrologueList = assembler.ConvertDefaultPrologueListToPb(templateInfoResp.GetGroupTemplate().GetDefaultPrologues())
	}
	log.InfoWithCtx(ctx, "GetGroupMemberList req:%s, resp:%s", req.String(), resp.String())
	return resp, nil
}

func (s *Server) GetFilterItem(ctx context.Context, req *api.GetScriptFilterItemRequest) (*api.GetScriptFilterItemResponse, error) {
	resp := new(api.GetScriptFilterItemResponse)

	resp.List = config.GetAigcHttpLogicConfig().GetScriptFilterItems()

	return resp, nil
}

func (s *Server) GetScriptList(ctx context.Context, req *api.GetScriptListRequest) (*api.GetScriptListResponse, error) {
	resp := new(api.GetScriptListResponse)

	listResp, err := s.clients.AigcGroup.GetScriptListByFilter(ctx, &aigc_group.GetScriptListByFilterRequest{
		Sex:    handleFilterSex(req.GetFilterItems()),
		Cursor: req.GetCursor(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetScriptList GetScriptListByFilter req(%+v) err: %v", req, err)
		return resp, err
	}
	resp.NextCursor = listResp.GetNextCursor()
	info, err := s.aiGroupAssembler.BuildScriptInfoList(ctx, listResp.GetTemplates())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetScriptList BuildScriptInfoList req err: %v", req.String(), err)
		return nil, err
	}
	resp.List = info
	log.InfoWithCtx(ctx, "GetScriptList finish req:%s, nextCursor:%s len(resp):%d", req.String(), resp.GetNextCursor(), len(resp.GetList()))
	return resp, nil
}

func handleFilterSex(filterItems []*api.FilterItem) aigc_group.GetScriptListByFilterRequest_SexOpt {
	if len(filterItems) == 0 {
		return aigc_group.GetScriptListByFilterRequest_SEX_ALL
	}

	for _, item := range filterItems {
		if item.GetType() == api.FilterItem_FILTER_TYPE_SEX_ALL {
			return aigc_group.GetScriptListByFilterRequest_SEX_ALL
		} else if item.GetType() == api.FilterItem_FILTER_TYPE_SEX_FEMALE {
			return aigc_group.GetScriptListByFilterRequest_SEX_FEMALE
		} else if item.GetType() == api.FilterItem_FILTER_TYPE_SEX_MALE {
			return aigc_group.GetScriptListByFilterRequest_SEX_MALE
		} else {
			continue
		}
	}
	return aigc_group.GetScriptListByFilterRequest_SEX_ALL
}

func (s *Server) GetScriptHotBanner(ctx context.Context, req *api.GetScriptHotBannerRequest) (*api.GetScriptHotBannerResponse, error) {
	resp := new(api.GetScriptHotBannerResponse)

	listResp, err := s.clients.AigcGroup.GetAllHotBanner(ctx, &aigc_group.GetAllHotBannerRequest{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetScriptHotBanner GetScriptHotBanner req(%+v) err: %v", req, err)
		return resp, err
	}
	if len(listResp.GetList()) == 0 {
		log.InfoWithCtx(ctx, "GetScriptHotBanner empty req:%s len(resp):%d", req.String(), len(resp.GetList()))
		return resp, nil
	}
	bannerList, err := s.aiGroupAssembler.BuildHotBannerList(ctx, listResp.GetList())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetScriptHotBanner BuildHotBannerList err:%v, req:%s", err, req.String())
		return resp, err
	}
	resp.BannerTitle = config.GetAigcHttpLogicConfig().GetBannerTitle()
	resp.List = bannerList
	log.InfoWithCtx(ctx, "GetScriptHotBanner finish req:%s len(resp):%d", req.String(), len(resp.GetList()))
	return resp, nil
}

func (s *Server) StartScriptMatch(ctx context.Context, req *api.StartScriptMatchRequest) (*api.StartScriptMatchResponse, error) {
	switch req.GetMode() {
	case api.StartScriptMatchRequest_MATCH_MODE_SPECIFY_SCRIPT:
		if req.GetScriptId() == 0 {
			return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "未指定剧本")
		}
	case api.StartScriptMatchRequest_MATCH_MODE_QUICK_PLAY:
	default:
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不支持的匹配模式")
	}

	uid := metainfo.GetServiceInfo(ctx).UserID()

	user, rpcErr := s.clients.Account.GetUserByUid(ctx, uid)
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "StartScriptMatch GetUserByUid uid(%d) err: %v", uid, rpcErr)
		return nil, rpcErr
	}

	joinReq := &aigc_group.JoinMatchRequest{
		Player: &aigc_group.JoinMatchRequest_Player{
			Uid: user.GetUid(),
			Sex: user.GetSex(),
		},
		TemplateId: req.GetScriptId(),
	}
	joinResp, err := s.clients.AigcGroup.JoinMatch(ctx, joinReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartScriptMatch JoinMatch req(%+v) err: %v", req, err)
		return nil, err
	}

	resp := &api.StartScriptMatchResponse{
		Strategy: uint32(joinResp.GetStrategy()),
	}
	switch joinResp.GetStrategy() {
	case aigc_group.MatchStrategy_MATCH_STRATEGY_POOL:
		randPlayerReq := &aigc_group.BatchGetRandMatchPlayerRequest{
			TemplId: []uint32{joinResp.GetTemplateId()},
			Limit:   6,
		}
		randPlayerResp, err := s.clients.AigcGroup.BatchGetRandMatchPlayer(ctx, randPlayerReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "StartScriptMatch BatchGetRandMatchPlayer req(%+v)", randPlayerReq)
			return nil, err
		}

		var list []*aigc_group.MatchPlayer
		if len(randPlayerResp.GetPlayers()) > 0 {
			list = randPlayerResp.GetPlayers()[joinResp.GetTemplateId()].GetList()
		}

		if resp.Users, err = s.aiGroupAssembler.BuildStartMatchUserList(ctx, list); err != nil {
			log.ErrorWithCtx(ctx, "StartScriptMatch BuildStartMatchUserList err: %v", err)
			return nil, err
		}
	case aigc_group.MatchStrategy_MATCH_STRATEGY_GROUP:
		resp.GroupId = joinResp.GetGroupId()
	default:
		log.WarnWithCtx(ctx, "StartScriptMatch invalid MatchStrategy: %d", joinResp.GetStrategy())
		return nil, nil
	}

	return resp, nil
}

func (s *Server) ConfirmScriptMatch(ctx context.Context, req *api.ConfirmScriptMatchRequest) (*api.ConfirmScriptMatchResponse, error) {
	if req.GetScriptMatchToken() == "" {
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "缺少匹配确认token")
	}

	uid := metainfo.GetServiceInfo(ctx).UserID()

	confirmReq := &aigc_group.ConfirmMatchRequest{
		Uid:   uid,
		Token: req.GetScriptMatchToken(),
	}
	if _, err := s.clients.AigcGroup.ConfirmMatch(ctx, confirmReq); err != nil {
		log.ErrorWithCtx(ctx, "ConfirmScriptMatch req(%+v) err: %v", confirmReq, err)
		return nil, err
	}

	resp := new(api.ConfirmScriptMatchResponse)
	return resp, nil
}

func (s *Server) CancelScriptMatch(ctx context.Context, req *api.CancelScriptMatchRequest) (*api.CancelScriptMatchResponse, error) {
	uid := metainfo.GetServiceInfo(ctx).UserID()

	cancelReq := &aigc_group.CancelMatchRequest{
		Uid: uid,
	}
	if _, err := s.clients.AigcGroup.CancelMatch(ctx, cancelReq); err != nil {
		log.ErrorWithCtx(ctx, "CancelScriptMatch req(%+v) err: %v", cancelReq, err)
		return nil, err
	}

	resp := new(api.CancelScriptMatchResponse)
	return resp, nil
}
