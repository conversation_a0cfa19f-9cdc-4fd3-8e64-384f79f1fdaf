package internal

import (
	"context"
	aigc_common "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_push "golang.52tt.com/protocol/services/aigc/aigc-push"
	aigc_soulmate_middle "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	aigc_trigger "golang.52tt.com/protocol/services/aigc/aigc-trigger"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/audit"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	account "golang.52tt.com/protocol/services/account-go"
	api "golang.52tt.com/protocol/services/aigc/aigc-http-logic"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	"golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner"
	"golang.52tt.com/services/aigc/aigc-http-logic/internal/mgr"
)

func (s *Server) CreateAIPartner(ctx context.Context, req *api.CreateAIPartnerRequest) (*api.CreateAIPartnerResponse, error) {
	log.InfoWithCtx(ctx, "CreateAIPartner req: %+v", req)
	resp := &api.CreateAIPartnerResponse{
		NameResult:     api.AuditResult_AuditResultPass,
		CallNameResult: api.AuditResult_AuditResultPass,
	}

	uid := metainfo.GetServiceInfo(ctx).UserID()
	reqPartner := req.GetPartner()

	user, rpcErr := s.clients.Account.GetUserByUid(ctx, uid)
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "CreateAIPartner GetUserByUid uid(%d) err: %v", uid)
		return nil, rpcErr
	}
	if user == nil {
		log.ErrorWithCtx(ctx, "CreateAIPartner user %d not found", uid)
		return nil, protocol.NewExactServerError(nil, status.ErrAccountNotExist)
	}

	if aigc_soulmate.AIPartnerSource(reqPartner.GetSource()) == aigc_soulmate.AIPartnerSource_AIPartnerSourceDeRole {
		switch account.USER_SEX(user.GetSex()) {
		case account.USER_SEX_USER_SEX_FEMALE:
			reqPartner.RoleId = 6
		default:
			reqPartner.RoleId = 2
		}
	}
	if reqPartner.GetRoleId() == 0 {
		log.WarnWithCtx(ctx, "CreateAIPartner invalid req: %+v", req)
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请选择角色")
	}

	var scannedTexts []*mgr.ScannedText
	if reqPartner.GetName() != "" {
		scannedTexts = append(scannedTexts, &mgr.ScannedText{
			Text:  reqPartner.GetName(),
			Scene: audit.SCENE_CODE_AI_PARTNER_NAME,
			Callback: func(result api.AuditResult) {
				if result != api.AuditResult_AuditResultPass {
					resp.NameResult = api.AuditResult_AuditResultReject
					reqPartner.Name = "*****"
				}
			},
		})
	}
	if reqPartner.GetCallName() != "" {
		scannedTexts = append(scannedTexts, &mgr.ScannedText{
			Text:  reqPartner.GetCallName(),
			Scene: audit.SCENE_CODE_AI_PARTNER_NAME,
			Callback: func(result api.AuditResult) {
				if result != api.AuditResult_AuditResultPass {
					resp.CallNameResult = api.AuditResult_AuditResultReject
					reqPartner.CallName = "*****"
				}
			},
		})
	}
	if len(scannedTexts) > 0 {
		err := s.censorMgr.SyncScanTexts(ctx, &mgr.ScanTextsReq{
			User:  user,
			Texts: scannedTexts,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateAIPartner SyncScanTexts err: %v", err)
			return nil, err
		}
	}

	err := s.soulmateMgr.CreatePartner(ctx, reqPartner)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateAIPartner CreatePartner partner(%+v) err: %v", reqPartner, err)
		return nil, err
	}

	log.InfoWithCtx(ctx, "CreateAIPartner req(%+v) resp: %+v", req, resp)
	return resp, nil
}

func (s *Server) UpdateAIPartner(ctx context.Context, req *api.UpdateAIPartnerRequest) (*api.UpdateAIPartnerResponse, error) {
	log.InfoWithCtx(ctx, "UpdateAIPartner req: %+v", req)
	resp := &api.UpdateAIPartnerResponse{
		NameResult:     api.AuditResult_AuditResultPass,
		CallNameResult: api.AuditResult_AuditResultPass,
	}

	uid := metainfo.GetServiceInfo(ctx).UserID()

	reqPartner := req.GetPartner()
	if reqPartner.GetName() == "" && reqPartner.GetCallName() == "" {
		log.WarnWithCtx(ctx, "UpdateAIPartner invalid req: %+v", req)
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请求参数错误")
	}

	user, rpcErr := s.clients.Account.GetUserByUid(ctx, uid)
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "UpdateAIPartner GetUserByUid uid(%d) err: %v", uid)
		return nil, rpcErr
	}
	if user == nil {
		log.ErrorWithCtx(ctx, "UpdateAIPartner user %d not found", uid)
		return nil, protocol.NewExactServerError(nil, status.ErrAccountNotExist)
	}

	partner, err := s.soulmateMgr.GetPartner(ctx, reqPartner.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIPartner FirstByID id(%d) err: %v", reqPartner.GetId())
		return nil, err
	}
	if partner.GetUid() != uid {
		log.WarnWithCtx(ctx, "UpdateAIPartner user(%d) partner(%d) not found", uid, req.GetPartner().GetId())
		return nil, protocol.NewExactServerError(nil, status.ErrChatBotAiPartnerNotFound)
	}

	var scannedTexts []*mgr.ScannedText
	if reqPartner.GetName() != partner.GetName() {
		scannedTexts = append(scannedTexts, &mgr.ScannedText{
			Text:  reqPartner.GetName(),
			Scene: audit.SCENE_CODE_AI_PARTNER_NAME,
			Callback: func(result api.AuditResult) {
				if result != api.AuditResult_AuditResultPass {
					resp.NameResult = api.AuditResult_AuditResultReject
					reqPartner.Name = "*****"
				}
			},
		})
	}
	if reqPartner.GetCallName() != partner.GetCallName() {
		scannedTexts = append(scannedTexts, &mgr.ScannedText{
			Text:  reqPartner.GetCallName(),
			Scene: audit.SCENE_CODE_AI_PARTNER_NAME,
			Callback: func(result api.AuditResult) {
				if result != api.AuditResult_AuditResultPass {
					resp.CallNameResult = api.AuditResult_AuditResultReject
					reqPartner.CallName = "*****"
				}
			},
		})
	}
	if len(scannedTexts) > 0 {
		err := s.censorMgr.SyncScanTexts(ctx, &mgr.ScanTextsReq{
			User:  user,
			Texts: scannedTexts,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateAIPartner SyncScanTexts err: %v", err)
			return nil, err
		}
	}

	err = s.soulmateMgr.UpdatePartner(ctx, reqPartner)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIPartner UpdatePartner partner(%+v) err: %v", reqPartner, err)
		return nil, err
	}

	log.InfoWithCtx(ctx, "UpdateAIPartner req(%+v) resp: %+v", req, resp)
	return resp, nil
}

func (s *Server) SwitchAIPartnerSilent(ctx context.Context, req *api.SwitchAIPartnerSilentRequest) (*api.SwitchAIPartnerSilentResponse, error) {
	resp := new(api.SwitchAIPartnerSilentResponse)
	log.InfoWithCtx(ctx, "SwitchAIPartnerSilent req: %+v", req)

	uid := metainfo.GetServiceInfo(ctx).UserID()

	partner, err := s.soulmateMgr.GetPartner(ctx, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "SwitchAIPartnerSilent GetPartner id(%d) err: %v", req.GetId(), err)
		return nil, err
	}
	if partner.GetUid() != uid {
		log.WarnWithCtx(ctx, "SwitchAIPartnerSilent user(%d) partner(%d) not found", uid, req.GetId())
		return nil, protocol.NewExactServerError(nil, status.ErrChatBotAiPartnerNotFound)
	}

	err = s.soulmateMgr.SwitchPartnerSilent(ctx, req.GetId(), req.GetSilent())
	if err != nil {
		log.ErrorWithCtx(ctx, "SwitchAIPartnerSilent SwitchPartnerSilent uid(%d) id(%d) silent(%t) err: %v", uid, req.GetId(), req.GetSilent(), err)
		return nil, err
	}

	return resp, nil
}

func (s *Server) RebindAIPartnerRole(ctx context.Context, req *api.RebindAIPartnerRoleRequest) (*api.RebindAIPartnerRoleResponse, error) {
	resp := new(api.RebindAIPartnerRoleResponse)
	log.InfoWithCtx(ctx, "RebindAIPartnerRole req: %+v", req)

	uid := metainfo.GetServiceInfo(ctx).UserID()

	partner, err := s.soulmateMgr.GetPartner(ctx, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "RebindAIPartnerRole GetPartner id(%d) err: %v", req.GetId(), err)
		return nil, err
	}
	if partner.GetUid() != uid {
		log.WarnWithCtx(ctx, "RebindAIPartnerRole user(%d) partner(%d) not found", uid, req.GetId())
		return nil, protocol.NewExactServerError(nil, status.ErrChatBotAiPartnerNotFound)
	}

	err = s.soulmateMgr.RebindPartnerRole(ctx, req.GetId(), req.GetRoleId())
	if err != nil {
		log.ErrorWithCtx(ctx, "RebindAIPartnerRole RebindPartnerRole id(%d) roleId(%d) err: %v", req.GetId(), req.GetRoleId(), err)
		return nil, err
	}

	return resp, nil
}

func (s *Server) ReportAIPartnerChatting(ctx context.Context, req *api.ReportAIPartnerChattingRequest) (*api.ReportAIPartnerChattingResponse, error) {
	resp := new(api.ReportAIPartnerChattingResponse)
	log.InfoWithCtx(ctx, "ReportAIPartnerChatting req: %+v", req)

	uid := metainfo.GetServiceInfo(ctx).UserID()

	partner, err := s.soulmateMgr.GetPartner(ctx, req.GetPartnerId())
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportAIPartnerChatting GetPartner id(%d) err: %v", req.GetPartnerId(), err)
		return nil, err
	}
	if partner.GetUid() != uid {
		log.WarnWithCtx(ctx, "ReportAIPartnerChatting user(%d) partner(%d) not found", uid, req.GetPartnerId())
		return nil, protocol.NewExactServerError(nil, status.ErrChatBotAiPartnerNotFound)
	}

	rcmdReq := &rcmd_ai_partner.UserEnterChattingNotifyReq{
		Uid:         uid,
		AiPartnerId: partner.GetId(),
		AiRoleType:  rcmd_ai_partner.AIRoleType(partner.GetRole().GetType()),
	}
	_, err = s.clients.RcmdPartner.UserEnterChattingNotify(ctx, rcmdReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportAIPartnerChatting UserEnterChattingNotify req(%+v) err: %v", rcmdReq, err)
		return nil, err
	}

	return resp, nil
}

func (s *Server) DeleteAIPartner(ctx context.Context, req *api.DeleteAIPartnerRequest) (*api.DeleteAIPartnerResponse, error) {
	resp := new(api.DeleteAIPartnerResponse)
	log.InfoWithCtx(ctx, "DeleteAIPartner req: %+v", req)

	uid := metainfo.GetServiceInfo(ctx).UserID()

	partner, err := s.soulmateMgr.GetPartner(ctx, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteAIPartner GetPartner id(%d) err: %v", req.GetId(), err)
		return nil, err
	}
	if partner.GetUid() != uid {
		log.WarnWithCtx(ctx, "DeleteAIPartner user(%d) partner(%d) not found", uid, req.GetId())
		return nil, protocol.NewExactServerError(nil, status.ErrChatBotAiPartnerNotFound)
	}

	_, err = s.clients.Soulmate.DeleteAIPartner(ctx, &aigc_soulmate.DeleteAIPartnerReq{Id: req.GetId()})
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteAIPartner DeleteAIPartner id(%d) err: %v", req.GetId(), err)
		return nil, err
	}

	return resp, nil
}

func (s *Server) GetAIPartner(ctx context.Context, req *api.GetAIPartnerRequest) (*api.GetAIPartnerResponse, error) {
	resp := new(api.GetAIPartnerResponse)
	log.DebugWithCtx(ctx, "GetAIPartner req: %+v", req)

	uid := metainfo.GetServiceInfo(ctx).UserID()

	getReq := &aigc_soulmate.GetUserAIPartnerReq{
		Uid:      uid,
		RoleId:   req.GetRoleId(),
		RoleType: aigc_soulmate.AIRoleType(req.GetRoleType()),
	}
	getResp, err := s.clients.Soulmate.GetUserAIPartner(ctx, getReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIPartner GetUserAIPartner req(%+v) err: %v", getReq, err)
		return nil, err
	}
	if getResp.GetPartner() == nil {
		return resp, nil
	}

	resp.Partner, err = s.aiPartnerAssembler.BuildAIPartner(ctx, getResp.GetPartner())
	return resp, err
}

func (s *Server) GetPetPartner(ctx context.Context, req *api.GetPetPartnerRequest) (*api.GetPetPartnerReponse, error) {
	resp := new(api.GetPetPartnerReponse)

	uid := metainfo.GetServiceInfo(ctx).UserID()

	getReq := &aigc_soulmate.GetUserAIPartnerReq{
		Uid:      uid,
		RoleType: aigc_soulmate.AIRoleType_AIRoleTypePet,
	}
	getResp, err := s.clients.Soulmate.GetUserAIPartner(ctx, getReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPetPartner GetUserAIPartner req(%+v) err: %v", getReq, err)
		return nil, err
	}
	if getResp.GetPartner() == nil {
		return resp, nil
	}

	resp.Partner, err = s.aiPartnerAssembler.BuildPetPartner(ctx, getResp.GetPartner())
	return resp, err
}

func (s *Server) GetChattingPartnerList(ctx context.Context, req *api.GetChattingPartnerListRequest) (*api.GetChattingPartnerListResponse, error) {
	resp := new(api.GetChattingPartnerListResponse)
	log.DebugWithCtx(ctx, "GetChattingPartnerList req: %+v", req)

	uid := metainfo.GetServiceInfo(ctx).UserID()

	getReq := &aigc_soulmate.GetUserAIPartnerListReq{
		Uid: uid,
		RoleTypes: []aigc_soulmate.AIRoleType{
			aigc_soulmate.AIRoleType_AIRoleTypePartner,
			aigc_soulmate.AIRoleType_AIRoleTypeGame,
		},
	}
	getResp, err := s.clients.Soulmate.GetUserAIPartnerList(ctx, getReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChattingPartnerList GetUserAIPartnerList req(%+v) err: %v", getReq, err)
		return nil, err
	}

	resp.List, err = s.aiPartnerAssembler.BuildChattingPartnerList(ctx, getResp.GetPartnerList()...)
	return resp, err
}

func (s *Server) GetChatNum(ctx context.Context, req *api.GetChatNumRequest) (*api.GetChatNumResponse, error) {
	uid := metainfo.GetServiceInfo(ctx).UserID()
	out := &api.GetChatNumResponse{}
	curNumRsp, err := s.clients.AigcCommon.BatGetCurSentenceCount(ctx, &aigc_common.BatGetCurSentenceCountRequest{
		Uid: uid,
		SentenceType: []uint32{
			uint32(aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_CUR_DAY),
		},
		BusinessType:            uint32(aigc_trigger.BusinessType_BUSINESS_TYPE_SINGLE_CHAT),
		RoleType:                uint32(aigc_soulmate.AIRoleType_AIRoleTypeGame),
		NeedAvailableExtraCount: false,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChatNum BatGetCurSentenceCount uid(%d) err: %v", uid, err)
		return out, err

	}
	tipsCountRsp, err := s.clients.AigcSoulmateMiddle.GetTipsCount(ctx, &aigc_soulmate_middle.GetTipsCountRequest{
		Uid:          uid,
		BusinessType: uint32(aigc_trigger.BusinessType_BUSINESS_TYPE_SINGLE_CHAT),
		RoleType:     uint32(aigc_soulmate.AIRoleType_AIRoleTypeGame),
		TipType:      uint32(aigc_push.TipType_TIP_TYPE_CUR_DAY),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChatNum GetTipsCount uid(%d) err: %v", uid, err)
		return out, err
	}
	out.CurDayCfgNum = tipsCountRsp.GetCurDayCfgNum()
	out.AvailableExtraNum = tipsCountRsp.GetExtraAvailableNum()
	out.CurDayUsedNum = curNumRsp.GetCurNumMap()[uint32(aigc_soulmate_middle.SentenceType_SENTENCE_TYPE_CUR_DAY)]
	log.InfoWithCtx(ctx, "GetChatNum success uid:%d resp:%s", uid, out.String())
	return out, nil
}
