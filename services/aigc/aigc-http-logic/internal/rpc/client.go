package rpc

import (
	"context"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/photo_album_go"

	"gitlab.ttyuyin.com/tyr/tt-ecosystem/grpc/client/interceptor"
	"google.golang.org/grpc"

	account_go "golang.52tt.com/clients/account-go"
	censoring_proxy "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/clients/ugc/friendship"
	"golang.52tt.com/pkg/abtest"
	"golang.52tt.com/pkg/log"
	aigc_account_middle "golang.52tt.com/protocol/services/aigc/aigc-account-middle"
	aigc_common "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_game "golang.52tt.com/protocol/services/aigc/aigc-game"
	aigc_group "golang.52tt.com/protocol/services/aigc/aigc-group"
	aigc_intimacy "golang.52tt.com/protocol/services/aigc/aigc-intimacy"
	aigc_search "golang.52tt.com/protocol/services/aigc/aigc-search"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	aigc_soulmate_middle "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle"
	rcmd_business "golang.52tt.com/protocol/services/rcmd/business_ai_partner"
	rcmd_cmd "golang.52tt.com/protocol/services/rcmd/cmd_gateway"
	rcmd_game_character "golang.52tt.com/protocol/services/rcmd/game_character"
	rcmd_partner "golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner"
	rcmd_role "golang.52tt.com/protocol/services/rcmd/rcmd_partner_role"
	risk_mng_api "golang.52tt.com/protocol/services/risk-mng-api"
)

type Clients struct {
	Account    account_go.IClient
	Soulmate   aigc_soulmate.AigcSoulmateClient
	Censoring  censoring_proxy.IClient
	RiskMngApi risk_mng_api.RiskMngApiClient
	ABTest     abtest.IABTestClient

	RcmdCmd           rcmd_cmd.CmdGatewayClient
	RcmdRole          rcmd_role.RCMDPartnerRoleClient
	RcmdPartner       rcmd_partner.RCMDAIPartnerClient
	RcmdBusiness      rcmd_business.BusinessAIPartnerClient
	RcmdGameCharacter rcmd_game_character.RCMDGameCharacterClient

	AigcGame         aigc_game.AigceGameClient
	AigcGroup        aigc_group.AigcGroupClient
	AigcSearch       aigc_search.AigcSearchClient
	AigcCommon       aigc_common.AigcCommonClient
	AigcIntimacy     aigc_intimacy.AigcIntimacyClient
	FriendshipClient friendship.IClient

	AigcSoulmateMiddle aigc_soulmate_middle.AigcSoulmateMiddleClient
	AigcAccountMiddle  aigc_account_middle.AigcAccountMiddleClient
	PhotoAlbumClient   photo_album_go.PhotoAlbumGoClient
}

const abtestUrl = "http://abtestlogic.commonservice.svc.cluster.local:8000/AbtestLogicService/GetUsersAbtestByTag"

func NewClients() *Clients {
	c := new(Clients)

	var (
		err error
		ctx = context.Background()
	)

	c.Account, err = account_go.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients account_go.NewClient err: %v", err)
	}

	c.Soulmate, err = aigc_soulmate.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients aigc_soulmate.NewClient err: %v", err)
	}

	c.Censoring = censoring_proxy.NewClient()

	c.RiskMngApi, err = risk_mng_api.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClients risk_mng_api.NewClient err: %v", err)
	}

	c.ABTest = abtest.NewABTestClient(abtestUrl, uint32(abtest.APPID_TTyuyin), "")

	c.RcmdCmd, err = rcmd_cmd.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClient rcmd_cmd.NewClient err: %v", err)
	}

	c.RcmdRole, err = rcmd_role.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClient rcmd_role.NewClient err: %v", err)
	}

	c.RcmdPartner, err = rcmd_partner.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClient rcmd_partner.NewClient err: %v", err)
	}

	c.RcmdBusiness, err = rcmd_business.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClient rcmd_business.NewClient err: %v", err)
	}

	c.RcmdGameCharacter, err = rcmd_game_character.NewClientTo(ctx, "rcmd-game-character.rcmd-tt.svc:8000", grpc.WithChainUnaryInterceptor(interceptor.NoneStatusAdapterInterceptor()))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClient rcmd_game_character.NewClient err: %v", err)
	}

	if c.AigcGame, err = aigc_game.NewClient(ctx); err != nil {
		log.ErrorWithCtx(ctx, "NewClient aigc_game.NewClient err: %v", err)
	}

	c.AigcGroup, err = aigc_group.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClient aigc_group.NewClient err: %v", err)
	}

	c.AigcSearch, err = aigc_search.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClient aigc_search.NewClient err: %v", err)
	}

	c.AigcCommon, err = aigc_common.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClient aigc_common.NewClient err: %v", err)
	}

	c.AigcIntimacy, err = aigc_intimacy.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClient aigc_intimacy.NewClient err: %v", err)
	}

	c.FriendshipClient, err = friendship.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClient friendship.NewClient err: %v", err)
	}

	c.AigcSoulmateMiddle, err = aigc_soulmate_middle.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClient aigc_soulmate_middle.NewClient err: %v", err)
	}
	c.AigcAccountMiddle, err = aigc_account_middle.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClient aigc_account_middle.NewClient err: %v", err)
	}
	c.PhotoAlbumClient, err = photo_album_go.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewClient photo_album_go.NewClient err: %v", err)
	}
	return c
}
