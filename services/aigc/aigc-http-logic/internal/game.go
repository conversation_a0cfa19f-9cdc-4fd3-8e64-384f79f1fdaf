package internal

import (
	"context"
	"strings"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	aigc_game "golang.52tt.com/protocol/services/aigc/aigc-game"
	api "golang.52tt.com/protocol/services/aigc/aigc-http-logic"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	"golang.52tt.com/protocol/services/rcmd/game_character"
	config "golang.52tt.com/services/aigc/aigc-http-logic/internal/config/aigc_http_logic"
	"golang.52tt.com/services/aigc/aigc-http-logic/internal/mgr"
)

func (s *Server) CreateInteractiveGame(ctx context.Context, req *api.CreateInteractiveGameRequest) (*api.CreateInteractiveGameResponse, error) {
	resp := &api.CreateInteractiveGameResponse{
		TitleAuditResult:    api.AuditResult_AuditResultPass,
		DescAuditResult:     api.AuditResult_AuditResultPass,
		PrologueAuditResult: api.AuditResult_AuditResultPass,
	}
	log.InfoWithCtx(ctx, "CreateInteractiveGame svcInfo(%+v) req: %+v", metainfo.GetServiceInfo(ctx), req)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.WarnWithCtx(ctx, "CreateInteractiveGame miss uid")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请重新登录")
	}

	reqGame := req.GetGame()

	// 风控检查 + 送审
	exposure := aigc_soulmate.InteractiveGameExposure_InteractiveGameExposureExposed
	riskParam := &mgr.InteractiveGameRiskParam{
		CreatedAt: time.Now().Unix(),

		RoleID:  reqGame.GetRoleId(),
		TopicID: reqGame.GetTopicId(),

		Desc:     reqGame.GetDesc(),
		Title:    reqGame.GetTitle(),
		Prologue: reqGame.GetPrologue(),

		State:    reqGame.GetState(),
		Exposure: uint32(exposure),

		ReviewTexts: []mgr.ScannedText{
			{
				Text: reqGame.GetDesc(),
				Callback: func(result api.AuditResult) {
					resp.DescAuditResult = result
				},
			},
			{
				Text: reqGame.GetTitle(),
				Callback: func(result api.AuditResult) {
					resp.TitleAuditResult = result
				},
			},
			{
				Text: reqGame.GetPrologue(),
				Callback: func(result api.AuditResult) {
					resp.PrologueAuditResult = result
				},
			},
		},
	}
	var err error
	if exposure, err = s.riskChecker.CheckInteractiveGame(ctx, riskParam); err != nil {
		log.ErrorWithCtx(ctx, "CreateInteractiveGame CheckInteractiveGame param(%+v) err: %v", riskParam, err)
		return nil, err
	}
	if resp.GetTitleAuditResult() != api.AuditResult_AuditResultPass ||
		resp.GetDescAuditResult() != api.AuditResult_AuditResultPass ||
		resp.GetPrologueAuditResult() != api.AuditResult_AuditResultPass {
		return resp, nil
	}

	// 创建
	createReq := &aigc_soulmate.CreateInteractiveGameReq{
		Game: &aigc_soulmate.CreateInteractiveGameReq_InteractiveGame{
			Source:   aigc_soulmate.InteractiveGameSource_InteractiveGameSourceUser,
			RoleId:   reqGame.GetRoleId(),
			TopicId:  reqGame.GetTopicId(),
			Title:    reqGame.GetTitle(),
			Desc:     reqGame.GetDesc(),
			Prologue: reqGame.GetPrologue(),
			State:    aigc_soulmate.InteractiveGameState(reqGame.GetState()),
			Exposure: exposure,
		},
	}
	createResp, err := s.clients.Soulmate.CreateInteractiveGame(ctx, createReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateInteractiveGame CreateInteractiveGame req(%+v) err: %v", createReq, err)
		return nil, err
	}

	resp.Id = createResp.GetId()

	log.InfoWithCtx(ctx, "CreateInteractiveGame req(%+v) resp: %+v", req, resp)
	return resp, nil
}

func (s *Server) UpdateInteractiveGame(ctx context.Context, req *api.UpdateInteractiveGameRequest) (*api.UpdateInteractiveGameResponse, error) {
	resp := &api.UpdateInteractiveGameResponse{
		TitleAuditResult:    api.AuditResult_AuditResultPass,
		DescAuditResult:     api.AuditResult_AuditResultPass,
		PrologueAuditResult: api.AuditResult_AuditResultPass,
	}
	log.InfoWithCtx(ctx, "UpdateInteractiveGame svcInfo(%+v) req: %+v", metainfo.GetServiceInfo(ctx), req)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.ErrorWithCtx(ctx, "UpdateInteractiveGame miss uid")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请重新登录")
	}

	reqGame := req.GetGame()

	game, err := s.soulmateMgr.GetInteractiveGame(ctx, reqGame.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateInteractiveGame GetInteractiveGame id(%s) err: %v", reqGame.GetId(), err)
		return nil, err
	}
	if game == nil || game.GetUid() != uid {
		log.WarnWithCtx(ctx, "UpdateInteractiveGame GetInteractiveGame id(%s) not found", reqGame.GetId())
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请选择需要修改的互动玩法")
	}

	// 风控检查 + 送审
	exposure := game.GetExposure()
	riskParam := &mgr.InteractiveGameRiskParam{
		ID:        game.GetId(),
		CreatedAt: game.GetCreatedAt(),

		RoleID:  game.GetRoleId(),
		TopicID: reqGame.GetTopicId(),

		Desc:     reqGame.GetDesc(),
		Title:    reqGame.GetTitle(),
		Prologue: reqGame.GetPrologue(),

		State:    reqGame.GetState(),
		Exposure: uint32(exposure),
	}
	if reqGame.GetTitle() != game.GetTitle() {
		riskParam.ReviewTexts = append(riskParam.ReviewTexts, mgr.ScannedText{
			Text: reqGame.GetTitle(),
			Callback: func(result api.AuditResult) {
				resp.TitleAuditResult = result
			},
		})
	}
	if reqGame.GetDesc() != game.GetDesc() {
		riskParam.ReviewTexts = append(riskParam.ReviewTexts, mgr.ScannedText{
			Text: reqGame.GetDesc(),
			Callback: func(result api.AuditResult) {
				resp.DescAuditResult = result
			},
		})
	}
	if reqGame.GetPrologue() != game.GetPrologue() {
		riskParam.ReviewTexts = append(riskParam.ReviewTexts, mgr.ScannedText{
			Text: reqGame.GetPrologue(),
			Callback: func(result api.AuditResult) {
				resp.PrologueAuditResult = result
			},
		})
	}
	if exposure, err = s.riskChecker.CheckInteractiveGame(ctx, riskParam); err != nil {
		log.ErrorWithCtx(ctx, "UpdateInteractiveGame CheckInteractiveGame param(%+v) err: %v", riskParam, err)
		return nil, err
	}
	if resp.GetTitleAuditResult() != api.AuditResult_AuditResultPass ||
		resp.GetDescAuditResult() != api.AuditResult_AuditResultPass ||
		resp.GetPrologueAuditResult() != api.AuditResult_AuditResultPass {
		return resp, nil
	}

	updateReq := &aigc_soulmate.UpdateInteractiveGameReq{
		Game: &aigc_soulmate.UpdateInteractiveGameReq_InteractiveGame{
			Id:       reqGame.GetId(),
			TopicId:  reqGame.GetTopicId(),
			Title:    reqGame.GetTitle(),
			Desc:     reqGame.GetDesc(),
			Prologue: reqGame.GetPrologue(),
			State:    aigc_soulmate.InteractiveGameState(reqGame.GetState()),
			Exposure: exposure,
		},
	}
	if _, err := s.clients.Soulmate.UpdateInteractiveGame(ctx, updateReq); err != nil {
		log.ErrorWithCtx(ctx, "UpdateInteractiveGame UpdateInteractiveGame req(%+v) err: %v", updateReq, err)
		return nil, err
	}

	log.InfoWithCtx(ctx, "UpdateInteractiveGame req(%+v) resp: %+v", req, resp)
	return resp, nil
}

func (s *Server) DeleteInteractiveGame(ctx context.Context, req *api.DeleteInteractiveGameRequest) (*api.DeleteInteractiveGameResponse, error) {
	resp := new(api.DeleteInteractiveGameResponse)
	log.InfoWithCtx(ctx, "DeleteInteractiveGame req: %+v", req)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.ErrorWithCtx(ctx, "DeleteInteractiveGame miss uid")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请重新登录")
	}

	game, err := s.soulmateMgr.GetInteractiveGame(ctx, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteInteractiveGame GetInteractiveGame id(%s) err: %v", req.GetId(), err)
		return nil, err
	}
	if game == nil || game.GetUid() != uid {
		log.WarnWithCtx(ctx, "DeleteInteractiveGame GetInteractiveGame id(%s) not found", req.GetId())
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请选择需要删除的互动玩法")
	}

	deleteReq := &aigc_soulmate.BatchDeleteInteractiveGameReq{
		IdList: []string{req.GetId()},
	}
	if _, err := s.clients.Soulmate.BatchDeleteInteractiveGame(ctx, deleteReq); err != nil {
		log.ErrorWithCtx(ctx, "DeleteInteractiveGame BatchDeleteInteractiveGame req(%+v) err: %v", deleteReq, err)
		return nil, err
	}

	log.InfoWithCtx(ctx, "DeleteInteractiveGame req(%+v) finished", req)
	return resp, nil
}

func (s *Server) GetInteractiveGameList(ctx context.Context, req *api.GetInteractiveGameListRequest) (*api.GetInteractiveGameListResponse, error) {
	resp := new(api.GetInteractiveGameListResponse)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.ErrorWithCtx(ctx, "GetInteractiveGameList miss uid")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请重新登录")
	}

	listReq := &aigc_soulmate.GetUserInteractiveGameListReq{
		Uid: uid,
	}
	listResp, err := s.clients.Soulmate.GetUserInteractiveGameList(ctx, listReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetInteractiveGameList GetUserInteractiveGameList req(%+v) err: %v", listReq, err)
		return nil, err
	}

	for _, game := range listResp.GetList() {
		resp.List = append(resp.List, &api.InteractiveGame{
			Id:       game.GetId(),
			RoleId:   game.GetRoleId(),
			TopicId:  game.GetTopicId(),
			Title:    game.GetTitle(),
			Desc:     game.GetDesc(),
			Prologue: game.GetPrologue(),
			State:    uint32(game.GetState()),
		})
	}

	resp.List, err = s.interactiveGameAssembler.BuildInteractiveGameList(ctx, listResp.GetList()...)
	return resp, err
}

func (s *Server) StartGame(ctx context.Context, req *api.StartGameRequest) (*api.StartGameResponse, error) {
	resp := new(api.StartGameResponse)
	log.InfoWithCtx(ctx, "StartGame req: %+v", req)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.ErrorWithCtx(ctx, "StartGame miss uid")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请重新登录")
	}

	if req.GetRoleId() == 0 || req.GetGameId() == "" {
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	role, err := s.soulmateMgr.GetRole(ctx, req.GetRoleId())
	if err != nil {
		log.ErrorWithCtx(ctx, "StartGame GetRole roleId(%d) err: %v", req.GetRoleId(), err)
		return nil, err
	}
	if role == nil {
		log.WarnWithCtx(ctx, "StartGame GetRole roleId(%d) not found", req.GetRoleId())
		return nil, protocol.NewExactServerError(nil, status.ErrChatBotAiRoleNotFound)
	}

	gameReq := &aigc_game.GetGameInfoRequest{
		Id: req.GetGameId(),
	}
	gameResp, err := s.clients.AigcGame.GetGameInfo(ctx, gameReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartGame GetGameInfo req(%+v) err: %v", gameReq, err)
		return nil, err
	}
	game := gameResp.GetInfo()
	if game == nil {
		log.WarnWithCtx(ctx, "StartGame GetGameInfo gameId(%s) not found", req.GetGameId())
		return nil, protocol.NewExactServerError(nil, status.ErrAigcGameGameNotFound)
	}

	var ctxId string
	// 双写开关
	if !config.GetAigcHttpLogicConfig().GetInteractiveGameTopSwitch() {
		startReq := &game_character.StartGameReq{
			Uid:       uid,
			PartnerId: req.GetPartnerId(),
			RoleId:    req.GetRoleId(),
			GameId:    req.GetGameId(),
		}
		startResp, err := s.clients.RcmdGameCharacter.StartGame(ctx, startReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "StartGame StartGame req(%+v) err: %v", startReq, err)
			return nil, err
		}

		log.InfoWithCtx(ctx, "StartGame StartGame req(%+v) resp: %+v", startReq, startResp)
		ctxId = startResp.GetCtxId()
	}

	enterReq := &aigc_game.EnterGameRequest{
		Uid:       uid,
		PartnerId: req.GetPartnerId(),

		Game: &aigc_game.EnterGameRequest_Game{
			Id:        game.GetId(),
			Source:    game.GetSource(),
			RoleId:    req.GetRoleId(),
			TopicId:   game.GetTopicId(),
			Greeting:  strings.ReplaceAll(game.GetPrologue(), "{{botname}}", role.GetName()),
			SceneDesc: strings.ReplaceAll(game.GetDesc(), "{{botname}}", role.GetName()),
		},

		CtxId: ctxId,
	}
	enterResp, err := s.clients.AigcGame.EnterGame(ctx, enterReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartGame EnterGame req(%+v) err: %v", enterReq, err)
		return nil, err
	}

	resp.CtxId = enterResp.GetCtxId()

	log.InfoWithCtx(ctx, "StartGame req(%+v) resp: %+v", req, resp)
	return resp, nil
}

func (s *Server) GetGameInfo(ctx context.Context, req *api.GetGameInfoRequest) (*api.GetGameInfoResponse, error) {
	resp := new(api.GetGameInfoResponse)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.ErrorWithCtx(ctx, "GetGameInfo miss uid")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请重新登录")
	}

	if req.GetGameId() == "" {
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	gameReq := &aigc_game.GetGameInfoRequest{
		Id: req.GetGameId(),
	}
	gameResp, err := s.clients.AigcGame.GetGameInfo(ctx, gameReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameInfo GetGameInfo req(%+v) err: %v", gameReq, err)
		return nil, err
	}
	if gameResp.GetInfo() == nil {
		return resp, nil
	}

	resp.Game = assembleGame(gameResp.GetInfo())
	return resp, nil
}

func (s *Server) GetGameList(ctx context.Context, req *api.GetGameListRequest) (*api.GetGameListResponse, error) {
	resp := new(api.GetGameListResponse)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.ErrorWithCtx(ctx, "GetGameList miss uid")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请重新登录")
	}

	gameReq := &aigc_game.GetOfficialGameListRequest{
		RoleId: req.GetRoleId(),
	}
	gameResp, err := s.clients.AigcGame.GetOfficialGameList(ctx, gameReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameList GetOfficialGameList req(%+v) err: %v", gameReq, err)
		return nil, err
	}

	for _, game := range gameResp.GetList() {
		resp.GameList = append(resp.GameList, assembleGame(game))
	}

	return resp, nil
}

func (s *Server) GetOutGames(ctx context.Context, req *api.GetOutGamesRequest) (*api.GetOutGamesResponse, error) {
	resp := new(api.GetOutGamesResponse)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.ErrorWithCtx(ctx, "GetOutGames miss uid")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请重新登录")
	}

	if req.GetRoleId() == 0 {
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	gameReq := &aigc_game.GetOfficialGameListRequest{
		RoleId: req.GetRoleId(),
	}
	gameResp, err := s.clients.AigcGame.GetOfficialGameList(ctx, gameReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOutGames GetOfficialGameList req(%+v) err: %v", gameReq, err)
		return nil, err
	}

	topicReq := &aigc_game.GetTopicListRequest{}
	topicResp, err := s.clients.AigcGame.GetTopicList(ctx, topicReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetOutGames GetTopicList req(%+v) err: %v", topicReq, err)
		return nil, err
	}

	topicGames := make(map[uint32][]*aigc_game.Game)
	for _, game := range gameResp.GetList() {
		topicGames[game.GetTopicId()] = append(topicGames[game.GetTopicId()], game)
	}

	for _, topic := range topicResp.GetList() {
		games := topicGames[topic.GetId()]
		if len(games) == 0 {
			continue
		}

		outGame := &api.OutGames{
			TopicId: topic.Id,
			Games:   make([]*api.GameInfo, 0, len(games)),
		}
		for _, game := range games {
			outGame.Games = append(outGame.Games, assembleGame(game))
		}

		resp.Games = append(resp.Games, outGame)
		resp.Topics = append(resp.Topics, assembleGameTopic(topic))
	}

	return resp, nil
}

func (s *Server) GetTopGameList(ctx context.Context, req *api.GetTopGameListRequest) (*api.GetTopGameListResponse, error) {
	resp := new(api.GetTopGameListResponse)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	if uid == 0 {
		log.ErrorWithCtx(ctx, "GetTopGameList miss uid")
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "请重新登录")
	}

	// 互动玩法热门列表放量开关，上线功能稳定后可去掉
	if !config.GetAigcHttpLogicConfig().GetInteractiveGameTopSwitch() {
		topicGamesReq := &game_character.GetTopicGamesReq{
			Uid:     uid,
			RoleId:  req.GetRoleId(),
			TopicId: req.GetTopicId(),
		}
		topicGamesResp, err := s.clients.RcmdGameCharacter.GetTopicGames(ctx, topicGamesReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetTopGameList GetTopicGames req(%+v) err: %v", topicGamesReq, err)
			return nil, err
		}

		for _, game := range topicGamesResp.GetGames() {
			resp.Games = append(resp.Games, &api.GameInfo{
				Id:        game.GetId(),
				Name:      game.GetName(),
				Icon:      game.GetIcon(),
				Bgm:       game.GetBgm(),
				Bg:        game.GetBg(),
				Creator:   game.GetCreator(),
				TopicId:   game.GetTopicId(),
				SceneDesc: game.GetSceneDesc(),
				Subtitle:  game.GetSubtitle(),
			})
		}

		return resp, nil
	}

	gameReq := &aigc_game.GetTopGameListRequest{
		RoleId:  req.GetRoleId(),
		TopicId: req.GetTopicId(),
	}
	gameResp, err := s.clients.AigcGame.GetTopGameList(ctx, gameReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTopGameList GetTopGameList req(%+v) err: %v", gameReq, err)
		return nil, err
	}

	for _, game := range gameResp.GetList() {
		resp.Games = append(resp.Games, assembleGame(game))
	}

	return resp, nil
}

func assembleGameTopic(topic *aigc_game.Topic) *api.GameTopic {
	if topic == nil {
		return nil
	}

	return &api.GameTopic{
		Id:   topic.GetId(),
		Name: topic.GetName(),
	}
}

func assembleGame(game *aigc_game.Game) *api.GameInfo {
	if game == nil {
		return nil
	}

	g := &api.GameInfo{
		Id:        game.GetId(),
		Name:      game.GetTitle(),
		Icon:      game.GetIcon(),
		Bgm:       game.GetBgm(),
		Bg:        game.GetBg(),
		TopicId:   game.GetTopicId(),
		SceneDesc: game.GetDesc(),
		Subtitle:  game.GetSubtitle(),
	}
	switch game.GetSource() {
	case aigc_game.GameSource_GAME_SOURCE_OFFICIAL:
		g.Creator = int32(game_character.GameCreator_GAME_CREATOR_SYSTEM)
	case aigc_game.GameSource_GAME_SOURCE_USER:
		g.Creator = int32(game_character.GameCreator_GAME_CREATOR_USER)
	}

	return g
}
