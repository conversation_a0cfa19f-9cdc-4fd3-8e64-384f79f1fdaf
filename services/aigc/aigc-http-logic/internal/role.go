package internal

import (
	"context"
	"strconv"
	"unicode/utf8"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/audit"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	aigc_common "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_group "golang.52tt.com/protocol/services/aigc/aigc-group"
	api "golang.52tt.com/protocol/services/aigc/aigc-http-logic"
	aigc_search "golang.52tt.com/protocol/services/aigc/aigc-search"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	"golang.52tt.com/services/aigc/aigc-http-logic/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-http-logic/internal/utils"
)

const RelationRoleNum = 5

func (s *Server) GetAIPetList(ctx context.Context, req *api.GetAIPetListRequest) (*api.GetAIPetListResponse, error) {
	resp := new(api.GetAIPetListResponse)

	listReq := &aigc_soulmate.GetOfficialAIRoleListReq{
		RoleType: aigc_soulmate.AIRoleType_AIRoleTypePet,
	}
	listResp, err := s.clients.Soulmate.GetOfficialAIRoleList(ctx, listReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIPetList GetOfficialAIRoleList req(%+v) err: %v", listReq, err)
		return nil, err
	}
	if len(listResp.GetRoleList()) == 0 {
		log.WarnWithCtx(ctx, "GetAIPetList GetOfficialAIRoleList req(%+v) empty", listReq)
		return resp, nil
	}

	resp.RoleList, err = s.aiRoleAssembler.BuildPetRoleList(ctx, listResp.GetRoleList()...)
	return resp, err
}

func (s *Server) GetAIRole(ctx context.Context, req *api.GetAIRoleRequest) (*api.GetAIRoleResponse, error) {
	resp := new(api.GetAIRoleResponse)

	listReq := &aigc_soulmate.GetAIRoleListReq{
		RoleIdList: []uint32{req.GetRoleId()},
	}
	listResp, err := s.clients.Soulmate.GetAIRoleList(ctx, listReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIRole GetAIRoleList req(%+v) err: %v", listReq, err)
		return nil, err
	}
	if len(listResp.GetRoleList()) == 0 {
		log.WarnWithCtx(ctx, "GetAIRole GetAIRoleList req(%+v) empty", listReq)
		return nil, protocol.NewExactServerError(nil, status.ErrChatBotAiRoleNotFound)
	}

	// 获取关联角色信息
	resp.RelationRoles, err = buildRelationRoleList(ctx, s.aiRoleAssembler.soulmateClient, listResp.GetRoleList()[0])
	if err != nil {
		log.WarnWithCtx(ctx, "GetAIRole BuildRelationRoleList err: %v", err)
	}

	resp.Role, err = s.aiRoleAssembler.BuildAIRole(ctx, listResp.GetRoleList()[0])
	return resp, err
}

func (s *Server) GetBannerAIRoleList(ctx context.Context, req *api.GetBannerAIRoleListRequest) (*api.GetBannerAIRoleListResponse, error) {
	resp := new(api.GetBannerAIRoleListResponse)

	listResp, err := s.clients.Soulmate.GetBannerRoleList(ctx, &aigc_soulmate.GetBannerRoleListReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBannerAIRoleList GetBannerRoleList err: %v", err)
		return nil, err
	}
	if len(listResp.GetBannerRoleList()) == 0 {
		log.WarnWithCtx(ctx, "GetBannerAIRoleList GetBannerRoleList empty")
		return resp, nil
	}

	resp.List, err = s.aiRoleAssembler.BuildBannerRoleList(ctx, listResp.GetBannerRoleList()...)
	return resp, err
}

func (s *Server) GetAIRoleCategoryList(ctx context.Context, req *api.GetAIRoleCategoryListRequest) (*api.GetAIRoleCategoryListResponse, error) {
	resp := new(api.GetAIRoleCategoryListResponse)

	listReq := &aigc_soulmate.BatchGetAIRoleCategoryReq{
		Source: aigc_soulmate.GetCategorySource_GET_CATEGORY_SOURCE_H5_HOME,
	}
	listResp, err := s.clients.Soulmate.BatchGetAIRoleCategory(ctx, listReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIRoleCategoryList BatchGetAIRoleCategory req(%+v) err: %v", listReq, err)
		return nil, err
	}
	if len(listResp.GetCategoryInfos()) == 0 {
		log.WarnWithCtx(ctx, "GetAIRoleCategoryList BatchGetAIRoleCategory req(%+v) empty", listResp)
		return resp, nil
	}

	for _, category := range listResp.GetCategoryInfos() {
		resp.CategoryInfos = append(resp.CategoryInfos, AssembleAIRoleCategory(category))
	}

	return resp, nil
}

func (s *Server) GetRcmdAIRoleList(ctx context.Context, req *api.GetRcmdAIRoleListRequest) (*api.GetRcmdAIRoleListResponse, error) {
	resp := new(api.GetRcmdAIRoleListResponse)
	log.InfoWithCtx(ctx, "GetRcmdAIRoleList req: %+v", req)

	mixEntities, roleMap, groupMap, bottom, err := s.soulmateMgr.GetRCMDRoleList(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetRcmdAIRoleList GetRCMDRoleList req(%+v) err: %v", req, err)
		return nil, err
	}

	resp.LoadFinish = bottom
	resp.MixDatas, err = s.aiRoleAssembler.BuildRcmdEntityList(ctx, mixEntities, roleMap, groupMap)
	return resp, err
}

func (s *Server) GetUserAIRoleList(ctx context.Context, req *api.GetUserAIRoleListRequest) (*api.GetUserAIRoleListResponse, error) {
	resp := new(api.GetUserAIRoleListResponse)

	uid := metainfo.GetServiceInfo(ctx).UserID()

	listReq := &aigc_soulmate.GetUserAIRoleListReq{
		Uid: uid,
	}
	listResp, err := s.clients.Soulmate.GetUserAIRoleList(ctx, listReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserAIRoleList GetUserAIRoleList req(%+v) err: %v", listReq, err)
		return nil, err
	}
	if len(listResp.GetRoleList()) == 0 {
		return resp, nil
	}

	resp.Roles, err = s.aiRoleAssembler.BuildAIRoleList(ctx, listResp.GetRoleList()...)
	return resp, err
}

func (s *Server) CreateAIRole(ctx context.Context, req *api.CreateAIRoleRequest) (*api.CreateAIRoleResponse, error) {
	resp := new(api.CreateAIRoleResponse)
	log.InfoWithCtx(ctx, "CreateAIRole req: %+v", req)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	reqRole := req.GetRole()

	user, rpcErr := s.clients.Account.GetUserByUid(ctx, uid)
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "CreateAIRole GetUserByUid uid(%d) err: %v", uid, rpcErr)
		return nil, rpcErr
	}

	// 创建角色
	createReq := &aigc_soulmate.CreateAIRoleReq{
		Role: &aigc_soulmate.CreateAIRoleReq_Role{
			Type:            aigc_soulmate.AIRoleType_AIRoleTypeGame,
			Source:          aigc_soulmate.AIRoleSource_AIRoleSourceUser,
			Sex:             reqRole.GetSex(),
			Name:            reqRole.GetName(),
			Avatar:          reqRole.GetAvatar(),
			Image:           reqRole.GetImage(),
			State:           aigc_soulmate.AIRoleState(reqRole.GetState()),
			Character:       reqRole.GetCharacter(),
			CategoryId:      reqRole.GetCategoryId(),
			Tags:            reqRole.GetTags(),
			Prologue:        reqRole.GetPrologue(),
			Timbre:          reqRole.GetTimbre(),
			Exposed:         true,
			EnableRcmdReply: true,
			UserRoleSetting: reqRole.GetUserRoleSetting(),
			CreatorInfoType: reqRole.GetCreatorInfoType(),
		},
	}

	createResp, err := s.clients.Soulmate.CreateAIRole(ctx, createReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateAIRole CreateAIRole req(%+v) err: %v", createReq, err)
		return nil, err
	}

	createdRole := createResp.GetRole()

	// 角色信息送审
	if createdRole.GetAuditResult() == aigc_soulmate.AuditResult_AuditResultReview {
		scanReq := &mgr.ScanMixReq{
			User:  user,
			Scene: audit.SCENE_CODE_AI_ROLE,
			CallbackParams: map[string]string{
				"id":       strconv.Itoa(int(createdRole.GetId())),
				"audit_at": strconv.Itoa(int(createdRole.GetAuditAt())),
			},
			Texts: []string{
				reqRole.GetName(),
				reqRole.GetPrologue(),
				reqRole.GetCharacter(),
			},
			Images: []string{
				reqRole.GetImage(),
				reqRole.GetAvatar(),
			},
		}
		if len(reqRole.GetUserRoleSetting()) > 0 {
			scanReq.Texts = append(scanReq.Texts, reqRole.GetUserRoleSetting())
		}
		err = s.censorMgr.AsyncScanMultiMedia(ctx, scanReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "CreateAIRole AsyncScanMultiMedia req(%+v) err: %v", scanReq, err)
			return nil, err
		}
	}

	resp.Id = createResp.GetRole().GetId()

	log.InfoWithCtx(ctx, "CreateAIRole req(%+v) resp: %+v", req, resp)
	return resp, nil
}

func (s *Server) UpdateAIRole(ctx context.Context, req *api.UpdateAIRoleRequest) (*api.UpdateAIRoleResponse, error) {
	resp := new(api.UpdateAIRoleResponse)
	log.InfoWithCtx(ctx, "UpdateAIRole req: %+v", req)

	uid := metainfo.GetServiceInfo(ctx).UserID()
	reqRole := req.GetRole()

	user, rpcErr := s.clients.Account.GetUserByUid(ctx, uid)
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "UpdateAIRole GetUserByUid uid(%d) err: %v", uid, rpcErr)
		return nil, rpcErr
	}

	role, err := s.soulmateMgr.GetRole(ctx, req.GetRole().GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIRole GetRole id(%d) err: %v", reqRole.GetId(), err)
		return nil, err
	}
	if role.GetUid() != uid {
		log.WarnWithCtx(ctx, "UpdateAIRole user(%d) role(%d) not found", uid, reqRole.GetId())
		return nil, protocol.NewExactServerError(nil, status.ErrChatBotAiRoleNotFound)
	}

	// 保存角色修改
	updateReq := &aigc_soulmate.UpdateAIRoleReq{
		Role: &aigc_soulmate.UpdateAIRoleReq_Role{
			Id:         reqRole.GetId(),
			Sex:        reqRole.GetSex(),
			Name:       reqRole.GetName(),
			Avatar:     reqRole.GetAvatar(),
			Image:      reqRole.GetImage(),
			State:      aigc_soulmate.AIRoleState(reqRole.GetState()),
			Character:  reqRole.GetCharacter(),
			CategoryId: reqRole.GetCategoryId(),
			Tags:       reqRole.GetTags(),
			Prologue:   reqRole.GetPrologue(),
			Timbre:     reqRole.GetTimbre(),

			CreatorInfoType: reqRole.GetCreatorInfoType(),
			UserRoleSetting: reqRole.GetUserRoleSetting(),
		},
	}

	updateResp, err := s.clients.Soulmate.UpdateAIRole(ctx, updateReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAIRole UpdateAIRole req(%+v) err: %v", updateReq, err)
		return nil, err
	}

	updatedRole := updateResp.GetRole()

	// 角色信息送审
	if updatedRole.GetAuditResult() == aigc_soulmate.AuditResult_AuditResultReview {
		scanReq := &mgr.ScanMixReq{
			User:  user,
			Scene: audit.SCENE_CODE_AI_ROLE,
			CallbackParams: map[string]string{
				"id":       strconv.Itoa(int(reqRole.GetId())),
				"audit_at": strconv.Itoa(int(updatedRole.GetAuditAt())),
			},
			Texts: []string{
				reqRole.GetName(),
				reqRole.GetPrologue(),
				reqRole.GetCharacter(),
			},
			Images: []string{
				reqRole.GetImage(),
				reqRole.GetAvatar(),
			},
		}
		if len(reqRole.GetUserRoleSetting()) > 0 {
			scanReq.Texts = append(scanReq.Texts, reqRole.GetUserRoleSetting())
		}
		err = s.censorMgr.AsyncScanMultiMedia(ctx, scanReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAIRole AsyncScanMultiMedia req(%+v) err: %v", scanReq, err)
			return nil, err
		}
	}

	log.InfoWithCtx(ctx, "UpdateAIRole req(%+v) resp: %+v", req, resp)
	return resp, nil
}

func (s *Server) LikeRole(ctx context.Context, req *api.LikeRoleRequest) (*api.LikeRoleResponse, error) {
	resp := new(api.LikeRoleResponse)
	log.InfoWithCtx(ctx, "LikeRole req: %+v", req)

	uid := metainfo.GetServiceInfo(ctx).UserID()

	role, err := s.soulmateMgr.GetRole(ctx, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "LikeRole GetRole id(%d) err: %v", req.GetId(), err)
		return nil, err
	}
	if role == nil {
		log.WarnWithCtx(ctx, "LikeRole user(%d) role(%d) not found", uid, req.GetId())
		return nil, protocol.NewExactServerError(nil, status.ErrChatBotAiRoleNotFound)
	}

	liked, err := s.soulmateMgr.IsLikedRole(ctx, uid, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "LikeRole IsLikedRole uid(%d) roleId(%d) err: %v", uid, req.GetId())
		return nil, err
	}
	if liked { // 已点赞过，取消点赞
		_, err := s.clients.Soulmate.UnlikeAIRole(ctx, &aigc_soulmate.UnlikeAIRoleReq{
			RoleId: req.GetId(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "LikeRole UnlikeAIRole uid(%d) id(%d) err: %v", uid, req.GetId(), err)
			return nil, err
		}

		resp.LikeState = uint32(aigc_soulmate.AIRole_LikeStateUnliked)
	} else { // 未点赞过，点赞
		_, err := s.clients.Soulmate.LikeAIRole(ctx, &aigc_soulmate.LikeAIRoleReq{
			RoleId: req.GetId(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "LikeRole LikeAIRole uid(%d) id(%d) err: %v", uid, req.GetId(), err)
			return nil, err
		}

		resp.LikeState = uint32(aigc_soulmate.AIRole_LikeStateLiked)
	}

	log.InfoWithCtx(ctx, "LikeRole req(%+v) resp: %+v", req, resp)
	return resp, nil
}

func (s *Server) ShareRole(ctx context.Context, req *api.ShareRoleRequest) (*api.ShareRoleResponse, error) {
	resp := new(api.ShareRoleResponse)
	log.InfoWithCtx(ctx, "ShareRole req: %+v", req)

	shareReq := &aigc_soulmate.ShareRoleReq{
		RoleId: req.GetRoleId(),
	}
	shareResp, err := s.clients.Soulmate.ShareRole(ctx, shareReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "ShareRole ShareRole req(%+v) err: %v", shareReq, err)
		return nil, err
	}

	resp.Key = shareResp.GetKey()
	resp.ExpireAt = shareResp.GetExpireAt()

	log.InfoWithCtx(ctx, "ShareRole req(%+v) resp: %+v", req, resp)
	return resp, nil
}

func (s *Server) DeleteAIRole(ctx context.Context, req *api.DeleteAIRoleRequest) (*api.DeleteAIRoleResponse, error) {
	resp := new(api.DeleteAIRoleResponse)
	log.InfoWithCtx(ctx, "DeleteAIRole req: %+v", req)

	uid := metainfo.GetServiceInfo(ctx).UserID()

	role, err := s.soulmateMgr.GetRole(ctx, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteAIRole GetRole id(%d) err: %v", req.GetId(), err)
		return nil, err
	}
	if role.GetUid() != uid {
		log.WarnWithCtx(ctx, "DeleteAIRole user(%d) role(%d) not found", uid, req.GetId())
		return nil, protocol.NewExactServerError(nil, status.ErrChatBotAiRoleNotFound)
	}

	deleteReq := &aigc_soulmate.DeleteAIRoleReq{
		Id: req.GetId(),
	}
	_, err = s.clients.Soulmate.DeleteAIRole(ctx, deleteReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteAIRole DeleteAIRole req(%+v) err: %v", deleteReq, err)
		return nil, err
	}

	log.InfoWithCtx(ctx, "DeleteAIRole req(%+v) resp: %+v", req, resp)
	return resp, nil
}

func truncateString(s string, maxLength int) string {
	if utf8.RuneCountInString(s) <= maxLength {
		return s
	}
	runes := []rune(s)
	return string(runes[:maxLength])
}

func (s *Server) SearchRole(ctx context.Context, req *api.SearchRoleRequest) (*api.SearchRoleResponse, error) {
	uid := metainfo.GetServiceInfo(ctx).UserID()
	log.InfoWithCtx(ctx, "SearchRole uid:%d, req: %s", uid, req.String())
	resp := new(api.SearchRoleResponse)
	if req.GetContent() == "" {
		return resp, nil
	}
	content := truncateString(req.GetContent(), 30)

	user, rpcErr := s.clients.Account.GetUserByUid(ctx, uid)
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "SearchRole GetUserByUid uid(%d) err: %v", uid)
		return nil, rpcErr
	}
	if user == nil {
		log.ErrorWithCtx(ctx, "SearchRole user %d not found", uid)
		return nil, protocol.NewExactServerError(nil, status.ErrAccountNotExist)
	}
	// 审核搜索类容
	isPass, _ := s.censorMgr.SimpleSyncScanTexts(ctx, user, &mgr.ScannedText{
		Text:  content,
		Scene: audit.SCENE_CODE_AI_ROLE_SEARCH,
	})
	if !isPass {
		log.WarnWithCtx(ctx, "SearchRole uid:%d, content(%s) is not pass", uid, content)
		return resp, nil
	}

	// 搜索数据
	searchReq := &aigc_search.AigcContentSearchReq{
		Content: content,
		LastId:  req.GetLastId(),
		Limit:   20,
	}
	listResp, err := s.clients.AigcSearch.AigcContentSearch(ctx, searchReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchAIRole fail, err:%v req:%+v", err, searchReq)
		return nil, err
	}
	log.DebugWithCtx(ctx, "SearchRole uid:%d, req(%s) resp: %+v", uid, req.String(), listResp)
	resp.LastId = listResp.GetLastId()
	resp.LoadFinish = listResp.GetLoadFinish()

	if len(listResp.GetItems()) == 0 {
		return resp, nil
	}

	roleIds := make([]uint32, 0, len(listResp.GetItems()))
	groupTemplateIds := make([]uint32, 0, len(listResp.GetItems()))
	roleMap := make(map[uint32]*aigc_soulmate.AIRole)
	groupMap := make(map[uint32]*aigc_group.GroupTemplate)
	groupLikeCountMap := make(map[uint32]int32)
	for _, item := range listResp.GetItems() {
		if item.GetType() == aigc_search.AigcContentType_AIGC_CONTENT_TYPE_GROUP_TEMPLATE {
			groupTemplateIds = append(groupTemplateIds, item.GetId())
		} else {
			roleIds = append(roleIds, item.GetId())
		}
	}

	if len(roleIds) > 0 {
		roleInfos, err := s.clients.Soulmate.GetAIRoleList(ctx, &aigc_soulmate.GetAIRoleListReq{
			RoleIdList: roleIds,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "SearchRole GetAIRoleList uid:%d, req(%+v) err: %v", uid, roleIds, err)
			return nil, err
		}
		for _, role := range roleInfos.GetRoleList() {
			roleMap[role.GetId()] = role
		}
	}

	if len(groupTemplateIds) > 0 && utils.IsHigherVersion(metainfo.GetServiceInfo(ctx).ClientVersion(), "6.62.5") {
		groupTemplateInfos, err := s.clients.AigcGroup.GetGroupTemplateByIds(ctx, &aigc_group.GetGroupTemplateByIdsRequest{
			Ids: groupTemplateIds,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "SearchRole GetGroupTemplateByIds uid:%d, req(%+v) err: %v", uid, groupTemplateIds, err)
			return nil, err
		}
		for _, group := range groupTemplateInfos.GetList() {
			groupMap[group.GetId()] = group
		}

		// 获取群模版的点赞数据
		objects := make([]*aigc_common.Object, 0, len(groupTemplateIds))
		for _, id := range groupTemplateIds {
			objects = append(objects, &aigc_common.Object{
				ObjectId:   id,
				ObjectType: aigc_common.ObjectType_OBJECT_TYPE_GROUP_TEMPLATE,
			})
		}
		likeResp, err := s.clients.AigcCommon.GetAttitudeCount(ctx, &aigc_common.GetAttitudeCountRequest{
			Objects: objects,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "SearchRole GetAttitudeCount uid:%d, req(%+v) err: %v", uid, objects, err)
		}
		for _, like := range likeResp.GetAttitudeCountInfos() {
			groupLikeCountMap[like.GetObject().GetObjectId()] = int32(like.GetCount())
		}
	}

	// 按序返回，搜索的数据已经过滤了不展示的数据，这里不需再判断
	resp.Data = make([]*api.AIRole, 0, len(listResp.GetItems()))
	resp.MixDatas = make([]*api.SearchMixDataItem, 0, len(listResp.GetItems()))
	for _, item := range listResp.GetItems() {
		if item.GetType() == aigc_search.AigcContentType_AIGC_CONTENT_TYPE_GROUP_TEMPLATE {
			if v, ok := groupMap[item.GetId()]; ok {
				resp.MixDatas = append(resp.MixDatas, &api.SearchMixDataItem{
					Type:      uint32(aigc_search.AigcContentType_AIGC_CONTENT_TYPE_GROUP_TEMPLATE),
					RoleInfo:  nil,
					GroupInfo: s.aiRoleAssembler.BuildSearchTemplateGroupInfo(v, groupLikeCountMap[v.GetId()]),
				})
			}
		} else {
			if v, ok := roleMap[item.GetId()]; ok {
				// 旧字段兼容
				resp.Data = append(resp.Data, s.aiRoleAssembler.BuildSearchRoleInfo(v))
				resp.MixDatas = append(resp.MixDatas, &api.SearchMixDataItem{
					Type:      uint32(aigc_search.AigcContentType_AIGC_CONTENT_TYPE_ROLE),
					RoleInfo:  s.aiRoleAssembler.BuildSearchRoleInfo(v),
					GroupInfo: nil,
				})
			}
		}
	}

	return resp, err
}

func (s *Server) GetUserExclusiveRoleList(ctx context.Context, req *api.GetUserExclusiveRoleListRequest) (*api.GetUserExclusiveRoleListResponse, error) {
	resp := new(api.GetUserExclusiveRoleListResponse)
	uid := metainfo.GetServiceInfo(ctx).UserID()

	listReq := &aigc_soulmate.GetUserExclusiveRoleListRequest{
		Uid: uid,

		Cursor: req.GetCursor(),
		Limit:  req.GetLimit(),
	}
	listResp, err := s.clients.Soulmate.GetUserExclusiveRoleList(ctx, listReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserExclusiveRoleList GetUserExclusiveRoleList req(%+v) err: %v", listReq, err)
		return nil, err
	}
	if len(listResp.GetList()) == 0 {
		return resp, nil
	}

	if resp.List, err = s.aiRoleAssembler.BuildAIRoleList(ctx, listResp.GetList()...); err != nil {
		return nil, err
	}

	resp.HasMore = listResp.GetHasMore()
	resp.NextCursor = listResp.GetNextCursor()
	return resp, nil
}
