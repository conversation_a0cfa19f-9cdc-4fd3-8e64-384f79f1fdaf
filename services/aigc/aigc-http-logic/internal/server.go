package internal

import (
	"context"

	"golang.52tt.com/kaihei-pkg/asr"

	config "golang.52tt.com/services/aigc/aigc-http-logic/internal/config/aigc_http_logic"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/http"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/web"
	"golang.52tt.com/pkg/web/binding"
	"golang.52tt.com/protocol/common/status"
	api "golang.52tt.com/protocol/services/aigc/aigc-http-logic"
	"golang.52tt.com/services/aigc/aigc-http-logic/internal/assembler"
	"golang.52tt.com/services/aigc/aigc-http-logic/internal/mgr"
	"golang.52tt.com/services/aigc/aigc-http-logic/internal/monitor"
	"golang.52tt.com/services/aigc/aigc-http-logic/internal/rpc"
)

type StartConfig struct {
	IsUsePost     bool `json:"is_use_post"`
	IsAuth<PERSON><PERSON>Token bool `json:"is_auth_by_token"`
	IsAllowCors   bool `json:"is_allow_cors"`
}

type Server struct {
	clients *rpc.Clients

	censorMgr   *mgr.CensorMgr
	soulmateMgr *mgr.SoulmateMgr
	riskChecker *mgr.RiskChecker

	dataLoader *assembler.DataLoader

	aiRoleAssembler           *AIRoleAssembler
	aiGroupAssembler          *assembler.AIGroupAssembler
	aiPartnerAssembler        *AIPartnerAssembler
	interactiveGameAssembler  *InteractiveGameAssembler
	backgroundAssembler       *assembler.BackgroundAssembler
	intimacyRelationAssembler *assembler.RelationAssembler

	rcmdGatewayReporter *monitor.RCMDGatewayReporter
	asrHandle           *asr.AsrHandle
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	clients := rpc.NewClients()

	censorMgr := mgr.NewCensorMgr(clients.Censoring)
	soulmateMgr := mgr.NewSoulmateMgr(clients.Soulmate, clients.AigcGroup, clients.RcmdRole, clients.RcmdPartner, clients.RcmdBusiness)
	riskChecker := mgr.NewRiskChecker(clients.RiskMngApi)

	dataLoader := assembler.NewDataLoader(
		clients.Account,
		clients.AigcGroup,
		clients.AigcCommon,
		clients.Soulmate,
	)

	aiRoleAssembler := NewAIRoleAssembler(clients.Soulmate, clients.RcmdBusiness, clients.Account, clients.FriendshipClient)
	aiGroupAssembler := assembler.NewAIGroupAssembler(dataLoader)
	aiPartnerAssembler := NewAIPartnerAssembler(clients.Soulmate, clients.RcmdBusiness, clients.Account, clients.FriendshipClient)
	interactiveGameAssembler := NewInteractiveGameAssembler(clients.Soulmate)
	backgroundAssembler := assembler.NewBackgroundAssembler(clients.AigcIntimacy, clients.Soulmate)

	intimacyRelationAssembler := assembler.NewRelationAssembler(clients.AigcIntimacy)

	rcmdGatewayReporter := monitor.NewRCMDGatewayReporter()

	asrHandle := asr.NewAsrHandle()

	err := config.InitAigcHttpLogicConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "InitAigcHttpLogicConfig err:%v", err)
		return nil, err
	}

	return &Server{
		clients: clients,

		censorMgr:   censorMgr,
		soulmateMgr: soulmateMgr,
		riskChecker: riskChecker,

		dataLoader: dataLoader,

		aiRoleAssembler:           aiRoleAssembler,
		aiGroupAssembler:          aiGroupAssembler,
		aiPartnerAssembler:        aiPartnerAssembler,
		interactiveGameAssembler:  interactiveGameAssembler,
		intimacyRelationAssembler: intimacyRelationAssembler,
		backgroundAssembler:       backgroundAssembler,
		rcmdGatewayReporter:       rcmdGatewayReporter,
		asrHandle:                 asrHandle,
	}, nil
}

func wrapper[request, response any](handler func(context.Context, *request) (*response, error)) func(context.Context, http.ResponseWriter, *http.Request) {
	return func(ctx context.Context, w http.ResponseWriter, r *http.Request) {
		req := new(request)
		if err := binding.Bind(r, req); err != nil {
			log.ErrorWithCtx(ctx, "Bind r(%+v) err: %v", r, err)
			web.ServeBadReq(w)
			return
		}

		resp, err := handler(ctx, req)
		if err != nil {
			if serverErr, ok := err.(protocol.ServerError); ok {
				if serverErr.Code() == status.ErrSys {
					web.ServeAPIError(w)
				} else {
					_ = web.ServeAPICodeJson(w, int32(serverErr.Code()), serverErr.Message(), nil)
				}
			} else {
				_ = web.ServeAPICodeJson(w, status.ErrSys, "内部错误", nil)
			}

			return
		}

		_ = web.ServeAPIJsonV2(w, resp)
	}
}

// Register 注册路由
func (s *Server) Register(router *http.Router) {
	{
		// 伴侣
		partner := router.Child("/partner")
		// 创建伴侣
		partner.POST("/create", wrapper(s.CreateAIPartner))
		// 修改伴侣
		partner.POST("/update", wrapper(s.UpdateAIPartner))
		// 修改伴侣沉默状态
		partner.POST("/silent/switch", wrapper(s.SwitchAIPartnerSilent))
		// 重新绑定伴侣角色
		partner.POST("/role/rebind", wrapper(s.RebindAIPartnerRole))
		// 伴侣聊天上报
		partner.POST("/chatting/report", wrapper(s.ReportAIPartnerChatting))
		// 删除伴侣
		partner.POST("/delete", wrapper(s.DeleteAIPartner))
		// 查询伴侣
		partner.GET("/get", wrapper(s.GetAIPartner))
		// 查询聊过的伴侣
		partner.GET("/chatting/list", wrapper(s.GetChattingPartnerList))
		// 查询桌宠伴侣
		partner.GET("/pet/get", wrapper(s.GetPetPartner))
		// 聊天挑战页面句数信息
		partner.GET("/challenge/count", wrapper(s.GetChatNum))
	}

	{
		// 角色
		role := router.Child("/role")
		// 查询角色
		role.POST("/get", wrapper(s.GetAIRole))
		// 桌宠角色列表
		role.GET("/pet/list", wrapper(s.GetAIPetList))
		// banner角色列表
		role.GET("/banner/list", wrapper(s.GetBannerAIRoleList))
		// 角色分类列表
		role.GET("/category/list", wrapper(s.GetAIRoleCategoryList))
		// 角色推荐列表
		role.POST("/rcmd/list", wrapper(s.GetRcmdAIRoleList))
		// 创建角色
		role.POST("/create", wrapper(s.CreateAIRole))
		// 修改角色
		role.POST("/update", wrapper(s.UpdateAIRole))
		// 删除角色
		role.POST("/delete", wrapper(s.DeleteAIRole))
		// 点赞角色
		role.POST("/like", wrapper(s.LikeRole))
		// 分享角色
		role.POST("/share", wrapper(s.ShareRole))
		// 搜索角色
		role.GET("/search", wrapper(s.SearchRole))
		// 自己的角色列表
		role.GET("/own/list", wrapper(s.GetUserAIRoleList))
		// 专属角色列表
		role.POST("/exclusive/list", wrapper(s.GetUserExclusiveRoleList))
	}

	{
		// 透传中台
		rcmd := router.Child("/rcmd")
		// 入口网关
		rcmd.POST("/gateway", func(ctx context.Context, w http.ResponseWriter, r *http.Request) {
			cmd := r.Header.Get("Rcmd-Cmd")
			if cmd == "" {
				web.ServeBadReq(w)
				return
			}
			payload, err := http.ReadBodyBytes(r)
			if err != nil {
				web.ServeAPIError(w)
				return
			}
			req := &api.HandleRcmdCommandRequest{
				Cmd:     cmd,
				Payload: payload,
			}
			resp, err := s.HandleRcmdCommand(ctx, req)
			if err != nil {
				web.ServeAPIError(w)
				return
			}
			if resp.GetCode() != 0 {
				web.ServeAPICodeJson(w, int32(resp.GetCode()), resp.GetMsg(), nil)
				return
			}
			w.Write(resp.GetData())
		})
	}

	{
		// 互动玩法
		game := router.Child("/interactive-game")
		// 创建互动玩法
		game.POST("/create", wrapper(s.CreateInteractiveGame))
		// 修改互动玩法
		game.POST("/update", wrapper(s.UpdateInteractiveGame))
		// 删除互动玩法
		game.POST("/delete", wrapper(s.DeleteInteractiveGame))
		// 获取直接创建的互动玩法列表
		game.GET("/own/list", wrapper(s.GetInteractiveGameList))
		// 开始玩法
		game.POST("/start", wrapper(s.StartGame))
		// 玩法列表
		game.POST("/list", wrapper(s.GetGameList))
		// 外显玩法列表
		game.POST("/out", wrapper(s.GetOutGames))
		// 玩法详情
		game.POST("/info", wrapper(s.GetGameInfo))
		// 热门玩法列表
		game.POST("/top", wrapper(s.GetTopGameList))
	}

	{
		// 聊天模板
		chatTemplate := router.Child("/chat-template")
		// 获取聊天模板列表
		chatTemplate.POST("/list", wrapper(s.GetBindChatTemplates))
	}

	{
		// 点赞
		attitude := router.Child("/attitude")
		// 点赞
		attitude.POST("/attitude", wrapper(s.Attitude))
		// 获取点赞状态
		attitude.POST("/had-attitude", wrapper(s.HadAttitude))

		// 读心
		readHeart := router.Child("/readheart")
		// 数目
		readHeart.POST("/count", wrapper(s.GetReadHeartCount))
		// 读心
		readHeart.POST("/read", wrapper(s.ReadHeart))

		msgInst := router.Child("/msg")
		// 消息
		msgInst.POST("/get", wrapper(s.BatchGetMsg))

		asrInst := router.Child("/asr")
		// 消息
		asrInst.POST("/audio2text", wrapper(s.TransAudioToText))
	}

	{
		//AI账号
		account := router.Child("/aigc-account")
		// AI账号扩列墙列表
		account.POST("/list", wrapper(s.GetAiAccountList))
	}

	// 亲密度路由
	s.registerIntimacy(router)
	// 群聊路由
	s.registerGroup(router)
}

func (s *Server) registerIntimacy(router *http.Router) {
	// 亲密度
	intimacy := router.Child("/intimacy")

	// 获取亲密度信息
	intimacy.POST("/info", wrapper(s.GetIntimacyInfo))
	// 获取其他详情信息
	intimacy.POST("/getExtraInfo", wrapper(s.GetExtraInfo))
	// 获取聊天背景列表
	intimacy.POST("/getChatBackgroundList", wrapper(s.GetChatBackgroundList))
	// 切换聊天背景
	intimacy.POST("/switchChatBackground", wrapper(s.SwitchChatBackground))
	// 获取关系列表
	intimacy.POST("/getRelationList", wrapper(s.GetRelationList))
	// 切换关系
	intimacy.POST("/switchRelation", wrapper(s.SwitchRelation))
}

func (s *Server) registerGroup(router *http.Router) {
	// 群聊
	group := router.Child("/group")

	// 创建群组
	group.POST("/create", wrapper(s.CreateGroup))
	// 离开群组
	group.POST("/leave", wrapper(s.LeaveGroup))
	// 删除群组
	group.POST("/delete", wrapper(s.DeleteGroup))
	// 获取群组信息
	group.GET("/info", wrapper(s.GetGroupInfo))
	// 获取群组列表
	group.GET("/list", wrapper(s.GetGroupList))
	// 获取群组模板信息
	group.GET("/template/info", wrapper(s.GetGroupTemplateInfo))
	// 获取群组成员列表
	group.GET("/member/list", wrapper(s.GetGroupMemberList))

	// 获取剧本玩法列表筛选项
	group.GET("/script/getfilteritem", wrapper(s.GetFilterItem))
	// 获取剧本玩法列表
	group.POST("/script/getscriptlist", wrapper(s.GetScriptList))
	// 获取Banner 位置接口
	group.GET("/script/gethotbanner", wrapper(s.GetScriptHotBanner))
	// 开始匹配
	group.POST("/script/match/start", wrapper(s.StartScriptMatch))
	// 确认匹配
	group.POST("/script/match/confirm", wrapper(s.ConfirmScriptMatch))
	// 取消匹配
	group.POST("/script/match/cancel", wrapper(s.CancelScriptMatch))
}
