package internal

import (
	"context"
	"sort"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"
	account "golang.52tt.com/clients/account-go"
	"golang.52tt.com/clients/ugc/friendship"
	aigc_group "golang.52tt.com/protocol/services/aigc/aigc-group"
	rcmd_role "golang.52tt.com/protocol/services/rcmd/rcmd_partner_role"

	"golang.52tt.com/pkg/log"
	api "golang.52tt.com/protocol/services/aigc/aigc-http-logic"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
	rcmd_business "golang.52tt.com/protocol/services/rcmd/business_ai_partner"
)

// AIRoleAssembler 组装角色返回数据
type AIRoleAssembler struct {
	soulmateClient     aigc_soulmate.AigcSoulmateClient
	rcmdBusinessClient rcmd_business.BusinessAIPartnerClient
	accountClient      account.IClient
	friendshipClient   friendship.IClient
}

func NewAIRoleAssembler(
	soulmateClient aigc_soulmate.AigcSoulmateClient,
	rcmdBusinessClient rcmd_business.BusinessAIPartnerClient,
	accountClient account.IClient,
	friendshipClient friendship.IClient,
) *AIRoleAssembler {
	return &AIRoleAssembler{
		soulmateClient:     soulmateClient,
		rcmdBusinessClient: rcmdBusinessClient,
		accountClient:      accountClient,
		friendshipClient:   friendshipClient,
	}
}

func (assembler *AIRoleAssembler) BuildBannerRoleList(ctx context.Context, roles ...*aigc_soulmate.AIRole) ([]*api.BannerRole, error) {
	bannerRoleList := make([]*api.BannerRole, 0, len(roles))
	for _, role := range roles {
		bannerRoleList = append(bannerRoleList, &api.BannerRole{
			Id:            role.GetId(),
			Type:          uint32(role.GetType()),
			Name:          role.GetName(),
			Image:         role.GetImage(),
			Character:     role.GetCharacter(),
			CornerIcon:    role.GetCornerIcon(),
			PrologueAudio: role.GetPrologueAudio(),
		})
	}

	return bannerRoleList, nil
}

func (assembler *AIRoleAssembler) BuildPetRoleList(ctx context.Context, roles ...*aigc_soulmate.AIRole) ([]*api.PetRole, error) {
	loadExtra, err := assembler.extraLoader(ctx, roles...)
	if err != nil {
		return nil, err
	}

	petRoleList := make([]*api.PetRole, 0, len(roles))
	for _, role := range roles {
		petRole := &api.PetRole{
			Id:         role.GetId(),
			Sex:        role.GetSex(),
			Name:       role.GetName(),
			Character:  role.GetCharacter(),
			CornerIcon: role.GetCornerIcon(),
		}
		if prologue := role.GetCollectPrologue(); prologue != nil {
			petRole.CollectPrologue = &api.AIRolePrologue{
				Text:  prologue.GetText(),
				Audio: prologue.GetAudio(),
			}
		}
		if extra := loadExtra(role.GetId()); extra != nil {
			petRole.Ext = extra.GetExt()
		}

		petRoleList = append(petRoleList, petRole)
	}

	return petRoleList, nil
}

func (assembler *AIRoleAssembler) BuildAIRole(ctx context.Context, role *aigc_soulmate.AIRole) (*api.AIRole, error) {
	list, err := assembler.BuildAIRoleList(ctx, role)
	if err != nil || len(list) == 0 {
		return nil, err
	}

	return list[0], nil
}

func (assembler *AIRoleAssembler) BuildAIRoleList(ctx context.Context, roles ...*aigc_soulmate.AIRole) ([]*api.AIRole, error) {
	loadExtra, err := assembler.extraLoader(ctx, roles...)
	if err != nil {
		return nil, err
	}

	loadLike, err := assembler.likeLoader(ctx, roles...)
	if err != nil {
		return nil, err
	}
	uid := metainfo.GetServiceInfo(ctx).UserID()

	loadCreatorInfo, err := assembler.creatorInfoLoader(ctx, uid, roles...)
	if err != nil {
		return nil, err
	}

	apiRoleList := make([]*api.AIRole, 0, len(roles))
	for _, role := range roles {
		apiRole := &api.AIRole{
			Id:              role.GetId(),
			Type:            uint32(role.GetType()),
			Sex:             role.GetSex(),
			Name:            role.GetName(),
			Style:           role.GetStyle(),
			State:           uint32(role.GetState()),
			Avatar:          role.GetAvatar(),
			Image:           role.GetImage(),
			Intro:           role.GetIntro(),
			DialogColor:     role.GetDialogColor(),
			Character:       role.GetCharacter(),
			Prologue:        role.GetPrologue(),
			PrologueAudio:   role.GetPrologueAudio(),
			Timbre:          role.GetTimbre(),
			CornerIcon:      role.GetCornerIcon(),
			AuditResult:     uint32(role.GetAuditResult()),
			Tags:            role.GetTags(),
			EnableRcmdReply: role.GetEnableRcmdReply(),
			StoryId:         role.GetStoryId(),
			CreatorUid:      role.GetUid(),
			Exposed:         role.GetExposed(),
			StoryMode:       uint32(role.GetStoryMode()),
			CreatorInfoType: role.GetCreatorInfoType(),
			UserRoleSetting: role.GetUserRoleSetting(),
			Scope:           uint32(role.GetScope()),
		}
		if category := role.GetCategory(); category != nil {
			apiRole.Category = &api.AIRole_Category{
				Id:    category.GetId(),
				Title: category.GetTitle(),
			}
		}
		if likeState := loadLike(role.GetId()); likeState != aigc_soulmate.AIRoleLikeInfo_LikeStateUnspecified {
			apiRole.LikeState = uint32(likeState)

			likeNum := role.GetConfigLikeNum() + int32(role.GetUserLikeNum())
			if likeNum > 0 {
				apiRole.LikeNum = uint32(likeNum)
			} else if likeNum <= 0 {
				if likeState == aigc_soulmate.AIRoleLikeInfo_LikeStateLiked {
					apiRole.LikeNum = 1
				} else {
					apiRole.LikeNum = 0
				}
			} else {
				apiRole.LikeNum = 0
			}

		}
		if extra := loadExtra(role.GetId()); extra != nil {
			apiRole.Ext = extra.GetExt()
		}

		if creatorInfo := loadCreatorInfo(role.GetId()); creatorInfo != nil {
			apiRole.CreatorInfo = creatorInfo
		}

		apiRoleList = append(apiRoleList, apiRole)
	}

	return apiRoleList, nil
}

func (assembler *AIRoleAssembler) BuildRcmdEntityList(ctx context.Context, mixEntities []*rcmd_role.RCMDEntity,
	roleMap map[uint32]*aigc_soulmate.AIRole, groupTemplateMap map[uint32]*aigc_group.GroupTemplate) ([]*api.ListMixDataItem, error) {
	if len(roleMap) == 0 && len(groupTemplateMap) == 0 {
		return nil, nil
	}

	mixItemList := make([]*api.ListMixDataItem, 0, len(mixEntities))
	for _, entity := range mixEntities {
		switch entity.GetType() {
		case rcmd_role.RCMDEntity_Role:
			if role, ok := roleMap[entity.GetId()]; ok {
				mixItemList = append(mixItemList, &api.ListMixDataItem{
					Type:     uint32(entity.GetType()),
					RoleInfo: assembler.BuildRcmdRoleInfo(role),
				})
			}
		case rcmd_role.RCMDEntity_Group:
			if groupTemplate, ok := groupTemplateMap[entity.GetId()]; ok {
				mixItemList = append(mixItemList, &api.ListMixDataItem{
					Type:      uint32(entity.GetType()),
					GroupInfo: assembler.BuildRcmdGroupTemplateInfo(groupTemplate),
				})
			}
		default:
			log.WarnWithCtx(ctx, "BuildRcmdEntityList invalid rcmdEntity type(%d)", entity.GetType())
		}
	}

	return mixItemList, nil
}

func (assembler *AIRoleAssembler) BuildRcmdRoleList(ctx context.Context, roles ...*aigc_soulmate.AIRole) ([]*api.RcmdRole, error) {
	if len(roles) == 0 {
		return nil, nil
	}

	rcmdRoleList := make([]*api.RcmdRole, 0, len(roles))
	for _, role := range roles {
		rcmdRoleList = append(rcmdRoleList, assembler.BuildRcmdRoleInfo(role))
	}

	return rcmdRoleList, nil
}

func (assembler *AIRoleAssembler) BuildRcmdRoleInfo(role *aigc_soulmate.AIRole) *api.RcmdRole {
	rcmdRole := &api.RcmdRole{
		Id:            role.GetId(),
		Type:          uint32(role.GetType()),
		Sex:           role.GetSex(),
		Name:          role.GetName(),
		Tags:          role.GetTags(),
		Image:         role.GetImage(),
		Character:     role.GetCharacter(),
		CornerIcon:    role.GetCornerIcon(),
		PrologueAudio: role.GetPrologueAudio(),
		Avatar:        role.GetAvatar(),
	}
	return rcmdRole
}

func (assembler *AIRoleAssembler) BuildRcmdGroupTemplateInfo(groupTemplate *aigc_group.GroupTemplate) *api.GroupDetailInfo {
	groupInfo := &api.GroupDetailInfo{
		Name:           groupTemplate.GetName(),
		Character:      groupTemplate.GetCharacter(),
		Tags:           groupTemplate.GetTags(),
		Avatar:         groupTemplate.GetAvatar(),
		Sex:            groupTemplate.GetSex(),
		ChatBackground: groupTemplate.GetChatBackgroundImg(),
		HomeBackground: groupTemplate.GetHomeBackgroundImg(),
		GroupIcon:      groupTemplate.GetGroupIcon(),
		TemplateId:     groupTemplate.GetId(),
	}
	return groupInfo
}

func (assembler *AIRoleAssembler) BuildSearchRoleInfo(role *aigc_soulmate.AIRole) *api.AIRole {
	likeNum := int32(role.GetUserLikeNum()) + role.GetConfigLikeNum()
	if likeNum <= 0 {
		likeNum = int32(role.GetUserLikeNum())
	}
	searchRole := &api.AIRole{
		Id:        role.GetId(),
		Type:      uint32(role.GetType()),
		Name:      role.GetName(),
		Image:     role.GetImage(),
		Sex:       role.GetSex(),
		LikeNum:   uint32(likeNum),
		Tags:      role.GetTags(),
		Character: role.GetCharacter(),
		Avatar:    role.GetAvatar(),
	}
	return searchRole
}

func (assembler *AIRoleAssembler) BuildSearchTemplateGroupInfo(template *aigc_group.GroupTemplate, likeCount int32) *api.GroupDetailInfo {
	likeNum := likeCount + template.GetConfigLikeNum()
	if likeNum <= 0 {
		likeNum = likeCount
	}
	groupTemplate := &api.GroupDetailInfo{
		Id:             0, // 群实例的id
		Name:           template.GetName(),
		Character:      template.GetCharacter(),
		Tags:           template.GetTags(),
		Avatar:         template.GetAvatar(),
		Sex:            template.GetSex(),
		ChatBackground: template.GetChatBackgroundImg(),
		HomeBackground: template.GetHomeBackgroundImg(),
		GroupIcon:      template.GetGroupIcon(),
		LikeNum:        uint32(likeNum),
		TemplateId:     template.GetId(), // 群模版
		OwnerUid:       0,                // 群实例用户id
	}
	return groupTemplate
}

func (assembler *AIRoleAssembler) extraLoader(ctx context.Context, roles ...*aigc_soulmate.AIRole) (func(id uint32) *rcmd_business.RoleExtra, error) {
	roleIdList := make([]uint32, 0, len(roles))
	for _, role := range roles {
		roleIdList = append(roleIdList, role.GetId())
	}

	req := &rcmd_business.BatchGetRoleExtraReq{
		RoleIds: roleIdList,
	}
	resp, err := assembler.rcmdBusinessClient.BatchGetRoleExtra(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "extraLoader BatchGetRoleExtra req(%+v) err: %v", req, err)
		return nil, err
	}

	extraMap := make(map[uint32]*rcmd_business.RoleExtra)
	for _, extra := range resp.GetRoleExtras() {
		extraMap[extra.GetRoleId()] = extra
	}

	return func(id uint32) *rcmd_business.RoleExtra {
		return extraMap[id]
	}, nil
}

func (assembler *AIRoleAssembler) likeLoader(ctx context.Context, roles ...*aigc_soulmate.AIRole) (func(id uint32) aigc_soulmate.AIRoleLikeInfo_LikeState, error) {
	roleIdList := make([]uint32, 0, len(roles))
	for _, role := range roles {
		roleIdList = append(roleIdList, role.GetId())
	}

	req := &aigc_soulmate.GetUserAIRoleLikesReq{
		Uid:        metainfo.GetServiceInfo(ctx).UserID(),
		RoleIdList: roleIdList,
	}
	resp, err := assembler.soulmateClient.GetUserAIRoleLikes(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "likeLoader GetUserAIRoleLikes req(%+v) err: %v", req, err)
		return nil, err
	}

	likeMap := make(map[uint32]aigc_soulmate.AIRoleLikeInfo_LikeState)
	for _, like := range resp.GetLikeList() {
		likeMap[like.GetId()] = like.GetState()
	}

	return func(id uint32) aigc_soulmate.AIRoleLikeInfo_LikeState {
		return likeMap[id]
	}, nil
}

func (assembler *AIRoleAssembler) creatorInfoLoader(ctx context.Context, reqUid uint32, roles ...*aigc_soulmate.AIRole) (func(id uint32) *api.CreatorInfo, error) {
	uidList := make([]uint32, 0, len(roles))
	repeatedMap := make(map[uint32]struct{})
	roleUidMap := make(map[uint32]uint32, len(roles))
	for _, role := range roles {

		if aigc_soulmate.CreatorInfoType(role.GetCreatorInfoType()) == aigc_soulmate.CreatorInfoType_CREATOR_INFO_TYPE_PUBLIC {
			var uid uint32
			if role.GetUid() == 0 && role.GetAppointUid() > 0 {
				uid = role.GetAppointUid()

			} else if role.GetUid() > 0 {
				uid = role.GetUid()
			} else {
				continue
			}
			roleUidMap[role.GetId()] = uid
			if _, ok := repeatedMap[role.GetUid()]; ok {
				continue
			}
			uidList = append(uidList, uid)
			repeatedMap[uid] = struct{}{}
		}
	}
	if len(uidList) == 0 {
		return func(id uint32) *api.CreatorInfo {
			return nil
		}, nil
	}

	accountInfoMap, err := assembler.accountClient.GetUsersMap(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "creatorInfoLoader GetUsersMap req(%+v) err: %v", uidList, err)
		return nil, err
	}

	followMap := make(map[uint32]bool, len(uidList))
	var lowErr error
	if len(uidList) != 0 {
		followMap, lowErr = getFollowStatus(ctx, reqUid, uidList, assembler.friendshipClient)
		if lowErr != nil {
			log.ErrorWithCtx(ctx, "creatorInfoLoader getFollowStatus reqUid:%d lowErr:%v", reqUid, lowErr)
		}
	}

	return func(id uint32) *api.CreatorInfo {
		if roleUid, ok := roleUidMap[id]; !ok {
			return nil
		} else {
			accountInfo, ok := accountInfoMap[roleUid]
			if !ok {
				return nil
			}
			return &api.CreatorInfo{
				Uid:      accountInfo.GetUid(),
				Name:     accountInfo.GetNickname(),
				Account:  accountInfo.GetUsername(),
				IsFollow: followMap[accountInfo.GetUid()],
			}
		}
	}, nil
}

func buildRelationRoleList(ctx context.Context, soulmateClient aigc_soulmate.AigcSoulmateClient, aiRole *aigc_soulmate.AIRole) ([]*api.RelationRoleInfo, error) {
	var roleList []*api.RelationRoleInfo
	if aiRole == nil {
		return roleList, nil
	}

	roleList, err := getRelationRoleList(ctx, soulmateClient, aiRole)
	if err != nil {
		return roleList, err
	}

	// web端展示推荐角色的数量是5个，如果关联角色少于5个，需要补充同创作者角色（包含系统归属）
	if len(roleList) < RelationRoleNum {
		sameCreatorRoles, err := getSameCreatorRoleList(ctx, soulmateClient, aiRole, roleList)
		if err != nil {
			return roleList, err
		}

		roleList = append(roleList, sameCreatorRoles...)
	}

	// 只展示5个
	if len(roleList) > RelationRoleNum {
		roleList = roleList[:RelationRoleNum]
	}

	return roleList, nil
}

func getSameCreatorRoleList(ctx context.Context, soulmateClient aigc_soulmate.AigcSoulmateClient, aiRole *aigc_soulmate.AIRole, existingRoles []*api.RelationRoleInfo) ([]*api.RelationRoleInfo, error) {
	// 如果归属ID不为空，则使用归属ID获取同创作者角色
	uid := aiRole.GetUid()
	if aiRole.GetAppointUid() != 0 {
		uid = aiRole.GetAppointUid()
	}

	if uid == 0 {
		return nil, nil
	}

	// 因为关联的角色可能是同作者的，所以这里拉取同创作者的角色时，需要尽量涵盖关联的同作者的角色
	// limit 设置 RelationRoleNum + 1 个，是为了获取到 除了关联角色和当前角色之外的同创作者角色
	req := &aigc_soulmate.GetUserAIRoleListWithAppointReq{
		Uid:      uid,
		RoleType: aigc_soulmate.AIRoleType_AIRoleTypeGame,
		Limit:    uint32(RelationRoleNum + 1),
	}
	userRoleList, err := soulmateClient.GetUserAIRoleListWithAppoint(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIRole GetUserAIRoleListWithAppoint uid(%d) err: %v", uid, err)
		return nil, err
	}

	if len(userRoleList.GetRoleList()) == 0 {
		return nil, nil
	}
	// 去掉关联角色和当前角色
	existingRoleIDs := make(map[uint32]struct{}, len(existingRoles)+1)
	for _, role := range existingRoles {
		existingRoleIDs[role.GetId()] = struct{}{}
	}
	existingRoleIDs[aiRole.GetId()] = struct{}{}

	var filteredRoles []*aigc_soulmate.AIRole
	for _, role := range userRoleList.GetRoleList() {
		if _, exists := existingRoleIDs[role.GetId()]; !exists {
			filteredRoles = append(filteredRoles, role)
		}
	}

	return assemblerRelationInfo(filteredRoles, api.RelationType_RELATION_TYPE_SAME_CREATOR), nil
}

func getRelationRoleList(ctx context.Context, soulmateClient aigc_soulmate.AigcSoulmateClient, aiRole *aigc_soulmate.AIRole) ([]*api.RelationRoleInfo, error) {
	if len(aiRole.GetRelationIds()) == 0 {
		log.InfoWithCtx(ctx, "GetAIRole GetAIRoleList empty relationIds, aiRole: %+v", aiRole)
		return nil, nil
	}
	relationRoleList, err := soulmateClient.GetAIRoleList(ctx, &aigc_soulmate.GetAIRoleListReq{
		RoleIdList: aiRole.GetRelationIds(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAIRole GetAIRoleList fail, aiRole(%+v) err: %v", aiRole, err)
		return nil, err
	}

	// 关联后的角色可能会被设置为私有或者不曝光，这里需要过滤掉没有公开和不曝光的角色
	var relationPublicRoleList []*aigc_soulmate.AIRole
	for _, role := range relationRoleList.GetRoleList() {
		if role.State == aigc_soulmate.AIRoleState_AIRoleStatePublic && role.Exposed {
			relationPublicRoleList = append(relationPublicRoleList, role)
		}
	}

	// 按照更新时间降序排序
	sort.Slice(relationPublicRoleList, func(i, j int) bool {
		return relationPublicRoleList[i].GetUpdatedAt() > relationPublicRoleList[j].GetUpdatedAt()
	})

	return assemblerRelationInfo(relationPublicRoleList, api.RelationType_RELATION_TYPE_RELATE), nil
}

func assemblerRelationInfo(roleList []*aigc_soulmate.AIRole, relationType api.RelationType) []*api.RelationRoleInfo {
	var relationList []*api.RelationRoleInfo
	if len(roleList) == 0 {
		return relationList
	}
	for _, role := range roleList {
		relationList = append(relationList, &api.RelationRoleInfo{
			Id:           role.GetId(),
			Type:         uint32(role.GetType()),
			Name:         role.Name,
			State:        uint32(role.State),
			Avatar:       role.GetAvatar(),
			Image:        role.Image,
			Exposed:      role.Exposed,
			RelationType: relationType,
		})
	}
	return relationList
}

func getFollowStatus(ctx context.Context, uid uint32, uids []uint32, friendshipClient friendship.IClient) (map[uint32]bool, error) {

	repeatedMap := make(map[uint32]bool)
	repeatedUids := make([]uint32, 0, len(uids))
	for _, id := range uids {
		if !repeatedMap[id] && id != uid {
			repeatedMap[id] = true
			repeatedUids = append(repeatedUids, id)
		}
	}
	if len(repeatedUids) == 0 {
		return nil, nil
	}
	stateMap := make(map[uint32]bool, len(repeatedUids))
	//get follow status
	followingMap, _, err := friendshipClient.BatchGetBiFollowing(ctx, uid, repeatedUids, true, false)
	if err != nil {
		return stateMap, err
	}
	for posterId, following := range followingMap {
		if following != nil {
			stateMap[posterId] = true
		}
	}
	return stateMap, nil
}

// AIPartnerAssembler 组装伴侣返回数据
type AIPartnerAssembler struct {
	soulmateClient     aigc_soulmate.AigcSoulmateClient
	rcmdBusinessClient rcmd_business.BusinessAIPartnerClient
	accountClient      account.IClient
	friendshipClient   friendship.IClient
}

func NewAIPartnerAssembler(
	soulmateClient aigc_soulmate.AigcSoulmateClient,
	rcmdBusinessClient rcmd_business.BusinessAIPartnerClient,
	accountClient account.IClient,
	friendshipClient friendship.IClient,
) *AIPartnerAssembler {
	return &AIPartnerAssembler{
		soulmateClient:     soulmateClient,
		rcmdBusinessClient: rcmdBusinessClient,
		accountClient:      accountClient,
		friendshipClient:   friendshipClient,
	}
}

func (assembler *AIPartnerAssembler) BuildAIPartnerList(ctx context.Context, partners ...*aigc_soulmate.AIPartner) ([]*api.AIPartner, error) {
	if len(partners) == 0 {
		return nil, nil
	}

	loadDetail, err := assembler.detailLoader(ctx, partners...)
	if err != nil {
		return nil, err
	}

	loadLike, err := assembler.likeLoader(ctx, partners...)
	if err != nil {
		return nil, err
	}

	roleOwnerIds := make([]uint32, 0, len(partners))
	repeatedMap := make(map[uint32]struct{})

	for _, partner := range partners {
		role := partner.GetRole()
		if _, ok := repeatedMap[role.GetId()]; ok {
			continue
		}
		if role.GetUid() > 0 {
			roleOwnerIds = append(roleOwnerIds, role.GetUid())
			repeatedMap[role.GetUid()] = struct{}{}
		} else if role.GetAppointUid() > 0 {
			roleOwnerIds = append(roleOwnerIds, role.GetAppointUid())
			repeatedMap[role.GetAppointUid()] = struct{}{}
		}
	}
	roleOwnerMap := make(map[uint32]*account.User)
	if len(roleOwnerIds) > 0 {
		roleOwnerMap, err = assembler.accountClient.GetUsersMap(ctx, roleOwnerIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "BuildAIPartnerList GetUsersMap req(%+v) err: %v", roleOwnerIds, err)
			return nil, err
		}
	}
	reqUid := metainfo.GetServiceInfo(ctx).UserID()
	followMap, err := getFollowStatus(ctx, reqUid, roleOwnerIds, assembler.friendshipClient)
	if err != nil {
		log.ErrorWithCtx(ctx, "BuildAIPartner getFollowStatus err: %v", err)
		return nil, err
	}
	apiPartnerList := make([]*api.AIPartner, 0, len(partners))
	for _, partner := range partners {
		role := partner.GetRole()
		relationRoles, err := buildRelationRoleList(ctx, assembler.soulmateClient, role)
		if err != nil {
			log.WarnWithCtx(ctx, "BuildAIPartnerList buildRelationRoleList fail, roleId(%d) err: %v", role.GetId(), err)
		}

		apiRole := &api.AIPartner_Role{
			Id:              role.GetId(),
			Type:            uint32(role.GetType()),
			Sex:             role.GetSex(),
			Name:            role.GetName(),
			State:           uint32(role.GetState()),
			Avatar:          role.GetAvatar(),
			Image:           role.GetImage(),
			DialogColor:     role.GetDialogColor(),
			Character:       role.GetCharacter(),
			Prologue:        role.GetPrologue(),
			PrologueAudio:   role.GetPrologueAudio(),
			Timbre:          role.GetTimbre(),
			AuditResult:     uint32(role.GetAuditResult()),
			EnableRcmdReply: role.GetEnableRcmdReply(),
			StoryId:         role.GetStoryId(),
			CreatorUid:      role.GetUid(),
			Exposed:         role.GetExposed(),
			StoryMode:       uint32(role.GetStoryMode()),
			UserRoleSetting: role.GetUserRoleSetting(),
			CreatorInfoType: role.GetCreatorInfoType(),
			Scope:           uint32(role.GetScope()),
		}
		if api.CreatorInfoType(apiRole.GetCreatorInfoType()) == api.CreatorInfoType_CREATOR_INFO_TYPE_PUBLIC {
			uid := role.GetUid()
			if uid == 0 && role.GetAppointUid() > 0 {
				uid = role.GetAppointUid()
			}
			apiRole.CreatorInfo = &api.CreatorInfo{
				Uid:      uid,
				Name:     roleOwnerMap[uid].GetNickname(),
				Account:  roleOwnerMap[uid].GetUsername(),
				IsFollow: followMap[uid],
			}
		}
		apiPartner := &api.AIPartner{
			Id:            partner.GetId(),
			Name:          partner.GetName(),
			CallName:      partner.GetCallName(),
			Role:          apiRole,
			Silent:        partner.GetSilent(),
			ChangeRoleCnt: partner.GetChangeRoleCnt(),
			Source:        uint32(partner.GetSource()),
			UnsetName:     partner.GetUnsetName(),
			UnsetRole:     partner.GetUnsetRole(),
			RelationRoles: relationRoles,
		}
		if detail := loadDetail(partner.GetId()); detail != nil {
			apiRole.Ext = detail.GetRoleExt()
			if detail.GetShowHint() {
				apiPartner.Hint = detail.GetHint()
				apiPartner.ShowHint = true
			}
			if photograph := detail.GetCurPhotograph(); photograph != nil {
				apiPartner.Photograph = &api.AIPhotograph{
					Id:     photograph.GetId(),
					Avatar: photograph.GetAvatar(),
					Image:  photograph.GetImage(),
				}

				apiRole.Image = photograph.GetImage()
				apiRole.Avatar = photograph.GetAvatar()
			}
			if relation := detail.GetRelationship(); relation != nil {
				apiPartner.Relation = &api.AIRelationship{
					Id:   relation.GetId(),
					Name: relation.GetName(),
				}
			}
		}
		if likeState := loadLike(role.GetId()); likeState != aigc_soulmate.AIRoleLikeInfo_LikeStateUnspecified {
			apiRole.LikeState = uint32(likeState)

			likeNum := role.GetConfigLikeNum() + int32(role.GetUserLikeNum())
			if likeNum > 0 {
				apiRole.LikeNum = uint32(likeNum)
			} else if likeNum <= 0 {
				if likeState == aigc_soulmate.AIRoleLikeInfo_LikeStateLiked {
					apiRole.LikeNum = 1
				} else {
					apiRole.LikeNum = 0
				}
			} else {
				apiRole.LikeNum = 0
			}

		}

		apiPartnerList = append(apiPartnerList, apiPartner)
	}

	return apiPartnerList, nil
}

func (assembler *AIPartnerAssembler) BuildAIPartner(ctx context.Context, partner *aigc_soulmate.AIPartner) (*api.AIPartner, error) {
	list, err := assembler.BuildAIPartnerList(ctx, partner)
	if err != nil || len(list) == 0 {
		return nil, err
	}

	return list[0], nil
}

func (assembler *AIPartnerAssembler) BuildChattingPartnerList(ctx context.Context, partners ...*aigc_soulmate.AIPartner) ([]*api.ChattingPartner, error) {
	if len(partners) == 0 {
		return nil, nil
	}

	apiPartnerList := make([]*api.ChattingPartner, 0, len(partners))
	for _, partner := range partners {
		role := partner.GetRole()

		apiRole := &api.ChattingPartner_Role{
			Id:          role.GetId(),
			Sex:         role.GetSex(),
			Type:        uint32(role.GetType()),
			Name:        role.GetName(),
			State:       uint32(role.GetState()),
			Avatar:      role.GetAvatar(),
			Exposed:     role.GetExposed(),
			CreatorUid:  role.GetUid(),
			AuditResult: uint32(role.GetAuditResult()),
		}
		apiPartner := &api.ChattingPartner{
			Id:   partner.GetId(),
			Name: partner.GetName(),

			Role: apiRole,
		}

		apiPartnerList = append(apiPartnerList, apiPartner)
	}

	return apiPartnerList, nil
}

func (assembler *AIPartnerAssembler) BuildPetPartner(ctx context.Context, partner *aigc_soulmate.AIPartner) (*api.PetPartner, error) {
	if partner == nil {
		return nil, nil
	}

	loadDetail, err := assembler.detailLoader(ctx, partner)
	if err != nil {
		return nil, err
	}

	role := partner.GetRole()

	petRole := &api.PetPartner_Role{
		Id:     role.GetId(),
		Type:   uint32(role.GetType()),
		Sex:    role.GetSex(),
		Avatar: role.GetAvatar(),
	}
	petPartner := &api.PetPartner{
		Id:       partner.GetId(),
		Source:   uint32(partner.GetSource()),
		Role:     petRole,
		Name:     partner.GetName(),
		CallName: partner.GetCallName(),
	}
	if detail := loadDetail(partner.GetId()); detail != nil {
		petRole.Ext = detail.GetRoleExt()

		for _, customVoice := range detail.GetCustomVoices() {
			petPartner.CustomVoices = append(petPartner.CustomVoices, &api.CustomVoice{
				Id:     customVoice.GetId(),
				Weight: customVoice.GetWeight(),
			})
		}
	}

	return petPartner, nil
}

func (assembler *AIPartnerAssembler) detailLoader(ctx context.Context, partners ...*aigc_soulmate.AIPartner) (func(id uint32) *rcmd_business.PartnerInfo, error) {
	partnerIdList := make([]uint32, 0, len(partners))
	for _, partner := range partners {
		partnerIdList = append(partnerIdList, partner.GetId())
	}

	req := &rcmd_business.BatchGetPartnerInfoReq{
		PartnerIds: partnerIdList,
		Uid:        metainfo.GetServiceInfo(ctx).UserID(),
	}
	resp, err := assembler.rcmdBusinessClient.BatchGetPartnerInfo(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "WithDetail BatchGetPartnerInfo req(%+v) err: %v", req, err)
		return nil, err
	}

	detailMap := make(map[uint32]*rcmd_business.PartnerInfo)
	for _, detail := range resp.GetPartnerInfoList() {
		detailMap[detail.GetId()] = detail
	}

	return func(id uint32) *rcmd_business.PartnerInfo {
		return detailMap[id]
	}, nil
}

func (assembler *AIPartnerAssembler) likeLoader(ctx context.Context, partners ...*aigc_soulmate.AIPartner) (func(id uint32) aigc_soulmate.AIRoleLikeInfo_LikeState, error) {
	var (
		roleIdSet  = make(map[uint32]struct{})
		roleIdList = make([]uint32, 0, len(partners))
	)
	for _, partner := range partners {
		if _, ok := roleIdSet[partner.GetRole().GetId()]; !ok {
			roleIdSet[partner.GetRole().GetId()] = struct{}{}
			roleIdList = append(roleIdList, partner.GetRole().GetId())
		}
	}

	req := &aigc_soulmate.GetUserAIRoleLikesReq{
		Uid:        metainfo.GetServiceInfo(ctx).UserID(),
		RoleIdList: roleIdList,
	}
	resp, err := assembler.soulmateClient.GetUserAIRoleLikes(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "likeLoader GetUserAIRoleLikes req(%+v) err: %v", req, err)
		return nil, err
	}

	likeMap := make(map[uint32]aigc_soulmate.AIRoleLikeInfo_LikeState)
	for _, like := range resp.GetLikeList() {
		likeMap[like.GetId()] = like.GetState()
	}

	return func(id uint32) aigc_soulmate.AIRoleLikeInfo_LikeState {
		return likeMap[id]
	}, nil
}

func AssembleAIRoleCategory(category *aigc_soulmate.AIRoleCategory) *api.AIRoleCategory {
	if category == nil {
		return nil
	}

	apiCategory := &api.AIRoleCategory{
		Id:         category.GetId(),
		Title:      category.GetTitle(),
		PropScenes: make([]uint32, 0, len(category.GetPropScenes())),
		Props:      make([]*api.AIRoleCategory_Prop, 0, len(category.GetProps())),
	}
	for _, scene := range category.GetPropScenes() {
		apiCategory.PropScenes = append(apiCategory.PropScenes, uint32(scene))
	}
	for _, prop := range category.GetProps() {
		p := &api.AIRoleCategory_Prop{
			Id:               prop.GetId(),
			Name:             prop.GetName(),
			Labels:           make([]*api.AIRoleCategory_Label, 0, len(prop.GetLabels())),
			LabelSelectLimit: prop.GetLabelSelectLimit(),
		}
		for _, label := range prop.GetLabels() {
			l := &api.AIRoleCategory_Label{
				Id:     label.GetId(),
				Name:   label.GetName(),
				Scenes: make([]uint32, 0, len(label.GetScenes())),
			}
			for _, scene := range label.GetScenes() {
				l.Scenes = append(l.Scenes, uint32(scene))
			}
			p.Labels = append(p.Labels, l)
		}
		apiCategory.Props = append(apiCategory.Props, p)
	}

	return apiCategory
}

type InteractiveGameAssembler struct {
	soulmateClient aigc_soulmate.AigcSoulmateClient
}

func NewInteractiveGameAssembler(soulmateClient aigc_soulmate.AigcSoulmateClient) *InteractiveGameAssembler {
	return &InteractiveGameAssembler{
		soulmateClient: soulmateClient,
	}
}

func (assembler *InteractiveGameAssembler) BuildInteractiveGameList(ctx context.Context, games ...*aigc_soulmate.InteractiveGame) ([]*api.InteractiveGame, error) {
	if len(games) == 0 {
		return nil, nil
	}

	loadRole, err := assembler.roleLoader(ctx, games...)
	if err != nil {
		return nil, err
	}

	list := make([]*api.InteractiveGame, 0, len(games))
	for _, game := range games {
		role := loadRole(game.GetRoleId())
		list = append(list, &api.InteractiveGame{
			Id:         game.GetId(),
			RoleId:     game.GetRoleId(),
			TopicId:    game.GetTopicId(),
			Title:      game.GetTitle(),
			Desc:       game.GetDesc(),
			Prologue:   game.GetPrologue(),
			State:      uint32(game.GetState()),
			RoleName:   role.GetName(),
			RoleAvatar: role.GetAvatar(),
		})
	}

	return list, nil
}

func (assembler *InteractiveGameAssembler) roleLoader(ctx context.Context, games ...*aigc_soulmate.InteractiveGame) (func(id uint32) *aigc_soulmate.AIRole, error) {
	roleIdSet := make(map[uint32]struct{})
	roleIdList := make([]uint32, 0, len(games))
	for _, game := range games {
		if _, ok := roleIdSet[game.GetRoleId()]; !ok {
			roleIdSet[game.GetRoleId()] = struct{}{}
			roleIdList = append(roleIdList, game.GetRoleId())
		}
	}

	req := &aigc_soulmate.GetAIRoleListReq{
		RoleIdList: roleIdList,
	}
	resp, err := assembler.soulmateClient.GetAIRoleList(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "roleLoader GetAIRoleList req(%+v) err: %v", req, err)
		return nil, err
	}

	roleMap := make(map[uint32]*aigc_soulmate.AIRole)
	for _, role := range resp.GetRoleList() {
		roleMap[role.GetId()] = role
	}

	return func(id uint32) *aigc_soulmate.AIRole {
		return roleMap[id]
	}, nil
}
