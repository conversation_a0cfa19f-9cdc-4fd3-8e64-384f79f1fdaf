package internal

import (
	"context"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/photo_album_go"
	account_go "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/log"
	aigc_account_middle "golang.52tt.com/protocol/services/aigc/aigc-account-middle"
	api "golang.52tt.com/protocol/services/aigc/aigc-http-logic"
	"golang.52tt.com/services/helper-from-cpp/albumhelper"
	obs_helper "golang.52tt.com/services/helper-from-cpp/obs-helper"
	"strings"
)

const (
	ChatCardWallShowStatusDisplay = 1
	AiAccountListLimit            = 30
)

func (s *Server) GetAiAccountList(ctx context.Context, req *api.GetAiAccountListRequest) (*api.GetAiAccountListResponse, error) {
	resp := new(api.GetAiAccountListResponse)

	accountListResp, err := s.clients.AigcAccountMiddle.GetPageAIAccount(ctx, &aigc_account_middle.GetPageAIAccountRequest{
		Size:                   AiAccountListLimit,
		ChatCardWallShowStatus: ChatCardWallShowStatusDisplay,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPageAIAccount rpc req:%s err: %v", req.String(), err)
		return resp, err
	}
	uidList := make([]uint32, 0, len(accountListResp.GetAccountList()))
	for _, account := range accountListResp.GetAccountList() {
		uidList = append(uidList, account.GetUid())
	}

	userMap, err := s.dataLoader.UserLoader(ctx, uidList...)
	if err != nil {
		log.WarnWithCtx(ctx, "GetPageAIAccount UserLoader uidList(%+v) err", uidList, err)
	}
	userPhotoAlbumMap := s.BatchGetPhotoAlbum(ctx, uidList)

	resp.Accounts = make([]*api.AiAccount, 0, len(accountListResp.GetAccountList()))
	for _, account := range accountListResp.GetAccountList() {
		user := userMap(account.GetUid())
		if user == nil {
			user = &account_go.User{}
		}

		resp.Accounts = append(resp.Accounts, &api.AiAccount{
			Uid:             account.GetUid(),
			Nickname:        account.GetNickname(),
			Account:         user.GetUsername(),
			Sex:             account.GetSex(),
			Signature:       account.GetSignature(),
			AccountTags:     account.GetAccountTags(),
			BackgroundImage: s.getBackgroundImage(userPhotoAlbumMap, account.GetUid()),
		})
	}
	log.InfoWithCtx(ctx, "GetAiAccountList success req:%s", req.String())
	return resp, nil
}

func (s *Server) BatchGetPhotoAlbum(ctx context.Context, uidList []uint32) map[uint32]*photo_album_go.ImgKeyInfo {
	photoAlbumResp, err := s.clients.PhotoAlbumClient.BatchGetPhotoAlbum(ctx, &photo_album_go.BatchGetPhotoAlbumRequest{
		UidList: uidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetPhotoAlbum rpc uidList:%s err: %v", uidList, err)
		return map[uint32]*photo_album_go.ImgKeyInfo{}
	}
	userPhotoAlbumMap := photoAlbumResp.GetPhotoAlbumMap()
	return userPhotoAlbumMap
}

func (s *Server) getBackgroundImage(userPhotoAlbumMap map[uint32]*photo_album_go.ImgKeyInfo, targetUid uint32) string {

	fn := func(splited []string) (keys []string) {
		for _, key := range splited {
			if key != "" {
				keys = append(keys, key)
			}
		}
		return keys
	}
	imgKeys := fn(strings.Split(userPhotoAlbumMap[targetUid].GetImgKeys(), ";"))
	newImgKeys := fn(strings.Split(userPhotoAlbumMap[targetUid].GetNewImgKeys(), ";"))
	imageUrl := ""

	if len(imgKeys) != 0 {

		imageUrl = albumhelper.ToLongPhotoUrl(imgKeys[0], 0, 0)
	} else {

		if len(newImgKeys) > 0 {
			imageUrl = obs_helper.GetObsUrl("photo-album", newImgKeys[0], 0, 0)
		}
	}
	return imageUrl
}
