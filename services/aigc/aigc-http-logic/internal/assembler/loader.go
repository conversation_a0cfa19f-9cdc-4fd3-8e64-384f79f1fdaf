package assembler

import (
	"context"
	config "golang.52tt.com/services/aigc/aigc-http-logic/internal/config/aigc_http_logic"
	
	account_go "golang.52tt.com/clients/account-go"
	aigc_common "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_group "golang.52tt.com/protocol/services/aigc/aigc-group"
	api "golang.52tt.com/protocol/services/aigc/aigc-http-logic"
	aigc_soulmate "golang.52tt.com/protocol/services/aigc/aigc-soulmate"
)

type DataLoader struct {
	accountClient account_go.IClient
	
	aigcGroupClient    aigc_group.AigcGroupClient
	aigcCommonClient   aigc_common.AigcCommonClient
	aigcSoulmateClient aigc_soulmate.AigcSoulmateClient
}

type commonObject struct {
	id  uint32
	typ aigc_common.ObjectType
}

func NewDataLoader(
	accountClient account_go.IClient,
	
	aigcGroupClient aigc_group.AigcGroupClient,
	aigcCommonClient aigc_common.AigcCommonClient,
	aigcSoulmateClient aigc_soulmate.AigcSoulmateClient,
) *DataLoader {
	return &DataLoader{
		accountClient: accountClient,
		
		aigcGroupClient:    aigcGroupClient,
		aigcCommonClient:   aigcCommonClient,
		aigcSoulmateClient: aigcSoulmateClient,
	}
}

func (dl *DataLoader) UserLoader(ctx context.Context, uidList ...uint32) (func(id uint32) *account_go.User, error) {
	if len(uidList) == 0 {
		return func(id uint32) *account_go.User { return nil }, nil
	}
	
	userMap, err := dl.accountClient.GetUsersMap(ctx, uidList)
	if err != nil {
		return nil, err
	}
	
	return func(id uint32) *account_go.User {
		return userMap[id]
	}, nil
}

func (dl *DataLoader) AIRoleLoader(ctx context.Context, roleIdList ...uint32) (func(id uint32) *aigc_soulmate.AIRole, error) {
	if len(roleIdList) == 0 {
		return func(id uint32) *aigc_soulmate.AIRole { return nil }, nil
	}
	
	listReq := &aigc_soulmate.GetAIRoleListReq{
		RoleIdList: roleIdList,
	}
	listResp, err := dl.aigcSoulmateClient.GetAIRoleList(ctx, listReq)
	if err != nil {
		return nil, err
	}
	
	m := make(map[uint32]*aigc_soulmate.AIRole)
	for _, role := range listResp.GetRoleList() {
		m[role.GetId()] = role
	}
	
	return func(id uint32) *aigc_soulmate.AIRole {
		return m[id]
	}, nil
}

func (dl *DataLoader) AIGroupTemplateLoader(ctx context.Context, templateIdList ...uint32) (func(id uint32) *aigc_group.GroupTemplate, error) {
	if len(templateIdList) == 0 {
		return func(id uint32) *aigc_group.GroupTemplate { return nil }, nil
	}
	
	listReq := &aigc_group.GetGroupTemplateByIdsRequest{
		Ids: templateIdList,
	}
	listResp, err := dl.aigcGroupClient.GetGroupTemplateByIds(ctx, listReq)
	if err != nil {
		return nil, err
	}
	
	m := make(map[uint32]*aigc_group.GroupTemplate)
	for _, template := range listResp.GetList() {
		m[template.GetId()] = template
	}
	
	return func(id uint32) *aigc_group.GroupTemplate {
		return m[id]
	}, nil
}

func (dl *DataLoader) AttitudeStateLoader(ctx context.Context, uid uint32, objectList ...commonObject) (func(object commonObject) api.LikeState, error) {
	if len(objectList) == 0 {
		return func(object commonObject) api.LikeState { return api.LikeState_LIKE_STATE_UNLIKED }, nil
	}
	
	// 目前只用到单个
	req := &aigc_common.HadAttitudeRequest{
		AttitudeObject: &aigc_common.Object{
			ObjectId:   objectList[0].id,
			ObjectType: objectList[0].typ,
		},
	}
	resp, err := dl.aigcCommonClient.HadAttitude(ctx, req)
	if err != nil {
		return nil, err
	}
	
	return func(object commonObject) api.LikeState {
		likeState := api.LikeState_LIKE_STATE_UNLIKED
		if resp.GetHadAttitude() {
			likeState = api.LikeState_LIKE_STATE_LIKED
		}
		
		return likeState
	}, nil
}

func (dl *DataLoader) AttitudeNumLoader(ctx context.Context, objectList ...commonObject) (func(object commonObject) uint32, error) {
	if len(objectList) == 0 {
		return func(object commonObject) uint32 { return 0 }, nil
	}
	
	req := &aigc_common.GetAttitudeCountRequest{}
	for _, object := range objectList {
		req.Objects = append(req.Objects, &aigc_common.Object{
			ObjectId:   object.id,
			ObjectType: object.typ,
		})
	}
	resp, err := dl.aigcCommonClient.GetAttitudeCount(ctx, req)
	if err != nil {
		return nil, err
	}
	
	m := make(map[commonObject]uint32)
	for _, info := range resp.GetAttitudeCountInfos() {
		key := commonObject{
			id:  info.GetObject().GetObjectId(),
			typ: info.GetObject().GetObjectType(),
		}
		
		m[key] = info.GetCount()
	}
	
	return func(object commonObject) uint32 {
		return m[object]
	}, nil
}

func (dl *DataLoader) RandomPlayersLoader(ctx context.Context, templateIds ...uint32) (func(templateId uint32) *api.ScriptItem_PlayingInfo, error) {
	if len(templateIds) == 0 {
		return func(templateId uint32) *api.ScriptItem_PlayingInfo { return nil }, nil
	}
	
	limit := config.GetAigcHttpLogicConfig().GetScriptListAvatarNum()
	if limit == 0 {
		return func(templateId uint32) *api.ScriptItem_PlayingInfo { return nil }, nil
	}
	resp, err := dl.aigcGroupClient.BatchGetRandMatchPlayer(ctx, &aigc_group.BatchGetRandMatchPlayerRequest{
		TemplId: templateIds,
		Limit:   uint32(limit),
	})
	if err != nil {
		return func(templateId uint32) *api.ScriptItem_PlayingInfo { return nil }, err
	}
	uidList := make([]uint32, 0)
	templateUidsMap := make(map[uint32][]uint32, len(templateIds))
	for templateId, playerList := range resp.GetPlayers() {
		for _, player := range playerList.GetList() {
			uidList = append(uidList, player.GetUid())
			templateUidsMap[templateId] = append(templateUidsMap[templateId], player.GetUid())
		}
	}
	
	var userMap map[uint32]*account_go.User
	if len(uidList) > 0 {
		userMap, err = dl.accountClient.GetUsersMap(ctx, uidList)
		if err != nil {
			return func(templateId uint32) *api.ScriptItem_PlayingInfo { return nil }, err
		}
	}
	
	m := make(map[uint32][]string, len(templateIds))
	fallBackAvatarsMap := make(map[uint32][]string, len(templateIds))
	for _, templateId := range templateIds {
		uids, ok := templateUidsMap[templateId]
		if ok {
			m[templateId] = make([]string, 0, limit)
			for _, uid := range uids {
				m[templateId] = append(m[templateId], userMap[uid].GetUsername())
			}
		}
		if len(uids) < limit {
			fallBackAvatarsMap[templateId] = config.GetAigcHttpLogicConfig().GetFallBackAvatars(limit - len(uids))
		}
	}
	
	return func(templateId uint32) *api.ScriptItem_PlayingInfo {
		return &api.ScriptItem_PlayingInfo{
			Accounts:           m[templateId],
			Text:               config.GetAigcHttpLogicConfig().GetRandomPlayTips(),
			FallBackAvatarUrls: fallBackAvatarsMap[templateId],
		}
	}, nil
}
