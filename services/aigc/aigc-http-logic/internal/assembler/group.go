package assembler

import (
	"context"
	"math/rand"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/metadata/metainfo"

	"golang.52tt.com/pkg/log"
	aigc_common "golang.52tt.com/protocol/services/aigc/aigc-common"
	aigc_group "golang.52tt.com/protocol/services/aigc/aigc-group"
	api "golang.52tt.com/protocol/services/aigc/aigc-http-logic"
	config "golang.52tt.com/services/aigc/aigc-http-logic/internal/config/aigc_http_logic"
)

type AIGroupAssembler struct {
	dataLoader *DataLoader
}

func NewAIGroupAssembler(dataLoader *DataLoader) *AIGroupAssembler {
	return &AIGroupAssembler{dataLoader: dataLoader}
}

func (as *AIGroupAssembler) BuildGroupInfo(ctx context.Context, group *aigc_group.Group) (*api.GroupInfo, error) {
	if group == nil {
		return nil, nil
	}

	uid := metainfo.GetServiceInfo(ctx).UserID()

	object := commonObject{
		id:  group.GetTemplateId(),
		typ: aigc_common.ObjectType_OBJECT_TYPE_GROUP_TEMPLATE,
	}
	loadAttitudeState, err := as.dataLoader.AttitudeStateLoader(ctx, uid, object)
	if err != nil {
		return nil, err
	}
	loadAttitudeNum, err := as.dataLoader.AttitudeNumLoader(ctx, object)
	if err != nil {
		return nil, err
	}

	loader, err := as.dataLoader.AIGroupTemplateLoader(ctx, group.GetTemplateId())
	if err != nil {
		return nil, err
	}
	template := loader(group.GetTemplateId())
	info := &api.GroupInfo{
		Id: group.GetId(),

		Source:     uint32(group.GetSource()),
		OwnerUid:   group.GetOwnerUid(),
		TemplateId: group.GetTemplateId(),

		LikeNum:        computeLikeNum(group.GetConfigLikeNum(), loadAttitudeNum(object), loadAttitudeState(object)),
		MemberNum:      group.GetMemberNum(),
		MaxRealUserNum: group.GetUserLimit(),

		LikeState:     uint32(loadAttitudeState(object)),
		LeaveStrategy: uint32(group.GetLeaveStrategy()),

		Sex:            group.GetSex(),
		Name:           group.GetName(),
		Desc:           group.GetDesc(),
		Avatar:         group.GetAvatar(),
		ChatBackground: group.GetChatBackground(),
		GroupType:      uint32(group.GetType()),

		QuickSpeakTexts: getRandQuickSpeakTexts(template.GetQuickSpeakTexts()),
	}

	if template.GetScriptInfo() != nil {
		info.BackgroundMusic = template.GetScriptInfo().GetBackgroundMusic()
		info.MatchStrategy = uint32(template.GetScriptInfo().GetMatchStrategy())
	}

	return info, nil
}

func (as *AIGroupAssembler) BuildGroupList(ctx context.Context, groups []*aigc_group.Group) ([]*api.GetGroupListResponse_Group, error) {
	if len(groups) == 0 {
		return nil, nil
	}

	list := make([]*api.GetGroupListResponse_Group, 0, len(groups))
	for _, group := range groups {
		list = append(list, &api.GetGroupListResponse_Group{
			Id:        group.GetId(),
			Sex:       group.GetSex(),
			Name:      group.GetName(),
			Avatar:    group.GetAvatar(),
			GroupType: uint32(group.GetType()),
		})
	}

	return list, nil
}

func (as *AIGroupAssembler) BuildGroupTemplateInfo(ctx context.Context, template *aigc_group.GroupTemplate) (*api.GroupTemplateInfo, error) {
	if template == nil {
		return nil, nil
	}

	uid := metainfo.GetServiceInfo(ctx).UserID()

	loadRole, err := as.dataLoader.AIRoleLoader(ctx, template.GetRoleIds()...)
	if err != nil {
		return nil, err
	}

	object := commonObject{
		id:  template.GetId(),
		typ: aigc_common.ObjectType_OBJECT_TYPE_GROUP_TEMPLATE,
	}
	loadAttitudeState, err := as.dataLoader.AttitudeStateLoader(ctx, uid, object)
	if err != nil {
		return nil, err
	}
	loadAttitudeNum, err := as.dataLoader.AttitudeNumLoader(ctx, object)
	if err != nil {
		return nil, err
	}

	info := &api.GroupTemplateInfo{
		Id: template.GetId(),

		LikeNum:   computeLikeNum(template.GetConfigLikeNum(), loadAttitudeNum(object), loadAttitudeState(object)),
		MemberNum: uint32(len(template.GetRoleIds())),

		LikeState: uint32(loadAttitudeState(object)),

		Sex:                 template.GetSex(),
		Name:                template.GetName(),
		Desc:                template.GetCharacter(),
		Avatar:              template.GetAvatar(),
		ChatBackground:      template.GetChatBackgroundImg(),
		GroupType:           uint32(template.GetGroupType()),
		DefaultPrologueList: ConvertDefaultPrologueListToPb(template.GetDefaultPrologues()),
		QuickSpeakTexts:     getRandQuickSpeakTexts(template.GetQuickSpeakTexts()),
	}

	if template.GetScriptInfo() != nil {
		info.ScriptInfo = &api.GroupTemplateInfo_ScriptInfo{
			BackgroundMusic: template.GetScriptInfo().GetBackgroundMusic(),
		}
	}
	for _, roleId := range template.GetRoleIds() {
		role := loadRole(roleId)
		if role == nil {
			log.WarnWithCtx(ctx, "BuildGroupTemplateInfo template(%d) role(%d) not found, skip", template.GetId(), roleId)
			continue
		}

		gr := &api.GroupRole{
			Id:        role.GetId(),
			Sex:       role.GetSex(),
			Name:      role.GetName(),
			Avatar:    role.GetAvatar(),
			Character: role.GetCharacter(),
		}
		for _, prologue := range role.GetGroupRoleConfig().GetPrologues() {
			gr.PrologueList = append(gr.PrologueList, &api.AIRolePrologue{
				Seq:   prologue.GetPriority(),
				Text:  prologue.GetText(),
				Audio: prologue.GetAudio(),
			})
		}

		info.RoleList = append(info.RoleList, gr)
	}

	return info, nil
}

func getRandQuickSpeakTexts(quickSpeakTexts []string) []string {
	if len(quickSpeakTexts) <= 3 {
		return quickSpeakTexts
	}
	rand.Shuffle(len(quickSpeakTexts), func(i, j int) {
		quickSpeakTexts[i], quickSpeakTexts[j] = quickSpeakTexts[j], quickSpeakTexts[i]
	})
	return quickSpeakTexts[:3]
}

func ConvertDefaultPrologueListToPb(list []*aigc_group.AIGroupPrologue) []*api.AIRolePrologue {
	if len(list) == 0 {
		return nil
	}
	prologueList := make([]*api.AIRolePrologue, 0, len(list))
	for _, item := range list {
		prologueList = append(prologueList, &api.AIRolePrologue{
			Text:  item.GetText(),
			Audio: item.GetAudio(),
			Seq:   item.GetPriority(),
		})
	}
	return prologueList
}

func (as *AIGroupAssembler) sortByMultiUserGroup(ctx context.Context, uid uint32, list []*api.GroupMember) []*api.GroupMember {
	listSort := make([]*api.GroupMember, 0, len(list))
	var myself *api.GroupMember
	var nofollowList, followList, aiRoleList []*api.GroupMember
	for _, item := range list {
		if aigc_group.GroupMemberType(item.GetType()) == aigc_group.GroupMemberType_GROUP_MEMBER_TYPE_USER {
			if item.GetId() == uid {
				myself = item
			} else if item.GetIsFollow() {
				followList = append(followList, item)
			} else {
				nofollowList = append(nofollowList, item)
			}
		} else {
			aiRoleList = append(aiRoleList, item)
		}
	}
	if myself != nil {
		listSort = append(listSort, myself)
	}

	if len(nofollowList) > 0 {
		listSort = append(listSort, nofollowList...)
	}

	if len(followList) > 0 {
		listSort = append(listSort, followList...)
	}

	if len(aiRoleList) > 0 {
		listSort = append(listSort, aiRoleList...)
	}

	log.DebugWithCtx(ctx, "sortByMultiUserGroup list:%+v, sortlist:%+v", list, listSort)
	return listSort
}

func (as *AIGroupAssembler) BuildGroupMemberList(ctx context.Context, members []*aigc_group.GroupMember, userSelectMap map[uint32]*aigc_group.UserSelectInfo,
	userRoleConfigMap map[uint32]*aigc_group.PlayRole, followMap map[uint32]bool, uid uint32, isMultiGroup bool, memberActiveTimeMap map[uint32]int64) ([]*api.GroupMember, error) {
	if len(members) == 0 {
		return nil, nil
	}

	log.InfoWithCtx(ctx, "======userSelectMap:%+v, userRoleConfigMap:%+v", userSelectMap, userRoleConfigMap)
	var (
		uidList    []uint32
		roleIdList []uint32
	)
	for _, member := range members {
		switch member.GetType() {
		case aigc_group.GroupMemberType_GROUP_MEMBER_TYPE_USER:
			uidList = append(uidList, member.GetId())
		case aigc_group.GroupMemberType_GROUP_MEMBER_TYPE_ROLE:
			roleIdList = append(roleIdList, member.GetId())
		}
	}

	loadUser, err := as.dataLoader.UserLoader(ctx, uidList...)
	if err != nil {
		return nil, err
	}

	loadRole, err := as.dataLoader.AIRoleLoader(ctx, roleIdList...)
	if err != nil {
		return nil, err
	}

	list := make([]*api.GroupMember, 0, len(members))
	for _, member := range members {
		switch member.GetType() {
		case aigc_group.GroupMemberType_GROUP_MEMBER_TYPE_USER:
			user := loadUser(member.GetId())
			if user == nil {
				log.WarnWithCtx(ctx, "BuildGroupMemberList user(%d) not found, skip", member.GetId())
				continue
			}

			userRole := &api.UserRole{}
			if userRoleConfigMap != nil && userSelectMap != nil {
				userRoleId := userSelectMap[member.GetId()].GetUserRoleId()
				userRole = &api.UserRole{
					RoleId:          userRoleId,
					RoleName:        userRoleConfigMap[userRoleId].GetName(),
					RoleAvatar:      userRoleConfigMap[userRoleId].GetAvatar(),
					RoleCharacter:   userRoleConfigMap[userRoleId].GetCharacter(),
					QuickSpeakTexts: getRandQuickSpeakTexts(userRoleConfigMap[userRoleId].GetQuickSpeakTexts()),
				}
				log.InfoWithCtx(ctx, "===test uid:%d, userRoleId:%d, userRoleMap[userRoleId]:%+v", member.GetId(), userRoleId, userRoleConfigMap[userRoleId])
			}

			list = append(list, &api.GroupMember{
				Id:             member.GetId(),
				Type:           uint32(member.GetType()),
				Name:           user.GetNickname(),
				Account:        user.GetUsername(),
				IsFollow:       followMap[member.GetId()],
				UserRole:       userRole,
				Sex:            user.GetSex(),
				JoinTime:       member.GetJoinedAt(),
				LastActiveTime: memberActiveTimeMap[member.GetId()],
			})
		case aigc_group.GroupMemberType_GROUP_MEMBER_TYPE_ROLE:
			role := loadRole(member.GetId())
			if role == nil {
				log.WarnWithCtx(ctx, "BuildGroupMemberList role(%d) not found, skip", member.GetId())
				continue
			}

			gm := &api.GroupMember{
				Id:        member.GetId(),
				Type:      uint32(member.GetType()),
				Name:      role.GetName(),
				Avatar:    role.GetAvatar(),
				Character: role.GetCharacter(),
				Sex:       role.GetSex(),
			}
			for _, prologue := range role.GetGroupRoleConfig().GetPrologues() {
				gm.PrologueList = append(gm.PrologueList, &api.AIRolePrologue{
					Seq:   prologue.GetPriority(),
					Text:  prologue.GetText(),
					Audio: prologue.GetAudio(),
				})
			}

			list = append(list, gm)
		default:
			log.WarnWithCtx(ctx, "BuildGroupMemberList invalid MemberType(%d), skip", member.GetType())
			continue
		}
	}
	if isMultiGroup {
		list = as.sortByMultiUserGroup(ctx, uid, list)
	}

	return list, nil
}

func computeLikeNum(configLikeNum int32, userLikeNum uint32, userLikeState api.LikeState) uint32 {
	likeNum := configLikeNum + int32(userLikeNum)
	if likeNum < 0 {
		likeNum = 0
	}
	if likeNum == 0 && userLikeState == api.LikeState_LIKE_STATE_LIKED {
		likeNum = 1
	}

	return uint32(likeNum)
}

func (as *AIGroupAssembler) BuildScriptInfoList(ctx context.Context, templates []*aigc_group.GroupTemplate) ([]*api.ScriptItem, error) {
	if len(templates) == 0 {
		return nil, nil
	}
	templateIds := make([]uint32, 0, len(templates))
	for _, template := range templates {
		templateIds = append(templateIds, template.GetId())
	}
	playingInfoLoader, err := as.dataLoader.RandomPlayersLoader(ctx, templateIds...)
	if err != nil {
		log.WarnWithCtx(ctx, "BuildScriptInfoList RandomPlayersLoader templateIds:%v err: %v", templateIds, err)
	}

	list := make([]*api.ScriptItem, 0, len(templates))
	for _, template := range templates {
		info, err := as.BuildScriptInfo(ctx, template)
		if err != nil {
			log.ErrorWithCtx(ctx, "BuildScriptInfo err: %v", err)
			continue
		}
		if info != nil {
			info.PlayingInfo = playingInfoLoader(template.GetId())
			list = append(list, info)
		} else {
			log.InfoWithCtx(ctx, "BuildScriptInfoList info is nil template:%s", template.String())
		}
	}

	return list, nil
}

func (as *AIGroupAssembler) BuildScriptInfo(ctx context.Context, template *aigc_group.GroupTemplate) (*api.ScriptItem, error) {
	if template == nil ||
		(template.GetGroupType() != aigc_group.GroupType_GROUP_TYPE_MULTI_USER &&
			template.GetGroupType() != aigc_group.GroupType_GROUP_TYPE_MULTI_USER_SCRIPT) {
		return nil, nil
	}

	info := &api.ScriptItem{
		Id:                template.GetId(),
		Name:              template.GetName(),
		Character:         template.GetCharacter(),
		ButtonDisplayText: template.GetScriptInfo().GetButtonDisplayText(),
		HomeBackgroundImg: template.GetHomeBackgroundImg(),
		Tags:              template.GetTags(),
		CornerIcon:        template.GetCornerIcon(),
		SuitableSex:       template.GetSuitableSex(),
	}

	return info, nil
}

func (as *AIGroupAssembler) BuildHotBannerList(ctx context.Context, banners []*aigc_group.HotBannerInfo) (
	[]*api.GetScriptHotBannerResponse_BannerInfo, error) {
	if len(banners) == 0 {
		return nil, nil
	}
	templateIds := make([]uint32, 0, len(banners))
	for _, banner := range banners {
		templateIds = append(templateIds, banner.GetTemplateId())
	}

	loader, err := as.dataLoader.AIGroupTemplateLoader(ctx, templateIds...)
	if err != nil {
		log.ErrorWithCtx(ctx, "BuildHotBannerList AIGroupTemplateLoader templateIds:%v err: %v", templateIds, err)
		return nil, err
	}
	list := make([]*api.GetScriptHotBannerResponse_BannerInfo, 0, len(banners))
	for _, banner := range banners {
		templateInfo := loader(banner.GetTemplateId())
		if templateInfo == nil {
			log.WarnWithCtx(ctx, "BuildHotBannerList templateInfo(%d) not found, skip", banner.GetTemplateId())
			continue
		}
		list = append(list, &api.GetScriptHotBannerResponse_BannerInfo{
			Id:                banner.GetId(),
			TemplateId:        banner.GetTemplateId(),
			TopTitle:          banner.GetTopTitle(),
			SubTitle:          banner.GetSubTitle(),
			BackgroundImg:     banner.GetBannerBackgroundImg(),
			ButtonDisplayText: banner.GetButtonTitle(),
			ButtonColor:       banner.GetButtonColor(),
			BarColor:          banner.GetBarColor(),
			Icon:              templateInfo.GetCornerIcon(),
			SuitableSex:       templateInfo.GetSuitableSex(),
		})
	}

	return list, nil
}

func (as *AIGroupAssembler) BuildStartMatchUserList(ctx context.Context, players []*aigc_group.MatchPlayer) ([]*api.StartScriptMatchResponse_User, error) {
	list := make([]*api.StartScriptMatchResponse_User, 0, 6)

	if len(players) > 0 {
		uidList := make([]uint32, 0, len(players))
		for _, player := range players {
			uidList = append(uidList, player.GetUid())
		}

		loadUser, err := as.dataLoader.UserLoader(ctx, uidList...)
		if err != nil {
			log.ErrorWithCtx(ctx, "BuildMatchUserList UserLoader uidList(%+v) err", uidList, err)
			return nil, err
		}

		for _, player := range players {
			user := loadUser(player.GetUid())
			if user != nil {
				list = append(list, &api.StartScriptMatchResponse_User{
					Account: user.GetUsername(),
				})
			}
		}
	}

	if len(list) < 6 {
		for _, avatar := range config.GetAigcHttpLogicConfig().GetFallBackAvatars(6 - len(list)) {
			list = append(list, &api.StartScriptMatchResponse_User{
				Avatar: avatar,
			})
		}
	}

	return list, nil
}
