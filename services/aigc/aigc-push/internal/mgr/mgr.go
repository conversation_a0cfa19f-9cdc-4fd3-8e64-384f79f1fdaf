package mgr

import (
	"context"

	pushPkg "gitlab.ttyuyin.com/bizFund/bizFund/pkg/push-notification"

	gaPush "golang.52tt.com/protocol/app/push"
	pb "golang.52tt.com/protocol/services/aigc/aigc-push"
	push_server "golang.52tt.com/protocol/services/push-notification/v3"
	"golang.52tt.com/services/aigc/aigc-push/internal/entity"
)

type GroupMsgPusher interface {
	Push(ctx context.Context, sender *pb.Peer, group *entity.Group, msg *entity.ImMsg, opt *pb.PushOption) error
}

type PartnerMsgPusher interface {
	Push(ctx context.Context, partner *entity.Partner, msg *entity.ImMsg, opt *pb.PushOption) error
}

//go:generate mockgen -destination=mocks/push.go -package=mocks golang.52tt.com/services/aigc/aigc-push/internal/mgr Pusher
type Pusher interface {
	SendOnlinePush(ctx context.Context, uidList []uint32, cmd gaPush.PushMessage_CMD_TYPE, label pushPkg.PushLabelType, msg any) error
	SendOfflinePush(ctx context.Context, uidList []uint32, taskId string, notify *push_server.Notification) error
}

//go:generate mockgen -destination=mocks/tip.go -package=mocks golang.52tt.com/services/aigc/aigc-push/internal/mgr TipHandler
type TipHandler interface {
	GenPartnerTipMsg(ctx context.Context, tipType pb.TipType, uid uint32, partner *entity.Partner) (*entity.ImMsg, error)
	GenGroupTipMsg(ctx context.Context, tipType pb.TipType, uid uint32) (*entity.ImMsg, error)
}
