package server

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-redis/redis"
	"github.com/opentracing/opentracing-go"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/configserver"
	"golang.52tt.com/clients/currency"
	"golang.52tt.com/clients/exp"
	"golang.52tt.com/clients/guild"
	missionTL "golang.52tt.com/clients/missiontimeline"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/seqgen/v2"
	tClient "golang.52tt.com/clients/timeline"
	tabLogic "golang.52tt.com/clients/topic-channel-logic"
	"golang.52tt.com/kaihei-pkg/blreport"
	"golang.52tt.com/pkg/bylink"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	tracing "golang.52tt.com/pkg/tracing/jaeger"
	pb "golang.52tt.com/protocol/app/impromotelogic"
	channel_base_api "golang.52tt.com/protocol/services/channel-base-api"
	configPB "golang.52tt.com/protocol/services/configserver"
	"golang.52tt.com/services/im-promote-logic/cache"
	"golang.52tt.com/services/im-promote-logic/conf"
)

type button struct {
	configKey   configserver.ConfigKeyType
	pageType    pb.OpenPageType
	detail      *pb.PushButtonInfo
	showVersion uint32
	clientType  uint16
}

type ImPromoteLogic_ struct {
	sc                *conf.ServiceConfigT
	cacheClient       *cache.ImPromoteLogicCache
	pushClient        *PushNotification.Client
	configClient      *configserver.Client
	accountCli        *account.Client
	tabClient         *tabLogic.Client
	seqGenClient      *seqgen.Client
	timelineClient    *tClient.Client
	guildCli          *guild.Client
	channelBaseApiCli channel_base_api.ChannelBaseApiClient
}

var (
	buttonTypeToKeyMap = map[pb.PushButtonType]configserver.ConfigKeyType{
		pb.PushButtonType_Attitude:          configserver.AttitudeType,
		pb.PushButtonType_CommentAndAt:      configserver.CommentType,
		pb.PushButtonType_FANS:              configserver.FollowType,
		pb.PushButtonType_Careful:           configserver.CarefulType,
		pb.PushButtonType_LiveStart:         configserver.LiveStart,
		pb.PushButtonType_DiyRecommend:      configserver.DiyRecommendType,
		pb.PushButtonType_EnterRoomNotify:   configserver.EnterRoomNotifyType,
		pb.PushButtonType_InviteRoom:        configserver.InviteRoomType,
		pb.PushButtonType_PalOnlineNotify:   configserver.PalOnlineNotifyType,
		pb.PushButtonType_AttitudeNotify:    configserver.PostAttitudeNotifyType,
		pb.PushButtonType_FastPcShowProcess: configserver.FastPcShowProcessType,
	}

	buttonInfos = []button{
		{
			configKey: configserver.AttitudeType,
			pageType:  pb.OpenPageType_MsgType,
			detail: &pb.PushButtonInfo{
				ButtonType: pb.PushButtonType_Attitude,
				On:         true,
				Desc:       "「被赞」通知",
			},
		},
		{
			configKey: configserver.CommentType,
			pageType:  pb.OpenPageType_MsgType,
			detail: &pb.PushButtonInfo{
				ButtonType: pb.PushButtonType_CommentAndAt,
				On:         true,
				Desc:       "「评论和@」通知",
			},
		},
		{
			configKey: configserver.FollowType,
			pageType:  pb.OpenPageType_MsgType,
			detail: &pb.PushButtonInfo{
				ButtonType: pb.PushButtonType_FANS,
				On:         true,
				Desc:       "「粉丝」通知",
			},
		},
		{
			configKey: configserver.CarefulType,
			pageType:  pb.OpenPageType_MsgType,
			detail: &pb.PushButtonInfo{
				ButtonType: pb.PushButtonType_Careful,
				On:         true,
				Desc:       "「关心」通知",
			},
		},
		{
			configKey: configserver.LiveStart,
			pageType:  pb.OpenPageType_MsgType,
			detail: &pb.PushButtonInfo{
				ButtonType: pb.PushButtonType_LiveStart,
				On:         true,
				Desc:       "达人开启听听通知",
			},
		},
		{
			configKey: configserver.DiyRecommendType,
			pageType:  pb.OpenPageType_ConcealType,
			detail: &pb.PushButtonInfo{
				ButtonType: pb.PushButtonType_DiyRecommend,
				On:         true,
				Desc:       "个性化推荐",
			},
		},
		{
			configKey: configserver.EnterRoomNotifyType,
			pageType:  pb.OpenPageType_MsgType,
			detail: &pb.PushButtonInfo{
				ButtonType: pb.PushButtonType_EnterRoomNotify,
				On:         false,
				Desc:       "房客进房通知",
			},
			showVersion: protocol.FormatClientVersion(6, 16, 0),
		},
		{
			configKey: configserver.InviteRoomType,
			pageType:  pb.OpenPageType_ConcealType,
			detail: &pb.PushButtonInfo{
				ButtonType: pb.PushButtonType_InviteRoom,
				On:         true,
				Desc:       "邀请进房（游戏开黑房）",
				SecondDesc: "开启后，会收到其他玩家邀请你进入游戏开黑房间",
			},
			showVersion: protocol.FormatClientVersion(6, 50, 2),
		},
		{
			configKey: configserver.PalOnlineNotifyType,
			pageType:  pb.OpenPageType_ConcealType,
			detail: &pb.PushButtonInfo{
				ButtonType: pb.PushButtonType_PalOnlineNotify,
				On:         true,
				Desc:       "好友上线系统通知",
				SecondDesc: "开启后，会收到常玩好友的上线通知",
			},
			showVersion: protocol.FormatClientVersion(6, 59, 5),
		},
		{
			configKey: configserver.PostAttitudeNotifyType,
			pageType:  pb.OpenPageType_MsgType,
			detail: &pb.PushButtonInfo{
				ButtonType: pb.PushButtonType_AttitudeNotify,
				On:         true,
				Desc:       "动态被赞通知",
			},
			clientType: protocol.ClientTypePcLFG,
			//showVersion: protocol.FormatClientVersion(6, 59, 5),
		},
		{
			configKey: configserver.FastPcShowProcessType,
			pageType:  pb.OpenPageType_ConcealType,
			detail: &pb.PushButtonInfo{
				ButtonType:    pb.PushButtonType_FastPcShowProcess,
				On:            true,
				Desc:          "展示游戏中/音乐播放",
				SecondDesc:    "关闭后，所有人都不能查看您当前在玩游戏或者播放音乐",
				ButtonSubType: uint32(pb.PushButtonSubType_PUSH_BUTTON_SUB_TYPE_ALL),
			},
			clientType: protocol.ClientTypePcLFG,
		},
	}
)

func NewImPromoteLogic_(ctx context.Context, config config.Configer, tracer opentracing.Tracer) (*ImPromoteLogic_, error) {

	sc := &conf.ServiceConfigT{}

	cfgPath := ctx.Value("configfile").(string)
	if cfgPath == "" {
		return nil, errors.New("configfile not exist")
	}
	err := sc.Parse(cfgPath)
	if err != nil {
		fmt.Println("cf:", err)
		return nil, err
	}
	defaultTogetherList = sc.PlayTogether
	log.InfoWithCtx(ctx, "NewImPromoteLogic_ defaultTogetherList: %+v", defaultTogetherList)

	pushClient, err := PushNotification.NewTracedClient(tracer)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushNotification.NewClient err:%s", err.Error())
		return nil, err
	}
	accountCli, err := account.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "account.NewClient err:%s", err.Error())
		return nil, err
	}
	currencyCli := currency.NewClient()
	if currencyCli == nil {
		log.ErrorWithCtx(ctx, "currency.NewClient err:%s")
		return nil, errors.New("currency.NewClient err")
	}
	expClient := exp.NewClient()
	if expClient == nil {
		log.ErrorWithCtx(ctx, "expClient.NewClient err:%s")
		return nil, errors.New("expClient.NewClient err")
	}
	missionTLClient := missionTL.NewClient()
	if missionTLClient == nil {
		log.ErrorWithCtx(ctx, "missionTLClient.NewClient err:%s")
		return nil, errors.New("missionTLClient.NewClient err")
	}

	channelBaseApiCli, _ := channel_base_api.NewClient(ctx)

	log.DebugWithCtx(ctx, "NewIMMsgEventSubscriber conf:%+v,err: %+v", sc.GetIMMsgKfk(), err)

	// logic调用logic，要加上新的拦截
	tabClient, err := tabLogic.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "init tab client fail: %v", err)
		return nil, err
	}

	redisClient := redis.NewClient(&redis.Options{
		Network:            sc.GetRedisConfig().Protocol,
		Addr:               sc.GetRedisConfig().Addr(),
		PoolSize:           sc.GetRedisConfig().PoolSize,
		IdleCheckFrequency: sc.GetRedisConfig().IdleCheckFrequency(),
		DB:                 sc.GetRedisConfig().DB,
	})
	log.DebugWithCtx(ctx, "Initialized redis connection pool to %s://%s/%d", sc.GetRedisConfig().Protocol, sc.GetRedisConfig().Addr(), sc.GetRedisConfig().DB)
	redisTracer := tracing.Init("im-promote-logic_redis")
	cacheClient := cache.NewImPromoteLogicCache(redisClient, redisTracer)
	log.InfoWithCtx(ctx, "knock token:%s, timeout:%d", string(sc.KnockInfo.Token), sc.KnockInfo.ImTimeout)

	configClient, _ := configserver.NewClient()
	seqGenClient, _ := seqgen.NewClient()
	timelineClient := tClient.NewClient()
	guildCli := guild.NewClient()

	// 百灵数据统计 初始化
	byLinkCollect, err := bylink.NewKfkCollector()
	if err != nil {
		log.ErrorWithCtx(ctx, "failed to new bylink kfk collector, err: %v", err)
		return nil, err
	}
	bylink.InitGlobalCollector(byLinkCollect)

	return &ImPromoteLogic_{
		sc:                sc,
		cacheClient:       cacheClient,
		pushClient:        pushClient,
		configClient:      configClient,
		accountCli:        accountCli,
		tabClient:         tabClient,
		seqGenClient:      seqGenClient,
		timelineClient:    timelineClient,
		guildCli:          guildCli,
		channelBaseApiCli: channelBaseApiCli,
	}, nil
}

func (s *ImPromoteLogic_) PushButtonsSave(ctx context.Context, in *pb.PushButtonsReq) (out *pb.PushButtonsRsp, err error) {
	out = &pb.PushButtonsRsp{}
	serverBaseInfo, _ := grpc.ServiceInfoFromContext(ctx)
	log.InfoWithCtx(ctx, "PushButtonsSave  req: %+v, uid:%d", in, serverBaseInfo.UserID)

	configInfos := make([]*configPB.ConfigInfo, 0, len(in.GetPushButtonInfos()))
	for _, buttonInfoTmp := range in.GetPushButtonInfos() {
		key, ok := buttonTypeToKeyMap[buttonInfoTmp.GetButtonType()]
		if !ok {
			continue
		}

		value := configserver.CloseSwitch
		if buttonInfoTmp.GetOn() {
			value = configserver.OpenSwitch
		}

		if buttonInfoTmp.GetOn() && buttonInfoTmp.GetButtonType() == pb.PushButtonType_FastPcShowProcess {
			value = pb.PushButtonSubType_name[int32(buttonInfoTmp.GetButtonSubType())]
			if buttonInfoTmp.GetButtonSubType() == 0 || value == "" {
				log.ErrorWithCtx(ctx, "PushButtonsSave invalid GetButtonSubType: %v", buttonInfoTmp.GetButtonSubType())
				continue
			}
		}
		configInfo := &configPB.ConfigInfo{
			ConfigKey:  string(key),
			Uid:        serverBaseInfo.UserID,
			ConfigType: uint32(in.GetPageType()),
			Value:      value,
		}
		configInfos = append(configInfos, configInfo)
	}
	if len(configInfos) == 0 {
		log.WarnWithCtx(ctx, "PushButtonsSave no valid button infos provided")
		return out, nil
	}

	req := configPB.BatchSaveConfigReq{
		ConfigInfo: configInfos,
	}

	_ = byReport(ctx, configInfos)

	_, err = s.configClient.BatchSaveConfig(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchSaveConfig err:%v req: %+v ", err, in)
		return out, err
	}
	return out, nil
}

func (s *ImPromoteLogic_) GetPushButtons(ctx context.Context, in *pb.GetPushButtonsReq) (out *pb.GetPushButtonsRsp, err error) {
	out = &pb.GetPushButtonsRsp{}
	serverBaseInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return nil, errors.New("no service info in context")
	}
	log.InfoWithCtx(ctx, "GetPushButtons req: %+v, uid:%d", in, serverBaseInfo.UserID)

	configKeys := getConfigKeys(serverBaseInfo.ClientVersion, in.GetPageType(), in.GetButtons())
	if len(configKeys) == 0 {
		return
	}
	req := configPB.BatchGetUserConfigReq{
		Uid:        serverBaseInfo.UserID,
		ConfigKeys: configKeys,
	}
	rsp, err := s.configClient.BatchGetUserConfig(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserConfig err:%v req: %+v ", err, in)
		return
	}
	configMap := make(map[string]*configPB.ConfigInfo)
	for _, configInfoTmp := range rsp.GetConfigInfo() {
		configMap[configInfoTmp.GetConfigKey()] = configInfoTmp
	}

	for _, configKey := range configKeys {
		confButtonInfo := button{}
		for _, v := range buttonInfos {
			if v.configKey == configserver.ConfigKeyType(configKey) {
				confButtonInfo = v
				break
			}
		}
		if confButtonInfo.detail == nil {
			continue
		}

		if (!protocol.IsFastPcClientType(uint32(serverBaseInfo.ClientType)) && !protocol.IsFastPcClientType(uint32(confButtonInfo.clientType))) ||
			(protocol.IsFastPcClientType(uint32(serverBaseInfo.ClientType)) && protocol.IsFastPcClientType(uint32(confButtonInfo.clientType))) {

			resButtonInfo := &pb.PushButtonInfo{
				ButtonType:    confButtonInfo.detail.ButtonType,
				On:            confButtonInfo.detail.On,
				Desc:          confButtonInfo.detail.Desc,
				ButtonSubType: confButtonInfo.detail.ButtonSubType,
			}
			if configInfoTmp, ok := configMap[configKey]; ok {
				if configInfoTmp.GetValue() == configserver.OpenSwitch {
					resButtonInfo.On = true
				} else {
					resButtonInfo.On = false
				}
				if confButtonInfo.detail.ButtonType == pb.PushButtonType_FastPcShowProcess {
					subType, ok1 := pb.PushButtonSubType_value[configInfoTmp.GetValue()]
					if ok1 {
						resButtonInfo.On = true
					}
					resButtonInfo.ButtonSubType = uint32(subType)
				}
			}
			out.PushButtonInfos = append(out.PushButtonInfos, resButtonInfo)
		}
	}
	log.InfoWithCtx(ctx, "GetPushButtons req: %+v, uid:%d, resp:%s", in, serverBaseInfo.UserID, out.String())
	return
}

func getConfigKeys(clientVer uint32, pageType pb.OpenPageType, filterButtonType []pb.PushButtonType) []string {
	var configKeys []string
	for _, v := range buttonInfos {
		if v.showVersion > clientVer {
			continue
		}
		if pageType == v.pageType && !filterReq(filterButtonType, v.detail.ButtonType) {
			configKeys = append(configKeys, string(v.configKey))
		}
	}
	return configKeys
}

func filterReq(filterButtonType []pb.PushButtonType, e pb.PushButtonType) bool {
	if len(filterButtonType) == 0 {
		return false
	}
	for _, t := range filterButtonType {
		if t == e {
			return false
		}
	}
	return true
}

func byReport(ctx context.Context, configInfo []*configPB.ConfigInfo) error {
	for _, v := range configInfo {
		if v.GetConfigKey() != string(configserver.FastPcShowProcessType) {
			continue
		}

		opType := "open"
		itemName := ""
		if v.GetValue() == configserver.CloseSwitch {
			opType = "close"
		} else {
			switch pb.PushButtonSubType_value[v.GetValue()] {
			case int32(pb.PushButtonSubType_PUSH_BUTTON_SUB_TYPE_ALL):
				itemName = "所有人可见"
			case int32(pb.PushButtonSubType_PUSH_BUTTON_SUB_TYPE_FANS):
				itemName = "玩伴及粉丝可见"
			case int32(pb.PushButtonSubType_PUSH_BUTTON_SUB_TYPE_PARTNER):
				itemName = "玩伴可见"
			default:
				log.WarnWithCtx(ctx, "byReport invalid PushButtonSubType, configInfo:%v", v)
				continue
			}
		}

		bylinkKV := map[string]interface{}{
			"uid":       v.GetUid(),
			"name":      "展示游戏中/音乐播放",
			"op":        opType,
			"item_name": itemName,
		}
		blreport.GetBlInst(true).Report2bylink(ctx, uint64(v.GetUid()), blreport.ProcessPcUserSetting, bylinkKV, false)
	}

	return nil
}

func (s *ImPromoteLogic_) ShutDown() {
	fmt.Println("ShutDown")
}
