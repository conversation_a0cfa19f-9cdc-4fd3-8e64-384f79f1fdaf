package server

import (
	"context"
	"fmt"
	"time"
	"unicode/utf8"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/melee-channel"
	"golang.52tt.com/protocol/common/status"
	channel_roleplay "golang.52tt.com/protocol/services/channel-roleplay"
	melee_channel "golang.52tt.com/protocol/services/melee-channel"
	"golang.52tt.com/services/melee-channel-logic/configz"
	rpcClient "golang.52tt.com/services/melee-channel-logic/rpc/client"
	"google.golang.org/grpc/codes"
)

const (
	applyMsgMaxLen = 15
)

type UserInfo struct {
	Uid      int
	Account  string
	Nickname string
	Vest     string
}

func (s *MeleeChannelLogic) GetChannelRoomApplyList(ctx context.Context, req *pb.GetChannelRoomApplyListReq) (*pb.GetChannelRoomApplyListResp, error) {
	out := &pb.GetChannelRoomApplyListResp{}
	log.InfoWithCtx(ctx, "GetChannelRoomApplyList req:%s", req.String())

	if req.ChannelId == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	if req.TabId == 0 {
		req.TabId = configz.Config.Get().EnterRoomWhitelistTabId
	}
	if req.Limit == 0 || req.Limit > 100 {
		req.Limit = 100
	}
	svInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, UserInfoErrMsg)
	}
	adminId := svInfo.UserID
	per, _ := hasPower(ctx, adminId, req.ChannelId)
	if !per {
		return out, protocol.NewExactServerError(codes.PermissionDenied, status.ErrChannelNoPermission)
	}

	res, err := rpcClient.MeleeChannelClient.GetChannelRoomApplyList(ctx, req.ChannelId, req.TabId, req.Limit, req.LastUpdateMs)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelRoomApplyList fail, err:%s, req:+%v", err.Error(), req)
		return out, protocol.ToServerError(err)
	}
	if len(res.ApplyList) == 0 {
		return out, nil
	}

	uids := make([]uint32, 0, len(res.ApplyList))
	for _, info := range res.ApplyList {
		uids = append(uids, info.Uid)
	}
	userMap, err := rpcClient.AccountClient.GetUsersMap(ctx, uids)
	if err != nil {
		log.ErrorWithCtx(ctx, "batchGetUserInfo GetUsersMap uids(%v) channelId(%d) error:%v", uids, req.ChannelId, err)
		return out, err
	}

	roles, err := rpcClient.ChannelRoleplayClient.GetChannelUserRoleMap(ctx, channel_roleplay.RoleType_RoleTypeVest, req.ChannelId, uids)
	if err != nil {
		log.ErrorWithCtx(ctx, "batchGetUserInfo GetChannelUserRoleMap uids(%v) channelId(%d) error:%v", uids, req.ChannelId, err)
		return out, err
	}
	out.TotalNum = res.TotalNum
	out.ApplicantUsers = make([]*pb.ChannelRoomRetItem, 0, len(res.ApplyList))
	for _, info := range res.ApplyList {
		if userMap[info.Uid] == nil {
			continue
		}
		name := ""
		if v, ok := roles[info.Uid]; ok {
			name = v.RoleName
		}
		out.ApplicantUsers = append(out.ApplicantUsers, &pb.ChannelRoomRetItem{
			Uid:          info.Uid,
			Account:      userMap[info.Uid].Username,
			Nickname:     userMap[info.Uid].Nickname,
			Vest:         name,
			Msg:          info.Msg,
			UpdateTimeMs: info.UpdateTimeMs,
		})
	}
	return out, nil
}

func (s *MeleeChannelLogic) AddChannelRoomApplyList(ctx context.Context, req *pb.AddChannelRoomApplyListReq) (*pb.AddChannelRoomApplyListResp, error) {
	out := &pb.AddChannelRoomApplyListResp{}
	log.InfoWithCtx(ctx, "AddChannelRoomApplyList in, req:%s", req.String())
	if req.TabId == 0 {
		req.TabId = configz.Config.Get().EnterRoomWhitelistTabId
	}

	if utf8.RuneCountInString(req.Msg) > applyMsgMaxLen {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	// msg添加T盾审核机制
	if configz.Config.Get().IsOpenCheckApplyMsg == configz.OpenCheckApplyMsg && len(req.Msg) > 0 {
		check, _ := auditAddChannelRoomApplyListMsg(ctx, req.ChannelId, req.Uid, req.Msg)
		if !check {
			log.WarnWithCtx(ctx, "AuditAddChannelRoomApplyListMsg fail, req:%+v", req)
			return out, protocol.NewExactServerError(nil, status.ErrMeleeChannelApplySensitive)
		}
	}

	_, err := rpcClient.MeleeChannelClient.AddChannelRoomApplyList(ctx, req.ChannelId, req.TabId, req.Uid, req.Msg, req.EnterRoomType)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddChannelRoomApplyList fail, err:%s, req:+%v", err.Error(), req)
		return nil, protocol.ToServerError(err)
	}

	adminList, err := rpcClient.ChannelClient.GetChannelAdmin(ctx, 0, req.ChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddChannelRoomApplyList GetChannelAdmin fail, err:%s, channelId:%d", err, req.ChannelId)
		return out, err
	}
	adminUids := make([]uint32, 0, len(adminList))
	for _, admin := range adminList {
		adminUids = append(adminUids, admin.GetUid())
	}
	//log.DebugWithCtx(ctx, "pushChannelRoomApplyListChange, channelId:%d, adminUids:%v", req.ChannelId, adminUid)
	err = pushChannelRoomApplyListChange(ctx, req.ChannelId, adminUids)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushChannelRoomApplyListChange fail, err:%s, channelId:%d, adminUid:%+v", err, req.ChannelId, adminUids)
	}
	return out, nil
}

func (s *MeleeChannelLogic) HandleChannelRoomApply(ctx context.Context, req *pb.HandleChannelRoomApplyReq) (*pb.HandleChannelRoomApplyResp, error) {
	out := &pb.HandleChannelRoomApplyResp{}
	log.InfoWithCtx(ctx, "HandleChannelRoomApply in, req:%s", req.String())
	if req.TabId == 0 {
		req.TabId = configz.Config.Get().EnterRoomWhitelistTabId
	}
	svInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, UserInfoErrMsg)
	}
	adminId := svInfo.UserID
	per, _ := hasPower(ctx, adminId, req.ChannelId)
	if !per {
		return out, protocol.NewExactServerError(codes.PermissionDenied, status.ErrChannelNoPermission)
	}

	res, err := rpcClient.MeleeChannelClient.HandleChannelRoomApply(ctx, req.ChannelId, req.TabId, adminId, req.ApplicantUid, melee_channel.ActionType(req.ActionType))
	if err != nil {
		log.WarnWithCtx(ctx, "HandleChannelRoomApply fail, err:%s, req:+%v", err.Error(), req)
		return out, protocol.ToServerError(err)
	}
	// xx分钟内同意，通知用户进房
	if req.ActionType == pb.ActionType_Agree {
		check := (time.Now().Unix() - int64(res.Item.UpdateTimeMs/1000)) < int64(configz.Config.Get().EnterRoomApplySucPushInterval)*60
		if check {
			info, e := rpcClient.ChannelClient.GetChannelSimpleInfo(ctx, adminId, req.ChannelId)
			if e == nil {
				user, uErr := rpcClient.AccountClient.GetUser(ctx, info.GetCreaterUid())
				if uErr == nil {
					pushMsg := fmt.Sprintf("您已进入%s的房间", user.Nickname)
					_ = pushChannelRoomWhitelistNotify(ctx, req.ChannelId, req.ApplicantUid, pushMsg, res.Item.EnterRoomType)
				}
			}
		}
	}
	adminList, err := rpcClient.ChannelClient.GetChannelAdmin(ctx, adminId, req.ChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleChannelRoomApply GetChannelAdmin fail, err:%s, channelId:%d", err.Error(), req.ChannelId)
		return out, err
	}
	adminUids := make([]uint32, 0, len(adminList))
	for _, admin := range adminList {
		adminUids = append(adminUids, admin.GetUid())
	}
	// 红点更新推送
	err = pushChannelRoomApplyListChange(ctx, req.ChannelId, adminUids)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushChannelRoomApplyListChange fail, err:%s, channelId:%d, adminUids:%+v", err.Error(), req.ChannelId, adminUids)
	}
	return out, nil
}

func (s *MeleeChannelLogic) GetChannelRoomWhitelist(ctx context.Context, req *pb.GetChannelRoomWhitelistReq) (*pb.GetChannelRoomWhitelistResp, error) {
	out := &pb.GetChannelRoomWhitelistResp{}
	log.DebugWithCtx(ctx, "GetChannelRoomWhitelist in, req:%s", req.String())
	if req.ChannelId == 0 || req.Limit > 100 {
		return nil, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	if req.TabId == 0 {
		req.TabId = configz.Config.Get().EnterRoomWhitelistTabId
	}
	svInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, UserInfoErrMsg)
	}
	adminId := svInfo.UserID
	per, _ := hasPower(ctx, adminId, req.ChannelId)
	if !per {
		return out, protocol.NewExactServerError(codes.PermissionDenied, status.ErrChannelNoPermission)
	}

	res, err := rpcClient.MeleeChannelClient.GetChannelRoomWhitelist(ctx, req.ChannelId, req.TabId, req.Limit, req.LastUpdateMs)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelRoomWhitelist fail, err:%s, req:+%v", err.Error(), req)
		return out, err
	}

	if len(res.Whitelist) == 0 {
		return out, nil
	}
	uids := make([]uint32, 0, len(res.Whitelist))
	for _, info := range res.Whitelist {
		uids = append(uids, info.Uid)
	}
	userMap, err := rpcClient.AccountClient.GetUsersMap(ctx, uids)
	if err != nil {
		log.ErrorWithCtx(ctx, "batchGetUserInfo GetUsersMap uids:%v, channelId:%d, error:%s", uids, req.ChannelId, err.Error())
		return out, err
	}

	roles, err := rpcClient.ChannelRoleplayClient.GetChannelUserRoleMap(ctx, channel_roleplay.RoleType_RoleTypeVest, req.ChannelId, uids)
	if err != nil {
		log.ErrorWithCtx(ctx, "batchGetUserInfo GetChannelUserRoleMap uids%v, channelId:%d, error:%s", uids, req.ChannelId, err.Error())
		return out, err
	}
	out.WhitelistUsers = make([]*pb.ChannelRoomRetItem, 0, len(res.Whitelist))
	for _, info := range res.Whitelist {
		if userMap[info.Uid] == nil {
			continue
		}
		name := ""
		if v, ok := roles[info.Uid]; ok {
			name = v.RoleName
		}
		out.WhitelistUsers = append(out.WhitelistUsers, &pb.ChannelRoomRetItem{
			Uid:          info.Uid,
			Account:      userMap[info.Uid].Username,
			Nickname:     userMap[info.Uid].Nickname,
			Vest:         name,
			UpdateTimeMs: info.UpdateTimeMs,
		})
	}
	log.InfoWithCtx(ctx, "GetChannelRoomWhitelist req:%s out:%s", req.String(), out.String())
	return out, nil
}

func (s *MeleeChannelLogic) AddChannelRoomWhiteList(ctx context.Context, req *pb.AddChannelRoomWhiteListReq) (*pb.AddChannelRoomWhiteListResp, error) {
	out := &pb.AddChannelRoomWhiteListResp{}
	log.InfoWithCtx(ctx, "AddChannelRoomWhiteList success, req:%s", req.String())

	if req.TabId == 0 {
		req.TabId = configz.Config.Get().EnterRoomWhitelistTabId
	}
	svInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, UserInfoErrMsg)
	}
	adminId := svInfo.UserID
	per, _ := hasPower(ctx, adminId, req.ChannelId)
	if !per {
		return out, protocol.NewExactServerError(codes.PermissionDenied, status.ErrChannelNoPermission)
	}

	_, err := rpcClient.MeleeChannelClient.AddChannelRoomWhiteList(ctx, req.ChannelId, req.TabId, req.Whitelist)
	if err != nil {
		log.WarnWithCtx(ctx, "AddChannelRoomWhiteList fail, err:%s, req:+%v", err.Error(), req)
		return out, protocol.ToServerError(err)
	}

	return out, nil
}

func (s *MeleeChannelLogic) RemoveChannelRoomWhiteList(ctx context.Context, req *pb.RemoveChannelRoomWhiteListReq) (*pb.RemoveChannelRoomWhiteListResp, error) {
	out := &pb.RemoveChannelRoomWhiteListResp{}
	log.InfoWithCtx(ctx, "RemoveChannelRoomWhiteList success, req:%s", req.String())

	if req.TabId == 0 {
		req.TabId = configz.Config.Get().EnterRoomWhitelistTabId
	}
	svInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, UserInfoErrMsg)
	}
	adminId := svInfo.UserID
	per, _ := hasPower(ctx, adminId, req.ChannelId)
	if !per {
		return out, protocol.NewExactServerError(codes.PermissionDenied, status.ErrChannelNoPermission)
	}

	_, err := rpcClient.MeleeChannelClient.RemoveChannelRoomWhiteList(ctx, req.ChannelId, req.TabId, req.Whitelist)
	if err != nil {
		log.ErrorWithCtx(ctx, "RemoveChannelRoomWhiteList fail, err:%s, req:+%v", err.Error(), req)
		return out, err
	}

	return out, nil
}

func (s *MeleeChannelLogic) EnterRoomWhitelistConfig(ctx context.Context, req *pb.EnterRoomWhitelistConfigReq) (*pb.EnterRoomWhitelistConfigResp, error) {
	out := &pb.EnterRoomWhitelistConfigResp{}

	log.InfoWithCtx(ctx, "EnterRoomWhitelistConfig in, req:%v", req)

	tabConfig, err := rpcClient.MeleeChannelClient.GetChannelRoomWhitelistTabConfig(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelRoomWhitelistTabConfig fail, err:%s, req:+%v", err.Error(), req)
		return out, err
	}

	isShowEntrance := false
	for _, tabId := range tabConfig.GetTabIds() {
		if tabId == req.GetTabId() {
			isShowEntrance = true
			break
		}
	}
	if !isShowEntrance {
		log.DebugWithCtx(ctx, "GetChannelRoomWhitelistTabConfig tab is no whitelist, req:+%v", req)
		return out, nil
	}

	svInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, UserInfoErrMsg)
	}
	adminId := svInfo.UserID
	per, _ := hasPower(ctx, adminId, req.GetCid())
	if !per {
		log.InfoWithCtx(ctx, "EnterRoomWhitelistConfig has no power, uid:%d, cid%d", adminId, req.GetCid())
		return out, nil
	}

	out.IsShowEntrance = isShowEntrance

	// 房间是否开启白名单开关
	rsp, err := rpcClient.ChannelClient.GetChannelSimpleInfo(ctx, adminId, req.GetCid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelSimpleInfo fail, uid:%d, cid:%d, err:%s", adminId, req.GetCid(), err.Error())
		return out, err
	}
	//if rsp.GetEnterControlType() != uint32(channelGA.EnterControlType_EnterControlType_WHITELIST) {
	//	return
	//}
	out.EnterControlType = rsp.GetEnterControlType()

	return out, nil
}
