package server

import (
	"context"
	"encoding/json"
	"math/rand"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/melee-channel"
	"golang.52tt.com/protocol/common/status"
	channelmicgoPB "golang.52tt.com/protocol/services/channel-mic"
	"golang.52tt.com/protocol/services/channelbox"
	channelmicPB "golang.52tt.com/protocol/services/channelmicsvr"
	melee_channel "golang.52tt.com/protocol/services/melee-channel"
	"golang.52tt.com/services/melee-channel-logic/configz"
	rpcClient "golang.52tt.com/services/melee-channel-logic/rpc/client"
)

const (
	MIN_MIC_IDX = 1
	MAX_MIC_IDX = 50

	APPLY_EXPIRE_SECOND = 3 //麦位那边申请过期是3s
)

type MicConf struct {
	MicId   uint32             `json:"mic_id"`
	MicType channelbox.MicType `json:"mic_type"`

	BoxId uint32 `json:"box_id"`
}

func (c *MicConf) String() string {
	data, _ := json.Marshal(c)
	return string(data)
}

func (s *MeleeChannelLogic) GetSelfOnMicQualifications(ctx context.Context, in *pb.GetSelfOnMicQualificationsReq) (out *pb.GetSelfOnMicQualificationsResp, err error) {
	out = &pb.GetSelfOnMicQualificationsResp{}
	svInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取用户信息失败")
	}

	var hp bool
	hp, _ = hasPower(ctx, svInfo.UserID, in.ChannelId)

	resp, err := rpcClient.MeleeChannelClient.GetSelfOnMicQualifications(ctx, svInfo.UserID, in.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSelfOnMicQualifications GetSelfOnMicQualifications uid(%d) channelId(%d) error:%v", svInfo.UserID, in.ChannelId, err)
		return out, err
	}

	out.SwitchStatus = pb.MeleeChannelWhiteListSwitchStatus(resp.GetSwitchStatus())
	out.IsWhite = resp.IsWhite || hp
	out.MaxMicCount = configz.Config.Get().MaxMicCount

	return out, err
}

func (s *MeleeChannelLogic) ApplyOnMicToken(ctx context.Context, in *pb.ApplyOnMicTokenReq) (out *pb.ApplyOnMicTokenResp, err error) {
	out = &pb.ApplyOnMicTokenResp{}
	log.InfoWithCtx(ctx, "ApplyOnMicToken in: %+v", in)

	applyMicType := channelbox.MicType_MicTypeFree
	if in.GetMicId() > 0 {
		applyMicType = channelbox.MicType_MicTypePublic
	}

	svInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取用户信息失败")
	}

	// 非房管
	if hp, _ := hasPower(ctx, svInfo.UserID, in.GetChannelId()); !hp {
		resp, err := rpcClient.MeleeChannelClient.GetSelfOnMicQualifications(ctx, svInfo.UserID, in.ChannelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyOnMicToken GetSelfOnMicQualifications uid(%d) channelId(%d) error:%v", svInfo.UserID, in.ChannelId, err)
			return out, err
		}

		// 房间开启白名单且用户不在白名单
		if resp.GetSwitchStatus() == melee_channel.WhiteListSwitchStatus_ONLY_WHITE && !resp.GetIsWhite() {
			log.WarnWithCtx(ctx, "ApplyOnMicToken user %d miss white list", svInfo.UserID)
			return out, protocol.NewExactServerError(nil, status.ErrMeleeChannelNoOnMicAccess)
		}
	}

	// 获取所有子频道配置
	boxInfoResp, err := rpcClient.ChannelBoxClient.GetBoxInfo(ctx, channelbox.GetBoxInfoReq{
		OpeUid:    svInfo.UserID,
		Channelid: in.GetChannelId(),
		BoxType:   channelbox.BoxType_BoxTypeMelee,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyOnMicToken GetBoxInfo channelId(%d) err: %v", in.GetChannelId(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "ApplyOnMicToken GetBoxInfo resp: %+v", boxInfoResp)

	micConfs := calcMicConfs(boxInfoResp.GetBoxList())
	log.InfoWithCtx(ctx, "ApplyOnMicToken micConfs: %+v", micConfs)

	// 查线上麦位详情
	micRsp, err := rpcClient.ChannelMicClient.GetMicrList(ctx, in.GetChannelId(), svInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyOnMicToken GetMicrList err: %+v", err)
		return out, err
	}
	log.InfoWithCtx(ctx, "ApplyOnMicToken GetMicrList resp: %+v", micRsp)

	// 查麦位申请记录
	micApplyRecordsResp, err := rpcClient.ChannelBoxClient.GetMicApplyRecords(ctx, &channelbox.GetMicApplyRecordsReq{
		ChannelId: in.GetChannelId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyOnMicToken GetMicApplyRecords channelId(%d) err: %+v", in.GetChannelId(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "ApplyOnMicToken GetMicApplyRecords resp: %+v", micApplyRecordsResp)

	mergeRecords, modifyRecords := diffAndMerge(ctx, applyMicType, micRsp.AllMicList, micApplyRecordsResp.GetRecords(), micConfs)
	log.InfoWithCtx(ctx, "ApplyOnMicToken diffAndMerge mergeRecords: %+v", mergeRecords)

	var targetMicId uint32
	switch applyMicType {
	case channelbox.MicType_MicTypePublic:
		err = preemptPublicMic(svInfo.UserID, in.GetMicId(), boxInfoResp.GetUserBoxid(), mergeRecords, micConfs)
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyOnMicToken preemptPublicMic micId(%d) err: %v", in.GetMicId(), err)
			return out, err
		}

		targetMicId = in.GetMicId()
	case channelbox.MicType_MicTypeFree:
		targetMicId, err = allocFreeMic(svInfo.UserID, boxInfoResp.GetUserBoxid(), mergeRecords, micConfs)
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyOnMicToken allocFreeMic err: %v", err)
			return out, err
		}
	default:
		log.WarnWithCtx(ctx, "ApplyOnMicToken invalid applyMicType %d", applyMicType)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "invalid applyMicType")
	}
	log.InfoWithCtx(ctx, "ApplyOnMicToken targetMicId: %d", targetMicId)

	newApplyRecord := &channelbox.MicApplyRecord{
		MicId:     targetMicId,
		Uid:       svInfo.UserID,
		BoxId:     boxInfoResp.GetUserBoxid(),
		MicType:   micConfs[targetMicId].MicType,
		MicStatus: channelbox.MicStatus_MicStatusApplying,
		UpdateAt:  uint32(time.Now().Unix()),
	}
	// 先写入再实际申请
	modifyRecords = append(modifyRecords, newApplyRecord)
	log.InfoWithCtx(ctx, "ApplyOnMicToken SetMicApplyRecords modifyRecords: %+v", modifyRecords)
	_, err = rpcClient.ChannelBoxClient.SetMicApplyRecords(ctx, &channelbox.SetMicApplyRecordsReq{
		ChannelId: in.GetChannelId(),
		Records:   modifyRecords,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyOnMicToken SetMicApplyRecords err: %+v", err)
		return
	}

	// 公共麦才执行，更新公共频道
	if applyMicType == channelbox.MicType_MicTypePublic {
		_, err = rpcClient.ChannelBoxClient.EnterBox(ctx, channelbox.EnterBoxReq{
			Uid:            svInfo.UserID,
			Channelid:      in.GetChannelId(),
			BoxType:        channelbox.BoxType_BoxTypeMelee,
			BroadcastBoxid: uint32(channelbox.SystemBoxID_SystemBoxIDSubChannel),
		})
		if err != nil {
			log.WarnWithCtx(ctx, "ApplyOnMicToken EnterBox err: %+v", err)
		}
	}

	// 真正去申请token
	applyRsp, err := rpcClient.GoChannelMicClient.ApplyMic(ctx, &channelmicgoPB.ApplyHoldMicReq{
		Cid:   in.GetChannelId(),
		Uid:   svInfo.UserID,
		MicId: targetMicId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyOnMicToken ApplyMic err: %+v", err)
		return
	}

	out.MicId = applyRsp.GetMicId()
	out.OnMicToken = applyRsp.GetToken()

	log.InfoWithCtx(ctx, "ApplyOnMicToken out: %+v", out)
	return out, nil
}

func calcMicConfs(boxConfs []*channelbox.BoxInfo) []*MicConf {
	micConfs := make([]*MicConf, MAX_MIC_IDX+1)
	for _, boxConf := range boxConfs {
		for _, id := range boxConf.PublicMicList {
			if id < MIN_MIC_IDX || id > MAX_MIC_IDX {
				continue
			}
			subPubConf := &MicConf{
				MicId:   id,
				BoxId:   boxConf.BoxId,
				MicType: channelbox.MicType_MicTypePublic,
			}
			micConfs[subPubConf.MicId] = subPubConf
		}
		for _, id := range boxConf.NormalMicList {
			if id < MIN_MIC_IDX || id > MAX_MIC_IDX {
				continue
			}
			subFreeConf := &MicConf{
				MicId:   id,
				BoxId:   boxConf.BoxId,
				MicType: channelbox.MicType_MicTypeFree,
			}
			micConfs[subFreeConf.MicId] = subFreeConf
		}
	}

	// 剩下没分配的麦位，主房间都可以用
	for i := MIN_MIC_IDX; i <= MAX_MIC_IDX; i++ {
		if micConfs[i] != nil {
			continue
		}
		mainFreeConf := &MicConf{
			MicId:   uint32(i),
			BoxId:   0,
			MicType: channelbox.MicType_MicTypeFree,
		}
		micConfs[mainFreeConf.MicId] = mainFreeConf
	}

	return micConfs
}

func diffAndMerge(ctx context.Context, applyMicType channelbox.MicType, micRecords []*channelmicPB.MicrSpaceInfo, applyRecords []*channelbox.MicApplyRecord,
	micConfs []*MicConf) ([]*channelbox.MicApplyRecord, []*channelbox.MicApplyRecord) {
	micTmp := make([]*channelbox.MicApplyRecord, MAX_MIC_IDX+1)
	for _, micRecord := range micRecords {
		if micRecord.MicId < MIN_MIC_IDX || micRecord.MicId > MAX_MIC_IDX { // 避免越界
			continue
		}
		if micRecord.MicUid == 0 {
			continue
		}
		var confBoxId uint32 = 0
		var confMicType channelbox.MicType = channelbox.MicType_MicTypeFree
		if micConfs[micRecord.MicId] != nil {
			confBoxId = micConfs[micRecord.MicId].BoxId
			confMicType = micConfs[micRecord.MicId].MicType
		}
		// 用默认值来填充
		record := &channelbox.MicApplyRecord{
			MicId:     micRecord.GetMicId(),
			Uid:       micRecord.GetMicUid(),
			BoxId:     confBoxId,
			MicType:   confMicType,
			MicStatus: channelbox.MicStatus_MicStatusUsing,
			UpdateAt:  micRecord.MicTs,
		}
		micTmp[record.MicId] = record
	}

	applyTmp := make([]*channelbox.MicApplyRecord, MAX_MIC_IDX+1)
	for _, applyRecord := range applyRecords {
		if applyRecord.MicId < MIN_MIC_IDX || applyRecord.MicId > MAX_MIC_IDX { // 避免越界
			continue
		}
		applyTmp[applyRecord.MicId] = applyRecord
	}

	mergeRecords := make([]*channelbox.MicApplyRecord, MAX_MIC_IDX+1)
	modifyRecords := make([]*channelbox.MicApplyRecord, 0)
	now := uint32(time.Now().Unix())
	for i := MIN_MIC_IDX; i <= MAX_MIC_IDX; i++ {
		micRecord, applyRecord := micTmp[i], applyTmp[i]
		if micRecord == nil && applyRecord == nil {
			continue
		} else if applyRecord == nil { // 可能是刚升级服务，存量房间还没缓存
			modifyRecords = append(modifyRecords, micRecord)
			mergeRecords[micRecord.MicId] = micRecord
		} else if micRecord == nil { // 可能已经下麦了，只加入申请还没过期的
			if applyMicType == applyRecord.GetMicType() && applyRecord.GetMicStatus() == channelbox.MicStatus_MicStatusApplying && now-applyRecord.UpdateAt <= APPLY_EXPIRE_SECOND {
				mergeRecords[applyRecord.MicId] = applyRecord
			}
		} else {
			if micRecord.Uid != applyRecord.Uid { // 不应该出现，因为都是先申请再上麦的
				log.ErrorWithCtx(ctx, "ApplyOnMicToken InconsistentStatus, mic=%+v, cache=%+v", micRecord, applyRecord)
				modifyRecords = append(modifyRecords, micRecord)
				mergeRecords[micRecord.MicId] = micRecord
			} else if applyRecord.GetMicStatus() == channelbox.MicStatus_MicStatusApplying { // 从申请变成上麦了
				applyRecord.MicStatus = channelbox.MicStatus_MicStatusUsing
				modifyRecords = append(modifyRecords, applyRecord)
				mergeRecords[applyRecord.MicId] = applyRecord
			} else {
				mergeRecords[applyRecord.MicId] = applyRecord
			}
		}
	}
	return mergeRecords, modifyRecords
}

// preemptCommonMic 抢占公共麦
func preemptPublicMic(
	reqUid, reqMicId, curBoxId uint32,
	mergeRecords []*channelbox.MicApplyRecord,
	micConfs []*MicConf) error {

	if mergeRecords[reqMicId] != nil { // 要申请的麦位上已经有人了
		if mergeRecords[reqMicId].Uid == reqUid { // 可能下麦后又很快上同个麦，这时候db中就还保持applying状态
			return nil
		}
		return protocol.NewExactServerError(nil, status.ErrChannelUserAlreadyHoldMicrophone)
	}
	if micConfs[reqMicId].MicType != channelbox.MicType_MicTypePublic {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "invalid reqMicId")
	}
	if micConfs[reqMicId].BoxId != curBoxId { // 子频道数据对不上，还是让上麦，只打印警告
		log.Warnf("preemptCommonMic micConfs[reqMicId(%d)].BoxId(%d) != curBoxId(%d)", reqMicId, micConfs[reqMicId].BoxId, curBoxId)
	}

	return nil
}

// allocFreeMic 分配自由麦
func allocFreeMic(
	reqUid, curBoxId uint32,
	mergeRecords []*channelbox.MicApplyRecord,
	micConfs []*MicConf) (uint32, error) {

	var candidates []uint32
	for i := MIN_MIC_IDX; i <= MAX_MIC_IDX; i++ {
		if mergeRecords[i] != nil {
			if mergeRecords[i].Uid == reqUid { // 可能下麦后又很快上麦，这时候db中就还保持applying状态，返回同个麦位即可
				return uint32(i), nil
			}
			continue
		}
		if micConfs[i].BoxId == curBoxId && micConfs[i].MicType == channelbox.MicType_MicTypeFree {
			candidates = append(candidates, micConfs[i].MicId)
		}
	}
	if len(candidates) <= 0 {
		return 0, protocol.NewExactServerError(nil, status.ErrChannelMicrophoneOverlimit)
	}
	if len(candidates) > 5 {
		candidates = candidates[0:5] // 选前5个麦位来随机就够了
	}

	// 随机选一个槽，避免一直选第一个，导致分布式覆盖
	r := rand.New(rand.NewSource(time.Now().UnixNano())) //nolint:gosec
	idx := r.Intn(len(candidates))
	return candidates[idx], nil
}
