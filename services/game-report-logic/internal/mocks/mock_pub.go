// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/game-report-logic/internal/event (interfaces: IEventLinkProducer)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	sarama "github.com/IBM/sarama"
	gomock "github.com/golang/mock/gomock"
)

// MockIEventLinkProducer is a mock of IEventLinkProducer interface.
type MockIEventLinkProducer struct {
	ctrl     *gomock.Controller
	recorder *MockIEventLinkProducerMockRecorder
}

// MockIEventLinkProducerMockRecorder is the mock recorder for MockIEventLinkProducer.
type MockIEventLinkProducerMockRecorder struct {
	mock *MockIEventLinkProducer
}

// NewMockIEventLinkProducer creates a new mock instance.
func NewMockIEventLinkProducer(ctrl *gomock.Controller) *MockIEventLinkProducer {
	mock := &MockIEventLinkProducer{ctrl: ctrl}
	mock.recorder = &MockIEventLinkProducerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIEventLinkProducer) EXPECT() *MockIEventLinkProducerMockRecorder {
	return m.recorder
}

// GetTopic mocks base method.
func (m *MockIEventLinkProducer) GetTopic() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTopic")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetTopic indicates an expected call of GetTopic.
func (mr *MockIEventLinkProducerMockRecorder) GetTopic() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopic", reflect.TypeOf((*MockIEventLinkProducer)(nil).GetTopic))
}

// Publish mocks base method.
func (m *MockIEventLinkProducer) Publish(arg0 context.Context, arg1 *sarama.ProducerMessage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Publish", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Publish indicates an expected call of Publish.
func (mr *MockIEventLinkProducerMockRecorder) Publish(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockIEventLinkProducer)(nil).Publish), arg0, arg1)
}
