package report_mgr

import (
	"context"
	"github.com/Shopify/sarama"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/publisher"
	"golang.52tt.com/pkg/log"
	game_report_logic "golang.52tt.com/protocol/app/game-report-logic"
	eventPB "golang.52tt.com/protocol/services/topic_channel/event"
	"golang.52tt.com/services/game-report-logic/internal/event"
	"strconv"
	"time"
)

type GameTaskMgr struct {
	gameActivityTaskProducer event.IEventLinkProducer
}

func NewGameTaskMgr(gameActivityTaskProducer event.IEventLinkProducer) *GameTaskMgr {
	return &GameTaskMgr{
		gameActivityTaskProducer: gameActivityTaskProducer,
	}
}

func (g *GameTaskMgr) ReportConfigTabViewData(ctx context.Context, in *game_report_logic.GameReportDailyTaskReq) error {
	// 发送kafka事件
	eventMsg := &eventPB.GameActivityTaskReportEvent{
		Uid:          in.GetUid(),
		FinishTime:   time.Now().Unix(),
		GameTaskType: in.GetTaskType(),
		TabId:        in.GetTabId(),
		ConfigTabId:  in.GetConfigTabId(),
		StayDuration: in.GetStayDuration(),
		ViewType:     in.GetViewType(),
		ViewCount:    in.GetViewCount(),
		AppId:        in.GetBaseReq().GetAppId(),
		MarketId:     in.GetBaseReq().GetMarketId(),
		TopicIds:     in.GetTopicIds(),
	}

	data, err := proto.Marshal(eventMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportNewUserVisitHomePage Marshal ev(%+v) err: %v", eventMsg, err)
		return err
	}

	err = g.gameActivityTaskProducer.Publish(ctx, &publisher.ProducerMessage{
		Topic: g.gameActivityTaskProducer.GetTopic(),
		Key:   sarama.StringEncoder(strconv.FormatUint(uint64(in.GetUid()), 10)),
		Value: sarama.StringEncoder(data),
	})
	return err
}
