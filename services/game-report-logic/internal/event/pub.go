package event

import (
	"context"

	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/publisher"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
)

//mockgen -destination=mocks/mock_pub.go -package=mocks golang.52tt.com/services/game-report-logic/internal/event IEventLinkProducer

const (
	TopicNewUserVisitHomePage = "new_user_visit_home_page"
)

type EventLinkProducer struct {
	topic string
	pub   publisher.Publisher
}

func NewEventLinkProducer(cfg *config.KafkaConfig) (*EventLinkProducer, error) {

	conf := kafka.DefaultConfig()
	conf.ClientID = cfg.ClientID
	conf.Producer.RequiredAcks = kafka.WaitForAll
	conf.Producer.Return.Successes = true
	conf.Producer.Return.Errors = true
	conf.ChannelBufferSize = 2048

	pub, err := kafka.NewAsyncPublisher(cfg.BrokerList(), conf, publisher.WithTopicRegisterHandler(cfg.TopicList()))
	if err != nil {
		log.Errorf("NewEventLinkProducer cfg(%+v) err %v", cfg, err)
		return nil, err
	}

	return &EventLinkProducer{
		topic: cfg.Topics,
		pub:   pub,
	}, nil
}

func (p *EventLinkProducer) Publish(ctx context.Context, msg *publisher.ProducerMessage) error {
	res := p.pub.Publish(ctx, msg)
	if res.Err != nil {
		log.ErrorWithCtx(ctx, "Publish msg(%+v) err: %v", msg, res.Err)
	}

	return res.Err
}

func (p *EventLinkProducer) GetTopic() string {
	return p.topic
}
