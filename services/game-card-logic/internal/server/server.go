package server

import (
	"context"
	"fmt"
	"golang.52tt.com/kaihei-pkg/supervision"
	"golang.52tt.com/pkg/coroutine"
	"golang.52tt.com/pkg/foundation/utils"
	game_card "golang.52tt.com/pkg/game-card"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protoGrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/services/game-card-logic/internal/conf"

	channelGA "golang.52tt.com/protocol/app/channel"
	gaGameCardPb "golang.52tt.com/protocol/app/game-card"
	topic_channel "golang.52tt.com/protocol/app/topic-channel"
	user_tag_v2 "golang.52tt.com/protocol/app/user-tag-v2"
	"golang.52tt.com/protocol/common/status"
	channelPlayTabPb "golang.52tt.com/protocol/services/channel-play-tab"
	"golang.52tt.com/protocol/services/demo/echo"
	gameCardPb "golang.52tt.com/protocol/services/game-card"
	userTagGoPb "golang.52tt.com/protocol/services/user-tag-go"
	"golang.52tt.com/services/game-card-logic/internal/mgr"
	"golang.52tt.com/services/game-card-logic/internal/rpc/client"
	"golang.52tt.com/services/game-card-logic/internal/util"
	"golang.52tt.com/services/game-card-logic/internal/util/net"
	"math/rand"
	"time"
)

const (
	GetUserInfoFail = "获取用户信息失败"
	MaxInputVal     = 100000
)

type GameCardLogic struct {
	supervisor *supervision.Supervisory
}

func NewGameCardLogic(ctx context.Context, cfg *conf.StartConfig) (*GameCardLogic, error) {
	err := cfg.Parse(ctx)
	if err != nil {
		log.ErrorWithCtx(context.Background(), "load config  fail: %v", err)
		return nil, err
	}
	log.Infof("[%s] server startup with cfg: %+v", conf.Environment, *cfg)

	client.SetUp()

	supervisorInst, err := supervision.NewSupervisory(nil, nil, nil, nil,
		client.AccountClient, client.ChannelPlayTabClient, conf.GetEnv())
	if err != nil {
		log.ErrorWithCtx(context.Background(), "NewSupervisory fail, err:%v", err)
		return nil, err
	}

	// 定时任务
	err = mgr.NewSchedule(context.Background())
	if err != nil {
		log.ErrorWithCtx(context.Background(), "NewSchedule fail, err:%v", err)
		return nil, err
	}
	rand.Seed(time.Now().UnixNano())

	return &GameCardLogic{
		supervisor: supervisorInst,
	}, nil
}

func (s *GameCardLogic) ShutDown() {
	coroutine.StopAll()
}

func (s *GameCardLogic) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *GameCardLogic) GetAllGameCardConf(ctx context.Context, req *gaGameCardPb.GetAllGameCardConfReq) (*gaGameCardPb.GetAllGameCardConfResp, error) {
	out := &gaGameCardPb.GetAllGameCardConfResp{
		GameCardConfList: make([]*gaGameCardPb.GameCardConfInfo, 0),
		MaxGameCardNum:   conf.GameCardDynamicConfig.GeMaxGameCardCount(),
	}
	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "getServiceInfo failed")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, GetUserInfoFail)
	}

	categoryIds := conf.GameCardDynamicConfig.GetFilterEntranceCategoryMap(ctx, gaGameCardPb.GameCardFilterEntrance_GAME_ZONE)
	if len(categoryIds) == 0 {
		log.ErrorWithCtx(ctx, "UserLine Time Err:categoryIds is empty")
		categoryIds = []uint32{1}
	}

	categoryGameCardConf := mgr.GetCategoryGameCardConf()
	tabImgMap := mgr.GetTabIdToGameCornerImgUrlMap()
	useCategoryGameCardConfTab := mgr.GetUseCategoryGameCardConfTab()
	respGameCardTabInfo := make([]*gaGameCardPb.SimpleTabInfo, 0, len(useCategoryGameCardConfTab))
	for _, info := range useCategoryGameCardConfTab {
		respGameCardTabInfo = append(respGameCardTabInfo, &gaGameCardPb.SimpleTabInfo{
			TabId:                info.GetId(),
			TabName:              info.GetName(),
			GameCardId:           game_card.ComposeGameCardId(categoryGameCardConf.GetGameCardId(), info.GetId()),
			GameCornerMarkImgUrl: tabImgMap[info.GetId()].GetImgUrl(),
			UGameId:              info.GetGameInfo().GetUGameId(),
		})
	}

	for _, gameCardConf := range mgr.GeAllGameCardConf() {
		if gameCardConf.IsHide {
			log.InfoWithCtx(ctx, "gameCardId:%d name:%s Conf isHide", gameCardConf.GameCardId, gameCardConf.GameCardName)
			continue
		}
		// 旧版本不下发类目游戏卡
		if gameCardConf.GetCardScopeType() == gameCardPb.CardScopeType_CARD_SCORE_TYPE_CATEGORY && serviceInfo.ClientVersion < protocol.FormatClientVersion(6, 49, 0) {
			continue
		}

		pbGameCardConf := s.transToPbGameCardConf(gameCardConf, req.GetIsShowInputOpt())

		if pbGameCardConf.GetCardScopeType() == uint32(gaGameCardPb.CardScopeType_CARD_SCORE_TYPE_CATEGORY) && categoryIds[0] == gameCardConf.GetCategoryId() {
			pbGameCardConf.CategoryGameCardConfInfo = &gaGameCardPb.CategoryGameCardConfInfo{
				CategoryId: categoryIds[0],
				TabInfo:    respGameCardTabInfo,
			}
		}
		out.GameCardConfList = append(out.GameCardConfList, pbGameCardConf)
	}
	return out, nil
}

func (s *GameCardLogic) GetGameCard(ctx context.Context, req *gaGameCardPb.GetGameCardReq) (*gaGameCardPb.GetGameCardResp, error) {
	targetUid := req.TargetUid
	out := &gaGameCardPb.GetGameCardResp{
		TargetUid:    targetUid,
		GameCardList: make([]*gaGameCardPb.GameCardInfo, 0),
	}
	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "getServiceInfo failed")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, GetUserInfoFail)
	}

	uid := serviceInfo.UserID
	clientType := uint32(serviceInfo.ClientType)
	appId := req.GetBaseReq().GetAppId()
	marketId := req.GetBaseReq().GetMarketId()

	gameCardList, err := client.GameCardClient.GetGameCard(ctx, targetUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "uid:%d GameCardClient.GetGameCard failed %v targetUid %d", uid, err, targetUid)
		return out, protocol.ToServerError(err)
	}

	for _, gameCard := range gameCardList {
		//配置是否已经不存在或者隐藏
		confGameCardId, tabId := game_card.DeComposeGameCardId(gameCard.GameCardId)
		if gameCardConf := mgr.GetGameCardConfById(confGameCardId); gameCardConf == nil {
			log.InfoWithCtx(ctx, "uid:%d, targetUid:%d gameCardId:%d, gameCardName:%s conf not find", uid, targetUid, confGameCardId, gameCardConf.GameCardName)
			continue
		} else {
			if gameCardConf.IsHide {
				log.InfoWithCtx(ctx, "uid:%d targetUid:%d gameCardId:%d, gameCardName:%s conf is hide", uid, targetUid, confGameCardId, gameCardConf.GameCardName)
				continue
			}
			gameCardConf = mgr.GetNewGameCardConf(ctx, gameCard, gameCardConf, tabId, uid, targetUid, appId, marketId)
			if gameCardConf == nil {
				log.InfoWithCtx(ctx, "gameCardConf is nil, uid:%d, targetUid:%d gameCardId:%d, tabId:%d", uid, targetUid, confGameCardId, tabId)
				continue
			}
			pbGameCard := s.transToPbGameCardInfo(uid, targetUid, gameCard, gameCardConf, req.GetIsShowInputOpt())

			//游戏截图链接替换
			for _, screenShot := range pbGameCard.ScreenshotList {
				afterImgUrl := mgr.ReplaceObsDomainByMarketId(marketId, clientType, screenShot.ImgUrl)
				screenShot.ImgUrl = afterImgUrl
			}

			out.GameCardList = append(out.GameCardList, pbGameCard)
		}
	}
	out.GameCardList = filterFastPcGameCard(ctx, clientType, out.GameCardList)
	log.DebugWithCtx(ctx, "targetUid %d, GetGameCard success %v", targetUid, utils.ToJson(out))
	return out, nil
}

func filterFastPcGameCard(ctx context.Context, clientType uint32, gameCardList []*gaGameCardPb.GameCardInfo) []*gaGameCardPb.GameCardInfo {
	if !protocol.IsFastPcClientType(clientType) {
		return gameCardList
	}
	// 极速pc端只展示对应有绑定的玩法游戏卡
	newGameCardList := make([]*gaGameCardPb.GameCardInfo, 0, len(gameCardList))
	for _, gameCard := range gameCardList {
		if !mgr.IsGameCardInFastPcClient(gameCard.GetGameCardId()) {
			log.DebugWithCtx(ctx, "gameCardId:%d not in fast pc client", gameCard.GetGameCardId())
			continue
		}
		newGameCardList = append(newGameCardList, gameCard)
	}
	return newGameCardList
}

// SetGameCard 修改接口，非覆盖式
func (s *GameCardLogic) SetGameCard(ctx context.Context, req *gaGameCardPb.SetGameCardReq) (*gaGameCardPb.SetGameCardResp, error) {
	out := &gaGameCardPb.SetGameCardResp{}

	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "SetGameCard getServiceInfo failed")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, GetUserInfoFail)
	}

	uid := serviceInfo.UserID
	reqGameCard := req.GetGameCard()
	if reqGameCard == nil {
		log.WarnWithCtx(ctx, "uid %d, SetGameCard reqGameCard is nil", uid)
		return out, protocol.NewExactServerError(nil, status.ErrGamecardSetParamErr)
	}

	log.InfoWithCtx(ctx, "uid %d, SetGameCard req:%v", uid, utils.ToJson(req))

	//黑产不能修改
	hitUid, err := client.DarkClient.UserBehaviorCheck(ctx, uid)
	if err != nil { //失败不影响接口
		log.ErrorWithCtx(ctx, "AntispamLogicClient.UserBehaviorCheck failed %v, uid %d", err, uid)
	}
	if err == nil && hitUid > 0 {
		log.InfoWithCtx(ctx, "blackRiskHitUid %d", hitUid)
		return out, protocol.NewExactServerError(nil, status.ErrAccountAbnormalFunctionInvalid)
	}

	// 游戏卡配置是否已经删除了
	confGameCardId, _ := game_card.DeComposeGameCardId(reqGameCard.GetGameCardId())
	gameCardConf := mgr.GetGameCardConfById(confGameCardId)
	if gameCardConf == nil || gameCardConf.IsHide {
		log.WarnWithCtx(ctx, "uid:%d,  gameCardId:%d, gameCardName:%s conf del or hide", uid, reqGameCard.GameCardId, reqGameCard.GameCardName)
		return out, protocol.NewExactServerError(nil, status.ErrUsertagConfDel)
	}

	//游戏卡截图参数检查
	if !mgr.CheckReqGameScreenshot(req.GetGameCard().GetScreenshotList()) {
		log.WarnWithCtx(ctx, "uid:%d,  gameCardId:%d, gameCardName:%s gameScreenShot param err, screenShotList:%+v", uid, reqGameCard.GameCardId, reqGameCard.GameCardName, req.GameCard.ScreenshotList)
		return out, protocol.NewExactServerError(nil, status.ErrGamecardSetParamErr)
	}
	// 检查配置项数量
	if pass, toast := mgr.CheckReqOptList(reqGameCard, gameCardConf); !pass {
		log.WarnWithCtx(ctx, "SetGameCard reqOptList param, invalid, uid:%d, gameCardId:%d, optList:+%v", uid, reqGameCard.GameCardId, reqGameCard.OptList)
		return out, protocol.NewExactServerError(nil, status.ErrGamecardSetParamErr, toast)
	}

	//获取要修改的游戏卡
	gameCardList, err := client.GameCardClient.GetGameCard(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GameCardClient.GetGameCard failed %v, uid %d", err, uid)
		return out, protocol.ToServerError(err)
	}
	var targetGameCard *gameCardPb.GameCardInfo
	for _, gameCard := range gameCardList {
		if gameCard.GameCardId == reqGameCard.GameCardId {
			targetGameCard = gameCard
			break
		}
	}
	if targetGameCard == nil {
		log.WarnWithCtx(ctx, "uid %d, gameCard not exist, gameCardId:%d, gameCardName:%s", uid, reqGameCard.GameCardId, reqGameCard.GameCardName)
		return out, protocol.NewExactServerError(nil, status.ErrGamecardSetParamErr)
	}

	//填充targetGameCard
	needAuditNickNameList, needAuditScreenShotList, checkErr := mgr.CheckAndTransSetGameCard(ctx, uid, req, targetGameCard, gameCardConf)
	if checkErr != nil {
		log.ErrorWithCtx(ctx, "uid:%d SetGameCard checkAndTransSetGameCard failed %v", checkErr)
		return out, protocol.ToServerError(checkErr)
	}

	appId := req.GetBaseReq().GetAppId()
	marketId := req.GetBaseReq().GetMarketId()
	ip := net.ConvertToStringIp(serviceInfo.ClientIP)
	opType := uint32(channelGA.ENUM_SET_USERTAG_OPER_ENUM_SET_USERTAG_OPER_MODIFY)

	if len(needAuditNickNameList) > 0 {
		// 昵称列表有修改，需要清空原有昵称
		targetGameCard.GameNicknameList = make([]*gameCardPb.GameNickNameInfo, 0, len(needAuditNickNameList))
		//昵称审核
		err := mgr.AuditGameNickNameList(ctx, uid, appId, marketId, ip, targetGameCard, needAuditNickNameList)
		if err != nil {
			return out, err
		}
	}

	//set
	err = client.GameCardClient.SetGameCard(ctx, uid, appId, marketId, targetGameCard)
	if err != nil {
		log.ErrorWithCtx(ctx, "GameCardClient.SetGameCard failed %v, uid %d, gameName:%s", err, uid, targetGameCard.GameCardName)
		return out, protocol.ToServerError(err)
	}

	//游戏截图审核
	if len(needAuditScreenShotList) > 0 {
		err := mgr.AuditGameScreenshot(ctx, uid, opType, appId, marketId, ip, needAuditScreenShotList, targetGameCard)

		if err != nil {
			log.ErrorWithCtx(ctx, "uid %d, gameCard:%s, auditGameScreenshot failed %v", uid, targetGameCard.GameCardName, err)
			return out, err
		}
	}

	log.InfoWithCtx(ctx, "uid:%d, SetGameCard success", uid)
	return out, nil
}

// CreateGameCardInRegister 注册流程调用的接口，包括创建游戏卡和生日(普通标签在注册流程只剩生日标签)
func (s *GameCardLogic) CreateGameCardInRegister(ctx context.Context, req *gaGameCardPb.CreateGameCardInRegisterReq) (*gaGameCardPb.CreateGameCardInRegisterResp, error) {
	out := &gaGameCardPb.CreateGameCardInRegisterResp{}
	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "CreateGameCardInRegister getServiceInfo failed")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, GetUserInfoFail)
	}

	uid := serviceInfo.UserID

	log.DebugWithCtx(ctx, "uid:%d CreateGameCardInRegister req:%v", uid, utils.ToJson(req))

	//滑块验证
	err := mgr.ShuMeiVerify(ctx, uid, req)
	if err != nil {
		log.InfoWithCtx(ctx, "uid:%d, shuMeiVerify no pass:%v", uid, err)
		return out, protocol.ToServerError(err)
	}

	gameCardList := make([]*gameCardPb.GameCardInfo, 0)
	if len(req.GameCardList) > 0 {
		for _, gaGameCard := range req.GameCardList {
			gameCardConf := mgr.GetGameCardConfById(gaGameCard.GameCardId)
			//游戏卡配置是否已经删除了
			if gameCardConf == nil || gameCardConf.IsHide {
				log.InfoWithCtx(ctx, "uid:%d,  gameCardId:%d, gameCardName:%s conf del or hide", uid, gaGameCard.GameCardId, gaGameCard.GameCardName)
				continue
			}
			// 检查配置项数量
			if pass, _ := mgr.CheckReqOptList(gaGameCard, gameCardConf); !pass {
				log.WarnWithCtx(ctx, "CreateGameCardInRegister reqOptList param, invalid, uid:%d, gameCardId:%d, optList:+%v", uid, gaGameCard.GameCardId, gaGameCard.OptList)
				continue
			}
			log.DebugWithCtx(ctx, "uid %d, CreateGameCardInRegister,gameCard:%v", uid, utils.ToJson(gaGameCard))
			svrGameCard := transToServerGameCardInfo(gaGameCard, gameCardConf)
			//注册的时候不能填昵称和截图
			svrGameCard.GameNickname = ""
			svrGameCard.GameNicknameList = make([]*gameCardPb.GameNickNameInfo, 0)
			svrGameCard.ScreenshotList = make([]*gameCardPb.GameScreenshot, 0)

			gameCardList = append(gameCardList, svrGameCard)
		}

		appId := req.GetBaseReq().GetAppId()
		marketId := req.GetBaseReq().GetMarketId()
		err = client.GameCardClient.CreateRegisterGameCard(ctx, uid, appId, marketId, gameCardList)
		if err != nil {
			log.ErrorWithCtx(ctx, "uid %d, GameCardClient.CreateRegisterGameCard failed %v", uid, err)
			return out, protocol.ToServerError(err)
		}
	}

	//生日标签
	userTagList := make([]*userTagGoPb.UserTagBase, 0)
	log.DebugWithCtx(ctx, "uid:%d, BirthDay:%s", uid, req.BirthDay)
	if len(req.GetBirthDay()) != 0 {
		birthDayUserTag := &userTagGoPb.UserTagBase{
			TagType: uint32(user_tag_v2.UserTagTypeV2_TAG_TYPE_BIRTHDAY),
			TagName: req.BirthDay,
			TagId:   0,
		}
		userTagList = append(userTagList, birthDayUserTag)
	}
	//usertaglist 为0时，也需要调接口，覆盖式的，清掉标签，同时触发一次kafka事件
	err = client.UserTagGoClient.SetUserTag(ctx, uid, 0, 1, userTagList)
	if err != nil {
		log.ErrorWithCtx(ctx, "uid:%d, UserTagGoClient.SetUserTag failed %v", uid, err)
		return out, protocol.ToServerError(err)
	}

	//旧的数据上报
	_ = mgr.OldUserTagUpdateEvent(ctx, uid, req.GetBaseReq().GetAppId(), req.GetBaseReq().GetMarketId(), userTagList, gameCardList)

	log.DebugWithCtx(ctx, "uid %d, CreateGameCardInRegister success", uid)

	return out, nil
}

func (s *GameCardLogic) CreateGameCard(ctx context.Context, req *gaGameCardPb.CreateGameCardReq) (*gaGameCardPb.CreateGameCardResp, error) {
	out := &gaGameCardPb.CreateGameCardResp{}

	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "CreateGameCard getServiceInfo failed")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, GetUserInfoFail)
	}

	uid := serviceInfo.UserID
	reqGameCard := req.GetGameCard()
	if reqGameCard == nil {
		log.WarnWithCtx(ctx, "uid %d, CreateGameCard reqGameCard is nil", uid)
		return out, protocol.NewExactServerError(nil, status.ErrGamecardSetParamErr)
	}

	log.InfoWithCtx(ctx, "uid %d, CreateGameCard req:%v", uid, utils.ToJson(req))

	//黑产不能修改
	hitUid, err := client.DarkClient.UserBehaviorCheck(ctx, uid)
	if err != nil { //失败不影响接口
		log.ErrorWithCtx(ctx, "AntispamLogicClient.UserBehaviorCheck failed %v, uid %d", err, uid)
	}
	if err == nil && hitUid > 0 {
		log.InfoWithCtx(ctx, "blackRiskHitUid %d", hitUid)
		return out, protocol.NewExactServerError(nil, status.ErrAccountAbnormalFunctionInvalid)
	}

	//游戏卡配置是否已经删除了
	confGameCardId, _ := game_card.DeComposeGameCardId(reqGameCard.GetGameCardId())
	gameCardConf := mgr.GetGameCardConfById(confGameCardId)
	if gameCardConf == nil || gameCardConf.IsHide {
		log.WarnWithCtx(ctx, "uid:%d,  gameCardId:%d, gameCardName:%s conf del or hide", uid, reqGameCard.GameCardId, reqGameCard.GameCardName)
		return out, protocol.NewExactServerError(nil, status.ErrUsertagConfDel)
	}

	if !mgr.CheckReqGameScreenshot(reqGameCard.ScreenshotList) {
		log.ErrorWithCtx(ctx, "uid:%d,  gameCardId:%d, gameCardName:%s gameScreenShot param err", uid, reqGameCard.GameCardId, reqGameCard.GameCardName)
		return out, protocol.NewExactServerError(nil, status.ErrGamecardSetParamErr)
	}
	// 检查配置项数量
	if pass, toast := mgr.CheckReqOptList(reqGameCard, gameCardConf); !pass {
		log.WarnWithCtx(ctx, "CreateGameCard reqOptList param, invalid, uid:%d, gameCardId:%d, optList:+%v", uid, reqGameCard.GameCardId, reqGameCard.OptList)
		return out, protocol.NewExactServerError(nil, status.ErrGamecardSetParamErr, toast)
	}

	appId := req.GetBaseReq().GetAppId()
	marketId := req.GetBaseReq().GetMarketId()
	ip := net.ConvertToStringIp(serviceInfo.ClientIP)
	opType := uint32(channelGA.ENUM_SET_USERTAG_OPER_ENUM_SET_USERTAG_OPER_ADD)

	svrGameCard := transToServerGameCardInfo(reqGameCard, gameCardConf)
	// 客户端传过来的是一个key，要生成对应的url
	for _, screenShot := range svrGameCard.ScreenshotList {
		afterImgUrl := mgr.GenUrl(screenShot.ImgUrl, screenShot.NewObs)
		screenShot.ImgUrl = afterImgUrl
	}
	// 旧昵称字段设置为空
	svrGameCard.GameNickname = ""

	//昵称审核
	needAuditNickNameList := mgr.GetNeedAuditGameNickList(reqGameCard, gameCardConf)
	svrGameCard.GameNicknameList = make([]*gameCardPb.GameNickNameInfo, 0, len(needAuditNickNameList))

	err2 := mgr.AuditGameNickNameList(ctx, uid, appId, marketId, ip, svrGameCard, needAuditNickNameList)
	if err2 != nil {
		return out, err
	}

	//create
	err = client.GameCardClient.CreateGameCard(ctx, uid, appId, marketId, svrGameCard)
	if err != nil {
		log.ErrorWithCtx(ctx, "uid:%d, gameName:%s, GameCardClient.CreateGameCard failed:%v", uid, reqGameCard.GameCardName, err)
		return out, protocol.ToServerError(err)
	}

	if len(svrGameCard.ScreenshotList) > 0 {
		err := mgr.AuditGameScreenshot(ctx, uid, opType, appId, marketId, ip, svrGameCard.ScreenshotList, svrGameCard)
		if err != nil {
			log.ErrorWithCtx(ctx, "uid %d, gameName:%s, auditGameScreenshot failed %v", uid, svrGameCard.GameCardName, err)
			return out, err
		}
	}
	log.InfoWithCtx(ctx, "uid %d, createGameCard success, gameCardId:%d, gameCardName:%s", uid, svrGameCard.GameCardId, svrGameCard.GameCardName)

	return out, nil
}

func (s *GameCardLogic) DeleteGameCard(ctx context.Context, req *gaGameCardPb.DeleteGameCardReq) (*gaGameCardPb.DeleteGameCardResp, error) {
	out := &gaGameCardPb.DeleteGameCardResp{}

	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "DeleteGameCard getServiceInfo failed")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, GetUserInfoFail)
	}
	uid := serviceInfo.UserID
	log.InfoWithCtx(ctx, "uid %d, DeleteGameCard req:%v", uid, utils.ToJson(req))

	//黑产不能修改
	hitUid, err := client.DarkClient.UserBehaviorCheck(ctx, uid)
	if err != nil { //失败不影响接口
		log.ErrorWithCtx(ctx, "AntispamLogicClient.UserBehaviorCheck failed %v, uid %d", err, uid)
	}
	if err == nil && hitUid > 0 {
		log.InfoWithCtx(ctx, "blackRiskHitUid %d", hitUid)
		return out, protocol.NewExactServerError(nil, status.ErrAccountAbnormalFunctionInvalid)
	}

	if req.GameCardId == 0 {
		log.ErrorWithCtx(ctx, "uid %d, DeleteGameCard cardId is zero", uid)
		return out, protocol.NewExactServerError(nil, status.ErrGamecardSetParamErr)
	}

	appId := req.GetBaseReq().GetAppId()
	marketId := req.GetBaseReq().GetMarketId()
	err = client.GameCardClient.DeleteGameCard(ctx, uid, appId, marketId, req.GameCardId)
	if err != nil {
		log.ErrorWithCtx(ctx, "uid %d, GameCardClient.DeleteGameCard failed %v, gameCardId:%d", uid, err, req.GameCardId)
		return out, protocol.ToServerError(err)
	}

	log.InfoWithCtx(ctx, "deleteGameCard success,uid %d, gameCardId:%d", uid, req.GameCardId)

	return out, nil
}

func (s *GameCardLogic) BatchCreateGameCard(ctx context.Context, req *gaGameCardPb.BatchCreateGameCardReq) (*gaGameCardPb.BatchCreateGameCardResp, error) {
	out := &gaGameCardPb.BatchCreateGameCardResp{}
	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "BatchCreateGameCard getServiceInfo failed")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, GetUserInfoFail)
	}
	uid := serviceInfo.UserID

	log.InfoWithCtx(ctx, "uid %d, BatchCreateGameCard req:%v", uid, utils.ToJson(req))

	if len(req.GameCard) == 0 {
		log.WarnWithCtx(ctx, "BatchCreateGameCard req.GameCard is empty")
		return out, nil
	}

	userGameCards, err := client.GameCardClient.GetGameCard(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "client.GameCardClient.GetGameCard fail, err:%s, uid:%d", err.Error(), uid)
		return out, err
	}
	hasGameCardLen := len(userGameCards)
	maxGameCardCount := int(conf.GameCardDynamicConfig.GeMaxGameCardCount())
	if len(req.GameCard) >= (maxGameCardCount - hasGameCardLen) {
		if maxGameCardCount-hasGameCardLen <= 0 {
			log.ErrorWithCtx(ctx, "UserLine Time Err: BatchCreateGameCard hasGameCardLen better than MaxGameCardNum, uid:%d, hasGameCardLen:%d, req.GameCard:%v",
				uid, hasGameCardLen, req.GameCard)
			return out, nil
		}
		log.WarnWithCtx(ctx, "UserLine Time Err: BatchCreateGameCard over limit, uid:%d, hasGameCardLen:%d, req:%+v", uid, hasGameCardLen, req)
		req.GameCard = req.GameCard[:(maxGameCardCount - hasGameCardLen)]
	}

	gameCardList := make([]*gameCardPb.GameCardInfo, 0, len(req.GameCard))
	for _, gaGameCard := range req.GameCard {
		//游戏卡配置是否已经删除了
		confGameCardId, _ := game_card.DeComposeGameCardId(gaGameCard.GetGameCardId())
		gameCardConf := mgr.GetGameCardConfById(confGameCardId)
		if gameCardConf == nil || gameCardConf.IsHide {
			log.WarnWithCtx(ctx, "BatchCreateGameCard uid:%d, gameCardId:%d, gameCardName:%s conf del or hide", uid, gaGameCard.GameCardId, gaGameCard.GameCardName)
			continue
		}
		svrGameCard := transToServerGameCardInfo(gaGameCard, gameCardConf)
		svrGameCard.GameNickname = ""
		svrGameCard.GameNicknameList = make([]*gameCardPb.GameNickNameInfo, 0)
		svrGameCard.ScreenshotList = make([]*gameCardPb.GameScreenshot, 0)
		gameCardList = append(gameCardList, svrGameCard)
	}
	if len(gameCardList) == 0 {
		log.ErrorWithCtx(ctx, "UserLine Time Err:BatchCreateGameCard no real game card create, uid:%d, req:%+v", uid, req)
		return out, nil
	}

	appId := req.GetBaseReq().GetAppId()
	marketId := req.GetBaseReq().GetMarketId()
	createErr := client.GameCardClient.CreateRegisterGameCard(ctx, uid, appId, marketId, gameCardList)
	if createErr != nil {
		log.ErrorWithCtx(ctx, "UserLine Time Err: CreateRegisterGameCard fail, err:%s, uid:%d, appId%d, marketId:%d, gameGardList:%+v",
			uid, createErr.Error(), appId, marketId, gameCardList)
		return out, protocol.ToServerError(createErr)
	}

	return out, nil
}

// GetGameCardConf 获取游戏专区下的游戏卡，客户端缓存，每个设备账号正常只会请求一次
func (s *GameCardLogic) GetGameCardConf(ctx context.Context, req *gaGameCardPb.GetGameCardConfReq) (*gaGameCardPb.GetGameCardConfResp, error) {
	out := &gaGameCardPb.GetGameCardConfResp{
		MaxGameCardNum: conf.GameCardDynamicConfig.GeMaxGameCardCount(),
	}
	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "CreateGameCardInRegister getServiceInfo failed")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, GetUserInfoFail)
	}
	uid := serviceInfo.UserID

	categoryIds := conf.GameCardDynamicConfig.GetFilterEntranceCategoryMap(ctx, req.GetFilterEntrance())
	if len(categoryIds) == 0 {
		log.WarnWithCtx(ctx, "UserLine Time Err:GetGameCardConf GetFilterEntranceCategoryMap categoryIds is empty, filterEntrance:%v", req.GetFilterEntrance())
		// 兜底一起开黑类目
		categoryIds = []uint32{1}
	}
	// 获取tab缓存数据
	tabInfos := mgr.GetAllTabInfo()
	out.GameCardConfList = make([]*gaGameCardPb.GameCardConfInfo, 0, len(tabInfos))
	repeatMap := make(map[uint32]*gaGameCardPb.GameCardConfInfo, len(tabInfos))
	for _, tabInfo := range tabInfos {
		if util.Uint32IndexOf(categoryIds, tabInfo.GetCategoryId()) == -1 {
			continue
		}
		gameCardConf := mgr.GetGameCardConfById(tabInfo.GetGameInfo().GetGameCardId())
		if gameCardConf == nil {
			continue
		}
		if _, ok := repeatMap[gameCardConf.GameCardId]; ok {
			continue
		}
		item := s.transToPbGameCardConf(gameCardConf, req.GetIsShowInputOpt())
		out.GameCardConfList = append(out.GameCardConfList, item)
		repeatMap[gameCardConf.GameCardId] = item
	}

	userGameCards, err := client.GameCardClient.GetGameCard(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "UserLine Time Err:GetGameCardConf GetGameCard fail, err:%s, uid:%d", err.Error(), uid)
		return out, err
	}

	// 用户该类目下的游戏卡数量为0时true，展示最近玩的游戏弹窗
	out.ShowRecentPlay = true
	for _, userGameCard := range userGameCards {
		if _, ok := repeatMap[userGameCard.GameCardId]; ok {
			out.ShowRecentPlay = false
			out.GameCardConfList = nil // 不展示弹窗，就不返回数据了
			break
		}
	}
	out.CurGameCardNum = uint32(len(userGameCards))
	return out, nil
}

// GetGameCardByTab 游戏专区获取用户游戏卡数据和对应玩法的游戏卡配置，还需要根据配置返回展示的头像和场数据
func (s *GameCardLogic) GetGameCardByTab(ctx context.Context, req *gaGameCardPb.GetGameCardByTabReq) (*gaGameCardPb.GetGameCardByTabResp, error) {
	out := &gaGameCardPb.GetGameCardByTabResp{
		GameCardList: new(gaGameCardPb.GameCardInfo),
	}

	log.DebugWithCtx(ctx, "GetGameCardByTab req:%+v", req)

	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "getServiceInfo failed")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, GetUserInfoFail)
	}
	clientType := uint32(serviceInfo.ClientType)
	marketId := req.GetBaseReq().GetMarketId()

	tabInfo := mgr.GetTabInfoById(req.GetTabId())
	if tabInfo == nil {
		log.WarnWithCtx(ctx, "UserLine Err:GetGameCardByTab tabInfo is nil, tabId:%d, serviceInfo:%+v", req.GetTabId(), serviceInfo)
		return out, protocol.NewExactServerError(nil, status.ErrGameTabNotExistErr)
	}
	if protocol.IsFastPcClientType(clientType) {
		if s.supervisor.IsFastPcCategoryFilter(serviceInfo, tabInfo.GetId(), mgr.GetFastPCCategoryConfig()) {
			log.WarnWithCtx(ctx, ":GetGameCardByTab tabId:%d not in fast pc", tabInfo.GetId())
			return out, protocol.NewExactServerError(nil, status.ErrGameTabNotExistErr)
		}
		if s.supervisor.MiniGameStageStrategy(tabInfo, serviceInfo, mgr.GetTabWhiteListMap()) {
			log.WarnWithCtx(ctx, "GetGameCardByTab tabId:%d strategy filter", tabInfo.GetId())
			return out, protocol.NewExactServerError(nil, status.ErrGameTabNotExistErr)
		}
	}

	out.TabName = tabInfo.GetName()
	out.CardsImageUrl = tabInfo.GetCardsImageUrl()
	out.FastPcRoomBackgroundImgUrl = tabInfo.GetFastPcRoomBackgroundImgUrl()
	gameCardConf := mgr.GetGameCardConfById(tabInfo.GetGameInfo().GetGameCardId())

	// 通用游戏卡，排除非开黑的
	if tabInfo.GetCategoryMapping() == uint32(topic_channel.CategoryType_Gangup_type) && gameCardConf == nil && serviceInfo.ClientVersion >= protocol.FormatClientVersion(6, 49, 0) && tabInfo.GetGameInfo() != nil {
		gameCardConf = mgr.GetCategoryGameCardConf()
	}

	// 没有游戏卡直接返回
	if gameCardConf == nil || gameCardConf.GetGameCardId() == 0 {
		log.WarnWithCtx(ctx, "not bind gameCard, tabId:%d, gameCardId:%d", tabInfo.GetId(), tabInfo.GetGameInfo().GetGameCardId())
		return out, nil
	}

	userGameCardList, err := client.GameCardClient.GetGameCard(ctx, serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "GameCardClient.GetGameCard fail, err:%v, uid:%d", serviceInfo.UserID)
		return out, protocol.ToServerError(err)
	}

	for _, uGameCard := range userGameCardList {
		confGameCardId, tabId := game_card.DeComposeGameCardId(uGameCard.GameCardId)
		if mgr.GetCategoryGameCardConf().GetGameCardId() == confGameCardId {
			if tabId == tabInfo.GetId() {
				pbGameCard := s.transToPbGameCardInfo(serviceInfo.UserID, serviceInfo.UserID, uGameCard, gameCardConf, req.GetIsShowInputOpt())
				//游戏截图链接替换
				for _, gameCard := range pbGameCard.ScreenshotList {
					gameCard.ImgUrl = mgr.ReplaceObsDomainByMarketId(marketId, clientType, gameCard.ImgUrl)
				}
				out.GameCardList = pbGameCard
				break
			}
		} else {
			if confGameCardId == gameCardConf.GameCardId {
				pbGameCard := s.transToPbGameCardInfo(serviceInfo.UserID, serviceInfo.UserID, uGameCard, gameCardConf, req.GetIsShowInputOpt())
				//游戏截图链接替换
				for _, gameCard := range pbGameCard.ScreenshotList {
					gameCard.ImgUrl = mgr.ReplaceObsDomainByMarketId(marketId, clientType, gameCard.ImgUrl)
				}
				out.GameCardList = pbGameCard
				break
			}
		}

	}
	// 有游戏卡配置，但用户没有创建，返回配置
	if out.GameCardList.GetConf() == nil {
		out.GameCardList.Conf = s.transToPbGameCardConf(gameCardConf, req.GetIsShowInputOpt())
	}
	// 如果是通用游戏卡的，替换角标图等数据
	if protocol.IsFastPcClientType(clientType) && mgr.GetCategoryGameCardConf().GetGameCardId() == out.GameCardList.GetConf().GetGameCardId() &&
		out.GameCardList.GetConf().GetCardScopeType() == uint32(gaGameCardPb.CardScopeType_CARD_SCORE_TYPE_CATEGORY) {

		tabImgMap := mgr.GetTabIdToGameCornerImgUrlMap()
		out.GameCardList.Conf.GameCardName = tabInfo.GetName()
		out.GameCardList.Conf.UGameId = tabInfo.GetGameInfo().GetUGameId()
		out.GameCardList.Conf.GameCardId = game_card.ComposeGameCardId(out.GameCardList.GetConf().GetGameCardId(), tabInfo.GetId())
		out.GameCardList.Conf.GameCornerMarkImgUrl = tabImgMap[tabInfo.GetId()].GetImgUrl()
	}

	// 游戏卡配置了展示组队数据
	if out.GameCardList.GetConf().GetShowTeamNum() {
		teamNum := mgr.GetTeamNumByTabId(ctx, tabInfo.GetId())
		accounts := mgr.GetRandomAccounts(4)
		if teamNum > 0 && len(accounts) > 0 {
			out.GameCardList.Conf.ShowTeamText = fmt.Sprintf("%s%s", GetNumText(int(teamNum)), out.GameCardList.GetConf().GetShowTeamText())
			out.GameCardList.Conf.Accounts = accounts

		} else {
			out.GameCardList.Conf.ShowTeamNum = false
		}
	}

	// 游戏专区是否需要展示快速匹配入口
	resp, err := client.ChannelPlayTabClient.GetNewQuickMatchConfig(ctx, &channelPlayTabPb.GetNewQuickMatchConfigReq{
		TabId: tabInfo.GetId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "TopicTabClient.GetNewQuickMatchConfig err: %v", err)
		return out, err
	}
	if resp.GetConfig() != nil {
		out.GameCardList.Conf.HasQuickMatchEntrance = true
		// 展示快速匹配入口的情况下，必须展示头像
		if len(out.GameCardList.GetConf().GetAccounts()) == 0 {
			out.GameCardList.Conf.Accounts = mgr.GetRandomAccounts(4)
		}
	}
	return out, nil
}

// GetGameCardConfByTabIds 根据玩法id获取游戏卡配置和玩法id，新用户承接注册时调用
func (s *GameCardLogic) GetGameCardConfByTabIds(ctx context.Context, req *gaGameCardPb.GetGameCardConfByTabIdsReq) (*gaGameCardPb.GetGameCardConfByTabIdsResp, error) {
	out := &gaGameCardPb.GetGameCardConfByTabIdsResp{}

	serviceInfo, ok := protoGrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "getServiceInfo failed")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, GetUserInfoFail)
	}

	repeatMap := make(map[uint32]struct{})
	for _, tabId := range req.GetTabIds() {
		if tabInfo := mgr.GetTabInfoById(tabId); tabInfo.GetGameInfo() != nil {
			if _, ok := repeatMap[tabInfo.GetGameInfo().GameCardId]; ok {
				continue
			}
			gameCardInfo := &gaGameCardPb.NewGameCardConfInfo{}
			if req.GetRequestSource() == uint32(gaGameCardPb.GetGameCardConfByTabIdsReq_REQUEST_SOURCE_IM_GAME_CARD) {
				// 过滤非一起开黑玩法
				if topic_channel.CategoryType(tabInfo.GetCategoryMapping()) != topic_channel.CategoryType_Gangup_type {
					log.DebugWithCtx(ctx, "GetGameCardConfByTabIds tabId:%d(tabName:%s) is not CategoryType_Gangup_type", tabId, tabInfo.GetName(), serviceInfo.UserID)
					continue
				}
				gameCardInfo = &gaGameCardPb.NewGameCardConfInfo{
					GameCardId:   tabInfo.GetGameInfo().GameCardId,
					GameCardName: tabInfo.GetGameInfo().GameCardName,
					UGameId:      tabInfo.GetGameInfo().UGameId,
					TabId:        tabId,
				}
				gameCardConf := mgr.GetGameCardConfById(tabInfo.GetGameInfo().GameCardId)
				if gameCardConf == nil { // 没有游戏卡配置，则取通用游戏卡配置
					gameCardConf = mgr.GetCategoryGameCardConf()
					// 通用游戏卡的名称取对应玩法名称
					gameCardInfo.GameCardName = tabInfo.GetName()
				}
				gameCardInfo.GameIconImgUrl = gameCardConf.GetGameIconImgUrl()
			} else {
				gameCardInfo = &gaGameCardPb.NewGameCardConfInfo{
					GameCardId:   tabInfo.GetGameInfo().GameCardId,
					GameCardName: tabInfo.GetGameInfo().GameCardName,
					UGameId:      tabInfo.GetGameInfo().UGameId,
					TabId:        tabId,
				}
			}
			out.GameCardConfList = append(out.GameCardConfList, gameCardInfo)

			repeatMap[gameCardInfo.GameCardId] = struct{}{}

		} else {
			log.WarnWithCtx(ctx, "UserLine Time Err:GetGameCardConfByTabIds not bind gameCard, uid:%d, tabId:%d, gameInfo:%+v",
				serviceInfo.UserID, tabInfo.GetId(), tabInfo.GetGameInfo())
		}

	}
	out.MaxGameCardNum = conf.GameCardDynamicConfig.GeMaxGameCardCount()
	log.DebugWithCtx(ctx, "GetGameCardConfByTabIds req:%+v, out:%+v", req, out)
	return out, nil
}
