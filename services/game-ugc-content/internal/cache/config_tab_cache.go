package cache

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/game-ugc-content"
	"golang.52tt.com/services/game-ugc-content/internal/dao"
	"golang.52tt.com/services/game-ugc-content/internal/model"
	"strconv"
	"strings"
)

var mongoDao dao.IMongoDao

type ConfigTabCache struct {
	ConfigTabInfoMapCache       map[string]*pb.ConfigTabInfo       // 动态tab缓存, key: config_tab_id
	GameConfigTabDetailMapCache map[string]*pb.GameConfigTabDetail // 不同分类下玩法绑定动态tab信息缓存, key: tab_sub_type-tab_id
	GamePalTabsMapCache         map[uint32]*pb.ConfigTabInfo       // 需要展示的玩法搭子卡配置缓存, key: tab_id
	visibleConfigTabMapCache    map[string]*pb.ConfigTabInfo       // 可见的configTab缓存, key: config_tab_type-tab_id
}

var ConfigTabInfoCache = &ConfigTabCache{}

func refreshConfigTabCache(ctx context.Context) error {
	configTabs, err := mongoDao.GetAllConfigTabs(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "RefreshConfigTabMapCache failed, MongoDao.GetConfigTabsByConfigTabTypeAndTabId err: %v", err)
		return err
	}

	topicIds := make([]string, 0)
	for _, configTab := range configTabs {
		if configTab.ConfigTabType != dao.ConfigTabTypePostTab {
			continue
		}
		if len(configTab.BindTopics) > 0 {
			for _, bindTopic := range configTab.BindTopics {
				topicIds = append(topicIds, bindTopic.TopicId)
			}
		} else {
			topicIds = append(topicIds, configTab.DefaultTopicId)
			topicIds = append(topicIds, configTab.OtherTopicIds...)
		}
	}
	topicMap, err := getTopicMap(ctx, topicIds)
	if err != nil {
		return err
	}

	configTabMap := make(map[string]*pb.ConfigTabInfo)
	gameConfigTabMap := make(map[string][]*dao.ConfigTab)
	gamePalTabsMap := make(map[uint32]*pb.ConfigTabInfo)
	visibleConfigTabMap := make(map[string]*pb.ConfigTabInfo)
	for _, configTab := range configTabs {
		// 过滤掉不展示的tab
		if configTab.IsHide {
			continue
		}
		if configTab.ConfigTabType == dao.ConfigTabTypeGamePalCard && configTab.TabSubType == uint32(pb.TabSubType_GAME) {
			gamePalTabsMap[configTab.TabId] = model.ConvertConfigTab2PbConfigTabInfo(configTab, topicMap)
		}
		if configTab.ConfigTabType != dao.ConfigTabTypePostTab && configTab.TabSubType == uint32(pb.TabSubType_GAME) {
			key := genGameConfigTabKey(configTab.ConfigTabType, configTab.TabId)
			visibleConfigTabMap[key] = model.ConvertConfigTab2PbConfigTabInfo(configTab, topicMap)
		}
		configTabMap[configTab.ConfigTabId] = model.ConvertConfigTab2PbConfigTabInfo(configTab, topicMap)
		key := genGameConfigTabKey(configTab.TabSubType, configTab.TabId)
		gameConfigTabMap[key] = append(gameConfigTabMap[key], configTab)
	}

	gameConfigTabDetailMap := make(map[string]*pb.GameConfigTabDetail)
	for key, configTabList := range gameConfigTabMap {
		_, tabId, err := decodeGameConfigTabKey(key)
		if err != nil {
			log.ErrorWithCtx(ctx, "RefreshConfigTabMapCache invalid gameConfigTabKey:%s", key)
			continue
		}
		gameConfigTabDetailMap[key] = model.ConvertGameConfigTabDetail2Pb(configTabList, tabId, topicMap)
	}

	tmpCache := &ConfigTabCache{}
	if len(configTabMap) > 0 {
		tmpCache.ConfigTabInfoMapCache = configTabMap
	} else {
		tmpCache.ConfigTabInfoMapCache = ConfigTabInfoCache.ConfigTabInfoMapCache
	}

	if len(gameConfigTabDetailMap) > 0 {
		tmpCache.GameConfigTabDetailMapCache = gameConfigTabDetailMap
	} else {
		tmpCache.GameConfigTabDetailMapCache = ConfigTabInfoCache.GameConfigTabDetailMapCache
	}
	if len(gamePalTabsMap) > 0 {
		tmpCache.GamePalTabsMapCache = gamePalTabsMap
	} else {
		tmpCache.GamePalTabsMapCache = ConfigTabInfoCache.GamePalTabsMapCache
	}
	if len(visibleConfigTabMap) > 0 {
		tmpCache.visibleConfigTabMapCache = visibleConfigTabMap
	} else {
		tmpCache.visibleConfigTabMapCache = ConfigTabInfoCache.visibleConfigTabMapCache
	}

	ConfigTabInfoCache = tmpCache
	log.InfoWithCtx(ctx, "refreshConfigTabCache success, len(ConfigTabInfoMapCache): %d, len(GameConfigTabDetailMapCache): %d, "+
		"len(GamePalTabsMapCache):%d", len(tmpCache.ConfigTabInfoMapCache), len(tmpCache.GameConfigTabDetailMapCache),
		len(tmpCache.GamePalTabsMapCache))

	return nil
}

func genGameConfigTabKey(tabSubtype, tabId uint32) string {
	return fmt.Sprintf("%d-%d", tabSubtype, tabId)
}

func decodeGameConfigTabKey(key string) (uint32, uint32, error) {
	items := strings.Split(key, "-")
	if len(items) != 2 {
		return 0, 0, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "invalid GameConfigTabKey")
	}
	tabSubtype, err := strconv.Atoi(items[0])
	if err != nil {
		return 0, 0, err
	}
	tabId, err := strconv.Atoi(items[1])
	if err != nil {
		return 0, 0, err
	}

	return uint32(tabSubtype), uint32(tabId), nil
}

func getTopicMap(ctx context.Context, topicIds []string) (map[string]*dao.TopicInfo, error) {
	topicMap := make(map[string]*dao.TopicInfo)
	topicList, err := mongoDao.BatGetTopicByIds(ctx, topicIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "getTopicMap MongoDao.BatGetTopicByIds err: %v", err)
		return topicMap, err
	}
	for _, topic := range topicList {
		topicMap[topic.TopicId] = topic
	}
	log.InfoWithCtx(ctx, "GetTopicMap len(topicIds): %d, len(topicMap): %d", len(topicIds), len(topicMap))
	return topicMap, nil
}

// GetConfigTabInfoWithCache 根据configTabId获取对应配置
func GetConfigTabInfoWithCache(ctx context.Context, configTabId string) (*pb.ConfigTabInfo, error) {
	configTabInfo, ok := ConfigTabInfoCache.ConfigTabInfoMapCache[configTabId]
	if !ok {
		log.InfoWithCtx(ctx, "GetConfigTabInfoWithCache cache miss, configTabId:%s", configTabId)
		return nil, nil
	}
	return configTabInfo, nil
}

// GetGameConfigTabDetailWithCache 根据tabSubType和tabId获取对应类型玩法的configTab配置
func GetGameConfigTabDetailWithCache(ctx context.Context, tabId, tabSubType uint32) (*pb.GameConfigTabDetail, error) {
	key := genGameConfigTabKey(tabSubType, tabId)
	gameConfigTabDetail, ok := ConfigTabInfoCache.GameConfigTabDetailMapCache[key]
	if !ok {
		log.InfoWithCtx(ctx, "GetGameConfigTabDetailWithCache cache miss, tabSubType:%d, tabId:%d", tabSubType, tabId)
		return nil, nil
	}
	return gameConfigTabDetail, nil
}

func BatGetGameConfigTabDetailWithCache(ctx context.Context, tabIds []uint32, tabSubType uint32) ([]*pb.GameConfigTabDetail, error) {
	res := make([]*pb.GameConfigTabDetail, 0, len(tabIds))
	for _, tabId := range tabIds {
		key := genGameConfigTabKey(tabSubType, tabId)
		gameConfigTabDetail, ok := ConfigTabInfoCache.GameConfigTabDetailMapCache[key]
		if !ok {
			continue
		}
		res = append(res, gameConfigTabDetail)
	}
	return res, nil
}

func GetGamePalTabsMapCache() map[uint32]*pb.ConfigTabInfo {
	return ConfigTabInfoCache.GamePalTabsMapCache
}

func GetDefaultTopicIdByConfigTabId(configTabId string) string {
	if configTabInfo, ok := ConfigTabInfoCache.ConfigTabInfoMapCache[configTabId]; ok {
		for _, topic := range configTabInfo.GetBindTopics() {
			if topic.GetIsDefault() {
				return topic.GetTopicId()
			}
		}
	}
	return ""

}

func GetVisibleConfigTabByTypeAndTabIdCache(configTabType, tabId uint32) *pb.ConfigTabInfo {
	if ConfigTabInfoCache.visibleConfigTabMapCache == nil {
		return nil
	}
	key := genGameConfigTabKey(configTabType, tabId)
	return ConfigTabInfoCache.visibleConfigTabMapCache[key]
}

func GetVisibleConfigTabsMapCache(configTabType pb.ConfigTabType) map[uint32]*pb.ConfigTabInfo {
	configTabsMap := make(map[uint32]*pb.ConfigTabInfo)
	if ConfigTabInfoCache.visibleConfigTabMapCache == nil {
		return configTabsMap
	}
	for key, configTab := range ConfigTabInfoCache.visibleConfigTabMapCache {
		if configTab.GetConfigTabType() == configTabType {
			_, tabId, err := decodeGameConfigTabKey(key)
			if err != nil {
				continue
			}
			configTabsMap[tabId] = configTab
		}
	}
	return configTabsMap
}
