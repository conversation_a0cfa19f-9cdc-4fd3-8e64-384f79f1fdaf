package manager

import (
	"context"
	"errors"
	"fmt"
	"golang.52tt.com/pkg/abtest"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/game-ugc-content"
	topicPb "golang.52tt.com/protocol/services/ugc/topic"
	"golang.52tt.com/services/game-ugc-content/internal/cache"
	"golang.52tt.com/services/game-ugc-content/internal/client"
	"golang.52tt.com/services/game-ugc-content/internal/conf"
	config "golang.52tt.com/services/game-ugc-content/internal/conf/ttconfig/game_ugc_content"
	"golang.52tt.com/services/game-ugc-content/internal/model"
	"gopkg.in/mgo.v2/bson"
	"sort"
	"strconv"
	"strings"
	"time"
	
	"golang.52tt.com/services/game-ugc-content/internal/dao"
)

var (
	ErrExistSameTopic = "同名话题已存在, 创建失败"
	
	// 组队大厅实验
	GameHallABTestControl = "invisible_game_hall" // 对照组：不可见组队大厅
)

type Manager struct {
	MongoDao  dao.IMongoDao
	sc        *conf.ServiceConfigT
	ABTestCli abtest.IABTestClient
}

func NewManager(ctx context.Context, sc *conf.ServiceConfigT, mongoDao *dao.MongoDao, abtestCli *abtest.ABTestClient) (*Manager, error) {
	
	err := mongoDao.CreateIndexes(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "mongoDao.CreateIndexes fail, err: %s", err.Error())
	}
	
	return &Manager{
		MongoDao:  mongoDao,
		sc:        sc,
		ABTestCli: abtestCli,
	}, nil
}

func genGameConfigTabKey(tabSubtype, tabId uint32) string {
	return fmt.Sprintf("%d-%d", tabSubtype, tabId)
}

func decodeGameConfigTabKey(key string) (uint32, uint32, error) {
	items := strings.Split(key, "-")
	if len(items) != 2 {
		return 0, 0, errors.New("invalid GameConfigTabKey")
	}
	tabSubtype, err := strconv.Atoi(items[0])
	if err != nil {
		return 0, 0, err
	}
	tabId, err := strconv.Atoi(items[1])
	if err != nil {
		return 0, 0, err
	}
	
	return uint32(tabSubtype), uint32(tabId), nil
}

// UpsertTopic 创建/删除游戏专区话题，运营后台用
func (m *Manager) UpsertTopic(ctx context.Context, topicId, topicName, displayName, displayDistrictName string, isDisplay bool) error {
	// 创建话题
	if topicId == "" {
		// 是否存在同名话题
		if m.MongoDao.IsTopicNameExist(ctx, topicId, topicName) {
			log.ErrorWithCtx(ctx, "UpsertTopic topic name already exist")
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, ErrExistSameTopic)
		}
		
		// 创建话题
		topicId = bson.NewObjectId().Hex()
		resp, rpcErr := client.TopicClient.CreateTopic(ctx, &topicPb.CreateTopicReq{
			Name:      topicId,
			TopicType: topicPb.TopicType_TypeGameDistrict,
			Enable:    true,
		})
		if rpcErr != nil {
			log.ErrorWithCtx(ctx, "UpsertTopic TopicClient.CreateTopic err: %v", rpcErr)
			return rpcErr
		}
		if !resp.GetIsNew() {
			log.ErrorWithCtx(ctx, "UpsertTopic TopicClient.CreateTopic topic already exist")
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, ErrExistSameTopic)
		}
		
		topicInfo := &dao.TopicInfo{
			TopicId:             resp.GetTopicId(),
			TopicName:           topicName,
			CreateTs:            time.Now().Unix(),
			DisplayName:         displayName,
			IsDisplay:           isDisplay,
			DisplayDistrictName: displayDistrictName,
		}
		err := m.MongoDao.InsertTopic(ctx, topicInfo)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpsertTopic MongoDao.InsertTopic err: %v", err)
			return err
		}
	} else { // 修改话题名称
		if m.MongoDao.IsTopicNameExist(ctx, topicId, topicName) {
			log.ErrorWithCtx(ctx, "UpsertTopic TopicClient.CreateTopic topic already exist")
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, ErrExistSameTopic)
		}
		
		err := m.MongoDao.UpdateTopicName(ctx, topicId, topicName, displayName, displayDistrictName, isDisplay)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpsertTopic MongoDao.UpdateTopicName err: %v", err)
			return err
		}
	}
	return nil
}

// GetTopicList 获取游戏专区话题列表，运营后台用
func (m *Manager) GetTopicList(ctx context.Context, in *pb.GetTopicListReq) ([]*pb.Topic, uint32, error) {
	filter := dao.TopicFilter{
		Page:                int64(in.GetPage()),
		Size:                int64(in.GetSize()),
		NeedCount:           in.GetNeedCount(),
		TopicId:             in.GetTopicId(),
		TopicName:           in.GetTopicName(),
		DisplayTopicName:    in.GetDisplayTopicName(),
		DisplayDistrictName: in.GetDisplayDistrictName(),
	}
	topics, total, err := m.MongoDao.GetTopicList(ctx, filter)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTopicList MongoDao.GetTopicList err: %v", err)
		return nil, 0, err
	}
	
	topicIds := make([]string, 0, len(topics))
	for _, topic := range topics {
		topicIds = append(topicIds, topic.TopicId)
	}
	
	resp, rpcErr := client.TopicClient.GetTopics(ctx, &topicPb.GetTopicsReq{
		TopicIds: topicIds,
		Option:   topicPb.TopicQueryOptionEx_ExQueryAll,
	})
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "GetTopicList TopicClient.GetTopics err: %v", rpcErr)
		return nil, 0, rpcErr
	}
	realTopics := resp.GetTopicInfos()
	
	postCountMap := make(map[string]uint32, len(realTopics))
	for _, topic := range realTopics {
		postCountMap[topic.GetTopicId()] = topic.GetPostCount()
	}
	
	pbTopics := make([]*pb.Topic, 0, len(topics))
	for _, topic := range topics {
		pbTopics = append(pbTopics, &pb.Topic{
			TopicId:             topic.TopicId,
			TopicName:           topic.TopicName,
			PostCount:           postCountMap[topic.TopicId],
			CreateAt:            topic.CreateTs,
			DisplayTopicName:    topic.DisplayName,
			IsDisplay:           topic.IsDisplay,
			DisplayDistrictName: topic.DisplayDistrictName,
		})
	}
	//log.InfoWithCtx(ctx, "GetTopicList rpcTopics: %+v, pbTopics: %+v", realTopics, pbTopics)
	
	return pbTopics, total, nil
}

// DelTopic 删除游戏专区话题，运营后台用
func (m *Manager) DelTopic(ctx context.Context, topicId string) error {
	filter := dao.ConfigTabFilter{
		Page:    1,
		Size:    1,
		TopicId: topicId,
	}
	configTabs, _, err := m.MongoDao.GetConfigTabList(ctx, filter)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelTopic MongoDao.GetConfigTabsByTopicId err: %v", err)
		return err
	}
	if len(configTabs) != 0 {
		log.ErrorWithCtx(ctx, "DelTopic failed, topic is bound by some configTabs")
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "话题已被动态Tab绑定, 取消绑定后才可以删除")
	}
	_, rpcErr := client.TopicClient.DeleteTopic(ctx, &topicPb.DeleteTopicReq{
		TopicId: topicId,
	})
	if rpcErr != nil {
		log.ErrorWithCtx(ctx, "DelTopic TopicClient.DeleteTopic failed, err: %v", rpcErr)
		return rpcErr
	}
	err = m.MongoDao.DelTopic(ctx, topicId)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelTopic MongoDao.DelTopic err: %v", err)
		return err
	}
	return nil
}

func (m *Manager) GetTopicByIds(ctx context.Context, topicIds []string) ([]*pb.Topic, error) {
	pbTopics := make([]*pb.Topic, 0, len(topicIds))
	topicList, err := m.MongoDao.BatGetTopicByIds(ctx, topicIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTopicByIds MongoDao.BatGetTopicByIds topicIds:%v err: %v", topicIds, err)
		return pbTopics, err
	}
	resp, err := client.TopicClient.GetTopics(ctx, &topicPb.GetTopicsReq{
		TopicIds: topicIds,
		Option:   topicPb.TopicQueryOptionEx_ExQueryAll,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTopicByIds TopicClient.GetTopics topicIds:%v err: %v", topicIds, err)
		return pbTopics, err
	}
	realTopics := resp.GetTopicInfos()
	
	postCountMap := make(map[string]uint32, len(realTopics))
	for _, topic := range realTopics {
		postCountMap[topic.GetTopicId()] = topic.GetPostCount()
	}
	
	for _, topic := range topicList {
		pbTopics = append(pbTopics, &pb.Topic{
			TopicId:             topic.TopicId,
			TopicName:           topic.TopicName,
			PostCount:           postCountMap[topic.TopicId],
			CreateAt:            topic.CreateTs,
			DisplayTopicName:    topic.DisplayName,
			IsDisplay:           topic.IsDisplay,
			DisplayDistrictName: topic.DisplayDistrictName,
		})
	}
	return pbTopics, nil
}

// UpsertConfigTab 创建/更新玩法的二级tab，运营后台用
func (m *Manager) UpsertConfigTab(ctx context.Context, configTab *dao.ConfigTab, source pb.RequestSource) error {
	switch source {
	case pb.RequestSource_RequestSource_ConfigTab:
		if configTab.ConfigTabId == "" {
			configTab.ConfigTabId = bson.NewObjectId().Hex()
		}
		err := m.MongoDao.UpsertConfigTab(ctx, configTab)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpsertConfigTab MongoDao.UpsertConfigTab err: %v, source: %v", err, source)
			return err
		}
	case pb.RequestSource_RequestSource_Title:
		err := m.MongoDao.UpdateConfigTabTitles(ctx, configTab.ConfigTabId, configTab.Titles)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpsertConfigTab MongoDao.UpsertConfigTab err: %v, source: %v", err, source)
			return err
		}
	default:
		log.WarnWithCtx(ctx, "UpsertConfigTab invalid request source:%v", source)
	}
	return nil
}

// UpsertComprehensiveTab 创建/更新综合频道下的tab，运营后台用
func (m *Manager) UpsertComprehensiveTab(ctx context.Context, entry *pb.ConfigTabEntry) error {
	newConfigTab := model.ConvertPb2ConfigTabEntry(entry)
	if newConfigTab.ConfigTabId == "" {
		// 最多只能创建一个综合频道 搭子卡tab
		if newConfigTab.ConfigTabType == dao.ConfigTabTypeGamePalCard {
			// 如果已存在搭子卡，则创建失败
			exist := m.MongoDao.IsGamePalCardTabExist(ctx, newConfigTab.TabId, newConfigTab.TabSubType)
			if exist {
				//log.DebugWithCtx(ctx, "UpsertComprehensiveTab gamePalCard exist, tabId:%d, tabSubType:%d", newConfigTab.TabId, newConfigTab.TabSubType)
				return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "已存在综合频道的搭子卡，无法重复创建")
			}
		}
		
		newConfigTab.ConfigTabId = bson.NewObjectId().Hex()
		//log.DebugWithCtx(ctx, "UpsertComprehensiveTab Insert configTab:%+v", newConfigTab)
		err := m.MongoDao.UpsertConfigTab(ctx, newConfigTab)
		if err != nil {
			return err
		}
		
		// 获取当前绑定的configTab排序
		configTabs, err := m.MongoDao.GetTabBindConfigTabsInOrder(ctx, newConfigTab.TabId, newConfigTab.TabSubType)
		if err != nil {
			return err
		}
		
		configTabIds := make([]string, 0, len(configTabs)+1)
		for _, configTab := range configTabs {
			configTabIds = append(configTabIds, configTab.ConfigTabId)
		}
		configTabIds = append(configTabIds, newConfigTab.ConfigTabId)
		// 保存新的绑定关系
		return m.SaveGameConfigTab(ctx, configTabIds, newConfigTab.TabId, newConfigTab.TabSubType)
	} else {
		//log.DebugWithCtx(ctx, "UpsertComprehensiveTab Update configTab:%+v", newConfigTab)
		return m.MongoDao.UpsertConfigTab(ctx, newConfigTab)
	}
}

// DelConfigTab 删除ConfigTab，运营后台用
func (m *Manager) DelConfigTab(ctx context.Context, configTabId string) error {
	err := m.MongoDao.DelConfigTab(ctx, configTabId)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelConfigTab MongoDao.DelConfigTab err: %v", err)
		return err
	}
	return nil
}

// GetConfigTabs 获取ConfigTab列表，运营后台用
func (m *Manager) GetConfigTabs(ctx context.Context, in *pb.GetConfigTabsReq) (configTabs []*dao.ConfigTab, total uint32, err error) {
	filter := dao.ConfigTabFilter{
		Page:          int64(in.GetPage()),
		Size:          int64(in.GetSize()),
		NeedCount:     in.GetNeedCount(),
		ConfigTabId:   in.GetConfigTabId(),
		ConfigTabName: in.GetConfigTabName(),
		TopicId:       in.GetTopicId(),
		TabId:         in.GetTabId(),
		TeleportUrl:   in.GetTeleportUrl(),
	}
	// 是否只展示有配置标题的配置
	if in.GetSource() == pb.RequestSource_RequestSource_Title {
		filter.OnlyHasTitles = true
	}
	configTabs = make([]*dao.ConfigTab, 0)
	switch in.GetSource() {
	case pb.RequestSource_RequestSource_ConfigTab, pb.RequestSource_RequestSource_Title:
		configTabs, total, err = m.MongoDao.GetConfigTabList(ctx, filter)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetConfigTabs MongoDao.GetConfigTabList err: %v", err)
			return nil, 0, err
		}
		/*	case pb.RequestSource_RequestSource_ActivityTab: // 综合频道配置页面展示活动tab类型数据
			configTabTypeList := []uint32{dao.ConfigTabTypeActivityPost, dao.ConfigTabTypeActivitySet, dao.ConfigTabTypeGamePalCard}
			tabSubType := uint32(pb.TabSubType_COMPREHENSIVE_CHANNEL)
			configTabs, err = m.MongoDao.GetConfigTabsByConfigTabTypeAndTabId(ctx, in.GetTabId(), tabSubType, configTabTypeList)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetConfigTabs MongoDao.GetConfigTabsByConfigTabTypeAndTabId err: %v", err)
				return nil, 0, err
			}*/
	}
	return configTabs, total, nil
}

// getConfigTabTopicInfo 获取动态tab话题数据
/*func (m *Manager) GetConfigTabTopicInfo(ctx context.Context, defaultTopicId string, otherTopicIds []string) (*dao.TopicInfo, []*dao.TopicInfo, error) {
	topics, err := m.MongoDao.BatGetTopicByIds(ctx, append(otherTopicIds, defaultTopicId))
	if err != nil {
		log.ErrorWithCtx(ctx, "getConfigTabTopicInfo MongoDao.BatGetTopicByIds err: %v", err)
		return nil, nil, err
	}
	var defaultTopic *dao.TopicInfo
	otherTopics := make([]*dao.TopicInfo, 0, len(topics))

	for _, topic := range topics {
		if topic.TopicId == defaultTopicId {
			defaultTopic = topic
		} else {
			otherTopics = append(otherTopics, topic)
		}
	}
	return defaultTopic, otherTopics, nil
}*/

// GetTopicMap 获取话题数据
func (m *Manager) GetTopicMap(ctx context.Context, topicIds []string) (map[string]*dao.TopicInfo, error) {
	topicMap := make(map[string]*dao.TopicInfo)
	topicList, err := m.MongoDao.BatGetTopicByIds(ctx, topicIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "getConfigTabTopicInfo MongoDao.BatGetTopicByIds err: %v", err)
		return topicMap, err
	}
	for _, topic := range topicList {
		topicMap[topic.TopicId] = topic
	}
	log.InfoWithCtx(ctx, "GetTopicMap len(topicIds): %d, len(topicMap): %d", len(topicIds), len(topicMap))
	return topicMap, nil
}

// SaveGameConfigTab 绑定玩法与configTab，需要加上tabSubType区分普通玩法和综合频道
func (m *Manager) SaveGameConfigTab(ctx context.Context, configTabIds []string, tabId, tabSubType uint32) error {
	// 将原有的绑定关系解除
	err := m.MongoDao.BatUnBindConfigTabGame(ctx, tabId, tabSubType)
	if err != nil {
		log.ErrorWithCtx(ctx, "SaveGameConfigTab MongoDao.BatUnBindConfigTabGame err: %v", err)
		return err
	}
	// 批量更新动态tab排序和绑定的玩法信息
	err = m.MongoDao.BatUpdateConfigTabBindGameAndSort(ctx, configTabIds, tabId, tabSubType)
	if err != nil {
		log.ErrorWithCtx(ctx, "SortGameConfigTab MongoDao.BatUpdateConfigTab err: %v", err)
		return err
	}
	//log.DebugWithCtx(ctx, "SortGameConfigTab len(configTabIds):%d, tabId:%d, tabSubType:%d", len(configTabIds), tabId, tabSubType)
	
	return nil
}

// 用于后续迁移动态数据时用
func (m *Manager) getPostInfo(configTab *dao.ConfigTab, topicMap map[string]*dao.TopicInfo, allGameConfigTab *pb.AllGameConfigTab) *pb.AllGameConfigTab {
	for _, bindTopic := range configTab.BindTopics {
		if bindTopic.IsDistrictDisplay {
			allGameConfigTab.BindTopics = append(allGameConfigTab.BindTopics, &pb.BindTopic{
				TopicId:           bindTopic.TopicId,
				IsDistrictDisplay: bindTopic.IsDistrictDisplay,
				IsDefault:         bindTopic.IsDefault,
			})
		}
	}
	for _, bindTopic := range configTab.BindTopics {
		if bindTopic.IsDistrictDisplay {
			if tmpTopic, ok := topicMap[bindTopic.TopicId]; ok {
				pbTopic := model.ConvertTopic2Pb(tmpTopic)
				allGameConfigTab.Topics = append(allGameConfigTab.Topics, pbTopic)
			}
		}
	}
	
	return allGameConfigTab
}

// GetGameConfigTabList 获取玩法绑定动态tab和房间流tab列表，运营后台用
func (m *Manager) GetGameConfigTabList(ctx context.Context, tabId, tabSubType uint32) ([]*pb.GameConfigTab, error) {
	// tabSubType必填，tabId为额外筛选条件
	configTabs, err := m.MongoDao.GetConfigTabsByTabSubType(ctx, tabSubType, tabId)
	
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameConfigTabList MongoDao.GetConfigTabsByTabSubType err: %v", err)
		return nil, err
	}
	// 按tabId和tabSubType对configTab进行聚合
	configTabMap := make(map[string][]*pb.AllGameConfigTab)
	topicMap := make(map[string]*dao.TopicInfo)
	var topicIds []string
	for _, configTab := range configTabs {
		if configTab.TabId == 0 {
			continue
		}
		if len(configTab.BindTopics) != 0 {
			for _, bindTopic := range configTab.BindTopics {
				if bindTopic.IsDistrictDisplay {
					topicIds = append(topicIds, bindTopic.TopicId)
				}
			}
		} else {
			//默认不展示
			//topicIds = append(topicIds, configTab.DefaultTopicId)
			//topicIds = append(topicIds, configTab.OtherTopicIds...)
		}
	}
	if len(topicIds) != 0 {
		topics, err := m.MongoDao.BatGetTopicByIds(ctx, topicIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGameConfigTabList MongoDao.BatGetTopicByIds err: %v", err)
			return nil, err
		}
		for _, topic := range topics {
			topicMap[topic.TopicId] = topic
		}
	}
	gameConfigTabs := make([]*pb.GameConfigTab, 0, len(configTabMap))
	for _, configTab := range configTabs {
		if configTab.TabId == 0 {
			continue
		}
		key := genGameConfigTabKey(configTab.TabSubType, configTab.TabId)
		allGameConfigTab := &pb.AllGameConfigTab{
			ConfigTabEntry: model.ConvertConfigTabEntry2Pb(configTab),
		}
		allGameConfigTab = m.getPostInfo(configTab, topicMap, allGameConfigTab)
		
		configTabMap[key] = append(configTabMap[key], allGameConfigTab)
	}
	
	for key, allConfigTabs := range configTabMap {
		tabSubType, tabId, err := decodeGameConfigTabKey(key)
		if err != nil {
			log.WarnWithCtx(ctx, "GetGameConfigTabList invalid gameConfigTabKey:%s", key)
			continue
		}
		gameConfigTabs = append(gameConfigTabs, &pb.GameConfigTab{
			TabId:             tabId,
			AllGameConfigTabs: allConfigTabs,
			TabSubType:        pb.TabSubType(tabSubType),
		})
	}
	sort.Slice(gameConfigTabs, func(i, j int) bool {
		if gameConfigTabs[i].TabSubType != gameConfigTabs[j].TabSubType {
			return gameConfigTabs[i].TabSubType < gameConfigTabs[j].TabSubType
		}
		return gameConfigTabs[i].TabId < gameConfigTabs[j].TabId
	})
	return gameConfigTabs, nil
}

// DelGameConfigTab 解除玩法与动态tab的绑定关系，topic-channel-tab服务在删除玩法时调用
func (m *Manager) DelGameConfigTab(ctx context.Context, tabId, tabSubType uint32) error {
	// 删除玩法的默认tab, 失败不影响后续操作
	err := m.DelDefaultConfigTabs(ctx, tabId, tabSubType)
	if err != nil {
		log.WarnWithCtx(ctx, "DelGameConfigTab MongoDao.DelDefaultConfigsTabByTabId err: %v", err)
	}
	// 解除玩法相关的动态tab绑定关系
	err = m.MongoDao.BatUnBindConfigTabGame(ctx, tabId, tabSubType)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelGameConfigTab MongoDao.BatUnBindConfigTabGame err: %v", err)
		return err
	}
	return nil
}

// BatchUpdateConfigTabsBaseInfo 更新玩法的默认tab基本信息：名称、是否隐藏，仅运营后台使用
func (m *Manager) BatchUpdateConfigTabsBaseInfo(ctx context.Context, updateConfigTabEntry []*pb.ConfigTabEntry) error {
	configTabs := make([]*dao.ConfigTab, 0, len(updateConfigTabEntry))
	for _, configTab := range updateConfigTabEntry {
		if configTab.GetConfigTabId() == "" {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "config_tab_id不能为空")
		}
		configTabs = append(configTabs, model.ConvertPb2ConfigTabEntry(configTab))
	}
	err := m.MongoDao.BatUpdateConfigTabsBaseInfo(ctx, configTabs)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchUpdateConfigTabsBaseInfo MongoDao.BatUpdateConfigTabsBaseInfo err: %v", err)
		return err
	}
	//log.DebugWithCtx(ctx, "BatUpdateConfigTabsBaseInfo len(updateConfigTabEntry):%d, len(configTabs):%d", len(updateConfigTabEntry), len(configTabs))
	return nil
}

// InsertDefaultConfigTabs 为玩法创建对应的游戏专区二级tab：开黑房、搭子卡、群聊，topic-channel-tab服务在新增玩法时调用
func (m *Manager) InsertDefaultConfigTabs(ctx context.Context, tabId uint32) error {
	// 默认搭子卡tab
	gamePalCardTab := &dao.ConfigTab{
		ConfigTabId:   bson.NewObjectId().Hex(),
		ConfigTabName: config.GetGameUgcContentConfig().GetDefaultGamePalTabName(),
		TabId:         tabId,
		ConfigTabType: dao.ConfigTabTypeGamePalCard,
		TabSubType:    uint32(pb.TabSubType_GAME),
		Sort:          1,
	}
	// 默认组队大厅tab
	gameHallTab := &dao.ConfigTab{
		ConfigTabId:   bson.NewObjectId().Hex(),
		ConfigTabName: config.GetGameUgcContentConfig().GetDefaultGameHallTabName(),
		TabId:         tabId,
		ConfigTabType: dao.ConfigTabTypeGameHall,
		TabSubType:    uint32(pb.TabSubType_GAME),
		Sort:          2,
	}
	// 默认房间流tab
	channelListTab := &dao.ConfigTab{
		ConfigTabId:   bson.NewObjectId().Hex(),
		ConfigTabName: config.GetGameUgcContentConfig().GetDefaultChannelTabName(),
		TabId:         tabId,
		ConfigTabType: dao.ConfigTabTypeChannelList,
		TabSubType:    uint32(pb.TabSubType_GAME),
		Sort:          3,
	}
	// 默认群聊tab
	groupChatTab := &dao.ConfigTab{
		ConfigTabId:   bson.NewObjectId().Hex(),
		ConfigTabName: config.GetGameUgcContentConfig().GetDefaultGroupChatTabName(),
		TabId:         tabId,
		ConfigTabType: dao.ConfigTabTypeGroupChat,
		TabSubType:    uint32(pb.TabSubType_GAME),
		Sort:          4,
	}
	defaultConfigTabs := []*dao.ConfigTab{gamePalCardTab, gameHallTab, channelListTab, groupChatTab}
	err := m.MongoDao.BatInsertConfigTabs(ctx, defaultConfigTabs)
	if err != nil {
		log.ErrorWithCtx(ctx, "InsertDefaultConfigTabs MongoDao.BatInsertConfigTabs err: %v", err)
		return err
	}
	
	return nil
}

// DelDefaultConfigTabs 删除玩法默认创建的二级tab
func (m *Manager) DelDefaultConfigTabs(ctx context.Context, tabId, tabSubType uint32) error {
	// 默认二级tab：开黑房、搭子卡、群聊、组队大厅
	configTabTypes := []uint32{dao.ConfigTabTypeChannelList, dao.ConfigTabTypeGamePalCard, dao.ConfigTabTypeGroupChat, dao.ConfigTabTypeGameHall}
	err := m.MongoDao.DelDefaultConfigsTabByTabId(ctx, tabId, tabSubType, configTabTypes)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelDefaultConfigTabs MongoDao.DelDefaultConfigsTabByTabId err: %v", err)
		return err
	}
	return nil
}

func (m *Manager) GetGameOnlyDistrictPostList(ctx context.Context, uid, limit uint32, createTime int64) (postInfoResp *pb.GetGameOnlyDistractPostListResp, err error) {
	postInfoResp, err = m.MongoDao.GetGameOnlyDistrictPostList(ctx, uid, limit, createTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameOnlyDistrictPostList err: %v", err)
		return postInfoResp, err
	}
	return postInfoResp, err
}

func (m *Manager) AddGameOnlyDistrictPost(ctx context.Context, postId string, uid uint32, createAt, updateAt int64) (err error) {
	onlyDistrictPost := &dao.GameOnlyDistrictPost{
		Id:         postId,
		Uid:        uid,
		CreateTime: createAt,
		UpdateTime: updateAt,
	}
	err = m.MongoDao.AddGameOnlyDistrictPost(ctx, onlyDistrictPost)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddGameOnlyDistrictPost err: %v", err)
		return err
	}
	return nil
}

func (m *Manager) DelGameOnlyDistrictPost(ctx context.Context, postId string) (err error) {
	err = m.MongoDao.DelGameOnlyDistrictPost(ctx, postId)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelGameOnlyDistrictPost err: %v", err)
		return err
	}
	return nil
}

// GetConfigTabsByType 根据类型查所有的ConfigTab
func (m *Manager) GetConfigTabsByType(ctx context.Context, configTabType pb.ConfigTabType, tabId uint32, deviceId string) *pb.ConfigTabInfo {
	// 组队大厅tab，由AB实验决定是否返回
	if configTabType == pb.ConfigTabType_ConfigTabType_GameHall {
		configTab := cache.GetVisibleConfigTabByTypeAndTabIdCache(uint32(configTabType), tabId)
		if configTab != nil && m.checkShowGameHallEntranceABTest(ctx, deviceId) {
			return configTab
		} else {
			return nil
		}
	}
	// 其他类型返回缓存数据
	return cache.GetVisibleConfigTabByTypeAndTabIdCache(uint32(configTabType), tabId)
}

// checkShowGameHallEntranceABTest 判断用户是否在组队大厅AB实验组
func (m *Manager) checkShowGameHallEntranceABTest(ctx context.Context, deviceId string) bool {
	newCtx, cancel := context.WithTimeout(ctx, 200*time.Millisecond)
	defer cancel()
	
	abTestCfg := m.sc.GameHallEntranceAbtestCfg
	testList, err := m.ABTestCli.GetUserTestListInLayerTagByDeviceId(ctx, deviceId, abTestCfg.LayerTag)
	// 接口报错默认为对照组
	if err != nil {
		log.ErrorWithCtx(newCtx, "checkShowGameHallEntranceABTest GetUserTestListInLayerTag deviceId(%s) err: %v",
			deviceId, err)
		return false
	}
	
	//log.DebugWithCtx(ctx, "checkShowGameHallEntranceABTest GetUserTestListInLayerTag deviceId(%s) testList: %+v", deviceId, testList)
	for _, test := range testList {
		for _, expt := range test.ExptList {
			if result, ok := expt.ExptArgv[abTestCfg.ArgvName]; ok {
				return result != GameHallABTestControl
			}
		}
	}
	return false
}

func (m *Manager) UpsertConfigTabEntry(ctx context.Context, entry *pb.ConfigTabEntry) error {
	configTab := model.ConvertPb2ConfigTabEntry(entry)
	if configTab.ConfigTabId == "" {
		configTab.ConfigTabId = bson.NewObjectId().Hex()
	}
	err := m.MongoDao.UpsertConfigTab(ctx, configTab)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpsertConfigTabEntry MongoDao.UpsertConfigTab err: %v", err)
		return err
	}
	return nil
}
