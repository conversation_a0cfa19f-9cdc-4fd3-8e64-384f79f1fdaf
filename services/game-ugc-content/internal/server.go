package internal

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/device_id"
	"golang.52tt.com/pkg/abtest"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/demo/echo"
	pb "golang.52tt.com/protocol/services/game-ugc-content"
	"golang.52tt.com/services/game-ugc-content/internal/ban_post_config_mgr"
	"golang.52tt.com/services/game-ugc-content/internal/cache"
	"golang.52tt.com/services/game-ugc-content/internal/client"
	"golang.52tt.com/services/game-ugc-content/internal/conf"
	config "golang.52tt.com/services/game-ugc-content/internal/conf/ttconfig/game_ugc_content"
	"golang.52tt.com/services/game-ugc-content/internal/dao"
	"golang.52tt.com/services/game-ugc-content/internal/event"
	"golang.52tt.com/services/game-ugc-content/internal/feeds_mgr"
	"golang.52tt.com/services/game-ugc-content/internal/manager"
	"golang.52tt.com/services/game-ugc-content/internal/model"
)

const (
	InvalidZeroId       = 0 //id为0
	getUserInfoErrorMsg = "获取用户信息失败"
	InvalidEmptyStr     = ""
	abtestUrl           = "http://abtestlogic.commonservice.svc.cluster.local:8000/AbtestLogicService/GetUsersAbtestByTag"
)

func NewServer(ctx context.Context, cfg *conf.ServiceConfigT) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	mongoDao, err := dao.NewMongoDao(ctx, cfg.GetMongoConfig())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create mongodb, err: %s", err.Error())
		return nil, err
	}
	err = config.InitGameUgcContentConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "InitGameUgcContentConfig err: %v", err)
		return nil, err
	}
	abtestCli := abtest.NewABTestClient(abtestUrl, uint32(abtest.APPID_TTyuyin), "")

	mgr, err := manager.NewManager(ctx, cfg, mongoDao, abtestCli)
	if err != nil {
		return nil, err
	}

	banPostConfigMgr := ban_post_config_mgr.NewBanPostConfigMgr(mongoDao, cfg.GetRefreshBanPostConfigCacheFre())
	go banPostConfigMgr.SyncBanPostConfigCache(ctx)
	_ = banPostConfigMgr.RefreshBanPostConfigCache(ctx)
	feedCommonHandler := feeds_mgr.NewFeedCommonHandle(mongoDao)
	feedMgr := feeds_mgr.NewFeedMgr(feedCommonHandler)
	s := &Server{
		sc:               cfg,
		manager:          mgr,
		feedMgr:          feedMgr,
		banPostConfigMgr: banPostConfigMgr,
	}
	client.Init(cfg.GetCrowdGroupConf())
	err = cache.NewCache(ctx, mongoDao)
	if err != nil {
		log.ErrorWithCtx(ctx, "new cache error %v", err)
		return s, err
	}

	s.contentSub, err = event.NewContentSub(ctx, cfg, mgr)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewContentSub err: %v", err)
		return s, err
	}
	log.DebugWithCtx(ctx, "NewServer success")

	return s, nil
}

type Server struct {
	sc               *conf.ServiceConfigT
	manager          *manager.Manager
	feedMgr          feeds_mgr.IFeedMgr
	banPostConfigMgr *ban_post_config_mgr.BanPostConfigMgr
	contentSub       *event.ContentSub
}

func (s *Server) ShutDown() {
	s.contentSub.Sub.Stop()
	log.InfoWithCtx(context.Background(), "game-ugc-content server shut down")
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) UpsertTopic(ctx context.Context, req *pb.UpsertTopicReq) (out *pb.UpsertTopicResp, err error) {
	out = new(pb.UpsertTopicResp)
	if req.GetTopicName() == InvalidEmptyStr {
		log.InfoWithCtx(ctx, "UpsertTopic topic name cannot be empty")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "话题名称不能为空")
	}
	if req.GetDisplayTopicName() == InvalidEmptyStr {
		log.InfoWithCtx(ctx, "UpsertTopic topic displayName cannot be empty")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "外显话题名称不能为空")
	}
	err = s.manager.UpsertTopic(ctx, req.GetTopicId(), req.GetTopicName(), req.GetDisplayTopicName(), req.GetDisplayDistrictName(), req.GetIsDisplay())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpsertTopic err: %v, in: %+v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "UpsertTopic success, in: %+v", req)
	return out, err
}

func (s *Server) GetTopicList(ctx context.Context, req *pb.GetTopicListReq) (out *pb.GetTopicListResp, err error) {
	out = new(pb.GetTopicListResp)
	topicList, total, err := s.manager.GetTopicList(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetTopicList err: %v, in: %+v", err, req)
		return out, err
	}
	out.TopicList = topicList
	out.Total = total
	log.InfoWithCtx(ctx, "GetTopicList success, in: %+v, total:%d, len(topicList): %d", req, total, len(topicList))
	return out, err
}

func (s *Server) DelTopic(ctx context.Context, req *pb.DelTopicReq) (out *pb.DelTopicResp, err error) {
	out = new(pb.DelTopicResp)
	err = s.manager.DelTopic(ctx, req.GetTopicId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DelTopic err: %v, in: %v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "DelTopic success, in: %+v", req)
	return out, err
}

func (s *Server) BatGetTopics(ctx context.Context, req *pb.BatGetTopicsReq) (*pb.BatGetTopicsResp, error) {
	out := &pb.BatGetTopicsResp{}
	if len(req.GetTopicIds()) == 0 {
		log.WarnWithCtx(ctx, "BatGetTopics empty req")
		return out, nil
	}
	var err error
	out.TopicList, err = s.manager.GetTopicByIds(ctx, req.GetTopicIds())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatGetTopics req:%s GetTopicByIds err:%v", req.String(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "BatGetTopics success req:%s len(out):%d", req.String(), len(out.GetTopicList()))
	return out, nil

}

func (s *Server) UpsertConfigTab(ctx context.Context, req *pb.UpsertConfigTabReq) (out *pb.UpsertConfigTabResp, err error) {
	out = new(pb.UpsertConfigTabResp)
	if err = s.checkUpsertConfigTabParam(req); err != nil {
		log.ErrorWithCtx(ctx, "UpsertConfigTab err: %v, in: %+v", err, req)
		return out, err
	}
	configTab := model.ConvertPb2ConfigTab(req.GetConfig())
	err = s.manager.UpsertConfigTab(ctx, configTab, req.GetSource())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpsertConfigTab err: %v, in: %+v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "UpsertConfigTab success, in: %+v", req)
	return out, err
}

func (s *Server) checkUpsertConfigTabParam(req *pb.UpsertConfigTabReq) error {
	if req.GetConfig() == nil {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "配置不能为空")
	}
	if req.GetSource() == pb.RequestSource_RequestSource_None {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "接口请求来源字段不能为空")
	}
	if req.GetSource() == pb.RequestSource_RequestSource_Title && req.GetConfig().GetConfigTabId() == InvalidEmptyStr {
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "动态Tab的id不能为空")
	}
	if req.GetSource() == pb.RequestSource_RequestSource_ConfigTab {
		configTab := req.GetConfig()
		topicMap := make(map[string]struct{})
		// 检查动态绑定话题配置, 判断是否有默认话题, 话题是否有重复
		for _, bindTopic := range configTab.GetBindTopicInfos() {
			if bindTopic.GetIsDefault() {
				topicMap[bindTopic.GetTopicId()] = struct{}{}
				if bindTopic.GetTopicId() == InvalidEmptyStr {
					return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "动态Tab的默认话题不能为空")
				}
			}
		}

		for _, bindTopic := range configTab.GetBindTopicInfos() {
			if !bindTopic.GetIsDefault() {
				if _, ok := topicMap[bindTopic.GetTopicId()]; ok {
					return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "一个动态Tab不能重复绑定同一话题")
				}
				topicMap[bindTopic.GetTopicId()] = struct{}{}
			}
		}

		// 检查动态小尾巴配置
		postTeleport := configTab.GetPostTeleport()
		if (postTeleport.GetText() == InvalidEmptyStr && postTeleport.GetUrl() != InvalidEmptyStr) ||
			(postTeleport.GetText() != InvalidEmptyStr && postTeleport.GetUrl() == InvalidEmptyStr) {
			return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "小尾巴的标题和链接必须同时配置或者都为空")
		}
	}

	return nil
}

func (s *Server) GetConfigTabs(ctx context.Context, req *pb.GetConfigTabsReq) (out *pb.GetConfigTabsResp, err error) {
	out = new(pb.GetConfigTabsResp)
	if req.GetSource() == pb.RequestSource_RequestSource_None {
		log.ErrorWithCtx(ctx, "GetConfigTabs RequestSource is None, in: %+v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "接口请求来源字段不能为空")
	}
	configTabs, total, err := s.manager.GetConfigTabs(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetConfigTabs err: %v, in: %+v", err, req)
		return out, err
	}

	pbConfigTabs := make([]*pb.AllConfigTab, 0, len(configTabs))
	switch req.GetSource() {
	case pb.RequestSource_RequestSource_ConfigTab:
		// 动态tab配置页面需要另外展示话题数据
		topicIds := make([]string, 0)
		for _, configTab := range configTabs {
			if len(configTab.BindTopics) != 0 {
				for _, bindTopic := range configTab.BindTopics {
					topicIds = append(topicIds, bindTopic.TopicId)
				}
			} else {
				if configTab.DefaultTopicId != "" {
					topicIds = append(topicIds, configTab.DefaultTopicId)
					if len(configTab.OtherTopicIds) != 0 {
						topicIds = append(topicIds, configTab.OtherTopicIds...)
					}
				}
			}
		}
		topicMap, err := s.manager.GetTopicMap(ctx, topicIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetConfigTabs GetTopicMap err: %v", err)
			return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "获取话题数据出错")
		}
		for _, configTab := range configTabs {
			allConfigTab := &pb.AllConfigTab{
				ConfigTab: model.ConvertConfigTab2Pb(configTab),
			}
			for _, bindTopic := range allConfigTab.ConfigTab.GetBindTopicInfos() {
				if topic, ok := topicMap[bindTopic.TopicId]; ok {
					tmpTopic := model.ConvertTopic2Pb(topic)
					allConfigTab.Topics = append(allConfigTab.Topics, tmpTopic)
					//log.DebugWithCtx(ctx, "GetConfigTabs topic, topic:%+v", allConfigTab)
				} else {
					log.WarnWithCtx(ctx, "GetConfigTabs topic not found, topicId:%s", bindTopic.TopicId)
				}
			}
			pbConfigTabs = append(pbConfigTabs, allConfigTab)
		}
		//log.DebugWithCtx(ctx, "test GetTopicMap, topicIds:%+v, topicMap:%+v", topicIds, topicMap)
		// 填充人群包信息
		s.fillConfigTabCrowdGroupInfo(ctx, pbConfigTabs)
	case pb.RequestSource_RequestSource_Title, pb.RequestSource_RequestSource_ActivityTab:
		for _, configTab := range configTabs {
			allConfigTab := &pb.AllConfigTab{
				ConfigTab: model.ConvertConfigTab2Pb(configTab),
			}
			pbConfigTabs = append(pbConfigTabs, allConfigTab)
		}
	}

	out.AllConfigTabs = pbConfigTabs
	out.Total = total
	log.InfoWithCtx(ctx, "GetConfigTabs success, in: %+v, total: %d, len(configTabs): %d", req, total, len(pbConfigTabs))
	return out, err
}

// fillCrowdGroupInfo 补充人群包信息，运营后台展示用
func (s *Server) fillConfigTabCrowdGroupInfo(ctx context.Context, configTabs []*pb.AllConfigTab) {
	crowdGroupIds := make([]string, 0, len(configTabs))
	for _, configTab := range configTabs {
		if configTab.GetConfigTab().GetCrowdType() == pb.CrowdType_CrowdTypeGroup {
			crowdGroupIds = append(crowdGroupIds, configTab.GetConfigTab().GetCrowdGroupId())
		}
	}
	// 查询人群包相关信息
	crowdGroupMap, err := client.CrowdGroupClient.GetCrowdGroupInfoByIds(ctx, crowdGroupIds)
	if err != nil || crowdGroupMap == nil {
		return
	}
	//log.DebugWithCtx(ctx, "fillCrowdGroupInfo crowdGroupMap:%+v", crowdGroupMap)
	for _, configTab := range configTabs {
		if configTab.GetConfigTab().GetCrowdType() == pb.CrowdType_CrowdTypeGroup {
			configTab.GetConfigTab().CrowdGroupName = crowdGroupMap[configTab.GetConfigTab().GetCrowdGroupId()].GroupName
			configTab.GetConfigTab().CrowdGroupUserCount = uint32(crowdGroupMap[configTab.GetConfigTab().GetCrowdGroupId()].EntityCount)
		}
	}
}

func (s *Server) DelConfigTab(ctx context.Context, req *pb.DelConfigTabReq) (out *pb.DelConfigTabResp, err error) {
	out = new(pb.DelConfigTabResp)
	err = s.manager.DelConfigTab(ctx, req.GetId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DelConfigTab err: %v, in: %v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "DelConfigTab success, in: %+v", req)
	return out, err
}

func (s *Server) SaveGameConfigTab(ctx context.Context, req *pb.SaveGameConfigTabReq) (out *pb.SaveGameConfigTabResp, err error) {
	out = new(pb.SaveGameConfigTabResp)
	if req.GetTabId() == InvalidZeroId {
		log.ErrorWithCtx(ctx, "SaveGameConfigTab invalid tab_id = 0, in: +%v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "tab_id不能为0")
	}
	if len(req.GetConfigTabEntrys()) == 0 {
		log.ErrorWithCtx(ctx, "SaveGameConfigTab invalid len(configTabs) = 0, in: +%v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "动态tab配置列表不能为空")
	}

	configTabIds := make([]string, 0, len(req.GetConfigTabEntrys()))
	for _, configTab := range req.GetConfigTabEntrys() {
		configTabId := configTab.GetConfigTabId()
		// tab已被其他玩法绑定，报错
		err := s.checkConfigTabIsBindingGame(ctx, configTab, req.GetTabId(), req.GetTabSubType())
		if err != nil {
			log.ErrorWithCtx(ctx, "SaveGameConfigTab checkConfigTabIsBindingGame err: %v, in: %v", err, req)
			return out, err
		}
		configTabIds = append(configTabIds, configTabId)
	}

	err = s.manager.SaveGameConfigTab(ctx, configTabIds, req.GetTabId(), uint32(req.GetTabSubType()))
	if err != nil {
		log.ErrorWithCtx(ctx, "SaveGameConfigTab err: %v, in: %v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "SaveGameConfigTab success, in: %+v", req)
	return out, err
}

// isConfigTabBindingGame 检查tab是否已被绑定，被绑定需要提示被哪个玩法绑定
func (s *Server) checkConfigTabIsBindingGame(ctx context.Context, configTab *pb.ConfigTabEntry, targetTabId uint32, targetTabSubType pb.TabSubType) error {
	if configTab.GetTabId() == 0 {
		return nil
	}
	if (configTab.GetTabId() == targetTabId && configTab.GetTabSubType() != targetTabSubType) ||
		configTab.GetTabId() != targetTabId {
		msg := ""
		if configTab.GetTabSubType() == pb.TabSubType_COMPREHENSIVE_CHANNEL {
			msg = fmt.Sprintf("保存失败，该Tab(%s)已经被综合频道(tab_id:%d)绑定，需要先将Tab与原有玩法解除绑定", configTab.GetConfigTabId(), configTab.GetTabId())
		} else {
			tabInfo, _ := client.TCTabClient.GetTabById(ctx, configTab.GetTabId())
			msg = fmt.Sprintf("保存失败，该Tab(%s)已经被玩法%s(tab_id:%d)绑定，需要先将Tab与原有玩法解除绑定", configTab.GetConfigTabId(), tabInfo.GetName(), configTab.GetTabId())
		}
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, msg)
	}
	return nil
}

func (s *Server) GetGameConfigTabList(ctx context.Context, req *pb.GetGameConfigTabListReq) (out *pb.GetGameConfigTabListResp, err error) {
	out = new(pb.GetGameConfigTabListResp)
	gameConfigTabList, err := s.manager.GetGameConfigTabList(ctx, req.GetTabId(), uint32(req.GetTabSubType()))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameConfigTabList err: %v, in: %v", err, req)
		return out, err
	}
	out.GameConfigTabList = gameConfigTabList
	log.InfoWithCtx(ctx, "GetGameConfigTabList success, in: %+v, len(gameConfigTabList): %d", req, len(gameConfigTabList))
	return out, err
}

func (s *Server) DelGameConfigTab(ctx context.Context, req *pb.DelGameConfigTabReq) (out *pb.DelGameConfigTabResp, err error) {
	out = new(pb.DelGameConfigTabResp)
	err = s.manager.DelGameConfigTab(ctx, req.GetTabId(), uint32(req.GetTabSubType()))
	if err != nil {
		log.ErrorWithCtx(ctx, "DelGameConfigTab MongoDao.BatUnBindConfigTabGame err: %v, in: %v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "DelGameConfigTab success, in: %+v", req)
	return out, err
}

func (s *Server) GetGameConfigTabDetailByTabId(ctx context.Context, req *pb.GetGameConfigTabDetailByTabIdReq) (out *pb.GetGameConfigTabDetailByTabIdResp, err error) {
	out = new(pb.GetGameConfigTabDetailByTabIdResp)
	if req.GetTabId() == InvalidZeroId {
		log.ErrorWithCtx(ctx, "GetGameConfigTabDetailByTabId invalid tab_id = 0")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "tab_id不能为0")
	}
	gameConfigTabDetail, err := cache.GetGameConfigTabDetailWithCache(ctx, req.GetTabId(), uint32(req.GetTabSubType()))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGameConfigTabDetailByTabId err: %v, in: %v", err, req)
		return out, err
	}
	out.Config = gameConfigTabDetail
	log.InfoWithCtx(ctx, "GetGameConfigTabDetailByTabId success, in: %+v, len(out.configTabs): %d", req, len(out.GetConfig().GetConfigTabs()))
	return out, err
}

func (s *Server) BatGetGameConfigTabDetailByTabId(ctx context.Context, req *pb.BatGetGameConfigTabDetailByTabIdReq) (out *pb.BatGetGameConfigTabDetailByTabIdResp, err error) {
	out = new(pb.BatGetGameConfigTabDetailByTabIdResp)
	if len(req.GetTabIds()) == InvalidZeroId {
		log.ErrorWithCtx(ctx, "BatGetGameConfigTabDetailByTabId invalid tab_id = 0")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	gameConfigTabDetail, err := cache.BatGetGameConfigTabDetailWithCache(ctx, req.GetTabIds(), uint32(pb.TabSubType_GAME))
	if err != nil {
		log.ErrorWithCtx(ctx, "BatGetGameConfigTabDetailWithCache err: %v, in: %v", err, req)
		return out, err
	}
	out.Config = gameConfigTabDetail
	log.InfoWithCtx(ctx, "BatGetGameConfigTabDetailByTabId success, in: %+v, len(out.configTabs): %d", req, len(out.GetConfig()))
	return out, err
}

func (s *Server) GetConfigTabInfoById(ctx context.Context, req *pb.GetConfigTabInfoByIdReq) (out *pb.GetConfigTabInfoByIdResp, err error) {
	out = new(pb.GetConfigTabInfoByIdResp)
	if req.GetConfigTabId() == InvalidEmptyStr {
		log.ErrorWithCtx(ctx, "GetConfigTabInfoById invalid config_tab_id is empty string")
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "config_tab_id不能为空")
	}
	configTabInfo, err := cache.GetConfigTabInfoWithCache(ctx, req.GetConfigTabId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetConfigTabInfoById err: %v, in: %v", err, req)
		return out, err
	}
	out.ConfigTabInfo = configTabInfo
	log.InfoWithCtx(ctx, "GetConfigTabInfoById success, in: %+v, configTabName:%s, configTabType:%v, crowdType:%v, crowdGroupId:%s, displayStrategy:%v", req,
		configTabInfo.GetConfigTabName(), configTabInfo.GetConfigTabType(), configTabInfo.GetCrowdType(), configTabInfo.GetCrowdGroupId(), configTabInfo.GetDisplayStrategy())
	return out, err
}

func (s *Server) GetGamePalTabs(ctx context.Context, req *pb.GetGamePalTabsReq) (out *pb.GetGamePalTabsResp, err error) {
	out = new(pb.GetGamePalTabsResp)
	out.GamePalTabs = cache.GetGamePalTabsMapCache()
	log.InfoWithCtx(ctx, "GetGamePalTabs success, len(GamePalTabs)=%d", len(out.GetGamePalTabs()))
	return out, nil
}

func (s *Server) BatchUpdateConfigTabsBaseInfo(ctx context.Context, req *pb.BatchUpdateConfigTabsBaseInfoReq) (out *pb.BatchUpdateConfigTabsBaseInfoResp, err error) {
	out = new(pb.BatchUpdateConfigTabsBaseInfoResp)
	err = s.manager.BatchUpdateConfigTabsBaseInfo(ctx, req.GetUpdateConfigTabs())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchUpdateConfigTabsBaseInfo err: %v, in: %+v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "BatchUpdateConfigTabsBaseInfo success, len(ConfigTabs)=%d", len(req.GetUpdateConfigTabs()))
	return out, err
}

func (s *Server) InsertDefaultConfigTabs(ctx context.Context, req *pb.InsertDefaultConfigTabsReq) (out *pb.InsertDefaultConfigTabsResp, err error) {
	out = new(pb.InsertDefaultConfigTabsResp)

	err = s.manager.InsertDefaultConfigTabs(ctx, req.GetTabId())
	if err != nil {
		log.ErrorWithCtx(ctx, "InsertDefaultConfigTabs err: %v, tabId: %d", err, req.GetTabId())
		return out, err
	}
	log.InfoWithCtx(ctx, "InsertDefaultConfigTabs success, tabId: %d", req.GetTabId())

	return out, err
}

func (s *Server) BatchGetGameFeedPbs(ctx context.Context, req *pb.BatchGetGameFeedPbsReq) (out *pb.BatchGetGameFeedPbsResp, err error) {
	out = new(pb.BatchGetGameFeedPbsResp)
	if len(req.GetPostId()) == InvalidZeroId {
		return out, err
	}
	pbContents, err := s.feedMgr.BatchGetGameFeedPbs(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetGameFeedPbs req:%s err%+v", req.String(), err)
		return out, err
	}
	out.GameFeedContent = pbContents
	return out, err
}

func (s *Server) GetGameOnlyDistractPostList(ctx context.Context, in *pb.GetGameOnlyDistractPostListReq) (out *pb.GetGameOnlyDistractPostListResp, err error) {
	out = &pb.GetGameOnlyDistractPostListResp{}
	out, err = s.manager.GetGameOnlyDistrictPostList(ctx, in.GetUid(), in.GetLimit(), in.GetLastPostCreateTime())
	log.InfoWithCtx(ctx, "GetGameOnlyDistractPostList req:%s, resp:%s", in.String(), out.String())
	return out, err
}

func (s *Server) IsConfigTabVisible(ctx context.Context, req *pb.IsConfigTabVisibleReq) (out *pb.IsConfigTabVisibleResp, err error) {
	out = new(pb.IsConfigTabVisibleResp)
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok || len(serviceInfo.DeviceID) == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, getUserInfoErrorMsg)
	}

	deviceHexId := device_id.ToDeviceHexId(serviceInfo.DeviceID, true)
	deviceId, err := device_id.ToClientDeviceId(deviceHexId, uint32(serviceInfo.ClientType))
	if err != nil {
		log.ErrorWithCtx(ctx, "IsConfigTabVisible device_id.ToClientDeviceId err:%v, req:%s, serviceInfo:%+v", err, req.String(), serviceInfo)
		return out, err
	}
	log.InfoWithCtx(ctx, "IsConfigTabVisible success, req: %+v, deviceId: %s, serviceInfo: %+v", req, deviceId, serviceInfo)

	configTab := s.manager.GetConfigTabsByType(ctx, req.GetConfigTabType(), req.GetTabId(), deviceId)
	if configTab != nil {
		out.IsVisible = true
	} else {
		out.IsVisible = false
	}

	log.InfoWithCtx(ctx, "IsConfigTabVisible success, req: %+v, out: %+v", req, out)
	return out, err
}

func (s *Server) GetBanGameHallUser(ctx context.Context, in *pb.GetBanGameHallUserReq) (out *pb.GetBanGameHallUserResp, err error) {
	out = &pb.GetBanGameHallUserResp{}
	gameHallConfs, err := s.banPostConfigMgr.GetBanGameHallConfig(ctx)
	if err != nil {
		return out, err
	}
	out.Configs = gameHallConfs
	return out, nil
}

func (s *Server) UpdateBanGameHallGetStatus(ctx context.Context, in *pb.UpdateBanGameHallGetStatusReq) (out *pb.UpdateBanGameHallGetStatusResp, err error) {
	out = &pb.UpdateBanGameHallGetStatusResp{}
	log.InfoWithCtx(ctx, "UpdateBanGameHallGetStatus req: %s", in.String())
	err = s.banPostConfigMgr.UpdateBanGameHallGetStatus(ctx, in.GetIds())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateBanGameHallGetStatus in:%s err: %v", in.String(), err)
		return out, err
	}
	return out, nil
}

func (s *Server) GetConfigTabsMapByType(ctx context.Context, req *pb.GetConfigTabsMapByTypeReq) (out *pb.GetConfigTabsMapByTypeResp, err error) {
	out = &pb.GetConfigTabsMapByTypeResp{}
	configTabsMap := cache.GetVisibleConfigTabsMapCache(req.GetConfigTabType())
	out.ConfigTabsMap = configTabsMap
	log.InfoWithCtx(ctx, "GetConfigTabsMapByType success, req: %+v, len(configTabsMap): %d", req, len(out.GetConfigTabsMap()))
	return out, nil
}

func (s *Server) UpsertConfigTabEntry(ctx context.Context, req *pb.UpsertConfigTabEntryReq) (out *pb.UpsertConfigTabEntryResp, err error) {
	out = &pb.UpsertConfigTabEntryResp{}
	log.InfoWithCtx(ctx, "UpsertConfigTabEntry req: %s", req.String())
	if req.GetConfigTabEntry().GetTabSubType() == pb.TabSubType_COMPREHENSIVE_CHANNEL {
		err = s.manager.UpsertComprehensiveTab(ctx, req.GetConfigTabEntry())
	} else {
		err = s.manager.UpsertConfigTabEntry(ctx, req.GetConfigTabEntry())
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "UpsertConfigTabEntry err: %v, in: %+v", err, req)
		return out, err
	}
	log.InfoWithCtx(ctx, "UpsertConfigTabEntry success, in: %+v", req)
	return out, nil
}
