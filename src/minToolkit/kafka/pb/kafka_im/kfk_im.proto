syntax = "proto3";

package kafka_im;

// 通用IM事件
message CommImEvent {
  enum EventType {
    EVENT_TYPE_UNSPECIFIED = 0;
    EVENT_TYPE_GROUP_IM = 1; // 群聊
    EVENT_TYPE_1V1_SEND_IM = 2; // 私聊 发送者
    EVENT_TYPE_1V1_RECV_IM = 3; // 私聊 接收者
    EVENT_TYPE_SENDIM_BOT = 4; // 运营后台助手推送
  }

  enum DataType {
    DATA_TYPE_UNSPECIFIED = 0;
    DATA_TYPE_TIMELINE_MSG = 1; // see ImTimelineMsg
    DATA_TYPE_BATCH_IM_MSG = 2; // see ImTimelineMsg
  }

  EventType event_type = 1; // 事件类型
  DataType data_type = 2; // 数据类型
  bytes data = 3;
  int64 create_ts = 4; // 事件创建时间戳
}

// IM 的 Timeline 消息
message ImTimelineMsg {
  // 消息类型
  enum TYPE {
    // 无效消息类型
    INVALID = 0;
    // Im
    IM_MSG = 1;
  }
  // Timeline TYPE 类型
  uint32 type = 1;
  // 消息序列 id
  uint32 seqid = 2;
  // 消息内容，二进制
  bytes msg_bin = 3;
  // IM 消息类型，see app/im/im.proto IM_MSG_TYPE
  uint32 im_msg_type = 4;
}