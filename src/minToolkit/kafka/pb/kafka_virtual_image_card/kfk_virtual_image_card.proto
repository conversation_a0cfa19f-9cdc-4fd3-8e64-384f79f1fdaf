syntax = "proto3";

package kfk_virtual_image_card;

enum EventType {
  EVENT_TYPE_UNSPECIFIED = 0;
  EVENT_TYPE_EXPIRED = 1; // 过期
  EVENT_TYPE_GAIN = 2; // 获得
}

// 用户无限换装卡变更事件
message UserVirtualImageCardChangeEvent {
  uint32 uid = 1;
  int64 event_ts = 2; // 事件时间戳
  EventType event_type = 3; // 事件类型
  int64 effect_ts = 4; // 生效时间戳
  int64 expire_ts = 5; // 过期时间戳
  oneof opt {
    GainOption gain_option = 6; // 获得事件的选项
  }
}

enum GainChannel {
  GAIN_CHANNEL_UNSPECIFIED = 0;
  GAIN_CHANNEL_PLACE_ORDER = 1; // 下单获得
  GAIN_CHANNEL_TRIAL_CARD = 2; // 体验卡获得
  GAIN_CHANNEL_ACTIVITY_ORDER = 3; //  活动下单获得
}

message GainOption {
  GainChannel gain_channel = 1; // 获得渠道
  bool is_first_place_order = 2; // 是否首次下单
}
