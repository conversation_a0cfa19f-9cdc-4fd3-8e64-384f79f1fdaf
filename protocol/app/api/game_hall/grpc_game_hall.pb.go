// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/game_hall/grpc_game_hall.proto

package game_hall // import "golang.52tt.com/protocol/app/api/game_hall"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import game_hall_logic "golang.52tt.com/protocol/app/game-hall-logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GameHallLogicClient is the client API for GameHallLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameHallLogicClient interface {
	// 获取订阅令牌及频道路径
	GetBuildChannelInfo(ctx context.Context, in *game_hall_logic.GetBuildChannelInfoRequest, opts ...grpc.CallOption) (*game_hall_logic.GetBuildChannelInfoResponse, error)
	// 拉取历史消息
	GetMsgList(ctx context.Context, in *game_hall_logic.GetMsgListRequest, opts ...grpc.CallOption) (*game_hall_logic.GetMsgListResponse, error)
	// 发送消息
	SendGameImMsg(ctx context.Context, in *game_hall_logic.SendGameImMsgRequest, opts ...grpc.CallOption) (*game_hall_logic.SendGameImMsgResponse, error)
	// 拉取组队信息
	GetGameHallTeamList(ctx context.Context, in *game_hall_logic.GetGameHallTeamListReq, opts ...grpc.CallOption) (*game_hall_logic.GetGameHallTeamListResp, error)
	// 组队大厅是否可进房前置判断
	CheckHallEnterRoom(ctx context.Context, in *game_hall_logic.CheckHallEnterRoomReq, opts ...grpc.CallOption) (*game_hall_logic.CheckHallEnterRoomResp, error)
	// 拉取用户未读的@消息
	GetUserUnreadAtMsg(ctx context.Context, in *game_hall_logic.GetUserUnreadAtMsgReq, opts ...grpc.CallOption) (*game_hall_logic.GetUserUnreadAtMsgResp, error)
	// 标记@消息为已读
	MarkUserAtMsgRead(ctx context.Context, in *game_hall_logic.MarkUserAtMsgReadReq, opts ...grpc.CallOption) (*game_hall_logic.MarkUserAtMsgReadResp, error)
	// 检查用户是否可以发送邀请进房
	CheckSendInviteRoomCond(ctx context.Context, in *game_hall_logic.CheckSendInviteRoomCondReq, opts ...grpc.CallOption) (*game_hall_logic.CheckSendInviteRoomCondResp, error)
	// 更改组队大厅消息通知状态
	UpdateGameHallNotifyStatus(ctx context.Context, in *game_hall_logic.UpdateGameHallNotifyStatusReq, opts ...grpc.CallOption) (*game_hall_logic.UpdateGameHallNotifyStatusResp, error)
	// 拉取组队大厅消息通知状态
	GetGameHallNotifyStatus(ctx context.Context, in *game_hall_logic.GetGameHallNotifyStatusReq, opts ...grpc.CallOption) (*game_hall_logic.GetGameHallNotifyStatusResp, error)
	SetShowEntranceSetting(ctx context.Context, in *game_hall_logic.SetShowEntranceSettingReq, opts ...grpc.CallOption) (*game_hall_logic.SetShowEntranceSettingResp, error)
	GetShowEntranceSetting(ctx context.Context, in *game_hall_logic.GetShowEntranceSettingReq, opts ...grpc.CallOption) (*game_hall_logic.GetShowEntranceSettingResp, error)
	GetGameHallPinConf(ctx context.Context, in *game_hall_logic.GetGameHallPinConfReq, opts ...grpc.CallOption) (*game_hall_logic.GetGameHallPinConfResp, error)
}

type gameHallLogicClient struct {
	cc *grpc.ClientConn
}

func NewGameHallLogicClient(cc *grpc.ClientConn) GameHallLogicClient {
	return &gameHallLogicClient{cc}
}

func (c *gameHallLogicClient) GetBuildChannelInfo(ctx context.Context, in *game_hall_logic.GetBuildChannelInfoRequest, opts ...grpc.CallOption) (*game_hall_logic.GetBuildChannelInfoResponse, error) {
	out := new(game_hall_logic.GetBuildChannelInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.game_hall.GameHallLogic/GetBuildChannelInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallLogicClient) GetMsgList(ctx context.Context, in *game_hall_logic.GetMsgListRequest, opts ...grpc.CallOption) (*game_hall_logic.GetMsgListResponse, error) {
	out := new(game_hall_logic.GetMsgListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.game_hall.GameHallLogic/GetMsgList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallLogicClient) SendGameImMsg(ctx context.Context, in *game_hall_logic.SendGameImMsgRequest, opts ...grpc.CallOption) (*game_hall_logic.SendGameImMsgResponse, error) {
	out := new(game_hall_logic.SendGameImMsgResponse)
	err := c.cc.Invoke(ctx, "/ga.api.game_hall.GameHallLogic/SendGameImMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallLogicClient) GetGameHallTeamList(ctx context.Context, in *game_hall_logic.GetGameHallTeamListReq, opts ...grpc.CallOption) (*game_hall_logic.GetGameHallTeamListResp, error) {
	out := new(game_hall_logic.GetGameHallTeamListResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_hall.GameHallLogic/GetGameHallTeamList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallLogicClient) CheckHallEnterRoom(ctx context.Context, in *game_hall_logic.CheckHallEnterRoomReq, opts ...grpc.CallOption) (*game_hall_logic.CheckHallEnterRoomResp, error) {
	out := new(game_hall_logic.CheckHallEnterRoomResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_hall.GameHallLogic/CheckHallEnterRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallLogicClient) GetUserUnreadAtMsg(ctx context.Context, in *game_hall_logic.GetUserUnreadAtMsgReq, opts ...grpc.CallOption) (*game_hall_logic.GetUserUnreadAtMsgResp, error) {
	out := new(game_hall_logic.GetUserUnreadAtMsgResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_hall.GameHallLogic/GetUserUnreadAtMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallLogicClient) MarkUserAtMsgRead(ctx context.Context, in *game_hall_logic.MarkUserAtMsgReadReq, opts ...grpc.CallOption) (*game_hall_logic.MarkUserAtMsgReadResp, error) {
	out := new(game_hall_logic.MarkUserAtMsgReadResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_hall.GameHallLogic/MarkUserAtMsgRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallLogicClient) CheckSendInviteRoomCond(ctx context.Context, in *game_hall_logic.CheckSendInviteRoomCondReq, opts ...grpc.CallOption) (*game_hall_logic.CheckSendInviteRoomCondResp, error) {
	out := new(game_hall_logic.CheckSendInviteRoomCondResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_hall.GameHallLogic/CheckSendInviteRoomCond", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallLogicClient) UpdateGameHallNotifyStatus(ctx context.Context, in *game_hall_logic.UpdateGameHallNotifyStatusReq, opts ...grpc.CallOption) (*game_hall_logic.UpdateGameHallNotifyStatusResp, error) {
	out := new(game_hall_logic.UpdateGameHallNotifyStatusResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_hall.GameHallLogic/UpdateGameHallNotifyStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallLogicClient) GetGameHallNotifyStatus(ctx context.Context, in *game_hall_logic.GetGameHallNotifyStatusReq, opts ...grpc.CallOption) (*game_hall_logic.GetGameHallNotifyStatusResp, error) {
	out := new(game_hall_logic.GetGameHallNotifyStatusResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_hall.GameHallLogic/GetGameHallNotifyStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallLogicClient) SetShowEntranceSetting(ctx context.Context, in *game_hall_logic.SetShowEntranceSettingReq, opts ...grpc.CallOption) (*game_hall_logic.SetShowEntranceSettingResp, error) {
	out := new(game_hall_logic.SetShowEntranceSettingResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_hall.GameHallLogic/SetShowEntranceSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallLogicClient) GetShowEntranceSetting(ctx context.Context, in *game_hall_logic.GetShowEntranceSettingReq, opts ...grpc.CallOption) (*game_hall_logic.GetShowEntranceSettingResp, error) {
	out := new(game_hall_logic.GetShowEntranceSettingResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_hall.GameHallLogic/GetShowEntranceSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallLogicClient) GetGameHallPinConf(ctx context.Context, in *game_hall_logic.GetGameHallPinConfReq, opts ...grpc.CallOption) (*game_hall_logic.GetGameHallPinConfResp, error) {
	out := new(game_hall_logic.GetGameHallPinConfResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_hall.GameHallLogic/GetGameHallPinConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameHallLogicServer is the server API for GameHallLogic service.
type GameHallLogicServer interface {
	// 获取订阅令牌及频道路径
	GetBuildChannelInfo(context.Context, *game_hall_logic.GetBuildChannelInfoRequest) (*game_hall_logic.GetBuildChannelInfoResponse, error)
	// 拉取历史消息
	GetMsgList(context.Context, *game_hall_logic.GetMsgListRequest) (*game_hall_logic.GetMsgListResponse, error)
	// 发送消息
	SendGameImMsg(context.Context, *game_hall_logic.SendGameImMsgRequest) (*game_hall_logic.SendGameImMsgResponse, error)
	// 拉取组队信息
	GetGameHallTeamList(context.Context, *game_hall_logic.GetGameHallTeamListReq) (*game_hall_logic.GetGameHallTeamListResp, error)
	// 组队大厅是否可进房前置判断
	CheckHallEnterRoom(context.Context, *game_hall_logic.CheckHallEnterRoomReq) (*game_hall_logic.CheckHallEnterRoomResp, error)
	// 拉取用户未读的@消息
	GetUserUnreadAtMsg(context.Context, *game_hall_logic.GetUserUnreadAtMsgReq) (*game_hall_logic.GetUserUnreadAtMsgResp, error)
	// 标记@消息为已读
	MarkUserAtMsgRead(context.Context, *game_hall_logic.MarkUserAtMsgReadReq) (*game_hall_logic.MarkUserAtMsgReadResp, error)
	// 检查用户是否可以发送邀请进房
	CheckSendInviteRoomCond(context.Context, *game_hall_logic.CheckSendInviteRoomCondReq) (*game_hall_logic.CheckSendInviteRoomCondResp, error)
	// 更改组队大厅消息通知状态
	UpdateGameHallNotifyStatus(context.Context, *game_hall_logic.UpdateGameHallNotifyStatusReq) (*game_hall_logic.UpdateGameHallNotifyStatusResp, error)
	// 拉取组队大厅消息通知状态
	GetGameHallNotifyStatus(context.Context, *game_hall_logic.GetGameHallNotifyStatusReq) (*game_hall_logic.GetGameHallNotifyStatusResp, error)
	SetShowEntranceSetting(context.Context, *game_hall_logic.SetShowEntranceSettingReq) (*game_hall_logic.SetShowEntranceSettingResp, error)
	GetShowEntranceSetting(context.Context, *game_hall_logic.GetShowEntranceSettingReq) (*game_hall_logic.GetShowEntranceSettingResp, error)
	GetGameHallPinConf(context.Context, *game_hall_logic.GetGameHallPinConfReq) (*game_hall_logic.GetGameHallPinConfResp, error)
}

func RegisterGameHallLogicServer(s *grpc.Server, srv GameHallLogicServer) {
	s.RegisterService(&_GameHallLogic_serviceDesc, srv)
}

func _GameHallLogic_GetBuildChannelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_hall_logic.GetBuildChannelInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallLogicServer).GetBuildChannelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_hall.GameHallLogic/GetBuildChannelInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallLogicServer).GetBuildChannelInfo(ctx, req.(*game_hall_logic.GetBuildChannelInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHallLogic_GetMsgList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_hall_logic.GetMsgListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallLogicServer).GetMsgList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_hall.GameHallLogic/GetMsgList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallLogicServer).GetMsgList(ctx, req.(*game_hall_logic.GetMsgListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHallLogic_SendGameImMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_hall_logic.SendGameImMsgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallLogicServer).SendGameImMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_hall.GameHallLogic/SendGameImMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallLogicServer).SendGameImMsg(ctx, req.(*game_hall_logic.SendGameImMsgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHallLogic_GetGameHallTeamList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_hall_logic.GetGameHallTeamListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallLogicServer).GetGameHallTeamList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_hall.GameHallLogic/GetGameHallTeamList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallLogicServer).GetGameHallTeamList(ctx, req.(*game_hall_logic.GetGameHallTeamListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHallLogic_CheckHallEnterRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_hall_logic.CheckHallEnterRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallLogicServer).CheckHallEnterRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_hall.GameHallLogic/CheckHallEnterRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallLogicServer).CheckHallEnterRoom(ctx, req.(*game_hall_logic.CheckHallEnterRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHallLogic_GetUserUnreadAtMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_hall_logic.GetUserUnreadAtMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallLogicServer).GetUserUnreadAtMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_hall.GameHallLogic/GetUserUnreadAtMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallLogicServer).GetUserUnreadAtMsg(ctx, req.(*game_hall_logic.GetUserUnreadAtMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHallLogic_MarkUserAtMsgRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_hall_logic.MarkUserAtMsgReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallLogicServer).MarkUserAtMsgRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_hall.GameHallLogic/MarkUserAtMsgRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallLogicServer).MarkUserAtMsgRead(ctx, req.(*game_hall_logic.MarkUserAtMsgReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHallLogic_CheckSendInviteRoomCond_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_hall_logic.CheckSendInviteRoomCondReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallLogicServer).CheckSendInviteRoomCond(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_hall.GameHallLogic/CheckSendInviteRoomCond",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallLogicServer).CheckSendInviteRoomCond(ctx, req.(*game_hall_logic.CheckSendInviteRoomCondReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHallLogic_UpdateGameHallNotifyStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_hall_logic.UpdateGameHallNotifyStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallLogicServer).UpdateGameHallNotifyStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_hall.GameHallLogic/UpdateGameHallNotifyStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallLogicServer).UpdateGameHallNotifyStatus(ctx, req.(*game_hall_logic.UpdateGameHallNotifyStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHallLogic_GetGameHallNotifyStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_hall_logic.GetGameHallNotifyStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallLogicServer).GetGameHallNotifyStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_hall.GameHallLogic/GetGameHallNotifyStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallLogicServer).GetGameHallNotifyStatus(ctx, req.(*game_hall_logic.GetGameHallNotifyStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHallLogic_SetShowEntranceSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_hall_logic.SetShowEntranceSettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallLogicServer).SetShowEntranceSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_hall.GameHallLogic/SetShowEntranceSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallLogicServer).SetShowEntranceSetting(ctx, req.(*game_hall_logic.SetShowEntranceSettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHallLogic_GetShowEntranceSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_hall_logic.GetShowEntranceSettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallLogicServer).GetShowEntranceSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_hall.GameHallLogic/GetShowEntranceSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallLogicServer).GetShowEntranceSetting(ctx, req.(*game_hall_logic.GetShowEntranceSettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHallLogic_GetGameHallPinConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_hall_logic.GetGameHallPinConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallLogicServer).GetGameHallPinConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_hall.GameHallLogic/GetGameHallPinConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallLogicServer).GetGameHallPinConf(ctx, req.(*game_hall_logic.GetGameHallPinConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameHallLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.game_hall.GameHallLogic",
	HandlerType: (*GameHallLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetBuildChannelInfo",
			Handler:    _GameHallLogic_GetBuildChannelInfo_Handler,
		},
		{
			MethodName: "GetMsgList",
			Handler:    _GameHallLogic_GetMsgList_Handler,
		},
		{
			MethodName: "SendGameImMsg",
			Handler:    _GameHallLogic_SendGameImMsg_Handler,
		},
		{
			MethodName: "GetGameHallTeamList",
			Handler:    _GameHallLogic_GetGameHallTeamList_Handler,
		},
		{
			MethodName: "CheckHallEnterRoom",
			Handler:    _GameHallLogic_CheckHallEnterRoom_Handler,
		},
		{
			MethodName: "GetUserUnreadAtMsg",
			Handler:    _GameHallLogic_GetUserUnreadAtMsg_Handler,
		},
		{
			MethodName: "MarkUserAtMsgRead",
			Handler:    _GameHallLogic_MarkUserAtMsgRead_Handler,
		},
		{
			MethodName: "CheckSendInviteRoomCond",
			Handler:    _GameHallLogic_CheckSendInviteRoomCond_Handler,
		},
		{
			MethodName: "UpdateGameHallNotifyStatus",
			Handler:    _GameHallLogic_UpdateGameHallNotifyStatus_Handler,
		},
		{
			MethodName: "GetGameHallNotifyStatus",
			Handler:    _GameHallLogic_GetGameHallNotifyStatus_Handler,
		},
		{
			MethodName: "SetShowEntranceSetting",
			Handler:    _GameHallLogic_SetShowEntranceSetting_Handler,
		},
		{
			MethodName: "GetShowEntranceSetting",
			Handler:    _GameHallLogic_GetShowEntranceSetting_Handler,
		},
		{
			MethodName: "GetGameHallPinConf",
			Handler:    _GameHallLogic_GetGameHallPinConf_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/game_hall/grpc_game_hall.proto",
}

func init() {
	proto.RegisterFile("api/game_hall/grpc_game_hall.proto", fileDescriptor_grpc_game_hall_d86de7bc0c7cfb0d)
}

var fileDescriptor_grpc_game_hall_d86de7bc0c7cfb0d = []byte{
	// 583 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x95, 0xdf, 0x8a, 0xd3, 0x40,
	0x14, 0xc6, 0xe9, 0x2e, 0xa8, 0x0c, 0x2e, 0xea, 0x08, 0x2b, 0x06, 0x84, 0xb2, 0xb0, 0xe2, 0x36,
	0x74, 0x82, 0x55, 0xaf, 0xbc, 0x72, 0xcb, 0x12, 0x0b, 0x5b, 0x59, 0xda, 0xed, 0x8d, 0x37, 0x65,
	0x4c, 0x4e, 0xd3, 0xb0, 0xc9, 0x4c, 0x9a, 0x9c, 0x5a, 0xbd, 0xb2, 0xee, 0xfa, 0x77, 0xf1, 0x29,
	0x7c, 0x41, 0x5f, 0x40, 0x45, 0x26, 0x6d, 0x46, 0xdb, 0x26, 0xb6, 0xb9, 0x9b, 0xe4, 0xfc, 0xce,
	0xf9, 0xbe, 0x39, 0x33, 0x87, 0x21, 0x7b, 0x3c, 0xf2, 0x2d, 0x8f, 0x87, 0xd0, 0x1f, 0xf2, 0x20,
	0xb0, 0xbc, 0x38, 0x72, 0xfa, 0xfa, 0x93, 0x45, 0xb1, 0x44, 0x49, 0x6f, 0x7a, 0x9c, 0xf1, 0xc8,
	0x67, 0xfa, 0xbf, 0xb1, 0xaf, 0x97, 0xfd, 0x40, 0x7a, 0xbe, 0x63, 0x2d, 0x7d, 0xcf, 0x12, 0x8d,
	0x7b, 0xaa, 0x38, 0xbc, 0x41, 0x10, 0x89, 0x2f, 0xc5, 0xdf, 0xd5, 0x2c, 0xdc, 0xf8, 0x71, 0x9d,
	0xec, 0xd8, 0x3c, 0x84, 0xe7, 0x3c, 0x08, 0x8e, 0x55, 0x1a, 0x7d, 0x47, 0x6e, 0xdb, 0x80, 0x87,
	0x63, 0x3f, 0x70, 0x9b, 0x43, 0x2e, 0x04, 0x04, 0x2d, 0x31, 0x90, 0x94, 0x31, 0x8f, 0xb3, 0x65,
	0x89, 0x1c, 0xb0, 0x03, 0xa3, 0x31, 0x24, 0x68, 0x58, 0x1b, 0xf3, 0x49, 0x24, 0x45, 0x02, 0x7b,
	0x57, 0xcf, 0xa7, 0xd5, 0xed, 0x6b, 0x3f, 0x6b, 0xd4, 0x25, 0xc4, 0x06, 0x6c, 0x27, 0xde, 0xb1,
	0x9f, 0x20, 0xdd, 0x2f, 0xa8, 0x33, 0x8f, 0x67, 0x72, 0xf7, 0xd7, 0x61, 0x0b, 0x2a, 0xbf, 0x6a,
	0x34, 0x24, 0x3b, 0x5d, 0x10, 0xae, 0xda, 0x7b, 0x2b, 0x6c, 0x27, 0x1e, 0x7d, 0x90, 0x57, 0x61,
	0x01, 0xc9, 0xb4, 0x0e, 0x36, 0x20, 0x17, 0xe4, 0x7e, 0xd7, 0xe8, 0x24, 0xed, 0x6a, 0xd6, 0xe9,
	0x53, 0xe0, 0x61, 0xba, 0xbb, 0x5a, 0x81, 0xed, 0x65, 0xb0, 0x03, 0x23, 0xc3, 0xdc, 0x98, 0x4d,
	0xa2, 0xb9, 0xf0, 0xd4, 0xa4, 0x48, 0x68, 0x73, 0x08, 0xce, 0x99, 0x22, 0x8e, 0x04, 0x42, 0xdc,
	0x91, 0x32, 0xa4, 0xb9, 0x5b, 0x58, 0xe5, 0x94, 0x6c, 0x6d, 0x53, 0x54, 0xab, 0xbe, 0x4f, 0x55,
	0x6d, 0xc0, 0x5e, 0x02, 0x71, 0x4f, 0xc4, 0xc0, 0xdd, 0x67, 0xea, 0x20, 0xf2, 0x55, 0x57, 0xb9,
	0x42, 0xd5, 0x3c, 0x54, 0xab, 0x5e, 0x98, 0x74, 0x44, 0x6e, 0xb5, 0x79, 0x7c, 0xa6, 0x98, 0x79,
	0x94, 0xbb, 0xf9, 0xe7, 0xba, 0x82, 0x29, 0xcd, 0x83, 0x0d, 0x49, 0x2d, 0xf9, 0xc1, 0xa4, 0x17,
	0x15, 0x72, 0x27, 0x6d, 0x86, 0x3a, 0xff, 0x96, 0x78, 0xed, 0x23, 0xa8, 0x6e, 0x34, 0xa5, 0x70,
	0xf3, 0x47, 0xa6, 0x00, 0x56, 0xfa, 0x56, 0x29, 0x5e, 0xbb, 0xf8, 0x68, 0xd2, 0x6f, 0x15, 0x62,
	0xf4, 0x22, 0x97, 0x23, 0x64, 0x97, 0xe1, 0x85, 0x44, 0x7f, 0xf0, 0xb6, 0x8b, 0x1c, 0xc7, 0x09,
	0x7d, 0x98, 0x57, 0xb8, 0x98, 0x57, 0x5e, 0x1a, 0x65, 0x53, 0xb4, 0x9d, 0x4f, 0xb3, 0xa6, 0xfc,
	0x73, 0x31, 0x17, 0xbc, 0xb0, 0x35, 0xb7, 0x78, 0xd9, 0x88, 0x55, 0x8a, 0xd7, 0x2e, 0x3e, 0x9b,
	0x74, 0x5a, 0x21, 0xbb, 0x5d, 0xc0, 0xee, 0x50, 0x4e, 0x8e, 0x04, 0xc6, 0x5c, 0x38, 0xd0, 0x05,
	0x44, 0x5f, 0x78, 0xb4, 0x9e, 0x3f, 0xc1, 0x79, 0xac, 0xf2, 0xc0, 0xca, 0xe0, 0xda, 0xc2, 0x97,
	0x99, 0x05, 0xbb, 0x84, 0x05, 0xbb, 0x9c, 0x05, 0x7b, 0x9d, 0x85, 0xaf, 0xd9, 0x24, 0x66, 0xdd,
	0x3a, 0xf1, 0x45, 0x53, 0x8a, 0x41, 0xe1, 0x24, 0x2e, 0x71, 0xff, 0x9b, 0xc4, 0x15, 0x54, 0xab,
	0x5e, 0x9a, 0xc6, 0xdd, 0xf3, 0x69, 0xf5, 0x86, 0x4a, 0xaa, 0xab, 0xa4, 0x7a, 0x9a, 0x74, 0x39,
	0xad, 0x6e, 0x79, 0xf2, 0xf0, 0x94, 0xec, 0x3a, 0x32, 0x64, 0xa3, 0xf1, 0x84, 0x0b, 0x86, 0x38,
	0x7b, 0x87, 0xd4, 0xdb, 0xf6, 0xf2, 0xb1, 0x27, 0x03, 0x2e, 0x3c, 0xf6, 0xa4, 0x81, 0xc8, 0x1c,
	0x19, 0x5a, 0x69, 0xc8, 0x91, 0x81, 0xc5, 0xa3, 0xc8, 0x5a, 0x78, 0x24, 0x9f, 0xea, 0xd5, 0xf7,
	0xad, 0xed, 0xce, 0x49, 0xf3, 0xd5, 0x95, 0x94, 0x7d, 0xf4, 0x27, 0x00, 0x00, 0xff, 0xff, 0xaa,
	0xea, 0x89, 0x9e, 0x4c, 0x07, 0x00, 0x00,
}
