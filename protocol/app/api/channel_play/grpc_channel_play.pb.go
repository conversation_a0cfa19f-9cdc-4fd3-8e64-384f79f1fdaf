// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/channel_play/grpc_channel_play.proto

package channel_play // import "golang.52tt.com/protocol/app/api/channel_play"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import channel_play "golang.52tt.com/protocol/app/channel-play"
import hobby_channel "golang.52tt.com/protocol/app/hobby-channel"
import topic_channel "golang.52tt.com/protocol/app/topic-channel"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelPlayLogicClient is the client API for ChannelPlayLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelPlayLogicClient interface {
	ListTopicChannel(ctx context.Context, in *channel_play.ListTopicChannelReq, opts ...grpc.CallOption) (*channel_play.ListTopicChannelResp, error)
	GetSecondaryFilter(ctx context.Context, in *channel_play.GetSecondaryFilterReq, opts ...grpc.CallOption) (*channel_play.GetSecondaryFilterResp, error)
	GetSecondaryFilterByCategory(ctx context.Context, in *channel_play.GetSecondaryFilterByCategoryReq, opts ...grpc.CallOption) (*channel_play.GetSecondaryFilterByCategoryResp, error)
	GetDefaultRoomNameList(ctx context.Context, in *channel_play.GetDefaultRoomNameListReq, opts ...grpc.CallOption) (*channel_play.GetDefaultRoomNameListResp, error)
	GetGameHomePageDIYFilter(ctx context.Context, in *hobby_channel.GetGameHomePageDIYFilterReq, opts ...grpc.CallOption) (*hobby_channel.GetGameHomePageDIYFilterResp, error)
	SetGameHomePageDIYFilter(ctx context.Context, in *hobby_channel.SetGameHomePageDIYFilterReq, opts ...grpc.CallOption) (*hobby_channel.SetGameHomePageDIYFilterResp, error)
	GetGameHomePageFilter(ctx context.Context, in *hobby_channel.GetGameHomePageFilterReq, opts ...grpc.CallOption) (*hobby_channel.GetGameHomePageFilterResp, error)
	CreateHobbyChannel(ctx context.Context, in *hobby_channel.CreateHobbyChannelReq, opts ...grpc.CallOption) (*hobby_channel.CreateHobbyChannelResp, error)
	PublishGangupChannel(ctx context.Context, in *channel_play.PublishGangupChannelReq, opts ...grpc.CallOption) (*channel_play.PublishGangupChannelResp, error)
	CancelGangupChannelPublish(ctx context.Context, in *channel_play.CancelGangupChannelPublishReq, opts ...grpc.CallOption) (*channel_play.CancelGangupChannelPublishResp, error)
	GetHomePageHeadConfig(ctx context.Context, in *channel_play.HomePageHeadConfigReq, opts ...grpc.CallOption) (*channel_play.HomePageHeadConfigResp, error)
	GetHotMiniGames(ctx context.Context, in *channel_play.GetHotMiniGamesReq, opts ...grpc.CallOption) (*channel_play.GetHotMiniGamesResp, error)
	GetQuickMiniGames(ctx context.Context, in *channel_play.GetQuickMiniGamesReq, opts ...grpc.CallOption) (*channel_play.GetQuickMiniGamesResp, error)
	GetPlayQuestions(ctx context.Context, in *channel_play.GetPlayQuestionsReq, opts ...grpc.CallOption) (*channel_play.GetPlayQuestionsResp, error)
	GameInsertFlowConfig(ctx context.Context, in *channel_play.GameInsertFlowConfigReq, opts ...grpc.CallOption) (*channel_play.GameInsertFlowConfigResp, error)
	GetHomePageGuide(ctx context.Context, in *channel_play.GetHomePageGuideReq, opts ...grpc.CallOption) (*channel_play.GetHomePageGuideResp, error)
	GetMoreTabConfig(ctx context.Context, in *channel_play.GetMoreTabConfigReq, opts ...grpc.CallOption) (*channel_play.GetMoreTabConfigResp, error)
	GetFilterItemByEntrance(ctx context.Context, in *channel_play.GetFilterItemByEntranceReq, opts ...grpc.CallOption) (*channel_play.GetFilterItemByEntranceResp, error)
	SetDIYFilterByEntrance(ctx context.Context, in *channel_play.SetDIYFilterByEntranceReq, opts ...grpc.CallOption) (*channel_play.SetDIYFilterByEntranceResp, error)
	GetDIYFilterByEntrance(ctx context.Context, in *channel_play.GetDIYFilterByEntranceReq, opts ...grpc.CallOption) (*channel_play.GetDIYFilterByEntranceResp, error)
	GetNegativeFeedBackInRoom(ctx context.Context, in *channel_play.GetNegativeFeedBackInRoomReq, opts ...grpc.CallOption) (*channel_play.GetNegativeFeedBackInRoomResp, error)
	ReportNegativeFeedBackInRoom(ctx context.Context, in *channel_play.ReportNegativeFeedBackInRoomReq, opts ...grpc.CallOption) (*channel_play.ReportNegativeFeedBackInRoomResp, error)
	GetPublishOptionGuide(ctx context.Context, in *channel_play.GetPublishOptionGuideReq, opts ...grpc.CallOption) (*channel_play.GetPublishOptionGuideResp, error)
	GetNewQuickMatchConfig(ctx context.Context, in *channel_play.GetNewQuickMatchConfigReq, opts ...grpc.CallOption) (*channel_play.GetNewQuickMatchConfigResp, error)
	GetTopicChannelCfgInfo(ctx context.Context, in *channel_play.GetTopicChannelCfgInfoReq, opts ...grpc.CallOption) (*channel_play.GetTopicChannelCfgInfoResp, error)
	SetUgcChannelPlayMode(ctx context.Context, in *channel_play.SetUgcChannelPlayModeReq, opts ...grpc.CallOption) (*channel_play.SetUgcChannelPlayModeResp, error)
	TypingStatusBroadcast(ctx context.Context, in *channel_play.TypingStatusBroadcastReq, opts ...grpc.CallOption) (*channel_play.TypingStatusBroadcastResp, error)
	GetChannelPlayModeGuide(ctx context.Context, in *channel_play.GetChannelPlayModeGuideReq, opts ...grpc.CallOption) (*channel_play.GetChannelPlayModeGuideResp, error)
	ReportDailyTask(ctx context.Context, in *channel_play.ReportDailyTaskReq, opts ...grpc.CallOption) (*channel_play.ReportDailyTaskResp, error)
	GetCache(ctx context.Context, in *channel_play.GetCacheReq, opts ...grpc.CallOption) (*channel_play.GetCacheResp, error)
	ShowTopicChannelTabList(ctx context.Context, in *topic_channel.ShowTopicChannelTabListReq, opts ...grpc.CallOption) (*topic_channel.ShowTopicChannelTabListResp, error)
	HomePageHeadConfigEnterCheck(ctx context.Context, in *channel_play.HomePageHeadConfigEnterCheckReq, opts ...grpc.CallOption) (*channel_play.HomePageHeadConfigEnterCheckResp, error)
	GetChannelListGuideConfigs(ctx context.Context, in *channel_play.GetChannelListGuideConfigsReq, opts ...grpc.CallOption) (*channel_play.GetChannelListGuideConfigsResp, error)
	GetTabInfos(ctx context.Context, in *channel_play.GetTabInfosReq, opts ...grpc.CallOption) (*channel_play.GetTabInfosResp, error)
	GetRecommendGames(ctx context.Context, in *channel_play.GetRecommendGamesReq, opts ...grpc.CallOption) (*channel_play.GetRecommendGamesResp, error)
	RefreshGameLabel(ctx context.Context, in *channel_play.RefreshGameLabelReq, opts ...grpc.CallOption) (*channel_play.RefreshGameLabelResp, error)
	GetSupportTabList(ctx context.Context, in *channel_play.GetSupportTabListReq, opts ...grpc.CallOption) (*channel_play.GetSupportTabListResp, error)
	GetChannelMicVolSet(ctx context.Context, in *channel_play.GetChannelMicVolSetReq, opts ...grpc.CallOption) (*channel_play.GetChannelMicVolSetResp, error)
	SetChannelMicVol(ctx context.Context, in *channel_play.SetChannelMicVolReq, opts ...grpc.CallOption) (*channel_play.SetChannelMicVolResp, error)
}

type channelPlayLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelPlayLogicClient(cc *grpc.ClientConn) ChannelPlayLogicClient {
	return &channelPlayLogicClient{cc}
}

func (c *channelPlayLogicClient) ListTopicChannel(ctx context.Context, in *channel_play.ListTopicChannelReq, opts ...grpc.CallOption) (*channel_play.ListTopicChannelResp, error) {
	out := new(channel_play.ListTopicChannelResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/ListTopicChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetSecondaryFilter(ctx context.Context, in *channel_play.GetSecondaryFilterReq, opts ...grpc.CallOption) (*channel_play.GetSecondaryFilterResp, error) {
	out := new(channel_play.GetSecondaryFilterResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetSecondaryFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetSecondaryFilterByCategory(ctx context.Context, in *channel_play.GetSecondaryFilterByCategoryReq, opts ...grpc.CallOption) (*channel_play.GetSecondaryFilterByCategoryResp, error) {
	out := new(channel_play.GetSecondaryFilterByCategoryResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetSecondaryFilterByCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetDefaultRoomNameList(ctx context.Context, in *channel_play.GetDefaultRoomNameListReq, opts ...grpc.CallOption) (*channel_play.GetDefaultRoomNameListResp, error) {
	out := new(channel_play.GetDefaultRoomNameListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetDefaultRoomNameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetGameHomePageDIYFilter(ctx context.Context, in *hobby_channel.GetGameHomePageDIYFilterReq, opts ...grpc.CallOption) (*hobby_channel.GetGameHomePageDIYFilterResp, error) {
	out := new(hobby_channel.GetGameHomePageDIYFilterResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetGameHomePageDIYFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) SetGameHomePageDIYFilter(ctx context.Context, in *hobby_channel.SetGameHomePageDIYFilterReq, opts ...grpc.CallOption) (*hobby_channel.SetGameHomePageDIYFilterResp, error) {
	out := new(hobby_channel.SetGameHomePageDIYFilterResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/SetGameHomePageDIYFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetGameHomePageFilter(ctx context.Context, in *hobby_channel.GetGameHomePageFilterReq, opts ...grpc.CallOption) (*hobby_channel.GetGameHomePageFilterResp, error) {
	out := new(hobby_channel.GetGameHomePageFilterResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetGameHomePageFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) CreateHobbyChannel(ctx context.Context, in *hobby_channel.CreateHobbyChannelReq, opts ...grpc.CallOption) (*hobby_channel.CreateHobbyChannelResp, error) {
	out := new(hobby_channel.CreateHobbyChannelResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/CreateHobbyChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) PublishGangupChannel(ctx context.Context, in *channel_play.PublishGangupChannelReq, opts ...grpc.CallOption) (*channel_play.PublishGangupChannelResp, error) {
	out := new(channel_play.PublishGangupChannelResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/PublishGangupChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) CancelGangupChannelPublish(ctx context.Context, in *channel_play.CancelGangupChannelPublishReq, opts ...grpc.CallOption) (*channel_play.CancelGangupChannelPublishResp, error) {
	out := new(channel_play.CancelGangupChannelPublishResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/CancelGangupChannelPublish", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetHomePageHeadConfig(ctx context.Context, in *channel_play.HomePageHeadConfigReq, opts ...grpc.CallOption) (*channel_play.HomePageHeadConfigResp, error) {
	out := new(channel_play.HomePageHeadConfigResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetHomePageHeadConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetHotMiniGames(ctx context.Context, in *channel_play.GetHotMiniGamesReq, opts ...grpc.CallOption) (*channel_play.GetHotMiniGamesResp, error) {
	out := new(channel_play.GetHotMiniGamesResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetHotMiniGames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetQuickMiniGames(ctx context.Context, in *channel_play.GetQuickMiniGamesReq, opts ...grpc.CallOption) (*channel_play.GetQuickMiniGamesResp, error) {
	out := new(channel_play.GetQuickMiniGamesResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetQuickMiniGames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetPlayQuestions(ctx context.Context, in *channel_play.GetPlayQuestionsReq, opts ...grpc.CallOption) (*channel_play.GetPlayQuestionsResp, error) {
	out := new(channel_play.GetPlayQuestionsResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetPlayQuestions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GameInsertFlowConfig(ctx context.Context, in *channel_play.GameInsertFlowConfigReq, opts ...grpc.CallOption) (*channel_play.GameInsertFlowConfigResp, error) {
	out := new(channel_play.GameInsertFlowConfigResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GameInsertFlowConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetHomePageGuide(ctx context.Context, in *channel_play.GetHomePageGuideReq, opts ...grpc.CallOption) (*channel_play.GetHomePageGuideResp, error) {
	out := new(channel_play.GetHomePageGuideResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetHomePageGuide", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetMoreTabConfig(ctx context.Context, in *channel_play.GetMoreTabConfigReq, opts ...grpc.CallOption) (*channel_play.GetMoreTabConfigResp, error) {
	out := new(channel_play.GetMoreTabConfigResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetMoreTabConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetFilterItemByEntrance(ctx context.Context, in *channel_play.GetFilterItemByEntranceReq, opts ...grpc.CallOption) (*channel_play.GetFilterItemByEntranceResp, error) {
	out := new(channel_play.GetFilterItemByEntranceResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetFilterItemByEntrance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) SetDIYFilterByEntrance(ctx context.Context, in *channel_play.SetDIYFilterByEntranceReq, opts ...grpc.CallOption) (*channel_play.SetDIYFilterByEntranceResp, error) {
	out := new(channel_play.SetDIYFilterByEntranceResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/SetDIYFilterByEntrance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetDIYFilterByEntrance(ctx context.Context, in *channel_play.GetDIYFilterByEntranceReq, opts ...grpc.CallOption) (*channel_play.GetDIYFilterByEntranceResp, error) {
	out := new(channel_play.GetDIYFilterByEntranceResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetDIYFilterByEntrance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetNegativeFeedBackInRoom(ctx context.Context, in *channel_play.GetNegativeFeedBackInRoomReq, opts ...grpc.CallOption) (*channel_play.GetNegativeFeedBackInRoomResp, error) {
	out := new(channel_play.GetNegativeFeedBackInRoomResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetNegativeFeedBackInRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) ReportNegativeFeedBackInRoom(ctx context.Context, in *channel_play.ReportNegativeFeedBackInRoomReq, opts ...grpc.CallOption) (*channel_play.ReportNegativeFeedBackInRoomResp, error) {
	out := new(channel_play.ReportNegativeFeedBackInRoomResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/ReportNegativeFeedBackInRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetPublishOptionGuide(ctx context.Context, in *channel_play.GetPublishOptionGuideReq, opts ...grpc.CallOption) (*channel_play.GetPublishOptionGuideResp, error) {
	out := new(channel_play.GetPublishOptionGuideResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetPublishOptionGuide", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetNewQuickMatchConfig(ctx context.Context, in *channel_play.GetNewQuickMatchConfigReq, opts ...grpc.CallOption) (*channel_play.GetNewQuickMatchConfigResp, error) {
	out := new(channel_play.GetNewQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetNewQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetTopicChannelCfgInfo(ctx context.Context, in *channel_play.GetTopicChannelCfgInfoReq, opts ...grpc.CallOption) (*channel_play.GetTopicChannelCfgInfoResp, error) {
	out := new(channel_play.GetTopicChannelCfgInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetTopicChannelCfgInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) SetUgcChannelPlayMode(ctx context.Context, in *channel_play.SetUgcChannelPlayModeReq, opts ...grpc.CallOption) (*channel_play.SetUgcChannelPlayModeResp, error) {
	out := new(channel_play.SetUgcChannelPlayModeResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/SetUgcChannelPlayMode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) TypingStatusBroadcast(ctx context.Context, in *channel_play.TypingStatusBroadcastReq, opts ...grpc.CallOption) (*channel_play.TypingStatusBroadcastResp, error) {
	out := new(channel_play.TypingStatusBroadcastResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/TypingStatusBroadcast", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetChannelPlayModeGuide(ctx context.Context, in *channel_play.GetChannelPlayModeGuideReq, opts ...grpc.CallOption) (*channel_play.GetChannelPlayModeGuideResp, error) {
	out := new(channel_play.GetChannelPlayModeGuideResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetChannelPlayModeGuide", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) ReportDailyTask(ctx context.Context, in *channel_play.ReportDailyTaskReq, opts ...grpc.CallOption) (*channel_play.ReportDailyTaskResp, error) {
	out := new(channel_play.ReportDailyTaskResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/ReportDailyTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetCache(ctx context.Context, in *channel_play.GetCacheReq, opts ...grpc.CallOption) (*channel_play.GetCacheResp, error) {
	out := new(channel_play.GetCacheResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) ShowTopicChannelTabList(ctx context.Context, in *topic_channel.ShowTopicChannelTabListReq, opts ...grpc.CallOption) (*topic_channel.ShowTopicChannelTabListResp, error) {
	out := new(topic_channel.ShowTopicChannelTabListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/ShowTopicChannelTabList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) HomePageHeadConfigEnterCheck(ctx context.Context, in *channel_play.HomePageHeadConfigEnterCheckReq, opts ...grpc.CallOption) (*channel_play.HomePageHeadConfigEnterCheckResp, error) {
	out := new(channel_play.HomePageHeadConfigEnterCheckResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/HomePageHeadConfigEnterCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetChannelListGuideConfigs(ctx context.Context, in *channel_play.GetChannelListGuideConfigsReq, opts ...grpc.CallOption) (*channel_play.GetChannelListGuideConfigsResp, error) {
	out := new(channel_play.GetChannelListGuideConfigsResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetChannelListGuideConfigs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetTabInfos(ctx context.Context, in *channel_play.GetTabInfosReq, opts ...grpc.CallOption) (*channel_play.GetTabInfosResp, error) {
	out := new(channel_play.GetTabInfosResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetTabInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetRecommendGames(ctx context.Context, in *channel_play.GetRecommendGamesReq, opts ...grpc.CallOption) (*channel_play.GetRecommendGamesResp, error) {
	out := new(channel_play.GetRecommendGamesResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetRecommendGames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) RefreshGameLabel(ctx context.Context, in *channel_play.RefreshGameLabelReq, opts ...grpc.CallOption) (*channel_play.RefreshGameLabelResp, error) {
	out := new(channel_play.RefreshGameLabelResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/RefreshGameLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetSupportTabList(ctx context.Context, in *channel_play.GetSupportTabListReq, opts ...grpc.CallOption) (*channel_play.GetSupportTabListResp, error) {
	out := new(channel_play.GetSupportTabListResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetSupportTabList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) GetChannelMicVolSet(ctx context.Context, in *channel_play.GetChannelMicVolSetReq, opts ...grpc.CallOption) (*channel_play.GetChannelMicVolSetResp, error) {
	out := new(channel_play.GetChannelMicVolSetResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/GetChannelMicVolSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayLogicClient) SetChannelMicVol(ctx context.Context, in *channel_play.SetChannelMicVolReq, opts ...grpc.CallOption) (*channel_play.SetChannelMicVolResp, error) {
	out := new(channel_play.SetChannelMicVolResp)
	err := c.cc.Invoke(ctx, "/ga.api.channel_play.ChannelPlayLogic/SetChannelMicVol", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelPlayLogicServer is the server API for ChannelPlayLogic service.
type ChannelPlayLogicServer interface {
	ListTopicChannel(context.Context, *channel_play.ListTopicChannelReq) (*channel_play.ListTopicChannelResp, error)
	GetSecondaryFilter(context.Context, *channel_play.GetSecondaryFilterReq) (*channel_play.GetSecondaryFilterResp, error)
	GetSecondaryFilterByCategory(context.Context, *channel_play.GetSecondaryFilterByCategoryReq) (*channel_play.GetSecondaryFilterByCategoryResp, error)
	GetDefaultRoomNameList(context.Context, *channel_play.GetDefaultRoomNameListReq) (*channel_play.GetDefaultRoomNameListResp, error)
	GetGameHomePageDIYFilter(context.Context, *hobby_channel.GetGameHomePageDIYFilterReq) (*hobby_channel.GetGameHomePageDIYFilterResp, error)
	SetGameHomePageDIYFilter(context.Context, *hobby_channel.SetGameHomePageDIYFilterReq) (*hobby_channel.SetGameHomePageDIYFilterResp, error)
	GetGameHomePageFilter(context.Context, *hobby_channel.GetGameHomePageFilterReq) (*hobby_channel.GetGameHomePageFilterResp, error)
	CreateHobbyChannel(context.Context, *hobby_channel.CreateHobbyChannelReq) (*hobby_channel.CreateHobbyChannelResp, error)
	PublishGangupChannel(context.Context, *channel_play.PublishGangupChannelReq) (*channel_play.PublishGangupChannelResp, error)
	CancelGangupChannelPublish(context.Context, *channel_play.CancelGangupChannelPublishReq) (*channel_play.CancelGangupChannelPublishResp, error)
	GetHomePageHeadConfig(context.Context, *channel_play.HomePageHeadConfigReq) (*channel_play.HomePageHeadConfigResp, error)
	GetHotMiniGames(context.Context, *channel_play.GetHotMiniGamesReq) (*channel_play.GetHotMiniGamesResp, error)
	GetQuickMiniGames(context.Context, *channel_play.GetQuickMiniGamesReq) (*channel_play.GetQuickMiniGamesResp, error)
	GetPlayQuestions(context.Context, *channel_play.GetPlayQuestionsReq) (*channel_play.GetPlayQuestionsResp, error)
	GameInsertFlowConfig(context.Context, *channel_play.GameInsertFlowConfigReq) (*channel_play.GameInsertFlowConfigResp, error)
	GetHomePageGuide(context.Context, *channel_play.GetHomePageGuideReq) (*channel_play.GetHomePageGuideResp, error)
	GetMoreTabConfig(context.Context, *channel_play.GetMoreTabConfigReq) (*channel_play.GetMoreTabConfigResp, error)
	GetFilterItemByEntrance(context.Context, *channel_play.GetFilterItemByEntranceReq) (*channel_play.GetFilterItemByEntranceResp, error)
	SetDIYFilterByEntrance(context.Context, *channel_play.SetDIYFilterByEntranceReq) (*channel_play.SetDIYFilterByEntranceResp, error)
	GetDIYFilterByEntrance(context.Context, *channel_play.GetDIYFilterByEntranceReq) (*channel_play.GetDIYFilterByEntranceResp, error)
	GetNegativeFeedBackInRoom(context.Context, *channel_play.GetNegativeFeedBackInRoomReq) (*channel_play.GetNegativeFeedBackInRoomResp, error)
	ReportNegativeFeedBackInRoom(context.Context, *channel_play.ReportNegativeFeedBackInRoomReq) (*channel_play.ReportNegativeFeedBackInRoomResp, error)
	GetPublishOptionGuide(context.Context, *channel_play.GetPublishOptionGuideReq) (*channel_play.GetPublishOptionGuideResp, error)
	GetNewQuickMatchConfig(context.Context, *channel_play.GetNewQuickMatchConfigReq) (*channel_play.GetNewQuickMatchConfigResp, error)
	GetTopicChannelCfgInfo(context.Context, *channel_play.GetTopicChannelCfgInfoReq) (*channel_play.GetTopicChannelCfgInfoResp, error)
	SetUgcChannelPlayMode(context.Context, *channel_play.SetUgcChannelPlayModeReq) (*channel_play.SetUgcChannelPlayModeResp, error)
	TypingStatusBroadcast(context.Context, *channel_play.TypingStatusBroadcastReq) (*channel_play.TypingStatusBroadcastResp, error)
	GetChannelPlayModeGuide(context.Context, *channel_play.GetChannelPlayModeGuideReq) (*channel_play.GetChannelPlayModeGuideResp, error)
	ReportDailyTask(context.Context, *channel_play.ReportDailyTaskReq) (*channel_play.ReportDailyTaskResp, error)
	GetCache(context.Context, *channel_play.GetCacheReq) (*channel_play.GetCacheResp, error)
	ShowTopicChannelTabList(context.Context, *topic_channel.ShowTopicChannelTabListReq) (*topic_channel.ShowTopicChannelTabListResp, error)
	HomePageHeadConfigEnterCheck(context.Context, *channel_play.HomePageHeadConfigEnterCheckReq) (*channel_play.HomePageHeadConfigEnterCheckResp, error)
	GetChannelListGuideConfigs(context.Context, *channel_play.GetChannelListGuideConfigsReq) (*channel_play.GetChannelListGuideConfigsResp, error)
	GetTabInfos(context.Context, *channel_play.GetTabInfosReq) (*channel_play.GetTabInfosResp, error)
	GetRecommendGames(context.Context, *channel_play.GetRecommendGamesReq) (*channel_play.GetRecommendGamesResp, error)
	RefreshGameLabel(context.Context, *channel_play.RefreshGameLabelReq) (*channel_play.RefreshGameLabelResp, error)
	GetSupportTabList(context.Context, *channel_play.GetSupportTabListReq) (*channel_play.GetSupportTabListResp, error)
	GetChannelMicVolSet(context.Context, *channel_play.GetChannelMicVolSetReq) (*channel_play.GetChannelMicVolSetResp, error)
	SetChannelMicVol(context.Context, *channel_play.SetChannelMicVolReq) (*channel_play.SetChannelMicVolResp, error)
}

func RegisterChannelPlayLogicServer(s *grpc.Server, srv ChannelPlayLogicServer) {
	s.RegisterService(&_ChannelPlayLogic_serviceDesc, srv)
}

func _ChannelPlayLogic_ListTopicChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.ListTopicChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).ListTopicChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/ListTopicChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).ListTopicChannel(ctx, req.(*channel_play.ListTopicChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetSecondaryFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetSecondaryFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetSecondaryFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetSecondaryFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetSecondaryFilter(ctx, req.(*channel_play.GetSecondaryFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetSecondaryFilterByCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetSecondaryFilterByCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetSecondaryFilterByCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetSecondaryFilterByCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetSecondaryFilterByCategory(ctx, req.(*channel_play.GetSecondaryFilterByCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetDefaultRoomNameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetDefaultRoomNameListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetDefaultRoomNameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetDefaultRoomNameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetDefaultRoomNameList(ctx, req.(*channel_play.GetDefaultRoomNameListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetGameHomePageDIYFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(hobby_channel.GetGameHomePageDIYFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetGameHomePageDIYFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetGameHomePageDIYFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetGameHomePageDIYFilter(ctx, req.(*hobby_channel.GetGameHomePageDIYFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_SetGameHomePageDIYFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(hobby_channel.SetGameHomePageDIYFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).SetGameHomePageDIYFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/SetGameHomePageDIYFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).SetGameHomePageDIYFilter(ctx, req.(*hobby_channel.SetGameHomePageDIYFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetGameHomePageFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(hobby_channel.GetGameHomePageFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetGameHomePageFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetGameHomePageFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetGameHomePageFilter(ctx, req.(*hobby_channel.GetGameHomePageFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_CreateHobbyChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(hobby_channel.CreateHobbyChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).CreateHobbyChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/CreateHobbyChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).CreateHobbyChannel(ctx, req.(*hobby_channel.CreateHobbyChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_PublishGangupChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.PublishGangupChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).PublishGangupChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/PublishGangupChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).PublishGangupChannel(ctx, req.(*channel_play.PublishGangupChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_CancelGangupChannelPublish_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.CancelGangupChannelPublishReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).CancelGangupChannelPublish(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/CancelGangupChannelPublish",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).CancelGangupChannelPublish(ctx, req.(*channel_play.CancelGangupChannelPublishReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetHomePageHeadConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.HomePageHeadConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetHomePageHeadConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetHomePageHeadConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetHomePageHeadConfig(ctx, req.(*channel_play.HomePageHeadConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetHotMiniGames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetHotMiniGamesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetHotMiniGames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetHotMiniGames",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetHotMiniGames(ctx, req.(*channel_play.GetHotMiniGamesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetQuickMiniGames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetQuickMiniGamesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetQuickMiniGames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetQuickMiniGames",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetQuickMiniGames(ctx, req.(*channel_play.GetQuickMiniGamesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetPlayQuestions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetPlayQuestionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetPlayQuestions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetPlayQuestions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetPlayQuestions(ctx, req.(*channel_play.GetPlayQuestionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GameInsertFlowConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GameInsertFlowConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GameInsertFlowConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GameInsertFlowConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GameInsertFlowConfig(ctx, req.(*channel_play.GameInsertFlowConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetHomePageGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetHomePageGuideReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetHomePageGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetHomePageGuide",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetHomePageGuide(ctx, req.(*channel_play.GetHomePageGuideReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetMoreTabConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetMoreTabConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetMoreTabConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetMoreTabConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetMoreTabConfig(ctx, req.(*channel_play.GetMoreTabConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetFilterItemByEntrance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetFilterItemByEntranceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetFilterItemByEntrance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetFilterItemByEntrance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetFilterItemByEntrance(ctx, req.(*channel_play.GetFilterItemByEntranceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_SetDIYFilterByEntrance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.SetDIYFilterByEntranceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).SetDIYFilterByEntrance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/SetDIYFilterByEntrance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).SetDIYFilterByEntrance(ctx, req.(*channel_play.SetDIYFilterByEntranceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetDIYFilterByEntrance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetDIYFilterByEntranceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetDIYFilterByEntrance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetDIYFilterByEntrance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetDIYFilterByEntrance(ctx, req.(*channel_play.GetDIYFilterByEntranceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetNegativeFeedBackInRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetNegativeFeedBackInRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetNegativeFeedBackInRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetNegativeFeedBackInRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetNegativeFeedBackInRoom(ctx, req.(*channel_play.GetNegativeFeedBackInRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_ReportNegativeFeedBackInRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.ReportNegativeFeedBackInRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).ReportNegativeFeedBackInRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/ReportNegativeFeedBackInRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).ReportNegativeFeedBackInRoom(ctx, req.(*channel_play.ReportNegativeFeedBackInRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetPublishOptionGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetPublishOptionGuideReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetPublishOptionGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetPublishOptionGuide",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetPublishOptionGuide(ctx, req.(*channel_play.GetPublishOptionGuideReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetNewQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetNewQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetNewQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetNewQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetNewQuickMatchConfig(ctx, req.(*channel_play.GetNewQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetTopicChannelCfgInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetTopicChannelCfgInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetTopicChannelCfgInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetTopicChannelCfgInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetTopicChannelCfgInfo(ctx, req.(*channel_play.GetTopicChannelCfgInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_SetUgcChannelPlayMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.SetUgcChannelPlayModeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).SetUgcChannelPlayMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/SetUgcChannelPlayMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).SetUgcChannelPlayMode(ctx, req.(*channel_play.SetUgcChannelPlayModeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_TypingStatusBroadcast_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.TypingStatusBroadcastReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).TypingStatusBroadcast(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/TypingStatusBroadcast",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).TypingStatusBroadcast(ctx, req.(*channel_play.TypingStatusBroadcastReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetChannelPlayModeGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetChannelPlayModeGuideReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetChannelPlayModeGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetChannelPlayModeGuide",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetChannelPlayModeGuide(ctx, req.(*channel_play.GetChannelPlayModeGuideReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_ReportDailyTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.ReportDailyTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).ReportDailyTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/ReportDailyTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).ReportDailyTask(ctx, req.(*channel_play.ReportDailyTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetCache(ctx, req.(*channel_play.GetCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_ShowTopicChannelTabList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(topic_channel.ShowTopicChannelTabListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).ShowTopicChannelTabList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/ShowTopicChannelTabList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).ShowTopicChannelTabList(ctx, req.(*topic_channel.ShowTopicChannelTabListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_HomePageHeadConfigEnterCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.HomePageHeadConfigEnterCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).HomePageHeadConfigEnterCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/HomePageHeadConfigEnterCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).HomePageHeadConfigEnterCheck(ctx, req.(*channel_play.HomePageHeadConfigEnterCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetChannelListGuideConfigs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetChannelListGuideConfigsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetChannelListGuideConfigs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetChannelListGuideConfigs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetChannelListGuideConfigs(ctx, req.(*channel_play.GetChannelListGuideConfigsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetTabInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetTabInfosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetTabInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetTabInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetTabInfos(ctx, req.(*channel_play.GetTabInfosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetRecommendGames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetRecommendGamesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetRecommendGames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetRecommendGames",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetRecommendGames(ctx, req.(*channel_play.GetRecommendGamesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_RefreshGameLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.RefreshGameLabelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).RefreshGameLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/RefreshGameLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).RefreshGameLabel(ctx, req.(*channel_play.RefreshGameLabelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetSupportTabList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetSupportTabListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetSupportTabList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetSupportTabList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetSupportTabList(ctx, req.(*channel_play.GetSupportTabListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_GetChannelMicVolSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.GetChannelMicVolSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).GetChannelMicVolSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/GetChannelMicVolSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).GetChannelMicVolSet(ctx, req.(*channel_play.GetChannelMicVolSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayLogic_SetChannelMicVol_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_play.SetChannelMicVolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayLogicServer).SetChannelMicVol(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.channel_play.ChannelPlayLogic/SetChannelMicVol",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayLogicServer).SetChannelMicVol(ctx, req.(*channel_play.SetChannelMicVolReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelPlayLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.channel_play.ChannelPlayLogic",
	HandlerType: (*ChannelPlayLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListTopicChannel",
			Handler:    _ChannelPlayLogic_ListTopicChannel_Handler,
		},
		{
			MethodName: "GetSecondaryFilter",
			Handler:    _ChannelPlayLogic_GetSecondaryFilter_Handler,
		},
		{
			MethodName: "GetSecondaryFilterByCategory",
			Handler:    _ChannelPlayLogic_GetSecondaryFilterByCategory_Handler,
		},
		{
			MethodName: "GetDefaultRoomNameList",
			Handler:    _ChannelPlayLogic_GetDefaultRoomNameList_Handler,
		},
		{
			MethodName: "GetGameHomePageDIYFilter",
			Handler:    _ChannelPlayLogic_GetGameHomePageDIYFilter_Handler,
		},
		{
			MethodName: "SetGameHomePageDIYFilter",
			Handler:    _ChannelPlayLogic_SetGameHomePageDIYFilter_Handler,
		},
		{
			MethodName: "GetGameHomePageFilter",
			Handler:    _ChannelPlayLogic_GetGameHomePageFilter_Handler,
		},
		{
			MethodName: "CreateHobbyChannel",
			Handler:    _ChannelPlayLogic_CreateHobbyChannel_Handler,
		},
		{
			MethodName: "PublishGangupChannel",
			Handler:    _ChannelPlayLogic_PublishGangupChannel_Handler,
		},
		{
			MethodName: "CancelGangupChannelPublish",
			Handler:    _ChannelPlayLogic_CancelGangupChannelPublish_Handler,
		},
		{
			MethodName: "GetHomePageHeadConfig",
			Handler:    _ChannelPlayLogic_GetHomePageHeadConfig_Handler,
		},
		{
			MethodName: "GetHotMiniGames",
			Handler:    _ChannelPlayLogic_GetHotMiniGames_Handler,
		},
		{
			MethodName: "GetQuickMiniGames",
			Handler:    _ChannelPlayLogic_GetQuickMiniGames_Handler,
		},
		{
			MethodName: "GetPlayQuestions",
			Handler:    _ChannelPlayLogic_GetPlayQuestions_Handler,
		},
		{
			MethodName: "GameInsertFlowConfig",
			Handler:    _ChannelPlayLogic_GameInsertFlowConfig_Handler,
		},
		{
			MethodName: "GetHomePageGuide",
			Handler:    _ChannelPlayLogic_GetHomePageGuide_Handler,
		},
		{
			MethodName: "GetMoreTabConfig",
			Handler:    _ChannelPlayLogic_GetMoreTabConfig_Handler,
		},
		{
			MethodName: "GetFilterItemByEntrance",
			Handler:    _ChannelPlayLogic_GetFilterItemByEntrance_Handler,
		},
		{
			MethodName: "SetDIYFilterByEntrance",
			Handler:    _ChannelPlayLogic_SetDIYFilterByEntrance_Handler,
		},
		{
			MethodName: "GetDIYFilterByEntrance",
			Handler:    _ChannelPlayLogic_GetDIYFilterByEntrance_Handler,
		},
		{
			MethodName: "GetNegativeFeedBackInRoom",
			Handler:    _ChannelPlayLogic_GetNegativeFeedBackInRoom_Handler,
		},
		{
			MethodName: "ReportNegativeFeedBackInRoom",
			Handler:    _ChannelPlayLogic_ReportNegativeFeedBackInRoom_Handler,
		},
		{
			MethodName: "GetPublishOptionGuide",
			Handler:    _ChannelPlayLogic_GetPublishOptionGuide_Handler,
		},
		{
			MethodName: "GetNewQuickMatchConfig",
			Handler:    _ChannelPlayLogic_GetNewQuickMatchConfig_Handler,
		},
		{
			MethodName: "GetTopicChannelCfgInfo",
			Handler:    _ChannelPlayLogic_GetTopicChannelCfgInfo_Handler,
		},
		{
			MethodName: "SetUgcChannelPlayMode",
			Handler:    _ChannelPlayLogic_SetUgcChannelPlayMode_Handler,
		},
		{
			MethodName: "TypingStatusBroadcast",
			Handler:    _ChannelPlayLogic_TypingStatusBroadcast_Handler,
		},
		{
			MethodName: "GetChannelPlayModeGuide",
			Handler:    _ChannelPlayLogic_GetChannelPlayModeGuide_Handler,
		},
		{
			MethodName: "ReportDailyTask",
			Handler:    _ChannelPlayLogic_ReportDailyTask_Handler,
		},
		{
			MethodName: "GetCache",
			Handler:    _ChannelPlayLogic_GetCache_Handler,
		},
		{
			MethodName: "ShowTopicChannelTabList",
			Handler:    _ChannelPlayLogic_ShowTopicChannelTabList_Handler,
		},
		{
			MethodName: "HomePageHeadConfigEnterCheck",
			Handler:    _ChannelPlayLogic_HomePageHeadConfigEnterCheck_Handler,
		},
		{
			MethodName: "GetChannelListGuideConfigs",
			Handler:    _ChannelPlayLogic_GetChannelListGuideConfigs_Handler,
		},
		{
			MethodName: "GetTabInfos",
			Handler:    _ChannelPlayLogic_GetTabInfos_Handler,
		},
		{
			MethodName: "GetRecommendGames",
			Handler:    _ChannelPlayLogic_GetRecommendGames_Handler,
		},
		{
			MethodName: "RefreshGameLabel",
			Handler:    _ChannelPlayLogic_RefreshGameLabel_Handler,
		},
		{
			MethodName: "GetSupportTabList",
			Handler:    _ChannelPlayLogic_GetSupportTabList_Handler,
		},
		{
			MethodName: "GetChannelMicVolSet",
			Handler:    _ChannelPlayLogic_GetChannelMicVolSet_Handler,
		},
		{
			MethodName: "SetChannelMicVol",
			Handler:    _ChannelPlayLogic_SetChannelMicVol_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/channel_play/grpc_channel_play.proto",
}

func init() {
	proto.RegisterFile("api/channel_play/grpc_channel_play.proto", fileDescriptor_grpc_channel_play_d5dd590e14c7fc9f)
}

var fileDescriptor_grpc_channel_play_d5dd590e14c7fc9f = []byte{
	// 1261 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x98, 0xcd, 0x6f, 0xdc, 0x44,
	0x14, 0xc0, 0xd9, 0x16, 0x41, 0x64, 0x0e, 0x94, 0x29, 0x34, 0x26, 0x6a, 0xc5, 0x2a, 0x24, 0x4d,
	0x9a, 0x12, 0x2f, 0x14, 0x71, 0x81, 0x5b, 0x36, 0x89, 0xb3, 0x52, 0x12, 0xd2, 0x75, 0x40, 0x6a,
	0x2f, 0xd1, 0xac, 0xf7, 0xc5, 0x6b, 0xc5, 0xeb, 0x71, 0xec, 0x59, 0xc2, 0x56, 0x42, 0x44, 0x41,
	0x42, 0x50, 0xc2, 0x57, 0x81, 0xb6, 0x7c, 0x43, 0xb9, 0xf0, 0xf5, 0x87, 0x71, 0xe4, 0x94, 0x73,
	0x35, 0xf6, 0x7a, 0x76, 0x6d, 0x3f, 0x7b, 0xbd, 0xb7, 0x38, 0xfe, 0xcd, 0xfc, 0xde, 0x7c, 0xbd,
	0x37, 0x5e, 0x65, 0x91, 0x7a, 0x76, 0xcd, 0xec, 0x50, 0xd7, 0x05, 0x67, 0xcf, 0x73, 0x68, 0xbf,
	0x66, 0xf9, 0x9e, 0xb9, 0x37, 0xfa, 0x1f, 0xcd, 0xf3, 0x19, 0x67, 0xe4, 0xa2, 0x45, 0x35, 0xea,
	0xd9, 0xda, 0xe8, 0xab, 0x99, 0x6a, 0xa2, 0xe9, 0xe0, 0x61, 0x59, 0x3c, 0xec, 0x45, 0xcd, 0x66,
	0x66, 0x3b, 0xac, 0xd5, 0xea, 0xc7, 0x1d, 0xd6, 0xc2, 0xa7, 0xe5, 0xb8, 0x55, 0xcc, 0x70, 0xe6,
	0xd9, 0x52, 0x5a, 0x4b, 0x3c, 0xc5, 0xcc, 0x15, 0x11, 0x28, 0xbc, 0xcf, 0xc1, 0x0d, 0x6c, 0xe6,
	0x0e, 0xff, 0x8a, 0x5e, 0xdf, 0xf8, 0x6f, 0x4e, 0xb9, 0x50, 0x8f, 0x5a, 0xec, 0x38, 0xb4, 0xbf,
	0xc9, 0x2c, 0xdb, 0x24, 0x1d, 0xe5, 0xc2, 0xa6, 0x1d, 0xf0, 0x5d, 0xd1, 0xdf, 0xe0, 0x25, 0x99,
	0xd3, 0x2c, 0x9a, 0x18, 0x83, 0x96, 0x46, 0x9a, 0x70, 0x38, 0x33, 0x5f, 0x82, 0x0a, 0xbc, 0xd9,
	0xa7, 0x4f, 0x8e, 0xab, 0xe7, 0xa7, 0x3e, 0x51, 0x89, 0xab, 0x10, 0x1d, 0xb8, 0x01, 0x26, 0x73,
	0xdb, 0xd4, 0xef, 0xaf, 0xdb, 0x0e, 0x07, 0x9f, 0x5c, 0xcd, 0xf4, 0x92, 0x85, 0x84, 0x6d, 0xa1,
	0x14, 0x27, 0x7d, 0x9f, 0xaa, 0xe4, 0xb4, 0xa2, 0x5c, 0xce, 0x32, 0x2b, 0xfd, 0x3a, 0xe5, 0x60,
	0x31, 0xbf, 0x4f, 0x5e, 0x2d, 0xd1, 0xe5, 0x10, 0x17, 0x41, 0xbc, 0x36, 0x61, 0x0b, 0x19, 0xce,
	0x5d, 0x95, 0xdc, 0x51, 0x2e, 0xe9, 0xc0, 0x57, 0x61, 0x9f, 0xf6, 0x1c, 0xde, 0x64, 0xac, 0xbb,
	0x4d, 0xbb, 0x20, 0x66, 0x8c, 0x2c, 0x61, 0xbd, 0x22, 0xa0, 0x88, 0xe0, 0x7a, 0x69, 0x56, 0xba,
	0x3f, 0x53, 0xc9, 0x47, 0x15, 0x45, 0xd5, 0x81, 0xeb, 0xb4, 0x0b, 0x1b, 0xac, 0x0b, 0x3b, 0xd4,
	0x82, 0xd5, 0xc6, 0xad, 0xc1, 0x0a, 0x2c, 0x8b, 0x2e, 0x13, 0x3b, 0x50, 0xcb, 0x63, 0x45, 0x04,
	0xda, 0x24, 0x78, 0xe0, 0xcd, 0x4e, 0x9d, 0x1c, 0x57, 0x9f, 0x9c, 0xba, 0x7f, 0x56, 0x09, 0xa3,
	0x30, 0x26, 0x88, 0xc2, 0x98, 0x2c, 0x0a, 0x63, 0x7c, 0x14, 0x0f, 0xce, 0x2a, 0xe4, 0x8e, 0xf2,
	0x42, 0x2a, 0xde, 0x41, 0x04, 0x4b, 0x63, 0x07, 0x36, 0xd4, 0x5f, 0x2f, 0xcd, 0x4a, 0xf7, 0xc3,
	0xb3, 0x0a, 0x39, 0x54, 0x48, 0xdd, 0x07, 0xca, 0x61, 0x43, 0x34, 0x8d, 0x8f, 0xdb, 0x42, 0xb6,
	0xb3, 0x2c, 0x25, 0xac, 0x8b, 0xe5, 0x40, 0xa9, 0x3c, 0x3d, 0xab, 0x10, 0xae, 0x3c, 0xbf, 0xd3,
	0x6b, 0x39, 0x76, 0xd0, 0xd1, 0xa9, 0x6b, 0xf5, 0xbc, 0x58, 0xba, 0x98, 0xd9, 0x48, 0x18, 0x26,
	0xac, 0xd7, 0x4a, 0x92, 0x72, 0xc3, 0x9d, 0xaa, 0xe4, 0xe3, 0x8a, 0x32, 0x53, 0xa7, 0xae, 0x09,
	0x4e, 0x02, 0x1a, 0x34, 0x24, 0x5a, 0xa6, 0xcb, 0x7c, 0x58, 0x84, 0x50, 0x9b, 0x88, 0x97, 0x81,
	0x7c, 0xae, 0x92, 0xc3, 0x70, 0xb5, 0xe3, 0x45, 0xd9, 0x00, 0xda, 0xae, 0x33, 0x77, 0xdf, 0xb6,
	0x90, 0xbc, 0x93, 0x85, 0xf0, 0xbc, 0x83, 0x71, 0x52, 0xf9, 0x85, 0x4a, 0x40, 0x79, 0x36, 0x54,
	0xf2, 0x2d, 0xdb, 0xb5, 0xc5, 0x96, 0x08, 0xc8, 0xcb, 0xd8, 0xa9, 0x1d, 0x25, 0x84, 0x69, 0x6e,
	0x3c, 0x24, 0x35, 0x5f, 0xaa, 0xe4, 0x40, 0x79, 0x4e, 0x07, 0x7e, 0xb3, 0x67, 0x9b, 0x07, 0x43,
	0xd1, 0x3c, 0xd6, 0x47, 0x92, 0x11, 0xaa, 0xab, 0x65, 0x30, 0x29, 0xfb, 0x4a, 0x15, 0x55, 0x42,
	0x07, 0x2e, 0xaa, 0xc6, 0xcd, 0x1e, 0x04, 0xdc, 0x66, 0x6e, 0x40, 0xd0, 0x78, 0x13, 0x08, 0x5e,
	0x25, 0xb2, 0x94, 0x34, 0x7d, 0xad, 0x8a, 0xfd, 0x2a, 0xfc, 0x0d, 0x37, 0x00, 0x9f, 0xaf, 0x3b,
	0xec, 0x68, 0xb0, 0x5e, 0xd9, 0xfd, 0x8a, 0x61, 0xf8, 0x7e, 0xc5, 0x49, 0x69, 0xbd, 0x17, 0x8f,
	0x2f, 0x5e, 0x59, 0xbd, 0x67, 0xb7, 0x81, 0xe4, 0xac, 0xc7, 0x08, 0x92, 0x3b, 0xbe, 0x14, 0x25,
	0x4d, 0xdf, 0xc4, 0xa6, 0x2d, 0xe6, 0xc3, 0x2e, 0x6d, 0x0d, 0xc6, 0x86, 0x9a, 0x12, 0x48, 0xae,
	0x29, 0x45, 0x49, 0xd3, 0xb7, 0x2a, 0xf9, 0x40, 0x99, 0xd6, 0x81, 0x47, 0x79, 0xa8, 0xc1, 0xa1,
	0xbb, 0xd2, 0x5f, 0x73, 0xb9, 0x2f, 0xce, 0x0d, 0x41, 0xab, 0x08, 0x46, 0x0a, 0xef, 0x2b, 0xe5,
	0x61, 0xa9, 0xff, 0x2e, 0xac, 0x77, 0x06, 0x70, 0x99, 0x85, 0x47, 0xec, 0xd9, 0x7a, 0x87, 0x83,
	0x78, 0xbd, 0xcb, 0x63, 0xa5, 0xfb, 0xbe, 0xac, 0xb5, 0xa5, 0xdc, 0xfa, 0x04, 0x6e, 0x7d, 0x9c,
	0xfb, 0x41, 0x58, 0x6b, 0x5f, 0xd4, 0x81, 0x6f, 0x83, 0x45, 0xb9, 0xfd, 0x1e, 0xac, 0x03, 0xb4,
	0x57, 0xa8, 0x79, 0xd0, 0x70, 0x45, 0x79, 0x8e, 0xca, 0x5c, 0xba, 0x4f, 0x9c, 0x95, 0x65, 0xae,
	0x2c, 0x2e, 0xa3, 0x78, 0x18, 0x5d, 0x7e, 0x9a, 0xe0, 0x31, 0x3f, 0x2f, 0x90, 0xec, 0xe5, 0xa7,
	0x08, 0xc7, 0x2f, 0x3f, 0xc5, 0x2d, 0x64, 0x38, 0xdf, 0xab, 0xe4, 0x28, 0x4c, 0xc3, 0x83, 0x0c,
	0xfd, 0xb6, 0x27, 0xce, 0x7d, 0x74, 0xc8, 0xae, 0xa1, 0xe9, 0x21, 0xc3, 0x09, 0xff, 0x52, 0x59,
	0x54, 0x8a, 0x7f, 0x88, 0x77, 0xc2, 0x36, 0x1c, 0x45, 0xd9, 0x8d, 0x72, 0xb3, 0x33, 0x38, 0x74,
	0x4b, 0xf8, 0xd4, 0x66, 0xc0, 0xdc, 0x9d, 0x80, 0xb2, 0xd2, 0xfd, 0x63, 0xec, 0x1e, 0xbd, 0x10,
	0xd7, 0xf7, 0xad, 0x86, 0xbb, 0xcf, 0x70, 0x37, 0x02, 0xe6, 0xba, 0x51, 0x56, 0xba, 0x7f, 0x0a,
	0x27, 0xdc, 0x00, 0xfe, 0x8e, 0x65, 0x8e, 0x5c, 0xf8, 0xb7, 0x18, 0x3a, 0xe1, 0x28, 0x87, 0x4f,
	0x78, 0x0e, 0x2a, 0xc5, 0x3f, 0x87, 0xe2, 0xdd, 0xbe, 0x67, 0xbb, 0x96, 0xc1, 0x29, 0xef, 0x05,
	0x2b, 0x3e, 0xa3, 0x6d, 0x93, 0x06, 0x1c, 0x11, 0xa3, 0x1c, 0x2e, 0xce, 0x41, 0xa5, 0xf8, 0x97,
	0x38, 0xdd, 0xa5, 0x62, 0x8b, 0x36, 0x19, 0x3a, 0x85, 0x18, 0x99, 0x9b, 0xee, 0x70, 0x58, 0xea,
	0x7f, 0x0d, 0xab, 0x7e, 0x74, 0x1c, 0x56, 0xa9, 0xed, 0xf4, 0x77, 0x69, 0x70, 0x80, 0x54, 0xfd,
	0x14, 0x81, 0x57, 0xfd, 0x0c, 0x24, 0x35, 0xbf, 0xa9, 0xa4, 0xa1, 0x4c, 0x89, 0x70, 0xa8, 0xd9,
	0x01, 0x72, 0x19, 0x8d, 0x54, 0xbc, 0x12, 0x1d, 0x5f, 0x29, 0x78, 0x1b, 0x78, 0xb3, 0x4f, 0x90,
	0x0f, 0x95, 0x69, 0xa3, 0xc3, 0x8e, 0x46, 0xb7, 0xd2, 0x2e, 0x6d, 0x85, 0x5f, 0x24, 0xe1, 0x1c,
	0x24, 0x3e, 0x31, 0xb5, 0x1c, 0x54, 0x98, 0x96, 0x27, 0xa0, 0xe5, 0x58, 0xfe, 0x9f, 0x0e, 0x73,
	0x54, 0xf6, 0x32, 0xb5, 0xe6, 0x72, 0xf0, 0xeb, 0x1d, 0x30, 0x0f, 0x90, 0x1c, 0x55, 0x84, 0xe3,
	0x39, 0xaa, 0xb8, 0x85, 0x0c, 0xe7, 0xf7, 0xe8, 0xce, 0x3a, 0x5c, 0x6a, 0x11, 0x6e, 0xb8, 0xcc,
	0x51, 0xa3, 0x80, 0x68, 0x05, 0xfb, 0x22, 0x0d, 0xe3, 0x77, 0xd6, 0x22, 0x5e, 0x06, 0xf2, 0x48,
	0x25, 0xb7, 0x94, 0x67, 0xc4, 0x11, 0xa7, 0x2d, 0x71, 0xac, 0x03, 0xf2, 0x12, 0x9a, 0x00, 0x06,
	0x6f, 0x85, 0xa9, 0x5a, 0x0c, 0xc8, 0xae, 0xff, 0x88, 0x2f, 0x8d, 0x4d, 0x30, 0x59, 0xb7, 0x0b,
	0x6e, 0xbb, 0xe0, 0xd2, 0x98, 0x64, 0x72, 0x2f, 0x8d, 0x69, 0x4c, 0xca, 0xfe, 0x0c, 0xaf, 0x3a,
	0x4d, 0xd8, 0xf7, 0x41, 0x7c, 0x29, 0x74, 0x61, 0x93, 0xb6, 0xd0, 0x9f, 0x16, 0xd2, 0x08, 0x7e,
	0xd5, 0xc9, 0x52, 0xd2, 0xf4, 0x57, 0x3c, 0x2c, 0xa3, 0xe7, 0x89, 0x93, 0x13, 0x6f, 0x62, 0x74,
	0x58, 0x49, 0x26, 0x77, 0x58, 0x69, 0x4c, 0xca, 0xfe, 0x16, 0x9f, 0x14, 0x17, 0x87, 0x2b, 0xb9,
	0x65, 0x9b, 0xef, 0x32, 0xc7, 0x00, 0x4e, 0x16, 0x0a, 0xd6, 0x5b, 0x52, 0xf2, 0x2b, 0x6e, 0x3c,
	0x28, 0x95, 0xff, 0x84, 0x33, 0x69, 0xa4, 0x18, 0x64, 0x26, 0xd3, 0x08, 0x3e, 0x93, 0x59, 0x4a,
	0x9a, 0xfe, 0x55, 0x67, 0xd6, 0x4e, 0x8e, 0xab, 0x64, 0xf4, 0x37, 0xaa, 0x65, 0x87, 0x59, 0xb6,
	0x79, 0xf7, 0xb8, 0x7a, 0xce, 0x62, 0xf7, 0x8e, 0xab, 0xf3, 0xb5, 0xf0, 0x39, 0xf5, 0x31, 0x96,
	0xfa, 0x51, 0xa9, 0xb6, 0x72, 0x5b, 0xb9, 0x64, 0xb2, 0xae, 0x76, 0xd8, 0x3b, 0xa2, 0xae, 0xc6,
	0x79, 0xf4, 0x03, 0x94, 0x46, 0x3d, 0xfb, 0xf6, 0x9b, 0x16, 0x73, 0xa8, 0x6b, 0x69, 0x6f, 0xdc,
	0xe0, 0x5c, 0x33, 0x59, 0xb7, 0x16, 0xbe, 0x32, 0x99, 0x53, 0xa3, 0x9e, 0x57, 0x4b, 0xff, 0xd2,
	0xf6, 0xd6, 0xe8, 0xc3, 0xa3, 0x73, 0xe7, 0x9b, 0x3b, 0xf5, 0xd6, 0x53, 0x61, 0x8b, 0xd7, 0x1f,
	0x07, 0x00, 0x00, 0xff, 0xff, 0x0b, 0x92, 0x1f, 0x19, 0x97, 0x13, 0x00, 0x00,
}
