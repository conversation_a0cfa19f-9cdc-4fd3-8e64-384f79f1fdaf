// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/chat_bot/grpc_chat_bot.proto

package chat_bot // import "golang.52tt.com/protocol/app/api/chat_bot"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import chat_bot_logic "golang.52tt.com/protocol/app/chat-bot-logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChatBotLogicClient is the client API for ChatBotLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChatBotLogicClient interface {
	GetAIPartnerEntrance(ctx context.Context, in *chat_bot_logic.GetAIPartnerEntranceRequest, opts ...grpc.CallOption) (*chat_bot_logic.GetAIPartnerEntranceResponse, error)
	GetAIPartnerEntranceV2(ctx context.Context, in *chat_bot_logic.GetAIPartnerEntranceV2Request, opts ...grpc.CallOption) (*chat_bot_logic.GetAIPartnerEntranceV2Response, error)
	GetAIPartnerEntranceList(ctx context.Context, in *chat_bot_logic.GetAIPartnerEntranceListRequest, opts ...grpc.CallOption) (*chat_bot_logic.GetAIPartnerEntranceListResponse, error)
	GetAIRoleInteractiveConfig(ctx context.Context, in *chat_bot_logic.GetAIRoleInteractiveConfigRequest, opts ...grpc.CallOption) (*chat_bot_logic.GetAIRoleInteractiveConfigResponse, error)
	ReportAIPetBehavior(ctx context.Context, in *chat_bot_logic.ReportAIPetBehaviorRequest, opts ...grpc.CallOption) (*chat_bot_logic.ReportAIPetBehaviorResponse, error)
	BatchGetAIAccount(ctx context.Context, in *chat_bot_logic.BatchGetAIAccountRequest, opts ...grpc.CallOption) (*chat_bot_logic.BatchGetAIAccountResponse, error)
}

type chatBotLogicClient struct {
	cc *grpc.ClientConn
}

func NewChatBotLogicClient(cc *grpc.ClientConn) ChatBotLogicClient {
	return &chatBotLogicClient{cc}
}

func (c *chatBotLogicClient) GetAIPartnerEntrance(ctx context.Context, in *chat_bot_logic.GetAIPartnerEntranceRequest, opts ...grpc.CallOption) (*chat_bot_logic.GetAIPartnerEntranceResponse, error) {
	out := new(chat_bot_logic.GetAIPartnerEntranceResponse)
	err := c.cc.Invoke(ctx, "/ga.api.chat_bot.ChatBotLogic/GetAIPartnerEntrance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotLogicClient) GetAIPartnerEntranceV2(ctx context.Context, in *chat_bot_logic.GetAIPartnerEntranceV2Request, opts ...grpc.CallOption) (*chat_bot_logic.GetAIPartnerEntranceV2Response, error) {
	out := new(chat_bot_logic.GetAIPartnerEntranceV2Response)
	err := c.cc.Invoke(ctx, "/ga.api.chat_bot.ChatBotLogic/GetAIPartnerEntranceV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotLogicClient) GetAIPartnerEntranceList(ctx context.Context, in *chat_bot_logic.GetAIPartnerEntranceListRequest, opts ...grpc.CallOption) (*chat_bot_logic.GetAIPartnerEntranceListResponse, error) {
	out := new(chat_bot_logic.GetAIPartnerEntranceListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.chat_bot.ChatBotLogic/GetAIPartnerEntranceList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotLogicClient) GetAIRoleInteractiveConfig(ctx context.Context, in *chat_bot_logic.GetAIRoleInteractiveConfigRequest, opts ...grpc.CallOption) (*chat_bot_logic.GetAIRoleInteractiveConfigResponse, error) {
	out := new(chat_bot_logic.GetAIRoleInteractiveConfigResponse)
	err := c.cc.Invoke(ctx, "/ga.api.chat_bot.ChatBotLogic/GetAIRoleInteractiveConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotLogicClient) ReportAIPetBehavior(ctx context.Context, in *chat_bot_logic.ReportAIPetBehaviorRequest, opts ...grpc.CallOption) (*chat_bot_logic.ReportAIPetBehaviorResponse, error) {
	out := new(chat_bot_logic.ReportAIPetBehaviorResponse)
	err := c.cc.Invoke(ctx, "/ga.api.chat_bot.ChatBotLogic/ReportAIPetBehavior", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatBotLogicClient) BatchGetAIAccount(ctx context.Context, in *chat_bot_logic.BatchGetAIAccountRequest, opts ...grpc.CallOption) (*chat_bot_logic.BatchGetAIAccountResponse, error) {
	out := new(chat_bot_logic.BatchGetAIAccountResponse)
	err := c.cc.Invoke(ctx, "/ga.api.chat_bot.ChatBotLogic/BatchGetAIAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChatBotLogicServer is the server API for ChatBotLogic service.
type ChatBotLogicServer interface {
	GetAIPartnerEntrance(context.Context, *chat_bot_logic.GetAIPartnerEntranceRequest) (*chat_bot_logic.GetAIPartnerEntranceResponse, error)
	GetAIPartnerEntranceV2(context.Context, *chat_bot_logic.GetAIPartnerEntranceV2Request) (*chat_bot_logic.GetAIPartnerEntranceV2Response, error)
	GetAIPartnerEntranceList(context.Context, *chat_bot_logic.GetAIPartnerEntranceListRequest) (*chat_bot_logic.GetAIPartnerEntranceListResponse, error)
	GetAIRoleInteractiveConfig(context.Context, *chat_bot_logic.GetAIRoleInteractiveConfigRequest) (*chat_bot_logic.GetAIRoleInteractiveConfigResponse, error)
	ReportAIPetBehavior(context.Context, *chat_bot_logic.ReportAIPetBehaviorRequest) (*chat_bot_logic.ReportAIPetBehaviorResponse, error)
	BatchGetAIAccount(context.Context, *chat_bot_logic.BatchGetAIAccountRequest) (*chat_bot_logic.BatchGetAIAccountResponse, error)
}

func RegisterChatBotLogicServer(s *grpc.Server, srv ChatBotLogicServer) {
	s.RegisterService(&_ChatBotLogic_serviceDesc, srv)
}

func _ChatBotLogic_GetAIPartnerEntrance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(chat_bot_logic.GetAIPartnerEntranceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotLogicServer).GetAIPartnerEntrance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.chat_bot.ChatBotLogic/GetAIPartnerEntrance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotLogicServer).GetAIPartnerEntrance(ctx, req.(*chat_bot_logic.GetAIPartnerEntranceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBotLogic_GetAIPartnerEntranceV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(chat_bot_logic.GetAIPartnerEntranceV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotLogicServer).GetAIPartnerEntranceV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.chat_bot.ChatBotLogic/GetAIPartnerEntranceV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotLogicServer).GetAIPartnerEntranceV2(ctx, req.(*chat_bot_logic.GetAIPartnerEntranceV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBotLogic_GetAIPartnerEntranceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(chat_bot_logic.GetAIPartnerEntranceListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotLogicServer).GetAIPartnerEntranceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.chat_bot.ChatBotLogic/GetAIPartnerEntranceList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotLogicServer).GetAIPartnerEntranceList(ctx, req.(*chat_bot_logic.GetAIPartnerEntranceListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBotLogic_GetAIRoleInteractiveConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(chat_bot_logic.GetAIRoleInteractiveConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotLogicServer).GetAIRoleInteractiveConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.chat_bot.ChatBotLogic/GetAIRoleInteractiveConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotLogicServer).GetAIRoleInteractiveConfig(ctx, req.(*chat_bot_logic.GetAIRoleInteractiveConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBotLogic_ReportAIPetBehavior_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(chat_bot_logic.ReportAIPetBehaviorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotLogicServer).ReportAIPetBehavior(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.chat_bot.ChatBotLogic/ReportAIPetBehavior",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotLogicServer).ReportAIPetBehavior(ctx, req.(*chat_bot_logic.ReportAIPetBehaviorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatBotLogic_BatchGetAIAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(chat_bot_logic.BatchGetAIAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatBotLogicServer).BatchGetAIAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.chat_bot.ChatBotLogic/BatchGetAIAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatBotLogicServer).BatchGetAIAccount(ctx, req.(*chat_bot_logic.BatchGetAIAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChatBotLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.chat_bot.ChatBotLogic",
	HandlerType: (*ChatBotLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAIPartnerEntrance",
			Handler:    _ChatBotLogic_GetAIPartnerEntrance_Handler,
		},
		{
			MethodName: "GetAIPartnerEntranceV2",
			Handler:    _ChatBotLogic_GetAIPartnerEntranceV2_Handler,
		},
		{
			MethodName: "GetAIPartnerEntranceList",
			Handler:    _ChatBotLogic_GetAIPartnerEntranceList_Handler,
		},
		{
			MethodName: "GetAIRoleInteractiveConfig",
			Handler:    _ChatBotLogic_GetAIRoleInteractiveConfig_Handler,
		},
		{
			MethodName: "ReportAIPetBehavior",
			Handler:    _ChatBotLogic_ReportAIPetBehavior_Handler,
		},
		{
			MethodName: "BatchGetAIAccount",
			Handler:    _ChatBotLogic_BatchGetAIAccount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/chat_bot/grpc_chat_bot.proto",
}

func init() {
	proto.RegisterFile("api/chat_bot/grpc_chat_bot.proto", fileDescriptor_grpc_chat_bot_b9944975f5722dd6)
}

var fileDescriptor_grpc_chat_bot_b9944975f5722dd6 = []byte{
	// 416 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x94, 0xcf, 0x8b, 0xd4, 0x30,
	0x14, 0xc7, 0xe9, 0x2e, 0xac, 0x12, 0x44, 0x31, 0x2e, 0x8b, 0x14, 0x84, 0xa2, 0x47, 0x9d, 0x64,
	0xed, 0xba, 0x27, 0x4f, 0xdb, 0x41, 0x64, 0x61, 0x0f, 0x4b, 0x0f, 0x7b, 0xf0, 0x32, 0x64, 0x42,
	0x4c, 0x03, 0xdd, 0xbc, 0x4c, 0xfa, 0x66, 0xc6, 0x8b, 0x50, 0xe6, 0xe0, 0x41, 0x3c, 0x0d, 0xfe,
	0xbc, 0xfa, 0xd7, 0xf9, 0x67, 0x48, 0x3b, 0x06, 0xa7, 0xda, 0x85, 0xce, 0xed, 0xa5, 0xf9, 0x7c,
	0x7f, 0xf4, 0xf0, 0x42, 0x12, 0xe1, 0x0c, 0x97, 0x85, 0xc0, 0xc9, 0x14, 0x90, 0x6b, 0xef, 0xe4,
	0x24, 0x9c, 0x98, 0xf3, 0x80, 0x40, 0xef, 0x69, 0xc1, 0x84, 0x33, 0x2c, 0x7c, 0x8e, 0x9f, 0x84,
	0x69, 0x52, 0x82, 0x36, 0x92, 0x77, 0x8f, 0x1b, 0x55, 0xfc, 0xa8, 0xf1, 0x55, 0xef, 0x50, 0xd9,
	0xca, 0x80, 0xfd, 0x3b, 0x6d, 0xae, 0xd3, 0x5f, 0x07, 0xe4, 0xce, 0xb8, 0x10, 0x98, 0x01, 0x5e,
	0x34, 0x2a, 0x5a, 0x47, 0xe4, 0xf0, 0xb5, 0xc2, 0xb3, 0xf3, 0x4b, 0xe1, 0xd1, 0x2a, 0xff, 0xca,
	0xa2, 0x17, 0x56, 0x2a, 0xca, 0x98, 0x16, 0xec, 0x9f, 0x88, 0x3e, 0x30, 0x57, 0xb3, 0xb9, 0xaa,
	0x30, 0xe6, 0x83, 0xf9, 0xca, 0x81, 0xad, 0xd4, 0xe3, 0x5b, 0xab, 0x3a, 0xd9, 0xbf, 0xfd, 0x99,
	0xd0, 0x0f, 0x11, 0x39, 0xea, 0x23, 0xaf, 0x52, 0x7a, 0x3c, 0xd0, 0xf4, 0x2a, 0x0d, 0x35, 0x9e,
	0xef, 0xa0, 0xe8, 0x14, 0xf9, 0x42, 0xe8, 0xa7, 0x88, 0x3c, 0xec, 0x63, 0x2f, 0x4c, 0x85, 0x34,
	0x1d, 0x68, 0xdc, 0xc0, 0xa1, 0xcc, 0xc9, 0x4e, 0x9a, 0x4e, 0x9d, 0xaf, 0x84, 0xae, 0x23, 0x12,
	0xb7, 0x74, 0x0e, 0xa5, 0x3a, 0xb7, 0xa8, 0xbc, 0x90, 0x68, 0x16, 0x6a, 0x0c, 0xf6, 0xad, 0xd1,
	0xf4, 0xc5, 0x4d, 0xe6, 0xbd, 0x78, 0xa8, 0x74, 0xba, 0xa3, 0xaa, 0x53, 0xea, 0x1b, 0xa1, 0xef,
	0xc9, 0x83, 0x5c, 0x39, 0xf0, 0xcd, 0x4f, 0x28, 0xcc, 0x54, 0x21, 0x16, 0x06, 0x3c, 0x1d, 0xf5,
	0xd8, 0xf6, 0x70, 0xa1, 0x05, 0x1b, 0x8a, 0x77, 0xe2, 0xbf, 0x13, 0xba, 0x24, 0xf7, 0x33, 0x81,
	0xb2, 0x68, 0x2b, 0x9f, 0x49, 0x09, 0x73, 0x8b, 0xf4, 0x69, 0x8f, 0xdb, 0x7f, 0x54, 0x88, 0x7e,
	0x36, 0x0c, 0xee, 0x04, 0xff, 0x20, 0xf1, 0xf1, 0xaa, 0x4e, 0xee, 0x36, 0xb2, 0xd1, 0x14, 0x70,
	0xd4, 0xca, 0x3e, 0xd6, 0xc9, 0x9e, 0x86, 0x75, 0x9d, 0x1c, 0xf2, 0x8d, 0xcd, 0xf6, 0x62, 0xf1,
	0x2c, 0x27, 0x47, 0x12, 0xae, 0xd9, 0x6c, 0xbe, 0x14, 0x96, 0xe1, 0x9f, 0xad, 0x6e, 0x36, 0xfa,
	0x4d, 0xaa, 0xa1, 0x14, 0x56, 0xb3, 0xd3, 0x14, 0x91, 0x49, 0xb8, 0xe6, 0xed, 0x95, 0x84, 0x92,
	0x0b, 0xe7, 0xf8, 0xf6, 0xc3, 0xf0, 0x32, 0x0c, 0x3f, 0xf7, 0xf6, 0xf3, 0xcb, 0xf1, 0xf4, 0xa0,
	0x25, 0x4f, 0x7e, 0x07, 0x00, 0x00, 0xff, 0xff, 0xa8, 0xeb, 0x47, 0x1d, 0x3e, 0x04, 0x00, 0x00,
}
