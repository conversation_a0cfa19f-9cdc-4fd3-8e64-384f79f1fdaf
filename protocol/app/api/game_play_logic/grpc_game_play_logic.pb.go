// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/game_play_logic/grpc_game_play_logic.proto

package game_play_logic // import "golang.52tt.com/protocol/app/api/game_play_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import game_play_logic "golang.52tt.com/protocol/app/game-play-logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GamePlayLogicClient is the client API for GamePlayLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GamePlayLogicClient interface {
	// 用户对开黑用户评价触发上报
	UserGameRateReport(ctx context.Context, in *game_play_logic.UserGameRateReportReq, opts ...grpc.CallOption) (*game_play_logic.UserGameRateReportResp, error)
	// IM页面获取未评价的数量，展示红点
	GetUserNotRateCount(ctx context.Context, in *game_play_logic.GetUserNotRateCountReq, opts ...grpc.CallOption) (*game_play_logic.GetUserNotRateCountResp, error)
	// 获取IM聊天页首次触发上报是否满足下发评价信息的条件
	SetFirstChatToken(ctx context.Context, in *game_play_logic.SetFirstChatTokenReq, opts ...grpc.CallOption) (*game_play_logic.SetFirstChatTokenResp, error)
	// 获取互评标签和信誉分信息
	GetRateReputationInfo(ctx context.Context, in *game_play_logic.GetRateReputationInfoReq, opts ...grpc.CallOption) (*game_play_logic.GetRateReputationInfoResp, error)
	// 查询用户获客口径和AB实验结果
	GetUserAcquisitionAndABTestResult(ctx context.Context, in *game_play_logic.GetUserAcquisitionAndABTestResultReq, opts ...grpc.CallOption) (*game_play_logic.GetUserAcquisitionAndABTestResultResp, error)
	// 获取回归弹窗
	GetRecallPopUp(ctx context.Context, in *game_play_logic.GetRecallPopUpReq, opts ...grpc.CallOption) (*game_play_logic.GetRecallPopUpResp, error)
	// 提交回归弹窗
	SubmitRecallPopUp(ctx context.Context, in *game_play_logic.SubmitRecallPopUpReq, opts ...grpc.CallOption) (*game_play_logic.SubmitRecallPopUpResp, error)
	// 获取组队弹窗
	GetRecallTeamUp(ctx context.Context, in *game_play_logic.GetRecallTeamUpReq, opts ...grpc.CallOption) (*game_play_logic.GetRecallTeamUpResp, error)
	// 提交组队弹窗
	SubmitRecallTeamUp(ctx context.Context, in *game_play_logic.SubmitRecallTeamUpReq, opts ...grpc.CallOption) (*game_play_logic.SubmitRecallTeamUpResp, error)
	// 音频转换文本asr
	TransAudioToText(ctx context.Context, in *game_play_logic.TransAudioToTextReq, opts ...grpc.CallOption) (*game_play_logic.TransAudioToTextResp, error)
	// 注册页，开黑配置选项
	GetRegisterPageConfigs(ctx context.Context, in *game_play_logic.GetRegisterPageConfigsReq, opts ...grpc.CallOption) (*game_play_logic.GetRegisterPageConfigsResp, error)
	// 保存游戏时间信息
	SaveGameTime(ctx context.Context, in *game_play_logic.SaveGameTimeRequest, opts ...grpc.CallOption) (*game_play_logic.SaveGameTimeResponse, error)
	// 获取游戏时间信息
	GetGameTime(ctx context.Context, in *game_play_logic.GetGameTimeRequest, opts ...grpc.CallOption) (*game_play_logic.GetGameTimeResponse, error)
	// 设置进房提醒
	SetEnterRoomNotify(ctx context.Context, in *game_play_logic.SetEnterRoomNotifyRequest, opts ...grpc.CallOption) (*game_play_logic.SetEnterRoomNotifyResponse, error)
	// 检查用户是否在线/在房
	CheckUserInRoom(ctx context.Context, in *game_play_logic.CheckUserInRoomReq, opts ...grpc.CallOption) (*game_play_logic.CheckUserInRoomResp, error)
	// 获取游戏时间列表
	GetGameTimeList(ctx context.Context, in *game_play_logic.GetGameTimeListRequest, opts ...grpc.CallOption) (*game_play_logic.GetGameTimeListResponse, error)
	FastPCFeedback(ctx context.Context, in *game_play_logic.FastPCFeedbackReq, opts ...grpc.CallOption) (*game_play_logic.FastPCFeedbackResp, error)
}

type gamePlayLogicClient struct {
	cc *grpc.ClientConn
}

func NewGamePlayLogicClient(cc *grpc.ClientConn) GamePlayLogicClient {
	return &gamePlayLogicClient{cc}
}

func (c *gamePlayLogicClient) UserGameRateReport(ctx context.Context, in *game_play_logic.UserGameRateReportReq, opts ...grpc.CallOption) (*game_play_logic.UserGameRateReportResp, error) {
	out := new(game_play_logic.UserGameRateReportResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/UserGameRateReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLogicClient) GetUserNotRateCount(ctx context.Context, in *game_play_logic.GetUserNotRateCountReq, opts ...grpc.CallOption) (*game_play_logic.GetUserNotRateCountResp, error) {
	out := new(game_play_logic.GetUserNotRateCountResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/GetUserNotRateCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLogicClient) SetFirstChatToken(ctx context.Context, in *game_play_logic.SetFirstChatTokenReq, opts ...grpc.CallOption) (*game_play_logic.SetFirstChatTokenResp, error) {
	out := new(game_play_logic.SetFirstChatTokenResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/SetFirstChatToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLogicClient) GetRateReputationInfo(ctx context.Context, in *game_play_logic.GetRateReputationInfoReq, opts ...grpc.CallOption) (*game_play_logic.GetRateReputationInfoResp, error) {
	out := new(game_play_logic.GetRateReputationInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/GetRateReputationInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLogicClient) GetUserAcquisitionAndABTestResult(ctx context.Context, in *game_play_logic.GetUserAcquisitionAndABTestResultReq, opts ...grpc.CallOption) (*game_play_logic.GetUserAcquisitionAndABTestResultResp, error) {
	out := new(game_play_logic.GetUserAcquisitionAndABTestResultResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/GetUserAcquisitionAndABTestResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLogicClient) GetRecallPopUp(ctx context.Context, in *game_play_logic.GetRecallPopUpReq, opts ...grpc.CallOption) (*game_play_logic.GetRecallPopUpResp, error) {
	out := new(game_play_logic.GetRecallPopUpResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/GetRecallPopUp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLogicClient) SubmitRecallPopUp(ctx context.Context, in *game_play_logic.SubmitRecallPopUpReq, opts ...grpc.CallOption) (*game_play_logic.SubmitRecallPopUpResp, error) {
	out := new(game_play_logic.SubmitRecallPopUpResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/SubmitRecallPopUp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLogicClient) GetRecallTeamUp(ctx context.Context, in *game_play_logic.GetRecallTeamUpReq, opts ...grpc.CallOption) (*game_play_logic.GetRecallTeamUpResp, error) {
	out := new(game_play_logic.GetRecallTeamUpResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/GetRecallTeamUp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLogicClient) SubmitRecallTeamUp(ctx context.Context, in *game_play_logic.SubmitRecallTeamUpReq, opts ...grpc.CallOption) (*game_play_logic.SubmitRecallTeamUpResp, error) {
	out := new(game_play_logic.SubmitRecallTeamUpResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/SubmitRecallTeamUp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLogicClient) TransAudioToText(ctx context.Context, in *game_play_logic.TransAudioToTextReq, opts ...grpc.CallOption) (*game_play_logic.TransAudioToTextResp, error) {
	out := new(game_play_logic.TransAudioToTextResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/TransAudioToText", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLogicClient) GetRegisterPageConfigs(ctx context.Context, in *game_play_logic.GetRegisterPageConfigsReq, opts ...grpc.CallOption) (*game_play_logic.GetRegisterPageConfigsResp, error) {
	out := new(game_play_logic.GetRegisterPageConfigsResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/GetRegisterPageConfigs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLogicClient) SaveGameTime(ctx context.Context, in *game_play_logic.SaveGameTimeRequest, opts ...grpc.CallOption) (*game_play_logic.SaveGameTimeResponse, error) {
	out := new(game_play_logic.SaveGameTimeResponse)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/SaveGameTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLogicClient) GetGameTime(ctx context.Context, in *game_play_logic.GetGameTimeRequest, opts ...grpc.CallOption) (*game_play_logic.GetGameTimeResponse, error) {
	out := new(game_play_logic.GetGameTimeResponse)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/GetGameTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLogicClient) SetEnterRoomNotify(ctx context.Context, in *game_play_logic.SetEnterRoomNotifyRequest, opts ...grpc.CallOption) (*game_play_logic.SetEnterRoomNotifyResponse, error) {
	out := new(game_play_logic.SetEnterRoomNotifyResponse)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/SetEnterRoomNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLogicClient) CheckUserInRoom(ctx context.Context, in *game_play_logic.CheckUserInRoomReq, opts ...grpc.CallOption) (*game_play_logic.CheckUserInRoomResp, error) {
	out := new(game_play_logic.CheckUserInRoomResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/CheckUserInRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLogicClient) GetGameTimeList(ctx context.Context, in *game_play_logic.GetGameTimeListRequest, opts ...grpc.CallOption) (*game_play_logic.GetGameTimeListResponse, error) {
	out := new(game_play_logic.GetGameTimeListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/GetGameTimeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLogicClient) FastPCFeedback(ctx context.Context, in *game_play_logic.FastPCFeedbackReq, opts ...grpc.CallOption) (*game_play_logic.FastPCFeedbackResp, error) {
	out := new(game_play_logic.FastPCFeedbackResp)
	err := c.cc.Invoke(ctx, "/ga.api.game_play_logic.GamePlayLogic/FastPCFeedback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GamePlayLogicServer is the server API for GamePlayLogic service.
type GamePlayLogicServer interface {
	// 用户对开黑用户评价触发上报
	UserGameRateReport(context.Context, *game_play_logic.UserGameRateReportReq) (*game_play_logic.UserGameRateReportResp, error)
	// IM页面获取未评价的数量，展示红点
	GetUserNotRateCount(context.Context, *game_play_logic.GetUserNotRateCountReq) (*game_play_logic.GetUserNotRateCountResp, error)
	// 获取IM聊天页首次触发上报是否满足下发评价信息的条件
	SetFirstChatToken(context.Context, *game_play_logic.SetFirstChatTokenReq) (*game_play_logic.SetFirstChatTokenResp, error)
	// 获取互评标签和信誉分信息
	GetRateReputationInfo(context.Context, *game_play_logic.GetRateReputationInfoReq) (*game_play_logic.GetRateReputationInfoResp, error)
	// 查询用户获客口径和AB实验结果
	GetUserAcquisitionAndABTestResult(context.Context, *game_play_logic.GetUserAcquisitionAndABTestResultReq) (*game_play_logic.GetUserAcquisitionAndABTestResultResp, error)
	// 获取回归弹窗
	GetRecallPopUp(context.Context, *game_play_logic.GetRecallPopUpReq) (*game_play_logic.GetRecallPopUpResp, error)
	// 提交回归弹窗
	SubmitRecallPopUp(context.Context, *game_play_logic.SubmitRecallPopUpReq) (*game_play_logic.SubmitRecallPopUpResp, error)
	// 获取组队弹窗
	GetRecallTeamUp(context.Context, *game_play_logic.GetRecallTeamUpReq) (*game_play_logic.GetRecallTeamUpResp, error)
	// 提交组队弹窗
	SubmitRecallTeamUp(context.Context, *game_play_logic.SubmitRecallTeamUpReq) (*game_play_logic.SubmitRecallTeamUpResp, error)
	// 音频转换文本asr
	TransAudioToText(context.Context, *game_play_logic.TransAudioToTextReq) (*game_play_logic.TransAudioToTextResp, error)
	// 注册页，开黑配置选项
	GetRegisterPageConfigs(context.Context, *game_play_logic.GetRegisterPageConfigsReq) (*game_play_logic.GetRegisterPageConfigsResp, error)
	// 保存游戏时间信息
	SaveGameTime(context.Context, *game_play_logic.SaveGameTimeRequest) (*game_play_logic.SaveGameTimeResponse, error)
	// 获取游戏时间信息
	GetGameTime(context.Context, *game_play_logic.GetGameTimeRequest) (*game_play_logic.GetGameTimeResponse, error)
	// 设置进房提醒
	SetEnterRoomNotify(context.Context, *game_play_logic.SetEnterRoomNotifyRequest) (*game_play_logic.SetEnterRoomNotifyResponse, error)
	// 检查用户是否在线/在房
	CheckUserInRoom(context.Context, *game_play_logic.CheckUserInRoomReq) (*game_play_logic.CheckUserInRoomResp, error)
	// 获取游戏时间列表
	GetGameTimeList(context.Context, *game_play_logic.GetGameTimeListRequest) (*game_play_logic.GetGameTimeListResponse, error)
	FastPCFeedback(context.Context, *game_play_logic.FastPCFeedbackReq) (*game_play_logic.FastPCFeedbackResp, error)
}

func RegisterGamePlayLogicServer(s *grpc.Server, srv GamePlayLogicServer) {
	s.RegisterService(&_GamePlayLogic_serviceDesc, srv)
}

func _GamePlayLogic_UserGameRateReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.UserGameRateReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).UserGameRateReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/UserGameRateReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).UserGameRateReport(ctx, req.(*game_play_logic.UserGameRateReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLogic_GetUserNotRateCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.GetUserNotRateCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).GetUserNotRateCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/GetUserNotRateCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).GetUserNotRateCount(ctx, req.(*game_play_logic.GetUserNotRateCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLogic_SetFirstChatToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.SetFirstChatTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).SetFirstChatToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/SetFirstChatToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).SetFirstChatToken(ctx, req.(*game_play_logic.SetFirstChatTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLogic_GetRateReputationInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.GetRateReputationInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).GetRateReputationInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/GetRateReputationInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).GetRateReputationInfo(ctx, req.(*game_play_logic.GetRateReputationInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLogic_GetUserAcquisitionAndABTestResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.GetUserAcquisitionAndABTestResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).GetUserAcquisitionAndABTestResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/GetUserAcquisitionAndABTestResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).GetUserAcquisitionAndABTestResult(ctx, req.(*game_play_logic.GetUserAcquisitionAndABTestResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLogic_GetRecallPopUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.GetRecallPopUpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).GetRecallPopUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/GetRecallPopUp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).GetRecallPopUp(ctx, req.(*game_play_logic.GetRecallPopUpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLogic_SubmitRecallPopUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.SubmitRecallPopUpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).SubmitRecallPopUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/SubmitRecallPopUp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).SubmitRecallPopUp(ctx, req.(*game_play_logic.SubmitRecallPopUpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLogic_GetRecallTeamUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.GetRecallTeamUpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).GetRecallTeamUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/GetRecallTeamUp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).GetRecallTeamUp(ctx, req.(*game_play_logic.GetRecallTeamUpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLogic_SubmitRecallTeamUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.SubmitRecallTeamUpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).SubmitRecallTeamUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/SubmitRecallTeamUp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).SubmitRecallTeamUp(ctx, req.(*game_play_logic.SubmitRecallTeamUpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLogic_TransAudioToText_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.TransAudioToTextReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).TransAudioToText(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/TransAudioToText",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).TransAudioToText(ctx, req.(*game_play_logic.TransAudioToTextReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLogic_GetRegisterPageConfigs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.GetRegisterPageConfigsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).GetRegisterPageConfigs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/GetRegisterPageConfigs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).GetRegisterPageConfigs(ctx, req.(*game_play_logic.GetRegisterPageConfigsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLogic_SaveGameTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.SaveGameTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).SaveGameTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/SaveGameTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).SaveGameTime(ctx, req.(*game_play_logic.SaveGameTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLogic_GetGameTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.GetGameTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).GetGameTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/GetGameTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).GetGameTime(ctx, req.(*game_play_logic.GetGameTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLogic_SetEnterRoomNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.SetEnterRoomNotifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).SetEnterRoomNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/SetEnterRoomNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).SetEnterRoomNotify(ctx, req.(*game_play_logic.SetEnterRoomNotifyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLogic_CheckUserInRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.CheckUserInRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).CheckUserInRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/CheckUserInRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).CheckUserInRoom(ctx, req.(*game_play_logic.CheckUserInRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLogic_GetGameTimeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.GetGameTimeListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).GetGameTimeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/GetGameTimeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).GetGameTimeList(ctx, req.(*game_play_logic.GetGameTimeListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLogic_FastPCFeedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_play_logic.FastPCFeedbackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLogicServer).FastPCFeedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.game_play_logic.GamePlayLogic/FastPCFeedback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLogicServer).FastPCFeedback(ctx, req.(*game_play_logic.FastPCFeedbackReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GamePlayLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.game_play_logic.GamePlayLogic",
	HandlerType: (*GamePlayLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UserGameRateReport",
			Handler:    _GamePlayLogic_UserGameRateReport_Handler,
		},
		{
			MethodName: "GetUserNotRateCount",
			Handler:    _GamePlayLogic_GetUserNotRateCount_Handler,
		},
		{
			MethodName: "SetFirstChatToken",
			Handler:    _GamePlayLogic_SetFirstChatToken_Handler,
		},
		{
			MethodName: "GetRateReputationInfo",
			Handler:    _GamePlayLogic_GetRateReputationInfo_Handler,
		},
		{
			MethodName: "GetUserAcquisitionAndABTestResult",
			Handler:    _GamePlayLogic_GetUserAcquisitionAndABTestResult_Handler,
		},
		{
			MethodName: "GetRecallPopUp",
			Handler:    _GamePlayLogic_GetRecallPopUp_Handler,
		},
		{
			MethodName: "SubmitRecallPopUp",
			Handler:    _GamePlayLogic_SubmitRecallPopUp_Handler,
		},
		{
			MethodName: "GetRecallTeamUp",
			Handler:    _GamePlayLogic_GetRecallTeamUp_Handler,
		},
		{
			MethodName: "SubmitRecallTeamUp",
			Handler:    _GamePlayLogic_SubmitRecallTeamUp_Handler,
		},
		{
			MethodName: "TransAudioToText",
			Handler:    _GamePlayLogic_TransAudioToText_Handler,
		},
		{
			MethodName: "GetRegisterPageConfigs",
			Handler:    _GamePlayLogic_GetRegisterPageConfigs_Handler,
		},
		{
			MethodName: "SaveGameTime",
			Handler:    _GamePlayLogic_SaveGameTime_Handler,
		},
		{
			MethodName: "GetGameTime",
			Handler:    _GamePlayLogic_GetGameTime_Handler,
		},
		{
			MethodName: "SetEnterRoomNotify",
			Handler:    _GamePlayLogic_SetEnterRoomNotify_Handler,
		},
		{
			MethodName: "CheckUserInRoom",
			Handler:    _GamePlayLogic_CheckUserInRoom_Handler,
		},
		{
			MethodName: "GetGameTimeList",
			Handler:    _GamePlayLogic_GetGameTimeList_Handler,
		},
		{
			MethodName: "FastPCFeedback",
			Handler:    _GamePlayLogic_FastPCFeedback_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/game_play_logic/grpc_game_play_logic.proto",
}

func init() {
	proto.RegisterFile("api/game_play_logic/grpc_game_play_logic.proto", fileDescriptor_grpc_game_play_logic_e75ac004a6f7a83a)
}

var fileDescriptor_grpc_game_play_logic_e75ac004a6f7a83a = []byte{
	// 691 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x96, 0x5b, 0x4f, 0x13, 0x41,
	0x14, 0x80, 0x03, 0x24, 0x6a, 0xc6, 0x0b, 0x38, 0x46, 0x52, 0x9b, 0x98, 0xa0, 0x09, 0x72, 0x93,
	0x69, 0x82, 0x31, 0xd1, 0xf8, 0x62, 0x69, 0xa4, 0x21, 0x21, 0xa4, 0x29, 0xe5, 0xc5, 0x17, 0x1c,
	0x96, 0xc3, 0x32, 0x76, 0x77, 0x66, 0xbb, 0x73, 0x56, 0x20, 0xd1, 0xa4, 0xf2, 0x68, 0xe2, 0xfd,
	0x12, 0xf5, 0x4d, 0xe3, 0xe5, 0x6f, 0x9a, 0x59, 0xca, 0x40, 0xf7, 0x02, 0x8b, 0x6f, 0xed, 0xee,
	0x77, 0xe6, 0x3b, 0xe7, 0x4c, 0xe7, 0x74, 0x08, 0xe3, 0x81, 0xa8, 0xb8, 0xdc, 0x87, 0xb5, 0xc0,
	0xe3, 0xbb, 0x6b, 0x9e, 0x72, 0x85, 0x53, 0x71, 0xc3, 0xc0, 0x59, 0x4b, 0x3c, 0x64, 0x41, 0xa8,
	0x50, 0xd1, 0x51, 0x97, 0x9b, 0x10, 0x96, 0x78, 0x5b, 0x1e, 0x4f, 0xad, 0x91, 0x15, 0x5e, 0xbe,
	0x6e, 0x74, 0xb0, 0x83, 0x20, 0xb5, 0x50, 0xf2, 0xf0, 0xd3, 0xfe, 0xeb, 0xb9, 0x97, 0x23, 0xe4,
	0x62, 0x9d, 0xfb, 0xd0, 0xf0, 0xf8, 0xee, 0x92, 0x09, 0xa3, 0x48, 0xe8, 0xaa, 0x86, 0xd0, 0x3c,
	0x6c, 0x72, 0x84, 0x26, 0x04, 0x2a, 0x44, 0x3a, 0xc5, 0x5c, 0x9e, 0x4c, 0x81, 0xa5, 0xb9, 0x26,
	0x74, 0xca, 0xd3, 0x45, 0x51, 0x1d, 0xdc, 0x3c, 0xbb, 0xd7, 0x1d, 0x1b, 0x3a, 0xf7, 0x9a, 0xd1,
	0x6d, 0x72, 0xa5, 0x0e, 0x68, 0xa8, 0x65, 0x85, 0x06, 0xaa, 0xa9, 0x48, 0x22, 0xcd, 0x5c, 0x2b,
	0x03, 0x34, 0xde, 0x99, 0xc2, 0xac, 0x15, 0xbf, 0x61, 0xb4, 0x43, 0x2e, 0xaf, 0x00, 0x2e, 0x88,
	0x50, 0x63, 0x6d, 0x8b, 0x63, 0x4b, 0xb5, 0x41, 0xd2, 0xc9, 0xac, 0xa5, 0x52, 0x98, 0x91, 0x4e,
	0x15, 0x24, 0xad, 0xf2, 0x2d, 0xa3, 0x2f, 0xc8, 0xd5, 0x3a, 0x60, 0xaf, 0x13, 0x11, 0x72, 0x14,
	0x4a, 0x2e, 0xca, 0x4d, 0x45, 0x6f, 0xe7, 0x54, 0x90, 0x46, 0x8d, 0x7a, 0xf6, 0x14, 0xb4, 0xd5,
	0xbf, 0x63, 0xf4, 0xc7, 0x00, 0xb9, 0xd1, 0x6b, 0x4b, 0xd5, 0xe9, 0x44, 0x42, 0x0b, 0xc3, 0x55,
	0xe5, 0x46, 0x75, 0xbe, 0x05, 0xda, 0xb4, 0x27, 0xf2, 0x90, 0xde, 0x3b, 0xa6, 0x9b, 0xf9, 0x61,
	0x26, 0xaf, 0xfb, 0xff, 0x19, 0x69, 0x73, 0x7c, 0xcf, 0xe8, 0x16, 0xb9, 0x64, 0x2a, 0x01, 0x87,
	0x7b, 0x5e, 0x43, 0x05, 0xab, 0x01, 0x1d, 0xcf, 0xab, 0xf6, 0x90, 0x31, 0xf2, 0x5b, 0x45, 0x30,
	0x6b, 0xfa, 0x55, 0x8a, 0xf7, 0x3f, 0x5a, 0xf7, 0x45, 0x9f, 0x2c, 0x7b, 0xff, 0x93, 0x58, 0xfe,
	0xfe, 0xa7, 0x49, 0xab, 0xfc, 0x5d, 0xa2, 0x6d, 0x32, 0x6c, 0x33, 0x6a, 0x01, 0xf7, 0x57, 0x03,
	0x7a, 0x7c, 0xda, 0xfb, 0x90, 0xd1, 0x4d, 0x14, 0xe2, 0xac, 0xec, 0x4f, 0xc9, 0x1c, 0xe7, 0xa3,
	0xe9, 0xf4, 0x7c, 0x27, 0xa6, 0x7d, 0xa8, 0x9c, 0x2e, 0x8a, 0x5a, 0xeb, 0xdf, 0x12, 0x95, 0x64,
	0xa4, 0x15, 0x72, 0xa9, 0xab, 0xd1, 0x86, 0x50, 0x2d, 0xd5, 0x82, 0x1d, 0xa4, 0x99, 0xb9, 0x27,
	0x29, 0x63, 0x9c, 0x2c, 0x06, 0x5a, 0xdf, 0x07, 0x46, 0xbb, 0x03, 0x64, 0x34, 0x6e, 0x83, 0x2b,
	0x34, 0x42, 0xd8, 0xe0, 0x2e, 0xd4, 0x94, 0xdc, 0x14, 0xae, 0xa6, 0xb9, 0xc7, 0x24, 0xcd, 0x1a,
	0x39, 0x3b, 0x0d, 0x6e, 0x53, 0xf8, 0xc8, 0xe8, 0x53, 0x72, 0x61, 0x85, 0x3f, 0x03, 0x33, 0xe4,
	0x5a, 0xc2, 0x87, 0xec, 0x72, 0x8f, 0x12, 0x4d, 0xe8, 0x44, 0xa0, 0x31, 0xbb, 0xdc, 0x7e, 0x50,
	0x07, 0x4a, 0x6a, 0xe8, 0xb9, 0x3e, 0x31, 0xea, 0x92, 0xf3, 0x75, 0x40, 0xab, 0xca, 0xfb, 0xf5,
	0x24, 0x4d, 0x13, 0x27, 0x72, 0x7d, 0xa2, 0xcf, 0x8c, 0x3e, 0x27, 0x74, 0x05, 0xf0, 0x91, 0x44,
	0x08, 0x9b, 0x4a, 0xf9, 0xcb, 0x0a, 0xc5, 0xe6, 0x6e, 0x76, 0x4b, 0xd3, 0xdc, 0x81, 0x96, 0x15,
	0xc5, 0xfb, 0xec, 0x5f, 0x98, 0x39, 0x28, 0xb5, 0x2d, 0x70, 0xda, 0x66, 0x72, 0x2c, 0x4a, 0x43,
	0x66, 0x97, 0x9a, 0x80, 0x72, 0x0f, 0x4a, 0x8a, 0xb3, 0xfb, 0xf7, 0x95, 0x51, 0x1d, 0x9f, 0xca,
	0x83, 0x56, 0x2c, 0x09, 0x9d, 0xff, 0xef, 0x73, 0x14, 0x3a, 0x28, 0x72, 0xa6, 0x10, 0xdb, 0x57,
	0xe1, 0xb7, 0x78, 0xce, 0x2d, 0x70, 0x8d, 0x8d, 0xda, 0x02, 0xc0, 0xc6, 0x3a, 0x77, 0xda, 0xd9,
	0x73, 0xae, 0x9f, 0xc9, 0x9d, 0x73, 0x49, 0xcc, 0x96, 0xf7, 0x9d, 0x95, 0xaf, 0xed, 0x75, 0xc7,
	0x86, 0x4d, 0xc0, 0xac, 0x09, 0x98, 0x8d, 0x03, 0x5e, 0x75, 0xc7, 0x06, 0x5d, 0x35, 0xff, 0x84,
	0x8c, 0x3a, 0xca, 0x67, 0x9d, 0x68, 0x9b, 0x4b, 0x86, 0xb8, 0x7f, 0x33, 0x30, 0x77, 0x8e, 0xc7,
	0x0f, 0x5d, 0xe5, 0x71, 0xe9, 0xb2, 0xbb, 0x73, 0x88, 0xcc, 0x51, 0x7e, 0x25, 0x7e, 0xe5, 0x28,
	0xaf, 0xc2, 0x83, 0xa0, 0x92, 0x71, 0x91, 0x79, 0x90, 0xf8, 0xfe, 0x73, 0x70, 0xa8, 0xd9, 0xa8,
	0xad, 0x9f, 0x89, 0xe3, 0xee, 0xfc, 0x0b, 0x00, 0x00, 0xff, 0xff, 0x51, 0x05, 0x09, 0xf5, 0xfc,
	0x08, 0x00, 0x00,
}
