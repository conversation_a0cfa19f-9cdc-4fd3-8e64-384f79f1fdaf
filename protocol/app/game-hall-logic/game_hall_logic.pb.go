// Code generated by protoc-gen-go. DO NOT EDIT.
// source: game_hall_logic/game_hall_logic.proto

package game_hall_logic // import "golang.52tt.com/protocol/app/game-hall-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type MsgStatus int32

const (
	MsgStatus_MSG_STATUS_UNSPECIFIED MsgStatus = 0
	MsgStatus_MSG_STATUS_NORMAL      MsgStatus = 1
	MsgStatus_MSG_STATUS_DELETE      MsgStatus = 2
	MsgStatus_MSG_STATUS_CANCEL      MsgStatus = 3
	MsgStatus_MSG_STATUS_UPDATE      MsgStatus = 4
)

var MsgStatus_name = map[int32]string{
	0: "MSG_STATUS_UNSPECIFIED",
	1: "MSG_STATUS_NORMAL",
	2: "MSG_STATUS_DELETE",
	3: "MSG_STATUS_CANCEL",
	4: "MSG_STATUS_UPDATE",
}
var MsgStatus_value = map[string]int32{
	"MSG_STATUS_UNSPECIFIED": 0,
	"MSG_STATUS_NORMAL":      1,
	"MSG_STATUS_DELETE":      2,
	"MSG_STATUS_CANCEL":      3,
	"MSG_STATUS_UPDATE":      4,
}

func (x MsgStatus) String() string {
	return proto.EnumName(MsgStatus_name, int32(x))
}
func (MsgStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{0}
}

type MsgAction int32

const (
	MsgAction_MSG_ACTION_UNSPECIFIED   MsgAction = 0
	MsgAction_MSG_ACTION_NORMAL        MsgAction = 1
	MsgAction_MSG_ACTION_CLEAN         MsgAction = 2
	MsgAction_MSG_ACTION_ONLINE_STATUS MsgAction = 3
	MsgAction_MSG_ACTION_BATCH_CANCEL  MsgAction = 4
)

var MsgAction_name = map[int32]string{
	0: "MSG_ACTION_UNSPECIFIED",
	1: "MSG_ACTION_NORMAL",
	2: "MSG_ACTION_CLEAN",
	3: "MSG_ACTION_ONLINE_STATUS",
	4: "MSG_ACTION_BATCH_CANCEL",
}
var MsgAction_value = map[string]int32{
	"MSG_ACTION_UNSPECIFIED":   0,
	"MSG_ACTION_NORMAL":        1,
	"MSG_ACTION_CLEAN":         2,
	"MSG_ACTION_ONLINE_STATUS": 3,
	"MSG_ACTION_BATCH_CANCEL":  4,
}

func (x MsgAction) String() string {
	return proto.EnumName(MsgAction_name, int32(x))
}
func (MsgAction) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{1}
}

type GameImMsgType int32

const (
	GameImMsgType_GAME_IM_MSG_TYPE_UNSPECIFIED     GameImMsgType = 0
	GameImMsgType_GAME_IM_MSG_TYPE_TEXT            GameImMsgType = 1
	GameImMsgType_GAME_IM_MSG_TYPE_GAME_SCREENSHOT GameImMsgType = 2
	GameImMsgType_GAME_IM_MSG_TYPE_CANCEL          GameImMsgType = 3 // Deprecated: Do not use.
	GameImMsgType_GAME_IM_MSG_TYPE_DEL             GameImMsgType = 4 // Deprecated: Do not use.
	GameImMsgType_GAME_IM_MSG_TYPE_AT_SOMEONE      GameImMsgType = 5
	GameImMsgType_GAME_IM_MSG_TYPE_QUOTE           GameImMsgType = 6
	GameImMsgType_GAME_IM_MSG_TYPE_FORM_TEAM       GameImMsgType = 7
	GameImMsgType_GAME_IM_MSG_TYPE_INVITE_ROOM     GameImMsgType = 8
	GameImMsgType_GAME_IM_MSG_TYPE_EXPRESSION      GameImMsgType = 9
)

var GameImMsgType_name = map[int32]string{
	0: "GAME_IM_MSG_TYPE_UNSPECIFIED",
	1: "GAME_IM_MSG_TYPE_TEXT",
	2: "GAME_IM_MSG_TYPE_GAME_SCREENSHOT",
	3: "GAME_IM_MSG_TYPE_CANCEL",
	4: "GAME_IM_MSG_TYPE_DEL",
	5: "GAME_IM_MSG_TYPE_AT_SOMEONE",
	6: "GAME_IM_MSG_TYPE_QUOTE",
	7: "GAME_IM_MSG_TYPE_FORM_TEAM",
	8: "GAME_IM_MSG_TYPE_INVITE_ROOM",
	9: "GAME_IM_MSG_TYPE_EXPRESSION",
}
var GameImMsgType_value = map[string]int32{
	"GAME_IM_MSG_TYPE_UNSPECIFIED":     0,
	"GAME_IM_MSG_TYPE_TEXT":            1,
	"GAME_IM_MSG_TYPE_GAME_SCREENSHOT": 2,
	"GAME_IM_MSG_TYPE_CANCEL":          3,
	"GAME_IM_MSG_TYPE_DEL":             4,
	"GAME_IM_MSG_TYPE_AT_SOMEONE":      5,
	"GAME_IM_MSG_TYPE_QUOTE":           6,
	"GAME_IM_MSG_TYPE_FORM_TEAM":       7,
	"GAME_IM_MSG_TYPE_INVITE_ROOM":     8,
	"GAME_IM_MSG_TYPE_EXPRESSION":      9,
}

func (x GameImMsgType) String() string {
	return proto.EnumName(GameImMsgType_name, int32(x))
}
func (GameImMsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{2}
}

// 废弃，使用GameUserStatus
type GameImUserStatus int32

const (
	GameImUserStatus_GAME_IM_USER_STATUS_UNSPECIFIED GameImUserStatus = 0
	GameImUserStatus_GAME_IM_USER_STATUS_OFFLINE     GameImUserStatus = 1
	GameImUserStatus_GAME_IM_USER_STATUS_ONLINE      GameImUserStatus = 2
)

var GameImUserStatus_name = map[int32]string{
	0: "GAME_IM_USER_STATUS_UNSPECIFIED",
	1: "GAME_IM_USER_STATUS_OFFLINE",
	2: "GAME_IM_USER_STATUS_ONLINE",
}
var GameImUserStatus_value = map[string]int32{
	"GAME_IM_USER_STATUS_UNSPECIFIED": 0,
	"GAME_IM_USER_STATUS_OFFLINE":     1,
	"GAME_IM_USER_STATUS_ONLINE":      2,
}

func (x GameImUserStatus) String() string {
	return proto.EnumName(GameImUserStatus_name, int32(x))
}
func (GameImUserStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{3}
}

type GameUserStatus int32

const (
	GameUserStatus_GAME_USER_STATUS_UNSPECIFIED GameUserStatus = 0
	GameUserStatus_GAME_USER_STATUS_ONLINE      GameUserStatus = 1
	GameUserStatus_GAME_USER_STATUS_OFFLINE     GameUserStatus = 2
)

var GameUserStatus_name = map[int32]string{
	0: "GAME_USER_STATUS_UNSPECIFIED",
	1: "GAME_USER_STATUS_ONLINE",
	2: "GAME_USER_STATUS_OFFLINE",
}
var GameUserStatus_value = map[string]int32{
	"GAME_USER_STATUS_UNSPECIFIED": 0,
	"GAME_USER_STATUS_ONLINE":      1,
	"GAME_USER_STATUS_OFFLINE":     2,
}

func (x GameUserStatus) String() string {
	return proto.EnumName(GameUserStatus_name, int32(x))
}
func (GameUserStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{4}
}

type ListEntrance int32

const (
	ListEntrance_LIST_ENTRANCE_UNSPECIFIED   ListEntrance = 0
	ListEntrance_LIST_ENTRANCE_ALL_MSG       ListEntrance = 1
	ListEntrance_LIST_ENTRANCE_FORM_TEAM_MSG ListEntrance = 2
	ListEntrance_LIST_ENTRANCE_PC_IM_MSG     ListEntrance = 3
)

var ListEntrance_name = map[int32]string{
	0: "LIST_ENTRANCE_UNSPECIFIED",
	1: "LIST_ENTRANCE_ALL_MSG",
	2: "LIST_ENTRANCE_FORM_TEAM_MSG",
	3: "LIST_ENTRANCE_PC_IM_MSG",
}
var ListEntrance_value = map[string]int32{
	"LIST_ENTRANCE_UNSPECIFIED":   0,
	"LIST_ENTRANCE_ALL_MSG":       1,
	"LIST_ENTRANCE_FORM_TEAM_MSG": 2,
	"LIST_ENTRANCE_PC_IM_MSG":     3,
}

func (x ListEntrance) String() string {
	return proto.EnumName(ListEntrance_name, int32(x))
}
func (ListEntrance) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{5}
}

type CheckSendInviteRoomSource int32

const (
	CheckSendInviteRoomSource_CHECK_SEND_INVITE_ROOM_SOURCE_UNSPECIFIED CheckSendInviteRoomSource = 0
	CheckSendInviteRoomSource_CHECK_SEND_INVITE_ROOM_SOURCE_TEAM        CheckSendInviteRoomSource = 1
	CheckSendInviteRoomSource_CHECK_SEND_INVITE_ROOM_SOURCE_HALL        CheckSendInviteRoomSource = 2
)

var CheckSendInviteRoomSource_name = map[int32]string{
	0: "CHECK_SEND_INVITE_ROOM_SOURCE_UNSPECIFIED",
	1: "CHECK_SEND_INVITE_ROOM_SOURCE_TEAM",
	2: "CHECK_SEND_INVITE_ROOM_SOURCE_HALL",
}
var CheckSendInviteRoomSource_value = map[string]int32{
	"CHECK_SEND_INVITE_ROOM_SOURCE_UNSPECIFIED": 0,
	"CHECK_SEND_INVITE_ROOM_SOURCE_TEAM":        1,
	"CHECK_SEND_INVITE_ROOM_SOURCE_HALL":        2,
}

func (x CheckSendInviteRoomSource) String() string {
	return proto.EnumName(CheckSendInviteRoomSource_name, int32(x))
}
func (CheckSendInviteRoomSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{6}
}

type GetMsgListRequest_ActionType int32

const (
	GetMsgListRequest_ACTION_TYPE_UNSPECIFIED GetMsgListRequest_ActionType = 0
	GetMsgListRequest_ACTION_TYPE_UP          GetMsgListRequest_ActionType = 1
	GetMsgListRequest_ACTION_TYPE_DOWN        GetMsgListRequest_ActionType = 2
	GetMsgListRequest_ACTION_TYPE_JUMP        GetMsgListRequest_ActionType = 3
)

var GetMsgListRequest_ActionType_name = map[int32]string{
	0: "ACTION_TYPE_UNSPECIFIED",
	1: "ACTION_TYPE_UP",
	2: "ACTION_TYPE_DOWN",
	3: "ACTION_TYPE_JUMP",
}
var GetMsgListRequest_ActionType_value = map[string]int32{
	"ACTION_TYPE_UNSPECIFIED": 0,
	"ACTION_TYPE_UP":          1,
	"ACTION_TYPE_DOWN":        2,
	"ACTION_TYPE_JUMP":        3,
}

func (x GetMsgListRequest_ActionType) String() string {
	return proto.EnumName(GetMsgListRequest_ActionType_name, int32(x))
}
func (GetMsgListRequest_ActionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{16, 0}
}

// 获取订阅令牌及频道路径
type GetBuildChannelInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBuildChannelInfoRequest) Reset()         { *m = GetBuildChannelInfoRequest{} }
func (m *GetBuildChannelInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetBuildChannelInfoRequest) ProtoMessage()    {}
func (*GetBuildChannelInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{0}
}
func (m *GetBuildChannelInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBuildChannelInfoRequest.Unmarshal(m, b)
}
func (m *GetBuildChannelInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBuildChannelInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetBuildChannelInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBuildChannelInfoRequest.Merge(dst, src)
}
func (m *GetBuildChannelInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetBuildChannelInfoRequest.Size(m)
}
func (m *GetBuildChannelInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBuildChannelInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBuildChannelInfoRequest proto.InternalMessageInfo

func (m *GetBuildChannelInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetBuildChannelInfoRequest) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetBuildChannelInfoResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Token                string        `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	ChannelPath          string        `protobuf:"bytes,3,opt,name=channel_path,json=channelPath,proto3" json:"channel_path,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetBuildChannelInfoResponse) Reset()         { *m = GetBuildChannelInfoResponse{} }
func (m *GetBuildChannelInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetBuildChannelInfoResponse) ProtoMessage()    {}
func (*GetBuildChannelInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{1}
}
func (m *GetBuildChannelInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBuildChannelInfoResponse.Unmarshal(m, b)
}
func (m *GetBuildChannelInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBuildChannelInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetBuildChannelInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBuildChannelInfoResponse.Merge(dst, src)
}
func (m *GetBuildChannelInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetBuildChannelInfoResponse.Size(m)
}
func (m *GetBuildChannelInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBuildChannelInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBuildChannelInfoResponse proto.InternalMessageInfo

func (m *GetBuildChannelInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBuildChannelInfoResponse) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *GetBuildChannelInfoResponse) GetChannelPath() string {
	if m != nil {
		return m.ChannelPath
	}
	return ""
}

type GameImUserInfo struct {
	Uid                  uint32                       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string                       `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	GameCardInfo         *GameImUserInfo_GameCardInfo `protobuf:"bytes,3,opt,name=game_card_info,json=gameCardInfo,proto3" json:"game_card_info,omitempty"`
	UserStatus           uint32                       `protobuf:"varint,4,opt,name=user_status,json=userStatus,proto3" json:"user_status,omitempty"`
	Account              string                       `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"`
	Sex                  uint32                       `protobuf:"varint,6,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GameImUserInfo) Reset()         { *m = GameImUserInfo{} }
func (m *GameImUserInfo) String() string { return proto.CompactTextString(m) }
func (*GameImUserInfo) ProtoMessage()    {}
func (*GameImUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{2}
}
func (m *GameImUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameImUserInfo.Unmarshal(m, b)
}
func (m *GameImUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameImUserInfo.Marshal(b, m, deterministic)
}
func (dst *GameImUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameImUserInfo.Merge(dst, src)
}
func (m *GameImUserInfo) XXX_Size() int {
	return xxx_messageInfo_GameImUserInfo.Size(m)
}
func (m *GameImUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameImUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameImUserInfo proto.InternalMessageInfo

func (m *GameImUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameImUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GameImUserInfo) GetGameCardInfo() *GameImUserInfo_GameCardInfo {
	if m != nil {
		return m.GameCardInfo
	}
	return nil
}

func (m *GameImUserInfo) GetUserStatus() uint32 {
	if m != nil {
		return m.UserStatus
	}
	return 0
}

func (m *GameImUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GameImUserInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type GameImUserInfo_GameCardInfo struct {
	GameCardText         string   `protobuf:"bytes,1,opt,name=game_card_text,json=gameCardText,proto3" json:"game_card_text,omitempty"`
	GameCardId           uint32   `protobuf:"varint,2,opt,name=game_card_id,json=gameCardId,proto3" json:"game_card_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameImUserInfo_GameCardInfo) Reset()         { *m = GameImUserInfo_GameCardInfo{} }
func (m *GameImUserInfo_GameCardInfo) String() string { return proto.CompactTextString(m) }
func (*GameImUserInfo_GameCardInfo) ProtoMessage()    {}
func (*GameImUserInfo_GameCardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{2, 0}
}
func (m *GameImUserInfo_GameCardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameImUserInfo_GameCardInfo.Unmarshal(m, b)
}
func (m *GameImUserInfo_GameCardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameImUserInfo_GameCardInfo.Marshal(b, m, deterministic)
}
func (dst *GameImUserInfo_GameCardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameImUserInfo_GameCardInfo.Merge(dst, src)
}
func (m *GameImUserInfo_GameCardInfo) XXX_Size() int {
	return xxx_messageInfo_GameImUserInfo_GameCardInfo.Size(m)
}
func (m *GameImUserInfo_GameCardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameImUserInfo_GameCardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameImUserInfo_GameCardInfo proto.InternalMessageInfo

func (m *GameImUserInfo_GameCardInfo) GetGameCardText() string {
	if m != nil {
		return m.GameCardText
	}
	return ""
}

func (m *GameImUserInfo_GameCardInfo) GetGameCardId() uint32 {
	if m != nil {
		return m.GameCardId
	}
	return 0
}

// 完整消息结构
type GameImMsg struct {
	MsgId                uint64          `protobuf:"varint,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	SenderInfo           *GameImUserInfo `protobuf:"bytes,2,opt,name=sender_info,json=senderInfo,proto3" json:"sender_info,omitempty"`
	MsgType              uint32          `protobuf:"varint,3,opt,name=msg_type,json=msgType,proto3" json:"msg_type,omitempty"`
	SendTime             int64           `protobuf:"varint,4,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	MsgStatus            uint32          `protobuf:"varint,5,opt,name=msg_status,json=msgStatus,proto3" json:"msg_status,omitempty"`
	TabId                uint32          `protobuf:"varint,6,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	MsgAction            uint32          `protobuf:"varint,7,opt,name=msg_action,json=msgAction,proto3" json:"msg_action,omitempty"`
	MsgContentBytes      []byte          `protobuf:"bytes,8,opt,name=msg_content_bytes,json=msgContentBytes,proto3" json:"msg_content_bytes,omitempty"`
	ClientMsgTag         int64           `protobuf:"varint,9,opt,name=client_msg_tag,json=clientMsgTag,proto3" json:"client_msg_tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GameImMsg) Reset()         { *m = GameImMsg{} }
func (m *GameImMsg) String() string { return proto.CompactTextString(m) }
func (*GameImMsg) ProtoMessage()    {}
func (*GameImMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{3}
}
func (m *GameImMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameImMsg.Unmarshal(m, b)
}
func (m *GameImMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameImMsg.Marshal(b, m, deterministic)
}
func (dst *GameImMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameImMsg.Merge(dst, src)
}
func (m *GameImMsg) XXX_Size() int {
	return xxx_messageInfo_GameImMsg.Size(m)
}
func (m *GameImMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GameImMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GameImMsg proto.InternalMessageInfo

func (m *GameImMsg) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *GameImMsg) GetSenderInfo() *GameImUserInfo {
	if m != nil {
		return m.SenderInfo
	}
	return nil
}

func (m *GameImMsg) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

func (m *GameImMsg) GetSendTime() int64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *GameImMsg) GetMsgStatus() uint32 {
	if m != nil {
		return m.MsgStatus
	}
	return 0
}

func (m *GameImMsg) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GameImMsg) GetMsgAction() uint32 {
	if m != nil {
		return m.MsgAction
	}
	return 0
}

func (m *GameImMsg) GetMsgContentBytes() []byte {
	if m != nil {
		return m.MsgContentBytes
	}
	return nil
}

func (m *GameImMsg) GetClientMsgTag() int64 {
	if m != nil {
		return m.ClientMsgTag
	}
	return 0
}

// 在线状态变更消息
type GameOlStatusMsg struct {
	OlStatusList         []*GameOlStatusMsg_OlStatusInfo `protobuf:"bytes,1,rep,name=ol_status_list,json=olStatusList,proto3" json:"ol_status_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GameOlStatusMsg) Reset()         { *m = GameOlStatusMsg{} }
func (m *GameOlStatusMsg) String() string { return proto.CompactTextString(m) }
func (*GameOlStatusMsg) ProtoMessage()    {}
func (*GameOlStatusMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{4}
}
func (m *GameOlStatusMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameOlStatusMsg.Unmarshal(m, b)
}
func (m *GameOlStatusMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameOlStatusMsg.Marshal(b, m, deterministic)
}
func (dst *GameOlStatusMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameOlStatusMsg.Merge(dst, src)
}
func (m *GameOlStatusMsg) XXX_Size() int {
	return xxx_messageInfo_GameOlStatusMsg.Size(m)
}
func (m *GameOlStatusMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GameOlStatusMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GameOlStatusMsg proto.InternalMessageInfo

func (m *GameOlStatusMsg) GetOlStatusList() []*GameOlStatusMsg_OlStatusInfo {
	if m != nil {
		return m.OlStatusList
	}
	return nil
}

type GameOlStatusMsg_OlStatusInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameOlStatusMsg_OlStatusInfo) Reset()         { *m = GameOlStatusMsg_OlStatusInfo{} }
func (m *GameOlStatusMsg_OlStatusInfo) String() string { return proto.CompactTextString(m) }
func (*GameOlStatusMsg_OlStatusInfo) ProtoMessage()    {}
func (*GameOlStatusMsg_OlStatusInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{4, 0}
}
func (m *GameOlStatusMsg_OlStatusInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameOlStatusMsg_OlStatusInfo.Unmarshal(m, b)
}
func (m *GameOlStatusMsg_OlStatusInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameOlStatusMsg_OlStatusInfo.Marshal(b, m, deterministic)
}
func (dst *GameOlStatusMsg_OlStatusInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameOlStatusMsg_OlStatusInfo.Merge(dst, src)
}
func (m *GameOlStatusMsg_OlStatusInfo) XXX_Size() int {
	return xxx_messageInfo_GameOlStatusMsg_OlStatusInfo.Size(m)
}
func (m *GameOlStatusMsg_OlStatusInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameOlStatusMsg_OlStatusInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameOlStatusMsg_OlStatusInfo proto.InternalMessageInfo

func (m *GameOlStatusMsg_OlStatusInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameOlStatusMsg_OlStatusInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

// 文本消息
type GameTextMsg struct {
	Text                 string   `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameTextMsg) Reset()         { *m = GameTextMsg{} }
func (m *GameTextMsg) String() string { return proto.CompactTextString(m) }
func (*GameTextMsg) ProtoMessage()    {}
func (*GameTextMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{5}
}
func (m *GameTextMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTextMsg.Unmarshal(m, b)
}
func (m *GameTextMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTextMsg.Marshal(b, m, deterministic)
}
func (dst *GameTextMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTextMsg.Merge(dst, src)
}
func (m *GameTextMsg) XXX_Size() int {
	return xxx_messageInfo_GameTextMsg.Size(m)
}
func (m *GameTextMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTextMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GameTextMsg proto.InternalMessageInfo

func (m *GameTextMsg) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

// 发送游戏截图消息
type GameScreenshotMsg struct {
	ImgUrl               string   `protobuf:"bytes,1,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	Height               uint32   `protobuf:"varint,2,opt,name=height,proto3" json:"height,omitempty"`
	Width                uint32   `protobuf:"varint,3,opt,name=width,proto3" json:"width,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameScreenshotMsg) Reset()         { *m = GameScreenshotMsg{} }
func (m *GameScreenshotMsg) String() string { return proto.CompactTextString(m) }
func (*GameScreenshotMsg) ProtoMessage()    {}
func (*GameScreenshotMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{6}
}
func (m *GameScreenshotMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameScreenshotMsg.Unmarshal(m, b)
}
func (m *GameScreenshotMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameScreenshotMsg.Marshal(b, m, deterministic)
}
func (dst *GameScreenshotMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameScreenshotMsg.Merge(dst, src)
}
func (m *GameScreenshotMsg) XXX_Size() int {
	return xxx_messageInfo_GameScreenshotMsg.Size(m)
}
func (m *GameScreenshotMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GameScreenshotMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GameScreenshotMsg proto.InternalMessageInfo

func (m *GameScreenshotMsg) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *GameScreenshotMsg) GetHeight() uint32 {
	if m != nil {
		return m.Height
	}
	return 0
}

func (m *GameScreenshotMsg) GetWidth() uint32 {
	if m != nil {
		return m.Width
	}
	return 0
}

// 废弃
type GameCancelMsg struct {
	CancelMsgId          uint32   `protobuf:"varint,1,opt,name=cancel_msg_id,json=cancelMsgId,proto3" json:"cancel_msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameCancelMsg) Reset()         { *m = GameCancelMsg{} }
func (m *GameCancelMsg) String() string { return proto.CompactTextString(m) }
func (*GameCancelMsg) ProtoMessage()    {}
func (*GameCancelMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{7}
}
func (m *GameCancelMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameCancelMsg.Unmarshal(m, b)
}
func (m *GameCancelMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameCancelMsg.Marshal(b, m, deterministic)
}
func (dst *GameCancelMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameCancelMsg.Merge(dst, src)
}
func (m *GameCancelMsg) XXX_Size() int {
	return xxx_messageInfo_GameCancelMsg.Size(m)
}
func (m *GameCancelMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GameCancelMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GameCancelMsg proto.InternalMessageInfo

func (m *GameCancelMsg) GetCancelMsgId() uint32 {
	if m != nil {
		return m.CancelMsgId
	}
	return 0
}

// 废弃
type GameDelMsg struct {
	DelMsgId             uint32   `protobuf:"varint,1,opt,name=del_msg_id,json=delMsgId,proto3" json:"del_msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameDelMsg) Reset()         { *m = GameDelMsg{} }
func (m *GameDelMsg) String() string { return proto.CompactTextString(m) }
func (*GameDelMsg) ProtoMessage()    {}
func (*GameDelMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{8}
}
func (m *GameDelMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameDelMsg.Unmarshal(m, b)
}
func (m *GameDelMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameDelMsg.Marshal(b, m, deterministic)
}
func (dst *GameDelMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameDelMsg.Merge(dst, src)
}
func (m *GameDelMsg) XXX_Size() int {
	return xxx_messageInfo_GameDelMsg.Size(m)
}
func (m *GameDelMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GameDelMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GameDelMsg proto.InternalMessageInfo

func (m *GameDelMsg) GetDelMsgId() uint32 {
	if m != nil {
		return m.DelMsgId
	}
	return 0
}

// at消息
type GameAtSomeoneMsg struct {
	AtUserId             []uint32 `protobuf:"varint,1,rep,packed,name=at_user_id,json=atUserId,proto3" json:"at_user_id,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameAtSomeoneMsg) Reset()         { *m = GameAtSomeoneMsg{} }
func (m *GameAtSomeoneMsg) String() string { return proto.CompactTextString(m) }
func (*GameAtSomeoneMsg) ProtoMessage()    {}
func (*GameAtSomeoneMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{9}
}
func (m *GameAtSomeoneMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameAtSomeoneMsg.Unmarshal(m, b)
}
func (m *GameAtSomeoneMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameAtSomeoneMsg.Marshal(b, m, deterministic)
}
func (dst *GameAtSomeoneMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameAtSomeoneMsg.Merge(dst, src)
}
func (m *GameAtSomeoneMsg) XXX_Size() int {
	return xxx_messageInfo_GameAtSomeoneMsg.Size(m)
}
func (m *GameAtSomeoneMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GameAtSomeoneMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GameAtSomeoneMsg proto.InternalMessageInfo

func (m *GameAtSomeoneMsg) GetAtUserId() []uint32 {
	if m != nil {
		return m.AtUserId
	}
	return nil
}

func (m *GameAtSomeoneMsg) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

// 引用消息
type GameQuoteMsg struct {
	Text                 string     `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	QuoteMsg             *GameImMsg `protobuf:"bytes,2,opt,name=quote_msg,json=quoteMsg,proto3" json:"quote_msg,omitempty"`
	AtUserId             []uint32   `protobuf:"varint,3,rep,packed,name=at_user_id,json=atUserId,proto3" json:"at_user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GameQuoteMsg) Reset()         { *m = GameQuoteMsg{} }
func (m *GameQuoteMsg) String() string { return proto.CompactTextString(m) }
func (*GameQuoteMsg) ProtoMessage()    {}
func (*GameQuoteMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{10}
}
func (m *GameQuoteMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameQuoteMsg.Unmarshal(m, b)
}
func (m *GameQuoteMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameQuoteMsg.Marshal(b, m, deterministic)
}
func (dst *GameQuoteMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameQuoteMsg.Merge(dst, src)
}
func (m *GameQuoteMsg) XXX_Size() int {
	return xxx_messageInfo_GameQuoteMsg.Size(m)
}
func (m *GameQuoteMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GameQuoteMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GameQuoteMsg proto.InternalMessageInfo

func (m *GameQuoteMsg) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *GameQuoteMsg) GetQuoteMsg() *GameImMsg {
	if m != nil {
		return m.QuoteMsg
	}
	return nil
}

func (m *GameQuoteMsg) GetAtUserId() []uint32 {
	if m != nil {
		return m.AtUserId
	}
	return nil
}

// 组队信号消息
type GameFormTeamMsg struct {
	TabId         uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName       string   `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Icon          string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Text          string   `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"`
	MemberAccount []string `protobuf:"bytes,5,rep,name=member_account,json=memberAccount,proto3" json:"member_account,omitempty"`
	TeamInfo      string   `protobuf:"bytes,6,opt,name=team_info,json=teamInfo,proto3" json:"team_info,omitempty"`
	IsJoin        bool     `protobuf:"varint,7,opt,name=is_join,json=isJoin,proto3" json:"is_join,omitempty"` // Deprecated: Do not use.
	// 客户端用于判断自己是否报名
	JoinUid              uint32   `protobuf:"varint,8,opt,name=join_uid,json=joinUid,proto3" json:"join_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameFormTeamMsg) Reset()         { *m = GameFormTeamMsg{} }
func (m *GameFormTeamMsg) String() string { return proto.CompactTextString(m) }
func (*GameFormTeamMsg) ProtoMessage()    {}
func (*GameFormTeamMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{11}
}
func (m *GameFormTeamMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameFormTeamMsg.Unmarshal(m, b)
}
func (m *GameFormTeamMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameFormTeamMsg.Marshal(b, m, deterministic)
}
func (dst *GameFormTeamMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameFormTeamMsg.Merge(dst, src)
}
func (m *GameFormTeamMsg) XXX_Size() int {
	return xxx_messageInfo_GameFormTeamMsg.Size(m)
}
func (m *GameFormTeamMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GameFormTeamMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GameFormTeamMsg proto.InternalMessageInfo

func (m *GameFormTeamMsg) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GameFormTeamMsg) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *GameFormTeamMsg) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GameFormTeamMsg) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *GameFormTeamMsg) GetMemberAccount() []string {
	if m != nil {
		return m.MemberAccount
	}
	return nil
}

func (m *GameFormTeamMsg) GetTeamInfo() string {
	if m != nil {
		return m.TeamInfo
	}
	return ""
}

// Deprecated: Do not use.
func (m *GameFormTeamMsg) GetIsJoin() bool {
	if m != nil {
		return m.IsJoin
	}
	return false
}

func (m *GameFormTeamMsg) GetJoinUid() uint32 {
	if m != nil {
		return m.JoinUid
	}
	return 0
}

// 邀请进房消息
type GameInviteRoomMsg struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Icon                 string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Text                 string   `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"`
	ExpireTime           int64    `protobuf:"varint,5,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	Cid                  uint32   `protobuf:"varint,6,opt,name=cid,proto3" json:"cid,omitempty"`
	BgColorNum           uint32   `protobuf:"varint,7,opt,name=bg_color_num,json=bgColorNum,proto3" json:"bg_color_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameInviteRoomMsg) Reset()         { *m = GameInviteRoomMsg{} }
func (m *GameInviteRoomMsg) String() string { return proto.CompactTextString(m) }
func (*GameInviteRoomMsg) ProtoMessage()    {}
func (*GameInviteRoomMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{12}
}
func (m *GameInviteRoomMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameInviteRoomMsg.Unmarshal(m, b)
}
func (m *GameInviteRoomMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameInviteRoomMsg.Marshal(b, m, deterministic)
}
func (dst *GameInviteRoomMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameInviteRoomMsg.Merge(dst, src)
}
func (m *GameInviteRoomMsg) XXX_Size() int {
	return xxx_messageInfo_GameInviteRoomMsg.Size(m)
}
func (m *GameInviteRoomMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GameInviteRoomMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GameInviteRoomMsg proto.InternalMessageInfo

func (m *GameInviteRoomMsg) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GameInviteRoomMsg) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *GameInviteRoomMsg) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GameInviteRoomMsg) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *GameInviteRoomMsg) GetExpireTime() int64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *GameInviteRoomMsg) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GameInviteRoomMsg) GetBgColorNum() uint32 {
	if m != nil {
		return m.BgColorNum
	}
	return 0
}

// 废弃， 客户端收到action 为  MSG_ACTION_CLEAN = 2;的消息，直接清屏，不需要考虑从哪个消息id开始清
type CleanMsg struct {
	MsgId                uint64   `protobuf:"varint,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CleanMsg) Reset()         { *m = CleanMsg{} }
func (m *CleanMsg) String() string { return proto.CompactTextString(m) }
func (*CleanMsg) ProtoMessage()    {}
func (*CleanMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{13}
}
func (m *CleanMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CleanMsg.Unmarshal(m, b)
}
func (m *CleanMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CleanMsg.Marshal(b, m, deterministic)
}
func (dst *CleanMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CleanMsg.Merge(dst, src)
}
func (m *CleanMsg) XXX_Size() int {
	return xxx_messageInfo_CleanMsg.Size(m)
}
func (m *CleanMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_CleanMsg.DiscardUnknown(m)
}

var xxx_messageInfo_CleanMsg proto.InternalMessageInfo

func (m *CleanMsg) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

// 表情包消息
type GameExpressionMsg struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Height               uint32   `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	Width                uint32   `protobuf:"varint,4,opt,name=width,proto3" json:"width,omitempty"`
	EmojiType            string   `protobuf:"bytes,5,opt,name=emoji_type,json=emojiType,proto3" json:"emoji_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameExpressionMsg) Reset()         { *m = GameExpressionMsg{} }
func (m *GameExpressionMsg) String() string { return proto.CompactTextString(m) }
func (*GameExpressionMsg) ProtoMessage()    {}
func (*GameExpressionMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{14}
}
func (m *GameExpressionMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameExpressionMsg.Unmarshal(m, b)
}
func (m *GameExpressionMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameExpressionMsg.Marshal(b, m, deterministic)
}
func (dst *GameExpressionMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameExpressionMsg.Merge(dst, src)
}
func (m *GameExpressionMsg) XXX_Size() int {
	return xxx_messageInfo_GameExpressionMsg.Size(m)
}
func (m *GameExpressionMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GameExpressionMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GameExpressionMsg proto.InternalMessageInfo

func (m *GameExpressionMsg) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GameExpressionMsg) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *GameExpressionMsg) GetHeight() uint32 {
	if m != nil {
		return m.Height
	}
	return 0
}

func (m *GameExpressionMsg) GetWidth() uint32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *GameExpressionMsg) GetEmojiType() string {
	if m != nil {
		return m.EmojiType
	}
	return ""
}

// 一键撤回用户所有消息
type GameBatchCancelMsg struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameBatchCancelMsg) Reset()         { *m = GameBatchCancelMsg{} }
func (m *GameBatchCancelMsg) String() string { return proto.CompactTextString(m) }
func (*GameBatchCancelMsg) ProtoMessage()    {}
func (*GameBatchCancelMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{15}
}
func (m *GameBatchCancelMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameBatchCancelMsg.Unmarshal(m, b)
}
func (m *GameBatchCancelMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameBatchCancelMsg.Marshal(b, m, deterministic)
}
func (dst *GameBatchCancelMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameBatchCancelMsg.Merge(dst, src)
}
func (m *GameBatchCancelMsg) XXX_Size() int {
	return xxx_messageInfo_GameBatchCancelMsg.Size(m)
}
func (m *GameBatchCancelMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GameBatchCancelMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GameBatchCancelMsg proto.InternalMessageInfo

func (m *GameBatchCancelMsg) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// 拉取历史消息
type GetMsgListRequest struct {
	BaseReq              *app.BaseReq                 `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32                       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	MsgId                uint64                       `protobuf:"varint,3,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	Action               GetMsgListRequest_ActionType `protobuf:"varint,4,opt,name=action,proto3,enum=ga.game_hall_logic.GetMsgListRequest_ActionType" json:"action,omitempty"`
	Entrance             uint32                       `protobuf:"varint,5,opt,name=entrance,proto3" json:"entrance,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetMsgListRequest) Reset()         { *m = GetMsgListRequest{} }
func (m *GetMsgListRequest) String() string { return proto.CompactTextString(m) }
func (*GetMsgListRequest) ProtoMessage()    {}
func (*GetMsgListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{16}
}
func (m *GetMsgListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMsgListRequest.Unmarshal(m, b)
}
func (m *GetMsgListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMsgListRequest.Marshal(b, m, deterministic)
}
func (dst *GetMsgListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMsgListRequest.Merge(dst, src)
}
func (m *GetMsgListRequest) XXX_Size() int {
	return xxx_messageInfo_GetMsgListRequest.Size(m)
}
func (m *GetMsgListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMsgListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMsgListRequest proto.InternalMessageInfo

func (m *GetMsgListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMsgListRequest) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetMsgListRequest) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *GetMsgListRequest) GetAction() GetMsgListRequest_ActionType {
	if m != nil {
		return m.Action
	}
	return GetMsgListRequest_ACTION_TYPE_UNSPECIFIED
}

func (m *GetMsgListRequest) GetEntrance() uint32 {
	if m != nil {
		return m.Entrance
	}
	return 0
}

type GetMsgListResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MsgList              []*GameImMsg  `protobuf:"bytes,2,rep,name=msg_list,json=msgList,proto3" json:"msg_list,omitempty"`
	LoadFinish           bool          `protobuf:"varint,3,opt,name=load_finish,json=loadFinish,proto3" json:"load_finish,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetMsgListResponse) Reset()         { *m = GetMsgListResponse{} }
func (m *GetMsgListResponse) String() string { return proto.CompactTextString(m) }
func (*GetMsgListResponse) ProtoMessage()    {}
func (*GetMsgListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{17}
}
func (m *GetMsgListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMsgListResponse.Unmarshal(m, b)
}
func (m *GetMsgListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMsgListResponse.Marshal(b, m, deterministic)
}
func (dst *GetMsgListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMsgListResponse.Merge(dst, src)
}
func (m *GetMsgListResponse) XXX_Size() int {
	return xxx_messageInfo_GetMsgListResponse.Size(m)
}
func (m *GetMsgListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMsgListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMsgListResponse proto.InternalMessageInfo

func (m *GetMsgListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMsgListResponse) GetMsgList() []*GameImMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

func (m *GetMsgListResponse) GetLoadFinish() bool {
	if m != nil {
		return m.LoadFinish
	}
	return false
}

// 发送消息
type SendGameImMsgRequest struct {
	BaseReq     *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelPath string       `protobuf:"bytes,2,opt,name=channel_path,json=channelPath,proto3" json:"channel_path,omitempty"`
	// Types that are valid to be assigned to Request:
	//	*SendGameImMsgRequest_TextRequest_
	//	*SendGameImMsgRequest_CancelRequest_
	//	*SendGameImMsgRequest_DelRequest_
	//	*SendGameImMsgRequest_AtRequest_
	//	*SendGameImMsgRequest_QuoteRequest_
	//	*SendGameImMsgRequest_GameScreenshotRequest
	//	*SendGameImMsgRequest_FormTeamRequest_
	//	*SendGameImMsgRequest_InviteRoomRequest_
	//	*SendGameImMsgRequest_ExpressionRequest_
	//	*SendGameImMsgRequest_JoinTeamRequest_
	Request              isSendGameImMsgRequest_Request `protobuf_oneof:"request"`
	ClientMsgTag         int64                          `protobuf:"varint,13,opt,name=client_msg_tag,json=clientMsgTag,proto3" json:"client_msg_tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *SendGameImMsgRequest) Reset()         { *m = SendGameImMsgRequest{} }
func (m *SendGameImMsgRequest) String() string { return proto.CompactTextString(m) }
func (*SendGameImMsgRequest) ProtoMessage()    {}
func (*SendGameImMsgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{18}
}
func (m *SendGameImMsgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGameImMsgRequest.Unmarshal(m, b)
}
func (m *SendGameImMsgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGameImMsgRequest.Marshal(b, m, deterministic)
}
func (dst *SendGameImMsgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGameImMsgRequest.Merge(dst, src)
}
func (m *SendGameImMsgRequest) XXX_Size() int {
	return xxx_messageInfo_SendGameImMsgRequest.Size(m)
}
func (m *SendGameImMsgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGameImMsgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendGameImMsgRequest proto.InternalMessageInfo

func (m *SendGameImMsgRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendGameImMsgRequest) GetChannelPath() string {
	if m != nil {
		return m.ChannelPath
	}
	return ""
}

type isSendGameImMsgRequest_Request interface {
	isSendGameImMsgRequest_Request()
}

type SendGameImMsgRequest_TextRequest_ struct {
	TextRequest *SendGameImMsgRequest_TextRequest `protobuf:"bytes,3,opt,name=text_request,json=textRequest,proto3,oneof"`
}

type SendGameImMsgRequest_CancelRequest_ struct {
	CancelRequest *SendGameImMsgRequest_CancelRequest `protobuf:"bytes,4,opt,name=cancel_request,json=cancelRequest,proto3,oneof"`
}

type SendGameImMsgRequest_DelRequest_ struct {
	DelRequest *SendGameImMsgRequest_DelRequest `protobuf:"bytes,5,opt,name=del_request,json=delRequest,proto3,oneof"`
}

type SendGameImMsgRequest_AtRequest_ struct {
	AtRequest *SendGameImMsgRequest_AtRequest `protobuf:"bytes,6,opt,name=at_request,json=atRequest,proto3,oneof"`
}

type SendGameImMsgRequest_QuoteRequest_ struct {
	QuoteRequest *SendGameImMsgRequest_QuoteRequest `protobuf:"bytes,7,opt,name=quote_request,json=quoteRequest,proto3,oneof"`
}

type SendGameImMsgRequest_GameScreenshotRequest struct {
	GameScreenshotRequest *SendGameImMsgRequest_GameScreenShotMsgRequest `protobuf:"bytes,8,opt,name=game_screenshot_request,json=gameScreenshotRequest,proto3,oneof"`
}

type SendGameImMsgRequest_FormTeamRequest_ struct {
	FormTeamRequest *SendGameImMsgRequest_FormTeamRequest `protobuf:"bytes,9,opt,name=form_team_request,json=formTeamRequest,proto3,oneof"`
}

type SendGameImMsgRequest_InviteRoomRequest_ struct {
	InviteRoomRequest *SendGameImMsgRequest_InviteRoomRequest `protobuf:"bytes,10,opt,name=invite_room_request,json=inviteRoomRequest,proto3,oneof"`
}

type SendGameImMsgRequest_ExpressionRequest_ struct {
	ExpressionRequest *SendGameImMsgRequest_ExpressionRequest `protobuf:"bytes,11,opt,name=expression_request,json=expressionRequest,proto3,oneof"`
}

type SendGameImMsgRequest_JoinTeamRequest_ struct {
	JoinTeamRequest *SendGameImMsgRequest_JoinTeamRequest `protobuf:"bytes,12,opt,name=join_team_request,json=joinTeamRequest,proto3,oneof"`
}

func (*SendGameImMsgRequest_TextRequest_) isSendGameImMsgRequest_Request() {}

func (*SendGameImMsgRequest_CancelRequest_) isSendGameImMsgRequest_Request() {}

func (*SendGameImMsgRequest_DelRequest_) isSendGameImMsgRequest_Request() {}

func (*SendGameImMsgRequest_AtRequest_) isSendGameImMsgRequest_Request() {}

func (*SendGameImMsgRequest_QuoteRequest_) isSendGameImMsgRequest_Request() {}

func (*SendGameImMsgRequest_GameScreenshotRequest) isSendGameImMsgRequest_Request() {}

func (*SendGameImMsgRequest_FormTeamRequest_) isSendGameImMsgRequest_Request() {}

func (*SendGameImMsgRequest_InviteRoomRequest_) isSendGameImMsgRequest_Request() {}

func (*SendGameImMsgRequest_ExpressionRequest_) isSendGameImMsgRequest_Request() {}

func (*SendGameImMsgRequest_JoinTeamRequest_) isSendGameImMsgRequest_Request() {}

func (m *SendGameImMsgRequest) GetRequest() isSendGameImMsgRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (m *SendGameImMsgRequest) GetTextRequest() *SendGameImMsgRequest_TextRequest {
	if x, ok := m.GetRequest().(*SendGameImMsgRequest_TextRequest_); ok {
		return x.TextRequest
	}
	return nil
}

func (m *SendGameImMsgRequest) GetCancelRequest() *SendGameImMsgRequest_CancelRequest {
	if x, ok := m.GetRequest().(*SendGameImMsgRequest_CancelRequest_); ok {
		return x.CancelRequest
	}
	return nil
}

func (m *SendGameImMsgRequest) GetDelRequest() *SendGameImMsgRequest_DelRequest {
	if x, ok := m.GetRequest().(*SendGameImMsgRequest_DelRequest_); ok {
		return x.DelRequest
	}
	return nil
}

func (m *SendGameImMsgRequest) GetAtRequest() *SendGameImMsgRequest_AtRequest {
	if x, ok := m.GetRequest().(*SendGameImMsgRequest_AtRequest_); ok {
		return x.AtRequest
	}
	return nil
}

func (m *SendGameImMsgRequest) GetQuoteRequest() *SendGameImMsgRequest_QuoteRequest {
	if x, ok := m.GetRequest().(*SendGameImMsgRequest_QuoteRequest_); ok {
		return x.QuoteRequest
	}
	return nil
}

func (m *SendGameImMsgRequest) GetGameScreenshotRequest() *SendGameImMsgRequest_GameScreenShotMsgRequest {
	if x, ok := m.GetRequest().(*SendGameImMsgRequest_GameScreenshotRequest); ok {
		return x.GameScreenshotRequest
	}
	return nil
}

func (m *SendGameImMsgRequest) GetFormTeamRequest() *SendGameImMsgRequest_FormTeamRequest {
	if x, ok := m.GetRequest().(*SendGameImMsgRequest_FormTeamRequest_); ok {
		return x.FormTeamRequest
	}
	return nil
}

func (m *SendGameImMsgRequest) GetInviteRoomRequest() *SendGameImMsgRequest_InviteRoomRequest {
	if x, ok := m.GetRequest().(*SendGameImMsgRequest_InviteRoomRequest_); ok {
		return x.InviteRoomRequest
	}
	return nil
}

func (m *SendGameImMsgRequest) GetExpressionRequest() *SendGameImMsgRequest_ExpressionRequest {
	if x, ok := m.GetRequest().(*SendGameImMsgRequest_ExpressionRequest_); ok {
		return x.ExpressionRequest
	}
	return nil
}

func (m *SendGameImMsgRequest) GetJoinTeamRequest() *SendGameImMsgRequest_JoinTeamRequest {
	if x, ok := m.GetRequest().(*SendGameImMsgRequest_JoinTeamRequest_); ok {
		return x.JoinTeamRequest
	}
	return nil
}

func (m *SendGameImMsgRequest) GetClientMsgTag() int64 {
	if m != nil {
		return m.ClientMsgTag
	}
	return 0
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*SendGameImMsgRequest) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _SendGameImMsgRequest_OneofMarshaler, _SendGameImMsgRequest_OneofUnmarshaler, _SendGameImMsgRequest_OneofSizer, []interface{}{
		(*SendGameImMsgRequest_TextRequest_)(nil),
		(*SendGameImMsgRequest_CancelRequest_)(nil),
		(*SendGameImMsgRequest_DelRequest_)(nil),
		(*SendGameImMsgRequest_AtRequest_)(nil),
		(*SendGameImMsgRequest_QuoteRequest_)(nil),
		(*SendGameImMsgRequest_GameScreenshotRequest)(nil),
		(*SendGameImMsgRequest_FormTeamRequest_)(nil),
		(*SendGameImMsgRequest_InviteRoomRequest_)(nil),
		(*SendGameImMsgRequest_ExpressionRequest_)(nil),
		(*SendGameImMsgRequest_JoinTeamRequest_)(nil),
	}
}

func _SendGameImMsgRequest_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*SendGameImMsgRequest)
	// request
	switch x := m.Request.(type) {
	case *SendGameImMsgRequest_TextRequest_:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.TextRequest); err != nil {
			return err
		}
	case *SendGameImMsgRequest_CancelRequest_:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.CancelRequest); err != nil {
			return err
		}
	case *SendGameImMsgRequest_DelRequest_:
		b.EncodeVarint(5<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.DelRequest); err != nil {
			return err
		}
	case *SendGameImMsgRequest_AtRequest_:
		b.EncodeVarint(6<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.AtRequest); err != nil {
			return err
		}
	case *SendGameImMsgRequest_QuoteRequest_:
		b.EncodeVarint(7<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.QuoteRequest); err != nil {
			return err
		}
	case *SendGameImMsgRequest_GameScreenshotRequest:
		b.EncodeVarint(8<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.GameScreenshotRequest); err != nil {
			return err
		}
	case *SendGameImMsgRequest_FormTeamRequest_:
		b.EncodeVarint(9<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.FormTeamRequest); err != nil {
			return err
		}
	case *SendGameImMsgRequest_InviteRoomRequest_:
		b.EncodeVarint(10<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.InviteRoomRequest); err != nil {
			return err
		}
	case *SendGameImMsgRequest_ExpressionRequest_:
		b.EncodeVarint(11<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ExpressionRequest); err != nil {
			return err
		}
	case *SendGameImMsgRequest_JoinTeamRequest_:
		b.EncodeVarint(12<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.JoinTeamRequest); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("SendGameImMsgRequest.Request has unexpected type %T", x)
	}
	return nil
}

func _SendGameImMsgRequest_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*SendGameImMsgRequest)
	switch tag {
	case 3: // request.text_request
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SendGameImMsgRequest_TextRequest)
		err := b.DecodeMessage(msg)
		m.Request = &SendGameImMsgRequest_TextRequest_{msg}
		return true, err
	case 4: // request.cancel_request
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SendGameImMsgRequest_CancelRequest)
		err := b.DecodeMessage(msg)
		m.Request = &SendGameImMsgRequest_CancelRequest_{msg}
		return true, err
	case 5: // request.del_request
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SendGameImMsgRequest_DelRequest)
		err := b.DecodeMessage(msg)
		m.Request = &SendGameImMsgRequest_DelRequest_{msg}
		return true, err
	case 6: // request.at_request
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SendGameImMsgRequest_AtRequest)
		err := b.DecodeMessage(msg)
		m.Request = &SendGameImMsgRequest_AtRequest_{msg}
		return true, err
	case 7: // request.quote_request
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SendGameImMsgRequest_QuoteRequest)
		err := b.DecodeMessage(msg)
		m.Request = &SendGameImMsgRequest_QuoteRequest_{msg}
		return true, err
	case 8: // request.game_screenshot_request
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SendGameImMsgRequest_GameScreenShotMsgRequest)
		err := b.DecodeMessage(msg)
		m.Request = &SendGameImMsgRequest_GameScreenshotRequest{msg}
		return true, err
	case 9: // request.form_team_request
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SendGameImMsgRequest_FormTeamRequest)
		err := b.DecodeMessage(msg)
		m.Request = &SendGameImMsgRequest_FormTeamRequest_{msg}
		return true, err
	case 10: // request.invite_room_request
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SendGameImMsgRequest_InviteRoomRequest)
		err := b.DecodeMessage(msg)
		m.Request = &SendGameImMsgRequest_InviteRoomRequest_{msg}
		return true, err
	case 11: // request.expression_request
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SendGameImMsgRequest_ExpressionRequest)
		err := b.DecodeMessage(msg)
		m.Request = &SendGameImMsgRequest_ExpressionRequest_{msg}
		return true, err
	case 12: // request.join_team_request
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SendGameImMsgRequest_JoinTeamRequest)
		err := b.DecodeMessage(msg)
		m.Request = &SendGameImMsgRequest_JoinTeamRequest_{msg}
		return true, err
	default:
		return false, nil
	}
}

func _SendGameImMsgRequest_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*SendGameImMsgRequest)
	// request
	switch x := m.Request.(type) {
	case *SendGameImMsgRequest_TextRequest_:
		s := proto.Size(x.TextRequest)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *SendGameImMsgRequest_CancelRequest_:
		s := proto.Size(x.CancelRequest)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *SendGameImMsgRequest_DelRequest_:
		s := proto.Size(x.DelRequest)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *SendGameImMsgRequest_AtRequest_:
		s := proto.Size(x.AtRequest)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *SendGameImMsgRequest_QuoteRequest_:
		s := proto.Size(x.QuoteRequest)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *SendGameImMsgRequest_GameScreenshotRequest:
		s := proto.Size(x.GameScreenshotRequest)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *SendGameImMsgRequest_FormTeamRequest_:
		s := proto.Size(x.FormTeamRequest)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *SendGameImMsgRequest_InviteRoomRequest_:
		s := proto.Size(x.InviteRoomRequest)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *SendGameImMsgRequest_ExpressionRequest_:
		s := proto.Size(x.ExpressionRequest)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *SendGameImMsgRequest_JoinTeamRequest_:
		s := proto.Size(x.JoinTeamRequest)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type SendGameImMsgRequest_TextRequest struct {
	Text                 string   `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGameImMsgRequest_TextRequest) Reset()         { *m = SendGameImMsgRequest_TextRequest{} }
func (m *SendGameImMsgRequest_TextRequest) String() string { return proto.CompactTextString(m) }
func (*SendGameImMsgRequest_TextRequest) ProtoMessage()    {}
func (*SendGameImMsgRequest_TextRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{18, 0}
}
func (m *SendGameImMsgRequest_TextRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGameImMsgRequest_TextRequest.Unmarshal(m, b)
}
func (m *SendGameImMsgRequest_TextRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGameImMsgRequest_TextRequest.Marshal(b, m, deterministic)
}
func (dst *SendGameImMsgRequest_TextRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGameImMsgRequest_TextRequest.Merge(dst, src)
}
func (m *SendGameImMsgRequest_TextRequest) XXX_Size() int {
	return xxx_messageInfo_SendGameImMsgRequest_TextRequest.Size(m)
}
func (m *SendGameImMsgRequest_TextRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGameImMsgRequest_TextRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendGameImMsgRequest_TextRequest proto.InternalMessageInfo

func (m *SendGameImMsgRequest_TextRequest) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type SendGameImMsgRequest_CancelRequest struct {
	CancelMsgId          uint64   `protobuf:"varint,1,opt,name=cancel_msg_id,json=cancelMsgId,proto3" json:"cancel_msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGameImMsgRequest_CancelRequest) Reset()         { *m = SendGameImMsgRequest_CancelRequest{} }
func (m *SendGameImMsgRequest_CancelRequest) String() string { return proto.CompactTextString(m) }
func (*SendGameImMsgRequest_CancelRequest) ProtoMessage()    {}
func (*SendGameImMsgRequest_CancelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{18, 1}
}
func (m *SendGameImMsgRequest_CancelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGameImMsgRequest_CancelRequest.Unmarshal(m, b)
}
func (m *SendGameImMsgRequest_CancelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGameImMsgRequest_CancelRequest.Marshal(b, m, deterministic)
}
func (dst *SendGameImMsgRequest_CancelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGameImMsgRequest_CancelRequest.Merge(dst, src)
}
func (m *SendGameImMsgRequest_CancelRequest) XXX_Size() int {
	return xxx_messageInfo_SendGameImMsgRequest_CancelRequest.Size(m)
}
func (m *SendGameImMsgRequest_CancelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGameImMsgRequest_CancelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendGameImMsgRequest_CancelRequest proto.InternalMessageInfo

func (m *SendGameImMsgRequest_CancelRequest) GetCancelMsgId() uint64 {
	if m != nil {
		return m.CancelMsgId
	}
	return 0
}

type SendGameImMsgRequest_DelRequest struct {
	DelMsgId             uint64   `protobuf:"varint,1,opt,name=del_msg_id,json=delMsgId,proto3" json:"del_msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGameImMsgRequest_DelRequest) Reset()         { *m = SendGameImMsgRequest_DelRequest{} }
func (m *SendGameImMsgRequest_DelRequest) String() string { return proto.CompactTextString(m) }
func (*SendGameImMsgRequest_DelRequest) ProtoMessage()    {}
func (*SendGameImMsgRequest_DelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{18, 2}
}
func (m *SendGameImMsgRequest_DelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGameImMsgRequest_DelRequest.Unmarshal(m, b)
}
func (m *SendGameImMsgRequest_DelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGameImMsgRequest_DelRequest.Marshal(b, m, deterministic)
}
func (dst *SendGameImMsgRequest_DelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGameImMsgRequest_DelRequest.Merge(dst, src)
}
func (m *SendGameImMsgRequest_DelRequest) XXX_Size() int {
	return xxx_messageInfo_SendGameImMsgRequest_DelRequest.Size(m)
}
func (m *SendGameImMsgRequest_DelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGameImMsgRequest_DelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendGameImMsgRequest_DelRequest proto.InternalMessageInfo

func (m *SendGameImMsgRequest_DelRequest) GetDelMsgId() uint64 {
	if m != nil {
		return m.DelMsgId
	}
	return 0
}

type SendGameImMsgRequest_AtRequest struct {
	AtInfoJson           string   `protobuf:"bytes,1,opt,name=at_info_json,json=atInfoJson,proto3" json:"at_info_json,omitempty"`
	AtUidList            []uint32 `protobuf:"varint,2,rep,packed,name=at_uid_list,json=atUidList,proto3" json:"at_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGameImMsgRequest_AtRequest) Reset()         { *m = SendGameImMsgRequest_AtRequest{} }
func (m *SendGameImMsgRequest_AtRequest) String() string { return proto.CompactTextString(m) }
func (*SendGameImMsgRequest_AtRequest) ProtoMessage()    {}
func (*SendGameImMsgRequest_AtRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{18, 3}
}
func (m *SendGameImMsgRequest_AtRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGameImMsgRequest_AtRequest.Unmarshal(m, b)
}
func (m *SendGameImMsgRequest_AtRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGameImMsgRequest_AtRequest.Marshal(b, m, deterministic)
}
func (dst *SendGameImMsgRequest_AtRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGameImMsgRequest_AtRequest.Merge(dst, src)
}
func (m *SendGameImMsgRequest_AtRequest) XXX_Size() int {
	return xxx_messageInfo_SendGameImMsgRequest_AtRequest.Size(m)
}
func (m *SendGameImMsgRequest_AtRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGameImMsgRequest_AtRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendGameImMsgRequest_AtRequest proto.InternalMessageInfo

func (m *SendGameImMsgRequest_AtRequest) GetAtInfoJson() string {
	if m != nil {
		return m.AtInfoJson
	}
	return ""
}

func (m *SendGameImMsgRequest_AtRequest) GetAtUidList() []uint32 {
	if m != nil {
		return m.AtUidList
	}
	return nil
}

type SendGameImMsgRequest_QuoteRequest struct {
	QuoteMsgId           uint64   `protobuf:"varint,1,opt,name=quote_msg_id,json=quoteMsgId,proto3" json:"quote_msg_id,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	AtUidList            []uint32 `protobuf:"varint,3,rep,packed,name=at_uid_list,json=atUidList,proto3" json:"at_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGameImMsgRequest_QuoteRequest) Reset()         { *m = SendGameImMsgRequest_QuoteRequest{} }
func (m *SendGameImMsgRequest_QuoteRequest) String() string { return proto.CompactTextString(m) }
func (*SendGameImMsgRequest_QuoteRequest) ProtoMessage()    {}
func (*SendGameImMsgRequest_QuoteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{18, 4}
}
func (m *SendGameImMsgRequest_QuoteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGameImMsgRequest_QuoteRequest.Unmarshal(m, b)
}
func (m *SendGameImMsgRequest_QuoteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGameImMsgRequest_QuoteRequest.Marshal(b, m, deterministic)
}
func (dst *SendGameImMsgRequest_QuoteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGameImMsgRequest_QuoteRequest.Merge(dst, src)
}
func (m *SendGameImMsgRequest_QuoteRequest) XXX_Size() int {
	return xxx_messageInfo_SendGameImMsgRequest_QuoteRequest.Size(m)
}
func (m *SendGameImMsgRequest_QuoteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGameImMsgRequest_QuoteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendGameImMsgRequest_QuoteRequest proto.InternalMessageInfo

func (m *SendGameImMsgRequest_QuoteRequest) GetQuoteMsgId() uint64 {
	if m != nil {
		return m.QuoteMsgId
	}
	return 0
}

func (m *SendGameImMsgRequest_QuoteRequest) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *SendGameImMsgRequest_QuoteRequest) GetAtUidList() []uint32 {
	if m != nil {
		return m.AtUidList
	}
	return nil
}

type SendGameImMsgRequest_GameScreenShotMsgRequest struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Pos                  uint32   `protobuf:"varint,2,opt,name=pos,proto3" json:"pos,omitempty"`
	Height               uint32   `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	Width                uint32   `protobuf:"varint,4,opt,name=width,proto3" json:"width,omitempty"`
	TotalSendNum         uint32   `protobuf:"varint,5,opt,name=total_send_num,json=totalSendNum,proto3" json:"total_send_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGameImMsgRequest_GameScreenShotMsgRequest) Reset() {
	*m = SendGameImMsgRequest_GameScreenShotMsgRequest{}
}
func (m *SendGameImMsgRequest_GameScreenShotMsgRequest) String() string {
	return proto.CompactTextString(m)
}
func (*SendGameImMsgRequest_GameScreenShotMsgRequest) ProtoMessage() {}
func (*SendGameImMsgRequest_GameScreenShotMsgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{18, 5}
}
func (m *SendGameImMsgRequest_GameScreenShotMsgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGameImMsgRequest_GameScreenShotMsgRequest.Unmarshal(m, b)
}
func (m *SendGameImMsgRequest_GameScreenShotMsgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGameImMsgRequest_GameScreenShotMsgRequest.Marshal(b, m, deterministic)
}
func (dst *SendGameImMsgRequest_GameScreenShotMsgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGameImMsgRequest_GameScreenShotMsgRequest.Merge(dst, src)
}
func (m *SendGameImMsgRequest_GameScreenShotMsgRequest) XXX_Size() int {
	return xxx_messageInfo_SendGameImMsgRequest_GameScreenShotMsgRequest.Size(m)
}
func (m *SendGameImMsgRequest_GameScreenShotMsgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGameImMsgRequest_GameScreenShotMsgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendGameImMsgRequest_GameScreenShotMsgRequest proto.InternalMessageInfo

func (m *SendGameImMsgRequest_GameScreenShotMsgRequest) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SendGameImMsgRequest_GameScreenShotMsgRequest) GetPos() uint32 {
	if m != nil {
		return m.Pos
	}
	return 0
}

func (m *SendGameImMsgRequest_GameScreenShotMsgRequest) GetHeight() uint32 {
	if m != nil {
		return m.Height
	}
	return 0
}

func (m *SendGameImMsgRequest_GameScreenShotMsgRequest) GetWidth() uint32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *SendGameImMsgRequest_GameScreenShotMsgRequest) GetTotalSendNum() uint32 {
	if m != nil {
		return m.TotalSendNum
	}
	return 0
}

type SendGameImMsgRequest_FormTeamRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGameImMsgRequest_FormTeamRequest) Reset()         { *m = SendGameImMsgRequest_FormTeamRequest{} }
func (m *SendGameImMsgRequest_FormTeamRequest) String() string { return proto.CompactTextString(m) }
func (*SendGameImMsgRequest_FormTeamRequest) ProtoMessage()    {}
func (*SendGameImMsgRequest_FormTeamRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{18, 6}
}
func (m *SendGameImMsgRequest_FormTeamRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGameImMsgRequest_FormTeamRequest.Unmarshal(m, b)
}
func (m *SendGameImMsgRequest_FormTeamRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGameImMsgRequest_FormTeamRequest.Marshal(b, m, deterministic)
}
func (dst *SendGameImMsgRequest_FormTeamRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGameImMsgRequest_FormTeamRequest.Merge(dst, src)
}
func (m *SendGameImMsgRequest_FormTeamRequest) XXX_Size() int {
	return xxx_messageInfo_SendGameImMsgRequest_FormTeamRequest.Size(m)
}
func (m *SendGameImMsgRequest_FormTeamRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGameImMsgRequest_FormTeamRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendGameImMsgRequest_FormTeamRequest proto.InternalMessageInfo

type SendGameImMsgRequest_InviteRoomRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGameImMsgRequest_InviteRoomRequest) Reset() {
	*m = SendGameImMsgRequest_InviteRoomRequest{}
}
func (m *SendGameImMsgRequest_InviteRoomRequest) String() string { return proto.CompactTextString(m) }
func (*SendGameImMsgRequest_InviteRoomRequest) ProtoMessage()    {}
func (*SendGameImMsgRequest_InviteRoomRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{18, 7}
}
func (m *SendGameImMsgRequest_InviteRoomRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGameImMsgRequest_InviteRoomRequest.Unmarshal(m, b)
}
func (m *SendGameImMsgRequest_InviteRoomRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGameImMsgRequest_InviteRoomRequest.Marshal(b, m, deterministic)
}
func (dst *SendGameImMsgRequest_InviteRoomRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGameImMsgRequest_InviteRoomRequest.Merge(dst, src)
}
func (m *SendGameImMsgRequest_InviteRoomRequest) XXX_Size() int {
	return xxx_messageInfo_SendGameImMsgRequest_InviteRoomRequest.Size(m)
}
func (m *SendGameImMsgRequest_InviteRoomRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGameImMsgRequest_InviteRoomRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendGameImMsgRequest_InviteRoomRequest proto.InternalMessageInfo

type SendGameImMsgRequest_ExpressionRequest struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Height               uint32   `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	Width                uint32   `protobuf:"varint,4,opt,name=width,proto3" json:"width,omitempty"`
	EmojiType            string   `protobuf:"bytes,5,opt,name=emoji_type,json=emojiType,proto3" json:"emoji_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGameImMsgRequest_ExpressionRequest) Reset() {
	*m = SendGameImMsgRequest_ExpressionRequest{}
}
func (m *SendGameImMsgRequest_ExpressionRequest) String() string { return proto.CompactTextString(m) }
func (*SendGameImMsgRequest_ExpressionRequest) ProtoMessage()    {}
func (*SendGameImMsgRequest_ExpressionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{18, 8}
}
func (m *SendGameImMsgRequest_ExpressionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGameImMsgRequest_ExpressionRequest.Unmarshal(m, b)
}
func (m *SendGameImMsgRequest_ExpressionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGameImMsgRequest_ExpressionRequest.Marshal(b, m, deterministic)
}
func (dst *SendGameImMsgRequest_ExpressionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGameImMsgRequest_ExpressionRequest.Merge(dst, src)
}
func (m *SendGameImMsgRequest_ExpressionRequest) XXX_Size() int {
	return xxx_messageInfo_SendGameImMsgRequest_ExpressionRequest.Size(m)
}
func (m *SendGameImMsgRequest_ExpressionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGameImMsgRequest_ExpressionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendGameImMsgRequest_ExpressionRequest proto.InternalMessageInfo

func (m *SendGameImMsgRequest_ExpressionRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SendGameImMsgRequest_ExpressionRequest) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *SendGameImMsgRequest_ExpressionRequest) GetHeight() uint32 {
	if m != nil {
		return m.Height
	}
	return 0
}

func (m *SendGameImMsgRequest_ExpressionRequest) GetWidth() uint32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *SendGameImMsgRequest_ExpressionRequest) GetEmojiType() string {
	if m != nil {
		return m.EmojiType
	}
	return ""
}

type SendGameImMsgRequest_JoinTeamRequest struct {
	TeamMsgId            uint64   `protobuf:"varint,1,opt,name=team_msg_id,json=teamMsgId,proto3" json:"team_msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGameImMsgRequest_JoinTeamRequest) Reset()         { *m = SendGameImMsgRequest_JoinTeamRequest{} }
func (m *SendGameImMsgRequest_JoinTeamRequest) String() string { return proto.CompactTextString(m) }
func (*SendGameImMsgRequest_JoinTeamRequest) ProtoMessage()    {}
func (*SendGameImMsgRequest_JoinTeamRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{18, 9}
}
func (m *SendGameImMsgRequest_JoinTeamRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGameImMsgRequest_JoinTeamRequest.Unmarshal(m, b)
}
func (m *SendGameImMsgRequest_JoinTeamRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGameImMsgRequest_JoinTeamRequest.Marshal(b, m, deterministic)
}
func (dst *SendGameImMsgRequest_JoinTeamRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGameImMsgRequest_JoinTeamRequest.Merge(dst, src)
}
func (m *SendGameImMsgRequest_JoinTeamRequest) XXX_Size() int {
	return xxx_messageInfo_SendGameImMsgRequest_JoinTeamRequest.Size(m)
}
func (m *SendGameImMsgRequest_JoinTeamRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGameImMsgRequest_JoinTeamRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendGameImMsgRequest_JoinTeamRequest proto.InternalMessageInfo

func (m *SendGameImMsgRequest_JoinTeamRequest) GetTeamMsgId() uint64 {
	if m != nil {
		return m.TeamMsgId
	}
	return 0
}

type SendGameImMsgResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Msg                  *GameImMsg    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SendGameImMsgResponse) Reset()         { *m = SendGameImMsgResponse{} }
func (m *SendGameImMsgResponse) String() string { return proto.CompactTextString(m) }
func (*SendGameImMsgResponse) ProtoMessage()    {}
func (*SendGameImMsgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{19}
}
func (m *SendGameImMsgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGameImMsgResponse.Unmarshal(m, b)
}
func (m *SendGameImMsgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGameImMsgResponse.Marshal(b, m, deterministic)
}
func (dst *SendGameImMsgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGameImMsgResponse.Merge(dst, src)
}
func (m *SendGameImMsgResponse) XXX_Size() int {
	return xxx_messageInfo_SendGameImMsgResponse.Size(m)
}
func (m *SendGameImMsgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGameImMsgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SendGameImMsgResponse proto.InternalMessageInfo

func (m *SendGameImMsgResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SendGameImMsgResponse) GetMsg() *GameImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

type JoinGameHallTeamNotify struct {
	// 消息id,即seq_id
	MsgId uint64 `protobuf:"varint,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	// 是否接收消息开关，true-接收，false-不接收，但是也要展示红点
	RecMsgSwitch bool `protobuf:"varint,2,opt,name=rec_msg_switch,json=recMsgSwitch,proto3" json:"rec_msg_switch,omitempty"`
	// 标题, 玩法名称+固定文案
	Title string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// 副标题 xxxx（昵称）报名了您的开黑组队
	SubTitle string `protobuf:"bytes,4,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	// 报名者信息
	MemInfo *GameHallTeamMemInfo `protobuf:"bytes,5,opt,name=mem_info,json=memInfo,proto3" json:"mem_info,omitempty"`
	// 玩法id
	TabId                uint32   `protobuf:"varint,6,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinGameHallTeamNotify) Reset()         { *m = JoinGameHallTeamNotify{} }
func (m *JoinGameHallTeamNotify) String() string { return proto.CompactTextString(m) }
func (*JoinGameHallTeamNotify) ProtoMessage()    {}
func (*JoinGameHallTeamNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{20}
}
func (m *JoinGameHallTeamNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinGameHallTeamNotify.Unmarshal(m, b)
}
func (m *JoinGameHallTeamNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinGameHallTeamNotify.Marshal(b, m, deterministic)
}
func (dst *JoinGameHallTeamNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinGameHallTeamNotify.Merge(dst, src)
}
func (m *JoinGameHallTeamNotify) XXX_Size() int {
	return xxx_messageInfo_JoinGameHallTeamNotify.Size(m)
}
func (m *JoinGameHallTeamNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinGameHallTeamNotify.DiscardUnknown(m)
}

var xxx_messageInfo_JoinGameHallTeamNotify proto.InternalMessageInfo

func (m *JoinGameHallTeamNotify) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *JoinGameHallTeamNotify) GetRecMsgSwitch() bool {
	if m != nil {
		return m.RecMsgSwitch
	}
	return false
}

func (m *JoinGameHallTeamNotify) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *JoinGameHallTeamNotify) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *JoinGameHallTeamNotify) GetMemInfo() *GameHallTeamMemInfo {
	if m != nil {
		return m.MemInfo
	}
	return nil
}

func (m *JoinGameHallTeamNotify) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

// const unsigned int CMD_GetGameHallTeamList = 5504;    // 拉取组队信息
type GetGameHallTeamListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGameHallTeamListReq) Reset()         { *m = GetGameHallTeamListReq{} }
func (m *GetGameHallTeamListReq) String() string { return proto.CompactTextString(m) }
func (*GetGameHallTeamListReq) ProtoMessage()    {}
func (*GetGameHallTeamListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{21}
}
func (m *GetGameHallTeamListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameHallTeamListReq.Unmarshal(m, b)
}
func (m *GetGameHallTeamListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameHallTeamListReq.Marshal(b, m, deterministic)
}
func (dst *GetGameHallTeamListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameHallTeamListReq.Merge(dst, src)
}
func (m *GetGameHallTeamListReq) XXX_Size() int {
	return xxx_messageInfo_GetGameHallTeamListReq.Size(m)
}
func (m *GetGameHallTeamListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameHallTeamListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameHallTeamListReq proto.InternalMessageInfo

func (m *GetGameHallTeamListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameHallTeamListReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GameHallTeamMemInfo struct {
	BaseInfo *GameImUserInfo `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	// 加入时间, 毫秒
	JoinTime             uint64   `protobuf:"varint,2,opt,name=join_time,json=joinTime,proto3" json:"join_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameHallTeamMemInfo) Reset()         { *m = GameHallTeamMemInfo{} }
func (m *GameHallTeamMemInfo) String() string { return proto.CompactTextString(m) }
func (*GameHallTeamMemInfo) ProtoMessage()    {}
func (*GameHallTeamMemInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{22}
}
func (m *GameHallTeamMemInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameHallTeamMemInfo.Unmarshal(m, b)
}
func (m *GameHallTeamMemInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameHallTeamMemInfo.Marshal(b, m, deterministic)
}
func (dst *GameHallTeamMemInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameHallTeamMemInfo.Merge(dst, src)
}
func (m *GameHallTeamMemInfo) XXX_Size() int {
	return xxx_messageInfo_GameHallTeamMemInfo.Size(m)
}
func (m *GameHallTeamMemInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameHallTeamMemInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameHallTeamMemInfo proto.InternalMessageInfo

func (m *GameHallTeamMemInfo) GetBaseInfo() *GameImUserInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *GameHallTeamMemInfo) GetJoinTime() uint64 {
	if m != nil {
		return m.JoinTime
	}
	return 0
}

type GetGameHallTeamListResp struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MemInfo              []*GameHallTeamMemInfo `protobuf:"bytes,2,rep,name=mem_info,json=memInfo,proto3" json:"mem_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetGameHallTeamListResp) Reset()         { *m = GetGameHallTeamListResp{} }
func (m *GetGameHallTeamListResp) String() string { return proto.CompactTextString(m) }
func (*GetGameHallTeamListResp) ProtoMessage()    {}
func (*GetGameHallTeamListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{23}
}
func (m *GetGameHallTeamListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameHallTeamListResp.Unmarshal(m, b)
}
func (m *GetGameHallTeamListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameHallTeamListResp.Marshal(b, m, deterministic)
}
func (dst *GetGameHallTeamListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameHallTeamListResp.Merge(dst, src)
}
func (m *GetGameHallTeamListResp) XXX_Size() int {
	return xxx_messageInfo_GetGameHallTeamListResp.Size(m)
}
func (m *GetGameHallTeamListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameHallTeamListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameHallTeamListResp proto.InternalMessageInfo

func (m *GetGameHallTeamListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGameHallTeamListResp) GetMemInfo() []*GameHallTeamMemInfo {
	if m != nil {
		return m.MemInfo
	}
	return nil
}

// const unsigned int CMD_CheckHallEnterRoom = 5505;     // 组队大厅是否可进房前置判断
type CheckHallEnterRoomReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	MsgId                uint64       `protobuf:"varint,2,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	SendUid              uint32       `protobuf:"varint,3,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	Cid                  uint32       `protobuf:"varint,4,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckHallEnterRoomReq) Reset()         { *m = CheckHallEnterRoomReq{} }
func (m *CheckHallEnterRoomReq) String() string { return proto.CompactTextString(m) }
func (*CheckHallEnterRoomReq) ProtoMessage()    {}
func (*CheckHallEnterRoomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{24}
}
func (m *CheckHallEnterRoomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckHallEnterRoomReq.Unmarshal(m, b)
}
func (m *CheckHallEnterRoomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckHallEnterRoomReq.Marshal(b, m, deterministic)
}
func (dst *CheckHallEnterRoomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckHallEnterRoomReq.Merge(dst, src)
}
func (m *CheckHallEnterRoomReq) XXX_Size() int {
	return xxx_messageInfo_CheckHallEnterRoomReq.Size(m)
}
func (m *CheckHallEnterRoomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckHallEnterRoomReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckHallEnterRoomReq proto.InternalMessageInfo

func (m *CheckHallEnterRoomReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckHallEnterRoomReq) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *CheckHallEnterRoomReq) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *CheckHallEnterRoomReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

type CheckHallEnterRoomResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Token                string        `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CheckHallEnterRoomResp) Reset()         { *m = CheckHallEnterRoomResp{} }
func (m *CheckHallEnterRoomResp) String() string { return proto.CompactTextString(m) }
func (*CheckHallEnterRoomResp) ProtoMessage()    {}
func (*CheckHallEnterRoomResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{25}
}
func (m *CheckHallEnterRoomResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckHallEnterRoomResp.Unmarshal(m, b)
}
func (m *CheckHallEnterRoomResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckHallEnterRoomResp.Marshal(b, m, deterministic)
}
func (dst *CheckHallEnterRoomResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckHallEnterRoomResp.Merge(dst, src)
}
func (m *CheckHallEnterRoomResp) XXX_Size() int {
	return xxx_messageInfo_CheckHallEnterRoomResp.Size(m)
}
func (m *CheckHallEnterRoomResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckHallEnterRoomResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckHallEnterRoomResp proto.InternalMessageInfo

func (m *CheckHallEnterRoomResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckHallEnterRoomResp) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

//  const unsigned int CMD_GetUserUnreadAtMsg = 5507;    // 拉取用户未读的@消息
type GetUserUnreadAtMsgReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserUnreadAtMsgReq) Reset()         { *m = GetUserUnreadAtMsgReq{} }
func (m *GetUserUnreadAtMsgReq) String() string { return proto.CompactTextString(m) }
func (*GetUserUnreadAtMsgReq) ProtoMessage()    {}
func (*GetUserUnreadAtMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{26}
}
func (m *GetUserUnreadAtMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserUnreadAtMsgReq.Unmarshal(m, b)
}
func (m *GetUserUnreadAtMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserUnreadAtMsgReq.Marshal(b, m, deterministic)
}
func (dst *GetUserUnreadAtMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserUnreadAtMsgReq.Merge(dst, src)
}
func (m *GetUserUnreadAtMsgReq) XXX_Size() int {
	return xxx_messageInfo_GetUserUnreadAtMsgReq.Size(m)
}
func (m *GetUserUnreadAtMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserUnreadAtMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserUnreadAtMsgReq proto.InternalMessageInfo

func (m *GetUserUnreadAtMsgReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserUnreadAtMsgReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetUserUnreadAtMsgResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MsgIds               []uint64      `protobuf:"varint,2,rep,packed,name=msg_ids,json=msgIds,proto3" json:"msg_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserUnreadAtMsgResp) Reset()         { *m = GetUserUnreadAtMsgResp{} }
func (m *GetUserUnreadAtMsgResp) String() string { return proto.CompactTextString(m) }
func (*GetUserUnreadAtMsgResp) ProtoMessage()    {}
func (*GetUserUnreadAtMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{27}
}
func (m *GetUserUnreadAtMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserUnreadAtMsgResp.Unmarshal(m, b)
}
func (m *GetUserUnreadAtMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserUnreadAtMsgResp.Marshal(b, m, deterministic)
}
func (dst *GetUserUnreadAtMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserUnreadAtMsgResp.Merge(dst, src)
}
func (m *GetUserUnreadAtMsgResp) XXX_Size() int {
	return xxx_messageInfo_GetUserUnreadAtMsgResp.Size(m)
}
func (m *GetUserUnreadAtMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserUnreadAtMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserUnreadAtMsgResp proto.InternalMessageInfo

func (m *GetUserUnreadAtMsgResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserUnreadAtMsgResp) GetMsgIds() []uint64 {
	if m != nil {
		return m.MsgIds
	}
	return nil
}

// const unsigned int CMD_MarkAtMsgRead = 5508;    // 标记@消息为已读
type MarkUserAtMsgReadReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	MsgIds               []uint64     `protobuf:"varint,3,rep,packed,name=msg_ids,json=msgIds,proto3" json:"msg_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MarkUserAtMsgReadReq) Reset()         { *m = MarkUserAtMsgReadReq{} }
func (m *MarkUserAtMsgReadReq) String() string { return proto.CompactTextString(m) }
func (*MarkUserAtMsgReadReq) ProtoMessage()    {}
func (*MarkUserAtMsgReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{28}
}
func (m *MarkUserAtMsgReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkUserAtMsgReadReq.Unmarshal(m, b)
}
func (m *MarkUserAtMsgReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkUserAtMsgReadReq.Marshal(b, m, deterministic)
}
func (dst *MarkUserAtMsgReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkUserAtMsgReadReq.Merge(dst, src)
}
func (m *MarkUserAtMsgReadReq) XXX_Size() int {
	return xxx_messageInfo_MarkUserAtMsgReadReq.Size(m)
}
func (m *MarkUserAtMsgReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkUserAtMsgReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkUserAtMsgReadReq proto.InternalMessageInfo

func (m *MarkUserAtMsgReadReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MarkUserAtMsgReadReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *MarkUserAtMsgReadReq) GetMsgIds() []uint64 {
	if m != nil {
		return m.MsgIds
	}
	return nil
}

type MarkUserAtMsgReadResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MarkUserAtMsgReadResp) Reset()         { *m = MarkUserAtMsgReadResp{} }
func (m *MarkUserAtMsgReadResp) String() string { return proto.CompactTextString(m) }
func (*MarkUserAtMsgReadResp) ProtoMessage()    {}
func (*MarkUserAtMsgReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{29}
}
func (m *MarkUserAtMsgReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkUserAtMsgReadResp.Unmarshal(m, b)
}
func (m *MarkUserAtMsgReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkUserAtMsgReadResp.Marshal(b, m, deterministic)
}
func (dst *MarkUserAtMsgReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkUserAtMsgReadResp.Merge(dst, src)
}
func (m *MarkUserAtMsgReadResp) XXX_Size() int {
	return xxx_messageInfo_MarkUserAtMsgReadResp.Size(m)
}
func (m *MarkUserAtMsgReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkUserAtMsgReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkUserAtMsgReadResp proto.InternalMessageInfo

func (m *MarkUserAtMsgReadResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// @消息内容推送
type GameHallAtMsgNotify struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	MsgId                uint64   `protobuf:"varint,2,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Title                string   `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle             string   `protobuf:"bytes,5,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	RecMsgNotify         bool     `protobuf:"varint,6,opt,name=rec_msg_notify,json=recMsgNotify,proto3" json:"rec_msg_notify,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameHallAtMsgNotify) Reset()         { *m = GameHallAtMsgNotify{} }
func (m *GameHallAtMsgNotify) String() string { return proto.CompactTextString(m) }
func (*GameHallAtMsgNotify) ProtoMessage()    {}
func (*GameHallAtMsgNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{30}
}
func (m *GameHallAtMsgNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameHallAtMsgNotify.Unmarshal(m, b)
}
func (m *GameHallAtMsgNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameHallAtMsgNotify.Marshal(b, m, deterministic)
}
func (dst *GameHallAtMsgNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameHallAtMsgNotify.Merge(dst, src)
}
func (m *GameHallAtMsgNotify) XXX_Size() int {
	return xxx_messageInfo_GameHallAtMsgNotify.Size(m)
}
func (m *GameHallAtMsgNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_GameHallAtMsgNotify.DiscardUnknown(m)
}

var xxx_messageInfo_GameHallAtMsgNotify proto.InternalMessageInfo

func (m *GameHallAtMsgNotify) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GameHallAtMsgNotify) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *GameHallAtMsgNotify) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GameHallAtMsgNotify) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GameHallAtMsgNotify) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *GameHallAtMsgNotify) GetRecMsgNotify() bool {
	if m != nil {
		return m.RecMsgNotify
	}
	return false
}

//  const unsigned int CMD_CheckSendInviteRoomCond = 5509;    // 检查用户是否可以发送邀请进房
type CheckSendInviteRoomCondReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	CheckSource          uint32       `protobuf:"varint,3,opt,name=check_source,json=checkSource,proto3" json:"check_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckSendInviteRoomCondReq) Reset()         { *m = CheckSendInviteRoomCondReq{} }
func (m *CheckSendInviteRoomCondReq) String() string { return proto.CompactTextString(m) }
func (*CheckSendInviteRoomCondReq) ProtoMessage()    {}
func (*CheckSendInviteRoomCondReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{31}
}
func (m *CheckSendInviteRoomCondReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckSendInviteRoomCondReq.Unmarshal(m, b)
}
func (m *CheckSendInviteRoomCondReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckSendInviteRoomCondReq.Marshal(b, m, deterministic)
}
func (dst *CheckSendInviteRoomCondReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSendInviteRoomCondReq.Merge(dst, src)
}
func (m *CheckSendInviteRoomCondReq) XXX_Size() int {
	return xxx_messageInfo_CheckSendInviteRoomCondReq.Size(m)
}
func (m *CheckSendInviteRoomCondReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSendInviteRoomCondReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSendInviteRoomCondReq proto.InternalMessageInfo

func (m *CheckSendInviteRoomCondReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckSendInviteRoomCondReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *CheckSendInviteRoomCondReq) GetCheckSource() uint32 {
	if m != nil {
		return m.CheckSource
	}
	return 0
}

type CheckSendInviteRoomCondResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CanInvite            bool          `protobuf:"varint,2,opt,name=can_invite,json=canInvite,proto3" json:"can_invite,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CheckSendInviteRoomCondResp) Reset()         { *m = CheckSendInviteRoomCondResp{} }
func (m *CheckSendInviteRoomCondResp) String() string { return proto.CompactTextString(m) }
func (*CheckSendInviteRoomCondResp) ProtoMessage()    {}
func (*CheckSendInviteRoomCondResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{32}
}
func (m *CheckSendInviteRoomCondResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckSendInviteRoomCondResp.Unmarshal(m, b)
}
func (m *CheckSendInviteRoomCondResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckSendInviteRoomCondResp.Marshal(b, m, deterministic)
}
func (dst *CheckSendInviteRoomCondResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSendInviteRoomCondResp.Merge(dst, src)
}
func (m *CheckSendInviteRoomCondResp) XXX_Size() int {
	return xxx_messageInfo_CheckSendInviteRoomCondResp.Size(m)
}
func (m *CheckSendInviteRoomCondResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSendInviteRoomCondResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSendInviteRoomCondResp proto.InternalMessageInfo

func (m *CheckSendInviteRoomCondResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckSendInviteRoomCondResp) GetCanInvite() bool {
	if m != nil {
		return m.CanInvite
	}
	return false
}

//  const unsigned int CMD_UpdateGameHallNotifyStatus = 5510;    // 更改组队大厅消息通知状态
type UpdateGameHallNotifyStatusReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	OpenNotify           bool         `protobuf:"varint,3,opt,name=open_notify,json=openNotify,proto3" json:"open_notify,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateGameHallNotifyStatusReq) Reset()         { *m = UpdateGameHallNotifyStatusReq{} }
func (m *UpdateGameHallNotifyStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGameHallNotifyStatusReq) ProtoMessage()    {}
func (*UpdateGameHallNotifyStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{33}
}
func (m *UpdateGameHallNotifyStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGameHallNotifyStatusReq.Unmarshal(m, b)
}
func (m *UpdateGameHallNotifyStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGameHallNotifyStatusReq.Marshal(b, m, deterministic)
}
func (dst *UpdateGameHallNotifyStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGameHallNotifyStatusReq.Merge(dst, src)
}
func (m *UpdateGameHallNotifyStatusReq) XXX_Size() int {
	return xxx_messageInfo_UpdateGameHallNotifyStatusReq.Size(m)
}
func (m *UpdateGameHallNotifyStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGameHallNotifyStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGameHallNotifyStatusReq proto.InternalMessageInfo

func (m *UpdateGameHallNotifyStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UpdateGameHallNotifyStatusReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *UpdateGameHallNotifyStatusReq) GetOpenNotify() bool {
	if m != nil {
		return m.OpenNotify
	}
	return false
}

type UpdateGameHallNotifyStatusResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateGameHallNotifyStatusResp) Reset()         { *m = UpdateGameHallNotifyStatusResp{} }
func (m *UpdateGameHallNotifyStatusResp) String() string { return proto.CompactTextString(m) }
func (*UpdateGameHallNotifyStatusResp) ProtoMessage()    {}
func (*UpdateGameHallNotifyStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{34}
}
func (m *UpdateGameHallNotifyStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGameHallNotifyStatusResp.Unmarshal(m, b)
}
func (m *UpdateGameHallNotifyStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGameHallNotifyStatusResp.Marshal(b, m, deterministic)
}
func (dst *UpdateGameHallNotifyStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGameHallNotifyStatusResp.Merge(dst, src)
}
func (m *UpdateGameHallNotifyStatusResp) XXX_Size() int {
	return xxx_messageInfo_UpdateGameHallNotifyStatusResp.Size(m)
}
func (m *UpdateGameHallNotifyStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGameHallNotifyStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGameHallNotifyStatusResp proto.InternalMessageInfo

func (m *UpdateGameHallNotifyStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

//  const unsigned int CMD_GetGameHallNotifyStatus = 5511;    // 拉取组队大厅消息通知状态
type GetGameHallNotifyStatusReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGameHallNotifyStatusReq) Reset()         { *m = GetGameHallNotifyStatusReq{} }
func (m *GetGameHallNotifyStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetGameHallNotifyStatusReq) ProtoMessage()    {}
func (*GetGameHallNotifyStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{35}
}
func (m *GetGameHallNotifyStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameHallNotifyStatusReq.Unmarshal(m, b)
}
func (m *GetGameHallNotifyStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameHallNotifyStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetGameHallNotifyStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameHallNotifyStatusReq.Merge(dst, src)
}
func (m *GetGameHallNotifyStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetGameHallNotifyStatusReq.Size(m)
}
func (m *GetGameHallNotifyStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameHallNotifyStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameHallNotifyStatusReq proto.InternalMessageInfo

func (m *GetGameHallNotifyStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameHallNotifyStatusReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetGameHallNotifyStatusResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsOpen               bool          `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetGameHallNotifyStatusResp) Reset()         { *m = GetGameHallNotifyStatusResp{} }
func (m *GetGameHallNotifyStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetGameHallNotifyStatusResp) ProtoMessage()    {}
func (*GetGameHallNotifyStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{36}
}
func (m *GetGameHallNotifyStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameHallNotifyStatusResp.Unmarshal(m, b)
}
func (m *GetGameHallNotifyStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameHallNotifyStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetGameHallNotifyStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameHallNotifyStatusResp.Merge(dst, src)
}
func (m *GetGameHallNotifyStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetGameHallNotifyStatusResp.Size(m)
}
func (m *GetGameHallNotifyStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameHallNotifyStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameHallNotifyStatusResp proto.InternalMessageInfo

func (m *GetGameHallNotifyStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGameHallNotifyStatusResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

//  const unsigned int CMD_SetShowEntranceSetting = 5512;    // 设置用户组队大厅入口展示
type SetShowEntranceSettingReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	IsOpen               bool         `protobuf:"varint,3,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetShowEntranceSettingReq) Reset()         { *m = SetShowEntranceSettingReq{} }
func (m *SetShowEntranceSettingReq) String() string { return proto.CompactTextString(m) }
func (*SetShowEntranceSettingReq) ProtoMessage()    {}
func (*SetShowEntranceSettingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{37}
}
func (m *SetShowEntranceSettingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetShowEntranceSettingReq.Unmarshal(m, b)
}
func (m *SetShowEntranceSettingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetShowEntranceSettingReq.Marshal(b, m, deterministic)
}
func (dst *SetShowEntranceSettingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetShowEntranceSettingReq.Merge(dst, src)
}
func (m *SetShowEntranceSettingReq) XXX_Size() int {
	return xxx_messageInfo_SetShowEntranceSettingReq.Size(m)
}
func (m *SetShowEntranceSettingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetShowEntranceSettingReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetShowEntranceSettingReq proto.InternalMessageInfo

func (m *SetShowEntranceSettingReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetShowEntranceSettingReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SetShowEntranceSettingReq) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type SetShowEntranceSettingResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetShowEntranceSettingResp) Reset()         { *m = SetShowEntranceSettingResp{} }
func (m *SetShowEntranceSettingResp) String() string { return proto.CompactTextString(m) }
func (*SetShowEntranceSettingResp) ProtoMessage()    {}
func (*SetShowEntranceSettingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{38}
}
func (m *SetShowEntranceSettingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetShowEntranceSettingResp.Unmarshal(m, b)
}
func (m *SetShowEntranceSettingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetShowEntranceSettingResp.Marshal(b, m, deterministic)
}
func (dst *SetShowEntranceSettingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetShowEntranceSettingResp.Merge(dst, src)
}
func (m *SetShowEntranceSettingResp) XXX_Size() int {
	return xxx_messageInfo_SetShowEntranceSettingResp.Size(m)
}
func (m *SetShowEntranceSettingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetShowEntranceSettingResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetShowEntranceSettingResp proto.InternalMessageInfo

func (m *SetShowEntranceSettingResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

//  const unsigned int CMD_GetShowEntranceSetting = 5513;    // 获取用户PC组队大厅入口设置
type GetShowEntranceSettingReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetShowEntranceSettingReq) Reset()         { *m = GetShowEntranceSettingReq{} }
func (m *GetShowEntranceSettingReq) String() string { return proto.CompactTextString(m) }
func (*GetShowEntranceSettingReq) ProtoMessage()    {}
func (*GetShowEntranceSettingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{39}
}
func (m *GetShowEntranceSettingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowEntranceSettingReq.Unmarshal(m, b)
}
func (m *GetShowEntranceSettingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowEntranceSettingReq.Marshal(b, m, deterministic)
}
func (dst *GetShowEntranceSettingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowEntranceSettingReq.Merge(dst, src)
}
func (m *GetShowEntranceSettingReq) XXX_Size() int {
	return xxx_messageInfo_GetShowEntranceSettingReq.Size(m)
}
func (m *GetShowEntranceSettingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowEntranceSettingReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowEntranceSettingReq proto.InternalMessageInfo

func (m *GetShowEntranceSettingReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetShowEntranceSettingReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type EntranceSettingItem struct {
	TabId                uint32     `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string     `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	TabIcon              string     `protobuf:"bytes,3,opt,name=tab_icon,json=tabIcon,proto3" json:"tab_icon,omitempty"`
	Msg                  *GameImMsg `protobuf:"bytes,4,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *EntranceSettingItem) Reset()         { *m = EntranceSettingItem{} }
func (m *EntranceSettingItem) String() string { return proto.CompactTextString(m) }
func (*EntranceSettingItem) ProtoMessage()    {}
func (*EntranceSettingItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{40}
}
func (m *EntranceSettingItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EntranceSettingItem.Unmarshal(m, b)
}
func (m *EntranceSettingItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EntranceSettingItem.Marshal(b, m, deterministic)
}
func (dst *EntranceSettingItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EntranceSettingItem.Merge(dst, src)
}
func (m *EntranceSettingItem) XXX_Size() int {
	return xxx_messageInfo_EntranceSettingItem.Size(m)
}
func (m *EntranceSettingItem) XXX_DiscardUnknown() {
	xxx_messageInfo_EntranceSettingItem.DiscardUnknown(m)
}

var xxx_messageInfo_EntranceSettingItem proto.InternalMessageInfo

func (m *EntranceSettingItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *EntranceSettingItem) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *EntranceSettingItem) GetTabIcon() string {
	if m != nil {
		return m.TabIcon
	}
	return ""
}

func (m *EntranceSettingItem) GetMsg() *GameImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

type GetShowEntranceSettingResp struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	EntranceSettingList  []*EntranceSettingItem `protobuf:"bytes,2,rep,name=entrance_setting_list,json=entranceSettingList,proto3" json:"entrance_setting_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetShowEntranceSettingResp) Reset()         { *m = GetShowEntranceSettingResp{} }
func (m *GetShowEntranceSettingResp) String() string { return proto.CompactTextString(m) }
func (*GetShowEntranceSettingResp) ProtoMessage()    {}
func (*GetShowEntranceSettingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{41}
}
func (m *GetShowEntranceSettingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowEntranceSettingResp.Unmarshal(m, b)
}
func (m *GetShowEntranceSettingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowEntranceSettingResp.Marshal(b, m, deterministic)
}
func (dst *GetShowEntranceSettingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowEntranceSettingResp.Merge(dst, src)
}
func (m *GetShowEntranceSettingResp) XXX_Size() int {
	return xxx_messageInfo_GetShowEntranceSettingResp.Size(m)
}
func (m *GetShowEntranceSettingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowEntranceSettingResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowEntranceSettingResp proto.InternalMessageInfo

func (m *GetShowEntranceSettingResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetShowEntranceSettingResp) GetEntranceSettingList() []*EntranceSettingItem {
	if m != nil {
		return m.EntranceSettingList
	}
	return nil
}

// 常驻入口推送的消息
type GameHallShowEntranceNotify struct {
	Msg                  []*GameImMsg `protobuf:"bytes,1,rep,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GameHallShowEntranceNotify) Reset()         { *m = GameHallShowEntranceNotify{} }
func (m *GameHallShowEntranceNotify) String() string { return proto.CompactTextString(m) }
func (*GameHallShowEntranceNotify) ProtoMessage()    {}
func (*GameHallShowEntranceNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{42}
}
func (m *GameHallShowEntranceNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameHallShowEntranceNotify.Unmarshal(m, b)
}
func (m *GameHallShowEntranceNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameHallShowEntranceNotify.Marshal(b, m, deterministic)
}
func (dst *GameHallShowEntranceNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameHallShowEntranceNotify.Merge(dst, src)
}
func (m *GameHallShowEntranceNotify) XXX_Size() int {
	return xxx_messageInfo_GameHallShowEntranceNotify.Size(m)
}
func (m *GameHallShowEntranceNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_GameHallShowEntranceNotify.DiscardUnknown(m)
}

var xxx_messageInfo_GameHallShowEntranceNotify proto.InternalMessageInfo

func (m *GameHallShowEntranceNotify) GetMsg() []*GameImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

//  const unsigned int CMD_GetGameHallPinConf = 5514;    // 获取组队大厅的置顶配置
type GetGameHallPinConfReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGameHallPinConfReq) Reset()         { *m = GetGameHallPinConfReq{} }
func (m *GetGameHallPinConfReq) String() string { return proto.CompactTextString(m) }
func (*GetGameHallPinConfReq) ProtoMessage()    {}
func (*GetGameHallPinConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{43}
}
func (m *GetGameHallPinConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameHallPinConfReq.Unmarshal(m, b)
}
func (m *GetGameHallPinConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameHallPinConfReq.Marshal(b, m, deterministic)
}
func (dst *GetGameHallPinConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameHallPinConfReq.Merge(dst, src)
}
func (m *GetGameHallPinConfReq) XXX_Size() int {
	return xxx_messageInfo_GetGameHallPinConfReq.Size(m)
}
func (m *GetGameHallPinConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameHallPinConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameHallPinConfReq proto.InternalMessageInfo

func (m *GetGameHallPinConfReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameHallPinConfReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GameHallPinConfItem struct {
	Ttid                 string                               `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Uid                  uint32                               `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string                               `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Img                  string                               `protobuf:"bytes,4,opt,name=img,proto3" json:"img,omitempty"`
	Content              string                               `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	Text                 []*GameHallPinConfItem_HighlightText `protobuf:"bytes,6,rep,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *GameHallPinConfItem) Reset()         { *m = GameHallPinConfItem{} }
func (m *GameHallPinConfItem) String() string { return proto.CompactTextString(m) }
func (*GameHallPinConfItem) ProtoMessage()    {}
func (*GameHallPinConfItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{44}
}
func (m *GameHallPinConfItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameHallPinConfItem.Unmarshal(m, b)
}
func (m *GameHallPinConfItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameHallPinConfItem.Marshal(b, m, deterministic)
}
func (dst *GameHallPinConfItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameHallPinConfItem.Merge(dst, src)
}
func (m *GameHallPinConfItem) XXX_Size() int {
	return xxx_messageInfo_GameHallPinConfItem.Size(m)
}
func (m *GameHallPinConfItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameHallPinConfItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameHallPinConfItem proto.InternalMessageInfo

func (m *GameHallPinConfItem) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *GameHallPinConfItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameHallPinConfItem) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GameHallPinConfItem) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *GameHallPinConfItem) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GameHallPinConfItem) GetText() []*GameHallPinConfItem_HighlightText {
	if m != nil {
		return m.Text
	}
	return nil
}

type GameHallPinConfItem_HighlightText struct {
	Highlight            string   `protobuf:"bytes,2,opt,name=highlight,proto3" json:"highlight,omitempty"`
	Url                  string   `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameHallPinConfItem_HighlightText) Reset()         { *m = GameHallPinConfItem_HighlightText{} }
func (m *GameHallPinConfItem_HighlightText) String() string { return proto.CompactTextString(m) }
func (*GameHallPinConfItem_HighlightText) ProtoMessage()    {}
func (*GameHallPinConfItem_HighlightText) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{44, 0}
}
func (m *GameHallPinConfItem_HighlightText) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameHallPinConfItem_HighlightText.Unmarshal(m, b)
}
func (m *GameHallPinConfItem_HighlightText) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameHallPinConfItem_HighlightText.Marshal(b, m, deterministic)
}
func (dst *GameHallPinConfItem_HighlightText) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameHallPinConfItem_HighlightText.Merge(dst, src)
}
func (m *GameHallPinConfItem_HighlightText) XXX_Size() int {
	return xxx_messageInfo_GameHallPinConfItem_HighlightText.Size(m)
}
func (m *GameHallPinConfItem_HighlightText) XXX_DiscardUnknown() {
	xxx_messageInfo_GameHallPinConfItem_HighlightText.DiscardUnknown(m)
}

var xxx_messageInfo_GameHallPinConfItem_HighlightText proto.InternalMessageInfo

func (m *GameHallPinConfItem_HighlightText) GetHighlight() string {
	if m != nil {
		return m.Highlight
	}
	return ""
}

func (m *GameHallPinConfItem_HighlightText) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type GetGameHallPinConfResp struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PinConfList          []*GameHallPinConfItem `protobuf:"bytes,2,rep,name=pin_conf_list,json=pinConfList,proto3" json:"pin_conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetGameHallPinConfResp) Reset()         { *m = GetGameHallPinConfResp{} }
func (m *GetGameHallPinConfResp) String() string { return proto.CompactTextString(m) }
func (*GetGameHallPinConfResp) ProtoMessage()    {}
func (*GetGameHallPinConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_logic_ca20fd27f990462c, []int{45}
}
func (m *GetGameHallPinConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameHallPinConfResp.Unmarshal(m, b)
}
func (m *GetGameHallPinConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameHallPinConfResp.Marshal(b, m, deterministic)
}
func (dst *GetGameHallPinConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameHallPinConfResp.Merge(dst, src)
}
func (m *GetGameHallPinConfResp) XXX_Size() int {
	return xxx_messageInfo_GetGameHallPinConfResp.Size(m)
}
func (m *GetGameHallPinConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameHallPinConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameHallPinConfResp proto.InternalMessageInfo

func (m *GetGameHallPinConfResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGameHallPinConfResp) GetPinConfList() []*GameHallPinConfItem {
	if m != nil {
		return m.PinConfList
	}
	return nil
}

func init() {
	proto.RegisterType((*GetBuildChannelInfoRequest)(nil), "ga.game_hall_logic.GetBuildChannelInfoRequest")
	proto.RegisterType((*GetBuildChannelInfoResponse)(nil), "ga.game_hall_logic.GetBuildChannelInfoResponse")
	proto.RegisterType((*GameImUserInfo)(nil), "ga.game_hall_logic.GameImUserInfo")
	proto.RegisterType((*GameImUserInfo_GameCardInfo)(nil), "ga.game_hall_logic.GameImUserInfo.GameCardInfo")
	proto.RegisterType((*GameImMsg)(nil), "ga.game_hall_logic.GameImMsg")
	proto.RegisterType((*GameOlStatusMsg)(nil), "ga.game_hall_logic.GameOlStatusMsg")
	proto.RegisterType((*GameOlStatusMsg_OlStatusInfo)(nil), "ga.game_hall_logic.GameOlStatusMsg.OlStatusInfo")
	proto.RegisterType((*GameTextMsg)(nil), "ga.game_hall_logic.GameTextMsg")
	proto.RegisterType((*GameScreenshotMsg)(nil), "ga.game_hall_logic.GameScreenshotMsg")
	proto.RegisterType((*GameCancelMsg)(nil), "ga.game_hall_logic.GameCancelMsg")
	proto.RegisterType((*GameDelMsg)(nil), "ga.game_hall_logic.GameDelMsg")
	proto.RegisterType((*GameAtSomeoneMsg)(nil), "ga.game_hall_logic.GameAtSomeoneMsg")
	proto.RegisterType((*GameQuoteMsg)(nil), "ga.game_hall_logic.GameQuoteMsg")
	proto.RegisterType((*GameFormTeamMsg)(nil), "ga.game_hall_logic.GameFormTeamMsg")
	proto.RegisterType((*GameInviteRoomMsg)(nil), "ga.game_hall_logic.GameInviteRoomMsg")
	proto.RegisterType((*CleanMsg)(nil), "ga.game_hall_logic.CleanMsg")
	proto.RegisterType((*GameExpressionMsg)(nil), "ga.game_hall_logic.GameExpressionMsg")
	proto.RegisterType((*GameBatchCancelMsg)(nil), "ga.game_hall_logic.GameBatchCancelMsg")
	proto.RegisterType((*GetMsgListRequest)(nil), "ga.game_hall_logic.GetMsgListRequest")
	proto.RegisterType((*GetMsgListResponse)(nil), "ga.game_hall_logic.GetMsgListResponse")
	proto.RegisterType((*SendGameImMsgRequest)(nil), "ga.game_hall_logic.SendGameImMsgRequest")
	proto.RegisterType((*SendGameImMsgRequest_TextRequest)(nil), "ga.game_hall_logic.SendGameImMsgRequest.TextRequest")
	proto.RegisterType((*SendGameImMsgRequest_CancelRequest)(nil), "ga.game_hall_logic.SendGameImMsgRequest.CancelRequest")
	proto.RegisterType((*SendGameImMsgRequest_DelRequest)(nil), "ga.game_hall_logic.SendGameImMsgRequest.DelRequest")
	proto.RegisterType((*SendGameImMsgRequest_AtRequest)(nil), "ga.game_hall_logic.SendGameImMsgRequest.AtRequest")
	proto.RegisterType((*SendGameImMsgRequest_QuoteRequest)(nil), "ga.game_hall_logic.SendGameImMsgRequest.QuoteRequest")
	proto.RegisterType((*SendGameImMsgRequest_GameScreenShotMsgRequest)(nil), "ga.game_hall_logic.SendGameImMsgRequest.GameScreenShotMsgRequest")
	proto.RegisterType((*SendGameImMsgRequest_FormTeamRequest)(nil), "ga.game_hall_logic.SendGameImMsgRequest.FormTeamRequest")
	proto.RegisterType((*SendGameImMsgRequest_InviteRoomRequest)(nil), "ga.game_hall_logic.SendGameImMsgRequest.InviteRoomRequest")
	proto.RegisterType((*SendGameImMsgRequest_ExpressionRequest)(nil), "ga.game_hall_logic.SendGameImMsgRequest.ExpressionRequest")
	proto.RegisterType((*SendGameImMsgRequest_JoinTeamRequest)(nil), "ga.game_hall_logic.SendGameImMsgRequest.JoinTeamRequest")
	proto.RegisterType((*SendGameImMsgResponse)(nil), "ga.game_hall_logic.SendGameImMsgResponse")
	proto.RegisterType((*JoinGameHallTeamNotify)(nil), "ga.game_hall_logic.JoinGameHallTeamNotify")
	proto.RegisterType((*GetGameHallTeamListReq)(nil), "ga.game_hall_logic.GetGameHallTeamListReq")
	proto.RegisterType((*GameHallTeamMemInfo)(nil), "ga.game_hall_logic.GameHallTeamMemInfo")
	proto.RegisterType((*GetGameHallTeamListResp)(nil), "ga.game_hall_logic.GetGameHallTeamListResp")
	proto.RegisterType((*CheckHallEnterRoomReq)(nil), "ga.game_hall_logic.CheckHallEnterRoomReq")
	proto.RegisterType((*CheckHallEnterRoomResp)(nil), "ga.game_hall_logic.CheckHallEnterRoomResp")
	proto.RegisterType((*GetUserUnreadAtMsgReq)(nil), "ga.game_hall_logic.GetUserUnreadAtMsgReq")
	proto.RegisterType((*GetUserUnreadAtMsgResp)(nil), "ga.game_hall_logic.GetUserUnreadAtMsgResp")
	proto.RegisterType((*MarkUserAtMsgReadReq)(nil), "ga.game_hall_logic.MarkUserAtMsgReadReq")
	proto.RegisterType((*MarkUserAtMsgReadResp)(nil), "ga.game_hall_logic.MarkUserAtMsgReadResp")
	proto.RegisterType((*GameHallAtMsgNotify)(nil), "ga.game_hall_logic.GameHallAtMsgNotify")
	proto.RegisterType((*CheckSendInviteRoomCondReq)(nil), "ga.game_hall_logic.CheckSendInviteRoomCondReq")
	proto.RegisterType((*CheckSendInviteRoomCondResp)(nil), "ga.game_hall_logic.CheckSendInviteRoomCondResp")
	proto.RegisterType((*UpdateGameHallNotifyStatusReq)(nil), "ga.game_hall_logic.UpdateGameHallNotifyStatusReq")
	proto.RegisterType((*UpdateGameHallNotifyStatusResp)(nil), "ga.game_hall_logic.UpdateGameHallNotifyStatusResp")
	proto.RegisterType((*GetGameHallNotifyStatusReq)(nil), "ga.game_hall_logic.GetGameHallNotifyStatusReq")
	proto.RegisterType((*GetGameHallNotifyStatusResp)(nil), "ga.game_hall_logic.GetGameHallNotifyStatusResp")
	proto.RegisterType((*SetShowEntranceSettingReq)(nil), "ga.game_hall_logic.SetShowEntranceSettingReq")
	proto.RegisterType((*SetShowEntranceSettingResp)(nil), "ga.game_hall_logic.SetShowEntranceSettingResp")
	proto.RegisterType((*GetShowEntranceSettingReq)(nil), "ga.game_hall_logic.GetShowEntranceSettingReq")
	proto.RegisterType((*EntranceSettingItem)(nil), "ga.game_hall_logic.EntranceSettingItem")
	proto.RegisterType((*GetShowEntranceSettingResp)(nil), "ga.game_hall_logic.GetShowEntranceSettingResp")
	proto.RegisterType((*GameHallShowEntranceNotify)(nil), "ga.game_hall_logic.GameHallShowEntranceNotify")
	proto.RegisterType((*GetGameHallPinConfReq)(nil), "ga.game_hall_logic.GetGameHallPinConfReq")
	proto.RegisterType((*GameHallPinConfItem)(nil), "ga.game_hall_logic.GameHallPinConfItem")
	proto.RegisterType((*GameHallPinConfItem_HighlightText)(nil), "ga.game_hall_logic.GameHallPinConfItem.HighlightText")
	proto.RegisterType((*GetGameHallPinConfResp)(nil), "ga.game_hall_logic.GetGameHallPinConfResp")
	proto.RegisterEnum("ga.game_hall_logic.MsgStatus", MsgStatus_name, MsgStatus_value)
	proto.RegisterEnum("ga.game_hall_logic.MsgAction", MsgAction_name, MsgAction_value)
	proto.RegisterEnum("ga.game_hall_logic.GameImMsgType", GameImMsgType_name, GameImMsgType_value)
	proto.RegisterEnum("ga.game_hall_logic.GameImUserStatus", GameImUserStatus_name, GameImUserStatus_value)
	proto.RegisterEnum("ga.game_hall_logic.GameUserStatus", GameUserStatus_name, GameUserStatus_value)
	proto.RegisterEnum("ga.game_hall_logic.ListEntrance", ListEntrance_name, ListEntrance_value)
	proto.RegisterEnum("ga.game_hall_logic.CheckSendInviteRoomSource", CheckSendInviteRoomSource_name, CheckSendInviteRoomSource_value)
	proto.RegisterEnum("ga.game_hall_logic.GetMsgListRequest_ActionType", GetMsgListRequest_ActionType_name, GetMsgListRequest_ActionType_value)
}

func init() {
	proto.RegisterFile("game_hall_logic/game_hall_logic.proto", fileDescriptor_game_hall_logic_ca20fd27f990462c)
}

var fileDescriptor_game_hall_logic_ca20fd27f990462c = []byte{
	// 2831 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x1a, 0x4d, 0x8f, 0xdb, 0xc6,
	0x35, 0x94, 0xb4, 0xfa, 0x78, 0x92, 0xd6, 0x5c, 0xda, 0xeb, 0xd5, 0x6a, 0xed, 0xd8, 0x66, 0x9d,
	0xd4, 0x59, 0x34, 0x72, 0x6b, 0x37, 0x45, 0x90, 0x4b, 0xa0, 0xd5, 0xd2, 0xbb, 0x4a, 0x56, 0xd2,
	0x86, 0x92, 0x9c, 0x4f, 0x80, 0xa0, 0xc8, 0x59, 0x2e, 0x6d, 0x91, 0x94, 0x49, 0xaa, 0x59, 0xa3,
	0x45, 0x8b, 0x36, 0x40, 0xd1, 0x5b, 0x0a, 0xf4, 0x50, 0x20, 0xd7, 0xf6, 0x27, 0x14, 0x3d, 0xf4,
	0xd8, 0x43, 0xef, 0xfd, 0x0d, 0x3d, 0xf4, 0x6f, 0x14, 0x6f, 0x66, 0x28, 0x52, 0x12, 0x15, 0xaf,
	0x6a, 0xbb, 0xb7, 0x99, 0x37, 0x33, 0xef, 0x6b, 0xde, 0xe7, 0x90, 0xf0, 0x96, 0xa5, 0x3b, 0x44,
	0x3b, 0xd7, 0xc7, 0x63, 0x6d, 0xec, 0x59, 0xb6, 0x71, 0x7f, 0x61, 0xde, 0x98, 0xf8, 0x5e, 0xe8,
	0x49, 0x92, 0xa5, 0x37, 0x16, 0x56, 0xea, 0x55, 0x4b, 0xd7, 0x46, 0x7a, 0x40, 0xd8, 0x16, 0xf9,
	0x4b, 0xa8, 0x1f, 0x91, 0xf0, 0x60, 0x6a, 0x8f, 0xcd, 0xd6, 0xb9, 0xee, 0xba, 0x64, 0xdc, 0x76,
	0xcf, 0x3c, 0x95, 0x3c, 0x9b, 0x92, 0x20, 0x94, 0xde, 0x86, 0x22, 0xee, 0xd5, 0x7c, 0xf2, 0xac,
	0x26, 0xdc, 0x16, 0xee, 0x95, 0x1f, 0x94, 0x1b, 0x96, 0xde, 0x38, 0xd0, 0x03, 0xa2, 0x92, 0x67,
	0x6a, 0x61, 0xc4, 0x06, 0xd2, 0x36, 0xe4, 0x43, 0x7d, 0xa4, 0xd9, 0x66, 0x2d, 0x73, 0x5b, 0xb8,
	0x57, 0x55, 0x37, 0x42, 0x7d, 0xd4, 0x36, 0xe5, 0xdf, 0x08, 0xb0, 0x97, 0x8a, 0x3d, 0x98, 0x78,
	0x6e, 0x40, 0xa4, 0x77, 0xa0, 0xc4, 0xd1, 0x07, 0x13, 0x8e, 0xbf, 0x12, 0xe3, 0x0f, 0x26, 0x6a,
	0x71, 0xc4, 0x47, 0xd2, 0x35, 0xd8, 0x08, 0xbd, 0xa7, 0xc4, 0xa5, 0x04, 0x4a, 0x2a, 0x9b, 0x48,
	0x77, 0xa0, 0x62, 0x30, 0xbc, 0xda, 0x44, 0x0f, 0xcf, 0x6b, 0x59, 0xba, 0x58, 0xe6, 0xb0, 0x53,
	0x3d, 0x3c, 0x97, 0xff, 0x9e, 0x81, 0xcd, 0x23, 0xdd, 0x21, 0x6d, 0x67, 0x18, 0x10, 0x1f, 0xc9,
	0x4b, 0x22, 0x64, 0xa7, 0xb6, 0x49, 0x09, 0x56, 0x55, 0x1c, 0x4a, 0x75, 0x28, 0xba, 0xb6, 0xf1,
	0xd4, 0xd5, 0x1d, 0xc2, 0x09, 0xcc, 0xe6, 0xd2, 0x10, 0x36, 0xa9, 0x0e, 0x0d, 0xdd, 0x37, 0x35,
	0xdb, 0x3d, 0xf3, 0x28, 0x95, 0xf2, 0x83, 0xfb, 0x8d, 0x65, 0xed, 0x36, 0xe6, 0x29, 0xd1, 0x69,
	0x4b, 0xf7, 0x4d, 0x2a, 0x75, 0xc5, 0x4a, 0xcc, 0xa4, 0x5b, 0x50, 0x9e, 0x06, 0xc4, 0xd7, 0x82,
	0x50, 0x0f, 0xa7, 0x41, 0x2d, 0x47, 0x99, 0x01, 0x04, 0xf5, 0x29, 0x44, 0xaa, 0x41, 0x41, 0x37,
	0x0c, 0x6f, 0xea, 0x86, 0xb5, 0x0d, 0xca, 0x52, 0x34, 0x45, 0xfe, 0x03, 0x72, 0x51, 0xcb, 0x33,
	0xfe, 0x03, 0x72, 0x51, 0x7f, 0x0c, 0x95, 0x24, 0x29, 0xe9, 0x6e, 0x92, 0xe7, 0x90, 0x5c, 0x84,
	0x54, 0xd8, 0x52, 0xcc, 0xc2, 0x80, 0x5c, 0x84, 0xd2, 0x6d, 0xa8, 0x24, 0x24, 0x8b, 0xee, 0x0e,
	0x66, 0x6c, 0x9a, 0xf2, 0x3f, 0x33, 0x50, 0x62, 0x22, 0x75, 0x02, 0x0b, 0x6f, 0xd9, 0x09, 0x2c,
	0x8d, 0xab, 0x2e, 0xa7, 0x6e, 0x38, 0x81, 0xd5, 0x36, 0xa5, 0x16, 0x94, 0x03, 0xe2, 0x9a, 0xc4,
	0x67, 0xda, 0xc9, 0x50, 0xed, 0xc8, 0x2f, 0xd6, 0x8e, 0x0a, 0xec, 0x18, 0xe5, 0x78, 0x17, 0x8a,
	0x88, 0x3b, 0x7c, 0x3e, 0x21, 0x54, 0xbf, 0x55, 0xb5, 0xe0, 0x04, 0xd6, 0xe0, 0xf9, 0x84, 0x48,
	0x7b, 0x50, 0xc2, 0x8d, 0x5a, 0x68, 0x3b, 0x84, 0xea, 0x29, 0xab, 0x16, 0x11, 0x30, 0xb0, 0x1d,
	0x22, 0xdd, 0x04, 0xc0, 0x73, 0x5c, 0x8b, 0x1b, 0xf4, 0x64, 0xc9, 0x09, 0x2c, 0xae, 0xc4, 0xd8,
	0x30, 0xf3, 0x09, 0xc3, 0x8c, 0x4e, 0xe9, 0x46, 0x68, 0x7b, 0x6e, 0xad, 0x30, 0x3b, 0xd5, 0xa4,
	0x00, 0x69, 0x1f, 0xb6, 0x70, 0xd9, 0xf0, 0xdc, 0x90, 0xb8, 0xa1, 0x36, 0x7a, 0x1e, 0x92, 0xa0,
	0x56, 0xbc, 0x2d, 0xdc, 0xab, 0xa8, 0x57, 0x9c, 0xc0, 0x6a, 0x31, 0xf8, 0x01, 0x82, 0x51, 0xd5,
	0xc6, 0xd8, 0xc6, 0x6d, 0x94, 0x7f, 0xdd, 0xaa, 0x95, 0x28, 0x8b, 0x15, 0x06, 0xed, 0x04, 0xd6,
	0x40, 0xb7, 0xe4, 0x3f, 0x0b, 0x70, 0x05, 0xa5, 0xef, 0x8d, 0x19, 0x63, 0xa8, 0xce, 0xc7, 0xb0,
	0xe9, 0x8d, 0x39, 0xe7, 0xda, 0xd8, 0x0e, 0xf0, 0x92, 0xb2, 0xf7, 0xca, 0x0f, 0x7e, 0xbc, 0x4a,
	0x75, 0x89, 0xc3, 0x8d, 0x68, 0xcc, 0x2c, 0xcb, 0xe3, 0xb3, 0x13, 0x3b, 0x08, 0xeb, 0xef, 0x43,
	0x25, 0xb9, 0x9a, 0x62, 0xee, 0xd7, 0x21, 0xcf, 0x15, 0xc6, 0xae, 0x9c, 0xcf, 0xe4, 0x3b, 0x50,
	0x46, 0x3a, 0x68, 0x1c, 0xc8, 0xa0, 0x04, 0xb9, 0x84, 0xed, 0xd0, 0xb1, 0xfc, 0x05, 0x6c, 0xe1,
	0x96, 0xbe, 0xe1, 0x13, 0xe2, 0x06, 0xe7, 0x1e, 0xdd, 0xb8, 0x03, 0x05, 0xdb, 0xb1, 0xb4, 0xa9,
	0x3f, 0xe6, 0x7b, 0xf3, 0xb6, 0x63, 0x0d, 0xfd, 0x31, 0x12, 0x3a, 0x27, 0xb6, 0x75, 0x1e, 0x46,
	0x84, 0xd8, 0x0c, 0xbd, 0xf9, 0x6b, 0xdb, 0xe4, 0x0e, 0x5b, 0x55, 0xd9, 0x44, 0x7e, 0x08, 0x55,
	0x66, 0xc5, 0xae, 0x41, 0xc6, 0x88, 0x57, 0x86, 0xaa, 0x41, 0x27, 0x5a, 0xc2, 0xee, 0xaa, 0x6a,
	0xd9, 0x88, 0x76, 0xb4, 0x4d, 0x79, 0x1f, 0x00, 0x0f, 0x1d, 0xb2, 0x13, 0x37, 0x00, 0xcc, 0xc5,
	0xed, 0x45, 0x33, 0xda, 0x7b, 0x08, 0x22, 0xee, 0x6d, 0x86, 0x7d, 0xcf, 0x21, 0x9e, 0x4b, 0xf8,
	0x09, 0x3d, 0xd4, 0xa8, 0x2b, 0xd2, 0x13, 0x59, 0x3c, 0xa1, 0x87, 0xd4, 0x48, 0xcd, 0x99, 0x0a,
	0x32, 0x09, 0x15, 0xfc, 0x92, 0x39, 0xdb, 0x27, 0x53, 0x2f, 0x24, 0x2b, 0xd4, 0x24, 0x7d, 0x00,
	0xa5, 0x67, 0xb8, 0x8e, 0x9c, 0x70, 0x8f, 0xb8, 0xb9, 0xda, 0x23, 0x3a, 0x81, 0xa5, 0x16, 0x9f,
	0x45, 0xf8, 0xe6, 0x39, 0xca, 0xce, 0x73, 0x24, 0xff, 0x87, 0x5b, 0xd2, 0x23, 0xcf, 0x77, 0x06,
	0x44, 0x8f, 0x1c, 0x93, 0x5b, 0xb9, 0x90, 0xb4, 0xf2, 0x5d, 0x28, 0x22, 0x38, 0x11, 0xd5, 0x0a,
	0xa1, 0x3e, 0xea, 0x62, 0x50, 0x93, 0x20, 0x67, 0x1b, 0x9e, 0xcb, 0x03, 0x26, 0x1d, 0xcf, 0xe4,
	0xc8, 0x25, 0xe4, 0x78, 0x0b, 0x36, 0x1d, 0xe2, 0x8c, 0x88, 0xaf, 0xc5, 0xb1, 0x28, 0x7b, 0xaf,
	0xa4, 0x56, 0x19, 0xb4, 0xc9, 0x23, 0xd2, 0x1e, 0x94, 0x42, 0xa2, 0x3b, 0x2c, 0x00, 0xe4, 0x59,
	0x00, 0x45, 0x00, 0xb5, 0xbf, 0x3d, 0x28, 0xd8, 0x81, 0xf6, 0xc4, 0xb3, 0x99, 0xa7, 0x15, 0x0f,
	0x32, 0x35, 0x41, 0xcd, 0xdb, 0xc1, 0x47, 0x9e, 0xed, 0x22, 0x8f, 0xb8, 0xa2, 0xa1, 0x85, 0x16,
	0x99, 0xdf, 0xe3, 0x7c, 0x68, 0x9b, 0xf2, 0x3f, 0x04, 0x66, 0x6b, 0x6d, 0xf7, 0xe7, 0x76, 0x48,
	0x54, 0xcf, 0x7b, 0xcd, 0xb2, 0xde, 0x82, 0x32, 0xb9, 0x98, 0xd8, 0x3e, 0x61, 0x91, 0x66, 0x83,
	0xba, 0x31, 0x30, 0x10, 0x8d, 0x35, 0x22, 0x64, 0x8d, 0x59, 0x24, 0xc1, 0x21, 0x46, 0xd0, 0x11,
	0xc6, 0x89, 0xb1, 0xe7, 0x6b, 0xee, 0xd4, 0xe1, 0x91, 0x04, 0x46, 0x56, 0x0b, 0x41, 0xdd, 0xa9,
	0x23, 0xdf, 0x81, 0x62, 0x6b, 0x4c, 0x74, 0x77, 0x75, 0xfc, 0x94, 0x7f, 0xcb, 0xe5, 0x54, 0x2e,
	0x26, 0x3e, 0x09, 0x02, 0xdb, 0xa3, 0x9b, 0x37, 0x21, 0xc3, 0x37, 0x96, 0xd4, 0x8c, 0x6d, 0x52,
	0x2f, 0xf6, 0xc7, 0x5c, 0x36, 0x1c, 0x26, 0x9c, 0x2b, 0x9b, 0xee, 0x5c, 0xb9, 0x84, 0x73, 0x61,
	0xc8, 0x23, 0x8e, 0xf7, 0xc4, 0x66, 0x21, 0x96, 0x65, 0x94, 0x12, 0x85, 0x60, 0x90, 0x95, 0xef,
	0x83, 0x84, 0x3c, 0x1c, 0xe8, 0xa1, 0x71, 0x1e, 0x3b, 0xe0, 0x2e, 0x14, 0xa7, 0xb6, 0x19, 0x07,
	0xa7, 0xaa, 0x5a, 0x98, 0xda, 0x26, 0x46, 0x19, 0xcc, 0xab, 0x5b, 0x47, 0x04, 0xdd, 0x1f, 0xa7,
	0xaf, 0xa6, 0x60, 0x48, 0x68, 0x28, 0x9b, 0xcc, 0x30, 0xc7, 0x90, 0xe7, 0xa1, 0x1a, 0x45, 0xda,
	0x5c, 0x11, 0x21, 0x17, 0x99, 0x69, 0xb0, 0x68, 0x8e, 0xe2, 0xa9, 0xfc, 0x3c, 0x26, 0x7a, 0xe2,
	0x86, 0x3e, 0x0a, 0xc8, 0x93, 0xc5, 0x6c, 0x2e, 0xdb, 0x00, 0xf1, 0x09, 0x69, 0x0f, 0x76, 0x9a,
	0xad, 0x41, 0xbb, 0xd7, 0xd5, 0x06, 0x9f, 0x9f, 0x2a, 0xda, 0xb0, 0xdb, 0x3f, 0x55, 0x5a, 0xed,
	0x47, 0x6d, 0xe5, 0x50, 0x7c, 0x43, 0x92, 0x60, 0x73, 0x6e, 0xf1, 0x54, 0x14, 0xa4, 0x6b, 0x20,
	0x26, 0x61, 0x87, 0xbd, 0x4f, 0xbb, 0x62, 0x66, 0x11, 0xfa, 0xd1, 0xb0, 0x73, 0x2a, 0x66, 0xe5,
	0xef, 0x04, 0x90, 0x92, 0xfc, 0xae, 0x5f, 0x0f, 0xbd, 0xcf, 0xf2, 0x25, 0xbd, 0x99, 0x0c, 0x4d,
	0x1b, 0x2f, 0x88, 0x2f, 0x98, 0x4e, 0x91, 0x18, 0x9a, 0xf9, 0xd8, 0xd3, 0x4d, 0xed, 0xcc, 0x76,
	0xed, 0x80, 0x45, 0xe0, 0xa2, 0x0a, 0x08, 0x7a, 0x44, 0x21, 0xf2, 0x5f, 0x37, 0xe1, 0x5a, 0x9f,
	0xb8, 0x66, 0x7c, 0x76, 0xcd, 0xcb, 0x5d, 0xac, 0xca, 0x32, 0x4b, 0x55, 0x99, 0xf4, 0x39, 0x54,
	0xd0, 0xe7, 0x10, 0x15, 0xa2, 0xe6, 0x25, 0xd5, 0x4f, 0xd3, 0x44, 0x48, 0x63, 0xa5, 0x81, 0x29,
	0x8a, 0x8f, 0x8f, 0xdf, 0x50, 0xcb, 0x61, 0x3c, 0x95, 0x34, 0xd8, 0xe4, 0x49, 0x23, 0x42, 0x9e,
	0xa3, 0xc8, 0x7f, 0x76, 0x69, 0xe4, 0xcc, 0xfe, 0x63, 0xf4, 0x3c, 0x09, 0x45, 0x04, 0x1e, 0x43,
	0xd9, 0x4c, 0x60, 0xdf, 0xa0, 0xd8, 0x1f, 0x5e, 0x1a, 0xfb, 0x61, 0x12, 0x35, 0x66, 0xab, 0x08,
	0x6f, 0x9f, 0xc6, 0xfd, 0x08, 0x6d, 0x9e, 0xa2, 0x7d, 0x70, 0x69, 0xb4, 0xcd, 0x84, 0x3e, 0x4a,
	0xfa, 0x4c, 0x1b, 0x5f, 0x41, 0x95, 0x25, 0xa2, 0x08, 0x6f, 0x81, 0xe2, 0x7d, 0xef, 0xd2, 0x78,
	0x69, 0x9a, 0x8b, 0x51, 0x57, 0x9e, 0x25, 0xe6, 0xd2, 0x2f, 0x60, 0x87, 0x22, 0x09, 0x66, 0xe5,
	0xc0, 0x8c, 0x4e, 0x91, 0xd2, 0x69, 0x5e, 0x9a, 0x4e, 0x5c, 0x55, 0xf4, 0x59, 0x55, 0x11, 0xd3,
	0xdc, 0xb6, 0xe6, 0x2a, 0x8e, 0x88, 0xf8, 0x19, 0x6c, 0x9d, 0x79, 0xbe, 0xa3, 0xd1, 0xcc, 0x13,
	0x91, 0x2d, 0x51, 0xb2, 0xef, 0x5f, 0x9a, 0x6c, 0x94, 0x46, 0x63, 0x6a, 0x57, 0xce, 0xe6, 0x41,
	0xd2, 0x18, 0xae, 0xda, 0x34, 0x05, 0x69, 0xbe, 0xe7, 0xc5, 0x94, 0x80, 0x52, 0xfa, 0xe0, 0xd2,
	0x94, 0xe2, 0x34, 0x16, 0xd3, 0xda, 0xb2, 0x17, 0x81, 0xd2, 0x53, 0x90, 0xc8, 0x2c, 0x11, 0xcc,
	0x88, 0x95, 0xd7, 0x24, 0x16, 0xe7, 0x92, 0x04, 0x31, 0xb2, 0x08, 0x44, 0x15, 0xd2, 0xec, 0x3b,
	0xa7, 0xc2, 0xca, 0x9a, 0x2a, 0xc4, 0x3c, 0xbe, 0xa0, 0xc2, 0x27, 0xf3, 0xa0, 0x94, 0x22, 0xb9,
	0xba, 0x5c, 0x24, 0xd7, 0xef, 0x40, 0x39, 0xe1, 0xd7, 0x69, 0x75, 0x55, 0xfd, 0x21, 0x54, 0xe7,
	0xbc, 0x33, 0xbd, 0x44, 0xcc, 0xcd, 0x95, 0x88, 0xf5, 0x7d, 0x80, 0xd8, 0xe9, 0x52, 0x4a, 0xc4,
	0x5c, 0x5c, 0x22, 0xd6, 0x3b, 0x50, 0x9a, 0x79, 0x12, 0xa6, 0x77, 0x3d, 0xa4, 0x45, 0x8d, 0xf6,
	0x24, 0xf0, 0x5c, 0xce, 0x09, 0xe8, 0x21, 0xd6, 0x35, 0x1f, 0x05, 0x9e, 0x2b, 0xbd, 0x09, 0x65,
	0xac, 0xd5, 0xa2, 0x1c, 0x99, 0xa1, 0x39, 0xb2, 0xa4, 0x87, 0x43, 0x96, 0x25, 0xeb, 0x26, 0x54,
	0x92, 0x0e, 0x84, 0x18, 0x67, 0x75, 0x61, 0x4c, 0x1e, 0xa2, 0xda, 0x2f, 0xbd, 0xe2, 0x5c, 0xa4,
	0x92, 0x5d, 0xa4, 0xf2, 0x27, 0x01, 0x6a, 0xab, 0xfc, 0x67, 0x55, 0xc1, 0x24, 0x42, 0x76, 0xe2,
	0x45, 0x0d, 0x00, 0x0e, 0xd7, 0xac, 0x27, 0xee, 0xc2, 0x66, 0xe8, 0x85, 0xfa, 0x58, 0xa3, 0xbd,
	0x19, 0x16, 0x3f, 0x2c, 0x9f, 0x56, 0x28, 0x14, 0x0d, 0xa5, 0x3b, 0x75, 0xea, 0x5b, 0x70, 0x65,
	0xc1, 0xc3, 0xea, 0x57, 0x61, 0x6b, 0xc9, 0x15, 0xea, 0x58, 0x03, 0x2d, 0xd9, 0xec, 0xff, 0xb9,
	0x06, 0xaa, 0xff, 0x04, 0xae, 0x2c, 0xd8, 0x32, 0x6a, 0x9e, 0xfa, 0xc6, 0xdc, 0x75, 0xd1, 0x5a,
	0x97, 0xde, 0xd6, 0x41, 0x09, 0x0a, 0xdc, 0x6d, 0xe4, 0x00, 0xb6, 0x17, 0xdc, 0x63, 0xfd, 0xac,
	0x7e, 0x1f, 0xb2, 0x97, 0x6e, 0x18, 0x70, 0xa7, 0xfc, 0x6f, 0x01, 0xae, 0x23, 0xcf, 0x08, 0x3e,
	0xd6, 0xc7, 0x63, 0xe4, 0xbd, 0xeb, 0x85, 0xf6, 0xd9, 0xf3, 0x55, 0xdd, 0xfa, 0x5d, 0xd8, 0xf4,
	0x89, 0x41, 0x05, 0x0a, 0xbe, 0xb6, 0x43, 0x83, 0xa5, 0xe7, 0xa2, 0x5a, 0xf1, 0x89, 0xd1, 0x09,
	0xac, 0x3e, 0x85, 0xd1, 0xe7, 0x16, 0x3b, 0x1c, 0x13, 0x5e, 0x34, 0xb3, 0x09, 0xed, 0xc4, 0xa7,
	0x23, 0x8d, 0xad, 0xb0, 0xd2, 0xb9, 0x18, 0x4c, 0x47, 0x03, 0xba, 0x78, 0x00, 0x45, 0x87, 0xf0,
	0x16, 0x80, 0xe5, 0xc4, 0x1f, 0xae, 0x12, 0x20, 0xe2, 0xb4, 0x43, 0x68, 0x87, 0xa0, 0x16, 0x1c,
	0x36, 0x58, 0xd1, 0xae, 0xcb, 0x9f, 0xc2, 0xf5, 0x23, 0x12, 0x26, 0x4f, 0xf2, 0x32, 0xef, 0x65,
	0x1f, 0xa8, 0x02, 0xb8, 0x9a, 0xc2, 0x8f, 0xf4, 0x21, 0xbf, 0x31, 0x2a, 0x8b, 0x70, 0xe9, 0xf7,
	0x0c, 0xca, 0x0a, 0x6f, 0x79, 0x4a, 0x2c, 0xae, 0xda, 0xbc, 0x1d, 0xc9, 0xa9, 0xb4, 0xcd, 0xc1,
	0x36, 0x42, 0xfe, 0xbd, 0x00, 0x3b, 0xa9, 0xe2, 0x04, 0x93, 0x75, 0x6c, 0x25, 0xa9, 0x6f, 0x56,
	0x01, 0xae, 0xad, 0x6f, 0xf9, 0x1b, 0x01, 0xb6, 0x5b, 0xe7, 0xc4, 0x78, 0x8a, 0x3b, 0x14, 0x37,
	0x24, 0x3e, 0x77, 0xca, 0x75, 0x14, 0xcb, 0xad, 0x2c, 0x93, 0xb4, 0xb2, 0x5d, 0xa0, 0x4f, 0x34,
	0xb4, 0xad, 0xe3, 0xcf, 0x39, 0x38, 0x1f, 0x32, 0x27, 0xc6, 0x2e, 0x2a, 0x37, 0xeb, 0xa2, 0xe4,
	0xcf, 0xe1, 0x7a, 0x1a, 0x13, 0xeb, 0xa9, 0x23, 0xf5, 0x81, 0x50, 0x7e, 0x0c, 0xdb, 0x47, 0x84,
	0xb6, 0xce, 0x43, 0xd7, 0x27, 0xba, 0xd9, 0xe4, 0x91, 0xf1, 0x65, 0x0d, 0xe7, 0x2b, 0x6a, 0x91,
	0x4b, 0x78, 0xd7, 0x63, 0x79, 0x07, 0x0a, 0x4c, 0x77, 0x01, 0xbd, 0xc0, 0x9c, 0x9a, 0xa7, 0xca,
	0x0b, 0x64, 0x17, 0xae, 0x75, 0x74, 0xff, 0x29, 0xa2, 0xe7, 0x88, 0x75, 0xf3, 0xe5, 0x99, 0x4e,
	0xd2, 0xcb, 0xce, 0xd1, 0x3b, 0x80, 0xed, 0x14, 0x7a, 0x6b, 0x09, 0x23, 0xff, 0x4d, 0x88, 0x7d,
	0x89, 0x22, 0x89, 0xc3, 0x50, 0x5a, 0xfa, 0x59, 0x61, 0x37, 0x89, 0x47, 0xcf, 0xec, 0xfc, 0xa3,
	0xe7, 0x2c, 0x22, 0xe5, 0x56, 0x46, 0xa4, 0x8d, 0x85, 0x88, 0x94, 0x08, 0x75, 0x2e, 0x65, 0x86,
	0x46, 0x95, 0x59, 0xa8, 0x63, 0x0c, 0xca, 0xbf, 0x82, 0x3a, 0xb5, 0x3e, 0x0c, 0xde, 0x71, 0x62,
	0x6a, 0x79, 0xee, 0xab, 0x50, 0x39, 0x6d, 0x85, 0x88, 0xf1, 0x54, 0x0b, 0xbc, 0xa9, 0x6f, 0x44,
	0x4f, 0x9b, 0x65, 0x0a, 0xeb, 0x53, 0x90, 0x6c, 0xc1, 0xde, 0x4a, 0xfa, 0xeb, 0xd9, 0xd3, 0x4d,
	0x00, 0x43, 0x77, 0x35, 0x56, 0x53, 0xf2, 0xb0, 0x5e, 0x32, 0x74, 0x97, 0x61, 0x95, 0x7f, 0x0d,
	0x37, 0x87, 0x13, 0x53, 0x0f, 0x49, 0x74, 0x4d, 0x4c, 0x01, 0xec, 0xa5, 0xf0, 0x15, 0xc8, 0x7a,
	0x0b, 0xca, 0xde, 0x84, 0xb8, 0x91, 0xae, 0x79, 0x63, 0x89, 0x20, 0xae, 0xe9, 0x8f, 0xe1, 0xcd,
	0xef, 0x63, 0x60, 0x3d, 0x7b, 0x63, 0x1f, 0x2e, 0x5e, 0x8f, 0x28, 0xb2, 0x4e, 0xbf, 0x5b, 0xbc,
	0x02, 0x36, 0xe9, 0xd3, 0x68, 0xa0, 0xa1, 0x12, 0xf8, 0x85, 0xe4, 0xed, 0xa0, 0x37, 0x21, 0xae,
	0x1c, 0xc0, 0x6e, 0x9f, 0x84, 0xfd, 0x73, 0xef, 0x6b, 0x85, 0x3f, 0x40, 0xf4, 0x49, 0x18, 0xda,
	0xae, 0xf5, 0x6a, 0x1c, 0x3d, 0x22, 0x9a, 0x9d, 0x23, 0x7a, 0x04, 0xf5, 0x55, 0x44, 0xd7, 0xd3,
	0xfe, 0x17, 0xb0, 0x7b, 0xf4, 0x9a, 0xb8, 0x97, 0xff, 0x28, 0xc0, 0xd5, 0x05, 0xac, 0xed, 0x90,
	0x38, 0xff, 0xc3, 0xcb, 0x1f, 0x5f, 0x4a, 0xbc, 0xfe, 0xe1, 0x52, 0xdb, 0xf0, 0xdc, 0xa8, 0xd2,
	0xca, 0x5d, 0xba, 0xd2, 0xfa, 0x8b, 0x40, 0x0d, 0xee, 0xe5, 0x75, 0x27, 0x7d, 0x09, 0xdb, 0xd1,
	0x9b, 0x93, 0x16, 0x30, 0x14, 0xc9, 0x77, 0x9c, 0xd4, 0x2c, 0x9e, 0xa2, 0x0f, 0xf5, 0x2a, 0x99,
	0x07, 0xd2, 0x67, 0xb9, 0x0e, 0xd4, 0x23, 0xb3, 0x4d, 0xb2, 0xca, 0x83, 0x31, 0x97, 0x5a, 0xb8,
	0xcc, 0x83, 0x11, 0x95, 0x9a, 0xe5, 0xcf, 0x08, 0xe3, 0xa9, 0xed, 0xb6, 0x3c, 0xf7, 0xec, 0x15,
	0xdc, 0xf1, 0xb7, 0x99, 0x38, 0x5b, 0x70, 0xac, 0xf4, 0x8e, 0xb1, 0xfb, 0x09, 0x67, 0x35, 0x3f,
	0x1d, 0x47, 0xdf, 0x2f, 0x32, 0xe9, 0x9f, 0xeb, 0xb2, 0x0b, 0x9f, 0xeb, 0x44, 0xc8, 0xda, 0x8e,
	0xc5, 0xb3, 0x04, 0x0e, 0x31, 0xa7, 0xf0, 0x2f, 0x39, 0xd1, 0x87, 0x34, 0x3e, 0x95, 0xda, 0xbc,
	0xd7, 0xca, 0x53, 0x7d, 0xbc, 0xf7, 0x7d, 0xe5, 0x53, 0x82, 0xc9, 0xc6, 0xb1, 0x6d, 0x9d, 0x8f,
	0xb1, 0xcd, 0xa0, 0xdd, 0x2a, 0x6b, 0x4c, 0x3f, 0x84, 0xea, 0x1c, 0x58, 0xba, 0x01, 0xa5, 0xf3,
	0x08, 0xc0, 0xed, 0x32, 0x06, 0x44, 0x9d, 0x4c, 0x76, 0xd6, 0xc9, 0xc8, 0x7f, 0x10, 0xe6, 0x8a,
	0xdc, 0x99, 0xaa, 0xd7, 0xb3, 0xad, 0x8f, 0xa1, 0x3a, 0xb1, 0x5d, 0xcd, 0xf0, 0xdc, 0xb3, 0x17,
	0xda, 0x54, 0x8a, 0x68, 0x6a, 0x79, 0xc2, 0x26, 0x68, 0x4b, 0xfb, 0xdf, 0x08, 0x50, 0xea, 0xcc,
	0x3e, 0xa5, 0xd5, 0xe1, 0x7a, 0xa7, 0x7f, 0xa4, 0xf5, 0x07, 0xcd, 0xc1, 0xb0, 0xbf, 0xf0, 0x1e,
	0xba, 0x0d, 0x5b, 0x89, 0xb5, 0x6e, 0x4f, 0xed, 0x34, 0x4f, 0x44, 0x61, 0x01, 0x7c, 0xa8, 0x9c,
	0x28, 0x03, 0x45, 0xcc, 0x2c, 0x80, 0x5b, 0xcd, 0x6e, 0x4b, 0x39, 0x11, 0xb3, 0x0b, 0xe0, 0xe1,
	0xe9, 0x61, 0x73, 0xa0, 0x88, 0xb9, 0xfd, 0x6f, 0x19, 0x17, 0xcd, 0xe8, 0x01, 0x97, 0x72, 0xc1,
	0xdf, 0x54, 0x53, 0xb9, 0xe0, 0x6b, 0x33, 0x2e, 0xae, 0x81, 0x98, 0x00, 0xb7, 0x4e, 0x94, 0x66,
	0x57, 0xcc, 0x48, 0x37, 0xa0, 0x96, 0x80, 0xf6, 0xba, 0x27, 0xed, 0xae, 0xc2, 0x69, 0x8b, 0x59,
	0x69, 0x0f, 0x76, 0x12, 0xab, 0x07, 0xcd, 0x41, 0xeb, 0x38, 0x62, 0x34, 0xb7, 0xff, 0xaf, 0x0c,
	0xfb, 0x50, 0x45, 0xfd, 0x84, 0x3e, 0x16, 0xdf, 0x86, 0x1b, 0x47, 0xcd, 0x8e, 0xa2, 0xb5, 0x3b,
	0x1a, 0x1e, 0x4b, 0x79, 0x31, 0xde, 0x85, 0xed, 0xa5, 0x1d, 0x03, 0xe5, 0xb3, 0x81, 0x28, 0x48,
	0x77, 0xe1, 0xf6, 0xd2, 0x12, 0x05, 0xf4, 0x5b, 0xaa, 0xa2, 0x74, 0xfb, 0xc7, 0xbd, 0x81, 0x98,
	0x91, 0x6e, 0xc1, 0xce, 0xd2, 0xae, 0x48, 0x75, 0xf5, 0x4c, 0x51, 0x90, 0x6e, 0xc0, 0xb5, 0xa5,
	0x0d, 0x87, 0xc8, 0x2f, 0x5d, 0xbd, 0x05, 0x7b, 0x4b, 0xab, 0xcd, 0x81, 0xd6, 0xef, 0x75, 0x94,
	0x5e, 0x57, 0x11, 0x37, 0x50, 0xb1, 0x4b, 0x1b, 0x3e, 0x19, 0xf6, 0x06, 0x8a, 0x98, 0x97, 0xde,
	0x84, 0xfa, 0xd2, 0xda, 0xa3, 0x9e, 0xda, 0xd1, 0x06, 0x4a, 0xb3, 0x23, 0x16, 0x52, 0xc5, 0x6f,
	0x77, 0x1f, 0xb7, 0x07, 0x8a, 0xa6, 0xf6, 0x7a, 0x1d, 0xb1, 0x98, 0x4a, 0x5e, 0xf9, 0xec, 0x54,
	0x55, 0xfa, 0xfd, 0x76, 0xaf, 0x2b, 0x96, 0xf6, 0x2f, 0xd8, 0xa7, 0x39, 0xd6, 0x4d, 0x71, 0x8b,
	0xfb, 0x01, 0xdc, 0x8a, 0x0e, 0x0d, 0xfb, 0x8a, 0x9a, 0x6e, 0x7a, 0x09, 0xcc, 0xc9, 0x4d, 0xbd,
	0x47, 0x8f, 0xf0, 0x46, 0x45, 0x21, 0xc9, 0xfc, 0xdc, 0x06, 0x7a, 0xe3, 0x62, 0x66, 0xdf, 0x61,
	0xff, 0x07, 0x24, 0xe8, 0x46, 0xe2, 0xac, 0x26, 0xba, 0xc7, 0x2f, 0x23, 0x05, 0x21, 0x5e, 0x44,
	0x6d, 0x79, 0x91, 0xb3, 0x93, 0xd9, 0xff, 0x9d, 0x00, 0x15, 0xf4, 0xae, 0x28, 0x32, 0x4b, 0x37,
	0x61, 0xf7, 0xa4, 0xdd, 0x1f, 0x68, 0x4a, 0x77, 0xa0, 0xe2, 0x7d, 0x2e, 0x1b, 0xce, 0xfc, 0x72,
	0xf3, 0xe4, 0x04, 0x75, 0x28, 0xd2, 0x3b, 0x9d, 0x5f, 0x9a, 0xdd, 0x09, 0xdd, 0x90, 0x41, 0x36,
	0xe7, 0x37, 0x9c, 0xb6, 0xf8, 0x0d, 0x88, 0xd9, 0xfd, 0xef, 0x04, 0xd8, 0x4d, 0x29, 0x3c, 0x59,
	0x55, 0x2a, 0xbd, 0x0b, 0xef, 0xb4, 0x8e, 0x95, 0xd6, 0xc7, 0x5a, 0x5f, 0xe9, 0x1e, 0x26, 0x2f,
	0x53, 0xeb, 0xf7, 0x86, 0xea, 0x12, 0x97, 0x6f, 0x83, 0xfc, 0xfd, 0xdb, 0xa9, 0xa5, 0x08, 0x2f,
	0xde, 0x77, 0xdc, 0x3c, 0x39, 0x11, 0x33, 0x07, 0xc7, 0x50, 0x33, 0x3c, 0xa7, 0xf1, 0xdc, 0x7e,
	0xee, 0x4d, 0x31, 0x76, 0x39, 0x9e, 0x49, 0xc6, 0xec, 0x97, 0x95, 0x2f, 0x7e, 0x64, 0x79, 0x63,
	0xdd, 0xb5, 0x1a, 0xef, 0x3d, 0x08, 0xc3, 0x86, 0xe1, 0x39, 0xf7, 0x29, 0xd8, 0xf0, 0xc6, 0xf7,
	0xf5, 0xc9, 0x84, 0xfe, 0x09, 0xf3, 0x2e, 0x46, 0xb9, 0x77, 0x69, 0x94, 0x1b, 0xe5, 0xe9, 0xea,
	0xc3, 0xff, 0x06, 0x00, 0x00, 0xff, 0xff, 0xfe, 0xd2, 0xb0, 0x8c, 0x33, 0x23, 0x00, 0x00,
}
