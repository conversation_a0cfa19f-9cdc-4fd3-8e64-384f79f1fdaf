// Code generated by protoc-gen-go. DO NOT EDIT.
// source: game_ugc/game_ugc.proto

package game_ugc // import "golang.52tt.com/protocol/app/game-ugc"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import online "golang.52tt.com/protocol/app/online"
import ugc "golang.52tt.com/protocol/app/ugc"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PersonalPageFilterType int32

const (
	PersonalPageFilterType_PERSONAL_PAGE_FILTER_TYPE_UNSPECIFIED PersonalPageFilterType = 0
	PersonalPageFilterType_PERSONAL_PAGE_FILTER_TYPE_GAME_ZONE   PersonalPageFilterType = 1
)

var PersonalPageFilterType_name = map[int32]string{
	0: "PERSONAL_PAGE_FILTER_TYPE_UNSPECIFIED",
	1: "PERSONAL_PAGE_FILTER_TYPE_GAME_ZONE",
}
var PersonalPageFilterType_value = map[string]int32{
	"PERSONAL_PAGE_FILTER_TYPE_UNSPECIFIED": 0,
	"PERSONAL_PAGE_FILTER_TYPE_GAME_ZONE":   1,
}

func (x PersonalPageFilterType) String() string {
	return proto.EnumName(PersonalPageFilterType_name, int32(x))
}
func (PersonalPageFilterType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{0}
}

type GamePostButtonType int32

const (
	GamePostButtonType_GAME_POST_BUTTON_TYPE_UNSPECIFIED    GamePostButtonType = 0
	GamePostButtonType_GAME_POST_BUTTON_TYPE_ENTER_ROOM     GamePostButtonType = 1
	GamePostButtonType_GAME_POST_BUTTON_TYPE_FOLLOW         GamePostButtonType = 2
	GamePostButtonType_GAME_POST_BUTTON_TYPE_ALREADY_FOLLOW GamePostButtonType = 3
)

var GamePostButtonType_name = map[int32]string{
	0: "GAME_POST_BUTTON_TYPE_UNSPECIFIED",
	1: "GAME_POST_BUTTON_TYPE_ENTER_ROOM",
	2: "GAME_POST_BUTTON_TYPE_FOLLOW",
	3: "GAME_POST_BUTTON_TYPE_ALREADY_FOLLOW",
}
var GamePostButtonType_value = map[string]int32{
	"GAME_POST_BUTTON_TYPE_UNSPECIFIED":    0,
	"GAME_POST_BUTTON_TYPE_ENTER_ROOM":     1,
	"GAME_POST_BUTTON_TYPE_FOLLOW":         2,
	"GAME_POST_BUTTON_TYPE_ALREADY_FOLLOW": 3,
}

func (x GamePostButtonType) String() string {
	return proto.EnumName(GamePostButtonType_name, int32(x))
}
func (GamePostButtonType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{1}
}

// 用户状态
type UserStatus int32

const (
	UserStatus_USER_STATUS_UNSPECIFIED UserStatus = 0
	UserStatus_USER_STATUS_PUBLISHING  UserStatus = 1
	UserStatus_USER_STATUS_ONLINE      UserStatus = 2
)

var UserStatus_name = map[int32]string{
	0: "USER_STATUS_UNSPECIFIED",
	1: "USER_STATUS_PUBLISHING",
	2: "USER_STATUS_ONLINE",
}
var UserStatus_value = map[string]int32{
	"USER_STATUS_UNSPECIFIED": 0,
	"USER_STATUS_PUBLISHING":  1,
	"USER_STATUS_ONLINE":      2,
}

func (x UserStatus) String() string {
	return proto.EnumName(UserStatus_name, int32(x))
}
func (UserStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{2}
}

type VisibleScope int32

const (
	VisibleScope_VISIBLE_SCOPE_UNSPECIFIED VisibleScope = 0
	VisibleScope_VISIBLE_SCOPE_GAME_ZONE   VisibleScope = 1
)

var VisibleScope_name = map[int32]string{
	0: "VISIBLE_SCOPE_UNSPECIFIED",
	1: "VISIBLE_SCOPE_GAME_ZONE",
}
var VisibleScope_value = map[string]int32{
	"VISIBLE_SCOPE_UNSPECIFIED": 0,
	"VISIBLE_SCOPE_GAME_ZONE":   1,
}

func (x VisibleScope) String() string {
	return proto.EnumName(VisibleScope_name, int32(x))
}
func (VisibleScope) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{3}
}

type GamePostOrigin int32

const (
	GamePostOrigin_GAME_POST_ORIGIN_UNSPECIFIED GamePostOrigin = 0
	GamePostOrigin_GAME_POST_ORIGIN_GAME_TAB    GamePostOrigin = 1
)

var GamePostOrigin_name = map[int32]string{
	0: "GAME_POST_ORIGIN_UNSPECIFIED",
	1: "GAME_POST_ORIGIN_GAME_TAB",
}
var GamePostOrigin_value = map[string]int32{
	"GAME_POST_ORIGIN_UNSPECIFIED": 0,
	"GAME_POST_ORIGIN_GAME_TAB":    1,
}

func (x GamePostOrigin) String() string {
	return proto.EnumName(GamePostOrigin_name, int32(x))
}
func (GamePostOrigin) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{4}
}

type ConfigTabType int32

const (
	ConfigTabType_CONFIG_TAB_TYPE_UNSPECIFIED   ConfigTabType = 0
	ConfigTabType_CONFIG_TAB_TYPE_CHANNEL_LIST  ConfigTabType = 1
	ConfigTabType_CONFIG_TAB_TYPE_POST_TAB      ConfigTabType = 2
	ConfigTabType_CONFIG_TAB_TYPE_ACTIVITY_POST ConfigTabType = 3
	ConfigTabType_CONFIG_TAB_TYPE_ACTIVITY_SET  ConfigTabType = 4
	ConfigTabType_CONFIG_TAB_TYPE_GAME_PAL_CARD ConfigTabType = 5
	ConfigTabType_CONFIG_TAB_TYPE_GROUP_CHAT    ConfigTabType = 6
	ConfigTabType_CONFIG_TAB_TYPE_GAME_HALL     ConfigTabType = 7
)

var ConfigTabType_name = map[int32]string{
	0: "CONFIG_TAB_TYPE_UNSPECIFIED",
	1: "CONFIG_TAB_TYPE_CHANNEL_LIST",
	2: "CONFIG_TAB_TYPE_POST_TAB",
	3: "CONFIG_TAB_TYPE_ACTIVITY_POST",
	4: "CONFIG_TAB_TYPE_ACTIVITY_SET",
	5: "CONFIG_TAB_TYPE_GAME_PAL_CARD",
	6: "CONFIG_TAB_TYPE_GROUP_CHAT",
	7: "CONFIG_TAB_TYPE_GAME_HALL",
}
var ConfigTabType_value = map[string]int32{
	"CONFIG_TAB_TYPE_UNSPECIFIED":   0,
	"CONFIG_TAB_TYPE_CHANNEL_LIST":  1,
	"CONFIG_TAB_TYPE_POST_TAB":      2,
	"CONFIG_TAB_TYPE_ACTIVITY_POST": 3,
	"CONFIG_TAB_TYPE_ACTIVITY_SET":  4,
	"CONFIG_TAB_TYPE_GAME_PAL_CARD": 5,
	"CONFIG_TAB_TYPE_GROUP_CHAT":    6,
	"CONFIG_TAB_TYPE_GAME_HALL":     7,
}

func (x ConfigTabType) String() string {
	return proto.EnumName(ConfigTabType_name, int32(x))
}
func (ConfigTabType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{5}
}

type BasePostInfo_PostSource int32

const (
	BasePostInfo_POST_SOURCE_UNSPECIFIED         BasePostInfo_PostSource = 0
	BasePostInfo_POST_SOURCE_GAME_RECOMMENDATION BasePostInfo_PostSource = 1
)

var BasePostInfo_PostSource_name = map[int32]string{
	0: "POST_SOURCE_UNSPECIFIED",
	1: "POST_SOURCE_GAME_RECOMMENDATION",
}
var BasePostInfo_PostSource_value = map[string]int32{
	"POST_SOURCE_UNSPECIFIED":         0,
	"POST_SOURCE_GAME_RECOMMENDATION": 1,
}

func (x BasePostInfo_PostSource) String() string {
	return proto.EnumName(BasePostInfo_PostSource_name, int32(x))
}
func (BasePostInfo_PostSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{4, 0}
}

// 开黑拉流接口
type GetGameNewsFeedsReq struct {
	BaseReq     *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GetMode     uint32       `protobuf:"varint,2,opt,name=get_mode,json=getMode,proto3" json:"get_mode,omitempty"`
	ContentType uint32       `protobuf:"varint,3,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"` // Deprecated: Do not use.
	BrowseList  []string     `protobuf:"bytes,4,rep,name=browse_list,json=browseList,proto3" json:"browse_list,omitempty"`
	// Types that are valid to be assigned to RequestType:
	//	*GetGameNewsFeedsReq_GameRecommendationStreamReq_
	//	*GetGameNewsFeedsReq_GetPersonalSourcePostReq_
	RequestType          isGetGameNewsFeedsReq_RequestType `protobuf_oneof:"request_type"`
	RecRuleType          uint32                            `protobuf:"varint,6,opt,name=rec_rule_type,json=recRuleType,proto3" json:"rec_rule_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *GetGameNewsFeedsReq) Reset()         { *m = GetGameNewsFeedsReq{} }
func (m *GetGameNewsFeedsReq) String() string { return proto.CompactTextString(m) }
func (*GetGameNewsFeedsReq) ProtoMessage()    {}
func (*GetGameNewsFeedsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{0}
}
func (m *GetGameNewsFeedsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameNewsFeedsReq.Unmarshal(m, b)
}
func (m *GetGameNewsFeedsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameNewsFeedsReq.Marshal(b, m, deterministic)
}
func (dst *GetGameNewsFeedsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameNewsFeedsReq.Merge(dst, src)
}
func (m *GetGameNewsFeedsReq) XXX_Size() int {
	return xxx_messageInfo_GetGameNewsFeedsReq.Size(m)
}
func (m *GetGameNewsFeedsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameNewsFeedsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameNewsFeedsReq proto.InternalMessageInfo

func (m *GetGameNewsFeedsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameNewsFeedsReq) GetGetMode() uint32 {
	if m != nil {
		return m.GetMode
	}
	return 0
}

// Deprecated: Do not use.
func (m *GetGameNewsFeedsReq) GetContentType() uint32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

func (m *GetGameNewsFeedsReq) GetBrowseList() []string {
	if m != nil {
		return m.BrowseList
	}
	return nil
}

type isGetGameNewsFeedsReq_RequestType interface {
	isGetGameNewsFeedsReq_RequestType()
}

type GetGameNewsFeedsReq_GameRecommendationStreamReq_ struct {
	GameRecommendationStreamReq *GetGameNewsFeedsReq_GameRecommendationStreamReq `protobuf:"bytes,5,opt,name=game_recommendation_stream_req,json=gameRecommendationStreamReq,proto3,oneof"`
}

type GetGameNewsFeedsReq_GetPersonalSourcePostReq_ struct {
	GetPersonalSourcePostReq *GetGameNewsFeedsReq_GetPersonalSourcePostReq `protobuf:"bytes,7,opt,name=get_personal_source_post_req,json=getPersonalSourcePostReq,proto3,oneof"`
}

func (*GetGameNewsFeedsReq_GameRecommendationStreamReq_) isGetGameNewsFeedsReq_RequestType() {}

func (*GetGameNewsFeedsReq_GetPersonalSourcePostReq_) isGetGameNewsFeedsReq_RequestType() {}

func (m *GetGameNewsFeedsReq) GetRequestType() isGetGameNewsFeedsReq_RequestType {
	if m != nil {
		return m.RequestType
	}
	return nil
}

func (m *GetGameNewsFeedsReq) GetGameRecommendationStreamReq() *GetGameNewsFeedsReq_GameRecommendationStreamReq {
	if x, ok := m.GetRequestType().(*GetGameNewsFeedsReq_GameRecommendationStreamReq_); ok {
		return x.GameRecommendationStreamReq
	}
	return nil
}

func (m *GetGameNewsFeedsReq) GetGetPersonalSourcePostReq() *GetGameNewsFeedsReq_GetPersonalSourcePostReq {
	if x, ok := m.GetRequestType().(*GetGameNewsFeedsReq_GetPersonalSourcePostReq_); ok {
		return x.GetPersonalSourcePostReq
	}
	return nil
}

func (m *GetGameNewsFeedsReq) GetRecRuleType() uint32 {
	if m != nil {
		return m.RecRuleType
	}
	return 0
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*GetGameNewsFeedsReq) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _GetGameNewsFeedsReq_OneofMarshaler, _GetGameNewsFeedsReq_OneofUnmarshaler, _GetGameNewsFeedsReq_OneofSizer, []interface{}{
		(*GetGameNewsFeedsReq_GameRecommendationStreamReq_)(nil),
		(*GetGameNewsFeedsReq_GetPersonalSourcePostReq_)(nil),
	}
}

func _GetGameNewsFeedsReq_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*GetGameNewsFeedsReq)
	// request_type
	switch x := m.RequestType.(type) {
	case *GetGameNewsFeedsReq_GameRecommendationStreamReq_:
		b.EncodeVarint(5<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.GameRecommendationStreamReq); err != nil {
			return err
		}
	case *GetGameNewsFeedsReq_GetPersonalSourcePostReq_:
		b.EncodeVarint(7<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.GetPersonalSourcePostReq); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("GetGameNewsFeedsReq.RequestType has unexpected type %T", x)
	}
	return nil
}

func _GetGameNewsFeedsReq_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*GetGameNewsFeedsReq)
	switch tag {
	case 5: // request_type.game_recommendation_stream_req
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(GetGameNewsFeedsReq_GameRecommendationStreamReq)
		err := b.DecodeMessage(msg)
		m.RequestType = &GetGameNewsFeedsReq_GameRecommendationStreamReq_{msg}
		return true, err
	case 7: // request_type.get_personal_source_post_req
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(GetGameNewsFeedsReq_GetPersonalSourcePostReq)
		err := b.DecodeMessage(msg)
		m.RequestType = &GetGameNewsFeedsReq_GetPersonalSourcePostReq_{msg}
		return true, err
	default:
		return false, nil
	}
}

func _GetGameNewsFeedsReq_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*GetGameNewsFeedsReq)
	// request_type
	switch x := m.RequestType.(type) {
	case *GetGameNewsFeedsReq_GameRecommendationStreamReq_:
		s := proto.Size(x.GameRecommendationStreamReq)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *GetGameNewsFeedsReq_GetPersonalSourcePostReq_:
		s := proto.Size(x.GetPersonalSourcePostReq)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type GetGameNewsFeedsReq_GameRecommendationStreamReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TopicSetId           string   `protobuf:"bytes,2,opt,name=topic_set_id,json=topicSetId,proto3" json:"topic_set_id,omitempty"` // Deprecated: Do not use.
	ConfigTabSetId       string   `protobuf:"bytes,3,opt,name=config_tab_set_id,json=configTabSetId,proto3" json:"config_tab_set_id,omitempty"`
	TopicIds             []string `protobuf:"bytes,4,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameNewsFeedsReq_GameRecommendationStreamReq) Reset() {
	*m = GetGameNewsFeedsReq_GameRecommendationStreamReq{}
}
func (m *GetGameNewsFeedsReq_GameRecommendationStreamReq) String() string {
	return proto.CompactTextString(m)
}
func (*GetGameNewsFeedsReq_GameRecommendationStreamReq) ProtoMessage() {}
func (*GetGameNewsFeedsReq_GameRecommendationStreamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{0, 0}
}
func (m *GetGameNewsFeedsReq_GameRecommendationStreamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameNewsFeedsReq_GameRecommendationStreamReq.Unmarshal(m, b)
}
func (m *GetGameNewsFeedsReq_GameRecommendationStreamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameNewsFeedsReq_GameRecommendationStreamReq.Marshal(b, m, deterministic)
}
func (dst *GetGameNewsFeedsReq_GameRecommendationStreamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameNewsFeedsReq_GameRecommendationStreamReq.Merge(dst, src)
}
func (m *GetGameNewsFeedsReq_GameRecommendationStreamReq) XXX_Size() int {
	return xxx_messageInfo_GetGameNewsFeedsReq_GameRecommendationStreamReq.Size(m)
}
func (m *GetGameNewsFeedsReq_GameRecommendationStreamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameNewsFeedsReq_GameRecommendationStreamReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameNewsFeedsReq_GameRecommendationStreamReq proto.InternalMessageInfo

func (m *GetGameNewsFeedsReq_GameRecommendationStreamReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

// Deprecated: Do not use.
func (m *GetGameNewsFeedsReq_GameRecommendationStreamReq) GetTopicSetId() string {
	if m != nil {
		return m.TopicSetId
	}
	return ""
}

func (m *GetGameNewsFeedsReq_GameRecommendationStreamReq) GetConfigTabSetId() string {
	if m != nil {
		return m.ConfigTabSetId
	}
	return ""
}

func (m *GetGameNewsFeedsReq_GameRecommendationStreamReq) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

type GetGameNewsFeedsReq_GetPersonalSourcePostReq struct {
	LastPostCreatedTime  int64    `protobuf:"varint,1,opt,name=last_post_created_time,json=lastPostCreatedTime,proto3" json:"last_post_created_time,omitempty"`
	FilterType           uint32   `protobuf:"varint,2,opt,name=filter_type,json=filterType,proto3" json:"filter_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameNewsFeedsReq_GetPersonalSourcePostReq) Reset() {
	*m = GetGameNewsFeedsReq_GetPersonalSourcePostReq{}
}
func (m *GetGameNewsFeedsReq_GetPersonalSourcePostReq) String() string {
	return proto.CompactTextString(m)
}
func (*GetGameNewsFeedsReq_GetPersonalSourcePostReq) ProtoMessage() {}
func (*GetGameNewsFeedsReq_GetPersonalSourcePostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{0, 1}
}
func (m *GetGameNewsFeedsReq_GetPersonalSourcePostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameNewsFeedsReq_GetPersonalSourcePostReq.Unmarshal(m, b)
}
func (m *GetGameNewsFeedsReq_GetPersonalSourcePostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameNewsFeedsReq_GetPersonalSourcePostReq.Marshal(b, m, deterministic)
}
func (dst *GetGameNewsFeedsReq_GetPersonalSourcePostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameNewsFeedsReq_GetPersonalSourcePostReq.Merge(dst, src)
}
func (m *GetGameNewsFeedsReq_GetPersonalSourcePostReq) XXX_Size() int {
	return xxx_messageInfo_GetGameNewsFeedsReq_GetPersonalSourcePostReq.Size(m)
}
func (m *GetGameNewsFeedsReq_GetPersonalSourcePostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameNewsFeedsReq_GetPersonalSourcePostReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameNewsFeedsReq_GetPersonalSourcePostReq proto.InternalMessageInfo

func (m *GetGameNewsFeedsReq_GetPersonalSourcePostReq) GetLastPostCreatedTime() int64 {
	if m != nil {
		return m.LastPostCreatedTime
	}
	return 0
}

func (m *GetGameNewsFeedsReq_GetPersonalSourcePostReq) GetFilterType() uint32 {
	if m != nil {
		return m.FilterType
	}
	return 0
}

type GetGameNewsFeedsResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Feeds                []*GameFeed   `protobuf:"bytes,2,rep,name=feeds,proto3" json:"feeds,omitempty"`
	LoadFinish           bool          `protobuf:"varint,3,opt,name=load_finish,json=loadFinish,proto3" json:"load_finish,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetGameNewsFeedsResp) Reset()         { *m = GetGameNewsFeedsResp{} }
func (m *GetGameNewsFeedsResp) String() string { return proto.CompactTextString(m) }
func (*GetGameNewsFeedsResp) ProtoMessage()    {}
func (*GetGameNewsFeedsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{1}
}
func (m *GetGameNewsFeedsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameNewsFeedsResp.Unmarshal(m, b)
}
func (m *GetGameNewsFeedsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameNewsFeedsResp.Marshal(b, m, deterministic)
}
func (dst *GetGameNewsFeedsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameNewsFeedsResp.Merge(dst, src)
}
func (m *GetGameNewsFeedsResp) XXX_Size() int {
	return xxx_messageInfo_GetGameNewsFeedsResp.Size(m)
}
func (m *GetGameNewsFeedsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameNewsFeedsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameNewsFeedsResp proto.InternalMessageInfo

func (m *GetGameNewsFeedsResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGameNewsFeedsResp) GetFeeds() []*GameFeed {
	if m != nil {
		return m.Feeds
	}
	return nil
}

func (m *GetGameNewsFeedsResp) GetLoadFinish() bool {
	if m != nil {
		return m.LoadFinish
	}
	return false
}

type GameUgcTopicInfo struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameUgcTopicInfo) Reset()         { *m = GameUgcTopicInfo{} }
func (m *GameUgcTopicInfo) String() string { return proto.CompactTextString(m) }
func (*GameUgcTopicInfo) ProtoMessage()    {}
func (*GameUgcTopicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{2}
}
func (m *GameUgcTopicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameUgcTopicInfo.Unmarshal(m, b)
}
func (m *GameUgcTopicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameUgcTopicInfo.Marshal(b, m, deterministic)
}
func (dst *GameUgcTopicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameUgcTopicInfo.Merge(dst, src)
}
func (m *GameUgcTopicInfo) XXX_Size() int {
	return xxx_messageInfo_GameUgcTopicInfo.Size(m)
}
func (m *GameUgcTopicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameUgcTopicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameUgcTopicInfo proto.InternalMessageInfo

func (m *GameUgcTopicInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GameUgcTopicInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type GameFeed struct {
	FeedId string `protobuf:"bytes,1,opt,name=feed_id,json=feedId,proto3" json:"feed_id,omitempty"`
	// Types that are valid to be assigned to FeedData:
	//	*GameFeed_Post
	//	*GameFeed_UgcPostInfo
	FeedData             isGameFeed_FeedData `protobuf_oneof:"feed_data"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GameFeed) Reset()         { *m = GameFeed{} }
func (m *GameFeed) String() string { return proto.CompactTextString(m) }
func (*GameFeed) ProtoMessage()    {}
func (*GameFeed) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{3}
}
func (m *GameFeed) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameFeed.Unmarshal(m, b)
}
func (m *GameFeed) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameFeed.Marshal(b, m, deterministic)
}
func (dst *GameFeed) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameFeed.Merge(dst, src)
}
func (m *GameFeed) XXX_Size() int {
	return xxx_messageInfo_GameFeed.Size(m)
}
func (m *GameFeed) XXX_DiscardUnknown() {
	xxx_messageInfo_GameFeed.DiscardUnknown(m)
}

var xxx_messageInfo_GameFeed proto.InternalMessageInfo

func (m *GameFeed) GetFeedId() string {
	if m != nil {
		return m.FeedId
	}
	return ""
}

type isGameFeed_FeedData interface {
	isGameFeed_FeedData()
}

type GameFeed_Post struct {
	Post *GameFeed_GamePostInfo `protobuf:"bytes,2,opt,name=post,proto3,oneof"`
}

type GameFeed_UgcPostInfo struct {
	UgcPostInfo *ugc.PostInfo `protobuf:"bytes,3,opt,name=ugc_post_info,json=ugcPostInfo,proto3,oneof"`
}

func (*GameFeed_Post) isGameFeed_FeedData() {}

func (*GameFeed_UgcPostInfo) isGameFeed_FeedData() {}

func (m *GameFeed) GetFeedData() isGameFeed_FeedData {
	if m != nil {
		return m.FeedData
	}
	return nil
}

func (m *GameFeed) GetPost() *GameFeed_GamePostInfo {
	if x, ok := m.GetFeedData().(*GameFeed_Post); ok {
		return x.Post
	}
	return nil
}

func (m *GameFeed) GetUgcPostInfo() *ugc.PostInfo {
	if x, ok := m.GetFeedData().(*GameFeed_UgcPostInfo); ok {
		return x.UgcPostInfo
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*GameFeed) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _GameFeed_OneofMarshaler, _GameFeed_OneofUnmarshaler, _GameFeed_OneofSizer, []interface{}{
		(*GameFeed_Post)(nil),
		(*GameFeed_UgcPostInfo)(nil),
	}
}

func _GameFeed_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*GameFeed)
	// feed_data
	switch x := m.FeedData.(type) {
	case *GameFeed_Post:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Post); err != nil {
			return err
		}
	case *GameFeed_UgcPostInfo:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UgcPostInfo); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("GameFeed.FeedData has unexpected type %T", x)
	}
	return nil
}

func _GameFeed_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*GameFeed)
	switch tag {
	case 2: // feed_data.post
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(GameFeed_GamePostInfo)
		err := b.DecodeMessage(msg)
		m.FeedData = &GameFeed_Post{msg}
		return true, err
	case 3: // feed_data.ugc_post_info
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ugc.PostInfo)
		err := b.DecodeMessage(msg)
		m.FeedData = &GameFeed_UgcPostInfo{msg}
		return true, err
	default:
		return false, nil
	}
}

func _GameFeed_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*GameFeed)
	// feed_data
	switch x := m.FeedData.(type) {
	case *GameFeed_Post:
		s := proto.Size(x.Post)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *GameFeed_UgcPostInfo:
		s := proto.Size(x.UgcPostInfo)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// 帖子
type GameFeed_GamePostInfo struct {
	PostInfo             *BasePostInfo         `protobuf:"bytes,1,opt,name=post_info,json=postInfo,proto3" json:"post_info,omitempty"`
	PostOwner            *PostOwnerInfo        `protobuf:"bytes,2,opt,name=post_owner,json=postOwner,proto3" json:"post_owner,omitempty"`
	Vote                 *ugc.VoteInfo         `protobuf:"bytes,12,opt,name=vote,proto3" json:"vote,omitempty"`
	RelationWithPost     *RelationWithPost     `protobuf:"bytes,4,opt,name=relation_with_post,json=relationWithPost,proto3" json:"relation_with_post,omitempty"`
	TopicInfos           []*GameUgcTopicInfo   `protobuf:"bytes,5,rep,name=topic_infos,json=topicInfos,proto3" json:"topic_infos,omitempty"`
	FollowInfo           *online.FriendsDetail `protobuf:"bytes,6,opt,name=follow_info,json=followInfo,proto3" json:"follow_info,omitempty"`
	ButtonType           uint32                `protobuf:"varint,7,opt,name=button_type,json=buttonType,proto3" json:"button_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GameFeed_GamePostInfo) Reset()         { *m = GameFeed_GamePostInfo{} }
func (m *GameFeed_GamePostInfo) String() string { return proto.CompactTextString(m) }
func (*GameFeed_GamePostInfo) ProtoMessage()    {}
func (*GameFeed_GamePostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{3, 0}
}
func (m *GameFeed_GamePostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameFeed_GamePostInfo.Unmarshal(m, b)
}
func (m *GameFeed_GamePostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameFeed_GamePostInfo.Marshal(b, m, deterministic)
}
func (dst *GameFeed_GamePostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameFeed_GamePostInfo.Merge(dst, src)
}
func (m *GameFeed_GamePostInfo) XXX_Size() int {
	return xxx_messageInfo_GameFeed_GamePostInfo.Size(m)
}
func (m *GameFeed_GamePostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameFeed_GamePostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameFeed_GamePostInfo proto.InternalMessageInfo

func (m *GameFeed_GamePostInfo) GetPostInfo() *BasePostInfo {
	if m != nil {
		return m.PostInfo
	}
	return nil
}

func (m *GameFeed_GamePostInfo) GetPostOwner() *PostOwnerInfo {
	if m != nil {
		return m.PostOwner
	}
	return nil
}

func (m *GameFeed_GamePostInfo) GetVote() *ugc.VoteInfo {
	if m != nil {
		return m.Vote
	}
	return nil
}

func (m *GameFeed_GamePostInfo) GetRelationWithPost() *RelationWithPost {
	if m != nil {
		return m.RelationWithPost
	}
	return nil
}

func (m *GameFeed_GamePostInfo) GetTopicInfos() []*GameUgcTopicInfo {
	if m != nil {
		return m.TopicInfos
	}
	return nil
}

func (m *GameFeed_GamePostInfo) GetFollowInfo() *online.FriendsDetail {
	if m != nil {
		return m.FollowInfo
	}
	return nil
}

func (m *GameFeed_GamePostInfo) GetButtonType() uint32 {
	if m != nil {
		return m.ButtonType
	}
	return 0
}

// 帖子信息
type BasePostInfo struct {
	PostId               string            `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PostType             uint32            `protobuf:"varint,2,opt,name=post_type,json=postType,proto3" json:"post_type,omitempty"`
	Content              string            `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	Attachments          []*ugc.Attachment `protobuf:"bytes,4,rep,name=attachments,proto3" json:"attachments,omitempty"`
	CommentCount         uint32            `protobuf:"varint,5,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	AttitudeCount        uint32            `protobuf:"varint,6,opt,name=attitude_count,json=attitudeCount,proto3" json:"attitude_count,omitempty"`
	ContentType          uint32            `protobuf:"varint,7,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	PostSource           uint32            `protobuf:"varint,8,opt,name=post_source,json=postSource,proto3" json:"post_source,omitempty"` // Deprecated: Do not use.
	Privacy              uint32            `protobuf:"varint,9,opt,name=privacy,proto3" json:"privacy,omitempty"`
	PostTime             int64             `protobuf:"varint,10,opt,name=post_time,json=postTime,proto3" json:"post_time,omitempty"`
	PostTitle            string            `protobuf:"bytes,11,opt,name=post_title,json=postTitle,proto3" json:"post_title,omitempty"`
	ShareCount           uint32            `protobuf:"varint,12,opt,name=share_count,json=shareCount,proto3" json:"share_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BasePostInfo) Reset()         { *m = BasePostInfo{} }
func (m *BasePostInfo) String() string { return proto.CompactTextString(m) }
func (*BasePostInfo) ProtoMessage()    {}
func (*BasePostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{4}
}
func (m *BasePostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BasePostInfo.Unmarshal(m, b)
}
func (m *BasePostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BasePostInfo.Marshal(b, m, deterministic)
}
func (dst *BasePostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BasePostInfo.Merge(dst, src)
}
func (m *BasePostInfo) XXX_Size() int {
	return xxx_messageInfo_BasePostInfo.Size(m)
}
func (m *BasePostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BasePostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BasePostInfo proto.InternalMessageInfo

func (m *BasePostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *BasePostInfo) GetPostType() uint32 {
	if m != nil {
		return m.PostType
	}
	return 0
}

func (m *BasePostInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *BasePostInfo) GetAttachments() []*ugc.Attachment {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *BasePostInfo) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *BasePostInfo) GetAttitudeCount() uint32 {
	if m != nil {
		return m.AttitudeCount
	}
	return 0
}

func (m *BasePostInfo) GetContentType() uint32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

// Deprecated: Do not use.
func (m *BasePostInfo) GetPostSource() uint32 {
	if m != nil {
		return m.PostSource
	}
	return 0
}

func (m *BasePostInfo) GetPrivacy() uint32 {
	if m != nil {
		return m.Privacy
	}
	return 0
}

func (m *BasePostInfo) GetPostTime() int64 {
	if m != nil {
		return m.PostTime
	}
	return 0
}

func (m *BasePostInfo) GetPostTitle() string {
	if m != nil {
		return m.PostTitle
	}
	return ""
}

func (m *BasePostInfo) GetShareCount() uint32 {
	if m != nil {
		return m.ShareCount
	}
	return 0
}

// 拉流用户与帖子的关系
type RelationWithPost struct {
	HadFollowedPoster bool `protobuf:"varint,1,opt,name=had_followed_poster,json=hadFollowedPoster,proto3" json:"had_followed_poster,omitempty"`
	// 调整代码结构时，忘记修改位序！！！导致后续新增字段位序错误，空白位序可以被后续新增字段占用
	HadAttitude          bool     `protobuf:"varint,7,opt,name=had_attitude,json=hadAttitude,proto3" json:"had_attitude,omitempty"`
	HadFavoured          bool     `protobuf:"varint,16,opt,name=had_favoured,json=hadFavoured,proto3" json:"had_favoured,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RelationWithPost) Reset()         { *m = RelationWithPost{} }
func (m *RelationWithPost) String() string { return proto.CompactTextString(m) }
func (*RelationWithPost) ProtoMessage()    {}
func (*RelationWithPost) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{5}
}
func (m *RelationWithPost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationWithPost.Unmarshal(m, b)
}
func (m *RelationWithPost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationWithPost.Marshal(b, m, deterministic)
}
func (dst *RelationWithPost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationWithPost.Merge(dst, src)
}
func (m *RelationWithPost) XXX_Size() int {
	return xxx_messageInfo_RelationWithPost.Size(m)
}
func (m *RelationWithPost) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationWithPost.DiscardUnknown(m)
}

var xxx_messageInfo_RelationWithPost proto.InternalMessageInfo

func (m *RelationWithPost) GetHadFollowedPoster() bool {
	if m != nil {
		return m.HadFollowedPoster
	}
	return false
}

func (m *RelationWithPost) GetHadAttitude() bool {
	if m != nil {
		return m.HadAttitude
	}
	return false
}

func (m *RelationWithPost) GetHadFavoured() bool {
	if m != nil {
		return m.HadFavoured
	}
	return false
}

// 帖子用户信息
type PostOwnerInfo struct {
	Uid                  uint32                      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string                      `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string                      `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Gender               uint32                      `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	GameCardInfo         *PostOwnerInfo_GameCardInfo `protobuf:"bytes,5,opt,name=game_card_info,json=gameCardInfo,proto3" json:"game_card_info,omitempty"`
	UserStatus           []uint32                    `protobuf:"varint,6,rep,packed,name=user_status,json=userStatus,proto3" json:"user_status,omitempty"`  // Deprecated: Do not use.
	UgcChannelId         uint32                      `protobuf:"varint,7,opt,name=ugc_channel_id,json=ugcChannelId,proto3" json:"ugc_channel_id,omitempty"` // Deprecated: Do not use.
	UserOnlineStatus     uint32                      `protobuf:"varint,8,opt,name=user_online_status,json=userOnlineStatus,proto3" json:"user_online_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *PostOwnerInfo) Reset()         { *m = PostOwnerInfo{} }
func (m *PostOwnerInfo) String() string { return proto.CompactTextString(m) }
func (*PostOwnerInfo) ProtoMessage()    {}
func (*PostOwnerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{6}
}
func (m *PostOwnerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostOwnerInfo.Unmarshal(m, b)
}
func (m *PostOwnerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostOwnerInfo.Marshal(b, m, deterministic)
}
func (dst *PostOwnerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostOwnerInfo.Merge(dst, src)
}
func (m *PostOwnerInfo) XXX_Size() int {
	return xxx_messageInfo_PostOwnerInfo.Size(m)
}
func (m *PostOwnerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PostOwnerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PostOwnerInfo proto.InternalMessageInfo

func (m *PostOwnerInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PostOwnerInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *PostOwnerInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *PostOwnerInfo) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *PostOwnerInfo) GetGameCardInfo() *PostOwnerInfo_GameCardInfo {
	if m != nil {
		return m.GameCardInfo
	}
	return nil
}

// Deprecated: Do not use.
func (m *PostOwnerInfo) GetUserStatus() []uint32 {
	if m != nil {
		return m.UserStatus
	}
	return nil
}

// Deprecated: Do not use.
func (m *PostOwnerInfo) GetUgcChannelId() uint32 {
	if m != nil {
		return m.UgcChannelId
	}
	return 0
}

func (m *PostOwnerInfo) GetUserOnlineStatus() uint32 {
	if m != nil {
		return m.UserOnlineStatus
	}
	return 0
}

type PostOwnerInfo_GameCardInfo struct {
	GameCardText         string   `protobuf:"bytes,1,opt,name=game_card_text,json=gameCardText,proto3" json:"game_card_text,omitempty"`
	GameCardId           uint32   `protobuf:"varint,2,opt,name=game_card_id,json=gameCardId,proto3" json:"game_card_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostOwnerInfo_GameCardInfo) Reset()         { *m = PostOwnerInfo_GameCardInfo{} }
func (m *PostOwnerInfo_GameCardInfo) String() string { return proto.CompactTextString(m) }
func (*PostOwnerInfo_GameCardInfo) ProtoMessage()    {}
func (*PostOwnerInfo_GameCardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{6, 0}
}
func (m *PostOwnerInfo_GameCardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostOwnerInfo_GameCardInfo.Unmarshal(m, b)
}
func (m *PostOwnerInfo_GameCardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostOwnerInfo_GameCardInfo.Marshal(b, m, deterministic)
}
func (dst *PostOwnerInfo_GameCardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostOwnerInfo_GameCardInfo.Merge(dst, src)
}
func (m *PostOwnerInfo_GameCardInfo) XXX_Size() int {
	return xxx_messageInfo_PostOwnerInfo_GameCardInfo.Size(m)
}
func (m *PostOwnerInfo_GameCardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PostOwnerInfo_GameCardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PostOwnerInfo_GameCardInfo proto.InternalMessageInfo

func (m *PostOwnerInfo_GameCardInfo) GetGameCardText() string {
	if m != nil {
		return m.GameCardText
	}
	return ""
}

func (m *PostOwnerInfo_GameCardInfo) GetGameCardId() uint32 {
	if m != nil {
		return m.GameCardId
	}
	return 0
}

// 发帖
type GamePostPostReq struct {
	BaseReq  *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Title    string       `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Content  string       `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	PostType uint32       `protobuf:"varint,4,opt,name=post_type,json=postType,proto3" json:"post_type,omitempty"`
	// ga.ugc.PostOrigin origin = 5[deprecated = true]; //废弃，// 发帖来源
	Origin                uint32            `protobuf:"varint,5,opt,name=origin,proto3" json:"origin,omitempty"`
	GameOrigin            uint32            `protobuf:"varint,6,opt,name=game_origin,json=gameOrigin,proto3" json:"game_origin,omitempty"`
	AttachmentImageCount  uint32            `protobuf:"varint,7,opt,name=attachment_image_count,json=attachmentImageCount,proto3" json:"attachment_image_count,omitempty"`
	AttachmentVideoCount  uint32            `protobuf:"varint,8,opt,name=attachment_video_count,json=attachmentVideoCount,proto3" json:"attachment_video_count,omitempty"`
	AttachmentAudioCount  uint32            `protobuf:"varint,9,opt,name=attachment_audio_count,json=attachmentAudioCount,proto3" json:"attachment_audio_count,omitempty"`
	PredefinedAttachments []*ugc.Attachment `protobuf:"bytes,10,rep,name=predefined_attachments,json=predefinedAttachments,proto3" json:"predefined_attachments,omitempty"`
	// ga.ugc.AttachmentDownloadPrivacy privacy = 11[deprecated = true];
	Privacy uint32 `protobuf:"varint,11,opt,name=privacy,proto3" json:"privacy,omitempty"`
	// extra 额外信息
	Extra *ugc.Extra `protobuf:"bytes,12,opt,name=extra,proto3" json:"extra,omitempty"`
	// 投票信息
	Vote                 *ugc.VoteInfo `protobuf:"bytes,13,opt,name=vote,proto3" json:"vote,omitempty"`
	ConfigTabId          uint32        `protobuf:"varint,14,opt,name=config_tab_id,json=configTabId,proto3" json:"config_tab_id,omitempty"` // Deprecated: Do not use.
	TabId                uint32        `protobuf:"varint,15,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	OriginNew            uint32        `protobuf:"varint,16,opt,name=origin_new,json=originNew,proto3" json:"origin_new,omitempty"`    // Deprecated: Do not use.
	PrivacyNew           uint32        `protobuf:"varint,17,opt,name=privacy_new,json=privacyNew,proto3" json:"privacy_new,omitempty"` // Deprecated: Do not use.
	ConfigTabIdNew       string        `protobuf:"bytes,18,opt,name=config_tab_id_new,json=configTabIdNew,proto3" json:"config_tab_id_new,omitempty"`
	VisibleScope         uint32        `protobuf:"varint,19,opt,name=visible_scope,json=visibleScope,proto3" json:"visible_scope,omitempty"`
	TopicId              string        `protobuf:"bytes,20,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GamePostPostReq) Reset()         { *m = GamePostPostReq{} }
func (m *GamePostPostReq) String() string { return proto.CompactTextString(m) }
func (*GamePostPostReq) ProtoMessage()    {}
func (*GamePostPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{7}
}
func (m *GamePostPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GamePostPostReq.Unmarshal(m, b)
}
func (m *GamePostPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GamePostPostReq.Marshal(b, m, deterministic)
}
func (dst *GamePostPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GamePostPostReq.Merge(dst, src)
}
func (m *GamePostPostReq) XXX_Size() int {
	return xxx_messageInfo_GamePostPostReq.Size(m)
}
func (m *GamePostPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GamePostPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_GamePostPostReq proto.InternalMessageInfo

func (m *GamePostPostReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GamePostPostReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GamePostPostReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GamePostPostReq) GetPostType() uint32 {
	if m != nil {
		return m.PostType
	}
	return 0
}

func (m *GamePostPostReq) GetOrigin() uint32 {
	if m != nil {
		return m.Origin
	}
	return 0
}

func (m *GamePostPostReq) GetGameOrigin() uint32 {
	if m != nil {
		return m.GameOrigin
	}
	return 0
}

func (m *GamePostPostReq) GetAttachmentImageCount() uint32 {
	if m != nil {
		return m.AttachmentImageCount
	}
	return 0
}

func (m *GamePostPostReq) GetAttachmentVideoCount() uint32 {
	if m != nil {
		return m.AttachmentVideoCount
	}
	return 0
}

func (m *GamePostPostReq) GetAttachmentAudioCount() uint32 {
	if m != nil {
		return m.AttachmentAudioCount
	}
	return 0
}

func (m *GamePostPostReq) GetPredefinedAttachments() []*ugc.Attachment {
	if m != nil {
		return m.PredefinedAttachments
	}
	return nil
}

func (m *GamePostPostReq) GetPrivacy() uint32 {
	if m != nil {
		return m.Privacy
	}
	return 0
}

func (m *GamePostPostReq) GetExtra() *ugc.Extra {
	if m != nil {
		return m.Extra
	}
	return nil
}

func (m *GamePostPostReq) GetVote() *ugc.VoteInfo {
	if m != nil {
		return m.Vote
	}
	return nil
}

// Deprecated: Do not use.
func (m *GamePostPostReq) GetConfigTabId() uint32 {
	if m != nil {
		return m.ConfigTabId
	}
	return 0
}

func (m *GamePostPostReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

// Deprecated: Do not use.
func (m *GamePostPostReq) GetOriginNew() uint32 {
	if m != nil {
		return m.OriginNew
	}
	return 0
}

// Deprecated: Do not use.
func (m *GamePostPostReq) GetPrivacyNew() uint32 {
	if m != nil {
		return m.PrivacyNew
	}
	return 0
}

func (m *GamePostPostReq) GetConfigTabIdNew() string {
	if m != nil {
		return m.ConfigTabIdNew
	}
	return ""
}

func (m *GamePostPostReq) GetVisibleScope() uint32 {
	if m != nil {
		return m.VisibleScope
	}
	return 0
}

func (m *GamePostPostReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type GamePostPostResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PostId               string        `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	ImageToken           string        `protobuf:"bytes,3,opt,name=image_token,json=imageToken,proto3" json:"image_token,omitempty"`
	VideoToken           string        `protobuf:"bytes,4,opt,name=video_token,json=videoToken,proto3" json:"video_token,omitempty"`
	ImageKeys            []string      `protobuf:"bytes,5,rep,name=image_keys,json=imageKeys,proto3" json:"image_keys,omitempty"`
	VideoKeys            []string      `protobuf:"bytes,6,rep,name=video_keys,json=videoKeys,proto3" json:"video_keys,omitempty"`
	AudioToken           string        `protobuf:"bytes,7,opt,name=audio_token,json=audioToken,proto3" json:"audio_token,omitempty"`
	AudioKeys            []string      `protobuf:"bytes,8,rep,name=audio_keys,json=audioKeys,proto3" json:"audio_keys,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GamePostPostResp) Reset()         { *m = GamePostPostResp{} }
func (m *GamePostPostResp) String() string { return proto.CompactTextString(m) }
func (*GamePostPostResp) ProtoMessage()    {}
func (*GamePostPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{8}
}
func (m *GamePostPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GamePostPostResp.Unmarshal(m, b)
}
func (m *GamePostPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GamePostPostResp.Marshal(b, m, deterministic)
}
func (dst *GamePostPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GamePostPostResp.Merge(dst, src)
}
func (m *GamePostPostResp) XXX_Size() int {
	return xxx_messageInfo_GamePostPostResp.Size(m)
}
func (m *GamePostPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GamePostPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_GamePostPostResp proto.InternalMessageInfo

func (m *GamePostPostResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GamePostPostResp) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GamePostPostResp) GetImageToken() string {
	if m != nil {
		return m.ImageToken
	}
	return ""
}

func (m *GamePostPostResp) GetVideoToken() string {
	if m != nil {
		return m.VideoToken
	}
	return ""
}

func (m *GamePostPostResp) GetImageKeys() []string {
	if m != nil {
		return m.ImageKeys
	}
	return nil
}

func (m *GamePostPostResp) GetVideoKeys() []string {
	if m != nil {
		return m.VideoKeys
	}
	return nil
}

func (m *GamePostPostResp) GetAudioToken() string {
	if m != nil {
		return m.AudioToken
	}
	return ""
}

func (m *GamePostPostResp) GetAudioKeys() []string {
	if m != nil {
		return m.AudioKeys
	}
	return nil
}

// 废弃结构
type SubTabConfig struct {
	SubTabId             uint32   `protobuf:"varint,1,opt,name=sub_tab_id,json=subTabId,proto3" json:"sub_tab_id,omitempty"`
	SubTabName           string   `protobuf:"bytes,2,opt,name=sub_tab_name,json=subTabName,proto3" json:"sub_tab_name,omitempty"`
	TabType              uint32   `protobuf:"varint,3,opt,name=tab_type,json=tabType,proto3" json:"tab_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubTabConfig) Reset()         { *m = SubTabConfig{} }
func (m *SubTabConfig) String() string { return proto.CompactTextString(m) }
func (*SubTabConfig) ProtoMessage()    {}
func (*SubTabConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{9}
}
func (m *SubTabConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubTabConfig.Unmarshal(m, b)
}
func (m *SubTabConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubTabConfig.Marshal(b, m, deterministic)
}
func (dst *SubTabConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubTabConfig.Merge(dst, src)
}
func (m *SubTabConfig) XXX_Size() int {
	return xxx_messageInfo_SubTabConfig.Size(m)
}
func (m *SubTabConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_SubTabConfig.DiscardUnknown(m)
}

var xxx_messageInfo_SubTabConfig proto.InternalMessageInfo

func (m *SubTabConfig) GetSubTabId() uint32 {
	if m != nil {
		return m.SubTabId
	}
	return 0
}

func (m *SubTabConfig) GetSubTabName() string {
	if m != nil {
		return m.SubTabName
	}
	return ""
}

func (m *SubTabConfig) GetTabType() uint32 {
	if m != nil {
		return m.TabType
	}
	return 0
}

// 废弃结构
type GetSubTabConfigByTabIdReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSubTabConfigByTabIdReq) Reset()         { *m = GetSubTabConfigByTabIdReq{} }
func (m *GetSubTabConfigByTabIdReq) String() string { return proto.CompactTextString(m) }
func (*GetSubTabConfigByTabIdReq) ProtoMessage()    {}
func (*GetSubTabConfigByTabIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{10}
}
func (m *GetSubTabConfigByTabIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSubTabConfigByTabIdReq.Unmarshal(m, b)
}
func (m *GetSubTabConfigByTabIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSubTabConfigByTabIdReq.Marshal(b, m, deterministic)
}
func (dst *GetSubTabConfigByTabIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSubTabConfigByTabIdReq.Merge(dst, src)
}
func (m *GetSubTabConfigByTabIdReq) XXX_Size() int {
	return xxx_messageInfo_GetSubTabConfigByTabIdReq.Size(m)
}
func (m *GetSubTabConfigByTabIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSubTabConfigByTabIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSubTabConfigByTabIdReq proto.InternalMessageInfo

func (m *GetSubTabConfigByTabIdReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSubTabConfigByTabIdReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

// 废弃结构
type GetSubTabConfigByTabIdResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Configs              []*SubTabConfig `protobuf:"bytes,2,rep,name=configs,proto3" json:"configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetSubTabConfigByTabIdResp) Reset()         { *m = GetSubTabConfigByTabIdResp{} }
func (m *GetSubTabConfigByTabIdResp) String() string { return proto.CompactTextString(m) }
func (*GetSubTabConfigByTabIdResp) ProtoMessage()    {}
func (*GetSubTabConfigByTabIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{11}
}
func (m *GetSubTabConfigByTabIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSubTabConfigByTabIdResp.Unmarshal(m, b)
}
func (m *GetSubTabConfigByTabIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSubTabConfigByTabIdResp.Marshal(b, m, deterministic)
}
func (dst *GetSubTabConfigByTabIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSubTabConfigByTabIdResp.Merge(dst, src)
}
func (m *GetSubTabConfigByTabIdResp) XXX_Size() int {
	return xxx_messageInfo_GetSubTabConfigByTabIdResp.Size(m)
}
func (m *GetSubTabConfigByTabIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSubTabConfigByTabIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSubTabConfigByTabIdResp proto.InternalMessageInfo

func (m *GetSubTabConfigByTabIdResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSubTabConfigByTabIdResp) GetConfigs() []*SubTabConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

type ConfigTab struct {
	ConfigTabId     string     `protobuf:"bytes,1,opt,name=config_tab_id,json=configTabId,proto3" json:"config_tab_id,omitempty"`
	ConfigTabName   string     `protobuf:"bytes,2,opt,name=config_tab_name,json=configTabName,proto3" json:"config_tab_name,omitempty"`
	TabType         uint32     `protobuf:"varint,3,opt,name=tab_type,json=tabType,proto3" json:"tab_type,omitempty"`
	RecRules        []*RecRule `protobuf:"bytes,4,rep,name=rec_rules,json=recRules,proto3" json:"rec_rules,omitempty"`
	ForceRecRule    uint32     `protobuf:"varint,5,opt,name=force_rec_rule,json=forceRecRule,proto3" json:"force_rec_rule,omitempty"`
	DisplayStrategy uint32     `protobuf:"varint,6,opt,name=display_strategy,json=displayStrategy,proto3" json:"display_strategy,omitempty"`
	// Types that are valid to be assigned to ExtInfo:
	//	*ConfigTab_UgcExtInfo
	//	*ConfigTab_GameHallExtInfo
	ExtInfo              isConfigTab_ExtInfo `protobuf_oneof:"ext_info"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ConfigTab) Reset()         { *m = ConfigTab{} }
func (m *ConfigTab) String() string { return proto.CompactTextString(m) }
func (*ConfigTab) ProtoMessage()    {}
func (*ConfigTab) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{12}
}
func (m *ConfigTab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfigTab.Unmarshal(m, b)
}
func (m *ConfigTab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfigTab.Marshal(b, m, deterministic)
}
func (dst *ConfigTab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfigTab.Merge(dst, src)
}
func (m *ConfigTab) XXX_Size() int {
	return xxx_messageInfo_ConfigTab.Size(m)
}
func (m *ConfigTab) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfigTab.DiscardUnknown(m)
}

var xxx_messageInfo_ConfigTab proto.InternalMessageInfo

func (m *ConfigTab) GetConfigTabId() string {
	if m != nil {
		return m.ConfigTabId
	}
	return ""
}

func (m *ConfigTab) GetConfigTabName() string {
	if m != nil {
		return m.ConfigTabName
	}
	return ""
}

func (m *ConfigTab) GetTabType() uint32 {
	if m != nil {
		return m.TabType
	}
	return 0
}

func (m *ConfigTab) GetRecRules() []*RecRule {
	if m != nil {
		return m.RecRules
	}
	return nil
}

func (m *ConfigTab) GetForceRecRule() uint32 {
	if m != nil {
		return m.ForceRecRule
	}
	return 0
}

func (m *ConfigTab) GetDisplayStrategy() uint32 {
	if m != nil {
		return m.DisplayStrategy
	}
	return 0
}

type isConfigTab_ExtInfo interface {
	isConfigTab_ExtInfo()
}

type ConfigTab_UgcExtInfo struct {
	UgcExtInfo *UgcTabExtInfo `protobuf:"bytes,7,opt,name=ugc_ext_info,json=ugcExtInfo,proto3,oneof"`
}

type ConfigTab_GameHallExtInfo struct {
	GameHallExtInfo *GameHallTabExtInfo `protobuf:"bytes,8,opt,name=game_hall_ext_info,json=gameHallExtInfo,proto3,oneof"`
}

func (*ConfigTab_UgcExtInfo) isConfigTab_ExtInfo() {}

func (*ConfigTab_GameHallExtInfo) isConfigTab_ExtInfo() {}

func (m *ConfigTab) GetExtInfo() isConfigTab_ExtInfo {
	if m != nil {
		return m.ExtInfo
	}
	return nil
}

func (m *ConfigTab) GetUgcExtInfo() *UgcTabExtInfo {
	if x, ok := m.GetExtInfo().(*ConfigTab_UgcExtInfo); ok {
		return x.UgcExtInfo
	}
	return nil
}

func (m *ConfigTab) GetGameHallExtInfo() *GameHallTabExtInfo {
	if x, ok := m.GetExtInfo().(*ConfigTab_GameHallExtInfo); ok {
		return x.GameHallExtInfo
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*ConfigTab) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _ConfigTab_OneofMarshaler, _ConfigTab_OneofUnmarshaler, _ConfigTab_OneofSizer, []interface{}{
		(*ConfigTab_UgcExtInfo)(nil),
		(*ConfigTab_GameHallExtInfo)(nil),
	}
}

func _ConfigTab_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*ConfigTab)
	// ext_info
	switch x := m.ExtInfo.(type) {
	case *ConfigTab_UgcExtInfo:
		b.EncodeVarint(7<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UgcExtInfo); err != nil {
			return err
		}
	case *ConfigTab_GameHallExtInfo:
		b.EncodeVarint(8<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.GameHallExtInfo); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("ConfigTab.ExtInfo has unexpected type %T", x)
	}
	return nil
}

func _ConfigTab_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*ConfigTab)
	switch tag {
	case 7: // ext_info.ugc_ext_info
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(UgcTabExtInfo)
		err := b.DecodeMessage(msg)
		m.ExtInfo = &ConfigTab_UgcExtInfo{msg}
		return true, err
	case 8: // ext_info.game_hall_ext_info
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(GameHallTabExtInfo)
		err := b.DecodeMessage(msg)
		m.ExtInfo = &ConfigTab_GameHallExtInfo{msg}
		return true, err
	default:
		return false, nil
	}
}

func _ConfigTab_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*ConfigTab)
	// ext_info
	switch x := m.ExtInfo.(type) {
	case *ConfigTab_UgcExtInfo:
		s := proto.Size(x.UgcExtInfo)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ConfigTab_GameHallExtInfo:
		s := proto.Size(x.GameHallExtInfo)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type UgcTabExtInfo struct {
	DisplayTopics        []*GameUgcTopicInfo `protobuf:"bytes,1,rep,name=display_topics,json=displayTopics,proto3" json:"display_topics,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UgcTabExtInfo) Reset()         { *m = UgcTabExtInfo{} }
func (m *UgcTabExtInfo) String() string { return proto.CompactTextString(m) }
func (*UgcTabExtInfo) ProtoMessage()    {}
func (*UgcTabExtInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{13}
}
func (m *UgcTabExtInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UgcTabExtInfo.Unmarshal(m, b)
}
func (m *UgcTabExtInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UgcTabExtInfo.Marshal(b, m, deterministic)
}
func (dst *UgcTabExtInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UgcTabExtInfo.Merge(dst, src)
}
func (m *UgcTabExtInfo) XXX_Size() int {
	return xxx_messageInfo_UgcTabExtInfo.Size(m)
}
func (m *UgcTabExtInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UgcTabExtInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UgcTabExtInfo proto.InternalMessageInfo

func (m *UgcTabExtInfo) GetDisplayTopics() []*GameUgcTopicInfo {
	if m != nil {
		return m.DisplayTopics
	}
	return nil
}

type GameHallTabExtInfo struct {
	// 房间列表强插组队大厅入口位置
	ChannelListPos       uint32   `protobuf:"varint,1,opt,name=channel_list_pos,json=channelListPos,proto3" json:"channel_list_pos,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameHallTabExtInfo) Reset()         { *m = GameHallTabExtInfo{} }
func (m *GameHallTabExtInfo) String() string { return proto.CompactTextString(m) }
func (*GameHallTabExtInfo) ProtoMessage()    {}
func (*GameHallTabExtInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{14}
}
func (m *GameHallTabExtInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameHallTabExtInfo.Unmarshal(m, b)
}
func (m *GameHallTabExtInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameHallTabExtInfo.Marshal(b, m, deterministic)
}
func (dst *GameHallTabExtInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameHallTabExtInfo.Merge(dst, src)
}
func (m *GameHallTabExtInfo) XXX_Size() int {
	return xxx_messageInfo_GameHallTabExtInfo.Size(m)
}
func (m *GameHallTabExtInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameHallTabExtInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameHallTabExtInfo proto.InternalMessageInfo

func (m *GameHallTabExtInfo) GetChannelListPos() uint32 {
	if m != nil {
		return m.ChannelListPos
	}
	return 0
}

type RecRule struct {
	RecRules             uint32   `protobuf:"varint,1,opt,name=rec_rules,json=recRules,proto3" json:"rec_rules,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecRule) Reset()         { *m = RecRule{} }
func (m *RecRule) String() string { return proto.CompactTextString(m) }
func (*RecRule) ProtoMessage()    {}
func (*RecRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{15}
}
func (m *RecRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecRule.Unmarshal(m, b)
}
func (m *RecRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecRule.Marshal(b, m, deterministic)
}
func (dst *RecRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecRule.Merge(dst, src)
}
func (m *RecRule) XXX_Size() int {
	return xxx_messageInfo_RecRule.Size(m)
}
func (m *RecRule) XXX_DiscardUnknown() {
	xxx_messageInfo_RecRule.DiscardUnknown(m)
}

var xxx_messageInfo_RecRule proto.InternalMessageInfo

func (m *RecRule) GetRecRules() uint32 {
	if m != nil {
		return m.RecRules
	}
	return 0
}

func (m *RecRule) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type GetConfigTabByTabIdReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	VisibleConfigTabType []uint32     `protobuf:"varint,3,rep,packed,name=visible_config_tab_type,json=visibleConfigTabType,proto3" json:"visible_config_tab_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetConfigTabByTabIdReq) Reset()         { *m = GetConfigTabByTabIdReq{} }
func (m *GetConfigTabByTabIdReq) String() string { return proto.CompactTextString(m) }
func (*GetConfigTabByTabIdReq) ProtoMessage()    {}
func (*GetConfigTabByTabIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{16}
}
func (m *GetConfigTabByTabIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConfigTabByTabIdReq.Unmarshal(m, b)
}
func (m *GetConfigTabByTabIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConfigTabByTabIdReq.Marshal(b, m, deterministic)
}
func (dst *GetConfigTabByTabIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConfigTabByTabIdReq.Merge(dst, src)
}
func (m *GetConfigTabByTabIdReq) XXX_Size() int {
	return xxx_messageInfo_GetConfigTabByTabIdReq.Size(m)
}
func (m *GetConfigTabByTabIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConfigTabByTabIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConfigTabByTabIdReq proto.InternalMessageInfo

func (m *GetConfigTabByTabIdReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetConfigTabByTabIdReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetConfigTabByTabIdReq) GetVisibleConfigTabType() []uint32 {
	if m != nil {
		return m.VisibleConfigTabType
	}
	return nil
}

type GetConfigTabByTabIdResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Configs              []*ConfigTab  `protobuf:"bytes,2,rep,name=configs,proto3" json:"configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetConfigTabByTabIdResp) Reset()         { *m = GetConfigTabByTabIdResp{} }
func (m *GetConfigTabByTabIdResp) String() string { return proto.CompactTextString(m) }
func (*GetConfigTabByTabIdResp) ProtoMessage()    {}
func (*GetConfigTabByTabIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{17}
}
func (m *GetConfigTabByTabIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConfigTabByTabIdResp.Unmarshal(m, b)
}
func (m *GetConfigTabByTabIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConfigTabByTabIdResp.Marshal(b, m, deterministic)
}
func (dst *GetConfigTabByTabIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConfigTabByTabIdResp.Merge(dst, src)
}
func (m *GetConfigTabByTabIdResp) XXX_Size() int {
	return xxx_messageInfo_GetConfigTabByTabIdResp.Size(m)
}
func (m *GetConfigTabByTabIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConfigTabByTabIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConfigTabByTabIdResp proto.InternalMessageInfo

func (m *GetConfigTabByTabIdResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetConfigTabByTabIdResp) GetConfigs() []*ConfigTab {
	if m != nil {
		return m.Configs
	}
	return nil
}

type GetConfigTabTitleReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ConfigTabId          string       `protobuf:"bytes,2,opt,name=config_tab_id,json=configTabId,proto3" json:"config_tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetConfigTabTitleReq) Reset()         { *m = GetConfigTabTitleReq{} }
func (m *GetConfigTabTitleReq) String() string { return proto.CompactTextString(m) }
func (*GetConfigTabTitleReq) ProtoMessage()    {}
func (*GetConfigTabTitleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{18}
}
func (m *GetConfigTabTitleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConfigTabTitleReq.Unmarshal(m, b)
}
func (m *GetConfigTabTitleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConfigTabTitleReq.Marshal(b, m, deterministic)
}
func (dst *GetConfigTabTitleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConfigTabTitleReq.Merge(dst, src)
}
func (m *GetConfigTabTitleReq) XXX_Size() int {
	return xxx_messageInfo_GetConfigTabTitleReq.Size(m)
}
func (m *GetConfigTabTitleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConfigTabTitleReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConfigTabTitleReq proto.InternalMessageInfo

func (m *GetConfigTabTitleReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetConfigTabTitleReq) GetConfigTabId() string {
	if m != nil {
		return m.ConfigTabId
	}
	return ""
}

type GetConfigTabTitleResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Title                []string      `protobuf:"bytes,2,rep,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetConfigTabTitleResp) Reset()         { *m = GetConfigTabTitleResp{} }
func (m *GetConfigTabTitleResp) String() string { return proto.CompactTextString(m) }
func (*GetConfigTabTitleResp) ProtoMessage()    {}
func (*GetConfigTabTitleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{19}
}
func (m *GetConfigTabTitleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConfigTabTitleResp.Unmarshal(m, b)
}
func (m *GetConfigTabTitleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConfigTabTitleResp.Marshal(b, m, deterministic)
}
func (dst *GetConfigTabTitleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConfigTabTitleResp.Merge(dst, src)
}
func (m *GetConfigTabTitleResp) XXX_Size() int {
	return xxx_messageInfo_GetConfigTabTitleResp.Size(m)
}
func (m *GetConfigTabTitleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConfigTabTitleResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConfigTabTitleResp proto.InternalMessageInfo

func (m *GetConfigTabTitleResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetConfigTabTitleResp) GetTitle() []string {
	if m != nil {
		return m.Title
	}
	return nil
}

type CheckUserIsBannedPostReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BanPostType          uint32       `protobuf:"varint,3,opt,name=ban_post_type,json=banPostType,proto3" json:"ban_post_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckUserIsBannedPostReq) Reset()         { *m = CheckUserIsBannedPostReq{} }
func (m *CheckUserIsBannedPostReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserIsBannedPostReq) ProtoMessage()    {}
func (*CheckUserIsBannedPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{20}
}
func (m *CheckUserIsBannedPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserIsBannedPostReq.Unmarshal(m, b)
}
func (m *CheckUserIsBannedPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserIsBannedPostReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserIsBannedPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserIsBannedPostReq.Merge(dst, src)
}
func (m *CheckUserIsBannedPostReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserIsBannedPostReq.Size(m)
}
func (m *CheckUserIsBannedPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserIsBannedPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserIsBannedPostReq proto.InternalMessageInfo

func (m *CheckUserIsBannedPostReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckUserIsBannedPostReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *CheckUserIsBannedPostReq) GetBanPostType() uint32 {
	if m != nil {
		return m.BanPostType
	}
	return 0
}

type CheckUserIsBannedPostResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsBanned             bool          `protobuf:"varint,2,opt,name=is_banned,json=isBanned,proto3" json:"is_banned,omitempty"`
	BannedReason         string        `protobuf:"bytes,3,opt,name=banned_reason,json=bannedReason,proto3" json:"banned_reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CheckUserIsBannedPostResp) Reset()         { *m = CheckUserIsBannedPostResp{} }
func (m *CheckUserIsBannedPostResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserIsBannedPostResp) ProtoMessage()    {}
func (*CheckUserIsBannedPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{21}
}
func (m *CheckUserIsBannedPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserIsBannedPostResp.Unmarshal(m, b)
}
func (m *CheckUserIsBannedPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserIsBannedPostResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserIsBannedPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserIsBannedPostResp.Merge(dst, src)
}
func (m *CheckUserIsBannedPostResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserIsBannedPostResp.Size(m)
}
func (m *CheckUserIsBannedPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserIsBannedPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserIsBannedPostResp proto.InternalMessageInfo

func (m *CheckUserIsBannedPostResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckUserIsBannedPostResp) GetIsBanned() bool {
	if m != nil {
		return m.IsBanned
	}
	return false
}

func (m *CheckUserIsBannedPostResp) GetBannedReason() string {
	if m != nil {
		return m.BannedReason
	}
	return ""
}

// 综合频道信息
type GetComprehensiveChannelInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	VisibleConfigTabType []uint32     `protobuf:"varint,3,rep,packed,name=visible_config_tab_type,json=visibleConfigTabType,proto3" json:"visible_config_tab_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetComprehensiveChannelInfoReq) Reset()         { *m = GetComprehensiveChannelInfoReq{} }
func (m *GetComprehensiveChannelInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetComprehensiveChannelInfoReq) ProtoMessage()    {}
func (*GetComprehensiveChannelInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{22}
}
func (m *GetComprehensiveChannelInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetComprehensiveChannelInfoReq.Unmarshal(m, b)
}
func (m *GetComprehensiveChannelInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetComprehensiveChannelInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetComprehensiveChannelInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetComprehensiveChannelInfoReq.Merge(dst, src)
}
func (m *GetComprehensiveChannelInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetComprehensiveChannelInfoReq.Size(m)
}
func (m *GetComprehensiveChannelInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetComprehensiveChannelInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetComprehensiveChannelInfoReq proto.InternalMessageInfo

func (m *GetComprehensiveChannelInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetComprehensiveChannelInfoReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetComprehensiveChannelInfoReq) GetVisibleConfigTabType() []uint32 {
	if m != nil {
		return m.VisibleConfigTabType
	}
	return nil
}

type GetComprehensiveChannelInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ActivityTabs         []*ConfigTab  `protobuf:"bytes,2,rep,name=activity_tabs,json=activityTabs,proto3" json:"activity_tabs,omitempty"`
	ConfigTabs           []*ConfigTab  `protobuf:"bytes,3,rep,name=config_tabs,json=configTabs,proto3" json:"config_tabs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetComprehensiveChannelInfoResp) Reset()         { *m = GetComprehensiveChannelInfoResp{} }
func (m *GetComprehensiveChannelInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetComprehensiveChannelInfoResp) ProtoMessage()    {}
func (*GetComprehensiveChannelInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{23}
}
func (m *GetComprehensiveChannelInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetComprehensiveChannelInfoResp.Unmarshal(m, b)
}
func (m *GetComprehensiveChannelInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetComprehensiveChannelInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetComprehensiveChannelInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetComprehensiveChannelInfoResp.Merge(dst, src)
}
func (m *GetComprehensiveChannelInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetComprehensiveChannelInfoResp.Size(m)
}
func (m *GetComprehensiveChannelInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetComprehensiveChannelInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetComprehensiveChannelInfoResp proto.InternalMessageInfo

func (m *GetComprehensiveChannelInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetComprehensiveChannelInfoResp) GetActivityTabs() []*ConfigTab {
	if m != nil {
		return m.ActivityTabs
	}
	return nil
}

func (m *GetComprehensiveChannelInfoResp) GetConfigTabs() []*ConfigTab {
	if m != nil {
		return m.ConfigTabs
	}
	return nil
}

// 根据帖子id，获取帖子列表信息
type GetGameFeedByIdsReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PostIds              []string     `protobuf:"bytes,2,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGameFeedByIdsReq) Reset()         { *m = GetGameFeedByIdsReq{} }
func (m *GetGameFeedByIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetGameFeedByIdsReq) ProtoMessage()    {}
func (*GetGameFeedByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{24}
}
func (m *GetGameFeedByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameFeedByIdsReq.Unmarshal(m, b)
}
func (m *GetGameFeedByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameFeedByIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetGameFeedByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameFeedByIdsReq.Merge(dst, src)
}
func (m *GetGameFeedByIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetGameFeedByIdsReq.Size(m)
}
func (m *GetGameFeedByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameFeedByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameFeedByIdsReq proto.InternalMessageInfo

func (m *GetGameFeedByIdsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameFeedByIdsReq) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

type GetGameFeedByIdsResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Feeds                []*GameFeed   `protobuf:"bytes,2,rep,name=feeds,proto3" json:"feeds,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetGameFeedByIdsResp) Reset()         { *m = GetGameFeedByIdsResp{} }
func (m *GetGameFeedByIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetGameFeedByIdsResp) ProtoMessage()    {}
func (*GetGameFeedByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{25}
}
func (m *GetGameFeedByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameFeedByIdsResp.Unmarshal(m, b)
}
func (m *GetGameFeedByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameFeedByIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetGameFeedByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameFeedByIdsResp.Merge(dst, src)
}
func (m *GetGameFeedByIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetGameFeedByIdsResp.Size(m)
}
func (m *GetGameFeedByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameFeedByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameFeedByIdsResp proto.InternalMessageInfo

func (m *GetGameFeedByIdsResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGameFeedByIdsResp) GetFeeds() []*GameFeed {
	if m != nil {
		return m.Feeds
	}
	return nil
}

// 是否展示个人页筛选
type NeedShowPersonalSourceFilterReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *NeedShowPersonalSourceFilterReq) Reset()         { *m = NeedShowPersonalSourceFilterReq{} }
func (m *NeedShowPersonalSourceFilterReq) String() string { return proto.CompactTextString(m) }
func (*NeedShowPersonalSourceFilterReq) ProtoMessage()    {}
func (*NeedShowPersonalSourceFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{26}
}
func (m *NeedShowPersonalSourceFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NeedShowPersonalSourceFilterReq.Unmarshal(m, b)
}
func (m *NeedShowPersonalSourceFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NeedShowPersonalSourceFilterReq.Marshal(b, m, deterministic)
}
func (dst *NeedShowPersonalSourceFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NeedShowPersonalSourceFilterReq.Merge(dst, src)
}
func (m *NeedShowPersonalSourceFilterReq) XXX_Size() int {
	return xxx_messageInfo_NeedShowPersonalSourceFilterReq.Size(m)
}
func (m *NeedShowPersonalSourceFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NeedShowPersonalSourceFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_NeedShowPersonalSourceFilterReq proto.InternalMessageInfo

func (m *NeedShowPersonalSourceFilterReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type NeedShowPersonalSourceFilterResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsShow               bool          `protobuf:"varint,2,opt,name=is_show,json=isShow,proto3" json:"is_show,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *NeedShowPersonalSourceFilterResp) Reset()         { *m = NeedShowPersonalSourceFilterResp{} }
func (m *NeedShowPersonalSourceFilterResp) String() string { return proto.CompactTextString(m) }
func (*NeedShowPersonalSourceFilterResp) ProtoMessage()    {}
func (*NeedShowPersonalSourceFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{27}
}
func (m *NeedShowPersonalSourceFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NeedShowPersonalSourceFilterResp.Unmarshal(m, b)
}
func (m *NeedShowPersonalSourceFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NeedShowPersonalSourceFilterResp.Marshal(b, m, deterministic)
}
func (dst *NeedShowPersonalSourceFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NeedShowPersonalSourceFilterResp.Merge(dst, src)
}
func (m *NeedShowPersonalSourceFilterResp) XXX_Size() int {
	return xxx_messageInfo_NeedShowPersonalSourceFilterResp.Size(m)
}
func (m *NeedShowPersonalSourceFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NeedShowPersonalSourceFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_NeedShowPersonalSourceFilterResp proto.InternalMessageInfo

func (m *NeedShowPersonalSourceFilterResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *NeedShowPersonalSourceFilterResp) GetIsShow() bool {
	if m != nil {
		return m.IsShow
	}
	return false
}

// 玩法二级tab是否可见
type IsConfigTabVisibleReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ConfigTabType        uint32       `protobuf:"varint,3,opt,name=config_tab_type,json=configTabType,proto3" json:"config_tab_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *IsConfigTabVisibleReq) Reset()         { *m = IsConfigTabVisibleReq{} }
func (m *IsConfigTabVisibleReq) String() string { return proto.CompactTextString(m) }
func (*IsConfigTabVisibleReq) ProtoMessage()    {}
func (*IsConfigTabVisibleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{28}
}
func (m *IsConfigTabVisibleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsConfigTabVisibleReq.Unmarshal(m, b)
}
func (m *IsConfigTabVisibleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsConfigTabVisibleReq.Marshal(b, m, deterministic)
}
func (dst *IsConfigTabVisibleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsConfigTabVisibleReq.Merge(dst, src)
}
func (m *IsConfigTabVisibleReq) XXX_Size() int {
	return xxx_messageInfo_IsConfigTabVisibleReq.Size(m)
}
func (m *IsConfigTabVisibleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsConfigTabVisibleReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsConfigTabVisibleReq proto.InternalMessageInfo

func (m *IsConfigTabVisibleReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *IsConfigTabVisibleReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *IsConfigTabVisibleReq) GetConfigTabType() uint32 {
	if m != nil {
		return m.ConfigTabType
	}
	return 0
}

type IsConfigTabVisibleResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsVisible            bool          `protobuf:"varint,2,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *IsConfigTabVisibleResp) Reset()         { *m = IsConfigTabVisibleResp{} }
func (m *IsConfigTabVisibleResp) String() string { return proto.CompactTextString(m) }
func (*IsConfigTabVisibleResp) ProtoMessage()    {}
func (*IsConfigTabVisibleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_ugc_c704952e413519e6, []int{29}
}
func (m *IsConfigTabVisibleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsConfigTabVisibleResp.Unmarshal(m, b)
}
func (m *IsConfigTabVisibleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsConfigTabVisibleResp.Marshal(b, m, deterministic)
}
func (dst *IsConfigTabVisibleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsConfigTabVisibleResp.Merge(dst, src)
}
func (m *IsConfigTabVisibleResp) XXX_Size() int {
	return xxx_messageInfo_IsConfigTabVisibleResp.Size(m)
}
func (m *IsConfigTabVisibleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsConfigTabVisibleResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsConfigTabVisibleResp proto.InternalMessageInfo

func (m *IsConfigTabVisibleResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *IsConfigTabVisibleResp) GetIsVisible() bool {
	if m != nil {
		return m.IsVisible
	}
	return false
}

func init() {
	proto.RegisterType((*GetGameNewsFeedsReq)(nil), "ga.game_ugc.GetGameNewsFeedsReq")
	proto.RegisterType((*GetGameNewsFeedsReq_GameRecommendationStreamReq)(nil), "ga.game_ugc.GetGameNewsFeedsReq.GameRecommendationStreamReq")
	proto.RegisterType((*GetGameNewsFeedsReq_GetPersonalSourcePostReq)(nil), "ga.game_ugc.GetGameNewsFeedsReq.GetPersonalSourcePostReq")
	proto.RegisterType((*GetGameNewsFeedsResp)(nil), "ga.game_ugc.GetGameNewsFeedsResp")
	proto.RegisterType((*GameUgcTopicInfo)(nil), "ga.game_ugc.GameUgcTopicInfo")
	proto.RegisterType((*GameFeed)(nil), "ga.game_ugc.GameFeed")
	proto.RegisterType((*GameFeed_GamePostInfo)(nil), "ga.game_ugc.GameFeed.GamePostInfo")
	proto.RegisterType((*BasePostInfo)(nil), "ga.game_ugc.BasePostInfo")
	proto.RegisterType((*RelationWithPost)(nil), "ga.game_ugc.RelationWithPost")
	proto.RegisterType((*PostOwnerInfo)(nil), "ga.game_ugc.PostOwnerInfo")
	proto.RegisterType((*PostOwnerInfo_GameCardInfo)(nil), "ga.game_ugc.PostOwnerInfo.GameCardInfo")
	proto.RegisterType((*GamePostPostReq)(nil), "ga.game_ugc.GamePostPostReq")
	proto.RegisterType((*GamePostPostResp)(nil), "ga.game_ugc.GamePostPostResp")
	proto.RegisterType((*SubTabConfig)(nil), "ga.game_ugc.SubTabConfig")
	proto.RegisterType((*GetSubTabConfigByTabIdReq)(nil), "ga.game_ugc.GetSubTabConfigByTabIdReq")
	proto.RegisterType((*GetSubTabConfigByTabIdResp)(nil), "ga.game_ugc.GetSubTabConfigByTabIdResp")
	proto.RegisterType((*ConfigTab)(nil), "ga.game_ugc.ConfigTab")
	proto.RegisterType((*UgcTabExtInfo)(nil), "ga.game_ugc.UgcTabExtInfo")
	proto.RegisterType((*GameHallTabExtInfo)(nil), "ga.game_ugc.GameHallTabExtInfo")
	proto.RegisterType((*RecRule)(nil), "ga.game_ugc.RecRule")
	proto.RegisterType((*GetConfigTabByTabIdReq)(nil), "ga.game_ugc.GetConfigTabByTabIdReq")
	proto.RegisterType((*GetConfigTabByTabIdResp)(nil), "ga.game_ugc.GetConfigTabByTabIdResp")
	proto.RegisterType((*GetConfigTabTitleReq)(nil), "ga.game_ugc.GetConfigTabTitleReq")
	proto.RegisterType((*GetConfigTabTitleResp)(nil), "ga.game_ugc.GetConfigTabTitleResp")
	proto.RegisterType((*CheckUserIsBannedPostReq)(nil), "ga.game_ugc.CheckUserIsBannedPostReq")
	proto.RegisterType((*CheckUserIsBannedPostResp)(nil), "ga.game_ugc.CheckUserIsBannedPostResp")
	proto.RegisterType((*GetComprehensiveChannelInfoReq)(nil), "ga.game_ugc.GetComprehensiveChannelInfoReq")
	proto.RegisterType((*GetComprehensiveChannelInfoResp)(nil), "ga.game_ugc.GetComprehensiveChannelInfoResp")
	proto.RegisterType((*GetGameFeedByIdsReq)(nil), "ga.game_ugc.GetGameFeedByIdsReq")
	proto.RegisterType((*GetGameFeedByIdsResp)(nil), "ga.game_ugc.GetGameFeedByIdsResp")
	proto.RegisterType((*NeedShowPersonalSourceFilterReq)(nil), "ga.game_ugc.NeedShowPersonalSourceFilterReq")
	proto.RegisterType((*NeedShowPersonalSourceFilterResp)(nil), "ga.game_ugc.NeedShowPersonalSourceFilterResp")
	proto.RegisterType((*IsConfigTabVisibleReq)(nil), "ga.game_ugc.IsConfigTabVisibleReq")
	proto.RegisterType((*IsConfigTabVisibleResp)(nil), "ga.game_ugc.IsConfigTabVisibleResp")
	proto.RegisterEnum("ga.game_ugc.PersonalPageFilterType", PersonalPageFilterType_name, PersonalPageFilterType_value)
	proto.RegisterEnum("ga.game_ugc.GamePostButtonType", GamePostButtonType_name, GamePostButtonType_value)
	proto.RegisterEnum("ga.game_ugc.UserStatus", UserStatus_name, UserStatus_value)
	proto.RegisterEnum("ga.game_ugc.VisibleScope", VisibleScope_name, VisibleScope_value)
	proto.RegisterEnum("ga.game_ugc.GamePostOrigin", GamePostOrigin_name, GamePostOrigin_value)
	proto.RegisterEnum("ga.game_ugc.ConfigTabType", ConfigTabType_name, ConfigTabType_value)
	proto.RegisterEnum("ga.game_ugc.BasePostInfo_PostSource", BasePostInfo_PostSource_name, BasePostInfo_PostSource_value)
}

func init() { proto.RegisterFile("game_ugc/game_ugc.proto", fileDescriptor_game_ugc_c704952e413519e6) }

var fileDescriptor_game_ugc_c704952e413519e6 = []byte{
	// 2674 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x59, 0x5b, 0x73, 0x1a, 0xc9,
	0xf5, 0x5f, 0x84, 0x24, 0xe0, 0x00, 0x32, 0xdb, 0xb6, 0x25, 0x2c, 0x59, 0x2b, 0x79, 0x6c, 0xef,
	0xca, 0xfe, 0xff, 0x23, 0x27, 0xde, 0x4b, 0xb2, 0x49, 0x6a, 0xab, 0x10, 0x46, 0xf2, 0x64, 0x31,
	0x28, 0x03, 0xd2, 0x5e, 0xaa, 0x52, 0x53, 0xcd, 0x4c, 0x03, 0x53, 0x1e, 0x66, 0xf0, 0x74, 0x23,
	0x59, 0x95, 0xcb, 0xcb, 0xbe, 0xe4, 0x29, 0x49, 0x25, 0x55, 0x9b, 0xf7, 0xbc, 0xe7, 0x31, 0x5f,
	0x20, 0x1f, 0x28, 0xf9, 0x0a, 0xa9, 0x3e, 0xdd, 0x03, 0x33, 0x08, 0xd9, 0xab, 0xaa, 0x4d, 0xe5,
	0x8d, 0x3e, 0xb7, 0x3e, 0x7d, 0x2e, 0xbf, 0x3e, 0x3d, 0xc0, 0xc6, 0x80, 0x8e, 0x98, 0x3d, 0x19,
	0x38, 0x4f, 0xe2, 0x1f, 0xfb, 0xe3, 0x28, 0x14, 0x21, 0x29, 0x0e, 0xe8, 0x7e, 0x4c, 0xda, 0x2c,
	0x0f, 0xa8, 0xdd, 0xa3, 0x9c, 0x29, 0xde, 0xe6, 0x9a, 0x94, 0x9f, 0x0c, 0x1c, 0x5b, 0xaf, 0xd7,
	0xc3, 0xc0, 0xf7, 0x02, 0xf6, 0xa4, 0x1f, 0x79, 0x2c, 0x70, 0x43, 0x5f, 0xd3, 0x8d, 0x7f, 0xad,
	0xc0, 0xcd, 0x23, 0x26, 0x8e, 0xe8, 0x88, 0xb5, 0xd8, 0x39, 0x3f, 0x64, 0xcc, 0xe5, 0x16, 0x7b,
	0x45, 0xde, 0x87, 0xbc, 0xb4, 0x66, 0x47, 0xec, 0x55, 0x35, 0xb3, 0x9b, 0xd9, 0x2b, 0x3e, 0x2d,
	0xee, 0x0f, 0xe8, 0xfe, 0x01, 0xe5, 0xcc, 0x62, 0xaf, 0xac, 0x5c, 0x4f, 0xfd, 0x20, 0x77, 0x20,
	0x3f, 0x60, 0xc2, 0x1e, 0x85, 0x2e, 0xab, 0x2e, 0xed, 0x66, 0xf6, 0xca, 0x56, 0x6e, 0xc0, 0xc4,
	0x8b, 0xd0, 0x65, 0xe4, 0x21, 0x94, 0x9c, 0x30, 0x10, 0x2c, 0x10, 0xb6, 0xb8, 0x18, 0xb3, 0x6a,
	0x56, 0xb2, 0x0f, 0x96, 0xaa, 0x19, 0xab, 0xa8, 0xe9, 0xdd, 0x8b, 0x31, 0x23, 0x3b, 0x50, 0xec,
	0x45, 0xe1, 0x39, 0x67, 0xb6, 0xef, 0x71, 0x51, 0x5d, 0xde, 0xcd, 0xee, 0x15, 0x2c, 0x50, 0xa4,
	0xa6, 0xc7, 0x05, 0xf9, 0x26, 0x03, 0xef, 0xe1, 0x31, 0x23, 0xe6, 0x84, 0xa3, 0x11, 0x0b, 0x5c,
	0x2a, 0xbc, 0x30, 0xb0, 0xb9, 0x88, 0x18, 0x1d, 0xa1, 0x87, 0x2b, 0xe8, 0xe1, 0xcf, 0xf7, 0x13,
	0x01, 0xd9, 0x5f, 0x70, 0xaa, 0x7d, 0x49, 0xb0, 0x52, 0x56, 0x3a, 0x68, 0xc4, 0x62, 0xaf, 0x9e,
	0xbf, 0x63, 0x6d, 0x0d, 0xae, 0x66, 0x93, 0x5f, 0xc3, 0x5d, 0x79, 0xd0, 0x31, 0x8b, 0x78, 0x18,
	0x50, 0xdf, 0xe6, 0xe1, 0x24, 0x72, 0x98, 0x3d, 0x0e, 0xb9, 0x40, 0x17, 0x72, 0xe8, 0xc2, 0xa7,
	0x6f, 0x77, 0x81, 0x89, 0x63, 0x6d, 0xa3, 0x83, 0x26, 0x8e, 0x43, 0x2e, 0xd4, 0xfe, 0xd5, 0xc1,
	0x15, 0x3c, 0x62, 0x40, 0x39, 0x62, 0x8e, 0x1d, 0x4d, 0x7c, 0xa6, 0x62, 0xb9, 0x8a, 0xa1, 0x2e,
	0x46, 0xcc, 0xb1, 0x26, 0x3e, 0x93, 0x71, 0xdc, 0xfc, 0x5b, 0x06, 0xb6, 0xde, 0x70, 0x3e, 0x72,
	0x1b, 0x56, 0x05, 0xed, 0xd9, 0x9e, 0x8b, 0xf9, 0x2c, 0x5b, 0x2b, 0x82, 0xf6, 0x4c, 0x97, 0x3c,
	0x80, 0x92, 0x08, 0xc7, 0x9e, 0x63, 0x73, 0x26, 0x24, 0x53, 0x26, 0xb1, 0x80, 0x59, 0x02, 0xa4,
	0x77, 0x98, 0x30, 0x5d, 0xf2, 0x08, 0xde, 0x75, 0xc2, 0xa0, 0xef, 0x0d, 0x6c, 0x69, 0x43, 0x8b,
	0xca, 0x84, 0x16, 0xac, 0x35, 0xc5, 0xe8, 0xd2, 0x9e, 0x12, 0xdd, 0x82, 0x82, 0x32, 0xe8, 0xb9,
	0x5c, 0x67, 0x33, 0x8f, 0x04, 0xd3, 0xe5, 0x9b, 0x63, 0xa8, 0x5e, 0x15, 0x00, 0xf2, 0x21, 0xac,
	0xfb, 0x94, 0x0b, 0x15, 0x52, 0x27, 0x62, 0x54, 0x30, 0xd7, 0x16, 0xde, 0x88, 0xa1, 0xc3, 0x59,
	0xeb, 0xa6, 0xe4, 0x4a, 0xe1, 0xba, 0xe2, 0x75, 0xbd, 0x11, 0x56, 0x4f, 0xdf, 0xf3, 0x05, 0x8b,
	0x54, 0x5c, 0x54, 0x09, 0x82, 0x22, 0xc9, 0xb0, 0x1c, 0xac, 0x41, 0x29, 0x62, 0xaf, 0x26, 0x8c,
	0xab, 0x2a, 0x34, 0xfe, 0x98, 0x81, 0x5b, 0x97, 0xf3, 0xc2, 0xc7, 0xe4, 0x11, 0x14, 0x74, 0xc5,
	0xf3, 0xb1, 0x2e, 0xf9, 0xd2, 0xac, 0xe4, 0xf9, 0xd8, 0xca, 0xf7, 0xf4, 0x2f, 0xf2, 0x7f, 0xb0,
	0xd2, 0x97, 0x7a, 0xd5, 0xa5, 0xdd, 0xec, 0x5e, 0xf1, 0xe9, 0xed, 0x74, 0xd2, 0xe9, 0x88, 0x49,
	0xab, 0x96, 0x92, 0x91, 0x1e, 0xfa, 0x21, 0x75, 0xed, 0xbe, 0x17, 0x78, 0x7c, 0x88, 0x41, 0xcb,
	0x5b, 0x20, 0x49, 0x87, 0x48, 0x31, 0x6a, 0x50, 0x91, 0x3a, 0x27, 0x03, 0xa7, 0x8b, 0x61, 0x0a,
	0xfa, 0xa1, 0x6c, 0xab, 0x38, 0x88, 0xe8, 0x4b, 0xc1, 0xca, 0xe9, 0x18, 0x12, 0x02, 0xcb, 0x01,
	0x1d, 0xa9, 0xa3, 0x16, 0x2c, 0xfc, 0x6d, 0xfc, 0x63, 0x19, 0xf2, 0xf1, 0xbe, 0x64, 0x03, 0x72,
	0x72, 0xe7, 0x99, 0xea, 0xaa, 0x5c, 0x9a, 0x2e, 0xf9, 0x09, 0x2c, 0xcb, 0xd8, 0xa2, 0x66, 0xf1,
	0xa9, 0xb1, 0xd0, 0x6b, 0xfc, 0x21, 0x83, 0x2c, 0xdd, 0x78, 0xfe, 0x8e, 0x85, 0x1a, 0xe4, 0x13,
	0x28, 0x4b, 0x2c, 0xc1, 0xcc, 0x78, 0x41, 0x3f, 0xc4, 0x53, 0x14, 0x9f, 0x56, 0xa4, 0x09, 0xa9,
	0x9d, 0x50, 0x28, 0x4e, 0x06, 0x4e, 0xbc, 0xdc, 0xfc, 0x53, 0x16, 0x4a, 0x49, 0x83, 0xe4, 0x13,
	0x28, 0xcc, 0x8c, 0xa8, 0x20, 0xdf, 0x49, 0xf9, 0x21, 0xa3, 0x1d, 0x4b, 0x5b, 0xf9, 0x71, 0xac,
	0xf7, 0x29, 0x00, 0xea, 0x85, 0xe7, 0x01, 0x8b, 0xf4, 0x01, 0x36, 0x53, 0x8a, 0x52, 0xa9, 0x2d,
	0xb9, 0xa8, 0x89, 0xbb, 0xe0, 0x92, 0x3c, 0x80, 0xe5, 0xb3, 0x50, 0xb0, 0x6a, 0x29, 0xed, 0xf2,
	0x69, 0x28, 0x18, 0x8a, 0x22, 0x97, 0x7c, 0x0e, 0x24, 0x62, 0xbe, 0x02, 0x96, 0x73, 0x4f, 0x0c,
	0xf1, 0xac, 0xd5, 0x65, 0xd4, 0xd9, 0x4e, 0x6d, 0x64, 0x69, 0xb1, 0x2f, 0x3c, 0x31, 0xc4, 0xd2,
	0xad, 0x44, 0x73, 0x14, 0xf2, 0x19, 0x14, 0x75, 0xf6, 0x82, 0x7e, 0xc8, 0xab, 0x2b, 0x58, 0x25,
	0xdb, 0x97, 0xe2, 0x9d, 0xcc, 0xb8, 0xee, 0x36, 0xf9, 0x93, 0x93, 0x4f, 0xa1, 0xd8, 0x0f, 0x7d,
	0x3f, 0x3c, 0x57, 0x71, 0x5a, 0x45, 0x2f, 0xaa, 0x52, 0x5f, 0xa1, 0xf8, 0xfe, 0x21, 0xa2, 0x38,
	0x7f, 0xc6, 0x04, 0xf5, 0x7c, 0x0b, 0x94, 0x30, 0x06, 0x4a, 0xa2, 0xe9, 0x44, 0x88, 0x30, 0x50,
	0xfd, 0x90, 0x53, 0xfd, 0xa0, 0x48, 0xd8, 0x0f, 0x45, 0x28, 0x60, 0x75, 0xb8, 0x54, 0x50, 0xe3,
	0xdf, 0x59, 0x28, 0x25, 0x23, 0x2e, 0x6b, 0x47, 0xe5, 0x67, 0x5a, 0x3b, 0x98, 0x02, 0xec, 0x6a,
	0x64, 0x24, 0xba, 0x0c, 0xb3, 0x83, 0x10, 0x5e, 0x85, 0x9c, 0x46, 0x74, 0x8d, 0x09, 0xf1, 0x92,
	0x7c, 0x04, 0x45, 0x2a, 0x04, 0x75, 0x86, 0x23, 0x16, 0x08, 0x05, 0x07, 0xc5, 0xa7, 0x24, 0xce,
	0x41, 0x6d, 0xca, 0xb2, 0x92, 0x62, 0xe4, 0x3e, 0x94, 0x15, 0x86, 0x09, 0xdb, 0x09, 0x27, 0x81,
	0x40, 0x7c, 0x2f, 0x5b, 0x25, 0x4d, 0xac, 0x4b, 0x1a, 0x79, 0x08, 0x6b, 0x54, 0x08, 0x4f, 0x4c,
	0x5c, 0xa6, 0xa5, 0x14, 0x28, 0x96, 0x63, 0xaa, 0x12, 0xbb, 0x37, 0x77, 0x0b, 0xa9, 0x88, 0xa4,
	0x6e, 0xa0, 0xfb, 0x50, 0xc4, 0xb3, 0x29, 0x48, 0xaf, 0xe6, 0xa7, 0xf7, 0x14, 0xd6, 0x9c, 0x02,
	0x29, 0x79, 0xc6, 0x71, 0xe4, 0x9d, 0x51, 0xe7, 0xa2, 0x5a, 0x50, 0xf7, 0x9c, 0x5e, 0xce, 0x42,
	0x23, 0xa1, 0x0a, 0x10, 0xaa, 0x54, 0x68, 0x24, 0x3e, 0x6d, 0xeb, 0xc2, 0x15, 0x9e, 0xf0, 0x59,
	0xb5, 0x88, 0xd1, 0x29, 0x28, 0xae, 0xf0, 0x11, 0xbe, 0xf8, 0x90, 0x46, 0xf1, 0x09, 0x4a, 0x2a,
	0x5d, 0x48, 0x42, 0xf7, 0x8d, 0x16, 0xc0, 0xf1, 0xcc, 0x89, 0x2d, 0xd8, 0x38, 0x6e, 0x77, 0xba,
	0x76, 0xa7, 0x7d, 0x62, 0xd5, 0x1b, 0xf6, 0x49, 0xab, 0x73, 0xdc, 0xa8, 0x9b, 0x87, 0x66, 0xe3,
	0x59, 0xe5, 0x1d, 0x72, 0x1f, 0x76, 0x92, 0xcc, 0xa3, 0xda, 0x8b, 0x86, 0x6d, 0x35, 0xea, 0xed,
	0x17, 0x2f, 0x1a, 0xad, 0x67, 0xb5, 0xae, 0xd9, 0x6e, 0x55, 0x32, 0xc6, 0xef, 0x33, 0x50, 0x99,
	0xaf, 0x60, 0xb2, 0x0f, 0x37, 0x87, 0x12, 0xa1, 0xb0, 0x8c, 0x98, 0x8b, 0xb5, 0xcf, 0x22, 0xac,
	0x80, 0xbc, 0xf5, 0xee, 0x90, 0xba, 0x87, 0x9a, 0x73, 0x8c, 0x0c, 0x19, 0x53, 0x29, 0x1f, 0x07,
	0x1a, 0x63, 0x9a, 0xb7, 0x8a, 0x43, 0xea, 0xd6, 0x34, 0x29, 0x16, 0xe9, 0xd3, 0xb3, 0x70, 0x12,
	0x31, 0xb7, 0x5a, 0x99, 0x8a, 0x1c, 0x6a, 0x92, 0xf1, 0x97, 0x2c, 0x94, 0x53, 0x5d, 0x4b, 0x2a,
	0x90, 0x9d, 0x4c, 0xef, 0x27, 0xf9, 0x53, 0x46, 0x9d, 0x3a, 0x2a, 0x36, 0x0a, 0xef, 0xe2, 0x25,
	0xd9, 0x84, 0x7c, 0xe0, 0x39, 0x2f, 0x11, 0x0a, 0x55, 0xd1, 0x4d, 0xd7, 0x64, 0x1d, 0x56, 0x07,
	0x2c, 0x70, 0x59, 0x84, 0x0d, 0x5c, 0xb6, 0xf4, 0x8a, 0xbc, 0x80, 0x35, 0x6c, 0x40, 0x87, 0x46,
	0xae, 0x6a, 0x2d, 0x35, 0x38, 0x7c, 0x70, 0x35, 0x92, 0x60, 0xa3, 0xd6, 0x69, 0xe4, 0x62, 0x93,
	0x96, 0x06, 0x89, 0x95, 0xac, 0x9b, 0x09, 0x67, 0x91, 0xcd, 0x05, 0x15, 0x13, 0x5e, 0x5d, 0xdd,
	0xcd, 0xc6, 0x75, 0x23, 0xc9, 0x1d, 0xa4, 0x92, 0x3d, 0x90, 0xa3, 0x98, 0xed, 0x0c, 0x69, 0x10,
	0x30, 0x5f, 0x36, 0x56, 0x6e, 0x5a, 0x5f, 0xa5, 0xc9, 0xc0, 0xa9, 0x2b, 0x86, 0xe9, 0x92, 0xff,
	0x07, 0x82, 0xe6, 0x54, 0x8f, 0xc7, 0x56, 0xb1, 0x1a, 0xad, 0x8a, 0xe4, 0xb4, 0x91, 0xa1, 0xec,
	0x6e, 0x9e, 0x2a, 0x64, 0x9d, 0x3a, 0xf3, 0x20, 0x79, 0x36, 0xc1, 0x5e, 0x0b, 0xdd, 0xc0, 0x53,
	0x97, 0xbb, 0xec, 0xb5, 0x20, 0xbb, 0x50, 0x4a, 0x44, 0xc0, 0x8d, 0xef, 0xcb, 0xe9, 0xb1, 0x5c,
	0xe3, 0x9b, 0x55, 0xb8, 0x11, 0x43, 0x76, 0x7c, 0x33, 0x7f, 0xd7, 0x61, 0xf0, 0x16, 0xac, 0xa8,
	0x3a, 0x57, 0xb9, 0x52, 0x8b, 0x37, 0xa0, 0x43, 0x0a, 0x54, 0x96, 0xe7, 0x40, 0x65, 0x1d, 0x56,
	0xc3, 0xc8, 0x1b, 0x78, 0x81, 0xee, 0x7e, 0xbd, 0x92, 0x2d, 0x83, 0x47, 0xd0, 0xcc, 0xd5, 0xd9,
	0x09, 0xda, 0x4a, 0xe0, 0x23, 0x58, 0x9f, 0x81, 0x89, 0xed, 0x8d, 0xe8, 0x20, 0x6e, 0x2f, 0xd5,
	0xfb, 0xb7, 0x66, 0x5c, 0x53, 0x32, 0x15, 0x4e, 0xa4, 0xb5, 0xce, 0x3c, 0x97, 0x85, 0x5a, 0x2b,
	0x3f, 0xaf, 0x75, 0x2a, 0x99, 0x8b, 0xb4, 0xe8, 0xc4, 0xf5, 0x62, 0xad, 0xc2, 0xbc, 0x56, 0x4d,
	0x32, 0x95, 0x96, 0x09, 0xeb, 0xe3, 0x88, 0xb9, 0xac, 0xef, 0x05, 0x0c, 0xdb, 0x68, 0x0a, 0x90,
	0x70, 0x25, 0x40, 0xde, 0x9e, 0x69, 0xd4, 0x12, 0x50, 0x99, 0x80, 0xa5, 0x62, 0x1a, 0x96, 0xee,
	0xc3, 0x0a, 0x7b, 0x2d, 0x22, 0xaa, 0x2f, 0xbe, 0x72, 0x6c, 0xb3, 0x21, 0x89, 0x96, 0xe2, 0x4d,
	0x2f, 0xc7, 0xf2, 0x1b, 0x2f, 0xc7, 0xf7, 0x25, 0x1e, 0x4f, 0xa7, 0x3f, 0xcf, 0xad, 0xae, 0xa5,
	0x46, 0x79, 0x35, 0xfd, 0x99, 0x6e, 0x62, 0xc4, 0xbc, 0x91, 0x1c, 0x31, 0xef, 0x01, 0xa8, 0x64,
	0xd9, 0x01, 0x3b, 0x47, 0x24, 0x50, 0xba, 0x05, 0x45, 0x6d, 0xb1, 0x73, 0x84, 0x60, 0xe5, 0x37,
	0xca, 0xbc, 0x9b, 0x80, 0x60, 0x45, 0x96, 0x42, 0xe9, 0x21, 0xd4, 0x73, 0x51, 0x94, 0xcc, 0x0d,
	0xa1, 0xa6, 0xab, 0xec, 0x95, 0xcf, 0x3c, 0xee, 0xf5, 0x7c, 0x66, 0x73, 0x27, 0x1c, 0xb3, 0xea,
	0x4d, 0x75, 0x83, 0x68, 0x62, 0x47, 0xd2, 0x52, 0x43, 0xd6, 0xad, 0xd4, 0x90, 0x65, 0x7c, 0xbb,
	0xa4, 0x86, 0xb2, 0x59, 0x17, 0x5c, 0x6f, 0x42, 0x4c, 0xdc, 0xa3, 0x4b, 0xa9, 0x7b, 0x74, 0x07,
	0x8a, 0xaa, 0x22, 0x45, 0xf8, 0x92, 0x05, 0xba, 0x21, 0x00, 0x49, 0x5d, 0x49, 0x91, 0x02, 0xaa,
	0xf8, 0x94, 0xc0, 0xb2, 0x12, 0x40, 0x92, 0x12, 0xd8, 0x06, 0x25, 0x6e, 0xbf, 0x64, 0x17, 0x6a,
	0xb6, 0x28, 0x58, 0x05, 0xa4, 0x7c, 0xce, 0x2e, 0xb8, 0x64, 0x2b, 0x7d, 0x64, 0xaf, 0x2a, 0x36,
	0x52, 0x90, 0xbd, 0x03, 0x45, 0x55, 0xa5, 0xca, 0x7c, 0x4e, 0x99, 0x47, 0xd2, 0xd4, 0xbc, 0x12,
	0x40, 0xfd, 0xbc, 0xd2, 0x47, 0x8a, 0xd4, 0x37, 0x3c, 0x28, 0x75, 0x26, 0xbd, 0x2e, 0xed, 0xd5,
	0x31, 0xe0, 0xe4, 0x2e, 0x00, 0x9f, 0xf4, 0xec, 0xd4, 0xcb, 0x22, 0xcf, 0x51, 0xc2, 0x74, 0x25,
	0xdc, 0xc4, 0xdc, 0xc4, 0xcc, 0x0a, 0x8a, 0xdf, 0x92, 0x50, 0x2d, 0x73, 0x40, 0x7b, 0x89, 0x07,
	0xa2, 0x95, 0x13, 0xb4, 0x27, 0x01, 0xc0, 0xf8, 0x1a, 0xee, 0x1c, 0x31, 0x91, 0xdc, 0xed, 0xe0,
	0x02, 0xcd, 0x5e, 0x07, 0x92, 0x66, 0x25, 0xb9, 0x94, 0x28, 0x49, 0xe3, 0x37, 0xb0, 0x79, 0x95,
	0xed, 0xeb, 0x25, 0xfa, 0x43, 0x04, 0xb7, 0xbe, 0x37, 0x88, 0x1f, 0x03, 0xe9, 0x71, 0x36, 0xb9,
	0x83, 0x15, 0x4b, 0x1a, 0x7f, 0xce, 0x42, 0xa1, 0x1e, 0x17, 0xac, 0x7c, 0xdc, 0xa5, 0xbb, 0x4b,
	0x01, 0x77, 0xaa, 0xb3, 0xde, 0x87, 0x1b, 0x09, 0x99, 0x44, 0x2c, 0xcb, 0x53, 0xa9, 0xb7, 0x84,
	0x93, 0xfc, 0x08, 0x0a, 0xf1, 0x1b, 0x32, 0x1e, 0xc4, 0x6e, 0xcd, 0x0d, 0xb6, 0xf8, 0x98, 0xb4,
	0xf2, 0xfa, 0x55, 0xc9, 0xe5, 0x9d, 0xd2, 0x0f, 0xe5, 0x2b, 0x37, 0x56, 0x8c, 0x07, 0x31, 0xa4,
	0x6a, 0x79, 0xf2, 0x08, 0x2a, 0xae, 0xc7, 0xc7, 0x3e, 0xbd, 0x90, 0x4f, 0x72, 0x2a, 0xd8, 0xe0,
	0x42, 0xa3, 0xf2, 0x0d, 0x4d, 0xef, 0x68, 0x32, 0xf9, 0x0c, 0xe4, 0x95, 0x67, 0xb3, 0xd7, 0xfa,
	0x05, 0x90, 0x5b, 0x30, 0xc8, 0xcb, 0xa9, 0x98, 0xf6, 0x1a, 0xaf, 0xe3, 0x07, 0x05, 0x4c, 0x06,
	0x8e, 0x5e, 0x91, 0x16, 0x10, 0x94, 0x1b, 0x52, 0xdf, 0x9f, 0x59, 0xc9, 0xa3, 0x95, 0x9d, 0x4b,
	0xf3, 0xf5, 0x73, 0xea, 0xfb, 0x29, 0x53, 0x37, 0x06, 0x9a, 0xaa, 0x49, 0x07, 0x00, 0xf9, 0xd8,
	0x8a, 0x71, 0x02, 0xe5, 0xd4, 0xd6, 0xe4, 0x19, 0xac, 0xc5, 0xe7, 0x42, 0x58, 0xe0, 0xd5, 0xcc,
	0x77, 0x19, 0xe4, 0xcb, 0x5a, 0x09, 0x29, 0xdc, 0xf8, 0x0c, 0xc8, 0x65, 0x5f, 0xc8, 0x1e, 0x54,
	0xe2, 0x89, 0xc0, 0xf7, 0xd4, 0x9b, 0x57, 0x37, 0xcf, 0x9a, 0xa6, 0x37, 0x3d, 0x44, 0x1e, 0xe3,
	0xa7, 0x90, 0x8b, 0x03, 0xbd, 0x95, 0xcc, 0xa0, 0x6e, 0xb5, 0x69, 0xae, 0x16, 0x3d, 0x0b, 0xff,
	0x90, 0x81, 0xf5, 0x23, 0x26, 0xa6, 0xa5, 0xf6, 0xbd, 0xf5, 0x0f, 0xf9, 0x18, 0x36, 0x62, 0x7c,
	0x4d, 0xd4, 0xa5, 0x2e, 0xbb, 0xac, 0xbc, 0xf8, 0x34, 0x7b, 0xba, 0x37, 0xb6, 0xf4, 0x19, 0x6c,
	0x2c, 0xf4, 0xe7, 0x7a, 0x3d, 0xf7, 0xc3, 0xf9, 0x9e, 0x5b, 0x4f, 0x65, 0x64, 0x6a, 0x7e, 0xd6,
	0x70, 0x3d, 0x7c, 0xf3, 0xcf, 0x7c, 0x91, 0x73, 0xc9, 0x75, 0xa2, 0x70, 0xa9, 0x45, 0x97, 0x2e,
	0xb5, 0xa8, 0xf1, 0x25, 0xdc, 0x5e, 0xb0, 0xc7, 0xf5, 0x4e, 0x96, 0x18, 0xa0, 0xb2, 0xd3, 0x01,
	0xca, 0xf8, 0x2d, 0x54, 0xeb, 0x43, 0xe6, 0xbc, 0x3c, 0xe1, 0x2c, 0x32, 0xf9, 0x81, 0x2c, 0x0f,
	0xf7, 0xba, 0xa3, 0xd9, 0x15, 0x79, 0x34, 0xa0, 0xdc, 0xa3, 0x81, 0x3d, 0x9b, 0xc2, 0x14, 0x68,
	0x14, 0x7b, 0x34, 0x38, 0xd6, 0x83, 0x98, 0x7c, 0x32, 0xdc, 0xb9, 0x62, 0xff, 0xeb, 0x9d, 0x6e,
	0x0b, 0x0a, 0x1e, 0xb7, 0x7b, 0xa8, 0x8f, 0x6e, 0xe4, 0xad, 0xbc, 0xa7, 0xed, 0xc9, 0x1b, 0x5b,
	0x71, 0xec, 0x88, 0x51, 0x1e, 0xc6, 0x57, 0x63, 0x49, 0x11, 0x2d, 0xa4, 0x19, 0xdf, 0x66, 0xe0,
	0x3d, 0x0c, 0xf2, 0x68, 0x1c, 0xb1, 0x21, 0x0b, 0xb8, 0x77, 0xc6, 0xe2, 0x01, 0x5a, 0xf6, 0xde,
	0xff, 0xac, 0xb0, 0xff, 0x99, 0x81, 0x9d, 0x37, 0x3a, 0x76, 0xbd, 0x48, 0xfd, 0x0c, 0xca, 0xd4,
	0x11, 0xde, 0x99, 0x27, 0x2e, 0xe4, 0xfe, 0x6f, 0xab, 0xf3, 0x52, 0x2c, 0xdc, 0xa5, 0x3d, 0x4e,
	0x7e, 0x0c, 0xc5, 0x99, 0xeb, 0x1c, 0xdd, 0xbe, 0x5a, 0x15, 0xa6, 0x25, 0xcc, 0x8d, 0x2f, 0xa7,
	0x9f, 0x82, 0x0f, 0x19, 0x73, 0x0f, 0x2e, 0xcc, 0x6b, 0x7f, 0x0a, 0xd6, 0x33, 0x0f, 0xd7, 0xf5,
	0x9b, 0x53, 0x43, 0x0f, 0x37, 0x82, 0xe9, 0x37, 0xb7, 0x84, 0xe5, 0xff, 0xde, 0x37, 0x37, 0xc3,
	0x84, 0x9d, 0x16, 0x63, 0x6e, 0x67, 0x18, 0x9e, 0xa7, 0xbf, 0x35, 0x1e, 0xe2, 0x87, 0xc1, 0x6b,
	0x9c, 0xca, 0xe8, 0xc3, 0xee, 0x9b, 0x4d, 0x5d, 0x7b, 0x30, 0xf4, 0xb8, 0xcd, 0x87, 0xe1, 0xb9,
	0xee, 0x80, 0x55, 0x8f, 0x4b, 0xdb, 0xc6, 0xef, 0xe0, 0xb6, 0xc9, 0xa7, 0x79, 0x39, 0x55, 0x45,
	0xf6, 0x3d, 0x14, 0x74, 0x7a, 0x72, 0x48, 0xf4, 0xf8, 0x6c, 0x72, 0xc0, 0x0a, 0xee, 0xc1, 0xfa,
	0xa2, 0xfd, 0xaf, 0x77, 0x3a, 0x39, 0x9b, 0x72, 0x5b, 0x77, 0x88, 0x3e, 0x60, 0xc1, 0xe3, 0xda,
	0xda, 0x63, 0x1f, 0xd6, 0xe3, 0x18, 0x1e, 0xd3, 0x81, 0x8e, 0x20, 0x0e, 0x27, 0x8f, 0xe0, 0xe1,
	0x71, 0xc3, 0xea, 0xb4, 0x5b, 0xb5, 0xa6, 0x7d, 0x5c, 0x3b, 0x6a, 0xd8, 0x87, 0x66, 0xb3, 0xdb,
	0xb0, 0xec, 0xee, 0x57, 0xc7, 0xf3, 0x9f, 0x39, 0x3e, 0x80, 0xfb, 0x57, 0x8b, 0xe2, 0x47, 0x8f,
	0xaf, 0xdb, 0xad, 0x46, 0x25, 0xf3, 0xf8, 0xef, 0x19, 0x75, 0xf5, 0x4a, 0xa8, 0x3a, 0x98, 0x7e,
	0x00, 0x23, 0x0f, 0xe1, 0x1e, 0x4a, 0xe1, 0xb7, 0x92, 0x83, 0x93, 0x6e, 0xb7, 0xdd, 0x5a, 0xb4,
	0xcd, 0x03, 0xd8, 0x5d, 0x2c, 0xd6, 0x68, 0xc9, 0xdd, 0xac, 0x76, 0xfb, 0x45, 0x25, 0x43, 0x76,
	0xe1, 0xee, 0x62, 0xa9, 0xc3, 0x76, 0xb3, 0xd9, 0xfe, 0xa2, 0xb2, 0x44, 0xf6, 0xe0, 0xc1, 0x62,
	0x89, 0x5a, 0xd3, 0x6a, 0xd4, 0x9e, 0x7d, 0x15, 0x4b, 0x66, 0x1f, 0xff, 0x0a, 0xe0, 0x64, 0xf6,
	0xdd, 0x60, 0x0b, 0x36, 0x4e, 0x3a, 0x0d, 0xcb, 0xee, 0x74, 0x6b, 0xdd, 0x93, 0xce, 0x9c, 0x73,
	0x9b, 0xb0, 0x9e, 0x64, 0x1e, 0x9f, 0x1c, 0x34, 0xcd, 0xce, 0x73, 0xb3, 0x75, 0x54, 0xc9, 0x90,
	0x75, 0x20, 0x49, 0x5e, 0xbb, 0xd5, 0x34, 0x5b, 0x8d, 0xca, 0xd2, 0xe3, 0x5f, 0x40, 0xe9, 0x34,
	0xf9, 0xfa, 0xd9, 0x86, 0x3b, 0xa7, 0x66, 0xc7, 0x3c, 0x68, 0x36, 0xec, 0x4e, 0xbd, 0x7d, 0xe9,
	0xfc, 0x5b, 0xb0, 0x91, 0x66, 0x27, 0x43, 0xfb, 0x4b, 0x58, 0x8b, 0x23, 0xab, 0x1f, 0xdd, 0xa9,
	0x40, 0xb4, 0x2d, 0xf3, 0xc8, 0x6c, 0xcd, 0x19, 0xdc, 0x86, 0x3b, 0x97, 0x24, 0x90, 0xd0, 0xad,
	0x1d, 0x54, 0x32, 0x8f, 0xff, 0xba, 0x04, 0xe5, 0x14, 0xa6, 0x92, 0x1d, 0xd8, 0xaa, 0xb7, 0x5b,
	0x87, 0xe6, 0x91, 0x94, 0x58, 0x94, 0xa2, 0x5d, 0xb8, 0x3b, 0x2f, 0x50, 0x7f, 0x5e, 0x6b, 0xb5,
	0x1a, 0x4d, 0xbb, 0x69, 0x76, 0xba, 0x95, 0x0c, 0xb9, 0x0b, 0xd5, 0x79, 0x09, 0xdc, 0x5e, 0x6e,
	0xb9, 0x44, 0xee, 0xc1, 0xf6, 0x3c, 0xb7, 0x56, 0xef, 0x9a, 0xa7, 0x66, 0xf7, 0x2b, 0x14, 0xab,
	0x64, 0x17, 0x6d, 0x31, 0x15, 0xe9, 0x34, 0xba, 0x95, 0xe5, 0x45, 0x46, 0xd4, 0x31, 0x6b, 0x4d,
	0xbb, 0x5e, 0xb3, 0x9e, 0x55, 0x56, 0xc8, 0x7b, 0xb0, 0x79, 0x49, 0xc4, 0x6a, 0x9f, 0x1c, 0x4b,
	0x6f, 0xbb, 0x95, 0x55, 0x19, 0x99, 0x85, 0x26, 0x9e, 0xd7, 0x9a, 0xcd, 0x4a, 0xee, 0xa0, 0x06,
	0x55, 0x27, 0x1c, 0xed, 0x5f, 0x78, 0x17, 0xe1, 0x44, 0xf6, 0xdd, 0x28, 0x74, 0x99, 0xaf, 0xfe,
	0xbe, 0xfb, 0xfa, 0xe1, 0x20, 0xf4, 0x69, 0x30, 0xd8, 0xff, 0xf8, 0xa9, 0x10, 0xfb, 0x4e, 0x38,
	0x7a, 0x82, 0x64, 0x27, 0xf4, 0x9f, 0xd0, 0xf1, 0x18, 0xff, 0x2f, 0xfc, 0xc1, 0x64, 0xe0, 0xf4,
	0x56, 0x91, 0xfc, 0xe1, 0x7f, 0x02, 0x00, 0x00, 0xff, 0xff, 0xcf, 0x6d, 0x93, 0x7d, 0x4b, 0x1c,
	0x00, 0x00,
}
