// Code generated by protoc-gen-go. DO NOT EDIT.
// source: chat_bot_logic/chat_bot_logic.proto

package chat_bot_logic // import "golang.52tt.com/protocol/app/chat-bot-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import web_im_logic "golang.52tt.com/protocol/app/web-im-logic"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AIPartnerEntranceType int32

const (
	// 无入口
	AIPartnerEntranceType_AI_PARTNER_ENTRANCE_TYPE_UNSPECIFIED AIPartnerEntranceType = 0
	// 树洞入口
	AIPartnerEntranceType_AI_PARTNER_ENTRANCE_TYPE_SOULMATE AIPartnerEntranceType = 1
	// 多角色入口
	AIPartnerEntranceType_AI_PARTNER_ENTRANCE_TYPE_MULTI_ROLE AIPartnerEntranceType = 2
	// 桌宠入口
	AIPartnerEntranceType_AI_PARTNER_ENTRANCE_TYPE_PET AIPartnerEntranceType = 3
	// 群组入口
	AIPartnerEntranceType_AI_PARTNER_ENTRANCE_TYPE_GROUP AIPartnerEntranceType = 4
)

var AIPartnerEntranceType_name = map[int32]string{
	0: "AI_PARTNER_ENTRANCE_TYPE_UNSPECIFIED",
	1: "AI_PARTNER_ENTRANCE_TYPE_SOULMATE",
	2: "AI_PARTNER_ENTRANCE_TYPE_MULTI_ROLE",
	3: "AI_PARTNER_ENTRANCE_TYPE_PET",
	4: "AI_PARTNER_ENTRANCE_TYPE_GROUP",
}
var AIPartnerEntranceType_value = map[string]int32{
	"AI_PARTNER_ENTRANCE_TYPE_UNSPECIFIED": 0,
	"AI_PARTNER_ENTRANCE_TYPE_SOULMATE":    1,
	"AI_PARTNER_ENTRANCE_TYPE_MULTI_ROLE":  2,
	"AI_PARTNER_ENTRANCE_TYPE_PET":         3,
	"AI_PARTNER_ENTRANCE_TYPE_GROUP":       4,
}

func (x AIPartnerEntranceType) String() string {
	return proto.EnumName(AIPartnerEntranceType_name, int32(x))
}
func (AIPartnerEntranceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{0}
}

type AIPartnerEntranceSource int32

const (
	AIPartnerEntranceSource_AI_PARTNER_ENTRANCE_SOURCE_UNSPECIFIED AIPartnerEntranceSource = 0
	// IM页面
	AIPartnerEntranceSource_AI_PARTNER_ENTRANCE_SOURCE_IM_TAB AIPartnerEntranceSource = 1
	// 登录
	AIPartnerEntranceSource_AI_PARTNER_ENTRANCE_SOURCE_LOGIN AIPartnerEntranceSource = 2
)

var AIPartnerEntranceSource_name = map[int32]string{
	0: "AI_PARTNER_ENTRANCE_SOURCE_UNSPECIFIED",
	1: "AI_PARTNER_ENTRANCE_SOURCE_IM_TAB",
	2: "AI_PARTNER_ENTRANCE_SOURCE_LOGIN",
}
var AIPartnerEntranceSource_value = map[string]int32{
	"AI_PARTNER_ENTRANCE_SOURCE_UNSPECIFIED": 0,
	"AI_PARTNER_ENTRANCE_SOURCE_IM_TAB":      1,
	"AI_PARTNER_ENTRANCE_SOURCE_LOGIN":       2,
}

func (x AIPartnerEntranceSource) String() string {
	return proto.EnumName(AIPartnerEntranceSource_name, int32(x))
}
func (AIPartnerEntranceSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{1}
}

// AI形象类型
type AIRoleType int32

const (
	// 树洞形象
	AIRoleType_AI_ROLE_TYPE_PARTNER_UNSPECIFIED AIRoleType = 0
	// 游戏形象/角色扮演
	AIRoleType_AI_ROLE_TYPE_GAME AIRoleType = 1
	// 桌宠
	AIRoleType_AI_ROLE_TYPE_PET AIRoleType = 2
)

var AIRoleType_name = map[int32]string{
	0: "AI_ROLE_TYPE_PARTNER_UNSPECIFIED",
	1: "AI_ROLE_TYPE_GAME",
	2: "AI_ROLE_TYPE_PET",
}
var AIRoleType_value = map[string]int32{
	"AI_ROLE_TYPE_PARTNER_UNSPECIFIED": 0,
	"AI_ROLE_TYPE_GAME":                1,
	"AI_ROLE_TYPE_PET":                 2,
}

func (x AIRoleType) String() string {
	return proto.EnumName(AIRoleType_name, int32(x))
}
func (AIRoleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{2}
}

type AIRoleState int32

const (
	AIRoleState_AI_ROLE_STATE_UNSPECIFIED AIRoleState = 0
	// 公开
	AIRoleState_AI_ROLE_STATE_PUBLIC AIRoleState = 1
	// 私有
	AIRoleState_AI_ROLE_STATE_PRIVATE AIRoleState = 2
)

var AIRoleState_name = map[int32]string{
	0: "AI_ROLE_STATE_UNSPECIFIED",
	1: "AI_ROLE_STATE_PUBLIC",
	2: "AI_ROLE_STATE_PRIVATE",
}
var AIRoleState_value = map[string]int32{
	"AI_ROLE_STATE_UNSPECIFIED": 0,
	"AI_ROLE_STATE_PUBLIC":      1,
	"AI_ROLE_STATE_PRIVATE":     2,
}

func (x AIRoleState) String() string {
	return proto.EnumName(AIRoleState_name, int32(x))
}
func (AIRoleState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{3}
}

type AIPartner_Relationship int32

const (
	AIPartner_RELATIONSHIP_UNSPECIFIED AIPartner_Relationship = 0
	// 朋友
	AIPartner_RELATIONSHIP_FRIEND AIPartner_Relationship = 1
	// 恋人
	AIPartner_RELATIONSHIP_LOVER AIPartner_Relationship = 2
)

var AIPartner_Relationship_name = map[int32]string{
	0: "RELATIONSHIP_UNSPECIFIED",
	1: "RELATIONSHIP_FRIEND",
	2: "RELATIONSHIP_LOVER",
}
var AIPartner_Relationship_value = map[string]int32{
	"RELATIONSHIP_UNSPECIFIED": 0,
	"RELATIONSHIP_FRIEND":      1,
	"RELATIONSHIP_LOVER":       2,
}

func (x AIPartner_Relationship) String() string {
	return proto.EnumName(AIPartner_Relationship_name, int32(x))
}
func (AIPartner_Relationship) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{2, 0}
}

type BatchGetAIAccountRequest_RequestSource int32

const (
	BatchGetAIAccountRequest_REQUEST_SOURCE_UNSPECIFIED BatchGetAIAccountRequest_RequestSource = 0
	// im tab
	BatchGetAIAccountRequest_REQUEST_SOURCE_IM_TAB BatchGetAIAccountRequest_RequestSource = 1
	// 房间个人资料卡
	BatchGetAIAccountRequest_REQUEST_SOURCE_CHANNEL_PERSONAL_PAGE BatchGetAIAccountRequest_RequestSource = 2
)

var BatchGetAIAccountRequest_RequestSource_name = map[int32]string{
	0: "REQUEST_SOURCE_UNSPECIFIED",
	1: "REQUEST_SOURCE_IM_TAB",
	2: "REQUEST_SOURCE_CHANNEL_PERSONAL_PAGE",
}
var BatchGetAIAccountRequest_RequestSource_value = map[string]int32{
	"REQUEST_SOURCE_UNSPECIFIED":           0,
	"REQUEST_SOURCE_IM_TAB":                1,
	"REQUEST_SOURCE_CHANNEL_PERSONAL_PAGE": 2,
}

func (x BatchGetAIAccountRequest_RequestSource) String() string {
	return proto.EnumName(BatchGetAIAccountRequest_RequestSource_name, int32(x))
}
func (BatchGetAIAccountRequest_RequestSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{17, 0}
}

// AI形象卡信息
type AIRole struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// AI头像
	Avatar string `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// AI风格
	Style string `protobuf:"bytes,3,opt,name=style,proto3" json:"style,omitempty"`
	// AI性别 0:女 1:男 2:其他
	Sex int32 `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	// 背景图
	Image string `protobuf:"bytes,5,opt,name=image,proto3" json:"image,omitempty"`
	// 形象类型 see enum AIRoleType
	Type uint32 `protobuf:"varint,6,opt,name=type,proto3" json:"type,omitempty"`
	// 名称
	Name string `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
	// 状态 see enum AIRoleState
	State uint32 `protobuf:"varint,8,opt,name=state,proto3" json:"state,omitempty"`
	// 创建角色的用户uid
	Uid uint32 `protobuf:"varint,9,opt,name=uid,proto3" json:"uid,omitempty"`
	// 透传中台扩展信息
	Ext []byte `protobuf:"bytes,10,opt,name=ext,proto3" json:"ext,omitempty"`
	// 入口标签
	EntranceTag          string   `protobuf:"bytes,11,opt,name=entrance_tag,json=entranceTag,proto3" json:"entrance_tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIRole) Reset()         { *m = AIRole{} }
func (m *AIRole) String() string { return proto.CompactTextString(m) }
func (*AIRole) ProtoMessage()    {}
func (*AIRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{0}
}
func (m *AIRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRole.Unmarshal(m, b)
}
func (m *AIRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRole.Marshal(b, m, deterministic)
}
func (dst *AIRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRole.Merge(dst, src)
}
func (m *AIRole) XXX_Size() int {
	return xxx_messageInfo_AIRole.Size(m)
}
func (m *AIRole) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRole.DiscardUnknown(m)
}

var xxx_messageInfo_AIRole proto.InternalMessageInfo

func (m *AIRole) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIRole) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *AIRole) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

func (m *AIRole) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *AIRole) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *AIRole) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AIRole) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AIRole) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *AIRole) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AIRole) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *AIRole) GetEntranceTag() string {
	if m != nil {
		return m.EntranceTag
	}
	return ""
}

// AI关系
type AIRelationship struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIRelationship) Reset()         { *m = AIRelationship{} }
func (m *AIRelationship) String() string { return proto.CompactTextString(m) }
func (*AIRelationship) ProtoMessage()    {}
func (*AIRelationship) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{1}
}
func (m *AIRelationship) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRelationship.Unmarshal(m, b)
}
func (m *AIRelationship) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRelationship.Marshal(b, m, deterministic)
}
func (dst *AIRelationship) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRelationship.Merge(dst, src)
}
func (m *AIRelationship) XXX_Size() int {
	return xxx_messageInfo_AIRelationship.Size(m)
}
func (m *AIRelationship) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRelationship.DiscardUnknown(m)
}

var xxx_messageInfo_AIRelationship proto.InternalMessageInfo

func (m *AIRelationship) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIRelationship) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// AI伴侣信息
type AIPartner struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ta的名字
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 你希望ta怎么称呼你
	CallName string `protobuf:"bytes,3,opt,name=call_name,json=callName,proto3" json:"call_name,omitempty"`
	// AI伴侣形象卡
	Role *AIRole `protobuf:"bytes,4,opt,name=role,proto3" json:"role,omitempty"`
	// 你们的关系(已废弃, 改成relation, 从中台获取)
	Relationship AIPartner_Relationship `protobuf:"varint,5,opt,name=relationship,proto3,enum=ga.chat_bot_logic.AIPartner_Relationship" json:"relationship,omitempty"`
	// [不再接收ta的消息]开关 true:打开 false:关闭
	Silent bool `protobuf:"varint,6,opt,name=silent,proto3" json:"silent,omitempty"`
	// 用户与AI伴侣关系
	Relation *AIRelationship `protobuf:"bytes,7,opt,name=relation,proto3" json:"relation,omitempty"`
	// 是否展示hint内容
	ShowHint bool `protobuf:"varint,8,opt,name=show_hint,json=showHint,proto3" json:"show_hint,omitempty"`
	// 描述文本
	Hint                 string   `protobuf:"bytes,9,opt,name=hint,proto3" json:"hint,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIPartner) Reset()         { *m = AIPartner{} }
func (m *AIPartner) String() string { return proto.CompactTextString(m) }
func (*AIPartner) ProtoMessage()    {}
func (*AIPartner) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{2}
}
func (m *AIPartner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIPartner.Unmarshal(m, b)
}
func (m *AIPartner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIPartner.Marshal(b, m, deterministic)
}
func (dst *AIPartner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIPartner.Merge(dst, src)
}
func (m *AIPartner) XXX_Size() int {
	return xxx_messageInfo_AIPartner.Size(m)
}
func (m *AIPartner) XXX_DiscardUnknown() {
	xxx_messageInfo_AIPartner.DiscardUnknown(m)
}

var xxx_messageInfo_AIPartner proto.InternalMessageInfo

func (m *AIPartner) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIPartner) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AIPartner) GetCallName() string {
	if m != nil {
		return m.CallName
	}
	return ""
}

func (m *AIPartner) GetRole() *AIRole {
	if m != nil {
		return m.Role
	}
	return nil
}

func (m *AIPartner) GetRelationship() AIPartner_Relationship {
	if m != nil {
		return m.Relationship
	}
	return AIPartner_RELATIONSHIP_UNSPECIFIED
}

func (m *AIPartner) GetSilent() bool {
	if m != nil {
		return m.Silent
	}
	return false
}

func (m *AIPartner) GetRelation() *AIRelationship {
	if m != nil {
		return m.Relation
	}
	return nil
}

func (m *AIPartner) GetShowHint() bool {
	if m != nil {
		return m.ShowHint
	}
	return false
}

func (m *AIPartner) GetHint() string {
	if m != nil {
		return m.Hint
	}
	return ""
}

// AI伴侣统一推送
type AIPartnerPushMsg struct {
	// 接收推送的用户
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// AI伴侣ID
	PartnerId uint32 `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	// 推送类型(WEB与中台对接)
	Type uint32 `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	// 推送内容
	Data                 []byte   `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIPartnerPushMsg) Reset()         { *m = AIPartnerPushMsg{} }
func (m *AIPartnerPushMsg) String() string { return proto.CompactTextString(m) }
func (*AIPartnerPushMsg) ProtoMessage()    {}
func (*AIPartnerPushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{3}
}
func (m *AIPartnerPushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIPartnerPushMsg.Unmarshal(m, b)
}
func (m *AIPartnerPushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIPartnerPushMsg.Marshal(b, m, deterministic)
}
func (dst *AIPartnerPushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIPartnerPushMsg.Merge(dst, src)
}
func (m *AIPartnerPushMsg) XXX_Size() int {
	return xxx_messageInfo_AIPartnerPushMsg.Size(m)
}
func (m *AIPartnerPushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_AIPartnerPushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_AIPartnerPushMsg proto.InternalMessageInfo

func (m *AIPartnerPushMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AIPartnerPushMsg) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *AIPartnerPushMsg) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AIPartnerPushMsg) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type AIAccount struct {
	Account string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	// 标识
	Identity string `protobuf:"bytes,6,opt,name=identity,proto3" json:"identity,omitempty"`
	// 说明
	Desc                 string   `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIAccount) Reset()         { *m = AIAccount{} }
func (m *AIAccount) String() string { return proto.CompactTextString(m) }
func (*AIAccount) ProtoMessage()    {}
func (*AIAccount) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{4}
}
func (m *AIAccount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIAccount.Unmarshal(m, b)
}
func (m *AIAccount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIAccount.Marshal(b, m, deterministic)
}
func (dst *AIAccount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIAccount.Merge(dst, src)
}
func (m *AIAccount) XXX_Size() int {
	return xxx_messageInfo_AIAccount.Size(m)
}
func (m *AIAccount) XXX_DiscardUnknown() {
	xxx_messageInfo_AIAccount.DiscardUnknown(m)
}

var xxx_messageInfo_AIAccount proto.InternalMessageInfo

func (m *AIAccount) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *AIAccount) GetIdentity() string {
	if m != nil {
		return m.Identity
	}
	return ""
}

func (m *AIAccount) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type GetAIPartnerEntranceRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAIPartnerEntranceRequest) Reset()         { *m = GetAIPartnerEntranceRequest{} }
func (m *GetAIPartnerEntranceRequest) String() string { return proto.CompactTextString(m) }
func (*GetAIPartnerEntranceRequest) ProtoMessage()    {}
func (*GetAIPartnerEntranceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{5}
}
func (m *GetAIPartnerEntranceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIPartnerEntranceRequest.Unmarshal(m, b)
}
func (m *GetAIPartnerEntranceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIPartnerEntranceRequest.Marshal(b, m, deterministic)
}
func (dst *GetAIPartnerEntranceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIPartnerEntranceRequest.Merge(dst, src)
}
func (m *GetAIPartnerEntranceRequest) XXX_Size() int {
	return xxx_messageInfo_GetAIPartnerEntranceRequest.Size(m)
}
func (m *GetAIPartnerEntranceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIPartnerEntranceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIPartnerEntranceRequest proto.InternalMessageInfo

func (m *GetAIPartnerEntranceRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetAIPartnerEntranceResponse struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// 是否展示触达入口
	Enable bool `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
	// 入口背景图
	Background string `protobuf:"bytes,3,opt,name=background,proto3" json:"background,omitempty"`
	// 跳转链接
	JumpLink string `protobuf:"bytes,4,opt,name=jump_link,json=jumpLink,proto3" json:"jump_link,omitempty"`
	// IM消息文案库
	ImMsgTexts []string `protobuf:"bytes,5,rep,name=im_msg_texts,json=imMsgTexts,proto3" json:"im_msg_texts,omitempty"`
	// 用户的AI伴侣(为空表示还没创建过AI伴侣)
	Partner              *AIPartner `protobuf:"bytes,6,opt,name=partner,proto3" json:"partner,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAIPartnerEntranceResponse) Reset()         { *m = GetAIPartnerEntranceResponse{} }
func (m *GetAIPartnerEntranceResponse) String() string { return proto.CompactTextString(m) }
func (*GetAIPartnerEntranceResponse) ProtoMessage()    {}
func (*GetAIPartnerEntranceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{6}
}
func (m *GetAIPartnerEntranceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIPartnerEntranceResponse.Unmarshal(m, b)
}
func (m *GetAIPartnerEntranceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIPartnerEntranceResponse.Marshal(b, m, deterministic)
}
func (dst *GetAIPartnerEntranceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIPartnerEntranceResponse.Merge(dst, src)
}
func (m *GetAIPartnerEntranceResponse) XXX_Size() int {
	return xxx_messageInfo_GetAIPartnerEntranceResponse.Size(m)
}
func (m *GetAIPartnerEntranceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIPartnerEntranceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIPartnerEntranceResponse proto.InternalMessageInfo

func (m *GetAIPartnerEntranceResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAIPartnerEntranceResponse) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

func (m *GetAIPartnerEntranceResponse) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *GetAIPartnerEntranceResponse) GetJumpLink() string {
	if m != nil {
		return m.JumpLink
	}
	return ""
}

func (m *GetAIPartnerEntranceResponse) GetImMsgTexts() []string {
	if m != nil {
		return m.ImMsgTexts
	}
	return nil
}

func (m *GetAIPartnerEntranceResponse) GetPartner() *AIPartner {
	if m != nil {
		return m.Partner
	}
	return nil
}

type AIPartnerEntrance struct {
	// 入口类型 see enum AIPartnerEntranceType
	Type uint32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	// 前景图
	Foreground string `protobuf:"bytes,2,opt,name=foreground,proto3" json:"foreground,omitempty"`
	// 背景图
	Background string `protobuf:"bytes,3,opt,name=background,proto3" json:"background,omitempty"`
	// 跳转链接
	JumpLink string `protobuf:"bytes,4,opt,name=jump_link,json=jumpLink,proto3" json:"jump_link,omitempty"`
	// 旧版本入口:返回默认第一个树洞伴侣 新版本入口:返回全量AI伴侣
	Partners []*AIPartner `protobuf:"bytes,5,rep,name=partners,proto3" json:"partners,omitempty"`
	// 标题
	Title string `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	// 标签
	Tag string `protobuf:"bytes,7,opt,name=tag,proto3" json:"tag,omitempty"`
	// 副标题
	Subtitle             string                       `protobuf:"bytes,8,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	LastGroupMsgs        []*web_im_logic.LastGroupMsg `protobuf:"bytes,9,rep,name=last_group_msgs,json=lastGroupMsgs,proto3" json:"last_group_msgs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *AIPartnerEntrance) Reset()         { *m = AIPartnerEntrance{} }
func (m *AIPartnerEntrance) String() string { return proto.CompactTextString(m) }
func (*AIPartnerEntrance) ProtoMessage()    {}
func (*AIPartnerEntrance) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{7}
}
func (m *AIPartnerEntrance) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIPartnerEntrance.Unmarshal(m, b)
}
func (m *AIPartnerEntrance) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIPartnerEntrance.Marshal(b, m, deterministic)
}
func (dst *AIPartnerEntrance) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIPartnerEntrance.Merge(dst, src)
}
func (m *AIPartnerEntrance) XXX_Size() int {
	return xxx_messageInfo_AIPartnerEntrance.Size(m)
}
func (m *AIPartnerEntrance) XXX_DiscardUnknown() {
	xxx_messageInfo_AIPartnerEntrance.DiscardUnknown(m)
}

var xxx_messageInfo_AIPartnerEntrance proto.InternalMessageInfo

func (m *AIPartnerEntrance) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AIPartnerEntrance) GetForeground() string {
	if m != nil {
		return m.Foreground
	}
	return ""
}

func (m *AIPartnerEntrance) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *AIPartnerEntrance) GetJumpLink() string {
	if m != nil {
		return m.JumpLink
	}
	return ""
}

func (m *AIPartnerEntrance) GetPartners() []*AIPartner {
	if m != nil {
		return m.Partners
	}
	return nil
}

func (m *AIPartnerEntrance) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *AIPartnerEntrance) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *AIPartnerEntrance) GetSubtitle() string {
	if m != nil {
		return m.Subtitle
	}
	return ""
}

func (m *AIPartnerEntrance) GetLastGroupMsgs() []*web_im_logic.LastGroupMsg {
	if m != nil {
		return m.LastGroupMsgs
	}
	return nil
}

type GetAIPartnerEntranceV2Request struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 请求来源
	Source               AIPartnerEntranceSource `protobuf:"varint,2,opt,name=source,proto3,enum=ga.chat_bot_logic.AIPartnerEntranceSource" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetAIPartnerEntranceV2Request) Reset()         { *m = GetAIPartnerEntranceV2Request{} }
func (m *GetAIPartnerEntranceV2Request) String() string { return proto.CompactTextString(m) }
func (*GetAIPartnerEntranceV2Request) ProtoMessage()    {}
func (*GetAIPartnerEntranceV2Request) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{8}
}
func (m *GetAIPartnerEntranceV2Request) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIPartnerEntranceV2Request.Unmarshal(m, b)
}
func (m *GetAIPartnerEntranceV2Request) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIPartnerEntranceV2Request.Marshal(b, m, deterministic)
}
func (dst *GetAIPartnerEntranceV2Request) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIPartnerEntranceV2Request.Merge(dst, src)
}
func (m *GetAIPartnerEntranceV2Request) XXX_Size() int {
	return xxx_messageInfo_GetAIPartnerEntranceV2Request.Size(m)
}
func (m *GetAIPartnerEntranceV2Request) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIPartnerEntranceV2Request.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIPartnerEntranceV2Request proto.InternalMessageInfo

func (m *GetAIPartnerEntranceV2Request) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetAIPartnerEntranceV2Request) GetSource() AIPartnerEntranceSource {
	if m != nil {
		return m.Source
	}
	return AIPartnerEntranceSource_AI_PARTNER_ENTRANCE_SOURCE_UNSPECIFIED
}

type GetAIPartnerEntranceV2Response struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Entrance             *AIPartnerEntrance `protobuf:"bytes,2,opt,name=entrance,proto3" json:"entrance,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetAIPartnerEntranceV2Response) Reset()         { *m = GetAIPartnerEntranceV2Response{} }
func (m *GetAIPartnerEntranceV2Response) String() string { return proto.CompactTextString(m) }
func (*GetAIPartnerEntranceV2Response) ProtoMessage()    {}
func (*GetAIPartnerEntranceV2Response) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{9}
}
func (m *GetAIPartnerEntranceV2Response) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIPartnerEntranceV2Response.Unmarshal(m, b)
}
func (m *GetAIPartnerEntranceV2Response) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIPartnerEntranceV2Response.Marshal(b, m, deterministic)
}
func (dst *GetAIPartnerEntranceV2Response) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIPartnerEntranceV2Response.Merge(dst, src)
}
func (m *GetAIPartnerEntranceV2Response) XXX_Size() int {
	return xxx_messageInfo_GetAIPartnerEntranceV2Response.Size(m)
}
func (m *GetAIPartnerEntranceV2Response) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIPartnerEntranceV2Response.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIPartnerEntranceV2Response proto.InternalMessageInfo

func (m *GetAIPartnerEntranceV2Response) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAIPartnerEntranceV2Response) GetEntrance() *AIPartnerEntrance {
	if m != nil {
		return m.Entrance
	}
	return nil
}

type GetAIPartnerEntranceListRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 请求来源 see enum AIPartnerEntranceSource
	Source uint32 `protobuf:"varint,2,opt,name=source,proto3" json:"source,omitempty"`
	// 指定需要返回的入口 see enum AIPartnerEntranceType
	EntranceTypeList     []uint32 `protobuf:"varint,3,rep,packed,name=entrance_type_list,json=entranceTypeList,proto3" json:"entrance_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAIPartnerEntranceListRequest) Reset()         { *m = GetAIPartnerEntranceListRequest{} }
func (m *GetAIPartnerEntranceListRequest) String() string { return proto.CompactTextString(m) }
func (*GetAIPartnerEntranceListRequest) ProtoMessage()    {}
func (*GetAIPartnerEntranceListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{10}
}
func (m *GetAIPartnerEntranceListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIPartnerEntranceListRequest.Unmarshal(m, b)
}
func (m *GetAIPartnerEntranceListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIPartnerEntranceListRequest.Marshal(b, m, deterministic)
}
func (dst *GetAIPartnerEntranceListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIPartnerEntranceListRequest.Merge(dst, src)
}
func (m *GetAIPartnerEntranceListRequest) XXX_Size() int {
	return xxx_messageInfo_GetAIPartnerEntranceListRequest.Size(m)
}
func (m *GetAIPartnerEntranceListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIPartnerEntranceListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIPartnerEntranceListRequest proto.InternalMessageInfo

func (m *GetAIPartnerEntranceListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetAIPartnerEntranceListRequest) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *GetAIPartnerEntranceListRequest) GetEntranceTypeList() []uint32 {
	if m != nil {
		return m.EntranceTypeList
	}
	return nil
}

type GetAIPartnerEntranceListResponse struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	EntranceList         []*AIPartnerEntrance `protobuf:"bytes,2,rep,name=entrance_list,json=entranceList,proto3" json:"entrance_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetAIPartnerEntranceListResponse) Reset()         { *m = GetAIPartnerEntranceListResponse{} }
func (m *GetAIPartnerEntranceListResponse) String() string { return proto.CompactTextString(m) }
func (*GetAIPartnerEntranceListResponse) ProtoMessage()    {}
func (*GetAIPartnerEntranceListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{11}
}
func (m *GetAIPartnerEntranceListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIPartnerEntranceListResponse.Unmarshal(m, b)
}
func (m *GetAIPartnerEntranceListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIPartnerEntranceListResponse.Marshal(b, m, deterministic)
}
func (dst *GetAIPartnerEntranceListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIPartnerEntranceListResponse.Merge(dst, src)
}
func (m *GetAIPartnerEntranceListResponse) XXX_Size() int {
	return xxx_messageInfo_GetAIPartnerEntranceListResponse.Size(m)
}
func (m *GetAIPartnerEntranceListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIPartnerEntranceListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIPartnerEntranceListResponse proto.InternalMessageInfo

func (m *GetAIPartnerEntranceListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAIPartnerEntranceListResponse) GetEntranceList() []*AIPartnerEntrance {
	if m != nil {
		return m.EntranceList
	}
	return nil
}

// AI角色交互配置
type AIRoleInteractiveConfig struct {
	// 角色ID
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 透传AIGC中台扩展配置
	Ext                  []byte   `protobuf:"bytes,2,opt,name=ext,proto3" json:"ext,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIRoleInteractiveConfig) Reset()         { *m = AIRoleInteractiveConfig{} }
func (m *AIRoleInteractiveConfig) String() string { return proto.CompactTextString(m) }
func (*AIRoleInteractiveConfig) ProtoMessage()    {}
func (*AIRoleInteractiveConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{12}
}
func (m *AIRoleInteractiveConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRoleInteractiveConfig.Unmarshal(m, b)
}
func (m *AIRoleInteractiveConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRoleInteractiveConfig.Marshal(b, m, deterministic)
}
func (dst *AIRoleInteractiveConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRoleInteractiveConfig.Merge(dst, src)
}
func (m *AIRoleInteractiveConfig) XXX_Size() int {
	return xxx_messageInfo_AIRoleInteractiveConfig.Size(m)
}
func (m *AIRoleInteractiveConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRoleInteractiveConfig.DiscardUnknown(m)
}

var xxx_messageInfo_AIRoleInteractiveConfig proto.InternalMessageInfo

func (m *AIRoleInteractiveConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIRoleInteractiveConfig) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

type GetAIRoleInteractiveConfigRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 角色ID
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAIRoleInteractiveConfigRequest) Reset()         { *m = GetAIRoleInteractiveConfigRequest{} }
func (m *GetAIRoleInteractiveConfigRequest) String() string { return proto.CompactTextString(m) }
func (*GetAIRoleInteractiveConfigRequest) ProtoMessage()    {}
func (*GetAIRoleInteractiveConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{13}
}
func (m *GetAIRoleInteractiveConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIRoleInteractiveConfigRequest.Unmarshal(m, b)
}
func (m *GetAIRoleInteractiveConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIRoleInteractiveConfigRequest.Marshal(b, m, deterministic)
}
func (dst *GetAIRoleInteractiveConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIRoleInteractiveConfigRequest.Merge(dst, src)
}
func (m *GetAIRoleInteractiveConfigRequest) XXX_Size() int {
	return xxx_messageInfo_GetAIRoleInteractiveConfigRequest.Size(m)
}
func (m *GetAIRoleInteractiveConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIRoleInteractiveConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIRoleInteractiveConfigRequest proto.InternalMessageInfo

func (m *GetAIRoleInteractiveConfigRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetAIRoleInteractiveConfigRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetAIRoleInteractiveConfigResponse struct {
	BaseResp             *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Config               *AIRoleInteractiveConfig `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetAIRoleInteractiveConfigResponse) Reset()         { *m = GetAIRoleInteractiveConfigResponse{} }
func (m *GetAIRoleInteractiveConfigResponse) String() string { return proto.CompactTextString(m) }
func (*GetAIRoleInteractiveConfigResponse) ProtoMessage()    {}
func (*GetAIRoleInteractiveConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{14}
}
func (m *GetAIRoleInteractiveConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIRoleInteractiveConfigResponse.Unmarshal(m, b)
}
func (m *GetAIRoleInteractiveConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIRoleInteractiveConfigResponse.Marshal(b, m, deterministic)
}
func (dst *GetAIRoleInteractiveConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIRoleInteractiveConfigResponse.Merge(dst, src)
}
func (m *GetAIRoleInteractiveConfigResponse) XXX_Size() int {
	return xxx_messageInfo_GetAIRoleInteractiveConfigResponse.Size(m)
}
func (m *GetAIRoleInteractiveConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIRoleInteractiveConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIRoleInteractiveConfigResponse proto.InternalMessageInfo

func (m *GetAIRoleInteractiveConfigResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAIRoleInteractiveConfigResponse) GetConfig() *AIRoleInteractiveConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type ReportAIPetBehaviorRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 客户端与AIGC中台约定的行为枚举
	Action               uint32   `protobuf:"varint,2,opt,name=action,proto3" json:"action,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	PartnerId            uint32   `protobuf:"varint,4,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	Tip                  string   `protobuf:"bytes,5,opt,name=tip,proto3" json:"tip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportAIPetBehaviorRequest) Reset()         { *m = ReportAIPetBehaviorRequest{} }
func (m *ReportAIPetBehaviorRequest) String() string { return proto.CompactTextString(m) }
func (*ReportAIPetBehaviorRequest) ProtoMessage()    {}
func (*ReportAIPetBehaviorRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{15}
}
func (m *ReportAIPetBehaviorRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportAIPetBehaviorRequest.Unmarshal(m, b)
}
func (m *ReportAIPetBehaviorRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportAIPetBehaviorRequest.Marshal(b, m, deterministic)
}
func (dst *ReportAIPetBehaviorRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportAIPetBehaviorRequest.Merge(dst, src)
}
func (m *ReportAIPetBehaviorRequest) XXX_Size() int {
	return xxx_messageInfo_ReportAIPetBehaviorRequest.Size(m)
}
func (m *ReportAIPetBehaviorRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportAIPetBehaviorRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReportAIPetBehaviorRequest proto.InternalMessageInfo

func (m *ReportAIPetBehaviorRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportAIPetBehaviorRequest) GetAction() uint32 {
	if m != nil {
		return m.Action
	}
	return 0
}

func (m *ReportAIPetBehaviorRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *ReportAIPetBehaviorRequest) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *ReportAIPetBehaviorRequest) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

type ReportAIPetBehaviorResponse struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// 上报后分配给tip的消息ID
	MsgId string `protobuf:"bytes,2,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	// 上报时间 ms
	ReportedAt           int64    `protobuf:"varint,3,opt,name=reported_at,json=reportedAt,proto3" json:"reported_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportAIPetBehaviorResponse) Reset()         { *m = ReportAIPetBehaviorResponse{} }
func (m *ReportAIPetBehaviorResponse) String() string { return proto.CompactTextString(m) }
func (*ReportAIPetBehaviorResponse) ProtoMessage()    {}
func (*ReportAIPetBehaviorResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{16}
}
func (m *ReportAIPetBehaviorResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportAIPetBehaviorResponse.Unmarshal(m, b)
}
func (m *ReportAIPetBehaviorResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportAIPetBehaviorResponse.Marshal(b, m, deterministic)
}
func (dst *ReportAIPetBehaviorResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportAIPetBehaviorResponse.Merge(dst, src)
}
func (m *ReportAIPetBehaviorResponse) XXX_Size() int {
	return xxx_messageInfo_ReportAIPetBehaviorResponse.Size(m)
}
func (m *ReportAIPetBehaviorResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportAIPetBehaviorResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReportAIPetBehaviorResponse proto.InternalMessageInfo

func (m *ReportAIPetBehaviorResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ReportAIPetBehaviorResponse) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *ReportAIPetBehaviorResponse) GetReportedAt() int64 {
	if m != nil {
		return m.ReportedAt
	}
	return 0
}

type BatchGetAIAccountRequest struct {
	BaseReq  *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Accounts []string     `protobuf:"bytes,2,rep,name=accounts,proto3" json:"accounts,omitempty"`
	// 请求来源 see enum RequestSource
	ReqSrc               uint32   `protobuf:"varint,3,opt,name=req_src,json=reqSrc,proto3" json:"req_src,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAIAccountRequest) Reset()         { *m = BatchGetAIAccountRequest{} }
func (m *BatchGetAIAccountRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetAIAccountRequest) ProtoMessage()    {}
func (*BatchGetAIAccountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{17}
}
func (m *BatchGetAIAccountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAIAccountRequest.Unmarshal(m, b)
}
func (m *BatchGetAIAccountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAIAccountRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetAIAccountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAIAccountRequest.Merge(dst, src)
}
func (m *BatchGetAIAccountRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetAIAccountRequest.Size(m)
}
func (m *BatchGetAIAccountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAIAccountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAIAccountRequest proto.InternalMessageInfo

func (m *BatchGetAIAccountRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatchGetAIAccountRequest) GetAccounts() []string {
	if m != nil {
		return m.Accounts
	}
	return nil
}

func (m *BatchGetAIAccountRequest) GetReqSrc() uint32 {
	if m != nil {
		return m.ReqSrc
	}
	return 0
}

type BatchGetAIAccountResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	List                 []*AIAccount  `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BatchGetAIAccountResponse) Reset()         { *m = BatchGetAIAccountResponse{} }
func (m *BatchGetAIAccountResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetAIAccountResponse) ProtoMessage()    {}
func (*BatchGetAIAccountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_chat_bot_logic_eb8d10dc596327cc, []int{18}
}
func (m *BatchGetAIAccountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAIAccountResponse.Unmarshal(m, b)
}
func (m *BatchGetAIAccountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAIAccountResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetAIAccountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAIAccountResponse.Merge(dst, src)
}
func (m *BatchGetAIAccountResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetAIAccountResponse.Size(m)
}
func (m *BatchGetAIAccountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAIAccountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAIAccountResponse proto.InternalMessageInfo

func (m *BatchGetAIAccountResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BatchGetAIAccountResponse) GetList() []*AIAccount {
	if m != nil {
		return m.List
	}
	return nil
}

func init() {
	proto.RegisterType((*AIRole)(nil), "ga.chat_bot_logic.AIRole")
	proto.RegisterType((*AIRelationship)(nil), "ga.chat_bot_logic.AIRelationship")
	proto.RegisterType((*AIPartner)(nil), "ga.chat_bot_logic.AIPartner")
	proto.RegisterType((*AIPartnerPushMsg)(nil), "ga.chat_bot_logic.AIPartnerPushMsg")
	proto.RegisterType((*AIAccount)(nil), "ga.chat_bot_logic.AIAccount")
	proto.RegisterType((*GetAIPartnerEntranceRequest)(nil), "ga.chat_bot_logic.GetAIPartnerEntranceRequest")
	proto.RegisterType((*GetAIPartnerEntranceResponse)(nil), "ga.chat_bot_logic.GetAIPartnerEntranceResponse")
	proto.RegisterType((*AIPartnerEntrance)(nil), "ga.chat_bot_logic.AIPartnerEntrance")
	proto.RegisterType((*GetAIPartnerEntranceV2Request)(nil), "ga.chat_bot_logic.GetAIPartnerEntranceV2Request")
	proto.RegisterType((*GetAIPartnerEntranceV2Response)(nil), "ga.chat_bot_logic.GetAIPartnerEntranceV2Response")
	proto.RegisterType((*GetAIPartnerEntranceListRequest)(nil), "ga.chat_bot_logic.GetAIPartnerEntranceListRequest")
	proto.RegisterType((*GetAIPartnerEntranceListResponse)(nil), "ga.chat_bot_logic.GetAIPartnerEntranceListResponse")
	proto.RegisterType((*AIRoleInteractiveConfig)(nil), "ga.chat_bot_logic.AIRoleInteractiveConfig")
	proto.RegisterType((*GetAIRoleInteractiveConfigRequest)(nil), "ga.chat_bot_logic.GetAIRoleInteractiveConfigRequest")
	proto.RegisterType((*GetAIRoleInteractiveConfigResponse)(nil), "ga.chat_bot_logic.GetAIRoleInteractiveConfigResponse")
	proto.RegisterType((*ReportAIPetBehaviorRequest)(nil), "ga.chat_bot_logic.ReportAIPetBehaviorRequest")
	proto.RegisterType((*ReportAIPetBehaviorResponse)(nil), "ga.chat_bot_logic.ReportAIPetBehaviorResponse")
	proto.RegisterType((*BatchGetAIAccountRequest)(nil), "ga.chat_bot_logic.BatchGetAIAccountRequest")
	proto.RegisterType((*BatchGetAIAccountResponse)(nil), "ga.chat_bot_logic.BatchGetAIAccountResponse")
	proto.RegisterEnum("ga.chat_bot_logic.AIPartnerEntranceType", AIPartnerEntranceType_name, AIPartnerEntranceType_value)
	proto.RegisterEnum("ga.chat_bot_logic.AIPartnerEntranceSource", AIPartnerEntranceSource_name, AIPartnerEntranceSource_value)
	proto.RegisterEnum("ga.chat_bot_logic.AIRoleType", AIRoleType_name, AIRoleType_value)
	proto.RegisterEnum("ga.chat_bot_logic.AIRoleState", AIRoleState_name, AIRoleState_value)
	proto.RegisterEnum("ga.chat_bot_logic.AIPartner_Relationship", AIPartner_Relationship_name, AIPartner_Relationship_value)
	proto.RegisterEnum("ga.chat_bot_logic.BatchGetAIAccountRequest_RequestSource", BatchGetAIAccountRequest_RequestSource_name, BatchGetAIAccountRequest_RequestSource_value)
}

func init() {
	proto.RegisterFile("chat_bot_logic/chat_bot_logic.proto", fileDescriptor_chat_bot_logic_eb8d10dc596327cc)
}

var fileDescriptor_chat_bot_logic_eb8d10dc596327cc = []byte{
	// 1472 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x57, 0x5f, 0x6f, 0x1b, 0x45,
	0x10, 0xe7, 0xec, 0xc4, 0xb1, 0xc7, 0x71, 0x70, 0x97, 0xa6, 0xbd, 0xa4, 0x49, 0xea, 0x5c, 0x4b,
	0x49, 0x03, 0x75, 0x91, 0xf9, 0x23, 0x24, 0x84, 0xc4, 0x39, 0xbd, 0xa6, 0x27, 0xf9, 0x1f, 0x6b,
	0xbb, 0x12, 0x20, 0x74, 0x5a, 0x9f, 0xb7, 0xce, 0x91, 0xf3, 0xdd, 0xe5, 0x76, 0xdd, 0x26, 0x6f,
	0xbc, 0x23, 0x84, 0x04, 0x2f, 0x7c, 0x02, 0xde, 0xf9, 0x18, 0x7c, 0x02, 0x3e, 0x05, 0x4f, 0xbc,
	0xf0, 0x88, 0x76, 0xef, 0xce, 0xb1, 0x1d, 0x3b, 0xd4, 0xf0, 0x36, 0x33, 0x3b, 0xb3, 0x33, 0xf3,
	0x9b, 0x9f, 0x77, 0xce, 0x70, 0xcf, 0x3e, 0x21, 0xdc, 0xea, 0xf9, 0xdc, 0x72, 0xfd, 0x81, 0x63,
	0x3f, 0x9e, 0x56, 0xcb, 0x41, 0xe8, 0x73, 0x1f, 0xdd, 0x18, 0x90, 0xf2, 0xf4, 0xc1, 0x76, 0x61,
	0x40, 0xac, 0x1e, 0x61, 0x34, 0xf2, 0xd8, 0xbe, 0xfb, 0x8a, 0xf6, 0x2c, 0x67, 0x18, 0x5f, 0x32,
	0xa9, 0x44, 0x0e, 0xda, 0x5f, 0x0a, 0x64, 0x74, 0x13, 0xfb, 0x2e, 0x45, 0x1b, 0x90, 0x72, 0xfa,
	0xaa, 0x52, 0x52, 0x0e, 0x0a, 0x38, 0xe5, 0xf4, 0xd1, 0x2d, 0xc8, 0x90, 0x97, 0x84, 0x93, 0x50,
	0x4d, 0x95, 0x94, 0x83, 0x1c, 0x8e, 0x35, 0x74, 0x13, 0x56, 0x19, 0xbf, 0x70, 0xa9, 0x9a, 0x96,
	0xe6, 0x48, 0x41, 0x45, 0x48, 0x33, 0x7a, 0xae, 0xae, 0x94, 0x94, 0x83, 0x55, 0x2c, 0x44, 0xe1,
	0xe7, 0x0c, 0xc9, 0x80, 0xaa, 0xab, 0x91, 0x9f, 0x54, 0x10, 0x82, 0x15, 0x7e, 0x11, 0x50, 0x35,
	0x23, 0xf3, 0x48, 0x59, 0xd8, 0x3c, 0x32, 0xa4, 0xea, 0x9a, 0x74, 0x94, 0x72, 0x94, 0x85, 0x70,
	0xaa, 0x66, 0xa5, 0x63, 0xa4, 0x88, 0x2c, 0x23, 0xa7, 0xaf, 0xe6, 0xa4, 0x4d, 0x88, 0xc2, 0x42,
	0xcf, 0xb9, 0x0a, 0x25, 0xe5, 0x60, 0x1d, 0x0b, 0x11, 0xed, 0xc3, 0x3a, 0xf5, 0x78, 0x48, 0x3c,
	0x9b, 0x5a, 0x9c, 0x0c, 0xd4, 0xbc, 0xbc, 0x35, 0x9f, 0xd8, 0x3a, 0x64, 0xa0, 0x7d, 0x08, 0x1b,
	0xba, 0x89, 0xa9, 0x4b, 0xb8, 0xe3, 0x7b, 0xec, 0xc4, 0x09, 0xae, 0x34, 0x9f, 0x94, 0x94, 0xba,
	0x2c, 0x49, 0xfb, 0x2d, 0x0d, 0x39, 0xdd, 0x6c, 0x91, 0x90, 0x7b, 0x34, 0x7c, 0x9d, 0x08, 0x74,
	0x07, 0x72, 0x36, 0x71, 0x5d, 0x4b, 0x1e, 0x44, 0x70, 0x65, 0x85, 0xa1, 0x21, 0x0e, 0x1f, 0xc1,
	0x4a, 0xe8, 0xbb, 0x54, 0x42, 0x96, 0xaf, 0x6c, 0x95, 0xaf, 0x0c, 0xb3, 0x1c, 0x0d, 0x06, 0x4b,
	0x37, 0x54, 0x87, 0xf5, 0x70, 0xa2, 0x62, 0x89, 0xea, 0x46, 0xe5, 0xe1, 0xdc, 0xb0, 0xb8, 0xc6,
	0xf2, 0x64, 0x8b, 0x78, 0x2a, 0x5c, 0x4c, 0x97, 0x39, 0x2e, 0xf5, 0xb8, 0x9c, 0x44, 0x16, 0xc7,
	0x1a, 0xfa, 0x0c, 0xb2, 0x89, 0x9f, 0x9c, 0x47, 0xbe, 0xb2, 0x3f, 0xbf, 0xb2, 0xc9, 0xab, 0xc7,
	0x21, 0xa2, 0x63, 0x76, 0xe2, 0xbf, 0xb2, 0x4e, 0x1c, 0x8f, 0xcb, 0xd1, 0x65, 0x71, 0x56, 0x18,
	0x9e, 0x39, 0x1e, 0x17, 0x10, 0x49, 0x7b, 0x2e, 0x82, 0x48, 0xc8, 0xda, 0x37, 0xb0, 0x3e, 0x35,
	0x88, 0x1d, 0x50, 0xb1, 0x51, 0xd3, 0x3b, 0x66, 0xb3, 0xd1, 0x7e, 0x66, 0xb6, 0xac, 0x6e, 0xa3,
	0xdd, 0x32, 0x8e, 0xcc, 0xa7, 0xa6, 0xf1, 0xa4, 0xf8, 0x06, 0xba, 0x0d, 0x6f, 0x4d, 0x9d, 0x3e,
	0xc5, 0xa6, 0xd1, 0x78, 0x52, 0x54, 0xd0, 0x2d, 0x40, 0x53, 0x07, 0xb5, 0xe6, 0x73, 0x03, 0x17,
	0x53, 0xda, 0x29, 0x14, 0xc7, 0x70, 0xb4, 0x46, 0xec, 0xa4, 0xce, 0x06, 0x09, 0x89, 0x94, 0x4b,
	0x12, 0xed, 0x02, 0x04, 0x91, 0x8f, 0xe5, 0xf4, 0xe5, 0x04, 0x0b, 0x38, 0x17, 0x5b, 0xcc, 0xfe,
	0x98, 0xb3, 0xe9, 0x69, 0xce, 0xf6, 0x09, 0x27, 0x72, 0x7a, 0xeb, 0x58, 0xca, 0x5a, 0x57, 0xf0,
	0x43, 0xb7, 0x6d, 0x7f, 0xe4, 0x71, 0xa4, 0xc2, 0x1a, 0x89, 0xc4, 0x98, 0x12, 0x89, 0x8a, 0xb6,
	0x21, 0xeb, 0xf4, 0xa9, 0xc7, 0x1d, 0x7e, 0x21, 0xc1, 0xcf, 0xe1, 0xb1, 0x2e, 0xaf, 0xa5, 0xcc,
	0x4e, 0x7e, 0x0a, 0x42, 0xd6, 0x0c, 0xb8, 0x73, 0x4c, 0xf9, 0xb8, 0x0d, 0x23, 0x26, 0x32, 0xa6,
	0x67, 0x23, 0xca, 0x38, 0x7a, 0x00, 0x59, 0xf1, 0x8b, 0xb7, 0x42, 0x7a, 0x26, 0x7b, 0xca, 0x57,
	0xf2, 0x62, 0x62, 0x55, 0xc2, 0x84, 0x0b, 0x5e, 0xeb, 0x45, 0x82, 0xf6, 0xb7, 0x02, 0x3b, 0xf3,
	0xef, 0x61, 0x81, 0xef, 0x31, 0x8a, 0x1e, 0x42, 0x2e, 0xbe, 0x88, 0x05, 0xf1, 0x4d, 0xeb, 0x97,
	0x37, 0xb1, 0x00, 0x67, 0x7b, 0xb1, 0x24, 0xd8, 0x43, 0x3d, 0xd2, 0x73, 0x23, 0xba, 0x67, 0x71,
	0xac, 0xa1, 0x3d, 0x80, 0x1e, 0xb1, 0x4f, 0x07, 0xa1, 0x3f, 0xf2, 0xfa, 0x31, 0xe3, 0x27, 0x2c,
	0x82, 0x1e, 0xdf, 0x8e, 0x86, 0x81, 0xe5, 0x3a, 0xde, 0xa9, 0x84, 0x2e, 0x87, 0xb3, 0xc2, 0x50,
	0x73, 0xbc, 0x53, 0x54, 0x82, 0x75, 0x67, 0x68, 0x0d, 0xd9, 0xc0, 0xe2, 0xf4, 0x9c, 0x33, 0x75,
	0xb5, 0x94, 0x16, 0xe1, 0xce, 0xb0, 0xce, 0x06, 0x1d, 0x61, 0x41, 0x1f, 0xc3, 0x5a, 0x3c, 0x15,
	0x09, 0x5c, 0xbe, 0xb2, 0x73, 0x1d, 0xfd, 0x71, 0xe2, 0xac, 0xfd, 0x9e, 0x82, 0x1b, 0x57, 0xfa,
	0x1e, 0x8f, 0x55, 0x99, 0x18, 0xeb, 0x1e, 0xc0, 0x0b, 0x3f, 0xa4, 0x71, 0x03, 0xd1, 0xe0, 0x26,
	0x2c, 0xff, 0xaf, 0xc1, 0x4f, 0x20, 0x1b, 0x57, 0x14, 0x35, 0xf7, 0x6f, 0xf5, 0x8f, 0xbd, 0xc5,
	0x6b, 0xc8, 0x1d, 0xee, 0xd2, 0x98, 0x2f, 0x91, 0x22, 0x88, 0x2c, 0x1e, 0xb8, 0x88, 0x2b, 0x42,
	0x14, 0xd4, 0x62, 0xa3, 0x5e, 0xe4, 0x9a, 0x8d, 0xb2, 0x27, 0x3a, 0x32, 0xe0, 0x4d, 0x97, 0x30,
	0x6e, 0x89, 0x4a, 0x03, 0x01, 0x33, 0x53, 0x73, 0xb2, 0x88, 0x5d, 0x51, 0xc4, 0xd4, 0x6e, 0xa8,
	0x11, 0xc6, 0x8f, 0x85, 0x5b, 0x9d, 0x0d, 0x70, 0xc1, 0x9d, 0xd0, 0x98, 0xf6, 0xbd, 0x02, 0xbb,
	0xf3, 0x68, 0xf4, 0xbc, 0xb2, 0x24, 0x21, 0x51, 0x15, 0x32, 0xcc, 0x1f, 0x85, 0x76, 0x44, 0xa2,
	0x8d, 0xca, 0xe1, 0x75, 0x60, 0x24, 0x69, 0xda, 0x32, 0x02, 0xc7, 0x91, 0xda, 0x0f, 0x0a, 0xec,
	0x2d, 0xaa, 0x66, 0x79, 0x5a, 0x7f, 0x0e, 0xd9, 0x64, 0x4d, 0xc8, 0x9a, 0xf2, 0x95, 0xfb, 0xaf,
	0x53, 0x13, 0x1e, 0x47, 0x69, 0x3f, 0x2a, 0x70, 0x77, 0x5e, 0x3d, 0x35, 0x87, 0xf1, 0x65, 0xf1,
	0xb9, 0x35, 0x85, 0x4f, 0x21, 0xe9, 0x19, 0xbd, 0x07, 0xe8, 0x72, 0xc1, 0x5d, 0x04, 0xd4, 0x72,
	0x1d, 0xc6, 0xd5, 0x74, 0x29, 0x7d, 0x50, 0xc0, 0xc5, 0xf1, 0x9a, 0xbb, 0x08, 0x64, 0x52, 0xed,
	0x17, 0x05, 0x4a, 0x8b, 0x2b, 0x5a, 0x1e, 0x23, 0x13, 0x0a, 0xe3, 0xec, 0x32, 0x71, 0x4a, 0x92,
	0xe8, 0xf5, 0x80, 0x1a, 0x6f, 0x66, 0x59, 0xda, 0xa7, 0x70, 0x3b, 0x5a, 0x71, 0xa6, 0xc7, 0x69,
	0x48, 0x6c, 0xee, 0xbc, 0xa4, 0x47, 0xbe, 0xf7, 0xc2, 0x19, 0x5c, 0xd9, 0xae, 0xf1, 0x9a, 0x4f,
	0x8d, 0xd7, 0xbc, 0xf6, 0x35, 0xec, 0xcb, 0xb6, 0xe6, 0xc6, 0x2f, 0x0b, 0x75, 0x94, 0x2e, 0x95,
	0xa4, 0xd3, 0x7e, 0x56, 0x40, 0xbb, 0xee, 0xf6, 0xe5, 0x61, 0xab, 0x42, 0xc6, 0x96, 0xc1, 0x31,
	0xb1, 0x0e, 0x17, 0xee, 0xfb, 0xab, 0xe9, 0xe2, 0x48, 0xed, 0x57, 0x05, 0xb6, 0x31, 0x0d, 0xfc,
	0x50, 0x4c, 0x93, 0xf2, 0x2a, 0x3d, 0x21, 0x2f, 0x1d, 0x3f, 0xfc, 0x0f, 0xbc, 0x12, 0xd7, 0xfb,
	0x5e, 0xc2, 0xab, 0x48, 0x43, 0xb7, 0x61, 0x4d, 0x7c, 0x69, 0x88, 0x15, 0x18, 0x6d, 0xba, 0x8c,
	0x50, 0xcd, 0xd9, 0xf5, 0xb8, 0x32, 0xbb, 0x1e, 0xc5, 0x33, 0x14, 0x7f, 0x90, 0x88, 0x67, 0xc8,
	0x09, 0xb4, 0xef, 0x14, 0xb8, 0x33, 0xb7, 0xd0, 0xe5, 0x71, 0xdb, 0x84, 0x8c, 0xd8, 0x08, 0x4e,
	0xf2, 0x18, 0xaf, 0x0e, 0xd9, 0xc0, 0xec, 0xa3, 0xbb, 0x90, 0x0f, 0x65, 0x02, 0xda, 0xb7, 0x08,
	0x97, 0xf5, 0xa6, 0x31, 0x24, 0x26, 0x9d, 0x6b, 0x7f, 0x2a, 0xa0, 0x56, 0x09, 0xb7, 0x4f, 0xe4,
	0x18, 0xe3, 0xa5, 0xbc, 0x2c, 0x52, 0xdb, 0x90, 0x8d, 0x97, 0x36, 0x93, 0x34, 0xcf, 0xe1, 0xb1,
	0x2e, 0xd1, 0xa2, 0x67, 0x16, 0x0b, 0xed, 0x31, 0x5a, 0xf4, 0xac, 0x1d, 0xda, 0x1a, 0x87, 0x42,
	0x9c, 0x27, 0x7a, 0xab, 0xd0, 0x1e, 0x6c, 0x63, 0xe3, 0x8b, 0xae, 0xd1, 0xee, 0x58, 0xed, 0x66,
	0x17, 0x1f, 0x19, 0x33, 0x1f, 0x35, 0x5b, 0xb0, 0x39, 0x73, 0x6e, 0xd6, 0xad, 0x8e, 0x5e, 0x2d,
	0x2a, 0xe8, 0x00, 0xee, 0xcf, 0x1c, 0x1d, 0x3d, 0xd3, 0x1b, 0x0d, 0xa3, 0x66, 0xb5, 0x0c, 0xdc,
	0x6e, 0x36, 0xf4, 0x9a, 0xd5, 0xd2, 0x8f, 0x8d, 0x62, 0x4a, 0x3b, 0x87, 0xad, 0x39, 0xed, 0x2e,
	0x8f, 0xf7, 0xfb, 0xb0, 0x32, 0xf1, 0xab, 0x9e, 0xbf, 0x9f, 0x92, 0xeb, 0xa5, 0xe7, 0xe1, 0x1f,
	0x0a, 0x6c, 0x5e, 0xf9, 0xa5, 0x8b, 0xe7, 0x47, 0x54, 0xaf, 0x9b, 0x56, 0x4b, 0xc7, 0x9d, 0x86,
	0x81, 0x2d, 0xa3, 0xd1, 0xc1, 0x7a, 0xe3, 0xc8, 0xb0, 0x3a, 0x5f, 0xb6, 0x66, 0x21, 0x78, 0x1b,
	0xf6, 0x17, 0x7a, 0xb6, 0x9b, 0xdd, 0x5a, 0x5d, 0xef, 0x18, 0x45, 0x05, 0xbd, 0x03, 0xf7, 0x16,
	0xba, 0xd5, 0xbb, 0xb5, 0x8e, 0x69, 0xe1, 0x66, 0xcd, 0x28, 0xa6, 0x50, 0x09, 0x76, 0x16, 0x3a,
	0xb6, 0x8c, 0x4e, 0x31, 0x8d, 0x34, 0xd8, 0x5b, 0xe8, 0x71, 0x8c, 0x9b, 0xdd, 0x56, 0x71, 0xe5,
	0xf0, 0x27, 0x45, 0x3c, 0x50, 0x73, 0x17, 0x10, 0x3a, 0x84, 0x07, 0xf3, 0xe2, 0xe7, 0x0e, 0x78,
	0x41, 0x77, 0xb3, 0xc3, 0xbe, 0x0f, 0xa5, 0x6b, 0xdc, 0x6a, 0xcd, 0x63, 0xb3, 0x51, 0x4c, 0x1d,
	0x5a, 0x00, 0xd1, 0x3b, 0x21, 0x21, 0x8e, 0x62, 0x44, 0xd7, 0x71, 0x73, 0x71, 0xf4, 0x74, 0x01,
	0x9b, 0xe2, 0xf3, 0x67, 0xc2, 0xeb, 0x58, 0xaf, 0x0b, 0x38, 0x6f, 0x8a, 0x8f, 0xe3, 0xc9, 0x60,
	0xa3, 0x53, 0x4c, 0x1d, 0x12, 0xc8, 0x47, 0x09, 0xda, 0xf2, 0x2f, 0xd7, 0x2e, 0x6c, 0x25, 0x4e,
	0xed, 0x8e, 0xde, 0x99, 0xed, 0x4d, 0x85, 0x9b, 0xd3, 0xc7, 0xad, 0x6e, 0xb5, 0x66, 0x1e, 0x15,
	0x15, 0x41, 0xeb, 0x99, 0x13, 0x6c, 0x3e, 0x17, 0x73, 0x4c, 0x55, 0x8f, 0x41, 0xb5, 0xfd, 0x61,
	0xf9, 0xc2, 0xb9, 0xf0, 0x47, 0x82, 0x61, 0x43, 0xbf, 0x4f, 0xdd, 0xe8, 0x1f, 0xe9, 0x57, 0xef,
	0x0e, 0x7c, 0x97, 0x78, 0x83, 0xf2, 0x47, 0x15, 0xce, 0xcb, 0xb6, 0x3f, 0x7c, 0x2c, 0xcd, 0xb6,
	0xef, 0x3e, 0x26, 0x41, 0x20, 0xff, 0x07, 0x3f, 0xea, 0xf9, 0xfc, 0x91, 0xe4, 0x62, 0x2f, 0x23,
	0x0f, 0x3f, 0xf8, 0x27, 0x00, 0x00, 0xff, 0xff, 0xa4, 0xdb, 0x0c, 0xc0, 0x2f, 0x0f, 0x00, 0x00,
}
