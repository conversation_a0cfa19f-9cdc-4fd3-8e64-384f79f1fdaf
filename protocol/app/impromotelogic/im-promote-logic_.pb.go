// Code generated by protoc-gen-go. DO NOT EDIT.
// source: impromotelogic/im-promote-logic_.proto

package impromotelogic // import "golang.52tt.com/protocol/app/impromotelogic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PushButtonType int32

const (
	PushButtonType_Default           PushButtonType = 0
	PushButtonType_CommentAndAt      PushButtonType = 1
	PushButtonType_FANS              PushButtonType = 2
	PushButtonType_Careful           PushButtonType = 3
	PushButtonType_LiveStart         PushButtonType = 4
	PushButtonType_Attitude          PushButtonType = 5
	PushButtonType_DiyRecommend      PushButtonType = 6
	PushButtonType_EnterRoomNotify   PushButtonType = 7
	PushButtonType_InviteRoom        PushButtonType = 8
	PushButtonType_PalOnlineNotify   PushButtonType = 9
	PushButtonType_AttitudeNotify    PushButtonType = 10
	PushButtonType_FastPcShowProcess PushButtonType = 11
)

var PushButtonType_name = map[int32]string{
	0:  "Default",
	1:  "CommentAndAt",
	2:  "FANS",
	3:  "Careful",
	4:  "LiveStart",
	5:  "Attitude",
	6:  "DiyRecommend",
	7:  "EnterRoomNotify",
	8:  "InviteRoom",
	9:  "PalOnlineNotify",
	10: "AttitudeNotify",
	11: "FastPcShowProcess",
}
var PushButtonType_value = map[string]int32{
	"Default":           0,
	"CommentAndAt":      1,
	"FANS":              2,
	"Careful":           3,
	"LiveStart":         4,
	"Attitude":          5,
	"DiyRecommend":      6,
	"EnterRoomNotify":   7,
	"InviteRoom":        8,
	"PalOnlineNotify":   9,
	"AttitudeNotify":    10,
	"FastPcShowProcess": 11,
}

func (x PushButtonType) String() string {
	return proto.EnumName(PushButtonType_name, int32(x))
}
func (PushButtonType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{0}
}

type OpenPageType int32

const (
	OpenPageType_MsgType     OpenPageType = 0
	OpenPageType_ConcealType OpenPageType = 1
)

var OpenPageType_name = map[int32]string{
	0: "MsgType",
	1: "ConcealType",
}
var OpenPageType_value = map[string]int32{
	"MsgType":     0,
	"ConcealType": 1,
}

func (x OpenPageType) String() string {
	return proto.EnumName(OpenPageType_name, int32(x))
}
func (OpenPageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{1}
}

// 给PushButtonInfo的 button_sub_type列出枚举
type PushButtonSubType int32

const (
	PushButtonSubType_PUSH_BUTTON_SUB_TYPE_UNSPECIFIED PushButtonSubType = 0
	PushButtonSubType_PUSH_BUTTON_SUB_TYPE_ALL         PushButtonSubType = 1
	PushButtonSubType_PUSH_BUTTON_SUB_TYPE_FANS        PushButtonSubType = 2
	PushButtonSubType_PUSH_BUTTON_SUB_TYPE_PARTNER     PushButtonSubType = 3
)

var PushButtonSubType_name = map[int32]string{
	0: "PUSH_BUTTON_SUB_TYPE_UNSPECIFIED",
	1: "PUSH_BUTTON_SUB_TYPE_ALL",
	2: "PUSH_BUTTON_SUB_TYPE_FANS",
	3: "PUSH_BUTTON_SUB_TYPE_PARTNER",
}
var PushButtonSubType_value = map[string]int32{
	"PUSH_BUTTON_SUB_TYPE_UNSPECIFIED": 0,
	"PUSH_BUTTON_SUB_TYPE_ALL":         1,
	"PUSH_BUTTON_SUB_TYPE_FANS":        2,
	"PUSH_BUTTON_SUB_TYPE_PARTNER":     3,
}

func (x PushButtonSubType) String() string {
	return proto.EnumName(PushButtonSubType_name, int32(x))
}
func (PushButtonSubType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{2}
}

// 一起玩客户端透传的消息结构
type ImPlayTogetherMsg struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CipherText           string   `protobuf:"bytes,2,opt,name=cipher_text,json=cipherText,proto3" json:"cipher_text,omitempty"`
	RoomType             int32    `protobuf:"varint,3,opt,name=room_type,json=roomType,proto3" json:"room_type,omitempty"`
	TimeOutEndTime       int64    `protobuf:"varint,4,opt,name=time_out_end_time,json=timeOutEndTime,proto3" json:"time_out_end_time,omitempty"`
	FollowUid            uint32   `protobuf:"varint,5,opt,name=follow_uid,json=followUid,proto3" json:"follow_uid,omitempty"`
	TabName              string   `protobuf:"bytes,6,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	TabLogoUrl           string   `protobuf:"bytes,7,opt,name=tab_logo_url,json=tabLogoUrl,proto3" json:"tab_logo_url,omitempty"`
	BgColor              string   `protobuf:"bytes,8,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImPlayTogetherMsg) Reset()         { *m = ImPlayTogetherMsg{} }
func (m *ImPlayTogetherMsg) String() string { return proto.CompactTextString(m) }
func (*ImPlayTogetherMsg) ProtoMessage()    {}
func (*ImPlayTogetherMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{0}
}
func (m *ImPlayTogetherMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImPlayTogetherMsg.Unmarshal(m, b)
}
func (m *ImPlayTogetherMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImPlayTogetherMsg.Marshal(b, m, deterministic)
}
func (dst *ImPlayTogetherMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImPlayTogetherMsg.Merge(dst, src)
}
func (m *ImPlayTogetherMsg) XXX_Size() int {
	return xxx_messageInfo_ImPlayTogetherMsg.Size(m)
}
func (m *ImPlayTogetherMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ImPlayTogetherMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ImPlayTogetherMsg proto.InternalMessageInfo

func (m *ImPlayTogetherMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ImPlayTogetherMsg) GetCipherText() string {
	if m != nil {
		return m.CipherText
	}
	return ""
}

func (m *ImPlayTogetherMsg) GetRoomType() int32 {
	if m != nil {
		return m.RoomType
	}
	return 0
}

func (m *ImPlayTogetherMsg) GetTimeOutEndTime() int64 {
	if m != nil {
		return m.TimeOutEndTime
	}
	return 0
}

func (m *ImPlayTogetherMsg) GetFollowUid() uint32 {
	if m != nil {
		return m.FollowUid
	}
	return 0
}

func (m *ImPlayTogetherMsg) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *ImPlayTogetherMsg) GetTabLogoUrl() string {
	if m != nil {
		return m.TabLogoUrl
	}
	return ""
}

func (m *ImPlayTogetherMsg) GetBgColor() string {
	if m != nil {
		return m.BgColor
	}
	return ""
}

type ImPromoteLogicReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ImPromoteLogicReq) Reset()         { *m = ImPromoteLogicReq{} }
func (m *ImPromoteLogicReq) String() string { return proto.CompactTextString(m) }
func (*ImPromoteLogicReq) ProtoMessage()    {}
func (*ImPromoteLogicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{1}
}
func (m *ImPromoteLogicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImPromoteLogicReq.Unmarshal(m, b)
}
func (m *ImPromoteLogicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImPromoteLogicReq.Marshal(b, m, deterministic)
}
func (dst *ImPromoteLogicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImPromoteLogicReq.Merge(dst, src)
}
func (m *ImPromoteLogicReq) XXX_Size() int {
	return xxx_messageInfo_ImPromoteLogicReq.Size(m)
}
func (m *ImPromoteLogicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ImPromoteLogicReq.DiscardUnknown(m)
}

var xxx_messageInfo_ImPromoteLogicReq proto.InternalMessageInfo

func (m *ImPromoteLogicReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type ImPromoteLogicResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ImPromoteLogicResp) Reset()         { *m = ImPromoteLogicResp{} }
func (m *ImPromoteLogicResp) String() string { return proto.CompactTextString(m) }
func (*ImPromoteLogicResp) ProtoMessage()    {}
func (*ImPromoteLogicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{2}
}
func (m *ImPromoteLogicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImPromoteLogicResp.Unmarshal(m, b)
}
func (m *ImPromoteLogicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImPromoteLogicResp.Marshal(b, m, deterministic)
}
func (dst *ImPromoteLogicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImPromoteLogicResp.Merge(dst, src)
}
func (m *ImPromoteLogicResp) XXX_Size() int {
	return xxx_messageInfo_ImPromoteLogicResp.Size(m)
}
func (m *ImPromoteLogicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ImPromoteLogicResp.DiscardUnknown(m)
}

var xxx_messageInfo_ImPromoteLogicResp proto.InternalMessageInfo

func (m *ImPromoteLogicResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取免密进房token
type GetImPromoteLogicTokenReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FollowAccount        string       `protobuf:"bytes,2,opt,name=follow_account,json=followAccount,proto3" json:"follow_account,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetImPromoteLogicTokenReq) Reset()         { *m = GetImPromoteLogicTokenReq{} }
func (m *GetImPromoteLogicTokenReq) String() string { return proto.CompactTextString(m) }
func (*GetImPromoteLogicTokenReq) ProtoMessage()    {}
func (*GetImPromoteLogicTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{3}
}
func (m *GetImPromoteLogicTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetImPromoteLogicTokenReq.Unmarshal(m, b)
}
func (m *GetImPromoteLogicTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetImPromoteLogicTokenReq.Marshal(b, m, deterministic)
}
func (dst *GetImPromoteLogicTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetImPromoteLogicTokenReq.Merge(dst, src)
}
func (m *GetImPromoteLogicTokenReq) XXX_Size() int {
	return xxx_messageInfo_GetImPromoteLogicTokenReq.Size(m)
}
func (m *GetImPromoteLogicTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetImPromoteLogicTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetImPromoteLogicTokenReq proto.InternalMessageInfo

func (m *GetImPromoteLogicTokenReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetImPromoteLogicTokenReq) GetFollowAccount() string {
	if m != nil {
		return m.FollowAccount
	}
	return ""
}

func (m *GetImPromoteLogicTokenReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetImPromoteLogicTokenResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Sign                 string        `protobuf:"bytes,2,opt,name=sign,proto3" json:"sign,omitempty"`
	TimeOut              int64         `protobuf:"varint,3,opt,name=time_out,json=timeOut,proto3" json:"time_out,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetImPromoteLogicTokenResp) Reset()         { *m = GetImPromoteLogicTokenResp{} }
func (m *GetImPromoteLogicTokenResp) String() string { return proto.CompactTextString(m) }
func (*GetImPromoteLogicTokenResp) ProtoMessage()    {}
func (*GetImPromoteLogicTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{4}
}
func (m *GetImPromoteLogicTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetImPromoteLogicTokenResp.Unmarshal(m, b)
}
func (m *GetImPromoteLogicTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetImPromoteLogicTokenResp.Marshal(b, m, deterministic)
}
func (dst *GetImPromoteLogicTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetImPromoteLogicTokenResp.Merge(dst, src)
}
func (m *GetImPromoteLogicTokenResp) XXX_Size() int {
	return xxx_messageInfo_GetImPromoteLogicTokenResp.Size(m)
}
func (m *GetImPromoteLogicTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetImPromoteLogicTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetImPromoteLogicTokenResp proto.InternalMessageInfo

func (m *GetImPromoteLogicTokenResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetImPromoteLogicTokenResp) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

func (m *GetImPromoteLogicTokenResp) GetTimeOut() int64 {
	if m != nil {
		return m.TimeOut
	}
	return 0
}

type GetPlayTogetherTabListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ToAccount            string       `protobuf:"bytes,2,opt,name=to_account,json=toAccount,proto3" json:"to_account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPlayTogetherTabListReq) Reset()         { *m = GetPlayTogetherTabListReq{} }
func (m *GetPlayTogetherTabListReq) String() string { return proto.CompactTextString(m) }
func (*GetPlayTogetherTabListReq) ProtoMessage()    {}
func (*GetPlayTogetherTabListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{5}
}
func (m *GetPlayTogetherTabListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayTogetherTabListReq.Unmarshal(m, b)
}
func (m *GetPlayTogetherTabListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayTogetherTabListReq.Marshal(b, m, deterministic)
}
func (dst *GetPlayTogetherTabListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayTogetherTabListReq.Merge(dst, src)
}
func (m *GetPlayTogetherTabListReq) XXX_Size() int {
	return xxx_messageInfo_GetPlayTogetherTabListReq.Size(m)
}
func (m *GetPlayTogetherTabListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayTogetherTabListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayTogetherTabListReq proto.InternalMessageInfo

func (m *GetPlayTogetherTabListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPlayTogetherTabListReq) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

type PlayTogetherTab struct {
	TagId                uint32   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TabType              uint32   `protobuf:"varint,2,opt,name=tab_type,json=tabType,proto3" json:"tab_type,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	ImageUri             string   `protobuf:"bytes,4,opt,name=image_uri,json=imageUri,proto3" json:"image_uri,omitempty"`
	NameColor            string   `protobuf:"bytes,5,opt,name=name_color,json=nameColor,proto3" json:"name_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlayTogetherTab) Reset()         { *m = PlayTogetherTab{} }
func (m *PlayTogetherTab) String() string { return proto.CompactTextString(m) }
func (*PlayTogetherTab) ProtoMessage()    {}
func (*PlayTogetherTab) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{6}
}
func (m *PlayTogetherTab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayTogetherTab.Unmarshal(m, b)
}
func (m *PlayTogetherTab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayTogetherTab.Marshal(b, m, deterministic)
}
func (dst *PlayTogetherTab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayTogetherTab.Merge(dst, src)
}
func (m *PlayTogetherTab) XXX_Size() int {
	return xxx_messageInfo_PlayTogetherTab.Size(m)
}
func (m *PlayTogetherTab) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayTogetherTab.DiscardUnknown(m)
}

var xxx_messageInfo_PlayTogetherTab proto.InternalMessageInfo

func (m *PlayTogetherTab) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *PlayTogetherTab) GetTabType() uint32 {
	if m != nil {
		return m.TabType
	}
	return 0
}

func (m *PlayTogetherTab) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PlayTogetherTab) GetImageUri() string {
	if m != nil {
		return m.ImageUri
	}
	return ""
}

func (m *PlayTogetherTab) GetNameColor() string {
	if m != nil {
		return m.NameColor
	}
	return ""
}

type GetPlayTogetherTabListResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	List                 []*PlayTogetherTab `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetPlayTogetherTabListResp) Reset()         { *m = GetPlayTogetherTabListResp{} }
func (m *GetPlayTogetherTabListResp) String() string { return proto.CompactTextString(m) }
func (*GetPlayTogetherTabListResp) ProtoMessage()    {}
func (*GetPlayTogetherTabListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{7}
}
func (m *GetPlayTogetherTabListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayTogetherTabListResp.Unmarshal(m, b)
}
func (m *GetPlayTogetherTabListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayTogetherTabListResp.Marshal(b, m, deterministic)
}
func (dst *GetPlayTogetherTabListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayTogetherTabListResp.Merge(dst, src)
}
func (m *GetPlayTogetherTabListResp) XXX_Size() int {
	return xxx_messageInfo_GetPlayTogetherTabListResp.Size(m)
}
func (m *GetPlayTogetherTabListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayTogetherTabListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayTogetherTabListResp proto.InternalMessageInfo

func (m *GetPlayTogetherTabListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPlayTogetherTabListResp) GetList() []*PlayTogetherTab {
	if m != nil {
		return m.List
	}
	return nil
}

type GetPlayTogetherMatchConfigReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPlayTogetherMatchConfigReq) Reset()         { *m = GetPlayTogetherMatchConfigReq{} }
func (m *GetPlayTogetherMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetPlayTogetherMatchConfigReq) ProtoMessage()    {}
func (*GetPlayTogetherMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{8}
}
func (m *GetPlayTogetherMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayTogetherMatchConfigReq.Unmarshal(m, b)
}
func (m *GetPlayTogetherMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayTogetherMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetPlayTogetherMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayTogetherMatchConfigReq.Merge(dst, src)
}
func (m *GetPlayTogetherMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetPlayTogetherMatchConfigReq.Size(m)
}
func (m *GetPlayTogetherMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayTogetherMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayTogetherMatchConfigReq proto.InternalMessageInfo

func (m *GetPlayTogetherMatchConfigReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type PlayTogetherMatchConfigItem struct {
	Tab *PlayTogetherTab `protobuf:"bytes,1,opt,name=tab,proto3" json:"tab,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	MatchItem            []string `protobuf:"bytes,2,rep,name=matchItem,proto3" json:"matchItem,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlayTogetherMatchConfigItem) Reset()         { *m = PlayTogetherMatchConfigItem{} }
func (m *PlayTogetherMatchConfigItem) String() string { return proto.CompactTextString(m) }
func (*PlayTogetherMatchConfigItem) ProtoMessage()    {}
func (*PlayTogetherMatchConfigItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{9}
}
func (m *PlayTogetherMatchConfigItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayTogetherMatchConfigItem.Unmarshal(m, b)
}
func (m *PlayTogetherMatchConfigItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayTogetherMatchConfigItem.Marshal(b, m, deterministic)
}
func (dst *PlayTogetherMatchConfigItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayTogetherMatchConfigItem.Merge(dst, src)
}
func (m *PlayTogetherMatchConfigItem) XXX_Size() int {
	return xxx_messageInfo_PlayTogetherMatchConfigItem.Size(m)
}
func (m *PlayTogetherMatchConfigItem) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayTogetherMatchConfigItem.DiscardUnknown(m)
}

var xxx_messageInfo_PlayTogetherMatchConfigItem proto.InternalMessageInfo

func (m *PlayTogetherMatchConfigItem) GetTab() *PlayTogetherTab {
	if m != nil {
		return m.Tab
	}
	return nil
}

func (m *PlayTogetherMatchConfigItem) GetMatchItem() []string {
	if m != nil {
		return m.MatchItem
	}
	return nil
}

type GetPlayTogetherMatchConfigResp struct {
	BaseResp             *app.BaseResp                  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ConfigList           []*PlayTogetherMatchConfigItem `protobuf:"bytes,2,rep,name=config_list,json=configList,proto3" json:"config_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *GetPlayTogetherMatchConfigResp) Reset()         { *m = GetPlayTogetherMatchConfigResp{} }
func (m *GetPlayTogetherMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetPlayTogetherMatchConfigResp) ProtoMessage()    {}
func (*GetPlayTogetherMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{10}
}
func (m *GetPlayTogetherMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayTogetherMatchConfigResp.Unmarshal(m, b)
}
func (m *GetPlayTogetherMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayTogetherMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetPlayTogetherMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayTogetherMatchConfigResp.Merge(dst, src)
}
func (m *GetPlayTogetherMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetPlayTogetherMatchConfigResp.Size(m)
}
func (m *GetPlayTogetherMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayTogetherMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayTogetherMatchConfigResp proto.InternalMessageInfo

func (m *GetPlayTogetherMatchConfigResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPlayTogetherMatchConfigResp) GetConfigList() []*PlayTogetherMatchConfigItem {
	if m != nil {
		return m.ConfigList
	}
	return nil
}

type CueConfig struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TypeName             string   `protobuf:"bytes,2,opt,name=type_name,json=typeName,proto3" json:"type_name,omitempty"`
	EntrancePic          string   `protobuf:"bytes,3,opt,name=entrance_pic,json=entrancePic,proto3" json:"entrance_pic,omitempty"`
	GuideTextFrom        string   `protobuf:"bytes,4,opt,name=guide_text_from,json=guideTextFrom,proto3" json:"guide_text_from,omitempty"`
	GuideTextTo          string   `protobuf:"bytes,5,opt,name=guide_text_to,json=guideTextTo,proto3" json:"guide_text_to,omitempty"`
	Lottie               string   `protobuf:"bytes,6,opt,name=lottie,proto3" json:"lottie,omitempty"`
	MsgPicLeft           string   `protobuf:"bytes,7,opt,name=msg_pic_left,json=msgPicLeft,proto3" json:"msg_pic_left,omitempty"`
	MsgPicRight          string   `protobuf:"bytes,8,opt,name=msg_pic_right,json=msgPicRight,proto3" json:"msg_pic_right,omitempty"`
	PreviewMsg           string   `protobuf:"bytes,9,opt,name=preview_msg,json=previewMsg,proto3" json:"preview_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CueConfig) Reset()         { *m = CueConfig{} }
func (m *CueConfig) String() string { return proto.CompactTextString(m) }
func (*CueConfig) ProtoMessage()    {}
func (*CueConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{11}
}
func (m *CueConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CueConfig.Unmarshal(m, b)
}
func (m *CueConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CueConfig.Marshal(b, m, deterministic)
}
func (dst *CueConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CueConfig.Merge(dst, src)
}
func (m *CueConfig) XXX_Size() int {
	return xxx_messageInfo_CueConfig.Size(m)
}
func (m *CueConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_CueConfig.DiscardUnknown(m)
}

var xxx_messageInfo_CueConfig proto.InternalMessageInfo

func (m *CueConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CueConfig) GetTypeName() string {
	if m != nil {
		return m.TypeName
	}
	return ""
}

func (m *CueConfig) GetEntrancePic() string {
	if m != nil {
		return m.EntrancePic
	}
	return ""
}

func (m *CueConfig) GetGuideTextFrom() string {
	if m != nil {
		return m.GuideTextFrom
	}
	return ""
}

func (m *CueConfig) GetGuideTextTo() string {
	if m != nil {
		return m.GuideTextTo
	}
	return ""
}

func (m *CueConfig) GetLottie() string {
	if m != nil {
		return m.Lottie
	}
	return ""
}

func (m *CueConfig) GetMsgPicLeft() string {
	if m != nil {
		return m.MsgPicLeft
	}
	return ""
}

func (m *CueConfig) GetMsgPicRight() string {
	if m != nil {
		return m.MsgPicRight
	}
	return ""
}

func (m *CueConfig) GetPreviewMsg() string {
	if m != nil {
		return m.PreviewMsg
	}
	return ""
}

type GetCueConfigReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCueConfigReq) Reset()         { *m = GetCueConfigReq{} }
func (m *GetCueConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetCueConfigReq) ProtoMessage()    {}
func (*GetCueConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{12}
}
func (m *GetCueConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCueConfigReq.Unmarshal(m, b)
}
func (m *GetCueConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCueConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetCueConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCueConfigReq.Merge(dst, src)
}
func (m *GetCueConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetCueConfigReq.Size(m)
}
func (m *GetCueConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCueConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCueConfigReq proto.InternalMessageInfo

func (m *GetCueConfigReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetCueConfigResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Conf                 []*CueConfig  `protobuf:"bytes,2,rep,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetCueConfigResp) Reset()         { *m = GetCueConfigResp{} }
func (m *GetCueConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetCueConfigResp) ProtoMessage()    {}
func (*GetCueConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{13}
}
func (m *GetCueConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCueConfigResp.Unmarshal(m, b)
}
func (m *GetCueConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCueConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetCueConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCueConfigResp.Merge(dst, src)
}
func (m *GetCueConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetCueConfigResp.Size(m)
}
func (m *GetCueConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCueConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCueConfigResp proto.InternalMessageInfo

func (m *GetCueConfigResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCueConfigResp) GetConf() []*CueConfig {
	if m != nil {
		return m.Conf
	}
	return nil
}

type PushButtonInfo struct {
	ButtonType PushButtonType `protobuf:"varint,1,opt,name=button_type,json=buttonType,proto3,enum=ga.impromotelogic.PushButtonType" json:"button_type,omitempty"`
	On         bool           `protobuf:"varint,2,opt,name=on,proto3" json:"on,omitempty"`
	Desc       string         `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	SecondDesc string         `protobuf:"bytes,4,opt,name=second_desc,json=secondDesc,proto3" json:"second_desc,omitempty"`
	// Types that are valid to be assigned to SubType:
	//	*PushButtonInfo_FastPcShowProcess
	SubType isPushButtonInfo_SubType `protobuf_oneof:"sub_type"`
	// 根据不同的button_type，sub_type有不同的含义, see PushButtonSubType
	ButtonSubType        uint32   `protobuf:"varint,6,opt,name=button_sub_type,json=buttonSubType,proto3" json:"button_sub_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushButtonInfo) Reset()         { *m = PushButtonInfo{} }
func (m *PushButtonInfo) String() string { return proto.CompactTextString(m) }
func (*PushButtonInfo) ProtoMessage()    {}
func (*PushButtonInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{14}
}
func (m *PushButtonInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushButtonInfo.Unmarshal(m, b)
}
func (m *PushButtonInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushButtonInfo.Marshal(b, m, deterministic)
}
func (dst *PushButtonInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushButtonInfo.Merge(dst, src)
}
func (m *PushButtonInfo) XXX_Size() int {
	return xxx_messageInfo_PushButtonInfo.Size(m)
}
func (m *PushButtonInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PushButtonInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PushButtonInfo proto.InternalMessageInfo

func (m *PushButtonInfo) GetButtonType() PushButtonType {
	if m != nil {
		return m.ButtonType
	}
	return PushButtonType_Default
}

func (m *PushButtonInfo) GetOn() bool {
	if m != nil {
		return m.On
	}
	return false
}

func (m *PushButtonInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *PushButtonInfo) GetSecondDesc() string {
	if m != nil {
		return m.SecondDesc
	}
	return ""
}

type isPushButtonInfo_SubType interface {
	isPushButtonInfo_SubType()
}

type PushButtonInfo_FastPcShowProcess struct {
	FastPcShowProcess uint32 `protobuf:"varint,5,opt,name=fast_pc_show_process,json=fastPcShowProcess,proto3,oneof"`
}

func (*PushButtonInfo_FastPcShowProcess) isPushButtonInfo_SubType() {}

func (m *PushButtonInfo) GetSubType() isPushButtonInfo_SubType {
	if m != nil {
		return m.SubType
	}
	return nil
}

func (m *PushButtonInfo) GetFastPcShowProcess() uint32 {
	if x, ok := m.GetSubType().(*PushButtonInfo_FastPcShowProcess); ok {
		return x.FastPcShowProcess
	}
	return 0
}

func (m *PushButtonInfo) GetButtonSubType() uint32 {
	if m != nil {
		return m.ButtonSubType
	}
	return 0
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*PushButtonInfo) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _PushButtonInfo_OneofMarshaler, _PushButtonInfo_OneofUnmarshaler, _PushButtonInfo_OneofSizer, []interface{}{
		(*PushButtonInfo_FastPcShowProcess)(nil),
	}
}

func _PushButtonInfo_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*PushButtonInfo)
	// sub_type
	switch x := m.SubType.(type) {
	case *PushButtonInfo_FastPcShowProcess:
		b.EncodeVarint(5<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.FastPcShowProcess))
	case nil:
	default:
		return fmt.Errorf("PushButtonInfo.SubType has unexpected type %T", x)
	}
	return nil
}

func _PushButtonInfo_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*PushButtonInfo)
	switch tag {
	case 5: // sub_type.fast_pc_show_process
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.SubType = &PushButtonInfo_FastPcShowProcess{uint32(x)}
		return true, err
	default:
		return false, nil
	}
}

func _PushButtonInfo_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*PushButtonInfo)
	// sub_type
	switch x := m.SubType.(type) {
	case *PushButtonInfo_FastPcShowProcess:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(x.FastPcShowProcess))
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type PushButtonsReq struct {
	BaseReq              *app.BaseReq      `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PushButtonInfos      []*PushButtonInfo `protobuf:"bytes,2,rep,name=push_button_infos,json=pushButtonInfos,proto3" json:"push_button_infos,omitempty"`
	PageType             OpenPageType      `protobuf:"varint,3,opt,name=page_type,json=pageType,proto3,enum=ga.impromotelogic.OpenPageType" json:"page_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PushButtonsReq) Reset()         { *m = PushButtonsReq{} }
func (m *PushButtonsReq) String() string { return proto.CompactTextString(m) }
func (*PushButtonsReq) ProtoMessage()    {}
func (*PushButtonsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{15}
}
func (m *PushButtonsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushButtonsReq.Unmarshal(m, b)
}
func (m *PushButtonsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushButtonsReq.Marshal(b, m, deterministic)
}
func (dst *PushButtonsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushButtonsReq.Merge(dst, src)
}
func (m *PushButtonsReq) XXX_Size() int {
	return xxx_messageInfo_PushButtonsReq.Size(m)
}
func (m *PushButtonsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushButtonsReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushButtonsReq proto.InternalMessageInfo

func (m *PushButtonsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PushButtonsReq) GetPushButtonInfos() []*PushButtonInfo {
	if m != nil {
		return m.PushButtonInfos
	}
	return nil
}

func (m *PushButtonsReq) GetPageType() OpenPageType {
	if m != nil {
		return m.PageType
	}
	return OpenPageType_MsgType
}

type PushButtonsRsp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PushButtonsRsp) Reset()         { *m = PushButtonsRsp{} }
func (m *PushButtonsRsp) String() string { return proto.CompactTextString(m) }
func (*PushButtonsRsp) ProtoMessage()    {}
func (*PushButtonsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{16}
}
func (m *PushButtonsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushButtonsRsp.Unmarshal(m, b)
}
func (m *PushButtonsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushButtonsRsp.Marshal(b, m, deterministic)
}
func (dst *PushButtonsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushButtonsRsp.Merge(dst, src)
}
func (m *PushButtonsRsp) XXX_Size() int {
	return xxx_messageInfo_PushButtonsRsp.Size(m)
}
func (m *PushButtonsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushButtonsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PushButtonsRsp proto.InternalMessageInfo

func (m *PushButtonsRsp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetPushButtonsReq struct {
	BaseReq              *app.BaseReq     `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PageType             OpenPageType     `protobuf:"varint,2,opt,name=page_type,json=pageType,proto3,enum=ga.impromotelogic.OpenPageType" json:"page_type,omitempty"`
	Buttons              []PushButtonType `protobuf:"varint,3,rep,packed,name=buttons,proto3,enum=ga.impromotelogic.PushButtonType" json:"buttons,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetPushButtonsReq) Reset()         { *m = GetPushButtonsReq{} }
func (m *GetPushButtonsReq) String() string { return proto.CompactTextString(m) }
func (*GetPushButtonsReq) ProtoMessage()    {}
func (*GetPushButtonsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{17}
}
func (m *GetPushButtonsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPushButtonsReq.Unmarshal(m, b)
}
func (m *GetPushButtonsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPushButtonsReq.Marshal(b, m, deterministic)
}
func (dst *GetPushButtonsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPushButtonsReq.Merge(dst, src)
}
func (m *GetPushButtonsReq) XXX_Size() int {
	return xxx_messageInfo_GetPushButtonsReq.Size(m)
}
func (m *GetPushButtonsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPushButtonsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPushButtonsReq proto.InternalMessageInfo

func (m *GetPushButtonsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPushButtonsReq) GetPageType() OpenPageType {
	if m != nil {
		return m.PageType
	}
	return OpenPageType_MsgType
}

func (m *GetPushButtonsReq) GetButtons() []PushButtonType {
	if m != nil {
		return m.Buttons
	}
	return nil
}

type GetPushButtonsRsp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PushButtonInfos      []*PushButtonInfo `protobuf:"bytes,2,rep,name=push_button_infos,json=pushButtonInfos,proto3" json:"push_button_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPushButtonsRsp) Reset()         { *m = GetPushButtonsRsp{} }
func (m *GetPushButtonsRsp) String() string { return proto.CompactTextString(m) }
func (*GetPushButtonsRsp) ProtoMessage()    {}
func (*GetPushButtonsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__60afc0820a918216, []int{18}
}
func (m *GetPushButtonsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPushButtonsRsp.Unmarshal(m, b)
}
func (m *GetPushButtonsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPushButtonsRsp.Marshal(b, m, deterministic)
}
func (dst *GetPushButtonsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPushButtonsRsp.Merge(dst, src)
}
func (m *GetPushButtonsRsp) XXX_Size() int {
	return xxx_messageInfo_GetPushButtonsRsp.Size(m)
}
func (m *GetPushButtonsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPushButtonsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPushButtonsRsp proto.InternalMessageInfo

func (m *GetPushButtonsRsp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPushButtonsRsp) GetPushButtonInfos() []*PushButtonInfo {
	if m != nil {
		return m.PushButtonInfos
	}
	return nil
}

func init() {
	proto.RegisterType((*ImPlayTogetherMsg)(nil), "ga.impromotelogic.ImPlayTogetherMsg")
	proto.RegisterType((*ImPromoteLogicReq)(nil), "ga.impromotelogic.ImPromoteLogicReq")
	proto.RegisterType((*ImPromoteLogicResp)(nil), "ga.impromotelogic.ImPromoteLogicResp")
	proto.RegisterType((*GetImPromoteLogicTokenReq)(nil), "ga.impromotelogic.GetImPromoteLogicTokenReq")
	proto.RegisterType((*GetImPromoteLogicTokenResp)(nil), "ga.impromotelogic.GetImPromoteLogicTokenResp")
	proto.RegisterType((*GetPlayTogetherTabListReq)(nil), "ga.impromotelogic.GetPlayTogetherTabListReq")
	proto.RegisterType((*PlayTogetherTab)(nil), "ga.impromotelogic.PlayTogetherTab")
	proto.RegisterType((*GetPlayTogetherTabListResp)(nil), "ga.impromotelogic.GetPlayTogetherTabListResp")
	proto.RegisterType((*GetPlayTogetherMatchConfigReq)(nil), "ga.impromotelogic.GetPlayTogetherMatchConfigReq")
	proto.RegisterType((*PlayTogetherMatchConfigItem)(nil), "ga.impromotelogic.PlayTogetherMatchConfigItem")
	proto.RegisterType((*GetPlayTogetherMatchConfigResp)(nil), "ga.impromotelogic.GetPlayTogetherMatchConfigResp")
	proto.RegisterType((*CueConfig)(nil), "ga.impromotelogic.CueConfig")
	proto.RegisterType((*GetCueConfigReq)(nil), "ga.impromotelogic.GetCueConfigReq")
	proto.RegisterType((*GetCueConfigResp)(nil), "ga.impromotelogic.GetCueConfigResp")
	proto.RegisterType((*PushButtonInfo)(nil), "ga.impromotelogic.PushButtonInfo")
	proto.RegisterType((*PushButtonsReq)(nil), "ga.impromotelogic.PushButtonsReq")
	proto.RegisterType((*PushButtonsRsp)(nil), "ga.impromotelogic.PushButtonsRsp")
	proto.RegisterType((*GetPushButtonsReq)(nil), "ga.impromotelogic.GetPushButtonsReq")
	proto.RegisterType((*GetPushButtonsRsp)(nil), "ga.impromotelogic.GetPushButtonsRsp")
	proto.RegisterEnum("ga.impromotelogic.PushButtonType", PushButtonType_name, PushButtonType_value)
	proto.RegisterEnum("ga.impromotelogic.OpenPageType", OpenPageType_name, OpenPageType_value)
	proto.RegisterEnum("ga.impromotelogic.PushButtonSubType", PushButtonSubType_name, PushButtonSubType_value)
}

func init() {
	proto.RegisterFile("impromotelogic/im-promote-logic_.proto", fileDescriptor_im_promote_logic__60afc0820a918216)
}

var fileDescriptor_im_promote_logic__60afc0820a918216 = []byte{
	// 1334 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x57, 0xcb, 0x6e, 0xdb, 0x46,
	0x17, 0x36, 0x25, 0x5f, 0xa4, 0x23, 0x5b, 0xa6, 0xe6, 0xff, 0xf3, 0x43, 0x49, 0xec, 0x3f, 0x0a,
	0xd1, 0x06, 0x4e, 0xda, 0xc8, 0x85, 0xdb, 0x14, 0x28, 0x52, 0xa0, 0xb0, 0x7c, 0x8b, 0x00, 0x5f,
	0x04, 0x5a, 0x5e, 0xb4, 0x9b, 0xc1, 0x90, 0x1c, 0x51, 0x83, 0x92, 0x1c, 0x9a, 0x1c, 0xda, 0xf1,
	0xaa, 0x9b, 0x6e, 0xba, 0xe8, 0xa6, 0xdd, 0x74, 0xd1, 0x75, 0x1f, 0xa1, 0x6f, 0xd0, 0x97, 0xe8,
	0xd3, 0x14, 0x73, 0x91, 0x6c, 0x29, 0x4a, 0x1a, 0x15, 0xdd, 0xcd, 0x7c, 0x73, 0xe6, 0xcc, 0x77,
	0xbe, 0xf3, 0xf1, 0xc8, 0x86, 0x27, 0x2c, 0x4e, 0x33, 0x1e, 0x73, 0x41, 0x23, 0x1e, 0x32, 0x7f,
	0x9b, 0xc5, 0xcf, 0xcd, 0xfe, 0xb9, 0x02, 0x70, 0x3b, 0xcd, 0xb8, 0xe0, 0xa8, 0x11, 0x92, 0xf6,
	0x64, 0xe8, 0x83, 0xb5, 0x90, 0x60, 0x8f, 0xe4, 0x54, 0x47, 0x38, 0x3f, 0x95, 0xa0, 0xd1, 0x8d,
	0x7b, 0x11, 0xb9, 0xe9, 0xf3, 0x90, 0x8a, 0x21, 0xcd, 0x4e, 0xf2, 0x10, 0x6d, 0x02, 0xf8, 0x43,
	0x92, 0x24, 0x34, 0xc2, 0x2c, 0x68, 0x5a, 0x2d, 0x6b, 0x6b, 0xcd, 0xad, 0x1a, 0xa4, 0x1b, 0xa0,
	0x47, 0x50, 0xf3, 0x59, 0x3a, 0xa4, 0x19, 0x16, 0xf4, 0xb5, 0x68, 0x96, 0x5a, 0xd6, 0x56, 0xd5,
	0x05, 0x0d, 0xf5, 0xe9, 0x6b, 0x81, 0x1e, 0x42, 0x35, 0xe3, 0x3c, 0xc6, 0xe2, 0x26, 0xa5, 0xcd,
	0x72, 0xcb, 0xda, 0x5a, 0x72, 0x2b, 0x12, 0xe8, 0xdf, 0xa4, 0x14, 0x3d, 0x85, 0x86, 0x60, 0x31,
	0xc5, 0xbc, 0x10, 0x98, 0x26, 0x01, 0x96, 0x9b, 0xe6, 0x62, 0xcb, 0xda, 0x2a, 0xbb, 0x75, 0xb9,
	0x3e, 0x2b, 0xc4, 0x41, 0x12, 0xf4, 0x59, 0x4c, 0x25, 0x8f, 0x01, 0x8f, 0x22, 0x7e, 0x8d, 0x0b,
	0x16, 0x34, 0x97, 0x34, 0x0f, 0x8d, 0x5c, 0xb0, 0x00, 0xdd, 0x87, 0x8a, 0x20, 0x1e, 0x4e, 0x48,
	0x4c, 0x9b, 0xcb, 0x8a, 0xc4, 0x8a, 0x20, 0xde, 0x29, 0x89, 0x29, 0x6a, 0xc1, 0xaa, 0x3c, 0x8a,
	0x78, 0xc8, 0x71, 0x91, 0x45, 0xcd, 0x15, 0xcd, 0x51, 0x10, 0xef, 0x98, 0x87, 0xfc, 0x22, 0x8b,
	0xe4, 0x65, 0x2f, 0xc4, 0x3e, 0x8f, 0x78, 0xd6, 0xac, 0xe8, 0xcb, 0x5e, 0xb8, 0x27, 0xb7, 0xce,
	0x4b, 0xa5, 0x89, 0x56, 0xed, 0x58, 0xaa, 0xe6, 0xd2, 0x4b, 0xf4, 0x04, 0x2a, 0x52, 0x37, 0x9c,
	0xd1, 0x4b, 0xa5, 0x48, 0x6d, 0xa7, 0xd6, 0x0e, 0x49, 0xbb, 0x43, 0x72, 0xea, 0xd2, 0x4b, 0x77,
	0xc5, 0xd3, 0x0b, 0xe7, 0x2b, 0x40, 0xd3, 0x97, 0xf3, 0x14, 0x3d, 0x85, 0xaa, 0xb9, 0x9d, 0xa7,
	0xe6, 0xfa, 0xea, 0xed, 0xf5, 0x3c, 0x75, 0x2b, 0x9e, 0x59, 0x39, 0x3f, 0x58, 0x70, 0xff, 0x88,
	0x8a, 0xc9, 0x24, 0x7d, 0xfe, 0x2d, 0x4d, 0xe6, 0xa0, 0x81, 0x3e, 0x84, 0xba, 0x91, 0x8e, 0xf8,
	0x3e, 0x2f, 0x92, 0x51, 0x9b, 0xd6, 0x34, 0xba, 0xab, 0xc1, 0xa9, 0x4e, 0x97, 0xa7, 0x3a, 0xed,
	0x5c, 0xc1, 0x83, 0xb7, 0x51, 0x99, 0xab, 0x28, 0x84, 0x60, 0x31, 0x67, 0x61, 0x62, 0x48, 0xa8,
	0xb5, 0x6a, 0x9f, 0x31, 0x82, 0x7a, 0xb9, 0xec, 0xae, 0x98, 0xfe, 0x3b, 0x9e, 0x92, 0xe0, 0xae,
	0x2d, 0xfb, 0xc4, 0x3b, 0x66, 0xb9, 0x98, 0x47, 0x82, 0x4d, 0x00, 0xc1, 0xa7, 0xca, 0xaf, 0x0a,
	0x6e, 0x4a, 0x77, 0x7e, 0xb6, 0x60, 0x7d, 0xea, 0x05, 0x74, 0x0f, 0x96, 0x05, 0x09, 0x6f, 0x4d,
	0xbf, 0x24, 0x48, 0xd8, 0x1d, 0x1b, 0x4d, 0xd9, 0xb9, 0xa4, 0x0e, 0xa4, 0xd1, 0x94, 0x9b, 0x11,
	0x2c, 0x2a, 0xff, 0x95, 0x75, 0x61, 0x72, 0x2d, 0xed, 0xcf, 0x62, 0x12, 0x52, 0x5c, 0x64, 0x4c,
	0x39, 0xbb, 0xea, 0x56, 0x14, 0x70, 0x91, 0x31, 0xc9, 0x4a, 0x06, 0x19, 0xe7, 0x2d, 0x69, 0x56,
	0x12, 0xd1, 0xde, 0xfb, 0x4e, 0x29, 0x3e, 0xb3, 0xf2, 0xf9, 0x14, 0xff, 0x1c, 0x16, 0x23, 0x96,
	0xcb, 0xba, 0xcb, 0x5b, 0xb5, 0x1d, 0xa7, 0xfd, 0xc6, 0x28, 0x68, 0x4f, 0x3d, 0xe2, 0xaa, 0x78,
	0xe7, 0x08, 0x36, 0xa7, 0x08, 0x9c, 0x10, 0xe1, 0x0f, 0xf7, 0x78, 0x32, 0x60, 0xe1, 0x3c, 0x1f,
	0xc2, 0x25, 0x3c, 0x7c, 0x4b, 0x96, 0xae, 0xa0, 0x31, 0xfa, 0x0c, 0xca, 0x82, 0x78, 0x26, 0xc3,
	0xfb, 0xd0, 0x93, 0xe1, 0x68, 0x03, 0xaa, 0xb1, 0x4c, 0x24, 0x53, 0xa8, 0xd2, 0xaa, 0xee, 0x2d,
	0xe0, 0xfc, 0x6a, 0xc1, 0xff, 0xdf, 0x45, 0x7e, 0x3e, 0x05, 0xcf, 0xa0, 0xe6, 0xab, 0x8b, 0xf8,
	0x8e, 0x90, 0xed, 0xbf, 0x61, 0x3a, 0x55, 0xa6, 0x0b, 0x3a, 0x85, 0xec, 0xa0, 0xf3, 0x5b, 0x09,
	0xaa, 0x7b, 0x05, 0xd5, 0xa7, 0xa8, 0x0e, 0xa5, 0xb1, 0xcf, 0x4a, 0x2c, 0x90, 0xae, 0x91, 0x06,
	0xd3, 0xe3, 0x4c, 0xbb, 0xb5, 0x22, 0x01, 0x35, 0xcf, 0x1e, 0xc3, 0x2a, 0x4d, 0x44, 0x46, 0x12,
	0x9f, 0xe2, 0x94, 0xf9, 0xc6, 0x6e, 0xb5, 0x11, 0xd6, 0x63, 0x3e, 0x7a, 0x02, 0xeb, 0x61, 0xc1,
	0x02, 0xaa, 0x86, 0x32, 0x1e, 0x64, 0x3c, 0x36, 0xde, 0x5b, 0x53, 0xb0, 0x1c, 0xcc, 0x87, 0x19,
	0x8f, 0x91, 0x03, 0x6b, 0x77, 0xe2, 0x04, 0x37, 0x1e, 0xac, 0x8d, 0xa3, 0xfa, 0x1c, 0xfd, 0x0f,
	0x96, 0x23, 0x2e, 0x04, 0x1b, 0xcd, 0x55, 0xb3, 0x93, 0x63, 0x35, 0xce, 0x43, 0xc9, 0x00, 0x47,
	0x74, 0x20, 0x46, 0x63, 0x35, 0xce, 0xc3, 0x1e, 0xf3, 0x8f, 0xe9, 0x40, 0xc8, 0xec, 0xa3, 0x88,
	0x8c, 0x85, 0x43, 0x61, 0x66, 0x6b, 0x4d, 0x87, 0xb8, 0x12, 0x92, 0xbf, 0x1f, 0x69, 0x46, 0xaf,
	0x18, 0xbd, 0xc6, 0x71, 0x1e, 0x36, 0xab, 0x3a, 0x89, 0x81, 0x4e, 0xf2, 0xd0, 0xf9, 0x02, 0xd6,
	0x8f, 0xa8, 0x18, 0x4b, 0x35, 0x8f, 0xeb, 0x38, 0xd8, 0x93, 0x57, 0xe7, 0xeb, 0xf9, 0x27, 0xb0,
	0x28, 0x1b, 0x66, 0x9a, 0xbd, 0x31, 0xa3, 0xd9, 0xb7, 0xa9, 0x55, 0xa4, 0xf3, 0x7d, 0x09, 0xea,
	0xbd, 0x22, 0x1f, 0x76, 0x0a, 0x21, 0x78, 0xd2, 0x4d, 0x06, 0x1c, 0x75, 0xa0, 0xe6, 0xa9, 0x9d,
	0x9e, 0x18, 0xf2, 0xc5, 0xfa, 0xce, 0xe3, 0x59, 0xc6, 0x19, 0xdf, 0x93, 0xb3, 0xc4, 0x05, 0x6f,
	0xbc, 0x96, 0xee, 0xe0, 0x7a, 0x5c, 0x56, 0xdc, 0x12, 0x4f, 0xe4, 0x9c, 0x09, 0x68, 0x3e, 0x6a,
	0xbc, 0x5a, 0x4b, 0x1d, 0x73, 0xea, 0xf3, 0x24, 0xc0, 0xea, 0x48, 0x77, 0x1b, 0x34, 0xb4, 0x2f,
	0x03, 0x5e, 0xc0, 0x7f, 0x07, 0x24, 0x17, 0x38, 0xf5, 0x71, 0x3e, 0xe4, 0xd7, 0x38, 0xcd, 0xb8,
	0x4f, 0xf3, 0x5c, 0xff, 0x92, 0x76, 0x4a, 0x4d, 0xeb, 0xd5, 0x82, 0xdb, 0x90, 0x11, 0x3d, 0xff,
	0x7c, 0xc8, 0xaf, 0x7b, 0xfa, 0x58, 0x3a, 0xc9, 0xf0, 0xcf, 0x0b, 0x33, 0xf5, 0x96, 0x95, 0x4d,
	0xd7, 0x34, 0x7c, 0x5e, 0xa8, 0xd9, 0xd7, 0x01, 0xa8, 0x8c, 0x02, 0x9c, 0x3f, 0xac, 0xbb, 0x32,
	0xe4, 0xf3, 0xcc, 0xe9, 0x13, 0x68, 0xa4, 0x45, 0x3e, 0xc4, 0xe6, 0x4d, 0x96, 0x0c, 0x78, 0x6e,
	0x1a, 0xf0, 0x6e, 0xd1, 0xa4, 0xd8, 0xee, 0x7a, 0x3a, 0xb1, 0xcf, 0xd1, 0x97, 0x50, 0x4d, 0xe5,
	0xf0, 0x1d, 0xff, 0xf1, 0x51, 0xdf, 0x79, 0x34, 0x23, 0xcd, 0x59, 0x4a, 0x93, 0x1e, 0x09, 0xa9,
	0x52, 0xbe, 0x92, 0x9a, 0x95, 0xf3, 0x72, 0xb2, 0x8c, 0xf9, 0x7e, 0xba, 0x7f, 0xb7, 0xa0, 0x21,
	0xe7, 0xcf, 0x3f, 0xd3, 0x61, 0x82, 0x78, 0x69, 0x4e, 0xe2, 0xe8, 0x25, 0xac, 0x68, 0x01, 0xf3,
	0x66, 0xb9, 0x55, 0x7e, 0x3f, 0xc3, 0x8d, 0x6e, 0x38, 0x3f, 0xbe, 0x49, 0x7c, 0xbe, 0xef, 0xe6,
	0xdf, 0xed, 0xe1, 0xb3, 0x3f, 0x27, 0xdc, 0xa4, 0xea, 0xab, 0xc1, 0xca, 0x3e, 0x1d, 0x90, 0x22,
	0x12, 0xf6, 0x02, 0xb2, 0x61, 0x75, 0x8f, 0xc7, 0x31, 0x4d, 0xc4, 0x6e, 0x12, 0xec, 0x0a, 0xdb,
	0x42, 0x15, 0x58, 0x3c, 0xdc, 0x3d, 0x3d, 0xb7, 0x4b, 0x32, 0x70, 0x8f, 0x64, 0x74, 0x50, 0x44,
	0x76, 0x19, 0xad, 0x41, 0xf5, 0x98, 0x5d, 0xd1, 0x73, 0x41, 0x32, 0x61, 0x2f, 0xa2, 0x55, 0xa8,
	0xec, 0x0a, 0xc1, 0x44, 0x11, 0x50, 0x7b, 0x49, 0x66, 0xd9, 0x67, 0x37, 0x2e, 0xf5, 0x55, 0xaa,
	0xc0, 0x5e, 0x46, 0xff, 0x81, 0xf5, 0x83, 0x44, 0xd0, 0xcc, 0xe5, 0x3c, 0x3e, 0xe5, 0x82, 0x0d,
	0x6e, 0xec, 0x15, 0x54, 0x07, 0xe8, 0x26, 0x57, 0x4c, 0x50, 0x89, 0xda, 0x15, 0x19, 0xd4, 0x23,
	0xd1, 0x59, 0x12, 0xb1, 0x84, 0x9a, 0xa0, 0x2a, 0x42, 0x50, 0x1f, 0x65, 0x36, 0x18, 0xa0, 0x7b,
	0xd0, 0x38, 0x9c, 0xfe, 0xb8, 0xec, 0xda, 0xb3, 0x8f, 0x61, 0xf5, 0x6e, 0x0f, 0x25, 0xe1, 0x93,
	0x3c, 0x94, 0x4b, 0x7b, 0x01, 0xad, 0x43, 0x6d, 0x8f, 0x27, 0x3e, 0x25, 0x91, 0x02, 0xac, 0x67,
	0xbf, 0x58, 0xd0, 0xb8, 0x95, 0xc2, 0x7c, 0x7a, 0xe8, 0x03, 0x68, 0xf5, 0x2e, 0xce, 0x5f, 0xe1,
	0xce, 0x45, 0xbf, 0x7f, 0x76, 0x8a, 0xcf, 0x2f, 0x3a, 0xb8, 0xff, 0x75, 0xef, 0x00, 0x5f, 0x9c,
	0x9e, 0xf7, 0x0e, 0xf6, 0xba, 0x87, 0xdd, 0x83, 0x7d, 0x7b, 0x01, 0x6d, 0x40, 0x73, 0x66, 0xd4,
	0xee, 0xf1, 0xb1, 0x6d, 0xa1, 0x4d, 0xb8, 0x3f, 0xf3, 0xd4, 0xe8, 0xd8, 0x82, 0x8d, 0x99, 0xc7,
	0xbd, 0x5d, 0xb7, 0x7f, 0x7a, 0xe0, 0xda, 0xe5, 0xce, 0x11, 0x34, 0x7d, 0x1e, 0xb7, 0x6f, 0xd8,
	0x0d, 0x2f, 0x64, 0x93, 0x63, 0x1e, 0xd0, 0x48, 0xff, 0x63, 0xf1, 0xcd, 0x47, 0x21, 0x8f, 0x48,
	0x12, 0xb6, 0x5f, 0xec, 0x08, 0xd1, 0xf6, 0x79, 0xbc, 0xad, 0x60, 0x9f, 0x47, 0xdb, 0x24, 0x4d,
	0xb7, 0x27, 0xed, 0xe0, 0x2d, 0xab, 0xc3, 0x4f, 0xff, 0x0a, 0x00, 0x00, 0xff, 0xff, 0x76, 0x24,
	0x9f, 0x60, 0xd8, 0x0c, 0x00, 0x00,
}
