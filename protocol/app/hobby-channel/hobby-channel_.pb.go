// Code generated by protoc-gen-go. DO NOT EDIT.
// source: hobby_channel/hobby-channel_.proto

package hobby_channel // import "golang.52tt.com/protocol/app/hobby-channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import muse_interest_hub_logic "golang.52tt.com/protocol/app/muse-interest-hub-logic"
import topic_channel "golang.52tt.com/protocol/app/topic-channel"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type CreateChannelSource int32

const (
	CreateChannelSource_From_Link   CreateChannelSource = 0
	CreateChannelSource_From_Client CreateChannelSource = 1
)

var CreateChannelSource_name = map[int32]string{
	0: "From_Link",
	1: "From_Client",
}
var CreateChannelSource_value = map[string]int32{
	"From_Link":   0,
	"From_Client": 1,
}

func (x CreateChannelSource) String() string {
	return proto.EnumName(CreateChannelSource_name, int32(x))
}
func (CreateChannelSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{0}
}

type SwitchSource int32

const (
	SwitchSource_SWITCH_SOURCE_UNSPECIFIED   SwitchSource = 0
	SwitchSource_SWITCH_SOURCE_ROOM          SwitchSource = 1
	SwitchSource_SWITCH_SOURCE_PLAY_TOGETHER SwitchSource = 2
	SwitchSource_SWITCH_SOURCE_JS_MITAO      SwitchSource = 3
	SwitchSource_SWITCH_SOURCE_CREATE_ROOM   SwitchSource = 4
	SwitchSource_SWITCH_SOURCE_COMMON_LINK   SwitchSource = 5
	SwitchSource_SWITCH_SOURCE_OTHER_BUSS    SwitchSource = 6
)

var SwitchSource_name = map[int32]string{
	0: "SWITCH_SOURCE_UNSPECIFIED",
	1: "SWITCH_SOURCE_ROOM",
	2: "SWITCH_SOURCE_PLAY_TOGETHER",
	3: "SWITCH_SOURCE_JS_MITAO",
	4: "SWITCH_SOURCE_CREATE_ROOM",
	5: "SWITCH_SOURCE_COMMON_LINK",
	6: "SWITCH_SOURCE_OTHER_BUSS",
}
var SwitchSource_value = map[string]int32{
	"SWITCH_SOURCE_UNSPECIFIED":   0,
	"SWITCH_SOURCE_ROOM":          1,
	"SWITCH_SOURCE_PLAY_TOGETHER": 2,
	"SWITCH_SOURCE_JS_MITAO":      3,
	"SWITCH_SOURCE_CREATE_ROOM":   4,
	"SWITCH_SOURCE_COMMON_LINK":   5,
	"SWITCH_SOURCE_OTHER_BUSS":    6,
}

func (x SwitchSource) String() string {
	return proto.EnumName(SwitchSource_name, int32(x))
}
func (SwitchSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{1}
}

type MusicHomePageViewType int32

const (
	MusicHomePageViewType_MusicHomePageView_Unknown     MusicHomePageViewType = 0
	MusicHomePageViewType_MusicHomePageView_Sing_A_Song MusicHomePageViewType = 1
	MusicHomePageViewType_MusicHomePageView_KTV         MusicHomePageViewType = 2
	MusicHomePageViewType_MusicHomePageView_Leisure     MusicHomePageViewType = 3
	MusicHomePageViewType_MusicHomePageView_Rap         MusicHomePageViewType = 4
)

var MusicHomePageViewType_name = map[int32]string{
	0: "MusicHomePageView_Unknown",
	1: "MusicHomePageView_Sing_A_Song",
	2: "MusicHomePageView_KTV",
	3: "MusicHomePageView_Leisure",
	4: "MusicHomePageView_Rap",
}
var MusicHomePageViewType_value = map[string]int32{
	"MusicHomePageView_Unknown":     0,
	"MusicHomePageView_Sing_A_Song": 1,
	"MusicHomePageView_KTV":         2,
	"MusicHomePageView_Leisure":     3,
	"MusicHomePageView_Rap":         4,
}

func (x MusicHomePageViewType) String() string {
	return proto.EnumName(MusicHomePageViewType_name, int32(x))
}
func (MusicHomePageViewType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{2}
}

type MysteryEntryType int32

const (
	MysteryEntryType_GameHomePageEntry    MysteryEntryType = 0
	MysteryEntryType_MysteryHomePageEntry MysteryEntryType = 1
	MysteryEntryType_PCHomePageEntry      MysteryEntryType = 2
	MysteryEntryType_FASTPCHomePageEntry  MysteryEntryType = 3
)

var MysteryEntryType_name = map[int32]string{
	0: "GameHomePageEntry",
	1: "MysteryHomePageEntry",
	2: "PCHomePageEntry",
	3: "FASTPCHomePageEntry",
}
var MysteryEntryType_value = map[string]int32{
	"GameHomePageEntry":    0,
	"MysteryHomePageEntry": 1,
	"PCHomePageEntry":      2,
	"FASTPCHomePageEntry":  3,
}

func (x MysteryEntryType) String() string {
	return proto.EnumName(MysteryEntryType_name, int32(x))
}
func (MysteryEntryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{3}
}

type MusicHomePageView_ActionType int32

const (
	MusicHomePageView_dialog MusicHomePageView_ActionType = 0
	MusicHomePageView_quick  MusicHomePageView_ActionType = 1
)

var MusicHomePageView_ActionType_name = map[int32]string{
	0: "dialog",
	1: "quick",
}
var MusicHomePageView_ActionType_value = map[string]int32{
	"dialog": 0,
	"quick":  1,
}

func (x MusicHomePageView_ActionType) String() string {
	return proto.EnumName(MusicHomePageView_ActionType_name, int32(x))
}
func (MusicHomePageView_ActionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{16, 0}
}

// Mode 是 Block 的选择模式
type ListMusicHomePageBlockResp_Block_Mode int32

const (
	ListMusicHomePageBlockResp_Block_SINGLE ListMusicHomePageBlockResp_Block_Mode = 0
	ListMusicHomePageBlockResp_Block_MULTI  ListMusicHomePageBlockResp_Block_Mode = 1
)

var ListMusicHomePageBlockResp_Block_Mode_name = map[int32]string{
	0: "SINGLE",
	1: "MULTI",
}
var ListMusicHomePageBlockResp_Block_Mode_value = map[string]int32{
	"SINGLE": 0,
	"MULTI":  1,
}

func (x ListMusicHomePageBlockResp_Block_Mode) String() string {
	return proto.EnumName(ListMusicHomePageBlockResp_Block_Mode_name, int32(x))
}
func (ListMusicHomePageBlockResp_Block_Mode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{23, 0, 0}
}

// 创建房间
type CreateHobbyChannelReq struct {
	BaseReq              *app.BaseReq        `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelName          string              `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	TabId                uint32              `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	CreateSource         CreateChannelSource `protobuf:"varint,4,opt,name=create_source,json=createSource,proto3,enum=ga.hobby_channel.CreateChannelSource" json:"create_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *CreateHobbyChannelReq) Reset()         { *m = CreateHobbyChannelReq{} }
func (m *CreateHobbyChannelReq) String() string { return proto.CompactTextString(m) }
func (*CreateHobbyChannelReq) ProtoMessage()    {}
func (*CreateHobbyChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{0}
}
func (m *CreateHobbyChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateHobbyChannelReq.Unmarshal(m, b)
}
func (m *CreateHobbyChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateHobbyChannelReq.Marshal(b, m, deterministic)
}
func (dst *CreateHobbyChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateHobbyChannelReq.Merge(dst, src)
}
func (m *CreateHobbyChannelReq) XXX_Size() int {
	return xxx_messageInfo_CreateHobbyChannelReq.Size(m)
}
func (m *CreateHobbyChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateHobbyChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateHobbyChannelReq proto.InternalMessageInfo

func (m *CreateHobbyChannelReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CreateHobbyChannelReq) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *CreateHobbyChannelReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *CreateHobbyChannelReq) GetCreateSource() CreateChannelSource {
	if m != nil {
		return m.CreateSource
	}
	return CreateChannelSource_From_Link
}

type CreateHobbyChannelResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CreateHobbyChannelResp) Reset()         { *m = CreateHobbyChannelResp{} }
func (m *CreateHobbyChannelResp) String() string { return proto.CompactTextString(m) }
func (*CreateHobbyChannelResp) ProtoMessage()    {}
func (*CreateHobbyChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{1}
}
func (m *CreateHobbyChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateHobbyChannelResp.Unmarshal(m, b)
}
func (m *CreateHobbyChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateHobbyChannelResp.Marshal(b, m, deterministic)
}
func (dst *CreateHobbyChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateHobbyChannelResp.Merge(dst, src)
}
func (m *CreateHobbyChannelResp) XXX_Size() int {
	return xxx_messageInfo_CreateHobbyChannelResp.Size(m)
}
func (m *CreateHobbyChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateHobbyChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateHobbyChannelResp proto.InternalMessageInfo

func (m *CreateHobbyChannelResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CreateHobbyChannelResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 切换玩法
type SwitchHobbyChannelSubjectReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32       `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	SwitchSource         uint32       `protobuf:"varint,4,opt,name=switch_source,json=switchSource,proto3" json:"switch_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SwitchHobbyChannelSubjectReq) Reset()         { *m = SwitchHobbyChannelSubjectReq{} }
func (m *SwitchHobbyChannelSubjectReq) String() string { return proto.CompactTextString(m) }
func (*SwitchHobbyChannelSubjectReq) ProtoMessage()    {}
func (*SwitchHobbyChannelSubjectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{2}
}
func (m *SwitchHobbyChannelSubjectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchHobbyChannelSubjectReq.Unmarshal(m, b)
}
func (m *SwitchHobbyChannelSubjectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchHobbyChannelSubjectReq.Marshal(b, m, deterministic)
}
func (dst *SwitchHobbyChannelSubjectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchHobbyChannelSubjectReq.Merge(dst, src)
}
func (m *SwitchHobbyChannelSubjectReq) XXX_Size() int {
	return xxx_messageInfo_SwitchHobbyChannelSubjectReq.Size(m)
}
func (m *SwitchHobbyChannelSubjectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchHobbyChannelSubjectReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchHobbyChannelSubjectReq proto.InternalMessageInfo

func (m *SwitchHobbyChannelSubjectReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SwitchHobbyChannelSubjectReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SwitchHobbyChannelSubjectReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SwitchHobbyChannelSubjectReq) GetSwitchSource() uint32 {
	if m != nil {
		return m.SwitchSource
	}
	return 0
}

type SwitchHobbyChannelSubjectResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SwitchHobbyChannelSubjectResp) Reset()         { *m = SwitchHobbyChannelSubjectResp{} }
func (m *SwitchHobbyChannelSubjectResp) String() string { return proto.CompactTextString(m) }
func (*SwitchHobbyChannelSubjectResp) ProtoMessage()    {}
func (*SwitchHobbyChannelSubjectResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{3}
}
func (m *SwitchHobbyChannelSubjectResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchHobbyChannelSubjectResp.Unmarshal(m, b)
}
func (m *SwitchHobbyChannelSubjectResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchHobbyChannelSubjectResp.Marshal(b, m, deterministic)
}
func (dst *SwitchHobbyChannelSubjectResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchHobbyChannelSubjectResp.Merge(dst, src)
}
func (m *SwitchHobbyChannelSubjectResp) XXX_Size() int {
	return xxx_messageInfo_SwitchHobbyChannelSubjectResp.Size(m)
}
func (m *SwitchHobbyChannelSubjectResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchHobbyChannelSubjectResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchHobbyChannelSubjectResp proto.InternalMessageInfo

func (m *SwitchHobbyChannelSubjectResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 发布房间
type PublishHobbyChannelReq struct {
	BaseReq              *app.BaseReq                 `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32                       `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOptions         []*topic_channel.BlockOption `protobuf:"bytes,4,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	ChannelName          string                       `protobuf:"bytes,5,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	IsWantFresh          bool                         `protobuf:"varint,6,opt,name=is_want_fresh,json=isWantFresh,proto3" json:"is_want_fresh,omitempty"`
	IsShowGeoInfo        bool                         `protobuf:"varint,7,opt,name=is_show_geo_info,json=isShowGeoInfo,proto3" json:"is_show_geo_info,omitempty"`
	DiyHobbyName         string                       `protobuf:"bytes,12,opt,name=diy_hobby_name,json=diyHobbyName,proto3" json:"diy_hobby_name,omitempty"`
	AllSelectedBids      []uint32                     `protobuf:"varint,13,rep,packed,name=all_selected_bids,json=allSelectedBids,proto3" json:"all_selected_bids,omitempty"`
	NeedCheckChannelName bool                         `protobuf:"varint,14,opt,name=need_check_channel_name,json=needCheckChannelName,proto3" json:"need_check_channel_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *PublishHobbyChannelReq) Reset()         { *m = PublishHobbyChannelReq{} }
func (m *PublishHobbyChannelReq) String() string { return proto.CompactTextString(m) }
func (*PublishHobbyChannelReq) ProtoMessage()    {}
func (*PublishHobbyChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{4}
}
func (m *PublishHobbyChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishHobbyChannelReq.Unmarshal(m, b)
}
func (m *PublishHobbyChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishHobbyChannelReq.Marshal(b, m, deterministic)
}
func (dst *PublishHobbyChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishHobbyChannelReq.Merge(dst, src)
}
func (m *PublishHobbyChannelReq) XXX_Size() int {
	return xxx_messageInfo_PublishHobbyChannelReq.Size(m)
}
func (m *PublishHobbyChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishHobbyChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_PublishHobbyChannelReq proto.InternalMessageInfo

func (m *PublishHobbyChannelReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PublishHobbyChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PublishHobbyChannelReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *PublishHobbyChannelReq) GetBlockOptions() []*topic_channel.BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *PublishHobbyChannelReq) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *PublishHobbyChannelReq) GetIsWantFresh() bool {
	if m != nil {
		return m.IsWantFresh
	}
	return false
}

func (m *PublishHobbyChannelReq) GetIsShowGeoInfo() bool {
	if m != nil {
		return m.IsShowGeoInfo
	}
	return false
}

func (m *PublishHobbyChannelReq) GetDiyHobbyName() string {
	if m != nil {
		return m.DiyHobbyName
	}
	return ""
}

func (m *PublishHobbyChannelReq) GetAllSelectedBids() []uint32 {
	if m != nil {
		return m.AllSelectedBids
	}
	return nil
}

func (m *PublishHobbyChannelReq) GetNeedCheckChannelName() bool {
	if m != nil {
		return m.NeedCheckChannelName
	}
	return false
}

type PublishHobbyChannelResp struct {
	BaseResp             *app.BaseResp                                     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChangeCoolDown       uint32                                            `protobuf:"varint,2,opt,name=change_cool_down,json=changeCoolDown,proto3" json:"change_cool_down,omitempty"`
	FreezeDuration       uint32                                            `protobuf:"varint,3,opt,name=freeze_duration,json=freezeDuration,proto3" json:"freeze_duration,omitempty"`
	AutoDismissDuration  uint32                                            `protobuf:"varint,4,opt,name=auto_dismiss_duration,json=autoDismissDuration,proto3" json:"auto_dismiss_duration,omitempty"`
	SecondaryItem        []*topic_channel.ShowTopicChannelTabSecondaryItem `protobuf:"bytes,5,rep,name=secondary_item,json=secondaryItem,proto3" json:"secondary_item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                          `json:"-"`
	XXX_unrecognized     []byte                                            `json:"-"`
	XXX_sizecache        int32                                             `json:"-"`
}

func (m *PublishHobbyChannelResp) Reset()         { *m = PublishHobbyChannelResp{} }
func (m *PublishHobbyChannelResp) String() string { return proto.CompactTextString(m) }
func (*PublishHobbyChannelResp) ProtoMessage()    {}
func (*PublishHobbyChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{5}
}
func (m *PublishHobbyChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishHobbyChannelResp.Unmarshal(m, b)
}
func (m *PublishHobbyChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishHobbyChannelResp.Marshal(b, m, deterministic)
}
func (dst *PublishHobbyChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishHobbyChannelResp.Merge(dst, src)
}
func (m *PublishHobbyChannelResp) XXX_Size() int {
	return xxx_messageInfo_PublishHobbyChannelResp.Size(m)
}
func (m *PublishHobbyChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishHobbyChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_PublishHobbyChannelResp proto.InternalMessageInfo

func (m *PublishHobbyChannelResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PublishHobbyChannelResp) GetChangeCoolDown() uint32 {
	if m != nil {
		return m.ChangeCoolDown
	}
	return 0
}

func (m *PublishHobbyChannelResp) GetFreezeDuration() uint32 {
	if m != nil {
		return m.FreezeDuration
	}
	return 0
}

func (m *PublishHobbyChannelResp) GetAutoDismissDuration() uint32 {
	if m != nil {
		return m.AutoDismissDuration
	}
	return 0
}

func (m *PublishHobbyChannelResp) GetSecondaryItem() []*topic_channel.ShowTopicChannelTabSecondaryItem {
	if m != nil {
		return m.SecondaryItem
	}
	return nil
}

// 房间流请求
type ListHobbyChannelReq struct {
	BaseReq              *app.BaseReq                 `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Count                uint32                       `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	TabId                uint32                       `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOptions         []*topic_channel.BlockOption `protobuf:"bytes,4,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	Sex                  int32                        `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	ChannelPackageId     string                       `protobuf:"bytes,6,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	IsStartLoad          bool                         `protobuf:"varint,7,opt,name=is_start_load,json=isStartLoad,proto3" json:"is_start_load,omitempty"`
	ExposeChannelIds     []uint32                     `protobuf:"varint,8,rep,packed,name=expose_channel_ids,json=exposeChannelIds,proto3" json:"expose_channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *ListHobbyChannelReq) Reset()         { *m = ListHobbyChannelReq{} }
func (m *ListHobbyChannelReq) String() string { return proto.CompactTextString(m) }
func (*ListHobbyChannelReq) ProtoMessage()    {}
func (*ListHobbyChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{6}
}
func (m *ListHobbyChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListHobbyChannelReq.Unmarshal(m, b)
}
func (m *ListHobbyChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListHobbyChannelReq.Marshal(b, m, deterministic)
}
func (dst *ListHobbyChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListHobbyChannelReq.Merge(dst, src)
}
func (m *ListHobbyChannelReq) XXX_Size() int {
	return xxx_messageInfo_ListHobbyChannelReq.Size(m)
}
func (m *ListHobbyChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListHobbyChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListHobbyChannelReq proto.InternalMessageInfo

func (m *ListHobbyChannelReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListHobbyChannelReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ListHobbyChannelReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ListHobbyChannelReq) GetBlockOptions() []*topic_channel.BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *ListHobbyChannelReq) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ListHobbyChannelReq) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *ListHobbyChannelReq) GetIsStartLoad() bool {
	if m != nil {
		return m.IsStartLoad
	}
	return false
}

func (m *ListHobbyChannelReq) GetExposeChannelIds() []uint32 {
	if m != nil {
		return m.ExposeChannelIds
	}
	return nil
}

type ListHobbyChannelResp struct {
	BaseResp             *app.BaseResp                            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Items                []*ListHobbyChannelResp_HobbyChannelItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	IsBottomReach        bool                                     `protobuf:"varint,3,opt,name=is_bottom_reach,json=isBottomReach,proto3" json:"is_bottom_reach,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-"`
	XXX_unrecognized     []byte                                   `json:"-"`
	XXX_sizecache        int32                                    `json:"-"`
}

func (m *ListHobbyChannelResp) Reset()         { *m = ListHobbyChannelResp{} }
func (m *ListHobbyChannelResp) String() string { return proto.CompactTextString(m) }
func (*ListHobbyChannelResp) ProtoMessage()    {}
func (*ListHobbyChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{7}
}
func (m *ListHobbyChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListHobbyChannelResp.Unmarshal(m, b)
}
func (m *ListHobbyChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListHobbyChannelResp.Marshal(b, m, deterministic)
}
func (dst *ListHobbyChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListHobbyChannelResp.Merge(dst, src)
}
func (m *ListHobbyChannelResp) XXX_Size() int {
	return xxx_messageInfo_ListHobbyChannelResp.Size(m)
}
func (m *ListHobbyChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListHobbyChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListHobbyChannelResp proto.InternalMessageInfo

func (m *ListHobbyChannelResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListHobbyChannelResp) GetItems() []*ListHobbyChannelResp_HobbyChannelItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *ListHobbyChannelResp) GetIsBottomReach() bool {
	if m != nil {
		return m.IsBottomReach
	}
	return false
}

type ListHobbyChannelResp_HobbyChannelItem struct {
	ChannelId            uint32                                            `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string                                            `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelOwnerAccount  string                                            `protobuf:"bytes,3,opt,name=channel_owner_account,json=channelOwnerAccount,proto3" json:"channel_owner_account,omitempty"`
	ChannelOwnerSex      int32                                             `protobuf:"varint,4,opt,name=channel_owner_sex,json=channelOwnerSex,proto3" json:"channel_owner_sex,omitempty"`
	ChannelMemberCount   uint32                                            `protobuf:"varint,5,opt,name=channel_member_count,json=channelMemberCount,proto3" json:"channel_member_count,omitempty"`
	PublishLabel         string                                            `protobuf:"bytes,6,opt,name=publish_label,json=publishLabel,proto3" json:"publish_label,omitempty"`
	PublishRegion        string                                            `protobuf:"bytes,7,opt,name=publish_region,json=publishRegion,proto3" json:"publish_region,omitempty"`
	ViewType             uint32                                            `protobuf:"varint,8,opt,name=view_type,json=viewType,proto3" json:"view_type,omitempty"`
	PbViewData           []byte                                            `protobuf:"bytes,9,opt,name=pb_view_data,json=pbViewData,proto3" json:"pb_view_data,omitempty"`
	RcmdLabel            uint32                                            `protobuf:"varint,10,opt,name=rcmd_label,json=rcmdLabel,proto3" json:"rcmd_label,omitempty"`
	GeoInfo              string                                            `protobuf:"bytes,11,opt,name=geo_info,json=geoInfo,proto3" json:"geo_info,omitempty"`
	Icon                 string                                            `protobuf:"bytes,12,opt,name=icon,proto3" json:"icon,omitempty"`
	PublishDesc          string                                            `protobuf:"bytes,13,opt,name=publish_desc,json=publishDesc,proto3" json:"publish_desc,omitempty"`
	SongTitle            string                                            `protobuf:"bytes,14,opt,name=song_title,json=songTitle,proto3" json:"song_title,omitempty"`
	HighQuality          bool                                              `protobuf:"varint,15,opt,name=high_quality,json=highQuality,proto3" json:"high_quality,omitempty"`
	TabId                uint32                                            `protobuf:"varint,16,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	RegionId             uint64                                            `protobuf:"varint,17,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	FellowCids           []uint32                                          `protobuf:"varint,18,rep,packed,name=fellow_cids,json=fellowCids,proto3" json:"fellow_cids,omitempty"`
	Footprint            string                                            `protobuf:"bytes,19,opt,name=footprint,proto3" json:"footprint,omitempty"`
	ReviewAccount        string                                            `protobuf:"bytes,20,opt,name=review_account,json=reviewAccount,proto3" json:"review_account,omitempty"`
	ReviewDesc           string                                            `protobuf:"bytes,21,opt,name=review_desc,json=reviewDesc,proto3" json:"review_desc,omitempty"`
	ReviewSex            int32                                             `protobuf:"varint,22,opt,name=review_sex,json=reviewSex,proto3" json:"review_sex,omitempty"`
	Bg                   string                                            `protobuf:"bytes,23,opt,name=bg,proto3" json:"bg,omitempty"`
	DarkBg               string                                            `protobuf:"bytes,24,opt,name=dark_bg,json=darkBg,proto3" json:"dark_bg,omitempty"`
	SameCity             *muse_interest_hub_logic.TopicChannelSameCityInfo `protobuf:"bytes,25,opt,name=same_city,json=sameCity,proto3" json:"same_city,omitempty"`
	ChannelType          uint32                                            `protobuf:"varint,26,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                          `json:"-"`
	XXX_unrecognized     []byte                                            `json:"-"`
	XXX_sizecache        int32                                             `json:"-"`
}

func (m *ListHobbyChannelResp_HobbyChannelItem) Reset()         { *m = ListHobbyChannelResp_HobbyChannelItem{} }
func (m *ListHobbyChannelResp_HobbyChannelItem) String() string { return proto.CompactTextString(m) }
func (*ListHobbyChannelResp_HobbyChannelItem) ProtoMessage()    {}
func (*ListHobbyChannelResp_HobbyChannelItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{7, 0}
}
func (m *ListHobbyChannelResp_HobbyChannelItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListHobbyChannelResp_HobbyChannelItem.Unmarshal(m, b)
}
func (m *ListHobbyChannelResp_HobbyChannelItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListHobbyChannelResp_HobbyChannelItem.Marshal(b, m, deterministic)
}
func (dst *ListHobbyChannelResp_HobbyChannelItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListHobbyChannelResp_HobbyChannelItem.Merge(dst, src)
}
func (m *ListHobbyChannelResp_HobbyChannelItem) XXX_Size() int {
	return xxx_messageInfo_ListHobbyChannelResp_HobbyChannelItem.Size(m)
}
func (m *ListHobbyChannelResp_HobbyChannelItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ListHobbyChannelResp_HobbyChannelItem.DiscardUnknown(m)
}

var xxx_messageInfo_ListHobbyChannelResp_HobbyChannelItem proto.InternalMessageInfo

func (m *ListHobbyChannelResp_HobbyChannelItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetChannelOwnerAccount() string {
	if m != nil {
		return m.ChannelOwnerAccount
	}
	return ""
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetChannelOwnerSex() int32 {
	if m != nil {
		return m.ChannelOwnerSex
	}
	return 0
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetChannelMemberCount() uint32 {
	if m != nil {
		return m.ChannelMemberCount
	}
	return 0
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetPublishLabel() string {
	if m != nil {
		return m.PublishLabel
	}
	return ""
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetPublishRegion() string {
	if m != nil {
		return m.PublishRegion
	}
	return ""
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetViewType() uint32 {
	if m != nil {
		return m.ViewType
	}
	return 0
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetPbViewData() []byte {
	if m != nil {
		return m.PbViewData
	}
	return nil
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetRcmdLabel() uint32 {
	if m != nil {
		return m.RcmdLabel
	}
	return 0
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetGeoInfo() string {
	if m != nil {
		return m.GeoInfo
	}
	return ""
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetPublishDesc() string {
	if m != nil {
		return m.PublishDesc
	}
	return ""
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetSongTitle() string {
	if m != nil {
		return m.SongTitle
	}
	return ""
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetHighQuality() bool {
	if m != nil {
		return m.HighQuality
	}
	return false
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetFellowCids() []uint32 {
	if m != nil {
		return m.FellowCids
	}
	return nil
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetFootprint() string {
	if m != nil {
		return m.Footprint
	}
	return ""
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetReviewAccount() string {
	if m != nil {
		return m.ReviewAccount
	}
	return ""
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetReviewDesc() string {
	if m != nil {
		return m.ReviewDesc
	}
	return ""
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetReviewSex() int32 {
	if m != nil {
		return m.ReviewSex
	}
	return 0
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetDarkBg() string {
	if m != nil {
		return m.DarkBg
	}
	return ""
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetSameCity() *muse_interest_hub_logic.TopicChannelSameCityInfo {
	if m != nil {
		return m.SameCity
	}
	return nil
}

func (m *ListHobbyChannelResp_HobbyChannelItem) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

// 快速匹配
type QuickMatchHobbyChannelReq struct {
	BaseReq              *app.BaseReq                 `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32                       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOptions         []*topic_channel.BlockOption `protobuf:"bytes,3,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	ChannelPackageId     string                       `protobuf:"bytes,4,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *QuickMatchHobbyChannelReq) Reset()         { *m = QuickMatchHobbyChannelReq{} }
func (m *QuickMatchHobbyChannelReq) String() string { return proto.CompactTextString(m) }
func (*QuickMatchHobbyChannelReq) ProtoMessage()    {}
func (*QuickMatchHobbyChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{8}
}
func (m *QuickMatchHobbyChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuickMatchHobbyChannelReq.Unmarshal(m, b)
}
func (m *QuickMatchHobbyChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuickMatchHobbyChannelReq.Marshal(b, m, deterministic)
}
func (dst *QuickMatchHobbyChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuickMatchHobbyChannelReq.Merge(dst, src)
}
func (m *QuickMatchHobbyChannelReq) XXX_Size() int {
	return xxx_messageInfo_QuickMatchHobbyChannelReq.Size(m)
}
func (m *QuickMatchHobbyChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QuickMatchHobbyChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_QuickMatchHobbyChannelReq proto.InternalMessageInfo

func (m *QuickMatchHobbyChannelReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *QuickMatchHobbyChannelReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *QuickMatchHobbyChannelReq) GetBlockOptions() []*topic_channel.BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *QuickMatchHobbyChannelReq) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

type QuickMatchHobbyChannelResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *QuickMatchHobbyChannelResp) Reset()         { *m = QuickMatchHobbyChannelResp{} }
func (m *QuickMatchHobbyChannelResp) String() string { return proto.CompactTextString(m) }
func (*QuickMatchHobbyChannelResp) ProtoMessage()    {}
func (*QuickMatchHobbyChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{9}
}
func (m *QuickMatchHobbyChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuickMatchHobbyChannelResp.Unmarshal(m, b)
}
func (m *QuickMatchHobbyChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuickMatchHobbyChannelResp.Marshal(b, m, deterministic)
}
func (dst *QuickMatchHobbyChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuickMatchHobbyChannelResp.Merge(dst, src)
}
func (m *QuickMatchHobbyChannelResp) XXX_Size() int {
	return xxx_messageInfo_QuickMatchHobbyChannelResp.Size(m)
}
func (m *QuickMatchHobbyChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QuickMatchHobbyChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_QuickMatchHobbyChannelResp proto.InternalMessageInfo

func (m *QuickMatchHobbyChannelResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *QuickMatchHobbyChannelResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 20秒调用一次，房主与主题房保持心跳，超时会停止展示在大厅
type KeepHobbyChannelPublishReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *KeepHobbyChannelPublishReq) Reset()         { *m = KeepHobbyChannelPublishReq{} }
func (m *KeepHobbyChannelPublishReq) String() string { return proto.CompactTextString(m) }
func (*KeepHobbyChannelPublishReq) ProtoMessage()    {}
func (*KeepHobbyChannelPublishReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{10}
}
func (m *KeepHobbyChannelPublishReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KeepHobbyChannelPublishReq.Unmarshal(m, b)
}
func (m *KeepHobbyChannelPublishReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KeepHobbyChannelPublishReq.Marshal(b, m, deterministic)
}
func (dst *KeepHobbyChannelPublishReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeepHobbyChannelPublishReq.Merge(dst, src)
}
func (m *KeepHobbyChannelPublishReq) XXX_Size() int {
	return xxx_messageInfo_KeepHobbyChannelPublishReq.Size(m)
}
func (m *KeepHobbyChannelPublishReq) XXX_DiscardUnknown() {
	xxx_messageInfo_KeepHobbyChannelPublishReq.DiscardUnknown(m)
}

var xxx_messageInfo_KeepHobbyChannelPublishReq proto.InternalMessageInfo

func (m *KeepHobbyChannelPublishReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *KeepHobbyChannelPublishReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type KeepHobbyChannelPublishResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsTheEnd             bool          `protobuf:"varint,2,opt,name=is_the_end,json=isTheEnd,proto3" json:"is_the_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *KeepHobbyChannelPublishResp) Reset()         { *m = KeepHobbyChannelPublishResp{} }
func (m *KeepHobbyChannelPublishResp) String() string { return proto.CompactTextString(m) }
func (*KeepHobbyChannelPublishResp) ProtoMessage()    {}
func (*KeepHobbyChannelPublishResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{11}
}
func (m *KeepHobbyChannelPublishResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KeepHobbyChannelPublishResp.Unmarshal(m, b)
}
func (m *KeepHobbyChannelPublishResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KeepHobbyChannelPublishResp.Marshal(b, m, deterministic)
}
func (dst *KeepHobbyChannelPublishResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeepHobbyChannelPublishResp.Merge(dst, src)
}
func (m *KeepHobbyChannelPublishResp) XXX_Size() int {
	return xxx_messageInfo_KeepHobbyChannelPublishResp.Size(m)
}
func (m *KeepHobbyChannelPublishResp) XXX_DiscardUnknown() {
	xxx_messageInfo_KeepHobbyChannelPublishResp.DiscardUnknown(m)
}

var xxx_messageInfo_KeepHobbyChannelPublishResp proto.InternalMessageInfo

func (m *KeepHobbyChannelPublishResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *KeepHobbyChannelPublishResp) GetIsTheEnd() bool {
	if m != nil {
		return m.IsTheEnd
	}
	return false
}

// 房间发布取消
type CancelHobbyChannelPublishReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CancelHobbyChannelPublishReq) Reset()         { *m = CancelHobbyChannelPublishReq{} }
func (m *CancelHobbyChannelPublishReq) String() string { return proto.CompactTextString(m) }
func (*CancelHobbyChannelPublishReq) ProtoMessage()    {}
func (*CancelHobbyChannelPublishReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{12}
}
func (m *CancelHobbyChannelPublishReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelHobbyChannelPublishReq.Unmarshal(m, b)
}
func (m *CancelHobbyChannelPublishReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelHobbyChannelPublishReq.Marshal(b, m, deterministic)
}
func (dst *CancelHobbyChannelPublishReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelHobbyChannelPublishReq.Merge(dst, src)
}
func (m *CancelHobbyChannelPublishReq) XXX_Size() int {
	return xxx_messageInfo_CancelHobbyChannelPublishReq.Size(m)
}
func (m *CancelHobbyChannelPublishReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelHobbyChannelPublishReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelHobbyChannelPublishReq proto.InternalMessageInfo

func (m *CancelHobbyChannelPublishReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CancelHobbyChannelPublishReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CancelHobbyChannelPublishResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CancelHobbyChannelPublishResp) Reset()         { *m = CancelHobbyChannelPublishResp{} }
func (m *CancelHobbyChannelPublishResp) String() string { return proto.CompactTextString(m) }
func (*CancelHobbyChannelPublishResp) ProtoMessage()    {}
func (*CancelHobbyChannelPublishResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{13}
}
func (m *CancelHobbyChannelPublishResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelHobbyChannelPublishResp.Unmarshal(m, b)
}
func (m *CancelHobbyChannelPublishResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelHobbyChannelPublishResp.Marshal(b, m, deterministic)
}
func (dst *CancelHobbyChannelPublishResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelHobbyChannelPublishResp.Merge(dst, src)
}
func (m *CancelHobbyChannelPublishResp) XXX_Size() int {
	return xxx_messageInfo_CancelHobbyChannelPublishResp.Size(m)
}
func (m *CancelHobbyChannelPublishResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelHobbyChannelPublishResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelHobbyChannelPublishResp proto.InternalMessageInfo

func (m *CancelHobbyChannelPublishResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 音乐首页控件
type GetMusicHomePageViewReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMusicHomePageViewReq) Reset()         { *m = GetMusicHomePageViewReq{} }
func (m *GetMusicHomePageViewReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicHomePageViewReq) ProtoMessage()    {}
func (*GetMusicHomePageViewReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{14}
}
func (m *GetMusicHomePageViewReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageViewReq.Unmarshal(m, b)
}
func (m *GetMusicHomePageViewReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageViewReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageViewReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageViewReq.Merge(dst, src)
}
func (m *GetMusicHomePageViewReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageViewReq.Size(m)
}
func (m *GetMusicHomePageViewReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageViewReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageViewReq proto.InternalMessageInfo

func (m *GetMusicHomePageViewReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetMusicHomePageViewResp struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	View                 []*MusicHomePageView `protobuf:"bytes,2,rep,name=view,proto3" json:"view,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetMusicHomePageViewResp) Reset()         { *m = GetMusicHomePageViewResp{} }
func (m *GetMusicHomePageViewResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicHomePageViewResp) ProtoMessage()    {}
func (*GetMusicHomePageViewResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{15}
}
func (m *GetMusicHomePageViewResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageViewResp.Unmarshal(m, b)
}
func (m *GetMusicHomePageViewResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageViewResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageViewResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageViewResp.Merge(dst, src)
}
func (m *GetMusicHomePageViewResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageViewResp.Size(m)
}
func (m *GetMusicHomePageViewResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageViewResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageViewResp proto.InternalMessageInfo

func (m *GetMusicHomePageViewResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusicHomePageViewResp) GetView() []*MusicHomePageView {
	if m != nil {
		return m.View
	}
	return nil
}

type MusicHomePageView struct {
	ViewType  MusicHomePageViewType `protobuf:"varint,1,opt,name=view_type,json=viewType,proto3,enum=ga.hobby_channel.MusicHomePageViewType" json:"view_type,omitempty"`
	Title     string                `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle  string                `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Image     string                `protobuf:"bytes,4,opt,name=image,proto3" json:"image,omitempty"`
	BackColor string                `protobuf:"bytes,5,opt,name=back_color,json=backColor,proto3" json:"back_color,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	ActionType MusicHomePageView_ActionType `protobuf:"varint,6,opt,name=actionType,proto3,enum=ga.hobby_channel.MusicHomePageView_ActionType" json:"actionType,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	TabId                uint32                       `protobuf:"varint,7,opt,name=tabId,proto3" json:"tabId,omitempty"`
	BlockOptions         []*topic_channel.BlockOption `protobuf:"bytes,8,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *MusicHomePageView) Reset()         { *m = MusicHomePageView{} }
func (m *MusicHomePageView) String() string { return proto.CompactTextString(m) }
func (*MusicHomePageView) ProtoMessage()    {}
func (*MusicHomePageView) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{16}
}
func (m *MusicHomePageView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicHomePageView.Unmarshal(m, b)
}
func (m *MusicHomePageView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicHomePageView.Marshal(b, m, deterministic)
}
func (dst *MusicHomePageView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicHomePageView.Merge(dst, src)
}
func (m *MusicHomePageView) XXX_Size() int {
	return xxx_messageInfo_MusicHomePageView.Size(m)
}
func (m *MusicHomePageView) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicHomePageView.DiscardUnknown(m)
}

var xxx_messageInfo_MusicHomePageView proto.InternalMessageInfo

func (m *MusicHomePageView) GetViewType() MusicHomePageViewType {
	if m != nil {
		return m.ViewType
	}
	return MusicHomePageViewType_MusicHomePageView_Unknown
}

func (m *MusicHomePageView) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *MusicHomePageView) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *MusicHomePageView) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *MusicHomePageView) GetBackColor() string {
	if m != nil {
		return m.BackColor
	}
	return ""
}

func (m *MusicHomePageView) GetActionType() MusicHomePageView_ActionType {
	if m != nil {
		return m.ActionType
	}
	return MusicHomePageView_dialog
}

func (m *MusicHomePageView) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *MusicHomePageView) GetBlockOptions() []*topic_channel.BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

// 音乐首页对话框
type GetMusicHomePageDialogReq struct {
	BaseReq              *app.BaseReq          `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Type                 MusicHomePageViewType `protobuf:"varint,2,opt,name=type,proto3,enum=ga.hobby_channel.MusicHomePageViewType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetMusicHomePageDialogReq) Reset()         { *m = GetMusicHomePageDialogReq{} }
func (m *GetMusicHomePageDialogReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicHomePageDialogReq) ProtoMessage()    {}
func (*GetMusicHomePageDialogReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{17}
}
func (m *GetMusicHomePageDialogReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageDialogReq.Unmarshal(m, b)
}
func (m *GetMusicHomePageDialogReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageDialogReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageDialogReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageDialogReq.Merge(dst, src)
}
func (m *GetMusicHomePageDialogReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageDialogReq.Size(m)
}
func (m *GetMusicHomePageDialogReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageDialogReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageDialogReq proto.InternalMessageInfo

func (m *GetMusicHomePageDialogReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMusicHomePageDialogReq) GetType() MusicHomePageViewType {
	if m != nil {
		return m.Type
	}
	return MusicHomePageViewType_MusicHomePageView_Unknown
}

type GetMusicHomePageDialogResp struct {
	BaseResp             *app.BaseResp                 `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Views                []*GetMusicHomePageDialogView `protobuf:"bytes,2,rep,name=views,proto3" json:"views,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetMusicHomePageDialogResp) Reset()         { *m = GetMusicHomePageDialogResp{} }
func (m *GetMusicHomePageDialogResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicHomePageDialogResp) ProtoMessage()    {}
func (*GetMusicHomePageDialogResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{18}
}
func (m *GetMusicHomePageDialogResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageDialogResp.Unmarshal(m, b)
}
func (m *GetMusicHomePageDialogResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageDialogResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageDialogResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageDialogResp.Merge(dst, src)
}
func (m *GetMusicHomePageDialogResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageDialogResp.Size(m)
}
func (m *GetMusicHomePageDialogResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageDialogResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageDialogResp proto.InternalMessageInfo

func (m *GetMusicHomePageDialogResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusicHomePageDialogResp) GetViews() []*GetMusicHomePageDialogView {
	if m != nil {
		return m.Views
	}
	return nil
}

type GetMusicHomePageDialogView struct {
	Title     string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle  string `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Image     string `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	BackColor string `protobuf:"bytes,4,opt,name=back_color,json=backColor,proto3" json:"back_color,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	TabId                uint32                       `protobuf:"varint,5,opt,name=tabId,proto3" json:"tabId,omitempty"`
	BlockOptions         []*topic_channel.BlockOption `protobuf:"bytes,6,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetMusicHomePageDialogView) Reset()         { *m = GetMusicHomePageDialogView{} }
func (m *GetMusicHomePageDialogView) String() string { return proto.CompactTextString(m) }
func (*GetMusicHomePageDialogView) ProtoMessage()    {}
func (*GetMusicHomePageDialogView) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{19}
}
func (m *GetMusicHomePageDialogView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageDialogView.Unmarshal(m, b)
}
func (m *GetMusicHomePageDialogView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageDialogView.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageDialogView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageDialogView.Merge(dst, src)
}
func (m *GetMusicHomePageDialogView) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageDialogView.Size(m)
}
func (m *GetMusicHomePageDialogView) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageDialogView.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageDialogView proto.InternalMessageInfo

func (m *GetMusicHomePageDialogView) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMusicHomePageDialogView) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *GetMusicHomePageDialogView) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *GetMusicHomePageDialogView) GetBackColor() string {
	if m != nil {
		return m.BackColor
	}
	return ""
}

func (m *GetMusicHomePageDialogView) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetMusicHomePageDialogView) GetBlockOptions() []*topic_channel.BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

// 音乐首页选择器
type ListMusicHomePageCategoryReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListMusicHomePageCategoryReq) Reset()         { *m = ListMusicHomePageCategoryReq{} }
func (m *ListMusicHomePageCategoryReq) String() string { return proto.CompactTextString(m) }
func (*ListMusicHomePageCategoryReq) ProtoMessage()    {}
func (*ListMusicHomePageCategoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{20}
}
func (m *ListMusicHomePageCategoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicHomePageCategoryReq.Unmarshal(m, b)
}
func (m *ListMusicHomePageCategoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicHomePageCategoryReq.Marshal(b, m, deterministic)
}
func (dst *ListMusicHomePageCategoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicHomePageCategoryReq.Merge(dst, src)
}
func (m *ListMusicHomePageCategoryReq) XXX_Size() int {
	return xxx_messageInfo_ListMusicHomePageCategoryReq.Size(m)
}
func (m *ListMusicHomePageCategoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicHomePageCategoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicHomePageCategoryReq proto.InternalMessageInfo

func (m *ListMusicHomePageCategoryReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type ListMusicHomePageCategoryResp struct {
	BaseResp             *app.BaseResp                             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Categories           []*ListMusicHomePageCategoryResp_Category `protobuf:"bytes,2,rep,name=categories,proto3" json:"categories,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                  `json:"-"`
	XXX_unrecognized     []byte                                    `json:"-"`
	XXX_sizecache        int32                                     `json:"-"`
}

func (m *ListMusicHomePageCategoryResp) Reset()         { *m = ListMusicHomePageCategoryResp{} }
func (m *ListMusicHomePageCategoryResp) String() string { return proto.CompactTextString(m) }
func (*ListMusicHomePageCategoryResp) ProtoMessage()    {}
func (*ListMusicHomePageCategoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{21}
}
func (m *ListMusicHomePageCategoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicHomePageCategoryResp.Unmarshal(m, b)
}
func (m *ListMusicHomePageCategoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicHomePageCategoryResp.Marshal(b, m, deterministic)
}
func (dst *ListMusicHomePageCategoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicHomePageCategoryResp.Merge(dst, src)
}
func (m *ListMusicHomePageCategoryResp) XXX_Size() int {
	return xxx_messageInfo_ListMusicHomePageCategoryResp.Size(m)
}
func (m *ListMusicHomePageCategoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicHomePageCategoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicHomePageCategoryResp proto.InternalMessageInfo

func (m *ListMusicHomePageCategoryResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListMusicHomePageCategoryResp) GetCategories() []*ListMusicHomePageCategoryResp_Category {
	if m != nil {
		return m.Categories
	}
	return nil
}

type ListMusicHomePageCategoryResp_Category struct {
	Id                   uint32                               `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Desc                 string                               `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	Tabs                 []*ListMusicHomePageCategoryResp_Tab `protobuf:"bytes,3,rep,name=tabs,proto3" json:"tabs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *ListMusicHomePageCategoryResp_Category) Reset() {
	*m = ListMusicHomePageCategoryResp_Category{}
}
func (m *ListMusicHomePageCategoryResp_Category) String() string { return proto.CompactTextString(m) }
func (*ListMusicHomePageCategoryResp_Category) ProtoMessage()    {}
func (*ListMusicHomePageCategoryResp_Category) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{21, 0}
}
func (m *ListMusicHomePageCategoryResp_Category) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicHomePageCategoryResp_Category.Unmarshal(m, b)
}
func (m *ListMusicHomePageCategoryResp_Category) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicHomePageCategoryResp_Category.Marshal(b, m, deterministic)
}
func (dst *ListMusicHomePageCategoryResp_Category) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicHomePageCategoryResp_Category.Merge(dst, src)
}
func (m *ListMusicHomePageCategoryResp_Category) XXX_Size() int {
	return xxx_messageInfo_ListMusicHomePageCategoryResp_Category.Size(m)
}
func (m *ListMusicHomePageCategoryResp_Category) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicHomePageCategoryResp_Category.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicHomePageCategoryResp_Category proto.InternalMessageInfo

func (m *ListMusicHomePageCategoryResp_Category) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ListMusicHomePageCategoryResp_Category) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ListMusicHomePageCategoryResp_Category) GetTabs() []*ListMusicHomePageCategoryResp_Tab {
	if m != nil {
		return m.Tabs
	}
	return nil
}

type ListMusicHomePageCategoryResp_Tab struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	TabType              int32    `protobuf:"varint,3,opt,name=tab_type,json=tabType,proto3" json:"tab_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListMusicHomePageCategoryResp_Tab) Reset()         { *m = ListMusicHomePageCategoryResp_Tab{} }
func (m *ListMusicHomePageCategoryResp_Tab) String() string { return proto.CompactTextString(m) }
func (*ListMusicHomePageCategoryResp_Tab) ProtoMessage()    {}
func (*ListMusicHomePageCategoryResp_Tab) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{21, 1}
}
func (m *ListMusicHomePageCategoryResp_Tab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicHomePageCategoryResp_Tab.Unmarshal(m, b)
}
func (m *ListMusicHomePageCategoryResp_Tab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicHomePageCategoryResp_Tab.Marshal(b, m, deterministic)
}
func (dst *ListMusicHomePageCategoryResp_Tab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicHomePageCategoryResp_Tab.Merge(dst, src)
}
func (m *ListMusicHomePageCategoryResp_Tab) XXX_Size() int {
	return xxx_messageInfo_ListMusicHomePageCategoryResp_Tab.Size(m)
}
func (m *ListMusicHomePageCategoryResp_Tab) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicHomePageCategoryResp_Tab.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicHomePageCategoryResp_Tab proto.InternalMessageInfo

func (m *ListMusicHomePageCategoryResp_Tab) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ListMusicHomePageCategoryResp_Tab) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ListMusicHomePageCategoryResp_Tab) GetTabType() int32 {
	if m != nil {
		return m.TabType
	}
	return 0
}

type ListMusicHomePageBlockReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListMusicHomePageBlockReq) Reset()         { *m = ListMusicHomePageBlockReq{} }
func (m *ListMusicHomePageBlockReq) String() string { return proto.CompactTextString(m) }
func (*ListMusicHomePageBlockReq) ProtoMessage()    {}
func (*ListMusicHomePageBlockReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{22}
}
func (m *ListMusicHomePageBlockReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicHomePageBlockReq.Unmarshal(m, b)
}
func (m *ListMusicHomePageBlockReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicHomePageBlockReq.Marshal(b, m, deterministic)
}
func (dst *ListMusicHomePageBlockReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicHomePageBlockReq.Merge(dst, src)
}
func (m *ListMusicHomePageBlockReq) XXX_Size() int {
	return xxx_messageInfo_ListMusicHomePageBlockReq.Size(m)
}
func (m *ListMusicHomePageBlockReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicHomePageBlockReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicHomePageBlockReq proto.InternalMessageInfo

func (m *ListMusicHomePageBlockReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListMusicHomePageBlockReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type ListMusicHomePageBlockResp struct {
	BaseResp             *app.BaseResp                       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Blocks               []*ListMusicHomePageBlockResp_Block `protobuf:"bytes,2,rep,name=blocks,proto3" json:"blocks,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *ListMusicHomePageBlockResp) Reset()         { *m = ListMusicHomePageBlockResp{} }
func (m *ListMusicHomePageBlockResp) String() string { return proto.CompactTextString(m) }
func (*ListMusicHomePageBlockResp) ProtoMessage()    {}
func (*ListMusicHomePageBlockResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{23}
}
func (m *ListMusicHomePageBlockResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicHomePageBlockResp.Unmarshal(m, b)
}
func (m *ListMusicHomePageBlockResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicHomePageBlockResp.Marshal(b, m, deterministic)
}
func (dst *ListMusicHomePageBlockResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicHomePageBlockResp.Merge(dst, src)
}
func (m *ListMusicHomePageBlockResp) XXX_Size() int {
	return xxx_messageInfo_ListMusicHomePageBlockResp.Size(m)
}
func (m *ListMusicHomePageBlockResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicHomePageBlockResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicHomePageBlockResp proto.InternalMessageInfo

func (m *ListMusicHomePageBlockResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListMusicHomePageBlockResp) GetBlocks() []*ListMusicHomePageBlockResp_Block {
	if m != nil {
		return m.Blocks
	}
	return nil
}

type ListMusicHomePageBlockResp_Block struct {
	Id                   uint32                                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Desc                 string                                `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	Elements             []*ListMusicHomePageBlockResp_Element `protobuf:"bytes,3,rep,name=elements,proto3" json:"elements,omitempty"`
	Mode                 ListMusicHomePageBlockResp_Block_Mode `protobuf:"varint,4,opt,name=mode,proto3,enum=ga.hobby_channel.ListMusicHomePageBlockResp_Block_Mode" json:"mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *ListMusicHomePageBlockResp_Block) Reset()         { *m = ListMusicHomePageBlockResp_Block{} }
func (m *ListMusicHomePageBlockResp_Block) String() string { return proto.CompactTextString(m) }
func (*ListMusicHomePageBlockResp_Block) ProtoMessage()    {}
func (*ListMusicHomePageBlockResp_Block) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{23, 0}
}
func (m *ListMusicHomePageBlockResp_Block) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicHomePageBlockResp_Block.Unmarshal(m, b)
}
func (m *ListMusicHomePageBlockResp_Block) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicHomePageBlockResp_Block.Marshal(b, m, deterministic)
}
func (dst *ListMusicHomePageBlockResp_Block) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicHomePageBlockResp_Block.Merge(dst, src)
}
func (m *ListMusicHomePageBlockResp_Block) XXX_Size() int {
	return xxx_messageInfo_ListMusicHomePageBlockResp_Block.Size(m)
}
func (m *ListMusicHomePageBlockResp_Block) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicHomePageBlockResp_Block.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicHomePageBlockResp_Block proto.InternalMessageInfo

func (m *ListMusicHomePageBlockResp_Block) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ListMusicHomePageBlockResp_Block) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ListMusicHomePageBlockResp_Block) GetElements() []*ListMusicHomePageBlockResp_Element {
	if m != nil {
		return m.Elements
	}
	return nil
}

func (m *ListMusicHomePageBlockResp_Block) GetMode() ListMusicHomePageBlockResp_Block_Mode {
	if m != nil {
		return m.Mode
	}
	return ListMusicHomePageBlockResp_Block_SINGLE
}

type ListMusicHomePageBlockResp_Element struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListMusicHomePageBlockResp_Element) Reset()         { *m = ListMusicHomePageBlockResp_Element{} }
func (m *ListMusicHomePageBlockResp_Element) String() string { return proto.CompactTextString(m) }
func (*ListMusicHomePageBlockResp_Element) ProtoMessage()    {}
func (*ListMusicHomePageBlockResp_Element) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{23, 1}
}
func (m *ListMusicHomePageBlockResp_Element) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicHomePageBlockResp_Element.Unmarshal(m, b)
}
func (m *ListMusicHomePageBlockResp_Element) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicHomePageBlockResp_Element.Marshal(b, m, deterministic)
}
func (dst *ListMusicHomePageBlockResp_Element) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicHomePageBlockResp_Element.Merge(dst, src)
}
func (m *ListMusicHomePageBlockResp_Element) XXX_Size() int {
	return xxx_messageInfo_ListMusicHomePageBlockResp_Element.Size(m)
}
func (m *ListMusicHomePageBlockResp_Element) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicHomePageBlockResp_Element.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicHomePageBlockResp_Element proto.InternalMessageInfo

func (m *ListMusicHomePageBlockResp_Element) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ListMusicHomePageBlockResp_Element) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type GetGameHomePageDIYFilterReq struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	Entry_Type           MysteryEntryType `protobuf:"varint,2,opt,name=entry_Type,json=entryType,proto3,enum=ga.hobby_channel.MysteryEntryType" json:"entry_Type,omitempty"`
	ChannelPkg           string           `protobuf:"bytes,3,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGameHomePageDIYFilterReq) Reset()         { *m = GetGameHomePageDIYFilterReq{} }
func (m *GetGameHomePageDIYFilterReq) String() string { return proto.CompactTextString(m) }
func (*GetGameHomePageDIYFilterReq) ProtoMessage()    {}
func (*GetGameHomePageDIYFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{24}
}
func (m *GetGameHomePageDIYFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameHomePageDIYFilterReq.Unmarshal(m, b)
}
func (m *GetGameHomePageDIYFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameHomePageDIYFilterReq.Marshal(b, m, deterministic)
}
func (dst *GetGameHomePageDIYFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameHomePageDIYFilterReq.Merge(dst, src)
}
func (m *GetGameHomePageDIYFilterReq) XXX_Size() int {
	return xxx_messageInfo_GetGameHomePageDIYFilterReq.Size(m)
}
func (m *GetGameHomePageDIYFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameHomePageDIYFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameHomePageDIYFilterReq proto.InternalMessageInfo

func (m *GetGameHomePageDIYFilterReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameHomePageDIYFilterReq) GetEntry_Type() MysteryEntryType {
	if m != nil {
		return m.Entry_Type
	}
	return MysteryEntryType_GameHomePageEntry
}

func (m *GetGameHomePageDIYFilterReq) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

type GetGameHomePageDIYFilterResp struct {
	BaseResp             *app.BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Items                []*GameHomePageFilterItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetGameHomePageDIYFilterResp) Reset()         { *m = GetGameHomePageDIYFilterResp{} }
func (m *GetGameHomePageDIYFilterResp) String() string { return proto.CompactTextString(m) }
func (*GetGameHomePageDIYFilterResp) ProtoMessage()    {}
func (*GetGameHomePageDIYFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{25}
}
func (m *GetGameHomePageDIYFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameHomePageDIYFilterResp.Unmarshal(m, b)
}
func (m *GetGameHomePageDIYFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameHomePageDIYFilterResp.Marshal(b, m, deterministic)
}
func (dst *GetGameHomePageDIYFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameHomePageDIYFilterResp.Merge(dst, src)
}
func (m *GetGameHomePageDIYFilterResp) XXX_Size() int {
	return xxx_messageInfo_GetGameHomePageDIYFilterResp.Size(m)
}
func (m *GetGameHomePageDIYFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameHomePageDIYFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameHomePageDIYFilterResp proto.InternalMessageInfo

func (m *GetGameHomePageDIYFilterResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGameHomePageDIYFilterResp) GetItems() []*GameHomePageFilterItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type GameHomePageFilterItem struct {
	TabId      uint32 `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	CategoryId uint32 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Title      string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// tab 里面的small_card_url
	TabImageUrl          string   `protobuf:"bytes,4,opt,name=tab_image_url,json=tabImageUrl,proto3" json:"tab_image_url,omitempty"`
	IsFallBackItem       bool     `protobuf:"varint,5,opt,name=is_fall_back_item,json=isFallBackItem,proto3" json:"is_fall_back_item,omitempty"`
	CategoryType         uint32   `protobuf:"varint,6,opt,name=category_type,json=categoryType,proto3" json:"category_type,omitempty"`
	HotText              string   `protobuf:"bytes,7,opt,name=hot_text,json=hotText,proto3" json:"hot_text,omitempty"`
	PcFastCategoryType   uint32   `protobuf:"varint,8,opt,name=pc_fast_category_type,json=pcFastCategoryType,proto3" json:"pc_fast_category_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameHomePageFilterItem) Reset()         { *m = GameHomePageFilterItem{} }
func (m *GameHomePageFilterItem) String() string { return proto.CompactTextString(m) }
func (*GameHomePageFilterItem) ProtoMessage()    {}
func (*GameHomePageFilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{26}
}
func (m *GameHomePageFilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameHomePageFilterItem.Unmarshal(m, b)
}
func (m *GameHomePageFilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameHomePageFilterItem.Marshal(b, m, deterministic)
}
func (dst *GameHomePageFilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameHomePageFilterItem.Merge(dst, src)
}
func (m *GameHomePageFilterItem) XXX_Size() int {
	return xxx_messageInfo_GameHomePageFilterItem.Size(m)
}
func (m *GameHomePageFilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameHomePageFilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameHomePageFilterItem proto.InternalMessageInfo

func (m *GameHomePageFilterItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GameHomePageFilterItem) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *GameHomePageFilterItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GameHomePageFilterItem) GetTabImageUrl() string {
	if m != nil {
		return m.TabImageUrl
	}
	return ""
}

func (m *GameHomePageFilterItem) GetIsFallBackItem() bool {
	if m != nil {
		return m.IsFallBackItem
	}
	return false
}

func (m *GameHomePageFilterItem) GetCategoryType() uint32 {
	if m != nil {
		return m.CategoryType
	}
	return 0
}

func (m *GameHomePageFilterItem) GetHotText() string {
	if m != nil {
		return m.HotText
	}
	return ""
}

func (m *GameHomePageFilterItem) GetPcFastCategoryType() uint32 {
	if m != nil {
		return m.PcFastCategoryType
	}
	return 0
}

type SetGameHomePageDIYFilterReq struct {
	BaseReq *app.BaseReq              `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Items   []*GameHomePageFilterItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	Entry_Type           MysteryEntryType `protobuf:"varint,3,opt,name=entry_Type,json=entryType,proto3,enum=ga.hobby_channel.MysteryEntryType" json:"entry_Type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SetGameHomePageDIYFilterReq) Reset()         { *m = SetGameHomePageDIYFilterReq{} }
func (m *SetGameHomePageDIYFilterReq) String() string { return proto.CompactTextString(m) }
func (*SetGameHomePageDIYFilterReq) ProtoMessage()    {}
func (*SetGameHomePageDIYFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{27}
}
func (m *SetGameHomePageDIYFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameHomePageDIYFilterReq.Unmarshal(m, b)
}
func (m *SetGameHomePageDIYFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameHomePageDIYFilterReq.Marshal(b, m, deterministic)
}
func (dst *SetGameHomePageDIYFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameHomePageDIYFilterReq.Merge(dst, src)
}
func (m *SetGameHomePageDIYFilterReq) XXX_Size() int {
	return xxx_messageInfo_SetGameHomePageDIYFilterReq.Size(m)
}
func (m *SetGameHomePageDIYFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameHomePageDIYFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameHomePageDIYFilterReq proto.InternalMessageInfo

func (m *SetGameHomePageDIYFilterReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetGameHomePageDIYFilterReq) GetItems() []*GameHomePageFilterItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *SetGameHomePageDIYFilterReq) GetEntry_Type() MysteryEntryType {
	if m != nil {
		return m.Entry_Type
	}
	return MysteryEntryType_GameHomePageEntry
}

type SetGameHomePageDIYFilterResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetGameHomePageDIYFilterResp) Reset()         { *m = SetGameHomePageDIYFilterResp{} }
func (m *SetGameHomePageDIYFilterResp) String() string { return proto.CompactTextString(m) }
func (*SetGameHomePageDIYFilterResp) ProtoMessage()    {}
func (*SetGameHomePageDIYFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{28}
}
func (m *SetGameHomePageDIYFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameHomePageDIYFilterResp.Unmarshal(m, b)
}
func (m *SetGameHomePageDIYFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameHomePageDIYFilterResp.Marshal(b, m, deterministic)
}
func (dst *SetGameHomePageDIYFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameHomePageDIYFilterResp.Merge(dst, src)
}
func (m *SetGameHomePageDIYFilterResp) XXX_Size() int {
	return xxx_messageInfo_SetGameHomePageDIYFilterResp.Size(m)
}
func (m *SetGameHomePageDIYFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameHomePageDIYFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameHomePageDIYFilterResp proto.InternalMessageInfo

func (m *SetGameHomePageDIYFilterResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetGameHomePageFilterReq struct {
	BaseReq     *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SelfGameIds []uint32     `protobuf:"varint,2,rep,packed,name=self_game_ids,json=selfGameIds,proto3" json:"self_game_ids,omitempty"`
	ActiveIds   []uint32     `protobuf:"varint,3,rep,packed,name=active_ids,json=activeIds,proto3" json:"active_ids,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	Entry_Type           MysteryEntryType `protobuf:"varint,4,opt,name=entry_Type,json=entryType,proto3,enum=ga.hobby_channel.MysteryEntryType" json:"entry_Type,omitempty"`
	ChannelPkg           string           `protobuf:"bytes,5,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGameHomePageFilterReq) Reset()         { *m = GetGameHomePageFilterReq{} }
func (m *GetGameHomePageFilterReq) String() string { return proto.CompactTextString(m) }
func (*GetGameHomePageFilterReq) ProtoMessage()    {}
func (*GetGameHomePageFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{29}
}
func (m *GetGameHomePageFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameHomePageFilterReq.Unmarshal(m, b)
}
func (m *GetGameHomePageFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameHomePageFilterReq.Marshal(b, m, deterministic)
}
func (dst *GetGameHomePageFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameHomePageFilterReq.Merge(dst, src)
}
func (m *GetGameHomePageFilterReq) XXX_Size() int {
	return xxx_messageInfo_GetGameHomePageFilterReq.Size(m)
}
func (m *GetGameHomePageFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameHomePageFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameHomePageFilterReq proto.InternalMessageInfo

func (m *GetGameHomePageFilterReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameHomePageFilterReq) GetSelfGameIds() []uint32 {
	if m != nil {
		return m.SelfGameIds
	}
	return nil
}

func (m *GetGameHomePageFilterReq) GetActiveIds() []uint32 {
	if m != nil {
		return m.ActiveIds
	}
	return nil
}

func (m *GetGameHomePageFilterReq) GetEntry_Type() MysteryEntryType {
	if m != nil {
		return m.Entry_Type
	}
	return MysteryEntryType_GameHomePageEntry
}

func (m *GetGameHomePageFilterReq) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

type GetGameHomePageFilterResp struct {
	BaseResp             *app.BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Items                []*GameHomePageFilterItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetGameHomePageFilterResp) Reset()         { *m = GetGameHomePageFilterResp{} }
func (m *GetGameHomePageFilterResp) String() string { return proto.CompactTextString(m) }
func (*GetGameHomePageFilterResp) ProtoMessage()    {}
func (*GetGameHomePageFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{30}
}
func (m *GetGameHomePageFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameHomePageFilterResp.Unmarshal(m, b)
}
func (m *GetGameHomePageFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameHomePageFilterResp.Marshal(b, m, deterministic)
}
func (dst *GetGameHomePageFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameHomePageFilterResp.Merge(dst, src)
}
func (m *GetGameHomePageFilterResp) XXX_Size() int {
	return xxx_messageInfo_GetGameHomePageFilterResp.Size(m)
}
func (m *GetGameHomePageFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameHomePageFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameHomePageFilterResp proto.InternalMessageInfo

func (m *GetGameHomePageFilterResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGameHomePageFilterResp) GetItems() []*GameHomePageFilterItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type GetGameTabByCategoryReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	CategoryId           uint32       `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	ChannelPkg           string       `protobuf:"bytes,3,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGameTabByCategoryReq) Reset()         { *m = GetGameTabByCategoryReq{} }
func (m *GetGameTabByCategoryReq) String() string { return proto.CompactTextString(m) }
func (*GetGameTabByCategoryReq) ProtoMessage()    {}
func (*GetGameTabByCategoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{31}
}
func (m *GetGameTabByCategoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTabByCategoryReq.Unmarshal(m, b)
}
func (m *GetGameTabByCategoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTabByCategoryReq.Marshal(b, m, deterministic)
}
func (dst *GetGameTabByCategoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTabByCategoryReq.Merge(dst, src)
}
func (m *GetGameTabByCategoryReq) XXX_Size() int {
	return xxx_messageInfo_GetGameTabByCategoryReq.Size(m)
}
func (m *GetGameTabByCategoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTabByCategoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTabByCategoryReq proto.InternalMessageInfo

func (m *GetGameTabByCategoryReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameTabByCategoryReq) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *GetGameTabByCategoryReq) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

type GetGameTabByCategoryResp struct {
	BaseResp             *app.BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Items                []*GameHomePageFilterItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetGameTabByCategoryResp) Reset()         { *m = GetGameTabByCategoryResp{} }
func (m *GetGameTabByCategoryResp) String() string { return proto.CompactTextString(m) }
func (*GetGameTabByCategoryResp) ProtoMessage()    {}
func (*GetGameTabByCategoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{32}
}
func (m *GetGameTabByCategoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTabByCategoryResp.Unmarshal(m, b)
}
func (m *GetGameTabByCategoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTabByCategoryResp.Marshal(b, m, deterministic)
}
func (dst *GetGameTabByCategoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTabByCategoryResp.Merge(dst, src)
}
func (m *GetGameTabByCategoryResp) XXX_Size() int {
	return xxx_messageInfo_GetGameTabByCategoryResp.Size(m)
}
func (m *GetGameTabByCategoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTabByCategoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTabByCategoryResp proto.InternalMessageInfo

func (m *GetGameTabByCategoryResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGameTabByCategoryResp) GetItems() []*GameHomePageFilterItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type GetMusicChannelFilterReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMusicChannelFilterReq) Reset()         { *m = GetMusicChannelFilterReq{} }
func (m *GetMusicChannelFilterReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelFilterReq) ProtoMessage()    {}
func (*GetMusicChannelFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{33}
}
func (m *GetMusicChannelFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelFilterReq.Unmarshal(m, b)
}
func (m *GetMusicChannelFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelFilterReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelFilterReq.Merge(dst, src)
}
func (m *GetMusicChannelFilterReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelFilterReq.Size(m)
}
func (m *GetMusicChannelFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelFilterReq proto.InternalMessageInfo

func (m *GetMusicChannelFilterReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetMusicChannelFilterResp struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	FilterItems          []*GetMusicChannelFilterResp_FilterItem `protobuf:"bytes,2,rep,name=filterItems,proto3" json:"filterItems,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *GetMusicChannelFilterResp) Reset()         { *m = GetMusicChannelFilterResp{} }
func (m *GetMusicChannelFilterResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelFilterResp) ProtoMessage()    {}
func (*GetMusicChannelFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{34}
}
func (m *GetMusicChannelFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelFilterResp.Unmarshal(m, b)
}
func (m *GetMusicChannelFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelFilterResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelFilterResp.Merge(dst, src)
}
func (m *GetMusicChannelFilterResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelFilterResp.Size(m)
}
func (m *GetMusicChannelFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelFilterResp proto.InternalMessageInfo

func (m *GetMusicChannelFilterResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusicChannelFilterResp) GetFilterItems() []*GetMusicChannelFilterResp_FilterItem {
	if m != nil {
		return m.FilterItems
	}
	return nil
}

type GetMusicChannelFilterResp_FilterItem struct {
	Title                string                       `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	TabId                uint32                       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOptions         []*topic_channel.BlockOption `protobuf:"bytes,3,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetMusicChannelFilterResp_FilterItem) Reset()         { *m = GetMusicChannelFilterResp_FilterItem{} }
func (m *GetMusicChannelFilterResp_FilterItem) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelFilterResp_FilterItem) ProtoMessage()    {}
func (*GetMusicChannelFilterResp_FilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{34, 0}
}
func (m *GetMusicChannelFilterResp_FilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelFilterResp_FilterItem.Unmarshal(m, b)
}
func (m *GetMusicChannelFilterResp_FilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelFilterResp_FilterItem.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelFilterResp_FilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelFilterResp_FilterItem.Merge(dst, src)
}
func (m *GetMusicChannelFilterResp_FilterItem) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelFilterResp_FilterItem.Size(m)
}
func (m *GetMusicChannelFilterResp_FilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelFilterResp_FilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelFilterResp_FilterItem proto.InternalMessageInfo

func (m *GetMusicChannelFilterResp_FilterItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMusicChannelFilterResp_FilterItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetMusicChannelFilterResp_FilterItem) GetBlockOptions() []*topic_channel.BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

type GetChannelByNewUserTagReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabName              string       `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	ElemName             string       `protobuf:"bytes,3,opt,name=elem_name,json=elemName,proto3" json:"elem_name,omitempty"`
	PromotionLabels      []string     `protobuf:"bytes,4,rep,name=promotion_labels,json=promotionLabels,proto3" json:"promotion_labels,omitempty"`
	ElemNameList         []string     `protobuf:"bytes,5,rep,name=elem_name_list,json=elemNameList,proto3" json:"elem_name_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelByNewUserTagReq) Reset()         { *m = GetChannelByNewUserTagReq{} }
func (m *GetChannelByNewUserTagReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelByNewUserTagReq) ProtoMessage()    {}
func (*GetChannelByNewUserTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{35}
}
func (m *GetChannelByNewUserTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelByNewUserTagReq.Unmarshal(m, b)
}
func (m *GetChannelByNewUserTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelByNewUserTagReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelByNewUserTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelByNewUserTagReq.Merge(dst, src)
}
func (m *GetChannelByNewUserTagReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelByNewUserTagReq.Size(m)
}
func (m *GetChannelByNewUserTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelByNewUserTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelByNewUserTagReq proto.InternalMessageInfo

func (m *GetChannelByNewUserTagReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelByNewUserTagReq) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *GetChannelByNewUserTagReq) GetElemName() string {
	if m != nil {
		return m.ElemName
	}
	return ""
}

func (m *GetChannelByNewUserTagReq) GetPromotionLabels() []string {
	if m != nil {
		return m.PromotionLabels
	}
	return nil
}

func (m *GetChannelByNewUserTagReq) GetElemNameList() []string {
	if m != nil {
		return m.ElemNameList
	}
	return nil
}

type GetChannelByNewUserTagResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MetaId               string        `protobuf:"bytes,3,opt,name=meta_id,json=metaId,proto3" json:"meta_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelByNewUserTagResp) Reset()         { *m = GetChannelByNewUserTagResp{} }
func (m *GetChannelByNewUserTagResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelByNewUserTagResp) ProtoMessage()    {}
func (*GetChannelByNewUserTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{36}
}
func (m *GetChannelByNewUserTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelByNewUserTagResp.Unmarshal(m, b)
}
func (m *GetChannelByNewUserTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelByNewUserTagResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelByNewUserTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelByNewUserTagResp.Merge(dst, src)
}
func (m *GetChannelByNewUserTagResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelByNewUserTagResp.Size(m)
}
func (m *GetChannelByNewUserTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelByNewUserTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelByNewUserTagResp proto.InternalMessageInfo

func (m *GetChannelByNewUserTagResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelByNewUserTagResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelByNewUserTagResp) GetMetaId() string {
	if m != nil {
		return m.MetaId
	}
	return ""
}

type ListHobbyElementConfReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ElemName             []string     `protobuf:"bytes,2,rep,name=elem_name,json=elemName,proto3" json:"elem_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListHobbyElementConfReq) Reset()         { *m = ListHobbyElementConfReq{} }
func (m *ListHobbyElementConfReq) String() string { return proto.CompactTextString(m) }
func (*ListHobbyElementConfReq) ProtoMessage()    {}
func (*ListHobbyElementConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{37}
}
func (m *ListHobbyElementConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListHobbyElementConfReq.Unmarshal(m, b)
}
func (m *ListHobbyElementConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListHobbyElementConfReq.Marshal(b, m, deterministic)
}
func (dst *ListHobbyElementConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListHobbyElementConfReq.Merge(dst, src)
}
func (m *ListHobbyElementConfReq) XXX_Size() int {
	return xxx_messageInfo_ListHobbyElementConfReq.Size(m)
}
func (m *ListHobbyElementConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListHobbyElementConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListHobbyElementConfReq proto.InternalMessageInfo

func (m *ListHobbyElementConfReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListHobbyElementConfReq) GetElemName() []string {
	if m != nil {
		return m.ElemName
	}
	return nil
}

type ListHobbyElementConfResp struct {
	BaseResp             *app.BaseResp                             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Configs              []*ListHobbyElementConfResp_ElementConfig `protobuf:"bytes,2,rep,name=configs,proto3" json:"configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                  `json:"-"`
	XXX_unrecognized     []byte                                    `json:"-"`
	XXX_sizecache        int32                                     `json:"-"`
}

func (m *ListHobbyElementConfResp) Reset()         { *m = ListHobbyElementConfResp{} }
func (m *ListHobbyElementConfResp) String() string { return proto.CompactTextString(m) }
func (*ListHobbyElementConfResp) ProtoMessage()    {}
func (*ListHobbyElementConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{38}
}
func (m *ListHobbyElementConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListHobbyElementConfResp.Unmarshal(m, b)
}
func (m *ListHobbyElementConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListHobbyElementConfResp.Marshal(b, m, deterministic)
}
func (dst *ListHobbyElementConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListHobbyElementConfResp.Merge(dst, src)
}
func (m *ListHobbyElementConfResp) XXX_Size() int {
	return xxx_messageInfo_ListHobbyElementConfResp.Size(m)
}
func (m *ListHobbyElementConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListHobbyElementConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListHobbyElementConfResp proto.InternalMessageInfo

func (m *ListHobbyElementConfResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListHobbyElementConfResp) GetConfigs() []*ListHobbyElementConfResp_ElementConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

type ListHobbyElementConfResp_ElementConfig struct {
	ElementName          string   `protobuf:"bytes,3,opt,name=element_name,json=elementName,proto3" json:"element_name,omitempty"`
	VideoUrl             string   `protobuf:"bytes,4,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	VideoType            string   `protobuf:"bytes,5,opt,name=video_type,json=videoType,proto3" json:"video_type,omitempty"`
	ImgUrl               string   `protobuf:"bytes,6,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	TabId                uint32   `protobuf:"varint,7,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,8,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListHobbyElementConfResp_ElementConfig) Reset() {
	*m = ListHobbyElementConfResp_ElementConfig{}
}
func (m *ListHobbyElementConfResp_ElementConfig) String() string { return proto.CompactTextString(m) }
func (*ListHobbyElementConfResp_ElementConfig) ProtoMessage()    {}
func (*ListHobbyElementConfResp_ElementConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{38, 0}
}
func (m *ListHobbyElementConfResp_ElementConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListHobbyElementConfResp_ElementConfig.Unmarshal(m, b)
}
func (m *ListHobbyElementConfResp_ElementConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListHobbyElementConfResp_ElementConfig.Marshal(b, m, deterministic)
}
func (dst *ListHobbyElementConfResp_ElementConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListHobbyElementConfResp_ElementConfig.Merge(dst, src)
}
func (m *ListHobbyElementConfResp_ElementConfig) XXX_Size() int {
	return xxx_messageInfo_ListHobbyElementConfResp_ElementConfig.Size(m)
}
func (m *ListHobbyElementConfResp_ElementConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_ListHobbyElementConfResp_ElementConfig.DiscardUnknown(m)
}

var xxx_messageInfo_ListHobbyElementConfResp_ElementConfig proto.InternalMessageInfo

func (m *ListHobbyElementConfResp_ElementConfig) GetElementName() string {
	if m != nil {
		return m.ElementName
	}
	return ""
}

func (m *ListHobbyElementConfResp_ElementConfig) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *ListHobbyElementConfResp_ElementConfig) GetVideoType() string {
	if m != nil {
		return m.VideoType
	}
	return ""
}

func (m *ListHobbyElementConfResp_ElementConfig) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *ListHobbyElementConfResp_ElementConfig) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ListHobbyElementConfResp_ElementConfig) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

type GetAllQuickPlayConfReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PromotionLabels      []string     `protobuf:"bytes,2,rep,name=promotion_labels,json=promotionLabels,proto3" json:"promotion_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAllQuickPlayConfReq) Reset()         { *m = GetAllQuickPlayConfReq{} }
func (m *GetAllQuickPlayConfReq) String() string { return proto.CompactTextString(m) }
func (*GetAllQuickPlayConfReq) ProtoMessage()    {}
func (*GetAllQuickPlayConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{39}
}
func (m *GetAllQuickPlayConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllQuickPlayConfReq.Unmarshal(m, b)
}
func (m *GetAllQuickPlayConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllQuickPlayConfReq.Marshal(b, m, deterministic)
}
func (dst *GetAllQuickPlayConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllQuickPlayConfReq.Merge(dst, src)
}
func (m *GetAllQuickPlayConfReq) XXX_Size() int {
	return xxx_messageInfo_GetAllQuickPlayConfReq.Size(m)
}
func (m *GetAllQuickPlayConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllQuickPlayConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllQuickPlayConfReq proto.InternalMessageInfo

func (m *GetAllQuickPlayConfReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetAllQuickPlayConfReq) GetPromotionLabels() []string {
	if m != nil {
		return m.PromotionLabels
	}
	return nil
}

type GetAllQuickPlayConfResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CardList             []*QuickPlayCard `protobuf:"bytes,2,rep,name=card_list,json=cardList,proto3" json:"card_list,omitempty"`
	DefaultCard          *QuickPlayCard   `protobuf:"bytes,3,opt,name=default_card,json=defaultCard,proto3" json:"default_card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetAllQuickPlayConfResp) Reset()         { *m = GetAllQuickPlayConfResp{} }
func (m *GetAllQuickPlayConfResp) String() string { return proto.CompactTextString(m) }
func (*GetAllQuickPlayConfResp) ProtoMessage()    {}
func (*GetAllQuickPlayConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{40}
}
func (m *GetAllQuickPlayConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllQuickPlayConfResp.Unmarshal(m, b)
}
func (m *GetAllQuickPlayConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllQuickPlayConfResp.Marshal(b, m, deterministic)
}
func (dst *GetAllQuickPlayConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllQuickPlayConfResp.Merge(dst, src)
}
func (m *GetAllQuickPlayConfResp) XXX_Size() int {
	return xxx_messageInfo_GetAllQuickPlayConfResp.Size(m)
}
func (m *GetAllQuickPlayConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllQuickPlayConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllQuickPlayConfResp proto.InternalMessageInfo

func (m *GetAllQuickPlayConfResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAllQuickPlayConfResp) GetCardList() []*QuickPlayCard {
	if m != nil {
		return m.CardList
	}
	return nil
}

func (m *GetAllQuickPlayConfResp) GetDefaultCard() *QuickPlayCard {
	if m != nil {
		return m.DefaultCard
	}
	return nil
}

type QuickPlayCard struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Img                  string   `protobuf:"bytes,2,opt,name=img,proto3" json:"img,omitempty"`
	TabName              string   `protobuf:"bytes,3,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	ElemNameList         []string `protobuf:"bytes,4,rep,name=elem_name_list,json=elemNameList,proto3" json:"elem_name_list,omitempty"`
	IsAnimation          bool     `protobuf:"varint,5,opt,name=is_animation,json=isAnimation,proto3" json:"is_animation,omitempty"`
	Color                string   `protobuf:"bytes,6,opt,name=color,proto3" json:"color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuickPlayCard) Reset()         { *m = QuickPlayCard{} }
func (m *QuickPlayCard) String() string { return proto.CompactTextString(m) }
func (*QuickPlayCard) ProtoMessage()    {}
func (*QuickPlayCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel__82e22106f8a539c0, []int{41}
}
func (m *QuickPlayCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuickPlayCard.Unmarshal(m, b)
}
func (m *QuickPlayCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuickPlayCard.Marshal(b, m, deterministic)
}
func (dst *QuickPlayCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuickPlayCard.Merge(dst, src)
}
func (m *QuickPlayCard) XXX_Size() int {
	return xxx_messageInfo_QuickPlayCard.Size(m)
}
func (m *QuickPlayCard) XXX_DiscardUnknown() {
	xxx_messageInfo_QuickPlayCard.DiscardUnknown(m)
}

var xxx_messageInfo_QuickPlayCard proto.InternalMessageInfo

func (m *QuickPlayCard) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *QuickPlayCard) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *QuickPlayCard) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *QuickPlayCard) GetElemNameList() []string {
	if m != nil {
		return m.ElemNameList
	}
	return nil
}

func (m *QuickPlayCard) GetIsAnimation() bool {
	if m != nil {
		return m.IsAnimation
	}
	return false
}

func (m *QuickPlayCard) GetColor() string {
	if m != nil {
		return m.Color
	}
	return ""
}

func init() {
	proto.RegisterType((*CreateHobbyChannelReq)(nil), "ga.hobby_channel.CreateHobbyChannelReq")
	proto.RegisterType((*CreateHobbyChannelResp)(nil), "ga.hobby_channel.CreateHobbyChannelResp")
	proto.RegisterType((*SwitchHobbyChannelSubjectReq)(nil), "ga.hobby_channel.SwitchHobbyChannelSubjectReq")
	proto.RegisterType((*SwitchHobbyChannelSubjectResp)(nil), "ga.hobby_channel.SwitchHobbyChannelSubjectResp")
	proto.RegisterType((*PublishHobbyChannelReq)(nil), "ga.hobby_channel.PublishHobbyChannelReq")
	proto.RegisterType((*PublishHobbyChannelResp)(nil), "ga.hobby_channel.PublishHobbyChannelResp")
	proto.RegisterType((*ListHobbyChannelReq)(nil), "ga.hobby_channel.ListHobbyChannelReq")
	proto.RegisterType((*ListHobbyChannelResp)(nil), "ga.hobby_channel.ListHobbyChannelResp")
	proto.RegisterType((*ListHobbyChannelResp_HobbyChannelItem)(nil), "ga.hobby_channel.ListHobbyChannelResp.HobbyChannelItem")
	proto.RegisterType((*QuickMatchHobbyChannelReq)(nil), "ga.hobby_channel.QuickMatchHobbyChannelReq")
	proto.RegisterType((*QuickMatchHobbyChannelResp)(nil), "ga.hobby_channel.QuickMatchHobbyChannelResp")
	proto.RegisterType((*KeepHobbyChannelPublishReq)(nil), "ga.hobby_channel.KeepHobbyChannelPublishReq")
	proto.RegisterType((*KeepHobbyChannelPublishResp)(nil), "ga.hobby_channel.KeepHobbyChannelPublishResp")
	proto.RegisterType((*CancelHobbyChannelPublishReq)(nil), "ga.hobby_channel.CancelHobbyChannelPublishReq")
	proto.RegisterType((*CancelHobbyChannelPublishResp)(nil), "ga.hobby_channel.CancelHobbyChannelPublishResp")
	proto.RegisterType((*GetMusicHomePageViewReq)(nil), "ga.hobby_channel.GetMusicHomePageViewReq")
	proto.RegisterType((*GetMusicHomePageViewResp)(nil), "ga.hobby_channel.GetMusicHomePageViewResp")
	proto.RegisterType((*MusicHomePageView)(nil), "ga.hobby_channel.MusicHomePageView")
	proto.RegisterType((*GetMusicHomePageDialogReq)(nil), "ga.hobby_channel.GetMusicHomePageDialogReq")
	proto.RegisterType((*GetMusicHomePageDialogResp)(nil), "ga.hobby_channel.GetMusicHomePageDialogResp")
	proto.RegisterType((*GetMusicHomePageDialogView)(nil), "ga.hobby_channel.GetMusicHomePageDialogView")
	proto.RegisterType((*ListMusicHomePageCategoryReq)(nil), "ga.hobby_channel.ListMusicHomePageCategoryReq")
	proto.RegisterType((*ListMusicHomePageCategoryResp)(nil), "ga.hobby_channel.ListMusicHomePageCategoryResp")
	proto.RegisterType((*ListMusicHomePageCategoryResp_Category)(nil), "ga.hobby_channel.ListMusicHomePageCategoryResp.Category")
	proto.RegisterType((*ListMusicHomePageCategoryResp_Tab)(nil), "ga.hobby_channel.ListMusicHomePageCategoryResp.Tab")
	proto.RegisterType((*ListMusicHomePageBlockReq)(nil), "ga.hobby_channel.ListMusicHomePageBlockReq")
	proto.RegisterType((*ListMusicHomePageBlockResp)(nil), "ga.hobby_channel.ListMusicHomePageBlockResp")
	proto.RegisterType((*ListMusicHomePageBlockResp_Block)(nil), "ga.hobby_channel.ListMusicHomePageBlockResp.Block")
	proto.RegisterType((*ListMusicHomePageBlockResp_Element)(nil), "ga.hobby_channel.ListMusicHomePageBlockResp.Element")
	proto.RegisterType((*GetGameHomePageDIYFilterReq)(nil), "ga.hobby_channel.GetGameHomePageDIYFilterReq")
	proto.RegisterType((*GetGameHomePageDIYFilterResp)(nil), "ga.hobby_channel.GetGameHomePageDIYFilterResp")
	proto.RegisterType((*GameHomePageFilterItem)(nil), "ga.hobby_channel.GameHomePageFilterItem")
	proto.RegisterType((*SetGameHomePageDIYFilterReq)(nil), "ga.hobby_channel.SetGameHomePageDIYFilterReq")
	proto.RegisterType((*SetGameHomePageDIYFilterResp)(nil), "ga.hobby_channel.SetGameHomePageDIYFilterResp")
	proto.RegisterType((*GetGameHomePageFilterReq)(nil), "ga.hobby_channel.GetGameHomePageFilterReq")
	proto.RegisterType((*GetGameHomePageFilterResp)(nil), "ga.hobby_channel.GetGameHomePageFilterResp")
	proto.RegisterType((*GetGameTabByCategoryReq)(nil), "ga.hobby_channel.GetGameTabByCategoryReq")
	proto.RegisterType((*GetGameTabByCategoryResp)(nil), "ga.hobby_channel.GetGameTabByCategoryResp")
	proto.RegisterType((*GetMusicChannelFilterReq)(nil), "ga.hobby_channel.GetMusicChannelFilterReq")
	proto.RegisterType((*GetMusicChannelFilterResp)(nil), "ga.hobby_channel.GetMusicChannelFilterResp")
	proto.RegisterType((*GetMusicChannelFilterResp_FilterItem)(nil), "ga.hobby_channel.GetMusicChannelFilterResp.FilterItem")
	proto.RegisterType((*GetChannelByNewUserTagReq)(nil), "ga.hobby_channel.GetChannelByNewUserTagReq")
	proto.RegisterType((*GetChannelByNewUserTagResp)(nil), "ga.hobby_channel.GetChannelByNewUserTagResp")
	proto.RegisterType((*ListHobbyElementConfReq)(nil), "ga.hobby_channel.ListHobbyElementConfReq")
	proto.RegisterType((*ListHobbyElementConfResp)(nil), "ga.hobby_channel.ListHobbyElementConfResp")
	proto.RegisterType((*ListHobbyElementConfResp_ElementConfig)(nil), "ga.hobby_channel.ListHobbyElementConfResp.ElementConfig")
	proto.RegisterType((*GetAllQuickPlayConfReq)(nil), "ga.hobby_channel.GetAllQuickPlayConfReq")
	proto.RegisterType((*GetAllQuickPlayConfResp)(nil), "ga.hobby_channel.GetAllQuickPlayConfResp")
	proto.RegisterType((*QuickPlayCard)(nil), "ga.hobby_channel.QuickPlayCard")
	proto.RegisterEnum("ga.hobby_channel.CreateChannelSource", CreateChannelSource_name, CreateChannelSource_value)
	proto.RegisterEnum("ga.hobby_channel.SwitchSource", SwitchSource_name, SwitchSource_value)
	proto.RegisterEnum("ga.hobby_channel.MusicHomePageViewType", MusicHomePageViewType_name, MusicHomePageViewType_value)
	proto.RegisterEnum("ga.hobby_channel.MysteryEntryType", MysteryEntryType_name, MysteryEntryType_value)
	proto.RegisterEnum("ga.hobby_channel.MusicHomePageView_ActionType", MusicHomePageView_ActionType_name, MusicHomePageView_ActionType_value)
	proto.RegisterEnum("ga.hobby_channel.ListMusicHomePageBlockResp_Block_Mode", ListMusicHomePageBlockResp_Block_Mode_name, ListMusicHomePageBlockResp_Block_Mode_value)
}

func init() {
	proto.RegisterFile("hobby_channel/hobby-channel_.proto", fileDescriptor_hobby_channel__82e22106f8a539c0)
}

var fileDescriptor_hobby_channel__82e22106f8a539c0 = []byte{
	// 2868 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5a, 0xcd, 0x6f, 0x23, 0xc7,
	0xb1, 0xdf, 0xe1, 0x87, 0x44, 0x16, 0x49, 0x69, 0x76, 0x56, 0x5a, 0x51, 0x5c, 0x09, 0x2b, 0xcf,
	0xfa, 0x43, 0x5e, 0xd8, 0xdc, 0xf7, 0x64, 0xef, 0xf3, 0x03, 0xde, 0x43, 0x00, 0x89, 0xfa, 0x58,
	0xee, 0x4a, 0x2b, 0x79, 0x48, 0xd9, 0xb1, 0x0f, 0x69, 0xf4, 0xcc, 0x34, 0x87, 0x13, 0x0d, 0xa7,
	0xb9, 0xd3, 0x43, 0x6b, 0x19, 0x20, 0x1f, 0x40, 0x90, 0x00, 0x41, 0x2e, 0xf9, 0x07, 0x82, 0x20,
	0x97, 0x20, 0xb9, 0x38, 0x87, 0x9c, 0x72, 0xcb, 0x25, 0x41, 0x0e, 0xb9, 0x06, 0xc8, 0xc9, 0x97,
	0x9c, 0xfc, 0x5f, 0x04, 0xdd, 0x3d, 0x43, 0x0e, 0x45, 0x72, 0xad, 0x59, 0xaf, 0x7d, 0x9b, 0xae,
	0xea, 0xae, 0xaa, 0xae, 0xfa, 0x55, 0x75, 0x75, 0x93, 0xa0, 0x77, 0xa9, 0x69, 0x0e, 0x91, 0xd5,
	0xc5, 0xbe, 0x4f, 0xbc, 0x07, 0x62, 0xf4, 0x6e, 0x34, 0x42, 0xf5, 0x7e, 0x40, 0x43, 0xaa, 0xa9,
	0x0e, 0xae, 0x4f, 0x4c, 0xab, 0x55, 0x1c, 0x8c, 0x4c, 0xcc, 0x88, 0x9c, 0x50, 0xd3, 0x43, 0xda,
	0x77, 0xad, 0x91, 0x90, 0x89, 0x51, 0x24, 0xa4, 0xf6, 0xb0, 0x37, 0x60, 0x04, 0xb9, 0x7e, 0x48,
	0x02, 0xc2, 0x42, 0xd4, 0x1d, 0x98, 0xc8, 0xa3, 0x8e, 0x6b, 0x3d, 0x98, 0x43, 0x97, 0xcb, 0xf4,
	0xbf, 0x2a, 0xb0, 0xda, 0x08, 0x08, 0x0e, 0xc9, 0x23, 0x6e, 0x41, 0x43, 0x0a, 0x35, 0xc8, 0x33,
	0xed, 0x4d, 0x28, 0x70, 0x13, 0x50, 0x40, 0x9e, 0x55, 0x95, 0x2d, 0x65, 0xbb, 0xb4, 0x53, 0xaa,
	0x3b, 0xb8, 0xbe, 0x87, 0x19, 0x31, 0xc8, 0x33, 0x63, 0xd1, 0x94, 0x1f, 0xda, 0x6b, 0x50, 0x8e,
	0x4d, 0xf1, 0x71, 0x8f, 0x54, 0x33, 0x5b, 0xca, 0x76, 0xd1, 0x28, 0x45, 0xb4, 0xa7, 0xb8, 0x47,
	0xb4, 0x55, 0x58, 0x08, 0xb1, 0x89, 0x5c, 0xbb, 0x9a, 0xdd, 0x52, 0xb6, 0x2b, 0x46, 0x3e, 0xc4,
	0x66, 0xd3, 0xd6, 0x1e, 0x43, 0xc5, 0x12, 0xaa, 0x11, 0xa3, 0x83, 0xc0, 0x22, 0xd5, 0xdc, 0x96,
	0xb2, 0xbd, 0xb4, 0xf3, 0x46, 0xfd, 0xaa, 0x3f, 0xea, 0xd2, 0xc2, 0xc8, 0xb8, 0x96, 0x98, 0x6c,
	0x94, 0xe5, 0x5a, 0x39, 0xd2, 0x4d, 0xb8, 0x3d, 0x6b, 0x1b, 0xac, 0xaf, 0xbd, 0x0d, 0xc5, 0x68,
	0x1f, 0xac, 0x1f, 0x6d, 0xa4, 0x3c, 0xde, 0x08, 0xeb, 0x1b, 0x05, 0x33, 0xfa, 0xd2, 0x36, 0x01,
	0xe2, 0xad, 0xb8, 0xb6, 0xd8, 0x48, 0xc5, 0x28, 0x46, 0x94, 0xa6, 0xad, 0xff, 0x56, 0x81, 0x8d,
	0xd6, 0xa5, 0x1b, 0x5a, 0xdd, 0xa4, 0x92, 0xd6, 0xc0, 0xfc, 0x3e, 0xb1, 0xc2, 0x34, 0x2e, 0x7b,
	0xb1, 0x9e, 0x79, 0xee, 0xba, 0x07, 0x15, 0x26, 0xb4, 0x27, 0xdd, 0x55, 0x31, 0xca, 0x92, 0x18,
	0xf9, 0xe1, 0x31, 0x6c, 0xbe, 0xc0, 0xc4, 0x54, 0xee, 0xd0, 0xff, 0x94, 0x85, 0xdb, 0x67, 0x03,
	0xd3, 0x73, 0x59, 0xf7, 0x65, 0xc1, 0xf1, 0x72, 0x3b, 0xdd, 0x83, 0x8a, 0xe9, 0x51, 0xeb, 0x02,
	0xd1, 0x7e, 0xe8, 0x52, 0x9f, 0x55, 0x73, 0x5b, 0xd9, 0xed, 0xd2, 0xce, 0x26, 0x57, 0x31, 0x01,
	0xfe, 0xfa, 0x1e, 0x9f, 0x76, 0x2a, 0x66, 0x19, 0x65, 0x73, 0x3c, 0x60, 0x53, 0xb0, 0xcc, 0x4f,
	0xc3, 0x52, 0x87, 0x8a, 0xcb, 0xd0, 0x25, 0xf6, 0x43, 0xd4, 0x09, 0x08, 0xeb, 0x56, 0x17, 0xb6,
	0x94, 0xed, 0x82, 0x51, 0x72, 0xd9, 0xc7, 0xd8, 0x0f, 0x0f, 0x39, 0x49, 0x7b, 0x0b, 0x54, 0x97,
	0x21, 0xd6, 0xa5, 0x97, 0xc8, 0x21, 0x14, 0xb9, 0x7e, 0x87, 0x56, 0x17, 0xc5, 0xb4, 0x8a, 0xcb,
	0x5a, 0x5d, 0x7a, 0x79, 0x44, 0x68, 0xd3, 0xef, 0x50, 0xed, 0x75, 0x58, 0xb2, 0xdd, 0x21, 0x92,
	0xb8, 0x15, 0x1a, 0xcb, 0x42, 0x63, 0xd9, 0x76, 0x87, 0xc2, 0x7b, 0x42, 0xe5, 0x7d, 0xb8, 0x89,
	0x3d, 0x0f, 0x31, 0xe2, 0x11, 0x2b, 0x24, 0x36, 0x32, 0x5d, 0x9b, 0x55, 0x2b, 0x5b, 0xd9, 0xed,
	0x8a, 0xb1, 0x8c, 0x3d, 0xaf, 0x15, 0xd1, 0xf7, 0x5c, 0x9b, 0x69, 0x0f, 0x61, 0xcd, 0x27, 0xc4,
	0x46, 0x56, 0x97, 0x58, 0x17, 0x68, 0x62, 0x33, 0x4b, 0xc2, 0x82, 0x15, 0xce, 0x6e, 0x70, 0x6e,
	0x63, 0xbc, 0x2b, 0xfd, 0x77, 0x19, 0x58, 0x9b, 0x19, 0xb5, 0x74, 0xb9, 0xb0, 0x0d, 0x2a, 0x57,
	0xe9, 0x10, 0x64, 0x51, 0xea, 0x21, 0x9b, 0x5e, 0xfa, 0x51, 0xfc, 0x96, 0x24, 0xbd, 0x41, 0xa9,
	0xb7, 0x4f, 0x2f, 0x7d, 0xed, 0x2d, 0x58, 0xee, 0x04, 0x84, 0xfc, 0x80, 0x20, 0x7b, 0x10, 0x60,
	0xee, 0xfd, 0x28, 0x9a, 0x4b, 0x92, 0xbc, 0x1f, 0x51, 0xb5, 0x1d, 0x58, 0xc5, 0x83, 0x90, 0x22,
	0xdb, 0x65, 0x3d, 0x97, 0xb1, 0xf1, 0x74, 0x09, 0xe4, 0x5b, 0x9c, 0xb9, 0x2f, 0x79, 0xa3, 0x35,
	0x9f, 0xc0, 0x12, 0x23, 0x16, 0xf5, 0x6d, 0x1c, 0x0c, 0x91, 0x1b, 0x92, 0x5e, 0x35, 0x2f, 0xb0,
	0xb0, 0x33, 0x8d, 0x05, 0x1e, 0x8d, 0x36, 0xa7, 0x44, 0x3b, 0x6e, 0x63, 0xb3, 0x15, 0x2f, 0x6d,
	0x86, 0xa4, 0x67, 0x54, 0x58, 0x72, 0xa8, 0xff, 0x25, 0x03, 0xb7, 0x8e, 0x5d, 0x16, 0xbe, 0x2c,
	0xb6, 0x57, 0x20, 0x6f, 0xd1, 0x81, 0x1f, 0x46, 0x6e, 0x91, 0x83, 0x6f, 0x12, 0xd2, 0x2a, 0x64,
	0x19, 0x79, 0x2e, 0x90, 0x9c, 0x37, 0xf8, 0xa7, 0xf6, 0x0e, 0x68, 0x31, 0x2e, 0xfa, 0xd8, 0xba,
	0xc0, 0x0e, 0xe1, 0x8a, 0x17, 0x04, 0xf0, 0xd4, 0x88, 0x73, 0x26, 0x19, 0x4d, 0x3b, 0xc2, 0x3b,
	0x0b, 0x71, 0x10, 0x22, 0x8f, 0x62, 0x3b, 0x02, 0x72, 0xc9, 0x65, 0x2d, 0x4e, 0x3b, 0xa6, 0xd8,
	0xe6, 0x12, 0xc9, 0xf3, 0x3e, 0x65, 0x04, 0x8d, 0xf3, 0x96, 0x55, 0x0b, 0x02, 0xa1, 0xaa, 0xe4,
	0x34, 0xe2, 0xf4, 0x65, 0xfa, 0xbf, 0x0a, 0xb0, 0x32, 0xed, 0xc2, 0x74, 0x40, 0x3b, 0x81, 0x3c,
	0x8f, 0x2b, 0xab, 0x66, 0x84, 0x47, 0x3e, 0x98, 0xae, 0xfe, 0xb3, 0x34, 0xd4, 0x93, 0x04, 0x11,
	0x5d, 0x29, 0x45, 0x7b, 0x13, 0x96, 0x5d, 0x86, 0x4c, 0x1a, 0x86, 0xb4, 0x87, 0x02, 0x82, 0xad,
	0xae, 0x08, 0x84, 0xc8, 0xd7, 0x3d, 0x41, 0x35, 0x38, 0xb1, 0xf6, 0xeb, 0x45, 0x50, 0xaf, 0xca,
	0xb8, 0x52, 0xae, 0x94, 0xab, 0xe5, 0xea, 0x1a, 0x47, 0xdd, 0x0e, 0xac, 0xc6, 0x53, 0xe8, 0xa5,
	0x4f, 0x02, 0x84, 0x2d, 0x09, 0x92, 0xac, 0x98, 0x7b, 0x2b, 0x62, 0x9e, 0x72, 0xde, 0xae, 0x64,
	0xf1, 0xa2, 0x30, 0xb9, 0x86, 0x47, 0x39, 0x27, 0xa2, 0xbc, 0x9c, 0x9c, 0xdf, 0x22, 0xcf, 0xb5,
	0xff, 0x82, 0x95, 0x78, 0x6e, 0x8f, 0xf4, 0x4c, 0x12, 0x20, 0x29, 0x3e, 0x2f, 0x6c, 0x8d, 0xd1,
	0x70, 0x22, 0x58, 0x0d, 0x21, 0xfd, 0x1e, 0x54, 0xfa, 0xb2, 0x1c, 0x20, 0x0f, 0x9b, 0xc4, 0x8b,
	0xe0, 0x51, 0x8e, 0x88, 0xc7, 0x9c, 0xa6, 0xbd, 0x01, 0x4b, 0xf1, 0xa4, 0x80, 0x38, 0x3c, 0x27,
	0x17, 0xc5, 0xac, 0x78, 0xa9, 0x21, 0x88, 0xda, 0x1d, 0x28, 0x7e, 0xe6, 0x92, 0x4b, 0x14, 0x0e,
	0xfb, 0xa4, 0x5a, 0x10, 0x2a, 0x0b, 0x9c, 0xd0, 0x1e, 0xf6, 0x89, 0xb6, 0x05, 0xe5, 0xbe, 0x89,
	0x04, 0xdf, 0xc6, 0x21, 0xae, 0x16, 0xb7, 0x94, 0xed, 0xb2, 0x01, 0x7d, 0xf3, 0x23, 0x97, 0x5c,
	0xee, 0xe3, 0x10, 0x73, 0xf7, 0x06, 0x56, 0xcf, 0x8e, 0xec, 0x00, 0xe9, 0x5e, 0x4e, 0x91, 0x46,
	0xac, 0x43, 0x61, 0x54, 0x63, 0x4b, 0x42, 0xfd, 0xa2, 0x13, 0x55, 0x57, 0x0d, 0x72, 0xae, 0x45,
	0xfd, 0xa8, 0xa6, 0x8a, 0x6f, 0x1e, 0x8d, 0xd8, 0x66, 0x9b, 0x30, 0xab, 0x5a, 0x91, 0xd1, 0x88,
	0x68, 0xfb, 0x84, 0x59, 0x5c, 0x21, 0xa3, 0xbe, 0x83, 0x42, 0x37, 0xf4, 0x64, 0xd5, 0x2c, 0x1a,
	0x45, 0x4e, 0x69, 0x73, 0x02, 0x97, 0xd0, 0x75, 0x9d, 0x2e, 0x7a, 0x36, 0xc0, 0x9e, 0x1b, 0x0e,
	0xab, 0xcb, 0x32, 0x1f, 0x38, 0xed, 0x43, 0x49, 0x4a, 0xa4, 0xb3, 0x9a, 0x4c, 0xe7, 0x3b, 0x50,
	0x94, 0x7e, 0xe2, 0x9c, 0x9b, 0x5b, 0xca, 0x76, 0xce, 0x28, 0x48, 0x42, 0xd3, 0xd6, 0xee, 0x42,
	0xa9, 0x43, 0x3c, 0x8f, 0x5e, 0x22, 0x8b, 0x27, 0x8f, 0x26, 0x92, 0x07, 0x24, 0xa9, 0xc1, 0x2b,
	0xfb, 0x06, 0x14, 0x3b, 0x94, 0x86, 0xfd, 0xc0, 0xf5, 0xc3, 0xea, 0x2d, 0x69, 0xd5, 0x88, 0xc0,
	0x63, 0x11, 0x10, 0xe1, 0xc6, 0x18, 0x3b, 0x2b, 0x32, 0x16, 0x92, 0x1a, 0xa3, 0xe6, 0x2e, 0x94,
	0xa2, 0x69, 0x62, 0xf7, 0xab, 0x62, 0x0e, 0x48, 0x52, 0xbc, 0xf9, 0x68, 0x02, 0xc7, 0xd3, 0x6d,
	0x81, 0xa7, 0xa2, 0xa4, 0x70, 0x24, 0x2d, 0x41, 0xc6, 0x74, 0xaa, 0x6b, 0x62, 0x59, 0xc6, 0x74,
	0xb4, 0x35, 0x58, 0xb4, 0x71, 0x70, 0x81, 0x4c, 0xa7, 0x5a, 0x15, 0xc4, 0x05, 0x3e, 0xdc, 0x73,
	0xb4, 0x0f, 0xa1, 0xc8, 0x70, 0x8f, 0x20, 0x8b, 0xbb, 0x68, 0x5d, 0xe4, 0xf2, 0xfb, 0x3c, 0x49,
	0xe7, 0x35, 0x96, 0xc9, 0x1a, 0xdc, 0xc2, 0x3d, 0xd2, 0x70, 0xc3, 0x21, 0x0f, 0xa2, 0x51, 0x60,
	0xd1, 0x28, 0x99, 0x48, 0x02, 0x4a, 0x35, 0xe1, 0xdb, 0x38, 0x91, 0x38, 0x9a, 0xf4, 0x7f, 0x28,
	0xb0, 0xfe, 0xe1, 0xc0, 0xb5, 0x2e, 0x4e, 0xf0, 0x95, 0x6e, 0x26, 0x4d, 0x8d, 0x1e, 0x87, 0x2f,
	0xf3, 0xc2, 0x6a, 0x9c, 0x4d, 0x5f, 0x8d, 0x67, 0xd7, 0xde, 0xdc, 0xec, 0xda, 0xab, 0x77, 0xa0,
	0x36, 0x6f, 0x37, 0xaf, 0xb4, 0x47, 0xb5, 0xa0, 0xf6, 0x84, 0x90, 0x7e, 0x52, 0xc3, 0x59, 0x9c,
	0xc2, 0xaf, 0xaa, 0x6d, 0xd3, 0x3b, 0x70, 0x67, 0xae, 0x92, 0x74, 0xbb, 0xd9, 0x00, 0x70, 0x19,
	0x0a, 0xbb, 0x04, 0x11, 0x5f, 0x2a, 0x2a, 0x18, 0x05, 0x97, 0xb5, 0xbb, 0xe4, 0xc0, 0xb7, 0x75,
	0x02, 0x1b, 0x0d, 0xec, 0x5b, 0xc4, 0xfb, 0x66, 0xb7, 0xf3, 0x18, 0x36, 0x5f, 0xa0, 0x26, 0x5d,
	0xcf, 0xbc, 0x0b, 0x6b, 0x47, 0x24, 0x3c, 0x19, 0x30, 0xd7, 0x7a, 0x44, 0x7b, 0xe4, 0x0c, 0x3b,
	0x84, 0x97, 0xbf, 0x14, 0xd6, 0xea, 0x3f, 0x82, 0xea, 0x6c, 0x11, 0xe9, 0x5c, 0xfb, 0x01, 0xe4,
	0x78, 0xaa, 0x47, 0xc7, 0xea, 0xbd, 0xe9, 0x63, 0x75, 0x5a, 0x83, 0x58, 0xa0, 0xff, 0x2a, 0x0b,
	0x37, 0xa7, 0x78, 0xda, 0x7e, 0xb2, 0xf4, 0x2b, 0xe2, 0xa2, 0xf6, 0xd6, 0x35, 0x64, 0xf2, 0x5c,
	0x4e, 0x9c, 0x11, 0x2b, 0x90, 0x97, 0xb5, 0x58, 0x1e, 0x9d, 0x72, 0xc0, 0xab, 0x29, 0x1b, 0x98,
	0x51, 0x95, 0x96, 0x07, 0x65, 0x81, 0x0d, 0x4c, 0x59, 0xa4, 0x57, 0x20, 0xef, 0xf6, 0xb0, 0x43,
	0xa2, 0xd4, 0x92, 0x03, 0x1e, 0x52, 0x13, 0xf3, 0xb6, 0x98, 0x7a, 0x34, 0x88, 0x9a, 0xfb, 0x22,
	0xa7, 0x34, 0x38, 0x41, 0x7b, 0x0a, 0x80, 0x2d, 0x9e, 0xa7, 0x5c, 0xab, 0x38, 0xf1, 0x96, 0x76,
	0xea, 0xd7, 0x30, 0xb7, 0xbe, 0x3b, 0x5a, 0x65, 0x24, 0x24, 0x08, 0xbb, 0x79, 0xe5, 0x10, 0xc7,
	0xe2, 0xfc, 0x32, 0x52, 0x48, 0x5d, 0x46, 0xf4, 0x7b, 0x00, 0x63, 0x9d, 0x1a, 0xc0, 0x82, 0xed,
	0x62, 0x8f, 0x3a, 0xea, 0x0d, 0xad, 0x08, 0xf9, 0x67, 0xbc, 0x64, 0xa8, 0x8a, 0xfe, 0x13, 0x05,
	0xd6, 0xaf, 0x62, 0x62, 0x5f, 0xcc, 0x4b, 0x93, 0x06, 0xff, 0x07, 0x39, 0x11, 0xbd, 0x4c, 0xba,
	0xe8, 0x89, 0x45, 0xfa, 0x2f, 0x15, 0xa8, 0xcd, 0x33, 0x21, 0x1d, 0x30, 0xf7, 0x20, 0xcf, 0xf1,
	0x10, 0x37, 0x7c, 0xef, 0x4c, 0xdb, 0x31, 0x5b, 0x8f, 0x80, 0xa8, 0x5c, 0xaa, 0x7f, 0x31, 0xd7,
	0x1a, 0x01, 0xd6, 0x11, 0xcc, 0x94, 0xb9, 0x30, 0xcb, 0xcc, 0x83, 0x59, 0x76, 0x3e, 0xcc, 0x72,
	0x57, 0x61, 0x36, 0x82, 0x45, 0xfe, 0x85, 0xb0, 0x58, 0x48, 0x0f, 0x8b, 0x43, 0xd8, 0xe0, 0x6d,
	0xef, 0xc4, 0x06, 0x1b, 0x38, 0x24, 0x0e, 0x0d, 0x86, 0x69, 0x8a, 0xc9, 0xbf, 0x33, 0xb0, 0xf9,
	0x02, 0x41, 0xe9, 0x22, 0xf7, 0x5d, 0x00, 0x4b, 0x2e, 0x75, 0x49, 0x1c, 0xbe, 0xff, 0x9d, 0xdd,
	0xaf, 0xcf, 0xd5, 0x57, 0x1f, 0x0d, 0x12, 0xb2, 0x6a, 0x97, 0x50, 0x88, 0xe9, 0xbc, 0x31, 0x19,
	0x35, 0xdf, 0x19, 0xd7, 0xe6, 0xbd, 0x9f, 0xe8, 0x70, 0x64, 0xc4, 0xc4, 0xb7, 0x76, 0x04, 0xb9,
	0x10, 0x9b, 0xf1, 0xb9, 0xfd, 0x5e, 0x5a, 0x1b, 0xda, 0xd8, 0x34, 0x84, 0x80, 0xda, 0x3e, 0x64,
	0xdb, 0xd8, 0xbc, 0x96, 0xce, 0x75, 0x28, 0xf0, 0x5e, 0x42, 0xa4, 0x50, 0x56, 0x74, 0x53, 0x8b,
	0x21, 0x36, 0x45, 0xb3, 0xf2, 0x29, 0xac, 0x4f, 0x29, 0x14, 0xb1, 0xfd, 0xfa, 0xbd, 0x8a, 0xfe,
	0x79, 0x16, 0x6a, 0xf3, 0x84, 0xa7, 0x0b, 0xdf, 0x63, 0x58, 0x10, 0x18, 0x8b, 0x43, 0xb7, 0x73,
	0x0d, 0xb7, 0x8d, 0x14, 0x49, 0xac, 0x1a, 0x91, 0x84, 0xda, 0x97, 0x0a, 0xe4, 0x05, 0xe5, 0x5a,
	0xae, 0x3b, 0x83, 0x02, 0xf1, 0x48, 0x8f, 0xf8, 0x61, 0x1c, 0xb2, 0xf7, 0x53, 0xe9, 0x3e, 0x90,
	0x8b, 0x8d, 0x91, 0x14, 0xed, 0x09, 0xe4, 0x7a, 0xd4, 0x8e, 0x9f, 0x0c, 0x3f, 0x48, 0xbf, 0x93,
	0xfa, 0x09, 0xb5, 0x89, 0x21, 0x84, 0xe8, 0x9b, 0x90, 0xe3, 0x23, 0x5e, 0x7d, 0x5b, 0xcd, 0xa7,
	0x47, 0xc7, 0x07, 0xb2, 0xfa, 0x9e, 0x9c, 0x1f, 0xb7, 0x9b, 0xaa, 0x52, 0x7b, 0x17, 0x16, 0x23,
	0x03, 0xae, 0xb3, 0x59, 0xfd, 0x0f, 0x0a, 0xdc, 0x39, 0x22, 0xe1, 0x11, 0xee, 0x91, 0x51, 0x69,
	0x6a, 0x7e, 0x72, 0xe8, 0x7a, 0x21, 0x09, 0xd2, 0xe0, 0x61, 0x17, 0x80, 0xf8, 0x61, 0x30, 0x44,
	0xed, 0x71, 0xd1, 0xd6, 0x67, 0x14, 0xed, 0x21, 0x0b, 0x49, 0x30, 0x3c, 0xe0, 0x53, 0x45, 0xbd,
	0x2e, 0x92, 0xf8, 0x93, 0xdf, 0x11, 0x46, 0x3d, 0xea, 0x85, 0x13, 0x95, 0xb6, 0xb8, 0x17, 0x3a,
	0xbb, 0x70, 0xf4, 0x5f, 0x28, 0xb0, 0x31, 0xdf, 0xd6, 0x74, 0xf0, 0xfa, 0xce, 0xe4, 0x45, 0x7e,
	0x7b, 0x46, 0x5d, 0x4f, 0xa8, 0x91, 0x3a, 0x12, 0x37, 0x77, 0xfd, 0xf7, 0x19, 0xb8, 0x3d, 0x7b,
	0x46, 0x22, 0x35, 0x94, 0x64, 0xa1, 0xe5, 0xdb, 0x8b, 0xd2, 0x7a, 0x9c, 0x36, 0x71, 0x59, 0x19,
	0x36, 0xed, 0xf1, 0x39, 0x90, 0x4d, 0x9e, 0x03, 0x3a, 0x54, 0x84, 0x34, 0x5e, 0xe1, 0xd1, 0x20,
	0xf0, 0xa2, 0xba, 0x5e, 0xe2, 0x42, 0x39, 0xed, 0x3c, 0xf0, 0xb4, 0xb7, 0xe1, 0xa6, 0xcb, 0x50,
	0x07, 0x7b, 0x1e, 0x12, 0x07, 0x40, 0xf4, 0xf4, 0xc4, 0xfb, 0xd3, 0x25, 0x97, 0x1d, 0x62, 0xcf,
	0xdb, 0xc3, 0xd6, 0x85, 0x30, 0xee, 0x1e, 0x54, 0x46, 0x56, 0x84, 0x71, 0xbb, 0x51, 0x31, 0xca,
	0x31, 0x51, 0x44, 0x62, 0x1d, 0x0a, 0x5d, 0x1a, 0xa2, 0x90, 0x3c, 0x0f, 0xa3, 0xab, 0xf5, 0x62,
	0x97, 0x86, 0x6d, 0xf2, 0x3c, 0xd4, 0xfe, 0x1b, 0x56, 0xfb, 0x16, 0xea, 0x60, 0x16, 0xa2, 0x49,
	0x39, 0xf2, 0x82, 0xad, 0xf5, 0xad, 0x43, 0xcc, 0xc2, 0x46, 0x42, 0x9a, 0xfe, 0x77, 0x05, 0xee,
	0xb4, 0x5e, 0x01, 0xc4, 0xbe, 0x66, 0xc8, 0xae, 0x40, 0x34, 0xfb, 0x12, 0x10, 0xd5, 0x9b, 0xb0,
	0xd1, 0x7a, 0x35, 0x00, 0xd4, 0xbf, 0x54, 0x44, 0xe7, 0x3c, 0x6d, 0x72, 0x1a, 0x97, 0xe8, 0x50,
	0x61, 0xc4, 0xeb, 0x20, 0x87, 0x5f, 0x79, 0xf9, 0xf5, 0x3d, 0x23, 0xae, 0xef, 0x25, 0x4e, 0xe4,
	0x92, 0x9b, 0x36, 0xe3, 0x5d, 0x01, 0xef, 0x0d, 0x3f, 0x93, 0x13, 0xb2, 0x62, 0x42, 0x51, 0x52,
	0x38, 0x7b, 0xd2, 0x2b, 0xb9, 0x57, 0x90, 0xb8, 0xf9, 0xa9, 0xc4, 0xfd, 0xb9, 0xec, 0x08, 0x67,
	0xed, 0xf5, 0xdb, 0xcd, 0xda, 0x9f, 0x2a, 0xe2, 0xc6, 0xc3, 0x27, 0xb5, 0xb1, 0xb9, 0x37, 0x7c,
	0x89, 0x26, 0xe5, 0xab, 0xf3, 0xf8, 0x2b, 0xeb, 0xd8, 0xcf, 0xc6, 0xa1, 0xbf, 0x62, 0xc5, 0xb7,
	0xeb, 0x8d, 0xbd, 0xf1, 0xdd, 0x2d, 0xba, 0x47, 0xa6, 0x46, 0xa0, 0xfe, 0x9b, 0xcc, 0xb8, 0xd9,
	0xbf, 0x22, 0x24, 0x6d, 0xbb, 0x56, 0xea, 0x8c, 0x2c, 0x8c, 0xb7, 0xf4, 0x3f, 0xf3, 0xdb, 0xed,
	0x29, 0x65, 0xf5, 0xc4, 0x06, 0x93, 0xa2, 0x6a, 0x3f, 0x04, 0x48, 0x54, 0xe7, 0xd9, 0xdd, 0xf6,
	0x37, 0xf7, 0xf4, 0x22, 0xde, 0x86, 0x8e, 0x48, 0x18, 0xd9, 0xbb, 0x37, 0x7c, 0x4a, 0x2e, 0xcf,
	0x19, 0x09, 0xda, 0x38, 0xd5, 0x75, 0x28, 0xea, 0xe7, 0x12, 0x2f, 0xb9, 0xbc, 0x9f, 0x13, 0xaf,
	0xb8, 0x77, 0xa0, 0xc8, 0x3b, 0x0d, 0xc9, 0x8b, 0x2e, 0xa4, 0x9c, 0x20, 0x98, 0x6f, 0x83, 0xda,
	0x0f, 0x68, 0x8f, 0x72, 0x5b, 0xe4, 0x53, 0xa6, 0x7c, 0xcd, 0x2f, 0x1a, 0xcb, 0x23, 0xba, 0x78,
	0xd0, 0x64, 0xda, 0xeb, 0xb0, 0x34, 0x92, 0x83, 0x3c, 0x97, 0x85, 0xe2, 0xd7, 0x8b, 0xa2, 0x51,
	0x8e, 0x85, 0xf1, 0x2e, 0x45, 0xff, 0xb1, 0xb8, 0xcb, 0xcc, 0xdc, 0xcd, 0xab, 0x7c, 0x1b, 0xd2,
	0xd6, 0x60, 0xb1, 0x47, 0x42, 0x1c, 0xff, 0x36, 0x51, 0x34, 0x16, 0xf8, 0xb0, 0x69, 0xeb, 0xdf,
	0x83, 0xb5, 0xd1, 0x1b, 0x7b, 0xd4, 0xe9, 0x34, 0xa8, 0xdf, 0x49, 0xe3, 0xcc, 0x09, 0x8f, 0x65,
	0xc4, 0x26, 0x47, 0x1e, 0xd3, 0xff, 0x99, 0x81, 0xea, 0x6c, 0x05, 0xe9, 0xf6, 0x67, 0xc0, 0xa2,
	0x45, 0xfd, 0x8e, 0xeb, 0x7c, 0xc5, 0xe5, 0x63, 0x96, 0x9e, 0x7a, 0x62, 0xec, 0x3a, 0x46, 0x2c,
	0xa8, 0xf6, 0x67, 0x05, 0x2a, 0x13, 0x2c, 0xed, 0x35, 0x28, 0x47, 0x6d, 0x66, 0x32, 0xfe, 0xa5,
	0x88, 0x16, 0xe3, 0xe3, 0x33, 0xd7, 0x26, 0x34, 0xd1, 0x3d, 0x14, 0x04, 0x81, 0xb7, 0x0e, 0x9b,
	0x00, 0x92, 0x29, 0x0e, 0xf1, 0xe8, 0x69, 0x42, 0x50, 0x44, 0x69, 0x5f, 0x83, 0x45, 0xb7, 0xe7,
	0x88, 0x95, 0xf2, 0x25, 0x7e, 0xc1, 0xed, 0x39, 0x7c, 0xdd, 0x38, 0x61, 0x26, 0x1e, 0x19, 0x92,
	0x30, 0x2d, 0x4c, 0xc0, 0x54, 0xbf, 0x80, 0xdb, 0x47, 0x24, 0xdc, 0xf5, 0x3c, 0xf1, 0xb4, 0x78,
	0xe6, 0xe1, 0x61, 0xda, 0xb0, 0xcd, 0xc2, 0x72, 0x66, 0x26, 0x96, 0xf5, 0xbf, 0xc9, 0x42, 0x3f,
	0xad, 0x2d, 0x5d, 0x0c, 0xff, 0x1f, 0x8a, 0x16, 0x0e, 0x6c, 0x99, 0x0d, 0x32, 0x8a, 0x77, 0xa7,
	0xa3, 0x38, 0x56, 0x81, 0x03, 0xdb, 0x28, 0xf0, 0x15, 0x3c, 0xb0, 0xda, 0x1e, 0x94, 0x6d, 0xd2,
	0xc1, 0x03, 0x8f, 0xf7, 0x4a, 0x81, 0xc4, 0xf1, 0x35, 0x04, 0x94, 0xa2, 0x45, 0x7c, 0xa0, 0xff,
	0x51, 0x81, 0xca, 0x04, 0x9b, 0x77, 0xf1, 0xc2, 0xbd, 0xb2, 0x7e, 0x89, 0x6f, 0x4d, 0x85, 0xac,
	0xdb, 0x73, 0xa2, 0xc2, 0xc0, 0x3f, 0x27, 0x02, 0x91, 0x9d, 0xac, 0x17, 0xd3, 0x79, 0x9e, 0x9b,
	0xce, 0x73, 0x0e, 0x2c, 0x97, 0x21, 0xec, 0xbb, 0x3d, 0xf9, 0xb3, 0x67, 0x3e, 0xfe, 0xf9, 0x6d,
	0x37, 0x26, 0xc9, 0xdf, 0x14, 0x3d, 0x1a, 0x44, 0xd0, 0x90, 0x83, 0xfb, 0x0f, 0xe1, 0xd6, 0x8c,
	0x7f, 0x40, 0x68, 0x15, 0x28, 0x1e, 0x06, 0xb4, 0x87, 0x8e, 0x5d, 0xff, 0x42, 0xbd, 0xa1, 0x2d,
	0x43, 0x49, 0x0c, 0x1b, 0x9e, 0x4b, 0xfc, 0x50, 0x55, 0xee, 0x7f, 0xa1, 0x40, 0xb9, 0x95, 0xf8,
	0x73, 0x80, 0xb6, 0x09, 0xeb, 0xad, 0x8f, 0x9b, 0xed, 0xc6, 0x23, 0xd4, 0x3a, 0x3d, 0x37, 0x1a,
	0x07, 0xe8, 0xfc, 0x69, 0xeb, 0xec, 0xa0, 0xd1, 0x3c, 0x6c, 0x1e, 0xec, 0xab, 0x37, 0xb4, 0xdb,
	0xa0, 0x4d, 0xb2, 0x8d, 0xd3, 0xd3, 0x13, 0x55, 0xd1, 0xee, 0xc2, 0x9d, 0x49, 0xfa, 0xd9, 0xf1,
	0xee, 0x27, 0xa8, 0x7d, 0x7a, 0x74, 0xd0, 0x7e, 0x74, 0x60, 0xa8, 0x19, 0xad, 0x06, 0xb7, 0x27,
	0x27, 0x3c, 0x6e, 0xa1, 0x93, 0x66, 0x7b, 0xf7, 0x54, 0xcd, 0x4e, 0xeb, 0x6c, 0x18, 0x07, 0xbb,
	0xed, 0x48, 0x76, 0x6e, 0x06, 0xfb, 0xf4, 0xe4, 0xe4, 0xf4, 0x29, 0x3a, 0x6e, 0x3e, 0x7d, 0xa2,
	0xe6, 0xb5, 0x0d, 0xa8, 0x4e, 0xb2, 0x4f, 0xb9, 0x4a, 0xb4, 0x77, 0xde, 0x6a, 0xa9, 0x0b, 0xf7,
	0x3f, 0x57, 0x60, 0x75, 0xe6, 0x9b, 0x15, 0x17, 0x3b, 0xc5, 0x40, 0xe7, 0xfe, 0x85, 0x4f, 0x2f,
	0x7d, 0xf5, 0x86, 0xf6, 0x1a, 0x6c, 0x4e, 0xb3, 0x5b, 0xae, 0xef, 0xa0, 0x5d, 0xd4, 0xa2, 0xbe,
	0xa3, 0x2a, 0xda, 0xfa, 0x0c, 0xd1, 0xe8, 0x49, 0xfb, 0x23, 0x35, 0x33, 0x5b, 0xf8, 0x31, 0x71,
	0xd9, 0x20, 0x20, 0x6a, 0x76, 0xf6, 0x4a, 0x03, 0xf7, 0xd5, 0xdc, 0xfd, 0x67, 0xa0, 0x5e, 0xed,
	0xfa, 0xb4, 0x55, 0xb8, 0x99, 0xec, 0x29, 0x04, 0x43, 0xbd, 0xa1, 0x55, 0x61, 0x25, 0x9a, 0x3a,
	0xc9, 0x51, 0xb4, 0x5b, 0xb0, 0x7c, 0xd6, 0x98, 0x24, 0x66, 0xb4, 0x35, 0xb8, 0x75, 0xb8, 0xdb,
	0x6a, 0x5f, 0x65, 0x64, 0xf7, 0x0e, 0xa1, 0x6a, 0xd1, 0x5e, 0x7d, 0xe8, 0x0e, 0xe9, 0x40, 0xfc,
	0x6a, 0x43, 0x6d, 0xe2, 0xc9, 0x3f, 0xff, 0x7c, 0x7a, 0xdf, 0xa1, 0x1e, 0xf6, 0x9d, 0xfa, 0xc3,
	0x9d, 0x30, 0xac, 0x5b, 0xb4, 0xf7, 0x40, 0x90, 0x2d, 0xea, 0x3d, 0xc0, 0xfd, 0xfe, 0xe4, 0x7f,
	0x95, 0xcc, 0x05, 0xc1, 0x7b, 0xef, 0x3f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x61, 0x7f, 0xc2, 0xd7,
	0xd1, 0x24, 0x00, 0x00,
}
