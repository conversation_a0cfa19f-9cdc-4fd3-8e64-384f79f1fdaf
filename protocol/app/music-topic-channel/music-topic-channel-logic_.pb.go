// Code generated by protoc-gen-go. DO NOT EDIT.
// source: music_topic_channel/music-topic-channel-logic_.proto

package music_topic_channel // import "golang.52tt.com/protocol/app/music-topic-channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import channel_play "golang.52tt.com/protocol/app/channel-play"
import im "golang.52tt.com/protocol/app/im"
import muse_interest_hub_logic "golang.52tt.com/protocol/app/muse-interest-hub-logic"
import topic_channel "golang.52tt.com/protocol/app/topic-channel"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 标签属性
type FilterAttrType int32

const (
	FilterAttrType_FILTER_ATTR_TYPE_UNEXPECTED FilterAttrType = 0
	FilterAttrType_FILTER_ATTR_TYPE_SAME_CITY  FilterAttrType = 1
)

var FilterAttrType_name = map[int32]string{
	0: "FILTER_ATTR_TYPE_UNEXPECTED",
	1: "FILTER_ATTR_TYPE_SAME_CITY",
}
var FilterAttrType_value = map[string]int32{
	"FILTER_ATTR_TYPE_UNEXPECTED": 0,
	"FILTER_ATTR_TYPE_SAME_CITY":  1,
}

func (x FilterAttrType) String() string {
	return proto.EnumName(FilterAttrType_name, int32(x))
}
func (FilterAttrType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{0}
}

type FilterModel int32

const (
	FilterModel_MtModel FilterModel = 0
	FilterModel_KhModel FilterModel = 1
)

var FilterModel_name = map[int32]string{
	0: "MtModel",
	1: "KhModel",
}
var FilterModel_value = map[string]int32{
	"MtModel": 0,
	"KhModel": 1,
}

func (x FilterModel) String() string {
	return proto.EnumName(FilterModel_name, int32(x))
}
func (FilterModel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{1}
}

type MusicHomePageDialogType int32

const (
	MusicHomePageDialogType_Ktv_Dialog MusicHomePageDialogType = 0
)

var MusicHomePageDialogType_name = map[int32]string{
	0: "Ktv_Dialog",
}
var MusicHomePageDialogType_value = map[string]int32{
	"Ktv_Dialog": 0,
}

func (x MusicHomePageDialogType) String() string {
	return proto.EnumName(MusicHomePageDialogType_name, int32(x))
}
func (MusicHomePageDialogType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{2}
}

type MusicChannelLabel int32

const (
	MusicChannelLabel_MusicChannelLabelNone    MusicChannelLabel = 0
	MusicChannelLabel_MusicChannelLabelQuality MusicChannelLabel = 1
	MusicChannelLabel_MusicChannelLabelHot     MusicChannelLabel = 2
)

var MusicChannelLabel_name = map[int32]string{
	0: "MusicChannelLabelNone",
	1: "MusicChannelLabelQuality",
	2: "MusicChannelLabelHot",
}
var MusicChannelLabel_value = map[string]int32{
	"MusicChannelLabelNone":    0,
	"MusicChannelLabelQuality": 1,
	"MusicChannelLabelHot":     2,
}

func (x MusicChannelLabel) String() string {
	return proto.EnumName(MusicChannelLabel_name, int32(x))
}
func (MusicChannelLabel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{3}
}

type MuseCategoryType int32

const (
	MuseCategoryType_MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_UNSPECIFIED             MuseCategoryType = 0
	MuseCategoryType_MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_GANGUP_TYPE             MuseCategoryType = 1
	MuseCategoryType_MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_CHAT_TYPE               MuseCategoryType = 2
	MuseCategoryType_MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_FUN_GAME_TYPE           MuseCategoryType = 3
	MuseCategoryType_MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_CASUAL_INTERACTION_TYPE MuseCategoryType = 4
	MuseCategoryType_MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_MUSIC_TYPE              MuseCategoryType = 5
	MuseCategoryType_MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_MELEE_TYPE              MuseCategoryType = 6
	MuseCategoryType_MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_ESCAPE_ROOM_TYPE        MuseCategoryType = 7
	MuseCategoryType_MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_GROUP_CHAT_TYPE         MuseCategoryType = 8
	MuseCategoryType_MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_NEW_CHAT_TYPE           MuseCategoryType = 9
)

var MuseCategoryType_name = map[int32]string{
	0: "MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_UNSPECIFIED",
	1: "MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_GANGUP_TYPE",
	2: "MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_CHAT_TYPE",
	3: "MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_FUN_GAME_TYPE",
	4: "MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_CASUAL_INTERACTION_TYPE",
	5: "MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_MUSIC_TYPE",
	6: "MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_MELEE_TYPE",
	7: "MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_ESCAPE_ROOM_TYPE",
	8: "MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_GROUP_CHAT_TYPE",
	9: "MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_NEW_CHAT_TYPE",
}
var MuseCategoryType_value = map[string]int32{
	"MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_UNSPECIFIED":             0,
	"MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_GANGUP_TYPE":             1,
	"MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_CHAT_TYPE":               2,
	"MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_FUN_GAME_TYPE":           3,
	"MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_CASUAL_INTERACTION_TYPE": 4,
	"MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_MUSIC_TYPE":              5,
	"MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_MELEE_TYPE":              6,
	"MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_ESCAPE_ROOM_TYPE":        7,
	"MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_GROUP_CHAT_TYPE":         8,
	"MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_NEW_CHAT_TYPE":           9,
}

func (x MuseCategoryType) String() string {
	return proto.EnumName(MuseCategoryType_name, int32(x))
}
func (MuseCategoryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{4}
}

type MusePlayingOption int32

const (
	MusePlayingOption_MUSE_SOCIAL_COMMUNITY_PLAYING_OPTION_AT_MAIN_PAGE_UNSPECIFIED MusePlayingOption = 0
	MusePlayingOption_MUSE_SOCIAL_COMMUNITY_PLAYING_OPTION_FRIEND                   MusePlayingOption = 1
)

var MusePlayingOption_name = map[int32]string{
	0: "MUSE_SOCIAL_COMMUNITY_PLAYING_OPTION_AT_MAIN_PAGE_UNSPECIFIED",
	1: "MUSE_SOCIAL_COMMUNITY_PLAYING_OPTION_FRIEND",
}
var MusePlayingOption_value = map[string]int32{
	"MUSE_SOCIAL_COMMUNITY_PLAYING_OPTION_AT_MAIN_PAGE_UNSPECIFIED": 0,
	"MUSE_SOCIAL_COMMUNITY_PLAYING_OPTION_FRIEND":                   1,
}

func (x MusePlayingOption) String() string {
	return proto.EnumName(MusePlayingOption_name, int32(x))
}
func (MusePlayingOption) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{5}
}

// 主题房处罚类型
type TopicChannelPunishType int32

const (
	TopicChannelPunishType_UnknownPunishType   TopicChannelPunishType = 0
	TopicChannelPunishType_WarnType            TopicChannelPunishType = 1
	TopicChannelPunishType_KickOutChannelType  TopicChannelPunishType = 2
	TopicChannelPunishType_PublishChannelLimit TopicChannelPunishType = 3
)

var TopicChannelPunishType_name = map[int32]string{
	0: "UnknownPunishType",
	1: "WarnType",
	2: "KickOutChannelType",
	3: "PublishChannelLimit",
}
var TopicChannelPunishType_value = map[string]int32{
	"UnknownPunishType":   0,
	"WarnType":            1,
	"KickOutChannelType":  2,
	"PublishChannelLimit": 3,
}

func (x TopicChannelPunishType) String() string {
	return proto.EnumName(TopicChannelPunishType_name, int32(x))
}
func (TopicChannelPunishType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{6}
}

type UserFollowInChannelScene int32

const (
	UserFollowInChannelScene_USER_FOLLOW_IN_CHANNEL_SCENE_UNSPECIFIED UserFollowInChannelScene = 0
	UserFollowInChannelScene_USER_FOLLOW_IN_CHANNEL_SCENE_OWNER       UserFollowInChannelScene = 1
	UserFollowInChannelScene_USER_FOLLOW_IN_CHANNEL_SCENE_NORMAL      UserFollowInChannelScene = 2
)

var UserFollowInChannelScene_name = map[int32]string{
	0: "USER_FOLLOW_IN_CHANNEL_SCENE_UNSPECIFIED",
	1: "USER_FOLLOW_IN_CHANNEL_SCENE_OWNER",
	2: "USER_FOLLOW_IN_CHANNEL_SCENE_NORMAL",
}
var UserFollowInChannelScene_value = map[string]int32{
	"USER_FOLLOW_IN_CHANNEL_SCENE_UNSPECIFIED": 0,
	"USER_FOLLOW_IN_CHANNEL_SCENE_OWNER":       1,
	"USER_FOLLOW_IN_CHANNEL_SCENE_NORMAL":      2,
}

func (x UserFollowInChannelScene) String() string {
	return proto.EnumName(UserFollowInChannelScene_name, int32(x))
}
func (UserFollowInChannelScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{7}
}

type GetMusicChannelFilterV2Resp_FilterItemType int32

const (
	GetMusicChannelFilterV2Resp_HOME_FILTER_ITEM  GetMusicChannelFilterV2Resp_FilterItemType = 0
	GetMusicChannelFilterV2Resp_PAGE_FILTER_ITERM GetMusicChannelFilterV2Resp_FilterItemType = 1
	GetMusicChannelFilterV2Resp_PAGE_POST         GetMusicChannelFilterV2Resp_FilterItemType = 2
)

var GetMusicChannelFilterV2Resp_FilterItemType_name = map[int32]string{
	0: "HOME_FILTER_ITEM",
	1: "PAGE_FILTER_ITERM",
	2: "PAGE_POST",
}
var GetMusicChannelFilterV2Resp_FilterItemType_value = map[string]int32{
	"HOME_FILTER_ITEM":  0,
	"PAGE_FILTER_ITERM": 1,
	"PAGE_POST":         2,
}

func (x GetMusicChannelFilterV2Resp_FilterItemType) String() string {
	return proto.EnumName(GetMusicChannelFilterV2Resp_FilterItemType_name, int32(x))
}
func (GetMusicChannelFilterV2Resp_FilterItemType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{1, 0}
}

type GetMusicHomePageViewV2Resp_MusicHomePageType int32

const (
	GetMusicHomePageViewV2Resp_MusicHomePage_Full   GetMusicHomePageViewV2Resp_MusicHomePageType = 0
	GetMusicHomePageViewV2Resp_MusicHomePage_Scroll GetMusicHomePageViewV2Resp_MusicHomePageType = 1
)

var GetMusicHomePageViewV2Resp_MusicHomePageType_name = map[int32]string{
	0: "MusicHomePage_Full",
	1: "MusicHomePage_Scroll",
}
var GetMusicHomePageViewV2Resp_MusicHomePageType_value = map[string]int32{
	"MusicHomePage_Full":   0,
	"MusicHomePage_Scroll": 1,
}

func (x GetMusicHomePageViewV2Resp_MusicHomePageType) String() string {
	return proto.EnumName(GetMusicHomePageViewV2Resp_MusicHomePageType_name, int32(x))
}
func (GetMusicHomePageViewV2Resp_MusicHomePageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{5, 0}
}

type GetMusicHomePageViewV2Resp_ActionType int32

const (
	GetMusicHomePageViewV2Resp_Sing_A_Round_Dialog      GetMusicHomePageViewV2Resp_ActionType = 0
	GetMusicHomePageViewV2Resp_Quick_Match              GetMusicHomePageViewV2Resp_ActionType = 1
	GetMusicHomePageViewV2Resp_Url                      GetMusicHomePageViewV2Resp_ActionType = 2
	GetMusicHomePageViewV2Resp_Sing_A_Round_Quick_Match GetMusicHomePageViewV2Resp_ActionType = 3
)

var GetMusicHomePageViewV2Resp_ActionType_name = map[int32]string{
	0: "Sing_A_Round_Dialog",
	1: "Quick_Match",
	2: "Url",
	3: "Sing_A_Round_Quick_Match",
}
var GetMusicHomePageViewV2Resp_ActionType_value = map[string]int32{
	"Sing_A_Round_Dialog": 0,
	"Quick_Match":         1,
	"Url":                 2,
	"Sing_A_Round_Quick_Match": 3,
}

func (x GetMusicHomePageViewV2Resp_ActionType) String() string {
	return proto.EnumName(GetMusicHomePageViewV2Resp_ActionType_name, int32(x))
}
func (GetMusicHomePageViewV2Resp_ActionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{5, 1}
}

type MuseGetTopicChannelInfoRequest_ChannelInfoType int32

const (
	MuseGetTopicChannelInfoRequest_CHANNEL_INFO_TYPE_NORMAL_UNSPECIFIED MuseGetTopicChannelInfoRequest_ChannelInfoType = 0
	MuseGetTopicChannelInfoRequest_CHANNEL_INFO_TYPE_PLAY_TYPE          MuseGetTopicChannelInfoRequest_ChannelInfoType = 1
)

var MuseGetTopicChannelInfoRequest_ChannelInfoType_name = map[int32]string{
	0: "CHANNEL_INFO_TYPE_NORMAL_UNSPECIFIED",
	1: "CHANNEL_INFO_TYPE_PLAY_TYPE",
}
var MuseGetTopicChannelInfoRequest_ChannelInfoType_value = map[string]int32{
	"CHANNEL_INFO_TYPE_NORMAL_UNSPECIFIED": 0,
	"CHANNEL_INFO_TYPE_PLAY_TYPE":          1,
}

func (x MuseGetTopicChannelInfoRequest_ChannelInfoType) String() string {
	return proto.EnumName(MuseGetTopicChannelInfoRequest_ChannelInfoType_name, int32(x))
}
func (MuseGetTopicChannelInfoRequest_ChannelInfoType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{38, 0}
}

type MuseGetTopicChannelInfoResponse_TabType int32

const (
	MuseGetTopicChannelInfoResponse_TAB_TYPE_NORMAL_UNSPECIFIED MuseGetTopicChannelInfoResponse_TabType = 0
	MuseGetTopicChannelInfoResponse_TAB_TYPE_GAME               MuseGetTopicChannelInfoResponse_TabType = 1
	MuseGetTopicChannelInfoResponse_TAB_TYPE_MINI_GAME          MuseGetTopicChannelInfoResponse_TabType = 2
)

var MuseGetTopicChannelInfoResponse_TabType_name = map[int32]string{
	0: "TAB_TYPE_NORMAL_UNSPECIFIED",
	1: "TAB_TYPE_GAME",
	2: "TAB_TYPE_MINI_GAME",
}
var MuseGetTopicChannelInfoResponse_TabType_value = map[string]int32{
	"TAB_TYPE_NORMAL_UNSPECIFIED": 0,
	"TAB_TYPE_GAME":               1,
	"TAB_TYPE_MINI_GAME":          2,
}

func (x MuseGetTopicChannelInfoResponse_TabType) String() string {
	return proto.EnumName(MuseGetTopicChannelInfoResponse_TabType_name, int32(x))
}
func (MuseGetTopicChannelInfoResponse_TabType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{39, 0}
}

// 获取筛选器
type GetMusicChannelFilterV2Req struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FilterType           string       `protobuf:"bytes,2,opt,name=filter_type,json=filterType,proto3" json:"filter_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMusicChannelFilterV2Req) Reset()         { *m = GetMusicChannelFilterV2Req{} }
func (m *GetMusicChannelFilterV2Req) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelFilterV2Req) ProtoMessage()    {}
func (*GetMusicChannelFilterV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{0}
}
func (m *GetMusicChannelFilterV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelFilterV2Req.Unmarshal(m, b)
}
func (m *GetMusicChannelFilterV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelFilterV2Req.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelFilterV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelFilterV2Req.Merge(dst, src)
}
func (m *GetMusicChannelFilterV2Req) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelFilterV2Req.Size(m)
}
func (m *GetMusicChannelFilterV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelFilterV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelFilterV2Req proto.InternalMessageInfo

func (m *GetMusicChannelFilterV2Req) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMusicChannelFilterV2Req) GetFilterType() string {
	if m != nil {
		return m.FilterType
	}
	return ""
}

type GetMusicChannelFilterV2Resp struct {
	BaseResp             *app.BaseResp                             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	FilterItems          []*GetMusicChannelFilterV2Resp_FilterItem `protobuf:"bytes,2,rep,name=filter_items,json=filterItems,proto3" json:"filter_items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                  `json:"-"`
	XXX_unrecognized     []byte                                    `json:"-"`
	XXX_sizecache        int32                                     `json:"-"`
}

func (m *GetMusicChannelFilterV2Resp) Reset()         { *m = GetMusicChannelFilterV2Resp{} }
func (m *GetMusicChannelFilterV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelFilterV2Resp) ProtoMessage()    {}
func (*GetMusicChannelFilterV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{1}
}
func (m *GetMusicChannelFilterV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp.Unmarshal(m, b)
}
func (m *GetMusicChannelFilterV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelFilterV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelFilterV2Resp.Merge(dst, src)
}
func (m *GetMusicChannelFilterV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp.Size(m)
}
func (m *GetMusicChannelFilterV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelFilterV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelFilterV2Resp proto.InternalMessageInfo

func (m *GetMusicChannelFilterV2Resp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusicChannelFilterV2Resp) GetFilterItems() []*GetMusicChannelFilterV2Resp_FilterItem {
	if m != nil {
		return m.FilterItems
	}
	return nil
}

type GetMusicChannelFilterV2Resp_FilterItem struct {
	Title                string                                       `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	FilterItemType       string                                       `protobuf:"bytes,2,opt,name=filter_item_type,json=filterItemType,proto3" json:"filter_item_type,omitempty"`
	FilterId             string                                       `protobuf:"bytes,3,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	FilterSubItems       []*GetMusicChannelFilterV2Resp_FilterSubItem `protobuf:"bytes,4,rep,name=filter_sub_items,json=filterSubItems,proto3" json:"filter_sub_items,omitempty"`
	Tip                  string                                       `protobuf:"bytes,5,opt,name=tip,proto3" json:"tip,omitempty"`
	FilterAttrType       uint32                                       `protobuf:"varint,6,opt,name=filter_attr_type,json=filterAttrType,proto3" json:"filter_attr_type,omitempty"`
	CityTitle            *SameCityTitle                               `protobuf:"bytes,7,opt,name=city_title,json=cityTitle,proto3" json:"city_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) Reset() {
	*m = GetMusicChannelFilterV2Resp_FilterItem{}
}
func (m *GetMusicChannelFilterV2Resp_FilterItem) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelFilterV2Resp_FilterItem) ProtoMessage()    {}
func (*GetMusicChannelFilterV2Resp_FilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{1, 0}
}
func (m *GetMusicChannelFilterV2Resp_FilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem.Unmarshal(m, b)
}
func (m *GetMusicChannelFilterV2Resp_FilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelFilterV2Resp_FilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem.Merge(dst, src)
}
func (m *GetMusicChannelFilterV2Resp_FilterItem) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem.Size(m)
}
func (m *GetMusicChannelFilterV2Resp_FilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterItem proto.InternalMessageInfo

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetFilterItemType() string {
	if m != nil {
		return m.FilterItemType
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetFilterSubItems() []*GetMusicChannelFilterV2Resp_FilterSubItem {
	if m != nil {
		return m.FilterSubItems
	}
	return nil
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetFilterAttrType() uint32 {
	if m != nil {
		return m.FilterAttrType
	}
	return 0
}

func (m *GetMusicChannelFilterV2Resp_FilterItem) GetCityTitle() *SameCityTitle {
	if m != nil {
		return m.CityTitle
	}
	return nil
}

type GetMusicChannelFilterV2Resp_FilterSubItem struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	FilterSubId          string   `protobuf:"bytes,2,opt,name=filter_sub_id,json=filterSubId,proto3" json:"filter_sub_id,omitempty"`
	FilterSubItemType    string   `protobuf:"bytes,3,opt,name=filter_sub_item_type,json=filterSubItemType,proto3" json:"filter_sub_item_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicChannelFilterV2Resp_FilterSubItem) Reset() {
	*m = GetMusicChannelFilterV2Resp_FilterSubItem{}
}
func (m *GetMusicChannelFilterV2Resp_FilterSubItem) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelFilterV2Resp_FilterSubItem) ProtoMessage()    {}
func (*GetMusicChannelFilterV2Resp_FilterSubItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{1, 1}
}
func (m *GetMusicChannelFilterV2Resp_FilterSubItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem.Unmarshal(m, b)
}
func (m *GetMusicChannelFilterV2Resp_FilterSubItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelFilterV2Resp_FilterSubItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem.Merge(dst, src)
}
func (m *GetMusicChannelFilterV2Resp_FilterSubItem) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem.Size(m)
}
func (m *GetMusicChannelFilterV2Resp_FilterSubItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelFilterV2Resp_FilterSubItem proto.InternalMessageInfo

func (m *GetMusicChannelFilterV2Resp_FilterSubItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterSubItem) GetFilterSubId() string {
	if m != nil {
		return m.FilterSubId
	}
	return ""
}

func (m *GetMusicChannelFilterV2Resp_FilterSubItem) GetFilterSubItemType() string {
	if m != nil {
		return m.FilterSubItemType
	}
	return ""
}

// 标签是同城则使用此结构体信息替换title
type SameCityTitle struct {
	CityName             string   `protobuf:"bytes,1,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	ProvinceName         string   `protobuf:"bytes,2,opt,name=province_name,json=provinceName,proto3" json:"province_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SameCityTitle) Reset()         { *m = SameCityTitle{} }
func (m *SameCityTitle) String() string { return proto.CompactTextString(m) }
func (*SameCityTitle) ProtoMessage()    {}
func (*SameCityTitle) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{2}
}
func (m *SameCityTitle) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SameCityTitle.Unmarshal(m, b)
}
func (m *SameCityTitle) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SameCityTitle.Marshal(b, m, deterministic)
}
func (dst *SameCityTitle) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SameCityTitle.Merge(dst, src)
}
func (m *SameCityTitle) XXX_Size() int {
	return xxx_messageInfo_SameCityTitle.Size(m)
}
func (m *SameCityTitle) XXX_DiscardUnknown() {
	xxx_messageInfo_SameCityTitle.DiscardUnknown(m)
}

var xxx_messageInfo_SameCityTitle proto.InternalMessageInfo

func (m *SameCityTitle) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *SameCityTitle) GetProvinceName() string {
	if m != nil {
		return m.ProvinceName
	}
	return ""
}

// 房间流请求
type ListHobbyChannelV2Req struct {
	BaseReq                *app.BaseReq                      `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Count                  uint32                            `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	Sex                    int32                             `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	FilterId               string                            `protobuf:"bytes,4,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	IsStartLoad            bool                              `protobuf:"varint,5,opt,name=is_start_load,json=isStartLoad,proto3" json:"is_start_load,omitempty"`
	ExposeChannelIds       []uint32                          `protobuf:"varint,6,rep,packed,name=expose_channel_ids,json=exposeChannelIds,proto3" json:"expose_channel_ids,omitempty"`
	FilterSubIds           []string                          `protobuf:"bytes,7,rep,name=filter_sub_ids,json=filterSubIds,proto3" json:"filter_sub_ids,omitempty"`
	ChannelPackageId       string                            `protobuf:"bytes,8,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	IsUserLocationAuthOpen bool                              `protobuf:"varint,9,opt,name=is_user_location_auth_open,json=isUserLocationAuthOpen,proto3" json:"is_user_location_auth_open,omitempty"`
	NotExposeChannel       *NotExposeChannelList             `protobuf:"bytes,10,opt,name=not_expose_channel,json=notExposeChannel,proto3" json:"not_expose_channel,omitempty"`
	FilterModel            FilterModel                       `protobuf:"varint,11,opt,name=filter_model,json=filterModel,proto3,enum=ga.music_topic_channel.FilterModel" json:"filter_model,omitempty"`
	TabIds                 []uint32                          `protobuf:"varint,12,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	BlockOption            []*channel_play.FilterBlockOption `protobuf:"bytes,13,rep,name=block_option,json=blockOption,proto3" json:"block_option,omitempty"`
	Labels                 []*topic_channel.GameLabel        `protobuf:"bytes,14,rep,name=labels,proto3" json:"labels,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                          `json:"-"`
	XXX_unrecognized       []byte                            `json:"-"`
	XXX_sizecache          int32                             `json:"-"`
}

func (m *ListHobbyChannelV2Req) Reset()         { *m = ListHobbyChannelV2Req{} }
func (m *ListHobbyChannelV2Req) String() string { return proto.CompactTextString(m) }
func (*ListHobbyChannelV2Req) ProtoMessage()    {}
func (*ListHobbyChannelV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{3}
}
func (m *ListHobbyChannelV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListHobbyChannelV2Req.Unmarshal(m, b)
}
func (m *ListHobbyChannelV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListHobbyChannelV2Req.Marshal(b, m, deterministic)
}
func (dst *ListHobbyChannelV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListHobbyChannelV2Req.Merge(dst, src)
}
func (m *ListHobbyChannelV2Req) XXX_Size() int {
	return xxx_messageInfo_ListHobbyChannelV2Req.Size(m)
}
func (m *ListHobbyChannelV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_ListHobbyChannelV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_ListHobbyChannelV2Req proto.InternalMessageInfo

func (m *ListHobbyChannelV2Req) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListHobbyChannelV2Req) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ListHobbyChannelV2Req) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ListHobbyChannelV2Req) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *ListHobbyChannelV2Req) GetIsStartLoad() bool {
	if m != nil {
		return m.IsStartLoad
	}
	return false
}

func (m *ListHobbyChannelV2Req) GetExposeChannelIds() []uint32 {
	if m != nil {
		return m.ExposeChannelIds
	}
	return nil
}

func (m *ListHobbyChannelV2Req) GetFilterSubIds() []string {
	if m != nil {
		return m.FilterSubIds
	}
	return nil
}

func (m *ListHobbyChannelV2Req) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *ListHobbyChannelV2Req) GetIsUserLocationAuthOpen() bool {
	if m != nil {
		return m.IsUserLocationAuthOpen
	}
	return false
}

func (m *ListHobbyChannelV2Req) GetNotExposeChannel() *NotExposeChannelList {
	if m != nil {
		return m.NotExposeChannel
	}
	return nil
}

func (m *ListHobbyChannelV2Req) GetFilterModel() FilterModel {
	if m != nil {
		return m.FilterModel
	}
	return FilterModel_MtModel
}

func (m *ListHobbyChannelV2Req) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *ListHobbyChannelV2Req) GetBlockOption() []*channel_play.FilterBlockOption {
	if m != nil {
		return m.BlockOption
	}
	return nil
}

func (m *ListHobbyChannelV2Req) GetLabels() []*topic_channel.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

// 音乐首页控件
type GetMusicHomePageViewV2Req struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMusicHomePageViewV2Req) Reset()         { *m = GetMusicHomePageViewV2Req{} }
func (m *GetMusicHomePageViewV2Req) String() string { return proto.CompactTextString(m) }
func (*GetMusicHomePageViewV2Req) ProtoMessage()    {}
func (*GetMusicHomePageViewV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{4}
}
func (m *GetMusicHomePageViewV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageViewV2Req.Unmarshal(m, b)
}
func (m *GetMusicHomePageViewV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageViewV2Req.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageViewV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageViewV2Req.Merge(dst, src)
}
func (m *GetMusicHomePageViewV2Req) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageViewV2Req.Size(m)
}
func (m *GetMusicHomePageViewV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageViewV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageViewV2Req proto.InternalMessageInfo

func (m *GetMusicHomePageViewV2Req) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetMusicHomePageViewV2Resp struct {
	BaseResp             *app.BaseResp                                     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Type                 GetMusicHomePageViewV2Resp_MusicHomePageType      `protobuf:"varint,2,opt,name=type,proto3,enum=ga.music_topic_channel.GetMusicHomePageViewV2Resp_MusicHomePageType" json:"type,omitempty"`
	FullTypeViews        []*GetMusicHomePageViewV2Resp_MusicHomePageV2View `protobuf:"bytes,3,rep,name=full_type_views,json=fullTypeViews,proto3" json:"full_type_views,omitempty"`
	ScrollTypeViews      []*GetMusicHomePageViewV2Resp_MusicHomePageV2View `protobuf:"bytes,4,rep,name=scroll_type_views,json=scrollTypeViews,proto3" json:"scroll_type_views,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                          `json:"-"`
	XXX_unrecognized     []byte                                            `json:"-"`
	XXX_sizecache        int32                                             `json:"-"`
}

func (m *GetMusicHomePageViewV2Resp) Reset()         { *m = GetMusicHomePageViewV2Resp{} }
func (m *GetMusicHomePageViewV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetMusicHomePageViewV2Resp) ProtoMessage()    {}
func (*GetMusicHomePageViewV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{5}
}
func (m *GetMusicHomePageViewV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageViewV2Resp.Unmarshal(m, b)
}
func (m *GetMusicHomePageViewV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageViewV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageViewV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageViewV2Resp.Merge(dst, src)
}
func (m *GetMusicHomePageViewV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageViewV2Resp.Size(m)
}
func (m *GetMusicHomePageViewV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageViewV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageViewV2Resp proto.InternalMessageInfo

func (m *GetMusicHomePageViewV2Resp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusicHomePageViewV2Resp) GetType() GetMusicHomePageViewV2Resp_MusicHomePageType {
	if m != nil {
		return m.Type
	}
	return GetMusicHomePageViewV2Resp_MusicHomePage_Full
}

func (m *GetMusicHomePageViewV2Resp) GetFullTypeViews() []*GetMusicHomePageViewV2Resp_MusicHomePageV2View {
	if m != nil {
		return m.FullTypeViews
	}
	return nil
}

func (m *GetMusicHomePageViewV2Resp) GetScrollTypeViews() []*GetMusicHomePageViewV2Resp_MusicHomePageV2View {
	if m != nil {
		return m.ScrollTypeViews
	}
	return nil
}

type GetMusicHomePageViewV2Resp_MusicHomePageV2View struct {
	Title    string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle string `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Icon     string `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	ActionType           GetMusicHomePageViewV2Resp_ActionType             `protobuf:"varint,4,opt,name=actionType,proto3,enum=ga.music_topic_channel.GetMusicHomePageViewV2Resp_ActionType" json:"actionType,omitempty"`
	SubViews             []*GetMusicHomePageViewV2Resp_MusicHomePageV2View `protobuf:"bytes,5,rep,name=sub_views,json=subViews,proto3" json:"sub_views,omitempty"`
	QuickMatchId         string                                            `protobuf:"bytes,6,opt,name=quick_match_id,json=quickMatchId,proto3" json:"quick_match_id,omitempty"`
	Url                  string                                            `protobuf:"bytes,7,opt,name=url,proto3" json:"url,omitempty"`
	TagId                uint32                                            `protobuf:"varint,8,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	BgImg                string                                            `protobuf:"bytes,9,opt,name=bg_img,json=bgImg,proto3" json:"bg_img,omitempty"`
	ViewId               string                                            `protobuf:"bytes,10,opt,name=view_id,json=viewId,proto3" json:"view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                          `json:"-"`
	XXX_unrecognized     []byte                                            `json:"-"`
	XXX_sizecache        int32                                             `json:"-"`
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) Reset() {
	*m = GetMusicHomePageViewV2Resp_MusicHomePageV2View{}
}
func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) String() string {
	return proto.CompactTextString(m)
}
func (*GetMusicHomePageViewV2Resp_MusicHomePageV2View) ProtoMessage() {}
func (*GetMusicHomePageViewV2Resp_MusicHomePageV2View) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{5, 0}
}
func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageViewV2Resp_MusicHomePageV2View.Unmarshal(m, b)
}
func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageViewV2Resp_MusicHomePageV2View.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageViewV2Resp_MusicHomePageV2View) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageViewV2Resp_MusicHomePageV2View.Merge(dst, src)
}
func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageViewV2Resp_MusicHomePageV2View.Size(m)
}
func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageViewV2Resp_MusicHomePageV2View.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageViewV2Resp_MusicHomePageV2View proto.InternalMessageInfo

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetActionType() GetMusicHomePageViewV2Resp_ActionType {
	if m != nil {
		return m.ActionType
	}
	return GetMusicHomePageViewV2Resp_Sing_A_Round_Dialog
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetSubViews() []*GetMusicHomePageViewV2Resp_MusicHomePageV2View {
	if m != nil {
		return m.SubViews
	}
	return nil
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetQuickMatchId() string {
	if m != nil {
		return m.QuickMatchId
	}
	return ""
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetBgImg() string {
	if m != nil {
		return m.BgImg
	}
	return ""
}

func (m *GetMusicHomePageViewV2Resp_MusicHomePageV2View) GetViewId() string {
	if m != nil {
		return m.ViewId
	}
	return ""
}

// 获取对话框
type GetMusicHomePageDialogV2Req struct {
	BaseReq              *app.BaseReq            `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Type                 MusicHomePageDialogType `protobuf:"varint,2,opt,name=type,proto3,enum=ga.music_topic_channel.MusicHomePageDialogType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetMusicHomePageDialogV2Req) Reset()         { *m = GetMusicHomePageDialogV2Req{} }
func (m *GetMusicHomePageDialogV2Req) String() string { return proto.CompactTextString(m) }
func (*GetMusicHomePageDialogV2Req) ProtoMessage()    {}
func (*GetMusicHomePageDialogV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{6}
}
func (m *GetMusicHomePageDialogV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageDialogV2Req.Unmarshal(m, b)
}
func (m *GetMusicHomePageDialogV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageDialogV2Req.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageDialogV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageDialogV2Req.Merge(dst, src)
}
func (m *GetMusicHomePageDialogV2Req) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageDialogV2Req.Size(m)
}
func (m *GetMusicHomePageDialogV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageDialogV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageDialogV2Req proto.InternalMessageInfo

func (m *GetMusicHomePageDialogV2Req) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMusicHomePageDialogV2Req) GetType() MusicHomePageDialogType {
	if m != nil {
		return m.Type
	}
	return MusicHomePageDialogType_Ktv_Dialog
}

type GetMusicHomePageDialogV2Resp struct {
	BaseResp             *app.BaseResp                              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Views                []*GetMusicHomePageDialogV2Resp_DialogView `protobuf:"bytes,2,rep,name=views,proto3" json:"views,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-"`
	XXX_unrecognized     []byte                                     `json:"-"`
	XXX_sizecache        int32                                      `json:"-"`
}

func (m *GetMusicHomePageDialogV2Resp) Reset()         { *m = GetMusicHomePageDialogV2Resp{} }
func (m *GetMusicHomePageDialogV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetMusicHomePageDialogV2Resp) ProtoMessage()    {}
func (*GetMusicHomePageDialogV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{7}
}
func (m *GetMusicHomePageDialogV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageDialogV2Resp.Unmarshal(m, b)
}
func (m *GetMusicHomePageDialogV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageDialogV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageDialogV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageDialogV2Resp.Merge(dst, src)
}
func (m *GetMusicHomePageDialogV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageDialogV2Resp.Size(m)
}
func (m *GetMusicHomePageDialogV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageDialogV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageDialogV2Resp proto.InternalMessageInfo

func (m *GetMusicHomePageDialogV2Resp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusicHomePageDialogV2Resp) GetViews() []*GetMusicHomePageDialogV2Resp_DialogView {
	if m != nil {
		return m.Views
	}
	return nil
}

type GetMusicHomePageDialogV2Resp_DialogView struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle             string   `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Image                string   `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	BackColor            string   `protobuf:"bytes,4,opt,name=back_color,json=backColor,proto3" json:"back_color,omitempty"`
	QuickMatchId         string   `protobuf:"bytes,5,opt,name=quick_match_id,json=quickMatchId,proto3" json:"quick_match_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicHomePageDialogV2Resp_DialogView) Reset() {
	*m = GetMusicHomePageDialogV2Resp_DialogView{}
}
func (m *GetMusicHomePageDialogV2Resp_DialogView) String() string { return proto.CompactTextString(m) }
func (*GetMusicHomePageDialogV2Resp_DialogView) ProtoMessage()    {}
func (*GetMusicHomePageDialogV2Resp_DialogView) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{7, 0}
}
func (m *GetMusicHomePageDialogV2Resp_DialogView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicHomePageDialogV2Resp_DialogView.Unmarshal(m, b)
}
func (m *GetMusicHomePageDialogV2Resp_DialogView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicHomePageDialogV2Resp_DialogView.Marshal(b, m, deterministic)
}
func (dst *GetMusicHomePageDialogV2Resp_DialogView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicHomePageDialogV2Resp_DialogView.Merge(dst, src)
}
func (m *GetMusicHomePageDialogV2Resp_DialogView) XXX_Size() int {
	return xxx_messageInfo_GetMusicHomePageDialogV2Resp_DialogView.Size(m)
}
func (m *GetMusicHomePageDialogV2Resp_DialogView) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicHomePageDialogV2Resp_DialogView.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicHomePageDialogV2Resp_DialogView proto.InternalMessageInfo

func (m *GetMusicHomePageDialogV2Resp_DialogView) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMusicHomePageDialogV2Resp_DialogView) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *GetMusicHomePageDialogV2Resp_DialogView) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *GetMusicHomePageDialogV2Resp_DialogView) GetBackColor() string {
	if m != nil {
		return m.BackColor
	}
	return ""
}

func (m *GetMusicHomePageDialogV2Resp_DialogView) GetQuickMatchId() string {
	if m != nil {
		return m.QuickMatchId
	}
	return ""
}

// 快速匹配
type QuickMatchHobbyChannelV2Req struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	QuickMatchId         string       `protobuf:"bytes,2,opt,name=quick_match_id,json=quickMatchId,proto3" json:"quick_match_id,omitempty"`
	ChannelPackageId     string       `protobuf:"bytes,3,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *QuickMatchHobbyChannelV2Req) Reset()         { *m = QuickMatchHobbyChannelV2Req{} }
func (m *QuickMatchHobbyChannelV2Req) String() string { return proto.CompactTextString(m) }
func (*QuickMatchHobbyChannelV2Req) ProtoMessage()    {}
func (*QuickMatchHobbyChannelV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{8}
}
func (m *QuickMatchHobbyChannelV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuickMatchHobbyChannelV2Req.Unmarshal(m, b)
}
func (m *QuickMatchHobbyChannelV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuickMatchHobbyChannelV2Req.Marshal(b, m, deterministic)
}
func (dst *QuickMatchHobbyChannelV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuickMatchHobbyChannelV2Req.Merge(dst, src)
}
func (m *QuickMatchHobbyChannelV2Req) XXX_Size() int {
	return xxx_messageInfo_QuickMatchHobbyChannelV2Req.Size(m)
}
func (m *QuickMatchHobbyChannelV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_QuickMatchHobbyChannelV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_QuickMatchHobbyChannelV2Req proto.InternalMessageInfo

func (m *QuickMatchHobbyChannelV2Req) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *QuickMatchHobbyChannelV2Req) GetQuickMatchId() string {
	if m != nil {
		return m.QuickMatchId
	}
	return ""
}

func (m *QuickMatchHobbyChannelV2Req) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

type QuickMatchHobbyChannelV2Resp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Footprint            string        `protobuf:"bytes,3,opt,name=footprint,proto3" json:"footprint,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *QuickMatchHobbyChannelV2Resp) Reset()         { *m = QuickMatchHobbyChannelV2Resp{} }
func (m *QuickMatchHobbyChannelV2Resp) String() string { return proto.CompactTextString(m) }
func (*QuickMatchHobbyChannelV2Resp) ProtoMessage()    {}
func (*QuickMatchHobbyChannelV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{9}
}
func (m *QuickMatchHobbyChannelV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuickMatchHobbyChannelV2Resp.Unmarshal(m, b)
}
func (m *QuickMatchHobbyChannelV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuickMatchHobbyChannelV2Resp.Marshal(b, m, deterministic)
}
func (dst *QuickMatchHobbyChannelV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuickMatchHobbyChannelV2Resp.Merge(dst, src)
}
func (m *QuickMatchHobbyChannelV2Resp) XXX_Size() int {
	return xxx_messageInfo_QuickMatchHobbyChannelV2Resp.Size(m)
}
func (m *QuickMatchHobbyChannelV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_QuickMatchHobbyChannelV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_QuickMatchHobbyChannelV2Resp proto.InternalMessageInfo

func (m *QuickMatchHobbyChannelV2Resp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *QuickMatchHobbyChannelV2Resp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *QuickMatchHobbyChannelV2Resp) GetFootprint() string {
	if m != nil {
		return m.Footprint
	}
	return ""
}

// 重逢互动 push
type ReunionInteractionPush struct {
	JoinUser             *ReunionUser `protobuf:"bytes,1,opt,name=join_user,json=joinUser,proto3" json:"join_user,omitempty"`
	ReunionUser          *ReunionUser `protobuf:"bytes,2,opt,name=reunion_user,json=reunionUser,proto3" json:"reunion_user,omitempty"`
	Text                 string       `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	MicText              string       `protobuf:"bytes,4,opt,name=mic_text,json=micText,proto3" json:"mic_text,omitempty"`
	MetaId               string       `protobuf:"bytes,5,opt,name=meta_id,json=metaId,proto3" json:"meta_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ReunionInteractionPush) Reset()         { *m = ReunionInteractionPush{} }
func (m *ReunionInteractionPush) String() string { return proto.CompactTextString(m) }
func (*ReunionInteractionPush) ProtoMessage()    {}
func (*ReunionInteractionPush) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{10}
}
func (m *ReunionInteractionPush) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReunionInteractionPush.Unmarshal(m, b)
}
func (m *ReunionInteractionPush) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReunionInteractionPush.Marshal(b, m, deterministic)
}
func (dst *ReunionInteractionPush) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReunionInteractionPush.Merge(dst, src)
}
func (m *ReunionInteractionPush) XXX_Size() int {
	return xxx_messageInfo_ReunionInteractionPush.Size(m)
}
func (m *ReunionInteractionPush) XXX_DiscardUnknown() {
	xxx_messageInfo_ReunionInteractionPush.DiscardUnknown(m)
}

var xxx_messageInfo_ReunionInteractionPush proto.InternalMessageInfo

func (m *ReunionInteractionPush) GetJoinUser() *ReunionUser {
	if m != nil {
		return m.JoinUser
	}
	return nil
}

func (m *ReunionInteractionPush) GetReunionUser() *ReunionUser {
	if m != nil {
		return m.ReunionUser
	}
	return nil
}

func (m *ReunionInteractionPush) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *ReunionInteractionPush) GetMicText() string {
	if m != nil {
		return m.MicText
	}
	return ""
}

func (m *ReunionInteractionPush) GetMetaId() string {
	if m != nil {
		return m.MetaId
	}
	return ""
}

type ReunionUser struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Username             string   `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  int32    `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReunionUser) Reset()         { *m = ReunionUser{} }
func (m *ReunionUser) String() string { return proto.CompactTextString(m) }
func (*ReunionUser) ProtoMessage()    {}
func (*ReunionUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{11}
}
func (m *ReunionUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReunionUser.Unmarshal(m, b)
}
func (m *ReunionUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReunionUser.Marshal(b, m, deterministic)
}
func (dst *ReunionUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReunionUser.Merge(dst, src)
}
func (m *ReunionUser) XXX_Size() int {
	return xxx_messageInfo_ReunionUser.Size(m)
}
func (m *ReunionUser) XXX_DiscardUnknown() {
	xxx_messageInfo_ReunionUser.DiscardUnknown(m)
}

var xxx_messageInfo_ReunionUser proto.InternalMessageInfo

func (m *ReunionUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReunionUser) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *ReunionUser) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ReunionUser) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

// 发布房间
type PublishMusicChannelReq struct {
	BaseReq              *app.BaseReq                 `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32                       `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOptions         []*topic_channel.BlockOption `protobuf:"bytes,4,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	ChannelName          string                       `protobuf:"bytes,5,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ShowGeoInfo          bool                         `protobuf:"varint,6,opt,name=show_geo_info,json=showGeoInfo,proto3" json:"show_geo_info,omitempty"`
	JumpTabId            uint32                       `protobuf:"varint,7,opt,name=jump_tab_id,json=jumpTabId,proto3" json:"jump_tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *PublishMusicChannelReq) Reset()         { *m = PublishMusicChannelReq{} }
func (m *PublishMusicChannelReq) String() string { return proto.CompactTextString(m) }
func (*PublishMusicChannelReq) ProtoMessage()    {}
func (*PublishMusicChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{12}
}
func (m *PublishMusicChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishMusicChannelReq.Unmarshal(m, b)
}
func (m *PublishMusicChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishMusicChannelReq.Marshal(b, m, deterministic)
}
func (dst *PublishMusicChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishMusicChannelReq.Merge(dst, src)
}
func (m *PublishMusicChannelReq) XXX_Size() int {
	return xxx_messageInfo_PublishMusicChannelReq.Size(m)
}
func (m *PublishMusicChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishMusicChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_PublishMusicChannelReq proto.InternalMessageInfo

func (m *PublishMusicChannelReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PublishMusicChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PublishMusicChannelReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *PublishMusicChannelReq) GetBlockOptions() []*topic_channel.BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *PublishMusicChannelReq) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *PublishMusicChannelReq) GetShowGeoInfo() bool {
	if m != nil {
		return m.ShowGeoInfo
	}
	return false
}

func (m *PublishMusicChannelReq) GetJumpTabId() uint32 {
	if m != nil {
		return m.JumpTabId
	}
	return 0
}

type PublishMusicChannelResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChangeCoolDown       uint32        `protobuf:"varint,2,opt,name=change_cool_down,json=changeCoolDown,proto3" json:"change_cool_down,omitempty"`
	FreezeDuration       uint32        `protobuf:"varint,3,opt,name=freeze_duration,json=freezeDuration,proto3" json:"freeze_duration,omitempty"`
	AutoDismissDuration  uint32        `protobuf:"varint,4,opt,name=auto_dismiss_duration,json=autoDismissDuration,proto3" json:"auto_dismiss_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PublishMusicChannelResp) Reset()         { *m = PublishMusicChannelResp{} }
func (m *PublishMusicChannelResp) String() string { return proto.CompactTextString(m) }
func (*PublishMusicChannelResp) ProtoMessage()    {}
func (*PublishMusicChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{13}
}
func (m *PublishMusicChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishMusicChannelResp.Unmarshal(m, b)
}
func (m *PublishMusicChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishMusicChannelResp.Marshal(b, m, deterministic)
}
func (dst *PublishMusicChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishMusicChannelResp.Merge(dst, src)
}
func (m *PublishMusicChannelResp) XXX_Size() int {
	return xxx_messageInfo_PublishMusicChannelResp.Size(m)
}
func (m *PublishMusicChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishMusicChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_PublishMusicChannelResp proto.InternalMessageInfo

func (m *PublishMusicChannelResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PublishMusicChannelResp) GetChangeCoolDown() uint32 {
	if m != nil {
		return m.ChangeCoolDown
	}
	return 0
}

func (m *PublishMusicChannelResp) GetFreezeDuration() uint32 {
	if m != nil {
		return m.FreezeDuration
	}
	return 0
}

func (m *PublishMusicChannelResp) GetAutoDismissDuration() uint32 {
	if m != nil {
		return m.AutoDismissDuration
	}
	return 0
}

// 房间发布取消
type CancelMusicChannelPublishReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CancelMusicChannelPublishReq) Reset()         { *m = CancelMusicChannelPublishReq{} }
func (m *CancelMusicChannelPublishReq) String() string { return proto.CompactTextString(m) }
func (*CancelMusicChannelPublishReq) ProtoMessage()    {}
func (*CancelMusicChannelPublishReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{14}
}
func (m *CancelMusicChannelPublishReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelMusicChannelPublishReq.Unmarshal(m, b)
}
func (m *CancelMusicChannelPublishReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelMusicChannelPublishReq.Marshal(b, m, deterministic)
}
func (dst *CancelMusicChannelPublishReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelMusicChannelPublishReq.Merge(dst, src)
}
func (m *CancelMusicChannelPublishReq) XXX_Size() int {
	return xxx_messageInfo_CancelMusicChannelPublishReq.Size(m)
}
func (m *CancelMusicChannelPublishReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelMusicChannelPublishReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelMusicChannelPublishReq proto.InternalMessageInfo

func (m *CancelMusicChannelPublishReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CancelMusicChannelPublishReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CancelMusicChannelPublishResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CancelMusicChannelPublishResp) Reset()         { *m = CancelMusicChannelPublishResp{} }
func (m *CancelMusicChannelPublishResp) String() string { return proto.CompactTextString(m) }
func (*CancelMusicChannelPublishResp) ProtoMessage()    {}
func (*CancelMusicChannelPublishResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{15}
}
func (m *CancelMusicChannelPublishResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelMusicChannelPublishResp.Unmarshal(m, b)
}
func (m *CancelMusicChannelPublishResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelMusicChannelPublishResp.Marshal(b, m, deterministic)
}
func (dst *CancelMusicChannelPublishResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelMusicChannelPublishResp.Merge(dst, src)
}
func (m *CancelMusicChannelPublishResp) XXX_Size() int {
	return xxx_messageInfo_CancelMusicChannelPublishResp.Size(m)
}
func (m *CancelMusicChannelPublishResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelMusicChannelPublishResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelMusicChannelPublishResp proto.InternalMessageInfo

func (m *CancelMusicChannelPublishResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetMusicFilterItemByIdsReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FilterIds            []string     `protobuf:"bytes,2,rep,name=filter_ids,json=filterIds,proto3" json:"filter_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMusicFilterItemByIdsReq) Reset()         { *m = GetMusicFilterItemByIdsReq{} }
func (m *GetMusicFilterItemByIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicFilterItemByIdsReq) ProtoMessage()    {}
func (*GetMusicFilterItemByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{16}
}
func (m *GetMusicFilterItemByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicFilterItemByIdsReq.Unmarshal(m, b)
}
func (m *GetMusicFilterItemByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicFilterItemByIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicFilterItemByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicFilterItemByIdsReq.Merge(dst, src)
}
func (m *GetMusicFilterItemByIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicFilterItemByIdsReq.Size(m)
}
func (m *GetMusicFilterItemByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicFilterItemByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicFilterItemByIdsReq proto.InternalMessageInfo

func (m *GetMusicFilterItemByIdsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMusicFilterItemByIdsReq) GetFilterIds() []string {
	if m != nil {
		return m.FilterIds
	}
	return nil
}

type GetMusicFilterItemByIdsResp struct {
	BaseResp             *app.BaseResp               `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	FilterMap            map[string]*MusicFilterItem `protobuf:"bytes,2,rep,name=filter_map,json=filterMap,proto3" json:"filter_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetMusicFilterItemByIdsResp) Reset()         { *m = GetMusicFilterItemByIdsResp{} }
func (m *GetMusicFilterItemByIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicFilterItemByIdsResp) ProtoMessage()    {}
func (*GetMusicFilterItemByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{17}
}
func (m *GetMusicFilterItemByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicFilterItemByIdsResp.Unmarshal(m, b)
}
func (m *GetMusicFilterItemByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicFilterItemByIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicFilterItemByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicFilterItemByIdsResp.Merge(dst, src)
}
func (m *GetMusicFilterItemByIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicFilterItemByIdsResp.Size(m)
}
func (m *GetMusicFilterItemByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicFilterItemByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicFilterItemByIdsResp proto.InternalMessageInfo

func (m *GetMusicFilterItemByIdsResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusicFilterItemByIdsResp) GetFilterMap() map[string]*MusicFilterItem {
	if m != nil {
		return m.FilterMap
	}
	return nil
}

type MusicFilterItem struct {
	FilterId             string             `protobuf:"bytes,1,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	Name                 string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string             `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Images               []string           `protobuf:"bytes,4,rep,name=images,proto3" json:"images,omitempty"`
	SubFilters           []*MusicFilterItem `protobuf:"bytes,5,rep,name=sub_filters,json=subFilters,proto3" json:"sub_filters,omitempty"`
	FilterAttrType       uint32             `protobuf:"varint,6,opt,name=filter_attr_type,json=filterAttrType,proto3" json:"filter_attr_type,omitempty"`
	CityTitle            *SameCityTitle     `protobuf:"bytes,7,opt,name=city_title,json=cityTitle,proto3" json:"city_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MusicFilterItem) Reset()         { *m = MusicFilterItem{} }
func (m *MusicFilterItem) String() string { return proto.CompactTextString(m) }
func (*MusicFilterItem) ProtoMessage()    {}
func (*MusicFilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{18}
}
func (m *MusicFilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicFilterItem.Unmarshal(m, b)
}
func (m *MusicFilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicFilterItem.Marshal(b, m, deterministic)
}
func (dst *MusicFilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicFilterItem.Merge(dst, src)
}
func (m *MusicFilterItem) XXX_Size() int {
	return xxx_messageInfo_MusicFilterItem.Size(m)
}
func (m *MusicFilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicFilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_MusicFilterItem proto.InternalMessageInfo

func (m *MusicFilterItem) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *MusicFilterItem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MusicFilterItem) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *MusicFilterItem) GetImages() []string {
	if m != nil {
		return m.Images
	}
	return nil
}

func (m *MusicFilterItem) GetSubFilters() []*MusicFilterItem {
	if m != nil {
		return m.SubFilters
	}
	return nil
}

func (m *MusicFilterItem) GetFilterAttrType() uint32 {
	if m != nil {
		return m.FilterAttrType
	}
	return 0
}

func (m *MusicFilterItem) GetCityTitle() *SameCityTitle {
	if m != nil {
		return m.CityTitle
	}
	return nil
}

type ListMusicChannelsReq struct {
	BaseReq                *app.BaseReq                      `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FilterId               string                            `protobuf:"bytes,2,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	SubFilterIds           []string                          `protobuf:"bytes,3,rep,name=sub_filter_ids,json=subFilterIds,proto3" json:"sub_filter_ids,omitempty"`
	Count                  uint32                            `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	IsStartLoad            bool                              `protobuf:"varint,5,opt,name=is_start_load,json=isStartLoad,proto3" json:"is_start_load,omitempty"`
	ExposeChannelIds       []uint32                          `protobuf:"varint,6,rep,packed,name=expose_channel_ids,json=exposeChannelIds,proto3" json:"expose_channel_ids,omitempty"`
	ChannelPackageId       string                            `protobuf:"bytes,7,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	IsUserLocationAuthOpen bool                              `protobuf:"varint,8,opt,name=is_user_location_auth_open,json=isUserLocationAuthOpen,proto3" json:"is_user_location_auth_open,omitempty"`
	NotExposeChannel       *NotExposeChannelList             `protobuf:"bytes,9,opt,name=not_expose_channel,json=notExposeChannel,proto3" json:"not_expose_channel,omitempty"`
	FilterModel            FilterModel                       `protobuf:"varint,10,opt,name=filter_model,json=filterModel,proto3,enum=ga.music_topic_channel.FilterModel" json:"filter_model,omitempty"`
	TabIds                 []uint32                          `protobuf:"varint,11,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	BlockOption            []*channel_play.FilterBlockOption `protobuf:"bytes,12,rep,name=block_option,json=blockOption,proto3" json:"block_option,omitempty"`
	Labels                 []*topic_channel.GameLabel        `protobuf:"bytes,13,rep,name=labels,proto3" json:"labels,omitempty"`
	ClassifyLabels         []*ClassifyLabellist              `protobuf:"bytes,14,rep,name=classify_labels,json=classifyLabels,proto3" json:"classify_labels,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                          `json:"-"`
	XXX_unrecognized       []byte                            `json:"-"`
	XXX_sizecache          int32                             `json:"-"`
}

func (m *ListMusicChannelsReq) Reset()         { *m = ListMusicChannelsReq{} }
func (m *ListMusicChannelsReq) String() string { return proto.CompactTextString(m) }
func (*ListMusicChannelsReq) ProtoMessage()    {}
func (*ListMusicChannelsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{19}
}
func (m *ListMusicChannelsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicChannelsReq.Unmarshal(m, b)
}
func (m *ListMusicChannelsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicChannelsReq.Marshal(b, m, deterministic)
}
func (dst *ListMusicChannelsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicChannelsReq.Merge(dst, src)
}
func (m *ListMusicChannelsReq) XXX_Size() int {
	return xxx_messageInfo_ListMusicChannelsReq.Size(m)
}
func (m *ListMusicChannelsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicChannelsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicChannelsReq proto.InternalMessageInfo

func (m *ListMusicChannelsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListMusicChannelsReq) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *ListMusicChannelsReq) GetSubFilterIds() []string {
	if m != nil {
		return m.SubFilterIds
	}
	return nil
}

func (m *ListMusicChannelsReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ListMusicChannelsReq) GetIsStartLoad() bool {
	if m != nil {
		return m.IsStartLoad
	}
	return false
}

func (m *ListMusicChannelsReq) GetExposeChannelIds() []uint32 {
	if m != nil {
		return m.ExposeChannelIds
	}
	return nil
}

func (m *ListMusicChannelsReq) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *ListMusicChannelsReq) GetIsUserLocationAuthOpen() bool {
	if m != nil {
		return m.IsUserLocationAuthOpen
	}
	return false
}

func (m *ListMusicChannelsReq) GetNotExposeChannel() *NotExposeChannelList {
	if m != nil {
		return m.NotExposeChannel
	}
	return nil
}

func (m *ListMusicChannelsReq) GetFilterModel() FilterModel {
	if m != nil {
		return m.FilterModel
	}
	return FilterModel_MtModel
}

func (m *ListMusicChannelsReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *ListMusicChannelsReq) GetBlockOption() []*channel_play.FilterBlockOption {
	if m != nil {
		return m.BlockOption
	}
	return nil
}

func (m *ListMusicChannelsReq) GetLabels() []*topic_channel.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *ListMusicChannelsReq) GetClassifyLabels() []*ClassifyLabellist {
	if m != nil {
		return m.ClassifyLabels
	}
	return nil
}

type ClassifyLabellist struct {
	ClassifyName         string                     `protobuf:"bytes,1,opt,name=classify_name,json=classifyName,proto3" json:"classify_name,omitempty"`
	ClassifyLabels       []*topic_channel.GameLabel `protobuf:"bytes,2,rep,name=classify_labels,json=classifyLabels,proto3" json:"classify_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *ClassifyLabellist) Reset()         { *m = ClassifyLabellist{} }
func (m *ClassifyLabellist) String() string { return proto.CompactTextString(m) }
func (*ClassifyLabellist) ProtoMessage()    {}
func (*ClassifyLabellist) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{20}
}
func (m *ClassifyLabellist) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClassifyLabellist.Unmarshal(m, b)
}
func (m *ClassifyLabellist) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClassifyLabellist.Marshal(b, m, deterministic)
}
func (dst *ClassifyLabellist) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClassifyLabellist.Merge(dst, src)
}
func (m *ClassifyLabellist) XXX_Size() int {
	return xxx_messageInfo_ClassifyLabellist.Size(m)
}
func (m *ClassifyLabellist) XXX_DiscardUnknown() {
	xxx_messageInfo_ClassifyLabellist.DiscardUnknown(m)
}

var xxx_messageInfo_ClassifyLabellist proto.InternalMessageInfo

func (m *ClassifyLabellist) GetClassifyName() string {
	if m != nil {
		return m.ClassifyName
	}
	return ""
}

func (m *ClassifyLabellist) GetClassifyLabels() []*topic_channel.GameLabel {
	if m != nil {
		return m.ClassifyLabels
	}
	return nil
}

// 未曝光的房间列表
type NotExposeChannelList struct {
	FilterId               string   `protobuf:"bytes,1,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	NotExposeChannelIdList []uint32 `protobuf:"varint,2,rep,packed,name=not_expose_channel_id_list,json=notExposeChannelIdList,proto3" json:"not_expose_channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *NotExposeChannelList) Reset()         { *m = NotExposeChannelList{} }
func (m *NotExposeChannelList) String() string { return proto.CompactTextString(m) }
func (*NotExposeChannelList) ProtoMessage()    {}
func (*NotExposeChannelList) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{21}
}
func (m *NotExposeChannelList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotExposeChannelList.Unmarshal(m, b)
}
func (m *NotExposeChannelList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotExposeChannelList.Marshal(b, m, deterministic)
}
func (dst *NotExposeChannelList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotExposeChannelList.Merge(dst, src)
}
func (m *NotExposeChannelList) XXX_Size() int {
	return xxx_messageInfo_NotExposeChannelList.Size(m)
}
func (m *NotExposeChannelList) XXX_DiscardUnknown() {
	xxx_messageInfo_NotExposeChannelList.DiscardUnknown(m)
}

var xxx_messageInfo_NotExposeChannelList proto.InternalMessageInfo

func (m *NotExposeChannelList) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *NotExposeChannelList) GetNotExposeChannelIdList() []uint32 {
	if m != nil {
		return m.NotExposeChannelIdList
	}
	return nil
}

type ListMusicChannelsResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Channels             []*MusicChannel `protobuf:"bytes,2,rep,name=channels,proto3" json:"channels,omitempty"`
	IsBottomReach        bool            `protobuf:"varint,3,opt,name=is_bottom_reach,json=isBottomReach,proto3" json:"is_bottom_reach,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListMusicChannelsResp) Reset()         { *m = ListMusicChannelsResp{} }
func (m *ListMusicChannelsResp) String() string { return proto.CompactTextString(m) }
func (*ListMusicChannelsResp) ProtoMessage()    {}
func (*ListMusicChannelsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{22}
}
func (m *ListMusicChannelsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMusicChannelsResp.Unmarshal(m, b)
}
func (m *ListMusicChannelsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMusicChannelsResp.Marshal(b, m, deterministic)
}
func (dst *ListMusicChannelsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMusicChannelsResp.Merge(dst, src)
}
func (m *ListMusicChannelsResp) XXX_Size() int {
	return xxx_messageInfo_ListMusicChannelsResp.Size(m)
}
func (m *ListMusicChannelsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMusicChannelsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListMusicChannelsResp proto.InternalMessageInfo

func (m *ListMusicChannelsResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListMusicChannelsResp) GetChannels() []*MusicChannel {
	if m != nil {
		return m.Channels
	}
	return nil
}

func (m *ListMusicChannelsResp) GetIsBottomReach() bool {
	if m != nil {
		return m.IsBottomReach
	}
	return false
}

type MusicChannel struct {
	ChannelId          uint32                    `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName        string                    `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelMemberCount uint32                    `protobuf:"varint,3,opt,name=channel_member_count,json=channelMemberCount,proto3" json:"channel_member_count,omitempty"`
	TabIcon            string                    `protobuf:"bytes,4,opt,name=tab_icon,json=tabIcon,proto3" json:"tab_icon,omitempty"`
	TabDesc            string                    `protobuf:"bytes,5,opt,name=tab_desc,json=tabDesc,proto3" json:"tab_desc,omitempty"`
	OwnerAccount       string                    `protobuf:"bytes,6,opt,name=owner_account,json=ownerAccount,proto3" json:"owner_account,omitempty"`
	OwnerSex           int32                     `protobuf:"varint,7,opt,name=owner_sex,json=ownerSex,proto3" json:"owner_sex,omitempty"`
	Accounts           []string                  `protobuf:"bytes,8,rep,name=accounts,proto3" json:"accounts,omitempty"`
	Status             string                    `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`
	Song               string                    `protobuf:"bytes,10,opt,name=song,proto3" json:"song,omitempty"`
	Review             *MusicChannelReview       `protobuf:"bytes,11,opt,name=review,proto3" json:"review,omitempty"`
	Label              MusicChannelLabel         `protobuf:"varint,12,opt,name=label,proto3,enum=ga.music_topic_channel.MusicChannelLabel" json:"label,omitempty"`
	Glory              *KtvGlory                 `protobuf:"bytes,13,opt,name=glory,proto3" json:"glory,omitempty"`
	PersonalCert       *MusicChannelPersonalCert `protobuf:"bytes,14,opt,name=personal_cert,json=personalCert,proto3" json:"personal_cert,omitempty"`
	// 非业务必须字段，埋点需要
	Footprint      string `protobuf:"bytes,15,opt,name=footprint,proto3" json:"footprint,omitempty"`
	TabId          uint32 `protobuf:"varint,16,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	RegionId       uint64 `protobuf:"varint,17,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	ChannelLevelId uint32 `protobuf:"varint,18,opt,name=channel_level_id,json=channelLevelId,proto3" json:"channel_level_id,omitempty"`
	// 非业务必须字段，埋点需要
	TabName              string                                            `protobuf:"bytes,19,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Pia                  *MusicPia                                         `protobuf:"bytes,20,opt,name=pia,proto3" json:"pia,omitempty"`
	Interesting          *MusicInteresting                                 `protobuf:"bytes,21,opt,name=interesting,proto3" json:"interesting,omitempty"`
	Logo                 string                                            `protobuf:"bytes,22,opt,name=logo,proto3" json:"logo,omitempty"`
	MusicSocial          *MusicSocial                                      `protobuf:"bytes,23,opt,name=music_social,json=musicSocial,proto3" json:"music_social,omitempty"`
	SameCity             *muse_interest_hub_logic.TopicChannelSameCityInfo `protobuf:"bytes,24,opt,name=same_city,json=sameCity,proto3" json:"same_city,omitempty"`
	ChannelType          uint32                                            `protobuf:"varint,25,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	Topic                string                                            `protobuf:"bytes,26,opt,name=topic,proto3" json:"topic,omitempty"`
	TopicIcon            string                                            `protobuf:"bytes,27,opt,name=topic_icon,json=topicIcon,proto3" json:"topic_icon,omitempty"`
	TopicType            int32                                             `protobuf:"varint,28,opt,name=topic_type,json=topicType,proto3" json:"topic_type,omitempty"`
	ShiningPoint         []*MuseShiningPoint                               `protobuf:"bytes,29,rep,name=shining_point,json=shiningPoint,proto3" json:"shining_point,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                          `json:"-"`
	XXX_unrecognized     []byte                                            `json:"-"`
	XXX_sizecache        int32                                             `json:"-"`
}

func (m *MusicChannel) Reset()         { *m = MusicChannel{} }
func (m *MusicChannel) String() string { return proto.CompactTextString(m) }
func (*MusicChannel) ProtoMessage()    {}
func (*MusicChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{23}
}
func (m *MusicChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicChannel.Unmarshal(m, b)
}
func (m *MusicChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicChannel.Marshal(b, m, deterministic)
}
func (dst *MusicChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicChannel.Merge(dst, src)
}
func (m *MusicChannel) XXX_Size() int {
	return xxx_messageInfo_MusicChannel.Size(m)
}
func (m *MusicChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicChannel.DiscardUnknown(m)
}

var xxx_messageInfo_MusicChannel proto.InternalMessageInfo

func (m *MusicChannel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MusicChannel) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *MusicChannel) GetChannelMemberCount() uint32 {
	if m != nil {
		return m.ChannelMemberCount
	}
	return 0
}

func (m *MusicChannel) GetTabIcon() string {
	if m != nil {
		return m.TabIcon
	}
	return ""
}

func (m *MusicChannel) GetTabDesc() string {
	if m != nil {
		return m.TabDesc
	}
	return ""
}

func (m *MusicChannel) GetOwnerAccount() string {
	if m != nil {
		return m.OwnerAccount
	}
	return ""
}

func (m *MusicChannel) GetOwnerSex() int32 {
	if m != nil {
		return m.OwnerSex
	}
	return 0
}

func (m *MusicChannel) GetAccounts() []string {
	if m != nil {
		return m.Accounts
	}
	return nil
}

func (m *MusicChannel) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *MusicChannel) GetSong() string {
	if m != nil {
		return m.Song
	}
	return ""
}

func (m *MusicChannel) GetReview() *MusicChannelReview {
	if m != nil {
		return m.Review
	}
	return nil
}

func (m *MusicChannel) GetLabel() MusicChannelLabel {
	if m != nil {
		return m.Label
	}
	return MusicChannelLabel_MusicChannelLabelNone
}

func (m *MusicChannel) GetGlory() *KtvGlory {
	if m != nil {
		return m.Glory
	}
	return nil
}

func (m *MusicChannel) GetPersonalCert() *MusicChannelPersonalCert {
	if m != nil {
		return m.PersonalCert
	}
	return nil
}

func (m *MusicChannel) GetFootprint() string {
	if m != nil {
		return m.Footprint
	}
	return ""
}

func (m *MusicChannel) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *MusicChannel) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *MusicChannel) GetChannelLevelId() uint32 {
	if m != nil {
		return m.ChannelLevelId
	}
	return 0
}

func (m *MusicChannel) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *MusicChannel) GetPia() *MusicPia {
	if m != nil {
		return m.Pia
	}
	return nil
}

func (m *MusicChannel) GetInteresting() *MusicInteresting {
	if m != nil {
		return m.Interesting
	}
	return nil
}

func (m *MusicChannel) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *MusicChannel) GetMusicSocial() *MusicSocial {
	if m != nil {
		return m.MusicSocial
	}
	return nil
}

func (m *MusicChannel) GetSameCity() *muse_interest_hub_logic.TopicChannelSameCityInfo {
	if m != nil {
		return m.SameCity
	}
	return nil
}

func (m *MusicChannel) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *MusicChannel) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *MusicChannel) GetTopicIcon() string {
	if m != nil {
		return m.TopicIcon
	}
	return ""
}

func (m *MusicChannel) GetTopicType() int32 {
	if m != nil {
		return m.TopicType
	}
	return 0
}

func (m *MusicChannel) GetShiningPoint() []*MuseShiningPoint {
	if m != nil {
		return m.ShiningPoint
	}
	return nil
}

type MuseShiningPoint struct {
	ShiningPointId       string   `protobuf:"bytes,1,opt,name=shining_point_id,json=shiningPointId,proto3" json:"shining_point_id,omitempty"`
	ShiningPointName     string   `protobuf:"bytes,2,opt,name=shining_point_name,json=shiningPointName,proto3" json:"shining_point_name,omitempty"`
	ShiningPointCertType uint32   `protobuf:"varint,3,opt,name=shining_point_cert_type,json=shiningPointCertType,proto3" json:"shining_point_cert_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseShiningPoint) Reset()         { *m = MuseShiningPoint{} }
func (m *MuseShiningPoint) String() string { return proto.CompactTextString(m) }
func (*MuseShiningPoint) ProtoMessage()    {}
func (*MuseShiningPoint) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{24}
}
func (m *MuseShiningPoint) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseShiningPoint.Unmarshal(m, b)
}
func (m *MuseShiningPoint) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseShiningPoint.Marshal(b, m, deterministic)
}
func (dst *MuseShiningPoint) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseShiningPoint.Merge(dst, src)
}
func (m *MuseShiningPoint) XXX_Size() int {
	return xxx_messageInfo_MuseShiningPoint.Size(m)
}
func (m *MuseShiningPoint) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseShiningPoint.DiscardUnknown(m)
}

var xxx_messageInfo_MuseShiningPoint proto.InternalMessageInfo

func (m *MuseShiningPoint) GetShiningPointId() string {
	if m != nil {
		return m.ShiningPointId
	}
	return ""
}

func (m *MuseShiningPoint) GetShiningPointName() string {
	if m != nil {
		return m.ShiningPointName
	}
	return ""
}

func (m *MuseShiningPoint) GetShiningPointCertType() uint32 {
	if m != nil {
		return m.ShiningPointCertType
	}
	return 0
}

type MusicSocialRankHonorSignInfo struct {
	Icon                 string   `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	StyleColorList       []string `protobuf:"bytes,2,rep,name=style_color_list,json=styleColorList,proto3" json:"style_color_list,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicSocialRankHonorSignInfo) Reset()         { *m = MusicSocialRankHonorSignInfo{} }
func (m *MusicSocialRankHonorSignInfo) String() string { return proto.CompactTextString(m) }
func (*MusicSocialRankHonorSignInfo) ProtoMessage()    {}
func (*MusicSocialRankHonorSignInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{25}
}
func (m *MusicSocialRankHonorSignInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicSocialRankHonorSignInfo.Unmarshal(m, b)
}
func (m *MusicSocialRankHonorSignInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicSocialRankHonorSignInfo.Marshal(b, m, deterministic)
}
func (dst *MusicSocialRankHonorSignInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicSocialRankHonorSignInfo.Merge(dst, src)
}
func (m *MusicSocialRankHonorSignInfo) XXX_Size() int {
	return xxx_messageInfo_MusicSocialRankHonorSignInfo.Size(m)
}
func (m *MusicSocialRankHonorSignInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicSocialRankHonorSignInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MusicSocialRankHonorSignInfo proto.InternalMessageInfo

func (m *MusicSocialRankHonorSignInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *MusicSocialRankHonorSignInfo) GetStyleColorList() []string {
	if m != nil {
		return m.StyleColorList
	}
	return nil
}

func (m *MusicSocialRankHonorSignInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type MusicSocial struct {
	MemberLabelBg        string                        `protobuf:"bytes,2,opt,name=member_label_bg,json=memberLabelBg,proto3" json:"member_label_bg,omitempty"`
	MemberLabelText      string                        `protobuf:"bytes,3,opt,name=member_label_text,json=memberLabelText,proto3" json:"member_label_text,omitempty"`
	RankSignInfo         *MusicSocialRankHonorSignInfo `protobuf:"bytes,4,opt,name=rank_sign_info,json=rankSignInfo,proto3" json:"rank_sign_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *MusicSocial) Reset()         { *m = MusicSocial{} }
func (m *MusicSocial) String() string { return proto.CompactTextString(m) }
func (*MusicSocial) ProtoMessage()    {}
func (*MusicSocial) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{26}
}
func (m *MusicSocial) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicSocial.Unmarshal(m, b)
}
func (m *MusicSocial) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicSocial.Marshal(b, m, deterministic)
}
func (dst *MusicSocial) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicSocial.Merge(dst, src)
}
func (m *MusicSocial) XXX_Size() int {
	return xxx_messageInfo_MusicSocial.Size(m)
}
func (m *MusicSocial) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicSocial.DiscardUnknown(m)
}

var xxx_messageInfo_MusicSocial proto.InternalMessageInfo

func (m *MusicSocial) GetMemberLabelBg() string {
	if m != nil {
		return m.MemberLabelBg
	}
	return ""
}

func (m *MusicSocial) GetMemberLabelText() string {
	if m != nil {
		return m.MemberLabelText
	}
	return ""
}

func (m *MusicSocial) GetRankSignInfo() *MusicSocialRankHonorSignInfo {
	if m != nil {
		return m.RankSignInfo
	}
	return nil
}

type MusicPia struct {
	Label                []string `protobuf:"bytes,1,rep,name=label,proto3" json:"label,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicPia) Reset()         { *m = MusicPia{} }
func (m *MusicPia) String() string { return proto.CompactTextString(m) }
func (*MusicPia) ProtoMessage()    {}
func (*MusicPia) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{27}
}
func (m *MusicPia) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicPia.Unmarshal(m, b)
}
func (m *MusicPia) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicPia.Marshal(b, m, deterministic)
}
func (dst *MusicPia) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicPia.Merge(dst, src)
}
func (m *MusicPia) XXX_Size() int {
	return xxx_messageInfo_MusicPia.Size(m)
}
func (m *MusicPia) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicPia.DiscardUnknown(m)
}

var xxx_messageInfo_MusicPia proto.InternalMessageInfo

func (m *MusicPia) GetLabel() []string {
	if m != nil {
		return m.Label
	}
	return nil
}

func (m *MusicPia) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type MusicInteresting struct {
	Topic                string   `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicInteresting) Reset()         { *m = MusicInteresting{} }
func (m *MusicInteresting) String() string { return proto.CompactTextString(m) }
func (*MusicInteresting) ProtoMessage()    {}
func (*MusicInteresting) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{28}
}
func (m *MusicInteresting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicInteresting.Unmarshal(m, b)
}
func (m *MusicInteresting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicInteresting.Marshal(b, m, deterministic)
}
func (dst *MusicInteresting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicInteresting.Merge(dst, src)
}
func (m *MusicInteresting) XXX_Size() int {
	return xxx_messageInfo_MusicInteresting.Size(m)
}
func (m *MusicInteresting) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicInteresting.DiscardUnknown(m)
}

var xxx_messageInfo_MusicInteresting proto.InternalMessageInfo

func (m *MusicInteresting) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

// 重逢
type MusicChannelReview struct {
	ReviewAccount        string   `protobuf:"bytes,1,opt,name=review_account,json=reviewAccount,proto3" json:"review_account,omitempty"`
	ReviewDesc           string   `protobuf:"bytes,2,opt,name=review_desc,json=reviewDesc,proto3" json:"review_desc,omitempty"`
	ReviewSex            int32    `protobuf:"varint,3,opt,name=review_sex,json=reviewSex,proto3" json:"review_sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicChannelReview) Reset()         { *m = MusicChannelReview{} }
func (m *MusicChannelReview) String() string { return proto.CompactTextString(m) }
func (*MusicChannelReview) ProtoMessage()    {}
func (*MusicChannelReview) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{29}
}
func (m *MusicChannelReview) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicChannelReview.Unmarshal(m, b)
}
func (m *MusicChannelReview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicChannelReview.Marshal(b, m, deterministic)
}
func (dst *MusicChannelReview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicChannelReview.Merge(dst, src)
}
func (m *MusicChannelReview) XXX_Size() int {
	return xxx_messageInfo_MusicChannelReview.Size(m)
}
func (m *MusicChannelReview) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicChannelReview.DiscardUnknown(m)
}

var xxx_messageInfo_MusicChannelReview proto.InternalMessageInfo

func (m *MusicChannelReview) GetReviewAccount() string {
	if m != nil {
		return m.ReviewAccount
	}
	return ""
}

func (m *MusicChannelReview) GetReviewDesc() string {
	if m != nil {
		return m.ReviewDesc
	}
	return ""
}

func (m *MusicChannelReview) GetReviewSex() int32 {
	if m != nil {
		return m.ReviewSex
	}
	return 0
}

type KtvGlory struct {
	GloryName            string   `protobuf:"bytes,1,opt,name=glory_name,json=gloryName,proto3" json:"glory_name,omitempty"`
	GloryImg             string   `protobuf:"bytes,2,opt,name=glory_img,json=gloryImg,proto3" json:"glory_img,omitempty"`
	GloryBgImg           string   `protobuf:"bytes,3,opt,name=glory_bg_img,json=gloryBgImg,proto3" json:"glory_bg_img,omitempty"`
	GloryRank            uint32   `protobuf:"varint,4,opt,name=glory_rank,json=gloryRank,proto3" json:"glory_rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KtvGlory) Reset()         { *m = KtvGlory{} }
func (m *KtvGlory) String() string { return proto.CompactTextString(m) }
func (*KtvGlory) ProtoMessage()    {}
func (*KtvGlory) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{30}
}
func (m *KtvGlory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KtvGlory.Unmarshal(m, b)
}
func (m *KtvGlory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KtvGlory.Marshal(b, m, deterministic)
}
func (dst *KtvGlory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KtvGlory.Merge(dst, src)
}
func (m *KtvGlory) XXX_Size() int {
	return xxx_messageInfo_KtvGlory.Size(m)
}
func (m *KtvGlory) XXX_DiscardUnknown() {
	xxx_messageInfo_KtvGlory.DiscardUnknown(m)
}

var xxx_messageInfo_KtvGlory proto.InternalMessageInfo

func (m *KtvGlory) GetGloryName() string {
	if m != nil {
		return m.GloryName
	}
	return ""
}

func (m *KtvGlory) GetGloryImg() string {
	if m != nil {
		return m.GloryImg
	}
	return ""
}

func (m *KtvGlory) GetGloryBgImg() string {
	if m != nil {
		return m.GloryBgImg
	}
	return ""
}

func (m *KtvGlory) GetGloryRank() uint32 {
	if m != nil {
		return m.GloryRank
	}
	return 0
}

type MusicChannelPersonalCert struct {
	Icon                 string   `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Color                []string `protobuf:"bytes,3,rep,name=color,proto3" json:"color,omitempty"`
	TextShadowColor      string   `protobuf:"bytes,4,opt,name=text_shadow_color,json=textShadowColor,proto3" json:"text_shadow_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicChannelPersonalCert) Reset()         { *m = MusicChannelPersonalCert{} }
func (m *MusicChannelPersonalCert) String() string { return proto.CompactTextString(m) }
func (*MusicChannelPersonalCert) ProtoMessage()    {}
func (*MusicChannelPersonalCert) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{31}
}
func (m *MusicChannelPersonalCert) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicChannelPersonalCert.Unmarshal(m, b)
}
func (m *MusicChannelPersonalCert) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicChannelPersonalCert.Marshal(b, m, deterministic)
}
func (dst *MusicChannelPersonalCert) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicChannelPersonalCert.Merge(dst, src)
}
func (m *MusicChannelPersonalCert) XXX_Size() int {
	return xxx_messageInfo_MusicChannelPersonalCert.Size(m)
}
func (m *MusicChannelPersonalCert) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicChannelPersonalCert.DiscardUnknown(m)
}

var xxx_messageInfo_MusicChannelPersonalCert proto.InternalMessageInfo

func (m *MusicChannelPersonalCert) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *MusicChannelPersonalCert) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *MusicChannelPersonalCert) GetColor() []string {
	if m != nil {
		return m.Color
	}
	return nil
}

func (m *MusicChannelPersonalCert) GetTextShadowColor() string {
	if m != nil {
		return m.TextShadowColor
	}
	return ""
}

type GetTabPublishHotRcmdReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetTabPublishHotRcmdReq) Reset()         { *m = GetTabPublishHotRcmdReq{} }
func (m *GetTabPublishHotRcmdReq) String() string { return proto.CompactTextString(m) }
func (*GetTabPublishHotRcmdReq) ProtoMessage()    {}
func (*GetTabPublishHotRcmdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{32}
}
func (m *GetTabPublishHotRcmdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabPublishHotRcmdReq.Unmarshal(m, b)
}
func (m *GetTabPublishHotRcmdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabPublishHotRcmdReq.Marshal(b, m, deterministic)
}
func (dst *GetTabPublishHotRcmdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabPublishHotRcmdReq.Merge(dst, src)
}
func (m *GetTabPublishHotRcmdReq) XXX_Size() int {
	return xxx_messageInfo_GetTabPublishHotRcmdReq.Size(m)
}
func (m *GetTabPublishHotRcmdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabPublishHotRcmdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabPublishHotRcmdReq proto.InternalMessageInfo

func (m *GetTabPublishHotRcmdReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetTabPublishHotRcmdReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetTabPublishHotRcmdResp struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Items                []*TabPublishHotRcmd `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetTabPublishHotRcmdResp) Reset()         { *m = GetTabPublishHotRcmdResp{} }
func (m *GetTabPublishHotRcmdResp) String() string { return proto.CompactTextString(m) }
func (*GetTabPublishHotRcmdResp) ProtoMessage()    {}
func (*GetTabPublishHotRcmdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{33}
}
func (m *GetTabPublishHotRcmdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabPublishHotRcmdResp.Unmarshal(m, b)
}
func (m *GetTabPublishHotRcmdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabPublishHotRcmdResp.Marshal(b, m, deterministic)
}
func (dst *GetTabPublishHotRcmdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabPublishHotRcmdResp.Merge(dst, src)
}
func (m *GetTabPublishHotRcmdResp) XXX_Size() int {
	return xxx_messageInfo_GetTabPublishHotRcmdResp.Size(m)
}
func (m *GetTabPublishHotRcmdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabPublishHotRcmdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabPublishHotRcmdResp proto.InternalMessageInfo

func (m *GetTabPublishHotRcmdResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetTabPublishHotRcmdResp) GetItems() []*TabPublishHotRcmd {
	if m != nil {
		return m.Items
	}
	return nil
}

type TabPublishHotRcmd struct {
	Id                   string        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Hint                 string        `protobuf:"bytes,3,opt,name=hint,proto3" json:"hint,omitempty"`
	Blocks               []*MusicBlock `protobuf:"bytes,4,rep,name=blocks,proto3" json:"blocks,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *TabPublishHotRcmd) Reset()         { *m = TabPublishHotRcmd{} }
func (m *TabPublishHotRcmd) String() string { return proto.CompactTextString(m) }
func (*TabPublishHotRcmd) ProtoMessage()    {}
func (*TabPublishHotRcmd) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{34}
}
func (m *TabPublishHotRcmd) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabPublishHotRcmd.Unmarshal(m, b)
}
func (m *TabPublishHotRcmd) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabPublishHotRcmd.Marshal(b, m, deterministic)
}
func (dst *TabPublishHotRcmd) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabPublishHotRcmd.Merge(dst, src)
}
func (m *TabPublishHotRcmd) XXX_Size() int {
	return xxx_messageInfo_TabPublishHotRcmd.Size(m)
}
func (m *TabPublishHotRcmd) XXX_DiscardUnknown() {
	xxx_messageInfo_TabPublishHotRcmd.DiscardUnknown(m)
}

var xxx_messageInfo_TabPublishHotRcmd proto.InternalMessageInfo

func (m *TabPublishHotRcmd) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *TabPublishHotRcmd) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TabPublishHotRcmd) GetHint() string {
	if m != nil {
		return m.Hint
	}
	return ""
}

func (m *TabPublishHotRcmd) GetBlocks() []*MusicBlock {
	if m != nil {
		return m.Blocks
	}
	return nil
}

type MusicBlock struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElementId            uint32   `protobuf:"varint,2,opt,name=element_id,json=elementId,proto3" json:"element_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicBlock) Reset()         { *m = MusicBlock{} }
func (m *MusicBlock) String() string { return proto.CompactTextString(m) }
func (*MusicBlock) ProtoMessage()    {}
func (*MusicBlock) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{35}
}
func (m *MusicBlock) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicBlock.Unmarshal(m, b)
}
func (m *MusicBlock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicBlock.Marshal(b, m, deterministic)
}
func (dst *MusicBlock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicBlock.Merge(dst, src)
}
func (m *MusicBlock) XXX_Size() int {
	return xxx_messageInfo_MusicBlock.Size(m)
}
func (m *MusicBlock) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicBlock.DiscardUnknown(m)
}

var xxx_messageInfo_MusicBlock proto.InternalMessageInfo

func (m *MusicBlock) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *MusicBlock) GetElementId() uint32 {
	if m != nil {
		return m.ElementId
	}
	return 0
}

type GetResourceConfigByChannelIdReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetResourceConfigByChannelIdReq) Reset()         { *m = GetResourceConfigByChannelIdReq{} }
func (m *GetResourceConfigByChannelIdReq) String() string { return proto.CompactTextString(m) }
func (*GetResourceConfigByChannelIdReq) ProtoMessage()    {}
func (*GetResourceConfigByChannelIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{36}
}
func (m *GetResourceConfigByChannelIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetResourceConfigByChannelIdReq.Unmarshal(m, b)
}
func (m *GetResourceConfigByChannelIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetResourceConfigByChannelIdReq.Marshal(b, m, deterministic)
}
func (dst *GetResourceConfigByChannelIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResourceConfigByChannelIdReq.Merge(dst, src)
}
func (m *GetResourceConfigByChannelIdReq) XXX_Size() int {
	return xxx_messageInfo_GetResourceConfigByChannelIdReq.Size(m)
}
func (m *GetResourceConfigByChannelIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResourceConfigByChannelIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetResourceConfigByChannelIdReq proto.InternalMessageInfo

func (m *GetResourceConfigByChannelIdReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetResourceConfigByChannelIdReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetResourceConfigByChannelIdResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Text                 string        `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Icon                 string        `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	JumpUrl              string        `protobuf:"bytes,4,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetResourceConfigByChannelIdResp) Reset()         { *m = GetResourceConfigByChannelIdResp{} }
func (m *GetResourceConfigByChannelIdResp) String() string { return proto.CompactTextString(m) }
func (*GetResourceConfigByChannelIdResp) ProtoMessage()    {}
func (*GetResourceConfigByChannelIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{37}
}
func (m *GetResourceConfigByChannelIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetResourceConfigByChannelIdResp.Unmarshal(m, b)
}
func (m *GetResourceConfigByChannelIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetResourceConfigByChannelIdResp.Marshal(b, m, deterministic)
}
func (dst *GetResourceConfigByChannelIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResourceConfigByChannelIdResp.Merge(dst, src)
}
func (m *GetResourceConfigByChannelIdResp) XXX_Size() int {
	return xxx_messageInfo_GetResourceConfigByChannelIdResp.Size(m)
}
func (m *GetResourceConfigByChannelIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResourceConfigByChannelIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetResourceConfigByChannelIdResp proto.InternalMessageInfo

func (m *GetResourceConfigByChannelIdResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetResourceConfigByChannelIdResp) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *GetResourceConfigByChannelIdResp) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GetResourceConfigByChannelIdResp) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

type MuseGetTopicChannelInfoRequest struct {
	BaseReq              *app.BaseReq                                   `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                                         `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelInfoType      MuseGetTopicChannelInfoRequest_ChannelInfoType `protobuf:"varint,3,opt,name=channel_info_type,json=channelInfoType,proto3,enum=ga.music_topic_channel.MuseGetTopicChannelInfoRequest_ChannelInfoType" json:"channel_info_type,omitempty"`
	ChannelType          uint32                                         `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                       `json:"-"`
	XXX_unrecognized     []byte                                         `json:"-"`
	XXX_sizecache        int32                                          `json:"-"`
}

func (m *MuseGetTopicChannelInfoRequest) Reset()         { *m = MuseGetTopicChannelInfoRequest{} }
func (m *MuseGetTopicChannelInfoRequest) String() string { return proto.CompactTextString(m) }
func (*MuseGetTopicChannelInfoRequest) ProtoMessage()    {}
func (*MuseGetTopicChannelInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{38}
}
func (m *MuseGetTopicChannelInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseGetTopicChannelInfoRequest.Unmarshal(m, b)
}
func (m *MuseGetTopicChannelInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseGetTopicChannelInfoRequest.Marshal(b, m, deterministic)
}
func (dst *MuseGetTopicChannelInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseGetTopicChannelInfoRequest.Merge(dst, src)
}
func (m *MuseGetTopicChannelInfoRequest) XXX_Size() int {
	return xxx_messageInfo_MuseGetTopicChannelInfoRequest.Size(m)
}
func (m *MuseGetTopicChannelInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseGetTopicChannelInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MuseGetTopicChannelInfoRequest proto.InternalMessageInfo

func (m *MuseGetTopicChannelInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MuseGetTopicChannelInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseGetTopicChannelInfoRequest) GetChannelInfoType() MuseGetTopicChannelInfoRequest_ChannelInfoType {
	if m != nil {
		return m.ChannelInfoType
	}
	return MuseGetTopicChannelInfoRequest_CHANNEL_INFO_TYPE_NORMAL_UNSPECIFIED
}

func (m *MuseGetTopicChannelInfoRequest) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type MuseGetTopicChannelInfoResponse struct {
	BaseResp       *app.BaseResp                           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId      uint32                                  `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId          uint32                                  `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName        string                                  `protobuf:"bytes,4,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	IsInGround     bool                                    `protobuf:"varint,5,opt,name=is_in_ground,json=isInGround,proto3" json:"is_in_ground,omitempty"`
	IsPrivate      bool                                    `protobuf:"varint,6,opt,name=is_private,json=isPrivate,proto3" json:"is_private,omitempty"`
	HeadDesc       string                                  `protobuf:"bytes,7,opt,name=head_desc,json=headDesc,proto3" json:"head_desc,omitempty"`
	PlayingOption  []MusePlayingOption                     `protobuf:"varint,8,rep,packed,name=playing_option,json=playingOption,proto3,enum=ga.music_topic_channel.MusePlayingOption" json:"playing_option,omitempty"`
	SwitchPlayInfo *MuseSwitchPlayInfo                     `protobuf:"bytes,9,opt,name=switch_play_info,json=switchPlayInfo,proto3" json:"switch_play_info,omitempty"`
	TabType        MuseGetTopicChannelInfoResponse_TabType `protobuf:"varint,10,opt,name=tab_type,json=tabType,proto3,enum=ga.music_topic_channel.MuseGetTopicChannelInfoResponse_TabType" json:"tab_type,omitempty"`
	WelcomeText    string                                  `protobuf:"bytes,11,opt,name=welcome_text,json=welcomeText,proto3" json:"welcome_text,omitempty"`
	// 从房间内跳到指定外部第三方游戏相关配置(即下载游戏)
	ThirdPartyGame       *MuseThirdPartyGame `protobuf:"bytes,12,opt,name=third_party_game,json=thirdPartyGame,proto3" json:"third_party_game,omitempty"`
	ShiftRoomDuration    uint32              `protobuf:"varint,13,opt,name=shift_room_duration,json=shiftRoomDuration,proto3" json:"shift_room_duration,omitempty"`
	FreezeDuration       uint32              `protobuf:"varint,14,opt,name=freeze_duration,json=freezeDuration,proto3" json:"freeze_duration,omitempty"`
	TagId                uint32              `protobuf:"varint,15,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TeamDesc             string              `protobuf:"bytes,16,opt,name=team_desc,json=teamDesc,proto3" json:"team_desc,omitempty"`
	ShowTeamDesc         bool                `protobuf:"varint,17,opt,name=show_team_desc,json=showTeamDesc,proto3" json:"show_team_desc,omitempty"`
	ShowPublishButton    bool                `protobuf:"varint,18,opt,name=show_publish_button,json=showPublishButton,proto3" json:"show_publish_button,omitempty"`
	CategoryType         MuseCategoryType    `protobuf:"varint,19,opt,name=category_type,json=categoryType,proto3,enum=ga.music_topic_channel.MuseCategoryType" json:"category_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *MuseGetTopicChannelInfoResponse) Reset()         { *m = MuseGetTopicChannelInfoResponse{} }
func (m *MuseGetTopicChannelInfoResponse) String() string { return proto.CompactTextString(m) }
func (*MuseGetTopicChannelInfoResponse) ProtoMessage()    {}
func (*MuseGetTopicChannelInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{39}
}
func (m *MuseGetTopicChannelInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseGetTopicChannelInfoResponse.Unmarshal(m, b)
}
func (m *MuseGetTopicChannelInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseGetTopicChannelInfoResponse.Marshal(b, m, deterministic)
}
func (dst *MuseGetTopicChannelInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseGetTopicChannelInfoResponse.Merge(dst, src)
}
func (m *MuseGetTopicChannelInfoResponse) XXX_Size() int {
	return xxx_messageInfo_MuseGetTopicChannelInfoResponse.Size(m)
}
func (m *MuseGetTopicChannelInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseGetTopicChannelInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MuseGetTopicChannelInfoResponse proto.InternalMessageInfo

func (m *MuseGetTopicChannelInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MuseGetTopicChannelInfoResponse) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseGetTopicChannelInfoResponse) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *MuseGetTopicChannelInfoResponse) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *MuseGetTopicChannelInfoResponse) GetIsInGround() bool {
	if m != nil {
		return m.IsInGround
	}
	return false
}

func (m *MuseGetTopicChannelInfoResponse) GetIsPrivate() bool {
	if m != nil {
		return m.IsPrivate
	}
	return false
}

func (m *MuseGetTopicChannelInfoResponse) GetHeadDesc() string {
	if m != nil {
		return m.HeadDesc
	}
	return ""
}

func (m *MuseGetTopicChannelInfoResponse) GetPlayingOption() []MusePlayingOption {
	if m != nil {
		return m.PlayingOption
	}
	return nil
}

func (m *MuseGetTopicChannelInfoResponse) GetSwitchPlayInfo() *MuseSwitchPlayInfo {
	if m != nil {
		return m.SwitchPlayInfo
	}
	return nil
}

func (m *MuseGetTopicChannelInfoResponse) GetTabType() MuseGetTopicChannelInfoResponse_TabType {
	if m != nil {
		return m.TabType
	}
	return MuseGetTopicChannelInfoResponse_TAB_TYPE_NORMAL_UNSPECIFIED
}

func (m *MuseGetTopicChannelInfoResponse) GetWelcomeText() string {
	if m != nil {
		return m.WelcomeText
	}
	return ""
}

func (m *MuseGetTopicChannelInfoResponse) GetThirdPartyGame() *MuseThirdPartyGame {
	if m != nil {
		return m.ThirdPartyGame
	}
	return nil
}

func (m *MuseGetTopicChannelInfoResponse) GetShiftRoomDuration() uint32 {
	if m != nil {
		return m.ShiftRoomDuration
	}
	return 0
}

func (m *MuseGetTopicChannelInfoResponse) GetFreezeDuration() uint32 {
	if m != nil {
		return m.FreezeDuration
	}
	return 0
}

func (m *MuseGetTopicChannelInfoResponse) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *MuseGetTopicChannelInfoResponse) GetTeamDesc() string {
	if m != nil {
		return m.TeamDesc
	}
	return ""
}

func (m *MuseGetTopicChannelInfoResponse) GetShowTeamDesc() bool {
	if m != nil {
		return m.ShowTeamDesc
	}
	return false
}

func (m *MuseGetTopicChannelInfoResponse) GetShowPublishButton() bool {
	if m != nil {
		return m.ShowPublishButton
	}
	return false
}

func (m *MuseGetTopicChannelInfoResponse) GetCategoryType() MuseCategoryType {
	if m != nil {
		return m.CategoryType
	}
	return MuseCategoryType_MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_UNSPECIFIED
}

type MuseSwitchPlayInfo struct {
	RoomModel            uint32   `protobuf:"varint,3,opt,name=room_model,json=roomModel,proto3" json:"room_model,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseSwitchPlayInfo) Reset()         { *m = MuseSwitchPlayInfo{} }
func (m *MuseSwitchPlayInfo) String() string { return proto.CompactTextString(m) }
func (*MuseSwitchPlayInfo) ProtoMessage()    {}
func (*MuseSwitchPlayInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{40}
}
func (m *MuseSwitchPlayInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSwitchPlayInfo.Unmarshal(m, b)
}
func (m *MuseSwitchPlayInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSwitchPlayInfo.Marshal(b, m, deterministic)
}
func (dst *MuseSwitchPlayInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSwitchPlayInfo.Merge(dst, src)
}
func (m *MuseSwitchPlayInfo) XXX_Size() int {
	return xxx_messageInfo_MuseSwitchPlayInfo.Size(m)
}
func (m *MuseSwitchPlayInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSwitchPlayInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSwitchPlayInfo proto.InternalMessageInfo

func (m *MuseSwitchPlayInfo) GetRoomModel() uint32 {
	if m != nil {
		return m.RoomModel
	}
	return 0
}

type MuseThirdPartyGame struct {
	LabelUrl             string                                 `protobuf:"bytes,1,opt,name=label_url,json=labelUrl,proto3" json:"label_url,omitempty"`
	PublicUrl            string                                 `protobuf:"bytes,2,opt,name=public_url,json=publicUrl,proto3" json:"public_url,omitempty"`
	GameBaseInfo         []*MuseThirdPartyGame_MuseGameBaseInfo `protobuf:"bytes,3,rep,name=game_base_info,json=gameBaseInfo,proto3" json:"game_base_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *MuseThirdPartyGame) Reset()         { *m = MuseThirdPartyGame{} }
func (m *MuseThirdPartyGame) String() string { return proto.CompactTextString(m) }
func (*MuseThirdPartyGame) ProtoMessage()    {}
func (*MuseThirdPartyGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{41}
}
func (m *MuseThirdPartyGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseThirdPartyGame.Unmarshal(m, b)
}
func (m *MuseThirdPartyGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseThirdPartyGame.Marshal(b, m, deterministic)
}
func (dst *MuseThirdPartyGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseThirdPartyGame.Merge(dst, src)
}
func (m *MuseThirdPartyGame) XXX_Size() int {
	return xxx_messageInfo_MuseThirdPartyGame.Size(m)
}
func (m *MuseThirdPartyGame) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseThirdPartyGame.DiscardUnknown(m)
}

var xxx_messageInfo_MuseThirdPartyGame proto.InternalMessageInfo

func (m *MuseThirdPartyGame) GetLabelUrl() string {
	if m != nil {
		return m.LabelUrl
	}
	return ""
}

func (m *MuseThirdPartyGame) GetPublicUrl() string {
	if m != nil {
		return m.PublicUrl
	}
	return ""
}

func (m *MuseThirdPartyGame) GetGameBaseInfo() []*MuseThirdPartyGame_MuseGameBaseInfo {
	if m != nil {
		return m.GameBaseInfo
	}
	return nil
}

type MuseThirdPartyGame_MuseGameBaseInfo struct {
	Platform             string   `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
	JumpUrl              string   `protobuf:"bytes,2,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	DownloadUrl          string   `protobuf:"bytes,3,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
	PackageName          string   `protobuf:"bytes,4,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseThirdPartyGame_MuseGameBaseInfo) Reset()         { *m = MuseThirdPartyGame_MuseGameBaseInfo{} }
func (m *MuseThirdPartyGame_MuseGameBaseInfo) String() string { return proto.CompactTextString(m) }
func (*MuseThirdPartyGame_MuseGameBaseInfo) ProtoMessage()    {}
func (*MuseThirdPartyGame_MuseGameBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{41, 0}
}
func (m *MuseThirdPartyGame_MuseGameBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseThirdPartyGame_MuseGameBaseInfo.Unmarshal(m, b)
}
func (m *MuseThirdPartyGame_MuseGameBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseThirdPartyGame_MuseGameBaseInfo.Marshal(b, m, deterministic)
}
func (dst *MuseThirdPartyGame_MuseGameBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseThirdPartyGame_MuseGameBaseInfo.Merge(dst, src)
}
func (m *MuseThirdPartyGame_MuseGameBaseInfo) XXX_Size() int {
	return xxx_messageInfo_MuseThirdPartyGame_MuseGameBaseInfo.Size(m)
}
func (m *MuseThirdPartyGame_MuseGameBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseThirdPartyGame_MuseGameBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MuseThirdPartyGame_MuseGameBaseInfo proto.InternalMessageInfo

func (m *MuseThirdPartyGame_MuseGameBaseInfo) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

func (m *MuseThirdPartyGame_MuseGameBaseInfo) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *MuseThirdPartyGame_MuseGameBaseInfo) GetDownloadUrl() string {
	if m != nil {
		return m.DownloadUrl
	}
	return ""
}

func (m *MuseThirdPartyGame_MuseGameBaseInfo) GetPackageName() string {
	if m != nil {
		return m.PackageName
	}
	return ""
}

// 主题房处罚推送
type TopicChannelUserWarnNotifyMsg struct {
	PunishType           uint32                `protobuf:"varint,1,opt,name=punish_type,json=punishType,proto3" json:"punish_type,omitempty"`
	ChannelId            uint32                `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UidList              []uint32              `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	InValue              []*im.RichTextElement `protobuf:"bytes,4,rep,name=in_value,json=inValue,proto3" json:"in_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *TopicChannelUserWarnNotifyMsg) Reset()         { *m = TopicChannelUserWarnNotifyMsg{} }
func (m *TopicChannelUserWarnNotifyMsg) String() string { return proto.CompactTextString(m) }
func (*TopicChannelUserWarnNotifyMsg) ProtoMessage()    {}
func (*TopicChannelUserWarnNotifyMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{42}
}
func (m *TopicChannelUserWarnNotifyMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicChannelUserWarnNotifyMsg.Unmarshal(m, b)
}
func (m *TopicChannelUserWarnNotifyMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicChannelUserWarnNotifyMsg.Marshal(b, m, deterministic)
}
func (dst *TopicChannelUserWarnNotifyMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicChannelUserWarnNotifyMsg.Merge(dst, src)
}
func (m *TopicChannelUserWarnNotifyMsg) XXX_Size() int {
	return xxx_messageInfo_TopicChannelUserWarnNotifyMsg.Size(m)
}
func (m *TopicChannelUserWarnNotifyMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicChannelUserWarnNotifyMsg.DiscardUnknown(m)
}

var xxx_messageInfo_TopicChannelUserWarnNotifyMsg proto.InternalMessageInfo

func (m *TopicChannelUserWarnNotifyMsg) GetPunishType() uint32 {
	if m != nil {
		return m.PunishType
	}
	return 0
}

func (m *TopicChannelUserWarnNotifyMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TopicChannelUserWarnNotifyMsg) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *TopicChannelUserWarnNotifyMsg) GetInValue() []*im.RichTextElement {
	if m != nil {
		return m.InValue
	}
	return nil
}

// 主题房处罚推送房间
type TopicChannelUserWarnNotifyInChannelMsg struct {
	ChannelId            uint32                `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	InValue              []*im.RichTextElement `protobuf:"bytes,4,rep,name=in_value,json=inValue,proto3" json:"in_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *TopicChannelUserWarnNotifyInChannelMsg) Reset() {
	*m = TopicChannelUserWarnNotifyInChannelMsg{}
}
func (m *TopicChannelUserWarnNotifyInChannelMsg) String() string { return proto.CompactTextString(m) }
func (*TopicChannelUserWarnNotifyInChannelMsg) ProtoMessage()    {}
func (*TopicChannelUserWarnNotifyInChannelMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{43}
}
func (m *TopicChannelUserWarnNotifyInChannelMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicChannelUserWarnNotifyInChannelMsg.Unmarshal(m, b)
}
func (m *TopicChannelUserWarnNotifyInChannelMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicChannelUserWarnNotifyInChannelMsg.Marshal(b, m, deterministic)
}
func (dst *TopicChannelUserWarnNotifyInChannelMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicChannelUserWarnNotifyInChannelMsg.Merge(dst, src)
}
func (m *TopicChannelUserWarnNotifyInChannelMsg) XXX_Size() int {
	return xxx_messageInfo_TopicChannelUserWarnNotifyInChannelMsg.Size(m)
}
func (m *TopicChannelUserWarnNotifyInChannelMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicChannelUserWarnNotifyInChannelMsg.DiscardUnknown(m)
}

var xxx_messageInfo_TopicChannelUserWarnNotifyInChannelMsg proto.InternalMessageInfo

func (m *TopicChannelUserWarnNotifyInChannelMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TopicChannelUserWarnNotifyInChannelMsg) GetInValue() []*im.RichTextElement {
	if m != nil {
		return m.InValue
	}
	return nil
}

type TopicChannelUserWarnLinkJumpURL struct {
	JumpUrlMap           map[string]string `protobuf:"bytes,1,rep,name=jump_url_map,json=jumpUrlMap,proto3" json:"jump_url_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // Deprecated: Do not use.
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *TopicChannelUserWarnLinkJumpURL) Reset()         { *m = TopicChannelUserWarnLinkJumpURL{} }
func (m *TopicChannelUserWarnLinkJumpURL) String() string { return proto.CompactTextString(m) }
func (*TopicChannelUserWarnLinkJumpURL) ProtoMessage()    {}
func (*TopicChannelUserWarnLinkJumpURL) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{44}
}
func (m *TopicChannelUserWarnLinkJumpURL) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicChannelUserWarnLinkJumpURL.Unmarshal(m, b)
}
func (m *TopicChannelUserWarnLinkJumpURL) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicChannelUserWarnLinkJumpURL.Marshal(b, m, deterministic)
}
func (dst *TopicChannelUserWarnLinkJumpURL) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicChannelUserWarnLinkJumpURL.Merge(dst, src)
}
func (m *TopicChannelUserWarnLinkJumpURL) XXX_Size() int {
	return xxx_messageInfo_TopicChannelUserWarnLinkJumpURL.Size(m)
}
func (m *TopicChannelUserWarnLinkJumpURL) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicChannelUserWarnLinkJumpURL.DiscardUnknown(m)
}

var xxx_messageInfo_TopicChannelUserWarnLinkJumpURL proto.InternalMessageInfo

// Deprecated: Do not use.
func (m *TopicChannelUserWarnLinkJumpURL) GetJumpUrlMap() map[string]string {
	if m != nil {
		return m.JumpUrlMap
	}
	return nil
}

type GetAssociateRevChannelsRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	NextToken            string       `protobuf:"bytes,2,opt,name=next_token,json=nextToken,proto3" json:"next_token,omitempty"`
	Limit                uint32       `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32       `protobuf:"varint,5,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAssociateRevChannelsRequest) Reset()         { *m = GetAssociateRevChannelsRequest{} }
func (m *GetAssociateRevChannelsRequest) String() string { return proto.CompactTextString(m) }
func (*GetAssociateRevChannelsRequest) ProtoMessage()    {}
func (*GetAssociateRevChannelsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{45}
}
func (m *GetAssociateRevChannelsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAssociateRevChannelsRequest.Unmarshal(m, b)
}
func (m *GetAssociateRevChannelsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAssociateRevChannelsRequest.Marshal(b, m, deterministic)
}
func (dst *GetAssociateRevChannelsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAssociateRevChannelsRequest.Merge(dst, src)
}
func (m *GetAssociateRevChannelsRequest) XXX_Size() int {
	return xxx_messageInfo_GetAssociateRevChannelsRequest.Size(m)
}
func (m *GetAssociateRevChannelsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAssociateRevChannelsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAssociateRevChannelsRequest proto.InternalMessageInfo

func (m *GetAssociateRevChannelsRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetAssociateRevChannelsRequest) GetNextToken() string {
	if m != nil {
		return m.NextToken
	}
	return ""
}

func (m *GetAssociateRevChannelsRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetAssociateRevChannelsRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetAssociateRevChannelsRequest) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetAssociateRevChannelsResponse struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	NextToken            string            `protobuf:"bytes,2,opt,name=next_token,json=nextToken,proto3" json:"next_token,omitempty"`
	Title                string            `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Channels             []*MuseRevChannel `protobuf:"bytes,4,rep,name=channels,proto3" json:"channels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAssociateRevChannelsResponse) Reset()         { *m = GetAssociateRevChannelsResponse{} }
func (m *GetAssociateRevChannelsResponse) String() string { return proto.CompactTextString(m) }
func (*GetAssociateRevChannelsResponse) ProtoMessage()    {}
func (*GetAssociateRevChannelsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{46}
}
func (m *GetAssociateRevChannelsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAssociateRevChannelsResponse.Unmarshal(m, b)
}
func (m *GetAssociateRevChannelsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAssociateRevChannelsResponse.Marshal(b, m, deterministic)
}
func (dst *GetAssociateRevChannelsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAssociateRevChannelsResponse.Merge(dst, src)
}
func (m *GetAssociateRevChannelsResponse) XXX_Size() int {
	return xxx_messageInfo_GetAssociateRevChannelsResponse.Size(m)
}
func (m *GetAssociateRevChannelsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAssociateRevChannelsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAssociateRevChannelsResponse proto.InternalMessageInfo

func (m *GetAssociateRevChannelsResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAssociateRevChannelsResponse) GetNextToken() string {
	if m != nil {
		return m.NextToken
	}
	return ""
}

func (m *GetAssociateRevChannelsResponse) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetAssociateRevChannelsResponse) GetChannels() []*MuseRevChannel {
	if m != nil {
		return m.Channels
	}
	return nil
}

type MuseRevChannel struct {
	ChannelId            uint32             `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SimpleDesc           string             `protobuf:"bytes,2,opt,name=simple_desc,json=simpleDesc,proto3" json:"simple_desc,omitempty"`
	ChannelName          string             `protobuf:"bytes,3,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelOwnerAccount  string             `protobuf:"bytes,4,opt,name=channel_owner_account,json=channelOwnerAccount,proto3" json:"channel_owner_account,omitempty"`
	ChannelOwnerSex      int32              `protobuf:"varint,5,opt,name=channel_owner_sex,json=channelOwnerSex,proto3" json:"channel_owner_sex,omitempty"`
	ChannelOwnerName     string             `protobuf:"bytes,6,opt,name=channel_owner_name,json=channelOwnerName,proto3" json:"channel_owner_name,omitempty"`
	ChannelHot           *MuseRevChannel    `protobuf:"bytes,7,opt,name=channel_hot,json=channelHot,proto3" json:"channel_hot,omitempty"` // Deprecated: Do not use.
	ChannelIconMd5       string             `protobuf:"bytes,8,opt,name=channel_icon_md5,json=channelIconMd5,proto3" json:"channel_icon_md5,omitempty"`
	RevChannelHot        *MuseRevChannelHot `protobuf:"bytes,9,opt,name=rev_channel_hot,json=revChannelHot,proto3" json:"rev_channel_hot,omitempty"`
	ChannelType          uint32             `protobuf:"varint,10,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MuseRevChannel) Reset()         { *m = MuseRevChannel{} }
func (m *MuseRevChannel) String() string { return proto.CompactTextString(m) }
func (*MuseRevChannel) ProtoMessage()    {}
func (*MuseRevChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{47}
}
func (m *MuseRevChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseRevChannel.Unmarshal(m, b)
}
func (m *MuseRevChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseRevChannel.Marshal(b, m, deterministic)
}
func (dst *MuseRevChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseRevChannel.Merge(dst, src)
}
func (m *MuseRevChannel) XXX_Size() int {
	return xxx_messageInfo_MuseRevChannel.Size(m)
}
func (m *MuseRevChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseRevChannel.DiscardUnknown(m)
}

var xxx_messageInfo_MuseRevChannel proto.InternalMessageInfo

func (m *MuseRevChannel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseRevChannel) GetSimpleDesc() string {
	if m != nil {
		return m.SimpleDesc
	}
	return ""
}

func (m *MuseRevChannel) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *MuseRevChannel) GetChannelOwnerAccount() string {
	if m != nil {
		return m.ChannelOwnerAccount
	}
	return ""
}

func (m *MuseRevChannel) GetChannelOwnerSex() int32 {
	if m != nil {
		return m.ChannelOwnerSex
	}
	return 0
}

func (m *MuseRevChannel) GetChannelOwnerName() string {
	if m != nil {
		return m.ChannelOwnerName
	}
	return ""
}

// Deprecated: Do not use.
func (m *MuseRevChannel) GetChannelHot() *MuseRevChannel {
	if m != nil {
		return m.ChannelHot
	}
	return nil
}

func (m *MuseRevChannel) GetChannelIconMd5() string {
	if m != nil {
		return m.ChannelIconMd5
	}
	return ""
}

func (m *MuseRevChannel) GetRevChannelHot() *MuseRevChannelHot {
	if m != nil {
		return m.RevChannelHot
	}
	return nil
}

func (m *MuseRevChannel) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type MuseRevChannelHot struct {
	Icon                 string   `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	Count                int64    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseRevChannelHot) Reset()         { *m = MuseRevChannelHot{} }
func (m *MuseRevChannelHot) String() string { return proto.CompactTextString(m) }
func (*MuseRevChannelHot) ProtoMessage()    {}
func (*MuseRevChannelHot) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{48}
}
func (m *MuseRevChannelHot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseRevChannelHot.Unmarshal(m, b)
}
func (m *MuseRevChannelHot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseRevChannelHot.Marshal(b, m, deterministic)
}
func (dst *MuseRevChannelHot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseRevChannelHot.Merge(dst, src)
}
func (m *MuseRevChannelHot) XXX_Size() int {
	return xxx_messageInfo_MuseRevChannelHot.Size(m)
}
func (m *MuseRevChannelHot) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseRevChannelHot.DiscardUnknown(m)
}

var xxx_messageInfo_MuseRevChannelHot proto.InternalMessageInfo

func (m *MuseRevChannelHot) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *MuseRevChannelHot) GetCount() int64 {
	if m != nil {
		return m.Count
	}
	return 0
}

type ListMuseSocialCommunityChannelsRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListMuseSocialCommunityChannelsRequest) Reset() {
	*m = ListMuseSocialCommunityChannelsRequest{}
}
func (m *ListMuseSocialCommunityChannelsRequest) String() string { return proto.CompactTextString(m) }
func (*ListMuseSocialCommunityChannelsRequest) ProtoMessage()    {}
func (*ListMuseSocialCommunityChannelsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{49}
}
func (m *ListMuseSocialCommunityChannelsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMuseSocialCommunityChannelsRequest.Unmarshal(m, b)
}
func (m *ListMuseSocialCommunityChannelsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMuseSocialCommunityChannelsRequest.Marshal(b, m, deterministic)
}
func (dst *ListMuseSocialCommunityChannelsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMuseSocialCommunityChannelsRequest.Merge(dst, src)
}
func (m *ListMuseSocialCommunityChannelsRequest) XXX_Size() int {
	return xxx_messageInfo_ListMuseSocialCommunityChannelsRequest.Size(m)
}
func (m *ListMuseSocialCommunityChannelsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMuseSocialCommunityChannelsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListMuseSocialCommunityChannelsRequest proto.InternalMessageInfo

func (m *ListMuseSocialCommunityChannelsRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListMuseSocialCommunityChannelsRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type ListMuseSocialCommunityChannelsResponse struct {
	BaseResp             *app.BaseResp                     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Channels             []*MuseSocialCommunityChannelInfo `protobuf:"bytes,2,rep,name=channels,proto3" json:"channels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *ListMuseSocialCommunityChannelsResponse) Reset() {
	*m = ListMuseSocialCommunityChannelsResponse{}
}
func (m *ListMuseSocialCommunityChannelsResponse) String() string { return proto.CompactTextString(m) }
func (*ListMuseSocialCommunityChannelsResponse) ProtoMessage()    {}
func (*ListMuseSocialCommunityChannelsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{50}
}
func (m *ListMuseSocialCommunityChannelsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMuseSocialCommunityChannelsResponse.Unmarshal(m, b)
}
func (m *ListMuseSocialCommunityChannelsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMuseSocialCommunityChannelsResponse.Marshal(b, m, deterministic)
}
func (dst *ListMuseSocialCommunityChannelsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMuseSocialCommunityChannelsResponse.Merge(dst, src)
}
func (m *ListMuseSocialCommunityChannelsResponse) XXX_Size() int {
	return xxx_messageInfo_ListMuseSocialCommunityChannelsResponse.Size(m)
}
func (m *ListMuseSocialCommunityChannelsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMuseSocialCommunityChannelsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListMuseSocialCommunityChannelsResponse proto.InternalMessageInfo

func (m *ListMuseSocialCommunityChannelsResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListMuseSocialCommunityChannelsResponse) GetChannels() []*MuseSocialCommunityChannelInfo {
	if m != nil {
		return m.Channels
	}
	return nil
}

type MuseSocialCommunityChannelInfo struct {
	ChannelId          uint32                    `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName        string                    `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelMemberCount uint32                    `protobuf:"varint,3,opt,name=channel_member_count,json=channelMemberCount,proto3" json:"channel_member_count,omitempty"`
	TabIcon            string                    `protobuf:"bytes,4,opt,name=tab_icon,json=tabIcon,proto3" json:"tab_icon,omitempty"`
	TabDesc            string                    `protobuf:"bytes,5,opt,name=tab_desc,json=tabDesc,proto3" json:"tab_desc,omitempty"`
	OwnerAccount       string                    `protobuf:"bytes,6,opt,name=owner_account,json=ownerAccount,proto3" json:"owner_account,omitempty"`
	OwnerSex           int32                     `protobuf:"varint,7,opt,name=owner_sex,json=ownerSex,proto3" json:"owner_sex,omitempty"`
	Accounts           []string                  `protobuf:"bytes,8,rep,name=accounts,proto3" json:"accounts,omitempty"`
	Status             string                    `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`
	Song               string                    `protobuf:"bytes,10,opt,name=song,proto3" json:"song,omitempty"`
	Label              uint32                    `protobuf:"varint,12,opt,name=label,proto3" json:"label,omitempty"`
	Glory              *KtvGlory                 `protobuf:"bytes,13,opt,name=glory,proto3" json:"glory,omitempty"`
	PersonalCert       *MusicChannelPersonalCert `protobuf:"bytes,14,opt,name=personal_cert,json=personalCert,proto3" json:"personal_cert,omitempty"`
	TabId              uint32                    `protobuf:"varint,16,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	RegionId           uint64                    `protobuf:"varint,17,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	ChannelLevelId     uint32                    `protobuf:"varint,18,opt,name=channel_level_id,json=channelLevelId,proto3" json:"channel_level_id,omitempty"`
	// 非业务必须字段，埋点需要
	TabName              string                           `protobuf:"bytes,19,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Pia                  *MusicPia                        `protobuf:"bytes,20,opt,name=pia,proto3" json:"pia,omitempty"`
	Interesting          *MusicInteresting                `protobuf:"bytes,21,opt,name=interesting,proto3" json:"interesting,omitempty"`
	Logo                 string                           `protobuf:"bytes,22,opt,name=logo,proto3" json:"logo,omitempty"`
	MuseLabel            *MuseSocialCommunityChannelLabel `protobuf:"bytes,23,opt,name=muse_label,json=museLabel,proto3" json:"muse_label,omitempty"`
	ChannelType          uint32                           `protobuf:"varint,25,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	Topic                string                           `protobuf:"bytes,26,opt,name=topic,proto3" json:"topic,omitempty"`
	TopicIcon            string                           `protobuf:"bytes,27,opt,name=topic_icon,json=topicIcon,proto3" json:"topic_icon,omitempty"`
	TopicType            int32                            `protobuf:"varint,28,opt,name=topic_type,json=topicType,proto3" json:"topic_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *MuseSocialCommunityChannelInfo) Reset()         { *m = MuseSocialCommunityChannelInfo{} }
func (m *MuseSocialCommunityChannelInfo) String() string { return proto.CompactTextString(m) }
func (*MuseSocialCommunityChannelInfo) ProtoMessage()    {}
func (*MuseSocialCommunityChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{51}
}
func (m *MuseSocialCommunityChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialCommunityChannelInfo.Unmarshal(m, b)
}
func (m *MuseSocialCommunityChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialCommunityChannelInfo.Marshal(b, m, deterministic)
}
func (dst *MuseSocialCommunityChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialCommunityChannelInfo.Merge(dst, src)
}
func (m *MuseSocialCommunityChannelInfo) XXX_Size() int {
	return xxx_messageInfo_MuseSocialCommunityChannelInfo.Size(m)
}
func (m *MuseSocialCommunityChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialCommunityChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialCommunityChannelInfo proto.InternalMessageInfo

func (m *MuseSocialCommunityChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseSocialCommunityChannelInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *MuseSocialCommunityChannelInfo) GetChannelMemberCount() uint32 {
	if m != nil {
		return m.ChannelMemberCount
	}
	return 0
}

func (m *MuseSocialCommunityChannelInfo) GetTabIcon() string {
	if m != nil {
		return m.TabIcon
	}
	return ""
}

func (m *MuseSocialCommunityChannelInfo) GetTabDesc() string {
	if m != nil {
		return m.TabDesc
	}
	return ""
}

func (m *MuseSocialCommunityChannelInfo) GetOwnerAccount() string {
	if m != nil {
		return m.OwnerAccount
	}
	return ""
}

func (m *MuseSocialCommunityChannelInfo) GetOwnerSex() int32 {
	if m != nil {
		return m.OwnerSex
	}
	return 0
}

func (m *MuseSocialCommunityChannelInfo) GetAccounts() []string {
	if m != nil {
		return m.Accounts
	}
	return nil
}

func (m *MuseSocialCommunityChannelInfo) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *MuseSocialCommunityChannelInfo) GetSong() string {
	if m != nil {
		return m.Song
	}
	return ""
}

func (m *MuseSocialCommunityChannelInfo) GetLabel() uint32 {
	if m != nil {
		return m.Label
	}
	return 0
}

func (m *MuseSocialCommunityChannelInfo) GetGlory() *KtvGlory {
	if m != nil {
		return m.Glory
	}
	return nil
}

func (m *MuseSocialCommunityChannelInfo) GetPersonalCert() *MusicChannelPersonalCert {
	if m != nil {
		return m.PersonalCert
	}
	return nil
}

func (m *MuseSocialCommunityChannelInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *MuseSocialCommunityChannelInfo) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *MuseSocialCommunityChannelInfo) GetChannelLevelId() uint32 {
	if m != nil {
		return m.ChannelLevelId
	}
	return 0
}

func (m *MuseSocialCommunityChannelInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *MuseSocialCommunityChannelInfo) GetPia() *MusicPia {
	if m != nil {
		return m.Pia
	}
	return nil
}

func (m *MuseSocialCommunityChannelInfo) GetInteresting() *MusicInteresting {
	if m != nil {
		return m.Interesting
	}
	return nil
}

func (m *MuseSocialCommunityChannelInfo) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *MuseSocialCommunityChannelInfo) GetMuseLabel() *MuseSocialCommunityChannelLabel {
	if m != nil {
		return m.MuseLabel
	}
	return nil
}

func (m *MuseSocialCommunityChannelInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *MuseSocialCommunityChannelInfo) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *MuseSocialCommunityChannelInfo) GetTopicIcon() string {
	if m != nil {
		return m.TopicIcon
	}
	return ""
}

func (m *MuseSocialCommunityChannelInfo) GetTopicType() int32 {
	if m != nil {
		return m.TopicType
	}
	return 0
}

type MuseSocialCommunityChannelLabel struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseSocialCommunityChannelLabel) Reset()         { *m = MuseSocialCommunityChannelLabel{} }
func (m *MuseSocialCommunityChannelLabel) String() string { return proto.CompactTextString(m) }
func (*MuseSocialCommunityChannelLabel) ProtoMessage()    {}
func (*MuseSocialCommunityChannelLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{52}
}
func (m *MuseSocialCommunityChannelLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialCommunityChannelLabel.Unmarshal(m, b)
}
func (m *MuseSocialCommunityChannelLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialCommunityChannelLabel.Marshal(b, m, deterministic)
}
func (dst *MuseSocialCommunityChannelLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialCommunityChannelLabel.Merge(dst, src)
}
func (m *MuseSocialCommunityChannelLabel) XXX_Size() int {
	return xxx_messageInfo_MuseSocialCommunityChannelLabel.Size(m)
}
func (m *MuseSocialCommunityChannelLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialCommunityChannelLabel.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialCommunityChannelLabel proto.InternalMessageInfo

func (m *MuseSocialCommunityChannelLabel) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MuseSocialCommunityChannelLabel) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

// 非上麦好友邀请进房 push
type MTInviteUserEnterChannelWelcomePush struct {
	Text                 string   `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MTInviteUserEnterChannelWelcomePush) Reset()         { *m = MTInviteUserEnterChannelWelcomePush{} }
func (m *MTInviteUserEnterChannelWelcomePush) String() string { return proto.CompactTextString(m) }
func (*MTInviteUserEnterChannelWelcomePush) ProtoMessage()    {}
func (*MTInviteUserEnterChannelWelcomePush) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{53}
}
func (m *MTInviteUserEnterChannelWelcomePush) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MTInviteUserEnterChannelWelcomePush.Unmarshal(m, b)
}
func (m *MTInviteUserEnterChannelWelcomePush) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MTInviteUserEnterChannelWelcomePush.Marshal(b, m, deterministic)
}
func (dst *MTInviteUserEnterChannelWelcomePush) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MTInviteUserEnterChannelWelcomePush.Merge(dst, src)
}
func (m *MTInviteUserEnterChannelWelcomePush) XXX_Size() int {
	return xxx_messageInfo_MTInviteUserEnterChannelWelcomePush.Size(m)
}
func (m *MTInviteUserEnterChannelWelcomePush) XXX_DiscardUnknown() {
	xxx_messageInfo_MTInviteUserEnterChannelWelcomePush.DiscardUnknown(m)
}

var xxx_messageInfo_MTInviteUserEnterChannelWelcomePush proto.InternalMessageInfo

func (m *MTInviteUserEnterChannelWelcomePush) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type FollowUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	ChannelRole          uint32   `protobuf:"varint,3,opt,name=channel_role,json=channelRole,proto3" json:"channel_role,omitempty"`
	Account              string   `protobuf:"bytes,4,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FollowUserInfo) Reset()         { *m = FollowUserInfo{} }
func (m *FollowUserInfo) String() string { return proto.CompactTextString(m) }
func (*FollowUserInfo) ProtoMessage()    {}
func (*FollowUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{54}
}
func (m *FollowUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FollowUserInfo.Unmarshal(m, b)
}
func (m *FollowUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FollowUserInfo.Marshal(b, m, deterministic)
}
func (dst *FollowUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FollowUserInfo.Merge(dst, src)
}
func (m *FollowUserInfo) XXX_Size() int {
	return xxx_messageInfo_FollowUserInfo.Size(m)
}
func (m *FollowUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FollowUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FollowUserInfo proto.InternalMessageInfo

func (m *FollowUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FollowUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *FollowUserInfo) GetChannelRole() uint32 {
	if m != nil {
		return m.ChannelRole
	}
	return 0
}

func (m *FollowUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type UserFollowInChannel struct {
	Scene                uint32            `protobuf:"varint,1,opt,name=scene,proto3" json:"scene,omitempty"`
	FollowUsers          []*FollowUserInfo `protobuf:"bytes,2,rep,name=follow_users,json=followUsers,proto3" json:"follow_users,omitempty"`
	FollowedUser         []*FollowUserInfo `protobuf:"bytes,3,rep,name=followed_user,json=followedUser,proto3" json:"followed_user,omitempty"`
	OriginSource         uint32            `protobuf:"varint,4,opt,name=origin_source,json=originSource,proto3" json:"origin_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserFollowInChannel) Reset()         { *m = UserFollowInChannel{} }
func (m *UserFollowInChannel) String() string { return proto.CompactTextString(m) }
func (*UserFollowInChannel) ProtoMessage()    {}
func (*UserFollowInChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{55}
}
func (m *UserFollowInChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserFollowInChannel.Unmarshal(m, b)
}
func (m *UserFollowInChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserFollowInChannel.Marshal(b, m, deterministic)
}
func (dst *UserFollowInChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserFollowInChannel.Merge(dst, src)
}
func (m *UserFollowInChannel) XXX_Size() int {
	return xxx_messageInfo_UserFollowInChannel.Size(m)
}
func (m *UserFollowInChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_UserFollowInChannel.DiscardUnknown(m)
}

var xxx_messageInfo_UserFollowInChannel proto.InternalMessageInfo

func (m *UserFollowInChannel) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *UserFollowInChannel) GetFollowUsers() []*FollowUserInfo {
	if m != nil {
		return m.FollowUsers
	}
	return nil
}

func (m *UserFollowInChannel) GetFollowedUser() []*FollowUserInfo {
	if m != nil {
		return m.FollowedUser
	}
	return nil
}

func (m *UserFollowInChannel) GetOriginSource() uint32 {
	if m != nil {
		return m.OriginSource
	}
	return 0
}

type PreferenceKeywords struct {
	PreferenceKeywords   []*PreferenceKeyword `protobuf:"bytes,1,rep,name=preference_keywords,json=preferenceKeywords,proto3" json:"preference_keywords,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *PreferenceKeywords) Reset()         { *m = PreferenceKeywords{} }
func (m *PreferenceKeywords) String() string { return proto.CompactTextString(m) }
func (*PreferenceKeywords) ProtoMessage()    {}
func (*PreferenceKeywords) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{56}
}
func (m *PreferenceKeywords) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreferenceKeywords.Unmarshal(m, b)
}
func (m *PreferenceKeywords) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreferenceKeywords.Marshal(b, m, deterministic)
}
func (dst *PreferenceKeywords) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreferenceKeywords.Merge(dst, src)
}
func (m *PreferenceKeywords) XXX_Size() int {
	return xxx_messageInfo_PreferenceKeywords.Size(m)
}
func (m *PreferenceKeywords) XXX_DiscardUnknown() {
	xxx_messageInfo_PreferenceKeywords.DiscardUnknown(m)
}

var xxx_messageInfo_PreferenceKeywords proto.InternalMessageInfo

func (m *PreferenceKeywords) GetPreferenceKeywords() []*PreferenceKeyword {
	if m != nil {
		return m.PreferenceKeywords
	}
	return nil
}

type PreferenceKeyword struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Keyword              string   `protobuf:"bytes,2,opt,name=keyword,proto3" json:"keyword,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreferenceKeyword) Reset()         { *m = PreferenceKeyword{} }
func (m *PreferenceKeyword) String() string { return proto.CompactTextString(m) }
func (*PreferenceKeyword) ProtoMessage()    {}
func (*PreferenceKeyword) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{57}
}
func (m *PreferenceKeyword) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreferenceKeyword.Unmarshal(m, b)
}
func (m *PreferenceKeyword) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreferenceKeyword.Marshal(b, m, deterministic)
}
func (dst *PreferenceKeyword) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreferenceKeyword.Merge(dst, src)
}
func (m *PreferenceKeyword) XXX_Size() int {
	return xxx_messageInfo_PreferenceKeyword.Size(m)
}
func (m *PreferenceKeyword) XXX_DiscardUnknown() {
	xxx_messageInfo_PreferenceKeyword.DiscardUnknown(m)
}

var xxx_messageInfo_PreferenceKeyword proto.InternalMessageInfo

func (m *PreferenceKeyword) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *PreferenceKeyword) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

type ListChannelPreferenceKeywordsRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                int32        `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListChannelPreferenceKeywordsRequest) Reset()         { *m = ListChannelPreferenceKeywordsRequest{} }
func (m *ListChannelPreferenceKeywordsRequest) String() string { return proto.CompactTextString(m) }
func (*ListChannelPreferenceKeywordsRequest) ProtoMessage()    {}
func (*ListChannelPreferenceKeywordsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{58}
}
func (m *ListChannelPreferenceKeywordsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListChannelPreferenceKeywordsRequest.Unmarshal(m, b)
}
func (m *ListChannelPreferenceKeywordsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListChannelPreferenceKeywordsRequest.Marshal(b, m, deterministic)
}
func (dst *ListChannelPreferenceKeywordsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListChannelPreferenceKeywordsRequest.Merge(dst, src)
}
func (m *ListChannelPreferenceKeywordsRequest) XXX_Size() int {
	return xxx_messageInfo_ListChannelPreferenceKeywordsRequest.Size(m)
}
func (m *ListChannelPreferenceKeywordsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListChannelPreferenceKeywordsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListChannelPreferenceKeywordsRequest proto.InternalMessageInfo

func (m *ListChannelPreferenceKeywordsRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListChannelPreferenceKeywordsRequest) GetTabId() int32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type ListChannelPreferenceKeywordsResponse struct {
	BaseResp             *app.BaseResp         `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PreferenceKeywords   []*PreferenceKeywords `protobuf:"bytes,2,rep,name=preference_keywords,json=preferenceKeywords,proto3" json:"preference_keywords,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ListChannelPreferenceKeywordsResponse) Reset()         { *m = ListChannelPreferenceKeywordsResponse{} }
func (m *ListChannelPreferenceKeywordsResponse) String() string { return proto.CompactTextString(m) }
func (*ListChannelPreferenceKeywordsResponse) ProtoMessage()    {}
func (*ListChannelPreferenceKeywordsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_topic_channel_logic__1107b2512245c0b7, []int{59}
}
func (m *ListChannelPreferenceKeywordsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListChannelPreferenceKeywordsResponse.Unmarshal(m, b)
}
func (m *ListChannelPreferenceKeywordsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListChannelPreferenceKeywordsResponse.Marshal(b, m, deterministic)
}
func (dst *ListChannelPreferenceKeywordsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListChannelPreferenceKeywordsResponse.Merge(dst, src)
}
func (m *ListChannelPreferenceKeywordsResponse) XXX_Size() int {
	return xxx_messageInfo_ListChannelPreferenceKeywordsResponse.Size(m)
}
func (m *ListChannelPreferenceKeywordsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListChannelPreferenceKeywordsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListChannelPreferenceKeywordsResponse proto.InternalMessageInfo

func (m *ListChannelPreferenceKeywordsResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListChannelPreferenceKeywordsResponse) GetPreferenceKeywords() []*PreferenceKeywords {
	if m != nil {
		return m.PreferenceKeywords
	}
	return nil
}

func init() {
	proto.RegisterType((*GetMusicChannelFilterV2Req)(nil), "ga.music_topic_channel.GetMusicChannelFilterV2Req")
	proto.RegisterType((*GetMusicChannelFilterV2Resp)(nil), "ga.music_topic_channel.GetMusicChannelFilterV2Resp")
	proto.RegisterType((*GetMusicChannelFilterV2Resp_FilterItem)(nil), "ga.music_topic_channel.GetMusicChannelFilterV2Resp.FilterItem")
	proto.RegisterType((*GetMusicChannelFilterV2Resp_FilterSubItem)(nil), "ga.music_topic_channel.GetMusicChannelFilterV2Resp.FilterSubItem")
	proto.RegisterType((*SameCityTitle)(nil), "ga.music_topic_channel.SameCityTitle")
	proto.RegisterType((*ListHobbyChannelV2Req)(nil), "ga.music_topic_channel.ListHobbyChannelV2Req")
	proto.RegisterType((*GetMusicHomePageViewV2Req)(nil), "ga.music_topic_channel.GetMusicHomePageViewV2Req")
	proto.RegisterType((*GetMusicHomePageViewV2Resp)(nil), "ga.music_topic_channel.GetMusicHomePageViewV2Resp")
	proto.RegisterType((*GetMusicHomePageViewV2Resp_MusicHomePageV2View)(nil), "ga.music_topic_channel.GetMusicHomePageViewV2Resp.MusicHomePageV2View")
	proto.RegisterType((*GetMusicHomePageDialogV2Req)(nil), "ga.music_topic_channel.GetMusicHomePageDialogV2Req")
	proto.RegisterType((*GetMusicHomePageDialogV2Resp)(nil), "ga.music_topic_channel.GetMusicHomePageDialogV2Resp")
	proto.RegisterType((*GetMusicHomePageDialogV2Resp_DialogView)(nil), "ga.music_topic_channel.GetMusicHomePageDialogV2Resp.DialogView")
	proto.RegisterType((*QuickMatchHobbyChannelV2Req)(nil), "ga.music_topic_channel.QuickMatchHobbyChannelV2Req")
	proto.RegisterType((*QuickMatchHobbyChannelV2Resp)(nil), "ga.music_topic_channel.QuickMatchHobbyChannelV2Resp")
	proto.RegisterType((*ReunionInteractionPush)(nil), "ga.music_topic_channel.ReunionInteractionPush")
	proto.RegisterType((*ReunionUser)(nil), "ga.music_topic_channel.ReunionUser")
	proto.RegisterType((*PublishMusicChannelReq)(nil), "ga.music_topic_channel.PublishMusicChannelReq")
	proto.RegisterType((*PublishMusicChannelResp)(nil), "ga.music_topic_channel.PublishMusicChannelResp")
	proto.RegisterType((*CancelMusicChannelPublishReq)(nil), "ga.music_topic_channel.CancelMusicChannelPublishReq")
	proto.RegisterType((*CancelMusicChannelPublishResp)(nil), "ga.music_topic_channel.CancelMusicChannelPublishResp")
	proto.RegisterType((*GetMusicFilterItemByIdsReq)(nil), "ga.music_topic_channel.GetMusicFilterItemByIdsReq")
	proto.RegisterType((*GetMusicFilterItemByIdsResp)(nil), "ga.music_topic_channel.GetMusicFilterItemByIdsResp")
	proto.RegisterMapType((map[string]*MusicFilterItem)(nil), "ga.music_topic_channel.GetMusicFilterItemByIdsResp.FilterMapEntry")
	proto.RegisterType((*MusicFilterItem)(nil), "ga.music_topic_channel.MusicFilterItem")
	proto.RegisterType((*ListMusicChannelsReq)(nil), "ga.music_topic_channel.ListMusicChannelsReq")
	proto.RegisterType((*ClassifyLabellist)(nil), "ga.music_topic_channel.ClassifyLabellist")
	proto.RegisterType((*NotExposeChannelList)(nil), "ga.music_topic_channel.NotExposeChannelList")
	proto.RegisterType((*ListMusicChannelsResp)(nil), "ga.music_topic_channel.ListMusicChannelsResp")
	proto.RegisterType((*MusicChannel)(nil), "ga.music_topic_channel.MusicChannel")
	proto.RegisterType((*MuseShiningPoint)(nil), "ga.music_topic_channel.MuseShiningPoint")
	proto.RegisterType((*MusicSocialRankHonorSignInfo)(nil), "ga.music_topic_channel.MusicSocialRankHonorSignInfo")
	proto.RegisterType((*MusicSocial)(nil), "ga.music_topic_channel.MusicSocial")
	proto.RegisterType((*MusicPia)(nil), "ga.music_topic_channel.MusicPia")
	proto.RegisterType((*MusicInteresting)(nil), "ga.music_topic_channel.MusicInteresting")
	proto.RegisterType((*MusicChannelReview)(nil), "ga.music_topic_channel.MusicChannelReview")
	proto.RegisterType((*KtvGlory)(nil), "ga.music_topic_channel.KtvGlory")
	proto.RegisterType((*MusicChannelPersonalCert)(nil), "ga.music_topic_channel.MusicChannelPersonalCert")
	proto.RegisterType((*GetTabPublishHotRcmdReq)(nil), "ga.music_topic_channel.GetTabPublishHotRcmdReq")
	proto.RegisterType((*GetTabPublishHotRcmdResp)(nil), "ga.music_topic_channel.GetTabPublishHotRcmdResp")
	proto.RegisterType((*TabPublishHotRcmd)(nil), "ga.music_topic_channel.TabPublishHotRcmd")
	proto.RegisterType((*MusicBlock)(nil), "ga.music_topic_channel.MusicBlock")
	proto.RegisterType((*GetResourceConfigByChannelIdReq)(nil), "ga.music_topic_channel.GetResourceConfigByChannelIdReq")
	proto.RegisterType((*GetResourceConfigByChannelIdResp)(nil), "ga.music_topic_channel.GetResourceConfigByChannelIdResp")
	proto.RegisterType((*MuseGetTopicChannelInfoRequest)(nil), "ga.music_topic_channel.MuseGetTopicChannelInfoRequest")
	proto.RegisterType((*MuseGetTopicChannelInfoResponse)(nil), "ga.music_topic_channel.MuseGetTopicChannelInfoResponse")
	proto.RegisterType((*MuseSwitchPlayInfo)(nil), "ga.music_topic_channel.MuseSwitchPlayInfo")
	proto.RegisterType((*MuseThirdPartyGame)(nil), "ga.music_topic_channel.MuseThirdPartyGame")
	proto.RegisterType((*MuseThirdPartyGame_MuseGameBaseInfo)(nil), "ga.music_topic_channel.MuseThirdPartyGame.MuseGameBaseInfo")
	proto.RegisterType((*TopicChannelUserWarnNotifyMsg)(nil), "ga.music_topic_channel.TopicChannelUserWarnNotifyMsg")
	proto.RegisterType((*TopicChannelUserWarnNotifyInChannelMsg)(nil), "ga.music_topic_channel.TopicChannelUserWarnNotifyInChannelMsg")
	proto.RegisterType((*TopicChannelUserWarnLinkJumpURL)(nil), "ga.music_topic_channel.TopicChannelUserWarnLinkJumpURL")
	proto.RegisterMapType((map[string]string)(nil), "ga.music_topic_channel.TopicChannelUserWarnLinkJumpURL.JumpUrlMapEntry")
	proto.RegisterType((*GetAssociateRevChannelsRequest)(nil), "ga.music_topic_channel.GetAssociateRevChannelsRequest")
	proto.RegisterType((*GetAssociateRevChannelsResponse)(nil), "ga.music_topic_channel.GetAssociateRevChannelsResponse")
	proto.RegisterType((*MuseRevChannel)(nil), "ga.music_topic_channel.MuseRevChannel")
	proto.RegisterType((*MuseRevChannelHot)(nil), "ga.music_topic_channel.MuseRevChannelHot")
	proto.RegisterType((*ListMuseSocialCommunityChannelsRequest)(nil), "ga.music_topic_channel.ListMuseSocialCommunityChannelsRequest")
	proto.RegisterType((*ListMuseSocialCommunityChannelsResponse)(nil), "ga.music_topic_channel.ListMuseSocialCommunityChannelsResponse")
	proto.RegisterType((*MuseSocialCommunityChannelInfo)(nil), "ga.music_topic_channel.MuseSocialCommunityChannelInfo")
	proto.RegisterType((*MuseSocialCommunityChannelLabel)(nil), "ga.music_topic_channel.MuseSocialCommunityChannelLabel")
	proto.RegisterType((*MTInviteUserEnterChannelWelcomePush)(nil), "ga.music_topic_channel.MTInviteUserEnterChannelWelcomePush")
	proto.RegisterType((*FollowUserInfo)(nil), "ga.music_topic_channel.FollowUserInfo")
	proto.RegisterType((*UserFollowInChannel)(nil), "ga.music_topic_channel.UserFollowInChannel")
	proto.RegisterType((*PreferenceKeywords)(nil), "ga.music_topic_channel.PreferenceKeywords")
	proto.RegisterType((*PreferenceKeyword)(nil), "ga.music_topic_channel.PreferenceKeyword")
	proto.RegisterType((*ListChannelPreferenceKeywordsRequest)(nil), "ga.music_topic_channel.ListChannelPreferenceKeywordsRequest")
	proto.RegisterType((*ListChannelPreferenceKeywordsResponse)(nil), "ga.music_topic_channel.ListChannelPreferenceKeywordsResponse")
	proto.RegisterEnum("ga.music_topic_channel.FilterAttrType", FilterAttrType_name, FilterAttrType_value)
	proto.RegisterEnum("ga.music_topic_channel.FilterModel", FilterModel_name, FilterModel_value)
	proto.RegisterEnum("ga.music_topic_channel.MusicHomePageDialogType", MusicHomePageDialogType_name, MusicHomePageDialogType_value)
	proto.RegisterEnum("ga.music_topic_channel.MusicChannelLabel", MusicChannelLabel_name, MusicChannelLabel_value)
	proto.RegisterEnum("ga.music_topic_channel.MuseCategoryType", MuseCategoryType_name, MuseCategoryType_value)
	proto.RegisterEnum("ga.music_topic_channel.MusePlayingOption", MusePlayingOption_name, MusePlayingOption_value)
	proto.RegisterEnum("ga.music_topic_channel.TopicChannelPunishType", TopicChannelPunishType_name, TopicChannelPunishType_value)
	proto.RegisterEnum("ga.music_topic_channel.UserFollowInChannelScene", UserFollowInChannelScene_name, UserFollowInChannelScene_value)
	proto.RegisterEnum("ga.music_topic_channel.GetMusicChannelFilterV2Resp_FilterItemType", GetMusicChannelFilterV2Resp_FilterItemType_name, GetMusicChannelFilterV2Resp_FilterItemType_value)
	proto.RegisterEnum("ga.music_topic_channel.GetMusicHomePageViewV2Resp_MusicHomePageType", GetMusicHomePageViewV2Resp_MusicHomePageType_name, GetMusicHomePageViewV2Resp_MusicHomePageType_value)
	proto.RegisterEnum("ga.music_topic_channel.GetMusicHomePageViewV2Resp_ActionType", GetMusicHomePageViewV2Resp_ActionType_name, GetMusicHomePageViewV2Resp_ActionType_value)
	proto.RegisterEnum("ga.music_topic_channel.MuseGetTopicChannelInfoRequest_ChannelInfoType", MuseGetTopicChannelInfoRequest_ChannelInfoType_name, MuseGetTopicChannelInfoRequest_ChannelInfoType_value)
	proto.RegisterEnum("ga.music_topic_channel.MuseGetTopicChannelInfoResponse_TabType", MuseGetTopicChannelInfoResponse_TabType_name, MuseGetTopicChannelInfoResponse_TabType_value)
}

func init() {
	proto.RegisterFile("music_topic_channel/music-topic-channel-logic_.proto", fileDescriptor_music_topic_channel_logic__1107b2512245c0b7)
}

var fileDescriptor_music_topic_channel_logic__1107b2512245c0b7 = []byte{
	// 4858 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x7b, 0xcd, 0x6f, 0x1b, 0x49,
	0x76, 0xb8, 0x9b, 0x12, 0x25, 0xf2, 0xf1, 0x43, 0x54, 0x59, 0xb6, 0x69, 0xc9, 0x1e, 0xeb, 0xd7,
	0xe3, 0xf5, 0x68, 0xf4, 0xdb, 0x91, 0x67, 0x34, 0xe3, 0xdd, 0xcd, 0x2c, 0x26, 0xb3, 0x14, 0x4d,
	0xc9, 0x1c, 0x8b, 0x14, 0xdd, 0xa4, 0xc6, 0xe3, 0x49, 0x16, 0x8d, 0x66, 0xb3, 0x44, 0xf5, 0xa8,
	0xbb, 0x8b, 0xd3, 0xdd, 0xb4, 0xac, 0x59, 0x20, 0x48, 0x80, 0x60, 0x17, 0xc8, 0x25, 0x41, 0x6e,
	0x9b, 0xcb, 0x62, 0x6f, 0x7b, 0x48, 0x90, 0x43, 0x4e, 0x39, 0xec, 0x71, 0x4f, 0x41, 0x90, 0x5c,
	0x02, 0xe4, 0x90, 0x43, 0x90, 0x53, 0x90, 0x43, 0xfe, 0x86, 0xa0, 0x5e, 0x55, 0x93, 0xdd, 0xfc,
	0x92, 0xe8, 0x71, 0x3e, 0x0e, 0x7b, 0xe3, 0x7b, 0xf5, 0xea, 0xd5, 0xab, 0x57, 0xaf, 0xde, 0x57,
	0x35, 0xe1, 0x23, 0xa7, 0xef, 0x5b, 0xa6, 0x1e, 0xb0, 0x9e, 0x65, 0xea, 0xe6, 0xa9, 0xe1, 0xba,
	0xd4, 0x7e, 0x88, 0xb8, 0xf7, 0x10, 0xf7, 0x9e, 0xc4, 0xbd, 0x67, 0xb3, 0xae, 0x65, 0xea, 0x3b,
	0x3d, 0x8f, 0x05, 0x8c, 0xdc, 0xec, 0x1a, 0x3b, 0x13, 0x26, 0xae, 0xe7, 0xba, 0x86, 0xde, 0x36,
	0x7c, 0x2a, 0xc8, 0xd6, 0xd5, 0x38, 0xdb, 0x18, 0x24, 0x59, 0xad, 0x6f, 0x86, 0x70, 0xcf, 0x36,
	0x2e, 0x1e, 0x86, 0xab, 0x71, 0x20, 0xa4, 0xc8, 0x58, 0xce, 0x43, 0xcb, 0x91, 0xc0, 0x23, 0xa7,
	0xef, 0x53, 0xdd, 0x72, 0x03, 0xea, 0x51, 0x3f, 0xd0, 0x4f, 0xfb, 0x6d, 0x1d, 0x25, 0x7b, 0x38,
	0x05, 0x2f, 0xa6, 0xa9, 0x14, 0xd6, 0x0f, 0x68, 0x50, 0xe3, 0x22, 0x97, 0xc5, 0x12, 0xfb, 0x96,
	0x1d, 0x50, 0xef, 0xf3, 0x5d, 0x8d, 0x7e, 0x4d, 0x1e, 0x40, 0x8a, 0x4b, 0xad, 0x7b, 0xf4, 0xeb,
	0xa2, 0xb2, 0xa9, 0x6c, 0x65, 0x76, 0x33, 0x3b, 0x5d, 0x63, 0x67, 0xcf, 0xf0, 0xa9, 0x46, 0xbf,
	0xd6, 0x96, 0xdb, 0xe2, 0x07, 0xb9, 0x07, 0x99, 0x13, 0x9c, 0xa6, 0x07, 0x17, 0x3d, 0x5a, 0x4c,
	0x6c, 0x2a, 0x5b, 0x69, 0x0d, 0x04, 0xaa, 0x75, 0xd1, 0xa3, 0xea, 0x3f, 0x26, 0x61, 0x63, 0xea,
	0x3a, 0x7e, 0x8f, 0xbc, 0x0b, 0x69, 0xb9, 0x90, 0xdf, 0x93, 0x2b, 0x65, 0x87, 0x2b, 0xf9, 0x3d,
	0x2d, 0xd5, 0x96, 0xbf, 0x88, 0x01, 0x59, 0xb9, 0x96, 0x15, 0x50, 0xc7, 0x2f, 0x26, 0x36, 0x17,
	0xb6, 0x32, 0xbb, 0xbf, 0xbb, 0x33, 0x59, 0xf3, 0x3b, 0x33, 0x56, 0xdd, 0x11, 0x40, 0x35, 0xa0,
	0x8e, 0x26, 0xe5, 0xe7, 0xbf, 0xfd, 0xf5, 0x7f, 0x4e, 0x00, 0x0c, 0xc7, 0xc8, 0x1a, 0x24, 0x03,
	0x2b, 0xb0, 0x29, 0x0a, 0x96, 0xd6, 0x04, 0x40, 0xb6, 0xa0, 0x10, 0x91, 0x23, 0xba, 0xf1, 0xfc,
	0x90, 0x17, 0xdf, 0x3c, 0xd9, 0x80, 0x74, 0x48, 0xd9, 0x29, 0x2e, 0x20, 0x49, 0x4a, 0x92, 0x74,
	0xc8, 0xd9, 0x80, 0x8d, 0xdf, 0x6f, 0xcb, 0x2d, 0x2d, 0xe2, 0x96, 0x4a, 0xaf, 0xbf, 0xa5, 0x66,
	0xbf, 0x8d, 0xbb, 0x92, 0x92, 0x48, 0xd0, 0x27, 0x05, 0x58, 0x08, 0xac, 0x5e, 0x31, 0x89, 0x32,
	0xf0, 0x9f, 0x91, 0x5d, 0x18, 0x41, 0x20, 0x8f, 0x6f, 0x69, 0x53, 0xd9, 0xca, 0x85, 0x73, 0x4b,
	0x41, 0x80, 0x47, 0x48, 0x1e, 0x03, 0x98, 0x56, 0x70, 0xa1, 0x0b, 0x55, 0x2c, 0xe3, 0x19, 0x7d,
	0x67, 0x9a, 0x88, 0x4d, 0xc3, 0xa1, 0x65, 0x2b, 0xb8, 0x68, 0x71, 0x62, 0x2d, 0x6d, 0x86, 0x3f,
	0xd7, 0xbf, 0x81, 0x5c, 0x4c, 0xc4, 0x29, 0xca, 0x55, 0x21, 0x17, 0xd5, 0x4a, 0x47, 0x6a, 0x36,
	0x33, 0xdc, 0x4f, 0x87, 0x3c, 0x84, 0xb5, 0x11, 0xcd, 0x09, 0xf1, 0x85, 0x86, 0x57, 0x63, 0x5b,
	0x47, 0x23, 0x3c, 0x84, 0xfc, 0x7e, 0xfc, 0x64, 0xd6, 0xa0, 0xf0, 0xe4, 0xa8, 0x56, 0xd1, 0xf7,
	0xab, 0x87, 0xad, 0x8a, 0xa6, 0x57, 0x5b, 0x95, 0x5a, 0xe1, 0x1a, 0xb9, 0x01, 0xab, 0x8d, 0xd2,
	0x41, 0x14, 0xab, 0xd5, 0x0a, 0x0a, 0xc9, 0x41, 0x1a, 0xd1, 0x8d, 0xa3, 0x66, 0xab, 0x90, 0x50,
	0x9f, 0x41, 0x2e, 0xb6, 0x4b, 0x7e, 0xcc, 0xa8, 0x20, 0xd7, 0x70, 0xc2, 0xdd, 0xa4, 0x38, 0xa2,
	0x6e, 0x38, 0x94, 0xbc, 0x0d, 0xb9, 0x9e, 0xc7, 0x5e, 0x5a, 0xae, 0x49, 0x05, 0x81, 0xd8, 0x50,
	0x36, 0x44, 0x72, 0x22, 0xf5, 0x17, 0x49, 0xb8, 0x71, 0x68, 0xf9, 0xc1, 0x13, 0xd6, 0x6e, 0x5f,
	0xc8, 0xd3, 0x9d, 0xef, 0x22, 0xae, 0x41, 0xd2, 0x64, 0x7d, 0x37, 0x40, 0xf6, 0x39, 0x4d, 0x00,
	0xfc, 0xd8, 0x7d, 0xfa, 0x0a, 0x15, 0x93, 0xd4, 0xf8, 0xcf, 0xb8, 0x49, 0x2e, 0x8e, 0x98, 0xa4,
	0x0a, 0x39, 0xcb, 0xd7, 0xfd, 0xc0, 0xf0, 0x02, 0xdd, 0x66, 0x46, 0x07, 0xed, 0x25, 0xa5, 0x65,
	0x2c, 0xbf, 0xc9, 0x71, 0x87, 0xcc, 0xe8, 0x90, 0xef, 0x02, 0xa1, 0xaf, 0x7a, 0xcc, 0xa7, 0x03,
	0xb7, 0x65, 0x75, 0xfc, 0xe2, 0xd2, 0xe6, 0xc2, 0x56, 0x4e, 0x2b, 0x88, 0x11, 0xb9, 0x81, 0x6a,
	0xc7, 0x27, 0xf7, 0x21, 0x1f, 0x3b, 0x4e, 0xbf, 0xb8, 0xbc, 0xb9, 0xc0, 0xb7, 0x1f, 0x39, 0x4f,
	0x9f, 0xf3, 0x1c, 0xf8, 0x3c, 0xc3, 0x3c, 0x33, 0xba, 0x94, 0x4b, 0x97, 0x42, 0xe9, 0x0a, 0x72,
	0xa4, 0x21, 0x06, 0xaa, 0x1d, 0xf2, 0x31, 0xac, 0x5b, 0xbe, 0xde, 0xf7, 0xa9, 0xa7, 0xdb, 0xcc,
	0x34, 0x02, 0x8b, 0xb9, 0xba, 0xd1, 0x0f, 0x4e, 0x75, 0xd6, 0xa3, 0x6e, 0x31, 0x8d, 0x22, 0xdf,
	0xb4, 0xfc, 0x63, 0x9f, 0x7a, 0x87, 0x72, 0xbc, 0xd4, 0x0f, 0x4e, 0x8f, 0x7a, 0xd4, 0x25, 0x5f,
	0x02, 0x71, 0x59, 0xa0, 0xc7, 0x77, 0x50, 0x04, 0x54, 0xec, 0x77, 0xa7, 0xd9, 0x74, 0x9d, 0x05,
	0x95, 0xe8, 0xc6, 0xf8, 0x49, 0x69, 0x05, 0x77, 0x04, 0x4b, 0xf6, 0x07, 0xfe, 0xc9, 0x61, 0x1d,
	0x6a, 0x17, 0x33, 0x9b, 0xca, 0x56, 0x7e, 0xf7, 0xed, 0x69, 0x5c, 0x85, 0x45, 0xd6, 0x38, 0x69,
	0x68, 0xde, 0x08, 0x90, 0x5b, 0xb0, 0x1c, 0x18, 0x42, 0x59, 0x59, 0x54, 0xeb, 0x52, 0x60, 0xa0,
	0x9a, 0x2a, 0x90, 0x6d, 0xdb, 0xcc, 0x3c, 0xd3, 0x59, 0x8f, 0x6f, 0xaa, 0x98, 0x43, 0x6f, 0xa1,
	0xf2, 0x05, 0xa2, 0x21, 0x43, 0x72, 0xde, 0xe3, 0xa4, 0x47, 0x48, 0xa9, 0x65, 0xda, 0x43, 0x80,
	0x7c, 0x08, 0x4b, 0xb6, 0xd1, 0xa6, 0xb6, 0x5f, 0xcc, 0x23, 0x83, 0x0d, 0xce, 0x60, 0xc4, 0xd1,
	0x18, 0x0e, 0x3d, 0xe4, 0x34, 0x9a, 0x24, 0x55, 0xcb, 0x70, 0x3b, 0xf4, 0x3e, 0x4f, 0x98, 0x43,
	0x1b, 0x46, 0x97, 0x7e, 0x6e, 0xd1, 0xf3, 0xb9, 0x8c, 0x54, 0xfd, 0xfb, 0xe5, 0x61, 0xd0, 0x19,
	0xe5, 0x32, 0x5f, 0x2c, 0xf8, 0x02, 0x16, 0x07, 0x7e, 0x37, 0xbf, 0xfb, 0xf8, 0x32, 0x87, 0x39,
	0xbe, 0xd8, 0x4e, 0x0c, 0xcf, 0x7d, 0x82, 0x86, 0x1c, 0x89, 0x0b, 0x2b, 0x27, 0x7d, 0xdb, 0x46,
	0x8f, 0xa2, 0xbf, 0xb4, 0xe8, 0xb9, 0x5f, 0x5c, 0x40, 0x35, 0xed, 0x7f, 0xdb, 0x45, 0x3e, 0xdf,
	0xe5, 0x23, 0x5a, 0x8e, 0xb3, 0xe7, 0x0b, 0x72, 0xc8, 0x27, 0x1e, 0xac, 0xfa, 0xa6, 0xc7, 0xe2,
	0x2b, 0x2e, 0xbe, 0xd1, 0x15, 0x57, 0xc4, 0x02, 0x83, 0x35, 0xd7, 0x7f, 0xb1, 0x00, 0xd7, 0x27,
	0x10, 0x4e, 0x71, 0xc9, 0x1b, 0x90, 0xe6, 0x97, 0x57, 0x8c, 0x08, 0xef, 0x95, 0xf2, 0xfb, 0x6d,
	0xe1, 0xfb, 0x08, 0x2c, 0x5a, 0x26, 0x73, 0xa5, 0xef, 0xc5, 0xdf, 0xe4, 0xc7, 0x00, 0x86, 0xc9,
	0x4d, 0x8d, 0xaf, 0x88, 0x4e, 0x26, 0xbf, 0xfb, 0xc9, 0x6b, 0xec, 0xa5, 0x34, 0x60, 0xa2, 0x45,
	0x18, 0x12, 0x53, 0xc8, 0x23, 0x34, 0x95, 0x7c, 0xa3, 0x9a, 0xe2, 0xfb, 0x12, 0xc7, 0x72, 0x1f,
	0xf2, 0x5f, 0xf7, 0x2d, 0xf3, 0x4c, 0x77, 0x8c, 0xc0, 0x3c, 0xe5, 0xee, 0x68, 0x49, 0xf8, 0x6d,
	0xc4, 0xd6, 0x38, 0xb2, 0xda, 0xe1, 0xfe, 0xb5, 0xef, 0xd9, 0x18, 0x13, 0xd3, 0x1a, 0xff, 0x49,
	0x6e, 0xc0, 0x52, 0x60, 0x74, 0x43, 0xf7, 0x95, 0xd3, 0x92, 0x81, 0xd1, 0xad, 0x76, 0x38, 0xba,
	0xdd, 0xd5, 0x2d, 0xa7, 0x8b, 0xfe, 0x29, 0xad, 0x25, 0xdb, 0xdd, 0xaa, 0xd3, 0xe5, 0x57, 0x9d,
	0x6f, 0x83, 0x93, 0x03, 0xe2, 0x97, 0x38, 0x58, 0xed, 0xa8, 0x15, 0x58, 0x1d, 0x33, 0x50, 0x72,
	0x13, 0x48, 0x0c, 0xa9, 0xef, 0xf7, 0x6d, 0xbb, 0x70, 0x8d, 0x14, 0x61, 0x2d, 0x8e, 0x6f, 0xe2,
	0x79, 0x17, 0x14, 0xf5, 0xc7, 0x00, 0x43, 0x25, 0x92, 0x5b, 0x70, 0xbd, 0x69, 0xb9, 0x5d, 0xbd,
	0xa4, 0x6b, 0xac, 0xef, 0x76, 0xf4, 0xc7, 0x96, 0x61, 0xb3, 0x6e, 0xe1, 0x1a, 0x59, 0x81, 0xcc,
	0x33, 0xdc, 0x2c, 0xee, 0xab, 0xa0, 0x90, 0x65, 0x58, 0x38, 0xf6, 0xec, 0x42, 0x82, 0xdc, 0x81,
	0x62, 0x6c, 0x4a, 0x94, 0x6c, 0x41, 0xfd, 0x13, 0x65, 0x98, 0xdc, 0x85, 0x8b, 0x0b, 0xa6, 0xf3,
	0x05, 0xaf, 0x72, 0xec, 0x36, 0x3f, 0x9c, 0x76, 0x98, 0x13, 0xd6, 0x19, 0x5e, 0x5c, 0xf5, 0xd7,
	0x09, 0xb8, 0x33, 0x5d, 0x98, 0xf9, 0xdc, 0xcb, 0x31, 0x24, 0x85, 0x79, 0x89, 0x1c, 0xf3, 0xd3,
	0xab, 0x9a, 0x57, 0x74, 0xbd, 0x1d, 0x09, 0x70, 0xbb, 0x12, 0xdc, 0xd6, 0xff, 0x42, 0x01, 0x18,
	0x62, 0x5f, 0xe7, 0xba, 0xad, 0x41, 0xd2, 0x72, 0x8c, 0x6e, 0x98, 0xeb, 0x08, 0x80, 0xdc, 0x05,
	0x68, 0x1b, 0xe6, 0x99, 0x6e, 0x32, 0x9b, 0x79, 0x32, 0xaa, 0xa7, 0x39, 0xa6, 0xcc, 0x11, 0x13,
	0x6c, 0x39, 0x39, 0x6e, 0xcb, 0xea, 0xcf, 0x15, 0xd8, 0x78, 0x36, 0x40, 0xbc, 0x7e, 0x26, 0x32,
	0xbe, 0x5a, 0x62, 0xc2, 0xcd, 0x99, 0x1c, 0xf2, 0x17, 0x26, 0x87, 0x7c, 0xf5, 0xa7, 0x0a, 0xdc,
	0x99, 0x2e, 0xdb, 0x7c, 0x67, 0x7b, 0x17, 0x60, 0x98, 0xb9, 0xc8, 0x74, 0x29, 0x6d, 0x86, 0x29,
	0x0b, 0xb9, 0x03, 0xe9, 0x13, 0xc6, 0x82, 0x9e, 0x67, 0xb9, 0x81, 0x94, 0x67, 0x88, 0x50, 0xff,
	0x43, 0x81, 0x9b, 0x1a, 0xed, 0xbb, 0x16, 0x73, 0xab, 0xbc, 0xb2, 0x12, 0x6e, 0xa9, 0xd1, 0xf7,
	0x4f, 0xc9, 0x8f, 0x20, 0xfd, 0x15, 0xb3, 0x5c, 0x4c, 0x4c, 0xa4, 0x08, 0x53, 0x63, 0xbf, 0x64,
	0xc1, 0x53, 0x14, 0x2d, 0xc5, 0x67, 0xf1, 0x5f, 0x3c, 0x81, 0xf0, 0xc4, 0x80, 0x60, 0x92, 0xb8,
	0x3a, 0x93, 0x8c, 0x37, 0x04, 0xb8, 0x4f, 0x0e, 0xe8, 0xab, 0x50, 0x7a, 0xfc, 0x4d, 0x6e, 0x43,
	0xca, 0xe1, 0x1c, 0x38, 0x5e, 0x18, 0xc8, 0xb2, 0x63, 0x99, 0x2d, 0x3e, 0x74, 0x0b, 0x96, 0x1d,
	0x1a, 0x18, 0x43, 0xbb, 0x58, 0xe2, 0x60, 0xb5, 0xa3, 0x5a, 0x90, 0x89, 0xac, 0x81, 0xce, 0xce,
	0xea, 0xe0, 0xd6, 0x72, 0x1a, 0xff, 0x49, 0xd6, 0x21, 0xc5, 0x05, 0x8d, 0xa4, 0xb5, 0x03, 0x98,
	0x8f, 0xb9, 0x96, 0x79, 0x86, 0x63, 0xb2, 0xf4, 0x09, 0xe1, 0x30, 0x2d, 0x5d, 0x1c, 0xa4, 0xa5,
	0xea, 0x2f, 0x13, 0x70, 0xb3, 0xd1, 0x6f, 0xdb, 0x96, 0x7f, 0x1a, 0xad, 0x70, 0xe6, 0xb1, 0xbb,
	0x4b, 0xce, 0x15, 0x1d, 0x73, 0x3b, 0x34, 0x32, 0x74, 0xcc, 0xbc, 0x96, 0xd8, 0x83, 0x5c, 0x34,
	0xa7, 0x0a, 0x43, 0xef, 0xdd, 0xf1, 0x9c, 0x28, 0x9a, 0x4f, 0x65, 0x23, 0xf9, 0x94, 0x4f, 0xfe,
	0x1f, 0x64, 0xc3, 0x95, 0x71, 0xbb, 0x42, 0x8b, 0x19, 0x89, 0xc3, 0x2a, 0x40, 0x85, 0x9c, 0x7f,
	0xca, 0xce, 0xf5, 0x2e, 0x65, 0xba, 0xe5, 0x9e, 0x30, 0x8c, 0x26, 0x29, 0x2d, 0xc3, 0x91, 0x07,
	0x94, 0x55, 0xdd, 0x13, 0x46, 0xde, 0x82, 0xcc, 0x57, 0x7d, 0xa7, 0xa7, 0x4b, 0x31, 0x97, 0xc5,
	0x0e, 0x38, 0xaa, 0xc5, 0x45, 0x55, 0xff, 0x4e, 0x81, 0x5b, 0x13, 0x75, 0x34, 0x9f, 0xfd, 0x6f,
	0x01, 0xde, 0xaf, 0x2e, 0xd5, 0x4d, 0xc6, 0x6c, 0xbd, 0xc3, 0xce, 0x5d, 0xa9, 0xad, 0xbc, 0xc0,
	0x97, 0x19, 0xb3, 0x1f, 0xb3, 0x73, 0x97, 0xbc, 0x03, 0x2b, 0x27, 0x1e, 0xa5, 0xdf, 0x50, 0xbd,
	0xd3, 0xf7, 0x30, 0x8f, 0x96, 0xba, 0xcb, 0x0b, 0xf4, 0x63, 0x89, 0x25, 0xbb, 0x70, 0xc3, 0xe8,
	0x07, 0x4c, 0xef, 0x58, 0xbe, 0x63, 0xf9, 0xfe, 0x90, 0x7c, 0x11, 0xc9, 0xaf, 0xf3, 0xc1, 0xc7,
	0x62, 0x2c, 0x9c, 0xa3, 0x52, 0xb8, 0x53, 0x36, 0x5c, 0x93, 0xda, 0xd1, 0xbd, 0xc8, 0xed, 0xbd,
	0xb9, 0x63, 0x57, 0x3f, 0x83, 0xbb, 0x33, 0x96, 0x99, 0x4b, 0x73, 0xaa, 0x39, 0xcc, 0x5e, 0x87,
	0xe5, 0xe4, 0xde, 0x45, 0xb5, 0xe3, 0xcf, 0x29, 0xf0, 0xa0, 0x02, 0x13, 0x01, 0x86, 0x7b, 0x18,
	0x59, 0x82, 0xf9, 0xea, 0x9f, 0x27, 0x86, 0x31, 0x75, 0x6c, 0x95, 0x79, 0x1b, 0x26, 0xe1, 0x4a,
	0x8e, 0xd1, 0x93, 0xa1, 0x6c, 0xef, 0xb2, 0x50, 0x36, 0x61, 0xcd, 0xb0, 0x54, 0x31, 0x7a, 0x15,
	0x37, 0xf0, 0x2e, 0x42, 0x69, 0x6b, 0x46, 0x6f, 0x9d, 0x86, 0x95, 0x75, 0x38, 0xc8, 0xef, 0xf6,
	0x19, 0xbd, 0x90, 0x21, 0x8d, 0xff, 0x24, 0x9f, 0x40, 0xf2, 0xa5, 0x61, 0xf7, 0xa9, 0xf4, 0x67,
	0xef, 0xcc, 0x0c, 0xef, 0x91, 0xce, 0x8c, 0x98, 0xf5, 0x71, 0xe2, 0x07, 0x8a, 0xfa, 0xab, 0x04,
	0xac, 0x8c, 0x0c, 0xc7, 0x2b, 0x59, 0x65, 0xa4, 0x92, 0x25, 0xb0, 0x18, 0xf1, 0x4a, 0xf8, 0x7b,
	0x62, 0xaa, 0x7a, 0x13, 0x96, 0x30, 0x84, 0x8a, 0x7b, 0x9f, 0xd6, 0x24, 0x44, 0x9e, 0x40, 0x86,
	0x07, 0x61, 0xc1, 0x2f, 0xcc, 0x32, 0xaf, 0x2c, 0x39, 0xf8, 0xfd, 0xb6, 0x00, 0xfd, 0xff, 0xe9,
	0x3e, 0x8b, 0xfa, 0xaf, 0x49, 0x58, 0xe3, 0x05, 0x6a, 0xd4, 0xde, 0xe7, 0xb2, 0xcf, 0x98, 0x5e,
	0x13, 0x23, 0x7a, 0xbd, 0x0f, 0xf9, 0xa1, 0x5e, 0xd0, 0x80, 0x17, 0x44, 0x3d, 0x3f, 0xd8, 0x31,
	0x2f, 0x54, 0x07, 0xcd, 0x88, 0xc5, 0x68, 0x33, 0xe2, 0xcd, 0x77, 0x17, 0x26, 0x27, 0x11, 0xcb,
	0xaf, 0xd5, 0x37, 0x48, 0xbd, 0x46, 0xdf, 0x20, 0xfd, 0xdf, 0xd2, 0x37, 0x80, 0x6f, 0xdf, 0x37,
	0xc8, 0xcc, 0xec, 0x1b, 0x64, 0xbf, 0x6d, 0xdf, 0x20, 0x77, 0xe5, 0xbe, 0x01, 0xd1, 0x60, 0xc5,
	0xb4, 0x0d, 0xdf, 0xb7, 0x4e, 0x2e, 0xf4, 0x58, 0xd7, 0xe1, 0xdd, 0x69, 0xfb, 0x2b, 0x4b, 0x72,
	0xe4, 0x63, 0x73, 0x95, 0xe5, 0xcd, 0x28, 0xca, 0x57, 0xff, 0x00, 0x56, 0xc7, 0x88, 0xc8, 0xdb,
	0x90, 0x1b, 0x2c, 0x14, 0x69, 0xc4, 0x65, 0x43, 0x24, 0x86, 0xe1, 0xc7, 0xe3, 0xd2, 0x24, 0x2e,
	0xdf, 0xcb, 0xe8, 0xfa, 0x0c, 0xd6, 0x26, 0x1d, 0xed, 0x6c, 0x8f, 0xf4, 0x31, 0xac, 0x8f, 0x5b,
	0x90, 0x6e, 0x75, 0x74, 0x2e, 0x3d, 0x4a, 0x91, 0xd3, 0x6e, 0x8e, 0xda, 0x46, 0xb5, 0xc3, 0x19,
	0xab, 0x7f, 0xa9, 0x88, 0xf6, 0xe0, 0xc8, 0x9d, 0x9e, 0x2f, 0x1a, 0xfc, 0x08, 0x52, 0x72, 0xd5,
	0x70, 0xd3, 0xf7, 0x67, 0xfa, 0xb3, 0x30, 0xbd, 0x18, 0xcc, 0x22, 0x0f, 0x60, 0xc5, 0xf2, 0xf5,
	0x36, 0x0b, 0x02, 0xe6, 0xe8, 0x1e, 0x35, 0xcc, 0x53, 0xf4, 0xa5, 0x29, 0x2d, 0x67, 0xf9, 0x7b,
	0x88, 0xd5, 0x38, 0x52, 0xfd, 0xb7, 0x34, 0x64, 0xa3, 0x2c, 0x46, 0x62, 0xb4, 0x32, 0x9a, 0x9a,
	0x8d, 0xe6, 0x4f, 0x89, 0xf1, 0xfc, 0xe9, 0x7d, 0x58, 0x0b, 0x49, 0x1c, 0xea, 0xb4, 0xa9, 0xa7,
	0x0b, 0x07, 0x23, 0xf2, 0x91, 0xd0, 0x0b, 0xd4, 0x70, 0xa8, 0x8c, 0xde, 0xe6, 0x36, 0xa4, 0xf0,
	0x36, 0x98, 0x32, 0x0d, 0x49, 0x6b, 0xfc, 0x76, 0x54, 0xb9, 0xd3, 0x97, 0x43, 0x1d, 0xea, 0x9b,
	0x32, 0x57, 0xe3, 0x43, 0x8f, 0xa9, 0x6f, 0x72, 0x2b, 0x62, 0xe7, 0x2e, 0x77, 0xd6, 0xa6, 0x58,
	0x40, 0x56, 0xfd, 0x88, 0x2c, 0x09, 0x1c, 0x3f, 0x67, 0x41, 0xc4, 0x93, 0xd8, 0x65, 0x4c, 0x62,
	0x53, 0x88, 0x68, 0xd2, 0x57, 0x3c, 0xef, 0x95, 0x73, 0xfd, 0x62, 0x0a, 0x7d, 0xe3, 0x00, 0xe6,
	0xd1, 0xc6, 0x0f, 0x8c, 0xa0, 0xef, 0xcb, 0x2e, 0x80, 0x84, 0x78, 0x64, 0xf2, 0x99, 0xdb, 0x95,
	0x3d, 0x00, 0xfc, 0x4d, 0xf6, 0x60, 0xc9, 0xa3, 0xbc, 0x6c, 0xc4, 0x3e, 0x62, 0x66, 0x77, 0xfb,
	0x4a, 0x87, 0x85, 0x33, 0x34, 0x39, 0x93, 0x7c, 0x0a, 0x49, 0xb4, 0xf2, 0x62, 0x16, 0x5d, 0xca,
	0xbb, 0x57, 0x61, 0x21, 0x4c, 0x5e, 0xcc, 0x23, 0xdf, 0x83, 0x64, 0xd7, 0x66, 0xde, 0x45, 0x31,
	0x87, 0x32, 0x6c, 0x4e, 0x63, 0xf0, 0x34, 0x78, 0x79, 0xc0, 0xe9, 0x34, 0x41, 0x4e, 0x8e, 0x21,
	0xd7, 0xa3, 0x9e, 0xcf, 0x5c, 0xc3, 0xd6, 0x4d, 0xea, 0x05, 0xc5, 0x3c, 0xce, 0x7f, 0xff, 0x2a,
	0x02, 0x34, 0xe4, 0xc4, 0x32, 0xf5, 0x02, 0x2d, 0xdb, 0x8b, 0x40, 0xf1, 0xda, 0x6c, 0x65, 0xa4,
	0x36, 0x8b, 0x64, 0xf8, 0x85, 0x68, 0x86, 0xbf, 0x01, 0x69, 0x8f, 0x76, 0xb9, 0xb3, 0xb7, 0x3a,
	0xc5, 0xd5, 0x4d, 0x65, 0x6b, 0x51, 0x4b, 0x09, 0x44, 0xb5, 0x13, 0x26, 0xc3, 0xdc, 0xae, 0x6c,
	0xfa, 0x52, 0xd8, 0x27, 0x19, 0x26, 0xc3, 0x5c, 0x1f, 0x1c, 0x5d, 0xed, 0x84, 0x46, 0x83, 0x06,
	0x7a, 0x7d, 0x60, 0x34, 0x68, 0x9c, 0xbb, 0xb0, 0xd0, 0xb3, 0x8c, 0xe2, 0xda, 0x6c, 0x1d, 0xe1,
	0x1e, 0x1b, 0x96, 0xa1, 0x71, 0x62, 0xf2, 0x19, 0x64, 0xc2, 0xa7, 0x39, 0xcb, 0xed, 0x16, 0x6f,
	0xe0, 0xdc, 0xad, 0x99, 0x73, 0xab, 0x43, 0x7a, 0x2d, 0x3a, 0x99, 0x9b, 0x8f, 0xcd, 0xba, 0xac,
	0x78, 0x53, 0x98, 0x0f, 0xff, 0xcd, 0x83, 0x8a, 0x60, 0xe4, 0x33, 0xd3, 0x32, 0xec, 0xe2, 0xad,
	0xd9, 0xb5, 0x24, 0x2e, 0xd0, 0x44, 0x52, 0x2d, 0xe3, 0x0c, 0x01, 0xf2, 0x0c, 0xd2, 0xbe, 0xe1,
	0x50, 0x9d, 0x27, 0x18, 0xc5, 0x22, 0x32, 0xf9, 0x48, 0x32, 0x99, 0xf8, 0xb8, 0xd8, 0xe2, 0x6c,
	0xe5, 0x49, 0x86, 0x39, 0x0a, 0xaf, 0x6e, 0xb4, 0x94, 0x2f, 0xa1, 0xe8, 0x75, 0xc7, 0x6c, 0xe8,
	0x36, 0xea, 0x3b, 0xbc, 0xee, 0xf2, 0x79, 0x26, 0x89, 0xf2, 0x15, 0xd7, 0x65, 0x67, 0x84, 0x03,
	0xdc, 0x8d, 0x08, 0xa9, 0xf1, 0x52, 0x6f, 0x88, 0xf3, 0x47, 0x0c, 0x5e, 0xeb, 0xc1, 0x30, 0x72,
	0xbd, 0x83, 0xf7, 0x52, 0x0c, 0x23, 0xcf, 0x1a, 0x2f, 0xc1, 0x2c, 0xd7, 0x72, 0xbb, 0x7a, 0x8f,
	0x71, 0x03, 0xba, 0x8b, 0x4e, 0x70, 0x96, 0xce, 0x69, 0x53, 0x4c, 0x68, 0x70, 0x7a, 0x2d, 0xeb,
	0x47, 0x20, 0xf5, 0x97, 0x0a, 0x14, 0x46, 0x49, 0xb8, 0x39, 0xc5, 0xd6, 0x18, 0x06, 0x82, 0x7c,
	0x74, 0xb2, 0xe8, 0x7f, 0xc4, 0x29, 0x23, 0x9e, 0xaf, 0x10, 0xa5, 0x45, 0x0b, 0x7b, 0x04, 0xb7,
	0xe2, 0xd4, 0xfc, 0x52, 0x0d, 0x1f, 0xbd, 0x72, 0xda, 0x5a, 0x74, 0x0a, 0xbf, 0x2b, 0xf8, 0xee,
	0xd5, 0x83, 0x3b, 0xd1, 0x83, 0x35, 0xdc, 0xb3, 0x27, 0xcc, 0x65, 0x5e, 0xd3, 0xea, 0xba, 0x58,
	0x71, 0x86, 0x19, 0xb1, 0x12, 0xc9, 0x88, 0xf9, 0x16, 0x82, 0x0b, 0x9b, 0x8a, 0x66, 0xd2, 0x30,
	0x3a, 0xf1, 0x2d, 0x70, 0x3c, 0xb6, 0x94, 0x30, 0xdc, 0x4d, 0x68, 0x33, 0xa8, 0xbf, 0x56, 0x20,
	0x13, 0x59, 0x92, 0x87, 0x0c, 0xe9, 0xaf, 0xd1, 0xa1, 0xe8, 0xed, 0xae, 0xdc, 0x63, 0x4e, 0xa0,
	0xd1, 0xdb, 0xec, 0x75, 0xc9, 0x36, 0xac, 0xc6, 0xe8, 0x22, 0x8c, 0x57, 0x22, 0x94, 0xd8, 0xaf,
	0xf8, 0x12, 0xf2, 0x9e, 0xe1, 0x9e, 0xe9, 0xbe, 0xd5, 0x75, 0x45, 0x31, 0xbd, 0x18, 0xb3, 0xcb,
	0x99, 0xc6, 0x3d, 0xaa, 0x03, 0x2d, 0xcb, 0x79, 0x85, 0x90, 0xfa, 0x11, 0xa4, 0xc2, 0x7b, 0xca,
	0x8d, 0x50, 0x78, 0x4f, 0x05, 0xb7, 0x2f, 0x5d, 0xe2, 0x84, 0xca, 0x42, 0xdd, 0x42, 0x53, 0x88,
	0xdd, 0xd0, 0xa1, 0x09, 0x2b, 0x11, 0x13, 0x56, 0x7f, 0x22, 0x5b, 0xb8, 0x31, 0x7f, 0x4d, 0xbe,
	0x03, 0x79, 0xe1, 0xb1, 0x07, 0x61, 0x47, 0x4c, 0xca, 0x09, 0x6c, 0x18, 0x77, 0xee, 0x41, 0x46,
	0x92, 0x61, 0xe8, 0x92, 0x8f, 0xed, 0x02, 0x85, 0xd1, 0xeb, 0x2e, 0x48, 0x48, 0x1f, 0xbe, 0xfa,
	0xa5, 0x05, 0xa6, 0x49, 0x5f, 0xa9, 0x3f, 0x53, 0x20, 0x15, 0x7a, 0x6a, 0x4e, 0x8b, 0xbe, 0x3a,
	0x9a, 0x2c, 0xa5, 0x11, 0x83, 0x16, 0xb7, 0x01, 0x02, 0xc0, 0x9e, 0xb5, 0xac, 0x02, 0x10, 0x51,
	0x75, 0xba, 0x64, 0x13, 0xb2, 0x62, 0x50, 0xf6, 0xb4, 0xc5, 0x41, 0x09, 0x7e, 0x7b, 0xd8, 0xd8,
	0x1e, 0x70, 0xe7, 0xda, 0x95, 0x65, 0x80, 0x60, 0xc8, 0x4f, 0x40, 0xfd, 0x63, 0x05, 0x8a, 0xd3,
	0x7c, 0xfe, 0x44, 0xab, 0x0c, 0x6d, 0x2d, 0x11, 0x69, 0x69, 0x61, 0x95, 0x61, 0x33, 0x4f, 0x96,
	0x20, 0x02, 0xe0, 0x96, 0xc4, 0x47, 0x75, 0xff, 0xd4, 0xe8, 0xb0, 0xf3, 0x58, 0x4b, 0x74, 0x85,
	0x0f, 0x34, 0x11, 0x8f, 0x56, 0xac, 0x7e, 0x01, 0xb7, 0x0e, 0x68, 0xd0, 0x32, 0xda, 0xb2, 0x21,
	0xf0, 0x84, 0x05, 0x9a, 0xe9, 0x74, 0xe6, 0xa9, 0x96, 0x86, 0x41, 0x27, 0x11, 0x09, 0x3a, 0xea,
	0x4f, 0x15, 0x28, 0x4e, 0x66, 0x3d, 0x5f, 0xd2, 0xf6, 0x29, 0x24, 0xa3, 0x1f, 0x3b, 0x4c, 0x8d,
	0xe0, 0xe3, 0x0b, 0x89, 0x79, 0xfc, 0xcc, 0x57, 0xc7, 0x06, 0x49, 0x1e, 0x12, 0x03, 0xcf, 0x94,
	0xb0, 0xa6, 0x96, 0xcb, 0xa7, 0xc3, 0x1e, 0x28, 0xfe, 0x26, 0x1f, 0xc3, 0x12, 0x56, 0x04, 0x61,
	0x9b, 0x4c, 0x9d, 0x79, 0xe5, 0xb0, 0x92, 0xd0, 0xe4, 0x0c, 0x75, 0x1f, 0x60, 0x88, 0xe5, 0xe1,
	0x54, 0xd4, 0x24, 0x83, 0x84, 0x70, 0x19, 0xe1, 0x6a, 0x87, 0xdb, 0x0e, 0xb5, 0xa9, 0x43, 0x85,
	0xfb, 0x94, 0x1d, 0x1d, 0x89, 0xa9, 0x76, 0xd4, 0x53, 0xb8, 0x77, 0x40, 0x03, 0x8d, 0xfa, 0xac,
	0xef, 0x99, 0xb4, 0xcc, 0xdc, 0x13, 0xab, 0xbb, 0x77, 0x31, 0x48, 0x98, 0xdf, 0x60, 0xef, 0xe8,
	0xcf, 0x14, 0xd8, 0x9c, 0xbd, 0xd4, 0x7c, 0x87, 0x39, 0xc9, 0x88, 0x27, 0x35, 0x25, 0x6e, 0x43,
	0x0a, 0x1b, 0x81, 0x7d, 0xcf, 0x0e, 0x53, 0x57, 0x0e, 0x1f, 0x7b, 0xb6, 0xfa, 0x2f, 0x09, 0x78,
	0x8b, 0x47, 0x1d, 0x6e, 0x5b, 0x91, 0x48, 0x8b, 0x9e, 0x8c, 0x7e, 0xdd, 0xa7, 0x7e, 0xf0, 0xa6,
	0xfa, 0xa5, 0x1e, 0xac, 0x0e, 0x86, 0xdd, 0x13, 0x36, 0x0c, 0x36, 0xf9, 0xe9, 0xaf, 0x6d, 0xb3,
	0x25, 0xdb, 0x89, 0xa0, 0xf0, 0xdd, 0x66, 0xc5, 0x8c, 0x23, 0xc6, 0x32, 0x83, 0xc5, 0xb1, 0xcc,
	0x40, 0xfd, 0x7d, 0x58, 0x19, 0x61, 0x43, 0xb6, 0xe0, 0x7e, 0xf9, 0x49, 0xa9, 0x5e, 0xaf, 0x1c,
	0xea, 0xd5, 0xfa, 0xfe, 0x91, 0xde, 0x7a, 0xd1, 0xa8, 0xe8, 0xf5, 0x23, 0xad, 0x56, 0x3a, 0xd4,
	0x8f, 0xeb, 0xcd, 0x46, 0xa5, 0x5c, 0xdd, 0xaf, 0x56, 0x1e, 0x17, 0xae, 0x91, 0x7b, 0xb0, 0x31,
	0x4e, 0xd9, 0x38, 0x2c, 0xbd, 0xc0, 0x5f, 0x05, 0x45, 0xfd, 0xa3, 0x14, 0xdc, 0x9b, 0xba, 0x09,
	0xbf, 0xc7, 0x5c, 0x9f, 0xbe, 0xc1, 0xa7, 0x86, 0x29, 0x2d, 0xe9, 0x68, 0xa6, 0xb9, 0x18, 0xcf,
	0x34, 0x37, 0x21, 0x6b, 0xf9, 0xba, 0xe5, 0xea, 0x5d, 0x8f, 0xf5, 0xdd, 0xb0, 0x83, 0x02, 0x96,
	0x5f, 0x75, 0x0f, 0x10, 0xc3, 0x97, 0xb4, 0x7c, 0xbd, 0xe7, 0x59, 0x2f, 0x8d, 0x80, 0xca, 0x2e,
	0x73, 0xda, 0xf2, 0x1b, 0x02, 0xc1, 0xdd, 0xfa, 0x29, 0x35, 0x3a, 0x22, 0x80, 0x88, 0x46, 0x49,
	0x8a, 0x23, 0x30, 0x7c, 0x34, 0x20, 0xdf, 0xb3, 0x8d, 0x0b, 0x9e, 0x65, 0xc8, 0x4e, 0x01, 0x2f,
	0x60, 0x66, 0xd7, 0x0d, 0xb4, 0x21, 0x66, 0xc8, 0x86, 0x41, 0xae, 0x17, 0x05, 0x49, 0x0b, 0x0a,
	0xfe, 0xb9, 0x15, 0x98, 0xa7, 0xd8, 0x60, 0x10, 0xc1, 0x3a, 0x7d, 0x69, 0x39, 0x43, 0x9b, 0x38,
	0x87, 0x73, 0x46, 0xcd, 0xe7, 0xfd, 0x18, 0x4c, 0xbe, 0x14, 0x0a, 0x42, 0x13, 0x11, 0xcd, 0x92,
	0x4f, 0xe7, 0xb6, 0x48, 0x71, 0x98, 0xdc, 0x6f, 0xa2, 0x29, 0x72, 0x0d, 0x87, 0x26, 0x78, 0x4e,
	0x6d, 0x93, 0x39, 0x54, 0xe4, 0x20, 0x19, 0x51, 0x8b, 0x4a, 0x1c, 0xe6, 0x1f, 0x2d, 0x28, 0x04,
	0xa7, 0x96, 0xd7, 0xd1, 0x7b, 0x86, 0x17, 0x5c, 0xe8, 0x5d, 0x7e, 0x4e, 0xd9, 0xcb, 0x37, 0xd5,
	0xe2, 0x73, 0x1a, 0x7c, 0xca, 0x81, 0xe1, 0x50, 0x2d, 0x1f, 0xc4, 0x60, 0xb2, 0x03, 0xd7, 0xfd,
	0x53, 0xeb, 0x24, 0xd0, 0x3d, 0xc6, 0x9c, 0x61, 0x07, 0x3d, 0x87, 0x96, 0xb1, 0x8a, 0x43, 0x1a,
	0x63, 0xce, 0xa0, 0xe7, 0x3e, 0xa1, 0x39, 0x9f, 0x9f, 0xd8, 0x9c, 0x1f, 0xbe, 0x48, 0xaf, 0x44,
	0x5f, 0xa4, 0x37, 0x20, 0x1d, 0x50, 0xc3, 0x11, 0x96, 0x50, 0x10, 0x96, 0xc0, 0x11, 0x68, 0x09,
	0xf7, 0x21, 0x8f, 0xcf, 0x15, 0x43, 0x8a, 0x55, 0xb4, 0xa4, 0x2c, 0xc7, 0xb6, 0x42, 0x2a, 0x14,
	0x99, 0x9d, 0xeb, 0x3d, 0x11, 0x5c, 0xf4, 0x76, 0x3f, 0x08, 0x98, 0x8b, 0xf5, 0x53, 0x8a, 0x8b,
	0xcc, 0xce, 0x65, 0xd8, 0xd9, 0xc3, 0x01, 0x9e, 0x81, 0x9b, 0x46, 0x40, 0xbb, 0x3c, 0x2f, 0xc0,
	0xc3, 0xbb, 0x8e, 0x87, 0x37, 0x33, 0x03, 0x2f, 0xcb, 0x09, 0x78, 0x4a, 0x59, 0x33, 0x02, 0xa9,
	0xc7, 0xb0, 0x2c, 0x8f, 0x8f, 0x5f, 0xec, 0x56, 0x69, 0x6f, 0xc6, 0xcd, 0x5f, 0x85, 0xdc, 0x80,
	0xe0, 0xa0, 0x54, 0xab, 0x14, 0x14, 0x72, 0x13, 0xc8, 0x00, 0x55, 0xab, 0xd6, 0xab, 0x02, 0x9f,
	0x50, 0x3f, 0xc4, 0x14, 0x6d, 0xc4, 0x06, 0x31, 0xb5, 0xe2, 0x07, 0x23, 0x5a, 0x74, 0xe2, 0xbe,
	0xa6, 0x39, 0x06, 0x7b, 0x6f, 0xea, 0x6f, 0x12, 0x62, 0x56, 0xfc, 0x90, 0xb9, 0x92, 0x45, 0x3e,
	0xcb, 0x5d, 0xb9, 0xec, 0x08, 0x21, 0xe2, 0xd8, 0xc3, 0xae, 0x08, 0x6a, 0xce, 0xc4, 0x51, 0x11,
	0x14, 0xd2, 0x02, 0xc3, 0x87, 0x0d, 0xc8, 0x73, 0xd3, 0xc2, 0xaf, 0x47, 0xc5, 0xcd, 0x11, 0xdf,
	0xa1, 0xfc, 0xf0, 0xea, 0x46, 0x26, 0xcc, 0xdf, 0x70, 0x28, 0xf7, 0x4d, 0x22, 0xdb, 0xed, 0x46,
	0xa0, 0xf5, 0x3f, 0x95, 0x35, 0x4c, 0x94, 0x84, 0xac, 0x43, 0xaa, 0x67, 0x1b, 0xc1, 0x09, 0xf3,
	0x9c, 0x50, 0xe4, 0x10, 0x8e, 0x45, 0xa6, 0x44, 0x2c, 0x32, 0xf1, 0x8b, 0xd3, 0x61, 0xe7, 0xae,
	0xcd, 0x8c, 0x0e, 0x0e, 0x8b, 0x80, 0x96, 0x09, 0x71, 0x92, 0x24, 0x6c, 0xd3, 0x46, 0x9c, 0x5b,
	0x46, 0xe2, 0xf0, 0x43, 0xb8, 0xbf, 0x52, 0xe0, 0x6e, 0xf4, 0xb2, 0x1e, 0xfb, 0xd4, 0x7b, 0x6e,
	0x78, 0x6e, 0x9d, 0x05, 0xd6, 0xc9, 0x45, 0xcd, 0xef, 0xf2, 0x24, 0xb8, 0xd7, 0x77, 0xb9, 0xb9,
	0xa1, 0x09, 0x89, 0xdc, 0x01, 0x04, 0x0a, 0x6d, 0xe1, 0x12, 0xa7, 0x7b, 0x1b, 0x52, 0xfd, 0xb0,
	0xeb, 0xb6, 0x80, 0x5d, 0xb7, 0xe5, 0xbe, 0x85, 0x6d, 0x36, 0xf2, 0x01, 0xa4, 0x2c, 0x57, 0x17,
	0x6f, 0x15, 0x22, 0xbf, 0xb9, 0xc9, 0x75, 0x6d, 0x39, 0x3b, 0x9a, 0x65, 0x9e, 0xf2, 0xbb, 0x5f,
	0x11, 0x59, 0x88, 0xb6, 0x6c, 0xb9, 0x9f, 0x73, 0x32, 0xf5, 0x1b, 0x78, 0x30, 0x5d, 0xdc, 0xaa,
	0x2b, 0xd1, 0x5c, 0xee, 0x4b, 0xc4, 0x7a, 0x8d, 0xb5, 0xff, 0x41, 0x81, 0x7b, 0x93, 0x16, 0x3f,
	0xb4, 0xdc, 0xb3, 0xcf, 0xf8, 0xa9, 0x68, 0x87, 0xc4, 0x81, 0x6c, 0x78, 0x60, 0xf8, 0x08, 0xa4,
	0x20, 0xeb, 0x83, 0xa9, 0x69, 0xe4, 0x6c, 0x76, 0x3b, 0x9f, 0x89, 0xc3, 0x0e, 0x1f, 0x7b, 0xf6,
	0x12, 0x45, 0x45, 0x83, 0xaf, 0x06, 0xc8, 0xf5, 0x4f, 0x60, 0x65, 0x84, 0x64, 0xc2, 0x7b, 0xd0,
	0x5a, 0xf4, 0x3d, 0x28, 0x1d, 0x7d, 0xe6, 0xf9, 0x1b, 0x05, 0xde, 0x3a, 0xa0, 0x41, 0xc9, 0xc7,
	0xa6, 0x45, 0x40, 0x35, 0xfa, 0x32, 0xf2, 0x82, 0x31, 0x6f, 0x76, 0xe3, 0xf2, 0x32, 0x20, 0x60,
	0x67, 0xd4, 0x0d, 0x2f, 0x17, 0xc7, 0xb4, 0x38, 0x02, 0x6b, 0x3b, 0xcb, 0xb1, 0xc2, 0x06, 0xa2,
	0x00, 0x46, 0xce, 0x68, 0x71, 0x7a, 0xbc, 0x4e, 0x46, 0x73, 0xfd, 0xdf, 0x28, 0x98, 0x91, 0x4e,
	0x96, 0xfa, 0xb5, 0x92, 0x86, 0x4b, 0x24, 0x17, 0x0f, 0x44, 0x0b, 0xd1, 0x8f, 0x46, 0xf6, 0x22,
	0xcd, 0x5d, 0x61, 0x3e, 0x0f, 0x66, 0xb9, 0x89, 0xa1, 0x88, 0xc3, 0xf6, 0xae, 0xfa, 0x9f, 0x0b,
	0x90, 0x8f, 0x0f, 0x5e, 0xd6, 0xb8, 0xbd, 0x07, 0x19, 0xdf, 0x72, 0x7a, 0x36, 0x8d, 0x15, 0xa4,
	0x02, 0x85, 0x11, 0x62, 0xb4, 0xb3, 0xbb, 0x30, 0xde, 0xd9, 0xdd, 0x85, 0x1b, 0x21, 0x49, 0xbc,
	0xf3, 0x2a, 0xbc, 0xc3, 0x75, 0x39, 0x78, 0x14, 0x6d, 0xc0, 0x6e, 0x0f, 0x73, 0xd3, 0x61, 0x23,
	0x36, 0x89, 0xe5, 0xee, 0x4a, 0x94, 0xbe, 0x49, 0x5f, 0x45, 0xdf, 0x88, 0x04, 0x2d, 0x0a, 0xb2,
	0x14, 0x7b, 0x23, 0x42, 0x62, 0x94, 0xe6, 0x29, 0x84, 0xc2, 0xe9, 0xa7, 0x2c, 0x90, 0x8f, 0x70,
	0x57, 0x54, 0xa5, 0xb8, 0x0d, 0x72, 0xec, 0x09, 0x0b, 0xa2, 0xcd, 0x45, 0x9e, 0xd7, 0xeb, 0x4e,
	0xe7, 0x91, 0xfc, 0xa8, 0x35, 0x6c, 0x2e, 0x56, 0x4d, 0xe6, 0xd6, 0x3a, 0x8f, 0xc8, 0x33, 0x58,
	0xf1, 0xe8, 0x4b, 0x3d, 0xba, 0xb4, 0x48, 0x93, 0xde, 0xbd, 0xda, 0xd2, 0xbc, 0xae, 0xcb, 0x79,
	0x51, 0x70, 0x2c, 0x97, 0x86, 0xf1, 0x5c, 0xfa, 0x13, 0xfc, 0xc8, 0x2c, 0xce, 0x66, 0x62, 0xf5,
	0x1d, 0xfb, 0xb8, 0x78, 0x41, 0xbe, 0xe7, 0xa9, 0x7f, 0xa8, 0xc0, 0x03, 0xf9, 0x2a, 0x41, 0x45,
	0x77, 0xa5, 0xcc, 0x1c, 0xa7, 0xef, 0x5a, 0xc1, 0xc5, 0xeb, 0xde, 0x5a, 0x9e, 0x51, 0x20, 0x27,
	0xdd, 0x0c, 0x59, 0x0d, 0x5f, 0x21, 0x57, 0xfd, 0xf8, 0x22, 0xd5, 0x8e, 0xfa, 0x2b, 0x05, 0xde,
	0xb9, 0x54, 0x84, 0xf9, 0xaf, 0xa0, 0x36, 0xf6, 0x54, 0xf2, 0xbd, 0x99, 0xe9, 0xea, 0xc4, 0x95,
	0x45, 0xd7, 0x73, 0x70, 0xbb, 0xfe, 0x69, 0x59, 0x54, 0x6e, 0xd3, 0x89, 0x7f, 0xfb, 0x4c, 0xf2,
	0x1a, 0xcf, 0x24, 0x6b, 0xd1, 0x27, 0x8e, 0xdc, 0xff, 0xd1, 0x77, 0x8b, 0xdf, 0xbe, 0x4c, 0x44,
	0x5e, 0x26, 0x3e, 0x07, 0xc0, 0xc7, 0x03, 0x71, 0x6c, 0xe2, 0x5d, 0xe2, 0xfb, 0xf3, 0x5f, 0x2f,
	0xf1, 0x4e, 0x95, 0xe6, 0xac, 0xf0, 0xe7, 0xff, 0xd2, 0xb3, 0x82, 0x7a, 0x24, 0x3a, 0x06, 0x33,
	0x84, 0x24, 0x45, 0x58, 0x8e, 0xf7, 0x75, 0x43, 0x70, 0x52, 0x47, 0x48, 0xfd, 0x1d, 0x78, 0xbb,
	0xd6, 0xaa, 0xba, 0x2f, 0xad, 0x80, 0xf2, 0x14, 0xac, 0xc2, 0xd5, 0x29, 0xd9, 0x3d, 0x17, 0x55,
	0x28, 0x7e, 0x6e, 0x18, 0x4e, 0x55, 0x22, 0x53, 0x7f, 0x02, 0xf9, 0x7d, 0x66, 0xdb, 0xec, 0x9c,
	0x4f, 0x44, 0x97, 0x32, 0xf1, 0x9b, 0xbd, 0xc1, 0x77, 0x79, 0x89, 0x91, 0xef, 0xf2, 0x22, 0x2a,
	0xf4, 0x98, 0x1d, 0xbe, 0x2d, 0x84, 0x2a, 0xd4, 0x98, 0x4d, 0xa3, 0x7b, 0x59, 0x8c, 0xed, 0x45,
	0xfd, 0x77, 0x05, 0xae, 0xf3, 0x75, 0x85, 0x04, 0x83, 0xdc, 0x97, 0x2b, 0xdd, 0x37, 0xa9, 0x1b,
	0xa6, 0xea, 0x02, 0x20, 0x55, 0xc8, 0x9e, 0x20, 0x21, 0x7e, 0x90, 0x11, 0xba, 0xd9, 0xa9, 0x91,
	0x36, 0xbe, 0x2d, 0x2d, 0x73, 0x32, 0x80, 0x7d, 0xf2, 0x14, 0x72, 0x02, 0xa4, 0x1d, 0xf1, 0xdd,
	0xe4, 0xc2, 0x5c, 0xbc, 0xb2, 0xe1, 0x64, 0xfc, 0xc8, 0x91, 0x7b, 0x36, 0xcf, 0xea, 0x5a, 0xae,
	0x2e, 0x9a, 0x7e, 0x32, 0x0b, 0xcc, 0x0a, 0x64, 0x13, 0x71, 0x6a, 0x0f, 0x48, 0xc3, 0xa3, 0x27,
	0xd4, 0xa3, 0xae, 0x49, 0x9f, 0xd2, 0x8b, 0x73, 0xe6, 0x75, 0x7c, 0xf2, 0x25, 0x5c, 0xef, 0x0d,
	0xb0, 0xfa, 0x99, 0x44, 0xcb, 0x94, 0x7b, 0x6a, 0x20, 0x1f, 0x63, 0xa4, 0x91, 0xde, 0x18, 0x6f,
	0x1e, 0xaa, 0xc7, 0x08, 0xc7, 0xba, 0xb8, 0x45, 0x58, 0x96, 0xab, 0x86, 0xc5, 0x99, 0x04, 0x55,
	0x0a, 0xf7, 0x79, 0x98, 0x0c, 0x3d, 0xd5, 0x18, 0xff, 0x79, 0xe3, 0x74, 0xbc, 0xeb, 0x9d, 0x0c,
	0x33, 0xe1, 0xbf, 0x56, 0xe0, 0x3b, 0x97, 0xac, 0x33, 0x7f, 0x30, 0xfe, 0xbd, 0xc9, 0x6a, 0x15,
	0x06, 0xb3, 0x7d, 0x65, 0xb5, 0xfa, 0x93, 0xf4, 0xba, 0xfd, 0x2c, 0xfc, 0x7e, 0x6d, 0xf0, 0x15,
	0xd6, 0x3d, 0xd8, 0x90, 0x7f, 0xff, 0x2a, 0xb5, 0x5a, 0x9a, 0x68, 0x0f, 0x1c, 0xd7, 0x2b, 0x5f,
	0x34, 0x2a, 0xe5, 0x16, 0xb6, 0x12, 0xde, 0x82, 0xf5, 0x31, 0x82, 0x66, 0xa9, 0x56, 0xd1, 0xcb,
	0xd5, 0xd6, 0x8b, 0x82, 0xb2, 0xfd, 0x0e, 0x64, 0x22, 0x9f, 0xe8, 0x90, 0x0c, 0x2c, 0xd7, 0x02,
	0xfc, 0x59, 0xb8, 0xc6, 0x81, 0xa7, 0xa7, 0x02, 0x50, 0xb6, 0xdf, 0x85, 0x5b, 0x53, 0xbe, 0x68,
	0x27, 0x79, 0x80, 0xa7, 0xc1, 0xcb, 0xc1, 0x07, 0xfa, 0xdb, 0x27, 0xf2, 0xef, 0x00, 0x31, 0xb7,
	0x72, 0x1b, 0x6e, 0x8c, 0x21, 0xeb, 0xcc, 0xa5, 0x85, 0x6b, 0xe4, 0x4e, 0xfc, 0x79, 0x05, 0x87,
	0x9e, 0xf5, 0x0d, 0xdb, 0x0a, 0x2e, 0x0a, 0xca, 0xe0, 0xff, 0x02, 0xd1, 0xd1, 0x27, 0x2c, 0x28,
	0x24, 0xb6, 0xff, 0x76, 0x51, 0x34, 0x04, 0xa2, 0x5d, 0x17, 0xf2, 0x21, 0x3c, 0xac, 0x1d, 0x37,
	0x2b, 0x7a, 0xf3, 0xa8, 0x5c, 0x2d, 0x1d, 0xea, 0xe5, 0xa3, 0x5a, 0xed, 0xb8, 0x5e, 0x6d, 0xbd,
	0xd0, 0xcb, 0xa5, 0x56, 0xe5, 0xe0, 0x48, 0x7b, 0x11, 0x2a, 0x29, 0xda, 0x70, 0xb9, 0xe2, 0xa4,
	0x83, 0x52, 0xfd, 0xe0, 0xb8, 0x21, 0xdb, 0xaf, 0xe4, 0x03, 0x78, 0xef, 0x2a, 0x93, 0xca, 0x4f,
	0x4a, 0x2d, 0x31, 0x25, 0x41, 0x1e, 0xc1, 0x07, 0x57, 0x99, 0xb2, 0x7f, 0x5c, 0xc7, 0xfe, 0x8e,
	0x98, 0xb6, 0x40, 0x3e, 0x85, 0x1f, 0x5e, 0x69, 0xa5, 0x52, 0xf3, 0xb8, 0x74, 0xa8, 0x57, 0xeb,
	0xad, 0x8a, 0x56, 0x2a, 0xb7, 0xaa, 0x47, 0x75, 0xc1, 0x60, 0x91, 0xec, 0xc2, 0xce, 0x55, 0x18,
	0xd4, 0x8e, 0x9b, 0xd5, 0xb2, 0x98, 0x93, 0xbc, 0xf2, 0x9c, 0xca, 0x61, 0x45, 0x0a, 0xba, 0x44,
	0x7e, 0x00, 0x1f, 0x5d, 0x65, 0x4e, 0xa5, 0x59, 0x2e, 0x35, 0x2a, 0xba, 0x76, 0x74, 0x54, 0x13,
	0x33, 0x97, 0xc9, 0xf7, 0xe1, 0xc3, 0x2b, 0x9d, 0x80, 0x76, 0x74, 0xdc, 0x88, 0xa8, 0x34, 0x75,
	0x55, 0x95, 0xd6, 0x2b, 0xcf, 0x23, 0xd3, 0xd2, 0xdb, 0x3f, 0x53, 0x44, 0x39, 0x11, 0x6b, 0x08,
	0x93, 0x12, 0x7c, 0x32, 0x99, 0x59, 0xe3, 0xb0, 0xf4, 0xa2, 0x5a, 0x3f, 0xd0, 0x8f, 0x1a, 0xa8,
	0xd6, 0x52, 0x4b, 0xaf, 0x95, 0xaa, 0x75, 0x1d, 0xff, 0x68, 0x19, 0x37, 0xa5, 0x87, 0xf0, 0xff,
	0xaf, 0xc4, 0x62, 0x5f, 0xab, 0x56, 0xea, 0x8f, 0x0b, 0xca, 0xb6, 0x0b, 0x37, 0xa3, 0x8d, 0x8c,
	0xc6, 0xb0, 0x37, 0x74, 0x03, 0x56, 0x8f, 0xdd, 0x33, 0x97, 0x9d, 0xbb, 0x43, 0x64, 0xe1, 0x1a,
	0xc9, 0x42, 0xea, 0xb9, 0xe1, 0xe1, 0x9f, 0x64, 0x44, 0x63, 0xf0, 0xa9, 0x65, 0x9e, 0x1d, 0xf5,
	0x43, 0x3f, 0x86, 0xf8, 0x04, 0xb9, 0x05, 0xd7, 0x65, 0x3f, 0x73, 0xf0, 0xd1, 0x97, 0x63, 0x05,
	0x85, 0x85, 0xed, 0x9f, 0x2b, 0x50, 0x9c, 0x10, 0xf9, 0x9a, 0x18, 0xe8, 0xbe, 0x0b, 0x5b, 0xc7,
	0xcd, 0x8a, 0xa6, 0xef, 0x1f, 0x1d, 0x1e, 0x1e, 0x3d, 0xd7, 0xab, 0x75, 0x3d, 0x7c, 0x82, 0x68,
	0x96, 0x2b, 0xf5, 0xd1, 0xbd, 0x3e, 0x00, 0x75, 0x26, 0xf5, 0xd1, 0xf3, 0x7a, 0x45, 0x2b, 0x28,
	0xe4, 0x1d, 0x78, 0x7b, 0x26, 0x9d, 0x68, 0x82, 0x16, 0x12, 0x7b, 0x87, 0x50, 0x34, 0x99, 0xb3,
	0x73, 0x61, 0x5d, 0xb0, 0x3e, 0xfa, 0x4a, 0xee, 0x7c, 0xc4, 0x5f, 0xc0, 0xbf, 0x7c, 0xbf, 0xcb,
	0x6c, 0xc3, 0xed, 0xee, 0x3c, 0xda, 0x0d, 0x82, 0x1d, 0x93, 0x39, 0x0f, 0x11, 0x6d, 0x32, 0xfb,
	0xa1, 0xd1, 0xeb, 0x4d, 0xfa, 0xcb, 0x7b, 0x7b, 0x09, 0x29, 0x3e, 0xfc, 0xaf, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xcd, 0x54, 0xd9, 0x49, 0x24, 0x3f, 0x00, 0x00,
}
