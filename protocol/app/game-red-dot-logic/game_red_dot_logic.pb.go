// Code generated by protoc-gen-go. DO NOT EDIT.
// source: game_red_dot_logic/game_red_dot_logic.proto

package game_red_dot_logic // import "golang.52tt.com/protocol/app/game-red-dot-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 业务类型
type RedDotBizType int32

const (
	RedDotBizType_RED_DOT_BIZ_TYPE_UNSPECIFIED           RedDotBizType = 0
	RedDotBizType_RED_DOT_BIZ_TYPE_GAME_HALL_AT_MSG      RedDotBizType = 1
	RedDotBizType_RED_DOT_BIZ_TYPE_GAME_HALL_JOIN_TEAM   RedDotBizType = 2
	RedDotBizType_RED_DOT_BIZ_TYPE_GAME_USER_RATE_BERATE RedDotBizType = 3
	RedDotBizType_RED_DOT_BIZ_TYPE_AIGC_COMMUNITY        RedDotBizType = 4
	RedDotBizType_RED_DOT_BIZ_TYPE_AIGC_GROUP            RedDotBizType = 5
	RedDotBizType_RED_DOT_BIZ_TYPE_AIGC_EXCLUSIVE_ROLE   RedDotBizType = 6
)

var RedDotBizType_name = map[int32]string{
	0: "RED_DOT_BIZ_TYPE_UNSPECIFIED",
	1: "RED_DOT_BIZ_TYPE_GAME_HALL_AT_MSG",
	2: "RED_DOT_BIZ_TYPE_GAME_HALL_JOIN_TEAM",
	3: "RED_DOT_BIZ_TYPE_GAME_USER_RATE_BERATE",
	4: "RED_DOT_BIZ_TYPE_AIGC_COMMUNITY",
	5: "RED_DOT_BIZ_TYPE_AIGC_GROUP",
	6: "RED_DOT_BIZ_TYPE_AIGC_EXCLUSIVE_ROLE",
}
var RedDotBizType_value = map[string]int32{
	"RED_DOT_BIZ_TYPE_UNSPECIFIED":           0,
	"RED_DOT_BIZ_TYPE_GAME_HALL_AT_MSG":      1,
	"RED_DOT_BIZ_TYPE_GAME_HALL_JOIN_TEAM":   2,
	"RED_DOT_BIZ_TYPE_GAME_USER_RATE_BERATE": 3,
	"RED_DOT_BIZ_TYPE_AIGC_COMMUNITY":        4,
	"RED_DOT_BIZ_TYPE_AIGC_GROUP":            5,
	"RED_DOT_BIZ_TYPE_AIGC_EXCLUSIVE_ROLE":   6,
}

func (x RedDotBizType) String() string {
	return proto.EnumName(RedDotBizType_name, int32(x))
}
func (RedDotBizType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_logic_cbf17191f176df90, []int{0}
}

// 拉取红点信息
type GetRedDotInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	BizTypes             []uint32     `protobuf:"varint,2,rep,packed,name=biz_types,json=bizTypes,proto3" json:"biz_types,omitempty"`
	TabId                uint32       `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRedDotInfoReq) Reset()         { *m = GetRedDotInfoReq{} }
func (m *GetRedDotInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetRedDotInfoReq) ProtoMessage()    {}
func (*GetRedDotInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_logic_cbf17191f176df90, []int{0}
}
func (m *GetRedDotInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRedDotInfoReq.Unmarshal(m, b)
}
func (m *GetRedDotInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRedDotInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetRedDotInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRedDotInfoReq.Merge(dst, src)
}
func (m *GetRedDotInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetRedDotInfoReq.Size(m)
}
func (m *GetRedDotInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRedDotInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRedDotInfoReq proto.InternalMessageInfo

func (m *GetRedDotInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRedDotInfoReq) GetBizTypes() []uint32 {
	if m != nil {
		return m.BizTypes
	}
	return nil
}

func (m *GetRedDotInfoReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetRedDotInfoResp struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RedDotInfos          map[uint32]*RedDotInfo `protobuf:"bytes,2,rep,name=red_dot_infos,json=redDotInfos,proto3" json:"red_dot_infos,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TotalCount           uint32                 `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetRedDotInfoResp) Reset()         { *m = GetRedDotInfoResp{} }
func (m *GetRedDotInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetRedDotInfoResp) ProtoMessage()    {}
func (*GetRedDotInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_logic_cbf17191f176df90, []int{1}
}
func (m *GetRedDotInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRedDotInfoResp.Unmarshal(m, b)
}
func (m *GetRedDotInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRedDotInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetRedDotInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRedDotInfoResp.Merge(dst, src)
}
func (m *GetRedDotInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetRedDotInfoResp.Size(m)
}
func (m *GetRedDotInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRedDotInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRedDotInfoResp proto.InternalMessageInfo

func (m *GetRedDotInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRedDotInfoResp) GetRedDotInfos() map[uint32]*RedDotInfo {
	if m != nil {
		return m.RedDotInfos
	}
	return nil
}

func (m *GetRedDotInfoResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type RedDotInfo struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	LastId               int64    `protobuf:"varint,2,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RedDotInfo) Reset()         { *m = RedDotInfo{} }
func (m *RedDotInfo) String() string { return proto.CompactTextString(m) }
func (*RedDotInfo) ProtoMessage()    {}
func (*RedDotInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_logic_cbf17191f176df90, []int{2}
}
func (m *RedDotInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RedDotInfo.Unmarshal(m, b)
}
func (m *RedDotInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RedDotInfo.Marshal(b, m, deterministic)
}
func (dst *RedDotInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RedDotInfo.Merge(dst, src)
}
func (m *RedDotInfo) XXX_Size() int {
	return xxx_messageInfo_RedDotInfo.Size(m)
}
func (m *RedDotInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RedDotInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RedDotInfo proto.InternalMessageInfo

func (m *RedDotInfo) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *RedDotInfo) GetLastId() int64 {
	if m != nil {
		return m.LastId
	}
	return 0
}

// 标记红点已读
type MarkRedDotReadReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	BizType              uint32       `protobuf:"varint,2,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	TabId                uint32       `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	LastId               int64        `protobuf:"varint,4,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MarkRedDotReadReq) Reset()         { *m = MarkRedDotReadReq{} }
func (m *MarkRedDotReadReq) String() string { return proto.CompactTextString(m) }
func (*MarkRedDotReadReq) ProtoMessage()    {}
func (*MarkRedDotReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_logic_cbf17191f176df90, []int{3}
}
func (m *MarkRedDotReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkRedDotReadReq.Unmarshal(m, b)
}
func (m *MarkRedDotReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkRedDotReadReq.Marshal(b, m, deterministic)
}
func (dst *MarkRedDotReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkRedDotReadReq.Merge(dst, src)
}
func (m *MarkRedDotReadReq) XXX_Size() int {
	return xxx_messageInfo_MarkRedDotReadReq.Size(m)
}
func (m *MarkRedDotReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkRedDotReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkRedDotReadReq proto.InternalMessageInfo

func (m *MarkRedDotReadReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MarkRedDotReadReq) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *MarkRedDotReadReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *MarkRedDotReadReq) GetLastId() int64 {
	if m != nil {
		return m.LastId
	}
	return 0
}

type MarkRedDotReadResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MarkRedDotReadResp) Reset()         { *m = MarkRedDotReadResp{} }
func (m *MarkRedDotReadResp) String() string { return proto.CompactTextString(m) }
func (*MarkRedDotReadResp) ProtoMessage()    {}
func (*MarkRedDotReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_logic_cbf17191f176df90, []int{4}
}
func (m *MarkRedDotReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkRedDotReadResp.Unmarshal(m, b)
}
func (m *MarkRedDotReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkRedDotReadResp.Marshal(b, m, deterministic)
}
func (dst *MarkRedDotReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkRedDotReadResp.Merge(dst, src)
}
func (m *MarkRedDotReadResp) XXX_Size() int {
	return xxx_messageInfo_MarkRedDotReadResp.Size(m)
}
func (m *MarkRedDotReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkRedDotReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkRedDotReadResp proto.InternalMessageInfo

func (m *MarkRedDotReadResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 红点更新推送
type RedDotUpdateNotify struct {
	BizType              uint32   `protobuf:"varint,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	LastId               int64    `protobuf:"varint,4,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	BizSuffixKey         string   `protobuf:"bytes,5,opt,name=biz_suffix_key,json=bizSuffixKey,proto3" json:"biz_suffix_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RedDotUpdateNotify) Reset()         { *m = RedDotUpdateNotify{} }
func (m *RedDotUpdateNotify) String() string { return proto.CompactTextString(m) }
func (*RedDotUpdateNotify) ProtoMessage()    {}
func (*RedDotUpdateNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_logic_cbf17191f176df90, []int{5}
}
func (m *RedDotUpdateNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RedDotUpdateNotify.Unmarshal(m, b)
}
func (m *RedDotUpdateNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RedDotUpdateNotify.Marshal(b, m, deterministic)
}
func (dst *RedDotUpdateNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RedDotUpdateNotify.Merge(dst, src)
}
func (m *RedDotUpdateNotify) XXX_Size() int {
	return xxx_messageInfo_RedDotUpdateNotify.Size(m)
}
func (m *RedDotUpdateNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_RedDotUpdateNotify.DiscardUnknown(m)
}

var xxx_messageInfo_RedDotUpdateNotify proto.InternalMessageInfo

func (m *RedDotUpdateNotify) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *RedDotUpdateNotify) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *RedDotUpdateNotify) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *RedDotUpdateNotify) GetLastId() int64 {
	if m != nil {
		return m.LastId
	}
	return 0
}

func (m *RedDotUpdateNotify) GetBizSuffixKey() string {
	if m != nil {
		return m.BizSuffixKey
	}
	return ""
}

// 批量清除红点
type BatchMarkRedDotReadReq struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 业务类型
	BizType uint32 `protobuf:"varint,2,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	// 业务场景key, 如ai群聊类型: 传群id
	BizSuffixKeys        []string `protobuf:"bytes,3,rep,name=biz_suffix_keys,json=bizSuffixKeys,proto3" json:"biz_suffix_keys,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchMarkRedDotReadReq) Reset()         { *m = BatchMarkRedDotReadReq{} }
func (m *BatchMarkRedDotReadReq) String() string { return proto.CompactTextString(m) }
func (*BatchMarkRedDotReadReq) ProtoMessage()    {}
func (*BatchMarkRedDotReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_logic_cbf17191f176df90, []int{6}
}
func (m *BatchMarkRedDotReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchMarkRedDotReadReq.Unmarshal(m, b)
}
func (m *BatchMarkRedDotReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchMarkRedDotReadReq.Marshal(b, m, deterministic)
}
func (dst *BatchMarkRedDotReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchMarkRedDotReadReq.Merge(dst, src)
}
func (m *BatchMarkRedDotReadReq) XXX_Size() int {
	return xxx_messageInfo_BatchMarkRedDotReadReq.Size(m)
}
func (m *BatchMarkRedDotReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchMarkRedDotReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchMarkRedDotReadReq proto.InternalMessageInfo

func (m *BatchMarkRedDotReadReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatchMarkRedDotReadReq) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *BatchMarkRedDotReadReq) GetBizSuffixKeys() []string {
	if m != nil {
		return m.BizSuffixKeys
	}
	return nil
}

type BatchMarkRedDotReadResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BatchMarkRedDotReadResp) Reset()         { *m = BatchMarkRedDotReadResp{} }
func (m *BatchMarkRedDotReadResp) String() string { return proto.CompactTextString(m) }
func (*BatchMarkRedDotReadResp) ProtoMessage()    {}
func (*BatchMarkRedDotReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_logic_cbf17191f176df90, []int{7}
}
func (m *BatchMarkRedDotReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchMarkRedDotReadResp.Unmarshal(m, b)
}
func (m *BatchMarkRedDotReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchMarkRedDotReadResp.Marshal(b, m, deterministic)
}
func (dst *BatchMarkRedDotReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchMarkRedDotReadResp.Merge(dst, src)
}
func (m *BatchMarkRedDotReadResp) XXX_Size() int {
	return xxx_messageInfo_BatchMarkRedDotReadResp.Size(m)
}
func (m *BatchMarkRedDotReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchMarkRedDotReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchMarkRedDotReadResp proto.InternalMessageInfo

func (m *BatchMarkRedDotReadResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*GetRedDotInfoReq)(nil), "ga.game_red_dot_logic.GetRedDotInfoReq")
	proto.RegisterType((*GetRedDotInfoResp)(nil), "ga.game_red_dot_logic.GetRedDotInfoResp")
	proto.RegisterMapType((map[uint32]*RedDotInfo)(nil), "ga.game_red_dot_logic.GetRedDotInfoResp.RedDotInfosEntry")
	proto.RegisterType((*RedDotInfo)(nil), "ga.game_red_dot_logic.RedDotInfo")
	proto.RegisterType((*MarkRedDotReadReq)(nil), "ga.game_red_dot_logic.MarkRedDotReadReq")
	proto.RegisterType((*MarkRedDotReadResp)(nil), "ga.game_red_dot_logic.MarkRedDotReadResp")
	proto.RegisterType((*RedDotUpdateNotify)(nil), "ga.game_red_dot_logic.RedDotUpdateNotify")
	proto.RegisterType((*BatchMarkRedDotReadReq)(nil), "ga.game_red_dot_logic.BatchMarkRedDotReadReq")
	proto.RegisterType((*BatchMarkRedDotReadResp)(nil), "ga.game_red_dot_logic.BatchMarkRedDotReadResp")
	proto.RegisterEnum("ga.game_red_dot_logic.RedDotBizType", RedDotBizType_name, RedDotBizType_value)
}

func init() {
	proto.RegisterFile("game_red_dot_logic/game_red_dot_logic.proto", fileDescriptor_game_red_dot_logic_cbf17191f176df90)
}

var fileDescriptor_game_red_dot_logic_cbf17191f176df90 = []byte{
	// 678 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x94, 0x5b, 0x6f, 0xda, 0x48,
	0x14, 0xc7, 0xd7, 0x38, 0x10, 0x38, 0xc4, 0xbb, 0xce, 0x68, 0xb3, 0x61, 0x93, 0x4a, 0x21, 0x34,
	0x8d, 0x68, 0xaa, 0x18, 0x29, 0x55, 0xd5, 0xdb, 0x43, 0xc5, 0xc5, 0xa5, 0x6e, 0xb8, 0x44, 0x03,
	0x54, 0x4d, 0xa4, 0x6a, 0x34, 0xc6, 0x03, 0xb5, 0x02, 0x8c, 0x63, 0x0f, 0x55, 0x9d, 0xd7, 0x4a,
	0xfd, 0x12, 0x95, 0x2a, 0xf5, 0x9b, 0x56, 0xb6, 0x49, 0x81, 0x00, 0x95, 0xf2, 0xd0, 0x27, 0xcf,
	0x39, 0xf3, 0x9f, 0xf3, 0xff, 0xcd, 0x39, 0xf2, 0xc0, 0xa3, 0x3e, 0x1d, 0x32, 0xe2, 0x32, 0x8b,
	0x58, 0x5c, 0x90, 0x01, 0xef, 0xdb, 0xdd, 0xc2, 0x62, 0x4a, 0x73, 0x5c, 0x2e, 0x38, 0xda, 0xea,
	0x53, 0x6d, 0x71, 0x73, 0x47, 0xe9, 0x53, 0x62, 0x52, 0x8f, 0x45, 0xaa, 0xdc, 0x08, 0xd4, 0x2a,
	0x13, 0x98, 0x59, 0x15, 0x2e, 0x8c, 0x51, 0x8f, 0x63, 0x76, 0x85, 0x0e, 0x21, 0x19, 0x28, 0x88,
	0xcb, 0xae, 0x32, 0x52, 0x56, 0xca, 0xa7, 0x4f, 0xd2, 0x5a, 0x9f, 0x6a, 0x25, 0xea, 0x31, 0xcc,
	0xae, 0xf0, 0xba, 0x19, 0x2d, 0xd0, 0x2e, 0xa4, 0x4c, 0xfb, 0x9a, 0x08, 0xdf, 0x61, 0x5e, 0x26,
	0x96, 0x95, 0xf3, 0x0a, 0x4e, 0x9a, 0xf6, 0x75, 0x3b, 0x88, 0xd1, 0x16, 0x24, 0x04, 0x35, 0x89,
	0x6d, 0x65, 0xe4, 0xac, 0x94, 0x57, 0x70, 0x5c, 0x50, 0xd3, 0xb0, 0x72, 0x3f, 0x62, 0xb0, 0x79,
	0xcb, 0xd0, 0x73, 0xd0, 0x43, 0x48, 0x4d, 0x1c, 0x3d, 0x67, 0x62, 0xb9, 0x31, 0xb5, 0xf4, 0x1c,
	0x9c, 0x34, 0x27, 0x2b, 0xf4, 0x01, 0x94, 0x9b, 0x0b, 0xd9, 0xa3, 0x1e, 0x8f, 0x8c, 0xd3, 0x27,
	0xcf, 0xb5, 0xa5, 0xd7, 0xd5, 0x16, 0xbc, 0xb4, 0x69, 0xe8, 0xe9, 0x23, 0xe1, 0xfa, 0x38, 0xed,
	0x4e, 0x33, 0x68, 0x0f, 0xd2, 0x82, 0x0b, 0x3a, 0x20, 0x5d, 0x3e, 0x1e, 0x89, 0x09, 0x3b, 0x84,
	0xa9, 0x72, 0x90, 0xd9, 0xa1, 0xa0, 0xde, 0xae, 0x80, 0x54, 0x90, 0x2f, 0x99, 0x1f, 0x82, 0x2b,
	0x38, 0x58, 0xa2, 0xa7, 0x10, 0xff, 0x44, 0x07, 0x63, 0x96, 0x89, 0x85, 0x97, 0xd9, 0x5f, 0x41,
	0x37, 0x83, 0x16, 0xe9, 0x5f, 0xc4, 0x9e, 0x49, 0xb9, 0x97, 0x00, 0xd3, 0x0d, 0xf4, 0x2f, 0xc4,
	0x23, 0x96, 0xa8, 0x7c, 0x14, 0xa0, 0x6d, 0x58, 0x1f, 0x50, 0x4f, 0x04, 0xfd, 0x0d, 0x2c, 0x64,
	0x9c, 0x08, 0x42, 0xc3, 0xca, 0x7d, 0x95, 0x60, 0xb3, 0x4e, 0xdd, 0xcb, 0xa8, 0x02, 0x66, 0xd4,
	0xba, 0xcb, 0x48, 0xff, 0x87, 0xe4, 0xcd, 0x48, 0xc3, 0xba, 0x0a, 0x5e, 0x9f, 0x4c, 0x74, 0xc5,
	0x40, 0x67, 0x41, 0xd6, 0xe6, 0x40, 0x5e, 0x01, 0xba, 0xcd, 0x71, 0xa7, 0x49, 0xe7, 0xbe, 0x49,
	0x80, 0xa2, 0xd3, 0x1d, 0xc7, 0xa2, 0x82, 0x35, 0xb8, 0xb0, 0x7b, 0xfe, 0x1c, 0xa2, 0xb4, 0x0a,
	0x31, 0x36, 0x8b, 0xf8, 0xab, 0x83, 0xf2, 0x8a, 0x0e, 0xce, 0x81, 0xa3, 0x03, 0xf8, 0x3b, 0x30,
	0xf0, 0xc6, 0xbd, 0x9e, 0xfd, 0x99, 0x04, 0x83, 0x8d, 0x67, 0xa5, 0x7c, 0x0a, 0x6f, 0x98, 0xf6,
	0x75, 0x2b, 0x4c, 0x9e, 0x32, 0x3f, 0xf7, 0x45, 0x82, 0xff, 0x4a, 0x54, 0x74, 0x3f, 0xfe, 0x91,
	0x66, 0x1f, 0xc2, 0x3f, 0xf3, 0x0c, 0x5e, 0x46, 0xce, 0xca, 0xf9, 0x14, 0x56, 0x66, 0x21, 0xbc,
	0x5c, 0x05, 0xb6, 0x97, 0x42, 0xdc, 0xa9, 0xd3, 0x47, 0xdf, 0x63, 0xa0, 0x44, 0xa7, 0x4b, 0x13,
	0xff, 0x2c, 0xdc, 0xc3, 0x7a, 0x85, 0x54, 0x9a, 0x6d, 0x52, 0x32, 0x2e, 0x48, 0xfb, 0xfc, 0x4c,
	0x27, 0x9d, 0x46, 0xeb, 0x4c, 0x2f, 0x1b, 0xaf, 0x0d, 0xbd, 0xa2, 0xfe, 0x85, 0x1e, 0xc0, 0xfe,
	0x82, 0xa2, 0x5a, 0xac, 0xeb, 0xe4, 0x4d, 0xb1, 0x56, 0x23, 0xc5, 0x36, 0xa9, 0xb7, 0xaa, 0xaa,
	0x84, 0xf2, 0x70, 0xf0, 0x1b, 0xd9, 0xdb, 0xa6, 0xd1, 0x20, 0x6d, 0xbd, 0x58, 0x57, 0x63, 0xe8,
	0x08, 0x0e, 0x97, 0x2b, 0x3b, 0x2d, 0x1d, 0x13, 0x5c, 0x6c, 0xeb, 0xa4, 0xa4, 0x07, 0x1f, 0x55,
	0x46, 0xf7, 0x61, 0x6f, 0x41, 0x5b, 0x34, 0xaa, 0x65, 0x52, 0x6e, 0xd6, 0xeb, 0x9d, 0x86, 0xd1,
	0x3e, 0x57, 0xd7, 0xd0, 0x1e, 0xec, 0x2e, 0x17, 0x55, 0x71, 0xb3, 0x73, 0xa6, 0xc6, 0x97, 0xb2,
	0x85, 0x02, 0xfd, 0x7d, 0xb9, 0xd6, 0x69, 0x19, 0xef, 0x74, 0x82, 0x9b, 0x35, 0x5d, 0x4d, 0x94,
	0x4e, 0x21, 0xd3, 0xe5, 0x43, 0xcd, 0xb7, 0x7d, 0x3e, 0x0e, 0x7a, 0x38, 0xe4, 0x16, 0x1b, 0x44,
	0x2f, 0xe8, 0x45, 0xa1, 0xcf, 0x07, 0x74, 0xd4, 0xd7, 0x9e, 0x9c, 0x08, 0xa1, 0x75, 0xf9, 0xb0,
	0x10, 0xa6, 0xbb, 0x7c, 0x50, 0xa0, 0x8e, 0x13, 0x3e, 0xcf, 0xc7, 0x2e, 0xb3, 0x8e, 0x2d, 0x2e,
	0x8e, 0xc3, 0x9f, 0xde, 0x4c, 0x84, 0x82, 0xc7, 0x3f, 0x03, 0x00, 0x00, 0xff, 0xff, 0x97, 0x44,
	0x02, 0xc8, 0xce, 0x05, 0x00, 0x00,
}
