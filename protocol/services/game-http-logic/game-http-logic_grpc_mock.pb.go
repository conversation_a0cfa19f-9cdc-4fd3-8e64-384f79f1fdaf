// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/game-http-logic/game-http-logic.proto

package game_http_logic

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockGameHttpLogicClient is a mock of GameHttpLogicClient interface.
type MockGameHttpLogicClient struct {
	ctrl     *gomock.Controller
	recorder *MockGameHttpLogicClientMockRecorder
}

// MockGameHttpLogicClientMockRecorder is the mock recorder for MockGameHttpLogicClient.
type MockGameHttpLogicClientMockRecorder struct {
	mock *MockGameHttpLogicClient
}

// NewMockGameHttpLogicClient creates a new mock instance.
func NewMockGameHttpLogicClient(ctrl *gomock.Controller) *MockGameHttpLogicClient {
	mock := &MockGameHttpLogicClient{ctrl: ctrl}
	mock.recorder = &MockGameHttpLogicClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGameHttpLogicClient) EXPECT() *MockGameHttpLogicClientMockRecorder {
	return m.recorder
}

// GetAggregateRedDotInfo mocks base method.
func (m *MockGameHttpLogicClient) GetAggregateRedDotInfo(ctx context.Context, in *GetAggregateRedDotInfoReq, opts ...grpc.CallOption) (*GetAggregateRedDotInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAggregateRedDotInfo", varargs...)
	ret0, _ := ret[0].(*GetAggregateRedDotInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAggregateRedDotInfo indicates an expected call of GetAggregateRedDotInfo.
func (mr *MockGameHttpLogicClientMockRecorder) GetAggregateRedDotInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAggregateRedDotInfo", reflect.TypeOf((*MockGameHttpLogicClient)(nil).GetAggregateRedDotInfo), varargs...)
}

// GetGameUserBeRateList mocks base method.
func (m *MockGameHttpLogicClient) GetGameUserBeRateList(ctx context.Context, in *GetGameUserBeRateListReq, opts ...grpc.CallOption) (*GetGameUserBeRateListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameUserBeRateList", varargs...)
	ret0, _ := ret[0].(*GetGameUserBeRateListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserBeRateList indicates an expected call of GetGameUserBeRateList.
func (mr *MockGameHttpLogicClientMockRecorder) GetGameUserBeRateList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserBeRateList", reflect.TypeOf((*MockGameHttpLogicClient)(nil).GetGameUserBeRateList), varargs...)
}

// GetGameUserRateList mocks base method.
func (m *MockGameHttpLogicClient) GetGameUserRateList(ctx context.Context, in *GetGameUserRateListReq, opts ...grpc.CallOption) (*GetGameUserRateListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameUserRateList", varargs...)
	ret0, _ := ret[0].(*GetGameUserRateListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserRateList indicates an expected call of GetGameUserRateList.
func (mr *MockGameHttpLogicClientMockRecorder) GetGameUserRateList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserRateList", reflect.TypeOf((*MockGameHttpLogicClient)(nil).GetGameUserRateList), varargs...)
}

// GetRedDotInfo mocks base method.
func (m *MockGameHttpLogicClient) GetRedDotInfo(ctx context.Context, in *GetRedDotInfoReq, opts ...grpc.CallOption) (*GetRedDotInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRedDotInfo", varargs...)
	ret0, _ := ret[0].(*GetRedDotInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRedDotInfo indicates an expected call of GetRedDotInfo.
func (mr *MockGameHttpLogicClientMockRecorder) GetRedDotInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedDotInfo", reflect.TypeOf((*MockGameHttpLogicClient)(nil).GetRedDotInfo), varargs...)
}

// GetSelfRateQuestions mocks base method.
func (m *MockGameHttpLogicClient) GetSelfRateQuestions(ctx context.Context, in *GetSelfRateQuestionsReq, opts ...grpc.CallOption) (*GetSelfRateQuestionsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSelfRateQuestions", varargs...)
	ret0, _ := ret[0].(*GetSelfRateQuestionsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSelfRateQuestions indicates an expected call of GetSelfRateQuestions.
func (mr *MockGameHttpLogicClientMockRecorder) GetSelfRateQuestions(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSelfRateQuestions", reflect.TypeOf((*MockGameHttpLogicClient)(nil).GetSelfRateQuestions), varargs...)
}

// GetUserImageRateResult mocks base method.
func (m *MockGameHttpLogicClient) GetUserImageRateResult(ctx context.Context, in *GetUserImageRateResultReq, opts ...grpc.CallOption) (*GameUserPersonalImageRespItem, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserImageRateResult", varargs...)
	ret0, _ := ret[0].(*GameUserPersonalImageRespItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserImageRateResult indicates an expected call of GetUserImageRateResult.
func (mr *MockGameHttpLogicClientMockRecorder) GetUserImageRateResult(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserImageRateResult", reflect.TypeOf((*MockGameHttpLogicClient)(nil).GetUserImageRateResult), varargs...)
}

// GetUserInfo mocks base method.
func (m *MockGameHttpLogicClient) GetUserInfo(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserInfo", varargs...)
	ret0, _ := ret[0].(*GetUserInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInfo indicates an expected call of GetUserInfo.
func (mr *MockGameHttpLogicClientMockRecorder) GetUserInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInfo", reflect.TypeOf((*MockGameHttpLogicClient)(nil).GetUserInfo), varargs...)
}

// GetUserInfos mocks base method.
func (m *MockGameHttpLogicClient) GetUserInfos(ctx context.Context, in *GetUserInfosReq, opts ...grpc.CallOption) (*GetUserInfosResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserInfos", varargs...)
	ret0, _ := ret[0].(*GetUserInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInfos indicates an expected call of GetUserInfos.
func (mr *MockGameHttpLogicClientMockRecorder) GetUserInfos(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInfos", reflect.TypeOf((*MockGameHttpLogicClient)(nil).GetUserInfos), varargs...)
}

// MarkAggregateRedDotRead mocks base method.
func (m *MockGameHttpLogicClient) MarkAggregateRedDotRead(ctx context.Context, in *MarkAggregateRedDotReadReq, opts ...grpc.CallOption) (*MarkAggregateRedDotReadResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MarkAggregateRedDotRead", varargs...)
	ret0, _ := ret[0].(*MarkAggregateRedDotReadResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MarkAggregateRedDotRead indicates an expected call of MarkAggregateRedDotRead.
func (mr *MockGameHttpLogicClientMockRecorder) MarkAggregateRedDotRead(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkAggregateRedDotRead", reflect.TypeOf((*MockGameHttpLogicClient)(nil).MarkAggregateRedDotRead), varargs...)
}

// MarkRedDotRead mocks base method.
func (m *MockGameHttpLogicClient) MarkRedDotRead(ctx context.Context, in *MarkRedDotReadReq, opts ...grpc.CallOption) (*MarkRedDotReadResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MarkRedDotRead", varargs...)
	ret0, _ := ret[0].(*MarkRedDotReadResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MarkRedDotRead indicates an expected call of MarkRedDotRead.
func (mr *MockGameHttpLogicClientMockRecorder) MarkRedDotRead(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkRedDotRead", reflect.TypeOf((*MockGameHttpLogicClient)(nil).MarkRedDotRead), varargs...)
}

// ReorderRateTags mocks base method.
func (m *MockGameHttpLogicClient) ReorderRateTags(ctx context.Context, in *ReorderRateTagsReq, opts ...grpc.CallOption) (*ReorderRateTagsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReorderRateTags", varargs...)
	ret0, _ := ret[0].(*ReorderRateTagsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReorderRateTags indicates an expected call of ReorderRateTags.
func (mr *MockGameHttpLogicClientMockRecorder) ReorderRateTags(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReorderRateTags", reflect.TypeOf((*MockGameHttpLogicClient)(nil).ReorderRateTags), varargs...)
}

// SubmitGameUserRate mocks base method.
func (m *MockGameHttpLogicClient) SubmitGameUserRate(ctx context.Context, in *SubmitGameUserRateReq, opts ...grpc.CallOption) (*SubmitGameUserRateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitGameUserRate", varargs...)
	ret0, _ := ret[0].(*SubmitGameUserRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitGameUserRate indicates an expected call of SubmitGameUserRate.
func (mr *MockGameHttpLogicClientMockRecorder) SubmitGameUserRate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitGameUserRate", reflect.TypeOf((*MockGameHttpLogicClient)(nil).SubmitGameUserRate), varargs...)
}

// SubmitSelfRateResult mocks base method.
func (m *MockGameHttpLogicClient) SubmitSelfRateResult(ctx context.Context, in *SubmitSelfRateResultReq, opts ...grpc.CallOption) (*SubmitGameUserRateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitSelfRateResult", varargs...)
	ret0, _ := ret[0].(*SubmitGameUserRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitSelfRateResult indicates an expected call of SubmitSelfRateResult.
func (mr *MockGameHttpLogicClientMockRecorder) SubmitSelfRateResult(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitSelfRateResult", reflect.TypeOf((*MockGameHttpLogicClient)(nil).SubmitSelfRateResult), varargs...)
}

// MockGameHttpLogicServer is a mock of GameHttpLogicServer interface.
type MockGameHttpLogicServer struct {
	ctrl     *gomock.Controller
	recorder *MockGameHttpLogicServerMockRecorder
}

// MockGameHttpLogicServerMockRecorder is the mock recorder for MockGameHttpLogicServer.
type MockGameHttpLogicServerMockRecorder struct {
	mock *MockGameHttpLogicServer
}

// NewMockGameHttpLogicServer creates a new mock instance.
func NewMockGameHttpLogicServer(ctrl *gomock.Controller) *MockGameHttpLogicServer {
	mock := &MockGameHttpLogicServer{ctrl: ctrl}
	mock.recorder = &MockGameHttpLogicServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGameHttpLogicServer) EXPECT() *MockGameHttpLogicServerMockRecorder {
	return m.recorder
}

// GetAggregateRedDotInfo mocks base method.
func (m *MockGameHttpLogicServer) GetAggregateRedDotInfo(ctx context.Context, in *GetAggregateRedDotInfoReq) (*GetAggregateRedDotInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAggregateRedDotInfo", ctx, in)
	ret0, _ := ret[0].(*GetAggregateRedDotInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAggregateRedDotInfo indicates an expected call of GetAggregateRedDotInfo.
func (mr *MockGameHttpLogicServerMockRecorder) GetAggregateRedDotInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAggregateRedDotInfo", reflect.TypeOf((*MockGameHttpLogicServer)(nil).GetAggregateRedDotInfo), ctx, in)
}

// GetGameUserBeRateList mocks base method.
func (m *MockGameHttpLogicServer) GetGameUserBeRateList(ctx context.Context, in *GetGameUserBeRateListReq) (*GetGameUserBeRateListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameUserBeRateList", ctx, in)
	ret0, _ := ret[0].(*GetGameUserBeRateListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserBeRateList indicates an expected call of GetGameUserBeRateList.
func (mr *MockGameHttpLogicServerMockRecorder) GetGameUserBeRateList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserBeRateList", reflect.TypeOf((*MockGameHttpLogicServer)(nil).GetGameUserBeRateList), ctx, in)
}

// GetGameUserRateList mocks base method.
func (m *MockGameHttpLogicServer) GetGameUserRateList(ctx context.Context, in *GetGameUserRateListReq) (*GetGameUserRateListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameUserRateList", ctx, in)
	ret0, _ := ret[0].(*GetGameUserRateListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserRateList indicates an expected call of GetGameUserRateList.
func (mr *MockGameHttpLogicServerMockRecorder) GetGameUserRateList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserRateList", reflect.TypeOf((*MockGameHttpLogicServer)(nil).GetGameUserRateList), ctx, in)
}

// GetRedDotInfo mocks base method.
func (m *MockGameHttpLogicServer) GetRedDotInfo(ctx context.Context, in *GetRedDotInfoReq) (*GetRedDotInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedDotInfo", ctx, in)
	ret0, _ := ret[0].(*GetRedDotInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRedDotInfo indicates an expected call of GetRedDotInfo.
func (mr *MockGameHttpLogicServerMockRecorder) GetRedDotInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedDotInfo", reflect.TypeOf((*MockGameHttpLogicServer)(nil).GetRedDotInfo), ctx, in)
}

// GetSelfRateQuestions mocks base method.
func (m *MockGameHttpLogicServer) GetSelfRateQuestions(ctx context.Context, in *GetSelfRateQuestionsReq) (*GetSelfRateQuestionsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSelfRateQuestions", ctx, in)
	ret0, _ := ret[0].(*GetSelfRateQuestionsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSelfRateQuestions indicates an expected call of GetSelfRateQuestions.
func (mr *MockGameHttpLogicServerMockRecorder) GetSelfRateQuestions(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSelfRateQuestions", reflect.TypeOf((*MockGameHttpLogicServer)(nil).GetSelfRateQuestions), ctx, in)
}

// GetUserImageRateResult mocks base method.
func (m *MockGameHttpLogicServer) GetUserImageRateResult(ctx context.Context, in *GetUserImageRateResultReq) (*GameUserPersonalImageRespItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserImageRateResult", ctx, in)
	ret0, _ := ret[0].(*GameUserPersonalImageRespItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserImageRateResult indicates an expected call of GetUserImageRateResult.
func (mr *MockGameHttpLogicServerMockRecorder) GetUserImageRateResult(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserImageRateResult", reflect.TypeOf((*MockGameHttpLogicServer)(nil).GetUserImageRateResult), ctx, in)
}

// GetUserInfo mocks base method.
func (m *MockGameHttpLogicServer) GetUserInfo(ctx context.Context, in *GetUserInfoReq) (*GetUserInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInfo", ctx, in)
	ret0, _ := ret[0].(*GetUserInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInfo indicates an expected call of GetUserInfo.
func (mr *MockGameHttpLogicServerMockRecorder) GetUserInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInfo", reflect.TypeOf((*MockGameHttpLogicServer)(nil).GetUserInfo), ctx, in)
}

// GetUserInfos mocks base method.
func (m *MockGameHttpLogicServer) GetUserInfos(ctx context.Context, in *GetUserInfosReq) (*GetUserInfosResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInfos", ctx, in)
	ret0, _ := ret[0].(*GetUserInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInfos indicates an expected call of GetUserInfos.
func (mr *MockGameHttpLogicServerMockRecorder) GetUserInfos(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInfos", reflect.TypeOf((*MockGameHttpLogicServer)(nil).GetUserInfos), ctx, in)
}

// MarkAggregateRedDotRead mocks base method.
func (m *MockGameHttpLogicServer) MarkAggregateRedDotRead(ctx context.Context, in *MarkAggregateRedDotReadReq) (*MarkAggregateRedDotReadResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkAggregateRedDotRead", ctx, in)
	ret0, _ := ret[0].(*MarkAggregateRedDotReadResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MarkAggregateRedDotRead indicates an expected call of MarkAggregateRedDotRead.
func (mr *MockGameHttpLogicServerMockRecorder) MarkAggregateRedDotRead(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkAggregateRedDotRead", reflect.TypeOf((*MockGameHttpLogicServer)(nil).MarkAggregateRedDotRead), ctx, in)
}

// MarkRedDotRead mocks base method.
func (m *MockGameHttpLogicServer) MarkRedDotRead(ctx context.Context, in *MarkRedDotReadReq) (*MarkRedDotReadResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkRedDotRead", ctx, in)
	ret0, _ := ret[0].(*MarkRedDotReadResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MarkRedDotRead indicates an expected call of MarkRedDotRead.
func (mr *MockGameHttpLogicServerMockRecorder) MarkRedDotRead(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkRedDotRead", reflect.TypeOf((*MockGameHttpLogicServer)(nil).MarkRedDotRead), ctx, in)
}

// ReorderRateTags mocks base method.
func (m *MockGameHttpLogicServer) ReorderRateTags(ctx context.Context, in *ReorderRateTagsReq) (*ReorderRateTagsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReorderRateTags", ctx, in)
	ret0, _ := ret[0].(*ReorderRateTagsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReorderRateTags indicates an expected call of ReorderRateTags.
func (mr *MockGameHttpLogicServerMockRecorder) ReorderRateTags(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReorderRateTags", reflect.TypeOf((*MockGameHttpLogicServer)(nil).ReorderRateTags), ctx, in)
}

// SubmitGameUserRate mocks base method.
func (m *MockGameHttpLogicServer) SubmitGameUserRate(ctx context.Context, in *SubmitGameUserRateReq) (*SubmitGameUserRateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitGameUserRate", ctx, in)
	ret0, _ := ret[0].(*SubmitGameUserRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitGameUserRate indicates an expected call of SubmitGameUserRate.
func (mr *MockGameHttpLogicServerMockRecorder) SubmitGameUserRate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitGameUserRate", reflect.TypeOf((*MockGameHttpLogicServer)(nil).SubmitGameUserRate), ctx, in)
}

// SubmitSelfRateResult mocks base method.
func (m *MockGameHttpLogicServer) SubmitSelfRateResult(ctx context.Context, in *SubmitSelfRateResultReq) (*SubmitGameUserRateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitSelfRateResult", ctx, in)
	ret0, _ := ret[0].(*SubmitGameUserRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitSelfRateResult indicates an expected call of SubmitSelfRateResult.
func (mr *MockGameHttpLogicServerMockRecorder) SubmitSelfRateResult(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitSelfRateResult", reflect.TypeOf((*MockGameHttpLogicServer)(nil).SubmitSelfRateResult), ctx, in)
}
