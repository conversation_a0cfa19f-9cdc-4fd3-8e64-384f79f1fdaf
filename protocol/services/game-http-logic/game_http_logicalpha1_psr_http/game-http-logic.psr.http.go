// Code generated by protoc-gen-psr-http-go. DO NOT EDIT.
//
// Source: tt/quicksilver/game-http-logic/game-http-logic.proto

package game_http_logicalpha1_psr_http

import (
	context "context"
	errors "errors"
	psr_http "gitlab.ttyuyin.com/hyperion-ecosystem/psr-http"
	game_http_logic "golang.52tt.com/protocol/services/game-http-logic"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = psr_http.IsAtLeastVersion1_17_0

const (
	// GameHttpLogicName is the fully-qualified name of the GameHttpLogic service.
	GameHttpLogicName = "game_http_logic.GameHttpLogic"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// GameHttpLogicGetSelfRateQuestionsProcedure is the fully-qualified name of the GameHttpLogic's
	// GetSelfRateQuestions RPC.
	GameHttpLogicGetSelfRateQuestionsProcedure = "/game-http-logic/user-rate/getSelfRateQuestions"
	// GameHttpLogicSubmitSelfRateResultProcedure is the fully-qualified name of the GameHttpLogic's
	// SubmitSelfRateResult RPC.
	GameHttpLogicSubmitSelfRateResultProcedure = "/game-http-logic/user-rate/submitSelfRateResult"
	// GameHttpLogicGetUserImageRateResultProcedure is the fully-qualified name of the GameHttpLogic's
	// GetUserImageRateResult RPC.
	GameHttpLogicGetUserImageRateResultProcedure = "/game-http-logic/user-rate/getUserImageRateResult"
	// GameHttpLogicGetUserInfoProcedure is the fully-qualified name of the GameHttpLogic's GetUserInfo
	// RPC.
	GameHttpLogicGetUserInfoProcedure = "/game-http-logic/game-account/getUserInfo"
	// GameHttpLogicGetGameUserBeRateListProcedure is the fully-qualified name of the GameHttpLogic's
	// GetGameUserBeRateList RPC.
	GameHttpLogicGetGameUserBeRateListProcedure = "/game-http-logic/user-rate/getGameUserBeRateList"
	// GameHttpLogicGetGameUserRateListProcedure is the fully-qualified name of the GameHttpLogic's
	// GetGameUserRateList RPC.
	GameHttpLogicGetGameUserRateListProcedure = "/game-http-logic/user-rate/getGameUserRateList"
	// GameHttpLogicSubmitGameUserRateProcedure is the fully-qualified name of the GameHttpLogic's
	// SubmitGameUserRate RPC.
	GameHttpLogicSubmitGameUserRateProcedure = "/game-http-logic/user-rate/submitGameUserRate"
	// GameHttpLogicGetRedDotInfoProcedure is the fully-qualified name of the GameHttpLogic's
	// GetRedDotInfo RPC.
	GameHttpLogicGetRedDotInfoProcedure = "/game-http-logic/red-dot/getRedDotInfo"
	// GameHttpLogicMarkRedDotReadProcedure is the fully-qualified name of the GameHttpLogic's
	// MarkRedDotRead RPC.
	GameHttpLogicMarkRedDotReadProcedure = "/game-http-logic/red-dot/markRedDotRead"
	// GameHttpLogicGetAggregateRedDotInfoProcedure is the fully-qualified name of the GameHttpLogic's
	// GetAggregateRedDotInfo RPC.
	GameHttpLogicGetAggregateRedDotInfoProcedure = "/game-http-logic/red-dot/getAggregateRedDotInfo"
	// GameHttpLogicMarkAggregateRedDotReadProcedure is the fully-qualified name of the GameHttpLogic's
	// MarkAggregateRedDotRead RPC.
	GameHttpLogicMarkAggregateRedDotReadProcedure = "/game-http-logic/red-dot/markAggregateRedDotRead"
	// GameHttpLogicGetUserInfosProcedure is the fully-qualified name of the GameHttpLogic's
	// GetUserInfos RPC.
	GameHttpLogicGetUserInfosProcedure = "/game-http-logic/game-account/getUserInfos"
	// GameHttpLogicReorderRateTagsProcedure is the fully-qualified name of the GameHttpLogic's
	// ReorderRateTags RPC.
	GameHttpLogicReorderRateTagsProcedure = "/game-http-logic/user-rate/reorderRateTags"
)

// These variables are the protoreflect.Descriptor objects for the RPCs defined in this package.
var (
	gameHttpLogicServiceDescriptor                       = game_http_logic.File_tt_quicksilver_game_http_logic_game_http_logic_proto.Services().ByName("GameHttpLogic")
	gameHttpLogicGetSelfRateQuestionsMethodDescriptor    = gameHttpLogicServiceDescriptor.Methods().ByName("GetSelfRateQuestions")
	gameHttpLogicSubmitSelfRateResultMethodDescriptor    = gameHttpLogicServiceDescriptor.Methods().ByName("SubmitSelfRateResult")
	gameHttpLogicGetUserImageRateResultMethodDescriptor  = gameHttpLogicServiceDescriptor.Methods().ByName("GetUserImageRateResult")
	gameHttpLogicGetUserInfoMethodDescriptor             = gameHttpLogicServiceDescriptor.Methods().ByName("GetUserInfo")
	gameHttpLogicGetGameUserBeRateListMethodDescriptor   = gameHttpLogicServiceDescriptor.Methods().ByName("GetGameUserBeRateList")
	gameHttpLogicGetGameUserRateListMethodDescriptor     = gameHttpLogicServiceDescriptor.Methods().ByName("GetGameUserRateList")
	gameHttpLogicSubmitGameUserRateMethodDescriptor      = gameHttpLogicServiceDescriptor.Methods().ByName("SubmitGameUserRate")
	gameHttpLogicGetRedDotInfoMethodDescriptor           = gameHttpLogicServiceDescriptor.Methods().ByName("GetRedDotInfo")
	gameHttpLogicMarkRedDotReadMethodDescriptor          = gameHttpLogicServiceDescriptor.Methods().ByName("MarkRedDotRead")
	gameHttpLogicGetAggregateRedDotInfoMethodDescriptor  = gameHttpLogicServiceDescriptor.Methods().ByName("GetAggregateRedDotInfo")
	gameHttpLogicMarkAggregateRedDotReadMethodDescriptor = gameHttpLogicServiceDescriptor.Methods().ByName("MarkAggregateRedDotRead")
	gameHttpLogicGetUserInfosMethodDescriptor            = gameHttpLogicServiceDescriptor.Methods().ByName("GetUserInfos")
	gameHttpLogicReorderRateTagsMethodDescriptor         = gameHttpLogicServiceDescriptor.Methods().ByName("ReorderRateTags")
)

// GameHttpLogicPSRHttpClientPost is a client for the game_http_logic.GameHttpLogic service.
type GameHttpLogicPSRHttpClientPost interface {
	// 获取开黑形象自评题目
	GetSelfRateQuestions(context.Context, *game_http_logic.GetSelfRateQuestionsReq) (*game_http_logic.GetSelfRateQuestionsResp, error)
	// 提交开黑形象自评结果
	SubmitSelfRateResult(context.Context, *game_http_logic.SubmitSelfRateResultReq) (*game_http_logic.SubmitGameUserRateResp, error)
	// 获取用户测评形象结果
	GetUserImageRateResult(context.Context, *game_http_logic.GetUserImageRateResultReq) (*game_http_logic.GameUserPersonalImageRespItem, error)
	// 获取用户信息
	GetUserInfo(context.Context, *game_http_logic.GetUserInfoReq) (*game_http_logic.GetUserInfoResp, error)
	// 获取用户被评列表
	GetGameUserBeRateList(context.Context, *game_http_logic.GetGameUserBeRateListReq) (*game_http_logic.GetGameUserBeRateListResp, error)
	// 获取用户评价列表
	GetGameUserRateList(context.Context, *game_http_logic.GetGameUserRateListReq) (*game_http_logic.GetGameUserRateListResp, error)
	// 提交用户评价
	SubmitGameUserRate(context.Context, *game_http_logic.SubmitGameUserRateReq) (*game_http_logic.SubmitGameUserRateResp, error)
	// 获取红点信息
	GetRedDotInfo(context.Context, *game_http_logic.GetRedDotInfoReq) (*game_http_logic.GetRedDotInfoResp, error)
	// 标记红点已读
	MarkRedDotRead(context.Context, *game_http_logic.MarkRedDotReadReq) (*game_http_logic.MarkRedDotReadResp, error)
	// 获取聚合红点信息
	GetAggregateRedDotInfo(context.Context, *game_http_logic.GetAggregateRedDotInfoReq) (*game_http_logic.GetAggregateRedDotInfoResp, error)
	// 标记聚合红点已读
	MarkAggregateRedDotRead(context.Context, *game_http_logic.MarkAggregateRedDotReadReq) (*game_http_logic.MarkAggregateRedDotReadResp, error)
	// 获取用户信息
	GetUserInfos(context.Context, *game_http_logic.GetUserInfosReq) (*game_http_logic.GetUserInfosResp, error)
	// 获取推荐评价标签
	ReorderRateTags(context.Context, *game_http_logic.ReorderRateTagsReq) (*game_http_logic.ReorderRateTagsResp, error)
}

// GameHttpLogicPSRHttpClientGet is a client for the game_http_logic.GameHttpLogic service.
type GameHttpLogicPSRHttpClientGet interface {
}

// NewGameHttpLogicPSRHttpClientPost constructs a client for the game_http_logic.GameHttpLogic
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the psrhttp.WithGRPC() or psrhttp.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewGameHttpLogicPSRHttpClientPost(httpClient psr_http.HTTPClient, baseURL string, opts ...psr_http.ClientOption) GameHttpLogicPSRHttpClientPost {
	baseURL = strings.TrimRight(baseURL, "/")
	return &gameHttpLogicPSRHttpClientPost{
		getSelfRateQuestions: psr_http.NewClient[game_http_logic.GetSelfRateQuestionsReq, game_http_logic.GetSelfRateQuestionsResp](
			httpClient,
			baseURL,
			GameHttpLogicGetSelfRateQuestionsProcedure,
			psr_http.WithSchema(gameHttpLogicGetSelfRateQuestionsMethodDescriptor),
			psr_http.WithProtoJSON(),
			psr_http.WithClientOptions(opts...),
		),
		submitSelfRateResult: psr_http.NewClient[game_http_logic.SubmitSelfRateResultReq, game_http_logic.SubmitGameUserRateResp](
			httpClient,
			baseURL,
			GameHttpLogicSubmitSelfRateResultProcedure,
			psr_http.WithSchema(gameHttpLogicSubmitSelfRateResultMethodDescriptor),
			psr_http.WithProtoJSON(),
			psr_http.WithClientOptions(opts...),
		),
		getUserImageRateResult: psr_http.NewClient[game_http_logic.GetUserImageRateResultReq, game_http_logic.GameUserPersonalImageRespItem](
			httpClient,
			baseURL,
			GameHttpLogicGetUserImageRateResultProcedure,
			psr_http.WithSchema(gameHttpLogicGetUserImageRateResultMethodDescriptor),
			psr_http.WithProtoJSON(),
			psr_http.WithClientOptions(opts...),
		),
		getUserInfo: psr_http.NewClient[game_http_logic.GetUserInfoReq, game_http_logic.GetUserInfoResp](
			httpClient,
			baseURL,
			GameHttpLogicGetUserInfoProcedure,
			psr_http.WithSchema(gameHttpLogicGetUserInfoMethodDescriptor),
			psr_http.WithProtoJSON(),
			psr_http.WithClientOptions(opts...),
		),
		getGameUserBeRateList: psr_http.NewClient[game_http_logic.GetGameUserBeRateListReq, game_http_logic.GetGameUserBeRateListResp](
			httpClient,
			baseURL,
			GameHttpLogicGetGameUserBeRateListProcedure,
			psr_http.WithSchema(gameHttpLogicGetGameUserBeRateListMethodDescriptor),
			psr_http.WithProtoJSON(),
			psr_http.WithClientOptions(opts...),
		),
		getGameUserRateList: psr_http.NewClient[game_http_logic.GetGameUserRateListReq, game_http_logic.GetGameUserRateListResp](
			httpClient,
			baseURL,
			GameHttpLogicGetGameUserRateListProcedure,
			psr_http.WithSchema(gameHttpLogicGetGameUserRateListMethodDescriptor),
			psr_http.WithProtoJSON(),
			psr_http.WithClientOptions(opts...),
		),
		submitGameUserRate: psr_http.NewClient[game_http_logic.SubmitGameUserRateReq, game_http_logic.SubmitGameUserRateResp](
			httpClient,
			baseURL,
			GameHttpLogicSubmitGameUserRateProcedure,
			psr_http.WithSchema(gameHttpLogicSubmitGameUserRateMethodDescriptor),
			psr_http.WithProtoJSON(),
			psr_http.WithClientOptions(opts...),
		),
		getRedDotInfo: psr_http.NewClient[game_http_logic.GetRedDotInfoReq, game_http_logic.GetRedDotInfoResp](
			httpClient,
			baseURL,
			GameHttpLogicGetRedDotInfoProcedure,
			psr_http.WithSchema(gameHttpLogicGetRedDotInfoMethodDescriptor),
			psr_http.WithProtoJSON(),
			psr_http.WithClientOptions(opts...),
		),
		markRedDotRead: psr_http.NewClient[game_http_logic.MarkRedDotReadReq, game_http_logic.MarkRedDotReadResp](
			httpClient,
			baseURL,
			GameHttpLogicMarkRedDotReadProcedure,
			psr_http.WithSchema(gameHttpLogicMarkRedDotReadMethodDescriptor),
			psr_http.WithProtoJSON(),
			psr_http.WithClientOptions(opts...),
		),
		getAggregateRedDotInfo: psr_http.NewClient[game_http_logic.GetAggregateRedDotInfoReq, game_http_logic.GetAggregateRedDotInfoResp](
			httpClient,
			baseURL,
			GameHttpLogicGetAggregateRedDotInfoProcedure,
			psr_http.WithSchema(gameHttpLogicGetAggregateRedDotInfoMethodDescriptor),
			psr_http.WithProtoJSON(),
			psr_http.WithClientOptions(opts...),
		),
		markAggregateRedDotRead: psr_http.NewClient[game_http_logic.MarkAggregateRedDotReadReq, game_http_logic.MarkAggregateRedDotReadResp](
			httpClient,
			baseURL,
			GameHttpLogicMarkAggregateRedDotReadProcedure,
			psr_http.WithSchema(gameHttpLogicMarkAggregateRedDotReadMethodDescriptor),
			psr_http.WithProtoJSON(),
			psr_http.WithClientOptions(opts...),
		),
		getUserInfos: psr_http.NewClient[game_http_logic.GetUserInfosReq, game_http_logic.GetUserInfosResp](
			httpClient,
			baseURL,
			GameHttpLogicGetUserInfosProcedure,
			psr_http.WithSchema(gameHttpLogicGetUserInfosMethodDescriptor),
			psr_http.WithProtoJSON(),
			psr_http.WithClientOptions(opts...),
		),
		reorderRateTags: psr_http.NewClient[game_http_logic.ReorderRateTagsReq, game_http_logic.ReorderRateTagsResp](
			httpClient,
			baseURL,
			GameHttpLogicReorderRateTagsProcedure,
			psr_http.WithSchema(gameHttpLogicReorderRateTagsMethodDescriptor),
			psr_http.WithProtoJSON(),
			psr_http.WithClientOptions(opts...),
		),
	}
}

// gameHttpLogicPSRHttpClientPost implements GameHttpLogicPSRHttpClientPost.
type gameHttpLogicPSRHttpClientPost struct {
	getSelfRateQuestions    *psr_http.Client[game_http_logic.GetSelfRateQuestionsReq, game_http_logic.GetSelfRateQuestionsResp]
	submitSelfRateResult    *psr_http.Client[game_http_logic.SubmitSelfRateResultReq, game_http_logic.SubmitGameUserRateResp]
	getUserImageRateResult  *psr_http.Client[game_http_logic.GetUserImageRateResultReq, game_http_logic.GameUserPersonalImageRespItem]
	getUserInfo             *psr_http.Client[game_http_logic.GetUserInfoReq, game_http_logic.GetUserInfoResp]
	getGameUserBeRateList   *psr_http.Client[game_http_logic.GetGameUserBeRateListReq, game_http_logic.GetGameUserBeRateListResp]
	getGameUserRateList     *psr_http.Client[game_http_logic.GetGameUserRateListReq, game_http_logic.GetGameUserRateListResp]
	submitGameUserRate      *psr_http.Client[game_http_logic.SubmitGameUserRateReq, game_http_logic.SubmitGameUserRateResp]
	getRedDotInfo           *psr_http.Client[game_http_logic.GetRedDotInfoReq, game_http_logic.GetRedDotInfoResp]
	markRedDotRead          *psr_http.Client[game_http_logic.MarkRedDotReadReq, game_http_logic.MarkRedDotReadResp]
	getAggregateRedDotInfo  *psr_http.Client[game_http_logic.GetAggregateRedDotInfoReq, game_http_logic.GetAggregateRedDotInfoResp]
	markAggregateRedDotRead *psr_http.Client[game_http_logic.MarkAggregateRedDotReadReq, game_http_logic.MarkAggregateRedDotReadResp]
	getUserInfos            *psr_http.Client[game_http_logic.GetUserInfosReq, game_http_logic.GetUserInfosResp]
	reorderRateTags         *psr_http.Client[game_http_logic.ReorderRateTagsReq, game_http_logic.ReorderRateTagsResp]
}

// GetSelfRateQuestions calls game_http_logic.GameHttpLogic.GetSelfRateQuestions.
func (c *gameHttpLogicPSRHttpClientPost) GetSelfRateQuestions(ctx context.Context, req *game_http_logic.GetSelfRateQuestionsReq) (*game_http_logic.GetSelfRateQuestionsResp, error) {
	return c.getSelfRateQuestions.CallUnary(ctx, req)
}

// SubmitSelfRateResult calls game_http_logic.GameHttpLogic.SubmitSelfRateResult.
func (c *gameHttpLogicPSRHttpClientPost) SubmitSelfRateResult(ctx context.Context, req *game_http_logic.SubmitSelfRateResultReq) (*game_http_logic.SubmitGameUserRateResp, error) {
	return c.submitSelfRateResult.CallUnary(ctx, req)
}

// GetUserImageRateResult calls game_http_logic.GameHttpLogic.GetUserImageRateResult.
func (c *gameHttpLogicPSRHttpClientPost) GetUserImageRateResult(ctx context.Context, req *game_http_logic.GetUserImageRateResultReq) (*game_http_logic.GameUserPersonalImageRespItem, error) {
	return c.getUserImageRateResult.CallUnary(ctx, req)
}

// GetUserInfo calls game_http_logic.GameHttpLogic.GetUserInfo.
func (c *gameHttpLogicPSRHttpClientPost) GetUserInfo(ctx context.Context, req *game_http_logic.GetUserInfoReq) (*game_http_logic.GetUserInfoResp, error) {
	return c.getUserInfo.CallUnary(ctx, req)
}

// GetGameUserBeRateList calls game_http_logic.GameHttpLogic.GetGameUserBeRateList.
func (c *gameHttpLogicPSRHttpClientPost) GetGameUserBeRateList(ctx context.Context, req *game_http_logic.GetGameUserBeRateListReq) (*game_http_logic.GetGameUserBeRateListResp, error) {
	return c.getGameUserBeRateList.CallUnary(ctx, req)
}

// GetGameUserRateList calls game_http_logic.GameHttpLogic.GetGameUserRateList.
func (c *gameHttpLogicPSRHttpClientPost) GetGameUserRateList(ctx context.Context, req *game_http_logic.GetGameUserRateListReq) (*game_http_logic.GetGameUserRateListResp, error) {
	return c.getGameUserRateList.CallUnary(ctx, req)
}

// SubmitGameUserRate calls game_http_logic.GameHttpLogic.SubmitGameUserRate.
func (c *gameHttpLogicPSRHttpClientPost) SubmitGameUserRate(ctx context.Context, req *game_http_logic.SubmitGameUserRateReq) (*game_http_logic.SubmitGameUserRateResp, error) {
	return c.submitGameUserRate.CallUnary(ctx, req)
}

// GetRedDotInfo calls game_http_logic.GameHttpLogic.GetRedDotInfo.
func (c *gameHttpLogicPSRHttpClientPost) GetRedDotInfo(ctx context.Context, req *game_http_logic.GetRedDotInfoReq) (*game_http_logic.GetRedDotInfoResp, error) {
	return c.getRedDotInfo.CallUnary(ctx, req)
}

// MarkRedDotRead calls game_http_logic.GameHttpLogic.MarkRedDotRead.
func (c *gameHttpLogicPSRHttpClientPost) MarkRedDotRead(ctx context.Context, req *game_http_logic.MarkRedDotReadReq) (*game_http_logic.MarkRedDotReadResp, error) {
	return c.markRedDotRead.CallUnary(ctx, req)
}

// GetAggregateRedDotInfo calls game_http_logic.GameHttpLogic.GetAggregateRedDotInfo.
func (c *gameHttpLogicPSRHttpClientPost) GetAggregateRedDotInfo(ctx context.Context, req *game_http_logic.GetAggregateRedDotInfoReq) (*game_http_logic.GetAggregateRedDotInfoResp, error) {
	return c.getAggregateRedDotInfo.CallUnary(ctx, req)
}

// MarkAggregateRedDotRead calls game_http_logic.GameHttpLogic.MarkAggregateRedDotRead.
func (c *gameHttpLogicPSRHttpClientPost) MarkAggregateRedDotRead(ctx context.Context, req *game_http_logic.MarkAggregateRedDotReadReq) (*game_http_logic.MarkAggregateRedDotReadResp, error) {
	return c.markAggregateRedDotRead.CallUnary(ctx, req)
}

// GetUserInfos calls game_http_logic.GameHttpLogic.GetUserInfos.
func (c *gameHttpLogicPSRHttpClientPost) GetUserInfos(ctx context.Context, req *game_http_logic.GetUserInfosReq) (*game_http_logic.GetUserInfosResp, error) {
	return c.getUserInfos.CallUnary(ctx, req)
}

// ReorderRateTags calls game_http_logic.GameHttpLogic.ReorderRateTags.
func (c *gameHttpLogicPSRHttpClientPost) ReorderRateTags(ctx context.Context, req *game_http_logic.ReorderRateTagsReq) (*game_http_logic.ReorderRateTagsResp, error) {
	return c.reorderRateTags.CallUnary(ctx, req)
}

// NewGameHttpLogicPSRHttpClientGet constructs a client for the game_http_logic.GameHttpLogic
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the psrhttp.WithGRPC() or psrhttp.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewGameHttpLogicPSRHttpClientGet(httpClient psr_http.HTTPClient, baseURL string, opts ...psr_http.ClientOption) GameHttpLogicPSRHttpClientGet {
	baseURL = strings.TrimRight(baseURL, "/")
	return &gameHttpLogicPSRHttpClientGet{}
}

// gameHttpLogicPSRHttpClientGet implements GameHttpLogicPSRHttpClientGet.
type gameHttpLogicPSRHttpClientGet struct {
}

// GameHttpLogicPSRHttpHandler is an implementation of the game_http_logic.GameHttpLogic service.
type GameHttpLogicPSRHttpHandler interface {
	// 获取开黑形象自评题目
	GetSelfRateQuestions(context.Context, *game_http_logic.GetSelfRateQuestionsReq) (*game_http_logic.GetSelfRateQuestionsResp, error)
	// 提交开黑形象自评结果
	SubmitSelfRateResult(context.Context, *game_http_logic.SubmitSelfRateResultReq) (*game_http_logic.SubmitGameUserRateResp, error)
	// 获取用户测评形象结果
	GetUserImageRateResult(context.Context, *game_http_logic.GetUserImageRateResultReq) (*game_http_logic.GameUserPersonalImageRespItem, error)
	// 获取用户信息
	GetUserInfo(context.Context, *game_http_logic.GetUserInfoReq) (*game_http_logic.GetUserInfoResp, error)
	// 获取用户被评列表
	GetGameUserBeRateList(context.Context, *game_http_logic.GetGameUserBeRateListReq) (*game_http_logic.GetGameUserBeRateListResp, error)
	// 获取用户评价列表
	GetGameUserRateList(context.Context, *game_http_logic.GetGameUserRateListReq) (*game_http_logic.GetGameUserRateListResp, error)
	// 提交用户评价
	SubmitGameUserRate(context.Context, *game_http_logic.SubmitGameUserRateReq) (*game_http_logic.SubmitGameUserRateResp, error)
	// 获取红点信息
	GetRedDotInfo(context.Context, *game_http_logic.GetRedDotInfoReq) (*game_http_logic.GetRedDotInfoResp, error)
	// 标记红点已读
	MarkRedDotRead(context.Context, *game_http_logic.MarkRedDotReadReq) (*game_http_logic.MarkRedDotReadResp, error)
	// 获取聚合红点信息
	GetAggregateRedDotInfo(context.Context, *game_http_logic.GetAggregateRedDotInfoReq) (*game_http_logic.GetAggregateRedDotInfoResp, error)
	// 标记聚合红点已读
	MarkAggregateRedDotRead(context.Context, *game_http_logic.MarkAggregateRedDotReadReq) (*game_http_logic.MarkAggregateRedDotReadResp, error)
	// 获取用户信息
	GetUserInfos(context.Context, *game_http_logic.GetUserInfosReq) (*game_http_logic.GetUserInfosResp, error)
	// 获取推荐评价标签
	ReorderRateTags(context.Context, *game_http_logic.ReorderRateTagsReq) (*game_http_logic.ReorderRateTagsResp, error)
}

// UnimplementedGameHttpLogicPSRHttpHandler returns CodeUnimplemented from all methods.
type UnimplementedGameHttpLogicPSRHttpHandler struct{}

func (UnimplementedGameHttpLogicPSRHttpHandler) GetSelfRateQuestions(context.Context, *game_http_logic.GetSelfRateQuestionsReq) (*game_http_logic.GetSelfRateQuestionsResp, error) {
	return nil, psr_http.NewError(psr_http.CodeUnimplemented, errors.New("game_http_logic.GameHttpLogic.GetSelfRateQuestions is not implemented"))
}

func (UnimplementedGameHttpLogicPSRHttpHandler) SubmitSelfRateResult(context.Context, *game_http_logic.SubmitSelfRateResultReq) (*game_http_logic.SubmitGameUserRateResp, error) {
	return nil, psr_http.NewError(psr_http.CodeUnimplemented, errors.New("game_http_logic.GameHttpLogic.SubmitSelfRateResult is not implemented"))
}

func (UnimplementedGameHttpLogicPSRHttpHandler) GetUserImageRateResult(context.Context, *game_http_logic.GetUserImageRateResultReq) (*game_http_logic.GameUserPersonalImageRespItem, error) {
	return nil, psr_http.NewError(psr_http.CodeUnimplemented, errors.New("game_http_logic.GameHttpLogic.GetUserImageRateResult is not implemented"))
}

func (UnimplementedGameHttpLogicPSRHttpHandler) GetUserInfo(context.Context, *game_http_logic.GetUserInfoReq) (*game_http_logic.GetUserInfoResp, error) {
	return nil, psr_http.NewError(psr_http.CodeUnimplemented, errors.New("game_http_logic.GameHttpLogic.GetUserInfo is not implemented"))
}

func (UnimplementedGameHttpLogicPSRHttpHandler) GetGameUserBeRateList(context.Context, *game_http_logic.GetGameUserBeRateListReq) (*game_http_logic.GetGameUserBeRateListResp, error) {
	return nil, psr_http.NewError(psr_http.CodeUnimplemented, errors.New("game_http_logic.GameHttpLogic.GetGameUserBeRateList is not implemented"))
}

func (UnimplementedGameHttpLogicPSRHttpHandler) GetGameUserRateList(context.Context, *game_http_logic.GetGameUserRateListReq) (*game_http_logic.GetGameUserRateListResp, error) {
	return nil, psr_http.NewError(psr_http.CodeUnimplemented, errors.New("game_http_logic.GameHttpLogic.GetGameUserRateList is not implemented"))
}

func (UnimplementedGameHttpLogicPSRHttpHandler) SubmitGameUserRate(context.Context, *game_http_logic.SubmitGameUserRateReq) (*game_http_logic.SubmitGameUserRateResp, error) {
	return nil, psr_http.NewError(psr_http.CodeUnimplemented, errors.New("game_http_logic.GameHttpLogic.SubmitGameUserRate is not implemented"))
}

func (UnimplementedGameHttpLogicPSRHttpHandler) GetRedDotInfo(context.Context, *game_http_logic.GetRedDotInfoReq) (*game_http_logic.GetRedDotInfoResp, error) {
	return nil, psr_http.NewError(psr_http.CodeUnimplemented, errors.New("game_http_logic.GameHttpLogic.GetRedDotInfo is not implemented"))
}

func (UnimplementedGameHttpLogicPSRHttpHandler) MarkRedDotRead(context.Context, *game_http_logic.MarkRedDotReadReq) (*game_http_logic.MarkRedDotReadResp, error) {
	return nil, psr_http.NewError(psr_http.CodeUnimplemented, errors.New("game_http_logic.GameHttpLogic.MarkRedDotRead is not implemented"))
}

func (UnimplementedGameHttpLogicPSRHttpHandler) GetAggregateRedDotInfo(context.Context, *game_http_logic.GetAggregateRedDotInfoReq) (*game_http_logic.GetAggregateRedDotInfoResp, error) {
	return nil, psr_http.NewError(psr_http.CodeUnimplemented, errors.New("game_http_logic.GameHttpLogic.GetAggregateRedDotInfo is not implemented"))
}

func (UnimplementedGameHttpLogicPSRHttpHandler) MarkAggregateRedDotRead(context.Context, *game_http_logic.MarkAggregateRedDotReadReq) (*game_http_logic.MarkAggregateRedDotReadResp, error) {
	return nil, psr_http.NewError(psr_http.CodeUnimplemented, errors.New("game_http_logic.GameHttpLogic.MarkAggregateRedDotRead is not implemented"))
}

func (UnimplementedGameHttpLogicPSRHttpHandler) GetUserInfos(context.Context, *game_http_logic.GetUserInfosReq) (*game_http_logic.GetUserInfosResp, error) {
	return nil, psr_http.NewError(psr_http.CodeUnimplemented, errors.New("game_http_logic.GameHttpLogic.GetUserInfos is not implemented"))
}

func (UnimplementedGameHttpLogicPSRHttpHandler) ReorderRateTags(context.Context, *game_http_logic.ReorderRateTagsReq) (*game_http_logic.ReorderRateTagsResp, error) {
	return nil, psr_http.NewError(psr_http.CodeUnimplemented, errors.New("game_http_logic.GameHttpLogic.ReorderRateTags is not implemented"))
}

type GameHttpLogicPSRHttpDispatcher struct {
	gameHttpLogicGetSelfRateQuestionsHandler    *psr_http.Handler[game_http_logic.GetSelfRateQuestionsReq, game_http_logic.GetSelfRateQuestionsResp]
	gameHttpLogicSubmitSelfRateResultHandler    *psr_http.Handler[game_http_logic.SubmitSelfRateResultReq, game_http_logic.SubmitGameUserRateResp]
	gameHttpLogicGetUserImageRateResultHandler  *psr_http.Handler[game_http_logic.GetUserImageRateResultReq, game_http_logic.GameUserPersonalImageRespItem]
	gameHttpLogicGetUserInfoHandler             *psr_http.Handler[game_http_logic.GetUserInfoReq, game_http_logic.GetUserInfoResp]
	gameHttpLogicGetGameUserBeRateListHandler   *psr_http.Handler[game_http_logic.GetGameUserBeRateListReq, game_http_logic.GetGameUserBeRateListResp]
	gameHttpLogicGetGameUserRateListHandler     *psr_http.Handler[game_http_logic.GetGameUserRateListReq, game_http_logic.GetGameUserRateListResp]
	gameHttpLogicSubmitGameUserRateHandler      *psr_http.Handler[game_http_logic.SubmitGameUserRateReq, game_http_logic.SubmitGameUserRateResp]
	gameHttpLogicGetRedDotInfoHandler           *psr_http.Handler[game_http_logic.GetRedDotInfoReq, game_http_logic.GetRedDotInfoResp]
	gameHttpLogicMarkRedDotReadHandler          *psr_http.Handler[game_http_logic.MarkRedDotReadReq, game_http_logic.MarkRedDotReadResp]
	gameHttpLogicGetAggregateRedDotInfoHandler  *psr_http.Handler[game_http_logic.GetAggregateRedDotInfoReq, game_http_logic.GetAggregateRedDotInfoResp]
	gameHttpLogicMarkAggregateRedDotReadHandler *psr_http.Handler[game_http_logic.MarkAggregateRedDotReadReq, game_http_logic.MarkAggregateRedDotReadResp]
	gameHttpLogicGetUserInfosHandler            *psr_http.Handler[game_http_logic.GetUserInfosReq, game_http_logic.GetUserInfosResp]
	gameHttpLogicReorderRateTagsHandler         *psr_http.Handler[game_http_logic.ReorderRateTagsReq, game_http_logic.ReorderRateTagsResp]
}

func (d *GameHttpLogicPSRHttpDispatcher) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	f := d.Dispatch(r.URL.Path)
	if f == nil {
		f = func(w http.ResponseWriter, r *http.Request) {
			http.NotFound(w, r)
		}
	}
	f(w, r)
}

func (d *GameHttpLogicPSRHttpDispatcher) Dispatch(path string) http.HandlerFunc {
	if path == GameHttpLogicGetSelfRateQuestionsProcedure {
		return d.gameHttpLogicGetSelfRateQuestionsHandler.ServeHTTP
	}
	if path == GameHttpLogicSubmitSelfRateResultProcedure {
		return d.gameHttpLogicSubmitSelfRateResultHandler.ServeHTTP
	}
	if path == GameHttpLogicGetUserImageRateResultProcedure {
		return d.gameHttpLogicGetUserImageRateResultHandler.ServeHTTP
	}
	if path == GameHttpLogicGetUserInfoProcedure {
		return d.gameHttpLogicGetUserInfoHandler.ServeHTTP
	}
	if path == GameHttpLogicGetGameUserBeRateListProcedure {
		return d.gameHttpLogicGetGameUserBeRateListHandler.ServeHTTP
	}
	if path == GameHttpLogicGetGameUserRateListProcedure {
		return d.gameHttpLogicGetGameUserRateListHandler.ServeHTTP
	}
	if path == GameHttpLogicSubmitGameUserRateProcedure {
		return d.gameHttpLogicSubmitGameUserRateHandler.ServeHTTP
	}
	if path == GameHttpLogicGetRedDotInfoProcedure {
		return d.gameHttpLogicGetRedDotInfoHandler.ServeHTTP
	}
	if path == GameHttpLogicMarkRedDotReadProcedure {
		return d.gameHttpLogicMarkRedDotReadHandler.ServeHTTP
	}
	if path == GameHttpLogicGetAggregateRedDotInfoProcedure {
		return d.gameHttpLogicGetAggregateRedDotInfoHandler.ServeHTTP
	}
	if path == GameHttpLogicMarkAggregateRedDotReadProcedure {
		return d.gameHttpLogicMarkAggregateRedDotReadHandler.ServeHTTP
	}
	if path == GameHttpLogicGetUserInfosProcedure {
		return d.gameHttpLogicGetUserInfosHandler.ServeHTTP
	}
	if path == GameHttpLogicReorderRateTagsProcedure {
		return d.gameHttpLogicReorderRateTagsHandler.ServeHTTP
	}
	return nil
}

func (d *GameHttpLogicPSRHttpDispatcher) Range(f func(method string, path string, handleFunc http.HandlerFunc)) {
	f(d.gameHttpLogicGetSelfRateQuestionsHandler.Method(), GameHttpLogicGetSelfRateQuestionsProcedure, d.gameHttpLogicGetSelfRateQuestionsHandler.ServeHTTP)
	f(d.gameHttpLogicSubmitSelfRateResultHandler.Method(), GameHttpLogicSubmitSelfRateResultProcedure, d.gameHttpLogicSubmitSelfRateResultHandler.ServeHTTP)
	f(d.gameHttpLogicGetUserImageRateResultHandler.Method(), GameHttpLogicGetUserImageRateResultProcedure, d.gameHttpLogicGetUserImageRateResultHandler.ServeHTTP)
	f(d.gameHttpLogicGetUserInfoHandler.Method(), GameHttpLogicGetUserInfoProcedure, d.gameHttpLogicGetUserInfoHandler.ServeHTTP)
	f(d.gameHttpLogicGetGameUserBeRateListHandler.Method(), GameHttpLogicGetGameUserBeRateListProcedure, d.gameHttpLogicGetGameUserBeRateListHandler.ServeHTTP)
	f(d.gameHttpLogicGetGameUserRateListHandler.Method(), GameHttpLogicGetGameUserRateListProcedure, d.gameHttpLogicGetGameUserRateListHandler.ServeHTTP)
	f(d.gameHttpLogicSubmitGameUserRateHandler.Method(), GameHttpLogicSubmitGameUserRateProcedure, d.gameHttpLogicSubmitGameUserRateHandler.ServeHTTP)
	f(d.gameHttpLogicGetRedDotInfoHandler.Method(), GameHttpLogicGetRedDotInfoProcedure, d.gameHttpLogicGetRedDotInfoHandler.ServeHTTP)
	f(d.gameHttpLogicMarkRedDotReadHandler.Method(), GameHttpLogicMarkRedDotReadProcedure, d.gameHttpLogicMarkRedDotReadHandler.ServeHTTP)
	f(d.gameHttpLogicGetAggregateRedDotInfoHandler.Method(), GameHttpLogicGetAggregateRedDotInfoProcedure, d.gameHttpLogicGetAggregateRedDotInfoHandler.ServeHTTP)
	f(d.gameHttpLogicMarkAggregateRedDotReadHandler.Method(), GameHttpLogicMarkAggregateRedDotReadProcedure, d.gameHttpLogicMarkAggregateRedDotReadHandler.ServeHTTP)
	f(d.gameHttpLogicGetUserInfosHandler.Method(), GameHttpLogicGetUserInfosProcedure, d.gameHttpLogicGetUserInfosHandler.ServeHTTP)
	f(d.gameHttpLogicReorderRateTagsHandler.Method(), GameHttpLogicReorderRateTagsProcedure, d.gameHttpLogicReorderRateTagsHandler.ServeHTTP)
}

func NewGameHttpLogicPSRHttpDispatcher(svc GameHttpLogicPSRHttpHandler, opts ...psr_http.HandlerOption) *GameHttpLogicPSRHttpDispatcher {
	return &GameHttpLogicPSRHttpDispatcher{
		gameHttpLogicGetSelfRateQuestionsHandler: psr_http.NewUnaryHandler(
			GameHttpLogicGetSelfRateQuestionsProcedure,
			svc.GetSelfRateQuestions,
			psr_http.WithSchema(gameHttpLogicGetSelfRateQuestionsMethodDescriptor),
			psr_http.WithHandlerOptions(opts...),
		),
		gameHttpLogicSubmitSelfRateResultHandler: psr_http.NewUnaryHandler(
			GameHttpLogicSubmitSelfRateResultProcedure,
			svc.SubmitSelfRateResult,
			psr_http.WithSchema(gameHttpLogicSubmitSelfRateResultMethodDescriptor),
			psr_http.WithHandlerOptions(opts...),
		),
		gameHttpLogicGetUserImageRateResultHandler: psr_http.NewUnaryHandler(
			GameHttpLogicGetUserImageRateResultProcedure,
			svc.GetUserImageRateResult,
			psr_http.WithSchema(gameHttpLogicGetUserImageRateResultMethodDescriptor),
			psr_http.WithHandlerOptions(opts...),
		),
		gameHttpLogicGetUserInfoHandler: psr_http.NewUnaryHandler(
			GameHttpLogicGetUserInfoProcedure,
			svc.GetUserInfo,
			psr_http.WithSchema(gameHttpLogicGetUserInfoMethodDescriptor),
			psr_http.WithHandlerOptions(opts...),
		),
		gameHttpLogicGetGameUserBeRateListHandler: psr_http.NewUnaryHandler(
			GameHttpLogicGetGameUserBeRateListProcedure,
			svc.GetGameUserBeRateList,
			psr_http.WithSchema(gameHttpLogicGetGameUserBeRateListMethodDescriptor),
			psr_http.WithHandlerOptions(opts...),
		),
		gameHttpLogicGetGameUserRateListHandler: psr_http.NewUnaryHandler(
			GameHttpLogicGetGameUserRateListProcedure,
			svc.GetGameUserRateList,
			psr_http.WithSchema(gameHttpLogicGetGameUserRateListMethodDescriptor),
			psr_http.WithHandlerOptions(opts...),
		),
		gameHttpLogicSubmitGameUserRateHandler: psr_http.NewUnaryHandler(
			GameHttpLogicSubmitGameUserRateProcedure,
			svc.SubmitGameUserRate,
			psr_http.WithSchema(gameHttpLogicSubmitGameUserRateMethodDescriptor),
			psr_http.WithHandlerOptions(opts...),
		),
		gameHttpLogicGetRedDotInfoHandler: psr_http.NewUnaryHandler(
			GameHttpLogicGetRedDotInfoProcedure,
			svc.GetRedDotInfo,
			psr_http.WithSchema(gameHttpLogicGetRedDotInfoMethodDescriptor),
			psr_http.WithHandlerOptions(opts...),
		),
		gameHttpLogicMarkRedDotReadHandler: psr_http.NewUnaryHandler(
			GameHttpLogicMarkRedDotReadProcedure,
			svc.MarkRedDotRead,
			psr_http.WithSchema(gameHttpLogicMarkRedDotReadMethodDescriptor),
			psr_http.WithHandlerOptions(opts...),
		),
		gameHttpLogicGetAggregateRedDotInfoHandler: psr_http.NewUnaryHandler(
			GameHttpLogicGetAggregateRedDotInfoProcedure,
			svc.GetAggregateRedDotInfo,
			psr_http.WithSchema(gameHttpLogicGetAggregateRedDotInfoMethodDescriptor),
			psr_http.WithHandlerOptions(opts...),
		),
		gameHttpLogicMarkAggregateRedDotReadHandler: psr_http.NewUnaryHandler(
			GameHttpLogicMarkAggregateRedDotReadProcedure,
			svc.MarkAggregateRedDotRead,
			psr_http.WithSchema(gameHttpLogicMarkAggregateRedDotReadMethodDescriptor),
			psr_http.WithHandlerOptions(opts...),
		),
		gameHttpLogicGetUserInfosHandler: psr_http.NewUnaryHandler(
			GameHttpLogicGetUserInfosProcedure,
			svc.GetUserInfos,
			psr_http.WithSchema(gameHttpLogicGetUserInfosMethodDescriptor),
			psr_http.WithHandlerOptions(opts...),
		),
		gameHttpLogicReorderRateTagsHandler: psr_http.NewUnaryHandler(
			GameHttpLogicReorderRateTagsProcedure,
			svc.ReorderRateTags,
			psr_http.WithSchema(gameHttpLogicReorderRateTagsMethodDescriptor),
			psr_http.WithHandlerOptions(opts...),
		),
	}
}
