// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/game-http-logic/game-http-logic.proto

package game_http_logic // import "golang.52tt.com/protocol/services/game-http-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "google.golang.org/genproto/googleapis/api/annotations"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 自评问题配置
type H5Question struct {
	// 题目id
	QuestionId string `protobuf:"bytes,1,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
	// 题目描述
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// 选项
	Options []*H5Question_Option `protobuf:"bytes,3,rep,name=options,proto3" json:"options,omitempty"`
	// 题目所属维度id
	DimensionId          string   `protobuf:"bytes,4,opt,name=dimension_id,json=dimensionId,proto3" json:"dimension_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *H5Question) Reset()         { *m = H5Question{} }
func (m *H5Question) String() string { return proto.CompactTextString(m) }
func (*H5Question) ProtoMessage()    {}
func (*H5Question) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{0}
}
func (m *H5Question) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_H5Question.Unmarshal(m, b)
}
func (m *H5Question) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_H5Question.Marshal(b, m, deterministic)
}
func (dst *H5Question) XXX_Merge(src proto.Message) {
	xxx_messageInfo_H5Question.Merge(dst, src)
}
func (m *H5Question) XXX_Size() int {
	return xxx_messageInfo_H5Question.Size(m)
}
func (m *H5Question) XXX_DiscardUnknown() {
	xxx_messageInfo_H5Question.DiscardUnknown(m)
}

var xxx_messageInfo_H5Question proto.InternalMessageInfo

func (m *H5Question) GetQuestionId() string {
	if m != nil {
		return m.QuestionId
	}
	return ""
}

func (m *H5Question) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *H5Question) GetOptions() []*H5Question_Option {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *H5Question) GetDimensionId() string {
	if m != nil {
		return m.DimensionId
	}
	return ""
}

type H5Question_Option struct {
	OptId                uint32   `protobuf:"varint,1,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *H5Question_Option) Reset()         { *m = H5Question_Option{} }
func (m *H5Question_Option) String() string { return proto.CompactTextString(m) }
func (*H5Question_Option) ProtoMessage()    {}
func (*H5Question_Option) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{0, 0}
}
func (m *H5Question_Option) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_H5Question_Option.Unmarshal(m, b)
}
func (m *H5Question_Option) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_H5Question_Option.Marshal(b, m, deterministic)
}
func (dst *H5Question_Option) XXX_Merge(src proto.Message) {
	xxx_messageInfo_H5Question_Option.Merge(dst, src)
}
func (m *H5Question_Option) XXX_Size() int {
	return xxx_messageInfo_H5Question_Option.Size(m)
}
func (m *H5Question_Option) XXX_DiscardUnknown() {
	xxx_messageInfo_H5Question_Option.DiscardUnknown(m)
}

var xxx_messageInfo_H5Question_Option proto.InternalMessageInfo

func (m *H5Question_Option) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *H5Question_Option) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// 获取开黑形象自评题目
type GetSelfRateQuestionsReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSelfRateQuestionsReq) Reset()         { *m = GetSelfRateQuestionsReq{} }
func (m *GetSelfRateQuestionsReq) String() string { return proto.CompactTextString(m) }
func (*GetSelfRateQuestionsReq) ProtoMessage()    {}
func (*GetSelfRateQuestionsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{1}
}
func (m *GetSelfRateQuestionsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSelfRateQuestionsReq.Unmarshal(m, b)
}
func (m *GetSelfRateQuestionsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSelfRateQuestionsReq.Marshal(b, m, deterministic)
}
func (dst *GetSelfRateQuestionsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSelfRateQuestionsReq.Merge(dst, src)
}
func (m *GetSelfRateQuestionsReq) XXX_Size() int {
	return xxx_messageInfo_GetSelfRateQuestionsReq.Size(m)
}
func (m *GetSelfRateQuestionsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSelfRateQuestionsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSelfRateQuestionsReq proto.InternalMessageInfo

func (m *GetSelfRateQuestionsReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetSelfRateQuestionsResp struct {
	Questions            []*H5Question `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSelfRateQuestionsResp) Reset()         { *m = GetSelfRateQuestionsResp{} }
func (m *GetSelfRateQuestionsResp) String() string { return proto.CompactTextString(m) }
func (*GetSelfRateQuestionsResp) ProtoMessage()    {}
func (*GetSelfRateQuestionsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{2}
}
func (m *GetSelfRateQuestionsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSelfRateQuestionsResp.Unmarshal(m, b)
}
func (m *GetSelfRateQuestionsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSelfRateQuestionsResp.Marshal(b, m, deterministic)
}
func (dst *GetSelfRateQuestionsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSelfRateQuestionsResp.Merge(dst, src)
}
func (m *GetSelfRateQuestionsResp) XXX_Size() int {
	return xxx_messageInfo_GetSelfRateQuestionsResp.Size(m)
}
func (m *GetSelfRateQuestionsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSelfRateQuestionsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSelfRateQuestionsResp proto.InternalMessageInfo

func (m *GetSelfRateQuestionsResp) GetQuestions() []*H5Question {
	if m != nil {
		return m.Questions
	}
	return nil
}

type SelfRateResult struct {
	// 题目id
	QuestionId string `protobuf:"bytes,1,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
	// 选项id
	OptId uint32 `protobuf:"varint,2,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	// 题目所属维度id
	DimensionId          string   `protobuf:"bytes,3,opt,name=dimension_id,json=dimensionId,proto3" json:"dimension_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SelfRateResult) Reset()         { *m = SelfRateResult{} }
func (m *SelfRateResult) String() string { return proto.CompactTextString(m) }
func (*SelfRateResult) ProtoMessage()    {}
func (*SelfRateResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{3}
}
func (m *SelfRateResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelfRateResult.Unmarshal(m, b)
}
func (m *SelfRateResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelfRateResult.Marshal(b, m, deterministic)
}
func (dst *SelfRateResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelfRateResult.Merge(dst, src)
}
func (m *SelfRateResult) XXX_Size() int {
	return xxx_messageInfo_SelfRateResult.Size(m)
}
func (m *SelfRateResult) XXX_DiscardUnknown() {
	xxx_messageInfo_SelfRateResult.DiscardUnknown(m)
}

var xxx_messageInfo_SelfRateResult proto.InternalMessageInfo

func (m *SelfRateResult) GetQuestionId() string {
	if m != nil {
		return m.QuestionId
	}
	return ""
}

func (m *SelfRateResult) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *SelfRateResult) GetDimensionId() string {
	if m != nil {
		return m.DimensionId
	}
	return ""
}

// 提交开黑形象自评结果
type SubmitSelfRateResultReq struct {
	TabId                uint32            `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Results              []*SelfRateResult `protobuf:"bytes,2,rep,name=results,proto3" json:"results,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SubmitSelfRateResultReq) Reset()         { *m = SubmitSelfRateResultReq{} }
func (m *SubmitSelfRateResultReq) String() string { return proto.CompactTextString(m) }
func (*SubmitSelfRateResultReq) ProtoMessage()    {}
func (*SubmitSelfRateResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{4}
}
func (m *SubmitSelfRateResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitSelfRateResultReq.Unmarshal(m, b)
}
func (m *SubmitSelfRateResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitSelfRateResultReq.Marshal(b, m, deterministic)
}
func (dst *SubmitSelfRateResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitSelfRateResultReq.Merge(dst, src)
}
func (m *SubmitSelfRateResultReq) XXX_Size() int {
	return xxx_messageInfo_SubmitSelfRateResultReq.Size(m)
}
func (m *SubmitSelfRateResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitSelfRateResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitSelfRateResultReq proto.InternalMessageInfo

func (m *SubmitSelfRateResultReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SubmitSelfRateResultReq) GetResults() []*SelfRateResult {
	if m != nil {
		return m.Results
	}
	return nil
}

// 获取用户测评形象结果
type GetUserImageRateResultReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserImageRateResultReq) Reset()         { *m = GetUserImageRateResultReq{} }
func (m *GetUserImageRateResultReq) String() string { return proto.CompactTextString(m) }
func (*GetUserImageRateResultReq) ProtoMessage()    {}
func (*GetUserImageRateResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{5}
}
func (m *GetUserImageRateResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserImageRateResultReq.Unmarshal(m, b)
}
func (m *GetUserImageRateResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserImageRateResultReq.Marshal(b, m, deterministic)
}
func (dst *GetUserImageRateResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserImageRateResultReq.Merge(dst, src)
}
func (m *GetUserImageRateResultReq) XXX_Size() int {
	return xxx_messageInfo_GetUserImageRateResultReq.Size(m)
}
func (m *GetUserImageRateResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserImageRateResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserImageRateResultReq proto.InternalMessageInfo

func (m *GetUserImageRateResultReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserImageRateResultReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GameUserPersonalImageRespItem struct {
	// 维度分数
	Item []*DimensionItem `protobuf:"bytes,1,rep,name=item,proto3" json:"item,omitempty"`
	// 评价总分
	TotalScore float32 `protobuf:"fixed32,2,opt,name=total_score,json=totalScore,proto3" json:"total_score,omitempty"`
	// 开黑关键字，aigc返回
	GameKeyWords string `protobuf:"bytes,3,opt,name=game_key_words,json=gameKeyWords,proto3" json:"game_key_words,omitempty"`
	// 关键字描述，aigc返回
	GameKeyWordsDesc string `protobuf:"bytes,4,opt,name=game_key_words_desc,json=gameKeyWordsDesc,proto3" json:"game_key_words_desc,omitempty"`
	// 自我介绍，aigc返回
	SelfIntro string `protobuf:"bytes,5,opt,name=self_intro,json=selfIntro,proto3" json:"self_intro,omitempty"`
	// 相似用户占比，数据库写入时取10%-40%之间的随机数，H5测评结果用
	SimilarUserPercent string `protobuf:"bytes,6,opt,name=similar_user_percent,json=similarUserPercent,proto3" json:"similar_user_percent,omitempty"`
	// 用户账号
	Account string `protobuf:"bytes,7,opt,name=account,proto3" json:"account,omitempty"`
	// 用户昵称
	Nickname string `protobuf:"bytes,8,opt,name=nickname,proto3" json:"nickname,omitempty"`
	// 玩法名称
	TabName              string   `protobuf:"bytes,9,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameUserPersonalImageRespItem) Reset()         { *m = GameUserPersonalImageRespItem{} }
func (m *GameUserPersonalImageRespItem) String() string { return proto.CompactTextString(m) }
func (*GameUserPersonalImageRespItem) ProtoMessage()    {}
func (*GameUserPersonalImageRespItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{6}
}
func (m *GameUserPersonalImageRespItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameUserPersonalImageRespItem.Unmarshal(m, b)
}
func (m *GameUserPersonalImageRespItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameUserPersonalImageRespItem.Marshal(b, m, deterministic)
}
func (dst *GameUserPersonalImageRespItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameUserPersonalImageRespItem.Merge(dst, src)
}
func (m *GameUserPersonalImageRespItem) XXX_Size() int {
	return xxx_messageInfo_GameUserPersonalImageRespItem.Size(m)
}
func (m *GameUserPersonalImageRespItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameUserPersonalImageRespItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameUserPersonalImageRespItem proto.InternalMessageInfo

func (m *GameUserPersonalImageRespItem) GetItem() []*DimensionItem {
	if m != nil {
		return m.Item
	}
	return nil
}

func (m *GameUserPersonalImageRespItem) GetTotalScore() float32 {
	if m != nil {
		return m.TotalScore
	}
	return 0
}

func (m *GameUserPersonalImageRespItem) GetGameKeyWords() string {
	if m != nil {
		return m.GameKeyWords
	}
	return ""
}

func (m *GameUserPersonalImageRespItem) GetGameKeyWordsDesc() string {
	if m != nil {
		return m.GameKeyWordsDesc
	}
	return ""
}

func (m *GameUserPersonalImageRespItem) GetSelfIntro() string {
	if m != nil {
		return m.SelfIntro
	}
	return ""
}

func (m *GameUserPersonalImageRespItem) GetSimilarUserPercent() string {
	if m != nil {
		return m.SimilarUserPercent
	}
	return ""
}

func (m *GameUserPersonalImageRespItem) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GameUserPersonalImageRespItem) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GameUserPersonalImageRespItem) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

type DimensionItem struct {
	// 维度id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 维度名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 维度分数
	Score                float32  `protobuf:"fixed32,3,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DimensionItem) Reset()         { *m = DimensionItem{} }
func (m *DimensionItem) String() string { return proto.CompactTextString(m) }
func (*DimensionItem) ProtoMessage()    {}
func (*DimensionItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{7}
}
func (m *DimensionItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DimensionItem.Unmarshal(m, b)
}
func (m *DimensionItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DimensionItem.Marshal(b, m, deterministic)
}
func (dst *DimensionItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DimensionItem.Merge(dst, src)
}
func (m *DimensionItem) XXX_Size() int {
	return xxx_messageInfo_DimensionItem.Size(m)
}
func (m *DimensionItem) XXX_DiscardUnknown() {
	xxx_messageInfo_DimensionItem.DiscardUnknown(m)
}

var xxx_messageInfo_DimensionItem proto.InternalMessageInfo

func (m *DimensionItem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *DimensionItem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DimensionItem) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type UserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Username             string   `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Alias                string   `protobuf:"bytes,4,opt,name=alias,proto3" json:"alias,omitempty"`
	Phone                string   `protobuf:"bytes,5,opt,name=phone,proto3" json:"phone,omitempty"`
	Sex                  int32    `protobuf:"varint,6,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{8}
}
func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (dst *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(dst, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserInfo) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *UserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserInfo) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *UserInfo) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *UserInfo) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

// 获取用户信息
type GetUserInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInfoReq) Reset()         { *m = GetUserInfoReq{} }
func (m *GetUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserInfoReq) ProtoMessage()    {}
func (*GetUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{9}
}
func (m *GetUserInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInfoReq.Unmarshal(m, b)
}
func (m *GetUserInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInfoReq.Merge(dst, src)
}
func (m *GetUserInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserInfoReq.Size(m)
}
func (m *GetUserInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInfoReq proto.InternalMessageInfo

type GetUserInfoResp struct {
	UserInfo             *UserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetUserInfoResp) Reset()         { *m = GetUserInfoResp{} }
func (m *GetUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserInfoResp) ProtoMessage()    {}
func (*GetUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{10}
}
func (m *GetUserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInfoResp.Unmarshal(m, b)
}
func (m *GetUserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInfoResp.Merge(dst, src)
}
func (m *GetUserInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserInfoResp.Size(m)
}
func (m *GetUserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInfoResp proto.InternalMessageInfo

func (m *GetUserInfoResp) GetUserInfo() *UserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

type GetGameUserBeRateListReq struct {
	// 用户id
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// last record time，分页用
	LastRecordTime       uint64   `protobuf:"varint,2,opt,name=last_record_time,json=lastRecordTime,proto3" json:"last_record_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameUserBeRateListReq) Reset()         { *m = GetGameUserBeRateListReq{} }
func (m *GetGameUserBeRateListReq) String() string { return proto.CompactTextString(m) }
func (*GetGameUserBeRateListReq) ProtoMessage()    {}
func (*GetGameUserBeRateListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{11}
}
func (m *GetGameUserBeRateListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameUserBeRateListReq.Unmarshal(m, b)
}
func (m *GetGameUserBeRateListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameUserBeRateListReq.Marshal(b, m, deterministic)
}
func (dst *GetGameUserBeRateListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameUserBeRateListReq.Merge(dst, src)
}
func (m *GetGameUserBeRateListReq) XXX_Size() int {
	return xxx_messageInfo_GetGameUserBeRateListReq.Size(m)
}
func (m *GetGameUserBeRateListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameUserBeRateListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameUserBeRateListReq proto.InternalMessageInfo

func (m *GetGameUserBeRateListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGameUserBeRateListReq) GetLastRecordTime() uint64 {
	if m != nil {
		return m.LastRecordTime
	}
	return 0
}

type GetGameUserBeRateListResp struct {
	// 被评详情数据
	Items []*GameUserBeRateItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	// 是否已经到底部，true为底部
	LoadFinish           bool     `protobuf:"varint,2,opt,name=load_finish,json=loadFinish,proto3" json:"load_finish,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameUserBeRateListResp) Reset()         { *m = GetGameUserBeRateListResp{} }
func (m *GetGameUserBeRateListResp) String() string { return proto.CompactTextString(m) }
func (*GetGameUserBeRateListResp) ProtoMessage()    {}
func (*GetGameUserBeRateListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{12}
}
func (m *GetGameUserBeRateListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameUserBeRateListResp.Unmarshal(m, b)
}
func (m *GetGameUserBeRateListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameUserBeRateListResp.Marshal(b, m, deterministic)
}
func (dst *GetGameUserBeRateListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameUserBeRateListResp.Merge(dst, src)
}
func (m *GetGameUserBeRateListResp) XXX_Size() int {
	return xxx_messageInfo_GetGameUserBeRateListResp.Size(m)
}
func (m *GetGameUserBeRateListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameUserBeRateListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameUserBeRateListResp proto.InternalMessageInfo

func (m *GetGameUserBeRateListResp) GetItems() []*GameUserBeRateItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *GetGameUserBeRateListResp) GetLoadFinish() bool {
	if m != nil {
		return m.LoadFinish
	}
	return false
}

type GameUserBeRateItem struct {
	// item类型，1-好评，2-扣分
	ItemType uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	// Types that are valid to be assigned to Content:
	//	*GameUserBeRateItem_LikeItem_
	//	*GameUserBeRateItem_DeductItem_
	Content              isGameUserBeRateItem_Content `protobuf_oneof:"content"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GameUserBeRateItem) Reset()         { *m = GameUserBeRateItem{} }
func (m *GameUserBeRateItem) String() string { return proto.CompactTextString(m) }
func (*GameUserBeRateItem) ProtoMessage()    {}
func (*GameUserBeRateItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{13}
}
func (m *GameUserBeRateItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameUserBeRateItem.Unmarshal(m, b)
}
func (m *GameUserBeRateItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameUserBeRateItem.Marshal(b, m, deterministic)
}
func (dst *GameUserBeRateItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameUserBeRateItem.Merge(dst, src)
}
func (m *GameUserBeRateItem) XXX_Size() int {
	return xxx_messageInfo_GameUserBeRateItem.Size(m)
}
func (m *GameUserBeRateItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameUserBeRateItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameUserBeRateItem proto.InternalMessageInfo

func (m *GameUserBeRateItem) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

type isGameUserBeRateItem_Content interface {
	isGameUserBeRateItem_Content()
}

type GameUserBeRateItem_LikeItem_ struct {
	LikeItem *GameUserBeRateItem_LikeItem `protobuf:"bytes,2,opt,name=like_item,json=likeItem,proto3,oneof"`
}

type GameUserBeRateItem_DeductItem_ struct {
	DeductItem *GameUserBeRateItem_DeductItem `protobuf:"bytes,3,opt,name=deduct_item,json=deductItem,proto3,oneof"`
}

func (*GameUserBeRateItem_LikeItem_) isGameUserBeRateItem_Content() {}

func (*GameUserBeRateItem_DeductItem_) isGameUserBeRateItem_Content() {}

func (m *GameUserBeRateItem) GetContent() isGameUserBeRateItem_Content {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *GameUserBeRateItem) GetLikeItem() *GameUserBeRateItem_LikeItem {
	if x, ok := m.GetContent().(*GameUserBeRateItem_LikeItem_); ok {
		return x.LikeItem
	}
	return nil
}

func (m *GameUserBeRateItem) GetDeductItem() *GameUserBeRateItem_DeductItem {
	if x, ok := m.GetContent().(*GameUserBeRateItem_DeductItem_); ok {
		return x.DeductItem
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*GameUserBeRateItem) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _GameUserBeRateItem_OneofMarshaler, _GameUserBeRateItem_OneofUnmarshaler, _GameUserBeRateItem_OneofSizer, []interface{}{
		(*GameUserBeRateItem_LikeItem_)(nil),
		(*GameUserBeRateItem_DeductItem_)(nil),
	}
}

func _GameUserBeRateItem_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*GameUserBeRateItem)
	// content
	switch x := m.Content.(type) {
	case *GameUserBeRateItem_LikeItem_:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.LikeItem); err != nil {
			return err
		}
	case *GameUserBeRateItem_DeductItem_:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.DeductItem); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("GameUserBeRateItem.Content has unexpected type %T", x)
	}
	return nil
}

func _GameUserBeRateItem_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*GameUserBeRateItem)
	switch tag {
	case 2: // content.like_item
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(GameUserBeRateItem_LikeItem)
		err := b.DecodeMessage(msg)
		m.Content = &GameUserBeRateItem_LikeItem_{msg}
		return true, err
	case 3: // content.deduct_item
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(GameUserBeRateItem_DeductItem)
		err := b.DecodeMessage(msg)
		m.Content = &GameUserBeRateItem_DeductItem_{msg}
		return true, err
	default:
		return false, nil
	}
}

func _GameUserBeRateItem_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*GameUserBeRateItem)
	// content
	switch x := m.Content.(type) {
	case *GameUserBeRateItem_LikeItem_:
		s := proto.Size(x.LikeItem)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *GameUserBeRateItem_DeductItem_:
		s := proto.Size(x.DeductItem)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// 好评item
type GameUserBeRateItem_LikeItem struct {
	// 评价id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 用户id
	Uid      uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Account  string `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Nickname string `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex      int32  `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	// 关注状态
	IsFollow bool `protobuf:"varint,6,opt,name=is_follow,json=isFollow,proto3" json:"is_follow,omitempty"`
	// 评价时间文案
	RateTimeText string `protobuf:"bytes,7,opt,name=rate_time_text,json=rateTimeText,proto3" json:"rate_time_text,omitempty"`
	// 下发时间，单位:ms
	CreateTime int64 `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 筛选的评价标签
	Tags []string `protobuf:"bytes,9,rep,name=tags,proto3" json:"tags,omitempty"`
	// 用户自定义评价
	UserRateText         string   `protobuf:"bytes,10,opt,name=user_rate_text,json=userRateText,proto3" json:"user_rate_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameUserBeRateItem_LikeItem) Reset()         { *m = GameUserBeRateItem_LikeItem{} }
func (m *GameUserBeRateItem_LikeItem) String() string { return proto.CompactTextString(m) }
func (*GameUserBeRateItem_LikeItem) ProtoMessage()    {}
func (*GameUserBeRateItem_LikeItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{13, 0}
}
func (m *GameUserBeRateItem_LikeItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameUserBeRateItem_LikeItem.Unmarshal(m, b)
}
func (m *GameUserBeRateItem_LikeItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameUserBeRateItem_LikeItem.Marshal(b, m, deterministic)
}
func (dst *GameUserBeRateItem_LikeItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameUserBeRateItem_LikeItem.Merge(dst, src)
}
func (m *GameUserBeRateItem_LikeItem) XXX_Size() int {
	return xxx_messageInfo_GameUserBeRateItem_LikeItem.Size(m)
}
func (m *GameUserBeRateItem_LikeItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameUserBeRateItem_LikeItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameUserBeRateItem_LikeItem proto.InternalMessageInfo

func (m *GameUserBeRateItem_LikeItem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GameUserBeRateItem_LikeItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameUserBeRateItem_LikeItem) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GameUserBeRateItem_LikeItem) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GameUserBeRateItem_LikeItem) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GameUserBeRateItem_LikeItem) GetIsFollow() bool {
	if m != nil {
		return m.IsFollow
	}
	return false
}

func (m *GameUserBeRateItem_LikeItem) GetRateTimeText() string {
	if m != nil {
		return m.RateTimeText
	}
	return ""
}

func (m *GameUserBeRateItem_LikeItem) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GameUserBeRateItem_LikeItem) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *GameUserBeRateItem_LikeItem) GetUserRateText() string {
	if m != nil {
		return m.UserRateText
	}
	return ""
}

// 扣分记录item
type GameUserBeRateItem_DeductItem struct {
	// 扣分记录id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// 副标题
	Subtitle string `protobuf:"bytes,3,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// 下发时间，单位:ms
	CreateTime           int64    `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameUserBeRateItem_DeductItem) Reset()         { *m = GameUserBeRateItem_DeductItem{} }
func (m *GameUserBeRateItem_DeductItem) String() string { return proto.CompactTextString(m) }
func (*GameUserBeRateItem_DeductItem) ProtoMessage()    {}
func (*GameUserBeRateItem_DeductItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{13, 1}
}
func (m *GameUserBeRateItem_DeductItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameUserBeRateItem_DeductItem.Unmarshal(m, b)
}
func (m *GameUserBeRateItem_DeductItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameUserBeRateItem_DeductItem.Marshal(b, m, deterministic)
}
func (dst *GameUserBeRateItem_DeductItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameUserBeRateItem_DeductItem.Merge(dst, src)
}
func (m *GameUserBeRateItem_DeductItem) XXX_Size() int {
	return xxx_messageInfo_GameUserBeRateItem_DeductItem.Size(m)
}
func (m *GameUserBeRateItem_DeductItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameUserBeRateItem_DeductItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameUserBeRateItem_DeductItem proto.InternalMessageInfo

func (m *GameUserBeRateItem_DeductItem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GameUserBeRateItem_DeductItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GameUserBeRateItem_DeductItem) GetSubtitle() string {
	if m != nil {
		return m.Subtitle
	}
	return ""
}

func (m *GameUserBeRateItem_DeductItem) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type GetGameUserRateListReq struct {
	// 评价来源，互动提示信息可用于不同文案
	Source uint32 `protobuf:"varint,1,opt,name=source,proto3" json:"source,omitempty"`
	// 用户id
	RateUid uint32 `protobuf:"varint,2,opt,name=rate_uid,json=rateUid,proto3" json:"rate_uid,omitempty"`
	// last id，分页用
	LastId               string   `protobuf:"bytes,3,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameUserRateListReq) Reset()         { *m = GetGameUserRateListReq{} }
func (m *GetGameUserRateListReq) String() string { return proto.CompactTextString(m) }
func (*GetGameUserRateListReq) ProtoMessage()    {}
func (*GetGameUserRateListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{14}
}
func (m *GetGameUserRateListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameUserRateListReq.Unmarshal(m, b)
}
func (m *GetGameUserRateListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameUserRateListReq.Marshal(b, m, deterministic)
}
func (dst *GetGameUserRateListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameUserRateListReq.Merge(dst, src)
}
func (m *GetGameUserRateListReq) XXX_Size() int {
	return xxx_messageInfo_GetGameUserRateListReq.Size(m)
}
func (m *GetGameUserRateListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameUserRateListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameUserRateListReq proto.InternalMessageInfo

func (m *GetGameUserRateListReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *GetGameUserRateListReq) GetRateUid() uint32 {
	if m != nil {
		return m.RateUid
	}
	return 0
}

func (m *GetGameUserRateListReq) GetLastId() string {
	if m != nil {
		return m.LastId
	}
	return ""
}

type GetGameUserRateListResp struct {
	// 喜欢的评价文案
	LikeText string `protobuf:"bytes,1,opt,name=like_text,json=likeText,proto3" json:"like_text,omitempty"`
	// 不喜欢的评价文案
	DislikeText string `protobuf:"bytes,2,opt,name=dislike_text,json=dislikeText,proto3" json:"dislike_text,omitempty"`
	// 评价详情数据
	Data []*GameUserRateItem `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	// 是否已经到底部，true为底部
	LoadFinish bool `protobuf:"varint,4,opt,name=load_finish,json=loadFinish,proto3" json:"load_finish,omitempty"`
	// 6.60版本-赞标签最大选择个数，0表示无限制
	MaxPositiveSelectCount uint32 `protobuf:"varint,5,opt,name=max_positive_select_count,json=maxPositiveSelectCount,proto3" json:"max_positive_select_count,omitempty"`
	// 6.60版本-踩标签最大选择个数，0表示无限制
	MaxNegativeSelectCount uint32   `protobuf:"varint,6,opt,name=max_negative_select_count,json=maxNegativeSelectCount,proto3" json:"max_negative_select_count,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *GetGameUserRateListResp) Reset()         { *m = GetGameUserRateListResp{} }
func (m *GetGameUserRateListResp) String() string { return proto.CompactTextString(m) }
func (*GetGameUserRateListResp) ProtoMessage()    {}
func (*GetGameUserRateListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{15}
}
func (m *GetGameUserRateListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameUserRateListResp.Unmarshal(m, b)
}
func (m *GetGameUserRateListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameUserRateListResp.Marshal(b, m, deterministic)
}
func (dst *GetGameUserRateListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameUserRateListResp.Merge(dst, src)
}
func (m *GetGameUserRateListResp) XXX_Size() int {
	return xxx_messageInfo_GetGameUserRateListResp.Size(m)
}
func (m *GetGameUserRateListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameUserRateListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameUserRateListResp proto.InternalMessageInfo

func (m *GetGameUserRateListResp) GetLikeText() string {
	if m != nil {
		return m.LikeText
	}
	return ""
}

func (m *GetGameUserRateListResp) GetDislikeText() string {
	if m != nil {
		return m.DislikeText
	}
	return ""
}

func (m *GetGameUserRateListResp) GetData() []*GameUserRateItem {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *GetGameUserRateListResp) GetLoadFinish() bool {
	if m != nil {
		return m.LoadFinish
	}
	return false
}

func (m *GetGameUserRateListResp) GetMaxPositiveSelectCount() uint32 {
	if m != nil {
		return m.MaxPositiveSelectCount
	}
	return 0
}

func (m *GetGameUserRateListResp) GetMaxNegativeSelectCount() uint32 {
	if m != nil {
		return m.MaxNegativeSelectCount
	}
	return 0
}

type GameUserRateItem struct {
	// id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 用户id
	Uid      uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Account  string `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Nickname string `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex      uint32 `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	// 是否关注
	IsFollow bool `protobuf:"varint,6,opt,name=is_follow,json=isFollow,proto3" json:"is_follow,omitempty"`
	// 交互提示信息，如：5小时前跟Ta一起玩过王者荣耀
	IntroTips string `protobuf:"bytes,7,opt,name=intro_tips,json=introTips,proto3" json:"intro_tips,omitempty"`
	// 评论状态， 1-未评价，2-已评价，3-已过期
	Status uint32 `protobuf:"varint,8,opt,name=status,proto3" json:"status,omitempty"`
	// 评价意向，赞或踩，0-未选，1-赞，2-踩
	Attitude uint32 `protobuf:"varint,9,opt,name=attitude,proto3" json:"attitude,omitempty"`
	// 评价标签
	Tags []*GameUserRateTag `protobuf:"bytes,10,rep,name=tags,proto3" json:"tags,omitempty"`
	// 创建时间
	CreateTime uint64 `protobuf:"varint,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 用户自定义评价
	UserRateText         string   `protobuf:"bytes,12,opt,name=user_rate_text,json=userRateText,proto3" json:"user_rate_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameUserRateItem) Reset()         { *m = GameUserRateItem{} }
func (m *GameUserRateItem) String() string { return proto.CompactTextString(m) }
func (*GameUserRateItem) ProtoMessage()    {}
func (*GameUserRateItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{16}
}
func (m *GameUserRateItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameUserRateItem.Unmarshal(m, b)
}
func (m *GameUserRateItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameUserRateItem.Marshal(b, m, deterministic)
}
func (dst *GameUserRateItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameUserRateItem.Merge(dst, src)
}
func (m *GameUserRateItem) XXX_Size() int {
	return xxx_messageInfo_GameUserRateItem.Size(m)
}
func (m *GameUserRateItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameUserRateItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameUserRateItem proto.InternalMessageInfo

func (m *GameUserRateItem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GameUserRateItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameUserRateItem) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GameUserRateItem) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GameUserRateItem) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GameUserRateItem) GetIsFollow() bool {
	if m != nil {
		return m.IsFollow
	}
	return false
}

func (m *GameUserRateItem) GetIntroTips() string {
	if m != nil {
		return m.IntroTips
	}
	return ""
}

func (m *GameUserRateItem) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GameUserRateItem) GetAttitude() uint32 {
	if m != nil {
		return m.Attitude
	}
	return 0
}

func (m *GameUserRateItem) GetTags() []*GameUserRateTag {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *GameUserRateItem) GetCreateTime() uint64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GameUserRateItem) GetUserRateText() string {
	if m != nil {
		return m.UserRateText
	}
	return ""
}

// 标签详情
type GameUserRateTag struct {
	// 维度标签id，普通标签无
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 标签名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 1-正向标签，点赞展示；2-负向标签，踩展示
	TagType              uint32   `protobuf:"varint,3,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameUserRateTag) Reset()         { *m = GameUserRateTag{} }
func (m *GameUserRateTag) String() string { return proto.CompactTextString(m) }
func (*GameUserRateTag) ProtoMessage()    {}
func (*GameUserRateTag) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{17}
}
func (m *GameUserRateTag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameUserRateTag.Unmarshal(m, b)
}
func (m *GameUserRateTag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameUserRateTag.Marshal(b, m, deterministic)
}
func (dst *GameUserRateTag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameUserRateTag.Merge(dst, src)
}
func (m *GameUserRateTag) XXX_Size() int {
	return xxx_messageInfo_GameUserRateTag.Size(m)
}
func (m *GameUserRateTag) XXX_DiscardUnknown() {
	xxx_messageInfo_GameUserRateTag.DiscardUnknown(m)
}

var xxx_messageInfo_GameUserRateTag proto.InternalMessageInfo

func (m *GameUserRateTag) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GameUserRateTag) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GameUserRateTag) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

type BaseRequest struct {
	DeviceId             string   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,4,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	ClientIp             string   `protobuf:"bytes,5,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseRequest) Reset()         { *m = BaseRequest{} }
func (m *BaseRequest) String() string { return proto.CompactTextString(m) }
func (*BaseRequest) ProtoMessage()    {}
func (*BaseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{18}
}
func (m *BaseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseRequest.Unmarshal(m, b)
}
func (m *BaseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseRequest.Marshal(b, m, deterministic)
}
func (dst *BaseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseRequest.Merge(dst, src)
}
func (m *BaseRequest) XXX_Size() int {
	return xxx_messageInfo_BaseRequest.Size(m)
}
func (m *BaseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BaseRequest proto.InternalMessageInfo

func (m *BaseRequest) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *BaseRequest) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *BaseRequest) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *BaseRequest) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *BaseRequest) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

type SubmitGameUserRateReq struct {
	// 评价id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 评价意向，赞或踩，0-未选，1-赞，2-踩
	Attitude uint32 `protobuf:"varint,2,opt,name=attitude,proto3" json:"attitude,omitempty"`
	// 选择的评价标签
	SelectTags []*GameUserRateTag `protobuf:"bytes,3,rep,name=select_tags,json=selectTags,proto3" json:"select_tags,omitempty"`
	// 用户自定义评价
	UserRateText string `protobuf:"bytes,4,opt,name=user_rate_text,json=userRateText,proto3" json:"user_rate_text,omitempty"`
	// 基本参数
	BaseRequest          *BaseRequest `protobuf:"bytes,5,opt,name=base_request,json=baseRequest,proto3" json:"base_request,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SubmitGameUserRateReq) Reset()         { *m = SubmitGameUserRateReq{} }
func (m *SubmitGameUserRateReq) String() string { return proto.CompactTextString(m) }
func (*SubmitGameUserRateReq) ProtoMessage()    {}
func (*SubmitGameUserRateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{19}
}
func (m *SubmitGameUserRateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitGameUserRateReq.Unmarshal(m, b)
}
func (m *SubmitGameUserRateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitGameUserRateReq.Marshal(b, m, deterministic)
}
func (dst *SubmitGameUserRateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitGameUserRateReq.Merge(dst, src)
}
func (m *SubmitGameUserRateReq) XXX_Size() int {
	return xxx_messageInfo_SubmitGameUserRateReq.Size(m)
}
func (m *SubmitGameUserRateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitGameUserRateReq.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitGameUserRateReq proto.InternalMessageInfo

func (m *SubmitGameUserRateReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SubmitGameUserRateReq) GetAttitude() uint32 {
	if m != nil {
		return m.Attitude
	}
	return 0
}

func (m *SubmitGameUserRateReq) GetSelectTags() []*GameUserRateTag {
	if m != nil {
		return m.SelectTags
	}
	return nil
}

func (m *SubmitGameUserRateReq) GetUserRateText() string {
	if m != nil {
		return m.UserRateText
	}
	return ""
}

func (m *SubmitGameUserRateReq) GetBaseRequest() *BaseRequest {
	if m != nil {
		return m.BaseRequest
	}
	return nil
}

type SubmitGameUserRateResp struct {
	IsShieldPass         bool     `protobuf:"varint,1,opt,name=is_shield_pass,json=isShieldPass,proto3" json:"is_shield_pass,omitempty"`
	NotPassText          string   `protobuf:"bytes,2,opt,name=not_pass_text,json=notPassText,proto3" json:"not_pass_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitGameUserRateResp) Reset()         { *m = SubmitGameUserRateResp{} }
func (m *SubmitGameUserRateResp) String() string { return proto.CompactTextString(m) }
func (*SubmitGameUserRateResp) ProtoMessage()    {}
func (*SubmitGameUserRateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{20}
}
func (m *SubmitGameUserRateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitGameUserRateResp.Unmarshal(m, b)
}
func (m *SubmitGameUserRateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitGameUserRateResp.Marshal(b, m, deterministic)
}
func (dst *SubmitGameUserRateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitGameUserRateResp.Merge(dst, src)
}
func (m *SubmitGameUserRateResp) XXX_Size() int {
	return xxx_messageInfo_SubmitGameUserRateResp.Size(m)
}
func (m *SubmitGameUserRateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitGameUserRateResp.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitGameUserRateResp proto.InternalMessageInfo

func (m *SubmitGameUserRateResp) GetIsShieldPass() bool {
	if m != nil {
		return m.IsShieldPass
	}
	return false
}

func (m *SubmitGameUserRateResp) GetNotPassText() string {
	if m != nil {
		return m.NotPassText
	}
	return ""
}

// 获取红点信息
type GetRedDotInfoReq struct {
	// 业务类型, see enum game_red_dot_logic.proto RedDotBizType
	BizType uint32 `protobuf:"varint,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	// 业务场景key
	BizSuffixKey         string   `protobuf:"bytes,2,opt,name=biz_suffix_key,json=bizSuffixKey,proto3" json:"biz_suffix_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRedDotInfoReq) Reset()         { *m = GetRedDotInfoReq{} }
func (m *GetRedDotInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetRedDotInfoReq) ProtoMessage()    {}
func (*GetRedDotInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{21}
}
func (m *GetRedDotInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRedDotInfoReq.Unmarshal(m, b)
}
func (m *GetRedDotInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRedDotInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetRedDotInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRedDotInfoReq.Merge(dst, src)
}
func (m *GetRedDotInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetRedDotInfoReq.Size(m)
}
func (m *GetRedDotInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRedDotInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRedDotInfoReq proto.InternalMessageInfo

func (m *GetRedDotInfoReq) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *GetRedDotInfoReq) GetBizSuffixKey() string {
	if m != nil {
		return m.BizSuffixKey
	}
	return ""
}

type GetRedDotInfoResp struct {
	// 未读红点数
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRedDotInfoResp) Reset()         { *m = GetRedDotInfoResp{} }
func (m *GetRedDotInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetRedDotInfoResp) ProtoMessage()    {}
func (*GetRedDotInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{22}
}
func (m *GetRedDotInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRedDotInfoResp.Unmarshal(m, b)
}
func (m *GetRedDotInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRedDotInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetRedDotInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRedDotInfoResp.Merge(dst, src)
}
func (m *GetRedDotInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetRedDotInfoResp.Size(m)
}
func (m *GetRedDotInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRedDotInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRedDotInfoResp proto.InternalMessageInfo

func (m *GetRedDotInfoResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

// 标记红点已读
type MarkRedDotReadReq struct {
	// 业务类型, see enum game_red_dot_logic.proto RedDotBizType
	BizType uint32 `protobuf:"varint,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	// 业务场景key, 如ai群聊类型: 传群id
	BizSuffixKey         string   `protobuf:"bytes,2,opt,name=biz_suffix_key,json=bizSuffixKey,proto3" json:"biz_suffix_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkRedDotReadReq) Reset()         { *m = MarkRedDotReadReq{} }
func (m *MarkRedDotReadReq) String() string { return proto.CompactTextString(m) }
func (*MarkRedDotReadReq) ProtoMessage()    {}
func (*MarkRedDotReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{23}
}
func (m *MarkRedDotReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkRedDotReadReq.Unmarshal(m, b)
}
func (m *MarkRedDotReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkRedDotReadReq.Marshal(b, m, deterministic)
}
func (dst *MarkRedDotReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkRedDotReadReq.Merge(dst, src)
}
func (m *MarkRedDotReadReq) XXX_Size() int {
	return xxx_messageInfo_MarkRedDotReadReq.Size(m)
}
func (m *MarkRedDotReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkRedDotReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkRedDotReadReq proto.InternalMessageInfo

func (m *MarkRedDotReadReq) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *MarkRedDotReadReq) GetBizSuffixKey() string {
	if m != nil {
		return m.BizSuffixKey
	}
	return ""
}

type MarkRedDotReadResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkRedDotReadResp) Reset()         { *m = MarkRedDotReadResp{} }
func (m *MarkRedDotReadResp) String() string { return proto.CompactTextString(m) }
func (*MarkRedDotReadResp) ProtoMessage()    {}
func (*MarkRedDotReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{24}
}
func (m *MarkRedDotReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkRedDotReadResp.Unmarshal(m, b)
}
func (m *MarkRedDotReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkRedDotReadResp.Marshal(b, m, deterministic)
}
func (dst *MarkRedDotReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkRedDotReadResp.Merge(dst, src)
}
func (m *MarkRedDotReadResp) XXX_Size() int {
	return xxx_messageInfo_MarkRedDotReadResp.Size(m)
}
func (m *MarkRedDotReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkRedDotReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkRedDotReadResp proto.InternalMessageInfo

// 获取聚合红点信息，目前只有ai社区用到
type GetAggregateRedDotInfoReq struct {
	// 业务类型, see enum game_red_dot_logic.proto RedDotBizType
	BizType              uint32   `protobuf:"varint,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAggregateRedDotInfoReq) Reset()         { *m = GetAggregateRedDotInfoReq{} }
func (m *GetAggregateRedDotInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetAggregateRedDotInfoReq) ProtoMessage()    {}
func (*GetAggregateRedDotInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{25}
}
func (m *GetAggregateRedDotInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAggregateRedDotInfoReq.Unmarshal(m, b)
}
func (m *GetAggregateRedDotInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAggregateRedDotInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetAggregateRedDotInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAggregateRedDotInfoReq.Merge(dst, src)
}
func (m *GetAggregateRedDotInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetAggregateRedDotInfoReq.Size(m)
}
func (m *GetAggregateRedDotInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAggregateRedDotInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAggregateRedDotInfoReq proto.InternalMessageInfo

func (m *GetAggregateRedDotInfoReq) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

type GetAggregateRedDotInfoResp struct {
	// 未读红点数
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAggregateRedDotInfoResp) Reset()         { *m = GetAggregateRedDotInfoResp{} }
func (m *GetAggregateRedDotInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetAggregateRedDotInfoResp) ProtoMessage()    {}
func (*GetAggregateRedDotInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{26}
}
func (m *GetAggregateRedDotInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAggregateRedDotInfoResp.Unmarshal(m, b)
}
func (m *GetAggregateRedDotInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAggregateRedDotInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetAggregateRedDotInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAggregateRedDotInfoResp.Merge(dst, src)
}
func (m *GetAggregateRedDotInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetAggregateRedDotInfoResp.Size(m)
}
func (m *GetAggregateRedDotInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAggregateRedDotInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAggregateRedDotInfoResp proto.InternalMessageInfo

func (m *GetAggregateRedDotInfoResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

// 标记聚合红点已读，目前只有ai社区用到
type MarkAggregateRedDotReadReq struct {
	// 业务类型, see enum game_red_dot_logic.proto RedDotBizType
	BizType              uint32   `protobuf:"varint,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkAggregateRedDotReadReq) Reset()         { *m = MarkAggregateRedDotReadReq{} }
func (m *MarkAggregateRedDotReadReq) String() string { return proto.CompactTextString(m) }
func (*MarkAggregateRedDotReadReq) ProtoMessage()    {}
func (*MarkAggregateRedDotReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{27}
}
func (m *MarkAggregateRedDotReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkAggregateRedDotReadReq.Unmarshal(m, b)
}
func (m *MarkAggregateRedDotReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkAggregateRedDotReadReq.Marshal(b, m, deterministic)
}
func (dst *MarkAggregateRedDotReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkAggregateRedDotReadReq.Merge(dst, src)
}
func (m *MarkAggregateRedDotReadReq) XXX_Size() int {
	return xxx_messageInfo_MarkAggregateRedDotReadReq.Size(m)
}
func (m *MarkAggregateRedDotReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkAggregateRedDotReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkAggregateRedDotReadReq proto.InternalMessageInfo

func (m *MarkAggregateRedDotReadReq) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

type MarkAggregateRedDotReadResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkAggregateRedDotReadResp) Reset()         { *m = MarkAggregateRedDotReadResp{} }
func (m *MarkAggregateRedDotReadResp) String() string { return proto.CompactTextString(m) }
func (*MarkAggregateRedDotReadResp) ProtoMessage()    {}
func (*MarkAggregateRedDotReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{28}
}
func (m *MarkAggregateRedDotReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkAggregateRedDotReadResp.Unmarshal(m, b)
}
func (m *MarkAggregateRedDotReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkAggregateRedDotReadResp.Marshal(b, m, deterministic)
}
func (dst *MarkAggregateRedDotReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkAggregateRedDotReadResp.Merge(dst, src)
}
func (m *MarkAggregateRedDotReadResp) XXX_Size() int {
	return xxx_messageInfo_MarkAggregateRedDotReadResp.Size(m)
}
func (m *MarkAggregateRedDotReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkAggregateRedDotReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkAggregateRedDotReadResp proto.InternalMessageInfo

type GetUserInfosReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInfosReq) Reset()         { *m = GetUserInfosReq{} }
func (m *GetUserInfosReq) String() string { return proto.CompactTextString(m) }
func (*GetUserInfosReq) ProtoMessage()    {}
func (*GetUserInfosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{29}
}
func (m *GetUserInfosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInfosReq.Unmarshal(m, b)
}
func (m *GetUserInfosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInfosReq.Marshal(b, m, deterministic)
}
func (dst *GetUserInfosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInfosReq.Merge(dst, src)
}
func (m *GetUserInfosReq) XXX_Size() int {
	return xxx_messageInfo_GetUserInfosReq.Size(m)
}
func (m *GetUserInfosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInfosReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInfosReq proto.InternalMessageInfo

func (m *GetUserInfosReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type GetUserInfosResp struct {
	UserInfos            []*UserInfo `protobuf:"bytes,1,rep,name=user_infos,json=userInfos,proto3" json:"user_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetUserInfosResp) Reset()         { *m = GetUserInfosResp{} }
func (m *GetUserInfosResp) String() string { return proto.CompactTextString(m) }
func (*GetUserInfosResp) ProtoMessage()    {}
func (*GetUserInfosResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{30}
}
func (m *GetUserInfosResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInfosResp.Unmarshal(m, b)
}
func (m *GetUserInfosResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInfosResp.Marshal(b, m, deterministic)
}
func (dst *GetUserInfosResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInfosResp.Merge(dst, src)
}
func (m *GetUserInfosResp) XXX_Size() int {
	return xxx_messageInfo_GetUserInfosResp.Size(m)
}
func (m *GetUserInfosResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInfosResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInfosResp proto.InternalMessageInfo

func (m *GetUserInfosResp) GetUserInfos() []*UserInfo {
	if m != nil {
		return m.UserInfos
	}
	return nil
}

// 获取推荐评价标签
type ReorderRateTagsReq struct {
	// 用户自定义评价
	UserRateText string `protobuf:"bytes,1,opt,name=user_rate_text,json=userRateText,proto3" json:"user_rate_text,omitempty"`
	// 评价id
	RateItemId           string   `protobuf:"bytes,2,opt,name=rate_item_id,json=rateItemId,proto3" json:"rate_item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReorderRateTagsReq) Reset()         { *m = ReorderRateTagsReq{} }
func (m *ReorderRateTagsReq) String() string { return proto.CompactTextString(m) }
func (*ReorderRateTagsReq) ProtoMessage()    {}
func (*ReorderRateTagsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{31}
}
func (m *ReorderRateTagsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReorderRateTagsReq.Unmarshal(m, b)
}
func (m *ReorderRateTagsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReorderRateTagsReq.Marshal(b, m, deterministic)
}
func (dst *ReorderRateTagsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReorderRateTagsReq.Merge(dst, src)
}
func (m *ReorderRateTagsReq) XXX_Size() int {
	return xxx_messageInfo_ReorderRateTagsReq.Size(m)
}
func (m *ReorderRateTagsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReorderRateTagsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReorderRateTagsReq proto.InternalMessageInfo

func (m *ReorderRateTagsReq) GetUserRateText() string {
	if m != nil {
		return m.UserRateText
	}
	return ""
}

func (m *ReorderRateTagsReq) GetRateItemId() string {
	if m != nil {
		return m.RateItemId
	}
	return ""
}

type ReorderRateTagsResp struct {
	// 返回推荐标签列表
	RateTags             []*GameUserRateTag `protobuf:"bytes,1,rep,name=rate_tags,json=rateTags,proto3" json:"rate_tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ReorderRateTagsResp) Reset()         { *m = ReorderRateTagsResp{} }
func (m *ReorderRateTagsResp) String() string { return proto.CompactTextString(m) }
func (*ReorderRateTagsResp) ProtoMessage()    {}
func (*ReorderRateTagsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_http_logic_d9d6ba513ec5a86c, []int{32}
}
func (m *ReorderRateTagsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReorderRateTagsResp.Unmarshal(m, b)
}
func (m *ReorderRateTagsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReorderRateTagsResp.Marshal(b, m, deterministic)
}
func (dst *ReorderRateTagsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReorderRateTagsResp.Merge(dst, src)
}
func (m *ReorderRateTagsResp) XXX_Size() int {
	return xxx_messageInfo_ReorderRateTagsResp.Size(m)
}
func (m *ReorderRateTagsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReorderRateTagsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReorderRateTagsResp proto.InternalMessageInfo

func (m *ReorderRateTagsResp) GetRateTags() []*GameUserRateTag {
	if m != nil {
		return m.RateTags
	}
	return nil
}

func init() {
	proto.RegisterType((*H5Question)(nil), "game_http_logic.H5Question")
	proto.RegisterType((*H5Question_Option)(nil), "game_http_logic.H5Question.Option")
	proto.RegisterType((*GetSelfRateQuestionsReq)(nil), "game_http_logic.GetSelfRateQuestionsReq")
	proto.RegisterType((*GetSelfRateQuestionsResp)(nil), "game_http_logic.GetSelfRateQuestionsResp")
	proto.RegisterType((*SelfRateResult)(nil), "game_http_logic.SelfRateResult")
	proto.RegisterType((*SubmitSelfRateResultReq)(nil), "game_http_logic.SubmitSelfRateResultReq")
	proto.RegisterType((*GetUserImageRateResultReq)(nil), "game_http_logic.GetUserImageRateResultReq")
	proto.RegisterType((*GameUserPersonalImageRespItem)(nil), "game_http_logic.GameUserPersonalImageRespItem")
	proto.RegisterType((*DimensionItem)(nil), "game_http_logic.DimensionItem")
	proto.RegisterType((*UserInfo)(nil), "game_http_logic.UserInfo")
	proto.RegisterType((*GetUserInfoReq)(nil), "game_http_logic.GetUserInfoReq")
	proto.RegisterType((*GetUserInfoResp)(nil), "game_http_logic.GetUserInfoResp")
	proto.RegisterType((*GetGameUserBeRateListReq)(nil), "game_http_logic.GetGameUserBeRateListReq")
	proto.RegisterType((*GetGameUserBeRateListResp)(nil), "game_http_logic.GetGameUserBeRateListResp")
	proto.RegisterType((*GameUserBeRateItem)(nil), "game_http_logic.GameUserBeRateItem")
	proto.RegisterType((*GameUserBeRateItem_LikeItem)(nil), "game_http_logic.GameUserBeRateItem.LikeItem")
	proto.RegisterType((*GameUserBeRateItem_DeductItem)(nil), "game_http_logic.GameUserBeRateItem.DeductItem")
	proto.RegisterType((*GetGameUserRateListReq)(nil), "game_http_logic.GetGameUserRateListReq")
	proto.RegisterType((*GetGameUserRateListResp)(nil), "game_http_logic.GetGameUserRateListResp")
	proto.RegisterType((*GameUserRateItem)(nil), "game_http_logic.GameUserRateItem")
	proto.RegisterType((*GameUserRateTag)(nil), "game_http_logic.GameUserRateTag")
	proto.RegisterType((*BaseRequest)(nil), "game_http_logic.BaseRequest")
	proto.RegisterType((*SubmitGameUserRateReq)(nil), "game_http_logic.SubmitGameUserRateReq")
	proto.RegisterType((*SubmitGameUserRateResp)(nil), "game_http_logic.SubmitGameUserRateResp")
	proto.RegisterType((*GetRedDotInfoReq)(nil), "game_http_logic.GetRedDotInfoReq")
	proto.RegisterType((*GetRedDotInfoResp)(nil), "game_http_logic.GetRedDotInfoResp")
	proto.RegisterType((*MarkRedDotReadReq)(nil), "game_http_logic.MarkRedDotReadReq")
	proto.RegisterType((*MarkRedDotReadResp)(nil), "game_http_logic.MarkRedDotReadResp")
	proto.RegisterType((*GetAggregateRedDotInfoReq)(nil), "game_http_logic.GetAggregateRedDotInfoReq")
	proto.RegisterType((*GetAggregateRedDotInfoResp)(nil), "game_http_logic.GetAggregateRedDotInfoResp")
	proto.RegisterType((*MarkAggregateRedDotReadReq)(nil), "game_http_logic.MarkAggregateRedDotReadReq")
	proto.RegisterType((*MarkAggregateRedDotReadResp)(nil), "game_http_logic.MarkAggregateRedDotReadResp")
	proto.RegisterType((*GetUserInfosReq)(nil), "game_http_logic.GetUserInfosReq")
	proto.RegisterType((*GetUserInfosResp)(nil), "game_http_logic.GetUserInfosResp")
	proto.RegisterType((*ReorderRateTagsReq)(nil), "game_http_logic.ReorderRateTagsReq")
	proto.RegisterType((*ReorderRateTagsResp)(nil), "game_http_logic.ReorderRateTagsResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GameHttpLogicClient is the client API for GameHttpLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameHttpLogicClient interface {
	// 获取开黑形象自评题目
	GetSelfRateQuestions(ctx context.Context, in *GetSelfRateQuestionsReq, opts ...grpc.CallOption) (*GetSelfRateQuestionsResp, error)
	// 提交开黑形象自评结果
	SubmitSelfRateResult(ctx context.Context, in *SubmitSelfRateResultReq, opts ...grpc.CallOption) (*SubmitGameUserRateResp, error)
	// 获取用户测评形象结果
	GetUserImageRateResult(ctx context.Context, in *GetUserImageRateResultReq, opts ...grpc.CallOption) (*GameUserPersonalImageRespItem, error)
	// 获取用户信息
	GetUserInfo(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error)
	// 获取用户被评列表
	GetGameUserBeRateList(ctx context.Context, in *GetGameUserBeRateListReq, opts ...grpc.CallOption) (*GetGameUserBeRateListResp, error)
	// 获取用户评价列表
	GetGameUserRateList(ctx context.Context, in *GetGameUserRateListReq, opts ...grpc.CallOption) (*GetGameUserRateListResp, error)
	// 提交用户评价
	SubmitGameUserRate(ctx context.Context, in *SubmitGameUserRateReq, opts ...grpc.CallOption) (*SubmitGameUserRateResp, error)
	// 获取红点信息
	GetRedDotInfo(ctx context.Context, in *GetRedDotInfoReq, opts ...grpc.CallOption) (*GetRedDotInfoResp, error)
	// 标记红点已读
	MarkRedDotRead(ctx context.Context, in *MarkRedDotReadReq, opts ...grpc.CallOption) (*MarkRedDotReadResp, error)
	// 获取聚合红点信息
	GetAggregateRedDotInfo(ctx context.Context, in *GetAggregateRedDotInfoReq, opts ...grpc.CallOption) (*GetAggregateRedDotInfoResp, error)
	// 标记聚合红点已读
	MarkAggregateRedDotRead(ctx context.Context, in *MarkAggregateRedDotReadReq, opts ...grpc.CallOption) (*MarkAggregateRedDotReadResp, error)
	// 获取用户信息
	GetUserInfos(ctx context.Context, in *GetUserInfosReq, opts ...grpc.CallOption) (*GetUserInfosResp, error)
	// 获取推荐评价标签
	ReorderRateTags(ctx context.Context, in *ReorderRateTagsReq, opts ...grpc.CallOption) (*ReorderRateTagsResp, error)
}

type gameHttpLogicClient struct {
	cc *grpc.ClientConn
}

func NewGameHttpLogicClient(cc *grpc.ClientConn) GameHttpLogicClient {
	return &gameHttpLogicClient{cc}
}

func (c *gameHttpLogicClient) GetSelfRateQuestions(ctx context.Context, in *GetSelfRateQuestionsReq, opts ...grpc.CallOption) (*GetSelfRateQuestionsResp, error) {
	out := new(GetSelfRateQuestionsResp)
	err := c.cc.Invoke(ctx, "/game_http_logic.GameHttpLogic/GetSelfRateQuestions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHttpLogicClient) SubmitSelfRateResult(ctx context.Context, in *SubmitSelfRateResultReq, opts ...grpc.CallOption) (*SubmitGameUserRateResp, error) {
	out := new(SubmitGameUserRateResp)
	err := c.cc.Invoke(ctx, "/game_http_logic.GameHttpLogic/SubmitSelfRateResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHttpLogicClient) GetUserImageRateResult(ctx context.Context, in *GetUserImageRateResultReq, opts ...grpc.CallOption) (*GameUserPersonalImageRespItem, error) {
	out := new(GameUserPersonalImageRespItem)
	err := c.cc.Invoke(ctx, "/game_http_logic.GameHttpLogic/GetUserImageRateResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHttpLogicClient) GetUserInfo(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error) {
	out := new(GetUserInfoResp)
	err := c.cc.Invoke(ctx, "/game_http_logic.GameHttpLogic/GetUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHttpLogicClient) GetGameUserBeRateList(ctx context.Context, in *GetGameUserBeRateListReq, opts ...grpc.CallOption) (*GetGameUserBeRateListResp, error) {
	out := new(GetGameUserBeRateListResp)
	err := c.cc.Invoke(ctx, "/game_http_logic.GameHttpLogic/GetGameUserBeRateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHttpLogicClient) GetGameUserRateList(ctx context.Context, in *GetGameUserRateListReq, opts ...grpc.CallOption) (*GetGameUserRateListResp, error) {
	out := new(GetGameUserRateListResp)
	err := c.cc.Invoke(ctx, "/game_http_logic.GameHttpLogic/GetGameUserRateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHttpLogicClient) SubmitGameUserRate(ctx context.Context, in *SubmitGameUserRateReq, opts ...grpc.CallOption) (*SubmitGameUserRateResp, error) {
	out := new(SubmitGameUserRateResp)
	err := c.cc.Invoke(ctx, "/game_http_logic.GameHttpLogic/SubmitGameUserRate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHttpLogicClient) GetRedDotInfo(ctx context.Context, in *GetRedDotInfoReq, opts ...grpc.CallOption) (*GetRedDotInfoResp, error) {
	out := new(GetRedDotInfoResp)
	err := c.cc.Invoke(ctx, "/game_http_logic.GameHttpLogic/GetRedDotInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHttpLogicClient) MarkRedDotRead(ctx context.Context, in *MarkRedDotReadReq, opts ...grpc.CallOption) (*MarkRedDotReadResp, error) {
	out := new(MarkRedDotReadResp)
	err := c.cc.Invoke(ctx, "/game_http_logic.GameHttpLogic/MarkRedDotRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHttpLogicClient) GetAggregateRedDotInfo(ctx context.Context, in *GetAggregateRedDotInfoReq, opts ...grpc.CallOption) (*GetAggregateRedDotInfoResp, error) {
	out := new(GetAggregateRedDotInfoResp)
	err := c.cc.Invoke(ctx, "/game_http_logic.GameHttpLogic/GetAggregateRedDotInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHttpLogicClient) MarkAggregateRedDotRead(ctx context.Context, in *MarkAggregateRedDotReadReq, opts ...grpc.CallOption) (*MarkAggregateRedDotReadResp, error) {
	out := new(MarkAggregateRedDotReadResp)
	err := c.cc.Invoke(ctx, "/game_http_logic.GameHttpLogic/MarkAggregateRedDotRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHttpLogicClient) GetUserInfos(ctx context.Context, in *GetUserInfosReq, opts ...grpc.CallOption) (*GetUserInfosResp, error) {
	out := new(GetUserInfosResp)
	err := c.cc.Invoke(ctx, "/game_http_logic.GameHttpLogic/GetUserInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHttpLogicClient) ReorderRateTags(ctx context.Context, in *ReorderRateTagsReq, opts ...grpc.CallOption) (*ReorderRateTagsResp, error) {
	out := new(ReorderRateTagsResp)
	err := c.cc.Invoke(ctx, "/game_http_logic.GameHttpLogic/ReorderRateTags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameHttpLogicServer is the server API for GameHttpLogic service.
type GameHttpLogicServer interface {
	// 获取开黑形象自评题目
	GetSelfRateQuestions(context.Context, *GetSelfRateQuestionsReq) (*GetSelfRateQuestionsResp, error)
	// 提交开黑形象自评结果
	SubmitSelfRateResult(context.Context, *SubmitSelfRateResultReq) (*SubmitGameUserRateResp, error)
	// 获取用户测评形象结果
	GetUserImageRateResult(context.Context, *GetUserImageRateResultReq) (*GameUserPersonalImageRespItem, error)
	// 获取用户信息
	GetUserInfo(context.Context, *GetUserInfoReq) (*GetUserInfoResp, error)
	// 获取用户被评列表
	GetGameUserBeRateList(context.Context, *GetGameUserBeRateListReq) (*GetGameUserBeRateListResp, error)
	// 获取用户评价列表
	GetGameUserRateList(context.Context, *GetGameUserRateListReq) (*GetGameUserRateListResp, error)
	// 提交用户评价
	SubmitGameUserRate(context.Context, *SubmitGameUserRateReq) (*SubmitGameUserRateResp, error)
	// 获取红点信息
	GetRedDotInfo(context.Context, *GetRedDotInfoReq) (*GetRedDotInfoResp, error)
	// 标记红点已读
	MarkRedDotRead(context.Context, *MarkRedDotReadReq) (*MarkRedDotReadResp, error)
	// 获取聚合红点信息
	GetAggregateRedDotInfo(context.Context, *GetAggregateRedDotInfoReq) (*GetAggregateRedDotInfoResp, error)
	// 标记聚合红点已读
	MarkAggregateRedDotRead(context.Context, *MarkAggregateRedDotReadReq) (*MarkAggregateRedDotReadResp, error)
	// 获取用户信息
	GetUserInfos(context.Context, *GetUserInfosReq) (*GetUserInfosResp, error)
	// 获取推荐评价标签
	ReorderRateTags(context.Context, *ReorderRateTagsReq) (*ReorderRateTagsResp, error)
}

func RegisterGameHttpLogicServer(s *grpc.Server, srv GameHttpLogicServer) {
	s.RegisterService(&_GameHttpLogic_serviceDesc, srv)
}

func _GameHttpLogic_GetSelfRateQuestions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSelfRateQuestionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHttpLogicServer).GetSelfRateQuestions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_http_logic.GameHttpLogic/GetSelfRateQuestions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHttpLogicServer).GetSelfRateQuestions(ctx, req.(*GetSelfRateQuestionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHttpLogic_SubmitSelfRateResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitSelfRateResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHttpLogicServer).SubmitSelfRateResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_http_logic.GameHttpLogic/SubmitSelfRateResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHttpLogicServer).SubmitSelfRateResult(ctx, req.(*SubmitSelfRateResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHttpLogic_GetUserImageRateResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserImageRateResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHttpLogicServer).GetUserImageRateResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_http_logic.GameHttpLogic/GetUserImageRateResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHttpLogicServer).GetUserImageRateResult(ctx, req.(*GetUserImageRateResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHttpLogic_GetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHttpLogicServer).GetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_http_logic.GameHttpLogic/GetUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHttpLogicServer).GetUserInfo(ctx, req.(*GetUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHttpLogic_GetGameUserBeRateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameUserBeRateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHttpLogicServer).GetGameUserBeRateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_http_logic.GameHttpLogic/GetGameUserBeRateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHttpLogicServer).GetGameUserBeRateList(ctx, req.(*GetGameUserBeRateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHttpLogic_GetGameUserRateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameUserRateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHttpLogicServer).GetGameUserRateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_http_logic.GameHttpLogic/GetGameUserRateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHttpLogicServer).GetGameUserRateList(ctx, req.(*GetGameUserRateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHttpLogic_SubmitGameUserRate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitGameUserRateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHttpLogicServer).SubmitGameUserRate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_http_logic.GameHttpLogic/SubmitGameUserRate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHttpLogicServer).SubmitGameUserRate(ctx, req.(*SubmitGameUserRateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHttpLogic_GetRedDotInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRedDotInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHttpLogicServer).GetRedDotInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_http_logic.GameHttpLogic/GetRedDotInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHttpLogicServer).GetRedDotInfo(ctx, req.(*GetRedDotInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHttpLogic_MarkRedDotRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkRedDotReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHttpLogicServer).MarkRedDotRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_http_logic.GameHttpLogic/MarkRedDotRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHttpLogicServer).MarkRedDotRead(ctx, req.(*MarkRedDotReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHttpLogic_GetAggregateRedDotInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAggregateRedDotInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHttpLogicServer).GetAggregateRedDotInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_http_logic.GameHttpLogic/GetAggregateRedDotInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHttpLogicServer).GetAggregateRedDotInfo(ctx, req.(*GetAggregateRedDotInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHttpLogic_MarkAggregateRedDotRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkAggregateRedDotReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHttpLogicServer).MarkAggregateRedDotRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_http_logic.GameHttpLogic/MarkAggregateRedDotRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHttpLogicServer).MarkAggregateRedDotRead(ctx, req.(*MarkAggregateRedDotReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHttpLogic_GetUserInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInfosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHttpLogicServer).GetUserInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_http_logic.GameHttpLogic/GetUserInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHttpLogicServer).GetUserInfos(ctx, req.(*GetUserInfosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHttpLogic_ReorderRateTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReorderRateTagsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHttpLogicServer).ReorderRateTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_http_logic.GameHttpLogic/ReorderRateTags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHttpLogicServer).ReorderRateTags(ctx, req.(*ReorderRateTagsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameHttpLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "game_http_logic.GameHttpLogic",
	HandlerType: (*GameHttpLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSelfRateQuestions",
			Handler:    _GameHttpLogic_GetSelfRateQuestions_Handler,
		},
		{
			MethodName: "SubmitSelfRateResult",
			Handler:    _GameHttpLogic_SubmitSelfRateResult_Handler,
		},
		{
			MethodName: "GetUserImageRateResult",
			Handler:    _GameHttpLogic_GetUserImageRateResult_Handler,
		},
		{
			MethodName: "GetUserInfo",
			Handler:    _GameHttpLogic_GetUserInfo_Handler,
		},
		{
			MethodName: "GetGameUserBeRateList",
			Handler:    _GameHttpLogic_GetGameUserBeRateList_Handler,
		},
		{
			MethodName: "GetGameUserRateList",
			Handler:    _GameHttpLogic_GetGameUserRateList_Handler,
		},
		{
			MethodName: "SubmitGameUserRate",
			Handler:    _GameHttpLogic_SubmitGameUserRate_Handler,
		},
		{
			MethodName: "GetRedDotInfo",
			Handler:    _GameHttpLogic_GetRedDotInfo_Handler,
		},
		{
			MethodName: "MarkRedDotRead",
			Handler:    _GameHttpLogic_MarkRedDotRead_Handler,
		},
		{
			MethodName: "GetAggregateRedDotInfo",
			Handler:    _GameHttpLogic_GetAggregateRedDotInfo_Handler,
		},
		{
			MethodName: "MarkAggregateRedDotRead",
			Handler:    _GameHttpLogic_MarkAggregateRedDotRead_Handler,
		},
		{
			MethodName: "GetUserInfos",
			Handler:    _GameHttpLogic_GetUserInfos_Handler,
		},
		{
			MethodName: "ReorderRateTags",
			Handler:    _GameHttpLogic_ReorderRateTags_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/game-http-logic/game-http-logic.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/game-http-logic/game-http-logic.proto", fileDescriptor_game_http_logic_d9d6ba513ec5a86c)
}

var fileDescriptor_game_http_logic_d9d6ba513ec5a86c = []byte{
	// 2072 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x59, 0x5f, 0x8f, 0x1b, 0x49,
	0x11, 0x67, 0x6c, 0xef, 0xae, 0x5d, 0x5e, 0x6f, 0xf6, 0x3a, 0x9b, 0xc4, 0x71, 0x2e, 0x64, 0xd3,
	0x97, 0xbb, 0x6c, 0xfe, 0xac, 0x9d, 0x6c, 0x2e, 0x77, 0xb7, 0x12, 0x08, 0x5d, 0x58, 0x5d, 0x62,
	0x25, 0x1c, 0xb9, 0xd9, 0xcd, 0x21, 0x21, 0xa4, 0x51, 0xdb, 0xd3, 0xeb, 0xb4, 0x3c, 0x9e, 0x99,
	0x4c, 0xb7, 0x93, 0xdd, 0x3c, 0x82, 0x04, 0x12, 0x4f, 0x70, 0xbc, 0xf2, 0x80, 0x10, 0x12, 0x08,
	0x21, 0xf8, 0x30, 0x7c, 0x03, 0xc4, 0x0b, 0x8a, 0xf8, 0x0e, 0xa8, 0xab, 0x67, 0xec, 0xf1, 0xcc,
	0xd8, 0x6b, 0x24, 0xc4, 0xdb, 0x74, 0x75, 0x75, 0xf5, 0xaf, 0xab, 0xeb, 0x57, 0x55, 0x6d, 0xc3,
	0xc7, 0x4a, 0x75, 0x5e, 0x8d, 0x45, 0x7f, 0x28, 0x85, 0xf7, 0x9a, 0x47, 0x9d, 0x01, 0x1b, 0xf1,
	0xdd, 0x97, 0x4a, 0x85, 0xbb, 0x5e, 0x30, 0x10, 0xfd, 0xec, 0xb8, 0x1d, 0x46, 0x81, 0x0a, 0xc8,
	0x39, 0x2d, 0x76, 0xb4, 0xd8, 0x41, 0x71, 0xeb, 0xfd, 0x41, 0x10, 0x0c, 0x3c, 0xde, 0x61, 0xa1,
	0xe8, 0x30, 0xdf, 0x0f, 0x14, 0x53, 0x22, 0xf0, 0xa5, 0x51, 0xa7, 0xef, 0x2c, 0x80, 0x27, 0x0f,
	0xbf, 0x1a, 0x73, 0xa9, 0xa5, 0xe4, 0x1a, 0xd4, 0x5f, 0xc5, 0xdf, 0x8e, 0x70, 0x9b, 0xd6, 0xb6,
	0xb5, 0x53, 0xb3, 0x21, 0x11, 0x75, 0x5d, 0xb2, 0x0d, 0x75, 0x97, 0xcb, 0x7e, 0x24, 0x42, 0x2d,
	0x68, 0x96, 0x50, 0x21, 0x2d, 0x22, 0xdf, 0x81, 0xb5, 0x00, 0xbf, 0x64, 0xb3, 0xbc, 0x5d, 0xde,
	0xa9, 0xef, 0xd1, 0x76, 0x06, 0x52, 0x7b, 0xba, 0x61, 0xfb, 0x87, 0xa8, 0x6a, 0x27, 0x4b, 0xc8,
	0x75, 0x58, 0x77, 0xc5, 0x88, 0xfb, 0x32, 0x46, 0x50, 0x89, 0x37, 0x48, 0x64, 0x5d, 0xb7, 0xb5,
	0x0f, 0xab, 0x66, 0x15, 0xb9, 0x00, 0xab, 0x41, 0xa8, 0x12, 0xa0, 0x0d, 0x7b, 0x25, 0x08, 0x55,
	0xd7, 0x25, 0x4d, 0x58, 0xeb, 0x07, 0xbe, 0xe2, 0xbe, 0x8a, 0xf1, 0x25, 0x43, 0x7a, 0x0f, 0x2e,
	0x3d, 0xe6, 0xea, 0x90, 0x7b, 0xc7, 0x36, 0x53, 0x3c, 0x01, 0x21, 0x6d, 0xfe, 0x4a, 0xdb, 0x52,
	0xac, 0x97, 0xb2, 0xa5, 0x58, 0xaf, 0xeb, 0xd2, 0x17, 0xd0, 0x2c, 0x5e, 0x21, 0x43, 0xb2, 0x0f,
	0xb5, 0xc4, 0x33, 0xb2, 0x69, 0xe1, 0x59, 0xaf, 0x2c, 0x38, 0xab, 0x3d, 0xd5, 0xa6, 0x43, 0xd8,
	0x48, 0x6c, 0xda, 0x5c, 0x8e, 0x3d, 0x75, 0xb6, 0xe7, 0xa7, 0x87, 0x2d, 0xa5, 0x0f, 0x9b, 0x75,
	0x58, 0x39, 0xe7, 0x30, 0x3a, 0x84, 0x4b, 0x87, 0xe3, 0xde, 0x48, 0xa8, 0xd9, 0x2d, 0xe7, 0x9f,
	0x9a, 0xec, 0xc3, 0x5a, 0x84, 0x3a, 0xb2, 0x59, 0xc2, 0x73, 0x5d, 0xcb, 0x9d, 0x2b, 0x63, 0x2b,
	0xd1, 0xa7, 0x07, 0x70, 0xf9, 0x31, 0x57, 0x2f, 0x24, 0x8f, 0xba, 0x23, 0x36, 0xe0, 0xb3, 0xdb,
	0x6d, 0x42, 0x79, 0x3c, 0xd9, 0x4b, 0x7f, 0xa6, 0x00, 0x94, 0xd2, 0x6e, 0x7f, 0x57, 0x82, 0xab,
	0x8f, 0xd9, 0x88, 0x6b, 0x3b, 0xcf, 0x79, 0x24, 0x03, 0x9f, 0x79, 0xc6, 0x1e, 0x97, 0x61, 0x57,
	0xf1, 0x11, 0xd9, 0x83, 0x8a, 0x50, 0x7c, 0x14, 0xfb, 0xfd, 0xdb, 0x39, 0x7c, 0x07, 0x13, 0x07,
	0x28, 0x3e, 0xb2, 0x51, 0x57, 0xfb, 0x58, 0x05, 0x8a, 0x79, 0x8e, 0xec, 0x07, 0x11, 0xc7, 0x1d,
	0x4b, 0x36, 0xa0, 0xe8, 0x50, 0x4b, 0xc8, 0x0d, 0xd8, 0x40, 0x3b, 0x43, 0x7e, 0xea, 0xbc, 0x09,
	0x22, 0x57, 0xc6, 0xee, 0x5c, 0xd7, 0xd2, 0xa7, 0xfc, 0xf4, 0x47, 0x5a, 0x46, 0x76, 0xe1, 0xfc,
	0xac, 0x96, 0xa3, 0xe3, 0x3f, 0x0e, 0xd5, 0xcd, 0xb4, 0xea, 0x01, 0x97, 0x7d, 0x72, 0x15, 0x40,
	0x72, 0xef, 0xd8, 0x11, 0xbe, 0x8a, 0x82, 0xe6, 0x0a, 0x6a, 0xd5, 0xb4, 0xa4, 0xab, 0x05, 0xe4,
	0x1e, 0x6c, 0x49, 0x31, 0x12, 0x1e, 0x8b, 0x9c, 0xb1, 0xe4, 0x91, 0x13, 0xf2, 0xa8, 0xaf, 0x43,
	0x77, 0x15, 0x15, 0x49, 0x3c, 0x17, 0x3b, 0x42, 0xcf, 0xe8, 0xf8, 0x66, 0xfd, 0x7e, 0x30, 0xf6,
	0x55, 0x73, 0xcd, 0xc4, 0x77, 0x3c, 0x24, 0x2d, 0xa8, 0xfa, 0xa2, 0x3f, 0xf4, 0xd9, 0x88, 0x37,
	0xab, 0x38, 0x35, 0x19, 0x93, 0xcb, 0x50, 0xd5, 0x9e, 0xc6, 0xb9, 0x9a, 0x59, 0xa6, 0x58, 0xef,
	0x4b, 0x36, 0xe2, 0xb4, 0x0b, 0x8d, 0x19, 0x77, 0x91, 0x0d, 0x28, 0x4d, 0x62, 0xb0, 0x24, 0x5c,
	0x42, 0xa0, 0x82, 0xeb, 0x0c, 0x9d, 0xf0, 0x9b, 0x6c, 0xc1, 0x8a, 0x71, 0x63, 0x19, 0xdd, 0x68,
	0x06, 0xf4, 0x1b, 0x0b, 0xaa, 0x78, 0xf9, 0xfe, 0x71, 0x50, 0x70, 0xdd, 0x2d, 0xa8, 0xea, 0x43,
	0xa6, 0x8c, 0x4d, 0xc6, 0x33, 0xe0, 0xcb, 0x19, 0xf0, 0x5b, 0xb0, 0xc2, 0x3c, 0xc1, 0x64, 0xec,
	0x64, 0x33, 0xd0, 0xd2, 0xf0, 0x65, 0xe0, 0xf3, 0xd8, 0xa9, 0x66, 0xa0, 0x77, 0x95, 0xfc, 0x04,
	0xfd, 0xb7, 0x62, 0xeb, 0x4f, 0xba, 0x09, 0x1b, 0x49, 0x4c, 0xfa, 0xc7, 0x81, 0xcd, 0x5f, 0xd1,
	0x2e, 0x9c, 0x9b, 0x91, 0xc8, 0x90, 0x7c, 0x02, 0x35, 0xf4, 0xbf, 0xf0, 0x8f, 0x03, 0x84, 0x5c,
	0xdf, 0xbb, 0x9c, 0x8b, 0xaa, 0xc9, 0x0a, 0x84, 0xad, 0xbf, 0xe8, 0xd7, 0x98, 0x21, 0x92, 0x60,
	0x7d, 0x84, 0x11, 0xff, 0x4c, 0xc8, 0x39, 0xf1, 0xbe, 0x03, 0x9b, 0x1e, 0x93, 0xca, 0x89, 0x78,
	0x3f, 0x88, 0x5c, 0x47, 0x89, 0xd8, 0x11, 0x15, 0x7b, 0x43, 0xcb, 0x6d, 0x14, 0x1f, 0x89, 0x11,
	0xa7, 0x6f, 0x90, 0x48, 0x45, 0x76, 0x31, 0xf5, 0xac, 0xe8, 0x88, 0x4e, 0xd2, 0xce, 0x07, 0x39,
	0xa0, 0xb3, 0xeb, 0x90, 0x03, 0x66, 0x85, 0x26, 0x81, 0x17, 0x30, 0xd7, 0x39, 0x16, 0xbe, 0x90,
	0x2f, 0x71, 0xf3, 0xaa, 0x0d, 0x5a, 0xf4, 0x05, 0x4a, 0xe8, 0x3f, 0x2a, 0x40, 0xf2, 0xcb, 0xc9,
	0x15, 0xa8, 0x69, 0x03, 0x8e, 0x3a, 0x0d, 0x79, 0x7c, 0xa2, 0xaa, 0x16, 0x1c, 0x9d, 0x86, 0x9c,
	0x3c, 0x85, 0x9a, 0x27, 0x86, 0xdc, 0x41, 0x4a, 0x96, 0xd0, 0x79, 0x77, 0x97, 0xc0, 0xd4, 0x7e,
	0x26, 0x86, 0xf8, 0xf1, 0xe4, 0x5b, 0x76, 0xd5, 0x8b, 0xbf, 0xc9, 0x57, 0xba, 0xc6, 0xb8, 0xe3,
	0xbe, 0x32, 0xe6, 0xca, 0x68, 0xae, 0xbd, 0x8c, 0xb9, 0x03, 0x5c, 0x16, 0x1b, 0x04, 0x77, 0x32,
	0x6a, 0x7d, 0x53, 0x82, 0x6a, 0xb2, 0x57, 0x2e, 0xba, 0xe3, 0x5b, 0x2a, 0x4d, 0x6f, 0x29, 0xc5,
	0xb0, 0xf2, 0x7c, 0x86, 0x55, 0x32, 0x41, 0x1a, 0x07, 0xde, 0xca, 0x24, 0xf0, 0xd0, 0x67, 0xd2,
	0x39, 0x0e, 0x3c, 0x2f, 0x78, 0x83, 0x01, 0x59, 0xb5, 0xab, 0x42, 0x7e, 0x81, 0x63, 0x9d, 0x6c,
	0x22, 0xa6, 0x38, 0xc6, 0x80, 0xa3, 0xf8, 0x49, 0xc2, 0xe6, 0x75, 0x2d, 0xd5, 0x21, 0x70, 0xc4,
	0x4f, 0xb0, 0x2e, 0xf4, 0x23, 0x9e, 0xe8, 0x21, 0xab, 0xcb, 0x36, 0x18, 0x91, 0x56, 0xd2, 0xdc,
	0x54, 0x6c, 0x20, 0x9b, 0xb5, 0xed, 0xb2, 0xe6, 0xa6, 0xfe, 0xd6, 0xa6, 0x31, 0x96, 0x8d, 0x7d,
	0x6d, 0x1a, 0x8c, 0x69, 0x2d, 0xd5, 0xde, 0xd2, 0xa6, 0x5b, 0x01, 0xc0, 0xd4, 0x61, 0x39, 0xaf,
	0x6c, 0xc1, 0x8a, 0x12, 0xca, 0x4b, 0x78, 0x6a, 0x06, 0xfa, 0xfc, 0x72, 0xdc, 0x33, 0x13, 0x31,
	0x49, 0x93, 0x71, 0x16, 0x6a, 0x25, 0x0b, 0xf5, 0x51, 0x6d, 0x52, 0x98, 0xa9, 0x0b, 0x17, 0x53,
	0xd1, 0x9d, 0xe6, 0xcc, 0x45, 0x58, 0x95, 0xc1, 0x38, 0xea, 0x27, 0x41, 0x16, 0x8f, 0x74, 0xfe,
	0xc2, 0xe3, 0x4c, 0xaf, 0x6a, 0x4d, 0x8f, 0x5f, 0x08, 0x97, 0x5c, 0x82, 0x35, 0x24, 0xd5, 0xa4,
	0xfc, 0xad, 0xea, 0x61, 0xd7, 0xa5, 0x7f, 0x2c, 0x61, 0xc1, 0xcf, 0x6f, 0x23, 0x43, 0x7d, 0x37,
	0x18, 0xb2, 0xe8, 0x1e, 0x73, 0x6c, 0x0c, 0x41, 0xf4, 0x3a, 0x56, 0x55, 0x39, 0x9d, 0x4f, 0xfa,
	0x1c, 0x23, 0x43, 0x95, 0x87, 0x50, 0x71, 0x99, 0x62, 0x71, 0x93, 0x73, 0x7d, 0x6e, 0x78, 0x4e,
	0xf8, 0x87, 0xea, 0x59, 0xfa, 0x55, 0xb2, 0xf4, 0x23, 0xfb, 0x70, 0x79, 0xc4, 0x4e, 0x9c, 0x30,
	0x90, 0x42, 0x89, 0xd7, 0xdc, 0x91, 0xdc, 0xe3, 0x7d, 0xe5, 0x98, 0x68, 0x5c, 0xc1, 0x83, 0x5f,
	0x1c, 0xb1, 0x93, 0xe7, 0xf1, 0xfc, 0x21, 0x4e, 0x7f, 0x1f, 0x83, 0x33, 0x5e, 0xea, 0xf3, 0x01,
	0xcb, 0x2f, 0x5d, 0x9d, 0x2c, 0xfd, 0x32, 0x9e, 0x4f, 0x2d, 0xa5, 0xff, 0x2a, 0xc1, 0x66, 0x16,
	0xf1, 0xff, 0x83, 0x28, 0x8d, 0x25, 0x88, 0x72, 0x15, 0x00, 0x6b, 0xa7, 0xa3, 0x44, 0x28, 0x63,
	0x92, 0xd4, 0x50, 0x72, 0x24, 0x42, 0x89, 0x01, 0xa3, 0x98, 0x1a, 0x4b, 0x24, 0x87, 0x0e, 0x18,
	0x1c, 0x69, 0x04, 0x4c, 0x29, 0xa1, 0xc6, 0xae, 0x29, 0x78, 0x0d, 0x7b, 0x32, 0x26, 0x1f, 0xc7,
	0xa4, 0x01, 0xbc, 0xbc, 0xed, 0x85, 0x97, 0x77, 0xc4, 0x06, 0x31, 0xad, 0x32, 0x01, 0x5e, 0xc7,
	0xbc, 0x9d, 0xe6, 0x62, 0x9e, 0x77, 0xeb, 0x79, 0xde, 0xd1, 0xe7, 0x70, 0x2e, 0x63, 0x7f, 0xa9,
	0x82, 0x8b, 0x05, 0x7c, 0x60, 0xf2, 0x6f, 0xd9, 0x10, 0x40, 0xb1, 0x81, 0x4e, 0xbf, 0xf4, 0x2f,
	0x16, 0xd4, 0x1f, 0x31, 0xc9, 0x6d, 0x8e, 0x0d, 0xa3, 0x76, 0xa7, 0xcb, 0x5f, 0x8b, 0x3e, 0x9f,
	0xb6, 0x92, 0x55, 0x23, 0xe8, 0xba, 0x7a, 0x72, 0xc4, 0xa2, 0x21, 0x4f, 0xf5, 0x92, 0x55, 0x23,
	0xe8, 0xba, 0x78, 0x44, 0x4f, 0x70, 0x5f, 0xa5, 0xf7, 0x01, 0x23, 0xc2, 0x4c, 0xff, 0x21, 0x6c,
	0xc4, 0x0a, 0xaf, 0x79, 0xa4, 0x1b, 0x06, 0xbc, 0xdd, 0x86, 0xdd, 0x30, 0xd2, 0xaf, 0x8d, 0x50,
	0x6f, 0x12, 0xab, 0x89, 0x30, 0x2e, 0xcf, 0x55, 0x23, 0xe8, 0x86, 0xf4, 0xdf, 0x16, 0x5c, 0x30,
	0x1d, 0x69, 0xda, 0x0f, 0x9a, 0xfc, 0x59, 0x3f, 0xa4, 0xef, 0xb0, 0x94, 0xb9, 0xc3, 0xcf, 0xa1,
	0x1e, 0x07, 0x38, 0x5e, 0x65, 0x79, 0xc9, 0xab, 0x04, 0xb3, 0xe8, 0xa8, 0x38, 0x4f, 0x56, 0xf2,
	0xf7, 0x45, 0xbe, 0x07, 0xeb, 0x3d, 0x26, 0xb9, 0x13, 0x19, 0xef, 0xe2, 0x71, 0xea, 0x7b, 0xef,
	0xe7, 0x76, 0x4a, 0xdd, 0x80, 0x5d, 0xef, 0x4d, 0x07, 0xb4, 0x07, 0x17, 0x8b, 0x8e, 0x2b, 0x43,
	0x0d, 0x40, 0x48, 0x47, 0xbe, 0x14, 0xdc, 0x73, 0x9d, 0x90, 0x49, 0x89, 0x67, 0xaf, 0xda, 0xeb,
	0x42, 0x1e, 0xa2, 0xf0, 0x39, 0x93, 0x92, 0x50, 0x68, 0xf8, 0x81, 0xc2, 0xf9, 0x99, 0x74, 0xe4,
	0x07, 0x4a, 0xcf, 0x63, 0x50, 0x1d, 0xc2, 0xe6, 0x63, 0xae, 0x6c, 0xee, 0x1e, 0x04, 0x2a, 0xee,
	0x72, 0x74, 0xc4, 0xf4, 0xc4, 0xdb, 0x74, 0xc5, 0x5e, 0xeb, 0x89, 0xb7, 0x78, 0x8d, 0x37, 0x60,
	0x43, 0x4f, 0xc9, 0xf1, 0xf1, 0xb1, 0x38, 0xd1, 0x9d, 0x6c, 0x6c, 0x73, 0xbd, 0x27, 0xde, 0x1e,
	0xa2, 0xf0, 0x29, 0x3f, 0xa5, 0xb7, 0xe0, 0xbd, 0x8c, 0x51, 0x19, 0xea, 0xc2, 0x60, 0x18, 0x1f,
	0x3f, 0x19, 0x70, 0x40, 0x8f, 0xe0, 0xbd, 0x1f, 0xb0, 0x68, 0x68, 0x74, 0x6d, 0xce, 0xdc, 0xff,
	0x09, 0x80, 0x2d, 0x20, 0x59, 0xab, 0x32, 0xa4, 0x9f, 0x60, 0x6b, 0xf4, 0xf9, 0x60, 0x10, 0xe9,
	0x4c, 0xc6, 0x97, 0x3d, 0x34, 0xdd, 0x83, 0xd6, 0xbc, 0x75, 0x73, 0xcf, 0xf5, 0x29, 0xb4, 0x34,
	0x82, 0xcc, 0xa2, 0xb3, 0x0f, 0x48, 0xaf, 0xc2, 0x95, 0xb9, 0x0b, 0x65, 0x48, 0x3f, 0x9c, 0xe9,
	0x40, 0xf1, 0x09, 0x4a, 0xa0, 0x32, 0x16, 0xae, 0xe9, 0xe9, 0x1a, 0x36, 0x7e, 0xd3, 0x67, 0x78,
	0xad, 0x29, 0x35, 0x19, 0x92, 0xcf, 0x00, 0x26, 0x9d, 0x6a, 0xd2, 0x01, 0x2e, 0x68, 0x55, 0x6b,
	0x49, 0xab, 0x2a, 0xe9, 0x4f, 0x80, 0xd8, 0x3c, 0x88, 0xdc, 0x09, 0x1b, 0x70, 0xdf, 0x3c, 0x0b,
	0xac, 0x02, 0x16, 0x6c, 0x03, 0x36, 0x26, 0xd8, 0x93, 0x25, 0x99, 0xa3, 0x66, 0x43, 0x14, 0x17,
	0x8b, 0xae, 0x4b, 0x8f, 0xe0, 0x7c, 0xce, 0xba, 0x0c, 0xc9, 0x77, 0xa1, 0x66, 0x2c, 0x6b, 0x96,
	0x5a, 0x4b, 0xb2, 0x14, 0x6b, 0xbd, 0x36, 0xb1, 0xf7, 0xeb, 0x4d, 0x68, 0xe8, 0xd9, 0x27, 0x4a,
	0x85, 0xcf, 0xb4, 0x2e, 0xf9, 0x83, 0x05, 0x5b, 0x45, 0x8f, 0x72, 0xb2, 0x93, 0x37, 0x5b, 0xfc,
	0xda, 0x6f, 0xdd, 0x5a, 0x52, 0x53, 0x86, 0xf4, 0xd3, 0x9f, 0xfe, 0xfd, 0x9f, 0xbf, 0x29, 0xdd,
	0xa7, 0x9d, 0xdc, 0x0f, 0x30, 0xda, 0x3d, 0xbb, 0x1a, 0x67, 0x67, 0x50, 0x84, 0xe6, 0xf7, 0x16,
	0x6c, 0x15, 0xbd, 0xbb, 0x0b, 0x60, 0xce, 0x79, 0x9e, 0xb7, 0x6e, 0xce, 0xd1, 0xcc, 0xe6, 0x91,
	0xa5, 0x40, 0xca, 0x22, 0x2c, 0x7f, 0xb3, 0xb0, 0x11, 0x2b, 0x78, 0xaf, 0x93, 0xdb, 0x45, 0x3e,
	0x2a, 0x7e, 0xd8, 0xb7, 0xe6, 0x77, 0xe7, 0x85, 0xaf, 0x77, 0xba, 0x8f, 0x78, 0x1f, 0xd0, 0xfb,
	0x8b, 0x9d, 0x5a, 0x04, 0xeb, 0x67, 0x16, 0xd4, 0x53, 0x94, 0x20, 0xd7, 0xe6, 0xc2, 0x34, 0x09,
	0xa1, 0xb5, 0xbd, 0x58, 0x41, 0x86, 0xf4, 0x3e, 0xa2, 0xb9, 0x43, 0x6f, 0x15, 0xff, 0xc6, 0x16,
	0xb7, 0x34, 0x13, 0x40, 0x7a, 0xd7, 0x3f, 0x59, 0x70, 0xa1, 0xf0, 0x79, 0x46, 0x0a, 0x43, 0xab,
	0xf0, 0x79, 0xd8, 0xba, 0xbd, 0xac, 0xaa, 0x0c, 0xe9, 0x67, 0x88, 0x71, 0x8f, 0xde, 0x5b, 0xec,
	0xb1, 0x02, 0x40, 0xbf, 0xb3, 0xe0, 0x7c, 0x41, 0x13, 0x4c, 0x6e, 0x2e, 0xda, 0x3d, 0x0d, 0x73,
	0x67, 0x39, 0x45, 0x9d, 0x98, 0x11, 0xe4, 0x3d, 0xda, 0x5e, 0x0e, 0xe4, 0x04, 0xca, 0x6f, 0x2d,
	0x20, 0xf9, 0xc8, 0x26, 0x1f, 0x2d, 0x15, 0xfe, 0xff, 0x05, 0x4d, 0x1e, 0x22, 0xbe, 0x0e, 0xdd,
	0x3d, 0x93, 0x26, 0x33, 0x38, 0x7e, 0x6e, 0x41, 0x63, 0xa6, 0x0e, 0x92, 0xeb, 0x45, 0x2e, 0x99,
	0xa9, 0x43, 0x2d, 0x7a, 0x96, 0x8a, 0x0c, 0x69, 0x1b, 0xf1, 0xec, 0xd0, 0x8f, 0x72, 0x78, 0x22,
	0xee, 0xee, 0xba, 0x01, 0xc6, 0x5c, 0x6a, 0xdb, 0x5f, 0x5a, 0xb0, 0x31, 0x5b, 0x0f, 0x49, 0x7e,
	0x9b, 0x5c, 0x19, 0x6e, 0x7d, 0x70, 0xa6, 0x8e, 0x0c, 0x69, 0x07, 0xb1, 0xdc, 0xa2, 0x37, 0xe7,
	0x62, 0x19, 0xcd, 0xee, 0xfc, 0x67, 0x93, 0x3a, 0x0a, 0xca, 0x69, 0x71, 0xea, 0x28, 0xae, 0xd7,
	0xad, 0x3b, 0x4b, 0xeb, 0x2e, 0xcc, 0x73, 0x29, 0x87, 0x15, 0x21, 0xfa, 0xab, 0x05, 0x97, 0xe6,
	0x94, 0x63, 0x72, 0xa7, 0xd0, 0x3d, 0xc5, 0x15, 0xbf, 0x75, 0x77, 0x79, 0xe5, 0x85, 0xac, 0x4d,
	0x3b, 0xb5, 0x08, 0xd4, 0x2f, 0x2c, 0x58, 0x4f, 0x57, 0x7e, 0xb2, 0x30, 0x8d, 0x61, 0x51, 0xbb,
	0x7e, 0x86, 0x86, 0x0c, 0xe9, 0x1e, 0xe2, 0xb9, 0x4b, 0x6f, 0x2f, 0x9d, 0xe9, 0x24, 0xf9, 0x95,
	0x05, 0xe7, 0x32, 0x75, 0x9d, 0xe4, 0x23, 0x2a, 0xdf, 0x57, 0xb4, 0x6e, 0x9c, 0xad, 0xb4, 0x10,
	0xd2, 0x94, 0x93, 0xd1, 0xec, 0xba, 0x47, 0x0f, 0x7e, 0x7c, 0x7f, 0x10, 0x78, 0xcc, 0x1f, 0xb4,
	0x1f, 0xee, 0x29, 0xd5, 0xee, 0x07, 0xa3, 0x0e, 0xfe, 0x9d, 0xd1, 0x0f, 0xbc, 0x8e, 0xe4, 0x91,
	0x7e, 0xe8, 0xc8, 0xac, 0xb9, 0xde, 0x2a, 0xaa, 0x3c, 0xf8, 0x4f, 0x00, 0x00, 0x00, 0xff, 0xff,
	0xc0, 0x26, 0x0c, 0x9c, 0x58, 0x19, 0x00, 0x00,
}
