// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/gangup-channel/gangup-channel.proto

package gangup_channel // import "golang.52tt.com/protocol/services/gangup-channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ChannelDisplayType int32

const (
	ChannelDisplayType_DISPLAY_AT_MAIN_PAGE   ChannelDisplayType = 0
	ChannelDisplayType_DISMISSED              ChannelDisplayType = 1
	ChannelDisplayType_DISPLAY_AT_FIND_FRIEND ChannelDisplayType = 2
	ChannelDisplayType_TEMPORARY              ChannelDisplayType = 3
)

var ChannelDisplayType_name = map[int32]string{
	0: "DISPLAY_AT_MAIN_PAGE",
	1: "DISMISSED",
	2: "DISPLAY_AT_FIND_FRIEND",
	3: "TEMPORARY",
}
var ChannelDisplayType_value = map[string]int32{
	"DISPLAY_AT_MAIN_PAGE":   0,
	"DISMISSED":              1,
	"DISPLAY_AT_FIND_FRIEND": 2,
	"TEMPORARY":              3,
}

func (x ChannelDisplayType) String() string {
	return proto.EnumName(ChannelDisplayType_name, int32(x))
}
func (ChannelDisplayType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{0}
}

type DismissType int32

const (
	DismissType_Unknown   DismissType = 0
	DismissType_Sever     DismissType = 1
	DismissType_Cancel    DismissType = 2
	DismissType_SwitchTab DismissType = 3
	DismissType_Quit      DismissType = 4
	DismissType_Freeze    DismissType = 5
)

var DismissType_name = map[int32]string{
	0: "Unknown",
	1: "Sever",
	2: "Cancel",
	3: "SwitchTab",
	4: "Quit",
	5: "Freeze",
}
var DismissType_value = map[string]int32{
	"Unknown":   0,
	"Sever":     1,
	"Cancel":    2,
	"SwitchTab": 3,
	"Quit":      4,
	"Freeze":    5,
}

func (x DismissType) String() string {
	return proto.EnumName(DismissType_name, int32(x))
}
func (DismissType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{1}
}

type HistoryType int32

const (
	HistoryType_CreateHistory HistoryType = 0
	HistoryType_UpdateHistory HistoryType = 1
)

var HistoryType_name = map[int32]string{
	0: "CreateHistory",
	1: "UpdateHistory",
}
var HistoryType_value = map[string]int32{
	"CreateHistory": 0,
	"UpdateHistory": 1,
}

func (x HistoryType) String() string {
	return proto.EnumName(HistoryType_name, int32(x))
}
func (HistoryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{2}
}

// 负反馈上报
type NegativeFeedbackType int32

const (
	NegativeFeedbackType_FeedbackTypeInvalid      NegativeFeedbackType = 0
	NegativeFeedbackType_FeedbackTypeChannelOwner NegativeFeedbackType = 1
	NegativeFeedbackType_FeedbackTypeChannelTab   NegativeFeedbackType = 2
	NegativeFeedbackType_FeedbackTypePublishCond  NegativeFeedbackType = 3
	NegativeFeedbackType_FeedbackTypeChannelName  NegativeFeedbackType = 4
	// 房间内反馈用户
	NegativeFeedbackType_FeedbackTypeOwnerInChannel            NegativeFeedbackType = 5
	NegativeFeedbackType_FeedbackTypeUserInChannel             NegativeFeedbackType = 6
	NegativeFeedbackType_FeedbackTypeQuitUninterestedInChannel NegativeFeedbackType = 7
)

var NegativeFeedbackType_name = map[int32]string{
	0: "FeedbackTypeInvalid",
	1: "FeedbackTypeChannelOwner",
	2: "FeedbackTypeChannelTab",
	3: "FeedbackTypePublishCond",
	4: "FeedbackTypeChannelName",
	5: "FeedbackTypeOwnerInChannel",
	6: "FeedbackTypeUserInChannel",
	7: "FeedbackTypeQuitUninterestedInChannel",
}
var NegativeFeedbackType_value = map[string]int32{
	"FeedbackTypeInvalid":                   0,
	"FeedbackTypeChannelOwner":              1,
	"FeedbackTypeChannelTab":                2,
	"FeedbackTypePublishCond":               3,
	"FeedbackTypeChannelName":               4,
	"FeedbackTypeOwnerInChannel":            5,
	"FeedbackTypeUserInChannel":             6,
	"FeedbackTypeQuitUninterestedInChannel": 7,
}

func (x NegativeFeedbackType) String() string {
	return proto.EnumName(NegativeFeedbackType_name, int32(x))
}
func (NegativeFeedbackType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{3}
}

// buf:lint:ignore ENUM_PASCAL_CASE
type REGULATORY_LEVEL int32

const (
	REGULATORY_LEVEL_FREE         REGULATORY_LEVEL = 0
	REGULATORY_LEVEL_SIMPLE_MINOR REGULATORY_LEVEL = 1
)

var REGULATORY_LEVEL_name = map[int32]string{
	0: "FREE",
	1: "SIMPLE_MINOR",
}
var REGULATORY_LEVEL_value = map[string]int32{
	"FREE":         0,
	"SIMPLE_MINOR": 1,
}

func (x REGULATORY_LEVEL) String() string {
	return proto.EnumName(REGULATORY_LEVEL_name, int32(x))
}
func (REGULATORY_LEVEL) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{4}
}

type EntryType int32

const (
	EntryType_GameHomePageEntry    EntryType = 0
	EntryType_MysteryHomePageEntry EntryType = 1
	EntryType_PCHomePageEntry      EntryType = 2
)

var EntryType_name = map[int32]string{
	0: "GameHomePageEntry",
	1: "MysteryHomePageEntry",
	2: "PCHomePageEntry",
}
var EntryType_value = map[string]int32{
	"GameHomePageEntry":    0,
	"MysteryHomePageEntry": 1,
	"PCHomePageEntry":      2,
}

func (x EntryType) String() string {
	return proto.EnumName(EntryType_name, int32(x))
}
func (EntryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{5}
}

type GetChannelIdsByTabIdReq_GetMode int32

const (
	GetChannelIdsByTabIdReq_ALL          GetChannelIdsByTabIdReq_GetMode = 0
	GetChannelIdsByTabIdReq_ONLY_RELEASE GetChannelIdsByTabIdReq_GetMode = 1
	GetChannelIdsByTabIdReq_ONLY_SINK    GetChannelIdsByTabIdReq_GetMode = 2
)

var GetChannelIdsByTabIdReq_GetMode_name = map[int32]string{
	0: "ALL",
	1: "ONLY_RELEASE",
	2: "ONLY_SINK",
}
var GetChannelIdsByTabIdReq_GetMode_value = map[string]int32{
	"ALL":          0,
	"ONLY_RELEASE": 1,
	"ONLY_SINK":    2,
}

func (x GetChannelIdsByTabIdReq_GetMode) String() string {
	return proto.EnumName(GetChannelIdsByTabIdReq_GetMode_name, int32(x))
}
func (GetChannelIdsByTabIdReq_GetMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{39, 0}
}

type BlockOptionList struct {
	BlockOptions         []*BlockOption `protobuf:"bytes,1,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BlockOptionList) Reset()         { *m = BlockOptionList{} }
func (m *BlockOptionList) String() string { return proto.CompactTextString(m) }
func (*BlockOptionList) ProtoMessage()    {}
func (*BlockOptionList) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{0}
}
func (m *BlockOptionList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOptionList.Unmarshal(m, b)
}
func (m *BlockOptionList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOptionList.Marshal(b, m, deterministic)
}
func (dst *BlockOptionList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOptionList.Merge(dst, src)
}
func (m *BlockOptionList) XXX_Size() int {
	return xxx_messageInfo_BlockOptionList.Size(m)
}
func (m *BlockOptionList) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOptionList.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOptionList proto.InternalMessageInfo

func (m *BlockOptionList) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

type BlockOption struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemId               uint32   `protobuf:"varint,2,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	ElemVal              string   `protobuf:"bytes,3,opt,name=elem_val,json=elemVal,proto3" json:"elem_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlockOption) Reset()         { *m = BlockOption{} }
func (m *BlockOption) String() string { return proto.CompactTextString(m) }
func (*BlockOption) ProtoMessage()    {}
func (*BlockOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{1}
}
func (m *BlockOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOption.Unmarshal(m, b)
}
func (m *BlockOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOption.Marshal(b, m, deterministic)
}
func (dst *BlockOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOption.Merge(dst, src)
}
func (m *BlockOption) XXX_Size() int {
	return xxx_messageInfo_BlockOption.Size(m)
}
func (m *BlockOption) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOption.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOption proto.InternalMessageInfo

func (m *BlockOption) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *BlockOption) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

func (m *BlockOption) GetElemVal() string {
	if m != nil {
		return m.ElemVal
	}
	return ""
}

type GameLabel struct {
	Val                  string   `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
	DisplayName          string   `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameLabel) Reset()         { *m = GameLabel{} }
func (m *GameLabel) String() string { return proto.CompactTextString(m) }
func (*GameLabel) ProtoMessage()    {}
func (*GameLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{2}
}
func (m *GameLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameLabel.Unmarshal(m, b)
}
func (m *GameLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameLabel.Marshal(b, m, deterministic)
}
func (dst *GameLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameLabel.Merge(dst, src)
}
func (m *GameLabel) XXX_Size() int {
	return xxx_messageInfo_GameLabel.Size(m)
}
func (m *GameLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_GameLabel.DiscardUnknown(m)
}

var xxx_messageInfo_GameLabel proto.InternalMessageInfo

func (m *GameLabel) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

func (m *GameLabel) GetDisplayName() string {
	if m != nil {
		return m.DisplayName
	}
	return ""
}

func (m *GameLabel) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GangupChannelReleaseInfo struct {
	Id                   uint32               `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TabId                uint32               `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ReleaseTime          int64                `protobuf:"varint,3,opt,name=release_time,json=releaseTime,proto3" json:"release_time,omitempty"`
	BlockOptions         []*BlockOption       `protobuf:"bytes,4,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	DisplayType          []ChannelDisplayType `protobuf:"varint,5,rep,packed,name=display_type,json=displayType,proto3,enum=gangup_channel.ChannelDisplayType" json:"display_type,omitempty"`
	ShowGeoInfo          bool                 `protobuf:"varint,6,opt,name=show_geo_info,json=showGeoInfo,proto3" json:"show_geo_info,omitempty"`
	MarketId             uint32               `protobuf:"varint,7,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	TerminalType         uint32               `protobuf:"varint,8,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	LastDismissTime      int64                `protobuf:"varint,9,opt,name=last_dismiss_time,json=lastDismissTime,proto3" json:"last_dismiss_time,omitempty"`
	SwitchTime           int64                `protobuf:"varint,10,opt,name=switch_time,json=switchTime,proto3" json:"switch_time,omitempty"`
	PushlishUid          uint32               `protobuf:"varint,11,opt,name=pushlish_uid,json=pushlishUid,proto3" json:"pushlish_uid,omitempty"`
	GameLabels           []*GameLabel         `protobuf:"bytes,12,rep,name=game_labels,json=gameLabels,proto3" json:"game_labels,omitempty"`
	ClientType           uint32               `protobuf:"varint,13,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GangupChannelReleaseInfo) Reset()         { *m = GangupChannelReleaseInfo{} }
func (m *GangupChannelReleaseInfo) String() string { return proto.CompactTextString(m) }
func (*GangupChannelReleaseInfo) ProtoMessage()    {}
func (*GangupChannelReleaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{3}
}
func (m *GangupChannelReleaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GangupChannelReleaseInfo.Unmarshal(m, b)
}
func (m *GangupChannelReleaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GangupChannelReleaseInfo.Marshal(b, m, deterministic)
}
func (dst *GangupChannelReleaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GangupChannelReleaseInfo.Merge(dst, src)
}
func (m *GangupChannelReleaseInfo) XXX_Size() int {
	return xxx_messageInfo_GangupChannelReleaseInfo.Size(m)
}
func (m *GangupChannelReleaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GangupChannelReleaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GangupChannelReleaseInfo proto.InternalMessageInfo

func (m *GangupChannelReleaseInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GangupChannelReleaseInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GangupChannelReleaseInfo) GetReleaseTime() int64 {
	if m != nil {
		return m.ReleaseTime
	}
	return 0
}

func (m *GangupChannelReleaseInfo) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *GangupChannelReleaseInfo) GetDisplayType() []ChannelDisplayType {
	if m != nil {
		return m.DisplayType
	}
	return nil
}

func (m *GangupChannelReleaseInfo) GetShowGeoInfo() bool {
	if m != nil {
		return m.ShowGeoInfo
	}
	return false
}

func (m *GangupChannelReleaseInfo) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GangupChannelReleaseInfo) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *GangupChannelReleaseInfo) GetLastDismissTime() int64 {
	if m != nil {
		return m.LastDismissTime
	}
	return 0
}

func (m *GangupChannelReleaseInfo) GetSwitchTime() int64 {
	if m != nil {
		return m.SwitchTime
	}
	return 0
}

func (m *GangupChannelReleaseInfo) GetPushlishUid() uint32 {
	if m != nil {
		return m.PushlishUid
	}
	return 0
}

func (m *GangupChannelReleaseInfo) GetGameLabels() []*GameLabel {
	if m != nil {
		return m.GameLabels
	}
	return nil
}

func (m *GangupChannelReleaseInfo) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type SetGangupChannelReleaseInfoReq struct {
	GangupChannelReleaseInfo *GangupChannelReleaseInfo `protobuf:"bytes,1,opt,name=gangup_channel_release_info,json=gangupChannelReleaseInfo,proto3" json:"gangup_channel_release_info,omitempty"`
	WantFresh                bool                      `protobuf:"varint,2,opt,name=want_fresh,json=wantFresh,proto3" json:"want_fresh,omitempty"`
	ReleaseIp                string                    `protobuf:"bytes,3,opt,name=release_ip,json=releaseIp,proto3" json:"release_ip,omitempty"`
	ChannelName              string                    `protobuf:"bytes,4,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	Creator                  uint32                    `protobuf:"varint,5,opt,name=creator,proto3" json:"creator,omitempty"`
	AllSelectedBids          []uint32                  `protobuf:"varint,6,rep,packed,name=all_selected_bids,json=allSelectedBids,proto3" json:"all_selected_bids,omitempty"`
	UnSelectBlockId          []uint32                  `protobuf:"varint,7,rep,packed,name=un_select_block_id,json=unSelectBlockId,proto3" json:"un_select_block_id,omitempty"`
	UnSelectBlockOptions     []*BlockOption            `protobuf:"bytes,8,rep,name=un_select_block_options,json=unSelectBlockOptions,proto3" json:"un_select_block_options,omitempty"`
	ChannelPlayMode          uint32                    `protobuf:"varint,9,opt,name=ChannelPlayMode,proto3" json:"ChannelPlayMode,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}                  `json:"-"`
	XXX_unrecognized         []byte                    `json:"-"`
	XXX_sizecache            int32                     `json:"-"`
}

func (m *SetGangupChannelReleaseInfoReq) Reset()         { *m = SetGangupChannelReleaseInfoReq{} }
func (m *SetGangupChannelReleaseInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetGangupChannelReleaseInfoReq) ProtoMessage()    {}
func (*SetGangupChannelReleaseInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{4}
}
func (m *SetGangupChannelReleaseInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGangupChannelReleaseInfoReq.Unmarshal(m, b)
}
func (m *SetGangupChannelReleaseInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGangupChannelReleaseInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetGangupChannelReleaseInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGangupChannelReleaseInfoReq.Merge(dst, src)
}
func (m *SetGangupChannelReleaseInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetGangupChannelReleaseInfoReq.Size(m)
}
func (m *SetGangupChannelReleaseInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGangupChannelReleaseInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGangupChannelReleaseInfoReq proto.InternalMessageInfo

func (m *SetGangupChannelReleaseInfoReq) GetGangupChannelReleaseInfo() *GangupChannelReleaseInfo {
	if m != nil {
		return m.GangupChannelReleaseInfo
	}
	return nil
}

func (m *SetGangupChannelReleaseInfoReq) GetWantFresh() bool {
	if m != nil {
		return m.WantFresh
	}
	return false
}

func (m *SetGangupChannelReleaseInfoReq) GetReleaseIp() string {
	if m != nil {
		return m.ReleaseIp
	}
	return ""
}

func (m *SetGangupChannelReleaseInfoReq) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *SetGangupChannelReleaseInfoReq) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *SetGangupChannelReleaseInfoReq) GetAllSelectedBids() []uint32 {
	if m != nil {
		return m.AllSelectedBids
	}
	return nil
}

func (m *SetGangupChannelReleaseInfoReq) GetUnSelectBlockId() []uint32 {
	if m != nil {
		return m.UnSelectBlockId
	}
	return nil
}

func (m *SetGangupChannelReleaseInfoReq) GetUnSelectBlockOptions() []*BlockOption {
	if m != nil {
		return m.UnSelectBlockOptions
	}
	return nil
}

func (m *SetGangupChannelReleaseInfoReq) GetChannelPlayMode() uint32 {
	if m != nil {
		return m.ChannelPlayMode
	}
	return 0
}

type SetGangupChannelReleaseInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGangupChannelReleaseInfoResp) Reset()         { *m = SetGangupChannelReleaseInfoResp{} }
func (m *SetGangupChannelReleaseInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetGangupChannelReleaseInfoResp) ProtoMessage()    {}
func (*SetGangupChannelReleaseInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{5}
}
func (m *SetGangupChannelReleaseInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGangupChannelReleaseInfoResp.Unmarshal(m, b)
}
func (m *SetGangupChannelReleaseInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGangupChannelReleaseInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetGangupChannelReleaseInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGangupChannelReleaseInfoResp.Merge(dst, src)
}
func (m *SetGangupChannelReleaseInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetGangupChannelReleaseInfoResp.Size(m)
}
func (m *SetGangupChannelReleaseInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGangupChannelReleaseInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGangupChannelReleaseInfoResp proto.InternalMessageInfo

type DismissGangupChannelReq struct {
	ChannelId            uint32      `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Source               string      `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	Type                 DismissType `protobuf:"varint,3,opt,name=type,proto3,enum=gangup_channel.DismissType" json:"type,omitempty"`
	TabId                uint32      `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *DismissGangupChannelReq) Reset()         { *m = DismissGangupChannelReq{} }
func (m *DismissGangupChannelReq) String() string { return proto.CompactTextString(m) }
func (*DismissGangupChannelReq) ProtoMessage()    {}
func (*DismissGangupChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{6}
}
func (m *DismissGangupChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DismissGangupChannelReq.Unmarshal(m, b)
}
func (m *DismissGangupChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DismissGangupChannelReq.Marshal(b, m, deterministic)
}
func (dst *DismissGangupChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DismissGangupChannelReq.Merge(dst, src)
}
func (m *DismissGangupChannelReq) XXX_Size() int {
	return xxx_messageInfo_DismissGangupChannelReq.Size(m)
}
func (m *DismissGangupChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DismissGangupChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DismissGangupChannelReq proto.InternalMessageInfo

func (m *DismissGangupChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DismissGangupChannelReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *DismissGangupChannelReq) GetType() DismissType {
	if m != nil {
		return m.Type
	}
	return DismissType_Unknown
}

func (m *DismissGangupChannelReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type DismissGangupChannelResp struct {
	Dismiss              bool     `protobuf:"varint,1,opt,name=dismiss,proto3" json:"dismiss,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DismissGangupChannelResp) Reset()         { *m = DismissGangupChannelResp{} }
func (m *DismissGangupChannelResp) String() string { return proto.CompactTextString(m) }
func (*DismissGangupChannelResp) ProtoMessage()    {}
func (*DismissGangupChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{7}
}
func (m *DismissGangupChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DismissGangupChannelResp.Unmarshal(m, b)
}
func (m *DismissGangupChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DismissGangupChannelResp.Marshal(b, m, deterministic)
}
func (dst *DismissGangupChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DismissGangupChannelResp.Merge(dst, src)
}
func (m *DismissGangupChannelResp) XXX_Size() int {
	return xxx_messageInfo_DismissGangupChannelResp.Size(m)
}
func (m *DismissGangupChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DismissGangupChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_DismissGangupChannelResp proto.InternalMessageInfo

func (m *DismissGangupChannelResp) GetDismiss() bool {
	if m != nil {
		return m.Dismiss
	}
	return false
}

type GetRecommendChannelListLoadMore struct {
	Num                  uint32   `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendChannelListLoadMore) Reset()         { *m = GetRecommendChannelListLoadMore{} }
func (m *GetRecommendChannelListLoadMore) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelListLoadMore) ProtoMessage()    {}
func (*GetRecommendChannelListLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{8}
}
func (m *GetRecommendChannelListLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendChannelListLoadMore.Unmarshal(m, b)
}
func (m *GetRecommendChannelListLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendChannelListLoadMore.Marshal(b, m, deterministic)
}
func (dst *GetRecommendChannelListLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendChannelListLoadMore.Merge(dst, src)
}
func (m *GetRecommendChannelListLoadMore) XXX_Size() int {
	return xxx_messageInfo_GetRecommendChannelListLoadMore.Size(m)
}
func (m *GetRecommendChannelListLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendChannelListLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendChannelListLoadMore proto.InternalMessageInfo

func (m *GetRecommendChannelListLoadMore) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type GetGangupChannelListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	TabIdList            []uint32 `protobuf:"varint,3,rep,packed,name=tab_id_list,json=tabIdList,proto3" json:"tab_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGangupChannelListReq) Reset()         { *m = GetGangupChannelListReq{} }
func (m *GetGangupChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetGangupChannelListReq) ProtoMessage()    {}
func (*GetGangupChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{9}
}
func (m *GetGangupChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangupChannelListReq.Unmarshal(m, b)
}
func (m *GetGangupChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangupChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetGangupChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangupChannelListReq.Merge(dst, src)
}
func (m *GetGangupChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetGangupChannelListReq.Size(m)
}
func (m *GetGangupChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangupChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangupChannelListReq proto.InternalMessageInfo

func (m *GetGangupChannelListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGangupChannelListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetGangupChannelListReq) GetTabIdList() []uint32 {
	if m != nil {
		return m.TabIdList
	}
	return nil
}

type GetGangupChannelListResp struct {
	ChannelList          []*GangupChannelReleaseInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetGangupChannelListResp) Reset()         { *m = GetGangupChannelListResp{} }
func (m *GetGangupChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetGangupChannelListResp) ProtoMessage()    {}
func (*GetGangupChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{10}
}
func (m *GetGangupChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangupChannelListResp.Unmarshal(m, b)
}
func (m *GetGangupChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangupChannelListResp.Marshal(b, m, deterministic)
}
func (dst *GetGangupChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangupChannelListResp.Merge(dst, src)
}
func (m *GetGangupChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetGangupChannelListResp.Size(m)
}
func (m *GetGangupChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangupChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangupChannelListResp proto.InternalMessageInfo

func (m *GetGangupChannelListResp) GetChannelList() []*GangupChannelReleaseInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type GetGangupChannelByIdsReq struct {
	Ids                  []uint32             `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	Types                []ChannelDisplayType `protobuf:"varint,2,rep,packed,name=types,proto3,enum=gangup_channel.ChannelDisplayType" json:"types,omitempty"`
	ReturnAll            bool                 `protobuf:"varint,3,opt,name=return_all,json=returnAll,proto3" json:"return_all,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetGangupChannelByIdsReq) Reset()         { *m = GetGangupChannelByIdsReq{} }
func (m *GetGangupChannelByIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetGangupChannelByIdsReq) ProtoMessage()    {}
func (*GetGangupChannelByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{11}
}
func (m *GetGangupChannelByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangupChannelByIdsReq.Unmarshal(m, b)
}
func (m *GetGangupChannelByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangupChannelByIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetGangupChannelByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangupChannelByIdsReq.Merge(dst, src)
}
func (m *GetGangupChannelByIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetGangupChannelByIdsReq.Size(m)
}
func (m *GetGangupChannelByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangupChannelByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangupChannelByIdsReq proto.InternalMessageInfo

func (m *GetGangupChannelByIdsReq) GetIds() []uint32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *GetGangupChannelByIdsReq) GetTypes() []ChannelDisplayType {
	if m != nil {
		return m.Types
	}
	return nil
}

func (m *GetGangupChannelByIdsReq) GetReturnAll() bool {
	if m != nil {
		return m.ReturnAll
	}
	return false
}

type GetGangupChannelByIdsResp struct {
	Info                 []*GangupChannelReleaseInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetGangupChannelByIdsResp) Reset()         { *m = GetGangupChannelByIdsResp{} }
func (m *GetGangupChannelByIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetGangupChannelByIdsResp) ProtoMessage()    {}
func (*GetGangupChannelByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{12}
}
func (m *GetGangupChannelByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangupChannelByIdsResp.Unmarshal(m, b)
}
func (m *GetGangupChannelByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangupChannelByIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetGangupChannelByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangupChannelByIdsResp.Merge(dst, src)
}
func (m *GetGangupChannelByIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetGangupChannelByIdsResp.Size(m)
}
func (m *GetGangupChannelByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangupChannelByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangupChannelByIdsResp proto.InternalMessageInfo

func (m *GetGangupChannelByIdsResp) GetInfo() []*GangupChannelReleaseInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type SwitchChannelTabReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AppId                uint32   `protobuf:"varint,4,opt,name=appId,proto3" json:"appId,omitempty"`
	MarketId             uint32   `protobuf:"varint,5,opt,name=marketId,proto3" json:"marketId,omitempty"`
	SwitchTime           int64    `protobuf:"varint,6,opt,name=switch_time,json=switchTime,proto3" json:"switch_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchChannelTabReq) Reset()         { *m = SwitchChannelTabReq{} }
func (m *SwitchChannelTabReq) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelTabReq) ProtoMessage()    {}
func (*SwitchChannelTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{13}
}
func (m *SwitchChannelTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelTabReq.Unmarshal(m, b)
}
func (m *SwitchChannelTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelTabReq.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelTabReq.Merge(dst, src)
}
func (m *SwitchChannelTabReq) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelTabReq.Size(m)
}
func (m *SwitchChannelTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelTabReq proto.InternalMessageInfo

func (m *SwitchChannelTabReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SwitchChannelTabReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SwitchChannelTabReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SwitchChannelTabReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *SwitchChannelTabReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *SwitchChannelTabReq) GetSwitchTime() int64 {
	if m != nil {
		return m.SwitchTime
	}
	return 0
}

type SwitchChannelTabResp struct {
	TabName              string   `protobuf:"bytes,1,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	WelcomeTxtList       []string `protobuf:"bytes,2,rep,name=welcome_txt_list,json=welcomeTxtList,proto3" json:"welcome_txt_list,omitempty"`
	MicMod               uint32   `protobuf:"varint,3,opt,name=mic_mod,json=micMod,proto3" json:"mic_mod,omitempty"`
	TabType              uint32   `protobuf:"varint,4,opt,name=tab_type,json=tabType,proto3" json:"tab_type,omitempty"`
	TagId                uint32   `protobuf:"varint,5,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchChannelTabResp) Reset()         { *m = SwitchChannelTabResp{} }
func (m *SwitchChannelTabResp) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelTabResp) ProtoMessage()    {}
func (*SwitchChannelTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{14}
}
func (m *SwitchChannelTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelTabResp.Unmarshal(m, b)
}
func (m *SwitchChannelTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelTabResp.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelTabResp.Merge(dst, src)
}
func (m *SwitchChannelTabResp) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelTabResp.Size(m)
}
func (m *SwitchChannelTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelTabResp proto.InternalMessageInfo

func (m *SwitchChannelTabResp) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *SwitchChannelTabResp) GetWelcomeTxtList() []string {
	if m != nil {
		return m.WelcomeTxtList
	}
	return nil
}

func (m *SwitchChannelTabResp) GetMicMod() uint32 {
	if m != nil {
		return m.MicMod
	}
	return 0
}

func (m *SwitchChannelTabResp) GetTabType() uint32 {
	if m != nil {
		return m.TabType
	}
	return 0
}

func (m *SwitchChannelTabResp) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type DisappearChannelReq struct {
	ClientId             string                              `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	AcquireDuration      uint64                              `protobuf:"varint,2,opt,name=acquire_duration,json=acquireDuration,proto3" json:"acquire_duration,omitempty"`
	TimeoutEvent         *DisappearChannelReq_Timeout        `protobuf:"bytes,10,opt,name=timeout_event,json=timeoutEvent,proto3" json:"timeout_event,omitempty"`
	KeepaliveEvent       *DisappearChannelReq_Keepalive      `protobuf:"bytes,11,opt,name=keepalive_event,json=keepaliveEvent,proto3" json:"keepalive_event,omitempty"`
	ReleaseTimeoutEvent  *DisappearChannelReq_ReleaseTimeout `protobuf:"bytes,12,opt,name=release_timeout_event,json=releaseTimeoutEvent,proto3" json:"release_timeout_event,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *DisappearChannelReq) Reset()         { *m = DisappearChannelReq{} }
func (m *DisappearChannelReq) String() string { return proto.CompactTextString(m) }
func (*DisappearChannelReq) ProtoMessage()    {}
func (*DisappearChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{15}
}
func (m *DisappearChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearChannelReq.Unmarshal(m, b)
}
func (m *DisappearChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearChannelReq.Marshal(b, m, deterministic)
}
func (dst *DisappearChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearChannelReq.Merge(dst, src)
}
func (m *DisappearChannelReq) XXX_Size() int {
	return xxx_messageInfo_DisappearChannelReq.Size(m)
}
func (m *DisappearChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearChannelReq proto.InternalMessageInfo

func (m *DisappearChannelReq) GetClientId() string {
	if m != nil {
		return m.ClientId
	}
	return ""
}

func (m *DisappearChannelReq) GetAcquireDuration() uint64 {
	if m != nil {
		return m.AcquireDuration
	}
	return 0
}

func (m *DisappearChannelReq) GetTimeoutEvent() *DisappearChannelReq_Timeout {
	if m != nil {
		return m.TimeoutEvent
	}
	return nil
}

func (m *DisappearChannelReq) GetKeepaliveEvent() *DisappearChannelReq_Keepalive {
	if m != nil {
		return m.KeepaliveEvent
	}
	return nil
}

func (m *DisappearChannelReq) GetReleaseTimeoutEvent() *DisappearChannelReq_ReleaseTimeout {
	if m != nil {
		return m.ReleaseTimeoutEvent
	}
	return nil
}

type DisappearChannelReq_Timeout struct {
	TimeoutDuration      uint64   `protobuf:"varint,1,opt,name=timeout_duration,json=timeoutDuration,proto3" json:"timeout_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisappearChannelReq_Timeout) Reset()         { *m = DisappearChannelReq_Timeout{} }
func (m *DisappearChannelReq_Timeout) String() string { return proto.CompactTextString(m) }
func (*DisappearChannelReq_Timeout) ProtoMessage()    {}
func (*DisappearChannelReq_Timeout) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{15, 0}
}
func (m *DisappearChannelReq_Timeout) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearChannelReq_Timeout.Unmarshal(m, b)
}
func (m *DisappearChannelReq_Timeout) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearChannelReq_Timeout.Marshal(b, m, deterministic)
}
func (dst *DisappearChannelReq_Timeout) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearChannelReq_Timeout.Merge(dst, src)
}
func (m *DisappearChannelReq_Timeout) XXX_Size() int {
	return xxx_messageInfo_DisappearChannelReq_Timeout.Size(m)
}
func (m *DisappearChannelReq_Timeout) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearChannelReq_Timeout.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearChannelReq_Timeout proto.InternalMessageInfo

func (m *DisappearChannelReq_Timeout) GetTimeoutDuration() uint64 {
	if m != nil {
		return m.TimeoutDuration
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type DisappearChannelReq_Keepalive struct {
	KeepaliveDuration    uint64   `protobuf:"varint,1,opt,name=Keepalive_duration,json=KeepaliveDuration,proto3" json:"Keepalive_duration,omitempty"`
	MemberCount          uint32   `protobuf:"varint,2,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisappearChannelReq_Keepalive) Reset()         { *m = DisappearChannelReq_Keepalive{} }
func (m *DisappearChannelReq_Keepalive) String() string { return proto.CompactTextString(m) }
func (*DisappearChannelReq_Keepalive) ProtoMessage()    {}
func (*DisappearChannelReq_Keepalive) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{15, 1}
}
func (m *DisappearChannelReq_Keepalive) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearChannelReq_Keepalive.Unmarshal(m, b)
}
func (m *DisappearChannelReq_Keepalive) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearChannelReq_Keepalive.Marshal(b, m, deterministic)
}
func (dst *DisappearChannelReq_Keepalive) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearChannelReq_Keepalive.Merge(dst, src)
}
func (m *DisappearChannelReq_Keepalive) XXX_Size() int {
	return xxx_messageInfo_DisappearChannelReq_Keepalive.Size(m)
}
func (m *DisappearChannelReq_Keepalive) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearChannelReq_Keepalive.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearChannelReq_Keepalive proto.InternalMessageInfo

func (m *DisappearChannelReq_Keepalive) GetKeepaliveDuration() uint64 {
	if m != nil {
		return m.KeepaliveDuration
	}
	return 0
}

func (m *DisappearChannelReq_Keepalive) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

type DisappearChannelReq_ReleaseTimeout struct {
	TimeoutDuration      uint64   `protobuf:"varint,1,opt,name=timeout_duration,json=timeoutDuration,proto3" json:"timeout_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisappearChannelReq_ReleaseTimeout) Reset()         { *m = DisappearChannelReq_ReleaseTimeout{} }
func (m *DisappearChannelReq_ReleaseTimeout) String() string { return proto.CompactTextString(m) }
func (*DisappearChannelReq_ReleaseTimeout) ProtoMessage()    {}
func (*DisappearChannelReq_ReleaseTimeout) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{15, 2}
}
func (m *DisappearChannelReq_ReleaseTimeout) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearChannelReq_ReleaseTimeout.Unmarshal(m, b)
}
func (m *DisappearChannelReq_ReleaseTimeout) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearChannelReq_ReleaseTimeout.Marshal(b, m, deterministic)
}
func (dst *DisappearChannelReq_ReleaseTimeout) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearChannelReq_ReleaseTimeout.Merge(dst, src)
}
func (m *DisappearChannelReq_ReleaseTimeout) XXX_Size() int {
	return xxx_messageInfo_DisappearChannelReq_ReleaseTimeout.Size(m)
}
func (m *DisappearChannelReq_ReleaseTimeout) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearChannelReq_ReleaseTimeout.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearChannelReq_ReleaseTimeout proto.InternalMessageInfo

func (m *DisappearChannelReq_ReleaseTimeout) GetTimeoutDuration() uint64 {
	if m != nil {
		return m.TimeoutDuration
	}
	return 0
}

type DisappearChannelResp struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisappearChannelResp) Reset()         { *m = DisappearChannelResp{} }
func (m *DisappearChannelResp) String() string { return proto.CompactTextString(m) }
func (*DisappearChannelResp) ProtoMessage()    {}
func (*DisappearChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{16}
}
func (m *DisappearChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisappearChannelResp.Unmarshal(m, b)
}
func (m *DisappearChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisappearChannelResp.Marshal(b, m, deterministic)
}
func (dst *DisappearChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisappearChannelResp.Merge(dst, src)
}
func (m *DisappearChannelResp) XXX_Size() int {
	return xxx_messageInfo_DisappearChannelResp.Size(m)
}
func (m *DisappearChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DisappearChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_DisappearChannelResp proto.InternalMessageInfo

func (m *DisappearChannelResp) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type GetOnlineInfoReq struct {
	OnlineUserCount      uint32   `protobuf:"varint,1,opt,name=online_user_count,json=onlineUserCount,proto3" json:"online_user_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOnlineInfoReq) Reset()         { *m = GetOnlineInfoReq{} }
func (m *GetOnlineInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetOnlineInfoReq) ProtoMessage()    {}
func (*GetOnlineInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{17}
}
func (m *GetOnlineInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOnlineInfoReq.Unmarshal(m, b)
}
func (m *GetOnlineInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOnlineInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetOnlineInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOnlineInfoReq.Merge(dst, src)
}
func (m *GetOnlineInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetOnlineInfoReq.Size(m)
}
func (m *GetOnlineInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOnlineInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOnlineInfoReq proto.InternalMessageInfo

func (m *GetOnlineInfoReq) GetOnlineUserCount() uint32 {
	if m != nil {
		return m.OnlineUserCount
	}
	return 0
}

type GetOnlineInfoResp struct {
	RoomCount            uint32   `protobuf:"varint,1,opt,name=room_count,json=roomCount,proto3" json:"room_count,omitempty"`
	OnlineUserList       []uint32 `protobuf:"varint,2,rep,packed,name=online_user_list,json=onlineUserList,proto3" json:"online_user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOnlineInfoResp) Reset()         { *m = GetOnlineInfoResp{} }
func (m *GetOnlineInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetOnlineInfoResp) ProtoMessage()    {}
func (*GetOnlineInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{18}
}
func (m *GetOnlineInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOnlineInfoResp.Unmarshal(m, b)
}
func (m *GetOnlineInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOnlineInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetOnlineInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOnlineInfoResp.Merge(dst, src)
}
func (m *GetOnlineInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetOnlineInfoResp.Size(m)
}
func (m *GetOnlineInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOnlineInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOnlineInfoResp proto.InternalMessageInfo

func (m *GetOnlineInfoResp) GetRoomCount() uint32 {
	if m != nil {
		return m.RoomCount
	}
	return 0
}

func (m *GetOnlineInfoResp) GetOnlineUserList() []uint32 {
	if m != nil {
		return m.OnlineUserList
	}
	return nil
}

type FreezeChannelReq struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	FreezeTime           int64    `protobuf:"varint,2,opt,name=freeze_time,json=freezeTime,proto3" json:"freeze_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreezeChannelReq) Reset()         { *m = FreezeChannelReq{} }
func (m *FreezeChannelReq) String() string { return proto.CompactTextString(m) }
func (*FreezeChannelReq) ProtoMessage()    {}
func (*FreezeChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{19}
}
func (m *FreezeChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeChannelReq.Unmarshal(m, b)
}
func (m *FreezeChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeChannelReq.Marshal(b, m, deterministic)
}
func (dst *FreezeChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeChannelReq.Merge(dst, src)
}
func (m *FreezeChannelReq) XXX_Size() int {
	return xxx_messageInfo_FreezeChannelReq.Size(m)
}
func (m *FreezeChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeChannelReq proto.InternalMessageInfo

func (m *FreezeChannelReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *FreezeChannelReq) GetFreezeTime() int64 {
	if m != nil {
		return m.FreezeTime
	}
	return 0
}

type FreezeChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreezeChannelResp) Reset()         { *m = FreezeChannelResp{} }
func (m *FreezeChannelResp) String() string { return proto.CompactTextString(m) }
func (*FreezeChannelResp) ProtoMessage()    {}
func (*FreezeChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{20}
}
func (m *FreezeChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeChannelResp.Unmarshal(m, b)
}
func (m *FreezeChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeChannelResp.Marshal(b, m, deterministic)
}
func (dst *FreezeChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeChannelResp.Merge(dst, src)
}
func (m *FreezeChannelResp) XXX_Size() int {
	return xxx_messageInfo_FreezeChannelResp.Size(m)
}
func (m *FreezeChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeChannelResp proto.InternalMessageInfo

type UnfreezeChannelReq struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnfreezeChannelReq) Reset()         { *m = UnfreezeChannelReq{} }
func (m *UnfreezeChannelReq) String() string { return proto.CompactTextString(m) }
func (*UnfreezeChannelReq) ProtoMessage()    {}
func (*UnfreezeChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{21}
}
func (m *UnfreezeChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnfreezeChannelReq.Unmarshal(m, b)
}
func (m *UnfreezeChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnfreezeChannelReq.Marshal(b, m, deterministic)
}
func (dst *UnfreezeChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnfreezeChannelReq.Merge(dst, src)
}
func (m *UnfreezeChannelReq) XXX_Size() int {
	return xxx_messageInfo_UnfreezeChannelReq.Size(m)
}
func (m *UnfreezeChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnfreezeChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnfreezeChannelReq proto.InternalMessageInfo

func (m *UnfreezeChannelReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type UnfreezeChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnfreezeChannelResp) Reset()         { *m = UnfreezeChannelResp{} }
func (m *UnfreezeChannelResp) String() string { return proto.CompactTextString(m) }
func (*UnfreezeChannelResp) ProtoMessage()    {}
func (*UnfreezeChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{22}
}
func (m *UnfreezeChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnfreezeChannelResp.Unmarshal(m, b)
}
func (m *UnfreezeChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnfreezeChannelResp.Marshal(b, m, deterministic)
}
func (dst *UnfreezeChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnfreezeChannelResp.Merge(dst, src)
}
func (m *UnfreezeChannelResp) XXX_Size() int {
	return xxx_messageInfo_UnfreezeChannelResp.Size(m)
}
func (m *UnfreezeChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnfreezeChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnfreezeChannelResp proto.InternalMessageInfo

type GetChannelFreezeInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelFreezeInfoReq) Reset()         { *m = GetChannelFreezeInfoReq{} }
func (m *GetChannelFreezeInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelFreezeInfoReq) ProtoMessage()    {}
func (*GetChannelFreezeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{23}
}
func (m *GetChannelFreezeInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelFreezeInfoReq.Unmarshal(m, b)
}
func (m *GetChannelFreezeInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelFreezeInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelFreezeInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelFreezeInfoReq.Merge(dst, src)
}
func (m *GetChannelFreezeInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelFreezeInfoReq.Size(m)
}
func (m *GetChannelFreezeInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelFreezeInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelFreezeInfoReq proto.InternalMessageInfo

func (m *GetChannelFreezeInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelFreezeInfoResp struct {
	FreezeTime           int64    `protobuf:"varint,1,opt,name=freeze_time,json=freezeTime,proto3" json:"freeze_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelFreezeInfoResp) Reset()         { *m = GetChannelFreezeInfoResp{} }
func (m *GetChannelFreezeInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelFreezeInfoResp) ProtoMessage()    {}
func (*GetChannelFreezeInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{24}
}
func (m *GetChannelFreezeInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelFreezeInfoResp.Unmarshal(m, b)
}
func (m *GetChannelFreezeInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelFreezeInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelFreezeInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelFreezeInfoResp.Merge(dst, src)
}
func (m *GetChannelFreezeInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelFreezeInfoResp.Size(m)
}
func (m *GetChannelFreezeInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelFreezeInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelFreezeInfoResp proto.InternalMessageInfo

func (m *GetChannelFreezeInfoResp) GetFreezeTime() int64 {
	if m != nil {
		return m.FreezeTime
	}
	return 0
}

type SetExtraHistoryReq struct {
	ChannelId            uint32      `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	HistoryType          HistoryType `protobuf:"varint,2,opt,name=history_type,json=historyType,proto3,enum=gangup_channel.HistoryType" json:"history_type,omitempty"`
	ExpireAfter          int64       `protobuf:"varint,3,opt,name=expire_after,json=expireAfter,proto3" json:"expire_after,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SetExtraHistoryReq) Reset()         { *m = SetExtraHistoryReq{} }
func (m *SetExtraHistoryReq) String() string { return proto.CompactTextString(m) }
func (*SetExtraHistoryReq) ProtoMessage()    {}
func (*SetExtraHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{25}
}
func (m *SetExtraHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetExtraHistoryReq.Unmarshal(m, b)
}
func (m *SetExtraHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetExtraHistoryReq.Marshal(b, m, deterministic)
}
func (dst *SetExtraHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetExtraHistoryReq.Merge(dst, src)
}
func (m *SetExtraHistoryReq) XXX_Size() int {
	return xxx_messageInfo_SetExtraHistoryReq.Size(m)
}
func (m *SetExtraHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetExtraHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetExtraHistoryReq proto.InternalMessageInfo

func (m *SetExtraHistoryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetExtraHistoryReq) GetHistoryType() HistoryType {
	if m != nil {
		return m.HistoryType
	}
	return HistoryType_CreateHistory
}

func (m *SetExtraHistoryReq) GetExpireAfter() int64 {
	if m != nil {
		return m.ExpireAfter
	}
	return 0
}

type SetExtraHistoryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetExtraHistoryResp) Reset()         { *m = SetExtraHistoryResp{} }
func (m *SetExtraHistoryResp) String() string { return proto.CompactTextString(m) }
func (*SetExtraHistoryResp) ProtoMessage()    {}
func (*SetExtraHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{26}
}
func (m *SetExtraHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetExtraHistoryResp.Unmarshal(m, b)
}
func (m *SetExtraHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetExtraHistoryResp.Marshal(b, m, deterministic)
}
func (dst *SetExtraHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetExtraHistoryResp.Merge(dst, src)
}
func (m *SetExtraHistoryResp) XXX_Size() int {
	return xxx_messageInfo_SetExtraHistoryResp.Size(m)
}
func (m *SetExtraHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetExtraHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetExtraHistoryResp proto.InternalMessageInfo

type GetExtraHistoryReq struct {
	ChannelId            uint32      `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	HistoryType          HistoryType `protobuf:"varint,2,opt,name=history_type,json=historyType,proto3,enum=gangup_channel.HistoryType" json:"history_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetExtraHistoryReq) Reset()         { *m = GetExtraHistoryReq{} }
func (m *GetExtraHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetExtraHistoryReq) ProtoMessage()    {}
func (*GetExtraHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{27}
}
func (m *GetExtraHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraHistoryReq.Unmarshal(m, b)
}
func (m *GetExtraHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetExtraHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraHistoryReq.Merge(dst, src)
}
func (m *GetExtraHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetExtraHistoryReq.Size(m)
}
func (m *GetExtraHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraHistoryReq proto.InternalMessageInfo

func (m *GetExtraHistoryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetExtraHistoryReq) GetHistoryType() HistoryType {
	if m != nil {
		return m.HistoryType
	}
	return HistoryType_CreateHistory
}

type GetExtraHistoryResp struct {
	Value                string   `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExtraHistoryResp) Reset()         { *m = GetExtraHistoryResp{} }
func (m *GetExtraHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetExtraHistoryResp) ProtoMessage()    {}
func (*GetExtraHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{28}
}
func (m *GetExtraHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraHistoryResp.Unmarshal(m, b)
}
func (m *GetExtraHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetExtraHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraHistoryResp.Merge(dst, src)
}
func (m *GetExtraHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetExtraHistoryResp.Size(m)
}
func (m *GetExtraHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraHistoryResp proto.InternalMessageInfo

func (m *GetExtraHistoryResp) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type GetChannelPlayModelReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelPlayModelReq) Reset()         { *m = GetChannelPlayModelReq{} }
func (m *GetChannelPlayModelReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelPlayModelReq) ProtoMessage()    {}
func (*GetChannelPlayModelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{29}
}
func (m *GetChannelPlayModelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPlayModelReq.Unmarshal(m, b)
}
func (m *GetChannelPlayModelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPlayModelReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelPlayModelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPlayModelReq.Merge(dst, src)
}
func (m *GetChannelPlayModelReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelPlayModelReq.Size(m)
}
func (m *GetChannelPlayModelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPlayModelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPlayModelReq proto.InternalMessageInfo

func (m *GetChannelPlayModelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelPlayModelResp struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelPlayModelResp) Reset()         { *m = GetChannelPlayModelResp{} }
func (m *GetChannelPlayModelResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelPlayModelResp) ProtoMessage()    {}
func (*GetChannelPlayModelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{30}
}
func (m *GetChannelPlayModelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPlayModelResp.Unmarshal(m, b)
}
func (m *GetChannelPlayModelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPlayModelResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelPlayModelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPlayModelResp.Merge(dst, src)
}
func (m *GetChannelPlayModelResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelPlayModelResp.Size(m)
}
func (m *GetChannelPlayModelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPlayModelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPlayModelResp proto.InternalMessageInfo

func (m *GetChannelPlayModelResp) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetChannelRoomUserNumberReq struct {
	TabId                []uint32 `protobuf:"varint,1,rep,packed,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelRoomUserNumberReq) Reset()         { *m = GetChannelRoomUserNumberReq{} }
func (m *GetChannelRoomUserNumberReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelRoomUserNumberReq) ProtoMessage()    {}
func (*GetChannelRoomUserNumberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{31}
}
func (m *GetChannelRoomUserNumberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelRoomUserNumberReq.Unmarshal(m, b)
}
func (m *GetChannelRoomUserNumberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelRoomUserNumberReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelRoomUserNumberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelRoomUserNumberReq.Merge(dst, src)
}
func (m *GetChannelRoomUserNumberReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelRoomUserNumberReq.Size(m)
}
func (m *GetChannelRoomUserNumberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelRoomUserNumberReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelRoomUserNumberReq proto.InternalMessageInfo

func (m *GetChannelRoomUserNumberReq) GetTabId() []uint32 {
	if m != nil {
		return m.TabId
	}
	return nil
}

type GetChannelRoomUserNumberResp struct {
	RoomUserInfo         []*GetChannelRoomUserNumberResp_RoomUserInfo `protobuf:"bytes,1,rep,name=room_user_info,json=roomUserInfo,proto3" json:"room_user_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *GetChannelRoomUserNumberResp) Reset()         { *m = GetChannelRoomUserNumberResp{} }
func (m *GetChannelRoomUserNumberResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelRoomUserNumberResp) ProtoMessage()    {}
func (*GetChannelRoomUserNumberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{32}
}
func (m *GetChannelRoomUserNumberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelRoomUserNumberResp.Unmarshal(m, b)
}
func (m *GetChannelRoomUserNumberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelRoomUserNumberResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelRoomUserNumberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelRoomUserNumberResp.Merge(dst, src)
}
func (m *GetChannelRoomUserNumberResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelRoomUserNumberResp.Size(m)
}
func (m *GetChannelRoomUserNumberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelRoomUserNumberResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelRoomUserNumberResp proto.InternalMessageInfo

func (m *GetChannelRoomUserNumberResp) GetRoomUserInfo() []*GetChannelRoomUserNumberResp_RoomUserInfo {
	if m != nil {
		return m.RoomUserInfo
	}
	return nil
}

type GetChannelRoomUserNumberResp_RoomUserInfo struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TotalUserNumber      int64    `protobuf:"varint,2,opt,name=total_user_number,json=totalUserNumber,proto3" json:"total_user_number,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelRoomUserNumberResp_RoomUserInfo) Reset() {
	*m = GetChannelRoomUserNumberResp_RoomUserInfo{}
}
func (m *GetChannelRoomUserNumberResp_RoomUserInfo) String() string { return proto.CompactTextString(m) }
func (*GetChannelRoomUserNumberResp_RoomUserInfo) ProtoMessage()    {}
func (*GetChannelRoomUserNumberResp_RoomUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{32, 0}
}
func (m *GetChannelRoomUserNumberResp_RoomUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo.Unmarshal(m, b)
}
func (m *GetChannelRoomUserNumberResp_RoomUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo.Marshal(b, m, deterministic)
}
func (dst *GetChannelRoomUserNumberResp_RoomUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo.Merge(dst, src)
}
func (m *GetChannelRoomUserNumberResp_RoomUserInfo) XXX_Size() int {
	return xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo.Size(m)
}
func (m *GetChannelRoomUserNumberResp_RoomUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelRoomUserNumberResp_RoomUserInfo proto.InternalMessageInfo

func (m *GetChannelRoomUserNumberResp_RoomUserInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetChannelRoomUserNumberResp_RoomUserInfo) GetTotalUserNumber() int64 {
	if m != nil {
		return m.TotalUserNumber
	}
	return 0
}

type AddTemporaryChannelReq struct {
	Channel              *GangupChannelReleaseInfo `protobuf:"bytes,1,opt,name=channel,proto3" json:"channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *AddTemporaryChannelReq) Reset()         { *m = AddTemporaryChannelReq{} }
func (m *AddTemporaryChannelReq) String() string { return proto.CompactTextString(m) }
func (*AddTemporaryChannelReq) ProtoMessage()    {}
func (*AddTemporaryChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{33}
}
func (m *AddTemporaryChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTemporaryChannelReq.Unmarshal(m, b)
}
func (m *AddTemporaryChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTemporaryChannelReq.Marshal(b, m, deterministic)
}
func (dst *AddTemporaryChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTemporaryChannelReq.Merge(dst, src)
}
func (m *AddTemporaryChannelReq) XXX_Size() int {
	return xxx_messageInfo_AddTemporaryChannelReq.Size(m)
}
func (m *AddTemporaryChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTemporaryChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddTemporaryChannelReq proto.InternalMessageInfo

func (m *AddTemporaryChannelReq) GetChannel() *GangupChannelReleaseInfo {
	if m != nil {
		return m.Channel
	}
	return nil
}

type AddTemporaryChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTemporaryChannelResp) Reset()         { *m = AddTemporaryChannelResp{} }
func (m *AddTemporaryChannelResp) String() string { return proto.CompactTextString(m) }
func (*AddTemporaryChannelResp) ProtoMessage()    {}
func (*AddTemporaryChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{34}
}
func (m *AddTemporaryChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTemporaryChannelResp.Unmarshal(m, b)
}
func (m *AddTemporaryChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTemporaryChannelResp.Marshal(b, m, deterministic)
}
func (dst *AddTemporaryChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTemporaryChannelResp.Merge(dst, src)
}
func (m *AddTemporaryChannelResp) XXX_Size() int {
	return xxx_messageInfo_AddTemporaryChannelResp.Size(m)
}
func (m *AddTemporaryChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTemporaryChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddTemporaryChannelResp proto.InternalMessageInfo

// 负反馈上报
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type NegativeFeedBackReq struct {
	ChannelId                    uint32                 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                        uint32                 `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Creator                      uint32                 `protobuf:"varint,3,opt,name=creator,proto3" json:"creator,omitempty"`
	BlockOptions                 []*BlockOption         `protobuf:"bytes,4,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	Name                         string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Negative_FeedbackType        []NegativeFeedbackType `protobuf:"varint,6,rep,packed,name=negative_Feedback_type,json=negativeFeedbackType,proto3,enum=gangup_channel.NegativeFeedbackType" json:"negative_Feedback_type,omitempty"`
	ReporterUid                  uint32                 `protobuf:"varint,7,opt,name=reporter_uid,json=reporterUid,proto3" json:"reporter_uid,omitempty"`
	ChannelNameKeywords          []string               `protobuf:"bytes,8,rep,name=channel_name_keywords,json=channelNameKeywords,proto3" json:"channel_name_keywords,omitempty"`
	ChannelOwnerReasons          []string               `protobuf:"bytes,9,rep,name=channel_owner_reasons,json=channelOwnerReasons,proto3" json:"channel_owner_reasons,omitempty"`
	BlackChannelUser             uint32                 `protobuf:"varint,10,opt,name=black_channel_user,json=blackChannelUser,proto3" json:"black_channel_user,omitempty"`
	BlackChannelUserEnableFilter bool                   `protobuf:"varint,11,opt,name=black_channel_user_enable_filter,json=blackChannelUserEnableFilter,proto3" json:"black_channel_user_enable_filter,omitempty"`
	ReasonsOfBlackChannelUser    []string               `protobuf:"bytes,12,rep,name=reasons_of_black_channel_user,json=reasonsOfBlackChannelUser,proto3" json:"reasons_of_black_channel_user,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}               `json:"-"`
	XXX_unrecognized             []byte                 `json:"-"`
	XXX_sizecache                int32                  `json:"-"`
}

func (m *NegativeFeedBackReq) Reset()         { *m = NegativeFeedBackReq{} }
func (m *NegativeFeedBackReq) String() string { return proto.CompactTextString(m) }
func (*NegativeFeedBackReq) ProtoMessage()    {}
func (*NegativeFeedBackReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{35}
}
func (m *NegativeFeedBackReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NegativeFeedBackReq.Unmarshal(m, b)
}
func (m *NegativeFeedBackReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NegativeFeedBackReq.Marshal(b, m, deterministic)
}
func (dst *NegativeFeedBackReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NegativeFeedBackReq.Merge(dst, src)
}
func (m *NegativeFeedBackReq) XXX_Size() int {
	return xxx_messageInfo_NegativeFeedBackReq.Size(m)
}
func (m *NegativeFeedBackReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NegativeFeedBackReq.DiscardUnknown(m)
}

var xxx_messageInfo_NegativeFeedBackReq proto.InternalMessageInfo

func (m *NegativeFeedBackReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NegativeFeedBackReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *NegativeFeedBackReq) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *NegativeFeedBackReq) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *NegativeFeedBackReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *NegativeFeedBackReq) GetNegative_FeedbackType() []NegativeFeedbackType {
	if m != nil {
		return m.Negative_FeedbackType
	}
	return nil
}

func (m *NegativeFeedBackReq) GetReporterUid() uint32 {
	if m != nil {
		return m.ReporterUid
	}
	return 0
}

func (m *NegativeFeedBackReq) GetChannelNameKeywords() []string {
	if m != nil {
		return m.ChannelNameKeywords
	}
	return nil
}

func (m *NegativeFeedBackReq) GetChannelOwnerReasons() []string {
	if m != nil {
		return m.ChannelOwnerReasons
	}
	return nil
}

func (m *NegativeFeedBackReq) GetBlackChannelUser() uint32 {
	if m != nil {
		return m.BlackChannelUser
	}
	return 0
}

func (m *NegativeFeedBackReq) GetBlackChannelUserEnableFilter() bool {
	if m != nil {
		return m.BlackChannelUserEnableFilter
	}
	return false
}

func (m *NegativeFeedBackReq) GetReasonsOfBlackChannelUser() []string {
	if m != nil {
		return m.ReasonsOfBlackChannelUser
	}
	return nil
}

type NegativeFeedBackResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NegativeFeedBackResp) Reset()         { *m = NegativeFeedBackResp{} }
func (m *NegativeFeedBackResp) String() string { return proto.CompactTextString(m) }
func (*NegativeFeedBackResp) ProtoMessage()    {}
func (*NegativeFeedBackResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{36}
}
func (m *NegativeFeedBackResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NegativeFeedBackResp.Unmarshal(m, b)
}
func (m *NegativeFeedBackResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NegativeFeedBackResp.Marshal(b, m, deterministic)
}
func (dst *NegativeFeedBackResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NegativeFeedBackResp.Merge(dst, src)
}
func (m *NegativeFeedBackResp) XXX_Size() int {
	return xxx_messageInfo_NegativeFeedBackResp.Size(m)
}
func (m *NegativeFeedBackResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NegativeFeedBackResp.DiscardUnknown(m)
}

var xxx_messageInfo_NegativeFeedBackResp proto.InternalMessageInfo

type CleanChannelInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CleanChannelInfoReq) Reset()         { *m = CleanChannelInfoReq{} }
func (m *CleanChannelInfoReq) String() string { return proto.CompactTextString(m) }
func (*CleanChannelInfoReq) ProtoMessage()    {}
func (*CleanChannelInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{37}
}
func (m *CleanChannelInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CleanChannelInfoReq.Unmarshal(m, b)
}
func (m *CleanChannelInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CleanChannelInfoReq.Marshal(b, m, deterministic)
}
func (dst *CleanChannelInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CleanChannelInfoReq.Merge(dst, src)
}
func (m *CleanChannelInfoReq) XXX_Size() int {
	return xxx_messageInfo_CleanChannelInfoReq.Size(m)
}
func (m *CleanChannelInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CleanChannelInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_CleanChannelInfoReq proto.InternalMessageInfo

func (m *CleanChannelInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CleanChannelInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CleanChannelInfoResp) Reset()         { *m = CleanChannelInfoResp{} }
func (m *CleanChannelInfoResp) String() string { return proto.CompactTextString(m) }
func (*CleanChannelInfoResp) ProtoMessage()    {}
func (*CleanChannelInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{38}
}
func (m *CleanChannelInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CleanChannelInfoResp.Unmarshal(m, b)
}
func (m *CleanChannelInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CleanChannelInfoResp.Marshal(b, m, deterministic)
}
func (dst *CleanChannelInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CleanChannelInfoResp.Merge(dst, src)
}
func (m *CleanChannelInfoResp) XXX_Size() int {
	return xxx_messageInfo_CleanChannelInfoResp.Size(m)
}
func (m *CleanChannelInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CleanChannelInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_CleanChannelInfoResp proto.InternalMessageInfo

type GetChannelIdsByTabIdReq struct {
	TabId                uint32                          `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	GetMode              GetChannelIdsByTabIdReq_GetMode `protobuf:"varint,2,opt,name=get_mode,json=getMode,proto3,enum=gangup_channel.GetChannelIdsByTabIdReq_GetMode" json:"get_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetChannelIdsByTabIdReq) Reset()         { *m = GetChannelIdsByTabIdReq{} }
func (m *GetChannelIdsByTabIdReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelIdsByTabIdReq) ProtoMessage()    {}
func (*GetChannelIdsByTabIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{39}
}
func (m *GetChannelIdsByTabIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelIdsByTabIdReq.Unmarshal(m, b)
}
func (m *GetChannelIdsByTabIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelIdsByTabIdReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelIdsByTabIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelIdsByTabIdReq.Merge(dst, src)
}
func (m *GetChannelIdsByTabIdReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelIdsByTabIdReq.Size(m)
}
func (m *GetChannelIdsByTabIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelIdsByTabIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelIdsByTabIdReq proto.InternalMessageInfo

func (m *GetChannelIdsByTabIdReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetChannelIdsByTabIdReq) GetGetMode() GetChannelIdsByTabIdReq_GetMode {
	if m != nil {
		return m.GetMode
	}
	return GetChannelIdsByTabIdReq_ALL
}

type GetChannelIdsByTabIdResp struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelIdsByTabIdResp) Reset()         { *m = GetChannelIdsByTabIdResp{} }
func (m *GetChannelIdsByTabIdResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelIdsByTabIdResp) ProtoMessage()    {}
func (*GetChannelIdsByTabIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{40}
}
func (m *GetChannelIdsByTabIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelIdsByTabIdResp.Unmarshal(m, b)
}
func (m *GetChannelIdsByTabIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelIdsByTabIdResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelIdsByTabIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelIdsByTabIdResp.Merge(dst, src)
}
func (m *GetChannelIdsByTabIdResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelIdsByTabIdResp.Size(m)
}
func (m *GetChannelIdsByTabIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelIdsByTabIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelIdsByTabIdResp proto.InternalMessageInfo

func (m *GetChannelIdsByTabIdResp) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type GetGameHomePageDIYFilterReq struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RegulatoryLevel      REGULATORY_LEVEL `protobuf:"varint,2,opt,name=regulatory_level,json=regulatoryLevel,proto3,enum=gangup_channel.REGULATORY_LEVEL" json:"regulatory_level,omitempty"`
	EntryType            EntryType        `protobuf:"varint,3,opt,name=entry_type,json=entryType,proto3,enum=gangup_channel.EntryType" json:"entry_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGameHomePageDIYFilterReq) Reset()         { *m = GetGameHomePageDIYFilterReq{} }
func (m *GetGameHomePageDIYFilterReq) String() string { return proto.CompactTextString(m) }
func (*GetGameHomePageDIYFilterReq) ProtoMessage()    {}
func (*GetGameHomePageDIYFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{41}
}
func (m *GetGameHomePageDIYFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameHomePageDIYFilterReq.Unmarshal(m, b)
}
func (m *GetGameHomePageDIYFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameHomePageDIYFilterReq.Marshal(b, m, deterministic)
}
func (dst *GetGameHomePageDIYFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameHomePageDIYFilterReq.Merge(dst, src)
}
func (m *GetGameHomePageDIYFilterReq) XXX_Size() int {
	return xxx_messageInfo_GetGameHomePageDIYFilterReq.Size(m)
}
func (m *GetGameHomePageDIYFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameHomePageDIYFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameHomePageDIYFilterReq proto.InternalMessageInfo

func (m *GetGameHomePageDIYFilterReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGameHomePageDIYFilterReq) GetRegulatoryLevel() REGULATORY_LEVEL {
	if m != nil {
		return m.RegulatoryLevel
	}
	return REGULATORY_LEVEL_FREE
}

func (m *GetGameHomePageDIYFilterReq) GetEntryType() EntryType {
	if m != nil {
		return m.EntryType
	}
	return EntryType_GameHomePageEntry
}

type GetGameHomePageDIYFilterResp struct {
	Items                []*GameHomePageFilterItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetGameHomePageDIYFilterResp) Reset()         { *m = GetGameHomePageDIYFilterResp{} }
func (m *GetGameHomePageDIYFilterResp) String() string { return proto.CompactTextString(m) }
func (*GetGameHomePageDIYFilterResp) ProtoMessage()    {}
func (*GetGameHomePageDIYFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{42}
}
func (m *GetGameHomePageDIYFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameHomePageDIYFilterResp.Unmarshal(m, b)
}
func (m *GetGameHomePageDIYFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameHomePageDIYFilterResp.Marshal(b, m, deterministic)
}
func (dst *GetGameHomePageDIYFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameHomePageDIYFilterResp.Merge(dst, src)
}
func (m *GetGameHomePageDIYFilterResp) XXX_Size() int {
	return xxx_messageInfo_GetGameHomePageDIYFilterResp.Size(m)
}
func (m *GetGameHomePageDIYFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameHomePageDIYFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameHomePageDIYFilterResp proto.InternalMessageInfo

func (m *GetGameHomePageDIYFilterResp) GetItems() []*GameHomePageFilterItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type GameHomePageFilterItem struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	CategoryId           uint32   `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameHomePageFilterItem) Reset()         { *m = GameHomePageFilterItem{} }
func (m *GameHomePageFilterItem) String() string { return proto.CompactTextString(m) }
func (*GameHomePageFilterItem) ProtoMessage()    {}
func (*GameHomePageFilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{43}
}
func (m *GameHomePageFilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameHomePageFilterItem.Unmarshal(m, b)
}
func (m *GameHomePageFilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameHomePageFilterItem.Marshal(b, m, deterministic)
}
func (dst *GameHomePageFilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameHomePageFilterItem.Merge(dst, src)
}
func (m *GameHomePageFilterItem) XXX_Size() int {
	return xxx_messageInfo_GameHomePageFilterItem.Size(m)
}
func (m *GameHomePageFilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameHomePageFilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameHomePageFilterItem proto.InternalMessageInfo

func (m *GameHomePageFilterItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GameHomePageFilterItem) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *GameHomePageFilterItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type SetGameHomePageDIYFilterReq struct {
	Uid                  uint32                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Items                []*GameHomePageFilterItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	EntryType            EntryType                 `protobuf:"varint,3,opt,name=entry_type,json=entryType,proto3,enum=gangup_channel.EntryType" json:"entry_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *SetGameHomePageDIYFilterReq) Reset()         { *m = SetGameHomePageDIYFilterReq{} }
func (m *SetGameHomePageDIYFilterReq) String() string { return proto.CompactTextString(m) }
func (*SetGameHomePageDIYFilterReq) ProtoMessage()    {}
func (*SetGameHomePageDIYFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{44}
}
func (m *SetGameHomePageDIYFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameHomePageDIYFilterReq.Unmarshal(m, b)
}
func (m *SetGameHomePageDIYFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameHomePageDIYFilterReq.Marshal(b, m, deterministic)
}
func (dst *SetGameHomePageDIYFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameHomePageDIYFilterReq.Merge(dst, src)
}
func (m *SetGameHomePageDIYFilterReq) XXX_Size() int {
	return xxx_messageInfo_SetGameHomePageDIYFilterReq.Size(m)
}
func (m *SetGameHomePageDIYFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameHomePageDIYFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameHomePageDIYFilterReq proto.InternalMessageInfo

func (m *SetGameHomePageDIYFilterReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetGameHomePageDIYFilterReq) GetItems() []*GameHomePageFilterItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *SetGameHomePageDIYFilterReq) GetEntryType() EntryType {
	if m != nil {
		return m.EntryType
	}
	return EntryType_GameHomePageEntry
}

type SetGameHomePageDIYFilterResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGameHomePageDIYFilterResp) Reset()         { *m = SetGameHomePageDIYFilterResp{} }
func (m *SetGameHomePageDIYFilterResp) String() string { return proto.CompactTextString(m) }
func (*SetGameHomePageDIYFilterResp) ProtoMessage()    {}
func (*SetGameHomePageDIYFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{45}
}
func (m *SetGameHomePageDIYFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameHomePageDIYFilterResp.Unmarshal(m, b)
}
func (m *SetGameHomePageDIYFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameHomePageDIYFilterResp.Marshal(b, m, deterministic)
}
func (dst *SetGameHomePageDIYFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameHomePageDIYFilterResp.Merge(dst, src)
}
func (m *SetGameHomePageDIYFilterResp) XXX_Size() int {
	return xxx_messageInfo_SetGameHomePageDIYFilterResp.Size(m)
}
func (m *SetGameHomePageDIYFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameHomePageDIYFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameHomePageDIYFilterResp proto.InternalMessageInfo

type SetGangupChannelDenoiseModeReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Mode                 uint32   `protobuf:"varint,2,opt,name=mode,proto3" json:"mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGangupChannelDenoiseModeReq) Reset()         { *m = SetGangupChannelDenoiseModeReq{} }
func (m *SetGangupChannelDenoiseModeReq) String() string { return proto.CompactTextString(m) }
func (*SetGangupChannelDenoiseModeReq) ProtoMessage()    {}
func (*SetGangupChannelDenoiseModeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{46}
}
func (m *SetGangupChannelDenoiseModeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGangupChannelDenoiseModeReq.Unmarshal(m, b)
}
func (m *SetGangupChannelDenoiseModeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGangupChannelDenoiseModeReq.Marshal(b, m, deterministic)
}
func (dst *SetGangupChannelDenoiseModeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGangupChannelDenoiseModeReq.Merge(dst, src)
}
func (m *SetGangupChannelDenoiseModeReq) XXX_Size() int {
	return xxx_messageInfo_SetGangupChannelDenoiseModeReq.Size(m)
}
func (m *SetGangupChannelDenoiseModeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGangupChannelDenoiseModeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGangupChannelDenoiseModeReq proto.InternalMessageInfo

func (m *SetGangupChannelDenoiseModeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetGangupChannelDenoiseModeReq) GetMode() uint32 {
	if m != nil {
		return m.Mode
	}
	return 0
}

type SetGangupChannelDenoiseModeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGangupChannelDenoiseModeResp) Reset()         { *m = SetGangupChannelDenoiseModeResp{} }
func (m *SetGangupChannelDenoiseModeResp) String() string { return proto.CompactTextString(m) }
func (*SetGangupChannelDenoiseModeResp) ProtoMessage()    {}
func (*SetGangupChannelDenoiseModeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{47}
}
func (m *SetGangupChannelDenoiseModeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGangupChannelDenoiseModeResp.Unmarshal(m, b)
}
func (m *SetGangupChannelDenoiseModeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGangupChannelDenoiseModeResp.Marshal(b, m, deterministic)
}
func (dst *SetGangupChannelDenoiseModeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGangupChannelDenoiseModeResp.Merge(dst, src)
}
func (m *SetGangupChannelDenoiseModeResp) XXX_Size() int {
	return xxx_messageInfo_SetGangupChannelDenoiseModeResp.Size(m)
}
func (m *SetGangupChannelDenoiseModeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGangupChannelDenoiseModeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGangupChannelDenoiseModeResp proto.InternalMessageInfo

// 开黑房扩展信息
type GangupChannelExtraInfo struct {
	// 降噪模式
	DenoiseMode          uint32   `protobuf:"varint,1,opt,name=denoise_mode,json=denoiseMode,proto3" json:"denoise_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GangupChannelExtraInfo) Reset()         { *m = GangupChannelExtraInfo{} }
func (m *GangupChannelExtraInfo) String() string { return proto.CompactTextString(m) }
func (*GangupChannelExtraInfo) ProtoMessage()    {}
func (*GangupChannelExtraInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{48}
}
func (m *GangupChannelExtraInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GangupChannelExtraInfo.Unmarshal(m, b)
}
func (m *GangupChannelExtraInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GangupChannelExtraInfo.Marshal(b, m, deterministic)
}
func (dst *GangupChannelExtraInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GangupChannelExtraInfo.Merge(dst, src)
}
func (m *GangupChannelExtraInfo) XXX_Size() int {
	return xxx_messageInfo_GangupChannelExtraInfo.Size(m)
}
func (m *GangupChannelExtraInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GangupChannelExtraInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GangupChannelExtraInfo proto.InternalMessageInfo

func (m *GangupChannelExtraInfo) GetDenoiseMode() uint32 {
	if m != nil {
		return m.DenoiseMode
	}
	return 0
}

type GetGangupChannelExtraInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGangupChannelExtraInfoReq) Reset()         { *m = GetGangupChannelExtraInfoReq{} }
func (m *GetGangupChannelExtraInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetGangupChannelExtraInfoReq) ProtoMessage()    {}
func (*GetGangupChannelExtraInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{49}
}
func (m *GetGangupChannelExtraInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangupChannelExtraInfoReq.Unmarshal(m, b)
}
func (m *GetGangupChannelExtraInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangupChannelExtraInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetGangupChannelExtraInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangupChannelExtraInfoReq.Merge(dst, src)
}
func (m *GetGangupChannelExtraInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetGangupChannelExtraInfoReq.Size(m)
}
func (m *GetGangupChannelExtraInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangupChannelExtraInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangupChannelExtraInfoReq proto.InternalMessageInfo

func (m *GetGangupChannelExtraInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetGangupChannelExtraInfoResp struct {
	Info                 *GangupChannelExtraInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetGangupChannelExtraInfoResp) Reset()         { *m = GetGangupChannelExtraInfoResp{} }
func (m *GetGangupChannelExtraInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGangupChannelExtraInfoResp) ProtoMessage()    {}
func (*GetGangupChannelExtraInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{50}
}
func (m *GetGangupChannelExtraInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangupChannelExtraInfoResp.Unmarshal(m, b)
}
func (m *GetGangupChannelExtraInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangupChannelExtraInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetGangupChannelExtraInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangupChannelExtraInfoResp.Merge(dst, src)
}
func (m *GetGangupChannelExtraInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetGangupChannelExtraInfoResp.Size(m)
}
func (m *GetGangupChannelExtraInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangupChannelExtraInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangupChannelExtraInfoResp proto.InternalMessageInfo

func (m *GetGangupChannelExtraInfoResp) GetInfo() *GangupChannelExtraInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 获取开黑房间冷却时间信息（发布冷却时间，修改冷却时间）
type GetGangupExtraHistoryReq struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	IsRelease            bool     `protobuf:"varint,2,opt,name=is_release,json=isRelease,proto3" json:"is_release,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGangupExtraHistoryReq) Reset()         { *m = GetGangupExtraHistoryReq{} }
func (m *GetGangupExtraHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetGangupExtraHistoryReq) ProtoMessage()    {}
func (*GetGangupExtraHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{51}
}
func (m *GetGangupExtraHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangupExtraHistoryReq.Unmarshal(m, b)
}
func (m *GetGangupExtraHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangupExtraHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetGangupExtraHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangupExtraHistoryReq.Merge(dst, src)
}
func (m *GetGangupExtraHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetGangupExtraHistoryReq.Size(m)
}
func (m *GetGangupExtraHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangupExtraHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangupExtraHistoryReq proto.InternalMessageInfo

func (m *GetGangupExtraHistoryReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetGangupExtraHistoryReq) GetIsRelease() bool {
	if m != nil {
		return m.IsRelease
	}
	return false
}

type GetGangupExtraHistoryResp struct {
	Value                string   `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGangupExtraHistoryResp) Reset()         { *m = GetGangupExtraHistoryResp{} }
func (m *GetGangupExtraHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetGangupExtraHistoryResp) ProtoMessage()    {}
func (*GetGangupExtraHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{52}
}
func (m *GetGangupExtraHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangupExtraHistoryResp.Unmarshal(m, b)
}
func (m *GetGangupExtraHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangupExtraHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetGangupExtraHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangupExtraHistoryResp.Merge(dst, src)
}
func (m *GetGangupExtraHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetGangupExtraHistoryResp.Size(m)
}
func (m *GetGangupExtraHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangupExtraHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangupExtraHistoryResp proto.InternalMessageInfo

func (m *GetGangupExtraHistoryResp) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type SetGangupExtraHistoryReq struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	Value                string   `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	ExpireAfter          int64    `protobuf:"varint,3,opt,name=expire_after,json=expireAfter,proto3" json:"expire_after,omitempty"`
	IsRelease            bool     `protobuf:"varint,4,opt,name=is_release,json=isRelease,proto3" json:"is_release,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGangupExtraHistoryReq) Reset()         { *m = SetGangupExtraHistoryReq{} }
func (m *SetGangupExtraHistoryReq) String() string { return proto.CompactTextString(m) }
func (*SetGangupExtraHistoryReq) ProtoMessage()    {}
func (*SetGangupExtraHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{53}
}
func (m *SetGangupExtraHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGangupExtraHistoryReq.Unmarshal(m, b)
}
func (m *SetGangupExtraHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGangupExtraHistoryReq.Marshal(b, m, deterministic)
}
func (dst *SetGangupExtraHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGangupExtraHistoryReq.Merge(dst, src)
}
func (m *SetGangupExtraHistoryReq) XXX_Size() int {
	return xxx_messageInfo_SetGangupExtraHistoryReq.Size(m)
}
func (m *SetGangupExtraHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGangupExtraHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGangupExtraHistoryReq proto.InternalMessageInfo

func (m *SetGangupExtraHistoryReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SetGangupExtraHistoryReq) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func (m *SetGangupExtraHistoryReq) GetExpireAfter() int64 {
	if m != nil {
		return m.ExpireAfter
	}
	return 0
}

func (m *SetGangupExtraHistoryReq) GetIsRelease() bool {
	if m != nil {
		return m.IsRelease
	}
	return false
}

type SetGangupExtraHistoryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGangupExtraHistoryResp) Reset()         { *m = SetGangupExtraHistoryResp{} }
func (m *SetGangupExtraHistoryResp) String() string { return proto.CompactTextString(m) }
func (*SetGangupExtraHistoryResp) ProtoMessage()    {}
func (*SetGangupExtraHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{54}
}
func (m *SetGangupExtraHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGangupExtraHistoryResp.Unmarshal(m, b)
}
func (m *SetGangupExtraHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGangupExtraHistoryResp.Marshal(b, m, deterministic)
}
func (dst *SetGangupExtraHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGangupExtraHistoryResp.Merge(dst, src)
}
func (m *SetGangupExtraHistoryResp) XXX_Size() int {
	return xxx_messageInfo_SetGangupExtraHistoryResp.Size(m)
}
func (m *SetGangupExtraHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGangupExtraHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGangupExtraHistoryResp proto.InternalMessageInfo

// 获取开黑房间发布次数
type GetGangupPublishCountHistoryReq struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGangupPublishCountHistoryReq) Reset()         { *m = GetGangupPublishCountHistoryReq{} }
func (m *GetGangupPublishCountHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetGangupPublishCountHistoryReq) ProtoMessage()    {}
func (*GetGangupPublishCountHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{55}
}
func (m *GetGangupPublishCountHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangupPublishCountHistoryReq.Unmarshal(m, b)
}
func (m *GetGangupPublishCountHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangupPublishCountHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetGangupPublishCountHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangupPublishCountHistoryReq.Merge(dst, src)
}
func (m *GetGangupPublishCountHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetGangupPublishCountHistoryReq.Size(m)
}
func (m *GetGangupPublishCountHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangupPublishCountHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangupPublishCountHistoryReq proto.InternalMessageInfo

func (m *GetGangupPublishCountHistoryReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

type GetGangupPublishCountHistoryResp struct {
	PublishCount         uint32   `protobuf:"varint,1,opt,name=publish_count,json=publishCount,proto3" json:"publish_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGangupPublishCountHistoryResp) Reset()         { *m = GetGangupPublishCountHistoryResp{} }
func (m *GetGangupPublishCountHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetGangupPublishCountHistoryResp) ProtoMessage()    {}
func (*GetGangupPublishCountHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{56}
}
func (m *GetGangupPublishCountHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangupPublishCountHistoryResp.Unmarshal(m, b)
}
func (m *GetGangupPublishCountHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangupPublishCountHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetGangupPublishCountHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangupPublishCountHistoryResp.Merge(dst, src)
}
func (m *GetGangupPublishCountHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetGangupPublishCountHistoryResp.Size(m)
}
func (m *GetGangupPublishCountHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangupPublishCountHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangupPublishCountHistoryResp proto.InternalMessageInfo

func (m *GetGangupPublishCountHistoryResp) GetPublishCount() uint32 {
	if m != nil {
		return m.PublishCount
	}
	return 0
}

type SetGangupPublishCountHistoryReq struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGangupPublishCountHistoryReq) Reset()         { *m = SetGangupPublishCountHistoryReq{} }
func (m *SetGangupPublishCountHistoryReq) String() string { return proto.CompactTextString(m) }
func (*SetGangupPublishCountHistoryReq) ProtoMessage()    {}
func (*SetGangupPublishCountHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{57}
}
func (m *SetGangupPublishCountHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGangupPublishCountHistoryReq.Unmarshal(m, b)
}
func (m *SetGangupPublishCountHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGangupPublishCountHistoryReq.Marshal(b, m, deterministic)
}
func (dst *SetGangupPublishCountHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGangupPublishCountHistoryReq.Merge(dst, src)
}
func (m *SetGangupPublishCountHistoryReq) XXX_Size() int {
	return xxx_messageInfo_SetGangupPublishCountHistoryReq.Size(m)
}
func (m *SetGangupPublishCountHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGangupPublishCountHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGangupPublishCountHistoryReq proto.InternalMessageInfo

func (m *SetGangupPublishCountHistoryReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

type SetGangupPublishCountHistoryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGangupPublishCountHistoryResp) Reset()         { *m = SetGangupPublishCountHistoryResp{} }
func (m *SetGangupPublishCountHistoryResp) String() string { return proto.CompactTextString(m) }
func (*SetGangupPublishCountHistoryResp) ProtoMessage()    {}
func (*SetGangupPublishCountHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{58}
}
func (m *SetGangupPublishCountHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGangupPublishCountHistoryResp.Unmarshal(m, b)
}
func (m *SetGangupPublishCountHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGangupPublishCountHistoryResp.Marshal(b, m, deterministic)
}
func (dst *SetGangupPublishCountHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGangupPublishCountHistoryResp.Merge(dst, src)
}
func (m *SetGangupPublishCountHistoryResp) XXX_Size() int {
	return xxx_messageInfo_SetGangupPublishCountHistoryResp.Size(m)
}
func (m *SetGangupPublishCountHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGangupPublishCountHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGangupPublishCountHistoryResp proto.InternalMessageInfo

// 设置根据入口设置常玩分类
type SetDIYFilterByEntranceReq struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Items                []*DIYFilterItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	EntryType            uint32           `protobuf:"varint,3,opt,name=entry_type,json=entryType,proto3" json:"entry_type,omitempty"`
	IsIncr               bool             `protobuf:"varint,4,opt,name=is_incr,json=isIncr,proto3" json:"is_incr,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SetDIYFilterByEntranceReq) Reset()         { *m = SetDIYFilterByEntranceReq{} }
func (m *SetDIYFilterByEntranceReq) String() string { return proto.CompactTextString(m) }
func (*SetDIYFilterByEntranceReq) ProtoMessage()    {}
func (*SetDIYFilterByEntranceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{59}
}
func (m *SetDIYFilterByEntranceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDIYFilterByEntranceReq.Unmarshal(m, b)
}
func (m *SetDIYFilterByEntranceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDIYFilterByEntranceReq.Marshal(b, m, deterministic)
}
func (dst *SetDIYFilterByEntranceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDIYFilterByEntranceReq.Merge(dst, src)
}
func (m *SetDIYFilterByEntranceReq) XXX_Size() int {
	return xxx_messageInfo_SetDIYFilterByEntranceReq.Size(m)
}
func (m *SetDIYFilterByEntranceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDIYFilterByEntranceReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetDIYFilterByEntranceReq proto.InternalMessageInfo

func (m *SetDIYFilterByEntranceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetDIYFilterByEntranceReq) GetItems() []*DIYFilterItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *SetDIYFilterByEntranceReq) GetEntryType() uint32 {
	if m != nil {
		return m.EntryType
	}
	return 0
}

func (m *SetDIYFilterByEntranceReq) GetIsIncr() bool {
	if m != nil {
		return m.IsIncr
	}
	return false
}

// 标识业务
type DIYFilterItem struct {
	GameBusinessId       uint32   `protobuf:"varint,1,opt,name=game_business_id,json=gameBusinessId,proto3" json:"game_business_id,omitempty"`
	MusicBusinessId      string   `protobuf:"bytes,2,opt,name=music_business_id,json=musicBusinessId,proto3" json:"music_business_id,omitempty"`
	FilterItemType       uint32   `protobuf:"varint,3,opt,name=filter_item_type,json=filterItemType,proto3" json:"filter_item_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DIYFilterItem) Reset()         { *m = DIYFilterItem{} }
func (m *DIYFilterItem) String() string { return proto.CompactTextString(m) }
func (*DIYFilterItem) ProtoMessage()    {}
func (*DIYFilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{60}
}
func (m *DIYFilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DIYFilterItem.Unmarshal(m, b)
}
func (m *DIYFilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DIYFilterItem.Marshal(b, m, deterministic)
}
func (dst *DIYFilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DIYFilterItem.Merge(dst, src)
}
func (m *DIYFilterItem) XXX_Size() int {
	return xxx_messageInfo_DIYFilterItem.Size(m)
}
func (m *DIYFilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_DIYFilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_DIYFilterItem proto.InternalMessageInfo

func (m *DIYFilterItem) GetGameBusinessId() uint32 {
	if m != nil {
		return m.GameBusinessId
	}
	return 0
}

func (m *DIYFilterItem) GetMusicBusinessId() string {
	if m != nil {
		return m.MusicBusinessId
	}
	return ""
}

func (m *DIYFilterItem) GetFilterItemType() uint32 {
	if m != nil {
		return m.FilterItemType
	}
	return 0
}

type SetDIYFilterByEntranceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDIYFilterByEntranceResp) Reset()         { *m = SetDIYFilterByEntranceResp{} }
func (m *SetDIYFilterByEntranceResp) String() string { return proto.CompactTextString(m) }
func (*SetDIYFilterByEntranceResp) ProtoMessage()    {}
func (*SetDIYFilterByEntranceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{61}
}
func (m *SetDIYFilterByEntranceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDIYFilterByEntranceResp.Unmarshal(m, b)
}
func (m *SetDIYFilterByEntranceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDIYFilterByEntranceResp.Marshal(b, m, deterministic)
}
func (dst *SetDIYFilterByEntranceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDIYFilterByEntranceResp.Merge(dst, src)
}
func (m *SetDIYFilterByEntranceResp) XXX_Size() int {
	return xxx_messageInfo_SetDIYFilterByEntranceResp.Size(m)
}
func (m *SetDIYFilterByEntranceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDIYFilterByEntranceResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetDIYFilterByEntranceResp proto.InternalMessageInfo

// 根据入口获取常玩分类
type GetDIYFilterByEntranceReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	EntryType            uint32   `protobuf:"varint,2,opt,name=entry_type,json=entryType,proto3" json:"entry_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDIYFilterByEntranceReq) Reset()         { *m = GetDIYFilterByEntranceReq{} }
func (m *GetDIYFilterByEntranceReq) String() string { return proto.CompactTextString(m) }
func (*GetDIYFilterByEntranceReq) ProtoMessage()    {}
func (*GetDIYFilterByEntranceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{62}
}
func (m *GetDIYFilterByEntranceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDIYFilterByEntranceReq.Unmarshal(m, b)
}
func (m *GetDIYFilterByEntranceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDIYFilterByEntranceReq.Marshal(b, m, deterministic)
}
func (dst *GetDIYFilterByEntranceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDIYFilterByEntranceReq.Merge(dst, src)
}
func (m *GetDIYFilterByEntranceReq) XXX_Size() int {
	return xxx_messageInfo_GetDIYFilterByEntranceReq.Size(m)
}
func (m *GetDIYFilterByEntranceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDIYFilterByEntranceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDIYFilterByEntranceReq proto.InternalMessageInfo

func (m *GetDIYFilterByEntranceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetDIYFilterByEntranceReq) GetEntryType() uint32 {
	if m != nil {
		return m.EntryType
	}
	return 0
}

type GetDIYFilterByEntranceResp struct {
	Items                []*DIYFilterItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetDIYFilterByEntranceResp) Reset()         { *m = GetDIYFilterByEntranceResp{} }
func (m *GetDIYFilterByEntranceResp) String() string { return proto.CompactTextString(m) }
func (*GetDIYFilterByEntranceResp) ProtoMessage()    {}
func (*GetDIYFilterByEntranceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{63}
}
func (m *GetDIYFilterByEntranceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDIYFilterByEntranceResp.Unmarshal(m, b)
}
func (m *GetDIYFilterByEntranceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDIYFilterByEntranceResp.Marshal(b, m, deterministic)
}
func (dst *GetDIYFilterByEntranceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDIYFilterByEntranceResp.Merge(dst, src)
}
func (m *GetDIYFilterByEntranceResp) XXX_Size() int {
	return xxx_messageInfo_GetDIYFilterByEntranceResp.Size(m)
}
func (m *GetDIYFilterByEntranceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDIYFilterByEntranceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDIYFilterByEntranceResp proto.InternalMessageInfo

func (m *GetDIYFilterByEntranceResp) GetItems() []*DIYFilterItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 根据玩法获取发布房间数
type GetReleasingChannelCountByTabIdsReq struct {
	TabIds               []uint32 `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReleasingChannelCountByTabIdsReq) Reset()         { *m = GetReleasingChannelCountByTabIdsReq{} }
func (m *GetReleasingChannelCountByTabIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetReleasingChannelCountByTabIdsReq) ProtoMessage()    {}
func (*GetReleasingChannelCountByTabIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{64}
}
func (m *GetReleasingChannelCountByTabIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReleasingChannelCountByTabIdsReq.Unmarshal(m, b)
}
func (m *GetReleasingChannelCountByTabIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReleasingChannelCountByTabIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetReleasingChannelCountByTabIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReleasingChannelCountByTabIdsReq.Merge(dst, src)
}
func (m *GetReleasingChannelCountByTabIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetReleasingChannelCountByTabIdsReq.Size(m)
}
func (m *GetReleasingChannelCountByTabIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReleasingChannelCountByTabIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetReleasingChannelCountByTabIdsReq proto.InternalMessageInfo

func (m *GetReleasingChannelCountByTabIdsReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type GetReleasingChannelCountByTabIdsResp struct {
	CountMap             map[uint32]int64 `protobuf:"bytes,1,rep,name=count_map,json=countMap,proto3" json:"count_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetReleasingChannelCountByTabIdsResp) Reset()         { *m = GetReleasingChannelCountByTabIdsResp{} }
func (m *GetReleasingChannelCountByTabIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetReleasingChannelCountByTabIdsResp) ProtoMessage()    {}
func (*GetReleasingChannelCountByTabIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{65}
}
func (m *GetReleasingChannelCountByTabIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReleasingChannelCountByTabIdsResp.Unmarshal(m, b)
}
func (m *GetReleasingChannelCountByTabIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReleasingChannelCountByTabIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetReleasingChannelCountByTabIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReleasingChannelCountByTabIdsResp.Merge(dst, src)
}
func (m *GetReleasingChannelCountByTabIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetReleasingChannelCountByTabIdsResp.Size(m)
}
func (m *GetReleasingChannelCountByTabIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReleasingChannelCountByTabIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetReleasingChannelCountByTabIdsResp proto.InternalMessageInfo

func (m *GetReleasingChannelCountByTabIdsResp) GetCountMap() map[uint32]int64 {
	if m != nil {
		return m.CountMap
	}
	return nil
}

// 房间内反馈
type ChannelFeedback struct {
	// 反馈原因
	Reasons []string `protobuf:"bytes,1,rep,name=reasons,proto3" json:"reasons,omitempty"`
	// 屏蔽文案
	BlockText            string   `protobuf:"bytes,2,opt,name=block_text,json=blockText,proto3" json:"block_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelFeedback) Reset()         { *m = ChannelFeedback{} }
func (m *ChannelFeedback) String() string { return proto.CompactTextString(m) }
func (*ChannelFeedback) ProtoMessage()    {}
func (*ChannelFeedback) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{66}
}
func (m *ChannelFeedback) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelFeedback.Unmarshal(m, b)
}
func (m *ChannelFeedback) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelFeedback.Marshal(b, m, deterministic)
}
func (dst *ChannelFeedback) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelFeedback.Merge(dst, src)
}
func (m *ChannelFeedback) XXX_Size() int {
	return xxx_messageInfo_ChannelFeedback.Size(m)
}
func (m *ChannelFeedback) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelFeedback.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelFeedback proto.InternalMessageInfo

func (m *ChannelFeedback) GetReasons() []string {
	if m != nil {
		return m.Reasons
	}
	return nil
}

func (m *ChannelFeedback) GetBlockText() string {
	if m != nil {
		return m.BlockText
	}
	return ""
}

type NegativeFeedback struct {
	// 列表房主反馈
	ListOwnerFeedback []string `protobuf:"bytes,1,rep,name=list_owner_feedback,json=listOwnerFeedback,proto3" json:"list_owner_feedback,omitempty"`
	// 房间内部反馈原因配置(房主侧)
	OwnerChannelFeedback *ChannelFeedback `protobuf:"bytes,2,opt,name=owner_channel_feedback,json=ownerChannelFeedback,proto3" json:"owner_channel_feedback,omitempty"`
	// 房间内部反馈原因配置(房客侧)
	GuestChannelFeedback *ChannelFeedback `protobuf:"bytes,3,opt,name=guest_channel_feedback,json=guestChannelFeedback,proto3" json:"guest_channel_feedback,omitempty"`
	// 房间内退房反馈配置
	QuitChannelFeedback  *ChannelFeedback `protobuf:"bytes,4,opt,name=quit_channel_feedback,json=quitChannelFeedback,proto3" json:"quit_channel_feedback,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *NegativeFeedback) Reset()         { *m = NegativeFeedback{} }
func (m *NegativeFeedback) String() string { return proto.CompactTextString(m) }
func (*NegativeFeedback) ProtoMessage()    {}
func (*NegativeFeedback) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{67}
}
func (m *NegativeFeedback) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NegativeFeedback.Unmarshal(m, b)
}
func (m *NegativeFeedback) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NegativeFeedback.Marshal(b, m, deterministic)
}
func (dst *NegativeFeedback) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NegativeFeedback.Merge(dst, src)
}
func (m *NegativeFeedback) XXX_Size() int {
	return xxx_messageInfo_NegativeFeedback.Size(m)
}
func (m *NegativeFeedback) XXX_DiscardUnknown() {
	xxx_messageInfo_NegativeFeedback.DiscardUnknown(m)
}

var xxx_messageInfo_NegativeFeedback proto.InternalMessageInfo

func (m *NegativeFeedback) GetListOwnerFeedback() []string {
	if m != nil {
		return m.ListOwnerFeedback
	}
	return nil
}

func (m *NegativeFeedback) GetOwnerChannelFeedback() *ChannelFeedback {
	if m != nil {
		return m.OwnerChannelFeedback
	}
	return nil
}

func (m *NegativeFeedback) GetGuestChannelFeedback() *ChannelFeedback {
	if m != nil {
		return m.GuestChannelFeedback
	}
	return nil
}

func (m *NegativeFeedback) GetQuitChannelFeedback() *ChannelFeedback {
	if m != nil {
		return m.QuitChannelFeedback
	}
	return nil
}

type UpdateNegativeFeedbackOptionReq struct {
	Feedback             *NegativeFeedback `protobuf:"bytes,1,opt,name=feedback,proto3" json:"feedback,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UpdateNegativeFeedbackOptionReq) Reset()         { *m = UpdateNegativeFeedbackOptionReq{} }
func (m *UpdateNegativeFeedbackOptionReq) String() string { return proto.CompactTextString(m) }
func (*UpdateNegativeFeedbackOptionReq) ProtoMessage()    {}
func (*UpdateNegativeFeedbackOptionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{68}
}
func (m *UpdateNegativeFeedbackOptionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNegativeFeedbackOptionReq.Unmarshal(m, b)
}
func (m *UpdateNegativeFeedbackOptionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNegativeFeedbackOptionReq.Marshal(b, m, deterministic)
}
func (dst *UpdateNegativeFeedbackOptionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNegativeFeedbackOptionReq.Merge(dst, src)
}
func (m *UpdateNegativeFeedbackOptionReq) XXX_Size() int {
	return xxx_messageInfo_UpdateNegativeFeedbackOptionReq.Size(m)
}
func (m *UpdateNegativeFeedbackOptionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNegativeFeedbackOptionReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNegativeFeedbackOptionReq proto.InternalMessageInfo

func (m *UpdateNegativeFeedbackOptionReq) GetFeedback() *NegativeFeedback {
	if m != nil {
		return m.Feedback
	}
	return nil
}

type UpdateNegativeFeedbackOptionResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateNegativeFeedbackOptionResp) Reset()         { *m = UpdateNegativeFeedbackOptionResp{} }
func (m *UpdateNegativeFeedbackOptionResp) String() string { return proto.CompactTextString(m) }
func (*UpdateNegativeFeedbackOptionResp) ProtoMessage()    {}
func (*UpdateNegativeFeedbackOptionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{69}
}
func (m *UpdateNegativeFeedbackOptionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNegativeFeedbackOptionResp.Unmarshal(m, b)
}
func (m *UpdateNegativeFeedbackOptionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNegativeFeedbackOptionResp.Marshal(b, m, deterministic)
}
func (dst *UpdateNegativeFeedbackOptionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNegativeFeedbackOptionResp.Merge(dst, src)
}
func (m *UpdateNegativeFeedbackOptionResp) XXX_Size() int {
	return xxx_messageInfo_UpdateNegativeFeedbackOptionResp.Size(m)
}
func (m *UpdateNegativeFeedbackOptionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNegativeFeedbackOptionResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNegativeFeedbackOptionResp proto.InternalMessageInfo

type GetNegativeFeedbackOptionReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNegativeFeedbackOptionReq) Reset()         { *m = GetNegativeFeedbackOptionReq{} }
func (m *GetNegativeFeedbackOptionReq) String() string { return proto.CompactTextString(m) }
func (*GetNegativeFeedbackOptionReq) ProtoMessage()    {}
func (*GetNegativeFeedbackOptionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{70}
}
func (m *GetNegativeFeedbackOptionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNegativeFeedbackOptionReq.Unmarshal(m, b)
}
func (m *GetNegativeFeedbackOptionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNegativeFeedbackOptionReq.Marshal(b, m, deterministic)
}
func (dst *GetNegativeFeedbackOptionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNegativeFeedbackOptionReq.Merge(dst, src)
}
func (m *GetNegativeFeedbackOptionReq) XXX_Size() int {
	return xxx_messageInfo_GetNegativeFeedbackOptionReq.Size(m)
}
func (m *GetNegativeFeedbackOptionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNegativeFeedbackOptionReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNegativeFeedbackOptionReq proto.InternalMessageInfo

type GetNegativeFeedbackOptionResp struct {
	Feedback             *NegativeFeedback `protobuf:"bytes,1,opt,name=feedback,proto3" json:"feedback,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetNegativeFeedbackOptionResp) Reset()         { *m = GetNegativeFeedbackOptionResp{} }
func (m *GetNegativeFeedbackOptionResp) String() string { return proto.CompactTextString(m) }
func (*GetNegativeFeedbackOptionResp) ProtoMessage()    {}
func (*GetNegativeFeedbackOptionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{71}
}
func (m *GetNegativeFeedbackOptionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNegativeFeedbackOptionResp.Unmarshal(m, b)
}
func (m *GetNegativeFeedbackOptionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNegativeFeedbackOptionResp.Marshal(b, m, deterministic)
}
func (dst *GetNegativeFeedbackOptionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNegativeFeedbackOptionResp.Merge(dst, src)
}
func (m *GetNegativeFeedbackOptionResp) XXX_Size() int {
	return xxx_messageInfo_GetNegativeFeedbackOptionResp.Size(m)
}
func (m *GetNegativeFeedbackOptionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNegativeFeedbackOptionResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNegativeFeedbackOptionResp proto.InternalMessageInfo

func (m *GetNegativeFeedbackOptionResp) GetFeedback() *NegativeFeedback {
	if m != nil {
		return m.Feedback
	}
	return nil
}

type FilterRecord struct {
	ElemTitle            []string `protobuf:"bytes,1,rep,name=elem_title,json=elemTitle,proto3" json:"elem_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FilterRecord) Reset()         { *m = FilterRecord{} }
func (m *FilterRecord) String() string { return proto.CompactTextString(m) }
func (*FilterRecord) ProtoMessage()    {}
func (*FilterRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{72}
}
func (m *FilterRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterRecord.Unmarshal(m, b)
}
func (m *FilterRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterRecord.Marshal(b, m, deterministic)
}
func (dst *FilterRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterRecord.Merge(dst, src)
}
func (m *FilterRecord) XXX_Size() int {
	return xxx_messageInfo_FilterRecord.Size(m)
}
func (m *FilterRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterRecord.DiscardUnknown(m)
}

var xxx_messageInfo_FilterRecord proto.InternalMessageInfo

func (m *FilterRecord) GetElemTitle() []string {
	if m != nil {
		return m.ElemTitle
	}
	return nil
}

// 更新房间列表筛选条件，只记录一起开黑分类下玩法
type UpdateChannelListFilterRecordReq struct {
	Uid                  uint32                                          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32                                          `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	LabelRecord          []*UpdateChannelListFilterRecordReq_LabelRecord `protobuf:"bytes,3,rep,name=label_record,json=labelRecord,proto3" json:"label_record,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                        `json:"-"`
	XXX_unrecognized     []byte                                          `json:"-"`
	XXX_sizecache        int32                                           `json:"-"`
}

func (m *UpdateChannelListFilterRecordReq) Reset()         { *m = UpdateChannelListFilterRecordReq{} }
func (m *UpdateChannelListFilterRecordReq) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelListFilterRecordReq) ProtoMessage()    {}
func (*UpdateChannelListFilterRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{73}
}
func (m *UpdateChannelListFilterRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelListFilterRecordReq.Unmarshal(m, b)
}
func (m *UpdateChannelListFilterRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelListFilterRecordReq.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelListFilterRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelListFilterRecordReq.Merge(dst, src)
}
func (m *UpdateChannelListFilterRecordReq) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelListFilterRecordReq.Size(m)
}
func (m *UpdateChannelListFilterRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelListFilterRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelListFilterRecordReq proto.InternalMessageInfo

func (m *UpdateChannelListFilterRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateChannelListFilterRecordReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *UpdateChannelListFilterRecordReq) GetLabelRecord() []*UpdateChannelListFilterRecordReq_LabelRecord {
	if m != nil {
		return m.LabelRecord
	}
	return nil
}

type UpdateChannelListFilterRecordReq_LabelRecord struct {
	DisplayTitle         string   `protobuf:"bytes,1,opt,name=display_title,json=displayTitle,proto3" json:"display_title,omitempty"`
	RelatedLabelVal      string   `protobuf:"bytes,2,opt,name=related_label_val,json=relatedLabelVal,proto3" json:"related_label_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateChannelListFilterRecordReq_LabelRecord) Reset() {
	*m = UpdateChannelListFilterRecordReq_LabelRecord{}
}
func (m *UpdateChannelListFilterRecordReq_LabelRecord) String() string {
	return proto.CompactTextString(m)
}
func (*UpdateChannelListFilterRecordReq_LabelRecord) ProtoMessage() {}
func (*UpdateChannelListFilterRecordReq_LabelRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{73, 0}
}
func (m *UpdateChannelListFilterRecordReq_LabelRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelListFilterRecordReq_LabelRecord.Unmarshal(m, b)
}
func (m *UpdateChannelListFilterRecordReq_LabelRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelListFilterRecordReq_LabelRecord.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelListFilterRecordReq_LabelRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelListFilterRecordReq_LabelRecord.Merge(dst, src)
}
func (m *UpdateChannelListFilterRecordReq_LabelRecord) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelListFilterRecordReq_LabelRecord.Size(m)
}
func (m *UpdateChannelListFilterRecordReq_LabelRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelListFilterRecordReq_LabelRecord.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelListFilterRecordReq_LabelRecord proto.InternalMessageInfo

func (m *UpdateChannelListFilterRecordReq_LabelRecord) GetDisplayTitle() string {
	if m != nil {
		return m.DisplayTitle
	}
	return ""
}

func (m *UpdateChannelListFilterRecordReq_LabelRecord) GetRelatedLabelVal() string {
	if m != nil {
		return m.RelatedLabelVal
	}
	return ""
}

type UpdateChannelListFilterRecordResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateChannelListFilterRecordResp) Reset()         { *m = UpdateChannelListFilterRecordResp{} }
func (m *UpdateChannelListFilterRecordResp) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelListFilterRecordResp) ProtoMessage()    {}
func (*UpdateChannelListFilterRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{74}
}
func (m *UpdateChannelListFilterRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelListFilterRecordResp.Unmarshal(m, b)
}
func (m *UpdateChannelListFilterRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelListFilterRecordResp.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelListFilterRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelListFilterRecordResp.Merge(dst, src)
}
func (m *UpdateChannelListFilterRecordResp) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelListFilterRecordResp.Size(m)
}
func (m *UpdateChannelListFilterRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelListFilterRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelListFilterRecordResp proto.InternalMessageInfo

type GetChannelListFilterRecordReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelListFilterRecordReq) Reset()         { *m = GetChannelListFilterRecordReq{} }
func (m *GetChannelListFilterRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelListFilterRecordReq) ProtoMessage()    {}
func (*GetChannelListFilterRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{75}
}
func (m *GetChannelListFilterRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelListFilterRecordReq.Unmarshal(m, b)
}
func (m *GetChannelListFilterRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelListFilterRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelListFilterRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelListFilterRecordReq.Merge(dst, src)
}
func (m *GetChannelListFilterRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelListFilterRecordReq.Size(m)
}
func (m *GetChannelListFilterRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelListFilterRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelListFilterRecordReq proto.InternalMessageInfo

func (m *GetChannelListFilterRecordReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetChannelListFilterRecordReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetChannelListFilterRecordResp struct {
	RecordMap            map[uint32]*FilterRecord `protobuf:"bytes,1,rep,name=record_map,json=recordMap,proto3" json:"record_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetChannelListFilterRecordResp) Reset()         { *m = GetChannelListFilterRecordResp{} }
func (m *GetChannelListFilterRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelListFilterRecordResp) ProtoMessage()    {}
func (*GetChannelListFilterRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{76}
}
func (m *GetChannelListFilterRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelListFilterRecordResp.Unmarshal(m, b)
}
func (m *GetChannelListFilterRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelListFilterRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelListFilterRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelListFilterRecordResp.Merge(dst, src)
}
func (m *GetChannelListFilterRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelListFilterRecordResp.Size(m)
}
func (m *GetChannelListFilterRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelListFilterRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelListFilterRecordResp proto.InternalMessageInfo

func (m *GetChannelListFilterRecordResp) GetRecordMap() map[uint32]*FilterRecord {
	if m != nil {
		return m.RecordMap
	}
	return nil
}

// 拉取麦位音量设置接口
type GetChannelMicVolSetReq struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelMicVolSetReq) Reset()         { *m = GetChannelMicVolSetReq{} }
func (m *GetChannelMicVolSetReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMicVolSetReq) ProtoMessage()    {}
func (*GetChannelMicVolSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{77}
}
func (m *GetChannelMicVolSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMicVolSetReq.Unmarshal(m, b)
}
func (m *GetChannelMicVolSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMicVolSetReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelMicVolSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMicVolSetReq.Merge(dst, src)
}
func (m *GetChannelMicVolSetReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelMicVolSetReq.Size(m)
}
func (m *GetChannelMicVolSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMicVolSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMicVolSetReq proto.InternalMessageInfo

func (m *GetChannelMicVolSetReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

type ChannelMicVolSetItem struct {
	MicId                uint32   `protobuf:"varint,1,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	MaxVol               uint32   `protobuf:"varint,2,opt,name=max_vol,json=maxVol,proto3" json:"max_vol,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMicVolSetItem) Reset()         { *m = ChannelMicVolSetItem{} }
func (m *ChannelMicVolSetItem) String() string { return proto.CompactTextString(m) }
func (*ChannelMicVolSetItem) ProtoMessage()    {}
func (*ChannelMicVolSetItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{78}
}
func (m *ChannelMicVolSetItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMicVolSetItem.Unmarshal(m, b)
}
func (m *ChannelMicVolSetItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMicVolSetItem.Marshal(b, m, deterministic)
}
func (dst *ChannelMicVolSetItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMicVolSetItem.Merge(dst, src)
}
func (m *ChannelMicVolSetItem) XXX_Size() int {
	return xxx_messageInfo_ChannelMicVolSetItem.Size(m)
}
func (m *ChannelMicVolSetItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMicVolSetItem.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMicVolSetItem proto.InternalMessageInfo

func (m *ChannelMicVolSetItem) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *ChannelMicVolSetItem) GetMaxVol() uint32 {
	if m != nil {
		return m.MaxVol
	}
	return 0
}

type GetChannelMicVolSetResp struct {
	MicVolSet            []*ChannelMicVolSetItem `protobuf:"bytes,1,rep,name=mic_vol_set,json=micVolSet,proto3" json:"mic_vol_set,omitempty"`
	SyncZeroMicSet       bool                    `protobuf:"varint,2,opt,name=sync_zero_mic_set,json=syncZeroMicSet,proto3" json:"sync_zero_mic_set,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetChannelMicVolSetResp) Reset()         { *m = GetChannelMicVolSetResp{} }
func (m *GetChannelMicVolSetResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMicVolSetResp) ProtoMessage()    {}
func (*GetChannelMicVolSetResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{79}
}
func (m *GetChannelMicVolSetResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMicVolSetResp.Unmarshal(m, b)
}
func (m *GetChannelMicVolSetResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMicVolSetResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelMicVolSetResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMicVolSetResp.Merge(dst, src)
}
func (m *GetChannelMicVolSetResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelMicVolSetResp.Size(m)
}
func (m *GetChannelMicVolSetResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMicVolSetResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMicVolSetResp proto.InternalMessageInfo

func (m *GetChannelMicVolSetResp) GetMicVolSet() []*ChannelMicVolSetItem {
	if m != nil {
		return m.MicVolSet
	}
	return nil
}

func (m *GetChannelMicVolSetResp) GetSyncZeroMicSet() bool {
	if m != nil {
		return m.SyncZeroMicSet
	}
	return false
}

type SetChannelMicVolReq struct {
	Cid                  uint32                  `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	Uid                  uint32                  `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	MicVolSet            []*ChannelMicVolSetItem `protobuf:"bytes,3,rep,name=mic_vol_set,json=micVolSet,proto3" json:"mic_vol_set,omitempty"`
	SyncZeroMicSet       bool                    `protobuf:"varint,4,opt,name=sync_zero_mic_set,json=syncZeroMicSet,proto3" json:"sync_zero_mic_set,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SetChannelMicVolReq) Reset()         { *m = SetChannelMicVolReq{} }
func (m *SetChannelMicVolReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelMicVolReq) ProtoMessage()    {}
func (*SetChannelMicVolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{80}
}
func (m *SetChannelMicVolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMicVolReq.Unmarshal(m, b)
}
func (m *SetChannelMicVolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMicVolReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelMicVolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMicVolReq.Merge(dst, src)
}
func (m *SetChannelMicVolReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelMicVolReq.Size(m)
}
func (m *SetChannelMicVolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMicVolReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMicVolReq proto.InternalMessageInfo

func (m *SetChannelMicVolReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SetChannelMicVolReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelMicVolReq) GetMicVolSet() []*ChannelMicVolSetItem {
	if m != nil {
		return m.MicVolSet
	}
	return nil
}

func (m *SetChannelMicVolReq) GetSyncZeroMicSet() bool {
	if m != nil {
		return m.SyncZeroMicSet
	}
	return false
}

type SetChannelMicVolResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelMicVolResp) Reset()         { *m = SetChannelMicVolResp{} }
func (m *SetChannelMicVolResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelMicVolResp) ProtoMessage()    {}
func (*SetChannelMicVolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{81}
}
func (m *SetChannelMicVolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMicVolResp.Unmarshal(m, b)
}
func (m *SetChannelMicVolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMicVolResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelMicVolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMicVolResp.Merge(dst, src)
}
func (m *SetChannelMicVolResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelMicVolResp.Size(m)
}
func (m *SetChannelMicVolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMicVolResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMicVolResp proto.InternalMessageInfo

type ListPublishingChannelIdsReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	TimeOffset           int64    `protobuf:"varint,3,opt,name=time_offset,json=timeOffset,proto3" json:"time_offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListPublishingChannelIdsReq) Reset()         { *m = ListPublishingChannelIdsReq{} }
func (m *ListPublishingChannelIdsReq) String() string { return proto.CompactTextString(m) }
func (*ListPublishingChannelIdsReq) ProtoMessage()    {}
func (*ListPublishingChannelIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{82}
}
func (m *ListPublishingChannelIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListPublishingChannelIdsReq.Unmarshal(m, b)
}
func (m *ListPublishingChannelIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListPublishingChannelIdsReq.Marshal(b, m, deterministic)
}
func (dst *ListPublishingChannelIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListPublishingChannelIdsReq.Merge(dst, src)
}
func (m *ListPublishingChannelIdsReq) XXX_Size() int {
	return xxx_messageInfo_ListPublishingChannelIdsReq.Size(m)
}
func (m *ListPublishingChannelIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListPublishingChannelIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListPublishingChannelIdsReq proto.InternalMessageInfo

func (m *ListPublishingChannelIdsReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ListPublishingChannelIdsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *ListPublishingChannelIdsReq) GetTimeOffset() int64 {
	if m != nil {
		return m.TimeOffset
	}
	return 0
}

type ListPublishingChannelIdsResp struct {
	Channels             []*ListPublishingChannelIdsRespChannel `protobuf:"bytes,1,rep,name=channels,proto3" json:"channels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *ListPublishingChannelIdsResp) Reset()         { *m = ListPublishingChannelIdsResp{} }
func (m *ListPublishingChannelIdsResp) String() string { return proto.CompactTextString(m) }
func (*ListPublishingChannelIdsResp) ProtoMessage()    {}
func (*ListPublishingChannelIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{83}
}
func (m *ListPublishingChannelIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListPublishingChannelIdsResp.Unmarshal(m, b)
}
func (m *ListPublishingChannelIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListPublishingChannelIdsResp.Marshal(b, m, deterministic)
}
func (dst *ListPublishingChannelIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListPublishingChannelIdsResp.Merge(dst, src)
}
func (m *ListPublishingChannelIdsResp) XXX_Size() int {
	return xxx_messageInfo_ListPublishingChannelIdsResp.Size(m)
}
func (m *ListPublishingChannelIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListPublishingChannelIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListPublishingChannelIdsResp proto.InternalMessageInfo

func (m *ListPublishingChannelIdsResp) GetChannels() []*ListPublishingChannelIdsRespChannel {
	if m != nil {
		return m.Channels
	}
	return nil
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
type ListPublishingChannelIdsRespChannel struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Score                int64    `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListPublishingChannelIdsRespChannel) Reset()         { *m = ListPublishingChannelIdsRespChannel{} }
func (m *ListPublishingChannelIdsRespChannel) String() string { return proto.CompactTextString(m) }
func (*ListPublishingChannelIdsRespChannel) ProtoMessage()    {}
func (*ListPublishingChannelIdsRespChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_gangup_channel_65affe1df2789b40, []int{83, 0}
}
func (m *ListPublishingChannelIdsRespChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListPublishingChannelIdsRespChannel.Unmarshal(m, b)
}
func (m *ListPublishingChannelIdsRespChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListPublishingChannelIdsRespChannel.Marshal(b, m, deterministic)
}
func (dst *ListPublishingChannelIdsRespChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListPublishingChannelIdsRespChannel.Merge(dst, src)
}
func (m *ListPublishingChannelIdsRespChannel) XXX_Size() int {
	return xxx_messageInfo_ListPublishingChannelIdsRespChannel.Size(m)
}
func (m *ListPublishingChannelIdsRespChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_ListPublishingChannelIdsRespChannel.DiscardUnknown(m)
}

var xxx_messageInfo_ListPublishingChannelIdsRespChannel proto.InternalMessageInfo

func (m *ListPublishingChannelIdsRespChannel) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ListPublishingChannelIdsRespChannel) GetScore() int64 {
	if m != nil {
		return m.Score
	}
	return 0
}

func init() {
	proto.RegisterType((*BlockOptionList)(nil), "gangup_channel.BlockOptionList")
	proto.RegisterType((*BlockOption)(nil), "gangup_channel.BlockOption")
	proto.RegisterType((*GameLabel)(nil), "gangup_channel.GameLabel")
	proto.RegisterType((*GangupChannelReleaseInfo)(nil), "gangup_channel.GangupChannelReleaseInfo")
	proto.RegisterType((*SetGangupChannelReleaseInfoReq)(nil), "gangup_channel.SetGangupChannelReleaseInfoReq")
	proto.RegisterType((*SetGangupChannelReleaseInfoResp)(nil), "gangup_channel.SetGangupChannelReleaseInfoResp")
	proto.RegisterType((*DismissGangupChannelReq)(nil), "gangup_channel.DismissGangupChannelReq")
	proto.RegisterType((*DismissGangupChannelResp)(nil), "gangup_channel.DismissGangupChannelResp")
	proto.RegisterType((*GetRecommendChannelListLoadMore)(nil), "gangup_channel.GetRecommendChannelListLoadMore")
	proto.RegisterType((*GetGangupChannelListReq)(nil), "gangup_channel.GetGangupChannelListReq")
	proto.RegisterType((*GetGangupChannelListResp)(nil), "gangup_channel.GetGangupChannelListResp")
	proto.RegisterType((*GetGangupChannelByIdsReq)(nil), "gangup_channel.GetGangupChannelByIdsReq")
	proto.RegisterType((*GetGangupChannelByIdsResp)(nil), "gangup_channel.GetGangupChannelByIdsResp")
	proto.RegisterType((*SwitchChannelTabReq)(nil), "gangup_channel.SwitchChannelTabReq")
	proto.RegisterType((*SwitchChannelTabResp)(nil), "gangup_channel.SwitchChannelTabResp")
	proto.RegisterType((*DisappearChannelReq)(nil), "gangup_channel.DisappearChannelReq")
	proto.RegisterType((*DisappearChannelReq_Timeout)(nil), "gangup_channel.DisappearChannelReq.Timeout")
	proto.RegisterType((*DisappearChannelReq_Keepalive)(nil), "gangup_channel.DisappearChannelReq.Keepalive")
	proto.RegisterType((*DisappearChannelReq_ReleaseTimeout)(nil), "gangup_channel.DisappearChannelReq.ReleaseTimeout")
	proto.RegisterType((*DisappearChannelResp)(nil), "gangup_channel.DisappearChannelResp")
	proto.RegisterType((*GetOnlineInfoReq)(nil), "gangup_channel.GetOnlineInfoReq")
	proto.RegisterType((*GetOnlineInfoResp)(nil), "gangup_channel.GetOnlineInfoResp")
	proto.RegisterType((*FreezeChannelReq)(nil), "gangup_channel.FreezeChannelReq")
	proto.RegisterType((*FreezeChannelResp)(nil), "gangup_channel.FreezeChannelResp")
	proto.RegisterType((*UnfreezeChannelReq)(nil), "gangup_channel.UnfreezeChannelReq")
	proto.RegisterType((*UnfreezeChannelResp)(nil), "gangup_channel.UnfreezeChannelResp")
	proto.RegisterType((*GetChannelFreezeInfoReq)(nil), "gangup_channel.GetChannelFreezeInfoReq")
	proto.RegisterType((*GetChannelFreezeInfoResp)(nil), "gangup_channel.GetChannelFreezeInfoResp")
	proto.RegisterType((*SetExtraHistoryReq)(nil), "gangup_channel.SetExtraHistoryReq")
	proto.RegisterType((*SetExtraHistoryResp)(nil), "gangup_channel.SetExtraHistoryResp")
	proto.RegisterType((*GetExtraHistoryReq)(nil), "gangup_channel.GetExtraHistoryReq")
	proto.RegisterType((*GetExtraHistoryResp)(nil), "gangup_channel.GetExtraHistoryResp")
	proto.RegisterType((*GetChannelPlayModelReq)(nil), "gangup_channel.GetChannelPlayModelReq")
	proto.RegisterType((*GetChannelPlayModelResp)(nil), "gangup_channel.GetChannelPlayModelResp")
	proto.RegisterType((*GetChannelRoomUserNumberReq)(nil), "gangup_channel.GetChannelRoomUserNumberReq")
	proto.RegisterType((*GetChannelRoomUserNumberResp)(nil), "gangup_channel.GetChannelRoomUserNumberResp")
	proto.RegisterType((*GetChannelRoomUserNumberResp_RoomUserInfo)(nil), "gangup_channel.GetChannelRoomUserNumberResp.RoomUserInfo")
	proto.RegisterType((*AddTemporaryChannelReq)(nil), "gangup_channel.AddTemporaryChannelReq")
	proto.RegisterType((*AddTemporaryChannelResp)(nil), "gangup_channel.AddTemporaryChannelResp")
	proto.RegisterType((*NegativeFeedBackReq)(nil), "gangup_channel.NegativeFeedBackReq")
	proto.RegisterType((*NegativeFeedBackResp)(nil), "gangup_channel.NegativeFeedBackResp")
	proto.RegisterType((*CleanChannelInfoReq)(nil), "gangup_channel.CleanChannelInfoReq")
	proto.RegisterType((*CleanChannelInfoResp)(nil), "gangup_channel.CleanChannelInfoResp")
	proto.RegisterType((*GetChannelIdsByTabIdReq)(nil), "gangup_channel.GetChannelIdsByTabIdReq")
	proto.RegisterType((*GetChannelIdsByTabIdResp)(nil), "gangup_channel.GetChannelIdsByTabIdResp")
	proto.RegisterType((*GetGameHomePageDIYFilterReq)(nil), "gangup_channel.GetGameHomePageDIYFilterReq")
	proto.RegisterType((*GetGameHomePageDIYFilterResp)(nil), "gangup_channel.GetGameHomePageDIYFilterResp")
	proto.RegisterType((*GameHomePageFilterItem)(nil), "gangup_channel.GameHomePageFilterItem")
	proto.RegisterType((*SetGameHomePageDIYFilterReq)(nil), "gangup_channel.SetGameHomePageDIYFilterReq")
	proto.RegisterType((*SetGameHomePageDIYFilterResp)(nil), "gangup_channel.SetGameHomePageDIYFilterResp")
	proto.RegisterType((*SetGangupChannelDenoiseModeReq)(nil), "gangup_channel.SetGangupChannelDenoiseModeReq")
	proto.RegisterType((*SetGangupChannelDenoiseModeResp)(nil), "gangup_channel.SetGangupChannelDenoiseModeResp")
	proto.RegisterType((*GangupChannelExtraInfo)(nil), "gangup_channel.GangupChannelExtraInfo")
	proto.RegisterType((*GetGangupChannelExtraInfoReq)(nil), "gangup_channel.GetGangupChannelExtraInfoReq")
	proto.RegisterType((*GetGangupChannelExtraInfoResp)(nil), "gangup_channel.GetGangupChannelExtraInfoResp")
	proto.RegisterType((*GetGangupExtraHistoryReq)(nil), "gangup_channel.GetGangupExtraHistoryReq")
	proto.RegisterType((*GetGangupExtraHistoryResp)(nil), "gangup_channel.GetGangupExtraHistoryResp")
	proto.RegisterType((*SetGangupExtraHistoryReq)(nil), "gangup_channel.SetGangupExtraHistoryReq")
	proto.RegisterType((*SetGangupExtraHistoryResp)(nil), "gangup_channel.SetGangupExtraHistoryResp")
	proto.RegisterType((*GetGangupPublishCountHistoryReq)(nil), "gangup_channel.GetGangupPublishCountHistoryReq")
	proto.RegisterType((*GetGangupPublishCountHistoryResp)(nil), "gangup_channel.GetGangupPublishCountHistoryResp")
	proto.RegisterType((*SetGangupPublishCountHistoryReq)(nil), "gangup_channel.SetGangupPublishCountHistoryReq")
	proto.RegisterType((*SetGangupPublishCountHistoryResp)(nil), "gangup_channel.SetGangupPublishCountHistoryResp")
	proto.RegisterType((*SetDIYFilterByEntranceReq)(nil), "gangup_channel.SetDIYFilterByEntranceReq")
	proto.RegisterType((*DIYFilterItem)(nil), "gangup_channel.DIYFilterItem")
	proto.RegisterType((*SetDIYFilterByEntranceResp)(nil), "gangup_channel.SetDIYFilterByEntranceResp")
	proto.RegisterType((*GetDIYFilterByEntranceReq)(nil), "gangup_channel.GetDIYFilterByEntranceReq")
	proto.RegisterType((*GetDIYFilterByEntranceResp)(nil), "gangup_channel.GetDIYFilterByEntranceResp")
	proto.RegisterType((*GetReleasingChannelCountByTabIdsReq)(nil), "gangup_channel.GetReleasingChannelCountByTabIdsReq")
	proto.RegisterType((*GetReleasingChannelCountByTabIdsResp)(nil), "gangup_channel.GetReleasingChannelCountByTabIdsResp")
	proto.RegisterMapType((map[uint32]int64)(nil), "gangup_channel.GetReleasingChannelCountByTabIdsResp.CountMapEntry")
	proto.RegisterType((*ChannelFeedback)(nil), "gangup_channel.ChannelFeedback")
	proto.RegisterType((*NegativeFeedback)(nil), "gangup_channel.NegativeFeedback")
	proto.RegisterType((*UpdateNegativeFeedbackOptionReq)(nil), "gangup_channel.UpdateNegativeFeedbackOptionReq")
	proto.RegisterType((*UpdateNegativeFeedbackOptionResp)(nil), "gangup_channel.UpdateNegativeFeedbackOptionResp")
	proto.RegisterType((*GetNegativeFeedbackOptionReq)(nil), "gangup_channel.GetNegativeFeedbackOptionReq")
	proto.RegisterType((*GetNegativeFeedbackOptionResp)(nil), "gangup_channel.GetNegativeFeedbackOptionResp")
	proto.RegisterType((*FilterRecord)(nil), "gangup_channel.FilterRecord")
	proto.RegisterType((*UpdateChannelListFilterRecordReq)(nil), "gangup_channel.UpdateChannelListFilterRecordReq")
	proto.RegisterType((*UpdateChannelListFilterRecordReq_LabelRecord)(nil), "gangup_channel.UpdateChannelListFilterRecordReq.LabelRecord")
	proto.RegisterType((*UpdateChannelListFilterRecordResp)(nil), "gangup_channel.UpdateChannelListFilterRecordResp")
	proto.RegisterType((*GetChannelListFilterRecordReq)(nil), "gangup_channel.GetChannelListFilterRecordReq")
	proto.RegisterType((*GetChannelListFilterRecordResp)(nil), "gangup_channel.GetChannelListFilterRecordResp")
	proto.RegisterMapType((map[uint32]*FilterRecord)(nil), "gangup_channel.GetChannelListFilterRecordResp.RecordMapEntry")
	proto.RegisterType((*GetChannelMicVolSetReq)(nil), "gangup_channel.GetChannelMicVolSetReq")
	proto.RegisterType((*ChannelMicVolSetItem)(nil), "gangup_channel.ChannelMicVolSetItem")
	proto.RegisterType((*GetChannelMicVolSetResp)(nil), "gangup_channel.GetChannelMicVolSetResp")
	proto.RegisterType((*SetChannelMicVolReq)(nil), "gangup_channel.SetChannelMicVolReq")
	proto.RegisterType((*SetChannelMicVolResp)(nil), "gangup_channel.SetChannelMicVolResp")
	proto.RegisterType((*ListPublishingChannelIdsReq)(nil), "gangup_channel.ListPublishingChannelIdsReq")
	proto.RegisterType((*ListPublishingChannelIdsResp)(nil), "gangup_channel.ListPublishingChannelIdsResp")
	proto.RegisterType((*ListPublishingChannelIdsRespChannel)(nil), "gangup_channel.ListPublishingChannelIdsResp.channel")
	proto.RegisterEnum("gangup_channel.ChannelDisplayType", ChannelDisplayType_name, ChannelDisplayType_value)
	proto.RegisterEnum("gangup_channel.DismissType", DismissType_name, DismissType_value)
	proto.RegisterEnum("gangup_channel.HistoryType", HistoryType_name, HistoryType_value)
	proto.RegisterEnum("gangup_channel.NegativeFeedbackType", NegativeFeedbackType_name, NegativeFeedbackType_value)
	proto.RegisterEnum("gangup_channel.REGULATORY_LEVEL", REGULATORY_LEVEL_name, REGULATORY_LEVEL_value)
	proto.RegisterEnum("gangup_channel.EntryType", EntryType_name, EntryType_value)
	proto.RegisterEnum("gangup_channel.GetChannelIdsByTabIdReq_GetMode", GetChannelIdsByTabIdReq_GetMode_name, GetChannelIdsByTabIdReq_GetMode_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GangupChannelClient is the client API for GangupChannel service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GangupChannelClient interface {
	// 修改主题房字段
	SetGangupChannelReleaseInfo(ctx context.Context, in *SetGangupChannelReleaseInfoReq, opts ...grpc.CallOption) (*SetGangupChannelReleaseInfoResp, error)
	// 解散主题房
	DismissGangupChannel(ctx context.Context, in *DismissGangupChannelReq, opts ...grpc.CallOption) (*DismissGangupChannelResp, error)
	// 获取房间列表（兜底推荐）
	GetGangupChannelList(ctx context.Context, in *GetGangupChannelListReq, opts ...grpc.CallOption) (*GetGangupChannelListResp, error)
	// 获取开黑房信息
	GetGangupChannelByIds(ctx context.Context, in *GetGangupChannelByIdsReq, opts ...grpc.CallOption) (*GetGangupChannelByIdsResp, error)
	// 切换房间玩法
	SwitchChannelTab(ctx context.Context, in *SwitchChannelTabReq, opts ...grpc.CallOption) (*SwitchChannelTabResp, error)
	// 获取主题房数，以及示例用户
	GetOnlineInfo(ctx context.Context, in *GetOnlineInfoReq, opts ...grpc.CallOption) (*GetOnlineInfoResp, error)
	// 获取指定房间类型人数
	GetChannelRoomUserNumber(ctx context.Context, in *GetChannelRoomUserNumberReq, opts ...grpc.CallOption) (*GetChannelRoomUserNumberResp, error)
	// 创建临时房
	AddTemporaryChannel(ctx context.Context, in *AddTemporaryChannelReq, opts ...grpc.CallOption) (*AddTemporaryChannelResp, error)
	// 负反馈上报
	NegativeFeedBack(ctx context.Context, in *NegativeFeedBackReq, opts ...grpc.CallOption) (*NegativeFeedBackResp, error)
	// 清除开黑房间信息
	CleanChannelInfo(ctx context.Context, in *CleanChannelInfoReq, opts ...grpc.CallOption) (*CleanChannelInfoResp, error)
	// 获取指定玩法类型房间id
	GetChannelIdsByTabId(ctx context.Context, in *GetChannelIdsByTabIdReq, opts ...grpc.CallOption) (*GetChannelIdsByTabIdResp, error)
	// 开黑列表自定义筛选器
	GetGameHomePageDIYFilter(ctx context.Context, in *GetGameHomePageDIYFilterReq, opts ...grpc.CallOption) (*GetGameHomePageDIYFilterResp, error)
	SetGameHomePageDIYFilter(ctx context.Context, in *SetGameHomePageDIYFilterReq, opts ...grpc.CallOption) (*SetGameHomePageDIYFilterResp, error)
	// 设置开黑房降噪模式
	SetGangupChannelDenoiseMode(ctx context.Context, in *SetGangupChannelDenoiseModeReq, opts ...grpc.CallOption) (*SetGangupChannelDenoiseModeResp, error)
	// 获取开黑房扩展信息
	GetGangupChannelExtraInfo(ctx context.Context, in *GetGangupChannelExtraInfoReq, opts ...grpc.CallOption) (*GetGangupChannelExtraInfoResp, error)
	// 获取开黑房间冷却时间信息（发布冷却时间，修改冷却时间）
	GetGangupExtraHistory(ctx context.Context, in *GetGangupExtraHistoryReq, opts ...grpc.CallOption) (*GetGangupExtraHistoryResp, error)
	SetGangupExtraHistory(ctx context.Context, in *SetGangupExtraHistoryReq, opts ...grpc.CallOption) (*SetGangupExtraHistoryResp, error)
	// 获取开黑房间发布次数
	GetGangupPublishCountHistory(ctx context.Context, in *GetGangupPublishCountHistoryReq, opts ...grpc.CallOption) (*GetGangupPublishCountHistoryResp, error)
	SetGangupPublishCountHistory(ctx context.Context, in *SetGangupPublishCountHistoryReq, opts ...grpc.CallOption) (*SetGangupPublishCountHistoryResp, error)
	// 根据入口获取常玩分类
	GetDIYFilterByEntrance(ctx context.Context, in *GetDIYFilterByEntranceReq, opts ...grpc.CallOption) (*GetDIYFilterByEntranceResp, error)
	// 根据入口设置常玩分类
	SetDIYFilterByEntrance(ctx context.Context, in *SetDIYFilterByEntranceReq, opts ...grpc.CallOption) (*SetDIYFilterByEntranceResp, error)
	// 根据玩法获取发布房间数
	GetReleasingChannelCountByTabIds(ctx context.Context, in *GetReleasingChannelCountByTabIdsReq, opts ...grpc.CallOption) (*GetReleasingChannelCountByTabIdsResp, error)
	// 真实发布中房间数加随机数
	GetReleasingChannelRandomCountByTabIds(ctx context.Context, in *GetReleasingChannelCountByTabIdsReq, opts ...grpc.CallOption) (*GetReleasingChannelCountByTabIdsResp, error)
	// 更新负反馈选项
	UpdateNegativeFeedbackOption(ctx context.Context, in *UpdateNegativeFeedbackOptionReq, opts ...grpc.CallOption) (*UpdateNegativeFeedbackOptionResp, error)
	// 获取负反馈选项
	GetNegativeFeedbackOption(ctx context.Context, in *GetNegativeFeedbackOptionReq, opts ...grpc.CallOption) (*GetNegativeFeedbackOptionResp, error)
	// 更新房间列表筛选条件，只记录一起开黑分类下玩法
	UpdateChannelListFilterRecord(ctx context.Context, in *UpdateChannelListFilterRecordReq, opts ...grpc.CallOption) (*UpdateChannelListFilterRecordResp, error)
	// 获取房间列表筛选记录
	GetChannelListFilterRecord(ctx context.Context, in *GetChannelListFilterRecordReq, opts ...grpc.CallOption) (*GetChannelListFilterRecordResp, error)
	// 获取房间麦位音量
	GetChannelMicVolSet(ctx context.Context, in *GetChannelMicVolSetReq, opts ...grpc.CallOption) (*GetChannelMicVolSetResp, error)
	// 设置房间麦位音量
	SetChannelMicVol(ctx context.Context, in *SetChannelMicVolReq, opts ...grpc.CallOption) (*SetChannelMicVolResp, error)
	// 获取发布中的房间
	ListPublishingChannelIds(ctx context.Context, in *ListPublishingChannelIdsReq, opts ...grpc.CallOption) (*ListPublishingChannelIdsResp, error)
}

type gangupChannelClient struct {
	cc *grpc.ClientConn
}

func NewGangupChannelClient(cc *grpc.ClientConn) GangupChannelClient {
	return &gangupChannelClient{cc}
}

func (c *gangupChannelClient) SetGangupChannelReleaseInfo(ctx context.Context, in *SetGangupChannelReleaseInfoReq, opts ...grpc.CallOption) (*SetGangupChannelReleaseInfoResp, error) {
	out := new(SetGangupChannelReleaseInfoResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/SetGangupChannelReleaseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) DismissGangupChannel(ctx context.Context, in *DismissGangupChannelReq, opts ...grpc.CallOption) (*DismissGangupChannelResp, error) {
	out := new(DismissGangupChannelResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/DismissGangupChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) GetGangupChannelList(ctx context.Context, in *GetGangupChannelListReq, opts ...grpc.CallOption) (*GetGangupChannelListResp, error) {
	out := new(GetGangupChannelListResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/GetGangupChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) GetGangupChannelByIds(ctx context.Context, in *GetGangupChannelByIdsReq, opts ...grpc.CallOption) (*GetGangupChannelByIdsResp, error) {
	out := new(GetGangupChannelByIdsResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/GetGangupChannelByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) SwitchChannelTab(ctx context.Context, in *SwitchChannelTabReq, opts ...grpc.CallOption) (*SwitchChannelTabResp, error) {
	out := new(SwitchChannelTabResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/SwitchChannelTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) GetOnlineInfo(ctx context.Context, in *GetOnlineInfoReq, opts ...grpc.CallOption) (*GetOnlineInfoResp, error) {
	out := new(GetOnlineInfoResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/GetOnlineInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) GetChannelRoomUserNumber(ctx context.Context, in *GetChannelRoomUserNumberReq, opts ...grpc.CallOption) (*GetChannelRoomUserNumberResp, error) {
	out := new(GetChannelRoomUserNumberResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/GetChannelRoomUserNumber", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) AddTemporaryChannel(ctx context.Context, in *AddTemporaryChannelReq, opts ...grpc.CallOption) (*AddTemporaryChannelResp, error) {
	out := new(AddTemporaryChannelResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/AddTemporaryChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) NegativeFeedBack(ctx context.Context, in *NegativeFeedBackReq, opts ...grpc.CallOption) (*NegativeFeedBackResp, error) {
	out := new(NegativeFeedBackResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/NegativeFeedBack", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) CleanChannelInfo(ctx context.Context, in *CleanChannelInfoReq, opts ...grpc.CallOption) (*CleanChannelInfoResp, error) {
	out := new(CleanChannelInfoResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/CleanChannelInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) GetChannelIdsByTabId(ctx context.Context, in *GetChannelIdsByTabIdReq, opts ...grpc.CallOption) (*GetChannelIdsByTabIdResp, error) {
	out := new(GetChannelIdsByTabIdResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/GetChannelIdsByTabId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) GetGameHomePageDIYFilter(ctx context.Context, in *GetGameHomePageDIYFilterReq, opts ...grpc.CallOption) (*GetGameHomePageDIYFilterResp, error) {
	out := new(GetGameHomePageDIYFilterResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/GetGameHomePageDIYFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) SetGameHomePageDIYFilter(ctx context.Context, in *SetGameHomePageDIYFilterReq, opts ...grpc.CallOption) (*SetGameHomePageDIYFilterResp, error) {
	out := new(SetGameHomePageDIYFilterResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/SetGameHomePageDIYFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) SetGangupChannelDenoiseMode(ctx context.Context, in *SetGangupChannelDenoiseModeReq, opts ...grpc.CallOption) (*SetGangupChannelDenoiseModeResp, error) {
	out := new(SetGangupChannelDenoiseModeResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/SetGangupChannelDenoiseMode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) GetGangupChannelExtraInfo(ctx context.Context, in *GetGangupChannelExtraInfoReq, opts ...grpc.CallOption) (*GetGangupChannelExtraInfoResp, error) {
	out := new(GetGangupChannelExtraInfoResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/GetGangupChannelExtraInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) GetGangupExtraHistory(ctx context.Context, in *GetGangupExtraHistoryReq, opts ...grpc.CallOption) (*GetGangupExtraHistoryResp, error) {
	out := new(GetGangupExtraHistoryResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/GetGangupExtraHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) SetGangupExtraHistory(ctx context.Context, in *SetGangupExtraHistoryReq, opts ...grpc.CallOption) (*SetGangupExtraHistoryResp, error) {
	out := new(SetGangupExtraHistoryResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/SetGangupExtraHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) GetGangupPublishCountHistory(ctx context.Context, in *GetGangupPublishCountHistoryReq, opts ...grpc.CallOption) (*GetGangupPublishCountHistoryResp, error) {
	out := new(GetGangupPublishCountHistoryResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/GetGangupPublishCountHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) SetGangupPublishCountHistory(ctx context.Context, in *SetGangupPublishCountHistoryReq, opts ...grpc.CallOption) (*SetGangupPublishCountHistoryResp, error) {
	out := new(SetGangupPublishCountHistoryResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/SetGangupPublishCountHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) GetDIYFilterByEntrance(ctx context.Context, in *GetDIYFilterByEntranceReq, opts ...grpc.CallOption) (*GetDIYFilterByEntranceResp, error) {
	out := new(GetDIYFilterByEntranceResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/GetDIYFilterByEntrance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) SetDIYFilterByEntrance(ctx context.Context, in *SetDIYFilterByEntranceReq, opts ...grpc.CallOption) (*SetDIYFilterByEntranceResp, error) {
	out := new(SetDIYFilterByEntranceResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/SetDIYFilterByEntrance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) GetReleasingChannelCountByTabIds(ctx context.Context, in *GetReleasingChannelCountByTabIdsReq, opts ...grpc.CallOption) (*GetReleasingChannelCountByTabIdsResp, error) {
	out := new(GetReleasingChannelCountByTabIdsResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/GetReleasingChannelCountByTabIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) GetReleasingChannelRandomCountByTabIds(ctx context.Context, in *GetReleasingChannelCountByTabIdsReq, opts ...grpc.CallOption) (*GetReleasingChannelCountByTabIdsResp, error) {
	out := new(GetReleasingChannelCountByTabIdsResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/GetReleasingChannelRandomCountByTabIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) UpdateNegativeFeedbackOption(ctx context.Context, in *UpdateNegativeFeedbackOptionReq, opts ...grpc.CallOption) (*UpdateNegativeFeedbackOptionResp, error) {
	out := new(UpdateNegativeFeedbackOptionResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/UpdateNegativeFeedbackOption", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) GetNegativeFeedbackOption(ctx context.Context, in *GetNegativeFeedbackOptionReq, opts ...grpc.CallOption) (*GetNegativeFeedbackOptionResp, error) {
	out := new(GetNegativeFeedbackOptionResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/GetNegativeFeedbackOption", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) UpdateChannelListFilterRecord(ctx context.Context, in *UpdateChannelListFilterRecordReq, opts ...grpc.CallOption) (*UpdateChannelListFilterRecordResp, error) {
	out := new(UpdateChannelListFilterRecordResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/UpdateChannelListFilterRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) GetChannelListFilterRecord(ctx context.Context, in *GetChannelListFilterRecordReq, opts ...grpc.CallOption) (*GetChannelListFilterRecordResp, error) {
	out := new(GetChannelListFilterRecordResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/GetChannelListFilterRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) GetChannelMicVolSet(ctx context.Context, in *GetChannelMicVolSetReq, opts ...grpc.CallOption) (*GetChannelMicVolSetResp, error) {
	out := new(GetChannelMicVolSetResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/GetChannelMicVolSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) SetChannelMicVol(ctx context.Context, in *SetChannelMicVolReq, opts ...grpc.CallOption) (*SetChannelMicVolResp, error) {
	out := new(SetChannelMicVolResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/SetChannelMicVol", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gangupChannelClient) ListPublishingChannelIds(ctx context.Context, in *ListPublishingChannelIdsReq, opts ...grpc.CallOption) (*ListPublishingChannelIdsResp, error) {
	out := new(ListPublishingChannelIdsResp)
	err := c.cc.Invoke(ctx, "/gangup_channel.GangupChannel/ListPublishingChannelIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GangupChannelServer is the server API for GangupChannel service.
type GangupChannelServer interface {
	// 修改主题房字段
	SetGangupChannelReleaseInfo(context.Context, *SetGangupChannelReleaseInfoReq) (*SetGangupChannelReleaseInfoResp, error)
	// 解散主题房
	DismissGangupChannel(context.Context, *DismissGangupChannelReq) (*DismissGangupChannelResp, error)
	// 获取房间列表（兜底推荐）
	GetGangupChannelList(context.Context, *GetGangupChannelListReq) (*GetGangupChannelListResp, error)
	// 获取开黑房信息
	GetGangupChannelByIds(context.Context, *GetGangupChannelByIdsReq) (*GetGangupChannelByIdsResp, error)
	// 切换房间玩法
	SwitchChannelTab(context.Context, *SwitchChannelTabReq) (*SwitchChannelTabResp, error)
	// 获取主题房数，以及示例用户
	GetOnlineInfo(context.Context, *GetOnlineInfoReq) (*GetOnlineInfoResp, error)
	// 获取指定房间类型人数
	GetChannelRoomUserNumber(context.Context, *GetChannelRoomUserNumberReq) (*GetChannelRoomUserNumberResp, error)
	// 创建临时房
	AddTemporaryChannel(context.Context, *AddTemporaryChannelReq) (*AddTemporaryChannelResp, error)
	// 负反馈上报
	NegativeFeedBack(context.Context, *NegativeFeedBackReq) (*NegativeFeedBackResp, error)
	// 清除开黑房间信息
	CleanChannelInfo(context.Context, *CleanChannelInfoReq) (*CleanChannelInfoResp, error)
	// 获取指定玩法类型房间id
	GetChannelIdsByTabId(context.Context, *GetChannelIdsByTabIdReq) (*GetChannelIdsByTabIdResp, error)
	// 开黑列表自定义筛选器
	GetGameHomePageDIYFilter(context.Context, *GetGameHomePageDIYFilterReq) (*GetGameHomePageDIYFilterResp, error)
	SetGameHomePageDIYFilter(context.Context, *SetGameHomePageDIYFilterReq) (*SetGameHomePageDIYFilterResp, error)
	// 设置开黑房降噪模式
	SetGangupChannelDenoiseMode(context.Context, *SetGangupChannelDenoiseModeReq) (*SetGangupChannelDenoiseModeResp, error)
	// 获取开黑房扩展信息
	GetGangupChannelExtraInfo(context.Context, *GetGangupChannelExtraInfoReq) (*GetGangupChannelExtraInfoResp, error)
	// 获取开黑房间冷却时间信息（发布冷却时间，修改冷却时间）
	GetGangupExtraHistory(context.Context, *GetGangupExtraHistoryReq) (*GetGangupExtraHistoryResp, error)
	SetGangupExtraHistory(context.Context, *SetGangupExtraHistoryReq) (*SetGangupExtraHistoryResp, error)
	// 获取开黑房间发布次数
	GetGangupPublishCountHistory(context.Context, *GetGangupPublishCountHistoryReq) (*GetGangupPublishCountHistoryResp, error)
	SetGangupPublishCountHistory(context.Context, *SetGangupPublishCountHistoryReq) (*SetGangupPublishCountHistoryResp, error)
	// 根据入口获取常玩分类
	GetDIYFilterByEntrance(context.Context, *GetDIYFilterByEntranceReq) (*GetDIYFilterByEntranceResp, error)
	// 根据入口设置常玩分类
	SetDIYFilterByEntrance(context.Context, *SetDIYFilterByEntranceReq) (*SetDIYFilterByEntranceResp, error)
	// 根据玩法获取发布房间数
	GetReleasingChannelCountByTabIds(context.Context, *GetReleasingChannelCountByTabIdsReq) (*GetReleasingChannelCountByTabIdsResp, error)
	// 真实发布中房间数加随机数
	GetReleasingChannelRandomCountByTabIds(context.Context, *GetReleasingChannelCountByTabIdsReq) (*GetReleasingChannelCountByTabIdsResp, error)
	// 更新负反馈选项
	UpdateNegativeFeedbackOption(context.Context, *UpdateNegativeFeedbackOptionReq) (*UpdateNegativeFeedbackOptionResp, error)
	// 获取负反馈选项
	GetNegativeFeedbackOption(context.Context, *GetNegativeFeedbackOptionReq) (*GetNegativeFeedbackOptionResp, error)
	// 更新房间列表筛选条件，只记录一起开黑分类下玩法
	UpdateChannelListFilterRecord(context.Context, *UpdateChannelListFilterRecordReq) (*UpdateChannelListFilterRecordResp, error)
	// 获取房间列表筛选记录
	GetChannelListFilterRecord(context.Context, *GetChannelListFilterRecordReq) (*GetChannelListFilterRecordResp, error)
	// 获取房间麦位音量
	GetChannelMicVolSet(context.Context, *GetChannelMicVolSetReq) (*GetChannelMicVolSetResp, error)
	// 设置房间麦位音量
	SetChannelMicVol(context.Context, *SetChannelMicVolReq) (*SetChannelMicVolResp, error)
	// 获取发布中的房间
	ListPublishingChannelIds(context.Context, *ListPublishingChannelIdsReq) (*ListPublishingChannelIdsResp, error)
}

func RegisterGangupChannelServer(s *grpc.Server, srv GangupChannelServer) {
	s.RegisterService(&_GangupChannel_serviceDesc, srv)
}

func _GangupChannel_SetGangupChannelReleaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGangupChannelReleaseInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).SetGangupChannelReleaseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/SetGangupChannelReleaseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).SetGangupChannelReleaseInfo(ctx, req.(*SetGangupChannelReleaseInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_DismissGangupChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DismissGangupChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).DismissGangupChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/DismissGangupChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).DismissGangupChannel(ctx, req.(*DismissGangupChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_GetGangupChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGangupChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).GetGangupChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/GetGangupChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).GetGangupChannelList(ctx, req.(*GetGangupChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_GetGangupChannelByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGangupChannelByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).GetGangupChannelByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/GetGangupChannelByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).GetGangupChannelByIds(ctx, req.(*GetGangupChannelByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_SwitchChannelTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchChannelTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).SwitchChannelTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/SwitchChannelTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).SwitchChannelTab(ctx, req.(*SwitchChannelTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_GetOnlineInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnlineInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).GetOnlineInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/GetOnlineInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).GetOnlineInfo(ctx, req.(*GetOnlineInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_GetChannelRoomUserNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelRoomUserNumberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).GetChannelRoomUserNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/GetChannelRoomUserNumber",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).GetChannelRoomUserNumber(ctx, req.(*GetChannelRoomUserNumberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_AddTemporaryChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTemporaryChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).AddTemporaryChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/AddTemporaryChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).AddTemporaryChannel(ctx, req.(*AddTemporaryChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_NegativeFeedBack_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NegativeFeedBackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).NegativeFeedBack(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/NegativeFeedBack",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).NegativeFeedBack(ctx, req.(*NegativeFeedBackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_CleanChannelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanChannelInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).CleanChannelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/CleanChannelInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).CleanChannelInfo(ctx, req.(*CleanChannelInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_GetChannelIdsByTabId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelIdsByTabIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).GetChannelIdsByTabId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/GetChannelIdsByTabId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).GetChannelIdsByTabId(ctx, req.(*GetChannelIdsByTabIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_GetGameHomePageDIYFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameHomePageDIYFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).GetGameHomePageDIYFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/GetGameHomePageDIYFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).GetGameHomePageDIYFilter(ctx, req.(*GetGameHomePageDIYFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_SetGameHomePageDIYFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGameHomePageDIYFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).SetGameHomePageDIYFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/SetGameHomePageDIYFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).SetGameHomePageDIYFilter(ctx, req.(*SetGameHomePageDIYFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_SetGangupChannelDenoiseMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGangupChannelDenoiseModeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).SetGangupChannelDenoiseMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/SetGangupChannelDenoiseMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).SetGangupChannelDenoiseMode(ctx, req.(*SetGangupChannelDenoiseModeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_GetGangupChannelExtraInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGangupChannelExtraInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).GetGangupChannelExtraInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/GetGangupChannelExtraInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).GetGangupChannelExtraInfo(ctx, req.(*GetGangupChannelExtraInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_GetGangupExtraHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGangupExtraHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).GetGangupExtraHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/GetGangupExtraHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).GetGangupExtraHistory(ctx, req.(*GetGangupExtraHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_SetGangupExtraHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGangupExtraHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).SetGangupExtraHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/SetGangupExtraHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).SetGangupExtraHistory(ctx, req.(*SetGangupExtraHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_GetGangupPublishCountHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGangupPublishCountHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).GetGangupPublishCountHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/GetGangupPublishCountHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).GetGangupPublishCountHistory(ctx, req.(*GetGangupPublishCountHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_SetGangupPublishCountHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGangupPublishCountHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).SetGangupPublishCountHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/SetGangupPublishCountHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).SetGangupPublishCountHistory(ctx, req.(*SetGangupPublishCountHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_GetDIYFilterByEntrance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDIYFilterByEntranceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).GetDIYFilterByEntrance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/GetDIYFilterByEntrance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).GetDIYFilterByEntrance(ctx, req.(*GetDIYFilterByEntranceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_SetDIYFilterByEntrance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDIYFilterByEntranceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).SetDIYFilterByEntrance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/SetDIYFilterByEntrance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).SetDIYFilterByEntrance(ctx, req.(*SetDIYFilterByEntranceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_GetReleasingChannelCountByTabIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReleasingChannelCountByTabIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).GetReleasingChannelCountByTabIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/GetReleasingChannelCountByTabIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).GetReleasingChannelCountByTabIds(ctx, req.(*GetReleasingChannelCountByTabIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_GetReleasingChannelRandomCountByTabIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReleasingChannelCountByTabIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).GetReleasingChannelRandomCountByTabIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/GetReleasingChannelRandomCountByTabIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).GetReleasingChannelRandomCountByTabIds(ctx, req.(*GetReleasingChannelCountByTabIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_UpdateNegativeFeedbackOption_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNegativeFeedbackOptionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).UpdateNegativeFeedbackOption(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/UpdateNegativeFeedbackOption",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).UpdateNegativeFeedbackOption(ctx, req.(*UpdateNegativeFeedbackOptionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_GetNegativeFeedbackOption_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNegativeFeedbackOptionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).GetNegativeFeedbackOption(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/GetNegativeFeedbackOption",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).GetNegativeFeedbackOption(ctx, req.(*GetNegativeFeedbackOptionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_UpdateChannelListFilterRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChannelListFilterRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).UpdateChannelListFilterRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/UpdateChannelListFilterRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).UpdateChannelListFilterRecord(ctx, req.(*UpdateChannelListFilterRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_GetChannelListFilterRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelListFilterRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).GetChannelListFilterRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/GetChannelListFilterRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).GetChannelListFilterRecord(ctx, req.(*GetChannelListFilterRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_GetChannelMicVolSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMicVolSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).GetChannelMicVolSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/GetChannelMicVolSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).GetChannelMicVolSet(ctx, req.(*GetChannelMicVolSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_SetChannelMicVol_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelMicVolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).SetChannelMicVol(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/SetChannelMicVol",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).SetChannelMicVol(ctx, req.(*SetChannelMicVolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GangupChannel_ListPublishingChannelIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPublishingChannelIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GangupChannelServer).ListPublishingChannelIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gangup_channel.GangupChannel/ListPublishingChannelIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GangupChannelServer).ListPublishingChannelIds(ctx, req.(*ListPublishingChannelIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GangupChannel_serviceDesc = grpc.ServiceDesc{
	ServiceName: "gangup_channel.GangupChannel",
	HandlerType: (*GangupChannelServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetGangupChannelReleaseInfo",
			Handler:    _GangupChannel_SetGangupChannelReleaseInfo_Handler,
		},
		{
			MethodName: "DismissGangupChannel",
			Handler:    _GangupChannel_DismissGangupChannel_Handler,
		},
		{
			MethodName: "GetGangupChannelList",
			Handler:    _GangupChannel_GetGangupChannelList_Handler,
		},
		{
			MethodName: "GetGangupChannelByIds",
			Handler:    _GangupChannel_GetGangupChannelByIds_Handler,
		},
		{
			MethodName: "SwitchChannelTab",
			Handler:    _GangupChannel_SwitchChannelTab_Handler,
		},
		{
			MethodName: "GetOnlineInfo",
			Handler:    _GangupChannel_GetOnlineInfo_Handler,
		},
		{
			MethodName: "GetChannelRoomUserNumber",
			Handler:    _GangupChannel_GetChannelRoomUserNumber_Handler,
		},
		{
			MethodName: "AddTemporaryChannel",
			Handler:    _GangupChannel_AddTemporaryChannel_Handler,
		},
		{
			MethodName: "NegativeFeedBack",
			Handler:    _GangupChannel_NegativeFeedBack_Handler,
		},
		{
			MethodName: "CleanChannelInfo",
			Handler:    _GangupChannel_CleanChannelInfo_Handler,
		},
		{
			MethodName: "GetChannelIdsByTabId",
			Handler:    _GangupChannel_GetChannelIdsByTabId_Handler,
		},
		{
			MethodName: "GetGameHomePageDIYFilter",
			Handler:    _GangupChannel_GetGameHomePageDIYFilter_Handler,
		},
		{
			MethodName: "SetGameHomePageDIYFilter",
			Handler:    _GangupChannel_SetGameHomePageDIYFilter_Handler,
		},
		{
			MethodName: "SetGangupChannelDenoiseMode",
			Handler:    _GangupChannel_SetGangupChannelDenoiseMode_Handler,
		},
		{
			MethodName: "GetGangupChannelExtraInfo",
			Handler:    _GangupChannel_GetGangupChannelExtraInfo_Handler,
		},
		{
			MethodName: "GetGangupExtraHistory",
			Handler:    _GangupChannel_GetGangupExtraHistory_Handler,
		},
		{
			MethodName: "SetGangupExtraHistory",
			Handler:    _GangupChannel_SetGangupExtraHistory_Handler,
		},
		{
			MethodName: "GetGangupPublishCountHistory",
			Handler:    _GangupChannel_GetGangupPublishCountHistory_Handler,
		},
		{
			MethodName: "SetGangupPublishCountHistory",
			Handler:    _GangupChannel_SetGangupPublishCountHistory_Handler,
		},
		{
			MethodName: "GetDIYFilterByEntrance",
			Handler:    _GangupChannel_GetDIYFilterByEntrance_Handler,
		},
		{
			MethodName: "SetDIYFilterByEntrance",
			Handler:    _GangupChannel_SetDIYFilterByEntrance_Handler,
		},
		{
			MethodName: "GetReleasingChannelCountByTabIds",
			Handler:    _GangupChannel_GetReleasingChannelCountByTabIds_Handler,
		},
		{
			MethodName: "GetReleasingChannelRandomCountByTabIds",
			Handler:    _GangupChannel_GetReleasingChannelRandomCountByTabIds_Handler,
		},
		{
			MethodName: "UpdateNegativeFeedbackOption",
			Handler:    _GangupChannel_UpdateNegativeFeedbackOption_Handler,
		},
		{
			MethodName: "GetNegativeFeedbackOption",
			Handler:    _GangupChannel_GetNegativeFeedbackOption_Handler,
		},
		{
			MethodName: "UpdateChannelListFilterRecord",
			Handler:    _GangupChannel_UpdateChannelListFilterRecord_Handler,
		},
		{
			MethodName: "GetChannelListFilterRecord",
			Handler:    _GangupChannel_GetChannelListFilterRecord_Handler,
		},
		{
			MethodName: "GetChannelMicVolSet",
			Handler:    _GangupChannel_GetChannelMicVolSet_Handler,
		},
		{
			MethodName: "SetChannelMicVol",
			Handler:    _GangupChannel_SetChannelMicVol_Handler,
		},
		{
			MethodName: "ListPublishingChannelIds",
			Handler:    _GangupChannel_ListPublishingChannelIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/gangup-channel/gangup-channel.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/gangup-channel/gangup-channel.proto", fileDescriptor_gangup_channel_65affe1df2789b40)
}

var fileDescriptor_gangup_channel_65affe1df2789b40 = []byte{
	// 3954 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x3b, 0x5d, 0x6f, 0x1b, 0x49,
	0x72, 0x1e, 0x52, 0x5f, 0x2c, 0x92, 0x12, 0xd5, 0x92, 0x25, 0x9a, 0xb2, 0xd7, 0xf2, 0xd8, 0xd9,
	0xd5, 0x6a, 0xd7, 0xd2, 0x9e, 0xec, 0xe0, 0x36, 0xbb, 0x7b, 0x8b, 0x93, 0x2c, 0x4a, 0xc7, 0xb3,
	0xbe, 0x3c, 0x94, 0x0c, 0x78, 0xcf, 0x9b, 0xc1, 0x88, 0xd3, 0xa2, 0x06, 0x9a, 0x2f, 0x4d, 0x0f,
	0x65, 0xe9, 0x02, 0x04, 0x08, 0x70, 0x79, 0x09, 0x72, 0xc8, 0x43, 0x80, 0x20, 0xc8, 0x53, 0x02,
	0x04, 0x09, 0x90, 0xa7, 0xe4, 0x25, 0xef, 0x01, 0x92, 0x3f, 0x90, 0xc7, 0xcb, 0x9f, 0x09, 0xfa,
	0x63, 0x86, 0xf3, 0xc9, 0x8f, 0xdd, 0x20, 0xf7, 0xc6, 0xae, 0xae, 0xaf, 0xae, 0xae, 0xaa, 0xae,
	0xae, 0x1e, 0xc2, 0x96, 0xef, 0x6f, 0x5e, 0xf7, 0x8c, 0xce, 0x15, 0x31, 0xcc, 0x1b, 0xec, 0x6d,
	0x76, 0x35, 0xbb, 0xdb, 0x73, 0x9f, 0x77, 0x2e, 0x35, 0xdb, 0xc6, 0x66, 0x62, 0xb8, 0xe1, 0x7a,
	0x8e, 0xef, 0xa0, 0x59, 0x0e, 0x55, 0x05, 0x54, 0x6e, 0xc3, 0xdc, 0x8e, 0xe9, 0x74, 0xae, 0x8e,
	0x5d, 0xdf, 0x70, 0xec, 0x03, 0x83, 0xf8, 0xe8, 0xe7, 0x50, 0x3d, 0xa7, 0x20, 0xd5, 0x61, 0x30,
	0x52, 0x97, 0x56, 0x8b, 0x6b, 0xe5, 0xad, 0x95, 0x8d, 0x38, 0xe9, 0x46, 0x84, 0x4e, 0xa9, 0x9c,
	0xf7, 0x07, 0x44, 0x7e, 0x0f, 0xe5, 0xc8, 0x24, 0x7a, 0x00, 0x33, 0x9c, 0xa1, 0xa1, 0xd7, 0xa5,
	0x55, 0x69, 0xad, 0xaa, 0x4c, 0xb3, 0x71, 0x4b, 0x47, 0xcb, 0x30, 0x8d, 0x4d, 0x6c, 0xd1, 0x99,
	0x02, 0x9b, 0x99, 0xa2, 0xc3, 0x96, 0x4e, 0x69, 0xd8, 0xc4, 0x8d, 0x66, 0xd6, 0x8b, 0xab, 0xd2,
	0x5a, 0x49, 0x61, 0x88, 0x6f, 0x35, 0x53, 0x3e, 0x85, 0xd2, 0xbe, 0x66, 0xe1, 0x03, 0xed, 0x1c,
	0x9b, 0xa8, 0x06, 0x45, 0x8a, 0x22, 0x31, 0x14, 0xfa, 0x13, 0x3d, 0x81, 0x8a, 0x6e, 0x10, 0xd7,
	0xd4, 0xee, 0x54, 0x5b, 0xb3, 0x30, 0xe3, 0x5b, 0x52, 0xca, 0x02, 0x76, 0xa4, 0x59, 0x18, 0x21,
	0x98, 0xf0, 0xef, 0x5c, 0xcc, 0x18, 0x57, 0x15, 0xf6, 0x5b, 0xfe, 0xfb, 0x09, 0xa8, 0xef, 0xb3,
	0x05, 0xbe, 0xe2, 0xeb, 0x53, 0xb0, 0x89, 0x35, 0x82, 0x5b, 0xf6, 0x85, 0x83, 0x66, 0xa1, 0x10,
	0xea, 0x5e, 0x30, 0x74, 0x74, 0x1f, 0xa6, 0x7c, 0xed, 0xbc, 0xaf, 0xf5, 0xa4, 0xaf, 0x9d, 0xb7,
	0x74, 0x2a, 0xda, 0xe3, 0x54, 0xaa, 0x6f, 0x58, 0x9c, 0x7f, 0x51, 0x29, 0x0b, 0xd8, 0xa9, 0x61,
	0xe1, 0xb4, 0x71, 0x27, 0xc6, 0x34, 0x2e, 0x6a, 0xf6, 0xd7, 0xc7, 0x16, 0x31, 0xb9, 0x5a, 0x5c,
	0x9b, 0xdd, 0x92, 0x93, 0x0c, 0xc4, 0x2a, 0x76, 0x39, 0xea, 0xe9, 0x9d, 0x8b, 0x43, 0x1b, 0xd0,
	0x01, 0x92, 0xa1, 0x4a, 0x2e, 0x9d, 0x0f, 0x6a, 0x17, 0x3b, 0xaa, 0x61, 0x5f, 0x38, 0xf5, 0xa9,
	0x55, 0x69, 0x6d, 0x46, 0x29, 0x53, 0xe0, 0x3e, 0x76, 0xd8, 0xb2, 0x57, 0xa0, 0x64, 0x69, 0xde,
	0x15, 0xf6, 0xe9, 0x4a, 0xa7, 0xd9, 0x4a, 0x67, 0x38, 0xa0, 0xa5, 0xa3, 0xa7, 0x50, 0xf5, 0xb1,
	0x67, 0x19, 0xb6, 0x66, 0x72, 0x45, 0x66, 0x18, 0x42, 0x25, 0x00, 0x32, 0x29, 0xeb, 0x30, 0x6f,
	0x6a, 0xc4, 0x57, 0x75, 0x83, 0x58, 0x06, 0x21, 0xdc, 0x2c, 0x25, 0x66, 0x96, 0x39, 0x3a, 0xb1,
	0xcb, 0xe1, 0xcc, 0x34, 0x8f, 0xa1, 0x4c, 0x3e, 0x18, 0x7e, 0xe7, 0x92, 0x63, 0x01, 0xc3, 0x02,
	0x0e, 0x62, 0x08, 0x4f, 0xa0, 0xe2, 0xf6, 0xc8, 0xa5, 0x69, 0x90, 0x4b, 0xb5, 0x67, 0xe8, 0xf5,
	0x32, 0x13, 0x58, 0x0e, 0x60, 0x67, 0x86, 0x8e, 0xbe, 0x82, 0x72, 0x57, 0xb3, 0xb0, 0x6a, 0x52,
	0xe7, 0x20, 0xf5, 0x0a, 0x33, 0xee, 0x83, 0xa4, 0x6d, 0x42, 0xf7, 0x51, 0xa0, 0x1b, 0xfc, 0x24,
	0x54, 0x7e, 0xc7, 0x34, 0xb0, 0xed, 0xf3, 0xe5, 0x54, 0x19, 0x77, 0xe0, 0x20, 0xba, 0x18, 0xf9,
	0x7f, 0x8a, 0xf0, 0x51, 0x1b, 0xfb, 0x79, 0x5e, 0xa2, 0xe0, 0x6b, 0xd4, 0x85, 0x95, 0xb8, 0x2c,
	0x35, 0x70, 0x08, 0x66, 0x63, 0xea, 0x41, 0xe5, 0xad, 0xb5, 0xb4, 0x3e, 0x39, 0x1c, 0xeb, 0xdd,
	0x3c, 0x8f, 0x7c, 0x04, 0xf0, 0x41, 0xb3, 0x7d, 0xf5, 0xc2, 0xc3, 0xe4, 0x92, 0x79, 0xe1, 0x8c,
	0x52, 0xa2, 0x90, 0x3d, 0x0a, 0xa0, 0xd3, 0xa1, 0x60, 0x57, 0x04, 0x50, 0x49, 0x40, 0x5a, 0x2e,
	0xb5, 0x64, 0xa0, 0x1f, 0x8b, 0x91, 0x09, 0x1e, 0x23, 0x02, 0xc6, 0x62, 0xa4, 0x0e, 0xd3, 0x1d,
	0x0f, 0x6b, 0xbe, 0xe3, 0xd5, 0x27, 0x79, 0xcc, 0x8a, 0x21, 0xdd, 0x53, 0xcd, 0x34, 0x55, 0x82,
	0x4d, 0xdc, 0xf1, 0xb1, 0xae, 0x9e, 0x1b, 0x3a, 0xa9, 0x4f, 0xad, 0x16, 0xd7, 0xaa, 0xca, 0x9c,
	0x66, 0x9a, 0x6d, 0x01, 0xdf, 0x31, 0x74, 0x82, 0x3e, 0x03, 0xd4, 0xb3, 0x05, 0xaa, 0x1a, 0x26,
	0x81, 0x69, 0x8e, 0xdc, 0xb3, 0x39, 0xee, 0x8e, 0x48, 0x06, 0x0a, 0x2c, 0x27, 0x91, 0x83, 0x28,
	0x99, 0x19, 0x1e, 0x25, 0x8b, 0x31, 0x76, 0x41, 0xb4, 0xac, 0xc1, 0x9c, 0xb0, 0xde, 0x89, 0xa9,
	0xdd, 0x1d, 0x3a, 0x3a, 0x77, 0xbf, 0xaa, 0x92, 0x04, 0xcb, 0x4f, 0xe0, 0xf1, 0xc0, 0xcd, 0x25,
	0xae, 0xfc, 0xb7, 0x12, 0x2c, 0x0b, 0x8f, 0x4d, 0xe0, 0x5d, 0x53, 0x8b, 0x07, 0x26, 0x0d, 0x53,
	0x45, 0x49, 0x40, 0x5a, 0x3a, 0x5a, 0x82, 0x29, 0xe2, 0xf4, 0xbc, 0x4e, 0x90, 0x8f, 0xc4, 0x08,
	0x6d, 0x46, 0x52, 0xd1, 0x6c, 0x7a, 0x81, 0x41, 0x7c, 0xd0, 0xf0, 0x65, 0x88, 0x91, 0xd4, 0x33,
	0x11, 0x49, 0x3d, 0xf2, 0x4b, 0xa8, 0x67, 0x6b, 0x46, 0x5c, 0xba, 0x95, 0x22, 0xfe, 0x98, 0x5e,
	0x33, 0x4a, 0x30, 0x94, 0x5f, 0xc0, 0xe3, 0x7d, 0xec, 0x2b, 0xb8, 0xe3, 0x58, 0x16, 0xb6, 0x75,
	0x41, 0x44, 0x4f, 0x81, 0x03, 0x47, 0xd3, 0x0f, 0x1d, 0x0f, 0xd3, 0x04, 0x6b, 0xf7, 0x2c, 0xb1,
	0x20, 0xfa, 0x53, 0xd6, 0x60, 0x79, 0x3f, 0x61, 0x28, 0x4a, 0x41, 0x8d, 0x50, 0x83, 0x62, 0x2f,
	0x5c, 0x3d, 0xfd, 0x89, 0x16, 0x61, 0xd2, 0x34, 0x2c, 0xc3, 0x0f, 0x12, 0x25, 0x1b, 0xa0, 0x8f,
	0xa0, 0xcc, 0x17, 0xa1, 0x9a, 0x06, 0xf1, 0xeb, 0x45, 0xe6, 0x0f, 0x25, 0xb6, 0x12, 0xca, 0x4a,
	0xee, 0x42, 0x3d, 0x5b, 0x04, 0x71, 0xd1, 0xeb, 0xbe, 0xef, 0x32, 0x62, 0x7e, 0x3a, 0x8d, 0x1e,
	0x53, 0x81, 0x97, 0x33, 0x41, 0x7f, 0x2e, 0xa5, 0x25, 0xed, 0xdc, 0xb5, 0x74, 0x22, 0x56, 0x43,
	0x5d, 0x5b, 0x62, 0xda, 0xd1, 0x9f, 0xe8, 0x4b, 0x98, 0xa4, 0x9b, 0x40, 0xea, 0x85, 0x91, 0x93,
	0x2e, 0x27, 0xe0, 0x01, 0xe9, 0xf7, 0x3c, 0x5b, 0xd5, 0x4c, 0x7e, 0xa2, 0xcd, 0xd0, 0x80, 0xa4,
	0x90, 0x6d, 0xd3, 0x94, 0xdf, 0xc1, 0x83, 0x1c, 0x35, 0x88, 0x8b, 0xbe, 0x81, 0x09, 0x91, 0x3d,
	0xc6, 0x5b, 0x29, 0xa3, 0x92, 0xff, 0x55, 0x82, 0x85, 0x36, 0x4b, 0xa2, 0x02, 0xe5, 0x54, 0x3b,
	0xcf, 0xde, 0xab, 0x9c, 0x53, 0x2d, 0xee, 0xd9, 0xc5, 0xa4, 0x67, 0x2f, 0xc2, 0xa4, 0xe6, 0xba,
	0xad, 0xd0, 0x1f, 0xd9, 0x00, 0x35, 0x20, 0x3c, 0x29, 0x44, 0xfe, 0xe8, 0x9f, 0x1c, 0x89, 0x44,
	0x3f, 0x95, 0x4c, 0xf4, 0xf2, 0x3f, 0x49, 0xb0, 0x98, 0x56, 0x99, 0xb8, 0xb4, 0x2a, 0xa0, 0x1a,
	0xb2, 0x9c, 0xc5, 0x8f, 0xfc, 0x69, 0x5f, 0x3b, 0x67, 0xf9, 0x6a, 0x0d, 0x6a, 0x1f, 0xb0, 0xd9,
	0x71, 0x2c, 0xac, 0xfa, 0xb7, 0x3e, 0x77, 0x0d, 0xba, 0x4b, 0x25, 0x65, 0x56, 0xc0, 0x4f, 0x6f,
	0x7d, 0x56, 0xdf, 0x2c, 0xc3, 0xb4, 0x65, 0x74, 0x54, 0xcb, 0x09, 0x16, 0x33, 0x65, 0x19, 0x9d,
	0x43, 0x47, 0x0f, 0xb8, 0xb3, 0x78, 0xe4, 0x8b, 0xa1, 0xdc, 0x4f, 0xc3, 0xa8, 0xeb, 0xd2, 0xf5,
	0x4f, 0x06, 0xa6, 0xe9, 0xb6, 0x74, 0xf9, 0x3f, 0x26, 0x60, 0x61, 0xd7, 0x20, 0x9a, 0xeb, 0x62,
	0xcd, 0x8b, 0x24, 0x83, 0x15, 0x28, 0x89, 0xa3, 0x44, 0x58, 0xb8, 0xa4, 0xcc, 0x70, 0x40, 0x4b,
	0x47, 0x9f, 0x42, 0x4d, 0xeb, 0x5c, 0xf7, 0x0c, 0x0f, 0xab, 0x7a, 0xcf, 0xd3, 0x68, 0x9e, 0x62,
	0x06, 0x9f, 0x50, 0xe6, 0x04, 0x7c, 0x57, 0x80, 0xd1, 0x09, 0x54, 0xa9, 0x89, 0x9c, 0x9e, 0xaf,
	0xe2, 0x1b, 0x6c, 0xfb, 0xec, 0x50, 0x2c, 0x6f, 0x7d, 0x96, 0x91, 0x26, 0x92, 0x3a, 0x6c, 0x9c,
	0x72, 0x42, 0xa5, 0x22, 0x38, 0x34, 0x29, 0x03, 0xf4, 0x16, 0xe6, 0xae, 0x30, 0x76, 0x35, 0xd3,
	0xb8, 0xc1, 0x82, 0x67, 0x99, 0xf1, 0x7c, 0x3e, 0x0a, 0xcf, 0xd7, 0x01, 0xa9, 0x32, 0x1b, 0x72,
	0xe1, 0x7c, 0x2f, 0xe0, 0x7e, 0xb4, 0xf4, 0xe9, 0x6b, 0x5c, 0x61, 0xdc, 0xb7, 0x46, 0xe1, 0xae,
	0xf4, 0xeb, 0x24, 0xaa, 0xf8, 0x82, 0x17, 0x1b, 0x33, 0x39, 0x8d, 0x97, 0x30, 0x2d, 0xc6, 0xd4,
	0x8e, 0x81, 0xa8, 0xd0, 0x8e, 0x12, 0xb7, 0xa3, 0x80, 0x07, 0x76, 0x6c, 0x7c, 0x0f, 0xa5, 0x50,
	0x75, 0xf4, 0x1c, 0x50, 0x38, 0x48, 0x52, 0xce, 0x87, 0x33, 0xe1, 0x1e, 0x3c, 0x81, 0x8a, 0x85,
	0xad, 0x73, 0xec, 0xa9, 0x1d, 0xa7, 0x67, 0x07, 0x89, 0xac, 0xcc, 0x61, 0xaf, 0x28, 0xa8, 0xf1,
	0x35, 0xcc, 0xc6, 0x75, 0x1f, 0x43, 0x37, 0xf9, 0xa7, 0xb0, 0x98, 0x36, 0x06, 0x71, 0x59, 0x39,
	0x12, 0x86, 0x5d, 0x90, 0x85, 0x20, 0x8c, 0x3b, 0x22, 0x7f, 0x0b, 0xb5, 0x7d, 0xec, 0x1f, 0xdb,
	0xa6, 0x61, 0x87, 0xf5, 0xc7, 0x3a, 0xcc, 0x3b, 0x0c, 0xa0, 0xf6, 0x48, 0xa8, 0x31, 0x0f, 0xf1,
	0x39, 0x3e, 0x71, 0x46, 0x84, 0xd6, 0xf2, 0x7b, 0x98, 0x4f, 0xd0, 0x13, 0x97, 0xe5, 0x29, 0xc7,
	0xb1, 0x62, 0x94, 0x25, 0x0a, 0x61, 0x34, 0x34, 0xca, 0xa2, 0xfc, 0xc3, 0x28, 0xab, 0x2a, 0xb3,
	0x7d, 0xf6, 0x2c, 0xb3, 0xfe, 0x0a, 0x6a, 0x7b, 0x1e, 0xc6, 0xbf, 0xc6, 0x91, 0xb0, 0xf8, 0x18,
	0xe6, 0xfa, 0x4b, 0xea, 0x67, 0xef, 0xaa, 0x52, 0x0d, 0x97, 0xc5, 0x22, 0xf4, 0x31, 0x94, 0x2f,
	0x18, 0x2d, 0x4f, 0x10, 0x05, 0x9e, 0x20, 0x38, 0x88, 0x25, 0x88, 0x05, 0x98, 0x4f, 0x30, 0x27,
	0xae, 0xfc, 0x0d, 0xa0, 0x33, 0xfb, 0xe2, 0x07, 0xca, 0x94, 0xef, 0xc3, 0x42, 0x8a, 0x9a, 0xb8,
	0xf2, 0x97, 0xec, 0xb0, 0x13, 0x10, 0x2e, 0x33, 0xb0, 0xf5, 0xe0, 0x13, 0x5f, 0xfe, 0x9a, 0x9d,
	0x2c, 0x19, 0x94, 0x7c, 0x6f, 0xa3, 0x0b, 0x94, 0x52, 0x0b, 0xfc, 0x1b, 0x09, 0x50, 0x1b, 0xfb,
	0xcd, 0x5b, 0xdf, 0xd3, 0x7e, 0x61, 0x10, 0xdf, 0xf1, 0xee, 0x46, 0x28, 0x32, 0xbe, 0x85, 0xca,
	0x25, 0x47, 0xe6, 0x49, 0xac, 0x90, 0x5d, 0x54, 0x08, 0x86, 0xfc, 0x4e, 0x70, 0xd9, 0x1f, 0x50,
	0x57, 0xc7, 0xb7, 0x2e, 0x4d, 0x4c, 0xda, 0x85, 0x8f, 0xbd, 0xe0, 0xfe, 0xc2, 0x61, 0xdb, 0x14,
	0x44, 0xcd, 0x94, 0xd2, 0x8b, 0xb8, 0x32, 0x01, 0xb4, 0xff, 0xff, 0xad, 0xae, 0xfc, 0x19, 0x2c,
	0xec, 0xa7, 0x75, 0xa1, 0x07, 0xd2, 0x8d, 0x66, 0xf6, 0x82, 0x13, 0x82, 0x0f, 0xe4, 0x9f, 0xc2,
	0x52, 0x7f, 0x3b, 0x82, 0xa2, 0x6f, 0x84, 0xca, 0x4d, 0xfe, 0x22, 0xea, 0x01, 0x11, 0x42, 0xe2,
	0x46, 0x0e, 0x4c, 0x29, 0x5e, 0x8b, 0xad, 0xf4, 0x29, 0x14, 0xc7, 0xb1, 0x68, 0x50, 0x1c, 0xf5,
	0x68, 0xbe, 0xa0, 0xf2, 0xa2, 0x54, 0xc5, 0x3e, 0xd5, 0x7f, 0x4b, 0xf0, 0x30, 0x9f, 0x8c, 0xb8,
	0x48, 0x85, 0x59, 0x16, 0x9a, 0x2c, 0xf2, 0x22, 0x05, 0xc1, 0x1f, 0xa5, 0x0a, 0x82, 0x01, 0x5c,
	0x36, 0x02, 0x10, 0xf3, 0xc5, 0x8a, 0x17, 0x19, 0x35, 0xde, 0x40, 0x25, 0x3a, 0x9b, 0xb3, 0x3c,
	0x9a, 0x63, 0x7c, 0xc7, 0xd7, 0x4c, 0xae, 0x88, 0xcd, 0x58, 0x8b, 0x18, 0x9d, 0x63, 0x13, 0x7d,
	0x89, 0xf2, 0x7b, 0x58, 0xda, 0xd6, 0xf5, 0x53, 0x6c, 0xb9, 0x8e, 0xa7, 0x79, 0x77, 0x91, 0xb8,
	0xdc, 0x81, 0x69, 0xa1, 0xef, 0xd8, 0xb7, 0xa2, 0x80, 0x50, 0x7e, 0x00, 0xcb, 0x99, 0xdc, 0x89,
	0x2b, 0xff, 0x6e, 0x02, 0x16, 0x8e, 0x70, 0x57, 0xf3, 0x8d, 0x1b, 0xbc, 0x87, 0xb1, 0xbe, 0xa3,
	0x75, 0xae, 0x46, 0x70, 0xc9, 0x9c, 0x12, 0x28, 0x72, 0x19, 0x2a, 0xc6, 0x2f, 0x43, 0x3f, 0xfe,
	0x3e, 0x8f, 0x60, 0x82, 0xd5, 0x33, 0x93, 0xcc, 0x5b, 0xd9, 0x6f, 0xf4, 0x1d, 0x2c, 0xd9, 0x42,
	0x79, 0x95, 0x6a, 0x7f, 0xae, 0x75, 0xae, 0x78, 0x8c, 0x4c, 0xb1, 0xc2, 0xf3, 0x59, 0x92, 0x7d,
	0x74, 0xa9, 0x14, 0x99, 0x05, 0xcb, 0xa2, 0x9d, 0x01, 0xe5, 0x4d, 0x0a, 0xd7, 0xf1, 0x7c, 0xec,
	0xb1, 0x5b, 0x34, 0xbf, 0xd7, 0x97, 0x03, 0x18, 0xbd, 0x45, 0x6f, 0xc1, 0xfd, 0xe8, 0xf5, 0x50,
	0xbd, 0xc2, 0x77, 0x1f, 0x1c, 0x4f, 0xe7, 0xd7, 0xb0, 0x92, 0xb2, 0x10, 0xb9, 0x27, 0xbe, 0x16,
	0x53, 0x51, 0x1a, 0xe7, 0x83, 0x8d, 0x3d, 0xd5, 0xc3, 0x1a, 0xa1, 0x06, 0x29, 0xc5, 0x68, 0x8e,
	0xe9, 0x9c, 0xc2, 0xa7, 0xd0, 0xe7, 0x80, 0xce, 0x4d, 0xba, 0xb4, 0x80, 0x92, 0x7a, 0x14, 0xab,
	0x71, 0xaa, 0x4a, 0x8d, 0xcd, 0x88, 0x2d, 0xa5, 0x1e, 0x85, 0xf6, 0x60, 0x35, 0x8d, 0xad, 0x62,
	0x5b, 0x3b, 0x37, 0xb1, 0x7a, 0x61, 0x98, 0x34, 0x63, 0x95, 0x59, 0x61, 0xfd, 0x30, 0x49, 0xdb,
	0x64, 0x48, 0x7b, 0x0c, 0x07, 0xfd, 0x1c, 0x1e, 0x09, 0xdd, 0x54, 0xe7, 0x42, 0xcd, 0x50, 0xa0,
	0xc2, 0x34, 0x7e, 0x20, 0x90, 0x8e, 0x2f, 0x76, 0x12, 0xdc, 0xe4, 0x25, 0x58, 0x4c, 0xfb, 0x16,
	0x71, 0xe5, 0x97, 0xb0, 0xf0, 0xca, 0xc4, 0x9a, 0x2d, 0x70, 0x47, 0x3c, 0x28, 0x96, 0x60, 0x31,
	0x4d, 0x45, 0x5c, 0xf9, 0xdf, 0xa4, 0x68, 0xe6, 0x69, 0xe9, 0x64, 0xe7, 0xee, 0x94, 0x7a, 0x63,
	0x32, 0x87, 0x44, 0xfc, 0xf4, 0x97, 0x30, 0xd3, 0xc5, 0x3e, 0x2d, 0x6d, 0x83, 0x6c, 0xba, 0x99,
	0x9f, 0x1c, 0x62, 0x1c, 0x29, 0x9c, 0x26, 0x36, 0x65, 0xba, 0xcb, 0x7f, 0xc8, 0x2f, 0x60, 0x5a,
	0xc0, 0xd0, 0x34, 0x14, 0xb7, 0x0f, 0x0e, 0x6a, 0xf7, 0x50, 0x0d, 0x2a, 0xc7, 0x47, 0x07, 0xef,
	0x54, 0xa5, 0x79, 0xd0, 0xdc, 0x6e, 0x37, 0x6b, 0x12, 0xaa, 0x42, 0x89, 0x41, 0xda, 0xad, 0xa3,
	0xd7, 0xb5, 0x42, 0xfc, 0xd0, 0x8b, 0x0a, 0x18, 0xa5, 0xa0, 0xf9, 0x77, 0x89, 0x25, 0xce, 0x7d,
	0xcd, 0xc2, 0xbf, 0x70, 0x2c, 0x7c, 0xa2, 0x75, 0xf1, 0x6e, 0xeb, 0x1d, 0xdf, 0xb5, 0xec, 0x1b,
	0xcb, 0x6b, 0xa8, 0x79, 0xb8, 0xdb, 0x33, 0x35, 0x76, 0x88, 0x98, 0xf8, 0x06, 0x9b, 0x62, 0xdd,
	0xab, 0xc9, 0x75, 0x2b, 0xcd, 0xfd, 0xb3, 0x83, 0xed, 0xd3, 0x63, 0xe5, 0x9d, 0x7a, 0xd0, 0x7c,
	0xdb, 0x3c, 0x50, 0xe6, 0xfa, 0x94, 0x07, 0x94, 0x10, 0x7d, 0x09, 0x80, 0x6d, 0x3f, 0x38, 0x8c,
	0xf8, 0x85, 0x3c, 0xd5, 0x3a, 0x6a, 0x52, 0x0c, 0x16, 0x5d, 0x25, 0x1c, 0xfc, 0x94, 0xdf, 0xb3,
	0xcc, 0x9d, 0xa3, 0x37, 0xbb, 0xc0, 0x4d, 0x1a, 0x3e, 0xb6, 0x82, 0x4e, 0xea, 0xc7, 0x59, 0xfd,
	0xa8, 0x80, 0x92, 0x93, 0xb5, 0x7c, 0x6c, 0x29, 0x9c, 0x48, 0xbe, 0x80, 0xa5, 0x6c, 0x84, 0x3c,
	0x2f, 0xa0, 0x86, 0xd6, 0x7c, 0xdc, 0xa5, 0x36, 0x09, 0x33, 0x19, 0x04, 0x20, 0x7e, 0x65, 0xf3,
	0x0d, 0xdf, 0xc4, 0xa2, 0x31, 0xc4, 0x07, 0xf2, 0x3f, 0x4b, 0xb0, 0xd2, 0x1e, 0xcb, 0xfc, 0xe1,
	0xba, 0x0a, 0x3f, 0x60, 0x5d, 0x3f, 0xc2, 0xde, 0x1f, 0xc1, 0xc3, 0xf6, 0x00, 0x7b, 0xcb, 0xed,
	0x74, 0x9f, 0x6e, 0x17, 0xdb, 0x8e, 0x41, 0x30, 0x73, 0xf1, 0xe1, 0xc7, 0x00, 0x82, 0x89, 0x30,
	0x86, 0xaa, 0x0a, 0xfb, 0x9d, 0xd5, 0x1f, 0x8a, 0x31, 0x25, 0xae, 0xfc, 0x35, 0xdd, 0xa9, 0xc8,
	0x3c, 0x2b, 0x4d, 0xd8, 0x51, 0xfa, 0x04, 0x2a, 0x3a, 0x47, 0xe6, 0xc1, 0xc9, 0x25, 0x96, 0xf5,
	0x3e, 0x03, 0xf9, 0x67, 0xc2, 0x89, 0xb2, 0xe8, 0x47, 0xc8, 0x22, 0xbf, 0x82, 0x47, 0x03, 0xc8,
	0x89, 0x8b, 0xbe, 0x0a, 0xbb, 0x08, 0x52, 0xf6, 0x5e, 0x65, 0x52, 0xf2, 0x1e, 0xc2, 0xeb, 0x48,
	0x97, 0x24, 0x59, 0xe4, 0xd5, 0xa0, 0xd8, 0xe9, 0xbb, 0x45, 0xc7, 0x60, 0x0d, 0x03, 0x83, 0x04,
	0x8d, 0xcf, 0xa0, 0x37, 0x69, 0x10, 0x71, 0x84, 0xcb, 0x3f, 0x89, 0xf4, 0x3a, 0x46, 0x2c, 0xde,
	0x7e, 0x23, 0x41, 0xbd, 0x3d, 0xba, 0x02, 0x21, 0x93, 0x42, 0x84, 0xc9, 0x08, 0xd5, 0x6d, 0x42,
	0xf3, 0x89, 0xa4, 0xe6, 0x2b, 0xf0, 0xa0, 0x9d, 0xa7, 0xb9, 0xe8, 0xa5, 0xf1, 0xc9, 0x93, 0xde,
	0xb9, 0x69, 0x90, 0x4b, 0x76, 0x67, 0x1a, 0xa4, 0xa9, 0xbc, 0x0f, 0xab, 0x83, 0x89, 0x88, 0x8b,
	0x9e, 0x42, 0xd5, 0xe5, 0x53, 0xb1, 0x5b, 0x59, 0xc5, 0x8d, 0xe0, 0x53, 0xe9, 0xed, 0xb1, 0xa5,
	0xcb, 0xb0, 0xda, 0x1e, 0x22, 0x5d, 0xfe, 0x3b, 0x89, 0x2d, 0x3a, 0x0c, 0xb0, 0x9d, 0x3b, 0x1a,
	0x92, 0x9a, 0xdd, 0xc1, 0xd9, 0x39, 0xe1, 0x45, 0x3c, 0x27, 0x3c, 0x4a, 0x5d, 0xfc, 0x03, 0x46,
	0xd1, 0x54, 0xf0, 0x28, 0x95, 0x0a, 0xaa, 0x91, 0x78, 0x47, 0xcb, 0x30, 0x6d, 0x10, 0xd5, 0xb0,
	0x3b, 0x9e, 0xd8, 0x93, 0x29, 0x83, 0xb4, 0xec, 0x8e, 0x27, 0xff, 0x95, 0x04, 0xd5, 0x18, 0x43,
	0x7a, 0x41, 0x65, 0x0f, 0x00, 0xe7, 0x3d, 0x62, 0xd8, 0x98, 0x90, 0x7e, 0xac, 0xcc, 0x52, 0xf8,
	0x8e, 0x00, 0xf3, 0x32, 0xd6, 0xea, 0x11, 0xa3, 0x13, 0x43, 0xe5, 0x0e, 0x33, 0xc7, 0x26, 0x22,
	0xb8, 0x6b, 0x50, 0xe3, 0x05, 0x86, 0x4a, 0xf5, 0x8d, 0x6a, 0x39, 0x7b, 0x11, 0xca, 0x66, 0xa9,
	0xe9, 0x21, 0x34, 0xf2, 0xac, 0x45, 0x5c, 0xf9, 0x80, 0xb9, 0xfe, 0xc8, 0xb6, 0x8c, 0x9b, 0xa5,
	0x90, 0x30, 0x8b, 0xfc, 0x06, 0x1a, 0xfb, 0xb9, 0xb2, 0xfa, 0x1b, 0x21, 0x8d, 0xbe, 0x11, 0xf2,
	0xb7, 0xf0, 0x94, 0x35, 0x84, 0xa9, 0xbf, 0x1b, 0x76, 0x57, 0xa4, 0x03, 0xe6, 0x14, 0xe2, 0x28,
	0x67, 0x9d, 0xd1, 0x65, 0x98, 0xe6, 0x07, 0x4f, 0x70, 0x8c, 0x4f, 0xb1, 0x93, 0x87, 0xc8, 0xff,
	0x29, 0xc1, 0xb3, 0xe1, 0x0c, 0xd8, 0x65, 0xa6, 0xc4, 0x9c, 0x59, 0xb5, 0x34, 0x57, 0x68, 0xb8,
	0x93, 0x51, 0xaa, 0x0c, 0x65, 0xb4, 0xc1, 0x20, 0x87, 0x9a, 0xcb, 0x0e, 0x0a, 0x65, 0xa6, 0x23,
	0x86, 0x8d, 0xaf, 0xa1, 0x1a, 0x9b, 0xa2, 0xe6, 0xbd, 0xc2, 0x77, 0x81, 0x79, 0xaf, 0xf0, 0x5d,
	0x3c, 0x4d, 0x14, 0x45, 0x9a, 0xf8, 0xaa, 0xf0, 0xa5, 0x24, 0xff, 0x32, 0x7c, 0x35, 0x08, 0x4a,
	0x67, 0x7a, 0x05, 0x08, 0x2a, 0x5a, 0x89, 0xd5, 0x87, 0xc1, 0x90, 0xee, 0x12, 0xbf, 0x02, 0xf8,
	0xf8, 0xd6, 0x17, 0x1e, 0x54, 0x62, 0x90, 0x53, 0x7c, 0xeb, 0xcb, 0xff, 0x55, 0x80, 0x5a, 0xb2,
	0x3c, 0x47, 0x1b, 0xb0, 0x60, 0x1a, 0xc4, 0x17, 0xa5, 0xf2, 0x85, 0x00, 0x0b, 0xce, 0xf3, 0x74,
	0x8a, 0x15, 0xca, 0x21, 0xfe, 0x19, 0x2c, 0x71, 0xd4, 0xe0, 0x08, 0x08, 0x49, 0x0a, 0x2c, 0x9d,
	0x3f, 0xce, 0xe9, 0x44, 0x07, 0x0c, 0x94, 0x45, 0x46, 0x9e, 0x5c, 0xd4, 0x19, 0x2c, 0x75, 0x7b,
	0x98, 0xf8, 0x69, 0xb6, 0xc5, 0x11, 0xd9, 0x32, 0xf2, 0x24, 0xdb, 0x36, 0xdc, 0xbf, 0xee, 0x19,
	0x19, 0x5c, 0x27, 0x46, 0xe3, 0xba, 0x40, 0xa9, 0x13, 0x40, 0x59, 0x85, 0xc7, 0x67, 0xae, 0xae,
	0xf9, 0x38, 0x69, 0x4c, 0x71, 0xab, 0xc2, 0xd7, 0xe8, 0x1b, 0x98, 0x89, 0x98, 0x92, 0x8a, 0x5a,
	0x1d, 0x76, 0x51, 0x52, 0x42, 0x0a, 0x9a, 0x0d, 0x07, 0x0b, 0x20, 0x2e, 0xad, 0x3c, 0xf6, 0xb1,
	0x9f, 0xab, 0x81, 0xfc, 0x3d, 0x3b, 0x85, 0xf3, 0x19, 0xfc, 0x48, 0x15, 0x9f, 0x43, 0x25, 0x28,
	0x73, 0x3a, 0x8e, 0xc7, 0x13, 0x84, 0x49, 0x13, 0x12, 0xab, 0xe6, 0xb8, 0xf7, 0x94, 0x28, 0xe4,
	0x94, 0x55, 0x74, 0x7f, 0x59, 0x08, 0x96, 0x14, 0x79, 0x44, 0x89, 0x32, 0x18, 0xeb, 0x1d, 0x40,
	0x85, 0x0a, 0x7b, 0x56, 0x55, 0x3d, 0x46, 0xcb, 0x5e, 0x6d, 0xca, 0x5b, 0xdf, 0x24, 0xd5, 0x1f,
	0x26, 0x70, 0x83, 0xbf, 0xbc, 0xf2, 0x61, 0xd9, 0xec, 0x0f, 0x1a, 0x7f, 0x0c, 0xe5, 0xc8, 0x1c,
	0x3d, 0xf7, 0xc2, 0x87, 0x6e, 0xb1, 0x3e, 0x1a, 0x5a, 0xc1, 0xeb, 0x37, 0x5b, 0x22, 0xcd, 0xe2,
	0x1e, 0x36, 0x35, 0x1f, 0xeb, 0xfc, 0xcd, 0x97, 0x7d, 0x30, 0x20, 0xb2, 0xb8, 0x98, 0x60, 0x3c,
	0xdf, 0x6a, 0xa6, 0xfc, 0x14, 0x9e, 0x0c, 0x51, 0x8e, 0xb8, 0xf2, 0x1b, 0xb6, 0x83, 0x03, 0xec,
	0xf5, 0x00, 0x66, 0x7a, 0xf1, 0x4e, 0xe2, 0x74, 0xcf, 0xe0, 0x7d, 0xcb, 0x6c, 0xc3, 0xc9, 0xbf,
	0x93, 0xe0, 0xa3, 0x41, 0x3c, 0x89, 0x8b, 0xde, 0x03, 0x70, 0xab, 0x46, 0xf2, 0xe1, 0xcf, 0xf2,
	0xaf, 0x6e, 0x59, 0x3c, 0x36, 0xf8, 0xcf, 0x30, 0x15, 0x96, 0xbc, 0x60, 0xdc, 0xf8, 0x0e, 0x66,
	0xe3, 0x93, 0x19, 0xc9, 0x70, 0x2b, 0x9a, 0x0c, 0xcb, 0x5b, 0x0f, 0x93, 0xc2, 0x63, 0xe2, 0x22,
	0xa9, 0x72, 0x3d, 0xda, 0x57, 0x3b, 0x34, 0x3a, 0x6f, 0x1d, 0xb3, 0x8d, 0xfd, 0xec, 0x7a, 0x63,
	0x0f, 0x16, 0x93, 0x88, 0xc1, 0x3d, 0xc6, 0x32, 0x3a, 0x91, 0x7b, 0x8c, 0x65, 0x74, 0xf8, 0xc7,
	0x21, 0x96, 0x76, 0xab, 0xde, 0x38, 0x66, 0xf0, 0x71, 0x88, 0xa5, 0xdd, 0xbe, 0x75, 0x4c, 0xf9,
	0x2f, 0x62, 0x37, 0xe3, 0x88, 0x50, 0xe2, 0xa2, 0x5d, 0x28, 0x53, 0x5e, 0x37, 0x8e, 0xa9, 0x12,
	0x1c, 0xbc, 0x0e, 0x3e, 0xcb, 0xc9, 0x38, 0x31, 0x35, 0x94, 0x92, 0x15, 0x0c, 0xd1, 0xa7, 0x30,
	0x4f, 0xee, 0xec, 0x8e, 0xfa, 0x6b, 0xec, 0x39, 0x2a, 0xe5, 0x47, 0x79, 0xf1, 0x4a, 0x76, 0x96,
	0x4e, 0x7c, 0x87, 0x3d, 0xe7, 0xd0, 0xe8, 0xb4, 0xb1, 0x2f, 0xff, 0x8b, 0xc4, 0x5a, 0xa2, 0x31,
	0x8e, 0xd9, 0x65, 0xa9, 0x88, 0xb4, 0x42, 0x3f, 0xd2, 0x12, 0xca, 0x16, 0xff, 0x0f, 0x95, 0x9d,
	0xc8, 0x54, 0x76, 0x09, 0x16, 0xd3, 0xba, 0x12, 0x57, 0xbe, 0x82, 0x15, 0xea, 0x53, 0xa2, 0x08,
	0xec, 0x9f, 0xb7, 0xe2, 0xbc, 0xcf, 0xb9, 0x68, 0x66, 0x3f, 0xee, 0x3e, 0x86, 0xb2, 0x6f, 0x58,
	0x58, 0x75, 0x2e, 0x2e, 0xf8, 0xa2, 0x58, 0x73, 0x9b, 0x82, 0x8e, 0x19, 0x44, 0xfe, 0x07, 0x09,
	0x1e, 0xe6, 0x4b, 0x23, 0x2e, 0x3a, 0x81, 0x19, 0xb1, 0xf6, 0xa0, 0x7a, 0x79, 0x99, 0xb4, 0xc9,
	0x20, 0xfa, 0x0d, 0x81, 0xa4, 0x84, 0x5c, 0x1a, 0x9b, 0x61, 0xb7, 0x31, 0xf5, 0x2d, 0xcf, 0x22,
	0x4c, 0x92, 0x8e, 0xe3, 0x85, 0x55, 0x00, 0x1b, 0xac, 0x5f, 0x02, 0x4a, 0x3f, 0xe6, 0xa2, 0x3a,
	0x2c, 0xee, 0xb6, 0xda, 0x27, 0x07, 0xdb, 0xef, 0xd4, 0xed, 0x53, 0xf5, 0x70, 0xbb, 0x75, 0xa4,
	0x9e, 0x6c, 0xef, 0x37, 0x6b, 0xf7, 0x50, 0x15, 0x4a, 0xbb, 0xad, 0xf6, 0x61, 0xab, 0xdd, 0x6e,
	0xee, 0xd6, 0x24, 0xd4, 0x80, 0xa5, 0x08, 0xe2, 0x5e, 0xeb, 0x68, 0x57, 0xdd, 0x53, 0x5a, 0xcd,
	0xa3, 0xdd, 0x5a, 0x81, 0xa2, 0x9e, 0x36, 0x0f, 0x4f, 0x8e, 0x95, 0x6d, 0xe5, 0x5d, 0xad, 0xb8,
	0xfe, 0x16, 0xca, 0x91, 0x57, 0x7e, 0x54, 0x86, 0xe9, 0x33, 0xfb, 0xca, 0x76, 0x3e, 0xd8, 0xb5,
	0x7b, 0xa8, 0x04, 0x93, 0x6d, 0x7c, 0x83, 0xbd, 0x9a, 0x84, 0x00, 0xa6, 0x5e, 0xd1, 0xda, 0xce,
	0xe4, 0x1c, 0xf8, 0xf3, 0xe8, 0xa9, 0x76, 0x5e, 0x2b, 0xa2, 0x19, 0x98, 0x78, 0xd3, 0x33, 0xfc,
	0xda, 0x04, 0x45, 0xe2, 0x2f, 0x0d, 0xb5, 0xc9, 0xf5, 0x17, 0x50, 0x8e, 0x74, 0xce, 0xd1, 0x3c,
	0x54, 0x5f, 0x79, 0x58, 0xf3, 0xb1, 0x00, 0xd6, 0xee, 0x51, 0x10, 0xcf, 0x87, 0x01, 0x48, 0x5a,
	0xff, 0x6d, 0x21, 0xde, 0xda, 0x0a, 0xbb, 0x86, 0xcb, 0xb0, 0x10, 0x1d, 0xb7, 0xec, 0x1b, 0xcd,
	0x34, 0xf4, 0xda, 0x3d, 0xf4, 0x10, 0xea, 0xd1, 0x89, 0x57, 0x91, 0x36, 0x1f, 0xb7, 0x43, 0xc6,
	0x2c, 0x55, 0xbb, 0x80, 0x56, 0x60, 0x39, 0x3a, 0x17, 0x5e, 0x40, 0x6c, 0xbd, 0x56, 0x4c, 0x4e,
	0xbe, 0xea, 0x77, 0x1c, 0x6b, 0x13, 0xe8, 0x23, 0x68, 0x44, 0x27, 0x99, 0xb0, 0x56, 0xd0, 0x3f,
	0xab, 0x4d, 0xa2, 0x47, 0xf0, 0x20, 0x3a, 0xcf, 0x1b, 0xda, 0xc1, 0xf4, 0x14, 0xfa, 0x14, 0xfe,
	0x20, 0x3a, 0x4d, 0x6d, 0x77, 0x66, 0x1b, 0xb6, 0x8f, 0x3d, 0x4c, 0x7c, 0xac, 0xf7, 0x51, 0xa7,
	0xd7, 0x37, 0xa0, 0x96, 0x6c, 0x1c, 0x51, 0x73, 0xef, 0x29, 0xcd, 0x26, 0x6f, 0x87, 0xb5, 0x5b,
	0x87, 0x27, 0x07, 0x4d, 0xf5, 0xb0, 0x75, 0x74, 0xac, 0xd4, 0xa4, 0xf5, 0x37, 0x50, 0x0a, 0x3b,
	0x16, 0xe8, 0x3e, 0xcc, 0x47, 0x7b, 0x14, 0x6c, 0xa2, 0x76, 0x8f, 0x3a, 0xd1, 0xe1, 0x1d, 0xf1,
	0xb1, 0x77, 0x17, 0x9f, 0x91, 0xd0, 0x02, 0xcc, 0x9d, 0xbc, 0x8a, 0x03, 0x0b, 0x5b, 0xff, 0xb8,
	0x02, 0xd5, 0xd8, 0xe5, 0x1c, 0xfd, 0xa9, 0xe8, 0xd3, 0xe4, 0x7c, 0x1a, 0xb4, 0x91, 0x8c, 0x95,
	0xc1, 0xdf, 0x2c, 0x35, 0x36, 0xc7, 0xc2, 0x27, 0x2e, 0x32, 0xd8, 0x8b, 0x65, 0xea, 0x5b, 0x13,
	0xf4, 0x49, 0xce, 0xd7, 0x2b, 0xc9, 0x6f, 0x65, 0x1a, 0x6b, 0xa3, 0x21, 0x72, 0x51, 0x59, 0x1f,
	0x82, 0xa4, 0x45, 0xe5, 0x7c, 0x91, 0x92, 0x16, 0x95, 0xfb, 0x5d, 0x89, 0x09, 0xf7, 0x33, 0x3f,
	0xc1, 0x40, 0x43, 0x59, 0x04, 0x1f, 0x8c, 0x34, 0x3e, 0x1d, 0x11, 0x93, 0xb8, 0xe8, 0x7b, 0xa8,
	0x25, 0xbf, 0x70, 0x40, 0x4f, 0x53, 0x1b, 0x91, 0xfe, 0x6c, 0xa3, 0xf1, 0x6c, 0x38, 0x12, 0x71,
	0xd1, 0x29, 0x54, 0x63, 0x6f, 0xbb, 0x68, 0x35, 0x43, 0xb5, 0xd8, 0xd3, 0x71, 0xe3, 0xc9, 0x10,
	0x0c, 0xe2, 0xa2, 0x5e, 0xb4, 0xbb, 0x1b, 0x7f, 0x5b, 0x42, 0x9f, 0x8d, 0xfe, 0x0a, 0x75, 0xdd,
	0xf8, 0x7c, 0x9c, 0x27, 0x2b, 0x74, 0x01, 0x0b, 0x19, 0xcf, 0x3c, 0x28, 0xd5, 0xc2, 0xca, 0x7e,
	0x69, 0x6a, 0x7c, 0x32, 0x12, 0x1e, 0xdf, 0x93, 0x64, 0x5b, 0x3f, 0xbd, 0x27, 0x19, 0x8f, 0x4a,
	0x8d, 0x67, 0xc3, 0x91, 0x38, 0xfb, 0x64, 0x9f, 0x3f, 0xcd, 0x3e, 0xe3, 0xfd, 0x20, 0xcd, 0x3e,
	0xeb, 0xb9, 0x40, 0x84, 0x4a, 0xaa, 0xf5, 0x9e, 0x19, 0x2a, 0x59, 0x2f, 0x00, 0x99, 0xa1, 0x92,
	0xdd, 0xc9, 0xef, 0x89, 0x76, 0x60, 0x46, 0xff, 0x35, 0xd3, 0x0f, 0xf2, 0x5a, 0xca, 0x99, 0x7e,
	0x90, 0xdf, 0x46, 0xef, 0x89, 0x26, 0xe0, 0x48, 0x62, 0xdb, 0xe3, 0x88, 0x1d, 0xd4, 0x4d, 0xce,
	0x4a, 0xb7, 0x91, 0xc6, 0xef, 0xf0, 0x74, 0x1b, 0x6f, 0x3d, 0x0f, 0x4f, 0xb7, 0x89, 0xae, 0x32,
	0xba, 0x4d, 0x7f, 0x1b, 0xd6, 0x6f, 0x2c, 0x7f, 0x3e, 0x2c, 0xe5, 0x44, 0x7b, 0xc8, 0x8d, 0xe7,
	0x63, 0x60, 0x27, 0x52, 0x62, 0xb4, 0xdf, 0x39, 0x20, 0x25, 0x26, 0x9a, 0xb3, 0x03, 0x52, 0x62,
	0xaa, 0xf5, 0x6b, 0xc2, 0xfd, 0xf6, 0x68, 0xd2, 0xda, 0x23, 0x4b, 0xcb, 0x6d, 0xd7, 0xa2, 0x3f,
	0x93, 0x22, 0xfd, 0xf6, 0x8c, 0xe6, 0x27, 0xda, 0xcc, 0xd5, 0x3c, 0xbb, 0xbf, 0xda, 0xf8, 0x62,
	0x3c, 0x02, 0xa1, 0x43, 0x7b, 0x2c, 0x1d, 0xda, 0xe3, 0xea, 0x30, 0xac, 0xbf, 0x8b, 0x1c, 0x76,
	0x7f, 0xcb, 0x68, 0x22, 0xa2, 0xac, 0xad, 0xcb, 0x6e, 0x5d, 0x36, 0xd6, 0x47, 0x45, 0xe5, 0x02,
	0xdb, 0x23, 0x0a, 0x6c, 0x8f, 0x2e, 0x30, 0xbf, 0xe9, 0x8a, 0x7e, 0x2b, 0xb1, 0x26, 0xfb, 0xc0,
	0x56, 0x22, 0x7a, 0x31, 0x7e, 0xf3, 0xf1, 0xba, 0xf1, 0xf2, 0x87, 0x74, 0x2c, 0xd1, 0x5f, 0x4b,
	0xf0, 0x71, 0x06, 0xa2, 0xa2, 0xd9, 0xba, 0xf8, 0xcc, 0xea, 0xf7, 0xa1, 0x15, 0xf5, 0xc5, 0x41,
	0xed, 0xaf, 0xb4, 0x2f, 0x0e, 0xe9, 0xc6, 0xa5, 0x7d, 0x71, 0x58, 0x77, 0x4d, 0x64, 0xba, 0x1c,
	0xf9, 0x59, 0x99, 0x2e, 0x5f, 0xf8, 0xf3, 0x31, 0xb0, 0x89, 0x8b, 0x7e, 0x23, 0xc1, 0xa3, 0x81,
	0xbd, 0x21, 0xf4, 0xc5, 0xb8, 0x7d, 0xae, 0xc6, 0x4f, 0xc6, 0xa4, 0x20, 0x2e, 0xfa, 0x13, 0xd6,
	0xd1, 0xcf, 0x53, 0xe1, 0xf9, 0x38, 0x0d, 0xa1, 0xeb, 0xc6, 0xc6, 0x78, 0xfd, 0x23, 0x5a, 0x66,
	0x65, 0x34, 0x55, 0xd2, 0x65, 0x56, 0x76, 0xbb, 0xa7, 0xf1, 0xc9, 0x48, 0x78, 0xa2, 0xf4, 0x4d,
	0x4c, 0x65, 0x94, 0xbe, 0xe9, 0x8e, 0x4a, 0x46, 0xe9, 0x9b, 0xd1, 0xca, 0xa0, 0x55, 0x42, 0x5e,
	0x73, 0x20, 0x5d, 0x25, 0x0c, 0x68, 0x7a, 0xa4, 0xab, 0x84, 0x41, 0x3d, 0x87, 0x9d, 0xad, 0xef,
	0xbe, 0xe8, 0x3a, 0xa6, 0x66, 0x77, 0x37, 0xfe, 0x70, 0xcb, 0xf7, 0x37, 0x3a, 0x8e, 0xb5, 0xc9,
	0xfe, 0x71, 0xd5, 0x71, 0xcc, 0x4d, 0x82, 0xbd, 0x1b, 0xa3, 0x83, 0x49, 0xe2, 0x2f, 0x59, 0xe7,
	0x53, 0x0c, 0xe3, 0xc5, 0xff, 0x06, 0x00, 0x00, 0xff, 0xff, 0x1f, 0xaa, 0x4a, 0x8a, 0xc9, 0x35,
	0x00, 0x00,
}
