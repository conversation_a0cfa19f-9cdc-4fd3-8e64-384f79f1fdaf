// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/game-user-rate/game-user-rate.proto

package game_user_rate // import "golang.52tt.com/protocol/services/game-user-rate"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GameUserRateSourceType int32

const (
	GameUserRateSourceType_GAME_USER_RATE_SOURCE_TYPE_UNSPECIFIED    GameUserRateSourceType = 0
	GameUserRateSourceType_GAME_USER_RATE_SOURCE_TYPE_CHANNEL        GameUserRateSourceType = 1
	GameUserRateSourceType_GAME_USER_RATE_SOURCE_TYPE_IM_CHAT        GameUserRateSourceType = 2
	GameUserRateSourceType_GAME_USER_RATE_SOURCE_TYPE_ASSISTANT_PUSH GameUserRateSourceType = 3
	GameUserRateSourceType_GAME_USER_RATE_SOURCE_TYPE_CHANNEL_LETTER GameUserRateSourceType = 4
)

var GameUserRateSourceType_name = map[int32]string{
	0: "GAME_USER_RATE_SOURCE_TYPE_UNSPECIFIED",
	1: "GAME_USER_RATE_SOURCE_TYPE_CHANNEL",
	2: "GAME_USER_RATE_SOURCE_TYPE_IM_CHAT",
	3: "GAME_USER_RATE_SOURCE_TYPE_ASSISTANT_PUSH",
	4: "GAME_USER_RATE_SOURCE_TYPE_CHANNEL_LETTER",
}
var GameUserRateSourceType_value = map[string]int32{
	"GAME_USER_RATE_SOURCE_TYPE_UNSPECIFIED":    0,
	"GAME_USER_RATE_SOURCE_TYPE_CHANNEL":        1,
	"GAME_USER_RATE_SOURCE_TYPE_IM_CHAT":        2,
	"GAME_USER_RATE_SOURCE_TYPE_ASSISTANT_PUSH": 3,
	"GAME_USER_RATE_SOURCE_TYPE_CHANNEL_LETTER": 4,
}

func (x GameUserRateSourceType) String() string {
	return proto.EnumName(GameUserRateSourceType_name, int32(x))
}
func (GameUserRateSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{0}
}

type GameUserRateStatus int32

const (
	GameUserRateStatus_GAME_USER_RATE_STATUS_UNSPECIFIED GameUserRateStatus = 0
	// 未评价
	GameUserRateStatus_GAME_USER_RATE_STATUS_NOT_RATE GameUserRateStatus = 1
	// 已评价
	GameUserRateStatus_GAME_USER_RATE_STATUS_RATED GameUserRateStatus = 2
	// 已过期
	GameUserRateStatus_GAME_USER_RATE_STATUS_EXPIRED GameUserRateStatus = 3
)

var GameUserRateStatus_name = map[int32]string{
	0: "GAME_USER_RATE_STATUS_UNSPECIFIED",
	1: "GAME_USER_RATE_STATUS_NOT_RATE",
	2: "GAME_USER_RATE_STATUS_RATED",
	3: "GAME_USER_RATE_STATUS_EXPIRED",
}
var GameUserRateStatus_value = map[string]int32{
	"GAME_USER_RATE_STATUS_UNSPECIFIED": 0,
	"GAME_USER_RATE_STATUS_NOT_RATE":    1,
	"GAME_USER_RATE_STATUS_RATED":       2,
	"GAME_USER_RATE_STATUS_EXPIRED":     3,
}

func (x GameUserRateStatus) String() string {
	return proto.EnumName(GameUserRateStatus_name, int32(x))
}
func (GameUserRateStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{1}
}

type GameUserRateAddTagType int32

const (
	GameUserRateAddTagType_GAME_USER_RATE_ADD_TAG_TYPE_UNSPECIFIED GameUserRateAddTagType = 0
	// 房间标签
	GameUserRateAddTagType_GAME_USER_RATE_ADD_TAG_TYPE_CHANNEL GameUserRateAddTagType = 1
	// 搭子标签
	GameUserRateAddTagType_GAME_USER_RATE_ADD_TAG_TYPE_GAME_PAL GameUserRateAddTagType = 2
)

var GameUserRateAddTagType_name = map[int32]string{
	0: "GAME_USER_RATE_ADD_TAG_TYPE_UNSPECIFIED",
	1: "GAME_USER_RATE_ADD_TAG_TYPE_CHANNEL",
	2: "GAME_USER_RATE_ADD_TAG_TYPE_GAME_PAL",
}
var GameUserRateAddTagType_value = map[string]int32{
	"GAME_USER_RATE_ADD_TAG_TYPE_UNSPECIFIED": 0,
	"GAME_USER_RATE_ADD_TAG_TYPE_CHANNEL":     1,
	"GAME_USER_RATE_ADD_TAG_TYPE_GAME_PAL":    2,
}

func (x GameUserRateAddTagType) String() string {
	return proto.EnumName(GameUserRateAddTagType_name, int32(x))
}
func (GameUserRateAddTagType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{2}
}

type GetGameUserRateByIdReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameUserRateByIdReq) Reset()         { *m = GetGameUserRateByIdReq{} }
func (m *GetGameUserRateByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetGameUserRateByIdReq) ProtoMessage()    {}
func (*GetGameUserRateByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{0}
}
func (m *GetGameUserRateByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameUserRateByIdReq.Unmarshal(m, b)
}
func (m *GetGameUserRateByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameUserRateByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetGameUserRateByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameUserRateByIdReq.Merge(dst, src)
}
func (m *GetGameUserRateByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetGameUserRateByIdReq.Size(m)
}
func (m *GetGameUserRateByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameUserRateByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameUserRateByIdReq proto.InternalMessageInfo

func (m *GetGameUserRateByIdReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type GetGameUserRateByIdResp struct {
	Data                 *GameUserRateItem `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetGameUserRateByIdResp) Reset()         { *m = GetGameUserRateByIdResp{} }
func (m *GetGameUserRateByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetGameUserRateByIdResp) ProtoMessage()    {}
func (*GetGameUserRateByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{1}
}
func (m *GetGameUserRateByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameUserRateByIdResp.Unmarshal(m, b)
}
func (m *GetGameUserRateByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameUserRateByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetGameUserRateByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameUserRateByIdResp.Merge(dst, src)
}
func (m *GetGameUserRateByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetGameUserRateByIdResp.Size(m)
}
func (m *GetGameUserRateByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameUserRateByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameUserRateByIdResp proto.InternalMessageInfo

func (m *GetGameUserRateByIdResp) GetData() *GameUserRateItem {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetGameUserRateListReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 评价来源，互动提示信息可用于不同文案
	Source GameUserRateSourceType `protobuf:"varint,2,opt,name=source,proto3,enum=game_user_rate.GameUserRateSourceType" json:"source,omitempty"`
	// 用户id
	RateUid uint32 `protobuf:"varint,3,opt,name=rate_uid,json=rateUid,proto3" json:"rate_uid,omitempty"`
	// last id，分页用
	LastId               string   `protobuf:"bytes,4,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameUserRateListReq) Reset()         { *m = GetGameUserRateListReq{} }
func (m *GetGameUserRateListReq) String() string { return proto.CompactTextString(m) }
func (*GetGameUserRateListReq) ProtoMessage()    {}
func (*GetGameUserRateListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{2}
}
func (m *GetGameUserRateListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameUserRateListReq.Unmarshal(m, b)
}
func (m *GetGameUserRateListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameUserRateListReq.Marshal(b, m, deterministic)
}
func (dst *GetGameUserRateListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameUserRateListReq.Merge(dst, src)
}
func (m *GetGameUserRateListReq) XXX_Size() int {
	return xxx_messageInfo_GetGameUserRateListReq.Size(m)
}
func (m *GetGameUserRateListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameUserRateListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameUserRateListReq proto.InternalMessageInfo

func (m *GetGameUserRateListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGameUserRateListReq) GetSource() GameUserRateSourceType {
	if m != nil {
		return m.Source
	}
	return GameUserRateSourceType_GAME_USER_RATE_SOURCE_TYPE_UNSPECIFIED
}

func (m *GetGameUserRateListReq) GetRateUid() uint32 {
	if m != nil {
		return m.RateUid
	}
	return 0
}

func (m *GetGameUserRateListReq) GetLastId() string {
	if m != nil {
		return m.LastId
	}
	return ""
}

type GetGameUserRateListResp struct {
	// 评价详情数据
	Data []*GameUserRateItem `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	// 是否已经到底部，true为底部
	LoadFinish           bool     `protobuf:"varint,2,opt,name=load_finish,json=loadFinish,proto3" json:"load_finish,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameUserRateListResp) Reset()         { *m = GetGameUserRateListResp{} }
func (m *GetGameUserRateListResp) String() string { return proto.CompactTextString(m) }
func (*GetGameUserRateListResp) ProtoMessage()    {}
func (*GetGameUserRateListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{3}
}
func (m *GetGameUserRateListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameUserRateListResp.Unmarshal(m, b)
}
func (m *GetGameUserRateListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameUserRateListResp.Marshal(b, m, deterministic)
}
func (dst *GetGameUserRateListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameUserRateListResp.Merge(dst, src)
}
func (m *GetGameUserRateListResp) XXX_Size() int {
	return xxx_messageInfo_GetGameUserRateListResp.Size(m)
}
func (m *GetGameUserRateListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameUserRateListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameUserRateListResp proto.InternalMessageInfo

func (m *GetGameUserRateListResp) GetData() []*GameUserRateItem {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *GetGameUserRateListResp) GetLoadFinish() bool {
	if m != nil {
		return m.LoadFinish
	}
	return false
}

type GameUserRateItem struct {
	// id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 用户id
	Uid uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// 被评价用户id
	RateUid uint32 `protobuf:"varint,3,opt,name=rate_uid,json=rateUid,proto3" json:"rate_uid,omitempty"`
	// 玩法id
	TabId uint32 `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	// 来源
	Source GameUserRateSourceType `protobuf:"varint,5,opt,name=source,proto3,enum=game_user_rate.GameUserRateSourceType" json:"source,omitempty"`
	// 评价创建时间， ms
	CreateTime uint64 `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 交互提示信息，如：5小时前跟Ta一起玩过王者荣耀， 废弃
	IntroTips string `protobuf:"bytes,7,opt,name=intro_tips,json=introTips,proto3" json:"intro_tips,omitempty"`
	// 评论状态， 1-未评价，2-已评价，3-已过期
	Status GameUserRateStatus `protobuf:"varint,8,opt,name=status,proto3,enum=game_user_rate.GameUserRateStatus" json:"status,omitempty"`
	// 评价意向，赞或踩，0-未选，1-赞，2-踩
	Attitude uint32 `protobuf:"varint,9,opt,name=attitude,proto3" json:"attitude,omitempty"`
	// 评价标签
	Tags []*GameUserRateTag `protobuf:"bytes,10,rep,name=tags,proto3" json:"tags,omitempty"`
	// 用户自定义评价
	UserRateText         string   `protobuf:"bytes,11,opt,name=user_rate_text,json=userRateText,proto3" json:"user_rate_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameUserRateItem) Reset()         { *m = GameUserRateItem{} }
func (m *GameUserRateItem) String() string { return proto.CompactTextString(m) }
func (*GameUserRateItem) ProtoMessage()    {}
func (*GameUserRateItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{4}
}
func (m *GameUserRateItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameUserRateItem.Unmarshal(m, b)
}
func (m *GameUserRateItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameUserRateItem.Marshal(b, m, deterministic)
}
func (dst *GameUserRateItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameUserRateItem.Merge(dst, src)
}
func (m *GameUserRateItem) XXX_Size() int {
	return xxx_messageInfo_GameUserRateItem.Size(m)
}
func (m *GameUserRateItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameUserRateItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameUserRateItem proto.InternalMessageInfo

func (m *GameUserRateItem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GameUserRateItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameUserRateItem) GetRateUid() uint32 {
	if m != nil {
		return m.RateUid
	}
	return 0
}

func (m *GameUserRateItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GameUserRateItem) GetSource() GameUserRateSourceType {
	if m != nil {
		return m.Source
	}
	return GameUserRateSourceType_GAME_USER_RATE_SOURCE_TYPE_UNSPECIFIED
}

func (m *GameUserRateItem) GetCreateTime() uint64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GameUserRateItem) GetIntroTips() string {
	if m != nil {
		return m.IntroTips
	}
	return ""
}

func (m *GameUserRateItem) GetStatus() GameUserRateStatus {
	if m != nil {
		return m.Status
	}
	return GameUserRateStatus_GAME_USER_RATE_STATUS_UNSPECIFIED
}

func (m *GameUserRateItem) GetAttitude() uint32 {
	if m != nil {
		return m.Attitude
	}
	return 0
}

func (m *GameUserRateItem) GetTags() []*GameUserRateTag {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *GameUserRateItem) GetUserRateText() string {
	if m != nil {
		return m.UserRateText
	}
	return ""
}

// 标签详情
type GameUserRateTag struct {
	// 维度标签id，普通标签无
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 标签名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 1-正向标签，点赞展示；2-负向标签，踩展示
	TagType              uint32   `protobuf:"varint,3,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameUserRateTag) Reset()         { *m = GameUserRateTag{} }
func (m *GameUserRateTag) String() string { return proto.CompactTextString(m) }
func (*GameUserRateTag) ProtoMessage()    {}
func (*GameUserRateTag) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{5}
}
func (m *GameUserRateTag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameUserRateTag.Unmarshal(m, b)
}
func (m *GameUserRateTag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameUserRateTag.Marshal(b, m, deterministic)
}
func (dst *GameUserRateTag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameUserRateTag.Merge(dst, src)
}
func (m *GameUserRateTag) XXX_Size() int {
	return xxx_messageInfo_GameUserRateTag.Size(m)
}
func (m *GameUserRateTag) XXX_DiscardUnknown() {
	xxx_messageInfo_GameUserRateTag.DiscardUnknown(m)
}

var xxx_messageInfo_GameUserRateTag proto.InternalMessageInfo

func (m *GameUserRateTag) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GameUserRateTag) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GameUserRateTag) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

type SubmitGameUserRateReq struct {
	// 评价id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 评价意向，赞或踩，0-未选，1-赞，2-踩
	Attitude uint32 `protobuf:"varint,2,opt,name=attitude,proto3" json:"attitude,omitempty"`
	// 选择的评价标签
	SelectTags []*GameUserRateTag `protobuf:"bytes,3,rep,name=select_tags,json=selectTags,proto3" json:"select_tags,omitempty"`
	// 用户id
	Uid uint32 `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	// 用户自定义评价
	UserRateText string `protobuf:"bytes,5,opt,name=user_rate_text,json=userRateText,proto3" json:"user_rate_text,omitempty"`
	// T盾审核是否通过
	IsShieldPass bool `protobuf:"varint,6,opt,name=is_shield_pass,json=isShieldPass,proto3" json:"is_shield_pass,omitempty"`
	// 是否有风控风险
	HasRisk              bool     `protobuf:"varint,7,opt,name=has_risk,json=hasRisk,proto3" json:"has_risk,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitGameUserRateReq) Reset()         { *m = SubmitGameUserRateReq{} }
func (m *SubmitGameUserRateReq) String() string { return proto.CompactTextString(m) }
func (*SubmitGameUserRateReq) ProtoMessage()    {}
func (*SubmitGameUserRateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{6}
}
func (m *SubmitGameUserRateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitGameUserRateReq.Unmarshal(m, b)
}
func (m *SubmitGameUserRateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitGameUserRateReq.Marshal(b, m, deterministic)
}
func (dst *SubmitGameUserRateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitGameUserRateReq.Merge(dst, src)
}
func (m *SubmitGameUserRateReq) XXX_Size() int {
	return xxx_messageInfo_SubmitGameUserRateReq.Size(m)
}
func (m *SubmitGameUserRateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitGameUserRateReq.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitGameUserRateReq proto.InternalMessageInfo

func (m *SubmitGameUserRateReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SubmitGameUserRateReq) GetAttitude() uint32 {
	if m != nil {
		return m.Attitude
	}
	return 0
}

func (m *SubmitGameUserRateReq) GetSelectTags() []*GameUserRateTag {
	if m != nil {
		return m.SelectTags
	}
	return nil
}

func (m *SubmitGameUserRateReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SubmitGameUserRateReq) GetUserRateText() string {
	if m != nil {
		return m.UserRateText
	}
	return ""
}

func (m *SubmitGameUserRateReq) GetIsShieldPass() bool {
	if m != nil {
		return m.IsShieldPass
	}
	return false
}

func (m *SubmitGameUserRateReq) GetHasRisk() bool {
	if m != nil {
		return m.HasRisk
	}
	return false
}

type SubmitGameUserRateResp struct {
	// 是否需要加经验
	IsNeedAddExp bool `protobuf:"varint,1,opt,name=is_need_add_exp,json=isNeedAddExp,proto3" json:"is_need_add_exp,omitempty"`
	// 成功评价后的信息
	Item *GameUserRateItem `protobuf:"bytes,2,opt,name=item,proto3" json:"item,omitempty"`
	// 是否需要给被评价用户加经验
	IsRateUserNeedAddExp bool     `protobuf:"varint,3,opt,name=is_rate_user_need_add_exp,json=isRateUserNeedAddExp,proto3" json:"is_rate_user_need_add_exp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitGameUserRateResp) Reset()         { *m = SubmitGameUserRateResp{} }
func (m *SubmitGameUserRateResp) String() string { return proto.CompactTextString(m) }
func (*SubmitGameUserRateResp) ProtoMessage()    {}
func (*SubmitGameUserRateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{7}
}
func (m *SubmitGameUserRateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitGameUserRateResp.Unmarshal(m, b)
}
func (m *SubmitGameUserRateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitGameUserRateResp.Marshal(b, m, deterministic)
}
func (dst *SubmitGameUserRateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitGameUserRateResp.Merge(dst, src)
}
func (m *SubmitGameUserRateResp) XXX_Size() int {
	return xxx_messageInfo_SubmitGameUserRateResp.Size(m)
}
func (m *SubmitGameUserRateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitGameUserRateResp.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitGameUserRateResp proto.InternalMessageInfo

func (m *SubmitGameUserRateResp) GetIsNeedAddExp() bool {
	if m != nil {
		return m.IsNeedAddExp
	}
	return false
}

func (m *SubmitGameUserRateResp) GetItem() *GameUserRateItem {
	if m != nil {
		return m.Item
	}
	return nil
}

func (m *SubmitGameUserRateResp) GetIsRateUserNeedAddExp() bool {
	if m != nil {
		return m.IsRateUserNeedAddExp
	}
	return false
}

type GetTabByDimensionIdsReq struct {
	// 维度id
	Ids                  []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTabByDimensionIdsReq) Reset()         { *m = GetTabByDimensionIdsReq{} }
func (m *GetTabByDimensionIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetTabByDimensionIdsReq) ProtoMessage()    {}
func (*GetTabByDimensionIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{8}
}
func (m *GetTabByDimensionIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabByDimensionIdsReq.Unmarshal(m, b)
}
func (m *GetTabByDimensionIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabByDimensionIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetTabByDimensionIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabByDimensionIdsReq.Merge(dst, src)
}
func (m *GetTabByDimensionIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetTabByDimensionIdsReq.Size(m)
}
func (m *GetTabByDimensionIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabByDimensionIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabByDimensionIdsReq proto.InternalMessageInfo

func (m *GetTabByDimensionIdsReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type GetTabByDimensionIdsItem struct {
	TabIds               []uint32 `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTabByDimensionIdsItem) Reset()         { *m = GetTabByDimensionIdsItem{} }
func (m *GetTabByDimensionIdsItem) String() string { return proto.CompactTextString(m) }
func (*GetTabByDimensionIdsItem) ProtoMessage()    {}
func (*GetTabByDimensionIdsItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{9}
}
func (m *GetTabByDimensionIdsItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabByDimensionIdsItem.Unmarshal(m, b)
}
func (m *GetTabByDimensionIdsItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabByDimensionIdsItem.Marshal(b, m, deterministic)
}
func (dst *GetTabByDimensionIdsItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabByDimensionIdsItem.Merge(dst, src)
}
func (m *GetTabByDimensionIdsItem) XXX_Size() int {
	return xxx_messageInfo_GetTabByDimensionIdsItem.Size(m)
}
func (m *GetTabByDimensionIdsItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabByDimensionIdsItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabByDimensionIdsItem proto.InternalMessageInfo

func (m *GetTabByDimensionIdsItem) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type GetTabByDimensionIdsResp struct {
	// k:维度id, v:玩法id
	DimensionTabMap      map[string]*GetTabByDimensionIdsItem `protobuf:"bytes,1,rep,name=dimension_tab_map,json=dimensionTabMap,proto3" json:"dimension_tab_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *GetTabByDimensionIdsResp) Reset()         { *m = GetTabByDimensionIdsResp{} }
func (m *GetTabByDimensionIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetTabByDimensionIdsResp) ProtoMessage()    {}
func (*GetTabByDimensionIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{10}
}
func (m *GetTabByDimensionIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabByDimensionIdsResp.Unmarshal(m, b)
}
func (m *GetTabByDimensionIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabByDimensionIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetTabByDimensionIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabByDimensionIdsResp.Merge(dst, src)
}
func (m *GetTabByDimensionIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetTabByDimensionIdsResp.Size(m)
}
func (m *GetTabByDimensionIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabByDimensionIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabByDimensionIdsResp proto.InternalMessageInfo

func (m *GetTabByDimensionIdsResp) GetDimensionTabMap() map[string]*GetTabByDimensionIdsItem {
	if m != nil {
		return m.DimensionTabMap
	}
	return nil
}

type AddGameUserRateReq struct {
	Uid                  uint32                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Items                []*AddGameUserRateItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AddGameUserRateReq) Reset()         { *m = AddGameUserRateReq{} }
func (m *AddGameUserRateReq) String() string { return proto.CompactTextString(m) }
func (*AddGameUserRateReq) ProtoMessage()    {}
func (*AddGameUserRateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{11}
}
func (m *AddGameUserRateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddGameUserRateReq.Unmarshal(m, b)
}
func (m *AddGameUserRateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddGameUserRateReq.Marshal(b, m, deterministic)
}
func (dst *AddGameUserRateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddGameUserRateReq.Merge(dst, src)
}
func (m *AddGameUserRateReq) XXX_Size() int {
	return xxx_messageInfo_AddGameUserRateReq.Size(m)
}
func (m *AddGameUserRateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddGameUserRateReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddGameUserRateReq proto.InternalMessageInfo

func (m *AddGameUserRateReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddGameUserRateReq) GetItems() []*AddGameUserRateItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type AddGameUserRateItem struct {
	// see GameUserRateSourceType
	Source uint32 `protobuf:"varint,1,opt,name=source,proto3" json:"source,omitempty"`
	// 被评价用户id
	RateUid uint32 `protobuf:"varint,2,opt,name=rate_uid,json=rateUid,proto3" json:"rate_uid,omitempty"`
	// 触发時間,ms
	InteractTime uint64 `protobuf:"varint,3,opt,name=interact_time,json=interactTime,proto3" json:"interact_time,omitempty"`
	// 玩法id
	TabId uint32 `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	// 需要添加的标签类型
	AddTagType           []GameUserRateAddTagType `protobuf:"varint,5,rep,packed,name=add_tag_type,json=addTagType,proto3,enum=game_user_rate.GameUserRateAddTagType" json:"add_tag_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *AddGameUserRateItem) Reset()         { *m = AddGameUserRateItem{} }
func (m *AddGameUserRateItem) String() string { return proto.CompactTextString(m) }
func (*AddGameUserRateItem) ProtoMessage()    {}
func (*AddGameUserRateItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{12}
}
func (m *AddGameUserRateItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddGameUserRateItem.Unmarshal(m, b)
}
func (m *AddGameUserRateItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddGameUserRateItem.Marshal(b, m, deterministic)
}
func (dst *AddGameUserRateItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddGameUserRateItem.Merge(dst, src)
}
func (m *AddGameUserRateItem) XXX_Size() int {
	return xxx_messageInfo_AddGameUserRateItem.Size(m)
}
func (m *AddGameUserRateItem) XXX_DiscardUnknown() {
	xxx_messageInfo_AddGameUserRateItem.DiscardUnknown(m)
}

var xxx_messageInfo_AddGameUserRateItem proto.InternalMessageInfo

func (m *AddGameUserRateItem) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *AddGameUserRateItem) GetRateUid() uint32 {
	if m != nil {
		return m.RateUid
	}
	return 0
}

func (m *AddGameUserRateItem) GetInteractTime() uint64 {
	if m != nil {
		return m.InteractTime
	}
	return 0
}

func (m *AddGameUserRateItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *AddGameUserRateItem) GetAddTagType() []GameUserRateAddTagType {
	if m != nil {
		return m.AddTagType
	}
	return nil
}

type AddGameUserRateResp struct {
	Items                []*AddGameUserRateItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AddGameUserRateResp) Reset()         { *m = AddGameUserRateResp{} }
func (m *AddGameUserRateResp) String() string { return proto.CompactTextString(m) }
func (*AddGameUserRateResp) ProtoMessage()    {}
func (*AddGameUserRateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{13}
}
func (m *AddGameUserRateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddGameUserRateResp.Unmarshal(m, b)
}
func (m *AddGameUserRateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddGameUserRateResp.Marshal(b, m, deterministic)
}
func (dst *AddGameUserRateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddGameUserRateResp.Merge(dst, src)
}
func (m *AddGameUserRateResp) XXX_Size() int {
	return xxx_messageInfo_AddGameUserRateResp.Size(m)
}
func (m *AddGameUserRateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddGameUserRateResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddGameUserRateResp proto.InternalMessageInfo

func (m *AddGameUserRateResp) GetItems() []*AddGameUserRateItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type CheckAddGameUserRateReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SelfDeviceId         string   `protobuf:"bytes,2,opt,name=self_device_id,json=selfDeviceId,proto3" json:"self_device_id,omitempty"`
	RateUids             []uint32 `protobuf:"varint,3,rep,packed,name=rate_uids,json=rateUids,proto3" json:"rate_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckAddGameUserRateReq) Reset()         { *m = CheckAddGameUserRateReq{} }
func (m *CheckAddGameUserRateReq) String() string { return proto.CompactTextString(m) }
func (*CheckAddGameUserRateReq) ProtoMessage()    {}
func (*CheckAddGameUserRateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{14}
}
func (m *CheckAddGameUserRateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckAddGameUserRateReq.Unmarshal(m, b)
}
func (m *CheckAddGameUserRateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckAddGameUserRateReq.Marshal(b, m, deterministic)
}
func (dst *CheckAddGameUserRateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckAddGameUserRateReq.Merge(dst, src)
}
func (m *CheckAddGameUserRateReq) XXX_Size() int {
	return xxx_messageInfo_CheckAddGameUserRateReq.Size(m)
}
func (m *CheckAddGameUserRateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckAddGameUserRateReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckAddGameUserRateReq proto.InternalMessageInfo

func (m *CheckAddGameUserRateReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckAddGameUserRateReq) GetSelfDeviceId() string {
	if m != nil {
		return m.SelfDeviceId
	}
	return ""
}

func (m *CheckAddGameUserRateReq) GetRateUids() []uint32 {
	if m != nil {
		return m.RateUids
	}
	return nil
}

type CheckAddGameUserRateResp struct {
	// key:rate_uid, v:是否可以添加评价
	BaseFilter map[uint32]bool `protobuf:"bytes,1,rep,name=base_filter,json=baseFilter,proto3" json:"base_filter,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// key:rate_uid, v:是否可以添加房间标签
	ChannelTagsFilter map[uint32]bool `protobuf:"bytes,2,rep,name=channel_tags_filter,json=channelTagsFilter,proto3" json:"channel_tags_filter,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// key:rate_uid, v:是否可以添加搭子标签
	GamePalTagsFilter    map[uint32]bool `protobuf:"bytes,3,rep,name=game_pal_tags_filter,json=gamePalTagsFilter,proto3" json:"game_pal_tags_filter,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CheckAddGameUserRateResp) Reset()         { *m = CheckAddGameUserRateResp{} }
func (m *CheckAddGameUserRateResp) String() string { return proto.CompactTextString(m) }
func (*CheckAddGameUserRateResp) ProtoMessage()    {}
func (*CheckAddGameUserRateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{15}
}
func (m *CheckAddGameUserRateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckAddGameUserRateResp.Unmarshal(m, b)
}
func (m *CheckAddGameUserRateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckAddGameUserRateResp.Marshal(b, m, deterministic)
}
func (dst *CheckAddGameUserRateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckAddGameUserRateResp.Merge(dst, src)
}
func (m *CheckAddGameUserRateResp) XXX_Size() int {
	return xxx_messageInfo_CheckAddGameUserRateResp.Size(m)
}
func (m *CheckAddGameUserRateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckAddGameUserRateResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckAddGameUserRateResp proto.InternalMessageInfo

func (m *CheckAddGameUserRateResp) GetBaseFilter() map[uint32]bool {
	if m != nil {
		return m.BaseFilter
	}
	return nil
}

func (m *CheckAddGameUserRateResp) GetChannelTagsFilter() map[uint32]bool {
	if m != nil {
		return m.ChannelTagsFilter
	}
	return nil
}

func (m *CheckAddGameUserRateResp) GetGamePalTagsFilter() map[uint32]bool {
	if m != nil {
		return m.GamePalTagsFilter
	}
	return nil
}

type BatchSetFirstChatTokenReq struct {
	Items                []*SetFirstChatTokenReqItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *BatchSetFirstChatTokenReq) Reset()         { *m = BatchSetFirstChatTokenReq{} }
func (m *BatchSetFirstChatTokenReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetFirstChatTokenReq) ProtoMessage()    {}
func (*BatchSetFirstChatTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{16}
}
func (m *BatchSetFirstChatTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetFirstChatTokenReq.Unmarshal(m, b)
}
func (m *BatchSetFirstChatTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetFirstChatTokenReq.Marshal(b, m, deterministic)
}
func (dst *BatchSetFirstChatTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetFirstChatTokenReq.Merge(dst, src)
}
func (m *BatchSetFirstChatTokenReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetFirstChatTokenReq.Size(m)
}
func (m *BatchSetFirstChatTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetFirstChatTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetFirstChatTokenReq proto.InternalMessageInfo

func (m *BatchSetFirstChatTokenReq) GetItems() []*SetFirstChatTokenReqItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type SetFirstChatTokenReqItem struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RateUid              uint32   `protobuf:"varint,2,opt,name=rate_uid,json=rateUid,proto3" json:"rate_uid,omitempty"`
	TabId                uint32   `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetFirstChatTokenReqItem) Reset()         { *m = SetFirstChatTokenReqItem{} }
func (m *SetFirstChatTokenReqItem) String() string { return proto.CompactTextString(m) }
func (*SetFirstChatTokenReqItem) ProtoMessage()    {}
func (*SetFirstChatTokenReqItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{17}
}
func (m *SetFirstChatTokenReqItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetFirstChatTokenReqItem.Unmarshal(m, b)
}
func (m *SetFirstChatTokenReqItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetFirstChatTokenReqItem.Marshal(b, m, deterministic)
}
func (dst *SetFirstChatTokenReqItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetFirstChatTokenReqItem.Merge(dst, src)
}
func (m *SetFirstChatTokenReqItem) XXX_Size() int {
	return xxx_messageInfo_SetFirstChatTokenReqItem.Size(m)
}
func (m *SetFirstChatTokenReqItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SetFirstChatTokenReqItem.DiscardUnknown(m)
}

var xxx_messageInfo_SetFirstChatTokenReqItem proto.InternalMessageInfo

func (m *SetFirstChatTokenReqItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetFirstChatTokenReqItem) GetRateUid() uint32 {
	if m != nil {
		return m.RateUid
	}
	return 0
}

func (m *SetFirstChatTokenReqItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type BatchSetFirstChatTokenResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetFirstChatTokenResp) Reset()         { *m = BatchSetFirstChatTokenResp{} }
func (m *BatchSetFirstChatTokenResp) String() string { return proto.CompactTextString(m) }
func (*BatchSetFirstChatTokenResp) ProtoMessage()    {}
func (*BatchSetFirstChatTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{18}
}
func (m *BatchSetFirstChatTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetFirstChatTokenResp.Unmarshal(m, b)
}
func (m *BatchSetFirstChatTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetFirstChatTokenResp.Marshal(b, m, deterministic)
}
func (dst *BatchSetFirstChatTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetFirstChatTokenResp.Merge(dst, src)
}
func (m *BatchSetFirstChatTokenResp) XXX_Size() int {
	return xxx_messageInfo_BatchSetFirstChatTokenResp.Size(m)
}
func (m *BatchSetFirstChatTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetFirstChatTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetFirstChatTokenResp proto.InternalMessageInfo

type BatchGetFirstChatTokenReq struct {
	Items                []*GetFirstChatTokenReqItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *BatchGetFirstChatTokenReq) Reset()         { *m = BatchGetFirstChatTokenReq{} }
func (m *BatchGetFirstChatTokenReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetFirstChatTokenReq) ProtoMessage()    {}
func (*BatchGetFirstChatTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{19}
}
func (m *BatchGetFirstChatTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetFirstChatTokenReq.Unmarshal(m, b)
}
func (m *BatchGetFirstChatTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetFirstChatTokenReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetFirstChatTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetFirstChatTokenReq.Merge(dst, src)
}
func (m *BatchGetFirstChatTokenReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetFirstChatTokenReq.Size(m)
}
func (m *BatchGetFirstChatTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetFirstChatTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetFirstChatTokenReq proto.InternalMessageInfo

func (m *BatchGetFirstChatTokenReq) GetItems() []*GetFirstChatTokenReqItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type GetFirstChatTokenReqItem struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RateUid              uint32   `protobuf:"varint,2,opt,name=rate_uid,json=rateUid,proto3" json:"rate_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFirstChatTokenReqItem) Reset()         { *m = GetFirstChatTokenReqItem{} }
func (m *GetFirstChatTokenReqItem) String() string { return proto.CompactTextString(m) }
func (*GetFirstChatTokenReqItem) ProtoMessage()    {}
func (*GetFirstChatTokenReqItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{20}
}
func (m *GetFirstChatTokenReqItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFirstChatTokenReqItem.Unmarshal(m, b)
}
func (m *GetFirstChatTokenReqItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFirstChatTokenReqItem.Marshal(b, m, deterministic)
}
func (dst *GetFirstChatTokenReqItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFirstChatTokenReqItem.Merge(dst, src)
}
func (m *GetFirstChatTokenReqItem) XXX_Size() int {
	return xxx_messageInfo_GetFirstChatTokenReqItem.Size(m)
}
func (m *GetFirstChatTokenReqItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFirstChatTokenReqItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetFirstChatTokenReqItem proto.InternalMessageInfo

func (m *GetFirstChatTokenReqItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFirstChatTokenReqItem) GetRateUid() uint32 {
	if m != nil {
		return m.RateUid
	}
	return 0
}

type BatchGetFirstChatTokenResp struct {
	// key:uid_rateUid, value:tabId
	Items                map[string]uint32 `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetFirstChatTokenResp) Reset()         { *m = BatchGetFirstChatTokenResp{} }
func (m *BatchGetFirstChatTokenResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetFirstChatTokenResp) ProtoMessage()    {}
func (*BatchGetFirstChatTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{21}
}
func (m *BatchGetFirstChatTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetFirstChatTokenResp.Unmarshal(m, b)
}
func (m *BatchGetFirstChatTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetFirstChatTokenResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetFirstChatTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetFirstChatTokenResp.Merge(dst, src)
}
func (m *BatchGetFirstChatTokenResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetFirstChatTokenResp.Size(m)
}
func (m *BatchGetFirstChatTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetFirstChatTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetFirstChatTokenResp proto.InternalMessageInfo

func (m *BatchGetFirstChatTokenResp) GetItems() map[string]uint32 {
	if m != nil {
		return m.Items
	}
	return nil
}

type GetGameUserNotRateCountReq struct {
	// 用户id
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 最新已读评价的id, 6.49添加
	RateId string `protobuf:"bytes,2,opt,name=rate_id,json=rateId,proto3" json:"rate_id,omitempty"`
	// 最新已读评价的创建时间戳, 6.49添加
	CreateTime           uint64   `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameUserNotRateCountReq) Reset()         { *m = GetGameUserNotRateCountReq{} }
func (m *GetGameUserNotRateCountReq) String() string { return proto.CompactTextString(m) }
func (*GetGameUserNotRateCountReq) ProtoMessage()    {}
func (*GetGameUserNotRateCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{22}
}
func (m *GetGameUserNotRateCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameUserNotRateCountReq.Unmarshal(m, b)
}
func (m *GetGameUserNotRateCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameUserNotRateCountReq.Marshal(b, m, deterministic)
}
func (dst *GetGameUserNotRateCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameUserNotRateCountReq.Merge(dst, src)
}
func (m *GetGameUserNotRateCountReq) XXX_Size() int {
	return xxx_messageInfo_GetGameUserNotRateCountReq.Size(m)
}
func (m *GetGameUserNotRateCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameUserNotRateCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameUserNotRateCountReq proto.InternalMessageInfo

func (m *GetGameUserNotRateCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGameUserNotRateCountReq) GetRateId() string {
	if m != nil {
		return m.RateId
	}
	return ""
}

func (m *GetGameUserNotRateCountReq) GetCreateTime() uint64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type GetGameUserNotRateCountResp struct {
	// 未评价数量
	NotRateCount uint32 `protobuf:"varint,1,opt,name=not_rate_count,json=notRateCount,proto3" json:"not_rate_count,omitempty"`
	// 未读数量
	NotReadCount uint32 `protobuf:"varint,2,opt,name=not_read_count,json=notReadCount,proto3" json:"not_read_count,omitempty"`
	// 最新的下发评价/收到评价时间戳
	FirstRecordTime      int64    `protobuf:"varint,4,opt,name=first_record_time,json=firstRecordTime,proto3" json:"first_record_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameUserNotRateCountResp) Reset()         { *m = GetGameUserNotRateCountResp{} }
func (m *GetGameUserNotRateCountResp) String() string { return proto.CompactTextString(m) }
func (*GetGameUserNotRateCountResp) ProtoMessage()    {}
func (*GetGameUserNotRateCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{23}
}
func (m *GetGameUserNotRateCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameUserNotRateCountResp.Unmarshal(m, b)
}
func (m *GetGameUserNotRateCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameUserNotRateCountResp.Marshal(b, m, deterministic)
}
func (dst *GetGameUserNotRateCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameUserNotRateCountResp.Merge(dst, src)
}
func (m *GetGameUserNotRateCountResp) XXX_Size() int {
	return xxx_messageInfo_GetGameUserNotRateCountResp.Size(m)
}
func (m *GetGameUserNotRateCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameUserNotRateCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameUserNotRateCountResp proto.InternalMessageInfo

func (m *GetGameUserNotRateCountResp) GetNotRateCount() uint32 {
	if m != nil {
		return m.NotRateCount
	}
	return 0
}

func (m *GetGameUserNotRateCountResp) GetNotReadCount() uint32 {
	if m != nil {
		return m.NotReadCount
	}
	return 0
}

func (m *GetGameUserNotRateCountResp) GetFirstRecordTime() int64 {
	if m != nil {
		return m.FirstRecordTime
	}
	return 0
}

type IsNeedPushAssistReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsNeedPushAssistReq) Reset()         { *m = IsNeedPushAssistReq{} }
func (m *IsNeedPushAssistReq) String() string { return proto.CompactTextString(m) }
func (*IsNeedPushAssistReq) ProtoMessage()    {}
func (*IsNeedPushAssistReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{24}
}
func (m *IsNeedPushAssistReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsNeedPushAssistReq.Unmarshal(m, b)
}
func (m *IsNeedPushAssistReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsNeedPushAssistReq.Marshal(b, m, deterministic)
}
func (dst *IsNeedPushAssistReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsNeedPushAssistReq.Merge(dst, src)
}
func (m *IsNeedPushAssistReq) XXX_Size() int {
	return xxx_messageInfo_IsNeedPushAssistReq.Size(m)
}
func (m *IsNeedPushAssistReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsNeedPushAssistReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsNeedPushAssistReq proto.InternalMessageInfo

func (m *IsNeedPushAssistReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type IsNeedPushAssistResp struct {
	Result               bool     `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsNeedPushAssistResp) Reset()         { *m = IsNeedPushAssistResp{} }
func (m *IsNeedPushAssistResp) String() string { return proto.CompactTextString(m) }
func (*IsNeedPushAssistResp) ProtoMessage()    {}
func (*IsNeedPushAssistResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{25}
}
func (m *IsNeedPushAssistResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsNeedPushAssistResp.Unmarshal(m, b)
}
func (m *IsNeedPushAssistResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsNeedPushAssistResp.Marshal(b, m, deterministic)
}
func (dst *IsNeedPushAssistResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsNeedPushAssistResp.Merge(dst, src)
}
func (m *IsNeedPushAssistResp) XXX_Size() int {
	return xxx_messageInfo_IsNeedPushAssistResp.Size(m)
}
func (m *IsNeedPushAssistResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsNeedPushAssistResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsNeedPushAssistResp proto.InternalMessageInfo

func (m *IsNeedPushAssistResp) GetResult() bool {
	if m != nil {
		return m.Result
	}
	return false
}

type GetGameUserPersonalImageReq struct {
	Item                 []*GameUserPersonalImageReqItem `protobuf:"bytes,1,rep,name=item,proto3" json:"item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetGameUserPersonalImageReq) Reset()         { *m = GetGameUserPersonalImageReq{} }
func (m *GetGameUserPersonalImageReq) String() string { return proto.CompactTextString(m) }
func (*GetGameUserPersonalImageReq) ProtoMessage()    {}
func (*GetGameUserPersonalImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{26}
}
func (m *GetGameUserPersonalImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameUserPersonalImageReq.Unmarshal(m, b)
}
func (m *GetGameUserPersonalImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameUserPersonalImageReq.Marshal(b, m, deterministic)
}
func (dst *GetGameUserPersonalImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameUserPersonalImageReq.Merge(dst, src)
}
func (m *GetGameUserPersonalImageReq) XXX_Size() int {
	return xxx_messageInfo_GetGameUserPersonalImageReq.Size(m)
}
func (m *GetGameUserPersonalImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameUserPersonalImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameUserPersonalImageReq proto.InternalMessageInfo

func (m *GetGameUserPersonalImageReq) GetItem() []*GameUserPersonalImageReqItem {
	if m != nil {
		return m.Item
	}
	return nil
}

type GameUserPersonalImageReqItem struct {
	// 用户id
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 玩法id
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameUserPersonalImageReqItem) Reset()         { *m = GameUserPersonalImageReqItem{} }
func (m *GameUserPersonalImageReqItem) String() string { return proto.CompactTextString(m) }
func (*GameUserPersonalImageReqItem) ProtoMessage()    {}
func (*GameUserPersonalImageReqItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{27}
}
func (m *GameUserPersonalImageReqItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameUserPersonalImageReqItem.Unmarshal(m, b)
}
func (m *GameUserPersonalImageReqItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameUserPersonalImageReqItem.Marshal(b, m, deterministic)
}
func (dst *GameUserPersonalImageReqItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameUserPersonalImageReqItem.Merge(dst, src)
}
func (m *GameUserPersonalImageReqItem) XXX_Size() int {
	return xxx_messageInfo_GameUserPersonalImageReqItem.Size(m)
}
func (m *GameUserPersonalImageReqItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameUserPersonalImageReqItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameUserPersonalImageReqItem proto.InternalMessageInfo

func (m *GameUserPersonalImageReqItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameUserPersonalImageReqItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetGameUserPersonalImageResp struct {
	// key:uid_tabId, value:用户开黑形象
	Items                map[string]*GameUserPersonalImageRespItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                  `json:"-"`
	XXX_unrecognized     []byte                                    `json:"-"`
	XXX_sizecache        int32                                     `json:"-"`
}

func (m *GetGameUserPersonalImageResp) Reset()         { *m = GetGameUserPersonalImageResp{} }
func (m *GetGameUserPersonalImageResp) String() string { return proto.CompactTextString(m) }
func (*GetGameUserPersonalImageResp) ProtoMessage()    {}
func (*GetGameUserPersonalImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{28}
}
func (m *GetGameUserPersonalImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameUserPersonalImageResp.Unmarshal(m, b)
}
func (m *GetGameUserPersonalImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameUserPersonalImageResp.Marshal(b, m, deterministic)
}
func (dst *GetGameUserPersonalImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameUserPersonalImageResp.Merge(dst, src)
}
func (m *GetGameUserPersonalImageResp) XXX_Size() int {
	return xxx_messageInfo_GetGameUserPersonalImageResp.Size(m)
}
func (m *GetGameUserPersonalImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameUserPersonalImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameUserPersonalImageResp proto.InternalMessageInfo

func (m *GetGameUserPersonalImageResp) GetItems() map[string]*GameUserPersonalImageRespItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type GameUserPersonalImageRespItem struct {
	// 维度分数
	Item []*DimensionItem `protobuf:"bytes,1,rep,name=item,proto3" json:"item,omitempty"`
	// 评价总分
	TotalScore float32 `protobuf:"fixed32,2,opt,name=total_score,json=totalScore,proto3" json:"total_score,omitempty"`
	// 开黑关键字，aigc返回
	GameKeyWords string `protobuf:"bytes,3,opt,name=game_key_words,json=gameKeyWords,proto3" json:"game_key_words,omitempty"`
	// 关键字描述，aigc返回
	GameKeyWordsDesc string `protobuf:"bytes,4,opt,name=game_key_words_desc,json=gameKeyWordsDesc,proto3" json:"game_key_words_desc,omitempty"`
	// 自我介绍，aigc返回
	SelfIntro string `protobuf:"bytes,5,opt,name=self_intro,json=selfIntro,proto3" json:"self_intro,omitempty"`
	// 相似用户占比，数据库写入时取10%-40%之间的随机数，H5测评结果用
	SimilarUserPercent   string   `protobuf:"bytes,6,opt,name=similar_user_percent,json=similarUserPercent,proto3" json:"similar_user_percent,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameUserPersonalImageRespItem) Reset()         { *m = GameUserPersonalImageRespItem{} }
func (m *GameUserPersonalImageRespItem) String() string { return proto.CompactTextString(m) }
func (*GameUserPersonalImageRespItem) ProtoMessage()    {}
func (*GameUserPersonalImageRespItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{29}
}
func (m *GameUserPersonalImageRespItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameUserPersonalImageRespItem.Unmarshal(m, b)
}
func (m *GameUserPersonalImageRespItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameUserPersonalImageRespItem.Marshal(b, m, deterministic)
}
func (dst *GameUserPersonalImageRespItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameUserPersonalImageRespItem.Merge(dst, src)
}
func (m *GameUserPersonalImageRespItem) XXX_Size() int {
	return xxx_messageInfo_GameUserPersonalImageRespItem.Size(m)
}
func (m *GameUserPersonalImageRespItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameUserPersonalImageRespItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameUserPersonalImageRespItem proto.InternalMessageInfo

func (m *GameUserPersonalImageRespItem) GetItem() []*DimensionItem {
	if m != nil {
		return m.Item
	}
	return nil
}

func (m *GameUserPersonalImageRespItem) GetTotalScore() float32 {
	if m != nil {
		return m.TotalScore
	}
	return 0
}

func (m *GameUserPersonalImageRespItem) GetGameKeyWords() string {
	if m != nil {
		return m.GameKeyWords
	}
	return ""
}

func (m *GameUserPersonalImageRespItem) GetGameKeyWordsDesc() string {
	if m != nil {
		return m.GameKeyWordsDesc
	}
	return ""
}

func (m *GameUserPersonalImageRespItem) GetSelfIntro() string {
	if m != nil {
		return m.SelfIntro
	}
	return ""
}

func (m *GameUserPersonalImageRespItem) GetSimilarUserPercent() string {
	if m != nil {
		return m.SimilarUserPercent
	}
	return ""
}

type DimensionItem struct {
	// 维度id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 维度名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 维度分数
	Score                float32  `protobuf:"fixed32,3,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DimensionItem) Reset()         { *m = DimensionItem{} }
func (m *DimensionItem) String() string { return proto.CompactTextString(m) }
func (*DimensionItem) ProtoMessage()    {}
func (*DimensionItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{30}
}
func (m *DimensionItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DimensionItem.Unmarshal(m, b)
}
func (m *DimensionItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DimensionItem.Marshal(b, m, deterministic)
}
func (dst *DimensionItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DimensionItem.Merge(dst, src)
}
func (m *DimensionItem) XXX_Size() int {
	return xxx_messageInfo_DimensionItem.Size(m)
}
func (m *DimensionItem) XXX_DiscardUnknown() {
	xxx_messageInfo_DimensionItem.DiscardUnknown(m)
}

var xxx_messageInfo_DimensionItem proto.InternalMessageInfo

func (m *DimensionItem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *DimensionItem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DimensionItem) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

// 自评问题配置
type H5Question struct {
	// 题目id
	QuestionId string `protobuf:"bytes,1,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
	// 题目描述
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// 选项
	Options []*H5Question_Option `protobuf:"bytes,3,rep,name=options,proto3" json:"options,omitempty"`
	// 题目所属维度id
	DimensionId          string   `protobuf:"bytes,4,opt,name=dimension_id,json=dimensionId,proto3" json:"dimension_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *H5Question) Reset()         { *m = H5Question{} }
func (m *H5Question) String() string { return proto.CompactTextString(m) }
func (*H5Question) ProtoMessage()    {}
func (*H5Question) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{31}
}
func (m *H5Question) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_H5Question.Unmarshal(m, b)
}
func (m *H5Question) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_H5Question.Marshal(b, m, deterministic)
}
func (dst *H5Question) XXX_Merge(src proto.Message) {
	xxx_messageInfo_H5Question.Merge(dst, src)
}
func (m *H5Question) XXX_Size() int {
	return xxx_messageInfo_H5Question.Size(m)
}
func (m *H5Question) XXX_DiscardUnknown() {
	xxx_messageInfo_H5Question.DiscardUnknown(m)
}

var xxx_messageInfo_H5Question proto.InternalMessageInfo

func (m *H5Question) GetQuestionId() string {
	if m != nil {
		return m.QuestionId
	}
	return ""
}

func (m *H5Question) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *H5Question) GetOptions() []*H5Question_Option {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *H5Question) GetDimensionId() string {
	if m != nil {
		return m.DimensionId
	}
	return ""
}

type H5Question_Option struct {
	OptId                uint32   `protobuf:"varint,1,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *H5Question_Option) Reset()         { *m = H5Question_Option{} }
func (m *H5Question_Option) String() string { return proto.CompactTextString(m) }
func (*H5Question_Option) ProtoMessage()    {}
func (*H5Question_Option) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{31, 0}
}
func (m *H5Question_Option) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_H5Question_Option.Unmarshal(m, b)
}
func (m *H5Question_Option) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_H5Question_Option.Marshal(b, m, deterministic)
}
func (dst *H5Question_Option) XXX_Merge(src proto.Message) {
	xxx_messageInfo_H5Question_Option.Merge(dst, src)
}
func (m *H5Question_Option) XXX_Size() int {
	return xxx_messageInfo_H5Question_Option.Size(m)
}
func (m *H5Question_Option) XXX_DiscardUnknown() {
	xxx_messageInfo_H5Question_Option.DiscardUnknown(m)
}

var xxx_messageInfo_H5Question_Option proto.InternalMessageInfo

func (m *H5Question_Option) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *H5Question_Option) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// 获取开黑形象自评题目
type GetGameUserSelfRateQuestionsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameUserSelfRateQuestionsReq) Reset()         { *m = GetGameUserSelfRateQuestionsReq{} }
func (m *GetGameUserSelfRateQuestionsReq) String() string { return proto.CompactTextString(m) }
func (*GetGameUserSelfRateQuestionsReq) ProtoMessage()    {}
func (*GetGameUserSelfRateQuestionsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{32}
}
func (m *GetGameUserSelfRateQuestionsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameUserSelfRateQuestionsReq.Unmarshal(m, b)
}
func (m *GetGameUserSelfRateQuestionsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameUserSelfRateQuestionsReq.Marshal(b, m, deterministic)
}
func (dst *GetGameUserSelfRateQuestionsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameUserSelfRateQuestionsReq.Merge(dst, src)
}
func (m *GetGameUserSelfRateQuestionsReq) XXX_Size() int {
	return xxx_messageInfo_GetGameUserSelfRateQuestionsReq.Size(m)
}
func (m *GetGameUserSelfRateQuestionsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameUserSelfRateQuestionsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameUserSelfRateQuestionsReq proto.InternalMessageInfo

func (m *GetGameUserSelfRateQuestionsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGameUserSelfRateQuestionsReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetGameUserSelfRateQuestionsResp struct {
	Questions            []*H5Question `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetGameUserSelfRateQuestionsResp) Reset()         { *m = GetGameUserSelfRateQuestionsResp{} }
func (m *GetGameUserSelfRateQuestionsResp) String() string { return proto.CompactTextString(m) }
func (*GetGameUserSelfRateQuestionsResp) ProtoMessage()    {}
func (*GetGameUserSelfRateQuestionsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{33}
}
func (m *GetGameUserSelfRateQuestionsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameUserSelfRateQuestionsResp.Unmarshal(m, b)
}
func (m *GetGameUserSelfRateQuestionsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameUserSelfRateQuestionsResp.Marshal(b, m, deterministic)
}
func (dst *GetGameUserSelfRateQuestionsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameUserSelfRateQuestionsResp.Merge(dst, src)
}
func (m *GetGameUserSelfRateQuestionsResp) XXX_Size() int {
	return xxx_messageInfo_GetGameUserSelfRateQuestionsResp.Size(m)
}
func (m *GetGameUserSelfRateQuestionsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameUserSelfRateQuestionsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameUserSelfRateQuestionsResp proto.InternalMessageInfo

func (m *GetGameUserSelfRateQuestionsResp) GetQuestions() []*H5Question {
	if m != nil {
		return m.Questions
	}
	return nil
}

type SelfRateResult struct {
	// 题目id
	QuestionId string `protobuf:"bytes,1,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
	// 选项id
	OptId uint32 `protobuf:"varint,2,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	// 题目所属维度id
	DimensionId          string   `protobuf:"bytes,3,opt,name=dimension_id,json=dimensionId,proto3" json:"dimension_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SelfRateResult) Reset()         { *m = SelfRateResult{} }
func (m *SelfRateResult) String() string { return proto.CompactTextString(m) }
func (*SelfRateResult) ProtoMessage()    {}
func (*SelfRateResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{34}
}
func (m *SelfRateResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelfRateResult.Unmarshal(m, b)
}
func (m *SelfRateResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelfRateResult.Marshal(b, m, deterministic)
}
func (dst *SelfRateResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelfRateResult.Merge(dst, src)
}
func (m *SelfRateResult) XXX_Size() int {
	return xxx_messageInfo_SelfRateResult.Size(m)
}
func (m *SelfRateResult) XXX_DiscardUnknown() {
	xxx_messageInfo_SelfRateResult.DiscardUnknown(m)
}

var xxx_messageInfo_SelfRateResult proto.InternalMessageInfo

func (m *SelfRateResult) GetQuestionId() string {
	if m != nil {
		return m.QuestionId
	}
	return ""
}

func (m *SelfRateResult) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *SelfRateResult) GetDimensionId() string {
	if m != nil {
		return m.DimensionId
	}
	return ""
}

// 提交开黑形象自评结果
type SubmitGameUserSelfRateReq struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32            `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Results              []*SelfRateResult `protobuf:"bytes,3,rep,name=results,proto3" json:"results,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SubmitGameUserSelfRateReq) Reset()         { *m = SubmitGameUserSelfRateReq{} }
func (m *SubmitGameUserSelfRateReq) String() string { return proto.CompactTextString(m) }
func (*SubmitGameUserSelfRateReq) ProtoMessage()    {}
func (*SubmitGameUserSelfRateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{35}
}
func (m *SubmitGameUserSelfRateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitGameUserSelfRateReq.Unmarshal(m, b)
}
func (m *SubmitGameUserSelfRateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitGameUserSelfRateReq.Marshal(b, m, deterministic)
}
func (dst *SubmitGameUserSelfRateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitGameUserSelfRateReq.Merge(dst, src)
}
func (m *SubmitGameUserSelfRateReq) XXX_Size() int {
	return xxx_messageInfo_SubmitGameUserSelfRateReq.Size(m)
}
func (m *SubmitGameUserSelfRateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitGameUserSelfRateReq.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitGameUserSelfRateReq proto.InternalMessageInfo

func (m *SubmitGameUserSelfRateReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SubmitGameUserSelfRateReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SubmitGameUserSelfRateReq) GetResults() []*SelfRateResult {
	if m != nil {
		return m.Results
	}
	return nil
}

type SubmitGameUserSelfRateResp struct {
	Item                 *GameUserPersonalImageRespItem `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *SubmitGameUserSelfRateResp) Reset()         { *m = SubmitGameUserSelfRateResp{} }
func (m *SubmitGameUserSelfRateResp) String() string { return proto.CompactTextString(m) }
func (*SubmitGameUserSelfRateResp) ProtoMessage()    {}
func (*SubmitGameUserSelfRateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{36}
}
func (m *SubmitGameUserSelfRateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitGameUserSelfRateResp.Unmarshal(m, b)
}
func (m *SubmitGameUserSelfRateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitGameUserSelfRateResp.Marshal(b, m, deterministic)
}
func (dst *SubmitGameUserSelfRateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitGameUserSelfRateResp.Merge(dst, src)
}
func (m *SubmitGameUserSelfRateResp) XXX_Size() int {
	return xxx_messageInfo_SubmitGameUserSelfRateResp.Size(m)
}
func (m *SubmitGameUserSelfRateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitGameUserSelfRateResp.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitGameUserSelfRateResp proto.InternalMessageInfo

func (m *SubmitGameUserSelfRateResp) GetItem() *GameUserPersonalImageRespItem {
	if m != nil {
		return m.Item
	}
	return nil
}

type GetGameDimensionConfReq struct {
	TabId                []uint32 `protobuf:"varint,1,rep,packed,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameDimensionConfReq) Reset()         { *m = GetGameDimensionConfReq{} }
func (m *GetGameDimensionConfReq) String() string { return proto.CompactTextString(m) }
func (*GetGameDimensionConfReq) ProtoMessage()    {}
func (*GetGameDimensionConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{37}
}
func (m *GetGameDimensionConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameDimensionConfReq.Unmarshal(m, b)
}
func (m *GetGameDimensionConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameDimensionConfReq.Marshal(b, m, deterministic)
}
func (dst *GetGameDimensionConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameDimensionConfReq.Merge(dst, src)
}
func (m *GetGameDimensionConfReq) XXX_Size() int {
	return xxx_messageInfo_GetGameDimensionConfReq.Size(m)
}
func (m *GetGameDimensionConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameDimensionConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameDimensionConfReq proto.InternalMessageInfo

func (m *GetGameDimensionConfReq) GetTabId() []uint32 {
	if m != nil {
		return m.TabId
	}
	return nil
}

type GameDimensionItem struct {
	DimensionItems       []*DimensionItem `protobuf:"bytes,1,rep,name=dimension_items,json=dimensionItems,proto3" json:"dimension_items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GameDimensionItem) Reset()         { *m = GameDimensionItem{} }
func (m *GameDimensionItem) String() string { return proto.CompactTextString(m) }
func (*GameDimensionItem) ProtoMessage()    {}
func (*GameDimensionItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{38}
}
func (m *GameDimensionItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameDimensionItem.Unmarshal(m, b)
}
func (m *GameDimensionItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameDimensionItem.Marshal(b, m, deterministic)
}
func (dst *GameDimensionItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameDimensionItem.Merge(dst, src)
}
func (m *GameDimensionItem) XXX_Size() int {
	return xxx_messageInfo_GameDimensionItem.Size(m)
}
func (m *GameDimensionItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameDimensionItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameDimensionItem proto.InternalMessageInfo

func (m *GameDimensionItem) GetDimensionItems() []*DimensionItem {
	if m != nil {
		return m.DimensionItems
	}
	return nil
}

type GetGameDimensionConfResp struct {
	Items                map[uint32]*GameDimensionItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	SelfRateInternal     uint32                        `protobuf:"varint,2,opt,name=self_rate_internal,json=selfRateInternal,proto3" json:"self_rate_internal,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetGameDimensionConfResp) Reset()         { *m = GetGameDimensionConfResp{} }
func (m *GetGameDimensionConfResp) String() string { return proto.CompactTextString(m) }
func (*GetGameDimensionConfResp) ProtoMessage()    {}
func (*GetGameDimensionConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{39}
}
func (m *GetGameDimensionConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameDimensionConfResp.Unmarshal(m, b)
}
func (m *GetGameDimensionConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameDimensionConfResp.Marshal(b, m, deterministic)
}
func (dst *GetGameDimensionConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameDimensionConfResp.Merge(dst, src)
}
func (m *GetGameDimensionConfResp) XXX_Size() int {
	return xxx_messageInfo_GetGameDimensionConfResp.Size(m)
}
func (m *GetGameDimensionConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameDimensionConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameDimensionConfResp proto.InternalMessageInfo

func (m *GetGameDimensionConfResp) GetItems() map[uint32]*GameDimensionItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *GetGameDimensionConfResp) GetSelfRateInternal() uint32 {
	if m != nil {
		return m.SelfRateInternal
	}
	return 0
}

type AddOtherRateReq struct {
	OtherRateMsg         []*OtherRateMsg `protobuf:"bytes,1,rep,name=other_rate_msg,json=otherRateMsg,proto3" json:"other_rate_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AddOtherRateReq) Reset()         { *m = AddOtherRateReq{} }
func (m *AddOtherRateReq) String() string { return proto.CompactTextString(m) }
func (*AddOtherRateReq) ProtoMessage()    {}
func (*AddOtherRateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{40}
}
func (m *AddOtherRateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddOtherRateReq.Unmarshal(m, b)
}
func (m *AddOtherRateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddOtherRateReq.Marshal(b, m, deterministic)
}
func (dst *AddOtherRateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddOtherRateReq.Merge(dst, src)
}
func (m *AddOtherRateReq) XXX_Size() int {
	return xxx_messageInfo_AddOtherRateReq.Size(m)
}
func (m *AddOtherRateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddOtherRateReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddOtherRateReq proto.InternalMessageInfo

func (m *AddOtherRateReq) GetOtherRateMsg() []*OtherRateMsg {
	if m != nil {
		return m.OtherRateMsg
	}
	return nil
}

type OtherRateMsg struct {
	DimensionScoreMap map[string]int32 `protobuf:"bytes,1,rep,name=dimension_score_map,json=dimensionScoreMap,proto3" json:"dimension_score_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	Uid               uint32           `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId             uint32           `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	RateUid           uint32           `protobuf:"varint,4,opt,name=rate_uid,json=rateUid,proto3" json:"rate_uid,omitempty"`
	HappenTime        int64            `protobuf:"varint,5,opt,name=happen_time,json=happenTime,proto3" json:"happen_time,omitempty"`
	// 用户搭子擦亮的时间
	GamePalLightTime     int64    `protobuf:"varint,6,opt,name=game_pal_light_time,json=gamePalLightTime,proto3" json:"game_pal_light_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OtherRateMsg) Reset()         { *m = OtherRateMsg{} }
func (m *OtherRateMsg) String() string { return proto.CompactTextString(m) }
func (*OtherRateMsg) ProtoMessage()    {}
func (*OtherRateMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{41}
}
func (m *OtherRateMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OtherRateMsg.Unmarshal(m, b)
}
func (m *OtherRateMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OtherRateMsg.Marshal(b, m, deterministic)
}
func (dst *OtherRateMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OtherRateMsg.Merge(dst, src)
}
func (m *OtherRateMsg) XXX_Size() int {
	return xxx_messageInfo_OtherRateMsg.Size(m)
}
func (m *OtherRateMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_OtherRateMsg.DiscardUnknown(m)
}

var xxx_messageInfo_OtherRateMsg proto.InternalMessageInfo

func (m *OtherRateMsg) GetDimensionScoreMap() map[string]int32 {
	if m != nil {
		return m.DimensionScoreMap
	}
	return nil
}

func (m *OtherRateMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OtherRateMsg) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *OtherRateMsg) GetRateUid() uint32 {
	if m != nil {
		return m.RateUid
	}
	return 0
}

func (m *OtherRateMsg) GetHappenTime() int64 {
	if m != nil {
		return m.HappenTime
	}
	return 0
}

func (m *OtherRateMsg) GetGamePalLightTime() int64 {
	if m != nil {
		return m.GamePalLightTime
	}
	return 0
}

type AddOtherRateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddOtherRateResp) Reset()         { *m = AddOtherRateResp{} }
func (m *AddOtherRateResp) String() string { return proto.CompactTextString(m) }
func (*AddOtherRateResp) ProtoMessage()    {}
func (*AddOtherRateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{42}
}
func (m *AddOtherRateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddOtherRateResp.Unmarshal(m, b)
}
func (m *AddOtherRateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddOtherRateResp.Marshal(b, m, deterministic)
}
func (dst *AddOtherRateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddOtherRateResp.Merge(dst, src)
}
func (m *AddOtherRateResp) XXX_Size() int {
	return xxx_messageInfo_AddOtherRateResp.Size(m)
}
func (m *AddOtherRateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddOtherRateResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddOtherRateResp proto.InternalMessageInfo

type UnionScoreReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnionScoreReq) Reset()         { *m = UnionScoreReq{} }
func (m *UnionScoreReq) String() string { return proto.CompactTextString(m) }
func (*UnionScoreReq) ProtoMessage()    {}
func (*UnionScoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{43}
}
func (m *UnionScoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnionScoreReq.Unmarshal(m, b)
}
func (m *UnionScoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnionScoreReq.Marshal(b, m, deterministic)
}
func (dst *UnionScoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnionScoreReq.Merge(dst, src)
}
func (m *UnionScoreReq) XXX_Size() int {
	return xxx_messageInfo_UnionScoreReq.Size(m)
}
func (m *UnionScoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnionScoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnionScoreReq proto.InternalMessageInfo

type DimensionScoreInfo struct {
	Uid                    uint32             `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                  uint32             `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	DimensionTotalScoreMap map[string]float32 `protobuf:"bytes,3,rep,name=dimension_total_score_map,json=dimensionTotalScoreMap,proto3" json:"dimension_total_score_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	TabName                string             `protobuf:"bytes,4,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Gender                 string             `protobuf:"bytes,5,opt,name=gender,proto3" json:"gender,omitempty"`
	Label                  string             `protobuf:"bytes,6,opt,name=label,proto3" json:"label,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}           `json:"-"`
	XXX_unrecognized       []byte             `json:"-"`
	XXX_sizecache          int32              `json:"-"`
}

func (m *DimensionScoreInfo) Reset()         { *m = DimensionScoreInfo{} }
func (m *DimensionScoreInfo) String() string { return proto.CompactTextString(m) }
func (*DimensionScoreInfo) ProtoMessage()    {}
func (*DimensionScoreInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{44}
}
func (m *DimensionScoreInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DimensionScoreInfo.Unmarshal(m, b)
}
func (m *DimensionScoreInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DimensionScoreInfo.Marshal(b, m, deterministic)
}
func (dst *DimensionScoreInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DimensionScoreInfo.Merge(dst, src)
}
func (m *DimensionScoreInfo) XXX_Size() int {
	return xxx_messageInfo_DimensionScoreInfo.Size(m)
}
func (m *DimensionScoreInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DimensionScoreInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DimensionScoreInfo proto.InternalMessageInfo

func (m *DimensionScoreInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DimensionScoreInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *DimensionScoreInfo) GetDimensionTotalScoreMap() map[string]float32 {
	if m != nil {
		return m.DimensionTotalScoreMap
	}
	return nil
}

func (m *DimensionScoreInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *DimensionScoreInfo) GetGender() string {
	if m != nil {
		return m.Gender
	}
	return ""
}

func (m *DimensionScoreInfo) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

type UnionScoreResp struct {
	DimensionScoreInfos  []*DimensionScoreInfo `protobuf:"bytes,1,rep,name=dimension_score_infos,json=dimensionScoreInfos,proto3" json:"dimension_score_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *UnionScoreResp) Reset()         { *m = UnionScoreResp{} }
func (m *UnionScoreResp) String() string { return proto.CompactTextString(m) }
func (*UnionScoreResp) ProtoMessage()    {}
func (*UnionScoreResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{45}
}
func (m *UnionScoreResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnionScoreResp.Unmarshal(m, b)
}
func (m *UnionScoreResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnionScoreResp.Marshal(b, m, deterministic)
}
func (dst *UnionScoreResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnionScoreResp.Merge(dst, src)
}
func (m *UnionScoreResp) XXX_Size() int {
	return xxx_messageInfo_UnionScoreResp.Size(m)
}
func (m *UnionScoreResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnionScoreResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnionScoreResp proto.InternalMessageInfo

func (m *UnionScoreResp) GetDimensionScoreInfos() []*DimensionScoreInfo {
	if m != nil {
		return m.DimensionScoreInfos
	}
	return nil
}

type GetGameUserBeRateListReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// last record time，分页用
	LastRecordTime       uint64   `protobuf:"varint,2,opt,name=last_record_time,json=lastRecordTime,proto3" json:"last_record_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameUserBeRateListReq) Reset()         { *m = GetGameUserBeRateListReq{} }
func (m *GetGameUserBeRateListReq) String() string { return proto.CompactTextString(m) }
func (*GetGameUserBeRateListReq) ProtoMessage()    {}
func (*GetGameUserBeRateListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{46}
}
func (m *GetGameUserBeRateListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameUserBeRateListReq.Unmarshal(m, b)
}
func (m *GetGameUserBeRateListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameUserBeRateListReq.Marshal(b, m, deterministic)
}
func (dst *GetGameUserBeRateListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameUserBeRateListReq.Merge(dst, src)
}
func (m *GetGameUserBeRateListReq) XXX_Size() int {
	return xxx_messageInfo_GetGameUserBeRateListReq.Size(m)
}
func (m *GetGameUserBeRateListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameUserBeRateListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameUserBeRateListReq proto.InternalMessageInfo

func (m *GetGameUserBeRateListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGameUserBeRateListReq) GetLastRecordTime() uint64 {
	if m != nil {
		return m.LastRecordTime
	}
	return 0
}

type GetGameUserBeRateListResp struct {
	// 评价详情数据
	Items []*GameUserBeRateItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	// 是否已经到底部，true为底部
	LoadFinish           bool     `protobuf:"varint,2,opt,name=load_finish,json=loadFinish,proto3" json:"load_finish,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameUserBeRateListResp) Reset()         { *m = GetGameUserBeRateListResp{} }
func (m *GetGameUserBeRateListResp) String() string { return proto.CompactTextString(m) }
func (*GetGameUserBeRateListResp) ProtoMessage()    {}
func (*GetGameUserBeRateListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{47}
}
func (m *GetGameUserBeRateListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameUserBeRateListResp.Unmarshal(m, b)
}
func (m *GetGameUserBeRateListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameUserBeRateListResp.Marshal(b, m, deterministic)
}
func (dst *GetGameUserBeRateListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameUserBeRateListResp.Merge(dst, src)
}
func (m *GetGameUserBeRateListResp) XXX_Size() int {
	return xxx_messageInfo_GetGameUserBeRateListResp.Size(m)
}
func (m *GetGameUserBeRateListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameUserBeRateListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameUserBeRateListResp proto.InternalMessageInfo

func (m *GetGameUserBeRateListResp) GetItems() []*GameUserBeRateItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *GetGameUserBeRateListResp) GetLoadFinish() bool {
	if m != nil {
		return m.LoadFinish
	}
	return false
}

type GameUserBeRateItem struct {
	// item类型，1-好评，2-扣分
	ItemType uint32 `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	// Types that are valid to be assigned to Content:
	//	*GameUserBeRateItem_LikeItem_
	//	*GameUserBeRateItem_DeductItem_
	Content              isGameUserBeRateItem_Content `protobuf_oneof:"content"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GameUserBeRateItem) Reset()         { *m = GameUserBeRateItem{} }
func (m *GameUserBeRateItem) String() string { return proto.CompactTextString(m) }
func (*GameUserBeRateItem) ProtoMessage()    {}
func (*GameUserBeRateItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{48}
}
func (m *GameUserBeRateItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameUserBeRateItem.Unmarshal(m, b)
}
func (m *GameUserBeRateItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameUserBeRateItem.Marshal(b, m, deterministic)
}
func (dst *GameUserBeRateItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameUserBeRateItem.Merge(dst, src)
}
func (m *GameUserBeRateItem) XXX_Size() int {
	return xxx_messageInfo_GameUserBeRateItem.Size(m)
}
func (m *GameUserBeRateItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameUserBeRateItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameUserBeRateItem proto.InternalMessageInfo

func (m *GameUserBeRateItem) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

type isGameUserBeRateItem_Content interface {
	isGameUserBeRateItem_Content()
}

type GameUserBeRateItem_LikeItem_ struct {
	LikeItem *GameUserBeRateItem_LikeItem `protobuf:"bytes,2,opt,name=like_item,json=likeItem,proto3,oneof"`
}

type GameUserBeRateItem_DeductItem_ struct {
	DeductItem *GameUserBeRateItem_DeductItem `protobuf:"bytes,3,opt,name=deduct_item,json=deductItem,proto3,oneof"`
}

func (*GameUserBeRateItem_LikeItem_) isGameUserBeRateItem_Content() {}

func (*GameUserBeRateItem_DeductItem_) isGameUserBeRateItem_Content() {}

func (m *GameUserBeRateItem) GetContent() isGameUserBeRateItem_Content {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *GameUserBeRateItem) GetLikeItem() *GameUserBeRateItem_LikeItem {
	if x, ok := m.GetContent().(*GameUserBeRateItem_LikeItem_); ok {
		return x.LikeItem
	}
	return nil
}

func (m *GameUserBeRateItem) GetDeductItem() *GameUserBeRateItem_DeductItem {
	if x, ok := m.GetContent().(*GameUserBeRateItem_DeductItem_); ok {
		return x.DeductItem
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*GameUserBeRateItem) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _GameUserBeRateItem_OneofMarshaler, _GameUserBeRateItem_OneofUnmarshaler, _GameUserBeRateItem_OneofSizer, []interface{}{
		(*GameUserBeRateItem_LikeItem_)(nil),
		(*GameUserBeRateItem_DeductItem_)(nil),
	}
}

func _GameUserBeRateItem_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*GameUserBeRateItem)
	// content
	switch x := m.Content.(type) {
	case *GameUserBeRateItem_LikeItem_:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.LikeItem); err != nil {
			return err
		}
	case *GameUserBeRateItem_DeductItem_:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.DeductItem); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("GameUserBeRateItem.Content has unexpected type %T", x)
	}
	return nil
}

func _GameUserBeRateItem_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*GameUserBeRateItem)
	switch tag {
	case 2: // content.like_item
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(GameUserBeRateItem_LikeItem)
		err := b.DecodeMessage(msg)
		m.Content = &GameUserBeRateItem_LikeItem_{msg}
		return true, err
	case 3: // content.deduct_item
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(GameUserBeRateItem_DeductItem)
		err := b.DecodeMessage(msg)
		m.Content = &GameUserBeRateItem_DeductItem_{msg}
		return true, err
	default:
		return false, nil
	}
}

func _GameUserBeRateItem_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*GameUserBeRateItem)
	// content
	switch x := m.Content.(type) {
	case *GameUserBeRateItem_LikeItem_:
		s := proto.Size(x.LikeItem)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *GameUserBeRateItem_DeductItem_:
		s := proto.Size(x.DeductItem)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// 好评记录item
type GameUserBeRateItem_LikeItem struct {
	// 评价id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 用户id
	Uid uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// 下发时间
	CreateTime int64 `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 评价标签
	SelectTags []string `protobuf:"bytes,4,rep,name=select_tags,json=selectTags,proto3" json:"select_tags,omitempty"`
	// 用户自定义评价
	UserRateText         string   `protobuf:"bytes,5,opt,name=user_rate_text,json=userRateText,proto3" json:"user_rate_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameUserBeRateItem_LikeItem) Reset()         { *m = GameUserBeRateItem_LikeItem{} }
func (m *GameUserBeRateItem_LikeItem) String() string { return proto.CompactTextString(m) }
func (*GameUserBeRateItem_LikeItem) ProtoMessage()    {}
func (*GameUserBeRateItem_LikeItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{48, 0}
}
func (m *GameUserBeRateItem_LikeItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameUserBeRateItem_LikeItem.Unmarshal(m, b)
}
func (m *GameUserBeRateItem_LikeItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameUserBeRateItem_LikeItem.Marshal(b, m, deterministic)
}
func (dst *GameUserBeRateItem_LikeItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameUserBeRateItem_LikeItem.Merge(dst, src)
}
func (m *GameUserBeRateItem_LikeItem) XXX_Size() int {
	return xxx_messageInfo_GameUserBeRateItem_LikeItem.Size(m)
}
func (m *GameUserBeRateItem_LikeItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameUserBeRateItem_LikeItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameUserBeRateItem_LikeItem proto.InternalMessageInfo

func (m *GameUserBeRateItem_LikeItem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GameUserBeRateItem_LikeItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameUserBeRateItem_LikeItem) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GameUserBeRateItem_LikeItem) GetSelectTags() []string {
	if m != nil {
		return m.SelectTags
	}
	return nil
}

func (m *GameUserBeRateItem_LikeItem) GetUserRateText() string {
	if m != nil {
		return m.UserRateText
	}
	return ""
}

// 扣分记录item
type GameUserBeRateItem_DeductItem struct {
	// 评价id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 当前信誉分
	CurScore uint32 `protobuf:"varint,2,opt,name=cur_score,json=curScore,proto3" json:"cur_score,omitempty"`
	// 下发时间
	CreateTime int64 `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 扣分详情
	DeductDetail         []*DeductDetail `protobuf:"bytes,4,rep,name=deduct_detail,json=deductDetail,proto3" json:"deduct_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GameUserBeRateItem_DeductItem) Reset()         { *m = GameUserBeRateItem_DeductItem{} }
func (m *GameUserBeRateItem_DeductItem) String() string { return proto.CompactTextString(m) }
func (*GameUserBeRateItem_DeductItem) ProtoMessage()    {}
func (*GameUserBeRateItem_DeductItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{48, 1}
}
func (m *GameUserBeRateItem_DeductItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameUserBeRateItem_DeductItem.Unmarshal(m, b)
}
func (m *GameUserBeRateItem_DeductItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameUserBeRateItem_DeductItem.Marshal(b, m, deterministic)
}
func (dst *GameUserBeRateItem_DeductItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameUserBeRateItem_DeductItem.Merge(dst, src)
}
func (m *GameUserBeRateItem_DeductItem) XXX_Size() int {
	return xxx_messageInfo_GameUserBeRateItem_DeductItem.Size(m)
}
func (m *GameUserBeRateItem_DeductItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameUserBeRateItem_DeductItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameUserBeRateItem_DeductItem proto.InternalMessageInfo

func (m *GameUserBeRateItem_DeductItem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GameUserBeRateItem_DeductItem) GetCurScore() uint32 {
	if m != nil {
		return m.CurScore
	}
	return 0
}

func (m *GameUserBeRateItem_DeductItem) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GameUserBeRateItem_DeductItem) GetDeductDetail() []*DeductDetail {
	if m != nil {
		return m.DeductDetail
	}
	return nil
}

type DeductDetail struct {
	// 扣分标签名
	TagName string `protobuf:"bytes,1,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	// 扣分数
	Score                uint32   `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeductDetail) Reset()         { *m = DeductDetail{} }
func (m *DeductDetail) String() string { return proto.CompactTextString(m) }
func (*DeductDetail) ProtoMessage()    {}
func (*DeductDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{49}
}
func (m *DeductDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductDetail.Unmarshal(m, b)
}
func (m *DeductDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductDetail.Marshal(b, m, deterministic)
}
func (dst *DeductDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductDetail.Merge(dst, src)
}
func (m *DeductDetail) XXX_Size() int {
	return xxx_messageInfo_DeductDetail.Size(m)
}
func (m *DeductDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DeductDetail proto.InternalMessageInfo

func (m *DeductDetail) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *DeductDetail) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type GetUserReputationScoreReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserReputationScoreReq) Reset()         { *m = GetUserReputationScoreReq{} }
func (m *GetUserReputationScoreReq) String() string { return proto.CompactTextString(m) }
func (*GetUserReputationScoreReq) ProtoMessage()    {}
func (*GetUserReputationScoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{50}
}
func (m *GetUserReputationScoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserReputationScoreReq.Unmarshal(m, b)
}
func (m *GetUserReputationScoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserReputationScoreReq.Marshal(b, m, deterministic)
}
func (dst *GetUserReputationScoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserReputationScoreReq.Merge(dst, src)
}
func (m *GetUserReputationScoreReq) XXX_Size() int {
	return xxx_messageInfo_GetUserReputationScoreReq.Size(m)
}
func (m *GetUserReputationScoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserReputationScoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserReputationScoreReq proto.InternalMessageInfo

func (m *GetUserReputationScoreReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserReputationScoreResp struct {
	// 信誉分
	Score                uint32   `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserReputationScoreResp) Reset()         { *m = GetUserReputationScoreResp{} }
func (m *GetUserReputationScoreResp) String() string { return proto.CompactTextString(m) }
func (*GetUserReputationScoreResp) ProtoMessage()    {}
func (*GetUserReputationScoreResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{51}
}
func (m *GetUserReputationScoreResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserReputationScoreResp.Unmarshal(m, b)
}
func (m *GetUserReputationScoreResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserReputationScoreResp.Marshal(b, m, deterministic)
}
func (dst *GetUserReputationScoreResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserReputationScoreResp.Merge(dst, src)
}
func (m *GetUserReputationScoreResp) XXX_Size() int {
	return xxx_messageInfo_GetUserReputationScoreResp.Size(m)
}
func (m *GetUserReputationScoreResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserReputationScoreResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserReputationScoreResp proto.InternalMessageInfo

func (m *GetUserReputationScoreResp) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

// 获取推荐评价标签并重排标签列表
type ReorderRateTagsReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 用户自定义评价
	UserRateText string `protobuf:"bytes,2,opt,name=user_rate_text,json=userRateText,proto3" json:"user_rate_text,omitempty"`
	// 评价id
	RateItemId           string   `protobuf:"bytes,3,opt,name=rate_item_id,json=rateItemId,proto3" json:"rate_item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReorderRateTagsReq) Reset()         { *m = ReorderRateTagsReq{} }
func (m *ReorderRateTagsReq) String() string { return proto.CompactTextString(m) }
func (*ReorderRateTagsReq) ProtoMessage()    {}
func (*ReorderRateTagsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{52}
}
func (m *ReorderRateTagsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReorderRateTagsReq.Unmarshal(m, b)
}
func (m *ReorderRateTagsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReorderRateTagsReq.Marshal(b, m, deterministic)
}
func (dst *ReorderRateTagsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReorderRateTagsReq.Merge(dst, src)
}
func (m *ReorderRateTagsReq) XXX_Size() int {
	return xxx_messageInfo_ReorderRateTagsReq.Size(m)
}
func (m *ReorderRateTagsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReorderRateTagsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReorderRateTagsReq proto.InternalMessageInfo

func (m *ReorderRateTagsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReorderRateTagsReq) GetUserRateText() string {
	if m != nil {
		return m.UserRateText
	}
	return ""
}

func (m *ReorderRateTagsReq) GetRateItemId() string {
	if m != nil {
		return m.RateItemId
	}
	return ""
}

type ReorderRateTagsResp struct {
	// 返回推荐标签列表
	RateTags             []*GameUserRateTag `protobuf:"bytes,1,rep,name=rate_tags,json=rateTags,proto3" json:"rate_tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ReorderRateTagsResp) Reset()         { *m = ReorderRateTagsResp{} }
func (m *ReorderRateTagsResp) String() string { return proto.CompactTextString(m) }
func (*ReorderRateTagsResp) ProtoMessage()    {}
func (*ReorderRateTagsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_user_rate_f6a3a178ce1b9b29, []int{53}
}
func (m *ReorderRateTagsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReorderRateTagsResp.Unmarshal(m, b)
}
func (m *ReorderRateTagsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReorderRateTagsResp.Marshal(b, m, deterministic)
}
func (dst *ReorderRateTagsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReorderRateTagsResp.Merge(dst, src)
}
func (m *ReorderRateTagsResp) XXX_Size() int {
	return xxx_messageInfo_ReorderRateTagsResp.Size(m)
}
func (m *ReorderRateTagsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReorderRateTagsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReorderRateTagsResp proto.InternalMessageInfo

func (m *ReorderRateTagsResp) GetRateTags() []*GameUserRateTag {
	if m != nil {
		return m.RateTags
	}
	return nil
}

func init() {
	proto.RegisterType((*GetGameUserRateByIdReq)(nil), "game_user_rate.GetGameUserRateByIdReq")
	proto.RegisterType((*GetGameUserRateByIdResp)(nil), "game_user_rate.GetGameUserRateByIdResp")
	proto.RegisterType((*GetGameUserRateListReq)(nil), "game_user_rate.GetGameUserRateListReq")
	proto.RegisterType((*GetGameUserRateListResp)(nil), "game_user_rate.GetGameUserRateListResp")
	proto.RegisterType((*GameUserRateItem)(nil), "game_user_rate.GameUserRateItem")
	proto.RegisterType((*GameUserRateTag)(nil), "game_user_rate.GameUserRateTag")
	proto.RegisterType((*SubmitGameUserRateReq)(nil), "game_user_rate.SubmitGameUserRateReq")
	proto.RegisterType((*SubmitGameUserRateResp)(nil), "game_user_rate.SubmitGameUserRateResp")
	proto.RegisterType((*GetTabByDimensionIdsReq)(nil), "game_user_rate.GetTabByDimensionIdsReq")
	proto.RegisterType((*GetTabByDimensionIdsItem)(nil), "game_user_rate.GetTabByDimensionIdsItem")
	proto.RegisterType((*GetTabByDimensionIdsResp)(nil), "game_user_rate.GetTabByDimensionIdsResp")
	proto.RegisterMapType((map[string]*GetTabByDimensionIdsItem)(nil), "game_user_rate.GetTabByDimensionIdsResp.DimensionTabMapEntry")
	proto.RegisterType((*AddGameUserRateReq)(nil), "game_user_rate.AddGameUserRateReq")
	proto.RegisterType((*AddGameUserRateItem)(nil), "game_user_rate.AddGameUserRateItem")
	proto.RegisterType((*AddGameUserRateResp)(nil), "game_user_rate.AddGameUserRateResp")
	proto.RegisterType((*CheckAddGameUserRateReq)(nil), "game_user_rate.CheckAddGameUserRateReq")
	proto.RegisterType((*CheckAddGameUserRateResp)(nil), "game_user_rate.CheckAddGameUserRateResp")
	proto.RegisterMapType((map[uint32]bool)(nil), "game_user_rate.CheckAddGameUserRateResp.BaseFilterEntry")
	proto.RegisterMapType((map[uint32]bool)(nil), "game_user_rate.CheckAddGameUserRateResp.ChannelTagsFilterEntry")
	proto.RegisterMapType((map[uint32]bool)(nil), "game_user_rate.CheckAddGameUserRateResp.GamePalTagsFilterEntry")
	proto.RegisterType((*BatchSetFirstChatTokenReq)(nil), "game_user_rate.BatchSetFirstChatTokenReq")
	proto.RegisterType((*SetFirstChatTokenReqItem)(nil), "game_user_rate.SetFirstChatTokenReqItem")
	proto.RegisterType((*BatchSetFirstChatTokenResp)(nil), "game_user_rate.BatchSetFirstChatTokenResp")
	proto.RegisterType((*BatchGetFirstChatTokenReq)(nil), "game_user_rate.BatchGetFirstChatTokenReq")
	proto.RegisterType((*GetFirstChatTokenReqItem)(nil), "game_user_rate.GetFirstChatTokenReqItem")
	proto.RegisterType((*BatchGetFirstChatTokenResp)(nil), "game_user_rate.BatchGetFirstChatTokenResp")
	proto.RegisterMapType((map[string]uint32)(nil), "game_user_rate.BatchGetFirstChatTokenResp.ItemsEntry")
	proto.RegisterType((*GetGameUserNotRateCountReq)(nil), "game_user_rate.GetGameUserNotRateCountReq")
	proto.RegisterType((*GetGameUserNotRateCountResp)(nil), "game_user_rate.GetGameUserNotRateCountResp")
	proto.RegisterType((*IsNeedPushAssistReq)(nil), "game_user_rate.IsNeedPushAssistReq")
	proto.RegisterType((*IsNeedPushAssistResp)(nil), "game_user_rate.IsNeedPushAssistResp")
	proto.RegisterType((*GetGameUserPersonalImageReq)(nil), "game_user_rate.GetGameUserPersonalImageReq")
	proto.RegisterType((*GameUserPersonalImageReqItem)(nil), "game_user_rate.GameUserPersonalImageReqItem")
	proto.RegisterType((*GetGameUserPersonalImageResp)(nil), "game_user_rate.GetGameUserPersonalImageResp")
	proto.RegisterMapType((map[string]*GameUserPersonalImageRespItem)(nil), "game_user_rate.GetGameUserPersonalImageResp.ItemsEntry")
	proto.RegisterType((*GameUserPersonalImageRespItem)(nil), "game_user_rate.GameUserPersonalImageRespItem")
	proto.RegisterType((*DimensionItem)(nil), "game_user_rate.DimensionItem")
	proto.RegisterType((*H5Question)(nil), "game_user_rate.H5Question")
	proto.RegisterType((*H5Question_Option)(nil), "game_user_rate.H5Question.Option")
	proto.RegisterType((*GetGameUserSelfRateQuestionsReq)(nil), "game_user_rate.GetGameUserSelfRateQuestionsReq")
	proto.RegisterType((*GetGameUserSelfRateQuestionsResp)(nil), "game_user_rate.GetGameUserSelfRateQuestionsResp")
	proto.RegisterType((*SelfRateResult)(nil), "game_user_rate.SelfRateResult")
	proto.RegisterType((*SubmitGameUserSelfRateReq)(nil), "game_user_rate.SubmitGameUserSelfRateReq")
	proto.RegisterType((*SubmitGameUserSelfRateResp)(nil), "game_user_rate.SubmitGameUserSelfRateResp")
	proto.RegisterType((*GetGameDimensionConfReq)(nil), "game_user_rate.GetGameDimensionConfReq")
	proto.RegisterType((*GameDimensionItem)(nil), "game_user_rate.GameDimensionItem")
	proto.RegisterType((*GetGameDimensionConfResp)(nil), "game_user_rate.GetGameDimensionConfResp")
	proto.RegisterMapType((map[uint32]*GameDimensionItem)(nil), "game_user_rate.GetGameDimensionConfResp.ItemsEntry")
	proto.RegisterType((*AddOtherRateReq)(nil), "game_user_rate.AddOtherRateReq")
	proto.RegisterType((*OtherRateMsg)(nil), "game_user_rate.OtherRateMsg")
	proto.RegisterMapType((map[string]int32)(nil), "game_user_rate.OtherRateMsg.DimensionScoreMapEntry")
	proto.RegisterType((*AddOtherRateResp)(nil), "game_user_rate.AddOtherRateResp")
	proto.RegisterType((*UnionScoreReq)(nil), "game_user_rate.UnionScoreReq")
	proto.RegisterType((*DimensionScoreInfo)(nil), "game_user_rate.DimensionScoreInfo")
	proto.RegisterMapType((map[string]float32)(nil), "game_user_rate.DimensionScoreInfo.DimensionTotalScoreMapEntry")
	proto.RegisterType((*UnionScoreResp)(nil), "game_user_rate.UnionScoreResp")
	proto.RegisterType((*GetGameUserBeRateListReq)(nil), "game_user_rate.GetGameUserBeRateListReq")
	proto.RegisterType((*GetGameUserBeRateListResp)(nil), "game_user_rate.GetGameUserBeRateListResp")
	proto.RegisterType((*GameUserBeRateItem)(nil), "game_user_rate.GameUserBeRateItem")
	proto.RegisterType((*GameUserBeRateItem_LikeItem)(nil), "game_user_rate.GameUserBeRateItem.LikeItem")
	proto.RegisterType((*GameUserBeRateItem_DeductItem)(nil), "game_user_rate.GameUserBeRateItem.DeductItem")
	proto.RegisterType((*DeductDetail)(nil), "game_user_rate.DeductDetail")
	proto.RegisterType((*GetUserReputationScoreReq)(nil), "game_user_rate.GetUserReputationScoreReq")
	proto.RegisterType((*GetUserReputationScoreResp)(nil), "game_user_rate.GetUserReputationScoreResp")
	proto.RegisterType((*ReorderRateTagsReq)(nil), "game_user_rate.ReorderRateTagsReq")
	proto.RegisterType((*ReorderRateTagsResp)(nil), "game_user_rate.ReorderRateTagsResp")
	proto.RegisterEnum("game_user_rate.GameUserRateSourceType", GameUserRateSourceType_name, GameUserRateSourceType_value)
	proto.RegisterEnum("game_user_rate.GameUserRateStatus", GameUserRateStatus_name, GameUserRateStatus_value)
	proto.RegisterEnum("game_user_rate.GameUserRateAddTagType", GameUserRateAddTagType_name, GameUserRateAddTagType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GameUserRateClient is the client API for GameUserRate service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameUserRateClient interface {
	// ////////////////////////// 运营后台的接口 /////////////////////////////
	// 运营后台的接口，接口名前加上Admin区分，对应结构体可放到admin_game_user_rate.proto
	// 新增/修改维度配置
	AdminUpsertDimension(ctx context.Context, in *AdminUpsertDimensionReq, opts ...grpc.CallOption) (*AdminUpsertDimensionResp, error)
	// 获取维度配置列表
	AdminGetDimensions(ctx context.Context, in *AdminGetDimensionsReq, opts ...grpc.CallOption) (*AdminGetDimensionsResp, error)
	// 删除维度配置
	AdminDelDimension(ctx context.Context, in *AdminDelDimensionReq, opts ...grpc.CallOption) (*AdminDelDimensionResp, error)
	// 保存最新房间标签配置
	AdminSaveRateLabelList(ctx context.Context, in *AdminSaveRateLabelListReq, opts ...grpc.CallOption) (*AdminSaveRateLabelListResp, error)
	// 获取房间标签配置列表
	AdminGetRateLabels(ctx context.Context, in *AdminGetRateLabelsReq, opts ...grpc.CallOption) (*AdminGetRateLabelsResp, error)
	// 绑定玩法与维度、标签
	AdminBindGameDimensionLabel(ctx context.Context, in *AdminBindGameDimensionLabelReq, opts ...grpc.CallOption) (*AdminBindGameDimensionLabelResp, error)
	// 获取玩法绑定的维度、标签
	AdminGetGameDimensionLabels(ctx context.Context, in *AdminGetGameDimensionLabelsReq, opts ...grpc.CallOption) (*AdminGetGameDimensionLabelsResp, error)
	// 删除玩法绑定维度、标签关系
	AdminDelGameDimensionLabel(ctx context.Context, in *AdminDelGameDimensionLabelReq, opts ...grpc.CallOption) (*AdminDelGameDimensionLabelResp, error)
	// 添加点评展示与处罚配置
	AddShowRateAndPunishConf(ctx context.Context, in *AddShowRateAndPunishConfReq, opts ...grpc.CallOption) (*AddShowRateAndPunishConfResp, error)
	// 更新点评展示与处罚配置
	UpdateShowRateAndPunishConf(ctx context.Context, in *UpdateShowRateAndPunishConfReq, opts ...grpc.CallOption) (*UpdateShowRateAndPunishConfResp, error)
	// 获取点评展示与处罚配置
	GetShowRateAndPunishConf(ctx context.Context, in *GetShowRateAndPunishConfReq, opts ...grpc.CallOption) (*GetShowRateAndPunishConfResp, error)
	// ////////////////////////// H5调用的接口 /////////////////////////////
	GetGameUserRateById(ctx context.Context, in *GetGameUserRateByIdReq, opts ...grpc.CallOption) (*GetGameUserRateByIdResp, error)
	// 获取用户评价列表
	GetGameUserRateList(ctx context.Context, in *GetGameUserRateListReq, opts ...grpc.CallOption) (*GetGameUserRateListResp, error)
	// 用户提交评价
	SubmitGameUserRate(ctx context.Context, in *SubmitGameUserRateReq, opts ...grpc.CallOption) (*SubmitGameUserRateResp, error)
	// 获取用户开黑形象自评题目
	GetGameUserSelfRateQuestions(ctx context.Context, in *GetGameUserSelfRateQuestionsReq, opts ...grpc.CallOption) (*GetGameUserSelfRateQuestionsResp, error)
	// 用户提交自评结果
	SubmitGameUserSelfRate(ctx context.Context, in *SubmitGameUserSelfRateReq, opts ...grpc.CallOption) (*SubmitGameUserSelfRateResp, error)
	// 根据维度id获取绑定的玩法
	GetTabByDimensionIds(ctx context.Context, in *GetTabByDimensionIdsReq, opts ...grpc.CallOption) (*GetTabByDimensionIdsResp, error)
	// 获取用户收到评价列表
	GetGameUserBeRateList(ctx context.Context, in *GetGameUserBeRateListReq, opts ...grpc.CallOption) (*GetGameUserBeRateListResp, error)
	// 获取推荐评价标签
	ReorderRateTags(ctx context.Context, in *ReorderRateTagsReq, opts ...grpc.CallOption) (*ReorderRateTagsResp, error)
	// ////////////////////////// 客户端调用的接口 /////////////////////////////
	// 触发添加用户评价
	AddGameUserRate(ctx context.Context, in *AddGameUserRateReq, opts ...grpc.CallOption) (*AddGameUserRateResp, error)
	// 预设可添加评价判断
	CheckAddGameUserRate(ctx context.Context, in *CheckAddGameUserRateReq, opts ...grpc.CallOption) (*CheckAddGameUserRateResp, error)
	// 设置搭子IM聊天来源Token
	BatchSetFirstChatToken(ctx context.Context, in *BatchSetFirstChatTokenReq, opts ...grpc.CallOption) (*BatchSetFirstChatTokenResp, error)
	// 设置搭子IM聊天来源Token
	BatchGetFirstChatToken(ctx context.Context, in *BatchGetFirstChatTokenReq, opts ...grpc.CallOption) (*BatchGetFirstChatTokenResp, error)
	// 获取用户未评价数量
	GetGameUserNotRateCount(ctx context.Context, in *GetGameUserNotRateCountReq, opts ...grpc.CallOption) (*GetGameUserNotRateCountResp, error)
	// 是否需要发送tt语音助手
	IsNeedPushAssist(ctx context.Context, in *IsNeedPushAssistReq, opts ...grpc.CallOption) (*IsNeedPushAssistResp, error)
	// 获取用户开黑形象
	GetGameUserPersonalImage(ctx context.Context, in *GetGameUserPersonalImageReq, opts ...grpc.CallOption) (*GetGameUserPersonalImageResp, error)
	// 获取搭子卡维度配置
	GetGameDimensionConf(ctx context.Context, in *GetGameDimensionConfReq, opts ...grpc.CallOption) (*GetGameDimensionConfResp, error)
	AddOtherRate(ctx context.Context, in *AddOtherRateReq, opts ...grpc.CallOption) (*AddOtherRateResp, error)
	UnionScore(ctx context.Context, in *UnionScoreReq, opts ...grpc.CallOption) (*UnionScoreResp, error)
	// 获取点评展示与处罚配置
	GetShowRateAndPunishConfWithCache(ctx context.Context, in *GetShowRateAndPunishConfReq, opts ...grpc.CallOption) (*GetShowRateAndPunishConfResp, error)
	// 获取用户评价信誉分数
	GetUserReputationScore(ctx context.Context, in *GetUserReputationScoreReq, opts ...grpc.CallOption) (*GetUserReputationScoreResp, error)
}

type gameUserRateClient struct {
	cc *grpc.ClientConn
}

func NewGameUserRateClient(cc *grpc.ClientConn) GameUserRateClient {
	return &gameUserRateClient{cc}
}

func (c *gameUserRateClient) AdminUpsertDimension(ctx context.Context, in *AdminUpsertDimensionReq, opts ...grpc.CallOption) (*AdminUpsertDimensionResp, error) {
	out := new(AdminUpsertDimensionResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/AdminUpsertDimension", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) AdminGetDimensions(ctx context.Context, in *AdminGetDimensionsReq, opts ...grpc.CallOption) (*AdminGetDimensionsResp, error) {
	out := new(AdminGetDimensionsResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/AdminGetDimensions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) AdminDelDimension(ctx context.Context, in *AdminDelDimensionReq, opts ...grpc.CallOption) (*AdminDelDimensionResp, error) {
	out := new(AdminDelDimensionResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/AdminDelDimension", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) AdminSaveRateLabelList(ctx context.Context, in *AdminSaveRateLabelListReq, opts ...grpc.CallOption) (*AdminSaveRateLabelListResp, error) {
	out := new(AdminSaveRateLabelListResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/AdminSaveRateLabelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) AdminGetRateLabels(ctx context.Context, in *AdminGetRateLabelsReq, opts ...grpc.CallOption) (*AdminGetRateLabelsResp, error) {
	out := new(AdminGetRateLabelsResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/AdminGetRateLabels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) AdminBindGameDimensionLabel(ctx context.Context, in *AdminBindGameDimensionLabelReq, opts ...grpc.CallOption) (*AdminBindGameDimensionLabelResp, error) {
	out := new(AdminBindGameDimensionLabelResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/AdminBindGameDimensionLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) AdminGetGameDimensionLabels(ctx context.Context, in *AdminGetGameDimensionLabelsReq, opts ...grpc.CallOption) (*AdminGetGameDimensionLabelsResp, error) {
	out := new(AdminGetGameDimensionLabelsResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/AdminGetGameDimensionLabels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) AdminDelGameDimensionLabel(ctx context.Context, in *AdminDelGameDimensionLabelReq, opts ...grpc.CallOption) (*AdminDelGameDimensionLabelResp, error) {
	out := new(AdminDelGameDimensionLabelResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/AdminDelGameDimensionLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) AddShowRateAndPunishConf(ctx context.Context, in *AddShowRateAndPunishConfReq, opts ...grpc.CallOption) (*AddShowRateAndPunishConfResp, error) {
	out := new(AddShowRateAndPunishConfResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/AddShowRateAndPunishConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) UpdateShowRateAndPunishConf(ctx context.Context, in *UpdateShowRateAndPunishConfReq, opts ...grpc.CallOption) (*UpdateShowRateAndPunishConfResp, error) {
	out := new(UpdateShowRateAndPunishConfResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/UpdateShowRateAndPunishConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) GetShowRateAndPunishConf(ctx context.Context, in *GetShowRateAndPunishConfReq, opts ...grpc.CallOption) (*GetShowRateAndPunishConfResp, error) {
	out := new(GetShowRateAndPunishConfResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/GetShowRateAndPunishConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) GetGameUserRateById(ctx context.Context, in *GetGameUserRateByIdReq, opts ...grpc.CallOption) (*GetGameUserRateByIdResp, error) {
	out := new(GetGameUserRateByIdResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/GetGameUserRateById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) GetGameUserRateList(ctx context.Context, in *GetGameUserRateListReq, opts ...grpc.CallOption) (*GetGameUserRateListResp, error) {
	out := new(GetGameUserRateListResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/GetGameUserRateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) SubmitGameUserRate(ctx context.Context, in *SubmitGameUserRateReq, opts ...grpc.CallOption) (*SubmitGameUserRateResp, error) {
	out := new(SubmitGameUserRateResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/SubmitGameUserRate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) GetGameUserSelfRateQuestions(ctx context.Context, in *GetGameUserSelfRateQuestionsReq, opts ...grpc.CallOption) (*GetGameUserSelfRateQuestionsResp, error) {
	out := new(GetGameUserSelfRateQuestionsResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/GetGameUserSelfRateQuestions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) SubmitGameUserSelfRate(ctx context.Context, in *SubmitGameUserSelfRateReq, opts ...grpc.CallOption) (*SubmitGameUserSelfRateResp, error) {
	out := new(SubmitGameUserSelfRateResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/SubmitGameUserSelfRate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) GetTabByDimensionIds(ctx context.Context, in *GetTabByDimensionIdsReq, opts ...grpc.CallOption) (*GetTabByDimensionIdsResp, error) {
	out := new(GetTabByDimensionIdsResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/GetTabByDimensionIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) GetGameUserBeRateList(ctx context.Context, in *GetGameUserBeRateListReq, opts ...grpc.CallOption) (*GetGameUserBeRateListResp, error) {
	out := new(GetGameUserBeRateListResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/GetGameUserBeRateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) ReorderRateTags(ctx context.Context, in *ReorderRateTagsReq, opts ...grpc.CallOption) (*ReorderRateTagsResp, error) {
	out := new(ReorderRateTagsResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/ReorderRateTags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) AddGameUserRate(ctx context.Context, in *AddGameUserRateReq, opts ...grpc.CallOption) (*AddGameUserRateResp, error) {
	out := new(AddGameUserRateResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/AddGameUserRate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) CheckAddGameUserRate(ctx context.Context, in *CheckAddGameUserRateReq, opts ...grpc.CallOption) (*CheckAddGameUserRateResp, error) {
	out := new(CheckAddGameUserRateResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/CheckAddGameUserRate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) BatchSetFirstChatToken(ctx context.Context, in *BatchSetFirstChatTokenReq, opts ...grpc.CallOption) (*BatchSetFirstChatTokenResp, error) {
	out := new(BatchSetFirstChatTokenResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/BatchSetFirstChatToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) BatchGetFirstChatToken(ctx context.Context, in *BatchGetFirstChatTokenReq, opts ...grpc.CallOption) (*BatchGetFirstChatTokenResp, error) {
	out := new(BatchGetFirstChatTokenResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/BatchGetFirstChatToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) GetGameUserNotRateCount(ctx context.Context, in *GetGameUserNotRateCountReq, opts ...grpc.CallOption) (*GetGameUserNotRateCountResp, error) {
	out := new(GetGameUserNotRateCountResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/GetGameUserNotRateCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) IsNeedPushAssist(ctx context.Context, in *IsNeedPushAssistReq, opts ...grpc.CallOption) (*IsNeedPushAssistResp, error) {
	out := new(IsNeedPushAssistResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/IsNeedPushAssist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) GetGameUserPersonalImage(ctx context.Context, in *GetGameUserPersonalImageReq, opts ...grpc.CallOption) (*GetGameUserPersonalImageResp, error) {
	out := new(GetGameUserPersonalImageResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/GetGameUserPersonalImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) GetGameDimensionConf(ctx context.Context, in *GetGameDimensionConfReq, opts ...grpc.CallOption) (*GetGameDimensionConfResp, error) {
	out := new(GetGameDimensionConfResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/GetGameDimensionConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) AddOtherRate(ctx context.Context, in *AddOtherRateReq, opts ...grpc.CallOption) (*AddOtherRateResp, error) {
	out := new(AddOtherRateResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/AddOtherRate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) UnionScore(ctx context.Context, in *UnionScoreReq, opts ...grpc.CallOption) (*UnionScoreResp, error) {
	out := new(UnionScoreResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/UnionScore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) GetShowRateAndPunishConfWithCache(ctx context.Context, in *GetShowRateAndPunishConfReq, opts ...grpc.CallOption) (*GetShowRateAndPunishConfResp, error) {
	out := new(GetShowRateAndPunishConfResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/GetShowRateAndPunishConfWithCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameUserRateClient) GetUserReputationScore(ctx context.Context, in *GetUserReputationScoreReq, opts ...grpc.CallOption) (*GetUserReputationScoreResp, error) {
	out := new(GetUserReputationScoreResp)
	err := c.cc.Invoke(ctx, "/game_user_rate.GameUserRate/GetUserReputationScore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameUserRateServer is the server API for GameUserRate service.
type GameUserRateServer interface {
	// ////////////////////////// 运营后台的接口 /////////////////////////////
	// 运营后台的接口，接口名前加上Admin区分，对应结构体可放到admin_game_user_rate.proto
	// 新增/修改维度配置
	AdminUpsertDimension(context.Context, *AdminUpsertDimensionReq) (*AdminUpsertDimensionResp, error)
	// 获取维度配置列表
	AdminGetDimensions(context.Context, *AdminGetDimensionsReq) (*AdminGetDimensionsResp, error)
	// 删除维度配置
	AdminDelDimension(context.Context, *AdminDelDimensionReq) (*AdminDelDimensionResp, error)
	// 保存最新房间标签配置
	AdminSaveRateLabelList(context.Context, *AdminSaveRateLabelListReq) (*AdminSaveRateLabelListResp, error)
	// 获取房间标签配置列表
	AdminGetRateLabels(context.Context, *AdminGetRateLabelsReq) (*AdminGetRateLabelsResp, error)
	// 绑定玩法与维度、标签
	AdminBindGameDimensionLabel(context.Context, *AdminBindGameDimensionLabelReq) (*AdminBindGameDimensionLabelResp, error)
	// 获取玩法绑定的维度、标签
	AdminGetGameDimensionLabels(context.Context, *AdminGetGameDimensionLabelsReq) (*AdminGetGameDimensionLabelsResp, error)
	// 删除玩法绑定维度、标签关系
	AdminDelGameDimensionLabel(context.Context, *AdminDelGameDimensionLabelReq) (*AdminDelGameDimensionLabelResp, error)
	// 添加点评展示与处罚配置
	AddShowRateAndPunishConf(context.Context, *AddShowRateAndPunishConfReq) (*AddShowRateAndPunishConfResp, error)
	// 更新点评展示与处罚配置
	UpdateShowRateAndPunishConf(context.Context, *UpdateShowRateAndPunishConfReq) (*UpdateShowRateAndPunishConfResp, error)
	// 获取点评展示与处罚配置
	GetShowRateAndPunishConf(context.Context, *GetShowRateAndPunishConfReq) (*GetShowRateAndPunishConfResp, error)
	// ////////////////////////// H5调用的接口 /////////////////////////////
	GetGameUserRateById(context.Context, *GetGameUserRateByIdReq) (*GetGameUserRateByIdResp, error)
	// 获取用户评价列表
	GetGameUserRateList(context.Context, *GetGameUserRateListReq) (*GetGameUserRateListResp, error)
	// 用户提交评价
	SubmitGameUserRate(context.Context, *SubmitGameUserRateReq) (*SubmitGameUserRateResp, error)
	// 获取用户开黑形象自评题目
	GetGameUserSelfRateQuestions(context.Context, *GetGameUserSelfRateQuestionsReq) (*GetGameUserSelfRateQuestionsResp, error)
	// 用户提交自评结果
	SubmitGameUserSelfRate(context.Context, *SubmitGameUserSelfRateReq) (*SubmitGameUserSelfRateResp, error)
	// 根据维度id获取绑定的玩法
	GetTabByDimensionIds(context.Context, *GetTabByDimensionIdsReq) (*GetTabByDimensionIdsResp, error)
	// 获取用户收到评价列表
	GetGameUserBeRateList(context.Context, *GetGameUserBeRateListReq) (*GetGameUserBeRateListResp, error)
	// 获取推荐评价标签
	ReorderRateTags(context.Context, *ReorderRateTagsReq) (*ReorderRateTagsResp, error)
	// ////////////////////////// 客户端调用的接口 /////////////////////////////
	// 触发添加用户评价
	AddGameUserRate(context.Context, *AddGameUserRateReq) (*AddGameUserRateResp, error)
	// 预设可添加评价判断
	CheckAddGameUserRate(context.Context, *CheckAddGameUserRateReq) (*CheckAddGameUserRateResp, error)
	// 设置搭子IM聊天来源Token
	BatchSetFirstChatToken(context.Context, *BatchSetFirstChatTokenReq) (*BatchSetFirstChatTokenResp, error)
	// 设置搭子IM聊天来源Token
	BatchGetFirstChatToken(context.Context, *BatchGetFirstChatTokenReq) (*BatchGetFirstChatTokenResp, error)
	// 获取用户未评价数量
	GetGameUserNotRateCount(context.Context, *GetGameUserNotRateCountReq) (*GetGameUserNotRateCountResp, error)
	// 是否需要发送tt语音助手
	IsNeedPushAssist(context.Context, *IsNeedPushAssistReq) (*IsNeedPushAssistResp, error)
	// 获取用户开黑形象
	GetGameUserPersonalImage(context.Context, *GetGameUserPersonalImageReq) (*GetGameUserPersonalImageResp, error)
	// 获取搭子卡维度配置
	GetGameDimensionConf(context.Context, *GetGameDimensionConfReq) (*GetGameDimensionConfResp, error)
	AddOtherRate(context.Context, *AddOtherRateReq) (*AddOtherRateResp, error)
	UnionScore(context.Context, *UnionScoreReq) (*UnionScoreResp, error)
	// 获取点评展示与处罚配置
	GetShowRateAndPunishConfWithCache(context.Context, *GetShowRateAndPunishConfReq) (*GetShowRateAndPunishConfResp, error)
	// 获取用户评价信誉分数
	GetUserReputationScore(context.Context, *GetUserReputationScoreReq) (*GetUserReputationScoreResp, error)
}

func RegisterGameUserRateServer(s *grpc.Server, srv GameUserRateServer) {
	s.RegisterService(&_GameUserRate_serviceDesc, srv)
}

func _GameUserRate_AdminUpsertDimension_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminUpsertDimensionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).AdminUpsertDimension(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/AdminUpsertDimension",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).AdminUpsertDimension(ctx, req.(*AdminUpsertDimensionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_AdminGetDimensions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminGetDimensionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).AdminGetDimensions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/AdminGetDimensions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).AdminGetDimensions(ctx, req.(*AdminGetDimensionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_AdminDelDimension_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminDelDimensionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).AdminDelDimension(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/AdminDelDimension",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).AdminDelDimension(ctx, req.(*AdminDelDimensionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_AdminSaveRateLabelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminSaveRateLabelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).AdminSaveRateLabelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/AdminSaveRateLabelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).AdminSaveRateLabelList(ctx, req.(*AdminSaveRateLabelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_AdminGetRateLabels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminGetRateLabelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).AdminGetRateLabels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/AdminGetRateLabels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).AdminGetRateLabels(ctx, req.(*AdminGetRateLabelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_AdminBindGameDimensionLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminBindGameDimensionLabelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).AdminBindGameDimensionLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/AdminBindGameDimensionLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).AdminBindGameDimensionLabel(ctx, req.(*AdminBindGameDimensionLabelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_AdminGetGameDimensionLabels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminGetGameDimensionLabelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).AdminGetGameDimensionLabels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/AdminGetGameDimensionLabels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).AdminGetGameDimensionLabels(ctx, req.(*AdminGetGameDimensionLabelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_AdminDelGameDimensionLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminDelGameDimensionLabelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).AdminDelGameDimensionLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/AdminDelGameDimensionLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).AdminDelGameDimensionLabel(ctx, req.(*AdminDelGameDimensionLabelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_AddShowRateAndPunishConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddShowRateAndPunishConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).AddShowRateAndPunishConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/AddShowRateAndPunishConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).AddShowRateAndPunishConf(ctx, req.(*AddShowRateAndPunishConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_UpdateShowRateAndPunishConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateShowRateAndPunishConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).UpdateShowRateAndPunishConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/UpdateShowRateAndPunishConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).UpdateShowRateAndPunishConf(ctx, req.(*UpdateShowRateAndPunishConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_GetShowRateAndPunishConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetShowRateAndPunishConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).GetShowRateAndPunishConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/GetShowRateAndPunishConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).GetShowRateAndPunishConf(ctx, req.(*GetShowRateAndPunishConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_GetGameUserRateById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameUserRateByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).GetGameUserRateById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/GetGameUserRateById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).GetGameUserRateById(ctx, req.(*GetGameUserRateByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_GetGameUserRateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameUserRateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).GetGameUserRateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/GetGameUserRateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).GetGameUserRateList(ctx, req.(*GetGameUserRateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_SubmitGameUserRate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitGameUserRateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).SubmitGameUserRate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/SubmitGameUserRate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).SubmitGameUserRate(ctx, req.(*SubmitGameUserRateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_GetGameUserSelfRateQuestions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameUserSelfRateQuestionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).GetGameUserSelfRateQuestions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/GetGameUserSelfRateQuestions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).GetGameUserSelfRateQuestions(ctx, req.(*GetGameUserSelfRateQuestionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_SubmitGameUserSelfRate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitGameUserSelfRateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).SubmitGameUserSelfRate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/SubmitGameUserSelfRate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).SubmitGameUserSelfRate(ctx, req.(*SubmitGameUserSelfRateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_GetTabByDimensionIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTabByDimensionIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).GetTabByDimensionIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/GetTabByDimensionIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).GetTabByDimensionIds(ctx, req.(*GetTabByDimensionIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_GetGameUserBeRateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameUserBeRateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).GetGameUserBeRateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/GetGameUserBeRateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).GetGameUserBeRateList(ctx, req.(*GetGameUserBeRateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_ReorderRateTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReorderRateTagsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).ReorderRateTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/ReorderRateTags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).ReorderRateTags(ctx, req.(*ReorderRateTagsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_AddGameUserRate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddGameUserRateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).AddGameUserRate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/AddGameUserRate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).AddGameUserRate(ctx, req.(*AddGameUserRateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_CheckAddGameUserRate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckAddGameUserRateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).CheckAddGameUserRate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/CheckAddGameUserRate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).CheckAddGameUserRate(ctx, req.(*CheckAddGameUserRateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_BatchSetFirstChatToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetFirstChatTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).BatchSetFirstChatToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/BatchSetFirstChatToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).BatchSetFirstChatToken(ctx, req.(*BatchSetFirstChatTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_BatchGetFirstChatToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetFirstChatTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).BatchGetFirstChatToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/BatchGetFirstChatToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).BatchGetFirstChatToken(ctx, req.(*BatchGetFirstChatTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_GetGameUserNotRateCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameUserNotRateCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).GetGameUserNotRateCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/GetGameUserNotRateCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).GetGameUserNotRateCount(ctx, req.(*GetGameUserNotRateCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_IsNeedPushAssist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsNeedPushAssistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).IsNeedPushAssist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/IsNeedPushAssist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).IsNeedPushAssist(ctx, req.(*IsNeedPushAssistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_GetGameUserPersonalImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameUserPersonalImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).GetGameUserPersonalImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/GetGameUserPersonalImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).GetGameUserPersonalImage(ctx, req.(*GetGameUserPersonalImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_GetGameDimensionConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameDimensionConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).GetGameDimensionConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/GetGameDimensionConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).GetGameDimensionConf(ctx, req.(*GetGameDimensionConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_AddOtherRate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddOtherRateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).AddOtherRate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/AddOtherRate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).AddOtherRate(ctx, req.(*AddOtherRateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_UnionScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnionScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).UnionScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/UnionScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).UnionScore(ctx, req.(*UnionScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_GetShowRateAndPunishConfWithCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetShowRateAndPunishConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).GetShowRateAndPunishConfWithCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/GetShowRateAndPunishConfWithCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).GetShowRateAndPunishConfWithCache(ctx, req.(*GetShowRateAndPunishConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameUserRate_GetUserReputationScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserReputationScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameUserRateServer).GetUserReputationScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_user_rate.GameUserRate/GetUserReputationScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameUserRateServer).GetUserReputationScore(ctx, req.(*GetUserReputationScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameUserRate_serviceDesc = grpc.ServiceDesc{
	ServiceName: "game_user_rate.GameUserRate",
	HandlerType: (*GameUserRateServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AdminUpsertDimension",
			Handler:    _GameUserRate_AdminUpsertDimension_Handler,
		},
		{
			MethodName: "AdminGetDimensions",
			Handler:    _GameUserRate_AdminGetDimensions_Handler,
		},
		{
			MethodName: "AdminDelDimension",
			Handler:    _GameUserRate_AdminDelDimension_Handler,
		},
		{
			MethodName: "AdminSaveRateLabelList",
			Handler:    _GameUserRate_AdminSaveRateLabelList_Handler,
		},
		{
			MethodName: "AdminGetRateLabels",
			Handler:    _GameUserRate_AdminGetRateLabels_Handler,
		},
		{
			MethodName: "AdminBindGameDimensionLabel",
			Handler:    _GameUserRate_AdminBindGameDimensionLabel_Handler,
		},
		{
			MethodName: "AdminGetGameDimensionLabels",
			Handler:    _GameUserRate_AdminGetGameDimensionLabels_Handler,
		},
		{
			MethodName: "AdminDelGameDimensionLabel",
			Handler:    _GameUserRate_AdminDelGameDimensionLabel_Handler,
		},
		{
			MethodName: "AddShowRateAndPunishConf",
			Handler:    _GameUserRate_AddShowRateAndPunishConf_Handler,
		},
		{
			MethodName: "UpdateShowRateAndPunishConf",
			Handler:    _GameUserRate_UpdateShowRateAndPunishConf_Handler,
		},
		{
			MethodName: "GetShowRateAndPunishConf",
			Handler:    _GameUserRate_GetShowRateAndPunishConf_Handler,
		},
		{
			MethodName: "GetGameUserRateById",
			Handler:    _GameUserRate_GetGameUserRateById_Handler,
		},
		{
			MethodName: "GetGameUserRateList",
			Handler:    _GameUserRate_GetGameUserRateList_Handler,
		},
		{
			MethodName: "SubmitGameUserRate",
			Handler:    _GameUserRate_SubmitGameUserRate_Handler,
		},
		{
			MethodName: "GetGameUserSelfRateQuestions",
			Handler:    _GameUserRate_GetGameUserSelfRateQuestions_Handler,
		},
		{
			MethodName: "SubmitGameUserSelfRate",
			Handler:    _GameUserRate_SubmitGameUserSelfRate_Handler,
		},
		{
			MethodName: "GetTabByDimensionIds",
			Handler:    _GameUserRate_GetTabByDimensionIds_Handler,
		},
		{
			MethodName: "GetGameUserBeRateList",
			Handler:    _GameUserRate_GetGameUserBeRateList_Handler,
		},
		{
			MethodName: "ReorderRateTags",
			Handler:    _GameUserRate_ReorderRateTags_Handler,
		},
		{
			MethodName: "AddGameUserRate",
			Handler:    _GameUserRate_AddGameUserRate_Handler,
		},
		{
			MethodName: "CheckAddGameUserRate",
			Handler:    _GameUserRate_CheckAddGameUserRate_Handler,
		},
		{
			MethodName: "BatchSetFirstChatToken",
			Handler:    _GameUserRate_BatchSetFirstChatToken_Handler,
		},
		{
			MethodName: "BatchGetFirstChatToken",
			Handler:    _GameUserRate_BatchGetFirstChatToken_Handler,
		},
		{
			MethodName: "GetGameUserNotRateCount",
			Handler:    _GameUserRate_GetGameUserNotRateCount_Handler,
		},
		{
			MethodName: "IsNeedPushAssist",
			Handler:    _GameUserRate_IsNeedPushAssist_Handler,
		},
		{
			MethodName: "GetGameUserPersonalImage",
			Handler:    _GameUserRate_GetGameUserPersonalImage_Handler,
		},
		{
			MethodName: "GetGameDimensionConf",
			Handler:    _GameUserRate_GetGameDimensionConf_Handler,
		},
		{
			MethodName: "AddOtherRate",
			Handler:    _GameUserRate_AddOtherRate_Handler,
		},
		{
			MethodName: "UnionScore",
			Handler:    _GameUserRate_UnionScore_Handler,
		},
		{
			MethodName: "GetShowRateAndPunishConfWithCache",
			Handler:    _GameUserRate_GetShowRateAndPunishConfWithCache_Handler,
		},
		{
			MethodName: "GetUserReputationScore",
			Handler:    _GameUserRate_GetUserReputationScore_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/game-user-rate/game-user-rate.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/game-user-rate/game-user-rate.proto", fileDescriptor_game_user_rate_f6a3a178ce1b9b29)
}

var fileDescriptor_game_user_rate_f6a3a178ce1b9b29 = []byte{
	// 3162 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x5a, 0xdd, 0x73, 0xdb, 0xc6,
	0xb5, 0x37, 0x49, 0x7d, 0xf1, 0xe8, 0x8b, 0x5e, 0xc9, 0x36, 0x4d, 0xd9, 0x91, 0x0c, 0x7f, 0x48,
	0x91, 0x23, 0xda, 0xd7, 0xbe, 0x99, 0x38, 0xb9, 0x37, 0x4e, 0x28, 0x89, 0xa6, 0x99, 0xd8, 0x32,
	0x2f, 0x48, 0x25, 0x37, 0x4d, 0xa6, 0x18, 0x88, 0x58, 0x91, 0x18, 0x91, 0x00, 0x84, 0x5d, 0x3a,
	0xd6, 0x43, 0x3b, 0x9d, 0xe9, 0x5f, 0x90, 0x99, 0x76, 0xa6, 0x79, 0x68, 0x5f, 0xf2, 0xde, 0xbf,
	0xa4, 0xed, 0x63, 0xff, 0x88, 0x3e, 0x75, 0xda, 0xf7, 0x4e, 0x67, 0x17, 0xdf, 0xc0, 0x02, 0x84,
	0x93, 0xe9, 0x1b, 0x70, 0xf0, 0xdb, 0x73, 0xf6, 0x9c, 0x3d, 0x7b, 0xce, 0xd9, 0xb3, 0x80, 0x47,
	0x94, 0x3e, 0x38, 0x9f, 0xe8, 0xfd, 0x33, 0xa2, 0x8f, 0x5e, 0x63, 0xfb, 0xc1, 0x40, 0x1d, 0xe3,
	0xbd, 0x09, 0xc1, 0xf6, 0x9e, 0xad, 0x52, 0x1c, 0x7b, 0xad, 0x5b, 0xb6, 0x49, 0x4d, 0xb4, 0xc2,
	0xa8, 0x0a, 0xa3, 0x2a, 0x8c, 0x5a, 0x7b, 0x92, 0xcd, 0x43, 0xd5, 0xc6, 0xba, 0xb1, 0x27, 0xe2,
	0x24, 0xed, 0xc0, 0xd5, 0x16, 0xa6, 0x2d, 0x75, 0x8c, 0x8f, 0x09, 0xb6, 0x65, 0x95, 0xe2, 0xfd,
	0x8b, 0xb6, 0x26, 0xe3, 0x73, 0xb4, 0x02, 0x45, 0x5d, 0xab, 0x16, 0xb6, 0x0a, 0x3b, 0x65, 0xb9,
	0xa8, 0x6b, 0xd2, 0x2b, 0xb8, 0x26, 0x44, 0x12, 0x0b, 0xfd, 0x37, 0xcc, 0x68, 0x2a, 0x55, 0x39,
	0x78, 0xf1, 0xd1, 0x56, 0x3d, 0x3a, 0xbb, 0x7a, 0x78, 0x4c, 0x9b, 0xe2, 0xb1, 0xcc, 0xd1, 0xd2,
	0x1f, 0x0a, 0x09, 0xd9, 0x2f, 0x74, 0x42, 0x99, 0xec, 0x0a, 0x94, 0x26, 0xae, 0xf0, 0x65, 0x99,
	0x3d, 0xa2, 0xa7, 0x30, 0x47, 0xcc, 0x89, 0xdd, 0xc7, 0xd5, 0xe2, 0x56, 0x61, 0x67, 0xe5, 0xd1,
	0xbd, 0x2c, 0x21, 0x5d, 0x8e, 0xec, 0x5d, 0x58, 0x58, 0x76, 0x47, 0xa1, 0xeb, 0xb0, 0xc0, 0x60,
	0x0a, 0x63, 0x5b, 0xe2, 0x6c, 0xe7, 0xd9, 0xfb, 0xb1, 0xae, 0xa1, 0x6b, 0x30, 0x3f, 0x52, 0x09,
	0x55, 0x74, 0xad, 0x3a, 0xc3, 0xb5, 0x9d, 0x63, 0xaf, 0x6d, 0x4d, 0xb2, 0x12, 0x1a, 0x3b, 0xf3,
	0x8b, 0x68, 0x5c, 0xca, 0xaf, 0x31, 0xda, 0x84, 0xc5, 0x91, 0xa9, 0x6a, 0xca, 0xa9, 0x6e, 0xe8,
	0x64, 0xc8, 0x35, 0x59, 0x90, 0x81, 0x91, 0x9e, 0x71, 0x8a, 0xf4, 0x7d, 0x09, 0x2a, 0xf1, 0xb1,
	0xf1, 0x85, 0xf0, 0x8c, 0x53, 0x0c, 0x8c, 0x93, 0xa1, 0xdc, 0x15, 0x98, 0xa3, 0xea, 0x89, 0xa7,
	0xdb, 0xb2, 0x3c, 0x4b, 0xd5, 0x93, 0x76, 0xd8, 0x9c, 0xb3, 0x3f, 0xca, 0x9c, 0x9b, 0xb0, 0xd8,
	0xb7, 0x31, 0x93, 0x49, 0xf5, 0x31, 0xae, 0xce, 0x6d, 0x15, 0x76, 0x66, 0x64, 0x70, 0x48, 0x3d,
	0x7d, 0x8c, 0xd1, 0x4d, 0x00, 0xdd, 0xa0, 0xb6, 0xa9, 0x50, 0xdd, 0x22, 0xd5, 0x79, 0x3e, 0xf9,
	0x32, 0xa7, 0xf4, 0x74, 0x8b, 0xa0, 0x8f, 0x60, 0x8e, 0x50, 0x95, 0x4e, 0x48, 0x75, 0x81, 0xcb,
	0x97, 0x32, 0xe5, 0x73, 0xa4, 0xec, 0x8e, 0x40, 0x35, 0x58, 0x50, 0x29, 0xd5, 0xe9, 0x44, 0xc3,
	0xd5, 0x32, 0x57, 0xca, 0x7f, 0x47, 0x8f, 0x61, 0x86, 0xaa, 0x03, 0x52, 0x05, 0xbe, 0x2e, 0x9b,
	0x59, 0x5c, 0x7b, 0xea, 0x40, 0xe6, 0x60, 0x74, 0x07, 0x56, 0x7c, 0x88, 0x42, 0xf1, 0x1b, 0x5a,
	0x5d, 0xe4, 0xf3, 0x5d, 0x9a, 0x78, 0x50, 0xfc, 0x86, 0x4a, 0x1d, 0x58, 0x8d, 0x0d, 0x4f, 0xac,
	0x0c, 0x82, 0x19, 0x43, 0x1d, 0x3b, 0x2e, 0x5a, 0x96, 0xf9, 0x33, 0x5b, 0x1b, 0xaa, 0x0e, 0x14,
	0x7a, 0x61, 0x61, 0x6f, 0x6d, 0xa8, 0x3a, 0x60, 0xc6, 0x94, 0xfe, 0x55, 0x80, 0x2b, 0xdd, 0xc9,
	0xc9, 0x58, 0x8f, 0xf8, 0x98, 0x60, 0xef, 0x45, 0x54, 0x2e, 0xc6, 0x54, 0xfe, 0x14, 0x16, 0x09,
	0x1e, 0xe1, 0x3e, 0x55, 0xb8, 0xe6, 0xa5, 0x7c, 0x9a, 0x83, 0x33, 0xa6, 0xc7, 0xf4, 0x77, 0x1d,
	0x6a, 0x26, 0x70, 0xa8, 0xa4, 0x45, 0x66, 0x93, 0x16, 0x61, 0x28, 0x9d, 0x28, 0x64, 0xa8, 0xe3,
	0x91, 0xa6, 0x58, 0x2a, 0x21, 0xdc, 0x0f, 0x16, 0xe4, 0x25, 0x9d, 0x74, 0x39, 0xb1, 0xa3, 0x12,
	0xc2, 0x0c, 0x30, 0x54, 0x89, 0x62, 0xeb, 0xe4, 0x8c, 0xfb, 0xc1, 0x82, 0x3c, 0x3f, 0x54, 0x89,
	0xac, 0x93, 0x33, 0xe9, 0x8f, 0x05, 0xb8, 0x2a, 0x32, 0x00, 0xb1, 0xd0, 0x5d, 0x58, 0xd5, 0x89,
	0x62, 0x60, 0xac, 0x29, 0xaa, 0xa6, 0x29, 0xf8, 0x8d, 0xc5, 0xcd, 0xc1, 0x99, 0x1f, 0x61, 0xac,
	0x35, 0x34, 0xad, 0xf9, 0x86, 0xef, 0x43, 0x9d, 0xe2, 0x31, 0x37, 0x4a, 0xae, 0x7d, 0xc8, 0xd0,
	0xe8, 0x03, 0xb8, 0xae, 0x13, 0x47, 0x39, 0x8e, 0x8d, 0x88, 0x29, 0x71, 0x31, 0xeb, 0x3a, 0x61,
	0x83, 0xd8, 0xe0, 0x40, 0x9c, 0x74, 0x9f, 0x47, 0x84, 0x9e, 0x7a, 0xb2, 0x7f, 0x71, 0xa8, 0x8f,
	0xb1, 0x41, 0x74, 0xd3, 0x68, 0x6b, 0xc4, 0x0d, 0x59, 0xba, 0x46, 0x78, 0x40, 0x28, 0xcb, 0xec,
	0x51, 0x7a, 0x0c, 0x55, 0x11, 0x98, 0xef, 0xe9, 0x6b, 0x30, 0xef, 0x6c, 0x4b, 0x67, 0xc4, 0xb2,
	0x3c, 0xc7, 0xf7, 0x25, 0x91, 0xfe, 0x59, 0x10, 0x8f, 0xe2, 0x46, 0xd1, 0xe1, 0xb2, 0xe6, 0xd1,
	0x14, 0x36, 0x7e, 0xac, 0x5a, 0x6e, 0x08, 0xfa, 0x38, 0xa1, 0x7a, 0x0a, 0x93, 0xba, 0x4f, 0xe8,
	0xa9, 0x27, 0x2f, 0x55, 0xab, 0x69, 0x50, 0xfb, 0x42, 0x5e, 0xd5, 0xa2, 0xd4, 0xda, 0x08, 0xd6,
	0x45, 0x40, 0xa6, 0xe6, 0x19, 0xbe, 0x70, 0x5d, 0x93, 0x3d, 0xa2, 0xa7, 0x30, 0xfb, 0x5a, 0x1d,
	0x4d, 0xb0, 0xbb, 0x06, 0x3b, 0x79, 0x26, 0xc2, 0xd7, 0xc2, 0x19, 0xf6, 0x51, 0xf1, 0x49, 0x41,
	0x52, 0x01, 0x35, 0x34, 0x2d, 0xbe, 0x0b, 0x92, 0x59, 0xe0, 0x43, 0x98, 0x65, 0x0b, 0x48, 0xaa,
	0x45, 0xae, 0xf4, 0xed, 0xb8, 0xac, 0x18, 0x13, 0x47, 0x0c, 0x1f, 0x21, 0xfd, 0xa5, 0x00, 0x6b,
	0x82, 0xcf, 0xe8, 0xaa, 0x1f, 0x09, 0x1d, 0x39, 0xa2, 0x84, 0x51, 0x8c, 0xc6, 0xd4, 0xdb, 0xb0,
	0xac, 0x1b, 0x14, 0xdb, 0x2a, 0xdb, 0x73, 0x2c, 0xfc, 0x95, 0x78, 0xf8, 0x5b, 0xf2, 0x88, 0x3c,
	0x00, 0xa6, 0x04, 0xde, 0xe7, 0xb0, 0xc4, 0x1c, 0xcd, 0x0f, 0x09, 0xb3, 0x5b, 0xa5, 0x69, 0xe1,
	0xb7, 0xa1, 0x69, 0x3d, 0x27, 0x62, 0xc8, 0xa0, 0xfa, 0xcf, 0x52, 0x27, 0xa1, 0x0f, 0xf7, 0x91,
	0x9f, 0x60, 0x22, 0x03, 0xae, 0x1d, 0x0c, 0x71, 0xff, 0x2c, 0xd7, 0x52, 0xdc, 0x81, 0x15, 0x82,
	0x47, 0xa7, 0x8a, 0x86, 0x5f, 0xeb, 0x7d, 0xac, 0xb8, 0x56, 0x2a, 0xcb, 0x4b, 0x8c, 0x7a, 0xc8,
	0x89, 0x6d, 0x0d, 0x6d, 0x40, 0xd9, 0xb3, 0xa2, 0x13, 0x9a, 0x96, 0xe5, 0x05, 0xd7, 0x8c, 0x44,
	0xfa, 0x61, 0x06, 0xaa, 0x62, 0x81, 0xc4, 0x42, 0x5f, 0xc1, 0xe2, 0x89, 0x4a, 0xb0, 0x72, 0xaa,
	0x8f, 0x28, 0xb6, 0x5d, 0x2f, 0x7f, 0x12, 0xd7, 0x26, 0x6d, 0x78, 0x7d, 0x5f, 0x25, 0xf8, 0x19,
	0x1f, 0xea, 0x38, 0x38, 0x9c, 0xf8, 0x04, 0x64, 0xc2, 0x5a, 0x7f, 0xa8, 0x1a, 0x06, 0x1e, 0xf1,
	0x90, 0xe9, 0x89, 0x70, 0x0c, 0xf6, 0x49, 0x6e, 0x11, 0x07, 0x0e, 0x0f, 0x16, 0x42, 0xc3, 0x92,
	0x2e, 0xf7, 0xe3, 0x74, 0x64, 0xc1, 0x3a, 0x67, 0x6a, 0xa9, 0x51, 0x89, 0xa5, 0xb7, 0x94, 0xc8,
	0x08, 0x1d, 0x35, 0x29, 0x71, 0x10, 0xa7, 0xd7, 0x3e, 0x86, 0xd5, 0x98, 0x05, 0xc2, 0x3b, 0x77,
	0xd9, 0xd9, 0xb9, 0xeb, 0xe1, 0x9d, 0xbb, 0x10, 0xda, 0x8f, 0xb5, 0x43, 0xb8, 0x2a, 0xd6, 0xee,
	0x6d, 0xb9, 0x88, 0x67, 0xfc, 0x36, 0x5c, 0xa4, 0xaf, 0xe1, 0xfa, 0xbe, 0x4a, 0xfb, 0xc3, 0x2e,
	0xa6, 0xcf, 0x74, 0x9b, 0xd0, 0x83, 0xa1, 0x4a, 0x7b, 0xe6, 0x19, 0x36, 0x98, 0x5f, 0x3e, 0xf5,
	0xbc, 0xdd, 0xf1, 0x8f, 0x44, 0xf0, 0x11, 0x0d, 0x0a, 0xbb, 0xfc, 0xcf, 0xa1, 0x9a, 0x06, 0x11,
	0xf8, 0x7c, 0x46, 0x4c, 0x08, 0xb6, 0x7b, 0x29, 0xb4, 0xdd, 0xa5, 0x1b, 0x50, 0x4b, 0x9b, 0x3c,
	0xb1, 0x7c, 0xd5, 0x5a, 0x3f, 0x46, 0xb5, 0xd6, 0x14, 0xd5, 0x5a, 0x3c, 0x91, 0xfc, 0x74, 0xd5,
	0xa4, 0x1f, 0x0a, 0xae, 0x12, 0x2d, 0x91, 0x12, 0xe8, 0xf3, 0xe8, 0x3c, 0xdf, 0x8f, 0xcf, 0x33,
	0x7d, 0x68, 0x9d, 0xcd, 0x85, 0x38, 0x3e, 0xec, 0xf0, 0xa8, 0x3d, 0x01, 0x08, 0x88, 0x82, 0x64,
	0x13, 0x71, 0x93, 0xe5, 0xb0, 0x9b, 0x0c, 0xa1, 0x16, 0x2a, 0xd6, 0x8f, 0x4c, 0xca, 0x76, 0xcd,
	0x81, 0x39, 0x31, 0x52, 0x0e, 0x14, 0xd7, 0x80, 0x2b, 0x18, 0x04, 0xae, 0x39, 0xf6, 0xda, 0xd6,
	0xe2, 0xa5, 0x6d, 0x29, 0x5e, 0xda, 0x4a, 0xbf, 0x2d, 0xc0, 0x46, 0xaa, 0x28, 0x62, 0xb1, 0xc8,
	0x68, 0x98, 0xd4, 0x29, 0x2f, 0xfa, 0x8c, 0xea, 0x8a, 0x5d, 0x32, 0x42, 0x48, 0x1f, 0x85, 0x55,
	0xcd, 0x45, 0x15, 0x03, 0x14, 0x56, 0x35, 0x07, 0xb5, 0x0b, 0x97, 0x4f, 0x99, 0xdd, 0x14, 0x1b,
	0xf7, 0x4d, 0x5b, 0x73, 0xa6, 0xc4, 0x12, 0x4a, 0x49, 0x5e, 0xe5, 0x1f, 0x64, 0x4e, 0xe7, 0xf3,
	0xda, 0x86, 0xb5, 0x36, 0xaf, 0x8d, 0x3a, 0x13, 0x32, 0x6c, 0x10, 0x92, 0x76, 0x96, 0x92, 0xea,
	0xb0, 0x9e, 0x04, 0x12, 0x8b, 0xa5, 0x42, 0x1b, 0x93, 0xc9, 0x88, 0xba, 0xa5, 0x96, 0xfb, 0x26,
	0x29, 0x11, 0x7d, 0x3b, 0xd8, 0x26, 0xa6, 0xa1, 0x8e, 0xda, 0x63, 0x75, 0xc0, 0x73, 0xc3, 0xa7,
	0x6e, 0x0d, 0xe6, 0xac, 0xff, 0x7b, 0x69, 0xa9, 0x2c, 0x3e, 0x2e, 0xa8, 0xc7, 0xa4, 0x16, 0xdc,
	0xc8, 0x42, 0x09, 0x56, 0x2f, 0xd8, 0x6e, 0xc5, 0xf0, 0x76, 0xfb, 0x6b, 0x01, 0x6e, 0xa4, 0x4f,
	0x95, 0x58, 0xe8, 0x65, 0xd4, 0x59, 0x3f, 0x10, 0x6c, 0xaa, 0xd4, 0xc1, 0x02, 0x77, 0x1d, 0x4c,
	0x71, 0xd7, 0x83, 0x68, 0x6d, 0xb4, 0x97, 0xd3, 0x36, 0xc4, 0x8a, 0x17, 0x48, 0xdf, 0x17, 0xe1,
	0x66, 0x26, 0x18, 0xfd, 0x57, 0x64, 0x15, 0x6e, 0xc6, 0x25, 0x05, 0xd5, 0x57, 0x50, 0x06, 0x6f,
	0xc2, 0x22, 0x35, 0xa9, 0x3a, 0x52, 0x48, 0xdf, 0xb4, 0x9d, 0x39, 0x16, 0x65, 0xe0, 0xa4, 0x2e,
	0xa3, 0x30, 0x1f, 0xe5, 0x6c, 0xce, 0xf0, 0x85, 0xf2, 0xad, 0x69, 0xf3, 0x14, 0xce, 0x73, 0x3c,
	0xa3, 0x7e, 0x8e, 0x2f, 0xbe, 0x64, 0x34, 0xb4, 0x07, 0x6b, 0x51, 0x94, 0xa2, 0x61, 0xd2, 0x77,
	0xcf, 0xd2, 0x95, 0x30, 0xf4, 0x10, 0x93, 0x3e, 0x3b, 0x19, 0xf2, 0xc2, 0x81, 0x1f, 0x06, 0xdd,
	0x73, 0x45, 0x99, 0x51, 0xda, 0x8c, 0x80, 0x1e, 0xc2, 0x3a, 0xd1, 0xc7, 0xfa, 0x48, 0xb5, 0x9d,
	0xd9, 0x5b, 0xd8, 0xee, 0x63, 0x83, 0xf2, 0xa3, 0x45, 0x59, 0x46, 0xee, 0x37, 0xd7, 0x0e, 0xec,
	0x8b, 0xd4, 0x86, 0xe5, 0x88, 0x76, 0xb9, 0x8e, 0x65, 0xeb, 0x30, 0xeb, 0x68, 0x5d, 0xe2, 0x5a,
	0x3b, 0x2f, 0xd2, 0xdf, 0x0a, 0x00, 0xcf, 0xdf, 0xff, 0xbf, 0x09, 0x26, 0x54, 0x37, 0x0d, 0x66,
	0xa0, 0x73, 0xf7, 0x59, 0xf1, 0x39, 0x82, 0x47, 0x6a, 0x6b, 0x68, 0x0b, 0x16, 0x99, 0xae, 0xb6,
	0x6e, 0x31, 0x82, 0x2b, 0x20, 0x4c, 0x42, 0xff, 0x03, 0xf3, 0x26, 0x7f, 0xf2, 0x4e, 0x66, 0xb7,
	0xe2, 0x2b, 0x13, 0xc8, 0xab, 0xbf, 0xe2, 0x48, 0xd9, 0x1b, 0x81, 0x6e, 0xc1, 0x52, 0x50, 0xef,
	0xfb, 0xed, 0x89, 0x45, 0x2d, 0xa8, 0xa4, 0x6b, 0x1f, 0xc2, 0x9c, 0x33, 0x8a, 0x6d, 0x09, 0xd3,
	0xa2, 0x8a, 0xbf, 0x4f, 0x66, 0x4d, 0x8b, 0xb6, 0x35, 0x54, 0x85, 0xf9, 0xbe, 0x69, 0x50, 0xec,
	0x06, 0x98, 0xb2, 0xec, 0xbd, 0x4a, 0x9f, 0xc1, 0x66, 0xc8, 0xdd, 0xbb, 0x78, 0x74, 0xca, 0xa2,
	0x93, 0x37, 0x19, 0x22, 0x0e, 0x9b, 0x29, 0x1b, 0xef, 0x1b, 0xd8, 0xca, 0xe6, 0x45, 0x2c, 0xf4,
	0x04, 0xca, 0x9e, 0xe9, 0xbc, 0xfd, 0x57, 0x4b, 0x37, 0x86, 0x1c, 0x80, 0xa5, 0x33, 0x58, 0xf1,
	0x58, 0xca, 0x3c, 0x24, 0x4d, 0x5f, 0x99, 0xc0, 0x1a, 0xc5, 0xb0, 0x35, 0xe2, 0x16, 0x2d, 0x25,
	0x2c, 0x2a, 0xfd, 0x12, 0xae, 0x47, 0xcf, 0xa4, 0x81, 0xe8, 0xfc, 0x06, 0x41, 0x4f, 0x60, 0xde,
	0x89, 0x9e, 0xde, 0xba, 0xbf, 0x93, 0x2c, 0x4d, 0xc2, 0x1a, 0xc9, 0x1e, 0x5c, 0x52, 0xa0, 0x96,
	0x26, 0x9f, 0x58, 0xa8, 0xe1, 0x6f, 0xf3, 0x1f, 0x11, 0x50, 0x9c, 0x68, 0xfb, 0xd0, 0x6f, 0x6b,
	0xf9, 0xdb, 0xe6, 0xc0, 0x34, 0x4e, 0x99, 0x7a, 0x81, 0x32, 0xce, 0xa9, 0xd4, 0x5d, 0xdd, 0xaf,
	0xe1, 0x72, 0x04, 0xce, 0x77, 0xd9, 0x33, 0x58, 0x0d, 0x99, 0x32, 0x14, 0x54, 0xa7, 0xc4, 0x9e,
	0x15, 0x2d, 0xfc, 0x4a, 0xa4, 0xbf, 0x3b, 0x27, 0x5e, 0xc1, 0x7c, 0x88, 0x85, 0xda, 0xd1, 0x78,
	0xfd, 0x38, 0x25, 0x5e, 0x27, 0x06, 0x26, 0x63, 0x35, 0x7a, 0x0f, 0x10, 0x8f, 0x3b, 0x4e, 0xd6,
	0x67, 0x47, 0x35, 0x43, 0x1d, 0xb9, 0x8b, 0x56, 0x21, 0xae, 0x8d, 0xdb, 0x2e, 0xbd, 0xf6, 0x75,
	0x5a, 0x64, 0x77, 0xeb, 0xd5, 0x0f, 0xa2, 0x91, 0xfd, 0x96, 0x68, 0x21, 0xa2, 0x7a, 0x87, 0xa2,
	0xf9, 0x31, 0xac, 0x36, 0x34, 0xed, 0x15, 0x1d, 0x06, 0x07, 0xac, 0x7d, 0x58, 0x31, 0xd9, 0xbb,
	0x33, 0xbd, 0x31, 0x19, 0xb8, 0x1a, 0xdf, 0x88, 0x33, 0xf6, 0x47, 0xbd, 0x24, 0x03, 0x79, 0xc9,
	0x0c, 0xbd, 0x49, 0x7f, 0x2e, 0xc2, 0x52, 0xf8, 0x33, 0xea, 0xc3, 0x5a, 0xb0, 0x44, 0x3c, 0xc2,
	0x85, 0x3a, 0x06, 0x8f, 0xb3, 0x38, 0x07, 0x6b, 0xc6, 0x33, 0x81, 0xdf, 0x27, 0x08, 0xfa, 0x0f,
	0x1e, 0x5d, 0xd0, 0x8e, 0x14, 0xd7, 0xc2, 0x91, 0x12, 0x73, 0x26, 0x5a, 0x3d, 0x6f, 0xc2, 0xe2,
	0x50, 0xb5, 0x2c, 0x6c, 0x38, 0x05, 0xce, 0x2c, 0x2f, 0x70, 0xc0, 0x21, 0xf1, 0xd3, 0xb4, 0x97,
	0x63, 0xd8, 0x09, 0x6a, 0xa4, 0x0f, 0x86, 0x34, 0xe8, 0x3b, 0x96, 0x9c, 0x1c, 0xd3, 0x51, 0x47,
	0x2f, 0xd8, 0x07, 0x06, 0x67, 0x27, 0x0f, 0xb1, 0x02, 0xd3, 0x4a, 0xca, 0xd9, 0xf0, 0x32, 0x21,
	0xa8, 0x44, 0x97, 0x89, 0x58, 0xd2, 0x2a, 0x2c, 0x1f, 0x1b, 0x1e, 0x57, 0x19, 0x9f, 0x4b, 0x7f,
	0x2a, 0x02, 0x8a, 0xca, 0x6a, 0x1b, 0xa7, 0x66, 0xfe, 0x40, 0x71, 0x01, 0xd7, 0x43, 0x3d, 0x9d,
	0x20, 0x1d, 0xf3, 0x95, 0x72, 0x42, 0xc7, 0xd3, 0xd4, 0x0d, 0xe5, 0xcb, 0x0b, 0x75, 0x75, 0xfc,
	0xf4, 0xed, 0x2f, 0xda, 0x55, 0x4d, 0xf8, 0xd1, 0x69, 0x4d, 0x9e, 0x28, 0x3c, 0x37, 0x3a, 0xa9,
	0x65, 0x9e, 0xaa, 0x27, 0x47, 0x2c, 0x3d, 0x5e, 0x85, 0xb9, 0x01, 0x36, 0x34, 0x6c, 0xbb, 0x09,
	0xda, 0x7d, 0x63, 0xc6, 0x1a, 0xa9, 0x27, 0x78, 0xe4, 0xa6, 0x63, 0xe7, 0xa5, 0xd6, 0x86, 0x8d,
	0x0c, 0xf9, 0xd3, 0x6c, 0x5e, 0x8c, 0x96, 0xf1, 0x2b, 0x61, 0xfb, 0x12, 0x0b, 0x7d, 0x01, 0x57,
	0xe2, 0x4e, 0xac, 0x1b, 0xa7, 0xa6, 0x17, 0x12, 0xa4, 0xe9, 0xc6, 0x91, 0xd7, 0xb4, 0x04, 0x8d,
	0x48, 0x5f, 0xf8, 0x61, 0x87, 0x05, 0xcc, 0x7d, 0x9c, 0x7d, 0xff, 0xb0, 0x03, 0x15, 0x7e, 0x49,
	0x10, 0xae, 0xc3, 0x8b, 0xfc, 0x68, 0xb0, 0xc2, 0xe8, 0xa1, 0x32, 0xfc, 0x35, 0x5c, 0x4f, 0xe1,
	0xcb, 0x73, 0x60, 0x24, 0x9e, 0xa5, 0xb6, 0xbd, 0x9d, 0x61, 0xa1, 0xe3, 0xdc, 0xf4, 0xbb, 0x83,
	0xef, 0x66, 0x00, 0x25, 0x87, 0xa3, 0x0d, 0x28, 0x33, 0x06, 0x4e, 0xb7, 0xc9, 0x51, 0x68, 0x81,
	0x11, 0x7a, 0x17, 0x16, 0x46, 0x9f, 0x41, 0x79, 0xa4, 0x9f, 0x61, 0x25, 0xd4, 0x43, 0xbd, 0x3f,
	0x7d, 0x4a, 0xf5, 0x17, 0xfa, 0x19, 0x7f, 0x78, 0x7e, 0x49, 0x5e, 0x18, 0xb9, 0xcf, 0xa8, 0xc3,
	0x6a, 0x21, 0x6d, 0xd2, 0xa7, 0x0e, 0xb7, 0x52, 0x76, 0x82, 0x0a, 0x71, 0x3b, 0xe4, 0xa3, 0x5c,
	0x7e, 0xa0, 0xf9, 0x6f, 0xb5, 0xdf, 0x14, 0x60, 0xc1, 0x13, 0x95, 0xe3, 0x16, 0x44, 0x70, 0x70,
	0x2b, 0x45, 0xee, 0x24, 0x36, 0xa3, 0x9d, 0xf2, 0x19, 0xde, 0xaa, 0x0d, 0x37, 0xc2, 0x73, 0xb5,
	0xbd, 0x6b, 0xbf, 0x2f, 0x00, 0x04, 0x73, 0x4e, 0x4c, 0x6c, 0x03, 0xca, 0xfd, 0x89, 0x1d, 0xaa,
	0xa9, 0x97, 0xe5, 0x85, 0xfe, 0xc4, 0x76, 0x2a, 0xea, 0xa9, 0x73, 0x6c, 0xc0, 0xb2, 0x6b, 0x45,
	0x0d, 0x53, 0x55, 0x1f, 0xf1, 0x59, 0x0a, 0xd2, 0x80, 0x33, 0x81, 0x43, 0x8e, 0x91, 0x97, 0xb4,
	0xd0, 0xdb, 0x7e, 0xd9, 0xaf, 0xf8, 0xa4, 0x4f, 0x60, 0x29, 0x0c, 0xf4, 0x2e, 0x23, 0xf8, 0x8e,
	0x2f, 0x78, 0x3b, 0x7e, 0x70, 0x14, 0x29, 0x88, 0xdd, 0xe8, 0xe4, 0x14, 0xc4, 0x7b, 0xdc, 0x99,
	0x79, 0x13, 0x0a, 0x5b, 0x13, 0xaa, 0xd2, 0x50, 0xe8, 0x13, 0x9c, 0x2c, 0x1f, 0xf1, 0x43, 0xb8,
	0x10, 0x4e, 0xac, 0x40, 0x44, 0x21, 0x2c, 0xc2, 0x00, 0x24, 0x63, 0xd3, 0xd6, 0xfc, 0xbb, 0x09,
	0x92, 0xda, 0x70, 0x8c, 0x2d, 0x4e, 0x51, 0x70, 0x27, 0xb1, 0x05, 0x4b, 0x4e, 0x82, 0x67, 0x3e,
	0xef, 0x17, 0x78, 0x60, 0xbb, 0xce, 0xd6, 0xd6, 0xa4, 0x2e, 0xac, 0x25, 0xe4, 0x11, 0x0b, 0xfd,
	0xaf, 0xdb, 0xa9, 0xe4, 0xae, 0x51, 0xc8, 0x77, 0x89, 0xc2, 0xf3, 0x19, 0xe3, 0xb0, 0xfb, 0x8f,
	0x82, 0xd3, 0xeb, 0x4a, 0x5e, 0x99, 0xa1, 0x5d, 0xb8, 0xd7, 0x6a, 0xbc, 0x6c, 0x2a, 0xc7, 0xdd,
	0xa6, 0xac, 0xc8, 0x8d, 0x5e, 0x53, 0xe9, 0xbe, 0x3a, 0x96, 0x0f, 0x9a, 0x4a, 0xef, 0xab, 0x4e,
	0x53, 0x39, 0x3e, 0xea, 0x76, 0x9a, 0x07, 0xed, 0x67, 0xed, 0xe6, 0x61, 0xe5, 0x12, 0xba, 0x07,
	0x52, 0x06, 0xf6, 0xe0, 0x79, 0xe3, 0xe8, 0xa8, 0xf9, 0xa2, 0x52, 0x98, 0x82, 0x6b, 0xbf, 0x64,
	0xd0, 0x5e, 0xa5, 0x88, 0xf6, 0xe0, 0xdd, 0x0c, 0x5c, 0xa3, 0xdb, 0x6d, 0x77, 0x7b, 0x8d, 0xa3,
	0x9e, 0xd2, 0x39, 0xee, 0x3e, 0xaf, 0x94, 0xa6, 0xc0, 0x5d, 0xf1, 0xca, 0x8b, 0x66, 0xaf, 0xd7,
	0x94, 0x2b, 0x33, 0xbb, 0x3f, 0x14, 0x82, 0x88, 0x13, 0xdc, 0xd3, 0xa1, 0xbb, 0x70, 0x2b, 0xce,
	0xa5, 0xd7, 0xe8, 0x1d, 0x77, 0x63, 0xba, 0x4a, 0xf0, 0x8e, 0x18, 0x76, 0xf4, 0xaa, 0xc7, 0xdf,
	0x2b, 0x05, 0xb4, 0x09, 0x1b, 0x62, 0x0c, 0x7b, 0x3e, 0xac, 0x14, 0xd1, 0x2d, 0xb8, 0x29, 0x06,
	0x34, 0xff, 0xbf, 0xd3, 0x96, 0x9b, 0x87, 0x95, 0xd2, 0xee, 0xef, 0x62, 0x4b, 0x13, 0xb4, 0xd3,
	0xd1, 0x7d, 0xd8, 0x8e, 0x8d, 0x6e, 0x1c, 0x1e, 0x2a, 0xbd, 0x46, 0x4b, 0xb4, 0x36, 0xdb, 0x70,
	0x3b, 0x0b, 0x1c, 0x2c, 0xce, 0x0e, 0xdc, 0xc9, 0x02, 0xf2, 0x6f, 0x9d, 0xc6, 0x8b, 0x4a, 0xf1,
	0xd1, 0x77, 0x1b, 0xb0, 0x14, 0x9e, 0x1a, 0x3a, 0x83, 0xf5, 0x86, 0x36, 0xd6, 0x8d, 0x63, 0x8b,
	0x60, 0x9b, 0xfa, 0x99, 0x0c, 0x6d, 0x27, 0xbb, 0xf8, 0x49, 0x94, 0x8c, 0xcf, 0x6b, 0x3b, 0xf9,
	0x80, 0xc4, 0x92, 0x2e, 0x21, 0x0c, 0x88, 0x7f, 0x6d, 0xe1, 0xe0, 0x13, 0x41, 0x77, 0x85, 0x1c,
	0x22, 0x18, 0x26, 0xe8, 0x5e, 0x1e, 0x18, 0x17, 0x73, 0x02, 0x97, 0xf9, 0xb7, 0x43, 0x3c, 0x0a,
	0x14, 0xba, 0x23, 0x1c, 0x1e, 0x86, 0x30, 0x21, 0x77, 0x73, 0xa0, 0xb8, 0x8c, 0x73, 0xb8, 0xca,
	0x3f, 0x75, 0xd5, 0xd7, 0x4e, 0xbe, 0x65, 0x65, 0x09, 0x4b, 0xba, 0xe8, 0x5d, 0x21, 0x8b, 0x04,
	0x8e, 0x49, 0xdb, 0xcd, 0x0b, 0x8d, 0x5b, 0xcf, 0xff, 0x9c, 0x61, 0xbd, 0x00, 0x93, 0x69, 0xbd,
	0x30, 0x8c, 0x8b, 0xf9, 0x55, 0x01, 0x36, 0xf8, 0xc7, 0x7d, 0xdd, 0xd0, 0x22, 0x87, 0x0a, 0x0e,
	0x43, 0x75, 0x21, 0x27, 0x31, 0x98, 0x49, 0x7e, 0xf0, 0x56, 0xf8, 0xe8, 0x14, 0xe2, 0x87, 0x2d,
	0x57, 0xe7, 0x7a, 0x9a, 0x32, 0x02, 0x70, 0xfa, 0x14, 0xd2, 0xf0, 0x7c, 0x0a, 0xbf, 0x80, 0x9a,
	0xb7, 0xf4, 0x02, 0x1b, 0xec, 0xa5, 0xb9, 0x89, 0xd8, 0x04, 0xf5, 0xb7, 0x81, 0x73, 0xf1, 0xdf,
	0x42, 0xb5, 0xa1, 0x69, 0xdd, 0xa1, 0xf9, 0x2d, 0x0f, 0x20, 0x86, 0xd6, 0x99, 0xb0, 0x92, 0x8b,
	0x1d, 0x36, 0xd1, 0x7d, 0xc1, 0x05, 0x9b, 0x10, 0xc9, 0x44, 0xbf, 0x97, 0x1f, 0xec, 0x9b, 0xfe,
	0xd8, 0xd2, 0x58, 0x6c, 0x15, 0x0a, 0x4f, 0xa8, 0x92, 0x01, 0x16, 0x9a, 0x3e, 0x13, 0xef, 0xe9,
	0xde, 0xc2, 0x34, 0xa7, 0xee, 0x69, 0x48, 0xa1, 0xee, 0xe9, 0x60, 0x2e, 0x78, 0x08, 0x6b, 0x82,
	0xff, 0x8d, 0xd0, 0xbd, 0x8c, 0x96, 0x6d, 0xe8, 0xf7, 0xa5, 0xda, 0x76, 0x2e, 0x5c, 0x8a, 0x24,
	0x1e, 0x3a, 0xa6, 0x49, 0xf2, 0xe2, 0xc6, 0x76, 0x2e, 0x9c, 0x17, 0x34, 0x92, 0xff, 0x3b, 0x24,
	0x83, 0x86, 0xf0, 0xa7, 0x90, 0x64, 0xd0, 0x10, 0xff, 0x3a, 0x21, 0x5d, 0x42, 0xbf, 0x8e, 0xb6,
	0xc1, 0x13, 0xed, 0x38, 0xf4, 0x20, 0x63, 0xca, 0xa2, 0x46, 0x60, 0xed, 0xe1, 0xdb, 0x0d, 0xf0,
	0x82, 0xb2, 0xb8, 0x91, 0x95, 0x0c, 0xca, 0xa9, 0x0d, 0xb7, 0x64, 0x50, 0x4e, 0xef, 0x8d, 0x49,
	0x97, 0x58, 0xfe, 0x14, 0xfd, 0x6e, 0x80, 0xb6, 0xf3, 0xfd, 0x1d, 0x21, 0xc8, 0x9f, 0x69, 0xbf,
	0x51, 0x48, 0x97, 0x90, 0x01, 0x57, 0x84, 0x07, 0x3d, 0xb4, 0x93, 0x61, 0xac, 0xc8, 0x39, 0xb3,
	0xf6, 0x6e, 0x4e, 0x24, 0x97, 0xf7, 0x0d, 0xac, 0xc6, 0x0a, 0x57, 0x94, 0x38, 0x3f, 0x26, 0x2b,
	0xe9, 0xda, 0xed, 0xa9, 0x18, 0x8f, 0x7b, 0xec, 0xca, 0x39, 0xc9, 0x3d, 0xf9, 0x63, 0x40, 0xed,
	0xf6, 0x54, 0x8c, 0xb7, 0x30, 0xa2, 0x5b, 0xed, 0xe4, 0xc2, 0xa4, 0xfc, 0x80, 0x90, 0x5c, 0x98,
	0xb4, 0x4b, 0x72, 0xc7, 0xf1, 0xc4, 0x97, 0xae, 0x49, 0xc7, 0x4b, 0xbd, 0x59, 0x4e, 0x3a, 0x5e,
	0xc6, 0x3d, 0x6e, 0x20, 0xb2, 0x95, 0x53, 0x64, 0x2b, 0xbf, 0xc8, 0x56, 0x8a, 0x48, 0x1a, 0xf9,
	0x3b, 0x31, 0x7c, 0x0b, 0x89, 0x76, 0x33, 0xdc, 0x2a, 0x76, 0x33, 0x5a, 0xbb, 0x9f, 0x1b, 0xcb,
	0xa5, 0x2a, 0x50, 0x89, 0xdf, 0x1d, 0xa2, 0x84, 0x0f, 0x08, 0xae, 0x21, 0x6b, 0x77, 0xa6, 0x83,
	0x42, 0xf9, 0x46, 0xd8, 0xc7, 0x46, 0xf7, 0xf3, 0x5f, 0xd7, 0x89, 0xf3, 0x4d, 0x6a, 0x7b, 0xdc,
	0x8f, 0x1d, 0x89, 0x6e, 0x32, 0xda, 0xce, 0xd7, 0x73, 0x16, 0xc7, 0x0e, 0x61, 0x73, 0x5a, 0xba,
	0x84, 0xba, 0xb0, 0x14, 0x6e, 0x2d, 0xa2, 0x4d, 0xc1, 0x36, 0x0a, 0xf7, 0x87, 0x6b, 0x5b, 0xd9,
	0x00, 0xce, 0xf4, 0x25, 0x40, 0xd0, 0x3b, 0x43, 0x89, 0x36, 0x7c, 0xa4, 0x6f, 0x59, 0x7b, 0x27,
	0xeb, 0xb3, 0x57, 0x7c, 0xdc, 0x4a, 0xcb, 0xd1, 0x5f, 0xea, 0x74, 0x78, 0xa0, 0xf6, 0x87, 0xf8,
	0x3f, 0x5b, 0x03, 0x9c, 0xf3, 0x3f, 0x84, 0x05, 0xfd, 0x04, 0x24, 0x8a, 0x9c, 0xe2, 0x36, 0x45,
	0x6d, 0x37, 0x2f, 0x94, 0x89, 0xdc, 0x7f, 0xf4, 0xb3, 0x87, 0x03, 0x73, 0xa4, 0x1a, 0x83, 0xfa,
	0xfb, 0x8f, 0x28, 0xad, 0xf7, 0xcd, 0xf1, 0x03, 0xfe, 0xa7, 0x74, 0xdf, 0x1c, 0x3d, 0x20, 0xd8,
	0x7e, 0xad, 0xf7, 0x31, 0x89, 0xfd, 0x5f, 0x7d, 0x32, 0xc7, 0x11, 0x8f, 0xff, 0x1d, 0x00, 0x00,
	0xff, 0xff, 0xef, 0xbc, 0x03, 0xd9, 0xcb, 0x2d, 0x00, 0x00,
}
