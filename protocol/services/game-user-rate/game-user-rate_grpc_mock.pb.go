// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/game-user-rate/game-user-rate.proto

package game_user_rate

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockGameUserRateClient is a mock of GameUserRateClient interface.
type MockGameUserRateClient struct {
	ctrl     *gomock.Controller
	recorder *MockGameUserRateClientMockRecorder
}

// MockGameUserRateClientMockRecorder is the mock recorder for MockGameUserRateClient.
type MockGameUserRateClientMockRecorder struct {
	mock *MockGameUserRateClient
}

// NewMockGameUserRateClient creates a new mock instance.
func NewMockGameUserRateClient(ctrl *gomock.Controller) *MockGameUserRateClient {
	mock := &MockGameUserRateClient{ctrl: ctrl}
	mock.recorder = &MockGameUserRateClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGameUserRateClient) EXPECT() *MockGameUserRateClientMockRecorder {
	return m.recorder
}

// AddGameUserRate mocks base method.
func (m *MockGameUserRateClient) AddGameUserRate(ctx context.Context, in *AddGameUserRateReq, opts ...grpc.CallOption) (*AddGameUserRateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddGameUserRate", varargs...)
	ret0, _ := ret[0].(*AddGameUserRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddGameUserRate indicates an expected call of AddGameUserRate.
func (mr *MockGameUserRateClientMockRecorder) AddGameUserRate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddGameUserRate", reflect.TypeOf((*MockGameUserRateClient)(nil).AddGameUserRate), varargs...)
}

// AddOtherRate mocks base method.
func (m *MockGameUserRateClient) AddOtherRate(ctx context.Context, in *AddOtherRateReq, opts ...grpc.CallOption) (*AddOtherRateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddOtherRate", varargs...)
	ret0, _ := ret[0].(*AddOtherRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddOtherRate indicates an expected call of AddOtherRate.
func (mr *MockGameUserRateClientMockRecorder) AddOtherRate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddOtherRate", reflect.TypeOf((*MockGameUserRateClient)(nil).AddOtherRate), varargs...)
}

// AddShowRateAndPunishConf mocks base method.
func (m *MockGameUserRateClient) AddShowRateAndPunishConf(ctx context.Context, in *AddShowRateAndPunishConfReq, opts ...grpc.CallOption) (*AddShowRateAndPunishConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddShowRateAndPunishConf", varargs...)
	ret0, _ := ret[0].(*AddShowRateAndPunishConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddShowRateAndPunishConf indicates an expected call of AddShowRateAndPunishConf.
func (mr *MockGameUserRateClientMockRecorder) AddShowRateAndPunishConf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddShowRateAndPunishConf", reflect.TypeOf((*MockGameUserRateClient)(nil).AddShowRateAndPunishConf), varargs...)
}

// AdminBindGameDimensionLabel mocks base method.
func (m *MockGameUserRateClient) AdminBindGameDimensionLabel(ctx context.Context, in *AdminBindGameDimensionLabelReq, opts ...grpc.CallOption) (*AdminBindGameDimensionLabelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AdminBindGameDimensionLabel", varargs...)
	ret0, _ := ret[0].(*AdminBindGameDimensionLabelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminBindGameDimensionLabel indicates an expected call of AdminBindGameDimensionLabel.
func (mr *MockGameUserRateClientMockRecorder) AdminBindGameDimensionLabel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminBindGameDimensionLabel", reflect.TypeOf((*MockGameUserRateClient)(nil).AdminBindGameDimensionLabel), varargs...)
}

// AdminDelDimension mocks base method.
func (m *MockGameUserRateClient) AdminDelDimension(ctx context.Context, in *AdminDelDimensionReq, opts ...grpc.CallOption) (*AdminDelDimensionResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AdminDelDimension", varargs...)
	ret0, _ := ret[0].(*AdminDelDimensionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminDelDimension indicates an expected call of AdminDelDimension.
func (mr *MockGameUserRateClientMockRecorder) AdminDelDimension(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminDelDimension", reflect.TypeOf((*MockGameUserRateClient)(nil).AdminDelDimension), varargs...)
}

// AdminDelGameDimensionLabel mocks base method.
func (m *MockGameUserRateClient) AdminDelGameDimensionLabel(ctx context.Context, in *AdminDelGameDimensionLabelReq, opts ...grpc.CallOption) (*AdminDelGameDimensionLabelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AdminDelGameDimensionLabel", varargs...)
	ret0, _ := ret[0].(*AdminDelGameDimensionLabelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminDelGameDimensionLabel indicates an expected call of AdminDelGameDimensionLabel.
func (mr *MockGameUserRateClientMockRecorder) AdminDelGameDimensionLabel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminDelGameDimensionLabel", reflect.TypeOf((*MockGameUserRateClient)(nil).AdminDelGameDimensionLabel), varargs...)
}

// AdminGetDimensions mocks base method.
func (m *MockGameUserRateClient) AdminGetDimensions(ctx context.Context, in *AdminGetDimensionsReq, opts ...grpc.CallOption) (*AdminGetDimensionsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AdminGetDimensions", varargs...)
	ret0, _ := ret[0].(*AdminGetDimensionsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminGetDimensions indicates an expected call of AdminGetDimensions.
func (mr *MockGameUserRateClientMockRecorder) AdminGetDimensions(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminGetDimensions", reflect.TypeOf((*MockGameUserRateClient)(nil).AdminGetDimensions), varargs...)
}

// AdminGetGameDimensionLabels mocks base method.
func (m *MockGameUserRateClient) AdminGetGameDimensionLabels(ctx context.Context, in *AdminGetGameDimensionLabelsReq, opts ...grpc.CallOption) (*AdminGetGameDimensionLabelsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AdminGetGameDimensionLabels", varargs...)
	ret0, _ := ret[0].(*AdminGetGameDimensionLabelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminGetGameDimensionLabels indicates an expected call of AdminGetGameDimensionLabels.
func (mr *MockGameUserRateClientMockRecorder) AdminGetGameDimensionLabels(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminGetGameDimensionLabels", reflect.TypeOf((*MockGameUserRateClient)(nil).AdminGetGameDimensionLabels), varargs...)
}

// AdminGetRateLabels mocks base method.
func (m *MockGameUserRateClient) AdminGetRateLabels(ctx context.Context, in *AdminGetRateLabelsReq, opts ...grpc.CallOption) (*AdminGetRateLabelsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AdminGetRateLabels", varargs...)
	ret0, _ := ret[0].(*AdminGetRateLabelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminGetRateLabels indicates an expected call of AdminGetRateLabels.
func (mr *MockGameUserRateClientMockRecorder) AdminGetRateLabels(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminGetRateLabels", reflect.TypeOf((*MockGameUserRateClient)(nil).AdminGetRateLabels), varargs...)
}

// AdminSaveRateLabelList mocks base method.
func (m *MockGameUserRateClient) AdminSaveRateLabelList(ctx context.Context, in *AdminSaveRateLabelListReq, opts ...grpc.CallOption) (*AdminSaveRateLabelListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AdminSaveRateLabelList", varargs...)
	ret0, _ := ret[0].(*AdminSaveRateLabelListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminSaveRateLabelList indicates an expected call of AdminSaveRateLabelList.
func (mr *MockGameUserRateClientMockRecorder) AdminSaveRateLabelList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminSaveRateLabelList", reflect.TypeOf((*MockGameUserRateClient)(nil).AdminSaveRateLabelList), varargs...)
}

// AdminUpsertDimension mocks base method.
func (m *MockGameUserRateClient) AdminUpsertDimension(ctx context.Context, in *AdminUpsertDimensionReq, opts ...grpc.CallOption) (*AdminUpsertDimensionResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AdminUpsertDimension", varargs...)
	ret0, _ := ret[0].(*AdminUpsertDimensionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminUpsertDimension indicates an expected call of AdminUpsertDimension.
func (mr *MockGameUserRateClientMockRecorder) AdminUpsertDimension(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminUpsertDimension", reflect.TypeOf((*MockGameUserRateClient)(nil).AdminUpsertDimension), varargs...)
}

// BatchGetFirstChatToken mocks base method.
func (m *MockGameUserRateClient) BatchGetFirstChatToken(ctx context.Context, in *BatchGetFirstChatTokenReq, opts ...grpc.CallOption) (*BatchGetFirstChatTokenResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetFirstChatToken", varargs...)
	ret0, _ := ret[0].(*BatchGetFirstChatTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetFirstChatToken indicates an expected call of BatchGetFirstChatToken.
func (mr *MockGameUserRateClientMockRecorder) BatchGetFirstChatToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetFirstChatToken", reflect.TypeOf((*MockGameUserRateClient)(nil).BatchGetFirstChatToken), varargs...)
}

// BatchSetFirstChatToken mocks base method.
func (m *MockGameUserRateClient) BatchSetFirstChatToken(ctx context.Context, in *BatchSetFirstChatTokenReq, opts ...grpc.CallOption) (*BatchSetFirstChatTokenResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchSetFirstChatToken", varargs...)
	ret0, _ := ret[0].(*BatchSetFirstChatTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchSetFirstChatToken indicates an expected call of BatchSetFirstChatToken.
func (mr *MockGameUserRateClientMockRecorder) BatchSetFirstChatToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetFirstChatToken", reflect.TypeOf((*MockGameUserRateClient)(nil).BatchSetFirstChatToken), varargs...)
}

// CheckAddGameUserRate mocks base method.
func (m *MockGameUserRateClient) CheckAddGameUserRate(ctx context.Context, in *CheckAddGameUserRateReq, opts ...grpc.CallOption) (*CheckAddGameUserRateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckAddGameUserRate", varargs...)
	ret0, _ := ret[0].(*CheckAddGameUserRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAddGameUserRate indicates an expected call of CheckAddGameUserRate.
func (mr *MockGameUserRateClientMockRecorder) CheckAddGameUserRate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAddGameUserRate", reflect.TypeOf((*MockGameUserRateClient)(nil).CheckAddGameUserRate), varargs...)
}

// GetGameDimensionConf mocks base method.
func (m *MockGameUserRateClient) GetGameDimensionConf(ctx context.Context, in *GetGameDimensionConfReq, opts ...grpc.CallOption) (*GetGameDimensionConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameDimensionConf", varargs...)
	ret0, _ := ret[0].(*GetGameDimensionConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameDimensionConf indicates an expected call of GetGameDimensionConf.
func (mr *MockGameUserRateClientMockRecorder) GetGameDimensionConf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameDimensionConf", reflect.TypeOf((*MockGameUserRateClient)(nil).GetGameDimensionConf), varargs...)
}

// GetGameUserBeRateList mocks base method.
func (m *MockGameUserRateClient) GetGameUserBeRateList(ctx context.Context, in *GetGameUserBeRateListReq, opts ...grpc.CallOption) (*GetGameUserBeRateListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameUserBeRateList", varargs...)
	ret0, _ := ret[0].(*GetGameUserBeRateListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserBeRateList indicates an expected call of GetGameUserBeRateList.
func (mr *MockGameUserRateClientMockRecorder) GetGameUserBeRateList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserBeRateList", reflect.TypeOf((*MockGameUserRateClient)(nil).GetGameUserBeRateList), varargs...)
}

// GetGameUserNotRateCount mocks base method.
func (m *MockGameUserRateClient) GetGameUserNotRateCount(ctx context.Context, in *GetGameUserNotRateCountReq, opts ...grpc.CallOption) (*GetGameUserNotRateCountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameUserNotRateCount", varargs...)
	ret0, _ := ret[0].(*GetGameUserNotRateCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserNotRateCount indicates an expected call of GetGameUserNotRateCount.
func (mr *MockGameUserRateClientMockRecorder) GetGameUserNotRateCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserNotRateCount", reflect.TypeOf((*MockGameUserRateClient)(nil).GetGameUserNotRateCount), varargs...)
}

// GetGameUserPersonalImage mocks base method.
func (m *MockGameUserRateClient) GetGameUserPersonalImage(ctx context.Context, in *GetGameUserPersonalImageReq, opts ...grpc.CallOption) (*GetGameUserPersonalImageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameUserPersonalImage", varargs...)
	ret0, _ := ret[0].(*GetGameUserPersonalImageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserPersonalImage indicates an expected call of GetGameUserPersonalImage.
func (mr *MockGameUserRateClientMockRecorder) GetGameUserPersonalImage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserPersonalImage", reflect.TypeOf((*MockGameUserRateClient)(nil).GetGameUserPersonalImage), varargs...)
}

// GetGameUserRateById mocks base method.
func (m *MockGameUserRateClient) GetGameUserRateById(ctx context.Context, in *GetGameUserRateByIdReq, opts ...grpc.CallOption) (*GetGameUserRateByIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameUserRateById", varargs...)
	ret0, _ := ret[0].(*GetGameUserRateByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserRateById indicates an expected call of GetGameUserRateById.
func (mr *MockGameUserRateClientMockRecorder) GetGameUserRateById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserRateById", reflect.TypeOf((*MockGameUserRateClient)(nil).GetGameUserRateById), varargs...)
}

// GetGameUserRateList mocks base method.
func (m *MockGameUserRateClient) GetGameUserRateList(ctx context.Context, in *GetGameUserRateListReq, opts ...grpc.CallOption) (*GetGameUserRateListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameUserRateList", varargs...)
	ret0, _ := ret[0].(*GetGameUserRateListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserRateList indicates an expected call of GetGameUserRateList.
func (mr *MockGameUserRateClientMockRecorder) GetGameUserRateList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserRateList", reflect.TypeOf((*MockGameUserRateClient)(nil).GetGameUserRateList), varargs...)
}

// GetGameUserSelfRateQuestions mocks base method.
func (m *MockGameUserRateClient) GetGameUserSelfRateQuestions(ctx context.Context, in *GetGameUserSelfRateQuestionsReq, opts ...grpc.CallOption) (*GetGameUserSelfRateQuestionsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameUserSelfRateQuestions", varargs...)
	ret0, _ := ret[0].(*GetGameUserSelfRateQuestionsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserSelfRateQuestions indicates an expected call of GetGameUserSelfRateQuestions.
func (mr *MockGameUserRateClientMockRecorder) GetGameUserSelfRateQuestions(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserSelfRateQuestions", reflect.TypeOf((*MockGameUserRateClient)(nil).GetGameUserSelfRateQuestions), varargs...)
}

// GetShowRateAndPunishConf mocks base method.
func (m *MockGameUserRateClient) GetShowRateAndPunishConf(ctx context.Context, in *GetShowRateAndPunishConfReq, opts ...grpc.CallOption) (*GetShowRateAndPunishConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetShowRateAndPunishConf", varargs...)
	ret0, _ := ret[0].(*GetShowRateAndPunishConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowRateAndPunishConf indicates an expected call of GetShowRateAndPunishConf.
func (mr *MockGameUserRateClientMockRecorder) GetShowRateAndPunishConf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowRateAndPunishConf", reflect.TypeOf((*MockGameUserRateClient)(nil).GetShowRateAndPunishConf), varargs...)
}

// GetShowRateAndPunishConfWithCache mocks base method.
func (m *MockGameUserRateClient) GetShowRateAndPunishConfWithCache(ctx context.Context, in *GetShowRateAndPunishConfReq, opts ...grpc.CallOption) (*GetShowRateAndPunishConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetShowRateAndPunishConfWithCache", varargs...)
	ret0, _ := ret[0].(*GetShowRateAndPunishConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowRateAndPunishConfWithCache indicates an expected call of GetShowRateAndPunishConfWithCache.
func (mr *MockGameUserRateClientMockRecorder) GetShowRateAndPunishConfWithCache(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowRateAndPunishConfWithCache", reflect.TypeOf((*MockGameUserRateClient)(nil).GetShowRateAndPunishConfWithCache), varargs...)
}

// GetTabByDimensionIds mocks base method.
func (m *MockGameUserRateClient) GetTabByDimensionIds(ctx context.Context, in *GetTabByDimensionIdsReq, opts ...grpc.CallOption) (*GetTabByDimensionIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTabByDimensionIds", varargs...)
	ret0, _ := ret[0].(*GetTabByDimensionIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabByDimensionIds indicates an expected call of GetTabByDimensionIds.
func (mr *MockGameUserRateClientMockRecorder) GetTabByDimensionIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabByDimensionIds", reflect.TypeOf((*MockGameUserRateClient)(nil).GetTabByDimensionIds), varargs...)
}

// GetUserReputationScore mocks base method.
func (m *MockGameUserRateClient) GetUserReputationScore(ctx context.Context, in *GetUserReputationScoreReq, opts ...grpc.CallOption) (*GetUserReputationScoreResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserReputationScore", varargs...)
	ret0, _ := ret[0].(*GetUserReputationScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserReputationScore indicates an expected call of GetUserReputationScore.
func (mr *MockGameUserRateClientMockRecorder) GetUserReputationScore(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserReputationScore", reflect.TypeOf((*MockGameUserRateClient)(nil).GetUserReputationScore), varargs...)
}

// IsNeedPushAssist mocks base method.
func (m *MockGameUserRateClient) IsNeedPushAssist(ctx context.Context, in *IsNeedPushAssistReq, opts ...grpc.CallOption) (*IsNeedPushAssistResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsNeedPushAssist", varargs...)
	ret0, _ := ret[0].(*IsNeedPushAssistResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsNeedPushAssist indicates an expected call of IsNeedPushAssist.
func (mr *MockGameUserRateClientMockRecorder) IsNeedPushAssist(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsNeedPushAssist", reflect.TypeOf((*MockGameUserRateClient)(nil).IsNeedPushAssist), varargs...)
}

// ReorderRateTags mocks base method.
func (m *MockGameUserRateClient) ReorderRateTags(ctx context.Context, in *ReorderRateTagsReq, opts ...grpc.CallOption) (*ReorderRateTagsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReorderRateTags", varargs...)
	ret0, _ := ret[0].(*ReorderRateTagsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReorderRateTags indicates an expected call of ReorderRateTags.
func (mr *MockGameUserRateClientMockRecorder) ReorderRateTags(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReorderRateTags", reflect.TypeOf((*MockGameUserRateClient)(nil).ReorderRateTags), varargs...)
}

// SubmitGameUserRate mocks base method.
func (m *MockGameUserRateClient) SubmitGameUserRate(ctx context.Context, in *SubmitGameUserRateReq, opts ...grpc.CallOption) (*SubmitGameUserRateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitGameUserRate", varargs...)
	ret0, _ := ret[0].(*SubmitGameUserRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitGameUserRate indicates an expected call of SubmitGameUserRate.
func (mr *MockGameUserRateClientMockRecorder) SubmitGameUserRate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitGameUserRate", reflect.TypeOf((*MockGameUserRateClient)(nil).SubmitGameUserRate), varargs...)
}

// SubmitGameUserSelfRate mocks base method.
func (m *MockGameUserRateClient) SubmitGameUserSelfRate(ctx context.Context, in *SubmitGameUserSelfRateReq, opts ...grpc.CallOption) (*SubmitGameUserSelfRateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitGameUserSelfRate", varargs...)
	ret0, _ := ret[0].(*SubmitGameUserSelfRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitGameUserSelfRate indicates an expected call of SubmitGameUserSelfRate.
func (mr *MockGameUserRateClientMockRecorder) SubmitGameUserSelfRate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitGameUserSelfRate", reflect.TypeOf((*MockGameUserRateClient)(nil).SubmitGameUserSelfRate), varargs...)
}

// UnionScore mocks base method.
func (m *MockGameUserRateClient) UnionScore(ctx context.Context, in *UnionScoreReq, opts ...grpc.CallOption) (*UnionScoreResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnionScore", varargs...)
	ret0, _ := ret[0].(*UnionScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnionScore indicates an expected call of UnionScore.
func (mr *MockGameUserRateClientMockRecorder) UnionScore(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnionScore", reflect.TypeOf((*MockGameUserRateClient)(nil).UnionScore), varargs...)
}

// UpdateShowRateAndPunishConf mocks base method.
func (m *MockGameUserRateClient) UpdateShowRateAndPunishConf(ctx context.Context, in *UpdateShowRateAndPunishConfReq, opts ...grpc.CallOption) (*UpdateShowRateAndPunishConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateShowRateAndPunishConf", varargs...)
	ret0, _ := ret[0].(*UpdateShowRateAndPunishConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateShowRateAndPunishConf indicates an expected call of UpdateShowRateAndPunishConf.
func (mr *MockGameUserRateClientMockRecorder) UpdateShowRateAndPunishConf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateShowRateAndPunishConf", reflect.TypeOf((*MockGameUserRateClient)(nil).UpdateShowRateAndPunishConf), varargs...)
}

// MockGameUserRateServer is a mock of GameUserRateServer interface.
type MockGameUserRateServer struct {
	ctrl     *gomock.Controller
	recorder *MockGameUserRateServerMockRecorder
}

// MockGameUserRateServerMockRecorder is the mock recorder for MockGameUserRateServer.
type MockGameUserRateServerMockRecorder struct {
	mock *MockGameUserRateServer
}

// NewMockGameUserRateServer creates a new mock instance.
func NewMockGameUserRateServer(ctrl *gomock.Controller) *MockGameUserRateServer {
	mock := &MockGameUserRateServer{ctrl: ctrl}
	mock.recorder = &MockGameUserRateServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGameUserRateServer) EXPECT() *MockGameUserRateServerMockRecorder {
	return m.recorder
}

// AddGameUserRate mocks base method.
func (m *MockGameUserRateServer) AddGameUserRate(ctx context.Context, in *AddGameUserRateReq) (*AddGameUserRateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddGameUserRate", ctx, in)
	ret0, _ := ret[0].(*AddGameUserRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddGameUserRate indicates an expected call of AddGameUserRate.
func (mr *MockGameUserRateServerMockRecorder) AddGameUserRate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddGameUserRate", reflect.TypeOf((*MockGameUserRateServer)(nil).AddGameUserRate), ctx, in)
}

// AddOtherRate mocks base method.
func (m *MockGameUserRateServer) AddOtherRate(ctx context.Context, in *AddOtherRateReq) (*AddOtherRateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddOtherRate", ctx, in)
	ret0, _ := ret[0].(*AddOtherRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddOtherRate indicates an expected call of AddOtherRate.
func (mr *MockGameUserRateServerMockRecorder) AddOtherRate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddOtherRate", reflect.TypeOf((*MockGameUserRateServer)(nil).AddOtherRate), ctx, in)
}

// AddShowRateAndPunishConf mocks base method.
func (m *MockGameUserRateServer) AddShowRateAndPunishConf(ctx context.Context, in *AddShowRateAndPunishConfReq) (*AddShowRateAndPunishConfResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddShowRateAndPunishConf", ctx, in)
	ret0, _ := ret[0].(*AddShowRateAndPunishConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddShowRateAndPunishConf indicates an expected call of AddShowRateAndPunishConf.
func (mr *MockGameUserRateServerMockRecorder) AddShowRateAndPunishConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddShowRateAndPunishConf", reflect.TypeOf((*MockGameUserRateServer)(nil).AddShowRateAndPunishConf), ctx, in)
}

// AdminBindGameDimensionLabel mocks base method.
func (m *MockGameUserRateServer) AdminBindGameDimensionLabel(ctx context.Context, in *AdminBindGameDimensionLabelReq) (*AdminBindGameDimensionLabelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AdminBindGameDimensionLabel", ctx, in)
	ret0, _ := ret[0].(*AdminBindGameDimensionLabelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminBindGameDimensionLabel indicates an expected call of AdminBindGameDimensionLabel.
func (mr *MockGameUserRateServerMockRecorder) AdminBindGameDimensionLabel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminBindGameDimensionLabel", reflect.TypeOf((*MockGameUserRateServer)(nil).AdminBindGameDimensionLabel), ctx, in)
}

// AdminDelDimension mocks base method.
func (m *MockGameUserRateServer) AdminDelDimension(ctx context.Context, in *AdminDelDimensionReq) (*AdminDelDimensionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AdminDelDimension", ctx, in)
	ret0, _ := ret[0].(*AdminDelDimensionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminDelDimension indicates an expected call of AdminDelDimension.
func (mr *MockGameUserRateServerMockRecorder) AdminDelDimension(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminDelDimension", reflect.TypeOf((*MockGameUserRateServer)(nil).AdminDelDimension), ctx, in)
}

// AdminDelGameDimensionLabel mocks base method.
func (m *MockGameUserRateServer) AdminDelGameDimensionLabel(ctx context.Context, in *AdminDelGameDimensionLabelReq) (*AdminDelGameDimensionLabelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AdminDelGameDimensionLabel", ctx, in)
	ret0, _ := ret[0].(*AdminDelGameDimensionLabelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminDelGameDimensionLabel indicates an expected call of AdminDelGameDimensionLabel.
func (mr *MockGameUserRateServerMockRecorder) AdminDelGameDimensionLabel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminDelGameDimensionLabel", reflect.TypeOf((*MockGameUserRateServer)(nil).AdminDelGameDimensionLabel), ctx, in)
}

// AdminGetDimensions mocks base method.
func (m *MockGameUserRateServer) AdminGetDimensions(ctx context.Context, in *AdminGetDimensionsReq) (*AdminGetDimensionsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AdminGetDimensions", ctx, in)
	ret0, _ := ret[0].(*AdminGetDimensionsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminGetDimensions indicates an expected call of AdminGetDimensions.
func (mr *MockGameUserRateServerMockRecorder) AdminGetDimensions(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminGetDimensions", reflect.TypeOf((*MockGameUserRateServer)(nil).AdminGetDimensions), ctx, in)
}

// AdminGetGameDimensionLabels mocks base method.
func (m *MockGameUserRateServer) AdminGetGameDimensionLabels(ctx context.Context, in *AdminGetGameDimensionLabelsReq) (*AdminGetGameDimensionLabelsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AdminGetGameDimensionLabels", ctx, in)
	ret0, _ := ret[0].(*AdminGetGameDimensionLabelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminGetGameDimensionLabels indicates an expected call of AdminGetGameDimensionLabels.
func (mr *MockGameUserRateServerMockRecorder) AdminGetGameDimensionLabels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminGetGameDimensionLabels", reflect.TypeOf((*MockGameUserRateServer)(nil).AdminGetGameDimensionLabels), ctx, in)
}

// AdminGetRateLabels mocks base method.
func (m *MockGameUserRateServer) AdminGetRateLabels(ctx context.Context, in *AdminGetRateLabelsReq) (*AdminGetRateLabelsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AdminGetRateLabels", ctx, in)
	ret0, _ := ret[0].(*AdminGetRateLabelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminGetRateLabels indicates an expected call of AdminGetRateLabels.
func (mr *MockGameUserRateServerMockRecorder) AdminGetRateLabels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminGetRateLabels", reflect.TypeOf((*MockGameUserRateServer)(nil).AdminGetRateLabels), ctx, in)
}

// AdminSaveRateLabelList mocks base method.
func (m *MockGameUserRateServer) AdminSaveRateLabelList(ctx context.Context, in *AdminSaveRateLabelListReq) (*AdminSaveRateLabelListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AdminSaveRateLabelList", ctx, in)
	ret0, _ := ret[0].(*AdminSaveRateLabelListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminSaveRateLabelList indicates an expected call of AdminSaveRateLabelList.
func (mr *MockGameUserRateServerMockRecorder) AdminSaveRateLabelList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminSaveRateLabelList", reflect.TypeOf((*MockGameUserRateServer)(nil).AdminSaveRateLabelList), ctx, in)
}

// AdminUpsertDimension mocks base method.
func (m *MockGameUserRateServer) AdminUpsertDimension(ctx context.Context, in *AdminUpsertDimensionReq) (*AdminUpsertDimensionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AdminUpsertDimension", ctx, in)
	ret0, _ := ret[0].(*AdminUpsertDimensionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AdminUpsertDimension indicates an expected call of AdminUpsertDimension.
func (mr *MockGameUserRateServerMockRecorder) AdminUpsertDimension(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminUpsertDimension", reflect.TypeOf((*MockGameUserRateServer)(nil).AdminUpsertDimension), ctx, in)
}

// BatchGetFirstChatToken mocks base method.
func (m *MockGameUserRateServer) BatchGetFirstChatToken(ctx context.Context, in *BatchGetFirstChatTokenReq) (*BatchGetFirstChatTokenResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetFirstChatToken", ctx, in)
	ret0, _ := ret[0].(*BatchGetFirstChatTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetFirstChatToken indicates an expected call of BatchGetFirstChatToken.
func (mr *MockGameUserRateServerMockRecorder) BatchGetFirstChatToken(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetFirstChatToken", reflect.TypeOf((*MockGameUserRateServer)(nil).BatchGetFirstChatToken), ctx, in)
}

// BatchSetFirstChatToken mocks base method.
func (m *MockGameUserRateServer) BatchSetFirstChatToken(ctx context.Context, in *BatchSetFirstChatTokenReq) (*BatchSetFirstChatTokenResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetFirstChatToken", ctx, in)
	ret0, _ := ret[0].(*BatchSetFirstChatTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchSetFirstChatToken indicates an expected call of BatchSetFirstChatToken.
func (mr *MockGameUserRateServerMockRecorder) BatchSetFirstChatToken(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetFirstChatToken", reflect.TypeOf((*MockGameUserRateServer)(nil).BatchSetFirstChatToken), ctx, in)
}

// CheckAddGameUserRate mocks base method.
func (m *MockGameUserRateServer) CheckAddGameUserRate(ctx context.Context, in *CheckAddGameUserRateReq) (*CheckAddGameUserRateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAddGameUserRate", ctx, in)
	ret0, _ := ret[0].(*CheckAddGameUserRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAddGameUserRate indicates an expected call of CheckAddGameUserRate.
func (mr *MockGameUserRateServerMockRecorder) CheckAddGameUserRate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAddGameUserRate", reflect.TypeOf((*MockGameUserRateServer)(nil).CheckAddGameUserRate), ctx, in)
}

// GetGameDimensionConf mocks base method.
func (m *MockGameUserRateServer) GetGameDimensionConf(ctx context.Context, in *GetGameDimensionConfReq) (*GetGameDimensionConfResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameDimensionConf", ctx, in)
	ret0, _ := ret[0].(*GetGameDimensionConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameDimensionConf indicates an expected call of GetGameDimensionConf.
func (mr *MockGameUserRateServerMockRecorder) GetGameDimensionConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameDimensionConf", reflect.TypeOf((*MockGameUserRateServer)(nil).GetGameDimensionConf), ctx, in)
}

// GetGameUserBeRateList mocks base method.
func (m *MockGameUserRateServer) GetGameUserBeRateList(ctx context.Context, in *GetGameUserBeRateListReq) (*GetGameUserBeRateListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameUserBeRateList", ctx, in)
	ret0, _ := ret[0].(*GetGameUserBeRateListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserBeRateList indicates an expected call of GetGameUserBeRateList.
func (mr *MockGameUserRateServerMockRecorder) GetGameUserBeRateList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserBeRateList", reflect.TypeOf((*MockGameUserRateServer)(nil).GetGameUserBeRateList), ctx, in)
}

// GetGameUserNotRateCount mocks base method.
func (m *MockGameUserRateServer) GetGameUserNotRateCount(ctx context.Context, in *GetGameUserNotRateCountReq) (*GetGameUserNotRateCountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameUserNotRateCount", ctx, in)
	ret0, _ := ret[0].(*GetGameUserNotRateCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserNotRateCount indicates an expected call of GetGameUserNotRateCount.
func (mr *MockGameUserRateServerMockRecorder) GetGameUserNotRateCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserNotRateCount", reflect.TypeOf((*MockGameUserRateServer)(nil).GetGameUserNotRateCount), ctx, in)
}

// GetGameUserPersonalImage mocks base method.
func (m *MockGameUserRateServer) GetGameUserPersonalImage(ctx context.Context, in *GetGameUserPersonalImageReq) (*GetGameUserPersonalImageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameUserPersonalImage", ctx, in)
	ret0, _ := ret[0].(*GetGameUserPersonalImageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserPersonalImage indicates an expected call of GetGameUserPersonalImage.
func (mr *MockGameUserRateServerMockRecorder) GetGameUserPersonalImage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserPersonalImage", reflect.TypeOf((*MockGameUserRateServer)(nil).GetGameUserPersonalImage), ctx, in)
}

// GetGameUserRateById mocks base method.
func (m *MockGameUserRateServer) GetGameUserRateById(ctx context.Context, in *GetGameUserRateByIdReq) (*GetGameUserRateByIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameUserRateById", ctx, in)
	ret0, _ := ret[0].(*GetGameUserRateByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserRateById indicates an expected call of GetGameUserRateById.
func (mr *MockGameUserRateServerMockRecorder) GetGameUserRateById(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserRateById", reflect.TypeOf((*MockGameUserRateServer)(nil).GetGameUserRateById), ctx, in)
}

// GetGameUserRateList mocks base method.
func (m *MockGameUserRateServer) GetGameUserRateList(ctx context.Context, in *GetGameUserRateListReq) (*GetGameUserRateListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameUserRateList", ctx, in)
	ret0, _ := ret[0].(*GetGameUserRateListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserRateList indicates an expected call of GetGameUserRateList.
func (mr *MockGameUserRateServerMockRecorder) GetGameUserRateList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserRateList", reflect.TypeOf((*MockGameUserRateServer)(nil).GetGameUserRateList), ctx, in)
}

// GetGameUserSelfRateQuestions mocks base method.
func (m *MockGameUserRateServer) GetGameUserSelfRateQuestions(ctx context.Context, in *GetGameUserSelfRateQuestionsReq) (*GetGameUserSelfRateQuestionsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameUserSelfRateQuestions", ctx, in)
	ret0, _ := ret[0].(*GetGameUserSelfRateQuestionsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameUserSelfRateQuestions indicates an expected call of GetGameUserSelfRateQuestions.
func (mr *MockGameUserRateServerMockRecorder) GetGameUserSelfRateQuestions(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameUserSelfRateQuestions", reflect.TypeOf((*MockGameUserRateServer)(nil).GetGameUserSelfRateQuestions), ctx, in)
}

// GetShowRateAndPunishConf mocks base method.
func (m *MockGameUserRateServer) GetShowRateAndPunishConf(ctx context.Context, in *GetShowRateAndPunishConfReq) (*GetShowRateAndPunishConfResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowRateAndPunishConf", ctx, in)
	ret0, _ := ret[0].(*GetShowRateAndPunishConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowRateAndPunishConf indicates an expected call of GetShowRateAndPunishConf.
func (mr *MockGameUserRateServerMockRecorder) GetShowRateAndPunishConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowRateAndPunishConf", reflect.TypeOf((*MockGameUserRateServer)(nil).GetShowRateAndPunishConf), ctx, in)
}

// GetShowRateAndPunishConfWithCache mocks base method.
func (m *MockGameUserRateServer) GetShowRateAndPunishConfWithCache(ctx context.Context, in *GetShowRateAndPunishConfReq) (*GetShowRateAndPunishConfResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShowRateAndPunishConfWithCache", ctx, in)
	ret0, _ := ret[0].(*GetShowRateAndPunishConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShowRateAndPunishConfWithCache indicates an expected call of GetShowRateAndPunishConfWithCache.
func (mr *MockGameUserRateServerMockRecorder) GetShowRateAndPunishConfWithCache(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShowRateAndPunishConfWithCache", reflect.TypeOf((*MockGameUserRateServer)(nil).GetShowRateAndPunishConfWithCache), ctx, in)
}

// GetTabByDimensionIds mocks base method.
func (m *MockGameUserRateServer) GetTabByDimensionIds(ctx context.Context, in *GetTabByDimensionIdsReq) (*GetTabByDimensionIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabByDimensionIds", ctx, in)
	ret0, _ := ret[0].(*GetTabByDimensionIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabByDimensionIds indicates an expected call of GetTabByDimensionIds.
func (mr *MockGameUserRateServerMockRecorder) GetTabByDimensionIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabByDimensionIds", reflect.TypeOf((*MockGameUserRateServer)(nil).GetTabByDimensionIds), ctx, in)
}

// GetUserReputationScore mocks base method.
func (m *MockGameUserRateServer) GetUserReputationScore(ctx context.Context, in *GetUserReputationScoreReq) (*GetUserReputationScoreResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserReputationScore", ctx, in)
	ret0, _ := ret[0].(*GetUserReputationScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserReputationScore indicates an expected call of GetUserReputationScore.
func (mr *MockGameUserRateServerMockRecorder) GetUserReputationScore(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserReputationScore", reflect.TypeOf((*MockGameUserRateServer)(nil).GetUserReputationScore), ctx, in)
}

// IsNeedPushAssist mocks base method.
func (m *MockGameUserRateServer) IsNeedPushAssist(ctx context.Context, in *IsNeedPushAssistReq) (*IsNeedPushAssistResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsNeedPushAssist", ctx, in)
	ret0, _ := ret[0].(*IsNeedPushAssistResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsNeedPushAssist indicates an expected call of IsNeedPushAssist.
func (mr *MockGameUserRateServerMockRecorder) IsNeedPushAssist(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsNeedPushAssist", reflect.TypeOf((*MockGameUserRateServer)(nil).IsNeedPushAssist), ctx, in)
}

// ReorderRateTags mocks base method.
func (m *MockGameUserRateServer) ReorderRateTags(ctx context.Context, in *ReorderRateTagsReq) (*ReorderRateTagsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReorderRateTags", ctx, in)
	ret0, _ := ret[0].(*ReorderRateTagsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReorderRateTags indicates an expected call of ReorderRateTags.
func (mr *MockGameUserRateServerMockRecorder) ReorderRateTags(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReorderRateTags", reflect.TypeOf((*MockGameUserRateServer)(nil).ReorderRateTags), ctx, in)
}

// SubmitGameUserRate mocks base method.
func (m *MockGameUserRateServer) SubmitGameUserRate(ctx context.Context, in *SubmitGameUserRateReq) (*SubmitGameUserRateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitGameUserRate", ctx, in)
	ret0, _ := ret[0].(*SubmitGameUserRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitGameUserRate indicates an expected call of SubmitGameUserRate.
func (mr *MockGameUserRateServerMockRecorder) SubmitGameUserRate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitGameUserRate", reflect.TypeOf((*MockGameUserRateServer)(nil).SubmitGameUserRate), ctx, in)
}

// SubmitGameUserSelfRate mocks base method.
func (m *MockGameUserRateServer) SubmitGameUserSelfRate(ctx context.Context, in *SubmitGameUserSelfRateReq) (*SubmitGameUserSelfRateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitGameUserSelfRate", ctx, in)
	ret0, _ := ret[0].(*SubmitGameUserSelfRateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitGameUserSelfRate indicates an expected call of SubmitGameUserSelfRate.
func (mr *MockGameUserRateServerMockRecorder) SubmitGameUserSelfRate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitGameUserSelfRate", reflect.TypeOf((*MockGameUserRateServer)(nil).SubmitGameUserSelfRate), ctx, in)
}

// UnionScore mocks base method.
func (m *MockGameUserRateServer) UnionScore(ctx context.Context, in *UnionScoreReq) (*UnionScoreResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnionScore", ctx, in)
	ret0, _ := ret[0].(*UnionScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnionScore indicates an expected call of UnionScore.
func (mr *MockGameUserRateServerMockRecorder) UnionScore(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnionScore", reflect.TypeOf((*MockGameUserRateServer)(nil).UnionScore), ctx, in)
}

// UpdateShowRateAndPunishConf mocks base method.
func (m *MockGameUserRateServer) UpdateShowRateAndPunishConf(ctx context.Context, in *UpdateShowRateAndPunishConfReq) (*UpdateShowRateAndPunishConfResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateShowRateAndPunishConf", ctx, in)
	ret0, _ := ret[0].(*UpdateShowRateAndPunishConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateShowRateAndPunishConf indicates an expected call of UpdateShowRateAndPunishConf.
func (mr *MockGameUserRateServerMockRecorder) UpdateShowRateAndPunishConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateShowRateAndPunishConf", reflect.TypeOf((*MockGameUserRateServer)(nil).UpdateShowRateAndPunishConf), ctx, in)
}
