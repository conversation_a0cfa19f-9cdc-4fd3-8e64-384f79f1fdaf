// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/muse-integration-middlelayer/muse-integration-middlelayer.proto

package muse_integration_middlelayer // import "golang.52tt.com/protocol/services/muse-integration-middlelayer"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import muse_biz_integration_middlelayer_logic "golang.52tt.com/protocol/app/muse_biz_integration_middlelayer_logic"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetMtEnterChannelPublicScreenExtendInfoRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMtEnterChannelPublicScreenExtendInfoRequest) Reset() {
	*m = GetMtEnterChannelPublicScreenExtendInfoRequest{}
}
func (m *GetMtEnterChannelPublicScreenExtendInfoRequest) String() string {
	return proto.CompactTextString(m)
}
func (*GetMtEnterChannelPublicScreenExtendInfoRequest) ProtoMessage() {}
func (*GetMtEnterChannelPublicScreenExtendInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_integration_middlelayer_0d0d32b7235790bb, []int{0}
}
func (m *GetMtEnterChannelPublicScreenExtendInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMtEnterChannelPublicScreenExtendInfoRequest.Unmarshal(m, b)
}
func (m *GetMtEnterChannelPublicScreenExtendInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMtEnterChannelPublicScreenExtendInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetMtEnterChannelPublicScreenExtendInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMtEnterChannelPublicScreenExtendInfoRequest.Merge(dst, src)
}
func (m *GetMtEnterChannelPublicScreenExtendInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetMtEnterChannelPublicScreenExtendInfoRequest.Size(m)
}
func (m *GetMtEnterChannelPublicScreenExtendInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMtEnterChannelPublicScreenExtendInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMtEnterChannelPublicScreenExtendInfoRequest proto.InternalMessageInfo

func (m *GetMtEnterChannelPublicScreenExtendInfoRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMtEnterChannelPublicScreenExtendInfoResponse struct {
	ChannelInfo          *muse_biz_integration_middlelayer_logic.MtEnterChannelPublicScreenExtend `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                                                 `json:"-"`
	XXX_unrecognized     []byte                                                                   `json:"-"`
	XXX_sizecache        int32                                                                    `json:"-"`
}

func (m *GetMtEnterChannelPublicScreenExtendInfoResponse) Reset() {
	*m = GetMtEnterChannelPublicScreenExtendInfoResponse{}
}
func (m *GetMtEnterChannelPublicScreenExtendInfoResponse) String() string {
	return proto.CompactTextString(m)
}
func (*GetMtEnterChannelPublicScreenExtendInfoResponse) ProtoMessage() {}
func (*GetMtEnterChannelPublicScreenExtendInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_integration_middlelayer_0d0d32b7235790bb, []int{1}
}
func (m *GetMtEnterChannelPublicScreenExtendInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMtEnterChannelPublicScreenExtendInfoResponse.Unmarshal(m, b)
}
func (m *GetMtEnterChannelPublicScreenExtendInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMtEnterChannelPublicScreenExtendInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetMtEnterChannelPublicScreenExtendInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMtEnterChannelPublicScreenExtendInfoResponse.Merge(dst, src)
}
func (m *GetMtEnterChannelPublicScreenExtendInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetMtEnterChannelPublicScreenExtendInfoResponse.Size(m)
}
func (m *GetMtEnterChannelPublicScreenExtendInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMtEnterChannelPublicScreenExtendInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMtEnterChannelPublicScreenExtendInfoResponse proto.InternalMessageInfo

func (m *GetMtEnterChannelPublicScreenExtendInfoResponse) GetChannelInfo() *muse_biz_integration_middlelayer_logic.MtEnterChannelPublicScreenExtend {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

type GetHomePageHeadMuseConfigRequest struct {
	ConfigTypes          []uint32 `protobuf:"varint,1,rep,packed,name=config_types,json=configTypes,proto3" json:"config_types,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHomePageHeadMuseConfigRequest) Reset()         { *m = GetHomePageHeadMuseConfigRequest{} }
func (m *GetHomePageHeadMuseConfigRequest) String() string { return proto.CompactTextString(m) }
func (*GetHomePageHeadMuseConfigRequest) ProtoMessage()    {}
func (*GetHomePageHeadMuseConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_integration_middlelayer_0d0d32b7235790bb, []int{2}
}
func (m *GetHomePageHeadMuseConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHomePageHeadMuseConfigRequest.Unmarshal(m, b)
}
func (m *GetHomePageHeadMuseConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHomePageHeadMuseConfigRequest.Marshal(b, m, deterministic)
}
func (dst *GetHomePageHeadMuseConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHomePageHeadMuseConfigRequest.Merge(dst, src)
}
func (m *GetHomePageHeadMuseConfigRequest) XXX_Size() int {
	return xxx_messageInfo_GetHomePageHeadMuseConfigRequest.Size(m)
}
func (m *GetHomePageHeadMuseConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHomePageHeadMuseConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetHomePageHeadMuseConfigRequest proto.InternalMessageInfo

func (m *GetHomePageHeadMuseConfigRequest) GetConfigTypes() []uint32 {
	if m != nil {
		return m.ConfigTypes
	}
	return nil
}

type GetHomePageHeadMuseConfigResponse struct {
	ConfigDataMap        map[uint32]*HomePageHeadMuseConfig `protobuf:"bytes,1,rep,name=config_data_map,json=configDataMap,proto3" json:"config_data_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ConfigDataMapList    map[uint32]*MuseConfigList         `protobuf:"bytes,2,rep,name=config_data_map_list,json=configDataMapList,proto3" json:"config_data_map_list,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *GetHomePageHeadMuseConfigResponse) Reset()         { *m = GetHomePageHeadMuseConfigResponse{} }
func (m *GetHomePageHeadMuseConfigResponse) String() string { return proto.CompactTextString(m) }
func (*GetHomePageHeadMuseConfigResponse) ProtoMessage()    {}
func (*GetHomePageHeadMuseConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_integration_middlelayer_0d0d32b7235790bb, []int{3}
}
func (m *GetHomePageHeadMuseConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHomePageHeadMuseConfigResponse.Unmarshal(m, b)
}
func (m *GetHomePageHeadMuseConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHomePageHeadMuseConfigResponse.Marshal(b, m, deterministic)
}
func (dst *GetHomePageHeadMuseConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHomePageHeadMuseConfigResponse.Merge(dst, src)
}
func (m *GetHomePageHeadMuseConfigResponse) XXX_Size() int {
	return xxx_messageInfo_GetHomePageHeadMuseConfigResponse.Size(m)
}
func (m *GetHomePageHeadMuseConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHomePageHeadMuseConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetHomePageHeadMuseConfigResponse proto.InternalMessageInfo

func (m *GetHomePageHeadMuseConfigResponse) GetConfigDataMap() map[uint32]*HomePageHeadMuseConfig {
	if m != nil {
		return m.ConfigDataMap
	}
	return nil
}

func (m *GetHomePageHeadMuseConfigResponse) GetConfigDataMapList() map[uint32]*MuseConfigList {
	if m != nil {
		return m.ConfigDataMapList
	}
	return nil
}

type MuseConfigList struct {
	ConfigList           []*HomePageHeadMuseConfig `protobuf:"bytes,1,rep,name=config_list,json=configList,proto3" json:"config_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *MuseConfigList) Reset()         { *m = MuseConfigList{} }
func (m *MuseConfigList) String() string { return proto.CompactTextString(m) }
func (*MuseConfigList) ProtoMessage()    {}
func (*MuseConfigList) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_integration_middlelayer_0d0d32b7235790bb, []int{4}
}
func (m *MuseConfigList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseConfigList.Unmarshal(m, b)
}
func (m *MuseConfigList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseConfigList.Marshal(b, m, deterministic)
}
func (dst *MuseConfigList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseConfigList.Merge(dst, src)
}
func (m *MuseConfigList) XXX_Size() int {
	return xxx_messageInfo_MuseConfigList.Size(m)
}
func (m *MuseConfigList) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseConfigList.DiscardUnknown(m)
}

var xxx_messageInfo_MuseConfigList proto.InternalMessageInfo

func (m *MuseConfigList) GetConfigList() []*HomePageHeadMuseConfig {
	if m != nil {
		return m.ConfigList
	}
	return nil
}

// muse-integration-middlelayer.proto
type HomePageHeadMuseConfig struct {
	// 专区类型
	ConfigType uint32 `protobuf:"varint,1,opt,name=config_type,json=configType,proto3" json:"config_type,omitempty"`
	// 主标题
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// 副标题
	SubTitle string `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	// 底图， 旧版代表展示两个一行的大图
	Background           string   `protobuf:"bytes,4,opt,name=background,proto3" json:"background,omitempty"`
	JumpLink             string   `protobuf:"bytes,5,opt,name=jump_link,json=jumpLink,proto3" json:"jump_link,omitempty"`
	SmallBackground      string   `protobuf:"bytes,6,opt,name=small_background,json=smallBackground,proto3" json:"small_background,omitempty"`
	ExtraData            []byte   `protobuf:"bytes,7,opt,name=extra_data,json=extraData,proto3" json:"extra_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HomePageHeadMuseConfig) Reset()         { *m = HomePageHeadMuseConfig{} }
func (m *HomePageHeadMuseConfig) String() string { return proto.CompactTextString(m) }
func (*HomePageHeadMuseConfig) ProtoMessage()    {}
func (*HomePageHeadMuseConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_integration_middlelayer_0d0d32b7235790bb, []int{5}
}
func (m *HomePageHeadMuseConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HomePageHeadMuseConfig.Unmarshal(m, b)
}
func (m *HomePageHeadMuseConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HomePageHeadMuseConfig.Marshal(b, m, deterministic)
}
func (dst *HomePageHeadMuseConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomePageHeadMuseConfig.Merge(dst, src)
}
func (m *HomePageHeadMuseConfig) XXX_Size() int {
	return xxx_messageInfo_HomePageHeadMuseConfig.Size(m)
}
func (m *HomePageHeadMuseConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_HomePageHeadMuseConfig.DiscardUnknown(m)
}

var xxx_messageInfo_HomePageHeadMuseConfig proto.InternalMessageInfo

func (m *HomePageHeadMuseConfig) GetConfigType() uint32 {
	if m != nil {
		return m.ConfigType
	}
	return 0
}

func (m *HomePageHeadMuseConfig) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *HomePageHeadMuseConfig) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *HomePageHeadMuseConfig) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *HomePageHeadMuseConfig) GetJumpLink() string {
	if m != nil {
		return m.JumpLink
	}
	return ""
}

func (m *HomePageHeadMuseConfig) GetSmallBackground() string {
	if m != nil {
		return m.SmallBackground
	}
	return ""
}

func (m *HomePageHeadMuseConfig) GetExtraData() []byte {
	if m != nil {
		return m.ExtraData
	}
	return nil
}

func init() {
	proto.RegisterType((*GetMtEnterChannelPublicScreenExtendInfoRequest)(nil), "muse_integration_middlelayer.GetMtEnterChannelPublicScreenExtendInfoRequest")
	proto.RegisterType((*GetMtEnterChannelPublicScreenExtendInfoResponse)(nil), "muse_integration_middlelayer.GetMtEnterChannelPublicScreenExtendInfoResponse")
	proto.RegisterType((*GetHomePageHeadMuseConfigRequest)(nil), "muse_integration_middlelayer.GetHomePageHeadMuseConfigRequest")
	proto.RegisterType((*GetHomePageHeadMuseConfigResponse)(nil), "muse_integration_middlelayer.GetHomePageHeadMuseConfigResponse")
	proto.RegisterMapType((map[uint32]*HomePageHeadMuseConfig)(nil), "muse_integration_middlelayer.GetHomePageHeadMuseConfigResponse.ConfigDataMapEntry")
	proto.RegisterMapType((map[uint32]*MuseConfigList)(nil), "muse_integration_middlelayer.GetHomePageHeadMuseConfigResponse.ConfigDataMapListEntry")
	proto.RegisterType((*MuseConfigList)(nil), "muse_integration_middlelayer.MuseConfigList")
	proto.RegisterType((*HomePageHeadMuseConfig)(nil), "muse_integration_middlelayer.HomePageHeadMuseConfig")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MuseIntegrationMiddlelayerClient is the client API for MuseIntegrationMiddlelayer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MuseIntegrationMiddlelayerClient interface {
	GetMtEnterChannelPublicScreenExtendInfo(ctx context.Context, in *GetMtEnterChannelPublicScreenExtendInfoRequest, opts ...grpc.CallOption) (*GetMtEnterChannelPublicScreenExtendInfoResponse, error)
	GetHomePageHeadMuseConfig(ctx context.Context, in *GetHomePageHeadMuseConfigRequest, opts ...grpc.CallOption) (*GetHomePageHeadMuseConfigResponse, error)
}

type museIntegrationMiddlelayerClient struct {
	cc *grpc.ClientConn
}

func NewMuseIntegrationMiddlelayerClient(cc *grpc.ClientConn) MuseIntegrationMiddlelayerClient {
	return &museIntegrationMiddlelayerClient{cc}
}

func (c *museIntegrationMiddlelayerClient) GetMtEnterChannelPublicScreenExtendInfo(ctx context.Context, in *GetMtEnterChannelPublicScreenExtendInfoRequest, opts ...grpc.CallOption) (*GetMtEnterChannelPublicScreenExtendInfoResponse, error) {
	out := new(GetMtEnterChannelPublicScreenExtendInfoResponse)
	err := c.cc.Invoke(ctx, "/muse_integration_middlelayer.MuseIntegrationMiddlelayer/GetMtEnterChannelPublicScreenExtendInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museIntegrationMiddlelayerClient) GetHomePageHeadMuseConfig(ctx context.Context, in *GetHomePageHeadMuseConfigRequest, opts ...grpc.CallOption) (*GetHomePageHeadMuseConfigResponse, error) {
	out := new(GetHomePageHeadMuseConfigResponse)
	err := c.cc.Invoke(ctx, "/muse_integration_middlelayer.MuseIntegrationMiddlelayer/GetHomePageHeadMuseConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MuseIntegrationMiddlelayerServer is the server API for MuseIntegrationMiddlelayer service.
type MuseIntegrationMiddlelayerServer interface {
	GetMtEnterChannelPublicScreenExtendInfo(context.Context, *GetMtEnterChannelPublicScreenExtendInfoRequest) (*GetMtEnterChannelPublicScreenExtendInfoResponse, error)
	GetHomePageHeadMuseConfig(context.Context, *GetHomePageHeadMuseConfigRequest) (*GetHomePageHeadMuseConfigResponse, error)
}

func RegisterMuseIntegrationMiddlelayerServer(s *grpc.Server, srv MuseIntegrationMiddlelayerServer) {
	s.RegisterService(&_MuseIntegrationMiddlelayer_serviceDesc, srv)
}

func _MuseIntegrationMiddlelayer_GetMtEnterChannelPublicScreenExtendInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMtEnterChannelPublicScreenExtendInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseIntegrationMiddlelayerServer).GetMtEnterChannelPublicScreenExtendInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_integration_middlelayer.MuseIntegrationMiddlelayer/GetMtEnterChannelPublicScreenExtendInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseIntegrationMiddlelayerServer).GetMtEnterChannelPublicScreenExtendInfo(ctx, req.(*GetMtEnterChannelPublicScreenExtendInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseIntegrationMiddlelayer_GetHomePageHeadMuseConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHomePageHeadMuseConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseIntegrationMiddlelayerServer).GetHomePageHeadMuseConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_integration_middlelayer.MuseIntegrationMiddlelayer/GetHomePageHeadMuseConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseIntegrationMiddlelayerServer).GetHomePageHeadMuseConfig(ctx, req.(*GetHomePageHeadMuseConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MuseIntegrationMiddlelayer_serviceDesc = grpc.ServiceDesc{
	ServiceName: "muse_integration_middlelayer.MuseIntegrationMiddlelayer",
	HandlerType: (*MuseIntegrationMiddlelayerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMtEnterChannelPublicScreenExtendInfo",
			Handler:    _MuseIntegrationMiddlelayer_GetMtEnterChannelPublicScreenExtendInfo_Handler,
		},
		{
			MethodName: "GetHomePageHeadMuseConfig",
			Handler:    _MuseIntegrationMiddlelayer_GetHomePageHeadMuseConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/muse-integration-middlelayer/muse-integration-middlelayer.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/muse-integration-middlelayer/muse-integration-middlelayer.proto", fileDescriptor_muse_integration_middlelayer_0d0d32b7235790bb)
}

var fileDescriptor_muse_integration_middlelayer_0d0d32b7235790bb = []byte{
	// 675 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x55, 0x41, 0x53, 0xd3, 0x4c,
	0x18, 0x26, 0x14, 0xf8, 0x3e, 0xde, 0xc2, 0x07, 0xdf, 0x0e, 0xc3, 0xd4, 0x88, 0x58, 0xe2, 0x41,
	0x9c, 0x91, 0x64, 0xa6, 0xea, 0x8c, 0xe3, 0x01, 0x9d, 0x62, 0x07, 0x50, 0xea, 0x30, 0x01, 0x3d,
	0x78, 0xc9, 0x6c, 0x93, 0x25, 0xae, 0x4d, 0x76, 0x4b, 0x76, 0xd3, 0xa1, 0xfc, 0x01, 0x7f, 0x81,
	0x57, 0x67, 0xfc, 0x01, 0x9e, 0xfd, 0x0b, 0x1e, 0xfc, 0x37, 0xde, 0x3c, 0x39, 0xbb, 0x5b, 0x68,
	0x41, 0x68, 0x2b, 0x72, 0x6a, 0xf3, 0xbe, 0x4f, 0x9e, 0xe7, 0x79, 0x93, 0xe7, 0xcd, 0xc2, 0x2b,
	0x29, 0xbd, 0xc3, 0x9c, 0x86, 0x4d, 0x41, 0x93, 0x36, 0xc9, 0xbc, 0x34, 0x17, 0x64, 0x8d, 0x32,
	0x49, 0xe2, 0x0c, 0x4b, 0xca, 0xd9, 0x5a, 0x4a, 0xa3, 0x28, 0x21, 0x09, 0xee, 0x0c, 0x69, 0xba,
	0xad, 0x8c, 0x4b, 0x8e, 0x96, 0x14, 0x26, 0xe8, 0xc3, 0x04, 0x7d, 0x18, 0xdb, 0x3d, 0xa7, 0x46,
	0x8e, 0x24, 0x61, 0x82, 0x72, 0xe6, 0xf1, 0x96, 0x02, 0x8b, 0x93, 0x5f, 0xc3, 0x66, 0xef, 0x69,
	0xb6, 0x06, 0x3d, 0xbe, 0x8c, 0x31, 0x48, 0x78, 0x4c, 0x43, 0x6f, 0x34, 0x98, 0x21, 0x75, 0xaa,
	0xe0, 0x6e, 0x12, 0x59, 0x97, 0x35, 0x26, 0x49, 0xb6, 0xf1, 0x0e, 0x33, 0x46, 0x92, 0xdd, 0xbc,
	0x91, 0xd0, 0x70, 0x2f, 0xcc, 0x08, 0x61, 0x35, 0x65, 0x2a, 0xda, 0x66, 0x07, 0xdc, 0x27, 0x87,
	0x39, 0x11, 0x12, 0xcd, 0x43, 0x21, 0xa7, 0x51, 0xc9, 0x2a, 0x5b, 0xab, 0xb3, 0xbe, 0xfa, 0xeb,
	0x7c, 0xb6, 0xc0, 0x1b, 0x99, 0x44, 0xb4, 0x38, 0x13, 0x04, 0x31, 0x98, 0x09, 0x0d, 0x30, 0xa0,
	0xec, 0x80, 0x6b, 0xba, 0x62, 0xe5, 0xa5, 0x1b, 0x63, 0x77, 0x44, 0xff, 0xc3, 0xe4, 0xfc, 0x62,
	0x57, 0x40, 0xe9, 0x3a, 0x35, 0x28, 0x6f, 0x12, 0xb9, 0xc5, 0x53, 0xb2, 0x8b, 0x63, 0xb2, 0x45,
	0x70, 0x54, 0xcf, 0x05, 0xd9, 0xe0, 0xec, 0x80, 0xc6, 0x27, 0x93, 0xad, 0xc0, 0x4c, 0xa8, 0x0b,
	0x81, 0xec, 0xb4, 0x88, 0x28, 0x59, 0xe5, 0xc2, 0xea, 0xac, 0x5f, 0x34, 0xb5, 0x7d, 0x55, 0x72,
	0x3e, 0x4e, 0xc0, 0xca, 0x00, 0x9e, 0xee, 0x70, 0xc7, 0x30, 0xd7, 0x25, 0x8a, 0xb0, 0xc4, 0x41,
	0x8a, 0x5b, 0x9a, 0xab, 0x58, 0xf1, 0xdd, 0x41, 0x89, 0x70, 0x87, 0x32, 0xbb, 0xe6, 0xf2, 0x39,
	0x96, 0xb8, 0x8e, 0x5b, 0x35, 0x26, 0xb3, 0x8e, 0x3f, 0x1b, 0xf6, 0xd7, 0xd0, 0x07, 0x0b, 0x16,
	0xce, 0x89, 0x07, 0x09, 0x15, 0xb2, 0x34, 0xae, 0x1d, 0xbc, 0xb9, 0x56, 0x07, 0x3b, 0x54, 0x48,
	0xe3, 0xe2, 0xff, 0xf0, 0x7c, 0xdd, 0x6e, 0x03, 0xfa, 0xdd, 0xae, 0x8a, 0x4f, 0x93, 0x74, 0x4e,
	0xe2, 0xd3, 0x24, 0x1d, 0xf4, 0x02, 0x26, 0xdb, 0x38, 0xc9, 0x49, 0x69, 0x5c, 0x67, 0xe0, 0xe1,
	0x60, 0x87, 0x97, 0xd8, 0x33, 0x14, 0x4f, 0xc6, 0x1f, 0x5b, 0x76, 0x06, 0x8b, 0x17, 0x9b, 0xbc,
	0x40, 0xbb, 0x7a, 0x56, 0xfb, 0xfe, 0x60, 0xed, 0x9e, 0x9e, 0xe2, 0xec, 0xd3, 0x74, 0x62, 0xf8,
	0xef, 0x6c, 0x13, 0xbd, 0x86, 0x6e, 0x70, 0xcc, 0xd3, 0x37, 0xef, 0xff, 0x6a, 0xb3, 0x41, 0x78,
	0x4a, 0xeb, 0xfc, 0xb0, 0x60, 0xf1, 0x62, 0x18, 0xba, 0x7d, 0xaa, 0xa8, 0xe2, 0xdb, 0x9d, 0x12,
	0x7a, 0xe9, 0x45, 0x0b, 0x30, 0x29, 0xa9, 0x4c, 0xcc, 0xb0, 0xd3, 0xbe, 0xb9, 0x40, 0x37, 0x61,
	0x5a, 0xe4, 0x8d, 0xc0, 0x74, 0x0a, 0xba, 0xf3, 0xaf, 0xc8, 0x1b, 0xfb, 0xba, 0xb9, 0x0c, 0xd0,
	0xc0, 0x61, 0x33, 0xce, 0x78, 0xce, 0xa2, 0xd2, 0x84, 0xee, 0xf6, 0x55, 0xd4, 0xcd, 0xef, 0xf3,
	0x54, 0x25, 0x8c, 0x35, 0x4b, 0x93, 0xe6, 0x66, 0x55, 0xd8, 0xa1, 0xac, 0x89, 0xee, 0xc1, 0xbc,
	0x48, 0x71, 0x92, 0x04, 0x7d, 0x14, 0x53, 0x1a, 0x33, 0xa7, 0xeb, 0xd5, 0x1e, 0xcf, 0x2d, 0x00,
	0x72, 0x24, 0x33, 0xac, 0x33, 0x5b, 0xfa, 0xa7, 0x6c, 0xad, 0xce, 0xf8, 0xd3, 0xba, 0xa2, 0x5e,
	0x62, 0xe5, 0x4b, 0x01, 0x6c, 0x35, 0xe9, 0x76, 0xef, 0xc1, 0xd5, 0x7b, 0xcf, 0x0d, 0x7d, 0xb3,
	0xe0, 0xee, 0x88, 0x1f, 0x20, 0xb4, 0x33, 0x74, 0x01, 0xfe, 0xe0, 0x63, 0x68, 0xd7, 0xaf, 0x89,
	0xcd, 0x2c, 0x97, 0x33, 0x86, 0x3e, 0x59, 0x70, 0xe3, 0xd2, 0x25, 0x44, 0xeb, 0x57, 0xde, 0x5e,
	0x63, 0xf7, 0xe9, 0x5f, 0x6e, 0xbf, 0x33, 0x66, 0xdf, 0xf9, 0xf9, 0xf5, 0xfb, 0xfe, 0x32, 0x2c,
	0x0d, 0x3a, 0xfe, 0xaa, 0xcf, 0xde, 0xae, 0xc7, 0x3c, 0xc1, 0x2c, 0x76, 0x1f, 0x55, 0xa4, 0x74,
	0x43, 0x9e, 0x7a, 0xfa, 0xb8, 0x09, 0x79, 0xe2, 0x09, 0x92, 0xb5, 0x69, 0x48, 0xc4, 0xc0, 0x03,
	0xb4, 0x31, 0xa5, 0xf1, 0x0f, 0x7e, 0x05, 0x00, 0x00, 0xff, 0xff, 0xf5, 0x48, 0x11, 0x4a, 0x93,
	0x07, 0x00, 0x00,
}
