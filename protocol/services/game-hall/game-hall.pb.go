// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/game-hall/game-hall.proto

package game_hall // import "golang.52tt.com/protocol/services/game-hall"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GameHallPinConfStatus int32

const (
	GameHallPinConfStatus_STATUS_UNSPECIFIED GameHallPinConfStatus = 0
	GameHallPinConfStatus_STATUS_NOT_ACTIVE  GameHallPinConfStatus = 1
	GameHallPinConfStatus_STATUS_ACTIVE      GameHallPinConfStatus = 2
	GameHallPinConfStatus_STATUS_EXPIRED     GameHallPinConfStatus = 3
)

var GameHallPinConfStatus_name = map[int32]string{
	0: "STATUS_UNSPECIFIED",
	1: "STATUS_NOT_ACTIVE",
	2: "STATUS_ACTIVE",
	3: "STATUS_EXPIRED",
}
var GameHallPinConfStatus_value = map[string]int32{
	"STATUS_UNSPECIFIED": 0,
	"STATUS_NOT_ACTIVE":  1,
	"STATUS_ACTIVE":      2,
	"STATUS_EXPIRED":     3,
}

func (x GameHallPinConfStatus) String() string {
	return proto.EnumName(GameHallPinConfStatus_name, int32(x))
}
func (GameHallPinConfStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{0}
}

// 入口展示来源
type EntranceSettingItem_EntranceSource int32

const (
	EntranceSettingItem_ENTRANCE_SOURCE_UNSPECIFIED EntranceSettingItem_EntranceSource = 0
	// 极速PC的IM列表常驻入口
	EntranceSettingItem_ENTRANCE_SOURCE_PC_IM EntranceSettingItem_EntranceSource = 1
)

var EntranceSettingItem_EntranceSource_name = map[int32]string{
	0: "ENTRANCE_SOURCE_UNSPECIFIED",
	1: "ENTRANCE_SOURCE_PC_IM",
}
var EntranceSettingItem_EntranceSource_value = map[string]int32{
	"ENTRANCE_SOURCE_UNSPECIFIED": 0,
	"ENTRANCE_SOURCE_PC_IM":       1,
}

func (x EntranceSettingItem_EntranceSource) String() string {
	return proto.EnumName(EntranceSettingItem_EntranceSource_name, int32(x))
}
func (EntranceSettingItem_EntranceSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{49, 0}
}

type AddCleanMsgRecordReq struct {
	TabIds               []uint32 `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCleanMsgRecordReq) Reset()         { *m = AddCleanMsgRecordReq{} }
func (m *AddCleanMsgRecordReq) String() string { return proto.CompactTextString(m) }
func (*AddCleanMsgRecordReq) ProtoMessage()    {}
func (*AddCleanMsgRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{0}
}
func (m *AddCleanMsgRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCleanMsgRecordReq.Unmarshal(m, b)
}
func (m *AddCleanMsgRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCleanMsgRecordReq.Marshal(b, m, deterministic)
}
func (dst *AddCleanMsgRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCleanMsgRecordReq.Merge(dst, src)
}
func (m *AddCleanMsgRecordReq) XXX_Size() int {
	return xxx_messageInfo_AddCleanMsgRecordReq.Size(m)
}
func (m *AddCleanMsgRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCleanMsgRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddCleanMsgRecordReq proto.InternalMessageInfo

func (m *AddCleanMsgRecordReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type AddCleanMsgRecordResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCleanMsgRecordResp) Reset()         { *m = AddCleanMsgRecordResp{} }
func (m *AddCleanMsgRecordResp) String() string { return proto.CompactTextString(m) }
func (*AddCleanMsgRecordResp) ProtoMessage()    {}
func (*AddCleanMsgRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{1}
}
func (m *AddCleanMsgRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCleanMsgRecordResp.Unmarshal(m, b)
}
func (m *AddCleanMsgRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCleanMsgRecordResp.Marshal(b, m, deterministic)
}
func (dst *AddCleanMsgRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCleanMsgRecordResp.Merge(dst, src)
}
func (m *AddCleanMsgRecordResp) XXX_Size() int {
	return xxx_messageInfo_AddCleanMsgRecordResp.Size(m)
}
func (m *AddCleanMsgRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCleanMsgRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddCleanMsgRecordResp proto.InternalMessageInfo

type AddMsgRecordReq struct {
	MsgId                uint64   `protobuf:"varint,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	SendTime             int64    `protobuf:"varint,3,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddMsgRecordReq) Reset()         { *m = AddMsgRecordReq{} }
func (m *AddMsgRecordReq) String() string { return proto.CompactTextString(m) }
func (*AddMsgRecordReq) ProtoMessage()    {}
func (*AddMsgRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{2}
}
func (m *AddMsgRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMsgRecordReq.Unmarshal(m, b)
}
func (m *AddMsgRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMsgRecordReq.Marshal(b, m, deterministic)
}
func (dst *AddMsgRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMsgRecordReq.Merge(dst, src)
}
func (m *AddMsgRecordReq) XXX_Size() int {
	return xxx_messageInfo_AddMsgRecordReq.Size(m)
}
func (m *AddMsgRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMsgRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddMsgRecordReq proto.InternalMessageInfo

func (m *AddMsgRecordReq) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *AddMsgRecordReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *AddMsgRecordReq) GetSendTime() int64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

type AddMsgRecordResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddMsgRecordResp) Reset()         { *m = AddMsgRecordResp{} }
func (m *AddMsgRecordResp) String() string { return proto.CompactTextString(m) }
func (*AddMsgRecordResp) ProtoMessage()    {}
func (*AddMsgRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{3}
}
func (m *AddMsgRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMsgRecordResp.Unmarshal(m, b)
}
func (m *AddMsgRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMsgRecordResp.Marshal(b, m, deterministic)
}
func (dst *AddMsgRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMsgRecordResp.Merge(dst, src)
}
func (m *AddMsgRecordResp) XXX_Size() int {
	return xxx_messageInfo_AddMsgRecordResp.Size(m)
}
func (m *AddMsgRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMsgRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddMsgRecordResp proto.InternalMessageInfo

type GetLastMsgIdBySendTimeReq struct {
	SendTime             int64    `protobuf:"varint,1,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastMsgIdBySendTimeReq) Reset()         { *m = GetLastMsgIdBySendTimeReq{} }
func (m *GetLastMsgIdBySendTimeReq) String() string { return proto.CompactTextString(m) }
func (*GetLastMsgIdBySendTimeReq) ProtoMessage()    {}
func (*GetLastMsgIdBySendTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{4}
}
func (m *GetLastMsgIdBySendTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastMsgIdBySendTimeReq.Unmarshal(m, b)
}
func (m *GetLastMsgIdBySendTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastMsgIdBySendTimeReq.Marshal(b, m, deterministic)
}
func (dst *GetLastMsgIdBySendTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastMsgIdBySendTimeReq.Merge(dst, src)
}
func (m *GetLastMsgIdBySendTimeReq) XXX_Size() int {
	return xxx_messageInfo_GetLastMsgIdBySendTimeReq.Size(m)
}
func (m *GetLastMsgIdBySendTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastMsgIdBySendTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastMsgIdBySendTimeReq proto.InternalMessageInfo

func (m *GetLastMsgIdBySendTimeReq) GetSendTime() int64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *GetLastMsgIdBySendTimeReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetLastMsgIdBySendTimeResp struct {
	MsgId                uint64   `protobuf:"varint,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastMsgIdBySendTimeResp) Reset()         { *m = GetLastMsgIdBySendTimeResp{} }
func (m *GetLastMsgIdBySendTimeResp) String() string { return proto.CompactTextString(m) }
func (*GetLastMsgIdBySendTimeResp) ProtoMessage()    {}
func (*GetLastMsgIdBySendTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{5}
}
func (m *GetLastMsgIdBySendTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastMsgIdBySendTimeResp.Unmarshal(m, b)
}
func (m *GetLastMsgIdBySendTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastMsgIdBySendTimeResp.Marshal(b, m, deterministic)
}
func (dst *GetLastMsgIdBySendTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastMsgIdBySendTimeResp.Merge(dst, src)
}
func (m *GetLastMsgIdBySendTimeResp) XXX_Size() int {
	return xxx_messageInfo_GetLastMsgIdBySendTimeResp.Size(m)
}
func (m *GetLastMsgIdBySendTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastMsgIdBySendTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastMsgIdBySendTimeResp proto.InternalMessageInfo

func (m *GetLastMsgIdBySendTimeResp) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

type AddTeamAndInviteMsgReq struct {
	MsgId                uint64   `protobuf:"varint,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTeamAndInviteMsgReq) Reset()         { *m = AddTeamAndInviteMsgReq{} }
func (m *AddTeamAndInviteMsgReq) String() string { return proto.CompactTextString(m) }
func (*AddTeamAndInviteMsgReq) ProtoMessage()    {}
func (*AddTeamAndInviteMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{6}
}
func (m *AddTeamAndInviteMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTeamAndInviteMsgReq.Unmarshal(m, b)
}
func (m *AddTeamAndInviteMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTeamAndInviteMsgReq.Marshal(b, m, deterministic)
}
func (dst *AddTeamAndInviteMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTeamAndInviteMsgReq.Merge(dst, src)
}
func (m *AddTeamAndInviteMsgReq) XXX_Size() int {
	return xxx_messageInfo_AddTeamAndInviteMsgReq.Size(m)
}
func (m *AddTeamAndInviteMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTeamAndInviteMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddTeamAndInviteMsgReq proto.InternalMessageInfo

func (m *AddTeamAndInviteMsgReq) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *AddTeamAndInviteMsgReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type AddTeamAndInviteMsgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTeamAndInviteMsgResp) Reset()         { *m = AddTeamAndInviteMsgResp{} }
func (m *AddTeamAndInviteMsgResp) String() string { return proto.CompactTextString(m) }
func (*AddTeamAndInviteMsgResp) ProtoMessage()    {}
func (*AddTeamAndInviteMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{7}
}
func (m *AddTeamAndInviteMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTeamAndInviteMsgResp.Unmarshal(m, b)
}
func (m *AddTeamAndInviteMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTeamAndInviteMsgResp.Marshal(b, m, deterministic)
}
func (dst *AddTeamAndInviteMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTeamAndInviteMsgResp.Merge(dst, src)
}
func (m *AddTeamAndInviteMsgResp) XXX_Size() int {
	return xxx_messageInfo_AddTeamAndInviteMsgResp.Size(m)
}
func (m *AddTeamAndInviteMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTeamAndInviteMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddTeamAndInviteMsgResp proto.InternalMessageInfo

type JoinGameHallTeamReq struct {
	MsgId                uint64   `protobuf:"varint,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	SendUid              uint32   `protobuf:"varint,3,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	JoinUid              uint32   `protobuf:"varint,4,opt,name=join_uid,json=joinUid,proto3" json:"join_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinGameHallTeamReq) Reset()         { *m = JoinGameHallTeamReq{} }
func (m *JoinGameHallTeamReq) String() string { return proto.CompactTextString(m) }
func (*JoinGameHallTeamReq) ProtoMessage()    {}
func (*JoinGameHallTeamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{8}
}
func (m *JoinGameHallTeamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinGameHallTeamReq.Unmarshal(m, b)
}
func (m *JoinGameHallTeamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinGameHallTeamReq.Marshal(b, m, deterministic)
}
func (dst *JoinGameHallTeamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinGameHallTeamReq.Merge(dst, src)
}
func (m *JoinGameHallTeamReq) XXX_Size() int {
	return xxx_messageInfo_JoinGameHallTeamReq.Size(m)
}
func (m *JoinGameHallTeamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinGameHallTeamReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinGameHallTeamReq proto.InternalMessageInfo

func (m *JoinGameHallTeamReq) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *JoinGameHallTeamReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *JoinGameHallTeamReq) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *JoinGameHallTeamReq) GetJoinUid() uint32 {
	if m != nil {
		return m.JoinUid
	}
	return 0
}

type JoinGameHallTeamResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinGameHallTeamResp) Reset()         { *m = JoinGameHallTeamResp{} }
func (m *JoinGameHallTeamResp) String() string { return proto.CompactTextString(m) }
func (*JoinGameHallTeamResp) ProtoMessage()    {}
func (*JoinGameHallTeamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{9}
}
func (m *JoinGameHallTeamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinGameHallTeamResp.Unmarshal(m, b)
}
func (m *JoinGameHallTeamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinGameHallTeamResp.Marshal(b, m, deterministic)
}
func (dst *JoinGameHallTeamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinGameHallTeamResp.Merge(dst, src)
}
func (m *JoinGameHallTeamResp) XXX_Size() int {
	return xxx_messageInfo_JoinGameHallTeamResp.Size(m)
}
func (m *JoinGameHallTeamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinGameHallTeamResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinGameHallTeamResp proto.InternalMessageInfo

type BatchGetGameHallTeamListReq struct {
	MsgIds               []uint64 `protobuf:"varint,1,rep,packed,name=msg_ids,json=msgIds,proto3" json:"msg_ids,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetGameHallTeamListReq) Reset()         { *m = BatchGetGameHallTeamListReq{} }
func (m *BatchGetGameHallTeamListReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetGameHallTeamListReq) ProtoMessage()    {}
func (*BatchGetGameHallTeamListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{10}
}
func (m *BatchGetGameHallTeamListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGameHallTeamListReq.Unmarshal(m, b)
}
func (m *BatchGetGameHallTeamListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGameHallTeamListReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetGameHallTeamListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGameHallTeamListReq.Merge(dst, src)
}
func (m *BatchGetGameHallTeamListReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetGameHallTeamListReq.Size(m)
}
func (m *BatchGetGameHallTeamListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGameHallTeamListReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGameHallTeamListReq proto.InternalMessageInfo

func (m *BatchGetGameHallTeamListReq) GetMsgIds() []uint64 {
	if m != nil {
		return m.MsgIds
	}
	return nil
}

func (m *BatchGetGameHallTeamListReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type BatchGetGameHallTeamListResp struct {
	// msg_id -> GameHallTeamInfo
	MemInfo              map[uint64]*GameHallTeamInfo `protobuf:"bytes,1,rep,name=mem_info,json=memInfo,proto3" json:"mem_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *BatchGetGameHallTeamListResp) Reset()         { *m = BatchGetGameHallTeamListResp{} }
func (m *BatchGetGameHallTeamListResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetGameHallTeamListResp) ProtoMessage()    {}
func (*BatchGetGameHallTeamListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{11}
}
func (m *BatchGetGameHallTeamListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGameHallTeamListResp.Unmarshal(m, b)
}
func (m *BatchGetGameHallTeamListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGameHallTeamListResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetGameHallTeamListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGameHallTeamListResp.Merge(dst, src)
}
func (m *BatchGetGameHallTeamListResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetGameHallTeamListResp.Size(m)
}
func (m *BatchGetGameHallTeamListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGameHallTeamListResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGameHallTeamListResp proto.InternalMessageInfo

func (m *BatchGetGameHallTeamListResp) GetMemInfo() map[uint64]*GameHallTeamInfo {
	if m != nil {
		return m.MemInfo
	}
	return nil
}

type GameHallTeamInfo struct {
	JoinUid              []uint32 `protobuf:"varint,1,rep,packed,name=join_uid,json=joinUid,proto3" json:"join_uid,omitempty"`
	IsSelfJoin           bool     `protobuf:"varint,2,opt,name=is_self_join,json=isSelfJoin,proto3" json:"is_self_join,omitempty"`
	MemCount             uint32   `protobuf:"varint,3,opt,name=mem_count,json=memCount,proto3" json:"mem_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameHallTeamInfo) Reset()         { *m = GameHallTeamInfo{} }
func (m *GameHallTeamInfo) String() string { return proto.CompactTextString(m) }
func (*GameHallTeamInfo) ProtoMessage()    {}
func (*GameHallTeamInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{12}
}
func (m *GameHallTeamInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameHallTeamInfo.Unmarshal(m, b)
}
func (m *GameHallTeamInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameHallTeamInfo.Marshal(b, m, deterministic)
}
func (dst *GameHallTeamInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameHallTeamInfo.Merge(dst, src)
}
func (m *GameHallTeamInfo) XXX_Size() int {
	return xxx_messageInfo_GameHallTeamInfo.Size(m)
}
func (m *GameHallTeamInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameHallTeamInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameHallTeamInfo proto.InternalMessageInfo

func (m *GameHallTeamInfo) GetJoinUid() []uint32 {
	if m != nil {
		return m.JoinUid
	}
	return nil
}

func (m *GameHallTeamInfo) GetIsSelfJoin() bool {
	if m != nil {
		return m.IsSelfJoin
	}
	return false
}

func (m *GameHallTeamInfo) GetMemCount() uint32 {
	if m != nil {
		return m.MemCount
	}
	return 0
}

type GetUserJoinTeamRecordReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserJoinTeamRecordReq) Reset()         { *m = GetUserJoinTeamRecordReq{} }
func (m *GetUserJoinTeamRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetUserJoinTeamRecordReq) ProtoMessage()    {}
func (*GetUserJoinTeamRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{13}
}
func (m *GetUserJoinTeamRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserJoinTeamRecordReq.Unmarshal(m, b)
}
func (m *GetUserJoinTeamRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserJoinTeamRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetUserJoinTeamRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserJoinTeamRecordReq.Merge(dst, src)
}
func (m *GetUserJoinTeamRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetUserJoinTeamRecordReq.Size(m)
}
func (m *GetUserJoinTeamRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserJoinTeamRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserJoinTeamRecordReq proto.InternalMessageInfo

func (m *GetUserJoinTeamRecordReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type UserJoinTeamMemInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	JoinTime             int64    `protobuf:"varint,2,opt,name=join_time,json=joinTime,proto3" json:"join_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserJoinTeamMemInfo) Reset()         { *m = UserJoinTeamMemInfo{} }
func (m *UserJoinTeamMemInfo) String() string { return proto.CompactTextString(m) }
func (*UserJoinTeamMemInfo) ProtoMessage()    {}
func (*UserJoinTeamMemInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{14}
}
func (m *UserJoinTeamMemInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserJoinTeamMemInfo.Unmarshal(m, b)
}
func (m *UserJoinTeamMemInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserJoinTeamMemInfo.Marshal(b, m, deterministic)
}
func (dst *UserJoinTeamMemInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserJoinTeamMemInfo.Merge(dst, src)
}
func (m *UserJoinTeamMemInfo) XXX_Size() int {
	return xxx_messageInfo_UserJoinTeamMemInfo.Size(m)
}
func (m *UserJoinTeamMemInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserJoinTeamMemInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserJoinTeamMemInfo proto.InternalMessageInfo

func (m *UserJoinTeamMemInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserJoinTeamMemInfo) GetJoinTime() int64 {
	if m != nil {
		return m.JoinTime
	}
	return 0
}

type GetUserJoinTeamRecordResp struct {
	MemInfo              []*UserJoinTeamMemInfo `protobuf:"bytes,1,rep,name=mem_info,json=memInfo,proto3" json:"mem_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetUserJoinTeamRecordResp) Reset()         { *m = GetUserJoinTeamRecordResp{} }
func (m *GetUserJoinTeamRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetUserJoinTeamRecordResp) ProtoMessage()    {}
func (*GetUserJoinTeamRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{15}
}
func (m *GetUserJoinTeamRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserJoinTeamRecordResp.Unmarshal(m, b)
}
func (m *GetUserJoinTeamRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserJoinTeamRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetUserJoinTeamRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserJoinTeamRecordResp.Merge(dst, src)
}
func (m *GetUserJoinTeamRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetUserJoinTeamRecordResp.Size(m)
}
func (m *GetUserJoinTeamRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserJoinTeamRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserJoinTeamRecordResp proto.InternalMessageInfo

func (m *GetUserJoinTeamRecordResp) GetMemInfo() []*UserJoinTeamMemInfo {
	if m != nil {
		return m.MemInfo
	}
	return nil
}

type GetTeamAndInviteMsgReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	MsgId                uint64   `protobuf:"varint,2,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	Limit                int64    `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTeamAndInviteMsgReq) Reset()         { *m = GetTeamAndInviteMsgReq{} }
func (m *GetTeamAndInviteMsgReq) String() string { return proto.CompactTextString(m) }
func (*GetTeamAndInviteMsgReq) ProtoMessage()    {}
func (*GetTeamAndInviteMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{16}
}
func (m *GetTeamAndInviteMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTeamAndInviteMsgReq.Unmarshal(m, b)
}
func (m *GetTeamAndInviteMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTeamAndInviteMsgReq.Marshal(b, m, deterministic)
}
func (dst *GetTeamAndInviteMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTeamAndInviteMsgReq.Merge(dst, src)
}
func (m *GetTeamAndInviteMsgReq) XXX_Size() int {
	return xxx_messageInfo_GetTeamAndInviteMsgReq.Size(m)
}
func (m *GetTeamAndInviteMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTeamAndInviteMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTeamAndInviteMsgReq proto.InternalMessageInfo

func (m *GetTeamAndInviteMsgReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetTeamAndInviteMsgReq) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *GetTeamAndInviteMsgReq) GetLimit() int64 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetTeamAndInviteMsgResp struct {
	MsgIds               []uint64 `protobuf:"varint,1,rep,packed,name=msg_ids,json=msgIds,proto3" json:"msg_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTeamAndInviteMsgResp) Reset()         { *m = GetTeamAndInviteMsgResp{} }
func (m *GetTeamAndInviteMsgResp) String() string { return proto.CompactTextString(m) }
func (*GetTeamAndInviteMsgResp) ProtoMessage()    {}
func (*GetTeamAndInviteMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{17}
}
func (m *GetTeamAndInviteMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTeamAndInviteMsgResp.Unmarshal(m, b)
}
func (m *GetTeamAndInviteMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTeamAndInviteMsgResp.Marshal(b, m, deterministic)
}
func (dst *GetTeamAndInviteMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTeamAndInviteMsgResp.Merge(dst, src)
}
func (m *GetTeamAndInviteMsgResp) XXX_Size() int {
	return xxx_messageInfo_GetTeamAndInviteMsgResp.Size(m)
}
func (m *GetTeamAndInviteMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTeamAndInviteMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTeamAndInviteMsgResp proto.InternalMessageInfo

func (m *GetTeamAndInviteMsgResp) GetMsgIds() []uint64 {
	if m != nil {
		return m.MsgIds
	}
	return nil
}

type BanUserSendMsgReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BanDuration          int64    `protobuf:"varint,2,opt,name=ban_duration,json=banDuration,proto3" json:"ban_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanUserSendMsgReq) Reset()         { *m = BanUserSendMsgReq{} }
func (m *BanUserSendMsgReq) String() string { return proto.CompactTextString(m) }
func (*BanUserSendMsgReq) ProtoMessage()    {}
func (*BanUserSendMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{18}
}
func (m *BanUserSendMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanUserSendMsgReq.Unmarshal(m, b)
}
func (m *BanUserSendMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanUserSendMsgReq.Marshal(b, m, deterministic)
}
func (dst *BanUserSendMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanUserSendMsgReq.Merge(dst, src)
}
func (m *BanUserSendMsgReq) XXX_Size() int {
	return xxx_messageInfo_BanUserSendMsgReq.Size(m)
}
func (m *BanUserSendMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BanUserSendMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_BanUserSendMsgReq proto.InternalMessageInfo

func (m *BanUserSendMsgReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BanUserSendMsgReq) GetBanDuration() int64 {
	if m != nil {
		return m.BanDuration
	}
	return 0
}

type BanUserSendMsgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanUserSendMsgResp) Reset()         { *m = BanUserSendMsgResp{} }
func (m *BanUserSendMsgResp) String() string { return proto.CompactTextString(m) }
func (*BanUserSendMsgResp) ProtoMessage()    {}
func (*BanUserSendMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{19}
}
func (m *BanUserSendMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanUserSendMsgResp.Unmarshal(m, b)
}
func (m *BanUserSendMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanUserSendMsgResp.Marshal(b, m, deterministic)
}
func (dst *BanUserSendMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanUserSendMsgResp.Merge(dst, src)
}
func (m *BanUserSendMsgResp) XXX_Size() int {
	return xxx_messageInfo_BanUserSendMsgResp.Size(m)
}
func (m *BanUserSendMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BanUserSendMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_BanUserSendMsgResp proto.InternalMessageInfo

type GetUserBanStatusMapReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBanStatusMapReq) Reset()         { *m = GetUserBanStatusMapReq{} }
func (m *GetUserBanStatusMapReq) String() string { return proto.CompactTextString(m) }
func (*GetUserBanStatusMapReq) ProtoMessage()    {}
func (*GetUserBanStatusMapReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{20}
}
func (m *GetUserBanStatusMapReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBanStatusMapReq.Unmarshal(m, b)
}
func (m *GetUserBanStatusMapReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBanStatusMapReq.Marshal(b, m, deterministic)
}
func (dst *GetUserBanStatusMapReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBanStatusMapReq.Merge(dst, src)
}
func (m *GetUserBanStatusMapReq) XXX_Size() int {
	return xxx_messageInfo_GetUserBanStatusMapReq.Size(m)
}
func (m *GetUserBanStatusMapReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBanStatusMapReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBanStatusMapReq proto.InternalMessageInfo

func (m *GetUserBanStatusMapReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type GetUserBanStatusMapResp struct {
	BanStatusMap         map[uint32]int64 `protobuf:"bytes,1,rep,name=ban_status_map,json=banStatusMap,proto3" json:"ban_status_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserBanStatusMapResp) Reset()         { *m = GetUserBanStatusMapResp{} }
func (m *GetUserBanStatusMapResp) String() string { return proto.CompactTextString(m) }
func (*GetUserBanStatusMapResp) ProtoMessage()    {}
func (*GetUserBanStatusMapResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{21}
}
func (m *GetUserBanStatusMapResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBanStatusMapResp.Unmarshal(m, b)
}
func (m *GetUserBanStatusMapResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBanStatusMapResp.Marshal(b, m, deterministic)
}
func (dst *GetUserBanStatusMapResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBanStatusMapResp.Merge(dst, src)
}
func (m *GetUserBanStatusMapResp) XXX_Size() int {
	return xxx_messageInfo_GetUserBanStatusMapResp.Size(m)
}
func (m *GetUserBanStatusMapResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBanStatusMapResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBanStatusMapResp proto.InternalMessageInfo

func (m *GetUserBanStatusMapResp) GetBanStatusMap() map[uint32]int64 {
	if m != nil {
		return m.BanStatusMap
	}
	return nil
}

type AddUserAtMsgRecordReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	MsgId                uint64   `protobuf:"varint,3,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	SendTime             int64    `protobuf:"varint,4,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserAtMsgRecordReq) Reset()         { *m = AddUserAtMsgRecordReq{} }
func (m *AddUserAtMsgRecordReq) String() string { return proto.CompactTextString(m) }
func (*AddUserAtMsgRecordReq) ProtoMessage()    {}
func (*AddUserAtMsgRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{22}
}
func (m *AddUserAtMsgRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserAtMsgRecordReq.Unmarshal(m, b)
}
func (m *AddUserAtMsgRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserAtMsgRecordReq.Marshal(b, m, deterministic)
}
func (dst *AddUserAtMsgRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserAtMsgRecordReq.Merge(dst, src)
}
func (m *AddUserAtMsgRecordReq) XXX_Size() int {
	return xxx_messageInfo_AddUserAtMsgRecordReq.Size(m)
}
func (m *AddUserAtMsgRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserAtMsgRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserAtMsgRecordReq proto.InternalMessageInfo

func (m *AddUserAtMsgRecordReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *AddUserAtMsgRecordReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *AddUserAtMsgRecordReq) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *AddUserAtMsgRecordReq) GetSendTime() int64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

type AddUserAtMsgRecordResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserAtMsgRecordResp) Reset()         { *m = AddUserAtMsgRecordResp{} }
func (m *AddUserAtMsgRecordResp) String() string { return proto.CompactTextString(m) }
func (*AddUserAtMsgRecordResp) ProtoMessage()    {}
func (*AddUserAtMsgRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{23}
}
func (m *AddUserAtMsgRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserAtMsgRecordResp.Unmarshal(m, b)
}
func (m *AddUserAtMsgRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserAtMsgRecordResp.Marshal(b, m, deterministic)
}
func (dst *AddUserAtMsgRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserAtMsgRecordResp.Merge(dst, src)
}
func (m *AddUserAtMsgRecordResp) XXX_Size() int {
	return xxx_messageInfo_AddUserAtMsgRecordResp.Size(m)
}
func (m *AddUserAtMsgRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserAtMsgRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserAtMsgRecordResp proto.InternalMessageInfo

type GetUserUnreadAtMsgReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserUnreadAtMsgReq) Reset()         { *m = GetUserUnreadAtMsgReq{} }
func (m *GetUserUnreadAtMsgReq) String() string { return proto.CompactTextString(m) }
func (*GetUserUnreadAtMsgReq) ProtoMessage()    {}
func (*GetUserUnreadAtMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{24}
}
func (m *GetUserUnreadAtMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserUnreadAtMsgReq.Unmarshal(m, b)
}
func (m *GetUserUnreadAtMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserUnreadAtMsgReq.Marshal(b, m, deterministic)
}
func (dst *GetUserUnreadAtMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserUnreadAtMsgReq.Merge(dst, src)
}
func (m *GetUserUnreadAtMsgReq) XXX_Size() int {
	return xxx_messageInfo_GetUserUnreadAtMsgReq.Size(m)
}
func (m *GetUserUnreadAtMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserUnreadAtMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserUnreadAtMsgReq proto.InternalMessageInfo

func (m *GetUserUnreadAtMsgReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserUnreadAtMsgReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetUserUnreadAtMsgResp struct {
	MsgIds               []uint64 `protobuf:"varint,1,rep,packed,name=msg_ids,json=msgIds,proto3" json:"msg_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserUnreadAtMsgResp) Reset()         { *m = GetUserUnreadAtMsgResp{} }
func (m *GetUserUnreadAtMsgResp) String() string { return proto.CompactTextString(m) }
func (*GetUserUnreadAtMsgResp) ProtoMessage()    {}
func (*GetUserUnreadAtMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{25}
}
func (m *GetUserUnreadAtMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserUnreadAtMsgResp.Unmarshal(m, b)
}
func (m *GetUserUnreadAtMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserUnreadAtMsgResp.Marshal(b, m, deterministic)
}
func (dst *GetUserUnreadAtMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserUnreadAtMsgResp.Merge(dst, src)
}
func (m *GetUserUnreadAtMsgResp) XXX_Size() int {
	return xxx_messageInfo_GetUserUnreadAtMsgResp.Size(m)
}
func (m *GetUserUnreadAtMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserUnreadAtMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserUnreadAtMsgResp proto.InternalMessageInfo

func (m *GetUserUnreadAtMsgResp) GetMsgIds() []uint64 {
	if m != nil {
		return m.MsgIds
	}
	return nil
}

type MarkUserAtMsgReadReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	MsgIds               []uint64 `protobuf:"varint,3,rep,packed,name=msg_ids,json=msgIds,proto3" json:"msg_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkUserAtMsgReadReq) Reset()         { *m = MarkUserAtMsgReadReq{} }
func (m *MarkUserAtMsgReadReq) String() string { return proto.CompactTextString(m) }
func (*MarkUserAtMsgReadReq) ProtoMessage()    {}
func (*MarkUserAtMsgReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{26}
}
func (m *MarkUserAtMsgReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkUserAtMsgReadReq.Unmarshal(m, b)
}
func (m *MarkUserAtMsgReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkUserAtMsgReadReq.Marshal(b, m, deterministic)
}
func (dst *MarkUserAtMsgReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkUserAtMsgReadReq.Merge(dst, src)
}
func (m *MarkUserAtMsgReadReq) XXX_Size() int {
	return xxx_messageInfo_MarkUserAtMsgReadReq.Size(m)
}
func (m *MarkUserAtMsgReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkUserAtMsgReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkUserAtMsgReadReq proto.InternalMessageInfo

func (m *MarkUserAtMsgReadReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MarkUserAtMsgReadReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *MarkUserAtMsgReadReq) GetMsgIds() []uint64 {
	if m != nil {
		return m.MsgIds
	}
	return nil
}

type MarkUserAtMsgReadResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkUserAtMsgReadResp) Reset()         { *m = MarkUserAtMsgReadResp{} }
func (m *MarkUserAtMsgReadResp) String() string { return proto.CompactTextString(m) }
func (*MarkUserAtMsgReadResp) ProtoMessage()    {}
func (*MarkUserAtMsgReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{27}
}
func (m *MarkUserAtMsgReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkUserAtMsgReadResp.Unmarshal(m, b)
}
func (m *MarkUserAtMsgReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkUserAtMsgReadResp.Marshal(b, m, deterministic)
}
func (dst *MarkUserAtMsgReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkUserAtMsgReadResp.Merge(dst, src)
}
func (m *MarkUserAtMsgReadResp) XXX_Size() int {
	return xxx_messageInfo_MarkUserAtMsgReadResp.Size(m)
}
func (m *MarkUserAtMsgReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkUserAtMsgReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkUserAtMsgReadResp proto.InternalMessageInfo

type AddCancelUserMsgRecordReq struct {
	Records              []*AddCancelUserMsgRecordReqRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *AddCancelUserMsgRecordReq) Reset()         { *m = AddCancelUserMsgRecordReq{} }
func (m *AddCancelUserMsgRecordReq) String() string { return proto.CompactTextString(m) }
func (*AddCancelUserMsgRecordReq) ProtoMessage()    {}
func (*AddCancelUserMsgRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{28}
}
func (m *AddCancelUserMsgRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCancelUserMsgRecordReq.Unmarshal(m, b)
}
func (m *AddCancelUserMsgRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCancelUserMsgRecordReq.Marshal(b, m, deterministic)
}
func (dst *AddCancelUserMsgRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCancelUserMsgRecordReq.Merge(dst, src)
}
func (m *AddCancelUserMsgRecordReq) XXX_Size() int {
	return xxx_messageInfo_AddCancelUserMsgRecordReq.Size(m)
}
func (m *AddCancelUserMsgRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCancelUserMsgRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddCancelUserMsgRecordReq proto.InternalMessageInfo

func (m *AddCancelUserMsgRecordReq) GetRecords() []*AddCancelUserMsgRecordReqRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
type AddCancelUserMsgRecordReqRecord struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCancelUserMsgRecordReqRecord) Reset()         { *m = AddCancelUserMsgRecordReqRecord{} }
func (m *AddCancelUserMsgRecordReqRecord) String() string { return proto.CompactTextString(m) }
func (*AddCancelUserMsgRecordReqRecord) ProtoMessage()    {}
func (*AddCancelUserMsgRecordReqRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{28, 0}
}
func (m *AddCancelUserMsgRecordReqRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCancelUserMsgRecordReqRecord.Unmarshal(m, b)
}
func (m *AddCancelUserMsgRecordReqRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCancelUserMsgRecordReqRecord.Marshal(b, m, deterministic)
}
func (dst *AddCancelUserMsgRecordReqRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCancelUserMsgRecordReqRecord.Merge(dst, src)
}
func (m *AddCancelUserMsgRecordReqRecord) XXX_Size() int {
	return xxx_messageInfo_AddCancelUserMsgRecordReqRecord.Size(m)
}
func (m *AddCancelUserMsgRecordReqRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCancelUserMsgRecordReqRecord.DiscardUnknown(m)
}

var xxx_messageInfo_AddCancelUserMsgRecordReqRecord proto.InternalMessageInfo

func (m *AddCancelUserMsgRecordReqRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddCancelUserMsgRecordReqRecord) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type AddCancelUserMsgRecordResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCancelUserMsgRecordResp) Reset()         { *m = AddCancelUserMsgRecordResp{} }
func (m *AddCancelUserMsgRecordResp) String() string { return proto.CompactTextString(m) }
func (*AddCancelUserMsgRecordResp) ProtoMessage()    {}
func (*AddCancelUserMsgRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{29}
}
func (m *AddCancelUserMsgRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCancelUserMsgRecordResp.Unmarshal(m, b)
}
func (m *AddCancelUserMsgRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCancelUserMsgRecordResp.Marshal(b, m, deterministic)
}
func (dst *AddCancelUserMsgRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCancelUserMsgRecordResp.Merge(dst, src)
}
func (m *AddCancelUserMsgRecordResp) XXX_Size() int {
	return xxx_messageInfo_AddCancelUserMsgRecordResp.Size(m)
}
func (m *AddCancelUserMsgRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCancelUserMsgRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddCancelUserMsgRecordResp proto.InternalMessageInfo

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetFilterRecordsReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	TabIds               []uint32 `protobuf:"varint,2,rep,packed,name=tabIds,proto3" json:"tabIds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFilterRecordsReq) Reset()         { *m = GetFilterRecordsReq{} }
func (m *GetFilterRecordsReq) String() string { return proto.CompactTextString(m) }
func (*GetFilterRecordsReq) ProtoMessage()    {}
func (*GetFilterRecordsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{30}
}
func (m *GetFilterRecordsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterRecordsReq.Unmarshal(m, b)
}
func (m *GetFilterRecordsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterRecordsReq.Marshal(b, m, deterministic)
}
func (dst *GetFilterRecordsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterRecordsReq.Merge(dst, src)
}
func (m *GetFilterRecordsReq) XXX_Size() int {
	return xxx_messageInfo_GetFilterRecordsReq.Size(m)
}
func (m *GetFilterRecordsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterRecordsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterRecordsReq proto.InternalMessageInfo

func (m *GetFilterRecordsReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetFilterRecordsReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type GetFilterRecordsResp struct {
	UidRecordMap         map[uint32]*GetFilterRecordsRespRecord `protobuf:"bytes,1,rep,name=uid_record_map,json=uidRecordMap,proto3" json:"uid_record_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TabRecordMap         map[uint32]uint64                      `protobuf:"bytes,2,rep,name=tab_record_map,json=tabRecordMap,proto3" json:"tab_record_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *GetFilterRecordsResp) Reset()         { *m = GetFilterRecordsResp{} }
func (m *GetFilterRecordsResp) String() string { return proto.CompactTextString(m) }
func (*GetFilterRecordsResp) ProtoMessage()    {}
func (*GetFilterRecordsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{31}
}
func (m *GetFilterRecordsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterRecordsResp.Unmarshal(m, b)
}
func (m *GetFilterRecordsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterRecordsResp.Marshal(b, m, deterministic)
}
func (dst *GetFilterRecordsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterRecordsResp.Merge(dst, src)
}
func (m *GetFilterRecordsResp) XXX_Size() int {
	return xxx_messageInfo_GetFilterRecordsResp.Size(m)
}
func (m *GetFilterRecordsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterRecordsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterRecordsResp proto.InternalMessageInfo

func (m *GetFilterRecordsResp) GetUidRecordMap() map[uint32]*GetFilterRecordsRespRecord {
	if m != nil {
		return m.UidRecordMap
	}
	return nil
}

func (m *GetFilterRecordsResp) GetTabRecordMap() map[uint32]uint64 {
	if m != nil {
		return m.TabRecordMap
	}
	return nil
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
type GetFilterRecordsRespRecord struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	MsgId                uint64   `protobuf:"varint,2,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFilterRecordsRespRecord) Reset()         { *m = GetFilterRecordsRespRecord{} }
func (m *GetFilterRecordsRespRecord) String() string { return proto.CompactTextString(m) }
func (*GetFilterRecordsRespRecord) ProtoMessage()    {}
func (*GetFilterRecordsRespRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{31, 0}
}
func (m *GetFilterRecordsRespRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterRecordsRespRecord.Unmarshal(m, b)
}
func (m *GetFilterRecordsRespRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterRecordsRespRecord.Marshal(b, m, deterministic)
}
func (dst *GetFilterRecordsRespRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterRecordsRespRecord.Merge(dst, src)
}
func (m *GetFilterRecordsRespRecord) XXX_Size() int {
	return xxx_messageInfo_GetFilterRecordsRespRecord.Size(m)
}
func (m *GetFilterRecordsRespRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterRecordsRespRecord.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterRecordsRespRecord proto.InternalMessageInfo

func (m *GetFilterRecordsRespRecord) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetFilterRecordsRespRecord) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type TempMsgInfo struct {
	MsgId                uint64   `protobuf:"varint,1,opt,name=msgId,proto3" json:"msgId,omitempty"`
	MsgType              uint32   `protobuf:"varint,2,opt,name=msg_type,json=msgType,proto3" json:"msg_type,omitempty"`
	MsgContent           []byte   `protobuf:"bytes,3,opt,name=msg_content,json=msgContent,proto3" json:"msg_content,omitempty"`
	TabId                uint32   `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	SendTime             int64    `protobuf:"varint,5,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	Uid                  uint32   `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TempMsgInfo) Reset()         { *m = TempMsgInfo{} }
func (m *TempMsgInfo) String() string { return proto.CompactTextString(m) }
func (*TempMsgInfo) ProtoMessage()    {}
func (*TempMsgInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{32}
}
func (m *TempMsgInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TempMsgInfo.Unmarshal(m, b)
}
func (m *TempMsgInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TempMsgInfo.Marshal(b, m, deterministic)
}
func (dst *TempMsgInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TempMsgInfo.Merge(dst, src)
}
func (m *TempMsgInfo) XXX_Size() int {
	return xxx_messageInfo_TempMsgInfo.Size(m)
}
func (m *TempMsgInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TempMsgInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TempMsgInfo proto.InternalMessageInfo

func (m *TempMsgInfo) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *TempMsgInfo) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

func (m *TempMsgInfo) GetMsgContent() []byte {
	if m != nil {
		return m.MsgContent
	}
	return nil
}

func (m *TempMsgInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *TempMsgInfo) GetSendTime() int64 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *TempMsgInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type TempKeepImgMsgReq struct {
	MsgInfo              *TempMsgInfo `protobuf:"bytes,1,opt,name=msg_info,json=msgInfo,proto3" json:"msg_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *TempKeepImgMsgReq) Reset()         { *m = TempKeepImgMsgReq{} }
func (m *TempKeepImgMsgReq) String() string { return proto.CompactTextString(m) }
func (*TempKeepImgMsgReq) ProtoMessage()    {}
func (*TempKeepImgMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{33}
}
func (m *TempKeepImgMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TempKeepImgMsgReq.Unmarshal(m, b)
}
func (m *TempKeepImgMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TempKeepImgMsgReq.Marshal(b, m, deterministic)
}
func (dst *TempKeepImgMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TempKeepImgMsgReq.Merge(dst, src)
}
func (m *TempKeepImgMsgReq) XXX_Size() int {
	return xxx_messageInfo_TempKeepImgMsgReq.Size(m)
}
func (m *TempKeepImgMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TempKeepImgMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_TempKeepImgMsgReq proto.InternalMessageInfo

func (m *TempKeepImgMsgReq) GetMsgInfo() *TempMsgInfo {
	if m != nil {
		return m.MsgInfo
	}
	return nil
}

type TempKeepImgMsgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TempKeepImgMsgResp) Reset()         { *m = TempKeepImgMsgResp{} }
func (m *TempKeepImgMsgResp) String() string { return proto.CompactTextString(m) }
func (*TempKeepImgMsgResp) ProtoMessage()    {}
func (*TempKeepImgMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{34}
}
func (m *TempKeepImgMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TempKeepImgMsgResp.Unmarshal(m, b)
}
func (m *TempKeepImgMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TempKeepImgMsgResp.Marshal(b, m, deterministic)
}
func (dst *TempKeepImgMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TempKeepImgMsgResp.Merge(dst, src)
}
func (m *TempKeepImgMsgResp) XXX_Size() int {
	return xxx_messageInfo_TempKeepImgMsgResp.Size(m)
}
func (m *TempKeepImgMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TempKeepImgMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_TempKeepImgMsgResp proto.InternalMessageInfo

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetTempMsgReq struct {
	MsgId                uint64   `protobuf:"varint,1,opt,name=msgId,proto3" json:"msgId,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tabId,proto3" json:"tabId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTempMsgReq) Reset()         { *m = GetTempMsgReq{} }
func (m *GetTempMsgReq) String() string { return proto.CompactTextString(m) }
func (*GetTempMsgReq) ProtoMessage()    {}
func (*GetTempMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{35}
}
func (m *GetTempMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTempMsgReq.Unmarshal(m, b)
}
func (m *GetTempMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTempMsgReq.Marshal(b, m, deterministic)
}
func (dst *GetTempMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTempMsgReq.Merge(dst, src)
}
func (m *GetTempMsgReq) XXX_Size() int {
	return xxx_messageInfo_GetTempMsgReq.Size(m)
}
func (m *GetTempMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTempMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTempMsgReq proto.InternalMessageInfo

func (m *GetTempMsgReq) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *GetTempMsgReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetTempMsgResp struct {
	MsgInfo              *TempMsgInfo `protobuf:"bytes,1,opt,name=msg_info,json=msgInfo,proto3" json:"msg_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetTempMsgResp) Reset()         { *m = GetTempMsgResp{} }
func (m *GetTempMsgResp) String() string { return proto.CompactTextString(m) }
func (*GetTempMsgResp) ProtoMessage()    {}
func (*GetTempMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{36}
}
func (m *GetTempMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTempMsgResp.Unmarshal(m, b)
}
func (m *GetTempMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTempMsgResp.Marshal(b, m, deterministic)
}
func (dst *GetTempMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTempMsgResp.Merge(dst, src)
}
func (m *GetTempMsgResp) XXX_Size() int {
	return xxx_messageInfo_GetTempMsgResp.Size(m)
}
func (m *GetTempMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTempMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTempMsgResp proto.InternalMessageInfo

func (m *GetTempMsgResp) GetMsgInfo() *TempMsgInfo {
	if m != nil {
		return m.MsgInfo
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type DelTempMsgReq struct {
	MsgId                uint64   `protobuf:"varint,1,opt,name=msgId,proto3" json:"msgId,omitempty"`
	NeedDelMsgRecord     bool     `protobuf:"varint,2,opt,name=need_del_msg_record,json=needDelMsgRecord,proto3" json:"need_del_msg_record,omitempty"`
	TabId                uint32   `protobuf:"varint,3,opt,name=tabId,proto3" json:"tabId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTempMsgReq) Reset()         { *m = DelTempMsgReq{} }
func (m *DelTempMsgReq) String() string { return proto.CompactTextString(m) }
func (*DelTempMsgReq) ProtoMessage()    {}
func (*DelTempMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{37}
}
func (m *DelTempMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTempMsgReq.Unmarshal(m, b)
}
func (m *DelTempMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTempMsgReq.Marshal(b, m, deterministic)
}
func (dst *DelTempMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTempMsgReq.Merge(dst, src)
}
func (m *DelTempMsgReq) XXX_Size() int {
	return xxx_messageInfo_DelTempMsgReq.Size(m)
}
func (m *DelTempMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTempMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelTempMsgReq proto.InternalMessageInfo

func (m *DelTempMsgReq) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *DelTempMsgReq) GetNeedDelMsgRecord() bool {
	if m != nil {
		return m.NeedDelMsgRecord
	}
	return false
}

func (m *DelTempMsgReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type DelTempMsgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTempMsgResp) Reset()         { *m = DelTempMsgResp{} }
func (m *DelTempMsgResp) String() string { return proto.CompactTextString(m) }
func (*DelTempMsgResp) ProtoMessage()    {}
func (*DelTempMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{38}
}
func (m *DelTempMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTempMsgResp.Unmarshal(m, b)
}
func (m *DelTempMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTempMsgResp.Marshal(b, m, deterministic)
}
func (dst *DelTempMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTempMsgResp.Merge(dst, src)
}
func (m *DelTempMsgResp) XXX_Size() int {
	return xxx_messageInfo_DelTempMsgResp.Size(m)
}
func (m *DelTempMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTempMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelTempMsgResp proto.InternalMessageInfo

// 更改组队大厅消息通知状态
type UpdateGameHallNotifyStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	OpenNotify           bool     `protobuf:"varint,3,opt,name=open_notify,json=openNotify,proto3" json:"open_notify,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGameHallNotifyStatusReq) Reset()         { *m = UpdateGameHallNotifyStatusReq{} }
func (m *UpdateGameHallNotifyStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGameHallNotifyStatusReq) ProtoMessage()    {}
func (*UpdateGameHallNotifyStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{39}
}
func (m *UpdateGameHallNotifyStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGameHallNotifyStatusReq.Unmarshal(m, b)
}
func (m *UpdateGameHallNotifyStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGameHallNotifyStatusReq.Marshal(b, m, deterministic)
}
func (dst *UpdateGameHallNotifyStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGameHallNotifyStatusReq.Merge(dst, src)
}
func (m *UpdateGameHallNotifyStatusReq) XXX_Size() int {
	return xxx_messageInfo_UpdateGameHallNotifyStatusReq.Size(m)
}
func (m *UpdateGameHallNotifyStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGameHallNotifyStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGameHallNotifyStatusReq proto.InternalMessageInfo

func (m *UpdateGameHallNotifyStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateGameHallNotifyStatusReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *UpdateGameHallNotifyStatusReq) GetOpenNotify() bool {
	if m != nil {
		return m.OpenNotify
	}
	return false
}

type UpdateGameHallNotifyStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGameHallNotifyStatusResp) Reset()         { *m = UpdateGameHallNotifyStatusResp{} }
func (m *UpdateGameHallNotifyStatusResp) String() string { return proto.CompactTextString(m) }
func (*UpdateGameHallNotifyStatusResp) ProtoMessage()    {}
func (*UpdateGameHallNotifyStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{40}
}
func (m *UpdateGameHallNotifyStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGameHallNotifyStatusResp.Unmarshal(m, b)
}
func (m *UpdateGameHallNotifyStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGameHallNotifyStatusResp.Marshal(b, m, deterministic)
}
func (dst *UpdateGameHallNotifyStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGameHallNotifyStatusResp.Merge(dst, src)
}
func (m *UpdateGameHallNotifyStatusResp) XXX_Size() int {
	return xxx_messageInfo_UpdateGameHallNotifyStatusResp.Size(m)
}
func (m *UpdateGameHallNotifyStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGameHallNotifyStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGameHallNotifyStatusResp proto.InternalMessageInfo

// 拉取组队大厅消息通知状态
type GetGameHallNotifyStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameHallNotifyStatusReq) Reset()         { *m = GetGameHallNotifyStatusReq{} }
func (m *GetGameHallNotifyStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetGameHallNotifyStatusReq) ProtoMessage()    {}
func (*GetGameHallNotifyStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{41}
}
func (m *GetGameHallNotifyStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameHallNotifyStatusReq.Unmarshal(m, b)
}
func (m *GetGameHallNotifyStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameHallNotifyStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetGameHallNotifyStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameHallNotifyStatusReq.Merge(dst, src)
}
func (m *GetGameHallNotifyStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetGameHallNotifyStatusReq.Size(m)
}
func (m *GetGameHallNotifyStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameHallNotifyStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameHallNotifyStatusReq proto.InternalMessageInfo

func (m *GetGameHallNotifyStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGameHallNotifyStatusReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetGameHallNotifyStatusResp struct {
	IsOpen               bool     `protobuf:"varint,1,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameHallNotifyStatusResp) Reset()         { *m = GetGameHallNotifyStatusResp{} }
func (m *GetGameHallNotifyStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetGameHallNotifyStatusResp) ProtoMessage()    {}
func (*GetGameHallNotifyStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{42}
}
func (m *GetGameHallNotifyStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameHallNotifyStatusResp.Unmarshal(m, b)
}
func (m *GetGameHallNotifyStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameHallNotifyStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetGameHallNotifyStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameHallNotifyStatusResp.Merge(dst, src)
}
func (m *GetGameHallNotifyStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetGameHallNotifyStatusResp.Size(m)
}
func (m *GetGameHallNotifyStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameHallNotifyStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameHallNotifyStatusResp proto.InternalMessageInfo

func (m *GetGameHallNotifyStatusResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

// 批量获取用户组队大厅消息通知状态
type BatchGetGameHallNotifyStatusReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetGameHallNotifyStatusReq) Reset()         { *m = BatchGetGameHallNotifyStatusReq{} }
func (m *BatchGetGameHallNotifyStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetGameHallNotifyStatusReq) ProtoMessage()    {}
func (*BatchGetGameHallNotifyStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{43}
}
func (m *BatchGetGameHallNotifyStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGameHallNotifyStatusReq.Unmarshal(m, b)
}
func (m *BatchGetGameHallNotifyStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGameHallNotifyStatusReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetGameHallNotifyStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGameHallNotifyStatusReq.Merge(dst, src)
}
func (m *BatchGetGameHallNotifyStatusReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetGameHallNotifyStatusReq.Size(m)
}
func (m *BatchGetGameHallNotifyStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGameHallNotifyStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGameHallNotifyStatusReq proto.InternalMessageInfo

func (m *BatchGetGameHallNotifyStatusReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *BatchGetGameHallNotifyStatusReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type BatchGetGameHallNotifyStatusResp struct {
	NotifyStatusMap      map[uint32]bool `protobuf:"bytes,1,rep,name=notify_status_map,json=notifyStatusMap,proto3" json:"notify_status_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchGetGameHallNotifyStatusResp) Reset()         { *m = BatchGetGameHallNotifyStatusResp{} }
func (m *BatchGetGameHallNotifyStatusResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetGameHallNotifyStatusResp) ProtoMessage()    {}
func (*BatchGetGameHallNotifyStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{44}
}
func (m *BatchGetGameHallNotifyStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGameHallNotifyStatusResp.Unmarshal(m, b)
}
func (m *BatchGetGameHallNotifyStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGameHallNotifyStatusResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetGameHallNotifyStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGameHallNotifyStatusResp.Merge(dst, src)
}
func (m *BatchGetGameHallNotifyStatusResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetGameHallNotifyStatusResp.Size(m)
}
func (m *BatchGetGameHallNotifyStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGameHallNotifyStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGameHallNotifyStatusResp proto.InternalMessageInfo

func (m *BatchGetGameHallNotifyStatusResp) GetNotifyStatusMap() map[uint32]bool {
	if m != nil {
		return m.NotifyStatusMap
	}
	return nil
}

type DelExpireMsgRecordByTabIdsReq struct {
	TabIds               []uint32 `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelExpireMsgRecordByTabIdsReq) Reset()         { *m = DelExpireMsgRecordByTabIdsReq{} }
func (m *DelExpireMsgRecordByTabIdsReq) String() string { return proto.CompactTextString(m) }
func (*DelExpireMsgRecordByTabIdsReq) ProtoMessage()    {}
func (*DelExpireMsgRecordByTabIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{45}
}
func (m *DelExpireMsgRecordByTabIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelExpireMsgRecordByTabIdsReq.Unmarshal(m, b)
}
func (m *DelExpireMsgRecordByTabIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelExpireMsgRecordByTabIdsReq.Marshal(b, m, deterministic)
}
func (dst *DelExpireMsgRecordByTabIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelExpireMsgRecordByTabIdsReq.Merge(dst, src)
}
func (m *DelExpireMsgRecordByTabIdsReq) XXX_Size() int {
	return xxx_messageInfo_DelExpireMsgRecordByTabIdsReq.Size(m)
}
func (m *DelExpireMsgRecordByTabIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelExpireMsgRecordByTabIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelExpireMsgRecordByTabIdsReq proto.InternalMessageInfo

func (m *DelExpireMsgRecordByTabIdsReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type DelExpireMsgRecordByTabIdsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelExpireMsgRecordByTabIdsResp) Reset()         { *m = DelExpireMsgRecordByTabIdsResp{} }
func (m *DelExpireMsgRecordByTabIdsResp) String() string { return proto.CompactTextString(m) }
func (*DelExpireMsgRecordByTabIdsResp) ProtoMessage()    {}
func (*DelExpireMsgRecordByTabIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{46}
}
func (m *DelExpireMsgRecordByTabIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelExpireMsgRecordByTabIdsResp.Unmarshal(m, b)
}
func (m *DelExpireMsgRecordByTabIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelExpireMsgRecordByTabIdsResp.Marshal(b, m, deterministic)
}
func (dst *DelExpireMsgRecordByTabIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelExpireMsgRecordByTabIdsResp.Merge(dst, src)
}
func (m *DelExpireMsgRecordByTabIdsResp) XXX_Size() int {
	return xxx_messageInfo_DelExpireMsgRecordByTabIdsResp.Size(m)
}
func (m *DelExpireMsgRecordByTabIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelExpireMsgRecordByTabIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelExpireMsgRecordByTabIdsResp proto.InternalMessageInfo

type SetShowEntranceSettingReq struct {
	Uid                  uint32               `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	EntranceSetting      *EntranceSettingItem `protobuf:"bytes,2,opt,name=entrance_setting,json=entranceSetting,proto3" json:"entrance_setting,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SetShowEntranceSettingReq) Reset()         { *m = SetShowEntranceSettingReq{} }
func (m *SetShowEntranceSettingReq) String() string { return proto.CompactTextString(m) }
func (*SetShowEntranceSettingReq) ProtoMessage()    {}
func (*SetShowEntranceSettingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{47}
}
func (m *SetShowEntranceSettingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetShowEntranceSettingReq.Unmarshal(m, b)
}
func (m *SetShowEntranceSettingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetShowEntranceSettingReq.Marshal(b, m, deterministic)
}
func (dst *SetShowEntranceSettingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetShowEntranceSettingReq.Merge(dst, src)
}
func (m *SetShowEntranceSettingReq) XXX_Size() int {
	return xxx_messageInfo_SetShowEntranceSettingReq.Size(m)
}
func (m *SetShowEntranceSettingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetShowEntranceSettingReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetShowEntranceSettingReq proto.InternalMessageInfo

func (m *SetShowEntranceSettingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetShowEntranceSettingReq) GetEntranceSetting() *EntranceSettingItem {
	if m != nil {
		return m.EntranceSetting
	}
	return nil
}

type SetShowEntranceSettingResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetShowEntranceSettingResp) Reset()         { *m = SetShowEntranceSettingResp{} }
func (m *SetShowEntranceSettingResp) String() string { return proto.CompactTextString(m) }
func (*SetShowEntranceSettingResp) ProtoMessage()    {}
func (*SetShowEntranceSettingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{48}
}
func (m *SetShowEntranceSettingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetShowEntranceSettingResp.Unmarshal(m, b)
}
func (m *SetShowEntranceSettingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetShowEntranceSettingResp.Marshal(b, m, deterministic)
}
func (dst *SetShowEntranceSettingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetShowEntranceSettingResp.Merge(dst, src)
}
func (m *SetShowEntranceSettingResp) XXX_Size() int {
	return xxx_messageInfo_SetShowEntranceSettingResp.Size(m)
}
func (m *SetShowEntranceSettingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetShowEntranceSettingResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetShowEntranceSettingResp proto.InternalMessageInfo

type EntranceSettingItem struct {
	TabId                uint32                             `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	IsOpen               bool                               `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	EntranceSource       EntranceSettingItem_EntranceSource `protobuf:"varint,3,opt,name=entrance_source,json=entranceSource,proto3,enum=game_hall.EntranceSettingItem_EntranceSource" json:"entrance_source,omitempty"`
	Uid                  uint32                             `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *EntranceSettingItem) Reset()         { *m = EntranceSettingItem{} }
func (m *EntranceSettingItem) String() string { return proto.CompactTextString(m) }
func (*EntranceSettingItem) ProtoMessage()    {}
func (*EntranceSettingItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{49}
}
func (m *EntranceSettingItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EntranceSettingItem.Unmarshal(m, b)
}
func (m *EntranceSettingItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EntranceSettingItem.Marshal(b, m, deterministic)
}
func (dst *EntranceSettingItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EntranceSettingItem.Merge(dst, src)
}
func (m *EntranceSettingItem) XXX_Size() int {
	return xxx_messageInfo_EntranceSettingItem.Size(m)
}
func (m *EntranceSettingItem) XXX_DiscardUnknown() {
	xxx_messageInfo_EntranceSettingItem.DiscardUnknown(m)
}

var xxx_messageInfo_EntranceSettingItem proto.InternalMessageInfo

func (m *EntranceSettingItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *EntranceSettingItem) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *EntranceSettingItem) GetEntranceSource() EntranceSettingItem_EntranceSource {
	if m != nil {
		return m.EntranceSource
	}
	return EntranceSettingItem_ENTRANCE_SOURCE_UNSPECIFIED
}

func (m *EntranceSettingItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetShowEntranceSettingReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetShowEntranceSettingReq) Reset()         { *m = GetShowEntranceSettingReq{} }
func (m *GetShowEntranceSettingReq) String() string { return proto.CompactTextString(m) }
func (*GetShowEntranceSettingReq) ProtoMessage()    {}
func (*GetShowEntranceSettingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{50}
}
func (m *GetShowEntranceSettingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowEntranceSettingReq.Unmarshal(m, b)
}
func (m *GetShowEntranceSettingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowEntranceSettingReq.Marshal(b, m, deterministic)
}
func (dst *GetShowEntranceSettingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowEntranceSettingReq.Merge(dst, src)
}
func (m *GetShowEntranceSettingReq) XXX_Size() int {
	return xxx_messageInfo_GetShowEntranceSettingReq.Size(m)
}
func (m *GetShowEntranceSettingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowEntranceSettingReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowEntranceSettingReq proto.InternalMessageInfo

func (m *GetShowEntranceSettingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetShowEntranceSettingReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetShowEntranceSettingResp struct {
	EntranceSettingList  []*EntranceSettingItem `protobuf:"bytes,1,rep,name=entrance_setting_list,json=entranceSettingList,proto3" json:"entrance_setting_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetShowEntranceSettingResp) Reset()         { *m = GetShowEntranceSettingResp{} }
func (m *GetShowEntranceSettingResp) String() string { return proto.CompactTextString(m) }
func (*GetShowEntranceSettingResp) ProtoMessage()    {}
func (*GetShowEntranceSettingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{51}
}
func (m *GetShowEntranceSettingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowEntranceSettingResp.Unmarshal(m, b)
}
func (m *GetShowEntranceSettingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowEntranceSettingResp.Marshal(b, m, deterministic)
}
func (dst *GetShowEntranceSettingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowEntranceSettingResp.Merge(dst, src)
}
func (m *GetShowEntranceSettingResp) XXX_Size() int {
	return xxx_messageInfo_GetShowEntranceSettingResp.Size(m)
}
func (m *GetShowEntranceSettingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowEntranceSettingResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowEntranceSettingResp proto.InternalMessageInfo

func (m *GetShowEntranceSettingResp) GetEntranceSettingList() []*EntranceSettingItem {
	if m != nil {
		return m.EntranceSettingList
	}
	return nil
}

type BatGetShowEntranceSettingByTabIdsReq struct {
	TabIds               []uint32 `protobuf:"varint,2,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetShowEntranceSettingByTabIdsReq) Reset()         { *m = BatGetShowEntranceSettingByTabIdsReq{} }
func (m *BatGetShowEntranceSettingByTabIdsReq) String() string { return proto.CompactTextString(m) }
func (*BatGetShowEntranceSettingByTabIdsReq) ProtoMessage()    {}
func (*BatGetShowEntranceSettingByTabIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{52}
}
func (m *BatGetShowEntranceSettingByTabIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetShowEntranceSettingByTabIdsReq.Unmarshal(m, b)
}
func (m *BatGetShowEntranceSettingByTabIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetShowEntranceSettingByTabIdsReq.Marshal(b, m, deterministic)
}
func (dst *BatGetShowEntranceSettingByTabIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetShowEntranceSettingByTabIdsReq.Merge(dst, src)
}
func (m *BatGetShowEntranceSettingByTabIdsReq) XXX_Size() int {
	return xxx_messageInfo_BatGetShowEntranceSettingByTabIdsReq.Size(m)
}
func (m *BatGetShowEntranceSettingByTabIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetShowEntranceSettingByTabIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetShowEntranceSettingByTabIdsReq proto.InternalMessageInfo

func (m *BatGetShowEntranceSettingByTabIdsReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type BatGetShowEntranceSettingByTabIdsResp struct {
	EntranceSettingMap   map[uint32]*BatGetShowEntranceSettingByTabIdsResp_Item `protobuf:"bytes,1,rep,name=entrance_setting_map,json=entranceSettingMap,proto3" json:"entrance_setting_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                               `json:"-"`
	XXX_unrecognized     []byte                                                 `json:"-"`
	XXX_sizecache        int32                                                  `json:"-"`
}

func (m *BatGetShowEntranceSettingByTabIdsResp) Reset()         { *m = BatGetShowEntranceSettingByTabIdsResp{} }
func (m *BatGetShowEntranceSettingByTabIdsResp) String() string { return proto.CompactTextString(m) }
func (*BatGetShowEntranceSettingByTabIdsResp) ProtoMessage()    {}
func (*BatGetShowEntranceSettingByTabIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{53}
}
func (m *BatGetShowEntranceSettingByTabIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetShowEntranceSettingByTabIdsResp.Unmarshal(m, b)
}
func (m *BatGetShowEntranceSettingByTabIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetShowEntranceSettingByTabIdsResp.Marshal(b, m, deterministic)
}
func (dst *BatGetShowEntranceSettingByTabIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetShowEntranceSettingByTabIdsResp.Merge(dst, src)
}
func (m *BatGetShowEntranceSettingByTabIdsResp) XXX_Size() int {
	return xxx_messageInfo_BatGetShowEntranceSettingByTabIdsResp.Size(m)
}
func (m *BatGetShowEntranceSettingByTabIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetShowEntranceSettingByTabIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetShowEntranceSettingByTabIdsResp proto.InternalMessageInfo

func (m *BatGetShowEntranceSettingByTabIdsResp) GetEntranceSettingMap() map[uint32]*BatGetShowEntranceSettingByTabIdsResp_Item {
	if m != nil {
		return m.EntranceSettingMap
	}
	return nil
}

type BatGetShowEntranceSettingByTabIdsResp_Item struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetShowEntranceSettingByTabIdsResp_Item) Reset() {
	*m = BatGetShowEntranceSettingByTabIdsResp_Item{}
}
func (m *BatGetShowEntranceSettingByTabIdsResp_Item) String() string {
	return proto.CompactTextString(m)
}
func (*BatGetShowEntranceSettingByTabIdsResp_Item) ProtoMessage() {}
func (*BatGetShowEntranceSettingByTabIdsResp_Item) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{53, 0}
}
func (m *BatGetShowEntranceSettingByTabIdsResp_Item) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetShowEntranceSettingByTabIdsResp_Item.Unmarshal(m, b)
}
func (m *BatGetShowEntranceSettingByTabIdsResp_Item) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetShowEntranceSettingByTabIdsResp_Item.Marshal(b, m, deterministic)
}
func (dst *BatGetShowEntranceSettingByTabIdsResp_Item) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetShowEntranceSettingByTabIdsResp_Item.Merge(dst, src)
}
func (m *BatGetShowEntranceSettingByTabIdsResp_Item) XXX_Size() int {
	return xxx_messageInfo_BatGetShowEntranceSettingByTabIdsResp_Item.Size(m)
}
func (m *BatGetShowEntranceSettingByTabIdsResp_Item) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetShowEntranceSettingByTabIdsResp_Item.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetShowEntranceSettingByTabIdsResp_Item proto.InternalMessageInfo

func (m *BatGetShowEntranceSettingByTabIdsResp_Item) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type AddGameHallPinConfReq struct {
	PinConfItem          *GameHallPinConfItem `protobuf:"bytes,1,opt,name=pin_conf_item,json=pinConfItem,proto3" json:"pin_conf_item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AddGameHallPinConfReq) Reset()         { *m = AddGameHallPinConfReq{} }
func (m *AddGameHallPinConfReq) String() string { return proto.CompactTextString(m) }
func (*AddGameHallPinConfReq) ProtoMessage()    {}
func (*AddGameHallPinConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{54}
}
func (m *AddGameHallPinConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddGameHallPinConfReq.Unmarshal(m, b)
}
func (m *AddGameHallPinConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddGameHallPinConfReq.Marshal(b, m, deterministic)
}
func (dst *AddGameHallPinConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddGameHallPinConfReq.Merge(dst, src)
}
func (m *AddGameHallPinConfReq) XXX_Size() int {
	return xxx_messageInfo_AddGameHallPinConfReq.Size(m)
}
func (m *AddGameHallPinConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddGameHallPinConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddGameHallPinConfReq proto.InternalMessageInfo

func (m *AddGameHallPinConfReq) GetPinConfItem() *GameHallPinConfItem {
	if m != nil {
		return m.PinConfItem
	}
	return nil
}

type AddGameHallPinConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddGameHallPinConfResp) Reset()         { *m = AddGameHallPinConfResp{} }
func (m *AddGameHallPinConfResp) String() string { return proto.CompactTextString(m) }
func (*AddGameHallPinConfResp) ProtoMessage()    {}
func (*AddGameHallPinConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{55}
}
func (m *AddGameHallPinConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddGameHallPinConfResp.Unmarshal(m, b)
}
func (m *AddGameHallPinConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddGameHallPinConfResp.Marshal(b, m, deterministic)
}
func (dst *AddGameHallPinConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddGameHallPinConfResp.Merge(dst, src)
}
func (m *AddGameHallPinConfResp) XXX_Size() int {
	return xxx_messageInfo_AddGameHallPinConfResp.Size(m)
}
func (m *AddGameHallPinConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddGameHallPinConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddGameHallPinConfResp proto.InternalMessageInfo

type UpdateGameHallPinConfReq struct {
	PinConfItem          *GameHallPinConfItem `protobuf:"bytes,1,opt,name=pin_conf_item,json=pinConfItem,proto3" json:"pin_conf_item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpdateGameHallPinConfReq) Reset()         { *m = UpdateGameHallPinConfReq{} }
func (m *UpdateGameHallPinConfReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGameHallPinConfReq) ProtoMessage()    {}
func (*UpdateGameHallPinConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{56}
}
func (m *UpdateGameHallPinConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGameHallPinConfReq.Unmarshal(m, b)
}
func (m *UpdateGameHallPinConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGameHallPinConfReq.Marshal(b, m, deterministic)
}
func (dst *UpdateGameHallPinConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGameHallPinConfReq.Merge(dst, src)
}
func (m *UpdateGameHallPinConfReq) XXX_Size() int {
	return xxx_messageInfo_UpdateGameHallPinConfReq.Size(m)
}
func (m *UpdateGameHallPinConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGameHallPinConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGameHallPinConfReq proto.InternalMessageInfo

func (m *UpdateGameHallPinConfReq) GetPinConfItem() *GameHallPinConfItem {
	if m != nil {
		return m.PinConfItem
	}
	return nil
}

type UpdateGameHallPinConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGameHallPinConfResp) Reset()         { *m = UpdateGameHallPinConfResp{} }
func (m *UpdateGameHallPinConfResp) String() string { return proto.CompactTextString(m) }
func (*UpdateGameHallPinConfResp) ProtoMessage()    {}
func (*UpdateGameHallPinConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{57}
}
func (m *UpdateGameHallPinConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGameHallPinConfResp.Unmarshal(m, b)
}
func (m *UpdateGameHallPinConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGameHallPinConfResp.Marshal(b, m, deterministic)
}
func (dst *UpdateGameHallPinConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGameHallPinConfResp.Merge(dst, src)
}
func (m *UpdateGameHallPinConfResp) XXX_Size() int {
	return xxx_messageInfo_UpdateGameHallPinConfResp.Size(m)
}
func (m *UpdateGameHallPinConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGameHallPinConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGameHallPinConfResp proto.InternalMessageInfo

type GameHallPinConfItem struct {
	Id                   string                               `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TabIds               []uint32                             `protobuf:"varint,2,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	Ttid                 string                               `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Uid                  uint32                               `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Content              string                               `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	Text                 []*GameHallPinConfItem_HighlightText `protobuf:"bytes,6,rep,name=text,proto3" json:"text,omitempty"`
	Img                  string                               `protobuf:"bytes,7,opt,name=img,proto3" json:"img,omitempty"`
	Status               GameHallPinConfStatus                `protobuf:"varint,8,opt,name=status,proto3,enum=game_hall.GameHallPinConfStatus" json:"status,omitempty"`
	StartTime            int64                                `protobuf:"varint,9,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64                                `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	UpdateTime           int64                                `protobuf:"varint,11,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	IsSelectAllTab       bool                                 `protobuf:"varint,12,opt,name=is_select_all_tab,json=isSelectAllTab,proto3" json:"is_select_all_tab,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *GameHallPinConfItem) Reset()         { *m = GameHallPinConfItem{} }
func (m *GameHallPinConfItem) String() string { return proto.CompactTextString(m) }
func (*GameHallPinConfItem) ProtoMessage()    {}
func (*GameHallPinConfItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{58}
}
func (m *GameHallPinConfItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameHallPinConfItem.Unmarshal(m, b)
}
func (m *GameHallPinConfItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameHallPinConfItem.Marshal(b, m, deterministic)
}
func (dst *GameHallPinConfItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameHallPinConfItem.Merge(dst, src)
}
func (m *GameHallPinConfItem) XXX_Size() int {
	return xxx_messageInfo_GameHallPinConfItem.Size(m)
}
func (m *GameHallPinConfItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameHallPinConfItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameHallPinConfItem proto.InternalMessageInfo

func (m *GameHallPinConfItem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GameHallPinConfItem) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *GameHallPinConfItem) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *GameHallPinConfItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameHallPinConfItem) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GameHallPinConfItem) GetText() []*GameHallPinConfItem_HighlightText {
	if m != nil {
		return m.Text
	}
	return nil
}

func (m *GameHallPinConfItem) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *GameHallPinConfItem) GetStatus() GameHallPinConfStatus {
	if m != nil {
		return m.Status
	}
	return GameHallPinConfStatus_STATUS_UNSPECIFIED
}

func (m *GameHallPinConfItem) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GameHallPinConfItem) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GameHallPinConfItem) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *GameHallPinConfItem) GetIsSelectAllTab() bool {
	if m != nil {
		return m.IsSelectAllTab
	}
	return false
}

type GameHallPinConfItem_HighlightText struct {
	Highlight            string   `protobuf:"bytes,2,opt,name=highlight,proto3" json:"highlight,omitempty"`
	Url                  string   `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameHallPinConfItem_HighlightText) Reset()         { *m = GameHallPinConfItem_HighlightText{} }
func (m *GameHallPinConfItem_HighlightText) String() string { return proto.CompactTextString(m) }
func (*GameHallPinConfItem_HighlightText) ProtoMessage()    {}
func (*GameHallPinConfItem_HighlightText) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{58, 0}
}
func (m *GameHallPinConfItem_HighlightText) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameHallPinConfItem_HighlightText.Unmarshal(m, b)
}
func (m *GameHallPinConfItem_HighlightText) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameHallPinConfItem_HighlightText.Marshal(b, m, deterministic)
}
func (dst *GameHallPinConfItem_HighlightText) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameHallPinConfItem_HighlightText.Merge(dst, src)
}
func (m *GameHallPinConfItem_HighlightText) XXX_Size() int {
	return xxx_messageInfo_GameHallPinConfItem_HighlightText.Size(m)
}
func (m *GameHallPinConfItem_HighlightText) XXX_DiscardUnknown() {
	xxx_messageInfo_GameHallPinConfItem_HighlightText.DiscardUnknown(m)
}

var xxx_messageInfo_GameHallPinConfItem_HighlightText proto.InternalMessageInfo

func (m *GameHallPinConfItem_HighlightText) GetHighlight() string {
	if m != nil {
		return m.Highlight
	}
	return ""
}

func (m *GameHallPinConfItem_HighlightText) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type GetPinConfByAdminReq struct {
	TabId                uint32                `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Status               GameHallPinConfStatus `protobuf:"varint,2,opt,name=status,proto3,enum=game_hall.GameHallPinConfStatus" json:"status,omitempty"`
	StartTime            int64                 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64                 `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Page                 int64                 `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             int64                 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPinConfByAdminReq) Reset()         { *m = GetPinConfByAdminReq{} }
func (m *GetPinConfByAdminReq) String() string { return proto.CompactTextString(m) }
func (*GetPinConfByAdminReq) ProtoMessage()    {}
func (*GetPinConfByAdminReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{59}
}
func (m *GetPinConfByAdminReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPinConfByAdminReq.Unmarshal(m, b)
}
func (m *GetPinConfByAdminReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPinConfByAdminReq.Marshal(b, m, deterministic)
}
func (dst *GetPinConfByAdminReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPinConfByAdminReq.Merge(dst, src)
}
func (m *GetPinConfByAdminReq) XXX_Size() int {
	return xxx_messageInfo_GetPinConfByAdminReq.Size(m)
}
func (m *GetPinConfByAdminReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPinConfByAdminReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPinConfByAdminReq proto.InternalMessageInfo

func (m *GetPinConfByAdminReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetPinConfByAdminReq) GetStatus() GameHallPinConfStatus {
	if m != nil {
		return m.Status
	}
	return GameHallPinConfStatus_STATUS_UNSPECIFIED
}

func (m *GetPinConfByAdminReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetPinConfByAdminReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetPinConfByAdminReq) GetPage() int64 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetPinConfByAdminReq) GetPageSize() int64 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetPinConfByAdminResp struct {
	TotalCount           int64                  `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	PinConfList          []*GameHallPinConfItem `protobuf:"bytes,2,rep,name=pin_conf_list,json=pinConfList,proto3" json:"pin_conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetPinConfByAdminResp) Reset()         { *m = GetPinConfByAdminResp{} }
func (m *GetPinConfByAdminResp) String() string { return proto.CompactTextString(m) }
func (*GetPinConfByAdminResp) ProtoMessage()    {}
func (*GetPinConfByAdminResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{60}
}
func (m *GetPinConfByAdminResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPinConfByAdminResp.Unmarshal(m, b)
}
func (m *GetPinConfByAdminResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPinConfByAdminResp.Marshal(b, m, deterministic)
}
func (dst *GetPinConfByAdminResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPinConfByAdminResp.Merge(dst, src)
}
func (m *GetPinConfByAdminResp) XXX_Size() int {
	return xxx_messageInfo_GetPinConfByAdminResp.Size(m)
}
func (m *GetPinConfByAdminResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPinConfByAdminResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPinConfByAdminResp proto.InternalMessageInfo

func (m *GetPinConfByAdminResp) GetTotalCount() int64 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetPinConfByAdminResp) GetPinConfList() []*GameHallPinConfItem {
	if m != nil {
		return m.PinConfList
	}
	return nil
}

type DelGameHallPinConfReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGameHallPinConfReq) Reset()         { *m = DelGameHallPinConfReq{} }
func (m *DelGameHallPinConfReq) String() string { return proto.CompactTextString(m) }
func (*DelGameHallPinConfReq) ProtoMessage()    {}
func (*DelGameHallPinConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{61}
}
func (m *DelGameHallPinConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGameHallPinConfReq.Unmarshal(m, b)
}
func (m *DelGameHallPinConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGameHallPinConfReq.Marshal(b, m, deterministic)
}
func (dst *DelGameHallPinConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGameHallPinConfReq.Merge(dst, src)
}
func (m *DelGameHallPinConfReq) XXX_Size() int {
	return xxx_messageInfo_DelGameHallPinConfReq.Size(m)
}
func (m *DelGameHallPinConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGameHallPinConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelGameHallPinConfReq proto.InternalMessageInfo

func (m *DelGameHallPinConfReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelGameHallPinConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGameHallPinConfResp) Reset()         { *m = DelGameHallPinConfResp{} }
func (m *DelGameHallPinConfResp) String() string { return proto.CompactTextString(m) }
func (*DelGameHallPinConfResp) ProtoMessage()    {}
func (*DelGameHallPinConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{62}
}
func (m *DelGameHallPinConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGameHallPinConfResp.Unmarshal(m, b)
}
func (m *DelGameHallPinConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGameHallPinConfResp.Marshal(b, m, deterministic)
}
func (dst *DelGameHallPinConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGameHallPinConfResp.Merge(dst, src)
}
func (m *DelGameHallPinConfResp) XXX_Size() int {
	return xxx_messageInfo_DelGameHallPinConfResp.Size(m)
}
func (m *DelGameHallPinConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGameHallPinConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelGameHallPinConfResp proto.InternalMessageInfo

type GetActivePinConfByTabIdReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActivePinConfByTabIdReq) Reset()         { *m = GetActivePinConfByTabIdReq{} }
func (m *GetActivePinConfByTabIdReq) String() string { return proto.CompactTextString(m) }
func (*GetActivePinConfByTabIdReq) ProtoMessage()    {}
func (*GetActivePinConfByTabIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{63}
}
func (m *GetActivePinConfByTabIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivePinConfByTabIdReq.Unmarshal(m, b)
}
func (m *GetActivePinConfByTabIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivePinConfByTabIdReq.Marshal(b, m, deterministic)
}
func (dst *GetActivePinConfByTabIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivePinConfByTabIdReq.Merge(dst, src)
}
func (m *GetActivePinConfByTabIdReq) XXX_Size() int {
	return xxx_messageInfo_GetActivePinConfByTabIdReq.Size(m)
}
func (m *GetActivePinConfByTabIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivePinConfByTabIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivePinConfByTabIdReq proto.InternalMessageInfo

func (m *GetActivePinConfByTabIdReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetActivePinConfByTabIdResp struct {
	PinConfList          []*GameHallPinConfItem `protobuf:"bytes,1,rep,name=pin_conf_list,json=pinConfList,proto3" json:"pin_conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetActivePinConfByTabIdResp) Reset()         { *m = GetActivePinConfByTabIdResp{} }
func (m *GetActivePinConfByTabIdResp) String() string { return proto.CompactTextString(m) }
func (*GetActivePinConfByTabIdResp) ProtoMessage()    {}
func (*GetActivePinConfByTabIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_hall_6be111de87bb0979, []int{64}
}
func (m *GetActivePinConfByTabIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivePinConfByTabIdResp.Unmarshal(m, b)
}
func (m *GetActivePinConfByTabIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivePinConfByTabIdResp.Marshal(b, m, deterministic)
}
func (dst *GetActivePinConfByTabIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivePinConfByTabIdResp.Merge(dst, src)
}
func (m *GetActivePinConfByTabIdResp) XXX_Size() int {
	return xxx_messageInfo_GetActivePinConfByTabIdResp.Size(m)
}
func (m *GetActivePinConfByTabIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivePinConfByTabIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivePinConfByTabIdResp proto.InternalMessageInfo

func (m *GetActivePinConfByTabIdResp) GetPinConfList() []*GameHallPinConfItem {
	if m != nil {
		return m.PinConfList
	}
	return nil
}

func init() {
	proto.RegisterType((*AddCleanMsgRecordReq)(nil), "game_hall.AddCleanMsgRecordReq")
	proto.RegisterType((*AddCleanMsgRecordResp)(nil), "game_hall.AddCleanMsgRecordResp")
	proto.RegisterType((*AddMsgRecordReq)(nil), "game_hall.AddMsgRecordReq")
	proto.RegisterType((*AddMsgRecordResp)(nil), "game_hall.AddMsgRecordResp")
	proto.RegisterType((*GetLastMsgIdBySendTimeReq)(nil), "game_hall.GetLastMsgIdBySendTimeReq")
	proto.RegisterType((*GetLastMsgIdBySendTimeResp)(nil), "game_hall.GetLastMsgIdBySendTimeResp")
	proto.RegisterType((*AddTeamAndInviteMsgReq)(nil), "game_hall.AddTeamAndInviteMsgReq")
	proto.RegisterType((*AddTeamAndInviteMsgResp)(nil), "game_hall.AddTeamAndInviteMsgResp")
	proto.RegisterType((*JoinGameHallTeamReq)(nil), "game_hall.JoinGameHallTeamReq")
	proto.RegisterType((*JoinGameHallTeamResp)(nil), "game_hall.JoinGameHallTeamResp")
	proto.RegisterType((*BatchGetGameHallTeamListReq)(nil), "game_hall.BatchGetGameHallTeamListReq")
	proto.RegisterType((*BatchGetGameHallTeamListResp)(nil), "game_hall.BatchGetGameHallTeamListResp")
	proto.RegisterMapType((map[uint64]*GameHallTeamInfo)(nil), "game_hall.BatchGetGameHallTeamListResp.MemInfoEntry")
	proto.RegisterType((*GameHallTeamInfo)(nil), "game_hall.GameHallTeamInfo")
	proto.RegisterType((*GetUserJoinTeamRecordReq)(nil), "game_hall.GetUserJoinTeamRecordReq")
	proto.RegisterType((*UserJoinTeamMemInfo)(nil), "game_hall.UserJoinTeamMemInfo")
	proto.RegisterType((*GetUserJoinTeamRecordResp)(nil), "game_hall.GetUserJoinTeamRecordResp")
	proto.RegisterType((*GetTeamAndInviteMsgReq)(nil), "game_hall.GetTeamAndInviteMsgReq")
	proto.RegisterType((*GetTeamAndInviteMsgResp)(nil), "game_hall.GetTeamAndInviteMsgResp")
	proto.RegisterType((*BanUserSendMsgReq)(nil), "game_hall.BanUserSendMsgReq")
	proto.RegisterType((*BanUserSendMsgResp)(nil), "game_hall.BanUserSendMsgResp")
	proto.RegisterType((*GetUserBanStatusMapReq)(nil), "game_hall.GetUserBanStatusMapReq")
	proto.RegisterType((*GetUserBanStatusMapResp)(nil), "game_hall.GetUserBanStatusMapResp")
	proto.RegisterMapType((map[uint32]int64)(nil), "game_hall.GetUserBanStatusMapResp.BanStatusMapEntry")
	proto.RegisterType((*AddUserAtMsgRecordReq)(nil), "game_hall.AddUserAtMsgRecordReq")
	proto.RegisterType((*AddUserAtMsgRecordResp)(nil), "game_hall.AddUserAtMsgRecordResp")
	proto.RegisterType((*GetUserUnreadAtMsgReq)(nil), "game_hall.GetUserUnreadAtMsgReq")
	proto.RegisterType((*GetUserUnreadAtMsgResp)(nil), "game_hall.GetUserUnreadAtMsgResp")
	proto.RegisterType((*MarkUserAtMsgReadReq)(nil), "game_hall.MarkUserAtMsgReadReq")
	proto.RegisterType((*MarkUserAtMsgReadResp)(nil), "game_hall.MarkUserAtMsgReadResp")
	proto.RegisterType((*AddCancelUserMsgRecordReq)(nil), "game_hall.AddCancelUserMsgRecordReq")
	proto.RegisterType((*AddCancelUserMsgRecordReqRecord)(nil), "game_hall.AddCancelUserMsgRecordReq.record")
	proto.RegisterType((*AddCancelUserMsgRecordResp)(nil), "game_hall.AddCancelUserMsgRecordResp")
	proto.RegisterType((*GetFilterRecordsReq)(nil), "game_hall.GetFilterRecordsReq")
	proto.RegisterType((*GetFilterRecordsResp)(nil), "game_hall.GetFilterRecordsResp")
	proto.RegisterMapType((map[uint32]uint64)(nil), "game_hall.GetFilterRecordsResp.TabRecordMapEntry")
	proto.RegisterMapType((map[uint32]*GetFilterRecordsRespRecord)(nil), "game_hall.GetFilterRecordsResp.UidRecordMapEntry")
	proto.RegisterType((*GetFilterRecordsRespRecord)(nil), "game_hall.GetFilterRecordsResp.record")
	proto.RegisterType((*TempMsgInfo)(nil), "game_hall.TempMsgInfo")
	proto.RegisterType((*TempKeepImgMsgReq)(nil), "game_hall.TempKeepImgMsgReq")
	proto.RegisterType((*TempKeepImgMsgResp)(nil), "game_hall.TempKeepImgMsgResp")
	proto.RegisterType((*GetTempMsgReq)(nil), "game_hall.GetTempMsgReq")
	proto.RegisterType((*GetTempMsgResp)(nil), "game_hall.GetTempMsgResp")
	proto.RegisterType((*DelTempMsgReq)(nil), "game_hall.DelTempMsgReq")
	proto.RegisterType((*DelTempMsgResp)(nil), "game_hall.DelTempMsgResp")
	proto.RegisterType((*UpdateGameHallNotifyStatusReq)(nil), "game_hall.UpdateGameHallNotifyStatusReq")
	proto.RegisterType((*UpdateGameHallNotifyStatusResp)(nil), "game_hall.UpdateGameHallNotifyStatusResp")
	proto.RegisterType((*GetGameHallNotifyStatusReq)(nil), "game_hall.GetGameHallNotifyStatusReq")
	proto.RegisterType((*GetGameHallNotifyStatusResp)(nil), "game_hall.GetGameHallNotifyStatusResp")
	proto.RegisterType((*BatchGetGameHallNotifyStatusReq)(nil), "game_hall.BatchGetGameHallNotifyStatusReq")
	proto.RegisterType((*BatchGetGameHallNotifyStatusResp)(nil), "game_hall.BatchGetGameHallNotifyStatusResp")
	proto.RegisterMapType((map[uint32]bool)(nil), "game_hall.BatchGetGameHallNotifyStatusResp.NotifyStatusMapEntry")
	proto.RegisterType((*DelExpireMsgRecordByTabIdsReq)(nil), "game_hall.DelExpireMsgRecordByTabIdsReq")
	proto.RegisterType((*DelExpireMsgRecordByTabIdsResp)(nil), "game_hall.DelExpireMsgRecordByTabIdsResp")
	proto.RegisterType((*SetShowEntranceSettingReq)(nil), "game_hall.SetShowEntranceSettingReq")
	proto.RegisterType((*SetShowEntranceSettingResp)(nil), "game_hall.SetShowEntranceSettingResp")
	proto.RegisterType((*EntranceSettingItem)(nil), "game_hall.EntranceSettingItem")
	proto.RegisterType((*GetShowEntranceSettingReq)(nil), "game_hall.GetShowEntranceSettingReq")
	proto.RegisterType((*GetShowEntranceSettingResp)(nil), "game_hall.GetShowEntranceSettingResp")
	proto.RegisterType((*BatGetShowEntranceSettingByTabIdsReq)(nil), "game_hall.BatGetShowEntranceSettingByTabIdsReq")
	proto.RegisterType((*BatGetShowEntranceSettingByTabIdsResp)(nil), "game_hall.BatGetShowEntranceSettingByTabIdsResp")
	proto.RegisterMapType((map[uint32]*BatGetShowEntranceSettingByTabIdsResp_Item)(nil), "game_hall.BatGetShowEntranceSettingByTabIdsResp.EntranceSettingMapEntry")
	proto.RegisterType((*BatGetShowEntranceSettingByTabIdsResp_Item)(nil), "game_hall.BatGetShowEntranceSettingByTabIdsResp.Item")
	proto.RegisterType((*AddGameHallPinConfReq)(nil), "game_hall.AddGameHallPinConfReq")
	proto.RegisterType((*AddGameHallPinConfResp)(nil), "game_hall.AddGameHallPinConfResp")
	proto.RegisterType((*UpdateGameHallPinConfReq)(nil), "game_hall.UpdateGameHallPinConfReq")
	proto.RegisterType((*UpdateGameHallPinConfResp)(nil), "game_hall.UpdateGameHallPinConfResp")
	proto.RegisterType((*GameHallPinConfItem)(nil), "game_hall.GameHallPinConfItem")
	proto.RegisterType((*GameHallPinConfItem_HighlightText)(nil), "game_hall.GameHallPinConfItem.HighlightText")
	proto.RegisterType((*GetPinConfByAdminReq)(nil), "game_hall.GetPinConfByAdminReq")
	proto.RegisterType((*GetPinConfByAdminResp)(nil), "game_hall.GetPinConfByAdminResp")
	proto.RegisterType((*DelGameHallPinConfReq)(nil), "game_hall.DelGameHallPinConfReq")
	proto.RegisterType((*DelGameHallPinConfResp)(nil), "game_hall.DelGameHallPinConfResp")
	proto.RegisterType((*GetActivePinConfByTabIdReq)(nil), "game_hall.GetActivePinConfByTabIdReq")
	proto.RegisterType((*GetActivePinConfByTabIdResp)(nil), "game_hall.GetActivePinConfByTabIdResp")
	proto.RegisterEnum("game_hall.GameHallPinConfStatus", GameHallPinConfStatus_name, GameHallPinConfStatus_value)
	proto.RegisterEnum("game_hall.EntranceSettingItem_EntranceSource", EntranceSettingItem_EntranceSource_name, EntranceSettingItem_EntranceSource_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GameHallClient is the client API for GameHall service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameHallClient interface {
	AddCleanMsgRecord(ctx context.Context, in *AddCleanMsgRecordReq, opts ...grpc.CallOption) (*AddCleanMsgRecordResp, error)
	AddMsgRecord(ctx context.Context, in *AddMsgRecordReq, opts ...grpc.CallOption) (*AddMsgRecordResp, error)
	GetLastMsgIdBySendTime(ctx context.Context, in *GetLastMsgIdBySendTimeReq, opts ...grpc.CallOption) (*GetLastMsgIdBySendTimeResp, error)
	AddTeamAndInviteMsg(ctx context.Context, in *AddTeamAndInviteMsgReq, opts ...grpc.CallOption) (*AddTeamAndInviteMsgResp, error)
	JoinGameHallTeam(ctx context.Context, in *JoinGameHallTeamReq, opts ...grpc.CallOption) (*JoinGameHallTeamResp, error)
	BatchGetGameHallTeamList(ctx context.Context, in *BatchGetGameHallTeamListReq, opts ...grpc.CallOption) (*BatchGetGameHallTeamListResp, error)
	GetUserJoinTeamRecord(ctx context.Context, in *GetUserJoinTeamRecordReq, opts ...grpc.CallOption) (*GetUserJoinTeamRecordResp, error)
	GetTeamAndInviteMsg(ctx context.Context, in *GetTeamAndInviteMsgReq, opts ...grpc.CallOption) (*GetTeamAndInviteMsgResp, error)
	BanUserSendMsg(ctx context.Context, in *BanUserSendMsgReq, opts ...grpc.CallOption) (*BanUserSendMsgResp, error)
	GetUserBanStatusMap(ctx context.Context, in *GetUserBanStatusMapReq, opts ...grpc.CallOption) (*GetUserBanStatusMapResp, error)
	AddUserAtMsgRecord(ctx context.Context, in *AddUserAtMsgRecordReq, opts ...grpc.CallOption) (*AddUserAtMsgRecordResp, error)
	GetUserUnreadAtMsg(ctx context.Context, in *GetUserUnreadAtMsgReq, opts ...grpc.CallOption) (*GetUserUnreadAtMsgResp, error)
	MarkUserAtMsgRead(ctx context.Context, in *MarkUserAtMsgReadReq, opts ...grpc.CallOption) (*MarkUserAtMsgReadResp, error)
	AddCancelUserMsgRecord(ctx context.Context, in *AddCancelUserMsgRecordReq, opts ...grpc.CallOption) (*AddCancelUserMsgRecordResp, error)
	GetFilterRecords(ctx context.Context, in *GetFilterRecordsReq, opts ...grpc.CallOption) (*GetFilterRecordsResp, error)
	TempKeepImgMsg(ctx context.Context, in *TempKeepImgMsgReq, opts ...grpc.CallOption) (*TempKeepImgMsgResp, error)
	GetTempMsg(ctx context.Context, in *GetTempMsgReq, opts ...grpc.CallOption) (*GetTempMsgResp, error)
	DelTempMsg(ctx context.Context, in *DelTempMsgReq, opts ...grpc.CallOption) (*DelTempMsgResp, error)
	UpdateGameHallNotifyStatus(ctx context.Context, in *UpdateGameHallNotifyStatusReq, opts ...grpc.CallOption) (*UpdateGameHallNotifyStatusResp, error)
	GetGameHallNotifyStatus(ctx context.Context, in *GetGameHallNotifyStatusReq, opts ...grpc.CallOption) (*GetGameHallNotifyStatusResp, error)
	BatchGetGameHallNotifyStatus(ctx context.Context, in *BatchGetGameHallNotifyStatusReq, opts ...grpc.CallOption) (*BatchGetGameHallNotifyStatusResp, error)
	DelExpireMsgRecordByTabIds(ctx context.Context, in *DelExpireMsgRecordByTabIdsReq, opts ...grpc.CallOption) (*DelExpireMsgRecordByTabIdsResp, error)
	SetShowEntranceSetting(ctx context.Context, in *SetShowEntranceSettingReq, opts ...grpc.CallOption) (*SetShowEntranceSettingResp, error)
	GetShowEntranceSetting(ctx context.Context, in *GetShowEntranceSettingReq, opts ...grpc.CallOption) (*GetShowEntranceSettingResp, error)
	BatGetShowEntranceSettingByTabIds(ctx context.Context, in *BatGetShowEntranceSettingByTabIdsReq, opts ...grpc.CallOption) (*BatGetShowEntranceSettingByTabIdsResp, error)
	AddGameHallPinConf(ctx context.Context, in *AddGameHallPinConfReq, opts ...grpc.CallOption) (*AddGameHallPinConfResp, error)
	UpdateGameHallPinConf(ctx context.Context, in *UpdateGameHallPinConfReq, opts ...grpc.CallOption) (*UpdateGameHallPinConfResp, error)
	GetPinConfByAdmin(ctx context.Context, in *GetPinConfByAdminReq, opts ...grpc.CallOption) (*GetPinConfByAdminResp, error)
	DelGameHallPinConf(ctx context.Context, in *DelGameHallPinConfReq, opts ...grpc.CallOption) (*DelGameHallPinConfResp, error)
	GetActivePinConfByTabId(ctx context.Context, in *GetActivePinConfByTabIdReq, opts ...grpc.CallOption) (*GetActivePinConfByTabIdResp, error)
}

type gameHallClient struct {
	cc *grpc.ClientConn
}

func NewGameHallClient(cc *grpc.ClientConn) GameHallClient {
	return &gameHallClient{cc}
}

func (c *gameHallClient) AddCleanMsgRecord(ctx context.Context, in *AddCleanMsgRecordReq, opts ...grpc.CallOption) (*AddCleanMsgRecordResp, error) {
	out := new(AddCleanMsgRecordResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/AddCleanMsgRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) AddMsgRecord(ctx context.Context, in *AddMsgRecordReq, opts ...grpc.CallOption) (*AddMsgRecordResp, error) {
	out := new(AddMsgRecordResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/AddMsgRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) GetLastMsgIdBySendTime(ctx context.Context, in *GetLastMsgIdBySendTimeReq, opts ...grpc.CallOption) (*GetLastMsgIdBySendTimeResp, error) {
	out := new(GetLastMsgIdBySendTimeResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/GetLastMsgIdBySendTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) AddTeamAndInviteMsg(ctx context.Context, in *AddTeamAndInviteMsgReq, opts ...grpc.CallOption) (*AddTeamAndInviteMsgResp, error) {
	out := new(AddTeamAndInviteMsgResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/AddTeamAndInviteMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) JoinGameHallTeam(ctx context.Context, in *JoinGameHallTeamReq, opts ...grpc.CallOption) (*JoinGameHallTeamResp, error) {
	out := new(JoinGameHallTeamResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/JoinGameHallTeam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) BatchGetGameHallTeamList(ctx context.Context, in *BatchGetGameHallTeamListReq, opts ...grpc.CallOption) (*BatchGetGameHallTeamListResp, error) {
	out := new(BatchGetGameHallTeamListResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/BatchGetGameHallTeamList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) GetUserJoinTeamRecord(ctx context.Context, in *GetUserJoinTeamRecordReq, opts ...grpc.CallOption) (*GetUserJoinTeamRecordResp, error) {
	out := new(GetUserJoinTeamRecordResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/GetUserJoinTeamRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) GetTeamAndInviteMsg(ctx context.Context, in *GetTeamAndInviteMsgReq, opts ...grpc.CallOption) (*GetTeamAndInviteMsgResp, error) {
	out := new(GetTeamAndInviteMsgResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/GetTeamAndInviteMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) BanUserSendMsg(ctx context.Context, in *BanUserSendMsgReq, opts ...grpc.CallOption) (*BanUserSendMsgResp, error) {
	out := new(BanUserSendMsgResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/BanUserSendMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) GetUserBanStatusMap(ctx context.Context, in *GetUserBanStatusMapReq, opts ...grpc.CallOption) (*GetUserBanStatusMapResp, error) {
	out := new(GetUserBanStatusMapResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/GetUserBanStatusMap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) AddUserAtMsgRecord(ctx context.Context, in *AddUserAtMsgRecordReq, opts ...grpc.CallOption) (*AddUserAtMsgRecordResp, error) {
	out := new(AddUserAtMsgRecordResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/AddUserAtMsgRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) GetUserUnreadAtMsg(ctx context.Context, in *GetUserUnreadAtMsgReq, opts ...grpc.CallOption) (*GetUserUnreadAtMsgResp, error) {
	out := new(GetUserUnreadAtMsgResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/GetUserUnreadAtMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) MarkUserAtMsgRead(ctx context.Context, in *MarkUserAtMsgReadReq, opts ...grpc.CallOption) (*MarkUserAtMsgReadResp, error) {
	out := new(MarkUserAtMsgReadResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/MarkUserAtMsgRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) AddCancelUserMsgRecord(ctx context.Context, in *AddCancelUserMsgRecordReq, opts ...grpc.CallOption) (*AddCancelUserMsgRecordResp, error) {
	out := new(AddCancelUserMsgRecordResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/AddCancelUserMsgRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) GetFilterRecords(ctx context.Context, in *GetFilterRecordsReq, opts ...grpc.CallOption) (*GetFilterRecordsResp, error) {
	out := new(GetFilterRecordsResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/GetFilterRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) TempKeepImgMsg(ctx context.Context, in *TempKeepImgMsgReq, opts ...grpc.CallOption) (*TempKeepImgMsgResp, error) {
	out := new(TempKeepImgMsgResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/TempKeepImgMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) GetTempMsg(ctx context.Context, in *GetTempMsgReq, opts ...grpc.CallOption) (*GetTempMsgResp, error) {
	out := new(GetTempMsgResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/GetTempMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) DelTempMsg(ctx context.Context, in *DelTempMsgReq, opts ...grpc.CallOption) (*DelTempMsgResp, error) {
	out := new(DelTempMsgResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/DelTempMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) UpdateGameHallNotifyStatus(ctx context.Context, in *UpdateGameHallNotifyStatusReq, opts ...grpc.CallOption) (*UpdateGameHallNotifyStatusResp, error) {
	out := new(UpdateGameHallNotifyStatusResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/UpdateGameHallNotifyStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) GetGameHallNotifyStatus(ctx context.Context, in *GetGameHallNotifyStatusReq, opts ...grpc.CallOption) (*GetGameHallNotifyStatusResp, error) {
	out := new(GetGameHallNotifyStatusResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/GetGameHallNotifyStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) BatchGetGameHallNotifyStatus(ctx context.Context, in *BatchGetGameHallNotifyStatusReq, opts ...grpc.CallOption) (*BatchGetGameHallNotifyStatusResp, error) {
	out := new(BatchGetGameHallNotifyStatusResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/BatchGetGameHallNotifyStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) DelExpireMsgRecordByTabIds(ctx context.Context, in *DelExpireMsgRecordByTabIdsReq, opts ...grpc.CallOption) (*DelExpireMsgRecordByTabIdsResp, error) {
	out := new(DelExpireMsgRecordByTabIdsResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/DelExpireMsgRecordByTabIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) SetShowEntranceSetting(ctx context.Context, in *SetShowEntranceSettingReq, opts ...grpc.CallOption) (*SetShowEntranceSettingResp, error) {
	out := new(SetShowEntranceSettingResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/SetShowEntranceSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) GetShowEntranceSetting(ctx context.Context, in *GetShowEntranceSettingReq, opts ...grpc.CallOption) (*GetShowEntranceSettingResp, error) {
	out := new(GetShowEntranceSettingResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/GetShowEntranceSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) BatGetShowEntranceSettingByTabIds(ctx context.Context, in *BatGetShowEntranceSettingByTabIdsReq, opts ...grpc.CallOption) (*BatGetShowEntranceSettingByTabIdsResp, error) {
	out := new(BatGetShowEntranceSettingByTabIdsResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/BatGetShowEntranceSettingByTabIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) AddGameHallPinConf(ctx context.Context, in *AddGameHallPinConfReq, opts ...grpc.CallOption) (*AddGameHallPinConfResp, error) {
	out := new(AddGameHallPinConfResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/AddGameHallPinConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) UpdateGameHallPinConf(ctx context.Context, in *UpdateGameHallPinConfReq, opts ...grpc.CallOption) (*UpdateGameHallPinConfResp, error) {
	out := new(UpdateGameHallPinConfResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/UpdateGameHallPinConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) GetPinConfByAdmin(ctx context.Context, in *GetPinConfByAdminReq, opts ...grpc.CallOption) (*GetPinConfByAdminResp, error) {
	out := new(GetPinConfByAdminResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/GetPinConfByAdmin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) DelGameHallPinConf(ctx context.Context, in *DelGameHallPinConfReq, opts ...grpc.CallOption) (*DelGameHallPinConfResp, error) {
	out := new(DelGameHallPinConfResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/DelGameHallPinConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameHallClient) GetActivePinConfByTabId(ctx context.Context, in *GetActivePinConfByTabIdReq, opts ...grpc.CallOption) (*GetActivePinConfByTabIdResp, error) {
	out := new(GetActivePinConfByTabIdResp)
	err := c.cc.Invoke(ctx, "/game_hall.GameHall/GetActivePinConfByTabId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameHallServer is the server API for GameHall service.
type GameHallServer interface {
	AddCleanMsgRecord(context.Context, *AddCleanMsgRecordReq) (*AddCleanMsgRecordResp, error)
	AddMsgRecord(context.Context, *AddMsgRecordReq) (*AddMsgRecordResp, error)
	GetLastMsgIdBySendTime(context.Context, *GetLastMsgIdBySendTimeReq) (*GetLastMsgIdBySendTimeResp, error)
	AddTeamAndInviteMsg(context.Context, *AddTeamAndInviteMsgReq) (*AddTeamAndInviteMsgResp, error)
	JoinGameHallTeam(context.Context, *JoinGameHallTeamReq) (*JoinGameHallTeamResp, error)
	BatchGetGameHallTeamList(context.Context, *BatchGetGameHallTeamListReq) (*BatchGetGameHallTeamListResp, error)
	GetUserJoinTeamRecord(context.Context, *GetUserJoinTeamRecordReq) (*GetUserJoinTeamRecordResp, error)
	GetTeamAndInviteMsg(context.Context, *GetTeamAndInviteMsgReq) (*GetTeamAndInviteMsgResp, error)
	BanUserSendMsg(context.Context, *BanUserSendMsgReq) (*BanUserSendMsgResp, error)
	GetUserBanStatusMap(context.Context, *GetUserBanStatusMapReq) (*GetUserBanStatusMapResp, error)
	AddUserAtMsgRecord(context.Context, *AddUserAtMsgRecordReq) (*AddUserAtMsgRecordResp, error)
	GetUserUnreadAtMsg(context.Context, *GetUserUnreadAtMsgReq) (*GetUserUnreadAtMsgResp, error)
	MarkUserAtMsgRead(context.Context, *MarkUserAtMsgReadReq) (*MarkUserAtMsgReadResp, error)
	AddCancelUserMsgRecord(context.Context, *AddCancelUserMsgRecordReq) (*AddCancelUserMsgRecordResp, error)
	GetFilterRecords(context.Context, *GetFilterRecordsReq) (*GetFilterRecordsResp, error)
	TempKeepImgMsg(context.Context, *TempKeepImgMsgReq) (*TempKeepImgMsgResp, error)
	GetTempMsg(context.Context, *GetTempMsgReq) (*GetTempMsgResp, error)
	DelTempMsg(context.Context, *DelTempMsgReq) (*DelTempMsgResp, error)
	UpdateGameHallNotifyStatus(context.Context, *UpdateGameHallNotifyStatusReq) (*UpdateGameHallNotifyStatusResp, error)
	GetGameHallNotifyStatus(context.Context, *GetGameHallNotifyStatusReq) (*GetGameHallNotifyStatusResp, error)
	BatchGetGameHallNotifyStatus(context.Context, *BatchGetGameHallNotifyStatusReq) (*BatchGetGameHallNotifyStatusResp, error)
	DelExpireMsgRecordByTabIds(context.Context, *DelExpireMsgRecordByTabIdsReq) (*DelExpireMsgRecordByTabIdsResp, error)
	SetShowEntranceSetting(context.Context, *SetShowEntranceSettingReq) (*SetShowEntranceSettingResp, error)
	GetShowEntranceSetting(context.Context, *GetShowEntranceSettingReq) (*GetShowEntranceSettingResp, error)
	BatGetShowEntranceSettingByTabIds(context.Context, *BatGetShowEntranceSettingByTabIdsReq) (*BatGetShowEntranceSettingByTabIdsResp, error)
	AddGameHallPinConf(context.Context, *AddGameHallPinConfReq) (*AddGameHallPinConfResp, error)
	UpdateGameHallPinConf(context.Context, *UpdateGameHallPinConfReq) (*UpdateGameHallPinConfResp, error)
	GetPinConfByAdmin(context.Context, *GetPinConfByAdminReq) (*GetPinConfByAdminResp, error)
	DelGameHallPinConf(context.Context, *DelGameHallPinConfReq) (*DelGameHallPinConfResp, error)
	GetActivePinConfByTabId(context.Context, *GetActivePinConfByTabIdReq) (*GetActivePinConfByTabIdResp, error)
}

func RegisterGameHallServer(s *grpc.Server, srv GameHallServer) {
	s.RegisterService(&_GameHall_serviceDesc, srv)
}

func _GameHall_AddCleanMsgRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCleanMsgRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).AddCleanMsgRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/AddCleanMsgRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).AddCleanMsgRecord(ctx, req.(*AddCleanMsgRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_AddMsgRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMsgRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).AddMsgRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/AddMsgRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).AddMsgRecord(ctx, req.(*AddMsgRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_GetLastMsgIdBySendTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastMsgIdBySendTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).GetLastMsgIdBySendTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/GetLastMsgIdBySendTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).GetLastMsgIdBySendTime(ctx, req.(*GetLastMsgIdBySendTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_AddTeamAndInviteMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTeamAndInviteMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).AddTeamAndInviteMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/AddTeamAndInviteMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).AddTeamAndInviteMsg(ctx, req.(*AddTeamAndInviteMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_JoinGameHallTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinGameHallTeamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).JoinGameHallTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/JoinGameHallTeam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).JoinGameHallTeam(ctx, req.(*JoinGameHallTeamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_BatchGetGameHallTeamList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGameHallTeamListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).BatchGetGameHallTeamList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/BatchGetGameHallTeamList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).BatchGetGameHallTeamList(ctx, req.(*BatchGetGameHallTeamListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_GetUserJoinTeamRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserJoinTeamRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).GetUserJoinTeamRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/GetUserJoinTeamRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).GetUserJoinTeamRecord(ctx, req.(*GetUserJoinTeamRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_GetTeamAndInviteMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTeamAndInviteMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).GetTeamAndInviteMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/GetTeamAndInviteMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).GetTeamAndInviteMsg(ctx, req.(*GetTeamAndInviteMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_BanUserSendMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BanUserSendMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).BanUserSendMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/BanUserSendMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).BanUserSendMsg(ctx, req.(*BanUserSendMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_GetUserBanStatusMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBanStatusMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).GetUserBanStatusMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/GetUserBanStatusMap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).GetUserBanStatusMap(ctx, req.(*GetUserBanStatusMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_AddUserAtMsgRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserAtMsgRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).AddUserAtMsgRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/AddUserAtMsgRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).AddUserAtMsgRecord(ctx, req.(*AddUserAtMsgRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_GetUserUnreadAtMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserUnreadAtMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).GetUserUnreadAtMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/GetUserUnreadAtMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).GetUserUnreadAtMsg(ctx, req.(*GetUserUnreadAtMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_MarkUserAtMsgRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkUserAtMsgReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).MarkUserAtMsgRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/MarkUserAtMsgRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).MarkUserAtMsgRead(ctx, req.(*MarkUserAtMsgReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_AddCancelUserMsgRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCancelUserMsgRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).AddCancelUserMsgRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/AddCancelUserMsgRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).AddCancelUserMsgRecord(ctx, req.(*AddCancelUserMsgRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_GetFilterRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFilterRecordsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).GetFilterRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/GetFilterRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).GetFilterRecords(ctx, req.(*GetFilterRecordsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_TempKeepImgMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TempKeepImgMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).TempKeepImgMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/TempKeepImgMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).TempKeepImgMsg(ctx, req.(*TempKeepImgMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_GetTempMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTempMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).GetTempMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/GetTempMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).GetTempMsg(ctx, req.(*GetTempMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_DelTempMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelTempMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).DelTempMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/DelTempMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).DelTempMsg(ctx, req.(*DelTempMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_UpdateGameHallNotifyStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGameHallNotifyStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).UpdateGameHallNotifyStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/UpdateGameHallNotifyStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).UpdateGameHallNotifyStatus(ctx, req.(*UpdateGameHallNotifyStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_GetGameHallNotifyStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameHallNotifyStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).GetGameHallNotifyStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/GetGameHallNotifyStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).GetGameHallNotifyStatus(ctx, req.(*GetGameHallNotifyStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_BatchGetGameHallNotifyStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGameHallNotifyStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).BatchGetGameHallNotifyStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/BatchGetGameHallNotifyStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).BatchGetGameHallNotifyStatus(ctx, req.(*BatchGetGameHallNotifyStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_DelExpireMsgRecordByTabIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelExpireMsgRecordByTabIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).DelExpireMsgRecordByTabIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/DelExpireMsgRecordByTabIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).DelExpireMsgRecordByTabIds(ctx, req.(*DelExpireMsgRecordByTabIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_SetShowEntranceSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetShowEntranceSettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).SetShowEntranceSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/SetShowEntranceSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).SetShowEntranceSetting(ctx, req.(*SetShowEntranceSettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_GetShowEntranceSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetShowEntranceSettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).GetShowEntranceSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/GetShowEntranceSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).GetShowEntranceSetting(ctx, req.(*GetShowEntranceSettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_BatGetShowEntranceSettingByTabIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetShowEntranceSettingByTabIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).BatGetShowEntranceSettingByTabIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/BatGetShowEntranceSettingByTabIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).BatGetShowEntranceSettingByTabIds(ctx, req.(*BatGetShowEntranceSettingByTabIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_AddGameHallPinConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddGameHallPinConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).AddGameHallPinConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/AddGameHallPinConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).AddGameHallPinConf(ctx, req.(*AddGameHallPinConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_UpdateGameHallPinConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGameHallPinConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).UpdateGameHallPinConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/UpdateGameHallPinConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).UpdateGameHallPinConf(ctx, req.(*UpdateGameHallPinConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_GetPinConfByAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPinConfByAdminReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).GetPinConfByAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/GetPinConfByAdmin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).GetPinConfByAdmin(ctx, req.(*GetPinConfByAdminReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_DelGameHallPinConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelGameHallPinConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).DelGameHallPinConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/DelGameHallPinConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).DelGameHallPinConf(ctx, req.(*DelGameHallPinConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameHall_GetActivePinConfByTabId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActivePinConfByTabIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameHallServer).GetActivePinConfByTabId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_hall.GameHall/GetActivePinConfByTabId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameHallServer).GetActivePinConfByTabId(ctx, req.(*GetActivePinConfByTabIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameHall_serviceDesc = grpc.ServiceDesc{
	ServiceName: "game_hall.GameHall",
	HandlerType: (*GameHallServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddCleanMsgRecord",
			Handler:    _GameHall_AddCleanMsgRecord_Handler,
		},
		{
			MethodName: "AddMsgRecord",
			Handler:    _GameHall_AddMsgRecord_Handler,
		},
		{
			MethodName: "GetLastMsgIdBySendTime",
			Handler:    _GameHall_GetLastMsgIdBySendTime_Handler,
		},
		{
			MethodName: "AddTeamAndInviteMsg",
			Handler:    _GameHall_AddTeamAndInviteMsg_Handler,
		},
		{
			MethodName: "JoinGameHallTeam",
			Handler:    _GameHall_JoinGameHallTeam_Handler,
		},
		{
			MethodName: "BatchGetGameHallTeamList",
			Handler:    _GameHall_BatchGetGameHallTeamList_Handler,
		},
		{
			MethodName: "GetUserJoinTeamRecord",
			Handler:    _GameHall_GetUserJoinTeamRecord_Handler,
		},
		{
			MethodName: "GetTeamAndInviteMsg",
			Handler:    _GameHall_GetTeamAndInviteMsg_Handler,
		},
		{
			MethodName: "BanUserSendMsg",
			Handler:    _GameHall_BanUserSendMsg_Handler,
		},
		{
			MethodName: "GetUserBanStatusMap",
			Handler:    _GameHall_GetUserBanStatusMap_Handler,
		},
		{
			MethodName: "AddUserAtMsgRecord",
			Handler:    _GameHall_AddUserAtMsgRecord_Handler,
		},
		{
			MethodName: "GetUserUnreadAtMsg",
			Handler:    _GameHall_GetUserUnreadAtMsg_Handler,
		},
		{
			MethodName: "MarkUserAtMsgRead",
			Handler:    _GameHall_MarkUserAtMsgRead_Handler,
		},
		{
			MethodName: "AddCancelUserMsgRecord",
			Handler:    _GameHall_AddCancelUserMsgRecord_Handler,
		},
		{
			MethodName: "GetFilterRecords",
			Handler:    _GameHall_GetFilterRecords_Handler,
		},
		{
			MethodName: "TempKeepImgMsg",
			Handler:    _GameHall_TempKeepImgMsg_Handler,
		},
		{
			MethodName: "GetTempMsg",
			Handler:    _GameHall_GetTempMsg_Handler,
		},
		{
			MethodName: "DelTempMsg",
			Handler:    _GameHall_DelTempMsg_Handler,
		},
		{
			MethodName: "UpdateGameHallNotifyStatus",
			Handler:    _GameHall_UpdateGameHallNotifyStatus_Handler,
		},
		{
			MethodName: "GetGameHallNotifyStatus",
			Handler:    _GameHall_GetGameHallNotifyStatus_Handler,
		},
		{
			MethodName: "BatchGetGameHallNotifyStatus",
			Handler:    _GameHall_BatchGetGameHallNotifyStatus_Handler,
		},
		{
			MethodName: "DelExpireMsgRecordByTabIds",
			Handler:    _GameHall_DelExpireMsgRecordByTabIds_Handler,
		},
		{
			MethodName: "SetShowEntranceSetting",
			Handler:    _GameHall_SetShowEntranceSetting_Handler,
		},
		{
			MethodName: "GetShowEntranceSetting",
			Handler:    _GameHall_GetShowEntranceSetting_Handler,
		},
		{
			MethodName: "BatGetShowEntranceSettingByTabIds",
			Handler:    _GameHall_BatGetShowEntranceSettingByTabIds_Handler,
		},
		{
			MethodName: "AddGameHallPinConf",
			Handler:    _GameHall_AddGameHallPinConf_Handler,
		},
		{
			MethodName: "UpdateGameHallPinConf",
			Handler:    _GameHall_UpdateGameHallPinConf_Handler,
		},
		{
			MethodName: "GetPinConfByAdmin",
			Handler:    _GameHall_GetPinConfByAdmin_Handler,
		},
		{
			MethodName: "DelGameHallPinConf",
			Handler:    _GameHall_DelGameHallPinConf_Handler,
		},
		{
			MethodName: "GetActivePinConfByTabId",
			Handler:    _GameHall_GetActivePinConfByTabId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/game-hall/game-hall.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/game-hall/game-hall.proto", fileDescriptor_game_hall_6be111de87bb0979)
}

var fileDescriptor_game_hall_6be111de87bb0979 = []byte{
	// 2517 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x5a, 0xdd, 0x72, 0xdb, 0xc6,
	0xf5, 0x17, 0x3f, 0x24, 0x51, 0x47, 0x1f, 0x96, 0x56, 0x94, 0x44, 0x42, 0xb6, 0x45, 0xe3, 0xef,
	0x38, 0x4a, 0xfc, 0x37, 0x55, 0xc9, 0x4d, 0xc6, 0xfd, 0x98, 0xb1, 0x29, 0x8a, 0x96, 0xd9, 0x58,
	0x96, 0x07, 0xa4, 0x1c, 0x4f, 0x92, 0x09, 0x06, 0x22, 0x56, 0x14, 0x6c, 0x00, 0x84, 0xb9, 0x4b,
	0xd5, 0xf2, 0xe4, 0x01, 0xfa, 0x02, 0x7d, 0x84, 0xde, 0xf4, 0x01, 0x7a, 0xd7, 0xfb, 0x5e, 0xb7,
	0x7d, 0x98, 0xde, 0x74, 0xa6, 0xb3, 0x0b, 0x90, 0xc4, 0x02, 0x0b, 0x90, 0x6a, 0x7a, 0x45, 0xe0,
	0xec, 0xee, 0x39, 0x67, 0xcf, 0x9e, 0xaf, 0xfd, 0x81, 0xb0, 0x4b, 0xe9, 0xde, 0x87, 0x81, 0xd5,
	0x79, 0x4f, 0x2c, 0xfb, 0x0a, 0xf7, 0xf7, 0xba, 0x86, 0x83, 0x1f, 0x5d, 0x1a, 0xb6, 0x3d, 0x7e,
	0xaa, 0x7a, 0xfd, 0x1e, 0xed, 0xa1, 0x05, 0x46, 0xd0, 0x19, 0x41, 0xdd, 0x83, 0x62, 0xcd, 0x34,
	0xeb, 0x36, 0x36, 0xdc, 0x13, 0xd2, 0xd5, 0x70, 0xa7, 0xd7, 0x37, 0x35, 0xfc, 0x01, 0x6d, 0xc1,
	0x3c, 0x35, 0xce, 0x75, 0xcb, 0x24, 0xa5, 0x4c, 0x25, 0xb7, 0xbb, 0xac, 0xcd, 0x51, 0xe3, 0xbc,
	0x69, 0x12, 0x75, 0x0b, 0x36, 0x24, 0x0b, 0x88, 0xa7, 0xfe, 0x00, 0xb7, 0x6a, 0xa6, 0x29, 0x30,
	0xd9, 0x80, 0x39, 0x87, 0x74, 0x75, 0xcb, 0x2c, 0x65, 0x2a, 0x99, 0xdd, 0xbc, 0x36, 0xeb, 0x90,
	0x6e, 0xd3, 0x64, 0x64, 0x9f, 0x77, 0x29, 0x5b, 0xc9, 0xec, 0x2e, 0x6b, 0xb3, 0x9c, 0x35, 0xda,
	0x86, 0x05, 0x82, 0x5d, 0x53, 0xa7, 0x96, 0x83, 0x4b, 0xb9, 0x4a, 0x66, 0x37, 0xa7, 0x15, 0x18,
	0xa1, 0x6d, 0x39, 0x58, 0x45, 0xb0, 0x2a, 0x72, 0x27, 0x9e, 0x7a, 0x0a, 0xe5, 0x63, 0x4c, 0x5f,
	0x1a, 0x84, 0x9e, 0x30, 0xbe, 0x87, 0xd7, 0xad, 0x60, 0x36, 0x93, 0x2d, 0x70, 0xcb, 0x88, 0xdc,
	0x12, 0x34, 0x50, 0x1f, 0x83, 0x92, 0xc4, 0x90, 0x78, 0x09, 0xbb, 0x51, 0x9f, 0xc3, 0x66, 0xcd,
	0x34, 0xdb, 0xd8, 0x70, 0x6a, 0xae, 0xd9, 0x74, 0xaf, 0x2c, 0x8a, 0xb9, 0x9a, 0x37, 0xdc, 0xbe,
	0x5a, 0x86, 0x2d, 0x29, 0x1f, 0xe2, 0xa9, 0xd7, 0xb0, 0xfe, 0xbb, 0x9e, 0xe5, 0x1e, 0x1b, 0x0e,
	0x7e, 0x61, 0xd8, 0x36, 0x9b, 0x73, 0x73, 0xf3, 0x96, 0x81, 0xef, 0x5f, 0x1f, 0x58, 0x26, 0xb7,
	0xee, 0xb2, 0x36, 0xcf, 0xde, 0xcf, 0x2c, 0x3e, 0xf4, 0xae, 0x67, 0xb9, 0x7c, 0x28, 0xef, 0x0f,
	0xb1, 0xf7, 0x33, 0xcb, 0x54, 0x37, 0xa1, 0x18, 0x17, 0x4d, 0x3c, 0xf5, 0x04, 0xb6, 0x0f, 0x0d,
	0xda, 0xb9, 0x3c, 0xc6, 0x34, 0x3c, 0xf6, 0xd2, 0x22, 0x34, 0x70, 0x1f, 0x5f, 0x35, 0xdf, 0x7d,
	0xf2, 0xda, 0x1c, 0xd7, 0x8d, 0x24, 0x6d, 0xfe, 0x6f, 0x19, 0xb8, 0x9d, 0xcc, 0x8f, 0x78, 0xe8,
	0x14, 0x0a, 0x0e, 0x76, 0x74, 0xcb, 0xbd, 0xe8, 0x71, 0x8e, 0x8b, 0x07, 0xbf, 0xac, 0x8e, 0xbc,
	0xb8, 0x9a, 0xb6, 0xb4, 0x7a, 0x82, 0x9d, 0xa6, 0x7b, 0xd1, 0x6b, 0xb8, 0xb4, 0x7f, 0xad, 0xcd,
	0x3b, 0xfe, 0x9b, 0xf2, 0x2d, 0x2c, 0x85, 0x07, 0xd0, 0x2a, 0xe4, 0xde, 0xe3, 0xeb, 0xc0, 0x92,
	0xec, 0x11, 0xed, 0xc3, 0xec, 0x95, 0x61, 0x0f, 0x30, 0xd7, 0x74, 0xf1, 0x60, 0x3b, 0x24, 0x2f,
	0x2c, 0x87, 0xb1, 0xd0, 0xfc, 0x99, 0xbf, 0xce, 0x3e, 0xc9, 0xa8, 0x36, 0xac, 0x46, 0x87, 0x05,
	0x03, 0xfb, 0xe1, 0x34, 0x34, 0x30, 0xaa, 0xc0, 0x92, 0x45, 0x74, 0x82, 0xed, 0x0b, 0x9d, 0x91,
	0xb8, 0xb0, 0x82, 0x06, 0x16, 0x69, 0x61, 0xfb, 0x82, 0x99, 0x9e, 0x79, 0x32, 0xdb, 0x7a, 0xa7,
	0x37, 0x70, 0x69, 0x70, 0x72, 0xcc, 0x16, 0x75, 0xf6, 0xae, 0xee, 0x43, 0xe9, 0x18, 0xd3, 0x33,
	0x82, 0xfb, 0x6c, 0xae, 0x7f, 0x3c, 0xa1, 0xf0, 0x0b, 0x6c, 0x9d, 0x09, 0xdb, 0xfa, 0x08, 0xd6,
	0xc3, 0xf3, 0x03, 0x2b, 0x30, 0x03, 0x0c, 0x46, 0x53, 0xd9, 0x23, 0x13, 0xcc, 0xb5, 0xe6, 0x21,
	0x94, 0xf5, 0x43, 0x88, 0x11, 0x78, 0x40, 0xbe, 0xe1, 0xc1, 0x27, 0x13, 0x4c, 0x3c, 0xf4, 0xab,
	0xd8, 0x69, 0xdd, 0x0d, 0x59, 0x4f, 0x22, 0x7d, 0x74, 0x2e, 0xea, 0x0f, 0xb0, 0x79, 0x8c, 0x69,
	0x42, 0x38, 0x49, 0xb6, 0x13, 0x8a, 0x82, 0x6c, 0x38, 0x0a, 0x8a, 0x30, 0x6b, 0x5b, 0x8e, 0x45,
	0x83, 0x4c, 0xe2, 0xbf, 0xa8, 0x07, 0xb0, 0x25, 0xe5, 0x4e, 0xbc, 0x44, 0x97, 0x55, 0x5f, 0xc0,
	0xda, 0xa1, 0xe1, 0x32, 0xa5, 0x59, 0x3a, 0x08, 0x94, 0x89, 0x5b, 0xeb, 0x1e, 0x2c, 0x9d, 0x1b,
	0xae, 0x6e, 0x0e, 0xfa, 0x06, 0xb5, 0x7a, 0x6e, 0x60, 0xb0, 0xc5, 0x73, 0xc3, 0x3d, 0x0a, 0x48,
	0x6a, 0x11, 0x50, 0x94, 0x13, 0xf1, 0xd4, 0xc7, 0x7c, 0xc7, 0x8c, 0x7a, 0x68, 0xb8, 0x2d, 0x6a,
	0xd0, 0x01, 0x39, 0x31, 0x3c, 0x26, 0xa4, 0x0c, 0x85, 0x81, 0x65, 0xea, 0xb6, 0x45, 0xe8, 0xd0,
	0x6d, 0x06, 0x96, 0xc9, 0x1c, 0x5b, 0xfd, 0x4b, 0x86, 0xef, 0x24, 0xbe, 0x8a, 0x78, 0xe8, 0x3b,
	0x58, 0x61, 0x9a, 0x10, 0x4e, 0xd4, 0x1d, 0xc3, 0x93, 0x44, 0x4c, 0xc2, 0xda, 0x6a, 0x98, 0xe0,
	0x47, 0x0c, 0xdb, 0xd5, 0x88, 0xa4, 0x3c, 0xe5, 0xc6, 0x10, 0xa7, 0x84, 0x63, 0x67, 0xd9, 0x8f,
	0x9d, 0x62, 0x38, 0x76, 0x72, 0xe1, 0xf0, 0xb8, 0xe2, 0xf5, 0x83, 0xc9, 0xae, 0x51, 0xa1, 0x58,
	0x20, 0xc8, 0x0f, 0xc6, 0xe5, 0x86, 0x3f, 0x27, 0xa5, 0xb2, 0xf1, 0x91, 0xe7, 0xc2, 0x47, 0x2e,
	0xa4, 0xfc, 0x7c, 0xa4, 0x80, 0x94, 0x78, 0x9a, 0x8e, 0xc9, 0x25, 0x9e, 0xfa, 0x0c, 0x36, 0x02,
	0x6b, 0x9c, 0xb9, 0x7d, 0x6c, 0x98, 0xc1, 0xb8, 0xec, 0x8c, 0x13, 0xb2, 0xd7, 0xfe, 0xe8, 0x04,
	0x05, 0x0e, 0x69, 0x4e, 0xf5, 0x16, 0x8a, 0x27, 0x46, 0xff, 0x7d, 0x48, 0x1f, 0xc3, 0xbc, 0x89,
	0xcc, 0x30, 0xe7, 0x9c, 0xc0, 0x79, 0x0b, 0x36, 0x24, 0x9c, 0x89, 0xa7, 0xfe, 0x31, 0x03, 0x65,
	0x56, 0xba, 0x0d, 0xb7, 0x83, 0x6d, 0x36, 0x2c, 0x98, 0xbf, 0x01, 0xf3, 0x7d, 0xfe, 0x42, 0x02,
	0x6f, 0x79, 0x18, 0xf2, 0x96, 0xc4, 0x65, 0x55, 0x7f, 0x8d, 0x36, 0x5c, 0xab, 0xec, 0xc3, 0x9c,
	0xff, 0x38, 0xbd, 0xf5, 0x6e, 0x83, 0x92, 0xc4, 0x9f, 0x78, 0xea, 0x0b, 0x58, 0x3f, 0xc6, 0xf4,
	0xb9, 0x65, 0x53, 0xdc, 0xf7, 0xc9, 0x24, 0x3d, 0x34, 0xd0, 0x26, 0x04, 0xbd, 0x4a, 0x29, 0x2b,
	0x74, 0x2e, 0x7f, 0xce, 0x41, 0x31, 0xce, 0x8a, 0x78, 0xe8, 0x5b, 0x58, 0x61, 0xbc, 0x7c, 0xbd,
	0x43, 0xf1, 0xb2, 0x2f, 0xc6, 0x4b, 0x6c, 0x61, 0xf5, 0xcc, 0x32, 0xfd, 0xd7, 0x71, 0xb0, 0x0c,
	0x42, 0x24, 0xc6, 0x98, 0x6d, 0x38, 0xc4, 0x38, 0x3b, 0x1d, 0xe3, 0xb6, 0x71, 0x1e, 0x65, 0x4c,
	0x43, 0x24, 0xe5, 0xeb, 0x91, 0x95, 0x6f, 0x94, 0x14, 0x95, 0x2e, 0xac, 0xc5, 0x74, 0x96, 0x44,
	0xef, 0x6f, 0xc5, 0xca, 0xf7, 0x60, 0x92, 0xba, 0x81, 0x13, 0x8c, 0xa3, 0x9c, 0xa5, 0x89, 0xd8,
	0x1e, 0x26, 0xa5, 0x89, 0x7c, 0x38, 0x4d, 0xfc, 0x29, 0x03, 0x8b, 0x6d, 0xec, 0x78, 0xac, 0x11,
	0x63, 0xd5, 0xa9, 0x08, 0xfe, 0x16, 0xc4, 0x56, 0xa7, 0x0c, 0x05, 0xb6, 0x4d, 0x7a, 0xed, 0xe1,
	0xc0, 0xa7, 0x58, 0x50, 0xb4, 0xaf, 0x3d, 0x8c, 0x76, 0x60, 0x91, 0x0d, 0x75, 0x7a, 0x2e, 0xc5,
	0x41, 0xdd, 0x5c, 0xd2, 0xc0, 0x21, 0xdd, 0xba, 0x4f, 0x09, 0x59, 0x2e, 0x9f, 0xd8, 0x85, 0xce,
	0x46, 0xfa, 0xc6, 0xc0, 0xa7, 0xe7, 0x46, 0x3e, 0xad, 0x3e, 0x87, 0x35, 0xa6, 0xe6, 0x37, 0x18,
	0x7b, 0x4d, 0xa7, 0x1b, 0x24, 0x8e, 0x7d, 0x5f, 0xad, 0xa0, 0xfc, 0x31, 0x13, 0x6e, 0x86, 0x4c,
	0x18, 0xda, 0x16, 0x57, 0x97, 0x97, 0xbd, 0x22, 0xa0, 0x28, 0x1f, 0xe2, 0xa9, 0xbf, 0x81, 0x65,
	0x5e, 0xae, 0xf8, 0x02, 0xc6, 0x59, 0x6e, 0x86, 0x22, 0xf8, 0xca, 0x8b, 0x71, 0x55, 0x87, 0x95,
	0xf0, 0x62, 0xe2, 0xfd, 0x37, 0x7a, 0xbd, 0x83, 0xe5, 0x23, 0x6c, 0x4f, 0xd4, 0xe0, 0x11, 0xac,
	0xbb, 0x18, 0x9b, 0xba, 0x89, 0x6d, 0x9d, 0x89, 0xf0, 0x3d, 0x22, 0x68, 0x66, 0x56, 0xd9, 0xd0,
	0x11, 0xb6, 0x47, 0x81, 0x3d, 0x56, 0x38, 0x17, 0x56, 0x78, 0x15, 0x56, 0xc2, 0xb2, 0x88, 0xa7,
	0x5a, 0x70, 0xe7, 0xcc, 0x33, 0x0d, 0x8a, 0x87, 0x1d, 0xd5, 0xab, 0x1e, 0xb5, 0x2e, 0xae, 0xfd,
	0xf2, 0x73, 0xa3, 0x74, 0xb9, 0x03, 0x8b, 0x3d, 0x0f, 0xbb, 0xba, 0xcb, 0x19, 0x70, 0xb9, 0x05,
	0x0d, 0x18, 0xc9, 0x67, 0xa9, 0x56, 0xe0, 0x6e, 0x9a, 0x28, 0xe2, 0xa9, 0x0d, 0x7e, 0x3b, 0xf8,
	0xb9, 0x9a, 0xa8, 0x5f, 0xc3, 0x76, 0x22, 0x1b, 0xbf, 0x62, 0x58, 0x44, 0x67, 0x8a, 0x71, 0x5e,
	0x05, 0x6d, 0xce, 0x22, 0xa7, 0x1e, 0x76, 0xd5, 0x97, 0xb0, 0x13, 0x6d, 0x73, 0xa3, 0x3a, 0x4c,
	0x5f, 0x42, 0xd5, 0x7f, 0x66, 0xa0, 0x92, 0xce, 0x8e, 0x78, 0xc8, 0x86, 0x35, 0xdf, 0x5e, 0xf1,
	0x5e, 0xe2, 0x59, 0x4a, 0xf7, 0x1d, 0xe5, 0x53, 0x0d, 0x13, 0x46, 0x19, 0xed, 0x96, 0x2b, 0x52,
	0x95, 0x43, 0x28, 0xca, 0x26, 0x4e, 0x4a, 0x1b, 0x85, 0x70, 0xda, 0x78, 0x02, 0x77, 0x8e, 0xb0,
	0xdd, 0xf8, 0xe8, 0x59, 0x7d, 0x3c, 0x72, 0xb7, 0xc3, 0xeb, 0x36, 0xaf, 0x00, 0xa9, 0xf7, 0xda,
	0x0a, 0xdc, 0x4d, 0x5b, 0x49, 0x3c, 0xf5, 0x23, 0x94, 0x5b, 0x98, 0xb6, 0x2e, 0x7b, 0xbf, 0x67,
	0x7a, 0xb1, 0x72, 0xd5, 0xc2, 0x94, 0x5a, 0x6e, 0x42, 0xaf, 0xd0, 0x84, 0x55, 0x1c, 0xcc, 0xd3,
	0x89, 0x3f, 0x31, 0xc8, 0xa7, 0xe1, 0x5e, 0x38, 0xc2, 0xaa, 0x49, 0xb1, 0xa3, 0xdd, 0xc2, 0x22,
	0x91, 0x55, 0xc8, 0x24, 0xc9, 0xc4, 0x53, 0xff, 0x9d, 0x81, 0x75, 0x09, 0x9b, 0xa4, 0xd2, 0x10,
	0x72, 0xb0, 0x6c, 0xd8, 0xc1, 0xd0, 0x1b, 0xb8, 0x35, 0x56, 0xb8, 0x37, 0xe8, 0x77, 0xfc, 0x5b,
	0xf8, 0xca, 0xc1, 0xa3, 0x74, 0x7d, 0xc7, 0x34, 0xbe, 0x48, 0x5b, 0xc1, 0xc2, 0xfb, 0xd0, 0x34,
	0xf9, 0x71, 0xd2, 0x7c, 0x09, 0x2b, 0xe2, 0x1a, 0xb4, 0x03, 0xdb, 0x8d, 0x57, 0x6d, 0xad, 0xf6,
	0xaa, 0xde, 0xd0, 0x5b, 0xa7, 0x67, 0x5a, 0xbd, 0xa1, 0x9f, 0xbd, 0x6a, 0xbd, 0x6e, 0xd4, 0x9b,
	0xcf, 0x9b, 0x8d, 0xa3, 0xd5, 0x19, 0x54, 0x86, 0x8d, 0xe8, 0x84, 0xd7, 0x75, 0xbd, 0x79, 0xb2,
	0x9a, 0x51, 0x8f, 0xf8, 0x4d, 0x64, 0xea, 0x73, 0x49, 0x08, 0x08, 0x8f, 0x47, 0x77, 0x82, 0x8d,
	0x91, 0x06, 0x1b, 0xd1, 0xc3, 0x1c, 0xf7, 0x1e, 0x93, 0x4f, 0x74, 0x3d, 0x72, 0xa2, 0xbc, 0x85,
	0x7f, 0x0a, 0xf7, 0x0f, 0x0d, 0x2a, 0x17, 0x9a, 0xe0, 0xb2, 0x62, 0x43, 0xf3, 0xd7, 0x2c, 0x7c,
	0x36, 0x05, 0x07, 0xe2, 0xa1, 0x4f, 0x50, 0x8c, 0xa9, 0x3f, 0x8e, 0xe5, 0x17, 0x62, 0x2c, 0x4f,
	0xe6, 0x17, 0xdd, 0xe3, 0x28, 0xa6, 0x11, 0x8e, 0x0d, 0x28, 0x0a, 0xe4, 0xb9, 0x3b, 0x4a, 0x92,
	0x93, 0xf2, 0x13, 0x6c, 0x25, 0xb0, 0x92, 0x44, 0xfd, 0x37, 0x62, 0x57, 0xf2, 0xd5, 0x8d, 0xb5,
	0xe6, 0x47, 0x11, 0x4a, 0x16, 0xdf, 0xf3, 0xab, 0xc8, 0x30, 0x6b, 0xbd, 0xb6, 0xdc, 0x7a, 0xcf,
	0xbd, 0x60, 0x16, 0x3f, 0x84, 0x65, 0xcf, 0x72, 0x59, 0xef, 0x70, 0xa1, 0x5b, 0x14, 0x3b, 0x41,
	0xb1, 0xbc, 0x2b, 0x41, 0x00, 0x82, 0x55, 0x9c, 0xf5, 0xa2, 0x37, 0x7e, 0x09, 0xee, 0x1b, 0x31,
	0xe6, 0xc4, 0x53, 0x7f, 0x84, 0x92, 0x58, 0x69, 0xfe, 0xc7, 0x92, 0xb7, 0xa1, 0x9c, 0xc0, 0x9f,
	0x78, 0xea, 0x3f, 0x72, 0xb0, 0x2e, 0xe1, 0x80, 0x56, 0x20, 0x1b, 0x84, 0xc9, 0x82, 0x96, 0xb5,
	0xcc, 0x44, 0xa7, 0x63, 0xc7, 0x48, 0x69, 0x70, 0xf3, 0x5a, 0xd0, 0xf8, 0x73, 0x3c, 0xc2, 0x51,
	0x09, 0xe6, 0x87, 0x9d, 0xd7, 0x2c, 0x9f, 0x38, 0x7c, 0x45, 0xcf, 0x20, 0x4f, 0xf1, 0x47, 0x5a,
	0x9a, 0xe3, 0xae, 0xf7, 0xff, 0xe9, 0x1b, 0xab, 0xbe, 0xb0, 0xba, 0x97, 0xb6, 0xd5, 0xbd, 0xa4,
	0x6d, 0xfc, 0x91, 0x6a, 0x7c, 0x25, 0x93, 0x66, 0x39, 0xdd, 0xd2, 0x3c, 0xe7, 0xcb, 0x1e, 0xd1,
	0x13, 0x98, 0xf3, 0x0b, 0x54, 0xa9, 0xc0, 0x13, 0x56, 0x25, 0x99, 0x6b, 0x50, 0x95, 0x82, 0xf9,
	0xe8, 0x0e, 0x00, 0xa1, 0x46, 0x9f, 0xfa, 0xed, 0xde, 0x02, 0x6f, 0xf7, 0x16, 0x38, 0x85, 0xf7,
	0x7b, 0x65, 0x28, 0x8c, 0x7a, 0x41, 0xe0, 0x83, 0xf3, 0xc3, 0x56, 0x70, 0x07, 0x16, 0x07, 0xdc,
	0xca, 0xfe, 0xe8, 0x22, 0x1f, 0x05, 0x9f, 0xc4, 0x27, 0x7c, 0x01, 0x6b, 0x3e, 0xb0, 0x83, 0x3b,
	0x54, 0x37, 0x6c, 0x5b, 0xa7, 0xc6, 0x79, 0x69, 0x89, 0x67, 0xdc, 0x15, 0x8e, 0xee, 0xe0, 0x0e,
	0xad, 0xd9, 0x76, 0xdb, 0x38, 0x57, 0x9e, 0xc2, 0xb2, 0xb0, 0x51, 0x74, 0x1b, 0x16, 0x2e, 0x87,
	0x04, 0xee, 0xee, 0x0b, 0xda, 0x98, 0xc0, 0xcd, 0xdd, 0xb7, 0x83, 0x13, 0x60, 0x8f, 0xea, 0xdf,
	0x33, 0xfc, 0x6a, 0x13, 0xec, 0xf1, 0xf0, 0xba, 0x66, 0x3a, 0x96, 0x9b, 0x82, 0x99, 0x8c, 0x0d,
	0x96, 0xfd, 0x59, 0x06, 0xcb, 0xa5, 0x19, 0x2c, 0x2f, 0x1a, 0x0c, 0x41, 0xde, 0x33, 0xba, 0xc3,
	0x9e, 0x9a, 0x3f, 0xb3, 0x66, 0x9b, 0xfd, 0xea, 0xc4, 0xfa, 0x84, 0x79, 0x57, 0x9d, 0xd3, 0x0a,
	0x8c, 0xd0, 0xb2, 0x3e, 0x61, 0xf5, 0x27, 0x7e, 0x2f, 0x8f, 0xee, 0x89, 0x78, 0xcc, 0xf4, 0xb4,
	0x47, 0x0d, 0x3b, 0x80, 0xc4, 0x7c, 0x70, 0x17, 0x38, 0x89, 0x83, 0x62, 0x42, 0x14, 0xf1, 0x2c,
	0x9d, 0x8d, 0x65, 0xe9, 0xb4, 0x28, 0xe2, 0xd9, 0xf9, 0x73, 0xd8, 0x38, 0xc2, 0xb6, 0x24, 0x44,
	0x23, 0x91, 0xc2, 0x02, 0x5d, 0x36, 0x91, 0x03, 0x3b, 0xac, 0xa4, 0xd4, 0x3a, 0xd4, 0xba, 0xc2,
	0xa3, 0x6d, 0xf0, 0x7c, 0x94, 0x82, 0xce, 0x19, 0xbc, 0x3d, 0x94, 0x2f, 0x22, 0x5e, 0x7c, 0x6b,
	0x99, 0x1b, 0x6f, 0xed, 0x4b, 0x07, 0x36, 0xa4, 0x87, 0x8c, 0x36, 0x01, 0xb5, 0xda, 0xb5, 0xf6,
	0x59, 0x2b, 0x52, 0x7c, 0x37, 0x60, 0x2d, 0xa0, 0xbf, 0x3a, 0x6d, 0xeb, 0xb5, 0x7a, 0xbb, 0xf9,
	0xa6, 0xb1, 0x9a, 0x41, 0x6b, 0xb0, 0x1c, 0x90, 0x03, 0x52, 0x16, 0x21, 0x58, 0x09, 0x48, 0x8d,
	0xb7, 0xaf, 0x9b, 0x5a, 0xe3, 0x68, 0x35, 0x77, 0xf0, 0xaf, 0x4d, 0x28, 0x0c, 0xe5, 0xa1, 0xb7,
	0xb0, 0x16, 0xfb, 0x7c, 0x80, 0x76, 0x22, 0x50, 0x43, 0xf4, 0x6b, 0x84, 0x52, 0x49, 0x9f, 0x40,
	0x3c, 0x75, 0x06, 0x35, 0x61, 0x29, 0xfc, 0x85, 0x00, 0x29, 0xe2, 0x1a, 0x81, 0xdf, 0x76, 0xe2,
	0x18, 0x67, 0xd5, 0xe5, 0x78, 0x8e, 0xe4, 0x3b, 0x00, 0xba, 0x2f, 0x5e, 0x85, 0xe5, 0xdf, 0x1e,
	0x94, 0xcf, 0xa6, 0x98, 0xc5, 0x05, 0xfd, 0x08, 0xeb, 0x12, 0xcc, 0x1f, 0xdd, 0x13, 0xd5, 0x93,
	0x80, 0xa1, 0x8a, 0x3a, 0x69, 0x0a, 0xe7, 0x7f, 0x06, 0xab, 0x51, 0xf4, 0x1e, 0x85, 0x5d, 0x45,
	0xf2, 0x55, 0x41, 0xd9, 0x49, 0x1d, 0xe7, 0x6c, 0x1d, 0x28, 0x25, 0x21, 0xee, 0xe8, 0xc1, 0x54,
	0xb0, 0xfc, 0x07, 0xe5, 0xf3, 0x29, 0xe1, 0x7b, 0x75, 0x06, 0x99, 0x23, 0x80, 0x4e, 0x84, 0x9a,
	0xd1, 0xff, 0xc5, 0x01, 0xcd, 0x18, 0x0a, 0xae, 0xdc, 0x9f, 0x3c, 0x69, 0x78, 0x16, 0x12, 0x68,
	0x58, 0x38, 0x0b, 0x39, 0x30, 0x2d, 0x9c, 0x45, 0x02, 0xba, 0xac, 0xce, 0xa0, 0x53, 0x58, 0x11,
	0xc1, 0x5f, 0x74, 0x5b, 0x30, 0x41, 0x04, 0x61, 0x56, 0xee, 0xa4, 0x8c, 0x86, 0x14, 0x8e, 0xa2,
	0xb8, 0x51, 0x85, 0x25, 0xb8, 0x72, 0x54, 0x61, 0x19, 0x10, 0xac, 0xce, 0xa0, 0xef, 0x01, 0xc5,
	0x11, 0x53, 0x14, 0x09, 0xc5, 0x38, 0x90, 0xab, 0xdc, 0x9b, 0x30, 0x63, 0xc8, 0x3c, 0x0e, 0x99,
	0x0a, 0xcc, 0xa5, 0x98, 0xac, 0x72, 0x6f, 0xc2, 0x0c, 0xce, 0xfc, 0x2d, 0xac, 0xc5, 0x20, 0x50,
	0x21, 0xc9, 0xc8, 0xa0, 0x57, 0x21, 0xc9, 0xc8, 0x11, 0x54, 0x9e, 0x19, 0xe4, 0x58, 0xa5, 0x90,
	0x19, 0x12, 0xe1, 0x52, 0x21, 0x33, 0xa4, 0x80, 0x9e, 0x3c, 0x72, 0xa3, 0x50, 0x9b, 0x10, 0xb9,
	0x12, 0x4c, 0x54, 0x88, 0x5c, 0x19, 0x4e, 0xe7, 0x3b, 0xa1, 0x08, 0x33, 0x09, 0x4e, 0x18, 0x43,
	0xb2, 0x04, 0x27, 0x94, 0xe0, 0x53, 0x33, 0xa8, 0x0e, 0x30, 0x06, 0x99, 0x50, 0x29, 0x1a, 0x09,
	0x43, 0xd8, 0x48, 0x29, 0x27, 0x8c, 0x0c, 0x99, 0x8c, 0x81, 0x1f, 0x81, 0x89, 0x80, 0x3d, 0x09,
	0x4c, 0x22, 0x48, 0xd1, 0x0c, 0x22, 0xa0, 0x24, 0x03, 0x38, 0x68, 0x37, 0xfc, 0xfd, 0x29, 0x0d,
	0x52, 0x52, 0xbe, 0x98, 0x72, 0x26, 0x17, 0xfa, 0x8e, 0x7f, 0x85, 0x91, 0x4a, 0x8c, 0x14, 0x81,
	0x24, 0x71, 0x0f, 0xa6, 0x99, 0xc6, 0x65, 0x5d, 0xc7, 0x3f, 0x91, 0x0a, 0x02, 0xbf, 0x9c, 0x1a,
	0x92, 0xf9, 0xa0, 0x3c, 0xbc, 0x01, 0x7c, 0xe3, 0xdb, 0x36, 0x19, 0x1c, 0x11, 0x6c, 0x9b, 0x8a,
	0xbe, 0x08, 0xb6, 0x9d, 0x80, 0xb6, 0xf0, 0x58, 0x93, 0xa3, 0x1e, 0x42, 0xac, 0x25, 0x42, 0x32,
	0x42, 0xac, 0xa5, 0xc0, 0x27, 0xc3, 0x72, 0x3f, 0x49, 0xd0, 0xf1, 0x54, 0x82, 0x8e, 0xd3, 0x04,
	0xfd, 0x21, 0x03, 0xf7, 0x26, 0x5e, 0x55, 0xd1, 0xde, 0xcd, 0x2e, 0xb6, 0x1f, 0x94, 0x5f, 0xdc,
	0xf4, 0x26, 0x3c, 0x4a, 0xee, 0x91, 0x36, 0x30, 0x9a, 0xdc, 0xe3, 0xdd, 0x6f, 0x34, 0xb9, 0xcb,
	0xda, 0x5e, 0x5e, 0xb0, 0xa5, 0x37, 0x50, 0xa1, 0x60, 0x27, 0xdd, 0x81, 0x85, 0x82, 0x9d, 0x7c,
	0x91, 0xe5, 0x59, 0x3e, 0x76, 0x3f, 0x40, 0x91, 0x1c, 0x18, 0xbb, 0x11, 0x29, 0x95, 0xf4, 0x09,
	0x43, 0xe3, 0xc4, 0x5b, 0x7a, 0xc1, 0x38, 0xd2, 0xab, 0x81, 0x60, 0x9c, 0x84, 0x3b, 0xc1, 0x30,
	0x65, 0xc8, 0x1a, 0xfc, 0x68, 0xca, 0x48, 0xb8, 0x39, 0x44, 0x53, 0x46, 0xd2, 0x5d, 0x41, 0x9d,
	0x39, 0x7c, 0xf4, 0xdd, 0xc3, 0x6e, 0xcf, 0x36, 0xdc, 0x6e, 0xf5, 0xab, 0x03, 0x4a, 0xab, 0x9d,
	0x9e, 0xb3, 0xc7, 0xff, 0x01, 0xd4, 0xe9, 0xd9, 0x7b, 0x04, 0xf7, 0xaf, 0xac, 0x0e, 0x26, 0xe3,
	0x7f, 0x07, 0x9d, 0xcf, 0xf1, 0xc1, 0xc7, 0xff, 0x09, 0x00, 0x00, 0xff, 0xff, 0x9c, 0x23, 0x19,
	0x24, 0x4a, 0x24, 0x00, 0x00,
}
