// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/avatar-mng-api/avatar-mng-api.proto

package avatar_mng_api // import "golang.52tt.com/protocol/services/avatar-mng-api"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AvatarDataType int32

const (
	AvatarDataType_AVATAR_DATA_TYPE_UNSPECIFIED AvatarDataType = 0
	AvatarDataType_AVATAR_DATA_TYPE_BIG         AvatarDataType = 1
	AvatarDataType_AVATAR_DATA__TYPE_SMALL      AvatarDataType = 2
)

var AvatarDataType_name = map[int32]string{
	0: "AVATAR_DATA_TYPE_UNSPECIFIED",
	1: "AVATAR_DATA_TYPE_BIG",
	2: "AVATAR_DATA__TYPE_SMALL",
}
var AvatarDataType_value = map[string]int32{
	"AVATAR_DATA_TYPE_UNSPECIFIED": 0,
	"AVATAR_DATA_TYPE_BIG":         1,
	"AVATAR_DATA__TYPE_SMALL":      2,
}

func (x AvatarDataType) String() string {
	return proto.EnumName(AvatarDataType_name, int32(x))
}
func (AvatarDataType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{0}
}

type AvatarType int32

const (
	AvatarType_AVATAR_TYPE_UNSPECIFIED AvatarType = 0
	AvatarType_AVATAR_TYPE_STATIC      AvatarType = 1
	AvatarType_AVATAR_TYPE_DYNAMIC     AvatarType = 2
)

var AvatarType_name = map[int32]string{
	0: "AVATAR_TYPE_UNSPECIFIED",
	1: "AVATAR_TYPE_STATIC",
	2: "AVATAR_TYPE_DYNAMIC",
}
var AvatarType_value = map[string]int32{
	"AVATAR_TYPE_UNSPECIFIED": 0,
	"AVATAR_TYPE_STATIC":      1,
	"AVATAR_TYPE_DYNAMIC":     2,
}

func (x AvatarType) String() string {
	return proto.EnumName(AvatarType_name, int32(x))
}
func (AvatarType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{1}
}

// 头像上传来源
type UplaodSource int32

const (
	UplaodSource_UPLOAD_SOURCE_DEFAULT UplaodSource = 0
	// 命令号上传
	UplaodSource_UPLOAD_SOURCE_CMD UplaodSource = 1
	// HTTP接口上传
	UplaodSource_UPLOAD_SOURCE_HTTP UplaodSource = 2
	// 第三方qq/wx头像同步
	UplaodSource_UPLOAD_SOURCE_THIRD_PARTY_REG UplaodSource = 3
	// 主播开播头像送审
	UplaodSource_UPLOAD_SOURCE_ANCHOR_LIVE_BEGIN UplaodSource = 4
	// 沉默用户头像送审
	UplaodSource_UPLOAD_SOURCE_SILENT_USER UplaodSource = 5
)

var UplaodSource_name = map[int32]string{
	0: "UPLOAD_SOURCE_DEFAULT",
	1: "UPLOAD_SOURCE_CMD",
	2: "UPLOAD_SOURCE_HTTP",
	3: "UPLOAD_SOURCE_THIRD_PARTY_REG",
	4: "UPLOAD_SOURCE_ANCHOR_LIVE_BEGIN",
	5: "UPLOAD_SOURCE_SILENT_USER",
}
var UplaodSource_value = map[string]int32{
	"UPLOAD_SOURCE_DEFAULT":           0,
	"UPLOAD_SOURCE_CMD":               1,
	"UPLOAD_SOURCE_HTTP":              2,
	"UPLOAD_SOURCE_THIRD_PARTY_REG":   3,
	"UPLOAD_SOURCE_ANCHOR_LIVE_BEGIN": 4,
	"UPLOAD_SOURCE_SILENT_USER":       5,
}

func (x UplaodSource) String() string {
	return proto.EnumName(UplaodSource_name, int32(x))
}
func (UplaodSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{2}
}

// 实体类型
type EntityType int32

const (
	EntityType_ENTITY_TYPE_UNSPECIFIED EntityType = 0
	// 用户头像，id 填 username
	EntityType_ENTITY_TYPE_USER EntityType = 1
	// 工会头像，id 填guildid
	EntityType_ENTITY_TYPE_GUILD EntityType = 2
	// 群头像，id 填groupid
	EntityType_ENTITY_TYPE_GROUP EntityType = 3
	// 游戏头像，id 填gameid
	EntityType_ENTITY_TYPE_GAME EntityType = 4
	// 公会群头像，id 填groupid
	EntityType_ENTITY_TYPE_GUILD_GROUP EntityType = 5
	// 游戏群头像，id 填groupid
	EntityType_ENTITY_TYPE_GAME_GROUP EntityType = 6
	// 公众号头像，id 填publicid
	EntityType_ENTITY_TYPE_PUBLIC_ACCOUNT EntityType = 7
	// 临时群头像，id 填groupid
	EntityType_ENTITY_TYPE_TGROUP EntityType = 8
	// 房间头像，id 填channelid
	EntityType_ENTITY_TYPE_CHANNEL EntityType = 9
	// 社团群头像，id 填groupid
	EntityType_ENTITY_TYPE_CGROUP EntityType = 10
)

var EntityType_name = map[int32]string{
	0:  "ENTITY_TYPE_UNSPECIFIED",
	1:  "ENTITY_TYPE_USER",
	2:  "ENTITY_TYPE_GUILD",
	3:  "ENTITY_TYPE_GROUP",
	4:  "ENTITY_TYPE_GAME",
	5:  "ENTITY_TYPE_GUILD_GROUP",
	6:  "ENTITY_TYPE_GAME_GROUP",
	7:  "ENTITY_TYPE_PUBLIC_ACCOUNT",
	8:  "ENTITY_TYPE_TGROUP",
	9:  "ENTITY_TYPE_CHANNEL",
	10: "ENTITY_TYPE_CGROUP",
}
var EntityType_value = map[string]int32{
	"ENTITY_TYPE_UNSPECIFIED":    0,
	"ENTITY_TYPE_USER":           1,
	"ENTITY_TYPE_GUILD":          2,
	"ENTITY_TYPE_GROUP":          3,
	"ENTITY_TYPE_GAME":           4,
	"ENTITY_TYPE_GUILD_GROUP":    5,
	"ENTITY_TYPE_GAME_GROUP":     6,
	"ENTITY_TYPE_PUBLIC_ACCOUNT": 7,
	"ENTITY_TYPE_TGROUP":         8,
	"ENTITY_TYPE_CHANNEL":        9,
	"ENTITY_TYPE_CGROUP":         10,
}

func (x EntityType) String() string {
	return proto.EnumName(EntityType_name, int32(x))
}
func (EntityType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{3}
}

type Entity struct {
	Type                 EntityType `protobuf:"varint,1,opt,name=type,proto3,enum=avatar_mng_api.EntityType" json:"type,omitempty"`
	Id                   string     `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *Entity) Reset()         { *m = Entity{} }
func (m *Entity) String() string { return proto.CompactTextString(m) }
func (*Entity) ProtoMessage()    {}
func (*Entity) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{0}
}
func (m *Entity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Entity.Unmarshal(m, b)
}
func (m *Entity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Entity.Marshal(b, m, deterministic)
}
func (dst *Entity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Entity.Merge(dst, src)
}
func (m *Entity) XXX_Size() int {
	return xxx_messageInfo_Entity.Size(m)
}
func (m *Entity) XXX_DiscardUnknown() {
	xxx_messageInfo_Entity.DiscardUnknown(m)
}

var xxx_messageInfo_Entity proto.InternalMessageInfo

func (m *Entity) GetType() EntityType {
	if m != nil {
		return m.Type
	}
	return EntityType_ENTITY_TYPE_UNSPECIFIED
}

func (m *Entity) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type ContextInfo struct {
	OpUid    uint32 `protobuf:"varint,1,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	MarketId uint32 `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	// 16进制的设备id
	DeviceIdHex          []byte   `protobuf:"bytes,3,opt,name=device_id_hex,json=deviceIdHex,proto3" json:"device_id_hex,omitempty"`
	ClientIp             string   `protobuf:"bytes,4,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	TerminalType         uint32   `protobuf:"varint,5,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContextInfo) Reset()         { *m = ContextInfo{} }
func (m *ContextInfo) String() string { return proto.CompactTextString(m) }
func (*ContextInfo) ProtoMessage()    {}
func (*ContextInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{1}
}
func (m *ContextInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContextInfo.Unmarshal(m, b)
}
func (m *ContextInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContextInfo.Marshal(b, m, deterministic)
}
func (dst *ContextInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContextInfo.Merge(dst, src)
}
func (m *ContextInfo) XXX_Size() int {
	return xxx_messageInfo_ContextInfo.Size(m)
}
func (m *ContextInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ContextInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ContextInfo proto.InternalMessageInfo

func (m *ContextInfo) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *ContextInfo) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *ContextInfo) GetDeviceIdHex() []byte {
	if m != nil {
		return m.DeviceIdHex
	}
	return nil
}

func (m *ContextInfo) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *ContextInfo) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

type AvatarVersion struct {
	Entity               *Entity  `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	Version              string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AvatarVersion) Reset()         { *m = AvatarVersion{} }
func (m *AvatarVersion) String() string { return proto.CompactTextString(m) }
func (*AvatarVersion) ProtoMessage()    {}
func (*AvatarVersion) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{2}
}
func (m *AvatarVersion) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AvatarVersion.Unmarshal(m, b)
}
func (m *AvatarVersion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AvatarVersion.Marshal(b, m, deterministic)
}
func (dst *AvatarVersion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AvatarVersion.Merge(dst, src)
}
func (m *AvatarVersion) XXX_Size() int {
	return xxx_messageInfo_AvatarVersion.Size(m)
}
func (m *AvatarVersion) XXX_DiscardUnknown() {
	xxx_messageInfo_AvatarVersion.DiscardUnknown(m)
}

var xxx_messageInfo_AvatarVersion proto.InternalMessageInfo

func (m *AvatarVersion) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *AvatarVersion) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type BatchGetAvatarVersionReq struct {
	AvatarType           AvatarType `protobuf:"varint,1,opt,name=avatar_type,json=avatarType,proto3,enum=avatar_mng_api.AvatarType" json:"avatar_type,omitempty"`
	EntityList           []*Entity  `protobuf:"bytes,2,rep,name=entity_list,json=entityList,proto3" json:"entity_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BatchGetAvatarVersionReq) Reset()         { *m = BatchGetAvatarVersionReq{} }
func (m *BatchGetAvatarVersionReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetAvatarVersionReq) ProtoMessage()    {}
func (*BatchGetAvatarVersionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{3}
}
func (m *BatchGetAvatarVersionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAvatarVersionReq.Unmarshal(m, b)
}
func (m *BatchGetAvatarVersionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAvatarVersionReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetAvatarVersionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAvatarVersionReq.Merge(dst, src)
}
func (m *BatchGetAvatarVersionReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetAvatarVersionReq.Size(m)
}
func (m *BatchGetAvatarVersionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAvatarVersionReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAvatarVersionReq proto.InternalMessageInfo

func (m *BatchGetAvatarVersionReq) GetAvatarType() AvatarType {
	if m != nil {
		return m.AvatarType
	}
	return AvatarType_AVATAR_TYPE_UNSPECIFIED
}

func (m *BatchGetAvatarVersionReq) GetEntityList() []*Entity {
	if m != nil {
		return m.EntityList
	}
	return nil
}

type BatchGetAvatarVersionResp struct {
	AvatarVersionList    []*AvatarVersion `protobuf:"bytes,1,rep,name=avatar_version_list,json=avatarVersionList,proto3" json:"avatar_version_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchGetAvatarVersionResp) Reset()         { *m = BatchGetAvatarVersionResp{} }
func (m *BatchGetAvatarVersionResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetAvatarVersionResp) ProtoMessage()    {}
func (*BatchGetAvatarVersionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{4}
}
func (m *BatchGetAvatarVersionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAvatarVersionResp.Unmarshal(m, b)
}
func (m *BatchGetAvatarVersionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAvatarVersionResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetAvatarVersionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAvatarVersionResp.Merge(dst, src)
}
func (m *BatchGetAvatarVersionResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetAvatarVersionResp.Size(m)
}
func (m *BatchGetAvatarVersionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAvatarVersionResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAvatarVersionResp proto.InternalMessageInfo

func (m *BatchGetAvatarVersionResp) GetAvatarVersionList() []*AvatarVersion {
	if m != nil {
		return m.AvatarVersionList
	}
	return nil
}

// 获取头像文件
type GetAvatarReq struct {
	AvatarType           AvatarType     `protobuf:"varint,1,opt,name=avatar_type,json=avatarType,proto3,enum=avatar_mng_api.AvatarType" json:"avatar_type,omitempty"`
	AvatarDataType       AvatarDataType `protobuf:"varint,2,opt,name=avatar_data_type,json=avatarDataType,proto3,enum=avatar_mng_api.AvatarDataType" json:"avatar_data_type,omitempty"`
	Entity               *Entity        `protobuf:"bytes,3,opt,name=entity,proto3" json:"entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetAvatarReq) Reset()         { *m = GetAvatarReq{} }
func (m *GetAvatarReq) String() string { return proto.CompactTextString(m) }
func (*GetAvatarReq) ProtoMessage()    {}
func (*GetAvatarReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{5}
}
func (m *GetAvatarReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAvatarReq.Unmarshal(m, b)
}
func (m *GetAvatarReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAvatarReq.Marshal(b, m, deterministic)
}
func (dst *GetAvatarReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAvatarReq.Merge(dst, src)
}
func (m *GetAvatarReq) XXX_Size() int {
	return xxx_messageInfo_GetAvatarReq.Size(m)
}
func (m *GetAvatarReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAvatarReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAvatarReq proto.InternalMessageInfo

func (m *GetAvatarReq) GetAvatarType() AvatarType {
	if m != nil {
		return m.AvatarType
	}
	return AvatarType_AVATAR_TYPE_UNSPECIFIED
}

func (m *GetAvatarReq) GetAvatarDataType() AvatarDataType {
	if m != nil {
		return m.AvatarDataType
	}
	return AvatarDataType_AVATAR_DATA_TYPE_UNSPECIFIED
}

func (m *GetAvatarReq) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

type GetAvatarResp struct {
	ImageData            []byte   `protobuf:"bytes,1,opt,name=image_data,json=imageData,proto3" json:"image_data,omitempty"`
	Version              string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAvatarResp) Reset()         { *m = GetAvatarResp{} }
func (m *GetAvatarResp) String() string { return proto.CompactTextString(m) }
func (*GetAvatarResp) ProtoMessage()    {}
func (*GetAvatarResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{6}
}
func (m *GetAvatarResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAvatarResp.Unmarshal(m, b)
}
func (m *GetAvatarResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAvatarResp.Marshal(b, m, deterministic)
}
func (dst *GetAvatarResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAvatarResp.Merge(dst, src)
}
func (m *GetAvatarResp) XXX_Size() int {
	return xxx_messageInfo_GetAvatarResp.Size(m)
}
func (m *GetAvatarResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAvatarResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAvatarResp proto.InternalMessageInfo

func (m *GetAvatarResp) GetImageData() []byte {
	if m != nil {
		return m.ImageData
	}
	return nil
}

func (m *GetAvatarResp) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

// 上传头像
type UploadAvatarReq struct {
	Entity               *Entity      `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	ImageData            []byte       `protobuf:"bytes,2,opt,name=image_data,json=imageData,proto3" json:"image_data,omitempty"`
	IsNeedAudit          bool         `protobuf:"varint,3,opt,name=is_need_audit,json=isNeedAudit,proto3" json:"is_need_audit,omitempty"`
	VersionPrefix        string       `protobuf:"bytes,4,opt,name=version_prefix,json=versionPrefix,proto3" json:"version_prefix,omitempty"`
	UploadSource         UplaodSource `protobuf:"varint,5,opt,name=upload_source,json=uploadSource,proto3,enum=avatar_mng_api.UplaodSource" json:"upload_source,omitempty"`
	ContextInfo          *ContextInfo `protobuf:"bytes,6,opt,name=context_info,json=contextInfo,proto3" json:"context_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UploadAvatarReq) Reset()         { *m = UploadAvatarReq{} }
func (m *UploadAvatarReq) String() string { return proto.CompactTextString(m) }
func (*UploadAvatarReq) ProtoMessage()    {}
func (*UploadAvatarReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{7}
}
func (m *UploadAvatarReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UploadAvatarReq.Unmarshal(m, b)
}
func (m *UploadAvatarReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UploadAvatarReq.Marshal(b, m, deterministic)
}
func (dst *UploadAvatarReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadAvatarReq.Merge(dst, src)
}
func (m *UploadAvatarReq) XXX_Size() int {
	return xxx_messageInfo_UploadAvatarReq.Size(m)
}
func (m *UploadAvatarReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadAvatarReq.DiscardUnknown(m)
}

var xxx_messageInfo_UploadAvatarReq proto.InternalMessageInfo

func (m *UploadAvatarReq) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *UploadAvatarReq) GetImageData() []byte {
	if m != nil {
		return m.ImageData
	}
	return nil
}

func (m *UploadAvatarReq) GetIsNeedAudit() bool {
	if m != nil {
		return m.IsNeedAudit
	}
	return false
}

func (m *UploadAvatarReq) GetVersionPrefix() string {
	if m != nil {
		return m.VersionPrefix
	}
	return ""
}

func (m *UploadAvatarReq) GetUploadSource() UplaodSource {
	if m != nil {
		return m.UploadSource
	}
	return UplaodSource_UPLOAD_SOURCE_DEFAULT
}

func (m *UploadAvatarReq) GetContextInfo() *ContextInfo {
	if m != nil {
		return m.ContextInfo
	}
	return nil
}

type UploadAvatarResp struct {
	AvatarVersion        string   `protobuf:"bytes,1,opt,name=avatar_version,json=avatarVersion,proto3" json:"avatar_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UploadAvatarResp) Reset()         { *m = UploadAvatarResp{} }
func (m *UploadAvatarResp) String() string { return proto.CompactTextString(m) }
func (*UploadAvatarResp) ProtoMessage()    {}
func (*UploadAvatarResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{8}
}
func (m *UploadAvatarResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UploadAvatarResp.Unmarshal(m, b)
}
func (m *UploadAvatarResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UploadAvatarResp.Marshal(b, m, deterministic)
}
func (dst *UploadAvatarResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadAvatarResp.Merge(dst, src)
}
func (m *UploadAvatarResp) XXX_Size() int {
	return xxx_messageInfo_UploadAvatarResp.Size(m)
}
func (m *UploadAvatarResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadAvatarResp.DiscardUnknown(m)
}

var xxx_messageInfo_UploadAvatarResp proto.InternalMessageInfo

func (m *UploadAvatarResp) GetAvatarVersion() string {
	if m != nil {
		return m.AvatarVersion
	}
	return ""
}

type RegUploadAvatarReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	ImageData            []byte   `protobuf:"bytes,2,opt,name=image_data,json=imageData,proto3" json:"image_data,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	SmDeviceId           string   `protobuf:"bytes,4,opt,name=sm_device_id,json=smDeviceId,proto3" json:"sm_device_id,omitempty"`
	SourceAccount        string   `protobuf:"bytes,5,opt,name=source_account,json=sourceAccount,proto3" json:"source_account,omitempty"`
	SourceId             uint32   `protobuf:"varint,6,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	Ip                   string   `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegUploadAvatarReq) Reset()         { *m = RegUploadAvatarReq{} }
func (m *RegUploadAvatarReq) String() string { return proto.CompactTextString(m) }
func (*RegUploadAvatarReq) ProtoMessage()    {}
func (*RegUploadAvatarReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{9}
}
func (m *RegUploadAvatarReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegUploadAvatarReq.Unmarshal(m, b)
}
func (m *RegUploadAvatarReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegUploadAvatarReq.Marshal(b, m, deterministic)
}
func (dst *RegUploadAvatarReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegUploadAvatarReq.Merge(dst, src)
}
func (m *RegUploadAvatarReq) XXX_Size() int {
	return xxx_messageInfo_RegUploadAvatarReq.Size(m)
}
func (m *RegUploadAvatarReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RegUploadAvatarReq.DiscardUnknown(m)
}

var xxx_messageInfo_RegUploadAvatarReq proto.InternalMessageInfo

func (m *RegUploadAvatarReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *RegUploadAvatarReq) GetImageData() []byte {
	if m != nil {
		return m.ImageData
	}
	return nil
}

func (m *RegUploadAvatarReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RegUploadAvatarReq) GetSmDeviceId() string {
	if m != nil {
		return m.SmDeviceId
	}
	return ""
}

func (m *RegUploadAvatarReq) GetSourceAccount() string {
	if m != nil {
		return m.SourceAccount
	}
	return ""
}

func (m *RegUploadAvatarReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *RegUploadAvatarReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

type RegUploadAvatarResp struct {
	Version              string   `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegUploadAvatarResp) Reset()         { *m = RegUploadAvatarResp{} }
func (m *RegUploadAvatarResp) String() string { return proto.CompactTextString(m) }
func (*RegUploadAvatarResp) ProtoMessage()    {}
func (*RegUploadAvatarResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{10}
}
func (m *RegUploadAvatarResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegUploadAvatarResp.Unmarshal(m, b)
}
func (m *RegUploadAvatarResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegUploadAvatarResp.Marshal(b, m, deterministic)
}
func (dst *RegUploadAvatarResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegUploadAvatarResp.Merge(dst, src)
}
func (m *RegUploadAvatarResp) XXX_Size() int {
	return xxx_messageInfo_RegUploadAvatarResp.Size(m)
}
func (m *RegUploadAvatarResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RegUploadAvatarResp.DiscardUnknown(m)
}

var xxx_messageInfo_RegUploadAvatarResp proto.InternalMessageInfo

func (m *RegUploadAvatarResp) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

// 拼接头像
type UploadJoinAvatarReq struct {
	MemberAccountList    []string `protobuf:"bytes,1,rep,name=member_account_list,json=memberAccountList,proto3" json:"member_account_list,omitempty"`
	VersionPrefix        string   `protobuf:"bytes,2,opt,name=version_prefix,json=versionPrefix,proto3" json:"version_prefix,omitempty"`
	Entity               *Entity  `protobuf:"bytes,3,opt,name=entity,proto3" json:"entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UploadJoinAvatarReq) Reset()         { *m = UploadJoinAvatarReq{} }
func (m *UploadJoinAvatarReq) String() string { return proto.CompactTextString(m) }
func (*UploadJoinAvatarReq) ProtoMessage()    {}
func (*UploadJoinAvatarReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{11}
}
func (m *UploadJoinAvatarReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UploadJoinAvatarReq.Unmarshal(m, b)
}
func (m *UploadJoinAvatarReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UploadJoinAvatarReq.Marshal(b, m, deterministic)
}
func (dst *UploadJoinAvatarReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadJoinAvatarReq.Merge(dst, src)
}
func (m *UploadJoinAvatarReq) XXX_Size() int {
	return xxx_messageInfo_UploadJoinAvatarReq.Size(m)
}
func (m *UploadJoinAvatarReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadJoinAvatarReq.DiscardUnknown(m)
}

var xxx_messageInfo_UploadJoinAvatarReq proto.InternalMessageInfo

func (m *UploadJoinAvatarReq) GetMemberAccountList() []string {
	if m != nil {
		return m.MemberAccountList
	}
	return nil
}

func (m *UploadJoinAvatarReq) GetVersionPrefix() string {
	if m != nil {
		return m.VersionPrefix
	}
	return ""
}

func (m *UploadJoinAvatarReq) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

type UploadJoinAvatarResp struct {
	AvatarVersion        string   `protobuf:"bytes,1,opt,name=avatar_version,json=avatarVersion,proto3" json:"avatar_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UploadJoinAvatarResp) Reset()         { *m = UploadJoinAvatarResp{} }
func (m *UploadJoinAvatarResp) String() string { return proto.CompactTextString(m) }
func (*UploadJoinAvatarResp) ProtoMessage()    {}
func (*UploadJoinAvatarResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_mng_api_07050dbf02d62f3b, []int{12}
}
func (m *UploadJoinAvatarResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UploadJoinAvatarResp.Unmarshal(m, b)
}
func (m *UploadJoinAvatarResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UploadJoinAvatarResp.Marshal(b, m, deterministic)
}
func (dst *UploadJoinAvatarResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadJoinAvatarResp.Merge(dst, src)
}
func (m *UploadJoinAvatarResp) XXX_Size() int {
	return xxx_messageInfo_UploadJoinAvatarResp.Size(m)
}
func (m *UploadJoinAvatarResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadJoinAvatarResp.DiscardUnknown(m)
}

var xxx_messageInfo_UploadJoinAvatarResp proto.InternalMessageInfo

func (m *UploadJoinAvatarResp) GetAvatarVersion() string {
	if m != nil {
		return m.AvatarVersion
	}
	return ""
}

func init() {
	proto.RegisterType((*Entity)(nil), "avatar_mng_api.Entity")
	proto.RegisterType((*ContextInfo)(nil), "avatar_mng_api.ContextInfo")
	proto.RegisterType((*AvatarVersion)(nil), "avatar_mng_api.AvatarVersion")
	proto.RegisterType((*BatchGetAvatarVersionReq)(nil), "avatar_mng_api.BatchGetAvatarVersionReq")
	proto.RegisterType((*BatchGetAvatarVersionResp)(nil), "avatar_mng_api.BatchGetAvatarVersionResp")
	proto.RegisterType((*GetAvatarReq)(nil), "avatar_mng_api.GetAvatarReq")
	proto.RegisterType((*GetAvatarResp)(nil), "avatar_mng_api.GetAvatarResp")
	proto.RegisterType((*UploadAvatarReq)(nil), "avatar_mng_api.UploadAvatarReq")
	proto.RegisterType((*UploadAvatarResp)(nil), "avatar_mng_api.UploadAvatarResp")
	proto.RegisterType((*RegUploadAvatarReq)(nil), "avatar_mng_api.RegUploadAvatarReq")
	proto.RegisterType((*RegUploadAvatarResp)(nil), "avatar_mng_api.RegUploadAvatarResp")
	proto.RegisterType((*UploadJoinAvatarReq)(nil), "avatar_mng_api.UploadJoinAvatarReq")
	proto.RegisterType((*UploadJoinAvatarResp)(nil), "avatar_mng_api.UploadJoinAvatarResp")
	proto.RegisterEnum("avatar_mng_api.AvatarDataType", AvatarDataType_name, AvatarDataType_value)
	proto.RegisterEnum("avatar_mng_api.AvatarType", AvatarType_name, AvatarType_value)
	proto.RegisterEnum("avatar_mng_api.UplaodSource", UplaodSource_name, UplaodSource_value)
	proto.RegisterEnum("avatar_mng_api.EntityType", EntityType_name, EntityType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AvatarMngApiClient is the client API for AvatarMngApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AvatarMngApiClient interface {
	// 获取头像文件
	GetAvatar(ctx context.Context, in *GetAvatarReq, opts ...grpc.CallOption) (*GetAvatarResp, error)
	// 批量获取头像版本号
	BatchGetAvatarVersion(ctx context.Context, in *BatchGetAvatarVersionReq, opts ...grpc.CallOption) (*BatchGetAvatarVersionResp, error)
	// 上传头像
	UploadAvatar(ctx context.Context, in *UploadAvatarReq, opts ...grpc.CallOption) (*UploadAvatarResp, error)
	// 上传拼接头像
	UploadJoinAvatar(ctx context.Context, in *UploadJoinAvatarReq, opts ...grpc.CallOption) (*UploadJoinAvatarResp, error)
	// 注册时上传头像
	RegUploadAvatar(ctx context.Context, in *RegUploadAvatarReq, opts ...grpc.CallOption) (*RegUploadAvatarResp, error)
}

type avatarMngApiClient struct {
	cc *grpc.ClientConn
}

func NewAvatarMngApiClient(cc *grpc.ClientConn) AvatarMngApiClient {
	return &avatarMngApiClient{cc}
}

func (c *avatarMngApiClient) GetAvatar(ctx context.Context, in *GetAvatarReq, opts ...grpc.CallOption) (*GetAvatarResp, error) {
	out := new(GetAvatarResp)
	err := c.cc.Invoke(ctx, "/avatar_mng_api.AvatarMngApi/GetAvatar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *avatarMngApiClient) BatchGetAvatarVersion(ctx context.Context, in *BatchGetAvatarVersionReq, opts ...grpc.CallOption) (*BatchGetAvatarVersionResp, error) {
	out := new(BatchGetAvatarVersionResp)
	err := c.cc.Invoke(ctx, "/avatar_mng_api.AvatarMngApi/BatchGetAvatarVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *avatarMngApiClient) UploadAvatar(ctx context.Context, in *UploadAvatarReq, opts ...grpc.CallOption) (*UploadAvatarResp, error) {
	out := new(UploadAvatarResp)
	err := c.cc.Invoke(ctx, "/avatar_mng_api.AvatarMngApi/UploadAvatar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *avatarMngApiClient) UploadJoinAvatar(ctx context.Context, in *UploadJoinAvatarReq, opts ...grpc.CallOption) (*UploadJoinAvatarResp, error) {
	out := new(UploadJoinAvatarResp)
	err := c.cc.Invoke(ctx, "/avatar_mng_api.AvatarMngApi/UploadJoinAvatar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *avatarMngApiClient) RegUploadAvatar(ctx context.Context, in *RegUploadAvatarReq, opts ...grpc.CallOption) (*RegUploadAvatarResp, error) {
	out := new(RegUploadAvatarResp)
	err := c.cc.Invoke(ctx, "/avatar_mng_api.AvatarMngApi/RegUploadAvatar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AvatarMngApiServer is the server API for AvatarMngApi service.
type AvatarMngApiServer interface {
	// 获取头像文件
	GetAvatar(context.Context, *GetAvatarReq) (*GetAvatarResp, error)
	// 批量获取头像版本号
	BatchGetAvatarVersion(context.Context, *BatchGetAvatarVersionReq) (*BatchGetAvatarVersionResp, error)
	// 上传头像
	UploadAvatar(context.Context, *UploadAvatarReq) (*UploadAvatarResp, error)
	// 上传拼接头像
	UploadJoinAvatar(context.Context, *UploadJoinAvatarReq) (*UploadJoinAvatarResp, error)
	// 注册时上传头像
	RegUploadAvatar(context.Context, *RegUploadAvatarReq) (*RegUploadAvatarResp, error)
}

func RegisterAvatarMngApiServer(s *grpc.Server, srv AvatarMngApiServer) {
	s.RegisterService(&_AvatarMngApi_serviceDesc, srv)
}

func _AvatarMngApi_GetAvatar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvatarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarMngApiServer).GetAvatar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar_mng_api.AvatarMngApi/GetAvatar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarMngApiServer).GetAvatar(ctx, req.(*GetAvatarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AvatarMngApi_BatchGetAvatarVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAvatarVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarMngApiServer).BatchGetAvatarVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar_mng_api.AvatarMngApi/BatchGetAvatarVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarMngApiServer).BatchGetAvatarVersion(ctx, req.(*BatchGetAvatarVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AvatarMngApi_UploadAvatar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadAvatarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarMngApiServer).UploadAvatar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar_mng_api.AvatarMngApi/UploadAvatar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarMngApiServer).UploadAvatar(ctx, req.(*UploadAvatarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AvatarMngApi_UploadJoinAvatar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadJoinAvatarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarMngApiServer).UploadJoinAvatar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar_mng_api.AvatarMngApi/UploadJoinAvatar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarMngApiServer).UploadJoinAvatar(ctx, req.(*UploadJoinAvatarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AvatarMngApi_RegUploadAvatar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegUploadAvatarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarMngApiServer).RegUploadAvatar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar_mng_api.AvatarMngApi/RegUploadAvatar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarMngApiServer).RegUploadAvatar(ctx, req.(*RegUploadAvatarReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AvatarMngApi_serviceDesc = grpc.ServiceDesc{
	ServiceName: "avatar_mng_api.AvatarMngApi",
	HandlerType: (*AvatarMngApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAvatar",
			Handler:    _AvatarMngApi_GetAvatar_Handler,
		},
		{
			MethodName: "BatchGetAvatarVersion",
			Handler:    _AvatarMngApi_BatchGetAvatarVersion_Handler,
		},
		{
			MethodName: "UploadAvatar",
			Handler:    _AvatarMngApi_UploadAvatar_Handler,
		},
		{
			MethodName: "UploadJoinAvatar",
			Handler:    _AvatarMngApi_UploadJoinAvatar_Handler,
		},
		{
			MethodName: "RegUploadAvatar",
			Handler:    _AvatarMngApi_RegUploadAvatar_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/avatar-mng-api/avatar-mng-api.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/avatar-mng-api/avatar-mng-api.proto", fileDescriptor_avatar_mng_api_07050dbf02d62f3b)
}

var fileDescriptor_avatar_mng_api_07050dbf02d62f3b = []byte{
	// 1172 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x56, 0xdb, 0x6e, 0xdb, 0x46,
	0x13, 0x0e, 0x29, 0x5b, 0xb6, 0x46, 0x87, 0x30, 0xeb, 0x38, 0x51, 0xe4, 0x38, 0xd1, 0x4f, 0xff,
	0x05, 0x5c, 0x03, 0x91, 0x0b, 0x15, 0x45, 0x51, 0x14, 0x2d, 0x40, 0x53, 0x8c, 0xc4, 0x40, 0x92,
	0x05, 0x8a, 0x34, 0xe0, 0xa0, 0xc0, 0x82, 0x11, 0xd7, 0xca, 0x36, 0x12, 0xc9, 0x88, 0x94, 0x61,
	0xbf, 0x45, 0x1f, 0xa0, 0xb7, 0x45, 0xef, 0xfa, 0x0a, 0xbd, 0xea, 0x23, 0xf4, 0xb6, 0xcf, 0x52,
	0x70, 0x97, 0xb2, 0x48, 0x4a, 0xb1, 0x1d, 0xf4, 0x8e, 0xfb, 0xcd, 0x37, 0xc7, 0x9d, 0x99, 0x25,
	0x34, 0xc3, 0xf0, 0xf8, 0xe3, 0x9c, 0x8e, 0x3e, 0x04, 0x74, 0x72, 0x49, 0x66, 0xc7, 0xf6, 0xa5,
	0x1d, 0xda, 0xb3, 0x57, 0x53, 0x77, 0xfc, 0xca, 0xf6, 0x69, 0xe6, 0xd8, 0xf0, 0x67, 0x5e, 0xe8,
	0xa1, 0x0a, 0x47, 0xf1, 0xd4, 0x1d, 0x63, 0xdb, 0xa7, 0x72, 0x07, 0xf2, 0x9a, 0x1b, 0xd2, 0xf0,
	0x1a, 0x35, 0x60, 0x23, 0xbc, 0xf6, 0x49, 0x55, 0xa8, 0x0b, 0x87, 0x95, 0x66, 0xad, 0x91, 0x26,
	0x36, 0x38, 0xcb, 0xbc, 0xf6, 0x89, 0xc1, 0x78, 0xa8, 0x02, 0x22, 0x75, 0xaa, 0x62, 0x5d, 0x38,
	0x2c, 0x18, 0x22, 0x75, 0xe4, 0xdf, 0x05, 0x28, 0xaa, 0x9e, 0x1b, 0x92, 0xab, 0x50, 0x77, 0x2f,
	0x3c, 0xb4, 0x0b, 0x79, 0xcf, 0xc7, 0x73, 0xea, 0x30, 0x8b, 0x65, 0x63, 0xd3, 0xf3, 0x2d, 0xea,
	0xa0, 0x3d, 0x28, 0x4c, 0xed, 0xd9, 0x07, 0x12, 0xe2, 0x58, 0xbb, 0x6c, 0x6c, 0x73, 0x40, 0x77,
	0x90, 0x0c, 0x65, 0x87, 0x5c, 0xd2, 0x11, 0xc1, 0xd4, 0xc1, 0xef, 0xc9, 0x55, 0x35, 0x57, 0x17,
	0x0e, 0x4b, 0x46, 0x91, 0x83, 0xba, 0xd3, 0x21, 0x57, 0x91, 0x81, 0xd1, 0x84, 0x12, 0x37, 0xc4,
	0xd4, 0xaf, 0x6e, 0x30, 0xf7, 0xdb, 0x1c, 0xd0, 0x7d, 0x74, 0x00, 0xe5, 0x90, 0xcc, 0xa6, 0xd4,
	0xb5, 0x27, 0x98, 0x65, 0xb3, 0xc9, 0x3c, 0x94, 0x16, 0x60, 0x14, 0xbf, 0x7c, 0x0e, 0x65, 0x85,
	0x25, 0x77, 0x46, 0x66, 0x01, 0xf5, 0x5c, 0xd4, 0x80, 0x3c, 0x61, 0xe9, 0xb1, 0x50, 0x8b, 0xcd,
	0x27, 0xeb, 0x93, 0x37, 0x62, 0x16, 0xaa, 0xc2, 0xd6, 0x25, 0x57, 0x8d, 0xf3, 0x5f, 0x1c, 0xe5,
	0x5f, 0x04, 0xa8, 0x9e, 0xd8, 0xe1, 0xe8, 0x7d, 0x9b, 0x84, 0x29, 0x1f, 0x06, 0xf9, 0x88, 0xbe,
	0x87, 0x62, 0x6c, 0xf7, 0xb6, 0x42, 0x73, 0x35, 0x56, 0x68, 0xb0, 0x6f, 0xbe, 0xd1, 0xb7, 0x50,
	0xe4, 0xde, 0xf1, 0x84, 0x06, 0x61, 0x55, 0xac, 0xe7, 0x6e, 0x09, 0x14, 0x38, 0xb5, 0x4b, 0x83,
	0x50, 0xfe, 0x19, 0x9e, 0x7d, 0x22, 0xa2, 0xc0, 0x47, 0x3d, 0xd8, 0x89, 0x2d, 0xc4, 0x19, 0x70,
	0xeb, 0x02, 0xb3, 0xbe, 0xbf, 0x3e, 0xb4, 0x85, 0xfe, 0x23, 0x3b, 0x79, 0x64, 0xbe, 0xfe, 0x12,
	0xa0, 0x74, 0xe3, 0xe7, 0x3f, 0xa7, 0xdc, 0x01, 0x29, 0x26, 0x3a, 0x76, 0x68, 0x73, 0x0b, 0x22,
	0xb3, 0xf0, 0x62, 0xbd, 0x85, 0x96, 0x1d, 0xda, 0xcc, 0x4a, 0xdc, 0xe5, 0x8b, 0x73, 0xe2, 0x82,
	0x73, 0xf7, 0xb9, 0x60, 0xb9, 0x03, 0xe5, 0x44, 0x1a, 0x81, 0x8f, 0xf6, 0x01, 0xe8, 0xd4, 0x1e,
	0x13, 0x16, 0x09, 0x4b, 0xa3, 0x64, 0x14, 0x18, 0x12, 0xf9, 0xb8, 0xa5, 0x21, 0xfe, 0x10, 0xe1,
	0xa1, 0xe5, 0x4f, 0x3c, 0xdb, 0x59, 0x16, 0xe5, 0x73, 0xdb, 0x2d, 0xed, 0x5c, 0xcc, 0x3a, 0x97,
	0xa1, 0x4c, 0x03, 0xec, 0x12, 0xe2, 0x60, 0x7b, 0xee, 0xd0, 0x90, 0xe5, 0xb8, 0x6d, 0x14, 0x69,
	0xd0, 0x27, 0xc4, 0x51, 0x22, 0x08, 0x7d, 0x01, 0x95, 0xc5, 0x05, 0xfb, 0x33, 0x72, 0x41, 0xaf,
	0xe2, 0xc9, 0x29, 0xc7, 0xe8, 0x80, 0x81, 0x48, 0x81, 0xf2, 0x9c, 0x05, 0x8b, 0x03, 0x6f, 0x3e,
	0x1b, 0xf1, 0xf1, 0xa9, 0x34, 0x9f, 0x67, 0x03, 0xb4, 0xfc, 0x89, 0xed, 0x39, 0x43, 0xc6, 0x31,
	0x4a, 0x5c, 0x85, 0x9f, 0xd0, 0x8f, 0x50, 0x1a, 0xf1, 0x2d, 0x80, 0xa9, 0x7b, 0xe1, 0x55, 0xf3,
	0x2c, 0xc5, 0xbd, 0xac, 0x85, 0xc4, 0xa6, 0x30, 0x8a, 0xa3, 0xe5, 0x41, 0xfe, 0x0e, 0xa4, 0x74,
	0xbd, 0x02, 0x3f, 0x8a, 0x3e, 0xdd, 0xa5, 0xac, 0x70, 0x05, 0xa3, 0x9c, 0xea, 0x40, 0xf9, 0x6f,
	0x01, 0x90, 0x41, 0xc6, 0xd9, 0x72, 0x57, 0x61, 0xcb, 0x1e, 0x8d, 0xbc, 0xb9, 0x1b, 0xc6, 0x6a,
	0x8b, 0xe3, 0x5d, 0x85, 0x95, 0x20, 0x17, 0xad, 0xaf, 0x1c, 0x5b, 0x21, 0xd1, 0x27, 0xaa, 0x43,
	0x29, 0x98, 0xe2, 0x9b, 0x15, 0x15, 0x17, 0x11, 0x82, 0x69, 0x2b, 0x5e, 0x50, 0x51, 0xa8, 0xbc,
	0x74, 0x78, 0xe1, 0x73, 0x93, 0x87, 0xca, 0x51, 0x25, 0xf6, 0xbc, 0x07, 0x85, 0x98, 0x46, 0x1d,
	0x56, 0xa2, 0xb2, 0xb1, 0xcd, 0x01, 0xdd, 0x61, 0x9b, 0xd5, 0xaf, 0x6e, 0xc5, 0x9b, 0xd5, 0x97,
	0x8f, 0x61, 0x67, 0x25, 0xad, 0xc0, 0x4f, 0x36, 0x9d, 0x90, 0x6e, 0xba, 0x5f, 0x05, 0xd8, 0xe1,
	0xf4, 0x37, 0x1e, 0x75, 0x93, 0x8d, 0xb7, 0x33, 0x25, 0xd3, 0x77, 0x64, 0xb6, 0x08, 0x6e, 0x39,
	0xed, 0x05, 0xe3, 0x11, 0x17, 0xc5, 0x11, 0x46, 0xe3, 0xbc, 0xa6, 0x6b, 0xc4, 0x75, 0x5d, 0xf3,
	0xb9, 0xd3, 0xf5, 0x03, 0x3c, 0x5e, 0x8d, 0xee, 0xde, 0xd7, 0x7c, 0x44, 0xa1, 0x92, 0x1e, 0x77,
	0x54, 0x87, 0xe7, 0xca, 0x99, 0x62, 0x2a, 0x06, 0x6e, 0x29, 0xa6, 0x82, 0xcd, 0xf3, 0x81, 0x86,
	0xad, 0xfe, 0x70, 0xa0, 0xa9, 0xfa, 0x6b, 0x5d, 0x6b, 0x49, 0x0f, 0x50, 0x15, 0x1e, 0xaf, 0x30,
	0x4e, 0xf4, 0xb6, 0x24, 0xa0, 0x3d, 0x78, 0x9a, 0x94, 0x70, 0xd1, 0xb0, 0xa7, 0x74, 0xbb, 0x92,
	0x78, 0xf4, 0x16, 0x60, 0xb9, 0x9b, 0x12, 0xd4, 0x35, 0x1e, 0x9e, 0x00, 0x4a, 0x0a, 0x87, 0xa6,
	0x62, 0xea, 0xaa, 0x24, 0xa0, 0xa7, 0xb0, 0x93, 0xc4, 0x5b, 0xe7, 0x7d, 0xa5, 0xa7, 0xab, 0x92,
	0x78, 0xf4, 0xa7, 0x00, 0xa5, 0xe4, 0x1c, 0xa1, 0x67, 0xb0, 0x6b, 0x0d, 0xba, 0xa7, 0x4a, 0x0b,
	0x0f, 0x4f, 0x2d, 0x43, 0xd5, 0x70, 0x4b, 0x7b, 0xad, 0x58, 0x5d, 0x53, 0x7a, 0x80, 0x76, 0xe1,
	0x51, 0x5a, 0xa4, 0xf6, 0x5a, 0x92, 0x10, 0xf9, 0x4c, 0xc3, 0x1d, 0xd3, 0x1c, 0x48, 0x22, 0xfa,
	0x1f, 0xec, 0xa7, 0x71, 0xb3, 0xa3, 0x1b, 0x2d, 0x3c, 0x50, 0x0c, 0xf3, 0x1c, 0x1b, 0x5a, 0x5b,
	0xca, 0xa1, 0x03, 0x78, 0x99, 0xa6, 0x28, 0x7d, 0xb5, 0x73, 0x6a, 0xe0, 0xae, 0x7e, 0xa6, 0xe1,
	0x13, 0xad, 0xad, 0xf7, 0xa5, 0x0d, 0xb4, 0x0f, 0xcf, 0xd2, 0xa4, 0xa1, 0xde, 0xd5, 0xfa, 0x26,
	0xb6, 0x86, 0x9a, 0x21, 0x6d, 0x1e, 0xfd, 0x26, 0x02, 0x2c, 0x7f, 0x0b, 0xa2, 0xf2, 0x68, 0x7d,
	0x53, 0x37, 0xcf, 0xd7, 0x95, 0xe7, 0x31, 0x48, 0x29, 0x61, 0x64, 0x41, 0x88, 0xf2, 0x4a, 0xa2,
	0x6d, 0x4b, 0xef, 0xb6, 0x24, 0x71, 0x05, 0x36, 0x4e, 0xad, 0x81, 0x94, 0xcb, 0xda, 0x68, 0x2b,
	0x3d, 0x4d, 0xda, 0xc8, 0xba, 0x65, 0x36, 0x62, 0x95, 0x4d, 0x54, 0x83, 0x27, 0x59, 0x95, 0x58,
	0x96, 0x47, 0x2f, 0xa0, 0x96, 0x94, 0x0d, 0xac, 0x93, 0xae, 0xae, 0x62, 0x45, 0x55, 0x4f, 0xad,
	0xbe, 0x29, 0x6d, 0x45, 0xd5, 0x4d, 0xca, 0x4d, 0xae, 0xb7, 0x1d, 0xdd, 0x68, 0x12, 0x57, 0x3b,
	0x4a, 0xbf, 0xaf, 0x75, 0xa5, 0x42, 0x56, 0x41, 0xe5, 0x0a, 0xd0, 0xfc, 0x27, 0x07, 0x25, 0xde,
	0x46, 0x3d, 0x77, 0xac, 0xf8, 0x14, 0xbd, 0x81, 0xc2, 0xcd, 0xf3, 0x82, 0x56, 0x96, 0x6b, 0xf2,
	0x01, 0xad, 0xed, 0xdf, 0x22, 0x0d, 0x7c, 0xf9, 0x01, 0x72, 0x61, 0x77, 0xed, 0xf3, 0x8e, 0x0e,
	0xb3, 0x9a, 0x9f, 0xfa, 0x2f, 0xa9, 0x7d, 0x79, 0x4f, 0x26, 0xf3, 0x37, 0x64, 0x5d, 0x7b, 0xb3,
	0x89, 0xd0, 0xcb, 0x35, 0x6f, 0x43, 0x72, 0xfd, 0xd6, 0xea, 0xb7, 0x13, 0x98, 0x51, 0xbc, 0x58,
	0xfa, 0xcb, 0x8d, 0x80, 0x0e, 0xd6, 0xeb, 0xa5, 0x36, 0x5a, 0xed, 0xff, 0x77, 0x93, 0x98, 0x83,
	0x9f, 0xe0, 0x61, 0x66, 0x85, 0x22, 0x39, 0xab, 0xba, 0xfa, 0x74, 0xd4, 0x0e, 0xee, 0xe4, 0x44,
	0xd6, 0x4f, 0x9a, 0x6f, 0xbf, 0x1a, 0x7b, 0x13, 0xdb, 0x1d, 0x37, 0xbe, 0x69, 0x86, 0x61, 0x63,
	0xe4, 0x4d, 0x8f, 0xd9, 0xdf, 0xf6, 0xc8, 0x9b, 0x1c, 0x07, 0x64, 0x16, 0x3d, 0x0d, 0x41, 0xe6,
	0x77, 0xfc, 0x5d, 0x9e, 0x31, 0xbe, 0xfe, 0x37, 0x00, 0x00, 0xff, 0xff, 0x65, 0x47, 0x86, 0x4f,
	0xc5, 0x0b, 0x00, 0x00,
}
