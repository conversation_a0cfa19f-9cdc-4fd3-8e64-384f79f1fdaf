// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/topic_channel/event.proto

package event // import "golang.52tt.com/protocol/services/topic_channel/event"

/*
buf:lint:ignore DIRECTORY_SAME_PACKAGE
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import anypb "google.golang.org/protobuf/types/known/anypb"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Sex int32

const (
	Sex_All    Sex = 0
	Sex_Male   Sex = 1
	Sex_Female Sex = 2
)

var Sex_name = map[int32]string{
	0: "All",
	1: "Male",
	2: "Female",
}
var Sex_value = map[string]int32{
	"All":    0,
	"Male":   1,
	"Female": 2,
}

func (x Sex) String() string {
	return proto.EnumName(Sex_name, int32(x))
}
func (Sex) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{0}
}

type GameLabelType int32

const (
	GameLabelType_Default        GameLabelType = 0
	GameLabelType_HotLabel       GameLabelType = 1
	GameLabelType_LabelOfPublish GameLabelType = 2
	GameLabelType_LabelOfTabName GameLabelType = 3
	GameLabelType_LabelOfGlobal  GameLabelType = 4
)

var GameLabelType_name = map[int32]string{
	0: "Default",
	1: "HotLabel",
	2: "LabelOfPublish",
	3: "LabelOfTabName",
	4: "LabelOfGlobal",
}
var GameLabelType_value = map[string]int32{
	"Default":        0,
	"HotLabel":       1,
	"LabelOfPublish": 2,
	"LabelOfTabName": 3,
	"LabelOfGlobal":  4,
}

func (x GameLabelType) String() string {
	return proto.EnumName(GameLabelType_name, int32(x))
}
func (GameLabelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{1}
}

type ChannelDisplayType int32

const (
	ChannelDisplayType_DISPLAY_AT_MAIN_PAGE   ChannelDisplayType = 0
	ChannelDisplayType_DISPLAY_AT_FIND_FRIEND ChannelDisplayType = 2
	ChannelDisplayType_TEMPORARY              ChannelDisplayType = 3
)

var ChannelDisplayType_name = map[int32]string{
	0: "DISPLAY_AT_MAIN_PAGE",
	2: "DISPLAY_AT_FIND_FRIEND",
	3: "TEMPORARY",
}
var ChannelDisplayType_value = map[string]int32{
	"DISPLAY_AT_MAIN_PAGE":   0,
	"DISPLAY_AT_FIND_FRIEND": 2,
	"TEMPORARY":              3,
}

func (x ChannelDisplayType) String() string {
	return proto.EnumName(ChannelDisplayType_name, int32(x))
}
func (ChannelDisplayType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{2}
}

type Source int32

const (
	Source_INVAIAL Source = 0
	Source_CREATE  Source = 1
	Source_SWITCH  Source = 2
)

var Source_name = map[int32]string{
	0: "INVAIAL",
	1: "CREATE",
	2: "SWITCH",
}
var Source_value = map[string]int32{
	"INVAIAL": 0,
	"CREATE":  1,
	"SWITCH":  2,
}

func (x Source) String() string {
	return proto.EnumName(Source_name, int32(x))
}
func (Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{3}
}

// 负反馈上报
type NegativeFeedbackType int32

const (
	NegativeFeedbackType_FeedbackTypeInvalid NegativeFeedbackType = 0
	// 反馈房间
	NegativeFeedbackType_FeedbackTypeChannelOwner NegativeFeedbackType = 1
	NegativeFeedbackType_FeedbackTypeChannelTab   NegativeFeedbackType = 2
	NegativeFeedbackType_FeedbackTypePublishCond  NegativeFeedbackType = 3
	NegativeFeedbackType_FeedbackTypeChannelName  NegativeFeedbackType = 4
	// 房间内反馈用户
	NegativeFeedbackType_FeedbackTypeOwnerInChannel            NegativeFeedbackType = 5
	NegativeFeedbackType_FeedbackTypeUserInChannel             NegativeFeedbackType = 6
	NegativeFeedbackType_FeedbackTypeQuitUninterestedInChannel NegativeFeedbackType = 7
)

var NegativeFeedbackType_name = map[int32]string{
	0: "FeedbackTypeInvalid",
	1: "FeedbackTypeChannelOwner",
	2: "FeedbackTypeChannelTab",
	3: "FeedbackTypePublishCond",
	4: "FeedbackTypeChannelName",
	5: "FeedbackTypeOwnerInChannel",
	6: "FeedbackTypeUserInChannel",
	7: "FeedbackTypeQuitUninterestedInChannel",
}
var NegativeFeedbackType_value = map[string]int32{
	"FeedbackTypeInvalid":                   0,
	"FeedbackTypeChannelOwner":              1,
	"FeedbackTypeChannelTab":                2,
	"FeedbackTypePublishCond":               3,
	"FeedbackTypeChannelName":               4,
	"FeedbackTypeOwnerInChannel":            5,
	"FeedbackTypeUserInChannel":             6,
	"FeedbackTypeQuitUninterestedInChannel": 7,
}

func (x NegativeFeedbackType) String() string {
	return proto.EnumName(NegativeFeedbackType_name, int32(x))
}
func (NegativeFeedbackType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{4}
}

type TopicChannelEvent_ACTION int32

const (
	TopicChannelEvent_INVALID TopicChannelEvent_ACTION = 0
	TopicChannelEvent_CREATE  TopicChannelEvent_ACTION = 1
	TopicChannelEvent_DISMISS TopicChannelEvent_ACTION = 2
)

var TopicChannelEvent_ACTION_name = map[int32]string{
	0: "INVALID",
	1: "CREATE",
	2: "DISMISS",
}
var TopicChannelEvent_ACTION_value = map[string]int32{
	"INVALID": 0,
	"CREATE":  1,
	"DISMISS": 2,
}

func (x TopicChannelEvent_ACTION) String() string {
	return proto.EnumName(TopicChannelEvent_ACTION_name, int32(x))
}
func (TopicChannelEvent_ACTION) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{0, 0}
}

// 事件类型
type GameActivityEvent_EventType int32

const (
	GameActivityEvent_EVENT_TYPE_UNSPECIFIED GameActivityEvent_EventType = 0
	// 游戏搭子卡
	GameActivityEvent_EVENT_TYPE_GAME_PAL_CARD GameActivityEvent_EventType = 1
)

var GameActivityEvent_EventType_name = map[int32]string{
	0: "EVENT_TYPE_UNSPECIFIED",
	1: "EVENT_TYPE_GAME_PAL_CARD",
}
var GameActivityEvent_EventType_value = map[string]int32{
	"EVENT_TYPE_UNSPECIFIED":   0,
	"EVENT_TYPE_GAME_PAL_CARD": 1,
}

func (x GameActivityEvent_EventType) String() string {
	return proto.EnumName(GameActivityEvent_EventType_name, int32(x))
}
func (GameActivityEvent_EventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{10, 0}
}

type GameActivityEvent_GamePalCardEvent_Action int32

const (
	GameActivityEvent_GamePalCardEvent_ACTION_UNSPECIFIED GameActivityEvent_GamePalCardEvent_Action = 0
	// 曝光
	GameActivityEvent_GamePalCardEvent_ACTION_EXPOSED GameActivityEvent_GamePalCardEvent_Action = 1
	// 点击下一个
	GameActivityEvent_GamePalCardEvent_ACTION_SWITCHED GameActivityEvent_GamePalCardEvent_Action = 2
	// 打招呼
	GameActivityEvent_GamePalCardEvent_ACTION_GREETED GameActivityEvent_GamePalCardEvent_Action = 3
)

var GameActivityEvent_GamePalCardEvent_Action_name = map[int32]string{
	0: "ACTION_UNSPECIFIED",
	1: "ACTION_EXPOSED",
	2: "ACTION_SWITCHED",
	3: "ACTION_GREETED",
}
var GameActivityEvent_GamePalCardEvent_Action_value = map[string]int32{
	"ACTION_UNSPECIFIED": 0,
	"ACTION_EXPOSED":     1,
	"ACTION_SWITCHED":    2,
	"ACTION_GREETED":     3,
}

func (x GameActivityEvent_GamePalCardEvent_Action) String() string {
	return proto.EnumName(GameActivityEvent_GamePalCardEvent_Action_name, int32(x))
}
func (GameActivityEvent_GamePalCardEvent_Action) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{10, 0, 0}
}

type TopicChannelEvent struct {
	ChannelId    uint32                   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId        uint32                   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Creator      uint32                   `protobuf:"varint,3,opt,name=creator,proto3" json:"creator,omitempty"`
	BlockOptions []*BlockOption           `protobuf:"bytes,4,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"` // Deprecated: Do not use.
	IsPrivate    bool                     `protobuf:"varint,5,opt,name=is_private,json=isPrivate,proto3" json:"is_private,omitempty"`
	Action       TopicChannelEvent_ACTION `protobuf:"varint,6,opt,name=action,proto3,enum=topic_channel.event.TopicChannelEvent_ACTION" json:"action,omitempty"`
	Name         string                   `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
	DisplayType  []ChannelDisplayType     `protobuf:"varint,8,rep,packed,name=display_type,json=displayType,proto3,enum=topic_channel.event.ChannelDisplayType" json:"display_type,omitempty"`
	WantFresh    bool                     `protobuf:"varint,9,opt,name=want_fresh,json=wantFresh,proto3" json:"want_fresh,omitempty"`
	Sex          Sex                      `protobuf:"varint,10,opt,name=sex,proto3,enum=topic_channel.event.Sex" json:"sex,omitempty"`
	IsChange     bool                     `protobuf:"varint,11,opt,name=is_change,json=isChange,proto3" json:"is_change,omitempty"`
	ReleaseTime  uint32                   `protobuf:"varint,12,opt,name=release_time,json=releaseTime,proto3" json:"release_time,omitempty"`
	ReleaseIp    string                   `protobuf:"bytes,13,opt,name=release_ip,json=releaseIp,proto3" json:"release_ip,omitempty"`
	ShowGeoInfo  bool                     `protobuf:"varint,14,opt,name=show_geo_info,json=showGeoInfo,proto3" json:"show_geo_info,omitempty"`
	// 15用过了，不能用了
	AllSelectedBids           []uint32       `protobuf:"varint,16,rep,packed,name=all_selected_bids,json=allSelectedBids,proto3" json:"all_selected_bids,omitempty"`            // Deprecated: Do not use.
	UserUnSelectedBids        []uint32       `protobuf:"varint,17,rep,packed,name=user_un_selected_bids,json=userUnSelectedBids,proto3" json:"user_un_selected_bids,omitempty"` // Deprecated: Do not use.
	ChannelPlayMode           uint32         `protobuf:"varint,18,opt,name=channel_play_mode,json=channelPlayMode,proto3" json:"channel_play_mode,omitempty"`
	CategoryType              uint32         `protobuf:"varint,19,opt,name=category_type,json=categoryType,proto3" json:"category_type,omitempty"`
	GameLabels                []*GameLabel   `protobuf:"bytes,20,rep,name=game_labels,json=gameLabels,proto3" json:"game_labels,omitempty"`
	ClientType                uint32         `protobuf:"varint,21,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	AllUnselectedBlockOptions []*BlockOption `protobuf:"bytes,22,rep,name=all_unselected_block_options,json=allUnselectedBlockOptions,proto3" json:"all_unselected_block_options,omitempty"`
	SelectedBlockOptions      []*BlockOption `protobuf:"bytes,23,rep,name=selected_block_options,json=selectedBlockOptions,proto3" json:"selected_block_options,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}       `json:"-"`
	XXX_unrecognized          []byte         `json:"-"`
	XXX_sizecache             int32          `json:"-"`
}

func (m *TopicChannelEvent) Reset()         { *m = TopicChannelEvent{} }
func (m *TopicChannelEvent) String() string { return proto.CompactTextString(m) }
func (*TopicChannelEvent) ProtoMessage()    {}
func (*TopicChannelEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{0}
}
func (m *TopicChannelEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicChannelEvent.Unmarshal(m, b)
}
func (m *TopicChannelEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicChannelEvent.Marshal(b, m, deterministic)
}
func (dst *TopicChannelEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicChannelEvent.Merge(dst, src)
}
func (m *TopicChannelEvent) XXX_Size() int {
	return xxx_messageInfo_TopicChannelEvent.Size(m)
}
func (m *TopicChannelEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicChannelEvent.DiscardUnknown(m)
}

var xxx_messageInfo_TopicChannelEvent proto.InternalMessageInfo

func (m *TopicChannelEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TopicChannelEvent) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *TopicChannelEvent) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

// Deprecated: Do not use.
func (m *TopicChannelEvent) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *TopicChannelEvent) GetIsPrivate() bool {
	if m != nil {
		return m.IsPrivate
	}
	return false
}

func (m *TopicChannelEvent) GetAction() TopicChannelEvent_ACTION {
	if m != nil {
		return m.Action
	}
	return TopicChannelEvent_INVALID
}

func (m *TopicChannelEvent) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TopicChannelEvent) GetDisplayType() []ChannelDisplayType {
	if m != nil {
		return m.DisplayType
	}
	return nil
}

func (m *TopicChannelEvent) GetWantFresh() bool {
	if m != nil {
		return m.WantFresh
	}
	return false
}

func (m *TopicChannelEvent) GetSex() Sex {
	if m != nil {
		return m.Sex
	}
	return Sex_All
}

func (m *TopicChannelEvent) GetIsChange() bool {
	if m != nil {
		return m.IsChange
	}
	return false
}

func (m *TopicChannelEvent) GetReleaseTime() uint32 {
	if m != nil {
		return m.ReleaseTime
	}
	return 0
}

func (m *TopicChannelEvent) GetReleaseIp() string {
	if m != nil {
		return m.ReleaseIp
	}
	return ""
}

func (m *TopicChannelEvent) GetShowGeoInfo() bool {
	if m != nil {
		return m.ShowGeoInfo
	}
	return false
}

// Deprecated: Do not use.
func (m *TopicChannelEvent) GetAllSelectedBids() []uint32 {
	if m != nil {
		return m.AllSelectedBids
	}
	return nil
}

// Deprecated: Do not use.
func (m *TopicChannelEvent) GetUserUnSelectedBids() []uint32 {
	if m != nil {
		return m.UserUnSelectedBids
	}
	return nil
}

func (m *TopicChannelEvent) GetChannelPlayMode() uint32 {
	if m != nil {
		return m.ChannelPlayMode
	}
	return 0
}

func (m *TopicChannelEvent) GetCategoryType() uint32 {
	if m != nil {
		return m.CategoryType
	}
	return 0
}

func (m *TopicChannelEvent) GetGameLabels() []*GameLabel {
	if m != nil {
		return m.GameLabels
	}
	return nil
}

func (m *TopicChannelEvent) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *TopicChannelEvent) GetAllUnselectedBlockOptions() []*BlockOption {
	if m != nil {
		return m.AllUnselectedBlockOptions
	}
	return nil
}

func (m *TopicChannelEvent) GetSelectedBlockOptions() []*BlockOption {
	if m != nil {
		return m.SelectedBlockOptions
	}
	return nil
}

// 外显label
type GameLabel struct {
	Val                  string        `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
	DisplayName          string        `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	Type                 GameLabelType `protobuf:"varint,3,opt,name=type,proto3,enum=topic_channel.event.GameLabelType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GameLabel) Reset()         { *m = GameLabel{} }
func (m *GameLabel) String() string { return proto.CompactTextString(m) }
func (*GameLabel) ProtoMessage()    {}
func (*GameLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{1}
}
func (m *GameLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameLabel.Unmarshal(m, b)
}
func (m *GameLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameLabel.Marshal(b, m, deterministic)
}
func (dst *GameLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameLabel.Merge(dst, src)
}
func (m *GameLabel) XXX_Size() int {
	return xxx_messageInfo_GameLabel.Size(m)
}
func (m *GameLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_GameLabel.DiscardUnknown(m)
}

var xxx_messageInfo_GameLabel proto.InternalMessageInfo

func (m *GameLabel) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

func (m *GameLabel) GetDisplayName() string {
	if m != nil {
		return m.DisplayName
	}
	return ""
}

func (m *GameLabel) GetType() GameLabelType {
	if m != nil {
		return m.Type
	}
	return GameLabelType_Default
}

type BlockOption struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemId               uint32   `protobuf:"varint,2,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	Val                  string   `protobuf:"bytes,3,opt,name=val,proto3" json:"val,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlockOption) Reset()         { *m = BlockOption{} }
func (m *BlockOption) String() string { return proto.CompactTextString(m) }
func (*BlockOption) ProtoMessage()    {}
func (*BlockOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{2}
}
func (m *BlockOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOption.Unmarshal(m, b)
}
func (m *BlockOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOption.Marshal(b, m, deterministic)
}
func (dst *BlockOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOption.Merge(dst, src)
}
func (m *BlockOption) XXX_Size() int {
	return xxx_messageInfo_BlockOption.Size(m)
}
func (m *BlockOption) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOption.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOption proto.InternalMessageInfo

func (m *BlockOption) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *BlockOption) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

func (m *BlockOption) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

// 切换房间玩法
type SwitchChannelTabEvent struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Source               Source   `protobuf:"varint,4,opt,name=source,proto3,enum=topic_channel.event.Source" json:"source,omitempty"`
	UpdateTime           int64    `protobuf:"varint,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Uid                  uint32   `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`
	OldTabId             uint32   `protobuf:"varint,7,opt,name=old_tab_id,json=oldTabId,proto3" json:"old_tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchChannelTabEvent) Reset()         { *m = SwitchChannelTabEvent{} }
func (m *SwitchChannelTabEvent) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelTabEvent) ProtoMessage()    {}
func (*SwitchChannelTabEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{3}
}
func (m *SwitchChannelTabEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelTabEvent.Unmarshal(m, b)
}
func (m *SwitchChannelTabEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelTabEvent.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelTabEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelTabEvent.Merge(dst, src)
}
func (m *SwitchChannelTabEvent) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelTabEvent.Size(m)
}
func (m *SwitchChannelTabEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelTabEvent.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelTabEvent proto.InternalMessageInfo

func (m *SwitchChannelTabEvent) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SwitchChannelTabEvent) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *SwitchChannelTabEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SwitchChannelTabEvent) GetSource() Source {
	if m != nil {
		return m.Source
	}
	return Source_INVAIAL
}

func (m *SwitchChannelTabEvent) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *SwitchChannelTabEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SwitchChannelTabEvent) GetOldTabId() uint32 {
	if m != nil {
		return m.OldTabId
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type NegativeFeedbackEvent struct {
	ChannelId                    uint32                 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                        uint32                 `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Creator                      uint32                 `protobuf:"varint,3,opt,name=creator,proto3" json:"creator,omitempty"`
	BlockOptions                 []*BlockOption         `protobuf:"bytes,4,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	Name                         string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Negative_FeedbackType        []NegativeFeedbackType `protobuf:"varint,6,rep,packed,name=negative_Feedback_type,json=negativeFeedbackType,proto3,enum=topic_channel.event.NegativeFeedbackType" json:"negative_Feedback_type,omitempty"`
	ReporterUid                  uint32                 `protobuf:"varint,7,opt,name=reporter_uid,json=reporterUid,proto3" json:"reporter_uid,omitempty"`
	ExcludeWordsOfRoom           []string               `protobuf:"bytes,8,rep,name=exclude_words_of_room,json=excludeWordsOfRoom,proto3" json:"exclude_words_of_room,omitempty"`
	ReasonsOfBlackingCreator     []string               `protobuf:"bytes,9,rep,name=reasons_of_blacking_creator,json=reasonsOfBlackingCreator,proto3" json:"reasons_of_blacking_creator,omitempty"`
	BlackChannelUser             uint32                 `protobuf:"varint,10,opt,name=black_channel_user,json=blackChannelUser,proto3" json:"black_channel_user,omitempty"`
	BlackChannelUserEnableFilter bool                   `protobuf:"varint,11,opt,name=black_channel_user_enable_filter,json=blackChannelUserEnableFilter,proto3" json:"black_channel_user_enable_filter,omitempty"`
	ReasonsOfBlackChannelUser    []string               `protobuf:"bytes,12,rep,name=reasons_of_black_channel_user,json=reasonsOfBlackChannelUser,proto3" json:"reasons_of_black_channel_user,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}               `json:"-"`
	XXX_unrecognized             []byte                 `json:"-"`
	XXX_sizecache                int32                  `json:"-"`
}

func (m *NegativeFeedbackEvent) Reset()         { *m = NegativeFeedbackEvent{} }
func (m *NegativeFeedbackEvent) String() string { return proto.CompactTextString(m) }
func (*NegativeFeedbackEvent) ProtoMessage()    {}
func (*NegativeFeedbackEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{4}
}
func (m *NegativeFeedbackEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NegativeFeedbackEvent.Unmarshal(m, b)
}
func (m *NegativeFeedbackEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NegativeFeedbackEvent.Marshal(b, m, deterministic)
}
func (dst *NegativeFeedbackEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NegativeFeedbackEvent.Merge(dst, src)
}
func (m *NegativeFeedbackEvent) XXX_Size() int {
	return xxx_messageInfo_NegativeFeedbackEvent.Size(m)
}
func (m *NegativeFeedbackEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_NegativeFeedbackEvent.DiscardUnknown(m)
}

var xxx_messageInfo_NegativeFeedbackEvent proto.InternalMessageInfo

func (m *NegativeFeedbackEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NegativeFeedbackEvent) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *NegativeFeedbackEvent) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *NegativeFeedbackEvent) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *NegativeFeedbackEvent) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *NegativeFeedbackEvent) GetNegative_FeedbackType() []NegativeFeedbackType {
	if m != nil {
		return m.Negative_FeedbackType
	}
	return nil
}

func (m *NegativeFeedbackEvent) GetReporterUid() uint32 {
	if m != nil {
		return m.ReporterUid
	}
	return 0
}

func (m *NegativeFeedbackEvent) GetExcludeWordsOfRoom() []string {
	if m != nil {
		return m.ExcludeWordsOfRoom
	}
	return nil
}

func (m *NegativeFeedbackEvent) GetReasonsOfBlackingCreator() []string {
	if m != nil {
		return m.ReasonsOfBlackingCreator
	}
	return nil
}

func (m *NegativeFeedbackEvent) GetBlackChannelUser() uint32 {
	if m != nil {
		return m.BlackChannelUser
	}
	return 0
}

func (m *NegativeFeedbackEvent) GetBlackChannelUserEnableFilter() bool {
	if m != nil {
		return m.BlackChannelUserEnableFilter
	}
	return false
}

func (m *NegativeFeedbackEvent) GetReasonsOfBlackChannelUser() []string {
	if m != nil {
		return m.ReasonsOfBlackChannelUser
	}
	return nil
}

type FreezeChannelEvent struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	FreezeAt             int64    `protobuf:"varint,2,opt,name=freeze_at,json=freezeAt,proto3" json:"freeze_at,omitempty"`
	FreezeTime           int64    `protobuf:"varint,3,opt,name=freeze_time,json=freezeTime,proto3" json:"freeze_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreezeChannelEvent) Reset()         { *m = FreezeChannelEvent{} }
func (m *FreezeChannelEvent) String() string { return proto.CompactTextString(m) }
func (*FreezeChannelEvent) ProtoMessage()    {}
func (*FreezeChannelEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{5}
}
func (m *FreezeChannelEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeChannelEvent.Unmarshal(m, b)
}
func (m *FreezeChannelEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeChannelEvent.Marshal(b, m, deterministic)
}
func (dst *FreezeChannelEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeChannelEvent.Merge(dst, src)
}
func (m *FreezeChannelEvent) XXX_Size() int {
	return xxx_messageInfo_FreezeChannelEvent.Size(m)
}
func (m *FreezeChannelEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeChannelEvent.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeChannelEvent proto.InternalMessageInfo

func (m *FreezeChannelEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *FreezeChannelEvent) GetFreezeAt() int64 {
	if m != nil {
		return m.FreezeAt
	}
	return 0
}

func (m *FreezeChannelEvent) GetFreezeTime() int64 {
	if m != nil {
		return m.FreezeTime
	}
	return 0
}

// 房间发布
type ChannelFollowStatusUpdateEvent struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Channel              uint32   `protobuf:"varint,2,opt,name=channel,proto3" json:"channel,omitempty"`
	IsPublish            bool     `protobuf:"varint,3,opt,name=is_publish,json=isPublish,proto3" json:"is_publish,omitempty"`
	TabId                uint32   `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelFollowStatusUpdateEvent) Reset()         { *m = ChannelFollowStatusUpdateEvent{} }
func (m *ChannelFollowStatusUpdateEvent) String() string { return proto.CompactTextString(m) }
func (*ChannelFollowStatusUpdateEvent) ProtoMessage()    {}
func (*ChannelFollowStatusUpdateEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{6}
}
func (m *ChannelFollowStatusUpdateEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelFollowStatusUpdateEvent.Unmarshal(m, b)
}
func (m *ChannelFollowStatusUpdateEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelFollowStatusUpdateEvent.Marshal(b, m, deterministic)
}
func (dst *ChannelFollowStatusUpdateEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelFollowStatusUpdateEvent.Merge(dst, src)
}
func (m *ChannelFollowStatusUpdateEvent) XXX_Size() int {
	return xxx_messageInfo_ChannelFollowStatusUpdateEvent.Size(m)
}
func (m *ChannelFollowStatusUpdateEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelFollowStatusUpdateEvent.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelFollowStatusUpdateEvent proto.InternalMessageInfo

func (m *ChannelFollowStatusUpdateEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelFollowStatusUpdateEvent) GetChannel() uint32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

func (m *ChannelFollowStatusUpdateEvent) GetIsPublish() bool {
	if m != nil {
		return m.IsPublish
	}
	return false
}

func (m *ChannelFollowStatusUpdateEvent) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type TopicDuration struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	StayDuration         uint32   `protobuf:"varint,2,opt,name=stay_duration,json=stayDuration,proto3" json:"stay_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicDuration) Reset()         { *m = TopicDuration{} }
func (m *TopicDuration) String() string { return proto.CompactTextString(m) }
func (*TopicDuration) ProtoMessage()    {}
func (*TopicDuration) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{7}
}
func (m *TopicDuration) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicDuration.Unmarshal(m, b)
}
func (m *TopicDuration) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicDuration.Marshal(b, m, deterministic)
}
func (dst *TopicDuration) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicDuration.Merge(dst, src)
}
func (m *TopicDuration) XXX_Size() int {
	return xxx_messageInfo_TopicDuration.Size(m)
}
func (m *TopicDuration) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicDuration.DiscardUnknown(m)
}

var xxx_messageInfo_TopicDuration proto.InternalMessageInfo

func (m *TopicDuration) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *TopicDuration) GetStayDuration() uint32 {
	if m != nil {
		return m.StayDuration
	}
	return 0
}

// 开黑活动上报
type GameActivityTaskReportEvent struct {
	Uid        uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FinishTime int64  `protobuf:"varint,2,opt,name=finish_time,json=finishTime,proto3" json:"finish_time,omitempty"`
	// 任务类型，见channel-play_.proto, DailyTaskType, 1：每日第一次进入游戏专区时上报,
	// CONFIG_TAB_VIEW_DATA = 2; // 用户在xxxx主题xxxxtab的停留的时长 和 对应元素的数量
	GameTaskType         uint32   `protobuf:"varint,3,opt,name=game_task_type,json=gameTaskType,proto3" json:"game_task_type,omitempty"`
	TabId                uint32   `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ConfigTabId          string   `protobuf:"bytes,5,opt,name=config_tab_id,json=configTabId,proto3" json:"config_tab_id,omitempty"`
	StayDuration         uint32   `protobuf:"varint,6,opt,name=stay_duration,json=stayDuration,proto3" json:"stay_duration,omitempty"`
	ViewType             uint32   `protobuf:"varint,7,opt,name=view_type,json=viewType,proto3" json:"view_type,omitempty"`
	ViewCount            uint32   `protobuf:"varint,8,opt,name=view_count,json=viewCount,proto3" json:"view_count,omitempty"`
	AppId                uint32   `protobuf:"varint,9,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId             uint32   `protobuf:"varint,10,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	TopicIds             []string `protobuf:"bytes,11,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameActivityTaskReportEvent) Reset()         { *m = GameActivityTaskReportEvent{} }
func (m *GameActivityTaskReportEvent) String() string { return proto.CompactTextString(m) }
func (*GameActivityTaskReportEvent) ProtoMessage()    {}
func (*GameActivityTaskReportEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{8}
}
func (m *GameActivityTaskReportEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameActivityTaskReportEvent.Unmarshal(m, b)
}
func (m *GameActivityTaskReportEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameActivityTaskReportEvent.Marshal(b, m, deterministic)
}
func (dst *GameActivityTaskReportEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameActivityTaskReportEvent.Merge(dst, src)
}
func (m *GameActivityTaskReportEvent) XXX_Size() int {
	return xxx_messageInfo_GameActivityTaskReportEvent.Size(m)
}
func (m *GameActivityTaskReportEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_GameActivityTaskReportEvent.DiscardUnknown(m)
}

var xxx_messageInfo_GameActivityTaskReportEvent proto.InternalMessageInfo

func (m *GameActivityTaskReportEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameActivityTaskReportEvent) GetFinishTime() int64 {
	if m != nil {
		return m.FinishTime
	}
	return 0
}

func (m *GameActivityTaskReportEvent) GetGameTaskType() uint32 {
	if m != nil {
		return m.GameTaskType
	}
	return 0
}

func (m *GameActivityTaskReportEvent) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GameActivityTaskReportEvent) GetConfigTabId() string {
	if m != nil {
		return m.ConfigTabId
	}
	return ""
}

func (m *GameActivityTaskReportEvent) GetStayDuration() uint32 {
	if m != nil {
		return m.StayDuration
	}
	return 0
}

func (m *GameActivityTaskReportEvent) GetViewType() uint32 {
	if m != nil {
		return m.ViewType
	}
	return 0
}

func (m *GameActivityTaskReportEvent) GetViewCount() uint32 {
	if m != nil {
		return m.ViewCount
	}
	return 0
}

func (m *GameActivityTaskReportEvent) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *GameActivityTaskReportEvent) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GameActivityTaskReportEvent) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

// 房间列表近三十分钟筛选记录
type ChannelListSelectTagEvent struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SelectTags           []string `protobuf:"bytes,2,rep,name=select_tags,json=selectTags,proto3" json:"select_tags,omitempty"`
	TabId                uint32   `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ExpireTime           int64    `protobuf:"varint,4,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelListSelectTagEvent) Reset()         { *m = ChannelListSelectTagEvent{} }
func (m *ChannelListSelectTagEvent) String() string { return proto.CompactTextString(m) }
func (*ChannelListSelectTagEvent) ProtoMessage()    {}
func (*ChannelListSelectTagEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{9}
}
func (m *ChannelListSelectTagEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListSelectTagEvent.Unmarshal(m, b)
}
func (m *ChannelListSelectTagEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListSelectTagEvent.Marshal(b, m, deterministic)
}
func (dst *ChannelListSelectTagEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListSelectTagEvent.Merge(dst, src)
}
func (m *ChannelListSelectTagEvent) XXX_Size() int {
	return xxx_messageInfo_ChannelListSelectTagEvent.Size(m)
}
func (m *ChannelListSelectTagEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListSelectTagEvent.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListSelectTagEvent proto.InternalMessageInfo

func (m *ChannelListSelectTagEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelListSelectTagEvent) GetSelectTags() []string {
	if m != nil {
		return m.SelectTags
	}
	return nil
}

func (m *ChannelListSelectTagEvent) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ChannelListSelectTagEvent) GetExpireTime() int64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

// 上报给活动方的事件
type GameActivityEvent struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AppId    uint32 `protobuf:"varint,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId uint32 `protobuf:"varint,3,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	// 上报时间
	ReportedAt           int64                       `protobuf:"varint,4,opt,name=reported_at,json=reportedAt,proto3" json:"reported_at,omitempty"`
	EventType            GameActivityEvent_EventType `protobuf:"varint,5,opt,name=event_type,json=eventType,proto3,enum=topic_channel.event.GameActivityEvent_EventType" json:"event_type,omitempty"`
	EventDetail          []byte                      `protobuf:"bytes,6,opt,name=event_detail,json=eventDetail,proto3" json:"event_detail,omitempty"`
	Detail               *anypb.Any                  `protobuf:"bytes,7,opt,name=detail,proto3" json:"detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GameActivityEvent) Reset()         { *m = GameActivityEvent{} }
func (m *GameActivityEvent) String() string { return proto.CompactTextString(m) }
func (*GameActivityEvent) ProtoMessage()    {}
func (*GameActivityEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{10}
}
func (m *GameActivityEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameActivityEvent.Unmarshal(m, b)
}
func (m *GameActivityEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameActivityEvent.Marshal(b, m, deterministic)
}
func (dst *GameActivityEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameActivityEvent.Merge(dst, src)
}
func (m *GameActivityEvent) XXX_Size() int {
	return xxx_messageInfo_GameActivityEvent.Size(m)
}
func (m *GameActivityEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_GameActivityEvent.DiscardUnknown(m)
}

var xxx_messageInfo_GameActivityEvent proto.InternalMessageInfo

func (m *GameActivityEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameActivityEvent) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *GameActivityEvent) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GameActivityEvent) GetReportedAt() int64 {
	if m != nil {
		return m.ReportedAt
	}
	return 0
}

func (m *GameActivityEvent) GetEventType() GameActivityEvent_EventType {
	if m != nil {
		return m.EventType
	}
	return GameActivityEvent_EVENT_TYPE_UNSPECIFIED
}

func (m *GameActivityEvent) GetEventDetail() []byte {
	if m != nil {
		return m.EventDetail
	}
	return nil
}

func (m *GameActivityEvent) GetDetail() *anypb.Any {
	if m != nil {
		return m.Detail
	}
	return nil
}

// 游戏搭子卡事件
type GameActivityEvent_GamePalCardEvent struct {
	Action GameActivityEvent_GamePalCardEvent_Action `protobuf:"varint,1,opt,name=action,proto3,enum=topic_channel.event.GameActivityEvent_GamePalCardEvent_Action" json:"action,omitempty"`
	// 玩法ID
	TabId uint32 `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	// 搭子卡ID
	CardId               string   `protobuf:"bytes,3,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameActivityEvent_GamePalCardEvent) Reset()         { *m = GameActivityEvent_GamePalCardEvent{} }
func (m *GameActivityEvent_GamePalCardEvent) String() string { return proto.CompactTextString(m) }
func (*GameActivityEvent_GamePalCardEvent) ProtoMessage()    {}
func (*GameActivityEvent_GamePalCardEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_fd9001a15e68d909, []int{10, 0}
}
func (m *GameActivityEvent_GamePalCardEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameActivityEvent_GamePalCardEvent.Unmarshal(m, b)
}
func (m *GameActivityEvent_GamePalCardEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameActivityEvent_GamePalCardEvent.Marshal(b, m, deterministic)
}
func (dst *GameActivityEvent_GamePalCardEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameActivityEvent_GamePalCardEvent.Merge(dst, src)
}
func (m *GameActivityEvent_GamePalCardEvent) XXX_Size() int {
	return xxx_messageInfo_GameActivityEvent_GamePalCardEvent.Size(m)
}
func (m *GameActivityEvent_GamePalCardEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_GameActivityEvent_GamePalCardEvent.DiscardUnknown(m)
}

var xxx_messageInfo_GameActivityEvent_GamePalCardEvent proto.InternalMessageInfo

func (m *GameActivityEvent_GamePalCardEvent) GetAction() GameActivityEvent_GamePalCardEvent_Action {
	if m != nil {
		return m.Action
	}
	return GameActivityEvent_GamePalCardEvent_ACTION_UNSPECIFIED
}

func (m *GameActivityEvent_GamePalCardEvent) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GameActivityEvent_GamePalCardEvent) GetCardId() string {
	if m != nil {
		return m.CardId
	}
	return ""
}

func init() {
	proto.RegisterType((*TopicChannelEvent)(nil), "topic_channel.event.TopicChannelEvent")
	proto.RegisterType((*GameLabel)(nil), "topic_channel.event.GameLabel")
	proto.RegisterType((*BlockOption)(nil), "topic_channel.event.BlockOption")
	proto.RegisterType((*SwitchChannelTabEvent)(nil), "topic_channel.event.SwitchChannelTabEvent")
	proto.RegisterType((*NegativeFeedbackEvent)(nil), "topic_channel.event.NegativeFeedbackEvent")
	proto.RegisterType((*FreezeChannelEvent)(nil), "topic_channel.event.FreezeChannelEvent")
	proto.RegisterType((*ChannelFollowStatusUpdateEvent)(nil), "topic_channel.event.ChannelFollowStatusUpdateEvent")
	proto.RegisterType((*TopicDuration)(nil), "topic_channel.event.TopicDuration")
	proto.RegisterType((*GameActivityTaskReportEvent)(nil), "topic_channel.event.GameActivityTaskReportEvent")
	proto.RegisterType((*ChannelListSelectTagEvent)(nil), "topic_channel.event.ChannelListSelectTagEvent")
	proto.RegisterType((*GameActivityEvent)(nil), "topic_channel.event.GameActivityEvent")
	proto.RegisterType((*GameActivityEvent_GamePalCardEvent)(nil), "topic_channel.event.GameActivityEvent.GamePalCardEvent")
	proto.RegisterEnum("topic_channel.event.Sex", Sex_name, Sex_value)
	proto.RegisterEnum("topic_channel.event.GameLabelType", GameLabelType_name, GameLabelType_value)
	proto.RegisterEnum("topic_channel.event.ChannelDisplayType", ChannelDisplayType_name, ChannelDisplayType_value)
	proto.RegisterEnum("topic_channel.event.Source", Source_name, Source_value)
	proto.RegisterEnum("topic_channel.event.NegativeFeedbackType", NegativeFeedbackType_name, NegativeFeedbackType_value)
	proto.RegisterEnum("topic_channel.event.TopicChannelEvent_ACTION", TopicChannelEvent_ACTION_name, TopicChannelEvent_ACTION_value)
	proto.RegisterEnum("topic_channel.event.GameActivityEvent_EventType", GameActivityEvent_EventType_name, GameActivityEvent_EventType_value)
	proto.RegisterEnum("topic_channel.event.GameActivityEvent_GamePalCardEvent_Action", GameActivityEvent_GamePalCardEvent_Action_name, GameActivityEvent_GamePalCardEvent_Action_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/topic_channel/event.proto", fileDescriptor_event_fd9001a15e68d909)
}

var fileDescriptor_event_fd9001a15e68d909 = []byte{
	// 1879 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0x4b, 0x73, 0xdb, 0xc8,
	0x11, 0x36, 0x09, 0x9a, 0x8f, 0x26, 0x69, 0x43, 0x63, 0xc9, 0x86, 0x2c, 0x3f, 0x14, 0x66, 0x53,
	0xd1, 0xaa, 0x76, 0xa9, 0xc4, 0x5b, 0xce, 0x2d, 0x0f, 0x8a, 0x04, 0xb5, 0x48, 0x49, 0x22, 0x03,
	0x52, 0xde, 0x38, 0x55, 0x29, 0xd4, 0x10, 0x18, 0x52, 0x28, 0x0d, 0x01, 0x2e, 0x30, 0xd4, 0x23,
	0xc7, 0x1c, 0x72, 0xcb, 0xef, 0xca, 0xcf, 0xc8, 0x21, 0xf7, 0x54, 0xae, 0xb9, 0xa5, 0xa6, 0x67,
	0x20, 0x41, 0x34, 0xb5, 0xe5, 0xa4, 0x2a, 0x17, 0x15, 0xe6, 0xeb, 0x9e, 0xe9, 0xc7, 0xf4, 0xd7,
	0xd3, 0x14, 0xec, 0x09, 0x71, 0xf0, 0xfd, 0x32, 0xf4, 0x2f, 0xd2, 0x90, 0x5f, 0xb2, 0xe4, 0x40,
	0xc4, 0x8b, 0xd0, 0xf7, 0xfc, 0x73, 0x1a, 0x45, 0x8c, 0x1f, 0xb0, 0x4b, 0x16, 0x89, 0xf6, 0x22,
	0x89, 0x45, 0x4c, 0x9e, 0xdd, 0x13, 0xb5, 0x51, 0xf4, 0x72, 0x7b, 0x16, 0xc7, 0x33, 0xce, 0x0e,
	0x50, 0x65, 0xb2, 0x9c, 0x1e, 0xd0, 0xe8, 0x46, 0xe9, 0xb7, 0xfe, 0x56, 0x85, 0x8d, 0xb1, 0xdc,
	0xd2, 0x55, 0x3b, 0x6c, 0xb9, 0x81, 0xbc, 0x06, 0xd0, 0x27, 0x78, 0x61, 0x60, 0x15, 0x76, 0x0b,
	0x7b, 0x4d, 0xb7, 0xa6, 0x11, 0x27, 0x20, 0x5b, 0x50, 0x16, 0x74, 0x22, 0x45, 0x45, 0x14, 0x3d,
	0x16, 0x74, 0xe2, 0x04, 0xc4, 0x82, 0x8a, 0x9f, 0x30, 0x2a, 0xe2, 0xc4, 0x32, 0x10, 0xcf, 0x96,
	0xc4, 0x81, 0xe6, 0x84, 0xc7, 0xfe, 0x85, 0x17, 0x2f, 0x44, 0x18, 0x47, 0xa9, 0x55, 0xda, 0x35,
	0xf6, 0xea, 0xef, 0x76, 0xdb, 0x6b, 0xbc, 0x6d, 0x1f, 0x4a, 0xcd, 0x01, 0x2a, 0x1e, 0x16, 0xad,
	0x82, 0xdb, 0x98, 0xdc, 0x01, 0xa9, 0x74, 0x2d, 0x4c, 0xbd, 0x45, 0x12, 0x5e, 0x52, 0xc1, 0xac,
	0xc7, 0xbb, 0x85, 0xbd, 0xaa, 0x5b, 0x0b, 0xd3, 0xa1, 0x02, 0x88, 0x0d, 0x65, 0xea, 0x4b, 0x4d,
	0xab, 0xbc, 0x5b, 0xd8, 0x7b, 0xf2, 0xee, 0xeb, 0xb5, 0x26, 0x3e, 0x89, 0xb8, 0xdd, 0xe9, 0x8e,
	0x9d, 0xc1, 0xa9, 0xab, 0x37, 0x13, 0x02, 0xa5, 0x88, 0xce, 0x99, 0x55, 0xd9, 0x2d, 0xec, 0xd5,
	0x5c, 0xfc, 0x26, 0xbf, 0x85, 0x46, 0x10, 0xa6, 0x0b, 0x4e, 0x6f, 0x3c, 0x71, 0xb3, 0x60, 0x56,
	0x75, 0xd7, 0xd8, 0x7b, 0xf2, 0xee, 0xa7, 0x6b, 0x0d, 0xe8, 0xb3, 0x7b, 0x4a, 0x7f, 0x7c, 0xb3,
	0x60, 0x6e, 0x3d, 0xb8, 0x5b, 0xc8, 0x28, 0xae, 0x68, 0x24, 0xbc, 0x69, 0xc2, 0xd2, 0x73, 0xab,
	0xa6, 0xa2, 0x90, 0x48, 0x5f, 0x02, 0x64, 0x1f, 0x8c, 0x94, 0x5d, 0x5b, 0x80, 0x21, 0x58, 0x6b,
	0x2d, 0x8c, 0xd8, 0xb5, 0x2b, 0x95, 0xc8, 0x0e, 0xd4, 0xc2, 0x14, 0x85, 0x33, 0x66, 0xd5, 0xf1,
	0xa4, 0x6a, 0x98, 0x76, 0x71, 0x4d, 0x7e, 0x04, 0x8d, 0x84, 0x71, 0x46, 0x53, 0xe6, 0x89, 0x70,
	0xce, 0xac, 0x06, 0xde, 0x4b, 0x5d, 0x63, 0xe3, 0x70, 0x8e, 0xae, 0x64, 0x2a, 0xe1, 0xc2, 0x6a,
	0x62, 0xc0, 0x35, 0x8d, 0x38, 0x0b, 0xd2, 0x82, 0x66, 0x7a, 0x1e, 0x5f, 0x79, 0x33, 0x16, 0x7b,
	0x61, 0x34, 0x8d, 0xad, 0x27, 0x68, 0xa2, 0x2e, 0xc1, 0x23, 0x16, 0x3b, 0xd1, 0x34, 0x26, 0x6d,
	0xd8, 0xa0, 0x9c, 0x7b, 0x29, 0xe3, 0xcc, 0x17, 0x2c, 0xf0, 0x26, 0x61, 0x90, 0x5a, 0xe6, 0xae,
	0xb1, 0xd7, 0xc4, 0x0b, 0x7c, 0x4a, 0x39, 0x1f, 0x69, 0xd9, 0x61, 0x18, 0xa4, 0xe4, 0x3d, 0x6c,
	0x2d, 0x53, 0x96, 0x78, 0xcb, 0x68, 0x65, 0xcf, 0xc6, 0xed, 0x1e, 0x22, 0x15, 0xce, 0xa2, 0x7b,
	0xdb, 0xf6, 0x61, 0x23, 0xab, 0x4a, 0xbc, 0x85, 0x79, 0x1c, 0x30, 0x8b, 0x60, 0x44, 0x4f, 0xb5,
	0x60, 0xc8, 0xe9, 0xcd, 0x49, 0x1c, 0x30, 0xf2, 0x63, 0x68, 0xfa, 0x54, 0xb0, 0x59, 0x9c, 0xe8,
	0xdb, 0x7a, 0x86, 0x7a, 0x8d, 0x0c, 0xc4, 0x5b, 0xf8, 0x35, 0xd4, 0x67, 0x74, 0xce, 0x3c, 0x4e,
	0x27, 0x8c, 0xa7, 0xd6, 0x26, 0x16, 0xe5, 0x9b, 0xb5, 0xe9, 0x3e, 0xa2, 0x73, 0x76, 0x2c, 0xd5,
	0x5c, 0x98, 0x65, 0x9f, 0x29, 0x79, 0x0b, 0x75, 0x9f, 0x87, 0x2c, 0x12, 0xca, 0xc6, 0x16, 0xda,
	0x00, 0x05, 0xa1, 0x05, 0x0a, 0xaf, 0x64, 0x66, 0x96, 0xd1, 0x5d, 0x9c, 0xf7, 0x78, 0xf0, 0xfc,
	0xf3, 0x78, 0xe0, 0x6e, 0x53, 0xce, 0xcf, 0x6e, 0x0f, 0x39, 0xcc, 0x13, 0xe2, 0x03, 0x3c, 0x7f,
	0xe0, 0xf0, 0x17, 0x9f, 0x79, 0xf8, 0xe6, 0xba, 0x73, 0x5b, 0x6d, 0x28, 0x2b, 0x52, 0x90, 0x3a,
	0x54, 0x9c, 0xd3, 0x0f, 0x9d, 0x63, 0xa7, 0x67, 0x3e, 0x22, 0x00, 0xe5, 0xae, 0x6b, 0x77, 0xc6,
	0xb6, 0x59, 0x90, 0x82, 0x9e, 0x33, 0x3a, 0x71, 0x46, 0x23, 0xb3, 0xd8, 0xba, 0x86, 0xda, 0x6d,
	0x92, 0x88, 0x09, 0xc6, 0x25, 0xe5, 0xd8, 0x39, 0x6a, 0xae, 0xfc, 0x94, 0x95, 0x98, 0xb1, 0x07,
	0x99, 0x55, 0x44, 0x51, 0x46, 0x8a, 0x53, 0x49, 0xb0, 0x5f, 0x40, 0x09, 0xd3, 0x68, 0x60, 0xd9,
	0xb7, 0x7e, 0xf8, 0x1e, 0x90, 0x53, 0xa8, 0xdf, 0x1a, 0x41, 0x3d, 0xe7, 0x39, 0xd9, 0x86, 0xaa,
	0xca, 0xc3, 0x6d, 0xeb, 0xaa, 0xe0, 0xda, 0x09, 0xc8, 0x0b, 0xa8, 0x30, 0xce, 0xe6, 0x77, 0x9d,
	0xab, 0x2c, 0x97, 0x4e, 0x90, 0xf9, 0x6b, 0xdc, 0xfa, 0xdb, 0xfa, 0x67, 0x01, 0xb6, 0x46, 0x57,
	0xa1, 0xf0, 0xcf, 0x35, 0x97, 0xc7, 0x74, 0xa2, 0x9a, 0xe3, 0x5d, 0xf7, 0x2b, 0xe4, 0xbb, 0xdf,
	0x36, 0x54, 0x25, 0x9c, 0x0b, 0xae, 0x22, 0xe8, 0x04, 0x03, 0xbb, 0xdf, 0x4e, 0x8d, 0xd5, 0x76,
	0xfa, 0x0d, 0x94, 0xd3, 0x78, 0x99, 0xf8, 0xcc, 0x2a, 0x61, 0xe4, 0x3b, 0xeb, 0x09, 0x8f, 0x2a,
	0xae, 0x56, 0x95, 0xa5, 0xb7, 0x5c, 0x04, 0x54, 0x68, 0x62, 0xcb, 0x46, 0x68, 0xb8, 0xa0, 0x20,
	0xe4, 0xb5, 0x09, 0xc6, 0x32, 0x0c, 0xb0, 0x0d, 0x36, 0x5d, 0xf9, 0x49, 0x5e, 0x01, 0xc4, 0x3c,
	0xf0, 0xb4, 0xf3, 0x15, 0x14, 0x54, 0x63, 0x1e, 0x8c, 0xa5, 0xff, 0xad, 0x7f, 0x95, 0x60, 0xeb,
	0x94, 0xcd, 0xa8, 0x08, 0x2f, 0x59, 0x9f, 0xb1, 0x60, 0x42, 0xfd, 0x8b, 0xff, 0xcf, 0x6b, 0x60,
	0xff, 0x8f, 0xaf, 0xc1, 0xca, 0x4b, 0x90, 0xf5, 0xe8, 0xc7, 0xb9, 0x1e, 0xed, 0xc1, 0xf3, 0x48,
	0xc7, 0xe0, 0x65, 0x41, 0x28, 0x6e, 0x96, 0xb1, 0x5b, 0x7f, 0xb9, 0xd6, 0xc6, 0x6a, 0xd8, 0x58,
	0x5b, 0x9b, 0xd1, 0x1a, 0x54, 0x35, 0xd4, 0x45, 0x9c, 0x08, 0xd9, 0xbe, 0x6e, 0xb3, 0x58, 0xcf,
	0xb0, 0xb3, 0x30, 0x20, 0x3f, 0x87, 0x2d, 0x76, 0xed, 0xf3, 0x65, 0xc0, 0xbc, 0xab, 0x38, 0x09,
	0x52, 0x2f, 0x9e, 0x7a, 0x49, 0x1c, 0xcf, 0xf1, 0xc1, 0xa8, 0xb9, 0x44, 0x0b, 0xbf, 0x93, 0xb2,
	0xc1, 0xd4, 0x8d, 0xe3, 0x39, 0xf9, 0x25, 0xec, 0x24, 0x8c, 0xa6, 0x71, 0x84, 0xca, 0x13, 0x4e,
	0xfd, 0x8b, 0x30, 0x9a, 0x79, 0x59, 0xfe, 0x6a, 0xb8, 0xd1, 0xd2, 0x2a, 0x83, 0xe9, 0xa1, 0x56,
	0xe8, 0xea, 0x84, 0x7e, 0x05, 0x04, 0xf7, 0x64, 0x61, 0x79, 0xb2, 0x79, 0xe2, 0xeb, 0xd1, 0x74,
	0x4d, 0x94, 0xe8, 0x1a, 0x3e, 0x4b, 0x59, 0x42, 0xfa, 0xb0, 0xfb, 0xa9, 0xb6, 0xc7, 0x22, 0x3a,
	0xe1, 0xcc, 0x9b, 0x86, 0x5c, 0xb0, 0x44, 0xbf, 0x23, 0xaf, 0x56, 0xf7, 0xda, 0xa8, 0xd4, 0x47,
	0x1d, 0xf2, 0x1b, 0x78, 0xbd, 0xea, 0xf4, 0x7d, 0x07, 0x1a, 0xe8, 0xf6, 0xf6, 0x7d, 0xb7, 0x73,
	0xa7, 0xb5, 0xbe, 0x07, 0xd2, 0x4f, 0x18, 0xfb, 0x13, 0xfb, 0x6f, 0x86, 0x8f, 0x1d, 0xa8, 0x4d,
	0x71, 0x93, 0x47, 0x05, 0x56, 0x9c, 0xe1, 0x56, 0x15, 0xd0, 0x11, 0x92, 0x15, 0x5a, 0x88, 0xac,
	0x30, 0x14, 0x2b, 0x14, 0x24, 0x59, 0xd1, 0xfa, 0x73, 0x01, 0xde, 0x68, 0x6b, 0xfd, 0x98, 0xf3,
	0xf8, 0x6a, 0x24, 0xa8, 0x58, 0xa6, 0x67, 0xc8, 0x1b, 0x65, 0x5f, 0x13, 0xa7, 0x70, 0x47, 0x1c,
	0x59, 0xca, 0x6a, 0x8f, 0x2e, 0xf1, 0x6c, 0x99, 0x4d, 0x23, 0xcb, 0x09, 0x0f, 0xd3, 0x73, 0x34,
	0xa7, 0xa6, 0x11, 0x05, 0xe4, 0xa8, 0x51, 0xca, 0x51, 0xa3, 0x35, 0x80, 0x26, 0x4e, 0x20, 0xbd,
	0x65, 0x42, 0xb3, 0x96, 0xa5, 0xea, 0x52, 0xdb, 0x95, 0xbd, 0x43, 0xae, 0x9d, 0x40, 0x3e, 0x64,
	0xa9, 0xa0, 0x37, 0x5e, 0xa0, 0x75, 0xb5, 0x07, 0x0d, 0x09, 0x66, 0xfb, 0x5b, 0xff, 0x28, 0xc2,
	0x8e, 0xec, 0x8c, 0x1d, 0x5f, 0x84, 0x97, 0xa1, 0xb8, 0x19, 0xd3, 0xf4, 0xc2, 0xc5, 0x92, 0x7c,
	0x28, 0x24, 0x99, 0xa8, 0x30, 0x0a, 0xd3, 0x73, 0x95, 0xa8, 0xa2, 0x4e, 0x14, 0x42, 0xd8, 0x3e,
	0xbe, 0x80, 0x27, 0xf8, 0x36, 0x0a, 0x9a, 0x6a, 0x06, 0x29, 0x16, 0x37, 0x24, 0x2a, 0xcf, 0x47,
	0x3a, 0xac, 0x0f, 0x50, 0x0e, 0x0d, 0x7e, 0x1c, 0x4d, 0xc3, 0x59, 0xd6, 0x6c, 0x14, 0x47, 0xeb,
	0x0a, 0xc4, 0x7e, 0xf3, 0x69, 0x60, 0xe5, 0x4f, 0x03, 0x93, 0x97, 0x7d, 0x19, 0xb2, 0x2b, 0xe5,
	0x80, 0xee, 0x58, 0x12, 0xc8, 0x86, 0x28, 0x14, 0xfa, 0xf1, 0x32, 0x12, 0x56, 0x55, 0x15, 0x8a,
	0x44, 0xba, 0x12, 0x90, 0xbe, 0xd1, 0xc5, 0x42, 0x5a, 0xaf, 0x29, 0xdf, 0xe8, 0x62, 0xa1, 0xea,
	0x67, 0x4e, 0x93, 0x0b, 0x26, 0xa4, 0x44, 0x71, 0xa4, 0xaa, 0x00, 0x25, 0xcc, 0x2e, 0x22, 0xb5,
	0xea, 0x58, 0xbf, 0x55, 0x7d, 0x13, 0x69, 0xeb, 0x2f, 0x05, 0xd8, 0xd6, 0xb5, 0x73, 0x1c, 0xa6,
	0x42, 0xcd, 0x26, 0x63, 0x3a, 0xfb, 0x81, 0x1c, 0xab, 0x97, 0xd5, 0x13, 0x74, 0x96, 0x5a, 0x45,
	0x3c, 0x0e, 0xd2, 0x6c, 0x5b, 0x9a, 0xcb, 0x9e, 0x91, 0xcf, 0xde, 0x5b, 0xa8, 0xb3, 0xeb, 0x45,
	0x98, 0xe8, 0x22, 0x2e, 0xa9, 0xbb, 0x51, 0x10, 0x16, 0xf1, 0xdf, 0x4b, 0xb0, 0x91, 0xbf, 0xee,
	0x87, 0x1c, 0xb8, 0xcb, 0x40, 0xf1, 0xc1, 0x0c, 0x18, 0x2b, 0x19, 0x78, 0x0b, 0x59, 0x33, 0x0b,
	0x24, 0xc1, 0xb4, 0xf1, 0x0c, 0xea, 0x08, 0x32, 0x00, 0xc0, 0xae, 0xa9, 0xee, 0xe4, 0x31, 0xbe,
	0x58, 0x3f, 0x7b, 0xf0, 0xad, 0xbe, 0xe7, 0x62, 0x1b, 0xff, 0x62, 0x77, 0xad, 0xb1, 0xec, 0x53,
	0xb6, 0x54, 0x75, 0x60, 0xc0, 0x04, 0x0d, 0x39, 0xd6, 0x41, 0xc3, 0xad, 0x23, 0xd6, 0x43, 0x88,
	0x7c, 0x05, 0x65, 0x2d, 0x94, 0x35, 0x50, 0x7f, 0xb7, 0xd9, 0x56, 0xbf, 0x68, 0xda, 0xd9, 0x2f,
	0x9a, 0x76, 0x27, 0xba, 0x71, 0xb5, 0xce, 0xcb, 0x7f, 0x17, 0xc0, 0x94, 0xb6, 0x87, 0x94, 0x77,
	0x69, 0x12, 0xa8, 0xec, 0x7c, 0xb8, 0xfd, 0x61, 0x50, 0x40, 0x97, 0x7f, 0xf5, 0x99, 0x2e, 0xaf,
	0x1e, 0xd4, 0xee, 0xe0, 0x29, 0xb7, 0xbf, 0x14, 0x1e, 0x78, 0xfd, 0x5e, 0x40, 0xc5, 0xa7, 0x49,
	0x90, 0x65, 0xb8, 0xe6, 0x96, 0xe5, 0xd2, 0x09, 0x5a, 0x7f, 0x84, 0xb2, 0x3a, 0x81, 0x3c, 0x07,
	0xa2, 0x06, 0x2c, 0xef, 0xec, 0x74, 0x34, 0xb4, 0xbb, 0x4e, 0xdf, 0xb1, 0xe5, 0x84, 0x45, 0xe0,
	0x89, 0xc6, 0xed, 0xdf, 0x0f, 0x07, 0x23, 0xbb, 0x67, 0x16, 0xc8, 0x33, 0x78, 0xaa, 0xb1, 0xd1,
	0x77, 0xce, 0xb8, 0xfb, 0xad, 0xdd, 0x33, 0x8b, 0x39, 0xc5, 0x23, 0xd7, 0xb6, 0xc7, 0x76, 0xcf,
	0x34, 0x5a, 0x36, 0xd4, 0x6e, 0x93, 0x4c, 0x5e, 0xc2, 0x73, 0xfb, 0x83, 0x7d, 0x3a, 0xf6, 0xc6,
	0x1f, 0x87, 0xf6, 0x8a, 0x95, 0x57, 0x60, 0xe5, 0x64, 0x47, 0x9d, 0x13, 0xdb, 0x1b, 0x76, 0x8e,
	0xbd, 0x6e, 0xc7, 0xed, 0x99, 0x85, 0xfd, 0x2f, 0xc0, 0x18, 0xb1, 0x6b, 0x52, 0x01, 0xa3, 0xc3,
	0xb9, 0xf9, 0x88, 0x54, 0xa1, 0x74, 0x42, 0x39, 0x33, 0x0b, 0x72, 0xfe, 0xeb, 0xb3, 0xb9, 0xfc,
	0x2e, 0xee, 0x33, 0x68, 0xde, 0x9b, 0xc7, 0x70, 0x20, 0x64, 0x53, 0xba, 0xe4, 0xc2, 0x7c, 0x44,
	0x1a, 0x50, 0xfd, 0x36, 0x16, 0x28, 0x34, 0x0b, 0xd2, 0x59, 0xfc, 0x1c, 0x4c, 0x75, 0x73, 0x54,
	0x01, 0x68, 0x6c, 0xac, 0x26, 0x25, 0xd3, 0x20, 0x1b, 0xd0, 0xd4, 0xd8, 0x11, 0x8f, 0x27, 0x94,
	0x9b, 0xa5, 0xfd, 0x8f, 0x40, 0x3e, 0xfd, 0x3d, 0x45, 0x2c, 0xd8, 0xec, 0x39, 0xa3, 0xe1, 0x71,
	0xe7, 0xa3, 0xd7, 0x19, 0x7b, 0x27, 0x1d, 0xe7, 0xd4, 0x1b, 0x76, 0x8e, 0x6c, 0xf3, 0x91, 0x0c,
	0x3b, 0x27, 0xe9, 0x3b, 0xa7, 0x3d, 0xaf, 0xef, 0x3a, 0xf6, 0xa9, 0xcc, 0x59, 0x13, 0x6a, 0x63,
	0xfb, 0x64, 0x38, 0x70, 0x3b, 0xee, 0x47, 0xd3, 0xd8, 0xff, 0x1a, 0xca, 0x6a, 0xae, 0xca, 0x86,
	0x5c, 0xa7, 0x73, 0xbc, 0x32, 0xe4, 0x02, 0x94, 0x55, 0xce, 0xcd, 0xe2, 0xfe, 0x5f, 0x8b, 0xb0,
	0xb9, 0x6e, 0x58, 0x20, 0x2f, 0xe0, 0x59, 0x7e, 0xed, 0x44, 0x97, 0x94, 0x87, 0x81, 0x4a, 0x73,
	0x5e, 0xa0, 0xe3, 0x18, 0x5c, 0x45, 0x2c, 0x31, 0x0b, 0xd2, 0xd3, 0x35, 0xd2, 0x31, 0x9d, 0x98,
	0x45, 0xb2, 0x03, 0x2f, 0xf2, 0x32, 0x9d, 0xb5, 0x6e, 0x1c, 0x05, 0xa6, 0xb1, 0x2a, 0xd4, 0x1b,
	0x31, 0x85, 0x25, 0xf2, 0x06, 0x5e, 0xe6, 0x85, 0x68, 0xcc, 0x89, 0xb4, 0x8e, 0xf9, 0x98, 0xbc,
	0x86, 0xed, 0xbc, 0x5c, 0x3e, 0xc5, 0x77, 0xe2, 0x32, 0xf9, 0x12, 0x7e, 0x92, 0x17, 0xff, 0x6e,
	0x19, 0x8a, 0xb3, 0x28, 0x8c, 0x04, 0x4b, 0x58, 0x2a, 0x58, 0x70, 0xa7, 0x5a, 0x39, 0xec, 0x1e,
	0x02, 0x56, 0xdb, 0x50, 0xd2, 0xf0, 0x0f, 0xef, 0x67, 0x31, 0xa7, 0xd1, 0xac, 0xfd, 0xfe, 0x9d,
	0x10, 0x6d, 0x3f, 0x9e, 0xab, 0xff, 0x37, 0xf8, 0x31, 0x3f, 0x48, 0x59, 0x72, 0x19, 0xfa, 0x2c,
	0x5d, 0xf7, 0x8f, 0x8b, 0x49, 0x19, 0xd5, 0xbe, 0xf9, 0x4f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xf3,
	0xd9, 0x81, 0x80, 0xe5, 0x10, 0x00, 0x00,
}
