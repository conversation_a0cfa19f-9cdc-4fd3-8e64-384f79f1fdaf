// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/topic_channel/rcmd_channel_label.proto

package rcmd_channel_label // import "golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type BusinessType int32

const (
	BusinessType_BusinessTypeDefault               BusinessType = 0
	BusinessType_BusinessTypeLabelGameCard         BusinessType = 1
	BusinessType_BusinessTypeBlockOptionToGameCard BusinessType = 2
)

var BusinessType_name = map[int32]string{
	0: "BusinessTypeDefault",
	1: "BusinessTypeLabelGameCard",
	2: "BusinessTypeBlockOptionToGameCard",
}
var BusinessType_value = map[string]int32{
	"BusinessTypeDefault":               0,
	"BusinessTypeLabelGameCard":         1,
	"BusinessTypeBlockOptionToGameCard": 2,
}

func (x BusinessType) String() string {
	return proto.EnumName(BusinessType_name, int32(x))
}
func (BusinessType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{0}
}

type GameLabelType int32

const (
	GameLabelType_Default         GameLabelType = 0
	GameLabelType_HotLabel        GameLabelType = 1
	GameLabelType_LabelOfPublish  GameLabelType = 2
	GameLabelType_LabelOfTabName  GameLabelType = 3
	GameLabelType_LabelOfGlobal   GameLabelType = 4
	GameLabelType_LabelOfClassify GameLabelType = 5
)

var GameLabelType_name = map[int32]string{
	0: "Default",
	1: "HotLabel",
	2: "LabelOfPublish",
	3: "LabelOfTabName",
	4: "LabelOfGlobal",
	5: "LabelOfClassify",
}
var GameLabelType_value = map[string]int32{
	"Default":         0,
	"HotLabel":        1,
	"LabelOfPublish":  2,
	"LabelOfTabName":  3,
	"LabelOfGlobal":   4,
	"LabelOfClassify": 5,
}

func (x GameLabelType) String() string {
	return proto.EnumName(GameLabelType_name, int32(x))
}
func (GameLabelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{1}
}

type RefreshGameLabelReq_Source int32

const (
	RefreshGameLabelReq_SourceInvalid             RefreshGameLabelReq_Source = 0
	RefreshGameLabelReq_SourceRefreshClientCache  RefreshGameLabelReq_Source = 1
	RefreshGameLabelReq_SourceRefreshServerLabels RefreshGameLabelReq_Source = 2
)

var RefreshGameLabelReq_Source_name = map[int32]string{
	0: "SourceInvalid",
	1: "SourceRefreshClientCache",
	2: "SourceRefreshServerLabels",
}
var RefreshGameLabelReq_Source_value = map[string]int32{
	"SourceInvalid":             0,
	"SourceRefreshClientCache":  1,
	"SourceRefreshServerLabels": 2,
}

func (x RefreshGameLabelReq_Source) String() string {
	return proto.EnumName(RefreshGameLabelReq_Source_name, int32(x))
}
func (RefreshGameLabelReq_Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{2, 0}
}

type TextPair_TextType int32

const (
	TextPair_TextTypeDefault        TextPair_TextType = 0
	TextPair_TextTypeMatchTeam      TextPair_TextType = 1
	TextPair_TextTypeOfficeQuestion TextPair_TextType = 2
	TextPair_TextTypeCustomQuestion TextPair_TextType = 3
	TextPair_TextTypeAgeQuestion    TextPair_TextType = 4
)

var TextPair_TextType_name = map[int32]string{
	0: "TextTypeDefault",
	1: "TextTypeMatchTeam",
	2: "TextTypeOfficeQuestion",
	3: "TextTypeCustomQuestion",
	4: "TextTypeAgeQuestion",
}
var TextPair_TextType_value = map[string]int32{
	"TextTypeDefault":        0,
	"TextTypeMatchTeam":      1,
	"TextTypeOfficeQuestion": 2,
	"TextTypeCustomQuestion": 3,
	"TextTypeAgeQuestion":    4,
}

func (x TextPair_TextType) String() string {
	return proto.EnumName(TextPair_TextType_name, int32(x))
}
func (TextPair_TextType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{13, 0}
}

type CutAndMatchLabelReq_CutType int32

const (
	CutAndMatchLabelReq_Jieba   CutAndMatchLabelReq_CutType = 0
	CutAndMatchLabelReq_Contain CutAndMatchLabelReq_CutType = 1
)

var CutAndMatchLabelReq_CutType_name = map[int32]string{
	0: "Jieba",
	1: "Contain",
}
var CutAndMatchLabelReq_CutType_value = map[string]int32{
	"Jieba":   0,
	"Contain": 1,
}

func (x CutAndMatchLabelReq_CutType) String() string {
	return proto.EnumName(CutAndMatchLabelReq_CutType_name, int32(x))
}
func (CutAndMatchLabelReq_CutType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{16, 0}
}

type RecommendationOption_Type int32

const (
	RecommendationOption_Default    RecommendationOption_Type = 0
	RecommendationOption_Publish    RecommendationOption_Type = 1
	RecommendationOption_ThirdLabel RecommendationOption_Type = 2
)

var RecommendationOption_Type_name = map[int32]string{
	0: "Default",
	1: "Publish",
	2: "ThirdLabel",
}
var RecommendationOption_Type_value = map[string]int32{
	"Default":    0,
	"Publish":    1,
	"ThirdLabel": 2,
}

func (x RecommendationOption_Type) String() string {
	return proto.EnumName(RecommendationOption_Type_name, int32(x))
}
func (RecommendationOption_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{22, 0}
}

type SearchGameNameReq_SourceType int32

const (
	SearchGameNameReq_SearchGuideSource  SearchGameNameReq_SourceType = 0
	SearchGameNameReq_RCMDGameListSource SearchGameNameReq_SourceType = 1
	SearchGameNameReq_TopicChannelSource SearchGameNameReq_SourceType = 2
	SearchGameNameReq_SearchPostSource   SearchGameNameReq_SourceType = 3
)

var SearchGameNameReq_SourceType_name = map[int32]string{
	0: "SearchGuideSource",
	1: "RCMDGameListSource",
	2: "TopicChannelSource",
	3: "SearchPostSource",
}
var SearchGameNameReq_SourceType_value = map[string]int32{
	"SearchGuideSource":  0,
	"RCMDGameListSource": 1,
	"TopicChannelSource": 2,
	"SearchPostSource":   3,
}

func (x SearchGameNameReq_SourceType) String() string {
	return proto.EnumName(SearchGameNameReq_SourceType_name, int32(x))
}
func (SearchGameNameReq_SourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{25, 0}
}

type ConvertGameLabelsReq_SourceType int32

const (
	ConvertGameLabelsReq_DEFAULT         ConvertGameLabelsReq_SourceType = 0
	ConvertGameLabelsReq_GameChannelList ConvertGameLabelsReq_SourceType = 1
	ConvertGameLabelsReq_QuestionPopWin  ConvertGameLabelsReq_SourceType = 2
)

var ConvertGameLabelsReq_SourceType_name = map[int32]string{
	0: "DEFAULT",
	1: "GameChannelList",
	2: "QuestionPopWin",
}
var ConvertGameLabelsReq_SourceType_value = map[string]int32{
	"DEFAULT":         0,
	"GameChannelList": 1,
	"QuestionPopWin":  2,
}

func (x ConvertGameLabelsReq_SourceType) String() string {
	return proto.EnumName(ConvertGameLabelsReq_SourceType_name, int32(x))
}
func (ConvertGameLabelsReq_SourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{41, 0}
}

type GetSearchHintResp_HintType int32

const (
	GetSearchHintResp_DEFAULT GetSearchHintResp_HintType = 0
	GetSearchHintResp_RCMD    GetSearchHintResp_HintType = 1
)

var GetSearchHintResp_HintType_name = map[int32]string{
	0: "DEFAULT",
	1: "RCMD",
}
var GetSearchHintResp_HintType_value = map[string]int32{
	"DEFAULT": 0,
	"RCMD":    1,
}

func (x GetSearchHintResp_HintType) String() string {
	return proto.EnumName(GetSearchHintResp_HintType_name, int32(x))
}
func (GetSearchHintResp_HintType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{43, 0}
}

type TabItem_TabType int32

const (
	TabItem_TabTypeSingleTab     TabItem_TabType = 0
	TabItem_TabTypeMtCombTab     TabItem_TabType = 1
	TabItem_TabTypeGangUpCombTab TabItem_TabType = 2
)

var TabItem_TabType_name = map[int32]string{
	0: "TabTypeSingleTab",
	1: "TabTypeMtCombTab",
	2: "TabTypeGangUpCombTab",
}
var TabItem_TabType_value = map[string]int32{
	"TabTypeSingleTab":     0,
	"TabTypeMtCombTab":     1,
	"TabTypeGangUpCombTab": 2,
}

func (x TabItem_TabType) String() string {
	return proto.EnumName(TabItem_TabType_name, int32(x))
}
func (TabItem_TabType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{48, 0}
}

type CutWordParam_SegType int32

const (
	CutWordParam_SegTypeDefalt      CutWordParam_SegType = 0
	CutWordParam_SegTypeNormal      CutWordParam_SegType = 1
	CutWordParam_SegTypeLabel       CutWordParam_SegType = 2
	CutWordParam_SegTypeBusinessSeg CutWordParam_SegType = 3
)

var CutWordParam_SegType_name = map[int32]string{
	0: "SegTypeDefalt",
	1: "SegTypeNormal",
	2: "SegTypeLabel",
	3: "SegTypeBusinessSeg",
}
var CutWordParam_SegType_value = map[string]int32{
	"SegTypeDefalt":      0,
	"SegTypeNormal":      1,
	"SegTypeLabel":       2,
	"SegTypeBusinessSeg": 3,
}

func (x CutWordParam_SegType) String() string {
	return proto.EnumName(CutWordParam_SegType_name, int32(x))
}
func (CutWordParam_SegType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{55, 0}
}

type LabelDetail_LocType int32

const (
	LabelDetail_LocType_NOTHING        LabelDetail_LocType = 0
	LabelDetail_LocType_SAME_LOC_MATCH LabelDetail_LocType = 1
	LabelDetail_LocType_LOC            LabelDetail_LocType = 2
)

var LabelDetail_LocType_name = map[int32]string{
	0: "LocType_NOTHING",
	1: "LocType_SAME_LOC_MATCH",
	2: "LocType_LOC",
}
var LabelDetail_LocType_value = map[string]int32{
	"LocType_NOTHING":        0,
	"LocType_SAME_LOC_MATCH": 1,
	"LocType_LOC":            2,
}

func (x LabelDetail_LocType) String() string {
	return proto.EnumName(LabelDetail_LocType_name, int32(x))
}
func (LabelDetail_LocType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{59, 0}
}

type LabelDetail_GeoType int32

const (
	LabelDetail_GeoType_INVALID  LabelDetail_GeoType = 0
	LabelDetail_GeoType_PROVINCE LabelDetail_GeoType = 1
	LabelDetail_GeoType_CITY     LabelDetail_GeoType = 2
)

var LabelDetail_GeoType_name = map[int32]string{
	0: "GeoType_INVALID",
	1: "GeoType_PROVINCE",
	2: "GeoType_CITY",
}
var LabelDetail_GeoType_value = map[string]int32{
	"GeoType_INVALID":  0,
	"GeoType_PROVINCE": 1,
	"GeoType_CITY":     2,
}

func (x LabelDetail_GeoType) String() string {
	return proto.EnumName(LabelDetail_GeoType_name, int32(x))
}
func (LabelDetail_GeoType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{59, 1}
}

type GetFilterBlockOptionListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabIds               []uint32 `protobuf:"varint,2,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFilterBlockOptionListReq) Reset()         { *m = GetFilterBlockOptionListReq{} }
func (m *GetFilterBlockOptionListReq) String() string { return proto.CompactTextString(m) }
func (*GetFilterBlockOptionListReq) ProtoMessage()    {}
func (*GetFilterBlockOptionListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{0}
}
func (m *GetFilterBlockOptionListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterBlockOptionListReq.Unmarshal(m, b)
}
func (m *GetFilterBlockOptionListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterBlockOptionListReq.Marshal(b, m, deterministic)
}
func (dst *GetFilterBlockOptionListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterBlockOptionListReq.Merge(dst, src)
}
func (m *GetFilterBlockOptionListReq) XXX_Size() int {
	return xxx_messageInfo_GetFilterBlockOptionListReq.Size(m)
}
func (m *GetFilterBlockOptionListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterBlockOptionListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterBlockOptionListReq proto.InternalMessageInfo

func (m *GetFilterBlockOptionListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFilterBlockOptionListReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type GetFilterBlockOptionListResp struct {
	BlockOptionList      []*GameBlockOption `protobuf:"bytes,1,rep,name=block_option_list,json=blockOptionList,proto3" json:"block_option_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetFilterBlockOptionListResp) Reset()         { *m = GetFilterBlockOptionListResp{} }
func (m *GetFilterBlockOptionListResp) String() string { return proto.CompactTextString(m) }
func (*GetFilterBlockOptionListResp) ProtoMessage()    {}
func (*GetFilterBlockOptionListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{1}
}
func (m *GetFilterBlockOptionListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFilterBlockOptionListResp.Unmarshal(m, b)
}
func (m *GetFilterBlockOptionListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFilterBlockOptionListResp.Marshal(b, m, deterministic)
}
func (dst *GetFilterBlockOptionListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFilterBlockOptionListResp.Merge(dst, src)
}
func (m *GetFilterBlockOptionListResp) XXX_Size() int {
	return xxx_messageInfo_GetFilterBlockOptionListResp.Size(m)
}
func (m *GetFilterBlockOptionListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFilterBlockOptionListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFilterBlockOptionListResp proto.InternalMessageInfo

func (m *GetFilterBlockOptionListResp) GetBlockOptionList() []*GameBlockOption {
	if m != nil {
		return m.BlockOptionList
	}
	return nil
}

type RefreshGameLabelReq struct {
	Uid                  uint32                     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32                     `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	GameLabels           []*GameLabel               `protobuf:"bytes,3,rep,name=game_labels,json=gameLabels,proto3" json:"game_labels,omitempty"`
	RefreshSource        RefreshGameLabelReq_Source `protobuf:"varint,4,opt,name=refresh_source,json=refreshSource,proto3,enum=topic_channel.rcmd_channel_label.RefreshGameLabelReq_Source" json:"refresh_source,omitempty"`
	TabIds               []uint32                   `protobuf:"varint,5,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *RefreshGameLabelReq) Reset()         { *m = RefreshGameLabelReq{} }
func (m *RefreshGameLabelReq) String() string { return proto.CompactTextString(m) }
func (*RefreshGameLabelReq) ProtoMessage()    {}
func (*RefreshGameLabelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{2}
}
func (m *RefreshGameLabelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefreshGameLabelReq.Unmarshal(m, b)
}
func (m *RefreshGameLabelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefreshGameLabelReq.Marshal(b, m, deterministic)
}
func (dst *RefreshGameLabelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefreshGameLabelReq.Merge(dst, src)
}
func (m *RefreshGameLabelReq) XXX_Size() int {
	return xxx_messageInfo_RefreshGameLabelReq.Size(m)
}
func (m *RefreshGameLabelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RefreshGameLabelReq.DiscardUnknown(m)
}

var xxx_messageInfo_RefreshGameLabelReq proto.InternalMessageInfo

func (m *RefreshGameLabelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RefreshGameLabelReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *RefreshGameLabelReq) GetGameLabels() []*GameLabel {
	if m != nil {
		return m.GameLabels
	}
	return nil
}

func (m *RefreshGameLabelReq) GetRefreshSource() RefreshGameLabelReq_Source {
	if m != nil {
		return m.RefreshSource
	}
	return RefreshGameLabelReq_SourceInvalid
}

func (m *RefreshGameLabelReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type RefreshGameLabelResp struct {
	GameLabels           []*GameLabel `protobuf:"bytes,1,rep,name=game_labels,json=gameLabels,proto3" json:"game_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RefreshGameLabelResp) Reset()         { *m = RefreshGameLabelResp{} }
func (m *RefreshGameLabelResp) String() string { return proto.CompactTextString(m) }
func (*RefreshGameLabelResp) ProtoMessage()    {}
func (*RefreshGameLabelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{3}
}
func (m *RefreshGameLabelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefreshGameLabelResp.Unmarshal(m, b)
}
func (m *RefreshGameLabelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefreshGameLabelResp.Marshal(b, m, deterministic)
}
func (dst *RefreshGameLabelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefreshGameLabelResp.Merge(dst, src)
}
func (m *RefreshGameLabelResp) XXX_Size() int {
	return xxx_messageInfo_RefreshGameLabelResp.Size(m)
}
func (m *RefreshGameLabelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RefreshGameLabelResp.DiscardUnknown(m)
}

var xxx_messageInfo_RefreshGameLabelResp proto.InternalMessageInfo

func (m *RefreshGameLabelResp) GetGameLabels() []*GameLabel {
	if m != nil {
		return m.GameLabels
	}
	return nil
}

type GetBusinessLabelRelationReq struct {
	BusinessType         []BusinessType `protobuf:"varint,1,rep,packed,name=business_type,json=businessType,proto3,enum=topic_channel.rcmd_channel_label.BusinessType" json:"business_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetBusinessLabelRelationReq) Reset()         { *m = GetBusinessLabelRelationReq{} }
func (m *GetBusinessLabelRelationReq) String() string { return proto.CompactTextString(m) }
func (*GetBusinessLabelRelationReq) ProtoMessage()    {}
func (*GetBusinessLabelRelationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{4}
}
func (m *GetBusinessLabelRelationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessLabelRelationReq.Unmarshal(m, b)
}
func (m *GetBusinessLabelRelationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessLabelRelationReq.Marshal(b, m, deterministic)
}
func (dst *GetBusinessLabelRelationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessLabelRelationReq.Merge(dst, src)
}
func (m *GetBusinessLabelRelationReq) XXX_Size() int {
	return xxx_messageInfo_GetBusinessLabelRelationReq.Size(m)
}
func (m *GetBusinessLabelRelationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessLabelRelationReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessLabelRelationReq proto.InternalMessageInfo

func (m *GetBusinessLabelRelationReq) GetBusinessType() []BusinessType {
	if m != nil {
		return m.BusinessType
	}
	return nil
}

type BusinessLabelRelation struct {
	BusinessType BusinessType `protobuf:"varint,1,opt,name=business_type,json=businessType,proto3,enum=topic_channel.rcmd_channel_label.BusinessType" json:"business_type,omitempty"`
	// 根据BusinessType解析：
	// BusinessTypeLabelGameCard->LabelGameCardRelation,
	// BusinessTypeBlockOptionToGameCard->BlockOptionGameCardRelation
	Data                 []byte   `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BusinessLabelRelation) Reset()         { *m = BusinessLabelRelation{} }
func (m *BusinessLabelRelation) String() string { return proto.CompactTextString(m) }
func (*BusinessLabelRelation) ProtoMessage()    {}
func (*BusinessLabelRelation) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{5}
}
func (m *BusinessLabelRelation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BusinessLabelRelation.Unmarshal(m, b)
}
func (m *BusinessLabelRelation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BusinessLabelRelation.Marshal(b, m, deterministic)
}
func (dst *BusinessLabelRelation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BusinessLabelRelation.Merge(dst, src)
}
func (m *BusinessLabelRelation) XXX_Size() int {
	return xxx_messageInfo_BusinessLabelRelation.Size(m)
}
func (m *BusinessLabelRelation) XXX_DiscardUnknown() {
	xxx_messageInfo_BusinessLabelRelation.DiscardUnknown(m)
}

var xxx_messageInfo_BusinessLabelRelation proto.InternalMessageInfo

func (m *BusinessLabelRelation) GetBusinessType() BusinessType {
	if m != nil {
		return m.BusinessType
	}
	return BusinessType_BusinessTypeDefault
}

func (m *BusinessLabelRelation) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type GameCardOpt struct {
	GameCardId           uint32   `protobuf:"varint,1,opt,name=game_card_id,json=gameCardId,proto3" json:"game_card_id,omitempty"`
	OptId                uint32   `protobuf:"varint,2,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	ConfValue            string   `protobuf:"bytes,3,opt,name=conf_value,json=confValue,proto3" json:"conf_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameCardOpt) Reset()         { *m = GameCardOpt{} }
func (m *GameCardOpt) String() string { return proto.CompactTextString(m) }
func (*GameCardOpt) ProtoMessage()    {}
func (*GameCardOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{6}
}
func (m *GameCardOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameCardOpt.Unmarshal(m, b)
}
func (m *GameCardOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameCardOpt.Marshal(b, m, deterministic)
}
func (dst *GameCardOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameCardOpt.Merge(dst, src)
}
func (m *GameCardOpt) XXX_Size() int {
	return xxx_messageInfo_GameCardOpt.Size(m)
}
func (m *GameCardOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_GameCardOpt.DiscardUnknown(m)
}

var xxx_messageInfo_GameCardOpt proto.InternalMessageInfo

func (m *GameCardOpt) GetGameCardId() uint32 {
	if m != nil {
		return m.GameCardId
	}
	return 0
}

func (m *GameCardOpt) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *GameCardOpt) GetConfValue() string {
	if m != nil {
		return m.ConfValue
	}
	return ""
}

type GameCardLabel struct {
	Label                string         `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	OptConfValueList     []*GameCardOpt `protobuf:"bytes,2,rep,name=opt_conf_value_list,json=optConfValueList,proto3" json:"opt_conf_value_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GameCardLabel) Reset()         { *m = GameCardLabel{} }
func (m *GameCardLabel) String() string { return proto.CompactTextString(m) }
func (*GameCardLabel) ProtoMessage()    {}
func (*GameCardLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{7}
}
func (m *GameCardLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameCardLabel.Unmarshal(m, b)
}
func (m *GameCardLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameCardLabel.Marshal(b, m, deterministic)
}
func (dst *GameCardLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameCardLabel.Merge(dst, src)
}
func (m *GameCardLabel) XXX_Size() int {
	return xxx_messageInfo_GameCardLabel.Size(m)
}
func (m *GameCardLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_GameCardLabel.DiscardUnknown(m)
}

var xxx_messageInfo_GameCardLabel proto.InternalMessageInfo

func (m *GameCardLabel) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

func (m *GameCardLabel) GetOptConfValueList() []*GameCardOpt {
	if m != nil {
		return m.OptConfValueList
	}
	return nil
}

type BlockOptionGameCard struct {
	TabId                uint32         `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOpt             *BlockOption   `protobuf:"bytes,2,opt,name=block_opt,json=blockOpt,proto3" json:"block_opt,omitempty"`
	OptConfValueList     []*GameCardOpt `protobuf:"bytes,3,rep,name=opt_conf_value_list,json=optConfValueList,proto3" json:"opt_conf_value_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BlockOptionGameCard) Reset()         { *m = BlockOptionGameCard{} }
func (m *BlockOptionGameCard) String() string { return proto.CompactTextString(m) }
func (*BlockOptionGameCard) ProtoMessage()    {}
func (*BlockOptionGameCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{8}
}
func (m *BlockOptionGameCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOptionGameCard.Unmarshal(m, b)
}
func (m *BlockOptionGameCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOptionGameCard.Marshal(b, m, deterministic)
}
func (dst *BlockOptionGameCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOptionGameCard.Merge(dst, src)
}
func (m *BlockOptionGameCard) XXX_Size() int {
	return xxx_messageInfo_BlockOptionGameCard.Size(m)
}
func (m *BlockOptionGameCard) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOptionGameCard.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOptionGameCard proto.InternalMessageInfo

func (m *BlockOptionGameCard) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *BlockOptionGameCard) GetBlockOpt() *BlockOption {
	if m != nil {
		return m.BlockOpt
	}
	return nil
}

func (m *BlockOptionGameCard) GetOptConfValueList() []*GameCardOpt {
	if m != nil {
		return m.OptConfValueList
	}
	return nil
}

type LabelGameCardRelation struct {
	GameCardLabel        []*GameCardLabel `protobuf:"bytes,1,rep,name=game_card_label,json=gameCardLabel,proto3" json:"game_card_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *LabelGameCardRelation) Reset()         { *m = LabelGameCardRelation{} }
func (m *LabelGameCardRelation) String() string { return proto.CompactTextString(m) }
func (*LabelGameCardRelation) ProtoMessage()    {}
func (*LabelGameCardRelation) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{9}
}
func (m *LabelGameCardRelation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LabelGameCardRelation.Unmarshal(m, b)
}
func (m *LabelGameCardRelation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LabelGameCardRelation.Marshal(b, m, deterministic)
}
func (dst *LabelGameCardRelation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelGameCardRelation.Merge(dst, src)
}
func (m *LabelGameCardRelation) XXX_Size() int {
	return xxx_messageInfo_LabelGameCardRelation.Size(m)
}
func (m *LabelGameCardRelation) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelGameCardRelation.DiscardUnknown(m)
}

var xxx_messageInfo_LabelGameCardRelation proto.InternalMessageInfo

func (m *LabelGameCardRelation) GetGameCardLabel() []*GameCardLabel {
	if m != nil {
		return m.GameCardLabel
	}
	return nil
}

type BlockOptionGameCardRelation struct {
	BlockOptionGameCardList []*BlockOptionGameCard `protobuf:"bytes,1,rep,name=block_option_game_card_list,json=blockOptionGameCardList,proto3" json:"block_option_game_card_list,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}               `json:"-"`
	XXX_unrecognized        []byte                 `json:"-"`
	XXX_sizecache           int32                  `json:"-"`
}

func (m *BlockOptionGameCardRelation) Reset()         { *m = BlockOptionGameCardRelation{} }
func (m *BlockOptionGameCardRelation) String() string { return proto.CompactTextString(m) }
func (*BlockOptionGameCardRelation) ProtoMessage()    {}
func (*BlockOptionGameCardRelation) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{10}
}
func (m *BlockOptionGameCardRelation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOptionGameCardRelation.Unmarshal(m, b)
}
func (m *BlockOptionGameCardRelation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOptionGameCardRelation.Marshal(b, m, deterministic)
}
func (dst *BlockOptionGameCardRelation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOptionGameCardRelation.Merge(dst, src)
}
func (m *BlockOptionGameCardRelation) XXX_Size() int {
	return xxx_messageInfo_BlockOptionGameCardRelation.Size(m)
}
func (m *BlockOptionGameCardRelation) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOptionGameCardRelation.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOptionGameCardRelation proto.InternalMessageInfo

func (m *BlockOptionGameCardRelation) GetBlockOptionGameCardList() []*BlockOptionGameCard {
	if m != nil {
		return m.BlockOptionGameCardList
	}
	return nil
}

type GetBusinessLabelRelationResp struct {
	BusinessLabelRelation []*BusinessLabelRelation `protobuf:"bytes,1,rep,name=business_label_relation,json=businessLabelRelation,proto3" json:"business_label_relation,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                 `json:"-"`
	XXX_unrecognized      []byte                   `json:"-"`
	XXX_sizecache         int32                    `json:"-"`
}

func (m *GetBusinessLabelRelationResp) Reset()         { *m = GetBusinessLabelRelationResp{} }
func (m *GetBusinessLabelRelationResp) String() string { return proto.CompactTextString(m) }
func (*GetBusinessLabelRelationResp) ProtoMessage()    {}
func (*GetBusinessLabelRelationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{11}
}
func (m *GetBusinessLabelRelationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessLabelRelationResp.Unmarshal(m, b)
}
func (m *GetBusinessLabelRelationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessLabelRelationResp.Marshal(b, m, deterministic)
}
func (dst *GetBusinessLabelRelationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessLabelRelationResp.Merge(dst, src)
}
func (m *GetBusinessLabelRelationResp) XXX_Size() int {
	return xxx_messageInfo_GetBusinessLabelRelationResp.Size(m)
}
func (m *GetBusinessLabelRelationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessLabelRelationResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessLabelRelationResp proto.InternalMessageInfo

func (m *GetBusinessLabelRelationResp) GetBusinessLabelRelation() []*BusinessLabelRelation {
	if m != nil {
		return m.BusinessLabelRelation
	}
	return nil
}

type MatchGameCardInfoReq struct {
	Uid   uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId uint32 `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	// Deprecated,replace by text_pair_list
	Texts                []string    `protobuf:"bytes,3,rep,name=texts,proto3" json:"texts,omitempty"`
	TextPairList         []*TextPair `protobuf:"bytes,4,rep,name=text_pair_list,json=textPairList,proto3" json:"text_pair_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *MatchGameCardInfoReq) Reset()         { *m = MatchGameCardInfoReq{} }
func (m *MatchGameCardInfoReq) String() string { return proto.CompactTextString(m) }
func (*MatchGameCardInfoReq) ProtoMessage()    {}
func (*MatchGameCardInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{12}
}
func (m *MatchGameCardInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MatchGameCardInfoReq.Unmarshal(m, b)
}
func (m *MatchGameCardInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MatchGameCardInfoReq.Marshal(b, m, deterministic)
}
func (dst *MatchGameCardInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MatchGameCardInfoReq.Merge(dst, src)
}
func (m *MatchGameCardInfoReq) XXX_Size() int {
	return xxx_messageInfo_MatchGameCardInfoReq.Size(m)
}
func (m *MatchGameCardInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MatchGameCardInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_MatchGameCardInfoReq proto.InternalMessageInfo

func (m *MatchGameCardInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MatchGameCardInfoReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *MatchGameCardInfoReq) GetTexts() []string {
	if m != nil {
		return m.Texts
	}
	return nil
}

func (m *MatchGameCardInfoReq) GetTextPairList() []*TextPair {
	if m != nil {
		return m.TextPairList
	}
	return nil
}

type TextPair struct {
	ValueText            []string          `protobuf:"bytes,1,rep,name=value_text,json=valueText,proto3" json:"value_text,omitempty"`
	TitleText            string            `protobuf:"bytes,2,opt,name=title_text,json=titleText,proto3" json:"title_text,omitempty"`
	TextType             TextPair_TextType `protobuf:"varint,3,opt,name=text_type,json=textType,proto3,enum=topic_channel.rcmd_channel_label.TextPair_TextType" json:"text_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *TextPair) Reset()         { *m = TextPair{} }
func (m *TextPair) String() string { return proto.CompactTextString(m) }
func (*TextPair) ProtoMessage()    {}
func (*TextPair) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{13}
}
func (m *TextPair) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TextPair.Unmarshal(m, b)
}
func (m *TextPair) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TextPair.Marshal(b, m, deterministic)
}
func (dst *TextPair) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TextPair.Merge(dst, src)
}
func (m *TextPair) XXX_Size() int {
	return xxx_messageInfo_TextPair.Size(m)
}
func (m *TextPair) XXX_DiscardUnknown() {
	xxx_messageInfo_TextPair.DiscardUnknown(m)
}

var xxx_messageInfo_TextPair proto.InternalMessageInfo

func (m *TextPair) GetValueText() []string {
	if m != nil {
		return m.ValueText
	}
	return nil
}

func (m *TextPair) GetTitleText() string {
	if m != nil {
		return m.TitleText
	}
	return ""
}

func (m *TextPair) GetTextType() TextPair_TextType {
	if m != nil {
		return m.TextType
	}
	return TextPair_TextTypeDefault
}

type GameCardInfo struct {
	OptId                uint32   `protobuf:"varint,1,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	OptValueList         []string `protobuf:"bytes,2,rep,name=opt_value_list,json=optValueList,proto3" json:"opt_value_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameCardInfo) Reset()         { *m = GameCardInfo{} }
func (m *GameCardInfo) String() string { return proto.CompactTextString(m) }
func (*GameCardInfo) ProtoMessage()    {}
func (*GameCardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{14}
}
func (m *GameCardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameCardInfo.Unmarshal(m, b)
}
func (m *GameCardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameCardInfo.Marshal(b, m, deterministic)
}
func (dst *GameCardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameCardInfo.Merge(dst, src)
}
func (m *GameCardInfo) XXX_Size() int {
	return xxx_messageInfo_GameCardInfo.Size(m)
}
func (m *GameCardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameCardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameCardInfo proto.InternalMessageInfo

func (m *GameCardInfo) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *GameCardInfo) GetOptValueList() []string {
	if m != nil {
		return m.OptValueList
	}
	return nil
}

type MatchGameCardInfoResp struct {
	GameCardId           uint32          `protobuf:"varint,1,opt,name=game_card_id,json=gameCardId,proto3" json:"game_card_id,omitempty"`
	GameCardInfo         []*GameCardInfo `protobuf:"bytes,2,rep,name=game_card_info,json=gameCardInfo,proto3" json:"game_card_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *MatchGameCardInfoResp) Reset()         { *m = MatchGameCardInfoResp{} }
func (m *MatchGameCardInfoResp) String() string { return proto.CompactTextString(m) }
func (*MatchGameCardInfoResp) ProtoMessage()    {}
func (*MatchGameCardInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{15}
}
func (m *MatchGameCardInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MatchGameCardInfoResp.Unmarshal(m, b)
}
func (m *MatchGameCardInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MatchGameCardInfoResp.Marshal(b, m, deterministic)
}
func (dst *MatchGameCardInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MatchGameCardInfoResp.Merge(dst, src)
}
func (m *MatchGameCardInfoResp) XXX_Size() int {
	return xxx_messageInfo_MatchGameCardInfoResp.Size(m)
}
func (m *MatchGameCardInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MatchGameCardInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_MatchGameCardInfoResp proto.InternalMessageInfo

func (m *MatchGameCardInfoResp) GetGameCardId() uint32 {
	if m != nil {
		return m.GameCardId
	}
	return 0
}

func (m *MatchGameCardInfoResp) GetGameCardInfo() []*GameCardInfo {
	if m != nil {
		return m.GameCardInfo
	}
	return nil
}

type CutAndMatchLabelReq struct {
	Text                 string                      `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	CutType              CutAndMatchLabelReq_CutType `protobuf:"varint,2,opt,name=cut_type,json=cutType,proto3,enum=topic_channel.rcmd_channel_label.CutAndMatchLabelReq_CutType" json:"cut_type,omitempty"`
	IsNotReturnLabel     bool                        `protobuf:"varint,3,opt,name=is_not_return_label,json=isNotReturnLabel,proto3" json:"is_not_return_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *CutAndMatchLabelReq) Reset()         { *m = CutAndMatchLabelReq{} }
func (m *CutAndMatchLabelReq) String() string { return proto.CompactTextString(m) }
func (*CutAndMatchLabelReq) ProtoMessage()    {}
func (*CutAndMatchLabelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{16}
}
func (m *CutAndMatchLabelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CutAndMatchLabelReq.Unmarshal(m, b)
}
func (m *CutAndMatchLabelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CutAndMatchLabelReq.Marshal(b, m, deterministic)
}
func (dst *CutAndMatchLabelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CutAndMatchLabelReq.Merge(dst, src)
}
func (m *CutAndMatchLabelReq) XXX_Size() int {
	return xxx_messageInfo_CutAndMatchLabelReq.Size(m)
}
func (m *CutAndMatchLabelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CutAndMatchLabelReq.DiscardUnknown(m)
}

var xxx_messageInfo_CutAndMatchLabelReq proto.InternalMessageInfo

func (m *CutAndMatchLabelReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *CutAndMatchLabelReq) GetCutType() CutAndMatchLabelReq_CutType {
	if m != nil {
		return m.CutType
	}
	return CutAndMatchLabelReq_Jieba
}

func (m *CutAndMatchLabelReq) GetIsNotReturnLabel() bool {
	if m != nil {
		return m.IsNotReturnLabel
	}
	return false
}

type CutAndMatchLabelResp struct {
	Labels               []*GameLabel `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty"`
	IsHitLabel           bool         `protobuf:"varint,2,opt,name=is_hit_label,json=isHitLabel,proto3" json:"is_hit_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CutAndMatchLabelResp) Reset()         { *m = CutAndMatchLabelResp{} }
func (m *CutAndMatchLabelResp) String() string { return proto.CompactTextString(m) }
func (*CutAndMatchLabelResp) ProtoMessage()    {}
func (*CutAndMatchLabelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{17}
}
func (m *CutAndMatchLabelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CutAndMatchLabelResp.Unmarshal(m, b)
}
func (m *CutAndMatchLabelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CutAndMatchLabelResp.Marshal(b, m, deterministic)
}
func (dst *CutAndMatchLabelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CutAndMatchLabelResp.Merge(dst, src)
}
func (m *CutAndMatchLabelResp) XXX_Size() int {
	return xxx_messageInfo_CutAndMatchLabelResp.Size(m)
}
func (m *CutAndMatchLabelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CutAndMatchLabelResp.DiscardUnknown(m)
}

var xxx_messageInfo_CutAndMatchLabelResp proto.InternalMessageInfo

func (m *CutAndMatchLabelResp) GetLabels() []*GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *CutAndMatchLabelResp) GetIsHitLabel() bool {
	if m != nil {
		return m.IsHitLabel
	}
	return false
}

type InternalGetAllLabelReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InternalGetAllLabelReq) Reset()         { *m = InternalGetAllLabelReq{} }
func (m *InternalGetAllLabelReq) String() string { return proto.CompactTextString(m) }
func (*InternalGetAllLabelReq) ProtoMessage()    {}
func (*InternalGetAllLabelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{18}
}
func (m *InternalGetAllLabelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InternalGetAllLabelReq.Unmarshal(m, b)
}
func (m *InternalGetAllLabelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InternalGetAllLabelReq.Marshal(b, m, deterministic)
}
func (dst *InternalGetAllLabelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InternalGetAllLabelReq.Merge(dst, src)
}
func (m *InternalGetAllLabelReq) XXX_Size() int {
	return xxx_messageInfo_InternalGetAllLabelReq.Size(m)
}
func (m *InternalGetAllLabelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InternalGetAllLabelReq.DiscardUnknown(m)
}

var xxx_messageInfo_InternalGetAllLabelReq proto.InternalMessageInfo

type InternalGetAllLabelResp struct {
	LabelText            string   `protobuf:"bytes,1,opt,name=label_text,json=labelText,proto3" json:"label_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InternalGetAllLabelResp) Reset()         { *m = InternalGetAllLabelResp{} }
func (m *InternalGetAllLabelResp) String() string { return proto.CompactTextString(m) }
func (*InternalGetAllLabelResp) ProtoMessage()    {}
func (*InternalGetAllLabelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{19}
}
func (m *InternalGetAllLabelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InternalGetAllLabelResp.Unmarshal(m, b)
}
func (m *InternalGetAllLabelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InternalGetAllLabelResp.Marshal(b, m, deterministic)
}
func (dst *InternalGetAllLabelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InternalGetAllLabelResp.Merge(dst, src)
}
func (m *InternalGetAllLabelResp) XXX_Size() int {
	return xxx_messageInfo_InternalGetAllLabelResp.Size(m)
}
func (m *InternalGetAllLabelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InternalGetAllLabelResp.DiscardUnknown(m)
}

var xxx_messageInfo_InternalGetAllLabelResp proto.InternalMessageInfo

func (m *InternalGetAllLabelResp) GetLabelText() string {
	if m != nil {
		return m.LabelText
	}
	return ""
}

// * ----------GetRelatedPublishLabels---------- *
type GetRelatedPublishLabelsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRelatedPublishLabelsReq) Reset()         { *m = GetRelatedPublishLabelsReq{} }
func (m *GetRelatedPublishLabelsReq) String() string { return proto.CompactTextString(m) }
func (*GetRelatedPublishLabelsReq) ProtoMessage()    {}
func (*GetRelatedPublishLabelsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{20}
}
func (m *GetRelatedPublishLabelsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRelatedPublishLabelsReq.Unmarshal(m, b)
}
func (m *GetRelatedPublishLabelsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRelatedPublishLabelsReq.Marshal(b, m, deterministic)
}
func (dst *GetRelatedPublishLabelsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRelatedPublishLabelsReq.Merge(dst, src)
}
func (m *GetRelatedPublishLabelsReq) XXX_Size() int {
	return xxx_messageInfo_GetRelatedPublishLabelsReq.Size(m)
}
func (m *GetRelatedPublishLabelsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRelatedPublishLabelsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRelatedPublishLabelsReq proto.InternalMessageInfo

func (m *GetRelatedPublishLabelsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRelatedPublishLabelsReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetRelatedPublishLabelsReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type RelatedPublishLabel struct {
	BlockOptions         *BlockOption `protobuf:"bytes,1,opt,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	PublishName          string       `protobuf:"bytes,2,opt,name=publish_name,json=publishName,proto3" json:"publish_name,omitempty"`
	Label                []*GameLabel `protobuf:"bytes,3,rep,name=label,proto3" json:"label,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RelatedPublishLabel) Reset()         { *m = RelatedPublishLabel{} }
func (m *RelatedPublishLabel) String() string { return proto.CompactTextString(m) }
func (*RelatedPublishLabel) ProtoMessage()    {}
func (*RelatedPublishLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{21}
}
func (m *RelatedPublishLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelatedPublishLabel.Unmarshal(m, b)
}
func (m *RelatedPublishLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelatedPublishLabel.Marshal(b, m, deterministic)
}
func (dst *RelatedPublishLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelatedPublishLabel.Merge(dst, src)
}
func (m *RelatedPublishLabel) XXX_Size() int {
	return xxx_messageInfo_RelatedPublishLabel.Size(m)
}
func (m *RelatedPublishLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_RelatedPublishLabel.DiscardUnknown(m)
}

var xxx_messageInfo_RelatedPublishLabel proto.InternalMessageInfo

func (m *RelatedPublishLabel) GetBlockOptions() *BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *RelatedPublishLabel) GetPublishName() string {
	if m != nil {
		return m.PublishName
	}
	return ""
}

func (m *RelatedPublishLabel) GetLabel() []*GameLabel {
	if m != nil {
		return m.Label
	}
	return nil
}

// 推荐选择区域
type RecommendationOption struct {
	OptionType           uint32       `protobuf:"varint,1,opt,name=option_type,json=optionType,proto3" json:"option_type,omitempty"`
	Label                *GameLabel   `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	BlockOptions         *BlockOption `protobuf:"bytes,3,opt,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RecommendationOption) Reset()         { *m = RecommendationOption{} }
func (m *RecommendationOption) String() string { return proto.CompactTextString(m) }
func (*RecommendationOption) ProtoMessage()    {}
func (*RecommendationOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{22}
}
func (m *RecommendationOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendationOption.Unmarshal(m, b)
}
func (m *RecommendationOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendationOption.Marshal(b, m, deterministic)
}
func (dst *RecommendationOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendationOption.Merge(dst, src)
}
func (m *RecommendationOption) XXX_Size() int {
	return xxx_messageInfo_RecommendationOption.Size(m)
}
func (m *RecommendationOption) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendationOption.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendationOption proto.InternalMessageInfo

func (m *RecommendationOption) GetOptionType() uint32 {
	if m != nil {
		return m.OptionType
	}
	return 0
}

func (m *RecommendationOption) GetLabel() *GameLabel {
	if m != nil {
		return m.Label
	}
	return nil
}

func (m *RecommendationOption) GetBlockOptions() *BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

// 推荐填写
type RecommendationInput struct {
	BlockOptions         *BlockOption `protobuf:"bytes,1,opt,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RecommendationInput) Reset()         { *m = RecommendationInput{} }
func (m *RecommendationInput) String() string { return proto.CompactTextString(m) }
func (*RecommendationInput) ProtoMessage()    {}
func (*RecommendationInput) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{23}
}
func (m *RecommendationInput) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendationInput.Unmarshal(m, b)
}
func (m *RecommendationInput) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendationInput.Marshal(b, m, deterministic)
}
func (dst *RecommendationInput) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendationInput.Merge(dst, src)
}
func (m *RecommendationInput) XXX_Size() int {
	return xxx_messageInfo_RecommendationInput.Size(m)
}
func (m *RecommendationInput) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendationInput.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendationInput proto.InternalMessageInfo

func (m *RecommendationInput) GetBlockOptions() *BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

// 661
type GetRelatedPublishLabelsResp struct {
	PublishLabel          []*RelatedPublishLabel  `protobuf:"bytes,1,rep,name=publish_label,json=publishLabel,proto3" json:"publish_label,omitempty"`
	RecommendationOptions []*RecommendationOption `protobuf:"bytes,2,rep,name=recommendation_options,json=recommendationOptions,proto3" json:"recommendation_options,omitempty"`
	RecommendationInputs  []*RecommendationInput  `protobuf:"bytes,3,rep,name=recommendation_inputs,json=recommendationInputs,proto3" json:"recommendation_inputs,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                `json:"-"`
	XXX_unrecognized      []byte                  `json:"-"`
	XXX_sizecache         int32                   `json:"-"`
}

func (m *GetRelatedPublishLabelsResp) Reset()         { *m = GetRelatedPublishLabelsResp{} }
func (m *GetRelatedPublishLabelsResp) String() string { return proto.CompactTextString(m) }
func (*GetRelatedPublishLabelsResp) ProtoMessage()    {}
func (*GetRelatedPublishLabelsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{24}
}
func (m *GetRelatedPublishLabelsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRelatedPublishLabelsResp.Unmarshal(m, b)
}
func (m *GetRelatedPublishLabelsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRelatedPublishLabelsResp.Marshal(b, m, deterministic)
}
func (dst *GetRelatedPublishLabelsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRelatedPublishLabelsResp.Merge(dst, src)
}
func (m *GetRelatedPublishLabelsResp) XXX_Size() int {
	return xxx_messageInfo_GetRelatedPublishLabelsResp.Size(m)
}
func (m *GetRelatedPublishLabelsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRelatedPublishLabelsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRelatedPublishLabelsResp proto.InternalMessageInfo

func (m *GetRelatedPublishLabelsResp) GetPublishLabel() []*RelatedPublishLabel {
	if m != nil {
		return m.PublishLabel
	}
	return nil
}

func (m *GetRelatedPublishLabelsResp) GetRecommendationOptions() []*RecommendationOption {
	if m != nil {
		return m.RecommendationOptions
	}
	return nil
}

func (m *GetRelatedPublishLabelsResp) GetRecommendationInputs() []*RecommendationInput {
	if m != nil {
		return m.RecommendationInputs
	}
	return nil
}

// * ----------SearchGameName---------- *
type SearchGameNameReq struct {
	Uid                  uint32                       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Text                 string                       `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	SourceType           SearchGameNameReq_SourceType `protobuf:"varint,3,opt,name=source_type,json=sourceType,proto3,enum=topic_channel.rcmd_channel_label.SearchGameNameReq_SourceType" json:"source_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *SearchGameNameReq) Reset()         { *m = SearchGameNameReq{} }
func (m *SearchGameNameReq) String() string { return proto.CompactTextString(m) }
func (*SearchGameNameReq) ProtoMessage()    {}
func (*SearchGameNameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{25}
}
func (m *SearchGameNameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchGameNameReq.Unmarshal(m, b)
}
func (m *SearchGameNameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchGameNameReq.Marshal(b, m, deterministic)
}
func (dst *SearchGameNameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchGameNameReq.Merge(dst, src)
}
func (m *SearchGameNameReq) XXX_Size() int {
	return xxx_messageInfo_SearchGameNameReq.Size(m)
}
func (m *SearchGameNameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchGameNameReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchGameNameReq proto.InternalMessageInfo

func (m *SearchGameNameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SearchGameNameReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *SearchGameNameReq) GetSourceType() SearchGameNameReq_SourceType {
	if m != nil {
		return m.SourceType
	}
	return SearchGameNameReq_SearchGuideSource
}

type GameInfo struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	Synonyms             []string `protobuf:"bytes,3,rep,name=synonyms,proto3" json:"synonyms,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameInfo) Reset()         { *m = GameInfo{} }
func (m *GameInfo) String() string { return proto.CompactTextString(m) }
func (*GameInfo) ProtoMessage()    {}
func (*GameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{26}
}
func (m *GameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameInfo.Unmarshal(m, b)
}
func (m *GameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameInfo.Marshal(b, m, deterministic)
}
func (dst *GameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameInfo.Merge(dst, src)
}
func (m *GameInfo) XXX_Size() int {
	return xxx_messageInfo_GameInfo.Size(m)
}
func (m *GameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameInfo proto.InternalMessageInfo

func (m *GameInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GameInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GameInfo) GetSynonyms() []string {
	if m != nil {
		return m.Synonyms
	}
	return nil
}

type SearchGameNameResp struct {
	GameLabel            []*GameLabel `protobuf:"bytes,1,rep,name=game_label,json=gameLabel,proto3" json:"game_label,omitempty"`
	GameInfoList         []*GameInfo  `protobuf:"bytes,2,rep,name=game_info_list,json=gameInfoList,proto3" json:"game_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SearchGameNameResp) Reset()         { *m = SearchGameNameResp{} }
func (m *SearchGameNameResp) String() string { return proto.CompactTextString(m) }
func (*SearchGameNameResp) ProtoMessage()    {}
func (*SearchGameNameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{27}
}
func (m *SearchGameNameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchGameNameResp.Unmarshal(m, b)
}
func (m *SearchGameNameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchGameNameResp.Marshal(b, m, deterministic)
}
func (dst *SearchGameNameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchGameNameResp.Merge(dst, src)
}
func (m *SearchGameNameResp) XXX_Size() int {
	return xxx_messageInfo_SearchGameNameResp.Size(m)
}
func (m *SearchGameNameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchGameNameResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchGameNameResp proto.InternalMessageInfo

func (m *SearchGameNameResp) GetGameLabel() []*GameLabel {
	if m != nil {
		return m.GameLabel
	}
	return nil
}

func (m *SearchGameNameResp) GetGameInfoList() []*GameInfo {
	if m != nil {
		return m.GameInfoList
	}
	return nil
}

// * ----------SearchGlobalLabel---------- *
type SearchGlobalLabelReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchGlobalLabelReq) Reset()         { *m = SearchGlobalLabelReq{} }
func (m *SearchGlobalLabelReq) String() string { return proto.CompactTextString(m) }
func (*SearchGlobalLabelReq) ProtoMessage()    {}
func (*SearchGlobalLabelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{28}
}
func (m *SearchGlobalLabelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchGlobalLabelReq.Unmarshal(m, b)
}
func (m *SearchGlobalLabelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchGlobalLabelReq.Marshal(b, m, deterministic)
}
func (dst *SearchGlobalLabelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchGlobalLabelReq.Merge(dst, src)
}
func (m *SearchGlobalLabelReq) XXX_Size() int {
	return xxx_messageInfo_SearchGlobalLabelReq.Size(m)
}
func (m *SearchGlobalLabelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchGlobalLabelReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchGlobalLabelReq proto.InternalMessageInfo

func (m *SearchGlobalLabelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SearchGlobalLabelReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type SearchLabel struct {
	GameName             string         `protobuf:"bytes,1,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	Label                []*GameLabel   `protobuf:"bytes,2,rep,name=label,proto3" json:"label,omitempty"`
	LabelDetail          []*LabelDetail `protobuf:"bytes,3,rep,name=label_detail,json=labelDetail,proto3" json:"label_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SearchLabel) Reset()         { *m = SearchLabel{} }
func (m *SearchLabel) String() string { return proto.CompactTextString(m) }
func (*SearchLabel) ProtoMessage()    {}
func (*SearchLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{29}
}
func (m *SearchLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchLabel.Unmarshal(m, b)
}
func (m *SearchLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchLabel.Marshal(b, m, deterministic)
}
func (dst *SearchLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchLabel.Merge(dst, src)
}
func (m *SearchLabel) XXX_Size() int {
	return xxx_messageInfo_SearchLabel.Size(m)
}
func (m *SearchLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchLabel.DiscardUnknown(m)
}

var xxx_messageInfo_SearchLabel proto.InternalMessageInfo

func (m *SearchLabel) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *SearchLabel) GetLabel() []*GameLabel {
	if m != nil {
		return m.Label
	}
	return nil
}

func (m *SearchLabel) GetLabelDetail() []*LabelDetail {
	if m != nil {
		return m.LabelDetail
	}
	return nil
}

type SearchGlobalLabelResp struct {
	SearchLabelList      []*SearchLabel `protobuf:"bytes,1,rep,name=search_label_list,json=searchLabelList,proto3" json:"search_label_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SearchGlobalLabelResp) Reset()         { *m = SearchGlobalLabelResp{} }
func (m *SearchGlobalLabelResp) String() string { return proto.CompactTextString(m) }
func (*SearchGlobalLabelResp) ProtoMessage()    {}
func (*SearchGlobalLabelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{30}
}
func (m *SearchGlobalLabelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchGlobalLabelResp.Unmarshal(m, b)
}
func (m *SearchGlobalLabelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchGlobalLabelResp.Marshal(b, m, deterministic)
}
func (dst *SearchGlobalLabelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchGlobalLabelResp.Merge(dst, src)
}
func (m *SearchGlobalLabelResp) XXX_Size() int {
	return xxx_messageInfo_SearchGlobalLabelResp.Size(m)
}
func (m *SearchGlobalLabelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchGlobalLabelResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchGlobalLabelResp proto.InternalMessageInfo

func (m *SearchGlobalLabelResp) GetSearchLabelList() []*SearchLabel {
	if m != nil {
		return m.SearchLabelList
	}
	return nil
}

type BlockOption struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemId               uint32   `protobuf:"varint,2,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlockOption) Reset()         { *m = BlockOption{} }
func (m *BlockOption) String() string { return proto.CompactTextString(m) }
func (*BlockOption) ProtoMessage()    {}
func (*BlockOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{31}
}
func (m *BlockOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOption.Unmarshal(m, b)
}
func (m *BlockOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOption.Marshal(b, m, deterministic)
}
func (dst *BlockOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOption.Merge(dst, src)
}
func (m *BlockOption) XXX_Size() int {
	return xxx_messageInfo_BlockOption.Size(m)
}
func (m *BlockOption) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOption.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOption proto.InternalMessageInfo

func (m *BlockOption) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *BlockOption) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

// * ----------GetPublishGameLabels---------- *
type GameBlockOption struct {
	TabId                uint32         `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOptions         []*BlockOption `protobuf:"bytes,2,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GameBlockOption) Reset()         { *m = GameBlockOption{} }
func (m *GameBlockOption) String() string { return proto.CompactTextString(m) }
func (*GameBlockOption) ProtoMessage()    {}
func (*GameBlockOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{32}
}
func (m *GameBlockOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameBlockOption.Unmarshal(m, b)
}
func (m *GameBlockOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameBlockOption.Marshal(b, m, deterministic)
}
func (dst *GameBlockOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameBlockOption.Merge(dst, src)
}
func (m *GameBlockOption) XXX_Size() int {
	return xxx_messageInfo_GameBlockOption.Size(m)
}
func (m *GameBlockOption) XXX_DiscardUnknown() {
	xxx_messageInfo_GameBlockOption.DiscardUnknown(m)
}

var xxx_messageInfo_GameBlockOption proto.InternalMessageInfo

func (m *GameBlockOption) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GameBlockOption) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

type GetPublishGameLabelsReq struct {
	GameBlockOption      []*GameBlockOption `protobuf:"bytes,1,rep,name=game_block_option,json=gameBlockOption,proto3" json:"game_block_option,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetPublishGameLabelsReq) Reset()         { *m = GetPublishGameLabelsReq{} }
func (m *GetPublishGameLabelsReq) String() string { return proto.CompactTextString(m) }
func (*GetPublishGameLabelsReq) ProtoMessage()    {}
func (*GetPublishGameLabelsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{33}
}
func (m *GetPublishGameLabelsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublishGameLabelsReq.Unmarshal(m, b)
}
func (m *GetPublishGameLabelsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublishGameLabelsReq.Marshal(b, m, deterministic)
}
func (dst *GetPublishGameLabelsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublishGameLabelsReq.Merge(dst, src)
}
func (m *GetPublishGameLabelsReq) XXX_Size() int {
	return xxx_messageInfo_GetPublishGameLabelsReq.Size(m)
}
func (m *GetPublishGameLabelsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublishGameLabelsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublishGameLabelsReq proto.InternalMessageInfo

func (m *GetPublishGameLabelsReq) GetGameBlockOption() []*GameBlockOption {
	if m != nil {
		return m.GameBlockOption
	}
	return nil
}

type BlockOptionLabel struct {
	BlockOptions         *BlockOption `protobuf:"bytes,1,opt,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	Label                []*GameLabel `protobuf:"bytes,2,rep,name=label,proto3" json:"label,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BlockOptionLabel) Reset()         { *m = BlockOptionLabel{} }
func (m *BlockOptionLabel) String() string { return proto.CompactTextString(m) }
func (*BlockOptionLabel) ProtoMessage()    {}
func (*BlockOptionLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{34}
}
func (m *BlockOptionLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOptionLabel.Unmarshal(m, b)
}
func (m *BlockOptionLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOptionLabel.Marshal(b, m, deterministic)
}
func (dst *BlockOptionLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOptionLabel.Merge(dst, src)
}
func (m *BlockOptionLabel) XXX_Size() int {
	return xxx_messageInfo_BlockOptionLabel.Size(m)
}
func (m *BlockOptionLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOptionLabel.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOptionLabel proto.InternalMessageInfo

func (m *BlockOptionLabel) GetBlockOptions() *BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *BlockOptionLabel) GetLabel() []*GameLabel {
	if m != nil {
		return m.Label
	}
	return nil
}

type BlockOptionLabelList struct {
	TabId                uint32              `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOptionLabel     []*BlockOptionLabel `protobuf:"bytes,2,rep,name=block_option_label,json=blockOptionLabel,proto3" json:"block_option_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BlockOptionLabelList) Reset()         { *m = BlockOptionLabelList{} }
func (m *BlockOptionLabelList) String() string { return proto.CompactTextString(m) }
func (*BlockOptionLabelList) ProtoMessage()    {}
func (*BlockOptionLabelList) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{35}
}
func (m *BlockOptionLabelList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOptionLabelList.Unmarshal(m, b)
}
func (m *BlockOptionLabelList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOptionLabelList.Marshal(b, m, deterministic)
}
func (dst *BlockOptionLabelList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOptionLabelList.Merge(dst, src)
}
func (m *BlockOptionLabelList) XXX_Size() int {
	return xxx_messageInfo_BlockOptionLabelList.Size(m)
}
func (m *BlockOptionLabelList) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOptionLabelList.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOptionLabelList proto.InternalMessageInfo

func (m *BlockOptionLabelList) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *BlockOptionLabelList) GetBlockOptionLabel() []*BlockOptionLabel {
	if m != nil {
		return m.BlockOptionLabel
	}
	return nil
}

type GetPublishGameLabelsResp struct {
	BlockOptionLabelList []*BlockOptionLabelList `protobuf:"bytes,1,rep,name=block_option_label_list,json=blockOptionLabelList,proto3" json:"block_option_label_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetPublishGameLabelsResp) Reset()         { *m = GetPublishGameLabelsResp{} }
func (m *GetPublishGameLabelsResp) String() string { return proto.CompactTextString(m) }
func (*GetPublishGameLabelsResp) ProtoMessage()    {}
func (*GetPublishGameLabelsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{36}
}
func (m *GetPublishGameLabelsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublishGameLabelsResp.Unmarshal(m, b)
}
func (m *GetPublishGameLabelsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublishGameLabelsResp.Marshal(b, m, deterministic)
}
func (dst *GetPublishGameLabelsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublishGameLabelsResp.Merge(dst, src)
}
func (m *GetPublishGameLabelsResp) XXX_Size() int {
	return xxx_messageInfo_GetPublishGameLabelsResp.Size(m)
}
func (m *GetPublishGameLabelsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublishGameLabelsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublishGameLabelsResp proto.InternalMessageInfo

func (m *GetPublishGameLabelsResp) GetBlockOptionLabelList() []*BlockOptionLabelList {
	if m != nil {
		return m.BlockOptionLabelList
	}
	return nil
}

// * ----------ConvertGameLabels---------- *
type ConvertGameLabel struct {
	Labels               *GameLabel `protobuf:"bytes,1,opt,name=labels,proto3" json:"labels,omitempty"`
	OriginText           string     `protobuf:"bytes,2,opt,name=origin_text,json=originText,proto3" json:"origin_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ConvertGameLabel) Reset()         { *m = ConvertGameLabel{} }
func (m *ConvertGameLabel) String() string { return proto.CompactTextString(m) }
func (*ConvertGameLabel) ProtoMessage()    {}
func (*ConvertGameLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{37}
}
func (m *ConvertGameLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConvertGameLabel.Unmarshal(m, b)
}
func (m *ConvertGameLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConvertGameLabel.Marshal(b, m, deterministic)
}
func (dst *ConvertGameLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConvertGameLabel.Merge(dst, src)
}
func (m *ConvertGameLabel) XXX_Size() int {
	return xxx_messageInfo_ConvertGameLabel.Size(m)
}
func (m *ConvertGameLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_ConvertGameLabel.DiscardUnknown(m)
}

var xxx_messageInfo_ConvertGameLabel proto.InternalMessageInfo

func (m *ConvertGameLabel) GetLabels() *GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *ConvertGameLabel) GetOriginText() string {
	if m != nil {
		return m.OriginText
	}
	return ""
}

type ConvertGameLabelList struct {
	Labels               []*ConvertGameLabel `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty"`
	TabId                uint32              `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ConvertGameLabelList) Reset()         { *m = ConvertGameLabelList{} }
func (m *ConvertGameLabelList) String() string { return proto.CompactTextString(m) }
func (*ConvertGameLabelList) ProtoMessage()    {}
func (*ConvertGameLabelList) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{38}
}
func (m *ConvertGameLabelList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConvertGameLabelList.Unmarshal(m, b)
}
func (m *ConvertGameLabelList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConvertGameLabelList.Marshal(b, m, deterministic)
}
func (dst *ConvertGameLabelList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConvertGameLabelList.Merge(dst, src)
}
func (m *ConvertGameLabelList) XXX_Size() int {
	return xxx_messageInfo_ConvertGameLabelList.Size(m)
}
func (m *ConvertGameLabelList) XXX_DiscardUnknown() {
	xxx_messageInfo_ConvertGameLabelList.DiscardUnknown(m)
}

var xxx_messageInfo_ConvertGameLabelList proto.InternalMessageInfo

func (m *ConvertGameLabelList) GetLabels() []*ConvertGameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *ConvertGameLabelList) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type ConvertGameLabelsResp struct {
	Labels               []*ConvertGameLabelList `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ConvertGameLabelsResp) Reset()         { *m = ConvertGameLabelsResp{} }
func (m *ConvertGameLabelsResp) String() string { return proto.CompactTextString(m) }
func (*ConvertGameLabelsResp) ProtoMessage()    {}
func (*ConvertGameLabelsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{39}
}
func (m *ConvertGameLabelsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConvertGameLabelsResp.Unmarshal(m, b)
}
func (m *ConvertGameLabelsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConvertGameLabelsResp.Marshal(b, m, deterministic)
}
func (dst *ConvertGameLabelsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConvertGameLabelsResp.Merge(dst, src)
}
func (m *ConvertGameLabelsResp) XXX_Size() int {
	return xxx_messageInfo_ConvertGameLabelsResp.Size(m)
}
func (m *ConvertGameLabelsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConvertGameLabelsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConvertGameLabelsResp proto.InternalMessageInfo

func (m *ConvertGameLabelsResp) GetLabels() []*ConvertGameLabelList {
	if m != nil {
		return m.Labels
	}
	return nil
}

type ConvertGameInfo struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Texts                []string `protobuf:"bytes,2,rep,name=texts,proto3" json:"texts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConvertGameInfo) Reset()         { *m = ConvertGameInfo{} }
func (m *ConvertGameInfo) String() string { return proto.CompactTextString(m) }
func (*ConvertGameInfo) ProtoMessage()    {}
func (*ConvertGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{40}
}
func (m *ConvertGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConvertGameInfo.Unmarshal(m, b)
}
func (m *ConvertGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConvertGameInfo.Marshal(b, m, deterministic)
}
func (dst *ConvertGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConvertGameInfo.Merge(dst, src)
}
func (m *ConvertGameInfo) XXX_Size() int {
	return xxx_messageInfo_ConvertGameInfo.Size(m)
}
func (m *ConvertGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ConvertGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ConvertGameInfo proto.InternalMessageInfo

func (m *ConvertGameInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ConvertGameInfo) GetTexts() []string {
	if m != nil {
		return m.Texts
	}
	return nil
}

type ConvertGameLabelsReq struct {
	Uid                  uint32                          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameInfos            []*ConvertGameInfo              `protobuf:"bytes,2,rep,name=game_infos,json=gameInfos,proto3" json:"game_infos,omitempty"`
	Source               ConvertGameLabelsReq_SourceType `protobuf:"varint,3,opt,name=source,proto3,enum=topic_channel.rcmd_channel_label.ConvertGameLabelsReq_SourceType" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *ConvertGameLabelsReq) Reset()         { *m = ConvertGameLabelsReq{} }
func (m *ConvertGameLabelsReq) String() string { return proto.CompactTextString(m) }
func (*ConvertGameLabelsReq) ProtoMessage()    {}
func (*ConvertGameLabelsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{41}
}
func (m *ConvertGameLabelsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConvertGameLabelsReq.Unmarshal(m, b)
}
func (m *ConvertGameLabelsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConvertGameLabelsReq.Marshal(b, m, deterministic)
}
func (dst *ConvertGameLabelsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConvertGameLabelsReq.Merge(dst, src)
}
func (m *ConvertGameLabelsReq) XXX_Size() int {
	return xxx_messageInfo_ConvertGameLabelsReq.Size(m)
}
func (m *ConvertGameLabelsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConvertGameLabelsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConvertGameLabelsReq proto.InternalMessageInfo

func (m *ConvertGameLabelsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConvertGameLabelsReq) GetGameInfos() []*ConvertGameInfo {
	if m != nil {
		return m.GameInfos
	}
	return nil
}

func (m *ConvertGameLabelsReq) GetSource() ConvertGameLabelsReq_SourceType {
	if m != nil {
		return m.Source
	}
	return ConvertGameLabelsReq_DEFAULT
}

type GetSearchHintReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSearchHintReq) Reset()         { *m = GetSearchHintReq{} }
func (m *GetSearchHintReq) String() string { return proto.CompactTextString(m) }
func (*GetSearchHintReq) ProtoMessage()    {}
func (*GetSearchHintReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{42}
}
func (m *GetSearchHintReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchHintReq.Unmarshal(m, b)
}
func (m *GetSearchHintReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchHintReq.Marshal(b, m, deterministic)
}
func (dst *GetSearchHintReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchHintReq.Merge(dst, src)
}
func (m *GetSearchHintReq) XXX_Size() int {
	return xxx_messageInfo_GetSearchHintReq.Size(m)
}
func (m *GetSearchHintReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchHintReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchHintReq proto.InternalMessageInfo

func (m *GetSearchHintReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSearchHintReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetSearchHintResp struct {
	Hint                 string                     `protobuf:"bytes,1,opt,name=hint,proto3" json:"hint,omitempty"`
	Type                 GetSearchHintResp_HintType `protobuf:"varint,2,opt,name=type,proto3,enum=topic_channel.rcmd_channel_label.GetSearchHintResp_HintType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetSearchHintResp) Reset()         { *m = GetSearchHintResp{} }
func (m *GetSearchHintResp) String() string { return proto.CompactTextString(m) }
func (*GetSearchHintResp) ProtoMessage()    {}
func (*GetSearchHintResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{43}
}
func (m *GetSearchHintResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchHintResp.Unmarshal(m, b)
}
func (m *GetSearchHintResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchHintResp.Marshal(b, m, deterministic)
}
func (dst *GetSearchHintResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchHintResp.Merge(dst, src)
}
func (m *GetSearchHintResp) XXX_Size() int {
	return xxx_messageInfo_GetSearchHintResp.Size(m)
}
func (m *GetSearchHintResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchHintResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchHintResp proto.InternalMessageInfo

func (m *GetSearchHintResp) GetHint() string {
	if m != nil {
		return m.Hint
	}
	return ""
}

func (m *GetSearchHintResp) GetType() GetSearchHintResp_HintType {
	if m != nil {
		return m.Type
	}
	return GetSearchHintResp_DEFAULT
}

type LabelSearchReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	TabIds               []uint32 `protobuf:"varint,4,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LabelSearchReq) Reset()         { *m = LabelSearchReq{} }
func (m *LabelSearchReq) String() string { return proto.CompactTextString(m) }
func (*LabelSearchReq) ProtoMessage()    {}
func (*LabelSearchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{44}
}
func (m *LabelSearchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LabelSearchReq.Unmarshal(m, b)
}
func (m *LabelSearchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LabelSearchReq.Marshal(b, m, deterministic)
}
func (dst *LabelSearchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelSearchReq.Merge(dst, src)
}
func (m *LabelSearchReq) XXX_Size() int {
	return xxx_messageInfo_LabelSearchReq.Size(m)
}
func (m *LabelSearchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelSearchReq.DiscardUnknown(m)
}

var xxx_messageInfo_LabelSearchReq proto.InternalMessageInfo

func (m *LabelSearchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LabelSearchReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *LabelSearchReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *LabelSearchReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type LabelSearchResp struct {
	Labels               []*GameLabel `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *LabelSearchResp) Reset()         { *m = LabelSearchResp{} }
func (m *LabelSearchResp) String() string { return proto.CompactTextString(m) }
func (*LabelSearchResp) ProtoMessage()    {}
func (*LabelSearchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{45}
}
func (m *LabelSearchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LabelSearchResp.Unmarshal(m, b)
}
func (m *LabelSearchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LabelSearchResp.Marshal(b, m, deterministic)
}
func (dst *LabelSearchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelSearchResp.Merge(dst, src)
}
func (m *LabelSearchResp) XXX_Size() int {
	return xxx_messageInfo_LabelSearchResp.Size(m)
}
func (m *LabelSearchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelSearchResp.DiscardUnknown(m)
}

var xxx_messageInfo_LabelSearchResp proto.InternalMessageInfo

func (m *LabelSearchResp) GetLabels() []*GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

// 外显label
type GameLabel struct {
	Val         string `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// Deprecated: replace by label_type
	Type                 GameLabelType `protobuf:"varint,3,opt,name=type,proto3,enum=topic_channel.rcmd_channel_label.GameLabelType" json:"type,omitempty"`
	LabelType            uint32        `protobuf:"varint,4,opt,name=label_type,json=labelType,proto3" json:"label_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GameLabel) Reset()         { *m = GameLabel{} }
func (m *GameLabel) String() string { return proto.CompactTextString(m) }
func (*GameLabel) ProtoMessage()    {}
func (*GameLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{46}
}
func (m *GameLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameLabel.Unmarshal(m, b)
}
func (m *GameLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameLabel.Marshal(b, m, deterministic)
}
func (dst *GameLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameLabel.Merge(dst, src)
}
func (m *GameLabel) XXX_Size() int {
	return xxx_messageInfo_GameLabel.Size(m)
}
func (m *GameLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_GameLabel.DiscardUnknown(m)
}

var xxx_messageInfo_GameLabel proto.InternalMessageInfo

func (m *GameLabel) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

func (m *GameLabel) GetDisplayName() string {
	if m != nil {
		return m.DisplayName
	}
	return ""
}

func (m *GameLabel) GetType() GameLabelType {
	if m != nil {
		return m.Type
	}
	return GameLabelType_Default
}

func (m *GameLabel) GetLabelType() uint32 {
	if m != nil {
		return m.LabelType
	}
	return 0
}

type ClassifyLabelList struct {
	ClassifyName         string       `protobuf:"bytes,1,opt,name=classify_name,json=classifyName,proto3" json:"classify_name,omitempty"`
	ClassifyLabels       []*GameLabel `protobuf:"bytes,2,rep,name=classify_labels,json=classifyLabels,proto3" json:"classify_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ClassifyLabelList) Reset()         { *m = ClassifyLabelList{} }
func (m *ClassifyLabelList) String() string { return proto.CompactTextString(m) }
func (*ClassifyLabelList) ProtoMessage()    {}
func (*ClassifyLabelList) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{47}
}
func (m *ClassifyLabelList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClassifyLabelList.Unmarshal(m, b)
}
func (m *ClassifyLabelList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClassifyLabelList.Marshal(b, m, deterministic)
}
func (dst *ClassifyLabelList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClassifyLabelList.Merge(dst, src)
}
func (m *ClassifyLabelList) XXX_Size() int {
	return xxx_messageInfo_ClassifyLabelList.Size(m)
}
func (m *ClassifyLabelList) XXX_DiscardUnknown() {
	xxx_messageInfo_ClassifyLabelList.DiscardUnknown(m)
}

var xxx_messageInfo_ClassifyLabelList proto.InternalMessageInfo

func (m *ClassifyLabelList) GetClassifyName() string {
	if m != nil {
		return m.ClassifyName
	}
	return ""
}

func (m *ClassifyLabelList) GetClassifyLabels() []*GameLabel {
	if m != nil {
		return m.ClassifyLabels
	}
	return nil
}

// 面板tab信息，玩法id或者混合filterId
type TabItem struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	CombId               string   `protobuf:"bytes,2,opt,name=comb_id,json=combId,proto3" json:"comb_id,omitempty"`
	TabType              uint32   `protobuf:"varint,3,opt,name=tab_type,json=tabType,proto3" json:"tab_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TabItem) Reset()         { *m = TabItem{} }
func (m *TabItem) String() string { return proto.CompactTextString(m) }
func (*TabItem) ProtoMessage()    {}
func (*TabItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{48}
}
func (m *TabItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabItem.Unmarshal(m, b)
}
func (m *TabItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabItem.Marshal(b, m, deterministic)
}
func (dst *TabItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabItem.Merge(dst, src)
}
func (m *TabItem) XXX_Size() int {
	return xxx_messageInfo_TabItem.Size(m)
}
func (m *TabItem) XXX_DiscardUnknown() {
	xxx_messageInfo_TabItem.DiscardUnknown(m)
}

var xxx_messageInfo_TabItem proto.InternalMessageInfo

func (m *TabItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *TabItem) GetCombId() string {
	if m != nil {
		return m.CombId
	}
	return ""
}

func (m *TabItem) GetTabType() uint32 {
	if m != nil {
		return m.TabType
	}
	return 0
}

type GetGameLabelsReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// Deprecated: replace by req_tab
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BrowseLabels         []*GameLabel `protobuf:"bytes,3,rep,name=browse_labels,json=browseLabels,proto3" json:"browse_labels,omitempty"`
	ReqTab               *TabItem     `protobuf:"bytes,4,opt,name=req_tab,json=reqTab,proto3" json:"req_tab,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGameLabelsReq) Reset()         { *m = GetGameLabelsReq{} }
func (m *GetGameLabelsReq) String() string { return proto.CompactTextString(m) }
func (*GetGameLabelsReq) ProtoMessage()    {}
func (*GetGameLabelsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{49}
}
func (m *GetGameLabelsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameLabelsReq.Unmarshal(m, b)
}
func (m *GetGameLabelsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameLabelsReq.Marshal(b, m, deterministic)
}
func (dst *GetGameLabelsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameLabelsReq.Merge(dst, src)
}
func (m *GetGameLabelsReq) XXX_Size() int {
	return xxx_messageInfo_GetGameLabelsReq.Size(m)
}
func (m *GetGameLabelsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameLabelsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameLabelsReq proto.InternalMessageInfo

func (m *GetGameLabelsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGameLabelsReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetGameLabelsReq) GetBrowseLabels() []*GameLabel {
	if m != nil {
		return m.BrowseLabels
	}
	return nil
}

func (m *GetGameLabelsReq) GetReqTab() *TabItem {
	if m != nil {
		return m.ReqTab
	}
	return nil
}

type GetGameLabelsResp struct {
	Labels               []*GameLabel         `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty"`
	Enable               bool                 `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
	ClassifyLabels       []*ClassifyLabelList `protobuf:"bytes,3,rep,name=classify_labels,json=classifyLabels,proto3" json:"classify_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetGameLabelsResp) Reset()         { *m = GetGameLabelsResp{} }
func (m *GetGameLabelsResp) String() string { return proto.CompactTextString(m) }
func (*GetGameLabelsResp) ProtoMessage()    {}
func (*GetGameLabelsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{50}
}
func (m *GetGameLabelsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameLabelsResp.Unmarshal(m, b)
}
func (m *GetGameLabelsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameLabelsResp.Marshal(b, m, deterministic)
}
func (dst *GetGameLabelsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameLabelsResp.Merge(dst, src)
}
func (m *GetGameLabelsResp) XXX_Size() int {
	return xxx_messageInfo_GetGameLabelsResp.Size(m)
}
func (m *GetGameLabelsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameLabelsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameLabelsResp proto.InternalMessageInfo

func (m *GetGameLabelsResp) GetLabels() []*GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *GetGameLabelsResp) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

func (m *GetGameLabelsResp) GetClassifyLabels() []*ClassifyLabelList {
	if m != nil {
		return m.ClassifyLabels
	}
	return nil
}

// * ----------BatchHotGameLabels---------- *
type BrowseLabel struct {
	BrowseLabels         []*GameLabel `protobuf:"bytes,1,rep,name=browse_labels,json=browseLabels,proto3" json:"browse_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BrowseLabel) Reset()         { *m = BrowseLabel{} }
func (m *BrowseLabel) String() string { return proto.CompactTextString(m) }
func (*BrowseLabel) ProtoMessage()    {}
func (*BrowseLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{51}
}
func (m *BrowseLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrowseLabel.Unmarshal(m, b)
}
func (m *BrowseLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrowseLabel.Marshal(b, m, deterministic)
}
func (dst *BrowseLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrowseLabel.Merge(dst, src)
}
func (m *BrowseLabel) XXX_Size() int {
	return xxx_messageInfo_BrowseLabel.Size(m)
}
func (m *BrowseLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_BrowseLabel.DiscardUnknown(m)
}

var xxx_messageInfo_BrowseLabel proto.InternalMessageInfo

func (m *BrowseLabel) GetBrowseLabels() []*GameLabel {
	if m != nil {
		return m.BrowseLabels
	}
	return nil
}

type BatchHotGameLabelsReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// Deprecated: replace by tab_list
	TabIds                 []uint32                `protobuf:"varint,2,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	BrowseLabelsMap        map[uint32]*BrowseLabel `protobuf:"bytes,3,rep,name=browse_labels_map,json=browseLabelsMap,proto3" json:"browse_labels_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TabList                []*TabItem              `protobuf:"bytes,4,rep,name=tab_list,json=tabList,proto3" json:"tab_list,omitempty"`
	CombTabBrowseLabelsMap map[string]*BrowseLabel `protobuf:"bytes,5,rep,name=comb_tab_browse_labels_map,json=combTabBrowseLabelsMap,proto3" json:"comb_tab_browse_labels_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral   struct{}                `json:"-"`
	XXX_unrecognized       []byte                  `json:"-"`
	XXX_sizecache          int32                   `json:"-"`
}

func (m *BatchHotGameLabelsReq) Reset()         { *m = BatchHotGameLabelsReq{} }
func (m *BatchHotGameLabelsReq) String() string { return proto.CompactTextString(m) }
func (*BatchHotGameLabelsReq) ProtoMessage()    {}
func (*BatchHotGameLabelsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{52}
}
func (m *BatchHotGameLabelsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchHotGameLabelsReq.Unmarshal(m, b)
}
func (m *BatchHotGameLabelsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchHotGameLabelsReq.Marshal(b, m, deterministic)
}
func (dst *BatchHotGameLabelsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchHotGameLabelsReq.Merge(dst, src)
}
func (m *BatchHotGameLabelsReq) XXX_Size() int {
	return xxx_messageInfo_BatchHotGameLabelsReq.Size(m)
}
func (m *BatchHotGameLabelsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchHotGameLabelsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchHotGameLabelsReq proto.InternalMessageInfo

func (m *BatchHotGameLabelsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchHotGameLabelsReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *BatchHotGameLabelsReq) GetBrowseLabelsMap() map[uint32]*BrowseLabel {
	if m != nil {
		return m.BrowseLabelsMap
	}
	return nil
}

func (m *BatchHotGameLabelsReq) GetTabList() []*TabItem {
	if m != nil {
		return m.TabList
	}
	return nil
}

func (m *BatchHotGameLabelsReq) GetCombTabBrowseLabelsMap() map[string]*BrowseLabel {
	if m != nil {
		return m.CombTabBrowseLabelsMap
	}
	return nil
}

type HotGameLabel struct {
	Labels               []*GameLabel         `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty"`
	Enable               bool                 `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
	ClassifyLabels       []*ClassifyLabelList `protobuf:"bytes,3,rep,name=classify_labels,json=classifyLabels,proto3" json:"classify_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *HotGameLabel) Reset()         { *m = HotGameLabel{} }
func (m *HotGameLabel) String() string { return proto.CompactTextString(m) }
func (*HotGameLabel) ProtoMessage()    {}
func (*HotGameLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{53}
}
func (m *HotGameLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HotGameLabel.Unmarshal(m, b)
}
func (m *HotGameLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HotGameLabel.Marshal(b, m, deterministic)
}
func (dst *HotGameLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HotGameLabel.Merge(dst, src)
}
func (m *HotGameLabel) XXX_Size() int {
	return xxx_messageInfo_HotGameLabel.Size(m)
}
func (m *HotGameLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_HotGameLabel.DiscardUnknown(m)
}

var xxx_messageInfo_HotGameLabel proto.InternalMessageInfo

func (m *HotGameLabel) GetLabels() []*GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *HotGameLabel) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

func (m *HotGameLabel) GetClassifyLabels() []*ClassifyLabelList {
	if m != nil {
		return m.ClassifyLabels
	}
	return nil
}

type BatchHotGameLabelsResp struct {
	HotGameLabelsMap     map[uint32]*HotGameLabel `protobuf:"bytes,1,rep,name=hot_game_labels_map,json=hotGameLabelsMap,proto3" json:"hot_game_labels_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	CombTabGameLabelsMap map[string]*HotGameLabel `protobuf:"bytes,2,rep,name=comb_tab_game_labels_map,json=combTabGameLabelsMap,proto3" json:"comb_tab_game_labels_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatchHotGameLabelsResp) Reset()         { *m = BatchHotGameLabelsResp{} }
func (m *BatchHotGameLabelsResp) String() string { return proto.CompactTextString(m) }
func (*BatchHotGameLabelsResp) ProtoMessage()    {}
func (*BatchHotGameLabelsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{54}
}
func (m *BatchHotGameLabelsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchHotGameLabelsResp.Unmarshal(m, b)
}
func (m *BatchHotGameLabelsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchHotGameLabelsResp.Marshal(b, m, deterministic)
}
func (dst *BatchHotGameLabelsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchHotGameLabelsResp.Merge(dst, src)
}
func (m *BatchHotGameLabelsResp) XXX_Size() int {
	return xxx_messageInfo_BatchHotGameLabelsResp.Size(m)
}
func (m *BatchHotGameLabelsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchHotGameLabelsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchHotGameLabelsResp proto.InternalMessageInfo

func (m *BatchHotGameLabelsResp) GetHotGameLabelsMap() map[uint32]*HotGameLabel {
	if m != nil {
		return m.HotGameLabelsMap
	}
	return nil
}

func (m *BatchHotGameLabelsResp) GetCombTabGameLabelsMap() map[string]*HotGameLabel {
	if m != nil {
		return m.CombTabGameLabelsMap
	}
	return nil
}

type CutWordParam struct {
	IsUseStop            bool                 `protobuf:"varint,1,opt,name=is_use_stop,json=isUseStop,proto3" json:"is_use_stop,omitempty"`
	IsUseHmm             bool                 `protobuf:"varint,2,opt,name=is_use_hmm,json=isUseHmm,proto3" json:"is_use_hmm,omitempty"`
	SegType              CutWordParam_SegType `protobuf:"varint,3,opt,name=seg_type,json=segType,proto3,enum=topic_channel.rcmd_channel_label.CutWordParam_SegType" json:"seg_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CutWordParam) Reset()         { *m = CutWordParam{} }
func (m *CutWordParam) String() string { return proto.CompactTextString(m) }
func (*CutWordParam) ProtoMessage()    {}
func (*CutWordParam) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{55}
}
func (m *CutWordParam) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CutWordParam.Unmarshal(m, b)
}
func (m *CutWordParam) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CutWordParam.Marshal(b, m, deterministic)
}
func (dst *CutWordParam) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CutWordParam.Merge(dst, src)
}
func (m *CutWordParam) XXX_Size() int {
	return xxx_messageInfo_CutWordParam.Size(m)
}
func (m *CutWordParam) XXX_DiscardUnknown() {
	xxx_messageInfo_CutWordParam.DiscardUnknown(m)
}

var xxx_messageInfo_CutWordParam proto.InternalMessageInfo

func (m *CutWordParam) GetIsUseStop() bool {
	if m != nil {
		return m.IsUseStop
	}
	return false
}

func (m *CutWordParam) GetIsUseHmm() bool {
	if m != nil {
		return m.IsUseHmm
	}
	return false
}

func (m *CutWordParam) GetSegType() CutWordParam_SegType {
	if m != nil {
		return m.SegType
	}
	return CutWordParam_SegTypeDefalt
}

type CutWordReq struct {
	Uid                  uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Text                 string        `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Limit                uint32        `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	IsNotStop            bool          `protobuf:"varint,4,opt,name=is_not_stop,json=isNotStop,proto3" json:"is_not_stop,omitempty"`
	CutWordParam         *CutWordParam `protobuf:"bytes,5,opt,name=cut_word_param,json=cutWordParam,proto3" json:"cut_word_param,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CutWordReq) Reset()         { *m = CutWordReq{} }
func (m *CutWordReq) String() string { return proto.CompactTextString(m) }
func (*CutWordReq) ProtoMessage()    {}
func (*CutWordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{56}
}
func (m *CutWordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CutWordReq.Unmarshal(m, b)
}
func (m *CutWordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CutWordReq.Marshal(b, m, deterministic)
}
func (dst *CutWordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CutWordReq.Merge(dst, src)
}
func (m *CutWordReq) XXX_Size() int {
	return xxx_messageInfo_CutWordReq.Size(m)
}
func (m *CutWordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CutWordReq.DiscardUnknown(m)
}

var xxx_messageInfo_CutWordReq proto.InternalMessageInfo

func (m *CutWordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CutWordReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *CutWordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *CutWordReq) GetIsNotStop() bool {
	if m != nil {
		return m.IsNotStop
	}
	return false
}

func (m *CutWordReq) GetCutWordParam() *CutWordParam {
	if m != nil {
		return m.CutWordParam
	}
	return nil
}

type CutWordResp struct {
	Labels               []string `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CutWordResp) Reset()         { *m = CutWordResp{} }
func (m *CutWordResp) String() string { return proto.CompactTextString(m) }
func (*CutWordResp) ProtoMessage()    {}
func (*CutWordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{57}
}
func (m *CutWordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CutWordResp.Unmarshal(m, b)
}
func (m *CutWordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CutWordResp.Marshal(b, m, deterministic)
}
func (dst *CutWordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CutWordResp.Merge(dst, src)
}
func (m *CutWordResp) XXX_Size() int {
	return xxx_messageInfo_CutWordResp.Size(m)
}
func (m *CutWordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CutWordResp.DiscardUnknown(m)
}

var xxx_messageInfo_CutWordResp proto.InternalMessageInfo

func (m *CutWordResp) GetLabels() []string {
	if m != nil {
		return m.Labels
	}
	return nil
}

type LabelRefreshReq struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Labels               []*GameLabel `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *LabelRefreshReq) Reset()         { *m = LabelRefreshReq{} }
func (m *LabelRefreshReq) String() string { return proto.CompactTextString(m) }
func (*LabelRefreshReq) ProtoMessage()    {}
func (*LabelRefreshReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{58}
}
func (m *LabelRefreshReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LabelRefreshReq.Unmarshal(m, b)
}
func (m *LabelRefreshReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LabelRefreshReq.Marshal(b, m, deterministic)
}
func (dst *LabelRefreshReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelRefreshReq.Merge(dst, src)
}
func (m *LabelRefreshReq) XXX_Size() int {
	return xxx_messageInfo_LabelRefreshReq.Size(m)
}
func (m *LabelRefreshReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelRefreshReq.DiscardUnknown(m)
}

var xxx_messageInfo_LabelRefreshReq proto.InternalMessageInfo

func (m *LabelRefreshReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LabelRefreshReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *LabelRefreshReq) GetLabels() []*GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

type LabelDetail struct {
	Name                 string              `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ParentName           string              `protobuf:"bytes,2,opt,name=parent_name,json=parentName,proto3" json:"parent_name,omitempty"`
	LocType              LabelDetail_LocType `protobuf:"varint,3,opt,name=loc_type,json=locType,proto3,enum=topic_channel.rcmd_channel_label.LabelDetail_LocType" json:"loc_type,omitempty"`
	GeoType              LabelDetail_GeoType `protobuf:"varint,4,opt,name=geo_type,json=geoType,proto3,enum=topic_channel.rcmd_channel_label.LabelDetail_GeoType" json:"geo_type,omitempty"`
	GeoInfo              string              `protobuf:"bytes,5,opt,name=geo_info,json=geoInfo,proto3" json:"geo_info,omitempty"`
	OtherTabName         string              `protobuf:"bytes,6,opt,name=other_tab_name,json=otherTabName,proto3" json:"other_tab_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *LabelDetail) Reset()         { *m = LabelDetail{} }
func (m *LabelDetail) String() string { return proto.CompactTextString(m) }
func (*LabelDetail) ProtoMessage()    {}
func (*LabelDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{59}
}
func (m *LabelDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LabelDetail.Unmarshal(m, b)
}
func (m *LabelDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LabelDetail.Marshal(b, m, deterministic)
}
func (dst *LabelDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelDetail.Merge(dst, src)
}
func (m *LabelDetail) XXX_Size() int {
	return xxx_messageInfo_LabelDetail.Size(m)
}
func (m *LabelDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelDetail.DiscardUnknown(m)
}

var xxx_messageInfo_LabelDetail proto.InternalMessageInfo

func (m *LabelDetail) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *LabelDetail) GetParentName() string {
	if m != nil {
		return m.ParentName
	}
	return ""
}

func (m *LabelDetail) GetLocType() LabelDetail_LocType {
	if m != nil {
		return m.LocType
	}
	return LabelDetail_LocType_NOTHING
}

func (m *LabelDetail) GetGeoType() LabelDetail_GeoType {
	if m != nil {
		return m.GeoType
	}
	return LabelDetail_GeoType_INVALID
}

func (m *LabelDetail) GetGeoInfo() string {
	if m != nil {
		return m.GeoInfo
	}
	return ""
}

func (m *LabelDetail) GetOtherTabName() string {
	if m != nil {
		return m.OtherTabName
	}
	return ""
}

type LabelRefreshResp struct {
	Labels               []*GameLabel   `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty"`
	Detail               []*LabelDetail `protobuf:"bytes,2,rep,name=detail,proto3" json:"detail,omitempty"`
	EnableSecondLabel    bool           `protobuf:"varint,3,opt,name=enable_second_label,json=enableSecondLabel,proto3" json:"enable_second_label,omitempty"`
	EnableKeywordWeight  bool           `protobuf:"varint,4,opt,name=enable_keyword_weight,json=enableKeywordWeight,proto3" json:"enable_keyword_weight,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *LabelRefreshResp) Reset()         { *m = LabelRefreshResp{} }
func (m *LabelRefreshResp) String() string { return proto.CompactTextString(m) }
func (*LabelRefreshResp) ProtoMessage()    {}
func (*LabelRefreshResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_channel_label_afd12f2757029d76, []int{60}
}
func (m *LabelRefreshResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LabelRefreshResp.Unmarshal(m, b)
}
func (m *LabelRefreshResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LabelRefreshResp.Marshal(b, m, deterministic)
}
func (dst *LabelRefreshResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelRefreshResp.Merge(dst, src)
}
func (m *LabelRefreshResp) XXX_Size() int {
	return xxx_messageInfo_LabelRefreshResp.Size(m)
}
func (m *LabelRefreshResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelRefreshResp.DiscardUnknown(m)
}

var xxx_messageInfo_LabelRefreshResp proto.InternalMessageInfo

func (m *LabelRefreshResp) GetLabels() []*GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *LabelRefreshResp) GetDetail() []*LabelDetail {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (m *LabelRefreshResp) GetEnableSecondLabel() bool {
	if m != nil {
		return m.EnableSecondLabel
	}
	return false
}

func (m *LabelRefreshResp) GetEnableKeywordWeight() bool {
	if m != nil {
		return m.EnableKeywordWeight
	}
	return false
}

func init() {
	proto.RegisterType((*GetFilterBlockOptionListReq)(nil), "topic_channel.rcmd_channel_label.GetFilterBlockOptionListReq")
	proto.RegisterType((*GetFilterBlockOptionListResp)(nil), "topic_channel.rcmd_channel_label.GetFilterBlockOptionListResp")
	proto.RegisterType((*RefreshGameLabelReq)(nil), "topic_channel.rcmd_channel_label.RefreshGameLabelReq")
	proto.RegisterType((*RefreshGameLabelResp)(nil), "topic_channel.rcmd_channel_label.RefreshGameLabelResp")
	proto.RegisterType((*GetBusinessLabelRelationReq)(nil), "topic_channel.rcmd_channel_label.GetBusinessLabelRelationReq")
	proto.RegisterType((*BusinessLabelRelation)(nil), "topic_channel.rcmd_channel_label.BusinessLabelRelation")
	proto.RegisterType((*GameCardOpt)(nil), "topic_channel.rcmd_channel_label.GameCardOpt")
	proto.RegisterType((*GameCardLabel)(nil), "topic_channel.rcmd_channel_label.GameCardLabel")
	proto.RegisterType((*BlockOptionGameCard)(nil), "topic_channel.rcmd_channel_label.BlockOptionGameCard")
	proto.RegisterType((*LabelGameCardRelation)(nil), "topic_channel.rcmd_channel_label.LabelGameCardRelation")
	proto.RegisterType((*BlockOptionGameCardRelation)(nil), "topic_channel.rcmd_channel_label.BlockOptionGameCardRelation")
	proto.RegisterType((*GetBusinessLabelRelationResp)(nil), "topic_channel.rcmd_channel_label.GetBusinessLabelRelationResp")
	proto.RegisterType((*MatchGameCardInfoReq)(nil), "topic_channel.rcmd_channel_label.MatchGameCardInfoReq")
	proto.RegisterType((*TextPair)(nil), "topic_channel.rcmd_channel_label.TextPair")
	proto.RegisterType((*GameCardInfo)(nil), "topic_channel.rcmd_channel_label.GameCardInfo")
	proto.RegisterType((*MatchGameCardInfoResp)(nil), "topic_channel.rcmd_channel_label.MatchGameCardInfoResp")
	proto.RegisterType((*CutAndMatchLabelReq)(nil), "topic_channel.rcmd_channel_label.CutAndMatchLabelReq")
	proto.RegisterType((*CutAndMatchLabelResp)(nil), "topic_channel.rcmd_channel_label.CutAndMatchLabelResp")
	proto.RegisterType((*InternalGetAllLabelReq)(nil), "topic_channel.rcmd_channel_label.InternalGetAllLabelReq")
	proto.RegisterType((*InternalGetAllLabelResp)(nil), "topic_channel.rcmd_channel_label.InternalGetAllLabelResp")
	proto.RegisterType((*GetRelatedPublishLabelsReq)(nil), "topic_channel.rcmd_channel_label.GetRelatedPublishLabelsReq")
	proto.RegisterType((*RelatedPublishLabel)(nil), "topic_channel.rcmd_channel_label.RelatedPublishLabel")
	proto.RegisterType((*RecommendationOption)(nil), "topic_channel.rcmd_channel_label.RecommendationOption")
	proto.RegisterType((*RecommendationInput)(nil), "topic_channel.rcmd_channel_label.RecommendationInput")
	proto.RegisterType((*GetRelatedPublishLabelsResp)(nil), "topic_channel.rcmd_channel_label.GetRelatedPublishLabelsResp")
	proto.RegisterType((*SearchGameNameReq)(nil), "topic_channel.rcmd_channel_label.SearchGameNameReq")
	proto.RegisterType((*GameInfo)(nil), "topic_channel.rcmd_channel_label.GameInfo")
	proto.RegisterType((*SearchGameNameResp)(nil), "topic_channel.rcmd_channel_label.SearchGameNameResp")
	proto.RegisterType((*SearchGlobalLabelReq)(nil), "topic_channel.rcmd_channel_label.SearchGlobalLabelReq")
	proto.RegisterType((*SearchLabel)(nil), "topic_channel.rcmd_channel_label.SearchLabel")
	proto.RegisterType((*SearchGlobalLabelResp)(nil), "topic_channel.rcmd_channel_label.SearchGlobalLabelResp")
	proto.RegisterType((*BlockOption)(nil), "topic_channel.rcmd_channel_label.BlockOption")
	proto.RegisterType((*GameBlockOption)(nil), "topic_channel.rcmd_channel_label.GameBlockOption")
	proto.RegisterType((*GetPublishGameLabelsReq)(nil), "topic_channel.rcmd_channel_label.GetPublishGameLabelsReq")
	proto.RegisterType((*BlockOptionLabel)(nil), "topic_channel.rcmd_channel_label.BlockOptionLabel")
	proto.RegisterType((*BlockOptionLabelList)(nil), "topic_channel.rcmd_channel_label.BlockOptionLabelList")
	proto.RegisterType((*GetPublishGameLabelsResp)(nil), "topic_channel.rcmd_channel_label.GetPublishGameLabelsResp")
	proto.RegisterType((*ConvertGameLabel)(nil), "topic_channel.rcmd_channel_label.ConvertGameLabel")
	proto.RegisterType((*ConvertGameLabelList)(nil), "topic_channel.rcmd_channel_label.ConvertGameLabelList")
	proto.RegisterType((*ConvertGameLabelsResp)(nil), "topic_channel.rcmd_channel_label.ConvertGameLabelsResp")
	proto.RegisterType((*ConvertGameInfo)(nil), "topic_channel.rcmd_channel_label.ConvertGameInfo")
	proto.RegisterType((*ConvertGameLabelsReq)(nil), "topic_channel.rcmd_channel_label.ConvertGameLabelsReq")
	proto.RegisterType((*GetSearchHintReq)(nil), "topic_channel.rcmd_channel_label.GetSearchHintReq")
	proto.RegisterType((*GetSearchHintResp)(nil), "topic_channel.rcmd_channel_label.GetSearchHintResp")
	proto.RegisterType((*LabelSearchReq)(nil), "topic_channel.rcmd_channel_label.LabelSearchReq")
	proto.RegisterType((*LabelSearchResp)(nil), "topic_channel.rcmd_channel_label.LabelSearchResp")
	proto.RegisterType((*GameLabel)(nil), "topic_channel.rcmd_channel_label.GameLabel")
	proto.RegisterType((*ClassifyLabelList)(nil), "topic_channel.rcmd_channel_label.ClassifyLabelList")
	proto.RegisterType((*TabItem)(nil), "topic_channel.rcmd_channel_label.TabItem")
	proto.RegisterType((*GetGameLabelsReq)(nil), "topic_channel.rcmd_channel_label.GetGameLabelsReq")
	proto.RegisterType((*GetGameLabelsResp)(nil), "topic_channel.rcmd_channel_label.GetGameLabelsResp")
	proto.RegisterType((*BrowseLabel)(nil), "topic_channel.rcmd_channel_label.BrowseLabel")
	proto.RegisterType((*BatchHotGameLabelsReq)(nil), "topic_channel.rcmd_channel_label.BatchHotGameLabelsReq")
	proto.RegisterMapType((map[uint32]*BrowseLabel)(nil), "topic_channel.rcmd_channel_label.BatchHotGameLabelsReq.BrowseLabelsMapEntry")
	proto.RegisterMapType((map[string]*BrowseLabel)(nil), "topic_channel.rcmd_channel_label.BatchHotGameLabelsReq.CombTabBrowseLabelsMapEntry")
	proto.RegisterType((*HotGameLabel)(nil), "topic_channel.rcmd_channel_label.HotGameLabel")
	proto.RegisterType((*BatchHotGameLabelsResp)(nil), "topic_channel.rcmd_channel_label.BatchHotGameLabelsResp")
	proto.RegisterMapType((map[string]*HotGameLabel)(nil), "topic_channel.rcmd_channel_label.BatchHotGameLabelsResp.CombTabGameLabelsMapEntry")
	proto.RegisterMapType((map[uint32]*HotGameLabel)(nil), "topic_channel.rcmd_channel_label.BatchHotGameLabelsResp.HotGameLabelsMapEntry")
	proto.RegisterType((*CutWordParam)(nil), "topic_channel.rcmd_channel_label.CutWordParam")
	proto.RegisterType((*CutWordReq)(nil), "topic_channel.rcmd_channel_label.CutWordReq")
	proto.RegisterType((*CutWordResp)(nil), "topic_channel.rcmd_channel_label.CutWordResp")
	proto.RegisterType((*LabelRefreshReq)(nil), "topic_channel.rcmd_channel_label.LabelRefreshReq")
	proto.RegisterType((*LabelDetail)(nil), "topic_channel.rcmd_channel_label.LabelDetail")
	proto.RegisterType((*LabelRefreshResp)(nil), "topic_channel.rcmd_channel_label.LabelRefreshResp")
	proto.RegisterEnum("topic_channel.rcmd_channel_label.BusinessType", BusinessType_name, BusinessType_value)
	proto.RegisterEnum("topic_channel.rcmd_channel_label.GameLabelType", GameLabelType_name, GameLabelType_value)
	proto.RegisterEnum("topic_channel.rcmd_channel_label.RefreshGameLabelReq_Source", RefreshGameLabelReq_Source_name, RefreshGameLabelReq_Source_value)
	proto.RegisterEnum("topic_channel.rcmd_channel_label.TextPair_TextType", TextPair_TextType_name, TextPair_TextType_value)
	proto.RegisterEnum("topic_channel.rcmd_channel_label.CutAndMatchLabelReq_CutType", CutAndMatchLabelReq_CutType_name, CutAndMatchLabelReq_CutType_value)
	proto.RegisterEnum("topic_channel.rcmd_channel_label.RecommendationOption_Type", RecommendationOption_Type_name, RecommendationOption_Type_value)
	proto.RegisterEnum("topic_channel.rcmd_channel_label.SearchGameNameReq_SourceType", SearchGameNameReq_SourceType_name, SearchGameNameReq_SourceType_value)
	proto.RegisterEnum("topic_channel.rcmd_channel_label.ConvertGameLabelsReq_SourceType", ConvertGameLabelsReq_SourceType_name, ConvertGameLabelsReq_SourceType_value)
	proto.RegisterEnum("topic_channel.rcmd_channel_label.GetSearchHintResp_HintType", GetSearchHintResp_HintType_name, GetSearchHintResp_HintType_value)
	proto.RegisterEnum("topic_channel.rcmd_channel_label.TabItem_TabType", TabItem_TabType_name, TabItem_TabType_value)
	proto.RegisterEnum("topic_channel.rcmd_channel_label.CutWordParam_SegType", CutWordParam_SegType_name, CutWordParam_SegType_value)
	proto.RegisterEnum("topic_channel.rcmd_channel_label.LabelDetail_LocType", LabelDetail_LocType_name, LabelDetail_LocType_value)
	proto.RegisterEnum("topic_channel.rcmd_channel_label.LabelDetail_GeoType", LabelDetail_GeoType_name, LabelDetail_GeoType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RCMDChannelLabelClient is the client API for RCMDChannelLabel service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RCMDChannelLabelClient interface {
	// 房间标签搜索
	LabelSearch(ctx context.Context, in *LabelSearchReq, opts ...grpc.CallOption) (*LabelSearchResp, error)
	// 房间玩法列表获取
	GetGameLabels(ctx context.Context, in *GetGameLabelsReq, opts ...grpc.CallOption) (*GetGameLabelsResp, error)
	// 结巴切词
	CutWord(ctx context.Context, in *CutWordReq, opts ...grpc.CallOption) (*CutWordResp, error)
	// 刷新玩法标签
	// DEPRECATED: 切词库和标签倒排索引已抽离作为公共基础库，不再通过rpc刷新标签
	LabelRefresh(ctx context.Context, in *LabelRefreshReq, opts ...grpc.CallOption) (*LabelRefreshResp, error)
	// 获取搜索框hint
	GetSearchHint(ctx context.Context, in *GetSearchHintReq, opts ...grpc.CallOption) (*GetSearchHintResp, error)
	// 玩法标签映射
	// 建议以后使用CutAndMatchLabel替代该方法
	ConvertGameLabels(ctx context.Context, in *ConvertGameLabelsReq, opts ...grpc.CallOption) (*ConvertGameLabelsResp, error)
	// 批量获取热门玩法
	BatchHotGameLabels(ctx context.Context, in *BatchHotGameLabelsReq, opts ...grpc.CallOption) (*BatchHotGameLabelsResp, error)
	// 获取游戏发布条件玩法
	// DEPRECATED: 之前提供给服务端缓存至本地，在用户请求的时候，将发布条件转玩法。先发布条件转玩法已迁至推荐
	GetPublishGameLabels(ctx context.Context, in *GetPublishGameLabelsReq, opts ...grpc.CallOption) (*GetPublishGameLabelsResp, error)
	// 全局搜索标签，todo: 迁移至上游调用方rcmd-search
	SearchGlobalLabel(ctx context.Context, in *SearchGlobalLabelReq, opts ...grpc.CallOption) (*SearchGlobalLabelResp, error)
	// 游戏名称搜索
	SearchGameName(ctx context.Context, in *SearchGameNameReq, opts ...grpc.CallOption) (*SearchGameNameResp, error)
	InternalGetAllLabel(ctx context.Context, in *InternalGetAllLabelReq, opts ...grpc.CallOption) (*InternalGetAllLabelResp, error)
	// 获取房间名称匹配的关联发布条件的标签
	GetRelatedPublishLabels(ctx context.Context, in *GetRelatedPublishLabelsReq, opts ...grpc.CallOption) (*GetRelatedPublishLabelsResp, error)
	// 切词并匹配标签
	CutAndMatchLabel(ctx context.Context, in *CutAndMatchLabelReq, opts ...grpc.CallOption) (*CutAndMatchLabelResp, error)
	// 获取游戏卡信息
	MatchGameCardInfo(ctx context.Context, in *MatchGameCardInfoReq, opts ...grpc.CallOption) (*MatchGameCardInfoResp, error)
	// 获取业务关联关系
	GetBusinessLabelRelation(ctx context.Context, in *GetBusinessLabelRelationReq, opts ...grpc.CallOption) (*GetBusinessLabelRelationResp, error)
	// 刷新GameLabel: 用于清除客户端本地gameLabel缓存，服务端房间列表筛选面板版本控制
	RefreshGameLabel(ctx context.Context, in *RefreshGameLabelReq, opts ...grpc.CallOption) (*RefreshGameLabelResp, error)
	// 返回用户所在实验域不可转玩法的BlockOption
	GetFilterBlockOptionList(ctx context.Context, in *GetFilterBlockOptionListReq, opts ...grpc.CallOption) (*GetFilterBlockOptionListResp, error)
}

type rCMDChannelLabelClient struct {
	cc *grpc.ClientConn
}

func NewRCMDChannelLabelClient(cc *grpc.ClientConn) RCMDChannelLabelClient {
	return &rCMDChannelLabelClient{cc}
}

func (c *rCMDChannelLabelClient) LabelSearch(ctx context.Context, in *LabelSearchReq, opts ...grpc.CallOption) (*LabelSearchResp, error) {
	out := new(LabelSearchResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/LabelSearch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDChannelLabelClient) GetGameLabels(ctx context.Context, in *GetGameLabelsReq, opts ...grpc.CallOption) (*GetGameLabelsResp, error) {
	out := new(GetGameLabelsResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/GetGameLabels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDChannelLabelClient) CutWord(ctx context.Context, in *CutWordReq, opts ...grpc.CallOption) (*CutWordResp, error) {
	out := new(CutWordResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/CutWord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDChannelLabelClient) LabelRefresh(ctx context.Context, in *LabelRefreshReq, opts ...grpc.CallOption) (*LabelRefreshResp, error) {
	out := new(LabelRefreshResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/LabelRefresh", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDChannelLabelClient) GetSearchHint(ctx context.Context, in *GetSearchHintReq, opts ...grpc.CallOption) (*GetSearchHintResp, error) {
	out := new(GetSearchHintResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/GetSearchHint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDChannelLabelClient) ConvertGameLabels(ctx context.Context, in *ConvertGameLabelsReq, opts ...grpc.CallOption) (*ConvertGameLabelsResp, error) {
	out := new(ConvertGameLabelsResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/ConvertGameLabels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDChannelLabelClient) BatchHotGameLabels(ctx context.Context, in *BatchHotGameLabelsReq, opts ...grpc.CallOption) (*BatchHotGameLabelsResp, error) {
	out := new(BatchHotGameLabelsResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/BatchHotGameLabels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDChannelLabelClient) GetPublishGameLabels(ctx context.Context, in *GetPublishGameLabelsReq, opts ...grpc.CallOption) (*GetPublishGameLabelsResp, error) {
	out := new(GetPublishGameLabelsResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/GetPublishGameLabels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDChannelLabelClient) SearchGlobalLabel(ctx context.Context, in *SearchGlobalLabelReq, opts ...grpc.CallOption) (*SearchGlobalLabelResp, error) {
	out := new(SearchGlobalLabelResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/SearchGlobalLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDChannelLabelClient) SearchGameName(ctx context.Context, in *SearchGameNameReq, opts ...grpc.CallOption) (*SearchGameNameResp, error) {
	out := new(SearchGameNameResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/SearchGameName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDChannelLabelClient) InternalGetAllLabel(ctx context.Context, in *InternalGetAllLabelReq, opts ...grpc.CallOption) (*InternalGetAllLabelResp, error) {
	out := new(InternalGetAllLabelResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/InternalGetAllLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDChannelLabelClient) GetRelatedPublishLabels(ctx context.Context, in *GetRelatedPublishLabelsReq, opts ...grpc.CallOption) (*GetRelatedPublishLabelsResp, error) {
	out := new(GetRelatedPublishLabelsResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/GetRelatedPublishLabels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDChannelLabelClient) CutAndMatchLabel(ctx context.Context, in *CutAndMatchLabelReq, opts ...grpc.CallOption) (*CutAndMatchLabelResp, error) {
	out := new(CutAndMatchLabelResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/CutAndMatchLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDChannelLabelClient) MatchGameCardInfo(ctx context.Context, in *MatchGameCardInfoReq, opts ...grpc.CallOption) (*MatchGameCardInfoResp, error) {
	out := new(MatchGameCardInfoResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/MatchGameCardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDChannelLabelClient) GetBusinessLabelRelation(ctx context.Context, in *GetBusinessLabelRelationReq, opts ...grpc.CallOption) (*GetBusinessLabelRelationResp, error) {
	out := new(GetBusinessLabelRelationResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/GetBusinessLabelRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDChannelLabelClient) RefreshGameLabel(ctx context.Context, in *RefreshGameLabelReq, opts ...grpc.CallOption) (*RefreshGameLabelResp, error) {
	out := new(RefreshGameLabelResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/RefreshGameLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDChannelLabelClient) GetFilterBlockOptionList(ctx context.Context, in *GetFilterBlockOptionListReq, opts ...grpc.CallOption) (*GetFilterBlockOptionListResp, error) {
	out := new(GetFilterBlockOptionListResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_channel_label.RCMDChannelLabel/GetFilterBlockOptionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RCMDChannelLabelServer is the server API for RCMDChannelLabel service.
type RCMDChannelLabelServer interface {
	// 房间标签搜索
	LabelSearch(context.Context, *LabelSearchReq) (*LabelSearchResp, error)
	// 房间玩法列表获取
	GetGameLabels(context.Context, *GetGameLabelsReq) (*GetGameLabelsResp, error)
	// 结巴切词
	CutWord(context.Context, *CutWordReq) (*CutWordResp, error)
	// 刷新玩法标签
	// DEPRECATED: 切词库和标签倒排索引已抽离作为公共基础库，不再通过rpc刷新标签
	LabelRefresh(context.Context, *LabelRefreshReq) (*LabelRefreshResp, error)
	// 获取搜索框hint
	GetSearchHint(context.Context, *GetSearchHintReq) (*GetSearchHintResp, error)
	// 玩法标签映射
	// 建议以后使用CutAndMatchLabel替代该方法
	ConvertGameLabels(context.Context, *ConvertGameLabelsReq) (*ConvertGameLabelsResp, error)
	// 批量获取热门玩法
	BatchHotGameLabels(context.Context, *BatchHotGameLabelsReq) (*BatchHotGameLabelsResp, error)
	// 获取游戏发布条件玩法
	// DEPRECATED: 之前提供给服务端缓存至本地，在用户请求的时候，将发布条件转玩法。先发布条件转玩法已迁至推荐
	GetPublishGameLabels(context.Context, *GetPublishGameLabelsReq) (*GetPublishGameLabelsResp, error)
	// 全局搜索标签，todo: 迁移至上游调用方rcmd-search
	SearchGlobalLabel(context.Context, *SearchGlobalLabelReq) (*SearchGlobalLabelResp, error)
	// 游戏名称搜索
	SearchGameName(context.Context, *SearchGameNameReq) (*SearchGameNameResp, error)
	InternalGetAllLabel(context.Context, *InternalGetAllLabelReq) (*InternalGetAllLabelResp, error)
	// 获取房间名称匹配的关联发布条件的标签
	GetRelatedPublishLabels(context.Context, *GetRelatedPublishLabelsReq) (*GetRelatedPublishLabelsResp, error)
	// 切词并匹配标签
	CutAndMatchLabel(context.Context, *CutAndMatchLabelReq) (*CutAndMatchLabelResp, error)
	// 获取游戏卡信息
	MatchGameCardInfo(context.Context, *MatchGameCardInfoReq) (*MatchGameCardInfoResp, error)
	// 获取业务关联关系
	GetBusinessLabelRelation(context.Context, *GetBusinessLabelRelationReq) (*GetBusinessLabelRelationResp, error)
	// 刷新GameLabel: 用于清除客户端本地gameLabel缓存，服务端房间列表筛选面板版本控制
	RefreshGameLabel(context.Context, *RefreshGameLabelReq) (*RefreshGameLabelResp, error)
	// 返回用户所在实验域不可转玩法的BlockOption
	GetFilterBlockOptionList(context.Context, *GetFilterBlockOptionListReq) (*GetFilterBlockOptionListResp, error)
}

func RegisterRCMDChannelLabelServer(s *grpc.Server, srv RCMDChannelLabelServer) {
	s.RegisterService(&_RCMDChannelLabel_serviceDesc, srv)
}

func _RCMDChannelLabel_LabelSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LabelSearchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).LabelSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/LabelSearch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).LabelSearch(ctx, req.(*LabelSearchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDChannelLabel_GetGameLabels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameLabelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).GetGameLabels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/GetGameLabels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).GetGameLabels(ctx, req.(*GetGameLabelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDChannelLabel_CutWord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CutWordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).CutWord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/CutWord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).CutWord(ctx, req.(*CutWordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDChannelLabel_LabelRefresh_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LabelRefreshReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).LabelRefresh(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/LabelRefresh",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).LabelRefresh(ctx, req.(*LabelRefreshReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDChannelLabel_GetSearchHint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSearchHintReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).GetSearchHint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/GetSearchHint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).GetSearchHint(ctx, req.(*GetSearchHintReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDChannelLabel_ConvertGameLabels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConvertGameLabelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).ConvertGameLabels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/ConvertGameLabels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).ConvertGameLabels(ctx, req.(*ConvertGameLabelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDChannelLabel_BatchHotGameLabels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchHotGameLabelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).BatchHotGameLabels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/BatchHotGameLabels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).BatchHotGameLabels(ctx, req.(*BatchHotGameLabelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDChannelLabel_GetPublishGameLabels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPublishGameLabelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).GetPublishGameLabels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/GetPublishGameLabels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).GetPublishGameLabels(ctx, req.(*GetPublishGameLabelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDChannelLabel_SearchGlobalLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchGlobalLabelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).SearchGlobalLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/SearchGlobalLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).SearchGlobalLabel(ctx, req.(*SearchGlobalLabelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDChannelLabel_SearchGameName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchGameNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).SearchGameName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/SearchGameName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).SearchGameName(ctx, req.(*SearchGameNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDChannelLabel_InternalGetAllLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InternalGetAllLabelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).InternalGetAllLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/InternalGetAllLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).InternalGetAllLabel(ctx, req.(*InternalGetAllLabelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDChannelLabel_GetRelatedPublishLabels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRelatedPublishLabelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).GetRelatedPublishLabels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/GetRelatedPublishLabels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).GetRelatedPublishLabels(ctx, req.(*GetRelatedPublishLabelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDChannelLabel_CutAndMatchLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CutAndMatchLabelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).CutAndMatchLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/CutAndMatchLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).CutAndMatchLabel(ctx, req.(*CutAndMatchLabelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDChannelLabel_MatchGameCardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MatchGameCardInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).MatchGameCardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/MatchGameCardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).MatchGameCardInfo(ctx, req.(*MatchGameCardInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDChannelLabel_GetBusinessLabelRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessLabelRelationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).GetBusinessLabelRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/GetBusinessLabelRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).GetBusinessLabelRelation(ctx, req.(*GetBusinessLabelRelationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDChannelLabel_RefreshGameLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshGameLabelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).RefreshGameLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/RefreshGameLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).RefreshGameLabel(ctx, req.(*RefreshGameLabelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDChannelLabel_GetFilterBlockOptionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFilterBlockOptionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDChannelLabelServer).GetFilterBlockOptionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_channel_label.RCMDChannelLabel/GetFilterBlockOptionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDChannelLabelServer).GetFilterBlockOptionList(ctx, req.(*GetFilterBlockOptionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RCMDChannelLabel_serviceDesc = grpc.ServiceDesc{
	ServiceName: "topic_channel.rcmd_channel_label.RCMDChannelLabel",
	HandlerType: (*RCMDChannelLabelServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LabelSearch",
			Handler:    _RCMDChannelLabel_LabelSearch_Handler,
		},
		{
			MethodName: "GetGameLabels",
			Handler:    _RCMDChannelLabel_GetGameLabels_Handler,
		},
		{
			MethodName: "CutWord",
			Handler:    _RCMDChannelLabel_CutWord_Handler,
		},
		{
			MethodName: "LabelRefresh",
			Handler:    _RCMDChannelLabel_LabelRefresh_Handler,
		},
		{
			MethodName: "GetSearchHint",
			Handler:    _RCMDChannelLabel_GetSearchHint_Handler,
		},
		{
			MethodName: "ConvertGameLabels",
			Handler:    _RCMDChannelLabel_ConvertGameLabels_Handler,
		},
		{
			MethodName: "BatchHotGameLabels",
			Handler:    _RCMDChannelLabel_BatchHotGameLabels_Handler,
		},
		{
			MethodName: "GetPublishGameLabels",
			Handler:    _RCMDChannelLabel_GetPublishGameLabels_Handler,
		},
		{
			MethodName: "SearchGlobalLabel",
			Handler:    _RCMDChannelLabel_SearchGlobalLabel_Handler,
		},
		{
			MethodName: "SearchGameName",
			Handler:    _RCMDChannelLabel_SearchGameName_Handler,
		},
		{
			MethodName: "InternalGetAllLabel",
			Handler:    _RCMDChannelLabel_InternalGetAllLabel_Handler,
		},
		{
			MethodName: "GetRelatedPublishLabels",
			Handler:    _RCMDChannelLabel_GetRelatedPublishLabels_Handler,
		},
		{
			MethodName: "CutAndMatchLabel",
			Handler:    _RCMDChannelLabel_CutAndMatchLabel_Handler,
		},
		{
			MethodName: "MatchGameCardInfo",
			Handler:    _RCMDChannelLabel_MatchGameCardInfo_Handler,
		},
		{
			MethodName: "GetBusinessLabelRelation",
			Handler:    _RCMDChannelLabel_GetBusinessLabelRelation_Handler,
		},
		{
			MethodName: "RefreshGameLabel",
			Handler:    _RCMDChannelLabel_RefreshGameLabel_Handler,
		},
		{
			MethodName: "GetFilterBlockOptionList",
			Handler:    _RCMDChannelLabel_GetFilterBlockOptionList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/topic_channel/rcmd_channel_label.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/topic_channel/rcmd_channel_label.proto", fileDescriptor_rcmd_channel_label_afd12f2757029d76)
}

var fileDescriptor_rcmd_channel_label_afd12f2757029d76 = []byte{
	// 3319 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x3b, 0xdd, 0x6f, 0x1b, 0xc7,
	0xf1, 0x3e, 0x52, 0x1f, 0xe4, 0x90, 0x92, 0x4e, 0x2b, 0xea, 0xc3, 0x74, 0xf2, 0xfb, 0xd9, 0xd7,
	0x04, 0x70, 0xdd, 0x5a, 0x4e, 0xe4, 0xca, 0xf9, 0x68, 0x12, 0x40, 0xa2, 0x1c, 0x89, 0x8e, 0x2c,
	0x29, 0x27, 0xc6, 0x8e, 0x83, 0x04, 0xd7, 0x23, 0xb9, 0xa2, 0xae, 0x3e, 0xde, 0x9d, 0x6f, 0x97,
	0xb6, 0xd4, 0x36, 0x68, 0xd2, 0x0f, 0x04, 0x2d, 0xd0, 0x06, 0xc8, 0x43, 0x81, 0x00, 0x7d, 0x4c,
	0x51, 0xf4, 0xb5, 0xcf, 0x45, 0xfb, 0xdc, 0x97, 0x16, 0x05, 0xda, 0xfe, 0x05, 0x7d, 0xeb, 0x7f,
	0xd0, 0xa7, 0x62, 0x3f, 0xee, 0xb8, 0x24, 0x8f, 0xd1, 0x91, 0x72, 0x1e, 0xfa, 0xb6, 0x37, 0xbb,
	0x3b, 0x33, 0x3b, 0x33, 0x3b, 0x3b, 0x33, 0xbb, 0x07, 0xeb, 0x94, 0xde, 0x78, 0xd4, 0x71, 0x1a,
	0x0f, 0x89, 0xe3, 0x3e, 0xc6, 0xe1, 0x0d, 0xea, 0x07, 0x4e, 0xc3, 0x6a, 0x1c, 0xdb, 0x9e, 0x87,
	0xdd, 0x1b, 0x61, 0xa3, 0xdd, 0x8c, 0x3e, 0x2c, 0xd7, 0xae, 0x63, 0x77, 0x35, 0x08, 0x7d, 0xea,
	0xa3, 0xcb, 0x3d, 0xe3, 0x56, 0x07, 0xc7, 0x19, 0x3b, 0x70, 0x69, 0x1b, 0xd3, 0x37, 0x1d, 0x97,
	0xe2, 0x70, 0xd3, 0xf5, 0x1b, 0x0f, 0xf7, 0x03, 0xea, 0xf8, 0xde, 0xae, 0x43, 0xa8, 0x89, 0x1f,
	0x21, 0x1d, 0xb2, 0x1d, 0xa7, 0xb9, 0xa2, 0x5d, 0xd6, 0xae, 0xce, 0x98, 0xac, 0x89, 0x96, 0x61,
	0x9a, 0xda, 0x75, 0xcb, 0x69, 0x92, 0x95, 0xcc, 0xe5, 0xec, 0xd5, 0x19, 0x73, 0x8a, 0xda, 0xf5,
	0x6a, 0x93, 0x18, 0x1f, 0xc2, 0x33, 0xc3, 0x31, 0x91, 0x00, 0x7d, 0x00, 0xf3, 0x75, 0x06, 0xb6,
	0x7c, 0x0e, 0xb7, 0x5c, 0x87, 0xd0, 0x15, 0xed, 0x72, 0xf6, 0x6a, 0x61, 0xed, 0xc5, 0xd5, 0xb3,
	0xf8, 0x5c, 0xdd, 0xb6, 0xdb, 0x58, 0xc1, 0x6a, 0xce, 0xd5, 0x7b, 0x49, 0x18, 0xff, 0xca, 0xc0,
	0x82, 0x89, 0x8f, 0x42, 0x4c, 0x8e, 0xd9, 0xd8, 0x5d, 0x36, 0x2b, 0x79, 0x05, 0x8b, 0x30, 0x25,
	0x56, 0xb0, 0x92, 0xe1, 0xc0, 0x49, 0xbe, 0x00, 0xb4, 0x0b, 0x85, 0x96, 0xdd, 0xc6, 0x82, 0x1e,
	0x59, 0xc9, 0x72, 0xce, 0xbe, 0x91, 0x8e, 0x33, 0x41, 0x0d, 0x5a, 0x51, 0x93, 0xa0, 0x06, 0xcc,
	0x86, 0x82, 0x1b, 0x8b, 0xf8, 0x9d, 0xb0, 0x81, 0x57, 0x26, 0x2e, 0x6b, 0x57, 0x67, 0xd7, 0x5e,
	0x3b, 0x1b, 0x61, 0xc2, 0x2a, 0x56, 0x0f, 0x39, 0x0e, 0x73, 0x46, 0xe2, 0x14, 0x9f, 0xaa, 0x2e,
	0x26, 0x7b, 0x74, 0xf1, 0x2e, 0x4c, 0xc9, 0x21, 0xf3, 0x30, 0x23, 0x5a, 0x55, 0xef, 0xb1, 0xed,
	0x3a, 0x4d, 0xfd, 0x02, 0x7a, 0x06, 0x56, 0x24, 0x3a, 0x81, 0xac, 0xe2, 0x3a, 0xd8, 0xa3, 0x15,
	0xbb, 0x71, 0x8c, 0x75, 0x0d, 0x3d, 0x0b, 0x17, 0x7b, 0x7a, 0x0f, 0x71, 0xf8, 0x18, 0x87, 0x62,
	0x55, 0x7a, 0xc6, 0x68, 0x42, 0x69, 0x90, 0x3f, 0x12, 0xf4, 0x4b, 0x4f, 0x3b, 0x97, 0xf4, 0x8c,
	0x90, 0x5b, 0xe5, 0x66, 0x87, 0x38, 0x1e, 0x26, 0x44, 0x52, 0x71, 0x6d, 0xae, 0x79, 0xfc, 0x08,
	0x1d, 0xc2, 0x4c, 0x5d, 0xf6, 0x59, 0xf4, 0x34, 0xc0, 0x9c, 0xdc, 0xec, 0xda, 0xea, 0xd9, 0xe4,
	0x22, 0x94, 0xb5, 0xd3, 0x00, 0x9b, 0xc5, 0xba, 0xf2, 0x65, 0x7c, 0xa4, 0xc1, 0x62, 0x22, 0xc5,
	0x24, 0x72, 0xda, 0x79, 0xc9, 0x21, 0x04, 0x13, 0x4d, 0x9b, 0xda, 0xdc, 0x06, 0x8b, 0x26, 0x6f,
	0x1b, 0x18, 0x0a, 0x4c, 0x1e, 0x15, 0x3b, 0x6c, 0xee, 0x07, 0x14, 0x5d, 0x86, 0x22, 0x97, 0x69,
	0xc3, 0x0e, 0x9b, 0x56, 0x6c, 0xc3, 0x5c, 0x4e, 0x6c, 0x48, 0x95, 0x9b, 0xb2, 0x1f, 0x50, 0xc5,
	0x94, 0xfd, 0x80, 0x56, 0x9b, 0xe8, 0x59, 0x80, 0x86, 0xef, 0x1d, 0x59, 0x8f, 0x6d, 0xb7, 0x83,
	0x57, 0xb2, 0x97, 0xb5, 0xab, 0x79, 0x33, 0xcf, 0x20, 0xf7, 0x18, 0xc0, 0xf8, 0xb1, 0x06, 0x33,
	0x11, 0x1d, 0xbe, 0x52, 0x54, 0x82, 0x49, 0xce, 0x30, 0x27, 0x91, 0x37, 0xc5, 0x07, 0x7a, 0x1f,
	0x16, 0x18, 0xf6, 0x2e, 0x2a, 0xb1, 0x67, 0x33, 0x5c, 0xb7, 0xd7, 0xd3, 0xe9, 0x56, 0xae, 0xc5,
	0xd4, 0xfd, 0x80, 0x56, 0x22, 0x0e, 0xf8, 0x86, 0xfd, 0xa7, 0x06, 0x0b, 0xca, 0x8e, 0x8e, 0x06,
	0x2b, 0xdb, 0x53, 0x53, 0xb7, 0xe7, 0x1d, 0xc8, 0xc7, 0xee, 0x83, 0xaf, 0x36, 0x15, 0x0b, 0xaa,
	0xcb, 0xc8, 0x45, 0x2e, 0x63, 0xd8, 0xc2, 0xb2, 0x4f, 0x67, 0x61, 0x01, 0x2c, 0x72, 0xa9, 0x46,
	0xa3, 0x62, 0x3b, 0xba, 0x0f, 0x73, 0x5d, 0x7d, 0x46, 0xf2, 0x66, 0x24, 0x6f, 0xa4, 0x27, 0x29,
	0x2c, 0x73, 0xa6, 0xa5, 0x7e, 0x1a, 0x9f, 0x69, 0x70, 0x29, 0x41, 0x94, 0x31, 0x61, 0x02, 0x97,
	0x7a, 0x5c, 0xaf, 0xc2, 0x45, 0xd7, 0x09, 0xaf, 0x8f, 0x24, 0xcd, 0x98, 0xc6, 0x72, 0x7d, 0x10,
	0xc8, 0xc5, 0xf0, 0xa9, 0xc6, 0x0f, 0x84, 0x21, 0x9b, 0x98, 0x04, 0xc8, 0x87, 0xe5, 0x78, 0x5b,
	0x71, 0xfc, 0x56, 0x28, 0xbb, 0x25, 0x47, 0x2f, 0xa5, 0xdf, 0x60, 0xbd, 0xd8, 0x17, 0xeb, 0x49,
	0x60, 0xe3, 0xb7, 0x1a, 0x94, 0xee, 0xda, 0xb4, 0x71, 0x1c, 0xf1, 0x59, 0xf5, 0x8e, 0xfc, 0x91,
	0xce, 0x88, 0x12, 0x4c, 0x52, 0x7c, 0x42, 0xc5, 0xe9, 0x90, 0x37, 0xc5, 0x07, 0x3a, 0x80, 0x59,
	0xd6, 0xb0, 0x02, 0xdb, 0x09, 0x85, 0x44, 0x27, 0x38, 0xff, 0xd7, 0xce, 0xe6, 0xbf, 0x86, 0x4f,
	0xe8, 0x81, 0xed, 0x84, 0x66, 0x91, 0xca, 0x16, 0x97, 0xdd, 0x17, 0x19, 0xc8, 0x45, 0x5d, 0x6c,
	0x37, 0x0b, 0x23, 0x65, 0x43, 0xb8, 0x68, 0xf2, 0x66, 0x9e, 0x43, 0xd8, 0x10, 0xd6, 0x4d, 0x1d,
	0xea, 0xca, 0xee, 0x8c, 0xd8, 0xec, 0x1c, 0xc2, 0xbb, 0x0f, 0x20, 0xcf, 0x99, 0xe3, 0x8e, 0x2b,
	0xcb, 0x1d, 0xd7, 0xcd, 0xf4, 0x7c, 0xf1, 0x06, 0xf7, 0x5e, 0x39, 0x2a, 0x5b, 0xc6, 0x27, 0x9a,
	0x60, 0x8e, 0xbb, 0xb1, 0x05, 0x98, 0x8b, 0xda, 0x5b, 0xf8, 0xc8, 0xee, 0xb8, 0x54, 0xbf, 0x80,
	0x16, 0x61, 0x3e, 0x02, 0x72, 0x79, 0xd7, 0xb0, 0xdd, 0xd6, 0x35, 0x54, 0x86, 0xa5, 0x08, 0xbc,
	0x7f, 0x74, 0xe4, 0x34, 0xf0, 0xdb, 0x1d, 0x4c, 0x98, 0x66, 0xf4, 0x8c, 0xda, 0x57, 0xe9, 0x10,
	0xea, 0xb7, 0xe3, 0xbe, 0x2c, 0x5a, 0x86, 0x85, 0xa8, 0x6f, 0xa3, 0xd5, 0x9d, 0x34, 0x61, 0xbc,
	0x05, 0x45, 0x55, 0x95, 0x8a, 0x3b, 0xd4, 0x54, 0x77, 0xf8, 0x1c, 0xcc, 0x32, 0x70, 0x9f, 0x0b,
	0xcb, 0x9b, 0x45, 0x3f, 0xa0, 0xdd, 0x6d, 0xfb, 0xa9, 0x06, 0x8b, 0x09, 0xd6, 0x41, 0x82, 0x14,
	0x7e, 0xb8, 0x06, 0xb3, 0xca, 0x08, 0xef, 0xc8, 0x97, 0x4e, 0x72, 0x35, 0xfd, 0xc6, 0xe6, 0xd4,
	0x8a, 0x2d, 0xe5, 0xcb, 0xf8, 0x87, 0x06, 0x0b, 0x95, 0x0e, 0xdd, 0xf0, 0x9a, 0x9c, 0xaf, 0x38,
	0xa4, 0x41, 0x30, 0x21, 0x4d, 0x81, 0xe9, 0x9a, 0xb7, 0xd1, 0xbb, 0x90, 0x6b, 0x74, 0xa4, 0x96,
	0x33, 0x5c, 0xcb, 0xaf, 0x9f, 0x4d, 0x3b, 0x01, 0x39, 0x83, 0x71, 0x7d, 0x4f, 0x37, 0x44, 0x03,
	0x5d, 0x87, 0x05, 0x87, 0x58, 0x9e, 0x4f, 0xad, 0x10, 0xd3, 0x4e, 0xe8, 0x49, 0xcf, 0xc5, 0x4c,
	0x29, 0x67, 0xea, 0x0e, 0xd9, 0xf3, 0xa9, 0xc9, 0x3b, 0x84, 0x2f, 0xba, 0x02, 0xd3, 0x12, 0x05,
	0xca, 0xc3, 0xe4, 0x1d, 0x07, 0xd7, 0x6d, 0xfd, 0x02, 0x2a, 0xc0, 0x74, 0xc5, 0xf7, 0xa8, 0xed,
	0x78, 0xba, 0x66, 0x7c, 0x08, 0xa5, 0x41, 0xca, 0x24, 0x40, 0x15, 0x98, 0x1a, 0x3f, 0x7c, 0x90,
	0x53, 0x99, 0xb2, 0x1c, 0x62, 0x1d, 0x3b, 0x54, 0xf2, 0x99, 0xe1, 0x7c, 0x82, 0x43, 0x76, 0x1c,
	0x2a, 0x38, 0x5c, 0x81, 0xa5, 0xaa, 0x47, 0x71, 0xe8, 0xd9, 0xee, 0x36, 0xa6, 0x1b, 0xae, 0x1b,
	0xad, 0xdd, 0x78, 0x19, 0x96, 0x13, 0x7b, 0x48, 0xc0, 0x76, 0x99, 0xf0, 0x51, 0x8a, 0xe4, 0xf3,
	0x1c, 0xc2, 0x4c, 0xd3, 0x78, 0x00, 0xe5, 0x6d, 0x4c, 0xb9, 0xa7, 0xc1, 0xcd, 0x83, 0x4e, 0xdd,
	0x75, 0x88, 0x58, 0x18, 0x19, 0xc9, 0xbf, 0x44, 0x9a, 0xcd, 0x76, 0x35, 0x6b, 0xfc, 0x55, 0x63,
	0x81, 0xed, 0x00, 0x62, 0x64, 0xc2, 0x8c, 0xea, 0xd4, 0x09, 0x47, 0x3f, 0xf2, 0xa1, 0x58, 0x54,
	0xdc, 0x37, 0x41, 0x57, 0xa0, 0x18, 0x08, 0x1a, 0x96, 0x67, 0xb7, 0xb1, 0xf4, 0x26, 0x05, 0x09,
	0xdb, 0xb3, 0xdb, 0x18, 0x6d, 0x44, 0xa1, 0xc2, 0x18, 0x01, 0xb2, 0x98, 0x69, 0x7c, 0x94, 0x61,
	0x41, 0x64, 0xc3, 0x6f, 0xb7, 0xb1, 0xd7, 0xe4, 0xae, 0x59, 0xd0, 0x47, 0xff, 0x0f, 0x05, 0x79,
	0x42, 0xc5, 0x61, 0xd6, 0x8c, 0x09, 0x02, 0xc4, 0x2d, 0x2a, 0x26, 0x2e, 0x02, 0x80, 0x31, 0x88,
	0x0f, 0x8a, 0x2d, 0x7b, 0x6e, 0xb1, 0x19, 0x2f, 0xc0, 0x04, 0x67, 0xaf, 0x00, 0xd3, 0x5d, 0x27,
	0x58, 0x80, 0x69, 0xa9, 0x2f, 0x5d, 0x43, 0xb3, 0x00, 0xb5, 0x63, 0x47, 0x9e, 0xd7, 0x7a, 0xc6,
	0x70, 0x98, 0x4e, 0x55, 0x09, 0x54, 0xbd, 0xa0, 0x43, 0xbf, 0x0a, 0x9d, 0x1a, 0x7f, 0xcf, 0xf0,
	0x60, 0x3a, 0xd9, 0x36, 0x49, 0x80, 0xde, 0x83, 0x99, 0x48, 0xe7, 0x6a, 0x4c, 0xb2, 0x9e, 0x26,
	0x51, 0x19, 0x40, 0x69, 0x46, 0xf6, 0x23, 0x6c, 0xb4, 0x0d, 0x4b, 0x61, 0xcf, 0x32, 0xe3, 0x85,
	0x09, 0xff, 0x78, 0x2b, 0x0d, 0x91, 0x41, 0x43, 0x31, 0x17, 0xc3, 0x04, 0x28, 0x41, 0xdf, 0x85,
	0xbe, 0x0e, 0xcb, 0x61, 0x62, 0x8d, 0x92, 0xb9, 0xf5, 0x51, 0xa9, 0x71, 0xa5, 0x98, 0xa5, 0x70,
	0x10, 0x48, 0x8c, 0x9f, 0x66, 0x60, 0xfe, 0x10, 0xdb, 0xa1, 0x38, 0x2f, 0xd8, 0xd6, 0x48, 0xde,
	0xe9, 0xd1, 0x96, 0xce, 0x28, 0xce, 0xda, 0x82, 0x82, 0x48, 0x0a, 0xd5, 0x53, 0xf9, 0x8d, 0xb3,
	0xb9, 0x1b, 0xa0, 0x27, 0xf3, 0x42, 0xee, 0xb0, 0x81, 0xc4, 0x6d, 0xc3, 0x01, 0xe8, 0xf6, 0xb0,
	0xe3, 0x58, 0xce, 0xec, 0x38, 0x4d, 0x2c, 0x3a, 0xf4, 0x0b, 0x68, 0x09, 0x90, 0x59, 0xb9, 0xbb,
	0xc5, 0x77, 0x88, 0x43, 0xa8, 0x84, 0x6b, 0x0c, 0x5e, 0x63, 0x9c, 0x54, 0x04, 0x6d, 0x09, 0xcf,
	0xa0, 0x12, 0xe8, 0x02, 0xcd, 0x81, 0x1f, 0x8f, 0xce, 0x1a, 0xef, 0x41, 0x8e, 0x61, 0x88, 0xce,
	0xdf, 0xa4, 0xd0, 0xfd, 0x12, 0xe4, 0xf9, 0xe9, 0xa8, 0xb8, 0x94, 0x5c, 0x4b, 0x2e, 0x02, 0x95,
	0x21, 0x47, 0x4e, 0x3d, 0xdf, 0x3b, 0x6d, 0x47, 0x51, 0x55, 0xfc, 0x6d, 0xfc, 0x5e, 0x03, 0xd4,
	0xbf, 0x66, 0x12, 0xa0, 0x3b, 0x00, 0xdd, 0x5c, 0x73, 0x9c, 0xb3, 0x22, 0x1f, 0xa7, 0x9a, 0x2c,
	0x76, 0xe3, 0xb8, 0xd8, 0xa1, 0xad, 0xa6, 0x37, 0xd7, 0xd2, 0xe1, 0xeb, 0x9e, 0xda, 0xac, 0xc5,
	0xe3, 0x88, 0xd7, 0xa0, 0x24, 0x79, 0x76, 0xfd, 0xba, 0xed, 0x7e, 0x49, 0x21, 0x22, 0xc1, 0x34,
	0x8c, 0x3f, 0x69, 0x50, 0x10, 0xd3, 0x05, 0x7f, 0x3d, 0xb2, 0xd3, 0xfa, 0x64, 0xa7, 0xb8, 0xc3,
	0x31, 0x7d, 0x31, 0x3a, 0x80, 0xa2, 0x38, 0xd7, 0x9a, 0x98, 0xda, 0x8e, 0x9b, 0x3e, 0x07, 0xe2,
	0x58, 0xb6, 0xf8, 0x24, 0xb3, 0xe0, 0x76, 0x3f, 0x8c, 0x10, 0x16, 0x13, 0xd6, 0x4f, 0x02, 0xf4,
	0x00, 0xe6, 0x09, 0xef, 0x90, 0xd1, 0xbe, 0x92, 0x7b, 0x5c, 0x4f, 0x6b, 0xfb, 0x02, 0xdb, 0x1c,
	0xe9, 0x7e, 0x70, 0x99, 0x6f, 0x40, 0x41, 0x71, 0x80, 0xe8, 0x22, 0x88, 0x5c, 0xaf, 0x6b, 0x89,
	0xd3, 0xfc, 0xbb, 0xca, 0xcb, 0x57, 0xd8, 0xc5, 0xed, 0xee, 0xc9, 0x3b, 0xc5, 0x3e, 0xab, 0x4d,
	0xe3, 0x07, 0x30, 0xd7, 0x57, 0x63, 0x1a, 0x66, 0xce, 0x03, 0x4e, 0x3a, 0x75, 0x42, 0x3c, 0xdc,
	0x49, 0x9f, 0xc0, 0xf2, 0x36, 0xa6, 0xd2, 0x93, 0xc6, 0x4a, 0xe2, 0xc1, 0xc3, 0x07, 0x30, 0xcf,
	0x2d, 0x40, 0xa5, 0x79, 0x8e, 0xba, 0x59, 0xab, 0x17, 0x60, 0xfc, 0x4e, 0x03, 0x5d, 0x2d, 0xd7,
	0x7d, 0x65, 0xb1, 0xc5, 0xf9, 0x8d, 0x95, 0x85, 0xe8, 0xa5, 0x7e, 0x5e, 0x99, 0xfe, 0x87, 0x69,
	0xea, 0x3b, 0x80, 0x7a, 0x4b, 0x8e, 0x0a, 0xfd, 0xb5, 0x91, 0xd6, 0x22, 0xd8, 0xd0, 0xeb, 0x7d,
	0x10, 0xe3, 0x67, 0x1a, 0xac, 0x24, 0x2b, 0x8e, 0x04, 0xa8, 0x0d, 0xcb, 0x83, 0xe4, 0x55, 0xb3,
	0xbf, 0x35, 0x3a, 0x0f, 0xbc, 0x9c, 0x5a, 0xaa, 0x27, 0x40, 0x8d, 0x13, 0xd0, 0x2b, 0xbe, 0xf7,
	0x18, 0x87, 0x34, 0xe6, 0xa3, 0x27, 0xa4, 0xd6, 0xc6, 0x0d, 0xa9, 0x59, 0x58, 0x16, 0x3a, 0x2d,
	0xc7, 0x53, 0x53, 0x4c, 0x10, 0x20, 0x1e, 0xfd, 0x9e, 0x42, 0xa9, 0x9f, 0x32, 0x57, 0xcb, 0x9d,
	0xbe, 0x80, 0x3e, 0x85, 0xcc, 0xfb, 0xf1, 0xc4, 0x4c, 0x24, 0x47, 0xcc, 0x46, 0x0b, 0x16, 0xfb,
	0xa7, 0x08, 0xe1, 0xef, 0xf5, 0xd1, 0xbe, 0x35, 0x3a, 0x6d, 0x2e, 0x6b, 0x89, 0xc5, 0x78, 0x03,
	0xe6, 0x94, 0xfe, 0x2f, 0x3b, 0xee, 0xe2, 0x22, 0x41, 0x46, 0x29, 0x12, 0x18, 0x9f, 0x67, 0x06,
	0x85, 0x34, 0x24, 0x39, 0x38, 0x90, 0xe7, 0x1b, 0x3b, 0x93, 0x22, 0xef, 0xf2, 0xe2, 0x48, 0xec,
	0xf3, 0x63, 0x29, 0x1f, 0x1d, 0x4b, 0x04, 0x3d, 0x80, 0x29, 0x59, 0x85, 0x16, 0xb1, 0xc6, 0xc6,
	0xe8, 0xc2, 0x20, 0x7d, 0xe1, 0x86, 0x44, 0x68, 0x6c, 0xf6, 0x84, 0x1a, 0x2c, 0x02, 0xbe, 0xfd,
	0xe6, 0xc6, 0x3b, 0xbb, 0x35, 0xfd, 0x02, 0x5a, 0x10, 0x2e, 0x55, 0xc6, 0x11, 0x4c, 0x9a, 0xba,
	0x86, 0x10, 0xcc, 0x46, 0x19, 0xfc, 0x81, 0x1f, 0xdc, 0x77, 0x3c, 0x3d, 0x63, 0x7c, 0x1b, 0xf4,
	0x6d, 0x4c, 0x85, 0x87, 0xdf, 0x71, 0x3c, 0x3a, 0x4a, 0xce, 0x64, 0xfc, 0x5a, 0x83, 0xf9, 0xbe,
	0xd9, 0x24, 0x60, 0x67, 0xeb, 0xb1, 0xe3, 0xc5, 0x39, 0x32, 0x6b, 0xa3, 0x03, 0x98, 0x50, 0xf2,
	0xe3, 0x14, 0x95, 0xf8, 0x01, 0xb4, 0xab, 0xac, 0xc1, 0x97, 0xcf, 0x31, 0x19, 0x57, 0x20, 0x17,
	0x41, 0x7a, 0x97, 0x9e, 0x83, 0x09, 0x16, 0x5b, 0xe9, 0x9a, 0x71, 0x04, 0xb3, 0x5c, 0x7e, 0x02,
	0xd1, 0x79, 0xb3, 0x41, 0xb5, 0xe4, 0x3f, 0xd1, 0x53, 0xf2, 0xbf, 0x07, 0x73, 0x3d, 0x74, 0x9e,
	0x52, 0x3e, 0x6d, 0x7c, 0xa1, 0x41, 0xbe, 0xeb, 0x4f, 0x74, 0xc8, 0x3e, 0xb6, 0xa3, 0x32, 0x31,
	0x6b, 0xb2, 0x94, 0xb1, 0xe9, 0x90, 0xc0, 0xb5, 0x4f, 0x7b, 0x52, 0x46, 0x09, 0xe3, 0x61, 0x4a,
	0x45, 0xca, 0x5d, 0xd8, 0xde, 0x8d, 0x11, 0xb8, 0xe8, 0x8a, 0x5a, 0x49, 0xc0, 0x19, 0xaa, 0x09,
	0x2e, 0x27, 0x99, 0x80, 0x33, 0x4d, 0xfc, 0x52, 0x83, 0xf9, 0x8a, 0x6b, 0x13, 0xe2, 0x1c, 0x9d,
	0x76, 0x1d, 0xd0, 0xd7, 0x60, 0xa6, 0x21, 0x81, 0x6a, 0x04, 0x55, 0x8c, 0x80, 0x9c, 0xbd, 0x1a,
	0xcc, 0xc5, 0x83, 0xa4, 0xbc, 0xc6, 0x38, 0xa2, 0x66, 0x1b, 0x2a, 0x75, 0x62, 0xfc, 0x46, 0x83,
	0xe9, 0x9a, 0x5d, 0xaf, 0x52, 0xdc, 0x1e, 0xe6, 0x28, 0x96, 0x61, 0xba, 0xe1, 0xb7, 0x63, 0xbd,
	0xe7, 0xcd, 0x29, 0xf6, 0x59, 0x6d, 0xb2, 0xf8, 0x85, 0x8d, 0x8f, 0x85, 0x36, 0x63, 0x32, 0xa5,
	0xf3, 0x75, 0xee, 0x73, 0xac, 0xdc, 0xe0, 0x4a, 0xa0, 0xcb, 0xe6, 0xa1, 0xe3, 0xb5, 0x5c, 0x5c,
	0xb3, 0xeb, 0xfa, 0x05, 0x05, 0x7a, 0x97, 0x56, 0xfc, 0x76, 0x9d, 0x41, 0x35, 0xb4, 0x02, 0x25,
	0x09, 0xdd, 0xb6, 0xbd, 0xd6, 0x3b, 0x41, 0xd4, 0x93, 0x31, 0xfe, 0xa6, 0xf1, 0xcd, 0x77, 0x96,
	0x4f, 0x1a, 0x62, 0xa2, 0x07, 0x30, 0x53, 0x0f, 0xfd, 0x27, 0xe4, 0x3c, 0xd7, 0x66, 0x45, 0x81,
	0x41, 0x5e, 0x9c, 0x6d, 0xc2, 0x74, 0x88, 0x1f, 0x59, 0xd4, 0xae, 0x73, 0x25, 0x17, 0xd6, 0xbe,
	0x9e, 0xa2, 0x5a, 0x29, 0xe4, 0x6c, 0x4e, 0x85, 0xf8, 0x51, 0xcd, 0xae, 0x1b, 0x7f, 0x11, 0x2e,
	0xa1, 0xef, 0x44, 0x78, 0x2a, 0xe5, 0xa5, 0x25, 0x98, 0xc2, 0x9e, 0x5d, 0x77, 0xb1, 0x2c, 0x2c,
	0xc9, 0x2f, 0xf4, 0xfe, 0xa0, 0x11, 0x09, 0x51, 0xa4, 0x28, 0xb6, 0x0e, 0xd8, 0xed, 0x80, 0x31,
	0x59, 0x50, 0xd8, 0xec, 0x0a, 0x69, 0x50, 0xea, 0xda, 0x39, 0xa5, 0x6e, 0x7c, 0x3c, 0x09, 0x8b,
	0x9b, 0x36, 0x6d, 0x1c, 0xef, 0xf8, 0x67, 0x9a, 0xc2, 0xb0, 0x1b, 0x60, 0x74, 0x02, 0xf3, 0x3d,
	0x6c, 0x59, 0x6d, 0x3b, 0x90, 0x52, 0xd8, 0x4d, 0x11, 0xe9, 0x24, 0x91, 0x5f, 0x55, 0x96, 0x4d,
	0xee, 0xda, 0xc1, 0x6d, 0x8f, 0x86, 0xa7, 0xe6, 0x5c, 0xbd, 0x17, 0x8a, 0xb6, 0xc4, 0x86, 0x51,
	0x6a, 0xef, 0x23, 0x58, 0x0d, 0x5b, 0x0d, 0xf7, 0x16, 0x9f, 0x6a, 0x50, 0xe6, 0x1b, 0x92, 0xe1,
	0x1a, 0x5c, 0xc9, 0x24, 0x47, 0x7c, 0x38, 0xee, 0x4a, 0xe4, 0x96, 0x4b, 0x5c, 0xd0, 0x52, 0x23,
	0xb1, 0xb3, 0xfc, 0x08, 0x4a, 0x49, 0xe3, 0x99, 0x52, 0x1e, 0xe2, 0xd3, 0x48, 0x29, 0x0f, 0xf1,
	0x29, 0xaa, 0xc0, 0xa4, 0xb8, 0xed, 0x4b, 0x7f, 0x35, 0xd6, 0x45, 0x6c, 0x8a, 0xb9, 0xaf, 0x66,
	0x5e, 0xd6, 0xca, 0x27, 0x70, 0xe9, 0x4b, 0x38, 0x55, 0x29, 0xe7, 0x9f, 0x2e, 0x65, 0xe3, 0xcf,
	0x1a, 0x14, 0x55, 0xa9, 0xfd, 0x2f, 0x6f, 0xd8, 0xcf, 0x26, 0x60, 0x29, 0xc9, 0x0c, 0x48, 0x80,
	0x3e, 0x84, 0x85, 0x63, 0x9f, 0x5a, 0xca, 0x6d, 0x39, 0xb7, 0x2e, 0xb1, 0xc4, 0xbd, 0xf1, 0xac,
	0x8b, 0x45, 0x26, 0x2a, 0x24, 0x36, 0x2c, 0xfd, 0xb8, 0x0f, 0x8c, 0x7e, 0xae, 0xc1, 0x4a, 0x6c,
	0xe4, 0xfd, 0x4c, 0x88, 0x73, 0xcf, 0x1c, 0x9b, 0x09, 0x69, 0x39, 0x09, 0x8c, 0x94, 0x1a, 0x09,
	0x5d, 0x65, 0x02, 0x8b, 0x89, 0x7c, 0x27, 0x18, 0xf8, 0x56, 0xaf, 0x99, 0xa5, 0xb8, 0x59, 0x51,
	0x31, 0xab, 0x16, 0xfe, 0x04, 0x2e, 0x0e, 0xe5, 0x33, 0xc1, 0xbe, 0x9f, 0x1a, 0x61, 0xe3, 0x3f,
	0x1a, 0x14, 0x2b, 0x1d, 0x7a, 0xdf, 0x0f, 0x9b, 0x07, 0x76, 0x68, 0xb7, 0xd1, 0xff, 0x41, 0xc1,
	0x21, 0x56, 0x87, 0x60, 0x8b, 0x50, 0x3f, 0xe0, 0x44, 0x73, 0x66, 0xde, 0x21, 0xef, 0x10, 0x7c,
	0x48, 0xfd, 0x00, 0x3d, 0x03, 0x20, 0xfb, 0x8f, 0xdb, 0x6d, 0x69, 0xbf, 0x39, 0xde, 0xbd, 0xd3,
	0x6e, 0xa3, 0xb7, 0x21, 0x47, 0x70, 0x4b, 0x2d, 0x21, 0xde, 0x4a, 0x75, 0xe5, 0x13, 0xd3, 0x5f,
	0x3d, 0xc4, 0x2d, 0x71, 0xd7, 0x43, 0x44, 0xc3, 0x78, 0x00, 0xd3, 0x12, 0xc6, 0x1f, 0x8e, 0x88,
	0xe6, 0x16, 0x3e, 0xb2, 0x79, 0x45, 0xbb, 0x0b, 0xda, 0xf3, 0xc3, 0xb6, 0xed, 0xea, 0x1a, 0xd2,
	0xa1, 0x28, 0x41, 0xb2, 0xb2, 0x8d, 0x96, 0x00, 0x49, 0x48, 0x74, 0x37, 0x7b, 0x88, 0x5b, 0x7a,
	0xd6, 0xf8, 0xa3, 0x06, 0x20, 0x89, 0xa7, 0x2f, 0x94, 0x96, 0x60, 0xd2, 0x75, 0xda, 0x0e, 0x95,
	0x51, 0x90, 0xf8, 0x90, 0x62, 0xf3, 0x7c, 0x2a, 0xc4, 0x36, 0x11, 0x89, 0x6d, 0xcf, 0xa7, 0x5c,
	0x6c, 0x35, 0x98, 0x6d, 0x74, 0xa8, 0xf5, 0xc4, 0x0f, 0x9b, 0x56, 0xc0, 0x16, 0xba, 0x32, 0x99,
	0x56, 0x75, 0xaa, 0x78, 0xcc, 0x62, 0x43, 0xf9, 0x32, 0x9e, 0x87, 0x42, 0xcc, 0x3f, 0x09, 0x98,
	0x5f, 0x51, 0x9c, 0x53, 0x3e, 0x8e, 0x97, 0x7f, 0x28, 0xe3, 0x70, 0xf9, 0x4a, 0x66, 0xa4, 0x68,
	0xaa, 0xeb, 0xf0, 0xb2, 0xe3, 0x07, 0xec, 0x7f, 0xc8, 0x42, 0x41, 0x29, 0xce, 0x31, 0xb9, 0x2a,
	0xa1, 0x2f, 0x6f, 0xb3, 0x8c, 0x3e, 0xb0, 0x43, 0xec, 0x51, 0x35, 0x66, 0x07, 0x01, 0xe2, 0x31,
	0xf1, 0x01, 0xe4, 0x5c, 0xbf, 0xa1, 0xda, 0xd6, 0xfa, 0x48, 0x25, 0xc1, 0xd5, 0x5d, 0xbf, 0x21,
	0x4c, 0xcb, 0x15, 0x0d, 0x86, 0xb1, 0x85, 0xfd, 0x6e, 0xf4, 0x3e, 0x32, 0xc6, 0x6d, 0xec, 0x0b,
	0x8c, 0x2d, 0xd1, 0x60, 0x51, 0x32, 0xc3, 0xc8, 0xaf, 0x5b, 0x27, 0xf9, 0x0a, 0x58, 0x17, 0xcf,
	0xcc, 0x9f, 0x83, 0x59, 0x9f, 0x1e, 0xe3, 0x90, 0x3b, 0x39, 0xbe, 0xc4, 0x29, 0x11, 0xf8, 0x73,
	0x68, 0xcd, 0xae, 0xb3, 0x45, 0x1a, 0x6f, 0xc1, 0xb4, 0x64, 0x93, 0xa5, 0xaa, 0xb2, 0x69, 0xed,
	0xed, 0xd7, 0x76, 0xaa, 0x7b, 0xdb, 0xfa, 0x05, 0x54, 0x86, 0xa5, 0x08, 0x78, 0xb8, 0x71, 0xf7,
	0xb6, 0xb5, 0xbb, 0x5f, 0xb1, 0xee, 0x6e, 0xd4, 0x2a, 0x3b, 0xba, 0x86, 0xe6, 0xa0, 0x10, 0xf5,
	0xed, 0xee, 0x57, 0xf4, 0x8c, 0xf1, 0x26, 0x4c, 0x4b, 0x0e, 0x79, 0xde, 0x2b, 0x9a, 0x56, 0x75,
	0xef, 0xde, 0xc6, 0x6e, 0x75, 0x4b, 0xc4, 0xe5, 0x11, 0xf0, 0xc0, 0xdc, 0xbf, 0x57, 0xdd, 0xab,
	0xdc, 0x16, 0xfb, 0x27, 0x82, 0x56, 0xaa, 0xb5, 0x07, 0x7a, 0xc6, 0xf8, 0x51, 0x06, 0xf4, 0x5e,
	0x03, 0x7a, 0x5a, 0xa1, 0xeb, 0x6d, 0x98, 0x92, 0x45, 0xde, 0xcc, 0x38, 0x45, 0x5e, 0x39, 0x19,
	0xad, 0xc2, 0x82, 0x38, 0x42, 0x2d, 0x82, 0x1b, 0xbe, 0xd7, 0xec, 0xb9, 0x0f, 0x9e, 0x17, 0x5d,
	0x87, 0xbc, 0x47, 0x9c, 0xe2, 0x6b, 0xb0, 0x28, 0xc7, 0x3f, 0xc4, 0xa7, 0x7c, 0x4f, 0x3e, 0xc1,
	0x4e, 0xeb, 0x98, 0xca, 0x7d, 0x2b, 0x91, 0xbd, 0x25, 0xfa, 0xee, 0xf3, 0xae, 0x6b, 0x6d, 0x28,
	0xaa, 0x4f, 0xa7, 0xd0, 0x32, 0x2c, 0xa8, 0xdf, 0xdd, 0x4b, 0xb6, 0x67, 0xe1, 0xa2, 0xda, 0xd1,
	0xf3, 0xee, 0x46, 0xd7, 0xd0, 0xf3, 0x70, 0x45, 0xed, 0x56, 0x8a, 0x69, 0x35, 0x3f, 0x1e, 0x96,
	0xb9, 0xf6, 0x3d, 0xf1, 0x1e, 0x2a, 0x4e, 0x39, 0x7b, 0x2f, 0xf2, 0x8a, 0x90, 0xdb, 0xf1, 0xc5,
	0xdd, 0xb1, 0xa8, 0x5f, 0xf0, 0xe6, 0xfe, 0x51, 0x74, 0xbb, 0x97, 0x51, 0x60, 0xd2, 0xb4, 0xf4,
	0x2c, 0x73, 0x96, 0x12, 0x26, 0xea, 0xe0, 0xfa, 0x04, 0x37, 0x32, 0x01, 0x8a, 0x02, 0x08, 0x7d,
	0x72, 0xed, 0xdf, 0xf3, 0xa0, 0x9b, 0x95, 0xbb, 0x5b, 0x51, 0x95, 0x84, 0xcb, 0x2c, 0x94, 0x5b,
	0x58, 0x24, 0xf3, 0xe8, 0x85, 0x94, 0x9a, 0x8a, 0x6b, 0x0c, 0xe5, 0x17, 0x47, 0x9c, 0x41, 0x02,
	0x74, 0x02, 0x33, 0x3d, 0x39, 0x13, 0x5a, 0x4b, 0x55, 0x20, 0xe9, 0x09, 0x72, 0xcb, 0x37, 0x47,
	0x9e, 0x43, 0x02, 0x74, 0xc4, 0x9f, 0x0c, 0x30, 0xcf, 0x8a, 0xbe, 0x99, 0xda, 0x45, 0x33, 0x6a,
	0xd7, 0x47, 0x18, 0x4d, 0x02, 0xd4, 0x81, 0xa2, 0xba, 0xb3, 0x50, 0x5a, 0x21, 0x75, 0x5d, 0x79,
	0x79, 0x6d, 0xd4, 0x29, 0xb1, 0x60, 0xbb, 0x85, 0xa4, 0x94, 0x82, 0xed, 0x29, 0x87, 0xa5, 0x14,
	0x6c, 0x5f, 0x11, 0xec, 0x27, 0x1a, 0xcc, 0x0f, 0xd4, 0xf1, 0xd0, 0xad, 0xf1, 0x8a, 0x7f, 0xe5,
	0x97, 0xc6, 0x9a, 0x47, 0x02, 0xf4, 0x89, 0x06, 0x68, 0x30, 0x60, 0x44, 0x2f, 0x8d, 0x99, 0x49,
	0x95, 0x5f, 0x1e, 0x37, 0x3e, 0x45, 0xbf, 0xd0, 0xa0, 0x94, 0x54, 0xae, 0x47, 0xaf, 0xa4, 0x12,
	0x6f, 0xd2, 0xfd, 0x4c, 0xf9, 0xd5, 0x71, 0xa7, 0x4a, 0x05, 0x0d, 0x5c, 0x96, 0xa5, 0x51, 0x50,
	0xd2, 0x0d, 0x63, 0x1a, 0x05, 0x25, 0xdf, 0xcc, 0x7d, 0x1f, 0x66, 0x7b, 0xaf, 0x59, 0xd1, 0xcd,
	0x31, 0x2e, 0xa3, 0xcb, 0xdf, 0x1a, 0x7d, 0x12, 0xe1, 0x09, 0xc9, 0x42, 0xc2, 0xab, 0x1b, 0x94,
	0x42, 0xcb, 0xc9, 0xcf, 0x78, 0xca, 0xaf, 0x8c, 0x39, 0x93, 0x04, 0xe8, 0x57, 0x1a, 0xbf, 0x88,
	0x4b, 0x7a, 0x2c, 0x81, 0xd2, 0x15, 0x8c, 0x87, 0xbc, 0x01, 0x2a, 0xbf, 0x7e, 0x8e, 0xd9, 0x24,
	0x40, 0x1f, 0x6b, 0xa0, 0xf7, 0x3f, 0x9a, 0x42, 0xeb, 0x63, 0x3d, 0xf1, 0x2a, 0xdf, 0x1a, 0x67,
	0x9a, 0xb4, 0xd6, 0x81, 0x17, 0x72, 0x69, 0xac, 0x35, 0xe9, 0xd1, 0x65, 0x1a, 0x6b, 0x4d, 0x7e,
	0x8e, 0xf7, 0xb9, 0xb8, 0x73, 0x4b, 0x7e, 0xab, 0x9d, 0x4e, 0xcc, 0xc3, 0x5e, 0x96, 0x97, 0xdf,
	0x38, 0xcf, 0x74, 0xa9, 0xa6, 0xfe, 0xf7, 0xf1, 0x68, 0x7d, 0xac, 0x37, 0xff, 0xe5, 0x5b, 0xe3,
	0x4c, 0xeb, 0xca, 0x27, 0xf1, 0x4f, 0x8c, 0x94, 0xf2, 0x19, 0xf6, 0x3f, 0x48, 0x4a, 0xf9, 0x0c,
	0xfd, 0x09, 0x64, 0x73, 0xeb, 0xbd, 0xcd, 0x96, 0xef, 0xda, 0x5e, 0x6b, 0x75, 0x7d, 0x8d, 0xd2,
	0xd5, 0x86, 0xdf, 0xbe, 0xc1, 0xff, 0x54, 0x69, 0xf8, 0xee, 0x0d, 0x82, 0xc3, 0xc7, 0x4e, 0x03,
	0x93, 0x33, 0x7f, 0x6e, 0xa9, 0x4f, 0xf1, 0x39, 0x37, 0xff, 0x1b, 0x00, 0x00, 0xff, 0xff, 0xed,
	0x6d, 0x1c, 0xcc, 0x16, 0x33, 0x00, 0x00,
}
