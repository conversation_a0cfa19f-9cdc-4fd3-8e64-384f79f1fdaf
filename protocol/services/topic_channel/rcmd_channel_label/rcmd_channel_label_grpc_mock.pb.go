// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/topic_channel/rcmd_channel_label.proto

package rcmd_channel_label

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockRCMDChannelLabelClient is a mock of RCMDChannelLabelClient interface.
type MockRCMDChannelLabelClient struct {
	ctrl     *gomock.Controller
	recorder *MockRCMDChannelLabelClientMockRecorder
}

// MockRCMDChannelLabelClientMockRecorder is the mock recorder for MockRCMDChannelLabelClient.
type MockRCMDChannelLabelClientMockRecorder struct {
	mock *MockRCMDChannelLabelClient
}

// NewMockRCMDChannelLabelClient creates a new mock instance.
func NewMockRCMDChannelLabelClient(ctrl *gomock.Controller) *MockRCMDChannelLabelClient {
	mock := &MockRCMDChannelLabelClient{ctrl: ctrl}
	mock.recorder = &MockRCMDChannelLabelClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRCMDChannelLabelClient) EXPECT() *MockRCMDChannelLabelClientMockRecorder {
	return m.recorder
}

// BatchHotGameLabels mocks base method.
func (m *MockRCMDChannelLabelClient) BatchHotGameLabels(ctx context.Context, in *BatchHotGameLabelsReq, opts ...grpc.CallOption) (*BatchHotGameLabelsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchHotGameLabels", varargs...)
	ret0, _ := ret[0].(*BatchHotGameLabelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchHotGameLabels indicates an expected call of BatchHotGameLabels.
func (mr *MockRCMDChannelLabelClientMockRecorder) BatchHotGameLabels(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchHotGameLabels", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).BatchHotGameLabels), varargs...)
}

// ConvertGameLabels mocks base method.
func (m *MockRCMDChannelLabelClient) ConvertGameLabels(ctx context.Context, in *ConvertGameLabelsReq, opts ...grpc.CallOption) (*ConvertGameLabelsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConvertGameLabels", varargs...)
	ret0, _ := ret[0].(*ConvertGameLabelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConvertGameLabels indicates an expected call of ConvertGameLabels.
func (mr *MockRCMDChannelLabelClientMockRecorder) ConvertGameLabels(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConvertGameLabels", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).ConvertGameLabels), varargs...)
}

// CutAndMatchLabel mocks base method.
func (m *MockRCMDChannelLabelClient) CutAndMatchLabel(ctx context.Context, in *CutAndMatchLabelReq, opts ...grpc.CallOption) (*CutAndMatchLabelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CutAndMatchLabel", varargs...)
	ret0, _ := ret[0].(*CutAndMatchLabelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CutAndMatchLabel indicates an expected call of CutAndMatchLabel.
func (mr *MockRCMDChannelLabelClientMockRecorder) CutAndMatchLabel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CutAndMatchLabel", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).CutAndMatchLabel), varargs...)
}

// CutWord mocks base method.
func (m *MockRCMDChannelLabelClient) CutWord(ctx context.Context, in *CutWordReq, opts ...grpc.CallOption) (*CutWordResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CutWord", varargs...)
	ret0, _ := ret[0].(*CutWordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CutWord indicates an expected call of CutWord.
func (mr *MockRCMDChannelLabelClientMockRecorder) CutWord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CutWord", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).CutWord), varargs...)
}

// GetBusinessLabelRelation mocks base method.
func (m *MockRCMDChannelLabelClient) GetBusinessLabelRelation(ctx context.Context, in *GetBusinessLabelRelationReq, opts ...grpc.CallOption) (*GetBusinessLabelRelationResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBusinessLabelRelation", varargs...)
	ret0, _ := ret[0].(*GetBusinessLabelRelationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBusinessLabelRelation indicates an expected call of GetBusinessLabelRelation.
func (mr *MockRCMDChannelLabelClientMockRecorder) GetBusinessLabelRelation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBusinessLabelRelation", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).GetBusinessLabelRelation), varargs...)
}

// GetFilterBlockOptionList mocks base method.
func (m *MockRCMDChannelLabelClient) GetFilterBlockOptionList(ctx context.Context, in *GetFilterBlockOptionListReq, opts ...grpc.CallOption) (*GetFilterBlockOptionListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFilterBlockOptionList", varargs...)
	ret0, _ := ret[0].(*GetFilterBlockOptionListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFilterBlockOptionList indicates an expected call of GetFilterBlockOptionList.
func (mr *MockRCMDChannelLabelClientMockRecorder) GetFilterBlockOptionList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFilterBlockOptionList", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).GetFilterBlockOptionList), varargs...)
}

// GetGameLabels mocks base method.
func (m *MockRCMDChannelLabelClient) GetGameLabels(ctx context.Context, in *GetGameLabelsReq, opts ...grpc.CallOption) (*GetGameLabelsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameLabels", varargs...)
	ret0, _ := ret[0].(*GetGameLabelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameLabels indicates an expected call of GetGameLabels.
func (mr *MockRCMDChannelLabelClientMockRecorder) GetGameLabels(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameLabels", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).GetGameLabels), varargs...)
}

// GetPublishGameLabels mocks base method.
func (m *MockRCMDChannelLabelClient) GetPublishGameLabels(ctx context.Context, in *GetPublishGameLabelsReq, opts ...grpc.CallOption) (*GetPublishGameLabelsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPublishGameLabels", varargs...)
	ret0, _ := ret[0].(*GetPublishGameLabelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPublishGameLabels indicates an expected call of GetPublishGameLabels.
func (mr *MockRCMDChannelLabelClientMockRecorder) GetPublishGameLabels(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPublishGameLabels", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).GetPublishGameLabels), varargs...)
}

// GetRelatedPublishLabels mocks base method.
func (m *MockRCMDChannelLabelClient) GetRelatedPublishLabels(ctx context.Context, in *GetRelatedPublishLabelsReq, opts ...grpc.CallOption) (*GetRelatedPublishLabelsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRelatedPublishLabels", varargs...)
	ret0, _ := ret[0].(*GetRelatedPublishLabelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelatedPublishLabels indicates an expected call of GetRelatedPublishLabels.
func (mr *MockRCMDChannelLabelClientMockRecorder) GetRelatedPublishLabels(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelatedPublishLabels", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).GetRelatedPublishLabels), varargs...)
}

// GetSearchHint mocks base method.
func (m *MockRCMDChannelLabelClient) GetSearchHint(ctx context.Context, in *GetSearchHintReq, opts ...grpc.CallOption) (*GetSearchHintResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSearchHint", varargs...)
	ret0, _ := ret[0].(*GetSearchHintResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSearchHint indicates an expected call of GetSearchHint.
func (mr *MockRCMDChannelLabelClientMockRecorder) GetSearchHint(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSearchHint", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).GetSearchHint), varargs...)
}

// InternalGetAllLabel mocks base method.
func (m *MockRCMDChannelLabelClient) InternalGetAllLabel(ctx context.Context, in *InternalGetAllLabelReq, opts ...grpc.CallOption) (*InternalGetAllLabelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InternalGetAllLabel", varargs...)
	ret0, _ := ret[0].(*InternalGetAllLabelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InternalGetAllLabel indicates an expected call of InternalGetAllLabel.
func (mr *MockRCMDChannelLabelClientMockRecorder) InternalGetAllLabel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InternalGetAllLabel", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).InternalGetAllLabel), varargs...)
}

// LabelRefresh mocks base method.
func (m *MockRCMDChannelLabelClient) LabelRefresh(ctx context.Context, in *LabelRefreshReq, opts ...grpc.CallOption) (*LabelRefreshResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LabelRefresh", varargs...)
	ret0, _ := ret[0].(*LabelRefreshResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LabelRefresh indicates an expected call of LabelRefresh.
func (mr *MockRCMDChannelLabelClientMockRecorder) LabelRefresh(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LabelRefresh", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).LabelRefresh), varargs...)
}

// LabelSearch mocks base method.
func (m *MockRCMDChannelLabelClient) LabelSearch(ctx context.Context, in *LabelSearchReq, opts ...grpc.CallOption) (*LabelSearchResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LabelSearch", varargs...)
	ret0, _ := ret[0].(*LabelSearchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LabelSearch indicates an expected call of LabelSearch.
func (mr *MockRCMDChannelLabelClientMockRecorder) LabelSearch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LabelSearch", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).LabelSearch), varargs...)
}

// MatchGameCardInfo mocks base method.
func (m *MockRCMDChannelLabelClient) MatchGameCardInfo(ctx context.Context, in *MatchGameCardInfoReq, opts ...grpc.CallOption) (*MatchGameCardInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MatchGameCardInfo", varargs...)
	ret0, _ := ret[0].(*MatchGameCardInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MatchGameCardInfo indicates an expected call of MatchGameCardInfo.
func (mr *MockRCMDChannelLabelClientMockRecorder) MatchGameCardInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MatchGameCardInfo", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).MatchGameCardInfo), varargs...)
}

// RefreshGameLabel mocks base method.
func (m *MockRCMDChannelLabelClient) RefreshGameLabel(ctx context.Context, in *RefreshGameLabelReq, opts ...grpc.CallOption) (*RefreshGameLabelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RefreshGameLabel", varargs...)
	ret0, _ := ret[0].(*RefreshGameLabelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RefreshGameLabel indicates an expected call of RefreshGameLabel.
func (mr *MockRCMDChannelLabelClientMockRecorder) RefreshGameLabel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshGameLabel", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).RefreshGameLabel), varargs...)
}

// SearchGameName mocks base method.
func (m *MockRCMDChannelLabelClient) SearchGameName(ctx context.Context, in *SearchGameNameReq, opts ...grpc.CallOption) (*SearchGameNameResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchGameName", varargs...)
	ret0, _ := ret[0].(*SearchGameNameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchGameName indicates an expected call of SearchGameName.
func (mr *MockRCMDChannelLabelClientMockRecorder) SearchGameName(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchGameName", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).SearchGameName), varargs...)
}

// SearchGlobalLabel mocks base method.
func (m *MockRCMDChannelLabelClient) SearchGlobalLabel(ctx context.Context, in *SearchGlobalLabelReq, opts ...grpc.CallOption) (*SearchGlobalLabelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchGlobalLabel", varargs...)
	ret0, _ := ret[0].(*SearchGlobalLabelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchGlobalLabel indicates an expected call of SearchGlobalLabel.
func (mr *MockRCMDChannelLabelClientMockRecorder) SearchGlobalLabel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchGlobalLabel", reflect.TypeOf((*MockRCMDChannelLabelClient)(nil).SearchGlobalLabel), varargs...)
}

// MockRCMDChannelLabelServer is a mock of RCMDChannelLabelServer interface.
type MockRCMDChannelLabelServer struct {
	ctrl     *gomock.Controller
	recorder *MockRCMDChannelLabelServerMockRecorder
}

// MockRCMDChannelLabelServerMockRecorder is the mock recorder for MockRCMDChannelLabelServer.
type MockRCMDChannelLabelServerMockRecorder struct {
	mock *MockRCMDChannelLabelServer
}

// NewMockRCMDChannelLabelServer creates a new mock instance.
func NewMockRCMDChannelLabelServer(ctrl *gomock.Controller) *MockRCMDChannelLabelServer {
	mock := &MockRCMDChannelLabelServer{ctrl: ctrl}
	mock.recorder = &MockRCMDChannelLabelServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRCMDChannelLabelServer) EXPECT() *MockRCMDChannelLabelServerMockRecorder {
	return m.recorder
}

// BatchHotGameLabels mocks base method.
func (m *MockRCMDChannelLabelServer) BatchHotGameLabels(ctx context.Context, in *BatchHotGameLabelsReq) (*BatchHotGameLabelsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchHotGameLabels", ctx, in)
	ret0, _ := ret[0].(*BatchHotGameLabelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchHotGameLabels indicates an expected call of BatchHotGameLabels.
func (mr *MockRCMDChannelLabelServerMockRecorder) BatchHotGameLabels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchHotGameLabels", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).BatchHotGameLabels), ctx, in)
}

// ConvertGameLabels mocks base method.
func (m *MockRCMDChannelLabelServer) ConvertGameLabels(ctx context.Context, in *ConvertGameLabelsReq) (*ConvertGameLabelsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConvertGameLabels", ctx, in)
	ret0, _ := ret[0].(*ConvertGameLabelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConvertGameLabels indicates an expected call of ConvertGameLabels.
func (mr *MockRCMDChannelLabelServerMockRecorder) ConvertGameLabels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConvertGameLabels", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).ConvertGameLabels), ctx, in)
}

// CutAndMatchLabel mocks base method.
func (m *MockRCMDChannelLabelServer) CutAndMatchLabel(ctx context.Context, in *CutAndMatchLabelReq) (*CutAndMatchLabelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CutAndMatchLabel", ctx, in)
	ret0, _ := ret[0].(*CutAndMatchLabelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CutAndMatchLabel indicates an expected call of CutAndMatchLabel.
func (mr *MockRCMDChannelLabelServerMockRecorder) CutAndMatchLabel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CutAndMatchLabel", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).CutAndMatchLabel), ctx, in)
}

// CutWord mocks base method.
func (m *MockRCMDChannelLabelServer) CutWord(ctx context.Context, in *CutWordReq) (*CutWordResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CutWord", ctx, in)
	ret0, _ := ret[0].(*CutWordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CutWord indicates an expected call of CutWord.
func (mr *MockRCMDChannelLabelServerMockRecorder) CutWord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CutWord", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).CutWord), ctx, in)
}

// GetBusinessLabelRelation mocks base method.
func (m *MockRCMDChannelLabelServer) GetBusinessLabelRelation(ctx context.Context, in *GetBusinessLabelRelationReq) (*GetBusinessLabelRelationResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBusinessLabelRelation", ctx, in)
	ret0, _ := ret[0].(*GetBusinessLabelRelationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBusinessLabelRelation indicates an expected call of GetBusinessLabelRelation.
func (mr *MockRCMDChannelLabelServerMockRecorder) GetBusinessLabelRelation(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBusinessLabelRelation", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).GetBusinessLabelRelation), ctx, in)
}

// GetFilterBlockOptionList mocks base method.
func (m *MockRCMDChannelLabelServer) GetFilterBlockOptionList(ctx context.Context, in *GetFilterBlockOptionListReq) (*GetFilterBlockOptionListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFilterBlockOptionList", ctx, in)
	ret0, _ := ret[0].(*GetFilterBlockOptionListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFilterBlockOptionList indicates an expected call of GetFilterBlockOptionList.
func (mr *MockRCMDChannelLabelServerMockRecorder) GetFilterBlockOptionList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFilterBlockOptionList", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).GetFilterBlockOptionList), ctx, in)
}

// GetGameLabels mocks base method.
func (m *MockRCMDChannelLabelServer) GetGameLabels(ctx context.Context, in *GetGameLabelsReq) (*GetGameLabelsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameLabels", ctx, in)
	ret0, _ := ret[0].(*GetGameLabelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameLabels indicates an expected call of GetGameLabels.
func (mr *MockRCMDChannelLabelServerMockRecorder) GetGameLabels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameLabels", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).GetGameLabels), ctx, in)
}

// GetPublishGameLabels mocks base method.
func (m *MockRCMDChannelLabelServer) GetPublishGameLabels(ctx context.Context, in *GetPublishGameLabelsReq) (*GetPublishGameLabelsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPublishGameLabels", ctx, in)
	ret0, _ := ret[0].(*GetPublishGameLabelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPublishGameLabels indicates an expected call of GetPublishGameLabels.
func (mr *MockRCMDChannelLabelServerMockRecorder) GetPublishGameLabels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPublishGameLabels", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).GetPublishGameLabels), ctx, in)
}

// GetRelatedPublishLabels mocks base method.
func (m *MockRCMDChannelLabelServer) GetRelatedPublishLabels(ctx context.Context, in *GetRelatedPublishLabelsReq) (*GetRelatedPublishLabelsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelatedPublishLabels", ctx, in)
	ret0, _ := ret[0].(*GetRelatedPublishLabelsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelatedPublishLabels indicates an expected call of GetRelatedPublishLabels.
func (mr *MockRCMDChannelLabelServerMockRecorder) GetRelatedPublishLabels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelatedPublishLabels", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).GetRelatedPublishLabels), ctx, in)
}

// GetSearchHint mocks base method.
func (m *MockRCMDChannelLabelServer) GetSearchHint(ctx context.Context, in *GetSearchHintReq) (*GetSearchHintResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSearchHint", ctx, in)
	ret0, _ := ret[0].(*GetSearchHintResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSearchHint indicates an expected call of GetSearchHint.
func (mr *MockRCMDChannelLabelServerMockRecorder) GetSearchHint(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSearchHint", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).GetSearchHint), ctx, in)
}

// InternalGetAllLabel mocks base method.
func (m *MockRCMDChannelLabelServer) InternalGetAllLabel(ctx context.Context, in *InternalGetAllLabelReq) (*InternalGetAllLabelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InternalGetAllLabel", ctx, in)
	ret0, _ := ret[0].(*InternalGetAllLabelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InternalGetAllLabel indicates an expected call of InternalGetAllLabel.
func (mr *MockRCMDChannelLabelServerMockRecorder) InternalGetAllLabel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InternalGetAllLabel", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).InternalGetAllLabel), ctx, in)
}

// LabelRefresh mocks base method.
func (m *MockRCMDChannelLabelServer) LabelRefresh(ctx context.Context, in *LabelRefreshReq) (*LabelRefreshResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LabelRefresh", ctx, in)
	ret0, _ := ret[0].(*LabelRefreshResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LabelRefresh indicates an expected call of LabelRefresh.
func (mr *MockRCMDChannelLabelServerMockRecorder) LabelRefresh(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LabelRefresh", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).LabelRefresh), ctx, in)
}

// LabelSearch mocks base method.
func (m *MockRCMDChannelLabelServer) LabelSearch(ctx context.Context, in *LabelSearchReq) (*LabelSearchResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LabelSearch", ctx, in)
	ret0, _ := ret[0].(*LabelSearchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LabelSearch indicates an expected call of LabelSearch.
func (mr *MockRCMDChannelLabelServerMockRecorder) LabelSearch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LabelSearch", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).LabelSearch), ctx, in)
}

// MatchGameCardInfo mocks base method.
func (m *MockRCMDChannelLabelServer) MatchGameCardInfo(ctx context.Context, in *MatchGameCardInfoReq) (*MatchGameCardInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MatchGameCardInfo", ctx, in)
	ret0, _ := ret[0].(*MatchGameCardInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MatchGameCardInfo indicates an expected call of MatchGameCardInfo.
func (mr *MockRCMDChannelLabelServerMockRecorder) MatchGameCardInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MatchGameCardInfo", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).MatchGameCardInfo), ctx, in)
}

// RefreshGameLabel mocks base method.
func (m *MockRCMDChannelLabelServer) RefreshGameLabel(ctx context.Context, in *RefreshGameLabelReq) (*RefreshGameLabelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefreshGameLabel", ctx, in)
	ret0, _ := ret[0].(*RefreshGameLabelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RefreshGameLabel indicates an expected call of RefreshGameLabel.
func (mr *MockRCMDChannelLabelServerMockRecorder) RefreshGameLabel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshGameLabel", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).RefreshGameLabel), ctx, in)
}

// SearchGameName mocks base method.
func (m *MockRCMDChannelLabelServer) SearchGameName(ctx context.Context, in *SearchGameNameReq) (*SearchGameNameResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchGameName", ctx, in)
	ret0, _ := ret[0].(*SearchGameNameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchGameName indicates an expected call of SearchGameName.
func (mr *MockRCMDChannelLabelServerMockRecorder) SearchGameName(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchGameName", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).SearchGameName), ctx, in)
}

// SearchGlobalLabel mocks base method.
func (m *MockRCMDChannelLabelServer) SearchGlobalLabel(ctx context.Context, in *SearchGlobalLabelReq) (*SearchGlobalLabelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchGlobalLabel", ctx, in)
	ret0, _ := ret[0].(*SearchGlobalLabelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchGlobalLabel indicates an expected call of SearchGlobalLabel.
func (mr *MockRCMDChannelLabelServerMockRecorder) SearchGlobalLabel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchGlobalLabel", reflect.TypeOf((*MockRCMDChannelLabelServer)(nil).SearchGlobalLabel), ctx, in)
}
