// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/topic_channel/recommendation_common.proto

package recommendation_common // import "golang.52tt.com/protocol/services/topic_channel/recommendation_common"

/*
buf:lint:ignore DIRECTORY_SAME_PACKAGE
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TcGameSetting_Op int32

const (
	TcGameSetting_Op_EQ  TcGameSetting_Op = 0
	TcGameSetting_Op_GT  TcGameSetting_Op = 1
	TcGameSetting_Op_GTE TcGameSetting_Op = 2
	TcGameSetting_Op_LT  TcGameSetting_Op = 3
	TcGameSetting_Op_LTE TcGameSetting_Op = 4
)

var TcGameSetting_Op_name = map[int32]string{
	0: "Op_EQ",
	1: "Op_GT",
	2: "Op_GTE",
	3: "Op_LT",
	4: "Op_LTE",
}
var TcGameSetting_Op_value = map[string]int32{
	"Op_EQ":  0,
	"Op_GT":  1,
	"Op_GTE": 2,
	"Op_LT":  3,
	"Op_LTE": 4,
}

func (x TcGameSetting_Op) String() string {
	return proto.EnumName(TcGameSetting_Op_name, int32(x))
}
func (TcGameSetting_Op) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_common_8e64b7c065c945ca, []int{11, 0}
}

type ChannelOpenGameStatusForRule struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	ChannelGameStatus    uint32   `protobuf:"varint,2,opt,name=channel_game_status,json=channelGameStatus,proto3" json:"channel_game_status,omitempty"`
	GamePlayerLimits     []uint32 `protobuf:"varint,3,rep,packed,name=game_player_limits,json=gamePlayerLimits,proto3" json:"game_player_limits,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelOpenGameStatusForRule) Reset()         { *m = ChannelOpenGameStatusForRule{} }
func (m *ChannelOpenGameStatusForRule) String() string { return proto.CompactTextString(m) }
func (*ChannelOpenGameStatusForRule) ProtoMessage()    {}
func (*ChannelOpenGameStatusForRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_common_8e64b7c065c945ca, []int{0}
}
func (m *ChannelOpenGameStatusForRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelOpenGameStatusForRule.Unmarshal(m, b)
}
func (m *ChannelOpenGameStatusForRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelOpenGameStatusForRule.Marshal(b, m, deterministic)
}
func (dst *ChannelOpenGameStatusForRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelOpenGameStatusForRule.Merge(dst, src)
}
func (m *ChannelOpenGameStatusForRule) XXX_Size() int {
	return xxx_messageInfo_ChannelOpenGameStatusForRule.Size(m)
}
func (m *ChannelOpenGameStatusForRule) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelOpenGameStatusForRule.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelOpenGameStatusForRule proto.InternalMessageInfo

func (m *ChannelOpenGameStatusForRule) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ChannelOpenGameStatusForRule) GetChannelGameStatus() uint32 {
	if m != nil {
		return m.ChannelGameStatus
	}
	return 0
}

func (m *ChannelOpenGameStatusForRule) GetGamePlayerLimits() []uint32 {
	if m != nil {
		return m.GamePlayerLimits
	}
	return nil
}

type MiniGameRoomInfoForRule struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	GameSettings         []string `protobuf:"bytes,2,rep,name=game_settings,json=gameSettings,proto3" json:"game_settings,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MiniGameRoomInfoForRule) Reset()         { *m = MiniGameRoomInfoForRule{} }
func (m *MiniGameRoomInfoForRule) String() string { return proto.CompactTextString(m) }
func (*MiniGameRoomInfoForRule) ProtoMessage()    {}
func (*MiniGameRoomInfoForRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_common_8e64b7c065c945ca, []int{1}
}
func (m *MiniGameRoomInfoForRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MiniGameRoomInfoForRule.Unmarshal(m, b)
}
func (m *MiniGameRoomInfoForRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MiniGameRoomInfoForRule.Marshal(b, m, deterministic)
}
func (dst *MiniGameRoomInfoForRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MiniGameRoomInfoForRule.Merge(dst, src)
}
func (m *MiniGameRoomInfoForRule) XXX_Size() int {
	return xxx_messageInfo_MiniGameRoomInfoForRule.Size(m)
}
func (m *MiniGameRoomInfoForRule) XXX_DiscardUnknown() {
	xxx_messageInfo_MiniGameRoomInfoForRule.DiscardUnknown(m)
}

var xxx_messageInfo_MiniGameRoomInfoForRule proto.InternalMessageInfo

func (m *MiniGameRoomInfoForRule) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *MiniGameRoomInfoForRule) GetGameSettings() []string {
	if m != nil {
		return m.GameSettings
	}
	return nil
}

type UserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Sex                  uint32   `protobuf:"varint,2,opt,name=sex,proto3" json:"sex,omitempty"`
	RegisteredTimestamp  uint32   `protobuf:"varint,3,opt,name=registered_timestamp,json=registeredTimestamp,proto3" json:"registered_timestamp,omitempty"`
	UpdateTimestamp      uint32   `protobuf:"varint,4,opt,name=update_timestamp,json=updateTimestamp,proto3" json:"update_timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_common_8e64b7c065c945ca, []int{2}
}
func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (dst *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(dst, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserInfo) GetRegisteredTimestamp() uint32 {
	if m != nil {
		return m.RegisteredTimestamp
	}
	return 0
}

func (m *UserInfo) GetUpdateTimestamp() uint32 {
	if m != nil {
		return m.UpdateTimestamp
	}
	return 0
}

type UserTag struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TagList              []*TagInfo     `protobuf:"bytes,2,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	GameTagList          []*GameTagInfo `protobuf:"bytes,3,rep,name=game_tag_list,json=gameTagList,proto3" json:"game_tag_list,omitempty"`
	UpdateTimestamp      uint32         `protobuf:"varint,4,opt,name=update_timestamp,json=updateTimestamp,proto3" json:"update_timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserTag) Reset()         { *m = UserTag{} }
func (m *UserTag) String() string { return proto.CompactTextString(m) }
func (*UserTag) ProtoMessage()    {}
func (*UserTag) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_common_8e64b7c065c945ca, []int{3}
}
func (m *UserTag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserTag.Unmarshal(m, b)
}
func (m *UserTag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserTag.Marshal(b, m, deterministic)
}
func (dst *UserTag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserTag.Merge(dst, src)
}
func (m *UserTag) XXX_Size() int {
	return xxx_messageInfo_UserTag.Size(m)
}
func (m *UserTag) XXX_DiscardUnknown() {
	xxx_messageInfo_UserTag.DiscardUnknown(m)
}

var xxx_messageInfo_UserTag proto.InternalMessageInfo

func (m *UserTag) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserTag) GetTagList() []*TagInfo {
	if m != nil {
		return m.TagList
	}
	return nil
}

func (m *UserTag) GetGameTagList() []*GameTagInfo {
	if m != nil {
		return m.GameTagList
	}
	return nil
}

func (m *UserTag) GetUpdateTimestamp() uint32 {
	if m != nil {
		return m.UpdateTimestamp
	}
	return 0
}

type TagInfo struct {
	TagId                uint32   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagName              string   `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	TagType              uint32   `protobuf:"varint,3,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagInfo) Reset()         { *m = TagInfo{} }
func (m *TagInfo) String() string { return proto.CompactTextString(m) }
func (*TagInfo) ProtoMessage()    {}
func (*TagInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_common_8e64b7c065c945ca, []int{4}
}
func (m *TagInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagInfo.Unmarshal(m, b)
}
func (m *TagInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagInfo.Marshal(b, m, deterministic)
}
func (dst *TagInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagInfo.Merge(dst, src)
}
func (m *TagInfo) XXX_Size() int {
	return xxx_messageInfo_TagInfo.Size(m)
}
func (m *TagInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TagInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TagInfo proto.InternalMessageInfo

func (m *TagInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *TagInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *TagInfo) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

type GameTagInfo struct {
	TagId                uint32        `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagName              string        `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	TagType              uint32        `protobuf:"varint,3,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	GameExtList          []*GameTagExt `protobuf:"bytes,4,rep,name=game_ext_list,json=gameExtList,proto3" json:"game_ext_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GameTagInfo) Reset()         { *m = GameTagInfo{} }
func (m *GameTagInfo) String() string { return proto.CompactTextString(m) }
func (*GameTagInfo) ProtoMessage()    {}
func (*GameTagInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_common_8e64b7c065c945ca, []int{5}
}
func (m *GameTagInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTagInfo.Unmarshal(m, b)
}
func (m *GameTagInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTagInfo.Marshal(b, m, deterministic)
}
func (dst *GameTagInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTagInfo.Merge(dst, src)
}
func (m *GameTagInfo) XXX_Size() int {
	return xxx_messageInfo_GameTagInfo.Size(m)
}
func (m *GameTagInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTagInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameTagInfo proto.InternalMessageInfo

func (m *GameTagInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *GameTagInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *GameTagInfo) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

func (m *GameTagInfo) GetGameExtList() []*GameTagExt {
	if m != nil {
		return m.GameExtList
	}
	return nil
}

type GameCardInputVal struct {
	ElemTitle            string   `protobuf:"bytes,1,opt,name=elem_title,json=elemTitle,proto3" json:"elem_title,omitempty"`
	ElemVal              string   `protobuf:"bytes,2,opt,name=elem_val,json=elemVal,proto3" json:"elem_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameCardInputVal) Reset()         { *m = GameCardInputVal{} }
func (m *GameCardInputVal) String() string { return proto.CompactTextString(m) }
func (*GameCardInputVal) ProtoMessage()    {}
func (*GameCardInputVal) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_common_8e64b7c065c945ca, []int{6}
}
func (m *GameCardInputVal) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameCardInputVal.Unmarshal(m, b)
}
func (m *GameCardInputVal) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameCardInputVal.Marshal(b, m, deterministic)
}
func (dst *GameCardInputVal) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameCardInputVal.Merge(dst, src)
}
func (m *GameCardInputVal) XXX_Size() int {
	return xxx_messageInfo_GameCardInputVal.Size(m)
}
func (m *GameCardInputVal) XXX_DiscardUnknown() {
	xxx_messageInfo_GameCardInputVal.DiscardUnknown(m)
}

var xxx_messageInfo_GameCardInputVal proto.InternalMessageInfo

func (m *GameCardInputVal) GetElemTitle() string {
	if m != nil {
		return m.ElemTitle
	}
	return ""
}

func (m *GameCardInputVal) GetElemVal() string {
	if m != nil {
		return m.ElemVal
	}
	return ""
}

type GameTagExt struct {
	OptName              string              `protobuf:"bytes,1,opt,name=opt_name,json=optName,proto3" json:"opt_name,omitempty"`
	ValueList            []string            `protobuf:"bytes,2,rep,name=value_list,json=valueList,proto3" json:"value_list,omitempty"`
	InputVal             []*GameCardInputVal `protobuf:"bytes,4,rep,name=input_val,json=inputVal,proto3" json:"input_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GameTagExt) Reset()         { *m = GameTagExt{} }
func (m *GameTagExt) String() string { return proto.CompactTextString(m) }
func (*GameTagExt) ProtoMessage()    {}
func (*GameTagExt) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_common_8e64b7c065c945ca, []int{7}
}
func (m *GameTagExt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTagExt.Unmarshal(m, b)
}
func (m *GameTagExt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTagExt.Marshal(b, m, deterministic)
}
func (dst *GameTagExt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTagExt.Merge(dst, src)
}
func (m *GameTagExt) XXX_Size() int {
	return xxx_messageInfo_GameTagExt.Size(m)
}
func (m *GameTagExt) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTagExt.DiscardUnknown(m)
}

var xxx_messageInfo_GameTagExt proto.InternalMessageInfo

func (m *GameTagExt) GetOptName() string {
	if m != nil {
		return m.OptName
	}
	return ""
}

func (m *GameTagExt) GetValueList() []string {
	if m != nil {
		return m.ValueList
	}
	return nil
}

func (m *GameTagExt) GetInputVal() []*GameCardInputVal {
	if m != nil {
		return m.InputVal
	}
	return nil
}

type OssColumn struct {
	Idx                  uint32   `protobuf:"varint,1,opt,name=idx,proto3" json:"idx,omitempty"`
	Val                  string   `protobuf:"bytes,2,opt,name=val,proto3" json:"val,omitempty"`
	Key                  string   `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OssColumn) Reset()         { *m = OssColumn{} }
func (m *OssColumn) String() string { return proto.CompactTextString(m) }
func (*OssColumn) ProtoMessage()    {}
func (*OssColumn) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_common_8e64b7c065c945ca, []int{8}
}
func (m *OssColumn) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OssColumn.Unmarshal(m, b)
}
func (m *OssColumn) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OssColumn.Marshal(b, m, deterministic)
}
func (dst *OssColumn) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OssColumn.Merge(dst, src)
}
func (m *OssColumn) XXX_Size() int {
	return xxx_messageInfo_OssColumn.Size(m)
}
func (m *OssColumn) XXX_DiscardUnknown() {
	xxx_messageInfo_OssColumn.DiscardUnknown(m)
}

var xxx_messageInfo_OssColumn proto.InternalMessageInfo

func (m *OssColumn) GetIdx() uint32 {
	if m != nil {
		return m.Idx
	}
	return 0
}

func (m *OssColumn) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

func (m *OssColumn) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type OssStatsRow struct {
	Values               []*OssColumn `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OssStatsRow) Reset()         { *m = OssStatsRow{} }
func (m *OssStatsRow) String() string { return proto.CompactTextString(m) }
func (*OssStatsRow) ProtoMessage()    {}
func (*OssStatsRow) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_common_8e64b7c065c945ca, []int{9}
}
func (m *OssStatsRow) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OssStatsRow.Unmarshal(m, b)
}
func (m *OssStatsRow) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OssStatsRow.Marshal(b, m, deterministic)
}
func (dst *OssStatsRow) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OssStatsRow.Merge(dst, src)
}
func (m *OssStatsRow) XXX_Size() int {
	return xxx_messageInfo_OssStatsRow.Size(m)
}
func (m *OssStatsRow) XXX_DiscardUnknown() {
	xxx_messageInfo_OssStatsRow.DiscardUnknown(m)
}

var xxx_messageInfo_OssStatsRow proto.InternalMessageInfo

func (m *OssStatsRow) GetValues() []*OssColumn {
	if m != nil {
		return m.Values
	}
	return nil
}

type OssStatsBatchEvent struct {
	SrcIp                string         `protobuf:"bytes,1,opt,name=src_ip,json=srcIp,proto3" json:"src_ip,omitempty"`
	ServerTimestamp      uint32         `protobuf:"varint,2,opt,name=server_timestamp,json=serverTimestamp,proto3" json:"server_timestamp,omitempty"`
	BizType              string         `protobuf:"bytes,3,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	RowList              []*OssStatsRow `protobuf:"bytes,4,rep,name=row_list,json=rowList,proto3" json:"row_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *OssStatsBatchEvent) Reset()         { *m = OssStatsBatchEvent{} }
func (m *OssStatsBatchEvent) String() string { return proto.CompactTextString(m) }
func (*OssStatsBatchEvent) ProtoMessage()    {}
func (*OssStatsBatchEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_common_8e64b7c065c945ca, []int{10}
}
func (m *OssStatsBatchEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OssStatsBatchEvent.Unmarshal(m, b)
}
func (m *OssStatsBatchEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OssStatsBatchEvent.Marshal(b, m, deterministic)
}
func (dst *OssStatsBatchEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OssStatsBatchEvent.Merge(dst, src)
}
func (m *OssStatsBatchEvent) XXX_Size() int {
	return xxx_messageInfo_OssStatsBatchEvent.Size(m)
}
func (m *OssStatsBatchEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_OssStatsBatchEvent.DiscardUnknown(m)
}

var xxx_messageInfo_OssStatsBatchEvent proto.InternalMessageInfo

func (m *OssStatsBatchEvent) GetSrcIp() string {
	if m != nil {
		return m.SrcIp
	}
	return ""
}

func (m *OssStatsBatchEvent) GetServerTimestamp() uint32 {
	if m != nil {
		return m.ServerTimestamp
	}
	return 0
}

func (m *OssStatsBatchEvent) GetBizType() string {
	if m != nil {
		return m.BizType
	}
	return ""
}

func (m *OssStatsBatchEvent) GetRowList() []*OssStatsRow {
	if m != nil {
		return m.RowList
	}
	return nil
}

type TcGameSetting struct {
	Key                  string               `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	ValList              []*TcGameSetting_Val `protobuf:"bytes,2,rep,name=val_list,json=valList,proto3" json:"val_list,omitempty"`
	GroupId              uint32               `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *TcGameSetting) Reset()         { *m = TcGameSetting{} }
func (m *TcGameSetting) String() string { return proto.CompactTextString(m) }
func (*TcGameSetting) ProtoMessage()    {}
func (*TcGameSetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_common_8e64b7c065c945ca, []int{11}
}
func (m *TcGameSetting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TcGameSetting.Unmarshal(m, b)
}
func (m *TcGameSetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TcGameSetting.Marshal(b, m, deterministic)
}
func (dst *TcGameSetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TcGameSetting.Merge(dst, src)
}
func (m *TcGameSetting) XXX_Size() int {
	return xxx_messageInfo_TcGameSetting.Size(m)
}
func (m *TcGameSetting) XXX_DiscardUnknown() {
	xxx_messageInfo_TcGameSetting.DiscardUnknown(m)
}

var xxx_messageInfo_TcGameSetting proto.InternalMessageInfo

func (m *TcGameSetting) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *TcGameSetting) GetValList() []*TcGameSetting_Val {
	if m != nil {
		return m.ValList
	}
	return nil
}

func (m *TcGameSetting) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type TcGameSetting_Val struct {
	Val                  string           `protobuf:"bytes,1,opt,name=val,proto3" json:"val,omitempty"`
	Op                   TcGameSetting_Op `protobuf:"varint,2,opt,name=op,proto3,enum=topic_channel.recommendation_common.TcGameSetting_Op" json:"op,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *TcGameSetting_Val) Reset()         { *m = TcGameSetting_Val{} }
func (m *TcGameSetting_Val) String() string { return proto.CompactTextString(m) }
func (*TcGameSetting_Val) ProtoMessage()    {}
func (*TcGameSetting_Val) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_common_8e64b7c065c945ca, []int{11, 0}
}
func (m *TcGameSetting_Val) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TcGameSetting_Val.Unmarshal(m, b)
}
func (m *TcGameSetting_Val) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TcGameSetting_Val.Marshal(b, m, deterministic)
}
func (dst *TcGameSetting_Val) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TcGameSetting_Val.Merge(dst, src)
}
func (m *TcGameSetting_Val) XXX_Size() int {
	return xxx_messageInfo_TcGameSetting_Val.Size(m)
}
func (m *TcGameSetting_Val) XXX_DiscardUnknown() {
	xxx_messageInfo_TcGameSetting_Val.DiscardUnknown(m)
}

var xxx_messageInfo_TcGameSetting_Val proto.InternalMessageInfo

func (m *TcGameSetting_Val) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

func (m *TcGameSetting_Val) GetOp() TcGameSetting_Op {
	if m != nil {
		return m.Op
	}
	return TcGameSetting_Op_EQ
}

func init() {
	proto.RegisterType((*ChannelOpenGameStatusForRule)(nil), "topic_channel.recommendation_common.ChannelOpenGameStatusForRule")
	proto.RegisterType((*MiniGameRoomInfoForRule)(nil), "topic_channel.recommendation_common.MiniGameRoomInfoForRule")
	proto.RegisterType((*UserInfo)(nil), "topic_channel.recommendation_common.UserInfo")
	proto.RegisterType((*UserTag)(nil), "topic_channel.recommendation_common.UserTag")
	proto.RegisterType((*TagInfo)(nil), "topic_channel.recommendation_common.TagInfo")
	proto.RegisterType((*GameTagInfo)(nil), "topic_channel.recommendation_common.GameTagInfo")
	proto.RegisterType((*GameCardInputVal)(nil), "topic_channel.recommendation_common.GameCardInputVal")
	proto.RegisterType((*GameTagExt)(nil), "topic_channel.recommendation_common.GameTagExt")
	proto.RegisterType((*OssColumn)(nil), "topic_channel.recommendation_common.OssColumn")
	proto.RegisterType((*OssStatsRow)(nil), "topic_channel.recommendation_common.OssStatsRow")
	proto.RegisterType((*OssStatsBatchEvent)(nil), "topic_channel.recommendation_common.OssStatsBatchEvent")
	proto.RegisterType((*TcGameSetting)(nil), "topic_channel.recommendation_common.TcGameSetting")
	proto.RegisterType((*TcGameSetting_Val)(nil), "topic_channel.recommendation_common.TcGameSetting.Val")
	proto.RegisterEnum("topic_channel.recommendation_common.TcGameSetting_Op", TcGameSetting_Op_name, TcGameSetting_Op_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/topic_channel/recommendation_common.proto", fileDescriptor_recommendation_common_8e64b7c065c945ca)
}

var fileDescriptor_recommendation_common_8e64b7c065c945ca = []byte{
	// 836 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x55, 0xd1, 0x8e, 0xdb, 0x44,
	0x14, 0xc5, 0xc9, 0xee, 0x26, 0xbe, 0xcb, 0x52, 0x33, 0x05, 0x91, 0x45, 0x54, 0xaa, 0xdc, 0x97,
	0x45, 0xaa, 0x9c, 0x52, 0x54, 0xc4, 0x0b, 0x0f, 0x74, 0x95, 0x46, 0x11, 0x81, 0xb4, 0x5e, 0x77,
	0x1f, 0x78, 0xc0, 0x9a, 0xb5, 0x07, 0x77, 0xd4, 0xb1, 0x67, 0x98, 0x19, 0x67, 0xb3, 0xfd, 0x06,
	0xc4, 0x07, 0xf0, 0x11, 0xf0, 0x07, 0xfc, 0x0d, 0xff, 0x81, 0xee, 0xd8, 0x89, 0x53, 0xa9, 0x48,
	0x5e, 0xa9, 0x6f, 0x77, 0xce, 0xbd, 0x3e, 0x39, 0xf7, 0xdc, 0x3b, 0x13, 0xf8, 0xd6, 0xda, 0xe9,
	0x6f, 0x35, 0xcf, 0x5e, 0x1b, 0x2e, 0xd6, 0x4c, 0x4f, 0xad, 0x54, 0x3c, 0x4b, 0xb3, 0x57, 0xb4,
	0xaa, 0x98, 0x98, 0x6a, 0x96, 0xc9, 0xb2, 0x64, 0x55, 0x4e, 0x2d, 0x97, 0x55, 0x8a, 0x07, 0x59,
	0x45, 0x4a, 0x4b, 0x2b, 0xc9, 0x83, 0xb7, 0x4a, 0xa3, 0x77, 0x96, 0x86, 0x7f, 0x78, 0xf0, 0xc5,
	0x79, 0x53, 0xb1, 0x52, 0xac, 0x9a, 0xd3, 0x92, 0x5d, 0x58, 0x6a, 0x6b, 0xf3, 0x4c, 0xea, 0xb8,
	0x16, 0x8c, 0x04, 0x30, 0xcc, 0x78, 0x3e, 0xf1, 0xee, 0x7b, 0x67, 0x27, 0x31, 0x86, 0x24, 0x82,
	0xbb, 0x2d, 0x67, 0x5a, 0xd0, 0x92, 0xa5, 0xc6, 0xd5, 0x4f, 0x06, 0xae, 0xe2, 0xe3, 0x36, 0xd5,
	0x11, 0x91, 0x87, 0x40, 0x5c, 0x9d, 0x12, 0xf4, 0x86, 0xe9, 0x54, 0xf0, 0x92, 0x5b, 0x33, 0x19,
	0xde, 0x1f, 0x9e, 0x9d, 0xc4, 0x01, 0x66, 0x9e, 0xbb, 0xc4, 0xd2, 0xe1, 0xe1, 0x73, 0xf8, 0xec,
	0x47, 0x5e, 0x71, 0xfc, 0x3e, 0x96, 0xb2, 0x5c, 0x54, 0xbf, 0xca, 0xff, 0x97, 0xf2, 0x00, 0x4e,
	0x1a, 0x09, 0xcc, 0x5a, 0x5e, 0x15, 0x28, 0x62, 0x78, 0xe6, 0xc7, 0x1f, 0x22, 0x78, 0xd1, 0x62,
	0xe1, 0xef, 0x1e, 0x8c, 0x5f, 0x1a, 0xa6, 0x91, 0x0a, 0x39, 0xea, 0x8e, 0xa3, 0xe6, 0x39, 0x22,
	0x86, 0x6d, 0x5a, 0xf9, 0x18, 0x92, 0xaf, 0xe0, 0x13, 0xcd, 0x0a, 0x6e, 0x2c, 0xd3, 0x2c, 0x4f,
	0x2d, 0x2f, 0x99, 0xb1, 0xb4, 0x54, 0x93, 0xa1, 0x2b, 0xb9, 0xdb, 0xe5, 0x92, 0x6d, 0x8a, 0x7c,
	0x09, 0x41, 0xad, 0x72, 0x6a, 0xd9, 0x5e, 0xf9, 0x81, 0x2b, 0xbf, 0xd3, 0xe0, 0xbb, 0xd2, 0xf0,
	0x5f, 0x0f, 0x46, 0x28, 0x27, 0xa1, 0xc5, 0x3b, 0xd4, 0xcc, 0x61, 0x6c, 0x69, 0x91, 0x0a, 0x6e,
	0xac, 0x6b, 0xe6, 0xf8, 0xf1, 0xc3, 0xa8, 0xc7, 0x1c, 0xa3, 0x84, 0x16, 0xd8, 0x5f, 0x3c, 0xb2,
	0xb4, 0x58, 0x72, 0x63, 0x49, 0xd2, 0x5a, 0xb3, 0x63, 0x1b, 0x3a, 0xb6, 0x47, 0xbd, 0xd8, 0xd0,
	0xfd, 0x2d, 0xe3, 0x71, 0xd1, 0x1c, 0x1c, 0xeb, 0x2d, 0xfa, 0xbc, 0x84, 0x51, 0x4b, 0x41, 0x3e,
	0x85, 0x23, 0x94, 0xb1, 0xeb, 0xf4, 0xd0, 0xd2, 0x62, 0x91, 0x93, 0xd3, 0xa6, 0xd7, 0x8a, 0x96,
	0xcc, 0xd9, 0xef, 0x3b, 0xf5, 0x3f, 0xd1, 0x92, 0x6d, 0x53, 0xf6, 0x46, 0xb1, 0xd6, 0x76, 0x4c,
	0x25, 0x37, 0x8a, 0x85, 0x7f, 0x7b, 0x70, 0xbc, 0xa7, 0xef, 0xbd, 0x92, 0x93, 0x8b, 0xd6, 0x35,
	0xb6, 0xb1, 0x8d, 0x6b, 0x07, 0xce, 0xb5, 0xe9, 0x6d, 0x5c, 0x9b, 0x6d, 0x6c, 0x63, 0xda, 0x6c,
	0x63, 0xd1, 0xb4, 0x70, 0x09, 0x01, 0xa6, 0xce, 0xa9, 0xce, 0x17, 0x95, 0xaa, 0xed, 0x25, 0x15,
	0xe4, 0x1e, 0x00, 0x13, 0xac, 0x4c, 0x2d, 0xb7, 0x82, 0x39, 0xe5, 0x7e, 0xec, 0x23, 0x92, 0x20,
	0x80, 0x12, 0x5d, 0x7a, 0x4d, 0xc5, 0x56, 0x3d, 0x9e, 0x2f, 0xa9, 0x08, 0xff, 0xf4, 0x00, 0xba,
	0x5f, 0xc2, 0x4a, 0xa9, 0x6c, 0xd3, 0x67, 0x43, 0x33, 0x92, 0xca, 0xba, 0x3e, 0xef, 0x01, 0xac,
	0xa9, 0xa8, 0x59, 0xb7, 0x4d, 0x7e, 0xec, 0x3b, 0xc4, 0xcd, 0x32, 0x06, 0x9f, 0xa3, 0x1c, 0xf7,
	0x23, 0x4d, 0x9f, 0x4f, 0x7a, 0xf7, 0xb9, 0xdf, 0x4c, 0x3c, 0xe6, 0x6d, 0x14, 0x7e, 0x0f, 0xfe,
	0xca, 0x98, 0x73, 0x29, 0xea, 0xb2, 0xc2, 0xed, 0xe6, 0xf9, 0x66, 0xbb, 0xdd, 0x3c, 0xdf, 0x20,
	0xd2, 0x75, 0x84, 0x21, 0x22, 0xaf, 0xd9, 0x8d, 0x1b, 0x83, 0x1f, 0x63, 0x18, 0xbe, 0x84, 0xe3,
	0x95, 0x31, 0xf8, 0x76, 0x98, 0x58, 0x5e, 0x93, 0x67, 0x70, 0xe4, 0x24, 0x9b, 0x89, 0xe7, 0x24,
	0x46, 0xbd, 0x24, 0xee, 0x44, 0xc4, 0xed, 0xd7, 0xe1, 0x3f, 0x1e, 0x90, 0x2d, 0xef, 0x53, 0x6a,
	0xb3, 0x57, 0xb3, 0x35, 0xab, 0x2c, 0x6e, 0x8f, 0xd1, 0x59, 0xca, 0x55, 0x6b, 0xde, 0xa1, 0xd1,
	0xd9, 0xc2, 0xdd, 0x67, 0xc3, 0xf4, 0x9a, 0xe9, 0xbd, 0x3d, 0x6f, 0x5e, 0x88, 0x3b, 0x0d, 0xde,
	0x5d, 0xfd, 0x53, 0x18, 0x5f, 0xf1, 0x37, 0xdd, 0x36, 0xf9, 0xf1, 0xe8, 0x8a, 0xbf, 0x71, 0xdb,
	0xf4, 0x03, 0x8c, 0xb5, 0xbc, 0xde, 0x5f, 0xa4, 0x47, 0x7d, 0xd5, 0x6f, 0xfb, 0x8f, 0x47, 0x5a,
	0x5e, 0xbb, 0x2d, 0xfa, 0x6b, 0x00, 0x27, 0x49, 0x36, 0xef, 0x5e, 0xb6, 0xad, 0x77, 0xde, 0xce,
	0x3b, 0xf2, 0x02, 0xc6, 0x6b, 0x2a, 0xf6, 0x5f, 0x8f, 0x6f, 0xfa, 0xbd, 0x1e, 0xfb, 0xbc, 0x11,
	0x8e, 0x74, 0xb4, 0xa6, 0xc2, 0x6d, 0xc9, 0x29, 0x8c, 0x0b, 0x2d, 0x6b, 0x85, 0x17, 0xac, 0xbd,
	0x2c, 0xee, 0xbc, 0xc8, 0x3f, 0xff, 0x05, 0x86, 0x97, 0xcd, 0x08, 0x71, 0xa8, 0x5e, 0x37, 0xd4,
	0x19, 0x0c, 0x64, 0xe3, 0xd7, 0x47, 0x3d, 0x57, 0xea, 0x6d, 0x01, 0x2b, 0x15, 0x0f, 0xa4, 0x0a,
	0xbf, 0x83, 0xc1, 0x4a, 0x11, 0x1f, 0x0e, 0x57, 0x2a, 0x9d, 0xbd, 0x08, 0x3e, 0x68, 0xc3, 0x79,
	0x12, 0x78, 0x04, 0xe0, 0xc8, 0x85, 0xb3, 0x60, 0xd0, 0xc2, 0xcb, 0x24, 0x18, 0xb6, 0xf0, 0x32,
	0x99, 0x05, 0x07, 0x4f, 0xe7, 0x3f, 0xcf, 0x0a, 0x29, 0x68, 0x55, 0x44, 0x4f, 0x1e, 0x5b, 0x1b,
	0x65, 0xb2, 0x9c, 0xba, 0x3f, 0xc6, 0x4c, 0x8a, 0x29, 0x0e, 0x91, 0x67, 0xcc, 0xf4, 0xf9, 0x3b,
	0xbd, 0x3a, 0x72, 0x9f, 0x7d, 0xfd, 0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x04, 0x3c, 0x1b, 0xc1,
	0x8b, 0x07, 0x00, 0x00,
}
