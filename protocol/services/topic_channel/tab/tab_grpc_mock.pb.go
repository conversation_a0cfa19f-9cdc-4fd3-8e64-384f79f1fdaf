// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/topic_channel/tab.proto

package tab

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockTopicChannelTabClient is a mock of TopicChannelTabClient interface.
type MockTopicChannelTabClient struct {
	ctrl     *gomock.Controller
	recorder *MockTopicChannelTabClientMockRecorder
}

// MockTopicChannelTabClientMockRecorder is the mock recorder for MockTopicChannelTabClient.
type MockTopicChannelTabClientMockRecorder struct {
	mock *MockTopicChannelTabClient
}

// NewMockTopicChannelTabClient creates a new mock instance.
func NewMockTopicChannelTabClient(ctrl *gomock.Controller) *MockTopicChannelTabClient {
	mock := &MockTopicChannelTabClient{ctrl: ctrl}
	mock.recorder = &MockTopicChannelTabClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTopicChannelTabClient) EXPECT() *MockTopicChannelTabClientMockRecorder {
	return m.recorder
}

// AddCategoryTitle mocks base method.
func (m *MockTopicChannelTabClient) AddCategoryTitle(ctx context.Context, in *AddCategoryTitleReq, opts ...grpc.CallOption) (*AddCategoryTitleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddCategoryTitle", varargs...)
	ret0, _ := ret[0].(*AddCategoryTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddCategoryTitle indicates an expected call of AddCategoryTitle.
func (mr *MockTopicChannelTabClientMockRecorder) AddCategoryTitle(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCategoryTitle", reflect.TypeOf((*MockTopicChannelTabClient)(nil).AddCategoryTitle), varargs...)
}

// AddMultilevelTitle mocks base method.
func (m *MockTopicChannelTabClient) AddMultilevelTitle(ctx context.Context, in *AddMultilevelTitleReq, opts ...grpc.CallOption) (*AddMultilevelTitleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddMultilevelTitle", varargs...)
	ret0, _ := ret[0].(*AddMultilevelTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMultilevelTitle indicates an expected call of AddMultilevelTitle.
func (mr *MockTopicChannelTabClientMockRecorder) AddMultilevelTitle(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMultilevelTitle", reflect.TypeOf((*MockTopicChannelTabClient)(nil).AddMultilevelTitle), varargs...)
}

// BatchGetBlockRelations mocks base method.
func (m *MockTopicChannelTabClient) BatchGetBlockRelations(ctx context.Context, in *BatchGetBlockRelationsReq, opts ...grpc.CallOption) (*BatchGetBlockRelationsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetBlockRelations", varargs...)
	ret0, _ := ret[0].(*BatchGetBlockRelationsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBlockRelations indicates an expected call of BatchGetBlockRelations.
func (mr *MockTopicChannelTabClientMockRecorder) BatchGetBlockRelations(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBlockRelations", reflect.TypeOf((*MockTopicChannelTabClient)(nil).BatchGetBlockRelations), varargs...)
}

// BatchGetBlocks mocks base method.
func (m *MockTopicChannelTabClient) BatchGetBlocks(ctx context.Context, in *BatchGetBlocksReq, opts ...grpc.CallOption) (*BatchGetBlocksResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetBlocks", varargs...)
	ret0, _ := ret[0].(*BatchGetBlocksResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBlocks indicates an expected call of BatchGetBlocks.
func (mr *MockTopicChannelTabClientMockRecorder) BatchGetBlocks(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBlocks", reflect.TypeOf((*MockTopicChannelTabClient)(nil).BatchGetBlocks), varargs...)
}

// BatchGetBusinessBlockInfo mocks base method.
func (m *MockTopicChannelTabClient) BatchGetBusinessBlockInfo(ctx context.Context, in *BatchGetBusinessBlockInfoReq, opts ...grpc.CallOption) (*BatchGetBusinessBlockInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetBusinessBlockInfo", varargs...)
	ret0, _ := ret[0].(*BatchGetBusinessBlockInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBusinessBlockInfo indicates an expected call of BatchGetBusinessBlockInfo.
func (mr *MockTopicChannelTabClientMockRecorder) BatchGetBusinessBlockInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBusinessBlockInfo", reflect.TypeOf((*MockTopicChannelTabClient)(nil).BatchGetBusinessBlockInfo), varargs...)
}

// BatchGetGameLabelItems mocks base method.
func (m *MockTopicChannelTabClient) BatchGetGameLabelItems(ctx context.Context, in *BatchGetGameLabelItemsReq, opts ...grpc.CallOption) (*BatchGetGameLabelItemsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetGameLabelItems", varargs...)
	ret0, _ := ret[0].(*BatchGetGameLabelItemsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGameLabelItems indicates an expected call of BatchGetGameLabelItems.
func (mr *MockTopicChannelTabClientMockRecorder) BatchGetGameLabelItems(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGameLabelItems", reflect.TypeOf((*MockTopicChannelTabClient)(nil).BatchGetGameLabelItems), varargs...)
}

// BatchGetNewQuickMatchConfig mocks base method.
func (m *MockTopicChannelTabClient) BatchGetNewQuickMatchConfig(ctx context.Context, in *BatchGetNewQuickMatchConfigReq, opts ...grpc.CallOption) (*BatchGetNewQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetNewQuickMatchConfig", varargs...)
	ret0, _ := ret[0].(*BatchGetNewQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetNewQuickMatchConfig indicates an expected call of BatchGetNewQuickMatchConfig.
func (mr *MockTopicChannelTabClientMockRecorder) BatchGetNewQuickMatchConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetNewQuickMatchConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).BatchGetNewQuickMatchConfig), varargs...)
}

// BatchGetOfficialRoomNameConfig mocks base method.
func (m *MockTopicChannelTabClient) BatchGetOfficialRoomNameConfig(ctx context.Context, in *BatchGetOfficialRoomNameConfigReq, opts ...grpc.CallOption) (*BatchGetOfficialRoomNameConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetOfficialRoomNameConfig", varargs...)
	ret0, _ := ret[0].(*BatchGetOfficialRoomNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetOfficialRoomNameConfig indicates an expected call of BatchGetOfficialRoomNameConfig.
func (mr *MockTopicChannelTabClientMockRecorder) BatchGetOfficialRoomNameConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetOfficialRoomNameConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).BatchGetOfficialRoomNameConfig), varargs...)
}

// BatchGetShieldSwitchByTabIds mocks base method.
func (m *MockTopicChannelTabClient) BatchGetShieldSwitchByTabIds(ctx context.Context, in *BatchGetShieldSwitchByTabIdsReq, opts ...grpc.CallOption) (*BatchGetShieldSwitchByTabIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetShieldSwitchByTabIds", varargs...)
	ret0, _ := ret[0].(*BatchGetShieldSwitchByTabIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetShieldSwitchByTabIds indicates an expected call of BatchGetShieldSwitchByTabIds.
func (mr *MockTopicChannelTabClientMockRecorder) BatchGetShieldSwitchByTabIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetShieldSwitchByTabIds", reflect.TypeOf((*MockTopicChannelTabClient)(nil).BatchGetShieldSwitchByTabIds), varargs...)
}

// Blocks mocks base method.
func (m *MockTopicChannelTabClient) Blocks(ctx context.Context, in *BlocksReq, opts ...grpc.CallOption) (*BlocksResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Blocks", varargs...)
	ret0, _ := ret[0].(*BlocksResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Blocks indicates an expected call of Blocks.
func (mr *MockTopicChannelTabClientMockRecorder) Blocks(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Blocks", reflect.TypeOf((*MockTopicChannelTabClient)(nil).Blocks), varargs...)
}

// DelNewQuickMatchConfig mocks base method.
func (m *MockTopicChannelTabClient) DelNewQuickMatchConfig(ctx context.Context, in *DelNewQuickMatchConfigReq, opts ...grpc.CallOption) (*DelNewQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelNewQuickMatchConfig", varargs...)
	ret0, _ := ret[0].(*DelNewQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelNewQuickMatchConfig indicates an expected call of DelNewQuickMatchConfig.
func (mr *MockTopicChannelTabClientMockRecorder) DelNewQuickMatchConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelNewQuickMatchConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).DelNewQuickMatchConfig), varargs...)
}

// DelOfficialRoomNameConfig mocks base method.
func (m *MockTopicChannelTabClient) DelOfficialRoomNameConfig(ctx context.Context, in *DelOfficialRoomNameConfigReq, opts ...grpc.CallOption) (*DelOfficialRoomNameConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelOfficialRoomNameConfig", varargs...)
	ret0, _ := ret[0].(*DelOfficialRoomNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelOfficialRoomNameConfig indicates an expected call of DelOfficialRoomNameConfig.
func (mr *MockTopicChannelTabClientMockRecorder) DelOfficialRoomNameConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelOfficialRoomNameConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).DelOfficialRoomNameConfig), varargs...)
}

// DelPageHeadConfig mocks base method.
func (m *MockTopicChannelTabClient) DelPageHeadConfig(ctx context.Context, in *DelPageHeadConfigReq, opts ...grpc.CallOption) (*DelPageHeadConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelPageHeadConfig", varargs...)
	ret0, _ := ret[0].(*DelPageHeadConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPageHeadConfig indicates an expected call of DelPageHeadConfig.
func (mr *MockTopicChannelTabClientMockRecorder) DelPageHeadConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPageHeadConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).DelPageHeadConfig), varargs...)
}

// DelTabInfoExt mocks base method.
func (m *MockTopicChannelTabClient) DelTabInfoExt(ctx context.Context, in *DelTabInfoExtReq, opts ...grpc.CallOption) (*DelTabInfoExtResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelTabInfoExt", varargs...)
	ret0, _ := ret[0].(*DelTabInfoExtResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelTabInfoExt indicates an expected call of DelTabInfoExt.
func (mr *MockTopicChannelTabClientMockRecorder) DelTabInfoExt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelTabInfoExt", reflect.TypeOf((*MockTopicChannelTabClient)(nil).DelTabInfoExt), varargs...)
}

// DeleteBlock mocks base method.
func (m *MockTopicChannelTabClient) DeleteBlock(ctx context.Context, in *DeleteBlockReq, opts ...grpc.CallOption) (*DeleteBlockResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteBlock", varargs...)
	ret0, _ := ret[0].(*DeleteBlockResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteBlock indicates an expected call of DeleteBlock.
func (mr *MockTopicChannelTabClientMockRecorder) DeleteBlock(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBlock", reflect.TypeOf((*MockTopicChannelTabClient)(nil).DeleteBlock), varargs...)
}

// DeleteCategoryTitle mocks base method.
func (m *MockTopicChannelTabClient) DeleteCategoryTitle(ctx context.Context, in *DeleteCategoryTitleReq, opts ...grpc.CallOption) (*DeleteCategoryTitleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteCategoryTitle", varargs...)
	ret0, _ := ret[0].(*DeleteCategoryTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteCategoryTitle indicates an expected call of DeleteCategoryTitle.
func (mr *MockTopicChannelTabClientMockRecorder) DeleteCategoryTitle(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCategoryTitle", reflect.TypeOf((*MockTopicChannelTabClient)(nil).DeleteCategoryTitle), varargs...)
}

// DeleteElem mocks base method.
func (m *MockTopicChannelTabClient) DeleteElem(ctx context.Context, in *DeleteElemReq, opts ...grpc.CallOption) (*DeleteElemResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteElem", varargs...)
	ret0, _ := ret[0].(*DeleteElemResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteElem indicates an expected call of DeleteElem.
func (mr *MockTopicChannelTabClientMockRecorder) DeleteElem(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteElem", reflect.TypeOf((*MockTopicChannelTabClient)(nil).DeleteElem), varargs...)
}

// DeleteMultilevelTitle mocks base method.
func (m *MockTopicChannelTabClient) DeleteMultilevelTitle(ctx context.Context, in *DeleteMultilevelTitleReq, opts ...grpc.CallOption) (*DeleteMultilevelTitleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteMultilevelTitle", varargs...)
	ret0, _ := ret[0].(*DeleteMultilevelTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteMultilevelTitle indicates an expected call of DeleteMultilevelTitle.
func (mr *MockTopicChannelTabClientMockRecorder) DeleteMultilevelTitle(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMultilevelTitle", reflect.TypeOf((*MockTopicChannelTabClient)(nil).DeleteMultilevelTitle), varargs...)
}

// DeleteQuickMatchConfig mocks base method.
func (m *MockTopicChannelTabClient) DeleteQuickMatchConfig(ctx context.Context, in *DeleteQuickMatchConfigReq, opts ...grpc.CallOption) (*DeleteQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteQuickMatchConfig", varargs...)
	ret0, _ := ret[0].(*DeleteQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteQuickMatchConfig indicates an expected call of DeleteQuickMatchConfig.
func (mr *MockTopicChannelTabClientMockRecorder) DeleteQuickMatchConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteQuickMatchConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).DeleteQuickMatchConfig), varargs...)
}

// DeleteTab mocks base method.
func (m *MockTopicChannelTabClient) DeleteTab(ctx context.Context, in *DeleteTabReq, opts ...grpc.CallOption) (*DeleteTabResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTab", varargs...)
	ret0, _ := ret[0].(*DeleteTabResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTab indicates an expected call of DeleteTab.
func (mr *MockTopicChannelTabClientMockRecorder) DeleteTab(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTab", reflect.TypeOf((*MockTopicChannelTabClient)(nil).DeleteTab), varargs...)
}

// FindFilterIdsByMixTabIds mocks base method.
func (m *MockTopicChannelTabClient) FindFilterIdsByMixTabIds(ctx context.Context, in *FindFilterIdsByMixTabIdsReq, opts ...grpc.CallOption) (*FindFilterIdsByMixTabIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindFilterIdsByMixTabIds", varargs...)
	ret0, _ := ret[0].(*FindFilterIdsByMixTabIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindFilterIdsByMixTabIds indicates an expected call of FindFilterIdsByMixTabIds.
func (mr *MockTopicChannelTabClientMockRecorder) FindFilterIdsByMixTabIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindFilterIdsByMixTabIds", reflect.TypeOf((*MockTopicChannelTabClient)(nil).FindFilterIdsByMixTabIds), varargs...)
}

// FindFilterMixTabIds mocks base method.
func (m *MockTopicChannelTabClient) FindFilterMixTabIds(ctx context.Context, in *FindFilterMixTabIdsReq, opts ...grpc.CallOption) (*FindFilterMixTabIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindFilterMixTabIds", varargs...)
	ret0, _ := ret[0].(*FindFilterMixTabIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindFilterMixTabIds indicates an expected call of FindFilterMixTabIds.
func (mr *MockTopicChannelTabClientMockRecorder) FindFilterMixTabIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindFilterMixTabIds", reflect.TypeOf((*MockTopicChannelTabClient)(nil).FindFilterMixTabIds), varargs...)
}

// FiniteTabs mocks base method.
func (m *MockTopicChannelTabClient) FiniteTabs(ctx context.Context, in *FiniteTabsReq, opts ...grpc.CallOption) (*FiniteTabsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FiniteTabs", varargs...)
	ret0, _ := ret[0].(*FiniteTabsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FiniteTabs indicates an expected call of FiniteTabs.
func (mr *MockTopicChannelTabClientMockRecorder) FiniteTabs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FiniteTabs", reflect.TypeOf((*MockTopicChannelTabClient)(nil).FiniteTabs), varargs...)
}

// FiniteTabsByTags mocks base method.
func (m *MockTopicChannelTabClient) FiniteTabsByTags(ctx context.Context, in *FiniteTabsByTagsReq, opts ...grpc.CallOption) (*FiniteTabsByTagsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FiniteTabsByTags", varargs...)
	ret0, _ := ret[0].(*FiniteTabsByTagsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FiniteTabsByTags indicates an expected call of FiniteTabsByTags.
func (mr *MockTopicChannelTabClientMockRecorder) FiniteTabsByTags(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FiniteTabsByTags", reflect.TypeOf((*MockTopicChannelTabClient)(nil).FiniteTabsByTags), varargs...)
}

// GetAllTabInfoExt mocks base method.
func (m *MockTopicChannelTabClient) GetAllTabInfoExt(ctx context.Context, in *GetAllTabInfoExtReq, opts ...grpc.CallOption) (*GetAllTabInfoExtResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllTabInfoExt", varargs...)
	ret0, _ := ret[0].(*GetAllTabInfoExtResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllTabInfoExt indicates an expected call of GetAllTabInfoExt.
func (mr *MockTopicChannelTabClientMockRecorder) GetAllTabInfoExt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTabInfoExt", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetAllTabInfoExt), varargs...)
}

// GetAllTabOfCategory mocks base method.
func (m *MockTopicChannelTabClient) GetAllTabOfCategory(ctx context.Context, in *GetAllTabOfCategoryReq, opts ...grpc.CallOption) (*GetAllTabOfCategoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllTabOfCategory", varargs...)
	ret0, _ := ret[0].(*GetAllTabOfCategoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllTabOfCategory indicates an expected call of GetAllTabOfCategory.
func (mr *MockTopicChannelTabClientMockRecorder) GetAllTabOfCategory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTabOfCategory", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetAllTabOfCategory), varargs...)
}

// GetBlockInfos mocks base method.
func (m *MockTopicChannelTabClient) GetBlockInfos(ctx context.Context, in *GetBlockInfosReq, opts ...grpc.CallOption) (*GetBlockInfosResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBlockInfos", varargs...)
	ret0, _ := ret[0].(*GetBlockInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlockInfos indicates an expected call of GetBlockInfos.
func (mr *MockTopicChannelTabClientMockRecorder) GetBlockInfos(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlockInfos", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetBlockInfos), varargs...)
}

// GetCache mocks base method.
func (m *MockTopicChannelTabClient) GetCache(ctx context.Context, in *GetCacheReq, opts ...grpc.CallOption) (*GetCacheResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCache", varargs...)
	ret0, _ := ret[0].(*GetCacheResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCache indicates an expected call of GetCache.
func (mr *MockTopicChannelTabClientMockRecorder) GetCache(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCache", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetCache), varargs...)
}

// GetCategoryTitleList mocks base method.
func (m *MockTopicChannelTabClient) GetCategoryTitleList(ctx context.Context, in *GetCategoryTitleReq, opts ...grpc.CallOption) (*GetCategoryTitleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCategoryTitleList", varargs...)
	ret0, _ := ret[0].(*GetCategoryTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCategoryTitleList indicates an expected call of GetCategoryTitleList.
func (mr *MockTopicChannelTabClientMockRecorder) GetCategoryTitleList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategoryTitleList", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetCategoryTitleList), varargs...)
}

// GetCategoryTitleListForTT mocks base method.
func (m *MockTopicChannelTabClient) GetCategoryTitleListForTT(ctx context.Context, in *GetCategoryTitleListForTTReq, opts ...grpc.CallOption) (*GetCategoryTitleListForTTResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCategoryTitleListForTT", varargs...)
	ret0, _ := ret[0].(*GetCategoryTitleListForTTResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCategoryTitleListForTT indicates an expected call of GetCategoryTitleListForTT.
func (mr *MockTopicChannelTabClientMockRecorder) GetCategoryTitleListForTT(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategoryTitleListForTT", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetCategoryTitleListForTT), varargs...)
}

// GetMinorityGameTabs mocks base method.
func (m *MockTopicChannelTabClient) GetMinorityGameTabs(ctx context.Context, in *GetMinorityGameTabsReq, opts ...grpc.CallOption) (*GetMinorityGameTabsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMinorityGameTabs", varargs...)
	ret0, _ := ret[0].(*GetMinorityGameTabsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMinorityGameTabs indicates an expected call of GetMinorityGameTabs.
func (mr *MockTopicChannelTabClientMockRecorder) GetMinorityGameTabs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinorityGameTabs", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetMinorityGameTabs), varargs...)
}

// GetMultilevelTitle mocks base method.
func (m *MockTopicChannelTabClient) GetMultilevelTitle(ctx context.Context, in *GetMultilevelTitleReq, opts ...grpc.CallOption) (*GetMultilevelTitleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMultilevelTitle", varargs...)
	ret0, _ := ret[0].(*GetMultilevelTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultilevelTitle indicates an expected call of GetMultilevelTitle.
func (mr *MockTopicChannelTabClientMockRecorder) GetMultilevelTitle(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultilevelTitle", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetMultilevelTitle), varargs...)
}

// GetMultilevelTitleForTT mocks base method.
func (m *MockTopicChannelTabClient) GetMultilevelTitleForTT(ctx context.Context, in *GetMultilevelTitleForTTReq, opts ...grpc.CallOption) (*GetMultilevelTitleForTTResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMultilevelTitleForTT", varargs...)
	ret0, _ := ret[0].(*GetMultilevelTitleForTTResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultilevelTitleForTT indicates an expected call of GetMultilevelTitleForTT.
func (mr *MockTopicChannelTabClientMockRecorder) GetMultilevelTitleForTT(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultilevelTitleForTT", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetMultilevelTitleForTT), varargs...)
}

// GetNewQuickMatchConfig mocks base method.
func (m *MockTopicChannelTabClient) GetNewQuickMatchConfig(ctx context.Context, in *GetNewQuickMatchConfigReq, opts ...grpc.CallOption) (*GetNewQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNewQuickMatchConfig", varargs...)
	ret0, _ := ret[0].(*GetNewQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewQuickMatchConfig indicates an expected call of GetNewQuickMatchConfig.
func (mr *MockTopicChannelTabClientMockRecorder) GetNewQuickMatchConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewQuickMatchConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetNewQuickMatchConfig), varargs...)
}

// GetQuickMatchConfig mocks base method.
func (m *MockTopicChannelTabClient) GetQuickMatchConfig(ctx context.Context, in *GetQuickMatchConfigReq, opts ...grpc.CallOption) (*GetQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetQuickMatchConfig", varargs...)
	ret0, _ := ret[0].(*GetQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQuickMatchConfig indicates an expected call of GetQuickMatchConfig.
func (mr *MockTopicChannelTabClientMockRecorder) GetQuickMatchConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuickMatchConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetQuickMatchConfig), varargs...)
}

// GetRelationOfEtChannel mocks base method.
func (m *MockTopicChannelTabClient) GetRelationOfEtChannel(ctx context.Context, in *GetRelationOfEtChannelReq, opts ...grpc.CallOption) (*GetRelationOfEtChannelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRelationOfEtChannel", varargs...)
	ret0, _ := ret[0].(*GetRelationOfEtChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelationOfEtChannel indicates an expected call of GetRelationOfEtChannel.
func (mr *MockTopicChannelTabClientMockRecorder) GetRelationOfEtChannel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationOfEtChannel", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetRelationOfEtChannel), varargs...)
}

// GetReleaseConditionForTT mocks base method.
func (m *MockTopicChannelTabClient) GetReleaseConditionForTT(ctx context.Context, in *GetReleaseConditionForTTReq, opts ...grpc.CallOption) (*GetReleaseConditionForTTResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetReleaseConditionForTT", varargs...)
	ret0, _ := ret[0].(*GetReleaseConditionForTTResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReleaseConditionForTT indicates an expected call of GetReleaseConditionForTT.
func (mr *MockTopicChannelTabClientMockRecorder) GetReleaseConditionForTT(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReleaseConditionForTT", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetReleaseConditionForTT), varargs...)
}

// GetRoomNameConfigure mocks base method.
func (m *MockTopicChannelTabClient) GetRoomNameConfigure(ctx context.Context, in *GetRoomNameConfigureReq, opts ...grpc.CallOption) (*GetRoomNameConfigureResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRoomNameConfigure", varargs...)
	ret0, _ := ret[0].(*GetRoomNameConfigureResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRoomNameConfigure indicates an expected call of GetRoomNameConfigure.
func (mr *MockTopicChannelTabClientMockRecorder) GetRoomNameConfigure(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoomNameConfigure", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetRoomNameConfigure), varargs...)
}

// GetSimpleBanner mocks base method.
func (m *MockTopicChannelTabClient) GetSimpleBanner(ctx context.Context, in *GetSimpleBannerReq, opts ...grpc.CallOption) (*GetSimpleBannerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSimpleBanner", varargs...)
	ret0, _ := ret[0].(*GetSimpleBannerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSimpleBanner indicates an expected call of GetSimpleBanner.
func (mr *MockTopicChannelTabClientMockRecorder) GetSimpleBanner(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSimpleBanner", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetSimpleBanner), varargs...)
}

// GetTabByName mocks base method.
func (m *MockTopicChannelTabClient) GetTabByName(ctx context.Context, in *GetTabByNameReq, opts ...grpc.CallOption) (*GetTabByNameResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTabByName", varargs...)
	ret0, _ := ret[0].(*GetTabByNameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabByName indicates an expected call of GetTabByName.
func (mr *MockTopicChannelTabClientMockRecorder) GetTabByName(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabByName", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetTabByName), varargs...)
}

// GetTabByUGameId mocks base method.
func (m *MockTopicChannelTabClient) GetTabByUGameId(ctx context.Context, in *GetTabByUGameIdReq, opts ...grpc.CallOption) (*GetTabByUGameIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTabByUGameId", varargs...)
	ret0, _ := ret[0].(*GetTabByUGameIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabByUGameId indicates an expected call of GetTabByUGameId.
func (mr *MockTopicChannelTabClientMockRecorder) GetTabByUGameId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabByUGameId", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetTabByUGameId), varargs...)
}

// GetTabInfoExt mocks base method.
func (m *MockTopicChannelTabClient) GetTabInfoExt(ctx context.Context, in *GetTabInfoExtReq, opts ...grpc.CallOption) (*GetTabInfoExtResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTabInfoExt", varargs...)
	ret0, _ := ret[0].(*GetTabInfoExtResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabInfoExt indicates an expected call of GetTabInfoExt.
func (mr *MockTopicChannelTabClientMockRecorder) GetTabInfoExt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabInfoExt", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetTabInfoExt), varargs...)
}

// GetTabQuestionConfig mocks base method.
func (m *MockTopicChannelTabClient) GetTabQuestionConfig(ctx context.Context, in *GetTabQuestionConfigReq, opts ...grpc.CallOption) (*GetTabQuestionConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTabQuestionConfig", varargs...)
	ret0, _ := ret[0].(*GetTabQuestionConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabQuestionConfig indicates an expected call of GetTabQuestionConfig.
func (mr *MockTopicChannelTabClientMockRecorder) GetTabQuestionConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabQuestionConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetTabQuestionConfig), varargs...)
}

// GetTabsByCategoryEnum mocks base method.
func (m *MockTopicChannelTabClient) GetTabsByCategoryEnum(ctx context.Context, in *GetTabsByCategoryEnumReq, opts ...grpc.CallOption) (*GetTabsByCategoryEnumResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTabsByCategoryEnum", varargs...)
	ret0, _ := ret[0].(*GetTabsByCategoryEnumResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabsByCategoryEnum indicates an expected call of GetTabsByCategoryEnum.
func (mr *MockTopicChannelTabClientMockRecorder) GetTabsByCategoryEnum(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabsByCategoryEnum", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetTabsByCategoryEnum), varargs...)
}

// GetTabsByCategoryIds mocks base method.
func (m *MockTopicChannelTabClient) GetTabsByCategoryIds(ctx context.Context, in *GetTabsByCategoryIdsReq, opts ...grpc.CallOption) (*GetTabsByCategoryIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTabsByCategoryIds", varargs...)
	ret0, _ := ret[0].(*GetTabsByCategoryIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabsByCategoryIds indicates an expected call of GetTabsByCategoryIds.
func (mr *MockTopicChannelTabClientMockRecorder) GetTabsByCategoryIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabsByCategoryIds", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetTabsByCategoryIds), varargs...)
}

// GetTabsForManagementOp mocks base method.
func (m *MockTopicChannelTabClient) GetTabsForManagementOp(ctx context.Context, in *GetTabsForManagementOpReq, opts ...grpc.CallOption) (*GetTabsForManagementOpResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTabsForManagementOp", varargs...)
	ret0, _ := ret[0].(*GetTabsForManagementOpResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabsForManagementOp indicates an expected call of GetTabsForManagementOp.
func (mr *MockTopicChannelTabClientMockRecorder) GetTabsForManagementOp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabsForManagementOp", reflect.TypeOf((*MockTopicChannelTabClient)(nil).GetTabsForManagementOp), varargs...)
}

// HomePageHeadConfig mocks base method.
func (m *MockTopicChannelTabClient) HomePageHeadConfig(ctx context.Context, in *HomePageHeadConfigReq, opts ...grpc.CallOption) (*HomePageHeadConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HomePageHeadConfig", varargs...)
	ret0, _ := ret[0].(*HomePageHeadConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HomePageHeadConfig indicates an expected call of HomePageHeadConfig.
func (mr *MockTopicChannelTabClientMockRecorder) HomePageHeadConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HomePageHeadConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).HomePageHeadConfig), varargs...)
}

// InsertBlock mocks base method.
func (m *MockTopicChannelTabClient) InsertBlock(ctx context.Context, in *InsertBlockReq, opts ...grpc.CallOption) (*InsertBlockResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InsertBlock", varargs...)
	ret0, _ := ret[0].(*InsertBlockResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertBlock indicates an expected call of InsertBlock.
func (mr *MockTopicChannelTabClientMockRecorder) InsertBlock(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertBlock", reflect.TypeOf((*MockTopicChannelTabClient)(nil).InsertBlock), varargs...)
}

// InsertElem mocks base method.
func (m *MockTopicChannelTabClient) InsertElem(ctx context.Context, in *InsertElemReq, opts ...grpc.CallOption) (*InsertElemResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InsertElem", varargs...)
	ret0, _ := ret[0].(*InsertElemResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertElem indicates an expected call of InsertElem.
func (mr *MockTopicChannelTabClientMockRecorder) InsertElem(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertElem", reflect.TypeOf((*MockTopicChannelTabClient)(nil).InsertElem), varargs...)
}

// InsertRelationOfEtChannel mocks base method.
func (m *MockTopicChannelTabClient) InsertRelationOfEtChannel(ctx context.Context, in *InsertRelationOfEtChannelReq, opts ...grpc.CallOption) (*InsertRelationOfEtChannelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InsertRelationOfEtChannel", varargs...)
	ret0, _ := ret[0].(*InsertRelationOfEtChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertRelationOfEtChannel indicates an expected call of InsertRelationOfEtChannel.
func (mr *MockTopicChannelTabClientMockRecorder) InsertRelationOfEtChannel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertRelationOfEtChannel", reflect.TypeOf((*MockTopicChannelTabClient)(nil).InsertRelationOfEtChannel), varargs...)
}

// InsertTab mocks base method.
func (m *MockTopicChannelTabClient) InsertTab(ctx context.Context, in *InsertTabReq, opts ...grpc.CallOption) (*InsertTabResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InsertTab", varargs...)
	ret0, _ := ret[0].(*InsertTabResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertTab indicates an expected call of InsertTab.
func (mr *MockTopicChannelTabClientMockRecorder) InsertTab(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertTab", reflect.TypeOf((*MockTopicChannelTabClient)(nil).InsertTab), varargs...)
}

// ListOfficialRoomNameConfig mocks base method.
func (m *MockTopicChannelTabClient) ListOfficialRoomNameConfig(ctx context.Context, in *ListOfficialRoomNameConfigReq, opts ...grpc.CallOption) (*ListOfficialRoomNameConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListOfficialRoomNameConfig", varargs...)
	ret0, _ := ret[0].(*ListOfficialRoomNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOfficialRoomNameConfig indicates an expected call of ListOfficialRoomNameConfig.
func (mr *MockTopicChannelTabClientMockRecorder) ListOfficialRoomNameConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOfficialRoomNameConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).ListOfficialRoomNameConfig), varargs...)
}

// ListReleaseCondition mocks base method.
func (m *MockTopicChannelTabClient) ListReleaseCondition(ctx context.Context, in *ListReleaseConditionReq, opts ...grpc.CallOption) (*ListReleaseConditionResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListReleaseCondition", varargs...)
	ret0, _ := ret[0].(*ListReleaseConditionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListReleaseCondition indicates an expected call of ListReleaseCondition.
func (mr *MockTopicChannelTabClientMockRecorder) ListReleaseCondition(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListReleaseCondition", reflect.TypeOf((*MockTopicChannelTabClient)(nil).ListReleaseCondition), varargs...)
}

// ListReleaseConditionForTT mocks base method.
func (m *MockTopicChannelTabClient) ListReleaseConditionForTT(ctx context.Context, in *ListReleaseConditionForTTReq, opts ...grpc.CallOption) (*ListReleaseConditionForTTResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListReleaseConditionForTT", varargs...)
	ret0, _ := ret[0].(*ListReleaseConditionForTTResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListReleaseConditionForTT indicates an expected call of ListReleaseConditionForTT.
func (mr *MockTopicChannelTabClientMockRecorder) ListReleaseConditionForTT(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListReleaseConditionForTT", reflect.TypeOf((*MockTopicChannelTabClient)(nil).ListReleaseConditionForTT), varargs...)
}

// ListTabHomePageConfig mocks base method.
func (m *MockTopicChannelTabClient) ListTabHomePageConfig(ctx context.Context, in *ListTabHomePageConfigReq, opts ...grpc.CallOption) (*ListTabHomePageConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListTabHomePageConfig", varargs...)
	ret0, _ := ret[0].(*ListTabHomePageConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTabHomePageConfig indicates an expected call of ListTabHomePageConfig.
func (mr *MockTopicChannelTabClientMockRecorder) ListTabHomePageConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTabHomePageConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).ListTabHomePageConfig), varargs...)
}

// ModifyTabHomePageConfig mocks base method.
func (m *MockTopicChannelTabClient) ModifyTabHomePageConfig(ctx context.Context, in *ModifyTabHomePageConfigReq, opts ...grpc.CallOption) (*ModifyTabHomePageConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ModifyTabHomePageConfig", varargs...)
	ret0, _ := ret[0].(*ModifyTabHomePageConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyTabHomePageConfig indicates an expected call of ModifyTabHomePageConfig.
func (mr *MockTopicChannelTabClientMockRecorder) ModifyTabHomePageConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyTabHomePageConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).ModifyTabHomePageConfig), varargs...)
}

// ReSortCategoryTitle mocks base method.
func (m *MockTopicChannelTabClient) ReSortCategoryTitle(ctx context.Context, in *ReSortCategoryTitleReq, opts ...grpc.CallOption) (*ReSortCategoryTitleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReSortCategoryTitle", varargs...)
	ret0, _ := ret[0].(*ReSortCategoryTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReSortCategoryTitle indicates an expected call of ReSortCategoryTitle.
func (mr *MockTopicChannelTabClientMockRecorder) ReSortCategoryTitle(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReSortCategoryTitle", reflect.TypeOf((*MockTopicChannelTabClient)(nil).ReSortCategoryTitle), varargs...)
}

// RearrangeOfficialRoomNameConfig mocks base method.
func (m *MockTopicChannelTabClient) RearrangeOfficialRoomNameConfig(ctx context.Context, in *RearrangeOfficialRoomNameConfigReq, opts ...grpc.CallOption) (*RearrangeOfficialRoomNameConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RearrangeOfficialRoomNameConfig", varargs...)
	ret0, _ := ret[0].(*RearrangeOfficialRoomNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RearrangeOfficialRoomNameConfig indicates an expected call of RearrangeOfficialRoomNameConfig.
func (mr *MockTopicChannelTabClientMockRecorder) RearrangeOfficialRoomNameConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RearrangeOfficialRoomNameConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).RearrangeOfficialRoomNameConfig), varargs...)
}

// RearrangeTabs mocks base method.
func (m *MockTopicChannelTabClient) RearrangeTabs(ctx context.Context, in *RearrangeTabsReq, opts ...grpc.CallOption) (*RearrangeTabsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RearrangeTabs", varargs...)
	ret0, _ := ret[0].(*RearrangeTabsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RearrangeTabs indicates an expected call of RearrangeTabs.
func (mr *MockTopicChannelTabClientMockRecorder) RearrangeTabs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RearrangeTabs", reflect.TypeOf((*MockTopicChannelTabClient)(nil).RearrangeTabs), varargs...)
}

// ResortQuickMatchConfig mocks base method.
func (m *MockTopicChannelTabClient) ResortQuickMatchConfig(ctx context.Context, in *ResortQuickMatchConfigReq, opts ...grpc.CallOption) (*ResortQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResortQuickMatchConfig", varargs...)
	ret0, _ := ret[0].(*ResortQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResortQuickMatchConfig indicates an expected call of ResortQuickMatchConfig.
func (mr *MockTopicChannelTabClientMockRecorder) ResortQuickMatchConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResortQuickMatchConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).ResortQuickMatchConfig), varargs...)
}

// ResortTabInfoExt mocks base method.
func (m *MockTopicChannelTabClient) ResortTabInfoExt(ctx context.Context, in *ResortTabInfoExtReq, opts ...grpc.CallOption) (*ResortTabInfoExtResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResortTabInfoExt", varargs...)
	ret0, _ := ret[0].(*ResortTabInfoExtResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResortTabInfoExt indicates an expected call of ResortTabInfoExt.
func (mr *MockTopicChannelTabClientMockRecorder) ResortTabInfoExt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResortTabInfoExt", reflect.TypeOf((*MockTopicChannelTabClient)(nil).ResortTabInfoExt), varargs...)
}

// SearchTabs mocks base method.
func (m *MockTopicChannelTabClient) SearchTabs(ctx context.Context, in *SearchTabsReq, opts ...grpc.CallOption) (*SearchTabsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchTabs", varargs...)
	ret0, _ := ret[0].(*SearchTabsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchTabs indicates an expected call of SearchTabs.
func (mr *MockTopicChannelTabClientMockRecorder) SearchTabs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchTabs", reflect.TypeOf((*MockTopicChannelTabClient)(nil).SearchTabs), varargs...)
}

// SetBlockInfos mocks base method.
func (m *MockTopicChannelTabClient) SetBlockInfos(ctx context.Context, in *SetBlockInfosReq, opts ...grpc.CallOption) (*SetBlockInfosResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetBlockInfos", varargs...)
	ret0, _ := ret[0].(*SetBlockInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetBlockInfos indicates an expected call of SetBlockInfos.
func (mr *MockTopicChannelTabClientMockRecorder) SetBlockInfos(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBlockInfos", reflect.TypeOf((*MockTopicChannelTabClient)(nil).SetBlockInfos), varargs...)
}

// SetOfficialRoomNameConfig mocks base method.
func (m *MockTopicChannelTabClient) SetOfficialRoomNameConfig(ctx context.Context, in *SetOfficialRoomNameConfigReq, opts ...grpc.CallOption) (*SetOfficialRoomNameConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetOfficialRoomNameConfig", varargs...)
	ret0, _ := ret[0].(*SetOfficialRoomNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetOfficialRoomNameConfig indicates an expected call of SetOfficialRoomNameConfig.
func (mr *MockTopicChannelTabClientMockRecorder) SetOfficialRoomNameConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetOfficialRoomNameConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).SetOfficialRoomNameConfig), varargs...)
}

// SetReleaseCondition mocks base method.
func (m *MockTopicChannelTabClient) SetReleaseCondition(ctx context.Context, in *SetReleaseConditionReq, opts ...grpc.CallOption) (*SetReleaseConditionResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetReleaseCondition", varargs...)
	ret0, _ := ret[0].(*SetReleaseConditionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetReleaseCondition indicates an expected call of SetReleaseCondition.
func (mr *MockTopicChannelTabClientMockRecorder) SetReleaseCondition(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetReleaseCondition", reflect.TypeOf((*MockTopicChannelTabClient)(nil).SetReleaseCondition), varargs...)
}

// SetRoomNameConfigure mocks base method.
func (m *MockTopicChannelTabClient) SetRoomNameConfigure(ctx context.Context, in *SetRoomNameConfigureReq, opts ...grpc.CallOption) (*SetRoomNameConfigureResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetRoomNameConfigure", varargs...)
	ret0, _ := ret[0].(*SetRoomNameConfigureResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetRoomNameConfigure indicates an expected call of SetRoomNameConfigure.
func (mr *MockTopicChannelTabClientMockRecorder) SetRoomNameConfigure(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRoomNameConfigure", reflect.TypeOf((*MockTopicChannelTabClient)(nil).SetRoomNameConfigure), varargs...)
}

// SetShieldSwitch mocks base method.
func (m *MockTopicChannelTabClient) SetShieldSwitch(ctx context.Context, in *SetShieldSwitchReq, opts ...grpc.CallOption) (*SetShieldSwitchResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetShieldSwitch", varargs...)
	ret0, _ := ret[0].(*SetShieldSwitchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetShieldSwitch indicates an expected call of SetShieldSwitch.
func (mr *MockTopicChannelTabClientMockRecorder) SetShieldSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetShieldSwitch", reflect.TypeOf((*MockTopicChannelTabClient)(nil).SetShieldSwitch), varargs...)
}

// SetSimpleBanner mocks base method.
func (m *MockTopicChannelTabClient) SetSimpleBanner(ctx context.Context, in *SetSimpleBannerReq, opts ...grpc.CallOption) (*SetSimpleBannerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetSimpleBanner", varargs...)
	ret0, _ := ret[0].(*SetSimpleBannerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSimpleBanner indicates an expected call of SetSimpleBanner.
func (mr *MockTopicChannelTabClientMockRecorder) SetSimpleBanner(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSimpleBanner", reflect.TypeOf((*MockTopicChannelTabClient)(nil).SetSimpleBanner), varargs...)
}

// SortTabHomePageConfig mocks base method.
func (m *MockTopicChannelTabClient) SortTabHomePageConfig(ctx context.Context, in *SortTabHomePageConfigReq, opts ...grpc.CallOption) (*SortTabHomePageConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SortTabHomePageConfig", varargs...)
	ret0, _ := ret[0].(*SortTabHomePageConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SortTabHomePageConfig indicates an expected call of SortTabHomePageConfig.
func (mr *MockTopicChannelTabClientMockRecorder) SortTabHomePageConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SortTabHomePageConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).SortTabHomePageConfig), varargs...)
}

// Tabs mocks base method.
func (m *MockTopicChannelTabClient) Tabs(ctx context.Context, in *TabsReq, opts ...grpc.CallOption) (*TabsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Tabs", varargs...)
	ret0, _ := ret[0].(*TabsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Tabs indicates an expected call of Tabs.
func (mr *MockTopicChannelTabClientMockRecorder) Tabs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Tabs", reflect.TypeOf((*MockTopicChannelTabClient)(nil).Tabs), varargs...)
}

// TabsForTT mocks base method.
func (m *MockTopicChannelTabClient) TabsForTT(ctx context.Context, in *TabsForTTReq, opts ...grpc.CallOption) (*TabsForTTResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TabsForTT", varargs...)
	ret0, _ := ret[0].(*TabsForTTResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TabsForTT indicates an expected call of TabsForTT.
func (mr *MockTopicChannelTabClientMockRecorder) TabsForTT(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TabsForTT", reflect.TypeOf((*MockTopicChannelTabClient)(nil).TabsForTT), varargs...)
}

// UpdateBlock mocks base method.
func (m *MockTopicChannelTabClient) UpdateBlock(ctx context.Context, in *UpdateBlockReq, opts ...grpc.CallOption) (*UpdateBlockResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateBlock", varargs...)
	ret0, _ := ret[0].(*UpdateBlockResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBlock indicates an expected call of UpdateBlock.
func (mr *MockTopicChannelTabClientMockRecorder) UpdateBlock(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBlock", reflect.TypeOf((*MockTopicChannelTabClient)(nil).UpdateBlock), varargs...)
}

// UpdateBusinessConf mocks base method.
func (m *MockTopicChannelTabClient) UpdateBusinessConf(ctx context.Context, in *UpdateBusinessConfReq, opts ...grpc.CallOption) (*UpdateBusinessConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateBusinessConf", varargs...)
	ret0, _ := ret[0].(*UpdateBusinessConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBusinessConf indicates an expected call of UpdateBusinessConf.
func (mr *MockTopicChannelTabClientMockRecorder) UpdateBusinessConf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBusinessConf", reflect.TypeOf((*MockTopicChannelTabClient)(nil).UpdateBusinessConf), varargs...)
}

// UpdateCategoryTitle mocks base method.
func (m *MockTopicChannelTabClient) UpdateCategoryTitle(ctx context.Context, in *UpdateCategoryTitleReq, opts ...grpc.CallOption) (*UpdateCategoryTitleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateCategoryTitle", varargs...)
	ret0, _ := ret[0].(*UpdateCategoryTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCategoryTitle indicates an expected call of UpdateCategoryTitle.
func (mr *MockTopicChannelTabClientMockRecorder) UpdateCategoryTitle(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCategoryTitle", reflect.TypeOf((*MockTopicChannelTabClient)(nil).UpdateCategoryTitle), varargs...)
}

// UpdateElem mocks base method.
func (m *MockTopicChannelTabClient) UpdateElem(ctx context.Context, in *UpdateElemReq, opts ...grpc.CallOption) (*UpdateElemResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateElem", varargs...)
	ret0, _ := ret[0].(*UpdateElemResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateElem indicates an expected call of UpdateElem.
func (mr *MockTopicChannelTabClientMockRecorder) UpdateElem(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateElem", reflect.TypeOf((*MockTopicChannelTabClient)(nil).UpdateElem), varargs...)
}

// UpdateMultilevelTitle mocks base method.
func (m *MockTopicChannelTabClient) UpdateMultilevelTitle(ctx context.Context, in *UpdateMultilevelTitleReq, opts ...grpc.CallOption) (*UpdateMultilevelTitleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateMultilevelTitle", varargs...)
	ret0, _ := ret[0].(*UpdateMultilevelTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateMultilevelTitle indicates an expected call of UpdateMultilevelTitle.
func (mr *MockTopicChannelTabClientMockRecorder) UpdateMultilevelTitle(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMultilevelTitle", reflect.TypeOf((*MockTopicChannelTabClient)(nil).UpdateMultilevelTitle), varargs...)
}

// UpdateQuickMatchConfig mocks base method.
func (m *MockTopicChannelTabClient) UpdateQuickMatchConfig(ctx context.Context, in *UpdateQuickMatchConfigReq, opts ...grpc.CallOption) (*UpdateQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateQuickMatchConfig", varargs...)
	ret0, _ := ret[0].(*UpdateQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateQuickMatchConfig indicates an expected call of UpdateQuickMatchConfig.
func (mr *MockTopicChannelTabClientMockRecorder) UpdateQuickMatchConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateQuickMatchConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).UpdateQuickMatchConfig), varargs...)
}

// UpdateTab mocks base method.
func (m *MockTopicChannelTabClient) UpdateTab(ctx context.Context, in *UpdateTabReq, opts ...grpc.CallOption) (*UpdateTabResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTab", varargs...)
	ret0, _ := ret[0].(*UpdateTabResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTab indicates an expected call of UpdateTab.
func (mr *MockTopicChannelTabClientMockRecorder) UpdateTab(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTab", reflect.TypeOf((*MockTopicChannelTabClient)(nil).UpdateTab), varargs...)
}

// UpdateTabMiniGameId mocks base method.
func (m *MockTopicChannelTabClient) UpdateTabMiniGameId(ctx context.Context, in *UpdateTabMiniGameIdReq, opts ...grpc.CallOption) (*UpdateTabMiniGameIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTabMiniGameId", varargs...)
	ret0, _ := ret[0].(*UpdateTabMiniGameIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTabMiniGameId indicates an expected call of UpdateTabMiniGameId.
func (mr *MockTopicChannelTabClientMockRecorder) UpdateTabMiniGameId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTabMiniGameId", reflect.TypeOf((*MockTopicChannelTabClient)(nil).UpdateTabMiniGameId), varargs...)
}

// UpdateTabUGameId mocks base method.
func (m *MockTopicChannelTabClient) UpdateTabUGameId(ctx context.Context, in *UpdateTabUGameIdReq, opts ...grpc.CallOption) (*UpdateTabUGameIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTabUGameId", varargs...)
	ret0, _ := ret[0].(*UpdateTabUGameIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTabUGameId indicates an expected call of UpdateTabUGameId.
func (mr *MockTopicChannelTabClientMockRecorder) UpdateTabUGameId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTabUGameId", reflect.TypeOf((*MockTopicChannelTabClient)(nil).UpdateTabUGameId), varargs...)
}

// UpsertHeadConfig mocks base method.
func (m *MockTopicChannelTabClient) UpsertHeadConfig(ctx context.Context, in *UpsertHeadConfigReq, opts ...grpc.CallOption) (*UpsertHeadConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertHeadConfig", varargs...)
	ret0, _ := ret[0].(*UpsertHeadConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertHeadConfig indicates an expected call of UpsertHeadConfig.
func (mr *MockTopicChannelTabClientMockRecorder) UpsertHeadConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertHeadConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).UpsertHeadConfig), varargs...)
}

// UpsertNewQuickMatchConfig mocks base method.
func (m *MockTopicChannelTabClient) UpsertNewQuickMatchConfig(ctx context.Context, in *UpsertNewQuickMatchConfigReq, opts ...grpc.CallOption) (*UpsertNewQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertNewQuickMatchConfig", varargs...)
	ret0, _ := ret[0].(*UpsertNewQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertNewQuickMatchConfig indicates an expected call of UpsertNewQuickMatchConfig.
func (mr *MockTopicChannelTabClientMockRecorder) UpsertNewQuickMatchConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertNewQuickMatchConfig", reflect.TypeOf((*MockTopicChannelTabClient)(nil).UpsertNewQuickMatchConfig), varargs...)
}

// UpsertTabInfoExt mocks base method.
func (m *MockTopicChannelTabClient) UpsertTabInfoExt(ctx context.Context, in *UpsertTabInfoExtReq, opts ...grpc.CallOption) (*UpsertTabInfoExtResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertTabInfoExt", varargs...)
	ret0, _ := ret[0].(*UpsertTabInfoExtResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertTabInfoExt indicates an expected call of UpsertTabInfoExt.
func (mr *MockTopicChannelTabClientMockRecorder) UpsertTabInfoExt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertTabInfoExt", reflect.TypeOf((*MockTopicChannelTabClient)(nil).UpsertTabInfoExt), varargs...)
}

// MockTopicChannelTabServer is a mock of TopicChannelTabServer interface.
type MockTopicChannelTabServer struct {
	ctrl     *gomock.Controller
	recorder *MockTopicChannelTabServerMockRecorder
}

// MockTopicChannelTabServerMockRecorder is the mock recorder for MockTopicChannelTabServer.
type MockTopicChannelTabServerMockRecorder struct {
	mock *MockTopicChannelTabServer
}

// NewMockTopicChannelTabServer creates a new mock instance.
func NewMockTopicChannelTabServer(ctrl *gomock.Controller) *MockTopicChannelTabServer {
	mock := &MockTopicChannelTabServer{ctrl: ctrl}
	mock.recorder = &MockTopicChannelTabServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTopicChannelTabServer) EXPECT() *MockTopicChannelTabServerMockRecorder {
	return m.recorder
}

// AddCategoryTitle mocks base method.
func (m *MockTopicChannelTabServer) AddCategoryTitle(ctx context.Context, in *AddCategoryTitleReq) (*AddCategoryTitleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCategoryTitle", ctx, in)
	ret0, _ := ret[0].(*AddCategoryTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddCategoryTitle indicates an expected call of AddCategoryTitle.
func (mr *MockTopicChannelTabServerMockRecorder) AddCategoryTitle(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCategoryTitle", reflect.TypeOf((*MockTopicChannelTabServer)(nil).AddCategoryTitle), ctx, in)
}

// AddMultilevelTitle mocks base method.
func (m *MockTopicChannelTabServer) AddMultilevelTitle(ctx context.Context, in *AddMultilevelTitleReq) (*AddMultilevelTitleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMultilevelTitle", ctx, in)
	ret0, _ := ret[0].(*AddMultilevelTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMultilevelTitle indicates an expected call of AddMultilevelTitle.
func (mr *MockTopicChannelTabServerMockRecorder) AddMultilevelTitle(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMultilevelTitle", reflect.TypeOf((*MockTopicChannelTabServer)(nil).AddMultilevelTitle), ctx, in)
}

// BatchGetBlockRelations mocks base method.
func (m *MockTopicChannelTabServer) BatchGetBlockRelations(ctx context.Context, in *BatchGetBlockRelationsReq) (*BatchGetBlockRelationsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBlockRelations", ctx, in)
	ret0, _ := ret[0].(*BatchGetBlockRelationsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBlockRelations indicates an expected call of BatchGetBlockRelations.
func (mr *MockTopicChannelTabServerMockRecorder) BatchGetBlockRelations(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBlockRelations", reflect.TypeOf((*MockTopicChannelTabServer)(nil).BatchGetBlockRelations), ctx, in)
}

// BatchGetBlocks mocks base method.
func (m *MockTopicChannelTabServer) BatchGetBlocks(ctx context.Context, in *BatchGetBlocksReq) (*BatchGetBlocksResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBlocks", ctx, in)
	ret0, _ := ret[0].(*BatchGetBlocksResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBlocks indicates an expected call of BatchGetBlocks.
func (mr *MockTopicChannelTabServerMockRecorder) BatchGetBlocks(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBlocks", reflect.TypeOf((*MockTopicChannelTabServer)(nil).BatchGetBlocks), ctx, in)
}

// BatchGetBusinessBlockInfo mocks base method.
func (m *MockTopicChannelTabServer) BatchGetBusinessBlockInfo(ctx context.Context, in *BatchGetBusinessBlockInfoReq) (*BatchGetBusinessBlockInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBusinessBlockInfo", ctx, in)
	ret0, _ := ret[0].(*BatchGetBusinessBlockInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBusinessBlockInfo indicates an expected call of BatchGetBusinessBlockInfo.
func (mr *MockTopicChannelTabServerMockRecorder) BatchGetBusinessBlockInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBusinessBlockInfo", reflect.TypeOf((*MockTopicChannelTabServer)(nil).BatchGetBusinessBlockInfo), ctx, in)
}

// BatchGetGameLabelItems mocks base method.
func (m *MockTopicChannelTabServer) BatchGetGameLabelItems(ctx context.Context, in *BatchGetGameLabelItemsReq) (*BatchGetGameLabelItemsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetGameLabelItems", ctx, in)
	ret0, _ := ret[0].(*BatchGetGameLabelItemsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGameLabelItems indicates an expected call of BatchGetGameLabelItems.
func (mr *MockTopicChannelTabServerMockRecorder) BatchGetGameLabelItems(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGameLabelItems", reflect.TypeOf((*MockTopicChannelTabServer)(nil).BatchGetGameLabelItems), ctx, in)
}

// BatchGetNewQuickMatchConfig mocks base method.
func (m *MockTopicChannelTabServer) BatchGetNewQuickMatchConfig(ctx context.Context, in *BatchGetNewQuickMatchConfigReq) (*BatchGetNewQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetNewQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*BatchGetNewQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetNewQuickMatchConfig indicates an expected call of BatchGetNewQuickMatchConfig.
func (mr *MockTopicChannelTabServerMockRecorder) BatchGetNewQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetNewQuickMatchConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).BatchGetNewQuickMatchConfig), ctx, in)
}

// BatchGetOfficialRoomNameConfig mocks base method.
func (m *MockTopicChannelTabServer) BatchGetOfficialRoomNameConfig(ctx context.Context, in *BatchGetOfficialRoomNameConfigReq) (*BatchGetOfficialRoomNameConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetOfficialRoomNameConfig", ctx, in)
	ret0, _ := ret[0].(*BatchGetOfficialRoomNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetOfficialRoomNameConfig indicates an expected call of BatchGetOfficialRoomNameConfig.
func (mr *MockTopicChannelTabServerMockRecorder) BatchGetOfficialRoomNameConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetOfficialRoomNameConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).BatchGetOfficialRoomNameConfig), ctx, in)
}

// BatchGetShieldSwitchByTabIds mocks base method.
func (m *MockTopicChannelTabServer) BatchGetShieldSwitchByTabIds(ctx context.Context, in *BatchGetShieldSwitchByTabIdsReq) (*BatchGetShieldSwitchByTabIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetShieldSwitchByTabIds", ctx, in)
	ret0, _ := ret[0].(*BatchGetShieldSwitchByTabIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetShieldSwitchByTabIds indicates an expected call of BatchGetShieldSwitchByTabIds.
func (mr *MockTopicChannelTabServerMockRecorder) BatchGetShieldSwitchByTabIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetShieldSwitchByTabIds", reflect.TypeOf((*MockTopicChannelTabServer)(nil).BatchGetShieldSwitchByTabIds), ctx, in)
}

// Blocks mocks base method.
func (m *MockTopicChannelTabServer) Blocks(ctx context.Context, in *BlocksReq) (*BlocksResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Blocks", ctx, in)
	ret0, _ := ret[0].(*BlocksResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Blocks indicates an expected call of Blocks.
func (mr *MockTopicChannelTabServerMockRecorder) Blocks(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Blocks", reflect.TypeOf((*MockTopicChannelTabServer)(nil).Blocks), ctx, in)
}

// DelNewQuickMatchConfig mocks base method.
func (m *MockTopicChannelTabServer) DelNewQuickMatchConfig(ctx context.Context, in *DelNewQuickMatchConfigReq) (*DelNewQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelNewQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*DelNewQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelNewQuickMatchConfig indicates an expected call of DelNewQuickMatchConfig.
func (mr *MockTopicChannelTabServerMockRecorder) DelNewQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelNewQuickMatchConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).DelNewQuickMatchConfig), ctx, in)
}

// DelOfficialRoomNameConfig mocks base method.
func (m *MockTopicChannelTabServer) DelOfficialRoomNameConfig(ctx context.Context, in *DelOfficialRoomNameConfigReq) (*DelOfficialRoomNameConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelOfficialRoomNameConfig", ctx, in)
	ret0, _ := ret[0].(*DelOfficialRoomNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelOfficialRoomNameConfig indicates an expected call of DelOfficialRoomNameConfig.
func (mr *MockTopicChannelTabServerMockRecorder) DelOfficialRoomNameConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelOfficialRoomNameConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).DelOfficialRoomNameConfig), ctx, in)
}

// DelPageHeadConfig mocks base method.
func (m *MockTopicChannelTabServer) DelPageHeadConfig(ctx context.Context, in *DelPageHeadConfigReq) (*DelPageHeadConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPageHeadConfig", ctx, in)
	ret0, _ := ret[0].(*DelPageHeadConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPageHeadConfig indicates an expected call of DelPageHeadConfig.
func (mr *MockTopicChannelTabServerMockRecorder) DelPageHeadConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPageHeadConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).DelPageHeadConfig), ctx, in)
}

// DelTabInfoExt mocks base method.
func (m *MockTopicChannelTabServer) DelTabInfoExt(ctx context.Context, in *DelTabInfoExtReq) (*DelTabInfoExtResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelTabInfoExt", ctx, in)
	ret0, _ := ret[0].(*DelTabInfoExtResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelTabInfoExt indicates an expected call of DelTabInfoExt.
func (mr *MockTopicChannelTabServerMockRecorder) DelTabInfoExt(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelTabInfoExt", reflect.TypeOf((*MockTopicChannelTabServer)(nil).DelTabInfoExt), ctx, in)
}

// DeleteBlock mocks base method.
func (m *MockTopicChannelTabServer) DeleteBlock(ctx context.Context, in *DeleteBlockReq) (*DeleteBlockResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBlock", ctx, in)
	ret0, _ := ret[0].(*DeleteBlockResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteBlock indicates an expected call of DeleteBlock.
func (mr *MockTopicChannelTabServerMockRecorder) DeleteBlock(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBlock", reflect.TypeOf((*MockTopicChannelTabServer)(nil).DeleteBlock), ctx, in)
}

// DeleteCategoryTitle mocks base method.
func (m *MockTopicChannelTabServer) DeleteCategoryTitle(ctx context.Context, in *DeleteCategoryTitleReq) (*DeleteCategoryTitleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCategoryTitle", ctx, in)
	ret0, _ := ret[0].(*DeleteCategoryTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteCategoryTitle indicates an expected call of DeleteCategoryTitle.
func (mr *MockTopicChannelTabServerMockRecorder) DeleteCategoryTitle(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCategoryTitle", reflect.TypeOf((*MockTopicChannelTabServer)(nil).DeleteCategoryTitle), ctx, in)
}

// DeleteElem mocks base method.
func (m *MockTopicChannelTabServer) DeleteElem(ctx context.Context, in *DeleteElemReq) (*DeleteElemResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteElem", ctx, in)
	ret0, _ := ret[0].(*DeleteElemResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteElem indicates an expected call of DeleteElem.
func (mr *MockTopicChannelTabServerMockRecorder) DeleteElem(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteElem", reflect.TypeOf((*MockTopicChannelTabServer)(nil).DeleteElem), ctx, in)
}

// DeleteMultilevelTitle mocks base method.
func (m *MockTopicChannelTabServer) DeleteMultilevelTitle(ctx context.Context, in *DeleteMultilevelTitleReq) (*DeleteMultilevelTitleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteMultilevelTitle", ctx, in)
	ret0, _ := ret[0].(*DeleteMultilevelTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteMultilevelTitle indicates an expected call of DeleteMultilevelTitle.
func (mr *MockTopicChannelTabServerMockRecorder) DeleteMultilevelTitle(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMultilevelTitle", reflect.TypeOf((*MockTopicChannelTabServer)(nil).DeleteMultilevelTitle), ctx, in)
}

// DeleteQuickMatchConfig mocks base method.
func (m *MockTopicChannelTabServer) DeleteQuickMatchConfig(ctx context.Context, in *DeleteQuickMatchConfigReq) (*DeleteQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*DeleteQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteQuickMatchConfig indicates an expected call of DeleteQuickMatchConfig.
func (mr *MockTopicChannelTabServerMockRecorder) DeleteQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteQuickMatchConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).DeleteQuickMatchConfig), ctx, in)
}

// DeleteTab mocks base method.
func (m *MockTopicChannelTabServer) DeleteTab(ctx context.Context, in *DeleteTabReq) (*DeleteTabResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTab", ctx, in)
	ret0, _ := ret[0].(*DeleteTabResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTab indicates an expected call of DeleteTab.
func (mr *MockTopicChannelTabServerMockRecorder) DeleteTab(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTab", reflect.TypeOf((*MockTopicChannelTabServer)(nil).DeleteTab), ctx, in)
}

// FindFilterIdsByMixTabIds mocks base method.
func (m *MockTopicChannelTabServer) FindFilterIdsByMixTabIds(ctx context.Context, in *FindFilterIdsByMixTabIdsReq) (*FindFilterIdsByMixTabIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindFilterIdsByMixTabIds", ctx, in)
	ret0, _ := ret[0].(*FindFilterIdsByMixTabIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindFilterIdsByMixTabIds indicates an expected call of FindFilterIdsByMixTabIds.
func (mr *MockTopicChannelTabServerMockRecorder) FindFilterIdsByMixTabIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindFilterIdsByMixTabIds", reflect.TypeOf((*MockTopicChannelTabServer)(nil).FindFilterIdsByMixTabIds), ctx, in)
}

// FindFilterMixTabIds mocks base method.
func (m *MockTopicChannelTabServer) FindFilterMixTabIds(ctx context.Context, in *FindFilterMixTabIdsReq) (*FindFilterMixTabIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindFilterMixTabIds", ctx, in)
	ret0, _ := ret[0].(*FindFilterMixTabIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindFilterMixTabIds indicates an expected call of FindFilterMixTabIds.
func (mr *MockTopicChannelTabServerMockRecorder) FindFilterMixTabIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindFilterMixTabIds", reflect.TypeOf((*MockTopicChannelTabServer)(nil).FindFilterMixTabIds), ctx, in)
}

// FiniteTabs mocks base method.
func (m *MockTopicChannelTabServer) FiniteTabs(ctx context.Context, in *FiniteTabsReq) (*FiniteTabsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FiniteTabs", ctx, in)
	ret0, _ := ret[0].(*FiniteTabsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FiniteTabs indicates an expected call of FiniteTabs.
func (mr *MockTopicChannelTabServerMockRecorder) FiniteTabs(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FiniteTabs", reflect.TypeOf((*MockTopicChannelTabServer)(nil).FiniteTabs), ctx, in)
}

// FiniteTabsByTags mocks base method.
func (m *MockTopicChannelTabServer) FiniteTabsByTags(ctx context.Context, in *FiniteTabsByTagsReq) (*FiniteTabsByTagsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FiniteTabsByTags", ctx, in)
	ret0, _ := ret[0].(*FiniteTabsByTagsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FiniteTabsByTags indicates an expected call of FiniteTabsByTags.
func (mr *MockTopicChannelTabServerMockRecorder) FiniteTabsByTags(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FiniteTabsByTags", reflect.TypeOf((*MockTopicChannelTabServer)(nil).FiniteTabsByTags), ctx, in)
}

// GetAllTabInfoExt mocks base method.
func (m *MockTopicChannelTabServer) GetAllTabInfoExt(ctx context.Context, in *GetAllTabInfoExtReq) (*GetAllTabInfoExtResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllTabInfoExt", ctx, in)
	ret0, _ := ret[0].(*GetAllTabInfoExtResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllTabInfoExt indicates an expected call of GetAllTabInfoExt.
func (mr *MockTopicChannelTabServerMockRecorder) GetAllTabInfoExt(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTabInfoExt", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetAllTabInfoExt), ctx, in)
}

// GetAllTabOfCategory mocks base method.
func (m *MockTopicChannelTabServer) GetAllTabOfCategory(ctx context.Context, in *GetAllTabOfCategoryReq) (*GetAllTabOfCategoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllTabOfCategory", ctx, in)
	ret0, _ := ret[0].(*GetAllTabOfCategoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllTabOfCategory indicates an expected call of GetAllTabOfCategory.
func (mr *MockTopicChannelTabServerMockRecorder) GetAllTabOfCategory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTabOfCategory", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetAllTabOfCategory), ctx, in)
}

// GetBlockInfos mocks base method.
func (m *MockTopicChannelTabServer) GetBlockInfos(ctx context.Context, in *GetBlockInfosReq) (*GetBlockInfosResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlockInfos", ctx, in)
	ret0, _ := ret[0].(*GetBlockInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlockInfos indicates an expected call of GetBlockInfos.
func (mr *MockTopicChannelTabServerMockRecorder) GetBlockInfos(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlockInfos", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetBlockInfos), ctx, in)
}

// GetCache mocks base method.
func (m *MockTopicChannelTabServer) GetCache(ctx context.Context, in *GetCacheReq) (*GetCacheResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCache", ctx, in)
	ret0, _ := ret[0].(*GetCacheResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCache indicates an expected call of GetCache.
func (mr *MockTopicChannelTabServerMockRecorder) GetCache(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCache", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetCache), ctx, in)
}

// GetCategoryTitleList mocks base method.
func (m *MockTopicChannelTabServer) GetCategoryTitleList(ctx context.Context, in *GetCategoryTitleReq) (*GetCategoryTitleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCategoryTitleList", ctx, in)
	ret0, _ := ret[0].(*GetCategoryTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCategoryTitleList indicates an expected call of GetCategoryTitleList.
func (mr *MockTopicChannelTabServerMockRecorder) GetCategoryTitleList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategoryTitleList", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetCategoryTitleList), ctx, in)
}

// GetCategoryTitleListForTT mocks base method.
func (m *MockTopicChannelTabServer) GetCategoryTitleListForTT(ctx context.Context, in *GetCategoryTitleListForTTReq) (*GetCategoryTitleListForTTResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCategoryTitleListForTT", ctx, in)
	ret0, _ := ret[0].(*GetCategoryTitleListForTTResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCategoryTitleListForTT indicates an expected call of GetCategoryTitleListForTT.
func (mr *MockTopicChannelTabServerMockRecorder) GetCategoryTitleListForTT(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategoryTitleListForTT", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetCategoryTitleListForTT), ctx, in)
}

// GetMinorityGameTabs mocks base method.
func (m *MockTopicChannelTabServer) GetMinorityGameTabs(ctx context.Context, in *GetMinorityGameTabsReq) (*GetMinorityGameTabsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinorityGameTabs", ctx, in)
	ret0, _ := ret[0].(*GetMinorityGameTabsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMinorityGameTabs indicates an expected call of GetMinorityGameTabs.
func (mr *MockTopicChannelTabServerMockRecorder) GetMinorityGameTabs(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinorityGameTabs", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetMinorityGameTabs), ctx, in)
}

// GetMultilevelTitle mocks base method.
func (m *MockTopicChannelTabServer) GetMultilevelTitle(ctx context.Context, in *GetMultilevelTitleReq) (*GetMultilevelTitleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultilevelTitle", ctx, in)
	ret0, _ := ret[0].(*GetMultilevelTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultilevelTitle indicates an expected call of GetMultilevelTitle.
func (mr *MockTopicChannelTabServerMockRecorder) GetMultilevelTitle(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultilevelTitle", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetMultilevelTitle), ctx, in)
}

// GetMultilevelTitleForTT mocks base method.
func (m *MockTopicChannelTabServer) GetMultilevelTitleForTT(ctx context.Context, in *GetMultilevelTitleForTTReq) (*GetMultilevelTitleForTTResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultilevelTitleForTT", ctx, in)
	ret0, _ := ret[0].(*GetMultilevelTitleForTTResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultilevelTitleForTT indicates an expected call of GetMultilevelTitleForTT.
func (mr *MockTopicChannelTabServerMockRecorder) GetMultilevelTitleForTT(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultilevelTitleForTT", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetMultilevelTitleForTT), ctx, in)
}

// GetNewQuickMatchConfig mocks base method.
func (m *MockTopicChannelTabServer) GetNewQuickMatchConfig(ctx context.Context, in *GetNewQuickMatchConfigReq) (*GetNewQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*GetNewQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewQuickMatchConfig indicates an expected call of GetNewQuickMatchConfig.
func (mr *MockTopicChannelTabServerMockRecorder) GetNewQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewQuickMatchConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetNewQuickMatchConfig), ctx, in)
}

// GetQuickMatchConfig mocks base method.
func (m *MockTopicChannelTabServer) GetQuickMatchConfig(ctx context.Context, in *GetQuickMatchConfigReq) (*GetQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*GetQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQuickMatchConfig indicates an expected call of GetQuickMatchConfig.
func (mr *MockTopicChannelTabServerMockRecorder) GetQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuickMatchConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetQuickMatchConfig), ctx, in)
}

// GetRelationOfEtChannel mocks base method.
func (m *MockTopicChannelTabServer) GetRelationOfEtChannel(ctx context.Context, in *GetRelationOfEtChannelReq) (*GetRelationOfEtChannelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelationOfEtChannel", ctx, in)
	ret0, _ := ret[0].(*GetRelationOfEtChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelationOfEtChannel indicates an expected call of GetRelationOfEtChannel.
func (mr *MockTopicChannelTabServerMockRecorder) GetRelationOfEtChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationOfEtChannel", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetRelationOfEtChannel), ctx, in)
}

// GetReleaseConditionForTT mocks base method.
func (m *MockTopicChannelTabServer) GetReleaseConditionForTT(ctx context.Context, in *GetReleaseConditionForTTReq) (*GetReleaseConditionForTTResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReleaseConditionForTT", ctx, in)
	ret0, _ := ret[0].(*GetReleaseConditionForTTResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReleaseConditionForTT indicates an expected call of GetReleaseConditionForTT.
func (mr *MockTopicChannelTabServerMockRecorder) GetReleaseConditionForTT(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReleaseConditionForTT", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetReleaseConditionForTT), ctx, in)
}

// GetRoomNameConfigure mocks base method.
func (m *MockTopicChannelTabServer) GetRoomNameConfigure(ctx context.Context, in *GetRoomNameConfigureReq) (*GetRoomNameConfigureResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoomNameConfigure", ctx, in)
	ret0, _ := ret[0].(*GetRoomNameConfigureResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRoomNameConfigure indicates an expected call of GetRoomNameConfigure.
func (mr *MockTopicChannelTabServerMockRecorder) GetRoomNameConfigure(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoomNameConfigure", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetRoomNameConfigure), ctx, in)
}

// GetSimpleBanner mocks base method.
func (m *MockTopicChannelTabServer) GetSimpleBanner(ctx context.Context, in *GetSimpleBannerReq) (*GetSimpleBannerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSimpleBanner", ctx, in)
	ret0, _ := ret[0].(*GetSimpleBannerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSimpleBanner indicates an expected call of GetSimpleBanner.
func (mr *MockTopicChannelTabServerMockRecorder) GetSimpleBanner(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSimpleBanner", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetSimpleBanner), ctx, in)
}

// GetTabByName mocks base method.
func (m *MockTopicChannelTabServer) GetTabByName(ctx context.Context, in *GetTabByNameReq) (*GetTabByNameResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabByName", ctx, in)
	ret0, _ := ret[0].(*GetTabByNameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabByName indicates an expected call of GetTabByName.
func (mr *MockTopicChannelTabServerMockRecorder) GetTabByName(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabByName", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetTabByName), ctx, in)
}

// GetTabByUGameId mocks base method.
func (m *MockTopicChannelTabServer) GetTabByUGameId(ctx context.Context, in *GetTabByUGameIdReq) (*GetTabByUGameIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabByUGameId", ctx, in)
	ret0, _ := ret[0].(*GetTabByUGameIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabByUGameId indicates an expected call of GetTabByUGameId.
func (mr *MockTopicChannelTabServerMockRecorder) GetTabByUGameId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabByUGameId", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetTabByUGameId), ctx, in)
}

// GetTabInfoExt mocks base method.
func (m *MockTopicChannelTabServer) GetTabInfoExt(ctx context.Context, in *GetTabInfoExtReq) (*GetTabInfoExtResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabInfoExt", ctx, in)
	ret0, _ := ret[0].(*GetTabInfoExtResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabInfoExt indicates an expected call of GetTabInfoExt.
func (mr *MockTopicChannelTabServerMockRecorder) GetTabInfoExt(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabInfoExt", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetTabInfoExt), ctx, in)
}

// GetTabQuestionConfig mocks base method.
func (m *MockTopicChannelTabServer) GetTabQuestionConfig(ctx context.Context, in *GetTabQuestionConfigReq) (*GetTabQuestionConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabQuestionConfig", ctx, in)
	ret0, _ := ret[0].(*GetTabQuestionConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabQuestionConfig indicates an expected call of GetTabQuestionConfig.
func (mr *MockTopicChannelTabServerMockRecorder) GetTabQuestionConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabQuestionConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetTabQuestionConfig), ctx, in)
}

// GetTabsByCategoryEnum mocks base method.
func (m *MockTopicChannelTabServer) GetTabsByCategoryEnum(ctx context.Context, in *GetTabsByCategoryEnumReq) (*GetTabsByCategoryEnumResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabsByCategoryEnum", ctx, in)
	ret0, _ := ret[0].(*GetTabsByCategoryEnumResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabsByCategoryEnum indicates an expected call of GetTabsByCategoryEnum.
func (mr *MockTopicChannelTabServerMockRecorder) GetTabsByCategoryEnum(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabsByCategoryEnum", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetTabsByCategoryEnum), ctx, in)
}

// GetTabsByCategoryIds mocks base method.
func (m *MockTopicChannelTabServer) GetTabsByCategoryIds(ctx context.Context, in *GetTabsByCategoryIdsReq) (*GetTabsByCategoryIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabsByCategoryIds", ctx, in)
	ret0, _ := ret[0].(*GetTabsByCategoryIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabsByCategoryIds indicates an expected call of GetTabsByCategoryIds.
func (mr *MockTopicChannelTabServerMockRecorder) GetTabsByCategoryIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabsByCategoryIds", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetTabsByCategoryIds), ctx, in)
}

// GetTabsForManagementOp mocks base method.
func (m *MockTopicChannelTabServer) GetTabsForManagementOp(ctx context.Context, in *GetTabsForManagementOpReq) (*GetTabsForManagementOpResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabsForManagementOp", ctx, in)
	ret0, _ := ret[0].(*GetTabsForManagementOpResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabsForManagementOp indicates an expected call of GetTabsForManagementOp.
func (mr *MockTopicChannelTabServerMockRecorder) GetTabsForManagementOp(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabsForManagementOp", reflect.TypeOf((*MockTopicChannelTabServer)(nil).GetTabsForManagementOp), ctx, in)
}

// HomePageHeadConfig mocks base method.
func (m *MockTopicChannelTabServer) HomePageHeadConfig(ctx context.Context, in *HomePageHeadConfigReq) (*HomePageHeadConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HomePageHeadConfig", ctx, in)
	ret0, _ := ret[0].(*HomePageHeadConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HomePageHeadConfig indicates an expected call of HomePageHeadConfig.
func (mr *MockTopicChannelTabServerMockRecorder) HomePageHeadConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HomePageHeadConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).HomePageHeadConfig), ctx, in)
}

// InsertBlock mocks base method.
func (m *MockTopicChannelTabServer) InsertBlock(ctx context.Context, in *InsertBlockReq) (*InsertBlockResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertBlock", ctx, in)
	ret0, _ := ret[0].(*InsertBlockResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertBlock indicates an expected call of InsertBlock.
func (mr *MockTopicChannelTabServerMockRecorder) InsertBlock(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertBlock", reflect.TypeOf((*MockTopicChannelTabServer)(nil).InsertBlock), ctx, in)
}

// InsertElem mocks base method.
func (m *MockTopicChannelTabServer) InsertElem(ctx context.Context, in *InsertElemReq) (*InsertElemResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertElem", ctx, in)
	ret0, _ := ret[0].(*InsertElemResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertElem indicates an expected call of InsertElem.
func (mr *MockTopicChannelTabServerMockRecorder) InsertElem(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertElem", reflect.TypeOf((*MockTopicChannelTabServer)(nil).InsertElem), ctx, in)
}

// InsertRelationOfEtChannel mocks base method.
func (m *MockTopicChannelTabServer) InsertRelationOfEtChannel(ctx context.Context, in *InsertRelationOfEtChannelReq) (*InsertRelationOfEtChannelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertRelationOfEtChannel", ctx, in)
	ret0, _ := ret[0].(*InsertRelationOfEtChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertRelationOfEtChannel indicates an expected call of InsertRelationOfEtChannel.
func (mr *MockTopicChannelTabServerMockRecorder) InsertRelationOfEtChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertRelationOfEtChannel", reflect.TypeOf((*MockTopicChannelTabServer)(nil).InsertRelationOfEtChannel), ctx, in)
}

// InsertTab mocks base method.
func (m *MockTopicChannelTabServer) InsertTab(ctx context.Context, in *InsertTabReq) (*InsertTabResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertTab", ctx, in)
	ret0, _ := ret[0].(*InsertTabResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertTab indicates an expected call of InsertTab.
func (mr *MockTopicChannelTabServerMockRecorder) InsertTab(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertTab", reflect.TypeOf((*MockTopicChannelTabServer)(nil).InsertTab), ctx, in)
}

// ListOfficialRoomNameConfig mocks base method.
func (m *MockTopicChannelTabServer) ListOfficialRoomNameConfig(ctx context.Context, in *ListOfficialRoomNameConfigReq) (*ListOfficialRoomNameConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListOfficialRoomNameConfig", ctx, in)
	ret0, _ := ret[0].(*ListOfficialRoomNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOfficialRoomNameConfig indicates an expected call of ListOfficialRoomNameConfig.
func (mr *MockTopicChannelTabServerMockRecorder) ListOfficialRoomNameConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOfficialRoomNameConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).ListOfficialRoomNameConfig), ctx, in)
}

// ListReleaseCondition mocks base method.
func (m *MockTopicChannelTabServer) ListReleaseCondition(ctx context.Context, in *ListReleaseConditionReq) (*ListReleaseConditionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListReleaseCondition", ctx, in)
	ret0, _ := ret[0].(*ListReleaseConditionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListReleaseCondition indicates an expected call of ListReleaseCondition.
func (mr *MockTopicChannelTabServerMockRecorder) ListReleaseCondition(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListReleaseCondition", reflect.TypeOf((*MockTopicChannelTabServer)(nil).ListReleaseCondition), ctx, in)
}

// ListReleaseConditionForTT mocks base method.
func (m *MockTopicChannelTabServer) ListReleaseConditionForTT(ctx context.Context, in *ListReleaseConditionForTTReq) (*ListReleaseConditionForTTResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListReleaseConditionForTT", ctx, in)
	ret0, _ := ret[0].(*ListReleaseConditionForTTResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListReleaseConditionForTT indicates an expected call of ListReleaseConditionForTT.
func (mr *MockTopicChannelTabServerMockRecorder) ListReleaseConditionForTT(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListReleaseConditionForTT", reflect.TypeOf((*MockTopicChannelTabServer)(nil).ListReleaseConditionForTT), ctx, in)
}

// ListTabHomePageConfig mocks base method.
func (m *MockTopicChannelTabServer) ListTabHomePageConfig(ctx context.Context, in *ListTabHomePageConfigReq) (*ListTabHomePageConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTabHomePageConfig", ctx, in)
	ret0, _ := ret[0].(*ListTabHomePageConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTabHomePageConfig indicates an expected call of ListTabHomePageConfig.
func (mr *MockTopicChannelTabServerMockRecorder) ListTabHomePageConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTabHomePageConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).ListTabHomePageConfig), ctx, in)
}

// ModifyTabHomePageConfig mocks base method.
func (m *MockTopicChannelTabServer) ModifyTabHomePageConfig(ctx context.Context, in *ModifyTabHomePageConfigReq) (*ModifyTabHomePageConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyTabHomePageConfig", ctx, in)
	ret0, _ := ret[0].(*ModifyTabHomePageConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyTabHomePageConfig indicates an expected call of ModifyTabHomePageConfig.
func (mr *MockTopicChannelTabServerMockRecorder) ModifyTabHomePageConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyTabHomePageConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).ModifyTabHomePageConfig), ctx, in)
}

// ReSortCategoryTitle mocks base method.
func (m *MockTopicChannelTabServer) ReSortCategoryTitle(ctx context.Context, in *ReSortCategoryTitleReq) (*ReSortCategoryTitleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReSortCategoryTitle", ctx, in)
	ret0, _ := ret[0].(*ReSortCategoryTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReSortCategoryTitle indicates an expected call of ReSortCategoryTitle.
func (mr *MockTopicChannelTabServerMockRecorder) ReSortCategoryTitle(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReSortCategoryTitle", reflect.TypeOf((*MockTopicChannelTabServer)(nil).ReSortCategoryTitle), ctx, in)
}

// RearrangeOfficialRoomNameConfig mocks base method.
func (m *MockTopicChannelTabServer) RearrangeOfficialRoomNameConfig(ctx context.Context, in *RearrangeOfficialRoomNameConfigReq) (*RearrangeOfficialRoomNameConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RearrangeOfficialRoomNameConfig", ctx, in)
	ret0, _ := ret[0].(*RearrangeOfficialRoomNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RearrangeOfficialRoomNameConfig indicates an expected call of RearrangeOfficialRoomNameConfig.
func (mr *MockTopicChannelTabServerMockRecorder) RearrangeOfficialRoomNameConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RearrangeOfficialRoomNameConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).RearrangeOfficialRoomNameConfig), ctx, in)
}

// RearrangeTabs mocks base method.
func (m *MockTopicChannelTabServer) RearrangeTabs(ctx context.Context, in *RearrangeTabsReq) (*RearrangeTabsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RearrangeTabs", ctx, in)
	ret0, _ := ret[0].(*RearrangeTabsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RearrangeTabs indicates an expected call of RearrangeTabs.
func (mr *MockTopicChannelTabServerMockRecorder) RearrangeTabs(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RearrangeTabs", reflect.TypeOf((*MockTopicChannelTabServer)(nil).RearrangeTabs), ctx, in)
}

// ResortQuickMatchConfig mocks base method.
func (m *MockTopicChannelTabServer) ResortQuickMatchConfig(ctx context.Context, in *ResortQuickMatchConfigReq) (*ResortQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResortQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*ResortQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResortQuickMatchConfig indicates an expected call of ResortQuickMatchConfig.
func (mr *MockTopicChannelTabServerMockRecorder) ResortQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResortQuickMatchConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).ResortQuickMatchConfig), ctx, in)
}

// ResortTabInfoExt mocks base method.
func (m *MockTopicChannelTabServer) ResortTabInfoExt(ctx context.Context, in *ResortTabInfoExtReq) (*ResortTabInfoExtResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResortTabInfoExt", ctx, in)
	ret0, _ := ret[0].(*ResortTabInfoExtResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResortTabInfoExt indicates an expected call of ResortTabInfoExt.
func (mr *MockTopicChannelTabServerMockRecorder) ResortTabInfoExt(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResortTabInfoExt", reflect.TypeOf((*MockTopicChannelTabServer)(nil).ResortTabInfoExt), ctx, in)
}

// SearchTabs mocks base method.
func (m *MockTopicChannelTabServer) SearchTabs(ctx context.Context, in *SearchTabsReq) (*SearchTabsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchTabs", ctx, in)
	ret0, _ := ret[0].(*SearchTabsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchTabs indicates an expected call of SearchTabs.
func (mr *MockTopicChannelTabServerMockRecorder) SearchTabs(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchTabs", reflect.TypeOf((*MockTopicChannelTabServer)(nil).SearchTabs), ctx, in)
}

// SetBlockInfos mocks base method.
func (m *MockTopicChannelTabServer) SetBlockInfos(ctx context.Context, in *SetBlockInfosReq) (*SetBlockInfosResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBlockInfos", ctx, in)
	ret0, _ := ret[0].(*SetBlockInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetBlockInfos indicates an expected call of SetBlockInfos.
func (mr *MockTopicChannelTabServerMockRecorder) SetBlockInfos(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBlockInfos", reflect.TypeOf((*MockTopicChannelTabServer)(nil).SetBlockInfos), ctx, in)
}

// SetOfficialRoomNameConfig mocks base method.
func (m *MockTopicChannelTabServer) SetOfficialRoomNameConfig(ctx context.Context, in *SetOfficialRoomNameConfigReq) (*SetOfficialRoomNameConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetOfficialRoomNameConfig", ctx, in)
	ret0, _ := ret[0].(*SetOfficialRoomNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetOfficialRoomNameConfig indicates an expected call of SetOfficialRoomNameConfig.
func (mr *MockTopicChannelTabServerMockRecorder) SetOfficialRoomNameConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetOfficialRoomNameConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).SetOfficialRoomNameConfig), ctx, in)
}

// SetReleaseCondition mocks base method.
func (m *MockTopicChannelTabServer) SetReleaseCondition(ctx context.Context, in *SetReleaseConditionReq) (*SetReleaseConditionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetReleaseCondition", ctx, in)
	ret0, _ := ret[0].(*SetReleaseConditionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetReleaseCondition indicates an expected call of SetReleaseCondition.
func (mr *MockTopicChannelTabServerMockRecorder) SetReleaseCondition(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetReleaseCondition", reflect.TypeOf((*MockTopicChannelTabServer)(nil).SetReleaseCondition), ctx, in)
}

// SetRoomNameConfigure mocks base method.
func (m *MockTopicChannelTabServer) SetRoomNameConfigure(ctx context.Context, in *SetRoomNameConfigureReq) (*SetRoomNameConfigureResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRoomNameConfigure", ctx, in)
	ret0, _ := ret[0].(*SetRoomNameConfigureResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetRoomNameConfigure indicates an expected call of SetRoomNameConfigure.
func (mr *MockTopicChannelTabServerMockRecorder) SetRoomNameConfigure(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRoomNameConfigure", reflect.TypeOf((*MockTopicChannelTabServer)(nil).SetRoomNameConfigure), ctx, in)
}

// SetShieldSwitch mocks base method.
func (m *MockTopicChannelTabServer) SetShieldSwitch(ctx context.Context, in *SetShieldSwitchReq) (*SetShieldSwitchResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetShieldSwitch", ctx, in)
	ret0, _ := ret[0].(*SetShieldSwitchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetShieldSwitch indicates an expected call of SetShieldSwitch.
func (mr *MockTopicChannelTabServerMockRecorder) SetShieldSwitch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetShieldSwitch", reflect.TypeOf((*MockTopicChannelTabServer)(nil).SetShieldSwitch), ctx, in)
}

// SetSimpleBanner mocks base method.
func (m *MockTopicChannelTabServer) SetSimpleBanner(ctx context.Context, in *SetSimpleBannerReq) (*SetSimpleBannerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSimpleBanner", ctx, in)
	ret0, _ := ret[0].(*SetSimpleBannerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSimpleBanner indicates an expected call of SetSimpleBanner.
func (mr *MockTopicChannelTabServerMockRecorder) SetSimpleBanner(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSimpleBanner", reflect.TypeOf((*MockTopicChannelTabServer)(nil).SetSimpleBanner), ctx, in)
}

// SortTabHomePageConfig mocks base method.
func (m *MockTopicChannelTabServer) SortTabHomePageConfig(ctx context.Context, in *SortTabHomePageConfigReq) (*SortTabHomePageConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SortTabHomePageConfig", ctx, in)
	ret0, _ := ret[0].(*SortTabHomePageConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SortTabHomePageConfig indicates an expected call of SortTabHomePageConfig.
func (mr *MockTopicChannelTabServerMockRecorder) SortTabHomePageConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SortTabHomePageConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).SortTabHomePageConfig), ctx, in)
}

// Tabs mocks base method.
func (m *MockTopicChannelTabServer) Tabs(ctx context.Context, in *TabsReq) (*TabsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Tabs", ctx, in)
	ret0, _ := ret[0].(*TabsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Tabs indicates an expected call of Tabs.
func (mr *MockTopicChannelTabServerMockRecorder) Tabs(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Tabs", reflect.TypeOf((*MockTopicChannelTabServer)(nil).Tabs), ctx, in)
}

// TabsForTT mocks base method.
func (m *MockTopicChannelTabServer) TabsForTT(ctx context.Context, in *TabsForTTReq) (*TabsForTTResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TabsForTT", ctx, in)
	ret0, _ := ret[0].(*TabsForTTResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TabsForTT indicates an expected call of TabsForTT.
func (mr *MockTopicChannelTabServerMockRecorder) TabsForTT(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TabsForTT", reflect.TypeOf((*MockTopicChannelTabServer)(nil).TabsForTT), ctx, in)
}

// UpdateBlock mocks base method.
func (m *MockTopicChannelTabServer) UpdateBlock(ctx context.Context, in *UpdateBlockReq) (*UpdateBlockResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBlock", ctx, in)
	ret0, _ := ret[0].(*UpdateBlockResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBlock indicates an expected call of UpdateBlock.
func (mr *MockTopicChannelTabServerMockRecorder) UpdateBlock(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBlock", reflect.TypeOf((*MockTopicChannelTabServer)(nil).UpdateBlock), ctx, in)
}

// UpdateBusinessConf mocks base method.
func (m *MockTopicChannelTabServer) UpdateBusinessConf(ctx context.Context, in *UpdateBusinessConfReq) (*UpdateBusinessConfResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBusinessConf", ctx, in)
	ret0, _ := ret[0].(*UpdateBusinessConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBusinessConf indicates an expected call of UpdateBusinessConf.
func (mr *MockTopicChannelTabServerMockRecorder) UpdateBusinessConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBusinessConf", reflect.TypeOf((*MockTopicChannelTabServer)(nil).UpdateBusinessConf), ctx, in)
}

// UpdateCategoryTitle mocks base method.
func (m *MockTopicChannelTabServer) UpdateCategoryTitle(ctx context.Context, in *UpdateCategoryTitleReq) (*UpdateCategoryTitleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCategoryTitle", ctx, in)
	ret0, _ := ret[0].(*UpdateCategoryTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCategoryTitle indicates an expected call of UpdateCategoryTitle.
func (mr *MockTopicChannelTabServerMockRecorder) UpdateCategoryTitle(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCategoryTitle", reflect.TypeOf((*MockTopicChannelTabServer)(nil).UpdateCategoryTitle), ctx, in)
}

// UpdateElem mocks base method.
func (m *MockTopicChannelTabServer) UpdateElem(ctx context.Context, in *UpdateElemReq) (*UpdateElemResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateElem", ctx, in)
	ret0, _ := ret[0].(*UpdateElemResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateElem indicates an expected call of UpdateElem.
func (mr *MockTopicChannelTabServerMockRecorder) UpdateElem(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateElem", reflect.TypeOf((*MockTopicChannelTabServer)(nil).UpdateElem), ctx, in)
}

// UpdateMultilevelTitle mocks base method.
func (m *MockTopicChannelTabServer) UpdateMultilevelTitle(ctx context.Context, in *UpdateMultilevelTitleReq) (*UpdateMultilevelTitleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMultilevelTitle", ctx, in)
	ret0, _ := ret[0].(*UpdateMultilevelTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateMultilevelTitle indicates an expected call of UpdateMultilevelTitle.
func (mr *MockTopicChannelTabServerMockRecorder) UpdateMultilevelTitle(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMultilevelTitle", reflect.TypeOf((*MockTopicChannelTabServer)(nil).UpdateMultilevelTitle), ctx, in)
}

// UpdateQuickMatchConfig mocks base method.
func (m *MockTopicChannelTabServer) UpdateQuickMatchConfig(ctx context.Context, in *UpdateQuickMatchConfigReq) (*UpdateQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*UpdateQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateQuickMatchConfig indicates an expected call of UpdateQuickMatchConfig.
func (mr *MockTopicChannelTabServerMockRecorder) UpdateQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateQuickMatchConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).UpdateQuickMatchConfig), ctx, in)
}

// UpdateTab mocks base method.
func (m *MockTopicChannelTabServer) UpdateTab(ctx context.Context, in *UpdateTabReq) (*UpdateTabResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTab", ctx, in)
	ret0, _ := ret[0].(*UpdateTabResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTab indicates an expected call of UpdateTab.
func (mr *MockTopicChannelTabServerMockRecorder) UpdateTab(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTab", reflect.TypeOf((*MockTopicChannelTabServer)(nil).UpdateTab), ctx, in)
}

// UpdateTabMiniGameId mocks base method.
func (m *MockTopicChannelTabServer) UpdateTabMiniGameId(ctx context.Context, in *UpdateTabMiniGameIdReq) (*UpdateTabMiniGameIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTabMiniGameId", ctx, in)
	ret0, _ := ret[0].(*UpdateTabMiniGameIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTabMiniGameId indicates an expected call of UpdateTabMiniGameId.
func (mr *MockTopicChannelTabServerMockRecorder) UpdateTabMiniGameId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTabMiniGameId", reflect.TypeOf((*MockTopicChannelTabServer)(nil).UpdateTabMiniGameId), ctx, in)
}

// UpdateTabUGameId mocks base method.
func (m *MockTopicChannelTabServer) UpdateTabUGameId(ctx context.Context, in *UpdateTabUGameIdReq) (*UpdateTabUGameIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTabUGameId", ctx, in)
	ret0, _ := ret[0].(*UpdateTabUGameIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTabUGameId indicates an expected call of UpdateTabUGameId.
func (mr *MockTopicChannelTabServerMockRecorder) UpdateTabUGameId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTabUGameId", reflect.TypeOf((*MockTopicChannelTabServer)(nil).UpdateTabUGameId), ctx, in)
}

// UpsertHeadConfig mocks base method.
func (m *MockTopicChannelTabServer) UpsertHeadConfig(ctx context.Context, in *UpsertHeadConfigReq) (*UpsertHeadConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertHeadConfig", ctx, in)
	ret0, _ := ret[0].(*UpsertHeadConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertHeadConfig indicates an expected call of UpsertHeadConfig.
func (mr *MockTopicChannelTabServerMockRecorder) UpsertHeadConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertHeadConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).UpsertHeadConfig), ctx, in)
}

// UpsertNewQuickMatchConfig mocks base method.
func (m *MockTopicChannelTabServer) UpsertNewQuickMatchConfig(ctx context.Context, in *UpsertNewQuickMatchConfigReq) (*UpsertNewQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertNewQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*UpsertNewQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertNewQuickMatchConfig indicates an expected call of UpsertNewQuickMatchConfig.
func (mr *MockTopicChannelTabServerMockRecorder) UpsertNewQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertNewQuickMatchConfig", reflect.TypeOf((*MockTopicChannelTabServer)(nil).UpsertNewQuickMatchConfig), ctx, in)
}

// UpsertTabInfoExt mocks base method.
func (m *MockTopicChannelTabServer) UpsertTabInfoExt(ctx context.Context, in *UpsertTabInfoExtReq) (*UpsertTabInfoExtResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertTabInfoExt", ctx, in)
	ret0, _ := ret[0].(*UpsertTabInfoExtResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertTabInfoExt indicates an expected call of UpsertTabInfoExt.
func (mr *MockTopicChannelTabServerMockRecorder) UpsertTabInfoExt(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertTabInfoExt", reflect.TypeOf((*MockTopicChannelTabServer)(nil).UpsertTabInfoExt), ctx, in)
}
