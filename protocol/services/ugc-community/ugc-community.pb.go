// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/ugc-community/ugc-community.proto

package ugc_community // import "golang.52tt.com/protocol/services/ugc-community"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PostType int32

const (
	PostType_POST_TYPE_UNSPECIFED PostType = 0
	// 文本
	PostType_POST_TYPE_TEXT PostType = 1
	// 图文
	PostType_POST_TYPE_IMAGE_TEXT PostType = 2
)

var PostType_name = map[int32]string{
	0: "POST_TYPE_UNSPECIFED",
	1: "POST_TYPE_TEXT",
	2: "POST_TYPE_IMAGE_TEXT",
}
var PostType_value = map[string]int32{
	"POST_TYPE_UNSPECIFED": 0,
	"POST_TYPE_TEXT":       1,
	"POST_TYPE_IMAGE_TEXT": 2,
}

func (x PostType) String() string {
	return proto.EnumName(PostType_name, int32(x))
}
func (PostType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{0}
}

// 帖子业务类型
type PostBizType int32

const (
	PostBizType_POST_BIZ_TYPE_UNSPECIFIED    PostBizType = 0
	PostBizType_POST_BIZ_TYPE_AIGC_COMMUNITY PostBizType = 1
)

var PostBizType_name = map[int32]string{
	0: "POST_BIZ_TYPE_UNSPECIFIED",
	1: "POST_BIZ_TYPE_AIGC_COMMUNITY",
}
var PostBizType_value = map[string]int32{
	"POST_BIZ_TYPE_UNSPECIFIED":    0,
	"POST_BIZ_TYPE_AIGC_COMMUNITY": 1,
}

func (x PostBizType) String() string {
	return proto.EnumName(PostBizType_name, int32(x))
}
func (PostBizType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{1}
}

// 发帖来源
type PostOrigin int32

const (
	PostOrigin_POST_ORIGIN_UNSPECIFIED PostOrigin = 0
	// AIGC社区-用户发布
	PostOrigin_POST_ORIGIN_AIGC_COMMUNITY_USER PostOrigin = 1
	// AI角色评论区优质评论-官方发布
	PostOrigin_POST_ORIGIN_AI_ROLE_HIGH_QUALITY_COMMENT PostOrigin = 2
	// 活动新发帖来源
	PostOrigin_POST_ORIGIN_AI_ROLE_USER_COMMENT PostOrigin = 3
)

var PostOrigin_name = map[int32]string{
	0: "POST_ORIGIN_UNSPECIFIED",
	1: "POST_ORIGIN_AIGC_COMMUNITY_USER",
	2: "POST_ORIGIN_AI_ROLE_HIGH_QUALITY_COMMENT",
	3: "POST_ORIGIN_AI_ROLE_USER_COMMENT",
}
var PostOrigin_value = map[string]int32{
	"POST_ORIGIN_UNSPECIFIED":                  0,
	"POST_ORIGIN_AIGC_COMMUNITY_USER":          1,
	"POST_ORIGIN_AI_ROLE_HIGH_QUALITY_COMMENT": 2,
	"POST_ORIGIN_AI_ROLE_USER_COMMENT":         3,
}

func (x PostOrigin) String() string {
	return proto.EnumName(PostOrigin_name, int32(x))
}
func (PostOrigin) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{2}
}

type PostState int32

const (
	PostState_POST_STATE_UNSPECIFIED PostState = 0
	// 公开
	PostState_POST_STATE_PUBLIC PostState = 1
	// 匿名
	PostState_POST_STATE_ANONY PostState = 2
)

var PostState_name = map[int32]string{
	0: "POST_STATE_UNSPECIFIED",
	1: "POST_STATE_PUBLIC",
	2: "POST_STATE_ANONY",
}
var PostState_value = map[string]int32{
	"POST_STATE_UNSPECIFIED": 0,
	"POST_STATE_PUBLIC":      1,
	"POST_STATE_ANONY":       2,
}

func (x PostState) String() string {
	return proto.EnumName(PostState_name, int32(x))
}
func (PostState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{3}
}

type AuditResult int32

const (
	AuditResult_AUDIT_RESULT_UNSPECIFIED AuditResult = 0
	AuditResult_AUDIT_RESULT_REVIEW      AuditResult = 1
	AuditResult_AUDIT_RESULT_PASS        AuditResult = 2
	AuditResult_AUDIT_RESULT_REJECT      AuditResult = 3
)

var AuditResult_name = map[int32]string{
	0: "AUDIT_RESULT_UNSPECIFIED",
	1: "AUDIT_RESULT_REVIEW",
	2: "AUDIT_RESULT_PASS",
	3: "AUDIT_RESULT_REJECT",
}
var AuditResult_value = map[string]int32{
	"AUDIT_RESULT_UNSPECIFIED": 0,
	"AUDIT_RESULT_REVIEW":      1,
	"AUDIT_RESULT_PASS":        2,
	"AUDIT_RESULT_REJECT":      3,
}

func (x AuditResult) String() string {
	return proto.EnumName(AuditResult_name, int32(x))
}
func (AuditResult) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{4}
}

type PostStatus int32

const (
	PostStatus_POST_STATUS_UNSPECIFIED PostStatus = 0
	// 已删除
	PostStatus_POST_STATUS_DELETED PostStatus = 1
)

var PostStatus_name = map[int32]string{
	0: "POST_STATUS_UNSPECIFIED",
	1: "POST_STATUS_DELETED",
}
var PostStatus_value = map[string]int32{
	"POST_STATUS_UNSPECIFIED": 0,
	"POST_STATUS_DELETED":     1,
}

func (x PostStatus) String() string {
	return proto.EnumName(PostStatus_name, int32(x))
}
func (PostStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{5}
}

// 话题类型
type TopicType int32

const (
	TopicType_TOPIC_TYPE_UNSPECIFIED TopicType = 0
	// 官方话题
	TopicType_TOPIC_TYPE_OFFICIAL TopicType = 1
)

var TopicType_name = map[int32]string{
	0: "TOPIC_TYPE_UNSPECIFIED",
	1: "TOPIC_TYPE_OFFICIAL",
}
var TopicType_value = map[string]int32{
	"TOPIC_TYPE_UNSPECIFIED": 0,
	"TOPIC_TYPE_OFFICIAL":    1,
}

func (x TopicType) String() string {
	return proto.EnumName(TopicType_name, int32(x))
}
func (TopicType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{6}
}

// -------- 点赞 --------begin
type ObjectType int32

const (
	ObjectType_OBJECT_TYPE_UNSPECIFIED ObjectType = 0
	ObjectType_OBJECT_TYPE_POST        ObjectType = 1
	ObjectType_OBJECT_TYPE_COMMENT     ObjectType = 2
)

var ObjectType_name = map[int32]string{
	0: "OBJECT_TYPE_UNSPECIFIED",
	1: "OBJECT_TYPE_POST",
	2: "OBJECT_TYPE_COMMENT",
}
var ObjectType_value = map[string]int32{
	"OBJECT_TYPE_UNSPECIFIED": 0,
	"OBJECT_TYPE_POST":        1,
	"OBJECT_TYPE_COMMENT":     2,
}

func (x ObjectType) String() string {
	return proto.EnumName(ObjectType_name, int32(x))
}
func (ObjectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{7}
}

type AttitudeAction int32

const (
	AttitudeAction_ATTITUDE_ACTION_UNSPECIFIED AttitudeAction = 0
	AttitudeAction_ATTITUDE_ACTION_LIKE        AttitudeAction = 1
	AttitudeAction_ATTITUDE_ACTION_DISLIKE     AttitudeAction = 2
)

var AttitudeAction_name = map[int32]string{
	0: "ATTITUDE_ACTION_UNSPECIFIED",
	1: "ATTITUDE_ACTION_LIKE",
	2: "ATTITUDE_ACTION_DISLIKE",
}
var AttitudeAction_value = map[string]int32{
	"ATTITUDE_ACTION_UNSPECIFIED": 0,
	"ATTITUDE_ACTION_LIKE":        1,
	"ATTITUDE_ACTION_DISLIKE":     2,
}

func (x AttitudeAction) String() string {
	return proto.EnumName(AttitudeAction_name, int32(x))
}
func (AttitudeAction) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{8}
}

type CommentEntityType int32

const (
	CommentEntityType_COMMENT_ENTITY_TYPE_USER CommentEntityType = 0
	CommentEntityType_COMMENT_ENTITY_TYPE_ROLE CommentEntityType = 1
)

var CommentEntityType_name = map[int32]string{
	0: "COMMENT_ENTITY_TYPE_USER",
	1: "COMMENT_ENTITY_TYPE_ROLE",
}
var CommentEntityType_value = map[string]int32{
	"COMMENT_ENTITY_TYPE_USER": 0,
	"COMMENT_ENTITY_TYPE_ROLE": 1,
}

func (x CommentEntityType) String() string {
	return proto.EnumName(CommentEntityType_name, int32(x))
}
func (CommentEntityType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{9}
}

type CommentOrigin int32

const (
	CommentOrigin_COMMENT_ORIGIN_NORMAL   CommentOrigin = 0
	CommentOrigin_COMMENT_ORIGIN_OFFICIAL CommentOrigin = 1
)

var CommentOrigin_name = map[int32]string{
	0: "COMMENT_ORIGIN_NORMAL",
	1: "COMMENT_ORIGIN_OFFICIAL",
}
var CommentOrigin_value = map[string]int32{
	"COMMENT_ORIGIN_NORMAL":   0,
	"COMMENT_ORIGIN_OFFICIAL": 1,
}

func (x CommentOrigin) String() string {
	return proto.EnumName(CommentOrigin_name, int32(x))
}
func (CommentOrigin) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{10}
}

// -------- 运营后台 --------begin
type AigcPostBizState int32

const (
	AigcPostBizState_AIGC_POST_BIZ_STATE_UNSPECIFIED  AigcPostBizState = 0
	AigcPostBizState_AIGC_POST_BIZ_STATE_NORMAL       AigcPostBizState = 1
	AigcPostBizState_AIGC_POST_BIZ_STATE_SHIELD       AigcPostBizState = 2
	AigcPostBizState_AIGC_POST_BIZ_STATE_FORCE_INSERT AigcPostBizState = 3
)

var AigcPostBizState_name = map[int32]string{
	0: "AIGC_POST_BIZ_STATE_UNSPECIFIED",
	1: "AIGC_POST_BIZ_STATE_NORMAL",
	2: "AIGC_POST_BIZ_STATE_SHIELD",
	3: "AIGC_POST_BIZ_STATE_FORCE_INSERT",
}
var AigcPostBizState_value = map[string]int32{
	"AIGC_POST_BIZ_STATE_UNSPECIFIED":  0,
	"AIGC_POST_BIZ_STATE_NORMAL":       1,
	"AIGC_POST_BIZ_STATE_SHIELD":       2,
	"AIGC_POST_BIZ_STATE_FORCE_INSERT": 3,
}

func (x AigcPostBizState) String() string {
	return proto.EnumName(AigcPostBizState_name, int32(x))
}
func (AigcPostBizState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{11}
}

type BoolFilter int32

const (
	BoolFilter_BOOL_FILTER_UNSPECIFIED BoolFilter = 0
	BoolFilter_BOOL_FILTER_TRUE        BoolFilter = 1
	BoolFilter_BOOL_FILTER_FALSE       BoolFilter = 2
)

var BoolFilter_name = map[int32]string{
	0: "BOOL_FILTER_UNSPECIFIED",
	1: "BOOL_FILTER_TRUE",
	2: "BOOL_FILTER_FALSE",
}
var BoolFilter_value = map[string]int32{
	"BOOL_FILTER_UNSPECIFIED": 0,
	"BOOL_FILTER_TRUE":        1,
	"BOOL_FILTER_FALSE":       2,
}

func (x BoolFilter) String() string {
	return proto.EnumName(BoolFilter_name, int32(x))
}
func (BoolFilter) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{12}
}

type SubjectType int32

const (
	SubjectType_SUBJECT_TYPE_UNSPECIFIED SubjectType = 0
	// 普通主题
	SubjectType_SUBJECT_TYPE_NORMAL SubjectType = 1
	// 精选
	SubjectType_SUBJECT_TYPE_FEATURED SubjectType = 2
)

var SubjectType_name = map[int32]string{
	0: "SUBJECT_TYPE_UNSPECIFIED",
	1: "SUBJECT_TYPE_NORMAL",
	2: "SUBJECT_TYPE_FEATURED",
}
var SubjectType_value = map[string]int32{
	"SUBJECT_TYPE_UNSPECIFIED": 0,
	"SUBJECT_TYPE_NORMAL":      1,
	"SUBJECT_TYPE_FEATURED":    2,
}

func (x SubjectType) String() string {
	return proto.EnumName(SubjectType_name, int32(x))
}
func (SubjectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{13}
}

type Attachment_Type int32

const (
	Attachment_TYPE_UNSPECIFIED Attachment_Type = 0
	// 图片
	Attachment_TYPE_IMAGE Attachment_Type = 1
)

var Attachment_Type_name = map[int32]string{
	0: "TYPE_UNSPECIFIED",
	1: "TYPE_IMAGE",
}
var Attachment_Type_value = map[string]int32{
	"TYPE_UNSPECIFIED": 0,
	"TYPE_IMAGE":       1,
}

func (x Attachment_Type) String() string {
	return proto.EnumName(Attachment_Type_name, int32(x))
}
func (Attachment_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{0, 0}
}

type PostBizData_Format int32

const (
	PostBizData_FORMAT_UNSPECIFIED PostBizData_Format = 0
	// protobuf
	PostBizData_FORMAT_PROTOBUF PostBizData_Format = 1
	// json
	PostBizData_FORMAT_JSON PostBizData_Format = 2
)

var PostBizData_Format_name = map[int32]string{
	0: "FORMAT_UNSPECIFIED",
	1: "FORMAT_PROTOBUF",
	2: "FORMAT_JSON",
}
var PostBizData_Format_value = map[string]int32{
	"FORMAT_UNSPECIFIED": 0,
	"FORMAT_PROTOBUF":    1,
	"FORMAT_JSON":        2,
}

func (x PostBizData_Format) String() string {
	return proto.EnumName(PostBizData_Format_name, int32(x))
}
func (PostBizData_Format) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{1, 0}
}

type AigcCommunityPost_ChatRecord_Sender int32

const (
	AigcCommunityPost_ChatRecord_SENDER_UNSPECIFIED AigcCommunityPost_ChatRecord_Sender = 0
	AigcCommunityPost_ChatRecord_SENDER_ROLE        AigcCommunityPost_ChatRecord_Sender = 1
	AigcCommunityPost_ChatRecord_SENDER_USER        AigcCommunityPost_ChatRecord_Sender = 2
)

var AigcCommunityPost_ChatRecord_Sender_name = map[int32]string{
	0: "SENDER_UNSPECIFIED",
	1: "SENDER_ROLE",
	2: "SENDER_USER",
}
var AigcCommunityPost_ChatRecord_Sender_value = map[string]int32{
	"SENDER_UNSPECIFIED": 0,
	"SENDER_ROLE":        1,
	"SENDER_USER":        2,
}

func (x AigcCommunityPost_ChatRecord_Sender) String() string {
	return proto.EnumName(AigcCommunityPost_ChatRecord_Sender_name, int32(x))
}
func (AigcCommunityPost_ChatRecord_Sender) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{3, 0, 0}
}

type BatchDeletePostRequest_Source int32

const (
	BatchDeletePostRequest_SOURCE_UNSPECIFIED BatchDeletePostRequest_Source = 0
	// 用户操作
	BatchDeletePostRequest_SOURCE_USER BatchDeletePostRequest_Source = 1
	// 官方操作
	BatchDeletePostRequest_SOURCE_OFFICIAL BatchDeletePostRequest_Source = 2
	// 审核不通过删除
	BatchDeletePostRequest_SOURCE_AUDIT_REJECT BatchDeletePostRequest_Source = 3
	// 超时未审核删除
	BatchDeletePostRequest_SOURCE_AUDIT_TIMEOUT BatchDeletePostRequest_Source = 4
)

var BatchDeletePostRequest_Source_name = map[int32]string{
	0: "SOURCE_UNSPECIFIED",
	1: "SOURCE_USER",
	2: "SOURCE_OFFICIAL",
	3: "SOURCE_AUDIT_REJECT",
	4: "SOURCE_AUDIT_TIMEOUT",
}
var BatchDeletePostRequest_Source_value = map[string]int32{
	"SOURCE_UNSPECIFIED":   0,
	"SOURCE_USER":          1,
	"SOURCE_OFFICIAL":      2,
	"SOURCE_AUDIT_REJECT":  3,
	"SOURCE_AUDIT_TIMEOUT": 4,
}

func (x BatchDeletePostRequest_Source) String() string {
	return proto.EnumName(BatchDeletePostRequest_Source_name, int32(x))
}
func (BatchDeletePostRequest_Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{11, 0}
}

type UpdateAigcBizPostRequest_Action int32

const (
	UpdateAigcBizPostRequest_ACTION_UNSPECIFIED UpdateAigcBizPostRequest_Action = 0
	UpdateAigcBizPostRequest_ACTION_ADD         UpdateAigcBizPostRequest_Action = 1
	UpdateAigcBizPostRequest_ACTION_DELETE      UpdateAigcBizPostRequest_Action = 2
)

var UpdateAigcBizPostRequest_Action_name = map[int32]string{
	0: "ACTION_UNSPECIFIED",
	1: "ACTION_ADD",
	2: "ACTION_DELETE",
}
var UpdateAigcBizPostRequest_Action_value = map[string]int32{
	"ACTION_UNSPECIFIED": 0,
	"ACTION_ADD":         1,
	"ACTION_DELETE":      2,
}

func (x UpdateAigcBizPostRequest_Action) String() string {
	return proto.EnumName(UpdateAigcBizPostRequest_Action_name, int32(x))
}
func (UpdateAigcBizPostRequest_Action) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{52, 0}
}

type UpsertSubjectTabRequest_OpType int32

const (
	UpsertSubjectTabRequest_OP_TYPE_UNSPECIFIED UpsertSubjectTabRequest_OpType = 0
	UpsertSubjectTabRequest_OP_TYPE_ADD         UpsertSubjectTabRequest_OpType = 1
	UpsertSubjectTabRequest_OP_TYPE_UPDATE      UpsertSubjectTabRequest_OpType = 2
)

var UpsertSubjectTabRequest_OpType_name = map[int32]string{
	0: "OP_TYPE_UNSPECIFIED",
	1: "OP_TYPE_ADD",
	2: "OP_TYPE_UPDATE",
}
var UpsertSubjectTabRequest_OpType_value = map[string]int32{
	"OP_TYPE_UNSPECIFIED": 0,
	"OP_TYPE_ADD":         1,
	"OP_TYPE_UPDATE":      2,
}

func (x UpsertSubjectTabRequest_OpType) String() string {
	return proto.EnumName(UpsertSubjectTabRequest_OpType_name, int32(x))
}
func (UpsertSubjectTabRequest_OpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{55, 0}
}

// 附件
type Attachment struct {
	// 附件类型
	Type Attachment_Type `protobuf:"varint,1,opt,name=type,proto3,enum=ugc_community.Attachment_Type" json:"type,omitempty"`
	// obs key
	Key string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	// obs 链接
	Url                  string   `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Attachment) Reset()         { *m = Attachment{} }
func (m *Attachment) String() string { return proto.CompactTextString(m) }
func (*Attachment) ProtoMessage()    {}
func (*Attachment) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{0}
}
func (m *Attachment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Attachment.Unmarshal(m, b)
}
func (m *Attachment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Attachment.Marshal(b, m, deterministic)
}
func (dst *Attachment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Attachment.Merge(dst, src)
}
func (m *Attachment) XXX_Size() int {
	return xxx_messageInfo_Attachment.Size(m)
}
func (m *Attachment) XXX_DiscardUnknown() {
	xxx_messageInfo_Attachment.DiscardUnknown(m)
}

var xxx_messageInfo_Attachment proto.InternalMessageInfo

func (m *Attachment) GetType() Attachment_Type {
	if m != nil {
		return m.Type
	}
	return Attachment_TYPE_UNSPECIFIED
}

func (m *Attachment) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *Attachment) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type PostBizData struct {
	// 业务类型
	Type PostBizType `protobuf:"varint,1,opt,name=type,proto3,enum=ugc_community.PostBizType" json:"type,omitempty"`
	// 数据格式
	Format PostBizData_Format `protobuf:"varint,2,opt,name=format,proto3,enum=ugc_community.PostBizData_Format" json:"format,omitempty"`
	// 内容
	Bin                  []byte   `protobuf:"bytes,3,opt,name=bin,proto3" json:"bin,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostBizData) Reset()         { *m = PostBizData{} }
func (m *PostBizData) String() string { return proto.CompactTextString(m) }
func (*PostBizData) ProtoMessage()    {}
func (*PostBizData) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{1}
}
func (m *PostBizData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostBizData.Unmarshal(m, b)
}
func (m *PostBizData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostBizData.Marshal(b, m, deterministic)
}
func (dst *PostBizData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostBizData.Merge(dst, src)
}
func (m *PostBizData) XXX_Size() int {
	return xxx_messageInfo_PostBizData.Size(m)
}
func (m *PostBizData) XXX_DiscardUnknown() {
	xxx_messageInfo_PostBizData.DiscardUnknown(m)
}

var xxx_messageInfo_PostBizData proto.InternalMessageInfo

func (m *PostBizData) GetType() PostBizType {
	if m != nil {
		return m.Type
	}
	return PostBizType_POST_BIZ_TYPE_UNSPECIFIED
}

func (m *PostBizData) GetFormat() PostBizData_Format {
	if m != nil {
		return m.Format
	}
	return PostBizData_FORMAT_UNSPECIFIED
}

func (m *PostBizData) GetBin() []byte {
	if m != nil {
		return m.Bin
	}
	return nil
}

type Post struct {
	Id                   string        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedAt            int64         `protobuf:"varint,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt            int64         `protobuf:"varint,3,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Uid                  uint32        `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Type                 PostType      `protobuf:"varint,5,opt,name=type,proto3,enum=ugc_community.PostType" json:"type,omitempty"`
	State                PostState     `protobuf:"varint,6,opt,name=state,proto3,enum=ugc_community.PostState" json:"state,omitempty"`
	Origin               PostOrigin    `protobuf:"varint,7,opt,name=origin,proto3,enum=ugc_community.PostOrigin" json:"origin,omitempty"`
	BizData              *PostBizData  `protobuf:"bytes,8,opt,name=biz_data,json=bizData,proto3" json:"biz_data,omitempty"`
	Content              string        `protobuf:"bytes,10,opt,name=content,proto3" json:"content,omitempty"`
	Attachments          []*Attachment `protobuf:"bytes,11,rep,name=attachments,proto3" json:"attachments,omitempty"`
	AuditAt              int64         `protobuf:"varint,12,opt,name=audit_at,json=auditAt,proto3" json:"audit_at,omitempty"`
	AuditResult          AuditResult   `protobuf:"varint,13,opt,name=audit_result,json=auditResult,proto3,enum=ugc_community.AuditResult" json:"audit_result,omitempty"`
	CommentCount         uint32        `protobuf:"varint,14,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	AttitudeCount        uint32        `protobuf:"varint,15,opt,name=attitude_count,json=attitudeCount,proto3" json:"attitude_count,omitempty"`
	Status               PostStatus    `protobuf:"varint,16,opt,name=status,proto3,enum=ugc_community.PostStatus" json:"status,omitempty"`
	TopicIdList          []string      `protobuf:"bytes,17,rep,name=topic_id_list,json=topicIdList,proto3" json:"topic_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *Post) Reset()         { *m = Post{} }
func (m *Post) String() string { return proto.CompactTextString(m) }
func (*Post) ProtoMessage()    {}
func (*Post) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{2}
}
func (m *Post) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Post.Unmarshal(m, b)
}
func (m *Post) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Post.Marshal(b, m, deterministic)
}
func (dst *Post) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Post.Merge(dst, src)
}
func (m *Post) XXX_Size() int {
	return xxx_messageInfo_Post.Size(m)
}
func (m *Post) XXX_DiscardUnknown() {
	xxx_messageInfo_Post.DiscardUnknown(m)
}

var xxx_messageInfo_Post proto.InternalMessageInfo

func (m *Post) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *Post) GetCreatedAt() int64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *Post) GetUpdatedAt() int64 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *Post) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Post) GetType() PostType {
	if m != nil {
		return m.Type
	}
	return PostType_POST_TYPE_UNSPECIFED
}

func (m *Post) GetState() PostState {
	if m != nil {
		return m.State
	}
	return PostState_POST_STATE_UNSPECIFIED
}

func (m *Post) GetOrigin() PostOrigin {
	if m != nil {
		return m.Origin
	}
	return PostOrigin_POST_ORIGIN_UNSPECIFIED
}

func (m *Post) GetBizData() *PostBizData {
	if m != nil {
		return m.BizData
	}
	return nil
}

func (m *Post) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *Post) GetAttachments() []*Attachment {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *Post) GetAuditAt() int64 {
	if m != nil {
		return m.AuditAt
	}
	return 0
}

func (m *Post) GetAuditResult() AuditResult {
	if m != nil {
		return m.AuditResult
	}
	return AuditResult_AUDIT_RESULT_UNSPECIFIED
}

func (m *Post) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *Post) GetAttitudeCount() uint32 {
	if m != nil {
		return m.AttitudeCount
	}
	return 0
}

func (m *Post) GetStatus() PostStatus {
	if m != nil {
		return m.Status
	}
	return PostStatus_POST_STATUS_UNSPECIFIED
}

func (m *Post) GetTopicIdList() []string {
	if m != nil {
		return m.TopicIdList
	}
	return nil
}

// BizType = POST_BIZ_TYPE_AIGC_COMMUNITY时 按这个结构unmarshal
type AigcCommunityPost struct {
	// 角色ID
	RoleId uint32 `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 与角色的聊天记录
	ChatRecords          []*AigcCommunityPost_ChatRecord `protobuf:"bytes,2,rep,name=chat_records,json=chatRecords,proto3" json:"chat_records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *AigcCommunityPost) Reset()         { *m = AigcCommunityPost{} }
func (m *AigcCommunityPost) String() string { return proto.CompactTextString(m) }
func (*AigcCommunityPost) ProtoMessage()    {}
func (*AigcCommunityPost) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{3}
}
func (m *AigcCommunityPost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AigcCommunityPost.Unmarshal(m, b)
}
func (m *AigcCommunityPost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AigcCommunityPost.Marshal(b, m, deterministic)
}
func (dst *AigcCommunityPost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AigcCommunityPost.Merge(dst, src)
}
func (m *AigcCommunityPost) XXX_Size() int {
	return xxx_messageInfo_AigcCommunityPost.Size(m)
}
func (m *AigcCommunityPost) XXX_DiscardUnknown() {
	xxx_messageInfo_AigcCommunityPost.DiscardUnknown(m)
}

var xxx_messageInfo_AigcCommunityPost proto.InternalMessageInfo

func (m *AigcCommunityPost) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *AigcCommunityPost) GetChatRecords() []*AigcCommunityPost_ChatRecord {
	if m != nil {
		return m.ChatRecords
	}
	return nil
}

type AigcCommunityPost_ChatRecord struct {
	// 消息发送方
	Sender AigcCommunityPost_ChatRecord_Sender `protobuf:"varint,1,opt,name=sender,proto3,enum=ugc_community.AigcCommunityPost_ChatRecord_Sender" json:"sender,omitempty"`
	// 消息内容
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AigcCommunityPost_ChatRecord) Reset()         { *m = AigcCommunityPost_ChatRecord{} }
func (m *AigcCommunityPost_ChatRecord) String() string { return proto.CompactTextString(m) }
func (*AigcCommunityPost_ChatRecord) ProtoMessage()    {}
func (*AigcCommunityPost_ChatRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{3, 0}
}
func (m *AigcCommunityPost_ChatRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AigcCommunityPost_ChatRecord.Unmarshal(m, b)
}
func (m *AigcCommunityPost_ChatRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AigcCommunityPost_ChatRecord.Marshal(b, m, deterministic)
}
func (dst *AigcCommunityPost_ChatRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AigcCommunityPost_ChatRecord.Merge(dst, src)
}
func (m *AigcCommunityPost_ChatRecord) XXX_Size() int {
	return xxx_messageInfo_AigcCommunityPost_ChatRecord.Size(m)
}
func (m *AigcCommunityPost_ChatRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_AigcCommunityPost_ChatRecord.DiscardUnknown(m)
}

var xxx_messageInfo_AigcCommunityPost_ChatRecord proto.InternalMessageInfo

func (m *AigcCommunityPost_ChatRecord) GetSender() AigcCommunityPost_ChatRecord_Sender {
	if m != nil {
		return m.Sender
	}
	return AigcCommunityPost_ChatRecord_SENDER_UNSPECIFIED
}

func (m *AigcCommunityPost_ChatRecord) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type PublishPostGuideNotify struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 角色ID
	RoleId uint32 `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 发帖引导任务token
	TaskToken string `protobuf:"bytes,3,opt,name=task_token,json=taskToken,proto3" json:"task_token,omitempty"`
	// 奖励句数
	RewardSentenceCnt    uint32   `protobuf:"varint,4,opt,name=reward_sentence_cnt,json=rewardSentenceCnt,proto3" json:"reward_sentence_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PublishPostGuideNotify) Reset()         { *m = PublishPostGuideNotify{} }
func (m *PublishPostGuideNotify) String() string { return proto.CompactTextString(m) }
func (*PublishPostGuideNotify) ProtoMessage()    {}
func (*PublishPostGuideNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{4}
}
func (m *PublishPostGuideNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishPostGuideNotify.Unmarshal(m, b)
}
func (m *PublishPostGuideNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishPostGuideNotify.Marshal(b, m, deterministic)
}
func (dst *PublishPostGuideNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishPostGuideNotify.Merge(dst, src)
}
func (m *PublishPostGuideNotify) XXX_Size() int {
	return xxx_messageInfo_PublishPostGuideNotify.Size(m)
}
func (m *PublishPostGuideNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishPostGuideNotify.DiscardUnknown(m)
}

var xxx_messageInfo_PublishPostGuideNotify proto.InternalMessageInfo

func (m *PublishPostGuideNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PublishPostGuideNotify) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *PublishPostGuideNotify) GetTaskToken() string {
	if m != nil {
		return m.TaskToken
	}
	return ""
}

func (m *PublishPostGuideNotify) GetRewardSentenceCnt() uint32 {
	if m != nil {
		return m.RewardSentenceCnt
	}
	return 0
}

type PostTaskFinishNotify struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 角色ID
	RoleId uint32 `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 标题
	Title string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// 内容
	Content              string   `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostTaskFinishNotify) Reset()         { *m = PostTaskFinishNotify{} }
func (m *PostTaskFinishNotify) String() string { return proto.CompactTextString(m) }
func (*PostTaskFinishNotify) ProtoMessage()    {}
func (*PostTaskFinishNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{5}
}
func (m *PostTaskFinishNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostTaskFinishNotify.Unmarshal(m, b)
}
func (m *PostTaskFinishNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostTaskFinishNotify.Marshal(b, m, deterministic)
}
func (dst *PostTaskFinishNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostTaskFinishNotify.Merge(dst, src)
}
func (m *PostTaskFinishNotify) XXX_Size() int {
	return xxx_messageInfo_PostTaskFinishNotify.Size(m)
}
func (m *PostTaskFinishNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_PostTaskFinishNotify.DiscardUnknown(m)
}

var xxx_messageInfo_PostTaskFinishNotify proto.InternalMessageInfo

func (m *PostTaskFinishNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PostTaskFinishNotify) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *PostTaskFinishNotify) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PostTaskFinishNotify) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type TopicInfo struct {
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 话题类型
	Type TopicType `protobuf:"varint,2,opt,name=type,proto3,enum=ugc_community.TopicType" json:"type,omitempty"`
	// 创建时间戳(秒)
	CreatedAt int64 `protobuf:"varint,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 修改时间戳(秒)
	UpdatedAt int64 `protobuf:"varint,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// 话题名称
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// 话题排序
	Sort                 uint32   `protobuf:"varint,6,opt,name=sort,proto3" json:"sort,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicInfo) Reset()         { *m = TopicInfo{} }
func (m *TopicInfo) String() string { return proto.CompactTextString(m) }
func (*TopicInfo) ProtoMessage()    {}
func (*TopicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{6}
}
func (m *TopicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicInfo.Unmarshal(m, b)
}
func (m *TopicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicInfo.Marshal(b, m, deterministic)
}
func (dst *TopicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicInfo.Merge(dst, src)
}
func (m *TopicInfo) XXX_Size() int {
	return xxx_messageInfo_TopicInfo.Size(m)
}
func (m *TopicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopicInfo proto.InternalMessageInfo

func (m *TopicInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *TopicInfo) GetType() TopicType {
	if m != nil {
		return m.Type
	}
	return TopicType_TOPIC_TYPE_UNSPECIFIED
}

func (m *TopicInfo) GetCreatedAt() int64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *TopicInfo) GetUpdatedAt() int64 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *TopicInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TopicInfo) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

type CreatePostRequest struct {
	Info                 *CreatePostRequest_Post `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *CreatePostRequest) Reset()         { *m = CreatePostRequest{} }
func (m *CreatePostRequest) String() string { return proto.CompactTextString(m) }
func (*CreatePostRequest) ProtoMessage()    {}
func (*CreatePostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{7}
}
func (m *CreatePostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePostRequest.Unmarshal(m, b)
}
func (m *CreatePostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePostRequest.Marshal(b, m, deterministic)
}
func (dst *CreatePostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePostRequest.Merge(dst, src)
}
func (m *CreatePostRequest) XXX_Size() int {
	return xxx_messageInfo_CreatePostRequest.Size(m)
}
func (m *CreatePostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePostRequest proto.InternalMessageInfo

func (m *CreatePostRequest) GetInfo() *CreatePostRequest_Post {
	if m != nil {
		return m.Info
	}
	return nil
}

type CreatePostRequest_Post struct {
	Id                   string        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                 PostType      `protobuf:"varint,2,opt,name=type,proto3,enum=ugc_community.PostType" json:"type,omitempty"`
	State                PostState     `protobuf:"varint,3,opt,name=state,proto3,enum=ugc_community.PostState" json:"state,omitempty"`
	Origin               PostOrigin    `protobuf:"varint,4,opt,name=origin,proto3,enum=ugc_community.PostOrigin" json:"origin,omitempty"`
	Content              string        `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	Attachments          []*Attachment `protobuf:"bytes,6,rep,name=attachments,proto3" json:"attachments,omitempty"`
	BizData              *PostBizData  `protobuf:"bytes,8,opt,name=biz_data,json=bizData,proto3" json:"biz_data,omitempty"`
	Uid                  uint32        `protobuf:"varint,9,opt,name=uid,proto3" json:"uid,omitempty"`
	AuditAt              int64         `protobuf:"varint,10,opt,name=audit_at,json=auditAt,proto3" json:"audit_at,omitempty"`
	AuditResult          AuditResult   `protobuf:"varint,11,opt,name=audit_result,json=auditResult,proto3,enum=ugc_community.AuditResult" json:"audit_result,omitempty"`
	TopicIdList          []string      `protobuf:"bytes,12,rep,name=topic_id_list,json=topicIdList,proto3" json:"topic_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CreatePostRequest_Post) Reset()         { *m = CreatePostRequest_Post{} }
func (m *CreatePostRequest_Post) String() string { return proto.CompactTextString(m) }
func (*CreatePostRequest_Post) ProtoMessage()    {}
func (*CreatePostRequest_Post) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{7, 0}
}
func (m *CreatePostRequest_Post) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePostRequest_Post.Unmarshal(m, b)
}
func (m *CreatePostRequest_Post) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePostRequest_Post.Marshal(b, m, deterministic)
}
func (dst *CreatePostRequest_Post) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePostRequest_Post.Merge(dst, src)
}
func (m *CreatePostRequest_Post) XXX_Size() int {
	return xxx_messageInfo_CreatePostRequest_Post.Size(m)
}
func (m *CreatePostRequest_Post) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePostRequest_Post.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePostRequest_Post proto.InternalMessageInfo

func (m *CreatePostRequest_Post) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CreatePostRequest_Post) GetType() PostType {
	if m != nil {
		return m.Type
	}
	return PostType_POST_TYPE_UNSPECIFED
}

func (m *CreatePostRequest_Post) GetState() PostState {
	if m != nil {
		return m.State
	}
	return PostState_POST_STATE_UNSPECIFIED
}

func (m *CreatePostRequest_Post) GetOrigin() PostOrigin {
	if m != nil {
		return m.Origin
	}
	return PostOrigin_POST_ORIGIN_UNSPECIFIED
}

func (m *CreatePostRequest_Post) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CreatePostRequest_Post) GetAttachments() []*Attachment {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *CreatePostRequest_Post) GetBizData() *PostBizData {
	if m != nil {
		return m.BizData
	}
	return nil
}

func (m *CreatePostRequest_Post) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CreatePostRequest_Post) GetAuditAt() int64 {
	if m != nil {
		return m.AuditAt
	}
	return 0
}

func (m *CreatePostRequest_Post) GetAuditResult() AuditResult {
	if m != nil {
		return m.AuditResult
	}
	return AuditResult_AUDIT_RESULT_UNSPECIFIED
}

func (m *CreatePostRequest_Post) GetTopicIdList() []string {
	if m != nil {
		return m.TopicIdList
	}
	return nil
}

type CreatePostResponse struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreatePostResponse) Reset()         { *m = CreatePostResponse{} }
func (m *CreatePostResponse) String() string { return proto.CompactTextString(m) }
func (*CreatePostResponse) ProtoMessage()    {}
func (*CreatePostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{8}
}
func (m *CreatePostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePostResponse.Unmarshal(m, b)
}
func (m *CreatePostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePostResponse.Marshal(b, m, deterministic)
}
func (dst *CreatePostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePostResponse.Merge(dst, src)
}
func (m *CreatePostResponse) XXX_Size() int {
	return xxx_messageInfo_CreatePostResponse.Size(m)
}
func (m *CreatePostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePostResponse proto.InternalMessageInfo

func (m *CreatePostResponse) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type UpdatePostAuditResultRequest struct {
	Id                   string      `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	AuditResult          AuditResult `protobuf:"varint,2,opt,name=audit_result,json=auditResult,proto3,enum=ugc_community.AuditResult" json:"audit_result,omitempty"`
	IsShield             bool        `protobuf:"varint,3,opt,name=is_shield,json=isShield,proto3" json:"is_shield,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpdatePostAuditResultRequest) Reset()         { *m = UpdatePostAuditResultRequest{} }
func (m *UpdatePostAuditResultRequest) String() string { return proto.CompactTextString(m) }
func (*UpdatePostAuditResultRequest) ProtoMessage()    {}
func (*UpdatePostAuditResultRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{9}
}
func (m *UpdatePostAuditResultRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostAuditResultRequest.Unmarshal(m, b)
}
func (m *UpdatePostAuditResultRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostAuditResultRequest.Marshal(b, m, deterministic)
}
func (dst *UpdatePostAuditResultRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostAuditResultRequest.Merge(dst, src)
}
func (m *UpdatePostAuditResultRequest) XXX_Size() int {
	return xxx_messageInfo_UpdatePostAuditResultRequest.Size(m)
}
func (m *UpdatePostAuditResultRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostAuditResultRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostAuditResultRequest proto.InternalMessageInfo

func (m *UpdatePostAuditResultRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpdatePostAuditResultRequest) GetAuditResult() AuditResult {
	if m != nil {
		return m.AuditResult
	}
	return AuditResult_AUDIT_RESULT_UNSPECIFIED
}

func (m *UpdatePostAuditResultRequest) GetIsShield() bool {
	if m != nil {
		return m.IsShield
	}
	return false
}

type UpdatePostAuditResultResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePostAuditResultResponse) Reset()         { *m = UpdatePostAuditResultResponse{} }
func (m *UpdatePostAuditResultResponse) String() string { return proto.CompactTextString(m) }
func (*UpdatePostAuditResultResponse) ProtoMessage()    {}
func (*UpdatePostAuditResultResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{10}
}
func (m *UpdatePostAuditResultResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePostAuditResultResponse.Unmarshal(m, b)
}
func (m *UpdatePostAuditResultResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePostAuditResultResponse.Marshal(b, m, deterministic)
}
func (dst *UpdatePostAuditResultResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePostAuditResultResponse.Merge(dst, src)
}
func (m *UpdatePostAuditResultResponse) XXX_Size() int {
	return xxx_messageInfo_UpdatePostAuditResultResponse.Size(m)
}
func (m *UpdatePostAuditResultResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePostAuditResultResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePostAuditResultResponse proto.InternalMessageInfo

type BatchDeletePostRequest struct {
	IdList               []string                      `protobuf:"bytes,1,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	Source               BatchDeletePostRequest_Source `protobuf:"varint,2,opt,name=source,proto3,enum=ugc_community.BatchDeletePostRequest_Source" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *BatchDeletePostRequest) Reset()         { *m = BatchDeletePostRequest{} }
func (m *BatchDeletePostRequest) String() string { return proto.CompactTextString(m) }
func (*BatchDeletePostRequest) ProtoMessage()    {}
func (*BatchDeletePostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{11}
}
func (m *BatchDeletePostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDeletePostRequest.Unmarshal(m, b)
}
func (m *BatchDeletePostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDeletePostRequest.Marshal(b, m, deterministic)
}
func (dst *BatchDeletePostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDeletePostRequest.Merge(dst, src)
}
func (m *BatchDeletePostRequest) XXX_Size() int {
	return xxx_messageInfo_BatchDeletePostRequest.Size(m)
}
func (m *BatchDeletePostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDeletePostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDeletePostRequest proto.InternalMessageInfo

func (m *BatchDeletePostRequest) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

func (m *BatchDeletePostRequest) GetSource() BatchDeletePostRequest_Source {
	if m != nil {
		return m.Source
	}
	return BatchDeletePostRequest_SOURCE_UNSPECIFIED
}

type BatchDeletePostResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDeletePostResponse) Reset()         { *m = BatchDeletePostResponse{} }
func (m *BatchDeletePostResponse) String() string { return proto.CompactTextString(m) }
func (*BatchDeletePostResponse) ProtoMessage()    {}
func (*BatchDeletePostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{12}
}
func (m *BatchDeletePostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDeletePostResponse.Unmarshal(m, b)
}
func (m *BatchDeletePostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDeletePostResponse.Marshal(b, m, deterministic)
}
func (dst *BatchDeletePostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDeletePostResponse.Merge(dst, src)
}
func (m *BatchDeletePostResponse) XXX_Size() int {
	return xxx_messageInfo_BatchDeletePostResponse.Size(m)
}
func (m *BatchDeletePostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDeletePostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDeletePostResponse proto.InternalMessageInfo

type GetPostRequest struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostRequest) Reset()         { *m = GetPostRequest{} }
func (m *GetPostRequest) String() string { return proto.CompactTextString(m) }
func (*GetPostRequest) ProtoMessage()    {}
func (*GetPostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{13}
}
func (m *GetPostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostRequest.Unmarshal(m, b)
}
func (m *GetPostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostRequest.Marshal(b, m, deterministic)
}
func (dst *GetPostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostRequest.Merge(dst, src)
}
func (m *GetPostRequest) XXX_Size() int {
	return xxx_messageInfo_GetPostRequest.Size(m)
}
func (m *GetPostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostRequest proto.InternalMessageInfo

func (m *GetPostRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type GetPostResponse struct {
	Info                 *Post    `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostResponse) Reset()         { *m = GetPostResponse{} }
func (m *GetPostResponse) String() string { return proto.CompactTextString(m) }
func (*GetPostResponse) ProtoMessage()    {}
func (*GetPostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{14}
}
func (m *GetPostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostResponse.Unmarshal(m, b)
}
func (m *GetPostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostResponse.Marshal(b, m, deterministic)
}
func (dst *GetPostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostResponse.Merge(dst, src)
}
func (m *GetPostResponse) XXX_Size() int {
	return xxx_messageInfo_GetPostResponse.Size(m)
}
func (m *GetPostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostResponse proto.InternalMessageInfo

func (m *GetPostResponse) GetInfo() *Post {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetPostListRequest struct {
	IdList               []string `protobuf:"bytes,1,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostListRequest) Reset()         { *m = GetPostListRequest{} }
func (m *GetPostListRequest) String() string { return proto.CompactTextString(m) }
func (*GetPostListRequest) ProtoMessage()    {}
func (*GetPostListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{15}
}
func (m *GetPostListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostListRequest.Unmarshal(m, b)
}
func (m *GetPostListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostListRequest.Marshal(b, m, deterministic)
}
func (dst *GetPostListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostListRequest.Merge(dst, src)
}
func (m *GetPostListRequest) XXX_Size() int {
	return xxx_messageInfo_GetPostListRequest.Size(m)
}
func (m *GetPostListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostListRequest proto.InternalMessageInfo

func (m *GetPostListRequest) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

type GetPostListResponse struct {
	List                 []*Post  `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostListResponse) Reset()         { *m = GetPostListResponse{} }
func (m *GetPostListResponse) String() string { return proto.CompactTextString(m) }
func (*GetPostListResponse) ProtoMessage()    {}
func (*GetPostListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{16}
}
func (m *GetPostListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostListResponse.Unmarshal(m, b)
}
func (m *GetPostListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostListResponse.Marshal(b, m, deterministic)
}
func (dst *GetPostListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostListResponse.Merge(dst, src)
}
func (m *GetPostListResponse) XXX_Size() int {
	return xxx_messageInfo_GetPostListResponse.Size(m)
}
func (m *GetPostListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostListResponse proto.InternalMessageInfo

func (m *GetPostListResponse) GetList() []*Post {
	if m != nil {
		return m.List
	}
	return nil
}

type GetPostListInAuditTimeRangeRequest struct {
	// 时间范围
	Begin int64 `protobuf:"varint,1,opt,name=begin,proto3" json:"begin,omitempty"`
	End   int64 `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
	// 查询>id
	Id                   string   `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostListInAuditTimeRangeRequest) Reset()         { *m = GetPostListInAuditTimeRangeRequest{} }
func (m *GetPostListInAuditTimeRangeRequest) String() string { return proto.CompactTextString(m) }
func (*GetPostListInAuditTimeRangeRequest) ProtoMessage()    {}
func (*GetPostListInAuditTimeRangeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{17}
}
func (m *GetPostListInAuditTimeRangeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostListInAuditTimeRangeRequest.Unmarshal(m, b)
}
func (m *GetPostListInAuditTimeRangeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostListInAuditTimeRangeRequest.Marshal(b, m, deterministic)
}
func (dst *GetPostListInAuditTimeRangeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostListInAuditTimeRangeRequest.Merge(dst, src)
}
func (m *GetPostListInAuditTimeRangeRequest) XXX_Size() int {
	return xxx_messageInfo_GetPostListInAuditTimeRangeRequest.Size(m)
}
func (m *GetPostListInAuditTimeRangeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostListInAuditTimeRangeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostListInAuditTimeRangeRequest proto.InternalMessageInfo

func (m *GetPostListInAuditTimeRangeRequest) GetBegin() int64 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetPostListInAuditTimeRangeRequest) GetEnd() int64 {
	if m != nil {
		return m.End
	}
	return 0
}

func (m *GetPostListInAuditTimeRangeRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetPostListInAuditTimeRangeRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetPostListInAuditTimeRangeResponse struct {
	// 用于下一次请求,空表示没有下一页
	LastId               string   `protobuf:"bytes,1,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	List                 []*Post  `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostListInAuditTimeRangeResponse) Reset()         { *m = GetPostListInAuditTimeRangeResponse{} }
func (m *GetPostListInAuditTimeRangeResponse) String() string { return proto.CompactTextString(m) }
func (*GetPostListInAuditTimeRangeResponse) ProtoMessage()    {}
func (*GetPostListInAuditTimeRangeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{18}
}
func (m *GetPostListInAuditTimeRangeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostListInAuditTimeRangeResponse.Unmarshal(m, b)
}
func (m *GetPostListInAuditTimeRangeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostListInAuditTimeRangeResponse.Marshal(b, m, deterministic)
}
func (dst *GetPostListInAuditTimeRangeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostListInAuditTimeRangeResponse.Merge(dst, src)
}
func (m *GetPostListInAuditTimeRangeResponse) XXX_Size() int {
	return xxx_messageInfo_GetPostListInAuditTimeRangeResponse.Size(m)
}
func (m *GetPostListInAuditTimeRangeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostListInAuditTimeRangeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostListInAuditTimeRangeResponse proto.InternalMessageInfo

func (m *GetPostListInAuditTimeRangeResponse) GetLastId() string {
	if m != nil {
		return m.LastId
	}
	return ""
}

func (m *GetPostListInAuditTimeRangeResponse) GetList() []*Post {
	if m != nil {
		return m.List
	}
	return nil
}

type AddUserPostRequest struct {
	Uid                  uint32      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BizType              PostBizType `protobuf:"varint,2,opt,name=biz_type,json=bizType,proto3,enum=ugc_community.PostBizType" json:"biz_type,omitempty"`
	PostId               string      `protobuf:"bytes,3,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *AddUserPostRequest) Reset()         { *m = AddUserPostRequest{} }
func (m *AddUserPostRequest) String() string { return proto.CompactTextString(m) }
func (*AddUserPostRequest) ProtoMessage()    {}
func (*AddUserPostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{19}
}
func (m *AddUserPostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserPostRequest.Unmarshal(m, b)
}
func (m *AddUserPostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserPostRequest.Marshal(b, m, deterministic)
}
func (dst *AddUserPostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserPostRequest.Merge(dst, src)
}
func (m *AddUserPostRequest) XXX_Size() int {
	return xxx_messageInfo_AddUserPostRequest.Size(m)
}
func (m *AddUserPostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserPostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserPostRequest proto.InternalMessageInfo

func (m *AddUserPostRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserPostRequest) GetBizType() PostBizType {
	if m != nil {
		return m.BizType
	}
	return PostBizType_POST_BIZ_TYPE_UNSPECIFIED
}

func (m *AddUserPostRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type AddUserPostResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserPostResponse) Reset()         { *m = AddUserPostResponse{} }
func (m *AddUserPostResponse) String() string { return proto.CompactTextString(m) }
func (*AddUserPostResponse) ProtoMessage()    {}
func (*AddUserPostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{20}
}
func (m *AddUserPostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserPostResponse.Unmarshal(m, b)
}
func (m *AddUserPostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserPostResponse.Marshal(b, m, deterministic)
}
func (dst *AddUserPostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserPostResponse.Merge(dst, src)
}
func (m *AddUserPostResponse) XXX_Size() int {
	return xxx_messageInfo_AddUserPostResponse.Size(m)
}
func (m *AddUserPostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserPostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserPostResponse proto.InternalMessageInfo

type DeleteUserPostRequest struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteUserPostRequest) Reset()         { *m = DeleteUserPostRequest{} }
func (m *DeleteUserPostRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteUserPostRequest) ProtoMessage()    {}
func (*DeleteUserPostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{21}
}
func (m *DeleteUserPostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteUserPostRequest.Unmarshal(m, b)
}
func (m *DeleteUserPostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteUserPostRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteUserPostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteUserPostRequest.Merge(dst, src)
}
func (m *DeleteUserPostRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteUserPostRequest.Size(m)
}
func (m *DeleteUserPostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteUserPostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteUserPostRequest proto.InternalMessageInfo

func (m *DeleteUserPostRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type DeleteUserPostResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteUserPostResponse) Reset()         { *m = DeleteUserPostResponse{} }
func (m *DeleteUserPostResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteUserPostResponse) ProtoMessage()    {}
func (*DeleteUserPostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{22}
}
func (m *DeleteUserPostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteUserPostResponse.Unmarshal(m, b)
}
func (m *DeleteUserPostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteUserPostResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteUserPostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteUserPostResponse.Merge(dst, src)
}
func (m *DeleteUserPostResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteUserPostResponse.Size(m)
}
func (m *DeleteUserPostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteUserPostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteUserPostResponse proto.InternalMessageInfo

type GetUserPostListRequest struct {
	Uid                  uint32      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BizType              PostBizType `protobuf:"varint,2,opt,name=biz_type,json=bizType,proto3,enum=ugc_community.PostBizType" json:"biz_type,omitempty"`
	LastPostId           string      `protobuf:"bytes,3,opt,name=last_post_id,json=lastPostId,proto3" json:"last_post_id,omitempty"`
	Limit                uint32      `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetUserPostListRequest) Reset()         { *m = GetUserPostListRequest{} }
func (m *GetUserPostListRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserPostListRequest) ProtoMessage()    {}
func (*GetUserPostListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{23}
}
func (m *GetUserPostListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPostListRequest.Unmarshal(m, b)
}
func (m *GetUserPostListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPostListRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserPostListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPostListRequest.Merge(dst, src)
}
func (m *GetUserPostListRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserPostListRequest.Size(m)
}
func (m *GetUserPostListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPostListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPostListRequest proto.InternalMessageInfo

func (m *GetUserPostListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPostListRequest) GetBizType() PostBizType {
	if m != nil {
		return m.BizType
	}
	return PostBizType_POST_BIZ_TYPE_UNSPECIFIED
}

func (m *GetUserPostListRequest) GetLastPostId() string {
	if m != nil {
		return m.LastPostId
	}
	return ""
}

func (m *GetUserPostListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetUserPostListResponse struct {
	PostIds              []string `protobuf:"bytes,1,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPostListResponse) Reset()         { *m = GetUserPostListResponse{} }
func (m *GetUserPostListResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserPostListResponse) ProtoMessage()    {}
func (*GetUserPostListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{24}
}
func (m *GetUserPostListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPostListResponse.Unmarshal(m, b)
}
func (m *GetUserPostListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPostListResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserPostListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPostListResponse.Merge(dst, src)
}
func (m *GetUserPostListResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserPostListResponse.Size(m)
}
func (m *GetUserPostListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPostListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPostListResponse proto.InternalMessageInfo

func (m *GetUserPostListResponse) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

type AttitudeObject struct {
	Id                   string     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CreateAt             int64      `protobuf:"varint,2,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	Type                 ObjectType `protobuf:"varint,3,opt,name=type,proto3,enum=ugc_community.ObjectType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *AttitudeObject) Reset()         { *m = AttitudeObject{} }
func (m *AttitudeObject) String() string { return proto.CompactTextString(m) }
func (*AttitudeObject) ProtoMessage()    {}
func (*AttitudeObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{25}
}
func (m *AttitudeObject) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttitudeObject.Unmarshal(m, b)
}
func (m *AttitudeObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttitudeObject.Marshal(b, m, deterministic)
}
func (dst *AttitudeObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttitudeObject.Merge(dst, src)
}
func (m *AttitudeObject) XXX_Size() int {
	return xxx_messageInfo_AttitudeObject.Size(m)
}
func (m *AttitudeObject) XXX_DiscardUnknown() {
	xxx_messageInfo_AttitudeObject.DiscardUnknown(m)
}

var xxx_messageInfo_AttitudeObject proto.InternalMessageInfo

func (m *AttitudeObject) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *AttitudeObject) GetCreateAt() int64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *AttitudeObject) GetType() ObjectType {
	if m != nil {
		return m.Type
	}
	return ObjectType_OBJECT_TYPE_UNSPECIFIED
}

type AddAttitudeRequest struct {
	UserId               uint32          `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Action               AttitudeAction  `protobuf:"varint,2,opt,name=action,proto3,enum=ugc_community.AttitudeAction" json:"action,omitempty"`
	Object               *AttitudeObject `protobuf:"bytes,3,opt,name=object,proto3" json:"object,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AddAttitudeRequest) Reset()         { *m = AddAttitudeRequest{} }
func (m *AddAttitudeRequest) String() string { return proto.CompactTextString(m) }
func (*AddAttitudeRequest) ProtoMessage()    {}
func (*AddAttitudeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{26}
}
func (m *AddAttitudeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAttitudeRequest.Unmarshal(m, b)
}
func (m *AddAttitudeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAttitudeRequest.Marshal(b, m, deterministic)
}
func (dst *AddAttitudeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAttitudeRequest.Merge(dst, src)
}
func (m *AddAttitudeRequest) XXX_Size() int {
	return xxx_messageInfo_AddAttitudeRequest.Size(m)
}
func (m *AddAttitudeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAttitudeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddAttitudeRequest proto.InternalMessageInfo

func (m *AddAttitudeRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddAttitudeRequest) GetAction() AttitudeAction {
	if m != nil {
		return m.Action
	}
	return AttitudeAction_ATTITUDE_ACTION_UNSPECIFIED
}

func (m *AddAttitudeRequest) GetObject() *AttitudeObject {
	if m != nil {
		return m.Object
	}
	return nil
}

type AddAttitudeResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAttitudeResponse) Reset()         { *m = AddAttitudeResponse{} }
func (m *AddAttitudeResponse) String() string { return proto.CompactTextString(m) }
func (*AddAttitudeResponse) ProtoMessage()    {}
func (*AddAttitudeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{27}
}
func (m *AddAttitudeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAttitudeResponse.Unmarshal(m, b)
}
func (m *AddAttitudeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAttitudeResponse.Marshal(b, m, deterministic)
}
func (dst *AddAttitudeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAttitudeResponse.Merge(dst, src)
}
func (m *AddAttitudeResponse) XXX_Size() int {
	return xxx_messageInfo_AddAttitudeResponse.Size(m)
}
func (m *AddAttitudeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAttitudeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddAttitudeResponse proto.InternalMessageInfo

type HadAttitudeRequest struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PostIds              []string `protobuf:"bytes,2,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	CommentIds           []string `protobuf:"bytes,3,rep,name=comment_ids,json=commentIds,proto3" json:"comment_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HadAttitudeRequest) Reset()         { *m = HadAttitudeRequest{} }
func (m *HadAttitudeRequest) String() string { return proto.CompactTextString(m) }
func (*HadAttitudeRequest) ProtoMessage()    {}
func (*HadAttitudeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{28}
}
func (m *HadAttitudeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HadAttitudeRequest.Unmarshal(m, b)
}
func (m *HadAttitudeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HadAttitudeRequest.Marshal(b, m, deterministic)
}
func (dst *HadAttitudeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HadAttitudeRequest.Merge(dst, src)
}
func (m *HadAttitudeRequest) XXX_Size() int {
	return xxx_messageInfo_HadAttitudeRequest.Size(m)
}
func (m *HadAttitudeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HadAttitudeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HadAttitudeRequest proto.InternalMessageInfo

func (m *HadAttitudeRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *HadAttitudeRequest) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

func (m *HadAttitudeRequest) GetCommentIds() []string {
	if m != nil {
		return m.CommentIds
	}
	return nil
}

type HadAttitudeResponse struct {
	PostHadAttitudeMap    map[string]bool `protobuf:"bytes,1,rep,name=post_had_attitude_map,json=postHadAttitudeMap,proto3" json:"post_had_attitude_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	CommentHadAttitudeMap map[string]bool `protobuf:"bytes,2,rep,name=comment_had_attitude_map,json=commentHadAttitudeMap,proto3" json:"comment_had_attitude_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral  struct{}        `json:"-"`
	XXX_unrecognized      []byte          `json:"-"`
	XXX_sizecache         int32           `json:"-"`
}

func (m *HadAttitudeResponse) Reset()         { *m = HadAttitudeResponse{} }
func (m *HadAttitudeResponse) String() string { return proto.CompactTextString(m) }
func (*HadAttitudeResponse) ProtoMessage()    {}
func (*HadAttitudeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{29}
}
func (m *HadAttitudeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HadAttitudeResponse.Unmarshal(m, b)
}
func (m *HadAttitudeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HadAttitudeResponse.Marshal(b, m, deterministic)
}
func (dst *HadAttitudeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HadAttitudeResponse.Merge(dst, src)
}
func (m *HadAttitudeResponse) XXX_Size() int {
	return xxx_messageInfo_HadAttitudeResponse.Size(m)
}
func (m *HadAttitudeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HadAttitudeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HadAttitudeResponse proto.InternalMessageInfo

func (m *HadAttitudeResponse) GetPostHadAttitudeMap() map[string]bool {
	if m != nil {
		return m.PostHadAttitudeMap
	}
	return nil
}

func (m *HadAttitudeResponse) GetCommentHadAttitudeMap() map[string]bool {
	if m != nil {
		return m.CommentHadAttitudeMap
	}
	return nil
}

// -------------- 评论 -------------- begin
type CommentSendRequest struct {
	// Deprecated
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 帖子id
	PostId string `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	// 首评论id，如果是回复帖子，这个字段为空
	RootParentId string `protobuf:"bytes,3,opt,name=root_parent_id,json=rootParentId,proto3" json:"root_parent_id,omitempty"`
	// 回复评论id，如果是第一条回复评论或帖子的，这个字段为空
	ParentId string `protobuf:"bytes,4,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 回复内容
	Content string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	// Deprecated  parent_id对应的回复评论的用户，评论的评论的回复用户
	ReplyUserId uint32 `protobuf:"varint,6,opt,name=reply_user_id,json=replyUserId,proto3" json:"reply_user_id,omitempty"`
	// 发帖时间,用于评论排序 unix second
	PostTime int64 `protobuf:"varint,7,opt,name=post_time,json=postTime,proto3" json:"post_time,omitempty"`
	// 发布评论对象
	SendEntity *CommentEntity `protobuf:"bytes,8,opt,name=send_entity,json=sendEntity,proto3" json:"send_entity,omitempty"`
	// parent_id对应的回复评论的对象，评论的评论的回复对象
	ReplyEntity *CommentEntity `protobuf:"bytes,9,opt,name=reply_entity,json=replyEntity,proto3" json:"reply_entity,omitempty"`
	// 评论来源
	Origin               CommentOrigin `protobuf:"varint,10,opt,name=origin,proto3,enum=ugc_community.CommentOrigin" json:"origin,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CommentSendRequest) Reset()         { *m = CommentSendRequest{} }
func (m *CommentSendRequest) String() string { return proto.CompactTextString(m) }
func (*CommentSendRequest) ProtoMessage()    {}
func (*CommentSendRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{30}
}
func (m *CommentSendRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentSendRequest.Unmarshal(m, b)
}
func (m *CommentSendRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentSendRequest.Marshal(b, m, deterministic)
}
func (dst *CommentSendRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentSendRequest.Merge(dst, src)
}
func (m *CommentSendRequest) XXX_Size() int {
	return xxx_messageInfo_CommentSendRequest.Size(m)
}
func (m *CommentSendRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentSendRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommentSendRequest proto.InternalMessageInfo

func (m *CommentSendRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CommentSendRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *CommentSendRequest) GetRootParentId() string {
	if m != nil {
		return m.RootParentId
	}
	return ""
}

func (m *CommentSendRequest) GetParentId() string {
	if m != nil {
		return m.ParentId
	}
	return ""
}

func (m *CommentSendRequest) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CommentSendRequest) GetReplyUserId() uint32 {
	if m != nil {
		return m.ReplyUserId
	}
	return 0
}

func (m *CommentSendRequest) GetPostTime() int64 {
	if m != nil {
		return m.PostTime
	}
	return 0
}

func (m *CommentSendRequest) GetSendEntity() *CommentEntity {
	if m != nil {
		return m.SendEntity
	}
	return nil
}

func (m *CommentSendRequest) GetReplyEntity() *CommentEntity {
	if m != nil {
		return m.ReplyEntity
	}
	return nil
}

func (m *CommentSendRequest) GetOrigin() CommentOrigin {
	if m != nil {
		return m.Origin
	}
	return CommentOrigin_COMMENT_ORIGIN_NORMAL
}

type CommentSendResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentSendResponse) Reset()         { *m = CommentSendResponse{} }
func (m *CommentSendResponse) String() string { return proto.CompactTextString(m) }
func (*CommentSendResponse) ProtoMessage()    {}
func (*CommentSendResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{31}
}
func (m *CommentSendResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentSendResponse.Unmarshal(m, b)
}
func (m *CommentSendResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentSendResponse.Marshal(b, m, deterministic)
}
func (dst *CommentSendResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentSendResponse.Merge(dst, src)
}
func (m *CommentSendResponse) XXX_Size() int {
	return xxx_messageInfo_CommentSendResponse.Size(m)
}
func (m *CommentSendResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentSendResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommentSendResponse proto.InternalMessageInfo

type CommentEntity struct {
	Type                 CommentEntityType `protobuf:"varint,1,opt,name=type,proto3,enum=ugc_community.CommentEntityType" json:"type,omitempty"`
	Id                   uint32            `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CommentEntity) Reset()         { *m = CommentEntity{} }
func (m *CommentEntity) String() string { return proto.CompactTextString(m) }
func (*CommentEntity) ProtoMessage()    {}
func (*CommentEntity) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{32}
}
func (m *CommentEntity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentEntity.Unmarshal(m, b)
}
func (m *CommentEntity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentEntity.Marshal(b, m, deterministic)
}
func (dst *CommentEntity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentEntity.Merge(dst, src)
}
func (m *CommentEntity) XXX_Size() int {
	return xxx_messageInfo_CommentEntity.Size(m)
}
func (m *CommentEntity) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentEntity.DiscardUnknown(m)
}

var xxx_messageInfo_CommentEntity proto.InternalMessageInfo

func (m *CommentEntity) GetType() CommentEntityType {
	if m != nil {
		return m.Type
	}
	return CommentEntityType_COMMENT_ENTITY_TYPE_USER
}

func (m *CommentEntity) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type CommentFetchRequest struct {
	// 帖子id
	PostId string `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	// 上一页最后的首次评id，分页使用
	LastCommentId string `protobuf:"bytes,2,opt,name=last_comment_id,json=lastCommentId,proto3" json:"last_comment_id,omitempty"`
	// 是否拉取ai角色评论
	FetchAiComment       bool     `protobuf:"varint,3,opt,name=fetch_ai_comment,json=fetchAiComment,proto3" json:"fetch_ai_comment,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentFetchRequest) Reset()         { *m = CommentFetchRequest{} }
func (m *CommentFetchRequest) String() string { return proto.CompactTextString(m) }
func (*CommentFetchRequest) ProtoMessage()    {}
func (*CommentFetchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{33}
}
func (m *CommentFetchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentFetchRequest.Unmarshal(m, b)
}
func (m *CommentFetchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentFetchRequest.Marshal(b, m, deterministic)
}
func (dst *CommentFetchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentFetchRequest.Merge(dst, src)
}
func (m *CommentFetchRequest) XXX_Size() int {
	return xxx_messageInfo_CommentFetchRequest.Size(m)
}
func (m *CommentFetchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentFetchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommentFetchRequest proto.InternalMessageInfo

func (m *CommentFetchRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *CommentFetchRequest) GetLastCommentId() string {
	if m != nil {
		return m.LastCommentId
	}
	return ""
}

func (m *CommentFetchRequest) GetFetchAiComment() bool {
	if m != nil {
		return m.FetchAiComment
	}
	return false
}

type CommentItem struct {
	// 评论id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Deprecated  评论人信息
	UserId uint32 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 评论内容
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	// 点赞数
	Likes uint32 `protobuf:"varint,4,opt,name=likes,proto3" json:"likes,omitempty"`
	// 评论时间，ms
	CreateAt int64 `protobuf:"varint,5,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	// 子评论，可能子评论很多，当前不做三级页面和分页处理，简单实现全部一次返回
	SubComments []*CommentItem_SecondCommentInfo `protobuf:"bytes,6,rep,name=sub_comments,json=subComments,proto3" json:"sub_comments,omitempty"`
	// 回复数
	ReplyCount uint32 `protobuf:"varint,7,opt,name=reply_count,json=replyCount,proto3" json:"reply_count,omitempty"`
	// 一级评论实体信息
	SendEntity           *CommentEntity `protobuf:"bytes,8,opt,name=send_entity,json=sendEntity,proto3" json:"send_entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CommentItem) Reset()         { *m = CommentItem{} }
func (m *CommentItem) String() string { return proto.CompactTextString(m) }
func (*CommentItem) ProtoMessage()    {}
func (*CommentItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{34}
}
func (m *CommentItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentItem.Unmarshal(m, b)
}
func (m *CommentItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentItem.Marshal(b, m, deterministic)
}
func (dst *CommentItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentItem.Merge(dst, src)
}
func (m *CommentItem) XXX_Size() int {
	return xxx_messageInfo_CommentItem.Size(m)
}
func (m *CommentItem) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentItem.DiscardUnknown(m)
}

var xxx_messageInfo_CommentItem proto.InternalMessageInfo

func (m *CommentItem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CommentItem) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *CommentItem) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CommentItem) GetLikes() uint32 {
	if m != nil {
		return m.Likes
	}
	return 0
}

func (m *CommentItem) GetCreateAt() int64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *CommentItem) GetSubComments() []*CommentItem_SecondCommentInfo {
	if m != nil {
		return m.SubComments
	}
	return nil
}

func (m *CommentItem) GetReplyCount() uint32 {
	if m != nil {
		return m.ReplyCount
	}
	return 0
}

func (m *CommentItem) GetSendEntity() *CommentEntity {
	if m != nil {
		return m.SendEntity
	}
	return nil
}

// 二级评论信息
type CommentItem_SecondCommentInfo struct {
	// 评论id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Deprecated  评论人信息
	UserId uint32 `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 评论内容
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	// 评论时间，ms
	CreateAt int64 `protobuf:"varint,4,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	// Deprecated  回复评论的用户，评论的评论的回复用户
	ReplyUserId uint32 `protobuf:"varint,5,opt,name=reply_user_id,json=replyUserId,proto3" json:"reply_user_id,omitempty"`
	// 评论实体信息
	SendEntity *CommentEntity `protobuf:"bytes,6,opt,name=send_entity,json=sendEntity,proto3" json:"send_entity,omitempty"`
	// 回复评论的实体，评论的评论的回复实体
	ReplyEntity          *CommentEntity `protobuf:"bytes,7,opt,name=reply_entity,json=replyEntity,proto3" json:"reply_entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CommentItem_SecondCommentInfo) Reset()         { *m = CommentItem_SecondCommentInfo{} }
func (m *CommentItem_SecondCommentInfo) String() string { return proto.CompactTextString(m) }
func (*CommentItem_SecondCommentInfo) ProtoMessage()    {}
func (*CommentItem_SecondCommentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{34, 0}
}
func (m *CommentItem_SecondCommentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentItem_SecondCommentInfo.Unmarshal(m, b)
}
func (m *CommentItem_SecondCommentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentItem_SecondCommentInfo.Marshal(b, m, deterministic)
}
func (dst *CommentItem_SecondCommentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentItem_SecondCommentInfo.Merge(dst, src)
}
func (m *CommentItem_SecondCommentInfo) XXX_Size() int {
	return xxx_messageInfo_CommentItem_SecondCommentInfo.Size(m)
}
func (m *CommentItem_SecondCommentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentItem_SecondCommentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CommentItem_SecondCommentInfo proto.InternalMessageInfo

func (m *CommentItem_SecondCommentInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CommentItem_SecondCommentInfo) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *CommentItem_SecondCommentInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CommentItem_SecondCommentInfo) GetCreateAt() int64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *CommentItem_SecondCommentInfo) GetReplyUserId() uint32 {
	if m != nil {
		return m.ReplyUserId
	}
	return 0
}

func (m *CommentItem_SecondCommentInfo) GetSendEntity() *CommentEntity {
	if m != nil {
		return m.SendEntity
	}
	return nil
}

func (m *CommentItem_SecondCommentInfo) GetReplyEntity() *CommentEntity {
	if m != nil {
		return m.ReplyEntity
	}
	return nil
}

type CommentFetchResponse struct {
	Comments             []*CommentItem `protobuf:"bytes,1,rep,name=comments,proto3" json:"comments,omitempty"`
	IsLoadFinish         bool           `protobuf:"varint,2,opt,name=is_load_finish,json=isLoadFinish,proto3" json:"is_load_finish,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CommentFetchResponse) Reset()         { *m = CommentFetchResponse{} }
func (m *CommentFetchResponse) String() string { return proto.CompactTextString(m) }
func (*CommentFetchResponse) ProtoMessage()    {}
func (*CommentFetchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{35}
}
func (m *CommentFetchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentFetchResponse.Unmarshal(m, b)
}
func (m *CommentFetchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentFetchResponse.Marshal(b, m, deterministic)
}
func (dst *CommentFetchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentFetchResponse.Merge(dst, src)
}
func (m *CommentFetchResponse) XXX_Size() int {
	return xxx_messageInfo_CommentFetchResponse.Size(m)
}
func (m *CommentFetchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentFetchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommentFetchResponse proto.InternalMessageInfo

func (m *CommentFetchResponse) GetComments() []*CommentItem {
	if m != nil {
		return m.Comments
	}
	return nil
}

func (m *CommentFetchResponse) GetIsLoadFinish() bool {
	if m != nil {
		return m.IsLoadFinish
	}
	return false
}

type GetPostHotCommentRequest struct {
	// 帖子id
	PostIds              []string `protobuf:"bytes,1,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostHotCommentRequest) Reset()         { *m = GetPostHotCommentRequest{} }
func (m *GetPostHotCommentRequest) String() string { return proto.CompactTextString(m) }
func (*GetPostHotCommentRequest) ProtoMessage()    {}
func (*GetPostHotCommentRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{36}
}
func (m *GetPostHotCommentRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostHotCommentRequest.Unmarshal(m, b)
}
func (m *GetPostHotCommentRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostHotCommentRequest.Marshal(b, m, deterministic)
}
func (dst *GetPostHotCommentRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostHotCommentRequest.Merge(dst, src)
}
func (m *GetPostHotCommentRequest) XXX_Size() int {
	return xxx_messageInfo_GetPostHotCommentRequest.Size(m)
}
func (m *GetPostHotCommentRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostHotCommentRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostHotCommentRequest proto.InternalMessageInfo

func (m *GetPostHotCommentRequest) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

type GetPostHotCommentResponse struct {
	HotComments          map[string]*CommentItem `protobuf:"bytes,1,rep,name=hot_comments,json=hotComments,proto3" json:"hot_comments,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetPostHotCommentResponse) Reset()         { *m = GetPostHotCommentResponse{} }
func (m *GetPostHotCommentResponse) String() string { return proto.CompactTextString(m) }
func (*GetPostHotCommentResponse) ProtoMessage()    {}
func (*GetPostHotCommentResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{37}
}
func (m *GetPostHotCommentResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostHotCommentResponse.Unmarshal(m, b)
}
func (m *GetPostHotCommentResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostHotCommentResponse.Marshal(b, m, deterministic)
}
func (dst *GetPostHotCommentResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostHotCommentResponse.Merge(dst, src)
}
func (m *GetPostHotCommentResponse) XXX_Size() int {
	return xxx_messageInfo_GetPostHotCommentResponse.Size(m)
}
func (m *GetPostHotCommentResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostHotCommentResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostHotCommentResponse proto.InternalMessageInfo

func (m *GetPostHotCommentResponse) GetHotComments() map[string]*CommentItem {
	if m != nil {
		return m.HotComments
	}
	return nil
}

// 删除评论
type CommentDeleteRequest struct {
	CommentId            string   `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentDeleteRequest) Reset()         { *m = CommentDeleteRequest{} }
func (m *CommentDeleteRequest) String() string { return proto.CompactTextString(m) }
func (*CommentDeleteRequest) ProtoMessage()    {}
func (*CommentDeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{38}
}
func (m *CommentDeleteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentDeleteRequest.Unmarshal(m, b)
}
func (m *CommentDeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentDeleteRequest.Marshal(b, m, deterministic)
}
func (dst *CommentDeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentDeleteRequest.Merge(dst, src)
}
func (m *CommentDeleteRequest) XXX_Size() int {
	return xxx_messageInfo_CommentDeleteRequest.Size(m)
}
func (m *CommentDeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentDeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommentDeleteRequest proto.InternalMessageInfo

func (m *CommentDeleteRequest) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

type CommentDeleteResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentDeleteResponse) Reset()         { *m = CommentDeleteResponse{} }
func (m *CommentDeleteResponse) String() string { return proto.CompactTextString(m) }
func (*CommentDeleteResponse) ProtoMessage()    {}
func (*CommentDeleteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{39}
}
func (m *CommentDeleteResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentDeleteResponse.Unmarshal(m, b)
}
func (m *CommentDeleteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentDeleteResponse.Marshal(b, m, deterministic)
}
func (dst *CommentDeleteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentDeleteResponse.Merge(dst, src)
}
func (m *CommentDeleteResponse) XXX_Size() int {
	return xxx_messageInfo_CommentDeleteResponse.Size(m)
}
func (m *CommentDeleteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentDeleteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommentDeleteResponse proto.InternalMessageInfo

type GetCommentInfoByIdRequest struct {
	CommentId string `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	// 是否拉取子评论
	LoadSubComments      bool     `protobuf:"varint,2,opt,name=load_sub_comments,json=loadSubComments,proto3" json:"load_sub_comments,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCommentInfoByIdRequest) Reset()         { *m = GetCommentInfoByIdRequest{} }
func (m *GetCommentInfoByIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetCommentInfoByIdRequest) ProtoMessage()    {}
func (*GetCommentInfoByIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{40}
}
func (m *GetCommentInfoByIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommentInfoByIdRequest.Unmarshal(m, b)
}
func (m *GetCommentInfoByIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommentInfoByIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetCommentInfoByIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommentInfoByIdRequest.Merge(dst, src)
}
func (m *GetCommentInfoByIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetCommentInfoByIdRequest.Size(m)
}
func (m *GetCommentInfoByIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommentInfoByIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommentInfoByIdRequest proto.InternalMessageInfo

func (m *GetCommentInfoByIdRequest) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *GetCommentInfoByIdRequest) GetLoadSubComments() bool {
	if m != nil {
		return m.LoadSubComments
	}
	return false
}

type GetCommentInfoByIdResponse struct {
	Item                 *CommentItem `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCommentInfoByIdResponse) Reset()         { *m = GetCommentInfoByIdResponse{} }
func (m *GetCommentInfoByIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetCommentInfoByIdResponse) ProtoMessage()    {}
func (*GetCommentInfoByIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{41}
}
func (m *GetCommentInfoByIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommentInfoByIdResponse.Unmarshal(m, b)
}
func (m *GetCommentInfoByIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommentInfoByIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetCommentInfoByIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommentInfoByIdResponse.Merge(dst, src)
}
func (m *GetCommentInfoByIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetCommentInfoByIdResponse.Size(m)
}
func (m *GetCommentInfoByIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommentInfoByIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommentInfoByIdResponse proto.InternalMessageInfo

func (m *GetCommentInfoByIdResponse) GetItem() *CommentItem {
	if m != nil {
		return m.Item
	}
	return nil
}

type GetAICommentCntInfoRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAICommentCntInfoRequest) Reset()         { *m = GetAICommentCntInfoRequest{} }
func (m *GetAICommentCntInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetAICommentCntInfoRequest) ProtoMessage()    {}
func (*GetAICommentCntInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{42}
}
func (m *GetAICommentCntInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAICommentCntInfoRequest.Unmarshal(m, b)
}
func (m *GetAICommentCntInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAICommentCntInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetAICommentCntInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAICommentCntInfoRequest.Merge(dst, src)
}
func (m *GetAICommentCntInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetAICommentCntInfoRequest.Size(m)
}
func (m *GetAICommentCntInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAICommentCntInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAICommentCntInfoRequest proto.InternalMessageInfo

func (m *GetAICommentCntInfoRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAICommentCntInfoRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type GetAICommentCntInfoResponse struct {
	UserAiCommentPostCnt  uint32   `protobuf:"varint,1,opt,name=user_ai_comment_post_cnt,json=userAiCommentPostCnt,proto3" json:"user_ai_comment_post_cnt,omitempty"`
	PostAiCommentCnt      uint32   `protobuf:"varint,2,opt,name=post_ai_comment_cnt,json=postAiCommentCnt,proto3" json:"post_ai_comment_cnt,omitempty"`
	TodayUserAiCommentCnt uint32   `protobuf:"varint,3,opt,name=today_user_ai_comment_cnt,json=todayUserAiCommentCnt,proto3" json:"today_user_ai_comment_cnt,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *GetAICommentCntInfoResponse) Reset()         { *m = GetAICommentCntInfoResponse{} }
func (m *GetAICommentCntInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetAICommentCntInfoResponse) ProtoMessage()    {}
func (*GetAICommentCntInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{43}
}
func (m *GetAICommentCntInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAICommentCntInfoResponse.Unmarshal(m, b)
}
func (m *GetAICommentCntInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAICommentCntInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetAICommentCntInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAICommentCntInfoResponse.Merge(dst, src)
}
func (m *GetAICommentCntInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetAICommentCntInfoResponse.Size(m)
}
func (m *GetAICommentCntInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAICommentCntInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAICommentCntInfoResponse proto.InternalMessageInfo

func (m *GetAICommentCntInfoResponse) GetUserAiCommentPostCnt() uint32 {
	if m != nil {
		return m.UserAiCommentPostCnt
	}
	return 0
}

func (m *GetAICommentCntInfoResponse) GetPostAiCommentCnt() uint32 {
	if m != nil {
		return m.PostAiCommentCnt
	}
	return 0
}

func (m *GetAICommentCntInfoResponse) GetTodayUserAiCommentCnt() uint32 {
	if m != nil {
		return m.TodayUserAiCommentCnt
	}
	return 0
}

type BatchGetPostAICommentCntRequest struct {
	PostIds              []string `protobuf:"bytes,1,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetPostAICommentCntRequest) Reset()         { *m = BatchGetPostAICommentCntRequest{} }
func (m *BatchGetPostAICommentCntRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetPostAICommentCntRequest) ProtoMessage()    {}
func (*BatchGetPostAICommentCntRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{44}
}
func (m *BatchGetPostAICommentCntRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPostAICommentCntRequest.Unmarshal(m, b)
}
func (m *BatchGetPostAICommentCntRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPostAICommentCntRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetPostAICommentCntRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPostAICommentCntRequest.Merge(dst, src)
}
func (m *BatchGetPostAICommentCntRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetPostAICommentCntRequest.Size(m)
}
func (m *BatchGetPostAICommentCntRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPostAICommentCntRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPostAICommentCntRequest proto.InternalMessageInfo

func (m *BatchGetPostAICommentCntRequest) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

type BatchGetPostAICommentCntResponse struct {
	CntMap               map[string]uint32 `protobuf:"bytes,1,rep,name=cnt_map,json=cntMap,proto3" json:"cnt_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetPostAICommentCntResponse) Reset()         { *m = BatchGetPostAICommentCntResponse{} }
func (m *BatchGetPostAICommentCntResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetPostAICommentCntResponse) ProtoMessage()    {}
func (*BatchGetPostAICommentCntResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{45}
}
func (m *BatchGetPostAICommentCntResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPostAICommentCntResponse.Unmarshal(m, b)
}
func (m *BatchGetPostAICommentCntResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPostAICommentCntResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetPostAICommentCntResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPostAICommentCntResponse.Merge(dst, src)
}
func (m *BatchGetPostAICommentCntResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetPostAICommentCntResponse.Size(m)
}
func (m *BatchGetPostAICommentCntResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPostAICommentCntResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPostAICommentCntResponse proto.InternalMessageInfo

func (m *BatchGetPostAICommentCntResponse) GetCntMap() map[string]uint32 {
	if m != nil {
		return m.CntMap
	}
	return nil
}

type SearchPostRequest struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32           `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	BeginTime            int64            `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64            `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Cursor               string           `protobuf:"bytes,5,opt,name=cursor,proto3" json:"cursor,omitempty"`
	BizState             AigcPostBizState `protobuf:"varint,6,opt,name=biz_state,json=bizState,proto3,enum=ugc_community.AigcPostBizState" json:"biz_state,omitempty"`
	TtId                 string           `protobuf:"bytes,7,opt,name=tt_id,json=ttId,proto3" json:"tt_id,omitempty"`
	HasChatRecord        BoolFilter       `protobuf:"varint,8,opt,name=has_chat_record,json=hasChatRecord,proto3,enum=ugc_community.BoolFilter" json:"has_chat_record,omitempty"`
	HasAiComment         BoolFilter       `protobuf:"varint,9,opt,name=has_ai_comment,json=hasAiComment,proto3,enum=ugc_community.BoolFilter" json:"has_ai_comment,omitempty"`
	PostId               string           `protobuf:"bytes,10,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SearchPostRequest) Reset()         { *m = SearchPostRequest{} }
func (m *SearchPostRequest) String() string { return proto.CompactTextString(m) }
func (*SearchPostRequest) ProtoMessage()    {}
func (*SearchPostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{46}
}
func (m *SearchPostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchPostRequest.Unmarshal(m, b)
}
func (m *SearchPostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchPostRequest.Marshal(b, m, deterministic)
}
func (dst *SearchPostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchPostRequest.Merge(dst, src)
}
func (m *SearchPostRequest) XXX_Size() int {
	return xxx_messageInfo_SearchPostRequest.Size(m)
}
func (m *SearchPostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchPostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SearchPostRequest proto.InternalMessageInfo

func (m *SearchPostRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SearchPostRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *SearchPostRequest) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *SearchPostRequest) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *SearchPostRequest) GetCursor() string {
	if m != nil {
		return m.Cursor
	}
	return ""
}

func (m *SearchPostRequest) GetBizState() AigcPostBizState {
	if m != nil {
		return m.BizState
	}
	return AigcPostBizState_AIGC_POST_BIZ_STATE_UNSPECIFIED
}

func (m *SearchPostRequest) GetTtId() string {
	if m != nil {
		return m.TtId
	}
	return ""
}

func (m *SearchPostRequest) GetHasChatRecord() BoolFilter {
	if m != nil {
		return m.HasChatRecord
	}
	return BoolFilter_BOOL_FILTER_UNSPECIFIED
}

func (m *SearchPostRequest) GetHasAiComment() BoolFilter {
	if m != nil {
		return m.HasAiComment
	}
	return BoolFilter_BOOL_FILTER_UNSPECIFIED
}

func (m *SearchPostRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type BackendPost struct {
	Id                   string                          `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32                          `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	CreatedAt            int64                           `protobuf:"varint,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	CommentCount         uint32                          `protobuf:"varint,4,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	AttitudeCount        uint32                          `protobuf:"varint,5,opt,name=attitude_count,json=attitudeCount,proto3" json:"attitude_count,omitempty"`
	RelateRoleId         uint32                          `protobuf:"varint,6,opt,name=relate_role_id,json=relateRoleId,proto3" json:"relate_role_id,omitempty"`
	Content              string                          `protobuf:"bytes,7,opt,name=content,proto3" json:"content,omitempty"`
	Attachments          []*Attachment                   `protobuf:"bytes,8,rep,name=attachments,proto3" json:"attachments,omitempty"`
	IsExposed            bool                            `protobuf:"varint,9,opt,name=is_exposed,json=isExposed,proto3" json:"is_exposed,omitempty"`
	InsertPos            uint32                          `protobuf:"varint,10,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	InsertSubjectId      string                          `protobuf:"bytes,11,opt,name=insert_subject_id,json=insertSubjectId,proto3" json:"insert_subject_id,omitempty"`
	HasChatRecord        bool                            `protobuf:"varint,12,opt,name=has_chat_record,json=hasChatRecord,proto3" json:"has_chat_record,omitempty"`
	ChatRecords          []*AigcCommunityPost_ChatRecord `protobuf:"bytes,13,rep,name=chat_records,json=chatRecords,proto3" json:"chat_records,omitempty"`
	HasAiComment         bool                            `protobuf:"varint,14,opt,name=has_ai_comment,json=hasAiComment,proto3" json:"has_ai_comment,omitempty"`
	AiComments           []*CommentItem                  `protobuf:"bytes,15,rep,name=ai_comments,json=aiComments,proto3" json:"ai_comments,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *BackendPost) Reset()         { *m = BackendPost{} }
func (m *BackendPost) String() string { return proto.CompactTextString(m) }
func (*BackendPost) ProtoMessage()    {}
func (*BackendPost) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{47}
}
func (m *BackendPost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BackendPost.Unmarshal(m, b)
}
func (m *BackendPost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BackendPost.Marshal(b, m, deterministic)
}
func (dst *BackendPost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BackendPost.Merge(dst, src)
}
func (m *BackendPost) XXX_Size() int {
	return xxx_messageInfo_BackendPost.Size(m)
}
func (m *BackendPost) XXX_DiscardUnknown() {
	xxx_messageInfo_BackendPost.DiscardUnknown(m)
}

var xxx_messageInfo_BackendPost proto.InternalMessageInfo

func (m *BackendPost) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *BackendPost) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BackendPost) GetCreatedAt() int64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *BackendPost) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *BackendPost) GetAttitudeCount() uint32 {
	if m != nil {
		return m.AttitudeCount
	}
	return 0
}

func (m *BackendPost) GetRelateRoleId() uint32 {
	if m != nil {
		return m.RelateRoleId
	}
	return 0
}

func (m *BackendPost) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *BackendPost) GetAttachments() []*Attachment {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *BackendPost) GetIsExposed() bool {
	if m != nil {
		return m.IsExposed
	}
	return false
}

func (m *BackendPost) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *BackendPost) GetInsertSubjectId() string {
	if m != nil {
		return m.InsertSubjectId
	}
	return ""
}

func (m *BackendPost) GetHasChatRecord() bool {
	if m != nil {
		return m.HasChatRecord
	}
	return false
}

func (m *BackendPost) GetChatRecords() []*AigcCommunityPost_ChatRecord {
	if m != nil {
		return m.ChatRecords
	}
	return nil
}

func (m *BackendPost) GetHasAiComment() bool {
	if m != nil {
		return m.HasAiComment
	}
	return false
}

func (m *BackendPost) GetAiComments() []*CommentItem {
	if m != nil {
		return m.AiComments
	}
	return nil
}

type SearchPostResponse struct {
	PostList             []*BackendPost `protobuf:"bytes,1,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	Cursor               string         `protobuf:"bytes,2,opt,name=cursor,proto3" json:"cursor,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SearchPostResponse) Reset()         { *m = SearchPostResponse{} }
func (m *SearchPostResponse) String() string { return proto.CompactTextString(m) }
func (*SearchPostResponse) ProtoMessage()    {}
func (*SearchPostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{48}
}
func (m *SearchPostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchPostResponse.Unmarshal(m, b)
}
func (m *SearchPostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchPostResponse.Marshal(b, m, deterministic)
}
func (dst *SearchPostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchPostResponse.Merge(dst, src)
}
func (m *SearchPostResponse) XXX_Size() int {
	return xxx_messageInfo_SearchPostResponse.Size(m)
}
func (m *SearchPostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchPostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SearchPostResponse proto.InternalMessageInfo

func (m *SearchPostResponse) GetPostList() []*BackendPost {
	if m != nil {
		return m.PostList
	}
	return nil
}

func (m *SearchPostResponse) GetCursor() string {
	if m != nil {
		return m.Cursor
	}
	return ""
}

type OfficialHandlePostRequest struct {
	PostInfos            []*OfficialHandlePostRequest_PostInfo `protobuf:"bytes,1,rep,name=post_infos,json=postInfos,proto3" json:"post_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *OfficialHandlePostRequest) Reset()         { *m = OfficialHandlePostRequest{} }
func (m *OfficialHandlePostRequest) String() string { return proto.CompactTextString(m) }
func (*OfficialHandlePostRequest) ProtoMessage()    {}
func (*OfficialHandlePostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{49}
}
func (m *OfficialHandlePostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfficialHandlePostRequest.Unmarshal(m, b)
}
func (m *OfficialHandlePostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfficialHandlePostRequest.Marshal(b, m, deterministic)
}
func (dst *OfficialHandlePostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfficialHandlePostRequest.Merge(dst, src)
}
func (m *OfficialHandlePostRequest) XXX_Size() int {
	return xxx_messageInfo_OfficialHandlePostRequest.Size(m)
}
func (m *OfficialHandlePostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfficialHandlePostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfficialHandlePostRequest proto.InternalMessageInfo

func (m *OfficialHandlePostRequest) GetPostInfos() []*OfficialHandlePostRequest_PostInfo {
	if m != nil {
		return m.PostInfos
	}
	return nil
}

type OfficialHandlePostRequest_PostInfo struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	InsertPos            uint32   `protobuf:"varint,2,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	IsExposed            bool     `protobuf:"varint,3,opt,name=is_exposed,json=isExposed,proto3" json:"is_exposed,omitempty"`
	InsertSubjectId      string   `protobuf:"bytes,4,opt,name=insert_subject_id,json=insertSubjectId,proto3" json:"insert_subject_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfficialHandlePostRequest_PostInfo) Reset()         { *m = OfficialHandlePostRequest_PostInfo{} }
func (m *OfficialHandlePostRequest_PostInfo) String() string { return proto.CompactTextString(m) }
func (*OfficialHandlePostRequest_PostInfo) ProtoMessage()    {}
func (*OfficialHandlePostRequest_PostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{49, 0}
}
func (m *OfficialHandlePostRequest_PostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfficialHandlePostRequest_PostInfo.Unmarshal(m, b)
}
func (m *OfficialHandlePostRequest_PostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfficialHandlePostRequest_PostInfo.Marshal(b, m, deterministic)
}
func (dst *OfficialHandlePostRequest_PostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfficialHandlePostRequest_PostInfo.Merge(dst, src)
}
func (m *OfficialHandlePostRequest_PostInfo) XXX_Size() int {
	return xxx_messageInfo_OfficialHandlePostRequest_PostInfo.Size(m)
}
func (m *OfficialHandlePostRequest_PostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OfficialHandlePostRequest_PostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OfficialHandlePostRequest_PostInfo proto.InternalMessageInfo

func (m *OfficialHandlePostRequest_PostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *OfficialHandlePostRequest_PostInfo) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *OfficialHandlePostRequest_PostInfo) GetIsExposed() bool {
	if m != nil {
		return m.IsExposed
	}
	return false
}

func (m *OfficialHandlePostRequest_PostInfo) GetInsertSubjectId() string {
	if m != nil {
		return m.InsertSubjectId
	}
	return ""
}

type OfficialHandlePostResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfficialHandlePostResponse) Reset()         { *m = OfficialHandlePostResponse{} }
func (m *OfficialHandlePostResponse) String() string { return proto.CompactTextString(m) }
func (*OfficialHandlePostResponse) ProtoMessage()    {}
func (*OfficialHandlePostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{50}
}
func (m *OfficialHandlePostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfficialHandlePostResponse.Unmarshal(m, b)
}
func (m *OfficialHandlePostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfficialHandlePostResponse.Marshal(b, m, deterministic)
}
func (dst *OfficialHandlePostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfficialHandlePostResponse.Merge(dst, src)
}
func (m *OfficialHandlePostResponse) XXX_Size() int {
	return xxx_messageInfo_OfficialHandlePostResponse.Size(m)
}
func (m *OfficialHandlePostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfficialHandlePostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfficialHandlePostResponse proto.InternalMessageInfo

type AigcBizPost struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CreatedAt            int64    `protobuf:"varint,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	RelateRoleId         uint32   `protobuf:"varint,4,opt,name=relate_role_id,json=relateRoleId,proto3" json:"relate_role_id,omitempty"`
	IsExposed            bool     `protobuf:"varint,5,opt,name=is_exposed,json=isExposed,proto3" json:"is_exposed,omitempty"`
	InsertPos            uint32   `protobuf:"varint,6,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	HasChatRecord        bool     `protobuf:"varint,7,opt,name=has_chat_record,json=hasChatRecord,proto3" json:"has_chat_record,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AigcBizPost) Reset()         { *m = AigcBizPost{} }
func (m *AigcBizPost) String() string { return proto.CompactTextString(m) }
func (*AigcBizPost) ProtoMessage()    {}
func (*AigcBizPost) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{51}
}
func (m *AigcBizPost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AigcBizPost.Unmarshal(m, b)
}
func (m *AigcBizPost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AigcBizPost.Marshal(b, m, deterministic)
}
func (dst *AigcBizPost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AigcBizPost.Merge(dst, src)
}
func (m *AigcBizPost) XXX_Size() int {
	return xxx_messageInfo_AigcBizPost.Size(m)
}
func (m *AigcBizPost) XXX_DiscardUnknown() {
	xxx_messageInfo_AigcBizPost.DiscardUnknown(m)
}

var xxx_messageInfo_AigcBizPost proto.InternalMessageInfo

func (m *AigcBizPost) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AigcBizPost) GetCreatedAt() int64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *AigcBizPost) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AigcBizPost) GetRelateRoleId() uint32 {
	if m != nil {
		return m.RelateRoleId
	}
	return 0
}

func (m *AigcBizPost) GetIsExposed() bool {
	if m != nil {
		return m.IsExposed
	}
	return false
}

func (m *AigcBizPost) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *AigcBizPost) GetHasChatRecord() bool {
	if m != nil {
		return m.HasChatRecord
	}
	return false
}

// 更新aigc业务表，消费帖子事件触发
type UpdateAigcBizPostRequest struct {
	HandleInfos          []*UpdateAigcBizPostRequest_HandleInfo `protobuf:"bytes,3,rep,name=handle_infos,json=handleInfos,proto3" json:"handle_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *UpdateAigcBizPostRequest) Reset()         { *m = UpdateAigcBizPostRequest{} }
func (m *UpdateAigcBizPostRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateAigcBizPostRequest) ProtoMessage()    {}
func (*UpdateAigcBizPostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{52}
}
func (m *UpdateAigcBizPostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAigcBizPostRequest.Unmarshal(m, b)
}
func (m *UpdateAigcBizPostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAigcBizPostRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateAigcBizPostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAigcBizPostRequest.Merge(dst, src)
}
func (m *UpdateAigcBizPostRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateAigcBizPostRequest.Size(m)
}
func (m *UpdateAigcBizPostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAigcBizPostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAigcBizPostRequest proto.InternalMessageInfo

func (m *UpdateAigcBizPostRequest) GetHandleInfos() []*UpdateAigcBizPostRequest_HandleInfo {
	if m != nil {
		return m.HandleInfos
	}
	return nil
}

type UpdateAigcBizPostRequest_HandleInfo struct {
	BizPostInfos         *AigcBizPost                    `protobuf:"bytes,1,opt,name=biz_post_infos,json=bizPostInfos,proto3" json:"biz_post_infos,omitempty"`
	Action               UpdateAigcBizPostRequest_Action `protobuf:"varint,2,opt,name=action,proto3,enum=ugc_community.UpdateAigcBizPostRequest_Action" json:"action,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *UpdateAigcBizPostRequest_HandleInfo) Reset()         { *m = UpdateAigcBizPostRequest_HandleInfo{} }
func (m *UpdateAigcBizPostRequest_HandleInfo) String() string { return proto.CompactTextString(m) }
func (*UpdateAigcBizPostRequest_HandleInfo) ProtoMessage()    {}
func (*UpdateAigcBizPostRequest_HandleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{52, 0}
}
func (m *UpdateAigcBizPostRequest_HandleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAigcBizPostRequest_HandleInfo.Unmarshal(m, b)
}
func (m *UpdateAigcBizPostRequest_HandleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAigcBizPostRequest_HandleInfo.Marshal(b, m, deterministic)
}
func (dst *UpdateAigcBizPostRequest_HandleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAigcBizPostRequest_HandleInfo.Merge(dst, src)
}
func (m *UpdateAigcBizPostRequest_HandleInfo) XXX_Size() int {
	return xxx_messageInfo_UpdateAigcBizPostRequest_HandleInfo.Size(m)
}
func (m *UpdateAigcBizPostRequest_HandleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAigcBizPostRequest_HandleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAigcBizPostRequest_HandleInfo proto.InternalMessageInfo

func (m *UpdateAigcBizPostRequest_HandleInfo) GetBizPostInfos() *AigcBizPost {
	if m != nil {
		return m.BizPostInfos
	}
	return nil
}

func (m *UpdateAigcBizPostRequest_HandleInfo) GetAction() UpdateAigcBizPostRequest_Action {
	if m != nil {
		return m.Action
	}
	return UpdateAigcBizPostRequest_ACTION_UNSPECIFIED
}

type UpdateAigcBizPostResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAigcBizPostResponse) Reset()         { *m = UpdateAigcBizPostResponse{} }
func (m *UpdateAigcBizPostResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateAigcBizPostResponse) ProtoMessage()    {}
func (*UpdateAigcBizPostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{53}
}
func (m *UpdateAigcBizPostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAigcBizPostResponse.Unmarshal(m, b)
}
func (m *UpdateAigcBizPostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAigcBizPostResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateAigcBizPostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAigcBizPostResponse.Merge(dst, src)
}
func (m *UpdateAigcBizPostResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateAigcBizPostResponse.Size(m)
}
func (m *UpdateAigcBizPostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAigcBizPostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAigcBizPostResponse proto.InternalMessageInfo

type StickyPostInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Icon                 string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StickyPostInfo) Reset()         { *m = StickyPostInfo{} }
func (m *StickyPostInfo) String() string { return proto.CompactTextString(m) }
func (*StickyPostInfo) ProtoMessage()    {}
func (*StickyPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{54}
}
func (m *StickyPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StickyPostInfo.Unmarshal(m, b)
}
func (m *StickyPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StickyPostInfo.Marshal(b, m, deterministic)
}
func (dst *StickyPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StickyPostInfo.Merge(dst, src)
}
func (m *StickyPostInfo) XXX_Size() int {
	return xxx_messageInfo_StickyPostInfo.Size(m)
}
func (m *StickyPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StickyPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StickyPostInfo proto.InternalMessageInfo

func (m *StickyPostInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *StickyPostInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *StickyPostInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

type UpsertSubjectTabRequest struct {
	Op                   UpsertSubjectTabRequest_OpType `protobuf:"varint,1,opt,name=op,proto3,enum=ugc_community.UpsertSubjectTabRequest_OpType" json:"op,omitempty"`
	Tab                  *UpsertSubjectTabRequest_Tab   `protobuf:"bytes,2,opt,name=tab,proto3" json:"tab,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *UpsertSubjectTabRequest) Reset()         { *m = UpsertSubjectTabRequest{} }
func (m *UpsertSubjectTabRequest) String() string { return proto.CompactTextString(m) }
func (*UpsertSubjectTabRequest) ProtoMessage()    {}
func (*UpsertSubjectTabRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{55}
}
func (m *UpsertSubjectTabRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertSubjectTabRequest.Unmarshal(m, b)
}
func (m *UpsertSubjectTabRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertSubjectTabRequest.Marshal(b, m, deterministic)
}
func (dst *UpsertSubjectTabRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertSubjectTabRequest.Merge(dst, src)
}
func (m *UpsertSubjectTabRequest) XXX_Size() int {
	return xxx_messageInfo_UpsertSubjectTabRequest.Size(m)
}
func (m *UpsertSubjectTabRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertSubjectTabRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertSubjectTabRequest proto.InternalMessageInfo

func (m *UpsertSubjectTabRequest) GetOp() UpsertSubjectTabRequest_OpType {
	if m != nil {
		return m.Op
	}
	return UpsertSubjectTabRequest_OP_TYPE_UNSPECIFIED
}

func (m *UpsertSubjectTabRequest) GetTab() *UpsertSubjectTabRequest_Tab {
	if m != nil {
		return m.Tab
	}
	return nil
}

type UpsertSubjectTabRequest_Tab struct {
	// 主题id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 主题名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 置顶帖子
	StickyPostList []*StickyPostInfo `protobuf:"bytes,3,rep,name=sticky_post_list,json=stickyPostList,proto3" json:"sticky_post_list,omitempty"`
	// 主题类型
	Type                 SubjectType `protobuf:"varint,4,opt,name=type,proto3,enum=ugc_community.SubjectType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpsertSubjectTabRequest_Tab) Reset()         { *m = UpsertSubjectTabRequest_Tab{} }
func (m *UpsertSubjectTabRequest_Tab) String() string { return proto.CompactTextString(m) }
func (*UpsertSubjectTabRequest_Tab) ProtoMessage()    {}
func (*UpsertSubjectTabRequest_Tab) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{55, 0}
}
func (m *UpsertSubjectTabRequest_Tab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertSubjectTabRequest_Tab.Unmarshal(m, b)
}
func (m *UpsertSubjectTabRequest_Tab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertSubjectTabRequest_Tab.Marshal(b, m, deterministic)
}
func (dst *UpsertSubjectTabRequest_Tab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertSubjectTabRequest_Tab.Merge(dst, src)
}
func (m *UpsertSubjectTabRequest_Tab) XXX_Size() int {
	return xxx_messageInfo_UpsertSubjectTabRequest_Tab.Size(m)
}
func (m *UpsertSubjectTabRequest_Tab) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertSubjectTabRequest_Tab.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertSubjectTabRequest_Tab proto.InternalMessageInfo

func (m *UpsertSubjectTabRequest_Tab) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpsertSubjectTabRequest_Tab) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpsertSubjectTabRequest_Tab) GetStickyPostList() []*StickyPostInfo {
	if m != nil {
		return m.StickyPostList
	}
	return nil
}

func (m *UpsertSubjectTabRequest_Tab) GetType() SubjectType {
	if m != nil {
		return m.Type
	}
	return SubjectType_SUBJECT_TYPE_UNSPECIFIED
}

// 主题
type SubjectTabInfo struct {
	// 主题ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 主题名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 主题置顶帖子
	StickyPostList []*StickyPostInfo `protobuf:"bytes,3,rep,name=sticky_post_list,json=stickyPostList,proto3" json:"sticky_post_list,omitempty"`
	// 主题类型
	Type                 SubjectType `protobuf:"varint,4,opt,name=type,proto3,enum=ugc_community.SubjectType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SubjectTabInfo) Reset()         { *m = SubjectTabInfo{} }
func (m *SubjectTabInfo) String() string { return proto.CompactTextString(m) }
func (*SubjectTabInfo) ProtoMessage()    {}
func (*SubjectTabInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{56}
}
func (m *SubjectTabInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubjectTabInfo.Unmarshal(m, b)
}
func (m *SubjectTabInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubjectTabInfo.Marshal(b, m, deterministic)
}
func (dst *SubjectTabInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubjectTabInfo.Merge(dst, src)
}
func (m *SubjectTabInfo) XXX_Size() int {
	return xxx_messageInfo_SubjectTabInfo.Size(m)
}
func (m *SubjectTabInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SubjectTabInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SubjectTabInfo proto.InternalMessageInfo

func (m *SubjectTabInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SubjectTabInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SubjectTabInfo) GetStickyPostList() []*StickyPostInfo {
	if m != nil {
		return m.StickyPostList
	}
	return nil
}

func (m *SubjectTabInfo) GetType() SubjectType {
	if m != nil {
		return m.Type
	}
	return SubjectType_SUBJECT_TYPE_UNSPECIFIED
}

type UpsertSubjectTabResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertSubjectTabResponse) Reset()         { *m = UpsertSubjectTabResponse{} }
func (m *UpsertSubjectTabResponse) String() string { return proto.CompactTextString(m) }
func (*UpsertSubjectTabResponse) ProtoMessage()    {}
func (*UpsertSubjectTabResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{57}
}
func (m *UpsertSubjectTabResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertSubjectTabResponse.Unmarshal(m, b)
}
func (m *UpsertSubjectTabResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertSubjectTabResponse.Marshal(b, m, deterministic)
}
func (dst *UpsertSubjectTabResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertSubjectTabResponse.Merge(dst, src)
}
func (m *UpsertSubjectTabResponse) XXX_Size() int {
	return xxx_messageInfo_UpsertSubjectTabResponse.Size(m)
}
func (m *UpsertSubjectTabResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertSubjectTabResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertSubjectTabResponse proto.InternalMessageInfo

type GetSubjectTabListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSubjectTabListRequest) Reset()         { *m = GetSubjectTabListRequest{} }
func (m *GetSubjectTabListRequest) String() string { return proto.CompactTextString(m) }
func (*GetSubjectTabListRequest) ProtoMessage()    {}
func (*GetSubjectTabListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{58}
}
func (m *GetSubjectTabListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSubjectTabListRequest.Unmarshal(m, b)
}
func (m *GetSubjectTabListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSubjectTabListRequest.Marshal(b, m, deterministic)
}
func (dst *GetSubjectTabListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSubjectTabListRequest.Merge(dst, src)
}
func (m *GetSubjectTabListRequest) XXX_Size() int {
	return xxx_messageInfo_GetSubjectTabListRequest.Size(m)
}
func (m *GetSubjectTabListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSubjectTabListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSubjectTabListRequest proto.InternalMessageInfo

type GetSubjectTabListResponse struct {
	List                 []*SubjectTabInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetSubjectTabListResponse) Reset()         { *m = GetSubjectTabListResponse{} }
func (m *GetSubjectTabListResponse) String() string { return proto.CompactTextString(m) }
func (*GetSubjectTabListResponse) ProtoMessage()    {}
func (*GetSubjectTabListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{59}
}
func (m *GetSubjectTabListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSubjectTabListResponse.Unmarshal(m, b)
}
func (m *GetSubjectTabListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSubjectTabListResponse.Marshal(b, m, deterministic)
}
func (dst *GetSubjectTabListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSubjectTabListResponse.Merge(dst, src)
}
func (m *GetSubjectTabListResponse) XXX_Size() int {
	return xxx_messageInfo_GetSubjectTabListResponse.Size(m)
}
func (m *GetSubjectTabListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSubjectTabListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSubjectTabListResponse proto.InternalMessageInfo

func (m *GetSubjectTabListResponse) GetList() []*SubjectTabInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type GetSubjectTabByIdsRequest struct {
	SubjectIds           []string `protobuf:"bytes,1,rep,name=subject_ids,json=subjectIds,proto3" json:"subject_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSubjectTabByIdsRequest) Reset()         { *m = GetSubjectTabByIdsRequest{} }
func (m *GetSubjectTabByIdsRequest) String() string { return proto.CompactTextString(m) }
func (*GetSubjectTabByIdsRequest) ProtoMessage()    {}
func (*GetSubjectTabByIdsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{60}
}
func (m *GetSubjectTabByIdsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSubjectTabByIdsRequest.Unmarshal(m, b)
}
func (m *GetSubjectTabByIdsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSubjectTabByIdsRequest.Marshal(b, m, deterministic)
}
func (dst *GetSubjectTabByIdsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSubjectTabByIdsRequest.Merge(dst, src)
}
func (m *GetSubjectTabByIdsRequest) XXX_Size() int {
	return xxx_messageInfo_GetSubjectTabByIdsRequest.Size(m)
}
func (m *GetSubjectTabByIdsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSubjectTabByIdsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSubjectTabByIdsRequest proto.InternalMessageInfo

func (m *GetSubjectTabByIdsRequest) GetSubjectIds() []string {
	if m != nil {
		return m.SubjectIds
	}
	return nil
}

type GetSubjectTabByIdsResponse struct {
	List                 []*SubjectTabInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetSubjectTabByIdsResponse) Reset()         { *m = GetSubjectTabByIdsResponse{} }
func (m *GetSubjectTabByIdsResponse) String() string { return proto.CompactTextString(m) }
func (*GetSubjectTabByIdsResponse) ProtoMessage()    {}
func (*GetSubjectTabByIdsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{61}
}
func (m *GetSubjectTabByIdsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSubjectTabByIdsResponse.Unmarshal(m, b)
}
func (m *GetSubjectTabByIdsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSubjectTabByIdsResponse.Marshal(b, m, deterministic)
}
func (dst *GetSubjectTabByIdsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSubjectTabByIdsResponse.Merge(dst, src)
}
func (m *GetSubjectTabByIdsResponse) XXX_Size() int {
	return xxx_messageInfo_GetSubjectTabByIdsResponse.Size(m)
}
func (m *GetSubjectTabByIdsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSubjectTabByIdsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSubjectTabByIdsResponse proto.InternalMessageInfo

func (m *GetSubjectTabByIdsResponse) GetList() []*SubjectTabInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type DeleteSubjectTabRequest struct {
	SubjectIds           []string `protobuf:"bytes,1,rep,name=subject_ids,json=subjectIds,proto3" json:"subject_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteSubjectTabRequest) Reset()         { *m = DeleteSubjectTabRequest{} }
func (m *DeleteSubjectTabRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteSubjectTabRequest) ProtoMessage()    {}
func (*DeleteSubjectTabRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{62}
}
func (m *DeleteSubjectTabRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteSubjectTabRequest.Unmarshal(m, b)
}
func (m *DeleteSubjectTabRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteSubjectTabRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteSubjectTabRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteSubjectTabRequest.Merge(dst, src)
}
func (m *DeleteSubjectTabRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteSubjectTabRequest.Size(m)
}
func (m *DeleteSubjectTabRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteSubjectTabRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteSubjectTabRequest proto.InternalMessageInfo

func (m *DeleteSubjectTabRequest) GetSubjectIds() []string {
	if m != nil {
		return m.SubjectIds
	}
	return nil
}

type DeleteSubjectTabResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteSubjectTabResponse) Reset()         { *m = DeleteSubjectTabResponse{} }
func (m *DeleteSubjectTabResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteSubjectTabResponse) ProtoMessage()    {}
func (*DeleteSubjectTabResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{63}
}
func (m *DeleteSubjectTabResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteSubjectTabResponse.Unmarshal(m, b)
}
func (m *DeleteSubjectTabResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteSubjectTabResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteSubjectTabResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteSubjectTabResponse.Merge(dst, src)
}
func (m *DeleteSubjectTabResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteSubjectTabResponse.Size(m)
}
func (m *DeleteSubjectTabResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteSubjectTabResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteSubjectTabResponse proto.InternalMessageInfo

type ResortSubjectTabRequest struct {
	SubjectIds           []string `protobuf:"bytes,1,rep,name=subject_ids,json=subjectIds,proto3" json:"subject_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResortSubjectTabRequest) Reset()         { *m = ResortSubjectTabRequest{} }
func (m *ResortSubjectTabRequest) String() string { return proto.CompactTextString(m) }
func (*ResortSubjectTabRequest) ProtoMessage()    {}
func (*ResortSubjectTabRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{64}
}
func (m *ResortSubjectTabRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResortSubjectTabRequest.Unmarshal(m, b)
}
func (m *ResortSubjectTabRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResortSubjectTabRequest.Marshal(b, m, deterministic)
}
func (dst *ResortSubjectTabRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResortSubjectTabRequest.Merge(dst, src)
}
func (m *ResortSubjectTabRequest) XXX_Size() int {
	return xxx_messageInfo_ResortSubjectTabRequest.Size(m)
}
func (m *ResortSubjectTabRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ResortSubjectTabRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ResortSubjectTabRequest proto.InternalMessageInfo

func (m *ResortSubjectTabRequest) GetSubjectIds() []string {
	if m != nil {
		return m.SubjectIds
	}
	return nil
}

type ResortSubjectTabResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResortSubjectTabResponse) Reset()         { *m = ResortSubjectTabResponse{} }
func (m *ResortSubjectTabResponse) String() string { return proto.CompactTextString(m) }
func (*ResortSubjectTabResponse) ProtoMessage()    {}
func (*ResortSubjectTabResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{65}
}
func (m *ResortSubjectTabResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResortSubjectTabResponse.Unmarshal(m, b)
}
func (m *ResortSubjectTabResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResortSubjectTabResponse.Marshal(b, m, deterministic)
}
func (dst *ResortSubjectTabResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResortSubjectTabResponse.Merge(dst, src)
}
func (m *ResortSubjectTabResponse) XXX_Size() int {
	return xxx_messageInfo_ResortSubjectTabResponse.Size(m)
}
func (m *ResortSubjectTabResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ResortSubjectTabResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ResortSubjectTabResponse proto.InternalMessageInfo

type CreateTopicRequest struct {
	Info                 *CreateTopicRequest_Topic `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *CreateTopicRequest) Reset()         { *m = CreateTopicRequest{} }
func (m *CreateTopicRequest) String() string { return proto.CompactTextString(m) }
func (*CreateTopicRequest) ProtoMessage()    {}
func (*CreateTopicRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{66}
}
func (m *CreateTopicRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTopicRequest.Unmarshal(m, b)
}
func (m *CreateTopicRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTopicRequest.Marshal(b, m, deterministic)
}
func (dst *CreateTopicRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTopicRequest.Merge(dst, src)
}
func (m *CreateTopicRequest) XXX_Size() int {
	return xxx_messageInfo_CreateTopicRequest.Size(m)
}
func (m *CreateTopicRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTopicRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTopicRequest proto.InternalMessageInfo

func (m *CreateTopicRequest) GetInfo() *CreateTopicRequest_Topic {
	if m != nil {
		return m.Info
	}
	return nil
}

type CreateTopicRequest_Topic struct {
	// 话题类型
	Type TopicType `protobuf:"varint,1,opt,name=type,proto3,enum=ugc_community.TopicType" json:"type,omitempty"`
	// 话题名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 排序
	Sort                 uint32   `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTopicRequest_Topic) Reset()         { *m = CreateTopicRequest_Topic{} }
func (m *CreateTopicRequest_Topic) String() string { return proto.CompactTextString(m) }
func (*CreateTopicRequest_Topic) ProtoMessage()    {}
func (*CreateTopicRequest_Topic) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{66, 0}
}
func (m *CreateTopicRequest_Topic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTopicRequest_Topic.Unmarshal(m, b)
}
func (m *CreateTopicRequest_Topic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTopicRequest_Topic.Marshal(b, m, deterministic)
}
func (dst *CreateTopicRequest_Topic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTopicRequest_Topic.Merge(dst, src)
}
func (m *CreateTopicRequest_Topic) XXX_Size() int {
	return xxx_messageInfo_CreateTopicRequest_Topic.Size(m)
}
func (m *CreateTopicRequest_Topic) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTopicRequest_Topic.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTopicRequest_Topic proto.InternalMessageInfo

func (m *CreateTopicRequest_Topic) GetType() TopicType {
	if m != nil {
		return m.Type
	}
	return TopicType_TOPIC_TYPE_UNSPECIFIED
}

func (m *CreateTopicRequest_Topic) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateTopicRequest_Topic) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

type CreateTopicResponse struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTopicResponse) Reset()         { *m = CreateTopicResponse{} }
func (m *CreateTopicResponse) String() string { return proto.CompactTextString(m) }
func (*CreateTopicResponse) ProtoMessage()    {}
func (*CreateTopicResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{67}
}
func (m *CreateTopicResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTopicResponse.Unmarshal(m, b)
}
func (m *CreateTopicResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTopicResponse.Marshal(b, m, deterministic)
}
func (dst *CreateTopicResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTopicResponse.Merge(dst, src)
}
func (m *CreateTopicResponse) XXX_Size() int {
	return xxx_messageInfo_CreateTopicResponse.Size(m)
}
func (m *CreateTopicResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTopicResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTopicResponse proto.InternalMessageInfo

func (m *CreateTopicResponse) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type UpdateTopicRequest struct {
	Info                 *UpdateTopicRequest_Topic `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UpdateTopicRequest) Reset()         { *m = UpdateTopicRequest{} }
func (m *UpdateTopicRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateTopicRequest) ProtoMessage()    {}
func (*UpdateTopicRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{68}
}
func (m *UpdateTopicRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTopicRequest.Unmarshal(m, b)
}
func (m *UpdateTopicRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTopicRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateTopicRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTopicRequest.Merge(dst, src)
}
func (m *UpdateTopicRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateTopicRequest.Size(m)
}
func (m *UpdateTopicRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTopicRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTopicRequest proto.InternalMessageInfo

func (m *UpdateTopicRequest) GetInfo() *UpdateTopicRequest_Topic {
	if m != nil {
		return m.Info
	}
	return nil
}

type UpdateTopicRequest_Topic struct {
	// 话题id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 话题名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 排序
	Sort                 uint32   `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTopicRequest_Topic) Reset()         { *m = UpdateTopicRequest_Topic{} }
func (m *UpdateTopicRequest_Topic) String() string { return proto.CompactTextString(m) }
func (*UpdateTopicRequest_Topic) ProtoMessage()    {}
func (*UpdateTopicRequest_Topic) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{68, 0}
}
func (m *UpdateTopicRequest_Topic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTopicRequest_Topic.Unmarshal(m, b)
}
func (m *UpdateTopicRequest_Topic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTopicRequest_Topic.Marshal(b, m, deterministic)
}
func (dst *UpdateTopicRequest_Topic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTopicRequest_Topic.Merge(dst, src)
}
func (m *UpdateTopicRequest_Topic) XXX_Size() int {
	return xxx_messageInfo_UpdateTopicRequest_Topic.Size(m)
}
func (m *UpdateTopicRequest_Topic) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTopicRequest_Topic.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTopicRequest_Topic proto.InternalMessageInfo

func (m *UpdateTopicRequest_Topic) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpdateTopicRequest_Topic) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpdateTopicRequest_Topic) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

type UpdateTopicResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTopicResponse) Reset()         { *m = UpdateTopicResponse{} }
func (m *UpdateTopicResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateTopicResponse) ProtoMessage()    {}
func (*UpdateTopicResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{69}
}
func (m *UpdateTopicResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTopicResponse.Unmarshal(m, b)
}
func (m *UpdateTopicResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTopicResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateTopicResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTopicResponse.Merge(dst, src)
}
func (m *UpdateTopicResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateTopicResponse.Size(m)
}
func (m *UpdateTopicResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTopicResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTopicResponse proto.InternalMessageInfo

type BatchDeleteTopicRequest struct {
	// 话题id
	IdList               []string `protobuf:"bytes,1,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDeleteTopicRequest) Reset()         { *m = BatchDeleteTopicRequest{} }
func (m *BatchDeleteTopicRequest) String() string { return proto.CompactTextString(m) }
func (*BatchDeleteTopicRequest) ProtoMessage()    {}
func (*BatchDeleteTopicRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{70}
}
func (m *BatchDeleteTopicRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDeleteTopicRequest.Unmarshal(m, b)
}
func (m *BatchDeleteTopicRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDeleteTopicRequest.Marshal(b, m, deterministic)
}
func (dst *BatchDeleteTopicRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDeleteTopicRequest.Merge(dst, src)
}
func (m *BatchDeleteTopicRequest) XXX_Size() int {
	return xxx_messageInfo_BatchDeleteTopicRequest.Size(m)
}
func (m *BatchDeleteTopicRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDeleteTopicRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDeleteTopicRequest proto.InternalMessageInfo

func (m *BatchDeleteTopicRequest) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

type BatchDeleteTopicResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDeleteTopicResponse) Reset()         { *m = BatchDeleteTopicResponse{} }
func (m *BatchDeleteTopicResponse) String() string { return proto.CompactTextString(m) }
func (*BatchDeleteTopicResponse) ProtoMessage()    {}
func (*BatchDeleteTopicResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{71}
}
func (m *BatchDeleteTopicResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDeleteTopicResponse.Unmarshal(m, b)
}
func (m *BatchDeleteTopicResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDeleteTopicResponse.Marshal(b, m, deterministic)
}
func (dst *BatchDeleteTopicResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDeleteTopicResponse.Merge(dst, src)
}
func (m *BatchDeleteTopicResponse) XXX_Size() int {
	return xxx_messageInfo_BatchDeleteTopicResponse.Size(m)
}
func (m *BatchDeleteTopicResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDeleteTopicResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDeleteTopicResponse proto.InternalMessageInfo

type GetTopicListRequest struct {
	// 分页游标，第一页传空
	Cursor string `protobuf:"bytes,1,opt,name=cursor,proto3" json:"cursor,omitempty"`
	// 每页返回数量，上限200
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicListRequest) Reset()         { *m = GetTopicListRequest{} }
func (m *GetTopicListRequest) String() string { return proto.CompactTextString(m) }
func (*GetTopicListRequest) ProtoMessage()    {}
func (*GetTopicListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{72}
}
func (m *GetTopicListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicListRequest.Unmarshal(m, b)
}
func (m *GetTopicListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicListRequest.Marshal(b, m, deterministic)
}
func (dst *GetTopicListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicListRequest.Merge(dst, src)
}
func (m *GetTopicListRequest) XXX_Size() int {
	return xxx_messageInfo_GetTopicListRequest.Size(m)
}
func (m *GetTopicListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicListRequest proto.InternalMessageInfo

func (m *GetTopicListRequest) GetCursor() string {
	if m != nil {
		return m.Cursor
	}
	return ""
}

func (m *GetTopicListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetTopicListResponse struct {
	// 用作下一页请求游标，到底为空
	NextCursor           string       `protobuf:"bytes,1,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
	List                 []*TopicInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetTopicListResponse) Reset()         { *m = GetTopicListResponse{} }
func (m *GetTopicListResponse) String() string { return proto.CompactTextString(m) }
func (*GetTopicListResponse) ProtoMessage()    {}
func (*GetTopicListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{73}
}
func (m *GetTopicListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicListResponse.Unmarshal(m, b)
}
func (m *GetTopicListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicListResponse.Marshal(b, m, deterministic)
}
func (dst *GetTopicListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicListResponse.Merge(dst, src)
}
func (m *GetTopicListResponse) XXX_Size() int {
	return xxx_messageInfo_GetTopicListResponse.Size(m)
}
func (m *GetTopicListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicListResponse proto.InternalMessageInfo

func (m *GetTopicListResponse) GetNextCursor() string {
	if m != nil {
		return m.NextCursor
	}
	return ""
}

func (m *GetTopicListResponse) GetList() []*TopicInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type BatchGetTopicRequest struct {
	IdList               []string `protobuf:"bytes,1,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetTopicRequest) Reset()         { *m = BatchGetTopicRequest{} }
func (m *BatchGetTopicRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetTopicRequest) ProtoMessage()    {}
func (*BatchGetTopicRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{74}
}
func (m *BatchGetTopicRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetTopicRequest.Unmarshal(m, b)
}
func (m *BatchGetTopicRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetTopicRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetTopicRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetTopicRequest.Merge(dst, src)
}
func (m *BatchGetTopicRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetTopicRequest.Size(m)
}
func (m *BatchGetTopicRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetTopicRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetTopicRequest proto.InternalMessageInfo

func (m *BatchGetTopicRequest) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

type BatchGetTopicResponse struct {
	Topics               map[string]*TopicInfo `protobuf:"bytes,1,rep,name=topics,proto3" json:"topics,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BatchGetTopicResponse) Reset()         { *m = BatchGetTopicResponse{} }
func (m *BatchGetTopicResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetTopicResponse) ProtoMessage()    {}
func (*BatchGetTopicResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{75}
}
func (m *BatchGetTopicResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetTopicResponse.Unmarshal(m, b)
}
func (m *BatchGetTopicResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetTopicResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetTopicResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetTopicResponse.Merge(dst, src)
}
func (m *BatchGetTopicResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetTopicResponse.Size(m)
}
func (m *BatchGetTopicResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetTopicResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetTopicResponse proto.InternalMessageInfo

func (m *BatchGetTopicResponse) GetTopics() map[string]*TopicInfo {
	if m != nil {
		return m.Topics
	}
	return nil
}

type GetTopicRequest struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicRequest) Reset()         { *m = GetTopicRequest{} }
func (m *GetTopicRequest) String() string { return proto.CompactTextString(m) }
func (*GetTopicRequest) ProtoMessage()    {}
func (*GetTopicRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{76}
}
func (m *GetTopicRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicRequest.Unmarshal(m, b)
}
func (m *GetTopicRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicRequest.Marshal(b, m, deterministic)
}
func (dst *GetTopicRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicRequest.Merge(dst, src)
}
func (m *GetTopicRequest) XXX_Size() int {
	return xxx_messageInfo_GetTopicRequest.Size(m)
}
func (m *GetTopicRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicRequest proto.InternalMessageInfo

func (m *GetTopicRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type GetTopicResponse struct {
	Info                 *TopicInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetTopicResponse) Reset()         { *m = GetTopicResponse{} }
func (m *GetTopicResponse) String() string { return proto.CompactTextString(m) }
func (*GetTopicResponse) ProtoMessage()    {}
func (*GetTopicResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{77}
}
func (m *GetTopicResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicResponse.Unmarshal(m, b)
}
func (m *GetTopicResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicResponse.Marshal(b, m, deterministic)
}
func (dst *GetTopicResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicResponse.Merge(dst, src)
}
func (m *GetTopicResponse) XXX_Size() int {
	return xxx_messageInfo_GetTopicResponse.Size(m)
}
func (m *GetTopicResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicResponse proto.InternalMessageInfo

func (m *GetTopicResponse) GetInfo() *TopicInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type AddPostGuideTaskRequest struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 角色ID
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPostGuideTaskRequest) Reset()         { *m = AddPostGuideTaskRequest{} }
func (m *AddPostGuideTaskRequest) String() string { return proto.CompactTextString(m) }
func (*AddPostGuideTaskRequest) ProtoMessage()    {}
func (*AddPostGuideTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{78}
}
func (m *AddPostGuideTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPostGuideTaskRequest.Unmarshal(m, b)
}
func (m *AddPostGuideTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPostGuideTaskRequest.Marshal(b, m, deterministic)
}
func (dst *AddPostGuideTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPostGuideTaskRequest.Merge(dst, src)
}
func (m *AddPostGuideTaskRequest) XXX_Size() int {
	return xxx_messageInfo_AddPostGuideTaskRequest.Size(m)
}
func (m *AddPostGuideTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPostGuideTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddPostGuideTaskRequest proto.InternalMessageInfo

func (m *AddPostGuideTaskRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddPostGuideTaskRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type AddPostGuideTaskResponse struct {
	TaskToken            string   `protobuf:"bytes,1,opt,name=task_token,json=taskToken,proto3" json:"task_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPostGuideTaskResponse) Reset()         { *m = AddPostGuideTaskResponse{} }
func (m *AddPostGuideTaskResponse) String() string { return proto.CompactTextString(m) }
func (*AddPostGuideTaskResponse) ProtoMessage()    {}
func (*AddPostGuideTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{79}
}
func (m *AddPostGuideTaskResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPostGuideTaskResponse.Unmarshal(m, b)
}
func (m *AddPostGuideTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPostGuideTaskResponse.Marshal(b, m, deterministic)
}
func (dst *AddPostGuideTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPostGuideTaskResponse.Merge(dst, src)
}
func (m *AddPostGuideTaskResponse) XXX_Size() int {
	return xxx_messageInfo_AddPostGuideTaskResponse.Size(m)
}
func (m *AddPostGuideTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPostGuideTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddPostGuideTaskResponse proto.InternalMessageInfo

func (m *AddPostGuideTaskResponse) GetTaskToken() string {
	if m != nil {
		return m.TaskToken
	}
	return ""
}

type GetPostGuideTaskRequest struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 角色ID
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostGuideTaskRequest) Reset()         { *m = GetPostGuideTaskRequest{} }
func (m *GetPostGuideTaskRequest) String() string { return proto.CompactTextString(m) }
func (*GetPostGuideTaskRequest) ProtoMessage()    {}
func (*GetPostGuideTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{80}
}
func (m *GetPostGuideTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostGuideTaskRequest.Unmarshal(m, b)
}
func (m *GetPostGuideTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostGuideTaskRequest.Marshal(b, m, deterministic)
}
func (dst *GetPostGuideTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostGuideTaskRequest.Merge(dst, src)
}
func (m *GetPostGuideTaskRequest) XXX_Size() int {
	return xxx_messageInfo_GetPostGuideTaskRequest.Size(m)
}
func (m *GetPostGuideTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostGuideTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostGuideTaskRequest proto.InternalMessageInfo

func (m *GetPostGuideTaskRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPostGuideTaskRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetPostGuideTaskResponse struct {
	// 发帖引导任务token
	TaskToken string `protobuf:"bytes,1,opt,name=task_token,json=taskToken,proto3" json:"task_token,omitempty"`
	// 任务是否已完成
	IsFinished           bool     `protobuf:"varint,2,opt,name=is_finished,json=isFinished,proto3" json:"is_finished,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostGuideTaskResponse) Reset()         { *m = GetPostGuideTaskResponse{} }
func (m *GetPostGuideTaskResponse) String() string { return proto.CompactTextString(m) }
func (*GetPostGuideTaskResponse) ProtoMessage()    {}
func (*GetPostGuideTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{81}
}
func (m *GetPostGuideTaskResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostGuideTaskResponse.Unmarshal(m, b)
}
func (m *GetPostGuideTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostGuideTaskResponse.Marshal(b, m, deterministic)
}
func (dst *GetPostGuideTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostGuideTaskResponse.Merge(dst, src)
}
func (m *GetPostGuideTaskResponse) XXX_Size() int {
	return xxx_messageInfo_GetPostGuideTaskResponse.Size(m)
}
func (m *GetPostGuideTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostGuideTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostGuideTaskResponse proto.InternalMessageInfo

func (m *GetPostGuideTaskResponse) GetTaskToken() string {
	if m != nil {
		return m.TaskToken
	}
	return ""
}

func (m *GetPostGuideTaskResponse) GetIsFinished() bool {
	if m != nil {
		return m.IsFinished
	}
	return false
}

type FinishPostGuideTaskRequest struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 角色ID
	RoleId uint32 `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 发帖引导任务token
	TaskToken            string   `protobuf:"bytes,3,opt,name=task_token,json=taskToken,proto3" json:"task_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FinishPostGuideTaskRequest) Reset()         { *m = FinishPostGuideTaskRequest{} }
func (m *FinishPostGuideTaskRequest) String() string { return proto.CompactTextString(m) }
func (*FinishPostGuideTaskRequest) ProtoMessage()    {}
func (*FinishPostGuideTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{82}
}
func (m *FinishPostGuideTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FinishPostGuideTaskRequest.Unmarshal(m, b)
}
func (m *FinishPostGuideTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FinishPostGuideTaskRequest.Marshal(b, m, deterministic)
}
func (dst *FinishPostGuideTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishPostGuideTaskRequest.Merge(dst, src)
}
func (m *FinishPostGuideTaskRequest) XXX_Size() int {
	return xxx_messageInfo_FinishPostGuideTaskRequest.Size(m)
}
func (m *FinishPostGuideTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishPostGuideTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FinishPostGuideTaskRequest proto.InternalMessageInfo

func (m *FinishPostGuideTaskRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FinishPostGuideTaskRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *FinishPostGuideTaskRequest) GetTaskToken() string {
	if m != nil {
		return m.TaskToken
	}
	return ""
}

type FinishPostGuideTaskResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FinishPostGuideTaskResponse) Reset()         { *m = FinishPostGuideTaskResponse{} }
func (m *FinishPostGuideTaskResponse) String() string { return proto.CompactTextString(m) }
func (*FinishPostGuideTaskResponse) ProtoMessage()    {}
func (*FinishPostGuideTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_4ebb26927b1775be, []int{83}
}
func (m *FinishPostGuideTaskResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FinishPostGuideTaskResponse.Unmarshal(m, b)
}
func (m *FinishPostGuideTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FinishPostGuideTaskResponse.Marshal(b, m, deterministic)
}
func (dst *FinishPostGuideTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishPostGuideTaskResponse.Merge(dst, src)
}
func (m *FinishPostGuideTaskResponse) XXX_Size() int {
	return xxx_messageInfo_FinishPostGuideTaskResponse.Size(m)
}
func (m *FinishPostGuideTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishPostGuideTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FinishPostGuideTaskResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*Attachment)(nil), "ugc_community.Attachment")
	proto.RegisterType((*PostBizData)(nil), "ugc_community.PostBizData")
	proto.RegisterType((*Post)(nil), "ugc_community.Post")
	proto.RegisterType((*AigcCommunityPost)(nil), "ugc_community.AigcCommunityPost")
	proto.RegisterType((*AigcCommunityPost_ChatRecord)(nil), "ugc_community.AigcCommunityPost.ChatRecord")
	proto.RegisterType((*PublishPostGuideNotify)(nil), "ugc_community.PublishPostGuideNotify")
	proto.RegisterType((*PostTaskFinishNotify)(nil), "ugc_community.PostTaskFinishNotify")
	proto.RegisterType((*TopicInfo)(nil), "ugc_community.TopicInfo")
	proto.RegisterType((*CreatePostRequest)(nil), "ugc_community.CreatePostRequest")
	proto.RegisterType((*CreatePostRequest_Post)(nil), "ugc_community.CreatePostRequest.Post")
	proto.RegisterType((*CreatePostResponse)(nil), "ugc_community.CreatePostResponse")
	proto.RegisterType((*UpdatePostAuditResultRequest)(nil), "ugc_community.UpdatePostAuditResultRequest")
	proto.RegisterType((*UpdatePostAuditResultResponse)(nil), "ugc_community.UpdatePostAuditResultResponse")
	proto.RegisterType((*BatchDeletePostRequest)(nil), "ugc_community.BatchDeletePostRequest")
	proto.RegisterType((*BatchDeletePostResponse)(nil), "ugc_community.BatchDeletePostResponse")
	proto.RegisterType((*GetPostRequest)(nil), "ugc_community.GetPostRequest")
	proto.RegisterType((*GetPostResponse)(nil), "ugc_community.GetPostResponse")
	proto.RegisterType((*GetPostListRequest)(nil), "ugc_community.GetPostListRequest")
	proto.RegisterType((*GetPostListResponse)(nil), "ugc_community.GetPostListResponse")
	proto.RegisterType((*GetPostListInAuditTimeRangeRequest)(nil), "ugc_community.GetPostListInAuditTimeRangeRequest")
	proto.RegisterType((*GetPostListInAuditTimeRangeResponse)(nil), "ugc_community.GetPostListInAuditTimeRangeResponse")
	proto.RegisterType((*AddUserPostRequest)(nil), "ugc_community.AddUserPostRequest")
	proto.RegisterType((*AddUserPostResponse)(nil), "ugc_community.AddUserPostResponse")
	proto.RegisterType((*DeleteUserPostRequest)(nil), "ugc_community.DeleteUserPostRequest")
	proto.RegisterType((*DeleteUserPostResponse)(nil), "ugc_community.DeleteUserPostResponse")
	proto.RegisterType((*GetUserPostListRequest)(nil), "ugc_community.GetUserPostListRequest")
	proto.RegisterType((*GetUserPostListResponse)(nil), "ugc_community.GetUserPostListResponse")
	proto.RegisterType((*AttitudeObject)(nil), "ugc_community.AttitudeObject")
	proto.RegisterType((*AddAttitudeRequest)(nil), "ugc_community.AddAttitudeRequest")
	proto.RegisterType((*AddAttitudeResponse)(nil), "ugc_community.AddAttitudeResponse")
	proto.RegisterType((*HadAttitudeRequest)(nil), "ugc_community.HadAttitudeRequest")
	proto.RegisterType((*HadAttitudeResponse)(nil), "ugc_community.HadAttitudeResponse")
	proto.RegisterMapType((map[string]bool)(nil), "ugc_community.HadAttitudeResponse.CommentHadAttitudeMapEntry")
	proto.RegisterMapType((map[string]bool)(nil), "ugc_community.HadAttitudeResponse.PostHadAttitudeMapEntry")
	proto.RegisterType((*CommentSendRequest)(nil), "ugc_community.CommentSendRequest")
	proto.RegisterType((*CommentSendResponse)(nil), "ugc_community.CommentSendResponse")
	proto.RegisterType((*CommentEntity)(nil), "ugc_community.CommentEntity")
	proto.RegisterType((*CommentFetchRequest)(nil), "ugc_community.CommentFetchRequest")
	proto.RegisterType((*CommentItem)(nil), "ugc_community.CommentItem")
	proto.RegisterType((*CommentItem_SecondCommentInfo)(nil), "ugc_community.CommentItem.SecondCommentInfo")
	proto.RegisterType((*CommentFetchResponse)(nil), "ugc_community.CommentFetchResponse")
	proto.RegisterType((*GetPostHotCommentRequest)(nil), "ugc_community.GetPostHotCommentRequest")
	proto.RegisterType((*GetPostHotCommentResponse)(nil), "ugc_community.GetPostHotCommentResponse")
	proto.RegisterMapType((map[string]*CommentItem)(nil), "ugc_community.GetPostHotCommentResponse.HotCommentsEntry")
	proto.RegisterType((*CommentDeleteRequest)(nil), "ugc_community.CommentDeleteRequest")
	proto.RegisterType((*CommentDeleteResponse)(nil), "ugc_community.CommentDeleteResponse")
	proto.RegisterType((*GetCommentInfoByIdRequest)(nil), "ugc_community.GetCommentInfoByIdRequest")
	proto.RegisterType((*GetCommentInfoByIdResponse)(nil), "ugc_community.GetCommentInfoByIdResponse")
	proto.RegisterType((*GetAICommentCntInfoRequest)(nil), "ugc_community.GetAICommentCntInfoRequest")
	proto.RegisterType((*GetAICommentCntInfoResponse)(nil), "ugc_community.GetAICommentCntInfoResponse")
	proto.RegisterType((*BatchGetPostAICommentCntRequest)(nil), "ugc_community.BatchGetPostAICommentCntRequest")
	proto.RegisterType((*BatchGetPostAICommentCntResponse)(nil), "ugc_community.BatchGetPostAICommentCntResponse")
	proto.RegisterMapType((map[string]uint32)(nil), "ugc_community.BatchGetPostAICommentCntResponse.CntMapEntry")
	proto.RegisterType((*SearchPostRequest)(nil), "ugc_community.SearchPostRequest")
	proto.RegisterType((*BackendPost)(nil), "ugc_community.BackendPost")
	proto.RegisterType((*SearchPostResponse)(nil), "ugc_community.SearchPostResponse")
	proto.RegisterType((*OfficialHandlePostRequest)(nil), "ugc_community.OfficialHandlePostRequest")
	proto.RegisterType((*OfficialHandlePostRequest_PostInfo)(nil), "ugc_community.OfficialHandlePostRequest.PostInfo")
	proto.RegisterType((*OfficialHandlePostResponse)(nil), "ugc_community.OfficialHandlePostResponse")
	proto.RegisterType((*AigcBizPost)(nil), "ugc_community.AigcBizPost")
	proto.RegisterType((*UpdateAigcBizPostRequest)(nil), "ugc_community.UpdateAigcBizPostRequest")
	proto.RegisterType((*UpdateAigcBizPostRequest_HandleInfo)(nil), "ugc_community.UpdateAigcBizPostRequest.HandleInfo")
	proto.RegisterType((*UpdateAigcBizPostResponse)(nil), "ugc_community.UpdateAigcBizPostResponse")
	proto.RegisterType((*StickyPostInfo)(nil), "ugc_community.StickyPostInfo")
	proto.RegisterType((*UpsertSubjectTabRequest)(nil), "ugc_community.UpsertSubjectTabRequest")
	proto.RegisterType((*UpsertSubjectTabRequest_Tab)(nil), "ugc_community.UpsertSubjectTabRequest.Tab")
	proto.RegisterType((*SubjectTabInfo)(nil), "ugc_community.SubjectTabInfo")
	proto.RegisterType((*UpsertSubjectTabResponse)(nil), "ugc_community.UpsertSubjectTabResponse")
	proto.RegisterType((*GetSubjectTabListRequest)(nil), "ugc_community.GetSubjectTabListRequest")
	proto.RegisterType((*GetSubjectTabListResponse)(nil), "ugc_community.GetSubjectTabListResponse")
	proto.RegisterType((*GetSubjectTabByIdsRequest)(nil), "ugc_community.GetSubjectTabByIdsRequest")
	proto.RegisterType((*GetSubjectTabByIdsResponse)(nil), "ugc_community.GetSubjectTabByIdsResponse")
	proto.RegisterType((*DeleteSubjectTabRequest)(nil), "ugc_community.DeleteSubjectTabRequest")
	proto.RegisterType((*DeleteSubjectTabResponse)(nil), "ugc_community.DeleteSubjectTabResponse")
	proto.RegisterType((*ResortSubjectTabRequest)(nil), "ugc_community.ResortSubjectTabRequest")
	proto.RegisterType((*ResortSubjectTabResponse)(nil), "ugc_community.ResortSubjectTabResponse")
	proto.RegisterType((*CreateTopicRequest)(nil), "ugc_community.CreateTopicRequest")
	proto.RegisterType((*CreateTopicRequest_Topic)(nil), "ugc_community.CreateTopicRequest.Topic")
	proto.RegisterType((*CreateTopicResponse)(nil), "ugc_community.CreateTopicResponse")
	proto.RegisterType((*UpdateTopicRequest)(nil), "ugc_community.UpdateTopicRequest")
	proto.RegisterType((*UpdateTopicRequest_Topic)(nil), "ugc_community.UpdateTopicRequest.Topic")
	proto.RegisterType((*UpdateTopicResponse)(nil), "ugc_community.UpdateTopicResponse")
	proto.RegisterType((*BatchDeleteTopicRequest)(nil), "ugc_community.BatchDeleteTopicRequest")
	proto.RegisterType((*BatchDeleteTopicResponse)(nil), "ugc_community.BatchDeleteTopicResponse")
	proto.RegisterType((*GetTopicListRequest)(nil), "ugc_community.GetTopicListRequest")
	proto.RegisterType((*GetTopicListResponse)(nil), "ugc_community.GetTopicListResponse")
	proto.RegisterType((*BatchGetTopicRequest)(nil), "ugc_community.BatchGetTopicRequest")
	proto.RegisterType((*BatchGetTopicResponse)(nil), "ugc_community.BatchGetTopicResponse")
	proto.RegisterMapType((map[string]*TopicInfo)(nil), "ugc_community.BatchGetTopicResponse.TopicsEntry")
	proto.RegisterType((*GetTopicRequest)(nil), "ugc_community.GetTopicRequest")
	proto.RegisterType((*GetTopicResponse)(nil), "ugc_community.GetTopicResponse")
	proto.RegisterType((*AddPostGuideTaskRequest)(nil), "ugc_community.AddPostGuideTaskRequest")
	proto.RegisterType((*AddPostGuideTaskResponse)(nil), "ugc_community.AddPostGuideTaskResponse")
	proto.RegisterType((*GetPostGuideTaskRequest)(nil), "ugc_community.GetPostGuideTaskRequest")
	proto.RegisterType((*GetPostGuideTaskResponse)(nil), "ugc_community.GetPostGuideTaskResponse")
	proto.RegisterType((*FinishPostGuideTaskRequest)(nil), "ugc_community.FinishPostGuideTaskRequest")
	proto.RegisterType((*FinishPostGuideTaskResponse)(nil), "ugc_community.FinishPostGuideTaskResponse")
	proto.RegisterEnum("ugc_community.PostType", PostType_name, PostType_value)
	proto.RegisterEnum("ugc_community.PostBizType", PostBizType_name, PostBizType_value)
	proto.RegisterEnum("ugc_community.PostOrigin", PostOrigin_name, PostOrigin_value)
	proto.RegisterEnum("ugc_community.PostState", PostState_name, PostState_value)
	proto.RegisterEnum("ugc_community.AuditResult", AuditResult_name, AuditResult_value)
	proto.RegisterEnum("ugc_community.PostStatus", PostStatus_name, PostStatus_value)
	proto.RegisterEnum("ugc_community.TopicType", TopicType_name, TopicType_value)
	proto.RegisterEnum("ugc_community.ObjectType", ObjectType_name, ObjectType_value)
	proto.RegisterEnum("ugc_community.AttitudeAction", AttitudeAction_name, AttitudeAction_value)
	proto.RegisterEnum("ugc_community.CommentEntityType", CommentEntityType_name, CommentEntityType_value)
	proto.RegisterEnum("ugc_community.CommentOrigin", CommentOrigin_name, CommentOrigin_value)
	proto.RegisterEnum("ugc_community.AigcPostBizState", AigcPostBizState_name, AigcPostBizState_value)
	proto.RegisterEnum("ugc_community.BoolFilter", BoolFilter_name, BoolFilter_value)
	proto.RegisterEnum("ugc_community.SubjectType", SubjectType_name, SubjectType_value)
	proto.RegisterEnum("ugc_community.Attachment_Type", Attachment_Type_name, Attachment_Type_value)
	proto.RegisterEnum("ugc_community.PostBizData_Format", PostBizData_Format_name, PostBizData_Format_value)
	proto.RegisterEnum("ugc_community.AigcCommunityPost_ChatRecord_Sender", AigcCommunityPost_ChatRecord_Sender_name, AigcCommunityPost_ChatRecord_Sender_value)
	proto.RegisterEnum("ugc_community.BatchDeletePostRequest_Source", BatchDeletePostRequest_Source_name, BatchDeletePostRequest_Source_value)
	proto.RegisterEnum("ugc_community.UpdateAigcBizPostRequest_Action", UpdateAigcBizPostRequest_Action_name, UpdateAigcBizPostRequest_Action_value)
	proto.RegisterEnum("ugc_community.UpsertSubjectTabRequest_OpType", UpsertSubjectTabRequest_OpType_name, UpsertSubjectTabRequest_OpType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UgcCommunityClient is the client API for UgcCommunity service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UgcCommunityClient interface {
	// 创建帖子
	CreatePost(ctx context.Context, in *CreatePostRequest, opts ...grpc.CallOption) (*CreatePostResponse, error)
	// 更新帖子审核状态
	UpdatePostAuditResult(ctx context.Context, in *UpdatePostAuditResultRequest, opts ...grpc.CallOption) (*UpdatePostAuditResultResponse, error)
	// 根据ID批量删除帖子
	BatchDeletePost(ctx context.Context, in *BatchDeletePostRequest, opts ...grpc.CallOption) (*BatchDeletePostResponse, error)
	// 根据ID获取帖子详情
	GetPost(ctx context.Context, in *GetPostRequest, opts ...grpc.CallOption) (*GetPostResponse, error)
	// 根据ID列表获取帖子列表
	GetPostList(ctx context.Context, in *GetPostListRequest, opts ...grpc.CallOption) (*GetPostListResponse, error)
	// 获取指定送审时间范围内的帖子列表
	GetPostListInAuditTimeRange(ctx context.Context, in *GetPostListInAuditTimeRangeRequest, opts ...grpc.CallOption) (*GetPostListInAuditTimeRangeResponse, error)
	// 用户关联帖子
	AddUserPost(ctx context.Context, in *AddUserPostRequest, opts ...grpc.CallOption) (*AddUserPostResponse, error)
	// 用户取消关联帖子
	DeleteUserPost(ctx context.Context, in *DeleteUserPostRequest, opts ...grpc.CallOption) (*DeleteUserPostResponse, error)
	// 获取用户帖子列表
	GetUserPostList(ctx context.Context, in *GetUserPostListRequest, opts ...grpc.CallOption) (*GetUserPostListResponse, error)
	// 点赞
	AddAttitude(ctx context.Context, in *AddAttitudeRequest, opts ...grpc.CallOption) (*AddAttitudeResponse, error)
	// 查询用户是否点赞
	HadAttitude(ctx context.Context, in *HadAttitudeRequest, opts ...grpc.CallOption) (*HadAttitudeResponse, error)
	// 官方操作帖子（删除屏蔽置顶）
	OfficialHandlePost(ctx context.Context, in *OfficialHandlePostRequest, opts ...grpc.CallOption) (*OfficialHandlePostResponse, error)
	// 运营后台搜索帖子
	SearchPost(ctx context.Context, in *SearchPostRequest, opts ...grpc.CallOption) (*SearchPostResponse, error)
	// 更新aigc业务表，消费帖子事件触发
	UpdateAigcBizPost(ctx context.Context, in *UpdateAigcBizPostRequest, opts ...grpc.CallOption) (*UpdateAigcBizPostResponse, error)
	// 评论操作
	CommentSend(ctx context.Context, in *CommentSendRequest, opts ...grpc.CallOption) (*CommentSendResponse, error)
	CommentFetch(ctx context.Context, in *CommentFetchRequest, opts ...grpc.CallOption) (*CommentFetchResponse, error)
	CommentDelete(ctx context.Context, in *CommentDeleteRequest, opts ...grpc.CallOption) (*CommentDeleteResponse, error)
	GetPostHotComment(ctx context.Context, in *GetPostHotCommentRequest, opts ...grpc.CallOption) (*GetPostHotCommentResponse, error)
	GetCommentInfoById(ctx context.Context, in *GetCommentInfoByIdRequest, opts ...grpc.CallOption) (*GetCommentInfoByIdResponse, error)
	GetAICommentCntInfo(ctx context.Context, in *GetAICommentCntInfoRequest, opts ...grpc.CallOption) (*GetAICommentCntInfoResponse, error)
	BatchGetPostAICommentCnt(ctx context.Context, in *BatchGetPostAICommentCntRequest, opts ...grpc.CallOption) (*BatchGetPostAICommentCntResponse, error)
	// 创建修改主题
	UpsertSubjectTab(ctx context.Context, in *UpsertSubjectTabRequest, opts ...grpc.CallOption) (*UpsertSubjectTabResponse, error)
	// 获取主题列表
	GetSubjectTabList(ctx context.Context, in *GetSubjectTabListRequest, opts ...grpc.CallOption) (*GetSubjectTabListResponse, error)
	// 根据id获取主题列表
	GetSubjectTabByIds(ctx context.Context, in *GetSubjectTabByIdsRequest, opts ...grpc.CallOption) (*GetSubjectTabByIdsResponse, error)
	// 删除主题
	DeleteSubjectTab(ctx context.Context, in *DeleteSubjectTabRequest, opts ...grpc.CallOption) (*DeleteSubjectTabResponse, error)
	// 主题排序
	ResortSubjectTab(ctx context.Context, in *ResortSubjectTabRequest, opts ...grpc.CallOption) (*ResortSubjectTabResponse, error)
	// 创建话题
	CreateTopic(ctx context.Context, in *CreateTopicRequest, opts ...grpc.CallOption) (*CreateTopicResponse, error)
	// 修改话题
	UpdateTopic(ctx context.Context, in *UpdateTopicRequest, opts ...grpc.CallOption) (*UpdateTopicResponse, error)
	// 根据id获取话题
	GetTopic(ctx context.Context, in *GetTopicRequest, opts ...grpc.CallOption) (*GetTopicResponse, error)
	// 删除话题
	BatchDeleteTopic(ctx context.Context, in *BatchDeleteTopicRequest, opts ...grpc.CallOption) (*BatchDeleteTopicResponse, error)
	// 获取话题列表
	GetTopicList(ctx context.Context, in *GetTopicListRequest, opts ...grpc.CallOption) (*GetTopicListResponse, error)
	// 根据id获取话题map
	BatchGetTopic(ctx context.Context, in *BatchGetTopicRequest, opts ...grpc.CallOption) (*BatchGetTopicResponse, error)
	// 增加发帖引导任务
	AddPostGuideTask(ctx context.Context, in *AddPostGuideTaskRequest, opts ...grpc.CallOption) (*AddPostGuideTaskResponse, error)
	// 获取发帖引导任务
	GetPostGuideTask(ctx context.Context, in *GetPostGuideTaskRequest, opts ...grpc.CallOption) (*GetPostGuideTaskResponse, error)
	// 完成发帖引导任务
	FinishPostGuideTask(ctx context.Context, in *FinishPostGuideTaskRequest, opts ...grpc.CallOption) (*FinishPostGuideTaskResponse, error)
}

type ugcCommunityClient struct {
	cc *grpc.ClientConn
}

func NewUgcCommunityClient(cc *grpc.ClientConn) UgcCommunityClient {
	return &ugcCommunityClient{cc}
}

func (c *ugcCommunityClient) CreatePost(ctx context.Context, in *CreatePostRequest, opts ...grpc.CallOption) (*CreatePostResponse, error) {
	out := new(CreatePostResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/CreatePost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) UpdatePostAuditResult(ctx context.Context, in *UpdatePostAuditResultRequest, opts ...grpc.CallOption) (*UpdatePostAuditResultResponse, error) {
	out := new(UpdatePostAuditResultResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/UpdatePostAuditResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) BatchDeletePost(ctx context.Context, in *BatchDeletePostRequest, opts ...grpc.CallOption) (*BatchDeletePostResponse, error) {
	out := new(BatchDeletePostResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/BatchDeletePost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) GetPost(ctx context.Context, in *GetPostRequest, opts ...grpc.CallOption) (*GetPostResponse, error) {
	out := new(GetPostResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/GetPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) GetPostList(ctx context.Context, in *GetPostListRequest, opts ...grpc.CallOption) (*GetPostListResponse, error) {
	out := new(GetPostListResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/GetPostList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) GetPostListInAuditTimeRange(ctx context.Context, in *GetPostListInAuditTimeRangeRequest, opts ...grpc.CallOption) (*GetPostListInAuditTimeRangeResponse, error) {
	out := new(GetPostListInAuditTimeRangeResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/GetPostListInAuditTimeRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) AddUserPost(ctx context.Context, in *AddUserPostRequest, opts ...grpc.CallOption) (*AddUserPostResponse, error) {
	out := new(AddUserPostResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/AddUserPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) DeleteUserPost(ctx context.Context, in *DeleteUserPostRequest, opts ...grpc.CallOption) (*DeleteUserPostResponse, error) {
	out := new(DeleteUserPostResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/DeleteUserPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) GetUserPostList(ctx context.Context, in *GetUserPostListRequest, opts ...grpc.CallOption) (*GetUserPostListResponse, error) {
	out := new(GetUserPostListResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/GetUserPostList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) AddAttitude(ctx context.Context, in *AddAttitudeRequest, opts ...grpc.CallOption) (*AddAttitudeResponse, error) {
	out := new(AddAttitudeResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/AddAttitude", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) HadAttitude(ctx context.Context, in *HadAttitudeRequest, opts ...grpc.CallOption) (*HadAttitudeResponse, error) {
	out := new(HadAttitudeResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/HadAttitude", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) OfficialHandlePost(ctx context.Context, in *OfficialHandlePostRequest, opts ...grpc.CallOption) (*OfficialHandlePostResponse, error) {
	out := new(OfficialHandlePostResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/OfficialHandlePost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) SearchPost(ctx context.Context, in *SearchPostRequest, opts ...grpc.CallOption) (*SearchPostResponse, error) {
	out := new(SearchPostResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/SearchPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) UpdateAigcBizPost(ctx context.Context, in *UpdateAigcBizPostRequest, opts ...grpc.CallOption) (*UpdateAigcBizPostResponse, error) {
	out := new(UpdateAigcBizPostResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/UpdateAigcBizPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) CommentSend(ctx context.Context, in *CommentSendRequest, opts ...grpc.CallOption) (*CommentSendResponse, error) {
	out := new(CommentSendResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/CommentSend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) CommentFetch(ctx context.Context, in *CommentFetchRequest, opts ...grpc.CallOption) (*CommentFetchResponse, error) {
	out := new(CommentFetchResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/CommentFetch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) CommentDelete(ctx context.Context, in *CommentDeleteRequest, opts ...grpc.CallOption) (*CommentDeleteResponse, error) {
	out := new(CommentDeleteResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/CommentDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) GetPostHotComment(ctx context.Context, in *GetPostHotCommentRequest, opts ...grpc.CallOption) (*GetPostHotCommentResponse, error) {
	out := new(GetPostHotCommentResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/GetPostHotComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) GetCommentInfoById(ctx context.Context, in *GetCommentInfoByIdRequest, opts ...grpc.CallOption) (*GetCommentInfoByIdResponse, error) {
	out := new(GetCommentInfoByIdResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/GetCommentInfoById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) GetAICommentCntInfo(ctx context.Context, in *GetAICommentCntInfoRequest, opts ...grpc.CallOption) (*GetAICommentCntInfoResponse, error) {
	out := new(GetAICommentCntInfoResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/GetAICommentCntInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) BatchGetPostAICommentCnt(ctx context.Context, in *BatchGetPostAICommentCntRequest, opts ...grpc.CallOption) (*BatchGetPostAICommentCntResponse, error) {
	out := new(BatchGetPostAICommentCntResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/BatchGetPostAICommentCnt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) UpsertSubjectTab(ctx context.Context, in *UpsertSubjectTabRequest, opts ...grpc.CallOption) (*UpsertSubjectTabResponse, error) {
	out := new(UpsertSubjectTabResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/UpsertSubjectTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) GetSubjectTabList(ctx context.Context, in *GetSubjectTabListRequest, opts ...grpc.CallOption) (*GetSubjectTabListResponse, error) {
	out := new(GetSubjectTabListResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/GetSubjectTabList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) GetSubjectTabByIds(ctx context.Context, in *GetSubjectTabByIdsRequest, opts ...grpc.CallOption) (*GetSubjectTabByIdsResponse, error) {
	out := new(GetSubjectTabByIdsResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/GetSubjectTabByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) DeleteSubjectTab(ctx context.Context, in *DeleteSubjectTabRequest, opts ...grpc.CallOption) (*DeleteSubjectTabResponse, error) {
	out := new(DeleteSubjectTabResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/DeleteSubjectTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) ResortSubjectTab(ctx context.Context, in *ResortSubjectTabRequest, opts ...grpc.CallOption) (*ResortSubjectTabResponse, error) {
	out := new(ResortSubjectTabResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/ResortSubjectTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) CreateTopic(ctx context.Context, in *CreateTopicRequest, opts ...grpc.CallOption) (*CreateTopicResponse, error) {
	out := new(CreateTopicResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/CreateTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) UpdateTopic(ctx context.Context, in *UpdateTopicRequest, opts ...grpc.CallOption) (*UpdateTopicResponse, error) {
	out := new(UpdateTopicResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/UpdateTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) GetTopic(ctx context.Context, in *GetTopicRequest, opts ...grpc.CallOption) (*GetTopicResponse, error) {
	out := new(GetTopicResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/GetTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) BatchDeleteTopic(ctx context.Context, in *BatchDeleteTopicRequest, opts ...grpc.CallOption) (*BatchDeleteTopicResponse, error) {
	out := new(BatchDeleteTopicResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/BatchDeleteTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) GetTopicList(ctx context.Context, in *GetTopicListRequest, opts ...grpc.CallOption) (*GetTopicListResponse, error) {
	out := new(GetTopicListResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/GetTopicList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) BatchGetTopic(ctx context.Context, in *BatchGetTopicRequest, opts ...grpc.CallOption) (*BatchGetTopicResponse, error) {
	out := new(BatchGetTopicResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/BatchGetTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) AddPostGuideTask(ctx context.Context, in *AddPostGuideTaskRequest, opts ...grpc.CallOption) (*AddPostGuideTaskResponse, error) {
	out := new(AddPostGuideTaskResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/AddPostGuideTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) GetPostGuideTask(ctx context.Context, in *GetPostGuideTaskRequest, opts ...grpc.CallOption) (*GetPostGuideTaskResponse, error) {
	out := new(GetPostGuideTaskResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/GetPostGuideTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityClient) FinishPostGuideTask(ctx context.Context, in *FinishPostGuideTaskRequest, opts ...grpc.CallOption) (*FinishPostGuideTaskResponse, error) {
	out := new(FinishPostGuideTaskResponse)
	err := c.cc.Invoke(ctx, "/ugc_community.UgcCommunity/FinishPostGuideTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UgcCommunityServer is the server API for UgcCommunity service.
type UgcCommunityServer interface {
	// 创建帖子
	CreatePost(context.Context, *CreatePostRequest) (*CreatePostResponse, error)
	// 更新帖子审核状态
	UpdatePostAuditResult(context.Context, *UpdatePostAuditResultRequest) (*UpdatePostAuditResultResponse, error)
	// 根据ID批量删除帖子
	BatchDeletePost(context.Context, *BatchDeletePostRequest) (*BatchDeletePostResponse, error)
	// 根据ID获取帖子详情
	GetPost(context.Context, *GetPostRequest) (*GetPostResponse, error)
	// 根据ID列表获取帖子列表
	GetPostList(context.Context, *GetPostListRequest) (*GetPostListResponse, error)
	// 获取指定送审时间范围内的帖子列表
	GetPostListInAuditTimeRange(context.Context, *GetPostListInAuditTimeRangeRequest) (*GetPostListInAuditTimeRangeResponse, error)
	// 用户关联帖子
	AddUserPost(context.Context, *AddUserPostRequest) (*AddUserPostResponse, error)
	// 用户取消关联帖子
	DeleteUserPost(context.Context, *DeleteUserPostRequest) (*DeleteUserPostResponse, error)
	// 获取用户帖子列表
	GetUserPostList(context.Context, *GetUserPostListRequest) (*GetUserPostListResponse, error)
	// 点赞
	AddAttitude(context.Context, *AddAttitudeRequest) (*AddAttitudeResponse, error)
	// 查询用户是否点赞
	HadAttitude(context.Context, *HadAttitudeRequest) (*HadAttitudeResponse, error)
	// 官方操作帖子（删除屏蔽置顶）
	OfficialHandlePost(context.Context, *OfficialHandlePostRequest) (*OfficialHandlePostResponse, error)
	// 运营后台搜索帖子
	SearchPost(context.Context, *SearchPostRequest) (*SearchPostResponse, error)
	// 更新aigc业务表，消费帖子事件触发
	UpdateAigcBizPost(context.Context, *UpdateAigcBizPostRequest) (*UpdateAigcBizPostResponse, error)
	// 评论操作
	CommentSend(context.Context, *CommentSendRequest) (*CommentSendResponse, error)
	CommentFetch(context.Context, *CommentFetchRequest) (*CommentFetchResponse, error)
	CommentDelete(context.Context, *CommentDeleteRequest) (*CommentDeleteResponse, error)
	GetPostHotComment(context.Context, *GetPostHotCommentRequest) (*GetPostHotCommentResponse, error)
	GetCommentInfoById(context.Context, *GetCommentInfoByIdRequest) (*GetCommentInfoByIdResponse, error)
	GetAICommentCntInfo(context.Context, *GetAICommentCntInfoRequest) (*GetAICommentCntInfoResponse, error)
	BatchGetPostAICommentCnt(context.Context, *BatchGetPostAICommentCntRequest) (*BatchGetPostAICommentCntResponse, error)
	// 创建修改主题
	UpsertSubjectTab(context.Context, *UpsertSubjectTabRequest) (*UpsertSubjectTabResponse, error)
	// 获取主题列表
	GetSubjectTabList(context.Context, *GetSubjectTabListRequest) (*GetSubjectTabListResponse, error)
	// 根据id获取主题列表
	GetSubjectTabByIds(context.Context, *GetSubjectTabByIdsRequest) (*GetSubjectTabByIdsResponse, error)
	// 删除主题
	DeleteSubjectTab(context.Context, *DeleteSubjectTabRequest) (*DeleteSubjectTabResponse, error)
	// 主题排序
	ResortSubjectTab(context.Context, *ResortSubjectTabRequest) (*ResortSubjectTabResponse, error)
	// 创建话题
	CreateTopic(context.Context, *CreateTopicRequest) (*CreateTopicResponse, error)
	// 修改话题
	UpdateTopic(context.Context, *UpdateTopicRequest) (*UpdateTopicResponse, error)
	// 根据id获取话题
	GetTopic(context.Context, *GetTopicRequest) (*GetTopicResponse, error)
	// 删除话题
	BatchDeleteTopic(context.Context, *BatchDeleteTopicRequest) (*BatchDeleteTopicResponse, error)
	// 获取话题列表
	GetTopicList(context.Context, *GetTopicListRequest) (*GetTopicListResponse, error)
	// 根据id获取话题map
	BatchGetTopic(context.Context, *BatchGetTopicRequest) (*BatchGetTopicResponse, error)
	// 增加发帖引导任务
	AddPostGuideTask(context.Context, *AddPostGuideTaskRequest) (*AddPostGuideTaskResponse, error)
	// 获取发帖引导任务
	GetPostGuideTask(context.Context, *GetPostGuideTaskRequest) (*GetPostGuideTaskResponse, error)
	// 完成发帖引导任务
	FinishPostGuideTask(context.Context, *FinishPostGuideTaskRequest) (*FinishPostGuideTaskResponse, error)
}

func RegisterUgcCommunityServer(s *grpc.Server, srv UgcCommunityServer) {
	s.RegisterService(&_UgcCommunity_serviceDesc, srv)
}

func _UgcCommunity_CreatePost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).CreatePost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/CreatePost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).CreatePost(ctx, req.(*CreatePostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_UpdatePostAuditResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePostAuditResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).UpdatePostAuditResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/UpdatePostAuditResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).UpdatePostAuditResult(ctx, req.(*UpdatePostAuditResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_BatchDeletePost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeletePostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).BatchDeletePost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/BatchDeletePost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).BatchDeletePost(ctx, req.(*BatchDeletePostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_GetPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).GetPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/GetPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).GetPost(ctx, req.(*GetPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_GetPostList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).GetPostList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/GetPostList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).GetPostList(ctx, req.(*GetPostListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_GetPostListInAuditTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostListInAuditTimeRangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).GetPostListInAuditTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/GetPostListInAuditTimeRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).GetPostListInAuditTimeRange(ctx, req.(*GetPostListInAuditTimeRangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_AddUserPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).AddUserPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/AddUserPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).AddUserPost(ctx, req.(*AddUserPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_DeleteUserPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).DeleteUserPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/DeleteUserPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).DeleteUserPost(ctx, req.(*DeleteUserPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_GetUserPostList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPostListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).GetUserPostList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/GetUserPostList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).GetUserPostList(ctx, req.(*GetUserPostListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_AddAttitude_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAttitudeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).AddAttitude(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/AddAttitude",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).AddAttitude(ctx, req.(*AddAttitudeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_HadAttitude_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HadAttitudeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).HadAttitude(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/HadAttitude",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).HadAttitude(ctx, req.(*HadAttitudeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_OfficialHandlePost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OfficialHandlePostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).OfficialHandlePost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/OfficialHandlePost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).OfficialHandlePost(ctx, req.(*OfficialHandlePostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_SearchPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).SearchPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/SearchPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).SearchPost(ctx, req.(*SearchPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_UpdateAigcBizPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAigcBizPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).UpdateAigcBizPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/UpdateAigcBizPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).UpdateAigcBizPost(ctx, req.(*UpdateAigcBizPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_CommentSend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommentSendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).CommentSend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/CommentSend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).CommentSend(ctx, req.(*CommentSendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_CommentFetch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommentFetchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).CommentFetch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/CommentFetch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).CommentFetch(ctx, req.(*CommentFetchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_CommentDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommentDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).CommentDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/CommentDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).CommentDelete(ctx, req.(*CommentDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_GetPostHotComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostHotCommentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).GetPostHotComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/GetPostHotComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).GetPostHotComment(ctx, req.(*GetPostHotCommentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_GetCommentInfoById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommentInfoByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).GetCommentInfoById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/GetCommentInfoById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).GetCommentInfoById(ctx, req.(*GetCommentInfoByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_GetAICommentCntInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAICommentCntInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).GetAICommentCntInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/GetAICommentCntInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).GetAICommentCntInfo(ctx, req.(*GetAICommentCntInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_BatchGetPostAICommentCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetPostAICommentCntRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).BatchGetPostAICommentCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/BatchGetPostAICommentCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).BatchGetPostAICommentCnt(ctx, req.(*BatchGetPostAICommentCntRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_UpsertSubjectTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertSubjectTabRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).UpsertSubjectTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/UpsertSubjectTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).UpsertSubjectTab(ctx, req.(*UpsertSubjectTabRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_GetSubjectTabList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubjectTabListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).GetSubjectTabList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/GetSubjectTabList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).GetSubjectTabList(ctx, req.(*GetSubjectTabListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_GetSubjectTabByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubjectTabByIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).GetSubjectTabByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/GetSubjectTabByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).GetSubjectTabByIds(ctx, req.(*GetSubjectTabByIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_DeleteSubjectTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSubjectTabRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).DeleteSubjectTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/DeleteSubjectTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).DeleteSubjectTab(ctx, req.(*DeleteSubjectTabRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_ResortSubjectTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResortSubjectTabRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).ResortSubjectTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/ResortSubjectTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).ResortSubjectTab(ctx, req.(*ResortSubjectTabRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_CreateTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTopicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).CreateTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/CreateTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).CreateTopic(ctx, req.(*CreateTopicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_UpdateTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTopicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).UpdateTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/UpdateTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).UpdateTopic(ctx, req.(*UpdateTopicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_GetTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).GetTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/GetTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).GetTopic(ctx, req.(*GetTopicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_BatchDeleteTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteTopicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).BatchDeleteTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/BatchDeleteTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).BatchDeleteTopic(ctx, req.(*BatchDeleteTopicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_GetTopicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).GetTopicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/GetTopicList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).GetTopicList(ctx, req.(*GetTopicListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_BatchGetTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetTopicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).BatchGetTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/BatchGetTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).BatchGetTopic(ctx, req.(*BatchGetTopicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_AddPostGuideTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPostGuideTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).AddPostGuideTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/AddPostGuideTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).AddPostGuideTask(ctx, req.(*AddPostGuideTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_GetPostGuideTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostGuideTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).GetPostGuideTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/GetPostGuideTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).GetPostGuideTask(ctx, req.(*GetPostGuideTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunity_FinishPostGuideTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FinishPostGuideTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityServer).FinishPostGuideTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community.UgcCommunity/FinishPostGuideTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityServer).FinishPostGuideTask(ctx, req.(*FinishPostGuideTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _UgcCommunity_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc_community.UgcCommunity",
	HandlerType: (*UgcCommunityServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePost",
			Handler:    _UgcCommunity_CreatePost_Handler,
		},
		{
			MethodName: "UpdatePostAuditResult",
			Handler:    _UgcCommunity_UpdatePostAuditResult_Handler,
		},
		{
			MethodName: "BatchDeletePost",
			Handler:    _UgcCommunity_BatchDeletePost_Handler,
		},
		{
			MethodName: "GetPost",
			Handler:    _UgcCommunity_GetPost_Handler,
		},
		{
			MethodName: "GetPostList",
			Handler:    _UgcCommunity_GetPostList_Handler,
		},
		{
			MethodName: "GetPostListInAuditTimeRange",
			Handler:    _UgcCommunity_GetPostListInAuditTimeRange_Handler,
		},
		{
			MethodName: "AddUserPost",
			Handler:    _UgcCommunity_AddUserPost_Handler,
		},
		{
			MethodName: "DeleteUserPost",
			Handler:    _UgcCommunity_DeleteUserPost_Handler,
		},
		{
			MethodName: "GetUserPostList",
			Handler:    _UgcCommunity_GetUserPostList_Handler,
		},
		{
			MethodName: "AddAttitude",
			Handler:    _UgcCommunity_AddAttitude_Handler,
		},
		{
			MethodName: "HadAttitude",
			Handler:    _UgcCommunity_HadAttitude_Handler,
		},
		{
			MethodName: "OfficialHandlePost",
			Handler:    _UgcCommunity_OfficialHandlePost_Handler,
		},
		{
			MethodName: "SearchPost",
			Handler:    _UgcCommunity_SearchPost_Handler,
		},
		{
			MethodName: "UpdateAigcBizPost",
			Handler:    _UgcCommunity_UpdateAigcBizPost_Handler,
		},
		{
			MethodName: "CommentSend",
			Handler:    _UgcCommunity_CommentSend_Handler,
		},
		{
			MethodName: "CommentFetch",
			Handler:    _UgcCommunity_CommentFetch_Handler,
		},
		{
			MethodName: "CommentDelete",
			Handler:    _UgcCommunity_CommentDelete_Handler,
		},
		{
			MethodName: "GetPostHotComment",
			Handler:    _UgcCommunity_GetPostHotComment_Handler,
		},
		{
			MethodName: "GetCommentInfoById",
			Handler:    _UgcCommunity_GetCommentInfoById_Handler,
		},
		{
			MethodName: "GetAICommentCntInfo",
			Handler:    _UgcCommunity_GetAICommentCntInfo_Handler,
		},
		{
			MethodName: "BatchGetPostAICommentCnt",
			Handler:    _UgcCommunity_BatchGetPostAICommentCnt_Handler,
		},
		{
			MethodName: "UpsertSubjectTab",
			Handler:    _UgcCommunity_UpsertSubjectTab_Handler,
		},
		{
			MethodName: "GetSubjectTabList",
			Handler:    _UgcCommunity_GetSubjectTabList_Handler,
		},
		{
			MethodName: "GetSubjectTabByIds",
			Handler:    _UgcCommunity_GetSubjectTabByIds_Handler,
		},
		{
			MethodName: "DeleteSubjectTab",
			Handler:    _UgcCommunity_DeleteSubjectTab_Handler,
		},
		{
			MethodName: "ResortSubjectTab",
			Handler:    _UgcCommunity_ResortSubjectTab_Handler,
		},
		{
			MethodName: "CreateTopic",
			Handler:    _UgcCommunity_CreateTopic_Handler,
		},
		{
			MethodName: "UpdateTopic",
			Handler:    _UgcCommunity_UpdateTopic_Handler,
		},
		{
			MethodName: "GetTopic",
			Handler:    _UgcCommunity_GetTopic_Handler,
		},
		{
			MethodName: "BatchDeleteTopic",
			Handler:    _UgcCommunity_BatchDeleteTopic_Handler,
		},
		{
			MethodName: "GetTopicList",
			Handler:    _UgcCommunity_GetTopicList_Handler,
		},
		{
			MethodName: "BatchGetTopic",
			Handler:    _UgcCommunity_BatchGetTopic_Handler,
		},
		{
			MethodName: "AddPostGuideTask",
			Handler:    _UgcCommunity_AddPostGuideTask_Handler,
		},
		{
			MethodName: "GetPostGuideTask",
			Handler:    _UgcCommunity_GetPostGuideTask_Handler,
		},
		{
			MethodName: "FinishPostGuideTask",
			Handler:    _UgcCommunity_FinishPostGuideTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/ugc-community/ugc-community.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/ugc-community/ugc-community.proto", fileDescriptor_ugc_community_4ebb26927b1775be)
}

var fileDescriptor_ugc_community_4ebb26927b1775be = []byte{
	// 4455 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x3b, 0x4d, 0x73, 0xdb, 0x58,
	0x72, 0x03, 0x92, 0xa2, 0xa4, 0x26, 0x45, 0x41, 0x4f, 0x92, 0x49, 0xc1, 0xe3, 0xb1, 0x0c, 0xdb,
	0x63, 0x45, 0xe3, 0xa1, 0xc7, 0xca, 0x4c, 0xb2, 0xf6, 0xcc, 0x64, 0x86, 0xa2, 0x28, 0x99, 0xb3,
	0xb2, 0xa8, 0x01, 0xc1, 0xdd, 0x1d, 0x57, 0xb6, 0x18, 0x08, 0x84, 0x4c, 0x44, 0x14, 0xc0, 0x21,
	0x40, 0x67, 0x35, 0xb9, 0x6c, 0x2a, 0x39, 0xa5, 0x6a, 0x2f, 0x9b, 0x4a, 0x52, 0xa9, 0x54, 0x2e,
	0x7b, 0x4f, 0x25, 0x55, 0xa9, 0xec, 0x31, 0xa7, 0x9c, 0x93, 0xaa, 0x54, 0xe5, 0x96, 0x9c, 0xf2,
	0x0f, 0x72, 0x4f, 0x55, 0xea, 0x7d, 0x00, 0x78, 0x00, 0x1e, 0x28, 0x6a, 0xbd, 0x87, 0xbd, 0x11,
	0xfd, 0xba, 0xfb, 0xf5, 0xeb, 0xd7, 0xdd, 0xaf, 0x5f, 0xbf, 0x26, 0x7c, 0xe4, 0xfb, 0x4f, 0xbe,
	0x9d, 0xda, 0xe6, 0x85, 0x67, 0x8f, 0xde, 0x58, 0x93, 0x27, 0xd3, 0xd7, 0xe6, 0x87, 0xa6, 0x7b,
	0x79, 0x39, 0x75, 0x6c, 0xff, 0x2a, 0xfe, 0x55, 0x1f, 0x4f, 0x5c, 0xdf, 0x45, 0x2b, 0xd3, 0xd7,
	0x66, 0x3f, 0x04, 0xaa, 0x3f, 0x97, 0x00, 0x1a, 0xbe, 0x6f, 0x98, 0xc3, 0x4b, 0xcb, 0xf1, 0xd1,
	0x1e, 0x14, 0xfc, 0xab, 0xb1, 0x55, 0x93, 0xb6, 0xa5, 0x9d, 0xca, 0xde, 0x7b, 0xf5, 0x18, 0x72,
	0x3d, 0x42, 0xac, 0xeb, 0x57, 0x63, 0x4b, 0x23, 0xb8, 0x48, 0x86, 0xfc, 0x85, 0x75, 0x55, 0xcb,
	0x6d, 0x4b, 0x3b, 0xcb, 0x1a, 0xfe, 0x89, 0x21, 0xd3, 0xc9, 0xa8, 0x96, 0xa7, 0x90, 0xe9, 0x64,
	0xa4, 0x3e, 0x86, 0x02, 0xa6, 0x40, 0x1b, 0x20, 0xeb, 0xdf, 0x9c, 0xb6, 0xfa, 0xbd, 0x93, 0xee,
	0x69, 0xab, 0xd9, 0x3e, 0x6c, 0xb7, 0x0e, 0xe4, 0x77, 0x50, 0x05, 0x80, 0x40, 0xdb, 0x2f, 0x1b,
	0x47, 0x2d, 0x59, 0x52, 0xff, 0x43, 0x82, 0xd2, 0xa9, 0xeb, 0xf9, 0xfb, 0xf6, 0x77, 0x07, 0x86,
	0x6f, 0xa0, 0x7a, 0x4c, 0x2a, 0x25, 0x21, 0x15, 0xc3, 0xe4, 0x24, 0x7a, 0x06, 0xc5, 0x73, 0x77,
	0x72, 0x69, 0xf8, 0x44, 0xa8, 0xca, 0xde, 0x3d, 0x31, 0x05, 0xe6, 0x5d, 0x3f, 0x24, 0x88, 0x1a,
	0x23, 0xc0, 0xa2, 0x9f, 0xd9, 0x0e, 0x11, 0xbd, 0xac, 0xe1, 0x9f, 0xea, 0x21, 0x14, 0x29, 0x0e,
	0xba, 0x05, 0xe8, 0xb0, 0xa3, 0xbd, 0x6c, 0xe8, 0x09, 0xf1, 0xd7, 0x61, 0x95, 0xc1, 0x4f, 0xb5,
	0x8e, 0xde, 0xd9, 0xef, 0x1d, 0xca, 0x12, 0x5a, 0x85, 0x12, 0x03, 0x7e, 0xd5, 0xed, 0x9c, 0xc8,
	0x39, 0xf5, 0x4f, 0x16, 0xa0, 0x80, 0x27, 0x46, 0x15, 0xc8, 0xd9, 0x03, 0xb2, 0x96, 0x65, 0x2d,
	0x67, 0x0f, 0xd0, 0x1d, 0x00, 0x73, 0x62, 0x19, 0xbe, 0x35, 0xe8, 0x33, 0x89, 0xf3, 0xda, 0x32,
	0x83, 0x34, 0x7c, 0x3c, 0x3c, 0x1d, 0x0f, 0x82, 0xe1, 0x3c, 0x1d, 0x66, 0x90, 0x06, 0x11, 0x78,
	0x6a, 0x0f, 0x6a, 0x85, 0x6d, 0x69, 0x67, 0x45, 0xc3, 0x3f, 0xd1, 0x07, 0x4c, 0x5b, 0x0b, 0x64,
	0xed, 0x55, 0xc1, 0xda, 0x39, 0x55, 0xd5, 0x61, 0xc1, 0xf3, 0x0d, 0xdf, 0xaa, 0x15, 0x09, 0x76,
	0x4d, 0x80, 0xdd, 0xc5, 0xe3, 0x1a, 0x45, 0x43, 0x4f, 0xa1, 0xe8, 0x4e, 0xec, 0xd7, 0xb6, 0x53,
	0x5b, 0x24, 0x04, 0x5b, 0x02, 0x82, 0x0e, 0x41, 0xd0, 0x18, 0x22, 0xfa, 0x04, 0x96, 0xce, 0xec,
	0xef, 0xfa, 0x03, 0xc3, 0x37, 0x6a, 0x4b, 0xdb, 0xd2, 0x4e, 0x29, 0x6b, 0x07, 0xf1, 0x7e, 0x68,
	0x8b, 0x67, 0x6c, 0xd3, 0x6b, 0xb0, 0x68, 0xba, 0x8e, 0x6f, 0x39, 0x7e, 0x0d, 0x88, 0xae, 0x82,
	0x4f, 0xf4, 0x29, 0x94, 0x8c, 0xd0, 0x12, 0xbd, 0x5a, 0x69, 0x3b, 0xbf, 0x53, 0x4a, 0x09, 0x12,
	0xd9, 0xaa, 0xc6, 0x63, 0xa3, 0x2d, 0x58, 0x32, 0xa6, 0x03, 0xdb, 0xc7, 0xca, 0x2c, 0x13, 0x65,
	0x2e, 0x92, 0xef, 0x86, 0x8f, 0x3e, 0x87, 0x32, 0x1d, 0x9a, 0x58, 0xde, 0x74, 0xe4, 0xd7, 0x56,
	0x84, 0xe6, 0xd6, 0xc0, 0x28, 0x1a, 0xc1, 0xd0, 0x4a, 0x46, 0xf4, 0x81, 0xee, 0xc3, 0x0a, 0xc6,
	0xb2, 0x1c, 0xbf, 0x6f, 0xba, 0x53, 0xc7, 0xaf, 0x55, 0xc8, 0x9e, 0x94, 0x19, 0xb0, 0x89, 0x61,
	0xe8, 0x21, 0x54, 0x0c, 0xdf, 0xb7, 0xfd, 0xe9, 0xc0, 0x62, 0x58, 0xab, 0x04, 0x6b, 0x25, 0x80,
	0x52, 0xb4, 0xa7, 0x50, 0xc4, 0xfa, 0x9e, 0x7a, 0x35, 0x39, 0x53, 0xcd, 0x5d, 0x82, 0xa0, 0x31,
	0x44, 0xa4, 0xc2, 0x8a, 0xef, 0x8e, 0x6d, 0xb3, 0x6f, 0x0f, 0xfa, 0x23, 0xdb, 0xf3, 0x6b, 0x6b,
	0xdb, 0xf9, 0x9d, 0x65, 0xad, 0x44, 0x80, 0xed, 0xc1, 0xb1, 0xed, 0xf9, 0xea, 0x3f, 0xe7, 0x60,
	0xad, 0x61, 0xbf, 0x36, 0x9b, 0x01, 0x23, 0x62, 0x90, 0x55, 0x58, 0x9c, 0xb8, 0x23, 0xab, 0xcf,
	0xac, 0x72, 0x45, 0x2b, 0xe2, 0xcf, 0xf6, 0x00, 0x9d, 0x40, 0xd9, 0x1c, 0x1a, 0x58, 0x1f, 0xa6,
	0x3b, 0x19, 0x78, 0xb5, 0x1c, 0xd1, 0xf4, 0x07, 0x49, 0x85, 0x24, 0x19, 0xd6, 0x9b, 0x43, 0xc3,
	0xd7, 0x08, 0x8d, 0x56, 0x32, 0xc3, 0xdf, 0x9e, 0xf2, 0x4b, 0x09, 0x20, 0x1a, 0x43, 0x5f, 0x41,
	0xd1, 0xb3, 0x9c, 0x81, 0x35, 0x61, 0x8e, 0xbd, 0x77, 0x03, 0xc6, 0xf5, 0x2e, 0xa1, 0xd4, 0x18,
	0x07, 0xde, 0x5a, 0x72, 0x31, 0x6b, 0x51, 0xf7, 0xa1, 0x48, 0x71, 0xb1, 0xff, 0x76, 0x5b, 0x27,
	0x07, 0x2d, 0x2d, 0xe1, 0xbf, 0xab, 0x50, 0x62, 0x70, 0xad, 0x73, 0xdc, 0xa2, 0xbe, 0x1b, 0x20,
	0x76, 0x5b, 0x9a, 0x9c, 0xc3, 0x51, 0xf2, 0xd6, 0xe9, 0xf4, 0x6c, 0x64, 0x7b, 0x43, 0x2c, 0xc7,
	0xd1, 0xd4, 0x1e, 0x58, 0x27, 0xae, 0x6f, 0x9f, 0x5f, 0x05, 0xfe, 0x27, 0x45, 0xfe, 0xc7, 0xa9,
	0x33, 0x17, 0x53, 0xe7, 0x1d, 0x00, 0xdf, 0xf0, 0x2e, 0xfa, 0xbe, 0x7b, 0x61, 0x39, 0x2c, 0x3a,
	0x2e, 0x63, 0x88, 0x8e, 0x01, 0xa8, 0x0e, 0xeb, 0x13, 0xeb, 0x8f, 0x8c, 0xc9, 0xa0, 0xef, 0x59,
	0x58, 0x74, 0xd3, 0xea, 0x9b, 0x8e, 0xcf, 0x3c, 0x7b, 0x8d, 0x0e, 0x75, 0xd9, 0x48, 0xd3, 0xf1,
	0xd5, 0x6f, 0x61, 0x83, 0x38, 0xb3, 0xe1, 0x5d, 0x1c, 0xda, 0x8e, 0xed, 0x0d, 0x6f, 0x2e, 0xd1,
	0x06, 0x2c, 0xf8, 0xb6, 0x3f, 0xb2, 0x98, 0x30, 0xf4, 0x83, 0xd7, 0x65, 0x21, 0xae, 0xcb, 0x7f,
	0x90, 0x60, 0x59, 0x27, 0xf6, 0xe4, 0x9c, 0xbb, 0xa9, 0x40, 0xf6, 0x98, 0x05, 0x9e, 0x9c, 0x30,
	0x94, 0x10, 0x3a, 0x2e, 0xf2, 0xc4, 0xc3, 0x5e, 0x7e, 0x76, 0xd8, 0x2b, 0x24, 0xc3, 0x1e, 0x82,
	0x82, 0x63, 0x5c, 0xd2, 0x20, 0xb7, 0xac, 0x91, 0xdf, 0x18, 0xe6, 0xb9, 0x13, 0x9f, 0x84, 0xb2,
	0x15, 0x8d, 0xfc, 0x56, 0xff, 0xb2, 0x00, 0x6b, 0x4d, 0xc2, 0x14, 0xeb, 0x4a, 0xb3, 0xbe, 0x9d,
	0x5a, 0x9e, 0x8f, 0x9e, 0x41, 0xc1, 0x76, 0xce, 0x5d, 0x22, 0x7b, 0x69, 0xef, 0x61, 0x42, 0xd2,
	0x14, 0x3e, 0x71, 0x37, 0x8d, 0x90, 0x28, 0xff, 0x9a, 0xcf, 0x08, 0xe3, 0x1f, 0xc4, 0x56, 0x3f,
	0x6f, 0xd8, 0xcd, 0xdf, 0x34, 0xec, 0x16, 0xe6, 0x0d, 0xbb, 0xdc, 0x2e, 0x2e, 0xcc, 0x8c, 0x9f,
	0xc5, 0x1b, 0xc5, 0xcf, 0x5f, 0x31, 0x9a, 0x33, 0xa3, 0x5c, 0x8e, 0x8c, 0x92, 0x0f, 0xc4, 0x30,
	0x3b, 0x10, 0x97, 0x6e, 0x16, 0x88, 0x53, 0x91, 0xb0, 0x9c, 0x8e, 0x84, 0x0f, 0x00, 0xf1, 0xdb,
	0xec, 0x8d, 0x5d, 0xc7, 0xb3, 0x92, 0x7b, 0xaa, 0xfe, 0xb9, 0x04, 0xef, 0xf6, 0x88, 0xcd, 0x61,
	0x34, 0x7e, 0x42, 0x66, 0x48, 0x49, 0x23, 0x48, 0x4a, 0x9e, 0xbb, 0x99, 0xe4, 0xb7, 0x61, 0xd9,
	0xf6, 0xfa, 0xde, 0xd0, 0xb6, 0x46, 0x03, 0x62, 0x1a, 0x4b, 0xda, 0x92, 0xed, 0x75, 0xc9, 0xb7,
	0x7a, 0x17, 0xee, 0x64, 0xc8, 0x42, 0xa5, 0x57, 0xff, 0x57, 0x82, 0x5b, 0xfb, 0x86, 0x6f, 0x0e,
	0x0f, 0xac, 0x91, 0x15, 0x37, 0xf8, 0x2a, 0x2c, 0x06, 0xca, 0x90, 0x88, 0x32, 0x8a, 0x36, 0xd1,
	0x03, 0x3a, 0x80, 0xa2, 0xe7, 0x4e, 0x27, 0x66, 0x60, 0xb7, 0x8f, 0x13, 0xa2, 0x8a, 0xf9, 0xd5,
	0xbb, 0x84, 0x46, 0x63, 0xb4, 0xea, 0x15, 0x14, 0x29, 0x84, 0xc4, 0xd8, 0x4e, 0x4f, 0x6b, 0xb6,
	0x04, 0x31, 0x96, 0xc1, 0x71, 0x48, 0x95, 0x70, 0xd2, 0xc4, 0x00, 0x9d, 0xc3, 0xc3, 0x76, 0xb3,
	0xdd, 0x38, 0x96, 0x73, 0xa8, 0x0a, 0xeb, 0x0c, 0xd8, 0xe8, 0x1d, 0xb4, 0xf5, 0xbe, 0xd6, 0xfa,
	0xaa, 0xd5, 0xd4, 0xe5, 0x3c, 0xaa, 0xc1, 0x46, 0x6c, 0x40, 0x6f, 0xbf, 0x6c, 0x75, 0x7a, 0xba,
	0x5c, 0x50, 0xb7, 0xa0, 0x9a, 0x92, 0x91, 0xe9, 0x63, 0x1b, 0x2a, 0x47, 0x96, 0xcf, 0xab, 0x21,
	0xb9, 0xbf, 0xcf, 0x61, 0x35, 0xc4, 0x60, 0x26, 0xf0, 0x28, 0x16, 0x1a, 0xd6, 0x05, 0xb6, 0x4d,
	0x03, 0x81, 0xfa, 0x21, 0x20, 0x46, 0x8b, 0x15, 0x79, 0x9d, 0xa2, 0xd5, 0xdf, 0x83, 0xf5, 0x18,
	0x7a, 0x34, 0x5d, 0x88, 0x9c, 0x35, 0x1d, 0x46, 0x50, 0x27, 0xa0, 0x72, 0xf4, 0x6d, 0x87, 0x18,
	0x80, 0x6e, 0x5f, 0x5a, 0x9a, 0xe1, 0xbc, 0xb6, 0x82, 0xe9, 0x37, 0x60, 0xe1, 0xcc, 0xc2, 0x61,
	0x42, 0x22, 0x1e, 0x45, 0x3f, 0xb0, 0xf3, 0x59, 0xce, 0x80, 0xa5, 0x96, 0xf8, 0x27, 0x53, 0x44,
	0x3e, 0xb4, 0xdb, 0x0d, 0x58, 0x18, 0xd9, 0x97, 0x76, 0x70, 0xda, 0xd0, 0x0f, 0xf5, 0x35, 0xdc,
	0x9f, 0x39, 0x27, 0x5b, 0x43, 0x15, 0x16, 0x47, 0x86, 0xe7, 0xf7, 0x43, 0xd5, 0x16, 0xf1, 0x67,
	0x7b, 0x10, 0x2e, 0x2e, 0x77, 0xdd, 0xe2, 0xde, 0x00, 0x6a, 0x0c, 0x06, 0x3d, 0xcf, 0x9a, 0xf0,
	0xbb, 0x95, 0x3e, 0xc8, 0x58, 0xf0, 0xe1, 0xe2, 0xec, 0xac, 0xcb, 0x00, 0x0e, 0x3e, 0xe4, 0xd6,
	0x51, 0x85, 0xc5, 0xb1, 0x4b, 0x05, 0xa4, 0x4b, 0x2e, 0xe2, 0xcf, 0xf6, 0x40, 0xdd, 0x84, 0xf5,
	0xd8, 0xbc, 0xcc, 0x70, 0x3e, 0x82, 0x4d, 0x6a, 0x4e, 0x49, 0x89, 0x38, 0x46, 0x52, 0x8c, 0x51,
	0x0d, 0x6e, 0x25, 0x29, 0x18, 0xaf, 0xbf, 0x91, 0xe0, 0xd6, 0x91, 0xe5, 0x07, 0x70, 0xde, 0x56,
	0x7e, 0x6d, 0xeb, 0xdb, 0x86, 0x32, 0xd9, 0x80, 0xf8, 0x22, 0x01, 0xc3, 0x4e, 0x89, 0x7c, 0x19,
	0xfb, 0xfb, 0x31, 0x54, 0x53, 0xa2, 0xb1, 0x3d, 0xdd, 0x82, 0x25, 0xc6, 0xcd, 0x63, 0x86, 0xbc,
	0x48, 0x97, 0xea, 0xa9, 0x23, 0xa8, 0x34, 0x58, 0xb2, 0xda, 0x39, 0xfb, 0x43, 0xcb, 0x4c, 0x47,
	0xc1, 0xdb, 0xc0, 0x0e, 0xf2, 0xe8, 0x42, 0xb3, 0x44, 0x01, 0x0d, 0x1f, 0x7d, 0xc8, 0xce, 0xc9,
	0xbc, 0xf0, 0x20, 0xa3, 0x1c, 0xa3, 0x93, 0x52, 0xfd, 0x5b, 0x89, 0xd8, 0x46, 0x30, 0x23, 0xb7,
	0x13, 0x53, 0xcf, 0x9a, 0x70, 0x39, 0x2b, 0xfe, 0x6c, 0x63, 0x15, 0x16, 0x0d, 0xd3, 0xb7, 0x5d,
	0x87, 0x29, 0xf0, 0x4e, 0xfa, 0x5c, 0x23, 0x8c, 0x1a, 0x04, 0x49, 0x63, 0xc8, 0x98, 0xcc, 0x25,
	0x53, 0x13, 0xb9, 0x4a, 0x99, 0x64, 0x54, 0x3e, 0x8d, 0x21, 0x33, 0x03, 0x8a, 0x84, 0x63, 0x9b,
	0x6e, 0x03, 0x7a, 0x61, 0xcc, 0x2f, 0x33, 0xaf, 0xec, 0x5c, 0x4c, 0xd9, 0xe8, 0x2e, 0x94, 0x82,
	0x4b, 0x05, 0x1e, 0xcd, 0x93, 0x51, 0x60, 0x20, 0xbc, 0x1b, 0x3f, 0xcb, 0xc3, 0x7a, 0x6c, 0x2e,
	0xb6, 0x81, 0x97, 0xb0, 0x49, 0x78, 0x0e, 0x0d, 0x9c, 0x40, 0xb1, 0x1b, 0xc7, 0xa5, 0x31, 0x66,
	0x91, 0xe6, 0x79, 0x62, 0x7d, 0x02, 0x16, 0xc4, 0xd6, 0x38, 0xf8, 0x4b, 0x63, 0xdc, 0x72, 0xfc,
	0xc9, 0x95, 0x86, 0xc6, 0xa9, 0x01, 0xf4, 0x06, 0x6a, 0x81, 0x9c, 0xa9, 0x19, 0xa9, 0xfb, 0x7f,
	0x3e, 0xc7, 0x8c, 0x4d, 0xca, 0x42, 0x34, 0xe9, 0xa6, 0x29, 0x1a, 0x53, 0x5a, 0x50, 0xcd, 0x10,
	0x33, 0xa8, 0x4b, 0x48, 0x51, 0x5d, 0x62, 0x03, 0x16, 0xde, 0x18, 0xa3, 0x29, 0xf5, 0xad, 0x25,
	0x8d, 0x7e, 0x3c, 0xcf, 0x7d, 0x4f, 0x52, 0x5e, 0x80, 0x92, 0x3d, 0xf7, 0x4d, 0x38, 0xa9, 0x7f,
	0x91, 0x07, 0xc4, 0x58, 0xe1, 0x6b, 0x47, 0xb6, 0xaf, 0x73, 0xb1, 0x24, 0xc7, 0xc7, 0x12, 0xf4,
	0x00, 0x2a, 0x13, 0xd7, 0xf5, 0xfb, 0x63, 0x63, 0x42, 0xb7, 0x9d, 0xf9, 0x73, 0x19, 0x43, 0x4f,
	0x09, 0xb0, 0x4d, 0x7c, 0x2c, 0x42, 0xa0, 0x69, 0xfa, 0xd2, 0x38, 0x18, 0xcc, 0xce, 0xfd, 0x54,
	0x58, 0x99, 0x58, 0xe3, 0xd1, 0x55, 0x3f, 0xb0, 0x44, 0x9a, 0x2c, 0x97, 0x08, 0xb0, 0x47, 0xcd,
	0x11, 0xb3, 0xc6, 0x92, 0xf9, 0xf6, 0xa5, 0x45, 0xae, 0xf9, 0x79, 0x8d, 0xd8, 0x27, 0x8e, 0xfc,
	0xe8, 0x73, 0x28, 0xe1, 0x2b, 0x57, 0xdf, 0x72, 0x7c, 0xdb, 0xbf, 0x62, 0x29, 0xe0, 0xbb, 0xc9,
	0x0c, 0x9a, 0x2a, 0xa0, 0x45, 0x70, 0x34, 0xc0, 0x04, 0xf4, 0x37, 0xfa, 0x02, 0xca, 0x74, 0x7e,
	0x46, 0xbf, 0x3c, 0x07, 0x3d, 0x15, 0x8e, 0x31, 0xf8, 0x38, 0xcc, 0x84, 0x81, 0xf8, 0x77, 0x06,
	0x69, 0x3c, 0x19, 0xc6, 0x7e, 0x1a, 0xdb, 0x14, 0xe6, 0xa7, 0x3d, 0x58, 0x89, 0x4d, 0x85, 0x3e,
	0x8e, 0x55, 0x9a, 0xb6, 0x67, 0x89, 0xc5, 0x65, 0xf3, 0x34, 0xfe, 0xd1, 0xab, 0x15, 0x4e, 0x2b,
	0x7e, 0x2a, 0x85, 0xd3, 0x1d, 0x5a, 0xbe, 0x39, 0xbc, 0xee, 0xf8, 0x40, 0xef, 0xc3, 0x2a, 0x09,
	0xe0, 0x91, 0xab, 0x33, 0x9b, 0x58, 0xc1, 0xe0, 0x66, 0xe0, 0xed, 0x68, 0x07, 0xe4, 0x73, 0xcc,
	0xb0, 0x6f, 0xd8, 0x01, 0x2e, 0x4b, 0x13, 0x2b, 0x04, 0xde, 0xb0, 0x19, 0xae, 0xfa, 0x6f, 0x05,
	0x28, 0x05, 0x74, 0xbe, 0x75, 0x99, 0x0a, 0xd1, 0x5c, 0x2c, 0xca, 0xc5, 0x62, 0x11, 0x67, 0x3a,
	0xf9, 0xb8, 0xe9, 0x90, 0x33, 0xe4, 0xc2, 0xf2, 0xa2, 0x33, 0xe4, 0xc2, 0xf2, 0xe2, 0xb1, 0x7e,
	0x21, 0x11, 0xeb, 0x3b, 0x50, 0xf6, 0xa6, 0x67, 0x81, 0xa8, 0xc1, 0x55, 0xe3, 0xb1, 0x58, 0xad,
	0x58, 0xce, 0x7a, 0xd7, 0x32, 0x5d, 0x67, 0x10, 0x40, 0x9c, 0x73, 0x57, 0x2b, 0x79, 0xd3, 0x33,
	0xf6, 0x4d, 0xc2, 0x21, 0x35, 0x1f, 0x5a, 0x3b, 0x59, 0x24, 0x92, 0x00, 0x01, 0xd1, 0xc2, 0xc9,
	0xdb, 0x99, 0xa7, 0xf2, 0xd7, 0x39, 0x58, 0x4b, 0x89, 0xf0, 0xeb, 0x50, 0x5e, 0x4c, 0x4d, 0x85,
	0x84, 0x9a, 0x52, 0x4e, 0xb9, 0x90, 0x76, 0xca, 0xc4, 0xc2, 0x8a, 0x6f, 0xe9, 0x77, 0x8b, 0x37,
	0xf4, 0x3b, 0xd5, 0x87, 0x8d, 0xb8, 0x49, 0xb3, 0x73, 0xe6, 0x77, 0x60, 0x29, 0xdc, 0x5e, 0x7a,
	0xb4, 0x28, 0xd9, 0xdb, 0xab, 0x85, 0xb8, 0x38, 0xca, 0xd9, 0x5e, 0x7f, 0xe4, 0x1a, 0x83, 0xfe,
	0x39, 0xa9, 0x5e, 0xb0, 0x50, 0x5a, 0xb6, 0xbd, 0x63, 0xd7, 0x18, 0xd0, 0x8a, 0x86, 0xfa, 0x09,
	0xd4, 0x58, 0x06, 0xfa, 0xc2, 0x0d, 0xfc, 0x20, 0xf0, 0xa6, 0x19, 0x29, 0xca, 0x7f, 0x4a, 0xb0,
	0x25, 0xa0, 0x63, 0x22, 0xff, 0x3e, 0x94, 0x87, 0xae, 0xdf, 0x4f, 0x88, 0xfd, 0x2c, 0x21, 0x76,
	0x26, 0x7d, 0x3d, 0x02, 0x79, 0xf4, 0x6c, 0x2a, 0x0d, 0x23, 0x88, 0xf2, 0x0a, 0xe4, 0x24, 0x82,
	0xe0, 0x00, 0xf9, 0x88, 0x3f, 0x40, 0x66, 0xeb, 0x8c, 0x3b, 0x5c, 0x3e, 0x09, 0x37, 0x81, 0x66,
	0x9b, 0x81, 0x2a, 0xee, 0x00, 0x70, 0xa1, 0x83, 0x4e, 0xb3, 0x1c, 0x26, 0x09, 0x6a, 0x15, 0x36,
	0x13, 0x64, 0x2c, 0xfe, 0x9d, 0x13, 0x35, 0x71, 0xa6, 0xbe, 0x7f, 0xd5, 0x1e, 0xcc, 0xc7, 0x14,
	0xed, 0xc2, 0x1a, 0xd9, 0xbd, 0x98, 0x83, 0xd3, 0x3d, 0x5c, 0xc5, 0x03, 0xdd, 0xc8, 0x6d, 0xd5,
	0x63, 0x50, 0x44, 0xf3, 0xb0, 0xfd, 0xa8, 0x43, 0xc1, 0xf6, 0xad, 0x4b, 0x76, 0xe5, 0x9a, 0xa5,
	0x0a, 0x82, 0xa7, 0x1e, 0x11, 0x6e, 0x8d, 0x36, 0x1b, 0x69, 0xb2, 0x40, 0x71, 0xe3, 0x93, 0x56,
	0xfd, 0x17, 0x09, 0x6e, 0x0b, 0x39, 0x85, 0xb6, 0x5d, 0x23, 0x1e, 0x19, 0x45, 0x5b, 0x9a, 0x62,
	0x9b, 0x8e, 0xcf, 0xf8, 0x6f, 0xe0, 0xf1, 0x30, 0xea, 0x62, 0x7b, 0x69, 0x3a, 0x38, 0xc5, 0x5d,
	0x27, 0x78, 0x1c, 0x9d, 0xc9, 0x0a, 0x93, 0x2b, 0x9a, 0x8c, 0x87, 0x42, 0x12, 0x8c, 0xfe, 0x3d,
	0xd8, 0xf2, 0xdd, 0x81, 0xc1, 0xdc, 0x3f, 0x41, 0x94, 0x27, 0x44, 0x9b, 0x04, 0xa1, 0xc7, 0x4f,
	0xd6, 0x74, 0x7c, 0xf5, 0x33, 0xb8, 0x4b, 0x2e, 0xbf, 0xcc, 0x56, 0xf9, 0x85, 0xcc, 0xe1, 0x25,
	0xff, 0x24, 0xc1, 0x76, 0x36, 0x39, 0xd3, 0x81, 0x0e, 0x8b, 0xa6, 0xe3, 0x73, 0x99, 0xe3, 0xa7,
	0xa2, 0x0a, 0xc1, 0x0c, 0x0e, 0xf5, 0xa6, 0xe3, 0x87, 0x59, 0x5c, 0xd1, 0x24, 0x1f, 0xca, 0x33,
	0x28, 0x71, 0xe0, 0xeb, 0x12, 0xac, 0x15, 0xde, 0x07, 0x7e, 0x9a, 0xc7, 0x21, 0xda, 0x98, 0x98,
	0xc3, 0xd9, 0x77, 0xc5, 0x59, 0x65, 0x58, 0x72, 0x2d, 0xa6, 0xf9, 0x0d, 0x2b, 0x3c, 0x12, 0x08,
	0x49, 0x70, 0xb6, 0x60, 0x09, 0xc7, 0x59, 0x32, 0x48, 0x03, 0xf5, 0xa2, 0xe5, 0x0c, 0xc8, 0xd0,
	0x2d, 0x28, 0x9a, 0xd3, 0x89, 0xe7, 0x4e, 0x58, 0x56, 0xc5, 0xbe, 0xd0, 0x67, 0xb0, 0x8c, 0xaf,
	0x6d, 0xfc, 0x43, 0xca, 0x5d, 0x41, 0x2d, 0x9b, 0xdd, 0xdd, 0x68, 0x61, 0x0f, 0x5f, 0xf4, 0xc8,
	0x2f, 0xb4, 0x0e, 0x0b, 0x3e, 0x31, 0xce, 0x45, 0x5a, 0xcb, 0xf4, 0xb1, 0x77, 0x35, 0x60, 0x75,
	0x68, 0x78, 0x7d, 0xae, 0xfc, 0x4e, 0xce, 0xb2, 0xf4, 0x85, 0x69, 0xdf, 0x75, 0x47, 0x87, 0xf6,
	0xc8, 0xb7, 0x26, 0xda, 0xca, 0xd0, 0xf0, 0xb8, 0xf2, 0xfa, 0x17, 0x50, 0xc1, 0x2c, 0xb8, 0x54,
	0x61, 0xf9, 0x3a, 0x0e, 0xe5, 0xa1, 0xe1, 0x85, 0x06, 0xc6, 0xfb, 0x0d, 0xc4, 0xfc, 0xe6, 0xbf,
	0x0a, 0x50, 0xda, 0x37, 0xcc, 0x0b, 0xcb, 0x19, 0x08, 0x4b, 0xa1, 0x6c, 0x33, 0x72, 0xd1, 0x66,
	0x5c, 0x53, 0xec, 0x4d, 0x3d, 0x9d, 0x14, 0xe6, 0x7a, 0x3a, 0x59, 0x10, 0x3d, 0x9d, 0xe0, 0xf4,
	0xd9, 0x1a, 0xe1, 0x93, 0x36, 0xd8, 0x7e, 0x9a, 0xe2, 0x96, 0x29, 0x54, 0xa3, 0x46, 0xc0, 0x9d,
	0xd4, 0x8b, 0x33, 0xab, 0xa3, 0x4b, 0x37, 0xaa, 0x8e, 0xde, 0x01, 0xb0, 0xbd, 0xbe, 0xf5, 0x93,
	0xb1, 0xeb, 0x59, 0xb4, 0xda, 0xb9, 0xa4, 0x2d, 0xdb, 0x5e, 0x8b, 0x02, 0xc8, 0xb0, 0xe3, 0x59,
	0x13, 0x12, 0x47, 0x88, 0x52, 0x57, 0xb4, 0x65, 0x0a, 0x39, 0x75, 0x3d, 0x1c, 0x52, 0xd9, 0xb0,
	0x37, 0x25, 0xf7, 0x4b, 0x2c, 0x7d, 0x89, 0x88, 0xb7, 0x4a, 0x07, 0xba, 0x14, 0x4e, 0x53, 0xc6,
	0xa4, 0x81, 0x94, 0xc9, 0x74, 0x09, 0x2b, 0x48, 0xbe, 0xe1, 0xac, 0xbc, 0xdd, 0x1b, 0x0e, 0x56,
	0x6f, 0xc2, 0xaa, 0x2a, 0xf4, 0xdc, 0x8e, 0x99, 0x0e, 0x56, 0xa2, 0x1d, 0x1d, 0x0b, 0xab, 0xd7,
	0x26, 0x06, 0x60, 0xd8, 0xe1, 0x69, 0x61, 0x01, 0xe2, 0x1d, 0x9c, 0x05, 0xa2, 0xdf, 0x65, 0xb7,
	0x12, 0xae, 0x5c, 0xa6, 0xa4, 0x42, 0x51, 0x68, 0x93, 0xf4, 0xc6, 0x42, 0x4a, 0x9c, 0x91, 0xd7,
	0xe6, 0x78, 0xaf, 0x55, 0xff, 0x4f, 0x82, 0xad, 0xce, 0xf9, 0xb9, 0x6d, 0xda, 0xc6, 0xe8, 0x85,
	0xe1, 0x0c, 0x46, 0xb1, 0x8a, 0xe9, 0x29, 0x00, 0x35, 0x7e, 0xe7, 0xdc, 0x0d, 0x52, 0x84, 0xa7,
	0xc9, 0x62, 0x45, 0x16, 0x35, 0xb9, 0x3a, 0x93, 0xa3, 0x84, 0xc8, 0x8c, 0x7f, 0x79, 0xca, 0xcf,
	0x24, 0x58, 0x0a, 0xe0, 0xd9, 0x57, 0x81, 0xb8, 0x89, 0xe4, 0x92, 0x26, 0x12, 0x37, 0xb0, 0x7c,
	0xd2, 0xc0, 0x84, 0x16, 0x54, 0x10, 0x5a, 0x90, 0xfa, 0x2e, 0x28, 0xa2, 0x05, 0xb0, 0xd4, 0xe0,
	0x7f, 0x24, 0x28, 0x61, 0xab, 0xd8, 0xb7, 0xbf, 0x0b, 0x1e, 0x09, 0x33, 0x05, 0x9e, 0xf5, 0x7c,
	0xcd, 0x62, 0x41, 0x3e, 0x8a, 0x05, 0x69, 0x07, 0x2d, 0x08, 0x1c, 0x34, 0xbe, 0xd0, 0x85, 0xd9,
	0x9e, 0x54, 0x4c, 0xaa, 0x49, 0xe0, 0x1d, 0x8b, 0x02, 0xef, 0x50, 0xff, 0x3d, 0x07, 0x35, 0x5a,
	0x54, 0xe7, 0xd6, 0x1a, 0x98, 0x40, 0x0f, 0xca, 0x43, 0xa2, 0x18, 0x66, 0x04, 0x79, 0x62, 0x04,
	0xc9, 0x57, 0xca, 0x2c, 0xf2, 0x3a, 0x55, 0x2a, 0xbd, 0xc3, 0x0c, 0xc3, 0xdf, 0x9e, 0xf2, 0x57,
	0x12, 0x40, 0x34, 0x86, 0xbe, 0x84, 0x0a, 0x3e, 0x3c, 0x62, 0xc6, 0x26, 0xca, 0x83, 0xf8, 0x19,
	0xca, 0x67, 0xf4, 0x07, 0x61, 0x88, 0x0e, 0x13, 0x25, 0xaf, 0xfa, 0xbc, 0x12, 0xc6, 0x6b, 0x60,
	0x6a, 0x13, 0x8a, 0x14, 0x82, 0x6e, 0x01, 0x6a, 0x34, 0xf5, 0x76, 0xe7, 0x24, 0xdd, 0xa8, 0xc1,
	0xe0, 0x8d, 0x83, 0x03, 0x59, 0x42, 0x6b, 0xb0, 0xc2, 0xbe, 0x0f, 0x5a, 0xc7, 0x2d, 0xbd, 0x25,
	0xe7, 0xd4, 0xdb, 0xb0, 0x25, 0x98, 0x8f, 0x19, 0xd5, 0x57, 0x50, 0xe9, 0xfa, 0xb6, 0x79, 0x71,
	0x15, 0xfa, 0x41, 0xf2, 0xe8, 0x08, 0x5f, 0x24, 0x73, 0xfc, 0x8b, 0x24, 0x82, 0x82, 0x6d, 0xba,
	0xc1, 0x9b, 0x29, 0xf9, 0xad, 0xfe, 0x69, 0x1e, 0xaa, 0xbd, 0x31, 0x67, 0xd2, 0xba, 0x71, 0x16,
	0xec, 0xdc, 0xe7, 0x90, 0x73, 0xc7, 0xec, 0x12, 0xff, 0x61, 0x4a, 0x1b, 0x42, 0x9a, 0x7a, 0x67,
	0x4c, 0x6e, 0xf4, 0x39, 0x77, 0x8c, 0x3e, 0x83, 0xbc, 0x6f, 0x9c, 0xb1, 0xd4, 0x7c, 0x77, 0x4e,
	0x7a, 0xfc, 0x13, 0x93, 0x29, 0xbf, 0x90, 0x20, 0xaf, 0x1b, 0x67, 0xa9, 0xa5, 0x05, 0x4f, 0x96,
	0x39, 0xee, 0xc9, 0xf2, 0x08, 0x64, 0x8f, 0x28, 0xa4, 0x1f, 0xc5, 0x36, 0x6a, 0x66, 0xc9, 0x02,
	0x64, 0x5c, 0x6f, 0x5a, 0xc5, 0x0b, 0xbf, 0x49, 0x90, 0x0b, 0x5a, 0x64, 0x0a, 0xc2, 0xaa, 0x71,
	0x20, 0x6d, 0x54, 0x56, 0x3d, 0x84, 0x22, 0x5d, 0x30, 0xaa, 0xc2, 0x7a, 0xe7, 0xb4, 0x2f, 0xe8,
	0xca, 0x59, 0x85, 0x52, 0x30, 0x40, 0x77, 0x1b, 0x41, 0x25, 0xc4, 0x3c, 0x3d, 0x68, 0x90, 0xed,
	0xfe, 0x7b, 0x09, 0x2a, 0x91, 0x2e, 0x84, 0x5b, 0xfa, 0x1b, 0xb5, 0x6e, 0x05, 0xfb, 0x7b, 0x72,
	0x03, 0x99, 0x75, 0x2a, 0xe4, 0xb2, 0x19, 0x0d, 0x70, 0xb5, 0x7a, 0xf5, 0x84, 0xdc, 0x94, 0x92,
	0x63, 0xec, 0x68, 0x7a, 0x1a, 0x7b, 0xc4, 0xb9, 0x93, 0x21, 0x04, 0x55, 0x0f, 0x7b, 0xf1, 0xf8,
	0x2c, 0xc1, 0x0f, 0x5f, 0x88, 0xbc, 0xc0, 0x7c, 0xef, 0x42, 0x29, 0x0a, 0xdf, 0x41, 0xda, 0x0e,
	0x5e, 0x10, 0xb9, 0x3d, 0xb5, 0x43, 0x6e, 0x40, 0x29, 0xea, 0x5f, 0x5d, 0x9c, 0xe7, 0x50, 0xa5,
	0x57, 0xc3, 0xb4, 0x2f, 0x5d, 0x2b, 0x8c, 0x02, 0xb5, 0x34, 0x2d, 0x53, 0xe9, 0x73, 0xa8, 0x6a,
	0x96, 0xe7, 0x8a, 0x7c, 0x74, 0x1e, 0xbe, 0x69, 0x5a, 0xc6, 0xf7, 0x1f, 0xa5, 0xe0, 0xfd, 0x96,
	0xb4, 0x15, 0x04, 0x3c, 0x3f, 0x8d, 0x3d, 0xde, 0x3d, 0x12, 0xbe, 0xeb, 0xf3, 0x04, 0xb4, 0x29,
	0x81, 0xbd, 0xec, 0xff, 0x18, 0x16, 0xc8, 0x67, 0xd8, 0xc7, 0x20, 0xcd, 0xd5, 0xc7, 0x20, 0x32,
	0xef, 0xa0, 0x13, 0x21, 0xcf, 0x75, 0x22, 0x3c, 0x84, 0xf5, 0x98, 0x00, 0x19, 0x4f, 0xce, 0x3f,
	0x97, 0x00, 0xd1, 0x00, 0x7a, 0x83, 0x95, 0xa5, 0x09, 0x62, 0x2b, 0xfb, 0x22, 0x58, 0xd9, 0x3c,
	0xae, 0x29, 0x92, 0x7d, 0x13, 0xd6, 0x63, 0x53, 0xb0, 0x5d, 0xd8, 0x8b, 0xbd, 0xbd, 0xc6, 0xe4,
	0xcd, 0x7c, 0x07, 0x55, 0xa0, 0x96, 0xa6, 0x61, 0xfc, 0x9a, 0xe4, 0x8d, 0x94, 0xc0, 0xf8, 0x77,
	0xb2, 0x28, 0x81, 0x93, 0x62, 0xd7, 0xae, 0xf0, 0x51, 0x2b, 0xc7, 0x3f, 0x6a, 0x59, 0xb0, 0x11,
	0x67, 0xc2, 0x14, 0x7d, 0x17, 0x4a, 0x8e, 0xf5, 0x13, 0xbf, 0x1f, 0x63, 0x05, 0x18, 0xd4, 0xa4,
	0xec, 0x1e, 0xc7, 0x5e, 0x2b, 0x85, 0xdb, 0xce, 0x79, 0xcc, 0x13, 0xd8, 0x08, 0x6e, 0xbe, 0xf3,
	0x2d, 0xfc, 0x97, 0x12, 0x6c, 0x26, 0x28, 0x98, 0x64, 0x2f, 0xa0, 0x48, 0x5a, 0x13, 0x82, 0x34,
	0xf3, 0xa3, 0x8c, 0x1b, 0x76, 0x8c, 0x8a, 0x0a, 0xc4, 0x0a, 0x50, 0x8c, 0x5e, 0xe9, 0x42, 0x89,
	0x03, 0x0b, 0xae, 0xd5, 0xf5, 0x78, 0xd9, 0x29, 0x7b, 0x91, 0xdc, 0x85, 0xfb, 0x1e, 0x79, 0x24,
	0x8f, 0x2d, 0x32, 0x69, 0xb4, 0x5f, 0x82, 0x9c, 0x5a, 0xd5, 0xe3, 0x98, 0xc5, 0xce, 0x50, 0x27,
	0x79, 0x4d, 0x3f, 0x80, 0x6a, 0x63, 0x30, 0x08, 0x9b, 0xab, 0x74, 0xc3, 0xbb, 0xb8, 0xf9, 0xd5,
	0x5e, 0x7d, 0x06, 0xb5, 0x34, 0x17, 0x26, 0x4f, 0xbc, 0xfb, 0x4a, 0x4a, 0x74, 0x5f, 0x61, 0x01,
	0x58, 0x11, 0xe3, 0x6d, 0x04, 0x78, 0x15, 0xd6, 0x2b, 0x6f, 0x2a, 0x00, 0xb6, 0x4f, 0xdb, 0x63,
	0xb5, 0x50, 0x6b, 0xc0, 0x2a, 0x69, 0x60, 0x7b, 0x87, 0x0c, 0xa2, 0x9e, 0x83, 0x42, 0x7f, 0xbf,
	0xa5, 0x90, 0xd7, 0xf4, 0xa1, 0xa9, 0x77, 0xe0, 0xb6, 0x70, 0x1e, 0xba, 0x8c, 0x5d, 0x8d, 0xde,
	0x62, 0x48, 0xee, 0x50, 0x83, 0x8d, 0xd3, 0x4e, 0x57, 0x8f, 0x67, 0x0f, 0x24, 0x79, 0x40, 0x50,
	0x89, 0x46, 0xf4, 0xd6, 0x8f, 0x74, 0x59, 0x8a, 0x63, 0x93, 0x5e, 0x5f, 0x3a, 0x92, 0xdb, 0x3d,
	0x09, 0xfb, 0x7d, 0x75, 0xda, 0x1a, 0xb6, 0x45, 0x10, 0xf7, 0xdb, 0xaf, 0x44, 0x89, 0xc9, 0x36,
	0xbc, 0x1b, 0x1f, 0x6e, 0xb4, 0x8f, 0x9a, 0xfd, 0x66, 0xe7, 0xe5, 0xcb, 0xde, 0x49, 0x5b, 0xff,
	0x46, 0x96, 0x76, 0x7f, 0x21, 0x01, 0x44, 0x2d, 0x51, 0xe8, 0x36, 0x54, 0x09, 0x41, 0x47, 0x6b,
	0x1f, 0xb5, 0x93, 0x39, 0xed, 0x7d, 0xb8, 0xcb, 0x0f, 0xc6, 0x79, 0x05, 0xdd, 0x2a, 0x8f, 0x61,
	0x27, 0x8e, 0x44, 0x5a, 0x05, 0xfb, 0x2f, 0xda, 0x47, 0x2f, 0xfa, 0x5f, 0xf7, 0x1a, 0xc7, 0x18,
	0x15, 0x13, 0xb5, 0x4e, 0x74, 0x39, 0x87, 0x1e, 0xc0, 0xb6, 0x08, 0x1b, 0xf3, 0x0a, 0xb1, 0xf2,
	0xbb, 0x3a, 0x2c, 0x87, 0x7d, 0x5e, 0x48, 0x81, 0x5b, 0x84, 0xa4, 0xab, 0x37, 0xf4, 0xe4, 0x7a,
	0x37, 0x61, 0x8d, 0x1b, 0x3b, 0xed, 0xed, 0x1f, 0xb7, 0x9b, 0xb2, 0x84, 0x36, 0x40, 0xe6, 0xc0,
	0x8d, 0x93, 0xce, 0xc9, 0x37, 0x72, 0x6e, 0xd7, 0x87, 0x12, 0xd7, 0x1b, 0x84, 0xde, 0x85, 0x5a,
	0xd0, 0x4a, 0xd3, 0xed, 0x1d, 0x27, 0x3b, 0x97, 0xab, 0xb0, 0x1e, 0x1b, 0xd5, 0x5a, 0x3f, 0x68,
	0xb7, 0x7e, 0x28, 0x4b, 0x78, 0xca, 0xd8, 0xc0, 0x69, 0xa3, 0xdb, 0xa5, 0xfd, 0x39, 0x09, 0x7c,
	0xda, 0x9f, 0xb3, 0xbb, 0x4f, 0xf5, 0x4d, 0x5b, 0x52, 0x43, 0x7d, 0x63, 0xc9, 0x7a, 0xdd, 0xf4,
	0x9c, 0xfc, 0x20, 0xbd, 0x38, 0x1c, 0xc8, 0xd2, 0xee, 0x97, 0xac, 0xb7, 0x90, 0x98, 0x80, 0x02,
	0xb7, 0xf4, 0xce, 0x69, 0xbb, 0x29, 0xda, 0xff, 0x2a, 0xac, 0x73, 0x63, 0x61, 0xfb, 0x90, 0xb4,
	0xfb, 0x23, 0x80, 0xa8, 0x7f, 0x00, 0x4b, 0xd1, 0xd9, 0xc7, 0xf2, 0x89, 0x78, 0x6c, 0x80, 0xcc,
	0x0f, 0x62, 0x89, 0x64, 0x89, 0xe4, 0xc2, 0x1c, 0x34, 0xdc, 0xd1, 0xdd, 0x61, 0xd4, 0xf3, 0xc0,
	0xae, 0x48, 0x77, 0xe1, 0x76, 0x43, 0xd7, 0xdb, 0x7a, 0xef, 0xa0, 0xd5, 0x17, 0xde, 0x95, 0x6a,
	0xb0, 0x91, 0x44, 0x38, 0x6e, 0x7f, 0xbf, 0x25, 0x4b, 0x58, 0xb0, 0xe4, 0xc8, 0x41, 0xbb, 0x4b,
	0x06, 0x73, 0xbb, 0x1d, 0x58, 0x4b, 0x3d, 0x33, 0xe2, 0x5d, 0x64, 0xb2, 0xf4, 0x5b, 0x27, 0x3a,
	0x36, 0x36, 0xba, 0x24, 0x6c, 0x9c, 0xef, 0x64, 0x8d, 0xd2, 0x66, 0xd6, 0xdd, 0xa3, 0xf0, 0x8d,
	0x93, 0x79, 0xc3, 0x16, 0x6c, 0x06, 0xe8, 0xcc, 0x40, 0x4f, 0x3a, 0xda, 0xcb, 0xc6, 0xb1, 0xfc,
	0x0e, 0x96, 0x2c, 0x31, 0xc4, 0x69, 0xf7, 0xef, 0x24, 0x90, 0x93, 0x65, 0x4c, 0xec, 0x3d, 0xc4,
	0x63, 0x42, 0x87, 0x14, 0x19, 0xf0, 0x7b, 0xa0, 0x88, 0x90, 0xd8, 0xb4, 0x52, 0xd6, 0x78, 0xf7,
	0x45, 0xbb, 0x75, 0x7c, 0x40, 0xfd, 0x49, 0x34, 0x7e, 0xd8, 0xd1, 0x9a, 0xad, 0x7e, 0xfb, 0xa4,
	0xdb, 0xd2, 0xb0, 0x0d, 0xfe, 0x00, 0x20, 0x2a, 0x65, 0xe2, 0xa5, 0xec, 0x77, 0x3a, 0xc7, 0xfd,
	0xc3, 0xf6, 0xb1, 0x9e, 0xea, 0xf8, 0xdd, 0x00, 0x99, 0x1f, 0xd4, 0xb5, 0x5e, 0x8b, 0x1a, 0x3d,
	0x0f, 0x3d, 0x6c, 0x1c, 0x77, 0xf1, 0x8e, 0xf4, 0xa1, 0xc4, 0xdd, 0x23, 0xb0, 0xb6, 0xbb, 0xbd,
	0x4c, 0xbb, 0xaa, 0xc2, 0x7a, 0x6c, 0x34, 0x5c, 0xe3, 0x16, 0x6c, 0xc6, 0x06, 0x0e, 0x5b, 0x0d,
	0xbd, 0xa7, 0xb5, 0x0e, 0xe4, 0xdc, 0xde, 0x7f, 0x2b, 0x50, 0xee, 0x71, 0xf5, 0x37, 0xf4, 0x35,
	0x40, 0xd4, 0x9c, 0x88, 0xb6, 0xaf, 0x6b, 0x4f, 0x55, 0xee, 0xcd, 0xc0, 0x60, 0x87, 0xcf, 0x04,
	0x36, 0x85, 0xcd, 0x83, 0xe8, 0x03, 0x61, 0x2a, 0x29, 0x6e, 0x77, 0x54, 0x1e, 0xcf, 0x87, 0xcc,
	0xe6, 0xfc, 0x03, 0x58, 0x4d, 0xb4, 0xe6, 0xa1, 0x87, 0x73, 0xb5, 0x17, 0x2a, 0xef, 0x5f, 0x87,
	0x16, 0x66, 0x4e, 0x8b, 0xec, 0xb8, 0x45, 0x77, 0xc4, 0xcf, 0x77, 0x01, 0xc7, 0xf7, 0xb2, 0x86,
	0xc3, 0x67, 0x8e, 0x12, 0xd7, 0xea, 0x86, 0xee, 0x89, 0xd1, 0xb9, 0xac, 0x54, 0x51, 0x67, 0xa1,
	0x30, 0xae, 0x7f, 0x46, 0x1f, 0x98, 0xb2, 0x3a, 0xe8, 0xd0, 0xd3, 0x6c, 0x1e, 0x19, 0x1d, 0x7e,
	0xca, 0xde, 0x4d, 0x48, 0xa2, 0xc5, 0x71, 0x6d, 0x6e, 0xa9, 0xc5, 0xa5, 0x5b, 0xef, 0x52, 0x8b,
	0x13, 0x74, 0xc9, 0xa1, 0x1f, 0x43, 0x25, 0xde, 0xf3, 0x86, 0x1e, 0x24, 0xa8, 0x84, 0x4d, 0x74,
	0xca, 0xc3, 0x6b, 0xb0, 0x22, 0xeb, 0x49, 0x34, 0xa7, 0xa5, 0xac, 0x47, 0xdc, 0x57, 0x97, 0xb2,
	0x9e, 0xac, 0x1e, 0x37, 0xaa, 0x96, 0x20, 0xae, 0x8b, 0xd4, 0x92, 0xe8, 0xe0, 0x12, 0xa9, 0x25,
	0xd5, 0x78, 0xa5, 0x43, 0x89, 0xeb, 0x21, 0x4a, 0x71, 0x4d, 0xf7, 0x85, 0xa5, 0xb8, 0x8a, 0xda,
	0xb9, 0x6c, 0x40, 0xe9, 0x62, 0x2d, 0xda, 0x99, 0xb7, 0x20, 0xad, 0xfc, 0xd6, 0x1c, 0x98, 0x6c,
	0xaa, 0xaf, 0x01, 0xa2, 0xf2, 0x7b, 0x2a, 0xfa, 0xa4, 0x9e, 0xde, 0x52, 0xd1, 0x47, 0x50, 0xbb,
	0x3f, 0x87, 0xb5, 0x54, 0x51, 0x10, 0x3d, 0x9a, 0xb3, 0x4c, 0xa9, 0xec, 0x5c, 0x8f, 0x18, 0xe9,
	0x9e, 0x6b, 0xf3, 0x49, 0xe9, 0x3e, 0xdd, 0x97, 0x95, 0xd2, 0xbd, 0xa0, 0x4b, 0x08, 0xfd, 0x10,
	0xca, 0x7c, 0xeb, 0x03, 0xca, 0xa0, 0xe1, 0x5b, 0x7d, 0x94, 0xfb, 0x33, 0x71, 0x18, 0xe3, 0x57,
	0xe1, 0xd1, 0x4c, 0x7d, 0x00, 0x65, 0x50, 0xc5, 0x1e, 0xfb, 0x95, 0x07, 0xb3, 0x91, 0x22, 0x95,
	0xa7, 0x3a, 0x18, 0x52, 0x2a, 0xcf, 0xea, 0xad, 0x48, 0xa9, 0x3c, 0xbb, 0x99, 0xc2, 0x26, 0x6d,
	0xd0, 0x89, 0xa7, 0x7d, 0x24, 0xa0, 0x17, 0x77, 0x19, 0xa4, 0x0c, 0x73, 0x46, 0x9f, 0xc0, 0x88,
	0x94, 0x07, 0x92, 0xaf, 0xf5, 0x48, 0xc0, 0x21, 0xa3, 0x37, 0x40, 0xd9, 0x9d, 0x07, 0x95, 0xcd,
	0xf6, 0xc7, 0xac, 0x50, 0x21, 0x78, 0xda, 0x46, 0xf5, 0xb9, 0xdf, 0xc0, 0xe9, 0xbc, 0x4f, 0x6e,
	0xf8, 0x66, 0x8e, 0x4c, 0x90, 0x93, 0x65, 0x4a, 0xf4, 0xfe, 0x7c, 0x85, 0x68, 0xe5, 0xd1, 0xb5,
	0x78, 0x31, 0x13, 0x89, 0xd7, 0x34, 0x45, 0x26, 0x22, 0xac, 0x88, 0x8a, 0x4c, 0x24, 0xa3, 0x3c,
	0x4a, 0x4d, 0x24, 0x51, 0xad, 0x44, 0x33, 0xe9, 0xf9, 0x72, 0xa8, 0xc8, 0x44, 0xb2, 0x4a, 0x9f,
	0x26, 0xc8, 0xc9, 0x5a, 0x64, 0x4a, 0x6f, 0x19, 0x85, 0xce, 0x94, 0xde, 0xb2, 0x8a, 0x9a, 0x78,
	0x92, 0x64, 0x61, 0x32, 0x35, 0x49, 0x46, 0xd5, 0x33, 0x35, 0x49, 0x56, 0x85, 0x93, 0x84, 0xb2,
	0xa8, 0x5c, 0x88, 0xee, 0x5d, 0x5b, 0xcb, 0x4c, 0x87, 0x32, 0x41, 0xb5, 0x51, 0x87, 0x12, 0x57,
	0xc8, 0x4b, 0x71, 0x4d, 0xd7, 0x11, 0x53, 0x5c, 0x05, 0x75, 0x40, 0xf4, 0x7d, 0x58, 0x0a, 0xca,
	0x3f, 0x48, 0x90, 0x68, 0xc5, 0xf8, 0xdd, 0xcd, 0x1c, 0x8f, 0xb4, 0x9b, 0x2c, 0x10, 0xa2, 0x19,
	0xf9, 0x60, 0x8c, 0xf9, 0xa3, 0x6b, 0xf1, 0xa2, 0x90, 0xce, 0x17, 0x09, 0x91, 0x9a, 0x21, 0x15,
	0x6f, 0xf0, 0xf7, 0x67, 0xe2, 0x44, 0x21, 0x3d, 0x56, 0xae, 0x4b, 0x85, 0x74, 0x51, 0xd1, 0x30,
	0x15, 0xd2, 0xc5, 0x75, 0x42, 0x13, 0xe4, 0x64, 0x75, 0x2b, 0xa5, 0x99, 0x8c, 0x22, 0x5a, 0x4a,
	0x33, 0x99, 0x65, 0x32, 0x93, 0x94, 0xf2, 0x66, 0x4f, 0x92, 0x51, 0x28, 0x53, 0x1e, 0x5d, 0x8b,
	0x17, 0x45, 0x72, 0x41, 0x89, 0x29, 0x15, 0xc9, 0xb3, 0xcb, 0x5d, 0xa9, 0x48, 0x3e, 0xa3, 0x62,
	0xb5, 0xff, 0xf4, 0xd5, 0x93, 0xd7, 0xee, 0xc8, 0x70, 0x5e, 0xd7, 0x3f, 0xd9, 0xf3, 0xfd, 0xba,
	0xe9, 0x5e, 0x3e, 0x21, 0xff, 0x85, 0x37, 0xdd, 0xd1, 0x13, 0xcf, 0x9a, 0xbc, 0xb1, 0x4d, 0xcb,
	0x8b, 0xff, 0x57, 0xfe, 0xac, 0x48, 0x10, 0x7e, 0xfb, 0xff, 0x03, 0x00, 0x00, 0xff, 0xff, 0xa9,
	0xbb, 0xe2, 0x5b, 0x60, 0x3f, 0x00, 0x00,
}
