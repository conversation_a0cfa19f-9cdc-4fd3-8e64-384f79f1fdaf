// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/ugc-community/event/event.proto

package event // import "golang.52tt.com/protocol/services/ugc-community/event"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import ugc_community "golang.52tt.com/protocol/services/ugc-community"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PostEvent_Event int32

const (
	PostEvent_EVENT_UNSPECIFIED PostEvent_Event = 0
	// 发布帖子
	PostEvent_EVENT_PUBLISHED PostEvent_Event = 1
	// 删除帖子
	PostEvent_EVENT_DELETED PostEvent_Event = 2
	// 禁用帖子
	PostEvent_EVENT_BAN PostEvent_Event = 3
)

var PostEvent_Event_name = map[int32]string{
	0: "EVENT_UNSPECIFIED",
	1: "EVENT_PUBLISHED",
	2: "EVENT_DELETED",
	3: "EVENT_BAN",
}
var PostEvent_Event_value = map[string]int32{
	"EVENT_UNSPECIFIED": 0,
	"EVENT_PUBLISHED":   1,
	"EVENT_DELETED":     2,
	"EVENT_BAN":         3,
}

func (x PostEvent_Event) String() string {
	return proto.EnumName(PostEvent_Event_name, int32(x))
}
func (PostEvent_Event) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{0, 0}
}

type CommentEvent_Action int32

const (
	CommentEvent_ACTION_UNSPECIFIC CommentEvent_Action = 0
	CommentEvent_ACTION_ADD        CommentEvent_Action = 1
	CommentEvent_ACTION_DEL        CommentEvent_Action = 2
)

var CommentEvent_Action_name = map[int32]string{
	0: "ACTION_UNSPECIFIC",
	1: "ACTION_ADD",
	2: "ACTION_DEL",
}
var CommentEvent_Action_value = map[string]int32{
	"ACTION_UNSPECIFIC": 0,
	"ACTION_ADD":        1,
	"ACTION_DEL":        2,
}

func (x CommentEvent_Action) String() string {
	return proto.EnumName(CommentEvent_Action_name, int32(x))
}
func (CommentEvent_Action) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{5, 0}
}

type TopicEvent_Action int32

const (
	TopicEvent_ACTION_UNSPECIFIED TopicEvent_Action = 0
	// 创建话题
	TopicEvent_ACTION_CREATED TopicEvent_Action = 1
	// 修改话题
	TopicEvent_ACTION_UPDATED TopicEvent_Action = 2
	// 删除话题
	TopicEvent_ACTION_DELETED TopicEvent_Action = 3
)

var TopicEvent_Action_name = map[int32]string{
	0: "ACTION_UNSPECIFIED",
	1: "ACTION_CREATED",
	2: "ACTION_UPDATED",
	3: "ACTION_DELETED",
}
var TopicEvent_Action_value = map[string]int32{
	"ACTION_UNSPECIFIED": 0,
	"ACTION_CREATED":     1,
	"ACTION_UPDATED":     2,
	"ACTION_DELETED":     3,
}

func (x TopicEvent_Action) String() string {
	return proto.EnumName(TopicEvent_Action_name, int32(x))
}
func (TopicEvent_Action) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{7, 0}
}

// 帖子事件 topic:ugc_community_post_event
type PostEvent struct {
	Event PostEvent_Event `protobuf:"varint,1,opt,name=event,proto3,enum=event.PostEvent_Event" json:"event,omitempty"`
	// 操作人uid
	Uid uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// 事件触发时间
	TriggeredAt          int64             `protobuf:"varint,3,opt,name=triggered_at,json=triggeredAt,proto3" json:"triggered_at,omitempty"`
	Posts                []*PostEvent_Post `protobuf:"bytes,4,rep,name=posts,proto3" json:"posts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PostEvent) Reset()         { *m = PostEvent{} }
func (m *PostEvent) String() string { return proto.CompactTextString(m) }
func (*PostEvent) ProtoMessage()    {}
func (*PostEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{0}
}
func (m *PostEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostEvent.Unmarshal(m, b)
}
func (m *PostEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostEvent.Marshal(b, m, deterministic)
}
func (dst *PostEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostEvent.Merge(dst, src)
}
func (m *PostEvent) XXX_Size() int {
	return xxx_messageInfo_PostEvent.Size(m)
}
func (m *PostEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_PostEvent.DiscardUnknown(m)
}

var xxx_messageInfo_PostEvent proto.InternalMessageInfo

func (m *PostEvent) GetEvent() PostEvent_Event {
	if m != nil {
		return m.Event
	}
	return PostEvent_EVENT_UNSPECIFIED
}

func (m *PostEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PostEvent) GetTriggeredAt() int64 {
	if m != nil {
		return m.TriggeredAt
	}
	return 0
}

func (m *PostEvent) GetPosts() []*PostEvent_Post {
	if m != nil {
		return m.Posts
	}
	return nil
}

type PostEvent_Post struct {
	// 帖子ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 发帖人uid
	Uid uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// 发帖时间
	PublishedAt int64 `protobuf:"varint,3,opt,name=published_at,json=publishedAt,proto3" json:"published_at,omitempty"`
	// 帖子类型
	Type ugc_community.PostType `protobuf:"varint,4,opt,name=type,proto3,enum=ugc_community.PostType" json:"type,omitempty"`
	// 帖子状态
	State ugc_community.PostState `protobuf:"varint,5,opt,name=state,proto3,enum=ugc_community.PostState" json:"state,omitempty"`
	// 发布来源
	Origin ugc_community.PostOrigin `protobuf:"varint,6,opt,name=origin,proto3,enum=ugc_community.PostOrigin" json:"origin,omitempty"`
	// 文本内容
	Content string `protobuf:"bytes,7,opt,name=content,proto3" json:"content,omitempty"`
	// 附件列表
	Attachments []*ugc_community.Attachment `protobuf:"bytes,8,rep,name=attachments,proto3" json:"attachments,omitempty"`
	// 业务数据 角色id包含在里面
	BizData *ugc_community.PostBizData `protobuf:"bytes,9,opt,name=biz_data,json=bizData,proto3" json:"biz_data,omitempty"`
	// 送审时间
	AuditAt int64 `protobuf:"varint,11,opt,name=audit_at,json=auditAt,proto3" json:"audit_at,omitempty"`
	// 审核结果
	AuditResult ugc_community.AuditResult `protobuf:"varint,12,opt,name=audit_result,json=auditResult,proto3,enum=ugc_community.AuditResult" json:"audit_result,omitempty"`
	// 帖子关联的主题
	TopicIdList []string `protobuf:"bytes,13,rep,name=topic_id_list,json=topicIdList,proto3" json:"topic_id_list,omitempty"`
	// 是否屏蔽
	IsShield             bool     `protobuf:"varint,14,opt,name=is_shield,json=isShield,proto3" json:"is_shield,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostEvent_Post) Reset()         { *m = PostEvent_Post{} }
func (m *PostEvent_Post) String() string { return proto.CompactTextString(m) }
func (*PostEvent_Post) ProtoMessage()    {}
func (*PostEvent_Post) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{0, 0}
}
func (m *PostEvent_Post) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostEvent_Post.Unmarshal(m, b)
}
func (m *PostEvent_Post) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostEvent_Post.Marshal(b, m, deterministic)
}
func (dst *PostEvent_Post) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostEvent_Post.Merge(dst, src)
}
func (m *PostEvent_Post) XXX_Size() int {
	return xxx_messageInfo_PostEvent_Post.Size(m)
}
func (m *PostEvent_Post) XXX_DiscardUnknown() {
	xxx_messageInfo_PostEvent_Post.DiscardUnknown(m)
}

var xxx_messageInfo_PostEvent_Post proto.InternalMessageInfo

func (m *PostEvent_Post) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *PostEvent_Post) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PostEvent_Post) GetPublishedAt() int64 {
	if m != nil {
		return m.PublishedAt
	}
	return 0
}

func (m *PostEvent_Post) GetType() ugc_community.PostType {
	if m != nil {
		return m.Type
	}
	return ugc_community.PostType_POST_TYPE_UNSPECIFED
}

func (m *PostEvent_Post) GetState() ugc_community.PostState {
	if m != nil {
		return m.State
	}
	return ugc_community.PostState_POST_STATE_UNSPECIFIED
}

func (m *PostEvent_Post) GetOrigin() ugc_community.PostOrigin {
	if m != nil {
		return m.Origin
	}
	return ugc_community.PostOrigin_POST_ORIGIN_UNSPECIFIED
}

func (m *PostEvent_Post) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PostEvent_Post) GetAttachments() []*ugc_community.Attachment {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *PostEvent_Post) GetBizData() *ugc_community.PostBizData {
	if m != nil {
		return m.BizData
	}
	return nil
}

func (m *PostEvent_Post) GetAuditAt() int64 {
	if m != nil {
		return m.AuditAt
	}
	return 0
}

func (m *PostEvent_Post) GetAuditResult() ugc_community.AuditResult {
	if m != nil {
		return m.AuditResult
	}
	return ugc_community.AuditResult_AUDIT_RESULT_UNSPECIFIED
}

func (m *PostEvent_Post) GetTopicIdList() []string {
	if m != nil {
		return m.TopicIdList
	}
	return nil
}

func (m *PostEvent_Post) GetIsShield() bool {
	if m != nil {
		return m.IsShield
	}
	return false
}

// 点赞事件
type AttitudeEvent struct {
	PostInfo             *AttitudeEventPostInfo       `protobuf:"bytes,1,opt,name=post_info,json=postInfo,proto3" json:"post_info,omitempty"`
	CommentInfo          *AttitudeEventCommentInfo    `protobuf:"bytes,2,opt,name=comment_info,json=commentInfo,proto3" json:"comment_info,omitempty"`
	AttitudeAction       ugc_community.AttitudeAction `protobuf:"varint,3,opt,name=attitude_action,json=attitudeAction,proto3,enum=ugc_community.AttitudeAction" json:"attitude_action,omitempty"`
	ObjectType           ugc_community.ObjectType     `protobuf:"varint,4,opt,name=object_type,json=objectType,proto3,enum=ugc_community.ObjectType" json:"object_type,omitempty"`
	HappenTime           int64                        `protobuf:"varint,5,opt,name=happen_time,json=happenTime,proto3" json:"happen_time,omitempty"`
	UserId               uint32                       `protobuf:"varint,6,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *AttitudeEvent) Reset()         { *m = AttitudeEvent{} }
func (m *AttitudeEvent) String() string { return proto.CompactTextString(m) }
func (*AttitudeEvent) ProtoMessage()    {}
func (*AttitudeEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{1}
}
func (m *AttitudeEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttitudeEvent.Unmarshal(m, b)
}
func (m *AttitudeEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttitudeEvent.Marshal(b, m, deterministic)
}
func (dst *AttitudeEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttitudeEvent.Merge(dst, src)
}
func (m *AttitudeEvent) XXX_Size() int {
	return xxx_messageInfo_AttitudeEvent.Size(m)
}
func (m *AttitudeEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_AttitudeEvent.DiscardUnknown(m)
}

var xxx_messageInfo_AttitudeEvent proto.InternalMessageInfo

func (m *AttitudeEvent) GetPostInfo() *AttitudeEventPostInfo {
	if m != nil {
		return m.PostInfo
	}
	return nil
}

func (m *AttitudeEvent) GetCommentInfo() *AttitudeEventCommentInfo {
	if m != nil {
		return m.CommentInfo
	}
	return nil
}

func (m *AttitudeEvent) GetAttitudeAction() ugc_community.AttitudeAction {
	if m != nil {
		return m.AttitudeAction
	}
	return ugc_community.AttitudeAction_ATTITUDE_ACTION_UNSPECIFIED
}

func (m *AttitudeEvent) GetObjectType() ugc_community.ObjectType {
	if m != nil {
		return m.ObjectType
	}
	return ugc_community.ObjectType_OBJECT_TYPE_UNSPECIFIED
}

func (m *AttitudeEvent) GetHappenTime() int64 {
	if m != nil {
		return m.HappenTime
	}
	return 0
}

func (m *AttitudeEvent) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type AttitudeEventPostInfo struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PosterId             uint32   `protobuf:"varint,2,opt,name=poster_id,json=posterId,proto3" json:"poster_id,omitempty"`
	AttitudeNum          uint32   `protobuf:"varint,3,opt,name=attitude_num,json=attitudeNum,proto3" json:"attitude_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AttitudeEventPostInfo) Reset()         { *m = AttitudeEventPostInfo{} }
func (m *AttitudeEventPostInfo) String() string { return proto.CompactTextString(m) }
func (*AttitudeEventPostInfo) ProtoMessage()    {}
func (*AttitudeEventPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{2}
}
func (m *AttitudeEventPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttitudeEventPostInfo.Unmarshal(m, b)
}
func (m *AttitudeEventPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttitudeEventPostInfo.Marshal(b, m, deterministic)
}
func (dst *AttitudeEventPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttitudeEventPostInfo.Merge(dst, src)
}
func (m *AttitudeEventPostInfo) XXX_Size() int {
	return xxx_messageInfo_AttitudeEventPostInfo.Size(m)
}
func (m *AttitudeEventPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AttitudeEventPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AttitudeEventPostInfo proto.InternalMessageInfo

func (m *AttitudeEventPostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AttitudeEventPostInfo) GetPosterId() uint32 {
	if m != nil {
		return m.PosterId
	}
	return 0
}

func (m *AttitudeEventPostInfo) GetAttitudeNum() uint32 {
	if m != nil {
		return m.AttitudeNum
	}
	return 0
}

type AttitudeEventCommentInfo struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	CommenterId          uint32   `protobuf:"varint,3,opt,name=commenter_id,json=commenterId,proto3" json:"commenter_id,omitempty"`
	AttitudeNum          uint32   `protobuf:"varint,4,opt,name=attitude_num,json=attitudeNum,proto3" json:"attitude_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AttitudeEventCommentInfo) Reset()         { *m = AttitudeEventCommentInfo{} }
func (m *AttitudeEventCommentInfo) String() string { return proto.CompactTextString(m) }
func (*AttitudeEventCommentInfo) ProtoMessage()    {}
func (*AttitudeEventCommentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{3}
}
func (m *AttitudeEventCommentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttitudeEventCommentInfo.Unmarshal(m, b)
}
func (m *AttitudeEventCommentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttitudeEventCommentInfo.Marshal(b, m, deterministic)
}
func (dst *AttitudeEventCommentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttitudeEventCommentInfo.Merge(dst, src)
}
func (m *AttitudeEventCommentInfo) XXX_Size() int {
	return xxx_messageInfo_AttitudeEventCommentInfo.Size(m)
}
func (m *AttitudeEventCommentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AttitudeEventCommentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AttitudeEventCommentInfo proto.InternalMessageInfo

func (m *AttitudeEventCommentInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AttitudeEventCommentInfo) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *AttitudeEventCommentInfo) GetCommenterId() uint32 {
	if m != nil {
		return m.CommenterId
	}
	return 0
}

func (m *AttitudeEventCommentInfo) GetAttitudeNum() uint32 {
	if m != nil {
		return m.AttitudeNum
	}
	return 0
}

// 官方操作帖子事件强插帖子、屏蔽帖子
type OfficialHandlePostEvent struct {
	PostInfo             []*OfficialHandlePostEvent_ExposedPostInfo `protobuf:"bytes,1,rep,name=post_info,json=postInfo,proto3" json:"post_info,omitempty"`
	AllForceInsertInfo   []*OfficialHandlePostEvent_InsertPostInfo  `protobuf:"bytes,2,rep,name=all_force_insert_info,json=allForceInsertInfo,proto3" json:"all_force_insert_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-"`
	XXX_unrecognized     []byte                                     `json:"-"`
	XXX_sizecache        int32                                      `json:"-"`
}

func (m *OfficialHandlePostEvent) Reset()         { *m = OfficialHandlePostEvent{} }
func (m *OfficialHandlePostEvent) String() string { return proto.CompactTextString(m) }
func (*OfficialHandlePostEvent) ProtoMessage()    {}
func (*OfficialHandlePostEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{4}
}
func (m *OfficialHandlePostEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfficialHandlePostEvent.Unmarshal(m, b)
}
func (m *OfficialHandlePostEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfficialHandlePostEvent.Marshal(b, m, deterministic)
}
func (dst *OfficialHandlePostEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfficialHandlePostEvent.Merge(dst, src)
}
func (m *OfficialHandlePostEvent) XXX_Size() int {
	return xxx_messageInfo_OfficialHandlePostEvent.Size(m)
}
func (m *OfficialHandlePostEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_OfficialHandlePostEvent.DiscardUnknown(m)
}

var xxx_messageInfo_OfficialHandlePostEvent proto.InternalMessageInfo

func (m *OfficialHandlePostEvent) GetPostInfo() []*OfficialHandlePostEvent_ExposedPostInfo {
	if m != nil {
		return m.PostInfo
	}
	return nil
}

func (m *OfficialHandlePostEvent) GetAllForceInsertInfo() []*OfficialHandlePostEvent_InsertPostInfo {
	if m != nil {
		return m.AllForceInsertInfo
	}
	return nil
}

type OfficialHandlePostEvent_InsertPostInfo struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	InsertPos            uint32   `protobuf:"varint,2,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	SubjectId            string   `protobuf:"bytes,3,opt,name=subject_id,json=subjectId,proto3" json:"subject_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfficialHandlePostEvent_InsertPostInfo) Reset() {
	*m = OfficialHandlePostEvent_InsertPostInfo{}
}
func (m *OfficialHandlePostEvent_InsertPostInfo) String() string { return proto.CompactTextString(m) }
func (*OfficialHandlePostEvent_InsertPostInfo) ProtoMessage()    {}
func (*OfficialHandlePostEvent_InsertPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{4, 0}
}
func (m *OfficialHandlePostEvent_InsertPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfficialHandlePostEvent_InsertPostInfo.Unmarshal(m, b)
}
func (m *OfficialHandlePostEvent_InsertPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfficialHandlePostEvent_InsertPostInfo.Marshal(b, m, deterministic)
}
func (dst *OfficialHandlePostEvent_InsertPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfficialHandlePostEvent_InsertPostInfo.Merge(dst, src)
}
func (m *OfficialHandlePostEvent_InsertPostInfo) XXX_Size() int {
	return xxx_messageInfo_OfficialHandlePostEvent_InsertPostInfo.Size(m)
}
func (m *OfficialHandlePostEvent_InsertPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OfficialHandlePostEvent_InsertPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OfficialHandlePostEvent_InsertPostInfo proto.InternalMessageInfo

func (m *OfficialHandlePostEvent_InsertPostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *OfficialHandlePostEvent_InsertPostInfo) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *OfficialHandlePostEvent_InsertPostInfo) GetSubjectId() string {
	if m != nil {
		return m.SubjectId
	}
	return ""
}

type OfficialHandlePostEvent_ExposedPostInfo struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	IsExposed            bool     `protobuf:"varint,2,opt,name=is_exposed,json=isExposed,proto3" json:"is_exposed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfficialHandlePostEvent_ExposedPostInfo) Reset() {
	*m = OfficialHandlePostEvent_ExposedPostInfo{}
}
func (m *OfficialHandlePostEvent_ExposedPostInfo) String() string { return proto.CompactTextString(m) }
func (*OfficialHandlePostEvent_ExposedPostInfo) ProtoMessage()    {}
func (*OfficialHandlePostEvent_ExposedPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{4, 1}
}
func (m *OfficialHandlePostEvent_ExposedPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfficialHandlePostEvent_ExposedPostInfo.Unmarshal(m, b)
}
func (m *OfficialHandlePostEvent_ExposedPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfficialHandlePostEvent_ExposedPostInfo.Marshal(b, m, deterministic)
}
func (dst *OfficialHandlePostEvent_ExposedPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfficialHandlePostEvent_ExposedPostInfo.Merge(dst, src)
}
func (m *OfficialHandlePostEvent_ExposedPostInfo) XXX_Size() int {
	return xxx_messageInfo_OfficialHandlePostEvent_ExposedPostInfo.Size(m)
}
func (m *OfficialHandlePostEvent_ExposedPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OfficialHandlePostEvent_ExposedPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OfficialHandlePostEvent_ExposedPostInfo proto.InternalMessageInfo

func (m *OfficialHandlePostEvent_ExposedPostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *OfficialHandlePostEvent_ExposedPostInfo) GetIsExposed() bool {
	if m != nil {
		return m.IsExposed
	}
	return false
}

// 评论事件 topic:ugc_community_comment_event
type CommentEvent struct {
	Action    CommentEvent_Action `protobuf:"varint,1,opt,name=action,proto3,enum=event.CommentEvent_Action" json:"action,omitempty"`
	PostId    string              `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId string              `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	Content   string              `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Ts        int64               `protobuf:"varint,5,opt,name=ts,proto3" json:"ts,omitempty"`
	UserId    uint32              `protobuf:"varint,6,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 首次评论id
	RootParentId string `protobuf:"bytes,7,opt,name=root_parent_id,json=rootParentId,proto3" json:"root_parent_id,omitempty"`
	// 回复的评论id
	ParentId string `protobuf:"bytes,8,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 当前帖子总评论数
	PostCommentCount uint32 `protobuf:"varint,9,opt,name=post_comment_count,json=postCommentCount,proto3" json:"post_comment_count,omitempty"`
	// 发布评论的实体信息
	SendEntity *ugc_community.CommentEntity `protobuf:"bytes,10,opt,name=send_entity,json=sendEntity,proto3" json:"send_entity,omitempty"`
	// 回复的实体信息
	ReplyEntity          *ugc_community.CommentEntity `protobuf:"bytes,11,opt,name=reply_entity,json=replyEntity,proto3" json:"reply_entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *CommentEvent) Reset()         { *m = CommentEvent{} }
func (m *CommentEvent) String() string { return proto.CompactTextString(m) }
func (*CommentEvent) ProtoMessage()    {}
func (*CommentEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{5}
}
func (m *CommentEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentEvent.Unmarshal(m, b)
}
func (m *CommentEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentEvent.Marshal(b, m, deterministic)
}
func (dst *CommentEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentEvent.Merge(dst, src)
}
func (m *CommentEvent) XXX_Size() int {
	return xxx_messageInfo_CommentEvent.Size(m)
}
func (m *CommentEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentEvent.DiscardUnknown(m)
}

var xxx_messageInfo_CommentEvent proto.InternalMessageInfo

func (m *CommentEvent) GetAction() CommentEvent_Action {
	if m != nil {
		return m.Action
	}
	return CommentEvent_ACTION_UNSPECIFIC
}

func (m *CommentEvent) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *CommentEvent) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *CommentEvent) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CommentEvent) GetTs() int64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *CommentEvent) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *CommentEvent) GetRootParentId() string {
	if m != nil {
		return m.RootParentId
	}
	return ""
}

func (m *CommentEvent) GetParentId() string {
	if m != nil {
		return m.ParentId
	}
	return ""
}

func (m *CommentEvent) GetPostCommentCount() uint32 {
	if m != nil {
		return m.PostCommentCount
	}
	return 0
}

func (m *CommentEvent) GetSendEntity() *ugc_community.CommentEntity {
	if m != nil {
		return m.SendEntity
	}
	return nil
}

func (m *CommentEvent) GetReplyEntity() *ugc_community.CommentEntity {
	if m != nil {
		return m.ReplyEntity
	}
	return nil
}

// 主题事件 topic:ugc_community_subject_event
type SubjectEvent struct {
	// 事件触发时间
	TriggeredAt int64 `protobuf:"varint,1,opt,name=triggered_at,json=triggeredAt,proto3" json:"triggered_at,omitempty"`
	// 变更的话题列表
	Subjects             []*SubjectEvent_SubjectTab `protobuf:"bytes,2,rep,name=subjects,proto3" json:"subjects,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *SubjectEvent) Reset()         { *m = SubjectEvent{} }
func (m *SubjectEvent) String() string { return proto.CompactTextString(m) }
func (*SubjectEvent) ProtoMessage()    {}
func (*SubjectEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{6}
}
func (m *SubjectEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubjectEvent.Unmarshal(m, b)
}
func (m *SubjectEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubjectEvent.Marshal(b, m, deterministic)
}
func (dst *SubjectEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubjectEvent.Merge(dst, src)
}
func (m *SubjectEvent) XXX_Size() int {
	return xxx_messageInfo_SubjectEvent.Size(m)
}
func (m *SubjectEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_SubjectEvent.DiscardUnknown(m)
}

var xxx_messageInfo_SubjectEvent proto.InternalMessageInfo

func (m *SubjectEvent) GetTriggeredAt() int64 {
	if m != nil {
		return m.TriggeredAt
	}
	return 0
}

func (m *SubjectEvent) GetSubjects() []*SubjectEvent_SubjectTab {
	if m != nil {
		return m.Subjects
	}
	return nil
}

type SubjectEvent_StickyPostInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubjectEvent_StickyPostInfo) Reset()         { *m = SubjectEvent_StickyPostInfo{} }
func (m *SubjectEvent_StickyPostInfo) String() string { return proto.CompactTextString(m) }
func (*SubjectEvent_StickyPostInfo) ProtoMessage()    {}
func (*SubjectEvent_StickyPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{6, 0}
}
func (m *SubjectEvent_StickyPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubjectEvent_StickyPostInfo.Unmarshal(m, b)
}
func (m *SubjectEvent_StickyPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubjectEvent_StickyPostInfo.Marshal(b, m, deterministic)
}
func (dst *SubjectEvent_StickyPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubjectEvent_StickyPostInfo.Merge(dst, src)
}
func (m *SubjectEvent_StickyPostInfo) XXX_Size() int {
	return xxx_messageInfo_SubjectEvent_StickyPostInfo.Size(m)
}
func (m *SubjectEvent_StickyPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SubjectEvent_StickyPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SubjectEvent_StickyPostInfo proto.InternalMessageInfo

func (m *SubjectEvent_StickyPostInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type SubjectEvent_SubjectTab struct {
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 主题名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 置顶帖子
	StickyPostList []*SubjectEvent_StickyPostInfo `protobuf:"bytes,3,rep,name=sticky_post_list,json=stickyPostList,proto3" json:"sticky_post_list,omitempty"`
	// 主题类型
	Type                 ugc_community.SubjectType `protobuf:"varint,4,opt,name=type,proto3,enum=ugc_community.SubjectType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *SubjectEvent_SubjectTab) Reset()         { *m = SubjectEvent_SubjectTab{} }
func (m *SubjectEvent_SubjectTab) String() string { return proto.CompactTextString(m) }
func (*SubjectEvent_SubjectTab) ProtoMessage()    {}
func (*SubjectEvent_SubjectTab) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{6, 1}
}
func (m *SubjectEvent_SubjectTab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubjectEvent_SubjectTab.Unmarshal(m, b)
}
func (m *SubjectEvent_SubjectTab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubjectEvent_SubjectTab.Marshal(b, m, deterministic)
}
func (dst *SubjectEvent_SubjectTab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubjectEvent_SubjectTab.Merge(dst, src)
}
func (m *SubjectEvent_SubjectTab) XXX_Size() int {
	return xxx_messageInfo_SubjectEvent_SubjectTab.Size(m)
}
func (m *SubjectEvent_SubjectTab) XXX_DiscardUnknown() {
	xxx_messageInfo_SubjectEvent_SubjectTab.DiscardUnknown(m)
}

var xxx_messageInfo_SubjectEvent_SubjectTab proto.InternalMessageInfo

func (m *SubjectEvent_SubjectTab) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SubjectEvent_SubjectTab) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SubjectEvent_SubjectTab) GetStickyPostList() []*SubjectEvent_StickyPostInfo {
	if m != nil {
		return m.StickyPostList
	}
	return nil
}

func (m *SubjectEvent_SubjectTab) GetType() ugc_community.SubjectType {
	if m != nil {
		return m.Type
	}
	return ugc_community.SubjectType_SUBJECT_TYPE_UNSPECIFIED
}

// 话题事件 topic:ugc_community_topic_event
type TopicEvent struct {
	Action TopicEvent_Action `protobuf:"varint,1,opt,name=action,proto3,enum=event.TopicEvent_Action" json:"action,omitempty"`
	// 事件触发时间
	TriggeredAt int64 `protobuf:"varint,2,opt,name=triggered_at,json=triggeredAt,proto3" json:"triggered_at,omitempty"`
	// 变更的话题列表
	Topics               []*TopicEvent_Topic `protobuf:"bytes,3,rep,name=topics,proto3" json:"topics,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *TopicEvent) Reset()         { *m = TopicEvent{} }
func (m *TopicEvent) String() string { return proto.CompactTextString(m) }
func (*TopicEvent) ProtoMessage()    {}
func (*TopicEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{7}
}
func (m *TopicEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicEvent.Unmarshal(m, b)
}
func (m *TopicEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicEvent.Marshal(b, m, deterministic)
}
func (dst *TopicEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicEvent.Merge(dst, src)
}
func (m *TopicEvent) XXX_Size() int {
	return xxx_messageInfo_TopicEvent.Size(m)
}
func (m *TopicEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicEvent.DiscardUnknown(m)
}

var xxx_messageInfo_TopicEvent proto.InternalMessageInfo

func (m *TopicEvent) GetAction() TopicEvent_Action {
	if m != nil {
		return m.Action
	}
	return TopicEvent_ACTION_UNSPECIFIED
}

func (m *TopicEvent) GetTriggeredAt() int64 {
	if m != nil {
		return m.TriggeredAt
	}
	return 0
}

func (m *TopicEvent) GetTopics() []*TopicEvent_Topic {
	if m != nil {
		return m.Topics
	}
	return nil
}

type TopicEvent_Topic struct {
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 话题类型
	Type ugc_community.TopicType `protobuf:"varint,2,opt,name=type,proto3,enum=ugc_community.TopicType" json:"type,omitempty"`
	// 创建时间戳(秒)
	CreatedAt int64 `protobuf:"varint,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 修改时间戳(秒)
	UpdatedAt int64 `protobuf:"varint,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// 话题名称
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// 话题排序
	Sort                 uint32   `protobuf:"varint,6,opt,name=sort,proto3" json:"sort,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicEvent_Topic) Reset()         { *m = TopicEvent_Topic{} }
func (m *TopicEvent_Topic) String() string { return proto.CompactTextString(m) }
func (*TopicEvent_Topic) ProtoMessage()    {}
func (*TopicEvent_Topic) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_d65c6957ebf6b1f1, []int{7, 0}
}
func (m *TopicEvent_Topic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicEvent_Topic.Unmarshal(m, b)
}
func (m *TopicEvent_Topic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicEvent_Topic.Marshal(b, m, deterministic)
}
func (dst *TopicEvent_Topic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicEvent_Topic.Merge(dst, src)
}
func (m *TopicEvent_Topic) XXX_Size() int {
	return xxx_messageInfo_TopicEvent_Topic.Size(m)
}
func (m *TopicEvent_Topic) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicEvent_Topic.DiscardUnknown(m)
}

var xxx_messageInfo_TopicEvent_Topic proto.InternalMessageInfo

func (m *TopicEvent_Topic) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *TopicEvent_Topic) GetType() ugc_community.TopicType {
	if m != nil {
		return m.Type
	}
	return ugc_community.TopicType_TOPIC_TYPE_UNSPECIFIED
}

func (m *TopicEvent_Topic) GetCreatedAt() int64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *TopicEvent_Topic) GetUpdatedAt() int64 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *TopicEvent_Topic) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TopicEvent_Topic) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func init() {
	proto.RegisterType((*PostEvent)(nil), "event.PostEvent")
	proto.RegisterType((*PostEvent_Post)(nil), "event.PostEvent.Post")
	proto.RegisterType((*AttitudeEvent)(nil), "event.AttitudeEvent")
	proto.RegisterType((*AttitudeEventPostInfo)(nil), "event.AttitudeEventPostInfo")
	proto.RegisterType((*AttitudeEventCommentInfo)(nil), "event.AttitudeEventCommentInfo")
	proto.RegisterType((*OfficialHandlePostEvent)(nil), "event.OfficialHandlePostEvent")
	proto.RegisterType((*OfficialHandlePostEvent_InsertPostInfo)(nil), "event.OfficialHandlePostEvent.InsertPostInfo")
	proto.RegisterType((*OfficialHandlePostEvent_ExposedPostInfo)(nil), "event.OfficialHandlePostEvent.ExposedPostInfo")
	proto.RegisterType((*CommentEvent)(nil), "event.CommentEvent")
	proto.RegisterType((*SubjectEvent)(nil), "event.SubjectEvent")
	proto.RegisterType((*SubjectEvent_StickyPostInfo)(nil), "event.SubjectEvent.StickyPostInfo")
	proto.RegisterType((*SubjectEvent_SubjectTab)(nil), "event.SubjectEvent.SubjectTab")
	proto.RegisterType((*TopicEvent)(nil), "event.TopicEvent")
	proto.RegisterType((*TopicEvent_Topic)(nil), "event.TopicEvent.Topic")
	proto.RegisterEnum("event.PostEvent_Event", PostEvent_Event_name, PostEvent_Event_value)
	proto.RegisterEnum("event.CommentEvent_Action", CommentEvent_Action_name, CommentEvent_Action_value)
	proto.RegisterEnum("event.TopicEvent_Action", TopicEvent_Action_name, TopicEvent_Action_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/ugc-community/event/event.proto", fileDescriptor_event_d65c6957ebf6b1f1)
}

var fileDescriptor_event_d65c6957ebf6b1f1 = []byte{
	// 1307 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x57, 0xcd, 0x6e, 0xdb, 0xc6,
	0x13, 0x8f, 0xa8, 0x0f, 0x8b, 0x43, 0x49, 0x51, 0xf6, 0x0f, 0xc7, 0x8c, 0xfe, 0x4d, 0xa3, 0x0a,
	0x3d, 0x08, 0x68, 0x22, 0xa7, 0x2a, 0x82, 0xa2, 0x29, 0x82, 0x40, 0xb6, 0x14, 0x44, 0xa8, 0x61,
	0x1b, 0xb4, 0xdc, 0x43, 0x51, 0x80, 0xa5, 0xc9, 0xb5, 0xbc, 0x0d, 0x45, 0xb2, 0xdc, 0xa5, 0x51,
	0xe7, 0xdc, 0x87, 0xe8, 0xa9, 0x2f, 0x50, 0xa0, 0xcf, 0xd0, 0x07, 0xe8, 0xbb, 0xf4, 0x5c, 0xa0,
	0x87, 0x62, 0x67, 0x57, 0xd4, 0xa7, 0xdd, 0x5e, 0x84, 0x9d, 0xd9, 0xdf, 0x7c, 0x70, 0xf6, 0x37,
	0xbb, 0x23, 0xe8, 0x09, 0xb1, 0xff, 0x43, 0xc6, 0xfc, 0x77, 0x9c, 0x85, 0xd7, 0x34, 0xdd, 0xcf,
	0xa6, 0xfe, 0x33, 0x3f, 0x9e, 0xcd, 0xb2, 0x88, 0x89, 0x9b, 0x7d, 0x7a, 0x4d, 0x23, 0xa1, 0x7e,
	0x7b, 0x49, 0x1a, 0x8b, 0x98, 0x94, 0x51, 0x68, 0x3d, 0xbf, 0xd3, 0x6c, 0x45, 0x52, 0x86, 0x9d,
	0xbf, 0xca, 0x60, 0x9e, 0xc6, 0x5c, 0x8c, 0xa4, 0x3d, 0x79, 0x0a, 0xca, 0x91, 0x5d, 0x68, 0x17,
	0xba, 0x8d, 0xfe, 0xc3, 0x9e, 0x8a, 0x91, 0x03, 0x7a, 0xf8, 0xeb, 0x28, 0x10, 0x69, 0x42, 0x31,
	0x63, 0x81, 0x6d, 0xb4, 0x0b, 0xdd, 0xba, 0x23, 0x97, 0xe4, 0x23, 0xa8, 0x89, 0x94, 0x4d, 0xa7,
	0x34, 0xa5, 0x81, 0xeb, 0x09, 0xbb, 0xd8, 0x2e, 0x74, 0x8b, 0x8e, 0x95, 0xeb, 0x06, 0x82, 0x7c,
	0x02, 0xe5, 0x24, 0xe6, 0x82, 0xdb, 0xa5, 0x76, 0xb1, 0x6b, 0xf5, 0x77, 0x37, 0x42, 0xc8, 0x95,
	0xa3, 0x30, 0xad, 0x9f, 0x4a, 0x50, 0x92, 0x32, 0x69, 0x80, 0xc1, 0x02, 0xcc, 0xca, 0x74, 0x0c,
	0x16, 0x6c, 0x0f, 0x9d, 0x64, 0x17, 0x21, 0xe3, 0x57, 0x2b, 0xa1, 0x73, 0x1d, 0x86, 0x2e, 0x89,
	0x9b, 0x84, 0xda, 0x25, 0xfc, 0xb8, 0xbd, 0x5e, 0x36, 0xf5, 0xdd, 0x45, 0x3d, 0x64, 0x9c, 0xc9,
	0x4d, 0x42, 0x1d, 0x04, 0x91, 0x1e, 0x94, 0xb9, 0xf0, 0x04, 0xb5, 0xcb, 0x88, 0xb6, 0xb7, 0xa0,
	0xcf, 0xe4, 0xbe, 0xa3, 0x60, 0xe4, 0x53, 0xa8, 0xc4, 0x29, 0x9b, 0xb2, 0xc8, 0xae, 0xa0, 0xc1,
	0xa3, 0x2d, 0x06, 0x27, 0x08, 0x70, 0x34, 0x90, 0xd8, 0xb0, 0xe3, 0xc7, 0x91, 0x90, 0xf5, 0xde,
	0xc1, 0x2f, 0x9b, 0x8b, 0xe4, 0x4b, 0xb0, 0x3c, 0x21, 0x3c, 0xff, 0x6a, 0x46, 0x23, 0xc1, 0xed,
	0x2a, 0x96, 0x6a, 0xdd, 0xe3, 0x20, 0x47, 0x38, 0xcb, 0x68, 0xf2, 0x02, 0xaa, 0x17, 0xec, 0xbd,
	0x1b, 0x78, 0xc2, 0xb3, 0xcd, 0x76, 0xa1, 0x6b, 0xf5, 0x5b, 0x5b, 0x72, 0x39, 0x60, 0xef, 0x87,
	0x9e, 0xf0, 0x9c, 0x9d, 0x0b, 0xb5, 0x20, 0x8f, 0xa0, 0xea, 0x65, 0x01, 0x13, 0xb2, 0x78, 0x16,
	0x16, 0x6f, 0x07, 0xe5, 0x81, 0x20, 0xaf, 0xa0, 0xa6, 0xb6, 0x52, 0xca, 0xb3, 0x50, 0xd8, 0x35,
	0xfc, 0xc2, 0x75, 0xaf, 0x03, 0x09, 0x71, 0x10, 0xe1, 0x58, 0xde, 0x42, 0x20, 0x1d, 0xa8, 0x8b,
	0x38, 0x61, 0xbe, 0xcb, 0x02, 0x37, 0x64, 0x5c, 0xd8, 0xf5, 0x76, 0xb1, 0x6b, 0x3a, 0x16, 0x2a,
	0xc7, 0xc1, 0x11, 0xe3, 0x82, 0xfc, 0x1f, 0x4c, 0xc6, 0x5d, 0x7e, 0xc5, 0x68, 0x18, 0xd8, 0x8d,
	0x76, 0xa1, 0x5b, 0x75, 0xaa, 0x8c, 0x9f, 0xa1, 0xdc, 0x39, 0x87, 0xb2, 0xe2, 0xe7, 0x2e, 0x3c,
	0x18, 0x7d, 0x3d, 0x3a, 0x9e, 0xb8, 0xe7, 0xc7, 0x67, 0xa7, 0xa3, 0xc3, 0xf1, 0x9b, 0xf1, 0x68,
	0xd8, 0xbc, 0x47, 0xfe, 0x07, 0xf7, 0x95, 0xfa, 0xf4, 0xfc, 0xe0, 0x68, 0x7c, 0xf6, 0x76, 0x34,
	0x6c, 0x16, 0xc8, 0x03, 0xa8, 0x2b, 0xe5, 0x70, 0x74, 0x34, 0x9a, 0x8c, 0x86, 0x4d, 0x83, 0xd4,
	0xc1, 0x54, 0xaa, 0x83, 0xc1, 0x71, 0xb3, 0xd8, 0xf9, 0xc3, 0x80, 0xfa, 0x40, 0x08, 0x26, 0xb2,
	0x80, 0x2a, 0xff, 0x5f, 0x80, 0x29, 0x89, 0xe7, 0xb2, 0xe8, 0x32, 0x46, 0xb6, 0x59, 0xfd, 0x0f,
	0x34, 0x41, 0x57, 0x80, 0xb2, 0x80, 0xe3, 0xe8, 0x32, 0x76, 0xaa, 0x89, 0x5e, 0x91, 0x03, 0xa8,
	0xc9, 0x52, 0xd0, 0x48, 0x5b, 0x1b, 0x68, 0xfd, 0x64, 0x9b, 0xf5, 0xa1, 0xc2, 0xa1, 0x03, 0xcb,
	0x5f, 0x08, 0xe4, 0x0d, 0xdc, 0xf7, 0x34, 0xd0, 0xf5, 0x7c, 0xc1, 0xe2, 0x08, 0x69, 0xdc, 0xe8,
	0x3f, 0xde, 0x3c, 0x7a, 0x44, 0x0d, 0x10, 0xe4, 0x34, 0xbc, 0x15, 0x99, 0xbc, 0x04, 0x2b, 0xbe,
	0xf8, 0x9e, 0xfa, 0xc2, 0x5d, 0xe2, 0xfb, 0x3a, 0x7d, 0x4e, 0x10, 0x81, 0x8c, 0x87, 0x38, 0x5f,
	0x93, 0x27, 0x60, 0x5d, 0x79, 0x49, 0x42, 0x23, 0x57, 0xb0, 0x99, 0x62, 0x7f, 0xd1, 0x01, 0xa5,
	0x9a, 0xb0, 0x19, 0x25, 0x7b, 0xb0, 0x93, 0x71, 0x9a, 0xba, 0x2c, 0x40, 0xa6, 0xd7, 0x9d, 0x8a,
	0x14, 0xc7, 0x41, 0x27, 0x81, 0xdd, 0xad, 0x45, 0x92, 0x16, 0xaa, 0xaa, 0xf3, 0x0e, 0xae, 0x60,
	0xd5, 0x02, 0x79, 0xe8, 0x72, 0xa5, 0x9c, 0xa9, 0x5e, 0xae, 0x2a, 0xc5, 0x18, 0x1b, 0x3a, 0x2f,
	0x46, 0x94, 0xcd, 0xb0, 0x12, 0x75, 0x64, 0x3a, 0xea, 0x8e, 0xb3, 0x59, 0xe7, 0xe7, 0x02, 0xd8,
	0xb7, 0x55, 0xf6, 0xf6, 0xa8, 0x8f, 0x01, 0xf2, 0x93, 0x52, 0x61, 0x4d, 0xc7, 0x9c, 0x1f, 0x03,
	0xc6, 0xd5, 0x82, 0xca, 0x4b, 0xc7, 0xcd, 0x75, 0x5b, 0x52, 0x2b, 0x6d, 0xa6, 0xf6, 0xb7, 0x01,
	0x7b, 0x27, 0x97, 0x97, 0xcc, 0x67, 0x5e, 0xf8, 0xd6, 0x8b, 0x82, 0x90, 0x2e, 0x6e, 0xd9, 0xaf,
	0x56, 0x59, 0x26, 0x7b, 0xbb, 0xa7, 0x79, 0x72, 0x8b, 0x49, 0x6f, 0xf4, 0x63, 0x12, 0x73, 0x1a,
	0x6c, 0xe1, 0xdd, 0x77, 0xb0, 0xeb, 0x85, 0xa1, 0x7b, 0x19, 0xa7, 0x3e, 0x75, 0x59, 0xc4, 0x69,
	0x9a, 0x13, 0x50, 0x3a, 0x7e, 0xf6, 0x2f, 0x8e, 0xc7, 0x68, 0x91, 0xfb, 0x25, 0x5e, 0x18, 0xbe,
	0x91, 0xae, 0x94, 0x5e, 0xea, 0x5a, 0x53, 0x68, 0xac, 0xa2, 0xee, 0x2c, 0xad, 0x4e, 0x21, 0x89,
	0xb9, 0x3e, 0x51, 0x93, 0xcd, 0x8d, 0xe5, 0x36, 0xcf, 0x14, 0x31, 0x75, 0x61, 0x4d, 0xc7, 0xd4,
	0x9a, 0x71, 0xd0, 0x1a, 0xc3, 0xfd, 0xb5, 0xef, 0xbc, 0x3b, 0x12, 0x77, 0xa9, 0x82, 0x63, 0xa4,
	0xaa, 0x63, 0x32, 0xae, 0xed, 0x3b, 0x7f, 0x16, 0xa1, 0xa6, 0xc9, 0xa0, 0x6a, 0xde, 0x87, 0x8a,
	0xee, 0xa8, 0x82, 0xbe, 0xbc, 0x54, 0x5d, 0x96, 0x41, 0x3d, 0xdd, 0x4e, 0x1a, 0xb9, 0x1c, 0xdc,
	0xb8, 0x83, 0x41, 0xc5, 0x75, 0x06, 0x2d, 0xdd, 0xeb, 0xa5, 0xd5, 0x7b, 0xbd, 0x01, 0x86, 0xe0,
	0xba, 0xa7, 0x0c, 0xc1, 0x6f, 0xed, 0x25, 0xf2, 0x31, 0x34, 0xd2, 0x38, 0x16, 0x6e, 0xe2, 0xa5,
	0x3a, 0x8a, 0x7a, 0x21, 0x6a, 0x52, 0x7b, 0x8a, 0x4a, 0xdd, 0x3f, 0x39, 0xa0, 0x8a, 0x80, 0x6a,
	0x32, 0xdf, 0x7c, 0x0a, 0x04, 0xb3, 0x9f, 0x67, 0xea, 0xc7, 0x59, 0x24, 0xf0, 0x41, 0xa8, 0x3b,
	0x4d, 0xb9, 0xa3, 0x3f, 0xfd, 0x50, 0xea, 0xc9, 0x2b, 0xb0, 0x38, 0x8d, 0x02, 0x97, 0x46, 0x82,
	0x89, 0x1b, 0x1b, 0xf4, 0xdd, 0xb7, 0x7a, 0x65, 0xcc, 0x8b, 0x85, 0x18, 0x07, 0xa4, 0x81, 0x5a,
	0x93, 0xd7, 0x50, 0x4b, 0x69, 0x12, 0xde, 0xcc, 0xed, 0xad, 0xff, 0x60, 0x6f, 0xa1, 0x85, 0x12,
	0x3a, 0xaf, 0xa1, 0xa2, 0x2f, 0xaf, 0x5d, 0x78, 0x30, 0x38, 0x9c, 0x8c, 0x4f, 0x8e, 0x17, 0x97,
	0xfc, 0x61, 0xf3, 0x1e, 0x69, 0x00, 0x68, 0xf5, 0x60, 0x28, 0xaf, 0xf7, 0x85, 0x3c, 0x1c, 0x1d,
	0x35, 0x8d, 0xce, 0xef, 0x06, 0xd4, 0xce, 0x14, 0x95, 0xd4, 0x89, 0xaf, 0xcf, 0x22, 0x85, 0xcd,
	0x59, 0xe4, 0x25, 0x54, 0x35, 0xfb, 0xb8, 0x6e, 0x97, 0x0f, 0x35, 0x2d, 0x96, 0x3d, 0xcd, 0x85,
	0x89, 0x77, 0xe1, 0xe4, 0xf8, 0x56, 0x1b, 0x1a, 0x67, 0x82, 0xf9, 0xef, 0x6e, 0x72, 0xae, 0xae,
	0xcd, 0x28, 0xad, 0xdf, 0x0a, 0x00, 0x0b, 0xd3, 0x8d, 0x11, 0x86, 0x40, 0x29, 0xf2, 0x66, 0x54,
	0x53, 0x0b, 0xd7, 0xe4, 0x08, 0x9a, 0x1c, 0x9d, 0xba, 0x78, 0x74, 0xf8, 0x58, 0x16, 0x31, 0xb1,
	0xce, 0xd6, 0xc4, 0x56, 0x12, 0x70, 0x1a, 0x3c, 0x97, 0xf1, 0x4d, 0xed, 0xad, 0xcc, 0x3b, 0xeb,
	0xcf, 0xf5, 0x3c, 0xb5, 0x7c, 0xe4, 0xe9, 0xfc, 0x52, 0x04, 0x98, 0xc8, 0x37, 0x59, 0x15, 0xf0,
	0xf9, 0x5a, 0xcb, 0xd8, 0x3a, 0x85, 0x05, 0x64, 0xbd, 0x61, 0xd6, 0x4b, 0x6e, 0x6c, 0x96, 0x7c,
	0x1f, 0x2a, 0xf8, 0xec, 0x73, 0xfd, 0x5d, 0x7b, 0x9b, 0x4e, 0x71, 0xe9, 0x68, 0x58, 0xeb, 0xd7,
	0x02, 0x94, 0x51, 0xb3, 0x51, 0xc0, 0xa7, 0xfa, 0xf3, 0x8c, 0xad, 0x03, 0x1a, 0xda, 0x2c, 0xcd,
	0x73, 0xb2, 0x67, 0x53, 0xea, 0x89, 0xe5, 0xe9, 0xd0, 0xd4, 0x9a, 0x81, 0x90, 0xdb, 0x59, 0x12,
	0xcc, 0xb7, 0x4b, 0x6a, 0x5b, 0x6b, 0x06, 0x22, 0x3f, 0xac, 0xf2, 0xd2, 0x61, 0x11, 0x28, 0xf1,
	0x38, 0x15, 0xba, 0x73, 0x71, 0xdd, 0xf9, 0x36, 0xa7, 0xf1, 0x43, 0x20, 0xeb, 0x34, 0xc6, 0x59,
	0x85, 0x40, 0x43, 0xeb, 0x0f, 0x9d, 0xd1, 0x60, 0x82, 0xa3, 0xca, 0x42, 0x77, 0x7e, 0x3a, 0x1c,
	0xa8, 0x59, 0x65, 0xa1, 0x9b, 0xcf, 0x2f, 0xc5, 0x83, 0xcf, 0xbf, 0x79, 0x31, 0x8d, 0x43, 0x2f,
	0x9a, 0xf6, 0x5e, 0xf4, 0x85, 0xe8, 0xf9, 0xf1, 0x6c, 0x1f, 0xa7, 0x78, 0x3f, 0x0e, 0xf7, 0x39,
	0x4d, 0xaf, 0x99, 0x4f, 0xf9, 0xb6, 0xbf, 0x0a, 0x17, 0x15, 0x84, 0x7d, 0xf6, 0x4f, 0x00, 0x00,
	0x00, 0xff, 0xff, 0x97, 0x14, 0x41, 0xd0, 0x57, 0x0c, 0x00, 0x00,
}
