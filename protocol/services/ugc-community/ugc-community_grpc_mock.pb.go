// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/ugc-community/ugc-community.proto

package ugc_community

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockUgcCommunityClient is a mock of UgcCommunityClient interface.
type MockUgcCommunityClient struct {
	ctrl     *gomock.Controller
	recorder *MockUgcCommunityClientMockRecorder
}

// MockUgcCommunityClientMockRecorder is the mock recorder for MockUgcCommunityClient.
type MockUgcCommunityClientMockRecorder struct {
	mock *MockUgcCommunityClient
}

// NewMockUgcCommunityClient creates a new mock instance.
func NewMockUgcCommunityClient(ctrl *gomock.Controller) *MockUgcCommunityClient {
	mock := &MockUgcCommunityClient{ctrl: ctrl}
	mock.recorder = &MockUgcCommunityClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUgcCommunityClient) EXPECT() *MockUgcCommunityClientMockRecorder {
	return m.recorder
}

// AddAttitude mocks base method.
func (m *MockUgcCommunityClient) AddAttitude(ctx context.Context, in *AddAttitudeRequest, opts ...grpc.CallOption) (*AddAttitudeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddAttitude", varargs...)
	ret0, _ := ret[0].(*AddAttitudeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAttitude indicates an expected call of AddAttitude.
func (mr *MockUgcCommunityClientMockRecorder) AddAttitude(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAttitude", reflect.TypeOf((*MockUgcCommunityClient)(nil).AddAttitude), varargs...)
}

// AddPostGuideTask mocks base method.
func (m *MockUgcCommunityClient) AddPostGuideTask(ctx context.Context, in *AddPostGuideTaskRequest, opts ...grpc.CallOption) (*AddPostGuideTaskResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPostGuideTask", varargs...)
	ret0, _ := ret[0].(*AddPostGuideTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPostGuideTask indicates an expected call of AddPostGuideTask.
func (mr *MockUgcCommunityClientMockRecorder) AddPostGuideTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPostGuideTask", reflect.TypeOf((*MockUgcCommunityClient)(nil).AddPostGuideTask), varargs...)
}

// AddUserPost mocks base method.
func (m *MockUgcCommunityClient) AddUserPost(ctx context.Context, in *AddUserPostRequest, opts ...grpc.CallOption) (*AddUserPostResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddUserPost", varargs...)
	ret0, _ := ret[0].(*AddUserPostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserPost indicates an expected call of AddUserPost.
func (mr *MockUgcCommunityClientMockRecorder) AddUserPost(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserPost", reflect.TypeOf((*MockUgcCommunityClient)(nil).AddUserPost), varargs...)
}

// BatchDeletePost mocks base method.
func (m *MockUgcCommunityClient) BatchDeletePost(ctx context.Context, in *BatchDeletePostRequest, opts ...grpc.CallOption) (*BatchDeletePostResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchDeletePost", varargs...)
	ret0, _ := ret[0].(*BatchDeletePostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDeletePost indicates an expected call of BatchDeletePost.
func (mr *MockUgcCommunityClientMockRecorder) BatchDeletePost(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeletePost", reflect.TypeOf((*MockUgcCommunityClient)(nil).BatchDeletePost), varargs...)
}

// BatchDeleteTopic mocks base method.
func (m *MockUgcCommunityClient) BatchDeleteTopic(ctx context.Context, in *BatchDeleteTopicRequest, opts ...grpc.CallOption) (*BatchDeleteTopicResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchDeleteTopic", varargs...)
	ret0, _ := ret[0].(*BatchDeleteTopicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDeleteTopic indicates an expected call of BatchDeleteTopic.
func (mr *MockUgcCommunityClientMockRecorder) BatchDeleteTopic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteTopic", reflect.TypeOf((*MockUgcCommunityClient)(nil).BatchDeleteTopic), varargs...)
}

// BatchGetPostAICommentCnt mocks base method.
func (m *MockUgcCommunityClient) BatchGetPostAICommentCnt(ctx context.Context, in *BatchGetPostAICommentCntRequest, opts ...grpc.CallOption) (*BatchGetPostAICommentCntResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetPostAICommentCnt", varargs...)
	ret0, _ := ret[0].(*BatchGetPostAICommentCntResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPostAICommentCnt indicates an expected call of BatchGetPostAICommentCnt.
func (mr *MockUgcCommunityClientMockRecorder) BatchGetPostAICommentCnt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPostAICommentCnt", reflect.TypeOf((*MockUgcCommunityClient)(nil).BatchGetPostAICommentCnt), varargs...)
}

// BatchGetTopic mocks base method.
func (m *MockUgcCommunityClient) BatchGetTopic(ctx context.Context, in *BatchGetTopicRequest, opts ...grpc.CallOption) (*BatchGetTopicResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetTopic", varargs...)
	ret0, _ := ret[0].(*BatchGetTopicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTopic indicates an expected call of BatchGetTopic.
func (mr *MockUgcCommunityClientMockRecorder) BatchGetTopic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTopic", reflect.TypeOf((*MockUgcCommunityClient)(nil).BatchGetTopic), varargs...)
}

// CommentDelete mocks base method.
func (m *MockUgcCommunityClient) CommentDelete(ctx context.Context, in *CommentDeleteRequest, opts ...grpc.CallOption) (*CommentDeleteResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CommentDelete", varargs...)
	ret0, _ := ret[0].(*CommentDeleteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CommentDelete indicates an expected call of CommentDelete.
func (mr *MockUgcCommunityClientMockRecorder) CommentDelete(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommentDelete", reflect.TypeOf((*MockUgcCommunityClient)(nil).CommentDelete), varargs...)
}

// CommentFetch mocks base method.
func (m *MockUgcCommunityClient) CommentFetch(ctx context.Context, in *CommentFetchRequest, opts ...grpc.CallOption) (*CommentFetchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CommentFetch", varargs...)
	ret0, _ := ret[0].(*CommentFetchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CommentFetch indicates an expected call of CommentFetch.
func (mr *MockUgcCommunityClientMockRecorder) CommentFetch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommentFetch", reflect.TypeOf((*MockUgcCommunityClient)(nil).CommentFetch), varargs...)
}

// CommentSend mocks base method.
func (m *MockUgcCommunityClient) CommentSend(ctx context.Context, in *CommentSendRequest, opts ...grpc.CallOption) (*CommentSendResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CommentSend", varargs...)
	ret0, _ := ret[0].(*CommentSendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CommentSend indicates an expected call of CommentSend.
func (mr *MockUgcCommunityClientMockRecorder) CommentSend(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommentSend", reflect.TypeOf((*MockUgcCommunityClient)(nil).CommentSend), varargs...)
}

// CreatePost mocks base method.
func (m *MockUgcCommunityClient) CreatePost(ctx context.Context, in *CreatePostRequest, opts ...grpc.CallOption) (*CreatePostResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreatePost", varargs...)
	ret0, _ := ret[0].(*CreatePostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePost indicates an expected call of CreatePost.
func (mr *MockUgcCommunityClientMockRecorder) CreatePost(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePost", reflect.TypeOf((*MockUgcCommunityClient)(nil).CreatePost), varargs...)
}

// CreateTopic mocks base method.
func (m *MockUgcCommunityClient) CreateTopic(ctx context.Context, in *CreateTopicRequest, opts ...grpc.CallOption) (*CreateTopicResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateTopic", varargs...)
	ret0, _ := ret[0].(*CreateTopicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTopic indicates an expected call of CreateTopic.
func (mr *MockUgcCommunityClientMockRecorder) CreateTopic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTopic", reflect.TypeOf((*MockUgcCommunityClient)(nil).CreateTopic), varargs...)
}

// DeleteSubjectTab mocks base method.
func (m *MockUgcCommunityClient) DeleteSubjectTab(ctx context.Context, in *DeleteSubjectTabRequest, opts ...grpc.CallOption) (*DeleteSubjectTabResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteSubjectTab", varargs...)
	ret0, _ := ret[0].(*DeleteSubjectTabResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteSubjectTab indicates an expected call of DeleteSubjectTab.
func (mr *MockUgcCommunityClientMockRecorder) DeleteSubjectTab(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSubjectTab", reflect.TypeOf((*MockUgcCommunityClient)(nil).DeleteSubjectTab), varargs...)
}

// DeleteUserPost mocks base method.
func (m *MockUgcCommunityClient) DeleteUserPost(ctx context.Context, in *DeleteUserPostRequest, opts ...grpc.CallOption) (*DeleteUserPostResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteUserPost", varargs...)
	ret0, _ := ret[0].(*DeleteUserPostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteUserPost indicates an expected call of DeleteUserPost.
func (mr *MockUgcCommunityClientMockRecorder) DeleteUserPost(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserPost", reflect.TypeOf((*MockUgcCommunityClient)(nil).DeleteUserPost), varargs...)
}

// FinishPostGuideTask mocks base method.
func (m *MockUgcCommunityClient) FinishPostGuideTask(ctx context.Context, in *FinishPostGuideTaskRequest, opts ...grpc.CallOption) (*FinishPostGuideTaskResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FinishPostGuideTask", varargs...)
	ret0, _ := ret[0].(*FinishPostGuideTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FinishPostGuideTask indicates an expected call of FinishPostGuideTask.
func (mr *MockUgcCommunityClientMockRecorder) FinishPostGuideTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FinishPostGuideTask", reflect.TypeOf((*MockUgcCommunityClient)(nil).FinishPostGuideTask), varargs...)
}

// GetAICommentCntInfo mocks base method.
func (m *MockUgcCommunityClient) GetAICommentCntInfo(ctx context.Context, in *GetAICommentCntInfoRequest, opts ...grpc.CallOption) (*GetAICommentCntInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAICommentCntInfo", varargs...)
	ret0, _ := ret[0].(*GetAICommentCntInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAICommentCntInfo indicates an expected call of GetAICommentCntInfo.
func (mr *MockUgcCommunityClientMockRecorder) GetAICommentCntInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAICommentCntInfo", reflect.TypeOf((*MockUgcCommunityClient)(nil).GetAICommentCntInfo), varargs...)
}

// GetCommentInfoById mocks base method.
func (m *MockUgcCommunityClient) GetCommentInfoById(ctx context.Context, in *GetCommentInfoByIdRequest, opts ...grpc.CallOption) (*GetCommentInfoByIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCommentInfoById", varargs...)
	ret0, _ := ret[0].(*GetCommentInfoByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommentInfoById indicates an expected call of GetCommentInfoById.
func (mr *MockUgcCommunityClientMockRecorder) GetCommentInfoById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommentInfoById", reflect.TypeOf((*MockUgcCommunityClient)(nil).GetCommentInfoById), varargs...)
}

// GetPost mocks base method.
func (m *MockUgcCommunityClient) GetPost(ctx context.Context, in *GetPostRequest, opts ...grpc.CallOption) (*GetPostResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPost", varargs...)
	ret0, _ := ret[0].(*GetPostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPost indicates an expected call of GetPost.
func (mr *MockUgcCommunityClientMockRecorder) GetPost(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPost", reflect.TypeOf((*MockUgcCommunityClient)(nil).GetPost), varargs...)
}

// GetPostGuideTask mocks base method.
func (m *MockUgcCommunityClient) GetPostGuideTask(ctx context.Context, in *GetPostGuideTaskRequest, opts ...grpc.CallOption) (*GetPostGuideTaskResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPostGuideTask", varargs...)
	ret0, _ := ret[0].(*GetPostGuideTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostGuideTask indicates an expected call of GetPostGuideTask.
func (mr *MockUgcCommunityClientMockRecorder) GetPostGuideTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostGuideTask", reflect.TypeOf((*MockUgcCommunityClient)(nil).GetPostGuideTask), varargs...)
}

// GetPostHotComment mocks base method.
func (m *MockUgcCommunityClient) GetPostHotComment(ctx context.Context, in *GetPostHotCommentRequest, opts ...grpc.CallOption) (*GetPostHotCommentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPostHotComment", varargs...)
	ret0, _ := ret[0].(*GetPostHotCommentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostHotComment indicates an expected call of GetPostHotComment.
func (mr *MockUgcCommunityClientMockRecorder) GetPostHotComment(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostHotComment", reflect.TypeOf((*MockUgcCommunityClient)(nil).GetPostHotComment), varargs...)
}

// GetPostList mocks base method.
func (m *MockUgcCommunityClient) GetPostList(ctx context.Context, in *GetPostListRequest, opts ...grpc.CallOption) (*GetPostListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPostList", varargs...)
	ret0, _ := ret[0].(*GetPostListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostList indicates an expected call of GetPostList.
func (mr *MockUgcCommunityClientMockRecorder) GetPostList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostList", reflect.TypeOf((*MockUgcCommunityClient)(nil).GetPostList), varargs...)
}

// GetPostListInAuditTimeRange mocks base method.
func (m *MockUgcCommunityClient) GetPostListInAuditTimeRange(ctx context.Context, in *GetPostListInAuditTimeRangeRequest, opts ...grpc.CallOption) (*GetPostListInAuditTimeRangeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPostListInAuditTimeRange", varargs...)
	ret0, _ := ret[0].(*GetPostListInAuditTimeRangeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostListInAuditTimeRange indicates an expected call of GetPostListInAuditTimeRange.
func (mr *MockUgcCommunityClientMockRecorder) GetPostListInAuditTimeRange(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostListInAuditTimeRange", reflect.TypeOf((*MockUgcCommunityClient)(nil).GetPostListInAuditTimeRange), varargs...)
}

// GetSubjectTabByIds mocks base method.
func (m *MockUgcCommunityClient) GetSubjectTabByIds(ctx context.Context, in *GetSubjectTabByIdsRequest, opts ...grpc.CallOption) (*GetSubjectTabByIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSubjectTabByIds", varargs...)
	ret0, _ := ret[0].(*GetSubjectTabByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubjectTabByIds indicates an expected call of GetSubjectTabByIds.
func (mr *MockUgcCommunityClientMockRecorder) GetSubjectTabByIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubjectTabByIds", reflect.TypeOf((*MockUgcCommunityClient)(nil).GetSubjectTabByIds), varargs...)
}

// GetSubjectTabList mocks base method.
func (m *MockUgcCommunityClient) GetSubjectTabList(ctx context.Context, in *GetSubjectTabListRequest, opts ...grpc.CallOption) (*GetSubjectTabListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSubjectTabList", varargs...)
	ret0, _ := ret[0].(*GetSubjectTabListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubjectTabList indicates an expected call of GetSubjectTabList.
func (mr *MockUgcCommunityClientMockRecorder) GetSubjectTabList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubjectTabList", reflect.TypeOf((*MockUgcCommunityClient)(nil).GetSubjectTabList), varargs...)
}

// GetTopic mocks base method.
func (m *MockUgcCommunityClient) GetTopic(ctx context.Context, in *GetTopicRequest, opts ...grpc.CallOption) (*GetTopicResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTopic", varargs...)
	ret0, _ := ret[0].(*GetTopicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTopic indicates an expected call of GetTopic.
func (mr *MockUgcCommunityClientMockRecorder) GetTopic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopic", reflect.TypeOf((*MockUgcCommunityClient)(nil).GetTopic), varargs...)
}

// GetTopicList mocks base method.
func (m *MockUgcCommunityClient) GetTopicList(ctx context.Context, in *GetTopicListRequest, opts ...grpc.CallOption) (*GetTopicListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTopicList", varargs...)
	ret0, _ := ret[0].(*GetTopicListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTopicList indicates an expected call of GetTopicList.
func (mr *MockUgcCommunityClientMockRecorder) GetTopicList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopicList", reflect.TypeOf((*MockUgcCommunityClient)(nil).GetTopicList), varargs...)
}

// GetUserPostList mocks base method.
func (m *MockUgcCommunityClient) GetUserPostList(ctx context.Context, in *GetUserPostListRequest, opts ...grpc.CallOption) (*GetUserPostListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPostList", varargs...)
	ret0, _ := ret[0].(*GetUserPostListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPostList indicates an expected call of GetUserPostList.
func (mr *MockUgcCommunityClientMockRecorder) GetUserPostList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPostList", reflect.TypeOf((*MockUgcCommunityClient)(nil).GetUserPostList), varargs...)
}

// HadAttitude mocks base method.
func (m *MockUgcCommunityClient) HadAttitude(ctx context.Context, in *HadAttitudeRequest, opts ...grpc.CallOption) (*HadAttitudeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HadAttitude", varargs...)
	ret0, _ := ret[0].(*HadAttitudeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HadAttitude indicates an expected call of HadAttitude.
func (mr *MockUgcCommunityClientMockRecorder) HadAttitude(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HadAttitude", reflect.TypeOf((*MockUgcCommunityClient)(nil).HadAttitude), varargs...)
}

// OfficialHandlePost mocks base method.
func (m *MockUgcCommunityClient) OfficialHandlePost(ctx context.Context, in *OfficialHandlePostRequest, opts ...grpc.CallOption) (*OfficialHandlePostResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OfficialHandlePost", varargs...)
	ret0, _ := ret[0].(*OfficialHandlePostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficialHandlePost indicates an expected call of OfficialHandlePost.
func (mr *MockUgcCommunityClientMockRecorder) OfficialHandlePost(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialHandlePost", reflect.TypeOf((*MockUgcCommunityClient)(nil).OfficialHandlePost), varargs...)
}

// ResortSubjectTab mocks base method.
func (m *MockUgcCommunityClient) ResortSubjectTab(ctx context.Context, in *ResortSubjectTabRequest, opts ...grpc.CallOption) (*ResortSubjectTabResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResortSubjectTab", varargs...)
	ret0, _ := ret[0].(*ResortSubjectTabResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResortSubjectTab indicates an expected call of ResortSubjectTab.
func (mr *MockUgcCommunityClientMockRecorder) ResortSubjectTab(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResortSubjectTab", reflect.TypeOf((*MockUgcCommunityClient)(nil).ResortSubjectTab), varargs...)
}

// SearchPost mocks base method.
func (m *MockUgcCommunityClient) SearchPost(ctx context.Context, in *SearchPostRequest, opts ...grpc.CallOption) (*SearchPostResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchPost", varargs...)
	ret0, _ := ret[0].(*SearchPostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchPost indicates an expected call of SearchPost.
func (mr *MockUgcCommunityClientMockRecorder) SearchPost(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchPost", reflect.TypeOf((*MockUgcCommunityClient)(nil).SearchPost), varargs...)
}

// UpdateAigcBizPost mocks base method.
func (m *MockUgcCommunityClient) UpdateAigcBizPost(ctx context.Context, in *UpdateAigcBizPostRequest, opts ...grpc.CallOption) (*UpdateAigcBizPostResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAigcBizPost", varargs...)
	ret0, _ := ret[0].(*UpdateAigcBizPostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAigcBizPost indicates an expected call of UpdateAigcBizPost.
func (mr *MockUgcCommunityClientMockRecorder) UpdateAigcBizPost(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAigcBizPost", reflect.TypeOf((*MockUgcCommunityClient)(nil).UpdateAigcBizPost), varargs...)
}

// UpdatePostAuditResult mocks base method.
func (m *MockUgcCommunityClient) UpdatePostAuditResult(ctx context.Context, in *UpdatePostAuditResultRequest, opts ...grpc.CallOption) (*UpdatePostAuditResultResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePostAuditResult", varargs...)
	ret0, _ := ret[0].(*UpdatePostAuditResultResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePostAuditResult indicates an expected call of UpdatePostAuditResult.
func (mr *MockUgcCommunityClientMockRecorder) UpdatePostAuditResult(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePostAuditResult", reflect.TypeOf((*MockUgcCommunityClient)(nil).UpdatePostAuditResult), varargs...)
}

// UpdateTopic mocks base method.
func (m *MockUgcCommunityClient) UpdateTopic(ctx context.Context, in *UpdateTopicRequest, opts ...grpc.CallOption) (*UpdateTopicResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTopic", varargs...)
	ret0, _ := ret[0].(*UpdateTopicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTopic indicates an expected call of UpdateTopic.
func (mr *MockUgcCommunityClientMockRecorder) UpdateTopic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTopic", reflect.TypeOf((*MockUgcCommunityClient)(nil).UpdateTopic), varargs...)
}

// UpsertSubjectTab mocks base method.
func (m *MockUgcCommunityClient) UpsertSubjectTab(ctx context.Context, in *UpsertSubjectTabRequest, opts ...grpc.CallOption) (*UpsertSubjectTabResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertSubjectTab", varargs...)
	ret0, _ := ret[0].(*UpsertSubjectTabResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertSubjectTab indicates an expected call of UpsertSubjectTab.
func (mr *MockUgcCommunityClientMockRecorder) UpsertSubjectTab(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertSubjectTab", reflect.TypeOf((*MockUgcCommunityClient)(nil).UpsertSubjectTab), varargs...)
}

// MockUgcCommunityServer is a mock of UgcCommunityServer interface.
type MockUgcCommunityServer struct {
	ctrl     *gomock.Controller
	recorder *MockUgcCommunityServerMockRecorder
}

// MockUgcCommunityServerMockRecorder is the mock recorder for MockUgcCommunityServer.
type MockUgcCommunityServerMockRecorder struct {
	mock *MockUgcCommunityServer
}

// NewMockUgcCommunityServer creates a new mock instance.
func NewMockUgcCommunityServer(ctrl *gomock.Controller) *MockUgcCommunityServer {
	mock := &MockUgcCommunityServer{ctrl: ctrl}
	mock.recorder = &MockUgcCommunityServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUgcCommunityServer) EXPECT() *MockUgcCommunityServerMockRecorder {
	return m.recorder
}

// AddAttitude mocks base method.
func (m *MockUgcCommunityServer) AddAttitude(ctx context.Context, in *AddAttitudeRequest) (*AddAttitudeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAttitude", ctx, in)
	ret0, _ := ret[0].(*AddAttitudeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAttitude indicates an expected call of AddAttitude.
func (mr *MockUgcCommunityServerMockRecorder) AddAttitude(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAttitude", reflect.TypeOf((*MockUgcCommunityServer)(nil).AddAttitude), ctx, in)
}

// AddPostGuideTask mocks base method.
func (m *MockUgcCommunityServer) AddPostGuideTask(ctx context.Context, in *AddPostGuideTaskRequest) (*AddPostGuideTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPostGuideTask", ctx, in)
	ret0, _ := ret[0].(*AddPostGuideTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPostGuideTask indicates an expected call of AddPostGuideTask.
func (mr *MockUgcCommunityServerMockRecorder) AddPostGuideTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPostGuideTask", reflect.TypeOf((*MockUgcCommunityServer)(nil).AddPostGuideTask), ctx, in)
}

// AddUserPost mocks base method.
func (m *MockUgcCommunityServer) AddUserPost(ctx context.Context, in *AddUserPostRequest) (*AddUserPostResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserPost", ctx, in)
	ret0, _ := ret[0].(*AddUserPostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserPost indicates an expected call of AddUserPost.
func (mr *MockUgcCommunityServerMockRecorder) AddUserPost(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserPost", reflect.TypeOf((*MockUgcCommunityServer)(nil).AddUserPost), ctx, in)
}

// BatchDeletePost mocks base method.
func (m *MockUgcCommunityServer) BatchDeletePost(ctx context.Context, in *BatchDeletePostRequest) (*BatchDeletePostResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeletePost", ctx, in)
	ret0, _ := ret[0].(*BatchDeletePostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDeletePost indicates an expected call of BatchDeletePost.
func (mr *MockUgcCommunityServerMockRecorder) BatchDeletePost(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeletePost", reflect.TypeOf((*MockUgcCommunityServer)(nil).BatchDeletePost), ctx, in)
}

// BatchDeleteTopic mocks base method.
func (m *MockUgcCommunityServer) BatchDeleteTopic(ctx context.Context, in *BatchDeleteTopicRequest) (*BatchDeleteTopicResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteTopic", ctx, in)
	ret0, _ := ret[0].(*BatchDeleteTopicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDeleteTopic indicates an expected call of BatchDeleteTopic.
func (mr *MockUgcCommunityServerMockRecorder) BatchDeleteTopic(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteTopic", reflect.TypeOf((*MockUgcCommunityServer)(nil).BatchDeleteTopic), ctx, in)
}

// BatchGetPostAICommentCnt mocks base method.
func (m *MockUgcCommunityServer) BatchGetPostAICommentCnt(ctx context.Context, in *BatchGetPostAICommentCntRequest) (*BatchGetPostAICommentCntResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPostAICommentCnt", ctx, in)
	ret0, _ := ret[0].(*BatchGetPostAICommentCntResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPostAICommentCnt indicates an expected call of BatchGetPostAICommentCnt.
func (mr *MockUgcCommunityServerMockRecorder) BatchGetPostAICommentCnt(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPostAICommentCnt", reflect.TypeOf((*MockUgcCommunityServer)(nil).BatchGetPostAICommentCnt), ctx, in)
}

// BatchGetTopic mocks base method.
func (m *MockUgcCommunityServer) BatchGetTopic(ctx context.Context, in *BatchGetTopicRequest) (*BatchGetTopicResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTopic", ctx, in)
	ret0, _ := ret[0].(*BatchGetTopicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTopic indicates an expected call of BatchGetTopic.
func (mr *MockUgcCommunityServerMockRecorder) BatchGetTopic(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTopic", reflect.TypeOf((*MockUgcCommunityServer)(nil).BatchGetTopic), ctx, in)
}

// CommentDelete mocks base method.
func (m *MockUgcCommunityServer) CommentDelete(ctx context.Context, in *CommentDeleteRequest) (*CommentDeleteResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CommentDelete", ctx, in)
	ret0, _ := ret[0].(*CommentDeleteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CommentDelete indicates an expected call of CommentDelete.
func (mr *MockUgcCommunityServerMockRecorder) CommentDelete(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommentDelete", reflect.TypeOf((*MockUgcCommunityServer)(nil).CommentDelete), ctx, in)
}

// CommentFetch mocks base method.
func (m *MockUgcCommunityServer) CommentFetch(ctx context.Context, in *CommentFetchRequest) (*CommentFetchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CommentFetch", ctx, in)
	ret0, _ := ret[0].(*CommentFetchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CommentFetch indicates an expected call of CommentFetch.
func (mr *MockUgcCommunityServerMockRecorder) CommentFetch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommentFetch", reflect.TypeOf((*MockUgcCommunityServer)(nil).CommentFetch), ctx, in)
}

// CommentSend mocks base method.
func (m *MockUgcCommunityServer) CommentSend(ctx context.Context, in *CommentSendRequest) (*CommentSendResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CommentSend", ctx, in)
	ret0, _ := ret[0].(*CommentSendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CommentSend indicates an expected call of CommentSend.
func (mr *MockUgcCommunityServerMockRecorder) CommentSend(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommentSend", reflect.TypeOf((*MockUgcCommunityServer)(nil).CommentSend), ctx, in)
}

// CreatePost mocks base method.
func (m *MockUgcCommunityServer) CreatePost(ctx context.Context, in *CreatePostRequest) (*CreatePostResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePost", ctx, in)
	ret0, _ := ret[0].(*CreatePostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePost indicates an expected call of CreatePost.
func (mr *MockUgcCommunityServerMockRecorder) CreatePost(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePost", reflect.TypeOf((*MockUgcCommunityServer)(nil).CreatePost), ctx, in)
}

// CreateTopic mocks base method.
func (m *MockUgcCommunityServer) CreateTopic(ctx context.Context, in *CreateTopicRequest) (*CreateTopicResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTopic", ctx, in)
	ret0, _ := ret[0].(*CreateTopicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTopic indicates an expected call of CreateTopic.
func (mr *MockUgcCommunityServerMockRecorder) CreateTopic(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTopic", reflect.TypeOf((*MockUgcCommunityServer)(nil).CreateTopic), ctx, in)
}

// DeleteSubjectTab mocks base method.
func (m *MockUgcCommunityServer) DeleteSubjectTab(ctx context.Context, in *DeleteSubjectTabRequest) (*DeleteSubjectTabResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSubjectTab", ctx, in)
	ret0, _ := ret[0].(*DeleteSubjectTabResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteSubjectTab indicates an expected call of DeleteSubjectTab.
func (mr *MockUgcCommunityServerMockRecorder) DeleteSubjectTab(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSubjectTab", reflect.TypeOf((*MockUgcCommunityServer)(nil).DeleteSubjectTab), ctx, in)
}

// DeleteUserPost mocks base method.
func (m *MockUgcCommunityServer) DeleteUserPost(ctx context.Context, in *DeleteUserPostRequest) (*DeleteUserPostResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUserPost", ctx, in)
	ret0, _ := ret[0].(*DeleteUserPostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteUserPost indicates an expected call of DeleteUserPost.
func (mr *MockUgcCommunityServerMockRecorder) DeleteUserPost(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserPost", reflect.TypeOf((*MockUgcCommunityServer)(nil).DeleteUserPost), ctx, in)
}

// FinishPostGuideTask mocks base method.
func (m *MockUgcCommunityServer) FinishPostGuideTask(ctx context.Context, in *FinishPostGuideTaskRequest) (*FinishPostGuideTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FinishPostGuideTask", ctx, in)
	ret0, _ := ret[0].(*FinishPostGuideTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FinishPostGuideTask indicates an expected call of FinishPostGuideTask.
func (mr *MockUgcCommunityServerMockRecorder) FinishPostGuideTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FinishPostGuideTask", reflect.TypeOf((*MockUgcCommunityServer)(nil).FinishPostGuideTask), ctx, in)
}

// GetAICommentCntInfo mocks base method.
func (m *MockUgcCommunityServer) GetAICommentCntInfo(ctx context.Context, in *GetAICommentCntInfoRequest) (*GetAICommentCntInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAICommentCntInfo", ctx, in)
	ret0, _ := ret[0].(*GetAICommentCntInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAICommentCntInfo indicates an expected call of GetAICommentCntInfo.
func (mr *MockUgcCommunityServerMockRecorder) GetAICommentCntInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAICommentCntInfo", reflect.TypeOf((*MockUgcCommunityServer)(nil).GetAICommentCntInfo), ctx, in)
}

// GetCommentInfoById mocks base method.
func (m *MockUgcCommunityServer) GetCommentInfoById(ctx context.Context, in *GetCommentInfoByIdRequest) (*GetCommentInfoByIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommentInfoById", ctx, in)
	ret0, _ := ret[0].(*GetCommentInfoByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommentInfoById indicates an expected call of GetCommentInfoById.
func (mr *MockUgcCommunityServerMockRecorder) GetCommentInfoById(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommentInfoById", reflect.TypeOf((*MockUgcCommunityServer)(nil).GetCommentInfoById), ctx, in)
}

// GetPost mocks base method.
func (m *MockUgcCommunityServer) GetPost(ctx context.Context, in *GetPostRequest) (*GetPostResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPost", ctx, in)
	ret0, _ := ret[0].(*GetPostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPost indicates an expected call of GetPost.
func (mr *MockUgcCommunityServerMockRecorder) GetPost(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPost", reflect.TypeOf((*MockUgcCommunityServer)(nil).GetPost), ctx, in)
}

// GetPostGuideTask mocks base method.
func (m *MockUgcCommunityServer) GetPostGuideTask(ctx context.Context, in *GetPostGuideTaskRequest) (*GetPostGuideTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPostGuideTask", ctx, in)
	ret0, _ := ret[0].(*GetPostGuideTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostGuideTask indicates an expected call of GetPostGuideTask.
func (mr *MockUgcCommunityServerMockRecorder) GetPostGuideTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostGuideTask", reflect.TypeOf((*MockUgcCommunityServer)(nil).GetPostGuideTask), ctx, in)
}

// GetPostHotComment mocks base method.
func (m *MockUgcCommunityServer) GetPostHotComment(ctx context.Context, in *GetPostHotCommentRequest) (*GetPostHotCommentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPostHotComment", ctx, in)
	ret0, _ := ret[0].(*GetPostHotCommentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostHotComment indicates an expected call of GetPostHotComment.
func (mr *MockUgcCommunityServerMockRecorder) GetPostHotComment(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostHotComment", reflect.TypeOf((*MockUgcCommunityServer)(nil).GetPostHotComment), ctx, in)
}

// GetPostList mocks base method.
func (m *MockUgcCommunityServer) GetPostList(ctx context.Context, in *GetPostListRequest) (*GetPostListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPostList", ctx, in)
	ret0, _ := ret[0].(*GetPostListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostList indicates an expected call of GetPostList.
func (mr *MockUgcCommunityServerMockRecorder) GetPostList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostList", reflect.TypeOf((*MockUgcCommunityServer)(nil).GetPostList), ctx, in)
}

// GetPostListInAuditTimeRange mocks base method.
func (m *MockUgcCommunityServer) GetPostListInAuditTimeRange(ctx context.Context, in *GetPostListInAuditTimeRangeRequest) (*GetPostListInAuditTimeRangeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPostListInAuditTimeRange", ctx, in)
	ret0, _ := ret[0].(*GetPostListInAuditTimeRangeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostListInAuditTimeRange indicates an expected call of GetPostListInAuditTimeRange.
func (mr *MockUgcCommunityServerMockRecorder) GetPostListInAuditTimeRange(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostListInAuditTimeRange", reflect.TypeOf((*MockUgcCommunityServer)(nil).GetPostListInAuditTimeRange), ctx, in)
}

// GetSubjectTabByIds mocks base method.
func (m *MockUgcCommunityServer) GetSubjectTabByIds(ctx context.Context, in *GetSubjectTabByIdsRequest) (*GetSubjectTabByIdsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSubjectTabByIds", ctx, in)
	ret0, _ := ret[0].(*GetSubjectTabByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubjectTabByIds indicates an expected call of GetSubjectTabByIds.
func (mr *MockUgcCommunityServerMockRecorder) GetSubjectTabByIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubjectTabByIds", reflect.TypeOf((*MockUgcCommunityServer)(nil).GetSubjectTabByIds), ctx, in)
}

// GetSubjectTabList mocks base method.
func (m *MockUgcCommunityServer) GetSubjectTabList(ctx context.Context, in *GetSubjectTabListRequest) (*GetSubjectTabListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSubjectTabList", ctx, in)
	ret0, _ := ret[0].(*GetSubjectTabListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubjectTabList indicates an expected call of GetSubjectTabList.
func (mr *MockUgcCommunityServerMockRecorder) GetSubjectTabList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubjectTabList", reflect.TypeOf((*MockUgcCommunityServer)(nil).GetSubjectTabList), ctx, in)
}

// GetTopic mocks base method.
func (m *MockUgcCommunityServer) GetTopic(ctx context.Context, in *GetTopicRequest) (*GetTopicResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTopic", ctx, in)
	ret0, _ := ret[0].(*GetTopicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTopic indicates an expected call of GetTopic.
func (mr *MockUgcCommunityServerMockRecorder) GetTopic(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopic", reflect.TypeOf((*MockUgcCommunityServer)(nil).GetTopic), ctx, in)
}

// GetTopicList mocks base method.
func (m *MockUgcCommunityServer) GetTopicList(ctx context.Context, in *GetTopicListRequest) (*GetTopicListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTopicList", ctx, in)
	ret0, _ := ret[0].(*GetTopicListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTopicList indicates an expected call of GetTopicList.
func (mr *MockUgcCommunityServerMockRecorder) GetTopicList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopicList", reflect.TypeOf((*MockUgcCommunityServer)(nil).GetTopicList), ctx, in)
}

// GetUserPostList mocks base method.
func (m *MockUgcCommunityServer) GetUserPostList(ctx context.Context, in *GetUserPostListRequest) (*GetUserPostListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPostList", ctx, in)
	ret0, _ := ret[0].(*GetUserPostListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPostList indicates an expected call of GetUserPostList.
func (mr *MockUgcCommunityServerMockRecorder) GetUserPostList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPostList", reflect.TypeOf((*MockUgcCommunityServer)(nil).GetUserPostList), ctx, in)
}

// HadAttitude mocks base method.
func (m *MockUgcCommunityServer) HadAttitude(ctx context.Context, in *HadAttitudeRequest) (*HadAttitudeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HadAttitude", ctx, in)
	ret0, _ := ret[0].(*HadAttitudeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HadAttitude indicates an expected call of HadAttitude.
func (mr *MockUgcCommunityServerMockRecorder) HadAttitude(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HadAttitude", reflect.TypeOf((*MockUgcCommunityServer)(nil).HadAttitude), ctx, in)
}

// OfficialHandlePost mocks base method.
func (m *MockUgcCommunityServer) OfficialHandlePost(ctx context.Context, in *OfficialHandlePostRequest) (*OfficialHandlePostResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfficialHandlePost", ctx, in)
	ret0, _ := ret[0].(*OfficialHandlePostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficialHandlePost indicates an expected call of OfficialHandlePost.
func (mr *MockUgcCommunityServerMockRecorder) OfficialHandlePost(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialHandlePost", reflect.TypeOf((*MockUgcCommunityServer)(nil).OfficialHandlePost), ctx, in)
}

// ResortSubjectTab mocks base method.
func (m *MockUgcCommunityServer) ResortSubjectTab(ctx context.Context, in *ResortSubjectTabRequest) (*ResortSubjectTabResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResortSubjectTab", ctx, in)
	ret0, _ := ret[0].(*ResortSubjectTabResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResortSubjectTab indicates an expected call of ResortSubjectTab.
func (mr *MockUgcCommunityServerMockRecorder) ResortSubjectTab(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResortSubjectTab", reflect.TypeOf((*MockUgcCommunityServer)(nil).ResortSubjectTab), ctx, in)
}

// SearchPost mocks base method.
func (m *MockUgcCommunityServer) SearchPost(ctx context.Context, in *SearchPostRequest) (*SearchPostResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchPost", ctx, in)
	ret0, _ := ret[0].(*SearchPostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchPost indicates an expected call of SearchPost.
func (mr *MockUgcCommunityServerMockRecorder) SearchPost(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchPost", reflect.TypeOf((*MockUgcCommunityServer)(nil).SearchPost), ctx, in)
}

// UpdateAigcBizPost mocks base method.
func (m *MockUgcCommunityServer) UpdateAigcBizPost(ctx context.Context, in *UpdateAigcBizPostRequest) (*UpdateAigcBizPostResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAigcBizPost", ctx, in)
	ret0, _ := ret[0].(*UpdateAigcBizPostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAigcBizPost indicates an expected call of UpdateAigcBizPost.
func (mr *MockUgcCommunityServerMockRecorder) UpdateAigcBizPost(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAigcBizPost", reflect.TypeOf((*MockUgcCommunityServer)(nil).UpdateAigcBizPost), ctx, in)
}

// UpdatePostAuditResult mocks base method.
func (m *MockUgcCommunityServer) UpdatePostAuditResult(ctx context.Context, in *UpdatePostAuditResultRequest) (*UpdatePostAuditResultResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePostAuditResult", ctx, in)
	ret0, _ := ret[0].(*UpdatePostAuditResultResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePostAuditResult indicates an expected call of UpdatePostAuditResult.
func (mr *MockUgcCommunityServerMockRecorder) UpdatePostAuditResult(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePostAuditResult", reflect.TypeOf((*MockUgcCommunityServer)(nil).UpdatePostAuditResult), ctx, in)
}

// UpdateTopic mocks base method.
func (m *MockUgcCommunityServer) UpdateTopic(ctx context.Context, in *UpdateTopicRequest) (*UpdateTopicResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTopic", ctx, in)
	ret0, _ := ret[0].(*UpdateTopicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTopic indicates an expected call of UpdateTopic.
func (mr *MockUgcCommunityServerMockRecorder) UpdateTopic(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTopic", reflect.TypeOf((*MockUgcCommunityServer)(nil).UpdateTopic), ctx, in)
}

// UpsertSubjectTab mocks base method.
func (m *MockUgcCommunityServer) UpsertSubjectTab(ctx context.Context, in *UpsertSubjectTabRequest) (*UpsertSubjectTabResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertSubjectTab", ctx, in)
	ret0, _ := ret[0].(*UpsertSubjectTabResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertSubjectTab indicates an expected call of UpsertSubjectTab.
func (mr *MockUgcCommunityServerMockRecorder) UpsertSubjectTab(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertSubjectTab", reflect.TypeOf((*MockUgcCommunityServer)(nil).UpsertSubjectTab), ctx, in)
}
