// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-account/aigc-account.proto

package aigc_account

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAigcAccountClient is a mock of AigcAccountClient interface.
type MockAigcAccountClient struct {
	ctrl     *gomock.Controller
	recorder *MockAigcAccountClientMockRecorder
}

// MockAigcAccountClientMockRecorder is the mock recorder for MockAigcAccountClient.
type MockAigcAccountClientMockRecorder struct {
	mock *MockAigcAccountClient
}

// NewMockAigcAccountClient creates a new mock instance.
func NewMockAigcAccountClient(ctrl *gomock.Controller) *MockAigcAccountClient {
	mock := &MockAigcAccountClient{ctrl: ctrl}
	mock.recorder = &MockAigcAccountClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcAccountClient) EXPECT() *MockAigcAccountClientMockRecorder {
	return m.recorder
}

// AddAiCommentPost mocks base method.
func (m *MockAigcAccountClient) AddAiCommentPost(ctx context.Context, in *AddAiCommentPostRequest, opts ...grpc.CallOption) (*AddAiCommentPostResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddAiCommentPost", varargs...)
	ret0, _ := ret[0].(*AddAiCommentPostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAiCommentPost indicates an expected call of AddAiCommentPost.
func (mr *MockAigcAccountClientMockRecorder) AddAiCommentPost(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAiCommentPost", reflect.TypeOf((*MockAigcAccountClient)(nil).AddAiCommentPost), varargs...)
}

// AddAiLikePost mocks base method.
func (m *MockAigcAccountClient) AddAiLikePost(ctx context.Context, in *AddAiLikePostRequest, opts ...grpc.CallOption) (*AddAiLikePostResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddAiLikePost", varargs...)
	ret0, _ := ret[0].(*AddAiLikePostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAiLikePost indicates an expected call of AddAiLikePost.
func (mr *MockAigcAccountClientMockRecorder) AddAiLikePost(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAiLikePost", reflect.TypeOf((*MockAigcAccountClient)(nil).AddAiLikePost), varargs...)
}

// AddAiPost mocks base method.
func (m *MockAigcAccountClient) AddAiPost(ctx context.Context, in *AddAiPostRequest, opts ...grpc.CallOption) (*AddAiPostResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddAiPost", varargs...)
	ret0, _ := ret[0].(*AddAiPostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAiPost indicates an expected call of AddAiPost.
func (mr *MockAigcAccountClientMockRecorder) AddAiPost(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAiPost", reflect.TypeOf((*MockAigcAccountClient)(nil).AddAiPost), varargs...)
}

// AddPostAiCommentRecord mocks base method.
func (m *MockAigcAccountClient) AddPostAiCommentRecord(ctx context.Context, in *AddPostAiCommentRecordRequest, opts ...grpc.CallOption) (*AddPostAiCommentRecordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPostAiCommentRecord", varargs...)
	ret0, _ := ret[0].(*AddPostAiCommentRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPostAiCommentRecord indicates an expected call of AddPostAiCommentRecord.
func (mr *MockAigcAccountClientMockRecorder) AddPostAiCommentRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPostAiCommentRecord", reflect.TypeOf((*MockAigcAccountClient)(nil).AddPostAiCommentRecord), varargs...)
}

// AddRunningRoom mocks base method.
func (m *MockAigcAccountClient) AddRunningRoom(ctx context.Context, in *AddRunningRoomRequest, opts ...grpc.CallOption) (*AddRunningRoomResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddRunningRoom", varargs...)
	ret0, _ := ret[0].(*AddRunningRoomResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddRunningRoom indicates an expected call of AddRunningRoom.
func (mr *MockAigcAccountClientMockRecorder) AddRunningRoom(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRunningRoom", reflect.TypeOf((*MockAigcAccountClient)(nil).AddRunningRoom), varargs...)
}

// AddUserAiChatRecord mocks base method.
func (m *MockAigcAccountClient) AddUserAiChatRecord(ctx context.Context, in *AddUserAiChatRecordRequest, opts ...grpc.CallOption) (*AddUserAiChatRecordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddUserAiChatRecord", varargs...)
	ret0, _ := ret[0].(*AddUserAiChatRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserAiChatRecord indicates an expected call of AddUserAiChatRecord.
func (mr *MockAigcAccountClientMockRecorder) AddUserAiChatRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserAiChatRecord", reflect.TypeOf((*MockAigcAccountClient)(nil).AddUserAiChatRecord), varargs...)
}

// AddUserAiInteractionRecord mocks base method.
func (m *MockAigcAccountClient) AddUserAiInteractionRecord(ctx context.Context, in *AddUserAiInteractionRecordRequest, opts ...grpc.CallOption) (*AddUserAiInteractionRecordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddUserAiInteractionRecord", varargs...)
	ret0, _ := ret[0].(*AddUserAiInteractionRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserAiInteractionRecord indicates an expected call of AddUserAiInteractionRecord.
func (mr *MockAigcAccountClientMockRecorder) AddUserAiInteractionRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserAiInteractionRecord", reflect.TypeOf((*MockAigcAccountClient)(nil).AddUserAiInteractionRecord), varargs...)
}

// BatGetRunningRoom mocks base method.
func (m *MockAigcAccountClient) BatGetRunningRoom(ctx context.Context, in *BatGetRunningRoomRequest, opts ...grpc.CallOption) (*BatGetRunningRoomResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetRunningRoom", varargs...)
	ret0, _ := ret[0].(*BatGetRunningRoomResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetRunningRoom indicates an expected call of BatGetRunningRoom.
func (mr *MockAigcAccountClientMockRecorder) BatGetRunningRoom(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetRunningRoom", reflect.TypeOf((*MockAigcAccountClient)(nil).BatGetRunningRoom), varargs...)
}

// BatRemoveRunningRoom mocks base method.
func (m *MockAigcAccountClient) BatRemoveRunningRoom(ctx context.Context, in *BatRemoveRunningRoomRequest, opts ...grpc.CallOption) (*BatRemoveRunningRoomResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatRemoveRunningRoom", varargs...)
	ret0, _ := ret[0].(*BatRemoveRunningRoomResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatRemoveRunningRoom indicates an expected call of BatRemoveRunningRoom.
func (mr *MockAigcAccountClientMockRecorder) BatRemoveRunningRoom(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatRemoveRunningRoom", reflect.TypeOf((*MockAigcAccountClient)(nil).BatRemoveRunningRoom), varargs...)
}

// BatUpdateHeartbeat mocks base method.
func (m *MockAigcAccountClient) BatUpdateHeartbeat(ctx context.Context, in *BatUpdateHeartbeatRequest, opts ...grpc.CallOption) (*BatUpdateHeartbeatResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatUpdateHeartbeat", varargs...)
	ret0, _ := ret[0].(*BatUpdateHeartbeatResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatUpdateHeartbeat indicates an expected call of BatUpdateHeartbeat.
func (mr *MockAigcAccountClientMockRecorder) BatUpdateHeartbeat(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatUpdateHeartbeat", reflect.TypeOf((*MockAigcAccountClient)(nil).BatUpdateHeartbeat), varargs...)
}

// BatchGetAIAccount mocks base method.
func (m *MockAigcAccountClient) BatchGetAIAccount(ctx context.Context, in *BatchGetAIAccountRequest, opts ...grpc.CallOption) (*BatchGetAIAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetAIAccount", varargs...)
	ret0, _ := ret[0].(*BatchGetAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAIAccount indicates an expected call of BatchGetAIAccount.
func (mr *MockAigcAccountClientMockRecorder) BatchGetAIAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAIAccount", reflect.TypeOf((*MockAigcAccountClient)(nil).BatchGetAIAccount), varargs...)
}

// BatchUnregisterAIAccount mocks base method.
func (m *MockAigcAccountClient) BatchUnregisterAIAccount(ctx context.Context, in *BatchUnregisterAIAccountRequest, opts ...grpc.CallOption) (*BatchUnregisterAIAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchUnregisterAIAccount", varargs...)
	ret0, _ := ret[0].(*BatchUnregisterAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUnregisterAIAccount indicates an expected call of BatchUnregisterAIAccount.
func (mr *MockAigcAccountClientMockRecorder) BatchUnregisterAIAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUnregisterAIAccount", reflect.TypeOf((*MockAigcAccountClient)(nil).BatchUnregisterAIAccount), varargs...)
}

// CheckReceptionUser mocks base method.
func (m *MockAigcAccountClient) CheckReceptionUser(ctx context.Context, in *CheckReceptionUserRequest, opts ...grpc.CallOption) (*CheckReceptionUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckReceptionUser", varargs...)
	ret0, _ := ret[0].(*CheckReceptionUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckReceptionUser indicates an expected call of CheckReceptionUser.
func (mr *MockAigcAccountClientMockRecorder) CheckReceptionUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckReceptionUser", reflect.TypeOf((*MockAigcAccountClient)(nil).CheckReceptionUser), varargs...)
}

// CreateAIAccount mocks base method.
func (m *MockAigcAccountClient) CreateAIAccount(ctx context.Context, in *CreateAIAccountRequest, opts ...grpc.CallOption) (*CreateAIAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAIAccount", varargs...)
	ret0, _ := ret[0].(*CreateAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAIAccount indicates an expected call of CreateAIAccount.
func (mr *MockAigcAccountClientMockRecorder) CreateAIAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAIAccount", reflect.TypeOf((*MockAigcAccountClient)(nil).CreateAIAccount), varargs...)
}

// GetAIAccount mocks base method.
func (m *MockAigcAccountClient) GetAIAccount(ctx context.Context, in *GetAIAccountRequest, opts ...grpc.CallOption) (*GetAIAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAIAccount", varargs...)
	ret0, _ := ret[0].(*GetAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIAccount indicates an expected call of GetAIAccount.
func (mr *MockAigcAccountClientMockRecorder) GetAIAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIAccount", reflect.TypeOf((*MockAigcAccountClient)(nil).GetAIAccount), varargs...)
}

// GetAIAccountByGender mocks base method.
func (m *MockAigcAccountClient) GetAIAccountByGender(ctx context.Context, in *GetAIAccountByGenderRequest, opts ...grpc.CallOption) (*GetAIAccountByGenderResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAIAccountByGender", varargs...)
	ret0, _ := ret[0].(*GetAIAccountByGenderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIAccountByGender indicates an expected call of GetAIAccountByGender.
func (mr *MockAigcAccountClientMockRecorder) GetAIAccountByGender(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIAccountByGender", reflect.TypeOf((*MockAigcAccountClient)(nil).GetAIAccountByGender), varargs...)
}

// GetAiCommentPostCount mocks base method.
func (m *MockAigcAccountClient) GetAiCommentPostCount(ctx context.Context, in *GetAiCommentPostCountRequest, opts ...grpc.CallOption) (*GetAiCommentPostCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAiCommentPostCount", varargs...)
	ret0, _ := ret[0].(*GetAiCommentPostCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiCommentPostCount indicates an expected call of GetAiCommentPostCount.
func (mr *MockAigcAccountClientMockRecorder) GetAiCommentPostCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiCommentPostCount", reflect.TypeOf((*MockAigcAccountClient)(nil).GetAiCommentPostCount), varargs...)
}

// GetAiLikePostCount mocks base method.
func (m *MockAigcAccountClient) GetAiLikePostCount(ctx context.Context, in *GetAiLikePostCountRequest, opts ...grpc.CallOption) (*GetAiLikePostCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAiLikePostCount", varargs...)
	ret0, _ := ret[0].(*GetAiLikePostCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiLikePostCount indicates an expected call of GetAiLikePostCount.
func (mr *MockAigcAccountClientMockRecorder) GetAiLikePostCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiLikePostCount", reflect.TypeOf((*MockAigcAccountClient)(nil).GetAiLikePostCount), varargs...)
}

// GetAiPost mocks base method.
func (m *MockAigcAccountClient) GetAiPost(ctx context.Context, in *GetAiPostRequest, opts ...grpc.CallOption) (*GetAiPostResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAiPost", varargs...)
	ret0, _ := ret[0].(*GetAiPostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiPost indicates an expected call of GetAiPost.
func (mr *MockAigcAccountClientMockRecorder) GetAiPost(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiPost", reflect.TypeOf((*MockAigcAccountClient)(nil).GetAiPost), varargs...)
}

// GetAiRoomCfg mocks base method.
func (m *MockAigcAccountClient) GetAiRoomCfg(ctx context.Context, in *GetAiRoomCfgRequest, opts ...grpc.CallOption) (*GetAiRoomCfgResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAiRoomCfg", varargs...)
	ret0, _ := ret[0].(*GetAiRoomCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiRoomCfg indicates an expected call of GetAiRoomCfg.
func (mr *MockAigcAccountClientMockRecorder) GetAiRoomCfg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiRoomCfg", reflect.TypeOf((*MockAigcAccountClient)(nil).GetAiRoomCfg), varargs...)
}

// GetChatRound mocks base method.
func (m *MockAigcAccountClient) GetChatRound(ctx context.Context, in *GetChatRoundRequest, opts ...grpc.CallOption) (*GetChatRoundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChatRound", varargs...)
	ret0, _ := ret[0].(*GetChatRoundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChatRound indicates an expected call of GetChatRound.
func (mr *MockAigcAccountClientMockRecorder) GetChatRound(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChatRound", reflect.TypeOf((*MockAigcAccountClient)(nil).GetChatRound), varargs...)
}

// GetLastChatTime mocks base method.
func (m *MockAigcAccountClient) GetLastChatTime(ctx context.Context, in *GetLastChatTimeRequest, opts ...grpc.CallOption) (*GetLastChatTimeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLastChatTime", varargs...)
	ret0, _ := ret[0].(*GetLastChatTimeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastChatTime indicates an expected call of GetLastChatTime.
func (mr *MockAigcAccountClientMockRecorder) GetLastChatTime(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastChatTime", reflect.TypeOf((*MockAigcAccountClient)(nil).GetLastChatTime), varargs...)
}

// GetLastReceptionTime mocks base method.
func (m *MockAigcAccountClient) GetLastReceptionTime(ctx context.Context, in *GetLastReceptionTimeRequest, opts ...grpc.CallOption) (*GetLastReceptionTimeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLastReceptionTime", varargs...)
	ret0, _ := ret[0].(*GetLastReceptionTimeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastReceptionTime indicates an expected call of GetLastReceptionTime.
func (mr *MockAigcAccountClientMockRecorder) GetLastReceptionTime(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastReceptionTime", reflect.TypeOf((*MockAigcAccountClient)(nil).GetLastReceptionTime), varargs...)
}

// GetNeedHeartbeatRoom mocks base method.
func (m *MockAigcAccountClient) GetNeedHeartbeatRoom(ctx context.Context, in *GetNeedHeartbeatRoomRequest, opts ...grpc.CallOption) (*GetNeedHeartbeatRoomResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNeedHeartbeatRoom", varargs...)
	ret0, _ := ret[0].(*GetNeedHeartbeatRoomResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNeedHeartbeatRoom indicates an expected call of GetNeedHeartbeatRoom.
func (mr *MockAigcAccountClientMockRecorder) GetNeedHeartbeatRoom(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNeedHeartbeatRoom", reflect.TypeOf((*MockAigcAccountClient)(nil).GetNeedHeartbeatRoom), varargs...)
}

// GetPageAIAccount mocks base method.
func (m *MockAigcAccountClient) GetPageAIAccount(ctx context.Context, in *GetPageAIAccountRequest, opts ...grpc.CallOption) (*GetPageAIAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPageAIAccount", varargs...)
	ret0, _ := ret[0].(*GetPageAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPageAIAccount indicates an expected call of GetPageAIAccount.
func (mr *MockAigcAccountClientMockRecorder) GetPageAIAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPageAIAccount", reflect.TypeOf((*MockAigcAccountClient)(nil).GetPageAIAccount), varargs...)
}

// GetPostAiCommentCount mocks base method.
func (m *MockAigcAccountClient) GetPostAiCommentCount(ctx context.Context, in *GetPostAiCommentCountRequest, opts ...grpc.CallOption) (*GetPostAiCommentCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPostAiCommentCount", varargs...)
	ret0, _ := ret[0].(*GetPostAiCommentCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostAiCommentCount indicates an expected call of GetPostAiCommentCount.
func (mr *MockAigcAccountClientMockRecorder) GetPostAiCommentCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostAiCommentCount", reflect.TypeOf((*MockAigcAccountClient)(nil).GetPostAiCommentCount), varargs...)
}

// GetPostAiCommentList mocks base method.
func (m *MockAigcAccountClient) GetPostAiCommentList(ctx context.Context, in *GetPostAiCommentListRequest, opts ...grpc.CallOption) (*GetPostAiCommentListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPostAiCommentList", varargs...)
	ret0, _ := ret[0].(*GetPostAiCommentListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostAiCommentList indicates an expected call of GetPostAiCommentList.
func (mr *MockAigcAccountClientMockRecorder) GetPostAiCommentList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostAiCommentList", reflect.TypeOf((*MockAigcAccountClient)(nil).GetPostAiCommentList), varargs...)
}

// GetRoomUserNum mocks base method.
func (m *MockAigcAccountClient) GetRoomUserNum(ctx context.Context, in *GetRoomUserNumRequest, opts ...grpc.CallOption) (*GetRoomUserNumResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRoomUserNum", varargs...)
	ret0, _ := ret[0].(*GetRoomUserNumResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRoomUserNum indicates an expected call of GetRoomUserNum.
func (mr *MockAigcAccountClientMockRecorder) GetRoomUserNum(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoomUserNum", reflect.TypeOf((*MockAigcAccountClient)(nil).GetRoomUserNum), varargs...)
}

// GetRunningAiRooms mocks base method.
func (m *MockAigcAccountClient) GetRunningAiRooms(ctx context.Context, in *GetRunningAiRoomsRequest, opts ...grpc.CallOption) (*GetRunningAiRoomsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRunningAiRooms", varargs...)
	ret0, _ := ret[0].(*GetRunningAiRoomsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRunningAiRooms indicates an expected call of GetRunningAiRooms.
func (mr *MockAigcAccountClientMockRecorder) GetRunningAiRooms(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRunningAiRooms", reflect.TypeOf((*MockAigcAccountClient)(nil).GetRunningAiRooms), varargs...)
}

// GetUserAiChatRecord mocks base method.
func (m *MockAigcAccountClient) GetUserAiChatRecord(ctx context.Context, in *GetUserAiChatRecordRequest, opts ...grpc.CallOption) (*GetUserAiChatRecordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserAiChatRecord", varargs...)
	ret0, _ := ret[0].(*GetUserAiChatRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAiChatRecord indicates an expected call of GetUserAiChatRecord.
func (mr *MockAigcAccountClientMockRecorder) GetUserAiChatRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAiChatRecord", reflect.TypeOf((*MockAigcAccountClient)(nil).GetUserAiChatRecord), varargs...)
}

// GetUserAiInteractionRecord mocks base method.
func (m *MockAigcAccountClient) GetUserAiInteractionRecord(ctx context.Context, in *GetUserAiInteractionRecordRequest, opts ...grpc.CallOption) (*GetUserAiInteractionRecordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserAiInteractionRecord", varargs...)
	ret0, _ := ret[0].(*GetUserAiInteractionRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAiInteractionRecord indicates an expected call of GetUserAiInteractionRecord.
func (mr *MockAigcAccountClientMockRecorder) GetUserAiInteractionRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAiInteractionRecord", reflect.TypeOf((*MockAigcAccountClient)(nil).GetUserAiInteractionRecord), varargs...)
}

// GetWaitingReceptionUser mocks base method.
func (m *MockAigcAccountClient) GetWaitingReceptionUser(ctx context.Context, in *GetWaitingReceptionUserRequest, opts ...grpc.CallOption) (*GetWaitingReceptionUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWaitingReceptionUser", varargs...)
	ret0, _ := ret[0].(*GetWaitingReceptionUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWaitingReceptionUser indicates an expected call of GetWaitingReceptionUser.
func (mr *MockAigcAccountClientMockRecorder) GetWaitingReceptionUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWaitingReceptionUser", reflect.TypeOf((*MockAigcAccountClient)(nil).GetWaitingReceptionUser), varargs...)
}

// IncrChatRound mocks base method.
func (m *MockAigcAccountClient) IncrChatRound(ctx context.Context, in *IncrChatRoundRequest, opts ...grpc.CallOption) (*IncrChatRoundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IncrChatRound", varargs...)
	ret0, _ := ret[0].(*IncrChatRoundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrChatRound indicates an expected call of IncrChatRound.
func (mr *MockAigcAccountClientMockRecorder) IncrChatRound(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrChatRound", reflect.TypeOf((*MockAigcAccountClient)(nil).IncrChatRound), varargs...)
}

// RecordReceptionTime mocks base method.
func (m *MockAigcAccountClient) RecordReceptionTime(ctx context.Context, in *RecordReceptionTimeRequest, opts ...grpc.CallOption) (*RecordReceptionTimeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecordReceptionTime", varargs...)
	ret0, _ := ret[0].(*RecordReceptionTimeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordReceptionTime indicates an expected call of RecordReceptionTime.
func (mr *MockAigcAccountClientMockRecorder) RecordReceptionTime(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordReceptionTime", reflect.TypeOf((*MockAigcAccountClient)(nil).RecordReceptionTime), varargs...)
}

// RecordReceptionUser mocks base method.
func (m *MockAigcAccountClient) RecordReceptionUser(ctx context.Context, in *RecordReceptionUserRequest, opts ...grpc.CallOption) (*RecordReceptionUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecordReceptionUser", varargs...)
	ret0, _ := ret[0].(*RecordReceptionUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordReceptionUser indicates an expected call of RecordReceptionUser.
func (mr *MockAigcAccountClientMockRecorder) RecordReceptionUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordReceptionUser", reflect.TypeOf((*MockAigcAccountClient)(nil).RecordReceptionUser), varargs...)
}

// RecordRoomUserNum mocks base method.
func (m *MockAigcAccountClient) RecordRoomUserNum(ctx context.Context, in *RecordRoomUserNumRequest, opts ...grpc.CallOption) (*RecordRoomUserNumResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecordRoomUserNum", varargs...)
	ret0, _ := ret[0].(*RecordRoomUserNumResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordRoomUserNum indicates an expected call of RecordRoomUserNum.
func (mr *MockAigcAccountClientMockRecorder) RecordRoomUserNum(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordRoomUserNum", reflect.TypeOf((*MockAigcAccountClient)(nil).RecordRoomUserNum), varargs...)
}

// RecordWaitingReceptionUser mocks base method.
func (m *MockAigcAccountClient) RecordWaitingReceptionUser(ctx context.Context, in *RecordWaitingReceptionUserRequest, opts ...grpc.CallOption) (*RecordWaitingReceptionUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecordWaitingReceptionUser", varargs...)
	ret0, _ := ret[0].(*RecordWaitingReceptionUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordWaitingReceptionUser indicates an expected call of RecordWaitingReceptionUser.
func (mr *MockAigcAccountClientMockRecorder) RecordWaitingReceptionUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordWaitingReceptionUser", reflect.TypeOf((*MockAigcAccountClient)(nil).RecordWaitingReceptionUser), varargs...)
}

// RemoveFromHeartbeatPool mocks base method.
func (m *MockAigcAccountClient) RemoveFromHeartbeatPool(ctx context.Context, in *RemoveFromHeartbeatPoolRequest, opts ...grpc.CallOption) (*RemoveFromHeartbeatPoolResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RemoveFromHeartbeatPool", varargs...)
	ret0, _ := ret[0].(*RemoveFromHeartbeatPoolResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveFromHeartbeatPool indicates an expected call of RemoveFromHeartbeatPool.
func (mr *MockAigcAccountClientMockRecorder) RemoveFromHeartbeatPool(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveFromHeartbeatPool", reflect.TypeOf((*MockAigcAccountClient)(nil).RemoveFromHeartbeatPool), varargs...)
}

// UpdateAIAccount mocks base method.
func (m *MockAigcAccountClient) UpdateAIAccount(ctx context.Context, in *UpdateAIAccountRequest, opts ...grpc.CallOption) (*UpdateAIAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAIAccount", varargs...)
	ret0, _ := ret[0].(*UpdateAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAIAccount indicates an expected call of UpdateAIAccount.
func (mr *MockAigcAccountClientMockRecorder) UpdateAIAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIAccount", reflect.TypeOf((*MockAigcAccountClient)(nil).UpdateAIAccount), varargs...)
}

// UpdateAiRoomCfg mocks base method.
func (m *MockAigcAccountClient) UpdateAiRoomCfg(ctx context.Context, in *UpdateAiRoomCfgRequest, opts ...grpc.CallOption) (*UpdateAiRoomCfgResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAiRoomCfg", varargs...)
	ret0, _ := ret[0].(*UpdateAiRoomCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAiRoomCfg indicates an expected call of UpdateAiRoomCfg.
func (mr *MockAigcAccountClientMockRecorder) UpdateAiRoomCfg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAiRoomCfg", reflect.TypeOf((*MockAigcAccountClient)(nil).UpdateAiRoomCfg), varargs...)
}

// UpdateChatTime mocks base method.
func (m *MockAigcAccountClient) UpdateChatTime(ctx context.Context, in *UpdateChatTimeRequest, opts ...grpc.CallOption) (*UpdateChatTimeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateChatTime", varargs...)
	ret0, _ := ret[0].(*UpdateChatTimeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateChatTime indicates an expected call of UpdateChatTime.
func (mr *MockAigcAccountClientMockRecorder) UpdateChatTime(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChatTime", reflect.TypeOf((*MockAigcAccountClient)(nil).UpdateChatTime), varargs...)
}

// MockAigcAccountServer is a mock of AigcAccountServer interface.
type MockAigcAccountServer struct {
	ctrl     *gomock.Controller
	recorder *MockAigcAccountServerMockRecorder
}

// MockAigcAccountServerMockRecorder is the mock recorder for MockAigcAccountServer.
type MockAigcAccountServerMockRecorder struct {
	mock *MockAigcAccountServer
}

// NewMockAigcAccountServer creates a new mock instance.
func NewMockAigcAccountServer(ctrl *gomock.Controller) *MockAigcAccountServer {
	mock := &MockAigcAccountServer{ctrl: ctrl}
	mock.recorder = &MockAigcAccountServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcAccountServer) EXPECT() *MockAigcAccountServerMockRecorder {
	return m.recorder
}

// AddAiCommentPost mocks base method.
func (m *MockAigcAccountServer) AddAiCommentPost(ctx context.Context, in *AddAiCommentPostRequest) (*AddAiCommentPostResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAiCommentPost", ctx, in)
	ret0, _ := ret[0].(*AddAiCommentPostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAiCommentPost indicates an expected call of AddAiCommentPost.
func (mr *MockAigcAccountServerMockRecorder) AddAiCommentPost(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAiCommentPost", reflect.TypeOf((*MockAigcAccountServer)(nil).AddAiCommentPost), ctx, in)
}

// AddAiLikePost mocks base method.
func (m *MockAigcAccountServer) AddAiLikePost(ctx context.Context, in *AddAiLikePostRequest) (*AddAiLikePostResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAiLikePost", ctx, in)
	ret0, _ := ret[0].(*AddAiLikePostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAiLikePost indicates an expected call of AddAiLikePost.
func (mr *MockAigcAccountServerMockRecorder) AddAiLikePost(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAiLikePost", reflect.TypeOf((*MockAigcAccountServer)(nil).AddAiLikePost), ctx, in)
}

// AddAiPost mocks base method.
func (m *MockAigcAccountServer) AddAiPost(ctx context.Context, in *AddAiPostRequest) (*AddAiPostResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAiPost", ctx, in)
	ret0, _ := ret[0].(*AddAiPostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAiPost indicates an expected call of AddAiPost.
func (mr *MockAigcAccountServerMockRecorder) AddAiPost(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAiPost", reflect.TypeOf((*MockAigcAccountServer)(nil).AddAiPost), ctx, in)
}

// AddPostAiCommentRecord mocks base method.
func (m *MockAigcAccountServer) AddPostAiCommentRecord(ctx context.Context, in *AddPostAiCommentRecordRequest) (*AddPostAiCommentRecordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPostAiCommentRecord", ctx, in)
	ret0, _ := ret[0].(*AddPostAiCommentRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPostAiCommentRecord indicates an expected call of AddPostAiCommentRecord.
func (mr *MockAigcAccountServerMockRecorder) AddPostAiCommentRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPostAiCommentRecord", reflect.TypeOf((*MockAigcAccountServer)(nil).AddPostAiCommentRecord), ctx, in)
}

// AddRunningRoom mocks base method.
func (m *MockAigcAccountServer) AddRunningRoom(ctx context.Context, in *AddRunningRoomRequest) (*AddRunningRoomResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRunningRoom", ctx, in)
	ret0, _ := ret[0].(*AddRunningRoomResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddRunningRoom indicates an expected call of AddRunningRoom.
func (mr *MockAigcAccountServerMockRecorder) AddRunningRoom(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRunningRoom", reflect.TypeOf((*MockAigcAccountServer)(nil).AddRunningRoom), ctx, in)
}

// AddUserAiChatRecord mocks base method.
func (m *MockAigcAccountServer) AddUserAiChatRecord(ctx context.Context, in *AddUserAiChatRecordRequest) (*AddUserAiChatRecordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserAiChatRecord", ctx, in)
	ret0, _ := ret[0].(*AddUserAiChatRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserAiChatRecord indicates an expected call of AddUserAiChatRecord.
func (mr *MockAigcAccountServerMockRecorder) AddUserAiChatRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserAiChatRecord", reflect.TypeOf((*MockAigcAccountServer)(nil).AddUserAiChatRecord), ctx, in)
}

// AddUserAiInteractionRecord mocks base method.
func (m *MockAigcAccountServer) AddUserAiInteractionRecord(ctx context.Context, in *AddUserAiInteractionRecordRequest) (*AddUserAiInteractionRecordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserAiInteractionRecord", ctx, in)
	ret0, _ := ret[0].(*AddUserAiInteractionRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserAiInteractionRecord indicates an expected call of AddUserAiInteractionRecord.
func (mr *MockAigcAccountServerMockRecorder) AddUserAiInteractionRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserAiInteractionRecord", reflect.TypeOf((*MockAigcAccountServer)(nil).AddUserAiInteractionRecord), ctx, in)
}

// BatGetRunningRoom mocks base method.
func (m *MockAigcAccountServer) BatGetRunningRoom(ctx context.Context, in *BatGetRunningRoomRequest) (*BatGetRunningRoomResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetRunningRoom", ctx, in)
	ret0, _ := ret[0].(*BatGetRunningRoomResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetRunningRoom indicates an expected call of BatGetRunningRoom.
func (mr *MockAigcAccountServerMockRecorder) BatGetRunningRoom(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetRunningRoom", reflect.TypeOf((*MockAigcAccountServer)(nil).BatGetRunningRoom), ctx, in)
}

// BatRemoveRunningRoom mocks base method.
func (m *MockAigcAccountServer) BatRemoveRunningRoom(ctx context.Context, in *BatRemoveRunningRoomRequest) (*BatRemoveRunningRoomResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatRemoveRunningRoom", ctx, in)
	ret0, _ := ret[0].(*BatRemoveRunningRoomResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatRemoveRunningRoom indicates an expected call of BatRemoveRunningRoom.
func (mr *MockAigcAccountServerMockRecorder) BatRemoveRunningRoom(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatRemoveRunningRoom", reflect.TypeOf((*MockAigcAccountServer)(nil).BatRemoveRunningRoom), ctx, in)
}

// BatUpdateHeartbeat mocks base method.
func (m *MockAigcAccountServer) BatUpdateHeartbeat(ctx context.Context, in *BatUpdateHeartbeatRequest) (*BatUpdateHeartbeatResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatUpdateHeartbeat", ctx, in)
	ret0, _ := ret[0].(*BatUpdateHeartbeatResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatUpdateHeartbeat indicates an expected call of BatUpdateHeartbeat.
func (mr *MockAigcAccountServerMockRecorder) BatUpdateHeartbeat(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatUpdateHeartbeat", reflect.TypeOf((*MockAigcAccountServer)(nil).BatUpdateHeartbeat), ctx, in)
}

// BatchGetAIAccount mocks base method.
func (m *MockAigcAccountServer) BatchGetAIAccount(ctx context.Context, in *BatchGetAIAccountRequest) (*BatchGetAIAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAIAccount", ctx, in)
	ret0, _ := ret[0].(*BatchGetAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAIAccount indicates an expected call of BatchGetAIAccount.
func (mr *MockAigcAccountServerMockRecorder) BatchGetAIAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAIAccount", reflect.TypeOf((*MockAigcAccountServer)(nil).BatchGetAIAccount), ctx, in)
}

// BatchUnregisterAIAccount mocks base method.
func (m *MockAigcAccountServer) BatchUnregisterAIAccount(ctx context.Context, in *BatchUnregisterAIAccountRequest) (*BatchUnregisterAIAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUnregisterAIAccount", ctx, in)
	ret0, _ := ret[0].(*BatchUnregisterAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUnregisterAIAccount indicates an expected call of BatchUnregisterAIAccount.
func (mr *MockAigcAccountServerMockRecorder) BatchUnregisterAIAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUnregisterAIAccount", reflect.TypeOf((*MockAigcAccountServer)(nil).BatchUnregisterAIAccount), ctx, in)
}

// CheckReceptionUser mocks base method.
func (m *MockAigcAccountServer) CheckReceptionUser(ctx context.Context, in *CheckReceptionUserRequest) (*CheckReceptionUserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckReceptionUser", ctx, in)
	ret0, _ := ret[0].(*CheckReceptionUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckReceptionUser indicates an expected call of CheckReceptionUser.
func (mr *MockAigcAccountServerMockRecorder) CheckReceptionUser(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckReceptionUser", reflect.TypeOf((*MockAigcAccountServer)(nil).CheckReceptionUser), ctx, in)
}

// CreateAIAccount mocks base method.
func (m *MockAigcAccountServer) CreateAIAccount(ctx context.Context, in *CreateAIAccountRequest) (*CreateAIAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAIAccount", ctx, in)
	ret0, _ := ret[0].(*CreateAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAIAccount indicates an expected call of CreateAIAccount.
func (mr *MockAigcAccountServerMockRecorder) CreateAIAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAIAccount", reflect.TypeOf((*MockAigcAccountServer)(nil).CreateAIAccount), ctx, in)
}

// GetAIAccount mocks base method.
func (m *MockAigcAccountServer) GetAIAccount(ctx context.Context, in *GetAIAccountRequest) (*GetAIAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAIAccount", ctx, in)
	ret0, _ := ret[0].(*GetAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIAccount indicates an expected call of GetAIAccount.
func (mr *MockAigcAccountServerMockRecorder) GetAIAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIAccount", reflect.TypeOf((*MockAigcAccountServer)(nil).GetAIAccount), ctx, in)
}

// GetAIAccountByGender mocks base method.
func (m *MockAigcAccountServer) GetAIAccountByGender(ctx context.Context, in *GetAIAccountByGenderRequest) (*GetAIAccountByGenderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAIAccountByGender", ctx, in)
	ret0, _ := ret[0].(*GetAIAccountByGenderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIAccountByGender indicates an expected call of GetAIAccountByGender.
func (mr *MockAigcAccountServerMockRecorder) GetAIAccountByGender(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIAccountByGender", reflect.TypeOf((*MockAigcAccountServer)(nil).GetAIAccountByGender), ctx, in)
}

// GetAiCommentPostCount mocks base method.
func (m *MockAigcAccountServer) GetAiCommentPostCount(ctx context.Context, in *GetAiCommentPostCountRequest) (*GetAiCommentPostCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAiCommentPostCount", ctx, in)
	ret0, _ := ret[0].(*GetAiCommentPostCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiCommentPostCount indicates an expected call of GetAiCommentPostCount.
func (mr *MockAigcAccountServerMockRecorder) GetAiCommentPostCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiCommentPostCount", reflect.TypeOf((*MockAigcAccountServer)(nil).GetAiCommentPostCount), ctx, in)
}

// GetAiLikePostCount mocks base method.
func (m *MockAigcAccountServer) GetAiLikePostCount(ctx context.Context, in *GetAiLikePostCountRequest) (*GetAiLikePostCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAiLikePostCount", ctx, in)
	ret0, _ := ret[0].(*GetAiLikePostCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiLikePostCount indicates an expected call of GetAiLikePostCount.
func (mr *MockAigcAccountServerMockRecorder) GetAiLikePostCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiLikePostCount", reflect.TypeOf((*MockAigcAccountServer)(nil).GetAiLikePostCount), ctx, in)
}

// GetAiPost mocks base method.
func (m *MockAigcAccountServer) GetAiPost(ctx context.Context, in *GetAiPostRequest) (*GetAiPostResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAiPost", ctx, in)
	ret0, _ := ret[0].(*GetAiPostResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiPost indicates an expected call of GetAiPost.
func (mr *MockAigcAccountServerMockRecorder) GetAiPost(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiPost", reflect.TypeOf((*MockAigcAccountServer)(nil).GetAiPost), ctx, in)
}

// GetAiRoomCfg mocks base method.
func (m *MockAigcAccountServer) GetAiRoomCfg(ctx context.Context, in *GetAiRoomCfgRequest) (*GetAiRoomCfgResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAiRoomCfg", ctx, in)
	ret0, _ := ret[0].(*GetAiRoomCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiRoomCfg indicates an expected call of GetAiRoomCfg.
func (mr *MockAigcAccountServerMockRecorder) GetAiRoomCfg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiRoomCfg", reflect.TypeOf((*MockAigcAccountServer)(nil).GetAiRoomCfg), ctx, in)
}

// GetChatRound mocks base method.
func (m *MockAigcAccountServer) GetChatRound(ctx context.Context, in *GetChatRoundRequest) (*GetChatRoundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChatRound", ctx, in)
	ret0, _ := ret[0].(*GetChatRoundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChatRound indicates an expected call of GetChatRound.
func (mr *MockAigcAccountServerMockRecorder) GetChatRound(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChatRound", reflect.TypeOf((*MockAigcAccountServer)(nil).GetChatRound), ctx, in)
}

// GetLastChatTime mocks base method.
func (m *MockAigcAccountServer) GetLastChatTime(ctx context.Context, in *GetLastChatTimeRequest) (*GetLastChatTimeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastChatTime", ctx, in)
	ret0, _ := ret[0].(*GetLastChatTimeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastChatTime indicates an expected call of GetLastChatTime.
func (mr *MockAigcAccountServerMockRecorder) GetLastChatTime(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastChatTime", reflect.TypeOf((*MockAigcAccountServer)(nil).GetLastChatTime), ctx, in)
}

// GetLastReceptionTime mocks base method.
func (m *MockAigcAccountServer) GetLastReceptionTime(ctx context.Context, in *GetLastReceptionTimeRequest) (*GetLastReceptionTimeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastReceptionTime", ctx, in)
	ret0, _ := ret[0].(*GetLastReceptionTimeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastReceptionTime indicates an expected call of GetLastReceptionTime.
func (mr *MockAigcAccountServerMockRecorder) GetLastReceptionTime(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastReceptionTime", reflect.TypeOf((*MockAigcAccountServer)(nil).GetLastReceptionTime), ctx, in)
}

// GetNeedHeartbeatRoom mocks base method.
func (m *MockAigcAccountServer) GetNeedHeartbeatRoom(ctx context.Context, in *GetNeedHeartbeatRoomRequest) (*GetNeedHeartbeatRoomResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNeedHeartbeatRoom", ctx, in)
	ret0, _ := ret[0].(*GetNeedHeartbeatRoomResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNeedHeartbeatRoom indicates an expected call of GetNeedHeartbeatRoom.
func (mr *MockAigcAccountServerMockRecorder) GetNeedHeartbeatRoom(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNeedHeartbeatRoom", reflect.TypeOf((*MockAigcAccountServer)(nil).GetNeedHeartbeatRoom), ctx, in)
}

// GetPageAIAccount mocks base method.
func (m *MockAigcAccountServer) GetPageAIAccount(ctx context.Context, in *GetPageAIAccountRequest) (*GetPageAIAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPageAIAccount", ctx, in)
	ret0, _ := ret[0].(*GetPageAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPageAIAccount indicates an expected call of GetPageAIAccount.
func (mr *MockAigcAccountServerMockRecorder) GetPageAIAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPageAIAccount", reflect.TypeOf((*MockAigcAccountServer)(nil).GetPageAIAccount), ctx, in)
}

// GetPostAiCommentCount mocks base method.
func (m *MockAigcAccountServer) GetPostAiCommentCount(ctx context.Context, in *GetPostAiCommentCountRequest) (*GetPostAiCommentCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPostAiCommentCount", ctx, in)
	ret0, _ := ret[0].(*GetPostAiCommentCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostAiCommentCount indicates an expected call of GetPostAiCommentCount.
func (mr *MockAigcAccountServerMockRecorder) GetPostAiCommentCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostAiCommentCount", reflect.TypeOf((*MockAigcAccountServer)(nil).GetPostAiCommentCount), ctx, in)
}

// GetPostAiCommentList mocks base method.
func (m *MockAigcAccountServer) GetPostAiCommentList(ctx context.Context, in *GetPostAiCommentListRequest) (*GetPostAiCommentListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPostAiCommentList", ctx, in)
	ret0, _ := ret[0].(*GetPostAiCommentListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostAiCommentList indicates an expected call of GetPostAiCommentList.
func (mr *MockAigcAccountServerMockRecorder) GetPostAiCommentList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostAiCommentList", reflect.TypeOf((*MockAigcAccountServer)(nil).GetPostAiCommentList), ctx, in)
}

// GetRoomUserNum mocks base method.
func (m *MockAigcAccountServer) GetRoomUserNum(ctx context.Context, in *GetRoomUserNumRequest) (*GetRoomUserNumResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoomUserNum", ctx, in)
	ret0, _ := ret[0].(*GetRoomUserNumResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRoomUserNum indicates an expected call of GetRoomUserNum.
func (mr *MockAigcAccountServerMockRecorder) GetRoomUserNum(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoomUserNum", reflect.TypeOf((*MockAigcAccountServer)(nil).GetRoomUserNum), ctx, in)
}

// GetRunningAiRooms mocks base method.
func (m *MockAigcAccountServer) GetRunningAiRooms(ctx context.Context, in *GetRunningAiRoomsRequest) (*GetRunningAiRoomsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRunningAiRooms", ctx, in)
	ret0, _ := ret[0].(*GetRunningAiRoomsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRunningAiRooms indicates an expected call of GetRunningAiRooms.
func (mr *MockAigcAccountServerMockRecorder) GetRunningAiRooms(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRunningAiRooms", reflect.TypeOf((*MockAigcAccountServer)(nil).GetRunningAiRooms), ctx, in)
}

// GetUserAiChatRecord mocks base method.
func (m *MockAigcAccountServer) GetUserAiChatRecord(ctx context.Context, in *GetUserAiChatRecordRequest) (*GetUserAiChatRecordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAiChatRecord", ctx, in)
	ret0, _ := ret[0].(*GetUserAiChatRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAiChatRecord indicates an expected call of GetUserAiChatRecord.
func (mr *MockAigcAccountServerMockRecorder) GetUserAiChatRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAiChatRecord", reflect.TypeOf((*MockAigcAccountServer)(nil).GetUserAiChatRecord), ctx, in)
}

// GetUserAiInteractionRecord mocks base method.
func (m *MockAigcAccountServer) GetUserAiInteractionRecord(ctx context.Context, in *GetUserAiInteractionRecordRequest) (*GetUserAiInteractionRecordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAiInteractionRecord", ctx, in)
	ret0, _ := ret[0].(*GetUserAiInteractionRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAiInteractionRecord indicates an expected call of GetUserAiInteractionRecord.
func (mr *MockAigcAccountServerMockRecorder) GetUserAiInteractionRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAiInteractionRecord", reflect.TypeOf((*MockAigcAccountServer)(nil).GetUserAiInteractionRecord), ctx, in)
}

// GetWaitingReceptionUser mocks base method.
func (m *MockAigcAccountServer) GetWaitingReceptionUser(ctx context.Context, in *GetWaitingReceptionUserRequest) (*GetWaitingReceptionUserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWaitingReceptionUser", ctx, in)
	ret0, _ := ret[0].(*GetWaitingReceptionUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWaitingReceptionUser indicates an expected call of GetWaitingReceptionUser.
func (mr *MockAigcAccountServerMockRecorder) GetWaitingReceptionUser(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWaitingReceptionUser", reflect.TypeOf((*MockAigcAccountServer)(nil).GetWaitingReceptionUser), ctx, in)
}

// IncrChatRound mocks base method.
func (m *MockAigcAccountServer) IncrChatRound(ctx context.Context, in *IncrChatRoundRequest) (*IncrChatRoundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrChatRound", ctx, in)
	ret0, _ := ret[0].(*IncrChatRoundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrChatRound indicates an expected call of IncrChatRound.
func (mr *MockAigcAccountServerMockRecorder) IncrChatRound(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrChatRound", reflect.TypeOf((*MockAigcAccountServer)(nil).IncrChatRound), ctx, in)
}

// RecordReceptionTime mocks base method.
func (m *MockAigcAccountServer) RecordReceptionTime(ctx context.Context, in *RecordReceptionTimeRequest) (*RecordReceptionTimeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordReceptionTime", ctx, in)
	ret0, _ := ret[0].(*RecordReceptionTimeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordReceptionTime indicates an expected call of RecordReceptionTime.
func (mr *MockAigcAccountServerMockRecorder) RecordReceptionTime(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordReceptionTime", reflect.TypeOf((*MockAigcAccountServer)(nil).RecordReceptionTime), ctx, in)
}

// RecordReceptionUser mocks base method.
func (m *MockAigcAccountServer) RecordReceptionUser(ctx context.Context, in *RecordReceptionUserRequest) (*RecordReceptionUserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordReceptionUser", ctx, in)
	ret0, _ := ret[0].(*RecordReceptionUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordReceptionUser indicates an expected call of RecordReceptionUser.
func (mr *MockAigcAccountServerMockRecorder) RecordReceptionUser(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordReceptionUser", reflect.TypeOf((*MockAigcAccountServer)(nil).RecordReceptionUser), ctx, in)
}

// RecordRoomUserNum mocks base method.
func (m *MockAigcAccountServer) RecordRoomUserNum(ctx context.Context, in *RecordRoomUserNumRequest) (*RecordRoomUserNumResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordRoomUserNum", ctx, in)
	ret0, _ := ret[0].(*RecordRoomUserNumResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordRoomUserNum indicates an expected call of RecordRoomUserNum.
func (mr *MockAigcAccountServerMockRecorder) RecordRoomUserNum(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordRoomUserNum", reflect.TypeOf((*MockAigcAccountServer)(nil).RecordRoomUserNum), ctx, in)
}

// RecordWaitingReceptionUser mocks base method.
func (m *MockAigcAccountServer) RecordWaitingReceptionUser(ctx context.Context, in *RecordWaitingReceptionUserRequest) (*RecordWaitingReceptionUserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordWaitingReceptionUser", ctx, in)
	ret0, _ := ret[0].(*RecordWaitingReceptionUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordWaitingReceptionUser indicates an expected call of RecordWaitingReceptionUser.
func (mr *MockAigcAccountServerMockRecorder) RecordWaitingReceptionUser(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordWaitingReceptionUser", reflect.TypeOf((*MockAigcAccountServer)(nil).RecordWaitingReceptionUser), ctx, in)
}

// RemoveFromHeartbeatPool mocks base method.
func (m *MockAigcAccountServer) RemoveFromHeartbeatPool(ctx context.Context, in *RemoveFromHeartbeatPoolRequest) (*RemoveFromHeartbeatPoolResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveFromHeartbeatPool", ctx, in)
	ret0, _ := ret[0].(*RemoveFromHeartbeatPoolResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveFromHeartbeatPool indicates an expected call of RemoveFromHeartbeatPool.
func (mr *MockAigcAccountServerMockRecorder) RemoveFromHeartbeatPool(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveFromHeartbeatPool", reflect.TypeOf((*MockAigcAccountServer)(nil).RemoveFromHeartbeatPool), ctx, in)
}

// UpdateAIAccount mocks base method.
func (m *MockAigcAccountServer) UpdateAIAccount(ctx context.Context, in *UpdateAIAccountRequest) (*UpdateAIAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAIAccount", ctx, in)
	ret0, _ := ret[0].(*UpdateAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAIAccount indicates an expected call of UpdateAIAccount.
func (mr *MockAigcAccountServerMockRecorder) UpdateAIAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIAccount", reflect.TypeOf((*MockAigcAccountServer)(nil).UpdateAIAccount), ctx, in)
}

// UpdateAiRoomCfg mocks base method.
func (m *MockAigcAccountServer) UpdateAiRoomCfg(ctx context.Context, in *UpdateAiRoomCfgRequest) (*UpdateAiRoomCfgResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAiRoomCfg", ctx, in)
	ret0, _ := ret[0].(*UpdateAiRoomCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAiRoomCfg indicates an expected call of UpdateAiRoomCfg.
func (mr *MockAigcAccountServerMockRecorder) UpdateAiRoomCfg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAiRoomCfg", reflect.TypeOf((*MockAigcAccountServer)(nil).UpdateAiRoomCfg), ctx, in)
}

// UpdateChatTime mocks base method.
func (m *MockAigcAccountServer) UpdateChatTime(ctx context.Context, in *UpdateChatTimeRequest) (*UpdateChatTimeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateChatTime", ctx, in)
	ret0, _ := ret[0].(*UpdateChatTimeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateChatTime indicates an expected call of UpdateChatTime.
func (mr *MockAigcAccountServerMockRecorder) UpdateChatTime(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChatTime", reflect.TypeOf((*MockAigcAccountServer)(nil).UpdateChatTime), ctx, in)
}
