// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-account/aigc-account.proto

package aigc_account // import "golang.52tt.com/protocol/services/aigc/aigc-account"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetAIAccountSource int32

const (
	// 默认
	GetAIAccountSource_DEFAULT GetAIAccountSource = 0
	// 运营后台
	GetAIAccountSource_ADMIN_BACKEND GetAIAccountSource = 1
	// im tab
	GetAIAccountSource_IM_TAB GetAIAccountSource = 2
	// 麦上
	GetAIAccountSource_ON_MIC GetAIAccountSource = 3
	// ugc 个人页
	GetAIAccountSource_UGC_PERSONAL_PAGE GetAIAccountSource = 4
	// 房间个人资料卡
	GetAIAccountSource_CHANNEL_PERSONAL_PAGE GetAIAccountSource = 5
	// ugc 流
	GetAIAccountSource_UGC_FEEDS GetAIAccountSource = 6
	// 房间列表
	GetAIAccountSource_CHANNEL_LIST GetAIAccountSource = 7
)

var GetAIAccountSource_name = map[int32]string{
	0: "DEFAULT",
	1: "ADMIN_BACKEND",
	2: "IM_TAB",
	3: "ON_MIC",
	4: "UGC_PERSONAL_PAGE",
	5: "CHANNEL_PERSONAL_PAGE",
	6: "UGC_FEEDS",
	7: "CHANNEL_LIST",
}
var GetAIAccountSource_value = map[string]int32{
	"DEFAULT":               0,
	"ADMIN_BACKEND":         1,
	"IM_TAB":                2,
	"ON_MIC":                3,
	"UGC_PERSONAL_PAGE":     4,
	"CHANNEL_PERSONAL_PAGE": 5,
	"UGC_FEEDS":             6,
	"CHANNEL_LIST":          7,
}

func (x GetAIAccountSource) String() string {
	return proto.EnumName(GetAIAccountSource_name, int32(x))
}
func (GetAIAccountSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{0}
}

type AIAccount struct {
	// ID
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 密码
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	// 提示词ID, 为空则AI不会回复用户
	PromptId uint32 `protobuf:"varint,4,opt,name=prompt_id,json=promptId,proto3" json:"prompt_id,omitempty"`
	// 音色ID
	TimbreId uint32 `protobuf:"varint,5,opt,name=timbre_id,json=timbreId,proto3" json:"timbre_id,omitempty"`
	// 关联角色ID
	RoleId uint32 `protobuf:"varint,6,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 开场白
	Prologue string `protobuf:"bytes,7,opt,name=prologue,proto3" json:"prologue,omitempty"`
	// 是否已注销
	IsUnregister bool `protobuf:"varint,8,opt,name=is_unregister,json=isUnregister,proto3" json:"is_unregister,omitempty"`
	// 创建时间
	CreateTime int64 `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime int64 `protobuf:"varint,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 是否已加入风控白名单
	IsInRiskWhiteList bool `protobuf:"varint,11,opt,name=is_in_risk_white_list,json=isInRiskWhiteList,proto3" json:"is_in_risk_white_list,omitempty"`
	// 标识
	Identity string `protobuf:"bytes,12,opt,name=identity,proto3" json:"identity,omitempty"`
	// 说明
	Desc string `protobuf:"bytes,13,opt,name=desc,proto3" json:"desc,omitempty"`
	Sort uint32 `protobuf:"varint,14,opt,name=sort,proto3" json:"sort,omitempty"`
	// 账号标签
	AccountTags []string `protobuf:"bytes,15,rep,name=account_tags,json=accountTags,proto3" json:"account_tags,omitempty"`
	// 是否展示在扩列墙
	IsShowInChatCardWall bool     `protobuf:"varint,16,opt,name=is_show_in_chat_card_wall,json=isShowInChatCardWall,proto3" json:"is_show_in_chat_card_wall,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIAccount) Reset()         { *m = AIAccount{} }
func (m *AIAccount) String() string { return proto.CompactTextString(m) }
func (*AIAccount) ProtoMessage()    {}
func (*AIAccount) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{0}
}
func (m *AIAccount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIAccount.Unmarshal(m, b)
}
func (m *AIAccount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIAccount.Marshal(b, m, deterministic)
}
func (dst *AIAccount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIAccount.Merge(dst, src)
}
func (m *AIAccount) XXX_Size() int {
	return xxx_messageInfo_AIAccount.Size(m)
}
func (m *AIAccount) XXX_DiscardUnknown() {
	xxx_messageInfo_AIAccount.DiscardUnknown(m)
}

var xxx_messageInfo_AIAccount proto.InternalMessageInfo

func (m *AIAccount) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AIAccount) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

func (m *AIAccount) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *AIAccount) GetPromptId() uint32 {
	if m != nil {
		return m.PromptId
	}
	return 0
}

func (m *AIAccount) GetTimbreId() uint32 {
	if m != nil {
		return m.TimbreId
	}
	return 0
}

func (m *AIAccount) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *AIAccount) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *AIAccount) GetIsUnregister() bool {
	if m != nil {
		return m.IsUnregister
	}
	return false
}

func (m *AIAccount) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *AIAccount) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *AIAccount) GetIsInRiskWhiteList() bool {
	if m != nil {
		return m.IsInRiskWhiteList
	}
	return false
}

func (m *AIAccount) GetIdentity() string {
	if m != nil {
		return m.Identity
	}
	return ""
}

func (m *AIAccount) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *AIAccount) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *AIAccount) GetAccountTags() []string {
	if m != nil {
		return m.AccountTags
	}
	return nil
}

func (m *AIAccount) GetIsShowInChatCardWall() bool {
	if m != nil {
		return m.IsShowInChatCardWall
	}
	return false
}

type CreateAIAccountRequest struct {
	Account              *AIAccount `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CreateAIAccountRequest) Reset()         { *m = CreateAIAccountRequest{} }
func (m *CreateAIAccountRequest) String() string { return proto.CompactTextString(m) }
func (*CreateAIAccountRequest) ProtoMessage()    {}
func (*CreateAIAccountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{1}
}
func (m *CreateAIAccountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIAccountRequest.Unmarshal(m, b)
}
func (m *CreateAIAccountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIAccountRequest.Marshal(b, m, deterministic)
}
func (dst *CreateAIAccountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIAccountRequest.Merge(dst, src)
}
func (m *CreateAIAccountRequest) XXX_Size() int {
	return xxx_messageInfo_CreateAIAccountRequest.Size(m)
}
func (m *CreateAIAccountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIAccountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIAccountRequest proto.InternalMessageInfo

func (m *CreateAIAccountRequest) GetAccount() *AIAccount {
	if m != nil {
		return m.Account
	}
	return nil
}

type CreateAIAccountResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateAIAccountResponse) Reset()         { *m = CreateAIAccountResponse{} }
func (m *CreateAIAccountResponse) String() string { return proto.CompactTextString(m) }
func (*CreateAIAccountResponse) ProtoMessage()    {}
func (*CreateAIAccountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{2}
}
func (m *CreateAIAccountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIAccountResponse.Unmarshal(m, b)
}
func (m *CreateAIAccountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIAccountResponse.Marshal(b, m, deterministic)
}
func (dst *CreateAIAccountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIAccountResponse.Merge(dst, src)
}
func (m *CreateAIAccountResponse) XXX_Size() int {
	return xxx_messageInfo_CreateAIAccountResponse.Size(m)
}
func (m *CreateAIAccountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIAccountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIAccountResponse proto.InternalMessageInfo

type UpdateAIAccountRequest struct {
	Account              *AIAccount `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UpdateAIAccountRequest) Reset()         { *m = UpdateAIAccountRequest{} }
func (m *UpdateAIAccountRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateAIAccountRequest) ProtoMessage()    {}
func (*UpdateAIAccountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{3}
}
func (m *UpdateAIAccountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIAccountRequest.Unmarshal(m, b)
}
func (m *UpdateAIAccountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIAccountRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateAIAccountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIAccountRequest.Merge(dst, src)
}
func (m *UpdateAIAccountRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateAIAccountRequest.Size(m)
}
func (m *UpdateAIAccountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIAccountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIAccountRequest proto.InternalMessageInfo

func (m *UpdateAIAccountRequest) GetAccount() *AIAccount {
	if m != nil {
		return m.Account
	}
	return nil
}

type UpdateAIAccountResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAIAccountResponse) Reset()         { *m = UpdateAIAccountResponse{} }
func (m *UpdateAIAccountResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateAIAccountResponse) ProtoMessage()    {}
func (*UpdateAIAccountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{4}
}
func (m *UpdateAIAccountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIAccountResponse.Unmarshal(m, b)
}
func (m *UpdateAIAccountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIAccountResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateAIAccountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIAccountResponse.Merge(dst, src)
}
func (m *UpdateAIAccountResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateAIAccountResponse.Size(m)
}
func (m *UpdateAIAccountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIAccountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIAccountResponse proto.InternalMessageInfo

type BatchUnregisterAIAccountRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUnregisterAIAccountRequest) Reset()         { *m = BatchUnregisterAIAccountRequest{} }
func (m *BatchUnregisterAIAccountRequest) String() string { return proto.CompactTextString(m) }
func (*BatchUnregisterAIAccountRequest) ProtoMessage()    {}
func (*BatchUnregisterAIAccountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{5}
}
func (m *BatchUnregisterAIAccountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUnregisterAIAccountRequest.Unmarshal(m, b)
}
func (m *BatchUnregisterAIAccountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUnregisterAIAccountRequest.Marshal(b, m, deterministic)
}
func (dst *BatchUnregisterAIAccountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUnregisterAIAccountRequest.Merge(dst, src)
}
func (m *BatchUnregisterAIAccountRequest) XXX_Size() int {
	return xxx_messageInfo_BatchUnregisterAIAccountRequest.Size(m)
}
func (m *BatchUnregisterAIAccountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUnregisterAIAccountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUnregisterAIAccountRequest proto.InternalMessageInfo

func (m *BatchUnregisterAIAccountRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchUnregisterAIAccountResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUnregisterAIAccountResponse) Reset()         { *m = BatchUnregisterAIAccountResponse{} }
func (m *BatchUnregisterAIAccountResponse) String() string { return proto.CompactTextString(m) }
func (*BatchUnregisterAIAccountResponse) ProtoMessage()    {}
func (*BatchUnregisterAIAccountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{6}
}
func (m *BatchUnregisterAIAccountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUnregisterAIAccountResponse.Unmarshal(m, b)
}
func (m *BatchUnregisterAIAccountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUnregisterAIAccountResponse.Marshal(b, m, deterministic)
}
func (dst *BatchUnregisterAIAccountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUnregisterAIAccountResponse.Merge(dst, src)
}
func (m *BatchUnregisterAIAccountResponse) XXX_Size() int {
	return xxx_messageInfo_BatchUnregisterAIAccountResponse.Size(m)
}
func (m *BatchUnregisterAIAccountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUnregisterAIAccountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUnregisterAIAccountResponse proto.InternalMessageInfo

type GetPageAIAccountRequest struct {
	Page                   uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                   uint32   `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	ChatCardWallShowStatus uint32   `protobuf:"varint,3,opt,name=chat_card_wall_show_status,json=chatCardWallShowStatus,proto3" json:"chat_card_wall_show_status,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *GetPageAIAccountRequest) Reset()         { *m = GetPageAIAccountRequest{} }
func (m *GetPageAIAccountRequest) String() string { return proto.CompactTextString(m) }
func (*GetPageAIAccountRequest) ProtoMessage()    {}
func (*GetPageAIAccountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{7}
}
func (m *GetPageAIAccountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPageAIAccountRequest.Unmarshal(m, b)
}
func (m *GetPageAIAccountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPageAIAccountRequest.Marshal(b, m, deterministic)
}
func (dst *GetPageAIAccountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPageAIAccountRequest.Merge(dst, src)
}
func (m *GetPageAIAccountRequest) XXX_Size() int {
	return xxx_messageInfo_GetPageAIAccountRequest.Size(m)
}
func (m *GetPageAIAccountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPageAIAccountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPageAIAccountRequest proto.InternalMessageInfo

func (m *GetPageAIAccountRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetPageAIAccountRequest) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetPageAIAccountRequest) GetChatCardWallShowStatus() uint32 {
	if m != nil {
		return m.ChatCardWallShowStatus
	}
	return 0
}

type GetPageAIAccountResponse struct {
	AccountList          []*AIAccount `protobuf:"bytes,1,rep,name=account_list,json=accountList,proto3" json:"account_list,omitempty"`
	Total                uint32       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPageAIAccountResponse) Reset()         { *m = GetPageAIAccountResponse{} }
func (m *GetPageAIAccountResponse) String() string { return proto.CompactTextString(m) }
func (*GetPageAIAccountResponse) ProtoMessage()    {}
func (*GetPageAIAccountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{8}
}
func (m *GetPageAIAccountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPageAIAccountResponse.Unmarshal(m, b)
}
func (m *GetPageAIAccountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPageAIAccountResponse.Marshal(b, m, deterministic)
}
func (dst *GetPageAIAccountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPageAIAccountResponse.Merge(dst, src)
}
func (m *GetPageAIAccountResponse) XXX_Size() int {
	return xxx_messageInfo_GetPageAIAccountResponse.Size(m)
}
func (m *GetPageAIAccountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPageAIAccountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPageAIAccountResponse proto.InternalMessageInfo

func (m *GetPageAIAccountResponse) GetAccountList() []*AIAccount {
	if m != nil {
		return m.AccountList
	}
	return nil
}

func (m *GetPageAIAccountResponse) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetAIAccountRequest struct {
	Uid                  uint32             `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ReqSource            GetAIAccountSource `protobuf:"varint,2,opt,name=req_source,json=reqSource,proto3,enum=aigc_account.GetAIAccountSource" json:"req_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetAIAccountRequest) Reset()         { *m = GetAIAccountRequest{} }
func (m *GetAIAccountRequest) String() string { return proto.CompactTextString(m) }
func (*GetAIAccountRequest) ProtoMessage()    {}
func (*GetAIAccountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{9}
}
func (m *GetAIAccountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIAccountRequest.Unmarshal(m, b)
}
func (m *GetAIAccountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIAccountRequest.Marshal(b, m, deterministic)
}
func (dst *GetAIAccountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIAccountRequest.Merge(dst, src)
}
func (m *GetAIAccountRequest) XXX_Size() int {
	return xxx_messageInfo_GetAIAccountRequest.Size(m)
}
func (m *GetAIAccountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIAccountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIAccountRequest proto.InternalMessageInfo

func (m *GetAIAccountRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAIAccountRequest) GetReqSource() GetAIAccountSource {
	if m != nil {
		return m.ReqSource
	}
	return GetAIAccountSource_DEFAULT
}

type GetAIAccountResponse struct {
	Account              *AIAccount `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAIAccountResponse) Reset()         { *m = GetAIAccountResponse{} }
func (m *GetAIAccountResponse) String() string { return proto.CompactTextString(m) }
func (*GetAIAccountResponse) ProtoMessage()    {}
func (*GetAIAccountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{10}
}
func (m *GetAIAccountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIAccountResponse.Unmarshal(m, b)
}
func (m *GetAIAccountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIAccountResponse.Marshal(b, m, deterministic)
}
func (dst *GetAIAccountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIAccountResponse.Merge(dst, src)
}
func (m *GetAIAccountResponse) XXX_Size() int {
	return xxx_messageInfo_GetAIAccountResponse.Size(m)
}
func (m *GetAIAccountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIAccountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIAccountResponse proto.InternalMessageInfo

func (m *GetAIAccountResponse) GetAccount() *AIAccount {
	if m != nil {
		return m.Account
	}
	return nil
}

type BatchGetAIAccountRequest struct {
	UidList              []uint32           `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	ReqSource            GetAIAccountSource `protobuf:"varint,2,opt,name=req_source,json=reqSource,proto3,enum=aigc_account.GetAIAccountSource" json:"req_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchGetAIAccountRequest) Reset()         { *m = BatchGetAIAccountRequest{} }
func (m *BatchGetAIAccountRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetAIAccountRequest) ProtoMessage()    {}
func (*BatchGetAIAccountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{11}
}
func (m *BatchGetAIAccountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAIAccountRequest.Unmarshal(m, b)
}
func (m *BatchGetAIAccountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAIAccountRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetAIAccountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAIAccountRequest.Merge(dst, src)
}
func (m *BatchGetAIAccountRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetAIAccountRequest.Size(m)
}
func (m *BatchGetAIAccountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAIAccountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAIAccountRequest proto.InternalMessageInfo

func (m *BatchGetAIAccountRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetAIAccountRequest) GetReqSource() GetAIAccountSource {
	if m != nil {
		return m.ReqSource
	}
	return GetAIAccountSource_DEFAULT
}

type BatchGetAIAccountResponse struct {
	AccountList          []*AIAccount `protobuf:"bytes,1,rep,name=account_list,json=accountList,proto3" json:"account_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchGetAIAccountResponse) Reset()         { *m = BatchGetAIAccountResponse{} }
func (m *BatchGetAIAccountResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetAIAccountResponse) ProtoMessage()    {}
func (*BatchGetAIAccountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{12}
}
func (m *BatchGetAIAccountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAIAccountResponse.Unmarshal(m, b)
}
func (m *BatchGetAIAccountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAIAccountResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetAIAccountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAIAccountResponse.Merge(dst, src)
}
func (m *BatchGetAIAccountResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetAIAccountResponse.Size(m)
}
func (m *BatchGetAIAccountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAIAccountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAIAccountResponse proto.InternalMessageInfo

func (m *BatchGetAIAccountResponse) GetAccountList() []*AIAccount {
	if m != nil {
		return m.AccountList
	}
	return nil
}

// 增加聊天轮次
type IncrChatRoundRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AiUid                uint32   `protobuf:"varint,2,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncrChatRoundRequest) Reset()         { *m = IncrChatRoundRequest{} }
func (m *IncrChatRoundRequest) String() string { return proto.CompactTextString(m) }
func (*IncrChatRoundRequest) ProtoMessage()    {}
func (*IncrChatRoundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{13}
}
func (m *IncrChatRoundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncrChatRoundRequest.Unmarshal(m, b)
}
func (m *IncrChatRoundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncrChatRoundRequest.Marshal(b, m, deterministic)
}
func (dst *IncrChatRoundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncrChatRoundRequest.Merge(dst, src)
}
func (m *IncrChatRoundRequest) XXX_Size() int {
	return xxx_messageInfo_IncrChatRoundRequest.Size(m)
}
func (m *IncrChatRoundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_IncrChatRoundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_IncrChatRoundRequest proto.InternalMessageInfo

func (m *IncrChatRoundRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IncrChatRoundRequest) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

type IncrChatRoundResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncrChatRoundResponse) Reset()         { *m = IncrChatRoundResponse{} }
func (m *IncrChatRoundResponse) String() string { return proto.CompactTextString(m) }
func (*IncrChatRoundResponse) ProtoMessage()    {}
func (*IncrChatRoundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{14}
}
func (m *IncrChatRoundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncrChatRoundResponse.Unmarshal(m, b)
}
func (m *IncrChatRoundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncrChatRoundResponse.Marshal(b, m, deterministic)
}
func (dst *IncrChatRoundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncrChatRoundResponse.Merge(dst, src)
}
func (m *IncrChatRoundResponse) XXX_Size() int {
	return xxx_messageInfo_IncrChatRoundResponse.Size(m)
}
func (m *IncrChatRoundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IncrChatRoundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IncrChatRoundResponse proto.InternalMessageInfo

// 获取聊天轮次
type GetChatRoundRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AiUid                uint32   `protobuf:"varint,2,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	NeedTotal            bool     `protobuf:"varint,3,opt,name=need_total,json=needTotal,proto3" json:"need_total,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChatRoundRequest) Reset()         { *m = GetChatRoundRequest{} }
func (m *GetChatRoundRequest) String() string { return proto.CompactTextString(m) }
func (*GetChatRoundRequest) ProtoMessage()    {}
func (*GetChatRoundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{15}
}
func (m *GetChatRoundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChatRoundRequest.Unmarshal(m, b)
}
func (m *GetChatRoundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChatRoundRequest.Marshal(b, m, deterministic)
}
func (dst *GetChatRoundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChatRoundRequest.Merge(dst, src)
}
func (m *GetChatRoundRequest) XXX_Size() int {
	return xxx_messageInfo_GetChatRoundRequest.Size(m)
}
func (m *GetChatRoundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChatRoundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChatRoundRequest proto.InternalMessageInfo

func (m *GetChatRoundRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChatRoundRequest) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

func (m *GetChatRoundRequest) GetNeedTotal() bool {
	if m != nil {
		return m.NeedTotal
	}
	return false
}

type GetChatRoundResponse struct {
	CurRound             uint32   `protobuf:"varint,1,opt,name=cur_round,json=curRound,proto3" json:"cur_round,omitempty"`
	TotalRound           uint32   `protobuf:"varint,2,opt,name=total_round,json=totalRound,proto3" json:"total_round,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChatRoundResponse) Reset()         { *m = GetChatRoundResponse{} }
func (m *GetChatRoundResponse) String() string { return proto.CompactTextString(m) }
func (*GetChatRoundResponse) ProtoMessage()    {}
func (*GetChatRoundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{16}
}
func (m *GetChatRoundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChatRoundResponse.Unmarshal(m, b)
}
func (m *GetChatRoundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChatRoundResponse.Marshal(b, m, deterministic)
}
func (dst *GetChatRoundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChatRoundResponse.Merge(dst, src)
}
func (m *GetChatRoundResponse) XXX_Size() int {
	return xxx_messageInfo_GetChatRoundResponse.Size(m)
}
func (m *GetChatRoundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChatRoundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChatRoundResponse proto.InternalMessageInfo

func (m *GetChatRoundResponse) GetCurRound() uint32 {
	if m != nil {
		return m.CurRound
	}
	return 0
}

func (m *GetChatRoundResponse) GetTotalRound() uint32 {
	if m != nil {
		return m.TotalRound
	}
	return 0
}

// 更新最近聊天时间
type UpdateChatTimeRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AiUid                uint32   `protobuf:"varint,2,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateChatTimeRequest) Reset()         { *m = UpdateChatTimeRequest{} }
func (m *UpdateChatTimeRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateChatTimeRequest) ProtoMessage()    {}
func (*UpdateChatTimeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{17}
}
func (m *UpdateChatTimeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChatTimeRequest.Unmarshal(m, b)
}
func (m *UpdateChatTimeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChatTimeRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateChatTimeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChatTimeRequest.Merge(dst, src)
}
func (m *UpdateChatTimeRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateChatTimeRequest.Size(m)
}
func (m *UpdateChatTimeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChatTimeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChatTimeRequest proto.InternalMessageInfo

func (m *UpdateChatTimeRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateChatTimeRequest) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

type UpdateChatTimeResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateChatTimeResponse) Reset()         { *m = UpdateChatTimeResponse{} }
func (m *UpdateChatTimeResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateChatTimeResponse) ProtoMessage()    {}
func (*UpdateChatTimeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{18}
}
func (m *UpdateChatTimeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChatTimeResponse.Unmarshal(m, b)
}
func (m *UpdateChatTimeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChatTimeResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateChatTimeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChatTimeResponse.Merge(dst, src)
}
func (m *UpdateChatTimeResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateChatTimeResponse.Size(m)
}
func (m *UpdateChatTimeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChatTimeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChatTimeResponse proto.InternalMessageInfo

// 获取最近聊天时间
type GetLastChatTimeRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AiUid                uint32   `protobuf:"varint,2,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastChatTimeRequest) Reset()         { *m = GetLastChatTimeRequest{} }
func (m *GetLastChatTimeRequest) String() string { return proto.CompactTextString(m) }
func (*GetLastChatTimeRequest) ProtoMessage()    {}
func (*GetLastChatTimeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{19}
}
func (m *GetLastChatTimeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastChatTimeRequest.Unmarshal(m, b)
}
func (m *GetLastChatTimeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastChatTimeRequest.Marshal(b, m, deterministic)
}
func (dst *GetLastChatTimeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastChatTimeRequest.Merge(dst, src)
}
func (m *GetLastChatTimeRequest) XXX_Size() int {
	return xxx_messageInfo_GetLastChatTimeRequest.Size(m)
}
func (m *GetLastChatTimeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastChatTimeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastChatTimeRequest proto.InternalMessageInfo

func (m *GetLastChatTimeRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLastChatTimeRequest) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

type GetLastChatTimeResponse struct {
	LastChatTime         int64    `protobuf:"varint,1,opt,name=last_chat_time,json=lastChatTime,proto3" json:"last_chat_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastChatTimeResponse) Reset()         { *m = GetLastChatTimeResponse{} }
func (m *GetLastChatTimeResponse) String() string { return proto.CompactTextString(m) }
func (*GetLastChatTimeResponse) ProtoMessage()    {}
func (*GetLastChatTimeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{20}
}
func (m *GetLastChatTimeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastChatTimeResponse.Unmarshal(m, b)
}
func (m *GetLastChatTimeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastChatTimeResponse.Marshal(b, m, deterministic)
}
func (dst *GetLastChatTimeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastChatTimeResponse.Merge(dst, src)
}
func (m *GetLastChatTimeResponse) XXX_Size() int {
	return xxx_messageInfo_GetLastChatTimeResponse.Size(m)
}
func (m *GetLastChatTimeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastChatTimeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastChatTimeResponse proto.InternalMessageInfo

func (m *GetLastChatTimeResponse) GetLastChatTime() int64 {
	if m != nil {
		return m.LastChatTime
	}
	return 0
}

type AddAiPostRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAiPostRequest) Reset()         { *m = AddAiPostRequest{} }
func (m *AddAiPostRequest) String() string { return proto.CompactTextString(m) }
func (*AddAiPostRequest) ProtoMessage()    {}
func (*AddAiPostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{21}
}
func (m *AddAiPostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAiPostRequest.Unmarshal(m, b)
}
func (m *AddAiPostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAiPostRequest.Marshal(b, m, deterministic)
}
func (dst *AddAiPostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAiPostRequest.Merge(dst, src)
}
func (m *AddAiPostRequest) XXX_Size() int {
	return xxx_messageInfo_AddAiPostRequest.Size(m)
}
func (m *AddAiPostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAiPostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddAiPostRequest proto.InternalMessageInfo

func (m *AddAiPostRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddAiPostRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type AddAiPostResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAiPostResponse) Reset()         { *m = AddAiPostResponse{} }
func (m *AddAiPostResponse) String() string { return proto.CompactTextString(m) }
func (*AddAiPostResponse) ProtoMessage()    {}
func (*AddAiPostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{22}
}
func (m *AddAiPostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAiPostResponse.Unmarshal(m, b)
}
func (m *AddAiPostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAiPostResponse.Marshal(b, m, deterministic)
}
func (dst *AddAiPostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAiPostResponse.Merge(dst, src)
}
func (m *AddAiPostResponse) XXX_Size() int {
	return xxx_messageInfo_AddAiPostResponse.Size(m)
}
func (m *AddAiPostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAiPostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddAiPostResponse proto.InternalMessageInfo

type GetAiPostRequest struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32   `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	AiUid                []uint32 `protobuf:"varint,3,rep,packed,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAiPostRequest) Reset()         { *m = GetAiPostRequest{} }
func (m *GetAiPostRequest) String() string { return proto.CompactTextString(m) }
func (*GetAiPostRequest) ProtoMessage()    {}
func (*GetAiPostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{23}
}
func (m *GetAiPostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAiPostRequest.Unmarshal(m, b)
}
func (m *GetAiPostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAiPostRequest.Marshal(b, m, deterministic)
}
func (dst *GetAiPostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAiPostRequest.Merge(dst, src)
}
func (m *GetAiPostRequest) XXX_Size() int {
	return xxx_messageInfo_GetAiPostRequest.Size(m)
}
func (m *GetAiPostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAiPostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAiPostRequest proto.InternalMessageInfo

func (m *GetAiPostRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetAiPostRequest) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetAiPostRequest) GetAiUid() []uint32 {
	if m != nil {
		return m.AiUid
	}
	return nil
}

type GetAiPostResponse struct {
	PostIds              []string `protobuf:"bytes,1,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	TotalNum             uint32   `protobuf:"varint,2,opt,name=total_num,json=totalNum,proto3" json:"total_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAiPostResponse) Reset()         { *m = GetAiPostResponse{} }
func (m *GetAiPostResponse) String() string { return proto.CompactTextString(m) }
func (*GetAiPostResponse) ProtoMessage()    {}
func (*GetAiPostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{24}
}
func (m *GetAiPostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAiPostResponse.Unmarshal(m, b)
}
func (m *GetAiPostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAiPostResponse.Marshal(b, m, deterministic)
}
func (dst *GetAiPostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAiPostResponse.Merge(dst, src)
}
func (m *GetAiPostResponse) XXX_Size() int {
	return xxx_messageInfo_GetAiPostResponse.Size(m)
}
func (m *GetAiPostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAiPostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAiPostResponse proto.InternalMessageInfo

func (m *GetAiPostResponse) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

func (m *GetAiPostResponse) GetTotalNum() uint32 {
	if m != nil {
		return m.TotalNum
	}
	return 0
}

type AddUserAiChatRecordRequest struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserAiChatRecordRequest) Reset()         { *m = AddUserAiChatRecordRequest{} }
func (m *AddUserAiChatRecordRequest) String() string { return proto.CompactTextString(m) }
func (*AddUserAiChatRecordRequest) ProtoMessage()    {}
func (*AddUserAiChatRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{25}
}
func (m *AddUserAiChatRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserAiChatRecordRequest.Unmarshal(m, b)
}
func (m *AddUserAiChatRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserAiChatRecordRequest.Marshal(b, m, deterministic)
}
func (dst *AddUserAiChatRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserAiChatRecordRequest.Merge(dst, src)
}
func (m *AddUserAiChatRecordRequest) XXX_Size() int {
	return xxx_messageInfo_AddUserAiChatRecordRequest.Size(m)
}
func (m *AddUserAiChatRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserAiChatRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserAiChatRecordRequest proto.InternalMessageInfo

func (m *AddUserAiChatRecordRequest) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *AddUserAiChatRecordRequest) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

type AddUserAiChatRecordResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserAiChatRecordResponse) Reset()         { *m = AddUserAiChatRecordResponse{} }
func (m *AddUserAiChatRecordResponse) String() string { return proto.CompactTextString(m) }
func (*AddUserAiChatRecordResponse) ProtoMessage()    {}
func (*AddUserAiChatRecordResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{26}
}
func (m *AddUserAiChatRecordResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserAiChatRecordResponse.Unmarshal(m, b)
}
func (m *AddUserAiChatRecordResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserAiChatRecordResponse.Marshal(b, m, deterministic)
}
func (dst *AddUserAiChatRecordResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserAiChatRecordResponse.Merge(dst, src)
}
func (m *AddUserAiChatRecordResponse) XXX_Size() int {
	return xxx_messageInfo_AddUserAiChatRecordResponse.Size(m)
}
func (m *AddUserAiChatRecordResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserAiChatRecordResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserAiChatRecordResponse proto.InternalMessageInfo

type GetUserAiChatRecordRequest struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAiChatRecordRequest) Reset()         { *m = GetUserAiChatRecordRequest{} }
func (m *GetUserAiChatRecordRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserAiChatRecordRequest) ProtoMessage()    {}
func (*GetUserAiChatRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{27}
}
func (m *GetUserAiChatRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAiChatRecordRequest.Unmarshal(m, b)
}
func (m *GetUserAiChatRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAiChatRecordRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserAiChatRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAiChatRecordRequest.Merge(dst, src)
}
func (m *GetUserAiChatRecordRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserAiChatRecordRequest.Size(m)
}
func (m *GetUserAiChatRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAiChatRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAiChatRecordRequest proto.InternalMessageInfo

func (m *GetUserAiChatRecordRequest) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *GetUserAiChatRecordRequest) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

type GetUserAiChatRecordResponse struct {
	RecordTime           int64    `protobuf:"varint,1,opt,name=record_time,json=recordTime,proto3" json:"record_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAiChatRecordResponse) Reset()         { *m = GetUserAiChatRecordResponse{} }
func (m *GetUserAiChatRecordResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserAiChatRecordResponse) ProtoMessage()    {}
func (*GetUserAiChatRecordResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{28}
}
func (m *GetUserAiChatRecordResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAiChatRecordResponse.Unmarshal(m, b)
}
func (m *GetUserAiChatRecordResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAiChatRecordResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserAiChatRecordResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAiChatRecordResponse.Merge(dst, src)
}
func (m *GetUserAiChatRecordResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserAiChatRecordResponse.Size(m)
}
func (m *GetUserAiChatRecordResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAiChatRecordResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAiChatRecordResponse proto.InternalMessageInfo

func (m *GetUserAiChatRecordResponse) GetRecordTime() int64 {
	if m != nil {
		return m.RecordTime
	}
	return 0
}

type AddUserAiInteractionRecordRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AiUid                uint32   `protobuf:"varint,2,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserAiInteractionRecordRequest) Reset()         { *m = AddUserAiInteractionRecordRequest{} }
func (m *AddUserAiInteractionRecordRequest) String() string { return proto.CompactTextString(m) }
func (*AddUserAiInteractionRecordRequest) ProtoMessage()    {}
func (*AddUserAiInteractionRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{29}
}
func (m *AddUserAiInteractionRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserAiInteractionRecordRequest.Unmarshal(m, b)
}
func (m *AddUserAiInteractionRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserAiInteractionRecordRequest.Marshal(b, m, deterministic)
}
func (dst *AddUserAiInteractionRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserAiInteractionRecordRequest.Merge(dst, src)
}
func (m *AddUserAiInteractionRecordRequest) XXX_Size() int {
	return xxx_messageInfo_AddUserAiInteractionRecordRequest.Size(m)
}
func (m *AddUserAiInteractionRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserAiInteractionRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserAiInteractionRecordRequest proto.InternalMessageInfo

func (m *AddUserAiInteractionRecordRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserAiInteractionRecordRequest) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

type AddUserAiInteractionRecordResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserAiInteractionRecordResponse) Reset()         { *m = AddUserAiInteractionRecordResponse{} }
func (m *AddUserAiInteractionRecordResponse) String() string { return proto.CompactTextString(m) }
func (*AddUserAiInteractionRecordResponse) ProtoMessage()    {}
func (*AddUserAiInteractionRecordResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{30}
}
func (m *AddUserAiInteractionRecordResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserAiInteractionRecordResponse.Unmarshal(m, b)
}
func (m *AddUserAiInteractionRecordResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserAiInteractionRecordResponse.Marshal(b, m, deterministic)
}
func (dst *AddUserAiInteractionRecordResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserAiInteractionRecordResponse.Merge(dst, src)
}
func (m *AddUserAiInteractionRecordResponse) XXX_Size() int {
	return xxx_messageInfo_AddUserAiInteractionRecordResponse.Size(m)
}
func (m *AddUserAiInteractionRecordResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserAiInteractionRecordResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserAiInteractionRecordResponse proto.InternalMessageInfo

type GetUserAiInteractionRecordRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAiInteractionRecordRequest) Reset()         { *m = GetUserAiInteractionRecordRequest{} }
func (m *GetUserAiInteractionRecordRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserAiInteractionRecordRequest) ProtoMessage()    {}
func (*GetUserAiInteractionRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{31}
}
func (m *GetUserAiInteractionRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAiInteractionRecordRequest.Unmarshal(m, b)
}
func (m *GetUserAiInteractionRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAiInteractionRecordRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserAiInteractionRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAiInteractionRecordRequest.Merge(dst, src)
}
func (m *GetUserAiInteractionRecordRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserAiInteractionRecordRequest.Size(m)
}
func (m *GetUserAiInteractionRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAiInteractionRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAiInteractionRecordRequest proto.InternalMessageInfo

func (m *GetUserAiInteractionRecordRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserAiInteractionRecordResponse struct {
	AiUidList            []uint32 `protobuf:"varint,1,rep,packed,name=ai_uid_list,json=aiUidList,proto3" json:"ai_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAiInteractionRecordResponse) Reset()         { *m = GetUserAiInteractionRecordResponse{} }
func (m *GetUserAiInteractionRecordResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserAiInteractionRecordResponse) ProtoMessage()    {}
func (*GetUserAiInteractionRecordResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{32}
}
func (m *GetUserAiInteractionRecordResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAiInteractionRecordResponse.Unmarshal(m, b)
}
func (m *GetUserAiInteractionRecordResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAiInteractionRecordResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserAiInteractionRecordResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAiInteractionRecordResponse.Merge(dst, src)
}
func (m *GetUserAiInteractionRecordResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserAiInteractionRecordResponse.Size(m)
}
func (m *GetUserAiInteractionRecordResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAiInteractionRecordResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAiInteractionRecordResponse proto.InternalMessageInfo

func (m *GetUserAiInteractionRecordResponse) GetAiUidList() []uint32 {
	if m != nil {
		return m.AiUidList
	}
	return nil
}

type AddAiLikePostRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAiLikePostRequest) Reset()         { *m = AddAiLikePostRequest{} }
func (m *AddAiLikePostRequest) String() string { return proto.CompactTextString(m) }
func (*AddAiLikePostRequest) ProtoMessage()    {}
func (*AddAiLikePostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{33}
}
func (m *AddAiLikePostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAiLikePostRequest.Unmarshal(m, b)
}
func (m *AddAiLikePostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAiLikePostRequest.Marshal(b, m, deterministic)
}
func (dst *AddAiLikePostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAiLikePostRequest.Merge(dst, src)
}
func (m *AddAiLikePostRequest) XXX_Size() int {
	return xxx_messageInfo_AddAiLikePostRequest.Size(m)
}
func (m *AddAiLikePostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAiLikePostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddAiLikePostRequest proto.InternalMessageInfo

func (m *AddAiLikePostRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddAiLikePostRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type AddAiLikePostResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAiLikePostResponse) Reset()         { *m = AddAiLikePostResponse{} }
func (m *AddAiLikePostResponse) String() string { return proto.CompactTextString(m) }
func (*AddAiLikePostResponse) ProtoMessage()    {}
func (*AddAiLikePostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{34}
}
func (m *AddAiLikePostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAiLikePostResponse.Unmarshal(m, b)
}
func (m *AddAiLikePostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAiLikePostResponse.Marshal(b, m, deterministic)
}
func (dst *AddAiLikePostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAiLikePostResponse.Merge(dst, src)
}
func (m *AddAiLikePostResponse) XXX_Size() int {
	return xxx_messageInfo_AddAiLikePostResponse.Size(m)
}
func (m *AddAiLikePostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAiLikePostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddAiLikePostResponse proto.InternalMessageInfo

type GetAiLikePostCountRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAiLikePostCountRequest) Reset()         { *m = GetAiLikePostCountRequest{} }
func (m *GetAiLikePostCountRequest) String() string { return proto.CompactTextString(m) }
func (*GetAiLikePostCountRequest) ProtoMessage()    {}
func (*GetAiLikePostCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{35}
}
func (m *GetAiLikePostCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAiLikePostCountRequest.Unmarshal(m, b)
}
func (m *GetAiLikePostCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAiLikePostCountRequest.Marshal(b, m, deterministic)
}
func (dst *GetAiLikePostCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAiLikePostCountRequest.Merge(dst, src)
}
func (m *GetAiLikePostCountRequest) XXX_Size() int {
	return xxx_messageInfo_GetAiLikePostCountRequest.Size(m)
}
func (m *GetAiLikePostCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAiLikePostCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAiLikePostCountRequest proto.InternalMessageInfo

func (m *GetAiLikePostCountRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAiLikePostCountResponse struct {
	LikePostCount        uint32   `protobuf:"varint,1,opt,name=like_post_count,json=likePostCount,proto3" json:"like_post_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAiLikePostCountResponse) Reset()         { *m = GetAiLikePostCountResponse{} }
func (m *GetAiLikePostCountResponse) String() string { return proto.CompactTextString(m) }
func (*GetAiLikePostCountResponse) ProtoMessage()    {}
func (*GetAiLikePostCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{36}
}
func (m *GetAiLikePostCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAiLikePostCountResponse.Unmarshal(m, b)
}
func (m *GetAiLikePostCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAiLikePostCountResponse.Marshal(b, m, deterministic)
}
func (dst *GetAiLikePostCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAiLikePostCountResponse.Merge(dst, src)
}
func (m *GetAiLikePostCountResponse) XXX_Size() int {
	return xxx_messageInfo_GetAiLikePostCountResponse.Size(m)
}
func (m *GetAiLikePostCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAiLikePostCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAiLikePostCountResponse proto.InternalMessageInfo

func (m *GetAiLikePostCountResponse) GetLikePostCount() uint32 {
	if m != nil {
		return m.LikePostCount
	}
	return 0
}

type AddAiCommentPostRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAiCommentPostRequest) Reset()         { *m = AddAiCommentPostRequest{} }
func (m *AddAiCommentPostRequest) String() string { return proto.CompactTextString(m) }
func (*AddAiCommentPostRequest) ProtoMessage()    {}
func (*AddAiCommentPostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{37}
}
func (m *AddAiCommentPostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAiCommentPostRequest.Unmarshal(m, b)
}
func (m *AddAiCommentPostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAiCommentPostRequest.Marshal(b, m, deterministic)
}
func (dst *AddAiCommentPostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAiCommentPostRequest.Merge(dst, src)
}
func (m *AddAiCommentPostRequest) XXX_Size() int {
	return xxx_messageInfo_AddAiCommentPostRequest.Size(m)
}
func (m *AddAiCommentPostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAiCommentPostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddAiCommentPostRequest proto.InternalMessageInfo

func (m *AddAiCommentPostRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddAiCommentPostRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type AddAiCommentPostResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAiCommentPostResponse) Reset()         { *m = AddAiCommentPostResponse{} }
func (m *AddAiCommentPostResponse) String() string { return proto.CompactTextString(m) }
func (*AddAiCommentPostResponse) ProtoMessage()    {}
func (*AddAiCommentPostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{38}
}
func (m *AddAiCommentPostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAiCommentPostResponse.Unmarshal(m, b)
}
func (m *AddAiCommentPostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAiCommentPostResponse.Marshal(b, m, deterministic)
}
func (dst *AddAiCommentPostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAiCommentPostResponse.Merge(dst, src)
}
func (m *AddAiCommentPostResponse) XXX_Size() int {
	return xxx_messageInfo_AddAiCommentPostResponse.Size(m)
}
func (m *AddAiCommentPostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAiCommentPostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddAiCommentPostResponse proto.InternalMessageInfo

type GetAiCommentPostCountRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAiCommentPostCountRequest) Reset()         { *m = GetAiCommentPostCountRequest{} }
func (m *GetAiCommentPostCountRequest) String() string { return proto.CompactTextString(m) }
func (*GetAiCommentPostCountRequest) ProtoMessage()    {}
func (*GetAiCommentPostCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{39}
}
func (m *GetAiCommentPostCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAiCommentPostCountRequest.Unmarshal(m, b)
}
func (m *GetAiCommentPostCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAiCommentPostCountRequest.Marshal(b, m, deterministic)
}
func (dst *GetAiCommentPostCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAiCommentPostCountRequest.Merge(dst, src)
}
func (m *GetAiCommentPostCountRequest) XXX_Size() int {
	return xxx_messageInfo_GetAiCommentPostCountRequest.Size(m)
}
func (m *GetAiCommentPostCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAiCommentPostCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAiCommentPostCountRequest proto.InternalMessageInfo

func (m *GetAiCommentPostCountRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAiCommentPostCountResponse struct {
	CommentPostCount     uint32   `protobuf:"varint,1,opt,name=comment_post_count,json=commentPostCount,proto3" json:"comment_post_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAiCommentPostCountResponse) Reset()         { *m = GetAiCommentPostCountResponse{} }
func (m *GetAiCommentPostCountResponse) String() string { return proto.CompactTextString(m) }
func (*GetAiCommentPostCountResponse) ProtoMessage()    {}
func (*GetAiCommentPostCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{40}
}
func (m *GetAiCommentPostCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAiCommentPostCountResponse.Unmarshal(m, b)
}
func (m *GetAiCommentPostCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAiCommentPostCountResponse.Marshal(b, m, deterministic)
}
func (dst *GetAiCommentPostCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAiCommentPostCountResponse.Merge(dst, src)
}
func (m *GetAiCommentPostCountResponse) XXX_Size() int {
	return xxx_messageInfo_GetAiCommentPostCountResponse.Size(m)
}
func (m *GetAiCommentPostCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAiCommentPostCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAiCommentPostCountResponse proto.InternalMessageInfo

func (m *GetAiCommentPostCountResponse) GetCommentPostCount() uint32 {
	if m != nil {
		return m.CommentPostCount
	}
	return 0
}

type AddPostAiCommentRecordRequest struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	AiUid                uint32   `protobuf:"varint,2,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPostAiCommentRecordRequest) Reset()         { *m = AddPostAiCommentRecordRequest{} }
func (m *AddPostAiCommentRecordRequest) String() string { return proto.CompactTextString(m) }
func (*AddPostAiCommentRecordRequest) ProtoMessage()    {}
func (*AddPostAiCommentRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{41}
}
func (m *AddPostAiCommentRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPostAiCommentRecordRequest.Unmarshal(m, b)
}
func (m *AddPostAiCommentRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPostAiCommentRecordRequest.Marshal(b, m, deterministic)
}
func (dst *AddPostAiCommentRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPostAiCommentRecordRequest.Merge(dst, src)
}
func (m *AddPostAiCommentRecordRequest) XXX_Size() int {
	return xxx_messageInfo_AddPostAiCommentRecordRequest.Size(m)
}
func (m *AddPostAiCommentRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPostAiCommentRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddPostAiCommentRecordRequest proto.InternalMessageInfo

func (m *AddPostAiCommentRecordRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AddPostAiCommentRecordRequest) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

type AddPostAiCommentRecordResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPostAiCommentRecordResponse) Reset()         { *m = AddPostAiCommentRecordResponse{} }
func (m *AddPostAiCommentRecordResponse) String() string { return proto.CompactTextString(m) }
func (*AddPostAiCommentRecordResponse) ProtoMessage()    {}
func (*AddPostAiCommentRecordResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{42}
}
func (m *AddPostAiCommentRecordResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPostAiCommentRecordResponse.Unmarshal(m, b)
}
func (m *AddPostAiCommentRecordResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPostAiCommentRecordResponse.Marshal(b, m, deterministic)
}
func (dst *AddPostAiCommentRecordResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPostAiCommentRecordResponse.Merge(dst, src)
}
func (m *AddPostAiCommentRecordResponse) XXX_Size() int {
	return xxx_messageInfo_AddPostAiCommentRecordResponse.Size(m)
}
func (m *AddPostAiCommentRecordResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPostAiCommentRecordResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddPostAiCommentRecordResponse proto.InternalMessageInfo

type GetPostAiCommentCountRequest struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	AiUid                uint32   `protobuf:"varint,2,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostAiCommentCountRequest) Reset()         { *m = GetPostAiCommentCountRequest{} }
func (m *GetPostAiCommentCountRequest) String() string { return proto.CompactTextString(m) }
func (*GetPostAiCommentCountRequest) ProtoMessage()    {}
func (*GetPostAiCommentCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{43}
}
func (m *GetPostAiCommentCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostAiCommentCountRequest.Unmarshal(m, b)
}
func (m *GetPostAiCommentCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostAiCommentCountRequest.Marshal(b, m, deterministic)
}
func (dst *GetPostAiCommentCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostAiCommentCountRequest.Merge(dst, src)
}
func (m *GetPostAiCommentCountRequest) XXX_Size() int {
	return xxx_messageInfo_GetPostAiCommentCountRequest.Size(m)
}
func (m *GetPostAiCommentCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostAiCommentCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostAiCommentCountRequest proto.InternalMessageInfo

func (m *GetPostAiCommentCountRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetPostAiCommentCountRequest) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

type GetPostAiCommentCountResponse struct {
	CommentCount         uint32   `protobuf:"varint,1,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostAiCommentCountResponse) Reset()         { *m = GetPostAiCommentCountResponse{} }
func (m *GetPostAiCommentCountResponse) String() string { return proto.CompactTextString(m) }
func (*GetPostAiCommentCountResponse) ProtoMessage()    {}
func (*GetPostAiCommentCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{44}
}
func (m *GetPostAiCommentCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostAiCommentCountResponse.Unmarshal(m, b)
}
func (m *GetPostAiCommentCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostAiCommentCountResponse.Marshal(b, m, deterministic)
}
func (dst *GetPostAiCommentCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostAiCommentCountResponse.Merge(dst, src)
}
func (m *GetPostAiCommentCountResponse) XXX_Size() int {
	return xxx_messageInfo_GetPostAiCommentCountResponse.Size(m)
}
func (m *GetPostAiCommentCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostAiCommentCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostAiCommentCountResponse proto.InternalMessageInfo

func (m *GetPostAiCommentCountResponse) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

type GetPostAiCommentListRequest struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPostAiCommentListRequest) Reset()         { *m = GetPostAiCommentListRequest{} }
func (m *GetPostAiCommentListRequest) String() string { return proto.CompactTextString(m) }
func (*GetPostAiCommentListRequest) ProtoMessage()    {}
func (*GetPostAiCommentListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{45}
}
func (m *GetPostAiCommentListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostAiCommentListRequest.Unmarshal(m, b)
}
func (m *GetPostAiCommentListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostAiCommentListRequest.Marshal(b, m, deterministic)
}
func (dst *GetPostAiCommentListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostAiCommentListRequest.Merge(dst, src)
}
func (m *GetPostAiCommentListRequest) XXX_Size() int {
	return xxx_messageInfo_GetPostAiCommentListRequest.Size(m)
}
func (m *GetPostAiCommentListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostAiCommentListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostAiCommentListRequest proto.InternalMessageInfo

func (m *GetPostAiCommentListRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type GetPostAiCommentListResponse struct {
	AiCommentList        []*AiCommentData `protobuf:"bytes,1,rep,name=ai_comment_list,json=aiCommentList,proto3" json:"ai_comment_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetPostAiCommentListResponse) Reset()         { *m = GetPostAiCommentListResponse{} }
func (m *GetPostAiCommentListResponse) String() string { return proto.CompactTextString(m) }
func (*GetPostAiCommentListResponse) ProtoMessage()    {}
func (*GetPostAiCommentListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{46}
}
func (m *GetPostAiCommentListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostAiCommentListResponse.Unmarshal(m, b)
}
func (m *GetPostAiCommentListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostAiCommentListResponse.Marshal(b, m, deterministic)
}
func (dst *GetPostAiCommentListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostAiCommentListResponse.Merge(dst, src)
}
func (m *GetPostAiCommentListResponse) XXX_Size() int {
	return xxx_messageInfo_GetPostAiCommentListResponse.Size(m)
}
func (m *GetPostAiCommentListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostAiCommentListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostAiCommentListResponse proto.InternalMessageInfo

func (m *GetPostAiCommentListResponse) GetAiCommentList() []*AiCommentData {
	if m != nil {
		return m.AiCommentList
	}
	return nil
}

type AiCommentData struct {
	AiUid                uint32   `protobuf:"varint,1,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	Num                  uint32   `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AiCommentData) Reset()         { *m = AiCommentData{} }
func (m *AiCommentData) String() string { return proto.CompactTextString(m) }
func (*AiCommentData) ProtoMessage()    {}
func (*AiCommentData) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{47}
}
func (m *AiCommentData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AiCommentData.Unmarshal(m, b)
}
func (m *AiCommentData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AiCommentData.Marshal(b, m, deterministic)
}
func (dst *AiCommentData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AiCommentData.Merge(dst, src)
}
func (m *AiCommentData) XXX_Size() int {
	return xxx_messageInfo_AiCommentData.Size(m)
}
func (m *AiCommentData) XXX_DiscardUnknown() {
	xxx_messageInfo_AiCommentData.DiscardUnknown(m)
}

var xxx_messageInfo_AiCommentData proto.InternalMessageInfo

func (m *AiCommentData) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

func (m *AiCommentData) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type GetAIAccountByGenderRequest struct {
	Sex                  int32    `protobuf:"varint,1,opt,name=sex,proto3" json:"sex,omitempty"`
	Limit                int32    `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAIAccountByGenderRequest) Reset()         { *m = GetAIAccountByGenderRequest{} }
func (m *GetAIAccountByGenderRequest) String() string { return proto.CompactTextString(m) }
func (*GetAIAccountByGenderRequest) ProtoMessage()    {}
func (*GetAIAccountByGenderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{48}
}
func (m *GetAIAccountByGenderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIAccountByGenderRequest.Unmarshal(m, b)
}
func (m *GetAIAccountByGenderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIAccountByGenderRequest.Marshal(b, m, deterministic)
}
func (dst *GetAIAccountByGenderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIAccountByGenderRequest.Merge(dst, src)
}
func (m *GetAIAccountByGenderRequest) XXX_Size() int {
	return xxx_messageInfo_GetAIAccountByGenderRequest.Size(m)
}
func (m *GetAIAccountByGenderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIAccountByGenderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIAccountByGenderRequest proto.InternalMessageInfo

func (m *GetAIAccountByGenderRequest) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GetAIAccountByGenderRequest) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetAIAccountByGenderResponse struct {
	AccountList          []*AIAccount `protobuf:"bytes,1,rep,name=account_list,json=accountList,proto3" json:"account_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAIAccountByGenderResponse) Reset()         { *m = GetAIAccountByGenderResponse{} }
func (m *GetAIAccountByGenderResponse) String() string { return proto.CompactTextString(m) }
func (*GetAIAccountByGenderResponse) ProtoMessage()    {}
func (*GetAIAccountByGenderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{49}
}
func (m *GetAIAccountByGenderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIAccountByGenderResponse.Unmarshal(m, b)
}
func (m *GetAIAccountByGenderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIAccountByGenderResponse.Marshal(b, m, deterministic)
}
func (dst *GetAIAccountByGenderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIAccountByGenderResponse.Merge(dst, src)
}
func (m *GetAIAccountByGenderResponse) XXX_Size() int {
	return xxx_messageInfo_GetAIAccountByGenderResponse.Size(m)
}
func (m *GetAIAccountByGenderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIAccountByGenderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIAccountByGenderResponse proto.InternalMessageInfo

func (m *GetAIAccountByGenderResponse) GetAccountList() []*AIAccount {
	if m != nil {
		return m.AccountList
	}
	return nil
}

// 房间基础配置
type RoomBaseCfg struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ChannelAvatar        string   `protobuf:"bytes,3,opt,name=channel_avatar,json=channelAvatar,proto3" json:"channel_avatar,omitempty"`
	ChannelName          string   `protobuf:"bytes,4,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	WelcomeMsg           string   `protobuf:"bytes,5,opt,name=welcome_msg,json=welcomeMsg,proto3" json:"welcome_msg,omitempty"`
	ChannelTopicTitle    string   `protobuf:"bytes,6,opt,name=channel_topic_title,json=channelTopicTitle,proto3" json:"channel_topic_title,omitempty"`
	ChannelTopicDetail   string   `protobuf:"bytes,7,opt,name=channel_topic_detail,json=channelTopicDetail,proto3" json:"channel_topic_detail,omitempty"`
	ChannelSupervisor    uint32   `protobuf:"varint,8,opt,name=channel_supervisor,json=channelSupervisor,proto3" json:"channel_supervisor,omitempty"`
	ChannelAdmin         []uint32 `protobuf:"varint,9,rep,packed,name=channel_admin,json=channelAdmin,proto3" json:"channel_admin,omitempty"`
	LockMicList          []uint32 `protobuf:"varint,10,rep,packed,name=lock_mic_list,json=lockMicList,proto3" json:"lock_mic_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoomBaseCfg) Reset()         { *m = RoomBaseCfg{} }
func (m *RoomBaseCfg) String() string { return proto.CompactTextString(m) }
func (*RoomBaseCfg) ProtoMessage()    {}
func (*RoomBaseCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{50}
}
func (m *RoomBaseCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoomBaseCfg.Unmarshal(m, b)
}
func (m *RoomBaseCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoomBaseCfg.Marshal(b, m, deterministic)
}
func (dst *RoomBaseCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoomBaseCfg.Merge(dst, src)
}
func (m *RoomBaseCfg) XXX_Size() int {
	return xxx_messageInfo_RoomBaseCfg.Size(m)
}
func (m *RoomBaseCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_RoomBaseCfg.DiscardUnknown(m)
}

var xxx_messageInfo_RoomBaseCfg proto.InternalMessageInfo

func (m *RoomBaseCfg) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *RoomBaseCfg) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *RoomBaseCfg) GetChannelAvatar() string {
	if m != nil {
		return m.ChannelAvatar
	}
	return ""
}

func (m *RoomBaseCfg) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *RoomBaseCfg) GetWelcomeMsg() string {
	if m != nil {
		return m.WelcomeMsg
	}
	return ""
}

func (m *RoomBaseCfg) GetChannelTopicTitle() string {
	if m != nil {
		return m.ChannelTopicTitle
	}
	return ""
}

func (m *RoomBaseCfg) GetChannelTopicDetail() string {
	if m != nil {
		return m.ChannelTopicDetail
	}
	return ""
}

func (m *RoomBaseCfg) GetChannelSupervisor() uint32 {
	if m != nil {
		return m.ChannelSupervisor
	}
	return 0
}

func (m *RoomBaseCfg) GetChannelAdmin() []uint32 {
	if m != nil {
		return m.ChannelAdmin
	}
	return nil
}

func (m *RoomBaseCfg) GetLockMicList() []uint32 {
	if m != nil {
		return m.LockMicList
	}
	return nil
}

type BlockOption struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemId               uint32   `protobuf:"varint,2,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	ElemVal              string   `protobuf:"bytes,3,opt,name=elem_val,json=elemVal,proto3" json:"elem_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlockOption) Reset()         { *m = BlockOption{} }
func (m *BlockOption) String() string { return proto.CompactTextString(m) }
func (*BlockOption) ProtoMessage()    {}
func (*BlockOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{51}
}
func (m *BlockOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOption.Unmarshal(m, b)
}
func (m *BlockOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOption.Marshal(b, m, deterministic)
}
func (dst *BlockOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOption.Merge(dst, src)
}
func (m *BlockOption) XXX_Size() int {
	return xxx_messageInfo_BlockOption.Size(m)
}
func (m *BlockOption) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOption.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOption proto.InternalMessageInfo

func (m *BlockOption) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *BlockOption) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

func (m *BlockOption) GetElemVal() string {
	if m != nil {
		return m.ElemVal
	}
	return ""
}

type TimeRange struct {
	StartTime            int64    `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeRange) Reset()         { *m = TimeRange{} }
func (m *TimeRange) String() string { return proto.CompactTextString(m) }
func (*TimeRange) ProtoMessage()    {}
func (*TimeRange) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{52}
}
func (m *TimeRange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeRange.Unmarshal(m, b)
}
func (m *TimeRange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeRange.Marshal(b, m, deterministic)
}
func (dst *TimeRange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeRange.Merge(dst, src)
}
func (m *TimeRange) XXX_Size() int {
	return xxx_messageInfo_TimeRange.Size(m)
}
func (m *TimeRange) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeRange.DiscardUnknown(m)
}

var xxx_messageInfo_TimeRange proto.InternalMessageInfo

func (m *TimeRange) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *TimeRange) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

// 业务配置信息
type BusinessCfg struct {
	AiUseMicIndex        uint32         `protobuf:"varint,1,opt,name=ai_use_mic_index,json=aiUseMicIndex,proto3" json:"ai_use_mic_index,omitempty"`
	AutoDismissUserNum   uint32         `protobuf:"varint,2,opt,name=auto_dismiss_user_num,json=autoDismissUserNum,proto3" json:"auto_dismiss_user_num,omitempty"`
	AutoPublishUserNum   uint32         `protobuf:"varint,3,opt,name=auto_publish_user_num,json=autoPublishUserNum,proto3" json:"auto_publish_user_num,omitempty"`
	OperationTimes       []*TimeRange   `protobuf:"bytes,4,rep,name=operation_times,json=operationTimes,proto3" json:"operation_times,omitempty"`
	PublishTimes         []*TimeRange   `protobuf:"bytes,5,rep,name=publish_times,json=publishTimes,proto3" json:"publish_times,omitempty"`
	ChannelBgm           string         `protobuf:"bytes,6,opt,name=channel_bgm,json=channelBgm,proto3" json:"channel_bgm,omitempty"`
	PublishOptions       []*BlockOption `protobuf:"bytes,7,rep,name=publish_options,json=publishOptions,proto3" json:"publish_options,omitempty"`
	PromptId             uint32         `protobuf:"varint,8,opt,name=prompt_id,json=promptId,proto3" json:"prompt_id,omitempty"`
	ReceptionMsgList     []string       `protobuf:"bytes,9,rep,name=reception_msg_list,json=receptionMsgList,proto3" json:"reception_msg_list,omitempty"`
	Script               []string       `protobuf:"bytes,10,rep,name=script,proto3" json:"script,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BusinessCfg) Reset()         { *m = BusinessCfg{} }
func (m *BusinessCfg) String() string { return proto.CompactTextString(m) }
func (*BusinessCfg) ProtoMessage()    {}
func (*BusinessCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{53}
}
func (m *BusinessCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BusinessCfg.Unmarshal(m, b)
}
func (m *BusinessCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BusinessCfg.Marshal(b, m, deterministic)
}
func (dst *BusinessCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BusinessCfg.Merge(dst, src)
}
func (m *BusinessCfg) XXX_Size() int {
	return xxx_messageInfo_BusinessCfg.Size(m)
}
func (m *BusinessCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_BusinessCfg.DiscardUnknown(m)
}

var xxx_messageInfo_BusinessCfg proto.InternalMessageInfo

func (m *BusinessCfg) GetAiUseMicIndex() uint32 {
	if m != nil {
		return m.AiUseMicIndex
	}
	return 0
}

func (m *BusinessCfg) GetAutoDismissUserNum() uint32 {
	if m != nil {
		return m.AutoDismissUserNum
	}
	return 0
}

func (m *BusinessCfg) GetAutoPublishUserNum() uint32 {
	if m != nil {
		return m.AutoPublishUserNum
	}
	return 0
}

func (m *BusinessCfg) GetOperationTimes() []*TimeRange {
	if m != nil {
		return m.OperationTimes
	}
	return nil
}

func (m *BusinessCfg) GetPublishTimes() []*TimeRange {
	if m != nil {
		return m.PublishTimes
	}
	return nil
}

func (m *BusinessCfg) GetChannelBgm() string {
	if m != nil {
		return m.ChannelBgm
	}
	return ""
}

func (m *BusinessCfg) GetPublishOptions() []*BlockOption {
	if m != nil {
		return m.PublishOptions
	}
	return nil
}

func (m *BusinessCfg) GetPromptId() uint32 {
	if m != nil {
		return m.PromptId
	}
	return 0
}

func (m *BusinessCfg) GetReceptionMsgList() []string {
	if m != nil {
		return m.ReceptionMsgList
	}
	return nil
}

func (m *BusinessCfg) GetScript() []string {
	if m != nil {
		return m.Script
	}
	return nil
}

type AiRoomCfg struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BaseCfg              *RoomBaseCfg `protobuf:"bytes,2,opt,name=base_cfg,json=baseCfg,proto3" json:"base_cfg,omitempty"`
	BusinessCfg          *BusinessCfg `protobuf:"bytes,3,opt,name=business_cfg,json=businessCfg,proto3" json:"business_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AiRoomCfg) Reset()         { *m = AiRoomCfg{} }
func (m *AiRoomCfg) String() string { return proto.CompactTextString(m) }
func (*AiRoomCfg) ProtoMessage()    {}
func (*AiRoomCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{54}
}
func (m *AiRoomCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AiRoomCfg.Unmarshal(m, b)
}
func (m *AiRoomCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AiRoomCfg.Marshal(b, m, deterministic)
}
func (dst *AiRoomCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AiRoomCfg.Merge(dst, src)
}
func (m *AiRoomCfg) XXX_Size() int {
	return xxx_messageInfo_AiRoomCfg.Size(m)
}
func (m *AiRoomCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_AiRoomCfg.DiscardUnknown(m)
}

var xxx_messageInfo_AiRoomCfg proto.InternalMessageInfo

func (m *AiRoomCfg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AiRoomCfg) GetBaseCfg() *RoomBaseCfg {
	if m != nil {
		return m.BaseCfg
	}
	return nil
}

func (m *AiRoomCfg) GetBusinessCfg() *BusinessCfg {
	if m != nil {
		return m.BusinessCfg
	}
	return nil
}

type UpdateAiRoomCfgRequest struct {
	RoomCfg              *AiRoomCfg `protobuf:"bytes,1,opt,name=room_cfg,json=roomCfg,proto3" json:"room_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UpdateAiRoomCfgRequest) Reset()         { *m = UpdateAiRoomCfgRequest{} }
func (m *UpdateAiRoomCfgRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateAiRoomCfgRequest) ProtoMessage()    {}
func (*UpdateAiRoomCfgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{55}
}
func (m *UpdateAiRoomCfgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAiRoomCfgRequest.Unmarshal(m, b)
}
func (m *UpdateAiRoomCfgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAiRoomCfgRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateAiRoomCfgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAiRoomCfgRequest.Merge(dst, src)
}
func (m *UpdateAiRoomCfgRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateAiRoomCfgRequest.Size(m)
}
func (m *UpdateAiRoomCfgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAiRoomCfgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAiRoomCfgRequest proto.InternalMessageInfo

func (m *UpdateAiRoomCfgRequest) GetRoomCfg() *AiRoomCfg {
	if m != nil {
		return m.RoomCfg
	}
	return nil
}

type UpdateAiRoomCfgResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAiRoomCfgResponse) Reset()         { *m = UpdateAiRoomCfgResponse{} }
func (m *UpdateAiRoomCfgResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateAiRoomCfgResponse) ProtoMessage()    {}
func (*UpdateAiRoomCfgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{56}
}
func (m *UpdateAiRoomCfgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAiRoomCfgResponse.Unmarshal(m, b)
}
func (m *UpdateAiRoomCfgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAiRoomCfgResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateAiRoomCfgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAiRoomCfgResponse.Merge(dst, src)
}
func (m *UpdateAiRoomCfgResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateAiRoomCfgResponse.Size(m)
}
func (m *UpdateAiRoomCfgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAiRoomCfgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAiRoomCfgResponse proto.InternalMessageInfo

type GetAiRoomCfgRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAiRoomCfgRequest) Reset()         { *m = GetAiRoomCfgRequest{} }
func (m *GetAiRoomCfgRequest) String() string { return proto.CompactTextString(m) }
func (*GetAiRoomCfgRequest) ProtoMessage()    {}
func (*GetAiRoomCfgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{57}
}
func (m *GetAiRoomCfgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAiRoomCfgRequest.Unmarshal(m, b)
}
func (m *GetAiRoomCfgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAiRoomCfgRequest.Marshal(b, m, deterministic)
}
func (dst *GetAiRoomCfgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAiRoomCfgRequest.Merge(dst, src)
}
func (m *GetAiRoomCfgRequest) XXX_Size() int {
	return xxx_messageInfo_GetAiRoomCfgRequest.Size(m)
}
func (m *GetAiRoomCfgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAiRoomCfgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAiRoomCfgRequest proto.InternalMessageInfo

func (m *GetAiRoomCfgRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAiRoomCfgResponse struct {
	RoomCfg              *AiRoomCfg `protobuf:"bytes,1,opt,name=room_cfg,json=roomCfg,proto3" json:"room_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAiRoomCfgResponse) Reset()         { *m = GetAiRoomCfgResponse{} }
func (m *GetAiRoomCfgResponse) String() string { return proto.CompactTextString(m) }
func (*GetAiRoomCfgResponse) ProtoMessage()    {}
func (*GetAiRoomCfgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{58}
}
func (m *GetAiRoomCfgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAiRoomCfgResponse.Unmarshal(m, b)
}
func (m *GetAiRoomCfgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAiRoomCfgResponse.Marshal(b, m, deterministic)
}
func (dst *GetAiRoomCfgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAiRoomCfgResponse.Merge(dst, src)
}
func (m *GetAiRoomCfgResponse) XXX_Size() int {
	return xxx_messageInfo_GetAiRoomCfgResponse.Size(m)
}
func (m *GetAiRoomCfgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAiRoomCfgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAiRoomCfgResponse proto.InternalMessageInfo

func (m *GetAiRoomCfgResponse) GetRoomCfg() *AiRoomCfg {
	if m != nil {
		return m.RoomCfg
	}
	return nil
}

// 运营时间中的房间配置信息
type GetRunningAiRoomsRequest struct {
	LastCid              uint32   `protobuf:"varint,1,opt,name=last_cid,json=lastCid,proto3" json:"last_cid,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRunningAiRoomsRequest) Reset()         { *m = GetRunningAiRoomsRequest{} }
func (m *GetRunningAiRoomsRequest) String() string { return proto.CompactTextString(m) }
func (*GetRunningAiRoomsRequest) ProtoMessage()    {}
func (*GetRunningAiRoomsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{59}
}
func (m *GetRunningAiRoomsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRunningAiRoomsRequest.Unmarshal(m, b)
}
func (m *GetRunningAiRoomsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRunningAiRoomsRequest.Marshal(b, m, deterministic)
}
func (dst *GetRunningAiRoomsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRunningAiRoomsRequest.Merge(dst, src)
}
func (m *GetRunningAiRoomsRequest) XXX_Size() int {
	return xxx_messageInfo_GetRunningAiRoomsRequest.Size(m)
}
func (m *GetRunningAiRoomsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRunningAiRoomsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRunningAiRoomsRequest proto.InternalMessageInfo

func (m *GetRunningAiRoomsRequest) GetLastCid() uint32 {
	if m != nil {
		return m.LastCid
	}
	return 0
}

func (m *GetRunningAiRoomsRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetRunningAiRoomsResponse struct {
	RoomCfgList          []*AiRoomCfg `protobuf:"bytes,1,rep,name=room_cfg_list,json=roomCfgList,proto3" json:"room_cfg_list,omitempty"`
	LoadFinish           bool         `protobuf:"varint,2,opt,name=load_finish,json=loadFinish,proto3" json:"load_finish,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRunningAiRoomsResponse) Reset()         { *m = GetRunningAiRoomsResponse{} }
func (m *GetRunningAiRoomsResponse) String() string { return proto.CompactTextString(m) }
func (*GetRunningAiRoomsResponse) ProtoMessage()    {}
func (*GetRunningAiRoomsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{60}
}
func (m *GetRunningAiRoomsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRunningAiRoomsResponse.Unmarshal(m, b)
}
func (m *GetRunningAiRoomsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRunningAiRoomsResponse.Marshal(b, m, deterministic)
}
func (dst *GetRunningAiRoomsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRunningAiRoomsResponse.Merge(dst, src)
}
func (m *GetRunningAiRoomsResponse) XXX_Size() int {
	return xxx_messageInfo_GetRunningAiRoomsResponse.Size(m)
}
func (m *GetRunningAiRoomsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRunningAiRoomsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRunningAiRoomsResponse proto.InternalMessageInfo

func (m *GetRunningAiRoomsResponse) GetRoomCfgList() []*AiRoomCfg {
	if m != nil {
		return m.RoomCfgList
	}
	return nil
}

func (m *GetRunningAiRoomsResponse) GetLoadFinish() bool {
	if m != nil {
		return m.LoadFinish
	}
	return false
}

type RecordReceptionTimeRequest struct {
	AiUid                uint32   `protobuf:"varint,1,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ReceptionTime        int64    `protobuf:"varint,3,opt,name=reception_time,json=receptionTime,proto3" json:"reception_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordReceptionTimeRequest) Reset()         { *m = RecordReceptionTimeRequest{} }
func (m *RecordReceptionTimeRequest) String() string { return proto.CompactTextString(m) }
func (*RecordReceptionTimeRequest) ProtoMessage()    {}
func (*RecordReceptionTimeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{61}
}
func (m *RecordReceptionTimeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordReceptionTimeRequest.Unmarshal(m, b)
}
func (m *RecordReceptionTimeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordReceptionTimeRequest.Marshal(b, m, deterministic)
}
func (dst *RecordReceptionTimeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordReceptionTimeRequest.Merge(dst, src)
}
func (m *RecordReceptionTimeRequest) XXX_Size() int {
	return xxx_messageInfo_RecordReceptionTimeRequest.Size(m)
}
func (m *RecordReceptionTimeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordReceptionTimeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RecordReceptionTimeRequest proto.InternalMessageInfo

func (m *RecordReceptionTimeRequest) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

func (m *RecordReceptionTimeRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecordReceptionTimeRequest) GetReceptionTime() int64 {
	if m != nil {
		return m.ReceptionTime
	}
	return 0
}

type RecordReceptionTimeResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordReceptionTimeResponse) Reset()         { *m = RecordReceptionTimeResponse{} }
func (m *RecordReceptionTimeResponse) String() string { return proto.CompactTextString(m) }
func (*RecordReceptionTimeResponse) ProtoMessage()    {}
func (*RecordReceptionTimeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{62}
}
func (m *RecordReceptionTimeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordReceptionTimeResponse.Unmarshal(m, b)
}
func (m *RecordReceptionTimeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordReceptionTimeResponse.Marshal(b, m, deterministic)
}
func (dst *RecordReceptionTimeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordReceptionTimeResponse.Merge(dst, src)
}
func (m *RecordReceptionTimeResponse) XXX_Size() int {
	return xxx_messageInfo_RecordReceptionTimeResponse.Size(m)
}
func (m *RecordReceptionTimeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordReceptionTimeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RecordReceptionTimeResponse proto.InternalMessageInfo

type GetLastReceptionTimeRequest struct {
	AiUid                uint32   `protobuf:"varint,1,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastReceptionTimeRequest) Reset()         { *m = GetLastReceptionTimeRequest{} }
func (m *GetLastReceptionTimeRequest) String() string { return proto.CompactTextString(m) }
func (*GetLastReceptionTimeRequest) ProtoMessage()    {}
func (*GetLastReceptionTimeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{63}
}
func (m *GetLastReceptionTimeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastReceptionTimeRequest.Unmarshal(m, b)
}
func (m *GetLastReceptionTimeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastReceptionTimeRequest.Marshal(b, m, deterministic)
}
func (dst *GetLastReceptionTimeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastReceptionTimeRequest.Merge(dst, src)
}
func (m *GetLastReceptionTimeRequest) XXX_Size() int {
	return xxx_messageInfo_GetLastReceptionTimeRequest.Size(m)
}
func (m *GetLastReceptionTimeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastReceptionTimeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastReceptionTimeRequest proto.InternalMessageInfo

func (m *GetLastReceptionTimeRequest) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

func (m *GetLastReceptionTimeRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetLastReceptionTimeResponse struct {
	LastReceptionTime    int64    `protobuf:"varint,1,opt,name=last_reception_time,json=lastReceptionTime,proto3" json:"last_reception_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastReceptionTimeResponse) Reset()         { *m = GetLastReceptionTimeResponse{} }
func (m *GetLastReceptionTimeResponse) String() string { return proto.CompactTextString(m) }
func (*GetLastReceptionTimeResponse) ProtoMessage()    {}
func (*GetLastReceptionTimeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{64}
}
func (m *GetLastReceptionTimeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastReceptionTimeResponse.Unmarshal(m, b)
}
func (m *GetLastReceptionTimeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastReceptionTimeResponse.Marshal(b, m, deterministic)
}
func (dst *GetLastReceptionTimeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastReceptionTimeResponse.Merge(dst, src)
}
func (m *GetLastReceptionTimeResponse) XXX_Size() int {
	return xxx_messageInfo_GetLastReceptionTimeResponse.Size(m)
}
func (m *GetLastReceptionTimeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastReceptionTimeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastReceptionTimeResponse proto.InternalMessageInfo

func (m *GetLastReceptionTimeResponse) GetLastReceptionTime() int64 {
	if m != nil {
		return m.LastReceptionTime
	}
	return 0
}

type RecordWaitingReceptionUserRequest struct {
	AiUid                uint32   `protobuf:"varint,1,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	RecordTime           int64    `protobuf:"varint,4,opt,name=record_time,json=recordTime,proto3" json:"record_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordWaitingReceptionUserRequest) Reset()         { *m = RecordWaitingReceptionUserRequest{} }
func (m *RecordWaitingReceptionUserRequest) String() string { return proto.CompactTextString(m) }
func (*RecordWaitingReceptionUserRequest) ProtoMessage()    {}
func (*RecordWaitingReceptionUserRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{65}
}
func (m *RecordWaitingReceptionUserRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordWaitingReceptionUserRequest.Unmarshal(m, b)
}
func (m *RecordWaitingReceptionUserRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordWaitingReceptionUserRequest.Marshal(b, m, deterministic)
}
func (dst *RecordWaitingReceptionUserRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordWaitingReceptionUserRequest.Merge(dst, src)
}
func (m *RecordWaitingReceptionUserRequest) XXX_Size() int {
	return xxx_messageInfo_RecordWaitingReceptionUserRequest.Size(m)
}
func (m *RecordWaitingReceptionUserRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordWaitingReceptionUserRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RecordWaitingReceptionUserRequest proto.InternalMessageInfo

func (m *RecordWaitingReceptionUserRequest) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

func (m *RecordWaitingReceptionUserRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecordWaitingReceptionUserRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecordWaitingReceptionUserRequest) GetRecordTime() int64 {
	if m != nil {
		return m.RecordTime
	}
	return 0
}

type RecordWaitingReceptionUserResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordWaitingReceptionUserResponse) Reset()         { *m = RecordWaitingReceptionUserResponse{} }
func (m *RecordWaitingReceptionUserResponse) String() string { return proto.CompactTextString(m) }
func (*RecordWaitingReceptionUserResponse) ProtoMessage()    {}
func (*RecordWaitingReceptionUserResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{66}
}
func (m *RecordWaitingReceptionUserResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordWaitingReceptionUserResponse.Unmarshal(m, b)
}
func (m *RecordWaitingReceptionUserResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordWaitingReceptionUserResponse.Marshal(b, m, deterministic)
}
func (dst *RecordWaitingReceptionUserResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordWaitingReceptionUserResponse.Merge(dst, src)
}
func (m *RecordWaitingReceptionUserResponse) XXX_Size() int {
	return xxx_messageInfo_RecordWaitingReceptionUserResponse.Size(m)
}
func (m *RecordWaitingReceptionUserResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordWaitingReceptionUserResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RecordWaitingReceptionUserResponse proto.InternalMessageInfo

type GetWaitingReceptionUserRequest struct {
	AiUid                uint32   `protobuf:"varint,1,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWaitingReceptionUserRequest) Reset()         { *m = GetWaitingReceptionUserRequest{} }
func (m *GetWaitingReceptionUserRequest) String() string { return proto.CompactTextString(m) }
func (*GetWaitingReceptionUserRequest) ProtoMessage()    {}
func (*GetWaitingReceptionUserRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{67}
}
func (m *GetWaitingReceptionUserRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWaitingReceptionUserRequest.Unmarshal(m, b)
}
func (m *GetWaitingReceptionUserRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWaitingReceptionUserRequest.Marshal(b, m, deterministic)
}
func (dst *GetWaitingReceptionUserRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWaitingReceptionUserRequest.Merge(dst, src)
}
func (m *GetWaitingReceptionUserRequest) XXX_Size() int {
	return xxx_messageInfo_GetWaitingReceptionUserRequest.Size(m)
}
func (m *GetWaitingReceptionUserRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWaitingReceptionUserRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWaitingReceptionUserRequest proto.InternalMessageInfo

func (m *GetWaitingReceptionUserRequest) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

func (m *GetWaitingReceptionUserRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetWaitingReceptionUserResponse struct {
	WaitingUserList      []*WaitingReceptionUser `protobuf:"bytes,1,rep,name=waiting_user_list,json=waitingUserList,proto3" json:"waiting_user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetWaitingReceptionUserResponse) Reset()         { *m = GetWaitingReceptionUserResponse{} }
func (m *GetWaitingReceptionUserResponse) String() string { return proto.CompactTextString(m) }
func (*GetWaitingReceptionUserResponse) ProtoMessage()    {}
func (*GetWaitingReceptionUserResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{68}
}
func (m *GetWaitingReceptionUserResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWaitingReceptionUserResponse.Unmarshal(m, b)
}
func (m *GetWaitingReceptionUserResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWaitingReceptionUserResponse.Marshal(b, m, deterministic)
}
func (dst *GetWaitingReceptionUserResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWaitingReceptionUserResponse.Merge(dst, src)
}
func (m *GetWaitingReceptionUserResponse) XXX_Size() int {
	return xxx_messageInfo_GetWaitingReceptionUserResponse.Size(m)
}
func (m *GetWaitingReceptionUserResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWaitingReceptionUserResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWaitingReceptionUserResponse proto.InternalMessageInfo

func (m *GetWaitingReceptionUserResponse) GetWaitingUserList() []*WaitingReceptionUser {
	if m != nil {
		return m.WaitingUserList
	}
	return nil
}

type WaitingReceptionUser struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RecordTime           int64    `protobuf:"varint,2,opt,name=record_time,json=recordTime,proto3" json:"record_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WaitingReceptionUser) Reset()         { *m = WaitingReceptionUser{} }
func (m *WaitingReceptionUser) String() string { return proto.CompactTextString(m) }
func (*WaitingReceptionUser) ProtoMessage()    {}
func (*WaitingReceptionUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{69}
}
func (m *WaitingReceptionUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WaitingReceptionUser.Unmarshal(m, b)
}
func (m *WaitingReceptionUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WaitingReceptionUser.Marshal(b, m, deterministic)
}
func (dst *WaitingReceptionUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WaitingReceptionUser.Merge(dst, src)
}
func (m *WaitingReceptionUser) XXX_Size() int {
	return xxx_messageInfo_WaitingReceptionUser.Size(m)
}
func (m *WaitingReceptionUser) XXX_DiscardUnknown() {
	xxx_messageInfo_WaitingReceptionUser.DiscardUnknown(m)
}

var xxx_messageInfo_WaitingReceptionUser proto.InternalMessageInfo

func (m *WaitingReceptionUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *WaitingReceptionUser) GetRecordTime() int64 {
	if m != nil {
		return m.RecordTime
	}
	return 0
}

type RecordReceptionUserRequest struct {
	AiUid                uint32   `protobuf:"varint,1,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordReceptionUserRequest) Reset()         { *m = RecordReceptionUserRequest{} }
func (m *RecordReceptionUserRequest) String() string { return proto.CompactTextString(m) }
func (*RecordReceptionUserRequest) ProtoMessage()    {}
func (*RecordReceptionUserRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{70}
}
func (m *RecordReceptionUserRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordReceptionUserRequest.Unmarshal(m, b)
}
func (m *RecordReceptionUserRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordReceptionUserRequest.Marshal(b, m, deterministic)
}
func (dst *RecordReceptionUserRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordReceptionUserRequest.Merge(dst, src)
}
func (m *RecordReceptionUserRequest) XXX_Size() int {
	return xxx_messageInfo_RecordReceptionUserRequest.Size(m)
}
func (m *RecordReceptionUserRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordReceptionUserRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RecordReceptionUserRequest proto.InternalMessageInfo

func (m *RecordReceptionUserRequest) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

func (m *RecordReceptionUserRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecordReceptionUserRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type RecordReceptionUserResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordReceptionUserResponse) Reset()         { *m = RecordReceptionUserResponse{} }
func (m *RecordReceptionUserResponse) String() string { return proto.CompactTextString(m) }
func (*RecordReceptionUserResponse) ProtoMessage()    {}
func (*RecordReceptionUserResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{71}
}
func (m *RecordReceptionUserResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordReceptionUserResponse.Unmarshal(m, b)
}
func (m *RecordReceptionUserResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordReceptionUserResponse.Marshal(b, m, deterministic)
}
func (dst *RecordReceptionUserResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordReceptionUserResponse.Merge(dst, src)
}
func (m *RecordReceptionUserResponse) XXX_Size() int {
	return xxx_messageInfo_RecordReceptionUserResponse.Size(m)
}
func (m *RecordReceptionUserResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordReceptionUserResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RecordReceptionUserResponse proto.InternalMessageInfo

type CheckReceptionUserRequest struct {
	AiUid                uint32   `protobuf:"varint,1,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckReceptionUserRequest) Reset()         { *m = CheckReceptionUserRequest{} }
func (m *CheckReceptionUserRequest) String() string { return proto.CompactTextString(m) }
func (*CheckReceptionUserRequest) ProtoMessage()    {}
func (*CheckReceptionUserRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{72}
}
func (m *CheckReceptionUserRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckReceptionUserRequest.Unmarshal(m, b)
}
func (m *CheckReceptionUserRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckReceptionUserRequest.Marshal(b, m, deterministic)
}
func (dst *CheckReceptionUserRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckReceptionUserRequest.Merge(dst, src)
}
func (m *CheckReceptionUserRequest) XXX_Size() int {
	return xxx_messageInfo_CheckReceptionUserRequest.Size(m)
}
func (m *CheckReceptionUserRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckReceptionUserRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckReceptionUserRequest proto.InternalMessageInfo

func (m *CheckReceptionUserRequest) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

func (m *CheckReceptionUserRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckReceptionUserRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type CheckReceptionUserResponse struct {
	ReceptionMap         map[uint32]bool `protobuf:"bytes,1,rep,name=reception_map,json=receptionMap,proto3" json:"reception_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CheckReceptionUserResponse) Reset()         { *m = CheckReceptionUserResponse{} }
func (m *CheckReceptionUserResponse) String() string { return proto.CompactTextString(m) }
func (*CheckReceptionUserResponse) ProtoMessage()    {}
func (*CheckReceptionUserResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{73}
}
func (m *CheckReceptionUserResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckReceptionUserResponse.Unmarshal(m, b)
}
func (m *CheckReceptionUserResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckReceptionUserResponse.Marshal(b, m, deterministic)
}
func (dst *CheckReceptionUserResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckReceptionUserResponse.Merge(dst, src)
}
func (m *CheckReceptionUserResponse) XXX_Size() int {
	return xxx_messageInfo_CheckReceptionUserResponse.Size(m)
}
func (m *CheckReceptionUserResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckReceptionUserResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckReceptionUserResponse proto.InternalMessageInfo

func (m *CheckReceptionUserResponse) GetReceptionMap() map[uint32]bool {
	if m != nil {
		return m.ReceptionMap
	}
	return nil
}

type RecordRoomUserNumRequest struct {
	AiUid                uint32   `protobuf:"varint,1,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UserNum              uint32   `protobuf:"varint,3,opt,name=user_num,json=userNum,proto3" json:"user_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordRoomUserNumRequest) Reset()         { *m = RecordRoomUserNumRequest{} }
func (m *RecordRoomUserNumRequest) String() string { return proto.CompactTextString(m) }
func (*RecordRoomUserNumRequest) ProtoMessage()    {}
func (*RecordRoomUserNumRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{74}
}
func (m *RecordRoomUserNumRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordRoomUserNumRequest.Unmarshal(m, b)
}
func (m *RecordRoomUserNumRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordRoomUserNumRequest.Marshal(b, m, deterministic)
}
func (dst *RecordRoomUserNumRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordRoomUserNumRequest.Merge(dst, src)
}
func (m *RecordRoomUserNumRequest) XXX_Size() int {
	return xxx_messageInfo_RecordRoomUserNumRequest.Size(m)
}
func (m *RecordRoomUserNumRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordRoomUserNumRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RecordRoomUserNumRequest proto.InternalMessageInfo

func (m *RecordRoomUserNumRequest) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

func (m *RecordRoomUserNumRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecordRoomUserNumRequest) GetUserNum() uint32 {
	if m != nil {
		return m.UserNum
	}
	return 0
}

type RecordRoomUserNumResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordRoomUserNumResponse) Reset()         { *m = RecordRoomUserNumResponse{} }
func (m *RecordRoomUserNumResponse) String() string { return proto.CompactTextString(m) }
func (*RecordRoomUserNumResponse) ProtoMessage()    {}
func (*RecordRoomUserNumResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{75}
}
func (m *RecordRoomUserNumResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordRoomUserNumResponse.Unmarshal(m, b)
}
func (m *RecordRoomUserNumResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordRoomUserNumResponse.Marshal(b, m, deterministic)
}
func (dst *RecordRoomUserNumResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordRoomUserNumResponse.Merge(dst, src)
}
func (m *RecordRoomUserNumResponse) XXX_Size() int {
	return xxx_messageInfo_RecordRoomUserNumResponse.Size(m)
}
func (m *RecordRoomUserNumResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordRoomUserNumResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RecordRoomUserNumResponse proto.InternalMessageInfo

type GetRoomUserNumRequest struct {
	AiUid                uint32   `protobuf:"varint,1,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRoomUserNumRequest) Reset()         { *m = GetRoomUserNumRequest{} }
func (m *GetRoomUserNumRequest) String() string { return proto.CompactTextString(m) }
func (*GetRoomUserNumRequest) ProtoMessage()    {}
func (*GetRoomUserNumRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{76}
}
func (m *GetRoomUserNumRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoomUserNumRequest.Unmarshal(m, b)
}
func (m *GetRoomUserNumRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoomUserNumRequest.Marshal(b, m, deterministic)
}
func (dst *GetRoomUserNumRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoomUserNumRequest.Merge(dst, src)
}
func (m *GetRoomUserNumRequest) XXX_Size() int {
	return xxx_messageInfo_GetRoomUserNumRequest.Size(m)
}
func (m *GetRoomUserNumRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoomUserNumRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoomUserNumRequest proto.InternalMessageInfo

func (m *GetRoomUserNumRequest) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

func (m *GetRoomUserNumRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetRoomUserNumResponse struct {
	UserNum              uint32   `protobuf:"varint,1,opt,name=user_num,json=userNum,proto3" json:"user_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRoomUserNumResponse) Reset()         { *m = GetRoomUserNumResponse{} }
func (m *GetRoomUserNumResponse) String() string { return proto.CompactTextString(m) }
func (*GetRoomUserNumResponse) ProtoMessage()    {}
func (*GetRoomUserNumResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{77}
}
func (m *GetRoomUserNumResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoomUserNumResponse.Unmarshal(m, b)
}
func (m *GetRoomUserNumResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoomUserNumResponse.Marshal(b, m, deterministic)
}
func (dst *GetRoomUserNumResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoomUserNumResponse.Merge(dst, src)
}
func (m *GetRoomUserNumResponse) XXX_Size() int {
	return xxx_messageInfo_GetRoomUserNumResponse.Size(m)
}
func (m *GetRoomUserNumResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoomUserNumResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoomUserNumResponse proto.InternalMessageInfo

func (m *GetRoomUserNumResponse) GetUserNum() uint32 {
	if m != nil {
		return m.UserNum
	}
	return 0
}

type AddRunningRoomRequest struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ExpiredAt            int64    `protobuf:"varint,3,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRunningRoomRequest) Reset()         { *m = AddRunningRoomRequest{} }
func (m *AddRunningRoomRequest) String() string { return proto.CompactTextString(m) }
func (*AddRunningRoomRequest) ProtoMessage()    {}
func (*AddRunningRoomRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{78}
}
func (m *AddRunningRoomRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRunningRoomRequest.Unmarshal(m, b)
}
func (m *AddRunningRoomRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRunningRoomRequest.Marshal(b, m, deterministic)
}
func (dst *AddRunningRoomRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRunningRoomRequest.Merge(dst, src)
}
func (m *AddRunningRoomRequest) XXX_Size() int {
	return xxx_messageInfo_AddRunningRoomRequest.Size(m)
}
func (m *AddRunningRoomRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRunningRoomRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddRunningRoomRequest proto.InternalMessageInfo

func (m *AddRunningRoomRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *AddRunningRoomRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddRunningRoomRequest) GetExpiredAt() int64 {
	if m != nil {
		return m.ExpiredAt
	}
	return 0
}

type AddRunningRoomResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRunningRoomResponse) Reset()         { *m = AddRunningRoomResponse{} }
func (m *AddRunningRoomResponse) String() string { return proto.CompactTextString(m) }
func (*AddRunningRoomResponse) ProtoMessage()    {}
func (*AddRunningRoomResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{79}
}
func (m *AddRunningRoomResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRunningRoomResponse.Unmarshal(m, b)
}
func (m *AddRunningRoomResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRunningRoomResponse.Marshal(b, m, deterministic)
}
func (dst *AddRunningRoomResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRunningRoomResponse.Merge(dst, src)
}
func (m *AddRunningRoomResponse) XXX_Size() int {
	return xxx_messageInfo_AddRunningRoomResponse.Size(m)
}
func (m *AddRunningRoomResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRunningRoomResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddRunningRoomResponse proto.InternalMessageInfo

type RoomInfo struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoomInfo) Reset()         { *m = RoomInfo{} }
func (m *RoomInfo) String() string { return proto.CompactTextString(m) }
func (*RoomInfo) ProtoMessage()    {}
func (*RoomInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{80}
}
func (m *RoomInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoomInfo.Unmarshal(m, b)
}
func (m *RoomInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoomInfo.Marshal(b, m, deterministic)
}
func (dst *RoomInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoomInfo.Merge(dst, src)
}
func (m *RoomInfo) XXX_Size() int {
	return xxx_messageInfo_RoomInfo.Size(m)
}
func (m *RoomInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RoomInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RoomInfo proto.InternalMessageInfo

func (m *RoomInfo) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *RoomInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type BatRemoveRunningRoomRequest struct {
	RoomList             []*RoomInfo `protobuf:"bytes,1,rep,name=room_list,json=roomList,proto3" json:"room_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *BatRemoveRunningRoomRequest) Reset()         { *m = BatRemoveRunningRoomRequest{} }
func (m *BatRemoveRunningRoomRequest) String() string { return proto.CompactTextString(m) }
func (*BatRemoveRunningRoomRequest) ProtoMessage()    {}
func (*BatRemoveRunningRoomRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{81}
}
func (m *BatRemoveRunningRoomRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatRemoveRunningRoomRequest.Unmarshal(m, b)
}
func (m *BatRemoveRunningRoomRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatRemoveRunningRoomRequest.Marshal(b, m, deterministic)
}
func (dst *BatRemoveRunningRoomRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatRemoveRunningRoomRequest.Merge(dst, src)
}
func (m *BatRemoveRunningRoomRequest) XXX_Size() int {
	return xxx_messageInfo_BatRemoveRunningRoomRequest.Size(m)
}
func (m *BatRemoveRunningRoomRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatRemoveRunningRoomRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatRemoveRunningRoomRequest proto.InternalMessageInfo

func (m *BatRemoveRunningRoomRequest) GetRoomList() []*RoomInfo {
	if m != nil {
		return m.RoomList
	}
	return nil
}

type BatRemoveRunningRoomResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatRemoveRunningRoomResponse) Reset()         { *m = BatRemoveRunningRoomResponse{} }
func (m *BatRemoveRunningRoomResponse) String() string { return proto.CompactTextString(m) }
func (*BatRemoveRunningRoomResponse) ProtoMessage()    {}
func (*BatRemoveRunningRoomResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{82}
}
func (m *BatRemoveRunningRoomResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatRemoveRunningRoomResponse.Unmarshal(m, b)
}
func (m *BatRemoveRunningRoomResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatRemoveRunningRoomResponse.Marshal(b, m, deterministic)
}
func (dst *BatRemoveRunningRoomResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatRemoveRunningRoomResponse.Merge(dst, src)
}
func (m *BatRemoveRunningRoomResponse) XXX_Size() int {
	return xxx_messageInfo_BatRemoveRunningRoomResponse.Size(m)
}
func (m *BatRemoveRunningRoomResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatRemoveRunningRoomResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatRemoveRunningRoomResponse proto.InternalMessageInfo

type BatGetRunningRoomRequest struct {
	RoomList             []*RoomInfo `protobuf:"bytes,1,rep,name=room_list,json=roomList,proto3" json:"room_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *BatGetRunningRoomRequest) Reset()         { *m = BatGetRunningRoomRequest{} }
func (m *BatGetRunningRoomRequest) String() string { return proto.CompactTextString(m) }
func (*BatGetRunningRoomRequest) ProtoMessage()    {}
func (*BatGetRunningRoomRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{83}
}
func (m *BatGetRunningRoomRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetRunningRoomRequest.Unmarshal(m, b)
}
func (m *BatGetRunningRoomRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetRunningRoomRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetRunningRoomRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetRunningRoomRequest.Merge(dst, src)
}
func (m *BatGetRunningRoomRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetRunningRoomRequest.Size(m)
}
func (m *BatGetRunningRoomRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetRunningRoomRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetRunningRoomRequest proto.InternalMessageInfo

func (m *BatGetRunningRoomRequest) GetRoomList() []*RoomInfo {
	if m != nil {
		return m.RoomList
	}
	return nil
}

type BatGetRunningRoomResponse struct {
	RoomRunningMap       map[uint32]bool `protobuf:"bytes,1,rep,name=room_running_map,json=roomRunningMap,proto3" json:"room_running_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatGetRunningRoomResponse) Reset()         { *m = BatGetRunningRoomResponse{} }
func (m *BatGetRunningRoomResponse) String() string { return proto.CompactTextString(m) }
func (*BatGetRunningRoomResponse) ProtoMessage()    {}
func (*BatGetRunningRoomResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{84}
}
func (m *BatGetRunningRoomResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetRunningRoomResponse.Unmarshal(m, b)
}
func (m *BatGetRunningRoomResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetRunningRoomResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetRunningRoomResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetRunningRoomResponse.Merge(dst, src)
}
func (m *BatGetRunningRoomResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetRunningRoomResponse.Size(m)
}
func (m *BatGetRunningRoomResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetRunningRoomResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetRunningRoomResponse proto.InternalMessageInfo

func (m *BatGetRunningRoomResponse) GetRoomRunningMap() map[uint32]bool {
	if m != nil {
		return m.RoomRunningMap
	}
	return nil
}

type BatUpdateHeartbeatRequest struct {
	CidList              []uint32 `protobuf:"varint,1,rep,packed,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatUpdateHeartbeatRequest) Reset()         { *m = BatUpdateHeartbeatRequest{} }
func (m *BatUpdateHeartbeatRequest) String() string { return proto.CompactTextString(m) }
func (*BatUpdateHeartbeatRequest) ProtoMessage()    {}
func (*BatUpdateHeartbeatRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{85}
}
func (m *BatUpdateHeartbeatRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatUpdateHeartbeatRequest.Unmarshal(m, b)
}
func (m *BatUpdateHeartbeatRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatUpdateHeartbeatRequest.Marshal(b, m, deterministic)
}
func (dst *BatUpdateHeartbeatRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatUpdateHeartbeatRequest.Merge(dst, src)
}
func (m *BatUpdateHeartbeatRequest) XXX_Size() int {
	return xxx_messageInfo_BatUpdateHeartbeatRequest.Size(m)
}
func (m *BatUpdateHeartbeatRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatUpdateHeartbeatRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatUpdateHeartbeatRequest proto.InternalMessageInfo

func (m *BatUpdateHeartbeatRequest) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

type BatUpdateHeartbeatResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatUpdateHeartbeatResponse) Reset()         { *m = BatUpdateHeartbeatResponse{} }
func (m *BatUpdateHeartbeatResponse) String() string { return proto.CompactTextString(m) }
func (*BatUpdateHeartbeatResponse) ProtoMessage()    {}
func (*BatUpdateHeartbeatResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{86}
}
func (m *BatUpdateHeartbeatResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatUpdateHeartbeatResponse.Unmarshal(m, b)
}
func (m *BatUpdateHeartbeatResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatUpdateHeartbeatResponse.Marshal(b, m, deterministic)
}
func (dst *BatUpdateHeartbeatResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatUpdateHeartbeatResponse.Merge(dst, src)
}
func (m *BatUpdateHeartbeatResponse) XXX_Size() int {
	return xxx_messageInfo_BatUpdateHeartbeatResponse.Size(m)
}
func (m *BatUpdateHeartbeatResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatUpdateHeartbeatResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatUpdateHeartbeatResponse proto.InternalMessageInfo

type GetNeedHeartbeatRoomRequest struct {
	Limit                uint32   `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	NoHeartbeatSeconds   uint32   `protobuf:"varint,2,opt,name=no_heartbeat_seconds,json=noHeartbeatSeconds,proto3" json:"no_heartbeat_seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNeedHeartbeatRoomRequest) Reset()         { *m = GetNeedHeartbeatRoomRequest{} }
func (m *GetNeedHeartbeatRoomRequest) String() string { return proto.CompactTextString(m) }
func (*GetNeedHeartbeatRoomRequest) ProtoMessage()    {}
func (*GetNeedHeartbeatRoomRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{87}
}
func (m *GetNeedHeartbeatRoomRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNeedHeartbeatRoomRequest.Unmarshal(m, b)
}
func (m *GetNeedHeartbeatRoomRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNeedHeartbeatRoomRequest.Marshal(b, m, deterministic)
}
func (dst *GetNeedHeartbeatRoomRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNeedHeartbeatRoomRequest.Merge(dst, src)
}
func (m *GetNeedHeartbeatRoomRequest) XXX_Size() int {
	return xxx_messageInfo_GetNeedHeartbeatRoomRequest.Size(m)
}
func (m *GetNeedHeartbeatRoomRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNeedHeartbeatRoomRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetNeedHeartbeatRoomRequest proto.InternalMessageInfo

func (m *GetNeedHeartbeatRoomRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetNeedHeartbeatRoomRequest) GetNoHeartbeatSeconds() uint32 {
	if m != nil {
		return m.NoHeartbeatSeconds
	}
	return 0
}

type GetNeedHeartbeatRoomResponse struct {
	CidList              []uint32 `protobuf:"varint,1,rep,packed,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNeedHeartbeatRoomResponse) Reset()         { *m = GetNeedHeartbeatRoomResponse{} }
func (m *GetNeedHeartbeatRoomResponse) String() string { return proto.CompactTextString(m) }
func (*GetNeedHeartbeatRoomResponse) ProtoMessage()    {}
func (*GetNeedHeartbeatRoomResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{88}
}
func (m *GetNeedHeartbeatRoomResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNeedHeartbeatRoomResponse.Unmarshal(m, b)
}
func (m *GetNeedHeartbeatRoomResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNeedHeartbeatRoomResponse.Marshal(b, m, deterministic)
}
func (dst *GetNeedHeartbeatRoomResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNeedHeartbeatRoomResponse.Merge(dst, src)
}
func (m *GetNeedHeartbeatRoomResponse) XXX_Size() int {
	return xxx_messageInfo_GetNeedHeartbeatRoomResponse.Size(m)
}
func (m *GetNeedHeartbeatRoomResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNeedHeartbeatRoomResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetNeedHeartbeatRoomResponse proto.InternalMessageInfo

func (m *GetNeedHeartbeatRoomResponse) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

type RemoveFromHeartbeatPoolRequest struct {
	CidList              []uint32 `protobuf:"varint,1,rep,packed,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveFromHeartbeatPoolRequest) Reset()         { *m = RemoveFromHeartbeatPoolRequest{} }
func (m *RemoveFromHeartbeatPoolRequest) String() string { return proto.CompactTextString(m) }
func (*RemoveFromHeartbeatPoolRequest) ProtoMessage()    {}
func (*RemoveFromHeartbeatPoolRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{89}
}
func (m *RemoveFromHeartbeatPoolRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveFromHeartbeatPoolRequest.Unmarshal(m, b)
}
func (m *RemoveFromHeartbeatPoolRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveFromHeartbeatPoolRequest.Marshal(b, m, deterministic)
}
func (dst *RemoveFromHeartbeatPoolRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveFromHeartbeatPoolRequest.Merge(dst, src)
}
func (m *RemoveFromHeartbeatPoolRequest) XXX_Size() int {
	return xxx_messageInfo_RemoveFromHeartbeatPoolRequest.Size(m)
}
func (m *RemoveFromHeartbeatPoolRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveFromHeartbeatPoolRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveFromHeartbeatPoolRequest proto.InternalMessageInfo

func (m *RemoveFromHeartbeatPoolRequest) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

type RemoveFromHeartbeatPoolResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveFromHeartbeatPoolResponse) Reset()         { *m = RemoveFromHeartbeatPoolResponse{} }
func (m *RemoveFromHeartbeatPoolResponse) String() string { return proto.CompactTextString(m) }
func (*RemoveFromHeartbeatPoolResponse) ProtoMessage()    {}
func (*RemoveFromHeartbeatPoolResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_6b87c18fd40d3bb3, []int{90}
}
func (m *RemoveFromHeartbeatPoolResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveFromHeartbeatPoolResponse.Unmarshal(m, b)
}
func (m *RemoveFromHeartbeatPoolResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveFromHeartbeatPoolResponse.Marshal(b, m, deterministic)
}
func (dst *RemoveFromHeartbeatPoolResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveFromHeartbeatPoolResponse.Merge(dst, src)
}
func (m *RemoveFromHeartbeatPoolResponse) XXX_Size() int {
	return xxx_messageInfo_RemoveFromHeartbeatPoolResponse.Size(m)
}
func (m *RemoveFromHeartbeatPoolResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveFromHeartbeatPoolResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveFromHeartbeatPoolResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*AIAccount)(nil), "aigc_account.AIAccount")
	proto.RegisterType((*CreateAIAccountRequest)(nil), "aigc_account.CreateAIAccountRequest")
	proto.RegisterType((*CreateAIAccountResponse)(nil), "aigc_account.CreateAIAccountResponse")
	proto.RegisterType((*UpdateAIAccountRequest)(nil), "aigc_account.UpdateAIAccountRequest")
	proto.RegisterType((*UpdateAIAccountResponse)(nil), "aigc_account.UpdateAIAccountResponse")
	proto.RegisterType((*BatchUnregisterAIAccountRequest)(nil), "aigc_account.BatchUnregisterAIAccountRequest")
	proto.RegisterType((*BatchUnregisterAIAccountResponse)(nil), "aigc_account.BatchUnregisterAIAccountResponse")
	proto.RegisterType((*GetPageAIAccountRequest)(nil), "aigc_account.GetPageAIAccountRequest")
	proto.RegisterType((*GetPageAIAccountResponse)(nil), "aigc_account.GetPageAIAccountResponse")
	proto.RegisterType((*GetAIAccountRequest)(nil), "aigc_account.GetAIAccountRequest")
	proto.RegisterType((*GetAIAccountResponse)(nil), "aigc_account.GetAIAccountResponse")
	proto.RegisterType((*BatchGetAIAccountRequest)(nil), "aigc_account.BatchGetAIAccountRequest")
	proto.RegisterType((*BatchGetAIAccountResponse)(nil), "aigc_account.BatchGetAIAccountResponse")
	proto.RegisterType((*IncrChatRoundRequest)(nil), "aigc_account.IncrChatRoundRequest")
	proto.RegisterType((*IncrChatRoundResponse)(nil), "aigc_account.IncrChatRoundResponse")
	proto.RegisterType((*GetChatRoundRequest)(nil), "aigc_account.GetChatRoundRequest")
	proto.RegisterType((*GetChatRoundResponse)(nil), "aigc_account.GetChatRoundResponse")
	proto.RegisterType((*UpdateChatTimeRequest)(nil), "aigc_account.UpdateChatTimeRequest")
	proto.RegisterType((*UpdateChatTimeResponse)(nil), "aigc_account.UpdateChatTimeResponse")
	proto.RegisterType((*GetLastChatTimeRequest)(nil), "aigc_account.GetLastChatTimeRequest")
	proto.RegisterType((*GetLastChatTimeResponse)(nil), "aigc_account.GetLastChatTimeResponse")
	proto.RegisterType((*AddAiPostRequest)(nil), "aigc_account.AddAiPostRequest")
	proto.RegisterType((*AddAiPostResponse)(nil), "aigc_account.AddAiPostResponse")
	proto.RegisterType((*GetAiPostRequest)(nil), "aigc_account.GetAiPostRequest")
	proto.RegisterType((*GetAiPostResponse)(nil), "aigc_account.GetAiPostResponse")
	proto.RegisterType((*AddUserAiChatRecordRequest)(nil), "aigc_account.AddUserAiChatRecordRequest")
	proto.RegisterType((*AddUserAiChatRecordResponse)(nil), "aigc_account.AddUserAiChatRecordResponse")
	proto.RegisterType((*GetUserAiChatRecordRequest)(nil), "aigc_account.GetUserAiChatRecordRequest")
	proto.RegisterType((*GetUserAiChatRecordResponse)(nil), "aigc_account.GetUserAiChatRecordResponse")
	proto.RegisterType((*AddUserAiInteractionRecordRequest)(nil), "aigc_account.AddUserAiInteractionRecordRequest")
	proto.RegisterType((*AddUserAiInteractionRecordResponse)(nil), "aigc_account.AddUserAiInteractionRecordResponse")
	proto.RegisterType((*GetUserAiInteractionRecordRequest)(nil), "aigc_account.GetUserAiInteractionRecordRequest")
	proto.RegisterType((*GetUserAiInteractionRecordResponse)(nil), "aigc_account.GetUserAiInteractionRecordResponse")
	proto.RegisterType((*AddAiLikePostRequest)(nil), "aigc_account.AddAiLikePostRequest")
	proto.RegisterType((*AddAiLikePostResponse)(nil), "aigc_account.AddAiLikePostResponse")
	proto.RegisterType((*GetAiLikePostCountRequest)(nil), "aigc_account.GetAiLikePostCountRequest")
	proto.RegisterType((*GetAiLikePostCountResponse)(nil), "aigc_account.GetAiLikePostCountResponse")
	proto.RegisterType((*AddAiCommentPostRequest)(nil), "aigc_account.AddAiCommentPostRequest")
	proto.RegisterType((*AddAiCommentPostResponse)(nil), "aigc_account.AddAiCommentPostResponse")
	proto.RegisterType((*GetAiCommentPostCountRequest)(nil), "aigc_account.GetAiCommentPostCountRequest")
	proto.RegisterType((*GetAiCommentPostCountResponse)(nil), "aigc_account.GetAiCommentPostCountResponse")
	proto.RegisterType((*AddPostAiCommentRecordRequest)(nil), "aigc_account.AddPostAiCommentRecordRequest")
	proto.RegisterType((*AddPostAiCommentRecordResponse)(nil), "aigc_account.AddPostAiCommentRecordResponse")
	proto.RegisterType((*GetPostAiCommentCountRequest)(nil), "aigc_account.GetPostAiCommentCountRequest")
	proto.RegisterType((*GetPostAiCommentCountResponse)(nil), "aigc_account.GetPostAiCommentCountResponse")
	proto.RegisterType((*GetPostAiCommentListRequest)(nil), "aigc_account.GetPostAiCommentListRequest")
	proto.RegisterType((*GetPostAiCommentListResponse)(nil), "aigc_account.GetPostAiCommentListResponse")
	proto.RegisterType((*AiCommentData)(nil), "aigc_account.AiCommentData")
	proto.RegisterType((*GetAIAccountByGenderRequest)(nil), "aigc_account.GetAIAccountByGenderRequest")
	proto.RegisterType((*GetAIAccountByGenderResponse)(nil), "aigc_account.GetAIAccountByGenderResponse")
	proto.RegisterType((*RoomBaseCfg)(nil), "aigc_account.RoomBaseCfg")
	proto.RegisterType((*BlockOption)(nil), "aigc_account.BlockOption")
	proto.RegisterType((*TimeRange)(nil), "aigc_account.TimeRange")
	proto.RegisterType((*BusinessCfg)(nil), "aigc_account.BusinessCfg")
	proto.RegisterType((*AiRoomCfg)(nil), "aigc_account.AiRoomCfg")
	proto.RegisterType((*UpdateAiRoomCfgRequest)(nil), "aigc_account.UpdateAiRoomCfgRequest")
	proto.RegisterType((*UpdateAiRoomCfgResponse)(nil), "aigc_account.UpdateAiRoomCfgResponse")
	proto.RegisterType((*GetAiRoomCfgRequest)(nil), "aigc_account.GetAiRoomCfgRequest")
	proto.RegisterType((*GetAiRoomCfgResponse)(nil), "aigc_account.GetAiRoomCfgResponse")
	proto.RegisterType((*GetRunningAiRoomsRequest)(nil), "aigc_account.GetRunningAiRoomsRequest")
	proto.RegisterType((*GetRunningAiRoomsResponse)(nil), "aigc_account.GetRunningAiRoomsResponse")
	proto.RegisterType((*RecordReceptionTimeRequest)(nil), "aigc_account.RecordReceptionTimeRequest")
	proto.RegisterType((*RecordReceptionTimeResponse)(nil), "aigc_account.RecordReceptionTimeResponse")
	proto.RegisterType((*GetLastReceptionTimeRequest)(nil), "aigc_account.GetLastReceptionTimeRequest")
	proto.RegisterType((*GetLastReceptionTimeResponse)(nil), "aigc_account.GetLastReceptionTimeResponse")
	proto.RegisterType((*RecordWaitingReceptionUserRequest)(nil), "aigc_account.RecordWaitingReceptionUserRequest")
	proto.RegisterType((*RecordWaitingReceptionUserResponse)(nil), "aigc_account.RecordWaitingReceptionUserResponse")
	proto.RegisterType((*GetWaitingReceptionUserRequest)(nil), "aigc_account.GetWaitingReceptionUserRequest")
	proto.RegisterType((*GetWaitingReceptionUserResponse)(nil), "aigc_account.GetWaitingReceptionUserResponse")
	proto.RegisterType((*WaitingReceptionUser)(nil), "aigc_account.WaitingReceptionUser")
	proto.RegisterType((*RecordReceptionUserRequest)(nil), "aigc_account.RecordReceptionUserRequest")
	proto.RegisterType((*RecordReceptionUserResponse)(nil), "aigc_account.RecordReceptionUserResponse")
	proto.RegisterType((*CheckReceptionUserRequest)(nil), "aigc_account.CheckReceptionUserRequest")
	proto.RegisterType((*CheckReceptionUserResponse)(nil), "aigc_account.CheckReceptionUserResponse")
	proto.RegisterMapType((map[uint32]bool)(nil), "aigc_account.CheckReceptionUserResponse.ReceptionMapEntry")
	proto.RegisterType((*RecordRoomUserNumRequest)(nil), "aigc_account.RecordRoomUserNumRequest")
	proto.RegisterType((*RecordRoomUserNumResponse)(nil), "aigc_account.RecordRoomUserNumResponse")
	proto.RegisterType((*GetRoomUserNumRequest)(nil), "aigc_account.GetRoomUserNumRequest")
	proto.RegisterType((*GetRoomUserNumResponse)(nil), "aigc_account.GetRoomUserNumResponse")
	proto.RegisterType((*AddRunningRoomRequest)(nil), "aigc_account.AddRunningRoomRequest")
	proto.RegisterType((*AddRunningRoomResponse)(nil), "aigc_account.AddRunningRoomResponse")
	proto.RegisterType((*RoomInfo)(nil), "aigc_account.RoomInfo")
	proto.RegisterType((*BatRemoveRunningRoomRequest)(nil), "aigc_account.BatRemoveRunningRoomRequest")
	proto.RegisterType((*BatRemoveRunningRoomResponse)(nil), "aigc_account.BatRemoveRunningRoomResponse")
	proto.RegisterType((*BatGetRunningRoomRequest)(nil), "aigc_account.BatGetRunningRoomRequest")
	proto.RegisterType((*BatGetRunningRoomResponse)(nil), "aigc_account.BatGetRunningRoomResponse")
	proto.RegisterMapType((map[uint32]bool)(nil), "aigc_account.BatGetRunningRoomResponse.RoomRunningMapEntry")
	proto.RegisterType((*BatUpdateHeartbeatRequest)(nil), "aigc_account.BatUpdateHeartbeatRequest")
	proto.RegisterType((*BatUpdateHeartbeatResponse)(nil), "aigc_account.BatUpdateHeartbeatResponse")
	proto.RegisterType((*GetNeedHeartbeatRoomRequest)(nil), "aigc_account.GetNeedHeartbeatRoomRequest")
	proto.RegisterType((*GetNeedHeartbeatRoomResponse)(nil), "aigc_account.GetNeedHeartbeatRoomResponse")
	proto.RegisterType((*RemoveFromHeartbeatPoolRequest)(nil), "aigc_account.RemoveFromHeartbeatPoolRequest")
	proto.RegisterType((*RemoveFromHeartbeatPoolResponse)(nil), "aigc_account.RemoveFromHeartbeatPoolResponse")
	proto.RegisterEnum("aigc_account.GetAIAccountSource", GetAIAccountSource_name, GetAIAccountSource_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AigcAccountClient is the client API for AigcAccount service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AigcAccountClient interface {
	// 创建AI账号
	CreateAIAccount(ctx context.Context, in *CreateAIAccountRequest, opts ...grpc.CallOption) (*CreateAIAccountResponse, error)
	// 更新AI账号
	UpdateAIAccount(ctx context.Context, in *UpdateAIAccountRequest, opts ...grpc.CallOption) (*UpdateAIAccountResponse, error)
	// 批量注销AI账号
	BatchUnregisterAIAccount(ctx context.Context, in *BatchUnregisterAIAccountRequest, opts ...grpc.CallOption) (*BatchUnregisterAIAccountResponse, error)
	// 获取AI账号列表（运营后台）
	GetPageAIAccount(ctx context.Context, in *GetPageAIAccountRequest, opts ...grpc.CallOption) (*GetPageAIAccountResponse, error)
	// 根据uid获取AI账号
	GetAIAccount(ctx context.Context, in *GetAIAccountRequest, opts ...grpc.CallOption) (*GetAIAccountResponse, error)
	// 批量获取AI账号
	BatchGetAIAccount(ctx context.Context, in *BatchGetAIAccountRequest, opts ...grpc.CallOption) (*BatchGetAIAccountResponse, error)
	// 增加聊天轮次
	IncrChatRound(ctx context.Context, in *IncrChatRoundRequest, opts ...grpc.CallOption) (*IncrChatRoundResponse, error)
	// 获取聊天轮次
	GetChatRound(ctx context.Context, in *GetChatRoundRequest, opts ...grpc.CallOption) (*GetChatRoundResponse, error)
	// 更新最近聊天时间
	UpdateChatTime(ctx context.Context, in *UpdateChatTimeRequest, opts ...grpc.CallOption) (*UpdateChatTimeResponse, error)
	// 获取最近聊天时间
	GetLastChatTime(ctx context.Context, in *GetLastChatTimeRequest, opts ...grpc.CallOption) (*GetLastChatTimeResponse, error)
	// 增加AI账号发帖
	AddAiPost(ctx context.Context, in *AddAiPostRequest, opts ...grpc.CallOption) (*AddAiPostResponse, error)
	// 获取AI账号发帖列表
	GetAiPost(ctx context.Context, in *GetAiPostRequest, opts ...grpc.CallOption) (*GetAiPostResponse, error)
	// 添加用户和AI账号聊天记录
	AddUserAiChatRecord(ctx context.Context, in *AddUserAiChatRecordRequest, opts ...grpc.CallOption) (*AddUserAiChatRecordResponse, error)
	// 获取用户和AI账号聊天记录
	GetUserAiChatRecord(ctx context.Context, in *GetUserAiChatRecordRequest, opts ...grpc.CallOption) (*GetUserAiChatRecordResponse, error)
	// 添加用户和AI互动(双方都发过消息)记录
	AddUserAiInteractionRecord(ctx context.Context, in *AddUserAiInteractionRecordRequest, opts ...grpc.CallOption) (*AddUserAiInteractionRecordResponse, error)
	// 获取用户和AI互动(双方都发过消息)记录
	GetUserAiInteractionRecord(ctx context.Context, in *GetUserAiInteractionRecordRequest, opts ...grpc.CallOption) (*GetUserAiInteractionRecordResponse, error)
	// 添加AI点赞的帖子
	AddAiLikePost(ctx context.Context, in *AddAiLikePostRequest, opts ...grpc.CallOption) (*AddAiLikePostResponse, error)
	// 获取AI点赞的帖子数
	GetAiLikePostCount(ctx context.Context, in *GetAiLikePostCountRequest, opts ...grpc.CallOption) (*GetAiLikePostCountResponse, error)
	// 添加AI评论的帖子
	AddAiCommentPost(ctx context.Context, in *AddAiCommentPostRequest, opts ...grpc.CallOption) (*AddAiCommentPostResponse, error)
	// 获取AI评论的帖子数
	GetAiCommentPostCount(ctx context.Context, in *GetAiCommentPostCountRequest, opts ...grpc.CallOption) (*GetAiCommentPostCountResponse, error)
	// 添加帖子的AI评论数据
	AddPostAiCommentRecord(ctx context.Context, in *AddPostAiCommentRecordRequest, opts ...grpc.CallOption) (*AddPostAiCommentRecordResponse, error)
	// 获取帖子的AI评论数
	GetPostAiCommentCount(ctx context.Context, in *GetPostAiCommentCountRequest, opts ...grpc.CallOption) (*GetPostAiCommentCountResponse, error)
	// 获取帖子评论的ai列表
	GetPostAiCommentList(ctx context.Context, in *GetPostAiCommentListRequest, opts ...grpc.CallOption) (*GetPostAiCommentListResponse, error)
	// 获取指定性别的AI账号列表
	GetAIAccountByGender(ctx context.Context, in *GetAIAccountByGenderRequest, opts ...grpc.CallOption) (*GetAIAccountByGenderResponse, error)
	// 更新AI账号房间配置
	UpdateAiRoomCfg(ctx context.Context, in *UpdateAiRoomCfgRequest, opts ...grpc.CallOption) (*UpdateAiRoomCfgResponse, error)
	// 获取AI账号房间配置
	GetAiRoomCfg(ctx context.Context, in *GetAiRoomCfgRequest, opts ...grpc.CallOption) (*GetAiRoomCfgResponse, error)
	// 获取运营时间中的房间配置信息
	GetRunningAiRooms(ctx context.Context, in *GetRunningAiRoomsRequest, opts ...grpc.CallOption) (*GetRunningAiRoomsResponse, error)
	// 记录最近一次接待时间
	RecordReceptionTime(ctx context.Context, in *RecordReceptionTimeRequest, opts ...grpc.CallOption) (*RecordReceptionTimeResponse, error)
	// 获取最近一次接待
	GetLastReceptionTime(ctx context.Context, in *GetLastReceptionTimeRequest, opts ...grpc.CallOption) (*GetLastReceptionTimeResponse, error)
	// 记录等待接待用户
	RecordWaitingReceptionUser(ctx context.Context, in *RecordWaitingReceptionUserRequest, opts ...grpc.CallOption) (*RecordWaitingReceptionUserResponse, error)
	// 获取等待接待用户
	GetWaitingReceptionUser(ctx context.Context, in *GetWaitingReceptionUserRequest, opts ...grpc.CallOption) (*GetWaitingReceptionUserResponse, error)
	// 记录已接待用户
	RecordReceptionUser(ctx context.Context, in *RecordReceptionUserRequest, opts ...grpc.CallOption) (*RecordReceptionUserResponse, error)
	// 获取已接待用户
	CheckReceptionUser(ctx context.Context, in *CheckReceptionUserRequest, opts ...grpc.CallOption) (*CheckReceptionUserResponse, error)
	// 记录当前房间人数
	RecordRoomUserNum(ctx context.Context, in *RecordRoomUserNumRequest, opts ...grpc.CallOption) (*RecordRoomUserNumResponse, error)
	// 获取当前房间人数
	GetRoomUserNum(ctx context.Context, in *GetRoomUserNumRequest, opts ...grpc.CallOption) (*GetRoomUserNumResponse, error)
	// 添加运营中的房间
	AddRunningRoom(ctx context.Context, in *AddRunningRoomRequest, opts ...grpc.CallOption) (*AddRunningRoomResponse, error)
	// 移除运营中的房间
	BatRemoveRunningRoom(ctx context.Context, in *BatRemoveRunningRoomRequest, opts ...grpc.CallOption) (*BatRemoveRunningRoomResponse, error)
	// 批量获取运营中的房间
	BatGetRunningRoom(ctx context.Context, in *BatGetRunningRoomRequest, opts ...grpc.CallOption) (*BatGetRunningRoomResponse, error)
	// 批量更新心跳时间
	BatUpdateHeartbeat(ctx context.Context, in *BatUpdateHeartbeatRequest, opts ...grpc.CallOption) (*BatUpdateHeartbeatResponse, error)
	// 获取需要心跳的房间
	GetNeedHeartbeatRoom(ctx context.Context, in *GetNeedHeartbeatRoomRequest, opts ...grpc.CallOption) (*GetNeedHeartbeatRoomResponse, error)
	// 从心跳池中移除
	RemoveFromHeartbeatPool(ctx context.Context, in *RemoveFromHeartbeatPoolRequest, opts ...grpc.CallOption) (*RemoveFromHeartbeatPoolResponse, error)
}

type aigcAccountClient struct {
	cc *grpc.ClientConn
}

func NewAigcAccountClient(cc *grpc.ClientConn) AigcAccountClient {
	return &aigcAccountClient{cc}
}

func (c *aigcAccountClient) CreateAIAccount(ctx context.Context, in *CreateAIAccountRequest, opts ...grpc.CallOption) (*CreateAIAccountResponse, error) {
	out := new(CreateAIAccountResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/CreateAIAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) UpdateAIAccount(ctx context.Context, in *UpdateAIAccountRequest, opts ...grpc.CallOption) (*UpdateAIAccountResponse, error) {
	out := new(UpdateAIAccountResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/UpdateAIAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) BatchUnregisterAIAccount(ctx context.Context, in *BatchUnregisterAIAccountRequest, opts ...grpc.CallOption) (*BatchUnregisterAIAccountResponse, error) {
	out := new(BatchUnregisterAIAccountResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/BatchUnregisterAIAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetPageAIAccount(ctx context.Context, in *GetPageAIAccountRequest, opts ...grpc.CallOption) (*GetPageAIAccountResponse, error) {
	out := new(GetPageAIAccountResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetPageAIAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetAIAccount(ctx context.Context, in *GetAIAccountRequest, opts ...grpc.CallOption) (*GetAIAccountResponse, error) {
	out := new(GetAIAccountResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetAIAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) BatchGetAIAccount(ctx context.Context, in *BatchGetAIAccountRequest, opts ...grpc.CallOption) (*BatchGetAIAccountResponse, error) {
	out := new(BatchGetAIAccountResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/BatchGetAIAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) IncrChatRound(ctx context.Context, in *IncrChatRoundRequest, opts ...grpc.CallOption) (*IncrChatRoundResponse, error) {
	out := new(IncrChatRoundResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/IncrChatRound", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetChatRound(ctx context.Context, in *GetChatRoundRequest, opts ...grpc.CallOption) (*GetChatRoundResponse, error) {
	out := new(GetChatRoundResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetChatRound", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) UpdateChatTime(ctx context.Context, in *UpdateChatTimeRequest, opts ...grpc.CallOption) (*UpdateChatTimeResponse, error) {
	out := new(UpdateChatTimeResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/UpdateChatTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetLastChatTime(ctx context.Context, in *GetLastChatTimeRequest, opts ...grpc.CallOption) (*GetLastChatTimeResponse, error) {
	out := new(GetLastChatTimeResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetLastChatTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) AddAiPost(ctx context.Context, in *AddAiPostRequest, opts ...grpc.CallOption) (*AddAiPostResponse, error) {
	out := new(AddAiPostResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/AddAiPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetAiPost(ctx context.Context, in *GetAiPostRequest, opts ...grpc.CallOption) (*GetAiPostResponse, error) {
	out := new(GetAiPostResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetAiPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) AddUserAiChatRecord(ctx context.Context, in *AddUserAiChatRecordRequest, opts ...grpc.CallOption) (*AddUserAiChatRecordResponse, error) {
	out := new(AddUserAiChatRecordResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/AddUserAiChatRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetUserAiChatRecord(ctx context.Context, in *GetUserAiChatRecordRequest, opts ...grpc.CallOption) (*GetUserAiChatRecordResponse, error) {
	out := new(GetUserAiChatRecordResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetUserAiChatRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) AddUserAiInteractionRecord(ctx context.Context, in *AddUserAiInteractionRecordRequest, opts ...grpc.CallOption) (*AddUserAiInteractionRecordResponse, error) {
	out := new(AddUserAiInteractionRecordResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/AddUserAiInteractionRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetUserAiInteractionRecord(ctx context.Context, in *GetUserAiInteractionRecordRequest, opts ...grpc.CallOption) (*GetUserAiInteractionRecordResponse, error) {
	out := new(GetUserAiInteractionRecordResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetUserAiInteractionRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) AddAiLikePost(ctx context.Context, in *AddAiLikePostRequest, opts ...grpc.CallOption) (*AddAiLikePostResponse, error) {
	out := new(AddAiLikePostResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/AddAiLikePost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetAiLikePostCount(ctx context.Context, in *GetAiLikePostCountRequest, opts ...grpc.CallOption) (*GetAiLikePostCountResponse, error) {
	out := new(GetAiLikePostCountResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetAiLikePostCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) AddAiCommentPost(ctx context.Context, in *AddAiCommentPostRequest, opts ...grpc.CallOption) (*AddAiCommentPostResponse, error) {
	out := new(AddAiCommentPostResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/AddAiCommentPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetAiCommentPostCount(ctx context.Context, in *GetAiCommentPostCountRequest, opts ...grpc.CallOption) (*GetAiCommentPostCountResponse, error) {
	out := new(GetAiCommentPostCountResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetAiCommentPostCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) AddPostAiCommentRecord(ctx context.Context, in *AddPostAiCommentRecordRequest, opts ...grpc.CallOption) (*AddPostAiCommentRecordResponse, error) {
	out := new(AddPostAiCommentRecordResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/AddPostAiCommentRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetPostAiCommentCount(ctx context.Context, in *GetPostAiCommentCountRequest, opts ...grpc.CallOption) (*GetPostAiCommentCountResponse, error) {
	out := new(GetPostAiCommentCountResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetPostAiCommentCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetPostAiCommentList(ctx context.Context, in *GetPostAiCommentListRequest, opts ...grpc.CallOption) (*GetPostAiCommentListResponse, error) {
	out := new(GetPostAiCommentListResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetPostAiCommentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetAIAccountByGender(ctx context.Context, in *GetAIAccountByGenderRequest, opts ...grpc.CallOption) (*GetAIAccountByGenderResponse, error) {
	out := new(GetAIAccountByGenderResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetAIAccountByGender", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) UpdateAiRoomCfg(ctx context.Context, in *UpdateAiRoomCfgRequest, opts ...grpc.CallOption) (*UpdateAiRoomCfgResponse, error) {
	out := new(UpdateAiRoomCfgResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/UpdateAiRoomCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetAiRoomCfg(ctx context.Context, in *GetAiRoomCfgRequest, opts ...grpc.CallOption) (*GetAiRoomCfgResponse, error) {
	out := new(GetAiRoomCfgResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetAiRoomCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetRunningAiRooms(ctx context.Context, in *GetRunningAiRoomsRequest, opts ...grpc.CallOption) (*GetRunningAiRoomsResponse, error) {
	out := new(GetRunningAiRoomsResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetRunningAiRooms", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) RecordReceptionTime(ctx context.Context, in *RecordReceptionTimeRequest, opts ...grpc.CallOption) (*RecordReceptionTimeResponse, error) {
	out := new(RecordReceptionTimeResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/RecordReceptionTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetLastReceptionTime(ctx context.Context, in *GetLastReceptionTimeRequest, opts ...grpc.CallOption) (*GetLastReceptionTimeResponse, error) {
	out := new(GetLastReceptionTimeResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetLastReceptionTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) RecordWaitingReceptionUser(ctx context.Context, in *RecordWaitingReceptionUserRequest, opts ...grpc.CallOption) (*RecordWaitingReceptionUserResponse, error) {
	out := new(RecordWaitingReceptionUserResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/RecordWaitingReceptionUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetWaitingReceptionUser(ctx context.Context, in *GetWaitingReceptionUserRequest, opts ...grpc.CallOption) (*GetWaitingReceptionUserResponse, error) {
	out := new(GetWaitingReceptionUserResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetWaitingReceptionUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) RecordReceptionUser(ctx context.Context, in *RecordReceptionUserRequest, opts ...grpc.CallOption) (*RecordReceptionUserResponse, error) {
	out := new(RecordReceptionUserResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/RecordReceptionUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) CheckReceptionUser(ctx context.Context, in *CheckReceptionUserRequest, opts ...grpc.CallOption) (*CheckReceptionUserResponse, error) {
	out := new(CheckReceptionUserResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/CheckReceptionUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) RecordRoomUserNum(ctx context.Context, in *RecordRoomUserNumRequest, opts ...grpc.CallOption) (*RecordRoomUserNumResponse, error) {
	out := new(RecordRoomUserNumResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/RecordRoomUserNum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetRoomUserNum(ctx context.Context, in *GetRoomUserNumRequest, opts ...grpc.CallOption) (*GetRoomUserNumResponse, error) {
	out := new(GetRoomUserNumResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetRoomUserNum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) AddRunningRoom(ctx context.Context, in *AddRunningRoomRequest, opts ...grpc.CallOption) (*AddRunningRoomResponse, error) {
	out := new(AddRunningRoomResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/AddRunningRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) BatRemoveRunningRoom(ctx context.Context, in *BatRemoveRunningRoomRequest, opts ...grpc.CallOption) (*BatRemoveRunningRoomResponse, error) {
	out := new(BatRemoveRunningRoomResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/BatRemoveRunningRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) BatGetRunningRoom(ctx context.Context, in *BatGetRunningRoomRequest, opts ...grpc.CallOption) (*BatGetRunningRoomResponse, error) {
	out := new(BatGetRunningRoomResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/BatGetRunningRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) BatUpdateHeartbeat(ctx context.Context, in *BatUpdateHeartbeatRequest, opts ...grpc.CallOption) (*BatUpdateHeartbeatResponse, error) {
	out := new(BatUpdateHeartbeatResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/BatUpdateHeartbeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) GetNeedHeartbeatRoom(ctx context.Context, in *GetNeedHeartbeatRoomRequest, opts ...grpc.CallOption) (*GetNeedHeartbeatRoomResponse, error) {
	out := new(GetNeedHeartbeatRoomResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/GetNeedHeartbeatRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountClient) RemoveFromHeartbeatPool(ctx context.Context, in *RemoveFromHeartbeatPoolRequest, opts ...grpc.CallOption) (*RemoveFromHeartbeatPoolResponse, error) {
	out := new(RemoveFromHeartbeatPoolResponse)
	err := c.cc.Invoke(ctx, "/aigc_account.AigcAccount/RemoveFromHeartbeatPool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AigcAccountServer is the server API for AigcAccount service.
type AigcAccountServer interface {
	// 创建AI账号
	CreateAIAccount(context.Context, *CreateAIAccountRequest) (*CreateAIAccountResponse, error)
	// 更新AI账号
	UpdateAIAccount(context.Context, *UpdateAIAccountRequest) (*UpdateAIAccountResponse, error)
	// 批量注销AI账号
	BatchUnregisterAIAccount(context.Context, *BatchUnregisterAIAccountRequest) (*BatchUnregisterAIAccountResponse, error)
	// 获取AI账号列表（运营后台）
	GetPageAIAccount(context.Context, *GetPageAIAccountRequest) (*GetPageAIAccountResponse, error)
	// 根据uid获取AI账号
	GetAIAccount(context.Context, *GetAIAccountRequest) (*GetAIAccountResponse, error)
	// 批量获取AI账号
	BatchGetAIAccount(context.Context, *BatchGetAIAccountRequest) (*BatchGetAIAccountResponse, error)
	// 增加聊天轮次
	IncrChatRound(context.Context, *IncrChatRoundRequest) (*IncrChatRoundResponse, error)
	// 获取聊天轮次
	GetChatRound(context.Context, *GetChatRoundRequest) (*GetChatRoundResponse, error)
	// 更新最近聊天时间
	UpdateChatTime(context.Context, *UpdateChatTimeRequest) (*UpdateChatTimeResponse, error)
	// 获取最近聊天时间
	GetLastChatTime(context.Context, *GetLastChatTimeRequest) (*GetLastChatTimeResponse, error)
	// 增加AI账号发帖
	AddAiPost(context.Context, *AddAiPostRequest) (*AddAiPostResponse, error)
	// 获取AI账号发帖列表
	GetAiPost(context.Context, *GetAiPostRequest) (*GetAiPostResponse, error)
	// 添加用户和AI账号聊天记录
	AddUserAiChatRecord(context.Context, *AddUserAiChatRecordRequest) (*AddUserAiChatRecordResponse, error)
	// 获取用户和AI账号聊天记录
	GetUserAiChatRecord(context.Context, *GetUserAiChatRecordRequest) (*GetUserAiChatRecordResponse, error)
	// 添加用户和AI互动(双方都发过消息)记录
	AddUserAiInteractionRecord(context.Context, *AddUserAiInteractionRecordRequest) (*AddUserAiInteractionRecordResponse, error)
	// 获取用户和AI互动(双方都发过消息)记录
	GetUserAiInteractionRecord(context.Context, *GetUserAiInteractionRecordRequest) (*GetUserAiInteractionRecordResponse, error)
	// 添加AI点赞的帖子
	AddAiLikePost(context.Context, *AddAiLikePostRequest) (*AddAiLikePostResponse, error)
	// 获取AI点赞的帖子数
	GetAiLikePostCount(context.Context, *GetAiLikePostCountRequest) (*GetAiLikePostCountResponse, error)
	// 添加AI评论的帖子
	AddAiCommentPost(context.Context, *AddAiCommentPostRequest) (*AddAiCommentPostResponse, error)
	// 获取AI评论的帖子数
	GetAiCommentPostCount(context.Context, *GetAiCommentPostCountRequest) (*GetAiCommentPostCountResponse, error)
	// 添加帖子的AI评论数据
	AddPostAiCommentRecord(context.Context, *AddPostAiCommentRecordRequest) (*AddPostAiCommentRecordResponse, error)
	// 获取帖子的AI评论数
	GetPostAiCommentCount(context.Context, *GetPostAiCommentCountRequest) (*GetPostAiCommentCountResponse, error)
	// 获取帖子评论的ai列表
	GetPostAiCommentList(context.Context, *GetPostAiCommentListRequest) (*GetPostAiCommentListResponse, error)
	// 获取指定性别的AI账号列表
	GetAIAccountByGender(context.Context, *GetAIAccountByGenderRequest) (*GetAIAccountByGenderResponse, error)
	// 更新AI账号房间配置
	UpdateAiRoomCfg(context.Context, *UpdateAiRoomCfgRequest) (*UpdateAiRoomCfgResponse, error)
	// 获取AI账号房间配置
	GetAiRoomCfg(context.Context, *GetAiRoomCfgRequest) (*GetAiRoomCfgResponse, error)
	// 获取运营时间中的房间配置信息
	GetRunningAiRooms(context.Context, *GetRunningAiRoomsRequest) (*GetRunningAiRoomsResponse, error)
	// 记录最近一次接待时间
	RecordReceptionTime(context.Context, *RecordReceptionTimeRequest) (*RecordReceptionTimeResponse, error)
	// 获取最近一次接待
	GetLastReceptionTime(context.Context, *GetLastReceptionTimeRequest) (*GetLastReceptionTimeResponse, error)
	// 记录等待接待用户
	RecordWaitingReceptionUser(context.Context, *RecordWaitingReceptionUserRequest) (*RecordWaitingReceptionUserResponse, error)
	// 获取等待接待用户
	GetWaitingReceptionUser(context.Context, *GetWaitingReceptionUserRequest) (*GetWaitingReceptionUserResponse, error)
	// 记录已接待用户
	RecordReceptionUser(context.Context, *RecordReceptionUserRequest) (*RecordReceptionUserResponse, error)
	// 获取已接待用户
	CheckReceptionUser(context.Context, *CheckReceptionUserRequest) (*CheckReceptionUserResponse, error)
	// 记录当前房间人数
	RecordRoomUserNum(context.Context, *RecordRoomUserNumRequest) (*RecordRoomUserNumResponse, error)
	// 获取当前房间人数
	GetRoomUserNum(context.Context, *GetRoomUserNumRequest) (*GetRoomUserNumResponse, error)
	// 添加运营中的房间
	AddRunningRoom(context.Context, *AddRunningRoomRequest) (*AddRunningRoomResponse, error)
	// 移除运营中的房间
	BatRemoveRunningRoom(context.Context, *BatRemoveRunningRoomRequest) (*BatRemoveRunningRoomResponse, error)
	// 批量获取运营中的房间
	BatGetRunningRoom(context.Context, *BatGetRunningRoomRequest) (*BatGetRunningRoomResponse, error)
	// 批量更新心跳时间
	BatUpdateHeartbeat(context.Context, *BatUpdateHeartbeatRequest) (*BatUpdateHeartbeatResponse, error)
	// 获取需要心跳的房间
	GetNeedHeartbeatRoom(context.Context, *GetNeedHeartbeatRoomRequest) (*GetNeedHeartbeatRoomResponse, error)
	// 从心跳池中移除
	RemoveFromHeartbeatPool(context.Context, *RemoveFromHeartbeatPoolRequest) (*RemoveFromHeartbeatPoolResponse, error)
}

func RegisterAigcAccountServer(s *grpc.Server, srv AigcAccountServer) {
	s.RegisterService(&_AigcAccount_serviceDesc, srv)
}

func _AigcAccount_CreateAIAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAIAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).CreateAIAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/CreateAIAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).CreateAIAccount(ctx, req.(*CreateAIAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_UpdateAIAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAIAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).UpdateAIAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/UpdateAIAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).UpdateAIAccount(ctx, req.(*UpdateAIAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_BatchUnregisterAIAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUnregisterAIAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).BatchUnregisterAIAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/BatchUnregisterAIAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).BatchUnregisterAIAccount(ctx, req.(*BatchUnregisterAIAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetPageAIAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPageAIAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetPageAIAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetPageAIAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetPageAIAccount(ctx, req.(*GetPageAIAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetAIAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAIAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetAIAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetAIAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetAIAccount(ctx, req.(*GetAIAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_BatchGetAIAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAIAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).BatchGetAIAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/BatchGetAIAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).BatchGetAIAccount(ctx, req.(*BatchGetAIAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_IncrChatRound_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncrChatRoundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).IncrChatRound(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/IncrChatRound",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).IncrChatRound(ctx, req.(*IncrChatRoundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetChatRound_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChatRoundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetChatRound(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetChatRound",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetChatRound(ctx, req.(*GetChatRoundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_UpdateChatTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChatTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).UpdateChatTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/UpdateChatTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).UpdateChatTime(ctx, req.(*UpdateChatTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetLastChatTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastChatTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetLastChatTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetLastChatTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetLastChatTime(ctx, req.(*GetLastChatTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_AddAiPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAiPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).AddAiPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/AddAiPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).AddAiPost(ctx, req.(*AddAiPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetAiPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAiPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetAiPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetAiPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetAiPost(ctx, req.(*GetAiPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_AddUserAiChatRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserAiChatRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).AddUserAiChatRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/AddUserAiChatRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).AddUserAiChatRecord(ctx, req.(*AddUserAiChatRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetUserAiChatRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAiChatRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetUserAiChatRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetUserAiChatRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetUserAiChatRecord(ctx, req.(*GetUserAiChatRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_AddUserAiInteractionRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserAiInteractionRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).AddUserAiInteractionRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/AddUserAiInteractionRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).AddUserAiInteractionRecord(ctx, req.(*AddUserAiInteractionRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetUserAiInteractionRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAiInteractionRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetUserAiInteractionRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetUserAiInteractionRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetUserAiInteractionRecord(ctx, req.(*GetUserAiInteractionRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_AddAiLikePost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAiLikePostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).AddAiLikePost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/AddAiLikePost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).AddAiLikePost(ctx, req.(*AddAiLikePostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetAiLikePostCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAiLikePostCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetAiLikePostCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetAiLikePostCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetAiLikePostCount(ctx, req.(*GetAiLikePostCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_AddAiCommentPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAiCommentPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).AddAiCommentPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/AddAiCommentPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).AddAiCommentPost(ctx, req.(*AddAiCommentPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetAiCommentPostCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAiCommentPostCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetAiCommentPostCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetAiCommentPostCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetAiCommentPostCount(ctx, req.(*GetAiCommentPostCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_AddPostAiCommentRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPostAiCommentRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).AddPostAiCommentRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/AddPostAiCommentRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).AddPostAiCommentRecord(ctx, req.(*AddPostAiCommentRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetPostAiCommentCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostAiCommentCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetPostAiCommentCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetPostAiCommentCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetPostAiCommentCount(ctx, req.(*GetPostAiCommentCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetPostAiCommentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostAiCommentListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetPostAiCommentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetPostAiCommentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetPostAiCommentList(ctx, req.(*GetPostAiCommentListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetAIAccountByGender_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAIAccountByGenderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetAIAccountByGender(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetAIAccountByGender",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetAIAccountByGender(ctx, req.(*GetAIAccountByGenderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_UpdateAiRoomCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAiRoomCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).UpdateAiRoomCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/UpdateAiRoomCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).UpdateAiRoomCfg(ctx, req.(*UpdateAiRoomCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetAiRoomCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAiRoomCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetAiRoomCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetAiRoomCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetAiRoomCfg(ctx, req.(*GetAiRoomCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetRunningAiRooms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRunningAiRoomsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetRunningAiRooms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetRunningAiRooms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetRunningAiRooms(ctx, req.(*GetRunningAiRoomsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_RecordReceptionTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordReceptionTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).RecordReceptionTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/RecordReceptionTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).RecordReceptionTime(ctx, req.(*RecordReceptionTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetLastReceptionTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastReceptionTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetLastReceptionTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetLastReceptionTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetLastReceptionTime(ctx, req.(*GetLastReceptionTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_RecordWaitingReceptionUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordWaitingReceptionUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).RecordWaitingReceptionUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/RecordWaitingReceptionUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).RecordWaitingReceptionUser(ctx, req.(*RecordWaitingReceptionUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetWaitingReceptionUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWaitingReceptionUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetWaitingReceptionUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetWaitingReceptionUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetWaitingReceptionUser(ctx, req.(*GetWaitingReceptionUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_RecordReceptionUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordReceptionUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).RecordReceptionUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/RecordReceptionUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).RecordReceptionUser(ctx, req.(*RecordReceptionUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_CheckReceptionUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckReceptionUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).CheckReceptionUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/CheckReceptionUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).CheckReceptionUser(ctx, req.(*CheckReceptionUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_RecordRoomUserNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordRoomUserNumRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).RecordRoomUserNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/RecordRoomUserNum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).RecordRoomUserNum(ctx, req.(*RecordRoomUserNumRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetRoomUserNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRoomUserNumRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetRoomUserNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetRoomUserNum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetRoomUserNum(ctx, req.(*GetRoomUserNumRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_AddRunningRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRunningRoomRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).AddRunningRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/AddRunningRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).AddRunningRoom(ctx, req.(*AddRunningRoomRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_BatRemoveRunningRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatRemoveRunningRoomRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).BatRemoveRunningRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/BatRemoveRunningRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).BatRemoveRunningRoom(ctx, req.(*BatRemoveRunningRoomRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_BatGetRunningRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetRunningRoomRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).BatGetRunningRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/BatGetRunningRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).BatGetRunningRoom(ctx, req.(*BatGetRunningRoomRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_BatUpdateHeartbeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatUpdateHeartbeatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).BatUpdateHeartbeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/BatUpdateHeartbeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).BatUpdateHeartbeat(ctx, req.(*BatUpdateHeartbeatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_GetNeedHeartbeatRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNeedHeartbeatRoomRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).GetNeedHeartbeatRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/GetNeedHeartbeatRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).GetNeedHeartbeatRoom(ctx, req.(*GetNeedHeartbeatRoomRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccount_RemoveFromHeartbeatPool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveFromHeartbeatPoolRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountServer).RemoveFromHeartbeatPool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account.AigcAccount/RemoveFromHeartbeatPool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountServer).RemoveFromHeartbeatPool(ctx, req.(*RemoveFromHeartbeatPoolRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _AigcAccount_serviceDesc = grpc.ServiceDesc{
	ServiceName: "aigc_account.AigcAccount",
	HandlerType: (*AigcAccountServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAIAccount",
			Handler:    _AigcAccount_CreateAIAccount_Handler,
		},
		{
			MethodName: "UpdateAIAccount",
			Handler:    _AigcAccount_UpdateAIAccount_Handler,
		},
		{
			MethodName: "BatchUnregisterAIAccount",
			Handler:    _AigcAccount_BatchUnregisterAIAccount_Handler,
		},
		{
			MethodName: "GetPageAIAccount",
			Handler:    _AigcAccount_GetPageAIAccount_Handler,
		},
		{
			MethodName: "GetAIAccount",
			Handler:    _AigcAccount_GetAIAccount_Handler,
		},
		{
			MethodName: "BatchGetAIAccount",
			Handler:    _AigcAccount_BatchGetAIAccount_Handler,
		},
		{
			MethodName: "IncrChatRound",
			Handler:    _AigcAccount_IncrChatRound_Handler,
		},
		{
			MethodName: "GetChatRound",
			Handler:    _AigcAccount_GetChatRound_Handler,
		},
		{
			MethodName: "UpdateChatTime",
			Handler:    _AigcAccount_UpdateChatTime_Handler,
		},
		{
			MethodName: "GetLastChatTime",
			Handler:    _AigcAccount_GetLastChatTime_Handler,
		},
		{
			MethodName: "AddAiPost",
			Handler:    _AigcAccount_AddAiPost_Handler,
		},
		{
			MethodName: "GetAiPost",
			Handler:    _AigcAccount_GetAiPost_Handler,
		},
		{
			MethodName: "AddUserAiChatRecord",
			Handler:    _AigcAccount_AddUserAiChatRecord_Handler,
		},
		{
			MethodName: "GetUserAiChatRecord",
			Handler:    _AigcAccount_GetUserAiChatRecord_Handler,
		},
		{
			MethodName: "AddUserAiInteractionRecord",
			Handler:    _AigcAccount_AddUserAiInteractionRecord_Handler,
		},
		{
			MethodName: "GetUserAiInteractionRecord",
			Handler:    _AigcAccount_GetUserAiInteractionRecord_Handler,
		},
		{
			MethodName: "AddAiLikePost",
			Handler:    _AigcAccount_AddAiLikePost_Handler,
		},
		{
			MethodName: "GetAiLikePostCount",
			Handler:    _AigcAccount_GetAiLikePostCount_Handler,
		},
		{
			MethodName: "AddAiCommentPost",
			Handler:    _AigcAccount_AddAiCommentPost_Handler,
		},
		{
			MethodName: "GetAiCommentPostCount",
			Handler:    _AigcAccount_GetAiCommentPostCount_Handler,
		},
		{
			MethodName: "AddPostAiCommentRecord",
			Handler:    _AigcAccount_AddPostAiCommentRecord_Handler,
		},
		{
			MethodName: "GetPostAiCommentCount",
			Handler:    _AigcAccount_GetPostAiCommentCount_Handler,
		},
		{
			MethodName: "GetPostAiCommentList",
			Handler:    _AigcAccount_GetPostAiCommentList_Handler,
		},
		{
			MethodName: "GetAIAccountByGender",
			Handler:    _AigcAccount_GetAIAccountByGender_Handler,
		},
		{
			MethodName: "UpdateAiRoomCfg",
			Handler:    _AigcAccount_UpdateAiRoomCfg_Handler,
		},
		{
			MethodName: "GetAiRoomCfg",
			Handler:    _AigcAccount_GetAiRoomCfg_Handler,
		},
		{
			MethodName: "GetRunningAiRooms",
			Handler:    _AigcAccount_GetRunningAiRooms_Handler,
		},
		{
			MethodName: "RecordReceptionTime",
			Handler:    _AigcAccount_RecordReceptionTime_Handler,
		},
		{
			MethodName: "GetLastReceptionTime",
			Handler:    _AigcAccount_GetLastReceptionTime_Handler,
		},
		{
			MethodName: "RecordWaitingReceptionUser",
			Handler:    _AigcAccount_RecordWaitingReceptionUser_Handler,
		},
		{
			MethodName: "GetWaitingReceptionUser",
			Handler:    _AigcAccount_GetWaitingReceptionUser_Handler,
		},
		{
			MethodName: "RecordReceptionUser",
			Handler:    _AigcAccount_RecordReceptionUser_Handler,
		},
		{
			MethodName: "CheckReceptionUser",
			Handler:    _AigcAccount_CheckReceptionUser_Handler,
		},
		{
			MethodName: "RecordRoomUserNum",
			Handler:    _AigcAccount_RecordRoomUserNum_Handler,
		},
		{
			MethodName: "GetRoomUserNum",
			Handler:    _AigcAccount_GetRoomUserNum_Handler,
		},
		{
			MethodName: "AddRunningRoom",
			Handler:    _AigcAccount_AddRunningRoom_Handler,
		},
		{
			MethodName: "BatRemoveRunningRoom",
			Handler:    _AigcAccount_BatRemoveRunningRoom_Handler,
		},
		{
			MethodName: "BatGetRunningRoom",
			Handler:    _AigcAccount_BatGetRunningRoom_Handler,
		},
		{
			MethodName: "BatUpdateHeartbeat",
			Handler:    _AigcAccount_BatUpdateHeartbeat_Handler,
		},
		{
			MethodName: "GetNeedHeartbeatRoom",
			Handler:    _AigcAccount_GetNeedHeartbeatRoom_Handler,
		},
		{
			MethodName: "RemoveFromHeartbeatPool",
			Handler:    _AigcAccount_RemoveFromHeartbeatPool_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/aigc/aigc-account/aigc-account.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/aigc/aigc-account/aigc-account.proto", fileDescriptor_aigc_account_6b87c18fd40d3bb3)
}

var fileDescriptor_aigc_account_6b87c18fd40d3bb3 = []byte{
	// 3270 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x3b, 0x5d, 0x73, 0xd4, 0xc6,
	0x96, 0x1e, 0x0f, 0xd8, 0x9e, 0x33, 0x1e, 0x33, 0x6e, 0x30, 0x1e, 0x0f, 0x01, 0x1b, 0x01, 0xc1,
	0x49, 0xc0, 0x10, 0xd8, 0x64, 0xb3, 0x24, 0xbb, 0x64, 0xfc, 0x81, 0x33, 0x8b, 0x6d, 0x58, 0xd9,
	0x0e, 0x55, 0xd4, 0xa6, 0xb4, 0xb2, 0xd4, 0x8c, 0xbb, 0xac, 0x91, 0x06, 0xb5, 0xc6, 0x84, 0x6c,
	0xed, 0xc3, 0x3e, 0x6e, 0xd5, 0xbe, 0xdc, 0x5f, 0x70, 0xff, 0xc9, 0x7d, 0xbb, 0xff, 0xe4, 0xfe,
	0x81, 0xfb, 0x7c, 0x5f, 0x6e, 0xf5, 0xe9, 0x96, 0x46, 0x6a, 0x49, 0x63, 0x07, 0xa8, 0xfb, 0x42,
	0x8d, 0x4e, 0x9f, 0x3e, 0x1f, 0x7d, 0xbe, 0xba, 0xcf, 0xc1, 0xf0, 0x38, 0x8a, 0x1e, 0xbc, 0x1d,
	0x32, 0xe7, 0x84, 0x33, 0xef, 0x94, 0x86, 0x0f, 0x6c, 0xd6, 0x73, 0xf0, 0x9f, 0xfb, 0xb6, 0xe3,
	0x04, 0x43, 0x3f, 0xca, 0x7c, 0xac, 0x0d, 0xc2, 0x20, 0x0a, 0xc8, 0xac, 0x80, 0x59, 0x0a, 0x66,
	0xfc, 0xb5, 0x0a, 0xb5, 0x4e, 0xb7, 0x23, 0xbf, 0x48, 0x13, 0xaa, 0x43, 0xe6, 0xb6, 0x2a, 0x2b,
	0x95, 0xd5, 0x86, 0x29, 0x7e, 0x92, 0x36, 0xcc, 0x0c, 0x6c, 0xce, 0xdf, 0x05, 0xa1, 0xdb, 0x9a,
	0x5c, 0xa9, 0xac, 0xd6, 0xcc, 0xe4, 0x9b, 0xcc, 0xc1, 0x24, 0x1b, 0xb4, 0xaa, 0x08, 0x9d, 0x64,
	0x03, 0x72, 0x0d, 0x6a, 0x83, 0x30, 0xe8, 0x0f, 0x22, 0x8b, 0xb9, 0xad, 0x0b, 0x48, 0x63, 0x46,
	0x02, 0xba, 0xae, 0x58, 0x8c, 0x58, 0xff, 0x28, 0xa4, 0x62, 0xf1, 0xa2, 0x5c, 0x94, 0x80, 0xae,
	0x4b, 0x16, 0x61, 0x3a, 0x0c, 0x3c, 0x5c, 0x9a, 0xc2, 0xa5, 0x29, 0xf1, 0xd9, 0x95, 0xec, 0xc3,
	0xc0, 0x0b, 0x7a, 0x43, 0xda, 0x9a, 0x56, 0xec, 0xd5, 0x37, 0xb9, 0x05, 0x0d, 0xc6, 0xad, 0xa1,
	0x1f, 0xd2, 0x1e, 0xe3, 0x11, 0x0d, 0x5b, 0x33, 0x2b, 0x95, 0xd5, 0x19, 0x73, 0x96, 0xf1, 0xc3,
	0x04, 0x46, 0x96, 0xa1, 0xee, 0x84, 0xd4, 0x8e, 0xa8, 0x15, 0xb1, 0x3e, 0x6d, 0xd5, 0x56, 0x2a,
	0xab, 0x55, 0x13, 0x24, 0xe8, 0x80, 0xf5, 0xa9, 0x40, 0x18, 0x0e, 0xdc, 0x04, 0x01, 0x24, 0x82,
	0x04, 0x21, 0xc2, 0x43, 0x58, 0x60, 0xdc, 0x62, 0xbe, 0x15, 0x32, 0x7e, 0x62, 0xbd, 0x3b, 0x66,
	0x11, 0xb5, 0x3c, 0xc6, 0xa3, 0x56, 0x1d, 0xd9, 0xcd, 0x33, 0xde, 0xf5, 0x4d, 0xc6, 0x4f, 0x5e,
	0x89, 0x95, 0x1d, 0xc6, 0x23, 0x21, 0x34, 0x73, 0xa9, 0x1f, 0xb1, 0xe8, 0x7d, 0x6b, 0x56, 0x0a,
	0x1d, 0x7f, 0x13, 0x02, 0x17, 0x5c, 0xca, 0x9d, 0x56, 0x03, 0xe1, 0xf8, 0x5b, 0xc0, 0x78, 0x10,
	0x46, 0xad, 0x39, 0x54, 0x1d, 0x7f, 0x93, 0x9b, 0x30, 0xab, 0x4c, 0x64, 0x45, 0x76, 0x8f, 0xb7,
	0x2e, 0xad, 0x54, 0x57, 0x6b, 0x66, 0x5d, 0xc1, 0x0e, 0xec, 0x1e, 0x27, 0xff, 0x0c, 0x4b, 0x8c,
	0x5b, 0xfc, 0x38, 0x78, 0x27, 0xa4, 0x73, 0x8e, 0xed, 0xc8, 0x72, 0xec, 0xd0, 0xb5, 0xde, 0xd9,
	0x9e, 0xd7, 0x6a, 0xa2, 0x70, 0x57, 0x18, 0xdf, 0x3f, 0x0e, 0xde, 0x75, 0xfd, 0x8d, 0x63, 0x3b,
	0xda, 0xb0, 0x43, 0xf7, 0x95, 0xed, 0x79, 0xc6, 0x73, 0xb8, 0xba, 0x81, 0x07, 0x90, 0x18, 0xde,
	0xa4, 0x6f, 0x87, 0x94, 0x47, 0xe4, 0x6b, 0x98, 0x56, 0x1c, 0xd0, 0x07, 0xea, 0x8f, 0x16, 0xd7,
	0xd2, 0xde, 0xb2, 0x36, 0xda, 0x10, 0xe3, 0x19, 0x4b, 0xb0, 0x98, 0x23, 0xc6, 0x07, 0x81, 0xcf,
	0xa9, 0xe0, 0x73, 0x88, 0xe7, 0xf8, 0x89, 0xf8, 0xe4, 0x88, 0x29, 0x3e, 0x3f, 0xc0, 0xf2, 0xba,
	0x1d, 0x39, 0xc7, 0x23, 0xb3, 0xe7, 0x18, 0x2e, 0xc1, 0xcc, 0x90, 0xb9, 0xd2, 0x6e, 0x95, 0x95,
	0xea, 0x6a, 0xc3, 0x9c, 0x1e, 0x32, 0x57, 0x58, 0xcb, 0x30, 0x60, 0xa5, 0x7c, 0xb7, 0xe2, 0xf0,
	0x3f, 0xb0, 0xb8, 0x4d, 0xa3, 0x97, 0x76, 0x2f, 0xaf, 0x0a, 0x81, 0x0b, 0x03, 0xbb, 0x47, 0x55,
	0xcc, 0xe0, 0x6f, 0x34, 0x28, 0xfb, 0x8d, 0x62, 0xc0, 0x08, 0x83, 0xb2, 0xdf, 0x28, 0x79, 0x02,
	0xed, 0xac, 0x89, 0xa4, 0xe5, 0x78, 0x64, 0x47, 0x43, 0x8e, 0x41, 0xd4, 0x30, 0xaf, 0x3a, 0x29,
	0x33, 0x09, 0xc3, 0xed, 0xe3, 0xaa, 0xe1, 0x41, 0x2b, 0xcf, 0x5e, 0x8a, 0x46, 0x9e, 0x8c, 0x1c,
	0x25, 0xd1, 0x6e, 0xcc, 0x79, 0xc6, 0x1e, 0x84, 0x8e, 0x7a, 0x05, 0x2e, 0x46, 0x41, 0x64, 0x7b,
	0x4a, 0x50, 0xf9, 0x61, 0x1c, 0xc3, 0xe5, 0x6d, 0x1a, 0xe5, 0x14, 0xcd, 0xe7, 0x86, 0xa7, 0x00,
	0x21, 0x7d, 0x6b, 0xf1, 0x60, 0x18, 0x3a, 0x52, 0xd9, 0xb9, 0x47, 0x2b, 0x59, 0xc6, 0x69, 0x42,
	0xfb, 0x88, 0x67, 0xd6, 0x42, 0xfa, 0x56, 0xfe, 0x34, 0xba, 0x70, 0x25, 0xcb, 0x49, 0xe9, 0xf4,
	0x01, 0xee, 0x71, 0x0a, 0x2d, 0xb4, 0x62, 0x91, 0xe4, 0xe5, 0xc6, 0xff, 0x78, 0x15, 0x5e, 0xc1,
	0x52, 0x01, 0xdf, 0x8f, 0xb7, 0x8d, 0xf1, 0x14, 0xae, 0x74, 0x7d, 0x27, 0x14, 0x81, 0x6b, 0x06,
	0x43, 0xdf, 0x2d, 0x37, 0xc3, 0x02, 0x4c, 0xd9, 0xcc, 0x12, 0x40, 0x65, 0x46, 0x9b, 0x1d, 0x32,
	0xd7, 0x58, 0x84, 0x05, 0x8d, 0x80, 0x72, 0xe6, 0x5f, 0xd0, 0xbe, 0x1f, 0x4c, 0x98, 0x5c, 0x07,
	0xf0, 0x29, 0x75, 0x2d, 0xe9, 0x3a, 0x55, 0x4c, 0x34, 0x35, 0x01, 0x39, 0x40, 0xf7, 0x39, 0x40,
	0xa3, 0xe6, 0xd8, 0x8a, 0x02, 0xe0, 0x0c, 0x43, 0x2b, 0x14, 0x40, 0xc5, 0x65, 0xc6, 0x19, 0x86,
	0x88, 0x24, 0xb2, 0x30, 0x92, 0x53, 0xcb, 0x92, 0x1f, 0x20, 0x08, 0x11, 0x8c, 0x1f, 0x61, 0x41,
	0x86, 0xbf, 0x20, 0x2c, 0xf2, 0xf2, 0xef, 0x3e, 0x8f, 0x56, 0x9c, 0x8d, 0x46, 0x14, 0xd4, 0x81,
	0x74, 0xe0, 0xea, 0x36, 0x8d, 0x76, 0x6c, 0x1e, 0x7d, 0x30, 0xf1, 0xa7, 0x98, 0x20, 0xb2, 0x24,
	0x94, 0xde, 0xb7, 0x61, 0xce, 0xb3, 0x79, 0x24, 0x13, 0x34, 0xd6, 0x98, 0x0a, 0xd6, 0x98, 0x59,
	0x2f, 0x85, 0x6d, 0xfc, 0x2b, 0x34, 0x3b, 0xae, 0xdb, 0x61, 0x2f, 0x03, 0x3e, 0x26, 0xe2, 0x16,
	0x61, 0x7a, 0x10, 0x70, 0xac, 0xaf, 0xb2, 0x18, 0x4f, 0x89, 0xcf, 0xae, 0x6b, 0x5c, 0x86, 0xf9,
	0xd4, 0x76, 0xa5, 0xd7, 0x7f, 0x40, 0x53, 0xb8, 0x65, 0x86, 0xe6, 0x79, 0xd3, 0xd5, 0x48, 0xcf,
	0x2a, 0x46, 0x8c, 0xd2, 0xf3, 0x39, 0xcc, 0xa7, 0x48, 0x2a, 0x0d, 0x97, 0x60, 0x46, 0x49, 0xc5,
	0xd1, 0xc5, 0x6b, 0xe6, 0xb4, 0x14, 0x8b, 0x63, 0xd5, 0x47, 0xbb, 0xfa, 0xc3, 0xbe, 0xa2, 0x3f,
	0x83, 0x80, 0xbd, 0x61, 0xdf, 0xd8, 0x83, 0x76, 0xc7, 0x75, 0x0f, 0x39, 0x0d, 0x3b, 0x0c, 0xfd,
	0x85, 0x3a, 0x41, 0xe8, 0xa6, 0xa2, 0xf6, 0x4d, 0x18, 0xf4, 0xad, 0xd1, 0x11, 0x4c, 0x8b, 0xef,
	0x43, 0x69, 0x84, 0x28, 0x48, 0x1b, 0x21, 0x0a, 0x84, 0x70, 0xd7, 0xe1, 0x5a, 0x21, 0x3d, 0x75,
	0x1c, 0x7b, 0xd0, 0xde, 0xa6, 0xd1, 0xa7, 0x63, 0xf7, 0x6f, 0x70, 0xad, 0x90, 0x9e, 0x3a, 0x95,
	0x65, 0xa8, 0x87, 0x08, 0x49, 0x1b, 0x1d, 0x24, 0x08, 0x4d, 0xbe, 0x03, 0x37, 0x13, 0x71, 0xbb,
	0x7e, 0x44, 0x43, 0xdb, 0x89, 0x58, 0xe0, 0x67, 0xc5, 0x3a, 0xb7, 0x07, 0xde, 0x06, 0x63, 0x1c,
	0x35, 0x75, 0x06, 0xdf, 0xc0, 0xcd, 0x44, 0xe6, 0xf3, 0xf3, 0x34, 0x36, 0xc1, 0x18, 0xb7, 0x4d,
	0x69, 0x7c, 0x03, 0xea, 0x52, 0xb2, 0x74, 0xaa, 0xad, 0xa1, 0x78, 0x98, 0xd2, 0x3a, 0x70, 0x05,
	0x9d, 0x74, 0x87, 0x9d, 0xd0, 0x0f, 0xf4, 0xf3, 0x45, 0x58, 0xd0, 0x48, 0x28, 0xc5, 0xee, 0xc3,
	0x12, 0x3a, 0x66, 0xbc, 0xb0, 0x31, 0xb6, 0x74, 0x19, 0x9b, 0xe8, 0x0b, 0x39, 0x74, 0xa5, 0xc8,
	0xe7, 0x70, 0xc9, 0x63, 0x27, 0xd4, 0x42, 0x19, 0x46, 0x75, 0xa8, 0x61, 0x36, 0xbc, 0x34, 0xbe,
	0xb1, 0x09, 0x8b, 0x28, 0xcd, 0x46, 0xd0, 0xef, 0x53, 0x3f, 0xfa, 0x40, 0x9d, 0xda, 0xd0, 0xca,
	0x53, 0x51, 0x6a, 0x3d, 0x84, 0xcf, 0x50, 0xce, 0xd4, 0xda, 0x19, 0x9a, 0xed, 0xc2, 0xf5, 0x92,
	0x1d, 0x4a, 0xb9, 0x7b, 0x40, 0x1c, 0xb9, 0x96, 0xd7, 0xaf, 0xe9, 0x68, 0xbb, 0x8c, 0x17, 0x70,
	0xbd, 0xe3, 0xba, 0xe2, 0x3b, 0x21, 0x99, 0x75, 0x96, 0x94, 0x5a, 0x95, 0xb4, 0x5a, 0x65, 0x7e,
	0xba, 0x02, 0x37, 0xca, 0x08, 0x26, 0x71, 0x2a, 0x74, 0xce, 0x60, 0x64, 0x74, 0xfe, 0xbd, 0x1c,
	0x37, 0xf1, 0x44, 0x8a, 0xe8, 0xa9, 0x13, 0xb9, 0x05, 0x8d, 0xf8, 0x44, 0xd2, 0x87, 0x31, 0xeb,
	0xa4, 0x90, 0x8d, 0x6f, 0x31, 0xda, 0x33, 0x54, 0x84, 0x53, 0x9f, 0x25, 0x94, 0xe1, 0xe4, 0xb5,
	0x91, 0xfb, 0x14, 0xf3, 0x0d, 0xb8, 0x64, 0x33, 0x2b, 0xe6, 0x9f, 0xba, 0x26, 0x5c, 0xd3, 0xae,
	0x09, 0xf1, 0xee, 0x4d, 0x3b, 0xb2, 0xcd, 0x86, 0x9d, 0x26, 0x66, 0x7c, 0x07, 0x8d, 0xcc, 0x7a,
	0xea, 0x28, 0x2a, 0xe9, 0xd2, 0xdd, 0x84, 0xea, 0x28, 0x11, 0x8b, 0x9f, 0xc6, 0x16, 0xaa, 0x95,
	0xdc, 0x41, 0xd6, 0xdf, 0x6f, 0x53, 0xdf, 0xa5, 0x61, 0xca, 0xbf, 0x38, 0xfd, 0x15, 0x89, 0x5c,
	0x34, 0xc5, 0x4f, 0x71, 0x67, 0xf4, 0x58, 0x9f, 0x45, 0x48, 0xe4, 0xa2, 0x29, 0x3f, 0x8c, 0xd7,
	0xd2, 0x4f, 0xf3, 0x64, 0x3e, 0xc1, 0x4d, 0xe8, 0x6f, 0x93, 0x50, 0x37, 0x83, 0xa0, 0xbf, 0x6e,
	0x73, 0xba, 0xf1, 0xa6, 0x27, 0x64, 0x72, 0x46, 0x3e, 0xef, 0xa8, 0x04, 0x6d, 0x1f, 0x59, 0xa9,
	0x04, 0x6d, 0x1f, 0x75, 0x5d, 0x72, 0x07, 0xe6, 0x9c, 0x63, 0xdb, 0xf7, 0xa9, 0x67, 0xd9, 0xa7,
	0x76, 0x64, 0x87, 0xea, 0xad, 0xda, 0x50, 0xd0, 0x0e, 0x02, 0xc5, 0x53, 0x2b, 0x46, 0xf3, 0xed,
	0x3e, 0xc5, 0x97, 0x6b, 0xcd, 0xac, 0x2b, 0xd8, 0x9e, 0x2d, 0x1f, 0x89, 0xef, 0xa8, 0xe7, 0x04,
	0x7d, 0x6a, 0xf5, 0x79, 0x0f, 0x9f, 0xaf, 0x35, 0x13, 0x14, 0x68, 0x97, 0xf7, 0xc8, 0x1a, 0x5c,
	0x8e, 0x69, 0x44, 0xc1, 0x80, 0x39, 0x56, 0xc4, 0x22, 0x8f, 0xe2, 0x63, 0xb6, 0x66, 0xce, 0xab,
	0xa5, 0x03, 0xb1, 0x72, 0x20, 0x16, 0xc8, 0x43, 0xb8, 0x92, 0xc5, 0x77, 0x69, 0x64, 0x33, 0x4f,
	0xbd, 0x71, 0x49, 0x7a, 0xc3, 0x26, 0xae, 0x90, 0xfb, 0x10, 0x43, 0x2d, 0x3e, 0x1c, 0xd0, 0xf0,
	0x94, 0xf1, 0x40, 0x3e, 0x79, 0x1b, 0x09, 0x83, 0xfd, 0x64, 0x01, 0x7d, 0x3a, 0xd6, 0xdd, 0xed,
	0x33, 0xbf, 0x55, 0xc3, 0x6c, 0x1c, 0x6b, 0xda, 0x11, 0x30, 0x62, 0x40, 0xc3, 0x0b, 0x9c, 0x13,
	0xab, 0xcf, 0x1c, 0x69, 0x16, 0x40, 0xa4, 0xba, 0x00, 0xee, 0x32, 0x07, 0x4f, 0xff, 0x3f, 0xa1,
	0xbe, 0x2e, 0xbe, 0x5f, 0x0c, 0x44, 0xc6, 0x17, 0x65, 0xf2, 0x08, 0xf7, 0x8c, 0xca, 0x24, 0x7e,
	0xcb, 0x47, 0x3c, 0xf5, 0x68, 0x7f, 0x64, 0x86, 0x29, 0xf1, 0xd9, 0x75, 0xc5, 0x1e, 0x5c, 0x38,
	0x55, 0xd7, 0xc5, 0x9a, 0x89, 0x88, 0x3f, 0xdb, 0x9e, 0xb1, 0x05, 0x35, 0xbc, 0x2c, 0xd9, 0x7e,
	0x8f, 0x8a, 0x8b, 0x25, 0x8f, 0xec, 0x30, 0x73, 0x4b, 0xaa, 0x21, 0x04, 0x1f, 0xe2, 0x82, 0x8c,
	0xaf, 0xaa, 0xe9, 0x24, 0x2e, 0x4e, 0x53, 0x5f, 0x96, 0xd2, 0xbf, 0x54, 0xa1, 0xbe, 0x3e, 0xe4,
	0xcc, 0xa7, 0x9c, 0x0b, 0x17, 0xb9, 0x0b, 0x4d, 0xe1, 0xfe, 0x9c, 0xa2, 0x6a, 0xcc, 0x77, 0x95,
	0x0f, 0x37, 0x44, 0xe0, 0x1c, 0x72, 0xba, 0xcb, 0x9c, 0xae, 0x00, 0x92, 0xaf, 0x61, 0xc1, 0x1e,
	0x46, 0x81, 0xe5, 0x32, 0xde, 0x67, 0x9c, 0x8b, 0x2d, 0x61, 0xea, 0xae, 0x42, 0xc4, 0xe2, 0xa6,
	0x5c, 0x13, 0xd5, 0x6f, 0x6f, 0xd8, 0x4f, 0xb6, 0x0c, 0x86, 0x47, 0x1e, 0xe3, 0xc7, 0xa3, 0x2d,
	0xd5, 0xd1, 0x96, 0x97, 0x72, 0x2d, 0xde, 0xf2, 0x23, 0x5c, 0x0a, 0x06, 0x34, 0xb4, 0xc5, 0x09,
	0xa2, 0xfc, 0xbc, 0x75, 0xa1, 0x28, 0x00, 0x92, 0xa3, 0x30, 0xe7, 0x12, 0x7c, 0x01, 0xe3, 0xe4,
	0x07, 0x68, 0xc4, 0xfc, 0xe4, 0xfe, 0x8b, 0xe3, 0xf7, 0xcf, 0x2a, 0x6c, 0xb9, 0x7b, 0x19, 0x62,
	0x6f, 0xb6, 0x8e, 0x7a, 0x7d, 0xe5, 0x95, 0xa0, 0x40, 0xeb, 0xbd, 0x3e, 0x59, 0x87, 0x4b, 0x31,
	0xf9, 0x00, 0xed, 0xcc, 0x5b, 0xd3, 0xc8, 0x60, 0x29, 0xcb, 0x20, 0xe5, 0x09, 0xe6, 0x9c, 0xda,
	0x21, 0x3f, 0x79, 0xb6, 0xfb, 0x33, 0xa3, 0x75, 0x7f, 0xee, 0x01, 0x09, 0xa9, 0x43, 0x11, 0x55,
	0x84, 0x90, 0x74, 0xb7, 0x1a, 0x5e, 0x16, 0x9b, 0xc9, 0xca, 0x2e, 0xef, 0xe1, 0xab, 0xec, 0x2a,
	0x4c, 0x71, 0x27, 0x64, 0x03, 0xe9, 0x90, 0x35, 0x53, 0x7d, 0x19, 0x7f, 0xa8, 0x40, 0xad, 0xc3,
	0x44, 0x2e, 0x50, 0x79, 0x40, 0x2b, 0xb1, 0xff, 0x04, 0x33, 0x47, 0x36, 0xa7, 0x96, 0xf3, 0xa6,
	0x87, 0x06, 0xcc, 0xc9, 0x9f, 0x4a, 0x23, 0xe6, 0xf4, 0x91, 0xca, 0x27, 0x3f, 0xc0, 0xec, 0x91,
	0xf2, 0x1d, 0xdc, 0x59, 0x2d, 0xda, 0x99, 0xf2, 0x2e, 0xb3, 0x7e, 0x34, 0xfa, 0x30, 0x76, 0x92,
	0x26, 0x47, 0x2c, 0x58, 0x9c, 0x3b, 0x1f, 0xc1, 0x4c, 0x18, 0x04, 0x7d, 0xa4, 0x59, 0xfc, 0x8c,
	0x4d, 0x76, 0x4c, 0x87, 0xf2, 0x47, 0xaa, 0xcb, 0x31, 0xa2, 0xa6, 0xca, 0xe2, 0x5d, 0xf9, 0x2c,
	0xd7, 0xb9, 0xe4, 0x6f, 0x00, 0xff, 0x2e, 0x5f, 0xd5, 0x3a, 0x81, 0x0f, 0x92, 0xe7, 0x39, 0x76,
	0x1e, 0xcc, 0xa1, 0xef, 0x33, 0xbf, 0x27, 0xd7, 0x79, 0xea, 0xc6, 0x2c, 0x1f, 0x36, 0xa3, 0x54,
	0x80, 0x4f, 0x1a, 0xe6, 0x66, 0x8b, 0x44, 0x23, 0x2e, 0x12, 0xef, 0xf1, 0x8e, 0xa6, 0x13, 0x53,
	0xd2, 0x7d, 0x0f, 0x8d, 0x58, 0xba, 0x71, 0x25, 0x22, 0x11, 0xb1, 0xae, 0x44, 0x44, 0x87, 0x59,
	0x86, 0xba, 0x17, 0xd8, 0xae, 0xf5, 0x86, 0xf9, 0x8c, 0x1f, 0x23, 0xd7, 0x19, 0x13, 0x04, 0xe8,
	0x19, 0x42, 0x8c, 0xf7, 0xd0, 0x8e, 0x6f, 0x19, 0xca, 0xd7, 0xd2, 0xcf, 0xbc, 0x92, 0x6a, 0x79,
	0x1d, 0xe2, 0x18, 0x19, 0xe5, 0xb4, 0x9a, 0x82, 0xc8, 0xf2, 0x32, 0xf2, 0x69, 0xcc, 0x4a, 0x55,
	0xcc, 0x4a, 0x8d, 0x30, 0xcd, 0x43, 0xbc, 0x4a, 0x0a, 0x59, 0x2b, 0xb3, 0xee, 0x63, 0x01, 0x16,
	0x2f, 0xc7, 0x4f, 0x27, 0x9a, 0xba, 0x42, 0x15, 0x10, 0x55, 0x87, 0xbd, 0x06, 0x97, 0xd1, 0x74,
	0x9a, 0xfc, 0x32, 0xe5, 0xce, 0x7b, 0xfa, 0x3e, 0xe3, 0xff, 0x2b, 0x70, 0x53, 0x2a, 0xf1, 0xca,
	0x66, 0x11, 0xf3, 0x7b, 0xc9, 0xb2, 0x48, 0x71, 0x1f, 0x77, 0x8c, 0xca, 0x81, 0xab, 0xa3, 0x30,
	0xd6, 0x5e, 0x4e, 0x17, 0x72, 0x2f, 0xa7, 0xdb, 0x60, 0x8c, 0x93, 0x46, 0x9d, 0xec, 0xcf, 0x70,
	0x63, 0x9b, 0x46, 0x9f, 0x5c, 0x60, 0xe3, 0x2d, 0x2c, 0x97, 0xd2, 0x55, 0xe7, 0xbb, 0x07, 0xf3,
	0xef, 0xe4, 0xba, 0x2c, 0x0f, 0x29, 0x87, 0x36, 0xb2, 0x0e, 0x5d, 0x48, 0xe6, 0x92, 0xda, 0x2c,
	0x3e, 0xb0, 0x08, 0x77, 0xe1, 0x4a, 0x11, 0x62, 0x41, 0x0a, 0xd4, 0xce, 0x6e, 0x32, 0x77, 0x76,
	0xfd, 0x5c, 0x24, 0x7c, 0xbc, 0x09, 0xd3, 0x0d, 0xb6, 0x6a, 0xb6, 0xbb, 0x9a, 0xf7, 0xfe, 0x8c,
	0x8d, 0x3c, 0x58, 0xda, 0x38, 0xa6, 0xce, 0xc9, 0x3f, 0x46, 0x98, 0x3f, 0x55, 0xa0, 0x5d, 0xc4,
	0x4e, 0x59, 0xcd, 0x82, 0x46, 0xaa, 0x48, 0xd9, 0x03, 0x65, 0xb1, 0x27, 0x59, 0x8b, 0x95, 0x13,
	0x58, 0x4b, 0xa0, 0xbb, 0xf6, 0x60, 0xcb, 0x8f, 0xc2, 0xf7, 0xe6, 0x6c, 0x98, 0x02, 0xb5, 0x9f,
	0xc2, 0x7c, 0x0e, 0x45, 0xd8, 0xf0, 0x84, 0xbe, 0x8f, 0x6d, 0x78, 0x42, 0xdf, 0x8b, 0xec, 0x79,
	0x6a, 0x7b, 0x43, 0xaa, 0xf2, 0x98, 0xfc, 0x78, 0x32, 0xf9, 0x5d, 0xc5, 0x38, 0x81, 0x96, 0x3a,
	0xcd, 0x20, 0xe8, 0xab, 0xdb, 0xc5, 0xc7, 0x9f, 0x56, 0xf6, 0x02, 0x33, 0x3d, 0x94, 0x74, 0x8d,
	0x6b, 0xb0, 0x54, 0xc0, 0x4c, 0x19, 0x6e, 0x17, 0x16, 0x44, 0x2e, 0xff, 0x44, 0x62, 0x18, 0x8f,
	0xb1, 0x05, 0x57, 0xc0, 0x28, 0x23, 0x60, 0x25, 0x2b, 0xe0, 0x6b, 0x6c, 0x06, 0xa8, 0x7a, 0x22,
	0xf6, 0xa6, 0x6a, 0xa2, 0xf6, 0x42, 0x50, 0x81, 0x32, 0x39, 0x0a, 0x94, 0xeb, 0x00, 0xf4, 0xd7,
	0x01, 0x0b, 0xa9, 0x6b, 0xd9, 0x91, 0xca, 0xdc, 0x35, 0x05, 0xe9, 0x44, 0x46, 0x0b, 0xae, 0xea,
	0xb4, 0x95, 0xe6, 0x6b, 0x30, 0x23, 0xbe, 0xbb, 0xfe, 0x9b, 0xe0, 0x3c, 0x8c, 0x0c, 0x13, 0xae,
	0xad, 0xdb, 0x91, 0x49, 0xfb, 0xc1, 0x29, 0x2d, 0x90, 0xf5, 0x31, 0xd4, 0xb0, 0xee, 0xa5, 0x52,
	0xc4, 0xd5, 0xfc, 0xa5, 0x45, 0x70, 0x33, 0xb1, 0x7c, 0xa3, 0x23, 0xdf, 0x80, 0xcf, 0x8a, 0x69,
	0x2a, 0x19, 0x5f, 0x60, 0x37, 0x7c, 0x54, 0x6c, 0x3f, 0x9a, 0xe1, 0x9f, 0x2b, 0xd8, 0xe7, 0xd6,
	0x29, 0x2a, 0x1b, 0x51, 0x68, 0x22, 0xc9, 0x50, 0xae, 0xa5, 0x62, 0xe7, 0x7b, 0xed, 0x16, 0x55,
	0x46, 0x02, 0x79, 0x2a, 0x78, 0x12, 0x3c, 0x73, 0x61, 0x06, 0xd8, 0xee, 0xc0, 0xe5, 0x02, 0xb4,
	0xdf, 0x15, 0x40, 0xdf, 0xa2, 0x1a, 0xf2, 0x8a, 0xf5, 0x13, 0xb5, 0xc3, 0xe8, 0x88, 0xda, 0xe9,
	0x39, 0x81, 0xa3, 0xcd, 0x09, 0x1c, 0x95, 0x39, 0x3e, 0x83, 0x76, 0xd1, 0x3e, 0x75, 0xdc, 0x14,
	0x6b, 0xf8, 0x1e, 0xa5, 0xee, 0x68, 0x2d, 0x75, 0xe2, 0xc9, 0x6d, 0xa8, 0x92, 0xba, 0x0d, 0x89,
	0x27, 0xa0, 0x1f, 0x58, 0xc7, 0xf1, 0x06, 0x8b, 0x53, 0x27, 0xf0, 0x5d, 0x1e, 0xbf, 0x3c, 0xfc,
	0x20, 0xa1, 0xb5, 0x2f, 0x57, 0x8c, 0x7f, 0xc1, 0xaa, 0x5e, 0xc0, 0x66, 0x14, 0x2a, 0x65, 0xf2,
	0x7f, 0x0f, 0x37, 0xa4, 0xb7, 0x3c, 0x0b, 0x83, 0x7e, 0xb2, 0xfb, 0x65, 0x10, 0x78, 0xe7, 0x50,
	0xfe, 0x26, 0x2c, 0x97, 0x6e, 0x96, 0xac, 0xbf, 0xfc, 0x63, 0x05, 0x48, 0x7e, 0x50, 0x42, 0xea,
	0x30, 0xbd, 0xb9, 0xf5, 0xac, 0x73, 0xb8, 0x73, 0xd0, 0x9c, 0x20, 0xf3, 0xd0, 0xe8, 0x6c, 0xee,
	0x76, 0xf7, 0xac, 0xf5, 0xce, 0xc6, 0xf3, 0xad, 0xbd, 0xcd, 0x66, 0x85, 0x00, 0x4c, 0x75, 0x77,
	0xad, 0x83, 0xce, 0x7a, 0x73, 0x52, 0xfc, 0x7e, 0xb1, 0x67, 0xed, 0x76, 0x37, 0x9a, 0x55, 0xb2,
	0x00, 0xf3, 0x87, 0xdb, 0x1b, 0xd6, 0xcb, 0x2d, 0x73, 0xff, 0xc5, 0x5e, 0x67, 0xc7, 0x7a, 0xd9,
	0xd9, 0xde, 0x6a, 0x5e, 0x20, 0x4b, 0xb0, 0xb0, 0xf1, 0x53, 0x67, 0x6f, 0x6f, 0x6b, 0x47, 0x5b,
	0xba, 0x48, 0x1a, 0x50, 0x13, 0x3b, 0x9e, 0x6d, 0x6d, 0x6d, 0xee, 0x37, 0xa7, 0x48, 0x13, 0x66,
	0x63, 0xcc, 0x9d, 0xee, 0xfe, 0x41, 0x73, 0xfa, 0xd1, 0xff, 0x19, 0x50, 0xef, 0xb0, 0x9e, 0x13,
	0x8f, 0xba, 0xff, 0x0b, 0x2e, 0x69, 0x73, 0x4b, 0x72, 0x5b, 0x4b, 0xf4, 0x85, 0x33, 0xd2, 0xf6,
	0x9d, 0x33, 0xb0, 0x94, 0x4f, 0x4c, 0x08, 0x0e, 0xda, 0xc4, 0x52, 0xe7, 0x50, 0x3c, 0x1d, 0xd5,
	0x39, 0x94, 0x8d, 0x3d, 0x27, 0xc8, 0x7f, 0xab, 0xa1, 0x57, 0xc1, 0xe8, 0x92, 0xdc, 0xcf, 0x45,
	0xde, 0xb8, 0x01, 0x69, 0x7b, 0xed, 0xbc, 0xe8, 0x09, 0x73, 0x07, 0xa7, 0x0b, 0x99, 0xa1, 0x24,
	0xb9, 0x93, 0x1b, 0x9d, 0x15, 0xcd, 0x4c, 0xdb, 0x9f, 0x9f, 0x85, 0x96, 0x30, 0x79, 0x05, 0xb3,
	0x69, 0xb7, 0x22, 0x37, 0xcb, 0x67, 0x73, 0x31, 0x71, 0x63, 0x1c, 0x4a, 0x42, 0xf8, 0x0d, 0xcc,
	0xe7, 0xe6, 0x76, 0xe4, 0xf3, 0x82, 0x43, 0x28, 0x62, 0x71, 0xf7, 0x4c, 0xbc, 0x84, 0xcf, 0x6b,
	0x68, 0x64, 0xa6, 0x70, 0x44, 0x13, 0xaf, 0x68, 0xc6, 0xd7, 0xbe, 0x35, 0x16, 0x47, 0x3b, 0x9c,
	0x11, 0xe9, 0xfc, 0xe1, 0xe4, 0x28, 0x1b, 0xe3, 0x50, 0x12, 0xc2, 0xbf, 0xc0, 0x5c, 0x76, 0x54,
	0x46, 0x6e, 0x15, 0xb9, 0xa4, 0x36, 0x2d, 0x6b, 0xdf, 0x1e, 0x8f, 0x94, 0x0e, 0x0c, 0x6d, 0x58,
	0xa6, 0x07, 0x46, 0xf1, 0x38, 0xae, 0x7d, 0xe7, 0x0c, 0xac, 0x84, 0xc3, 0x1e, 0xd4, 0x92, 0x71,
	0x18, 0xb9, 0xa1, 0x3d, 0x21, 0xb5, 0x31, 0x5b, 0x7b, 0xb9, 0x74, 0x3d, 0x4d, 0x2f, 0x19, 0x7b,
	0xe9, 0xf4, 0xf4, 0x11, 0x9b, 0x4e, 0x2f, 0x37, 0x2f, 0x33, 0x26, 0x88, 0x07, 0x97, 0x0b, 0x26,
	0x55, 0x64, 0x35, 0x27, 0x49, 0xc9, 0xb4, 0xaa, 0xfd, 0xc5, 0x39, 0x30, 0xd3, 0xdc, 0x0a, 0x06,
	0x55, 0x3a, 0xb7, 0xf2, 0xd9, 0x98, 0xce, 0x6d, 0xcc, 0xd4, 0xcb, 0x98, 0x20, 0xff, 0x5b, 0x49,
	0x8d, 0xf5, 0x72, 0xc3, 0x22, 0xf2, 0xa0, 0x44, 0xf2, 0xb2, 0x69, 0x54, 0xfb, 0xe1, 0xf9, 0x37,
	0x64, 0x64, 0x28, 0x1f, 0x58, 0xe9, 0x32, 0x9c, 0x39, 0x11, 0xd3, 0x65, 0x38, 0x7b, 0x16, 0x26,
	0x23, 0x3f, 0x33, 0xaa, 0xd2, 0x23, 0xbf, 0x68, 0x14, 0xa6, 0x47, 0x7e, 0xf1, 0xac, 0x6b, 0x82,
	0x30, 0x59, 0x6d, 0xb3, 0xe3, 0x2b, 0x72, 0xb7, 0xc0, 0xf1, 0x8a, 0xe6, 0x61, 0xed, 0xd5, 0xb3,
	0x11, 0xd3, 0x69, 0x5e, 0x9f, 0x4e, 0xe9, 0x69, 0xbe, 0x64, 0x06, 0xa6, 0xa7, 0xf9, 0xd2, 0x21,
	0xd7, 0x04, 0x09, 0xf1, 0x35, 0x91, 0x1f, 0x5a, 0x91, 0x2f, 0x0b, 0x24, 0x2d, 0x99, 0x85, 0xb5,
	0xbf, 0x3a, 0x17, 0x6e, 0xc2, 0x73, 0x88, 0x37, 0xfc, 0x82, 0x41, 0x14, 0xf9, 0x2a, 0x27, 0x77,
	0xf9, 0xfc, 0xab, 0x7d, 0xef, 0x7c, 0xc8, 0x9a, 0xaa, 0xf9, 0x69, 0x54, 0x81, 0xaa, 0xa5, 0x23,
	0xb0, 0x02, 0x55, 0xcb, 0xc7, 0x5b, 0xc6, 0x04, 0x09, 0xb0, 0x23, 0x98, 0x9b, 0x41, 0x91, 0x2f,
	0xc6, 0x93, 0x49, 0xcd, 0xb7, 0xda, 0x5f, 0x9e, 0x07, 0x55, 0x63, 0x98, 0x1b, 0x07, 0x15, 0x30,
	0x2c, 0x9b, 0x3c, 0x15, 0x30, 0x2c, 0x9d, 0x2e, 0x65, 0xee, 0x5a, 0x49, 0x7b, 0xb8, 0xf8, 0xae,
	0xa5, 0xb5, 0x4f, 0x4b, 0xee, 0x5a, 0xb9, 0xe6, 0x6b, 0x72, 0x13, 0x49, 0xc8, 0x17, 0xdc, 0x44,
	0x74, 0xda, 0xc6, 0x38, 0x94, 0xf4, 0x4d, 0x24, 0xd7, 0x15, 0x25, 0xf9, 0x1b, 0x52, 0x61, 0x0f,
	0xb6, 0x7d, 0xf7, 0x4c, 0xbc, 0x74, 0x15, 0x28, 0xe8, 0x43, 0xea, 0x55, 0xa0, 0xbc, 0x4b, 0xaa,
	0x57, 0x81, 0x71, 0x4d, 0xcd, 0xd8, 0x03, 0x72, 0x1d, 0xc8, 0x02, 0x0f, 0x28, 0x6b, 0x7d, 0x16,
	0x78, 0x40, 0x69, 0x43, 0x53, 0xa5, 0xfc, 0xf2, 0xa6, 0xa0, 0x9e, 0xf2, 0xcf, 0x6c, 0x66, 0xea,
	0x29, 0xff, 0x1c, 0xfd, 0xc6, 0x09, 0xf2, 0x2b, 0xfe, 0x2f, 0xa0, 0x42, 0xfe, 0xf7, 0x72, 0xca,
	0x8c, 0x63, 0x7e, 0xff, 0x9c, 0xd8, 0x63, 0x8c, 0x8b, 0x5c, 0xc7, 0x1b, 0x37, 0xcd, 0xf1, 0x8b,
	0x73, 0x60, 0xa6, 0xcb, 0x4f, 0xbe, 0x0b, 0xa6, 0x97, 0x9f, 0xd2, 0xbe, 0x9e, 0x5e, 0x7e, 0xca,
	0x1b, 0x6a, 0x32, 0x3a, 0x72, 0x4d, 0x28, 0x3d, 0x3a, 0xca, 0x5a, 0x62, 0x7a, 0x74, 0x94, 0x77,
	0xb3, 0xf0, 0xca, 0x9b, 0x6d, 0x40, 0xe9, 0x57, 0xde, 0xc2, 0x6e, 0x57, 0xfb, 0xf6, 0x78, 0xa4,
	0x34, 0xf9, 0x6c, 0x3b, 0x89, 0xe4, 0x2b, 0x7d, 0xbe, 0x57, 0xa3, 0x93, 0x2f, 0xe9, 0x48, 0x61,
	0xb4, 0x15, 0xf5, 0x83, 0xf4, 0x68, 0x1b, 0xd3, 0x87, 0xd2, 0xa3, 0x6d, 0x6c, 0x7b, 0x29, 0x7e,
	0x3e, 0x65, 0x7b, 0x39, 0x05, 0xcf, 0xa7, 0xc2, 0x0e, 0x54, 0xc1, 0xf3, 0xa9, 0xb8, 0x29, 0x24,
	0x3d, 0x2d, 0xdf, 0x77, 0x21, 0x79, 0x02, 0xc5, 0x1d, 0x1d, 0xdd, 0xd3, 0xc6, 0xb4, 0x70, 0xe2,
	0x8c, 0x95, 0xeb, 0xae, 0x14, 0x64, 0xac, 0xb2, 0x46, 0x4f, 0x41, 0xc6, 0x2a, 0x6d, 0xd6, 0xc8,
	0x6c, 0x51, 0xd2, 0x56, 0xd1, 0xb3, 0xc5, 0xf8, 0xd6, 0x8d, 0x9e, 0x2d, 0xce, 0xe8, 0xd5, 0x18,
	0x13, 0xeb, 0xdf, 0xbc, 0x7e, 0xdc, 0x0b, 0x3c, 0xdb, 0xef, 0xad, 0x7d, 0xf3, 0x28, 0x8a, 0xd6,
	0x9c, 0xa0, 0xff, 0x00, 0xff, 0x36, 0xc0, 0x09, 0xbc, 0x07, 0x9c, 0x86, 0xa7, 0xcc, 0xa1, 0x3c,
	0xff, 0xc7, 0x04, 0x47, 0x53, 0x88, 0xf4, 0xf8, 0xef, 0x01, 0x00, 0x00, 0xff, 0xff, 0x99, 0x3e,
	0xba, 0x50, 0x77, 0x30, 0x00, 0x00,
}
