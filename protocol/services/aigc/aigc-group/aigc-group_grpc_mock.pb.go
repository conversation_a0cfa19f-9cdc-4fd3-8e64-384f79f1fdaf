// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-group/aigc-group.proto

package aigc_group

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAigcGroupClient is a mock of AigcGroupClient interface.
type MockAigcGroupClient struct {
	ctrl     *gomock.Controller
	recorder *MockAigcGroupClientMockRecorder
}

// MockAigcGroupClientMockRecorder is the mock recorder for MockAigcGroupClient.
type MockAigcGroupClientMockRecorder struct {
	mock *MockAigcGroupClient
}

// NewMockAigcGroupClient creates a new mock instance.
func NewMockAigcGroupClient(ctrl *gomock.Controller) *MockAigcGroupClient {
	mock := &MockAigcGroupClient{ctrl: ctrl}
	mock.recorder = &MockAigcGroupClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcGroupClient) EXPECT() *MockAigcGroupClientMockRecorder {
	return m.recorder
}

// AddSpecialMsg mocks base method.
func (m *MockAigcGroupClient) AddSpecialMsg(ctx context.Context, in *AddSpecialMsgRequest, opts ...grpc.CallOption) (*AddSpecialMsgResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddSpecialMsg", varargs...)
	ret0, _ := ret[0].(*AddSpecialMsgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddSpecialMsg indicates an expected call of AddSpecialMsg.
func (mr *MockAigcGroupClientMockRecorder) AddSpecialMsg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSpecialMsg", reflect.TypeOf((*MockAigcGroupClient)(nil).AddSpecialMsg), varargs...)
}

// BatGetGroupLatestSeqId mocks base method.
func (m *MockAigcGroupClient) BatGetGroupLatestSeqId(ctx context.Context, in *BatGetGroupLatestSeqIdRequest, opts ...grpc.CallOption) (*BatGetGroupLatestSeqIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetGroupLatestSeqId", varargs...)
	ret0, _ := ret[0].(*BatGetGroupLatestSeqIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetGroupLatestSeqId indicates an expected call of BatGetGroupLatestSeqId.
func (mr *MockAigcGroupClientMockRecorder) BatGetGroupLatestSeqId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetGroupLatestSeqId", reflect.TypeOf((*MockAigcGroupClient)(nil).BatGetGroupLatestSeqId), varargs...)
}

// BatGetLastSpecialMsg mocks base method.
func (m *MockAigcGroupClient) BatGetLastSpecialMsg(ctx context.Context, in *BatGetLastSpecialMsgRequest, opts ...grpc.CallOption) (*BatGetLastSpecialMsgResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetLastSpecialMsg", varargs...)
	ret0, _ := ret[0].(*BatGetLastSpecialMsgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetLastSpecialMsg indicates an expected call of BatGetLastSpecialMsg.
func (mr *MockAigcGroupClientMockRecorder) BatGetLastSpecialMsg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetLastSpecialMsg", reflect.TypeOf((*MockAigcGroupClient)(nil).BatGetLastSpecialMsg), varargs...)
}

// BatchGetActiveGroup mocks base method.
func (m *MockAigcGroupClient) BatchGetActiveGroup(ctx context.Context, in *BatchGetActiveGroupRequest, opts ...grpc.CallOption) (*BatchGetActiveGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetActiveGroup", varargs...)
	ret0, _ := ret[0].(*BatchGetActiveGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetActiveGroup indicates an expected call of BatchGetActiveGroup.
func (mr *MockAigcGroupClientMockRecorder) BatchGetActiveGroup(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetActiveGroup", reflect.TypeOf((*MockAigcGroupClient)(nil).BatchGetActiveGroup), varargs...)
}

// BatchGetGroupMember mocks base method.
func (m *MockAigcGroupClient) BatchGetGroupMember(ctx context.Context, in *BatchGetGroupMemberRequest, opts ...grpc.CallOption) (*BatchGetGroupMemberResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetGroupMember", varargs...)
	ret0, _ := ret[0].(*BatchGetGroupMemberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGroupMember indicates an expected call of BatchGetGroupMember.
func (mr *MockAigcGroupClientMockRecorder) BatchGetGroupMember(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGroupMember", reflect.TypeOf((*MockAigcGroupClient)(nil).BatchGetGroupMember), varargs...)
}

// BatchGetGroupMemberList mocks base method.
func (m *MockAigcGroupClient) BatchGetGroupMemberList(ctx context.Context, in *BatchGetGroupMemberListRequest, opts ...grpc.CallOption) (*BatchGetGroupMemberListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetGroupMemberList", varargs...)
	ret0, _ := ret[0].(*BatchGetGroupMemberListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGroupMemberList indicates an expected call of BatchGetGroupMemberList.
func (mr *MockAigcGroupClientMockRecorder) BatchGetGroupMemberList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGroupMemberList", reflect.TypeOf((*MockAigcGroupClient)(nil).BatchGetGroupMemberList), varargs...)
}

// BatchGetRandMatchPlayer mocks base method.
func (m *MockAigcGroupClient) BatchGetRandMatchPlayer(ctx context.Context, in *BatchGetRandMatchPlayerRequest, opts ...grpc.CallOption) (*BatchGetRandMatchPlayerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetRandMatchPlayer", varargs...)
	ret0, _ := ret[0].(*BatchGetRandMatchPlayerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetRandMatchPlayer indicates an expected call of BatchGetRandMatchPlayer.
func (mr *MockAigcGroupClientMockRecorder) BatchGetRandMatchPlayer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetRandMatchPlayer", reflect.TypeOf((*MockAigcGroupClient)(nil).BatchGetRandMatchPlayer), varargs...)
}

// CancelMatch mocks base method.
func (m *MockAigcGroupClient) CancelMatch(ctx context.Context, in *CancelMatchRequest, opts ...grpc.CallOption) (*CancelMatchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelMatch", varargs...)
	ret0, _ := ret[0].(*CancelMatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelMatch indicates an expected call of CancelMatch.
func (mr *MockAigcGroupClientMockRecorder) CancelMatch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelMatch", reflect.TypeOf((*MockAigcGroupClient)(nil).CancelMatch), varargs...)
}

// ConfirmMatch mocks base method.
func (m *MockAigcGroupClient) ConfirmMatch(ctx context.Context, in *ConfirmMatchRequest, opts ...grpc.CallOption) (*ConfirmMatchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmMatch", varargs...)
	ret0, _ := ret[0].(*ConfirmMatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmMatch indicates an expected call of ConfirmMatch.
func (mr *MockAigcGroupClientMockRecorder) ConfirmMatch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmMatch", reflect.TypeOf((*MockAigcGroupClient)(nil).ConfirmMatch), varargs...)
}

// CreateGroup mocks base method.
func (m *MockAigcGroupClient) CreateGroup(ctx context.Context, in *CreateGroupRequest, opts ...grpc.CallOption) (*CreateGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateGroup", varargs...)
	ret0, _ := ret[0].(*CreateGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateGroup indicates an expected call of CreateGroup.
func (mr *MockAigcGroupClientMockRecorder) CreateGroup(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGroup", reflect.TypeOf((*MockAigcGroupClient)(nil).CreateGroup), varargs...)
}

// CreateGroupTemplate mocks base method.
func (m *MockAigcGroupClient) CreateGroupTemplate(ctx context.Context, in *CreateGroupTemplateRequest, opts ...grpc.CallOption) (*CreateGroupTemplateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateGroupTemplate", varargs...)
	ret0, _ := ret[0].(*CreateGroupTemplateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateGroupTemplate indicates an expected call of CreateGroupTemplate.
func (mr *MockAigcGroupClientMockRecorder) CreateGroupTemplate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGroupTemplate", reflect.TypeOf((*MockAigcGroupClient)(nil).CreateGroupTemplate), varargs...)
}

// CreateHotBanner mocks base method.
func (m *MockAigcGroupClient) CreateHotBanner(ctx context.Context, in *CreateHotBannerRequest, opts ...grpc.CallOption) (*CreateHotBannerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateHotBanner", varargs...)
	ret0, _ := ret[0].(*CreateHotBannerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateHotBanner indicates an expected call of CreateHotBanner.
func (mr *MockAigcGroupClientMockRecorder) CreateHotBanner(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateHotBanner", reflect.TypeOf((*MockAigcGroupClient)(nil).CreateHotBanner), varargs...)
}

// DeleteGroup mocks base method.
func (m *MockAigcGroupClient) DeleteGroup(ctx context.Context, in *DeleteGroupRequest, opts ...grpc.CallOption) (*DeleteGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteGroup", varargs...)
	ret0, _ := ret[0].(*DeleteGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteGroup indicates an expected call of DeleteGroup.
func (mr *MockAigcGroupClientMockRecorder) DeleteGroup(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteGroup", reflect.TypeOf((*MockAigcGroupClient)(nil).DeleteGroup), varargs...)
}

// DeleteGroupTemplate mocks base method.
func (m *MockAigcGroupClient) DeleteGroupTemplate(ctx context.Context, in *DeleteGroupTemplateRequest, opts ...grpc.CallOption) (*DeleteGroupTemplateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteGroupTemplate", varargs...)
	ret0, _ := ret[0].(*DeleteGroupTemplateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteGroupTemplate indicates an expected call of DeleteGroupTemplate.
func (mr *MockAigcGroupClientMockRecorder) DeleteGroupTemplate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteGroupTemplate", reflect.TypeOf((*MockAigcGroupClient)(nil).DeleteGroupTemplate), varargs...)
}

// DeleteHotBanner mocks base method.
func (m *MockAigcGroupClient) DeleteHotBanner(ctx context.Context, in *DeleteHotBannerRequest, opts ...grpc.CallOption) (*DeleteHotBannerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteHotBanner", varargs...)
	ret0, _ := ret[0].(*DeleteHotBannerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteHotBanner indicates an expected call of DeleteHotBanner.
func (mr *MockAigcGroupClientMockRecorder) DeleteHotBanner(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteHotBanner", reflect.TypeOf((*MockAigcGroupClient)(nil).DeleteHotBanner), varargs...)
}

// DetectDumbGroupMember mocks base method.
func (m *MockAigcGroupClient) DetectDumbGroupMember(ctx context.Context, in *DetectDumbGroupMemberRequest, opts ...grpc.CallOption) (*DetectDumbGroupMemberResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DetectDumbGroupMember", varargs...)
	ret0, _ := ret[0].(*DetectDumbGroupMemberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DetectDumbGroupMember indicates an expected call of DetectDumbGroupMember.
func (mr *MockAigcGroupClientMockRecorder) DetectDumbGroupMember(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DetectDumbGroupMember", reflect.TypeOf((*MockAigcGroupClient)(nil).DetectDumbGroupMember), varargs...)
}

// GetActiveGroupList mocks base method.
func (m *MockAigcGroupClient) GetActiveGroupList(ctx context.Context, in *GetActiveGroupListRequest, opts ...grpc.CallOption) (*GetActiveGroupListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetActiveGroupList", varargs...)
	ret0, _ := ret[0].(*GetActiveGroupListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveGroupList indicates an expected call of GetActiveGroupList.
func (mr *MockAigcGroupClientMockRecorder) GetActiveGroupList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveGroupList", reflect.TypeOf((*MockAigcGroupClient)(nil).GetActiveGroupList), varargs...)
}

// GetAllHotBanner mocks base method.
func (m *MockAigcGroupClient) GetAllHotBanner(ctx context.Context, in *GetAllHotBannerRequest, opts ...grpc.CallOption) (*GetAllHotBannerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllHotBanner", varargs...)
	ret0, _ := ret[0].(*GetAllHotBannerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllHotBanner indicates an expected call of GetAllHotBanner.
func (mr *MockAigcGroupClientMockRecorder) GetAllHotBanner(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllHotBanner", reflect.TypeOf((*MockAigcGroupClient)(nil).GetAllHotBanner), varargs...)
}

// GetFakeHelloCount mocks base method.
func (m *MockAigcGroupClient) GetFakeHelloCount(ctx context.Context, in *GetFakeHelloCountRequest, opts ...grpc.CallOption) (*GetFakeHelloCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFakeHelloCount", varargs...)
	ret0, _ := ret[0].(*GetFakeHelloCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFakeHelloCount indicates an expected call of GetFakeHelloCount.
func (mr *MockAigcGroupClientMockRecorder) GetFakeHelloCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFakeHelloCount", reflect.TypeOf((*MockAigcGroupClient)(nil).GetFakeHelloCount), varargs...)
}

// GetGroupInfo mocks base method.
func (m *MockAigcGroupClient) GetGroupInfo(ctx context.Context, in *GetGroupInfoRequest, opts ...grpc.CallOption) (*GetGroupInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGroupInfo", varargs...)
	ret0, _ := ret[0].(*GetGroupInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupInfo indicates an expected call of GetGroupInfo.
func (mr *MockAigcGroupClientMockRecorder) GetGroupInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupInfo", reflect.TypeOf((*MockAigcGroupClient)(nil).GetGroupInfo), varargs...)
}

// GetGroupMember mocks base method.
func (m *MockAigcGroupClient) GetGroupMember(ctx context.Context, in *GetGroupMemberRequest, opts ...grpc.CallOption) (*GetGroupMemberResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGroupMember", varargs...)
	ret0, _ := ret[0].(*GetGroupMemberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupMember indicates an expected call of GetGroupMember.
func (mr *MockAigcGroupClientMockRecorder) GetGroupMember(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupMember", reflect.TypeOf((*MockAigcGroupClient)(nil).GetGroupMember), varargs...)
}

// GetGroupMemberList mocks base method.
func (m *MockAigcGroupClient) GetGroupMemberList(ctx context.Context, in *GetGroupMemberListRequest, opts ...grpc.CallOption) (*GetGroupMemberListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGroupMemberList", varargs...)
	ret0, _ := ret[0].(*GetGroupMemberListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupMemberList indicates an expected call of GetGroupMemberList.
func (mr *MockAigcGroupClientMockRecorder) GetGroupMemberList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupMemberList", reflect.TypeOf((*MockAigcGroupClient)(nil).GetGroupMemberList), varargs...)
}

// GetGroupTemplateByIds mocks base method.
func (m *MockAigcGroupClient) GetGroupTemplateByIds(ctx context.Context, in *GetGroupTemplateByIdsRequest, opts ...grpc.CallOption) (*GetGroupTemplateByIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGroupTemplateByIds", varargs...)
	ret0, _ := ret[0].(*GetGroupTemplateByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupTemplateByIds indicates an expected call of GetGroupTemplateByIds.
func (mr *MockAigcGroupClientMockRecorder) GetGroupTemplateByIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupTemplateByIds", reflect.TypeOf((*MockAigcGroupClient)(nil).GetGroupTemplateByIds), varargs...)
}

// GetGroupTemplateByPage mocks base method.
func (m *MockAigcGroupClient) GetGroupTemplateByPage(ctx context.Context, in *GetGroupTemplateByPageRequest, opts ...grpc.CallOption) (*GetGroupTemplateByPageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGroupTemplateByPage", varargs...)
	ret0, _ := ret[0].(*GetGroupTemplateByPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupTemplateByPage indicates an expected call of GetGroupTemplateByPage.
func (mr *MockAigcGroupClientMockRecorder) GetGroupTemplateByPage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupTemplateByPage", reflect.TypeOf((*MockAigcGroupClient)(nil).GetGroupTemplateByPage), varargs...)
}

// GetHotBannerById mocks base method.
func (m *MockAigcGroupClient) GetHotBannerById(ctx context.Context, in *GetHotBannerByIdRequest, opts ...grpc.CallOption) (*GetHotBannerByIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetHotBannerById", varargs...)
	ret0, _ := ret[0].(*GetHotBannerByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHotBannerById indicates an expected call of GetHotBannerById.
func (mr *MockAigcGroupClientMockRecorder) GetHotBannerById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHotBannerById", reflect.TypeOf((*MockAigcGroupClient)(nil).GetHotBannerById), varargs...)
}

// GetMultiGroupUserRoleInfo mocks base method.
func (m *MockAigcGroupClient) GetMultiGroupUserRoleInfo(ctx context.Context, in *GetMultiGroupUserRoleInfoRequest, opts ...grpc.CallOption) (*GetMultiGroupUserRoleInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMultiGroupUserRoleInfo", varargs...)
	ret0, _ := ret[0].(*GetMultiGroupUserRoleInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiGroupUserRoleInfo indicates an expected call of GetMultiGroupUserRoleInfo.
func (mr *MockAigcGroupClientMockRecorder) GetMultiGroupUserRoleInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiGroupUserRoleInfo", reflect.TypeOf((*MockAigcGroupClient)(nil).GetMultiGroupUserRoleInfo), varargs...)
}

// GetScriptListByFilter mocks base method.
func (m *MockAigcGroupClient) GetScriptListByFilter(ctx context.Context, in *GetScriptListByFilterRequest, opts ...grpc.CallOption) (*GetScriptListByFilterResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetScriptListByFilter", varargs...)
	ret0, _ := ret[0].(*GetScriptListByFilterResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScriptListByFilter indicates an expected call of GetScriptListByFilter.
func (mr *MockAigcGroupClientMockRecorder) GetScriptListByFilter(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScriptListByFilter", reflect.TypeOf((*MockAigcGroupClient)(nil).GetScriptListByFilter), varargs...)
}

// GetTemplateByGroupId mocks base method.
func (m *MockAigcGroupClient) GetTemplateByGroupId(ctx context.Context, in *GetTemplateByGroupIdRequest, opts ...grpc.CallOption) (*GetTemplateByGroupIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTemplateByGroupId", varargs...)
	ret0, _ := ret[0].(*GetTemplateByGroupIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTemplateByGroupId indicates an expected call of GetTemplateByGroupId.
func (mr *MockAigcGroupClientMockRecorder) GetTemplateByGroupId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTemplateByGroupId", reflect.TypeOf((*MockAigcGroupClient)(nil).GetTemplateByGroupId), varargs...)
}

// GetUserJoinedGroupList mocks base method.
func (m *MockAigcGroupClient) GetUserJoinedGroupList(ctx context.Context, in *GetUserJoinedGroupListRequest, opts ...grpc.CallOption) (*GetUserJoinedGroupListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserJoinedGroupList", varargs...)
	ret0, _ := ret[0].(*GetUserJoinedGroupListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserJoinedGroupList indicates an expected call of GetUserJoinedGroupList.
func (mr *MockAigcGroupClientMockRecorder) GetUserJoinedGroupList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserJoinedGroupList", reflect.TypeOf((*MockAigcGroupClient)(nil).GetUserJoinedGroupList), varargs...)
}

// GetUserOwnedGroupList mocks base method.
func (m *MockAigcGroupClient) GetUserOwnedGroupList(ctx context.Context, in *GetUserOwnedGroupListRequest, opts ...grpc.CallOption) (*GetUserOwnedGroupListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserOwnedGroupList", varargs...)
	ret0, _ := ret[0].(*GetUserOwnedGroupListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserOwnedGroupList indicates an expected call of GetUserOwnedGroupList.
func (mr *MockAigcGroupClientMockRecorder) GetUserOwnedGroupList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserOwnedGroupList", reflect.TypeOf((*MockAigcGroupClient)(nil).GetUserOwnedGroupList), varargs...)
}

// IncrFakeHelloCount mocks base method.
func (m *MockAigcGroupClient) IncrFakeHelloCount(ctx context.Context, in *IncrFakeHelloCountRequest, opts ...grpc.CallOption) (*IncrFakeHelloCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IncrFakeHelloCount", varargs...)
	ret0, _ := ret[0].(*IncrFakeHelloCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrFakeHelloCount indicates an expected call of IncrFakeHelloCount.
func (mr *MockAigcGroupClientMockRecorder) IncrFakeHelloCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrFakeHelloCount", reflect.TypeOf((*MockAigcGroupClient)(nil).IncrFakeHelloCount), varargs...)
}

// JoinMatch mocks base method.
func (m *MockAigcGroupClient) JoinMatch(ctx context.Context, in *JoinMatchRequest, opts ...grpc.CallOption) (*JoinMatchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "JoinMatch", varargs...)
	ret0, _ := ret[0].(*JoinMatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// JoinMatch indicates an expected call of JoinMatch.
func (mr *MockAigcGroupClientMockRecorder) JoinMatch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JoinMatch", reflect.TypeOf((*MockAigcGroupClient)(nil).JoinMatch), varargs...)
}

// LeaveGroup mocks base method.
func (m *MockAigcGroupClient) LeaveGroup(ctx context.Context, in *LeaveGroupRequest, opts ...grpc.CallOption) (*LeaveGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LeaveGroup", varargs...)
	ret0, _ := ret[0].(*LeaveGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LeaveGroup indicates an expected call of LeaveGroup.
func (mr *MockAigcGroupClientMockRecorder) LeaveGroup(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LeaveGroup", reflect.TypeOf((*MockAigcGroupClient)(nil).LeaveGroup), varargs...)
}

// MultiGroupDefaultUserRoleSelect mocks base method.
func (m *MockAigcGroupClient) MultiGroupDefaultUserRoleSelect(ctx context.Context, in *MultiGroupDefaultUserRoleSelectRequest, opts ...grpc.CallOption) (*MultiGroupDefaultUserRoleSelectResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MultiGroupDefaultUserRoleSelect", varargs...)
	ret0, _ := ret[0].(*MultiGroupDefaultUserRoleSelectResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MultiGroupDefaultUserRoleSelect indicates an expected call of MultiGroupDefaultUserRoleSelect.
func (mr *MockAigcGroupClientMockRecorder) MultiGroupDefaultUserRoleSelect(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MultiGroupDefaultUserRoleSelect", reflect.TypeOf((*MockAigcGroupClient)(nil).MultiGroupDefaultUserRoleSelect), varargs...)
}

// PullSpecialMsg mocks base method.
func (m *MockAigcGroupClient) PullSpecialMsg(ctx context.Context, in *PullSpecialMsgRequest, opts ...grpc.CallOption) (*PullSpecialMsgResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PullSpecialMsg", varargs...)
	ret0, _ := ret[0].(*PullSpecialMsgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PullSpecialMsg indicates an expected call of PullSpecialMsg.
func (mr *MockAigcGroupClientMockRecorder) PullSpecialMsg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PullSpecialMsg", reflect.TypeOf((*MockAigcGroupClient)(nil).PullSpecialMsg), varargs...)
}

// RemoveGroupTriggerTask mocks base method.
func (m *MockAigcGroupClient) RemoveGroupTriggerTask(ctx context.Context, in *RemoveGroupTriggerTaskRequest, opts ...grpc.CallOption) (*RemoveGroupTriggerTaskResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RemoveGroupTriggerTask", varargs...)
	ret0, _ := ret[0].(*RemoveGroupTriggerTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveGroupTriggerTask indicates an expected call of RemoveGroupTriggerTask.
func (mr *MockAigcGroupClientMockRecorder) RemoveGroupTriggerTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveGroupTriggerTask", reflect.TypeOf((*MockAigcGroupClient)(nil).RemoveGroupTriggerTask), varargs...)
}

// ResortBanner mocks base method.
func (m *MockAigcGroupClient) ResortBanner(ctx context.Context, in *ResortBannerRequest, opts ...grpc.CallOption) (*ResortBannerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResortBanner", varargs...)
	ret0, _ := ret[0].(*ResortBannerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResortBanner indicates an expected call of ResortBanner.
func (mr *MockAigcGroupClientMockRecorder) ResortBanner(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResortBanner", reflect.TypeOf((*MockAigcGroupClient)(nil).ResortBanner), varargs...)
}

// ScanGroupTriggerTask mocks base method.
func (m *MockAigcGroupClient) ScanGroupTriggerTask(ctx context.Context, in *ScanGroupTriggerTaskRequest, opts ...grpc.CallOption) (*ScanGroupTriggerTaskResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ScanGroupTriggerTask", varargs...)
	ret0, _ := ret[0].(*ScanGroupTriggerTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScanGroupTriggerTask indicates an expected call of ScanGroupTriggerTask.
func (mr *MockAigcGroupClientMockRecorder) ScanGroupTriggerTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScanGroupTriggerTask", reflect.TypeOf((*MockAigcGroupClient)(nil).ScanGroupTriggerTask), varargs...)
}

// UpdateGroupLatestSeqId mocks base method.
func (m *MockAigcGroupClient) UpdateGroupLatestSeqId(ctx context.Context, in *UpdateGroupLatestSeqIdRequest, opts ...grpc.CallOption) (*UpdateGroupLatestSeqIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateGroupLatestSeqId", varargs...)
	ret0, _ := ret[0].(*UpdateGroupLatestSeqIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateGroupLatestSeqId indicates an expected call of UpdateGroupLatestSeqId.
func (mr *MockAigcGroupClientMockRecorder) UpdateGroupLatestSeqId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGroupLatestSeqId", reflect.TypeOf((*MockAigcGroupClient)(nil).UpdateGroupLatestSeqId), varargs...)
}

// UpdateGroupTemplate mocks base method.
func (m *MockAigcGroupClient) UpdateGroupTemplate(ctx context.Context, in *UpdateGroupTemplateRequest, opts ...grpc.CallOption) (*UpdateGroupTemplateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateGroupTemplate", varargs...)
	ret0, _ := ret[0].(*UpdateGroupTemplateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateGroupTemplate indicates an expected call of UpdateGroupTemplate.
func (mr *MockAigcGroupClientMockRecorder) UpdateGroupTemplate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGroupTemplate", reflect.TypeOf((*MockAigcGroupClient)(nil).UpdateGroupTemplate), varargs...)
}

// UpdateHotBanner mocks base method.
func (m *MockAigcGroupClient) UpdateHotBanner(ctx context.Context, in *UpdateHotBannerRequest, opts ...grpc.CallOption) (*UpdateHotBannerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateHotBanner", varargs...)
	ret0, _ := ret[0].(*UpdateHotBannerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateHotBanner indicates an expected call of UpdateHotBanner.
func (mr *MockAigcGroupClientMockRecorder) UpdateHotBanner(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateHotBanner", reflect.TypeOf((*MockAigcGroupClient)(nil).UpdateHotBanner), varargs...)
}

// MockAigcGroupServer is a mock of AigcGroupServer interface.
type MockAigcGroupServer struct {
	ctrl     *gomock.Controller
	recorder *MockAigcGroupServerMockRecorder
}

// MockAigcGroupServerMockRecorder is the mock recorder for MockAigcGroupServer.
type MockAigcGroupServerMockRecorder struct {
	mock *MockAigcGroupServer
}

// NewMockAigcGroupServer creates a new mock instance.
func NewMockAigcGroupServer(ctrl *gomock.Controller) *MockAigcGroupServer {
	mock := &MockAigcGroupServer{ctrl: ctrl}
	mock.recorder = &MockAigcGroupServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcGroupServer) EXPECT() *MockAigcGroupServerMockRecorder {
	return m.recorder
}

// AddSpecialMsg mocks base method.
func (m *MockAigcGroupServer) AddSpecialMsg(ctx context.Context, in *AddSpecialMsgRequest) (*AddSpecialMsgResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddSpecialMsg", ctx, in)
	ret0, _ := ret[0].(*AddSpecialMsgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddSpecialMsg indicates an expected call of AddSpecialMsg.
func (mr *MockAigcGroupServerMockRecorder) AddSpecialMsg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSpecialMsg", reflect.TypeOf((*MockAigcGroupServer)(nil).AddSpecialMsg), ctx, in)
}

// BatGetGroupLatestSeqId mocks base method.
func (m *MockAigcGroupServer) BatGetGroupLatestSeqId(ctx context.Context, in *BatGetGroupLatestSeqIdRequest) (*BatGetGroupLatestSeqIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetGroupLatestSeqId", ctx, in)
	ret0, _ := ret[0].(*BatGetGroupLatestSeqIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetGroupLatestSeqId indicates an expected call of BatGetGroupLatestSeqId.
func (mr *MockAigcGroupServerMockRecorder) BatGetGroupLatestSeqId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetGroupLatestSeqId", reflect.TypeOf((*MockAigcGroupServer)(nil).BatGetGroupLatestSeqId), ctx, in)
}

// BatGetLastSpecialMsg mocks base method.
func (m *MockAigcGroupServer) BatGetLastSpecialMsg(ctx context.Context, in *BatGetLastSpecialMsgRequest) (*BatGetLastSpecialMsgResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetLastSpecialMsg", ctx, in)
	ret0, _ := ret[0].(*BatGetLastSpecialMsgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetLastSpecialMsg indicates an expected call of BatGetLastSpecialMsg.
func (mr *MockAigcGroupServerMockRecorder) BatGetLastSpecialMsg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetLastSpecialMsg", reflect.TypeOf((*MockAigcGroupServer)(nil).BatGetLastSpecialMsg), ctx, in)
}

// BatchGetActiveGroup mocks base method.
func (m *MockAigcGroupServer) BatchGetActiveGroup(ctx context.Context, in *BatchGetActiveGroupRequest) (*BatchGetActiveGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetActiveGroup", ctx, in)
	ret0, _ := ret[0].(*BatchGetActiveGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetActiveGroup indicates an expected call of BatchGetActiveGroup.
func (mr *MockAigcGroupServerMockRecorder) BatchGetActiveGroup(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetActiveGroup", reflect.TypeOf((*MockAigcGroupServer)(nil).BatchGetActiveGroup), ctx, in)
}

// BatchGetGroupMember mocks base method.
func (m *MockAigcGroupServer) BatchGetGroupMember(ctx context.Context, in *BatchGetGroupMemberRequest) (*BatchGetGroupMemberResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetGroupMember", ctx, in)
	ret0, _ := ret[0].(*BatchGetGroupMemberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGroupMember indicates an expected call of BatchGetGroupMember.
func (mr *MockAigcGroupServerMockRecorder) BatchGetGroupMember(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGroupMember", reflect.TypeOf((*MockAigcGroupServer)(nil).BatchGetGroupMember), ctx, in)
}

// BatchGetGroupMemberList mocks base method.
func (m *MockAigcGroupServer) BatchGetGroupMemberList(ctx context.Context, in *BatchGetGroupMemberListRequest) (*BatchGetGroupMemberListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetGroupMemberList", ctx, in)
	ret0, _ := ret[0].(*BatchGetGroupMemberListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGroupMemberList indicates an expected call of BatchGetGroupMemberList.
func (mr *MockAigcGroupServerMockRecorder) BatchGetGroupMemberList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGroupMemberList", reflect.TypeOf((*MockAigcGroupServer)(nil).BatchGetGroupMemberList), ctx, in)
}

// BatchGetRandMatchPlayer mocks base method.
func (m *MockAigcGroupServer) BatchGetRandMatchPlayer(ctx context.Context, in *BatchGetRandMatchPlayerRequest) (*BatchGetRandMatchPlayerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetRandMatchPlayer", ctx, in)
	ret0, _ := ret[0].(*BatchGetRandMatchPlayerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetRandMatchPlayer indicates an expected call of BatchGetRandMatchPlayer.
func (mr *MockAigcGroupServerMockRecorder) BatchGetRandMatchPlayer(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetRandMatchPlayer", reflect.TypeOf((*MockAigcGroupServer)(nil).BatchGetRandMatchPlayer), ctx, in)
}

// CancelMatch mocks base method.
func (m *MockAigcGroupServer) CancelMatch(ctx context.Context, in *CancelMatchRequest) (*CancelMatchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelMatch", ctx, in)
	ret0, _ := ret[0].(*CancelMatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelMatch indicates an expected call of CancelMatch.
func (mr *MockAigcGroupServerMockRecorder) CancelMatch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelMatch", reflect.TypeOf((*MockAigcGroupServer)(nil).CancelMatch), ctx, in)
}

// ConfirmMatch mocks base method.
func (m *MockAigcGroupServer) ConfirmMatch(ctx context.Context, in *ConfirmMatchRequest) (*ConfirmMatchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmMatch", ctx, in)
	ret0, _ := ret[0].(*ConfirmMatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmMatch indicates an expected call of ConfirmMatch.
func (mr *MockAigcGroupServerMockRecorder) ConfirmMatch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmMatch", reflect.TypeOf((*MockAigcGroupServer)(nil).ConfirmMatch), ctx, in)
}

// CreateGroup mocks base method.
func (m *MockAigcGroupServer) CreateGroup(ctx context.Context, in *CreateGroupRequest) (*CreateGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateGroup", ctx, in)
	ret0, _ := ret[0].(*CreateGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateGroup indicates an expected call of CreateGroup.
func (mr *MockAigcGroupServerMockRecorder) CreateGroup(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGroup", reflect.TypeOf((*MockAigcGroupServer)(nil).CreateGroup), ctx, in)
}

// CreateGroupTemplate mocks base method.
func (m *MockAigcGroupServer) CreateGroupTemplate(ctx context.Context, in *CreateGroupTemplateRequest) (*CreateGroupTemplateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateGroupTemplate", ctx, in)
	ret0, _ := ret[0].(*CreateGroupTemplateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateGroupTemplate indicates an expected call of CreateGroupTemplate.
func (mr *MockAigcGroupServerMockRecorder) CreateGroupTemplate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGroupTemplate", reflect.TypeOf((*MockAigcGroupServer)(nil).CreateGroupTemplate), ctx, in)
}

// CreateHotBanner mocks base method.
func (m *MockAigcGroupServer) CreateHotBanner(ctx context.Context, in *CreateHotBannerRequest) (*CreateHotBannerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateHotBanner", ctx, in)
	ret0, _ := ret[0].(*CreateHotBannerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateHotBanner indicates an expected call of CreateHotBanner.
func (mr *MockAigcGroupServerMockRecorder) CreateHotBanner(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateHotBanner", reflect.TypeOf((*MockAigcGroupServer)(nil).CreateHotBanner), ctx, in)
}

// DeleteGroup mocks base method.
func (m *MockAigcGroupServer) DeleteGroup(ctx context.Context, in *DeleteGroupRequest) (*DeleteGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteGroup", ctx, in)
	ret0, _ := ret[0].(*DeleteGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteGroup indicates an expected call of DeleteGroup.
func (mr *MockAigcGroupServerMockRecorder) DeleteGroup(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteGroup", reflect.TypeOf((*MockAigcGroupServer)(nil).DeleteGroup), ctx, in)
}

// DeleteGroupTemplate mocks base method.
func (m *MockAigcGroupServer) DeleteGroupTemplate(ctx context.Context, in *DeleteGroupTemplateRequest) (*DeleteGroupTemplateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteGroupTemplate", ctx, in)
	ret0, _ := ret[0].(*DeleteGroupTemplateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteGroupTemplate indicates an expected call of DeleteGroupTemplate.
func (mr *MockAigcGroupServerMockRecorder) DeleteGroupTemplate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteGroupTemplate", reflect.TypeOf((*MockAigcGroupServer)(nil).DeleteGroupTemplate), ctx, in)
}

// DeleteHotBanner mocks base method.
func (m *MockAigcGroupServer) DeleteHotBanner(ctx context.Context, in *DeleteHotBannerRequest) (*DeleteHotBannerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteHotBanner", ctx, in)
	ret0, _ := ret[0].(*DeleteHotBannerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteHotBanner indicates an expected call of DeleteHotBanner.
func (mr *MockAigcGroupServerMockRecorder) DeleteHotBanner(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteHotBanner", reflect.TypeOf((*MockAigcGroupServer)(nil).DeleteHotBanner), ctx, in)
}

// DetectDumbGroupMember mocks base method.
func (m *MockAigcGroupServer) DetectDumbGroupMember(ctx context.Context, in *DetectDumbGroupMemberRequest) (*DetectDumbGroupMemberResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DetectDumbGroupMember", ctx, in)
	ret0, _ := ret[0].(*DetectDumbGroupMemberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DetectDumbGroupMember indicates an expected call of DetectDumbGroupMember.
func (mr *MockAigcGroupServerMockRecorder) DetectDumbGroupMember(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DetectDumbGroupMember", reflect.TypeOf((*MockAigcGroupServer)(nil).DetectDumbGroupMember), ctx, in)
}

// GetActiveGroupList mocks base method.
func (m *MockAigcGroupServer) GetActiveGroupList(ctx context.Context, in *GetActiveGroupListRequest) (*GetActiveGroupListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveGroupList", ctx, in)
	ret0, _ := ret[0].(*GetActiveGroupListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveGroupList indicates an expected call of GetActiveGroupList.
func (mr *MockAigcGroupServerMockRecorder) GetActiveGroupList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveGroupList", reflect.TypeOf((*MockAigcGroupServer)(nil).GetActiveGroupList), ctx, in)
}

// GetAllHotBanner mocks base method.
func (m *MockAigcGroupServer) GetAllHotBanner(ctx context.Context, in *GetAllHotBannerRequest) (*GetAllHotBannerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllHotBanner", ctx, in)
	ret0, _ := ret[0].(*GetAllHotBannerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllHotBanner indicates an expected call of GetAllHotBanner.
func (mr *MockAigcGroupServerMockRecorder) GetAllHotBanner(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllHotBanner", reflect.TypeOf((*MockAigcGroupServer)(nil).GetAllHotBanner), ctx, in)
}

// GetFakeHelloCount mocks base method.
func (m *MockAigcGroupServer) GetFakeHelloCount(ctx context.Context, in *GetFakeHelloCountRequest) (*GetFakeHelloCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFakeHelloCount", ctx, in)
	ret0, _ := ret[0].(*GetFakeHelloCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFakeHelloCount indicates an expected call of GetFakeHelloCount.
func (mr *MockAigcGroupServerMockRecorder) GetFakeHelloCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFakeHelloCount", reflect.TypeOf((*MockAigcGroupServer)(nil).GetFakeHelloCount), ctx, in)
}

// GetGroupInfo mocks base method.
func (m *MockAigcGroupServer) GetGroupInfo(ctx context.Context, in *GetGroupInfoRequest) (*GetGroupInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupInfo", ctx, in)
	ret0, _ := ret[0].(*GetGroupInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupInfo indicates an expected call of GetGroupInfo.
func (mr *MockAigcGroupServerMockRecorder) GetGroupInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupInfo", reflect.TypeOf((*MockAigcGroupServer)(nil).GetGroupInfo), ctx, in)
}

// GetGroupMember mocks base method.
func (m *MockAigcGroupServer) GetGroupMember(ctx context.Context, in *GetGroupMemberRequest) (*GetGroupMemberResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupMember", ctx, in)
	ret0, _ := ret[0].(*GetGroupMemberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupMember indicates an expected call of GetGroupMember.
func (mr *MockAigcGroupServerMockRecorder) GetGroupMember(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupMember", reflect.TypeOf((*MockAigcGroupServer)(nil).GetGroupMember), ctx, in)
}

// GetGroupMemberList mocks base method.
func (m *MockAigcGroupServer) GetGroupMemberList(ctx context.Context, in *GetGroupMemberListRequest) (*GetGroupMemberListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupMemberList", ctx, in)
	ret0, _ := ret[0].(*GetGroupMemberListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupMemberList indicates an expected call of GetGroupMemberList.
func (mr *MockAigcGroupServerMockRecorder) GetGroupMemberList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupMemberList", reflect.TypeOf((*MockAigcGroupServer)(nil).GetGroupMemberList), ctx, in)
}

// GetGroupTemplateByIds mocks base method.
func (m *MockAigcGroupServer) GetGroupTemplateByIds(ctx context.Context, in *GetGroupTemplateByIdsRequest) (*GetGroupTemplateByIdsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupTemplateByIds", ctx, in)
	ret0, _ := ret[0].(*GetGroupTemplateByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupTemplateByIds indicates an expected call of GetGroupTemplateByIds.
func (mr *MockAigcGroupServerMockRecorder) GetGroupTemplateByIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupTemplateByIds", reflect.TypeOf((*MockAigcGroupServer)(nil).GetGroupTemplateByIds), ctx, in)
}

// GetGroupTemplateByPage mocks base method.
func (m *MockAigcGroupServer) GetGroupTemplateByPage(ctx context.Context, in *GetGroupTemplateByPageRequest) (*GetGroupTemplateByPageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupTemplateByPage", ctx, in)
	ret0, _ := ret[0].(*GetGroupTemplateByPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupTemplateByPage indicates an expected call of GetGroupTemplateByPage.
func (mr *MockAigcGroupServerMockRecorder) GetGroupTemplateByPage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupTemplateByPage", reflect.TypeOf((*MockAigcGroupServer)(nil).GetGroupTemplateByPage), ctx, in)
}

// GetHotBannerById mocks base method.
func (m *MockAigcGroupServer) GetHotBannerById(ctx context.Context, in *GetHotBannerByIdRequest) (*GetHotBannerByIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHotBannerById", ctx, in)
	ret0, _ := ret[0].(*GetHotBannerByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHotBannerById indicates an expected call of GetHotBannerById.
func (mr *MockAigcGroupServerMockRecorder) GetHotBannerById(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHotBannerById", reflect.TypeOf((*MockAigcGroupServer)(nil).GetHotBannerById), ctx, in)
}

// GetMultiGroupUserRoleInfo mocks base method.
func (m *MockAigcGroupServer) GetMultiGroupUserRoleInfo(ctx context.Context, in *GetMultiGroupUserRoleInfoRequest) (*GetMultiGroupUserRoleInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiGroupUserRoleInfo", ctx, in)
	ret0, _ := ret[0].(*GetMultiGroupUserRoleInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiGroupUserRoleInfo indicates an expected call of GetMultiGroupUserRoleInfo.
func (mr *MockAigcGroupServerMockRecorder) GetMultiGroupUserRoleInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiGroupUserRoleInfo", reflect.TypeOf((*MockAigcGroupServer)(nil).GetMultiGroupUserRoleInfo), ctx, in)
}

// GetScriptListByFilter mocks base method.
func (m *MockAigcGroupServer) GetScriptListByFilter(ctx context.Context, in *GetScriptListByFilterRequest) (*GetScriptListByFilterResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetScriptListByFilter", ctx, in)
	ret0, _ := ret[0].(*GetScriptListByFilterResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScriptListByFilter indicates an expected call of GetScriptListByFilter.
func (mr *MockAigcGroupServerMockRecorder) GetScriptListByFilter(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScriptListByFilter", reflect.TypeOf((*MockAigcGroupServer)(nil).GetScriptListByFilter), ctx, in)
}

// GetTemplateByGroupId mocks base method.
func (m *MockAigcGroupServer) GetTemplateByGroupId(ctx context.Context, in *GetTemplateByGroupIdRequest) (*GetTemplateByGroupIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTemplateByGroupId", ctx, in)
	ret0, _ := ret[0].(*GetTemplateByGroupIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTemplateByGroupId indicates an expected call of GetTemplateByGroupId.
func (mr *MockAigcGroupServerMockRecorder) GetTemplateByGroupId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTemplateByGroupId", reflect.TypeOf((*MockAigcGroupServer)(nil).GetTemplateByGroupId), ctx, in)
}

// GetUserJoinedGroupList mocks base method.
func (m *MockAigcGroupServer) GetUserJoinedGroupList(ctx context.Context, in *GetUserJoinedGroupListRequest) (*GetUserJoinedGroupListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserJoinedGroupList", ctx, in)
	ret0, _ := ret[0].(*GetUserJoinedGroupListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserJoinedGroupList indicates an expected call of GetUserJoinedGroupList.
func (mr *MockAigcGroupServerMockRecorder) GetUserJoinedGroupList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserJoinedGroupList", reflect.TypeOf((*MockAigcGroupServer)(nil).GetUserJoinedGroupList), ctx, in)
}

// GetUserOwnedGroupList mocks base method.
func (m *MockAigcGroupServer) GetUserOwnedGroupList(ctx context.Context, in *GetUserOwnedGroupListRequest) (*GetUserOwnedGroupListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserOwnedGroupList", ctx, in)
	ret0, _ := ret[0].(*GetUserOwnedGroupListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserOwnedGroupList indicates an expected call of GetUserOwnedGroupList.
func (mr *MockAigcGroupServerMockRecorder) GetUserOwnedGroupList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserOwnedGroupList", reflect.TypeOf((*MockAigcGroupServer)(nil).GetUserOwnedGroupList), ctx, in)
}

// IncrFakeHelloCount mocks base method.
func (m *MockAigcGroupServer) IncrFakeHelloCount(ctx context.Context, in *IncrFakeHelloCountRequest) (*IncrFakeHelloCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrFakeHelloCount", ctx, in)
	ret0, _ := ret[0].(*IncrFakeHelloCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrFakeHelloCount indicates an expected call of IncrFakeHelloCount.
func (mr *MockAigcGroupServerMockRecorder) IncrFakeHelloCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrFakeHelloCount", reflect.TypeOf((*MockAigcGroupServer)(nil).IncrFakeHelloCount), ctx, in)
}

// JoinMatch mocks base method.
func (m *MockAigcGroupServer) JoinMatch(ctx context.Context, in *JoinMatchRequest) (*JoinMatchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JoinMatch", ctx, in)
	ret0, _ := ret[0].(*JoinMatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// JoinMatch indicates an expected call of JoinMatch.
func (mr *MockAigcGroupServerMockRecorder) JoinMatch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JoinMatch", reflect.TypeOf((*MockAigcGroupServer)(nil).JoinMatch), ctx, in)
}

// LeaveGroup mocks base method.
func (m *MockAigcGroupServer) LeaveGroup(ctx context.Context, in *LeaveGroupRequest) (*LeaveGroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LeaveGroup", ctx, in)
	ret0, _ := ret[0].(*LeaveGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LeaveGroup indicates an expected call of LeaveGroup.
func (mr *MockAigcGroupServerMockRecorder) LeaveGroup(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LeaveGroup", reflect.TypeOf((*MockAigcGroupServer)(nil).LeaveGroup), ctx, in)
}

// MultiGroupDefaultUserRoleSelect mocks base method.
func (m *MockAigcGroupServer) MultiGroupDefaultUserRoleSelect(ctx context.Context, in *MultiGroupDefaultUserRoleSelectRequest) (*MultiGroupDefaultUserRoleSelectResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MultiGroupDefaultUserRoleSelect", ctx, in)
	ret0, _ := ret[0].(*MultiGroupDefaultUserRoleSelectResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MultiGroupDefaultUserRoleSelect indicates an expected call of MultiGroupDefaultUserRoleSelect.
func (mr *MockAigcGroupServerMockRecorder) MultiGroupDefaultUserRoleSelect(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MultiGroupDefaultUserRoleSelect", reflect.TypeOf((*MockAigcGroupServer)(nil).MultiGroupDefaultUserRoleSelect), ctx, in)
}

// PullSpecialMsg mocks base method.
func (m *MockAigcGroupServer) PullSpecialMsg(ctx context.Context, in *PullSpecialMsgRequest) (*PullSpecialMsgResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PullSpecialMsg", ctx, in)
	ret0, _ := ret[0].(*PullSpecialMsgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PullSpecialMsg indicates an expected call of PullSpecialMsg.
func (mr *MockAigcGroupServerMockRecorder) PullSpecialMsg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PullSpecialMsg", reflect.TypeOf((*MockAigcGroupServer)(nil).PullSpecialMsg), ctx, in)
}

// RemoveGroupTriggerTask mocks base method.
func (m *MockAigcGroupServer) RemoveGroupTriggerTask(ctx context.Context, in *RemoveGroupTriggerTaskRequest) (*RemoveGroupTriggerTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveGroupTriggerTask", ctx, in)
	ret0, _ := ret[0].(*RemoveGroupTriggerTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveGroupTriggerTask indicates an expected call of RemoveGroupTriggerTask.
func (mr *MockAigcGroupServerMockRecorder) RemoveGroupTriggerTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveGroupTriggerTask", reflect.TypeOf((*MockAigcGroupServer)(nil).RemoveGroupTriggerTask), ctx, in)
}

// ResortBanner mocks base method.
func (m *MockAigcGroupServer) ResortBanner(ctx context.Context, in *ResortBannerRequest) (*ResortBannerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResortBanner", ctx, in)
	ret0, _ := ret[0].(*ResortBannerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResortBanner indicates an expected call of ResortBanner.
func (mr *MockAigcGroupServerMockRecorder) ResortBanner(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResortBanner", reflect.TypeOf((*MockAigcGroupServer)(nil).ResortBanner), ctx, in)
}

// ScanGroupTriggerTask mocks base method.
func (m *MockAigcGroupServer) ScanGroupTriggerTask(ctx context.Context, in *ScanGroupTriggerTaskRequest) (*ScanGroupTriggerTaskResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScanGroupTriggerTask", ctx, in)
	ret0, _ := ret[0].(*ScanGroupTriggerTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScanGroupTriggerTask indicates an expected call of ScanGroupTriggerTask.
func (mr *MockAigcGroupServerMockRecorder) ScanGroupTriggerTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScanGroupTriggerTask", reflect.TypeOf((*MockAigcGroupServer)(nil).ScanGroupTriggerTask), ctx, in)
}

// UpdateGroupLatestSeqId mocks base method.
func (m *MockAigcGroupServer) UpdateGroupLatestSeqId(ctx context.Context, in *UpdateGroupLatestSeqIdRequest) (*UpdateGroupLatestSeqIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGroupLatestSeqId", ctx, in)
	ret0, _ := ret[0].(*UpdateGroupLatestSeqIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateGroupLatestSeqId indicates an expected call of UpdateGroupLatestSeqId.
func (mr *MockAigcGroupServerMockRecorder) UpdateGroupLatestSeqId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGroupLatestSeqId", reflect.TypeOf((*MockAigcGroupServer)(nil).UpdateGroupLatestSeqId), ctx, in)
}

// UpdateGroupTemplate mocks base method.
func (m *MockAigcGroupServer) UpdateGroupTemplate(ctx context.Context, in *UpdateGroupTemplateRequest) (*UpdateGroupTemplateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGroupTemplate", ctx, in)
	ret0, _ := ret[0].(*UpdateGroupTemplateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateGroupTemplate indicates an expected call of UpdateGroupTemplate.
func (mr *MockAigcGroupServerMockRecorder) UpdateGroupTemplate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGroupTemplate", reflect.TypeOf((*MockAigcGroupServer)(nil).UpdateGroupTemplate), ctx, in)
}

// UpdateHotBanner mocks base method.
func (m *MockAigcGroupServer) UpdateHotBanner(ctx context.Context, in *UpdateHotBannerRequest) (*UpdateHotBannerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateHotBanner", ctx, in)
	ret0, _ := ret[0].(*UpdateHotBannerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateHotBanner indicates an expected call of UpdateHotBanner.
func (mr *MockAigcGroupServerMockRecorder) UpdateHotBanner(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateHotBanner", reflect.TypeOf((*MockAigcGroupServer)(nil).UpdateHotBanner), ctx, in)
}
