// Code generated by protoc-gen-tt-client. DO NOT EDIT.
// versions:
// - protoc-gen-tt-client v0.0.1
// - protoc                   (unknown)
// source: tt/quicksilver/aigc/aigc-group/aigc-group.proto

package aigc_group

import (
	context "context"
	client "gitlab.ttyuyin.com/tyr/tt-ecosystem/grpc/client"
	grpc "google.golang.org/grpc"
)

const serviceName = "aigc-group"

type Client struct {
	AigcGroupClient

	cc *grpc.ClientConn
}

func MustNewClient(ctx context.Context, opts ...grpc.DialOption) *Client {
	c, err := NewClient(ctx, opts...)
	if err != nil {
		panic(err)
	}
	return c
}

func NewClient(ctx context.Context, opts ...grpc.DialOption) (*Client, error) {
	c := &Client{}
	if err := client.DialContextWithConnUpdate(ctx, serviceName, opts, func(cc *grpc.ClientConn) *grpc.ClientConn {
		c.cc, cc = cc, c.cc
		c.AigcGroupClient = NewAigcGroupClient(c.cc)
		return cc
	}); err != nil {
		return nil, err
	}
	return c, nil
}

func MustNewClientTo(ctx context.Context, target string, opts ...grpc.DialOption) *Client {
	c, err := NewClientTo(ctx, target, opts...)
	if err != nil {
		panic(err)
	}
	return c
}

func NewClientTo(ctx context.Context, target string, opts ...grpc.DialOption) (*Client, error) {
	cc, err := client.DialContextTo(ctx, target, opts...)
	if err != nil {
		return nil, err
	}
	return &Client{
		AigcGroupClient: NewAigcGroupClient(cc),
	}, nil
}
