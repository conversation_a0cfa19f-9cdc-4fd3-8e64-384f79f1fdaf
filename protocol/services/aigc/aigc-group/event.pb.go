// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-group/event.proto

package aigc_group // import "golang.52tt.com/protocol/services/aigc/aigc-group"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GroupTemplateChangeEvent_Op int32

const (
	GroupTemplateChangeEvent_OP_INVALID GroupTemplateChangeEvent_Op = 0
	GroupTemplateChangeEvent_OP_CREATE  GroupTemplateChangeEvent_Op = 1
	GroupTemplateChangeEvent_OP_UPDATE  GroupTemplateChangeEvent_Op = 2
	GroupTemplateChangeEvent_OP_DELETE  GroupTemplateChangeEvent_Op = 3
)

var GroupTemplateChangeEvent_Op_name = map[int32]string{
	0: "OP_INVALID",
	1: "OP_CREATE",
	2: "OP_UPDATE",
	3: "OP_DELETE",
}
var GroupTemplateChangeEvent_Op_value = map[string]int32{
	"OP_INVALID": 0,
	"OP_CREATE":  1,
	"OP_UPDATE":  2,
	"OP_DELETE":  3,
}

func (x GroupTemplateChangeEvent_Op) String() string {
	return proto.EnumName(GroupTemplateChangeEvent_Op_name, int32(x))
}
func (GroupTemplateChangeEvent_Op) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_c7f025f50bc224da, []int{0, 0}
}

type GroupEvent_Event int32

const (
	GroupEvent_EVENT_UNSPECIFIED GroupEvent_Event = 0
	// 建群
	GroupEvent_EVENT_CREATE GroupEvent_Event = 1
	// 删群
	GroupEvent_EVENT_DELETE GroupEvent_Event = 2
)

var GroupEvent_Event_name = map[int32]string{
	0: "EVENT_UNSPECIFIED",
	1: "EVENT_CREATE",
	2: "EVENT_DELETE",
}
var GroupEvent_Event_value = map[string]int32{
	"EVENT_UNSPECIFIED": 0,
	"EVENT_CREATE":      1,
	"EVENT_DELETE":      2,
}

func (x GroupEvent_Event) String() string {
	return proto.EnumName(GroupEvent_Event_name, int32(x))
}
func (GroupEvent_Event) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_c7f025f50bc224da, []int{1, 0}
}

type GroupTemplateChangeEvent struct {
	GroupTemplateList    []*GroupTemplateChangeEvent_GroupTemplate `protobuf:"bytes,1,rep,name=group_template_list,json=groupTemplateList,proto3" json:"group_template_list,omitempty"`
	Operation            GroupTemplateChangeEvent_Op               `protobuf:"varint,2,opt,name=operation,proto3,enum=aigc_group.GroupTemplateChangeEvent_Op" json:"operation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                  `json:"-"`
	XXX_unrecognized     []byte                                    `json:"-"`
	XXX_sizecache        int32                                     `json:"-"`
}

func (m *GroupTemplateChangeEvent) Reset()         { *m = GroupTemplateChangeEvent{} }
func (m *GroupTemplateChangeEvent) String() string { return proto.CompactTextString(m) }
func (*GroupTemplateChangeEvent) ProtoMessage()    {}
func (*GroupTemplateChangeEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_c7f025f50bc224da, []int{0}
}
func (m *GroupTemplateChangeEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupTemplateChangeEvent.Unmarshal(m, b)
}
func (m *GroupTemplateChangeEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupTemplateChangeEvent.Marshal(b, m, deterministic)
}
func (dst *GroupTemplateChangeEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupTemplateChangeEvent.Merge(dst, src)
}
func (m *GroupTemplateChangeEvent) XXX_Size() int {
	return xxx_messageInfo_GroupTemplateChangeEvent.Size(m)
}
func (m *GroupTemplateChangeEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupTemplateChangeEvent.DiscardUnknown(m)
}

var xxx_messageInfo_GroupTemplateChangeEvent proto.InternalMessageInfo

func (m *GroupTemplateChangeEvent) GetGroupTemplateList() []*GroupTemplateChangeEvent_GroupTemplate {
	if m != nil {
		return m.GroupTemplateList
	}
	return nil
}

func (m *GroupTemplateChangeEvent) GetOperation() GroupTemplateChangeEvent_Op {
	if m != nil {
		return m.Operation
	}
	return GroupTemplateChangeEvent_OP_INVALID
}

type GroupTemplateChangeEvent_GroupTemplate struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 群名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 群设定
	Character string `protobuf:"bytes,3,opt,name=character,proto3" json:"character,omitempty"`
	// 群标签
	Tags []string `protobuf:"bytes,4,rep,name=tags,proto3" json:"tags,omitempty"`
	// 群头像
	Avatar string `protobuf:"bytes,5,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 群性别0:女 1:男
	Sex int32 `protobuf:"varint,6,opt,name=sex,proto3" json:"sex,omitempty"`
	// 是否展示/曝光到首页 true:展示 false:不展示
	Exposed bool `protobuf:"varint,7,opt,name=exposed,proto3" json:"exposed,omitempty"`
	// 强插位置
	InsertPos uint32 `protobuf:"varint,8,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	// 群组所属分类ID
	CategoryId string `protobuf:"bytes,9,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 群模板角色列表
	RoleIds []uint32 `protobuf:"varint,10,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	// 模板类型
	GroupType GroupType `protobuf:"varint,11,opt,name=group_type,json=groupType,proto3,enum=aigc_group.GroupType" json:"group_type,omitempty"`
	// 多人剧本玩法额外字段
	ScriptInfo *ScriptInfo `protobuf:"bytes,12,opt,name=script_info,json=scriptInfo,proto3" json:"script_info,omitempty"`
	// 适用于的性别
	SuitableSex          []int32  `protobuf:"varint,13,rep,packed,name=suitable_sex,json=suitableSex,proto3" json:"suitable_sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupTemplateChangeEvent_GroupTemplate) Reset() {
	*m = GroupTemplateChangeEvent_GroupTemplate{}
}
func (m *GroupTemplateChangeEvent_GroupTemplate) String() string { return proto.CompactTextString(m) }
func (*GroupTemplateChangeEvent_GroupTemplate) ProtoMessage()    {}
func (*GroupTemplateChangeEvent_GroupTemplate) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_c7f025f50bc224da, []int{0, 0}
}
func (m *GroupTemplateChangeEvent_GroupTemplate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupTemplateChangeEvent_GroupTemplate.Unmarshal(m, b)
}
func (m *GroupTemplateChangeEvent_GroupTemplate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupTemplateChangeEvent_GroupTemplate.Marshal(b, m, deterministic)
}
func (dst *GroupTemplateChangeEvent_GroupTemplate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupTemplateChangeEvent_GroupTemplate.Merge(dst, src)
}
func (m *GroupTemplateChangeEvent_GroupTemplate) XXX_Size() int {
	return xxx_messageInfo_GroupTemplateChangeEvent_GroupTemplate.Size(m)
}
func (m *GroupTemplateChangeEvent_GroupTemplate) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupTemplateChangeEvent_GroupTemplate.DiscardUnknown(m)
}

var xxx_messageInfo_GroupTemplateChangeEvent_GroupTemplate proto.InternalMessageInfo

func (m *GroupTemplateChangeEvent_GroupTemplate) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupTemplateChangeEvent_GroupTemplate) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GroupTemplateChangeEvent_GroupTemplate) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *GroupTemplateChangeEvent_GroupTemplate) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *GroupTemplateChangeEvent_GroupTemplate) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *GroupTemplateChangeEvent_GroupTemplate) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GroupTemplateChangeEvent_GroupTemplate) GetExposed() bool {
	if m != nil {
		return m.Exposed
	}
	return false
}

func (m *GroupTemplateChangeEvent_GroupTemplate) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *GroupTemplateChangeEvent_GroupTemplate) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *GroupTemplateChangeEvent_GroupTemplate) GetRoleIds() []uint32 {
	if m != nil {
		return m.RoleIds
	}
	return nil
}

func (m *GroupTemplateChangeEvent_GroupTemplate) GetGroupType() GroupType {
	if m != nil {
		return m.GroupType
	}
	return GroupType_GROUP_TYPE_UNSPECIFIED
}

func (m *GroupTemplateChangeEvent_GroupTemplate) GetScriptInfo() *ScriptInfo {
	if m != nil {
		return m.ScriptInfo
	}
	return nil
}

func (m *GroupTemplateChangeEvent_GroupTemplate) GetSuitableSex() []int32 {
	if m != nil {
		return m.SuitableSex
	}
	return nil
}

type GroupEvent struct {
	Event                GroupEvent_Event     `protobuf:"varint,1,opt,name=event,proto3,enum=aigc_group.GroupEvent_Event" json:"event,omitempty"`
	Group                *GroupEvent_Group    `protobuf:"bytes,2,opt,name=group,proto3" json:"group,omitempty"`
	Members              []*GroupEvent_Member `protobuf:"bytes,3,rep,name=members,proto3" json:"members,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GroupEvent) Reset()         { *m = GroupEvent{} }
func (m *GroupEvent) String() string { return proto.CompactTextString(m) }
func (*GroupEvent) ProtoMessage()    {}
func (*GroupEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_c7f025f50bc224da, []int{1}
}
func (m *GroupEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupEvent.Unmarshal(m, b)
}
func (m *GroupEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupEvent.Marshal(b, m, deterministic)
}
func (dst *GroupEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupEvent.Merge(dst, src)
}
func (m *GroupEvent) XXX_Size() int {
	return xxx_messageInfo_GroupEvent.Size(m)
}
func (m *GroupEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupEvent.DiscardUnknown(m)
}

var xxx_messageInfo_GroupEvent proto.InternalMessageInfo

func (m *GroupEvent) GetEvent() GroupEvent_Event {
	if m != nil {
		return m.Event
	}
	return GroupEvent_EVENT_UNSPECIFIED
}

func (m *GroupEvent) GetGroup() *GroupEvent_Group {
	if m != nil {
		return m.Group
	}
	return nil
}

func (m *GroupEvent) GetMembers() []*GroupEvent_Member {
	if m != nil {
		return m.Members
	}
	return nil
}

type GroupEvent_Group struct {
	Id                   uint32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedAt            int64     `protobuf:"varint,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	OwnerUid             uint32    `protobuf:"varint,4,opt,name=owner_uid,json=ownerUid,proto3" json:"owner_uid,omitempty"`
	TemplateId           uint32    `protobuf:"varint,5,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	Type                 GroupType `protobuf:"varint,6,opt,name=type,proto3,enum=aigc_group.GroupType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GroupEvent_Group) Reset()         { *m = GroupEvent_Group{} }
func (m *GroupEvent_Group) String() string { return proto.CompactTextString(m) }
func (*GroupEvent_Group) ProtoMessage()    {}
func (*GroupEvent_Group) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_c7f025f50bc224da, []int{1, 0}
}
func (m *GroupEvent_Group) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupEvent_Group.Unmarshal(m, b)
}
func (m *GroupEvent_Group) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupEvent_Group.Marshal(b, m, deterministic)
}
func (dst *GroupEvent_Group) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupEvent_Group.Merge(dst, src)
}
func (m *GroupEvent_Group) XXX_Size() int {
	return xxx_messageInfo_GroupEvent_Group.Size(m)
}
func (m *GroupEvent_Group) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupEvent_Group.DiscardUnknown(m)
}

var xxx_messageInfo_GroupEvent_Group proto.InternalMessageInfo

func (m *GroupEvent_Group) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupEvent_Group) GetCreatedAt() int64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *GroupEvent_Group) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

func (m *GroupEvent_Group) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *GroupEvent_Group) GetType() GroupType {
	if m != nil {
		return m.Type
	}
	return GroupType_GROUP_TYPE_UNSPECIFIED
}

type GroupEvent_Member struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	JoinedAt             int64    `protobuf:"varint,2,opt,name=joined_at,json=joinedAt,proto3" json:"joined_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupEvent_Member) Reset()         { *m = GroupEvent_Member{} }
func (m *GroupEvent_Member) String() string { return proto.CompactTextString(m) }
func (*GroupEvent_Member) ProtoMessage()    {}
func (*GroupEvent_Member) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_c7f025f50bc224da, []int{1, 1}
}
func (m *GroupEvent_Member) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupEvent_Member.Unmarshal(m, b)
}
func (m *GroupEvent_Member) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupEvent_Member.Marshal(b, m, deterministic)
}
func (dst *GroupEvent_Member) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupEvent_Member.Merge(dst, src)
}
func (m *GroupEvent_Member) XXX_Size() int {
	return xxx_messageInfo_GroupEvent_Member.Size(m)
}
func (m *GroupEvent_Member) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupEvent_Member.DiscardUnknown(m)
}

var xxx_messageInfo_GroupEvent_Member proto.InternalMessageInfo

func (m *GroupEvent_Member) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GroupEvent_Member) GetJoinedAt() int64 {
	if m != nil {
		return m.JoinedAt
	}
	return 0
}

// 群成员入群事件 topic:aigc_group_join_member
type GroupMemberJoinedEvent struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	JoinedAt             int64    `protobuf:"varint,3,opt,name=joined_at,json=joinedAt,proto3" json:"joined_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupMemberJoinedEvent) Reset()         { *m = GroupMemberJoinedEvent{} }
func (m *GroupMemberJoinedEvent) String() string { return proto.CompactTextString(m) }
func (*GroupMemberJoinedEvent) ProtoMessage()    {}
func (*GroupMemberJoinedEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_c7f025f50bc224da, []int{2}
}
func (m *GroupMemberJoinedEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupMemberJoinedEvent.Unmarshal(m, b)
}
func (m *GroupMemberJoinedEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupMemberJoinedEvent.Marshal(b, m, deterministic)
}
func (dst *GroupMemberJoinedEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupMemberJoinedEvent.Merge(dst, src)
}
func (m *GroupMemberJoinedEvent) XXX_Size() int {
	return xxx_messageInfo_GroupMemberJoinedEvent.Size(m)
}
func (m *GroupMemberJoinedEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupMemberJoinedEvent.DiscardUnknown(m)
}

var xxx_messageInfo_GroupMemberJoinedEvent proto.InternalMessageInfo

func (m *GroupMemberJoinedEvent) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupMemberJoinedEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GroupMemberJoinedEvent) GetJoinedAt() int64 {
	if m != nil {
		return m.JoinedAt
	}
	return 0
}

func init() {
	proto.RegisterType((*GroupTemplateChangeEvent)(nil), "aigc_group.GroupTemplateChangeEvent")
	proto.RegisterType((*GroupTemplateChangeEvent_GroupTemplate)(nil), "aigc_group.GroupTemplateChangeEvent.GroupTemplate")
	proto.RegisterType((*GroupEvent)(nil), "aigc_group.GroupEvent")
	proto.RegisterType((*GroupEvent_Group)(nil), "aigc_group.GroupEvent.Group")
	proto.RegisterType((*GroupEvent_Member)(nil), "aigc_group.GroupEvent.Member")
	proto.RegisterType((*GroupMemberJoinedEvent)(nil), "aigc_group.GroupMemberJoinedEvent")
	proto.RegisterEnum("aigc_group.GroupTemplateChangeEvent_Op", GroupTemplateChangeEvent_Op_name, GroupTemplateChangeEvent_Op_value)
	proto.RegisterEnum("aigc_group.GroupEvent_Event", GroupEvent_Event_name, GroupEvent_Event_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/aigc/aigc-group/event.proto", fileDescriptor_event_c7f025f50bc224da)
}

var fileDescriptor_event_c7f025f50bc224da = []byte{
	// 720 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x54, 0x5d, 0x6f, 0xe3, 0x44,
	0x14, 0xc5, 0x71, 0xf3, 0xe1, 0x9b, 0x26, 0xca, 0x0e, 0xda, 0x6a, 0x36, 0x6c, 0x85, 0xc9, 0x0b,
	0x06, 0x89, 0x44, 0x64, 0x41, 0x7d, 0xce, 0xb6, 0x06, 0x19, 0x95, 0x26, 0x9a, 0xa6, 0xfb, 0xc0,
	0x8b, 0x35, 0xb1, 0x67, 0xbd, 0x03, 0x89, 0xc7, 0x78, 0x26, 0xa1, 0xfd, 0x23, 0xbc, 0x22, 0xfe,
	0x0f, 0x3f, 0x0a, 0xcd, 0x75, 0x3e, 0x5a, 0xaa, 0x00, 0x2f, 0xd6, 0xbd, 0xe7, 0x9e, 0x73, 0xe7,
	0xd8, 0xf7, 0x7a, 0xe0, 0x4b, 0x63, 0x46, 0xbf, 0xae, 0x65, 0xf2, 0x8b, 0x96, 0xcb, 0x8d, 0x28,
	0x47, 0x5c, 0x66, 0x09, 0x3e, 0xbe, 0xca, 0x4a, 0xb5, 0x2e, 0x46, 0x62, 0x23, 0x72, 0x33, 0x2c,
	0x4a, 0x65, 0x14, 0x01, 0x8b, 0xc7, 0x88, 0xf7, 0x47, 0xff, 0xa1, 0x3b, 0x84, 0x95, 0x78, 0xf0,
	0x67, 0x1d, 0xe8, 0xf7, 0x36, 0x9f, 0x8b, 0x55, 0xb1, 0xe4, 0x46, 0x5c, 0x7e, 0xe0, 0x79, 0x26,
	0x42, 0xdb, 0x9f, 0x2c, 0xe0, 0x63, 0xe4, 0xc6, 0x66, 0x5b, 0x8c, 0x97, 0x52, 0x1b, 0xea, 0xf8,
	0x6e, 0xd0, 0x1e, 0x8f, 0x87, 0x87, 0x73, 0x87, 0xc7, 0x5a, 0x3c, 0x2d, 0xb0, 0x17, 0xd9, 0xe3,
	0xf4, 0x5a, 0x6a, 0x43, 0x42, 0xf0, 0x54, 0x21, 0x4a, 0x6e, 0xa4, 0xca, 0x69, 0xcd, 0x77, 0x82,
	0xee, 0xf8, 0xf3, 0xff, 0xd5, 0x79, 0x5a, 0xb0, 0x83, 0xb2, 0xff, 0xbb, 0x0b, 0x9d, 0x27, 0x54,
	0xd2, 0x85, 0x9a, 0x4c, 0xa9, 0xe3, 0x3b, 0x41, 0x87, 0xd5, 0x64, 0x4a, 0x08, 0x9c, 0xe4, 0x7c,
	0x25, 0xf0, 0x0c, 0x8f, 0x61, 0x4c, 0x5e, 0x83, 0x97, 0x7c, 0xe0, 0x25, 0x4f, 0x8c, 0x28, 0xa9,
	0x8b, 0x85, 0x03, 0x60, 0x15, 0x86, 0x67, 0x9a, 0x9e, 0xf8, 0xae, 0x55, 0xd8, 0x98, 0x9c, 0x41,
	0x83, 0x6f, 0xb8, 0xe1, 0x25, 0xad, 0x23, 0x7d, 0x9b, 0x91, 0x1e, 0xb8, 0x5a, 0xdc, 0xd3, 0x86,
	0xef, 0x04, 0x75, 0x66, 0x43, 0x42, 0xa1, 0x29, 0xee, 0x0b, 0xa5, 0x45, 0x4a, 0x9b, 0xbe, 0x13,
	0xb4, 0xd8, 0x2e, 0x25, 0xe7, 0x00, 0x32, 0xd7, 0xa2, 0x34, 0x71, 0xa1, 0x34, 0x6d, 0xa1, 0x43,
	0xaf, 0x42, 0x66, 0x4a, 0x93, 0x4f, 0xa1, 0x9d, 0x70, 0x23, 0x32, 0x55, 0x3e, 0xc4, 0x32, 0xa5,
	0x1e, 0x9e, 0x03, 0x3b, 0x28, 0x4a, 0xc9, 0x2b, 0x68, 0x95, 0x6a, 0x29, 0x62, 0x99, 0x6a, 0x0a,
	0xbe, 0x1b, 0x74, 0x58, 0xd3, 0xe6, 0x51, 0xaa, 0xc9, 0x37, 0x00, 0xdb, 0x89, 0x3d, 0x14, 0x82,
	0xb6, 0xf1, 0x73, 0xbe, 0x7c, 0xfe, 0x39, 0x1f, 0x0a, 0xc1, 0xbc, 0x6c, 0x17, 0x92, 0x0b, 0x68,
	0xeb, 0xa4, 0x94, 0x85, 0x89, 0x65, 0xfe, 0x5e, 0xd1, 0x53, 0xdf, 0x09, 0xda, 0xe3, 0xb3, 0xc7,
	0xb2, 0x5b, 0x2c, 0x47, 0xf9, 0x7b, 0xc5, 0x40, 0xef, 0x63, 0xf2, 0x19, 0x9c, 0xea, 0xb5, 0x34,
	0x7c, 0xb1, 0x14, 0xb1, 0x7d, 0xfd, 0x8e, 0xef, 0x06, 0x75, 0xd6, 0xde, 0x61, 0xb7, 0xe2, 0x7e,
	0x30, 0x81, 0xda, 0xb4, 0x20, 0x5d, 0x80, 0xe9, 0x2c, 0x8e, 0x6e, 0xde, 0x4d, 0xae, 0xa3, 0xab,
	0xde, 0x47, 0xa4, 0x03, 0xde, 0x74, 0x16, 0x5f, 0xb2, 0x70, 0x32, 0x0f, 0x7b, 0xce, 0x36, 0xbd,
	0x9b, 0x5d, 0xd9, 0xb4, 0xb6, 0x4d, 0xaf, 0xc2, 0xeb, 0x70, 0x1e, 0xf6, 0xdc, 0xc1, 0x5f, 0x2e,
	0x00, 0xfa, 0xae, 0xb6, 0x72, 0x0c, 0x75, 0x5c, 0x7f, 0x9c, 0x6d, 0x77, 0xfc, 0xfa, 0xd9, 0xeb,
	0x55, 0xfb, 0x81, 0x4f, 0x56, 0x51, 0xad, 0x06, 0x09, 0x38, 0xfd, 0xf6, 0x51, 0x0d, 0x86, 0xac,
	0xa2, 0x92, 0x0b, 0x68, 0xae, 0xc4, 0x6a, 0x21, 0x4a, 0x4d, 0x5d, 0xdc, 0xf8, 0xf3, 0x23, 0xaa,
	0x1f, 0x91, 0xc5, 0x76, 0xec, 0xfe, 0x1f, 0x0e, 0xd4, 0xb1, 0xfc, 0x6c, 0x07, 0xcf, 0x01, 0x92,
	0x52, 0x70, 0x23, 0xd2, 0x98, 0x1b, 0xf4, 0xe2, 0x32, 0x6f, 0x8b, 0x4c, 0x0c, 0xf9, 0x04, 0x3c,
	0xf5, 0x5b, 0x2e, 0xca, 0x78, 0x2d, 0x53, 0x7a, 0x82, 0xaa, 0x16, 0x02, 0x77, 0x32, 0xb5, 0x6b,
	0xb1, 0xff, 0x0d, 0x65, 0x8a, 0xeb, 0xd7, 0x61, 0xb0, 0x83, 0xa2, 0x94, 0x7c, 0x01, 0x27, 0x38,
	0xf5, 0xc6, 0xbf, 0x4d, 0x1d, 0x29, 0xfd, 0x0b, 0x68, 0x54, 0xa6, 0xed, 0xde, 0xae, 0xf7, 0x16,
	0x6d, 0x68, 0x4d, 0xfc, 0xac, 0x64, 0xfe, 0xd8, 0x62, 0xab, 0x02, 0x26, 0x66, 0xf0, 0x16, 0xea,
	0xd5, 0x10, 0x5e, 0xc2, 0x8b, 0xf0, 0x5d, 0x78, 0x33, 0x8f, 0xef, 0x6e, 0x6e, 0x67, 0xe1, 0x65,
	0xf4, 0x5d, 0x14, 0xda, 0xb9, 0xf6, 0xe0, 0xb4, 0x82, 0xf7, 0xa3, 0xdd, 0x23, 0xdb, 0x71, 0xd6,
	0x06, 0x0b, 0x38, 0x43, 0x3f, 0x95, 0x83, 0x1f, 0xb0, 0x75, 0xd5, 0xf4, 0x15, 0xb4, 0xaa, 0xed,
	0xdd, 0x3b, 0x6a, 0x62, 0x1e, 0xa5, 0x3b, 0x9f, 0xb5, 0x23, 0x3e, 0xdd, 0xa7, 0x3e, 0xdf, 0xbe,
	0xf9, 0xe9, 0xeb, 0x4c, 0x2d, 0x79, 0x9e, 0x0d, 0xbf, 0x1d, 0x1b, 0x33, 0x4c, 0xd4, 0x6a, 0x84,
	0xf7, 0x5d, 0xa2, 0x96, 0x23, 0x2d, 0xca, 0x8d, 0x4c, 0x84, 0xfe, 0xe7, 0xe5, 0xb8, 0x68, 0x20,
	0xe5, 0xcd, 0xdf, 0x01, 0x00, 0x00, 0xff, 0xff, 0x26, 0xc3, 0x4e, 0x42, 0x7d, 0x05, 0x00, 0x00,
}
