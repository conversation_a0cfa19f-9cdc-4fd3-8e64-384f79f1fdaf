// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-group/aigc-group.proto

package aigc_group // import "golang.52tt.com/protocol/services/aigc/aigc-group"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 群类型
type GroupType int32

const (
	GroupType_GROUP_TYPE_UNSPECIFIED GroupType = 0
	// 单用户
	GroupType_GROUP_TYPE_SINGLE_USER GroupType = 1
	// 多用户
	GroupType_GROUP_TYPE_MULTI_USER GroupType = 2
	// 多用户剧本
	GroupType_GROUP_TYPE_MULTI_USER_SCRIPT GroupType = 3
)

var GroupType_name = map[int32]string{
	0: "GROUP_TYPE_UNSPECIFIED",
	1: "GROUP_TYPE_SINGLE_USER",
	2: "GROUP_TYPE_MULTI_USER",
	3: "GROUP_TYPE_MULTI_USER_SCRIPT",
}
var GroupType_value = map[string]int32{
	"GROUP_TYPE_UNSPECIFIED":       0,
	"GROUP_TYPE_SINGLE_USER":       1,
	"GROUP_TYPE_MULTI_USER":        2,
	"GROUP_TYPE_MULTI_USER_SCRIPT": 3,
}

func (x GroupType) String() string {
	return proto.EnumName(GroupType_name, int32(x))
}
func (GroupType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{0}
}

// 群成员类型
type GroupMemberType int32

const (
	GroupMemberType_GROUP_MEMBER_TYPE_UNSPECIFIED GroupMemberType = 0
	// 用户
	GroupMemberType_GROUP_MEMBER_TYPE_USER GroupMemberType = 1
	// 角色
	GroupMemberType_GROUP_MEMBER_TYPE_ROLE GroupMemberType = 2
)

var GroupMemberType_name = map[int32]string{
	0: "GROUP_MEMBER_TYPE_UNSPECIFIED",
	1: "GROUP_MEMBER_TYPE_USER",
	2: "GROUP_MEMBER_TYPE_ROLE",
}
var GroupMemberType_value = map[string]int32{
	"GROUP_MEMBER_TYPE_UNSPECIFIED": 0,
	"GROUP_MEMBER_TYPE_USER":        1,
	"GROUP_MEMBER_TYPE_ROLE":        2,
}

func (x GroupMemberType) String() string {
	return proto.EnumName(GroupMemberType_name, int32(x))
}
func (GroupMemberType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{1}
}

// 匹配策略
type MatchStrategy int32

const (
	MatchStrategy_MATCH_STRATEGY_UNSPECIFIED MatchStrategy = 0
	MatchStrategy_MATCH_STRATEGY_POOL        MatchStrategy = 1
	MatchStrategy_MATCH_STRATEGY_GROUP       MatchStrategy = 2
)

var MatchStrategy_name = map[int32]string{
	0: "MATCH_STRATEGY_UNSPECIFIED",
	1: "MATCH_STRATEGY_POOL",
	2: "MATCH_STRATEGY_GROUP",
}
var MatchStrategy_value = map[string]int32{
	"MATCH_STRATEGY_UNSPECIFIED": 0,
	"MATCH_STRATEGY_POOL":        1,
	"MATCH_STRATEGY_GROUP":       2,
}

func (x MatchStrategy) String() string {
	return proto.EnumName(MatchStrategy_name, int32(x))
}
func (MatchStrategy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{2}
}

// 群来源
type GroupSource int32

const (
	GroupSource_GROUP_SOURCE_UNSPECIFIED GroupSource = 0
	// 用户创建
	GroupSource_GROUP_SOURCE_USER GroupSource = 1
	// 匹配池创建
	GroupSource_GROUP_SOURCE_MATCH_POOL GroupSource = 2
	// 匹配群创建
	GroupSource_GROUP_SOURCE_MATCH_GROUP GroupSource = 3
)

var GroupSource_name = map[int32]string{
	0: "GROUP_SOURCE_UNSPECIFIED",
	1: "GROUP_SOURCE_USER",
	2: "GROUP_SOURCE_MATCH_POOL",
	3: "GROUP_SOURCE_MATCH_GROUP",
}
var GroupSource_value = map[string]int32{
	"GROUP_SOURCE_UNSPECIFIED": 0,
	"GROUP_SOURCE_USER":        1,
	"GROUP_SOURCE_MATCH_POOL":  2,
	"GROUP_SOURCE_MATCH_GROUP": 3,
}

func (x GroupSource) String() string {
	return proto.EnumName(GroupSource_name, int32(x))
}
func (GroupSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{3}
}

type LeaveStrategy int32

const (
	LeaveStrategy_LEAVE_STRATEGY_UNSPECIFIED LeaveStrategy = 0
	LeaveStrategy_LEAVE_STRATEGY_QUIT        LeaveStrategy = 1
)

var LeaveStrategy_name = map[int32]string{
	0: "LEAVE_STRATEGY_UNSPECIFIED",
	1: "LEAVE_STRATEGY_QUIT",
}
var LeaveStrategy_value = map[string]int32{
	"LEAVE_STRATEGY_UNSPECIFIED": 0,
	"LEAVE_STRATEGY_QUIT":        1,
}

func (x LeaveStrategy) String() string {
	return proto.EnumName(LeaveStrategy_name, int32(x))
}
func (LeaveStrategy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{4}
}

type SpecialMsgType int32

const (
	SpecialMsgType_SPECIAL_MSG_TYPE_UNSPECIFIED SpecialMsgType = 0
	SpecialMsgType_SPECIAL_MSG_TYPE_ONLY_ME     SpecialMsgType = 1
)

var SpecialMsgType_name = map[int32]string{
	0: "SPECIAL_MSG_TYPE_UNSPECIFIED",
	1: "SPECIAL_MSG_TYPE_ONLY_ME",
}
var SpecialMsgType_value = map[string]int32{
	"SPECIAL_MSG_TYPE_UNSPECIFIED": 0,
	"SPECIAL_MSG_TYPE_ONLY_ME":     1,
}

func (x SpecialMsgType) String() string {
	return proto.EnumName(SpecialMsgType_name, int32(x))
}
func (SpecialMsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{5}
}

type GroupTriggerTask_Type int32

const (
	GroupTriggerTask_TYPE_UNSPECIFIED GroupTriggerTask_Type = 0
	// 检测群活跃度
	GroupTriggerTask_TYPE_DETECT_GROUP_ACTIVITY GroupTriggerTask_Type = 1
	// 检测群成员活跃度
	GroupTriggerTask_TYPE_DETECT_GROUP_MEMBERS_ACTIVITY GroupTriggerTask_Type = 2
)

var GroupTriggerTask_Type_name = map[int32]string{
	0: "TYPE_UNSPECIFIED",
	1: "TYPE_DETECT_GROUP_ACTIVITY",
	2: "TYPE_DETECT_GROUP_MEMBERS_ACTIVITY",
}
var GroupTriggerTask_Type_value = map[string]int32{
	"TYPE_UNSPECIFIED":                   0,
	"TYPE_DETECT_GROUP_ACTIVITY":         1,
	"TYPE_DETECT_GROUP_MEMBERS_ACTIVITY": 2,
}

func (x GroupTriggerTask_Type) String() string {
	return proto.EnumName(GroupTriggerTask_Type_name, int32(x))
}
func (GroupTriggerTask_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{12, 0}
}

type GetScriptListByFilterRequest_SexOpt int32

const (
	GetScriptListByFilterRequest_SEX_UNSPECIFIED GetScriptListByFilterRequest_SexOpt = 0
	GetScriptListByFilterRequest_SEX_FEMALE      GetScriptListByFilterRequest_SexOpt = 1
	GetScriptListByFilterRequest_SEX_MALE        GetScriptListByFilterRequest_SexOpt = 2
	GetScriptListByFilterRequest_SEX_ALL         GetScriptListByFilterRequest_SexOpt = 3
)

var GetScriptListByFilterRequest_SexOpt_name = map[int32]string{
	0: "SEX_UNSPECIFIED",
	1: "SEX_FEMALE",
	2: "SEX_MALE",
	3: "SEX_ALL",
}
var GetScriptListByFilterRequest_SexOpt_value = map[string]int32{
	"SEX_UNSPECIFIED": 0,
	"SEX_FEMALE":      1,
	"SEX_MALE":        2,
	"SEX_ALL":         3,
}

func (x GetScriptListByFilterRequest_SexOpt) String() string {
	return proto.EnumName(GetScriptListByFilterRequest_SexOpt_name, int32(x))
}
func (GetScriptListByFilterRequest_SexOpt) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{76, 0}
}

type GroupTemplate struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 群名称,玩法名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 群聊描述，玩法简介
	Character string `protobuf:"bytes,3,opt,name=character,proto3" json:"character,omitempty"`
	// 群标签，
	Tags []string `protobuf:"bytes,4,rep,name=tags,proto3" json:"tags,omitempty"`
	// 群头像，玩法头像
	Avatar string `protobuf:"bytes,5,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 群性别、玩法性别 0:女 1:男 2:其它
	Sex int32 `protobuf:"varint,6,opt,name=sex,proto3" json:"sex,omitempty"`
	// 聊天页背景图、玩法背景
	ChatBackgroundImg string `protobuf:"bytes,7,opt,name=chat_background_img,json=chatBackgroundImg,proto3" json:"chat_background_img,omitempty"`
	// 首页背景图，玩法封面
	HomeBackgroundImg string `protobuf:"bytes,8,opt,name=home_background_img,json=homeBackgroundImg,proto3" json:"home_background_img,omitempty"`
	// 群聊icon
	GroupIcon string `protobuf:"bytes,9,opt,name=group_icon,json=groupIcon,proto3" json:"group_icon,omitempty"`
	// 是否展示/曝光到首页 true:展示 false:不展示
	Exposed bool `protobuf:"varint,10,opt,name=exposed,proto3" json:"exposed,omitempty"`
	// 强插位置
	InsertPos uint32 `protobuf:"varint,11,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	// 角标 url
	CornerIcon string `protobuf:"bytes,12,opt,name=corner_icon,json=cornerIcon,proto3" json:"corner_icon,omitempty"`
	// 群组所属分类ID
	CategoryId string `protobuf:"bytes,13,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 配置点赞数
	ConfigLikeNum int32 `protobuf:"varint,14,opt,name=config_like_num,json=configLikeNum,proto3" json:"config_like_num,omitempty"`
	// 配置的角色id列表
	RoleIds []uint32 `protobuf:"varint,15,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	// 展示在IMtab的标签
	ImTabTags string `protobuf:"bytes,16,opt,name=im_tab_tags,json=imTabTags,proto3" json:"im_tab_tags,omitempty"`
	// 类型
	GroupType GroupType `protobuf:"varint,17,opt,name=group_type,json=groupType,proto3,enum=aigc_group.GroupType" json:"group_type,omitempty"`
	// 多人剧本玩法额外字段
	ScriptInfo       *ScriptInfo        `protobuf:"bytes,18,opt,name=script_info,json=scriptInfo,proto3" json:"script_info,omitempty"`
	SuitableSex      []int32            `protobuf:"varint,19,rep,packed,name=suitable_sex,json=suitableSex,proto3" json:"suitable_sex,omitempty"`
	DefaultPrologues []*AIGroupPrologue `protobuf:"bytes,20,rep,name=default_prologues,json=defaultPrologues,proto3" json:"default_prologues,omitempty"`
	// 多用户玩法一键发言列表
	QuickSpeakTexts      []string      `protobuf:"bytes,21,rep,name=quick_speak_texts,json=quickSpeakTexts,proto3" json:"quick_speak_texts,omitempty"`
	LeaveStrategy        LeaveStrategy `protobuf:"varint,22,opt,name=leave_strategy,json=leaveStrategy,proto3,enum=aigc_group.LeaveStrategy" json:"leave_strategy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GroupTemplate) Reset()         { *m = GroupTemplate{} }
func (m *GroupTemplate) String() string { return proto.CompactTextString(m) }
func (*GroupTemplate) ProtoMessage()    {}
func (*GroupTemplate) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{0}
}
func (m *GroupTemplate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupTemplate.Unmarshal(m, b)
}
func (m *GroupTemplate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupTemplate.Marshal(b, m, deterministic)
}
func (dst *GroupTemplate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupTemplate.Merge(dst, src)
}
func (m *GroupTemplate) XXX_Size() int {
	return xxx_messageInfo_GroupTemplate.Size(m)
}
func (m *GroupTemplate) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupTemplate.DiscardUnknown(m)
}

var xxx_messageInfo_GroupTemplate proto.InternalMessageInfo

func (m *GroupTemplate) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupTemplate) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GroupTemplate) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *GroupTemplate) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *GroupTemplate) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *GroupTemplate) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GroupTemplate) GetChatBackgroundImg() string {
	if m != nil {
		return m.ChatBackgroundImg
	}
	return ""
}

func (m *GroupTemplate) GetHomeBackgroundImg() string {
	if m != nil {
		return m.HomeBackgroundImg
	}
	return ""
}

func (m *GroupTemplate) GetGroupIcon() string {
	if m != nil {
		return m.GroupIcon
	}
	return ""
}

func (m *GroupTemplate) GetExposed() bool {
	if m != nil {
		return m.Exposed
	}
	return false
}

func (m *GroupTemplate) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *GroupTemplate) GetCornerIcon() string {
	if m != nil {
		return m.CornerIcon
	}
	return ""
}

func (m *GroupTemplate) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *GroupTemplate) GetConfigLikeNum() int32 {
	if m != nil {
		return m.ConfigLikeNum
	}
	return 0
}

func (m *GroupTemplate) GetRoleIds() []uint32 {
	if m != nil {
		return m.RoleIds
	}
	return nil
}

func (m *GroupTemplate) GetImTabTags() string {
	if m != nil {
		return m.ImTabTags
	}
	return ""
}

func (m *GroupTemplate) GetGroupType() GroupType {
	if m != nil {
		return m.GroupType
	}
	return GroupType_GROUP_TYPE_UNSPECIFIED
}

func (m *GroupTemplate) GetScriptInfo() *ScriptInfo {
	if m != nil {
		return m.ScriptInfo
	}
	return nil
}

func (m *GroupTemplate) GetSuitableSex() []int32 {
	if m != nil {
		return m.SuitableSex
	}
	return nil
}

func (m *GroupTemplate) GetDefaultPrologues() []*AIGroupPrologue {
	if m != nil {
		return m.DefaultPrologues
	}
	return nil
}

func (m *GroupTemplate) GetQuickSpeakTexts() []string {
	if m != nil {
		return m.QuickSpeakTexts
	}
	return nil
}

func (m *GroupTemplate) GetLeaveStrategy() LeaveStrategy {
	if m != nil {
		return m.LeaveStrategy
	}
	return LeaveStrategy_LEAVE_STRATEGY_UNSPECIFIED
}

// 用户扮演的角色信息
type PlayRole struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Avatar               string   `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	SuitableSex          []int32  `protobuf:"varint,4,rep,packed,name=suitable_sex,json=suitableSex,proto3" json:"suitable_sex,omitempty"`
	Character            string   `protobuf:"bytes,5,opt,name=character,proto3" json:"character,omitempty"`
	QuickSpeakTexts      []string `protobuf:"bytes,6,rep,name=quick_speak_texts,json=quickSpeakTexts,proto3" json:"quick_speak_texts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlayRole) Reset()         { *m = PlayRole{} }
func (m *PlayRole) String() string { return proto.CompactTextString(m) }
func (*PlayRole) ProtoMessage()    {}
func (*PlayRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{1}
}
func (m *PlayRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayRole.Unmarshal(m, b)
}
func (m *PlayRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayRole.Marshal(b, m, deterministic)
}
func (dst *PlayRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayRole.Merge(dst, src)
}
func (m *PlayRole) XXX_Size() int {
	return xxx_messageInfo_PlayRole.Size(m)
}
func (m *PlayRole) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayRole.DiscardUnknown(m)
}

var xxx_messageInfo_PlayRole proto.InternalMessageInfo

func (m *PlayRole) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PlayRole) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *PlayRole) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PlayRole) GetSuitableSex() []int32 {
	if m != nil {
		return m.SuitableSex
	}
	return nil
}

func (m *PlayRole) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *PlayRole) GetQuickSpeakTexts() []string {
	if m != nil {
		return m.QuickSpeakTexts
	}
	return nil
}

// 开场白
type AIGroupPrologue struct {
	// 开场白文本内容
	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// 开场白语音链接
	Audio string `protobuf:"bytes,2,opt,name=audio,proto3" json:"audio,omitempty"`
	// 开场白顺序
	Priority             uint32   `protobuf:"varint,3,opt,name=priority,proto3" json:"priority,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIGroupPrologue) Reset()         { *m = AIGroupPrologue{} }
func (m *AIGroupPrologue) String() string { return proto.CompactTextString(m) }
func (*AIGroupPrologue) ProtoMessage()    {}
func (*AIGroupPrologue) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{2}
}
func (m *AIGroupPrologue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIGroupPrologue.Unmarshal(m, b)
}
func (m *AIGroupPrologue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIGroupPrologue.Marshal(b, m, deterministic)
}
func (dst *AIGroupPrologue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIGroupPrologue.Merge(dst, src)
}
func (m *AIGroupPrologue) XXX_Size() int {
	return xxx_messageInfo_AIGroupPrologue.Size(m)
}
func (m *AIGroupPrologue) XXX_DiscardUnknown() {
	xxx_messageInfo_AIGroupPrologue.DiscardUnknown(m)
}

var xxx_messageInfo_AIGroupPrologue proto.InternalMessageInfo

func (m *AIGroupPrologue) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *AIGroupPrologue) GetAudio() string {
	if m != nil {
		return m.Audio
	}
	return ""
}

func (m *AIGroupPrologue) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

type ScriptInfo struct {
	PlayRoles            []*PlayRole   `protobuf:"bytes,1,rep,name=play_roles,json=playRoles,proto3" json:"play_roles,omitempty"`
	ButtonDisplayText    string        `protobuf:"bytes,2,opt,name=button_display_text,json=buttonDisplayText,proto3" json:"button_display_text,omitempty"`
	BackgroundMusic      string        `protobuf:"bytes,3,opt,name=background_music,json=backgroundMusic,proto3" json:"background_music,omitempty"`
	UserNum              uint32        `protobuf:"varint,4,opt,name=user_num,json=userNum,proto3" json:"user_num,omitempty"`
	MaleNum              uint32        `protobuf:"varint,5,opt,name=male_num,json=maleNum,proto3" json:"male_num,omitempty"`
	FemaleNum            uint32        `protobuf:"varint,6,opt,name=female_num,json=femaleNum,proto3" json:"female_num,omitempty"`
	Sort                 uint32        `protobuf:"varint,7,opt,name=sort,proto3" json:"sort,omitempty"`
	AnyNum               uint32        `protobuf:"varint,8,opt,name=any_num,json=anyNum,proto3" json:"any_num,omitempty"`
	MatchStrategy        MatchStrategy `protobuf:"varint,9,opt,name=match_strategy,json=matchStrategy,proto3,enum=aigc_group.MatchStrategy" json:"match_strategy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ScriptInfo) Reset()         { *m = ScriptInfo{} }
func (m *ScriptInfo) String() string { return proto.CompactTextString(m) }
func (*ScriptInfo) ProtoMessage()    {}
func (*ScriptInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{3}
}
func (m *ScriptInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScriptInfo.Unmarshal(m, b)
}
func (m *ScriptInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScriptInfo.Marshal(b, m, deterministic)
}
func (dst *ScriptInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScriptInfo.Merge(dst, src)
}
func (m *ScriptInfo) XXX_Size() int {
	return xxx_messageInfo_ScriptInfo.Size(m)
}
func (m *ScriptInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ScriptInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ScriptInfo proto.InternalMessageInfo

func (m *ScriptInfo) GetPlayRoles() []*PlayRole {
	if m != nil {
		return m.PlayRoles
	}
	return nil
}

func (m *ScriptInfo) GetButtonDisplayText() string {
	if m != nil {
		return m.ButtonDisplayText
	}
	return ""
}

func (m *ScriptInfo) GetBackgroundMusic() string {
	if m != nil {
		return m.BackgroundMusic
	}
	return ""
}

func (m *ScriptInfo) GetUserNum() uint32 {
	if m != nil {
		return m.UserNum
	}
	return 0
}

func (m *ScriptInfo) GetMaleNum() uint32 {
	if m != nil {
		return m.MaleNum
	}
	return 0
}

func (m *ScriptInfo) GetFemaleNum() uint32 {
	if m != nil {
		return m.FemaleNum
	}
	return 0
}

func (m *ScriptInfo) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *ScriptInfo) GetAnyNum() uint32 {
	if m != nil {
		return m.AnyNum
	}
	return 0
}

func (m *ScriptInfo) GetMatchStrategy() MatchStrategy {
	if m != nil {
		return m.MatchStrategy
	}
	return MatchStrategy_MATCH_STRATEGY_UNSPECIFIED
}

// 群组信息
type Group struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 群组类型
	Type GroupType `protobuf:"varint,2,opt,name=type,proto3,enum=aigc_group.GroupType" json:"type,omitempty"`
	// 建群时间戳(秒)
	CreatedAt int64 `protobuf:"varint,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 建群来源
	Source GroupSource `protobuf:"varint,4,opt,name=source,proto3,enum=aigc_group.GroupSource" json:"source,omitempty"`
	// 群主uid
	OwnerUid uint32 `protobuf:"varint,11,opt,name=owner_uid,json=ownerUid,proto3" json:"owner_uid,omitempty"`
	// 关联的群模板
	TemplateId uint32 `protobuf:"varint,12,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// 配置的群点赞数
	ConfigLikeNum int32 `protobuf:"varint,15,opt,name=config_like_num,json=configLikeNum,proto3" json:"config_like_num,omitempty"`
	// 群人数
	MemberNum uint32 `protobuf:"varint,16,opt,name=member_num,json=memberNum,proto3" json:"member_num,omitempty"`
	// 群用户上限
	UserLimit uint32 `protobuf:"varint,17,opt,name=user_limit,json=userLimit,proto3" json:"user_limit,omitempty"`
	// 群性别
	Sex int32 `protobuf:"varint,21,opt,name=sex,proto3" json:"sex,omitempty"`
	// 群名称
	Name string `protobuf:"bytes,22,opt,name=name,proto3" json:"name,omitempty"`
	// 群描述
	Desc string `protobuf:"bytes,23,opt,name=desc,proto3" json:"desc,omitempty"`
	// 群头像
	Avatar string `protobuf:"bytes,24,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 聊天背景图
	ChatBackground string `protobuf:"bytes,25,opt,name=chat_background,json=chatBackground,proto3" json:"chat_background,omitempty"`
	// im tab 标签
	ImTabTag string `protobuf:"bytes,26,opt,name=im_tab_tag,json=imTabTag,proto3" json:"im_tab_tag,omitempty"`
	// 退群策略
	LeaveStrategy        LeaveStrategy `protobuf:"varint,27,opt,name=leave_strategy,json=leaveStrategy,proto3,enum=aigc_group.LeaveStrategy" json:"leave_strategy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *Group) Reset()         { *m = Group{} }
func (m *Group) String() string { return proto.CompactTextString(m) }
func (*Group) ProtoMessage()    {}
func (*Group) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{4}
}
func (m *Group) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Group.Unmarshal(m, b)
}
func (m *Group) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Group.Marshal(b, m, deterministic)
}
func (dst *Group) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Group.Merge(dst, src)
}
func (m *Group) XXX_Size() int {
	return xxx_messageInfo_Group.Size(m)
}
func (m *Group) XXX_DiscardUnknown() {
	xxx_messageInfo_Group.DiscardUnknown(m)
}

var xxx_messageInfo_Group proto.InternalMessageInfo

func (m *Group) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Group) GetType() GroupType {
	if m != nil {
		return m.Type
	}
	return GroupType_GROUP_TYPE_UNSPECIFIED
}

func (m *Group) GetCreatedAt() int64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *Group) GetSource() GroupSource {
	if m != nil {
		return m.Source
	}
	return GroupSource_GROUP_SOURCE_UNSPECIFIED
}

func (m *Group) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

func (m *Group) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *Group) GetConfigLikeNum() int32 {
	if m != nil {
		return m.ConfigLikeNum
	}
	return 0
}

func (m *Group) GetMemberNum() uint32 {
	if m != nil {
		return m.MemberNum
	}
	return 0
}

func (m *Group) GetUserLimit() uint32 {
	if m != nil {
		return m.UserLimit
	}
	return 0
}

func (m *Group) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *Group) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Group) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *Group) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *Group) GetChatBackground() string {
	if m != nil {
		return m.ChatBackground
	}
	return ""
}

func (m *Group) GetImTabTag() string {
	if m != nil {
		return m.ImTabTag
	}
	return ""
}

func (m *Group) GetLeaveStrategy() LeaveStrategy {
	if m != nil {
		return m.LeaveStrategy
	}
	return LeaveStrategy_LEAVE_STRATEGY_UNSPECIFIED
}

// 群成员信息
type GroupMember struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 群成员类型
	Type GroupMemberType `protobuf:"varint,2,opt,name=type,proto3,enum=aigc_group.GroupMemberType" json:"type,omitempty"`
	// 入群时间,毫秒级
	JoinedAt             int64    `protobuf:"varint,3,opt,name=joined_at,json=joinedAt,proto3" json:"joined_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupMember) Reset()         { *m = GroupMember{} }
func (m *GroupMember) String() string { return proto.CompactTextString(m) }
func (*GroupMember) ProtoMessage()    {}
func (*GroupMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{5}
}
func (m *GroupMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupMember.Unmarshal(m, b)
}
func (m *GroupMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupMember.Marshal(b, m, deterministic)
}
func (dst *GroupMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupMember.Merge(dst, src)
}
func (m *GroupMember) XXX_Size() int {
	return xxx_messageInfo_GroupMember.Size(m)
}
func (m *GroupMember) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupMember.DiscardUnknown(m)
}

var xxx_messageInfo_GroupMember proto.InternalMessageInfo

func (m *GroupMember) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupMember) GetType() GroupMemberType {
	if m != nil {
		return m.Type
	}
	return GroupMemberType_GROUP_MEMBER_TYPE_UNSPECIFIED
}

func (m *GroupMember) GetJoinedAt() int64 {
	if m != nil {
		return m.JoinedAt
	}
	return 0
}

// 匹配就绪通知
type MatchReadyNotify struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Token                string   `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	TriggeredAt          int64    `protobuf:"varint,3,opt,name=triggered_at,json=triggeredAt,proto3" json:"triggered_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MatchReadyNotify) Reset()         { *m = MatchReadyNotify{} }
func (m *MatchReadyNotify) String() string { return proto.CompactTextString(m) }
func (*MatchReadyNotify) ProtoMessage()    {}
func (*MatchReadyNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{6}
}
func (m *MatchReadyNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MatchReadyNotify.Unmarshal(m, b)
}
func (m *MatchReadyNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MatchReadyNotify.Marshal(b, m, deterministic)
}
func (dst *MatchReadyNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MatchReadyNotify.Merge(dst, src)
}
func (m *MatchReadyNotify) XXX_Size() int {
	return xxx_messageInfo_MatchReadyNotify.Size(m)
}
func (m *MatchReadyNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_MatchReadyNotify.DiscardUnknown(m)
}

var xxx_messageInfo_MatchReadyNotify proto.InternalMessageInfo

func (m *MatchReadyNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MatchReadyNotify) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *MatchReadyNotify) GetTriggeredAt() int64 {
	if m != nil {
		return m.TriggeredAt
	}
	return 0
}

// 匹配成功通知
type MatchFoundNotify struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GroupId              uint32   `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	TriggeredAt          int64    `protobuf:"varint,3,opt,name=triggered_at,json=triggeredAt,proto3" json:"triggered_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MatchFoundNotify) Reset()         { *m = MatchFoundNotify{} }
func (m *MatchFoundNotify) String() string { return proto.CompactTextString(m) }
func (*MatchFoundNotify) ProtoMessage()    {}
func (*MatchFoundNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{7}
}
func (m *MatchFoundNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MatchFoundNotify.Unmarshal(m, b)
}
func (m *MatchFoundNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MatchFoundNotify.Marshal(b, m, deterministic)
}
func (dst *MatchFoundNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MatchFoundNotify.Merge(dst, src)
}
func (m *MatchFoundNotify) XXX_Size() int {
	return xxx_messageInfo_MatchFoundNotify.Size(m)
}
func (m *MatchFoundNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_MatchFoundNotify.DiscardUnknown(m)
}

var xxx_messageInfo_MatchFoundNotify proto.InternalMessageInfo

func (m *MatchFoundNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MatchFoundNotify) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MatchFoundNotify) GetTriggeredAt() int64 {
	if m != nil {
		return m.TriggeredAt
	}
	return 0
}

type MatchPlayer struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Sex                  int32    `protobuf:"varint,2,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MatchPlayer) Reset()         { *m = MatchPlayer{} }
func (m *MatchPlayer) String() string { return proto.CompactTextString(m) }
func (*MatchPlayer) ProtoMessage()    {}
func (*MatchPlayer) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{8}
}
func (m *MatchPlayer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MatchPlayer.Unmarshal(m, b)
}
func (m *MatchPlayer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MatchPlayer.Marshal(b, m, deterministic)
}
func (dst *MatchPlayer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MatchPlayer.Merge(dst, src)
}
func (m *MatchPlayer) XXX_Size() int {
	return xxx_messageInfo_MatchPlayer.Size(m)
}
func (m *MatchPlayer) XXX_DiscardUnknown() {
	xxx_messageInfo_MatchPlayer.DiscardUnknown(m)
}

var xxx_messageInfo_MatchPlayer proto.InternalMessageInfo

func (m *MatchPlayer) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MatchPlayer) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type ActiveGroup struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 最近活跃时间
	LastActiveAt int64 `protobuf:"varint,2,opt,name=last_active_at,json=lastActiveAt,proto3" json:"last_active_at,omitempty"`
	// 活跃群成员
	Members []*ActiveGroupMember `protobuf:"bytes,3,rep,name=members,proto3" json:"members,omitempty"`
	// 群类型
	Type GroupType `protobuf:"varint,4,opt,name=type,proto3,enum=aigc_group.GroupType" json:"type,omitempty"`
	// 群模板id
	TemplateId           uint32   `protobuf:"varint,5,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActiveGroup) Reset()         { *m = ActiveGroup{} }
func (m *ActiveGroup) String() string { return proto.CompactTextString(m) }
func (*ActiveGroup) ProtoMessage()    {}
func (*ActiveGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{9}
}
func (m *ActiveGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActiveGroup.Unmarshal(m, b)
}
func (m *ActiveGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActiveGroup.Marshal(b, m, deterministic)
}
func (dst *ActiveGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActiveGroup.Merge(dst, src)
}
func (m *ActiveGroup) XXX_Size() int {
	return xxx_messageInfo_ActiveGroup.Size(m)
}
func (m *ActiveGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_ActiveGroup.DiscardUnknown(m)
}

var xxx_messageInfo_ActiveGroup proto.InternalMessageInfo

func (m *ActiveGroup) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ActiveGroup) GetLastActiveAt() int64 {
	if m != nil {
		return m.LastActiveAt
	}
	return 0
}

func (m *ActiveGroup) GetMembers() []*ActiveGroupMember {
	if m != nil {
		return m.Members
	}
	return nil
}

func (m *ActiveGroup) GetType() GroupType {
	if m != nil {
		return m.Type
	}
	return GroupType_GROUP_TYPE_UNSPECIFIED
}

func (m *ActiveGroup) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

type ActiveGroupMember struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	LastActiveAt         int64    `protobuf:"varint,2,opt,name=last_active_at,json=lastActiveAt,proto3" json:"last_active_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActiveGroupMember) Reset()         { *m = ActiveGroupMember{} }
func (m *ActiveGroupMember) String() string { return proto.CompactTextString(m) }
func (*ActiveGroupMember) ProtoMessage()    {}
func (*ActiveGroupMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{10}
}
func (m *ActiveGroupMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActiveGroupMember.Unmarshal(m, b)
}
func (m *ActiveGroupMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActiveGroupMember.Marshal(b, m, deterministic)
}
func (dst *ActiveGroupMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActiveGroupMember.Merge(dst, src)
}
func (m *ActiveGroupMember) XXX_Size() int {
	return xxx_messageInfo_ActiveGroupMember.Size(m)
}
func (m *ActiveGroupMember) XXX_DiscardUnknown() {
	xxx_messageInfo_ActiveGroupMember.DiscardUnknown(m)
}

var xxx_messageInfo_ActiveGroupMember proto.InternalMessageInfo

func (m *ActiveGroupMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ActiveGroupMember) GetLastActiveAt() int64 {
	if m != nil {
		return m.LastActiveAt
	}
	return 0
}

type MatchPlayerList struct {
	List                 []*MatchPlayer `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *MatchPlayerList) Reset()         { *m = MatchPlayerList{} }
func (m *MatchPlayerList) String() string { return proto.CompactTextString(m) }
func (*MatchPlayerList) ProtoMessage()    {}
func (*MatchPlayerList) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{11}
}
func (m *MatchPlayerList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MatchPlayerList.Unmarshal(m, b)
}
func (m *MatchPlayerList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MatchPlayerList.Marshal(b, m, deterministic)
}
func (dst *MatchPlayerList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MatchPlayerList.Merge(dst, src)
}
func (m *MatchPlayerList) XXX_Size() int {
	return xxx_messageInfo_MatchPlayerList.Size(m)
}
func (m *MatchPlayerList) XXX_DiscardUnknown() {
	xxx_messageInfo_MatchPlayerList.DiscardUnknown(m)
}

var xxx_messageInfo_MatchPlayerList proto.InternalMessageInfo

func (m *MatchPlayerList) GetList() []*MatchPlayer {
	if m != nil {
		return m.List
	}
	return nil
}

type GroupTriggerTask struct {
	GroupId              uint32                `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Type                 GroupTriggerTask_Type `protobuf:"varint,2,opt,name=type,proto3,enum=aigc_group.GroupTriggerTask_Type" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GroupTriggerTask) Reset()         { *m = GroupTriggerTask{} }
func (m *GroupTriggerTask) String() string { return proto.CompactTextString(m) }
func (*GroupTriggerTask) ProtoMessage()    {}
func (*GroupTriggerTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{12}
}
func (m *GroupTriggerTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupTriggerTask.Unmarshal(m, b)
}
func (m *GroupTriggerTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupTriggerTask.Marshal(b, m, deterministic)
}
func (dst *GroupTriggerTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupTriggerTask.Merge(dst, src)
}
func (m *GroupTriggerTask) XXX_Size() int {
	return xxx_messageInfo_GroupTriggerTask.Size(m)
}
func (m *GroupTriggerTask) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupTriggerTask.DiscardUnknown(m)
}

var xxx_messageInfo_GroupTriggerTask proto.InternalMessageInfo

func (m *GroupTriggerTask) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupTriggerTask) GetType() GroupTriggerTask_Type {
	if m != nil {
		return m.Type
	}
	return GroupTriggerTask_TYPE_UNSPECIFIED
}

// 关注引导通知
type FollowGuideNotify struct {
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FollowGuideNotify) Reset()         { *m = FollowGuideNotify{} }
func (m *FollowGuideNotify) String() string { return proto.CompactTextString(m) }
func (*FollowGuideNotify) ProtoMessage()    {}
func (*FollowGuideNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{13}
}
func (m *FollowGuideNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FollowGuideNotify.Unmarshal(m, b)
}
func (m *FollowGuideNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FollowGuideNotify.Marshal(b, m, deterministic)
}
func (dst *FollowGuideNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FollowGuideNotify.Merge(dst, src)
}
func (m *FollowGuideNotify) XXX_Size() int {
	return xxx_messageInfo_FollowGuideNotify.Size(m)
}
func (m *FollowGuideNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_FollowGuideNotify.DiscardUnknown(m)
}

var xxx_messageInfo_FollowGuideNotify proto.InternalMessageInfo

func (m *FollowGuideNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FollowGuideNotify) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

// 用户入群通知
type GroupMemberJoinedNotify struct {
	// 加入成员的uid
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 加入的群id
	GroupId              uint32   `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupMemberJoinedNotify) Reset()         { *m = GroupMemberJoinedNotify{} }
func (m *GroupMemberJoinedNotify) String() string { return proto.CompactTextString(m) }
func (*GroupMemberJoinedNotify) ProtoMessage()    {}
func (*GroupMemberJoinedNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{14}
}
func (m *GroupMemberJoinedNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupMemberJoinedNotify.Unmarshal(m, b)
}
func (m *GroupMemberJoinedNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupMemberJoinedNotify.Marshal(b, m, deterministic)
}
func (dst *GroupMemberJoinedNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupMemberJoinedNotify.Merge(dst, src)
}
func (m *GroupMemberJoinedNotify) XXX_Size() int {
	return xxx_messageInfo_GroupMemberJoinedNotify.Size(m)
}
func (m *GroupMemberJoinedNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupMemberJoinedNotify.DiscardUnknown(m)
}

var xxx_messageInfo_GroupMemberJoinedNotify proto.InternalMessageInfo

func (m *GroupMemberJoinedNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GroupMemberJoinedNotify) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type User struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Sex                  int32    `protobuf:"varint,2,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *User) Reset()         { *m = User{} }
func (m *User) String() string { return proto.CompactTextString(m) }
func (*User) ProtoMessage()    {}
func (*User) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{15}
}
func (m *User) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_User.Unmarshal(m, b)
}
func (m *User) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_User.Marshal(b, m, deterministic)
}
func (dst *User) XXX_Merge(src proto.Message) {
	xxx_messageInfo_User.Merge(dst, src)
}
func (m *User) XXX_Size() int {
	return xxx_messageInfo_User.Size(m)
}
func (m *User) XXX_DiscardUnknown() {
	xxx_messageInfo_User.DiscardUnknown(m)
}

var xxx_messageInfo_User proto.InternalMessageInfo

func (m *User) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *User) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

// 创建群模板
type CreateGroupTemplateRequest struct {
	GroupTemplate        *GroupTemplate `protobuf:"bytes,1,opt,name=group_template,json=groupTemplate,proto3" json:"group_template,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CreateGroupTemplateRequest) Reset()         { *m = CreateGroupTemplateRequest{} }
func (m *CreateGroupTemplateRequest) String() string { return proto.CompactTextString(m) }
func (*CreateGroupTemplateRequest) ProtoMessage()    {}
func (*CreateGroupTemplateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{16}
}
func (m *CreateGroupTemplateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGroupTemplateRequest.Unmarshal(m, b)
}
func (m *CreateGroupTemplateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGroupTemplateRequest.Marshal(b, m, deterministic)
}
func (dst *CreateGroupTemplateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGroupTemplateRequest.Merge(dst, src)
}
func (m *CreateGroupTemplateRequest) XXX_Size() int {
	return xxx_messageInfo_CreateGroupTemplateRequest.Size(m)
}
func (m *CreateGroupTemplateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGroupTemplateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGroupTemplateRequest proto.InternalMessageInfo

func (m *CreateGroupTemplateRequest) GetGroupTemplate() *GroupTemplate {
	if m != nil {
		return m.GroupTemplate
	}
	return nil
}

type CreateGroupTemplateResponse struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGroupTemplateResponse) Reset()         { *m = CreateGroupTemplateResponse{} }
func (m *CreateGroupTemplateResponse) String() string { return proto.CompactTextString(m) }
func (*CreateGroupTemplateResponse) ProtoMessage()    {}
func (*CreateGroupTemplateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{17}
}
func (m *CreateGroupTemplateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGroupTemplateResponse.Unmarshal(m, b)
}
func (m *CreateGroupTemplateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGroupTemplateResponse.Marshal(b, m, deterministic)
}
func (dst *CreateGroupTemplateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGroupTemplateResponse.Merge(dst, src)
}
func (m *CreateGroupTemplateResponse) XXX_Size() int {
	return xxx_messageInfo_CreateGroupTemplateResponse.Size(m)
}
func (m *CreateGroupTemplateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGroupTemplateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGroupTemplateResponse proto.InternalMessageInfo

func (m *CreateGroupTemplateResponse) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 更新群模板
type UpdateGroupTemplateRequest struct {
	GroupTemplate        *GroupTemplate `protobuf:"bytes,1,opt,name=group_template,json=groupTemplate,proto3" json:"group_template,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UpdateGroupTemplateRequest) Reset()         { *m = UpdateGroupTemplateRequest{} }
func (m *UpdateGroupTemplateRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupTemplateRequest) ProtoMessage()    {}
func (*UpdateGroupTemplateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{18}
}
func (m *UpdateGroupTemplateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupTemplateRequest.Unmarshal(m, b)
}
func (m *UpdateGroupTemplateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupTemplateRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupTemplateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupTemplateRequest.Merge(dst, src)
}
func (m *UpdateGroupTemplateRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupTemplateRequest.Size(m)
}
func (m *UpdateGroupTemplateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupTemplateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupTemplateRequest proto.InternalMessageInfo

func (m *UpdateGroupTemplateRequest) GetGroupTemplate() *GroupTemplate {
	if m != nil {
		return m.GroupTemplate
	}
	return nil
}

type UpdateGroupTemplateResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupTemplateResponse) Reset()         { *m = UpdateGroupTemplateResponse{} }
func (m *UpdateGroupTemplateResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupTemplateResponse) ProtoMessage()    {}
func (*UpdateGroupTemplateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{19}
}
func (m *UpdateGroupTemplateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupTemplateResponse.Unmarshal(m, b)
}
func (m *UpdateGroupTemplateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupTemplateResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupTemplateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupTemplateResponse.Merge(dst, src)
}
func (m *UpdateGroupTemplateResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupTemplateResponse.Size(m)
}
func (m *UpdateGroupTemplateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupTemplateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupTemplateResponse proto.InternalMessageInfo

// 删除群模板
type DeleteGroupTemplateRequest struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteGroupTemplateRequest) Reset()         { *m = DeleteGroupTemplateRequest{} }
func (m *DeleteGroupTemplateRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteGroupTemplateRequest) ProtoMessage()    {}
func (*DeleteGroupTemplateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{20}
}
func (m *DeleteGroupTemplateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteGroupTemplateRequest.Unmarshal(m, b)
}
func (m *DeleteGroupTemplateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteGroupTemplateRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteGroupTemplateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteGroupTemplateRequest.Merge(dst, src)
}
func (m *DeleteGroupTemplateRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteGroupTemplateRequest.Size(m)
}
func (m *DeleteGroupTemplateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteGroupTemplateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteGroupTemplateRequest proto.InternalMessageInfo

func (m *DeleteGroupTemplateRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DeleteGroupTemplateResponse struct {
	RoleIds              []uint32 `protobuf:"varint,1,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteGroupTemplateResponse) Reset()         { *m = DeleteGroupTemplateResponse{} }
func (m *DeleteGroupTemplateResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteGroupTemplateResponse) ProtoMessage()    {}
func (*DeleteGroupTemplateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{21}
}
func (m *DeleteGroupTemplateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteGroupTemplateResponse.Unmarshal(m, b)
}
func (m *DeleteGroupTemplateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteGroupTemplateResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteGroupTemplateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteGroupTemplateResponse.Merge(dst, src)
}
func (m *DeleteGroupTemplateResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteGroupTemplateResponse.Size(m)
}
func (m *DeleteGroupTemplateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteGroupTemplateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteGroupTemplateResponse proto.InternalMessageInfo

func (m *DeleteGroupTemplateResponse) GetRoleIds() []uint32 {
	if m != nil {
		return m.RoleIds
	}
	return nil
}

// 根据id获取群模板列表
type GetGroupTemplateByIdsRequest struct {
	Ids                  []uint32 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupTemplateByIdsRequest) Reset()         { *m = GetGroupTemplateByIdsRequest{} }
func (m *GetGroupTemplateByIdsRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupTemplateByIdsRequest) ProtoMessage()    {}
func (*GetGroupTemplateByIdsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{22}
}
func (m *GetGroupTemplateByIdsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupTemplateByIdsRequest.Unmarshal(m, b)
}
func (m *GetGroupTemplateByIdsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupTemplateByIdsRequest.Marshal(b, m, deterministic)
}
func (dst *GetGroupTemplateByIdsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupTemplateByIdsRequest.Merge(dst, src)
}
func (m *GetGroupTemplateByIdsRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupTemplateByIdsRequest.Size(m)
}
func (m *GetGroupTemplateByIdsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupTemplateByIdsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupTemplateByIdsRequest proto.InternalMessageInfo

func (m *GetGroupTemplateByIdsRequest) GetIds() []uint32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type GetGroupTemplateByIdsResponse struct {
	List                 []*GroupTemplate `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGroupTemplateByIdsResponse) Reset()         { *m = GetGroupTemplateByIdsResponse{} }
func (m *GetGroupTemplateByIdsResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupTemplateByIdsResponse) ProtoMessage()    {}
func (*GetGroupTemplateByIdsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{23}
}
func (m *GetGroupTemplateByIdsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupTemplateByIdsResponse.Unmarshal(m, b)
}
func (m *GetGroupTemplateByIdsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupTemplateByIdsResponse.Marshal(b, m, deterministic)
}
func (dst *GetGroupTemplateByIdsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupTemplateByIdsResponse.Merge(dst, src)
}
func (m *GetGroupTemplateByIdsResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupTemplateByIdsResponse.Size(m)
}
func (m *GetGroupTemplateByIdsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupTemplateByIdsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupTemplateByIdsResponse proto.InternalMessageInfo

func (m *GetGroupTemplateByIdsResponse) GetList() []*GroupTemplate {
	if m != nil {
		return m.List
	}
	return nil
}

// 分页获取群模板，用于后台管理. 模板数量不超过200数量少，采用page+size分页
type GetGroupTemplateByPageRequest struct {
	Page                 int64       `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                 int64       `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	GroupType            []GroupType `protobuf:"varint,3,rep,packed,name=group_type,json=groupType,proto3,enum=aigc_group.GroupType" json:"group_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetGroupTemplateByPageRequest) Reset()         { *m = GetGroupTemplateByPageRequest{} }
func (m *GetGroupTemplateByPageRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupTemplateByPageRequest) ProtoMessage()    {}
func (*GetGroupTemplateByPageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{24}
}
func (m *GetGroupTemplateByPageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupTemplateByPageRequest.Unmarshal(m, b)
}
func (m *GetGroupTemplateByPageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupTemplateByPageRequest.Marshal(b, m, deterministic)
}
func (dst *GetGroupTemplateByPageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupTemplateByPageRequest.Merge(dst, src)
}
func (m *GetGroupTemplateByPageRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupTemplateByPageRequest.Size(m)
}
func (m *GetGroupTemplateByPageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupTemplateByPageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupTemplateByPageRequest proto.InternalMessageInfo

func (m *GetGroupTemplateByPageRequest) GetPage() int64 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetGroupTemplateByPageRequest) GetSize() int64 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetGroupTemplateByPageRequest) GetGroupType() []GroupType {
	if m != nil {
		return m.GroupType
	}
	return nil
}

type GetGroupTemplateByPageResponse struct {
	List                 []*GroupTemplate `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                int64            `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGroupTemplateByPageResponse) Reset()         { *m = GetGroupTemplateByPageResponse{} }
func (m *GetGroupTemplateByPageResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupTemplateByPageResponse) ProtoMessage()    {}
func (*GetGroupTemplateByPageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{25}
}
func (m *GetGroupTemplateByPageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupTemplateByPageResponse.Unmarshal(m, b)
}
func (m *GetGroupTemplateByPageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupTemplateByPageResponse.Marshal(b, m, deterministic)
}
func (dst *GetGroupTemplateByPageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupTemplateByPageResponse.Merge(dst, src)
}
func (m *GetGroupTemplateByPageResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupTemplateByPageResponse.Size(m)
}
func (m *GetGroupTemplateByPageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupTemplateByPageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupTemplateByPageResponse proto.InternalMessageInfo

func (m *GetGroupTemplateByPageResponse) GetList() []*GroupTemplate {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetGroupTemplateByPageResponse) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

type CreateGroupRequest struct {
	// 群主uid
	OwnerUid uint32 `protobuf:"varint,1,opt,name=owner_uid,json=ownerUid,proto3" json:"owner_uid,omitempty"`
	// 群模板id
	TemplateId uint32 `protobuf:"varint,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// 群类型
	Type GroupType `protobuf:"varint,3,opt,name=type,proto3,enum=aigc_group.GroupType" json:"type,omitempty"`
	// 群来源
	Source               GroupSource `protobuf:"varint,4,opt,name=source,proto3,enum=aigc_group.GroupSource" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CreateGroupRequest) Reset()         { *m = CreateGroupRequest{} }
func (m *CreateGroupRequest) String() string { return proto.CompactTextString(m) }
func (*CreateGroupRequest) ProtoMessage()    {}
func (*CreateGroupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{26}
}
func (m *CreateGroupRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGroupRequest.Unmarshal(m, b)
}
func (m *CreateGroupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGroupRequest.Marshal(b, m, deterministic)
}
func (dst *CreateGroupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGroupRequest.Merge(dst, src)
}
func (m *CreateGroupRequest) XXX_Size() int {
	return xxx_messageInfo_CreateGroupRequest.Size(m)
}
func (m *CreateGroupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGroupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGroupRequest proto.InternalMessageInfo

func (m *CreateGroupRequest) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

func (m *CreateGroupRequest) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *CreateGroupRequest) GetType() GroupType {
	if m != nil {
		return m.Type
	}
	return GroupType_GROUP_TYPE_UNSPECIFIED
}

func (m *CreateGroupRequest) GetSource() GroupSource {
	if m != nil {
		return m.Source
	}
	return GroupSource_GROUP_SOURCE_UNSPECIFIED
}

type CreateGroupResponse struct {
	// 创建出来的群组id
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGroupResponse) Reset()         { *m = CreateGroupResponse{} }
func (m *CreateGroupResponse) String() string { return proto.CompactTextString(m) }
func (*CreateGroupResponse) ProtoMessage()    {}
func (*CreateGroupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{27}
}
func (m *CreateGroupResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGroupResponse.Unmarshal(m, b)
}
func (m *CreateGroupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGroupResponse.Marshal(b, m, deterministic)
}
func (dst *CreateGroupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGroupResponse.Merge(dst, src)
}
func (m *CreateGroupResponse) XXX_Size() int {
	return xxx_messageInfo_CreateGroupResponse.Size(m)
}
func (m *CreateGroupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGroupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGroupResponse proto.InternalMessageInfo

func (m *CreateGroupResponse) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DeleteGroupRequest struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteGroupRequest) Reset()         { *m = DeleteGroupRequest{} }
func (m *DeleteGroupRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteGroupRequest) ProtoMessage()    {}
func (*DeleteGroupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{28}
}
func (m *DeleteGroupRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteGroupRequest.Unmarshal(m, b)
}
func (m *DeleteGroupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteGroupRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteGroupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteGroupRequest.Merge(dst, src)
}
func (m *DeleteGroupRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteGroupRequest.Size(m)
}
func (m *DeleteGroupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteGroupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteGroupRequest proto.InternalMessageInfo

func (m *DeleteGroupRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DeleteGroupResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteGroupResponse) Reset()         { *m = DeleteGroupResponse{} }
func (m *DeleteGroupResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteGroupResponse) ProtoMessage()    {}
func (*DeleteGroupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{29}
}
func (m *DeleteGroupResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteGroupResponse.Unmarshal(m, b)
}
func (m *DeleteGroupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteGroupResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteGroupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteGroupResponse.Merge(dst, src)
}
func (m *DeleteGroupResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteGroupResponse.Size(m)
}
func (m *DeleteGroupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteGroupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteGroupResponse proto.InternalMessageInfo

type LeaveGroupRequest struct {
	User                 *User    `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	GroupId              uint32   `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LeaveGroupRequest) Reset()         { *m = LeaveGroupRequest{} }
func (m *LeaveGroupRequest) String() string { return proto.CompactTextString(m) }
func (*LeaveGroupRequest) ProtoMessage()    {}
func (*LeaveGroupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{30}
}
func (m *LeaveGroupRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LeaveGroupRequest.Unmarshal(m, b)
}
func (m *LeaveGroupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LeaveGroupRequest.Marshal(b, m, deterministic)
}
func (dst *LeaveGroupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LeaveGroupRequest.Merge(dst, src)
}
func (m *LeaveGroupRequest) XXX_Size() int {
	return xxx_messageInfo_LeaveGroupRequest.Size(m)
}
func (m *LeaveGroupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LeaveGroupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LeaveGroupRequest proto.InternalMessageInfo

func (m *LeaveGroupRequest) GetUser() *User {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *LeaveGroupRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type LeaveGroupResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LeaveGroupResponse) Reset()         { *m = LeaveGroupResponse{} }
func (m *LeaveGroupResponse) String() string { return proto.CompactTextString(m) }
func (*LeaveGroupResponse) ProtoMessage()    {}
func (*LeaveGroupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{31}
}
func (m *LeaveGroupResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LeaveGroupResponse.Unmarshal(m, b)
}
func (m *LeaveGroupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LeaveGroupResponse.Marshal(b, m, deterministic)
}
func (dst *LeaveGroupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LeaveGroupResponse.Merge(dst, src)
}
func (m *LeaveGroupResponse) XXX_Size() int {
	return xxx_messageInfo_LeaveGroupResponse.Size(m)
}
func (m *LeaveGroupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LeaveGroupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LeaveGroupResponse proto.InternalMessageInfo

type GetGroupInfoRequest struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupInfoRequest) Reset()         { *m = GetGroupInfoRequest{} }
func (m *GetGroupInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupInfoRequest) ProtoMessage()    {}
func (*GetGroupInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{32}
}
func (m *GetGroupInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupInfoRequest.Unmarshal(m, b)
}
func (m *GetGroupInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetGroupInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupInfoRequest.Merge(dst, src)
}
func (m *GetGroupInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupInfoRequest.Size(m)
}
func (m *GetGroupInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupInfoRequest proto.InternalMessageInfo

func (m *GetGroupInfoRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetGroupInfoResponse struct {
	Info                 *Group   `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupInfoResponse) Reset()         { *m = GetGroupInfoResponse{} }
func (m *GetGroupInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupInfoResponse) ProtoMessage()    {}
func (*GetGroupInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{33}
}
func (m *GetGroupInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupInfoResponse.Unmarshal(m, b)
}
func (m *GetGroupInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetGroupInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupInfoResponse.Merge(dst, src)
}
func (m *GetGroupInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupInfoResponse.Size(m)
}
func (m *GetGroupInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupInfoResponse proto.InternalMessageInfo

func (m *GetGroupInfoResponse) GetInfo() *Group {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetUserOwnedGroupListRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TemplateId           uint32   `protobuf:"varint,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserOwnedGroupListRequest) Reset()         { *m = GetUserOwnedGroupListRequest{} }
func (m *GetUserOwnedGroupListRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserOwnedGroupListRequest) ProtoMessage()    {}
func (*GetUserOwnedGroupListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{34}
}
func (m *GetUserOwnedGroupListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOwnedGroupListRequest.Unmarshal(m, b)
}
func (m *GetUserOwnedGroupListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOwnedGroupListRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserOwnedGroupListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOwnedGroupListRequest.Merge(dst, src)
}
func (m *GetUserOwnedGroupListRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserOwnedGroupListRequest.Size(m)
}
func (m *GetUserOwnedGroupListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOwnedGroupListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOwnedGroupListRequest proto.InternalMessageInfo

func (m *GetUserOwnedGroupListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserOwnedGroupListRequest) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

type GetUserOwnedGroupListResponse struct {
	List                 []*Group `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserOwnedGroupListResponse) Reset()         { *m = GetUserOwnedGroupListResponse{} }
func (m *GetUserOwnedGroupListResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserOwnedGroupListResponse) ProtoMessage()    {}
func (*GetUserOwnedGroupListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{35}
}
func (m *GetUserOwnedGroupListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOwnedGroupListResponse.Unmarshal(m, b)
}
func (m *GetUserOwnedGroupListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOwnedGroupListResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserOwnedGroupListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOwnedGroupListResponse.Merge(dst, src)
}
func (m *GetUserOwnedGroupListResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserOwnedGroupListResponse.Size(m)
}
func (m *GetUserOwnedGroupListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOwnedGroupListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOwnedGroupListResponse proto.InternalMessageInfo

func (m *GetUserOwnedGroupListResponse) GetList() []*Group {
	if m != nil {
		return m.List
	}
	return nil
}

type GetUserJoinedGroupListRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserJoinedGroupListRequest) Reset()         { *m = GetUserJoinedGroupListRequest{} }
func (m *GetUserJoinedGroupListRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserJoinedGroupListRequest) ProtoMessage()    {}
func (*GetUserJoinedGroupListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{36}
}
func (m *GetUserJoinedGroupListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserJoinedGroupListRequest.Unmarshal(m, b)
}
func (m *GetUserJoinedGroupListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserJoinedGroupListRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserJoinedGroupListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserJoinedGroupListRequest.Merge(dst, src)
}
func (m *GetUserJoinedGroupListRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserJoinedGroupListRequest.Size(m)
}
func (m *GetUserJoinedGroupListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserJoinedGroupListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserJoinedGroupListRequest proto.InternalMessageInfo

func (m *GetUserJoinedGroupListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserJoinedGroupListResponse struct {
	List                 []*Group `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserJoinedGroupListResponse) Reset()         { *m = GetUserJoinedGroupListResponse{} }
func (m *GetUserJoinedGroupListResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserJoinedGroupListResponse) ProtoMessage()    {}
func (*GetUserJoinedGroupListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{37}
}
func (m *GetUserJoinedGroupListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserJoinedGroupListResponse.Unmarshal(m, b)
}
func (m *GetUserJoinedGroupListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserJoinedGroupListResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserJoinedGroupListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserJoinedGroupListResponse.Merge(dst, src)
}
func (m *GetUserJoinedGroupListResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserJoinedGroupListResponse.Size(m)
}
func (m *GetUserJoinedGroupListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserJoinedGroupListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserJoinedGroupListResponse proto.InternalMessageInfo

func (m *GetUserJoinedGroupListResponse) GetList() []*Group {
	if m != nil {
		return m.List
	}
	return nil
}

type GetGroupMemberRequest struct {
	GroupId              uint32          `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	MemberId             uint32          `protobuf:"varint,2,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	MemberType           GroupMemberType `protobuf:"varint,3,opt,name=member_type,json=memberType,proto3,enum=aigc_group.GroupMemberType" json:"member_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetGroupMemberRequest) Reset()         { *m = GetGroupMemberRequest{} }
func (m *GetGroupMemberRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupMemberRequest) ProtoMessage()    {}
func (*GetGroupMemberRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{38}
}
func (m *GetGroupMemberRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupMemberRequest.Unmarshal(m, b)
}
func (m *GetGroupMemberRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupMemberRequest.Marshal(b, m, deterministic)
}
func (dst *GetGroupMemberRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupMemberRequest.Merge(dst, src)
}
func (m *GetGroupMemberRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupMemberRequest.Size(m)
}
func (m *GetGroupMemberRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupMemberRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupMemberRequest proto.InternalMessageInfo

func (m *GetGroupMemberRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GetGroupMemberRequest) GetMemberId() uint32 {
	if m != nil {
		return m.MemberId
	}
	return 0
}

func (m *GetGroupMemberRequest) GetMemberType() GroupMemberType {
	if m != nil {
		return m.MemberType
	}
	return GroupMemberType_GROUP_MEMBER_TYPE_UNSPECIFIED
}

type GetGroupMemberResponse struct {
	Member               *GroupMember `protobuf:"bytes,1,opt,name=member,proto3" json:"member,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGroupMemberResponse) Reset()         { *m = GetGroupMemberResponse{} }
func (m *GetGroupMemberResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupMemberResponse) ProtoMessage()    {}
func (*GetGroupMemberResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{39}
}
func (m *GetGroupMemberResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupMemberResponse.Unmarshal(m, b)
}
func (m *GetGroupMemberResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupMemberResponse.Marshal(b, m, deterministic)
}
func (dst *GetGroupMemberResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupMemberResponse.Merge(dst, src)
}
func (m *GetGroupMemberResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupMemberResponse.Size(m)
}
func (m *GetGroupMemberResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupMemberResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupMemberResponse proto.InternalMessageInfo

func (m *GetGroupMemberResponse) GetMember() *GroupMember {
	if m != nil {
		return m.Member
	}
	return nil
}

type BatchGetGroupMemberRequest struct {
	GroupId              uint32                               `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Members              []*BatchGetGroupMemberRequest_Member `protobuf:"bytes,2,rep,name=members,proto3" json:"members,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *BatchGetGroupMemberRequest) Reset()         { *m = BatchGetGroupMemberRequest{} }
func (m *BatchGetGroupMemberRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetGroupMemberRequest) ProtoMessage()    {}
func (*BatchGetGroupMemberRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{40}
}
func (m *BatchGetGroupMemberRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGroupMemberRequest.Unmarshal(m, b)
}
func (m *BatchGetGroupMemberRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGroupMemberRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetGroupMemberRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGroupMemberRequest.Merge(dst, src)
}
func (m *BatchGetGroupMemberRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetGroupMemberRequest.Size(m)
}
func (m *BatchGetGroupMemberRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGroupMemberRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGroupMemberRequest proto.InternalMessageInfo

func (m *BatchGetGroupMemberRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *BatchGetGroupMemberRequest) GetMembers() []*BatchGetGroupMemberRequest_Member {
	if m != nil {
		return m.Members
	}
	return nil
}

type BatchGetGroupMemberRequest_Member struct {
	Id                   uint32          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                 GroupMemberType `protobuf:"varint,2,opt,name=type,proto3,enum=aigc_group.GroupMemberType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchGetGroupMemberRequest_Member) Reset()         { *m = BatchGetGroupMemberRequest_Member{} }
func (m *BatchGetGroupMemberRequest_Member) String() string { return proto.CompactTextString(m) }
func (*BatchGetGroupMemberRequest_Member) ProtoMessage()    {}
func (*BatchGetGroupMemberRequest_Member) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{40, 0}
}
func (m *BatchGetGroupMemberRequest_Member) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGroupMemberRequest_Member.Unmarshal(m, b)
}
func (m *BatchGetGroupMemberRequest_Member) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGroupMemberRequest_Member.Marshal(b, m, deterministic)
}
func (dst *BatchGetGroupMemberRequest_Member) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGroupMemberRequest_Member.Merge(dst, src)
}
func (m *BatchGetGroupMemberRequest_Member) XXX_Size() int {
	return xxx_messageInfo_BatchGetGroupMemberRequest_Member.Size(m)
}
func (m *BatchGetGroupMemberRequest_Member) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGroupMemberRequest_Member.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGroupMemberRequest_Member proto.InternalMessageInfo

func (m *BatchGetGroupMemberRequest_Member) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BatchGetGroupMemberRequest_Member) GetType() GroupMemberType {
	if m != nil {
		return m.Type
	}
	return GroupMemberType_GROUP_MEMBER_TYPE_UNSPECIFIED
}

type BatchGetGroupMemberResponse struct {
	Members              []*GroupMember `protobuf:"bytes,1,rep,name=members,proto3" json:"members,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchGetGroupMemberResponse) Reset()         { *m = BatchGetGroupMemberResponse{} }
func (m *BatchGetGroupMemberResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetGroupMemberResponse) ProtoMessage()    {}
func (*BatchGetGroupMemberResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{41}
}
func (m *BatchGetGroupMemberResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGroupMemberResponse.Unmarshal(m, b)
}
func (m *BatchGetGroupMemberResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGroupMemberResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetGroupMemberResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGroupMemberResponse.Merge(dst, src)
}
func (m *BatchGetGroupMemberResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetGroupMemberResponse.Size(m)
}
func (m *BatchGetGroupMemberResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGroupMemberResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGroupMemberResponse proto.InternalMessageInfo

func (m *BatchGetGroupMemberResponse) GetMembers() []*GroupMember {
	if m != nil {
		return m.Members
	}
	return nil
}

type GetGroupMemberListRequest struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupMemberListRequest) Reset()         { *m = GetGroupMemberListRequest{} }
func (m *GetGroupMemberListRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupMemberListRequest) ProtoMessage()    {}
func (*GetGroupMemberListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{42}
}
func (m *GetGroupMemberListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupMemberListRequest.Unmarshal(m, b)
}
func (m *GetGroupMemberListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupMemberListRequest.Marshal(b, m, deterministic)
}
func (dst *GetGroupMemberListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupMemberListRequest.Merge(dst, src)
}
func (m *GetGroupMemberListRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupMemberListRequest.Size(m)
}
func (m *GetGroupMemberListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupMemberListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupMemberListRequest proto.InternalMessageInfo

func (m *GetGroupMemberListRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetGroupMemberListResponse struct {
	List                 []*GroupMember `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetGroupMemberListResponse) Reset()         { *m = GetGroupMemberListResponse{} }
func (m *GetGroupMemberListResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupMemberListResponse) ProtoMessage()    {}
func (*GetGroupMemberListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{43}
}
func (m *GetGroupMemberListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupMemberListResponse.Unmarshal(m, b)
}
func (m *GetGroupMemberListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupMemberListResponse.Marshal(b, m, deterministic)
}
func (dst *GetGroupMemberListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupMemberListResponse.Merge(dst, src)
}
func (m *GetGroupMemberListResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupMemberListResponse.Size(m)
}
func (m *GetGroupMemberListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupMemberListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupMemberListResponse proto.InternalMessageInfo

func (m *GetGroupMemberListResponse) GetList() []*GroupMember {
	if m != nil {
		return m.List
	}
	return nil
}

type BatchGetGroupMemberListRequest struct {
	GroupIds             []uint32 `protobuf:"varint,1,rep,packed,name=group_ids,json=groupIds,proto3" json:"group_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetGroupMemberListRequest) Reset()         { *m = BatchGetGroupMemberListRequest{} }
func (m *BatchGetGroupMemberListRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetGroupMemberListRequest) ProtoMessage()    {}
func (*BatchGetGroupMemberListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{44}
}
func (m *BatchGetGroupMemberListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGroupMemberListRequest.Unmarshal(m, b)
}
func (m *BatchGetGroupMemberListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGroupMemberListRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetGroupMemberListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGroupMemberListRequest.Merge(dst, src)
}
func (m *BatchGetGroupMemberListRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetGroupMemberListRequest.Size(m)
}
func (m *BatchGetGroupMemberListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGroupMemberListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGroupMemberListRequest proto.InternalMessageInfo

func (m *BatchGetGroupMemberListRequest) GetGroupIds() []uint32 {
	if m != nil {
		return m.GroupIds
	}
	return nil
}

type GroupMemberList struct {
	Members              []*GroupMember `protobuf:"bytes,1,rep,name=members,proto3" json:"members,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GroupMemberList) Reset()         { *m = GroupMemberList{} }
func (m *GroupMemberList) String() string { return proto.CompactTextString(m) }
func (*GroupMemberList) ProtoMessage()    {}
func (*GroupMemberList) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{45}
}
func (m *GroupMemberList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupMemberList.Unmarshal(m, b)
}
func (m *GroupMemberList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupMemberList.Marshal(b, m, deterministic)
}
func (dst *GroupMemberList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupMemberList.Merge(dst, src)
}
func (m *GroupMemberList) XXX_Size() int {
	return xxx_messageInfo_GroupMemberList.Size(m)
}
func (m *GroupMemberList) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupMemberList.DiscardUnknown(m)
}

var xxx_messageInfo_GroupMemberList proto.InternalMessageInfo

func (m *GroupMemberList) GetMembers() []*GroupMember {
	if m != nil {
		return m.Members
	}
	return nil
}

type BatchGetGroupMemberListResponse struct {
	GroupMembersMap      map[uint32]*GroupMemberList `protobuf:"bytes,1,rep,name=group_members_map,json=groupMembersMap,proto3" json:"group_members_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *BatchGetGroupMemberListResponse) Reset()         { *m = BatchGetGroupMemberListResponse{} }
func (m *BatchGetGroupMemberListResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetGroupMemberListResponse) ProtoMessage()    {}
func (*BatchGetGroupMemberListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{46}
}
func (m *BatchGetGroupMemberListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGroupMemberListResponse.Unmarshal(m, b)
}
func (m *BatchGetGroupMemberListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGroupMemberListResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetGroupMemberListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGroupMemberListResponse.Merge(dst, src)
}
func (m *BatchGetGroupMemberListResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetGroupMemberListResponse.Size(m)
}
func (m *BatchGetGroupMemberListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGroupMemberListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGroupMemberListResponse proto.InternalMessageInfo

func (m *BatchGetGroupMemberListResponse) GetGroupMembersMap() map[uint32]*GroupMemberList {
	if m != nil {
		return m.GroupMembersMap
	}
	return nil
}

type HotBannerInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TemplateId           uint32   `protobuf:"varint,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	TopTitle             string   `protobuf:"bytes,3,opt,name=top_title,json=topTitle,proto3" json:"top_title,omitempty"`
	SubTitle             string   `protobuf:"bytes,4,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	BarColor             string   `protobuf:"bytes,5,opt,name=bar_color,json=barColor,proto3" json:"bar_color,omitempty"`
	ButtonColor          string   `protobuf:"bytes,6,opt,name=button_color,json=buttonColor,proto3" json:"button_color,omitempty"`
	Sort                 uint32   `protobuf:"varint,7,opt,name=sort,proto3" json:"sort,omitempty"`
	ButtonTitle          string   `protobuf:"bytes,8,opt,name=button_title,json=buttonTitle,proto3" json:"button_title,omitempty"`
	BannerBackgroundImg  string   `protobuf:"bytes,9,opt,name=banner_background_img,json=bannerBackgroundImg,proto3" json:"banner_background_img,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HotBannerInfo) Reset()         { *m = HotBannerInfo{} }
func (m *HotBannerInfo) String() string { return proto.CompactTextString(m) }
func (*HotBannerInfo) ProtoMessage()    {}
func (*HotBannerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{47}
}
func (m *HotBannerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HotBannerInfo.Unmarshal(m, b)
}
func (m *HotBannerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HotBannerInfo.Marshal(b, m, deterministic)
}
func (dst *HotBannerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HotBannerInfo.Merge(dst, src)
}
func (m *HotBannerInfo) XXX_Size() int {
	return xxx_messageInfo_HotBannerInfo.Size(m)
}
func (m *HotBannerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HotBannerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HotBannerInfo proto.InternalMessageInfo

func (m *HotBannerInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *HotBannerInfo) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *HotBannerInfo) GetTopTitle() string {
	if m != nil {
		return m.TopTitle
	}
	return ""
}

func (m *HotBannerInfo) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *HotBannerInfo) GetBarColor() string {
	if m != nil {
		return m.BarColor
	}
	return ""
}

func (m *HotBannerInfo) GetButtonColor() string {
	if m != nil {
		return m.ButtonColor
	}
	return ""
}

func (m *HotBannerInfo) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *HotBannerInfo) GetButtonTitle() string {
	if m != nil {
		return m.ButtonTitle
	}
	return ""
}

func (m *HotBannerInfo) GetBannerBackgroundImg() string {
	if m != nil {
		return m.BannerBackgroundImg
	}
	return ""
}

type CreateHotBannerRequest struct {
	HotBannerInfo        *CreateHotBannerRequest_HotBanner `protobuf:"bytes,1,opt,name=hot_banner_info,json=hotBannerInfo,proto3" json:"hot_banner_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *CreateHotBannerRequest) Reset()         { *m = CreateHotBannerRequest{} }
func (m *CreateHotBannerRequest) String() string { return proto.CompactTextString(m) }
func (*CreateHotBannerRequest) ProtoMessage()    {}
func (*CreateHotBannerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{48}
}
func (m *CreateHotBannerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateHotBannerRequest.Unmarshal(m, b)
}
func (m *CreateHotBannerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateHotBannerRequest.Marshal(b, m, deterministic)
}
func (dst *CreateHotBannerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateHotBannerRequest.Merge(dst, src)
}
func (m *CreateHotBannerRequest) XXX_Size() int {
	return xxx_messageInfo_CreateHotBannerRequest.Size(m)
}
func (m *CreateHotBannerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateHotBannerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateHotBannerRequest proto.InternalMessageInfo

func (m *CreateHotBannerRequest) GetHotBannerInfo() *CreateHotBannerRequest_HotBanner {
	if m != nil {
		return m.HotBannerInfo
	}
	return nil
}

type CreateHotBannerRequest_HotBanner struct {
	TemplateId           uint32   `protobuf:"varint,1,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	TopTitle             string   `protobuf:"bytes,2,opt,name=top_title,json=topTitle,proto3" json:"top_title,omitempty"`
	SubTitle             string   `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	BarColor             string   `protobuf:"bytes,4,opt,name=bar_color,json=barColor,proto3" json:"bar_color,omitempty"`
	ButtonColor          string   `protobuf:"bytes,5,opt,name=button_color,json=buttonColor,proto3" json:"button_color,omitempty"`
	ButtonTitle          string   `protobuf:"bytes,6,opt,name=button_title,json=buttonTitle,proto3" json:"button_title,omitempty"`
	BannerBackgroundImg  string   `protobuf:"bytes,7,opt,name=banner_background_img,json=bannerBackgroundImg,proto3" json:"banner_background_img,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateHotBannerRequest_HotBanner) Reset()         { *m = CreateHotBannerRequest_HotBanner{} }
func (m *CreateHotBannerRequest_HotBanner) String() string { return proto.CompactTextString(m) }
func (*CreateHotBannerRequest_HotBanner) ProtoMessage()    {}
func (*CreateHotBannerRequest_HotBanner) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{48, 0}
}
func (m *CreateHotBannerRequest_HotBanner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateHotBannerRequest_HotBanner.Unmarshal(m, b)
}
func (m *CreateHotBannerRequest_HotBanner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateHotBannerRequest_HotBanner.Marshal(b, m, deterministic)
}
func (dst *CreateHotBannerRequest_HotBanner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateHotBannerRequest_HotBanner.Merge(dst, src)
}
func (m *CreateHotBannerRequest_HotBanner) XXX_Size() int {
	return xxx_messageInfo_CreateHotBannerRequest_HotBanner.Size(m)
}
func (m *CreateHotBannerRequest_HotBanner) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateHotBannerRequest_HotBanner.DiscardUnknown(m)
}

var xxx_messageInfo_CreateHotBannerRequest_HotBanner proto.InternalMessageInfo

func (m *CreateHotBannerRequest_HotBanner) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *CreateHotBannerRequest_HotBanner) GetTopTitle() string {
	if m != nil {
		return m.TopTitle
	}
	return ""
}

func (m *CreateHotBannerRequest_HotBanner) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *CreateHotBannerRequest_HotBanner) GetBarColor() string {
	if m != nil {
		return m.BarColor
	}
	return ""
}

func (m *CreateHotBannerRequest_HotBanner) GetButtonColor() string {
	if m != nil {
		return m.ButtonColor
	}
	return ""
}

func (m *CreateHotBannerRequest_HotBanner) GetButtonTitle() string {
	if m != nil {
		return m.ButtonTitle
	}
	return ""
}

func (m *CreateHotBannerRequest_HotBanner) GetBannerBackgroundImg() string {
	if m != nil {
		return m.BannerBackgroundImg
	}
	return ""
}

type CreateHotBannerResponse struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateHotBannerResponse) Reset()         { *m = CreateHotBannerResponse{} }
func (m *CreateHotBannerResponse) String() string { return proto.CompactTextString(m) }
func (*CreateHotBannerResponse) ProtoMessage()    {}
func (*CreateHotBannerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{49}
}
func (m *CreateHotBannerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateHotBannerResponse.Unmarshal(m, b)
}
func (m *CreateHotBannerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateHotBannerResponse.Marshal(b, m, deterministic)
}
func (dst *CreateHotBannerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateHotBannerResponse.Merge(dst, src)
}
func (m *CreateHotBannerResponse) XXX_Size() int {
	return xxx_messageInfo_CreateHotBannerResponse.Size(m)
}
func (m *CreateHotBannerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateHotBannerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateHotBannerResponse proto.InternalMessageInfo

func (m *CreateHotBannerResponse) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type UpdateHotBannerRequest struct {
	HotBannerInfo        *UpdateHotBannerRequest_HotBanner `protobuf:"bytes,1,opt,name=hot_banner_info,json=hotBannerInfo,proto3" json:"hot_banner_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *UpdateHotBannerRequest) Reset()         { *m = UpdateHotBannerRequest{} }
func (m *UpdateHotBannerRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateHotBannerRequest) ProtoMessage()    {}
func (*UpdateHotBannerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{50}
}
func (m *UpdateHotBannerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateHotBannerRequest.Unmarshal(m, b)
}
func (m *UpdateHotBannerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateHotBannerRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateHotBannerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateHotBannerRequest.Merge(dst, src)
}
func (m *UpdateHotBannerRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateHotBannerRequest.Size(m)
}
func (m *UpdateHotBannerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateHotBannerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateHotBannerRequest proto.InternalMessageInfo

func (m *UpdateHotBannerRequest) GetHotBannerInfo() *UpdateHotBannerRequest_HotBanner {
	if m != nil {
		return m.HotBannerInfo
	}
	return nil
}

type UpdateHotBannerRequest_HotBanner struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TemplateId           uint32   `protobuf:"varint,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	TopTitle             string   `protobuf:"bytes,3,opt,name=top_title,json=topTitle,proto3" json:"top_title,omitempty"`
	SubTitle             string   `protobuf:"bytes,4,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	BarColor             string   `protobuf:"bytes,5,opt,name=bar_color,json=barColor,proto3" json:"bar_color,omitempty"`
	ButtonColor          string   `protobuf:"bytes,6,opt,name=button_color,json=buttonColor,proto3" json:"button_color,omitempty"`
	ButtonTitle          string   `protobuf:"bytes,7,opt,name=button_title,json=buttonTitle,proto3" json:"button_title,omitempty"`
	BannerBackgroundImg  string   `protobuf:"bytes,8,opt,name=banner_background_img,json=bannerBackgroundImg,proto3" json:"banner_background_img,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateHotBannerRequest_HotBanner) Reset()         { *m = UpdateHotBannerRequest_HotBanner{} }
func (m *UpdateHotBannerRequest_HotBanner) String() string { return proto.CompactTextString(m) }
func (*UpdateHotBannerRequest_HotBanner) ProtoMessage()    {}
func (*UpdateHotBannerRequest_HotBanner) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{50, 0}
}
func (m *UpdateHotBannerRequest_HotBanner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateHotBannerRequest_HotBanner.Unmarshal(m, b)
}
func (m *UpdateHotBannerRequest_HotBanner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateHotBannerRequest_HotBanner.Marshal(b, m, deterministic)
}
func (dst *UpdateHotBannerRequest_HotBanner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateHotBannerRequest_HotBanner.Merge(dst, src)
}
func (m *UpdateHotBannerRequest_HotBanner) XXX_Size() int {
	return xxx_messageInfo_UpdateHotBannerRequest_HotBanner.Size(m)
}
func (m *UpdateHotBannerRequest_HotBanner) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateHotBannerRequest_HotBanner.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateHotBannerRequest_HotBanner proto.InternalMessageInfo

func (m *UpdateHotBannerRequest_HotBanner) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpdateHotBannerRequest_HotBanner) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *UpdateHotBannerRequest_HotBanner) GetTopTitle() string {
	if m != nil {
		return m.TopTitle
	}
	return ""
}

func (m *UpdateHotBannerRequest_HotBanner) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *UpdateHotBannerRequest_HotBanner) GetBarColor() string {
	if m != nil {
		return m.BarColor
	}
	return ""
}

func (m *UpdateHotBannerRequest_HotBanner) GetButtonColor() string {
	if m != nil {
		return m.ButtonColor
	}
	return ""
}

func (m *UpdateHotBannerRequest_HotBanner) GetButtonTitle() string {
	if m != nil {
		return m.ButtonTitle
	}
	return ""
}

func (m *UpdateHotBannerRequest_HotBanner) GetBannerBackgroundImg() string {
	if m != nil {
		return m.BannerBackgroundImg
	}
	return ""
}

type UpdateHotBannerResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateHotBannerResponse) Reset()         { *m = UpdateHotBannerResponse{} }
func (m *UpdateHotBannerResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateHotBannerResponse) ProtoMessage()    {}
func (*UpdateHotBannerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{51}
}
func (m *UpdateHotBannerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateHotBannerResponse.Unmarshal(m, b)
}
func (m *UpdateHotBannerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateHotBannerResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateHotBannerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateHotBannerResponse.Merge(dst, src)
}
func (m *UpdateHotBannerResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateHotBannerResponse.Size(m)
}
func (m *UpdateHotBannerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateHotBannerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateHotBannerResponse proto.InternalMessageInfo

type DeleteHotBannerRequest struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteHotBannerRequest) Reset()         { *m = DeleteHotBannerRequest{} }
func (m *DeleteHotBannerRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteHotBannerRequest) ProtoMessage()    {}
func (*DeleteHotBannerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{52}
}
func (m *DeleteHotBannerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteHotBannerRequest.Unmarshal(m, b)
}
func (m *DeleteHotBannerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteHotBannerRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteHotBannerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteHotBannerRequest.Merge(dst, src)
}
func (m *DeleteHotBannerRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteHotBannerRequest.Size(m)
}
func (m *DeleteHotBannerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteHotBannerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteHotBannerRequest proto.InternalMessageInfo

func (m *DeleteHotBannerRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DeleteHotBannerResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteHotBannerResponse) Reset()         { *m = DeleteHotBannerResponse{} }
func (m *DeleteHotBannerResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteHotBannerResponse) ProtoMessage()    {}
func (*DeleteHotBannerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{53}
}
func (m *DeleteHotBannerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteHotBannerResponse.Unmarshal(m, b)
}
func (m *DeleteHotBannerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteHotBannerResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteHotBannerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteHotBannerResponse.Merge(dst, src)
}
func (m *DeleteHotBannerResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteHotBannerResponse.Size(m)
}
func (m *DeleteHotBannerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteHotBannerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteHotBannerResponse proto.InternalMessageInfo

type GetAllHotBannerRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllHotBannerRequest) Reset()         { *m = GetAllHotBannerRequest{} }
func (m *GetAllHotBannerRequest) String() string { return proto.CompactTextString(m) }
func (*GetAllHotBannerRequest) ProtoMessage()    {}
func (*GetAllHotBannerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{54}
}
func (m *GetAllHotBannerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllHotBannerRequest.Unmarshal(m, b)
}
func (m *GetAllHotBannerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllHotBannerRequest.Marshal(b, m, deterministic)
}
func (dst *GetAllHotBannerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllHotBannerRequest.Merge(dst, src)
}
func (m *GetAllHotBannerRequest) XXX_Size() int {
	return xxx_messageInfo_GetAllHotBannerRequest.Size(m)
}
func (m *GetAllHotBannerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllHotBannerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllHotBannerRequest proto.InternalMessageInfo

type GetAllHotBannerResponse struct {
	List                 []*HotBannerInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetAllHotBannerResponse) Reset()         { *m = GetAllHotBannerResponse{} }
func (m *GetAllHotBannerResponse) String() string { return proto.CompactTextString(m) }
func (*GetAllHotBannerResponse) ProtoMessage()    {}
func (*GetAllHotBannerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{55}
}
func (m *GetAllHotBannerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllHotBannerResponse.Unmarshal(m, b)
}
func (m *GetAllHotBannerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllHotBannerResponse.Marshal(b, m, deterministic)
}
func (dst *GetAllHotBannerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllHotBannerResponse.Merge(dst, src)
}
func (m *GetAllHotBannerResponse) XXX_Size() int {
	return xxx_messageInfo_GetAllHotBannerResponse.Size(m)
}
func (m *GetAllHotBannerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllHotBannerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllHotBannerResponse proto.InternalMessageInfo

func (m *GetAllHotBannerResponse) GetList() []*HotBannerInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type GetHotBannerByIdRequest struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHotBannerByIdRequest) Reset()         { *m = GetHotBannerByIdRequest{} }
func (m *GetHotBannerByIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetHotBannerByIdRequest) ProtoMessage()    {}
func (*GetHotBannerByIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{56}
}
func (m *GetHotBannerByIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHotBannerByIdRequest.Unmarshal(m, b)
}
func (m *GetHotBannerByIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHotBannerByIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetHotBannerByIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHotBannerByIdRequest.Merge(dst, src)
}
func (m *GetHotBannerByIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetHotBannerByIdRequest.Size(m)
}
func (m *GetHotBannerByIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHotBannerByIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetHotBannerByIdRequest proto.InternalMessageInfo

func (m *GetHotBannerByIdRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type GetHotBannerByIdResponse struct {
	Info                 *HotBannerInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetHotBannerByIdResponse) Reset()         { *m = GetHotBannerByIdResponse{} }
func (m *GetHotBannerByIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetHotBannerByIdResponse) ProtoMessage()    {}
func (*GetHotBannerByIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{57}
}
func (m *GetHotBannerByIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHotBannerByIdResponse.Unmarshal(m, b)
}
func (m *GetHotBannerByIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHotBannerByIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetHotBannerByIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHotBannerByIdResponse.Merge(dst, src)
}
func (m *GetHotBannerByIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetHotBannerByIdResponse.Size(m)
}
func (m *GetHotBannerByIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHotBannerByIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetHotBannerByIdResponse proto.InternalMessageInfo

func (m *GetHotBannerByIdResponse) GetInfo() *HotBannerInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type ResortBannerRequest struct {
	Ids                  []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResortBannerRequest) Reset()         { *m = ResortBannerRequest{} }
func (m *ResortBannerRequest) String() string { return proto.CompactTextString(m) }
func (*ResortBannerRequest) ProtoMessage()    {}
func (*ResortBannerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{58}
}
func (m *ResortBannerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResortBannerRequest.Unmarshal(m, b)
}
func (m *ResortBannerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResortBannerRequest.Marshal(b, m, deterministic)
}
func (dst *ResortBannerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResortBannerRequest.Merge(dst, src)
}
func (m *ResortBannerRequest) XXX_Size() int {
	return xxx_messageInfo_ResortBannerRequest.Size(m)
}
func (m *ResortBannerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ResortBannerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ResortBannerRequest proto.InternalMessageInfo

func (m *ResortBannerRequest) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type ResortBannerResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResortBannerResponse) Reset()         { *m = ResortBannerResponse{} }
func (m *ResortBannerResponse) String() string { return proto.CompactTextString(m) }
func (*ResortBannerResponse) ProtoMessage()    {}
func (*ResortBannerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{59}
}
func (m *ResortBannerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResortBannerResponse.Unmarshal(m, b)
}
func (m *ResortBannerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResortBannerResponse.Marshal(b, m, deterministic)
}
func (dst *ResortBannerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResortBannerResponse.Merge(dst, src)
}
func (m *ResortBannerResponse) XXX_Size() int {
	return xxx_messageInfo_ResortBannerResponse.Size(m)
}
func (m *ResortBannerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ResortBannerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ResortBannerResponse proto.InternalMessageInfo

type JoinMatchRequest struct {
	Player               *JoinMatchRequest_Player `protobuf:"bytes,1,opt,name=player,proto3" json:"player,omitempty"`
	TemplateId           uint32                   `protobuf:"varint,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *JoinMatchRequest) Reset()         { *m = JoinMatchRequest{} }
func (m *JoinMatchRequest) String() string { return proto.CompactTextString(m) }
func (*JoinMatchRequest) ProtoMessage()    {}
func (*JoinMatchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{60}
}
func (m *JoinMatchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinMatchRequest.Unmarshal(m, b)
}
func (m *JoinMatchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinMatchRequest.Marshal(b, m, deterministic)
}
func (dst *JoinMatchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinMatchRequest.Merge(dst, src)
}
func (m *JoinMatchRequest) XXX_Size() int {
	return xxx_messageInfo_JoinMatchRequest.Size(m)
}
func (m *JoinMatchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinMatchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JoinMatchRequest proto.InternalMessageInfo

func (m *JoinMatchRequest) GetPlayer() *JoinMatchRequest_Player {
	if m != nil {
		return m.Player
	}
	return nil
}

func (m *JoinMatchRequest) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

type JoinMatchRequest_Player struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Sex                  int32    `protobuf:"varint,2,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinMatchRequest_Player) Reset()         { *m = JoinMatchRequest_Player{} }
func (m *JoinMatchRequest_Player) String() string { return proto.CompactTextString(m) }
func (*JoinMatchRequest_Player) ProtoMessage()    {}
func (*JoinMatchRequest_Player) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{60, 0}
}
func (m *JoinMatchRequest_Player) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinMatchRequest_Player.Unmarshal(m, b)
}
func (m *JoinMatchRequest_Player) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinMatchRequest_Player.Marshal(b, m, deterministic)
}
func (dst *JoinMatchRequest_Player) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinMatchRequest_Player.Merge(dst, src)
}
func (m *JoinMatchRequest_Player) XXX_Size() int {
	return xxx_messageInfo_JoinMatchRequest_Player.Size(m)
}
func (m *JoinMatchRequest_Player) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinMatchRequest_Player.DiscardUnknown(m)
}

var xxx_messageInfo_JoinMatchRequest_Player proto.InternalMessageInfo

func (m *JoinMatchRequest_Player) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *JoinMatchRequest_Player) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type JoinMatchResponse struct {
	TemplateId uint32 `protobuf:"varint,1,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// 匹配策略
	Strategy MatchStrategy `protobuf:"varint,2,opt,name=strategy,proto3,enum=aigc_group.MatchStrategy" json:"strategy,omitempty"`
	// 当strategy = MATCH_STRATEGY_GROUP，才会赋值
	GroupId              uint32   `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinMatchResponse) Reset()         { *m = JoinMatchResponse{} }
func (m *JoinMatchResponse) String() string { return proto.CompactTextString(m) }
func (*JoinMatchResponse) ProtoMessage()    {}
func (*JoinMatchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{61}
}
func (m *JoinMatchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinMatchResponse.Unmarshal(m, b)
}
func (m *JoinMatchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinMatchResponse.Marshal(b, m, deterministic)
}
func (dst *JoinMatchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinMatchResponse.Merge(dst, src)
}
func (m *JoinMatchResponse) XXX_Size() int {
	return xxx_messageInfo_JoinMatchResponse.Size(m)
}
func (m *JoinMatchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinMatchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JoinMatchResponse proto.InternalMessageInfo

func (m *JoinMatchResponse) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *JoinMatchResponse) GetStrategy() MatchStrategy {
	if m != nil {
		return m.Strategy
	}
	return MatchStrategy_MATCH_STRATEGY_UNSPECIFIED
}

func (m *JoinMatchResponse) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type CancelMatchRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelMatchRequest) Reset()         { *m = CancelMatchRequest{} }
func (m *CancelMatchRequest) String() string { return proto.CompactTextString(m) }
func (*CancelMatchRequest) ProtoMessage()    {}
func (*CancelMatchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{62}
}
func (m *CancelMatchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelMatchRequest.Unmarshal(m, b)
}
func (m *CancelMatchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelMatchRequest.Marshal(b, m, deterministic)
}
func (dst *CancelMatchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelMatchRequest.Merge(dst, src)
}
func (m *CancelMatchRequest) XXX_Size() int {
	return xxx_messageInfo_CancelMatchRequest.Size(m)
}
func (m *CancelMatchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelMatchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CancelMatchRequest proto.InternalMessageInfo

func (m *CancelMatchRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CancelMatchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelMatchResponse) Reset()         { *m = CancelMatchResponse{} }
func (m *CancelMatchResponse) String() string { return proto.CompactTextString(m) }
func (*CancelMatchResponse) ProtoMessage()    {}
func (*CancelMatchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{63}
}
func (m *CancelMatchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelMatchResponse.Unmarshal(m, b)
}
func (m *CancelMatchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelMatchResponse.Marshal(b, m, deterministic)
}
func (dst *CancelMatchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelMatchResponse.Merge(dst, src)
}
func (m *CancelMatchResponse) XXX_Size() int {
	return xxx_messageInfo_CancelMatchResponse.Size(m)
}
func (m *CancelMatchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelMatchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CancelMatchResponse proto.InternalMessageInfo

type ConfirmMatchRequest struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 匹配确认时下发的token
	Token                string   `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmMatchRequest) Reset()         { *m = ConfirmMatchRequest{} }
func (m *ConfirmMatchRequest) String() string { return proto.CompactTextString(m) }
func (*ConfirmMatchRequest) ProtoMessage()    {}
func (*ConfirmMatchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{64}
}
func (m *ConfirmMatchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmMatchRequest.Unmarshal(m, b)
}
func (m *ConfirmMatchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmMatchRequest.Marshal(b, m, deterministic)
}
func (dst *ConfirmMatchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmMatchRequest.Merge(dst, src)
}
func (m *ConfirmMatchRequest) XXX_Size() int {
	return xxx_messageInfo_ConfirmMatchRequest.Size(m)
}
func (m *ConfirmMatchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmMatchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmMatchRequest proto.InternalMessageInfo

func (m *ConfirmMatchRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConfirmMatchRequest) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type ConfirmMatchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmMatchResponse) Reset()         { *m = ConfirmMatchResponse{} }
func (m *ConfirmMatchResponse) String() string { return proto.CompactTextString(m) }
func (*ConfirmMatchResponse) ProtoMessage()    {}
func (*ConfirmMatchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{65}
}
func (m *ConfirmMatchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmMatchResponse.Unmarshal(m, b)
}
func (m *ConfirmMatchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmMatchResponse.Marshal(b, m, deterministic)
}
func (dst *ConfirmMatchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmMatchResponse.Merge(dst, src)
}
func (m *ConfirmMatchResponse) XXX_Size() int {
	return xxx_messageInfo_ConfirmMatchResponse.Size(m)
}
func (m *ConfirmMatchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmMatchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmMatchResponse proto.InternalMessageInfo

type BatchGetRandMatchPlayerRequest struct {
	TemplId              []uint32 `protobuf:"varint,1,rep,packed,name=templ_id,json=templId,proto3" json:"templ_id,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetRandMatchPlayerRequest) Reset()         { *m = BatchGetRandMatchPlayerRequest{} }
func (m *BatchGetRandMatchPlayerRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetRandMatchPlayerRequest) ProtoMessage()    {}
func (*BatchGetRandMatchPlayerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{66}
}
func (m *BatchGetRandMatchPlayerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRandMatchPlayerRequest.Unmarshal(m, b)
}
func (m *BatchGetRandMatchPlayerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRandMatchPlayerRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetRandMatchPlayerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRandMatchPlayerRequest.Merge(dst, src)
}
func (m *BatchGetRandMatchPlayerRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetRandMatchPlayerRequest.Size(m)
}
func (m *BatchGetRandMatchPlayerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRandMatchPlayerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRandMatchPlayerRequest proto.InternalMessageInfo

func (m *BatchGetRandMatchPlayerRequest) GetTemplId() []uint32 {
	if m != nil {
		return m.TemplId
	}
	return nil
}

func (m *BatchGetRandMatchPlayerRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type BatchGetRandMatchPlayerResponse struct {
	Players              map[uint32]*MatchPlayerList `protobuf:"bytes,1,rep,name=players,proto3" json:"players,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *BatchGetRandMatchPlayerResponse) Reset()         { *m = BatchGetRandMatchPlayerResponse{} }
func (m *BatchGetRandMatchPlayerResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetRandMatchPlayerResponse) ProtoMessage()    {}
func (*BatchGetRandMatchPlayerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{67}
}
func (m *BatchGetRandMatchPlayerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRandMatchPlayerResponse.Unmarshal(m, b)
}
func (m *BatchGetRandMatchPlayerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRandMatchPlayerResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetRandMatchPlayerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRandMatchPlayerResponse.Merge(dst, src)
}
func (m *BatchGetRandMatchPlayerResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetRandMatchPlayerResponse.Size(m)
}
func (m *BatchGetRandMatchPlayerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRandMatchPlayerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRandMatchPlayerResponse proto.InternalMessageInfo

func (m *BatchGetRandMatchPlayerResponse) GetPlayers() map[uint32]*MatchPlayerList {
	if m != nil {
		return m.Players
	}
	return nil
}

type UserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UserGender           int32    `protobuf:"varint,2,opt,name=user_gender,json=userGender,proto3" json:"user_gender,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{68}
}
func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (dst *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(dst, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserInfo) GetUserGender() int32 {
	if m != nil {
		return m.UserGender
	}
	return 0
}

type MultiGroupDefaultUserRoleSelectRequest struct {
	GroupId              uint32         `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	UserInfos            []*UserInfo    `protobuf:"bytes,2,rep,name=user_infos,json=userInfos,proto3" json:"user_infos,omitempty"`
	TemplateInfo         *GroupTemplate `protobuf:"bytes,3,opt,name=template_info,json=templateInfo,proto3" json:"template_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *MultiGroupDefaultUserRoleSelectRequest) Reset() {
	*m = MultiGroupDefaultUserRoleSelectRequest{}
}
func (m *MultiGroupDefaultUserRoleSelectRequest) String() string { return proto.CompactTextString(m) }
func (*MultiGroupDefaultUserRoleSelectRequest) ProtoMessage()    {}
func (*MultiGroupDefaultUserRoleSelectRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{69}
}
func (m *MultiGroupDefaultUserRoleSelectRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiGroupDefaultUserRoleSelectRequest.Unmarshal(m, b)
}
func (m *MultiGroupDefaultUserRoleSelectRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiGroupDefaultUserRoleSelectRequest.Marshal(b, m, deterministic)
}
func (dst *MultiGroupDefaultUserRoleSelectRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiGroupDefaultUserRoleSelectRequest.Merge(dst, src)
}
func (m *MultiGroupDefaultUserRoleSelectRequest) XXX_Size() int {
	return xxx_messageInfo_MultiGroupDefaultUserRoleSelectRequest.Size(m)
}
func (m *MultiGroupDefaultUserRoleSelectRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiGroupDefaultUserRoleSelectRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MultiGroupDefaultUserRoleSelectRequest proto.InternalMessageInfo

func (m *MultiGroupDefaultUserRoleSelectRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MultiGroupDefaultUserRoleSelectRequest) GetUserInfos() []*UserInfo {
	if m != nil {
		return m.UserInfos
	}
	return nil
}

func (m *MultiGroupDefaultUserRoleSelectRequest) GetTemplateInfo() *GroupTemplate {
	if m != nil {
		return m.TemplateInfo
	}
	return nil
}

type MultiGroupDefaultUserRoleSelectResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MultiGroupDefaultUserRoleSelectResponse) Reset() {
	*m = MultiGroupDefaultUserRoleSelectResponse{}
}
func (m *MultiGroupDefaultUserRoleSelectResponse) String() string { return proto.CompactTextString(m) }
func (*MultiGroupDefaultUserRoleSelectResponse) ProtoMessage()    {}
func (*MultiGroupDefaultUserRoleSelectResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{70}
}
func (m *MultiGroupDefaultUserRoleSelectResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiGroupDefaultUserRoleSelectResponse.Unmarshal(m, b)
}
func (m *MultiGroupDefaultUserRoleSelectResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiGroupDefaultUserRoleSelectResponse.Marshal(b, m, deterministic)
}
func (dst *MultiGroupDefaultUserRoleSelectResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiGroupDefaultUserRoleSelectResponse.Merge(dst, src)
}
func (m *MultiGroupDefaultUserRoleSelectResponse) XXX_Size() int {
	return xxx_messageInfo_MultiGroupDefaultUserRoleSelectResponse.Size(m)
}
func (m *MultiGroupDefaultUserRoleSelectResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiGroupDefaultUserRoleSelectResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MultiGroupDefaultUserRoleSelectResponse proto.InternalMessageInfo

type GetMultiGroupUserRoleInfoRequest struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMultiGroupUserRoleInfoRequest) Reset()         { *m = GetMultiGroupUserRoleInfoRequest{} }
func (m *GetMultiGroupUserRoleInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetMultiGroupUserRoleInfoRequest) ProtoMessage()    {}
func (*GetMultiGroupUserRoleInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{71}
}
func (m *GetMultiGroupUserRoleInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiGroupUserRoleInfoRequest.Unmarshal(m, b)
}
func (m *GetMultiGroupUserRoleInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiGroupUserRoleInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetMultiGroupUserRoleInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiGroupUserRoleInfoRequest.Merge(dst, src)
}
func (m *GetMultiGroupUserRoleInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetMultiGroupUserRoleInfoRequest.Size(m)
}
func (m *GetMultiGroupUserRoleInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiGroupUserRoleInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiGroupUserRoleInfoRequest proto.InternalMessageInfo

func (m *GetMultiGroupUserRoleInfoRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type UserSelectInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UserRoleId           uint32   `protobuf:"varint,2,opt,name=user_role_id,json=userRoleId,proto3" json:"user_role_id,omitempty"`
	UserRoleName         string   `protobuf:"bytes,3,opt,name=user_role_name,json=userRoleName,proto3" json:"user_role_name,omitempty"`
	Gender               int32    `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSelectInfo) Reset()         { *m = UserSelectInfo{} }
func (m *UserSelectInfo) String() string { return proto.CompactTextString(m) }
func (*UserSelectInfo) ProtoMessage()    {}
func (*UserSelectInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{72}
}
func (m *UserSelectInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSelectInfo.Unmarshal(m, b)
}
func (m *UserSelectInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSelectInfo.Marshal(b, m, deterministic)
}
func (dst *UserSelectInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSelectInfo.Merge(dst, src)
}
func (m *UserSelectInfo) XXX_Size() int {
	return xxx_messageInfo_UserSelectInfo.Size(m)
}
func (m *UserSelectInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSelectInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserSelectInfo proto.InternalMessageInfo

func (m *UserSelectInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserSelectInfo) GetUserRoleId() uint32 {
	if m != nil {
		return m.UserRoleId
	}
	return 0
}

func (m *UserSelectInfo) GetUserRoleName() string {
	if m != nil {
		return m.UserRoleName
	}
	return ""
}

func (m *UserSelectInfo) GetGender() int32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

type GetMultiGroupUserRoleInfoResponse struct {
	UserSelectRoleMap    map[uint32]*UserSelectInfo `protobuf:"bytes,1,rep,name=user_select_role_map,json=userSelectRoleMap,proto3" json:"user_select_role_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetMultiGroupUserRoleInfoResponse) Reset()         { *m = GetMultiGroupUserRoleInfoResponse{} }
func (m *GetMultiGroupUserRoleInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetMultiGroupUserRoleInfoResponse) ProtoMessage()    {}
func (*GetMultiGroupUserRoleInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{73}
}
func (m *GetMultiGroupUserRoleInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiGroupUserRoleInfoResponse.Unmarshal(m, b)
}
func (m *GetMultiGroupUserRoleInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiGroupUserRoleInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetMultiGroupUserRoleInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiGroupUserRoleInfoResponse.Merge(dst, src)
}
func (m *GetMultiGroupUserRoleInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetMultiGroupUserRoleInfoResponse.Size(m)
}
func (m *GetMultiGroupUserRoleInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiGroupUserRoleInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiGroupUserRoleInfoResponse proto.InternalMessageInfo

func (m *GetMultiGroupUserRoleInfoResponse) GetUserSelectRoleMap() map[uint32]*UserSelectInfo {
	if m != nil {
		return m.UserSelectRoleMap
	}
	return nil
}

type GetTemplateByGroupIdRequest struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTemplateByGroupIdRequest) Reset()         { *m = GetTemplateByGroupIdRequest{} }
func (m *GetTemplateByGroupIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetTemplateByGroupIdRequest) ProtoMessage()    {}
func (*GetTemplateByGroupIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{74}
}
func (m *GetTemplateByGroupIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTemplateByGroupIdRequest.Unmarshal(m, b)
}
func (m *GetTemplateByGroupIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTemplateByGroupIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetTemplateByGroupIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTemplateByGroupIdRequest.Merge(dst, src)
}
func (m *GetTemplateByGroupIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetTemplateByGroupIdRequest.Size(m)
}
func (m *GetTemplateByGroupIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTemplateByGroupIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTemplateByGroupIdRequest proto.InternalMessageInfo

func (m *GetTemplateByGroupIdRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetTemplateByGroupIdResponse struct {
	GroupTemplate        *GroupTemplate `protobuf:"bytes,1,opt,name=group_template,json=groupTemplate,proto3" json:"group_template,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetTemplateByGroupIdResponse) Reset()         { *m = GetTemplateByGroupIdResponse{} }
func (m *GetTemplateByGroupIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetTemplateByGroupIdResponse) ProtoMessage()    {}
func (*GetTemplateByGroupIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{75}
}
func (m *GetTemplateByGroupIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTemplateByGroupIdResponse.Unmarshal(m, b)
}
func (m *GetTemplateByGroupIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTemplateByGroupIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetTemplateByGroupIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTemplateByGroupIdResponse.Merge(dst, src)
}
func (m *GetTemplateByGroupIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetTemplateByGroupIdResponse.Size(m)
}
func (m *GetTemplateByGroupIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTemplateByGroupIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTemplateByGroupIdResponse proto.InternalMessageInfo

func (m *GetTemplateByGroupIdResponse) GetGroupTemplate() *GroupTemplate {
	if m != nil {
		return m.GroupTemplate
	}
	return nil
}

type GetScriptListByFilterRequest struct {
	Sex                  GetScriptListByFilterRequest_SexOpt `protobuf:"varint,1,opt,name=sex,proto3,enum=aigc_group.GetScriptListByFilterRequest_SexOpt" json:"sex,omitempty"`
	Cursor               string                              `protobuf:"bytes,2,opt,name=cursor,proto3" json:"cursor,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *GetScriptListByFilterRequest) Reset()         { *m = GetScriptListByFilterRequest{} }
func (m *GetScriptListByFilterRequest) String() string { return proto.CompactTextString(m) }
func (*GetScriptListByFilterRequest) ProtoMessage()    {}
func (*GetScriptListByFilterRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{76}
}
func (m *GetScriptListByFilterRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScriptListByFilterRequest.Unmarshal(m, b)
}
func (m *GetScriptListByFilterRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScriptListByFilterRequest.Marshal(b, m, deterministic)
}
func (dst *GetScriptListByFilterRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScriptListByFilterRequest.Merge(dst, src)
}
func (m *GetScriptListByFilterRequest) XXX_Size() int {
	return xxx_messageInfo_GetScriptListByFilterRequest.Size(m)
}
func (m *GetScriptListByFilterRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScriptListByFilterRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetScriptListByFilterRequest proto.InternalMessageInfo

func (m *GetScriptListByFilterRequest) GetSex() GetScriptListByFilterRequest_SexOpt {
	if m != nil {
		return m.Sex
	}
	return GetScriptListByFilterRequest_SEX_UNSPECIFIED
}

func (m *GetScriptListByFilterRequest) GetCursor() string {
	if m != nil {
		return m.Cursor
	}
	return ""
}

type GetScriptListByFilterResponse struct {
	Templates            []*GroupTemplate `protobuf:"bytes,1,rep,name=templates,proto3" json:"templates,omitempty"`
	NextCursor           string           `protobuf:"bytes,2,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetScriptListByFilterResponse) Reset()         { *m = GetScriptListByFilterResponse{} }
func (m *GetScriptListByFilterResponse) String() string { return proto.CompactTextString(m) }
func (*GetScriptListByFilterResponse) ProtoMessage()    {}
func (*GetScriptListByFilterResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{77}
}
func (m *GetScriptListByFilterResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScriptListByFilterResponse.Unmarshal(m, b)
}
func (m *GetScriptListByFilterResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScriptListByFilterResponse.Marshal(b, m, deterministic)
}
func (dst *GetScriptListByFilterResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScriptListByFilterResponse.Merge(dst, src)
}
func (m *GetScriptListByFilterResponse) XXX_Size() int {
	return xxx_messageInfo_GetScriptListByFilterResponse.Size(m)
}
func (m *GetScriptListByFilterResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScriptListByFilterResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetScriptListByFilterResponse proto.InternalMessageInfo

func (m *GetScriptListByFilterResponse) GetTemplates() []*GroupTemplate {
	if m != nil {
		return m.Templates
	}
	return nil
}

func (m *GetScriptListByFilterResponse) GetNextCursor() string {
	if m != nil {
		return m.NextCursor
	}
	return ""
}

type GetActiveGroupListRequest struct {
	Begin                int64    `protobuf:"varint,1,opt,name=begin,proto3" json:"begin,omitempty"`
	End                  int64    `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset               uint32   `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActiveGroupListRequest) Reset()         { *m = GetActiveGroupListRequest{} }
func (m *GetActiveGroupListRequest) String() string { return proto.CompactTextString(m) }
func (*GetActiveGroupListRequest) ProtoMessage()    {}
func (*GetActiveGroupListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{78}
}
func (m *GetActiveGroupListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActiveGroupListRequest.Unmarshal(m, b)
}
func (m *GetActiveGroupListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActiveGroupListRequest.Marshal(b, m, deterministic)
}
func (dst *GetActiveGroupListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActiveGroupListRequest.Merge(dst, src)
}
func (m *GetActiveGroupListRequest) XXX_Size() int {
	return xxx_messageInfo_GetActiveGroupListRequest.Size(m)
}
func (m *GetActiveGroupListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActiveGroupListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetActiveGroupListRequest proto.InternalMessageInfo

func (m *GetActiveGroupListRequest) GetBegin() int64 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetActiveGroupListRequest) GetEnd() int64 {
	if m != nil {
		return m.End
	}
	return 0
}

func (m *GetActiveGroupListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetActiveGroupListRequest) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

type GetActiveGroupListResponse struct {
	List                 []*ActiveGroup `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetActiveGroupListResponse) Reset()         { *m = GetActiveGroupListResponse{} }
func (m *GetActiveGroupListResponse) String() string { return proto.CompactTextString(m) }
func (*GetActiveGroupListResponse) ProtoMessage()    {}
func (*GetActiveGroupListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{79}
}
func (m *GetActiveGroupListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActiveGroupListResponse.Unmarshal(m, b)
}
func (m *GetActiveGroupListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActiveGroupListResponse.Marshal(b, m, deterministic)
}
func (dst *GetActiveGroupListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActiveGroupListResponse.Merge(dst, src)
}
func (m *GetActiveGroupListResponse) XXX_Size() int {
	return xxx_messageInfo_GetActiveGroupListResponse.Size(m)
}
func (m *GetActiveGroupListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActiveGroupListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetActiveGroupListResponse proto.InternalMessageInfo

func (m *GetActiveGroupListResponse) GetList() []*ActiveGroup {
	if m != nil {
		return m.List
	}
	return nil
}

type BatchGetActiveGroupRequest struct {
	IdList               []uint32 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetActiveGroupRequest) Reset()         { *m = BatchGetActiveGroupRequest{} }
func (m *BatchGetActiveGroupRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetActiveGroupRequest) ProtoMessage()    {}
func (*BatchGetActiveGroupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{80}
}
func (m *BatchGetActiveGroupRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetActiveGroupRequest.Unmarshal(m, b)
}
func (m *BatchGetActiveGroupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetActiveGroupRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetActiveGroupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetActiveGroupRequest.Merge(dst, src)
}
func (m *BatchGetActiveGroupRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetActiveGroupRequest.Size(m)
}
func (m *BatchGetActiveGroupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetActiveGroupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetActiveGroupRequest proto.InternalMessageInfo

func (m *BatchGetActiveGroupRequest) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type BatchGetActiveGroupResponse struct {
	Groups               map[uint32]*ActiveGroup `protobuf:"bytes,1,rep,name=groups,proto3" json:"groups,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchGetActiveGroupResponse) Reset()         { *m = BatchGetActiveGroupResponse{} }
func (m *BatchGetActiveGroupResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetActiveGroupResponse) ProtoMessage()    {}
func (*BatchGetActiveGroupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{81}
}
func (m *BatchGetActiveGroupResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetActiveGroupResponse.Unmarshal(m, b)
}
func (m *BatchGetActiveGroupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetActiveGroupResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetActiveGroupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetActiveGroupResponse.Merge(dst, src)
}
func (m *BatchGetActiveGroupResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetActiveGroupResponse.Size(m)
}
func (m *BatchGetActiveGroupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetActiveGroupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetActiveGroupResponse proto.InternalMessageInfo

func (m *BatchGetActiveGroupResponse) GetGroups() map[uint32]*ActiveGroup {
	if m != nil {
		return m.Groups
	}
	return nil
}

type ScanGroupTriggerTaskRequest struct {
	TaskType GroupTriggerTask_Type `protobuf:"varint,1,opt,name=task_type,json=taskType,proto3,enum=aigc_group.GroupTriggerTask_Type" json:"task_type,omitempty"`
	// 扫描出<=expire的任务
	Expire               int64    `protobuf:"varint,2,opt,name=expire,proto3" json:"expire,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScanGroupTriggerTaskRequest) Reset()         { *m = ScanGroupTriggerTaskRequest{} }
func (m *ScanGroupTriggerTaskRequest) String() string { return proto.CompactTextString(m) }
func (*ScanGroupTriggerTaskRequest) ProtoMessage()    {}
func (*ScanGroupTriggerTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{82}
}
func (m *ScanGroupTriggerTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScanGroupTriggerTaskRequest.Unmarshal(m, b)
}
func (m *ScanGroupTriggerTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScanGroupTriggerTaskRequest.Marshal(b, m, deterministic)
}
func (dst *ScanGroupTriggerTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanGroupTriggerTaskRequest.Merge(dst, src)
}
func (m *ScanGroupTriggerTaskRequest) XXX_Size() int {
	return xxx_messageInfo_ScanGroupTriggerTaskRequest.Size(m)
}
func (m *ScanGroupTriggerTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanGroupTriggerTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ScanGroupTriggerTaskRequest proto.InternalMessageInfo

func (m *ScanGroupTriggerTaskRequest) GetTaskType() GroupTriggerTask_Type {
	if m != nil {
		return m.TaskType
	}
	return GroupTriggerTask_TYPE_UNSPECIFIED
}

func (m *ScanGroupTriggerTaskRequest) GetExpire() int64 {
	if m != nil {
		return m.Expire
	}
	return 0
}

func (m *ScanGroupTriggerTaskRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type ScanGroupTriggerTaskResponse struct {
	HasMore              bool                `protobuf:"varint,1,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	List                 []*GroupTriggerTask `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ScanGroupTriggerTaskResponse) Reset()         { *m = ScanGroupTriggerTaskResponse{} }
func (m *ScanGroupTriggerTaskResponse) String() string { return proto.CompactTextString(m) }
func (*ScanGroupTriggerTaskResponse) ProtoMessage()    {}
func (*ScanGroupTriggerTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{83}
}
func (m *ScanGroupTriggerTaskResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScanGroupTriggerTaskResponse.Unmarshal(m, b)
}
func (m *ScanGroupTriggerTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScanGroupTriggerTaskResponse.Marshal(b, m, deterministic)
}
func (dst *ScanGroupTriggerTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanGroupTriggerTaskResponse.Merge(dst, src)
}
func (m *ScanGroupTriggerTaskResponse) XXX_Size() int {
	return xxx_messageInfo_ScanGroupTriggerTaskResponse.Size(m)
}
func (m *ScanGroupTriggerTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanGroupTriggerTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ScanGroupTriggerTaskResponse proto.InternalMessageInfo

func (m *ScanGroupTriggerTaskResponse) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

func (m *ScanGroupTriggerTaskResponse) GetList() []*GroupTriggerTask {
	if m != nil {
		return m.List
	}
	return nil
}

type RemoveGroupTriggerTaskRequest struct {
	TaskType             GroupTriggerTask_Type `protobuf:"varint,1,opt,name=task_type,json=taskType,proto3,enum=aigc_group.GroupTriggerTask_Type" json:"task_type,omitempty"`
	GroupIdList          []uint32              `protobuf:"varint,2,rep,packed,name=group_id_list,json=groupIdList,proto3" json:"group_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *RemoveGroupTriggerTaskRequest) Reset()         { *m = RemoveGroupTriggerTaskRequest{} }
func (m *RemoveGroupTriggerTaskRequest) String() string { return proto.CompactTextString(m) }
func (*RemoveGroupTriggerTaskRequest) ProtoMessage()    {}
func (*RemoveGroupTriggerTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{84}
}
func (m *RemoveGroupTriggerTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveGroupTriggerTaskRequest.Unmarshal(m, b)
}
func (m *RemoveGroupTriggerTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveGroupTriggerTaskRequest.Marshal(b, m, deterministic)
}
func (dst *RemoveGroupTriggerTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveGroupTriggerTaskRequest.Merge(dst, src)
}
func (m *RemoveGroupTriggerTaskRequest) XXX_Size() int {
	return xxx_messageInfo_RemoveGroupTriggerTaskRequest.Size(m)
}
func (m *RemoveGroupTriggerTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveGroupTriggerTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveGroupTriggerTaskRequest proto.InternalMessageInfo

func (m *RemoveGroupTriggerTaskRequest) GetTaskType() GroupTriggerTask_Type {
	if m != nil {
		return m.TaskType
	}
	return GroupTriggerTask_TYPE_UNSPECIFIED
}

func (m *RemoveGroupTriggerTaskRequest) GetGroupIdList() []uint32 {
	if m != nil {
		return m.GroupIdList
	}
	return nil
}

type RemoveGroupTriggerTaskResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveGroupTriggerTaskResponse) Reset()         { *m = RemoveGroupTriggerTaskResponse{} }
func (m *RemoveGroupTriggerTaskResponse) String() string { return proto.CompactTextString(m) }
func (*RemoveGroupTriggerTaskResponse) ProtoMessage()    {}
func (*RemoveGroupTriggerTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{85}
}
func (m *RemoveGroupTriggerTaskResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveGroupTriggerTaskResponse.Unmarshal(m, b)
}
func (m *RemoveGroupTriggerTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveGroupTriggerTaskResponse.Marshal(b, m, deterministic)
}
func (dst *RemoveGroupTriggerTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveGroupTriggerTaskResponse.Merge(dst, src)
}
func (m *RemoveGroupTriggerTaskResponse) XXX_Size() int {
	return xxx_messageInfo_RemoveGroupTriggerTaskResponse.Size(m)
}
func (m *RemoveGroupTriggerTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveGroupTriggerTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveGroupTriggerTaskResponse proto.InternalMessageInfo

type GetFakeHelloCountRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFakeHelloCountRequest) Reset()         { *m = GetFakeHelloCountRequest{} }
func (m *GetFakeHelloCountRequest) String() string { return proto.CompactTextString(m) }
func (*GetFakeHelloCountRequest) ProtoMessage()    {}
func (*GetFakeHelloCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{86}
}
func (m *GetFakeHelloCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFakeHelloCountRequest.Unmarshal(m, b)
}
func (m *GetFakeHelloCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFakeHelloCountRequest.Marshal(b, m, deterministic)
}
func (dst *GetFakeHelloCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFakeHelloCountRequest.Merge(dst, src)
}
func (m *GetFakeHelloCountRequest) XXX_Size() int {
	return xxx_messageInfo_GetFakeHelloCountRequest.Size(m)
}
func (m *GetFakeHelloCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFakeHelloCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFakeHelloCountRequest proto.InternalMessageInfo

func (m *GetFakeHelloCountRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetFakeHelloCountResponse struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFakeHelloCountResponse) Reset()         { *m = GetFakeHelloCountResponse{} }
func (m *GetFakeHelloCountResponse) String() string { return proto.CompactTextString(m) }
func (*GetFakeHelloCountResponse) ProtoMessage()    {}
func (*GetFakeHelloCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{87}
}
func (m *GetFakeHelloCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFakeHelloCountResponse.Unmarshal(m, b)
}
func (m *GetFakeHelloCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFakeHelloCountResponse.Marshal(b, m, deterministic)
}
func (dst *GetFakeHelloCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFakeHelloCountResponse.Merge(dst, src)
}
func (m *GetFakeHelloCountResponse) XXX_Size() int {
	return xxx_messageInfo_GetFakeHelloCountResponse.Size(m)
}
func (m *GetFakeHelloCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFakeHelloCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFakeHelloCountResponse proto.InternalMessageInfo

func (m *GetFakeHelloCountResponse) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type IncrFakeHelloCountRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncrFakeHelloCountRequest) Reset()         { *m = IncrFakeHelloCountRequest{} }
func (m *IncrFakeHelloCountRequest) String() string { return proto.CompactTextString(m) }
func (*IncrFakeHelloCountRequest) ProtoMessage()    {}
func (*IncrFakeHelloCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{88}
}
func (m *IncrFakeHelloCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncrFakeHelloCountRequest.Unmarshal(m, b)
}
func (m *IncrFakeHelloCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncrFakeHelloCountRequest.Marshal(b, m, deterministic)
}
func (dst *IncrFakeHelloCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncrFakeHelloCountRequest.Merge(dst, src)
}
func (m *IncrFakeHelloCountRequest) XXX_Size() int {
	return xxx_messageInfo_IncrFakeHelloCountRequest.Size(m)
}
func (m *IncrFakeHelloCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_IncrFakeHelloCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_IncrFakeHelloCountRequest proto.InternalMessageInfo

func (m *IncrFakeHelloCountRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type IncrFakeHelloCountResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncrFakeHelloCountResponse) Reset()         { *m = IncrFakeHelloCountResponse{} }
func (m *IncrFakeHelloCountResponse) String() string { return proto.CompactTextString(m) }
func (*IncrFakeHelloCountResponse) ProtoMessage()    {}
func (*IncrFakeHelloCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{89}
}
func (m *IncrFakeHelloCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncrFakeHelloCountResponse.Unmarshal(m, b)
}
func (m *IncrFakeHelloCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncrFakeHelloCountResponse.Marshal(b, m, deterministic)
}
func (dst *IncrFakeHelloCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncrFakeHelloCountResponse.Merge(dst, src)
}
func (m *IncrFakeHelloCountResponse) XXX_Size() int {
	return xxx_messageInfo_IncrFakeHelloCountResponse.Size(m)
}
func (m *IncrFakeHelloCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IncrFakeHelloCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IncrFakeHelloCountResponse proto.InternalMessageInfo

type DetectDumbGroupMemberRequest struct {
	Expire               int64    `protobuf:"varint,1,opt,name=expire,proto3" json:"expire,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DetectDumbGroupMemberRequest) Reset()         { *m = DetectDumbGroupMemberRequest{} }
func (m *DetectDumbGroupMemberRequest) String() string { return proto.CompactTextString(m) }
func (*DetectDumbGroupMemberRequest) ProtoMessage()    {}
func (*DetectDumbGroupMemberRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{90}
}
func (m *DetectDumbGroupMemberRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DetectDumbGroupMemberRequest.Unmarshal(m, b)
}
func (m *DetectDumbGroupMemberRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DetectDumbGroupMemberRequest.Marshal(b, m, deterministic)
}
func (dst *DetectDumbGroupMemberRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DetectDumbGroupMemberRequest.Merge(dst, src)
}
func (m *DetectDumbGroupMemberRequest) XXX_Size() int {
	return xxx_messageInfo_DetectDumbGroupMemberRequest.Size(m)
}
func (m *DetectDumbGroupMemberRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DetectDumbGroupMemberRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DetectDumbGroupMemberRequest proto.InternalMessageInfo

func (m *DetectDumbGroupMemberRequest) GetExpire() int64 {
	if m != nil {
		return m.Expire
	}
	return 0
}

func (m *DetectDumbGroupMemberRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type DetectDumbGroupMemberResponse struct {
	Members              []*DetectDumbGroupMemberResponse_Member `protobuf:"bytes,1,rep,name=members,proto3" json:"members,omitempty"`
	HasMore              bool                                    `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *DetectDumbGroupMemberResponse) Reset()         { *m = DetectDumbGroupMemberResponse{} }
func (m *DetectDumbGroupMemberResponse) String() string { return proto.CompactTextString(m) }
func (*DetectDumbGroupMemberResponse) ProtoMessage()    {}
func (*DetectDumbGroupMemberResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{91}
}
func (m *DetectDumbGroupMemberResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DetectDumbGroupMemberResponse.Unmarshal(m, b)
}
func (m *DetectDumbGroupMemberResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DetectDumbGroupMemberResponse.Marshal(b, m, deterministic)
}
func (dst *DetectDumbGroupMemberResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DetectDumbGroupMemberResponse.Merge(dst, src)
}
func (m *DetectDumbGroupMemberResponse) XXX_Size() int {
	return xxx_messageInfo_DetectDumbGroupMemberResponse.Size(m)
}
func (m *DetectDumbGroupMemberResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DetectDumbGroupMemberResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DetectDumbGroupMemberResponse proto.InternalMessageInfo

func (m *DetectDumbGroupMemberResponse) GetMembers() []*DetectDumbGroupMemberResponse_Member {
	if m != nil {
		return m.Members
	}
	return nil
}

func (m *DetectDumbGroupMemberResponse) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

type DetectDumbGroupMemberResponse_Member struct {
	Uid                  uint32    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TemplId              uint32    `protobuf:"varint,2,opt,name=templ_id,json=templId,proto3" json:"templ_id,omitempty"`
	GroupId              uint32    `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GroupType            GroupType `protobuf:"varint,4,opt,name=group_type,json=groupType,proto3,enum=aigc_group.GroupType" json:"group_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *DetectDumbGroupMemberResponse_Member) Reset()         { *m = DetectDumbGroupMemberResponse_Member{} }
func (m *DetectDumbGroupMemberResponse_Member) String() string { return proto.CompactTextString(m) }
func (*DetectDumbGroupMemberResponse_Member) ProtoMessage()    {}
func (*DetectDumbGroupMemberResponse_Member) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{91, 0}
}
func (m *DetectDumbGroupMemberResponse_Member) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DetectDumbGroupMemberResponse_Member.Unmarshal(m, b)
}
func (m *DetectDumbGroupMemberResponse_Member) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DetectDumbGroupMemberResponse_Member.Marshal(b, m, deterministic)
}
func (dst *DetectDumbGroupMemberResponse_Member) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DetectDumbGroupMemberResponse_Member.Merge(dst, src)
}
func (m *DetectDumbGroupMemberResponse_Member) XXX_Size() int {
	return xxx_messageInfo_DetectDumbGroupMemberResponse_Member.Size(m)
}
func (m *DetectDumbGroupMemberResponse_Member) XXX_DiscardUnknown() {
	xxx_messageInfo_DetectDumbGroupMemberResponse_Member.DiscardUnknown(m)
}

var xxx_messageInfo_DetectDumbGroupMemberResponse_Member proto.InternalMessageInfo

func (m *DetectDumbGroupMemberResponse_Member) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DetectDumbGroupMemberResponse_Member) GetTemplId() uint32 {
	if m != nil {
		return m.TemplId
	}
	return 0
}

func (m *DetectDumbGroupMemberResponse_Member) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *DetectDumbGroupMemberResponse_Member) GetGroupType() GroupType {
	if m != nil {
		return m.GroupType
	}
	return GroupType_GROUP_TYPE_UNSPECIFIED
}

type AddSpecialMsgRequest struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	SeqId                uint32   `protobuf:"varint,3,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	Msg                  []byte   `protobuf:"bytes,4,opt,name=msg,proto3" json:"msg,omitempty"`
	MsgType              uint32   `protobuf:"varint,5,opt,name=msg_type,json=msgType,proto3" json:"msg_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSpecialMsgRequest) Reset()         { *m = AddSpecialMsgRequest{} }
func (m *AddSpecialMsgRequest) String() string { return proto.CompactTextString(m) }
func (*AddSpecialMsgRequest) ProtoMessage()    {}
func (*AddSpecialMsgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{92}
}
func (m *AddSpecialMsgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSpecialMsgRequest.Unmarshal(m, b)
}
func (m *AddSpecialMsgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSpecialMsgRequest.Marshal(b, m, deterministic)
}
func (dst *AddSpecialMsgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSpecialMsgRequest.Merge(dst, src)
}
func (m *AddSpecialMsgRequest) XXX_Size() int {
	return xxx_messageInfo_AddSpecialMsgRequest.Size(m)
}
func (m *AddSpecialMsgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSpecialMsgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddSpecialMsgRequest proto.InternalMessageInfo

func (m *AddSpecialMsgRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *AddSpecialMsgRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddSpecialMsgRequest) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *AddSpecialMsgRequest) GetMsg() []byte {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *AddSpecialMsgRequest) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

type AddSpecialMsgResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSpecialMsgResponse) Reset()         { *m = AddSpecialMsgResponse{} }
func (m *AddSpecialMsgResponse) String() string { return proto.CompactTextString(m) }
func (*AddSpecialMsgResponse) ProtoMessage()    {}
func (*AddSpecialMsgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{93}
}
func (m *AddSpecialMsgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSpecialMsgResponse.Unmarshal(m, b)
}
func (m *AddSpecialMsgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSpecialMsgResponse.Marshal(b, m, deterministic)
}
func (dst *AddSpecialMsgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSpecialMsgResponse.Merge(dst, src)
}
func (m *AddSpecialMsgResponse) XXX_Size() int {
	return xxx_messageInfo_AddSpecialMsgResponse.Size(m)
}
func (m *AddSpecialMsgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSpecialMsgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddSpecialMsgResponse proto.InternalMessageInfo

type PullSpecialMsgRequest struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	StartSeq             uint32   `protobuf:"varint,3,opt,name=start_seq,json=startSeq,proto3" json:"start_seq,omitempty"`
	BusinessType         uint32   `protobuf:"varint,4,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	Reverse              bool     `protobuf:"varint,6,opt,name=reverse,proto3" json:"reverse,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PullSpecialMsgRequest) Reset()         { *m = PullSpecialMsgRequest{} }
func (m *PullSpecialMsgRequest) String() string { return proto.CompactTextString(m) }
func (*PullSpecialMsgRequest) ProtoMessage()    {}
func (*PullSpecialMsgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{94}
}
func (m *PullSpecialMsgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PullSpecialMsgRequest.Unmarshal(m, b)
}
func (m *PullSpecialMsgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PullSpecialMsgRequest.Marshal(b, m, deterministic)
}
func (dst *PullSpecialMsgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PullSpecialMsgRequest.Merge(dst, src)
}
func (m *PullSpecialMsgRequest) XXX_Size() int {
	return xxx_messageInfo_PullSpecialMsgRequest.Size(m)
}
func (m *PullSpecialMsgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PullSpecialMsgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PullSpecialMsgRequest proto.InternalMessageInfo

func (m *PullSpecialMsgRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *PullSpecialMsgRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PullSpecialMsgRequest) GetStartSeq() uint32 {
	if m != nil {
		return m.StartSeq
	}
	return 0
}

func (m *PullSpecialMsgRequest) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

func (m *PullSpecialMsgRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *PullSpecialMsgRequest) GetReverse() bool {
	if m != nil {
		return m.Reverse
	}
	return false
}

type PullSpecialMsgResponse struct {
	Msg                  [][]byte `protobuf:"bytes,1,rep,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PullSpecialMsgResponse) Reset()         { *m = PullSpecialMsgResponse{} }
func (m *PullSpecialMsgResponse) String() string { return proto.CompactTextString(m) }
func (*PullSpecialMsgResponse) ProtoMessage()    {}
func (*PullSpecialMsgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{95}
}
func (m *PullSpecialMsgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PullSpecialMsgResponse.Unmarshal(m, b)
}
func (m *PullSpecialMsgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PullSpecialMsgResponse.Marshal(b, m, deterministic)
}
func (dst *PullSpecialMsgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PullSpecialMsgResponse.Merge(dst, src)
}
func (m *PullSpecialMsgResponse) XXX_Size() int {
	return xxx_messageInfo_PullSpecialMsgResponse.Size(m)
}
func (m *PullSpecialMsgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PullSpecialMsgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PullSpecialMsgResponse proto.InternalMessageInfo

func (m *PullSpecialMsgResponse) GetMsg() [][]byte {
	if m != nil {
		return m.Msg
	}
	return nil
}

type BatGetLastSpecialMsgRequest struct {
	GroupIds             []uint32 `protobuf:"varint,1,rep,packed,name=group_ids,json=groupIds,proto3" json:"group_ids,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	BusinessType         uint32   `protobuf:"varint,3,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetLastSpecialMsgRequest) Reset()         { *m = BatGetLastSpecialMsgRequest{} }
func (m *BatGetLastSpecialMsgRequest) String() string { return proto.CompactTextString(m) }
func (*BatGetLastSpecialMsgRequest) ProtoMessage()    {}
func (*BatGetLastSpecialMsgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{96}
}
func (m *BatGetLastSpecialMsgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetLastSpecialMsgRequest.Unmarshal(m, b)
}
func (m *BatGetLastSpecialMsgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetLastSpecialMsgRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetLastSpecialMsgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetLastSpecialMsgRequest.Merge(dst, src)
}
func (m *BatGetLastSpecialMsgRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetLastSpecialMsgRequest.Size(m)
}
func (m *BatGetLastSpecialMsgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetLastSpecialMsgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetLastSpecialMsgRequest proto.InternalMessageInfo

func (m *BatGetLastSpecialMsgRequest) GetGroupIds() []uint32 {
	if m != nil {
		return m.GroupIds
	}
	return nil
}

func (m *BatGetLastSpecialMsgRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatGetLastSpecialMsgRequest) GetBusinessType() uint32 {
	if m != nil {
		return m.BusinessType
	}
	return 0
}

type BatGetLastSpecialMsgResponse struct {
	GroupMsgMap          map[uint32][]byte `protobuf:"bytes,1,rep,name=group_msg_map,json=groupMsgMap,proto3" json:"group_msg_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatGetLastSpecialMsgResponse) Reset()         { *m = BatGetLastSpecialMsgResponse{} }
func (m *BatGetLastSpecialMsgResponse) String() string { return proto.CompactTextString(m) }
func (*BatGetLastSpecialMsgResponse) ProtoMessage()    {}
func (*BatGetLastSpecialMsgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{97}
}
func (m *BatGetLastSpecialMsgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetLastSpecialMsgResponse.Unmarshal(m, b)
}
func (m *BatGetLastSpecialMsgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetLastSpecialMsgResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetLastSpecialMsgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetLastSpecialMsgResponse.Merge(dst, src)
}
func (m *BatGetLastSpecialMsgResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetLastSpecialMsgResponse.Size(m)
}
func (m *BatGetLastSpecialMsgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetLastSpecialMsgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetLastSpecialMsgResponse proto.InternalMessageInfo

func (m *BatGetLastSpecialMsgResponse) GetGroupMsgMap() map[uint32][]byte {
	if m != nil {
		return m.GroupMsgMap
	}
	return nil
}

type BatGetGroupLatestSeqIdRequest struct {
	GroupIds             []uint32 `protobuf:"varint,1,rep,packed,name=group_ids,json=groupIds,proto3" json:"group_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetGroupLatestSeqIdRequest) Reset()         { *m = BatGetGroupLatestSeqIdRequest{} }
func (m *BatGetGroupLatestSeqIdRequest) String() string { return proto.CompactTextString(m) }
func (*BatGetGroupLatestSeqIdRequest) ProtoMessage()    {}
func (*BatGetGroupLatestSeqIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{98}
}
func (m *BatGetGroupLatestSeqIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetGroupLatestSeqIdRequest.Unmarshal(m, b)
}
func (m *BatGetGroupLatestSeqIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetGroupLatestSeqIdRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetGroupLatestSeqIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetGroupLatestSeqIdRequest.Merge(dst, src)
}
func (m *BatGetGroupLatestSeqIdRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetGroupLatestSeqIdRequest.Size(m)
}
func (m *BatGetGroupLatestSeqIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetGroupLatestSeqIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetGroupLatestSeqIdRequest proto.InternalMessageInfo

func (m *BatGetGroupLatestSeqIdRequest) GetGroupIds() []uint32 {
	if m != nil {
		return m.GroupIds
	}
	return nil
}

type BatGetGroupLatestSeqIdResponse struct {
	GroupSeqIdMap        map[uint32]uint32 `protobuf:"bytes,1,rep,name=group_seq_id_map,json=groupSeqIdMap,proto3" json:"group_seq_id_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatGetGroupLatestSeqIdResponse) Reset()         { *m = BatGetGroupLatestSeqIdResponse{} }
func (m *BatGetGroupLatestSeqIdResponse) String() string { return proto.CompactTextString(m) }
func (*BatGetGroupLatestSeqIdResponse) ProtoMessage()    {}
func (*BatGetGroupLatestSeqIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{99}
}
func (m *BatGetGroupLatestSeqIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetGroupLatestSeqIdResponse.Unmarshal(m, b)
}
func (m *BatGetGroupLatestSeqIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetGroupLatestSeqIdResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetGroupLatestSeqIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetGroupLatestSeqIdResponse.Merge(dst, src)
}
func (m *BatGetGroupLatestSeqIdResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetGroupLatestSeqIdResponse.Size(m)
}
func (m *BatGetGroupLatestSeqIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetGroupLatestSeqIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetGroupLatestSeqIdResponse proto.InternalMessageInfo

func (m *BatGetGroupLatestSeqIdResponse) GetGroupSeqIdMap() map[uint32]uint32 {
	if m != nil {
		return m.GroupSeqIdMap
	}
	return nil
}

type UpdateGroupLatestSeqIdRequest struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	SeqId                uint32   `protobuf:"varint,2,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupLatestSeqIdRequest) Reset()         { *m = UpdateGroupLatestSeqIdRequest{} }
func (m *UpdateGroupLatestSeqIdRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupLatestSeqIdRequest) ProtoMessage()    {}
func (*UpdateGroupLatestSeqIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{100}
}
func (m *UpdateGroupLatestSeqIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupLatestSeqIdRequest.Unmarshal(m, b)
}
func (m *UpdateGroupLatestSeqIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupLatestSeqIdRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupLatestSeqIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupLatestSeqIdRequest.Merge(dst, src)
}
func (m *UpdateGroupLatestSeqIdRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupLatestSeqIdRequest.Size(m)
}
func (m *UpdateGroupLatestSeqIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupLatestSeqIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupLatestSeqIdRequest proto.InternalMessageInfo

func (m *UpdateGroupLatestSeqIdRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *UpdateGroupLatestSeqIdRequest) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

type UpdateGroupLatestSeqIdResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupLatestSeqIdResponse) Reset()         { *m = UpdateGroupLatestSeqIdResponse{} }
func (m *UpdateGroupLatestSeqIdResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupLatestSeqIdResponse) ProtoMessage()    {}
func (*UpdateGroupLatestSeqIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{101}
}
func (m *UpdateGroupLatestSeqIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupLatestSeqIdResponse.Unmarshal(m, b)
}
func (m *UpdateGroupLatestSeqIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupLatestSeqIdResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupLatestSeqIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupLatestSeqIdResponse.Merge(dst, src)
}
func (m *UpdateGroupLatestSeqIdResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupLatestSeqIdResponse.Size(m)
}
func (m *UpdateGroupLatestSeqIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupLatestSeqIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupLatestSeqIdResponse proto.InternalMessageInfo

type LeaveGroupNotify struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GroupId              uint32   `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LeaveGroupNotify) Reset()         { *m = LeaveGroupNotify{} }
func (m *LeaveGroupNotify) String() string { return proto.CompactTextString(m) }
func (*LeaveGroupNotify) ProtoMessage()    {}
func (*LeaveGroupNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_group_fdb4e3a8f77fbae9, []int{102}
}
func (m *LeaveGroupNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LeaveGroupNotify.Unmarshal(m, b)
}
func (m *LeaveGroupNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LeaveGroupNotify.Marshal(b, m, deterministic)
}
func (dst *LeaveGroupNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LeaveGroupNotify.Merge(dst, src)
}
func (m *LeaveGroupNotify) XXX_Size() int {
	return xxx_messageInfo_LeaveGroupNotify.Size(m)
}
func (m *LeaveGroupNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_LeaveGroupNotify.DiscardUnknown(m)
}

var xxx_messageInfo_LeaveGroupNotify proto.InternalMessageInfo

func (m *LeaveGroupNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LeaveGroupNotify) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func init() {
	proto.RegisterType((*GroupTemplate)(nil), "aigc_group.GroupTemplate")
	proto.RegisterType((*PlayRole)(nil), "aigc_group.PlayRole")
	proto.RegisterType((*AIGroupPrologue)(nil), "aigc_group.AIGroupPrologue")
	proto.RegisterType((*ScriptInfo)(nil), "aigc_group.ScriptInfo")
	proto.RegisterType((*Group)(nil), "aigc_group.Group")
	proto.RegisterType((*GroupMember)(nil), "aigc_group.GroupMember")
	proto.RegisterType((*MatchReadyNotify)(nil), "aigc_group.MatchReadyNotify")
	proto.RegisterType((*MatchFoundNotify)(nil), "aigc_group.MatchFoundNotify")
	proto.RegisterType((*MatchPlayer)(nil), "aigc_group.MatchPlayer")
	proto.RegisterType((*ActiveGroup)(nil), "aigc_group.ActiveGroup")
	proto.RegisterType((*ActiveGroupMember)(nil), "aigc_group.ActiveGroupMember")
	proto.RegisterType((*MatchPlayerList)(nil), "aigc_group.MatchPlayerList")
	proto.RegisterType((*GroupTriggerTask)(nil), "aigc_group.GroupTriggerTask")
	proto.RegisterType((*FollowGuideNotify)(nil), "aigc_group.FollowGuideNotify")
	proto.RegisterType((*GroupMemberJoinedNotify)(nil), "aigc_group.GroupMemberJoinedNotify")
	proto.RegisterType((*User)(nil), "aigc_group.User")
	proto.RegisterType((*CreateGroupTemplateRequest)(nil), "aigc_group.CreateGroupTemplateRequest")
	proto.RegisterType((*CreateGroupTemplateResponse)(nil), "aigc_group.CreateGroupTemplateResponse")
	proto.RegisterType((*UpdateGroupTemplateRequest)(nil), "aigc_group.UpdateGroupTemplateRequest")
	proto.RegisterType((*UpdateGroupTemplateResponse)(nil), "aigc_group.UpdateGroupTemplateResponse")
	proto.RegisterType((*DeleteGroupTemplateRequest)(nil), "aigc_group.DeleteGroupTemplateRequest")
	proto.RegisterType((*DeleteGroupTemplateResponse)(nil), "aigc_group.DeleteGroupTemplateResponse")
	proto.RegisterType((*GetGroupTemplateByIdsRequest)(nil), "aigc_group.GetGroupTemplateByIdsRequest")
	proto.RegisterType((*GetGroupTemplateByIdsResponse)(nil), "aigc_group.GetGroupTemplateByIdsResponse")
	proto.RegisterType((*GetGroupTemplateByPageRequest)(nil), "aigc_group.GetGroupTemplateByPageRequest")
	proto.RegisterType((*GetGroupTemplateByPageResponse)(nil), "aigc_group.GetGroupTemplateByPageResponse")
	proto.RegisterType((*CreateGroupRequest)(nil), "aigc_group.CreateGroupRequest")
	proto.RegisterType((*CreateGroupResponse)(nil), "aigc_group.CreateGroupResponse")
	proto.RegisterType((*DeleteGroupRequest)(nil), "aigc_group.DeleteGroupRequest")
	proto.RegisterType((*DeleteGroupResponse)(nil), "aigc_group.DeleteGroupResponse")
	proto.RegisterType((*LeaveGroupRequest)(nil), "aigc_group.LeaveGroupRequest")
	proto.RegisterType((*LeaveGroupResponse)(nil), "aigc_group.LeaveGroupResponse")
	proto.RegisterType((*GetGroupInfoRequest)(nil), "aigc_group.GetGroupInfoRequest")
	proto.RegisterType((*GetGroupInfoResponse)(nil), "aigc_group.GetGroupInfoResponse")
	proto.RegisterType((*GetUserOwnedGroupListRequest)(nil), "aigc_group.GetUserOwnedGroupListRequest")
	proto.RegisterType((*GetUserOwnedGroupListResponse)(nil), "aigc_group.GetUserOwnedGroupListResponse")
	proto.RegisterType((*GetUserJoinedGroupListRequest)(nil), "aigc_group.GetUserJoinedGroupListRequest")
	proto.RegisterType((*GetUserJoinedGroupListResponse)(nil), "aigc_group.GetUserJoinedGroupListResponse")
	proto.RegisterType((*GetGroupMemberRequest)(nil), "aigc_group.GetGroupMemberRequest")
	proto.RegisterType((*GetGroupMemberResponse)(nil), "aigc_group.GetGroupMemberResponse")
	proto.RegisterType((*BatchGetGroupMemberRequest)(nil), "aigc_group.BatchGetGroupMemberRequest")
	proto.RegisterType((*BatchGetGroupMemberRequest_Member)(nil), "aigc_group.BatchGetGroupMemberRequest.Member")
	proto.RegisterType((*BatchGetGroupMemberResponse)(nil), "aigc_group.BatchGetGroupMemberResponse")
	proto.RegisterType((*GetGroupMemberListRequest)(nil), "aigc_group.GetGroupMemberListRequest")
	proto.RegisterType((*GetGroupMemberListResponse)(nil), "aigc_group.GetGroupMemberListResponse")
	proto.RegisterType((*BatchGetGroupMemberListRequest)(nil), "aigc_group.BatchGetGroupMemberListRequest")
	proto.RegisterType((*GroupMemberList)(nil), "aigc_group.GroupMemberList")
	proto.RegisterType((*BatchGetGroupMemberListResponse)(nil), "aigc_group.BatchGetGroupMemberListResponse")
	proto.RegisterMapType((map[uint32]*GroupMemberList)(nil), "aigc_group.BatchGetGroupMemberListResponse.GroupMembersMapEntry")
	proto.RegisterType((*HotBannerInfo)(nil), "aigc_group.HotBannerInfo")
	proto.RegisterType((*CreateHotBannerRequest)(nil), "aigc_group.CreateHotBannerRequest")
	proto.RegisterType((*CreateHotBannerRequest_HotBanner)(nil), "aigc_group.CreateHotBannerRequest.HotBanner")
	proto.RegisterType((*CreateHotBannerResponse)(nil), "aigc_group.CreateHotBannerResponse")
	proto.RegisterType((*UpdateHotBannerRequest)(nil), "aigc_group.UpdateHotBannerRequest")
	proto.RegisterType((*UpdateHotBannerRequest_HotBanner)(nil), "aigc_group.UpdateHotBannerRequest.HotBanner")
	proto.RegisterType((*UpdateHotBannerResponse)(nil), "aigc_group.UpdateHotBannerResponse")
	proto.RegisterType((*DeleteHotBannerRequest)(nil), "aigc_group.DeleteHotBannerRequest")
	proto.RegisterType((*DeleteHotBannerResponse)(nil), "aigc_group.DeleteHotBannerResponse")
	proto.RegisterType((*GetAllHotBannerRequest)(nil), "aigc_group.GetAllHotBannerRequest")
	proto.RegisterType((*GetAllHotBannerResponse)(nil), "aigc_group.GetAllHotBannerResponse")
	proto.RegisterType((*GetHotBannerByIdRequest)(nil), "aigc_group.GetHotBannerByIdRequest")
	proto.RegisterType((*GetHotBannerByIdResponse)(nil), "aigc_group.GetHotBannerByIdResponse")
	proto.RegisterType((*ResortBannerRequest)(nil), "aigc_group.ResortBannerRequest")
	proto.RegisterType((*ResortBannerResponse)(nil), "aigc_group.ResortBannerResponse")
	proto.RegisterType((*JoinMatchRequest)(nil), "aigc_group.JoinMatchRequest")
	proto.RegisterType((*JoinMatchRequest_Player)(nil), "aigc_group.JoinMatchRequest.Player")
	proto.RegisterType((*JoinMatchResponse)(nil), "aigc_group.JoinMatchResponse")
	proto.RegisterType((*CancelMatchRequest)(nil), "aigc_group.CancelMatchRequest")
	proto.RegisterType((*CancelMatchResponse)(nil), "aigc_group.CancelMatchResponse")
	proto.RegisterType((*ConfirmMatchRequest)(nil), "aigc_group.ConfirmMatchRequest")
	proto.RegisterType((*ConfirmMatchResponse)(nil), "aigc_group.ConfirmMatchResponse")
	proto.RegisterType((*BatchGetRandMatchPlayerRequest)(nil), "aigc_group.BatchGetRandMatchPlayerRequest")
	proto.RegisterType((*BatchGetRandMatchPlayerResponse)(nil), "aigc_group.BatchGetRandMatchPlayerResponse")
	proto.RegisterMapType((map[uint32]*MatchPlayerList)(nil), "aigc_group.BatchGetRandMatchPlayerResponse.PlayersEntry")
	proto.RegisterType((*UserInfo)(nil), "aigc_group.UserInfo")
	proto.RegisterType((*MultiGroupDefaultUserRoleSelectRequest)(nil), "aigc_group.MultiGroupDefaultUserRoleSelectRequest")
	proto.RegisterType((*MultiGroupDefaultUserRoleSelectResponse)(nil), "aigc_group.MultiGroupDefaultUserRoleSelectResponse")
	proto.RegisterType((*GetMultiGroupUserRoleInfoRequest)(nil), "aigc_group.GetMultiGroupUserRoleInfoRequest")
	proto.RegisterType((*UserSelectInfo)(nil), "aigc_group.UserSelectInfo")
	proto.RegisterType((*GetMultiGroupUserRoleInfoResponse)(nil), "aigc_group.GetMultiGroupUserRoleInfoResponse")
	proto.RegisterMapType((map[uint32]*UserSelectInfo)(nil), "aigc_group.GetMultiGroupUserRoleInfoResponse.UserSelectRoleMapEntry")
	proto.RegisterType((*GetTemplateByGroupIdRequest)(nil), "aigc_group.GetTemplateByGroupIdRequest")
	proto.RegisterType((*GetTemplateByGroupIdResponse)(nil), "aigc_group.GetTemplateByGroupIdResponse")
	proto.RegisterType((*GetScriptListByFilterRequest)(nil), "aigc_group.GetScriptListByFilterRequest")
	proto.RegisterType((*GetScriptListByFilterResponse)(nil), "aigc_group.GetScriptListByFilterResponse")
	proto.RegisterType((*GetActiveGroupListRequest)(nil), "aigc_group.GetActiveGroupListRequest")
	proto.RegisterType((*GetActiveGroupListResponse)(nil), "aigc_group.GetActiveGroupListResponse")
	proto.RegisterType((*BatchGetActiveGroupRequest)(nil), "aigc_group.BatchGetActiveGroupRequest")
	proto.RegisterType((*BatchGetActiveGroupResponse)(nil), "aigc_group.BatchGetActiveGroupResponse")
	proto.RegisterMapType((map[uint32]*ActiveGroup)(nil), "aigc_group.BatchGetActiveGroupResponse.GroupsEntry")
	proto.RegisterType((*ScanGroupTriggerTaskRequest)(nil), "aigc_group.ScanGroupTriggerTaskRequest")
	proto.RegisterType((*ScanGroupTriggerTaskResponse)(nil), "aigc_group.ScanGroupTriggerTaskResponse")
	proto.RegisterType((*RemoveGroupTriggerTaskRequest)(nil), "aigc_group.RemoveGroupTriggerTaskRequest")
	proto.RegisterType((*RemoveGroupTriggerTaskResponse)(nil), "aigc_group.RemoveGroupTriggerTaskResponse")
	proto.RegisterType((*GetFakeHelloCountRequest)(nil), "aigc_group.GetFakeHelloCountRequest")
	proto.RegisterType((*GetFakeHelloCountResponse)(nil), "aigc_group.GetFakeHelloCountResponse")
	proto.RegisterType((*IncrFakeHelloCountRequest)(nil), "aigc_group.IncrFakeHelloCountRequest")
	proto.RegisterType((*IncrFakeHelloCountResponse)(nil), "aigc_group.IncrFakeHelloCountResponse")
	proto.RegisterType((*DetectDumbGroupMemberRequest)(nil), "aigc_group.DetectDumbGroupMemberRequest")
	proto.RegisterType((*DetectDumbGroupMemberResponse)(nil), "aigc_group.DetectDumbGroupMemberResponse")
	proto.RegisterType((*DetectDumbGroupMemberResponse_Member)(nil), "aigc_group.DetectDumbGroupMemberResponse.Member")
	proto.RegisterType((*AddSpecialMsgRequest)(nil), "aigc_group.AddSpecialMsgRequest")
	proto.RegisterType((*AddSpecialMsgResponse)(nil), "aigc_group.AddSpecialMsgResponse")
	proto.RegisterType((*PullSpecialMsgRequest)(nil), "aigc_group.PullSpecialMsgRequest")
	proto.RegisterType((*PullSpecialMsgResponse)(nil), "aigc_group.PullSpecialMsgResponse")
	proto.RegisterType((*BatGetLastSpecialMsgRequest)(nil), "aigc_group.BatGetLastSpecialMsgRequest")
	proto.RegisterType((*BatGetLastSpecialMsgResponse)(nil), "aigc_group.BatGetLastSpecialMsgResponse")
	proto.RegisterMapType((map[uint32][]byte)(nil), "aigc_group.BatGetLastSpecialMsgResponse.GroupMsgMapEntry")
	proto.RegisterType((*BatGetGroupLatestSeqIdRequest)(nil), "aigc_group.BatGetGroupLatestSeqIdRequest")
	proto.RegisterType((*BatGetGroupLatestSeqIdResponse)(nil), "aigc_group.BatGetGroupLatestSeqIdResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "aigc_group.BatGetGroupLatestSeqIdResponse.GroupSeqIdMapEntry")
	proto.RegisterType((*UpdateGroupLatestSeqIdRequest)(nil), "aigc_group.UpdateGroupLatestSeqIdRequest")
	proto.RegisterType((*UpdateGroupLatestSeqIdResponse)(nil), "aigc_group.UpdateGroupLatestSeqIdResponse")
	proto.RegisterType((*LeaveGroupNotify)(nil), "aigc_group.LeaveGroupNotify")
	proto.RegisterEnum("aigc_group.GroupType", GroupType_name, GroupType_value)
	proto.RegisterEnum("aigc_group.GroupMemberType", GroupMemberType_name, GroupMemberType_value)
	proto.RegisterEnum("aigc_group.MatchStrategy", MatchStrategy_name, MatchStrategy_value)
	proto.RegisterEnum("aigc_group.GroupSource", GroupSource_name, GroupSource_value)
	proto.RegisterEnum("aigc_group.LeaveStrategy", LeaveStrategy_name, LeaveStrategy_value)
	proto.RegisterEnum("aigc_group.SpecialMsgType", SpecialMsgType_name, SpecialMsgType_value)
	proto.RegisterEnum("aigc_group.GroupTriggerTask_Type", GroupTriggerTask_Type_name, GroupTriggerTask_Type_value)
	proto.RegisterEnum("aigc_group.GetScriptListByFilterRequest_SexOpt", GetScriptListByFilterRequest_SexOpt_name, GetScriptListByFilterRequest_SexOpt_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AigcGroupClient is the client API for AigcGroup service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AigcGroupClient interface {
	// 群聊配置
	CreateGroupTemplate(ctx context.Context, in *CreateGroupTemplateRequest, opts ...grpc.CallOption) (*CreateGroupTemplateResponse, error)
	UpdateGroupTemplate(ctx context.Context, in *UpdateGroupTemplateRequest, opts ...grpc.CallOption) (*UpdateGroupTemplateResponse, error)
	DeleteGroupTemplate(ctx context.Context, in *DeleteGroupTemplateRequest, opts ...grpc.CallOption) (*DeleteGroupTemplateResponse, error)
	GetGroupTemplateByIds(ctx context.Context, in *GetGroupTemplateByIdsRequest, opts ...grpc.CallOption) (*GetGroupTemplateByIdsResponse, error)
	GetGroupTemplateByPage(ctx context.Context, in *GetGroupTemplateByPageRequest, opts ...grpc.CallOption) (*GetGroupTemplateByPageResponse, error)
	GetTemplateByGroupId(ctx context.Context, in *GetTemplateByGroupIdRequest, opts ...grpc.CallOption) (*GetTemplateByGroupIdResponse, error)
	GetScriptListByFilter(ctx context.Context, in *GetScriptListByFilterRequest, opts ...grpc.CallOption) (*GetScriptListByFilterResponse, error)
	// 创建群组
	CreateGroup(ctx context.Context, in *CreateGroupRequest, opts ...grpc.CallOption) (*CreateGroupResponse, error)
	// 删除群组
	DeleteGroup(ctx context.Context, in *DeleteGroupRequest, opts ...grpc.CallOption) (*DeleteGroupResponse, error)
	// 退出群组
	LeaveGroup(ctx context.Context, in *LeaveGroupRequest, opts ...grpc.CallOption) (*LeaveGroupResponse, error)
	// 获取群组详情
	GetGroupInfo(ctx context.Context, in *GetGroupInfoRequest, opts ...grpc.CallOption) (*GetGroupInfoResponse, error)
	// 获取用户创建的群列表
	GetUserOwnedGroupList(ctx context.Context, in *GetUserOwnedGroupListRequest, opts ...grpc.CallOption) (*GetUserOwnedGroupListResponse, error)
	// 获取用户加入的群列表
	GetUserJoinedGroupList(ctx context.Context, in *GetUserJoinedGroupListRequest, opts ...grpc.CallOption) (*GetUserJoinedGroupListResponse, error)
	// 获取群组成员
	GetGroupMember(ctx context.Context, in *GetGroupMemberRequest, opts ...grpc.CallOption) (*GetGroupMemberResponse, error)
	// 批量获取群组成员
	BatchGetGroupMember(ctx context.Context, in *BatchGetGroupMemberRequest, opts ...grpc.CallOption) (*BatchGetGroupMemberResponse, error)
	// 获取群组成员列表
	GetGroupMemberList(ctx context.Context, in *GetGroupMemberListRequest, opts ...grpc.CallOption) (*GetGroupMemberListResponse, error)
	// 批量获取多个群组成员列表
	BatchGetGroupMemberList(ctx context.Context, in *BatchGetGroupMemberListRequest, opts ...grpc.CallOption) (*BatchGetGroupMemberListResponse, error)
	// 剧本列表HotBanner
	CreateHotBanner(ctx context.Context, in *CreateHotBannerRequest, opts ...grpc.CallOption) (*CreateHotBannerResponse, error)
	UpdateHotBanner(ctx context.Context, in *UpdateHotBannerRequest, opts ...grpc.CallOption) (*UpdateHotBannerResponse, error)
	DeleteHotBanner(ctx context.Context, in *DeleteHotBannerRequest, opts ...grpc.CallOption) (*DeleteHotBannerResponse, error)
	GetAllHotBanner(ctx context.Context, in *GetAllHotBannerRequest, opts ...grpc.CallOption) (*GetAllHotBannerResponse, error)
	GetHotBannerById(ctx context.Context, in *GetHotBannerByIdRequest, opts ...grpc.CallOption) (*GetHotBannerByIdResponse, error)
	ResortBanner(ctx context.Context, in *ResortBannerRequest, opts ...grpc.CallOption) (*ResortBannerResponse, error)
	// 加入匹配
	JoinMatch(ctx context.Context, in *JoinMatchRequest, opts ...grpc.CallOption) (*JoinMatchResponse, error)
	// 取消匹配
	CancelMatch(ctx context.Context, in *CancelMatchRequest, opts ...grpc.CallOption) (*CancelMatchResponse, error)
	// 确认匹配
	ConfirmMatch(ctx context.Context, in *ConfirmMatchRequest, opts ...grpc.CallOption) (*ConfirmMatchResponse, error)
	// 批量随机获取匹配玩家
	BatchGetRandMatchPlayer(ctx context.Context, in *BatchGetRandMatchPlayerRequest, opts ...grpc.CallOption) (*BatchGetRandMatchPlayerResponse, error)
	// 默认用户角色选择
	MultiGroupDefaultUserRoleSelect(ctx context.Context, in *MultiGroupDefaultUserRoleSelectRequest, opts ...grpc.CallOption) (*MultiGroupDefaultUserRoleSelectResponse, error)
	// 获取用户选择角色信息
	GetMultiGroupUserRoleInfo(ctx context.Context, in *GetMultiGroupUserRoleInfoRequest, opts ...grpc.CallOption) (*GetMultiGroupUserRoleInfoResponse, error)
	// 获取活跃群
	GetActiveGroupList(ctx context.Context, in *GetActiveGroupListRequest, opts ...grpc.CallOption) (*GetActiveGroupListResponse, error)
	// 批量获取活跃群
	BatchGetActiveGroup(ctx context.Context, in *BatchGetActiveGroupRequest, opts ...grpc.CallOption) (*BatchGetActiveGroupResponse, error)
	// 扫描群组触发任务
	ScanGroupTriggerTask(ctx context.Context, in *ScanGroupTriggerTaskRequest, opts ...grpc.CallOption) (*ScanGroupTriggerTaskResponse, error)
	// 移除群组触发任务
	RemoveGroupTriggerTask(ctx context.Context, in *RemoveGroupTriggerTaskRequest, opts ...grpc.CallOption) (*RemoveGroupTriggerTaskResponse, error)
	// 获取用户假装打招呼次数
	GetFakeHelloCount(ctx context.Context, in *GetFakeHelloCountRequest, opts ...grpc.CallOption) (*GetFakeHelloCountResponse, error)
	// 增加用户假装打招呼次数
	IncrFakeHelloCount(ctx context.Context, in *IncrFakeHelloCountRequest, opts ...grpc.CallOption) (*IncrFakeHelloCountResponse, error)
	// 查找特殊群聊消息
	PullSpecialMsg(ctx context.Context, in *PullSpecialMsgRequest, opts ...grpc.CallOption) (*PullSpecialMsgResponse, error)
	// 批量获取群聊最后一条特殊消息
	BatGetLastSpecialMsg(ctx context.Context, in *BatGetLastSpecialMsgRequest, opts ...grpc.CallOption) (*BatGetLastSpecialMsgResponse, error)
	// 写入特殊群聊消息
	AddSpecialMsg(ctx context.Context, in *AddSpecialMsgRequest, opts ...grpc.CallOption) (*AddSpecialMsgResponse, error)
	// 检测不发言的群成员
	DetectDumbGroupMember(ctx context.Context, in *DetectDumbGroupMemberRequest, opts ...grpc.CallOption) (*DetectDumbGroupMemberResponse, error)
	// 获取群组最新seq_id
	BatGetGroupLatestSeqId(ctx context.Context, in *BatGetGroupLatestSeqIdRequest, opts ...grpc.CallOption) (*BatGetGroupLatestSeqIdResponse, error)
	// 更新群组最新seq_id
	UpdateGroupLatestSeqId(ctx context.Context, in *UpdateGroupLatestSeqIdRequest, opts ...grpc.CallOption) (*UpdateGroupLatestSeqIdResponse, error)
}

type aigcGroupClient struct {
	cc *grpc.ClientConn
}

func NewAigcGroupClient(cc *grpc.ClientConn) AigcGroupClient {
	return &aigcGroupClient{cc}
}

func (c *aigcGroupClient) CreateGroupTemplate(ctx context.Context, in *CreateGroupTemplateRequest, opts ...grpc.CallOption) (*CreateGroupTemplateResponse, error) {
	out := new(CreateGroupTemplateResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/CreateGroupTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) UpdateGroupTemplate(ctx context.Context, in *UpdateGroupTemplateRequest, opts ...grpc.CallOption) (*UpdateGroupTemplateResponse, error) {
	out := new(UpdateGroupTemplateResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/UpdateGroupTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) DeleteGroupTemplate(ctx context.Context, in *DeleteGroupTemplateRequest, opts ...grpc.CallOption) (*DeleteGroupTemplateResponse, error) {
	out := new(DeleteGroupTemplateResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/DeleteGroupTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) GetGroupTemplateByIds(ctx context.Context, in *GetGroupTemplateByIdsRequest, opts ...grpc.CallOption) (*GetGroupTemplateByIdsResponse, error) {
	out := new(GetGroupTemplateByIdsResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/GetGroupTemplateByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) GetGroupTemplateByPage(ctx context.Context, in *GetGroupTemplateByPageRequest, opts ...grpc.CallOption) (*GetGroupTemplateByPageResponse, error) {
	out := new(GetGroupTemplateByPageResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/GetGroupTemplateByPage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) GetTemplateByGroupId(ctx context.Context, in *GetTemplateByGroupIdRequest, opts ...grpc.CallOption) (*GetTemplateByGroupIdResponse, error) {
	out := new(GetTemplateByGroupIdResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/GetTemplateByGroupId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) GetScriptListByFilter(ctx context.Context, in *GetScriptListByFilterRequest, opts ...grpc.CallOption) (*GetScriptListByFilterResponse, error) {
	out := new(GetScriptListByFilterResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/GetScriptListByFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) CreateGroup(ctx context.Context, in *CreateGroupRequest, opts ...grpc.CallOption) (*CreateGroupResponse, error) {
	out := new(CreateGroupResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/CreateGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) DeleteGroup(ctx context.Context, in *DeleteGroupRequest, opts ...grpc.CallOption) (*DeleteGroupResponse, error) {
	out := new(DeleteGroupResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/DeleteGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) LeaveGroup(ctx context.Context, in *LeaveGroupRequest, opts ...grpc.CallOption) (*LeaveGroupResponse, error) {
	out := new(LeaveGroupResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/LeaveGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) GetGroupInfo(ctx context.Context, in *GetGroupInfoRequest, opts ...grpc.CallOption) (*GetGroupInfoResponse, error) {
	out := new(GetGroupInfoResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/GetGroupInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) GetUserOwnedGroupList(ctx context.Context, in *GetUserOwnedGroupListRequest, opts ...grpc.CallOption) (*GetUserOwnedGroupListResponse, error) {
	out := new(GetUserOwnedGroupListResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/GetUserOwnedGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) GetUserJoinedGroupList(ctx context.Context, in *GetUserJoinedGroupListRequest, opts ...grpc.CallOption) (*GetUserJoinedGroupListResponse, error) {
	out := new(GetUserJoinedGroupListResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/GetUserJoinedGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) GetGroupMember(ctx context.Context, in *GetGroupMemberRequest, opts ...grpc.CallOption) (*GetGroupMemberResponse, error) {
	out := new(GetGroupMemberResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/GetGroupMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) BatchGetGroupMember(ctx context.Context, in *BatchGetGroupMemberRequest, opts ...grpc.CallOption) (*BatchGetGroupMemberResponse, error) {
	out := new(BatchGetGroupMemberResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/BatchGetGroupMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) GetGroupMemberList(ctx context.Context, in *GetGroupMemberListRequest, opts ...grpc.CallOption) (*GetGroupMemberListResponse, error) {
	out := new(GetGroupMemberListResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/GetGroupMemberList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) BatchGetGroupMemberList(ctx context.Context, in *BatchGetGroupMemberListRequest, opts ...grpc.CallOption) (*BatchGetGroupMemberListResponse, error) {
	out := new(BatchGetGroupMemberListResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/BatchGetGroupMemberList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) CreateHotBanner(ctx context.Context, in *CreateHotBannerRequest, opts ...grpc.CallOption) (*CreateHotBannerResponse, error) {
	out := new(CreateHotBannerResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/CreateHotBanner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) UpdateHotBanner(ctx context.Context, in *UpdateHotBannerRequest, opts ...grpc.CallOption) (*UpdateHotBannerResponse, error) {
	out := new(UpdateHotBannerResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/UpdateHotBanner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) DeleteHotBanner(ctx context.Context, in *DeleteHotBannerRequest, opts ...grpc.CallOption) (*DeleteHotBannerResponse, error) {
	out := new(DeleteHotBannerResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/DeleteHotBanner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) GetAllHotBanner(ctx context.Context, in *GetAllHotBannerRequest, opts ...grpc.CallOption) (*GetAllHotBannerResponse, error) {
	out := new(GetAllHotBannerResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/GetAllHotBanner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) GetHotBannerById(ctx context.Context, in *GetHotBannerByIdRequest, opts ...grpc.CallOption) (*GetHotBannerByIdResponse, error) {
	out := new(GetHotBannerByIdResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/GetHotBannerById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) ResortBanner(ctx context.Context, in *ResortBannerRequest, opts ...grpc.CallOption) (*ResortBannerResponse, error) {
	out := new(ResortBannerResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/ResortBanner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) JoinMatch(ctx context.Context, in *JoinMatchRequest, opts ...grpc.CallOption) (*JoinMatchResponse, error) {
	out := new(JoinMatchResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/JoinMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) CancelMatch(ctx context.Context, in *CancelMatchRequest, opts ...grpc.CallOption) (*CancelMatchResponse, error) {
	out := new(CancelMatchResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/CancelMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) ConfirmMatch(ctx context.Context, in *ConfirmMatchRequest, opts ...grpc.CallOption) (*ConfirmMatchResponse, error) {
	out := new(ConfirmMatchResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/ConfirmMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) BatchGetRandMatchPlayer(ctx context.Context, in *BatchGetRandMatchPlayerRequest, opts ...grpc.CallOption) (*BatchGetRandMatchPlayerResponse, error) {
	out := new(BatchGetRandMatchPlayerResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/BatchGetRandMatchPlayer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) MultiGroupDefaultUserRoleSelect(ctx context.Context, in *MultiGroupDefaultUserRoleSelectRequest, opts ...grpc.CallOption) (*MultiGroupDefaultUserRoleSelectResponse, error) {
	out := new(MultiGroupDefaultUserRoleSelectResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/MultiGroupDefaultUserRoleSelect", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) GetMultiGroupUserRoleInfo(ctx context.Context, in *GetMultiGroupUserRoleInfoRequest, opts ...grpc.CallOption) (*GetMultiGroupUserRoleInfoResponse, error) {
	out := new(GetMultiGroupUserRoleInfoResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/GetMultiGroupUserRoleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) GetActiveGroupList(ctx context.Context, in *GetActiveGroupListRequest, opts ...grpc.CallOption) (*GetActiveGroupListResponse, error) {
	out := new(GetActiveGroupListResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/GetActiveGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) BatchGetActiveGroup(ctx context.Context, in *BatchGetActiveGroupRequest, opts ...grpc.CallOption) (*BatchGetActiveGroupResponse, error) {
	out := new(BatchGetActiveGroupResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/BatchGetActiveGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) ScanGroupTriggerTask(ctx context.Context, in *ScanGroupTriggerTaskRequest, opts ...grpc.CallOption) (*ScanGroupTriggerTaskResponse, error) {
	out := new(ScanGroupTriggerTaskResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/ScanGroupTriggerTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) RemoveGroupTriggerTask(ctx context.Context, in *RemoveGroupTriggerTaskRequest, opts ...grpc.CallOption) (*RemoveGroupTriggerTaskResponse, error) {
	out := new(RemoveGroupTriggerTaskResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/RemoveGroupTriggerTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) GetFakeHelloCount(ctx context.Context, in *GetFakeHelloCountRequest, opts ...grpc.CallOption) (*GetFakeHelloCountResponse, error) {
	out := new(GetFakeHelloCountResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/GetFakeHelloCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) IncrFakeHelloCount(ctx context.Context, in *IncrFakeHelloCountRequest, opts ...grpc.CallOption) (*IncrFakeHelloCountResponse, error) {
	out := new(IncrFakeHelloCountResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/IncrFakeHelloCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) PullSpecialMsg(ctx context.Context, in *PullSpecialMsgRequest, opts ...grpc.CallOption) (*PullSpecialMsgResponse, error) {
	out := new(PullSpecialMsgResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/PullSpecialMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) BatGetLastSpecialMsg(ctx context.Context, in *BatGetLastSpecialMsgRequest, opts ...grpc.CallOption) (*BatGetLastSpecialMsgResponse, error) {
	out := new(BatGetLastSpecialMsgResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/BatGetLastSpecialMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) AddSpecialMsg(ctx context.Context, in *AddSpecialMsgRequest, opts ...grpc.CallOption) (*AddSpecialMsgResponse, error) {
	out := new(AddSpecialMsgResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/AddSpecialMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) DetectDumbGroupMember(ctx context.Context, in *DetectDumbGroupMemberRequest, opts ...grpc.CallOption) (*DetectDumbGroupMemberResponse, error) {
	out := new(DetectDumbGroupMemberResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/DetectDumbGroupMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) BatGetGroupLatestSeqId(ctx context.Context, in *BatGetGroupLatestSeqIdRequest, opts ...grpc.CallOption) (*BatGetGroupLatestSeqIdResponse, error) {
	out := new(BatGetGroupLatestSeqIdResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/BatGetGroupLatestSeqId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcGroupClient) UpdateGroupLatestSeqId(ctx context.Context, in *UpdateGroupLatestSeqIdRequest, opts ...grpc.CallOption) (*UpdateGroupLatestSeqIdResponse, error) {
	out := new(UpdateGroupLatestSeqIdResponse)
	err := c.cc.Invoke(ctx, "/aigc_group.AigcGroup/UpdateGroupLatestSeqId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AigcGroupServer is the server API for AigcGroup service.
type AigcGroupServer interface {
	// 群聊配置
	CreateGroupTemplate(context.Context, *CreateGroupTemplateRequest) (*CreateGroupTemplateResponse, error)
	UpdateGroupTemplate(context.Context, *UpdateGroupTemplateRequest) (*UpdateGroupTemplateResponse, error)
	DeleteGroupTemplate(context.Context, *DeleteGroupTemplateRequest) (*DeleteGroupTemplateResponse, error)
	GetGroupTemplateByIds(context.Context, *GetGroupTemplateByIdsRequest) (*GetGroupTemplateByIdsResponse, error)
	GetGroupTemplateByPage(context.Context, *GetGroupTemplateByPageRequest) (*GetGroupTemplateByPageResponse, error)
	GetTemplateByGroupId(context.Context, *GetTemplateByGroupIdRequest) (*GetTemplateByGroupIdResponse, error)
	GetScriptListByFilter(context.Context, *GetScriptListByFilterRequest) (*GetScriptListByFilterResponse, error)
	// 创建群组
	CreateGroup(context.Context, *CreateGroupRequest) (*CreateGroupResponse, error)
	// 删除群组
	DeleteGroup(context.Context, *DeleteGroupRequest) (*DeleteGroupResponse, error)
	// 退出群组
	LeaveGroup(context.Context, *LeaveGroupRequest) (*LeaveGroupResponse, error)
	// 获取群组详情
	GetGroupInfo(context.Context, *GetGroupInfoRequest) (*GetGroupInfoResponse, error)
	// 获取用户创建的群列表
	GetUserOwnedGroupList(context.Context, *GetUserOwnedGroupListRequest) (*GetUserOwnedGroupListResponse, error)
	// 获取用户加入的群列表
	GetUserJoinedGroupList(context.Context, *GetUserJoinedGroupListRequest) (*GetUserJoinedGroupListResponse, error)
	// 获取群组成员
	GetGroupMember(context.Context, *GetGroupMemberRequest) (*GetGroupMemberResponse, error)
	// 批量获取群组成员
	BatchGetGroupMember(context.Context, *BatchGetGroupMemberRequest) (*BatchGetGroupMemberResponse, error)
	// 获取群组成员列表
	GetGroupMemberList(context.Context, *GetGroupMemberListRequest) (*GetGroupMemberListResponse, error)
	// 批量获取多个群组成员列表
	BatchGetGroupMemberList(context.Context, *BatchGetGroupMemberListRequest) (*BatchGetGroupMemberListResponse, error)
	// 剧本列表HotBanner
	CreateHotBanner(context.Context, *CreateHotBannerRequest) (*CreateHotBannerResponse, error)
	UpdateHotBanner(context.Context, *UpdateHotBannerRequest) (*UpdateHotBannerResponse, error)
	DeleteHotBanner(context.Context, *DeleteHotBannerRequest) (*DeleteHotBannerResponse, error)
	GetAllHotBanner(context.Context, *GetAllHotBannerRequest) (*GetAllHotBannerResponse, error)
	GetHotBannerById(context.Context, *GetHotBannerByIdRequest) (*GetHotBannerByIdResponse, error)
	ResortBanner(context.Context, *ResortBannerRequest) (*ResortBannerResponse, error)
	// 加入匹配
	JoinMatch(context.Context, *JoinMatchRequest) (*JoinMatchResponse, error)
	// 取消匹配
	CancelMatch(context.Context, *CancelMatchRequest) (*CancelMatchResponse, error)
	// 确认匹配
	ConfirmMatch(context.Context, *ConfirmMatchRequest) (*ConfirmMatchResponse, error)
	// 批量随机获取匹配玩家
	BatchGetRandMatchPlayer(context.Context, *BatchGetRandMatchPlayerRequest) (*BatchGetRandMatchPlayerResponse, error)
	// 默认用户角色选择
	MultiGroupDefaultUserRoleSelect(context.Context, *MultiGroupDefaultUserRoleSelectRequest) (*MultiGroupDefaultUserRoleSelectResponse, error)
	// 获取用户选择角色信息
	GetMultiGroupUserRoleInfo(context.Context, *GetMultiGroupUserRoleInfoRequest) (*GetMultiGroupUserRoleInfoResponse, error)
	// 获取活跃群
	GetActiveGroupList(context.Context, *GetActiveGroupListRequest) (*GetActiveGroupListResponse, error)
	// 批量获取活跃群
	BatchGetActiveGroup(context.Context, *BatchGetActiveGroupRequest) (*BatchGetActiveGroupResponse, error)
	// 扫描群组触发任务
	ScanGroupTriggerTask(context.Context, *ScanGroupTriggerTaskRequest) (*ScanGroupTriggerTaskResponse, error)
	// 移除群组触发任务
	RemoveGroupTriggerTask(context.Context, *RemoveGroupTriggerTaskRequest) (*RemoveGroupTriggerTaskResponse, error)
	// 获取用户假装打招呼次数
	GetFakeHelloCount(context.Context, *GetFakeHelloCountRequest) (*GetFakeHelloCountResponse, error)
	// 增加用户假装打招呼次数
	IncrFakeHelloCount(context.Context, *IncrFakeHelloCountRequest) (*IncrFakeHelloCountResponse, error)
	// 查找特殊群聊消息
	PullSpecialMsg(context.Context, *PullSpecialMsgRequest) (*PullSpecialMsgResponse, error)
	// 批量获取群聊最后一条特殊消息
	BatGetLastSpecialMsg(context.Context, *BatGetLastSpecialMsgRequest) (*BatGetLastSpecialMsgResponse, error)
	// 写入特殊群聊消息
	AddSpecialMsg(context.Context, *AddSpecialMsgRequest) (*AddSpecialMsgResponse, error)
	// 检测不发言的群成员
	DetectDumbGroupMember(context.Context, *DetectDumbGroupMemberRequest) (*DetectDumbGroupMemberResponse, error)
	// 获取群组最新seq_id
	BatGetGroupLatestSeqId(context.Context, *BatGetGroupLatestSeqIdRequest) (*BatGetGroupLatestSeqIdResponse, error)
	// 更新群组最新seq_id
	UpdateGroupLatestSeqId(context.Context, *UpdateGroupLatestSeqIdRequest) (*UpdateGroupLatestSeqIdResponse, error)
}

func RegisterAigcGroupServer(s *grpc.Server, srv AigcGroupServer) {
	s.RegisterService(&_AigcGroup_serviceDesc, srv)
}

func _AigcGroup_CreateGroupTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGroupTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).CreateGroupTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/CreateGroupTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).CreateGroupTemplate(ctx, req.(*CreateGroupTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_UpdateGroupTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroupTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).UpdateGroupTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/UpdateGroupTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).UpdateGroupTemplate(ctx, req.(*UpdateGroupTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_DeleteGroupTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGroupTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).DeleteGroupTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/DeleteGroupTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).DeleteGroupTemplate(ctx, req.(*DeleteGroupTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_GetGroupTemplateByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupTemplateByIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).GetGroupTemplateByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/GetGroupTemplateByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).GetGroupTemplateByIds(ctx, req.(*GetGroupTemplateByIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_GetGroupTemplateByPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupTemplateByPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).GetGroupTemplateByPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/GetGroupTemplateByPage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).GetGroupTemplateByPage(ctx, req.(*GetGroupTemplateByPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_GetTemplateByGroupId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTemplateByGroupIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).GetTemplateByGroupId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/GetTemplateByGroupId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).GetTemplateByGroupId(ctx, req.(*GetTemplateByGroupIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_GetScriptListByFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScriptListByFilterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).GetScriptListByFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/GetScriptListByFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).GetScriptListByFilter(ctx, req.(*GetScriptListByFilterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_CreateGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).CreateGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/CreateGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).CreateGroup(ctx, req.(*CreateGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_DeleteGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).DeleteGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/DeleteGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).DeleteGroup(ctx, req.(*DeleteGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_LeaveGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LeaveGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).LeaveGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/LeaveGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).LeaveGroup(ctx, req.(*LeaveGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_GetGroupInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).GetGroupInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/GetGroupInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).GetGroupInfo(ctx, req.(*GetGroupInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_GetUserOwnedGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserOwnedGroupListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).GetUserOwnedGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/GetUserOwnedGroupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).GetUserOwnedGroupList(ctx, req.(*GetUserOwnedGroupListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_GetUserJoinedGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserJoinedGroupListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).GetUserJoinedGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/GetUserJoinedGroupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).GetUserJoinedGroupList(ctx, req.(*GetUserJoinedGroupListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_GetGroupMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).GetGroupMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/GetGroupMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).GetGroupMember(ctx, req.(*GetGroupMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_BatchGetGroupMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGroupMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).BatchGetGroupMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/BatchGetGroupMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).BatchGetGroupMember(ctx, req.(*BatchGetGroupMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_GetGroupMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupMemberListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).GetGroupMemberList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/GetGroupMemberList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).GetGroupMemberList(ctx, req.(*GetGroupMemberListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_BatchGetGroupMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGroupMemberListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).BatchGetGroupMemberList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/BatchGetGroupMemberList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).BatchGetGroupMemberList(ctx, req.(*BatchGetGroupMemberListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_CreateHotBanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateHotBannerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).CreateHotBanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/CreateHotBanner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).CreateHotBanner(ctx, req.(*CreateHotBannerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_UpdateHotBanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHotBannerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).UpdateHotBanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/UpdateHotBanner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).UpdateHotBanner(ctx, req.(*UpdateHotBannerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_DeleteHotBanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteHotBannerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).DeleteHotBanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/DeleteHotBanner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).DeleteHotBanner(ctx, req.(*DeleteHotBannerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_GetAllHotBanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllHotBannerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).GetAllHotBanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/GetAllHotBanner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).GetAllHotBanner(ctx, req.(*GetAllHotBannerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_GetHotBannerById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHotBannerByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).GetHotBannerById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/GetHotBannerById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).GetHotBannerById(ctx, req.(*GetHotBannerByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_ResortBanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResortBannerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).ResortBanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/ResortBanner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).ResortBanner(ctx, req.(*ResortBannerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_JoinMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).JoinMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/JoinMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).JoinMatch(ctx, req.(*JoinMatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_CancelMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).CancelMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/CancelMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).CancelMatch(ctx, req.(*CancelMatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_ConfirmMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).ConfirmMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/ConfirmMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).ConfirmMatch(ctx, req.(*ConfirmMatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_BatchGetRandMatchPlayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetRandMatchPlayerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).BatchGetRandMatchPlayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/BatchGetRandMatchPlayer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).BatchGetRandMatchPlayer(ctx, req.(*BatchGetRandMatchPlayerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_MultiGroupDefaultUserRoleSelect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MultiGroupDefaultUserRoleSelectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).MultiGroupDefaultUserRoleSelect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/MultiGroupDefaultUserRoleSelect",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).MultiGroupDefaultUserRoleSelect(ctx, req.(*MultiGroupDefaultUserRoleSelectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_GetMultiGroupUserRoleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiGroupUserRoleInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).GetMultiGroupUserRoleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/GetMultiGroupUserRoleInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).GetMultiGroupUserRoleInfo(ctx, req.(*GetMultiGroupUserRoleInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_GetActiveGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveGroupListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).GetActiveGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/GetActiveGroupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).GetActiveGroupList(ctx, req.(*GetActiveGroupListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_BatchGetActiveGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetActiveGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).BatchGetActiveGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/BatchGetActiveGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).BatchGetActiveGroup(ctx, req.(*BatchGetActiveGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_ScanGroupTriggerTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanGroupTriggerTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).ScanGroupTriggerTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/ScanGroupTriggerTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).ScanGroupTriggerTask(ctx, req.(*ScanGroupTriggerTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_RemoveGroupTriggerTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveGroupTriggerTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).RemoveGroupTriggerTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/RemoveGroupTriggerTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).RemoveGroupTriggerTask(ctx, req.(*RemoveGroupTriggerTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_GetFakeHelloCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFakeHelloCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).GetFakeHelloCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/GetFakeHelloCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).GetFakeHelloCount(ctx, req.(*GetFakeHelloCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_IncrFakeHelloCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncrFakeHelloCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).IncrFakeHelloCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/IncrFakeHelloCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).IncrFakeHelloCount(ctx, req.(*IncrFakeHelloCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_PullSpecialMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PullSpecialMsgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).PullSpecialMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/PullSpecialMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).PullSpecialMsg(ctx, req.(*PullSpecialMsgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_BatGetLastSpecialMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetLastSpecialMsgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).BatGetLastSpecialMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/BatGetLastSpecialMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).BatGetLastSpecialMsg(ctx, req.(*BatGetLastSpecialMsgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_AddSpecialMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSpecialMsgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).AddSpecialMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/AddSpecialMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).AddSpecialMsg(ctx, req.(*AddSpecialMsgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_DetectDumbGroupMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetectDumbGroupMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).DetectDumbGroupMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/DetectDumbGroupMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).DetectDumbGroupMember(ctx, req.(*DetectDumbGroupMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_BatGetGroupLatestSeqId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetGroupLatestSeqIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).BatGetGroupLatestSeqId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/BatGetGroupLatestSeqId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).BatGetGroupLatestSeqId(ctx, req.(*BatGetGroupLatestSeqIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcGroup_UpdateGroupLatestSeqId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroupLatestSeqIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcGroupServer).UpdateGroupLatestSeqId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_group.AigcGroup/UpdateGroupLatestSeqId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcGroupServer).UpdateGroupLatestSeqId(ctx, req.(*UpdateGroupLatestSeqIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _AigcGroup_serviceDesc = grpc.ServiceDesc{
	ServiceName: "aigc_group.AigcGroup",
	HandlerType: (*AigcGroupServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateGroupTemplate",
			Handler:    _AigcGroup_CreateGroupTemplate_Handler,
		},
		{
			MethodName: "UpdateGroupTemplate",
			Handler:    _AigcGroup_UpdateGroupTemplate_Handler,
		},
		{
			MethodName: "DeleteGroupTemplate",
			Handler:    _AigcGroup_DeleteGroupTemplate_Handler,
		},
		{
			MethodName: "GetGroupTemplateByIds",
			Handler:    _AigcGroup_GetGroupTemplateByIds_Handler,
		},
		{
			MethodName: "GetGroupTemplateByPage",
			Handler:    _AigcGroup_GetGroupTemplateByPage_Handler,
		},
		{
			MethodName: "GetTemplateByGroupId",
			Handler:    _AigcGroup_GetTemplateByGroupId_Handler,
		},
		{
			MethodName: "GetScriptListByFilter",
			Handler:    _AigcGroup_GetScriptListByFilter_Handler,
		},
		{
			MethodName: "CreateGroup",
			Handler:    _AigcGroup_CreateGroup_Handler,
		},
		{
			MethodName: "DeleteGroup",
			Handler:    _AigcGroup_DeleteGroup_Handler,
		},
		{
			MethodName: "LeaveGroup",
			Handler:    _AigcGroup_LeaveGroup_Handler,
		},
		{
			MethodName: "GetGroupInfo",
			Handler:    _AigcGroup_GetGroupInfo_Handler,
		},
		{
			MethodName: "GetUserOwnedGroupList",
			Handler:    _AigcGroup_GetUserOwnedGroupList_Handler,
		},
		{
			MethodName: "GetUserJoinedGroupList",
			Handler:    _AigcGroup_GetUserJoinedGroupList_Handler,
		},
		{
			MethodName: "GetGroupMember",
			Handler:    _AigcGroup_GetGroupMember_Handler,
		},
		{
			MethodName: "BatchGetGroupMember",
			Handler:    _AigcGroup_BatchGetGroupMember_Handler,
		},
		{
			MethodName: "GetGroupMemberList",
			Handler:    _AigcGroup_GetGroupMemberList_Handler,
		},
		{
			MethodName: "BatchGetGroupMemberList",
			Handler:    _AigcGroup_BatchGetGroupMemberList_Handler,
		},
		{
			MethodName: "CreateHotBanner",
			Handler:    _AigcGroup_CreateHotBanner_Handler,
		},
		{
			MethodName: "UpdateHotBanner",
			Handler:    _AigcGroup_UpdateHotBanner_Handler,
		},
		{
			MethodName: "DeleteHotBanner",
			Handler:    _AigcGroup_DeleteHotBanner_Handler,
		},
		{
			MethodName: "GetAllHotBanner",
			Handler:    _AigcGroup_GetAllHotBanner_Handler,
		},
		{
			MethodName: "GetHotBannerById",
			Handler:    _AigcGroup_GetHotBannerById_Handler,
		},
		{
			MethodName: "ResortBanner",
			Handler:    _AigcGroup_ResortBanner_Handler,
		},
		{
			MethodName: "JoinMatch",
			Handler:    _AigcGroup_JoinMatch_Handler,
		},
		{
			MethodName: "CancelMatch",
			Handler:    _AigcGroup_CancelMatch_Handler,
		},
		{
			MethodName: "ConfirmMatch",
			Handler:    _AigcGroup_ConfirmMatch_Handler,
		},
		{
			MethodName: "BatchGetRandMatchPlayer",
			Handler:    _AigcGroup_BatchGetRandMatchPlayer_Handler,
		},
		{
			MethodName: "MultiGroupDefaultUserRoleSelect",
			Handler:    _AigcGroup_MultiGroupDefaultUserRoleSelect_Handler,
		},
		{
			MethodName: "GetMultiGroupUserRoleInfo",
			Handler:    _AigcGroup_GetMultiGroupUserRoleInfo_Handler,
		},
		{
			MethodName: "GetActiveGroupList",
			Handler:    _AigcGroup_GetActiveGroupList_Handler,
		},
		{
			MethodName: "BatchGetActiveGroup",
			Handler:    _AigcGroup_BatchGetActiveGroup_Handler,
		},
		{
			MethodName: "ScanGroupTriggerTask",
			Handler:    _AigcGroup_ScanGroupTriggerTask_Handler,
		},
		{
			MethodName: "RemoveGroupTriggerTask",
			Handler:    _AigcGroup_RemoveGroupTriggerTask_Handler,
		},
		{
			MethodName: "GetFakeHelloCount",
			Handler:    _AigcGroup_GetFakeHelloCount_Handler,
		},
		{
			MethodName: "IncrFakeHelloCount",
			Handler:    _AigcGroup_IncrFakeHelloCount_Handler,
		},
		{
			MethodName: "PullSpecialMsg",
			Handler:    _AigcGroup_PullSpecialMsg_Handler,
		},
		{
			MethodName: "BatGetLastSpecialMsg",
			Handler:    _AigcGroup_BatGetLastSpecialMsg_Handler,
		},
		{
			MethodName: "AddSpecialMsg",
			Handler:    _AigcGroup_AddSpecialMsg_Handler,
		},
		{
			MethodName: "DetectDumbGroupMember",
			Handler:    _AigcGroup_DetectDumbGroupMember_Handler,
		},
		{
			MethodName: "BatGetGroupLatestSeqId",
			Handler:    _AigcGroup_BatGetGroupLatestSeqId_Handler,
		},
		{
			MethodName: "UpdateGroupLatestSeqId",
			Handler:    _AigcGroup_UpdateGroupLatestSeqId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/aigc/aigc-group/aigc-group.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/aigc/aigc-group/aigc-group.proto", fileDescriptor_aigc_group_fdb4e3a8f77fbae9)
}

var fileDescriptor_aigc_group_fdb4e3a8f77fbae9 = []byte{
	// 4382 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x3c, 0x5d, 0x6f, 0x1b, 0x49,
	0x72, 0x1e, 0x92, 0xa2, 0xc8, 0xa2, 0x28, 0x51, 0xad, 0x2f, 0x9a, 0x92, 0x6c, 0x79, 0x76, 0x6d,
	0xcb, 0xba, 0xb5, 0xbc, 0xb6, 0xb3, 0xd9, 0x4d, 0x72, 0xde, 0x58, 0x96, 0x65, 0x99, 0xb7, 0x92,
	0x2d, 0x0f, 0xe9, 0xdb, 0x6c, 0x70, 0x97, 0xd9, 0x11, 0xd9, 0xa2, 0x27, 0x1a, 0x72, 0xa8, 0x99,
	0xa1, 0x57, 0x0a, 0x70, 0x40, 0x90, 0x00, 0x17, 0x20, 0xc1, 0x21, 0xc8, 0x1f, 0xc8, 0x5b, 0x5e,
	0xf3, 0x90, 0x00, 0x87, 0xbc, 0x24, 0x2f, 0x79, 0x09, 0x2e, 0x40, 0x7e, 0x43, 0x7e, 0x41, 0x90,
	0x1f, 0x90, 0x04, 0x41, 0x7f, 0x0c, 0xa7, 0x7b, 0xa6, 0x67, 0x28, 0xf9, 0xb2, 0x0f, 0x79, 0x31,
	0x38, 0xdd, 0xd5, 0x55, 0xd5, 0xf5, 0xd5, 0xd5, 0x55, 0x2d, 0xc3, 0x83, 0x20, 0x78, 0x70, 0x36,
	0xb2, 0x3b, 0xa7, 0xbe, 0xed, 0xbc, 0xc7, 0xde, 0x03, 0xcb, 0xee, 0x75, 0xe8, 0x3f, 0xf7, 0x7b,
	0x9e, 0x3b, 0x1a, 0x0a, 0x3f, 0xb7, 0x87, 0x9e, 0x1b, 0xb8, 0x08, 0xc8, 0x88, 0x49, 0x47, 0xf4,
	0x5f, 0x16, 0xa1, 0xba, 0x4f, 0x7e, 0xb5, 0x71, 0x7f, 0xe8, 0x58, 0x01, 0x46, 0xb3, 0x90, 0xb3,
	0xbb, 0x75, 0x6d, 0x43, 0xdb, 0xac, 0x1a, 0x39, 0xbb, 0x8b, 0x10, 0x14, 0x06, 0x56, 0x1f, 0xd7,
	0x73, 0x1b, 0xda, 0x66, 0xd9, 0xa0, 0xbf, 0xd1, 0x1a, 0x94, 0x3b, 0xef, 0x2c, 0xcf, 0xea, 0x04,
	0xd8, 0xab, 0xe7, 0xe9, 0x44, 0x34, 0x40, 0x56, 0x04, 0x56, 0xcf, 0xaf, 0x17, 0x36, 0xf2, 0x64,
	0x05, 0xf9, 0x8d, 0x96, 0xa1, 0x68, 0xbd, 0xb7, 0x02, 0xcb, 0xab, 0x4f, 0x51, 0x70, 0xfe, 0x85,
	0x6a, 0x90, 0xf7, 0xf1, 0x79, 0xbd, 0xb8, 0xa1, 0x6d, 0x4e, 0x19, 0xe4, 0x27, 0xda, 0x86, 0x85,
	0xce, 0x3b, 0x2b, 0x30, 0x8f, 0xad, 0xce, 0x29, 0xe1, 0x71, 0xd0, 0x35, 0xed, 0x7e, 0xaf, 0x3e,
	0x4d, 0x97, 0xcd, 0x93, 0xa9, 0x67, 0xe3, 0x99, 0x66, 0xbf, 0x47, 0xe0, 0xdf, 0xb9, 0x7d, 0x1c,
	0x87, 0x2f, 0x31, 0x78, 0x32, 0x25, 0xc3, 0xaf, 0x03, 0xd0, 0xad, 0x9b, 0x76, 0xc7, 0x1d, 0xd4,
	0xcb, 0x8c, 0x79, 0x3a, 0xd2, 0xec, 0xb8, 0x03, 0x54, 0x87, 0x69, 0x7c, 0x3e, 0x74, 0x7d, 0xdc,
	0xad, 0xc3, 0x86, 0xb6, 0x59, 0x32, 0xc2, 0x4f, 0xb2, 0xd0, 0x1e, 0xf8, 0xd8, 0x0b, 0xcc, 0xa1,
	0xeb, 0xd7, 0x2b, 0x54, 0x40, 0x65, 0x36, 0x72, 0xe4, 0xfa, 0xe8, 0x26, 0x54, 0x3a, 0xae, 0x37,
	0xc0, 0x1e, 0x43, 0x3c, 0x43, 0x11, 0x03, 0x1b, 0xa2, 0x98, 0x09, 0x80, 0x15, 0xe0, 0x9e, 0xeb,
	0x5d, 0x98, 0x76, 0xb7, 0x5e, 0xe5, 0x00, 0x7c, 0xa8, 0xd9, 0x45, 0x77, 0x60, 0xae, 0xe3, 0x0e,
	0x4e, 0xec, 0x9e, 0xe9, 0xd8, 0xa7, 0xd8, 0x1c, 0x8c, 0xfa, 0xf5, 0x59, 0x2a, 0x97, 0x2a, 0x1b,
	0x3e, 0xb0, 0x4f, 0xf1, 0xab, 0x51, 0x1f, 0x5d, 0x87, 0x92, 0xe7, 0x3a, 0xd8, 0xb4, 0xbb, 0x7e,
	0x7d, 0x6e, 0x23, 0xbf, 0x59, 0x35, 0xa6, 0xc9, 0x77, 0xb3, 0xeb, 0xa3, 0x1b, 0x50, 0xb1, 0xfb,
	0x66, 0x60, 0x1d, 0x9b, 0x54, 0x03, 0x35, 0xb6, 0x3b, 0xbb, 0xdf, 0xb6, 0x8e, 0xdb, 0x44, 0x0d,
	0xbf, 0x11, 0x6e, 0x3e, 0xb8, 0x18, 0xe2, 0xfa, 0xfc, 0x86, 0xb6, 0x39, 0xfb, 0x68, 0x69, 0x3b,
	0xb2, 0x87, 0x6d, 0x66, 0x0b, 0x17, 0x43, 0xcc, 0x65, 0x42, 0x7e, 0xa2, 0xcf, 0xa1, 0xe2, 0x77,
	0x3c, 0x7b, 0x18, 0x98, 0xf6, 0xe0, 0xc4, 0xad, 0xa3, 0x0d, 0x6d, 0xb3, 0xf2, 0x68, 0x59, 0x5c,
	0xd6, 0xa2, 0xd3, 0xcd, 0xc1, 0x89, 0x6b, 0x80, 0x3f, 0xfe, 0x8d, 0x6e, 0xc1, 0x8c, 0x3f, 0xb2,
	0x03, 0xeb, 0xd8, 0xc1, 0x26, 0x51, 0xf3, 0xc2, 0x46, 0x7e, 0x73, 0xca, 0xa8, 0x84, 0x63, 0x2d,
	0x7c, 0x8e, 0x5e, 0xc2, 0x7c, 0x17, 0x9f, 0x58, 0x23, 0x27, 0x30, 0x87, 0x9e, 0xeb, 0xb8, 0xbd,
	0x11, 0xf6, 0xeb, 0x8b, 0x1b, 0xf9, 0xcd, 0xca, 0xa3, 0x55, 0x91, 0xc2, 0x4e, 0x93, 0xb2, 0x76,
	0xc4, 0x61, 0x8c, 0x1a, 0x5f, 0x15, 0x0e, 0xf8, 0x68, 0x0b, 0xe6, 0xa9, 0x1b, 0x98, 0xfe, 0x10,
	0x5b, 0xa7, 0x66, 0x80, 0xcf, 0x03, 0xbf, 0xbe, 0x44, 0x6d, 0x70, 0x8e, 0x4e, 0xb4, 0xc8, 0x78,
	0x9b, 0x0c, 0xa3, 0xa7, 0x30, 0xeb, 0x60, 0xeb, 0x3d, 0x36, 0xfd, 0xc0, 0x23, 0x0a, 0xb8, 0xa8,
	0x2f, 0x53, 0x59, 0x5c, 0x17, 0x49, 0x1e, 0x10, 0x88, 0x16, 0x07, 0x30, 0xaa, 0x8e, 0xf8, 0xa9,
	0xff, 0xbd, 0x06, 0xa5, 0x23, 0xc7, 0xba, 0x30, 0x5c, 0x27, 0xe9, 0x33, 0x91, 0xb5, 0xe7, 0x24,
	0x6b, 0x0f, 0x7d, 0x29, 0x2f, 0xf8, 0x52, 0x5c, 0x46, 0x85, 0xa4, 0x8c, 0x24, 0x77, 0x9b, 0x8a,
	0xbb, 0x9b, 0x72, 0xdf, 0x45, 0xe5, 0xbe, 0xf5, 0xaf, 0x61, 0x2e, 0x26, 0x48, 0xea, 0xad, 0xf8,
	0x3c, 0xa0, 0xdc, 0x13, 0x6f, 0xc5, 0xe7, 0x01, 0x5a, 0x84, 0x29, 0x6b, 0xd4, 0xb5, 0x5d, 0xce,
	0x3e, 0xfb, 0x40, 0x0d, 0x28, 0x0d, 0x3d, 0xdb, 0xf5, 0xec, 0xe0, 0x82, 0xee, 0xa0, 0x6a, 0x8c,
	0xbf, 0xf5, 0x7f, 0xcf, 0x01, 0x44, 0x46, 0x80, 0x1e, 0x03, 0x0c, 0x1d, 0xeb, 0xc2, 0x24, 0x76,
	0xe9, 0xd7, 0x35, 0xaa, 0xce, 0x45, 0x51, 0xb6, 0xa1, 0xe8, 0x8c, 0xf2, 0x90, 0xff, 0xf2, 0x89,
	0x27, 0x1f, 0x8f, 0x82, 0xc0, 0x1d, 0x98, 0x5d, 0xdb, 0xa7, 0xcb, 0x29, 0x63, 0x8c, 0x87, 0x79,
	0x36, 0xf5, 0x9c, 0xcd, 0x90, 0xdd, 0xa0, 0x7b, 0x50, 0x13, 0x9c, 0xbe, 0x3f, 0xf2, 0xed, 0x0e,
	0x97, 0xec, 0x5c, 0x34, 0x7e, 0x48, 0x86, 0x89, 0xcb, 0x8c, 0x7c, 0xec, 0x51, 0x9f, 0x2a, 0x50,
	0xd6, 0xa7, 0xc9, 0x37, 0xf7, 0xa6, 0xbe, 0xe5, 0x30, 0x77, 0x9b, 0x62, 0x53, 0xe4, 0x9b, 0x4c,
	0xad, 0x03, 0x9c, 0xe0, 0xf1, 0x64, 0x91, 0x79, 0x3c, 0x1b, 0x21, 0xd3, 0x08, 0x0a, 0xbe, 0xeb,
	0x05, 0x34, 0x34, 0x55, 0x0d, 0xfa, 0x1b, 0xad, 0xc0, 0xb4, 0x35, 0xb8, 0xa0, 0xf0, 0x25, 0x3a,
	0x5c, 0xb4, 0x06, 0x17, 0x04, 0xf8, 0x29, 0xcc, 0xf6, 0xad, 0xa0, 0xf3, 0x2e, 0xb2, 0xb8, 0x72,
	0xd2, 0xe2, 0x0e, 0x09, 0x44, 0x64, 0x71, 0x7d, 0xf1, 0x53, 0xff, 0xab, 0x02, 0x4c, 0x51, 0xd5,
	0x25, 0xcc, 0xed, 0x1e, 0x14, 0xa8, 0x3f, 0xe7, 0xb2, 0xfc, 0x99, 0x82, 0x90, 0x2d, 0x75, 0x3c,
	0x6c, 0x05, 0xb8, 0x6b, 0x5a, 0x01, 0x95, 0x56, 0xde, 0x28, 0xf3, 0x91, 0x9d, 0x00, 0x3d, 0x80,
	0xa2, 0xef, 0x8e, 0xbc, 0x0e, 0xa6, 0x52, 0x9a, 0x7d, 0xb4, 0x92, 0xc0, 0xd5, 0xa2, 0xd3, 0x06,
	0x07, 0x43, 0xab, 0x50, 0x76, 0xbf, 0x23, 0x41, 0x6f, 0x64, 0x77, 0x79, 0x4c, 0x2c, 0xd1, 0x81,
	0xb7, 0x76, 0x97, 0x44, 0xbc, 0x80, 0x1f, 0x2b, 0x24, 0xe2, 0xcd, 0xd0, 0x69, 0x08, 0x87, 0xd4,
	0x11, 0x6f, 0x4e, 0x15, 0xf1, 0xd6, 0x01, 0xfa, 0xb8, 0x7f, 0xcc, 0x15, 0x58, 0x63, 0x8a, 0x60,
	0x23, 0x7c, 0x9a, 0x6a, 0xd7, 0xb1, 0xfb, 0x76, 0x40, 0xa3, 0x5a, 0xd5, 0x28, 0x93, 0x91, 0x03,
	0x32, 0x10, 0x9e, 0x31, 0x4b, 0xd1, 0x19, 0x13, 0xfa, 0xe1, 0xb2, 0xe0, 0x87, 0x08, 0x0a, 0x5d,
	0xec, 0x77, 0xea, 0x2b, 0x6c, 0x8c, 0xfc, 0x16, 0xfc, 0xb8, 0x2e, 0xf9, 0xf1, 0x5d, 0x98, 0x8b,
	0x9d, 0x51, 0xf5, 0xeb, 0x14, 0x60, 0x56, 0x3e, 0x9f, 0xd0, 0x1a, 0x40, 0x14, 0x8f, 0xeb, 0x0d,
	0x0a, 0x53, 0x0a, 0xc3, 0xb1, 0x22, 0x0a, 0xad, 0x5e, 0x31, 0x0a, 0x9d, 0x42, 0x85, 0x6a, 0xe5,
	0x90, 0xca, 0x22, 0x61, 0x18, 0x0f, 0x24, 0xc3, 0x58, 0x4d, 0x28, 0x93, 0x2d, 0x13, 0xcc, 0x63,
	0x15, 0xca, 0x7f, 0xe8, 0xda, 0x03, 0xd1, 0x3a, 0x4a, 0x6c, 0x60, 0x27, 0xd0, 0x7f, 0x0a, 0x35,
	0x6a, 0xa0, 0x06, 0xb6, 0xba, 0x17, 0xaf, 0xdc, 0xc0, 0x3e, 0xb9, 0x20, 0xb2, 0x1d, 0x8d, 0x49,
	0x92, 0x9f, 0x24, 0x76, 0x04, 0xee, 0x29, 0x1e, 0x84, 0xb1, 0x83, 0x7e, 0x90, 0x28, 0x17, 0x78,
	0x76, 0xaf, 0x87, 0x3d, 0x11, 0x77, 0x65, 0x3c, 0xb6, 0x13, 0xe8, 0xc7, 0x1c, 0xfd, 0x0b, 0x22,
	0xb9, 0x54, 0xf4, 0xd7, 0xa1, 0xc4, 0x8f, 0xef, 0x2e, 0xa5, 0x50, 0x35, 0xa6, 0xd9, 0xe1, 0xdd,
	0xbd, 0x0c, 0x8d, 0x87, 0x50, 0xa1, 0x34, 0x48, 0xf8, 0xc1, 0x9e, 0x02, 0x3d, 0xb7, 0x95, 0xdc,
	0xd8, 0x56, 0xf4, 0x7f, 0xd1, 0xa0, 0xb2, 0xd3, 0x09, 0xec, 0xf7, 0x58, 0xed, 0x7c, 0x1f, 0xc3,
	0xac, 0x63, 0xf9, 0x81, 0x69, 0x51, 0x18, 0x42, 0x37, 0x47, 0xe9, 0xce, 0x90, 0x51, 0xb6, 0x70,
	0x27, 0x40, 0x9f, 0xc3, 0x34, 0xb3, 0x57, 0xbf, 0x9e, 0xa7, 0xd1, 0x70, 0x5d, 0x3a, 0xdc, 0x22,
	0xfc, 0x4c, 0x25, 0x46, 0x08, 0x3d, 0xf6, 0xed, 0xc2, 0x64, 0xdf, 0x8e, 0xb9, 0xdb, 0x54, 0xdc,
	0xdd, 0xf4, 0xaf, 0x60, 0x3e, 0x41, 0x49, 0x21, 0x83, 0x4b, 0xed, 0x48, 0xff, 0x12, 0xe6, 0x04,
	0x51, 0x1e, 0xd8, 0x7e, 0x80, 0x7e, 0x00, 0x05, 0xc7, 0xf6, 0x03, 0x1e, 0xef, 0x57, 0x12, 0x91,
	0x8d, 0x81, 0x1a, 0x14, 0x48, 0xff, 0x95, 0x06, 0x35, 0xb6, 0x03, 0xa6, 0x9f, 0xb6, 0xe5, 0x9f,
	0x4a, 0xda, 0xd5, 0x64, 0xed, 0x7e, 0x26, 0xd9, 0xf2, 0xad, 0xa4, 0x20, 0x22, 0x34, 0xdb, 0x91,
	0x50, 0xf4, 0x2e, 0x14, 0x68, 0x0e, 0xb3, 0x08, 0xb5, 0xf6, 0x37, 0x47, 0x7b, 0xe6, 0xdb, 0x57,
	0xad, 0xa3, 0xbd, 0xdd, 0xe6, 0x8b, 0xe6, 0xde, 0xf3, 0xda, 0x35, 0x74, 0x03, 0x1a, 0x74, 0xf4,
	0xf9, 0x5e, 0x7b, 0x6f, 0xb7, 0x6d, 0xee, 0x1b, 0xaf, 0xdf, 0x1e, 0x99, 0x3b, 0xbb, 0xed, 0xe6,
	0x8f, 0x9b, 0xed, 0x6f, 0x6a, 0x1a, 0xba, 0x03, 0x7a, 0x72, 0xfe, 0x70, 0xef, 0xf0, 0xd9, 0x9e,
	0xd1, 0x8a, 0xe0, 0x72, 0xfa, 0x53, 0x98, 0x7f, 0xe1, 0x3a, 0x8e, 0xfb, 0xdd, 0xfe, 0xc8, 0xee,
	0x62, 0xd9, 0x78, 0x73, 0x6a, 0xe3, 0x95, 0xb7, 0xa7, 0xbf, 0x80, 0x15, 0x41, 0x2b, 0x3f, 0xa2,
	0x3e, 0xf7, 0x01, 0x4e, 0xa0, 0x6f, 0x41, 0xe1, 0xad, 0x7f, 0x49, 0xd3, 0xfe, 0x03, 0x68, 0xec,
	0xd2, 0xd0, 0x2f, 0xdd, 0x00, 0x0c, 0x7c, 0x36, 0xc2, 0x7e, 0x40, 0xa2, 0x13, 0xcf, 0x15, 0xf9,
	0x04, 0x45, 0x56, 0x91, 0xa3, 0x93, 0xbc, 0xb2, 0xda, 0x13, 0x3f, 0xf5, 0xfb, 0xb0, 0xaa, 0xc4,
	0xef, 0x0f, 0xdd, 0x81, 0x9f, 0xc8, 0x9a, 0x08, 0x3b, 0x6f, 0x87, 0xdd, 0xef, 0x8f, 0x9d, 0x75,
	0x58, 0x55, 0xe2, 0x67, 0xec, 0xe8, 0x9f, 0x40, 0xe3, 0x39, 0x76, 0x70, 0x0a, 0xf9, 0x38, 0xb3,
	0x5f, 0xc0, 0xaa, 0x12, 0x9a, 0xef, 0x4d, 0xcc, 0xd1, 0x35, 0x29, 0x47, 0xd7, 0x3f, 0x85, 0xb5,
	0x7d, 0x1c, 0x48, 0xcb, 0x9e, 0x5d, 0x34, 0xbb, 0x7e, 0x48, 0xa9, 0x06, 0xf9, 0x68, 0x15, 0xf9,
	0xa9, 0xbf, 0x82, 0xf5, 0x94, 0x15, 0x9c, 0xda, 0x7d, 0xc9, 0xf1, 0x32, 0x24, 0xc2, 0x5c, 0xef,
	0x67, 0x2a, 0x7c, 0x47, 0x56, 0x6f, 0xbc, 0x59, 0x04, 0x85, 0xa1, 0xd5, 0x63, 0x12, 0xce, 0x1b,
	0xf4, 0x37, 0xcd, 0x76, 0xec, 0x3f, 0xc2, 0x3c, 0x16, 0xd0, 0xdf, 0xb1, 0xeb, 0x04, 0x09, 0x6c,
	0x97, 0xb8, 0x4e, 0xe8, 0x18, 0x6e, 0xa4, 0x91, 0xff, 0xa0, 0xfd, 0xb0, 0x23, 0x27, 0xb0, 0x1c,
	0xce, 0x1b, 0xfb, 0xd0, 0xff, 0x56, 0x03, 0x24, 0x98, 0x5f, 0xb8, 0x37, 0x29, 0x63, 0xd1, 0xb2,
	0x33, 0x96, 0x5c, 0x22, 0x63, 0x09, 0xc3, 0x71, 0x7e, 0x72, 0x38, 0xbe, 0x6a, 0x2e, 0xa5, 0xdf,
	0x86, 0x05, 0x89, 0xdf, 0x14, 0x37, 0xf9, 0x18, 0x90, 0x60, 0x79, 0x69, 0xf6, 0xb9, 0x04, 0x0b,
	0x12, 0x14, 0x37, 0xf2, 0x36, 0xcc, 0xd3, 0x84, 0x42, 0x5a, 0xfb, 0x31, 0x14, 0x48, 0xb6, 0xc4,
	0x1d, 0xaa, 0x26, 0xf2, 0x49, 0x62, 0x89, 0x41, 0x67, 0xb3, 0x82, 0xce, 0x22, 0x20, 0x11, 0x2b,
	0xa7, 0x75, 0x1b, 0x16, 0x42, 0x3d, 0xd3, 0x9b, 0x61, 0x0a, 0xa7, 0x4f, 0x60, 0x51, 0x06, 0xe3,
	0xfb, 0xbe, 0x0d, 0x05, 0x7a, 0xdd, 0x64, 0x5c, 0xcd, 0x27, 0xa4, 0x67, 0xd0, 0x69, 0xfd, 0x0d,
	0x75, 0x27, 0xc2, 0xe7, 0xeb, 0xef, 0x06, 0xb8, 0x4b, 0xa7, 0xc8, 0x69, 0x24, 0xb8, 0x53, 0x2c,
	0x10, 0x4e, 0x52, 0xb2, 0xfe, 0x82, 0xfa, 0x87, 0x0a, 0x65, 0xc4, 0x9a, 0x60, 0x9f, 0x2a, 0xd6,
	0xa8, 0x9f, 0x3d, 0x1c, 0xe3, 0x61, 0xf1, 0x7c, 0x32, 0x6f, 0xfa, 0x3e, 0xf5, 0x0d, 0xe5, 0x92,
	0xab, 0xd1, 0xfe, 0x85, 0x06, 0x4b, 0xa1, 0x58, 0x79, 0x4e, 0xc1, 0x89, 0x66, 0x9c, 0xb1, 0xab,
	0xc0, 0xb3, 0xea, 0x48, 0x2e, 0x25, 0x36, 0xd0, 0xec, 0xa2, 0x1f, 0x42, 0x85, 0x4f, 0x0a, 0x1e,
	0x90, 0x99, 0x53, 0xf2, 0xa4, 0x9d, 0x3a, 0x7d, 0x13, 0x96, 0xe3, 0xec, 0xf0, 0x0d, 0x3d, 0x80,
	0x22, 0x83, 0xe3, 0x9a, 0x5e, 0x49, 0x41, 0x69, 0x70, 0x30, 0xfd, 0x5f, 0x35, 0x68, 0x3c, 0x23,
	0xf9, 0xc4, 0x95, 0xf7, 0xb7, 0x1f, 0x65, 0x61, 0x39, 0x2a, 0xbe, 0xfb, 0x22, 0xad, 0x74, 0x9c,
	0xdb, 0xb1, 0xac, 0xac, 0xd1, 0x84, 0xe2, 0xff, 0x51, 0xca, 0xad, 0x1f, 0xc1, 0xaa, 0x92, 0x30,
	0x97, 0xce, 0xc3, 0x88, 0x65, 0x45, 0x5a, 0xa5, 0x4a, 0x19, 0xf5, 0xdf, 0x84, 0xeb, 0x32, 0x32,
	0xd1, 0xe4, 0x32, 0x52, 0x90, 0x26, 0x34, 0x54, 0xeb, 0x38, 0x23, 0x19, 0xc9, 0x9d, 0xc8, 0x05,
	0xb3, 0xbe, 0x27, 0x70, 0x43, 0xb1, 0x29, 0x91, 0x8f, 0x55, 0x28, 0x87, 0x7c, 0x84, 0x67, 0x5d,
	0x89, 0x33, 0xe2, 0xeb, 0xcf, 0x61, 0x2e, 0xb6, 0xec, 0x43, 0xe4, 0xf0, 0x9f, 0x1a, 0xdc, 0x4c,
	0xe5, 0x82, 0xef, 0xca, 0x81, 0x79, 0xc6, 0x06, 0x5f, 0x64, 0xf6, 0xad, 0x21, 0x27, 0xf0, 0x74,
	0x82, 0x6d, 0x88, 0x78, 0x44, 0x06, 0xfc, 0x43, 0x6b, 0xb8, 0x37, 0x08, 0xbc, 0x0b, 0x63, 0xae,
	0x27, 0x8f, 0x36, 0x4c, 0x58, 0x54, 0x01, 0x92, 0x38, 0x70, 0x8a, 0x2f, 0xc2, 0x38, 0x70, 0x8a,
	0x2f, 0xd0, 0x43, 0x98, 0x7a, 0x6f, 0x39, 0x23, 0x66, 0x47, 0x95, 0x54, 0x3b, 0xa2, 0x3c, 0x30,
	0xc8, 0xdf, 0xce, 0x7d, 0xa1, 0xe9, 0x7f, 0x93, 0x83, 0xea, 0x4b, 0x37, 0x78, 0x66, 0x0d, 0x06,
	0xd8, 0xa3, 0x95, 0x98, 0xc8, 0x3e, 0xcb, 0xd4, 0x3e, 0x27, 0x9e, 0x70, 0xab, 0x50, 0x0e, 0xdc,
	0xa1, 0x19, 0xd8, 0x81, 0x13, 0x16, 0xaa, 0x4a, 0x81, 0x3b, 0x6c, 0x93, 0x6f, 0x32, 0xe9, 0x8f,
	0x8e, 0xf9, 0x64, 0x81, 0x4d, 0xfa, 0xa3, 0xe3, 0xf1, 0xe4, 0xb1, 0xe5, 0x99, 0x1d, 0xd7, 0x71,
	0xc3, 0x32, 0x55, 0xe9, 0xd8, 0xf2, 0x76, 0xc9, 0x37, 0xb9, 0x9c, 0xf1, 0xe2, 0x0e, 0x9b, 0x2f,
	0xd2, 0xf9, 0x0a, 0x1b, 0x63, 0x20, 0xaa, 0x7a, 0x4a, 0xb4, 0x8c, 0xd1, 0x2c, 0x89, 0xcb, 0x18,
	0xd9, 0x47, 0xb0, 0x74, 0x4c, 0xf7, 0x1b, 0x2f, 0x01, 0xb3, 0xda, 0xee, 0x02, 0x9b, 0x94, 0x8a,
	0xc0, 0xfa, 0x7f, 0xe4, 0x60, 0x99, 0x9d, 0xb5, 0x63, 0x69, 0x85, 0x86, 0xd9, 0x86, 0xb9, 0x77,
	0x2e, 0xb9, 0xda, 0x53, 0x94, 0xc2, 0x09, 0xf4, 0x89, 0xa8, 0x03, 0xf5, 0xe2, 0xed, 0x68, 0xa0,
	0xfa, 0x4e, 0x54, 0x43, 0xe3, 0xbf, 0x35, 0x28, 0x8f, 0x27, 0xe3, 0x4a, 0xd0, 0xb2, 0x95, 0x90,
	0xcb, 0x52, 0x42, 0x3e, 0x4b, 0x09, 0x85, 0x09, 0x4a, 0x98, 0x4a, 0x2a, 0x21, 0x2e, 0xf0, 0xe2,
	0x15, 0x04, 0x3e, 0x9d, 0x2e, 0xf0, 0x7b, 0xb0, 0x92, 0x10, 0x59, 0x22, 0xbf, 0xa1, 0x16, 0xaa,
	0xff, 0x4f, 0x0e, 0x96, 0x59, 0x9e, 0xfe, 0x81, 0xba, 0x51, 0x2f, 0x4e, 0xd7, 0xcd, 0x2f, 0x72,
	0xa2, 0x6e, 0xfe, 0x1f, 0x39, 0x4c, 0x5c, 0x57, 0xd3, 0x57, 0xd0, 0x55, 0x29, 0x5d, 0x57, 0xd7,
	0x61, 0x25, 0x21, 0x42, 0x9e, 0xd2, 0x6d, 0xc2, 0x32, 0xcb, 0x2a, 0x13, 0xaa, 0x89, 0x6b, 0xf1,
	0x3a, 0xac, 0x24, 0x20, 0x39, 0x92, 0x3a, 0x4d, 0x05, 0x76, 0x1c, 0x27, 0x8e, 0x44, 0x7f, 0x09,
	0x2b, 0x89, 0x99, 0xc9, 0x57, 0x02, 0x29, 0xe0, 0xf1, 0x03, 0xe8, 0x1e, 0xc5, 0x34, 0x9e, 0x21,
	0xd7, 0xa5, 0x34, 0x4e, 0x9b, 0x50, 0x4f, 0x82, 0x46, 0x54, 0x05, 0x2b, 0xcb, 0xa2, 0x4a, 0x73,
	0xd1, 0xbb, 0xb0, 0x60, 0x60, 0x12, 0xb7, 0x64, 0xd9, 0x08, 0x37, 0xba, 0x32, 0xbb, 0xd1, 0x2d,
	0xc3, 0xa2, 0x0c, 0xc8, 0x45, 0xf3, 0xd7, 0x1a, 0xd4, 0x48, 0xe2, 0xc7, 0xeb, 0x6c, 0x6c, 0xf9,
	0xef, 0x40, 0x71, 0x48, 0x2b, 0x27, 0x9c, 0x8d, 0x8f, 0x44, 0x36, 0xe2, 0xd0, 0xdb, 0xbc, 0xc8,
	0xc2, 0x97, 0x4c, 0x34, 0xdf, 0xc6, 0x27, 0x50, 0xbc, 0x42, 0x35, 0xec, 0xe7, 0x1a, 0xcc, 0x0b,
	0x24, 0xb9, 0x98, 0x26, 0xc6, 0xb3, 0xcf, 0xa0, 0x34, 0xae, 0x71, 0xe6, 0x26, 0xd5, 0xbd, 0xc7,
	0xa0, 0x52, 0xb2, 0x92, 0x97, 0x93, 0x95, 0x3b, 0x80, 0x76, 0xad, 0x41, 0x07, 0x3b, 0x92, 0xa8,
	0x92, 0x09, 0xf5, 0x12, 0x2c, 0x48, 0x70, 0x5c, 0xd0, 0x4f, 0x60, 0x61, 0xd7, 0x1d, 0x9c, 0xd8,
	0x5e, 0x3f, 0x7b, 0xbd, 0xba, 0x9c, 0x49, 0xf4, 0x27, 0x2f, 0xe7, 0x68, 0xdf, 0x44, 0x79, 0x8f,
	0x61, 0x0d, 0xba, 0x62, 0xd5, 0x2b, 0xca, 0xbf, 0xa8, 0x5c, 0x98, 0x9c, 0x68, 0x61, 0x80, 0x7e,
	0x37, 0x29, 0x29, 0x56, 0xc1, 0x66, 0x4a, 0x62, 0x1f, 0xfa, 0xbf, 0x09, 0x59, 0x4c, 0x02, 0x27,
	0x97, 0xbf, 0x01, 0xd3, 0x4c, 0xdd, 0x61, 0x72, 0xf4, 0x85, 0x2a, 0x77, 0x49, 0x59, 0xcd, 0x2d,
	0xc6, 0x67, 0x39, 0x4b, 0x88, 0xa8, 0xf1, 0x35, 0xcc, 0x88, 0x13, 0x57, 0xcc, 0x51, 0x62, 0xa5,
	0x41, 0x31, 0x47, 0x79, 0x02, 0x25, 0x72, 0xbf, 0xa1, 0xd9, 0x89, 0xf2, 0x72, 0x46, 0x6b, 0xf9,
	0x3d, 0x3c, 0xe8, 0x62, 0x8f, 0x9b, 0x1e, 0x2d, 0xef, 0xef, 0xd3, 0x11, 0xfd, 0x1f, 0x34, 0xb8,
	0x73, 0x38, 0x72, 0x02, 0x9b, 0xa6, 0x41, 0xcf, 0x59, 0x17, 0x90, 0x5e, 0x53, 0x5d, 0x07, 0xb7,
	0xb0, 0x83, 0x3b, 0x97, 0xc8, 0x75, 0xd1, 0x63, 0xde, 0x32, 0x20, 0x6e, 0x1b, 0x5e, 0x06, 0x16,
	0xe3, 0x17, 0x5f, 0xea, 0xd9, 0xb4, 0x91, 0x40, 0x7e, 0xf9, 0xe8, 0x4b, 0xa8, 0x46, 0x66, 0x4e,
	0xc2, 0x42, 0x7e, 0x52, 0x05, 0x6a, 0x66, 0xec, 0x03, 0x24, 0x3c, 0xdc, 0x83, 0xbb, 0x13, 0x39,
	0x1f, 0xdb, 0xe7, 0xc6, 0x3e, 0x0e, 0x22, 0xe8, 0x10, 0x4c, 0xbc, 0x48, 0x67, 0xa4, 0xf2, 0x7f,
	0xa2, 0xc1, 0x2c, 0x59, 0xc2, 0xb0, 0xa6, 0x88, 0x7a, 0x03, 0x66, 0xa8, 0x0c, 0x78, 0xa1, 0x2a,
	0x8c, 0x0d, 0xa3, 0x90, 0x14, 0xad, 0x04, 0x47, 0x10, 0x42, 0xe7, 0x72, 0x26, 0x84, 0x79, 0x65,
	0xf5, 0x31, 0x5a, 0x86, 0x22, 0xd7, 0x56, 0x81, 0x6a, 0x8b, 0x7f, 0xe9, 0xff, 0xa5, 0xc1, 0xad,
	0x8c, 0x4d, 0x70, 0xdb, 0x1d, 0xc1, 0x22, 0xa5, 0xe1, 0x53, 0x56, 0x19, 0xa9, 0x28, 0x09, 0x7f,
	0x2e, 0xc9, 0x76, 0x12, 0xb2, 0xed, 0x68, 0xcf, 0x64, 0x6a, 0x9c, 0x88, 0xcf, 0x8f, 0xe2, 0xe3,
	0x8d, 0x6f, 0x61, 0x59, 0x0d, 0xac, 0x30, 0xf4, 0x4f, 0x65, 0x43, 0x6f, 0xc4, 0xed, 0x24, 0x92,
	0xb2, 0x68, 0xe7, 0x5f, 0xc0, 0xea, 0x3e, 0x0e, 0xa2, 0x0a, 0x17, 0xab, 0x70, 0x74, 0x2f, 0xa1,
	0xbd, 0x6f, 0x69, 0x49, 0x43, 0xb1, 0x92, 0x8b, 0xec, 0xd7, 0x2f, 0x85, 0xfe, 0xb3, 0x46, 0x49,
	0xb0, 0x8e, 0x2d, 0x71, 0xd0, 0x67, 0x17, 0x2f, 0x6c, 0x27, 0x88, 0xc2, 0xd4, 0x0e, 0x8b, 0xfc,
	0x1a, 0x8d, 0xd5, 0x0f, 0x62, 0x4a, 0x48, 0x5d, 0xb6, 0xdd, 0xc2, 0xe7, 0xaf, 0x87, 0x01, 0x6b,
	0xb2, 0x2d, 0x43, 0xb1, 0x33, 0xf2, 0x7c, 0x77, 0xdc, 0x04, 0x67, 0x5f, 0xfa, 0x4b, 0x28, 0x32,
	0x30, 0xb4, 0x00, 0x73, 0xad, 0xbd, 0xdf, 0x8b, 0x95, 0xe4, 0x67, 0x01, 0xc8, 0xe0, 0x8b, 0xbd,
	0xc3, 0x9d, 0x83, 0xbd, 0x9a, 0x86, 0x66, 0xa0, 0x44, 0xbe, 0xe9, 0x57, 0x0e, 0x55, 0x60, 0x9a,
	0x7c, 0xed, 0x1c, 0x1c, 0xd4, 0xf2, 0xfa, 0x05, 0xad, 0xaf, 0xa8, 0xb8, 0xe1, 0x82, 0xfa, 0x1c,
	0xca, 0xa1, 0x88, 0xfc, 0xc9, 0xc5, 0xc4, 0x08, 0x96, 0x44, 0xa1, 0x01, 0x3e, 0x0f, 0x4c, 0x69,
	0x03, 0x40, 0x86, 0x76, 0xd9, 0x26, 0xce, 0xe8, 0x1d, 0x5b, 0xe8, 0xa6, 0x88, 0x77, 0xdb, 0x45,
	0x98, 0x3a, 0xc6, 0x3d, 0x7b, 0xc0, 0xeb, 0xa7, 0xec, 0x83, 0xd8, 0x15, 0x1e, 0x74, 0x79, 0x8d,
	0x92, 0xfc, 0x8c, 0x02, 0x7e, 0x5e, 0x08, 0xf8, 0x44, 0x6e, 0xee, 0xc9, 0x89, 0x8f, 0x03, 0xde,
	0xa9, 0xe6, 0x5f, 0xfc, 0x7a, 0x9e, 0x20, 0x39, 0xf9, 0x7a, 0x2e, 0x2c, 0xe1, 0xd9, 0xd1, 0x67,
	0x51, 0x01, 0x45, 0x9c, 0xe4, 0xec, 0xaf, 0xc0, 0xb4, 0xdd, 0x35, 0xc7, 0xd8, 0xaa, 0x46, 0xd1,
	0xee, 0x12, 0x5a, 0xfa, 0x3f, 0x69, 0x51, 0xad, 0x42, 0x5a, 0xc7, 0x79, 0xf8, 0x0a, 0x8a, 0x94,
	0x62, 0x28, 0xeb, 0xc7, 0xaa, 0x53, 0x48, 0xb1, 0x90, 0xe9, 0x81, 0x1f, 0x40, 0x1c, 0x45, 0xc3,
	0xe0, 0xad, 0xcd, 0xd4, 0xe3, 0xe7, 0xbe, 0xec, 0x95, 0xa9, 0x5b, 0x16, 0x5c, 0xf2, 0x2f, 0x34,
	0x58, 0x6d, 0x75, 0xac, 0x41, 0xbc, 0x61, 0x14, 0xee, 0xfc, 0x4b, 0x28, 0x07, 0x96, 0x7f, 0xca,
	0x0a, 0x5c, 0xda, 0x65, 0x1b, 0x4d, 0x25, 0xb2, 0x86, 0x36, 0x99, 0x96, 0xa1, 0x88, 0xcf, 0x87,
	0xb6, 0x17, 0x56, 0xc9, 0xf9, 0x97, 0x5a, 0xd1, 0xfa, 0x29, 0xac, 0xa9, 0x99, 0x89, 0x7a, 0x08,
	0xef, 0x2c, 0xdf, 0xec, 0xbb, 0x1e, 0x63, 0xa6, 0x64, 0x4c, 0xbf, 0xb3, 0xfc, 0x43, 0xd7, 0xc3,
	0xe8, 0x53, 0xae, 0x6d, 0x76, 0x70, 0xad, 0x65, 0xf1, 0xc8, 0x55, 0xfe, 0xa7, 0x1a, 0xac, 0x1b,
	0xb8, 0xef, 0x72, 0xa9, 0x7c, 0x0f, 0x9b, 0xd7, 0xa1, 0x1a, 0x06, 0x34, 0x73, 0xcc, 0x5c, 0xd5,
	0xa8, 0xf0, 0xa8, 0x46, 0x2d, 0x68, 0x03, 0x6e, 0xa4, 0x31, 0x31, 0xee, 0xc2, 0x90, 0x6c, 0xfc,
	0x85, 0x75, 0x8a, 0x5f, 0x62, 0xc7, 0x71, 0x77, 0xdd, 0xd1, 0x20, 0xa3, 0x5c, 0xfa, 0x90, 0xba,
	0x61, 0x1c, 0x9a, 0xcb, 0x6f, 0x11, 0xa6, 0x3a, 0x64, 0x80, 0x2f, 0x60, 0x1f, 0xfa, 0x7d, 0xb8,
	0xde, 0x1c, 0x74, 0xbc, 0xcb, 0x52, 0x58, 0x83, 0x86, 0x0a, 0x9c, 0x73, 0x7b, 0x00, 0x6b, 0xcf,
	0x71, 0x80, 0x3b, 0xc1, 0xf3, 0x51, 0xff, 0x58, 0x51, 0x8b, 0x8c, 0x0c, 0x42, 0x53, 0x1b, 0x84,
	0x94, 0xea, 0xfd, 0x65, 0x0e, 0xd6, 0x53, 0xd0, 0xf1, 0x2d, 0xfd, 0x28, 0x5e, 0x05, 0xfb, 0x54,
	0xd4, 0x50, 0xe6, 0xda, 0x78, 0x0d, 0x53, 0x32, 0xaf, 0x9c, 0x64, 0x5e, 0x8d, 0x9f, 0x6b, 0xe3,
	0xfa, 0xa6, 0xb2, 0xf9, 0x38, 0xce, 0x60, 0x79, 0x1f, 0x20, 0xcc, 0x60, 0xd3, 0xf3, 0xf5, 0x58,
	0xab, 0xa8, 0x70, 0xb9, 0x97, 0x67, 0xfa, 0x9f, 0x69, 0xb0, 0xb8, 0xd3, 0xed, 0xb6, 0x86, 0xb8,
	0x63, 0x5b, 0xce, 0xa1, 0xdf, 0xbb, 0x44, 0x6a, 0x97, 0x6c, 0xbb, 0x2e, 0x41, 0xd1, 0xc7, 0x67,
	0x11, 0x53, 0x53, 0x3e, 0x3e, 0x63, 0x80, 0x7d, 0xbf, 0x47, 0x79, 0x99, 0x31, 0xc8, 0x4f, 0xfa,
	0x16, 0xc8, 0xef, 0x31, 0x16, 0xc3, 0xb7, 0x40, 0x7e, 0x8f, 0x72, 0xb2, 0x02, 0x4b, 0x31, 0x46,
	0xb8, 0x09, 0xfc, 0x52, 0x83, 0xa5, 0xa3, 0x91, 0xe3, 0xfc, 0x9a, 0x3c, 0xae, 0x42, 0xd9, 0x0f,
	0x2c, 0x2f, 0x30, 0x7d, 0x7c, 0x16, 0xbe, 0xae, 0xa2, 0x03, 0x2d, 0x7c, 0x86, 0x3e, 0x82, 0xea,
	0xf1, 0xc8, 0xb7, 0x07, 0xd8, 0xf7, 0x23, 0xf9, 0x55, 0x8d, 0x99, 0x70, 0x90, 0x77, 0xb8, 0xb9,
	0x4d, 0x4d, 0x89, 0xa7, 0x49, 0x1d, 0xa6, 0x3d, 0xfc, 0x1e, 0x7b, 0x3e, 0x2b, 0xe5, 0x94, 0x8c,
	0xf0, 0x53, 0xdf, 0x82, 0xe5, 0x38, 0xdf, 0xdc, 0xca, 0xb8, 0x60, 0x88, 0x85, 0x31, 0xc1, 0xe8,
	0x67, 0x34, 0xf0, 0xef, 0xe3, 0xe0, 0xc0, 0xf2, 0x83, 0xe4, 0x4e, 0xb3, 0x8a, 0xb9, 0x8a, 0xbd,
	0x26, 0xb6, 0x93, 0x4f, 0x6e, 0x47, 0xff, 0x47, 0x0d, 0xd6, 0xd4, 0x34, 0x39, 0x97, 0x3f, 0x0d,
	0xe3, 0x0d, 0x51, 0x59, 0x94, 0x31, 0xfe, 0x56, 0xec, 0xd0, 0x49, 0x45, 0xc0, 0xeb, 0xa8, 0x7e,
	0x6f, 0x9c, 0x26, 0xb2, 0x50, 0xc5, 0x46, 0x1a, 0x5f, 0xf2, 0xe7, 0x09, 0x02, 0x80, 0xe2, 0x10,
	0x5a, 0x14, 0x0f, 0xa1, 0x19, 0xf1, 0xac, 0xf9, 0x21, 0xac, 0x33, 0xea, 0xec, 0xac, 0x26, 0x69,
	0x05, 0xd1, 0x65, 0x94, 0x00, 0x66, 0x56, 0xc0, 0x7f, 0xa5, 0xd1, 0x9b, 0xa4, 0x72, 0x39, 0xdf,
	0xff, 0x09, 0xd4, 0xd8, 0x7a, 0x66, 0xdb, 0x82, 0x08, 0x9e, 0x24, 0x45, 0x90, 0x86, 0x85, 0x37,
	0x22, 0xc9, 0xd0, 0x58, 0x0c, 0x4c, 0xac, 0xe1, 0x58, 0xe3, 0x29, 0xa0, 0x24, 0xd0, 0x24, 0x51,
	0x54, 0x45, 0x51, 0xbc, 0x81, 0x75, 0xa1, 0xf1, 0xae, 0x10, 0x45, 0x86, 0xa7, 0x44, 0xbe, 0x9b,
	0x13, 0x7c, 0x97, 0x1c, 0x24, 0x69, 0x28, 0xb9, 0x5f, 0xfe, 0x2e, 0xd4, 0xa2, 0x9e, 0xe4, 0x07,
	0xbc, 0xa4, 0xd8, 0xfa, 0x63, 0x0d, 0xca, 0xe3, 0xa0, 0x84, 0x1a, 0xb0, 0xcc, 0x5e, 0x7f, 0x28,
	0x5e, 0x91, 0xc8, 0x73, 0xad, 0xe6, 0xab, 0xfd, 0x83, 0x3d, 0xf3, 0x6d, 0x6b, 0xcf, 0xa8, 0x69,
	0xe8, 0x3a, 0x2c, 0x09, 0x73, 0x87, 0x6f, 0x0f, 0xda, 0x4d, 0x36, 0x95, 0x43, 0x1b, 0xb0, 0xa6,
	0x9c, 0x32, 0x5b, 0xbb, 0x46, 0xf3, 0xa8, 0x5d, 0xcb, 0x6f, 0x39, 0x52, 0x1f, 0x84, 0xf2, 0x71,
	0x0b, 0xd6, 0xc5, 0x57, 0x28, 0x99, 0xec, 0x48, 0x20, 0x8c, 0x1d, 0xe5, 0x9c, 0xf1, 0x9a, 0xe4,
	0xd6, 0x5b, 0xc7, 0x50, 0x95, 0x0a, 0x31, 0xe8, 0x06, 0x34, 0x0e, 0x77, 0xda, 0xbb, 0x2f, 0xcd,
	0x56, 0xdb, 0xd8, 0x69, 0xef, 0xed, 0x7f, 0x13, 0x23, 0xb4, 0x02, 0x0b, 0xb1, 0xf9, 0xa3, 0xd7,
	0xaf, 0x0f, 0x6a, 0x1a, 0xaa, 0xc3, 0x62, 0x6c, 0x82, 0x12, 0xad, 0xe5, 0xb6, 0x7e, 0xc6, 0xb3,
	0x3a, 0xd6, 0xfa, 0x46, 0x6b, 0x50, 0x67, 0xec, 0xb4, 0x5e, 0xbf, 0x35, 0x76, 0xe3, 0x1b, 0x59,
	0x82, 0x79, 0x79, 0x96, 0xed, 0x61, 0x15, 0x56, 0xa4, 0x61, 0x46, 0x8a, 0x92, 0xce, 0x25, 0x30,
	0xb2, 0x49, 0x46, 0x3e, 0xbf, 0xf5, 0x12, 0xaa, 0xd2, 0x7b, 0x3a, 0xb2, 0xc5, 0x83, 0xbd, 0x9d,
	0x1f, 0xef, 0x65, 0x6c, 0x31, 0x36, 0xff, 0xe6, 0x6d, 0xb3, 0x5d, 0xd3, 0xb6, 0x8e, 0x60, 0x36,
	0x0a, 0x29, 0x54, 0x33, 0x1b, 0xb0, 0x46, 0x57, 0xee, 0x1c, 0x98, 0x87, 0xad, 0x7d, 0x95, 0x62,
	0xd6, 0xa0, 0x9e, 0x80, 0x78, 0xfd, 0xea, 0xe0, 0x1b, 0xf3, 0x70, 0xaf, 0xa6, 0x3d, 0xfa, 0xbb,
	0x0d, 0x28, 0xef, 0xd8, 0xbd, 0x0e, 0x7b, 0x66, 0xf6, 0x4e, 0x7a, 0x0c, 0x30, 0x7e, 0x9d, 0x7f,
	0x27, 0xd9, 0x84, 0x50, 0x3d, 0x57, 0x69, 0xdc, 0x9d, 0x08, 0xc7, 0xdd, 0xe4, 0x1a, 0xa1, 0xa4,
	0x78, 0x16, 0x23, 0x53, 0x4a, 0x7f, 0x97, 0x23, 0x53, 0xca, 0x7a, 0x5f, 0x43, 0x29, 0x29, 0xde,
	0xcc, 0xc8, 0x94, 0xd2, 0x9f, 0xe0, 0xc8, 0x94, 0x32, 0x1e, 0xdf, 0xe8, 0xd7, 0xd0, 0x20, 0x6a,
	0x7e, 0x4b, 0x2f, 0x66, 0xd0, 0x66, 0xec, 0x2a, 0x9b, 0xfa, 0x0c, 0xa7, 0x71, 0xef, 0x12, 0x90,
	0x63, 0x7a, 0x67, 0x51, 0x77, 0x5b, 0x7e, 0xd2, 0x82, 0x26, 0xa0, 0x11, 0x5e, 0xdd, 0x34, 0xb6,
	0x2e, 0x03, 0x3a, 0x26, 0x79, 0x4a, 0x9f, 0x4d, 0x24, 0x8a, 0x04, 0xe8, 0x6e, 0x0c, 0x4b, 0x5a,
	0x01, 0xa2, 0xb1, 0x39, 0x19, 0x30, 0x26, 0xcf, 0xe4, 0x4d, 0x3b, 0x21, 0xcf, 0xd4, 0xd2, 0x40,
	0x42, 0x9e, 0xe9, 0xd7, 0x76, 0xfd, 0x1a, 0x7a, 0x05, 0x15, 0xc1, 0x68, 0xd1, 0x8d, 0x14, 0x6b,
	0x0e, 0x71, 0xdf, 0x4c, 0x9d, 0xe7, 0x67, 0xe5, 0x2b, 0xa8, 0x08, 0x06, 0x23, 0xe3, 0x4b, 0x3e,
	0xa6, 0x91, 0xf1, 0x29, 0x9e, 0xd1, 0xa0, 0xaf, 0x00, 0xa2, 0xc3, 0x05, 0xad, 0x27, 0xde, 0xeb,
	0x4a, 0xd8, 0x6e, 0xa4, 0x4d, 0x73, 0x64, 0x6f, 0x60, 0x46, 0x7c, 0x00, 0x83, 0x6e, 0xaa, 0xec,
	0x40, 0x28, 0xfc, 0x35, 0x36, 0xd2, 0x01, 0xc6, 0x6d, 0xed, 0x25, 0xe5, 0x0b, 0x96, 0x84, 0xbe,
	0x52, 0xdf, 0xcd, 0x24, 0xf4, 0x95, 0xf1, 0x1c, 0xc6, 0xa5, 0xd6, 0xaf, 0x78, 0xb4, 0x82, 0x54,
	0x48, 0xd4, 0x6f, 0x61, 0x12, 0xd6, 0x9f, 0xf5, 0x06, 0xe6, 0x6b, 0x98, 0x95, 0x7b, 0xf1, 0xe8,
	0x96, 0x4a, 0x24, 0xd2, 0x5d, 0xac, 0xa1, 0x67, 0x81, 0x8c, 0x73, 0xaa, 0x05, 0x45, 0xa7, 0x5f,
	0x8e, 0x50, 0xe9, 0xcf, 0x44, 0xe4, 0x08, 0x95, 0xf5, 0xaa, 0xa3, 0x03, 0x28, 0xf9, 0x98, 0x00,
	0xdd, 0x4e, 0xe7, 0x50, 0x94, 0xd4, 0x9d, 0x49, 0x60, 0x9c, 0x88, 0x07, 0x2b, 0x29, 0xcf, 0x16,
	0xd0, 0xd6, 0xa5, 0xde, 0x36, 0x30, 0x72, 0x3f, 0xb8, 0xc2, 0x3b, 0x08, 0xf4, 0x13, 0x98, 0x8b,
	0xf5, 0x79, 0x91, 0x3e, 0xb9, 0x6f, 0xde, 0xf8, 0x28, 0x13, 0x66, 0x1c, 0x16, 0x7e, 0x02, 0x73,
	0xb1, 0xce, 0xa4, 0x8c, 0x5d, 0xdd, 0xf9, 0x95, 0xb1, 0xa7, 0xb5, 0x36, 0x29, 0xf6, 0x58, 0xcb,
	0x52, 0xc6, 0xae, 0xee, 0x7c, 0xca, 0xd8, 0xd3, 0x7a, 0x9e, 0x14, 0x7b, 0xac, 0xb7, 0x89, 0xe2,
	0x16, 0xa9, 0x68, 0x89, 0xca, 0xd8, 0x53, 0x9a, 0xa3, 0xfa, 0x35, 0x64, 0x42, 0x2d, 0xde, 0xc4,
	0x44, 0xf1, 0xa5, 0xaa, 0x6e, 0x68, 0xe3, 0xe3, 0x6c, 0xa0, 0x31, 0x81, 0x16, 0xcc, 0x88, 0x1d,
	0x4b, 0x39, 0x48, 0x29, 0x9a, 0x9e, 0x72, 0x90, 0x52, 0x36, 0x3b, 0xaf, 0xa1, 0x97, 0x50, 0x1e,
	0x37, 0x13, 0xd1, 0x5a, 0x56, 0x5b, 0xb3, 0xb1, 0x9e, 0x32, 0x1b, 0x05, 0x78, 0xa1, 0xcd, 0x17,
	0x3b, 0x30, 0x12, 0x7d, 0xc2, 0xd8, 0x81, 0x91, 0xec, 0x0f, 0x92, 0x98, 0x2c, 0x36, 0xf8, 0xe4,
	0xed, 0x2a, 0x3a, 0x87, 0xf2, 0x76, 0x55, 0xbd, 0x41, 0xd1, 0x1d, 0x63, 0x9d, 0x38, 0xb5, 0x3b,
	0xaa, 0x1b, 0x88, 0x6a, 0x77, 0x4c, 0x6b, 0x0c, 0xfe, 0xb9, 0x06, 0x37, 0x27, 0xb4, 0x9c, 0xd0,
	0x23, 0xa9, 0x6f, 0x77, 0xa9, 0xce, 0x5a, 0xe3, 0xf1, 0x95, 0xd6, 0x70, 0x66, 0xce, 0x69, 0xb1,
	0x4e, 0xdd, 0xc1, 0x41, 0x9f, 0x5c, 0xb2, 0xd1, 0xc3, 0xe8, 0xdf, 0xbf, 0x52, 0x5b, 0x88, 0x87,
	0xdb, 0x58, 0xe9, 0x3c, 0x11, 0x6e, 0xd5, 0xd5, 0xfc, 0x44, 0xb8, 0x4d, 0xab, 0xc0, 0x0b, 0x67,
	0x87, 0xf8, 0xf7, 0x22, 0x77, 0x26, 0x16, 0xc1, 0x33, 0xce, 0x0e, 0x55, 0x95, 0xdd, 0x86, 0x45,
	0x55, 0xd9, 0x58, 0x4e, 0xfc, 0x32, 0xaa, 0xdc, 0x72, 0xe2, 0x97, 0x59, 0x81, 0x76, 0x61, 0x59,
	0x5d, 0xae, 0x95, 0x0f, 0xf6, 0xcc, 0xba, 0xb2, 0x7c, 0xb0, 0x67, 0x57, 0x7f, 0xd1, 0xb7, 0x30,
	0x9f, 0xa8, 0xe7, 0xa2, 0x78, 0x88, 0x52, 0x96, 0x6e, 0x1b, 0xb7, 0x27, 0x40, 0x45, 0xa6, 0x90,
	0xac, 0xe7, 0xca, 0xa6, 0x90, 0x5a, 0x1e, 0x96, 0x4d, 0x21, 0xbd, 0x2c, 0x4c, 0xf2, 0x13, 0xb9,
	0xb4, 0x26, 0xe7, 0x27, 0xca, 0x72, 0xa1, 0x9c, 0x9f, 0xa4, 0x54, 0xe6, 0x6c, 0x58, 0x54, 0x95,
	0xb4, 0xd0, 0xdd, 0xc9, 0x45, 0x2f, 0x85, 0xee, 0x33, 0xcb, 0x6b, 0x6d, 0xa8, 0x4a, 0x05, 0x4f,
	0x24, 0x45, 0x38, 0x55, 0x51, 0xb6, 0x71, 0x2b, 0x03, 0x22, 0x4a, 0x4c, 0x95, 0x55, 0x6a, 0x39,
	0x31, 0xcd, 0xaa, 0xa9, 0xcb, 0x89, 0x69, 0x76, 0xb9, 0xdc, 0x85, 0x65, 0x75, 0xf9, 0x4b, 0xb6,
	0xdf, 0xcc, 0x3a, 0x5d, 0x63, 0xeb, 0xf2, 0xd5, 0x34, 0x42, 0x50, 0x5d, 0x96, 0x92, 0x09, 0x66,
	0x56, 0xc3, 0x64, 0x82, 0xd9, 0x55, 0xae, 0x67, 0x8f, 0x7f, 0xff, 0x61, 0xcf, 0x75, 0xac, 0x41,
	0x6f, 0xfb, 0xb3, 0x47, 0x41, 0xb0, 0xdd, 0x71, 0xfb, 0x0f, 0xe8, 0x1f, 0xf9, 0x77, 0x5c, 0xe7,
	0x81, 0x8f, 0xbd, 0xf7, 0x76, 0x07, 0xfb, 0xf1, 0xff, 0x11, 0xe0, 0xb8, 0x48, 0x41, 0x1e, 0xff,
	0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x7b, 0xf7, 0x74, 0xb4, 0x3a, 0x40, 0x00, 0x00,
}
