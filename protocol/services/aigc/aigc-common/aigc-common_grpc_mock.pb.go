// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-common/aigc-common.proto

package aigc_common

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAigcCommonClient is a mock of AigcCommonClient interface.
type MockAigcCommonClient struct {
	ctrl     *gomock.Controller
	recorder *MockAigcCommonClientMockRecorder
}

// MockAigcCommonClientMockRecorder is the mock recorder for MockAigcCommonClient.
type MockAigcCommonClientMockRecorder struct {
	mock *MockAigcCommonClient
}

// NewMockAigcCommonClient creates a new mock instance.
func NewMockAigcCommonClient(ctrl *gomock.Controller) *MockAigcCommonClient {
	mock := &MockAigcCommonClient{ctrl: ctrl}
	mock.recorder = &MockAigcCommonClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcCommonClient) EXPECT() *MockAigcCommonClientMockRecorder {
	return m.recorder
}

// AddBussSentenceCount mocks base method.
func (m *MockAigcCommonClient) AddBussSentenceCount(ctx context.Context, in *AddBussSentenceCountRequest, opts ...grpc.CallOption) (*AddBussSentenceCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddBussSentenceCount", varargs...)
	ret0, _ := ret[0].(*AddBussSentenceCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddBussSentenceCount indicates an expected call of AddBussSentenceCount.
func (mr *MockAigcCommonClientMockRecorder) AddBussSentenceCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBussSentenceCount", reflect.TypeOf((*MockAigcCommonClient)(nil).AddBussSentenceCount), varargs...)
}

// AddExtraCount mocks base method.
func (m *MockAigcCommonClient) AddExtraCount(ctx context.Context, in *AddExtraCountRequest, opts ...grpc.CallOption) (*AddExtraCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddExtraCount", varargs...)
	ret0, _ := ret[0].(*AddExtraCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddExtraCount indicates an expected call of AddExtraCount.
func (mr *MockAigcCommonClientMockRecorder) AddExtraCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddExtraCount", reflect.TypeOf((*MockAigcCommonClient)(nil).AddExtraCount), varargs...)
}

// AddSentenceCount mocks base method.
func (m *MockAigcCommonClient) AddSentenceCount(ctx context.Context, in *AddSentenceCountRequest, opts ...grpc.CallOption) (*AddSentenceCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddSentenceCount", varargs...)
	ret0, _ := ret[0].(*AddSentenceCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddSentenceCount indicates an expected call of AddSentenceCount.
func (mr *MockAigcCommonClientMockRecorder) AddSentenceCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSentenceCount", reflect.TypeOf((*MockAigcCommonClient)(nil).AddSentenceCount), varargs...)
}

// AddToDelayQueue mocks base method.
func (m *MockAigcCommonClient) AddToDelayQueue(ctx context.Context, in *AddToDelayQueueRequest, opts ...grpc.CallOption) (*AddToDelayQueueResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddToDelayQueue", varargs...)
	ret0, _ := ret[0].(*AddToDelayQueueResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddToDelayQueue indicates an expected call of AddToDelayQueue.
func (mr *MockAigcCommonClientMockRecorder) AddToDelayQueue(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddToDelayQueue", reflect.TypeOf((*MockAigcCommonClient)(nil).AddToDelayQueue), varargs...)
}

// Attitude mocks base method.
func (m *MockAigcCommonClient) Attitude(ctx context.Context, in *AttitudeRequest, opts ...grpc.CallOption) (*AttitudeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Attitude", varargs...)
	ret0, _ := ret[0].(*AttitudeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Attitude indicates an expected call of Attitude.
func (mr *MockAigcCommonClientMockRecorder) Attitude(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Attitude", reflect.TypeOf((*MockAigcCommonClient)(nil).Attitude), varargs...)
}

// BatAddBussSentenceCount mocks base method.
func (m *MockAigcCommonClient) BatAddBussSentenceCount(ctx context.Context, in *BatAddBussSentenceCountRequest, opts ...grpc.CallOption) (*BatAddBussSentenceCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatAddBussSentenceCount", varargs...)
	ret0, _ := ret[0].(*BatAddBussSentenceCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatAddBussSentenceCount indicates an expected call of BatAddBussSentenceCount.
func (mr *MockAigcCommonClientMockRecorder) BatAddBussSentenceCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatAddBussSentenceCount", reflect.TypeOf((*MockAigcCommonClient)(nil).BatAddBussSentenceCount), varargs...)
}

// BatGetCurSentenceCount mocks base method.
func (m *MockAigcCommonClient) BatGetCurSentenceCount(ctx context.Context, in *BatGetCurSentenceCountRequest, opts ...grpc.CallOption) (*BatGetCurSentenceCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetCurSentenceCount", varargs...)
	ret0, _ := ret[0].(*BatGetCurSentenceCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetCurSentenceCount indicates an expected call of BatGetCurSentenceCount.
func (mr *MockAigcCommonClientMockRecorder) BatGetCurSentenceCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetCurSentenceCount", reflect.TypeOf((*MockAigcCommonClient)(nil).BatGetCurSentenceCount), varargs...)
}

// ConsumeSentenceCount mocks base method.
func (m *MockAigcCommonClient) ConsumeSentenceCount(ctx context.Context, in *ConsumeSentenceCountRequest, opts ...grpc.CallOption) (*ConsumeSentenceCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConsumeSentenceCount", varargs...)
	ret0, _ := ret[0].(*ConsumeSentenceCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConsumeSentenceCount indicates an expected call of ConsumeSentenceCount.
func (mr *MockAigcCommonClientMockRecorder) ConsumeSentenceCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConsumeSentenceCount", reflect.TypeOf((*MockAigcCommonClient)(nil).ConsumeSentenceCount), varargs...)
}

// DecrBussSentenceCount mocks base method.
func (m *MockAigcCommonClient) DecrBussSentenceCount(ctx context.Context, in *DecrBussSentenceCountRequest, opts ...grpc.CallOption) (*DecrBussSentenceCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DecrBussSentenceCount", varargs...)
	ret0, _ := ret[0].(*DecrBussSentenceCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DecrBussSentenceCount indicates an expected call of DecrBussSentenceCount.
func (mr *MockAigcCommonClientMockRecorder) DecrBussSentenceCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecrBussSentenceCount", reflect.TypeOf((*MockAigcCommonClient)(nil).DecrBussSentenceCount), varargs...)
}

// GetAttitudeCount mocks base method.
func (m *MockAigcCommonClient) GetAttitudeCount(ctx context.Context, in *GetAttitudeCountRequest, opts ...grpc.CallOption) (*GetAttitudeCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAttitudeCount", varargs...)
	ret0, _ := ret[0].(*GetAttitudeCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAttitudeCount indicates an expected call of GetAttitudeCount.
func (mr *MockAigcCommonClientMockRecorder) GetAttitudeCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAttitudeCount", reflect.TypeOf((*MockAigcCommonClient)(nil).GetAttitudeCount), varargs...)
}

// GetExtraCountByRewardTypes mocks base method.
func (m *MockAigcCommonClient) GetExtraCountByRewardTypes(ctx context.Context, in *GetExtraCountByRewardTypesRequest, opts ...grpc.CallOption) (*GetExtraCountByRewardTypesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExtraCountByRewardTypes", varargs...)
	ret0, _ := ret[0].(*GetExtraCountByRewardTypesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExtraCountByRewardTypes indicates an expected call of GetExtraCountByRewardTypes.
func (mr *MockAigcCommonClientMockRecorder) GetExtraCountByRewardTypes(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtraCountByRewardTypes", reflect.TypeOf((*MockAigcCommonClient)(nil).GetExtraCountByRewardTypes), varargs...)
}

// GetSentenceCountMap mocks base method.
func (m *MockAigcCommonClient) GetSentenceCountMap(ctx context.Context, in *GetSentenceCountMapRequest, opts ...grpc.CallOption) (*GetSentenceCountMapResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSentenceCountMap", varargs...)
	ret0, _ := ret[0].(*GetSentenceCountMapResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSentenceCountMap indicates an expected call of GetSentenceCountMap.
func (mr *MockAigcCommonClientMockRecorder) GetSentenceCountMap(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSentenceCountMap", reflect.TypeOf((*MockAigcCommonClient)(nil).GetSentenceCountMap), varargs...)
}

// GetUsedSentenceCount mocks base method.
func (m *MockAigcCommonClient) GetUsedSentenceCount(ctx context.Context, in *GetUsedSentenceCountReq, opts ...grpc.CallOption) (*GetUsedSentenceCountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUsedSentenceCount", varargs...)
	ret0, _ := ret[0].(*GetUsedSentenceCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUsedSentenceCount indicates an expected call of GetUsedSentenceCount.
func (mr *MockAigcCommonClientMockRecorder) GetUsedSentenceCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsedSentenceCount", reflect.TypeOf((*MockAigcCommonClient)(nil).GetUsedSentenceCount), varargs...)
}

// GetUserSentenceCount mocks base method.
func (m *MockAigcCommonClient) GetUserSentenceCount(ctx context.Context, in *GetUserSentenceCountReq, opts ...grpc.CallOption) (*GetUserSentenceCountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserSentenceCount", varargs...)
	ret0, _ := ret[0].(*GetUserSentenceCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSentenceCount indicates an expected call of GetUserSentenceCount.
func (mr *MockAigcCommonClientMockRecorder) GetUserSentenceCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSentenceCount", reflect.TypeOf((*MockAigcCommonClient)(nil).GetUserSentenceCount), varargs...)
}

// HadAttitude mocks base method.
func (m *MockAigcCommonClient) HadAttitude(ctx context.Context, in *HadAttitudeRequest, opts ...grpc.CallOption) (*HadAttitudeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HadAttitude", varargs...)
	ret0, _ := ret[0].(*HadAttitudeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HadAttitude indicates an expected call of HadAttitude.
func (mr *MockAigcCommonClientMockRecorder) HadAttitude(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HadAttitude", reflect.TypeOf((*MockAigcCommonClient)(nil).HadAttitude), varargs...)
}

// MockAigcCommonServer is a mock of AigcCommonServer interface.
type MockAigcCommonServer struct {
	ctrl     *gomock.Controller
	recorder *MockAigcCommonServerMockRecorder
}

// MockAigcCommonServerMockRecorder is the mock recorder for MockAigcCommonServer.
type MockAigcCommonServerMockRecorder struct {
	mock *MockAigcCommonServer
}

// NewMockAigcCommonServer creates a new mock instance.
func NewMockAigcCommonServer(ctrl *gomock.Controller) *MockAigcCommonServer {
	mock := &MockAigcCommonServer{ctrl: ctrl}
	mock.recorder = &MockAigcCommonServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcCommonServer) EXPECT() *MockAigcCommonServerMockRecorder {
	return m.recorder
}

// AddBussSentenceCount mocks base method.
func (m *MockAigcCommonServer) AddBussSentenceCount(ctx context.Context, in *AddBussSentenceCountRequest) (*AddBussSentenceCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddBussSentenceCount", ctx, in)
	ret0, _ := ret[0].(*AddBussSentenceCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddBussSentenceCount indicates an expected call of AddBussSentenceCount.
func (mr *MockAigcCommonServerMockRecorder) AddBussSentenceCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBussSentenceCount", reflect.TypeOf((*MockAigcCommonServer)(nil).AddBussSentenceCount), ctx, in)
}

// AddExtraCount mocks base method.
func (m *MockAigcCommonServer) AddExtraCount(ctx context.Context, in *AddExtraCountRequest) (*AddExtraCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddExtraCount", ctx, in)
	ret0, _ := ret[0].(*AddExtraCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddExtraCount indicates an expected call of AddExtraCount.
func (mr *MockAigcCommonServerMockRecorder) AddExtraCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddExtraCount", reflect.TypeOf((*MockAigcCommonServer)(nil).AddExtraCount), ctx, in)
}

// AddSentenceCount mocks base method.
func (m *MockAigcCommonServer) AddSentenceCount(ctx context.Context, in *AddSentenceCountRequest) (*AddSentenceCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddSentenceCount", ctx, in)
	ret0, _ := ret[0].(*AddSentenceCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddSentenceCount indicates an expected call of AddSentenceCount.
func (mr *MockAigcCommonServerMockRecorder) AddSentenceCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSentenceCount", reflect.TypeOf((*MockAigcCommonServer)(nil).AddSentenceCount), ctx, in)
}

// AddToDelayQueue mocks base method.
func (m *MockAigcCommonServer) AddToDelayQueue(ctx context.Context, in *AddToDelayQueueRequest) (*AddToDelayQueueResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddToDelayQueue", ctx, in)
	ret0, _ := ret[0].(*AddToDelayQueueResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddToDelayQueue indicates an expected call of AddToDelayQueue.
func (mr *MockAigcCommonServerMockRecorder) AddToDelayQueue(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddToDelayQueue", reflect.TypeOf((*MockAigcCommonServer)(nil).AddToDelayQueue), ctx, in)
}

// Attitude mocks base method.
func (m *MockAigcCommonServer) Attitude(ctx context.Context, in *AttitudeRequest) (*AttitudeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Attitude", ctx, in)
	ret0, _ := ret[0].(*AttitudeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Attitude indicates an expected call of Attitude.
func (mr *MockAigcCommonServerMockRecorder) Attitude(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Attitude", reflect.TypeOf((*MockAigcCommonServer)(nil).Attitude), ctx, in)
}

// BatAddBussSentenceCount mocks base method.
func (m *MockAigcCommonServer) BatAddBussSentenceCount(ctx context.Context, in *BatAddBussSentenceCountRequest) (*BatAddBussSentenceCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatAddBussSentenceCount", ctx, in)
	ret0, _ := ret[0].(*BatAddBussSentenceCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatAddBussSentenceCount indicates an expected call of BatAddBussSentenceCount.
func (mr *MockAigcCommonServerMockRecorder) BatAddBussSentenceCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatAddBussSentenceCount", reflect.TypeOf((*MockAigcCommonServer)(nil).BatAddBussSentenceCount), ctx, in)
}

// BatGetCurSentenceCount mocks base method.
func (m *MockAigcCommonServer) BatGetCurSentenceCount(ctx context.Context, in *BatGetCurSentenceCountRequest) (*BatGetCurSentenceCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetCurSentenceCount", ctx, in)
	ret0, _ := ret[0].(*BatGetCurSentenceCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetCurSentenceCount indicates an expected call of BatGetCurSentenceCount.
func (mr *MockAigcCommonServerMockRecorder) BatGetCurSentenceCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetCurSentenceCount", reflect.TypeOf((*MockAigcCommonServer)(nil).BatGetCurSentenceCount), ctx, in)
}

// ConsumeSentenceCount mocks base method.
func (m *MockAigcCommonServer) ConsumeSentenceCount(ctx context.Context, in *ConsumeSentenceCountRequest) (*ConsumeSentenceCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConsumeSentenceCount", ctx, in)
	ret0, _ := ret[0].(*ConsumeSentenceCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConsumeSentenceCount indicates an expected call of ConsumeSentenceCount.
func (mr *MockAigcCommonServerMockRecorder) ConsumeSentenceCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConsumeSentenceCount", reflect.TypeOf((*MockAigcCommonServer)(nil).ConsumeSentenceCount), ctx, in)
}

// DecrBussSentenceCount mocks base method.
func (m *MockAigcCommonServer) DecrBussSentenceCount(ctx context.Context, in *DecrBussSentenceCountRequest) (*DecrBussSentenceCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecrBussSentenceCount", ctx, in)
	ret0, _ := ret[0].(*DecrBussSentenceCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DecrBussSentenceCount indicates an expected call of DecrBussSentenceCount.
func (mr *MockAigcCommonServerMockRecorder) DecrBussSentenceCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecrBussSentenceCount", reflect.TypeOf((*MockAigcCommonServer)(nil).DecrBussSentenceCount), ctx, in)
}

// GetAttitudeCount mocks base method.
func (m *MockAigcCommonServer) GetAttitudeCount(ctx context.Context, in *GetAttitudeCountRequest) (*GetAttitudeCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAttitudeCount", ctx, in)
	ret0, _ := ret[0].(*GetAttitudeCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAttitudeCount indicates an expected call of GetAttitudeCount.
func (mr *MockAigcCommonServerMockRecorder) GetAttitudeCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAttitudeCount", reflect.TypeOf((*MockAigcCommonServer)(nil).GetAttitudeCount), ctx, in)
}

// GetExtraCountByRewardTypes mocks base method.
func (m *MockAigcCommonServer) GetExtraCountByRewardTypes(ctx context.Context, in *GetExtraCountByRewardTypesRequest) (*GetExtraCountByRewardTypesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExtraCountByRewardTypes", ctx, in)
	ret0, _ := ret[0].(*GetExtraCountByRewardTypesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExtraCountByRewardTypes indicates an expected call of GetExtraCountByRewardTypes.
func (mr *MockAigcCommonServerMockRecorder) GetExtraCountByRewardTypes(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtraCountByRewardTypes", reflect.TypeOf((*MockAigcCommonServer)(nil).GetExtraCountByRewardTypes), ctx, in)
}

// GetSentenceCountMap mocks base method.
func (m *MockAigcCommonServer) GetSentenceCountMap(ctx context.Context, in *GetSentenceCountMapRequest) (*GetSentenceCountMapResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSentenceCountMap", ctx, in)
	ret0, _ := ret[0].(*GetSentenceCountMapResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSentenceCountMap indicates an expected call of GetSentenceCountMap.
func (mr *MockAigcCommonServerMockRecorder) GetSentenceCountMap(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSentenceCountMap", reflect.TypeOf((*MockAigcCommonServer)(nil).GetSentenceCountMap), ctx, in)
}

// GetUsedSentenceCount mocks base method.
func (m *MockAigcCommonServer) GetUsedSentenceCount(ctx context.Context, in *GetUsedSentenceCountReq) (*GetUsedSentenceCountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsedSentenceCount", ctx, in)
	ret0, _ := ret[0].(*GetUsedSentenceCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUsedSentenceCount indicates an expected call of GetUsedSentenceCount.
func (mr *MockAigcCommonServerMockRecorder) GetUsedSentenceCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsedSentenceCount", reflect.TypeOf((*MockAigcCommonServer)(nil).GetUsedSentenceCount), ctx, in)
}

// GetUserSentenceCount mocks base method.
func (m *MockAigcCommonServer) GetUserSentenceCount(ctx context.Context, in *GetUserSentenceCountReq) (*GetUserSentenceCountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSentenceCount", ctx, in)
	ret0, _ := ret[0].(*GetUserSentenceCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSentenceCount indicates an expected call of GetUserSentenceCount.
func (mr *MockAigcCommonServerMockRecorder) GetUserSentenceCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSentenceCount", reflect.TypeOf((*MockAigcCommonServer)(nil).GetUserSentenceCount), ctx, in)
}

// HadAttitude mocks base method.
func (m *MockAigcCommonServer) HadAttitude(ctx context.Context, in *HadAttitudeRequest) (*HadAttitudeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HadAttitude", ctx, in)
	ret0, _ := ret[0].(*HadAttitudeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HadAttitude indicates an expected call of HadAttitude.
func (mr *MockAigcCommonServerMockRecorder) HadAttitude(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HadAttitude", reflect.TypeOf((*MockAigcCommonServer)(nil).HadAttitude), ctx, in)
}
