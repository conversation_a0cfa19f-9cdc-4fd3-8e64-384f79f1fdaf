// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-common/event.proto

package aigc_common // import "golang.52tt.com/protocol/services/aigc/aigc-common"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AigcAttitudeEvent_Action int32

const (
	AigcAttitudeEvent_ACTION_INVALID AigcAttitudeEvent_Action = 0
	AigcAttitudeEvent_ACTION_LIKE    AigcAttitudeEvent_Action = 1
	AigcAttitudeEvent_ACTION_UNLIKE  AigcAttitudeEvent_Action = 2
)

var AigcAttitudeEvent_Action_name = map[int32]string{
	0: "ACTION_INVALID",
	1: "ACTION_LIKE",
	2: "ACTION_UNLIKE",
}
var AigcAttitudeEvent_Action_value = map[string]int32{
	"ACTION_INVALID": 0,
	"ACTION_LIKE":    1,
	"ACTION_UNLIKE":  2,
}

func (x AigcAttitudeEvent_Action) String() string {
	return proto.EnumName(AigcAttitudeEvent_Action_name, int32(x))
}
func (AigcAttitudeEvent_Action) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_8c85f205bebe803d, []int{0, 0}
}

type AigcAttitudeEvent_ObjectType int32

const (
	AigcAttitudeEvent_OBJECT_TYPE_UNSPECIFIED    AigcAttitudeEvent_ObjectType = 0
	AigcAttitudeEvent_OBJECT_TYPE_GROUP_TEMPLATE AigcAttitudeEvent_ObjectType = 1
)

var AigcAttitudeEvent_ObjectType_name = map[int32]string{
	0: "OBJECT_TYPE_UNSPECIFIED",
	1: "OBJECT_TYPE_GROUP_TEMPLATE",
}
var AigcAttitudeEvent_ObjectType_value = map[string]int32{
	"OBJECT_TYPE_UNSPECIFIED":    0,
	"OBJECT_TYPE_GROUP_TEMPLATE": 1,
}

func (x AigcAttitudeEvent_ObjectType) String() string {
	return proto.EnumName(AigcAttitudeEvent_ObjectType_name, int32(x))
}
func (AigcAttitudeEvent_ObjectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_8c85f205bebe803d, []int{0, 1}
}

// 点赞事件
type AigcAttitudeEvent struct {
	ObjectType           uint32                   `protobuf:"varint,1,opt,name=object_type,json=objectType,proto3" json:"object_type,omitempty"`
	ObjectId             uint32                   `protobuf:"varint,2,opt,name=object_id,json=objectId,proto3" json:"object_id,omitempty"`
	Action               AigcAttitudeEvent_Action `protobuf:"varint,3,opt,name=action,proto3,enum=aigc_common.AigcAttitudeEvent_Action" json:"action,omitempty"`
	HappenTime           int64                    `protobuf:"varint,4,opt,name=happen_time,json=happenTime,proto3" json:"happen_time,omitempty"`
	UserId               uint32                   `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *AigcAttitudeEvent) Reset()         { *m = AigcAttitudeEvent{} }
func (m *AigcAttitudeEvent) String() string { return proto.CompactTextString(m) }
func (*AigcAttitudeEvent) ProtoMessage()    {}
func (*AigcAttitudeEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_8c85f205bebe803d, []int{0}
}
func (m *AigcAttitudeEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AigcAttitudeEvent.Unmarshal(m, b)
}
func (m *AigcAttitudeEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AigcAttitudeEvent.Marshal(b, m, deterministic)
}
func (dst *AigcAttitudeEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AigcAttitudeEvent.Merge(dst, src)
}
func (m *AigcAttitudeEvent) XXX_Size() int {
	return xxx_messageInfo_AigcAttitudeEvent.Size(m)
}
func (m *AigcAttitudeEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_AigcAttitudeEvent.DiscardUnknown(m)
}

var xxx_messageInfo_AigcAttitudeEvent proto.InternalMessageInfo

func (m *AigcAttitudeEvent) GetObjectType() uint32 {
	if m != nil {
		return m.ObjectType
	}
	return 0
}

func (m *AigcAttitudeEvent) GetObjectId() uint32 {
	if m != nil {
		return m.ObjectId
	}
	return 0
}

func (m *AigcAttitudeEvent) GetAction() AigcAttitudeEvent_Action {
	if m != nil {
		return m.Action
	}
	return AigcAttitudeEvent_ACTION_INVALID
}

func (m *AigcAttitudeEvent) GetHappenTime() int64 {
	if m != nil {
		return m.HappenTime
	}
	return 0
}

func (m *AigcAttitudeEvent) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

// 延时消息事件
type DelayMsgEvent struct {
	PushTo               PushTo   `protobuf:"varint,1,opt,name=push_to,json=pushTo,proto3,enum=aigc_common.PushTo" json:"push_to,omitempty"`
	PushReq              []byte   `protobuf:"bytes,2,opt,name=push_req,json=pushReq,proto3" json:"push_req,omitempty"`
	ShouldSendTime       int64    `protobuf:"varint,3,opt,name=should_send_time,json=shouldSendTime,proto3" json:"should_send_time,omitempty"`
	ReceiveMsg           []byte   `protobuf:"bytes,4,opt,name=receive_msg,json=receiveMsg,proto3" json:"receive_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelayMsgEvent) Reset()         { *m = DelayMsgEvent{} }
func (m *DelayMsgEvent) String() string { return proto.CompactTextString(m) }
func (*DelayMsgEvent) ProtoMessage()    {}
func (*DelayMsgEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_8c85f205bebe803d, []int{1}
}
func (m *DelayMsgEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelayMsgEvent.Unmarshal(m, b)
}
func (m *DelayMsgEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelayMsgEvent.Marshal(b, m, deterministic)
}
func (dst *DelayMsgEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelayMsgEvent.Merge(dst, src)
}
func (m *DelayMsgEvent) XXX_Size() int {
	return xxx_messageInfo_DelayMsgEvent.Size(m)
}
func (m *DelayMsgEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_DelayMsgEvent.DiscardUnknown(m)
}

var xxx_messageInfo_DelayMsgEvent proto.InternalMessageInfo

func (m *DelayMsgEvent) GetPushTo() PushTo {
	if m != nil {
		return m.PushTo
	}
	return PushTo_PUSH_TO_UNSPECIFIED
}

func (m *DelayMsgEvent) GetPushReq() []byte {
	if m != nil {
		return m.PushReq
	}
	return nil
}

func (m *DelayMsgEvent) GetShouldSendTime() int64 {
	if m != nil {
		return m.ShouldSendTime
	}
	return 0
}

func (m *DelayMsgEvent) GetReceiveMsg() []byte {
	if m != nil {
		return m.ReceiveMsg
	}
	return nil
}

func init() {
	proto.RegisterType((*AigcAttitudeEvent)(nil), "aigc_common.AigcAttitudeEvent")
	proto.RegisterType((*DelayMsgEvent)(nil), "aigc_common.DelayMsgEvent")
	proto.RegisterEnum("aigc_common.AigcAttitudeEvent_Action", AigcAttitudeEvent_Action_name, AigcAttitudeEvent_Action_value)
	proto.RegisterEnum("aigc_common.AigcAttitudeEvent_ObjectType", AigcAttitudeEvent_ObjectType_name, AigcAttitudeEvent_ObjectType_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/aigc/aigc-common/event.proto", fileDescriptor_event_8c85f205bebe803d)
}

var fileDescriptor_event_8c85f205bebe803d = []byte{
	// 452 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x92, 0xdd, 0x8e, 0x93, 0x4c,
	0x18, 0xc7, 0x5f, 0xda, 0x57, 0x76, 0x7d, 0xba, 0xad, 0xdd, 0xf1, 0x60, 0xeb, 0x9a, 0xb8, 0x4d,
	0x13, 0x93, 0x26, 0x2a, 0xc4, 0xaa, 0x87, 0x26, 0xb2, 0x5d, 0x34, 0x68, 0x3f, 0x08, 0x4b, 0x4d,
	0xf4, 0x64, 0xc2, 0x0e, 0x4f, 0xe8, 0x28, 0x30, 0x94, 0x19, 0x9a, 0xf4, 0x7a, 0xbc, 0x28, 0x6f,
	0xc7, 0x30, 0x70, 0x50, 0xb3, 0x07, 0x9e, 0xf0, 0xf1, 0x7b, 0x3e, 0xfe, 0xf0, 0xcb, 0xc0, 0x0b,
	0xa5, 0xec, 0x5d, 0xc5, 0xd9, 0x4f, 0xc9, 0xd3, 0x3d, 0x96, 0x76, 0xc4, 0x13, 0xa6, 0x2f, 0xaf,
	0x98, 0xc8, 0x32, 0x91, 0xdb, 0xb8, 0xc7, 0x5c, 0x59, 0x45, 0x29, 0x94, 0x20, 0xbd, 0xba, 0x40,
	0x9b, 0xc2, 0xe5, 0xeb, 0x7f, 0x4d, 0x1e, 0x3d, 0x37, 0xf3, 0x93, 0xdf, 0x1d, 0x38, 0x77, 0x78,
	0xc2, 0x1c, 0xa5, 0xb8, 0xaa, 0x62, 0x74, 0xeb, 0xdd, 0xe4, 0x0a, 0x7a, 0xe2, 0xee, 0x07, 0x32,
	0x45, 0xd5, 0xa1, 0xc0, 0x91, 0x31, 0x36, 0xa6, 0xfd, 0x00, 0x1a, 0x14, 0x1e, 0x0a, 0x24, 0x4f,
	0xe1, 0x61, 0xdb, 0xc0, 0xe3, 0x51, 0x47, 0x97, 0x4f, 0x1b, 0xe0, 0xc5, 0xe4, 0x3d, 0x98, 0x11,
	0x53, 0x5c, 0xe4, 0xa3, 0xee, 0xd8, 0x98, 0x0e, 0x66, 0xcf, 0xad, 0xa3, 0x8f, 0xb4, 0xee, 0xa5,
	0x59, 0x8e, 0x6e, 0x0e, 0xda, 0xa1, 0x3a, 0x7c, 0x1b, 0x15, 0x05, 0xe6, 0x54, 0xf1, 0x0c, 0x47,
	0xff, 0x8f, 0x8d, 0x69, 0x37, 0x80, 0x06, 0x85, 0x3c, 0x43, 0x72, 0x01, 0x27, 0x95, 0xc4, 0xb2,
	0x8e, 0x7e, 0xa0, 0xa3, 0xcd, 0xfa, 0xd5, 0x8b, 0x27, 0x1f, 0xc0, 0x6c, 0x76, 0x11, 0x02, 0x03,
	0x67, 0x1e, 0x7a, 0xeb, 0x15, 0xf5, 0x56, 0x5f, 0x9d, 0x85, 0x77, 0x33, 0xfc, 0x8f, 0x3c, 0x82,
	0x5e, 0xcb, 0x16, 0xde, 0x17, 0x77, 0x68, 0x90, 0x73, 0xe8, 0xb7, 0x60, 0xb3, 0xd2, 0xa8, 0x33,
	0xf1, 0x00, 0xd6, 0xc7, 0x7f, 0x79, 0xb1, 0xbe, 0xfe, 0xec, 0xce, 0x43, 0x1a, 0x7e, 0xf3, 0x5d,
	0xba, 0x59, 0xdd, 0xfa, 0xee, 0xdc, 0xfb, 0xe8, 0xb9, 0xf5, 0xba, 0x67, 0x70, 0x79, 0x5c, 0xfc,
	0x14, 0xac, 0x37, 0x3e, 0x0d, 0xdd, 0xa5, 0xbf, 0x70, 0x42, 0x77, 0x68, 0x4c, 0x7e, 0x19, 0xd0,
	0xbf, 0xc1, 0x34, 0x3a, 0x2c, 0x65, 0xd2, 0x58, 0x7d, 0x09, 0x27, 0x45, 0x25, 0xb7, 0x54, 0x09,
	0x6d, 0x74, 0x30, 0x7b, 0xfc, 0x97, 0x18, 0xbf, 0x92, 0xdb, 0x50, 0x04, 0x66, 0xa1, 0xef, 0xe4,
	0x09, 0x9c, 0xea, 0xee, 0x12, 0x77, 0xda, 0xf0, 0x59, 0xa0, 0xa7, 0x03, 0xdc, 0x91, 0x29, 0x0c,
	0xe5, 0x56, 0x54, 0x69, 0x4c, 0x25, 0xe6, 0x71, 0xa3, 0xa9, 0xab, 0x35, 0x0d, 0x1a, 0x7e, 0x8b,
	0x79, 0xac, 0x55, 0x5d, 0x41, 0xaf, 0x44, 0x86, 0x7c, 0x8f, 0x34, 0x93, 0x89, 0x76, 0x79, 0x16,
	0x40, 0x8b, 0x96, 0x32, 0xb9, 0x7e, 0xfb, 0x7d, 0x96, 0x88, 0x34, 0xca, 0x13, 0xeb, 0xdd, 0x4c,
	0x29, 0x8b, 0x89, 0xcc, 0xd6, 0x07, 0x83, 0x89, 0xd4, 0x96, 0x58, 0xee, 0x39, 0x43, 0x79, 0xef,
	0x1c, 0xdd, 0x99, 0xba, 0xe7, 0xcd, 0x9f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xf9, 0xc7, 0x3f, 0x62,
	0xab, 0x02, 0x00, 0x00,
}
