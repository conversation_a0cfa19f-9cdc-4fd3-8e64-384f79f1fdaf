// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-account-middle/aigc-account-middle.proto

package aigc_account_middle // import "golang.52tt.com/protocol/services/aigc/aigc-account-middle"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import aigc_account "golang.52tt.com/protocol/services/aigc/aigc-account"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AIAccountInfo struct {
	// ID
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 密码, 运营后台的请求才返回
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	// 提示词ID, 为空则AI不会回复用户
	PromptId uint32 `protobuf:"varint,4,opt,name=prompt_id,json=promptId,proto3" json:"prompt_id,omitempty"`
	// 音色ID
	TimbreId uint32 `protobuf:"varint,5,opt,name=timbre_id,json=timbreId,proto3" json:"timbre_id,omitempty"`
	// 关联角色ID
	RoleId uint32 `protobuf:"varint,6,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 开场白
	Prologue string `protobuf:"bytes,7,opt,name=prologue,proto3" json:"prologue,omitempty"`
	// 创建时间
	CreateTime int64 `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime int64 `protobuf:"varint,9,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// ttid
	Ttid string `protobuf:"bytes,10,opt,name=ttid,proto3" json:"ttid,omitempty"`
	// 绑定手机号
	Phone string `protobuf:"bytes,11,opt,name=phone,proto3" json:"phone,omitempty"`
	// 头像
	Avatar string `protobuf:"bytes,12,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 昵称
	Nickname string `protobuf:"bytes,13,opt,name=nickname,proto3" json:"nickname,omitempty"`
	// 性别 0:女 1:男
	Sex uint32 `protobuf:"varint,14,opt,name=sex,proto3" json:"sex,omitempty"`
	// 生日, 格式: 2006-01-02
	Birthday string `protobuf:"bytes,15,opt,name=birthday,proto3" json:"birthday,omitempty"`
	// 个性签名
	Signature string `protobuf:"bytes,16,opt,name=signature,proto3" json:"signature,omitempty"`
	// 相册
	PhotoImgUrls []string `protobuf:"bytes,17,rep,name=photo_img_urls,json=photoImgUrls,proto3" json:"photo_img_urls,omitempty"`
	// 账号经验等级
	ExpLevel uint32 `protobuf:"varint,18,opt,name=exp_level,json=expLevel,proto3" json:"exp_level,omitempty"`
	// ip归属地省份
	IpLocation string `protobuf:"bytes,19,opt,name=ip_location,json=ipLocation,proto3" json:"ip_location,omitempty"`
	// 标识
	Identity string `protobuf:"bytes,20,opt,name=identity,proto3" json:"identity,omitempty"`
	// 说明
	Desc string `protobuf:"bytes,21,opt,name=desc,proto3" json:"desc,omitempty"`
	// 排序
	Sort uint32 `protobuf:"varint,22,opt,name=sort,proto3" json:"sort,omitempty"`
	// 账号标签
	AccountTags []string `protobuf:"bytes,23,rep,name=account_tags,json=accountTags,proto3" json:"account_tags,omitempty"`
	// 是否展示在扩列墙
	IsShowInChatCardWall bool     `protobuf:"varint,24,opt,name=is_show_in_chat_card_wall,json=isShowInChatCardWall,proto3" json:"is_show_in_chat_card_wall,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIAccountInfo) Reset()         { *m = AIAccountInfo{} }
func (m *AIAccountInfo) String() string { return proto.CompactTextString(m) }
func (*AIAccountInfo) ProtoMessage()    {}
func (*AIAccountInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{0}
}
func (m *AIAccountInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIAccountInfo.Unmarshal(m, b)
}
func (m *AIAccountInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIAccountInfo.Marshal(b, m, deterministic)
}
func (dst *AIAccountInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIAccountInfo.Merge(dst, src)
}
func (m *AIAccountInfo) XXX_Size() int {
	return xxx_messageInfo_AIAccountInfo.Size(m)
}
func (m *AIAccountInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AIAccountInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AIAccountInfo proto.InternalMessageInfo

func (m *AIAccountInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AIAccountInfo) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

func (m *AIAccountInfo) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *AIAccountInfo) GetPromptId() uint32 {
	if m != nil {
		return m.PromptId
	}
	return 0
}

func (m *AIAccountInfo) GetTimbreId() uint32 {
	if m != nil {
		return m.TimbreId
	}
	return 0
}

func (m *AIAccountInfo) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *AIAccountInfo) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *AIAccountInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *AIAccountInfo) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *AIAccountInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *AIAccountInfo) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *AIAccountInfo) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *AIAccountInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *AIAccountInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *AIAccountInfo) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *AIAccountInfo) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

func (m *AIAccountInfo) GetPhotoImgUrls() []string {
	if m != nil {
		return m.PhotoImgUrls
	}
	return nil
}

func (m *AIAccountInfo) GetExpLevel() uint32 {
	if m != nil {
		return m.ExpLevel
	}
	return 0
}

func (m *AIAccountInfo) GetIpLocation() string {
	if m != nil {
		return m.IpLocation
	}
	return ""
}

func (m *AIAccountInfo) GetIdentity() string {
	if m != nil {
		return m.Identity
	}
	return ""
}

func (m *AIAccountInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *AIAccountInfo) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *AIAccountInfo) GetAccountTags() []string {
	if m != nil {
		return m.AccountTags
	}
	return nil
}

func (m *AIAccountInfo) GetIsShowInChatCardWall() bool {
	if m != nil {
		return m.IsShowInChatCardWall
	}
	return false
}

type CreateAIAccountRequest struct {
	Account              *CreateAIAccountRequest_Account `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *CreateAIAccountRequest) Reset()         { *m = CreateAIAccountRequest{} }
func (m *CreateAIAccountRequest) String() string { return proto.CompactTextString(m) }
func (*CreateAIAccountRequest) ProtoMessage()    {}
func (*CreateAIAccountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{1}
}
func (m *CreateAIAccountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIAccountRequest.Unmarshal(m, b)
}
func (m *CreateAIAccountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIAccountRequest.Marshal(b, m, deterministic)
}
func (dst *CreateAIAccountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIAccountRequest.Merge(dst, src)
}
func (m *CreateAIAccountRequest) XXX_Size() int {
	return xxx_messageInfo_CreateAIAccountRequest.Size(m)
}
func (m *CreateAIAccountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIAccountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIAccountRequest proto.InternalMessageInfo

func (m *CreateAIAccountRequest) GetAccount() *CreateAIAccountRequest_Account {
	if m != nil {
		return m.Account
	}
	return nil
}

type CreateAIAccountRequest_Account struct {
	// 绑定手机号
	Phone string `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	// 密码
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	// 提示词ID
	PromptId uint32 `protobuf:"varint,4,opt,name=prompt_id,json=promptId,proto3" json:"prompt_id,omitempty"`
	// 音色ID
	TimbreId uint32 `protobuf:"varint,5,opt,name=timbre_id,json=timbreId,proto3" json:"timbre_id,omitempty"`
	// 关联角色ID
	RoleId uint32 `protobuf:"varint,6,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 开场白
	Prologue string `protobuf:"bytes,7,opt,name=prologue,proto3" json:"prologue,omitempty"`
	// 头像obs链接 字节流数据太大无法直接传
	Avatar string `protobuf:"bytes,8,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 昵称
	Nickname string `protobuf:"bytes,9,opt,name=nickname,proto3" json:"nickname,omitempty"`
	// 性别 0:女 1:男
	Sex uint32 `protobuf:"varint,10,opt,name=sex,proto3" json:"sex,omitempty"`
	// 生日, 格式: 2006-01-02
	Birthday string `protobuf:"bytes,11,opt,name=birthday,proto3" json:"birthday,omitempty"`
	// 个性签名
	Signature string `protobuf:"bytes,12,opt,name=signature,proto3" json:"signature,omitempty"`
	// 相册
	PhotoImgKeys []string `protobuf:"bytes,13,rep,name=photo_img_keys,json=photoImgKeys,proto3" json:"photo_img_keys,omitempty"`
	// 发放经验值
	AddExp uint32 `protobuf:"varint,14,opt,name=add_exp,json=addExp,proto3" json:"add_exp,omitempty"`
	// 标识
	Identity string `protobuf:"bytes,15,opt,name=identity,proto3" json:"identity,omitempty"`
	// 说明
	Desc string `protobuf:"bytes,16,opt,name=desc,proto3" json:"desc,omitempty"`
	// 排序
	Sort uint32 `protobuf:"varint,17,opt,name=sort,proto3" json:"sort,omitempty"`
	// 账号标签
	AccountTags []string `protobuf:"bytes,18,rep,name=account_tags,json=accountTags,proto3" json:"account_tags,omitempty"`
	// 是否展示在扩列墙
	IsShowInChatCardWall bool     `protobuf:"varint,19,opt,name=is_show_in_chat_card_wall,json=isShowInChatCardWall,proto3" json:"is_show_in_chat_card_wall,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateAIAccountRequest_Account) Reset()         { *m = CreateAIAccountRequest_Account{} }
func (m *CreateAIAccountRequest_Account) String() string { return proto.CompactTextString(m) }
func (*CreateAIAccountRequest_Account) ProtoMessage()    {}
func (*CreateAIAccountRequest_Account) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{1, 0}
}
func (m *CreateAIAccountRequest_Account) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIAccountRequest_Account.Unmarshal(m, b)
}
func (m *CreateAIAccountRequest_Account) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIAccountRequest_Account.Marshal(b, m, deterministic)
}
func (dst *CreateAIAccountRequest_Account) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIAccountRequest_Account.Merge(dst, src)
}
func (m *CreateAIAccountRequest_Account) XXX_Size() int {
	return xxx_messageInfo_CreateAIAccountRequest_Account.Size(m)
}
func (m *CreateAIAccountRequest_Account) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIAccountRequest_Account.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIAccountRequest_Account proto.InternalMessageInfo

func (m *CreateAIAccountRequest_Account) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *CreateAIAccountRequest_Account) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

func (m *CreateAIAccountRequest_Account) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *CreateAIAccountRequest_Account) GetPromptId() uint32 {
	if m != nil {
		return m.PromptId
	}
	return 0
}

func (m *CreateAIAccountRequest_Account) GetTimbreId() uint32 {
	if m != nil {
		return m.TimbreId
	}
	return 0
}

func (m *CreateAIAccountRequest_Account) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *CreateAIAccountRequest_Account) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *CreateAIAccountRequest_Account) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *CreateAIAccountRequest_Account) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *CreateAIAccountRequest_Account) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *CreateAIAccountRequest_Account) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *CreateAIAccountRequest_Account) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

func (m *CreateAIAccountRequest_Account) GetPhotoImgKeys() []string {
	if m != nil {
		return m.PhotoImgKeys
	}
	return nil
}

func (m *CreateAIAccountRequest_Account) GetAddExp() uint32 {
	if m != nil {
		return m.AddExp
	}
	return 0
}

func (m *CreateAIAccountRequest_Account) GetIdentity() string {
	if m != nil {
		return m.Identity
	}
	return ""
}

func (m *CreateAIAccountRequest_Account) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *CreateAIAccountRequest_Account) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *CreateAIAccountRequest_Account) GetAccountTags() []string {
	if m != nil {
		return m.AccountTags
	}
	return nil
}

func (m *CreateAIAccountRequest_Account) GetIsShowInChatCardWall() bool {
	if m != nil {
		return m.IsShowInChatCardWall
	}
	return false
}

type CreateAIAccountResponse struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateAIAccountResponse) Reset()         { *m = CreateAIAccountResponse{} }
func (m *CreateAIAccountResponse) String() string { return proto.CompactTextString(m) }
func (*CreateAIAccountResponse) ProtoMessage()    {}
func (*CreateAIAccountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{2}
}
func (m *CreateAIAccountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIAccountResponse.Unmarshal(m, b)
}
func (m *CreateAIAccountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIAccountResponse.Marshal(b, m, deterministic)
}
func (dst *CreateAIAccountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIAccountResponse.Merge(dst, src)
}
func (m *CreateAIAccountResponse) XXX_Size() int {
	return xxx_messageInfo_CreateAIAccountResponse.Size(m)
}
func (m *CreateAIAccountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIAccountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIAccountResponse proto.InternalMessageInfo

func (m *CreateAIAccountResponse) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 增量更新接口，通过update_flags字段来标记哪些字段需要更新
type UpdateAIAccountRequest struct {
	Account *UpdateAIAccountRequest_Account `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	// 用于标记哪些字段需要更新, key: 字段名(如 prompt_id), val: 是否更新
	UpdateFlags          map[string]bool `protobuf:"bytes,2,rep,name=update_flags,json=updateFlags,proto3" json:"update_flags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UpdateAIAccountRequest) Reset()         { *m = UpdateAIAccountRequest{} }
func (m *UpdateAIAccountRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateAIAccountRequest) ProtoMessage()    {}
func (*UpdateAIAccountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{3}
}
func (m *UpdateAIAccountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIAccountRequest.Unmarshal(m, b)
}
func (m *UpdateAIAccountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIAccountRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateAIAccountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIAccountRequest.Merge(dst, src)
}
func (m *UpdateAIAccountRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateAIAccountRequest.Size(m)
}
func (m *UpdateAIAccountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIAccountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIAccountRequest proto.InternalMessageInfo

func (m *UpdateAIAccountRequest) GetAccount() *UpdateAIAccountRequest_Account {
	if m != nil {
		return m.Account
	}
	return nil
}

func (m *UpdateAIAccountRequest) GetUpdateFlags() map[string]bool {
	if m != nil {
		return m.UpdateFlags
	}
	return nil
}

type UpdateAIAccountRequest_Account struct {
	// uid
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 密码
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	// ip地址
	Ip string `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	// 提示词ID
	PromptId uint32 `protobuf:"varint,4,opt,name=prompt_id,json=promptId,proto3" json:"prompt_id,omitempty"`
	// 音色ID
	TimbreId uint32 `protobuf:"varint,5,opt,name=timbre_id,json=timbreId,proto3" json:"timbre_id,omitempty"`
	// 关联角色ID
	RoleId uint32 `protobuf:"varint,6,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 开场白
	Prologue string `protobuf:"bytes,7,opt,name=prologue,proto3" json:"prologue,omitempty"`
	// 头像obs链接 字节流数据太大无法直接传
	Avatar string `protobuf:"bytes,8,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 昵称
	Nickname string `protobuf:"bytes,9,opt,name=nickname,proto3" json:"nickname,omitempty"`
	// 性别 0:女 1:男, 账号性别只可以修改一次
	Sex uint32 `protobuf:"varint,10,opt,name=sex,proto3" json:"sex,omitempty"`
	// 生日, 格式: 2006-01-02
	Birthday string `protobuf:"bytes,11,opt,name=birthday,proto3" json:"birthday,omitempty"`
	// 个性签名
	Signature string `protobuf:"bytes,12,opt,name=signature,proto3" json:"signature,omitempty"`
	// 相册
	PhotoImgKeys []string `protobuf:"bytes,13,rep,name=photo_img_keys,json=photoImgKeys,proto3" json:"photo_img_keys,omitempty"`
	// 发放经验值
	AddExp uint32 `protobuf:"varint,14,opt,name=add_exp,json=addExp,proto3" json:"add_exp,omitempty"`
	// 绑定手机号
	Phone string `protobuf:"bytes,15,opt,name=phone,proto3" json:"phone,omitempty"`
	// 标识
	Identity string `protobuf:"bytes,16,opt,name=identity,proto3" json:"identity,omitempty"`
	// 说明
	Desc string `protobuf:"bytes,17,opt,name=desc,proto3" json:"desc,omitempty"`
	// 排序
	Sort uint32 `protobuf:"varint,18,opt,name=sort,proto3" json:"sort,omitempty"`
	// 账号标签
	AccountTags []string `protobuf:"bytes,19,rep,name=account_tags,json=accountTags,proto3" json:"account_tags,omitempty"`
	// 是否展示在扩列墙
	IsShowInChatCardWall bool     `protobuf:"varint,20,opt,name=is_show_in_chat_card_wall,json=isShowInChatCardWall,proto3" json:"is_show_in_chat_card_wall,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAIAccountRequest_Account) Reset()         { *m = UpdateAIAccountRequest_Account{} }
func (m *UpdateAIAccountRequest_Account) String() string { return proto.CompactTextString(m) }
func (*UpdateAIAccountRequest_Account) ProtoMessage()    {}
func (*UpdateAIAccountRequest_Account) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{3, 0}
}
func (m *UpdateAIAccountRequest_Account) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIAccountRequest_Account.Unmarshal(m, b)
}
func (m *UpdateAIAccountRequest_Account) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIAccountRequest_Account.Marshal(b, m, deterministic)
}
func (dst *UpdateAIAccountRequest_Account) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIAccountRequest_Account.Merge(dst, src)
}
func (m *UpdateAIAccountRequest_Account) XXX_Size() int {
	return xxx_messageInfo_UpdateAIAccountRequest_Account.Size(m)
}
func (m *UpdateAIAccountRequest_Account) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIAccountRequest_Account.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIAccountRequest_Account proto.InternalMessageInfo

func (m *UpdateAIAccountRequest_Account) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateAIAccountRequest_Account) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

func (m *UpdateAIAccountRequest_Account) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *UpdateAIAccountRequest_Account) GetPromptId() uint32 {
	if m != nil {
		return m.PromptId
	}
	return 0
}

func (m *UpdateAIAccountRequest_Account) GetTimbreId() uint32 {
	if m != nil {
		return m.TimbreId
	}
	return 0
}

func (m *UpdateAIAccountRequest_Account) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *UpdateAIAccountRequest_Account) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *UpdateAIAccountRequest_Account) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *UpdateAIAccountRequest_Account) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UpdateAIAccountRequest_Account) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UpdateAIAccountRequest_Account) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *UpdateAIAccountRequest_Account) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

func (m *UpdateAIAccountRequest_Account) GetPhotoImgKeys() []string {
	if m != nil {
		return m.PhotoImgKeys
	}
	return nil
}

func (m *UpdateAIAccountRequest_Account) GetAddExp() uint32 {
	if m != nil {
		return m.AddExp
	}
	return 0
}

func (m *UpdateAIAccountRequest_Account) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *UpdateAIAccountRequest_Account) GetIdentity() string {
	if m != nil {
		return m.Identity
	}
	return ""
}

func (m *UpdateAIAccountRequest_Account) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *UpdateAIAccountRequest_Account) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *UpdateAIAccountRequest_Account) GetAccountTags() []string {
	if m != nil {
		return m.AccountTags
	}
	return nil
}

func (m *UpdateAIAccountRequest_Account) GetIsShowInChatCardWall() bool {
	if m != nil {
		return m.IsShowInChatCardWall
	}
	return false
}

type UpdateAIAccountResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAIAccountResponse) Reset()         { *m = UpdateAIAccountResponse{} }
func (m *UpdateAIAccountResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateAIAccountResponse) ProtoMessage()    {}
func (*UpdateAIAccountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{4}
}
func (m *UpdateAIAccountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIAccountResponse.Unmarshal(m, b)
}
func (m *UpdateAIAccountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIAccountResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateAIAccountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIAccountResponse.Merge(dst, src)
}
func (m *UpdateAIAccountResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateAIAccountResponse.Size(m)
}
func (m *UpdateAIAccountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIAccountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIAccountResponse proto.InternalMessageInfo

type UnregisterAIAccountRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnregisterAIAccountRequest) Reset()         { *m = UnregisterAIAccountRequest{} }
func (m *UnregisterAIAccountRequest) String() string { return proto.CompactTextString(m) }
func (*UnregisterAIAccountRequest) ProtoMessage()    {}
func (*UnregisterAIAccountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{5}
}
func (m *UnregisterAIAccountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnregisterAIAccountRequest.Unmarshal(m, b)
}
func (m *UnregisterAIAccountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnregisterAIAccountRequest.Marshal(b, m, deterministic)
}
func (dst *UnregisterAIAccountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnregisterAIAccountRequest.Merge(dst, src)
}
func (m *UnregisterAIAccountRequest) XXX_Size() int {
	return xxx_messageInfo_UnregisterAIAccountRequest.Size(m)
}
func (m *UnregisterAIAccountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UnregisterAIAccountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UnregisterAIAccountRequest proto.InternalMessageInfo

func (m *UnregisterAIAccountRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UnregisterAIAccountRequest) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

type UnregisterAIAccountResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnregisterAIAccountResponse) Reset()         { *m = UnregisterAIAccountResponse{} }
func (m *UnregisterAIAccountResponse) String() string { return proto.CompactTextString(m) }
func (*UnregisterAIAccountResponse) ProtoMessage()    {}
func (*UnregisterAIAccountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{6}
}
func (m *UnregisterAIAccountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnregisterAIAccountResponse.Unmarshal(m, b)
}
func (m *UnregisterAIAccountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnregisterAIAccountResponse.Marshal(b, m, deterministic)
}
func (dst *UnregisterAIAccountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnregisterAIAccountResponse.Merge(dst, src)
}
func (m *UnregisterAIAccountResponse) XXX_Size() int {
	return xxx_messageInfo_UnregisterAIAccountResponse.Size(m)
}
func (m *UnregisterAIAccountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UnregisterAIAccountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UnregisterAIAccountResponse proto.InternalMessageInfo

type GetPageAIAccountRequest struct {
	Page                   uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                   uint32   `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	ChatCardWallShowStatus uint32   `protobuf:"varint,3,opt,name=chat_card_wall_show_status,json=chatCardWallShowStatus,proto3" json:"chat_card_wall_show_status,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *GetPageAIAccountRequest) Reset()         { *m = GetPageAIAccountRequest{} }
func (m *GetPageAIAccountRequest) String() string { return proto.CompactTextString(m) }
func (*GetPageAIAccountRequest) ProtoMessage()    {}
func (*GetPageAIAccountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{7}
}
func (m *GetPageAIAccountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPageAIAccountRequest.Unmarshal(m, b)
}
func (m *GetPageAIAccountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPageAIAccountRequest.Marshal(b, m, deterministic)
}
func (dst *GetPageAIAccountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPageAIAccountRequest.Merge(dst, src)
}
func (m *GetPageAIAccountRequest) XXX_Size() int {
	return xxx_messageInfo_GetPageAIAccountRequest.Size(m)
}
func (m *GetPageAIAccountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPageAIAccountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPageAIAccountRequest proto.InternalMessageInfo

func (m *GetPageAIAccountRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetPageAIAccountRequest) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetPageAIAccountRequest) GetChatCardWallShowStatus() uint32 {
	if m != nil {
		return m.ChatCardWallShowStatus
	}
	return 0
}

type GetPageAIAccountResponse struct {
	AccountList          []*AIAccountInfo `protobuf:"bytes,1,rep,name=account_list,json=accountList,proto3" json:"account_list,omitempty"`
	Total                uint32           `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetPageAIAccountResponse) Reset()         { *m = GetPageAIAccountResponse{} }
func (m *GetPageAIAccountResponse) String() string { return proto.CompactTextString(m) }
func (*GetPageAIAccountResponse) ProtoMessage()    {}
func (*GetPageAIAccountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{8}
}
func (m *GetPageAIAccountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPageAIAccountResponse.Unmarshal(m, b)
}
func (m *GetPageAIAccountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPageAIAccountResponse.Marshal(b, m, deterministic)
}
func (dst *GetPageAIAccountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPageAIAccountResponse.Merge(dst, src)
}
func (m *GetPageAIAccountResponse) XXX_Size() int {
	return xxx_messageInfo_GetPageAIAccountResponse.Size(m)
}
func (m *GetPageAIAccountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPageAIAccountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPageAIAccountResponse proto.InternalMessageInfo

func (m *GetPageAIAccountResponse) GetAccountList() []*AIAccountInfo {
	if m != nil {
		return m.AccountList
	}
	return nil
}

func (m *GetPageAIAccountResponse) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetAIAccountRequest struct {
	Uid                  uint32                          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ReqSource            aigc_account.GetAIAccountSource `protobuf:"varint,2,opt,name=req_source,json=reqSource,proto3,enum=aigc_account.GetAIAccountSource" json:"req_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetAIAccountRequest) Reset()         { *m = GetAIAccountRequest{} }
func (m *GetAIAccountRequest) String() string { return proto.CompactTextString(m) }
func (*GetAIAccountRequest) ProtoMessage()    {}
func (*GetAIAccountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{9}
}
func (m *GetAIAccountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIAccountRequest.Unmarshal(m, b)
}
func (m *GetAIAccountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIAccountRequest.Marshal(b, m, deterministic)
}
func (dst *GetAIAccountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIAccountRequest.Merge(dst, src)
}
func (m *GetAIAccountRequest) XXX_Size() int {
	return xxx_messageInfo_GetAIAccountRequest.Size(m)
}
func (m *GetAIAccountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIAccountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIAccountRequest proto.InternalMessageInfo

func (m *GetAIAccountRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAIAccountRequest) GetReqSource() aigc_account.GetAIAccountSource {
	if m != nil {
		return m.ReqSource
	}
	return aigc_account.GetAIAccountSource_DEFAULT
}

type GetAIAccountResponse struct {
	Account              *AIAccountInfo `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetAIAccountResponse) Reset()         { *m = GetAIAccountResponse{} }
func (m *GetAIAccountResponse) String() string { return proto.CompactTextString(m) }
func (*GetAIAccountResponse) ProtoMessage()    {}
func (*GetAIAccountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{10}
}
func (m *GetAIAccountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIAccountResponse.Unmarshal(m, b)
}
func (m *GetAIAccountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIAccountResponse.Marshal(b, m, deterministic)
}
func (dst *GetAIAccountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIAccountResponse.Merge(dst, src)
}
func (m *GetAIAccountResponse) XXX_Size() int {
	return xxx_messageInfo_GetAIAccountResponse.Size(m)
}
func (m *GetAIAccountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIAccountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIAccountResponse proto.InternalMessageInfo

func (m *GetAIAccountResponse) GetAccount() *AIAccountInfo {
	if m != nil {
		return m.Account
	}
	return nil
}

type BatchGetAIAccountRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAIAccountRequest) Reset()         { *m = BatchGetAIAccountRequest{} }
func (m *BatchGetAIAccountRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetAIAccountRequest) ProtoMessage()    {}
func (*BatchGetAIAccountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{11}
}
func (m *BatchGetAIAccountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAIAccountRequest.Unmarshal(m, b)
}
func (m *BatchGetAIAccountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAIAccountRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetAIAccountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAIAccountRequest.Merge(dst, src)
}
func (m *BatchGetAIAccountRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetAIAccountRequest.Size(m)
}
func (m *BatchGetAIAccountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAIAccountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAIAccountRequest proto.InternalMessageInfo

func (m *BatchGetAIAccountRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetAIAccountResponse struct {
	AccountList          []*AIAccountInfo `protobuf:"bytes,1,rep,name=account_list,json=accountList,proto3" json:"account_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchGetAIAccountResponse) Reset()         { *m = BatchGetAIAccountResponse{} }
func (m *BatchGetAIAccountResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetAIAccountResponse) ProtoMessage()    {}
func (*BatchGetAIAccountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{12}
}
func (m *BatchGetAIAccountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAIAccountResponse.Unmarshal(m, b)
}
func (m *BatchGetAIAccountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAIAccountResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetAIAccountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAIAccountResponse.Merge(dst, src)
}
func (m *BatchGetAIAccountResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetAIAccountResponse.Size(m)
}
func (m *BatchGetAIAccountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAIAccountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAIAccountResponse proto.InternalMessageInfo

func (m *BatchGetAIAccountResponse) GetAccountList() []*AIAccountInfo {
	if m != nil {
		return m.AccountList
	}
	return nil
}

type AddAiPostReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAiPostReq) Reset()         { *m = AddAiPostReq{} }
func (m *AddAiPostReq) String() string { return proto.CompactTextString(m) }
func (*AddAiPostReq) ProtoMessage()    {}
func (*AddAiPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{13}
}
func (m *AddAiPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAiPostReq.Unmarshal(m, b)
}
func (m *AddAiPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAiPostReq.Marshal(b, m, deterministic)
}
func (dst *AddAiPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAiPostReq.Merge(dst, src)
}
func (m *AddAiPostReq) XXX_Size() int {
	return xxx_messageInfo_AddAiPostReq.Size(m)
}
func (m *AddAiPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAiPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddAiPostReq proto.InternalMessageInfo

func (m *AddAiPostReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddAiPostReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type AddAiPostResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAiPostResp) Reset()         { *m = AddAiPostResp{} }
func (m *AddAiPostResp) String() string { return proto.CompactTextString(m) }
func (*AddAiPostResp) ProtoMessage()    {}
func (*AddAiPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{14}
}
func (m *AddAiPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAiPostResp.Unmarshal(m, b)
}
func (m *AddAiPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAiPostResp.Marshal(b, m, deterministic)
}
func (dst *AddAiPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAiPostResp.Merge(dst, src)
}
func (m *AddAiPostResp) XXX_Size() int {
	return xxx_messageInfo_AddAiPostResp.Size(m)
}
func (m *AddAiPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAiPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddAiPostResp proto.InternalMessageInfo

type GetAiPostReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32   `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAiPostReq) Reset()         { *m = GetAiPostReq{} }
func (m *GetAiPostReq) String() string { return proto.CompactTextString(m) }
func (*GetAiPostReq) ProtoMessage()    {}
func (*GetAiPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{15}
}
func (m *GetAiPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAiPostReq.Unmarshal(m, b)
}
func (m *GetAiPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAiPostReq.Marshal(b, m, deterministic)
}
func (dst *GetAiPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAiPostReq.Merge(dst, src)
}
func (m *GetAiPostReq) XXX_Size() int {
	return xxx_messageInfo_GetAiPostReq.Size(m)
}
func (m *GetAiPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAiPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAiPostReq proto.InternalMessageInfo

func (m *GetAiPostReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetAiPostReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetAiPostReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAiPostResp struct {
	PostList             []*PostInfo `protobuf:"bytes,1,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	TotalNum             uint32      `protobuf:"varint,2,opt,name=total_num,json=totalNum,proto3" json:"total_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetAiPostResp) Reset()         { *m = GetAiPostResp{} }
func (m *GetAiPostResp) String() string { return proto.CompactTextString(m) }
func (*GetAiPostResp) ProtoMessage()    {}
func (*GetAiPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{16}
}
func (m *GetAiPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAiPostResp.Unmarshal(m, b)
}
func (m *GetAiPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAiPostResp.Marshal(b, m, deterministic)
}
func (dst *GetAiPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAiPostResp.Merge(dst, src)
}
func (m *GetAiPostResp) XXX_Size() int {
	return xxx_messageInfo_GetAiPostResp.Size(m)
}
func (m *GetAiPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAiPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAiPostResp proto.InternalMessageInfo

func (m *GetAiPostResp) GetPostList() []*PostInfo {
	if m != nil {
		return m.PostList
	}
	return nil
}

func (m *GetAiPostResp) GetTotalNum() uint32 {
	if m != nil {
		return m.TotalNum
	}
	return 0
}

type PostInfo struct {
	PostId               string            `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Content              string            `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Attachments          []*AttachmentInfo `protobuf:"bytes,3,rep,name=attachments,proto3" json:"attachments,omitempty"`
	CreateTime           uint64            `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PostInfo) Reset()         { *m = PostInfo{} }
func (m *PostInfo) String() string { return proto.CompactTextString(m) }
func (*PostInfo) ProtoMessage()    {}
func (*PostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{17}
}
func (m *PostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostInfo.Unmarshal(m, b)
}
func (m *PostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostInfo.Marshal(b, m, deterministic)
}
func (dst *PostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostInfo.Merge(dst, src)
}
func (m *PostInfo) XXX_Size() int {
	return xxx_messageInfo_PostInfo.Size(m)
}
func (m *PostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PostInfo proto.InternalMessageInfo

func (m *PostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PostInfo) GetAttachments() []*AttachmentInfo {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *PostInfo) GetCreateTime() uint64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type AttachmentInfo struct {
	AttachmentType    uint32 `protobuf:"varint,1,opt,name=attachment_type,json=attachmentType,proto3" json:"attachment_type,omitempty"`
	AttachmentContent string `protobuf:"bytes,2,opt,name=attachment_content,json=attachmentContent,proto3" json:"attachment_content,omitempty"`
	ExtraInfo         string `protobuf:"bytes,3,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`
	Key               string `protobuf:"bytes,4,opt,name=key,proto3" json:"key,omitempty"`
	VmContent         string `protobuf:"bytes,5,opt,name=vm_content,json=vmContent,proto3" json:"vm_content,omitempty"`
	// 只有type = VIDEO 才有的
	Param string `protobuf:"bytes,6,opt,name=param,proto3" json:"param,omitempty"`
	// 原视频封面
	OriginVideoCover     string   `protobuf:"bytes,7,opt,name=origin_video_cover,json=originVideoCover,proto3" json:"origin_video_cover,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AttachmentInfo) Reset()         { *m = AttachmentInfo{} }
func (m *AttachmentInfo) String() string { return proto.CompactTextString(m) }
func (*AttachmentInfo) ProtoMessage()    {}
func (*AttachmentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{18}
}
func (m *AttachmentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttachmentInfo.Unmarshal(m, b)
}
func (m *AttachmentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttachmentInfo.Marshal(b, m, deterministic)
}
func (dst *AttachmentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttachmentInfo.Merge(dst, src)
}
func (m *AttachmentInfo) XXX_Size() int {
	return xxx_messageInfo_AttachmentInfo.Size(m)
}
func (m *AttachmentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AttachmentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AttachmentInfo proto.InternalMessageInfo

func (m *AttachmentInfo) GetAttachmentType() uint32 {
	if m != nil {
		return m.AttachmentType
	}
	return 0
}

func (m *AttachmentInfo) GetAttachmentContent() string {
	if m != nil {
		return m.AttachmentContent
	}
	return ""
}

func (m *AttachmentInfo) GetExtraInfo() string {
	if m != nil {
		return m.ExtraInfo
	}
	return ""
}

func (m *AttachmentInfo) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *AttachmentInfo) GetVmContent() string {
	if m != nil {
		return m.VmContent
	}
	return ""
}

func (m *AttachmentInfo) GetParam() string {
	if m != nil {
		return m.Param
	}
	return ""
}

func (m *AttachmentInfo) GetOriginVideoCover() string {
	if m != nil {
		return m.OriginVideoCover
	}
	return ""
}

type UpdateAiRoomCfgRequest struct {
	AiRoomCfg            *aigc_account.AiRoomCfg `protobuf:"bytes,1,opt,name=ai_room_cfg,json=aiRoomCfg,proto3" json:"ai_room_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *UpdateAiRoomCfgRequest) Reset()         { *m = UpdateAiRoomCfgRequest{} }
func (m *UpdateAiRoomCfgRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateAiRoomCfgRequest) ProtoMessage()    {}
func (*UpdateAiRoomCfgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{19}
}
func (m *UpdateAiRoomCfgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAiRoomCfgRequest.Unmarshal(m, b)
}
func (m *UpdateAiRoomCfgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAiRoomCfgRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateAiRoomCfgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAiRoomCfgRequest.Merge(dst, src)
}
func (m *UpdateAiRoomCfgRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateAiRoomCfgRequest.Size(m)
}
func (m *UpdateAiRoomCfgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAiRoomCfgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAiRoomCfgRequest proto.InternalMessageInfo

func (m *UpdateAiRoomCfgRequest) GetAiRoomCfg() *aigc_account.AiRoomCfg {
	if m != nil {
		return m.AiRoomCfg
	}
	return nil
}

type UpdateAiRoomCfgResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAiRoomCfgResponse) Reset()         { *m = UpdateAiRoomCfgResponse{} }
func (m *UpdateAiRoomCfgResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateAiRoomCfgResponse) ProtoMessage()    {}
func (*UpdateAiRoomCfgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{20}
}
func (m *UpdateAiRoomCfgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAiRoomCfgResponse.Unmarshal(m, b)
}
func (m *UpdateAiRoomCfgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAiRoomCfgResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateAiRoomCfgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAiRoomCfgResponse.Merge(dst, src)
}
func (m *UpdateAiRoomCfgResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateAiRoomCfgResponse.Size(m)
}
func (m *UpdateAiRoomCfgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAiRoomCfgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAiRoomCfgResponse proto.InternalMessageInfo

type GetAiRoomCfgRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAiRoomCfgRequest) Reset()         { *m = GetAiRoomCfgRequest{} }
func (m *GetAiRoomCfgRequest) String() string { return proto.CompactTextString(m) }
func (*GetAiRoomCfgRequest) ProtoMessage()    {}
func (*GetAiRoomCfgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{21}
}
func (m *GetAiRoomCfgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAiRoomCfgRequest.Unmarshal(m, b)
}
func (m *GetAiRoomCfgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAiRoomCfgRequest.Marshal(b, m, deterministic)
}
func (dst *GetAiRoomCfgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAiRoomCfgRequest.Merge(dst, src)
}
func (m *GetAiRoomCfgRequest) XXX_Size() int {
	return xxx_messageInfo_GetAiRoomCfgRequest.Size(m)
}
func (m *GetAiRoomCfgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAiRoomCfgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAiRoomCfgRequest proto.InternalMessageInfo

func (m *GetAiRoomCfgRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAiRoomCfgResponse struct {
	AiRoomCfg            *aigc_account.AiRoomCfg `protobuf:"bytes,1,opt,name=ai_room_cfg,json=aiRoomCfg,proto3" json:"ai_room_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetAiRoomCfgResponse) Reset()         { *m = GetAiRoomCfgResponse{} }
func (m *GetAiRoomCfgResponse) String() string { return proto.CompactTextString(m) }
func (*GetAiRoomCfgResponse) ProtoMessage()    {}
func (*GetAiRoomCfgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499, []int{22}
}
func (m *GetAiRoomCfgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAiRoomCfgResponse.Unmarshal(m, b)
}
func (m *GetAiRoomCfgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAiRoomCfgResponse.Marshal(b, m, deterministic)
}
func (dst *GetAiRoomCfgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAiRoomCfgResponse.Merge(dst, src)
}
func (m *GetAiRoomCfgResponse) XXX_Size() int {
	return xxx_messageInfo_GetAiRoomCfgResponse.Size(m)
}
func (m *GetAiRoomCfgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAiRoomCfgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAiRoomCfgResponse proto.InternalMessageInfo

func (m *GetAiRoomCfgResponse) GetAiRoomCfg() *aigc_account.AiRoomCfg {
	if m != nil {
		return m.AiRoomCfg
	}
	return nil
}

func init() {
	proto.RegisterType((*AIAccountInfo)(nil), "aigc_account_middle.AIAccountInfo")
	proto.RegisterType((*CreateAIAccountRequest)(nil), "aigc_account_middle.CreateAIAccountRequest")
	proto.RegisterType((*CreateAIAccountRequest_Account)(nil), "aigc_account_middle.CreateAIAccountRequest.Account")
	proto.RegisterType((*CreateAIAccountResponse)(nil), "aigc_account_middle.CreateAIAccountResponse")
	proto.RegisterType((*UpdateAIAccountRequest)(nil), "aigc_account_middle.UpdateAIAccountRequest")
	proto.RegisterMapType((map[string]bool)(nil), "aigc_account_middle.UpdateAIAccountRequest.UpdateFlagsEntry")
	proto.RegisterType((*UpdateAIAccountRequest_Account)(nil), "aigc_account_middle.UpdateAIAccountRequest.Account")
	proto.RegisterType((*UpdateAIAccountResponse)(nil), "aigc_account_middle.UpdateAIAccountResponse")
	proto.RegisterType((*UnregisterAIAccountRequest)(nil), "aigc_account_middle.UnregisterAIAccountRequest")
	proto.RegisterType((*UnregisterAIAccountResponse)(nil), "aigc_account_middle.UnregisterAIAccountResponse")
	proto.RegisterType((*GetPageAIAccountRequest)(nil), "aigc_account_middle.GetPageAIAccountRequest")
	proto.RegisterType((*GetPageAIAccountResponse)(nil), "aigc_account_middle.GetPageAIAccountResponse")
	proto.RegisterType((*GetAIAccountRequest)(nil), "aigc_account_middle.GetAIAccountRequest")
	proto.RegisterType((*GetAIAccountResponse)(nil), "aigc_account_middle.GetAIAccountResponse")
	proto.RegisterType((*BatchGetAIAccountRequest)(nil), "aigc_account_middle.BatchGetAIAccountRequest")
	proto.RegisterType((*BatchGetAIAccountResponse)(nil), "aigc_account_middle.BatchGetAIAccountResponse")
	proto.RegisterType((*AddAiPostReq)(nil), "aigc_account_middle.AddAiPostReq")
	proto.RegisterType((*AddAiPostResp)(nil), "aigc_account_middle.AddAiPostResp")
	proto.RegisterType((*GetAiPostReq)(nil), "aigc_account_middle.GetAiPostReq")
	proto.RegisterType((*GetAiPostResp)(nil), "aigc_account_middle.GetAiPostResp")
	proto.RegisterType((*PostInfo)(nil), "aigc_account_middle.PostInfo")
	proto.RegisterType((*AttachmentInfo)(nil), "aigc_account_middle.AttachmentInfo")
	proto.RegisterType((*UpdateAiRoomCfgRequest)(nil), "aigc_account_middle.UpdateAiRoomCfgRequest")
	proto.RegisterType((*UpdateAiRoomCfgResponse)(nil), "aigc_account_middle.UpdateAiRoomCfgResponse")
	proto.RegisterType((*GetAiRoomCfgRequest)(nil), "aigc_account_middle.GetAiRoomCfgRequest")
	proto.RegisterType((*GetAiRoomCfgResponse)(nil), "aigc_account_middle.GetAiRoomCfgResponse")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AigcAccountMiddleClient is the client API for AigcAccountMiddle service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AigcAccountMiddleClient interface {
	// 创建AI账号
	CreateAIAccount(ctx context.Context, in *CreateAIAccountRequest, opts ...grpc.CallOption) (*CreateAIAccountResponse, error)
	// 更新AI账号
	UpdateAIAccount(ctx context.Context, in *UpdateAIAccountRequest, opts ...grpc.CallOption) (*UpdateAIAccountResponse, error)
	// 注销AI账号
	UnregisterAIAccount(ctx context.Context, in *UnregisterAIAccountRequest, opts ...grpc.CallOption) (*UnregisterAIAccountResponse, error)
	// 获取AI账号列表（运营后台）
	GetPageAIAccount(ctx context.Context, in *GetPageAIAccountRequest, opts ...grpc.CallOption) (*GetPageAIAccountResponse, error)
	// 根据uid获取AI账号
	GetAIAccount(ctx context.Context, in *GetAIAccountRequest, opts ...grpc.CallOption) (*GetAIAccountResponse, error)
	// 批量获取AI账号
	BatchGetAIAccount(ctx context.Context, in *BatchGetAIAccountRequest, opts ...grpc.CallOption) (*BatchGetAIAccountResponse, error)
	// ai发帖上报
	AddAiPost(ctx context.Context, in *AddAiPostReq, opts ...grpc.CallOption) (*AddAiPostResp, error)
	// 获取帖子列表
	GetAiPost(ctx context.Context, in *GetAiPostReq, opts ...grpc.CallOption) (*GetAiPostResp, error)
	// 更新AI账号房间配置
	UpdateAiRoomCfg(ctx context.Context, in *UpdateAiRoomCfgRequest, opts ...grpc.CallOption) (*UpdateAiRoomCfgResponse, error)
	// 获取AI账号房间配置
	GetAiRoomCfg(ctx context.Context, in *GetAiRoomCfgRequest, opts ...grpc.CallOption) (*GetAiRoomCfgResponse, error)
}

type aigcAccountMiddleClient struct {
	cc *grpc.ClientConn
}

func NewAigcAccountMiddleClient(cc *grpc.ClientConn) AigcAccountMiddleClient {
	return &aigcAccountMiddleClient{cc}
}

func (c *aigcAccountMiddleClient) CreateAIAccount(ctx context.Context, in *CreateAIAccountRequest, opts ...grpc.CallOption) (*CreateAIAccountResponse, error) {
	out := new(CreateAIAccountResponse)
	err := c.cc.Invoke(ctx, "/aigc_account_middle.AigcAccountMiddle/CreateAIAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountMiddleClient) UpdateAIAccount(ctx context.Context, in *UpdateAIAccountRequest, opts ...grpc.CallOption) (*UpdateAIAccountResponse, error) {
	out := new(UpdateAIAccountResponse)
	err := c.cc.Invoke(ctx, "/aigc_account_middle.AigcAccountMiddle/UpdateAIAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountMiddleClient) UnregisterAIAccount(ctx context.Context, in *UnregisterAIAccountRequest, opts ...grpc.CallOption) (*UnregisterAIAccountResponse, error) {
	out := new(UnregisterAIAccountResponse)
	err := c.cc.Invoke(ctx, "/aigc_account_middle.AigcAccountMiddle/UnregisterAIAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountMiddleClient) GetPageAIAccount(ctx context.Context, in *GetPageAIAccountRequest, opts ...grpc.CallOption) (*GetPageAIAccountResponse, error) {
	out := new(GetPageAIAccountResponse)
	err := c.cc.Invoke(ctx, "/aigc_account_middle.AigcAccountMiddle/GetPageAIAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountMiddleClient) GetAIAccount(ctx context.Context, in *GetAIAccountRequest, opts ...grpc.CallOption) (*GetAIAccountResponse, error) {
	out := new(GetAIAccountResponse)
	err := c.cc.Invoke(ctx, "/aigc_account_middle.AigcAccountMiddle/GetAIAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountMiddleClient) BatchGetAIAccount(ctx context.Context, in *BatchGetAIAccountRequest, opts ...grpc.CallOption) (*BatchGetAIAccountResponse, error) {
	out := new(BatchGetAIAccountResponse)
	err := c.cc.Invoke(ctx, "/aigc_account_middle.AigcAccountMiddle/BatchGetAIAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountMiddleClient) AddAiPost(ctx context.Context, in *AddAiPostReq, opts ...grpc.CallOption) (*AddAiPostResp, error) {
	out := new(AddAiPostResp)
	err := c.cc.Invoke(ctx, "/aigc_account_middle.AigcAccountMiddle/AddAiPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountMiddleClient) GetAiPost(ctx context.Context, in *GetAiPostReq, opts ...grpc.CallOption) (*GetAiPostResp, error) {
	out := new(GetAiPostResp)
	err := c.cc.Invoke(ctx, "/aigc_account_middle.AigcAccountMiddle/GetAiPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountMiddleClient) UpdateAiRoomCfg(ctx context.Context, in *UpdateAiRoomCfgRequest, opts ...grpc.CallOption) (*UpdateAiRoomCfgResponse, error) {
	out := new(UpdateAiRoomCfgResponse)
	err := c.cc.Invoke(ctx, "/aigc_account_middle.AigcAccountMiddle/UpdateAiRoomCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcAccountMiddleClient) GetAiRoomCfg(ctx context.Context, in *GetAiRoomCfgRequest, opts ...grpc.CallOption) (*GetAiRoomCfgResponse, error) {
	out := new(GetAiRoomCfgResponse)
	err := c.cc.Invoke(ctx, "/aigc_account_middle.AigcAccountMiddle/GetAiRoomCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AigcAccountMiddleServer is the server API for AigcAccountMiddle service.
type AigcAccountMiddleServer interface {
	// 创建AI账号
	CreateAIAccount(context.Context, *CreateAIAccountRequest) (*CreateAIAccountResponse, error)
	// 更新AI账号
	UpdateAIAccount(context.Context, *UpdateAIAccountRequest) (*UpdateAIAccountResponse, error)
	// 注销AI账号
	UnregisterAIAccount(context.Context, *UnregisterAIAccountRequest) (*UnregisterAIAccountResponse, error)
	// 获取AI账号列表（运营后台）
	GetPageAIAccount(context.Context, *GetPageAIAccountRequest) (*GetPageAIAccountResponse, error)
	// 根据uid获取AI账号
	GetAIAccount(context.Context, *GetAIAccountRequest) (*GetAIAccountResponse, error)
	// 批量获取AI账号
	BatchGetAIAccount(context.Context, *BatchGetAIAccountRequest) (*BatchGetAIAccountResponse, error)
	// ai发帖上报
	AddAiPost(context.Context, *AddAiPostReq) (*AddAiPostResp, error)
	// 获取帖子列表
	GetAiPost(context.Context, *GetAiPostReq) (*GetAiPostResp, error)
	// 更新AI账号房间配置
	UpdateAiRoomCfg(context.Context, *UpdateAiRoomCfgRequest) (*UpdateAiRoomCfgResponse, error)
	// 获取AI账号房间配置
	GetAiRoomCfg(context.Context, *GetAiRoomCfgRequest) (*GetAiRoomCfgResponse, error)
}

func RegisterAigcAccountMiddleServer(s *grpc.Server, srv AigcAccountMiddleServer) {
	s.RegisterService(&_AigcAccountMiddle_serviceDesc, srv)
}

func _AigcAccountMiddle_CreateAIAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAIAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountMiddleServer).CreateAIAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account_middle.AigcAccountMiddle/CreateAIAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountMiddleServer).CreateAIAccount(ctx, req.(*CreateAIAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccountMiddle_UpdateAIAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAIAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountMiddleServer).UpdateAIAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account_middle.AigcAccountMiddle/UpdateAIAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountMiddleServer).UpdateAIAccount(ctx, req.(*UpdateAIAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccountMiddle_UnregisterAIAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnregisterAIAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountMiddleServer).UnregisterAIAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account_middle.AigcAccountMiddle/UnregisterAIAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountMiddleServer).UnregisterAIAccount(ctx, req.(*UnregisterAIAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccountMiddle_GetPageAIAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPageAIAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountMiddleServer).GetPageAIAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account_middle.AigcAccountMiddle/GetPageAIAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountMiddleServer).GetPageAIAccount(ctx, req.(*GetPageAIAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccountMiddle_GetAIAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAIAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountMiddleServer).GetAIAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account_middle.AigcAccountMiddle/GetAIAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountMiddleServer).GetAIAccount(ctx, req.(*GetAIAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccountMiddle_BatchGetAIAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAIAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountMiddleServer).BatchGetAIAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account_middle.AigcAccountMiddle/BatchGetAIAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountMiddleServer).BatchGetAIAccount(ctx, req.(*BatchGetAIAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccountMiddle_AddAiPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAiPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountMiddleServer).AddAiPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account_middle.AigcAccountMiddle/AddAiPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountMiddleServer).AddAiPost(ctx, req.(*AddAiPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccountMiddle_GetAiPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAiPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountMiddleServer).GetAiPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account_middle.AigcAccountMiddle/GetAiPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountMiddleServer).GetAiPost(ctx, req.(*GetAiPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccountMiddle_UpdateAiRoomCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAiRoomCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountMiddleServer).UpdateAiRoomCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account_middle.AigcAccountMiddle/UpdateAiRoomCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountMiddleServer).UpdateAiRoomCfg(ctx, req.(*UpdateAiRoomCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcAccountMiddle_GetAiRoomCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAiRoomCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcAccountMiddleServer).GetAiRoomCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc_account_middle.AigcAccountMiddle/GetAiRoomCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcAccountMiddleServer).GetAiRoomCfg(ctx, req.(*GetAiRoomCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _AigcAccountMiddle_serviceDesc = grpc.ServiceDesc{
	ServiceName: "aigc_account_middle.AigcAccountMiddle",
	HandlerType: (*AigcAccountMiddleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAIAccount",
			Handler:    _AigcAccountMiddle_CreateAIAccount_Handler,
		},
		{
			MethodName: "UpdateAIAccount",
			Handler:    _AigcAccountMiddle_UpdateAIAccount_Handler,
		},
		{
			MethodName: "UnregisterAIAccount",
			Handler:    _AigcAccountMiddle_UnregisterAIAccount_Handler,
		},
		{
			MethodName: "GetPageAIAccount",
			Handler:    _AigcAccountMiddle_GetPageAIAccount_Handler,
		},
		{
			MethodName: "GetAIAccount",
			Handler:    _AigcAccountMiddle_GetAIAccount_Handler,
		},
		{
			MethodName: "BatchGetAIAccount",
			Handler:    _AigcAccountMiddle_BatchGetAIAccount_Handler,
		},
		{
			MethodName: "AddAiPost",
			Handler:    _AigcAccountMiddle_AddAiPost_Handler,
		},
		{
			MethodName: "GetAiPost",
			Handler:    _AigcAccountMiddle_GetAiPost_Handler,
		},
		{
			MethodName: "UpdateAiRoomCfg",
			Handler:    _AigcAccountMiddle_UpdateAiRoomCfg_Handler,
		},
		{
			MethodName: "GetAiRoomCfg",
			Handler:    _AigcAccountMiddle_GetAiRoomCfg_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/aigc/aigc-account-middle/aigc-account-middle.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/aigc/aigc-account-middle/aigc-account-middle.proto", fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499)
}

var fileDescriptor_aigc_account_middle_9e5ee8a8c6a1e499 = []byte{
	// 1510 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x58, 0xcb, 0x6e, 0xdb, 0x46,
	0x17, 0x8e, 0x7c, 0x95, 0x8e, 0x2c, 0x5f, 0xc6, 0xfe, 0x6d, 0x5a, 0xf9, 0x8d, 0xdf, 0xe1, 0x5f,
	0x20, 0x0e, 0x1a, 0xcb, 0x85, 0x83, 0x20, 0xad, 0x11, 0xb4, 0x70, 0x0c, 0x37, 0x15, 0x9a, 0xb4,
	0xa9, 0xe2, 0xb4, 0x40, 0x37, 0xc4, 0x98, 0x1c, 0x53, 0x03, 0x93, 0x1c, 0x9a, 0x33, 0x94, 0xad,
	0x00, 0x5d, 0xf5, 0x29, 0xfa, 0x00, 0x5d, 0xb5, 0xeb, 0x3e, 0x40, 0x9f, 0xab, 0x8b, 0x62, 0x66,
	0x48, 0x8a, 0x92, 0x48, 0x45, 0x0e, 0xba, 0x68, 0x36, 0xc6, 0x9c, 0xcb, 0x9c, 0xdb, 0x7c, 0xe7,
	0x1c, 0xd1, 0x70, 0x2c, 0xc4, 0xc1, 0x55, 0x4c, 0xed, 0x4b, 0x4e, 0xbd, 0x1e, 0x89, 0x0e, 0x30,
	0x75, 0x6d, 0xf5, 0x67, 0x1f, 0xdb, 0x36, 0x8b, 0x03, 0xb1, 0xef, 0x53, 0xc7, 0xf1, 0x48, 0x11,
	0xaf, 0x15, 0x46, 0x4c, 0x30, 0xb4, 0x2e, 0x45, 0x56, 0x22, 0xb2, 0xb4, 0xa8, 0xf9, 0xe8, 0x5d,
	0x76, 0x87, 0x08, 0x6d, 0xc9, 0xfc, 0x7d, 0x1e, 0x1a, 0xc7, 0xed, 0x63, 0xcd, 0x6b, 0x07, 0x17,
	0x0c, 0xad, 0xc2, 0x6c, 0x4c, 0x1d, 0xa3, 0xb2, 0x5b, 0xd9, 0x6b, 0x74, 0xe4, 0x11, 0x35, 0xa1,
	0x1a, 0x62, 0xce, 0xaf, 0x59, 0xe4, 0x18, 0x33, 0xbb, 0x95, 0xbd, 0x5a, 0x27, 0xa3, 0xd1, 0x32,
	0xcc, 0xd0, 0xd0, 0x98, 0x55, 0xdc, 0x19, 0x1a, 0xa2, 0xbb, 0x50, 0x0b, 0x23, 0xe6, 0x87, 0xc2,
	0xa2, 0x8e, 0x31, 0xa7, 0x6c, 0x54, 0x35, 0xa3, 0xed, 0x48, 0xa1, 0xa0, 0xfe, 0x79, 0x44, 0xa4,
	0x70, 0x5e, 0x0b, 0x35, 0xa3, 0xed, 0xa0, 0x2d, 0x58, 0x8c, 0x98, 0xa7, 0x44, 0x0b, 0x4a, 0xb4,
	0x20, 0xc9, 0xb6, 0x76, 0x1f, 0x31, 0x8f, 0xb9, 0x31, 0x31, 0x16, 0x13, 0xf7, 0x09, 0x8d, 0xfe,
	0x07, 0x75, 0x3b, 0x22, 0x58, 0x10, 0x4b, 0x50, 0x9f, 0x18, 0xd5, 0xdd, 0xca, 0xde, 0x6c, 0x07,
	0x34, 0xeb, 0x8c, 0xfa, 0x4a, 0x21, 0x0e, 0x9d, 0x4c, 0xa1, 0xa6, 0x15, 0x34, 0x4b, 0x29, 0x20,
	0x98, 0x13, 0x82, 0x3a, 0x06, 0x28, 0xcb, 0xea, 0x8c, 0x36, 0x60, 0x3e, 0xec, 0xb2, 0x80, 0x18,
	0x75, 0xc5, 0xd4, 0x04, 0xda, 0x84, 0x05, 0xdc, 0xc3, 0x02, 0x47, 0xc6, 0x92, 0x62, 0x27, 0x94,
	0x8c, 0x2f, 0xa0, 0xf6, 0x65, 0x80, 0x7d, 0x62, 0x34, 0x74, 0x7c, 0x29, 0x2d, 0x8b, 0xc9, 0xc9,
	0x8d, 0xb1, 0xac, 0x8b, 0xc9, 0xc9, 0x8d, 0xd4, 0x3e, 0xa7, 0x91, 0xe8, 0x3a, 0xb8, 0x6f, 0xac,
	0x68, 0xed, 0x94, 0x46, 0xff, 0x85, 0x1a, 0xa7, 0x6e, 0x80, 0x45, 0x1c, 0x11, 0x63, 0x55, 0x09,
	0x07, 0x0c, 0xf4, 0x11, 0x2c, 0x87, 0x5d, 0x26, 0x98, 0x45, 0x7d, 0xd7, 0x8a, 0x23, 0x8f, 0x1b,
	0x6b, 0xbb, 0xb3, 0x7b, 0xb5, 0xce, 0x92, 0xe2, 0xb6, 0x7d, 0xf7, 0x4d, 0xe4, 0x71, 0x59, 0x63,
	0x72, 0x13, 0x5a, 0x1e, 0xe9, 0x11, 0xcf, 0x40, 0xba, 0xc6, 0xe4, 0x26, 0x7c, 0x21, 0x69, 0x59,
	0x0d, 0x1a, 0x5a, 0x1e, 0xb3, 0xb1, 0xa0, 0x2c, 0x30, 0xd6, 0x95, 0x0b, 0xa0, 0xe1, 0x8b, 0x84,
	0x23, 0xa3, 0xa3, 0x0e, 0x09, 0x04, 0x15, 0x7d, 0x63, 0x43, 0x47, 0x97, 0xd2, 0xb2, 0x52, 0x0e,
	0xe1, 0xb6, 0xf1, 0x1f, 0x5d, 0x29, 0x79, 0x96, 0x3c, 0xce, 0x22, 0x61, 0x6c, 0x2a, 0x47, 0xea,
	0x8c, 0xee, 0xc1, 0x52, 0x8a, 0x4c, 0x81, 0x5d, 0x6e, 0x6c, 0xa9, 0x28, 0xeb, 0x09, 0xef, 0x0c,
	0xbb, 0x1c, 0x3d, 0x81, 0x6d, 0xca, 0x2d, 0xde, 0x65, 0xd7, 0x16, 0x0d, 0x2c, 0xbb, 0x8b, 0x85,
	0x65, 0xe3, 0xc8, 0xb1, 0xae, 0xb1, 0xe7, 0x19, 0xc6, 0x6e, 0x65, 0xaf, 0xda, 0xd9, 0xa0, 0xfc,
	0x75, 0x97, 0x5d, 0xb7, 0x83, 0x93, 0x2e, 0x16, 0x27, 0x38, 0x72, 0x7e, 0xc0, 0x9e, 0x67, 0xfe,
	0x3c, 0x0f, 0x9b, 0x27, 0xea, 0x75, 0x33, 0xd0, 0x76, 0xc8, 0x55, 0x4c, 0xb8, 0x40, 0x2f, 0x61,
	0x31, 0x71, 0xa1, 0xb0, 0x5b, 0x3f, 0x7c, 0xd4, 0x2a, 0xe8, 0x92, 0x56, 0xf1, 0xed, 0x56, 0x4a,
	0xa6, 0x36, 0x9a, 0xbf, 0xcc, 0xc1, 0x62, 0xc2, 0x1c, 0xe0, 0xa1, 0x92, 0xc7, 0xc3, 0xbf, 0xb8,
	0x2d, 0x06, 0x50, 0xad, 0x96, 0x42, 0xb5, 0x56, 0x0c, 0x55, 0x28, 0x86, 0x6a, 0x7d, 0x12, 0x54,
	0x97, 0x26, 0x42, 0xf5, 0x92, 0xf4, 0xb9, 0xd1, 0x18, 0x86, 0xea, 0xd7, 0xa4, 0xcf, 0x65, 0x6a,
	0xd8, 0x71, 0x2c, 0x72, 0x13, 0x26, 0x0d, 0xb2, 0x80, 0x1d, 0xe7, 0xf4, 0x26, 0x1c, 0x42, 0xe1,
	0x4a, 0x09, 0x0a, 0x57, 0x0b, 0x50, 0xb8, 0x36, 0x01, 0x85, 0xe8, 0x96, 0x28, 0x5c, 0x9f, 0x80,
	0xc2, 0x07, 0xb0, 0x35, 0x06, 0x23, 0x1e, 0xb2, 0x80, 0x13, 0xf5, 0xf0, 0xe9, 0xf0, 0x9c, 0xa1,
	0x8e, 0xf9, 0xc7, 0x02, 0x6c, 0xbe, 0x51, 0xd3, 0xe6, 0x7d, 0x01, 0x5b, 0x7c, 0x7b, 0x0c, 0xb0,
	0xc8, 0x82, 0xa5, 0x64, 0xd2, 0x5d, 0x78, 0x32, 0xe1, 0x99, 0xdd, 0xd9, 0xbd, 0xfa, 0xe1, 0xd3,
	0xdb, 0xd8, 0xd4, 0xec, 0x2f, 0xe5, 0xf5, 0xd3, 0x40, 0x44, 0xfd, 0x4e, 0x32, 0x3b, 0x15, 0xa7,
	0xf9, 0x5b, 0xae, 0x23, 0x3e, 0xa8, 0x25, 0xf1, 0x81, 0x77, 0x43, 0x36, 0x7d, 0x56, 0x46, 0xa6,
	0x4f, 0xd6, 0x23, 0xab, 0x25, 0x3d, 0xb2, 0x56, 0xd0, 0x23, 0x68, 0x42, 0x8f, 0xac, 0xdf, 0xb2,
	0x47, 0x36, 0xca, 0x7b, 0xa4, 0xf9, 0x39, 0xac, 0x8e, 0xc2, 0x49, 0x16, 0xf5, 0x92, 0xf4, 0x93,
	0x29, 0x2a, 0x8f, 0x32, 0xb7, 0x1e, 0xf6, 0x62, 0xa2, 0x20, 0x53, 0xed, 0x68, 0xe2, 0x68, 0xe6,
	0xd3, 0x8a, 0xb9, 0x0d, 0x5b, 0x63, 0x28, 0xd5, 0x3d, 0x66, 0x3e, 0x83, 0xe6, 0x9b, 0x20, 0x22,
	0x2e, 0xe5, 0x82, 0x44, 0x63, 0x6d, 0x35, 0x0e, 0xcd, 0x74, 0xc5, 0xcf, 0x0c, 0x56, 0xbc, 0xb9,
	0x03, 0x77, 0x0b, 0x6d, 0x24, 0x2e, 0x7e, 0x82, 0xad, 0xe7, 0x44, 0xbc, 0xc2, 0xee, 0x78, 0xdb,
	0x22, 0x98, 0x0b, 0xb1, 0x4b, 0x12, 0x07, 0xea, 0xac, 0x8a, 0x4b, 0xdf, 0xea, 0x2c, 0x64, 0x71,
	0xe9, 0x5b, 0x82, 0x8e, 0xa0, 0x39, 0x5c, 0x2e, 0x5d, 0x45, 0x2e, 0xb0, 0x88, 0xb9, 0x6a, 0x86,
	0x46, 0x67, 0xd3, 0xce, 0x95, 0x4c, 0x16, 0xf1, 0xb5, 0x92, 0x9a, 0xd7, 0x60, 0x8c, 0xbb, 0x4f,
	0x26, 0xcc, 0xe9, 0xe0, 0xd1, 0x3c, 0xca, 0xe5, 0xec, 0x90, 0x7d, 0x6e, 0x16, 0xf6, 0xf9, 0xd0,
	0x2f, 0xbb, 0xec, 0x61, 0x5f, 0x50, 0xae, 0x76, 0x9a, 0x60, 0x02, 0x7b, 0x49, 0xcc, 0x9a, 0x30,
	0xbb, 0xb0, 0xfe, 0x9c, 0x88, 0x29, 0x6a, 0xfa, 0x05, 0x40, 0x44, 0xae, 0x2c, 0xce, 0xe2, 0xc8,
	0xd6, 0x79, 0x2f, 0x1f, 0xee, 0x0e, 0xc5, 0xd0, 0xca, 0x1b, 0x7a, 0xad, 0xf4, 0x3a, 0xb5, 0x88,
	0x5c, 0xe9, 0xa3, 0x79, 0x06, 0x1b, 0xc3, 0x9e, 0x92, 0xf4, 0x9e, 0x8e, 0x4e, 0xc5, 0x69, 0x32,
	0x4b, 0xaf, 0x98, 0x8f, 0xc1, 0x78, 0x86, 0x85, 0xdd, 0x2d, 0x4a, 0x62, 0x1b, 0xaa, 0x31, 0x75,
	0x06, 0x45, 0x6b, 0x74, 0x16, 0x63, 0xea, 0xc8, 0x62, 0x98, 0xe7, 0xb0, 0x5d, 0x70, 0xed, 0x1f,
	0x2d, 0xb8, 0xf9, 0x19, 0x2c, 0x1d, 0x3b, 0xce, 0x31, 0x7d, 0xc5, 0xb8, 0x0c, 0xa9, 0xa0, 0xa6,
	0x5b, 0xb0, 0x18, 0x32, 0xae, 0x86, 0xa2, 0x86, 0xea, 0x82, 0x24, 0xdb, 0x8e, 0xb9, 0x02, 0x8d,
	0xdc, 0x55, 0x1e, 0x9a, 0x5f, 0xc1, 0x92, 0x0c, 0x35, 0xb3, 0x35, 0x2d, 0x26, 0x13, 0x9f, 0xb3,
	0x99, 0x4f, 0xb3, 0x0b, 0x8d, 0x9c, 0x25, 0x1e, 0xa2, 0x23, 0xa8, 0xa9, 0x20, 0x72, 0xa9, 0xee,
	0x14, 0xa6, 0x2a, 0x6f, 0xa8, 0x2c, 0xab, 0x52, 0x5f, 0x61, 0x4a, 0x8e, 0x6e, 0x09, 0x23, 0x2b,
	0x88, 0xfd, 0xc4, 0x6f, 0x55, 0x31, 0xbe, 0x89, 0x7d, 0xf3, 0xd7, 0x0a, 0x54, 0xd3, 0x3b, 0xf9,
	0x54, 0x2b, 0xf9, 0x54, 0x91, 0x01, 0x8b, 0x36, 0x0b, 0x04, 0x09, 0x44, 0x52, 0x83, 0x94, 0x44,
	0xa7, 0x50, 0xc7, 0x42, 0x60, 0xbb, 0xeb, 0x93, 0x40, 0xc8, 0x06, 0x92, 0xa1, 0xfd, 0xbf, 0xf8,
	0x15, 0x32, 0xbd, 0xe4, 0x19, 0x06, 0xf7, 0x46, 0xbf, 0x18, 0xe4, 0xf6, 0x99, 0xcb, 0x7f, 0x31,
	0x98, 0x7f, 0x55, 0x60, 0x79, 0xd8, 0x00, 0xba, 0x0f, 0x2b, 0x03, 0x13, 0x96, 0xe8, 0x87, 0x69,
	0xa5, 0x97, 0x07, 0xec, 0xb3, 0x7e, 0x48, 0xd0, 0x3e, 0xa0, 0x9c, 0xe2, 0x70, 0x22, 0x6b, 0x03,
	0xc9, 0x49, 0x92, 0xd2, 0x0e, 0x00, 0xb9, 0x11, 0x11, 0xb6, 0x68, 0x70, 0xc1, 0x92, 0xfd, 0x58,
	0x53, 0x9c, 0xf4, 0x4b, 0x4c, 0x8e, 0xcb, 0xb9, 0xc1, 0xb8, 0xdc, 0x01, 0xe8, 0xf9, 0x99, 0xdd,
	0x79, 0x7d, 0xa1, 0xe7, 0xa7, 0xf6, 0xe4, 0xa6, 0xc0, 0x11, 0xf6, 0xd5, 0x6e, 0x94, 0x9b, 0x42,
	0x12, 0xe8, 0x21, 0x20, 0x16, 0x51, 0x97, 0x06, 0x56, 0x8f, 0x3a, 0x84, 0x59, 0x36, 0xeb, 0x91,
	0x28, 0x59, 0x92, 0xab, 0x5a, 0xf2, 0xbd, 0x14, 0x9c, 0x48, 0xbe, 0xf9, 0x5d, 0xf6, 0x7b, 0x85,
	0x76, 0x18, 0xf3, 0x4f, 0x2e, 0xdc, 0xb4, 0x7f, 0x9e, 0x40, 0x1d, 0x53, 0x2b, 0x62, 0xcc, 0xb7,
	0xec, 0x0b, 0x37, 0xe9, 0xce, 0xad, 0xe1, 0x9e, 0x1f, 0x5c, 0xaa, 0xe1, 0xf4, 0x98, 0x1b, 0xe5,
	0x03, 0x93, 0xc9, 0x9c, 0xbd, 0xaf, 0xe7, 0xcd, 0xa8, 0xab, 0xb1, 0xde, 0x30, 0xbf, 0xd5, 0xe3,
	0x62, 0xd4, 0xc0, 0x7b, 0x07, 0x75, 0xf8, 0x67, 0x15, 0xd6, 0x8e, 0xa9, 0x6b, 0x27, 0xfd, 0xfa,
	0x52, 0x21, 0x07, 0x05, 0xb0, 0x32, 0xf2, 0xcb, 0x0e, 0x7d, 0x7c, 0x8b, 0xcf, 0x88, 0xe6, 0xc3,
	0xe9, 0x94, 0x93, 0xec, 0xef, 0x48, 0x7f, 0x23, 0x5b, 0xae, 0xc4, 0x5f, 0xf1, 0x2f, 0xb6, 0x12,
	0x7f, 0x65, 0x8b, 0xf3, 0x0e, 0x7a, 0x0b, 0xeb, 0x05, 0x6b, 0x0f, 0x1d, 0x14, 0x9b, 0x29, 0x5d,
	0xb2, 0xcd, 0x4f, 0xa6, 0xbf, 0x90, 0xf9, 0xbe, 0x82, 0xd5, 0xd1, 0xa5, 0x86, 0x8a, 0xe3, 0x2f,
	0x59, 0xbd, 0xcd, 0xfd, 0x29, 0xb5, 0x33, 0x97, 0x44, 0xcf, 0xc9, 0xcc, 0xdd, 0x5e, 0x99, 0x81,
	0x31, 0x57, 0x0f, 0xa6, 0xd0, 0xcc, 0xdc, 0x08, 0x58, 0x1b, 0x5b, 0x1f, 0xa8, 0x38, 0xd8, 0xb2,
	0xed, 0xd4, 0x6c, 0x4d, 0xab, 0x9e, 0x79, 0x3d, 0x83, 0x5a, 0xb6, 0x15, 0xd0, 0xbd, 0xe2, 0x41,
	0x98, 0x5b, 0x38, 0x4d, 0xf3, 0x5d, 0x2a, 0x3c, 0xd4, 0x56, 0xb3, 0x85, 0x50, 0x62, 0x35, 0xbf,
	0x7a, 0x4a, 0xac, 0x0e, 0xed, 0x94, 0x21, 0x9c, 0xa7, 0x0d, 0x38, 0x19, 0xe7, 0x23, 0x03, 0x61,
	0x32, 0xce, 0xc7, 0xa6, 0x4a, 0xf6, 0xf0, 0x99, 0xb3, 0xf2, 0x87, 0x1f, 0xf5, 0xf4, 0x60, 0x0a,
	0xcd, 0xd4, 0xcd, 0xb3, 0xa7, 0x3f, 0x1e, 0xb9, 0xcc, 0xc3, 0x81, 0xdb, 0x7a, 0x7c, 0x28, 0x44,
	0xcb, 0x66, 0xfe, 0x81, 0xfa, 0xb7, 0x9a, 0xcd, 0xbc, 0x03, 0x4e, 0xa2, 0x1e, 0xb5, 0x09, 0x2f,
	0xfd, 0xff, 0xde, 0xf9, 0x82, 0xd2, 0x7d, 0xf4, 0x77, 0x00, 0x00, 0x00, 0xff, 0xff, 0xd9, 0x47,
	0xe6, 0x4c, 0x11, 0x14, 0x00, 0x00,
}
