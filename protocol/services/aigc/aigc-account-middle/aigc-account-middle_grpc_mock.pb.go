// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/aigc/aigc-account-middle/aigc-account-middle.proto

package aigc_account_middle

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAigcAccountMiddleClient is a mock of AigcAccountMiddleClient interface.
type MockAigcAccountMiddleClient struct {
	ctrl     *gomock.Controller
	recorder *MockAigcAccountMiddleClientMockRecorder
}

// MockAigcAccountMiddleClientMockRecorder is the mock recorder for MockAigcAccountMiddleClient.
type MockAigcAccountMiddleClientMockRecorder struct {
	mock *MockAigcAccountMiddleClient
}

// NewMockAigcAccountMiddleClient creates a new mock instance.
func NewMockAigcAccountMiddleClient(ctrl *gomock.Controller) *MockAigcAccountMiddleClient {
	mock := &MockAigcAccountMiddleClient{ctrl: ctrl}
	mock.recorder = &MockAigcAccountMiddleClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcAccountMiddleClient) EXPECT() *MockAigcAccountMiddleClientMockRecorder {
	return m.recorder
}

// AddAiPost mocks base method.
func (m *MockAigcAccountMiddleClient) AddAiPost(ctx context.Context, in *AddAiPostReq, opts ...grpc.CallOption) (*AddAiPostResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddAiPost", varargs...)
	ret0, _ := ret[0].(*AddAiPostResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAiPost indicates an expected call of AddAiPost.
func (mr *MockAigcAccountMiddleClientMockRecorder) AddAiPost(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAiPost", reflect.TypeOf((*MockAigcAccountMiddleClient)(nil).AddAiPost), varargs...)
}

// BatchGetAIAccount mocks base method.
func (m *MockAigcAccountMiddleClient) BatchGetAIAccount(ctx context.Context, in *BatchGetAIAccountRequest, opts ...grpc.CallOption) (*BatchGetAIAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetAIAccount", varargs...)
	ret0, _ := ret[0].(*BatchGetAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAIAccount indicates an expected call of BatchGetAIAccount.
func (mr *MockAigcAccountMiddleClientMockRecorder) BatchGetAIAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAIAccount", reflect.TypeOf((*MockAigcAccountMiddleClient)(nil).BatchGetAIAccount), varargs...)
}

// CreateAIAccount mocks base method.
func (m *MockAigcAccountMiddleClient) CreateAIAccount(ctx context.Context, in *CreateAIAccountRequest, opts ...grpc.CallOption) (*CreateAIAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAIAccount", varargs...)
	ret0, _ := ret[0].(*CreateAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAIAccount indicates an expected call of CreateAIAccount.
func (mr *MockAigcAccountMiddleClientMockRecorder) CreateAIAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAIAccount", reflect.TypeOf((*MockAigcAccountMiddleClient)(nil).CreateAIAccount), varargs...)
}

// GetAIAccount mocks base method.
func (m *MockAigcAccountMiddleClient) GetAIAccount(ctx context.Context, in *GetAIAccountRequest, opts ...grpc.CallOption) (*GetAIAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAIAccount", varargs...)
	ret0, _ := ret[0].(*GetAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIAccount indicates an expected call of GetAIAccount.
func (mr *MockAigcAccountMiddleClientMockRecorder) GetAIAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIAccount", reflect.TypeOf((*MockAigcAccountMiddleClient)(nil).GetAIAccount), varargs...)
}

// GetAiPost mocks base method.
func (m *MockAigcAccountMiddleClient) GetAiPost(ctx context.Context, in *GetAiPostReq, opts ...grpc.CallOption) (*GetAiPostResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAiPost", varargs...)
	ret0, _ := ret[0].(*GetAiPostResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiPost indicates an expected call of GetAiPost.
func (mr *MockAigcAccountMiddleClientMockRecorder) GetAiPost(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiPost", reflect.TypeOf((*MockAigcAccountMiddleClient)(nil).GetAiPost), varargs...)
}

// GetAiRoomCfg mocks base method.
func (m *MockAigcAccountMiddleClient) GetAiRoomCfg(ctx context.Context, in *GetAiRoomCfgRequest, opts ...grpc.CallOption) (*GetAiRoomCfgResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAiRoomCfg", varargs...)
	ret0, _ := ret[0].(*GetAiRoomCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiRoomCfg indicates an expected call of GetAiRoomCfg.
func (mr *MockAigcAccountMiddleClientMockRecorder) GetAiRoomCfg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiRoomCfg", reflect.TypeOf((*MockAigcAccountMiddleClient)(nil).GetAiRoomCfg), varargs...)
}

// GetPageAIAccount mocks base method.
func (m *MockAigcAccountMiddleClient) GetPageAIAccount(ctx context.Context, in *GetPageAIAccountRequest, opts ...grpc.CallOption) (*GetPageAIAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPageAIAccount", varargs...)
	ret0, _ := ret[0].(*GetPageAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPageAIAccount indicates an expected call of GetPageAIAccount.
func (mr *MockAigcAccountMiddleClientMockRecorder) GetPageAIAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPageAIAccount", reflect.TypeOf((*MockAigcAccountMiddleClient)(nil).GetPageAIAccount), varargs...)
}

// UnregisterAIAccount mocks base method.
func (m *MockAigcAccountMiddleClient) UnregisterAIAccount(ctx context.Context, in *UnregisterAIAccountRequest, opts ...grpc.CallOption) (*UnregisterAIAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnregisterAIAccount", varargs...)
	ret0, _ := ret[0].(*UnregisterAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnregisterAIAccount indicates an expected call of UnregisterAIAccount.
func (mr *MockAigcAccountMiddleClientMockRecorder) UnregisterAIAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnregisterAIAccount", reflect.TypeOf((*MockAigcAccountMiddleClient)(nil).UnregisterAIAccount), varargs...)
}

// UpdateAIAccount mocks base method.
func (m *MockAigcAccountMiddleClient) UpdateAIAccount(ctx context.Context, in *UpdateAIAccountRequest, opts ...grpc.CallOption) (*UpdateAIAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAIAccount", varargs...)
	ret0, _ := ret[0].(*UpdateAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAIAccount indicates an expected call of UpdateAIAccount.
func (mr *MockAigcAccountMiddleClientMockRecorder) UpdateAIAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIAccount", reflect.TypeOf((*MockAigcAccountMiddleClient)(nil).UpdateAIAccount), varargs...)
}

// UpdateAiRoomCfg mocks base method.
func (m *MockAigcAccountMiddleClient) UpdateAiRoomCfg(ctx context.Context, in *UpdateAiRoomCfgRequest, opts ...grpc.CallOption) (*UpdateAiRoomCfgResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAiRoomCfg", varargs...)
	ret0, _ := ret[0].(*UpdateAiRoomCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAiRoomCfg indicates an expected call of UpdateAiRoomCfg.
func (mr *MockAigcAccountMiddleClientMockRecorder) UpdateAiRoomCfg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAiRoomCfg", reflect.TypeOf((*MockAigcAccountMiddleClient)(nil).UpdateAiRoomCfg), varargs...)
}

// MockAigcAccountMiddleServer is a mock of AigcAccountMiddleServer interface.
type MockAigcAccountMiddleServer struct {
	ctrl     *gomock.Controller
	recorder *MockAigcAccountMiddleServerMockRecorder
}

// MockAigcAccountMiddleServerMockRecorder is the mock recorder for MockAigcAccountMiddleServer.
type MockAigcAccountMiddleServerMockRecorder struct {
	mock *MockAigcAccountMiddleServer
}

// NewMockAigcAccountMiddleServer creates a new mock instance.
func NewMockAigcAccountMiddleServer(ctrl *gomock.Controller) *MockAigcAccountMiddleServer {
	mock := &MockAigcAccountMiddleServer{ctrl: ctrl}
	mock.recorder = &MockAigcAccountMiddleServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcAccountMiddleServer) EXPECT() *MockAigcAccountMiddleServerMockRecorder {
	return m.recorder
}

// AddAiPost mocks base method.
func (m *MockAigcAccountMiddleServer) AddAiPost(ctx context.Context, in *AddAiPostReq) (*AddAiPostResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAiPost", ctx, in)
	ret0, _ := ret[0].(*AddAiPostResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAiPost indicates an expected call of AddAiPost.
func (mr *MockAigcAccountMiddleServerMockRecorder) AddAiPost(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAiPost", reflect.TypeOf((*MockAigcAccountMiddleServer)(nil).AddAiPost), ctx, in)
}

// BatchGetAIAccount mocks base method.
func (m *MockAigcAccountMiddleServer) BatchGetAIAccount(ctx context.Context, in *BatchGetAIAccountRequest) (*BatchGetAIAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAIAccount", ctx, in)
	ret0, _ := ret[0].(*BatchGetAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAIAccount indicates an expected call of BatchGetAIAccount.
func (mr *MockAigcAccountMiddleServerMockRecorder) BatchGetAIAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAIAccount", reflect.TypeOf((*MockAigcAccountMiddleServer)(nil).BatchGetAIAccount), ctx, in)
}

// CreateAIAccount mocks base method.
func (m *MockAigcAccountMiddleServer) CreateAIAccount(ctx context.Context, in *CreateAIAccountRequest) (*CreateAIAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAIAccount", ctx, in)
	ret0, _ := ret[0].(*CreateAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAIAccount indicates an expected call of CreateAIAccount.
func (mr *MockAigcAccountMiddleServerMockRecorder) CreateAIAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAIAccount", reflect.TypeOf((*MockAigcAccountMiddleServer)(nil).CreateAIAccount), ctx, in)
}

// GetAIAccount mocks base method.
func (m *MockAigcAccountMiddleServer) GetAIAccount(ctx context.Context, in *GetAIAccountRequest) (*GetAIAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAIAccount", ctx, in)
	ret0, _ := ret[0].(*GetAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAIAccount indicates an expected call of GetAIAccount.
func (mr *MockAigcAccountMiddleServerMockRecorder) GetAIAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAIAccount", reflect.TypeOf((*MockAigcAccountMiddleServer)(nil).GetAIAccount), ctx, in)
}

// GetAiPost mocks base method.
func (m *MockAigcAccountMiddleServer) GetAiPost(ctx context.Context, in *GetAiPostReq) (*GetAiPostResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAiPost", ctx, in)
	ret0, _ := ret[0].(*GetAiPostResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiPost indicates an expected call of GetAiPost.
func (mr *MockAigcAccountMiddleServerMockRecorder) GetAiPost(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiPost", reflect.TypeOf((*MockAigcAccountMiddleServer)(nil).GetAiPost), ctx, in)
}

// GetAiRoomCfg mocks base method.
func (m *MockAigcAccountMiddleServer) GetAiRoomCfg(ctx context.Context, in *GetAiRoomCfgRequest) (*GetAiRoomCfgResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAiRoomCfg", ctx, in)
	ret0, _ := ret[0].(*GetAiRoomCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAiRoomCfg indicates an expected call of GetAiRoomCfg.
func (mr *MockAigcAccountMiddleServerMockRecorder) GetAiRoomCfg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAiRoomCfg", reflect.TypeOf((*MockAigcAccountMiddleServer)(nil).GetAiRoomCfg), ctx, in)
}

// GetPageAIAccount mocks base method.
func (m *MockAigcAccountMiddleServer) GetPageAIAccount(ctx context.Context, in *GetPageAIAccountRequest) (*GetPageAIAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPageAIAccount", ctx, in)
	ret0, _ := ret[0].(*GetPageAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPageAIAccount indicates an expected call of GetPageAIAccount.
func (mr *MockAigcAccountMiddleServerMockRecorder) GetPageAIAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPageAIAccount", reflect.TypeOf((*MockAigcAccountMiddleServer)(nil).GetPageAIAccount), ctx, in)
}

// UnregisterAIAccount mocks base method.
func (m *MockAigcAccountMiddleServer) UnregisterAIAccount(ctx context.Context, in *UnregisterAIAccountRequest) (*UnregisterAIAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnregisterAIAccount", ctx, in)
	ret0, _ := ret[0].(*UnregisterAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnregisterAIAccount indicates an expected call of UnregisterAIAccount.
func (mr *MockAigcAccountMiddleServerMockRecorder) UnregisterAIAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnregisterAIAccount", reflect.TypeOf((*MockAigcAccountMiddleServer)(nil).UnregisterAIAccount), ctx, in)
}

// UpdateAIAccount mocks base method.
func (m *MockAigcAccountMiddleServer) UpdateAIAccount(ctx context.Context, in *UpdateAIAccountRequest) (*UpdateAIAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAIAccount", ctx, in)
	ret0, _ := ret[0].(*UpdateAIAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAIAccount indicates an expected call of UpdateAIAccount.
func (mr *MockAigcAccountMiddleServerMockRecorder) UpdateAIAccount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIAccount", reflect.TypeOf((*MockAigcAccountMiddleServer)(nil).UpdateAIAccount), ctx, in)
}

// UpdateAiRoomCfg mocks base method.
func (m *MockAigcAccountMiddleServer) UpdateAiRoomCfg(ctx context.Context, in *UpdateAiRoomCfgRequest) (*UpdateAiRoomCfgResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAiRoomCfg", ctx, in)
	ret0, _ := ret[0].(*UpdateAiRoomCfgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAiRoomCfg indicates an expected call of UpdateAiRoomCfg.
func (mr *MockAigcAccountMiddleServerMockRecorder) UpdateAiRoomCfg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAiRoomCfg", reflect.TypeOf((*MockAigcAccountMiddleServer)(nil).UpdateAiRoomCfg), ctx, in)
}
