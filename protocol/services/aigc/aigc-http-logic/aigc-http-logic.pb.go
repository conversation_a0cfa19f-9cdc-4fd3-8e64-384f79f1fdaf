// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/aigc-http-logic/aigc-http-logic.proto

package aigc_http_logic // import "golang.52tt.com/protocol/services/aigc/aigc-http-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 送审结果
type AuditResult int32

const (
	// 机审无法识别
	AuditResult_AuditResultReview AuditResult = 0
	// 通过
	AuditResult_AuditResultPass AuditResult = 1
	// 不通过
	AuditResult_AuditResultReject AuditResult = 2
)

var AuditResult_name = map[int32]string{
	0: "AuditResultReview",
	1: "AuditResultPass",
	2: "AuditResultReject",
}
var AuditResult_value = map[string]int32{
	"AuditResultReview": 0,
	"AuditResultPass":   1,
	"AuditResultReject": 2,
}

func (x AuditResult) String() string {
	return proto.EnumName(AuditResult_name, int32(x))
}
func (AuditResult) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{0}
}

type LikeState int32

const (
	LikeState_LIKE_STATE_UNSPECIFIED LikeState = 0
	LikeState_LIKE_STATE_LIKED       LikeState = 1
	LikeState_LIKE_STATE_UNLIKED     LikeState = 2
)

var LikeState_name = map[int32]string{
	0: "LIKE_STATE_UNSPECIFIED",
	1: "LIKE_STATE_LIKED",
	2: "LIKE_STATE_UNLIKED",
}
var LikeState_value = map[string]int32{
	"LIKE_STATE_UNSPECIFIED": 0,
	"LIKE_STATE_LIKED":       1,
	"LIKE_STATE_UNLIKED":     2,
}

func (x LikeState) String() string {
	return proto.EnumName(LikeState_name, int32(x))
}
func (LikeState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{1}
}

// 创作者信息展示类型
type CreatorInfoType int32

const (
	CreatorInfoType_CREATOR_INFO_TYPE_ANONYMOUS CreatorInfoType = 0
	CreatorInfoType_CREATOR_INFO_TYPE_PUBLIC    CreatorInfoType = 1
	CreatorInfoType_CREATOR_INFO_TYPE_HIDE      CreatorInfoType = 2
)

var CreatorInfoType_name = map[int32]string{
	0: "CREATOR_INFO_TYPE_ANONYMOUS",
	1: "CREATOR_INFO_TYPE_PUBLIC",
	2: "CREATOR_INFO_TYPE_HIDE",
}
var CreatorInfoType_value = map[string]int32{
	"CREATOR_INFO_TYPE_ANONYMOUS": 0,
	"CREATOR_INFO_TYPE_PUBLIC":    1,
	"CREATOR_INFO_TYPE_HIDE":      2,
}

func (x CreatorInfoType) String() string {
	return proto.EnumName(CreatorInfoType_name, int32(x))
}
func (CreatorInfoType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{2}
}

type RelationType int32

const (
	RelationType_RELATION_TYPE_UNSPECIFIED  RelationType = 0
	RelationType_RELATION_TYPE_RELATE       RelationType = 1
	RelationType_RELATION_TYPE_SAME_CREATOR RelationType = 2
)

var RelationType_name = map[int32]string{
	0: "RELATION_TYPE_UNSPECIFIED",
	1: "RELATION_TYPE_RELATE",
	2: "RELATION_TYPE_SAME_CREATOR",
}
var RelationType_value = map[string]int32{
	"RELATION_TYPE_UNSPECIFIED":  0,
	"RELATION_TYPE_RELATE":       1,
	"RELATION_TYPE_SAME_CREATOR": 2,
}

func (x RelationType) String() string {
	return proto.EnumName(RelationType_name, int32(x))
}
func (RelationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{3}
}

type AttitudeAction int32

const (
	AttitudeAction_ATTITUDE_ACTION_UNSPECIFIED AttitudeAction = 0
	AttitudeAction_ATTITUDE_ACTION_LIKE        AttitudeAction = 1
	AttitudeAction_ATTITUDE_ACTION_UNLIKE      AttitudeAction = 2
)

var AttitudeAction_name = map[int32]string{
	0: "ATTITUDE_ACTION_UNSPECIFIED",
	1: "ATTITUDE_ACTION_LIKE",
	2: "ATTITUDE_ACTION_UNLIKE",
}
var AttitudeAction_value = map[string]int32{
	"ATTITUDE_ACTION_UNSPECIFIED": 0,
	"ATTITUDE_ACTION_LIKE":        1,
	"ATTITUDE_ACTION_UNLIKE":      2,
}

func (x AttitudeAction) String() string {
	return proto.EnumName(AttitudeAction_name, int32(x))
}
func (AttitudeAction) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{4}
}

type ObjectType int32

const (
	ObjectType_OBJECT_TYPE_UNSPECIFIED    ObjectType = 0
	ObjectType_OBJECT_TYPE_GROUP_TEMPLATE ObjectType = 1
)

var ObjectType_name = map[int32]string{
	0: "OBJECT_TYPE_UNSPECIFIED",
	1: "OBJECT_TYPE_GROUP_TEMPLATE",
}
var ObjectType_value = map[string]int32{
	"OBJECT_TYPE_UNSPECIFIED":    0,
	"OBJECT_TYPE_GROUP_TEMPLATE": 1,
}

func (x ObjectType) String() string {
	return proto.EnumName(ObjectType_name, int32(x))
}
func (ObjectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{5}
}

type Entity_Type int32

const (
	Entity_TYPE_UNSPECIFIED    Entity_Type = 0
	Entity_TYPE_ROLE           Entity_Type = 1
	Entity_TYPE_GROUP_TEMPLATE Entity_Type = 2
	Entity_TYPE_PARTNER        Entity_Type = 3
)

var Entity_Type_name = map[int32]string{
	0: "TYPE_UNSPECIFIED",
	1: "TYPE_ROLE",
	2: "TYPE_GROUP_TEMPLATE",
	3: "TYPE_PARTNER",
}
var Entity_Type_value = map[string]int32{
	"TYPE_UNSPECIFIED":    0,
	"TYPE_ROLE":           1,
	"TYPE_GROUP_TEMPLATE": 2,
	"TYPE_PARTNER":        3,
}

func (x Entity_Type) String() string {
	return proto.EnumName(Entity_Type_name, int32(x))
}
func (Entity_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{21, 0}
}

type TemplateMsg_SenderType int32

const (
	TemplateMsg_SenderType_Unspecified TemplateMsg_SenderType = 0
	TemplateMsg_SenderType_AI          TemplateMsg_SenderType = 1
	TemplateMsg_SenderType_User        TemplateMsg_SenderType = 2
)

var TemplateMsg_SenderType_name = map[int32]string{
	0: "SenderType_Unspecified",
	1: "SenderType_AI",
	2: "SenderType_User",
}
var TemplateMsg_SenderType_value = map[string]int32{
	"SenderType_Unspecified": 0,
	"SenderType_AI":          1,
	"SenderType_User":        2,
}

func (x TemplateMsg_SenderType) String() string {
	return proto.EnumName(TemplateMsg_SenderType_name, int32(x))
}
func (TemplateMsg_SenderType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{83, 0}
}

type ChatBackground_State int32

const (
	ChatBackground_STATE_UNSPECIFIED ChatBackground_State = 0
	ChatBackground_STATE_UNLOCK      ChatBackground_State = 1
	ChatBackground_STATE_LOCK        ChatBackground_State = 2
	ChatBackground_STATE_USING       ChatBackground_State = 3
)

var ChatBackground_State_name = map[int32]string{
	0: "STATE_UNSPECIFIED",
	1: "STATE_UNLOCK",
	2: "STATE_LOCK",
	3: "STATE_USING",
}
var ChatBackground_State_value = map[string]int32{
	"STATE_UNSPECIFIED": 0,
	"STATE_UNLOCK":      1,
	"STATE_LOCK":        2,
	"STATE_USING":       3,
}

func (x ChatBackground_State) String() string {
	return proto.EnumName(ChatBackground_State_name, int32(x))
}
func (ChatBackground_State) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{110, 0}
}

type Relation_State int32

const (
	Relation_STATE_UNSPECIFIED Relation_State = 0
	Relation_STATE_UNLOCK      Relation_State = 1
	Relation_STATE_LOCK        Relation_State = 2
	Relation_STATE_USING       Relation_State = 3
)

var Relation_State_name = map[int32]string{
	0: "STATE_UNSPECIFIED",
	1: "STATE_UNLOCK",
	2: "STATE_LOCK",
	3: "STATE_USING",
}
var Relation_State_value = map[string]int32{
	"STATE_UNSPECIFIED": 0,
	"STATE_UNLOCK":      1,
	"STATE_LOCK":        2,
	"STATE_USING":       3,
}

func (x Relation_State) String() string {
	return proto.EnumName(Relation_State_name, int32(x))
}
func (Relation_State) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{116, 0}
}

type FilterItem_FilterType int32

const (
	FilterItem_FILTER_TYPE_UNSPECIFIED FilterItem_FilterType = 0
	// 女生
	FilterItem_FILTER_TYPE_SEX_FEMALE FilterItem_FilterType = 1
	// 男生
	FilterItem_FILTER_TYPE_SEX_MALE FilterItem_FilterType = 2
	// 所有性别
	FilterItem_FILTER_TYPE_SEX_ALL FilterItem_FilterType = 3
)

var FilterItem_FilterType_name = map[int32]string{
	0: "FILTER_TYPE_UNSPECIFIED",
	1: "FILTER_TYPE_SEX_FEMALE",
	2: "FILTER_TYPE_SEX_MALE",
	3: "FILTER_TYPE_SEX_ALL",
}
var FilterItem_FilterType_value = map[string]int32{
	"FILTER_TYPE_UNSPECIFIED": 0,
	"FILTER_TYPE_SEX_FEMALE":  1,
	"FILTER_TYPE_SEX_MALE":    2,
	"FILTER_TYPE_SEX_ALL":     3,
}

func (x FilterItem_FilterType) String() string {
	return proto.EnumName(FilterItem_FilterType_name, int32(x))
}
func (FilterItem_FilterType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{124, 0}
}

type StartScriptMatchRequest_MatchMode int32

const (
	StartScriptMatchRequest_MATCH_MODE_UNSPECIFIED StartScriptMatchRequest_MatchMode = 0
	// 指定剧本
	StartScriptMatchRequest_MATCH_MODE_SPECIFY_SCRIPT StartScriptMatchRequest_MatchMode = 1
	// 快速开玩
	StartScriptMatchRequest_MATCH_MODE_QUICK_PLAY StartScriptMatchRequest_MatchMode = 2
)

var StartScriptMatchRequest_MatchMode_name = map[int32]string{
	0: "MATCH_MODE_UNSPECIFIED",
	1: "MATCH_MODE_SPECIFY_SCRIPT",
	2: "MATCH_MODE_QUICK_PLAY",
}
var StartScriptMatchRequest_MatchMode_value = map[string]int32{
	"MATCH_MODE_UNSPECIFIED":    0,
	"MATCH_MODE_SPECIFY_SCRIPT": 1,
	"MATCH_MODE_QUICK_PLAY":     2,
}

func (x StartScriptMatchRequest_MatchMode) String() string {
	return proto.EnumName(StartScriptMatchRequest_MatchMode_name, int32(x))
}
func (StartScriptMatchRequest_MatchMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{131, 0}
}

type BaseRequest struct {
	DeviceId             string   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,4,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseRequest) Reset()         { *m = BaseRequest{} }
func (m *BaseRequest) String() string { return proto.CompactTextString(m) }
func (*BaseRequest) ProtoMessage()    {}
func (*BaseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{0}
}
func (m *BaseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseRequest.Unmarshal(m, b)
}
func (m *BaseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseRequest.Marshal(b, m, deterministic)
}
func (dst *BaseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseRequest.Merge(dst, src)
}
func (m *BaseRequest) XXX_Size() int {
	return xxx_messageInfo_BaseRequest.Size(m)
}
func (m *BaseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BaseRequest proto.InternalMessageInfo

func (m *BaseRequest) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *BaseRequest) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *BaseRequest) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *BaseRequest) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

type AIRoleCategory struct {
	Id                   string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	PropScenes           []uint32               `protobuf:"varint,3,rep,packed,name=prop_scenes,json=propScenes,proto3" json:"prop_scenes,omitempty"`
	Props                []*AIRoleCategory_Prop `protobuf:"bytes,4,rep,name=props,proto3" json:"props,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AIRoleCategory) Reset()         { *m = AIRoleCategory{} }
func (m *AIRoleCategory) String() string { return proto.CompactTextString(m) }
func (*AIRoleCategory) ProtoMessage()    {}
func (*AIRoleCategory) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{1}
}
func (m *AIRoleCategory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRoleCategory.Unmarshal(m, b)
}
func (m *AIRoleCategory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRoleCategory.Marshal(b, m, deterministic)
}
func (dst *AIRoleCategory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRoleCategory.Merge(dst, src)
}
func (m *AIRoleCategory) XXX_Size() int {
	return xxx_messageInfo_AIRoleCategory.Size(m)
}
func (m *AIRoleCategory) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRoleCategory.DiscardUnknown(m)
}

var xxx_messageInfo_AIRoleCategory proto.InternalMessageInfo

func (m *AIRoleCategory) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *AIRoleCategory) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *AIRoleCategory) GetPropScenes() []uint32 {
	if m != nil {
		return m.PropScenes
	}
	return nil
}

func (m *AIRoleCategory) GetProps() []*AIRoleCategory_Prop {
	if m != nil {
		return m.Props
	}
	return nil
}

type AIRoleCategory_Label struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Scenes               []uint32 `protobuf:"varint,3,rep,packed,name=scenes,proto3" json:"scenes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIRoleCategory_Label) Reset()         { *m = AIRoleCategory_Label{} }
func (m *AIRoleCategory_Label) String() string { return proto.CompactTextString(m) }
func (*AIRoleCategory_Label) ProtoMessage()    {}
func (*AIRoleCategory_Label) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{1, 0}
}
func (m *AIRoleCategory_Label) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRoleCategory_Label.Unmarshal(m, b)
}
func (m *AIRoleCategory_Label) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRoleCategory_Label.Marshal(b, m, deterministic)
}
func (dst *AIRoleCategory_Label) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRoleCategory_Label.Merge(dst, src)
}
func (m *AIRoleCategory_Label) XXX_Size() int {
	return xxx_messageInfo_AIRoleCategory_Label.Size(m)
}
func (m *AIRoleCategory_Label) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRoleCategory_Label.DiscardUnknown(m)
}

var xxx_messageInfo_AIRoleCategory_Label proto.InternalMessageInfo

func (m *AIRoleCategory_Label) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *AIRoleCategory_Label) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AIRoleCategory_Label) GetScenes() []uint32 {
	if m != nil {
		return m.Scenes
	}
	return nil
}

type AIRoleCategory_Prop struct {
	Id                   string                  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Labels               []*AIRoleCategory_Label `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels,omitempty"`
	LabelSelectLimit     uint32                  `protobuf:"varint,5,opt,name=label_select_limit,json=labelSelectLimit,proto3" json:"label_select_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AIRoleCategory_Prop) Reset()         { *m = AIRoleCategory_Prop{} }
func (m *AIRoleCategory_Prop) String() string { return proto.CompactTextString(m) }
func (*AIRoleCategory_Prop) ProtoMessage()    {}
func (*AIRoleCategory_Prop) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{1, 1}
}
func (m *AIRoleCategory_Prop) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRoleCategory_Prop.Unmarshal(m, b)
}
func (m *AIRoleCategory_Prop) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRoleCategory_Prop.Marshal(b, m, deterministic)
}
func (dst *AIRoleCategory_Prop) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRoleCategory_Prop.Merge(dst, src)
}
func (m *AIRoleCategory_Prop) XXX_Size() int {
	return xxx_messageInfo_AIRoleCategory_Prop.Size(m)
}
func (m *AIRoleCategory_Prop) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRoleCategory_Prop.DiscardUnknown(m)
}

var xxx_messageInfo_AIRoleCategory_Prop proto.InternalMessageInfo

func (m *AIRoleCategory_Prop) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *AIRoleCategory_Prop) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AIRoleCategory_Prop) GetLabels() []*AIRoleCategory_Label {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *AIRoleCategory_Prop) GetLabelSelectLimit() uint32 {
	if m != nil {
		return m.LabelSelectLimit
	}
	return 0
}

type CreatorInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	IsFollow             bool     `protobuf:"varint,4,opt,name=is_follow,json=isFollow,proto3" json:"is_follow,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreatorInfo) Reset()         { *m = CreatorInfo{} }
func (m *CreatorInfo) String() string { return proto.CompactTextString(m) }
func (*CreatorInfo) ProtoMessage()    {}
func (*CreatorInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{2}
}
func (m *CreatorInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatorInfo.Unmarshal(m, b)
}
func (m *CreatorInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatorInfo.Marshal(b, m, deterministic)
}
func (dst *CreatorInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatorInfo.Merge(dst, src)
}
func (m *CreatorInfo) XXX_Size() int {
	return xxx_messageInfo_CreatorInfo.Size(m)
}
func (m *CreatorInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatorInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CreatorInfo proto.InternalMessageInfo

func (m *CreatorInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CreatorInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreatorInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *CreatorInfo) GetIsFollow() bool {
	if m != nil {
		return m.IsFollow
	}
	return false
}

type AIRole struct {
	Id            uint32           `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type          uint32           `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Sex           int32            `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Name          string           `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Style         string           `protobuf:"bytes,5,opt,name=style,proto3" json:"style,omitempty"`
	State         uint32           `protobuf:"varint,6,opt,name=state,proto3" json:"state,omitempty"`
	Avatar        string           `protobuf:"bytes,7,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Image         string           `protobuf:"bytes,8,opt,name=image,proto3" json:"image,omitempty"`
	Intro         string           `protobuf:"bytes,9,opt,name=intro,proto3" json:"intro,omitempty"`
	DialogColor   string           `protobuf:"bytes,10,opt,name=dialog_color,json=dialogColor,proto3" json:"dialog_color,omitempty"`
	Character     string           `protobuf:"bytes,11,opt,name=character,proto3" json:"character,omitempty"`
	Prologue      string           `protobuf:"bytes,12,opt,name=prologue,proto3" json:"prologue,omitempty"`
	PrologueAudio string           `protobuf:"bytes,13,opt,name=prologue_audio,json=prologueAudio,proto3" json:"prologue_audio,omitempty"`
	Timbre        string           `protobuf:"bytes,14,opt,name=timbre,proto3" json:"timbre,omitempty"`
	CornerIcon    string           `protobuf:"bytes,15,opt,name=corner_icon,json=cornerIcon,proto3" json:"corner_icon,omitempty"`
	AuditResult   uint32           `protobuf:"varint,16,opt,name=audit_result,json=auditResult,proto3" json:"audit_result,omitempty"`
	Tags          []string         `protobuf:"bytes,17,rep,name=tags,proto3" json:"tags,omitempty"`
	Category      *AIRole_Category `protobuf:"bytes,18,opt,name=category,proto3" json:"category,omitempty"`
	// 是否开启推荐回复
	EnableRcmdReply bool   `protobuf:"varint,19,opt,name=enable_rcmd_reply,json=enableRcmdReply,proto3" json:"enable_rcmd_reply,omitempty"`
	LikeNum         uint32 `protobuf:"varint,20,opt,name=like_num,json=likeNum,proto3" json:"like_num,omitempty"`
	LikeState       uint32 `protobuf:"varint,21,opt,name=like_state,json=likeState,proto3" json:"like_state,omitempty"`
	StoryId         string `protobuf:"bytes,22,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	CreatorUid      uint32 `protobuf:"varint,23,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	Exposed         bool   `protobuf:"varint,24,opt,name=exposed,proto3" json:"exposed,omitempty"`
	// 透传从中台获取的角色扩展信息
	Ext             []byte       `protobuf:"bytes,25,opt,name=ext,proto3" json:"ext,omitempty"`
	StoryMode       uint32       `protobuf:"varint,26,opt,name=story_mode,json=storyMode,proto3" json:"story_mode,omitempty"`
	CreatorInfoType uint32       `protobuf:"varint,27,opt,name=creator_info_type,json=creatorInfoType,proto3" json:"creator_info_type,omitempty"`
	CreatorInfo     *CreatorInfo `protobuf:"bytes,28,opt,name=creator_info,json=creatorInfo,proto3" json:"creator_info,omitempty"`
	UserRoleSetting string       `protobuf:"bytes,29,opt,name=user_role_setting,json=userRoleSetting,proto3" json:"user_role_setting,omitempty"`
	// 使用/可见范围 see aigc-soulmate.proto enum AIRoleScope
	Scope                uint32   `protobuf:"varint,30,opt,name=scope,proto3" json:"scope,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIRole) Reset()         { *m = AIRole{} }
func (m *AIRole) String() string { return proto.CompactTextString(m) }
func (*AIRole) ProtoMessage()    {}
func (*AIRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{3}
}
func (m *AIRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRole.Unmarshal(m, b)
}
func (m *AIRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRole.Marshal(b, m, deterministic)
}
func (dst *AIRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRole.Merge(dst, src)
}
func (m *AIRole) XXX_Size() int {
	return xxx_messageInfo_AIRole.Size(m)
}
func (m *AIRole) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRole.DiscardUnknown(m)
}

var xxx_messageInfo_AIRole proto.InternalMessageInfo

func (m *AIRole) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIRole) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AIRole) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *AIRole) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AIRole) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

func (m *AIRole) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *AIRole) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *AIRole) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *AIRole) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *AIRole) GetDialogColor() string {
	if m != nil {
		return m.DialogColor
	}
	return ""
}

func (m *AIRole) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *AIRole) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *AIRole) GetPrologueAudio() string {
	if m != nil {
		return m.PrologueAudio
	}
	return ""
}

func (m *AIRole) GetTimbre() string {
	if m != nil {
		return m.Timbre
	}
	return ""
}

func (m *AIRole) GetCornerIcon() string {
	if m != nil {
		return m.CornerIcon
	}
	return ""
}

func (m *AIRole) GetAuditResult() uint32 {
	if m != nil {
		return m.AuditResult
	}
	return 0
}

func (m *AIRole) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *AIRole) GetCategory() *AIRole_Category {
	if m != nil {
		return m.Category
	}
	return nil
}

func (m *AIRole) GetEnableRcmdReply() bool {
	if m != nil {
		return m.EnableRcmdReply
	}
	return false
}

func (m *AIRole) GetLikeNum() uint32 {
	if m != nil {
		return m.LikeNum
	}
	return 0
}

func (m *AIRole) GetLikeState() uint32 {
	if m != nil {
		return m.LikeState
	}
	return 0
}

func (m *AIRole) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

func (m *AIRole) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

func (m *AIRole) GetExposed() bool {
	if m != nil {
		return m.Exposed
	}
	return false
}

func (m *AIRole) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *AIRole) GetStoryMode() uint32 {
	if m != nil {
		return m.StoryMode
	}
	return 0
}

func (m *AIRole) GetCreatorInfoType() uint32 {
	if m != nil {
		return m.CreatorInfoType
	}
	return 0
}

func (m *AIRole) GetCreatorInfo() *CreatorInfo {
	if m != nil {
		return m.CreatorInfo
	}
	return nil
}

func (m *AIRole) GetUserRoleSetting() string {
	if m != nil {
		return m.UserRoleSetting
	}
	return ""
}

func (m *AIRole) GetScope() uint32 {
	if m != nil {
		return m.Scope
	}
	return 0
}

type AIRole_Category struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIRole_Category) Reset()         { *m = AIRole_Category{} }
func (m *AIRole_Category) String() string { return proto.CompactTextString(m) }
func (*AIRole_Category) ProtoMessage()    {}
func (*AIRole_Category) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{3, 0}
}
func (m *AIRole_Category) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRole_Category.Unmarshal(m, b)
}
func (m *AIRole_Category) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRole_Category.Marshal(b, m, deterministic)
}
func (dst *AIRole_Category) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRole_Category.Merge(dst, src)
}
func (m *AIRole_Category) XXX_Size() int {
	return xxx_messageInfo_AIRole_Category.Size(m)
}
func (m *AIRole_Category) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRole_Category.DiscardUnknown(m)
}

var xxx_messageInfo_AIRole_Category proto.InternalMessageInfo

func (m *AIRole_Category) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *AIRole_Category) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type PetRole struct {
	Id        uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Sex       int32  `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Character string `protobuf:"bytes,5,opt,name=character,proto3" json:"character,omitempty"`
	Ext       []byte `protobuf:"bytes,6,opt,name=ext,proto3" json:"ext,omitempty"`
	// 领取页开场白
	CollectPrologue      *AIRolePrologue `protobuf:"bytes,7,opt,name=collect_prologue,json=collectPrologue,proto3" json:"collect_prologue,omitempty"`
	CornerIcon           string          `protobuf:"bytes,8,opt,name=corner_icon,json=cornerIcon,proto3" json:"corner_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *PetRole) Reset()         { *m = PetRole{} }
func (m *PetRole) String() string { return proto.CompactTextString(m) }
func (*PetRole) ProtoMessage()    {}
func (*PetRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{4}
}
func (m *PetRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetRole.Unmarshal(m, b)
}
func (m *PetRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetRole.Marshal(b, m, deterministic)
}
func (dst *PetRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetRole.Merge(dst, src)
}
func (m *PetRole) XXX_Size() int {
	return xxx_messageInfo_PetRole.Size(m)
}
func (m *PetRole) XXX_DiscardUnknown() {
	xxx_messageInfo_PetRole.DiscardUnknown(m)
}

var xxx_messageInfo_PetRole proto.InternalMessageInfo

func (m *PetRole) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetRole) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *PetRole) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PetRole) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *PetRole) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *PetRole) GetCollectPrologue() *AIRolePrologue {
	if m != nil {
		return m.CollectPrologue
	}
	return nil
}

func (m *PetRole) GetCornerIcon() string {
	if m != nil {
		return m.CornerIcon
	}
	return ""
}

// AI关系
type AIRelationship struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIRelationship) Reset()         { *m = AIRelationship{} }
func (m *AIRelationship) String() string { return proto.CompactTextString(m) }
func (*AIRelationship) ProtoMessage()    {}
func (*AIRelationship) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{5}
}
func (m *AIRelationship) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRelationship.Unmarshal(m, b)
}
func (m *AIRelationship) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRelationship.Marshal(b, m, deterministic)
}
func (dst *AIRelationship) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRelationship.Merge(dst, src)
}
func (m *AIRelationship) XXX_Size() int {
	return xxx_messageInfo_AIRelationship.Size(m)
}
func (m *AIRelationship) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRelationship.DiscardUnknown(m)
}

var xxx_messageInfo_AIRelationship proto.InternalMessageInfo

func (m *AIRelationship) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIRelationship) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// AI形象立绘
type AIPhotograph struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Avatar               string   `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Image                string   `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIPhotograph) Reset()         { *m = AIPhotograph{} }
func (m *AIPhotograph) String() string { return proto.CompactTextString(m) }
func (*AIPhotograph) ProtoMessage()    {}
func (*AIPhotograph) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{6}
}
func (m *AIPhotograph) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIPhotograph.Unmarshal(m, b)
}
func (m *AIPhotograph) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIPhotograph.Marshal(b, m, deterministic)
}
func (dst *AIPhotograph) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIPhotograph.Merge(dst, src)
}
func (m *AIPhotograph) XXX_Size() int {
	return xxx_messageInfo_AIPhotograph.Size(m)
}
func (m *AIPhotograph) XXX_DiscardUnknown() {
	xxx_messageInfo_AIPhotograph.DiscardUnknown(m)
}

var xxx_messageInfo_AIPhotograph proto.InternalMessageInfo

func (m *AIPhotograph) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIPhotograph) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *AIPhotograph) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

type CustomVoice struct {
	// 音色ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 权重 0.0~1.0
	Weight               float32  `protobuf:"fixed32,2,opt,name=weight,proto3" json:"weight,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomVoice) Reset()         { *m = CustomVoice{} }
func (m *CustomVoice) String() string { return proto.CompactTextString(m) }
func (*CustomVoice) ProtoMessage()    {}
func (*CustomVoice) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{7}
}
func (m *CustomVoice) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomVoice.Unmarshal(m, b)
}
func (m *CustomVoice) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomVoice.Marshal(b, m, deterministic)
}
func (dst *CustomVoice) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomVoice.Merge(dst, src)
}
func (m *CustomVoice) XXX_Size() int {
	return xxx_messageInfo_CustomVoice.Size(m)
}
func (m *CustomVoice) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomVoice.DiscardUnknown(m)
}

var xxx_messageInfo_CustomVoice proto.InternalMessageInfo

func (m *CustomVoice) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CustomVoice) GetWeight() float32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

type AIPartner struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// ta的名字
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 你希望ta怎么称呼你
	CallName string `protobuf:"bytes,3,opt,name=call_name,json=callName,proto3" json:"call_name,omitempty"`
	// AI伴侣形象卡
	Role *AIPartner_Role `protobuf:"bytes,4,opt,name=role,proto3" json:"role,omitempty"`
	// [不再接收ta的消息]开关 true:打开 false:关闭
	Silent bool `protobuf:"varint,5,opt,name=silent,proto3" json:"silent,omitempty"`
	// AI伴侣修改形象次数
	ChangeRoleCnt uint32 `protobuf:"varint,7,opt,name=change_role_cnt,json=changeRoleCnt,proto3" json:"change_role_cnt,omitempty"`
	// AI伴侣形象立绘
	Photograph *AIPhotograph `protobuf:"bytes,8,opt,name=photograph,proto3" json:"photograph,omitempty"`
	// AI伴侣关系
	Relation *AIRelationship `protobuf:"bytes,9,opt,name=relation,proto3" json:"relation,omitempty"`
	// AI伴侣来源
	Source uint32 `protobuf:"varint,10,opt,name=source,proto3" json:"source,omitempty"`
	// 未设置过名字
	UnsetName bool `protobuf:"varint,11,opt,name=unset_name,json=unsetName,proto3" json:"unset_name,omitempty"`
	// 是否展示hint内容
	ShowHint bool `protobuf:"varint,12,opt,name=show_hint,json=showHint,proto3" json:"show_hint,omitempty"`
	// hint内容
	Hint string `protobuf:"bytes,13,opt,name=hint,proto3" json:"hint,omitempty"`
	// 未设置角色
	UnsetRole bool `protobuf:"varint,14,opt,name=unset_role,json=unsetRole,proto3" json:"unset_role,omitempty"`
	// 关系角色列表
	RelationRoles        []*RelationRoleInfo `protobuf:"bytes,15,rep,name=relation_roles,json=relationRoles,proto3" json:"relation_roles,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *AIPartner) Reset()         { *m = AIPartner{} }
func (m *AIPartner) String() string { return proto.CompactTextString(m) }
func (*AIPartner) ProtoMessage()    {}
func (*AIPartner) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{8}
}
func (m *AIPartner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIPartner.Unmarshal(m, b)
}
func (m *AIPartner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIPartner.Marshal(b, m, deterministic)
}
func (dst *AIPartner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIPartner.Merge(dst, src)
}
func (m *AIPartner) XXX_Size() int {
	return xxx_messageInfo_AIPartner.Size(m)
}
func (m *AIPartner) XXX_DiscardUnknown() {
	xxx_messageInfo_AIPartner.DiscardUnknown(m)
}

var xxx_messageInfo_AIPartner proto.InternalMessageInfo

func (m *AIPartner) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIPartner) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AIPartner) GetCallName() string {
	if m != nil {
		return m.CallName
	}
	return ""
}

func (m *AIPartner) GetRole() *AIPartner_Role {
	if m != nil {
		return m.Role
	}
	return nil
}

func (m *AIPartner) GetSilent() bool {
	if m != nil {
		return m.Silent
	}
	return false
}

func (m *AIPartner) GetChangeRoleCnt() uint32 {
	if m != nil {
		return m.ChangeRoleCnt
	}
	return 0
}

func (m *AIPartner) GetPhotograph() *AIPhotograph {
	if m != nil {
		return m.Photograph
	}
	return nil
}

func (m *AIPartner) GetRelation() *AIRelationship {
	if m != nil {
		return m.Relation
	}
	return nil
}

func (m *AIPartner) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *AIPartner) GetUnsetName() bool {
	if m != nil {
		return m.UnsetName
	}
	return false
}

func (m *AIPartner) GetShowHint() bool {
	if m != nil {
		return m.ShowHint
	}
	return false
}

func (m *AIPartner) GetHint() string {
	if m != nil {
		return m.Hint
	}
	return ""
}

func (m *AIPartner) GetUnsetRole() bool {
	if m != nil {
		return m.UnsetRole
	}
	return false
}

func (m *AIPartner) GetRelationRoles() []*RelationRoleInfo {
	if m != nil {
		return m.RelationRoles
	}
	return nil
}

type AIPartner_Role struct {
	Id                   uint32       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                 uint32       `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Sex                  int32        `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Name                 string       `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	State                uint32       `protobuf:"varint,5,opt,name=state,proto3" json:"state,omitempty"`
	Avatar               string       `protobuf:"bytes,6,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Image                string       `protobuf:"bytes,7,opt,name=image,proto3" json:"image,omitempty"`
	DialogColor          string       `protobuf:"bytes,8,opt,name=dialog_color,json=dialogColor,proto3" json:"dialog_color,omitempty"`
	Character            string       `protobuf:"bytes,9,opt,name=character,proto3" json:"character,omitempty"`
	Prologue             string       `protobuf:"bytes,10,opt,name=prologue,proto3" json:"prologue,omitempty"`
	PrologueAudio        string       `protobuf:"bytes,11,opt,name=prologue_audio,json=prologueAudio,proto3" json:"prologue_audio,omitempty"`
	Timbre               string       `protobuf:"bytes,12,opt,name=timbre,proto3" json:"timbre,omitempty"`
	AuditResult          uint32       `protobuf:"varint,13,opt,name=audit_result,json=auditResult,proto3" json:"audit_result,omitempty"`
	EnableRcmdReply      bool         `protobuf:"varint,14,opt,name=enable_rcmd_reply,json=enableRcmdReply,proto3" json:"enable_rcmd_reply,omitempty"`
	LikeNum              uint32       `protobuf:"varint,15,opt,name=like_num,json=likeNum,proto3" json:"like_num,omitempty"`
	LikeState            uint32       `protobuf:"varint,16,opt,name=like_state,json=likeState,proto3" json:"like_state,omitempty"`
	StoryId              string       `protobuf:"bytes,17,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	CreatorUid           uint32       `protobuf:"varint,18,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	Exposed              bool         `protobuf:"varint,19,opt,name=exposed,proto3" json:"exposed,omitempty"`
	Ext                  []byte       `protobuf:"bytes,20,opt,name=ext,proto3" json:"ext,omitempty"`
	StoryMode            uint32       `protobuf:"varint,21,opt,name=story_mode,json=storyMode,proto3" json:"story_mode,omitempty"`
	CreatorInfoType      uint32       `protobuf:"varint,22,opt,name=creator_info_type,json=creatorInfoType,proto3" json:"creator_info_type,omitempty"`
	CreatorInfo          *CreatorInfo `protobuf:"bytes,23,opt,name=creator_info,json=creatorInfo,proto3" json:"creator_info,omitempty"`
	UserRoleSetting      string       `protobuf:"bytes,24,opt,name=user_role_setting,json=userRoleSetting,proto3" json:"user_role_setting,omitempty"`
	Scope                uint32       `protobuf:"varint,25,opt,name=scope,proto3" json:"scope,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AIPartner_Role) Reset()         { *m = AIPartner_Role{} }
func (m *AIPartner_Role) String() string { return proto.CompactTextString(m) }
func (*AIPartner_Role) ProtoMessage()    {}
func (*AIPartner_Role) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{8, 0}
}
func (m *AIPartner_Role) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIPartner_Role.Unmarshal(m, b)
}
func (m *AIPartner_Role) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIPartner_Role.Marshal(b, m, deterministic)
}
func (dst *AIPartner_Role) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIPartner_Role.Merge(dst, src)
}
func (m *AIPartner_Role) XXX_Size() int {
	return xxx_messageInfo_AIPartner_Role.Size(m)
}
func (m *AIPartner_Role) XXX_DiscardUnknown() {
	xxx_messageInfo_AIPartner_Role.DiscardUnknown(m)
}

var xxx_messageInfo_AIPartner_Role proto.InternalMessageInfo

func (m *AIPartner_Role) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIPartner_Role) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AIPartner_Role) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *AIPartner_Role) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AIPartner_Role) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *AIPartner_Role) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *AIPartner_Role) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *AIPartner_Role) GetDialogColor() string {
	if m != nil {
		return m.DialogColor
	}
	return ""
}

func (m *AIPartner_Role) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *AIPartner_Role) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *AIPartner_Role) GetPrologueAudio() string {
	if m != nil {
		return m.PrologueAudio
	}
	return ""
}

func (m *AIPartner_Role) GetTimbre() string {
	if m != nil {
		return m.Timbre
	}
	return ""
}

func (m *AIPartner_Role) GetAuditResult() uint32 {
	if m != nil {
		return m.AuditResult
	}
	return 0
}

func (m *AIPartner_Role) GetEnableRcmdReply() bool {
	if m != nil {
		return m.EnableRcmdReply
	}
	return false
}

func (m *AIPartner_Role) GetLikeNum() uint32 {
	if m != nil {
		return m.LikeNum
	}
	return 0
}

func (m *AIPartner_Role) GetLikeState() uint32 {
	if m != nil {
		return m.LikeState
	}
	return 0
}

func (m *AIPartner_Role) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

func (m *AIPartner_Role) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

func (m *AIPartner_Role) GetExposed() bool {
	if m != nil {
		return m.Exposed
	}
	return false
}

func (m *AIPartner_Role) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *AIPartner_Role) GetStoryMode() uint32 {
	if m != nil {
		return m.StoryMode
	}
	return 0
}

func (m *AIPartner_Role) GetCreatorInfoType() uint32 {
	if m != nil {
		return m.CreatorInfoType
	}
	return 0
}

func (m *AIPartner_Role) GetCreatorInfo() *CreatorInfo {
	if m != nil {
		return m.CreatorInfo
	}
	return nil
}

func (m *AIPartner_Role) GetUserRoleSetting() string {
	if m != nil {
		return m.UserRoleSetting
	}
	return ""
}

func (m *AIPartner_Role) GetScope() uint32 {
	if m != nil {
		return m.Scope
	}
	return 0
}

type RelationRoleInfo struct {
	Id                   uint32       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                 uint32       `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Name                 string       `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	State                uint32       `protobuf:"varint,4,opt,name=state,proto3" json:"state,omitempty"`
	Avatar               string       `protobuf:"bytes,5,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Image                string       `protobuf:"bytes,6,opt,name=image,proto3" json:"image,omitempty"`
	Exposed              bool         `protobuf:"varint,7,opt,name=exposed,proto3" json:"exposed,omitempty"`
	RelationType         RelationType `protobuf:"varint,8,opt,name=relation_type,json=relationType,proto3,enum=aigc_http_logic.RelationType" json:"relation_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RelationRoleInfo) Reset()         { *m = RelationRoleInfo{} }
func (m *RelationRoleInfo) String() string { return proto.CompactTextString(m) }
func (*RelationRoleInfo) ProtoMessage()    {}
func (*RelationRoleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{9}
}
func (m *RelationRoleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationRoleInfo.Unmarshal(m, b)
}
func (m *RelationRoleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationRoleInfo.Marshal(b, m, deterministic)
}
func (dst *RelationRoleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationRoleInfo.Merge(dst, src)
}
func (m *RelationRoleInfo) XXX_Size() int {
	return xxx_messageInfo_RelationRoleInfo.Size(m)
}
func (m *RelationRoleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationRoleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RelationRoleInfo proto.InternalMessageInfo

func (m *RelationRoleInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RelationRoleInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *RelationRoleInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RelationRoleInfo) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *RelationRoleInfo) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *RelationRoleInfo) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *RelationRoleInfo) GetExposed() bool {
	if m != nil {
		return m.Exposed
	}
	return false
}

func (m *RelationRoleInfo) GetRelationType() RelationType {
	if m != nil {
		return m.RelationType
	}
	return RelationType_RELATION_TYPE_UNSPECIFIED
}

type PetPartner struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 来源
	Source uint32 `protobuf:"varint,2,opt,name=source,proto3" json:"source,omitempty"`
	// 绑定的角色
	Role *PetPartner_Role `protobuf:"bytes,3,opt,name=role,proto3" json:"role,omitempty"`
	// ta的名字
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// ta对你的称呼
	CallName string `protobuf:"bytes,5,opt,name=call_name,json=callName,proto3" json:"call_name,omitempty"`
	// 自定义音色
	CustomVoices         []*CustomVoice `protobuf:"bytes,6,rep,name=custom_voices,json=customVoices,proto3" json:"custom_voices,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *PetPartner) Reset()         { *m = PetPartner{} }
func (m *PetPartner) String() string { return proto.CompactTextString(m) }
func (*PetPartner) ProtoMessage()    {}
func (*PetPartner) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{10}
}
func (m *PetPartner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetPartner.Unmarshal(m, b)
}
func (m *PetPartner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetPartner.Marshal(b, m, deterministic)
}
func (dst *PetPartner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetPartner.Merge(dst, src)
}
func (m *PetPartner) XXX_Size() int {
	return xxx_messageInfo_PetPartner.Size(m)
}
func (m *PetPartner) XXX_DiscardUnknown() {
	xxx_messageInfo_PetPartner.DiscardUnknown(m)
}

var xxx_messageInfo_PetPartner proto.InternalMessageInfo

func (m *PetPartner) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetPartner) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *PetPartner) GetRole() *PetPartner_Role {
	if m != nil {
		return m.Role
	}
	return nil
}

func (m *PetPartner) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PetPartner) GetCallName() string {
	if m != nil {
		return m.CallName
	}
	return ""
}

func (m *PetPartner) GetCustomVoices() []*CustomVoice {
	if m != nil {
		return m.CustomVoices
	}
	return nil
}

type PetPartner_Role struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Sex                  int32    `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Avatar               string   `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Ext                  []byte   `protobuf:"bytes,5,opt,name=ext,proto3" json:"ext,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PetPartner_Role) Reset()         { *m = PetPartner_Role{} }
func (m *PetPartner_Role) String() string { return proto.CompactTextString(m) }
func (*PetPartner_Role) ProtoMessage()    {}
func (*PetPartner_Role) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{10, 0}
}
func (m *PetPartner_Role) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetPartner_Role.Unmarshal(m, b)
}
func (m *PetPartner_Role) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetPartner_Role.Marshal(b, m, deterministic)
}
func (dst *PetPartner_Role) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetPartner_Role.Merge(dst, src)
}
func (m *PetPartner_Role) XXX_Size() int {
	return xxx_messageInfo_PetPartner_Role.Size(m)
}
func (m *PetPartner_Role) XXX_DiscardUnknown() {
	xxx_messageInfo_PetPartner_Role.DiscardUnknown(m)
}

var xxx_messageInfo_PetPartner_Role proto.InternalMessageInfo

func (m *PetPartner_Role) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PetPartner_Role) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *PetPartner_Role) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *PetPartner_Role) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *PetPartner_Role) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

type ChattingPartner struct {
	Id                   uint32                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Role                 *ChattingPartner_Role `protobuf:"bytes,3,opt,name=role,proto3" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ChattingPartner) Reset()         { *m = ChattingPartner{} }
func (m *ChattingPartner) String() string { return proto.CompactTextString(m) }
func (*ChattingPartner) ProtoMessage()    {}
func (*ChattingPartner) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{11}
}
func (m *ChattingPartner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChattingPartner.Unmarshal(m, b)
}
func (m *ChattingPartner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChattingPartner.Marshal(b, m, deterministic)
}
func (dst *ChattingPartner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChattingPartner.Merge(dst, src)
}
func (m *ChattingPartner) XXX_Size() int {
	return xxx_messageInfo_ChattingPartner.Size(m)
}
func (m *ChattingPartner) XXX_DiscardUnknown() {
	xxx_messageInfo_ChattingPartner.DiscardUnknown(m)
}

var xxx_messageInfo_ChattingPartner proto.InternalMessageInfo

func (m *ChattingPartner) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ChattingPartner) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChattingPartner) GetRole() *ChattingPartner_Role {
	if m != nil {
		return m.Role
	}
	return nil
}

type ChattingPartner_Role struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Sex                  int32    `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	State                uint32   `protobuf:"varint,5,opt,name=state,proto3" json:"state,omitempty"`
	Avatar               string   `protobuf:"bytes,6,opt,name=avatar,proto3" json:"avatar,omitempty"`
	AuditResult          uint32   `protobuf:"varint,13,opt,name=audit_result,json=auditResult,proto3" json:"audit_result,omitempty"`
	CreatorUid           uint32   `protobuf:"varint,18,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	Exposed              bool     `protobuf:"varint,19,opt,name=exposed,proto3" json:"exposed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChattingPartner_Role) Reset()         { *m = ChattingPartner_Role{} }
func (m *ChattingPartner_Role) String() string { return proto.CompactTextString(m) }
func (*ChattingPartner_Role) ProtoMessage()    {}
func (*ChattingPartner_Role) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{11, 0}
}
func (m *ChattingPartner_Role) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChattingPartner_Role.Unmarshal(m, b)
}
func (m *ChattingPartner_Role) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChattingPartner_Role.Marshal(b, m, deterministic)
}
func (dst *ChattingPartner_Role) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChattingPartner_Role.Merge(dst, src)
}
func (m *ChattingPartner_Role) XXX_Size() int {
	return xxx_messageInfo_ChattingPartner_Role.Size(m)
}
func (m *ChattingPartner_Role) XXX_DiscardUnknown() {
	xxx_messageInfo_ChattingPartner_Role.DiscardUnknown(m)
}

var xxx_messageInfo_ChattingPartner_Role proto.InternalMessageInfo

func (m *ChattingPartner_Role) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ChattingPartner_Role) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ChattingPartner_Role) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ChattingPartner_Role) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChattingPartner_Role) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *ChattingPartner_Role) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *ChattingPartner_Role) GetAuditResult() uint32 {
	if m != nil {
		return m.AuditResult
	}
	return 0
}

func (m *ChattingPartner_Role) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

func (m *ChattingPartner_Role) GetExposed() bool {
	if m != nil {
		return m.Exposed
	}
	return false
}

type BannerRole struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Sex                  int32    `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Image                string   `protobuf:"bytes,5,opt,name=image,proto3" json:"image,omitempty"`
	Character            string   `protobuf:"bytes,6,opt,name=character,proto3" json:"character,omitempty"`
	CornerIcon           string   `protobuf:"bytes,7,opt,name=corner_icon,json=cornerIcon,proto3" json:"corner_icon,omitempty"`
	PrologueAudio        string   `protobuf:"bytes,8,opt,name=prologue_audio,json=prologueAudio,proto3" json:"prologue_audio,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BannerRole) Reset()         { *m = BannerRole{} }
func (m *BannerRole) String() string { return proto.CompactTextString(m) }
func (*BannerRole) ProtoMessage()    {}
func (*BannerRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{12}
}
func (m *BannerRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BannerRole.Unmarshal(m, b)
}
func (m *BannerRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BannerRole.Marshal(b, m, deterministic)
}
func (dst *BannerRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BannerRole.Merge(dst, src)
}
func (m *BannerRole) XXX_Size() int {
	return xxx_messageInfo_BannerRole.Size(m)
}
func (m *BannerRole) XXX_DiscardUnknown() {
	xxx_messageInfo_BannerRole.DiscardUnknown(m)
}

var xxx_messageInfo_BannerRole proto.InternalMessageInfo

func (m *BannerRole) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BannerRole) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *BannerRole) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *BannerRole) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *BannerRole) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *BannerRole) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *BannerRole) GetCornerIcon() string {
	if m != nil {
		return m.CornerIcon
	}
	return ""
}

func (m *BannerRole) GetPrologueAudio() string {
	if m != nil {
		return m.PrologueAudio
	}
	return ""
}

type RcmdRole struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Sex                  int32    `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Image                string   `protobuf:"bytes,5,opt,name=image,proto3" json:"image,omitempty"`
	Character            string   `protobuf:"bytes,6,opt,name=character,proto3" json:"character,omitempty"`
	CornerIcon           string   `protobuf:"bytes,7,opt,name=corner_icon,json=cornerIcon,proto3" json:"corner_icon,omitempty"`
	PrologueAudio        string   `protobuf:"bytes,8,opt,name=prologue_audio,json=prologueAudio,proto3" json:"prologue_audio,omitempty"`
	Tags                 []string `protobuf:"bytes,9,rep,name=tags,proto3" json:"tags,omitempty"`
	Avatar               string   `protobuf:"bytes,10,opt,name=avatar,proto3" json:"avatar,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RcmdRole) Reset()         { *m = RcmdRole{} }
func (m *RcmdRole) String() string { return proto.CompactTextString(m) }
func (*RcmdRole) ProtoMessage()    {}
func (*RcmdRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{13}
}
func (m *RcmdRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RcmdRole.Unmarshal(m, b)
}
func (m *RcmdRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RcmdRole.Marshal(b, m, deterministic)
}
func (dst *RcmdRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RcmdRole.Merge(dst, src)
}
func (m *RcmdRole) XXX_Size() int {
	return xxx_messageInfo_RcmdRole.Size(m)
}
func (m *RcmdRole) XXX_DiscardUnknown() {
	xxx_messageInfo_RcmdRole.DiscardUnknown(m)
}

var xxx_messageInfo_RcmdRole proto.InternalMessageInfo

func (m *RcmdRole) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RcmdRole) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *RcmdRole) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *RcmdRole) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RcmdRole) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *RcmdRole) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *RcmdRole) GetCornerIcon() string {
	if m != nil {
		return m.CornerIcon
	}
	return ""
}

func (m *RcmdRole) GetPrologueAudio() string {
	if m != nil {
		return m.PrologueAudio
	}
	return ""
}

func (m *RcmdRole) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *RcmdRole) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

type AIRolePrologue struct {
	Text                 string   `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	Audio                string   `protobuf:"bytes,2,opt,name=audio,proto3" json:"audio,omitempty"`
	Seq                  uint32   `protobuf:"varint,3,opt,name=seq,proto3" json:"seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIRolePrologue) Reset()         { *m = AIRolePrologue{} }
func (m *AIRolePrologue) String() string { return proto.CompactTextString(m) }
func (*AIRolePrologue) ProtoMessage()    {}
func (*AIRolePrologue) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{14}
}
func (m *AIRolePrologue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRolePrologue.Unmarshal(m, b)
}
func (m *AIRolePrologue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRolePrologue.Marshal(b, m, deterministic)
}
func (dst *AIRolePrologue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRolePrologue.Merge(dst, src)
}
func (m *AIRolePrologue) XXX_Size() int {
	return xxx_messageInfo_AIRolePrologue.Size(m)
}
func (m *AIRolePrologue) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRolePrologue.DiscardUnknown(m)
}

var xxx_messageInfo_AIRolePrologue proto.InternalMessageInfo

func (m *AIRolePrologue) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *AIRolePrologue) GetAudio() string {
	if m != nil {
		return m.Audio
	}
	return ""
}

func (m *AIRolePrologue) GetSeq() uint32 {
	if m != nil {
		return m.Seq
	}
	return 0
}

type InteractiveGame struct {
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 绑定角色ID
	RoleId uint32 `protobuf:"varint,4,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 绑定主题ID
	TopicId uint32 `protobuf:"varint,5,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	// 标题
	Title string `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	// 描述
	Desc string `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`
	// 开场白
	Prologue string `protobuf:"bytes,8,opt,name=prologue,proto3" json:"prologue,omitempty"`
	// 公开/私有状态
	State uint32 `protobuf:"varint,9,opt,name=state,proto3" json:"state,omitempty"`
	// 绑定角色名称
	RoleName string `protobuf:"bytes,10,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	// 绑定角色头像
	RoleAvatar           string   `protobuf:"bytes,11,opt,name=role_avatar,json=roleAvatar,proto3" json:"role_avatar,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InteractiveGame) Reset()         { *m = InteractiveGame{} }
func (m *InteractiveGame) String() string { return proto.CompactTextString(m) }
func (*InteractiveGame) ProtoMessage()    {}
func (*InteractiveGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{15}
}
func (m *InteractiveGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractiveGame.Unmarshal(m, b)
}
func (m *InteractiveGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractiveGame.Marshal(b, m, deterministic)
}
func (dst *InteractiveGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractiveGame.Merge(dst, src)
}
func (m *InteractiveGame) XXX_Size() int {
	return xxx_messageInfo_InteractiveGame.Size(m)
}
func (m *InteractiveGame) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractiveGame.DiscardUnknown(m)
}

var xxx_messageInfo_InteractiveGame proto.InternalMessageInfo

func (m *InteractiveGame) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *InteractiveGame) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *InteractiveGame) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *InteractiveGame) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *InteractiveGame) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *InteractiveGame) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *InteractiveGame) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *InteractiveGame) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

func (m *InteractiveGame) GetRoleAvatar() string {
	if m != nil {
		return m.RoleAvatar
	}
	return ""
}

type GroupInfo struct {
	Id         uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	OwnerUid   uint32 `protobuf:"varint,2,opt,name=owner_uid,json=ownerUid,proto3" json:"owner_uid,omitempty"`
	TemplateId uint32 `protobuf:"varint,3,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// 建群来源 see aigc-group.proto enum GroupSource
	Source               uint32   `protobuf:"varint,4,opt,name=source,proto3" json:"source,omitempty"`
	LikeNum              uint32   `protobuf:"varint,6,opt,name=like_num,json=likeNum,proto3" json:"like_num,omitempty"`
	MemberNum            uint32   `protobuf:"varint,7,opt,name=member_num,json=memberNum,proto3" json:"member_num,omitempty"`
	MaxRealUserNum       uint32   `protobuf:"varint,8,opt,name=max_real_user_num,json=maxRealUserNum,proto3" json:"max_real_user_num,omitempty"`
	LikeState            uint32   `protobuf:"varint,11,opt,name=like_state,json=likeState,proto3" json:"like_state,omitempty"`
	MatchStrategy        uint32   `protobuf:"varint,12,opt,name=match_strategy,json=matchStrategy,proto3" json:"match_strategy,omitempty"`
	LeaveStrategy        uint32   `protobuf:"varint,13,opt,name=leave_strategy,json=leaveStrategy,proto3" json:"leave_strategy,omitempty"`
	Sex                  int32    `protobuf:"varint,16,opt,name=sex,proto3" json:"sex,omitempty"`
	Name                 string   `protobuf:"bytes,17,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,18,opt,name=desc,proto3" json:"desc,omitempty"`
	Avatar               string   `protobuf:"bytes,19,opt,name=avatar,proto3" json:"avatar,omitempty"`
	ChatBackground       string   `protobuf:"bytes,20,opt,name=chat_background,json=chatBackground,proto3" json:"chat_background,omitempty"`
	GroupType            uint32   `protobuf:"varint,21,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	BackgroundMusic      string   `protobuf:"bytes,22,opt,name=background_music,json=backgroundMusic,proto3" json:"background_music,omitempty"`
	QuickSpeakTexts      []string `protobuf:"bytes,32,rep,name=quick_speak_texts,json=quickSpeakTexts,proto3" json:"quick_speak_texts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupInfo) Reset()         { *m = GroupInfo{} }
func (m *GroupInfo) String() string { return proto.CompactTextString(m) }
func (*GroupInfo) ProtoMessage()    {}
func (*GroupInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{16}
}
func (m *GroupInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupInfo.Unmarshal(m, b)
}
func (m *GroupInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupInfo.Marshal(b, m, deterministic)
}
func (dst *GroupInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupInfo.Merge(dst, src)
}
func (m *GroupInfo) XXX_Size() int {
	return xxx_messageInfo_GroupInfo.Size(m)
}
func (m *GroupInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GroupInfo proto.InternalMessageInfo

func (m *GroupInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupInfo) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

func (m *GroupInfo) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *GroupInfo) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *GroupInfo) GetLikeNum() uint32 {
	if m != nil {
		return m.LikeNum
	}
	return 0
}

func (m *GroupInfo) GetMemberNum() uint32 {
	if m != nil {
		return m.MemberNum
	}
	return 0
}

func (m *GroupInfo) GetMaxRealUserNum() uint32 {
	if m != nil {
		return m.MaxRealUserNum
	}
	return 0
}

func (m *GroupInfo) GetLikeState() uint32 {
	if m != nil {
		return m.LikeState
	}
	return 0
}

func (m *GroupInfo) GetMatchStrategy() uint32 {
	if m != nil {
		return m.MatchStrategy
	}
	return 0
}

func (m *GroupInfo) GetLeaveStrategy() uint32 {
	if m != nil {
		return m.LeaveStrategy
	}
	return 0
}

func (m *GroupInfo) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GroupInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GroupInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *GroupInfo) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *GroupInfo) GetChatBackground() string {
	if m != nil {
		return m.ChatBackground
	}
	return ""
}

func (m *GroupInfo) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *GroupInfo) GetBackgroundMusic() string {
	if m != nil {
		return m.BackgroundMusic
	}
	return ""
}

func (m *GroupInfo) GetQuickSpeakTexts() []string {
	if m != nil {
		return m.QuickSpeakTexts
	}
	return nil
}

type GroupRole struct {
	Id                   uint32            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Sex                  int32             `protobuf:"varint,6,opt,name=sex,proto3" json:"sex,omitempty"`
	Name                 string            `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
	Avatar               string            `protobuf:"bytes,8,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Character            string            `protobuf:"bytes,9,opt,name=character,proto3" json:"character,omitempty"`
	PrologueList         []*AIRolePrologue `protobuf:"bytes,16,rep,name=prologue_list,json=prologueList,proto3" json:"prologue_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GroupRole) Reset()         { *m = GroupRole{} }
func (m *GroupRole) String() string { return proto.CompactTextString(m) }
func (*GroupRole) ProtoMessage()    {}
func (*GroupRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{17}
}
func (m *GroupRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupRole.Unmarshal(m, b)
}
func (m *GroupRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupRole.Marshal(b, m, deterministic)
}
func (dst *GroupRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupRole.Merge(dst, src)
}
func (m *GroupRole) XXX_Size() int {
	return xxx_messageInfo_GroupRole.Size(m)
}
func (m *GroupRole) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupRole.DiscardUnknown(m)
}

var xxx_messageInfo_GroupRole proto.InternalMessageInfo

func (m *GroupRole) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupRole) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GroupRole) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GroupRole) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *GroupRole) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *GroupRole) GetPrologueList() []*AIRolePrologue {
	if m != nil {
		return m.PrologueList
	}
	return nil
}

type GroupTemplateInfo struct {
	Id                   uint32                        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	LikeNum              uint32                        `protobuf:"varint,6,opt,name=like_num,json=likeNum,proto3" json:"like_num,omitempty"`
	MemberNum            uint32                        `protobuf:"varint,7,opt,name=member_num,json=memberNum,proto3" json:"member_num,omitempty"`
	LikeState            uint32                        `protobuf:"varint,11,opt,name=like_state,json=likeState,proto3" json:"like_state,omitempty"`
	Sex                  int32                         `protobuf:"varint,16,opt,name=sex,proto3" json:"sex,omitempty"`
	Name                 string                        `protobuf:"bytes,17,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string                        `protobuf:"bytes,18,opt,name=desc,proto3" json:"desc,omitempty"`
	Avatar               string                        `protobuf:"bytes,19,opt,name=avatar,proto3" json:"avatar,omitempty"`
	ChatBackground       string                        `protobuf:"bytes,20,opt,name=chat_background,json=chatBackground,proto3" json:"chat_background,omitempty"`
	GroupType            uint32                        `protobuf:"varint,21,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	ScriptInfo           *GroupTemplateInfo_ScriptInfo `protobuf:"bytes,22,opt,name=script_info,json=scriptInfo,proto3" json:"script_info,omitempty"`
	DefaultPrologueList  []*AIRolePrologue             `protobuf:"bytes,23,rep,name=default_prologue_list,json=defaultPrologueList,proto3" json:"default_prologue_list,omitempty"`
	RoleList             []*GroupRole                  `protobuf:"bytes,31,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	QuickSpeakTexts      []string                      `protobuf:"bytes,32,rep,name=quick_speak_texts,json=quickSpeakTexts,proto3" json:"quick_speak_texts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GroupTemplateInfo) Reset()         { *m = GroupTemplateInfo{} }
func (m *GroupTemplateInfo) String() string { return proto.CompactTextString(m) }
func (*GroupTemplateInfo) ProtoMessage()    {}
func (*GroupTemplateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{18}
}
func (m *GroupTemplateInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupTemplateInfo.Unmarshal(m, b)
}
func (m *GroupTemplateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupTemplateInfo.Marshal(b, m, deterministic)
}
func (dst *GroupTemplateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupTemplateInfo.Merge(dst, src)
}
func (m *GroupTemplateInfo) XXX_Size() int {
	return xxx_messageInfo_GroupTemplateInfo.Size(m)
}
func (m *GroupTemplateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupTemplateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GroupTemplateInfo proto.InternalMessageInfo

func (m *GroupTemplateInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupTemplateInfo) GetLikeNum() uint32 {
	if m != nil {
		return m.LikeNum
	}
	return 0
}

func (m *GroupTemplateInfo) GetMemberNum() uint32 {
	if m != nil {
		return m.MemberNum
	}
	return 0
}

func (m *GroupTemplateInfo) GetLikeState() uint32 {
	if m != nil {
		return m.LikeState
	}
	return 0
}

func (m *GroupTemplateInfo) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GroupTemplateInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GroupTemplateInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *GroupTemplateInfo) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *GroupTemplateInfo) GetChatBackground() string {
	if m != nil {
		return m.ChatBackground
	}
	return ""
}

func (m *GroupTemplateInfo) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *GroupTemplateInfo) GetScriptInfo() *GroupTemplateInfo_ScriptInfo {
	if m != nil {
		return m.ScriptInfo
	}
	return nil
}

func (m *GroupTemplateInfo) GetDefaultPrologueList() []*AIRolePrologue {
	if m != nil {
		return m.DefaultPrologueList
	}
	return nil
}

func (m *GroupTemplateInfo) GetRoleList() []*GroupRole {
	if m != nil {
		return m.RoleList
	}
	return nil
}

func (m *GroupTemplateInfo) GetQuickSpeakTexts() []string {
	if m != nil {
		return m.QuickSpeakTexts
	}
	return nil
}

type GroupTemplateInfo_ScriptInfo struct {
	BackgroundMusic      string   `protobuf:"bytes,1,opt,name=background_music,json=backgroundMusic,proto3" json:"background_music,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupTemplateInfo_ScriptInfo) Reset()         { *m = GroupTemplateInfo_ScriptInfo{} }
func (m *GroupTemplateInfo_ScriptInfo) String() string { return proto.CompactTextString(m) }
func (*GroupTemplateInfo_ScriptInfo) ProtoMessage()    {}
func (*GroupTemplateInfo_ScriptInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{18, 0}
}
func (m *GroupTemplateInfo_ScriptInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupTemplateInfo_ScriptInfo.Unmarshal(m, b)
}
func (m *GroupTemplateInfo_ScriptInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupTemplateInfo_ScriptInfo.Marshal(b, m, deterministic)
}
func (dst *GroupTemplateInfo_ScriptInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupTemplateInfo_ScriptInfo.Merge(dst, src)
}
func (m *GroupTemplateInfo_ScriptInfo) XXX_Size() int {
	return xxx_messageInfo_GroupTemplateInfo_ScriptInfo.Size(m)
}
func (m *GroupTemplateInfo_ScriptInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupTemplateInfo_ScriptInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GroupTemplateInfo_ScriptInfo proto.InternalMessageInfo

func (m *GroupTemplateInfo_ScriptInfo) GetBackgroundMusic() string {
	if m != nil {
		return m.BackgroundMusic
	}
	return ""
}

type UserRole struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	RoleName             string   `protobuf:"bytes,2,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	RoleAvatar           string   `protobuf:"bytes,3,opt,name=role_avatar,json=roleAvatar,proto3" json:"role_avatar,omitempty"`
	RoleCharacter        string   `protobuf:"bytes,4,opt,name=role_character,json=roleCharacter,proto3" json:"role_character,omitempty"`
	QuickSpeakTexts      []string `protobuf:"bytes,5,rep,name=quick_speak_texts,json=quickSpeakTexts,proto3" json:"quick_speak_texts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserRole) Reset()         { *m = UserRole{} }
func (m *UserRole) String() string { return proto.CompactTextString(m) }
func (*UserRole) ProtoMessage()    {}
func (*UserRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{19}
}
func (m *UserRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRole.Unmarshal(m, b)
}
func (m *UserRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRole.Marshal(b, m, deterministic)
}
func (dst *UserRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRole.Merge(dst, src)
}
func (m *UserRole) XXX_Size() int {
	return xxx_messageInfo_UserRole.Size(m)
}
func (m *UserRole) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRole.DiscardUnknown(m)
}

var xxx_messageInfo_UserRole proto.InternalMessageInfo

func (m *UserRole) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *UserRole) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

func (m *UserRole) GetRoleAvatar() string {
	if m != nil {
		return m.RoleAvatar
	}
	return ""
}

func (m *UserRole) GetRoleCharacter() string {
	if m != nil {
		return m.RoleCharacter
	}
	return ""
}

func (m *UserRole) GetQuickSpeakTexts() []string {
	if m != nil {
		return m.QuickSpeakTexts
	}
	return nil
}

type GroupMember struct {
	Id                   uint32            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                 uint32            `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Name                 string            `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Avatar               string            `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Account              string            `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"`
	Character            string            `protobuf:"bytes,6,opt,name=character,proto3" json:"character,omitempty"`
	IsFollow             bool              `protobuf:"varint,7,opt,name=is_follow,json=isFollow,proto3" json:"is_follow,omitempty"`
	UserRole             *UserRole         `protobuf:"bytes,8,opt,name=user_role,json=userRole,proto3" json:"user_role,omitempty"`
	PrologueList         []*AIRolePrologue `protobuf:"bytes,11,rep,name=prologue_list,json=prologueList,proto3" json:"prologue_list,omitempty"`
	Sex                  int32             `protobuf:"varint,12,opt,name=sex,proto3" json:"sex,omitempty"`
	JoinTime             int64             `protobuf:"varint,13,opt,name=join_time,json=joinTime,proto3" json:"join_time,omitempty"`
	LastActiveTime       int64             `protobuf:"varint,14,opt,name=last_active_time,json=lastActiveTime,proto3" json:"last_active_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GroupMember) Reset()         { *m = GroupMember{} }
func (m *GroupMember) String() string { return proto.CompactTextString(m) }
func (*GroupMember) ProtoMessage()    {}
func (*GroupMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{20}
}
func (m *GroupMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupMember.Unmarshal(m, b)
}
func (m *GroupMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupMember.Marshal(b, m, deterministic)
}
func (dst *GroupMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupMember.Merge(dst, src)
}
func (m *GroupMember) XXX_Size() int {
	return xxx_messageInfo_GroupMember.Size(m)
}
func (m *GroupMember) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupMember.DiscardUnknown(m)
}

var xxx_messageInfo_GroupMember proto.InternalMessageInfo

func (m *GroupMember) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupMember) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GroupMember) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GroupMember) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *GroupMember) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GroupMember) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *GroupMember) GetIsFollow() bool {
	if m != nil {
		return m.IsFollow
	}
	return false
}

func (m *GroupMember) GetUserRole() *UserRole {
	if m != nil {
		return m.UserRole
	}
	return nil
}

func (m *GroupMember) GetPrologueList() []*AIRolePrologue {
	if m != nil {
		return m.PrologueList
	}
	return nil
}

func (m *GroupMember) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GroupMember) GetJoinTime() int64 {
	if m != nil {
		return m.JoinTime
	}
	return 0
}

func (m *GroupMember) GetLastActiveTime() int64 {
	if m != nil {
		return m.LastActiveTime
	}
	return 0
}

type Entity struct {
	Id                   uint32      `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                 Entity_Type `protobuf:"varint,2,opt,name=type,proto3,enum=aigc_http_logic.Entity_Type" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *Entity) Reset()         { *m = Entity{} }
func (m *Entity) String() string { return proto.CompactTextString(m) }
func (*Entity) ProtoMessage()    {}
func (*Entity) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{21}
}
func (m *Entity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Entity.Unmarshal(m, b)
}
func (m *Entity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Entity.Marshal(b, m, deterministic)
}
func (dst *Entity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Entity.Merge(dst, src)
}
func (m *Entity) XXX_Size() int {
	return xxx_messageInfo_Entity.Size(m)
}
func (m *Entity) XXX_DiscardUnknown() {
	xxx_messageInfo_Entity.DiscardUnknown(m)
}

var xxx_messageInfo_Entity proto.InternalMessageInfo

func (m *Entity) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Entity) GetType() Entity_Type {
	if m != nil {
		return m.Type
	}
	return Entity_TYPE_UNSPECIFIED
}

type LevelConfig struct {
	// 等级
	Level uint32 `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	// 到达等级需要的亲密值
	Value uint32 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	// 等级权益描述
	BenefitDesc          string   `protobuf:"bytes,3,opt,name=benefit_desc,json=benefitDesc,proto3" json:"benefit_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelConfig) Reset()         { *m = LevelConfig{} }
func (m *LevelConfig) String() string { return proto.CompactTextString(m) }
func (*LevelConfig) ProtoMessage()    {}
func (*LevelConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{22}
}
func (m *LevelConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelConfig.Unmarshal(m, b)
}
func (m *LevelConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelConfig.Marshal(b, m, deterministic)
}
func (dst *LevelConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelConfig.Merge(dst, src)
}
func (m *LevelConfig) XXX_Size() int {
	return xxx_messageInfo_LevelConfig.Size(m)
}
func (m *LevelConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelConfig.DiscardUnknown(m)
}

var xxx_messageInfo_LevelConfig proto.InternalMessageInfo

func (m *LevelConfig) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *LevelConfig) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *LevelConfig) GetBenefitDesc() string {
	if m != nil {
		return m.BenefitDesc
	}
	return ""
}

type LevelUpCondition struct {
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 图标
	Icon string `protobuf:"bytes,6,opt,name=icon,proto3" json:"icon,omitempty"`
	// 标题
	Title string `protobuf:"bytes,7,opt,name=title,proto3" json:"title,omitempty"`
	// 描述
	Desc string `protobuf:"bytes,8,opt,name=desc,proto3" json:"desc,omitempty"`
	// 满足条件增加的亲密值
	GrowthValue          uint32   `protobuf:"varint,16,opt,name=growth_value,json=growthValue,proto3" json:"growth_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelUpCondition) Reset()         { *m = LevelUpCondition{} }
func (m *LevelUpCondition) String() string { return proto.CompactTextString(m) }
func (*LevelUpCondition) ProtoMessage()    {}
func (*LevelUpCondition) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{23}
}
func (m *LevelUpCondition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelUpCondition.Unmarshal(m, b)
}
func (m *LevelUpCondition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelUpCondition.Marshal(b, m, deterministic)
}
func (dst *LevelUpCondition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelUpCondition.Merge(dst, src)
}
func (m *LevelUpCondition) XXX_Size() int {
	return xxx_messageInfo_LevelUpCondition.Size(m)
}
func (m *LevelUpCondition) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelUpCondition.DiscardUnknown(m)
}

var xxx_messageInfo_LevelUpCondition proto.InternalMessageInfo

func (m *LevelUpCondition) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *LevelUpCondition) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *LevelUpCondition) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *LevelUpCondition) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *LevelUpCondition) GetGrowthValue() uint32 {
	if m != nil {
		return m.GrowthValue
	}
	return 0
}

type GameTopic struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameTopic) Reset()         { *m = GameTopic{} }
func (m *GameTopic) String() string { return proto.CompactTextString(m) }
func (*GameTopic) ProtoMessage()    {}
func (*GameTopic) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{24}
}
func (m *GameTopic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTopic.Unmarshal(m, b)
}
func (m *GameTopic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTopic.Marshal(b, m, deterministic)
}
func (dst *GameTopic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTopic.Merge(dst, src)
}
func (m *GameTopic) XXX_Size() int {
	return xxx_messageInfo_GameTopic.Size(m)
}
func (m *GameTopic) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTopic.DiscardUnknown(m)
}

var xxx_messageInfo_GameTopic proto.InternalMessageInfo

func (m *GameTopic) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GameTopic) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type GameInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Bgm                  string   `protobuf:"bytes,4,opt,name=bgm,proto3" json:"bgm,omitempty"`
	Bg                   string   `protobuf:"bytes,5,opt,name=bg,proto3" json:"bg,omitempty"`
	Creator              int32    `protobuf:"varint,6,opt,name=creator,proto3" json:"creator,omitempty"`
	TopicId              uint32   `protobuf:"varint,7,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	SceneDesc            string   `protobuf:"bytes,8,opt,name=scene_desc,json=sceneDesc,proto3" json:"scene_desc,omitempty"`
	Subtitle             string   `protobuf:"bytes,16,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameInfo) Reset()         { *m = GameInfo{} }
func (m *GameInfo) String() string { return proto.CompactTextString(m) }
func (*GameInfo) ProtoMessage()    {}
func (*GameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{25}
}
func (m *GameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameInfo.Unmarshal(m, b)
}
func (m *GameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameInfo.Marshal(b, m, deterministic)
}
func (dst *GameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameInfo.Merge(dst, src)
}
func (m *GameInfo) XXX_Size() int {
	return xxx_messageInfo_GameInfo.Size(m)
}
func (m *GameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameInfo proto.InternalMessageInfo

func (m *GameInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GameInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GameInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GameInfo) GetBgm() string {
	if m != nil {
		return m.Bgm
	}
	return ""
}

func (m *GameInfo) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *GameInfo) GetCreator() int32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *GameInfo) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GameInfo) GetSceneDesc() string {
	if m != nil {
		return m.SceneDesc
	}
	return ""
}

func (m *GameInfo) GetSubtitle() string {
	if m != nil {
		return m.Subtitle
	}
	return ""
}

type OutGames struct {
	TopicId              uint32      `protobuf:"varint,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Games                []*GameInfo `protobuf:"bytes,2,rep,name=games,proto3" json:"games,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *OutGames) Reset()         { *m = OutGames{} }
func (m *OutGames) String() string { return proto.CompactTextString(m) }
func (*OutGames) ProtoMessage()    {}
func (*OutGames) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{26}
}
func (m *OutGames) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OutGames.Unmarshal(m, b)
}
func (m *OutGames) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OutGames.Marshal(b, m, deterministic)
}
func (dst *OutGames) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OutGames.Merge(dst, src)
}
func (m *OutGames) XXX_Size() int {
	return xxx_messageInfo_OutGames.Size(m)
}
func (m *OutGames) XXX_DiscardUnknown() {
	xxx_messageInfo_OutGames.DiscardUnknown(m)
}

var xxx_messageInfo_OutGames proto.InternalMessageInfo

func (m *OutGames) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *OutGames) GetGames() []*GameInfo {
	if m != nil {
		return m.Games
	}
	return nil
}

type CreateAIPartnerRequest struct {
	// @gotags: binding:"required"
	Partner              *CreateAIPartnerRequest_Partner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty" binding:"required"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *CreateAIPartnerRequest) Reset()         { *m = CreateAIPartnerRequest{} }
func (m *CreateAIPartnerRequest) String() string { return proto.CompactTextString(m) }
func (*CreateAIPartnerRequest) ProtoMessage()    {}
func (*CreateAIPartnerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{27}
}
func (m *CreateAIPartnerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIPartnerRequest.Unmarshal(m, b)
}
func (m *CreateAIPartnerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIPartnerRequest.Marshal(b, m, deterministic)
}
func (dst *CreateAIPartnerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIPartnerRequest.Merge(dst, src)
}
func (m *CreateAIPartnerRequest) XXX_Size() int {
	return xxx_messageInfo_CreateAIPartnerRequest.Size(m)
}
func (m *CreateAIPartnerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIPartnerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIPartnerRequest proto.InternalMessageInfo

func (m *CreateAIPartnerRequest) GetPartner() *CreateAIPartnerRequest_Partner {
	if m != nil {
		return m.Partner
	}
	return nil
}

type CreateAIPartnerRequest_Partner struct {
	// @gotags: binding:"min=0,max=5"
	Source               uint32   `protobuf:"varint,1,opt,name=source,proto3" json:"source,omitempty" binding:"min=0,max=5"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	CallName             string   `protobuf:"bytes,4,opt,name=call_name,json=callName,proto3" json:"call_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateAIPartnerRequest_Partner) Reset()         { *m = CreateAIPartnerRequest_Partner{} }
func (m *CreateAIPartnerRequest_Partner) String() string { return proto.CompactTextString(m) }
func (*CreateAIPartnerRequest_Partner) ProtoMessage()    {}
func (*CreateAIPartnerRequest_Partner) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{27, 0}
}
func (m *CreateAIPartnerRequest_Partner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIPartnerRequest_Partner.Unmarshal(m, b)
}
func (m *CreateAIPartnerRequest_Partner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIPartnerRequest_Partner.Marshal(b, m, deterministic)
}
func (dst *CreateAIPartnerRequest_Partner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIPartnerRequest_Partner.Merge(dst, src)
}
func (m *CreateAIPartnerRequest_Partner) XXX_Size() int {
	return xxx_messageInfo_CreateAIPartnerRequest_Partner.Size(m)
}
func (m *CreateAIPartnerRequest_Partner) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIPartnerRequest_Partner.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIPartnerRequest_Partner proto.InternalMessageInfo

func (m *CreateAIPartnerRequest_Partner) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *CreateAIPartnerRequest_Partner) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *CreateAIPartnerRequest_Partner) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateAIPartnerRequest_Partner) GetCallName() string {
	if m != nil {
		return m.CallName
	}
	return ""
}

type CreateAIPartnerResponse struct {
	NameResult           AuditResult `protobuf:"varint,1,opt,name=name_result,json=nameResult,proto3,enum=aigc_http_logic.AuditResult" json:"name_result,omitempty"`
	CallNameResult       AuditResult `protobuf:"varint,2,opt,name=call_name_result,json=callNameResult,proto3,enum=aigc_http_logic.AuditResult" json:"call_name_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CreateAIPartnerResponse) Reset()         { *m = CreateAIPartnerResponse{} }
func (m *CreateAIPartnerResponse) String() string { return proto.CompactTextString(m) }
func (*CreateAIPartnerResponse) ProtoMessage()    {}
func (*CreateAIPartnerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{28}
}
func (m *CreateAIPartnerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIPartnerResponse.Unmarshal(m, b)
}
func (m *CreateAIPartnerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIPartnerResponse.Marshal(b, m, deterministic)
}
func (dst *CreateAIPartnerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIPartnerResponse.Merge(dst, src)
}
func (m *CreateAIPartnerResponse) XXX_Size() int {
	return xxx_messageInfo_CreateAIPartnerResponse.Size(m)
}
func (m *CreateAIPartnerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIPartnerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIPartnerResponse proto.InternalMessageInfo

func (m *CreateAIPartnerResponse) GetNameResult() AuditResult {
	if m != nil {
		return m.NameResult
	}
	return AuditResult_AuditResultReview
}

func (m *CreateAIPartnerResponse) GetCallNameResult() AuditResult {
	if m != nil {
		return m.CallNameResult
	}
	return AuditResult_AuditResultReview
}

type UpdateAIPartnerRequest struct {
	// @gotags: binding:"required"
	Partner              *UpdateAIPartnerRequest_Partner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty" binding:"required"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *UpdateAIPartnerRequest) Reset()         { *m = UpdateAIPartnerRequest{} }
func (m *UpdateAIPartnerRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateAIPartnerRequest) ProtoMessage()    {}
func (*UpdateAIPartnerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{29}
}
func (m *UpdateAIPartnerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIPartnerRequest.Unmarshal(m, b)
}
func (m *UpdateAIPartnerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIPartnerRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateAIPartnerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIPartnerRequest.Merge(dst, src)
}
func (m *UpdateAIPartnerRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateAIPartnerRequest.Size(m)
}
func (m *UpdateAIPartnerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIPartnerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIPartnerRequest proto.InternalMessageInfo

func (m *UpdateAIPartnerRequest) GetPartner() *UpdateAIPartnerRequest_Partner {
	if m != nil {
		return m.Partner
	}
	return nil
}

type UpdateAIPartnerRequest_Partner struct {
	// @gotags: binding:"required"
	Id                   uint32         `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" binding:"required"`
	Name                 string         `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	CallName             string         `protobuf:"bytes,3,opt,name=call_name,json=callName,proto3" json:"call_name,omitempty"`
	CustomVoices         []*CustomVoice `protobuf:"bytes,4,rep,name=custom_voices,json=customVoices,proto3" json:"custom_voices,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UpdateAIPartnerRequest_Partner) Reset()         { *m = UpdateAIPartnerRequest_Partner{} }
func (m *UpdateAIPartnerRequest_Partner) String() string { return proto.CompactTextString(m) }
func (*UpdateAIPartnerRequest_Partner) ProtoMessage()    {}
func (*UpdateAIPartnerRequest_Partner) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{29, 0}
}
func (m *UpdateAIPartnerRequest_Partner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIPartnerRequest_Partner.Unmarshal(m, b)
}
func (m *UpdateAIPartnerRequest_Partner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIPartnerRequest_Partner.Marshal(b, m, deterministic)
}
func (dst *UpdateAIPartnerRequest_Partner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIPartnerRequest_Partner.Merge(dst, src)
}
func (m *UpdateAIPartnerRequest_Partner) XXX_Size() int {
	return xxx_messageInfo_UpdateAIPartnerRequest_Partner.Size(m)
}
func (m *UpdateAIPartnerRequest_Partner) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIPartnerRequest_Partner.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIPartnerRequest_Partner proto.InternalMessageInfo

func (m *UpdateAIPartnerRequest_Partner) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateAIPartnerRequest_Partner) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpdateAIPartnerRequest_Partner) GetCallName() string {
	if m != nil {
		return m.CallName
	}
	return ""
}

func (m *UpdateAIPartnerRequest_Partner) GetCustomVoices() []*CustomVoice {
	if m != nil {
		return m.CustomVoices
	}
	return nil
}

type UpdateAIPartnerResponse struct {
	NameResult           AuditResult `protobuf:"varint,1,opt,name=name_result,json=nameResult,proto3,enum=aigc_http_logic.AuditResult" json:"name_result,omitempty"`
	CallNameResult       AuditResult `protobuf:"varint,2,opt,name=call_name_result,json=callNameResult,proto3,enum=aigc_http_logic.AuditResult" json:"call_name_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpdateAIPartnerResponse) Reset()         { *m = UpdateAIPartnerResponse{} }
func (m *UpdateAIPartnerResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateAIPartnerResponse) ProtoMessage()    {}
func (*UpdateAIPartnerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{30}
}
func (m *UpdateAIPartnerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIPartnerResponse.Unmarshal(m, b)
}
func (m *UpdateAIPartnerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIPartnerResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateAIPartnerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIPartnerResponse.Merge(dst, src)
}
func (m *UpdateAIPartnerResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateAIPartnerResponse.Size(m)
}
func (m *UpdateAIPartnerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIPartnerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIPartnerResponse proto.InternalMessageInfo

func (m *UpdateAIPartnerResponse) GetNameResult() AuditResult {
	if m != nil {
		return m.NameResult
	}
	return AuditResult_AuditResultReview
}

func (m *UpdateAIPartnerResponse) GetCallNameResult() AuditResult {
	if m != nil {
		return m.CallNameResult
	}
	return AuditResult_AuditResultReview
}

type SwitchAIPartnerSilentRequest struct {
	// @gotags: binding:"required"
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" binding:"required"`
	Silent               bool     `protobuf:"varint,2,opt,name=silent,proto3" json:"silent,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchAIPartnerSilentRequest) Reset()         { *m = SwitchAIPartnerSilentRequest{} }
func (m *SwitchAIPartnerSilentRequest) String() string { return proto.CompactTextString(m) }
func (*SwitchAIPartnerSilentRequest) ProtoMessage()    {}
func (*SwitchAIPartnerSilentRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{31}
}
func (m *SwitchAIPartnerSilentRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchAIPartnerSilentRequest.Unmarshal(m, b)
}
func (m *SwitchAIPartnerSilentRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchAIPartnerSilentRequest.Marshal(b, m, deterministic)
}
func (dst *SwitchAIPartnerSilentRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchAIPartnerSilentRequest.Merge(dst, src)
}
func (m *SwitchAIPartnerSilentRequest) XXX_Size() int {
	return xxx_messageInfo_SwitchAIPartnerSilentRequest.Size(m)
}
func (m *SwitchAIPartnerSilentRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchAIPartnerSilentRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchAIPartnerSilentRequest proto.InternalMessageInfo

func (m *SwitchAIPartnerSilentRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SwitchAIPartnerSilentRequest) GetSilent() bool {
	if m != nil {
		return m.Silent
	}
	return false
}

type SwitchAIPartnerSilentResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchAIPartnerSilentResponse) Reset()         { *m = SwitchAIPartnerSilentResponse{} }
func (m *SwitchAIPartnerSilentResponse) String() string { return proto.CompactTextString(m) }
func (*SwitchAIPartnerSilentResponse) ProtoMessage()    {}
func (*SwitchAIPartnerSilentResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{32}
}
func (m *SwitchAIPartnerSilentResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchAIPartnerSilentResponse.Unmarshal(m, b)
}
func (m *SwitchAIPartnerSilentResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchAIPartnerSilentResponse.Marshal(b, m, deterministic)
}
func (dst *SwitchAIPartnerSilentResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchAIPartnerSilentResponse.Merge(dst, src)
}
func (m *SwitchAIPartnerSilentResponse) XXX_Size() int {
	return xxx_messageInfo_SwitchAIPartnerSilentResponse.Size(m)
}
func (m *SwitchAIPartnerSilentResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchAIPartnerSilentResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchAIPartnerSilentResponse proto.InternalMessageInfo

type RebindAIPartnerRoleRequest struct {
	// @gotags: binding:"required"
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" binding:"required"`
	// @gotags: binding:"required"
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty" binding:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RebindAIPartnerRoleRequest) Reset()         { *m = RebindAIPartnerRoleRequest{} }
func (m *RebindAIPartnerRoleRequest) String() string { return proto.CompactTextString(m) }
func (*RebindAIPartnerRoleRequest) ProtoMessage()    {}
func (*RebindAIPartnerRoleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{33}
}
func (m *RebindAIPartnerRoleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RebindAIPartnerRoleRequest.Unmarshal(m, b)
}
func (m *RebindAIPartnerRoleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RebindAIPartnerRoleRequest.Marshal(b, m, deterministic)
}
func (dst *RebindAIPartnerRoleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RebindAIPartnerRoleRequest.Merge(dst, src)
}
func (m *RebindAIPartnerRoleRequest) XXX_Size() int {
	return xxx_messageInfo_RebindAIPartnerRoleRequest.Size(m)
}
func (m *RebindAIPartnerRoleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RebindAIPartnerRoleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RebindAIPartnerRoleRequest proto.InternalMessageInfo

func (m *RebindAIPartnerRoleRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RebindAIPartnerRoleRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type RebindAIPartnerRoleResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RebindAIPartnerRoleResponse) Reset()         { *m = RebindAIPartnerRoleResponse{} }
func (m *RebindAIPartnerRoleResponse) String() string { return proto.CompactTextString(m) }
func (*RebindAIPartnerRoleResponse) ProtoMessage()    {}
func (*RebindAIPartnerRoleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{34}
}
func (m *RebindAIPartnerRoleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RebindAIPartnerRoleResponse.Unmarshal(m, b)
}
func (m *RebindAIPartnerRoleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RebindAIPartnerRoleResponse.Marshal(b, m, deterministic)
}
func (dst *RebindAIPartnerRoleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RebindAIPartnerRoleResponse.Merge(dst, src)
}
func (m *RebindAIPartnerRoleResponse) XXX_Size() int {
	return xxx_messageInfo_RebindAIPartnerRoleResponse.Size(m)
}
func (m *RebindAIPartnerRoleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RebindAIPartnerRoleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RebindAIPartnerRoleResponse proto.InternalMessageInfo

type ReportAIPartnerChattingRequest struct {
	// @gotags: binding:"required"
	PartnerId            uint32   `protobuf:"varint,1,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty" binding:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportAIPartnerChattingRequest) Reset()         { *m = ReportAIPartnerChattingRequest{} }
func (m *ReportAIPartnerChattingRequest) String() string { return proto.CompactTextString(m) }
func (*ReportAIPartnerChattingRequest) ProtoMessage()    {}
func (*ReportAIPartnerChattingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{35}
}
func (m *ReportAIPartnerChattingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportAIPartnerChattingRequest.Unmarshal(m, b)
}
func (m *ReportAIPartnerChattingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportAIPartnerChattingRequest.Marshal(b, m, deterministic)
}
func (dst *ReportAIPartnerChattingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportAIPartnerChattingRequest.Merge(dst, src)
}
func (m *ReportAIPartnerChattingRequest) XXX_Size() int {
	return xxx_messageInfo_ReportAIPartnerChattingRequest.Size(m)
}
func (m *ReportAIPartnerChattingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportAIPartnerChattingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReportAIPartnerChattingRequest proto.InternalMessageInfo

func (m *ReportAIPartnerChattingRequest) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

type ReportAIPartnerChattingResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportAIPartnerChattingResponse) Reset()         { *m = ReportAIPartnerChattingResponse{} }
func (m *ReportAIPartnerChattingResponse) String() string { return proto.CompactTextString(m) }
func (*ReportAIPartnerChattingResponse) ProtoMessage()    {}
func (*ReportAIPartnerChattingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{36}
}
func (m *ReportAIPartnerChattingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportAIPartnerChattingResponse.Unmarshal(m, b)
}
func (m *ReportAIPartnerChattingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportAIPartnerChattingResponse.Marshal(b, m, deterministic)
}
func (dst *ReportAIPartnerChattingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportAIPartnerChattingResponse.Merge(dst, src)
}
func (m *ReportAIPartnerChattingResponse) XXX_Size() int {
	return xxx_messageInfo_ReportAIPartnerChattingResponse.Size(m)
}
func (m *ReportAIPartnerChattingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportAIPartnerChattingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReportAIPartnerChattingResponse proto.InternalMessageInfo

type DeleteAIPartnerRequest struct {
	// @gotags: binding:"required"
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" binding:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAIPartnerRequest) Reset()         { *m = DeleteAIPartnerRequest{} }
func (m *DeleteAIPartnerRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteAIPartnerRequest) ProtoMessage()    {}
func (*DeleteAIPartnerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{37}
}
func (m *DeleteAIPartnerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAIPartnerRequest.Unmarshal(m, b)
}
func (m *DeleteAIPartnerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAIPartnerRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteAIPartnerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAIPartnerRequest.Merge(dst, src)
}
func (m *DeleteAIPartnerRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteAIPartnerRequest.Size(m)
}
func (m *DeleteAIPartnerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAIPartnerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAIPartnerRequest proto.InternalMessageInfo

func (m *DeleteAIPartnerRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DeleteAIPartnerResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAIPartnerResponse) Reset()         { *m = DeleteAIPartnerResponse{} }
func (m *DeleteAIPartnerResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteAIPartnerResponse) ProtoMessage()    {}
func (*DeleteAIPartnerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{38}
}
func (m *DeleteAIPartnerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAIPartnerResponse.Unmarshal(m, b)
}
func (m *DeleteAIPartnerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAIPartnerResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteAIPartnerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAIPartnerResponse.Merge(dst, src)
}
func (m *DeleteAIPartnerResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteAIPartnerResponse.Size(m)
}
func (m *DeleteAIPartnerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAIPartnerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAIPartnerResponse proto.InternalMessageInfo

type GetAIPartnerRequest struct {
	// @gotags: form:"role_id,default=0"
	RoleId uint32 `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty" form:"role_id,default=0"`
	// @gotags: form:"role_type,default=0"
	RoleType             uint32   `protobuf:"varint,2,opt,name=role_type,json=roleType,proto3" json:"role_type,omitempty" form:"role_type,default=0"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAIPartnerRequest) Reset()         { *m = GetAIPartnerRequest{} }
func (m *GetAIPartnerRequest) String() string { return proto.CompactTextString(m) }
func (*GetAIPartnerRequest) ProtoMessage()    {}
func (*GetAIPartnerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{39}
}
func (m *GetAIPartnerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIPartnerRequest.Unmarshal(m, b)
}
func (m *GetAIPartnerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIPartnerRequest.Marshal(b, m, deterministic)
}
func (dst *GetAIPartnerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIPartnerRequest.Merge(dst, src)
}
func (m *GetAIPartnerRequest) XXX_Size() int {
	return xxx_messageInfo_GetAIPartnerRequest.Size(m)
}
func (m *GetAIPartnerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIPartnerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIPartnerRequest proto.InternalMessageInfo

func (m *GetAIPartnerRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetAIPartnerRequest) GetRoleType() uint32 {
	if m != nil {
		return m.RoleType
	}
	return 0
}

type GetAIPartnerResponse struct {
	Partner              *AIPartner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAIPartnerResponse) Reset()         { *m = GetAIPartnerResponse{} }
func (m *GetAIPartnerResponse) String() string { return proto.CompactTextString(m) }
func (*GetAIPartnerResponse) ProtoMessage()    {}
func (*GetAIPartnerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{40}
}
func (m *GetAIPartnerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIPartnerResponse.Unmarshal(m, b)
}
func (m *GetAIPartnerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIPartnerResponse.Marshal(b, m, deterministic)
}
func (dst *GetAIPartnerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIPartnerResponse.Merge(dst, src)
}
func (m *GetAIPartnerResponse) XXX_Size() int {
	return xxx_messageInfo_GetAIPartnerResponse.Size(m)
}
func (m *GetAIPartnerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIPartnerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIPartnerResponse proto.InternalMessageInfo

func (m *GetAIPartnerResponse) GetPartner() *AIPartner {
	if m != nil {
		return m.Partner
	}
	return nil
}

type GetAIPetListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAIPetListRequest) Reset()         { *m = GetAIPetListRequest{} }
func (m *GetAIPetListRequest) String() string { return proto.CompactTextString(m) }
func (*GetAIPetListRequest) ProtoMessage()    {}
func (*GetAIPetListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{41}
}
func (m *GetAIPetListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIPetListRequest.Unmarshal(m, b)
}
func (m *GetAIPetListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIPetListRequest.Marshal(b, m, deterministic)
}
func (dst *GetAIPetListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIPetListRequest.Merge(dst, src)
}
func (m *GetAIPetListRequest) XXX_Size() int {
	return xxx_messageInfo_GetAIPetListRequest.Size(m)
}
func (m *GetAIPetListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIPetListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIPetListRequest proto.InternalMessageInfo

type GetAIPetListResponse struct {
	RoleList             []*PetRole `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAIPetListResponse) Reset()         { *m = GetAIPetListResponse{} }
func (m *GetAIPetListResponse) String() string { return proto.CompactTextString(m) }
func (*GetAIPetListResponse) ProtoMessage()    {}
func (*GetAIPetListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{42}
}
func (m *GetAIPetListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIPetListResponse.Unmarshal(m, b)
}
func (m *GetAIPetListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIPetListResponse.Marshal(b, m, deterministic)
}
func (dst *GetAIPetListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIPetListResponse.Merge(dst, src)
}
func (m *GetAIPetListResponse) XXX_Size() int {
	return xxx_messageInfo_GetAIPetListResponse.Size(m)
}
func (m *GetAIPetListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIPetListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIPetListResponse proto.InternalMessageInfo

func (m *GetAIPetListResponse) GetRoleList() []*PetRole {
	if m != nil {
		return m.RoleList
	}
	return nil
}

type GetAIRoleRequest struct {
	// @gotags: binding:"required"
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty" binding:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAIRoleRequest) Reset()         { *m = GetAIRoleRequest{} }
func (m *GetAIRoleRequest) String() string { return proto.CompactTextString(m) }
func (*GetAIRoleRequest) ProtoMessage()    {}
func (*GetAIRoleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{43}
}
func (m *GetAIRoleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIRoleRequest.Unmarshal(m, b)
}
func (m *GetAIRoleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIRoleRequest.Marshal(b, m, deterministic)
}
func (dst *GetAIRoleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIRoleRequest.Merge(dst, src)
}
func (m *GetAIRoleRequest) XXX_Size() int {
	return xxx_messageInfo_GetAIRoleRequest.Size(m)
}
func (m *GetAIRoleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIRoleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIRoleRequest proto.InternalMessageInfo

func (m *GetAIRoleRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetAIRoleResponse struct {
	Role                 *AIRole             `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	RelationRoles        []*RelationRoleInfo `protobuf:"bytes,2,rep,name=relation_roles,json=relationRoles,proto3" json:"relation_roles,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAIRoleResponse) Reset()         { *m = GetAIRoleResponse{} }
func (m *GetAIRoleResponse) String() string { return proto.CompactTextString(m) }
func (*GetAIRoleResponse) ProtoMessage()    {}
func (*GetAIRoleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{44}
}
func (m *GetAIRoleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIRoleResponse.Unmarshal(m, b)
}
func (m *GetAIRoleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIRoleResponse.Marshal(b, m, deterministic)
}
func (dst *GetAIRoleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIRoleResponse.Merge(dst, src)
}
func (m *GetAIRoleResponse) XXX_Size() int {
	return xxx_messageInfo_GetAIRoleResponse.Size(m)
}
func (m *GetAIRoleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIRoleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIRoleResponse proto.InternalMessageInfo

func (m *GetAIRoleResponse) GetRole() *AIRole {
	if m != nil {
		return m.Role
	}
	return nil
}

func (m *GetAIRoleResponse) GetRelationRoles() []*RelationRoleInfo {
	if m != nil {
		return m.RelationRoles
	}
	return nil
}

type CreateAIRoleRequest struct {
	// @gotags: binding:"required"
	Role                 *CreateAIRoleRequest_Role `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty" binding:"required"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *CreateAIRoleRequest) Reset()         { *m = CreateAIRoleRequest{} }
func (m *CreateAIRoleRequest) String() string { return proto.CompactTextString(m) }
func (*CreateAIRoleRequest) ProtoMessage()    {}
func (*CreateAIRoleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{45}
}
func (m *CreateAIRoleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIRoleRequest.Unmarshal(m, b)
}
func (m *CreateAIRoleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIRoleRequest.Marshal(b, m, deterministic)
}
func (dst *CreateAIRoleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIRoleRequest.Merge(dst, src)
}
func (m *CreateAIRoleRequest) XXX_Size() int {
	return xxx_messageInfo_CreateAIRoleRequest.Size(m)
}
func (m *CreateAIRoleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIRoleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIRoleRequest proto.InternalMessageInfo

func (m *CreateAIRoleRequest) GetRole() *CreateAIRoleRequest_Role {
	if m != nil {
		return m.Role
	}
	return nil
}

type CreateAIRoleRequest_Role struct {
	// @gotags: binding:"min=0,max=2"
	Sex int32 `protobuf:"varint,1,opt,name=sex,proto3" json:"sex,omitempty" binding:"min=0,max=2"`
	// @gotags: binding:"required"
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" binding:"required"`
	// @gotags: binding:"min=1,max=2"
	State uint32 `protobuf:"varint,3,opt,name=state,proto3" json:"state,omitempty" binding:"min=1,max=2"`
	// @gotags: binding:"required,url"
	Avatar string `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty" binding:"required,url"`
	// @gotags: binding:"required,url"
	Image string `protobuf:"bytes,5,opt,name=image,proto3" json:"image,omitempty" binding:"required,url"`
	// @gotags: binding:"required"
	Character string `protobuf:"bytes,6,opt,name=character,proto3" json:"character,omitempty" binding:"required"`
	// @gotaags: binding:"required"
	CategoryId string `protobuf:"bytes,7,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// @gotags: binding:"required"
	Prologue             string   `protobuf:"bytes,8,opt,name=prologue,proto3" json:"prologue,omitempty" binding:"required"`
	Timbre               string   `protobuf:"bytes,9,opt,name=timbre,proto3" json:"timbre,omitempty"`
	Tags                 []string `protobuf:"bytes,10,rep,name=tags,proto3" json:"tags,omitempty"`
	CreatorInfoType      uint32   `protobuf:"varint,11,opt,name=creator_info_type,json=creatorInfoType,proto3" json:"creator_info_type,omitempty"`
	UserRoleSetting      string   `protobuf:"bytes,12,opt,name=user_role_setting,json=userRoleSetting,proto3" json:"user_role_setting,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateAIRoleRequest_Role) Reset()         { *m = CreateAIRoleRequest_Role{} }
func (m *CreateAIRoleRequest_Role) String() string { return proto.CompactTextString(m) }
func (*CreateAIRoleRequest_Role) ProtoMessage()    {}
func (*CreateAIRoleRequest_Role) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{45, 0}
}
func (m *CreateAIRoleRequest_Role) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIRoleRequest_Role.Unmarshal(m, b)
}
func (m *CreateAIRoleRequest_Role) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIRoleRequest_Role.Marshal(b, m, deterministic)
}
func (dst *CreateAIRoleRequest_Role) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIRoleRequest_Role.Merge(dst, src)
}
func (m *CreateAIRoleRequest_Role) XXX_Size() int {
	return xxx_messageInfo_CreateAIRoleRequest_Role.Size(m)
}
func (m *CreateAIRoleRequest_Role) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIRoleRequest_Role.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIRoleRequest_Role proto.InternalMessageInfo

func (m *CreateAIRoleRequest_Role) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *CreateAIRoleRequest_Role) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateAIRoleRequest_Role) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *CreateAIRoleRequest_Role) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *CreateAIRoleRequest_Role) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *CreateAIRoleRequest_Role) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *CreateAIRoleRequest_Role) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *CreateAIRoleRequest_Role) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *CreateAIRoleRequest_Role) GetTimbre() string {
	if m != nil {
		return m.Timbre
	}
	return ""
}

func (m *CreateAIRoleRequest_Role) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *CreateAIRoleRequest_Role) GetCreatorInfoType() uint32 {
	if m != nil {
		return m.CreatorInfoType
	}
	return 0
}

func (m *CreateAIRoleRequest_Role) GetUserRoleSetting() string {
	if m != nil {
		return m.UserRoleSetting
	}
	return ""
}

type CreateAIRoleResponse struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateAIRoleResponse) Reset()         { *m = CreateAIRoleResponse{} }
func (m *CreateAIRoleResponse) String() string { return proto.CompactTextString(m) }
func (*CreateAIRoleResponse) ProtoMessage()    {}
func (*CreateAIRoleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{46}
}
func (m *CreateAIRoleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAIRoleResponse.Unmarshal(m, b)
}
func (m *CreateAIRoleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAIRoleResponse.Marshal(b, m, deterministic)
}
func (dst *CreateAIRoleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAIRoleResponse.Merge(dst, src)
}
func (m *CreateAIRoleResponse) XXX_Size() int {
	return xxx_messageInfo_CreateAIRoleResponse.Size(m)
}
func (m *CreateAIRoleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAIRoleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAIRoleResponse proto.InternalMessageInfo

func (m *CreateAIRoleResponse) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type UpdateAIRoleRequest struct {
	// @gotags: binding:"required"
	Role                 *UpdateAIRoleRequest_Role `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty" binding:"required"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UpdateAIRoleRequest) Reset()         { *m = UpdateAIRoleRequest{} }
func (m *UpdateAIRoleRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateAIRoleRequest) ProtoMessage()    {}
func (*UpdateAIRoleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{47}
}
func (m *UpdateAIRoleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIRoleRequest.Unmarshal(m, b)
}
func (m *UpdateAIRoleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIRoleRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateAIRoleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIRoleRequest.Merge(dst, src)
}
func (m *UpdateAIRoleRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateAIRoleRequest.Size(m)
}
func (m *UpdateAIRoleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIRoleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIRoleRequest proto.InternalMessageInfo

func (m *UpdateAIRoleRequest) GetRole() *UpdateAIRoleRequest_Role {
	if m != nil {
		return m.Role
	}
	return nil
}

type UpdateAIRoleRequest_Role struct {
	// @gotags: binding:"required"
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" binding:"required"`
	// @gotags: binding:"min=0,max=2"
	Sex int32 `protobuf:"varint,2,opt,name=sex,proto3" json:"sex,omitempty" binding:"min=0,max=2"`
	// @gotags: binding:"required"
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty" binding:"required"`
	// @gotags: binding:"min=1,max=2"
	State uint32 `protobuf:"varint,4,opt,name=state,proto3" json:"state,omitempty" binding:"min=1,max=2"`
	// @gotags: binding:"required,url"
	Avatar string `protobuf:"bytes,5,opt,name=avatar,proto3" json:"avatar,omitempty" binding:"required,url"`
	// @gotags: binding:"required,url"
	Image string `protobuf:"bytes,6,opt,name=image,proto3" json:"image,omitempty" binding:"required,url"`
	// @gotags: binding:"required"
	Character string `protobuf:"bytes,7,opt,name=character,proto3" json:"character,omitempty" binding:"required"`
	// @gotaags: binding:"required"
	CategoryId string `protobuf:"bytes,8,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// @gotags: binding:"required"
	Prologue             string   `protobuf:"bytes,9,opt,name=prologue,proto3" json:"prologue,omitempty" binding:"required"`
	Timbre               string   `protobuf:"bytes,10,opt,name=timbre,proto3" json:"timbre,omitempty"`
	Tags                 []string `protobuf:"bytes,11,rep,name=tags,proto3" json:"tags,omitempty"`
	CreatorInfoType      uint32   `protobuf:"varint,12,opt,name=creator_info_type,json=creatorInfoType,proto3" json:"creator_info_type,omitempty"`
	UserRoleSetting      string   `protobuf:"bytes,13,opt,name=user_role_setting,json=userRoleSetting,proto3" json:"user_role_setting,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAIRoleRequest_Role) Reset()         { *m = UpdateAIRoleRequest_Role{} }
func (m *UpdateAIRoleRequest_Role) String() string { return proto.CompactTextString(m) }
func (*UpdateAIRoleRequest_Role) ProtoMessage()    {}
func (*UpdateAIRoleRequest_Role) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{47, 0}
}
func (m *UpdateAIRoleRequest_Role) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIRoleRequest_Role.Unmarshal(m, b)
}
func (m *UpdateAIRoleRequest_Role) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIRoleRequest_Role.Marshal(b, m, deterministic)
}
func (dst *UpdateAIRoleRequest_Role) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIRoleRequest_Role.Merge(dst, src)
}
func (m *UpdateAIRoleRequest_Role) XXX_Size() int {
	return xxx_messageInfo_UpdateAIRoleRequest_Role.Size(m)
}
func (m *UpdateAIRoleRequest_Role) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIRoleRequest_Role.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIRoleRequest_Role proto.InternalMessageInfo

func (m *UpdateAIRoleRequest_Role) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateAIRoleRequest_Role) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UpdateAIRoleRequest_Role) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpdateAIRoleRequest_Role) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *UpdateAIRoleRequest_Role) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *UpdateAIRoleRequest_Role) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *UpdateAIRoleRequest_Role) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *UpdateAIRoleRequest_Role) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *UpdateAIRoleRequest_Role) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *UpdateAIRoleRequest_Role) GetTimbre() string {
	if m != nil {
		return m.Timbre
	}
	return ""
}

func (m *UpdateAIRoleRequest_Role) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *UpdateAIRoleRequest_Role) GetCreatorInfoType() uint32 {
	if m != nil {
		return m.CreatorInfoType
	}
	return 0
}

func (m *UpdateAIRoleRequest_Role) GetUserRoleSetting() string {
	if m != nil {
		return m.UserRoleSetting
	}
	return ""
}

type UpdateAIRoleResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAIRoleResponse) Reset()         { *m = UpdateAIRoleResponse{} }
func (m *UpdateAIRoleResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateAIRoleResponse) ProtoMessage()    {}
func (*UpdateAIRoleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{48}
}
func (m *UpdateAIRoleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIRoleResponse.Unmarshal(m, b)
}
func (m *UpdateAIRoleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIRoleResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateAIRoleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIRoleResponse.Merge(dst, src)
}
func (m *UpdateAIRoleResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateAIRoleResponse.Size(m)
}
func (m *UpdateAIRoleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIRoleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIRoleResponse proto.InternalMessageInfo

type DeleteAIRoleRequest struct {
	// @gotags: binding:"required"
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" binding:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAIRoleRequest) Reset()         { *m = DeleteAIRoleRequest{} }
func (m *DeleteAIRoleRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteAIRoleRequest) ProtoMessage()    {}
func (*DeleteAIRoleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{49}
}
func (m *DeleteAIRoleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAIRoleRequest.Unmarshal(m, b)
}
func (m *DeleteAIRoleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAIRoleRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteAIRoleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAIRoleRequest.Merge(dst, src)
}
func (m *DeleteAIRoleRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteAIRoleRequest.Size(m)
}
func (m *DeleteAIRoleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAIRoleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAIRoleRequest proto.InternalMessageInfo

func (m *DeleteAIRoleRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DeleteAIRoleResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAIRoleResponse) Reset()         { *m = DeleteAIRoleResponse{} }
func (m *DeleteAIRoleResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteAIRoleResponse) ProtoMessage()    {}
func (*DeleteAIRoleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{50}
}
func (m *DeleteAIRoleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAIRoleResponse.Unmarshal(m, b)
}
func (m *DeleteAIRoleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAIRoleResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteAIRoleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAIRoleResponse.Merge(dst, src)
}
func (m *DeleteAIRoleResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteAIRoleResponse.Size(m)
}
func (m *DeleteAIRoleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAIRoleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAIRoleResponse proto.InternalMessageInfo

type ShareRoleRequest struct {
	// @gotags: binding:"required"
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty" binding:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShareRoleRequest) Reset()         { *m = ShareRoleRequest{} }
func (m *ShareRoleRequest) String() string { return proto.CompactTextString(m) }
func (*ShareRoleRequest) ProtoMessage()    {}
func (*ShareRoleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{51}
}
func (m *ShareRoleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShareRoleRequest.Unmarshal(m, b)
}
func (m *ShareRoleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShareRoleRequest.Marshal(b, m, deterministic)
}
func (dst *ShareRoleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShareRoleRequest.Merge(dst, src)
}
func (m *ShareRoleRequest) XXX_Size() int {
	return xxx_messageInfo_ShareRoleRequest.Size(m)
}
func (m *ShareRoleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ShareRoleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ShareRoleRequest proto.InternalMessageInfo

func (m *ShareRoleRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type ShareRoleResponse struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	ExpireAt             int64    `protobuf:"varint,2,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShareRoleResponse) Reset()         { *m = ShareRoleResponse{} }
func (m *ShareRoleResponse) String() string { return proto.CompactTextString(m) }
func (*ShareRoleResponse) ProtoMessage()    {}
func (*ShareRoleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{52}
}
func (m *ShareRoleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShareRoleResponse.Unmarshal(m, b)
}
func (m *ShareRoleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShareRoleResponse.Marshal(b, m, deterministic)
}
func (dst *ShareRoleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShareRoleResponse.Merge(dst, src)
}
func (m *ShareRoleResponse) XXX_Size() int {
	return xxx_messageInfo_ShareRoleResponse.Size(m)
}
func (m *ShareRoleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ShareRoleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ShareRoleResponse proto.InternalMessageInfo

func (m *ShareRoleResponse) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ShareRoleResponse) GetExpireAt() int64 {
	if m != nil {
		return m.ExpireAt
	}
	return 0
}

type LikeRoleRequest struct {
	// @gotags: binding:"required"
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" binding:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LikeRoleRequest) Reset()         { *m = LikeRoleRequest{} }
func (m *LikeRoleRequest) String() string { return proto.CompactTextString(m) }
func (*LikeRoleRequest) ProtoMessage()    {}
func (*LikeRoleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{53}
}
func (m *LikeRoleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LikeRoleRequest.Unmarshal(m, b)
}
func (m *LikeRoleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LikeRoleRequest.Marshal(b, m, deterministic)
}
func (dst *LikeRoleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LikeRoleRequest.Merge(dst, src)
}
func (m *LikeRoleRequest) XXX_Size() int {
	return xxx_messageInfo_LikeRoleRequest.Size(m)
}
func (m *LikeRoleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LikeRoleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LikeRoleRequest proto.InternalMessageInfo

func (m *LikeRoleRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type LikeRoleResponse struct {
	LikeState            uint32   `protobuf:"varint,1,opt,name=like_state,json=likeState,proto3" json:"like_state,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LikeRoleResponse) Reset()         { *m = LikeRoleResponse{} }
func (m *LikeRoleResponse) String() string { return proto.CompactTextString(m) }
func (*LikeRoleResponse) ProtoMessage()    {}
func (*LikeRoleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{54}
}
func (m *LikeRoleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LikeRoleResponse.Unmarshal(m, b)
}
func (m *LikeRoleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LikeRoleResponse.Marshal(b, m, deterministic)
}
func (dst *LikeRoleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LikeRoleResponse.Merge(dst, src)
}
func (m *LikeRoleResponse) XXX_Size() int {
	return xxx_messageInfo_LikeRoleResponse.Size(m)
}
func (m *LikeRoleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LikeRoleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LikeRoleResponse proto.InternalMessageInfo

func (m *LikeRoleResponse) GetLikeState() uint32 {
	if m != nil {
		return m.LikeState
	}
	return 0
}

type GetUserAIRoleListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAIRoleListRequest) Reset()         { *m = GetUserAIRoleListRequest{} }
func (m *GetUserAIRoleListRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserAIRoleListRequest) ProtoMessage()    {}
func (*GetUserAIRoleListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{55}
}
func (m *GetUserAIRoleListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIRoleListRequest.Unmarshal(m, b)
}
func (m *GetUserAIRoleListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIRoleListRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserAIRoleListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIRoleListRequest.Merge(dst, src)
}
func (m *GetUserAIRoleListRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserAIRoleListRequest.Size(m)
}
func (m *GetUserAIRoleListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIRoleListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIRoleListRequest proto.InternalMessageInfo

type GetUserAIRoleListResponse struct {
	Roles                []*AIRole `protobuf:"bytes,1,rep,name=roles,proto3" json:"roles,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetUserAIRoleListResponse) Reset()         { *m = GetUserAIRoleListResponse{} }
func (m *GetUserAIRoleListResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserAIRoleListResponse) ProtoMessage()    {}
func (*GetUserAIRoleListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{56}
}
func (m *GetUserAIRoleListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIRoleListResponse.Unmarshal(m, b)
}
func (m *GetUserAIRoleListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIRoleListResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserAIRoleListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIRoleListResponse.Merge(dst, src)
}
func (m *GetUserAIRoleListResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserAIRoleListResponse.Size(m)
}
func (m *GetUserAIRoleListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIRoleListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIRoleListResponse proto.InternalMessageInfo

func (m *GetUserAIRoleListResponse) GetRoles() []*AIRole {
	if m != nil {
		return m.Roles
	}
	return nil
}

type GetBannerAIRoleListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBannerAIRoleListRequest) Reset()         { *m = GetBannerAIRoleListRequest{} }
func (m *GetBannerAIRoleListRequest) String() string { return proto.CompactTextString(m) }
func (*GetBannerAIRoleListRequest) ProtoMessage()    {}
func (*GetBannerAIRoleListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{57}
}
func (m *GetBannerAIRoleListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannerAIRoleListRequest.Unmarshal(m, b)
}
func (m *GetBannerAIRoleListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannerAIRoleListRequest.Marshal(b, m, deterministic)
}
func (dst *GetBannerAIRoleListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannerAIRoleListRequest.Merge(dst, src)
}
func (m *GetBannerAIRoleListRequest) XXX_Size() int {
	return xxx_messageInfo_GetBannerAIRoleListRequest.Size(m)
}
func (m *GetBannerAIRoleListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannerAIRoleListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannerAIRoleListRequest proto.InternalMessageInfo

type GetBannerAIRoleListResponse struct {
	List                 []*BannerRole `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetBannerAIRoleListResponse) Reset()         { *m = GetBannerAIRoleListResponse{} }
func (m *GetBannerAIRoleListResponse) String() string { return proto.CompactTextString(m) }
func (*GetBannerAIRoleListResponse) ProtoMessage()    {}
func (*GetBannerAIRoleListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{58}
}
func (m *GetBannerAIRoleListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannerAIRoleListResponse.Unmarshal(m, b)
}
func (m *GetBannerAIRoleListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannerAIRoleListResponse.Marshal(b, m, deterministic)
}
func (dst *GetBannerAIRoleListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannerAIRoleListResponse.Merge(dst, src)
}
func (m *GetBannerAIRoleListResponse) XXX_Size() int {
	return xxx_messageInfo_GetBannerAIRoleListResponse.Size(m)
}
func (m *GetBannerAIRoleListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannerAIRoleListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannerAIRoleListResponse proto.InternalMessageInfo

func (m *GetBannerAIRoleListResponse) GetList() []*BannerRole {
	if m != nil {
		return m.List
	}
	return nil
}

type GetRcmdAIRoleListRequest struct {
	// @gotags: binding:"required"
	RoleTagId string `protobuf:"bytes,1,opt,name=role_tag_id,json=roleTagId,proto3" json:"role_tag_id,omitempty" binding:"required"`
	// @gotags: binding:"required"
	GetMode              uint32                           `protobuf:"varint,2,opt,name=get_mode,json=getMode,proto3" json:"get_mode,omitempty" binding:"required"`
	NoBrowseList         []uint32                         `protobuf:"varint,3,rep,packed,name=no_browse_list,json=noBrowseList,proto3" json:"no_browse_list,omitempty"`
	Props                []*GetRcmdAIRoleListRequest_Prop `protobuf:"bytes,4,rep,name=props,proto3" json:"props,omitempty"`
	TopEntities          []*Entity                        `protobuf:"bytes,5,rep,name=top_entities,json=topEntities,proto3" json:"top_entities,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *GetRcmdAIRoleListRequest) Reset()         { *m = GetRcmdAIRoleListRequest{} }
func (m *GetRcmdAIRoleListRequest) String() string { return proto.CompactTextString(m) }
func (*GetRcmdAIRoleListRequest) ProtoMessage()    {}
func (*GetRcmdAIRoleListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{59}
}
func (m *GetRcmdAIRoleListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRcmdAIRoleListRequest.Unmarshal(m, b)
}
func (m *GetRcmdAIRoleListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRcmdAIRoleListRequest.Marshal(b, m, deterministic)
}
func (dst *GetRcmdAIRoleListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRcmdAIRoleListRequest.Merge(dst, src)
}
func (m *GetRcmdAIRoleListRequest) XXX_Size() int {
	return xxx_messageInfo_GetRcmdAIRoleListRequest.Size(m)
}
func (m *GetRcmdAIRoleListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRcmdAIRoleListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRcmdAIRoleListRequest proto.InternalMessageInfo

func (m *GetRcmdAIRoleListRequest) GetRoleTagId() string {
	if m != nil {
		return m.RoleTagId
	}
	return ""
}

func (m *GetRcmdAIRoleListRequest) GetGetMode() uint32 {
	if m != nil {
		return m.GetMode
	}
	return 0
}

func (m *GetRcmdAIRoleListRequest) GetNoBrowseList() []uint32 {
	if m != nil {
		return m.NoBrowseList
	}
	return nil
}

func (m *GetRcmdAIRoleListRequest) GetProps() []*GetRcmdAIRoleListRequest_Prop {
	if m != nil {
		return m.Props
	}
	return nil
}

func (m *GetRcmdAIRoleListRequest) GetTopEntities() []*Entity {
	if m != nil {
		return m.TopEntities
	}
	return nil
}

type GetRcmdAIRoleListRequest_Prop struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Labels               []string `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRcmdAIRoleListRequest_Prop) Reset()         { *m = GetRcmdAIRoleListRequest_Prop{} }
func (m *GetRcmdAIRoleListRequest_Prop) String() string { return proto.CompactTextString(m) }
func (*GetRcmdAIRoleListRequest_Prop) ProtoMessage()    {}
func (*GetRcmdAIRoleListRequest_Prop) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{59, 0}
}
func (m *GetRcmdAIRoleListRequest_Prop) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRcmdAIRoleListRequest_Prop.Unmarshal(m, b)
}
func (m *GetRcmdAIRoleListRequest_Prop) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRcmdAIRoleListRequest_Prop.Marshal(b, m, deterministic)
}
func (dst *GetRcmdAIRoleListRequest_Prop) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRcmdAIRoleListRequest_Prop.Merge(dst, src)
}
func (m *GetRcmdAIRoleListRequest_Prop) XXX_Size() int {
	return xxx_messageInfo_GetRcmdAIRoleListRequest_Prop.Size(m)
}
func (m *GetRcmdAIRoleListRequest_Prop) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRcmdAIRoleListRequest_Prop.DiscardUnknown(m)
}

var xxx_messageInfo_GetRcmdAIRoleListRequest_Prop proto.InternalMessageInfo

func (m *GetRcmdAIRoleListRequest_Prop) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetRcmdAIRoleListRequest_Prop) GetLabels() []string {
	if m != nil {
		return m.Labels
	}
	return nil
}

type GetRcmdAIRoleListResponse struct {
	RoleList             []*RcmdRole        `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	LoadFinish           bool               `protobuf:"varint,2,opt,name=load_finish,json=loadFinish,proto3" json:"load_finish,omitempty"`
	MixDatas             []*ListMixDataItem `protobuf:"bytes,3,rep,name=mix_datas,json=mixDatas,proto3" json:"mix_datas,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetRcmdAIRoleListResponse) Reset()         { *m = GetRcmdAIRoleListResponse{} }
func (m *GetRcmdAIRoleListResponse) String() string { return proto.CompactTextString(m) }
func (*GetRcmdAIRoleListResponse) ProtoMessage()    {}
func (*GetRcmdAIRoleListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{60}
}
func (m *GetRcmdAIRoleListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRcmdAIRoleListResponse.Unmarshal(m, b)
}
func (m *GetRcmdAIRoleListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRcmdAIRoleListResponse.Marshal(b, m, deterministic)
}
func (dst *GetRcmdAIRoleListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRcmdAIRoleListResponse.Merge(dst, src)
}
func (m *GetRcmdAIRoleListResponse) XXX_Size() int {
	return xxx_messageInfo_GetRcmdAIRoleListResponse.Size(m)
}
func (m *GetRcmdAIRoleListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRcmdAIRoleListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRcmdAIRoleListResponse proto.InternalMessageInfo

func (m *GetRcmdAIRoleListResponse) GetRoleList() []*RcmdRole {
	if m != nil {
		return m.RoleList
	}
	return nil
}

func (m *GetRcmdAIRoleListResponse) GetLoadFinish() bool {
	if m != nil {
		return m.LoadFinish
	}
	return false
}

func (m *GetRcmdAIRoleListResponse) GetMixDatas() []*ListMixDataItem {
	if m != nil {
		return m.MixDatas
	}
	return nil
}

type ListMixDataItem struct {
	// 1-角色 2-群聊
	Type                 uint32           `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	RoleInfo             *RcmdRole        `protobuf:"bytes,2,opt,name=role_info,json=roleInfo,proto3" json:"role_info,omitempty"`
	GroupInfo            *GroupDetailInfo `protobuf:"bytes,3,opt,name=group_info,json=groupInfo,proto3" json:"group_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ListMixDataItem) Reset()         { *m = ListMixDataItem{} }
func (m *ListMixDataItem) String() string { return proto.CompactTextString(m) }
func (*ListMixDataItem) ProtoMessage()    {}
func (*ListMixDataItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{61}
}
func (m *ListMixDataItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMixDataItem.Unmarshal(m, b)
}
func (m *ListMixDataItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMixDataItem.Marshal(b, m, deterministic)
}
func (dst *ListMixDataItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMixDataItem.Merge(dst, src)
}
func (m *ListMixDataItem) XXX_Size() int {
	return xxx_messageInfo_ListMixDataItem.Size(m)
}
func (m *ListMixDataItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMixDataItem.DiscardUnknown(m)
}

var xxx_messageInfo_ListMixDataItem proto.InternalMessageInfo

func (m *ListMixDataItem) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ListMixDataItem) GetRoleInfo() *RcmdRole {
	if m != nil {
		return m.RoleInfo
	}
	return nil
}

func (m *ListMixDataItem) GetGroupInfo() *GroupDetailInfo {
	if m != nil {
		return m.GroupInfo
	}
	return nil
}

type GroupDetailInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Character            string   `protobuf:"bytes,3,opt,name=character,proto3" json:"character,omitempty"`
	Tags                 []string `protobuf:"bytes,4,rep,name=tags,proto3" json:"tags,omitempty"`
	Avatar               string   `protobuf:"bytes,5,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Sex                  int32    `protobuf:"varint,6,opt,name=sex,proto3" json:"sex,omitempty"`
	ChatBackground       string   `protobuf:"bytes,7,opt,name=chat_background,json=chatBackground,proto3" json:"chat_background,omitempty"`
	HomeBackground       string   `protobuf:"bytes,8,opt,name=home_background,json=homeBackground,proto3" json:"home_background,omitempty"`
	GroupIcon            string   `protobuf:"bytes,9,opt,name=group_icon,json=groupIcon,proto3" json:"group_icon,omitempty"`
	LikeNum              uint32   `protobuf:"varint,10,opt,name=like_num,json=likeNum,proto3" json:"like_num,omitempty"`
	TemplateId           uint32   `protobuf:"varint,11,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	OwnerUid             uint32   `protobuf:"varint,12,opt,name=owner_uid,json=ownerUid,proto3" json:"owner_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupDetailInfo) Reset()         { *m = GroupDetailInfo{} }
func (m *GroupDetailInfo) String() string { return proto.CompactTextString(m) }
func (*GroupDetailInfo) ProtoMessage()    {}
func (*GroupDetailInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{62}
}
func (m *GroupDetailInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupDetailInfo.Unmarshal(m, b)
}
func (m *GroupDetailInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupDetailInfo.Marshal(b, m, deterministic)
}
func (dst *GroupDetailInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupDetailInfo.Merge(dst, src)
}
func (m *GroupDetailInfo) XXX_Size() int {
	return xxx_messageInfo_GroupDetailInfo.Size(m)
}
func (m *GroupDetailInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupDetailInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GroupDetailInfo proto.InternalMessageInfo

func (m *GroupDetailInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupDetailInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GroupDetailInfo) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *GroupDetailInfo) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *GroupDetailInfo) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *GroupDetailInfo) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GroupDetailInfo) GetChatBackground() string {
	if m != nil {
		return m.ChatBackground
	}
	return ""
}

func (m *GroupDetailInfo) GetHomeBackground() string {
	if m != nil {
		return m.HomeBackground
	}
	return ""
}

func (m *GroupDetailInfo) GetGroupIcon() string {
	if m != nil {
		return m.GroupIcon
	}
	return ""
}

func (m *GroupDetailInfo) GetLikeNum() uint32 {
	if m != nil {
		return m.LikeNum
	}
	return 0
}

func (m *GroupDetailInfo) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *GroupDetailInfo) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

type GetAIRoleCategoryListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAIRoleCategoryListRequest) Reset()         { *m = GetAIRoleCategoryListRequest{} }
func (m *GetAIRoleCategoryListRequest) String() string { return proto.CompactTextString(m) }
func (*GetAIRoleCategoryListRequest) ProtoMessage()    {}
func (*GetAIRoleCategoryListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{63}
}
func (m *GetAIRoleCategoryListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIRoleCategoryListRequest.Unmarshal(m, b)
}
func (m *GetAIRoleCategoryListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIRoleCategoryListRequest.Marshal(b, m, deterministic)
}
func (dst *GetAIRoleCategoryListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIRoleCategoryListRequest.Merge(dst, src)
}
func (m *GetAIRoleCategoryListRequest) XXX_Size() int {
	return xxx_messageInfo_GetAIRoleCategoryListRequest.Size(m)
}
func (m *GetAIRoleCategoryListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIRoleCategoryListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIRoleCategoryListRequest proto.InternalMessageInfo

type GetAIRoleCategoryListResponse struct {
	CategoryInfos        []*AIRoleCategory `protobuf:"bytes,1,rep,name=category_infos,json=categoryInfos,proto3" json:"category_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAIRoleCategoryListResponse) Reset()         { *m = GetAIRoleCategoryListResponse{} }
func (m *GetAIRoleCategoryListResponse) String() string { return proto.CompactTextString(m) }
func (*GetAIRoleCategoryListResponse) ProtoMessage()    {}
func (*GetAIRoleCategoryListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{64}
}
func (m *GetAIRoleCategoryListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAIRoleCategoryListResponse.Unmarshal(m, b)
}
func (m *GetAIRoleCategoryListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAIRoleCategoryListResponse.Marshal(b, m, deterministic)
}
func (dst *GetAIRoleCategoryListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAIRoleCategoryListResponse.Merge(dst, src)
}
func (m *GetAIRoleCategoryListResponse) XXX_Size() int {
	return xxx_messageInfo_GetAIRoleCategoryListResponse.Size(m)
}
func (m *GetAIRoleCategoryListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAIRoleCategoryListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAIRoleCategoryListResponse proto.InternalMessageInfo

func (m *GetAIRoleCategoryListResponse) GetCategoryInfos() []*AIRoleCategory {
	if m != nil {
		return m.CategoryInfos
	}
	return nil
}

type HandleRcmdCommandRequest struct {
	Cmd                  string   `protobuf:"bytes,1,opt,name=cmd,proto3" json:"cmd,omitempty"`
	Payload              []byte   `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleRcmdCommandRequest) Reset()         { *m = HandleRcmdCommandRequest{} }
func (m *HandleRcmdCommandRequest) String() string { return proto.CompactTextString(m) }
func (*HandleRcmdCommandRequest) ProtoMessage()    {}
func (*HandleRcmdCommandRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{65}
}
func (m *HandleRcmdCommandRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleRcmdCommandRequest.Unmarshal(m, b)
}
func (m *HandleRcmdCommandRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleRcmdCommandRequest.Marshal(b, m, deterministic)
}
func (dst *HandleRcmdCommandRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleRcmdCommandRequest.Merge(dst, src)
}
func (m *HandleRcmdCommandRequest) XXX_Size() int {
	return xxx_messageInfo_HandleRcmdCommandRequest.Size(m)
}
func (m *HandleRcmdCommandRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleRcmdCommandRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HandleRcmdCommandRequest proto.InternalMessageInfo

func (m *HandleRcmdCommandRequest) GetCmd() string {
	if m != nil {
		return m.Cmd
	}
	return ""
}

func (m *HandleRcmdCommandRequest) GetPayload() []byte {
	if m != nil {
		return m.Payload
	}
	return nil
}

type HandleRcmdCommandResponse struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data                 []byte   `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleRcmdCommandResponse) Reset()         { *m = HandleRcmdCommandResponse{} }
func (m *HandleRcmdCommandResponse) String() string { return proto.CompactTextString(m) }
func (*HandleRcmdCommandResponse) ProtoMessage()    {}
func (*HandleRcmdCommandResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{66}
}
func (m *HandleRcmdCommandResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleRcmdCommandResponse.Unmarshal(m, b)
}
func (m *HandleRcmdCommandResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleRcmdCommandResponse.Marshal(b, m, deterministic)
}
func (dst *HandleRcmdCommandResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleRcmdCommandResponse.Merge(dst, src)
}
func (m *HandleRcmdCommandResponse) XXX_Size() int {
	return xxx_messageInfo_HandleRcmdCommandResponse.Size(m)
}
func (m *HandleRcmdCommandResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleRcmdCommandResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HandleRcmdCommandResponse proto.InternalMessageInfo

func (m *HandleRcmdCommandResponse) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *HandleRcmdCommandResponse) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *HandleRcmdCommandResponse) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetPetPartnerRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetPartnerRequest) Reset()         { *m = GetPetPartnerRequest{} }
func (m *GetPetPartnerRequest) String() string { return proto.CompactTextString(m) }
func (*GetPetPartnerRequest) ProtoMessage()    {}
func (*GetPetPartnerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{67}
}
func (m *GetPetPartnerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetPartnerRequest.Unmarshal(m, b)
}
func (m *GetPetPartnerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetPartnerRequest.Marshal(b, m, deterministic)
}
func (dst *GetPetPartnerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetPartnerRequest.Merge(dst, src)
}
func (m *GetPetPartnerRequest) XXX_Size() int {
	return xxx_messageInfo_GetPetPartnerRequest.Size(m)
}
func (m *GetPetPartnerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetPartnerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetPartnerRequest proto.InternalMessageInfo

type GetPetPartnerReponse struct {
	Partner              *PetPartner `protobuf:"bytes,1,opt,name=partner,proto3" json:"partner,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetPetPartnerReponse) Reset()         { *m = GetPetPartnerReponse{} }
func (m *GetPetPartnerReponse) String() string { return proto.CompactTextString(m) }
func (*GetPetPartnerReponse) ProtoMessage()    {}
func (*GetPetPartnerReponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{68}
}
func (m *GetPetPartnerReponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetPartnerReponse.Unmarshal(m, b)
}
func (m *GetPetPartnerReponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetPartnerReponse.Marshal(b, m, deterministic)
}
func (dst *GetPetPartnerReponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetPartnerReponse.Merge(dst, src)
}
func (m *GetPetPartnerReponse) XXX_Size() int {
	return xxx_messageInfo_GetPetPartnerReponse.Size(m)
}
func (m *GetPetPartnerReponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetPartnerReponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetPartnerReponse proto.InternalMessageInfo

func (m *GetPetPartnerReponse) GetPartner() *PetPartner {
	if m != nil {
		return m.Partner
	}
	return nil
}

type SearchRoleRequest struct {
	// @gotags: form:"content"
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty" form:"content"`
	// @gotags: form:"last_id"
	LastId               string   `protobuf:"bytes,2,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty" form:"last_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchRoleRequest) Reset()         { *m = SearchRoleRequest{} }
func (m *SearchRoleRequest) String() string { return proto.CompactTextString(m) }
func (*SearchRoleRequest) ProtoMessage()    {}
func (*SearchRoleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{69}
}
func (m *SearchRoleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchRoleRequest.Unmarshal(m, b)
}
func (m *SearchRoleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchRoleRequest.Marshal(b, m, deterministic)
}
func (dst *SearchRoleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchRoleRequest.Merge(dst, src)
}
func (m *SearchRoleRequest) XXX_Size() int {
	return xxx_messageInfo_SearchRoleRequest.Size(m)
}
func (m *SearchRoleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchRoleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SearchRoleRequest proto.InternalMessageInfo

func (m *SearchRoleRequest) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *SearchRoleRequest) GetLastId() string {
	if m != nil {
		return m.LastId
	}
	return ""
}

type SearchRoleResponse struct {
	Data                 []*AIRole            `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	LastId               string               `protobuf:"bytes,2,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	LoadFinish           bool                 `protobuf:"varint,3,opt,name=load_finish,json=loadFinish,proto3" json:"load_finish,omitempty"`
	MixDatas             []*SearchMixDataItem `protobuf:"bytes,4,rep,name=mix_datas,json=mixDatas,proto3" json:"mix_datas,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SearchRoleResponse) Reset()         { *m = SearchRoleResponse{} }
func (m *SearchRoleResponse) String() string { return proto.CompactTextString(m) }
func (*SearchRoleResponse) ProtoMessage()    {}
func (*SearchRoleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{70}
}
func (m *SearchRoleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchRoleResponse.Unmarshal(m, b)
}
func (m *SearchRoleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchRoleResponse.Marshal(b, m, deterministic)
}
func (dst *SearchRoleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchRoleResponse.Merge(dst, src)
}
func (m *SearchRoleResponse) XXX_Size() int {
	return xxx_messageInfo_SearchRoleResponse.Size(m)
}
func (m *SearchRoleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchRoleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SearchRoleResponse proto.InternalMessageInfo

func (m *SearchRoleResponse) GetData() []*AIRole {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *SearchRoleResponse) GetLastId() string {
	if m != nil {
		return m.LastId
	}
	return ""
}

func (m *SearchRoleResponse) GetLoadFinish() bool {
	if m != nil {
		return m.LoadFinish
	}
	return false
}

func (m *SearchRoleResponse) GetMixDatas() []*SearchMixDataItem {
	if m != nil {
		return m.MixDatas
	}
	return nil
}

type SearchMixDataItem struct {
	// 1-角色 2-群聊
	Type                 uint32           `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	RoleInfo             *AIRole          `protobuf:"bytes,2,opt,name=role_info,json=roleInfo,proto3" json:"role_info,omitempty"`
	GroupInfo            *GroupDetailInfo `protobuf:"bytes,3,opt,name=group_info,json=groupInfo,proto3" json:"group_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SearchMixDataItem) Reset()         { *m = SearchMixDataItem{} }
func (m *SearchMixDataItem) String() string { return proto.CompactTextString(m) }
func (*SearchMixDataItem) ProtoMessage()    {}
func (*SearchMixDataItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{71}
}
func (m *SearchMixDataItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchMixDataItem.Unmarshal(m, b)
}
func (m *SearchMixDataItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchMixDataItem.Marshal(b, m, deterministic)
}
func (dst *SearchMixDataItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchMixDataItem.Merge(dst, src)
}
func (m *SearchMixDataItem) XXX_Size() int {
	return xxx_messageInfo_SearchMixDataItem.Size(m)
}
func (m *SearchMixDataItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchMixDataItem.DiscardUnknown(m)
}

var xxx_messageInfo_SearchMixDataItem proto.InternalMessageInfo

func (m *SearchMixDataItem) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *SearchMixDataItem) GetRoleInfo() *AIRole {
	if m != nil {
		return m.RoleInfo
	}
	return nil
}

func (m *SearchMixDataItem) GetGroupInfo() *GroupDetailInfo {
	if m != nil {
		return m.GroupInfo
	}
	return nil
}

type CreateInteractiveGameRequest struct {
	// @gotags: binding:"required"
	Game                 *CreateInteractiveGameRequest_InteractiveGame `protobuf:"bytes,1,opt,name=game,proto3" json:"game,omitempty" binding:"required"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *CreateInteractiveGameRequest) Reset()         { *m = CreateInteractiveGameRequest{} }
func (m *CreateInteractiveGameRequest) String() string { return proto.CompactTextString(m) }
func (*CreateInteractiveGameRequest) ProtoMessage()    {}
func (*CreateInteractiveGameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{72}
}
func (m *CreateInteractiveGameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateInteractiveGameRequest.Unmarshal(m, b)
}
func (m *CreateInteractiveGameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateInteractiveGameRequest.Marshal(b, m, deterministic)
}
func (dst *CreateInteractiveGameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateInteractiveGameRequest.Merge(dst, src)
}
func (m *CreateInteractiveGameRequest) XXX_Size() int {
	return xxx_messageInfo_CreateInteractiveGameRequest.Size(m)
}
func (m *CreateInteractiveGameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateInteractiveGameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateInteractiveGameRequest proto.InternalMessageInfo

func (m *CreateInteractiveGameRequest) GetGame() *CreateInteractiveGameRequest_InteractiveGame {
	if m != nil {
		return m.Game
	}
	return nil
}

type CreateInteractiveGameRequest_InteractiveGame struct {
	// @gotags: binding:"required"
	RoleId uint32 `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty" binding:"required"`
	// @gotags: binding:"required"
	TopicId uint32 `protobuf:"varint,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty" binding:"required"`
	// @gotags: binding:"required,max=30"
	Title string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" binding:"required,max=30"`
	// @gotags: binding:"required,max=300"
	Desc string `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty" binding:"required,max=300"`
	// @gotags: binding:"required,max=50"
	Prologue string `protobuf:"bytes,5,opt,name=prologue,proto3" json:"prologue,omitempty" binding:"required,max=50"`
	// @gotags: binding:"required,min=1,max=2"
	State                uint32   `protobuf:"varint,6,opt,name=state,proto3" json:"state,omitempty" binding:"required,min=1,max=2"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateInteractiveGameRequest_InteractiveGame) Reset() {
	*m = CreateInteractiveGameRequest_InteractiveGame{}
}
func (m *CreateInteractiveGameRequest_InteractiveGame) String() string {
	return proto.CompactTextString(m)
}
func (*CreateInteractiveGameRequest_InteractiveGame) ProtoMessage() {}
func (*CreateInteractiveGameRequest_InteractiveGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{72, 0}
}
func (m *CreateInteractiveGameRequest_InteractiveGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateInteractiveGameRequest_InteractiveGame.Unmarshal(m, b)
}
func (m *CreateInteractiveGameRequest_InteractiveGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateInteractiveGameRequest_InteractiveGame.Marshal(b, m, deterministic)
}
func (dst *CreateInteractiveGameRequest_InteractiveGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateInteractiveGameRequest_InteractiveGame.Merge(dst, src)
}
func (m *CreateInteractiveGameRequest_InteractiveGame) XXX_Size() int {
	return xxx_messageInfo_CreateInteractiveGameRequest_InteractiveGame.Size(m)
}
func (m *CreateInteractiveGameRequest_InteractiveGame) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateInteractiveGameRequest_InteractiveGame.DiscardUnknown(m)
}

var xxx_messageInfo_CreateInteractiveGameRequest_InteractiveGame proto.InternalMessageInfo

func (m *CreateInteractiveGameRequest_InteractiveGame) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *CreateInteractiveGameRequest_InteractiveGame) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CreateInteractiveGameRequest_InteractiveGame) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *CreateInteractiveGameRequest_InteractiveGame) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *CreateInteractiveGameRequest_InteractiveGame) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *CreateInteractiveGameRequest_InteractiveGame) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

type CreateInteractiveGameResponse struct {
	Id                   string      `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TitleAuditResult     AuditResult `protobuf:"varint,2,opt,name=title_audit_result,json=titleAuditResult,proto3,enum=aigc_http_logic.AuditResult" json:"title_audit_result,omitempty"`
	DescAuditResult      AuditResult `protobuf:"varint,3,opt,name=desc_audit_result,json=descAuditResult,proto3,enum=aigc_http_logic.AuditResult" json:"desc_audit_result,omitempty"`
	PrologueAuditResult  AuditResult `protobuf:"varint,4,opt,name=prologue_audit_result,json=prologueAuditResult,proto3,enum=aigc_http_logic.AuditResult" json:"prologue_audit_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CreateInteractiveGameResponse) Reset()         { *m = CreateInteractiveGameResponse{} }
func (m *CreateInteractiveGameResponse) String() string { return proto.CompactTextString(m) }
func (*CreateInteractiveGameResponse) ProtoMessage()    {}
func (*CreateInteractiveGameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{73}
}
func (m *CreateInteractiveGameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateInteractiveGameResponse.Unmarshal(m, b)
}
func (m *CreateInteractiveGameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateInteractiveGameResponse.Marshal(b, m, deterministic)
}
func (dst *CreateInteractiveGameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateInteractiveGameResponse.Merge(dst, src)
}
func (m *CreateInteractiveGameResponse) XXX_Size() int {
	return xxx_messageInfo_CreateInteractiveGameResponse.Size(m)
}
func (m *CreateInteractiveGameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateInteractiveGameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateInteractiveGameResponse proto.InternalMessageInfo

func (m *CreateInteractiveGameResponse) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CreateInteractiveGameResponse) GetTitleAuditResult() AuditResult {
	if m != nil {
		return m.TitleAuditResult
	}
	return AuditResult_AuditResultReview
}

func (m *CreateInteractiveGameResponse) GetDescAuditResult() AuditResult {
	if m != nil {
		return m.DescAuditResult
	}
	return AuditResult_AuditResultReview
}

func (m *CreateInteractiveGameResponse) GetPrologueAuditResult() AuditResult {
	if m != nil {
		return m.PrologueAuditResult
	}
	return AuditResult_AuditResultReview
}

type UpdateInteractiveGameRequest struct {
	// @gotags: binding:"required"
	Game                 *UpdateInteractiveGameRequest_InteractiveGame `protobuf:"bytes,1,opt,name=game,proto3" json:"game,omitempty" binding:"required"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *UpdateInteractiveGameRequest) Reset()         { *m = UpdateInteractiveGameRequest{} }
func (m *UpdateInteractiveGameRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateInteractiveGameRequest) ProtoMessage()    {}
func (*UpdateInteractiveGameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{74}
}
func (m *UpdateInteractiveGameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateInteractiveGameRequest.Unmarshal(m, b)
}
func (m *UpdateInteractiveGameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateInteractiveGameRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateInteractiveGameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateInteractiveGameRequest.Merge(dst, src)
}
func (m *UpdateInteractiveGameRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateInteractiveGameRequest.Size(m)
}
func (m *UpdateInteractiveGameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateInteractiveGameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateInteractiveGameRequest proto.InternalMessageInfo

func (m *UpdateInteractiveGameRequest) GetGame() *UpdateInteractiveGameRequest_InteractiveGame {
	if m != nil {
		return m.Game
	}
	return nil
}

type UpdateInteractiveGameRequest_InteractiveGame struct {
	// @gotags: binding:"required"
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" binding:"required"`
	// @gotags: binding:"required"
	TopicId uint32 `protobuf:"varint,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty" binding:"required"`
	// @gotags: binding:"required,max=30"
	Title string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" binding:"required,max=30"`
	// @gotags: binding:"required,max=300"
	Desc string `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty" binding:"required,max=300"`
	// @gotags: binding:"required,max=50"
	Prologue string `protobuf:"bytes,5,opt,name=prologue,proto3" json:"prologue,omitempty" binding:"required,max=50"`
	// @gotags: binding:"required,min=1,max=2"
	State                uint32   `protobuf:"varint,6,opt,name=state,proto3" json:"state,omitempty" binding:"required,min=1,max=2"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateInteractiveGameRequest_InteractiveGame) Reset() {
	*m = UpdateInteractiveGameRequest_InteractiveGame{}
}
func (m *UpdateInteractiveGameRequest_InteractiveGame) String() string {
	return proto.CompactTextString(m)
}
func (*UpdateInteractiveGameRequest_InteractiveGame) ProtoMessage() {}
func (*UpdateInteractiveGameRequest_InteractiveGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{74, 0}
}
func (m *UpdateInteractiveGameRequest_InteractiveGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateInteractiveGameRequest_InteractiveGame.Unmarshal(m, b)
}
func (m *UpdateInteractiveGameRequest_InteractiveGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateInteractiveGameRequest_InteractiveGame.Marshal(b, m, deterministic)
}
func (dst *UpdateInteractiveGameRequest_InteractiveGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateInteractiveGameRequest_InteractiveGame.Merge(dst, src)
}
func (m *UpdateInteractiveGameRequest_InteractiveGame) XXX_Size() int {
	return xxx_messageInfo_UpdateInteractiveGameRequest_InteractiveGame.Size(m)
}
func (m *UpdateInteractiveGameRequest_InteractiveGame) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateInteractiveGameRequest_InteractiveGame.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateInteractiveGameRequest_InteractiveGame proto.InternalMessageInfo

func (m *UpdateInteractiveGameRequest_InteractiveGame) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpdateInteractiveGameRequest_InteractiveGame) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *UpdateInteractiveGameRequest_InteractiveGame) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *UpdateInteractiveGameRequest_InteractiveGame) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *UpdateInteractiveGameRequest_InteractiveGame) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *UpdateInteractiveGameRequest_InteractiveGame) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

type UpdateInteractiveGameResponse struct {
	TitleAuditResult     AuditResult `protobuf:"varint,1,opt,name=title_audit_result,json=titleAuditResult,proto3,enum=aigc_http_logic.AuditResult" json:"title_audit_result,omitempty"`
	DescAuditResult      AuditResult `protobuf:"varint,2,opt,name=desc_audit_result,json=descAuditResult,proto3,enum=aigc_http_logic.AuditResult" json:"desc_audit_result,omitempty"`
	PrologueAuditResult  AuditResult `protobuf:"varint,3,opt,name=prologue_audit_result,json=prologueAuditResult,proto3,enum=aigc_http_logic.AuditResult" json:"prologue_audit_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpdateInteractiveGameResponse) Reset()         { *m = UpdateInteractiveGameResponse{} }
func (m *UpdateInteractiveGameResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateInteractiveGameResponse) ProtoMessage()    {}
func (*UpdateInteractiveGameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{75}
}
func (m *UpdateInteractiveGameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateInteractiveGameResponse.Unmarshal(m, b)
}
func (m *UpdateInteractiveGameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateInteractiveGameResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateInteractiveGameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateInteractiveGameResponse.Merge(dst, src)
}
func (m *UpdateInteractiveGameResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateInteractiveGameResponse.Size(m)
}
func (m *UpdateInteractiveGameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateInteractiveGameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateInteractiveGameResponse proto.InternalMessageInfo

func (m *UpdateInteractiveGameResponse) GetTitleAuditResult() AuditResult {
	if m != nil {
		return m.TitleAuditResult
	}
	return AuditResult_AuditResultReview
}

func (m *UpdateInteractiveGameResponse) GetDescAuditResult() AuditResult {
	if m != nil {
		return m.DescAuditResult
	}
	return AuditResult_AuditResultReview
}

func (m *UpdateInteractiveGameResponse) GetPrologueAuditResult() AuditResult {
	if m != nil {
		return m.PrologueAuditResult
	}
	return AuditResult_AuditResultReview
}

type DeleteInteractiveGameRequest struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteInteractiveGameRequest) Reset()         { *m = DeleteInteractiveGameRequest{} }
func (m *DeleteInteractiveGameRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteInteractiveGameRequest) ProtoMessage()    {}
func (*DeleteInteractiveGameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{76}
}
func (m *DeleteInteractiveGameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteInteractiveGameRequest.Unmarshal(m, b)
}
func (m *DeleteInteractiveGameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteInteractiveGameRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteInteractiveGameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteInteractiveGameRequest.Merge(dst, src)
}
func (m *DeleteInteractiveGameRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteInteractiveGameRequest.Size(m)
}
func (m *DeleteInteractiveGameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteInteractiveGameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteInteractiveGameRequest proto.InternalMessageInfo

func (m *DeleteInteractiveGameRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DeleteInteractiveGameResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteInteractiveGameResponse) Reset()         { *m = DeleteInteractiveGameResponse{} }
func (m *DeleteInteractiveGameResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteInteractiveGameResponse) ProtoMessage()    {}
func (*DeleteInteractiveGameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{77}
}
func (m *DeleteInteractiveGameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteInteractiveGameResponse.Unmarshal(m, b)
}
func (m *DeleteInteractiveGameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteInteractiveGameResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteInteractiveGameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteInteractiveGameResponse.Merge(dst, src)
}
func (m *DeleteInteractiveGameResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteInteractiveGameResponse.Size(m)
}
func (m *DeleteInteractiveGameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteInteractiveGameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteInteractiveGameResponse proto.InternalMessageInfo

type GetInteractiveGameListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInteractiveGameListRequest) Reset()         { *m = GetInteractiveGameListRequest{} }
func (m *GetInteractiveGameListRequest) String() string { return proto.CompactTextString(m) }
func (*GetInteractiveGameListRequest) ProtoMessage()    {}
func (*GetInteractiveGameListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{78}
}
func (m *GetInteractiveGameListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInteractiveGameListRequest.Unmarshal(m, b)
}
func (m *GetInteractiveGameListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInteractiveGameListRequest.Marshal(b, m, deterministic)
}
func (dst *GetInteractiveGameListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInteractiveGameListRequest.Merge(dst, src)
}
func (m *GetInteractiveGameListRequest) XXX_Size() int {
	return xxx_messageInfo_GetInteractiveGameListRequest.Size(m)
}
func (m *GetInteractiveGameListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInteractiveGameListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetInteractiveGameListRequest proto.InternalMessageInfo

type GetInteractiveGameListResponse struct {
	List                 []*InteractiveGame `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetInteractiveGameListResponse) Reset()         { *m = GetInteractiveGameListResponse{} }
func (m *GetInteractiveGameListResponse) String() string { return proto.CompactTextString(m) }
func (*GetInteractiveGameListResponse) ProtoMessage()    {}
func (*GetInteractiveGameListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{79}
}
func (m *GetInteractiveGameListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInteractiveGameListResponse.Unmarshal(m, b)
}
func (m *GetInteractiveGameListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInteractiveGameListResponse.Marshal(b, m, deterministic)
}
func (dst *GetInteractiveGameListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInteractiveGameListResponse.Merge(dst, src)
}
func (m *GetInteractiveGameListResponse) XXX_Size() int {
	return xxx_messageInfo_GetInteractiveGameListResponse.Size(m)
}
func (m *GetInteractiveGameListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInteractiveGameListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetInteractiveGameListResponse proto.InternalMessageInfo

func (m *GetInteractiveGameListResponse) GetList() []*InteractiveGame {
	if m != nil {
		return m.List
	}
	return nil
}

type GetChattingPartnerListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChattingPartnerListRequest) Reset()         { *m = GetChattingPartnerListRequest{} }
func (m *GetChattingPartnerListRequest) String() string { return proto.CompactTextString(m) }
func (*GetChattingPartnerListRequest) ProtoMessage()    {}
func (*GetChattingPartnerListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{80}
}
func (m *GetChattingPartnerListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChattingPartnerListRequest.Unmarshal(m, b)
}
func (m *GetChattingPartnerListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChattingPartnerListRequest.Marshal(b, m, deterministic)
}
func (dst *GetChattingPartnerListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChattingPartnerListRequest.Merge(dst, src)
}
func (m *GetChattingPartnerListRequest) XXX_Size() int {
	return xxx_messageInfo_GetChattingPartnerListRequest.Size(m)
}
func (m *GetChattingPartnerListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChattingPartnerListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChattingPartnerListRequest proto.InternalMessageInfo

type GetChattingPartnerListResponse struct {
	List                 []*ChattingPartner `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetChattingPartnerListResponse) Reset()         { *m = GetChattingPartnerListResponse{} }
func (m *GetChattingPartnerListResponse) String() string { return proto.CompactTextString(m) }
func (*GetChattingPartnerListResponse) ProtoMessage()    {}
func (*GetChattingPartnerListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{81}
}
func (m *GetChattingPartnerListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChattingPartnerListResponse.Unmarshal(m, b)
}
func (m *GetChattingPartnerListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChattingPartnerListResponse.Marshal(b, m, deterministic)
}
func (dst *GetChattingPartnerListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChattingPartnerListResponse.Merge(dst, src)
}
func (m *GetChattingPartnerListResponse) XXX_Size() int {
	return xxx_messageInfo_GetChattingPartnerListResponse.Size(m)
}
func (m *GetChattingPartnerListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChattingPartnerListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChattingPartnerListResponse proto.InternalMessageInfo

func (m *GetChattingPartnerListResponse) GetList() []*ChattingPartner {
	if m != nil {
		return m.List
	}
	return nil
}

type ChatTemplate struct {
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 模版名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 模版消息内容
	Msgs                 []*TemplateMsg `protobuf:"bytes,3,rep,name=msgs,proto3" json:"msgs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ChatTemplate) Reset()         { *m = ChatTemplate{} }
func (m *ChatTemplate) String() string { return proto.CompactTextString(m) }
func (*ChatTemplate) ProtoMessage()    {}
func (*ChatTemplate) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{82}
}
func (m *ChatTemplate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatTemplate.Unmarshal(m, b)
}
func (m *ChatTemplate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatTemplate.Marshal(b, m, deterministic)
}
func (dst *ChatTemplate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatTemplate.Merge(dst, src)
}
func (m *ChatTemplate) XXX_Size() int {
	return xxx_messageInfo_ChatTemplate.Size(m)
}
func (m *ChatTemplate) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatTemplate.DiscardUnknown(m)
}

var xxx_messageInfo_ChatTemplate proto.InternalMessageInfo

func (m *ChatTemplate) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ChatTemplate) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChatTemplate) GetMsgs() []*TemplateMsg {
	if m != nil {
		return m.Msgs
	}
	return nil
}

type TemplateMsg struct {
	// 消息内容
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	// 发送者类型
	SenderType           TemplateMsg_SenderType `protobuf:"varint,2,opt,name=sender_type,json=senderType,proto3,enum=aigc_http_logic.TemplateMsg_SenderType" json:"sender_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *TemplateMsg) Reset()         { *m = TemplateMsg{} }
func (m *TemplateMsg) String() string { return proto.CompactTextString(m) }
func (*TemplateMsg) ProtoMessage()    {}
func (*TemplateMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{83}
}
func (m *TemplateMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TemplateMsg.Unmarshal(m, b)
}
func (m *TemplateMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TemplateMsg.Marshal(b, m, deterministic)
}
func (dst *TemplateMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TemplateMsg.Merge(dst, src)
}
func (m *TemplateMsg) XXX_Size() int {
	return xxx_messageInfo_TemplateMsg.Size(m)
}
func (m *TemplateMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_TemplateMsg.DiscardUnknown(m)
}

var xxx_messageInfo_TemplateMsg proto.InternalMessageInfo

func (m *TemplateMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *TemplateMsg) GetSenderType() TemplateMsg_SenderType {
	if m != nil {
		return m.SenderType
	}
	return TemplateMsg_SenderType_Unspecified
}

type GetBindChatTemplatesReq struct {
	// 模版关联对象类型
	EntityType uint32 `protobuf:"varint,1,opt,name=entity_type,json=entityType,proto3" json:"entity_type,omitempty"`
	// 对象ID
	EntityId             uint32   `protobuf:"varint,2,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBindChatTemplatesReq) Reset()         { *m = GetBindChatTemplatesReq{} }
func (m *GetBindChatTemplatesReq) String() string { return proto.CompactTextString(m) }
func (*GetBindChatTemplatesReq) ProtoMessage()    {}
func (*GetBindChatTemplatesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{84}
}
func (m *GetBindChatTemplatesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindChatTemplatesReq.Unmarshal(m, b)
}
func (m *GetBindChatTemplatesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindChatTemplatesReq.Marshal(b, m, deterministic)
}
func (dst *GetBindChatTemplatesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindChatTemplatesReq.Merge(dst, src)
}
func (m *GetBindChatTemplatesReq) XXX_Size() int {
	return xxx_messageInfo_GetBindChatTemplatesReq.Size(m)
}
func (m *GetBindChatTemplatesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindChatTemplatesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindChatTemplatesReq proto.InternalMessageInfo

func (m *GetBindChatTemplatesReq) GetEntityType() uint32 {
	if m != nil {
		return m.EntityType
	}
	return 0
}

func (m *GetBindChatTemplatesReq) GetEntityId() uint32 {
	if m != nil {
		return m.EntityId
	}
	return 0
}

type GetBindChatTemplatesResp struct {
	ChatTemplates        []*ChatTemplate `protobuf:"bytes,1,rep,name=chat_templates,json=chatTemplates,proto3" json:"chat_templates,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetBindChatTemplatesResp) Reset()         { *m = GetBindChatTemplatesResp{} }
func (m *GetBindChatTemplatesResp) String() string { return proto.CompactTextString(m) }
func (*GetBindChatTemplatesResp) ProtoMessage()    {}
func (*GetBindChatTemplatesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{85}
}
func (m *GetBindChatTemplatesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindChatTemplatesResp.Unmarshal(m, b)
}
func (m *GetBindChatTemplatesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindChatTemplatesResp.Marshal(b, m, deterministic)
}
func (dst *GetBindChatTemplatesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindChatTemplatesResp.Merge(dst, src)
}
func (m *GetBindChatTemplatesResp) XXX_Size() int {
	return xxx_messageInfo_GetBindChatTemplatesResp.Size(m)
}
func (m *GetBindChatTemplatesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindChatTemplatesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindChatTemplatesResp proto.InternalMessageInfo

func (m *GetBindChatTemplatesResp) GetChatTemplates() []*ChatTemplate {
	if m != nil {
		return m.ChatTemplates
	}
	return nil
}

type CreateGroupRequest struct {
	// @gotags: binding:"required"
	TemplateId           uint32   `protobuf:"varint,1,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty" binding:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGroupRequest) Reset()         { *m = CreateGroupRequest{} }
func (m *CreateGroupRequest) String() string { return proto.CompactTextString(m) }
func (*CreateGroupRequest) ProtoMessage()    {}
func (*CreateGroupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{86}
}
func (m *CreateGroupRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGroupRequest.Unmarshal(m, b)
}
func (m *CreateGroupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGroupRequest.Marshal(b, m, deterministic)
}
func (dst *CreateGroupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGroupRequest.Merge(dst, src)
}
func (m *CreateGroupRequest) XXX_Size() int {
	return xxx_messageInfo_CreateGroupRequest.Size(m)
}
func (m *CreateGroupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGroupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGroupRequest proto.InternalMessageInfo

func (m *CreateGroupRequest) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

type CreateGroupResponse struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGroupResponse) Reset()         { *m = CreateGroupResponse{} }
func (m *CreateGroupResponse) String() string { return proto.CompactTextString(m) }
func (*CreateGroupResponse) ProtoMessage()    {}
func (*CreateGroupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{87}
}
func (m *CreateGroupResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGroupResponse.Unmarshal(m, b)
}
func (m *CreateGroupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGroupResponse.Marshal(b, m, deterministic)
}
func (dst *CreateGroupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGroupResponse.Merge(dst, src)
}
func (m *CreateGroupResponse) XXX_Size() int {
	return xxx_messageInfo_CreateGroupResponse.Size(m)
}
func (m *CreateGroupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGroupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGroupResponse proto.InternalMessageInfo

func (m *CreateGroupResponse) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DeleteGroupRequest struct {
	// @gotags: binding:"required"
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" binding:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteGroupRequest) Reset()         { *m = DeleteGroupRequest{} }
func (m *DeleteGroupRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteGroupRequest) ProtoMessage()    {}
func (*DeleteGroupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{88}
}
func (m *DeleteGroupRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteGroupRequest.Unmarshal(m, b)
}
func (m *DeleteGroupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteGroupRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteGroupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteGroupRequest.Merge(dst, src)
}
func (m *DeleteGroupRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteGroupRequest.Size(m)
}
func (m *DeleteGroupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteGroupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteGroupRequest proto.InternalMessageInfo

func (m *DeleteGroupRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DeleteGroupResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteGroupResponse) Reset()         { *m = DeleteGroupResponse{} }
func (m *DeleteGroupResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteGroupResponse) ProtoMessage()    {}
func (*DeleteGroupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{89}
}
func (m *DeleteGroupResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteGroupResponse.Unmarshal(m, b)
}
func (m *DeleteGroupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteGroupResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteGroupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteGroupResponse.Merge(dst, src)
}
func (m *DeleteGroupResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteGroupResponse.Size(m)
}
func (m *DeleteGroupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteGroupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteGroupResponse proto.InternalMessageInfo

type GetGroupInfoRequest struct {
	// 根据群组id获取
	// @gotags: form:"id"
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" form:"id"`
	// 根据群模板id
	// @gotags: form:"template_id"
	TemplateId           uint32   `protobuf:"varint,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty" form:"template_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupInfoRequest) Reset()         { *m = GetGroupInfoRequest{} }
func (m *GetGroupInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupInfoRequest) ProtoMessage()    {}
func (*GetGroupInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{90}
}
func (m *GetGroupInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupInfoRequest.Unmarshal(m, b)
}
func (m *GetGroupInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetGroupInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupInfoRequest.Merge(dst, src)
}
func (m *GetGroupInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupInfoRequest.Size(m)
}
func (m *GetGroupInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupInfoRequest proto.InternalMessageInfo

func (m *GetGroupInfoRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetGroupInfoRequest) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

type GetGroupInfoResponse struct {
	Info                 *GroupInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetGroupInfoResponse) Reset()         { *m = GetGroupInfoResponse{} }
func (m *GetGroupInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupInfoResponse) ProtoMessage()    {}
func (*GetGroupInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{91}
}
func (m *GetGroupInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupInfoResponse.Unmarshal(m, b)
}
func (m *GetGroupInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetGroupInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupInfoResponse.Merge(dst, src)
}
func (m *GetGroupInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupInfoResponse.Size(m)
}
func (m *GetGroupInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupInfoResponse proto.InternalMessageInfo

func (m *GetGroupInfoResponse) GetInfo() *GroupInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetGroupTemplateInfoRequest struct {
	// @gotags: binding:"required" form:"id"
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" binding:"required" form:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupTemplateInfoRequest) Reset()         { *m = GetGroupTemplateInfoRequest{} }
func (m *GetGroupTemplateInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupTemplateInfoRequest) ProtoMessage()    {}
func (*GetGroupTemplateInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{92}
}
func (m *GetGroupTemplateInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupTemplateInfoRequest.Unmarshal(m, b)
}
func (m *GetGroupTemplateInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupTemplateInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetGroupTemplateInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupTemplateInfoRequest.Merge(dst, src)
}
func (m *GetGroupTemplateInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupTemplateInfoRequest.Size(m)
}
func (m *GetGroupTemplateInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupTemplateInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupTemplateInfoRequest proto.InternalMessageInfo

func (m *GetGroupTemplateInfoRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetGroupTemplateInfoResponse struct {
	Info                 *GroupTemplateInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetGroupTemplateInfoResponse) Reset()         { *m = GetGroupTemplateInfoResponse{} }
func (m *GetGroupTemplateInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupTemplateInfoResponse) ProtoMessage()    {}
func (*GetGroupTemplateInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{93}
}
func (m *GetGroupTemplateInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupTemplateInfoResponse.Unmarshal(m, b)
}
func (m *GetGroupTemplateInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupTemplateInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetGroupTemplateInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupTemplateInfoResponse.Merge(dst, src)
}
func (m *GetGroupTemplateInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupTemplateInfoResponse.Size(m)
}
func (m *GetGroupTemplateInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupTemplateInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupTemplateInfoResponse proto.InternalMessageInfo

func (m *GetGroupTemplateInfoResponse) GetInfo() *GroupTemplateInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetGroupListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupListRequest) Reset()         { *m = GetGroupListRequest{} }
func (m *GetGroupListRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupListRequest) ProtoMessage()    {}
func (*GetGroupListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{94}
}
func (m *GetGroupListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupListRequest.Unmarshal(m, b)
}
func (m *GetGroupListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupListRequest.Marshal(b, m, deterministic)
}
func (dst *GetGroupListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupListRequest.Merge(dst, src)
}
func (m *GetGroupListRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupListRequest.Size(m)
}
func (m *GetGroupListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupListRequest proto.InternalMessageInfo

type GetGroupListResponse struct {
	List                 []*GetGroupListResponse_Group `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetGroupListResponse) Reset()         { *m = GetGroupListResponse{} }
func (m *GetGroupListResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupListResponse) ProtoMessage()    {}
func (*GetGroupListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{95}
}
func (m *GetGroupListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupListResponse.Unmarshal(m, b)
}
func (m *GetGroupListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupListResponse.Marshal(b, m, deterministic)
}
func (dst *GetGroupListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupListResponse.Merge(dst, src)
}
func (m *GetGroupListResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupListResponse.Size(m)
}
func (m *GetGroupListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupListResponse proto.InternalMessageInfo

func (m *GetGroupListResponse) GetList() []*GetGroupListResponse_Group {
	if m != nil {
		return m.List
	}
	return nil
}

type GetGroupListResponse_Group struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Sex                  int32    `protobuf:"varint,6,opt,name=sex,proto3" json:"sex,omitempty"`
	Name                 string   `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
	Avatar               string   `protobuf:"bytes,8,opt,name=avatar,proto3" json:"avatar,omitempty"`
	GroupType            uint32   `protobuf:"varint,9,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupListResponse_Group) Reset()         { *m = GetGroupListResponse_Group{} }
func (m *GetGroupListResponse_Group) String() string { return proto.CompactTextString(m) }
func (*GetGroupListResponse_Group) ProtoMessage()    {}
func (*GetGroupListResponse_Group) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{95, 0}
}
func (m *GetGroupListResponse_Group) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupListResponse_Group.Unmarshal(m, b)
}
func (m *GetGroupListResponse_Group) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupListResponse_Group.Marshal(b, m, deterministic)
}
func (dst *GetGroupListResponse_Group) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupListResponse_Group.Merge(dst, src)
}
func (m *GetGroupListResponse_Group) XXX_Size() int {
	return xxx_messageInfo_GetGroupListResponse_Group.Size(m)
}
func (m *GetGroupListResponse_Group) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupListResponse_Group.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupListResponse_Group proto.InternalMessageInfo

func (m *GetGroupListResponse_Group) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetGroupListResponse_Group) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GetGroupListResponse_Group) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetGroupListResponse_Group) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *GetGroupListResponse_Group) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

type GetGroupMemberListRequest struct {
	// @gotags: binding:"required" form:"group_id"
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty" binding:"required" form:"group_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupMemberListRequest) Reset()         { *m = GetGroupMemberListRequest{} }
func (m *GetGroupMemberListRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupMemberListRequest) ProtoMessage()    {}
func (*GetGroupMemberListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{96}
}
func (m *GetGroupMemberListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupMemberListRequest.Unmarshal(m, b)
}
func (m *GetGroupMemberListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupMemberListRequest.Marshal(b, m, deterministic)
}
func (dst *GetGroupMemberListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupMemberListRequest.Merge(dst, src)
}
func (m *GetGroupMemberListRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupMemberListRequest.Size(m)
}
func (m *GetGroupMemberListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupMemberListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupMemberListRequest proto.InternalMessageInfo

func (m *GetGroupMemberListRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetGroupMemberListResponse struct {
	List                 []*GroupMember    `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	DefaultPrologueList  []*AIRolePrologue `protobuf:"bytes,2,rep,name=default_prologue_list,json=defaultPrologueList,proto3" json:"default_prologue_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetGroupMemberListResponse) Reset()         { *m = GetGroupMemberListResponse{} }
func (m *GetGroupMemberListResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupMemberListResponse) ProtoMessage()    {}
func (*GetGroupMemberListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{97}
}
func (m *GetGroupMemberListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupMemberListResponse.Unmarshal(m, b)
}
func (m *GetGroupMemberListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupMemberListResponse.Marshal(b, m, deterministic)
}
func (dst *GetGroupMemberListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupMemberListResponse.Merge(dst, src)
}
func (m *GetGroupMemberListResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupMemberListResponse.Size(m)
}
func (m *GetGroupMemberListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupMemberListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupMemberListResponse proto.InternalMessageInfo

func (m *GetGroupMemberListResponse) GetList() []*GroupMember {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetGroupMemberListResponse) GetDefaultPrologueList() []*AIRolePrologue {
	if m != nil {
		return m.DefaultPrologueList
	}
	return nil
}

type AttitudeRequest struct {
	Action               uint32   `protobuf:"varint,1,opt,name=action,proto3" json:"action,omitempty"`
	ObjectId             uint32   `protobuf:"varint,2,opt,name=object_id,json=objectId,proto3" json:"object_id,omitempty"`
	ObjectType           uint32   `protobuf:"varint,3,opt,name=object_type,json=objectType,proto3" json:"object_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AttitudeRequest) Reset()         { *m = AttitudeRequest{} }
func (m *AttitudeRequest) String() string { return proto.CompactTextString(m) }
func (*AttitudeRequest) ProtoMessage()    {}
func (*AttitudeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{98}
}
func (m *AttitudeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttitudeRequest.Unmarshal(m, b)
}
func (m *AttitudeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttitudeRequest.Marshal(b, m, deterministic)
}
func (dst *AttitudeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttitudeRequest.Merge(dst, src)
}
func (m *AttitudeRequest) XXX_Size() int {
	return xxx_messageInfo_AttitudeRequest.Size(m)
}
func (m *AttitudeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AttitudeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AttitudeRequest proto.InternalMessageInfo

func (m *AttitudeRequest) GetAction() uint32 {
	if m != nil {
		return m.Action
	}
	return 0
}

func (m *AttitudeRequest) GetObjectId() uint32 {
	if m != nil {
		return m.ObjectId
	}
	return 0
}

func (m *AttitudeRequest) GetObjectType() uint32 {
	if m != nil {
		return m.ObjectType
	}
	return 0
}

type AttitudeResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AttitudeResponse) Reset()         { *m = AttitudeResponse{} }
func (m *AttitudeResponse) String() string { return proto.CompactTextString(m) }
func (*AttitudeResponse) ProtoMessage()    {}
func (*AttitudeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{99}
}
func (m *AttitudeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttitudeResponse.Unmarshal(m, b)
}
func (m *AttitudeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttitudeResponse.Marshal(b, m, deterministic)
}
func (dst *AttitudeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttitudeResponse.Merge(dst, src)
}
func (m *AttitudeResponse) XXX_Size() int {
	return xxx_messageInfo_AttitudeResponse.Size(m)
}
func (m *AttitudeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AttitudeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AttitudeResponse proto.InternalMessageInfo

type HadAttitudeRequest struct {
	ObjectId             uint32   `protobuf:"varint,1,opt,name=object_id,json=objectId,proto3" json:"object_id,omitempty"`
	ObjectType           uint32   `protobuf:"varint,2,opt,name=object_type,json=objectType,proto3" json:"object_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HadAttitudeRequest) Reset()         { *m = HadAttitudeRequest{} }
func (m *HadAttitudeRequest) String() string { return proto.CompactTextString(m) }
func (*HadAttitudeRequest) ProtoMessage()    {}
func (*HadAttitudeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{100}
}
func (m *HadAttitudeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HadAttitudeRequest.Unmarshal(m, b)
}
func (m *HadAttitudeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HadAttitudeRequest.Marshal(b, m, deterministic)
}
func (dst *HadAttitudeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HadAttitudeRequest.Merge(dst, src)
}
func (m *HadAttitudeRequest) XXX_Size() int {
	return xxx_messageInfo_HadAttitudeRequest.Size(m)
}
func (m *HadAttitudeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HadAttitudeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HadAttitudeRequest proto.InternalMessageInfo

func (m *HadAttitudeRequest) GetObjectId() uint32 {
	if m != nil {
		return m.ObjectId
	}
	return 0
}

func (m *HadAttitudeRequest) GetObjectType() uint32 {
	if m != nil {
		return m.ObjectType
	}
	return 0
}

type HadAttitudeResponse struct {
	HadAttitude          bool     `protobuf:"varint,1,opt,name=had_attitude,json=hadAttitude,proto3" json:"had_attitude,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HadAttitudeResponse) Reset()         { *m = HadAttitudeResponse{} }
func (m *HadAttitudeResponse) String() string { return proto.CompactTextString(m) }
func (*HadAttitudeResponse) ProtoMessage()    {}
func (*HadAttitudeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{101}
}
func (m *HadAttitudeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HadAttitudeResponse.Unmarshal(m, b)
}
func (m *HadAttitudeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HadAttitudeResponse.Marshal(b, m, deterministic)
}
func (dst *HadAttitudeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HadAttitudeResponse.Merge(dst, src)
}
func (m *HadAttitudeResponse) XXX_Size() int {
	return xxx_messageInfo_HadAttitudeResponse.Size(m)
}
func (m *HadAttitudeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HadAttitudeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HadAttitudeResponse proto.InternalMessageInfo

func (m *HadAttitudeResponse) GetHadAttitude() bool {
	if m != nil {
		return m.HadAttitude
	}
	return false
}

type GetReadHeartCountRequest struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReadHeartCountRequest) Reset()         { *m = GetReadHeartCountRequest{} }
func (m *GetReadHeartCountRequest) String() string { return proto.CompactTextString(m) }
func (*GetReadHeartCountRequest) ProtoMessage()    {}
func (*GetReadHeartCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{102}
}
func (m *GetReadHeartCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReadHeartCountRequest.Unmarshal(m, b)
}
func (m *GetReadHeartCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReadHeartCountRequest.Marshal(b, m, deterministic)
}
func (dst *GetReadHeartCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReadHeartCountRequest.Merge(dst, src)
}
func (m *GetReadHeartCountRequest) XXX_Size() int {
	return xxx_messageInfo_GetReadHeartCountRequest.Size(m)
}
func (m *GetReadHeartCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReadHeartCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetReadHeartCountRequest proto.InternalMessageInfo

func (m *GetReadHeartCountRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetReadHeartCountRequest) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

type GetReadHeartCountResponse struct {
	ReadHeartCount       uint32   `protobuf:"varint,1,opt,name=read_heart_count,json=readHeartCount,proto3" json:"read_heart_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReadHeartCountResponse) Reset()         { *m = GetReadHeartCountResponse{} }
func (m *GetReadHeartCountResponse) String() string { return proto.CompactTextString(m) }
func (*GetReadHeartCountResponse) ProtoMessage()    {}
func (*GetReadHeartCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{103}
}
func (m *GetReadHeartCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReadHeartCountResponse.Unmarshal(m, b)
}
func (m *GetReadHeartCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReadHeartCountResponse.Marshal(b, m, deterministic)
}
func (dst *GetReadHeartCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReadHeartCountResponse.Merge(dst, src)
}
func (m *GetReadHeartCountResponse) XXX_Size() int {
	return xxx_messageInfo_GetReadHeartCountResponse.Size(m)
}
func (m *GetReadHeartCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReadHeartCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetReadHeartCountResponse proto.InternalMessageInfo

func (m *GetReadHeartCountResponse) GetReadHeartCount() uint32 {
	if m != nil {
		return m.ReadHeartCount
	}
	return 0
}

type ReadHeartRequest struct {
	MsgId                string   `protobuf:"bytes,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	PartnerId            uint32   `protobuf:"varint,3,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	Content              string   `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadHeartRequest) Reset()         { *m = ReadHeartRequest{} }
func (m *ReadHeartRequest) String() string { return proto.CompactTextString(m) }
func (*ReadHeartRequest) ProtoMessage()    {}
func (*ReadHeartRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{104}
}
func (m *ReadHeartRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadHeartRequest.Unmarshal(m, b)
}
func (m *ReadHeartRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadHeartRequest.Marshal(b, m, deterministic)
}
func (dst *ReadHeartRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadHeartRequest.Merge(dst, src)
}
func (m *ReadHeartRequest) XXX_Size() int {
	return xxx_messageInfo_ReadHeartRequest.Size(m)
}
func (m *ReadHeartRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadHeartRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReadHeartRequest proto.InternalMessageInfo

func (m *ReadHeartRequest) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *ReadHeartRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *ReadHeartRequest) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *ReadHeartRequest) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type ReadHeartResponse struct {
	ReadHeartCount       uint32   `protobuf:"varint,1,opt,name=read_heart_count,json=readHeartCount,proto3" json:"read_heart_count,omitempty"`
	ReadHeartText        string   `protobuf:"bytes,2,opt,name=read_heart_text,json=readHeartText,proto3" json:"read_heart_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadHeartResponse) Reset()         { *m = ReadHeartResponse{} }
func (m *ReadHeartResponse) String() string { return proto.CompactTextString(m) }
func (*ReadHeartResponse) ProtoMessage()    {}
func (*ReadHeartResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{105}
}
func (m *ReadHeartResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadHeartResponse.Unmarshal(m, b)
}
func (m *ReadHeartResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadHeartResponse.Marshal(b, m, deterministic)
}
func (dst *ReadHeartResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadHeartResponse.Merge(dst, src)
}
func (m *ReadHeartResponse) XXX_Size() int {
	return xxx_messageInfo_ReadHeartResponse.Size(m)
}
func (m *ReadHeartResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadHeartResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReadHeartResponse proto.InternalMessageInfo

func (m *ReadHeartResponse) GetReadHeartCount() uint32 {
	if m != nil {
		return m.ReadHeartCount
	}
	return 0
}

func (m *ReadHeartResponse) GetReadHeartText() string {
	if m != nil {
		return m.ReadHeartText
	}
	return ""
}

type BatchGetMsgRequest struct {
	MsgIds               []string `protobuf:"bytes,1,rep,name=msg_ids,json=msgIds,proto3" json:"msg_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetMsgRequest) Reset()         { *m = BatchGetMsgRequest{} }
func (m *BatchGetMsgRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetMsgRequest) ProtoMessage()    {}
func (*BatchGetMsgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{106}
}
func (m *BatchGetMsgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetMsgRequest.Unmarshal(m, b)
}
func (m *BatchGetMsgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetMsgRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetMsgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetMsgRequest.Merge(dst, src)
}
func (m *BatchGetMsgRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetMsgRequest.Size(m)
}
func (m *BatchGetMsgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetMsgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetMsgRequest proto.InternalMessageInfo

func (m *BatchGetMsgRequest) GetMsgIds() []string {
	if m != nil {
		return m.MsgIds
	}
	return nil
}

type ReadHeartInfo struct {
	MsgId                string   `protobuf:"bytes,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	IsEntrance           bool     `protobuf:"varint,2,opt,name=is_entrance,json=isEntrance,proto3" json:"is_entrance,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadHeartInfo) Reset()         { *m = ReadHeartInfo{} }
func (m *ReadHeartInfo) String() string { return proto.CompactTextString(m) }
func (*ReadHeartInfo) ProtoMessage()    {}
func (*ReadHeartInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{107}
}
func (m *ReadHeartInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadHeartInfo.Unmarshal(m, b)
}
func (m *ReadHeartInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadHeartInfo.Marshal(b, m, deterministic)
}
func (dst *ReadHeartInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadHeartInfo.Merge(dst, src)
}
func (m *ReadHeartInfo) XXX_Size() int {
	return xxx_messageInfo_ReadHeartInfo.Size(m)
}
func (m *ReadHeartInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadHeartInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReadHeartInfo proto.InternalMessageInfo

func (m *ReadHeartInfo) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *ReadHeartInfo) GetIsEntrance() bool {
	if m != nil {
		return m.IsEntrance
	}
	return false
}

func (m *ReadHeartInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type BatchGetMsgResponse struct {
	ReadHeartInfos       []*ReadHeartInfo `protobuf:"bytes,1,rep,name=read_heart_infos,json=readHeartInfos,proto3" json:"read_heart_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchGetMsgResponse) Reset()         { *m = BatchGetMsgResponse{} }
func (m *BatchGetMsgResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetMsgResponse) ProtoMessage()    {}
func (*BatchGetMsgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{108}
}
func (m *BatchGetMsgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetMsgResponse.Unmarshal(m, b)
}
func (m *BatchGetMsgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetMsgResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetMsgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetMsgResponse.Merge(dst, src)
}
func (m *BatchGetMsgResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetMsgResponse.Size(m)
}
func (m *BatchGetMsgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetMsgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetMsgResponse proto.InternalMessageInfo

func (m *BatchGetMsgResponse) GetReadHeartInfos() []*ReadHeartInfo {
	if m != nil {
		return m.ReadHeartInfos
	}
	return nil
}

type GetExtraInfoRequest struct {
	Entity               *Entity  `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExtraInfoRequest) Reset()         { *m = GetExtraInfoRequest{} }
func (m *GetExtraInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetExtraInfoRequest) ProtoMessage()    {}
func (*GetExtraInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{109}
}
func (m *GetExtraInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraInfoRequest.Unmarshal(m, b)
}
func (m *GetExtraInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetExtraInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraInfoRequest.Merge(dst, src)
}
func (m *GetExtraInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetExtraInfoRequest.Size(m)
}
func (m *GetExtraInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraInfoRequest proto.InternalMessageInfo

func (m *GetExtraInfoRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

type ChatBackground struct {
	Id                   string               `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	BackgroundUrl        string               `protobuf:"bytes,2,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	ShowHeadImg          bool                 `protobuf:"varint,3,opt,name=show_head_img,json=showHeadImg,proto3" json:"show_head_img,omitempty"`
	Name                 string               `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	State                ChatBackground_State `protobuf:"varint,5,opt,name=state,proto3,enum=aigc_http_logic.ChatBackground_State" json:"state,omitempty"`
	UnlockCondition      string               `protobuf:"bytes,6,opt,name=unlock_condition,json=unlockCondition,proto3" json:"unlock_condition,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ChatBackground) Reset()         { *m = ChatBackground{} }
func (m *ChatBackground) String() string { return proto.CompactTextString(m) }
func (*ChatBackground) ProtoMessage()    {}
func (*ChatBackground) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{110}
}
func (m *ChatBackground) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatBackground.Unmarshal(m, b)
}
func (m *ChatBackground) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatBackground.Marshal(b, m, deterministic)
}
func (dst *ChatBackground) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatBackground.Merge(dst, src)
}
func (m *ChatBackground) XXX_Size() int {
	return xxx_messageInfo_ChatBackground.Size(m)
}
func (m *ChatBackground) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatBackground.DiscardUnknown(m)
}

var xxx_messageInfo_ChatBackground proto.InternalMessageInfo

func (m *ChatBackground) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ChatBackground) GetBackgroundUrl() string {
	if m != nil {
		return m.BackgroundUrl
	}
	return ""
}

func (m *ChatBackground) GetShowHeadImg() bool {
	if m != nil {
		return m.ShowHeadImg
	}
	return false
}

func (m *ChatBackground) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChatBackground) GetState() ChatBackground_State {
	if m != nil {
		return m.State
	}
	return ChatBackground_STATE_UNSPECIFIED
}

func (m *ChatBackground) GetUnlockCondition() string {
	if m != nil {
		return m.UnlockCondition
	}
	return ""
}

type GetExtraInfoResponse struct {
	CurBackground        *ChatBackground `protobuf:"bytes,1,opt,name=cur_background,json=curBackground,proto3" json:"cur_background,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetExtraInfoResponse) Reset()         { *m = GetExtraInfoResponse{} }
func (m *GetExtraInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetExtraInfoResponse) ProtoMessage()    {}
func (*GetExtraInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{111}
}
func (m *GetExtraInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraInfoResponse.Unmarshal(m, b)
}
func (m *GetExtraInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetExtraInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraInfoResponse.Merge(dst, src)
}
func (m *GetExtraInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetExtraInfoResponse.Size(m)
}
func (m *GetExtraInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraInfoResponse proto.InternalMessageInfo

func (m *GetExtraInfoResponse) GetCurBackground() *ChatBackground {
	if m != nil {
		return m.CurBackground
	}
	return nil
}

type GetChatBackgroundListRequest struct {
	Entity               *Entity  `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChatBackgroundListRequest) Reset()         { *m = GetChatBackgroundListRequest{} }
func (m *GetChatBackgroundListRequest) String() string { return proto.CompactTextString(m) }
func (*GetChatBackgroundListRequest) ProtoMessage()    {}
func (*GetChatBackgroundListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{112}
}
func (m *GetChatBackgroundListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChatBackgroundListRequest.Unmarshal(m, b)
}
func (m *GetChatBackgroundListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChatBackgroundListRequest.Marshal(b, m, deterministic)
}
func (dst *GetChatBackgroundListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChatBackgroundListRequest.Merge(dst, src)
}
func (m *GetChatBackgroundListRequest) XXX_Size() int {
	return xxx_messageInfo_GetChatBackgroundListRequest.Size(m)
}
func (m *GetChatBackgroundListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChatBackgroundListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChatBackgroundListRequest proto.InternalMessageInfo

func (m *GetChatBackgroundListRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

type GetChatBackgroundListResponse struct {
	List                 []*ChatBackground `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetChatBackgroundListResponse) Reset()         { *m = GetChatBackgroundListResponse{} }
func (m *GetChatBackgroundListResponse) String() string { return proto.CompactTextString(m) }
func (*GetChatBackgroundListResponse) ProtoMessage()    {}
func (*GetChatBackgroundListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{113}
}
func (m *GetChatBackgroundListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChatBackgroundListResponse.Unmarshal(m, b)
}
func (m *GetChatBackgroundListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChatBackgroundListResponse.Marshal(b, m, deterministic)
}
func (dst *GetChatBackgroundListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChatBackgroundListResponse.Merge(dst, src)
}
func (m *GetChatBackgroundListResponse) XXX_Size() int {
	return xxx_messageInfo_GetChatBackgroundListResponse.Size(m)
}
func (m *GetChatBackgroundListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChatBackgroundListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChatBackgroundListResponse proto.InternalMessageInfo

func (m *GetChatBackgroundListResponse) GetList() []*ChatBackground {
	if m != nil {
		return m.List
	}
	return nil
}

type SwitchChatBackgroundRequest struct {
	Entity               *Entity  `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	BackgroundId         string   `protobuf:"bytes,2,opt,name=background_id,json=backgroundId,proto3" json:"background_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchChatBackgroundRequest) Reset()         { *m = SwitchChatBackgroundRequest{} }
func (m *SwitchChatBackgroundRequest) String() string { return proto.CompactTextString(m) }
func (*SwitchChatBackgroundRequest) ProtoMessage()    {}
func (*SwitchChatBackgroundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{114}
}
func (m *SwitchChatBackgroundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChatBackgroundRequest.Unmarshal(m, b)
}
func (m *SwitchChatBackgroundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChatBackgroundRequest.Marshal(b, m, deterministic)
}
func (dst *SwitchChatBackgroundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChatBackgroundRequest.Merge(dst, src)
}
func (m *SwitchChatBackgroundRequest) XXX_Size() int {
	return xxx_messageInfo_SwitchChatBackgroundRequest.Size(m)
}
func (m *SwitchChatBackgroundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChatBackgroundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChatBackgroundRequest proto.InternalMessageInfo

func (m *SwitchChatBackgroundRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *SwitchChatBackgroundRequest) GetBackgroundId() string {
	if m != nil {
		return m.BackgroundId
	}
	return ""
}

type SwitchChatBackgroundResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchChatBackgroundResponse) Reset()         { *m = SwitchChatBackgroundResponse{} }
func (m *SwitchChatBackgroundResponse) String() string { return proto.CompactTextString(m) }
func (*SwitchChatBackgroundResponse) ProtoMessage()    {}
func (*SwitchChatBackgroundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{115}
}
func (m *SwitchChatBackgroundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChatBackgroundResponse.Unmarshal(m, b)
}
func (m *SwitchChatBackgroundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChatBackgroundResponse.Marshal(b, m, deterministic)
}
func (dst *SwitchChatBackgroundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChatBackgroundResponse.Merge(dst, src)
}
func (m *SwitchChatBackgroundResponse) XXX_Size() int {
	return xxx_messageInfo_SwitchChatBackgroundResponse.Size(m)
}
func (m *SwitchChatBackgroundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChatBackgroundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChatBackgroundResponse proto.InternalMessageInfo

type Relation struct {
	Id        string         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name      string         `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	UnlockTip string         `protobuf:"bytes,3,opt,name=unlock_tip,json=unlockTip,proto3" json:"unlock_tip,omitempty"`
	State     Relation_State `protobuf:"varint,4,opt,name=state,proto3,enum=aigc_http_logic.Relation_State" json:"state,omitempty"`
	// 外显icon
	Icon string `protobuf:"bytes,5,opt,name=icon,proto3" json:"icon,omitempty"`
	// 亲密度页面展示背景图
	IntimacyBackgroundImg string   `protobuf:"bytes,6,opt,name=intimacy_background_img,json=intimacyBackgroundImg,proto3" json:"intimacy_background_img,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *Relation) Reset()         { *m = Relation{} }
func (m *Relation) String() string { return proto.CompactTextString(m) }
func (*Relation) ProtoMessage()    {}
func (*Relation) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{116}
}
func (m *Relation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Relation.Unmarshal(m, b)
}
func (m *Relation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Relation.Marshal(b, m, deterministic)
}
func (dst *Relation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Relation.Merge(dst, src)
}
func (m *Relation) XXX_Size() int {
	return xxx_messageInfo_Relation.Size(m)
}
func (m *Relation) XXX_DiscardUnknown() {
	xxx_messageInfo_Relation.DiscardUnknown(m)
}

var xxx_messageInfo_Relation proto.InternalMessageInfo

func (m *Relation) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *Relation) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Relation) GetUnlockTip() string {
	if m != nil {
		return m.UnlockTip
	}
	return ""
}

func (m *Relation) GetState() Relation_State {
	if m != nil {
		return m.State
	}
	return Relation_STATE_UNSPECIFIED
}

func (m *Relation) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *Relation) GetIntimacyBackgroundImg() string {
	if m != nil {
		return m.IntimacyBackgroundImg
	}
	return ""
}

type GetRelationListRequest struct {
	Entity               *Entity  `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRelationListRequest) Reset()         { *m = GetRelationListRequest{} }
func (m *GetRelationListRequest) String() string { return proto.CompactTextString(m) }
func (*GetRelationListRequest) ProtoMessage()    {}
func (*GetRelationListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{117}
}
func (m *GetRelationListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRelationListRequest.Unmarshal(m, b)
}
func (m *GetRelationListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRelationListRequest.Marshal(b, m, deterministic)
}
func (dst *GetRelationListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRelationListRequest.Merge(dst, src)
}
func (m *GetRelationListRequest) XXX_Size() int {
	return xxx_messageInfo_GetRelationListRequest.Size(m)
}
func (m *GetRelationListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRelationListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRelationListRequest proto.InternalMessageInfo

func (m *GetRelationListRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

type GetRelationListResponse struct {
	List                 []*Relation `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetRelationListResponse) Reset()         { *m = GetRelationListResponse{} }
func (m *GetRelationListResponse) String() string { return proto.CompactTextString(m) }
func (*GetRelationListResponse) ProtoMessage()    {}
func (*GetRelationListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{118}
}
func (m *GetRelationListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRelationListResponse.Unmarshal(m, b)
}
func (m *GetRelationListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRelationListResponse.Marshal(b, m, deterministic)
}
func (dst *GetRelationListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRelationListResponse.Merge(dst, src)
}
func (m *GetRelationListResponse) XXX_Size() int {
	return xxx_messageInfo_GetRelationListResponse.Size(m)
}
func (m *GetRelationListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRelationListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRelationListResponse proto.InternalMessageInfo

func (m *GetRelationListResponse) GetList() []*Relation {
	if m != nil {
		return m.List
	}
	return nil
}

type SwitchRelationRequest struct {
	Entity               *Entity  `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	RelationId           string   `protobuf:"bytes,2,opt,name=relation_id,json=relationId,proto3" json:"relation_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchRelationRequest) Reset()         { *m = SwitchRelationRequest{} }
func (m *SwitchRelationRequest) String() string { return proto.CompactTextString(m) }
func (*SwitchRelationRequest) ProtoMessage()    {}
func (*SwitchRelationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{119}
}
func (m *SwitchRelationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchRelationRequest.Unmarshal(m, b)
}
func (m *SwitchRelationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchRelationRequest.Marshal(b, m, deterministic)
}
func (dst *SwitchRelationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchRelationRequest.Merge(dst, src)
}
func (m *SwitchRelationRequest) XXX_Size() int {
	return xxx_messageInfo_SwitchRelationRequest.Size(m)
}
func (m *SwitchRelationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchRelationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchRelationRequest proto.InternalMessageInfo

func (m *SwitchRelationRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *SwitchRelationRequest) GetRelationId() string {
	if m != nil {
		return m.RelationId
	}
	return ""
}

type SwitchRelationResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchRelationResponse) Reset()         { *m = SwitchRelationResponse{} }
func (m *SwitchRelationResponse) String() string { return proto.CompactTextString(m) }
func (*SwitchRelationResponse) ProtoMessage()    {}
func (*SwitchRelationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{120}
}
func (m *SwitchRelationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchRelationResponse.Unmarshal(m, b)
}
func (m *SwitchRelationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchRelationResponse.Marshal(b, m, deterministic)
}
func (dst *SwitchRelationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchRelationResponse.Merge(dst, src)
}
func (m *SwitchRelationResponse) XXX_Size() int {
	return xxx_messageInfo_SwitchRelationResponse.Size(m)
}
func (m *SwitchRelationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchRelationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchRelationResponse proto.InternalMessageInfo

type GetIntimacyInfoRequest struct {
	Entity               *Entity  `protobuf:"bytes,1,opt,name=entity,proto3" json:"entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetIntimacyInfoRequest) Reset()         { *m = GetIntimacyInfoRequest{} }
func (m *GetIntimacyInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetIntimacyInfoRequest) ProtoMessage()    {}
func (*GetIntimacyInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{121}
}
func (m *GetIntimacyInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIntimacyInfoRequest.Unmarshal(m, b)
}
func (m *GetIntimacyInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIntimacyInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetIntimacyInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIntimacyInfoRequest.Merge(dst, src)
}
func (m *GetIntimacyInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetIntimacyInfoRequest.Size(m)
}
func (m *GetIntimacyInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIntimacyInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetIntimacyInfoRequest proto.InternalMessageInfo

func (m *GetIntimacyInfoRequest) GetEntity() *Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

type GetIntimacyInfoResponse struct {
	// 当前等级
	CurLevel uint32 `protobuf:"varint,1,opt,name=cur_level,json=curLevel,proto3" json:"cur_level,omitempty"`
	// 当前亲密值
	CurValue uint32 `protobuf:"varint,2,opt,name=cur_value,json=curValue,proto3" json:"cur_value,omitempty"`
	// 相遇的第x天
	MeetDay uint32 `protobuf:"varint,6,opt,name=meet_day,json=meetDay,proto3" json:"meet_day,omitempty"`
	// 今日增长的亲密值
	TodayGrowthValue uint32 `protobuf:"varint,7,opt,name=today_growth_value,json=todayGrowthValue,proto3" json:"today_growth_value,omitempty"`
	// 关系
	Relation *GetIntimacyInfoResponse_Relation `protobuf:"bytes,11,opt,name=relation,proto3" json:"relation,omitempty"`
	// 等级配置
	Levels []*LevelConfig `protobuf:"bytes,16,rep,name=levels,proto3" json:"levels,omitempty"`
	// 升级条件
	LevelUpConditions    []*LevelUpCondition `protobuf:"bytes,17,rep,name=level_up_conditions,json=levelUpConditions,proto3" json:"level_up_conditions,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetIntimacyInfoResponse) Reset()         { *m = GetIntimacyInfoResponse{} }
func (m *GetIntimacyInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetIntimacyInfoResponse) ProtoMessage()    {}
func (*GetIntimacyInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{122}
}
func (m *GetIntimacyInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIntimacyInfoResponse.Unmarshal(m, b)
}
func (m *GetIntimacyInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIntimacyInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetIntimacyInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIntimacyInfoResponse.Merge(dst, src)
}
func (m *GetIntimacyInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetIntimacyInfoResponse.Size(m)
}
func (m *GetIntimacyInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIntimacyInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetIntimacyInfoResponse proto.InternalMessageInfo

func (m *GetIntimacyInfoResponse) GetCurLevel() uint32 {
	if m != nil {
		return m.CurLevel
	}
	return 0
}

func (m *GetIntimacyInfoResponse) GetCurValue() uint32 {
	if m != nil {
		return m.CurValue
	}
	return 0
}

func (m *GetIntimacyInfoResponse) GetMeetDay() uint32 {
	if m != nil {
		return m.MeetDay
	}
	return 0
}

func (m *GetIntimacyInfoResponse) GetTodayGrowthValue() uint32 {
	if m != nil {
		return m.TodayGrowthValue
	}
	return 0
}

func (m *GetIntimacyInfoResponse) GetRelation() *GetIntimacyInfoResponse_Relation {
	if m != nil {
		return m.Relation
	}
	return nil
}

func (m *GetIntimacyInfoResponse) GetLevels() []*LevelConfig {
	if m != nil {
		return m.Levels
	}
	return nil
}

func (m *GetIntimacyInfoResponse) GetLevelUpConditions() []*LevelUpCondition {
	if m != nil {
		return m.LevelUpConditions
	}
	return nil
}

type GetIntimacyInfoResponse_Relation struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Background           string   `protobuf:"bytes,3,opt,name=background,proto3" json:"background,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetIntimacyInfoResponse_Relation) Reset()         { *m = GetIntimacyInfoResponse_Relation{} }
func (m *GetIntimacyInfoResponse_Relation) String() string { return proto.CompactTextString(m) }
func (*GetIntimacyInfoResponse_Relation) ProtoMessage()    {}
func (*GetIntimacyInfoResponse_Relation) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{122, 0}
}
func (m *GetIntimacyInfoResponse_Relation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIntimacyInfoResponse_Relation.Unmarshal(m, b)
}
func (m *GetIntimacyInfoResponse_Relation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIntimacyInfoResponse_Relation.Marshal(b, m, deterministic)
}
func (dst *GetIntimacyInfoResponse_Relation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIntimacyInfoResponse_Relation.Merge(dst, src)
}
func (m *GetIntimacyInfoResponse_Relation) XXX_Size() int {
	return xxx_messageInfo_GetIntimacyInfoResponse_Relation.Size(m)
}
func (m *GetIntimacyInfoResponse_Relation) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIntimacyInfoResponse_Relation.DiscardUnknown(m)
}

var xxx_messageInfo_GetIntimacyInfoResponse_Relation proto.InternalMessageInfo

func (m *GetIntimacyInfoResponse_Relation) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetIntimacyInfoResponse_Relation) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetIntimacyInfoResponse_Relation) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

type GetScriptFilterItemRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScriptFilterItemRequest) Reset()         { *m = GetScriptFilterItemRequest{} }
func (m *GetScriptFilterItemRequest) String() string { return proto.CompactTextString(m) }
func (*GetScriptFilterItemRequest) ProtoMessage()    {}
func (*GetScriptFilterItemRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{123}
}
func (m *GetScriptFilterItemRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScriptFilterItemRequest.Unmarshal(m, b)
}
func (m *GetScriptFilterItemRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScriptFilterItemRequest.Marshal(b, m, deterministic)
}
func (dst *GetScriptFilterItemRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScriptFilterItemRequest.Merge(dst, src)
}
func (m *GetScriptFilterItemRequest) XXX_Size() int {
	return xxx_messageInfo_GetScriptFilterItemRequest.Size(m)
}
func (m *GetScriptFilterItemRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScriptFilterItemRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetScriptFilterItemRequest proto.InternalMessageInfo

type FilterItem struct {
	Type                 FilterItem_FilterType `protobuf:"varint,1,opt,name=type,proto3,enum=aigc_http_logic.FilterItem_FilterType" json:"type,omitempty"`
	DisplayName          string                `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *FilterItem) Reset()         { *m = FilterItem{} }
func (m *FilterItem) String() string { return proto.CompactTextString(m) }
func (*FilterItem) ProtoMessage()    {}
func (*FilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{124}
}
func (m *FilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterItem.Unmarshal(m, b)
}
func (m *FilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterItem.Marshal(b, m, deterministic)
}
func (dst *FilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterItem.Merge(dst, src)
}
func (m *FilterItem) XXX_Size() int {
	return xxx_messageInfo_FilterItem.Size(m)
}
func (m *FilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_FilterItem proto.InternalMessageInfo

func (m *FilterItem) GetType() FilterItem_FilterType {
	if m != nil {
		return m.Type
	}
	return FilterItem_FILTER_TYPE_UNSPECIFIED
}

func (m *FilterItem) GetDisplayName() string {
	if m != nil {
		return m.DisplayName
	}
	return ""
}

type GetScriptFilterItemResponse struct {
	List                 []*FilterItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetScriptFilterItemResponse) Reset()         { *m = GetScriptFilterItemResponse{} }
func (m *GetScriptFilterItemResponse) String() string { return proto.CompactTextString(m) }
func (*GetScriptFilterItemResponse) ProtoMessage()    {}
func (*GetScriptFilterItemResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{125}
}
func (m *GetScriptFilterItemResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScriptFilterItemResponse.Unmarshal(m, b)
}
func (m *GetScriptFilterItemResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScriptFilterItemResponse.Marshal(b, m, deterministic)
}
func (dst *GetScriptFilterItemResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScriptFilterItemResponse.Merge(dst, src)
}
func (m *GetScriptFilterItemResponse) XXX_Size() int {
	return xxx_messageInfo_GetScriptFilterItemResponse.Size(m)
}
func (m *GetScriptFilterItemResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScriptFilterItemResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetScriptFilterItemResponse proto.InternalMessageInfo

func (m *GetScriptFilterItemResponse) GetList() []*FilterItem {
	if m != nil {
		return m.List
	}
	return nil
}

type GetScriptListRequest struct {
	FilterItems          []*FilterItem `protobuf:"bytes,1,rep,name=filter_items,json=filterItems,proto3" json:"filter_items,omitempty"`
	Cursor               string        `protobuf:"bytes,2,opt,name=cursor,proto3" json:"cursor,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetScriptListRequest) Reset()         { *m = GetScriptListRequest{} }
func (m *GetScriptListRequest) String() string { return proto.CompactTextString(m) }
func (*GetScriptListRequest) ProtoMessage()    {}
func (*GetScriptListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{126}
}
func (m *GetScriptListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScriptListRequest.Unmarshal(m, b)
}
func (m *GetScriptListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScriptListRequest.Marshal(b, m, deterministic)
}
func (dst *GetScriptListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScriptListRequest.Merge(dst, src)
}
func (m *GetScriptListRequest) XXX_Size() int {
	return xxx_messageInfo_GetScriptListRequest.Size(m)
}
func (m *GetScriptListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScriptListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetScriptListRequest proto.InternalMessageInfo

func (m *GetScriptListRequest) GetFilterItems() []*FilterItem {
	if m != nil {
		return m.FilterItems
	}
	return nil
}

func (m *GetScriptListRequest) GetCursor() string {
	if m != nil {
		return m.Cursor
	}
	return ""
}

type ScriptItem struct {
	Id                   uint32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Character            string                  `protobuf:"bytes,3,opt,name=character,proto3" json:"character,omitempty"`
	ButtonDisplayText    string                  `protobuf:"bytes,4,opt,name=button_display_text,json=buttonDisplayText,proto3" json:"button_display_text,omitempty"`
	PlayingInfo          *ScriptItem_PlayingInfo `protobuf:"bytes,5,opt,name=playing_info,json=playingInfo,proto3" json:"playing_info,omitempty"`
	HomeBackgroundImg    string                  `protobuf:"bytes,6,opt,name=home_background_img,json=homeBackgroundImg,proto3" json:"home_background_img,omitempty"`
	Tags                 []string                `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`
	CornerIcon           string                  `protobuf:"bytes,8,opt,name=corner_icon,json=cornerIcon,proto3" json:"corner_icon,omitempty"`
	SuitableSex          []int32                 `protobuf:"varint,9,rep,packed,name=suitable_sex,json=suitableSex,proto3" json:"suitable_sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ScriptItem) Reset()         { *m = ScriptItem{} }
func (m *ScriptItem) String() string { return proto.CompactTextString(m) }
func (*ScriptItem) ProtoMessage()    {}
func (*ScriptItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{127}
}
func (m *ScriptItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScriptItem.Unmarshal(m, b)
}
func (m *ScriptItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScriptItem.Marshal(b, m, deterministic)
}
func (dst *ScriptItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScriptItem.Merge(dst, src)
}
func (m *ScriptItem) XXX_Size() int {
	return xxx_messageInfo_ScriptItem.Size(m)
}
func (m *ScriptItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ScriptItem.DiscardUnknown(m)
}

var xxx_messageInfo_ScriptItem proto.InternalMessageInfo

func (m *ScriptItem) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ScriptItem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ScriptItem) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *ScriptItem) GetButtonDisplayText() string {
	if m != nil {
		return m.ButtonDisplayText
	}
	return ""
}

func (m *ScriptItem) GetPlayingInfo() *ScriptItem_PlayingInfo {
	if m != nil {
		return m.PlayingInfo
	}
	return nil
}

func (m *ScriptItem) GetHomeBackgroundImg() string {
	if m != nil {
		return m.HomeBackgroundImg
	}
	return ""
}

func (m *ScriptItem) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *ScriptItem) GetCornerIcon() string {
	if m != nil {
		return m.CornerIcon
	}
	return ""
}

func (m *ScriptItem) GetSuitableSex() []int32 {
	if m != nil {
		return m.SuitableSex
	}
	return nil
}

type ScriptItem_PlayingInfo struct {
	Accounts             []string `protobuf:"bytes,1,rep,name=accounts,proto3" json:"accounts,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	FallBackAvatarUrls   []string `protobuf:"bytes,3,rep,name=fall_back_avatar_urls,json=fallBackAvatarUrls,proto3" json:"fall_back_avatar_urls,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScriptItem_PlayingInfo) Reset()         { *m = ScriptItem_PlayingInfo{} }
func (m *ScriptItem_PlayingInfo) String() string { return proto.CompactTextString(m) }
func (*ScriptItem_PlayingInfo) ProtoMessage()    {}
func (*ScriptItem_PlayingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{127, 0}
}
func (m *ScriptItem_PlayingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScriptItem_PlayingInfo.Unmarshal(m, b)
}
func (m *ScriptItem_PlayingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScriptItem_PlayingInfo.Marshal(b, m, deterministic)
}
func (dst *ScriptItem_PlayingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScriptItem_PlayingInfo.Merge(dst, src)
}
func (m *ScriptItem_PlayingInfo) XXX_Size() int {
	return xxx_messageInfo_ScriptItem_PlayingInfo.Size(m)
}
func (m *ScriptItem_PlayingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ScriptItem_PlayingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ScriptItem_PlayingInfo proto.InternalMessageInfo

func (m *ScriptItem_PlayingInfo) GetAccounts() []string {
	if m != nil {
		return m.Accounts
	}
	return nil
}

func (m *ScriptItem_PlayingInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *ScriptItem_PlayingInfo) GetFallBackAvatarUrls() []string {
	if m != nil {
		return m.FallBackAvatarUrls
	}
	return nil
}

type GetScriptListResponse struct {
	List                 []*ScriptItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	NextCursor           string        `protobuf:"bytes,2,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetScriptListResponse) Reset()         { *m = GetScriptListResponse{} }
func (m *GetScriptListResponse) String() string { return proto.CompactTextString(m) }
func (*GetScriptListResponse) ProtoMessage()    {}
func (*GetScriptListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{128}
}
func (m *GetScriptListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScriptListResponse.Unmarshal(m, b)
}
func (m *GetScriptListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScriptListResponse.Marshal(b, m, deterministic)
}
func (dst *GetScriptListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScriptListResponse.Merge(dst, src)
}
func (m *GetScriptListResponse) XXX_Size() int {
	return xxx_messageInfo_GetScriptListResponse.Size(m)
}
func (m *GetScriptListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScriptListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetScriptListResponse proto.InternalMessageInfo

func (m *GetScriptListResponse) GetList() []*ScriptItem {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetScriptListResponse) GetNextCursor() string {
	if m != nil {
		return m.NextCursor
	}
	return ""
}

type GetScriptHotBannerRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScriptHotBannerRequest) Reset()         { *m = GetScriptHotBannerRequest{} }
func (m *GetScriptHotBannerRequest) String() string { return proto.CompactTextString(m) }
func (*GetScriptHotBannerRequest) ProtoMessage()    {}
func (*GetScriptHotBannerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{129}
}
func (m *GetScriptHotBannerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScriptHotBannerRequest.Unmarshal(m, b)
}
func (m *GetScriptHotBannerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScriptHotBannerRequest.Marshal(b, m, deterministic)
}
func (dst *GetScriptHotBannerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScriptHotBannerRequest.Merge(dst, src)
}
func (m *GetScriptHotBannerRequest) XXX_Size() int {
	return xxx_messageInfo_GetScriptHotBannerRequest.Size(m)
}
func (m *GetScriptHotBannerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScriptHotBannerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetScriptHotBannerRequest proto.InternalMessageInfo

type GetScriptHotBannerResponse struct {
	BannerTitle          string                                   `protobuf:"bytes,1,opt,name=banner_title,json=bannerTitle,proto3" json:"banner_title,omitempty"`
	List                 []*GetScriptHotBannerResponse_BannerInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-"`
	XXX_unrecognized     []byte                                   `json:"-"`
	XXX_sizecache        int32                                    `json:"-"`
}

func (m *GetScriptHotBannerResponse) Reset()         { *m = GetScriptHotBannerResponse{} }
func (m *GetScriptHotBannerResponse) String() string { return proto.CompactTextString(m) }
func (*GetScriptHotBannerResponse) ProtoMessage()    {}
func (*GetScriptHotBannerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{130}
}
func (m *GetScriptHotBannerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScriptHotBannerResponse.Unmarshal(m, b)
}
func (m *GetScriptHotBannerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScriptHotBannerResponse.Marshal(b, m, deterministic)
}
func (dst *GetScriptHotBannerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScriptHotBannerResponse.Merge(dst, src)
}
func (m *GetScriptHotBannerResponse) XXX_Size() int {
	return xxx_messageInfo_GetScriptHotBannerResponse.Size(m)
}
func (m *GetScriptHotBannerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScriptHotBannerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetScriptHotBannerResponse proto.InternalMessageInfo

func (m *GetScriptHotBannerResponse) GetBannerTitle() string {
	if m != nil {
		return m.BannerTitle
	}
	return ""
}

func (m *GetScriptHotBannerResponse) GetList() []*GetScriptHotBannerResponse_BannerInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type GetScriptHotBannerResponse_BannerInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TemplateId           uint32   `protobuf:"varint,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	TopTitle             string   `protobuf:"bytes,3,opt,name=top_title,json=topTitle,proto3" json:"top_title,omitempty"`
	SubTitle             string   `protobuf:"bytes,4,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	BackgroundImg        string   `protobuf:"bytes,5,opt,name=background_img,json=backgroundImg,proto3" json:"background_img,omitempty"`
	ButtonDisplayText    string   `protobuf:"bytes,6,opt,name=button_display_text,json=buttonDisplayText,proto3" json:"button_display_text,omitempty"`
	ButtonColor          string   `protobuf:"bytes,7,opt,name=button_color,json=buttonColor,proto3" json:"button_color,omitempty"`
	BarColor             string   `protobuf:"bytes,8,opt,name=bar_color,json=barColor,proto3" json:"bar_color,omitempty"`
	Icon                 string   `protobuf:"bytes,9,opt,name=icon,proto3" json:"icon,omitempty"`
	SuitableSex          []int32  `protobuf:"varint,10,rep,packed,name=suitable_sex,json=suitableSex,proto3" json:"suitable_sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScriptHotBannerResponse_BannerInfo) Reset()         { *m = GetScriptHotBannerResponse_BannerInfo{} }
func (m *GetScriptHotBannerResponse_BannerInfo) String() string { return proto.CompactTextString(m) }
func (*GetScriptHotBannerResponse_BannerInfo) ProtoMessage()    {}
func (*GetScriptHotBannerResponse_BannerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{130, 0}
}
func (m *GetScriptHotBannerResponse_BannerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScriptHotBannerResponse_BannerInfo.Unmarshal(m, b)
}
func (m *GetScriptHotBannerResponse_BannerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScriptHotBannerResponse_BannerInfo.Marshal(b, m, deterministic)
}
func (dst *GetScriptHotBannerResponse_BannerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScriptHotBannerResponse_BannerInfo.Merge(dst, src)
}
func (m *GetScriptHotBannerResponse_BannerInfo) XXX_Size() int {
	return xxx_messageInfo_GetScriptHotBannerResponse_BannerInfo.Size(m)
}
func (m *GetScriptHotBannerResponse_BannerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScriptHotBannerResponse_BannerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetScriptHotBannerResponse_BannerInfo proto.InternalMessageInfo

func (m *GetScriptHotBannerResponse_BannerInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetScriptHotBannerResponse_BannerInfo) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *GetScriptHotBannerResponse_BannerInfo) GetTopTitle() string {
	if m != nil {
		return m.TopTitle
	}
	return ""
}

func (m *GetScriptHotBannerResponse_BannerInfo) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *GetScriptHotBannerResponse_BannerInfo) GetBackgroundImg() string {
	if m != nil {
		return m.BackgroundImg
	}
	return ""
}

func (m *GetScriptHotBannerResponse_BannerInfo) GetButtonDisplayText() string {
	if m != nil {
		return m.ButtonDisplayText
	}
	return ""
}

func (m *GetScriptHotBannerResponse_BannerInfo) GetButtonColor() string {
	if m != nil {
		return m.ButtonColor
	}
	return ""
}

func (m *GetScriptHotBannerResponse_BannerInfo) GetBarColor() string {
	if m != nil {
		return m.BarColor
	}
	return ""
}

func (m *GetScriptHotBannerResponse_BannerInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GetScriptHotBannerResponse_BannerInfo) GetSuitableSex() []int32 {
	if m != nil {
		return m.SuitableSex
	}
	return nil
}

type StartScriptMatchRequest struct {
	// 匹配模式
	Mode StartScriptMatchRequest_MatchMode `protobuf:"varint,1,opt,name=mode,proto3,enum=aigc_http_logic.StartScriptMatchRequest_MatchMode" json:"mode,omitempty"`
	// 剧本id
	ScriptId             uint32   `protobuf:"varint,2,opt,name=script_id,json=scriptId,proto3" json:"script_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartScriptMatchRequest) Reset()         { *m = StartScriptMatchRequest{} }
func (m *StartScriptMatchRequest) String() string { return proto.CompactTextString(m) }
func (*StartScriptMatchRequest) ProtoMessage()    {}
func (*StartScriptMatchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{131}
}
func (m *StartScriptMatchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartScriptMatchRequest.Unmarshal(m, b)
}
func (m *StartScriptMatchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartScriptMatchRequest.Marshal(b, m, deterministic)
}
func (dst *StartScriptMatchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartScriptMatchRequest.Merge(dst, src)
}
func (m *StartScriptMatchRequest) XXX_Size() int {
	return xxx_messageInfo_StartScriptMatchRequest.Size(m)
}
func (m *StartScriptMatchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StartScriptMatchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StartScriptMatchRequest proto.InternalMessageInfo

func (m *StartScriptMatchRequest) GetMode() StartScriptMatchRequest_MatchMode {
	if m != nil {
		return m.Mode
	}
	return StartScriptMatchRequest_MATCH_MODE_UNSPECIFIED
}

func (m *StartScriptMatchRequest) GetScriptId() uint32 {
	if m != nil {
		return m.ScriptId
	}
	return 0
}

type StartScriptMatchResponse struct {
	Users []*StartScriptMatchResponse_User `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	// 匹配策略 see aigc-group.proto
	Strategy uint32 `protobuf:"varint,2,opt,name=strategy,proto3" json:"strategy,omitempty"`
	// 匹配到的群实例id
	GroupId              uint32   `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartScriptMatchResponse) Reset()         { *m = StartScriptMatchResponse{} }
func (m *StartScriptMatchResponse) String() string { return proto.CompactTextString(m) }
func (*StartScriptMatchResponse) ProtoMessage()    {}
func (*StartScriptMatchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{132}
}
func (m *StartScriptMatchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartScriptMatchResponse.Unmarshal(m, b)
}
func (m *StartScriptMatchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartScriptMatchResponse.Marshal(b, m, deterministic)
}
func (dst *StartScriptMatchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartScriptMatchResponse.Merge(dst, src)
}
func (m *StartScriptMatchResponse) XXX_Size() int {
	return xxx_messageInfo_StartScriptMatchResponse.Size(m)
}
func (m *StartScriptMatchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StartScriptMatchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StartScriptMatchResponse proto.InternalMessageInfo

func (m *StartScriptMatchResponse) GetUsers() []*StartScriptMatchResponse_User {
	if m != nil {
		return m.Users
	}
	return nil
}

func (m *StartScriptMatchResponse) GetStrategy() uint32 {
	if m != nil {
		return m.Strategy
	}
	return 0
}

func (m *StartScriptMatchResponse) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type StartScriptMatchResponse_User struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Sex                  int32    `protobuf:"varint,2,opt,name=sex,proto3" json:"sex,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Avatar               string   `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartScriptMatchResponse_User) Reset()         { *m = StartScriptMatchResponse_User{} }
func (m *StartScriptMatchResponse_User) String() string { return proto.CompactTextString(m) }
func (*StartScriptMatchResponse_User) ProtoMessage()    {}
func (*StartScriptMatchResponse_User) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{132, 0}
}
func (m *StartScriptMatchResponse_User) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartScriptMatchResponse_User.Unmarshal(m, b)
}
func (m *StartScriptMatchResponse_User) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartScriptMatchResponse_User.Marshal(b, m, deterministic)
}
func (dst *StartScriptMatchResponse_User) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartScriptMatchResponse_User.Merge(dst, src)
}
func (m *StartScriptMatchResponse_User) XXX_Size() int {
	return xxx_messageInfo_StartScriptMatchResponse_User.Size(m)
}
func (m *StartScriptMatchResponse_User) XXX_DiscardUnknown() {
	xxx_messageInfo_StartScriptMatchResponse_User.DiscardUnknown(m)
}

var xxx_messageInfo_StartScriptMatchResponse_User proto.InternalMessageInfo

func (m *StartScriptMatchResponse_User) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartScriptMatchResponse_User) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *StartScriptMatchResponse_User) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *StartScriptMatchResponse_User) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

type ConfirmScriptMatchRequest struct {
	// deprecated
	Token                string   `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	ScriptMatchToken     string   `protobuf:"bytes,2,opt,name=script_match_token,json=scriptMatchToken,proto3" json:"script_match_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmScriptMatchRequest) Reset()         { *m = ConfirmScriptMatchRequest{} }
func (m *ConfirmScriptMatchRequest) String() string { return proto.CompactTextString(m) }
func (*ConfirmScriptMatchRequest) ProtoMessage()    {}
func (*ConfirmScriptMatchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{133}
}
func (m *ConfirmScriptMatchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmScriptMatchRequest.Unmarshal(m, b)
}
func (m *ConfirmScriptMatchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmScriptMatchRequest.Marshal(b, m, deterministic)
}
func (dst *ConfirmScriptMatchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmScriptMatchRequest.Merge(dst, src)
}
func (m *ConfirmScriptMatchRequest) XXX_Size() int {
	return xxx_messageInfo_ConfirmScriptMatchRequest.Size(m)
}
func (m *ConfirmScriptMatchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmScriptMatchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmScriptMatchRequest proto.InternalMessageInfo

func (m *ConfirmScriptMatchRequest) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *ConfirmScriptMatchRequest) GetScriptMatchToken() string {
	if m != nil {
		return m.ScriptMatchToken
	}
	return ""
}

type ConfirmScriptMatchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmScriptMatchResponse) Reset()         { *m = ConfirmScriptMatchResponse{} }
func (m *ConfirmScriptMatchResponse) String() string { return proto.CompactTextString(m) }
func (*ConfirmScriptMatchResponse) ProtoMessage()    {}
func (*ConfirmScriptMatchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{134}
}
func (m *ConfirmScriptMatchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmScriptMatchResponse.Unmarshal(m, b)
}
func (m *ConfirmScriptMatchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmScriptMatchResponse.Marshal(b, m, deterministic)
}
func (dst *ConfirmScriptMatchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmScriptMatchResponse.Merge(dst, src)
}
func (m *ConfirmScriptMatchResponse) XXX_Size() int {
	return xxx_messageInfo_ConfirmScriptMatchResponse.Size(m)
}
func (m *ConfirmScriptMatchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmScriptMatchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmScriptMatchResponse proto.InternalMessageInfo

type CancelScriptMatchRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelScriptMatchRequest) Reset()         { *m = CancelScriptMatchRequest{} }
func (m *CancelScriptMatchRequest) String() string { return proto.CompactTextString(m) }
func (*CancelScriptMatchRequest) ProtoMessage()    {}
func (*CancelScriptMatchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{135}
}
func (m *CancelScriptMatchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelScriptMatchRequest.Unmarshal(m, b)
}
func (m *CancelScriptMatchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelScriptMatchRequest.Marshal(b, m, deterministic)
}
func (dst *CancelScriptMatchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelScriptMatchRequest.Merge(dst, src)
}
func (m *CancelScriptMatchRequest) XXX_Size() int {
	return xxx_messageInfo_CancelScriptMatchRequest.Size(m)
}
func (m *CancelScriptMatchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelScriptMatchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CancelScriptMatchRequest proto.InternalMessageInfo

type CancelScriptMatchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelScriptMatchResponse) Reset()         { *m = CancelScriptMatchResponse{} }
func (m *CancelScriptMatchResponse) String() string { return proto.CompactTextString(m) }
func (*CancelScriptMatchResponse) ProtoMessage()    {}
func (*CancelScriptMatchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{136}
}
func (m *CancelScriptMatchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelScriptMatchResponse.Unmarshal(m, b)
}
func (m *CancelScriptMatchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelScriptMatchResponse.Marshal(b, m, deterministic)
}
func (dst *CancelScriptMatchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelScriptMatchResponse.Merge(dst, src)
}
func (m *CancelScriptMatchResponse) XXX_Size() int {
	return xxx_messageInfo_CancelScriptMatchResponse.Size(m)
}
func (m *CancelScriptMatchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelScriptMatchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CancelScriptMatchResponse proto.InternalMessageInfo

type TransAudioToTextReq struct {
	BaseReq *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 上传obs后的key
	AudioKey             string   `protobuf:"bytes,2,opt,name=audio_key,json=audioKey,proto3" json:"audio_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TransAudioToTextReq) Reset()         { *m = TransAudioToTextReq{} }
func (m *TransAudioToTextReq) String() string { return proto.CompactTextString(m) }
func (*TransAudioToTextReq) ProtoMessage()    {}
func (*TransAudioToTextReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{137}
}
func (m *TransAudioToTextReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TransAudioToTextReq.Unmarshal(m, b)
}
func (m *TransAudioToTextReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TransAudioToTextReq.Marshal(b, m, deterministic)
}
func (dst *TransAudioToTextReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransAudioToTextReq.Merge(dst, src)
}
func (m *TransAudioToTextReq) XXX_Size() int {
	return xxx_messageInfo_TransAudioToTextReq.Size(m)
}
func (m *TransAudioToTextReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TransAudioToTextReq.DiscardUnknown(m)
}

var xxx_messageInfo_TransAudioToTextReq proto.InternalMessageInfo

func (m *TransAudioToTextReq) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *TransAudioToTextReq) GetAudioKey() string {
	if m != nil {
		return m.AudioKey
	}
	return ""
}

type TransAudioToTextResp struct {
	// 音频转文字
	TransText            string   `protobuf:"bytes,1,opt,name=trans_text,json=transText,proto3" json:"trans_text,omitempty"`
	TextType             uint32   `protobuf:"varint,2,opt,name=text_type,json=textType,proto3" json:"text_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TransAudioToTextResp) Reset()         { *m = TransAudioToTextResp{} }
func (m *TransAudioToTextResp) String() string { return proto.CompactTextString(m) }
func (*TransAudioToTextResp) ProtoMessage()    {}
func (*TransAudioToTextResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{138}
}
func (m *TransAudioToTextResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TransAudioToTextResp.Unmarshal(m, b)
}
func (m *TransAudioToTextResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TransAudioToTextResp.Marshal(b, m, deterministic)
}
func (dst *TransAudioToTextResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransAudioToTextResp.Merge(dst, src)
}
func (m *TransAudioToTextResp) XXX_Size() int {
	return xxx_messageInfo_TransAudioToTextResp.Size(m)
}
func (m *TransAudioToTextResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TransAudioToTextResp.DiscardUnknown(m)
}

var xxx_messageInfo_TransAudioToTextResp proto.InternalMessageInfo

func (m *TransAudioToTextResp) GetTransText() string {
	if m != nil {
		return m.TransText
	}
	return ""
}

func (m *TransAudioToTextResp) GetTextType() uint32 {
	if m != nil {
		return m.TextType
	}
	return 0
}

type GetChatNumRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChatNumRequest) Reset()         { *m = GetChatNumRequest{} }
func (m *GetChatNumRequest) String() string { return proto.CompactTextString(m) }
func (*GetChatNumRequest) ProtoMessage()    {}
func (*GetChatNumRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{139}
}
func (m *GetChatNumRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChatNumRequest.Unmarshal(m, b)
}
func (m *GetChatNumRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChatNumRequest.Marshal(b, m, deterministic)
}
func (dst *GetChatNumRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChatNumRequest.Merge(dst, src)
}
func (m *GetChatNumRequest) XXX_Size() int {
	return xxx_messageInfo_GetChatNumRequest.Size(m)
}
func (m *GetChatNumRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChatNumRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChatNumRequest proto.InternalMessageInfo

type GetChatNumResponse struct {
	CurDayUsedNum        uint32   `protobuf:"varint,1,opt,name=cur_day_used_num,json=curDayUsedNum,proto3" json:"cur_day_used_num,omitempty"`
	CurDayCfgNum         uint32   `protobuf:"varint,2,opt,name=cur_day_cfg_num,json=curDayCfgNum,proto3" json:"cur_day_cfg_num,omitempty"`
	AvailableExtraNum    uint32   `protobuf:"varint,3,opt,name=available_extra_num,json=availableExtraNum,proto3" json:"available_extra_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChatNumResponse) Reset()         { *m = GetChatNumResponse{} }
func (m *GetChatNumResponse) String() string { return proto.CompactTextString(m) }
func (*GetChatNumResponse) ProtoMessage()    {}
func (*GetChatNumResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{140}
}
func (m *GetChatNumResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChatNumResponse.Unmarshal(m, b)
}
func (m *GetChatNumResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChatNumResponse.Marshal(b, m, deterministic)
}
func (dst *GetChatNumResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChatNumResponse.Merge(dst, src)
}
func (m *GetChatNumResponse) XXX_Size() int {
	return xxx_messageInfo_GetChatNumResponse.Size(m)
}
func (m *GetChatNumResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChatNumResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChatNumResponse proto.InternalMessageInfo

func (m *GetChatNumResponse) GetCurDayUsedNum() uint32 {
	if m != nil {
		return m.CurDayUsedNum
	}
	return 0
}

func (m *GetChatNumResponse) GetCurDayCfgNum() uint32 {
	if m != nil {
		return m.CurDayCfgNum
	}
	return 0
}

func (m *GetChatNumResponse) GetAvailableExtraNum() uint32 {
	if m != nil {
		return m.AvailableExtraNum
	}
	return 0
}

type StartGameRequest struct {
	BaseReq              *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PartnerId            uint32       `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32       `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	GameId               string       `protobuf:"bytes,4,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *StartGameRequest) Reset()         { *m = StartGameRequest{} }
func (m *StartGameRequest) String() string { return proto.CompactTextString(m) }
func (*StartGameRequest) ProtoMessage()    {}
func (*StartGameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{141}
}
func (m *StartGameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartGameRequest.Unmarshal(m, b)
}
func (m *StartGameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartGameRequest.Marshal(b, m, deterministic)
}
func (dst *StartGameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartGameRequest.Merge(dst, src)
}
func (m *StartGameRequest) XXX_Size() int {
	return xxx_messageInfo_StartGameRequest.Size(m)
}
func (m *StartGameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StartGameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StartGameRequest proto.InternalMessageInfo

func (m *StartGameRequest) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *StartGameRequest) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *StartGameRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *StartGameRequest) GetGameId() string {
	if m != nil {
		return m.GameId
	}
	return ""
}

type StartGameResponse struct {
	CtxId                string   `protobuf:"bytes,1,opt,name=ctx_id,json=ctxId,proto3" json:"ctx_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartGameResponse) Reset()         { *m = StartGameResponse{} }
func (m *StartGameResponse) String() string { return proto.CompactTextString(m) }
func (*StartGameResponse) ProtoMessage()    {}
func (*StartGameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{142}
}
func (m *StartGameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartGameResponse.Unmarshal(m, b)
}
func (m *StartGameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartGameResponse.Marshal(b, m, deterministic)
}
func (dst *StartGameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartGameResponse.Merge(dst, src)
}
func (m *StartGameResponse) XXX_Size() int {
	return xxx_messageInfo_StartGameResponse.Size(m)
}
func (m *StartGameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StartGameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StartGameResponse proto.InternalMessageInfo

func (m *StartGameResponse) GetCtxId() string {
	if m != nil {
		return m.CtxId
	}
	return ""
}

type GetGameInfoRequest struct {
	BaseReq              *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GameId               string       `protobuf:"bytes,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGameInfoRequest) Reset()         { *m = GetGameInfoRequest{} }
func (m *GetGameInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetGameInfoRequest) ProtoMessage()    {}
func (*GetGameInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{143}
}
func (m *GetGameInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameInfoRequest.Unmarshal(m, b)
}
func (m *GetGameInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetGameInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameInfoRequest.Merge(dst, src)
}
func (m *GetGameInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetGameInfoRequest.Size(m)
}
func (m *GetGameInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameInfoRequest proto.InternalMessageInfo

func (m *GetGameInfoRequest) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameInfoRequest) GetGameId() string {
	if m != nil {
		return m.GameId
	}
	return ""
}

type GetGameInfoResponse struct {
	Game                 *GameInfo `protobuf:"bytes,1,opt,name=game,proto3" json:"game,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetGameInfoResponse) Reset()         { *m = GetGameInfoResponse{} }
func (m *GetGameInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetGameInfoResponse) ProtoMessage()    {}
func (*GetGameInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{144}
}
func (m *GetGameInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameInfoResponse.Unmarshal(m, b)
}
func (m *GetGameInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetGameInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameInfoResponse.Merge(dst, src)
}
func (m *GetGameInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetGameInfoResponse.Size(m)
}
func (m *GetGameInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameInfoResponse proto.InternalMessageInfo

func (m *GetGameInfoResponse) GetGame() *GameInfo {
	if m != nil {
		return m.Game
	}
	return nil
}

type GetGameListRequest struct {
	BaseReq              *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	RoleId               uint32       `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGameListRequest) Reset()         { *m = GetGameListRequest{} }
func (m *GetGameListRequest) String() string { return proto.CompactTextString(m) }
func (*GetGameListRequest) ProtoMessage()    {}
func (*GetGameListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{145}
}
func (m *GetGameListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameListRequest.Unmarshal(m, b)
}
func (m *GetGameListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameListRequest.Marshal(b, m, deterministic)
}
func (dst *GetGameListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameListRequest.Merge(dst, src)
}
func (m *GetGameListRequest) XXX_Size() int {
	return xxx_messageInfo_GetGameListRequest.Size(m)
}
func (m *GetGameListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameListRequest proto.InternalMessageInfo

func (m *GetGameListRequest) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameListRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetGameListResponse struct {
	GameList             []*GameInfo `protobuf:"bytes,1,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetGameListResponse) Reset()         { *m = GetGameListResponse{} }
func (m *GetGameListResponse) String() string { return proto.CompactTextString(m) }
func (*GetGameListResponse) ProtoMessage()    {}
func (*GetGameListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{146}
}
func (m *GetGameListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameListResponse.Unmarshal(m, b)
}
func (m *GetGameListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameListResponse.Marshal(b, m, deterministic)
}
func (dst *GetGameListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameListResponse.Merge(dst, src)
}
func (m *GetGameListResponse) XXX_Size() int {
	return xxx_messageInfo_GetGameListResponse.Size(m)
}
func (m *GetGameListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameListResponse proto.InternalMessageInfo

func (m *GetGameListResponse) GetGameList() []*GameInfo {
	if m != nil {
		return m.GameList
	}
	return nil
}

type GetOutGamesRequest struct {
	BaseReq              *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	RoleId               uint32       `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetOutGamesRequest) Reset()         { *m = GetOutGamesRequest{} }
func (m *GetOutGamesRequest) String() string { return proto.CompactTextString(m) }
func (*GetOutGamesRequest) ProtoMessage()    {}
func (*GetOutGamesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{147}
}
func (m *GetOutGamesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOutGamesRequest.Unmarshal(m, b)
}
func (m *GetOutGamesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOutGamesRequest.Marshal(b, m, deterministic)
}
func (dst *GetOutGamesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOutGamesRequest.Merge(dst, src)
}
func (m *GetOutGamesRequest) XXX_Size() int {
	return xxx_messageInfo_GetOutGamesRequest.Size(m)
}
func (m *GetOutGamesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOutGamesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOutGamesRequest proto.InternalMessageInfo

func (m *GetOutGamesRequest) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetOutGamesRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetOutGamesResponse struct {
	Topics               []*GameTopic `protobuf:"bytes,1,rep,name=topics,proto3" json:"topics,omitempty"`
	Games                []*OutGames  `protobuf:"bytes,2,rep,name=games,proto3" json:"games,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetOutGamesResponse) Reset()         { *m = GetOutGamesResponse{} }
func (m *GetOutGamesResponse) String() string { return proto.CompactTextString(m) }
func (*GetOutGamesResponse) ProtoMessage()    {}
func (*GetOutGamesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{148}
}
func (m *GetOutGamesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOutGamesResponse.Unmarshal(m, b)
}
func (m *GetOutGamesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOutGamesResponse.Marshal(b, m, deterministic)
}
func (dst *GetOutGamesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOutGamesResponse.Merge(dst, src)
}
func (m *GetOutGamesResponse) XXX_Size() int {
	return xxx_messageInfo_GetOutGamesResponse.Size(m)
}
func (m *GetOutGamesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOutGamesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOutGamesResponse proto.InternalMessageInfo

func (m *GetOutGamesResponse) GetTopics() []*GameTopic {
	if m != nil {
		return m.Topics
	}
	return nil
}

func (m *GetOutGamesResponse) GetGames() []*OutGames {
	if m != nil {
		return m.Games
	}
	return nil
}

type GetTopGameListRequest struct {
	BaseReq              *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	RoleId               uint32       `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	TopicId              uint32       `protobuf:"varint,5,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetTopGameListRequest) Reset()         { *m = GetTopGameListRequest{} }
func (m *GetTopGameListRequest) String() string { return proto.CompactTextString(m) }
func (*GetTopGameListRequest) ProtoMessage()    {}
func (*GetTopGameListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{149}
}
func (m *GetTopGameListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopGameListRequest.Unmarshal(m, b)
}
func (m *GetTopGameListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopGameListRequest.Marshal(b, m, deterministic)
}
func (dst *GetTopGameListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopGameListRequest.Merge(dst, src)
}
func (m *GetTopGameListRequest) XXX_Size() int {
	return xxx_messageInfo_GetTopGameListRequest.Size(m)
}
func (m *GetTopGameListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopGameListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopGameListRequest proto.InternalMessageInfo

func (m *GetTopGameListRequest) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetTopGameListRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetTopGameListRequest) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type GetTopGameListResponse struct {
	Games                []*GameInfo `protobuf:"bytes,1,rep,name=games,proto3" json:"games,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetTopGameListResponse) Reset()         { *m = GetTopGameListResponse{} }
func (m *GetTopGameListResponse) String() string { return proto.CompactTextString(m) }
func (*GetTopGameListResponse) ProtoMessage()    {}
func (*GetTopGameListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{150}
}
func (m *GetTopGameListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopGameListResponse.Unmarshal(m, b)
}
func (m *GetTopGameListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopGameListResponse.Marshal(b, m, deterministic)
}
func (dst *GetTopGameListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopGameListResponse.Merge(dst, src)
}
func (m *GetTopGameListResponse) XXX_Size() int {
	return xxx_messageInfo_GetTopGameListResponse.Size(m)
}
func (m *GetTopGameListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopGameListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopGameListResponse proto.InternalMessageInfo

func (m *GetTopGameListResponse) GetGames() []*GameInfo {
	if m != nil {
		return m.Games
	}
	return nil
}

type LeaveGroupRequest struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LeaveGroupRequest) Reset()         { *m = LeaveGroupRequest{} }
func (m *LeaveGroupRequest) String() string { return proto.CompactTextString(m) }
func (*LeaveGroupRequest) ProtoMessage()    {}
func (*LeaveGroupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{151}
}
func (m *LeaveGroupRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LeaveGroupRequest.Unmarshal(m, b)
}
func (m *LeaveGroupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LeaveGroupRequest.Marshal(b, m, deterministic)
}
func (dst *LeaveGroupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LeaveGroupRequest.Merge(dst, src)
}
func (m *LeaveGroupRequest) XXX_Size() int {
	return xxx_messageInfo_LeaveGroupRequest.Size(m)
}
func (m *LeaveGroupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LeaveGroupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LeaveGroupRequest proto.InternalMessageInfo

func (m *LeaveGroupRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type LeaveGroupResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LeaveGroupResponse) Reset()         { *m = LeaveGroupResponse{} }
func (m *LeaveGroupResponse) String() string { return proto.CompactTextString(m) }
func (*LeaveGroupResponse) ProtoMessage()    {}
func (*LeaveGroupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{152}
}
func (m *LeaveGroupResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LeaveGroupResponse.Unmarshal(m, b)
}
func (m *LeaveGroupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LeaveGroupResponse.Marshal(b, m, deterministic)
}
func (dst *LeaveGroupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LeaveGroupResponse.Merge(dst, src)
}
func (m *LeaveGroupResponse) XXX_Size() int {
	return xxx_messageInfo_LeaveGroupResponse.Size(m)
}
func (m *LeaveGroupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LeaveGroupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LeaveGroupResponse proto.InternalMessageInfo

type GetAiAccountListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAiAccountListRequest) Reset()         { *m = GetAiAccountListRequest{} }
func (m *GetAiAccountListRequest) String() string { return proto.CompactTextString(m) }
func (*GetAiAccountListRequest) ProtoMessage()    {}
func (*GetAiAccountListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{153}
}
func (m *GetAiAccountListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAiAccountListRequest.Unmarshal(m, b)
}
func (m *GetAiAccountListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAiAccountListRequest.Marshal(b, m, deterministic)
}
func (dst *GetAiAccountListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAiAccountListRequest.Merge(dst, src)
}
func (m *GetAiAccountListRequest) XXX_Size() int {
	return xxx_messageInfo_GetAiAccountListRequest.Size(m)
}
func (m *GetAiAccountListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAiAccountListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAiAccountListRequest proto.InternalMessageInfo

type AiAccount struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Sex                  uint32   `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	Signature            string   `protobuf:"bytes,5,opt,name=signature,proto3" json:"signature,omitempty"`
	AccountTags          []string `protobuf:"bytes,6,rep,name=account_tags,json=accountTags,proto3" json:"account_tags,omitempty"`
	BackgroundImage      string   `protobuf:"bytes,7,opt,name=background_image,json=backgroundImage,proto3" json:"background_image,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AiAccount) Reset()         { *m = AiAccount{} }
func (m *AiAccount) String() string { return proto.CompactTextString(m) }
func (*AiAccount) ProtoMessage()    {}
func (*AiAccount) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{154}
}
func (m *AiAccount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AiAccount.Unmarshal(m, b)
}
func (m *AiAccount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AiAccount.Marshal(b, m, deterministic)
}
func (dst *AiAccount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AiAccount.Merge(dst, src)
}
func (m *AiAccount) XXX_Size() int {
	return xxx_messageInfo_AiAccount.Size(m)
}
func (m *AiAccount) XXX_DiscardUnknown() {
	xxx_messageInfo_AiAccount.DiscardUnknown(m)
}

var xxx_messageInfo_AiAccount proto.InternalMessageInfo

func (m *AiAccount) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AiAccount) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *AiAccount) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *AiAccount) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *AiAccount) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

func (m *AiAccount) GetAccountTags() []string {
	if m != nil {
		return m.AccountTags
	}
	return nil
}

func (m *AiAccount) GetBackgroundImage() string {
	if m != nil {
		return m.BackgroundImage
	}
	return ""
}

type GetAiAccountListResponse struct {
	Accounts             []*AiAccount `protobuf:"bytes,1,rep,name=accounts,proto3" json:"accounts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAiAccountListResponse) Reset()         { *m = GetAiAccountListResponse{} }
func (m *GetAiAccountListResponse) String() string { return proto.CompactTextString(m) }
func (*GetAiAccountListResponse) ProtoMessage()    {}
func (*GetAiAccountListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{155}
}
func (m *GetAiAccountListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAiAccountListResponse.Unmarshal(m, b)
}
func (m *GetAiAccountListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAiAccountListResponse.Marshal(b, m, deterministic)
}
func (dst *GetAiAccountListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAiAccountListResponse.Merge(dst, src)
}
func (m *GetAiAccountListResponse) XXX_Size() int {
	return xxx_messageInfo_GetAiAccountListResponse.Size(m)
}
func (m *GetAiAccountListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAiAccountListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAiAccountListResponse proto.InternalMessageInfo

func (m *GetAiAccountListResponse) GetAccounts() []*AiAccount {
	if m != nil {
		return m.Accounts
	}
	return nil
}

type GetUserExclusiveRoleListRequest struct {
	Cursor               string   `protobuf:"bytes,1,opt,name=cursor,proto3" json:"cursor,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserExclusiveRoleListRequest) Reset()         { *m = GetUserExclusiveRoleListRequest{} }
func (m *GetUserExclusiveRoleListRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserExclusiveRoleListRequest) ProtoMessage()    {}
func (*GetUserExclusiveRoleListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{156}
}
func (m *GetUserExclusiveRoleListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserExclusiveRoleListRequest.Unmarshal(m, b)
}
func (m *GetUserExclusiveRoleListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserExclusiveRoleListRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserExclusiveRoleListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserExclusiveRoleListRequest.Merge(dst, src)
}
func (m *GetUserExclusiveRoleListRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserExclusiveRoleListRequest.Size(m)
}
func (m *GetUserExclusiveRoleListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserExclusiveRoleListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserExclusiveRoleListRequest proto.InternalMessageInfo

func (m *GetUserExclusiveRoleListRequest) GetCursor() string {
	if m != nil {
		return m.Cursor
	}
	return ""
}

func (m *GetUserExclusiveRoleListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetUserExclusiveRoleListResponse struct {
	List                 []*AIRole `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	HasMore              bool      `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	NextCursor           string    `protobuf:"bytes,3,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetUserExclusiveRoleListResponse) Reset()         { *m = GetUserExclusiveRoleListResponse{} }
func (m *GetUserExclusiveRoleListResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserExclusiveRoleListResponse) ProtoMessage()    {}
func (*GetUserExclusiveRoleListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_http_logic_fa5096f01ccf9885, []int{157}
}
func (m *GetUserExclusiveRoleListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserExclusiveRoleListResponse.Unmarshal(m, b)
}
func (m *GetUserExclusiveRoleListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserExclusiveRoleListResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserExclusiveRoleListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserExclusiveRoleListResponse.Merge(dst, src)
}
func (m *GetUserExclusiveRoleListResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserExclusiveRoleListResponse.Size(m)
}
func (m *GetUserExclusiveRoleListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserExclusiveRoleListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserExclusiveRoleListResponse proto.InternalMessageInfo

func (m *GetUserExclusiveRoleListResponse) GetList() []*AIRole {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetUserExclusiveRoleListResponse) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

func (m *GetUserExclusiveRoleListResponse) GetNextCursor() string {
	if m != nil {
		return m.NextCursor
	}
	return ""
}

func init() {
	proto.RegisterType((*BaseRequest)(nil), "aigc_http_logic.BaseRequest")
	proto.RegisterType((*AIRoleCategory)(nil), "aigc_http_logic.AIRoleCategory")
	proto.RegisterType((*AIRoleCategory_Label)(nil), "aigc_http_logic.AIRoleCategory.Label")
	proto.RegisterType((*AIRoleCategory_Prop)(nil), "aigc_http_logic.AIRoleCategory.Prop")
	proto.RegisterType((*CreatorInfo)(nil), "aigc_http_logic.CreatorInfo")
	proto.RegisterType((*AIRole)(nil), "aigc_http_logic.AIRole")
	proto.RegisterType((*AIRole_Category)(nil), "aigc_http_logic.AIRole.Category")
	proto.RegisterType((*PetRole)(nil), "aigc_http_logic.PetRole")
	proto.RegisterType((*AIRelationship)(nil), "aigc_http_logic.AIRelationship")
	proto.RegisterType((*AIPhotograph)(nil), "aigc_http_logic.AIPhotograph")
	proto.RegisterType((*CustomVoice)(nil), "aigc_http_logic.CustomVoice")
	proto.RegisterType((*AIPartner)(nil), "aigc_http_logic.AIPartner")
	proto.RegisterType((*AIPartner_Role)(nil), "aigc_http_logic.AIPartner.Role")
	proto.RegisterType((*RelationRoleInfo)(nil), "aigc_http_logic.RelationRoleInfo")
	proto.RegisterType((*PetPartner)(nil), "aigc_http_logic.PetPartner")
	proto.RegisterType((*PetPartner_Role)(nil), "aigc_http_logic.PetPartner.Role")
	proto.RegisterType((*ChattingPartner)(nil), "aigc_http_logic.ChattingPartner")
	proto.RegisterType((*ChattingPartner_Role)(nil), "aigc_http_logic.ChattingPartner.Role")
	proto.RegisterType((*BannerRole)(nil), "aigc_http_logic.BannerRole")
	proto.RegisterType((*RcmdRole)(nil), "aigc_http_logic.RcmdRole")
	proto.RegisterType((*AIRolePrologue)(nil), "aigc_http_logic.AIRolePrologue")
	proto.RegisterType((*InteractiveGame)(nil), "aigc_http_logic.InteractiveGame")
	proto.RegisterType((*GroupInfo)(nil), "aigc_http_logic.GroupInfo")
	proto.RegisterType((*GroupRole)(nil), "aigc_http_logic.GroupRole")
	proto.RegisterType((*GroupTemplateInfo)(nil), "aigc_http_logic.GroupTemplateInfo")
	proto.RegisterType((*GroupTemplateInfo_ScriptInfo)(nil), "aigc_http_logic.GroupTemplateInfo.ScriptInfo")
	proto.RegisterType((*UserRole)(nil), "aigc_http_logic.UserRole")
	proto.RegisterType((*GroupMember)(nil), "aigc_http_logic.GroupMember")
	proto.RegisterType((*Entity)(nil), "aigc_http_logic.Entity")
	proto.RegisterType((*LevelConfig)(nil), "aigc_http_logic.LevelConfig")
	proto.RegisterType((*LevelUpCondition)(nil), "aigc_http_logic.LevelUpCondition")
	proto.RegisterType((*GameTopic)(nil), "aigc_http_logic.GameTopic")
	proto.RegisterType((*GameInfo)(nil), "aigc_http_logic.GameInfo")
	proto.RegisterType((*OutGames)(nil), "aigc_http_logic.OutGames")
	proto.RegisterType((*CreateAIPartnerRequest)(nil), "aigc_http_logic.CreateAIPartnerRequest")
	proto.RegisterType((*CreateAIPartnerRequest_Partner)(nil), "aigc_http_logic.CreateAIPartnerRequest.Partner")
	proto.RegisterType((*CreateAIPartnerResponse)(nil), "aigc_http_logic.CreateAIPartnerResponse")
	proto.RegisterType((*UpdateAIPartnerRequest)(nil), "aigc_http_logic.UpdateAIPartnerRequest")
	proto.RegisterType((*UpdateAIPartnerRequest_Partner)(nil), "aigc_http_logic.UpdateAIPartnerRequest.Partner")
	proto.RegisterType((*UpdateAIPartnerResponse)(nil), "aigc_http_logic.UpdateAIPartnerResponse")
	proto.RegisterType((*SwitchAIPartnerSilentRequest)(nil), "aigc_http_logic.SwitchAIPartnerSilentRequest")
	proto.RegisterType((*SwitchAIPartnerSilentResponse)(nil), "aigc_http_logic.SwitchAIPartnerSilentResponse")
	proto.RegisterType((*RebindAIPartnerRoleRequest)(nil), "aigc_http_logic.RebindAIPartnerRoleRequest")
	proto.RegisterType((*RebindAIPartnerRoleResponse)(nil), "aigc_http_logic.RebindAIPartnerRoleResponse")
	proto.RegisterType((*ReportAIPartnerChattingRequest)(nil), "aigc_http_logic.ReportAIPartnerChattingRequest")
	proto.RegisterType((*ReportAIPartnerChattingResponse)(nil), "aigc_http_logic.ReportAIPartnerChattingResponse")
	proto.RegisterType((*DeleteAIPartnerRequest)(nil), "aigc_http_logic.DeleteAIPartnerRequest")
	proto.RegisterType((*DeleteAIPartnerResponse)(nil), "aigc_http_logic.DeleteAIPartnerResponse")
	proto.RegisterType((*GetAIPartnerRequest)(nil), "aigc_http_logic.GetAIPartnerRequest")
	proto.RegisterType((*GetAIPartnerResponse)(nil), "aigc_http_logic.GetAIPartnerResponse")
	proto.RegisterType((*GetAIPetListRequest)(nil), "aigc_http_logic.GetAIPetListRequest")
	proto.RegisterType((*GetAIPetListResponse)(nil), "aigc_http_logic.GetAIPetListResponse")
	proto.RegisterType((*GetAIRoleRequest)(nil), "aigc_http_logic.GetAIRoleRequest")
	proto.RegisterType((*GetAIRoleResponse)(nil), "aigc_http_logic.GetAIRoleResponse")
	proto.RegisterType((*CreateAIRoleRequest)(nil), "aigc_http_logic.CreateAIRoleRequest")
	proto.RegisterType((*CreateAIRoleRequest_Role)(nil), "aigc_http_logic.CreateAIRoleRequest.Role")
	proto.RegisterType((*CreateAIRoleResponse)(nil), "aigc_http_logic.CreateAIRoleResponse")
	proto.RegisterType((*UpdateAIRoleRequest)(nil), "aigc_http_logic.UpdateAIRoleRequest")
	proto.RegisterType((*UpdateAIRoleRequest_Role)(nil), "aigc_http_logic.UpdateAIRoleRequest.Role")
	proto.RegisterType((*UpdateAIRoleResponse)(nil), "aigc_http_logic.UpdateAIRoleResponse")
	proto.RegisterType((*DeleteAIRoleRequest)(nil), "aigc_http_logic.DeleteAIRoleRequest")
	proto.RegisterType((*DeleteAIRoleResponse)(nil), "aigc_http_logic.DeleteAIRoleResponse")
	proto.RegisterType((*ShareRoleRequest)(nil), "aigc_http_logic.ShareRoleRequest")
	proto.RegisterType((*ShareRoleResponse)(nil), "aigc_http_logic.ShareRoleResponse")
	proto.RegisterType((*LikeRoleRequest)(nil), "aigc_http_logic.LikeRoleRequest")
	proto.RegisterType((*LikeRoleResponse)(nil), "aigc_http_logic.LikeRoleResponse")
	proto.RegisterType((*GetUserAIRoleListRequest)(nil), "aigc_http_logic.GetUserAIRoleListRequest")
	proto.RegisterType((*GetUserAIRoleListResponse)(nil), "aigc_http_logic.GetUserAIRoleListResponse")
	proto.RegisterType((*GetBannerAIRoleListRequest)(nil), "aigc_http_logic.GetBannerAIRoleListRequest")
	proto.RegisterType((*GetBannerAIRoleListResponse)(nil), "aigc_http_logic.GetBannerAIRoleListResponse")
	proto.RegisterType((*GetRcmdAIRoleListRequest)(nil), "aigc_http_logic.GetRcmdAIRoleListRequest")
	proto.RegisterType((*GetRcmdAIRoleListRequest_Prop)(nil), "aigc_http_logic.GetRcmdAIRoleListRequest.Prop")
	proto.RegisterType((*GetRcmdAIRoleListResponse)(nil), "aigc_http_logic.GetRcmdAIRoleListResponse")
	proto.RegisterType((*ListMixDataItem)(nil), "aigc_http_logic.ListMixDataItem")
	proto.RegisterType((*GroupDetailInfo)(nil), "aigc_http_logic.GroupDetailInfo")
	proto.RegisterType((*GetAIRoleCategoryListRequest)(nil), "aigc_http_logic.GetAIRoleCategoryListRequest")
	proto.RegisterType((*GetAIRoleCategoryListResponse)(nil), "aigc_http_logic.GetAIRoleCategoryListResponse")
	proto.RegisterType((*HandleRcmdCommandRequest)(nil), "aigc_http_logic.HandleRcmdCommandRequest")
	proto.RegisterType((*HandleRcmdCommandResponse)(nil), "aigc_http_logic.HandleRcmdCommandResponse")
	proto.RegisterType((*GetPetPartnerRequest)(nil), "aigc_http_logic.GetPetPartnerRequest")
	proto.RegisterType((*GetPetPartnerReponse)(nil), "aigc_http_logic.GetPetPartnerReponse")
	proto.RegisterType((*SearchRoleRequest)(nil), "aigc_http_logic.SearchRoleRequest")
	proto.RegisterType((*SearchRoleResponse)(nil), "aigc_http_logic.SearchRoleResponse")
	proto.RegisterType((*SearchMixDataItem)(nil), "aigc_http_logic.SearchMixDataItem")
	proto.RegisterType((*CreateInteractiveGameRequest)(nil), "aigc_http_logic.CreateInteractiveGameRequest")
	proto.RegisterType((*CreateInteractiveGameRequest_InteractiveGame)(nil), "aigc_http_logic.CreateInteractiveGameRequest.InteractiveGame")
	proto.RegisterType((*CreateInteractiveGameResponse)(nil), "aigc_http_logic.CreateInteractiveGameResponse")
	proto.RegisterType((*UpdateInteractiveGameRequest)(nil), "aigc_http_logic.UpdateInteractiveGameRequest")
	proto.RegisterType((*UpdateInteractiveGameRequest_InteractiveGame)(nil), "aigc_http_logic.UpdateInteractiveGameRequest.InteractiveGame")
	proto.RegisterType((*UpdateInteractiveGameResponse)(nil), "aigc_http_logic.UpdateInteractiveGameResponse")
	proto.RegisterType((*DeleteInteractiveGameRequest)(nil), "aigc_http_logic.DeleteInteractiveGameRequest")
	proto.RegisterType((*DeleteInteractiveGameResponse)(nil), "aigc_http_logic.DeleteInteractiveGameResponse")
	proto.RegisterType((*GetInteractiveGameListRequest)(nil), "aigc_http_logic.GetInteractiveGameListRequest")
	proto.RegisterType((*GetInteractiveGameListResponse)(nil), "aigc_http_logic.GetInteractiveGameListResponse")
	proto.RegisterType((*GetChattingPartnerListRequest)(nil), "aigc_http_logic.GetChattingPartnerListRequest")
	proto.RegisterType((*GetChattingPartnerListResponse)(nil), "aigc_http_logic.GetChattingPartnerListResponse")
	proto.RegisterType((*ChatTemplate)(nil), "aigc_http_logic.ChatTemplate")
	proto.RegisterType((*TemplateMsg)(nil), "aigc_http_logic.TemplateMsg")
	proto.RegisterType((*GetBindChatTemplatesReq)(nil), "aigc_http_logic.GetBindChatTemplatesReq")
	proto.RegisterType((*GetBindChatTemplatesResp)(nil), "aigc_http_logic.GetBindChatTemplatesResp")
	proto.RegisterType((*CreateGroupRequest)(nil), "aigc_http_logic.CreateGroupRequest")
	proto.RegisterType((*CreateGroupResponse)(nil), "aigc_http_logic.CreateGroupResponse")
	proto.RegisterType((*DeleteGroupRequest)(nil), "aigc_http_logic.DeleteGroupRequest")
	proto.RegisterType((*DeleteGroupResponse)(nil), "aigc_http_logic.DeleteGroupResponse")
	proto.RegisterType((*GetGroupInfoRequest)(nil), "aigc_http_logic.GetGroupInfoRequest")
	proto.RegisterType((*GetGroupInfoResponse)(nil), "aigc_http_logic.GetGroupInfoResponse")
	proto.RegisterType((*GetGroupTemplateInfoRequest)(nil), "aigc_http_logic.GetGroupTemplateInfoRequest")
	proto.RegisterType((*GetGroupTemplateInfoResponse)(nil), "aigc_http_logic.GetGroupTemplateInfoResponse")
	proto.RegisterType((*GetGroupListRequest)(nil), "aigc_http_logic.GetGroupListRequest")
	proto.RegisterType((*GetGroupListResponse)(nil), "aigc_http_logic.GetGroupListResponse")
	proto.RegisterType((*GetGroupListResponse_Group)(nil), "aigc_http_logic.GetGroupListResponse.Group")
	proto.RegisterType((*GetGroupMemberListRequest)(nil), "aigc_http_logic.GetGroupMemberListRequest")
	proto.RegisterType((*GetGroupMemberListResponse)(nil), "aigc_http_logic.GetGroupMemberListResponse")
	proto.RegisterType((*AttitudeRequest)(nil), "aigc_http_logic.AttitudeRequest")
	proto.RegisterType((*AttitudeResponse)(nil), "aigc_http_logic.AttitudeResponse")
	proto.RegisterType((*HadAttitudeRequest)(nil), "aigc_http_logic.HadAttitudeRequest")
	proto.RegisterType((*HadAttitudeResponse)(nil), "aigc_http_logic.HadAttitudeResponse")
	proto.RegisterType((*GetReadHeartCountRequest)(nil), "aigc_http_logic.GetReadHeartCountRequest")
	proto.RegisterType((*GetReadHeartCountResponse)(nil), "aigc_http_logic.GetReadHeartCountResponse")
	proto.RegisterType((*ReadHeartRequest)(nil), "aigc_http_logic.ReadHeartRequest")
	proto.RegisterType((*ReadHeartResponse)(nil), "aigc_http_logic.ReadHeartResponse")
	proto.RegisterType((*BatchGetMsgRequest)(nil), "aigc_http_logic.BatchGetMsgRequest")
	proto.RegisterType((*ReadHeartInfo)(nil), "aigc_http_logic.ReadHeartInfo")
	proto.RegisterType((*BatchGetMsgResponse)(nil), "aigc_http_logic.BatchGetMsgResponse")
	proto.RegisterType((*GetExtraInfoRequest)(nil), "aigc_http_logic.GetExtraInfoRequest")
	proto.RegisterType((*ChatBackground)(nil), "aigc_http_logic.ChatBackground")
	proto.RegisterType((*GetExtraInfoResponse)(nil), "aigc_http_logic.GetExtraInfoResponse")
	proto.RegisterType((*GetChatBackgroundListRequest)(nil), "aigc_http_logic.GetChatBackgroundListRequest")
	proto.RegisterType((*GetChatBackgroundListResponse)(nil), "aigc_http_logic.GetChatBackgroundListResponse")
	proto.RegisterType((*SwitchChatBackgroundRequest)(nil), "aigc_http_logic.SwitchChatBackgroundRequest")
	proto.RegisterType((*SwitchChatBackgroundResponse)(nil), "aigc_http_logic.SwitchChatBackgroundResponse")
	proto.RegisterType((*Relation)(nil), "aigc_http_logic.Relation")
	proto.RegisterType((*GetRelationListRequest)(nil), "aigc_http_logic.GetRelationListRequest")
	proto.RegisterType((*GetRelationListResponse)(nil), "aigc_http_logic.GetRelationListResponse")
	proto.RegisterType((*SwitchRelationRequest)(nil), "aigc_http_logic.SwitchRelationRequest")
	proto.RegisterType((*SwitchRelationResponse)(nil), "aigc_http_logic.SwitchRelationResponse")
	proto.RegisterType((*GetIntimacyInfoRequest)(nil), "aigc_http_logic.GetIntimacyInfoRequest")
	proto.RegisterType((*GetIntimacyInfoResponse)(nil), "aigc_http_logic.GetIntimacyInfoResponse")
	proto.RegisterType((*GetIntimacyInfoResponse_Relation)(nil), "aigc_http_logic.GetIntimacyInfoResponse.Relation")
	proto.RegisterType((*GetScriptFilterItemRequest)(nil), "aigc_http_logic.GetScriptFilterItemRequest")
	proto.RegisterType((*FilterItem)(nil), "aigc_http_logic.FilterItem")
	proto.RegisterType((*GetScriptFilterItemResponse)(nil), "aigc_http_logic.GetScriptFilterItemResponse")
	proto.RegisterType((*GetScriptListRequest)(nil), "aigc_http_logic.GetScriptListRequest")
	proto.RegisterType((*ScriptItem)(nil), "aigc_http_logic.ScriptItem")
	proto.RegisterType((*ScriptItem_PlayingInfo)(nil), "aigc_http_logic.ScriptItem.PlayingInfo")
	proto.RegisterType((*GetScriptListResponse)(nil), "aigc_http_logic.GetScriptListResponse")
	proto.RegisterType((*GetScriptHotBannerRequest)(nil), "aigc_http_logic.GetScriptHotBannerRequest")
	proto.RegisterType((*GetScriptHotBannerResponse)(nil), "aigc_http_logic.GetScriptHotBannerResponse")
	proto.RegisterType((*GetScriptHotBannerResponse_BannerInfo)(nil), "aigc_http_logic.GetScriptHotBannerResponse.BannerInfo")
	proto.RegisterType((*StartScriptMatchRequest)(nil), "aigc_http_logic.StartScriptMatchRequest")
	proto.RegisterType((*StartScriptMatchResponse)(nil), "aigc_http_logic.StartScriptMatchResponse")
	proto.RegisterType((*StartScriptMatchResponse_User)(nil), "aigc_http_logic.StartScriptMatchResponse.User")
	proto.RegisterType((*ConfirmScriptMatchRequest)(nil), "aigc_http_logic.ConfirmScriptMatchRequest")
	proto.RegisterType((*ConfirmScriptMatchResponse)(nil), "aigc_http_logic.ConfirmScriptMatchResponse")
	proto.RegisterType((*CancelScriptMatchRequest)(nil), "aigc_http_logic.CancelScriptMatchRequest")
	proto.RegisterType((*CancelScriptMatchResponse)(nil), "aigc_http_logic.CancelScriptMatchResponse")
	proto.RegisterType((*TransAudioToTextReq)(nil), "aigc_http_logic.TransAudioToTextReq")
	proto.RegisterType((*TransAudioToTextResp)(nil), "aigc_http_logic.TransAudioToTextResp")
	proto.RegisterType((*GetChatNumRequest)(nil), "aigc_http_logic.GetChatNumRequest")
	proto.RegisterType((*GetChatNumResponse)(nil), "aigc_http_logic.GetChatNumResponse")
	proto.RegisterType((*StartGameRequest)(nil), "aigc_http_logic.StartGameRequest")
	proto.RegisterType((*StartGameResponse)(nil), "aigc_http_logic.StartGameResponse")
	proto.RegisterType((*GetGameInfoRequest)(nil), "aigc_http_logic.GetGameInfoRequest")
	proto.RegisterType((*GetGameInfoResponse)(nil), "aigc_http_logic.GetGameInfoResponse")
	proto.RegisterType((*GetGameListRequest)(nil), "aigc_http_logic.GetGameListRequest")
	proto.RegisterType((*GetGameListResponse)(nil), "aigc_http_logic.GetGameListResponse")
	proto.RegisterType((*GetOutGamesRequest)(nil), "aigc_http_logic.GetOutGamesRequest")
	proto.RegisterType((*GetOutGamesResponse)(nil), "aigc_http_logic.GetOutGamesResponse")
	proto.RegisterType((*GetTopGameListRequest)(nil), "aigc_http_logic.GetTopGameListRequest")
	proto.RegisterType((*GetTopGameListResponse)(nil), "aigc_http_logic.GetTopGameListResponse")
	proto.RegisterType((*LeaveGroupRequest)(nil), "aigc_http_logic.LeaveGroupRequest")
	proto.RegisterType((*LeaveGroupResponse)(nil), "aigc_http_logic.LeaveGroupResponse")
	proto.RegisterType((*GetAiAccountListRequest)(nil), "aigc_http_logic.GetAiAccountListRequest")
	proto.RegisterType((*AiAccount)(nil), "aigc_http_logic.AiAccount")
	proto.RegisterType((*GetAiAccountListResponse)(nil), "aigc_http_logic.GetAiAccountListResponse")
	proto.RegisterType((*GetUserExclusiveRoleListRequest)(nil), "aigc_http_logic.GetUserExclusiveRoleListRequest")
	proto.RegisterType((*GetUserExclusiveRoleListResponse)(nil), "aigc_http_logic.GetUserExclusiveRoleListResponse")
	proto.RegisterEnum("aigc_http_logic.AuditResult", AuditResult_name, AuditResult_value)
	proto.RegisterEnum("aigc_http_logic.LikeState", LikeState_name, LikeState_value)
	proto.RegisterEnum("aigc_http_logic.CreatorInfoType", CreatorInfoType_name, CreatorInfoType_value)
	proto.RegisterEnum("aigc_http_logic.RelationType", RelationType_name, RelationType_value)
	proto.RegisterEnum("aigc_http_logic.AttitudeAction", AttitudeAction_name, AttitudeAction_value)
	proto.RegisterEnum("aigc_http_logic.ObjectType", ObjectType_name, ObjectType_value)
	proto.RegisterEnum("aigc_http_logic.Entity_Type", Entity_Type_name, Entity_Type_value)
	proto.RegisterEnum("aigc_http_logic.TemplateMsg_SenderType", TemplateMsg_SenderType_name, TemplateMsg_SenderType_value)
	proto.RegisterEnum("aigc_http_logic.ChatBackground_State", ChatBackground_State_name, ChatBackground_State_value)
	proto.RegisterEnum("aigc_http_logic.Relation_State", Relation_State_name, Relation_State_value)
	proto.RegisterEnum("aigc_http_logic.FilterItem_FilterType", FilterItem_FilterType_name, FilterItem_FilterType_value)
	proto.RegisterEnum("aigc_http_logic.StartScriptMatchRequest_MatchMode", StartScriptMatchRequest_MatchMode_name, StartScriptMatchRequest_MatchMode_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/aigc-http-logic/aigc-http-logic.proto", fileDescriptor_aigc_http_logic_fa5096f01ccf9885)
}

var fileDescriptor_aigc_http_logic_fa5096f01ccf9885 = []byte{
	// 6505 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x7c, 0x4b, 0x6c, 0x24, 0x49,
	0x5a, 0xf0, 0x64, 0x3d, 0xec, 0xaa, 0xaf, 0xca, 0xe5, 0x72, 0xfa, 0x55, 0x2e, 0x77, 0xb7, 0x7b,
	0xf2, 0xdf, 0x99, 0xe9, 0xe9, 0xd9, 0x71, 0xcf, 0xf6, 0xce, 0xcc, 0xee, 0xbf, 0xfb, 0xcf, 0x3f,
	0xaa, 0xf6, 0xa3, 0xbb, 0x66, 0xfc, 0x9a, 0x74, 0xb9, 0x61, 0x60, 0x45, 0x92, 0xce, 0x0a, 0x97,
	0x73, 0xba, 0x2a, 0xb3, 0x26, 0x33, 0xcb, 0x6d, 0xc3, 0x05, 0x09, 0x24, 0xc4, 0x4b, 0x5c, 0x40,
	0xcb, 0x01, 0x21, 0x60, 0x25, 0x6e, 0x9c, 0x58, 0x71, 0x40, 0x70, 0xe1, 0x75, 0x43, 0xcb, 0xeb,
	0x82, 0x58, 0x09, 0x71, 0xe0, 0x86, 0xb4, 0x12, 0x88, 0x1b, 0x07, 0x14, 0x5f, 0x44, 0x64, 0x46,
	0xbe, 0x6c, 0xf7, 0x63, 0xb4, 0x2b, 0x6e, 0x19, 0x5f, 0xbc, 0xbe, 0x88, 0xf8, 0xe2, 0x7b, 0x47,
	0xc2, 0xbb, 0x41, 0x70, 0xef, 0xf3, 0x89, 0x6d, 0x3d, 0xf1, 0xed, 0xe1, 0x19, 0xf1, 0xee, 0x99,
	0xf6, 0xc0, 0x7a, 0xfb, 0x34, 0x08, 0xc6, 0x6f, 0x0f, 0xdd, 0x81, 0x6d, 0x25, 0xcb, 0xeb, 0x63,
	0xcf, 0x0d, 0x5c, 0x75, 0x96, 0x82, 0x0d, 0x0a, 0x36, 0x10, 0xac, 0xfd, 0xba, 0x02, 0xb5, 0x07,
	0xa6, 0x4f, 0x74, 0xf2, 0xf9, 0x84, 0xf8, 0x81, 0xba, 0x0a, 0xd5, 0x3e, 0x39, 0xb3, 0x2d, 0x62,
	0xd8, 0xfd, 0x96, 0x72, 0x5b, 0xb9, 0x53, 0xd5, 0x2b, 0x0c, 0xd0, 0xed, 0xd3, 0xca, 0x91, 0xe9,
	0x3d, 0x21, 0x01, 0xad, 0x2c, 0xdc, 0x56, 0xee, 0xcc, 0xe8, 0x15, 0x06, 0xe8, 0xf6, 0xd5, 0x35,
	0xa8, 0x59, 0x43, 0x9b, 0x38, 0x81, 0x11, 0x5c, 0x8c, 0x49, 0xab, 0x88, 0xd5, 0xc0, 0x40, 0xbd,
	0x8b, 0x31, 0x51, 0x5f, 0x83, 0x06, 0x6f, 0x70, 0x46, 0x3c, 0xdf, 0x76, 0x9d, 0x56, 0x09, 0xdb,
	0xcc, 0x30, 0xe8, 0x63, 0x06, 0xd4, 0xfe, 0xa3, 0x00, 0x8d, 0x4e, 0x57, 0x77, 0x87, 0x64, 0xc3,
	0x0c, 0xc8, 0xc0, 0xf5, 0x2e, 0xd4, 0x06, 0x14, 0x42, 0x6c, 0x0a, 0x76, 0x5f, 0x5d, 0x80, 0x72,
	0x60, 0x07, 0x43, 0x82, 0x38, 0x54, 0x75, 0x56, 0xa0, 0x08, 0x8c, 0x3d, 0x77, 0x6c, 0xf8, 0x16,
	0x71, 0x88, 0xdf, 0x2a, 0xde, 0x2e, 0x52, 0x04, 0x28, 0xe8, 0x10, 0x21, 0xea, 0x37, 0xa0, 0x4c,
	0x4b, 0x7e, 0xab, 0x74, 0xbb, 0x78, 0xa7, 0x76, 0xff, 0x4b, 0xeb, 0x89, 0xcd, 0x58, 0x8f, 0x4f,
	0xbb, 0x7e, 0xe0, 0xb9, 0x63, 0x9d, 0x75, 0x69, 0x6f, 0x40, 0x79, 0xc7, 0x3c, 0x26, 0xc3, 0x14,
	0x2e, 0x2a, 0x94, 0x1c, 0x73, 0x24, 0x50, 0xc1, 0x6f, 0x75, 0x09, 0xa6, 0x62, 0x48, 0xf0, 0x52,
	0xfb, 0xdb, 0x0a, 0x94, 0xe8, 0xa0, 0xd7, 0x1a, 0xe4, 0x03, 0x98, 0x1a, 0xd2, 0x19, 0x05, 0xba,
	0xaf, 0x5d, 0x85, 0x2e, 0xe2, 0xa7, 0xf3, 0x4e, 0xea, 0x97, 0x41, 0xc5, 0x2f, 0xc3, 0x27, 0x43,
	0x62, 0x05, 0xc6, 0xd0, 0x1e, 0xd9, 0x41, 0xab, 0x8c, 0x3b, 0xde, 0xc4, 0x9a, 0x43, 0xac, 0xd8,
	0xa1, 0x70, 0xed, 0x33, 0xa8, 0x6d, 0x78, 0xc4, 0x0c, 0x5c, 0xaf, 0xeb, 0x9c, 0xb8, 0x6a, 0x13,
	0x8a, 0x13, 0x8e, 0xe0, 0x8c, 0x4e, 0x3f, 0x33, 0x31, 0x6c, 0xc1, 0xb4, 0x69, 0x59, 0xee, 0xc4,
	0x09, 0xf0, 0xb4, 0xab, 0xba, 0x28, 0x52, 0x42, 0xb1, 0x7d, 0xe3, 0xc4, 0x1d, 0x0e, 0xdd, 0xa7,
	0x78, 0xca, 0x15, 0xbd, 0x62, 0xfb, 0xdb, 0x58, 0xd6, 0xfe, 0x7c, 0x1a, 0xa6, 0x18, 0xea, 0xd2,
	0x3e, 0xcc, 0x88, 0x7d, 0x40, 0xe2, 0x61, 0xb4, 0x85, 0xdf, 0x14, 0x17, 0x9f, 0x9c, 0xe3, 0x0c,
	0x65, 0x9d, 0x7e, 0x86, 0xb8, 0x94, 0x24, 0x5c, 0x16, 0xa0, 0xec, 0x07, 0x17, 0x43, 0x82, 0x2b,
	0xac, 0xea, 0xac, 0xc0, 0xa0, 0x66, 0x40, 0x5a, 0x53, 0x38, 0x20, 0x2b, 0xd0, 0xe3, 0x31, 0xcf,
	0xcc, 0xc0, 0xf4, 0x5a, 0xd3, 0xd8, 0x98, 0x97, 0x68, 0x6b, 0x7b, 0x64, 0x0e, 0x48, 0xab, 0xc2,
	0xc6, 0xc0, 0x02, 0x42, 0x9d, 0xc0, 0x73, 0x5b, 0x55, 0x0e, 0xa5, 0x05, 0xf5, 0x55, 0xa8, 0xf7,
	0x6d, 0x73, 0xe8, 0x0e, 0x0c, 0xcb, 0x1d, 0xba, 0x5e, 0x0b, 0xb0, 0xb2, 0xc6, 0x60, 0x1b, 0x14,
	0xa4, 0xde, 0x80, 0xaa, 0x75, 0x6a, 0x7a, 0xa6, 0x15, 0x10, 0xaf, 0x55, 0xc3, 0xfa, 0x08, 0xa0,
	0xb6, 0xa1, 0x32, 0xf6, 0xdc, 0xa1, 0x3b, 0x98, 0x90, 0x56, 0x9d, 0xdd, 0x33, 0x51, 0xa6, 0x37,
	0x45, 0x7c, 0x1b, 0xe6, 0xa4, 0x6f, 0xbb, 0xad, 0x19, 0x6c, 0x31, 0x23, 0xa0, 0x1d, 0x0a, 0xa4,
	0xeb, 0x08, 0xec, 0xd1, 0xb1, 0x47, 0x5a, 0x0d, 0xb6, 0x0e, 0x56, 0xc2, 0x9b, 0xe8, 0x7a, 0x0e,
	0xf1, 0x0c, 0xdb, 0x72, 0x9d, 0xd6, 0x2c, 0x56, 0x02, 0x03, 0x75, 0x2d, 0xd7, 0xa1, 0xc8, 0xd3,
	0x61, 0x03, 0xc3, 0x23, 0xfe, 0x64, 0x18, 0xb4, 0x9a, 0xb8, 0x3b, 0x35, 0x84, 0xe9, 0x08, 0xc2,
	0x93, 0x30, 0x07, 0x7e, 0x6b, 0xee, 0x76, 0x91, 0xee, 0x31, 0xfd, 0x56, 0xff, 0x1f, 0x54, 0x2c,
	0x4e, 0x6c, 0x2d, 0xf5, 0xb6, 0x72, 0xa7, 0x76, 0xff, 0x76, 0x0e, 0x4d, 0xae, 0x0b, 0xa2, 0xd4,
	0xc3, 0x1e, 0xea, 0x5d, 0x98, 0x23, 0x8e, 0x79, 0x3c, 0x24, 0x86, 0x67, 0x8d, 0xfa, 0x86, 0x47,
	0xc6, 0xc3, 0x8b, 0xd6, 0x3c, 0xd2, 0xc6, 0x2c, 0xab, 0xd0, 0xad, 0x51, 0x5f, 0xa7, 0x60, 0x75,
	0x05, 0x2a, 0x43, 0xfb, 0x09, 0x31, 0x9c, 0xc9, 0xa8, 0xb5, 0x80, 0xc8, 0x4d, 0xd3, 0xf2, 0xde,
	0x64, 0xa4, 0xde, 0x04, 0xc0, 0x2a, 0x76, 0xae, 0x8b, 0x58, 0x59, 0xa5, 0x90, 0x43, 0x3c, 0xdb,
	0x15, 0xa8, 0xf8, 0x81, 0xeb, 0x5d, 0x50, 0x0e, 0xb5, 0xc4, 0x88, 0x12, 0xcb, 0x9c, 0x41, 0x31,
	0x1a, 0x37, 0x28, 0x71, 0x2f, 0x73, 0x06, 0xc5, 0x40, 0x47, 0x76, 0x9f, 0xd2, 0x33, 0x39, 0x1f,
	0xbb, 0x3e, 0xe9, 0xb7, 0x5a, 0x88, 0x97, 0x28, 0x52, 0x1a, 0x24, 0xe7, 0x41, 0x6b, 0xe5, 0xb6,
	0x72, 0xa7, 0xae, 0xd3, 0x4f, 0x8a, 0x06, 0x9b, 0x67, 0xe4, 0xf6, 0x49, 0xab, 0xcd, 0xd0, 0x40,
	0xc8, 0xae, 0xdb, 0x27, 0x74, 0xb1, 0x62, 0x2e, 0xdb, 0x39, 0x71, 0x19, 0x4b, 0x5c, 0xc5, 0x56,
	0xb3, 0x56, 0x74, 0xd1, 0x90, 0x2f, 0x7e, 0x08, 0x75, 0xb9, 0x6d, 0xeb, 0x06, 0x6e, 0xed, 0x8d,
	0xd4, 0xd6, 0x4a, 0x17, 0x54, 0xaf, 0x49, 0x83, 0xd0, 0xc9, 0x26, 0x3e, 0xf1, 0x0c, 0xcf, 0x1d,
	0x12, 0xc3, 0x27, 0x41, 0x60, 0x3b, 0x83, 0xd6, 0x4d, 0x5c, 0xfc, 0x2c, 0xad, 0xa0, 0x27, 0x72,
	0xc8, 0xc0, 0x78, 0x23, 0x2c, 0x77, 0x4c, 0x5a, 0xb7, 0xf8, 0x8d, 0xa0, 0x85, 0xf6, 0x3b, 0x50,
	0x79, 0x36, 0x66, 0xab, 0x7d, 0x5f, 0x81, 0xe9, 0x03, 0x12, 0x64, 0xde, 0xe2, 0xeb, 0xdd, 0xd8,
	0xd8, 0xf5, 0x28, 0x27, 0xaf, 0x07, 0xdf, 0xf1, 0xa9, 0x68, 0xc7, 0x3f, 0x82, 0xa6, 0xe5, 0x0e,
	0x91, 0x97, 0x85, 0x17, 0x67, 0x1a, 0xb7, 0x6a, 0x2d, 0x87, 0x0a, 0x0f, 0x78, 0x33, 0x7d, 0x96,
	0x77, 0x14, 0x80, 0xe4, 0x0d, 0xa9, 0x24, 0x6f, 0x88, 0xf6, 0x2e, 0xca, 0x20, 0x32, 0x34, 0x03,
	0xdb, 0x75, 0xfc, 0x53, 0x7b, 0x9c, 0xc5, 0xaa, 0x92, 0x0c, 0x51, 0xdb, 0x81, 0x7a, 0xa7, 0x7b,
	0x70, 0xea, 0x06, 0xee, 0xc0, 0x33, 0xc7, 0xa7, 0xa9, 0x3e, 0x11, 0xe3, 0x29, 0x64, 0x33, 0x9e,
	0xa2, 0xc4, 0x78, 0xb4, 0xf7, 0xa0, 0xb6, 0x31, 0xf1, 0x03, 0x77, 0xf4, 0xd8, 0xb5, 0x2d, 0x92,
	0x3a, 0x97, 0x25, 0x98, 0x7a, 0x4a, 0xec, 0xc1, 0x69, 0x80, 0x83, 0x15, 0x74, 0x5e, 0xd2, 0x7e,
	0x0e, 0xa0, 0xda, 0xe9, 0x1e, 0x98, 0x5e, 0xe0, 0x10, 0xef, 0x3a, 0x68, 0x53, 0x6e, 0x6d, 0x99,
	0xc3, 0xa1, 0x81, 0x15, 0x0c, 0x85, 0x0a, 0x05, 0xec, 0xd1, 0xca, 0xaf, 0x42, 0x89, 0xd2, 0x15,
	0x1e, 0x5d, 0xf6, 0x56, 0xf3, 0xa9, 0xd6, 0xe9, 0x96, 0xeb, 0xd8, 0x18, 0x05, 0xa0, 0x3d, 0x24,
	0x0e, 0x13, 0x38, 0x15, 0x9d, 0x97, 0xd4, 0xd7, 0x61, 0xd6, 0x3a, 0x35, 0x9d, 0x01, 0x61, 0xb4,
	0x6a, 0x39, 0x01, 0x1e, 0x21, 0xd5, 0x01, 0x10, 0x8c, 0xf2, 0xcc, 0x09, 0xd4, 0x0f, 0x00, 0xc6,
	0xe1, 0x36, 0xe2, 0xf1, 0xd4, 0xee, 0xdf, 0xcc, 0x9a, 0x3a, 0x6c, 0xa4, 0x4b, 0x1d, 0xd4, 0x6f,
	0x42, 0xc5, 0xe3, 0x67, 0x87, 0x5c, 0x3b, 0x87, 0x44, 0xa4, 0xe3, 0xd5, 0xc3, 0x0e, 0x88, 0xbb,
	0x3b, 0xf1, 0x2c, 0x82, 0x3c, 0x9d, 0x0a, 0x6f, 0x2c, 0xd1, 0x1b, 0x3f, 0x71, 0x7c, 0x12, 0xb0,
	0x6d, 0xaa, 0xe1, 0xba, 0xaa, 0x08, 0xd9, 0xe3, 0x9b, 0xe8, 0x9f, 0xba, 0x4f, 0x8d, 0x53, 0xdb,
	0x09, 0x90, 0xa1, 0x57, 0xf4, 0x0a, 0x05, 0x3c, 0xb2, 0x1d, 0xe4, 0xa6, 0x08, 0x67, 0x6c, 0x1c,
	0xbf, 0xa3, 0xf1, 0x70, 0x7b, 0x1b, 0xd2, 0x78, 0x78, 0xa9, 0x1e, 0x41, 0x43, 0xa0, 0x84, 0x2d,
	0xfc, 0xd6, 0x2c, 0xaa, 0x01, 0xaf, 0xa6, 0x56, 0x22, 0xd6, 0x41, 0xbb, 0x21, 0x73, 0x98, 0xf1,
	0x24, 0x88, 0xdf, 0xfe, 0x41, 0x19, 0x4a, 0x5f, 0x84, 0xb4, 0xa5, 0xfc, 0xb7, 0x9c, 0x2d, 0x57,
	0xa7, 0xb2, 0xc9, 0x7b, 0x5a, 0x96, 0xab, 0x49, 0x09, 0x5a, 0xb9, 0x42, 0x82, 0x56, 0x2f, 0x93,
	0xa0, 0x70, 0xa5, 0x04, 0xad, 0x5d, 0x2e, 0x41, 0xeb, 0x31, 0x09, 0x9a, 0x14, 0x90, 0x33, 0x69,
	0x01, 0x99, 0x29, 0xce, 0x1a, 0x57, 0x8b, 0xb3, 0xd9, 0xcb, 0xc4, 0x59, 0xf3, 0x32, 0x71, 0x36,
	0x77, 0xa9, 0x38, 0x53, 0x2f, 0x13, 0x67, 0xf3, 0x99, 0xe2, 0x6c, 0x21, 0x4f, 0x9c, 0x2d, 0x5e,
	0x4b, 0x9c, 0x2d, 0x5d, 0x4f, 0x9c, 0x2d, 0xbf, 0x14, 0x71, 0xd6, 0xba, 0x42, 0x9c, 0xad, 0x48,
	0xe2, 0x4c, 0xfb, 0x77, 0x05, 0x9a, 0xc9, 0x5b, 0x71, 0x2d, 0xea, 0x17, 0xb4, 0x5e, 0xcc, 0xa2,
	0xf5, 0x52, 0x36, 0xad, 0x97, 0xb3, 0x69, 0x7d, 0x4a, 0xa6, 0x75, 0xe9, 0x28, 0xa6, 0xe3, 0x47,
	0xf1, 0x00, 0xc2, 0xdb, 0xca, 0x76, 0x95, 0x5e, 0x83, 0x46, 0x06, 0xb3, 0x13, 0xeb, 0xa1, 0x7b,
	0xac, 0xd7, 0x3d, 0xa9, 0xa4, 0xfd, 0x75, 0x01, 0xe0, 0x80, 0x04, 0x79, 0x2c, 0x3f, 0x62, 0x68,
	0x85, 0x18, 0x43, 0x7b, 0x97, 0x73, 0xf6, 0x62, 0x8e, 0x2a, 0x17, 0x0d, 0x29, 0xb3, 0xf6, 0x2c,
	0x76, 0x10, 0x13, 0x20, 0xe5, 0x84, 0x00, 0xe9, 0xc0, 0x8c, 0x85, 0x62, 0xcc, 0x38, 0xa3, 0x72,
	0xcc, 0x6f, 0x4d, 0x21, 0x1f, 0xcb, 0x20, 0x88, 0x48, 0xd8, 0xe9, 0x75, 0x2b, 0x2a, 0xf8, 0xed,
	0x93, 0x17, 0x62, 0x60, 0xd1, 0x51, 0x95, 0x62, 0x47, 0xc5, 0x6f, 0x41, 0x39, 0xbc, 0x05, 0xda,
	0xf7, 0x0a, 0x30, 0xbb, 0x71, 0x6a, 0x22, 0x69, 0x3d, 0x8b, 0x00, 0xfd, 0xbf, 0xb1, 0x9d, 0x4c,
	0x1b, 0x6a, 0x89, 0x31, 0xa5, 0xed, 0x6c, 0x7f, 0x5f, 0xf9, 0x21, 0x33, 0xe7, 0x6b, 0xb0, 0xba,
	0xe7, 0xe7, 0x34, 0xda, 0xdf, 0x2b, 0x00, 0x0f, 0x4c, 0xc7, 0x61, 0xb7, 0xf6, 0xe5, 0x2e, 0x92,
	0xdd, 0xb3, 0xb2, 0x7c, 0xcf, 0x62, 0x02, 0x63, 0x2a, 0x29, 0x30, 0x12, 0x5a, 0xdf, 0x74, 0xca,
	0x2e, 0x4a, 0x4b, 0x8d, 0x4a, 0x86, 0xd4, 0xd0, 0xfe, 0x5b, 0x81, 0x0a, 0x32, 0xf7, 0xff, 0x35,
	0x4b, 0x0a, 0xcd, 0xbd, 0xaa, 0x64, 0xee, 0x45, 0x14, 0x03, 0x32, 0xc5, 0x68, 0x3b, 0xc2, 0x3f,
	0x13, 0xaa, 0xd3, 0xb4, 0x37, 0xbd, 0x4a, 0x4c, 0x39, 0xc5, 0x6f, 0xba, 0x1a, 0x36, 0x1f, 0x37,
	0x1b, 0xb0, 0xc0, 0x76, 0xe2, 0x73, 0xee, 0x1c, 0xa2, 0x9f, 0xda, 0x0f, 0x14, 0x98, 0xed, 0x3a,
	0x01, 0xa1, 0x0b, 0xb2, 0xcf, 0xc8, 0x43, 0xba, 0x13, 0x49, 0x55, 0x77, 0x19, 0xa6, 0x51, 0x18,
	0xd8, 0x7d, 0xce, 0x84, 0xa7, 0x68, 0xb1, 0xdb, 0xa7, 0xe2, 0x31, 0x70, 0xc7, 0xb6, 0x45, 0x6b,
	0x18, 0xb5, 0x4f, 0x63, 0xb9, 0x2b, 0x99, 0x2d, 0x53, 0xb2, 0x8f, 0x48, 0x85, 0x52, 0x9f, 0xf8,
	0x16, 0xdf, 0x28, 0xfc, 0x8e, 0xe9, 0x11, 0x95, 0x84, 0x1e, 0x11, 0xde, 0xa5, 0xaa, 0x7c, 0x97,
	0x56, 0xa1, 0x8a, 0xf8, 0xe0, 0x11, 0x72, 0xd5, 0x83, 0x02, 0x90, 0xdf, 0xad, 0x41, 0x0d, 0x2b,
	0xf9, 0xde, 0x31, 0xbd, 0x03, 0x28, 0xa8, 0xc3, 0xf6, 0xef, 0xbb, 0x25, 0xa8, 0x3e, 0xf4, 0xdc,
	0xc9, 0x38, 0x53, 0x2c, 0xad, 0x42, 0xd5, 0x7d, 0x4a, 0x0f, 0x74, 0x12, 0xf9, 0xd8, 0x10, 0x40,
	0x2f, 0xda, 0x1a, 0xd4, 0x02, 0x32, 0x1a, 0x0f, 0xcd, 0x00, 0x37, 0x83, 0xfb, 0xd8, 0x04, 0xa8,
	0x2b, 0xf3, 0xfa, 0x52, 0x8c, 0xd7, 0xcb, 0x1a, 0xc8, 0x54, 0x4a, 0x03, 0x19, 0x91, 0xd1, 0x31,
	0xf1, 0xb0, 0x92, 0xa9, 0xe3, 0x55, 0x06, 0xa1, 0xd5, 0x6f, 0xc2, 0xdc, 0xc8, 0x3c, 0x37, 0x3c,
	0x62, 0x0e, 0x0d, 0x14, 0xcb, 0xb4, 0x55, 0x05, 0x5b, 0x35, 0x46, 0xe6, 0xb9, 0x4e, 0xcc, 0xe1,
	0x91, 0xcf, 0x9a, 0xc6, 0x75, 0x99, 0x5a, 0x52, 0x97, 0x79, 0x0d, 0x1a, 0x23, 0x33, 0xb0, 0x4e,
	0x0d, 0x3f, 0xf0, 0xa8, 0xb1, 0x79, 0x81, 0x4a, 0xd7, 0x8c, 0x3e, 0x83, 0xd0, 0x43, 0x0e, 0xa4,
	0xcd, 0x86, 0xc4, 0x3c, 0x23, 0x51, 0x33, 0xc6, 0x92, 0x66, 0x10, 0x1a, 0x36, 0xe3, 0x77, 0xaa,
	0x99, 0xbe, 0x53, 0x73, 0xd2, 0x9d, 0x12, 0xe7, 0xad, 0x4a, 0xe7, 0x1d, 0xd1, 0xf5, 0x7c, 0x8c,
	0x13, 0xbe, 0x81, 0xc6, 0x49, 0x60, 0x1c, 0x9b, 0xd6, 0x93, 0x81, 0xe7, 0x4e, 0x9c, 0x3e, 0x6a,
	0x48, 0x55, 0xbd, 0x41, 0xc1, 0x0f, 0x42, 0x28, 0x5d, 0x27, 0xfd, 0x1a, 0x33, 0x81, 0xcd, 0x95,
	0x25, 0x84, 0xa0, 0x02, 0xf4, 0x26, 0x34, 0xa3, 0x21, 0x8c, 0xd1, 0xc4, 0xb7, 0x2d, 0xee, 0x8a,
	0x98, 0x8d, 0xe0, 0xbb, 0x14, 0x4c, 0x55, 0x1d, 0xf4, 0xe1, 0x1a, 0xfe, 0x98, 0x98, 0x4f, 0x0c,
	0x7a, 0x71, 0xfc, 0xd6, 0x6d, 0xbc, 0x83, 0xb3, 0x58, 0x71, 0x48, 0xe1, 0x3d, 0x0a, 0xd6, 0xfe,
	0x4c, 0xe1, 0x64, 0x73, 0x99, 0xcd, 0x3d, 0x95, 0xde, 0x8e, 0xe9, 0xb8, 0x63, 0x92, 0x2f, 0xbd,
	0x12, 0x5b, 0xfa, 0xe5, 0x8a, 0xf6, 0x26, 0x84, 0xdc, 0xc2, 0x18, 0xda, 0x7e, 0xd0, 0x6a, 0xa2,
	0x04, 0xbf, 0xd2, 0xec, 0xae, 0x8b, 0x5e, 0x3b, 0xb6, 0x1f, 0x68, 0xff, 0x54, 0x82, 0x39, 0xc4,
	0xbf, 0x27, 0xc8, 0x35, 0x8b, 0xfc, 0x9f, 0x9f, 0x50, 0xaf, 0xa0, 0xbe, 0x1f, 0x5d, 0x7a, 0xd9,
	0x83, 0x9a, 0x6f, 0x79, 0xf6, 0x38, 0x60, 0xfa, 0xf2, 0x12, 0x2a, 0x11, 0x6f, 0xa7, 0x36, 0x37,
	0xb5, 0x77, 0xeb, 0x87, 0xd8, 0x0b, 0x15, 0x68, 0xf0, 0xc3, 0x6f, 0xf5, 0x10, 0x16, 0xfb, 0xe4,
	0xc4, 0x9c, 0x0c, 0x23, 0x47, 0x09, 0x3b, 0xb6, 0xe5, 0xeb, 0x1d, 0xdb, 0x3c, 0xef, 0x7d, 0x20,
	0x9d, 0x9e, 0xfa, 0x35, 0xce, 0xf2, 0x70, 0xa0, 0x35, 0x1c, 0xa8, 0x9d, 0x8d, 0x22, 0x2a, 0x37,
	0xc8, 0x0e, 0xb1, 0xe3, 0x33, 0x90, 0x78, 0xfb, 0x6b, 0x00, 0xd1, 0x9a, 0x32, 0xef, 0x91, 0x92,
	0x79, 0x8f, 0xb4, 0xef, 0x2a, 0x50, 0x39, 0xe2, 0xa6, 0x81, 0x2c, 0x2d, 0x94, 0x98, 0xb4, 0x88,
	0xb1, 0xed, 0xc2, 0xe5, 0x6c, 0xbb, 0x98, 0x64, 0xdb, 0x94, 0x2f, 0x31, 0xa7, 0x45, 0x78, 0x51,
	0x98, 0xf0, 0x9e, 0xa1, 0xd0, 0x8d, 0xf0, 0xb2, 0x64, 0xae, 0xb7, 0x9c, 0x7d, 0xa5, 0x7f, 0xa9,
	0x08, 0x35, 0xdc, 0xb3, 0x5d, 0xa4, 0xe2, 0xe7, 0x36, 0x51, 0xf2, 0x34, 0x5c, 0xc9, 0x41, 0x5f,
	0x8e, 0x3b, 0xe8, 0x2f, 0xd7, 0x2a, 0x62, 0xee, 0xfb, 0xe9, 0xb8, 0xfb, 0x5e, 0x7d, 0x1f, 0xaa,
	0xa1, 0x79, 0xc6, 0x5d, 0x33, 0x2b, 0x29, 0x4a, 0x10, 0x87, 0xa1, 0x57, 0x84, 0xc5, 0x96, 0xe6,
	0x22, 0xb5, 0xe7, 0xe0, 0x22, 0xe2, 0x1a, 0xd7, 0xa3, 0x6b, 0xbc, 0x0a, 0xd5, 0xcf, 0x5c, 0xdb,
	0x31, 0x02, 0x7b, 0x44, 0x50, 0x54, 0x14, 0xf5, 0x0a, 0x05, 0xf4, 0xec, 0x11, 0x51, 0xef, 0x40,
	0x73, 0x68, 0xfa, 0x81, 0xc1, 0x94, 0x0b, 0xd6, 0xa6, 0x81, 0x6d, 0x1a, 0x14, 0xde, 0x41, 0x30,
	0x6d, 0xa9, 0xfd, 0xbe, 0x02, 0x53, 0x5b, 0x4e, 0x60, 0x07, 0x17, 0xa9, 0x63, 0x78, 0x47, 0x3a,
	0x86, 0x46, 0x86, 0xe1, 0xc2, 0xba, 0xad, 0xa3, 0x65, 0x86, 0x2d, 0xb5, 0xc7, 0x50, 0xc2, 0xab,
	0xbd, 0x00, 0xcd, 0xde, 0xa7, 0x07, 0x5b, 0xc6, 0xd1, 0xde, 0xe1, 0xc1, 0xd6, 0x46, 0x77, 0xbb,
	0xbb, 0xb5, 0xd9, 0x7c, 0x45, 0x9d, 0x81, 0x2a, 0x42, 0xf5, 0xfd, 0x9d, 0xad, 0xa6, 0xa2, 0x2e,
	0xc3, 0x3c, 0x16, 0x1f, 0xea, 0xfb, 0x47, 0x07, 0x46, 0x6f, 0x6b, 0xf7, 0x60, 0xa7, 0xd3, 0xdb,
	0x6a, 0x16, 0xd4, 0x26, 0xd4, 0xb1, 0xe2, 0xa0, 0xa3, 0xf7, 0xf6, 0xb6, 0xf4, 0x66, 0x51, 0xfb,
	0x16, 0xd4, 0x76, 0xc8, 0x19, 0x19, 0x6e, 0xb8, 0xce, 0x89, 0x8d, 0xd6, 0xef, 0x90, 0x16, 0x39,
	0xae, 0xac, 0x40, 0xa1, 0x67, 0xe6, 0x70, 0x22, 0xc8, 0x86, 0x15, 0xa8, 0x9e, 0x7f, 0x4c, 0x1c,
	0x72, 0x62, 0x07, 0x06, 0x72, 0x38, 0x46, 0x3f, 0x35, 0x0e, 0xdb, 0x24, 0xbe, 0xa5, 0xfd, 0xbc,
	0x02, 0x4d, 0x1c, 0xfe, 0x68, 0xbc, 0xe1, 0x3a, 0x7d, 0x1b, 0xdd, 0x61, 0x19, 0xa1, 0x2a, 0x54,
	0x35, 0x19, 0xd1, 0xe0, 0x77, 0xa4, 0x6b, 0x4d, 0x67, 0xe9, 0x5a, 0x15, 0x89, 0x97, 0xbe, 0x0a,
	0xf5, 0x81, 0xe7, 0x3e, 0x0d, 0x4e, 0x0d, 0x86, 0x22, 0x8f, 0x3c, 0x30, 0xd8, 0x63, 0x0a, 0xd2,
	0xee, 0x41, 0x95, 0x2a, 0x81, 0x3d, 0xaa, 0xc7, 0x5d, 0xcb, 0xeb, 0xfa, 0x8f, 0x0a, 0x54, 0x68,
	0x8f, 0x84, 0x3c, 0xc9, 0x8f, 0xac, 0x89, 0x25, 0x14, 0xa5, 0x25, 0x34, 0xa1, 0x78, 0x3c, 0x18,
	0xf1, 0xfb, 0x43, 0x3f, 0xe9, 0x48, 0xc7, 0x03, 0x7e, 0x6f, 0x0a, 0xc7, 0x03, 0x7a, 0x99, 0xb8,
	0xc9, 0xc3, 0xa5, 0xac, 0x28, 0xc6, 0xb4, 0xd0, 0xe9, 0xb8, 0x16, 0x7a, 0x13, 0x00, 0x63, 0x7f,
	0x86, 0xb4, 0x13, 0x55, 0x84, 0x6c, 0x72, 0xd5, 0xd3, 0x9f, 0x1c, 0xb3, 0xbd, 0x6b, 0x32, 0x86,
	0x24, 0xca, 0xda, 0x63, 0xa8, 0xec, 0x4f, 0x02, 0xba, 0x30, 0x3f, 0x36, 0x83, 0x12, 0x9f, 0xe1,
	0x1e, 0x94, 0x07, 0xb4, 0x4d, 0xab, 0x80, 0xd7, 0x29, 0x7d, 0x15, 0xc5, 0xd6, 0xe8, 0xac, 0x9d,
	0xf6, 0xb7, 0x0a, 0x2c, 0xa1, 0xef, 0x85, 0x84, 0xae, 0x5b, 0x11, 0xfc, 0xed, 0xc2, 0xf4, 0x98,
	0x41, 0x70, 0x96, 0xda, 0xfd, 0x7b, 0xd9, 0x5e, 0x9b, 0x54, 0xcf, 0x75, 0x51, 0x14, 0xfd, 0xdb,
	0x4f, 0x60, 0x5a, 0x58, 0xd0, 0x91, 0x4e, 0xaa, 0xc4, 0x74, 0x52, 0x89, 0x4f, 0x17, 0x62, 0x7c,
	0x3a, 0x8b, 0xc5, 0xc5, 0x5c, 0x0c, 0xa5, 0xb8, 0x8b, 0x41, 0xfb, 0x5d, 0x05, 0x96, 0x53, 0x88,
	0xf9, 0x63, 0xd7, 0xf1, 0x89, 0xfa, 0x01, 0xd4, 0x68, 0x1f, 0x61, 0xde, 0x2a, 0x39, 0x77, 0xb8,
	0x13, 0xd9, 0xbb, 0x3a, 0xd0, 0x0e, 0xdc, 0xf6, 0xdd, 0x86, 0x66, 0x38, 0xaf, 0x18, 0xa3, 0x70,
	0x8d, 0x31, 0x1a, 0x02, 0x39, 0x56, 0xd6, 0xfe, 0x53, 0x81, 0xa5, 0xa3, 0x71, 0xff, 0x39, 0x77,
	0x3d, 0xbb, 0x67, 0x7a, 0xd7, 0x7f, 0x4d, 0x89, 0xb6, 0xfd, 0x85, 0x3d, 0xff, 0x29, 0xc7, 0x4d,
	0xe9, 0x59, 0x1d, 0x37, 0x78, 0x30, 0x29, 0xdc, 0x7f, 0xb4, 0x0e, 0x66, 0x1b, 0x6e, 0x1c, 0x3e,
	0xb5, 0x03, 0xeb, 0x34, 0xc4, 0xf0, 0x10, 0x63, 0x15, 0xe2, 0x74, 0xb2, 0xbc, 0x69, 0x2c, 0xb4,
	0x51, 0x90, 0x43, 0x1b, 0xda, 0x1a, 0xdc, 0xcc, 0x19, 0x87, 0xad, 0x57, 0xdb, 0x82, 0xb6, 0x4e,
	0x8e, 0x6d, 0xa7, 0x1f, 0x6d, 0x05, 0x15, 0x90, 0x39, 0xd3, 0xe4, 0x5d, 0x0e, 0xed, 0x26, 0xac,
	0x66, 0x0e, 0xc3, 0x67, 0xf9, 0x10, 0x6e, 0xe9, 0x64, 0xec, 0x7a, 0x41, 0x58, 0x2d, 0x9c, 0x4f,
	0x62, 0xa6, 0x9b, 0x00, 0x9c, 0x5c, 0x22, 0x6e, 0x52, 0xe5, 0x90, 0x6e, 0x5f, 0x7b, 0x15, 0xd6,
	0x72, 0x07, 0xe0, 0x73, 0xdc, 0x81, 0xa5, 0x4d, 0x32, 0x24, 0x19, 0xa4, 0x9c, 0x58, 0x85, 0xb6,
	0x02, 0xcb, 0xa9, 0x96, 0x7c, 0x90, 0x8f, 0x61, 0xfe, 0x21, 0x09, 0x52, 0x23, 0x5c, 0xa9, 0xbc,
	0x49, 0x0a, 0x11, 0x2a, 0x6f, 0xe8, 0x01, 0xdd, 0x81, 0x85, 0xf8, 0x60, 0x9c, 0xc6, 0xde, 0x4d,
	0x5e, 0xad, 0x76, 0x7e, 0xfc, 0x2a, 0xbc, 0x45, 0xda, 0xa2, 0x40, 0x8d, 0x04, 0x54, 0xe7, 0xe0,
	0xa8, 0x69, 0xbb, 0x62, 0x12, 0x01, 0xe6, 0x93, 0xbc, 0x27, 0xab, 0xc6, 0x0a, 0xde, 0x91, 0x56,
	0x96, 0x33, 0x35, 0xae, 0x18, 0x6b, 0x6f, 0x41, 0x13, 0x87, 0x93, 0xa9, 0x20, 0x6f, 0xf5, 0xda,
	0x2f, 0x2b, 0x30, 0x27, 0xb5, 0xe6, 0x33, 0xbf, 0xc5, 0xfd, 0x8e, 0x6c, 0x6d, 0xcb, 0x39, 0x9a,
	0x14, 0x77, 0xdc, 0xa6, 0x03, 0x4a, 0x85, 0xe7, 0x0b, 0x28, 0x69, 0x7f, 0x50, 0x84, 0x79, 0xc1,
	0x6e, 0x65, 0xec, 0x3f, 0x88, 0xa1, 0xf3, 0x66, 0xae, 0xec, 0x90, 0xfa, 0xc8, 0xae, 0xd0, 0xef,
	0x15, 0xb8, 0x2b, 0x94, 0xeb, 0x78, 0x4a, 0xda, 0x54, 0x2b, 0x64, 0xb9, 0x39, 0x8b, 0xd9, 0x6e,
	0xce, 0x52, 0xb6, 0x5f, 0xfe, 0x19, 0x9d, 0x6b, 0x3c, 0x2a, 0x2e, 0x44, 0x7b, 0x55, 0x07, 0x01,
	0xea, 0xf6, 0x2f, 0xf5, 0x1c, 0x45, 0xa1, 0xa5, 0x6a, 0x2c, 0xb4, 0x24, 0x3c, 0x6d, 0x20, 0x79,
	0xda, 0x32, 0xc3, 0x2b, 0xb5, 0xec, 0xf0, 0x4a, 0x66, 0x74, 0xa4, 0x9e, 0x19, 0x1d, 0xd1, 0x5e,
	0x87, 0x85, 0xf8, 0x9e, 0x73, 0xba, 0x49, 0x5e, 0xd3, 0x3f, 0x2e, 0xc2, 0xbc, 0x60, 0xd3, 0xcf,
	0x72, 0xa0, 0x19, 0x7d, 0xe4, 0x03, 0xfd, 0x97, 0x42, 0x8e, 0x6f, 0x9b, 0x1f, 0x70, 0x21, 0x7d,
	0xc0, 0x2f, 0x3b, 0xf0, 0x12, 0x3b, 0xe0, 0xe9, 0x2b, 0x0e, 0xb8, 0x72, 0xe9, 0x01, 0x57, 0x73,
	0x0f, 0x18, 0x32, 0x0f, 0xb8, 0x76, 0xd5, 0x01, 0xd7, 0x9f, 0xe1, 0x80, 0x67, 0xb2, 0x0f, 0x78,
	0x09, 0x16, 0xe2, 0x67, 0xc0, 0x99, 0xeb, 0x6b, 0x30, 0x2f, 0xf8, 0xee, 0x25, 0x42, 0x86, 0x76,
	0x8f, 0x37, 0xe3, 0xdd, 0xdf, 0x82, 0xe6, 0xe1, 0xa9, 0xe9, 0x91, 0x6b, 0xb1, 0xa6, 0x07, 0x30,
	0x27, 0x35, 0xe6, 0x14, 0xd6, 0x84, 0xe2, 0x13, 0x72, 0xc1, 0xf5, 0x70, 0xfa, 0x49, 0xf9, 0x37,
	0x39, 0x1f, 0xdb, 0x1e, 0x31, 0x4c, 0x26, 0x3a, 0x8b, 0x7a, 0x85, 0x01, 0x3a, 0x81, 0xf6, 0x2a,
	0xcc, 0xee, 0xd8, 0x4f, 0xc8, 0x65, 0xb8, 0x7e, 0x05, 0x9a, 0x51, 0x13, 0x3e, 0x4b, 0xdc, 0xe5,
	0xa3, 0x24, 0x5c, 0x3e, 0x5a, 0x1b, 0x5a, 0x0f, 0x49, 0x40, 0x4d, 0x51, 0xb6, 0x3e, 0x99, 0x99,
	0x7f, 0x04, 0x2b, 0x19, 0x75, 0x7c, 0xdc, 0xb7, 0xa1, 0xcc, 0x38, 0x24, 0xe3, 0xe6, 0xb9, 0x8c,
	0x95, 0xb5, 0xd2, 0x6e, 0x40, 0xfb, 0x21, 0x09, 0x58, 0x98, 0x23, 0x3d, 0xd3, 0x1e, 0xac, 0x66,
	0xd6, 0xf2, 0xb9, 0xee, 0x41, 0x49, 0x12, 0x1c, 0xab, 0xa9, 0xa9, 0xa2, 0xe8, 0x89, 0x8e, 0x0d,
	0xb5, 0x3f, 0x2c, 0xe0, 0xb2, 0x74, 0x6b, 0xd4, 0x4f, 0x4d, 0xa6, 0xde, 0xe2, 0x5e, 0x8c, 0xc0,
	0x1c, 0x44, 0x09, 0x9c, 0x28, 0x9e, 0x7a, 0xe6, 0x80, 0x39, 0xcc, 0x07, 0x24, 0x60, 0x51, 0x5e,
	0x26, 0x44, 0xa7, 0x07, 0x24, 0xc0, 0x18, 0xef, 0x97, 0xa0, 0xe1, 0xb8, 0xc6, 0xb1, 0xe7, 0x3e,
	0xf5, 0xb9, 0x2c, 0x63, 0xc9, 0x8b, 0x75, 0xc7, 0x7d, 0x80, 0x40, 0xb4, 0xbf, 0x37, 0xe3, 0x39,
	0x94, 0xeb, 0x69, 0x73, 0x23, 0x07, 0x35, 0x39, 0x9b, 0x52, 0xfd, 0x06, 0xd4, 0x03, 0x77, 0x6c,
	0x10, 0x6a, 0x38, 0xdb, 0x84, 0xf9, 0x47, 0xb2, 0xf6, 0x99, 0x59, 0xd6, 0x7a, 0x2d, 0x70, 0xc7,
	0x5b, 0xbc, 0x6d, 0x7b, 0x3d, 0x27, 0x87, 0x72, 0x29, 0xcc, 0x97, 0x2c, 0xe0, 0xcd, 0xe3, 0x25,
	0xed, 0x8f, 0x14, 0x3c, 0xea, 0x24, 0x52, 0x7c, 0xfb, 0xdf, 0x4f, 0x0b, 0xef, 0xb4, 0x09, 0x25,
	0x82, 0x3d, 0x92, 0x5b, 0x6b, 0x0d, 0x6a, 0x43, 0xd7, 0xec, 0x1b, 0x27, 0xb6, 0x63, 0xfb, 0xa7,
	0x5c, 0x17, 0x04, 0x0a, 0xda, 0x46, 0x88, 0xfa, 0x01, 0x54, 0x47, 0xf6, 0xb9, 0xd1, 0x37, 0x03,
	0x93, 0xa5, 0x81, 0x66, 0x85, 0x58, 0xe9, 0x50, 0xbb, 0xf6, 0xf9, 0xa6, 0x19, 0x98, 0xdd, 0x80,
	0x8c, 0xf4, 0xca, 0x88, 0x15, 0x7c, 0xed, 0x77, 0x14, 0x7a, 0x25, 0x62, 0xb5, 0xa1, 0x3b, 0x48,
	0x91, 0xdc, 0x41, 0x02, 0x7f, 0x74, 0x1d, 0x16, 0x72, 0xbc, 0x31, 0x71, 0xfc, 0xd1, 0x4e, 0xfe,
	0x50, 0xf8, 0x24, 0xb1, 0x63, 0x5e, 0x08, 0x18, 0x9d, 0x53, 0x9b, 0x24, 0x30, 0xed, 0x21, 0x2a,
	0x02, 0xcc, 0x6b, 0x49, 0x3f, 0xb5, 0x7f, 0x2e, 0xc0, 0x6c, 0xa2, 0xfa, 0x5a, 0x26, 0x47, 0x8c,
	0x23, 0x17, 0x93, 0x1c, 0x59, 0x30, 0xcf, 0x52, 0x66, 0x1c, 0xaa, 0x9c, 0x8c, 0xdf, 0x26, 0x5c,
	0xde, 0x19, 0x1e, 0xd9, 0xe9, 0x4c, 0x8f, 0xec, 0x1b, 0x30, 0x7b, 0xea, 0x8e, 0x88, 0xdc, 0x90,
	0x31, 0xff, 0x06, 0x05, 0x67, 0xb9, 0x6e, 0xd1, 0x61, 0xc0, 0x3d, 0xe3, 0x6c, 0x13, 0x2c, 0xd7,
	0x89, 0x79, 0xab, 0x21, 0xee, 0xad, 0x4e, 0x84, 0x6a, 0x6a, 0xa9, 0x50, 0x4d, 0x2c, 0xd0, 0x53,
	0x8f, 0x07, 0x7a, 0xb4, 0x5b, 0x70, 0x23, 0x54, 0xf7, 0x44, 0x66, 0x9e, 0xcc, 0x54, 0x06, 0x70,
	0x33, 0xa7, 0x9e, 0xd3, 0xf5, 0x36, 0x34, 0x22, 0xd1, 0xe6, 0x9c, 0xb8, 0x82, 0x97, 0xad, 0x5d,
	0x91, 0x45, 0xac, 0xcf, 0x84, 0xe2, 0x8f, 0xf6, 0xd2, 0xb6, 0xa1, 0xf5, 0xc8, 0x74, 0xfa, 0x2c,
	0x9b, 0x65, 0xc3, 0x1d, 0x8d, 0x4c, 0xa7, 0x2f, 0x98, 0x4d, 0x13, 0x8a, 0xd6, 0x48, 0x5c, 0x41,
	0xfa, 0xa9, 0xb6, 0xa8, 0xbe, 0x7d, 0x41, 0x6f, 0x01, 0x9e, 0x79, 0x5d, 0x17, 0x45, 0xed, 0x08,
	0x56, 0x32, 0xc6, 0xe1, 0xc8, 0xaa, 0x50, 0xb2, 0x28, 0x47, 0xe2, 0x84, 0x4d, 0xbf, 0xe9, 0xe0,
	0x23, 0x7f, 0xc0, 0x49, 0x87, 0x7e, 0xa2, 0x3f, 0xc9, 0x0c, 0x4c, 0x24, 0x9a, 0xba, 0x8e, 0xdf,
	0x54, 0x82, 0x3d, 0x24, 0x41, 0x94, 0xa9, 0x10, 0xd7, 0xd5, 0x65, 0xb8, 0xd0, 0xd5, 0x13, 0x06,
	0xc1, 0xea, 0x25, 0x69, 0x0f, 0x91, 0x45, 0xb0, 0x0d, 0x73, 0x87, 0xc4, 0xf4, 0xac, 0x53, 0x59,
	0x42, 0xb5, 0x60, 0xda, 0x72, 0x9d, 0x80, 0x9a, 0x82, 0x6c, 0x0b, 0x44, 0x91, 0xca, 0x4a, 0xf4,
	0x3a, 0x72, 0xe3, 0x0d, 0x79, 0x91, 0x1f, 0x74, 0xfb, 0xda, 0x9f, 0x2a, 0xa0, 0xca, 0x03, 0x45,
	0x7a, 0x3c, 0xae, 0xec, 0x0a, 0x71, 0x83, 0x8d, 0x72, 0x07, 0x4f, 0xb2, 0xa4, 0x62, 0x8a, 0x25,
	0x7d, 0x28, 0xb3, 0x24, 0xc6, 0xbf, 0xb5, 0xd4, 0x5c, 0x0c, 0xbd, 0x5c, 0xa6, 0x34, 0x97, 0xaa,
	0xcf, 0x64, 0x4b, 0xef, 0xa6, 0xd9, 0x52, 0xee, 0xb2, 0x5e, 0x22, 0x53, 0xfa, 0xcd, 0x02, 0xdc,
	0x60, 0x1a, 0x6f, 0x22, 0xa4, 0x2c, 0xce, 0xec, 0x13, 0x28, 0x0d, 0x28, 0x47, 0x62, 0x87, 0xff,
	0x41, 0x8e, 0x89, 0x92, 0xdd, 0x79, 0x3d, 0x09, 0xc6, 0xa1, 0xda, 0xbf, 0x97, 0x11, 0xc0, 0xce,
	0xb5, 0x62, 0x65, 0x47, 0x5e, 0x21, 0x27, 0x60, 0x5d, 0xcc, 0x72, 0xa2, 0x96, 0x72, 0x02, 0xd6,
	0xe5, 0xbc, 0x80, 0xb5, 0x9c, 0xf1, 0xae, 0x7d, 0xbb, 0x00, 0x37, 0x73, 0x96, 0x96, 0x32, 0x09,
	0x98, 0x34, 0xfd, 0x08, 0x54, 0x44, 0xc0, 0x88, 0x25, 0x87, 0x5c, 0xc7, 0xc1, 0xd2, 0xc4, 0x7e,
	0x12, 0x44, 0x7d, 0x04, 0x73, 0x14, 0xef, 0xf8, 0x50, 0xc5, 0x6b, 0x0c, 0x35, 0x4b, 0xbb, 0xc9,
	0x23, 0x1d, 0xc0, 0x62, 0x2c, 0x9b, 0x21, 0x1c, 0xad, 0x74, 0x8d, 0xd1, 0xe6, 0xe5, 0x94, 0x07,
	0x0e, 0xd4, 0x7e, 0xb5, 0x00, 0x37, 0x98, 0x0a, 0xfd, 0x9c, 0x14, 0x73, 0x59, 0xe7, 0x1c, 0x8a,
	0xf9, 0xad, 0x6b, 0xa4, 0x3c, 0xfc, 0x90, 0x08, 0xe5, 0x17, 0x0b, 0x70, 0x33, 0x67, 0x45, 0x9c,
	0x50, 0xb2, 0x09, 0x43, 0x79, 0x79, 0x84, 0x51, 0x78, 0xa9, 0x84, 0x51, 0x7c, 0x5e, 0xc2, 0x58,
	0x87, 0x1b, 0xcc, 0x36, 0xca, 0xa1, 0x8b, 0xc4, 0x81, 0x69, 0x6b, 0x70, 0x33, 0xa7, 0x3d, 0x37,
	0xaa, 0xd6, 0x50, 0x64, 0x27, 0x6a, 0x65, 0x99, 0xfe, 0x18, 0x6e, 0xe5, 0x35, 0x08, 0xdd, 0x59,
	0xb2, 0xad, 0x90, 0xe6, 0x8c, 0x29, 0x72, 0x43, 0x83, 0x81, 0x4d, 0x9c, 0xc8, 0x41, 0x4b, 0x4f,
	0x9c, 0xd9, 0xe0, 0x9a, 0x13, 0x27, 0xfa, 0xf2, 0x89, 0xfb, 0x50, 0xa7, 0x15, 0x22, 0x66, 0x7d,
	0xad, 0xd8, 0xcc, 0x3b, 0x50, 0x1a, 0xf9, 0x03, 0xa1, 0x31, 0xa7, 0xcf, 0x4d, 0x0c, 0xb6, 0xeb,
	0x0f, 0x74, 0x6c, 0xa9, 0xfd, 0x85, 0x02, 0x35, 0x09, 0x7a, 0x89, 0x58, 0x7e, 0x04, 0x35, 0x9f,
	0x38, 0x7d, 0xe2, 0x19, 0x52, 0x38, 0xef, 0x8d, 0xcb, 0xa6, 0x58, 0x3f, 0xc4, 0xf6, 0x18, 0xd9,
	0x03, 0x3f, 0xfc, 0xd6, 0x0e, 0x00, 0xa2, 0x1a, 0xb5, 0x0d, 0x4b, 0x51, 0xc9, 0x38, 0x72, 0xfc,
	0x31, 0xb1, 0xec, 0x13, 0x9b, 0xf4, 0x9b, 0xaf, 0xa8, 0x73, 0x30, 0x23, 0xd5, 0x75, 0xba, 0x4d,
	0x45, 0x9d, 0x87, 0x59, 0xb9, 0xb9, 0x4f, 0xbc, 0x66, 0x41, 0xfb, 0x31, 0x58, 0xa6, 0x56, 0xa2,
	0xed, 0xf4, 0xe5, 0x2d, 0xf3, 0x75, 0xf2, 0x39, 0x95, 0xeb, 0x68, 0x28, 0x5d, 0x18, 0x92, 0x98,
	0x05, 0x06, 0xc2, 0xf9, 0xa9, 0x69, 0xcd, 0x1a, 0x44, 0x29, 0x43, 0x0c, 0xd0, 0xed, 0x6b, 0x3f,
	0x8d, 0xd6, 0x62, 0xc6, 0xc0, 0xfe, 0x58, 0xdd, 0x04, 0x54, 0x8c, 0x0d, 0xa1, 0x95, 0x0a, 0x25,
	0xf1, 0x66, 0xe6, 0x01, 0x8b, 0xbe, 0x98, 0xac, 0x1f, 0x8d, 0xa4, 0xbd, 0x07, 0x2a, 0x93, 0x2d,
	0x2c, 0xfc, 0xcf, 0xef, 0x47, 0x42, 0xff, 0x55, 0x92, 0xfa, 0xaf, 0xf6, 0x9a, 0x70, 0x22, 0xf2,
	0x6e, 0x39, 0xbe, 0xa9, 0x2f, 0x81, 0xca, 0xee, 0x55, 0x6c, 0xf4, 0x64, 0xab, 0x45, 0xe1, 0xf0,
	0x88, 0x0d, 0xa6, 0x6d, 0xa3, 0x27, 0x37, 0x4c, 0xb6, 0xca, 0x73, 0xb6, 0x27, 0x70, 0x2d, 0xa4,
	0x70, 0xdd, 0x46, 0x75, 0x52, 0x1a, 0x87, 0x23, 0xbb, 0x0e, 0x25, 0x54, 0x55, 0xf2, 0x9c, 0xcb,
	0x51, 0x0f, 0x6c, 0xa7, 0xbd, 0x8d, 0xbe, 0x80, 0x54, 0x26, 0x47, 0xde, 0xaa, 0x1e, 0xa3, 0x15,
	0x90, 0xd1, 0x3c, 0x34, 0x5e, 0xe5, 0xe9, 0xb5, 0xab, 0x53, 0x46, 0x38, 0x1a, 0x8b, 0xd1, 0xb6,
	0xc8, 0x7c, 0xe0, 0x6f, 0x94, 0x68, 0x99, 0xb1, 0xeb, 0xff, 0x61, 0xec, 0xfa, 0xbf, 0x95, 0x65,
	0xf3, 0xa7, 0x3a, 0xf1, 0x64, 0x10, 0xec, 0xd8, 0x0e, 0xa0, 0x8c, 0xc5, 0x97, 0x9c, 0xb6, 0x14,
	0x4f, 0xac, 0xa9, 0x26, 0x12, 0x6b, 0xb4, 0xf7, 0xd1, 0xf0, 0x97, 0x12, 0x2c, 0x64, 0x4f, 0xc9,
	0x0a, 0x54, 0xb8, 0xae, 0x19, 0x86, 0x54, 0x99, 0x1e, 0xd9, 0xd7, 0xbe, 0xa3, 0xa0, 0x43, 0x27,
	0xd5, 0x91, 0xef, 0xc6, 0x3b, 0xb1, 0xdd, 0xb8, 0x91, 0xbd, 0xeb, 0xac, 0x1f, 0x5b, 0x7e, 0x7e,
	0x46, 0x4e, 0xe1, 0xf9, 0x33, 0x72, 0xb4, 0x01, 0xcc, 0x76, 0x82, 0xc0, 0x0e, 0x26, 0xfd, 0x50,
	0x26, 0xd1, 0x7d, 0xb2, 0xf0, 0xd5, 0x0b, 0xd7, 0x3a, 0x59, 0x09, 0x4d, 0xcd, 0xe3, 0xcf, 0x88,
	0x25, 0xbf, 0xdb, 0x65, 0x00, 0x66, 0x36, 0xf0, 0x4a, 0xf9, 0xdd, 0x2e, 0x03, 0xe1, 0x36, 0xaa,
	0xd0, 0x8c, 0x26, 0xe2, 0x17, 0x4b, 0x07, 0xf5, 0x91, 0xd9, 0x4f, 0xce, 0x1f, 0x9b, 0x47, 0xb9,
	0x7c, 0x9e, 0x42, 0x6a, 0x9e, 0xaf, 0xc3, 0x7c, 0x6c, 0x4c, 0xbe, 0xdd, 0xaf, 0x42, 0xfd, 0xd4,
	0xec, 0x1b, 0x26, 0x87, 0xe3, 0xb8, 0x15, 0xbd, 0x76, 0x1a, 0x35, 0xd5, 0x74, 0xe6, 0x11, 0x23,
	0x66, 0xff, 0x11, 0x31, 0xbd, 0x60, 0xc3, 0x9d, 0x44, 0xf1, 0xbb, 0x5c, 0x55, 0x3c, 0x1e, 0x07,
	0x2b, 0x24, 0xe3, 0x60, 0x5b, 0xcc, 0x6b, 0x94, 0x18, 0x93, 0xe3, 0x74, 0x07, 0x9a, 0x1e, 0x31,
	0xfb, 0xc6, 0x29, 0xad, 0x32, 0x58, 0x86, 0x0d, 0x1b, 0xbd, 0xe1, 0xc5, 0x7a, 0x68, 0x3f, 0x0b,
	0xcd, 0x70, 0x0c, 0x81, 0xd2, 0x22, 0x4c, 0x8d, 0x7c, 0xc9, 0x3f, 0x57, 0x1e, 0xf9, 0x83, 0x6e,
	0x7e, 0xc8, 0x2f, 0x81, 0x69, 0x31, 0x81, 0xa9, 0x2c, 0xf0, 0x4a, 0x31, 0x81, 0xa7, 0x11, 0x98,
	0x93, 0x26, 0x7f, 0x56, 0xdc, 0xd5, 0xd7, 0x61, 0x56, 0x6a, 0x89, 0x19, 0xbe, 0x05, 0x9e, 0xf2,
	0x24, 0x1a, 0xf6, 0xc8, 0x79, 0xa0, 0xbd, 0x0d, 0xea, 0x03, 0x33, 0xb0, 0x4e, 0x1f, 0x92, 0x80,
	0x8a, 0xe5, 0x68, 0xe3, 0xd9, 0x2a, 0x99, 0x54, 0xa9, 0xea, 0x53, 0xb8, 0x4c, 0x5f, 0xfb, 0x49,
	0x98, 0x09, 0xb1, 0x42, 0xb3, 0x2f, 0x67, 0x3f, 0xd6, 0xa0, 0x66, 0xfb, 0x06, 0x71, 0x02, 0xcf,
	0x74, 0xf8, 0xe3, 0x85, 0x8a, 0x0e, 0xb6, 0xbf, 0xc5, 0x21, 0x61, 0xda, 0x71, 0x31, 0x4a, 0x3b,
	0xd6, 0x0c, 0x98, 0x8f, 0xe1, 0xc2, 0x17, 0xfd, 0x28, 0xb6, 0x68, 0xd9, 0x21, 0x72, 0x2b, 0x23,
	0xfc, 0x25, 0x21, 0x27, 0x6d, 0x8a, 0x70, 0x88, 0x50, 0xde, 0xb9, 0x75, 0x1e, 0x78, 0xa6, 0xcc,
	0xba, 0xef, 0xc1, 0x14, 0x13, 0xb9, 0xb9, 0xc1, 0x38, 0xee, 0xcb, 0xe4, 0xcd, 0xb4, 0x3f, 0x29,
	0x40, 0x63, 0x23, 0xee, 0x95, 0x4a, 0xea, 0x47, 0xaf, 0x41, 0x43, 0x4a, 0x80, 0x9b, 0x78, 0x43,
	0xb1, 0xfd, 0x11, 0xf4, 0xc8, 0x1b, 0xaa, 0x1a, 0xcc, 0xb0, 0x97, 0x67, 0x74, 0x81, 0xf6, 0x68,
	0xc0, 0x2d, 0xff, 0x1a, 0xbe, 0x3e, 0x23, 0x66, 0xbf, 0x3b, 0x1a, 0x64, 0xe6, 0x9b, 0x7f, 0x53,
	0x7e, 0x27, 0xd0, 0xc8, 0x79, 0xb6, 0x10, 0xa1, 0xb7, 0x8e, 0x4e, 0x75, 0x11, 0x86, 0x79, 0x13,
	0x9a, 0x13, 0x67, 0xe8, 0x5a, 0x4f, 0x0c, 0x4b, 0xa4, 0x0a, 0xf1, 0xc8, 0xcb, 0x2c, 0x83, 0x87,
	0x19, 0x44, 0xda, 0x27, 0x50, 0x66, 0x29, 0x98, 0x8b, 0x30, 0x77, 0xd8, 0xeb, 0xf4, 0x92, 0xe9,
	0x50, 0x4d, 0xa8, 0x0b, 0xf0, 0xce, 0xfe, 0xc6, 0xc7, 0x4d, 0x45, 0x6d, 0x00, 0x30, 0x08, 0x96,
	0x0b, 0xea, 0x2c, 0xd4, 0x78, 0x8b, 0xc3, 0xee, 0xde, 0xc3, 0x66, 0x51, 0xfb, 0x29, 0x14, 0x54,
	0xd2, 0x21, 0x48, 0x5e, 0xaf, 0x89, 0x27, 0xbb, 0xf5, 0x94, 0x9c, 0xe7, 0x7f, 0xf1, 0xb5, 0xe9,
	0x33, 0xd6, 0xc4, 0x8b, 0x8a, 0xda, 0x3e, 0x0a, 0xde, 0x78, 0x1b, 0x59, 0x78, 0x3c, 0xf3, 0x69,
	0xf7, 0x42, 0x1d, 0x3c, 0x39, 0x20, 0xc7, 0xfc, 0xab, 0x31, 0xa1, 0x72, 0x25, 0xbe, 0x4c, 0xc1,
	0xf6, 0x61, 0x95, 0xe5, 0x1c, 0x24, 0x6a, 0x9f, 0x13, 0x4b, 0xf5, 0xff, 0x80, 0x44, 0x5a, 0x91,
	0x83, 0xa9, 0x1e, 0x01, 0xbb, 0xe8, 0x9a, 0xcc, 0x9e, 0x94, 0x8b, 0x86, 0xef, 0x14, 0xa0, 0x22,
	0x22, 0xc8, 0xd7, 0x52, 0xf9, 0xf1, 0x21, 0x24, 0x92, 0x52, 0x60, 0x8f, 0x85, 0x4b, 0x98, 0x41,
	0x7a, 0xf6, 0x58, 0x7d, 0x4f, 0x0e, 0x03, 0x36, 0x32, 0xb6, 0x46, 0x4c, 0x16, 0x27, 0x50, 0x91,
	0xe4, 0x55, 0x96, 0x92, 0xbc, 0xde, 0x87, 0x65, 0xdb, 0x09, 0xec, 0x91, 0x69, 0x5d, 0x18, 0xf2,
	0x42, 0x47, 0x03, 0x4e, 0xbb, 0x8b, 0xa2, 0x3a, 0x5a, 0x57, 0x77, 0x34, 0xf8, 0x22, 0x28, 0xb8,
	0x0b, 0x4b, 0x28, 0x5e, 0x18, 0xea, 0x2f, 0x44, 0x5b, 0x8f, 0xd0, 0x74, 0x88, 0x0f, 0x15, 0x06,
	0xb2, 0x4a, 0x97, 0x07, 0x36, 0x44, 0x5c, 0x9f, 0xd1, 0x93, 0x0d, 0x8b, 0xec, 0x68, 0x43, 0xf8,
	0xf3, 0x52, 0xd2, 0x1a, 0xd4, 0xc2, 0x64, 0x83, 0x90, 0x8e, 0x40, 0x80, 0xba, 0x7d, 0xad, 0x05,
	0x4b, 0xc9, 0xa9, 0x38, 0xfd, 0xb0, 0x9d, 0xe9, 0xf2, 0x83, 0x78, 0x21, 0x1e, 0xfb, 0x97, 0x45,
	0xdc, 0x9a, 0xf8, 0x58, 0x7c, 0x6b, 0x56, 0xa1, 0x4a, 0x59, 0x85, 0x9c, 0x3f, 0x59, 0xb1, 0x26,
	0x1e, 0xe6, 0x3f, 0x8a, 0x4a, 0x39, 0x8d, 0x92, 0x56, 0x62, 0x82, 0x22, 0xd5, 0x1c, 0x47, 0x84,
	0x04, 0x46, 0xdf, 0xbc, 0x10, 0x29, 0xea, 0xb4, 0xbc, 0x69, 0x5e, 0xa8, 0x5f, 0x06, 0x35, 0x70,
	0xfb, 0xe6, 0x85, 0x11, 0x4b, 0x72, 0x64, 0x39, 0x81, 0x4d, 0xac, 0x79, 0x18, 0x65, 0x3a, 0xaa,
	0xbb, 0xd2, 0x33, 0xe5, 0x1a, 0xae, 0xe8, 0x2b, 0x59, 0xaa, 0x75, 0x16, 0xfa, 0xd1, 0xc9, 0x45,
	0x0f, 0x97, 0xdf, 0x85, 0x29, 0x5c, 0x8d, 0xcf, 0xf3, 0xf3, 0xd3, 0x9a, 0xa9, 0x94, 0x3b, 0xaa,
	0xf3, 0xb6, 0xea, 0x27, 0x30, 0x8f, 0x5f, 0xc6, 0x64, 0x1c, 0xb1, 0x72, 0xf6, 0xdf, 0x87, 0xac,
	0xdc, 0x90, 0x64, 0x7e, 0xa8, 0x3e, 0x37, 0x4c, 0x40, 0xfc, 0xf6, 0xde, 0x33, 0x32, 0x80, 0x5b,
	0x00, 0x12, 0xc7, 0xe6, 0x99, 0xd7, 0x11, 0x84, 0xc7, 0x57, 0x59, 0x66, 0xf8, 0xb6, 0x3d, 0x0c,
	0x88, 0x87, 0x7e, 0x69, 0x6e, 0xb5, 0xfc, 0x9b, 0x02, 0x10, 0x41, 0xd5, 0x6f, 0x48, 0xde, 0xe8,
	0xc6, 0xfd, 0xd7, 0x53, 0x0b, 0x88, 0x9a, 0xf2, 0xcf, 0x28, 0x6d, 0x97, 0x3d, 0x49, 0xf6, 0xc7,
	0x43, 0xf3, 0x42, 0xce, 0x11, 0xaf, 0x71, 0x18, 0xa6, 0x1a, 0x9e, 0x8b, 0xc9, 0xb8, 0xe5, 0xbd,
	0xbc, 0xdd, 0xdd, 0xe9, 0x6d, 0xe9, 0x46, 0x46, 0x9a, 0x6f, 0x1b, 0x96, 0xe4, 0xca, 0xc3, 0xad,
	0x1f, 0x37, 0xb6, 0xb7, 0x76, 0x3b, 0x98, 0xf3, 0xdb, 0x82, 0x85, 0x64, 0x1d, 0xd6, 0x14, 0xd4,
	0x65, 0x98, 0x4f, 0xd6, 0x74, 0x76, 0x76, 0x9a, 0x45, 0x1e, 0x47, 0x4e, 0xef, 0xc2, 0x35, 0xe3,
	0xc8, 0x52, 0x17, 0x76, 0xd9, 0x1d, 0x94, 0xa1, 0x6c, 0x3c, 0x99, 0xff, 0xfc, 0x7f, 0xa8, 0x9f,
	0x60, 0x5b, 0xc3, 0x0e, 0xc8, 0xc8, 0xbf, 0xce, 0x80, 0xb5, 0x93, 0xf0, 0x1b, 0xc3, 0x75, 0xd6,
	0xc4, 0xf3, 0xdd, 0xf0, 0x27, 0x07, 0xac, 0x44, 0x2f, 0xa3, 0xc8, 0xee, 0xa7, 0xe7, 0xf4, 0xe2,
	0xb1, 0xc2, 0x75, 0x98, 0x3f, 0x9e, 0x04, 0x81, 0xeb, 0x18, 0xe2, 0xd0, 0x50, 0x1b, 0x64, 0x2a,
	0xce, 0x1c, 0xab, 0xda, 0x64, 0x35, 0x3d, 0xf6, 0x03, 0x89, 0x3a, 0xfd, 0xb6, 0x9d, 0x01, 0x8b,
	0x2f, 0x94, 0xf1, 0xca, 0xa5, 0xfd, 0x3f, 0x11, 0x92, 0xeb, 0x07, 0xac, 0x3d, 0x7b, 0xa3, 0x3c,
	0x8e, 0x0a, 0x74, 0xee, 0x44, 0x00, 0x51, 0x92, 0x22, 0x73, 0xf1, 0x20, 0x22, 0xd7, 0xbf, 0x30,
	0xae, 0x39, 0x2d, 0xc5, 0x35, 0xaf, 0xfa, 0x09, 0x05, 0x25, 0x47, 0x7f, 0x62, 0x07, 0xf8, 0xc8,
	0x9c, 0x5a, 0xc9, 0xd5, 0xdb, 0xc5, 0x3b, 0x65, 0xbd, 0x26, 0x60, 0x87, 0xe4, 0xbc, 0x3d, 0x86,
	0x9a, 0x84, 0xa3, 0xda, 0x86, 0x0a, 0xcf, 0xf0, 0x17, 0x4a, 0x77, 0x58, 0x0e, 0xb5, 0xe5, 0x82,
	0xf4, 0x48, 0xef, 0x2b, 0xb0, 0x78, 0x62, 0x0e, 0x87, 0xb8, 0x0c, 0xfe, 0xf2, 0x81, 0xea, 0x99,
	0xcc, 0xfd, 0x56, 0xd5, 0x55, 0x5a, 0x49, 0x17, 0xc2, 0x9e, 0x40, 0x1c, 0x79, 0x43, 0x9f, 0xca,
	0x88, 0x04, 0xd9, 0x5c, 0x93, 0x00, 0xa3, 0x6d, 0xe5, 0x56, 0xf1, 0x1a, 0xd4, 0x1c, 0x72, 0x1e,
	0x18, 0x31, 0x6a, 0x01, 0x0a, 0xda, 0x60, 0x14, 0xb3, 0x8a, 0x26, 0x18, 0xeb, 0xf7, 0xc8, 0xe5,
	0x29, 0x14, 0xe2, 0xda, 0xff, 0x43, 0x51, 0xe2, 0x0a, 0x52, 0x6d, 0x64, 0x35, 0x1e, 0x23, 0xc4,
	0x60, 0xbe, 0x72, 0x85, 0xa7, 0xbb, 0x23, 0xac, 0x87, 0x1e, 0xf3, 0x8f, 0x38, 0xc2, 0xcc, 0x08,
	0x7f, 0x3f, 0x8b, 0xf5, 0xe6, 0x8c, 0xce, 0x93, 0x32, 0x98, 0x47, 0x05, 0x1d, 0x1c, 0x7f, 0x55,
	0x10, 0xef, 0x5c, 0x33, 0xb3, 0xd0, 0xaf, 0x72, 0x30, 0x51, 0x81, 0x13, 0xb8, 0x63, 0x43, 0xf6,
	0xeb, 0x57, 0x02, 0x77, 0xcc, 0x10, 0x5d, 0x85, 0xaa, 0x3f, 0x39, 0xe6, 0x95, 0xa5, 0x30, 0x4d,
	0x9c, 0x55, 0xc6, 0x8d, 0x04, 0x4a, 0x84, 0xe5, 0xa4, 0x91, 0x40, 0x09, 0x30, 0xe7, 0xb2, 0x4c,
	0xe5, 0x5d, 0x16, 0xba, 0x7f, 0xac, 0x3d, 0xfb, 0x3b, 0xc3, 0x34, 0xdf, 0x3f, 0x84, 0xb1, 0xbf,
	0x33, 0xac, 0x42, 0xf5, 0xd8, 0xf4, 0x62, 0x7f, 0x6f, 0xa8, 0x1c, 0x9b, 0x1e, 0xab, 0x14, 0xea,
	0x57, 0x55, 0x52, 0xbf, 0x92, 0xf4, 0x0c, 0x29, 0x7a, 0xd6, 0xfe, 0x55, 0x81, 0xe5, 0xc3, 0xc0,
	0xf4, 0xf8, 0xce, 0xef, 0x9a, 0xa8, 0x21, 0x30, 0xc6, 0xb4, 0x0d, 0xa5, 0x91, 0x88, 0x12, 0x37,
	0xee, 0xdf, 0x4f, 0x13, 0x58, 0x76, 0xbf, 0x75, 0x2c, 0xec, 0xba, 0x7d, 0xa2, 0x63, 0x7f, 0xdc,
	0x4e, 0xfe, 0xde, 0x2a, 0xf4, 0x86, 0xf0, 0xe7, 0x53, 0x7d, 0xcd, 0x84, 0x6a, 0xd8, 0x9e, 0x72,
	0xf0, 0xdd, 0x4e, 0x6f, 0xe3, 0x91, 0xb1, 0xbb, 0xbf, 0x99, 0xe4, 0xee, 0x37, 0x61, 0x45, 0xaa,
	0x63, 0x35, 0x9f, 0x1a, 0x87, 0x1b, 0x7a, 0xf7, 0xa0, 0xd7, 0x54, 0xd4, 0x15, 0x58, 0x94, 0xaa,
	0x3f, 0x39, 0xea, 0x6e, 0x7c, 0x6c, 0x1c, 0xec, 0x74, 0x3e, 0x6d, 0x16, 0xb4, 0xff, 0x52, 0xa0,
	0x95, 0xc6, 0x95, 0xd3, 0xed, 0x26, 0x94, 0x27, 0x3e, 0xf1, 0x04, 0xdb, 0x5d, 0xbf, 0xc6, 0x2a,
	0x39, 0x4d, 0xe2, 0x93, 0x1b, 0xd6, 0x19, 0xdf, 0x15, 0x88, 0xd7, 0x93, 0x62, 0x85, 0xe2, 0xe1,
	0xa4, 0xec, 0xf8, 0x2a, 0xc6, 0x1c, 0x5f, 0xed, 0x6f, 0x41, 0x89, 0x8e, 0x92, 0xf1, 0xfb, 0xaf,
	0x74, 0xc6, 0x5e, 0xfe, 0xcf, 0xbf, 0x72, 0x52, 0x30, 0x35, 0x03, 0x56, 0x50, 0xf7, 0xf0, 0x46,
	0x19, 0x87, 0xbb, 0x00, 0xe5, 0xc0, 0x7d, 0x42, 0x1c, 0xe1, 0x02, 0xc0, 0x02, 0xd5, 0xa7, 0xf8,
	0x51, 0xb1, 0x97, 0xa3, 0xac, 0x09, 0xe3, 0x14, 0x4d, 0x3f, 0x1a, 0xa5, 0x47, 0xe1, 0x54, 0x4f,
	0xc8, 0x9a, 0x80, 0xeb, 0x95, 0x6d, 0x68, 0x6d, 0x98, 0x8e, 0x45, 0x86, 0xe9, 0xd9, 0x29, 0xa7,
	0xc9, 0xa8, 0xe3, 0x1d, 0x9f, 0xc0, 0x7c, 0xcf, 0x33, 0x1d, 0x1f, 0x5f, 0x4a, 0xf7, 0x5c, 0x7a,
	0x3d, 0x74, 0xf2, 0xb9, 0xfa, 0x35, 0xa8, 0x1c, 0x9b, 0x3e, 0x31, 0x3c, 0xf2, 0x39, 0xd7, 0x47,
	0x6f, 0x64, 0x24, 0x6f, 0x85, 0x7f, 0xd6, 0xd3, 0xa7, 0x8f, 0x59, 0x81, 0xd2, 0x1f, 0x3e, 0x86,
	0x36, 0x9e, 0x90, 0x0b, 0xf1, 0x0c, 0x0d, 0x01, 0x1f, 0x93, 0x0b, 0x4d, 0x87, 0x85, 0xf4, 0x64,
	0xfe, 0x98, 0x1a, 0x49, 0x01, 0x85, 0x1b, 0xd2, 0x43, 0xeb, 0x2a, 0x42, 0xf0, 0xba, 0x52, 0xfe,
	0x41, 0x79, 0xa9, 0x9c, 0x1d, 0x4d, 0x01, 0xe8, 0x58, 0x9b, 0xc7, 0xdc, 0x61, 0x6a, 0xae, 0xed,
	0x4d, 0x42, 0xb5, 0xe9, 0x37, 0x14, 0x50, 0x65, 0x28, 0xa7, 0xbf, 0x37, 0xa0, 0x49, 0x35, 0x5f,
	0xaa, 0xc3, 0x4e, 0x7c, 0xd2, 0xc7, 0xcc, 0x16, 0x85, 0xff, 0xa2, 0x67, 0xe2, 0x6d, 0x9a, 0x17,
	0x47, 0x3e, 0xe9, 0xef, 0x4d, 0x46, 0xea, 0x6b, 0x30, 0x2b, 0x1a, 0x5a, 0x27, 0x03, 0x6c, 0xc7,
	0xe6, 0xad, 0xb3, 0x76, 0x1b, 0x27, 0x03, 0xda, 0x6c, 0x1d, 0xe6, 0xcd, 0x33, 0xd3, 0x1e, 0xe2,
	0xa5, 0x27, 0xd4, 0x60, 0xc7, 0xa6, 0x8c, 0xf0, 0xe6, 0xc2, 0x2a, 0x34, 0xe5, 0xf7, 0x26, 0x23,
	0xed, 0xb7, 0x15, 0x68, 0x22, 0x89, 0xcb, 0xb1, 0xb6, 0xe7, 0xde, 0xea, 0xcb, 0x7d, 0x7c, 0xb2,
	0xc7, 0xad, 0x18, 0xf3, 0xb8, 0x2d, 0xc3, 0xf4, 0xc0, 0x1c, 0x85, 0x0f, 0xce, 0xab, 0xfa, 0x14,
	0x2d, 0x76, 0xfb, 0xda, 0x5d, 0x98, 0x93, 0xb0, 0xe3, 0x7b, 0xb6, 0x08, 0x53, 0x56, 0x70, 0x2e,
	0xf9, 0xaf, 0xac, 0xe0, 0xbc, 0xdb, 0xd7, 0x4e, 0x70, 0x83, 0xc3, 0xe7, 0x37, 0x2f, 0xba, 0x16,
	0x09, 0xa7, 0x42, 0x0c, 0xa7, 0x4d, 0xe6, 0xcd, 0x0f, 0xe7, 0x89, 0x6c, 0x3f, 0x29, 0x70, 0x7d,
	0xc9, 0xbb, 0x20, 0x6c, 0x26, 0x61, 0x2b, 0x2b, 0x83, 0x2f, 0x82, 0x6d, 0xf6, 0xfb, 0x85, 0xdd,
	0x10, 0xdb, 0x64, 0x1e, 0x1e, 0xae, 0xee, 0x52, 0x73, 0x35, 0x44, 0xb9, 0x32, 0xe0, 0xfd, 0x39,
	0xda, 0xe2, 0xa1, 0xd4, 0x17, 0x87, 0xf6, 0xcf, 0x20, 0xda, 0xd1, 0x3c, 0x1c, 0xed, 0xfb, 0x30,
	0x85, 0x61, 0x79, 0xc1, 0xaf, 0xdb, 0x99, 0x38, 0xe3, 0x5b, 0x36, 0x9d, 0xb7, 0xbc, 0xfa, 0xc5,
	0x56, 0x38, 0x0b, 0x7f, 0xb1, 0xf5, 0x0b, 0x0a, 0xea, 0x5c, 0x3d, 0x77, 0xfc, 0x45, 0x1c, 0x4f,
	0xf1, 0x9a, 0x7f, 0x54, 0xe0, 0x86, 0x79, 0x0c, 0x8b, 0x50, 0xf5, 0xe3, 0x2b, 0x52, 0xae, 0xf9,
	0x06, 0x6d, 0x1d, 0xe6, 0x76, 0x88, 0x79, 0x16, 0x8f, 0xe9, 0x5d, 0x12, 0x91, 0x59, 0x00, 0x55,
	0x6e, 0xcf, 0x19, 0xf3, 0x0a, 0x5a, 0xf7, 0x1d, 0xbb, 0xc3, 0x04, 0x8f, 0x1c, 0xca, 0xfa, 0x3b,
	0x05, 0xaa, 0x61, 0x45, 0x86, 0x3c, 0x6b, 0x43, 0xc5, 0xb1, 0xad, 0x27, 0xf2, 0x4b, 0x60, 0x51,
	0xbe, 0x44, 0xb2, 0x71, 0x29, 0x58, 0x12, 0x7f, 0xaf, 0x38, 0xa7, 0x16, 0x8a, 0x6f, 0x0f, 0x1c,
	0x33, 0x98, 0x78, 0x22, 0xc5, 0x22, 0x02, 0xe0, 0xbf, 0x55, 0x58, 0x57, 0x03, 0xb5, 0xff, 0x29,
	0xd4, 0xaa, 0x6b, 0x1c, 0xd6, 0xa3, 0x46, 0x40, 0xfc, 0x91, 0xb3, 0xfc, 0x9b, 0xac, 0x59, 0x59,
	0x81, 0x33, 0x07, 0x22, 0xca, 0x91, 0x58, 0x6e, 0x78, 0x7d, 0xe2, 0x8a, 0x7f, 0xe6, 0x4b, 0x17,
	0xd1, 0x33, 0x32, 0x0a, 0xb4, 0x7d, 0x58, 0xe3, 0x69, 0xd0, 0x5b, 0xe7, 0xd6, 0x70, 0xe2, 0xdb,
	0x67, 0x24, 0x99, 0x52, 0x1c, 0xd9, 0x73, 0x8a, 0x6c, 0xcf, 0xe1, 0xe3, 0x53, 0xfc, 0xa7, 0x28,
	0x7f, 0x66, 0x8a, 0x05, 0xed, 0x57, 0x14, 0xb8, 0x9d, 0x3f, 0x62, 0x94, 0xef, 0x26, 0xdd, 0xf3,
	0xfc, 0x7c, 0x37, 0x34, 0x13, 0x56, 0xa0, 0x72, 0x6a, 0xfa, 0xc6, 0xc8, 0xf5, 0x44, 0x0c, 0x60,
	0xfa, 0xd4, 0xf4, 0x77, 0x5d, 0xf6, 0xa3, 0x4b, 0xd9, 0x82, 0x28, 0x26, 0x2d, 0x88, 0xbb, 0x07,
	0x50, 0x93, 0xb3, 0x44, 0x16, 0x61, 0x4e, 0xce, 0xfb, 0x20, 0x67, 0x36, 0x79, 0xda, 0x7c, 0x45,
	0x9d, 0x87, 0x59, 0x09, 0x7c, 0x60, 0xfa, 0x7e, 0x53, 0x49, 0xb5, 0xfd, 0x8c, 0x58, 0x41, 0xb3,
	0x70, 0xf7, 0x08, 0xaa, 0x3b, 0xe1, 0x3f, 0x05, 0xda, 0xb0, 0xb4, 0xd3, 0xfd, 0x78, 0xcb, 0xc8,
	0xf2, 0x09, 0x2e, 0x40, 0x53, 0xaa, 0xa3, 0x9f, 0x9b, 0x4d, 0x45, 0x5d, 0x02, 0x35, 0xd6, 0x83,
	0xc1, 0x0b, 0x77, 0x87, 0x30, 0xbb, 0x91, 0x78, 0x07, 0xb0, 0x06, 0xab, 0x1b, 0xfa, 0x56, 0xa7,
	0xb7, 0xaf, 0x1b, 0xdd, 0xbd, 0xed, 0x7d, 0xe6, 0x0e, 0xe8, 0xec, 0xed, 0xef, 0x7d, 0xba, 0xbb,
	0x7f, 0x74, 0xd8, 0x7c, 0x45, 0xbd, 0x01, 0xad, 0x74, 0x83, 0x83, 0xa3, 0x07, 0x3b, 0xdd, 0x8d,
	0xa6, 0x42, 0x71, 0x4b, 0xd7, 0x3e, 0xea, 0x6e, 0x6e, 0x35, 0x0b, 0x77, 0x07, 0x50, 0x97, 0x7f,
	0x27, 0x45, 0x75, 0x59, 0x7d, 0x6b, 0xa7, 0xd3, 0xeb, 0xee, 0xef, 0x65, 0x39, 0x32, 0x5a, 0xb0,
	0x10, 0xaf, 0xc6, 0xd2, 0x56, 0x53, 0x51, 0x6f, 0x41, 0x3b, 0x5e, 0x73, 0xd8, 0xd9, 0xdd, 0x32,
	0xf8, 0xbc, 0x38, 0x51, 0x43, 0x04, 0xe9, 0x3a, 0x2c, 0x14, 0xb9, 0x06, 0xab, 0x9d, 0x5e, 0xaf,
	0xdb, 0x3b, 0xda, 0xdc, 0x32, 0x3a, 0x1b, 0xd8, 0x31, 0x35, 0x59, 0xb2, 0x01, 0xdd, 0x24, 0xb6,
	0xa2, 0x74, 0x57, 0xac, 0x2b, 0xdc, 0xed, 0x02, 0xec, 0x87, 0x91, 0x44, 0x75, 0x15, 0x96, 0xf7,
	0x1f, 0x7c, 0xb4, 0xb5, 0xd1, 0xcb, 0x5a, 0xcd, 0x2d, 0x68, 0xcb, 0x95, 0x89, 0x57, 0xd7, 0xca,
	0x83, 0xaf, 0xff, 0xc4, 0xfb, 0x03, 0x77, 0x68, 0x3a, 0x83, 0xf5, 0xf7, 0xee, 0x07, 0xc1, 0xba,
	0xe5, 0x8e, 0xee, 0xe1, 0xbf, 0x93, 0x2d, 0x77, 0x78, 0xcf, 0x27, 0xde, 0x99, 0x6d, 0x11, 0x1f,
	0xff, 0xae, 0x9c, 0xfc, 0xc5, 0xf2, 0xf1, 0x14, 0xb6, 0xfb, 0xea, 0xff, 0x04, 0x00, 0x00, 0xff,
	0xff, 0x58, 0xcf, 0x75, 0x8a, 0x9b, 0x59, 0x00, 0x00,
}
