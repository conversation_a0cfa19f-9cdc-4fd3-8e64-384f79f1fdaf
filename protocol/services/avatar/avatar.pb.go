// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/avatar-svr/avatar.proto

package avatar // import "golang.52tt.com/protocol/services/avatar"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AvatarType int32

const (
	// 默认小图
	AvatarType_AVATAR_TYPE_UNSPECIFIED AvatarType = 0
	// 大图
	AvatarType_AVATAR_TYPE_BIG AvatarType = 1
	// 小图
	AvatarType_AVATAR_TYPE_SMALL AvatarType = 2
)

var AvatarType_name = map[int32]string{
	0: "AVATAR_TYPE_UNSPECIFIED",
	1: "AVATAR_TYPE_BIG",
	2: "AVATAR_TYPE_SMALL",
}
var AvatarType_value = map[string]int32{
	"AVATAR_TYPE_UNSPECIFIED": 0,
	"AVATAR_TYPE_BIG":         1,
	"AVATAR_TYPE_SMALL":       2,
}

func (x AvatarType) String() string {
	return proto.EnumName(AvatarType_name, int32(x))
}
func (AvatarType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{0}
}

type SaveAvatarReq struct {
	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Big     []byte `protobuf:"bytes,2,opt,name=big,proto3" json:"big,omitempty"`
	Small   []byte `protobuf:"bytes,3,opt,name=small,proto3" json:"small,omitempty"`
	// 自定义版本号前缀，版本号格式：{prefix}_xxx
	VersionPrefix        string   `protobuf:"bytes,4,opt,name=version_prefix,json=versionPrefix,proto3" json:"version_prefix,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveAvatarReq) Reset()         { *m = SaveAvatarReq{} }
func (m *SaveAvatarReq) String() string { return proto.CompactTextString(m) }
func (*SaveAvatarReq) ProtoMessage()    {}
func (*SaveAvatarReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{0}
}
func (m *SaveAvatarReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveAvatarReq.Unmarshal(m, b)
}
func (m *SaveAvatarReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveAvatarReq.Marshal(b, m, deterministic)
}
func (dst *SaveAvatarReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveAvatarReq.Merge(dst, src)
}
func (m *SaveAvatarReq) XXX_Size() int {
	return xxx_messageInfo_SaveAvatarReq.Size(m)
}
func (m *SaveAvatarReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveAvatarReq.DiscardUnknown(m)
}

var xxx_messageInfo_SaveAvatarReq proto.InternalMessageInfo

func (m *SaveAvatarReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *SaveAvatarReq) GetBig() []byte {
	if m != nil {
		return m.Big
	}
	return nil
}

func (m *SaveAvatarReq) GetSmall() []byte {
	if m != nil {
		return m.Small
	}
	return nil
}

func (m *SaveAvatarReq) GetVersionPrefix() string {
	if m != nil {
		return m.VersionPrefix
	}
	return ""
}

type SaveAvatarResp struct {
	Version              string   `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveAvatarResp) Reset()         { *m = SaveAvatarResp{} }
func (m *SaveAvatarResp) String() string { return proto.CompactTextString(m) }
func (*SaveAvatarResp) ProtoMessage()    {}
func (*SaveAvatarResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{1}
}
func (m *SaveAvatarResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveAvatarResp.Unmarshal(m, b)
}
func (m *SaveAvatarResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveAvatarResp.Marshal(b, m, deterministic)
}
func (dst *SaveAvatarResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveAvatarResp.Merge(dst, src)
}
func (m *SaveAvatarResp) XXX_Size() int {
	return xxx_messageInfo_SaveAvatarResp.Size(m)
}
func (m *SaveAvatarResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveAvatarResp.DiscardUnknown(m)
}

var xxx_messageInfo_SaveAvatarResp proto.InternalMessageInfo

func (m *SaveAvatarResp) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type GetAvatarReq struct {
	Account              string     `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	AvatarType           AvatarType `protobuf:"varint,2,opt,name=avatar_type,json=avatarType,proto3,enum=avatar.AvatarType" json:"avatar_type,omitempty"`
	Version              string     `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAvatarReq) Reset()         { *m = GetAvatarReq{} }
func (m *GetAvatarReq) String() string { return proto.CompactTextString(m) }
func (*GetAvatarReq) ProtoMessage()    {}
func (*GetAvatarReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{2}
}
func (m *GetAvatarReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAvatarReq.Unmarshal(m, b)
}
func (m *GetAvatarReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAvatarReq.Marshal(b, m, deterministic)
}
func (dst *GetAvatarReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAvatarReq.Merge(dst, src)
}
func (m *GetAvatarReq) XXX_Size() int {
	return xxx_messageInfo_GetAvatarReq.Size(m)
}
func (m *GetAvatarReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAvatarReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAvatarReq proto.InternalMessageInfo

func (m *GetAvatarReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GetAvatarReq) GetAvatarType() AvatarType {
	if m != nil {
		return m.AvatarType
	}
	return AvatarType_AVATAR_TYPE_UNSPECIFIED
}

func (m *GetAvatarReq) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type GetAvatarResp struct {
	Avatar               []byte   `protobuf:"bytes,1,opt,name=avatar,proto3" json:"avatar,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAvatarResp) Reset()         { *m = GetAvatarResp{} }
func (m *GetAvatarResp) String() string { return proto.CompactTextString(m) }
func (*GetAvatarResp) ProtoMessage()    {}
func (*GetAvatarResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{3}
}
func (m *GetAvatarResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAvatarResp.Unmarshal(m, b)
}
func (m *GetAvatarResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAvatarResp.Marshal(b, m, deterministic)
}
func (dst *GetAvatarResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAvatarResp.Merge(dst, src)
}
func (m *GetAvatarResp) XXX_Size() int {
	return xxx_messageInfo_GetAvatarResp.Size(m)
}
func (m *GetAvatarResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAvatarResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAvatarResp proto.InternalMessageInfo

func (m *GetAvatarResp) GetAvatar() []byte {
	if m != nil {
		return m.Avatar
	}
	return nil
}

type DeleteAvatarReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Version              string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAvatarReq) Reset()         { *m = DeleteAvatarReq{} }
func (m *DeleteAvatarReq) String() string { return proto.CompactTextString(m) }
func (*DeleteAvatarReq) ProtoMessage()    {}
func (*DeleteAvatarReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{4}
}
func (m *DeleteAvatarReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAvatarReq.Unmarshal(m, b)
}
func (m *DeleteAvatarReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAvatarReq.Marshal(b, m, deterministic)
}
func (dst *DeleteAvatarReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAvatarReq.Merge(dst, src)
}
func (m *DeleteAvatarReq) XXX_Size() int {
	return xxx_messageInfo_DeleteAvatarReq.Size(m)
}
func (m *DeleteAvatarReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAvatarReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAvatarReq proto.InternalMessageInfo

func (m *DeleteAvatarReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *DeleteAvatarReq) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type DeleteAvatarResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAvatarResp) Reset()         { *m = DeleteAvatarResp{} }
func (m *DeleteAvatarResp) String() string { return proto.CompactTextString(m) }
func (*DeleteAvatarResp) ProtoMessage()    {}
func (*DeleteAvatarResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{5}
}
func (m *DeleteAvatarResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAvatarResp.Unmarshal(m, b)
}
func (m *DeleteAvatarResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAvatarResp.Marshal(b, m, deterministic)
}
func (dst *DeleteAvatarResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAvatarResp.Merge(dst, src)
}
func (m *DeleteAvatarResp) XXX_Size() int {
	return xxx_messageInfo_DeleteAvatarResp.Size(m)
}
func (m *DeleteAvatarResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAvatarResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAvatarResp proto.InternalMessageInfo

type GetAvatarByAccountReq struct {
	Account              string     `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	AvatarType           AvatarType `protobuf:"varint,2,opt,name=avatar_type,json=avatarType,proto3,enum=avatar.AvatarType" json:"avatar_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetAvatarByAccountReq) Reset()         { *m = GetAvatarByAccountReq{} }
func (m *GetAvatarByAccountReq) String() string { return proto.CompactTextString(m) }
func (*GetAvatarByAccountReq) ProtoMessage()    {}
func (*GetAvatarByAccountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{6}
}
func (m *GetAvatarByAccountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAvatarByAccountReq.Unmarshal(m, b)
}
func (m *GetAvatarByAccountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAvatarByAccountReq.Marshal(b, m, deterministic)
}
func (dst *GetAvatarByAccountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAvatarByAccountReq.Merge(dst, src)
}
func (m *GetAvatarByAccountReq) XXX_Size() int {
	return xxx_messageInfo_GetAvatarByAccountReq.Size(m)
}
func (m *GetAvatarByAccountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAvatarByAccountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAvatarByAccountReq proto.InternalMessageInfo

func (m *GetAvatarByAccountReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GetAvatarByAccountReq) GetAvatarType() AvatarType {
	if m != nil {
		return m.AvatarType
	}
	return AvatarType_AVATAR_TYPE_UNSPECIFIED
}

type GetAvatarByAccountResp struct {
	Avatar               []byte   `protobuf:"bytes,1,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Version              string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAvatarByAccountResp) Reset()         { *m = GetAvatarByAccountResp{} }
func (m *GetAvatarByAccountResp) String() string { return proto.CompactTextString(m) }
func (*GetAvatarByAccountResp) ProtoMessage()    {}
func (*GetAvatarByAccountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{7}
}
func (m *GetAvatarByAccountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAvatarByAccountResp.Unmarshal(m, b)
}
func (m *GetAvatarByAccountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAvatarByAccountResp.Marshal(b, m, deterministic)
}
func (dst *GetAvatarByAccountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAvatarByAccountResp.Merge(dst, src)
}
func (m *GetAvatarByAccountResp) XXX_Size() int {
	return xxx_messageInfo_GetAvatarByAccountResp.Size(m)
}
func (m *GetAvatarByAccountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAvatarByAccountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAvatarByAccountResp proto.InternalMessageInfo

func (m *GetAvatarByAccountResp) GetAvatar() []byte {
	if m != nil {
		return m.Avatar
	}
	return nil
}

func (m *GetAvatarByAccountResp) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type SaveAvatarByAccountReq struct {
	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Big     []byte `protobuf:"bytes,2,opt,name=big,proto3" json:"big,omitempty"`
	Small   []byte `protobuf:"bytes,3,opt,name=small,proto3" json:"small,omitempty"`
	// 自定义版本号前缀，版本号格式：{prefix}_xxx
	VersionPrefix        string   `protobuf:"bytes,4,opt,name=version_prefix,json=versionPrefix,proto3" json:"version_prefix,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveAvatarByAccountReq) Reset()         { *m = SaveAvatarByAccountReq{} }
func (m *SaveAvatarByAccountReq) String() string { return proto.CompactTextString(m) }
func (*SaveAvatarByAccountReq) ProtoMessage()    {}
func (*SaveAvatarByAccountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{8}
}
func (m *SaveAvatarByAccountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveAvatarByAccountReq.Unmarshal(m, b)
}
func (m *SaveAvatarByAccountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveAvatarByAccountReq.Marshal(b, m, deterministic)
}
func (dst *SaveAvatarByAccountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveAvatarByAccountReq.Merge(dst, src)
}
func (m *SaveAvatarByAccountReq) XXX_Size() int {
	return xxx_messageInfo_SaveAvatarByAccountReq.Size(m)
}
func (m *SaveAvatarByAccountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveAvatarByAccountReq.DiscardUnknown(m)
}

var xxx_messageInfo_SaveAvatarByAccountReq proto.InternalMessageInfo

func (m *SaveAvatarByAccountReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *SaveAvatarByAccountReq) GetBig() []byte {
	if m != nil {
		return m.Big
	}
	return nil
}

func (m *SaveAvatarByAccountReq) GetSmall() []byte {
	if m != nil {
		return m.Small
	}
	return nil
}

func (m *SaveAvatarByAccountReq) GetVersionPrefix() string {
	if m != nil {
		return m.VersionPrefix
	}
	return ""
}

type SaveAvatarByAccountResp struct {
	Version              string   `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveAvatarByAccountResp) Reset()         { *m = SaveAvatarByAccountResp{} }
func (m *SaveAvatarByAccountResp) String() string { return proto.CompactTextString(m) }
func (*SaveAvatarByAccountResp) ProtoMessage()    {}
func (*SaveAvatarByAccountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{9}
}
func (m *SaveAvatarByAccountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveAvatarByAccountResp.Unmarshal(m, b)
}
func (m *SaveAvatarByAccountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveAvatarByAccountResp.Marshal(b, m, deterministic)
}
func (dst *SaveAvatarByAccountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveAvatarByAccountResp.Merge(dst, src)
}
func (m *SaveAvatarByAccountResp) XXX_Size() int {
	return xxx_messageInfo_SaveAvatarByAccountResp.Size(m)
}
func (m *SaveAvatarByAccountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveAvatarByAccountResp.DiscardUnknown(m)
}

var xxx_messageInfo_SaveAvatarByAccountResp proto.InternalMessageInfo

func (m *SaveAvatarByAccountResp) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type DeleteAvatarByAccountReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAvatarByAccountReq) Reset()         { *m = DeleteAvatarByAccountReq{} }
func (m *DeleteAvatarByAccountReq) String() string { return proto.CompactTextString(m) }
func (*DeleteAvatarByAccountReq) ProtoMessage()    {}
func (*DeleteAvatarByAccountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{10}
}
func (m *DeleteAvatarByAccountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAvatarByAccountReq.Unmarshal(m, b)
}
func (m *DeleteAvatarByAccountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAvatarByAccountReq.Marshal(b, m, deterministic)
}
func (dst *DeleteAvatarByAccountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAvatarByAccountReq.Merge(dst, src)
}
func (m *DeleteAvatarByAccountReq) XXX_Size() int {
	return xxx_messageInfo_DeleteAvatarByAccountReq.Size(m)
}
func (m *DeleteAvatarByAccountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAvatarByAccountReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAvatarByAccountReq proto.InternalMessageInfo

func (m *DeleteAvatarByAccountReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type DeleteAvatarByAccountResp struct {
	DelVersion           string   `protobuf:"bytes,1,opt,name=del_version,json=delVersion,proto3" json:"del_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAvatarByAccountResp) Reset()         { *m = DeleteAvatarByAccountResp{} }
func (m *DeleteAvatarByAccountResp) String() string { return proto.CompactTextString(m) }
func (*DeleteAvatarByAccountResp) ProtoMessage()    {}
func (*DeleteAvatarByAccountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{11}
}
func (m *DeleteAvatarByAccountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAvatarByAccountResp.Unmarshal(m, b)
}
func (m *DeleteAvatarByAccountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAvatarByAccountResp.Marshal(b, m, deterministic)
}
func (dst *DeleteAvatarByAccountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAvatarByAccountResp.Merge(dst, src)
}
func (m *DeleteAvatarByAccountResp) XXX_Size() int {
	return xxx_messageInfo_DeleteAvatarByAccountResp.Size(m)
}
func (m *DeleteAvatarByAccountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAvatarByAccountResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAvatarByAccountResp proto.InternalMessageInfo

func (m *DeleteAvatarByAccountResp) GetDelVersion() string {
	if m != nil {
		return m.DelVersion
	}
	return ""
}

type GetAvatarVersionByAccountReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAvatarVersionByAccountReq) Reset()         { *m = GetAvatarVersionByAccountReq{} }
func (m *GetAvatarVersionByAccountReq) String() string { return proto.CompactTextString(m) }
func (*GetAvatarVersionByAccountReq) ProtoMessage()    {}
func (*GetAvatarVersionByAccountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{12}
}
func (m *GetAvatarVersionByAccountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAvatarVersionByAccountReq.Unmarshal(m, b)
}
func (m *GetAvatarVersionByAccountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAvatarVersionByAccountReq.Marshal(b, m, deterministic)
}
func (dst *GetAvatarVersionByAccountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAvatarVersionByAccountReq.Merge(dst, src)
}
func (m *GetAvatarVersionByAccountReq) XXX_Size() int {
	return xxx_messageInfo_GetAvatarVersionByAccountReq.Size(m)
}
func (m *GetAvatarVersionByAccountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAvatarVersionByAccountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAvatarVersionByAccountReq proto.InternalMessageInfo

func (m *GetAvatarVersionByAccountReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type GetAvatarVersionByAccountResp struct {
	Version              string   `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAvatarVersionByAccountResp) Reset()         { *m = GetAvatarVersionByAccountResp{} }
func (m *GetAvatarVersionByAccountResp) String() string { return proto.CompactTextString(m) }
func (*GetAvatarVersionByAccountResp) ProtoMessage()    {}
func (*GetAvatarVersionByAccountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{13}
}
func (m *GetAvatarVersionByAccountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAvatarVersionByAccountResp.Unmarshal(m, b)
}
func (m *GetAvatarVersionByAccountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAvatarVersionByAccountResp.Marshal(b, m, deterministic)
}
func (dst *GetAvatarVersionByAccountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAvatarVersionByAccountResp.Merge(dst, src)
}
func (m *GetAvatarVersionByAccountResp) XXX_Size() int {
	return xxx_messageInfo_GetAvatarVersionByAccountResp.Size(m)
}
func (m *GetAvatarVersionByAccountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAvatarVersionByAccountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAvatarVersionByAccountResp proto.InternalMessageInfo

func (m *GetAvatarVersionByAccountResp) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type BatchGetAvatarVersionByAccountReq struct {
	AccountList          []string `protobuf:"bytes,1,rep,name=account_list,json=accountList,proto3" json:"account_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAvatarVersionByAccountReq) Reset()         { *m = BatchGetAvatarVersionByAccountReq{} }
func (m *BatchGetAvatarVersionByAccountReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetAvatarVersionByAccountReq) ProtoMessage()    {}
func (*BatchGetAvatarVersionByAccountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{14}
}
func (m *BatchGetAvatarVersionByAccountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAvatarVersionByAccountReq.Unmarshal(m, b)
}
func (m *BatchGetAvatarVersionByAccountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAvatarVersionByAccountReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetAvatarVersionByAccountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAvatarVersionByAccountReq.Merge(dst, src)
}
func (m *BatchGetAvatarVersionByAccountReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetAvatarVersionByAccountReq.Size(m)
}
func (m *BatchGetAvatarVersionByAccountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAvatarVersionByAccountReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAvatarVersionByAccountReq proto.InternalMessageInfo

func (m *BatchGetAvatarVersionByAccountReq) GetAccountList() []string {
	if m != nil {
		return m.AccountList
	}
	return nil
}

type AvatarVersion struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Version              string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AvatarVersion) Reset()         { *m = AvatarVersion{} }
func (m *AvatarVersion) String() string { return proto.CompactTextString(m) }
func (*AvatarVersion) ProtoMessage()    {}
func (*AvatarVersion) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{15}
}
func (m *AvatarVersion) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AvatarVersion.Unmarshal(m, b)
}
func (m *AvatarVersion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AvatarVersion.Marshal(b, m, deterministic)
}
func (dst *AvatarVersion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AvatarVersion.Merge(dst, src)
}
func (m *AvatarVersion) XXX_Size() int {
	return xxx_messageInfo_AvatarVersion.Size(m)
}
func (m *AvatarVersion) XXX_DiscardUnknown() {
	xxx_messageInfo_AvatarVersion.DiscardUnknown(m)
}

var xxx_messageInfo_AvatarVersion proto.InternalMessageInfo

func (m *AvatarVersion) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *AvatarVersion) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type BatchGetAvatarVersionByAccountResp struct {
	VersionList          []*AvatarVersion `protobuf:"bytes,1,rep,name=version_list,json=versionList,proto3" json:"version_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchGetAvatarVersionByAccountResp) Reset()         { *m = BatchGetAvatarVersionByAccountResp{} }
func (m *BatchGetAvatarVersionByAccountResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetAvatarVersionByAccountResp) ProtoMessage()    {}
func (*BatchGetAvatarVersionByAccountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{16}
}
func (m *BatchGetAvatarVersionByAccountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAvatarVersionByAccountResp.Unmarshal(m, b)
}
func (m *BatchGetAvatarVersionByAccountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAvatarVersionByAccountResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetAvatarVersionByAccountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAvatarVersionByAccountResp.Merge(dst, src)
}
func (m *BatchGetAvatarVersionByAccountResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetAvatarVersionByAccountResp.Size(m)
}
func (m *BatchGetAvatarVersionByAccountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAvatarVersionByAccountResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAvatarVersionByAccountResp proto.InternalMessageInfo

func (m *BatchGetAvatarVersionByAccountResp) GetVersionList() []*AvatarVersion {
	if m != nil {
		return m.VersionList
	}
	return nil
}

type SetAvatarVersionByAccountReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Version              string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAvatarVersionByAccountReq) Reset()         { *m = SetAvatarVersionByAccountReq{} }
func (m *SetAvatarVersionByAccountReq) String() string { return proto.CompactTextString(m) }
func (*SetAvatarVersionByAccountReq) ProtoMessage()    {}
func (*SetAvatarVersionByAccountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{17}
}
func (m *SetAvatarVersionByAccountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAvatarVersionByAccountReq.Unmarshal(m, b)
}
func (m *SetAvatarVersionByAccountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAvatarVersionByAccountReq.Marshal(b, m, deterministic)
}
func (dst *SetAvatarVersionByAccountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAvatarVersionByAccountReq.Merge(dst, src)
}
func (m *SetAvatarVersionByAccountReq) XXX_Size() int {
	return xxx_messageInfo_SetAvatarVersionByAccountReq.Size(m)
}
func (m *SetAvatarVersionByAccountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAvatarVersionByAccountReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetAvatarVersionByAccountReq proto.InternalMessageInfo

func (m *SetAvatarVersionByAccountReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *SetAvatarVersionByAccountReq) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type SetAvatarVersionByAccountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAvatarVersionByAccountResp) Reset()         { *m = SetAvatarVersionByAccountResp{} }
func (m *SetAvatarVersionByAccountResp) String() string { return proto.CompactTextString(m) }
func (*SetAvatarVersionByAccountResp) ProtoMessage()    {}
func (*SetAvatarVersionByAccountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{18}
}
func (m *SetAvatarVersionByAccountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAvatarVersionByAccountResp.Unmarshal(m, b)
}
func (m *SetAvatarVersionByAccountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAvatarVersionByAccountResp.Marshal(b, m, deterministic)
}
func (dst *SetAvatarVersionByAccountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAvatarVersionByAccountResp.Merge(dst, src)
}
func (m *SetAvatarVersionByAccountResp) XXX_Size() int {
	return xxx_messageInfo_SetAvatarVersionByAccountResp.Size(m)
}
func (m *SetAvatarVersionByAccountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAvatarVersionByAccountResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetAvatarVersionByAccountResp proto.InternalMessageInfo

type DeleteAvatarVersionByAccountReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAvatarVersionByAccountReq) Reset()         { *m = DeleteAvatarVersionByAccountReq{} }
func (m *DeleteAvatarVersionByAccountReq) String() string { return proto.CompactTextString(m) }
func (*DeleteAvatarVersionByAccountReq) ProtoMessage()    {}
func (*DeleteAvatarVersionByAccountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{19}
}
func (m *DeleteAvatarVersionByAccountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAvatarVersionByAccountReq.Unmarshal(m, b)
}
func (m *DeleteAvatarVersionByAccountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAvatarVersionByAccountReq.Marshal(b, m, deterministic)
}
func (dst *DeleteAvatarVersionByAccountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAvatarVersionByAccountReq.Merge(dst, src)
}
func (m *DeleteAvatarVersionByAccountReq) XXX_Size() int {
	return xxx_messageInfo_DeleteAvatarVersionByAccountReq.Size(m)
}
func (m *DeleteAvatarVersionByAccountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAvatarVersionByAccountReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAvatarVersionByAccountReq proto.InternalMessageInfo

func (m *DeleteAvatarVersionByAccountReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type DeleteAvatarVersionByAccountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAvatarVersionByAccountResp) Reset()         { *m = DeleteAvatarVersionByAccountResp{} }
func (m *DeleteAvatarVersionByAccountResp) String() string { return proto.CompactTextString(m) }
func (*DeleteAvatarVersionByAccountResp) ProtoMessage()    {}
func (*DeleteAvatarVersionByAccountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{20}
}
func (m *DeleteAvatarVersionByAccountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAvatarVersionByAccountResp.Unmarshal(m, b)
}
func (m *DeleteAvatarVersionByAccountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAvatarVersionByAccountResp.Marshal(b, m, deterministic)
}
func (dst *DeleteAvatarVersionByAccountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAvatarVersionByAccountResp.Merge(dst, src)
}
func (m *DeleteAvatarVersionByAccountResp) XXX_Size() int {
	return xxx_messageInfo_DeleteAvatarVersionByAccountResp.Size(m)
}
func (m *DeleteAvatarVersionByAccountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAvatarVersionByAccountResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAvatarVersionByAccountResp proto.InternalMessageInfo

type GetStaticAvatarReq struct {
	Filename             string   `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStaticAvatarReq) Reset()         { *m = GetStaticAvatarReq{} }
func (m *GetStaticAvatarReq) String() string { return proto.CompactTextString(m) }
func (*GetStaticAvatarReq) ProtoMessage()    {}
func (*GetStaticAvatarReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{21}
}
func (m *GetStaticAvatarReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStaticAvatarReq.Unmarshal(m, b)
}
func (m *GetStaticAvatarReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStaticAvatarReq.Marshal(b, m, deterministic)
}
func (dst *GetStaticAvatarReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStaticAvatarReq.Merge(dst, src)
}
func (m *GetStaticAvatarReq) XXX_Size() int {
	return xxx_messageInfo_GetStaticAvatarReq.Size(m)
}
func (m *GetStaticAvatarReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStaticAvatarReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStaticAvatarReq proto.InternalMessageInfo

func (m *GetStaticAvatarReq) GetFilename() string {
	if m != nil {
		return m.Filename
	}
	return ""
}

type GetStaticAvatarResp struct {
	Avatar               []byte   `protobuf:"bytes,1,opt,name=avatar,proto3" json:"avatar,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStaticAvatarResp) Reset()         { *m = GetStaticAvatarResp{} }
func (m *GetStaticAvatarResp) String() string { return proto.CompactTextString(m) }
func (*GetStaticAvatarResp) ProtoMessage()    {}
func (*GetStaticAvatarResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{22}
}
func (m *GetStaticAvatarResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStaticAvatarResp.Unmarshal(m, b)
}
func (m *GetStaticAvatarResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStaticAvatarResp.Marshal(b, m, deterministic)
}
func (dst *GetStaticAvatarResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStaticAvatarResp.Merge(dst, src)
}
func (m *GetStaticAvatarResp) XXX_Size() int {
	return xxx_messageInfo_GetStaticAvatarResp.Size(m)
}
func (m *GetStaticAvatarResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStaticAvatarResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStaticAvatarResp proto.InternalMessageInfo

func (m *GetStaticAvatarResp) GetAvatar() []byte {
	if m != nil {
		return m.Avatar
	}
	return nil
}

type UploadAvatarReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	ImageData            []byte   `protobuf:"bytes,2,opt,name=image_data,json=imageData,proto3" json:"image_data,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	SmDeviceId           string   `protobuf:"bytes,4,opt,name=sm_device_id,json=smDeviceId,proto3" json:"sm_device_id,omitempty"`
	SourceAccount        string   `protobuf:"bytes,5,opt,name=source_account,json=sourceAccount,proto3" json:"source_account,omitempty"`
	SourceId             uint32   `protobuf:"varint,6,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	Ip                   string   `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UploadAvatarReq) Reset()         { *m = UploadAvatarReq{} }
func (m *UploadAvatarReq) String() string { return proto.CompactTextString(m) }
func (*UploadAvatarReq) ProtoMessage()    {}
func (*UploadAvatarReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{23}
}
func (m *UploadAvatarReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UploadAvatarReq.Unmarshal(m, b)
}
func (m *UploadAvatarReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UploadAvatarReq.Marshal(b, m, deterministic)
}
func (dst *UploadAvatarReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadAvatarReq.Merge(dst, src)
}
func (m *UploadAvatarReq) XXX_Size() int {
	return xxx_messageInfo_UploadAvatarReq.Size(m)
}
func (m *UploadAvatarReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadAvatarReq.DiscardUnknown(m)
}

var xxx_messageInfo_UploadAvatarReq proto.InternalMessageInfo

func (m *UploadAvatarReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UploadAvatarReq) GetImageData() []byte {
	if m != nil {
		return m.ImageData
	}
	return nil
}

func (m *UploadAvatarReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UploadAvatarReq) GetSmDeviceId() string {
	if m != nil {
		return m.SmDeviceId
	}
	return ""
}

func (m *UploadAvatarReq) GetSourceAccount() string {
	if m != nil {
		return m.SourceAccount
	}
	return ""
}

func (m *UploadAvatarReq) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *UploadAvatarReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

type UploadAvatarResp struct {
	Md5                  string   `protobuf:"bytes,1,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UploadAvatarResp) Reset()         { *m = UploadAvatarResp{} }
func (m *UploadAvatarResp) String() string { return proto.CompactTextString(m) }
func (*UploadAvatarResp) ProtoMessage()    {}
func (*UploadAvatarResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_avatar_415b76125b97f116, []int{24}
}
func (m *UploadAvatarResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UploadAvatarResp.Unmarshal(m, b)
}
func (m *UploadAvatarResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UploadAvatarResp.Marshal(b, m, deterministic)
}
func (dst *UploadAvatarResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadAvatarResp.Merge(dst, src)
}
func (m *UploadAvatarResp) XXX_Size() int {
	return xxx_messageInfo_UploadAvatarResp.Size(m)
}
func (m *UploadAvatarResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadAvatarResp.DiscardUnknown(m)
}

var xxx_messageInfo_UploadAvatarResp proto.InternalMessageInfo

func (m *UploadAvatarResp) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func init() {
	proto.RegisterType((*SaveAvatarReq)(nil), "avatar.SaveAvatarReq")
	proto.RegisterType((*SaveAvatarResp)(nil), "avatar.SaveAvatarResp")
	proto.RegisterType((*GetAvatarReq)(nil), "avatar.GetAvatarReq")
	proto.RegisterType((*GetAvatarResp)(nil), "avatar.GetAvatarResp")
	proto.RegisterType((*DeleteAvatarReq)(nil), "avatar.DeleteAvatarReq")
	proto.RegisterType((*DeleteAvatarResp)(nil), "avatar.DeleteAvatarResp")
	proto.RegisterType((*GetAvatarByAccountReq)(nil), "avatar.GetAvatarByAccountReq")
	proto.RegisterType((*GetAvatarByAccountResp)(nil), "avatar.GetAvatarByAccountResp")
	proto.RegisterType((*SaveAvatarByAccountReq)(nil), "avatar.SaveAvatarByAccountReq")
	proto.RegisterType((*SaveAvatarByAccountResp)(nil), "avatar.SaveAvatarByAccountResp")
	proto.RegisterType((*DeleteAvatarByAccountReq)(nil), "avatar.DeleteAvatarByAccountReq")
	proto.RegisterType((*DeleteAvatarByAccountResp)(nil), "avatar.DeleteAvatarByAccountResp")
	proto.RegisterType((*GetAvatarVersionByAccountReq)(nil), "avatar.GetAvatarVersionByAccountReq")
	proto.RegisterType((*GetAvatarVersionByAccountResp)(nil), "avatar.GetAvatarVersionByAccountResp")
	proto.RegisterType((*BatchGetAvatarVersionByAccountReq)(nil), "avatar.BatchGetAvatarVersionByAccountReq")
	proto.RegisterType((*AvatarVersion)(nil), "avatar.AvatarVersion")
	proto.RegisterType((*BatchGetAvatarVersionByAccountResp)(nil), "avatar.BatchGetAvatarVersionByAccountResp")
	proto.RegisterType((*SetAvatarVersionByAccountReq)(nil), "avatar.SetAvatarVersionByAccountReq")
	proto.RegisterType((*SetAvatarVersionByAccountResp)(nil), "avatar.SetAvatarVersionByAccountResp")
	proto.RegisterType((*DeleteAvatarVersionByAccountReq)(nil), "avatar.DeleteAvatarVersionByAccountReq")
	proto.RegisterType((*DeleteAvatarVersionByAccountResp)(nil), "avatar.DeleteAvatarVersionByAccountResp")
	proto.RegisterType((*GetStaticAvatarReq)(nil), "avatar.GetStaticAvatarReq")
	proto.RegisterType((*GetStaticAvatarResp)(nil), "avatar.GetStaticAvatarResp")
	proto.RegisterType((*UploadAvatarReq)(nil), "avatar.UploadAvatarReq")
	proto.RegisterType((*UploadAvatarResp)(nil), "avatar.UploadAvatarResp")
	proto.RegisterEnum("avatar.AvatarType", AvatarType_name, AvatarType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AvatarClient is the client API for Avatar service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AvatarClient interface {
	// 获取账号的头像
	GetAvatarByAccount(ctx context.Context, in *GetAvatarByAccountReq, opts ...grpc.CallOption) (*GetAvatarByAccountResp, error)
	// 保存账号的头像
	SaveAvatarByAccount(ctx context.Context, in *SaveAvatarByAccountReq, opts ...grpc.CallOption) (*SaveAvatarByAccountResp, error)
	// 删除账号的头像
	DeleteAvatarByAccount(ctx context.Context, in *DeleteAvatarByAccountReq, opts ...grpc.CallOption) (*DeleteAvatarByAccountResp, error)
	// 获取账号的头像版本号
	GetAvatarVersionByAccount(ctx context.Context, in *GetAvatarVersionByAccountReq, opts ...grpc.CallOption) (*GetAvatarVersionByAccountResp, error)
	// 批量获取账号的头像版本号
	BatchGetAvatarVersionByAccount(ctx context.Context, in *BatchGetAvatarVersionByAccountReq, opts ...grpc.CallOption) (*BatchGetAvatarVersionByAccountResp, error)
	// 设置账号的头像版本号
	SetAvatarVersionByAccount(ctx context.Context, in *SetAvatarVersionByAccountReq, opts ...grpc.CallOption) (*SetAvatarVersionByAccountResp, error)
	// 删除账号的头像版本号
	DeleteAvatarVersionByAccount(ctx context.Context, in *DeleteAvatarVersionByAccountReq, opts ...grpc.CallOption) (*DeleteAvatarVersionByAccountResp, error)
	// 获取头像文件
	GetAvatar(ctx context.Context, in *GetAvatarReq, opts ...grpc.CallOption) (*GetAvatarResp, error)
	// 保存头像文件
	SaveAvatar(ctx context.Context, in *SaveAvatarReq, opts ...grpc.CallOption) (*SaveAvatarResp, error)
	// 删除头像文件
	DeleteAvatar(ctx context.Context, in *DeleteAvatarReq, opts ...grpc.CallOption) (*DeleteAvatarResp, error)
	// 获取静态头像文件
	GetStaticAvatar(ctx context.Context, in *GetStaticAvatarReq, opts ...grpc.CallOption) (*GetStaticAvatarResp, error)
	// deprecated, migrate to avatar-mng-api
	UploadAvatar(ctx context.Context, in *UploadAvatarReq, opts ...grpc.CallOption) (*UploadAvatarResp, error)
}

type avatarClient struct {
	cc *grpc.ClientConn
}

func NewAvatarClient(cc *grpc.ClientConn) AvatarClient {
	return &avatarClient{cc}
}

func (c *avatarClient) GetAvatarByAccount(ctx context.Context, in *GetAvatarByAccountReq, opts ...grpc.CallOption) (*GetAvatarByAccountResp, error) {
	out := new(GetAvatarByAccountResp)
	err := c.cc.Invoke(ctx, "/avatar.Avatar/GetAvatarByAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *avatarClient) SaveAvatarByAccount(ctx context.Context, in *SaveAvatarByAccountReq, opts ...grpc.CallOption) (*SaveAvatarByAccountResp, error) {
	out := new(SaveAvatarByAccountResp)
	err := c.cc.Invoke(ctx, "/avatar.Avatar/SaveAvatarByAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *avatarClient) DeleteAvatarByAccount(ctx context.Context, in *DeleteAvatarByAccountReq, opts ...grpc.CallOption) (*DeleteAvatarByAccountResp, error) {
	out := new(DeleteAvatarByAccountResp)
	err := c.cc.Invoke(ctx, "/avatar.Avatar/DeleteAvatarByAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *avatarClient) GetAvatarVersionByAccount(ctx context.Context, in *GetAvatarVersionByAccountReq, opts ...grpc.CallOption) (*GetAvatarVersionByAccountResp, error) {
	out := new(GetAvatarVersionByAccountResp)
	err := c.cc.Invoke(ctx, "/avatar.Avatar/GetAvatarVersionByAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *avatarClient) BatchGetAvatarVersionByAccount(ctx context.Context, in *BatchGetAvatarVersionByAccountReq, opts ...grpc.CallOption) (*BatchGetAvatarVersionByAccountResp, error) {
	out := new(BatchGetAvatarVersionByAccountResp)
	err := c.cc.Invoke(ctx, "/avatar.Avatar/BatchGetAvatarVersionByAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *avatarClient) SetAvatarVersionByAccount(ctx context.Context, in *SetAvatarVersionByAccountReq, opts ...grpc.CallOption) (*SetAvatarVersionByAccountResp, error) {
	out := new(SetAvatarVersionByAccountResp)
	err := c.cc.Invoke(ctx, "/avatar.Avatar/SetAvatarVersionByAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *avatarClient) DeleteAvatarVersionByAccount(ctx context.Context, in *DeleteAvatarVersionByAccountReq, opts ...grpc.CallOption) (*DeleteAvatarVersionByAccountResp, error) {
	out := new(DeleteAvatarVersionByAccountResp)
	err := c.cc.Invoke(ctx, "/avatar.Avatar/DeleteAvatarVersionByAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *avatarClient) GetAvatar(ctx context.Context, in *GetAvatarReq, opts ...grpc.CallOption) (*GetAvatarResp, error) {
	out := new(GetAvatarResp)
	err := c.cc.Invoke(ctx, "/avatar.Avatar/GetAvatar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *avatarClient) SaveAvatar(ctx context.Context, in *SaveAvatarReq, opts ...grpc.CallOption) (*SaveAvatarResp, error) {
	out := new(SaveAvatarResp)
	err := c.cc.Invoke(ctx, "/avatar.Avatar/SaveAvatar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *avatarClient) DeleteAvatar(ctx context.Context, in *DeleteAvatarReq, opts ...grpc.CallOption) (*DeleteAvatarResp, error) {
	out := new(DeleteAvatarResp)
	err := c.cc.Invoke(ctx, "/avatar.Avatar/DeleteAvatar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *avatarClient) GetStaticAvatar(ctx context.Context, in *GetStaticAvatarReq, opts ...grpc.CallOption) (*GetStaticAvatarResp, error) {
	out := new(GetStaticAvatarResp)
	err := c.cc.Invoke(ctx, "/avatar.Avatar/GetStaticAvatar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *avatarClient) UploadAvatar(ctx context.Context, in *UploadAvatarReq, opts ...grpc.CallOption) (*UploadAvatarResp, error) {
	out := new(UploadAvatarResp)
	err := c.cc.Invoke(ctx, "/avatar.Avatar/UploadAvatar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AvatarServer is the server API for Avatar service.
type AvatarServer interface {
	// 获取账号的头像
	GetAvatarByAccount(context.Context, *GetAvatarByAccountReq) (*GetAvatarByAccountResp, error)
	// 保存账号的头像
	SaveAvatarByAccount(context.Context, *SaveAvatarByAccountReq) (*SaveAvatarByAccountResp, error)
	// 删除账号的头像
	DeleteAvatarByAccount(context.Context, *DeleteAvatarByAccountReq) (*DeleteAvatarByAccountResp, error)
	// 获取账号的头像版本号
	GetAvatarVersionByAccount(context.Context, *GetAvatarVersionByAccountReq) (*GetAvatarVersionByAccountResp, error)
	// 批量获取账号的头像版本号
	BatchGetAvatarVersionByAccount(context.Context, *BatchGetAvatarVersionByAccountReq) (*BatchGetAvatarVersionByAccountResp, error)
	// 设置账号的头像版本号
	SetAvatarVersionByAccount(context.Context, *SetAvatarVersionByAccountReq) (*SetAvatarVersionByAccountResp, error)
	// 删除账号的头像版本号
	DeleteAvatarVersionByAccount(context.Context, *DeleteAvatarVersionByAccountReq) (*DeleteAvatarVersionByAccountResp, error)
	// 获取头像文件
	GetAvatar(context.Context, *GetAvatarReq) (*GetAvatarResp, error)
	// 保存头像文件
	SaveAvatar(context.Context, *SaveAvatarReq) (*SaveAvatarResp, error)
	// 删除头像文件
	DeleteAvatar(context.Context, *DeleteAvatarReq) (*DeleteAvatarResp, error)
	// 获取静态头像文件
	GetStaticAvatar(context.Context, *GetStaticAvatarReq) (*GetStaticAvatarResp, error)
	// deprecated, migrate to avatar-mng-api
	UploadAvatar(context.Context, *UploadAvatarReq) (*UploadAvatarResp, error)
}

func RegisterAvatarServer(s *grpc.Server, srv AvatarServer) {
	s.RegisterService(&_Avatar_serviceDesc, srv)
}

func _Avatar_GetAvatarByAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvatarByAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarServer).GetAvatarByAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar.Avatar/GetAvatarByAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarServer).GetAvatarByAccount(ctx, req.(*GetAvatarByAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Avatar_SaveAvatarByAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveAvatarByAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarServer).SaveAvatarByAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar.Avatar/SaveAvatarByAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarServer).SaveAvatarByAccount(ctx, req.(*SaveAvatarByAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Avatar_DeleteAvatarByAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAvatarByAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarServer).DeleteAvatarByAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar.Avatar/DeleteAvatarByAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarServer).DeleteAvatarByAccount(ctx, req.(*DeleteAvatarByAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Avatar_GetAvatarVersionByAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvatarVersionByAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarServer).GetAvatarVersionByAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar.Avatar/GetAvatarVersionByAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarServer).GetAvatarVersionByAccount(ctx, req.(*GetAvatarVersionByAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Avatar_BatchGetAvatarVersionByAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAvatarVersionByAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarServer).BatchGetAvatarVersionByAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar.Avatar/BatchGetAvatarVersionByAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarServer).BatchGetAvatarVersionByAccount(ctx, req.(*BatchGetAvatarVersionByAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Avatar_SetAvatarVersionByAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAvatarVersionByAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarServer).SetAvatarVersionByAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar.Avatar/SetAvatarVersionByAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarServer).SetAvatarVersionByAccount(ctx, req.(*SetAvatarVersionByAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Avatar_DeleteAvatarVersionByAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAvatarVersionByAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarServer).DeleteAvatarVersionByAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar.Avatar/DeleteAvatarVersionByAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarServer).DeleteAvatarVersionByAccount(ctx, req.(*DeleteAvatarVersionByAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Avatar_GetAvatar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvatarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarServer).GetAvatar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar.Avatar/GetAvatar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarServer).GetAvatar(ctx, req.(*GetAvatarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Avatar_SaveAvatar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveAvatarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarServer).SaveAvatar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar.Avatar/SaveAvatar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarServer).SaveAvatar(ctx, req.(*SaveAvatarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Avatar_DeleteAvatar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAvatarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarServer).DeleteAvatar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar.Avatar/DeleteAvatar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarServer).DeleteAvatar(ctx, req.(*DeleteAvatarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Avatar_GetStaticAvatar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaticAvatarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarServer).GetStaticAvatar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar.Avatar/GetStaticAvatar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarServer).GetStaticAvatar(ctx, req.(*GetStaticAvatarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Avatar_UploadAvatar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadAvatarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AvatarServer).UploadAvatar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/avatar.Avatar/UploadAvatar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AvatarServer).UploadAvatar(ctx, req.(*UploadAvatarReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Avatar_serviceDesc = grpc.ServiceDesc{
	ServiceName: "avatar.Avatar",
	HandlerType: (*AvatarServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAvatarByAccount",
			Handler:    _Avatar_GetAvatarByAccount_Handler,
		},
		{
			MethodName: "SaveAvatarByAccount",
			Handler:    _Avatar_SaveAvatarByAccount_Handler,
		},
		{
			MethodName: "DeleteAvatarByAccount",
			Handler:    _Avatar_DeleteAvatarByAccount_Handler,
		},
		{
			MethodName: "GetAvatarVersionByAccount",
			Handler:    _Avatar_GetAvatarVersionByAccount_Handler,
		},
		{
			MethodName: "BatchGetAvatarVersionByAccount",
			Handler:    _Avatar_BatchGetAvatarVersionByAccount_Handler,
		},
		{
			MethodName: "SetAvatarVersionByAccount",
			Handler:    _Avatar_SetAvatarVersionByAccount_Handler,
		},
		{
			MethodName: "DeleteAvatarVersionByAccount",
			Handler:    _Avatar_DeleteAvatarVersionByAccount_Handler,
		},
		{
			MethodName: "GetAvatar",
			Handler:    _Avatar_GetAvatar_Handler,
		},
		{
			MethodName: "SaveAvatar",
			Handler:    _Avatar_SaveAvatar_Handler,
		},
		{
			MethodName: "DeleteAvatar",
			Handler:    _Avatar_DeleteAvatar_Handler,
		},
		{
			MethodName: "GetStaticAvatar",
			Handler:    _Avatar_GetStaticAvatar_Handler,
		},
		{
			MethodName: "UploadAvatar",
			Handler:    _Avatar_UploadAvatar_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/avatar-svr/avatar.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/avatar-svr/avatar.proto", fileDescriptor_avatar_415b76125b97f116)
}

var fileDescriptor_avatar_415b76125b97f116 = []byte{
	// 880 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x56, 0xff, 0x4f, 0xdb, 0x46,
	0x14, 0x5f, 0xc2, 0x4a, 0xc9, 0x4b, 0x02, 0xd9, 0xa3, 0x01, 0xd7, 0x40, 0x09, 0x56, 0xbb, 0x66,
	0x48, 0x85, 0x09, 0x56, 0xa9, 0x53, 0xf7, 0x4b, 0x28, 0x14, 0xa5, 0x62, 0x13, 0xb2, 0x01, 0x69,
	0xd3, 0x34, 0xeb, 0x6a, 0x1f, 0xf4, 0x34, 0x07, 0x1f, 0xb9, 0x23, 0x2d, 0x3f, 0xee, 0xff, 0xd8,
	0x5f, 0xb5, 0xbf, 0x68, 0xf2, 0xf9, 0x9c, 0x38, 0xc1, 0x71, 0xdc, 0xaa, 0xbf, 0xf9, 0xde, 0xdd,
	0x7b, 0x9f, 0xcf, 0xfb, 0xf6, 0x91, 0xe1, 0x7b, 0x29, 0x77, 0x6f, 0x6e, 0x99, 0xf7, 0xb7, 0x60,
	0xc1, 0x80, 0xf6, 0x77, 0xc9, 0x80, 0x48, 0xd2, 0x7f, 0x21, 0x06, 0xc9, 0xe7, 0x0e, 0xef, 0x87,
	0x32, 0xc4, 0xf9, 0xf8, 0x64, 0x7d, 0x82, 0xba, 0x43, 0x06, 0xb4, 0xa3, 0x4e, 0x36, 0xbd, 0x41,
	0x03, 0x1e, 0x12, 0xcf, 0x0b, 0x6f, 0xaf, 0xa5, 0x51, 0x6a, 0x95, 0xda, 0x15, 0x3b, 0x39, 0x62,
	0x03, 0xe6, 0xde, 0xb3, 0x2b, 0xa3, 0xdc, 0x2a, 0xb5, 0x6b, 0x76, 0xf4, 0x89, 0x8f, 0xe0, 0x81,
	0xe8, 0x91, 0x20, 0x30, 0xe6, 0x94, 0x2d, 0x3e, 0xe0, 0x33, 0x58, 0x1c, 0xd0, 0xbe, 0x60, 0xe1,
	0xb5, 0xcb, 0xfb, 0xf4, 0x92, 0x7d, 0x32, 0xbe, 0x55, 0x81, 0xea, 0xda, 0x7a, 0xaa, 0x8c, 0xd6,
	0x36, 0x2c, 0xa6, 0x91, 0x05, 0x8f, 0xa0, 0xf5, 0x93, 0x04, 0x5a, 0x1f, 0xad, 0x8f, 0x50, 0x3b,
	0xa6, 0xb2, 0x08, 0xc9, 0x7d, 0xa8, 0xc6, 0x99, 0xb9, 0xf2, 0x8e, 0x53, 0x45, 0x76, 0x71, 0x0f,
	0x77, 0x74, 0xee, 0x71, 0x84, 0xb3, 0x3b, 0x4e, 0x6d, 0x20, 0xc3, 0xef, 0x34, 0xf0, 0xdc, 0x38,
	0xf0, 0x73, 0xa8, 0xa7, 0x80, 0x05, 0xc7, 0x15, 0xd0, 0x95, 0x53, 0xc0, 0x35, 0x3b, 0xa9, 0xe3,
	0x11, 0x2c, 0x1d, 0xd2, 0x80, 0xca, 0x42, 0x95, 0x4c, 0xe1, 0x95, 0xc7, 0xf1, 0x10, 0x1a, 0xe3,
	0x61, 0x04, 0xb7, 0x2e, 0xa1, 0x39, 0xe4, 0x70, 0x70, 0xd7, 0x89, 0x63, 0x7c, 0xfd, 0x2a, 0x58,
	0xef, 0x60, 0x25, 0x0b, 0x67, 0x7a, 0xd2, 0x39, 0x79, 0xfc, 0x53, 0x82, 0x95, 0x51, 0x77, 0x0b,
	0xb2, 0xfe, 0xca, 0x03, 0xb6, 0x0f, 0xab, 0x99, 0x14, 0x72, 0x27, 0xed, 0x27, 0x30, 0xd2, 0x0d,
	0x28, 0xc6, 0xdc, 0xfa, 0x05, 0x1e, 0x4f, 0xf1, 0x12, 0x1c, 0x37, 0xa1, 0xea, 0xd3, 0xc0, 0x1d,
	0x07, 0x04, 0x9f, 0x06, 0x17, 0x1a, 0xf3, 0x15, 0xac, 0x0f, 0x0b, 0xaf, 0x6d, 0x05, 0x71, 0x7f,
	0x86, 0x8d, 0x1c, 0xcf, 0xdc, 0x44, 0xdf, 0xc2, 0xd6, 0x01, 0x91, 0xde, 0x87, 0x5c, 0xe4, 0x2d,
	0xa8, 0x69, 0x28, 0x37, 0x60, 0x22, 0x82, 0x9f, 0x6b, 0x57, 0xec, 0xaa, 0xb6, 0x9d, 0x30, 0x21,
	0xad, 0x37, 0x50, 0x1f, 0xf3, 0xff, 0xa2, 0xb1, 0xff, 0x0b, 0xac, 0x59, 0x64, 0x04, 0xc7, 0x57,
	0x50, 0x4b, 0xfa, 0x3e, 0x64, 0x53, 0xdd, 0x6b, 0x8e, 0x8f, 0xb5, 0xf6, 0xb4, 0xab, 0xfa, 0xa9,
	0x22, 0x69, 0xc3, 0xba, 0xf3, 0x45, 0x15, 0xce, 0xe1, 0xbc, 0x09, 0x1b, 0x4e, 0x1e, 0x5d, 0xeb,
	0x35, 0x6c, 0xa6, 0x87, 0xe2, 0xf3, 0x3a, 0x6b, 0x41, 0x2b, 0xdf, 0x59, 0x70, 0xeb, 0x47, 0xc0,
	0x63, 0x2a, 0x1d, 0x49, 0x24, 0xf3, 0x46, 0xb2, 0x63, 0xc2, 0xc2, 0x25, 0x0b, 0xe8, 0x35, 0xe9,
	0x51, 0x1d, 0x74, 0x78, 0xb6, 0x5e, 0xc0, 0xf2, 0x3d, 0x8f, 0x1c, 0x51, 0xfb, 0xaf, 0x04, 0x4b,
	0xe7, 0x3c, 0x08, 0x89, 0x5f, 0x44, 0xd5, 0x36, 0x00, 0x58, 0x8f, 0x5c, 0x51, 0xd7, 0x27, 0x92,
	0xe8, 0x2d, 0xae, 0x28, 0xcb, 0x21, 0x91, 0x24, 0xda, 0xee, 0x5b, 0xe6, 0xab, 0x4d, 0xae, 0xdb,
	0xd1, 0x27, 0xb6, 0xa0, 0x26, 0x7a, 0xae, 0x4f, 0x07, 0xcc, 0xa3, 0x2e, 0xf3, 0xf5, 0x16, 0x83,
	0xe8, 0x1d, 0x2a, 0x53, 0xd7, 0x8f, 0x36, 0x5d, 0x84, 0xb7, 0x7d, 0x8f, 0xba, 0x09, 0xe6, 0x83,
	0x78, 0xd3, 0x63, 0xab, 0x2e, 0x06, 0xae, 0x41, 0x45, 0x3f, 0x63, 0xbe, 0x31, 0xaf, 0x00, 0x16,
	0x62, 0x43, 0xd7, 0xc7, 0x45, 0x28, 0x33, 0x6e, 0x3c, 0x54, 0x7e, 0x65, 0xc6, 0xad, 0xa7, 0xd0,
	0x18, 0xcf, 0x49, 0xf0, 0x88, 0x5b, 0xcf, 0x7f, 0xa9, 0x13, 0x8a, 0x3e, 0xb7, 0xcf, 0x01, 0x46,
	0x32, 0x89, 0x6b, 0xb0, 0xda, 0xb9, 0xe8, 0x9c, 0x75, 0x6c, 0xf7, 0xec, 0xf7, 0xd3, 0x23, 0xf7,
	0xfc, 0x37, 0xe7, 0xf4, 0xe8, 0x4d, 0xf7, 0x6d, 0xf7, 0xe8, 0xb0, 0xf1, 0x0d, 0x2e, 0xc3, 0x52,
	0xfa, 0xf2, 0xa0, 0x7b, 0xdc, 0x28, 0x61, 0x13, 0xbe, 0x4b, 0x1b, 0x9d, 0x5f, 0x3b, 0x27, 0x27,
	0x8d, 0xf2, 0xde, 0xbf, 0x0b, 0x30, 0x1f, 0xc7, 0x45, 0x47, 0x75, 0x6f, 0x42, 0x30, 0x70, 0x23,
	0x99, 0xe6, 0x4c, 0xc9, 0x37, 0x9f, 0xe4, 0x5d, 0x0b, 0x8e, 0x17, 0xb0, 0x9c, 0xa1, 0x79, 0x38,
	0x74, 0xcb, 0xd6, 0x64, 0x73, 0x33, 0xf7, 0x5e, 0x70, 0xfc, 0x13, 0x9a, 0x99, 0x02, 0x87, 0xad,
	0xc4, 0x73, 0x9a, 0x6a, 0x9a, 0x5b, 0x33, 0x5e, 0x08, 0x8e, 0x1f, 0xe0, 0xf1, 0xd4, 0xcd, 0xc7,
	0xa7, 0xf7, 0x52, 0xce, 0xd8, 0x24, 0xf3, 0x59, 0x81, 0x57, 0x82, 0xe3, 0x47, 0x78, 0x92, 0x2f,
	0x34, 0xf8, 0x43, 0x12, 0x68, 0xa6, 0x3a, 0x9a, 0xdb, 0x45, 0x9f, 0xc6, 0x29, 0x3a, 0xb3, 0x53,
	0x74, 0x0a, 0xa5, 0x98, 0x2b, 0x3b, 0x78, 0x03, 0xeb, 0x79, 0xca, 0x81, 0xcf, 0xb3, 0xfa, 0x91,
	0x85, 0xd7, 0x2e, 0xf6, 0x50, 0x09, 0x73, 0x65, 0x98, 0x3d, 0x3e, 0xba, 0xd7, 0x89, 0x28, 0x58,
	0x33, 0xc3, 0x2a, 0x38, 0xbe, 0x06, 0x18, 0x8d, 0x1c, 0x36, 0xef, 0x8f, 0x61, 0xe4, 0xbb, 0x92,
	0x65, 0x16, 0x1c, 0x3b, 0x50, 0x4b, 0x53, 0xc3, 0xd5, 0x2c, 0xc2, 0x51, 0x00, 0x23, 0xfb, 0x42,
	0x70, 0x7c, 0x07, 0x4b, 0x13, 0x82, 0x88, 0x66, 0x8a, 0xe9, 0x84, 0xb6, 0x9a, 0x6b, 0x53, 0xef,
	0x62, 0x3a, 0x69, 0x61, 0x19, 0xd1, 0x99, 0x90, 0xd0, 0x11, 0x9d, 0x49, 0x1d, 0x3a, 0xd8, 0xfe,
	0xa3, 0x7d, 0x15, 0x06, 0xe4, 0xfa, 0x6a, 0xe7, 0xe5, 0x9e, 0x94, 0x3b, 0x5e, 0xd8, 0xdb, 0x55,
	0xbf, 0xeb, 0x5e, 0x18, 0xec, 0x0a, 0xda, 0x8f, 0x54, 0x51, 0xe8, 0xff, 0xf8, 0xf7, 0xf3, 0xea,
	0x66, 0xff, 0xff, 0x00, 0x00, 0x00, 0xff, 0xff, 0x65, 0x30, 0x0e, 0x05, 0xf2, 0x0b, 0x00, 0x00,
}
