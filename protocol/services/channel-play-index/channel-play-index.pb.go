// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-play-index/channel-play-index.proto

package channel_play_index // import "golang.52tt.com/protocol/services/channel-play-index"

/*
buf:lint:ignore DIRECTORY_SAME_PACKAGE
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type UserPlayType int32

const (
	UserPlayType_USER_PLAY_TYPE_UNSPECIFIED UserPlayType = 0
	// 房间次数
	UserPlayType_USER_PLAY_TYPE_ROOM_CNT UserPlayType = 1
	// 房间时长
	UserPlayType_USER_PLAY_TYPE_ROOM_DURATION UserPlayType = 2
)

var UserPlayType_name = map[int32]string{
	0: "USER_PLAY_TYPE_UNSPECIFIED",
	1: "USER_PLAY_TYPE_ROOM_CNT",
	2: "USER_PLAY_TYPE_ROOM_DURATION",
}
var UserPlayType_value = map[string]int32{
	"USER_PLAY_TYPE_UNSPECIFIED":   0,
	"USER_PLAY_TYPE_ROOM_CNT":      1,
	"USER_PLAY_TYPE_ROOM_DURATION": 2,
}

func (x UserPlayType) String() string {
	return proto.EnumName(UserPlayType_name, int32(x))
}
func (UserPlayType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{0}
}

type TaskEnum int32

const (
	TaskEnum_TASK_ENUM_INVALID    TaskEnum = 0
	TaskEnum_TASK_ENUM_ZONE_GUIDE TaskEnum = 1
)

var TaskEnum_name = map[int32]string{
	0: "TASK_ENUM_INVALID",
	1: "TASK_ENUM_ZONE_GUIDE",
}
var TaskEnum_value = map[string]int32{
	"TASK_ENUM_INVALID":    0,
	"TASK_ENUM_ZONE_GUIDE": 1,
}

func (x TaskEnum) String() string {
	return proto.EnumName(TaskEnum_name, int32(x))
}
func (TaskEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{1}
}

type PublishRecordType int32

const (
	PublishRecordType_CATE_TYPE_UNSPECIFIED PublishRecordType = 0
	// 一起开黑
	PublishRecordType_CATE_TYPE_KAIHEI PublishRecordType = 1
)

var PublishRecordType_name = map[int32]string{
	0: "CATE_TYPE_UNSPECIFIED",
	1: "CATE_TYPE_KAIHEI",
}
var PublishRecordType_value = map[string]int32{
	"CATE_TYPE_UNSPECIFIED": 0,
	"CATE_TYPE_KAIHEI":      1,
}

func (x PublishRecordType) String() string {
	return proto.EnumName(PublishRecordType_name, int32(x))
}
func (PublishRecordType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{2}
}

type GetTodayUserPlayInfoReq struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UserPlayTypes        []UserPlayType `protobuf:"varint,2,rep,packed,name=user_play_types,json=userPlayTypes,proto3,enum=channel_play_index.UserPlayType" json:"user_play_types,omitempty"`
	Cid                  uint32         `protobuf:"varint,3,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetTodayUserPlayInfoReq) Reset()         { *m = GetTodayUserPlayInfoReq{} }
func (m *GetTodayUserPlayInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetTodayUserPlayInfoReq) ProtoMessage()    {}
func (*GetTodayUserPlayInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{0}
}
func (m *GetTodayUserPlayInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTodayUserPlayInfoReq.Unmarshal(m, b)
}
func (m *GetTodayUserPlayInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTodayUserPlayInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetTodayUserPlayInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTodayUserPlayInfoReq.Merge(dst, src)
}
func (m *GetTodayUserPlayInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetTodayUserPlayInfoReq.Size(m)
}
func (m *GetTodayUserPlayInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTodayUserPlayInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTodayUserPlayInfoReq proto.InternalMessageInfo

func (m *GetTodayUserPlayInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTodayUserPlayInfoReq) GetUserPlayTypes() []UserPlayType {
	if m != nil {
		return m.UserPlayTypes
	}
	return nil
}

func (m *GetTodayUserPlayInfoReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

type GetTodayUserPlayInfoResp struct {
	UserInRoomCnt        uint32   `protobuf:"varint,1,opt,name=user_in_room_cnt,json=userInRoomCnt,proto3" json:"user_in_room_cnt,omitempty"`
	UserInRoomDuration   uint32   `protobuf:"varint,2,opt,name=user_in_room_duration,json=userInRoomDuration,proto3" json:"user_in_room_duration,omitempty"`
	TabId                uint32   `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTodayUserPlayInfoResp) Reset()         { *m = GetTodayUserPlayInfoResp{} }
func (m *GetTodayUserPlayInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetTodayUserPlayInfoResp) ProtoMessage()    {}
func (*GetTodayUserPlayInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{1}
}
func (m *GetTodayUserPlayInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTodayUserPlayInfoResp.Unmarshal(m, b)
}
func (m *GetTodayUserPlayInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTodayUserPlayInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetTodayUserPlayInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTodayUserPlayInfoResp.Merge(dst, src)
}
func (m *GetTodayUserPlayInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetTodayUserPlayInfoResp.Size(m)
}
func (m *GetTodayUserPlayInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTodayUserPlayInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTodayUserPlayInfoResp proto.InternalMessageInfo

func (m *GetTodayUserPlayInfoResp) GetUserInRoomCnt() uint32 {
	if m != nil {
		return m.UserInRoomCnt
	}
	return 0
}

func (m *GetTodayUserPlayInfoResp) GetUserInRoomDuration() uint32 {
	if m != nil {
		return m.UserInRoomDuration
	}
	return 0
}

func (m *GetTodayUserPlayInfoResp) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type UserInRoomInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	EnterRoomTs          int64    `protobuf:"varint,2,opt,name=enter_room_ts,json=enterRoomTs,proto3" json:"enter_room_ts,omitempty"`
	ExitRoomTs           int64    `protobuf:"varint,3,opt,name=exit_room_ts,json=exitRoomTs,proto3" json:"exit_room_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInRoomInfo) Reset()         { *m = UserInRoomInfo{} }
func (m *UserInRoomInfo) String() string { return proto.CompactTextString(m) }
func (*UserInRoomInfo) ProtoMessage()    {}
func (*UserInRoomInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{2}
}
func (m *UserInRoomInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInRoomInfo.Unmarshal(m, b)
}
func (m *UserInRoomInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInRoomInfo.Marshal(b, m, deterministic)
}
func (dst *UserInRoomInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInRoomInfo.Merge(dst, src)
}
func (m *UserInRoomInfo) XXX_Size() int {
	return xxx_messageInfo_UserInRoomInfo.Size(m)
}
func (m *UserInRoomInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInRoomInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInRoomInfo proto.InternalMessageInfo

func (m *UserInRoomInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserInRoomInfo) GetEnterRoomTs() int64 {
	if m != nil {
		return m.EnterRoomTs
	}
	return 0
}

func (m *UserInRoomInfo) GetExitRoomTs() int64 {
	if m != nil {
		return m.ExitRoomTs
	}
	return 0
}

type CheckUserInRoomReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CheckUids            []uint32 `protobuf:"varint,2,rep,packed,name=check_uids,json=checkUids,proto3" json:"check_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserInRoomReq) Reset()         { *m = CheckUserInRoomReq{} }
func (m *CheckUserInRoomReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserInRoomReq) ProtoMessage()    {}
func (*CheckUserInRoomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{3}
}
func (m *CheckUserInRoomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserInRoomReq.Unmarshal(m, b)
}
func (m *CheckUserInRoomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserInRoomReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserInRoomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserInRoomReq.Merge(dst, src)
}
func (m *CheckUserInRoomReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserInRoomReq.Size(m)
}
func (m *CheckUserInRoomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserInRoomReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserInRoomReq proto.InternalMessageInfo

func (m *CheckUserInRoomReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckUserInRoomReq) GetCheckUids() []uint32 {
	if m != nil {
		return m.CheckUids
	}
	return nil
}

type ExitRoomInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ExitRoomMs           int64    `protobuf:"varint,2,opt,name=exit_room_ms,json=exitRoomMs,proto3" json:"exit_room_ms,omitempty"`
	ExitUid              uint32   `protobuf:"varint,3,opt,name=exit_uid,json=exitUid,proto3" json:"exit_uid,omitempty"`
	TabId                uint32   `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	SameRoomUid          uint32   `protobuf:"varint,5,opt,name=same_room_uid,json=sameRoomUid,proto3" json:"same_room_uid,omitempty"`
	TogetherTime         int64    `protobuf:"varint,6,opt,name=together_time,json=togetherTime,proto3" json:"together_time,omitempty"`
	EnterRoomMs          int64    `protobuf:"varint,7,opt,name=enter_room_ms,json=enterRoomMs,proto3" json:"enter_room_ms,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExitRoomInfo) Reset()         { *m = ExitRoomInfo{} }
func (m *ExitRoomInfo) String() string { return proto.CompactTextString(m) }
func (*ExitRoomInfo) ProtoMessage()    {}
func (*ExitRoomInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{4}
}
func (m *ExitRoomInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitRoomInfo.Unmarshal(m, b)
}
func (m *ExitRoomInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitRoomInfo.Marshal(b, m, deterministic)
}
func (dst *ExitRoomInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitRoomInfo.Merge(dst, src)
}
func (m *ExitRoomInfo) XXX_Size() int {
	return xxx_messageInfo_ExitRoomInfo.Size(m)
}
func (m *ExitRoomInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitRoomInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ExitRoomInfo proto.InternalMessageInfo

func (m *ExitRoomInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ExitRoomInfo) GetExitRoomMs() int64 {
	if m != nil {
		return m.ExitRoomMs
	}
	return 0
}

func (m *ExitRoomInfo) GetExitUid() uint32 {
	if m != nil {
		return m.ExitUid
	}
	return 0
}

func (m *ExitRoomInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ExitRoomInfo) GetSameRoomUid() uint32 {
	if m != nil {
		return m.SameRoomUid
	}
	return 0
}

func (m *ExitRoomInfo) GetTogetherTime() int64 {
	if m != nil {
		return m.TogetherTime
	}
	return 0
}

func (m *ExitRoomInfo) GetEnterRoomMs() int64 {
	if m != nil {
		return m.EnterRoomMs
	}
	return 0
}

type CheckUserInRoomResp struct {
	ExitRoomInfoMap      map[string]*ExitRoomInfo `protobuf:"bytes,1,rep,name=exit_room_info_map,json=exitRoomInfoMap,proto3" json:"exit_room_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *CheckUserInRoomResp) Reset()         { *m = CheckUserInRoomResp{} }
func (m *CheckUserInRoomResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserInRoomResp) ProtoMessage()    {}
func (*CheckUserInRoomResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{5}
}
func (m *CheckUserInRoomResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserInRoomResp.Unmarshal(m, b)
}
func (m *CheckUserInRoomResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserInRoomResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserInRoomResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserInRoomResp.Merge(dst, src)
}
func (m *CheckUserInRoomResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserInRoomResp.Size(m)
}
func (m *CheckUserInRoomResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserInRoomResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserInRoomResp proto.InternalMessageInfo

func (m *CheckUserInRoomResp) GetExitRoomInfoMap() map[string]*ExitRoomInfo {
	if m != nil {
		return m.ExitRoomInfoMap
	}
	return nil
}

type IsFinishTaskReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TaskType             TaskEnum `protobuf:"varint,2,opt,name=task_type,json=taskType,proto3,enum=channel_play_index.TaskEnum" json:"task_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsFinishTaskReq) Reset()         { *m = IsFinishTaskReq{} }
func (m *IsFinishTaskReq) String() string { return proto.CompactTextString(m) }
func (*IsFinishTaskReq) ProtoMessage()    {}
func (*IsFinishTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{6}
}
func (m *IsFinishTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsFinishTaskReq.Unmarshal(m, b)
}
func (m *IsFinishTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsFinishTaskReq.Marshal(b, m, deterministic)
}
func (dst *IsFinishTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsFinishTaskReq.Merge(dst, src)
}
func (m *IsFinishTaskReq) XXX_Size() int {
	return xxx_messageInfo_IsFinishTaskReq.Size(m)
}
func (m *IsFinishTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsFinishTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsFinishTaskReq proto.InternalMessageInfo

func (m *IsFinishTaskReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IsFinishTaskReq) GetTaskType() TaskEnum {
	if m != nil {
		return m.TaskType
	}
	return TaskEnum_TASK_ENUM_INVALID
}

type IsFinishTaskResp struct {
	Finish               bool     `protobuf:"varint,1,opt,name=finish,proto3" json:"finish,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsFinishTaskResp) Reset()         { *m = IsFinishTaskResp{} }
func (m *IsFinishTaskResp) String() string { return proto.CompactTextString(m) }
func (*IsFinishTaskResp) ProtoMessage()    {}
func (*IsFinishTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{7}
}
func (m *IsFinishTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsFinishTaskResp.Unmarshal(m, b)
}
func (m *IsFinishTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsFinishTaskResp.Marshal(b, m, deterministic)
}
func (dst *IsFinishTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsFinishTaskResp.Merge(dst, src)
}
func (m *IsFinishTaskResp) XXX_Size() int {
	return xxx_messageInfo_IsFinishTaskResp.Size(m)
}
func (m *IsFinishTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsFinishTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsFinishTaskResp proto.InternalMessageInfo

func (m *IsFinishTaskResp) GetFinish() bool {
	if m != nil {
		return m.Finish
	}
	return false
}

type BatchGetDaysContentNumReq struct {
	TabIds               []uint32 `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetDaysContentNumReq) Reset()         { *m = BatchGetDaysContentNumReq{} }
func (m *BatchGetDaysContentNumReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetDaysContentNumReq) ProtoMessage()    {}
func (*BatchGetDaysContentNumReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{8}
}
func (m *BatchGetDaysContentNumReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetDaysContentNumReq.Unmarshal(m, b)
}
func (m *BatchGetDaysContentNumReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetDaysContentNumReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetDaysContentNumReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetDaysContentNumReq.Merge(dst, src)
}
func (m *BatchGetDaysContentNumReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetDaysContentNumReq.Size(m)
}
func (m *BatchGetDaysContentNumReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetDaysContentNumReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetDaysContentNumReq proto.InternalMessageInfo

func (m *BatchGetDaysContentNumReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type BatchGetDaysContentNumResp struct {
	ContentNumMap        map[uint32]uint32 `protobuf:"bytes,1,rep,name=content_num_map,json=contentNumMap,proto3" json:"content_num_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetDaysContentNumResp) Reset()         { *m = BatchGetDaysContentNumResp{} }
func (m *BatchGetDaysContentNumResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetDaysContentNumResp) ProtoMessage()    {}
func (*BatchGetDaysContentNumResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{9}
}
func (m *BatchGetDaysContentNumResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetDaysContentNumResp.Unmarshal(m, b)
}
func (m *BatchGetDaysContentNumResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetDaysContentNumResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetDaysContentNumResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetDaysContentNumResp.Merge(dst, src)
}
func (m *BatchGetDaysContentNumResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetDaysContentNumResp.Size(m)
}
func (m *BatchGetDaysContentNumResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetDaysContentNumResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetDaysContentNumResp proto.InternalMessageInfo

func (m *BatchGetDaysContentNumResp) GetContentNumMap() map[uint32]uint32 {
	if m != nil {
		return m.ContentNumMap
	}
	return nil
}

type GetTodayDistrictPlayInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTodayDistrictPlayInfoReq) Reset()         { *m = GetTodayDistrictPlayInfoReq{} }
func (m *GetTodayDistrictPlayInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetTodayDistrictPlayInfoReq) ProtoMessage()    {}
func (*GetTodayDistrictPlayInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{10}
}
func (m *GetTodayDistrictPlayInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTodayDistrictPlayInfoReq.Unmarshal(m, b)
}
func (m *GetTodayDistrictPlayInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTodayDistrictPlayInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetTodayDistrictPlayInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTodayDistrictPlayInfoReq.Merge(dst, src)
}
func (m *GetTodayDistrictPlayInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetTodayDistrictPlayInfoReq.Size(m)
}
func (m *GetTodayDistrictPlayInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTodayDistrictPlayInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTodayDistrictPlayInfoReq proto.InternalMessageInfo

func (m *GetTodayDistrictPlayInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTodayDistrictPlayInfoReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetTodayDistrictPlayInfoResp struct {
	UserInGameDistrictDuration uint32   `protobuf:"varint,1,opt,name=user_in_game_district_duration,json=userInGameDistrictDuration,proto3" json:"user_in_game_district_duration,omitempty"`
	XXX_NoUnkeyedLiteral       struct{} `json:"-"`
	XXX_unrecognized           []byte   `json:"-"`
	XXX_sizecache              int32    `json:"-"`
}

func (m *GetTodayDistrictPlayInfoResp) Reset()         { *m = GetTodayDistrictPlayInfoResp{} }
func (m *GetTodayDistrictPlayInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetTodayDistrictPlayInfoResp) ProtoMessage()    {}
func (*GetTodayDistrictPlayInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{11}
}
func (m *GetTodayDistrictPlayInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTodayDistrictPlayInfoResp.Unmarshal(m, b)
}
func (m *GetTodayDistrictPlayInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTodayDistrictPlayInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetTodayDistrictPlayInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTodayDistrictPlayInfoResp.Merge(dst, src)
}
func (m *GetTodayDistrictPlayInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetTodayDistrictPlayInfoResp.Size(m)
}
func (m *GetTodayDistrictPlayInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTodayDistrictPlayInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTodayDistrictPlayInfoResp proto.InternalMessageInfo

func (m *GetTodayDistrictPlayInfoResp) GetUserInGameDistrictDuration() uint32 {
	if m != nil {
		return m.UserInGameDistrictDuration
	}
	return 0
}

type CheckUserTodaySendMsgReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CheckUids            []uint32 `protobuf:"varint,2,rep,packed,name=check_uids,json=checkUids,proto3" json:"check_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserTodaySendMsgReq) Reset()         { *m = CheckUserTodaySendMsgReq{} }
func (m *CheckUserTodaySendMsgReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserTodaySendMsgReq) ProtoMessage()    {}
func (*CheckUserTodaySendMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{12}
}
func (m *CheckUserTodaySendMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserTodaySendMsgReq.Unmarshal(m, b)
}
func (m *CheckUserTodaySendMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserTodaySendMsgReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserTodaySendMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserTodaySendMsgReq.Merge(dst, src)
}
func (m *CheckUserTodaySendMsgReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserTodaySendMsgReq.Size(m)
}
func (m *CheckUserTodaySendMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserTodaySendMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserTodaySendMsgReq proto.InternalMessageInfo

func (m *CheckUserTodaySendMsgReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckUserTodaySendMsgReq) GetCheckUids() []uint32 {
	if m != nil {
		return m.CheckUids
	}
	return nil
}

type CheckUserTodaySendMsgResp struct {
	ExistUids            []uint32 `protobuf:"varint,1,rep,packed,name=exist_uids,json=existUids,proto3" json:"exist_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserTodaySendMsgResp) Reset()         { *m = CheckUserTodaySendMsgResp{} }
func (m *CheckUserTodaySendMsgResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserTodaySendMsgResp) ProtoMessage()    {}
func (*CheckUserTodaySendMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{13}
}
func (m *CheckUserTodaySendMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserTodaySendMsgResp.Unmarshal(m, b)
}
func (m *CheckUserTodaySendMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserTodaySendMsgResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserTodaySendMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserTodaySendMsgResp.Merge(dst, src)
}
func (m *CheckUserTodaySendMsgResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserTodaySendMsgResp.Size(m)
}
func (m *CheckUserTodaySendMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserTodaySendMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserTodaySendMsgResp proto.InternalMessageInfo

func (m *CheckUserTodaySendMsgResp) GetExistUids() []uint32 {
	if m != nil {
		return m.ExistUids
	}
	return nil
}

type GetUserTodayChatUidsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTodayChatUidsReq) Reset()         { *m = GetUserTodayChatUidsReq{} }
func (m *GetUserTodayChatUidsReq) String() string { return proto.CompactTextString(m) }
func (*GetUserTodayChatUidsReq) ProtoMessage()    {}
func (*GetUserTodayChatUidsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{14}
}
func (m *GetUserTodayChatUidsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTodayChatUidsReq.Unmarshal(m, b)
}
func (m *GetUserTodayChatUidsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTodayChatUidsReq.Marshal(b, m, deterministic)
}
func (dst *GetUserTodayChatUidsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTodayChatUidsReq.Merge(dst, src)
}
func (m *GetUserTodayChatUidsReq) XXX_Size() int {
	return xxx_messageInfo_GetUserTodayChatUidsReq.Size(m)
}
func (m *GetUserTodayChatUidsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTodayChatUidsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTodayChatUidsReq proto.InternalMessageInfo

func (m *GetUserTodayChatUidsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserTodayChatUidsResp struct {
	ChatUids             []uint32 `protobuf:"varint,1,rep,packed,name=chat_uids,json=chatUids,proto3" json:"chat_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTodayChatUidsResp) Reset()         { *m = GetUserTodayChatUidsResp{} }
func (m *GetUserTodayChatUidsResp) String() string { return proto.CompactTextString(m) }
func (*GetUserTodayChatUidsResp) ProtoMessage()    {}
func (*GetUserTodayChatUidsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{15}
}
func (m *GetUserTodayChatUidsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTodayChatUidsResp.Unmarshal(m, b)
}
func (m *GetUserTodayChatUidsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTodayChatUidsResp.Marshal(b, m, deterministic)
}
func (dst *GetUserTodayChatUidsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTodayChatUidsResp.Merge(dst, src)
}
func (m *GetUserTodayChatUidsResp) XXX_Size() int {
	return xxx_messageInfo_GetUserTodayChatUidsResp.Size(m)
}
func (m *GetUserTodayChatUidsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTodayChatUidsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTodayChatUidsResp proto.InternalMessageInfo

func (m *GetUserTodayChatUidsResp) GetChatUids() []uint32 {
	if m != nil {
		return m.ChatUids
	}
	return nil
}

type IsUserTodayAddNewFriendReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsUserTodayAddNewFriendReq) Reset()         { *m = IsUserTodayAddNewFriendReq{} }
func (m *IsUserTodayAddNewFriendReq) String() string { return proto.CompactTextString(m) }
func (*IsUserTodayAddNewFriendReq) ProtoMessage()    {}
func (*IsUserTodayAddNewFriendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{16}
}
func (m *IsUserTodayAddNewFriendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsUserTodayAddNewFriendReq.Unmarshal(m, b)
}
func (m *IsUserTodayAddNewFriendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsUserTodayAddNewFriendReq.Marshal(b, m, deterministic)
}
func (dst *IsUserTodayAddNewFriendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsUserTodayAddNewFriendReq.Merge(dst, src)
}
func (m *IsUserTodayAddNewFriendReq) XXX_Size() int {
	return xxx_messageInfo_IsUserTodayAddNewFriendReq.Size(m)
}
func (m *IsUserTodayAddNewFriendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsUserTodayAddNewFriendReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsUserTodayAddNewFriendReq proto.InternalMessageInfo

func (m *IsUserTodayAddNewFriendReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type IsUserTodayAddNewFriendResp struct {
	IsAddNewFriend       bool     `protobuf:"varint,1,opt,name=is_add_new_friend,json=isAddNewFriend,proto3" json:"is_add_new_friend,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsUserTodayAddNewFriendResp) Reset()         { *m = IsUserTodayAddNewFriendResp{} }
func (m *IsUserTodayAddNewFriendResp) String() string { return proto.CompactTextString(m) }
func (*IsUserTodayAddNewFriendResp) ProtoMessage()    {}
func (*IsUserTodayAddNewFriendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{17}
}
func (m *IsUserTodayAddNewFriendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsUserTodayAddNewFriendResp.Unmarshal(m, b)
}
func (m *IsUserTodayAddNewFriendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsUserTodayAddNewFriendResp.Marshal(b, m, deterministic)
}
func (dst *IsUserTodayAddNewFriendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsUserTodayAddNewFriendResp.Merge(dst, src)
}
func (m *IsUserTodayAddNewFriendResp) XXX_Size() int {
	return xxx_messageInfo_IsUserTodayAddNewFriendResp.Size(m)
}
func (m *IsUserTodayAddNewFriendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsUserTodayAddNewFriendResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsUserTodayAddNewFriendResp proto.InternalMessageInfo

func (m *IsUserTodayAddNewFriendResp) GetIsAddNewFriend() bool {
	if m != nil {
		return m.IsAddNewFriend
	}
	return false
}

type GetTabsRoomCountReq struct {
	TabIds               []uint32 `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTabsRoomCountReq) Reset()         { *m = GetTabsRoomCountReq{} }
func (m *GetTabsRoomCountReq) String() string { return proto.CompactTextString(m) }
func (*GetTabsRoomCountReq) ProtoMessage()    {}
func (*GetTabsRoomCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{18}
}
func (m *GetTabsRoomCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabsRoomCountReq.Unmarshal(m, b)
}
func (m *GetTabsRoomCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabsRoomCountReq.Marshal(b, m, deterministic)
}
func (dst *GetTabsRoomCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabsRoomCountReq.Merge(dst, src)
}
func (m *GetTabsRoomCountReq) XXX_Size() int {
	return xxx_messageInfo_GetTabsRoomCountReq.Size(m)
}
func (m *GetTabsRoomCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabsRoomCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabsRoomCountReq proto.InternalMessageInfo

func (m *GetTabsRoomCountReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type GetTabsRoomCountResp struct {
	CountMap             map[uint32]int64 `protobuf:"bytes,1,rep,name=count_map,json=countMap,proto3" json:"count_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetTabsRoomCountResp) Reset()         { *m = GetTabsRoomCountResp{} }
func (m *GetTabsRoomCountResp) String() string { return proto.CompactTextString(m) }
func (*GetTabsRoomCountResp) ProtoMessage()    {}
func (*GetTabsRoomCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{19}
}
func (m *GetTabsRoomCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabsRoomCountResp.Unmarshal(m, b)
}
func (m *GetTabsRoomCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabsRoomCountResp.Marshal(b, m, deterministic)
}
func (dst *GetTabsRoomCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabsRoomCountResp.Merge(dst, src)
}
func (m *GetTabsRoomCountResp) XXX_Size() int {
	return xxx_messageInfo_GetTabsRoomCountResp.Size(m)
}
func (m *GetTabsRoomCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabsRoomCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabsRoomCountResp proto.InternalMessageInfo

func (m *GetTabsRoomCountResp) GetCountMap() map[uint32]int64 {
	if m != nil {
		return m.CountMap
	}
	return nil
}

type SetPublishTabCntByOneDayReq struct {
	TabId                uint32            `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Uid                  uint32            `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	PublishTime          int64             `protobuf:"varint,3,opt,name=publish_time,json=publishTime,proto3" json:"publish_time,omitempty"`
	PublishType          PublishRecordType `protobuf:"varint,4,opt,name=publish_type,json=publishType,proto3,enum=channel_play_index.PublishRecordType" json:"publish_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SetPublishTabCntByOneDayReq) Reset()         { *m = SetPublishTabCntByOneDayReq{} }
func (m *SetPublishTabCntByOneDayReq) String() string { return proto.CompactTextString(m) }
func (*SetPublishTabCntByOneDayReq) ProtoMessage()    {}
func (*SetPublishTabCntByOneDayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{20}
}
func (m *SetPublishTabCntByOneDayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPublishTabCntByOneDayReq.Unmarshal(m, b)
}
func (m *SetPublishTabCntByOneDayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPublishTabCntByOneDayReq.Marshal(b, m, deterministic)
}
func (dst *SetPublishTabCntByOneDayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPublishTabCntByOneDayReq.Merge(dst, src)
}
func (m *SetPublishTabCntByOneDayReq) XXX_Size() int {
	return xxx_messageInfo_SetPublishTabCntByOneDayReq.Size(m)
}
func (m *SetPublishTabCntByOneDayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPublishTabCntByOneDayReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPublishTabCntByOneDayReq proto.InternalMessageInfo

func (m *SetPublishTabCntByOneDayReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SetPublishTabCntByOneDayReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetPublishTabCntByOneDayReq) GetPublishTime() int64 {
	if m != nil {
		return m.PublishTime
	}
	return 0
}

func (m *SetPublishTabCntByOneDayReq) GetPublishType() PublishRecordType {
	if m != nil {
		return m.PublishType
	}
	return PublishRecordType_CATE_TYPE_UNSPECIFIED
}

type SetPublishTabCntByOneDayResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPublishTabCntByOneDayResp) Reset()         { *m = SetPublishTabCntByOneDayResp{} }
func (m *SetPublishTabCntByOneDayResp) String() string { return proto.CompactTextString(m) }
func (*SetPublishTabCntByOneDayResp) ProtoMessage()    {}
func (*SetPublishTabCntByOneDayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{21}
}
func (m *SetPublishTabCntByOneDayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPublishTabCntByOneDayResp.Unmarshal(m, b)
}
func (m *SetPublishTabCntByOneDayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPublishTabCntByOneDayResp.Marshal(b, m, deterministic)
}
func (dst *SetPublishTabCntByOneDayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPublishTabCntByOneDayResp.Merge(dst, src)
}
func (m *SetPublishTabCntByOneDayResp) XXX_Size() int {
	return xxx_messageInfo_SetPublishTabCntByOneDayResp.Size(m)
}
func (m *SetPublishTabCntByOneDayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPublishTabCntByOneDayResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPublishTabCntByOneDayResp proto.InternalMessageInfo

type GetPublishTabCntListReq struct {
	QueryTime            int64             `protobuf:"varint,1,opt,name=query_time,json=queryTime,proto3" json:"query_time,omitempty"`
	LimitCnt             uint32            `protobuf:"varint,2,opt,name=limit_cnt,json=limitCnt,proto3" json:"limit_cnt,omitempty"`
	PublishType          PublishRecordType `protobuf:"varint,3,opt,name=publish_type,json=publishType,proto3,enum=channel_play_index.PublishRecordType" json:"publish_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPublishTabCntListReq) Reset()         { *m = GetPublishTabCntListReq{} }
func (m *GetPublishTabCntListReq) String() string { return proto.CompactTextString(m) }
func (*GetPublishTabCntListReq) ProtoMessage()    {}
func (*GetPublishTabCntListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{22}
}
func (m *GetPublishTabCntListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublishTabCntListReq.Unmarshal(m, b)
}
func (m *GetPublishTabCntListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublishTabCntListReq.Marshal(b, m, deterministic)
}
func (dst *GetPublishTabCntListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublishTabCntListReq.Merge(dst, src)
}
func (m *GetPublishTabCntListReq) XXX_Size() int {
	return xxx_messageInfo_GetPublishTabCntListReq.Size(m)
}
func (m *GetPublishTabCntListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublishTabCntListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublishTabCntListReq proto.InternalMessageInfo

func (m *GetPublishTabCntListReq) GetQueryTime() int64 {
	if m != nil {
		return m.QueryTime
	}
	return 0
}

func (m *GetPublishTabCntListReq) GetLimitCnt() uint32 {
	if m != nil {
		return m.LimitCnt
	}
	return 0
}

func (m *GetPublishTabCntListReq) GetPublishType() PublishRecordType {
	if m != nil {
		return m.PublishType
	}
	return PublishRecordType_CATE_TYPE_UNSPECIFIED
}

type GetPublishTabCntListResp struct {
	TabIds               []uint32 `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPublishTabCntListResp) Reset()         { *m = GetPublishTabCntListResp{} }
func (m *GetPublishTabCntListResp) String() string { return proto.CompactTextString(m) }
func (*GetPublishTabCntListResp) ProtoMessage()    {}
func (*GetPublishTabCntListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{23}
}
func (m *GetPublishTabCntListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublishTabCntListResp.Unmarshal(m, b)
}
func (m *GetPublishTabCntListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublishTabCntListResp.Marshal(b, m, deterministic)
}
func (dst *GetPublishTabCntListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublishTabCntListResp.Merge(dst, src)
}
func (m *GetPublishTabCntListResp) XXX_Size() int {
	return xxx_messageInfo_GetPublishTabCntListResp.Size(m)
}
func (m *GetPublishTabCntListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublishTabCntListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublishTabCntListResp proto.InternalMessageInfo

func (m *GetPublishTabCntListResp) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type GetUserDayRoomDurationReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDayRoomDurationReq) Reset()         { *m = GetUserDayRoomDurationReq{} }
func (m *GetUserDayRoomDurationReq) String() string { return proto.CompactTextString(m) }
func (*GetUserDayRoomDurationReq) ProtoMessage()    {}
func (*GetUserDayRoomDurationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{24}
}
func (m *GetUserDayRoomDurationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDayRoomDurationReq.Unmarshal(m, b)
}
func (m *GetUserDayRoomDurationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDayRoomDurationReq.Marshal(b, m, deterministic)
}
func (dst *GetUserDayRoomDurationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDayRoomDurationReq.Merge(dst, src)
}
func (m *GetUserDayRoomDurationReq) XXX_Size() int {
	return xxx_messageInfo_GetUserDayRoomDurationReq.Size(m)
}
func (m *GetUserDayRoomDurationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDayRoomDurationReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDayRoomDurationReq proto.InternalMessageInfo

func (m *GetUserDayRoomDurationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserDayRoomDurationResp struct {
	RoomDuration         int64    `protobuf:"varint,1,opt,name=room_duration,json=roomDuration,proto3" json:"room_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDayRoomDurationResp) Reset()         { *m = GetUserDayRoomDurationResp{} }
func (m *GetUserDayRoomDurationResp) String() string { return proto.CompactTextString(m) }
func (*GetUserDayRoomDurationResp) ProtoMessage()    {}
func (*GetUserDayRoomDurationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{25}
}
func (m *GetUserDayRoomDurationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDayRoomDurationResp.Unmarshal(m, b)
}
func (m *GetUserDayRoomDurationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDayRoomDurationResp.Marshal(b, m, deterministic)
}
func (dst *GetUserDayRoomDurationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDayRoomDurationResp.Merge(dst, src)
}
func (m *GetUserDayRoomDurationResp) XXX_Size() int {
	return xxx_messageInfo_GetUserDayRoomDurationResp.Size(m)
}
func (m *GetUserDayRoomDurationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDayRoomDurationResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDayRoomDurationResp proto.InternalMessageInfo

func (m *GetUserDayRoomDurationResp) GetRoomDuration() int64 {
	if m != nil {
		return m.RoomDuration
	}
	return 0
}

type GetUserDayOnlineDurationReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDayOnlineDurationReq) Reset()         { *m = GetUserDayOnlineDurationReq{} }
func (m *GetUserDayOnlineDurationReq) String() string { return proto.CompactTextString(m) }
func (*GetUserDayOnlineDurationReq) ProtoMessage()    {}
func (*GetUserDayOnlineDurationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{26}
}
func (m *GetUserDayOnlineDurationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDayOnlineDurationReq.Unmarshal(m, b)
}
func (m *GetUserDayOnlineDurationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDayOnlineDurationReq.Marshal(b, m, deterministic)
}
func (dst *GetUserDayOnlineDurationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDayOnlineDurationReq.Merge(dst, src)
}
func (m *GetUserDayOnlineDurationReq) XXX_Size() int {
	return xxx_messageInfo_GetUserDayOnlineDurationReq.Size(m)
}
func (m *GetUserDayOnlineDurationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDayOnlineDurationReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDayOnlineDurationReq proto.InternalMessageInfo

func (m *GetUserDayOnlineDurationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserDayOnlineDurationResp struct {
	OnlineDuration       int64    `protobuf:"varint,1,opt,name=online_duration,json=onlineDuration,proto3" json:"online_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserDayOnlineDurationResp) Reset()         { *m = GetUserDayOnlineDurationResp{} }
func (m *GetUserDayOnlineDurationResp) String() string { return proto.CompactTextString(m) }
func (*GetUserDayOnlineDurationResp) ProtoMessage()    {}
func (*GetUserDayOnlineDurationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{27}
}
func (m *GetUserDayOnlineDurationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDayOnlineDurationResp.Unmarshal(m, b)
}
func (m *GetUserDayOnlineDurationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDayOnlineDurationResp.Marshal(b, m, deterministic)
}
func (dst *GetUserDayOnlineDurationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDayOnlineDurationResp.Merge(dst, src)
}
func (m *GetUserDayOnlineDurationResp) XXX_Size() int {
	return xxx_messageInfo_GetUserDayOnlineDurationResp.Size(m)
}
func (m *GetUserDayOnlineDurationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDayOnlineDurationResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDayOnlineDurationResp proto.InternalMessageInfo

func (m *GetUserDayOnlineDurationResp) GetOnlineDuration() int64 {
	if m != nil {
		return m.OnlineDuration
	}
	return 0
}

type IsTodayHasFollowOrBeFollowReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsTodayHasFollowOrBeFollowReq) Reset()         { *m = IsTodayHasFollowOrBeFollowReq{} }
func (m *IsTodayHasFollowOrBeFollowReq) String() string { return proto.CompactTextString(m) }
func (*IsTodayHasFollowOrBeFollowReq) ProtoMessage()    {}
func (*IsTodayHasFollowOrBeFollowReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{28}
}
func (m *IsTodayHasFollowOrBeFollowReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsTodayHasFollowOrBeFollowReq.Unmarshal(m, b)
}
func (m *IsTodayHasFollowOrBeFollowReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsTodayHasFollowOrBeFollowReq.Marshal(b, m, deterministic)
}
func (dst *IsTodayHasFollowOrBeFollowReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsTodayHasFollowOrBeFollowReq.Merge(dst, src)
}
func (m *IsTodayHasFollowOrBeFollowReq) XXX_Size() int {
	return xxx_messageInfo_IsTodayHasFollowOrBeFollowReq.Size(m)
}
func (m *IsTodayHasFollowOrBeFollowReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsTodayHasFollowOrBeFollowReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsTodayHasFollowOrBeFollowReq proto.InternalMessageInfo

func (m *IsTodayHasFollowOrBeFollowReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type IsTodayHasFollowOrBeFollowResp struct {
	FollowStatus         bool     `protobuf:"varint,1,opt,name=follow_status,json=followStatus,proto3" json:"follow_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsTodayHasFollowOrBeFollowResp) Reset()         { *m = IsTodayHasFollowOrBeFollowResp{} }
func (m *IsTodayHasFollowOrBeFollowResp) String() string { return proto.CompactTextString(m) }
func (*IsTodayHasFollowOrBeFollowResp) ProtoMessage()    {}
func (*IsTodayHasFollowOrBeFollowResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{29}
}
func (m *IsTodayHasFollowOrBeFollowResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsTodayHasFollowOrBeFollowResp.Unmarshal(m, b)
}
func (m *IsTodayHasFollowOrBeFollowResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsTodayHasFollowOrBeFollowResp.Marshal(b, m, deterministic)
}
func (dst *IsTodayHasFollowOrBeFollowResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsTodayHasFollowOrBeFollowResp.Merge(dst, src)
}
func (m *IsTodayHasFollowOrBeFollowResp) XXX_Size() int {
	return xxx_messageInfo_IsTodayHasFollowOrBeFollowResp.Size(m)
}
func (m *IsTodayHasFollowOrBeFollowResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsTodayHasFollowOrBeFollowResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsTodayHasFollowOrBeFollowResp proto.InternalMessageInfo

func (m *IsTodayHasFollowOrBeFollowResp) GetFollowStatus() bool {
	if m != nil {
		return m.FollowStatus
	}
	return false
}

type GetRoomUidsRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRoomUidsRequest) Reset()         { *m = GetRoomUidsRequest{} }
func (m *GetRoomUidsRequest) String() string { return proto.CompactTextString(m) }
func (*GetRoomUidsRequest) ProtoMessage()    {}
func (*GetRoomUidsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{30}
}
func (m *GetRoomUidsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoomUidsRequest.Unmarshal(m, b)
}
func (m *GetRoomUidsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoomUidsRequest.Marshal(b, m, deterministic)
}
func (dst *GetRoomUidsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoomUidsRequest.Merge(dst, src)
}
func (m *GetRoomUidsRequest) XXX_Size() int {
	return xxx_messageInfo_GetRoomUidsRequest.Size(m)
}
func (m *GetRoomUidsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoomUidsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoomUidsRequest proto.InternalMessageInfo

func (m *GetRoomUidsRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetRoomUidsResponse struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRoomUidsResponse) Reset()         { *m = GetRoomUidsResponse{} }
func (m *GetRoomUidsResponse) String() string { return proto.CompactTextString(m) }
func (*GetRoomUidsResponse) ProtoMessage()    {}
func (*GetRoomUidsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_index_4bedfa7ae545262a, []int{31}
}
func (m *GetRoomUidsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoomUidsResponse.Unmarshal(m, b)
}
func (m *GetRoomUidsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoomUidsResponse.Marshal(b, m, deterministic)
}
func (dst *GetRoomUidsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoomUidsResponse.Merge(dst, src)
}
func (m *GetRoomUidsResponse) XXX_Size() int {
	return xxx_messageInfo_GetRoomUidsResponse.Size(m)
}
func (m *GetRoomUidsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoomUidsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoomUidsResponse proto.InternalMessageInfo

func (m *GetRoomUidsResponse) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func init() {
	proto.RegisterType((*GetTodayUserPlayInfoReq)(nil), "channel_play_index.GetTodayUserPlayInfoReq")
	proto.RegisterType((*GetTodayUserPlayInfoResp)(nil), "channel_play_index.GetTodayUserPlayInfoResp")
	proto.RegisterType((*UserInRoomInfo)(nil), "channel_play_index.UserInRoomInfo")
	proto.RegisterType((*CheckUserInRoomReq)(nil), "channel_play_index.CheckUserInRoomReq")
	proto.RegisterType((*ExitRoomInfo)(nil), "channel_play_index.ExitRoomInfo")
	proto.RegisterType((*CheckUserInRoomResp)(nil), "channel_play_index.CheckUserInRoomResp")
	proto.RegisterMapType((map[string]*ExitRoomInfo)(nil), "channel_play_index.CheckUserInRoomResp.ExitRoomInfoMapEntry")
	proto.RegisterType((*IsFinishTaskReq)(nil), "channel_play_index.IsFinishTaskReq")
	proto.RegisterType((*IsFinishTaskResp)(nil), "channel_play_index.IsFinishTaskResp")
	proto.RegisterType((*BatchGetDaysContentNumReq)(nil), "channel_play_index.BatchGetDaysContentNumReq")
	proto.RegisterType((*BatchGetDaysContentNumResp)(nil), "channel_play_index.BatchGetDaysContentNumResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "channel_play_index.BatchGetDaysContentNumResp.ContentNumMapEntry")
	proto.RegisterType((*GetTodayDistrictPlayInfoReq)(nil), "channel_play_index.GetTodayDistrictPlayInfoReq")
	proto.RegisterType((*GetTodayDistrictPlayInfoResp)(nil), "channel_play_index.GetTodayDistrictPlayInfoResp")
	proto.RegisterType((*CheckUserTodaySendMsgReq)(nil), "channel_play_index.CheckUserTodaySendMsgReq")
	proto.RegisterType((*CheckUserTodaySendMsgResp)(nil), "channel_play_index.CheckUserTodaySendMsgResp")
	proto.RegisterType((*GetUserTodayChatUidsReq)(nil), "channel_play_index.GetUserTodayChatUidsReq")
	proto.RegisterType((*GetUserTodayChatUidsResp)(nil), "channel_play_index.GetUserTodayChatUidsResp")
	proto.RegisterType((*IsUserTodayAddNewFriendReq)(nil), "channel_play_index.IsUserTodayAddNewFriendReq")
	proto.RegisterType((*IsUserTodayAddNewFriendResp)(nil), "channel_play_index.IsUserTodayAddNewFriendResp")
	proto.RegisterType((*GetTabsRoomCountReq)(nil), "channel_play_index.GetTabsRoomCountReq")
	proto.RegisterType((*GetTabsRoomCountResp)(nil), "channel_play_index.GetTabsRoomCountResp")
	proto.RegisterMapType((map[uint32]int64)(nil), "channel_play_index.GetTabsRoomCountResp.CountMapEntry")
	proto.RegisterType((*SetPublishTabCntByOneDayReq)(nil), "channel_play_index.SetPublishTabCntByOneDayReq")
	proto.RegisterType((*SetPublishTabCntByOneDayResp)(nil), "channel_play_index.SetPublishTabCntByOneDayResp")
	proto.RegisterType((*GetPublishTabCntListReq)(nil), "channel_play_index.GetPublishTabCntListReq")
	proto.RegisterType((*GetPublishTabCntListResp)(nil), "channel_play_index.GetPublishTabCntListResp")
	proto.RegisterType((*GetUserDayRoomDurationReq)(nil), "channel_play_index.GetUserDayRoomDurationReq")
	proto.RegisterType((*GetUserDayRoomDurationResp)(nil), "channel_play_index.GetUserDayRoomDurationResp")
	proto.RegisterType((*GetUserDayOnlineDurationReq)(nil), "channel_play_index.GetUserDayOnlineDurationReq")
	proto.RegisterType((*GetUserDayOnlineDurationResp)(nil), "channel_play_index.GetUserDayOnlineDurationResp")
	proto.RegisterType((*IsTodayHasFollowOrBeFollowReq)(nil), "channel_play_index.IsTodayHasFollowOrBeFollowReq")
	proto.RegisterType((*IsTodayHasFollowOrBeFollowResp)(nil), "channel_play_index.IsTodayHasFollowOrBeFollowResp")
	proto.RegisterType((*GetRoomUidsRequest)(nil), "channel_play_index.GetRoomUidsRequest")
	proto.RegisterType((*GetRoomUidsResponse)(nil), "channel_play_index.GetRoomUidsResponse")
	proto.RegisterEnum("channel_play_index.UserPlayType", UserPlayType_name, UserPlayType_value)
	proto.RegisterEnum("channel_play_index.TaskEnum", TaskEnum_name, TaskEnum_value)
	proto.RegisterEnum("channel_play_index.PublishRecordType", PublishRecordType_name, PublishRecordType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelPlayIndexClient is the client API for ChannelPlayIndex service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelPlayIndexClient interface {
	GetTodayUserPlayInfo(ctx context.Context, in *GetTodayUserPlayInfoReq, opts ...grpc.CallOption) (*GetTodayUserPlayInfoResp, error)
	CheckUserInRoom(ctx context.Context, in *CheckUserInRoomReq, opts ...grpc.CallOption) (*CheckUserInRoomResp, error)
	IsFinishTask(ctx context.Context, in *IsFinishTaskReq, opts ...grpc.CallOption) (*IsFinishTaskResp, error)
	BatchGetDaysContentNum(ctx context.Context, in *BatchGetDaysContentNumReq, opts ...grpc.CallOption) (*BatchGetDaysContentNumResp, error)
	GetTodayDistrictPlayInfo(ctx context.Context, in *GetTodayDistrictPlayInfoReq, opts ...grpc.CallOption) (*GetTodayDistrictPlayInfoResp, error)
	CheckUserTodaySendMsg(ctx context.Context, in *CheckUserTodaySendMsgReq, opts ...grpc.CallOption) (*CheckUserTodaySendMsgResp, error)
	GetUserTodayChatUids(ctx context.Context, in *GetUserTodayChatUidsReq, opts ...grpc.CallOption) (*GetUserTodayChatUidsResp, error)
	IsUserTodayAddNewFriend(ctx context.Context, in *IsUserTodayAddNewFriendReq, opts ...grpc.CallOption) (*IsUserTodayAddNewFriendResp, error)
	GetTabsRoomCount(ctx context.Context, in *GetTabsRoomCountReq, opts ...grpc.CallOption) (*GetTabsRoomCountResp, error)
	SetPublishTabCntByOneDay(ctx context.Context, in *SetPublishTabCntByOneDayReq, opts ...grpc.CallOption) (*SetPublishTabCntByOneDayResp, error)
	GetPublishTabCntList(ctx context.Context, in *GetPublishTabCntListReq, opts ...grpc.CallOption) (*GetPublishTabCntListResp, error)
	GetUserDayRoomDuration(ctx context.Context, in *GetUserDayRoomDurationReq, opts ...grpc.CallOption) (*GetUserDayRoomDurationResp, error)
	GetUserDayOnlineDuration(ctx context.Context, in *GetUserDayOnlineDurationReq, opts ...grpc.CallOption) (*GetUserDayOnlineDurationResp, error)
	IsTodayHasFollowOrBeFollow(ctx context.Context, in *IsTodayHasFollowOrBeFollowReq, opts ...grpc.CallOption) (*IsTodayHasFollowOrBeFollowResp, error)
	GetRoomUids(ctx context.Context, in *GetRoomUidsRequest, opts ...grpc.CallOption) (*GetRoomUidsResponse, error)
}

type channelPlayIndexClient struct {
	cc *grpc.ClientConn
}

func NewChannelPlayIndexClient(cc *grpc.ClientConn) ChannelPlayIndexClient {
	return &channelPlayIndexClient{cc}
}

func (c *channelPlayIndexClient) GetTodayUserPlayInfo(ctx context.Context, in *GetTodayUserPlayInfoReq, opts ...grpc.CallOption) (*GetTodayUserPlayInfoResp, error) {
	out := new(GetTodayUserPlayInfoResp)
	err := c.cc.Invoke(ctx, "/channel_play_index.ChannelPlayIndex/GetTodayUserPlayInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayIndexClient) CheckUserInRoom(ctx context.Context, in *CheckUserInRoomReq, opts ...grpc.CallOption) (*CheckUserInRoomResp, error) {
	out := new(CheckUserInRoomResp)
	err := c.cc.Invoke(ctx, "/channel_play_index.ChannelPlayIndex/CheckUserInRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayIndexClient) IsFinishTask(ctx context.Context, in *IsFinishTaskReq, opts ...grpc.CallOption) (*IsFinishTaskResp, error) {
	out := new(IsFinishTaskResp)
	err := c.cc.Invoke(ctx, "/channel_play_index.ChannelPlayIndex/IsFinishTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayIndexClient) BatchGetDaysContentNum(ctx context.Context, in *BatchGetDaysContentNumReq, opts ...grpc.CallOption) (*BatchGetDaysContentNumResp, error) {
	out := new(BatchGetDaysContentNumResp)
	err := c.cc.Invoke(ctx, "/channel_play_index.ChannelPlayIndex/BatchGetDaysContentNum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayIndexClient) GetTodayDistrictPlayInfo(ctx context.Context, in *GetTodayDistrictPlayInfoReq, opts ...grpc.CallOption) (*GetTodayDistrictPlayInfoResp, error) {
	out := new(GetTodayDistrictPlayInfoResp)
	err := c.cc.Invoke(ctx, "/channel_play_index.ChannelPlayIndex/GetTodayDistrictPlayInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayIndexClient) CheckUserTodaySendMsg(ctx context.Context, in *CheckUserTodaySendMsgReq, opts ...grpc.CallOption) (*CheckUserTodaySendMsgResp, error) {
	out := new(CheckUserTodaySendMsgResp)
	err := c.cc.Invoke(ctx, "/channel_play_index.ChannelPlayIndex/CheckUserTodaySendMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayIndexClient) GetUserTodayChatUids(ctx context.Context, in *GetUserTodayChatUidsReq, opts ...grpc.CallOption) (*GetUserTodayChatUidsResp, error) {
	out := new(GetUserTodayChatUidsResp)
	err := c.cc.Invoke(ctx, "/channel_play_index.ChannelPlayIndex/GetUserTodayChatUids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayIndexClient) IsUserTodayAddNewFriend(ctx context.Context, in *IsUserTodayAddNewFriendReq, opts ...grpc.CallOption) (*IsUserTodayAddNewFriendResp, error) {
	out := new(IsUserTodayAddNewFriendResp)
	err := c.cc.Invoke(ctx, "/channel_play_index.ChannelPlayIndex/IsUserTodayAddNewFriend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayIndexClient) GetTabsRoomCount(ctx context.Context, in *GetTabsRoomCountReq, opts ...grpc.CallOption) (*GetTabsRoomCountResp, error) {
	out := new(GetTabsRoomCountResp)
	err := c.cc.Invoke(ctx, "/channel_play_index.ChannelPlayIndex/GetTabsRoomCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayIndexClient) SetPublishTabCntByOneDay(ctx context.Context, in *SetPublishTabCntByOneDayReq, opts ...grpc.CallOption) (*SetPublishTabCntByOneDayResp, error) {
	out := new(SetPublishTabCntByOneDayResp)
	err := c.cc.Invoke(ctx, "/channel_play_index.ChannelPlayIndex/SetPublishTabCntByOneDay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayIndexClient) GetPublishTabCntList(ctx context.Context, in *GetPublishTabCntListReq, opts ...grpc.CallOption) (*GetPublishTabCntListResp, error) {
	out := new(GetPublishTabCntListResp)
	err := c.cc.Invoke(ctx, "/channel_play_index.ChannelPlayIndex/GetPublishTabCntList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayIndexClient) GetUserDayRoomDuration(ctx context.Context, in *GetUserDayRoomDurationReq, opts ...grpc.CallOption) (*GetUserDayRoomDurationResp, error) {
	out := new(GetUserDayRoomDurationResp)
	err := c.cc.Invoke(ctx, "/channel_play_index.ChannelPlayIndex/GetUserDayRoomDuration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayIndexClient) GetUserDayOnlineDuration(ctx context.Context, in *GetUserDayOnlineDurationReq, opts ...grpc.CallOption) (*GetUserDayOnlineDurationResp, error) {
	out := new(GetUserDayOnlineDurationResp)
	err := c.cc.Invoke(ctx, "/channel_play_index.ChannelPlayIndex/GetUserDayOnlineDuration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayIndexClient) IsTodayHasFollowOrBeFollow(ctx context.Context, in *IsTodayHasFollowOrBeFollowReq, opts ...grpc.CallOption) (*IsTodayHasFollowOrBeFollowResp, error) {
	out := new(IsTodayHasFollowOrBeFollowResp)
	err := c.cc.Invoke(ctx, "/channel_play_index.ChannelPlayIndex/IsTodayHasFollowOrBeFollow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayIndexClient) GetRoomUids(ctx context.Context, in *GetRoomUidsRequest, opts ...grpc.CallOption) (*GetRoomUidsResponse, error) {
	out := new(GetRoomUidsResponse)
	err := c.cc.Invoke(ctx, "/channel_play_index.ChannelPlayIndex/GetRoomUids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelPlayIndexServer is the server API for ChannelPlayIndex service.
type ChannelPlayIndexServer interface {
	GetTodayUserPlayInfo(context.Context, *GetTodayUserPlayInfoReq) (*GetTodayUserPlayInfoResp, error)
	CheckUserInRoom(context.Context, *CheckUserInRoomReq) (*CheckUserInRoomResp, error)
	IsFinishTask(context.Context, *IsFinishTaskReq) (*IsFinishTaskResp, error)
	BatchGetDaysContentNum(context.Context, *BatchGetDaysContentNumReq) (*BatchGetDaysContentNumResp, error)
	GetTodayDistrictPlayInfo(context.Context, *GetTodayDistrictPlayInfoReq) (*GetTodayDistrictPlayInfoResp, error)
	CheckUserTodaySendMsg(context.Context, *CheckUserTodaySendMsgReq) (*CheckUserTodaySendMsgResp, error)
	GetUserTodayChatUids(context.Context, *GetUserTodayChatUidsReq) (*GetUserTodayChatUidsResp, error)
	IsUserTodayAddNewFriend(context.Context, *IsUserTodayAddNewFriendReq) (*IsUserTodayAddNewFriendResp, error)
	GetTabsRoomCount(context.Context, *GetTabsRoomCountReq) (*GetTabsRoomCountResp, error)
	SetPublishTabCntByOneDay(context.Context, *SetPublishTabCntByOneDayReq) (*SetPublishTabCntByOneDayResp, error)
	GetPublishTabCntList(context.Context, *GetPublishTabCntListReq) (*GetPublishTabCntListResp, error)
	GetUserDayRoomDuration(context.Context, *GetUserDayRoomDurationReq) (*GetUserDayRoomDurationResp, error)
	GetUserDayOnlineDuration(context.Context, *GetUserDayOnlineDurationReq) (*GetUserDayOnlineDurationResp, error)
	IsTodayHasFollowOrBeFollow(context.Context, *IsTodayHasFollowOrBeFollowReq) (*IsTodayHasFollowOrBeFollowResp, error)
	GetRoomUids(context.Context, *GetRoomUidsRequest) (*GetRoomUidsResponse, error)
}

func RegisterChannelPlayIndexServer(s *grpc.Server, srv ChannelPlayIndexServer) {
	s.RegisterService(&_ChannelPlayIndex_serviceDesc, srv)
}

func _ChannelPlayIndex_GetTodayUserPlayInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTodayUserPlayInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayIndexServer).GetTodayUserPlayInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_index.ChannelPlayIndex/GetTodayUserPlayInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayIndexServer).GetTodayUserPlayInfo(ctx, req.(*GetTodayUserPlayInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayIndex_CheckUserInRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserInRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayIndexServer).CheckUserInRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_index.ChannelPlayIndex/CheckUserInRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayIndexServer).CheckUserInRoom(ctx, req.(*CheckUserInRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayIndex_IsFinishTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsFinishTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayIndexServer).IsFinishTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_index.ChannelPlayIndex/IsFinishTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayIndexServer).IsFinishTask(ctx, req.(*IsFinishTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayIndex_BatchGetDaysContentNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetDaysContentNumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayIndexServer).BatchGetDaysContentNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_index.ChannelPlayIndex/BatchGetDaysContentNum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayIndexServer).BatchGetDaysContentNum(ctx, req.(*BatchGetDaysContentNumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayIndex_GetTodayDistrictPlayInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTodayDistrictPlayInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayIndexServer).GetTodayDistrictPlayInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_index.ChannelPlayIndex/GetTodayDistrictPlayInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayIndexServer).GetTodayDistrictPlayInfo(ctx, req.(*GetTodayDistrictPlayInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayIndex_CheckUserTodaySendMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserTodaySendMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayIndexServer).CheckUserTodaySendMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_index.ChannelPlayIndex/CheckUserTodaySendMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayIndexServer).CheckUserTodaySendMsg(ctx, req.(*CheckUserTodaySendMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayIndex_GetUserTodayChatUids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserTodayChatUidsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayIndexServer).GetUserTodayChatUids(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_index.ChannelPlayIndex/GetUserTodayChatUids",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayIndexServer).GetUserTodayChatUids(ctx, req.(*GetUserTodayChatUidsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayIndex_IsUserTodayAddNewFriend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsUserTodayAddNewFriendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayIndexServer).IsUserTodayAddNewFriend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_index.ChannelPlayIndex/IsUserTodayAddNewFriend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayIndexServer).IsUserTodayAddNewFriend(ctx, req.(*IsUserTodayAddNewFriendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayIndex_GetTabsRoomCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTabsRoomCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayIndexServer).GetTabsRoomCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_index.ChannelPlayIndex/GetTabsRoomCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayIndexServer).GetTabsRoomCount(ctx, req.(*GetTabsRoomCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayIndex_SetPublishTabCntByOneDay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPublishTabCntByOneDayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayIndexServer).SetPublishTabCntByOneDay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_index.ChannelPlayIndex/SetPublishTabCntByOneDay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayIndexServer).SetPublishTabCntByOneDay(ctx, req.(*SetPublishTabCntByOneDayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayIndex_GetPublishTabCntList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPublishTabCntListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayIndexServer).GetPublishTabCntList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_index.ChannelPlayIndex/GetPublishTabCntList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayIndexServer).GetPublishTabCntList(ctx, req.(*GetPublishTabCntListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayIndex_GetUserDayRoomDuration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDayRoomDurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayIndexServer).GetUserDayRoomDuration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_index.ChannelPlayIndex/GetUserDayRoomDuration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayIndexServer).GetUserDayRoomDuration(ctx, req.(*GetUserDayRoomDurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayIndex_GetUserDayOnlineDuration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDayOnlineDurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayIndexServer).GetUserDayOnlineDuration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_index.ChannelPlayIndex/GetUserDayOnlineDuration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayIndexServer).GetUserDayOnlineDuration(ctx, req.(*GetUserDayOnlineDurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayIndex_IsTodayHasFollowOrBeFollow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsTodayHasFollowOrBeFollowReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayIndexServer).IsTodayHasFollowOrBeFollow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_index.ChannelPlayIndex/IsTodayHasFollowOrBeFollow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayIndexServer).IsTodayHasFollowOrBeFollow(ctx, req.(*IsTodayHasFollowOrBeFollowReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayIndex_GetRoomUids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRoomUidsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayIndexServer).GetRoomUids(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_index.ChannelPlayIndex/GetRoomUids",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayIndexServer).GetRoomUids(ctx, req.(*GetRoomUidsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelPlayIndex_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_play_index.ChannelPlayIndex",
	HandlerType: (*ChannelPlayIndexServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetTodayUserPlayInfo",
			Handler:    _ChannelPlayIndex_GetTodayUserPlayInfo_Handler,
		},
		{
			MethodName: "CheckUserInRoom",
			Handler:    _ChannelPlayIndex_CheckUserInRoom_Handler,
		},
		{
			MethodName: "IsFinishTask",
			Handler:    _ChannelPlayIndex_IsFinishTask_Handler,
		},
		{
			MethodName: "BatchGetDaysContentNum",
			Handler:    _ChannelPlayIndex_BatchGetDaysContentNum_Handler,
		},
		{
			MethodName: "GetTodayDistrictPlayInfo",
			Handler:    _ChannelPlayIndex_GetTodayDistrictPlayInfo_Handler,
		},
		{
			MethodName: "CheckUserTodaySendMsg",
			Handler:    _ChannelPlayIndex_CheckUserTodaySendMsg_Handler,
		},
		{
			MethodName: "GetUserTodayChatUids",
			Handler:    _ChannelPlayIndex_GetUserTodayChatUids_Handler,
		},
		{
			MethodName: "IsUserTodayAddNewFriend",
			Handler:    _ChannelPlayIndex_IsUserTodayAddNewFriend_Handler,
		},
		{
			MethodName: "GetTabsRoomCount",
			Handler:    _ChannelPlayIndex_GetTabsRoomCount_Handler,
		},
		{
			MethodName: "SetPublishTabCntByOneDay",
			Handler:    _ChannelPlayIndex_SetPublishTabCntByOneDay_Handler,
		},
		{
			MethodName: "GetPublishTabCntList",
			Handler:    _ChannelPlayIndex_GetPublishTabCntList_Handler,
		},
		{
			MethodName: "GetUserDayRoomDuration",
			Handler:    _ChannelPlayIndex_GetUserDayRoomDuration_Handler,
		},
		{
			MethodName: "GetUserDayOnlineDuration",
			Handler:    _ChannelPlayIndex_GetUserDayOnlineDuration_Handler,
		},
		{
			MethodName: "IsTodayHasFollowOrBeFollow",
			Handler:    _ChannelPlayIndex_IsTodayHasFollowOrBeFollow_Handler,
		},
		{
			MethodName: "GetRoomUids",
			Handler:    _ChannelPlayIndex_GetRoomUids_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channel-play-index/channel-play-index.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-play-index/channel-play-index.proto", fileDescriptor_channel_play_index_4bedfa7ae545262a)
}

var fileDescriptor_channel_play_index_4bedfa7ae545262a = []byte{
	// 1605 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x58, 0x4f, 0x53, 0xdb, 0x48,
	0x16, 0x47, 0x38, 0x21, 0xf0, 0xb0, 0xc1, 0xe9, 0x40, 0x30, 0x82, 0x50, 0xac, 0xb2, 0xbb, 0x10,
	0x12, 0xcc, 0x86, 0x64, 0xb3, 0xbb, 0xc9, 0x1e, 0xd6, 0xd8, 0x06, 0x54, 0x09, 0x86, 0x12, 0xf6,
	0x56, 0x25, 0x53, 0x35, 0x1a, 0x21, 0x35, 0xa0, 0xc2, 0x96, 0x84, 0xba, 0x45, 0x70, 0xd5, 0x54,
	0xcd, 0x69, 0x6a, 0x4e, 0xf3, 0x19, 0xe6, 0x3a, 0xf7, 0xf9, 0x14, 0xf3, 0x45, 0xe6, 0x38, 0xc7,
	0x39, 0x4f, 0x75, 0xb7, 0x6c, 0xcb, 0x76, 0xcb, 0x40, 0x6e, 0xea, 0xd7, 0xef, 0x5f, 0xbf, 0xbf,
	0x3f, 0x1b, 0xde, 0x52, 0xba, 0x75, 0x19, 0xb9, 0xf6, 0x05, 0x71, 0x9b, 0x57, 0x38, 0xdc, 0xb2,
	0xcf, 0x2d, 0xcf, 0xc3, 0xcd, 0xcd, 0xa0, 0x69, 0xb5, 0x37, 0x5d, 0xcf, 0xc1, 0xd7, 0x12, 0x52,
	0x31, 0x08, 0x7d, 0xea, 0x23, 0x14, 0xdf, 0x98, 0xec, 0xc6, 0xe4, 0x37, 0xda, 0x0f, 0x0a, 0x2c,
	0xec, 0x61, 0x5a, 0xf7, 0x1d, 0xab, 0xdd, 0x20, 0x38, 0x3c, 0x6a, 0x5a, 0x6d, 0xdd, 0x3b, 0xf5,
	0x0d, 0x7c, 0x89, 0xf2, 0x90, 0x89, 0x5c, 0xa7, 0xa0, 0xac, 0x2a, 0xeb, 0x39, 0x83, 0x7d, 0xa2,
	0x7d, 0x98, 0x8d, 0x08, 0x0e, 0x85, 0x02, 0xda, 0x0e, 0x30, 0x29, 0x8c, 0xaf, 0x66, 0xd6, 0x67,
	0xb6, 0x57, 0x8b, 0xc3, 0xba, 0x8b, 0x1d, 0x7d, 0xf5, 0x76, 0x80, 0x8d, 0x5c, 0x94, 0x38, 0x11,
	0xa6, 0xdb, 0x76, 0x9d, 0x42, 0x46, 0xe8, 0xb6, 0x5d, 0x47, 0xfb, 0x51, 0x81, 0x82, 0xdc, 0x13,
	0x12, 0xa0, 0x35, 0xc8, 0x73, 0xc3, 0xae, 0x67, 0x86, 0xbe, 0xdf, 0x32, 0x6d, 0x8f, 0xc6, 0x7e,
	0x71, 0xbd, 0xba, 0x67, 0xf8, 0x7e, 0xab, 0xec, 0x51, 0xf4, 0x12, 0xe6, 0xfb, 0x18, 0x9d, 0x28,
	0xb4, 0xa8, 0xeb, 0x7b, 0x85, 0x71, 0xce, 0x8d, 0x7a, 0xdc, 0x95, 0xf8, 0x06, 0xcd, 0xc3, 0x04,
	0xb5, 0x4e, 0xcc, 0xae, 0x37, 0xf7, 0xa9, 0x75, 0xa2, 0x3b, 0x5a, 0x04, 0x33, 0x8d, 0x2e, 0x33,
	0x73, 0x04, 0x3d, 0x01, 0xe8, 0xbc, 0xb2, 0x1b, 0x96, 0xa9, 0x98, 0xa2, 0x3b, 0x48, 0x83, 0x1c,
	0xf6, 0x28, 0x0e, 0x85, 0x61, 0x4a, 0xb8, 0xc9, 0x8c, 0x31, 0xcd, 0x89, 0x4c, 0x49, 0x9d, 0xa0,
	0x55, 0xc8, 0xe2, 0x6b, 0x97, 0x76, 0x59, 0x32, 0x9c, 0x05, 0x18, 0x4d, 0x70, 0x68, 0x55, 0x40,
	0xe5, 0x73, 0x6c, 0x5f, 0xf4, 0x6c, 0xcb, 0x53, 0xc1, 0x9d, 0xc1, 0xf6, 0x85, 0x19, 0xb9, 0x8e,
	0xc8, 0x02, 0x77, 0x86, 0x49, 0xba, 0x0e, 0xd1, 0x7e, 0x57, 0x20, 0x5b, 0x8d, 0xb5, 0xde, 0xc6,
	0xf9, 0x3e, 0xc7, 0x5a, 0x1d, 0xdf, 0xbb, 0x8e, 0x1d, 0x10, 0xb4, 0x08, 0x93, 0x9c, 0x23, 0xea,
	0x06, 0xea, 0x01, 0x3b, 0x37, 0x5c, 0x27, 0x11, 0xc1, 0x7b, 0x89, 0x08, 0xb2, 0x80, 0x10, 0xab,
	0x85, 0x85, 0x4e, 0x26, 0x76, 0x9f, 0xdf, 0x4e, 0x33, 0x22, 0x53, 0xca, 0x44, 0x9f, 0x42, 0x8e,
	0xfa, 0x67, 0x98, 0x9e, 0xe3, 0xd0, 0xa4, 0x6e, 0x0b, 0x17, 0x26, 0xb8, 0xe1, 0x6c, 0x87, 0x58,
	0x77, 0x5b, 0x78, 0x20, 0xb2, 0x2d, 0x52, 0x78, 0x30, 0x10, 0xd9, 0x03, 0xa2, 0xfd, 0xa6, 0xc0,
	0xa3, 0xa1, 0xc0, 0x91, 0x00, 0xb9, 0x80, 0x7a, 0x0f, 0x73, 0xbd, 0x53, 0xdf, 0x6c, 0x59, 0x41,
	0x41, 0x59, 0xcd, 0xac, 0x4f, 0x6f, 0xff, 0x57, 0x56, 0xb5, 0x12, 0x25, 0xc5, 0x64, 0x24, 0x0f,
	0xac, 0xa0, 0xea, 0xd1, 0xb0, 0x6d, 0xcc, 0xe2, 0x7e, 0xaa, 0xea, 0xc0, 0x9c, 0x8c, 0x91, 0x25,
	0xef, 0x02, 0xb7, 0x79, 0xcc, 0xa7, 0x0c, 0xf6, 0x89, 0xde, 0xc0, 0xfd, 0x2b, 0xab, 0x19, 0x61,
	0x1e, 0xe6, 0x69, 0x79, 0xf7, 0x24, 0x55, 0x19, 0x82, 0xfd, 0xed, 0xf8, 0xbf, 0x15, 0xed, 0x6b,
	0x98, 0xd5, 0xc9, 0xae, 0xeb, 0xb9, 0xe4, 0xbc, 0x6e, 0x91, 0x0b, 0x79, 0x75, 0xfc, 0x07, 0xa6,
	0xa8, 0x45, 0x2e, 0x78, 0x8f, 0x72, 0x23, 0x33, 0xdb, 0xcb, 0x32, 0x23, 0x4c, 0x43, 0xd5, 0x8b,
	0x5a, 0xc6, 0x24, 0x63, 0x67, 0xad, 0xa9, 0x6d, 0x40, 0xbe, 0x5f, 0x3f, 0x09, 0xd0, 0x63, 0x98,
	0x38, 0xe5, 0x14, 0x6e, 0x63, 0xd2, 0x88, 0x4f, 0xda, 0x6b, 0x58, 0xdc, 0xb1, 0xa8, 0x7d, 0xbe,
	0x87, 0x69, 0xc5, 0x6a, 0x93, 0xb2, 0xef, 0x51, 0xec, 0xd1, 0x5a, 0xc4, 0x6b, 0x76, 0x01, 0x1e,
	0x88, 0xaa, 0x20, 0x3c, 0xdc, 0x39, 0x63, 0x82, 0x97, 0x05, 0xd1, 0x7e, 0x55, 0x40, 0x4d, 0x13,
	0xe3, 0x19, 0x9b, 0xb5, 0x05, 0xc5, 0xf4, 0xa2, 0x56, 0x22, 0x5d, 0x25, 0xd9, 0x0b, 0xd2, 0x15,
	0x15, 0x7b, 0xc7, 0x6e, 0xce, 0x72, 0x76, 0x92, 0xa6, 0xfe, 0x0f, 0xd0, 0x30, 0x53, 0x32, 0x5f,
	0x39, 0x91, 0xaf, 0xb9, 0x64, 0xbe, 0x72, 0xc9, 0x6c, 0xec, 0xc2, 0x52, 0x67, 0x68, 0x55, 0x5c,
	0x42, 0x43, 0xd7, 0xa6, 0xa3, 0x47, 0x68, 0xaf, 0x57, 0xc6, 0x93, 0xd3, 0xe6, 0x04, 0x96, 0xd3,
	0xf5, 0x90, 0x00, 0xed, 0xc0, 0x4a, 0x67, 0xae, 0x9d, 0xb1, 0x9e, 0x72, 0x62, 0xa6, 0xde, 0x80,
	0x13, 0x36, 0x54, 0x31, 0xe0, 0xf6, 0xac, 0x16, 0xee, 0xe8, 0xe9, 0x0c, 0x3a, 0xed, 0x3d, 0x14,
	0xba, 0xc5, 0xcd, 0x2d, 0x1d, 0x63, 0xcf, 0x39, 0x20, 0x67, 0x5f, 0x34, 0x60, 0xde, 0xc2, 0x62,
	0x8a, 0x32, 0x12, 0x30, 0x59, 0x7c, 0xed, 0x12, 0x2a, 0x64, 0x45, 0xf6, 0xa7, 0x38, 0x85, 0xcb,
	0x3e, 0xe7, 0x3b, 0xa7, 0x2b, 0x59, 0x3e, 0xb7, 0x38, 0x5d, 0xea, 0x87, 0xf6, 0x2f, 0xbe, 0x16,
	0x24, 0xcc, 0x24, 0x40, 0x4b, 0xc0, 0x46, 0x58, 0x9f, 0x99, 0x49, 0x3b, 0x66, 0xd0, 0x8a, 0xa0,
	0xea, 0xa4, 0x2b, 0x57, 0x72, 0x9c, 0x1a, 0xfe, 0xbc, 0x1b, 0xba, 0xd8, 0x73, 0xe4, 0x86, 0xf6,
	0x61, 0x29, 0x95, 0x9f, 0x04, 0xe8, 0x19, 0x3c, 0x74, 0x89, 0x69, 0x39, 0x8e, 0xe9, 0xe1, 0xcf,
	0xe6, 0x29, 0xbf, 0x88, 0xdb, 0x61, 0xc6, 0x25, 0x49, 0x76, 0xad, 0x08, 0x8f, 0x58, 0x32, 0xad,
	0x13, 0xc2, 0xd7, 0x92, 0x1f, 0x79, 0x74, 0x64, 0x43, 0xfc, 0xac, 0xc0, 0xdc, 0xb0, 0x00, 0x09,
	0xd0, 0x31, 0x4c, 0xd9, 0xec, 0x90, 0x68, 0x82, 0x37, 0xb2, 0x26, 0x90, 0x09, 0x17, 0xf9, 0x57,
	0xb7, 0xf2, 0x27, 0xed, 0xf8, 0xa8, 0xbe, 0x83, 0x5c, 0xdf, 0xd5, 0x4d, 0xf5, 0x9e, 0x49, 0xd6,
	0xfb, 0x2f, 0x0a, 0x2c, 0x1d, 0x63, 0x7a, 0x14, 0x9d, 0x34, 0xf9, 0x80, 0x38, 0x29, 0x7b, 0x74,
	0xa7, 0x7d, 0xe8, 0xe1, 0x8a, 0xd5, 0x66, 0x6f, 0xec, 0x95, 0xb7, 0x92, 0x5c, 0x05, 0x71, 0xb4,
	0xc7, 0x7b, 0xe5, 0xf5, 0x17, 0xc8, 0x06, 0x42, 0x89, 0x98, 0xfb, 0x62, 0x13, 0x4e, 0xc7, 0x34,
	0x3e, 0xf6, 0xf7, 0x13, 0x2c, 0x6c, 0x8e, 0xdd, 0xe3, 0x73, 0xec, 0x6f, 0xb2, 0x00, 0xc4, 0xfe,
	0x18, 0xd8, 0xf6, 0x43, 0x87, 0xe3, 0x8d, 0xae, 0x26, 0x36, 0xd3, 0x56, 0x60, 0x39, 0xdd, 0x69,
	0x12, 0x68, 0x3f, 0x09, 0x14, 0xd4, 0xc7, 0xf0, 0xc1, 0x25, 0x3c, 0x6b, 0x4f, 0x00, 0x2e, 0x23,
	0x1c, 0xb6, 0x85, 0x9b, 0x0a, 0x77, 0x73, 0x8a, 0x53, 0xb8, 0x93, 0x4b, 0x30, 0xd5, 0x74, 0x5b,
	0x2e, 0xe5, 0x90, 0x44, 0xbc, 0x6f, 0x92, 0x13, 0x18, 0x1a, 0x19, 0x7c, 0x41, 0xe6, 0x8b, 0x5f,
	0xf0, 0x8a, 0x77, 0x81, 0xc4, 0x41, 0x12, 0xa4, 0xd7, 0xd5, 0x26, 0x2c, 0xc6, 0xad, 0xc3, 0x1e,
	0x9a, 0xc0, 0x3c, 0xf2, 0x06, 0x28, 0x81, 0x9a, 0xc6, 0x4e, 0x02, 0xb6, 0xa9, 0xfb, 0x11, 0x95,
	0x08, 0x45, 0x36, 0x4c, 0x30, 0x6a, 0x5b, 0x7c, 0x1c, 0xc6, 0x2a, 0x0e, 0xbd, 0xa6, 0xeb, 0xe1,
	0xd1, 0x36, 0xf7, 0xf8, 0xdc, 0x4b, 0x11, 0xe0, 0xc0, 0x6f, 0xd6, 0xe7, 0xd4, 0x41, 0xbb, 0x33,
	0x7e, 0x1f, 0xb3, 0xf6, 0x12, 0x9e, 0xe8, 0x84, 0x77, 0xee, 0xbe, 0x45, 0x76, 0xfd, 0x66, 0xd3,
	0xff, 0x7c, 0x18, 0xee, 0x60, 0xf1, 0x25, 0xb7, 0x5d, 0x85, 0x95, 0x51, 0x22, 0xe2, 0xcd, 0xa7,
	0xfc, 0x64, 0x12, 0x6a, 0xd1, 0x88, 0xc4, 0xfd, 0x9e, 0x15, 0xc4, 0x63, 0x4e, 0xd3, 0x5e, 0x01,
	0xda, 0xc3, 0x34, 0x06, 0x34, 0x6c, 0x88, 0x45, 0x98, 0xd0, 0x1b, 0xf0, 0x96, 0xf6, 0x8c, 0x8f,
	0x88, 0x9e, 0x10, 0x09, 0x7c, 0x8f, 0x60, 0x84, 0xe0, 0x5e, 0x62, 0x96, 0xf1, 0xef, 0x8d, 0x16,
	0x64, 0x93, 0x48, 0x1a, 0xad, 0x80, 0xda, 0x38, 0xae, 0x1a, 0xe6, 0xd1, 0x87, 0xd2, 0x47, 0xb3,
	0xfe, 0xf1, 0xa8, 0x6a, 0x36, 0x6a, 0xc7, 0x47, 0xd5, 0xb2, 0xbe, 0xab, 0x57, 0x2b, 0xf9, 0x31,
	0xb4, 0x04, 0x0b, 0x03, 0xf7, 0xc6, 0xe1, 0xe1, 0x81, 0x59, 0xae, 0xd5, 0xf3, 0x0a, 0x5a, 0x85,
	0x65, 0xd9, 0x65, 0xa5, 0x61, 0x94, 0xea, 0xfa, 0x61, 0x2d, 0x3f, 0xbe, 0xf1, 0x0e, 0x26, 0x3b,
	0xa8, 0x00, 0xcd, 0xc3, 0xc3, 0x7a, 0xe9, 0xf8, 0xbd, 0x59, 0xad, 0x35, 0x0e, 0x4c, 0xbd, 0xf6,
	0xff, 0xd2, 0x07, 0x9d, 0x59, 0x28, 0xc0, 0x5c, 0x8f, 0xfc, 0xe9, 0xb0, 0x56, 0x35, 0xf7, 0x1a,
	0x7a, 0xa5, 0x9a, 0x57, 0x36, 0x2a, 0xf0, 0x70, 0xa8, 0x90, 0xd1, 0x22, 0xcc, 0x97, 0x4b, 0xf5,
	0xaa, 0xcc, 0xd7, 0x39, 0xc8, 0xf7, 0xae, 0xde, 0x97, 0xf4, 0xfd, 0xaa, 0x9e, 0x57, 0xb6, 0xff,
	0xc8, 0x41, 0xbe, 0x2c, 0x42, 0x25, 0x96, 0xa0, 0x83, 0xaf, 0x11, 0x11, 0x33, 0x72, 0xf0, 0xe7,
	0x01, 0x7a, 0x9e, 0x36, 0x10, 0x25, 0x3f, 0x69, 0xd4, 0x17, 0xb7, 0x67, 0x26, 0x81, 0x36, 0x86,
	0x1c, 0x98, 0x1d, 0xc0, 0x83, 0xe8, 0xef, 0xb7, 0x02, 0x8d, 0x97, 0xea, 0xda, 0x2d, 0xc1, 0xa5,
	0x36, 0x86, 0xbe, 0x82, 0x6c, 0x12, 0x72, 0xa1, 0xa7, 0x32, 0xd1, 0x01, 0xd0, 0xa7, 0xfe, 0xf5,
	0x66, 0x26, 0xae, 0xbc, 0x0d, 0x8f, 0xe5, 0x18, 0x09, 0x6d, 0xde, 0x05, 0x4f, 0x5d, 0xaa, 0xc5,
	0xbb, 0xc1, 0x2f, 0x6d, 0x0c, 0x7d, 0xd7, 0xfb, 0x45, 0x37, 0x08, 0x6a, 0xd0, 0xd6, 0xa8, 0x4c,
	0x48, 0xa0, 0x94, 0xfa, 0x8f, 0xbb, 0x09, 0x70, 0x07, 0xae, 0x60, 0x5e, 0x0a, 0x52, 0xd0, 0x8b,
	0x91, 0xc9, 0x19, 0x00, 0x47, 0xea, 0xe6, 0x1d, 0xb8, 0xb9, 0x5d, 0x51, 0xab, 0x43, 0x98, 0x25,
	0xb5, 0x56, 0x65, 0x50, 0x28, 0xb5, 0x56, 0xa5, 0x50, 0x48, 0x1b, 0x43, 0xdf, 0xc2, 0x42, 0x0a,
	0x7e, 0x41, 0x45, 0x79, 0xad, 0xa4, 0x81, 0x23, 0x75, 0xeb, 0x4e, 0xfc, 0xdc, 0xfa, 0x19, 0xe4,
	0x07, 0x51, 0x08, 0x5a, 0xbb, 0x1d, 0x56, 0xb9, 0x54, 0xd7, 0x6f, 0x0b, 0x6a, 0x44, 0x51, 0xa5,
	0xed, 0x72, 0x79, 0x51, 0x8d, 0x80, 0x2b, 0xf2, 0xa2, 0x1a, 0x09, 0x15, 0x3a, 0xc9, 0x1d, 0x5a,
	0xc5, 0xa9, 0xc9, 0x95, 0xa1, 0x8a, 0xd4, 0xe4, 0x4a, 0x37, 0xbc, 0xe8, 0x62, 0xf9, 0x6e, 0x96,
	0x77, 0x71, 0xea, 0xda, 0x97, 0x77, 0x71, 0xfa, 0xda, 0xef, 0x76, 0xb1, 0x74, 0x45, 0xa7, 0x76,
	0x71, 0x1a, 0x02, 0x48, 0xed, 0xe2, 0x54, 0x04, 0xa0, 0x8d, 0xa1, 0xef, 0x15, 0x86, 0xe4, 0xd3,
	0x16, 0x35, 0x7a, 0x29, 0x2f, 0xd6, 0x11, 0x58, 0x40, 0xdd, 0xbe, 0xab, 0x08, 0xf7, 0xe3, 0x1b,
	0x98, 0x4e, 0xec, 0x6c, 0xf9, 0x22, 0x18, 0x46, 0x02, 0xea, 0xda, 0x8d, 0x7c, 0x62, 0xf9, 0x6b,
	0x63, 0x3b, 0x6f, 0x3e, 0xbd, 0x3e, 0xf3, 0x9b, 0x96, 0x77, 0x56, 0xfc, 0xe7, 0x36, 0xa5, 0x45,
	0xdb, 0x6f, 0x6d, 0xf1, 0xbf, 0xee, 0x6c, 0xbf, 0xb9, 0x45, 0x70, 0x78, 0xe5, 0xda, 0x98, 0x48,
	0xfe, 0xdf, 0x3b, 0x99, 0xe0, 0x5c, 0xaf, 0xfe, 0x0c, 0x00, 0x00, 0xff, 0xff, 0xb0, 0x05, 0x1a,
	0x09, 0x1e, 0x14, 0x00, 0x00,
}
