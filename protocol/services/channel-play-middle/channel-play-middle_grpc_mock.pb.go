// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/channel-play-middle/channel-play-middle.proto

package channel_play_middle

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockChannelPlayMiddleClient is a mock of ChannelPlayMiddleClient interface.
type MockChannelPlayMiddleClient struct {
	ctrl     *gomock.Controller
	recorder *MockChannelPlayMiddleClientMockRecorder
}

// MockChannelPlayMiddleClientMockRecorder is the mock recorder for MockChannelPlayMiddleClient.
type MockChannelPlayMiddleClientMockRecorder struct {
	mock *MockChannelPlayMiddleClient
}

// NewMockChannelPlayMiddleClient creates a new mock instance.
func NewMockChannelPlayMiddleClient(ctrl *gomock.Controller) *MockChannelPlayMiddleClient {
	mock := &MockChannelPlayMiddleClient{ctrl: ctrl}
	mock.recorder = &MockChannelPlayMiddleClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelPlayMiddleClient) EXPECT() *MockChannelPlayMiddleClientMockRecorder {
	return m.recorder
}

// BatGetGameChannelViewMap mocks base method.
func (m *MockChannelPlayMiddleClient) BatGetGameChannelViewMap(ctx context.Context, in *BatGetGameChannelViewMapReq, opts ...grpc.CallOption) (*BatGetGameChannelViewMapResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetGameChannelViewMap", varargs...)
	ret0, _ := ret[0].(*BatGetGameChannelViewMapResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetGameChannelViewMap indicates an expected call of BatGetGameChannelViewMap.
func (mr *MockChannelPlayMiddleClientMockRecorder) BatGetGameChannelViewMap(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetGameChannelViewMap", reflect.TypeOf((*MockChannelPlayMiddleClient)(nil).BatGetGameChannelViewMap), varargs...)
}

// FilterTab mocks base method.
func (m *MockChannelPlayMiddleClient) FilterTab(ctx context.Context, in *FilterTabReq, opts ...grpc.CallOption) (*FilterTabResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FilterTab", varargs...)
	ret0, _ := ret[0].(*FilterTabResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FilterTab indicates an expected call of FilterTab.
func (mr *MockChannelPlayMiddleClientMockRecorder) FilterTab(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FilterTab", reflect.TypeOf((*MockChannelPlayMiddleClient)(nil).FilterTab), varargs...)
}

// GetFastPcSupportTabList mocks base method.
func (m *MockChannelPlayMiddleClient) GetFastPcSupportTabList(ctx context.Context, in *GetFastPcSupportTabListReq, opts ...grpc.CallOption) (*GetFastPcSupportTabListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFastPcSupportTabList", varargs...)
	ret0, _ := ret[0].(*GetFastPcSupportTabListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFastPcSupportTabList indicates an expected call of GetFastPcSupportTabList.
func (mr *MockChannelPlayMiddleClientMockRecorder) GetFastPcSupportTabList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFastPcSupportTabList", reflect.TypeOf((*MockChannelPlayMiddleClient)(nil).GetFastPcSupportTabList), varargs...)
}

// MockChannelPlayMiddleServer is a mock of ChannelPlayMiddleServer interface.
type MockChannelPlayMiddleServer struct {
	ctrl     *gomock.Controller
	recorder *MockChannelPlayMiddleServerMockRecorder
}

// MockChannelPlayMiddleServerMockRecorder is the mock recorder for MockChannelPlayMiddleServer.
type MockChannelPlayMiddleServerMockRecorder struct {
	mock *MockChannelPlayMiddleServer
}

// NewMockChannelPlayMiddleServer creates a new mock instance.
func NewMockChannelPlayMiddleServer(ctrl *gomock.Controller) *MockChannelPlayMiddleServer {
	mock := &MockChannelPlayMiddleServer{ctrl: ctrl}
	mock.recorder = &MockChannelPlayMiddleServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelPlayMiddleServer) EXPECT() *MockChannelPlayMiddleServerMockRecorder {
	return m.recorder
}

// BatGetGameChannelViewMap mocks base method.
func (m *MockChannelPlayMiddleServer) BatGetGameChannelViewMap(ctx context.Context, in *BatGetGameChannelViewMapReq) (*BatGetGameChannelViewMapResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetGameChannelViewMap", ctx, in)
	ret0, _ := ret[0].(*BatGetGameChannelViewMapResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetGameChannelViewMap indicates an expected call of BatGetGameChannelViewMap.
func (mr *MockChannelPlayMiddleServerMockRecorder) BatGetGameChannelViewMap(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetGameChannelViewMap", reflect.TypeOf((*MockChannelPlayMiddleServer)(nil).BatGetGameChannelViewMap), ctx, in)
}

// FilterTab mocks base method.
func (m *MockChannelPlayMiddleServer) FilterTab(ctx context.Context, in *FilterTabReq) (*FilterTabResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FilterTab", ctx, in)
	ret0, _ := ret[0].(*FilterTabResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FilterTab indicates an expected call of FilterTab.
func (mr *MockChannelPlayMiddleServerMockRecorder) FilterTab(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FilterTab", reflect.TypeOf((*MockChannelPlayMiddleServer)(nil).FilterTab), ctx, in)
}

// GetFastPcSupportTabList mocks base method.
func (m *MockChannelPlayMiddleServer) GetFastPcSupportTabList(ctx context.Context, in *GetFastPcSupportTabListReq) (*GetFastPcSupportTabListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFastPcSupportTabList", ctx, in)
	ret0, _ := ret[0].(*GetFastPcSupportTabListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFastPcSupportTabList indicates an expected call of GetFastPcSupportTabList.
func (mr *MockChannelPlayMiddleServerMockRecorder) GetFastPcSupportTabList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFastPcSupportTabList", reflect.TypeOf((*MockChannelPlayMiddleServer)(nil).GetFastPcSupportTabList), ctx, in)
}
