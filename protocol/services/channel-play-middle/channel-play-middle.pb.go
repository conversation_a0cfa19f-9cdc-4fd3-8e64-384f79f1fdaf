// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-play-middle/channel-play-middle.proto

package channel_play_middle // import "golang.52tt.com/protocol/services/channel-play-middle"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import common "golang.52tt.com/protocol/services/rcmd/common"
import anypb "google.golang.org/protobuf/types/known/anypb"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ReqSource int32

const (
	ReqSource_REQ_SOURCE_UNSPECIFIED    ReqSource = 0
	ReqSource_REQ_SOURCE_SEARCH_BY_NAME ReqSource = 1
	ReqSource_REQ_SOURCE_SEARCH_BY_ID   ReqSource = 2
	ReqSource_REQ_SOURCE_LIST           ReqSource = 3
)

var ReqSource_name = map[int32]string{
	0: "REQ_SOURCE_UNSPECIFIED",
	1: "REQ_SOURCE_SEARCH_BY_NAME",
	2: "REQ_SOURCE_SEARCH_BY_ID",
	3: "REQ_SOURCE_LIST",
}
var ReqSource_value = map[string]int32{
	"REQ_SOURCE_UNSPECIFIED":    0,
	"REQ_SOURCE_SEARCH_BY_NAME": 1,
	"REQ_SOURCE_SEARCH_BY_ID":   2,
	"REQ_SOURCE_LIST":           3,
}

func (x ReqSource) String() string {
	return proto.EnumName(ReqSource_name, int32(x))
}
func (ReqSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_middle_e3fd144a503c0313, []int{0}
}

type BatGetGameChannelViewMapReq struct {
	ReqSource            uint32            `protobuf:"varint,1,opt,name=req_source,json=reqSource,proto3" json:"req_source,omitempty"`
	ChannelIds           []uint32          `protobuf:"varint,2,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	RecommendInfo        *RcmdInfo         `protobuf:"bytes,3,opt,name=recommend_info,json=recommendInfo,proto3" json:"recommend_info,omitempty"`
	ListParams           *SourceListParams `protobuf:"bytes,4,opt,name=list_params,json=listParams,proto3" json:"list_params,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatGetGameChannelViewMapReq) Reset()         { *m = BatGetGameChannelViewMapReq{} }
func (m *BatGetGameChannelViewMapReq) String() string { return proto.CompactTextString(m) }
func (*BatGetGameChannelViewMapReq) ProtoMessage()    {}
func (*BatGetGameChannelViewMapReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_middle_e3fd144a503c0313, []int{0}
}
func (m *BatGetGameChannelViewMapReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetGameChannelViewMapReq.Unmarshal(m, b)
}
func (m *BatGetGameChannelViewMapReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetGameChannelViewMapReq.Marshal(b, m, deterministic)
}
func (dst *BatGetGameChannelViewMapReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetGameChannelViewMapReq.Merge(dst, src)
}
func (m *BatGetGameChannelViewMapReq) XXX_Size() int {
	return xxx_messageInfo_BatGetGameChannelViewMapReq.Size(m)
}
func (m *BatGetGameChannelViewMapReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetGameChannelViewMapReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetGameChannelViewMapReq proto.InternalMessageInfo

func (m *BatGetGameChannelViewMapReq) GetReqSource() uint32 {
	if m != nil {
		return m.ReqSource
	}
	return 0
}

func (m *BatGetGameChannelViewMapReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

func (m *BatGetGameChannelViewMapReq) GetRecommendInfo() *RcmdInfo {
	if m != nil {
		return m.RecommendInfo
	}
	return nil
}

func (m *BatGetGameChannelViewMapReq) GetListParams() *SourceListParams {
	if m != nil {
		return m.ListParams
	}
	return nil
}

type SourceListParams struct {
	// 区分首页入口,见channel-play_.proto ChannelListEnterSource
	ChannelListEnterSource uint32 `protobuf:"varint,1,opt,name=channel_list_enter_source,json=channelListEnterSource,proto3" json:"channel_list_enter_source,omitempty"`
	// 房间列表样式,见channel-play_.proto ChannelListStyleType
	ListStyleType uint32 `protobuf:"varint,2,opt,name=list_style_type,json=listStyleType,proto3" json:"list_style_type,omitempty"`
	// 提供view的来源标识，见channel-play_.proto GenViewSource
	GenViewSource        []uint32 `protobuf:"varint,3,rep,packed,name=gen_view_source,json=genViewSource,proto3" json:"gen_view_source,omitempty"`
	ChannelPackageId     string   `protobuf:"bytes,4,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	TabId                uint32   `protobuf:"varint,5,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabIdsStr            string   `protobuf:"bytes,6,opt,name=tab_ids_str,json=tabIdsStr,proto3" json:"tab_ids_str,omitempty"`
	CategoryIdsStr       string   `protobuf:"bytes,7,opt,name=category_ids_str,json=categoryIdsStr,proto3" json:"category_ids_str,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SourceListParams) Reset()         { *m = SourceListParams{} }
func (m *SourceListParams) String() string { return proto.CompactTextString(m) }
func (*SourceListParams) ProtoMessage()    {}
func (*SourceListParams) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_middle_e3fd144a503c0313, []int{1}
}
func (m *SourceListParams) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SourceListParams.Unmarshal(m, b)
}
func (m *SourceListParams) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SourceListParams.Marshal(b, m, deterministic)
}
func (dst *SourceListParams) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SourceListParams.Merge(dst, src)
}
func (m *SourceListParams) XXX_Size() int {
	return xxx_messageInfo_SourceListParams.Size(m)
}
func (m *SourceListParams) XXX_DiscardUnknown() {
	xxx_messageInfo_SourceListParams.DiscardUnknown(m)
}

var xxx_messageInfo_SourceListParams proto.InternalMessageInfo

func (m *SourceListParams) GetChannelListEnterSource() uint32 {
	if m != nil {
		return m.ChannelListEnterSource
	}
	return 0
}

func (m *SourceListParams) GetListStyleType() uint32 {
	if m != nil {
		return m.ListStyleType
	}
	return 0
}

func (m *SourceListParams) GetGenViewSource() []uint32 {
	if m != nil {
		return m.GenViewSource
	}
	return nil
}

func (m *SourceListParams) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *SourceListParams) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SourceListParams) GetTabIdsStr() string {
	if m != nil {
		return m.TabIdsStr
	}
	return ""
}

func (m *SourceListParams) GetCategoryIdsStr() string {
	if m != nil {
		return m.CategoryIdsStr
	}
	return ""
}

type RcmdInfo struct {
	SelfLocation         *common.LocationInfo           `protobuf:"bytes,1,opt,name=self_location,json=selfLocation,proto3" json:"self_location,omitempty"`
	ChannelInfoMap       map[uint32]*common.ChannelInfo `protobuf:"bytes,3,rep,name=channel_info_map,json=channelInfoMap,proto3" json:"channel_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *RcmdInfo) Reset()         { *m = RcmdInfo{} }
func (m *RcmdInfo) String() string { return proto.CompactTextString(m) }
func (*RcmdInfo) ProtoMessage()    {}
func (*RcmdInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_middle_e3fd144a503c0313, []int{2}
}
func (m *RcmdInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RcmdInfo.Unmarshal(m, b)
}
func (m *RcmdInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RcmdInfo.Marshal(b, m, deterministic)
}
func (dst *RcmdInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RcmdInfo.Merge(dst, src)
}
func (m *RcmdInfo) XXX_Size() int {
	return xxx_messageInfo_RcmdInfo.Size(m)
}
func (m *RcmdInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RcmdInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RcmdInfo proto.InternalMessageInfo

func (m *RcmdInfo) GetSelfLocation() *common.LocationInfo {
	if m != nil {
		return m.SelfLocation
	}
	return nil
}

func (m *RcmdInfo) GetChannelInfoMap() map[uint32]*common.ChannelInfo {
	if m != nil {
		return m.ChannelInfoMap
	}
	return nil
}

// Any 使用channel-play_.proto 的TopicChannelItem解析
type BatGetGameChannelViewMapResp struct {
	ChannelViewMap       map[uint32]*anypb.Any `protobuf:"bytes,1,rep,name=channel_view_map,json=channelViewMap,proto3" json:"channel_view_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BatGetGameChannelViewMapResp) Reset()         { *m = BatGetGameChannelViewMapResp{} }
func (m *BatGetGameChannelViewMapResp) String() string { return proto.CompactTextString(m) }
func (*BatGetGameChannelViewMapResp) ProtoMessage()    {}
func (*BatGetGameChannelViewMapResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_middle_e3fd144a503c0313, []int{3}
}
func (m *BatGetGameChannelViewMapResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetGameChannelViewMapResp.Unmarshal(m, b)
}
func (m *BatGetGameChannelViewMapResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetGameChannelViewMapResp.Marshal(b, m, deterministic)
}
func (dst *BatGetGameChannelViewMapResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetGameChannelViewMapResp.Merge(dst, src)
}
func (m *BatGetGameChannelViewMapResp) XXX_Size() int {
	return xxx_messageInfo_BatGetGameChannelViewMapResp.Size(m)
}
func (m *BatGetGameChannelViewMapResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetGameChannelViewMapResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetGameChannelViewMapResp proto.InternalMessageInfo

func (m *BatGetGameChannelViewMapResp) GetChannelViewMap() map[uint32]*anypb.Any {
	if m != nil {
		return m.ChannelViewMap
	}
	return nil
}

type FilterTabReq struct {
	MarketId             uint32   `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,2,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	TerminalType         uint32   `protobuf:"varint,3,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,4,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	Uid                  uint32   `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelPkgId         string   `protobuf:"bytes,6,opt,name=channel_pkg_id,json=channelPkgId,proto3" json:"channel_pkg_id,omitempty"`
	TabIds               []uint32 `protobuf:"varint,7,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	CategoryIds          []uint32 `protobuf:"varint,8,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FilterTabReq) Reset()         { *m = FilterTabReq{} }
func (m *FilterTabReq) String() string { return proto.CompactTextString(m) }
func (*FilterTabReq) ProtoMessage()    {}
func (*FilterTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_middle_e3fd144a503c0313, []int{4}
}
func (m *FilterTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterTabReq.Unmarshal(m, b)
}
func (m *FilterTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterTabReq.Marshal(b, m, deterministic)
}
func (dst *FilterTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterTabReq.Merge(dst, src)
}
func (m *FilterTabReq) XXX_Size() int {
	return xxx_messageInfo_FilterTabReq.Size(m)
}
func (m *FilterTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_FilterTabReq proto.InternalMessageInfo

func (m *FilterTabReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *FilterTabReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *FilterTabReq) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *FilterTabReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *FilterTabReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FilterTabReq) GetChannelPkgId() string {
	if m != nil {
		return m.ChannelPkgId
	}
	return ""
}

func (m *FilterTabReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *FilterTabReq) GetCategoryIds() []uint32 {
	if m != nil {
		return m.CategoryIds
	}
	return nil
}

type FilterTabResp struct {
	TabIds               []uint32 `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	CategoryIds          []uint32 `protobuf:"varint,2,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FilterTabResp) Reset()         { *m = FilterTabResp{} }
func (m *FilterTabResp) String() string { return proto.CompactTextString(m) }
func (*FilterTabResp) ProtoMessage()    {}
func (*FilterTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_middle_e3fd144a503c0313, []int{5}
}
func (m *FilterTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterTabResp.Unmarshal(m, b)
}
func (m *FilterTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterTabResp.Marshal(b, m, deterministic)
}
func (dst *FilterTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterTabResp.Merge(dst, src)
}
func (m *FilterTabResp) XXX_Size() int {
	return xxx_messageInfo_FilterTabResp.Size(m)
}
func (m *FilterTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_FilterTabResp proto.InternalMessageInfo

func (m *FilterTabResp) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *FilterTabResp) GetCategoryIds() []uint32 {
	if m != nil {
		return m.CategoryIds
	}
	return nil
}

type GetFastPcSupportTabListReq struct {
	// 是否需要走玩法的过滤
	NeedTabFilter        bool     `protobuf:"varint,1,opt,name=need_tab_filter,json=needTabFilter,proto3" json:"need_tab_filter,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFastPcSupportTabListReq) Reset()         { *m = GetFastPcSupportTabListReq{} }
func (m *GetFastPcSupportTabListReq) String() string { return proto.CompactTextString(m) }
func (*GetFastPcSupportTabListReq) ProtoMessage()    {}
func (*GetFastPcSupportTabListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_middle_e3fd144a503c0313, []int{6}
}
func (m *GetFastPcSupportTabListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFastPcSupportTabListReq.Unmarshal(m, b)
}
func (m *GetFastPcSupportTabListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFastPcSupportTabListReq.Marshal(b, m, deterministic)
}
func (dst *GetFastPcSupportTabListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFastPcSupportTabListReq.Merge(dst, src)
}
func (m *GetFastPcSupportTabListReq) XXX_Size() int {
	return xxx_messageInfo_GetFastPcSupportTabListReq.Size(m)
}
func (m *GetFastPcSupportTabListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFastPcSupportTabListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFastPcSupportTabListReq proto.InternalMessageInfo

func (m *GetFastPcSupportTabListReq) GetNeedTabFilter() bool {
	if m != nil {
		return m.NeedTabFilter
	}
	return false
}

type GetFastPcSupportTabListResp struct {
	Tabs                 []*GetFastPcSupportTabListResp_TabInfo `protobuf:"bytes,1,rep,name=tabs,proto3" json:"tabs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *GetFastPcSupportTabListResp) Reset()         { *m = GetFastPcSupportTabListResp{} }
func (m *GetFastPcSupportTabListResp) String() string { return proto.CompactTextString(m) }
func (*GetFastPcSupportTabListResp) ProtoMessage()    {}
func (*GetFastPcSupportTabListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_middle_e3fd144a503c0313, []int{7}
}
func (m *GetFastPcSupportTabListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFastPcSupportTabListResp.Unmarshal(m, b)
}
func (m *GetFastPcSupportTabListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFastPcSupportTabListResp.Marshal(b, m, deterministic)
}
func (dst *GetFastPcSupportTabListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFastPcSupportTabListResp.Merge(dst, src)
}
func (m *GetFastPcSupportTabListResp) XXX_Size() int {
	return xxx_messageInfo_GetFastPcSupportTabListResp.Size(m)
}
func (m *GetFastPcSupportTabListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFastPcSupportTabListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFastPcSupportTabListResp proto.InternalMessageInfo

func (m *GetFastPcSupportTabListResp) GetTabs() []*GetFastPcSupportTabListResp_TabInfo {
	if m != nil {
		return m.Tabs
	}
	return nil
}

type GetFastPcSupportTabListResp_TabInfo struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFastPcSupportTabListResp_TabInfo) Reset()         { *m = GetFastPcSupportTabListResp_TabInfo{} }
func (m *GetFastPcSupportTabListResp_TabInfo) String() string { return proto.CompactTextString(m) }
func (*GetFastPcSupportTabListResp_TabInfo) ProtoMessage()    {}
func (*GetFastPcSupportTabListResp_TabInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_middle_e3fd144a503c0313, []int{7, 0}
}
func (m *GetFastPcSupportTabListResp_TabInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFastPcSupportTabListResp_TabInfo.Unmarshal(m, b)
}
func (m *GetFastPcSupportTabListResp_TabInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFastPcSupportTabListResp_TabInfo.Marshal(b, m, deterministic)
}
func (dst *GetFastPcSupportTabListResp_TabInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFastPcSupportTabListResp_TabInfo.Merge(dst, src)
}
func (m *GetFastPcSupportTabListResp_TabInfo) XXX_Size() int {
	return xxx_messageInfo_GetFastPcSupportTabListResp_TabInfo.Size(m)
}
func (m *GetFastPcSupportTabListResp_TabInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFastPcSupportTabListResp_TabInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetFastPcSupportTabListResp_TabInfo proto.InternalMessageInfo

func (m *GetFastPcSupportTabListResp_TabInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetFastPcSupportTabListResp_TabInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func init() {
	proto.RegisterType((*BatGetGameChannelViewMapReq)(nil), "channel_play_middle.BatGetGameChannelViewMapReq")
	proto.RegisterType((*SourceListParams)(nil), "channel_play_middle.SourceListParams")
	proto.RegisterType((*RcmdInfo)(nil), "channel_play_middle.RcmdInfo")
	proto.RegisterMapType((map[uint32]*common.ChannelInfo)(nil), "channel_play_middle.RcmdInfo.ChannelInfoMapEntry")
	proto.RegisterType((*BatGetGameChannelViewMapResp)(nil), "channel_play_middle.BatGetGameChannelViewMapResp")
	proto.RegisterMapType((map[uint32]*anypb.Any)(nil), "channel_play_middle.BatGetGameChannelViewMapResp.ChannelViewMapEntry")
	proto.RegisterType((*FilterTabReq)(nil), "channel_play_middle.FilterTabReq")
	proto.RegisterType((*FilterTabResp)(nil), "channel_play_middle.FilterTabResp")
	proto.RegisterType((*GetFastPcSupportTabListReq)(nil), "channel_play_middle.GetFastPcSupportTabListReq")
	proto.RegisterType((*GetFastPcSupportTabListResp)(nil), "channel_play_middle.GetFastPcSupportTabListResp")
	proto.RegisterType((*GetFastPcSupportTabListResp_TabInfo)(nil), "channel_play_middle.GetFastPcSupportTabListResp.TabInfo")
	proto.RegisterEnum("channel_play_middle.ReqSource", ReqSource_name, ReqSource_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelPlayMiddleClient is the client API for ChannelPlayMiddle service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelPlayMiddleClient interface {
	BatGetGameChannelViewMap(ctx context.Context, in *BatGetGameChannelViewMapReq, opts ...grpc.CallOption) (*BatGetGameChannelViewMapResp, error)
	FilterTab(ctx context.Context, in *FilterTabReq, opts ...grpc.CallOption) (*FilterTabResp, error)
	GetFastPcSupportTabList(ctx context.Context, in *GetFastPcSupportTabListReq, opts ...grpc.CallOption) (*GetFastPcSupportTabListResp, error)
}

type channelPlayMiddleClient struct {
	cc *grpc.ClientConn
}

func NewChannelPlayMiddleClient(cc *grpc.ClientConn) ChannelPlayMiddleClient {
	return &channelPlayMiddleClient{cc}
}

func (c *channelPlayMiddleClient) BatGetGameChannelViewMap(ctx context.Context, in *BatGetGameChannelViewMapReq, opts ...grpc.CallOption) (*BatGetGameChannelViewMapResp, error) {
	out := new(BatGetGameChannelViewMapResp)
	err := c.cc.Invoke(ctx, "/channel_play_middle.ChannelPlayMiddle/BatGetGameChannelViewMap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayMiddleClient) FilterTab(ctx context.Context, in *FilterTabReq, opts ...grpc.CallOption) (*FilterTabResp, error) {
	out := new(FilterTabResp)
	err := c.cc.Invoke(ctx, "/channel_play_middle.ChannelPlayMiddle/FilterTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayMiddleClient) GetFastPcSupportTabList(ctx context.Context, in *GetFastPcSupportTabListReq, opts ...grpc.CallOption) (*GetFastPcSupportTabListResp, error) {
	out := new(GetFastPcSupportTabListResp)
	err := c.cc.Invoke(ctx, "/channel_play_middle.ChannelPlayMiddle/GetFastPcSupportTabList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelPlayMiddleServer is the server API for ChannelPlayMiddle service.
type ChannelPlayMiddleServer interface {
	BatGetGameChannelViewMap(context.Context, *BatGetGameChannelViewMapReq) (*BatGetGameChannelViewMapResp, error)
	FilterTab(context.Context, *FilterTabReq) (*FilterTabResp, error)
	GetFastPcSupportTabList(context.Context, *GetFastPcSupportTabListReq) (*GetFastPcSupportTabListResp, error)
}

func RegisterChannelPlayMiddleServer(s *grpc.Server, srv ChannelPlayMiddleServer) {
	s.RegisterService(&_ChannelPlayMiddle_serviceDesc, srv)
}

func _ChannelPlayMiddle_BatGetGameChannelViewMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetGameChannelViewMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayMiddleServer).BatGetGameChannelViewMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_middle.ChannelPlayMiddle/BatGetGameChannelViewMap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayMiddleServer).BatGetGameChannelViewMap(ctx, req.(*BatGetGameChannelViewMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayMiddle_FilterTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayMiddleServer).FilterTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_middle.ChannelPlayMiddle/FilterTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayMiddleServer).FilterTab(ctx, req.(*FilterTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayMiddle_GetFastPcSupportTabList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFastPcSupportTabListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayMiddleServer).GetFastPcSupportTabList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_middle.ChannelPlayMiddle/GetFastPcSupportTabList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayMiddleServer).GetFastPcSupportTabList(ctx, req.(*GetFastPcSupportTabListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelPlayMiddle_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_play_middle.ChannelPlayMiddle",
	HandlerType: (*ChannelPlayMiddleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatGetGameChannelViewMap",
			Handler:    _ChannelPlayMiddle_BatGetGameChannelViewMap_Handler,
		},
		{
			MethodName: "FilterTab",
			Handler:    _ChannelPlayMiddle_FilterTab_Handler,
		},
		{
			MethodName: "GetFastPcSupportTabList",
			Handler:    _ChannelPlayMiddle_GetFastPcSupportTabList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channel-play-middle/channel-play-middle.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-play-middle/channel-play-middle.proto", fileDescriptor_channel_play_middle_e3fd144a503c0313)
}

var fileDescriptor_channel_play_middle_e3fd144a503c0313 = []byte{
	// 1017 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x56, 0x5d, 0x6f, 0xe3, 0x44,
	0x17, 0x7e, 0x9d, 0xf4, 0x2b, 0x27, 0x4d, 0x9b, 0x77, 0x0a, 0x5b, 0xc7, 0xa5, 0xd0, 0x0d, 0xec,
	0xaa, 0x5a, 0x51, 0x67, 0x37, 0xb0, 0x62, 0xf9, 0x10, 0x52, 0x3f, 0xd2, 0x62, 0xd1, 0x96, 0xe0,
	0x64, 0x17, 0xc1, 0x5e, 0x58, 0x13, 0x7b, 0x62, 0xac, 0xf8, 0x2b, 0x9e, 0x49, 0x16, 0x4b, 0x48,
	0xdc, 0x70, 0xc9, 0x0f, 0xe1, 0x82, 0xdf, 0x85, 0x90, 0xf8, 0x13, 0x68, 0x66, 0x1c, 0xe3, 0x2c,
	0x6e, 0x57, 0xbd, 0xea, 0xe4, 0x39, 0xcf, 0x79, 0x66, 0xce, 0x99, 0xe7, 0x4c, 0x0d, 0x5f, 0x30,
	0xd6, 0x99, 0xce, 0x3c, 0x7b, 0x42, 0x3d, 0x7f, 0x4e, 0x92, 0x8e, 0xfd, 0x23, 0x0e, 0x43, 0xe2,
	0x1f, 0xc5, 0x3e, 0x4e, 0x8f, 0x02, 0xcf, 0x71, 0x7c, 0x52, 0x86, 0xe9, 0x71, 0x12, 0xb1, 0x08,
	0xed, 0x64, 0x21, 0x8b, 0x87, 0x2c, 0x19, 0xd2, 0x8e, 0x5e, 0x93, 0x4c, 0xec, 0xc0, 0x39, 0xf2,
	0x23, 0x1b, 0xfb, 0x1d, 0x3b, 0x0a, 0x82, 0x28, 0xcc, 0xfe, 0x48, 0x0d, 0xed, 0xe3, 0x37, 0xd2,
	0x59, 0x14, 0x7b, 0xb6, 0x95, 0x6d, 0x95, 0x65, 0xb5, 0xdc, 0x28, 0x72, 0x7d, 0xd2, 0x11, 0xbf,
	0x46, 0xb3, 0x71, 0x07, 0x87, 0xa9, 0x0c, 0xb5, 0xff, 0x56, 0x60, 0xef, 0x04, 0xb3, 0x0b, 0xc2,
	0x2e, 0x70, 0x40, 0x4e, 0x65, 0xda, 0x0b, 0x8f, 0xbc, 0xba, 0xc2, 0xb1, 0x49, 0xa6, 0x68, 0x1f,
	0x20, 0x21, 0x53, 0x8b, 0x46, 0xb3, 0xc4, 0x26, 0xaa, 0x72, 0xa0, 0x1c, 0x36, 0xcc, 0x5a, 0x42,
	0xa6, 0x03, 0x01, 0xa0, 0xf7, 0xa0, 0xbe, 0xa8, 0xca, 0x73, 0xa8, 0x5a, 0x39, 0xa8, 0x1e, 0x36,
	0x4c, 0xc8, 0x20, 0xc3, 0xa1, 0xe8, 0x0c, 0xb6, 0x12, 0xc2, 0x8f, 0x46, 0x42, 0xc7, 0xf2, 0xc2,
	0x71, 0xa4, 0x56, 0x0f, 0x94, 0xc3, 0x7a, 0x77, 0x5f, 0x2f, 0xe9, 0x86, 0x6e, 0xda, 0x81, 0x63,
	0x84, 0xe3, 0xc8, 0x6c, 0xe4, 0x49, 0xfc, 0x27, 0x3a, 0x87, 0xba, 0xef, 0x51, 0x66, 0xc5, 0x38,
	0xc1, 0x01, 0x55, 0x57, 0x84, 0xc4, 0x83, 0x52, 0x09, 0x79, 0xb0, 0x4b, 0x8f, 0xb2, 0xbe, 0x20,
	0x9b, 0xe0, 0xe7, 0xeb, 0xf6, 0x1f, 0x15, 0x68, 0xbe, 0x4e, 0x40, 0x9f, 0x42, 0x6b, 0x21, 0x24,
	0x36, 0x21, 0x21, 0x23, 0xc9, 0x72, 0xc5, 0xf7, 0x32, 0x02, 0xcf, 0xea, 0xf1, 0x70, 0x56, 0xfe,
	0x43, 0xd8, 0x16, 0x29, 0x94, 0xa5, 0x3e, 0xb1, 0x58, 0x1a, 0x13, 0xb5, 0x22, 0x12, 0x1a, 0x1c,
	0x1e, 0x70, 0x74, 0x98, 0xc6, 0x82, 0xe7, 0x92, 0xd0, 0x9a, 0x7b, 0xe4, 0xd5, 0x42, 0xb8, 0x2a,
	0x5a, 0xd5, 0x70, 0x49, 0xc8, 0xbb, 0x9d, 0xe9, 0x7d, 0x08, 0x28, 0xaf, 0x09, 0xdb, 0x13, 0xec,
	0x12, 0xcb, 0x73, 0x44, 0xb9, 0x35, 0xb3, 0x99, 0x45, 0xfa, 0x32, 0x60, 0x38, 0xe8, 0x6d, 0x58,
	0x63, 0x78, 0xc4, 0x19, 0xab, 0x62, 0xd3, 0x55, 0x86, 0x47, 0x86, 0x83, 0xde, 0x85, 0xba, 0x84,
	0xa9, 0x45, 0x59, 0xa2, 0xae, 0x89, 0xec, 0x9a, 0x88, 0xd1, 0x01, 0x4b, 0xd0, 0x21, 0x34, 0x6d,
	0xcc, 0x88, 0x1b, 0x25, 0x69, 0x4e, 0x5a, 0x17, 0xa4, 0xad, 0x05, 0x2e, 0x99, 0xed, 0x5f, 0x2b,
	0xb0, 0xb1, 0xb8, 0x12, 0xf4, 0x25, 0x34, 0x28, 0xf1, 0xc7, 0x16, 0x77, 0x1b, 0xf3, 0xa2, 0x50,
	0xb4, 0xa6, 0xde, 0x6d, 0xe9, 0xdc, 0x83, 0x7a, 0xe6, 0xd2, 0xcb, 0x2c, 0x28, 0x2e, 0x71, 0x93,
	0xf3, 0x17, 0x08, 0x7a, 0x09, 0xcd, 0xdc, 0x2a, 0xe1, 0x38, 0xb2, 0x02, 0x1c, 0x8b, 0x26, 0xd4,
	0xbb, 0x4f, 0x6e, 0xf5, 0x82, 0x9e, 0x99, 0x92, 0xaf, 0xaf, 0x70, 0xdc, 0x0b, 0x59, 0x92, 0x9a,
	0x5b, 0xf6, 0x12, 0xa8, 0xbd, 0x84, 0x9d, 0x12, 0x1a, 0x6a, 0x42, 0x75, 0x42, 0xd2, 0xec, 0x12,
	0xf9, 0x12, 0xe9, 0xb0, 0x3a, 0xc7, 0xfe, 0x4c, 0xde, 0x53, 0xbd, 0xab, 0x2e, 0x9d, 0xbe, 0x20,
	0x61, 0x4a, 0xda, 0x67, 0x95, 0x67, 0x4a, 0xfb, 0x2f, 0x05, 0xde, 0xb9, 0x79, 0x46, 0x68, 0x8c,
	0xa2, 0x7f, 0x4b, 0x13, 0x57, 0xcc, 0x4b, 0x53, 0x44, 0x69, 0xbd, 0xd2, 0xd2, 0x6e, 0x13, 0xd3,
	0x97, 0xa1, 0xe5, 0x72, 0x33, 0x50, 0xfb, 0x2e, 0x2f, 0xb7, 0x48, 0x2b, 0x29, 0xf7, 0xd1, 0x72,
	0xb9, 0x6f, 0xe9, 0xf2, 0x25, 0xd0, 0x17, 0x2f, 0x81, 0x7e, 0x1c, 0xa6, 0xc5, 0x52, 0x7f, 0xab,
	0xc0, 0xe6, 0xb9, 0xe7, 0x33, 0x92, 0x0c, 0xf1, 0x88, 0xcf, 0xff, 0x1e, 0xd4, 0x02, 0x9c, 0x4c,
	0x08, 0xe3, 0x36, 0x93, 0xc2, 0x1b, 0x12, 0x30, 0x1c, 0x31, 0xfd, 0xbe, 0x47, 0x42, 0x56, 0xb4,
	0x3e, 0x48, 0x48, 0xf8, 0xfe, 0x7d, 0x68, 0x30, 0x92, 0x04, 0x5e, 0x88, 0x7d, 0x49, 0xa9, 0x0a,
	0xca, 0xe6, 0x02, 0x14, 0xa4, 0x07, 0xb0, 0x95, 0xa9, 0xcc, 0x49, 0x42, 0xb9, 0xb3, 0x56, 0xe4,
	0x0c, 0x49, 0xf4, 0x85, 0x04, 0x79, 0x71, 0xb3, 0xdc, 0xea, 0x7c, 0x89, 0x3e, 0x80, 0xad, 0xbc,
	0xbb, 0x13, 0x97, 0x1f, 0x50, 0x7a, 0x7d, 0x73, 0x31, 0x29, 0x13, 0xd7, 0x70, 0xd0, 0x2e, 0xac,
	0x67, 0xe3, 0xa0, 0xae, 0x8b, 0x99, 0x5b, 0x93, 0xa3, 0x80, 0xee, 0xc3, 0x66, 0x71, 0x0e, 0xd4,
	0x0d, 0x11, 0xad, 0x17, 0x66, 0xa0, 0xfd, 0x35, 0x34, 0x0a, 0xdd, 0xa0, 0x71, 0x51, 0x4c, 0xb9,
	0x55, 0xac, 0xf2, 0x5f, 0xb1, 0x33, 0xd0, 0x2e, 0x08, 0x3b, 0xc7, 0x94, 0xf5, 0xed, 0xc1, 0x2c,
	0x8e, 0xa3, 0x84, 0x0d, 0xf1, 0x88, 0x3f, 0x29, 0xbc, 0xd1, 0x0f, 0x61, 0x3b, 0x24, 0xc4, 0xb1,
	0xb8, 0xfc, 0x58, 0xec, 0x29, 0xda, 0xbd, 0x61, 0x36, 0x38, 0x3c, 0xc4, 0x23, 0x79, 0x90, 0xf6,
	0xef, 0x0a, 0xec, 0xdd, 0x28, 0x43, 0x63, 0x74, 0x09, 0x2b, 0x0c, 0x8f, 0x68, 0xe6, 0xbf, 0x67,
	0xa5, 0xfe, 0xbb, 0x25, 0x5f, 0x1f, 0xe2, 0x91, 0xf0, 0xbf, 0x50, 0xd1, 0x3e, 0x87, 0xf5, 0x0c,
	0x28, 0xbc, 0x36, 0x4a, 0xf1, 0xb5, 0x69, 0xc1, 0x06, 0x87, 0x43, 0x1c, 0x48, 0x03, 0xd4, 0x4c,
	0xde, 0xa1, 0x6b, 0x1c, 0x90, 0x47, 0x3f, 0x41, 0xcd, 0xcc, 0xff, 0x53, 0x68, 0x70, 0xcf, 0xec,
	0x7d, 0x6b, 0x0d, 0xbe, 0x79, 0x6e, 0x9e, 0xf6, 0xac, 0xe7, 0xd7, 0x83, 0x7e, 0xef, 0xd4, 0x38,
	0x37, 0x7a, 0x67, 0xcd, 0xff, 0xa1, 0x7d, 0x68, 0x15, 0x62, 0x83, 0xde, 0xb1, 0x79, 0xfa, 0x95,
	0x75, 0xf2, 0xbd, 0x75, 0x7d, 0x7c, 0xd5, 0x6b, 0x2a, 0x68, 0x0f, 0x76, 0x4b, 0xc3, 0xc6, 0x59,
	0xb3, 0x82, 0x76, 0x60, 0xbb, 0x10, 0xbc, 0x34, 0x06, 0xc3, 0x66, 0xb5, 0xfb, 0x67, 0x05, 0xfe,
	0x9f, 0x0d, 0x48, 0xdf, 0xc7, 0xe9, 0x95, 0x28, 0x1b, 0xfd, 0x02, 0xea, 0x4d, 0x93, 0x87, 0x1e,
	0xdf, 0x71, 0x50, 0xa7, 0xda, 0x93, 0x3b, 0x8f, 0x36, 0x32, 0xa1, 0x96, 0xdb, 0x09, 0xdd, 0x2f,
	0xcd, 0x2f, 0x0e, 0x9f, 0xd6, 0x7e, 0x13, 0x85, 0xc6, 0xe8, 0x67, 0xd8, 0xbd, 0xe1, 0x3a, 0x51,
	0xe7, 0x6e, 0x97, 0x3f, 0xd5, 0x1e, 0xdf, 0xd5, 0x2d, 0x27, 0x9f, 0xfc, 0xf0, 0xd4, 0x8d, 0x7c,
	0x1c, 0xba, 0xfa, 0xd3, 0x2e, 0x63, 0xfc, 0x1d, 0x95, 0x1f, 0x19, 0x76, 0xe4, 0x77, 0x28, 0x49,
	0xe6, 0x9e, 0x4d, 0x68, 0xd9, 0x27, 0xd1, 0x68, 0x4d, 0xd0, 0x3e, 0xfa, 0x27, 0x00, 0x00, 0xff,
	0xff, 0xd7, 0xa4, 0xb3, 0xe6, 0x53, 0x09, 0x00, 0x00,
}
