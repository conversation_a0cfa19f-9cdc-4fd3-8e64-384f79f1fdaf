// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd-ai-partner/rcmd_ai_partner_kafka.proto

package rcmd_ai_partner_kafka // import "golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner_kafka"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type UserSource int32

const (
	UserSource_UserSource_Unknown     UserSource = 0
	UserSource_UserSource_ChatIn3Day  UserSource = 1
	UserSource_UserSource_GettingUser UserSource = 2
	UserSource_UserSource_SelectTime  UserSource = 3
)

var UserSource_name = map[int32]string{
	0: "UserSource_Unknown",
	1: "UserSource_ChatIn3Day",
	2: "UserSource_GettingUser",
	3: "UserSource_SelectTime",
}
var UserSource_value = map[string]int32{
	"UserSource_Unknown":     0,
	"UserSource_ChatIn3Day":  1,
	"UserSource_GettingUser": 2,
	"UserSource_SelectTime":  3,
}

func (x UserSource) String() string {
	return proto.EnumName(UserSource_name, int32(x))
}
func (UserSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{0}
}

type MsgType int32

const (
	MsgType_Invalid              MsgType = 0
	MsgType_Text                 MsgType = 1
	MsgType_Meme                 MsgType = 3
	MsgType_Silence              MsgType = 4
	MsgType_AirTick              MsgType = 5
	MsgType_Common               MsgType = 6
	MsgType_AnimatedMeme         MsgType = 10
	MsgType_RichText             MsgType = 11
	MsgType_RelationshipLetter   MsgType = 12
	MsgType_NewPhotograph        MsgType = 13
	MsgType_ListenSong           MsgType = 14
	MsgType_SetName              MsgType = 15
	MsgType_SetUserNickname      MsgType = 16
	MsgType_SetRoleTip           MsgType = 17
	MsgType_UserAutoReply        MsgType = 18
	MsgType_UserGuide            MsgType = 19
	MsgType_StoryEntrance        MsgType = 20
	MsgType_StoryNarration       MsgType = 21
	MsgType_StoryText            MsgType = 22
	MsgType_StoryImage           MsgType = 23
	MsgType_StoryFinish          MsgType = 24
	MsgType_StoryOptForUserReply MsgType = 25
	MsgType_StoryRewardsNotify   MsgType = 26
	MsgType_NewAtmosphere        MsgType = 27
	MsgType_GameCharacterTips    MsgType = 28
	MsgType_TTSMsg               MsgType = 29
	MsgType_UserGuideV2          MsgType = 30
	MsgType_StoryAnimatedMeme    MsgType = 31
	MsgType_SetPartnerInfo       MsgType = 32
	MsgType_QuickReply           MsgType = 33
	MsgType_StoryBookEntrance    MsgType = 34
	MsgType_StoryTTSMsg          MsgType = 35
	MsgType_VoiceChatReply       MsgType = 36
	MsgType_PetReply             MsgType = 37
	MsgType_AIRcmdReply          MsgType = 38
	MsgType_ContinueChat         MsgType = 39
	MsgType_ReachChatLimitMsg    MsgType = 40
	MsgType_PlayTarotTip         MsgType = 41
	MsgType_TargetCompleteMsg    MsgType = 42
	MsgType_TargetTriggerMsg     MsgType = 43
)

var MsgType_name = map[int32]string{
	0:  "Invalid",
	1:  "Text",
	3:  "Meme",
	4:  "Silence",
	5:  "AirTick",
	6:  "Common",
	10: "AnimatedMeme",
	11: "RichText",
	12: "RelationshipLetter",
	13: "NewPhotograph",
	14: "ListenSong",
	15: "SetName",
	16: "SetUserNickname",
	17: "SetRoleTip",
	18: "UserAutoReply",
	19: "UserGuide",
	20: "StoryEntrance",
	21: "StoryNarration",
	22: "StoryText",
	23: "StoryImage",
	24: "StoryFinish",
	25: "StoryOptForUserReply",
	26: "StoryRewardsNotify",
	27: "NewAtmosphere",
	28: "GameCharacterTips",
	29: "TTSMsg",
	30: "UserGuideV2",
	31: "StoryAnimatedMeme",
	32: "SetPartnerInfo",
	33: "QuickReply",
	34: "StoryBookEntrance",
	35: "StoryTTSMsg",
	36: "VoiceChatReply",
	37: "PetReply",
	38: "AIRcmdReply",
	39: "ContinueChat",
	40: "ReachChatLimitMsg",
	41: "PlayTarotTip",
	42: "TargetCompleteMsg",
	43: "TargetTriggerMsg",
}
var MsgType_value = map[string]int32{
	"Invalid":              0,
	"Text":                 1,
	"Meme":                 3,
	"Silence":              4,
	"AirTick":              5,
	"Common":               6,
	"AnimatedMeme":         10,
	"RichText":             11,
	"RelationshipLetter":   12,
	"NewPhotograph":        13,
	"ListenSong":           14,
	"SetName":              15,
	"SetUserNickname":      16,
	"SetRoleTip":           17,
	"UserAutoReply":        18,
	"UserGuide":            19,
	"StoryEntrance":        20,
	"StoryNarration":       21,
	"StoryText":            22,
	"StoryImage":           23,
	"StoryFinish":          24,
	"StoryOptForUserReply": 25,
	"StoryRewardsNotify":   26,
	"NewAtmosphere":        27,
	"GameCharacterTips":    28,
	"TTSMsg":               29,
	"UserGuideV2":          30,
	"StoryAnimatedMeme":    31,
	"SetPartnerInfo":       32,
	"QuickReply":           33,
	"StoryBookEntrance":    34,
	"StoryTTSMsg":          35,
	"VoiceChatReply":       36,
	"PetReply":             37,
	"AIRcmdReply":          38,
	"ContinueChat":         39,
	"ReachChatLimitMsg":    40,
	"PlayTarotTip":         41,
	"TargetCompleteMsg":    42,
	"TargetTriggerMsg":     43,
}

func (x MsgType) String() string {
	return proto.EnumName(MsgType_name, int32(x))
}
func (MsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{1}
}

// 上下文场景
type CtxScene int32

const (
	CtxScene_CtxScene_Unknown      CtxScene = 0
	CtxScene_CtxScene_PetSleepTalk CtxScene = 3001
)

var CtxScene_name = map[int32]string{
	0:    "CtxScene_Unknown",
	3001: "CtxScene_PetSleepTalk",
}
var CtxScene_value = map[string]int32{
	"CtxScene_Unknown":      0,
	"CtxScene_PetSleepTalk": 3001,
}

func (x CtxScene) String() string {
	return proto.EnumName(CtxScene_name, int32(x))
}
func (CtxScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{2}
}

type MP_BreakSilenceGreeting_Source int32

const (
	MP_BreakSilenceGreeting_Source_EnterChatting  MP_BreakSilenceGreeting_Source = 0
	MP_BreakSilenceGreeting_Source_BotSelfTrigger MP_BreakSilenceGreeting_Source = 1
)

var MP_BreakSilenceGreeting_Source_name = map[int32]string{
	0: "Source_EnterChatting",
	1: "Source_BotSelfTrigger",
}
var MP_BreakSilenceGreeting_Source_value = map[string]int32{
	"Source_EnterChatting":  0,
	"Source_BotSelfTrigger": 1,
}

func (x MP_BreakSilenceGreeting_Source) String() string {
	return proto.EnumName(MP_BreakSilenceGreeting_Source_name, int32(x))
}
func (MP_BreakSilenceGreeting_Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{0, 0}
}

type MP_Greeting_GreetingType int32

const (
	MP_Greeting_GreetingType_Unknown      MP_Greeting_GreetingType = 0
	MP_Greeting_GreetingType_EnterPetHome MP_Greeting_GreetingType = 1
)

var MP_Greeting_GreetingType_name = map[int32]string{
	0: "GreetingType_Unknown",
	1: "GreetingType_EnterPetHome",
}
var MP_Greeting_GreetingType_value = map[string]int32{
	"GreetingType_Unknown":      0,
	"GreetingType_EnterPetHome": 1,
}

func (x MP_Greeting_GreetingType) String() string {
	return proto.EnumName(MP_Greeting_GreetingType_name, int32(x))
}
func (MP_Greeting_GreetingType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{1, 0}
}

type MP_LoginGreeting_Type int32

const (
	MP_LoginGreeting_LoginEventType_Login     MP_LoginGreeting_Type = 0
	MP_LoginGreeting_LoginEventType_Register  MP_LoginGreeting_Type = 1
	MP_LoginGreeting_LoginEventType_VisitHome MP_LoginGreeting_Type = 2
)

var MP_LoginGreeting_Type_name = map[int32]string{
	0: "LoginEventType_Login",
	1: "LoginEventType_Register",
	2: "LoginEventType_VisitHome",
}
var MP_LoginGreeting_Type_value = map[string]int32{
	"LoginEventType_Login":     0,
	"LoginEventType_Register":  1,
	"LoginEventType_VisitHome": 2,
}

func (x MP_LoginGreeting_Type) String() string {
	return proto.EnumName(MP_LoginGreeting_Type_name, int32(x))
}
func (MP_LoginGreeting_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{2, 0}
}

type MP_ScheduleGreeting_Type int32

const (
	MP_ScheduleGreeting_Type_Invalid         MP_ScheduleGreeting_Type = 0
	MP_ScheduleGreeting_Type_FirstGreeting   MP_ScheduleGreeting_Type = 1
	MP_ScheduleGreeting_Type_SecondGreeting  MP_ScheduleGreeting_Type = 2
	MP_ScheduleGreeting_Type_WeekendGreeting MP_ScheduleGreeting_Type = 3
	MP_ScheduleGreeting_Type_Common          MP_ScheduleGreeting_Type = 4
	MP_ScheduleGreeting_Type_Role            MP_ScheduleGreeting_Type = 5
)

var MP_ScheduleGreeting_Type_name = map[int32]string{
	0: "Type_Invalid",
	1: "Type_FirstGreeting",
	2: "Type_SecondGreeting",
	3: "Type_WeekendGreeting",
	4: "Type_Common",
	5: "Type_Role",
}
var MP_ScheduleGreeting_Type_value = map[string]int32{
	"Type_Invalid":         0,
	"Type_FirstGreeting":   1,
	"Type_SecondGreeting":  2,
	"Type_WeekendGreeting": 3,
	"Type_Common":          4,
	"Type_Role":            5,
}

func (x MP_ScheduleGreeting_Type) String() string {
	return proto.EnumName(MP_ScheduleGreeting_Type_name, int32(x))
}
func (MP_ScheduleGreeting_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{3, 0}
}

type IMEvent_RoleType int32

const (
	IMEvent_RoleType_User    IMEvent_RoleType = 0
	IMEvent_RoleType_Partner IMEvent_RoleType = 1
)

var IMEvent_RoleType_name = map[int32]string{
	0: "RoleType_User",
	1: "RoleType_Partner",
}
var IMEvent_RoleType_value = map[string]int32{
	"RoleType_User":    0,
	"RoleType_Partner": 1,
}

func (x IMEvent_RoleType) String() string {
	return proto.EnumName(IMEvent_RoleType_name, int32(x))
}
func (IMEvent_RoleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{23, 0}
}

type NoReplyEvent_Type int32

const (
	NoReplyEvent_Type_Invalid        NoReplyEvent_Type = 0
	NoReplyEvent_Type_RoleTip        NoReplyEvent_Type = 1
	NoReplyEvent_Type_SetName        NoReplyEvent_Type = 2
	NoReplyEvent_Type_SetNickname    NoReplyEvent_Type = 3
	NoReplyEvent_Type_SetPartnerInfo NoReplyEvent_Type = 4
)

var NoReplyEvent_Type_name = map[int32]string{
	0: "Type_Invalid",
	1: "Type_RoleTip",
	2: "Type_SetName",
	3: "Type_SetNickname",
	4: "Type_SetPartnerInfo",
}
var NoReplyEvent_Type_value = map[string]int32{
	"Type_Invalid":        0,
	"Type_RoleTip":        1,
	"Type_SetName":        2,
	"Type_SetNickname":    3,
	"Type_SetPartnerInfo": 4,
}

func (x NoReplyEvent_Type) String() string {
	return proto.EnumName(NoReplyEvent_Type_name, int32(x))
}
func (NoReplyEvent_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{24, 0}
}

type StoryNotify_NotifyType int32

const (
	StoryNotify_NotifyType_Invalid  StoryNotify_NotifyType = 0
	StoryNotify_NotifyType_Start    StoryNotify_NotifyType = 1
	StoryNotify_NotifyType_Restart  StoryNotify_NotifyType = 2
	StoryNotify_NotifyType_Next     StoryNotify_NotifyType = 3
	StoryNotify_NotifyType_Continue StoryNotify_NotifyType = 4
)

var StoryNotify_NotifyType_name = map[int32]string{
	0: "NotifyType_Invalid",
	1: "NotifyType_Start",
	2: "NotifyType_Restart",
	3: "NotifyType_Next",
	4: "NotifyType_Continue",
}
var StoryNotify_NotifyType_value = map[string]int32{
	"NotifyType_Invalid":  0,
	"NotifyType_Start":    1,
	"NotifyType_Restart":  2,
	"NotifyType_Next":     3,
	"NotifyType_Continue": 4,
}

func (x StoryNotify_NotifyType) String() string {
	return proto.EnumName(StoryNotify_NotifyType_name, int32(x))
}
func (StoryNotify_NotifyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{33, 0}
}

type PushSchedule_Type int32

const (
	PushSchedule_Type_Default             PushSchedule_Type = 0
	PushSchedule_Type_AfterPartnerCreated PushSchedule_Type = 1
)

var PushSchedule_Type_name = map[int32]string{
	0: "Type_Default",
	1: "Type_AfterPartnerCreated",
}
var PushSchedule_Type_value = map[string]int32{
	"Type_Default":             0,
	"Type_AfterPartnerCreated": 1,
}

func (x PushSchedule_Type) String() string {
	return proto.EnumName(PushSchedule_Type_name, int32(x))
}
func (PushSchedule_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{34, 0}
}

type WhiteListUserEvent_Source int32

const (
	WhiteListUserEvent_Source_COST   WhiteListUserEvent_Source = 0
	WhiteListUserEvent_Source_SEARCH WhiteListUserEvent_Source = 1
)

var WhiteListUserEvent_Source_name = map[int32]string{
	0: "Source_COST",
	1: "Source_SEARCH",
}
var WhiteListUserEvent_Source_value = map[string]int32{
	"Source_COST":   0,
	"Source_SEARCH": 1,
}

func (x WhiteListUserEvent_Source) String() string {
	return proto.EnumName(WhiteListUserEvent_Source_name, int32(x))
}
func (WhiteListUserEvent_Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{39, 0}
}

type StoryBookNotify_Type int32

const (
	StoryBookNotify_Type_Invalid  StoryBookNotify_Type = 0
	StoryBookNotify_Type_Start    StoryBookNotify_Type = 1
	StoryBookNotify_Type_Continue StoryBookNotify_Type = 2
)

var StoryBookNotify_Type_name = map[int32]string{
	0: "Type_Invalid",
	1: "Type_Start",
	2: "Type_Continue",
}
var StoryBookNotify_Type_value = map[string]int32{
	"Type_Invalid":  0,
	"Type_Start":    1,
	"Type_Continue": 2,
}

func (x StoryBookNotify_Type) String() string {
	return proto.EnumName(StoryBookNotify_Type_name, int32(x))
}
func (StoryBookNotify_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{40, 0}
}

type PetTipReport_Action int32

const (
	PetTipReport_Action_Invalid      PetTipReport_Action = 0
	PetTipReport_Action_DefaultClick PetTipReport_Action = 1
	PetTipReport_Action_Drag         PetTipReport_Action = 2
	PetTipReport_Action_PeekClick    PetTipReport_Action = 3
	PetTipReport_Action_PeekAFK      PetTipReport_Action = 4
	PetTipReport_Action_DefaultAFK   PetTipReport_Action = 5
)

var PetTipReport_Action_name = map[int32]string{
	0: "Action_Invalid",
	1: "Action_DefaultClick",
	2: "Action_Drag",
	3: "Action_PeekClick",
	4: "Action_PeekAFK",
	5: "Action_DefaultAFK",
}
var PetTipReport_Action_value = map[string]int32{
	"Action_Invalid":      0,
	"Action_DefaultClick": 1,
	"Action_Drag":         2,
	"Action_PeekClick":    3,
	"Action_PeekAFK":      4,
	"Action_DefaultAFK":   5,
}

func (x PetTipReport_Action) String() string {
	return proto.EnumName(PetTipReport_Action_name, int32(x))
}
func (PetTipReport_Action) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{42, 0}
}

type MP_BreakSilenceGreeting struct {
	Uid                  uint32                         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32                         `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	Source               MP_BreakSilenceGreeting_Source `protobuf:"varint,3,opt,name=source,proto3,enum=rcmd.rcmd_ai_partner_kafka.MP_BreakSilenceGreeting_Source" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *MP_BreakSilenceGreeting) Reset()         { *m = MP_BreakSilenceGreeting{} }
func (m *MP_BreakSilenceGreeting) String() string { return proto.CompactTextString(m) }
func (*MP_BreakSilenceGreeting) ProtoMessage()    {}
func (*MP_BreakSilenceGreeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{0}
}
func (m *MP_BreakSilenceGreeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_BreakSilenceGreeting.Unmarshal(m, b)
}
func (m *MP_BreakSilenceGreeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_BreakSilenceGreeting.Marshal(b, m, deterministic)
}
func (dst *MP_BreakSilenceGreeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_BreakSilenceGreeting.Merge(dst, src)
}
func (m *MP_BreakSilenceGreeting) XXX_Size() int {
	return xxx_messageInfo_MP_BreakSilenceGreeting.Size(m)
}
func (m *MP_BreakSilenceGreeting) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_BreakSilenceGreeting.DiscardUnknown(m)
}

var xxx_messageInfo_MP_BreakSilenceGreeting proto.InternalMessageInfo

func (m *MP_BreakSilenceGreeting) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_BreakSilenceGreeting) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *MP_BreakSilenceGreeting) GetSource() MP_BreakSilenceGreeting_Source {
	if m != nil {
		return m.Source
	}
	return MP_BreakSilenceGreeting_Source_EnterChatting
}

type MP_Greeting struct {
	Uid                  uint32                   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32                   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32                   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	GreetingType         MP_Greeting_GreetingType `protobuf:"varint,4,opt,name=greeting_type,json=greetingType,proto3,enum=rcmd.rcmd_ai_partner_kafka.MP_Greeting_GreetingType" json:"greeting_type,omitempty"`
	Source               string                   `protobuf:"bytes,5,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *MP_Greeting) Reset()         { *m = MP_Greeting{} }
func (m *MP_Greeting) String() string { return proto.CompactTextString(m) }
func (*MP_Greeting) ProtoMessage()    {}
func (*MP_Greeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{1}
}
func (m *MP_Greeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_Greeting.Unmarshal(m, b)
}
func (m *MP_Greeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_Greeting.Marshal(b, m, deterministic)
}
func (dst *MP_Greeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_Greeting.Merge(dst, src)
}
func (m *MP_Greeting) XXX_Size() int {
	return xxx_messageInfo_MP_Greeting.Size(m)
}
func (m *MP_Greeting) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_Greeting.DiscardUnknown(m)
}

var xxx_messageInfo_MP_Greeting proto.InternalMessageInfo

func (m *MP_Greeting) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_Greeting) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *MP_Greeting) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *MP_Greeting) GetGreetingType() MP_Greeting_GreetingType {
	if m != nil {
		return m.GreetingType
	}
	return MP_Greeting_GreetingType_Unknown
}

func (m *MP_Greeting) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

type MP_LoginGreeting struct {
	Uid                  uint32                `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MarketId             uint32                `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	CliVersion           uint32                `protobuf:"varint,3,opt,name=cli_version,json=cliVersion,proto3" json:"cli_version,omitempty"`
	Type                 MP_LoginGreeting_Type `protobuf:"varint,4,opt,name=type,proto3,enum=rcmd.rcmd_ai_partner_kafka.MP_LoginGreeting_Type" json:"type,omitempty"`
	ClientType           uint32                `protobuf:"varint,5,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	DeviceIdHex          string                `protobuf:"bytes,6,opt,name=device_id_hex,json=deviceIdHex,proto3" json:"device_id_hex,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *MP_LoginGreeting) Reset()         { *m = MP_LoginGreeting{} }
func (m *MP_LoginGreeting) String() string { return proto.CompactTextString(m) }
func (*MP_LoginGreeting) ProtoMessage()    {}
func (*MP_LoginGreeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{2}
}
func (m *MP_LoginGreeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_LoginGreeting.Unmarshal(m, b)
}
func (m *MP_LoginGreeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_LoginGreeting.Marshal(b, m, deterministic)
}
func (dst *MP_LoginGreeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_LoginGreeting.Merge(dst, src)
}
func (m *MP_LoginGreeting) XXX_Size() int {
	return xxx_messageInfo_MP_LoginGreeting.Size(m)
}
func (m *MP_LoginGreeting) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_LoginGreeting.DiscardUnknown(m)
}

var xxx_messageInfo_MP_LoginGreeting proto.InternalMessageInfo

func (m *MP_LoginGreeting) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_LoginGreeting) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *MP_LoginGreeting) GetCliVersion() uint32 {
	if m != nil {
		return m.CliVersion
	}
	return 0
}

func (m *MP_LoginGreeting) GetType() MP_LoginGreeting_Type {
	if m != nil {
		return m.Type
	}
	return MP_LoginGreeting_LoginEventType_Login
}

func (m *MP_LoginGreeting) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *MP_LoginGreeting) GetDeviceIdHex() string {
	if m != nil {
		return m.DeviceIdHex
	}
	return ""
}

type MP_ScheduleGreeting struct {
	Uid                  uint32                   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32                   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	Type                 MP_ScheduleGreeting_Type `protobuf:"varint,3,opt,name=type,proto3,enum=rcmd.rcmd_ai_partner_kafka.MP_ScheduleGreeting_Type" json:"type,omitempty"`
	ExpectedSendAt       int64                    `protobuf:"varint,4,opt,name=expected_send_at,json=expectedSendAt,proto3" json:"expected_send_at,omitempty"`
	ScheduleConfId       string                   `protobuf:"bytes,5,opt,name=schedule_conf_id,json=scheduleConfId,proto3" json:"schedule_conf_id,omitempty"`
	RoleId               uint32                   `protobuf:"varint,6,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	RoleStyle            string                   `protobuf:"bytes,7,opt,name=role_style,json=roleStyle,proto3" json:"role_style,omitempty"`
	SessionId            string                   `protobuf:"bytes,8,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	DayPushNum           uint32                   `protobuf:"varint,9,opt,name=day_push_num,json=dayPushNum,proto3" json:"day_push_num,omitempty"`
	UserSource           int32                    `protobuf:"varint,10,opt,name=user_source,json=userSource,proto3" json:"user_source,omitempty"`
	GreetContent         []string                 `protobuf:"bytes,11,rep,name=greet_content,json=greetContent,proto3" json:"greet_content,omitempty"`
	BusId                string                   `protobuf:"bytes,12,opt,name=bus_id,json=busId,proto3" json:"bus_id,omitempty"`
	IsTest               bool                     `protobuf:"varint,13,opt,name=is_test,json=isTest,proto3" json:"is_test,omitempty"`
	ReplyType            uint32                   `protobuf:"varint,14,opt,name=reply_type,json=replyType,proto3" json:"reply_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *MP_ScheduleGreeting) Reset()         { *m = MP_ScheduleGreeting{} }
func (m *MP_ScheduleGreeting) String() string { return proto.CompactTextString(m) }
func (*MP_ScheduleGreeting) ProtoMessage()    {}
func (*MP_ScheduleGreeting) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{3}
}
func (m *MP_ScheduleGreeting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_ScheduleGreeting.Unmarshal(m, b)
}
func (m *MP_ScheduleGreeting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_ScheduleGreeting.Marshal(b, m, deterministic)
}
func (dst *MP_ScheduleGreeting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_ScheduleGreeting.Merge(dst, src)
}
func (m *MP_ScheduleGreeting) XXX_Size() int {
	return xxx_messageInfo_MP_ScheduleGreeting.Size(m)
}
func (m *MP_ScheduleGreeting) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_ScheduleGreeting.DiscardUnknown(m)
}

var xxx_messageInfo_MP_ScheduleGreeting proto.InternalMessageInfo

func (m *MP_ScheduleGreeting) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_ScheduleGreeting) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *MP_ScheduleGreeting) GetType() MP_ScheduleGreeting_Type {
	if m != nil {
		return m.Type
	}
	return MP_ScheduleGreeting_Type_Invalid
}

func (m *MP_ScheduleGreeting) GetExpectedSendAt() int64 {
	if m != nil {
		return m.ExpectedSendAt
	}
	return 0
}

func (m *MP_ScheduleGreeting) GetScheduleConfId() string {
	if m != nil {
		return m.ScheduleConfId
	}
	return ""
}

func (m *MP_ScheduleGreeting) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *MP_ScheduleGreeting) GetRoleStyle() string {
	if m != nil {
		return m.RoleStyle
	}
	return ""
}

func (m *MP_ScheduleGreeting) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

func (m *MP_ScheduleGreeting) GetDayPushNum() uint32 {
	if m != nil {
		return m.DayPushNum
	}
	return 0
}

func (m *MP_ScheduleGreeting) GetUserSource() int32 {
	if m != nil {
		return m.UserSource
	}
	return 0
}

func (m *MP_ScheduleGreeting) GetGreetContent() []string {
	if m != nil {
		return m.GreetContent
	}
	return nil
}

func (m *MP_ScheduleGreeting) GetBusId() string {
	if m != nil {
		return m.BusId
	}
	return ""
}

func (m *MP_ScheduleGreeting) GetIsTest() bool {
	if m != nil {
		return m.IsTest
	}
	return false
}

func (m *MP_ScheduleGreeting) GetReplyType() uint32 {
	if m != nil {
		return m.ReplyType
	}
	return 0
}

// 用户回复角色消息
type MP_UserReply struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	ActualMsgType        MsgType  `protobuf:"varint,3,opt,name=actual_msg_type,json=actualMsgType,proto3,enum=rcmd.rcmd_ai_partner_kafka.MsgType" json:"actual_msg_type,omitempty"`
	MsgContent           string   `protobuf:"bytes,4,opt,name=msg_content,json=msgContent,proto3" json:"msg_content,omitempty"`
	Ext                  []byte   `protobuf:"bytes,5,opt,name=ext,proto3" json:"ext,omitempty"`
	TtMsgType            uint32   `protobuf:"varint,6,opt,name=tt_msg_type,json=ttMsgType,proto3" json:"tt_msg_type,omitempty"`
	MsgId                string   `protobuf:"bytes,7,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	SentAt               int64    `protobuf:"varint,8,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	TraceId              string   `protobuf:"bytes,9,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	AiRoleType           uint32   `protobuf:"varint,10,opt,name=ai_role_type,json=aiRoleType,proto3" json:"ai_role_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_UserReply) Reset()         { *m = MP_UserReply{} }
func (m *MP_UserReply) String() string { return proto.CompactTextString(m) }
func (*MP_UserReply) ProtoMessage()    {}
func (*MP_UserReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{4}
}
func (m *MP_UserReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_UserReply.Unmarshal(m, b)
}
func (m *MP_UserReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_UserReply.Marshal(b, m, deterministic)
}
func (dst *MP_UserReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_UserReply.Merge(dst, src)
}
func (m *MP_UserReply) XXX_Size() int {
	return xxx_messageInfo_MP_UserReply.Size(m)
}
func (m *MP_UserReply) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_UserReply.DiscardUnknown(m)
}

var xxx_messageInfo_MP_UserReply proto.InternalMessageInfo

func (m *MP_UserReply) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_UserReply) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *MP_UserReply) GetActualMsgType() MsgType {
	if m != nil {
		return m.ActualMsgType
	}
	return MsgType_Invalid
}

func (m *MP_UserReply) GetMsgContent() string {
	if m != nil {
		return m.MsgContent
	}
	return ""
}

func (m *MP_UserReply) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *MP_UserReply) GetTtMsgType() uint32 {
	if m != nil {
		return m.TtMsgType
	}
	return 0
}

func (m *MP_UserReply) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *MP_UserReply) GetSentAt() int64 {
	if m != nil {
		return m.SentAt
	}
	return 0
}

func (m *MP_UserReply) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *MP_UserReply) GetAiRoleType() uint32 {
	if m != nil {
		return m.AiRoleType
	}
	return 0
}

// 用户回复群组消息
type MP_GroupUserReply struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GroupTemplateId      uint32   `protobuf:"varint,2,opt,name=group_template_id,json=groupTemplateId,proto3" json:"group_template_id,omitempty"`
	GroupInstanceId      uint32   `protobuf:"varint,3,opt,name=group_instance_id,json=groupInstanceId,proto3" json:"group_instance_id,omitempty"`
	ActualMsgType        MsgType  `protobuf:"varint,4,opt,name=actual_msg_type,json=actualMsgType,proto3,enum=rcmd.rcmd_ai_partner_kafka.MsgType" json:"actual_msg_type,omitempty"`
	MsgContent           string   `protobuf:"bytes,5,opt,name=msg_content,json=msgContent,proto3" json:"msg_content,omitempty"`
	Ext                  []byte   `protobuf:"bytes,6,opt,name=ext,proto3" json:"ext,omitempty"`
	TtMsgType            uint32   `protobuf:"varint,7,opt,name=tt_msg_type,json=ttMsgType,proto3" json:"tt_msg_type,omitempty"`
	SeqId                uint32   `protobuf:"varint,8,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	SentAt               int64    `protobuf:"varint,9,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	TraceId              string   `protobuf:"bytes,10,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	AiGroupType          uint32   `protobuf:"varint,11,opt,name=ai_group_type,json=aiGroupType,proto3" json:"ai_group_type,omitempty"`
	TargetRoleIds        []uint32 `protobuf:"varint,12,rep,packed,name=target_role_ids,json=targetRoleIds,proto3" json:"target_role_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_GroupUserReply) Reset()         { *m = MP_GroupUserReply{} }
func (m *MP_GroupUserReply) String() string { return proto.CompactTextString(m) }
func (*MP_GroupUserReply) ProtoMessage()    {}
func (*MP_GroupUserReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{5}
}
func (m *MP_GroupUserReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_GroupUserReply.Unmarshal(m, b)
}
func (m *MP_GroupUserReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_GroupUserReply.Marshal(b, m, deterministic)
}
func (dst *MP_GroupUserReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_GroupUserReply.Merge(dst, src)
}
func (m *MP_GroupUserReply) XXX_Size() int {
	return xxx_messageInfo_MP_GroupUserReply.Size(m)
}
func (m *MP_GroupUserReply) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_GroupUserReply.DiscardUnknown(m)
}

var xxx_messageInfo_MP_GroupUserReply proto.InternalMessageInfo

func (m *MP_GroupUserReply) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_GroupUserReply) GetGroupTemplateId() uint32 {
	if m != nil {
		return m.GroupTemplateId
	}
	return 0
}

func (m *MP_GroupUserReply) GetGroupInstanceId() uint32 {
	if m != nil {
		return m.GroupInstanceId
	}
	return 0
}

func (m *MP_GroupUserReply) GetActualMsgType() MsgType {
	if m != nil {
		return m.ActualMsgType
	}
	return MsgType_Invalid
}

func (m *MP_GroupUserReply) GetMsgContent() string {
	if m != nil {
		return m.MsgContent
	}
	return ""
}

func (m *MP_GroupUserReply) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *MP_GroupUserReply) GetTtMsgType() uint32 {
	if m != nil {
		return m.TtMsgType
	}
	return 0
}

func (m *MP_GroupUserReply) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *MP_GroupUserReply) GetSentAt() int64 {
	if m != nil {
		return m.SentAt
	}
	return 0
}

func (m *MP_GroupUserReply) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *MP_GroupUserReply) GetAiGroupType() uint32 {
	if m != nil {
		return m.AiGroupType
	}
	return 0
}

func (m *MP_GroupUserReply) GetTargetRoleIds() []uint32 {
	if m != nil {
		return m.TargetRoleIds
	}
	return nil
}

type MP_SilenceNotify struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	Version              string   `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_SilenceNotify) Reset()         { *m = MP_SilenceNotify{} }
func (m *MP_SilenceNotify) String() string { return proto.CompactTextString(m) }
func (*MP_SilenceNotify) ProtoMessage()    {}
func (*MP_SilenceNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{6}
}
func (m *MP_SilenceNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_SilenceNotify.Unmarshal(m, b)
}
func (m *MP_SilenceNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_SilenceNotify.Marshal(b, m, deterministic)
}
func (dst *MP_SilenceNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_SilenceNotify.Merge(dst, src)
}
func (m *MP_SilenceNotify) XXX_Size() int {
	return xxx_messageInfo_MP_SilenceNotify.Size(m)
}
func (m *MP_SilenceNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_SilenceNotify.DiscardUnknown(m)
}

var xxx_messageInfo_MP_SilenceNotify proto.InternalMessageInfo

func (m *MP_SilenceNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_SilenceNotify) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *MP_SilenceNotify) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type MP_ResetElizaCtx struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	ElizaCtxId           uint32   `protobuf:"varint,3,opt,name=eliza_ctx_id,json=elizaCtxId,proto3" json:"eliza_ctx_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_ResetElizaCtx) Reset()         { *m = MP_ResetElizaCtx{} }
func (m *MP_ResetElizaCtx) String() string { return proto.CompactTextString(m) }
func (*MP_ResetElizaCtx) ProtoMessage()    {}
func (*MP_ResetElizaCtx) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{7}
}
func (m *MP_ResetElizaCtx) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_ResetElizaCtx.Unmarshal(m, b)
}
func (m *MP_ResetElizaCtx) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_ResetElizaCtx.Marshal(b, m, deterministic)
}
func (dst *MP_ResetElizaCtx) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_ResetElizaCtx.Merge(dst, src)
}
func (m *MP_ResetElizaCtx) XXX_Size() int {
	return xxx_messageInfo_MP_ResetElizaCtx.Size(m)
}
func (m *MP_ResetElizaCtx) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_ResetElizaCtx.DiscardUnknown(m)
}

var xxx_messageInfo_MP_ResetElizaCtx proto.InternalMessageInfo

func (m *MP_ResetElizaCtx) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_ResetElizaCtx) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *MP_ResetElizaCtx) GetElizaCtxId() uint32 {
	if m != nil {
		return m.ElizaCtxId
	}
	return 0
}

type MP_Tick struct {
	Ts                   int64    `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_Tick) Reset()         { *m = MP_Tick{} }
func (m *MP_Tick) String() string { return proto.CompactTextString(m) }
func (*MP_Tick) ProtoMessage()    {}
func (*MP_Tick) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{8}
}
func (m *MP_Tick) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_Tick.Unmarshal(m, b)
}
func (m *MP_Tick) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_Tick.Marshal(b, m, deterministic)
}
func (dst *MP_Tick) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_Tick.Merge(dst, src)
}
func (m *MP_Tick) XXX_Size() int {
	return xxx_messageInfo_MP_Tick.Size(m)
}
func (m *MP_Tick) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_Tick.DiscardUnknown(m)
}

var xxx_messageInfo_MP_Tick proto.InternalMessageInfo

func (m *MP_Tick) GetTs() int64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *MP_Tick) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type MP_DelayMsgEvent struct {
	Uid                 uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId           uint32     `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	SessionId           uint32     `protobuf:"varint,3,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	ElizaId             uint32     `protobuf:"varint,4,opt,name=eliza_id,json=elizaId,proto3" json:"eliza_id,omitempty"`
	RoleId              uint32     `protobuf:"varint,5,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	RoleStyle           string     `protobuf:"bytes,6,opt,name=role_style,json=roleStyle,proto3" json:"role_style,omitempty"`
	UserMsg             *SendMsg   `protobuf:"bytes,7,opt,name=user_msg,json=userMsg,proto3" json:"user_msg,omitempty"`
	ReceiveTs           int64      `protobuf:"varint,8,opt,name=receive_ts,json=receiveTs,proto3" json:"receive_ts,omitempty"`
	SendMsgId           string     `protobuf:"bytes,9,opt,name=send_msg_id,json=sendMsgId,proto3" json:"send_msg_id,omitempty"`
	SendMsgOrder        uint32     `protobuf:"varint,10,opt,name=send_msg_order,json=sendMsgOrder,proto3" json:"send_msg_order,omitempty"`
	SendMsgs            []*SendMsg `protobuf:"bytes,11,rep,name=send_msgs,json=sendMsgs,proto3" json:"send_msgs,omitempty"`
	ReplyType           uint32     `protobuf:"varint,12,opt,name=reply_type,json=replyType,proto3" json:"reply_type,omitempty"`
	SceneType           uint32     `protobuf:"varint,13,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	ReplyMsgPlanB       uint32     `protobuf:"varint,14,opt,name=reply_msg_plan_b,json=replyMsgPlanB,proto3" json:"reply_msg_plan_b,omitempty"`
	ExceptedSendMsg     string     `protobuf:"bytes,15,opt,name=excepted_send_msg,json=exceptedSendMsg,proto3" json:"excepted_send_msg,omitempty"`
	ModelUsed           string     `protobuf:"bytes,16,opt,name=model_used,json=modelUsed,proto3" json:"model_used,omitempty"`
	AlgoDetectedInfo    string     `protobuf:"bytes,17,opt,name=algo_detected_info,json=algoDetectedInfo,proto3" json:"algo_detected_info,omitempty"`
	Retry               bool       `protobuf:"varint,18,opt,name=retry,proto3" json:"retry,omitempty"`
	SaveReply           bool       `protobuf:"varint,19,opt,name=save_reply,json=saveReply,proto3" json:"save_reply,omitempty"`
	UserFormatMsg       string     `protobuf:"bytes,20,opt,name=user_format_msg,json=userFormatMsg,proto3" json:"user_format_msg,omitempty"`
	MeetDays            uint32     `protobuf:"varint,21,opt,name=meet_days,json=meetDays,proto3" json:"meet_days,omitempty"`
	Placeholder         string     `protobuf:"bytes,22,opt,name=placeholder,proto3" json:"placeholder,omitempty"`
	SessionIdStr        string     `protobuf:"bytes,23,opt,name=session_id_str,json=sessionIdStr,proto3" json:"session_id_str,omitempty"`
	StoryInfo           *StoryInfo `protobuf:"bytes,24,opt,name=story_info,json=storyInfo,proto3" json:"story_info,omitempty"`
	OriginalExceptedMsg string     `protobuf:"bytes,25,opt,name=original_excepted_msg,json=originalExceptedMsg,proto3" json:"original_excepted_msg,omitempty"`
	UserTextLabels      []string   `protobuf:"bytes,26,rep,name=user_text_labels,json=userTextLabels,proto3" json:"user_text_labels,omitempty"`
	NoParseGptText      string     `protobuf:"bytes,27,opt,name=no_parse_gpt_text,json=noParseGptText,proto3" json:"no_parse_gpt_text,omitempty"`
	ChattingId          string     `protobuf:"bytes,28,opt,name=chatting_id,json=chattingId,proto3" json:"chatting_id,omitempty"`
	PromptId            string     `protobuf:"bytes,29,opt,name=prompt_id,json=promptId,proto3" json:"prompt_id,omitempty"`
	PromptVersion       string     `protobuf:"bytes,30,opt,name=prompt_version,json=promptVersion,proto3" json:"prompt_version,omitempty"`
	RetryReport         string     `protobuf:"bytes,31,opt,name=retry_report,json=retryReport,proto3" json:"retry_report,omitempty"`
	ComeFrom            string     `protobuf:"bytes,32,opt,name=come_from,json=comeFrom,proto3" json:"come_from,omitempty"`
	// 从db.user_game_character获取来源，如果为true，就不再db里面拉取。
	// 防止db的数据为空时，在进行子消息发送时，出现重复读DB的无效情况
	DisableGetComeFrom       bool       `protobuf:"varint,33,opt,name=disable_get_come_from,json=disableGetComeFrom,proto3" json:"disable_get_come_from,omitempty"`
	UserTextLabelsModelInfo  string     `protobuf:"bytes,34,opt,name=user_text_labels_model_info,json=userTextLabelsModelInfo,proto3" json:"user_text_labels_model_info,omitempty"`
	CtxInfo                  *CtxInfo   `protobuf:"bytes,35,opt,name=ctx_info,json=ctxInfo,proto3" json:"ctx_info,omitempty"`
	GameType                 string     `protobuf:"bytes,36,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	VoiceChatCtxId           string     `protobuf:"bytes,37,opt,name=voice_chat_ctx_id,json=voiceChatCtxId,proto3" json:"voice_chat_ctx_id,omitempty"`
	Ticks                    []*MP_Tick `protobuf:"bytes,38,rep,name=ticks,proto3" json:"ticks,omitempty"`
	TraceId                  string     `protobuf:"bytes,39,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	AiRoleType               uint32     `protobuf:"varint,40,opt,name=ai_role_type,json=aiRoleType,proto3" json:"ai_role_type,omitempty"`
	IsUseExtraChat           bool       `protobuf:"varint,41,opt,name=is_use_extra_chat,json=isUseExtraChat,proto3" json:"is_use_extra_chat,omitempty"`
	LoginPushContentInterval int64      `protobuf:"varint,42,opt,name=login_push_content_interval,json=loginPushContentInterval,proto3" json:"login_push_content_interval,omitempty"`
	UserMsgCount             uint32     `protobuf:"varint,43,opt,name=user_msg_count,json=userMsgCount,proto3" json:"user_msg_count,omitempty"`
	OriginalReplyWordCount   uint32     `protobuf:"varint,44,opt,name=original_reply_word_count,json=originalReplyWordCount,proto3" json:"original_reply_word_count,omitempty"`
	SegmentCount             uint32     `protobuf:"varint,45,opt,name=segment_count,json=segmentCount,proto3" json:"segment_count,omitempty"`
	TriggerMsgType           uint32     `protobuf:"varint,46,opt,name=trigger_msg_type,json=triggerMsgType,proto3" json:"trigger_msg_type,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}   `json:"-"`
	XXX_unrecognized         []byte     `json:"-"`
	XXX_sizecache            int32      `json:"-"`
}

func (m *MP_DelayMsgEvent) Reset()         { *m = MP_DelayMsgEvent{} }
func (m *MP_DelayMsgEvent) String() string { return proto.CompactTextString(m) }
func (*MP_DelayMsgEvent) ProtoMessage()    {}
func (*MP_DelayMsgEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{9}
}
func (m *MP_DelayMsgEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_DelayMsgEvent.Unmarshal(m, b)
}
func (m *MP_DelayMsgEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_DelayMsgEvent.Marshal(b, m, deterministic)
}
func (dst *MP_DelayMsgEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_DelayMsgEvent.Merge(dst, src)
}
func (m *MP_DelayMsgEvent) XXX_Size() int {
	return xxx_messageInfo_MP_DelayMsgEvent.Size(m)
}
func (m *MP_DelayMsgEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_DelayMsgEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_DelayMsgEvent proto.InternalMessageInfo

func (m *MP_DelayMsgEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_DelayMsgEvent) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *MP_DelayMsgEvent) GetSessionId() uint32 {
	if m != nil {
		return m.SessionId
	}
	return 0
}

func (m *MP_DelayMsgEvent) GetElizaId() uint32 {
	if m != nil {
		return m.ElizaId
	}
	return 0
}

func (m *MP_DelayMsgEvent) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *MP_DelayMsgEvent) GetRoleStyle() string {
	if m != nil {
		return m.RoleStyle
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetUserMsg() *SendMsg {
	if m != nil {
		return m.UserMsg
	}
	return nil
}

func (m *MP_DelayMsgEvent) GetReceiveTs() int64 {
	if m != nil {
		return m.ReceiveTs
	}
	return 0
}

func (m *MP_DelayMsgEvent) GetSendMsgId() string {
	if m != nil {
		return m.SendMsgId
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetSendMsgOrder() uint32 {
	if m != nil {
		return m.SendMsgOrder
	}
	return 0
}

func (m *MP_DelayMsgEvent) GetSendMsgs() []*SendMsg {
	if m != nil {
		return m.SendMsgs
	}
	return nil
}

func (m *MP_DelayMsgEvent) GetReplyType() uint32 {
	if m != nil {
		return m.ReplyType
	}
	return 0
}

func (m *MP_DelayMsgEvent) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

func (m *MP_DelayMsgEvent) GetReplyMsgPlanB() uint32 {
	if m != nil {
		return m.ReplyMsgPlanB
	}
	return 0
}

func (m *MP_DelayMsgEvent) GetExceptedSendMsg() string {
	if m != nil {
		return m.ExceptedSendMsg
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetModelUsed() string {
	if m != nil {
		return m.ModelUsed
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetAlgoDetectedInfo() string {
	if m != nil {
		return m.AlgoDetectedInfo
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetRetry() bool {
	if m != nil {
		return m.Retry
	}
	return false
}

func (m *MP_DelayMsgEvent) GetSaveReply() bool {
	if m != nil {
		return m.SaveReply
	}
	return false
}

func (m *MP_DelayMsgEvent) GetUserFormatMsg() string {
	if m != nil {
		return m.UserFormatMsg
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetMeetDays() uint32 {
	if m != nil {
		return m.MeetDays
	}
	return 0
}

func (m *MP_DelayMsgEvent) GetPlaceholder() string {
	if m != nil {
		return m.Placeholder
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetSessionIdStr() string {
	if m != nil {
		return m.SessionIdStr
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetStoryInfo() *StoryInfo {
	if m != nil {
		return m.StoryInfo
	}
	return nil
}

func (m *MP_DelayMsgEvent) GetOriginalExceptedMsg() string {
	if m != nil {
		return m.OriginalExceptedMsg
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetUserTextLabels() []string {
	if m != nil {
		return m.UserTextLabels
	}
	return nil
}

func (m *MP_DelayMsgEvent) GetNoParseGptText() string {
	if m != nil {
		return m.NoParseGptText
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetChattingId() string {
	if m != nil {
		return m.ChattingId
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetPromptId() string {
	if m != nil {
		return m.PromptId
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetPromptVersion() string {
	if m != nil {
		return m.PromptVersion
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetRetryReport() string {
	if m != nil {
		return m.RetryReport
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetComeFrom() string {
	if m != nil {
		return m.ComeFrom
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetDisableGetComeFrom() bool {
	if m != nil {
		return m.DisableGetComeFrom
	}
	return false
}

func (m *MP_DelayMsgEvent) GetUserTextLabelsModelInfo() string {
	if m != nil {
		return m.UserTextLabelsModelInfo
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetCtxInfo() *CtxInfo {
	if m != nil {
		return m.CtxInfo
	}
	return nil
}

func (m *MP_DelayMsgEvent) GetGameType() string {
	if m != nil {
		return m.GameType
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetVoiceChatCtxId() string {
	if m != nil {
		return m.VoiceChatCtxId
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetTicks() []*MP_Tick {
	if m != nil {
		return m.Ticks
	}
	return nil
}

func (m *MP_DelayMsgEvent) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *MP_DelayMsgEvent) GetAiRoleType() uint32 {
	if m != nil {
		return m.AiRoleType
	}
	return 0
}

func (m *MP_DelayMsgEvent) GetIsUseExtraChat() bool {
	if m != nil {
		return m.IsUseExtraChat
	}
	return false
}

func (m *MP_DelayMsgEvent) GetLoginPushContentInterval() int64 {
	if m != nil {
		return m.LoginPushContentInterval
	}
	return 0
}

func (m *MP_DelayMsgEvent) GetUserMsgCount() uint32 {
	if m != nil {
		return m.UserMsgCount
	}
	return 0
}

func (m *MP_DelayMsgEvent) GetOriginalReplyWordCount() uint32 {
	if m != nil {
		return m.OriginalReplyWordCount
	}
	return 0
}

func (m *MP_DelayMsgEvent) GetSegmentCount() uint32 {
	if m != nil {
		return m.SegmentCount
	}
	return 0
}

func (m *MP_DelayMsgEvent) GetTriggerMsgType() uint32 {
	if m != nil {
		return m.TriggerMsgType
	}
	return 0
}

type StoryInfo struct {
	StoryId              uint32   `protobuf:"varint,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	SceneId              string   `protobuf:"bytes,2,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	DialogId             string   `protobuf:"bytes,3,opt,name=dialog_id,json=dialogId,proto3" json:"dialog_id,omitempty"`
	ContentId            string   `protobuf:"bytes,4,opt,name=content_id,json=contentId,proto3" json:"content_id,omitempty"`
	OldSceneId           string   `protobuf:"bytes,5,opt,name=old_scene_id,json=oldSceneId,proto3" json:"old_scene_id,omitempty"`
	OldDialogId          string   `protobuf:"bytes,6,opt,name=old_dialog_id,json=oldDialogId,proto3" json:"old_dialog_id,omitempty"`
	OldContentId         string   `protobuf:"bytes,7,opt,name=old_content_id,json=oldContentId,proto3" json:"old_content_id,omitempty"`
	StoryIdStr           string   `protobuf:"bytes,8,opt,name=story_id_str,json=storyIdStr,proto3" json:"story_id_str,omitempty"`
	ChapterId            string   `protobuf:"bytes,9,opt,name=chapter_id,json=chapterId,proto3" json:"chapter_id,omitempty"`
	NpcVoiceId           string   `protobuf:"bytes,10,opt,name=npc_voice_id,json=npcVoiceId,proto3" json:"npc_voice_id,omitempty"`
	NarratorVoiceId      string   `protobuf:"bytes,11,opt,name=narrator_voice_id,json=narratorVoiceId,proto3" json:"narrator_voice_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoryInfo) Reset()         { *m = StoryInfo{} }
func (m *StoryInfo) String() string { return proto.CompactTextString(m) }
func (*StoryInfo) ProtoMessage()    {}
func (*StoryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{10}
}
func (m *StoryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoryInfo.Unmarshal(m, b)
}
func (m *StoryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoryInfo.Marshal(b, m, deterministic)
}
func (dst *StoryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoryInfo.Merge(dst, src)
}
func (m *StoryInfo) XXX_Size() int {
	return xxx_messageInfo_StoryInfo.Size(m)
}
func (m *StoryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StoryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StoryInfo proto.InternalMessageInfo

func (m *StoryInfo) GetStoryId() uint32 {
	if m != nil {
		return m.StoryId
	}
	return 0
}

func (m *StoryInfo) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *StoryInfo) GetDialogId() string {
	if m != nil {
		return m.DialogId
	}
	return ""
}

func (m *StoryInfo) GetContentId() string {
	if m != nil {
		return m.ContentId
	}
	return ""
}

func (m *StoryInfo) GetOldSceneId() string {
	if m != nil {
		return m.OldSceneId
	}
	return ""
}

func (m *StoryInfo) GetOldDialogId() string {
	if m != nil {
		return m.OldDialogId
	}
	return ""
}

func (m *StoryInfo) GetOldContentId() string {
	if m != nil {
		return m.OldContentId
	}
	return ""
}

func (m *StoryInfo) GetStoryIdStr() string {
	if m != nil {
		return m.StoryIdStr
	}
	return ""
}

func (m *StoryInfo) GetChapterId() string {
	if m != nil {
		return m.ChapterId
	}
	return ""
}

func (m *StoryInfo) GetNpcVoiceId() string {
	if m != nil {
		return m.NpcVoiceId
	}
	return ""
}

func (m *StoryInfo) GetNarratorVoiceId() string {
	if m != nil {
		return m.NarratorVoiceId
	}
	return ""
}

type SendMsg struct {
	Content            string  `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	ContentType        uint32  `protobuf:"varint,2,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	MsgType            MsgType `protobuf:"varint,3,opt,name=msg_type,json=msgType,proto3,enum=rcmd.rcmd_ai_partner_kafka.MsgType" json:"msg_type,omitempty"`
	Ext                []byte  `protobuf:"bytes,4,opt,name=ext,proto3" json:"ext,omitempty"`
	OfflinePushContent string  `protobuf:"bytes,5,opt,name=offline_push_content,json=offlinePushContent,proto3" json:"offline_push_content,omitempty"`
	// 消息拆分后，每个单独句子进行算法检测，需要上报对应数据,这些信息在发送下一句时回填到MP_DelayMsgEvent
	IsSplit              bool     `protobuf:"varint,6,opt,name=is_split,json=isSplit,proto3" json:"is_split,omitempty"`
	ReplyType            uint32   `protobuf:"varint,7,opt,name=reply_type,json=replyType,proto3" json:"reply_type,omitempty"`
	ReplyMsgPlanB        uint32   `protobuf:"varint,8,opt,name=reply_msg_plan_b,json=replyMsgPlanB,proto3" json:"reply_msg_plan_b,omitempty"`
	ExceptedSendMsg      string   `protobuf:"bytes,9,opt,name=excepted_send_msg,json=exceptedSendMsg,proto3" json:"excepted_send_msg,omitempty"`
	AlgoDetectedInfo     string   `protobuf:"bytes,10,opt,name=algo_detected_info,json=algoDetectedInfo,proto3" json:"algo_detected_info,omitempty"`
	SaveAsHistory        bool     `protobuf:"varint,11,opt,name=save_as_history,json=saveAsHistory,proto3" json:"save_as_history,omitempty"`
	Tts                  bool     `protobuf:"varint,12,opt,name=tts,proto3" json:"tts,omitempty"`
	SpkId                int32    `protobuf:"varint,13,opt,name=spk_id,json=spkId,proto3" json:"spk_id,omitempty"`
	TtsRoleId            uint32   `protobuf:"varint,14,opt,name=tts_role_id,json=ttsRoleId,proto3" json:"tts_role_id,omitempty"`
	TtsModel             string   `protobuf:"bytes,15,opt,name=tts_model,json=ttsModel,proto3" json:"tts_model,omitempty"`
	TtsVoiceId           string   `protobuf:"bytes,16,opt,name=tts_voice_id,json=ttsVoiceId,proto3" json:"tts_voice_id,omitempty"`
	DelaySeconds         int64    `protobuf:"varint,17,opt,name=delay_seconds,json=delaySeconds,proto3" json:"delay_seconds,omitempty"`
	CustomVoiceJson      string   `protobuf:"bytes,18,opt,name=custom_voice_json,json=customVoiceJson,proto3" json:"custom_voice_json,omitempty"`
	IsNoAudit            bool     `protobuf:"varint,19,opt,name=is_no_audit,json=isNoAudit,proto3" json:"is_no_audit,omitempty"`
	AuditText            string   `protobuf:"bytes,20,opt,name=audit_text,json=auditText,proto3" json:"audit_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendMsg) Reset()         { *m = SendMsg{} }
func (m *SendMsg) String() string { return proto.CompactTextString(m) }
func (*SendMsg) ProtoMessage()    {}
func (*SendMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{11}
}
func (m *SendMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendMsg.Unmarshal(m, b)
}
func (m *SendMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendMsg.Marshal(b, m, deterministic)
}
func (dst *SendMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendMsg.Merge(dst, src)
}
func (m *SendMsg) XXX_Size() int {
	return xxx_messageInfo_SendMsg.Size(m)
}
func (m *SendMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_SendMsg.DiscardUnknown(m)
}

var xxx_messageInfo_SendMsg proto.InternalMessageInfo

func (m *SendMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *SendMsg) GetContentType() uint32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

func (m *SendMsg) GetMsgType() MsgType {
	if m != nil {
		return m.MsgType
	}
	return MsgType_Invalid
}

func (m *SendMsg) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *SendMsg) GetOfflinePushContent() string {
	if m != nil {
		return m.OfflinePushContent
	}
	return ""
}

func (m *SendMsg) GetIsSplit() bool {
	if m != nil {
		return m.IsSplit
	}
	return false
}

func (m *SendMsg) GetReplyType() uint32 {
	if m != nil {
		return m.ReplyType
	}
	return 0
}

func (m *SendMsg) GetReplyMsgPlanB() uint32 {
	if m != nil {
		return m.ReplyMsgPlanB
	}
	return 0
}

func (m *SendMsg) GetExceptedSendMsg() string {
	if m != nil {
		return m.ExceptedSendMsg
	}
	return ""
}

func (m *SendMsg) GetAlgoDetectedInfo() string {
	if m != nil {
		return m.AlgoDetectedInfo
	}
	return ""
}

func (m *SendMsg) GetSaveAsHistory() bool {
	if m != nil {
		return m.SaveAsHistory
	}
	return false
}

func (m *SendMsg) GetTts() bool {
	if m != nil {
		return m.Tts
	}
	return false
}

func (m *SendMsg) GetSpkId() int32 {
	if m != nil {
		return m.SpkId
	}
	return 0
}

func (m *SendMsg) GetTtsRoleId() uint32 {
	if m != nil {
		return m.TtsRoleId
	}
	return 0
}

func (m *SendMsg) GetTtsModel() string {
	if m != nil {
		return m.TtsModel
	}
	return ""
}

func (m *SendMsg) GetTtsVoiceId() string {
	if m != nil {
		return m.TtsVoiceId
	}
	return ""
}

func (m *SendMsg) GetDelaySeconds() int64 {
	if m != nil {
		return m.DelaySeconds
	}
	return 0
}

func (m *SendMsg) GetCustomVoiceJson() string {
	if m != nil {
		return m.CustomVoiceJson
	}
	return ""
}

func (m *SendMsg) GetIsNoAudit() bool {
	if m != nil {
		return m.IsNoAudit
	}
	return false
}

func (m *SendMsg) GetAuditText() string {
	if m != nil {
		return m.AuditText
	}
	return ""
}

type Ext_StoryBookEntrance struct {
	StoryBookId          string   `protobuf:"bytes,1,opt,name=story_book_id,json=storyBookId,proto3" json:"story_book_id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Subtitle             string   `protobuf:"bytes,3,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	CoverImage           string   `protobuf:"bytes,4,opt,name=cover_image,json=coverImage,proto3" json:"cover_image,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Ext_StoryBookEntrance) Reset()         { *m = Ext_StoryBookEntrance{} }
func (m *Ext_StoryBookEntrance) String() string { return proto.CompactTextString(m) }
func (*Ext_StoryBookEntrance) ProtoMessage()    {}
func (*Ext_StoryBookEntrance) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{12}
}
func (m *Ext_StoryBookEntrance) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_StoryBookEntrance.Unmarshal(m, b)
}
func (m *Ext_StoryBookEntrance) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_StoryBookEntrance.Marshal(b, m, deterministic)
}
func (dst *Ext_StoryBookEntrance) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_StoryBookEntrance.Merge(dst, src)
}
func (m *Ext_StoryBookEntrance) XXX_Size() int {
	return xxx_messageInfo_Ext_StoryBookEntrance.Size(m)
}
func (m *Ext_StoryBookEntrance) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_StoryBookEntrance.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_StoryBookEntrance proto.InternalMessageInfo

func (m *Ext_StoryBookEntrance) GetStoryBookId() string {
	if m != nil {
		return m.StoryBookId
	}
	return ""
}

func (m *Ext_StoryBookEntrance) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Ext_StoryBookEntrance) GetSubtitle() string {
	if m != nil {
		return m.Subtitle
	}
	return ""
}

func (m *Ext_StoryBookEntrance) GetCoverImage() string {
	if m != nil {
		return m.CoverImage
	}
	return ""
}

type CtxInfo struct {
	CtxId                string   `protobuf:"bytes,1,opt,name=ctx_id,json=ctxId,proto3" json:"ctx_id,omitempty"`
	GameId               string   `protobuf:"bytes,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	CtxScene             uint32   `protobuf:"varint,3,opt,name=ctx_scene,json=ctxScene,proto3" json:"ctx_scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CtxInfo) Reset()         { *m = CtxInfo{} }
func (m *CtxInfo) String() string { return proto.CompactTextString(m) }
func (*CtxInfo) ProtoMessage()    {}
func (*CtxInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{13}
}
func (m *CtxInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CtxInfo.Unmarshal(m, b)
}
func (m *CtxInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CtxInfo.Marshal(b, m, deterministic)
}
func (dst *CtxInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CtxInfo.Merge(dst, src)
}
func (m *CtxInfo) XXX_Size() int {
	return xxx_messageInfo_CtxInfo.Size(m)
}
func (m *CtxInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CtxInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CtxInfo proto.InternalMessageInfo

func (m *CtxInfo) GetCtxId() string {
	if m != nil {
		return m.CtxId
	}
	return ""
}

func (m *CtxInfo) GetGameId() string {
	if m != nil {
		return m.GameId
	}
	return ""
}

func (m *CtxInfo) GetCtxScene() uint32 {
	if m != nil {
		return m.CtxScene
	}
	return 0
}

type UniformMsg struct {
	Type                 MsgType  `protobuf:"varint,1,opt,name=type,proto3,enum=rcmd.rcmd_ai_partner_kafka.MsgType" json:"type,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Ext                  []byte   `protobuf:"bytes,3,opt,name=ext,proto3" json:"ext,omitempty"`
	CtxInfo              *CtxInfo `protobuf:"bytes,4,opt,name=ctx_info,json=ctxInfo,proto3" json:"ctx_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UniformMsg) Reset()         { *m = UniformMsg{} }
func (m *UniformMsg) String() string { return proto.CompactTextString(m) }
func (*UniformMsg) ProtoMessage()    {}
func (*UniformMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{14}
}
func (m *UniformMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UniformMsg.Unmarshal(m, b)
}
func (m *UniformMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UniformMsg.Marshal(b, m, deterministic)
}
func (dst *UniformMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UniformMsg.Merge(dst, src)
}
func (m *UniformMsg) XXX_Size() int {
	return xxx_messageInfo_UniformMsg.Size(m)
}
func (m *UniformMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_UniformMsg.DiscardUnknown(m)
}

var xxx_messageInfo_UniformMsg proto.InternalMessageInfo

func (m *UniformMsg) GetType() MsgType {
	if m != nil {
		return m.Type
	}
	return MsgType_Invalid
}

func (m *UniformMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *UniformMsg) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *UniformMsg) GetCtxInfo() *CtxInfo {
	if m != nil {
		return m.CtxInfo
	}
	return nil
}

type Ext_AnimateMeme struct {
	IsAutoShow           bool     `protobuf:"varint,1,opt,name=is_auto_show,json=isAutoShow,proto3" json:"is_auto_show,omitempty"`
	IsFirstUnlock        bool     `protobuf:"varint,2,opt,name=is_first_unlock,json=isFirstUnlock,proto3" json:"is_first_unlock,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Ext_AnimateMeme) Reset()         { *m = Ext_AnimateMeme{} }
func (m *Ext_AnimateMeme) String() string { return proto.CompactTextString(m) }
func (*Ext_AnimateMeme) ProtoMessage()    {}
func (*Ext_AnimateMeme) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{15}
}
func (m *Ext_AnimateMeme) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_AnimateMeme.Unmarshal(m, b)
}
func (m *Ext_AnimateMeme) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_AnimateMeme.Marshal(b, m, deterministic)
}
func (dst *Ext_AnimateMeme) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_AnimateMeme.Merge(dst, src)
}
func (m *Ext_AnimateMeme) XXX_Size() int {
	return xxx_messageInfo_Ext_AnimateMeme.Size(m)
}
func (m *Ext_AnimateMeme) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_AnimateMeme.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_AnimateMeme proto.InternalMessageInfo

func (m *Ext_AnimateMeme) GetIsAutoShow() bool {
	if m != nil {
		return m.IsAutoShow
	}
	return false
}

func (m *Ext_AnimateMeme) GetIsFirstUnlock() bool {
	if m != nil {
		return m.IsFirstUnlock
	}
	return false
}

type RichTextFormat struct {
	Text                 string   `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	Style                string   `protobuf:"bytes,2,opt,name=style,proto3" json:"style,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RichTextFormat) Reset()         { *m = RichTextFormat{} }
func (m *RichTextFormat) String() string { return proto.CompactTextString(m) }
func (*RichTextFormat) ProtoMessage()    {}
func (*RichTextFormat) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{16}
}
func (m *RichTextFormat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RichTextFormat.Unmarshal(m, b)
}
func (m *RichTextFormat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RichTextFormat.Marshal(b, m, deterministic)
}
func (dst *RichTextFormat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RichTextFormat.Merge(dst, src)
}
func (m *RichTextFormat) XXX_Size() int {
	return xxx_messageInfo_RichTextFormat.Size(m)
}
func (m *RichTextFormat) XXX_DiscardUnknown() {
	xxx_messageInfo_RichTextFormat.DiscardUnknown(m)
}

var xxx_messageInfo_RichTextFormat proto.InternalMessageInfo

func (m *RichTextFormat) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *RichTextFormat) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

type Ext_RichText struct {
	Texts                []*RichTextFormat `protobuf:"bytes,1,rep,name=texts,proto3" json:"texts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *Ext_RichText) Reset()         { *m = Ext_RichText{} }
func (m *Ext_RichText) String() string { return proto.CompactTextString(m) }
func (*Ext_RichText) ProtoMessage()    {}
func (*Ext_RichText) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{17}
}
func (m *Ext_RichText) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_RichText.Unmarshal(m, b)
}
func (m *Ext_RichText) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_RichText.Marshal(b, m, deterministic)
}
func (dst *Ext_RichText) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_RichText.Merge(dst, src)
}
func (m *Ext_RichText) XXX_Size() int {
	return xxx_messageInfo_Ext_RichText.Size(m)
}
func (m *Ext_RichText) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_RichText.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_RichText proto.InternalMessageInfo

func (m *Ext_RichText) GetTexts() []*RichTextFormat {
	if m != nil {
		return m.Texts
	}
	return nil
}

type Ext_RelationshipLetter struct {
	To                   string   `protobuf:"bytes,1,opt,name=to,proto3" json:"to,omitempty"`
	From                 string   `protobuf:"bytes,2,opt,name=from,proto3" json:"from,omitempty"`
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Ext_RelationshipLetter) Reset()         { *m = Ext_RelationshipLetter{} }
func (m *Ext_RelationshipLetter) String() string { return proto.CompactTextString(m) }
func (*Ext_RelationshipLetter) ProtoMessage()    {}
func (*Ext_RelationshipLetter) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{18}
}
func (m *Ext_RelationshipLetter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_RelationshipLetter.Unmarshal(m, b)
}
func (m *Ext_RelationshipLetter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_RelationshipLetter.Marshal(b, m, deterministic)
}
func (dst *Ext_RelationshipLetter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_RelationshipLetter.Merge(dst, src)
}
func (m *Ext_RelationshipLetter) XXX_Size() int {
	return xxx_messageInfo_Ext_RelationshipLetter.Size(m)
}
func (m *Ext_RelationshipLetter) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_RelationshipLetter.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_RelationshipLetter proto.InternalMessageInfo

func (m *Ext_RelationshipLetter) GetTo() string {
	if m != nil {
		return m.To
	}
	return ""
}

func (m *Ext_RelationshipLetter) GetFrom() string {
	if m != nil {
		return m.From
	}
	return ""
}

func (m *Ext_RelationshipLetter) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type Ext_TTSMsg struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Seconds              uint32   `protobuf:"varint,2,opt,name=seconds,proto3" json:"seconds,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Ext_TTSMsg) Reset()         { *m = Ext_TTSMsg{} }
func (m *Ext_TTSMsg) String() string { return proto.CompactTextString(m) }
func (*Ext_TTSMsg) ProtoMessage()    {}
func (*Ext_TTSMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{19}
}
func (m *Ext_TTSMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_TTSMsg.Unmarshal(m, b)
}
func (m *Ext_TTSMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_TTSMsg.Marshal(b, m, deterministic)
}
func (dst *Ext_TTSMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_TTSMsg.Merge(dst, src)
}
func (m *Ext_TTSMsg) XXX_Size() int {
	return xxx_messageInfo_Ext_TTSMsg.Size(m)
}
func (m *Ext_TTSMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_TTSMsg.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_TTSMsg proto.InternalMessageInfo

func (m *Ext_TTSMsg) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *Ext_TTSMsg) GetSeconds() uint32 {
	if m != nil {
		return m.Seconds
	}
	return 0
}

func (m *Ext_TTSMsg) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type Ext_NewPhotograph struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Avatar               string   `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Image                string   `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	Type                 uint32   `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	ImageForMsg          string   `protobuf:"bytes,5,opt,name=image_for_msg,json=imageForMsg,proto3" json:"image_for_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Ext_NewPhotograph) Reset()         { *m = Ext_NewPhotograph{} }
func (m *Ext_NewPhotograph) String() string { return proto.CompactTextString(m) }
func (*Ext_NewPhotograph) ProtoMessage()    {}
func (*Ext_NewPhotograph) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{20}
}
func (m *Ext_NewPhotograph) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_NewPhotograph.Unmarshal(m, b)
}
func (m *Ext_NewPhotograph) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_NewPhotograph.Marshal(b, m, deterministic)
}
func (dst *Ext_NewPhotograph) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_NewPhotograph.Merge(dst, src)
}
func (m *Ext_NewPhotograph) XXX_Size() int {
	return xxx_messageInfo_Ext_NewPhotograph.Size(m)
}
func (m *Ext_NewPhotograph) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_NewPhotograph.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_NewPhotograph proto.InternalMessageInfo

func (m *Ext_NewPhotograph) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Ext_NewPhotograph) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *Ext_NewPhotograph) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *Ext_NewPhotograph) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *Ext_NewPhotograph) GetImageForMsg() string {
	if m != nil {
		return m.ImageForMsg
	}
	return ""
}

type Ext_UserAutoReply struct {
	Texts                 []*RichTextFormat `protobuf:"bytes,1,rep,name=texts,proto3" json:"texts,omitempty"`
	TriggerMsgType        uint32            `protobuf:"varint,2,opt,name=trigger_msg_type,json=triggerMsgType,proto3" json:"trigger_msg_type,omitempty"`
	TriggerUniformMsgType MsgType           `protobuf:"varint,3,opt,name=trigger_uniform_msg_type,json=triggerUniformMsgType,proto3,enum=rcmd.rcmd_ai_partner_kafka.MsgType" json:"trigger_uniform_msg_type,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}          `json:"-"`
	XXX_unrecognized      []byte            `json:"-"`
	XXX_sizecache         int32             `json:"-"`
}

func (m *Ext_UserAutoReply) Reset()         { *m = Ext_UserAutoReply{} }
func (m *Ext_UserAutoReply) String() string { return proto.CompactTextString(m) }
func (*Ext_UserAutoReply) ProtoMessage()    {}
func (*Ext_UserAutoReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{21}
}
func (m *Ext_UserAutoReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_UserAutoReply.Unmarshal(m, b)
}
func (m *Ext_UserAutoReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_UserAutoReply.Marshal(b, m, deterministic)
}
func (dst *Ext_UserAutoReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_UserAutoReply.Merge(dst, src)
}
func (m *Ext_UserAutoReply) XXX_Size() int {
	return xxx_messageInfo_Ext_UserAutoReply.Size(m)
}
func (m *Ext_UserAutoReply) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_UserAutoReply.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_UserAutoReply proto.InternalMessageInfo

func (m *Ext_UserAutoReply) GetTexts() []*RichTextFormat {
	if m != nil {
		return m.Texts
	}
	return nil
}

func (m *Ext_UserAutoReply) GetTriggerMsgType() uint32 {
	if m != nil {
		return m.TriggerMsgType
	}
	return 0
}

func (m *Ext_UserAutoReply) GetTriggerUniformMsgType() MsgType {
	if m != nil {
		return m.TriggerUniformMsgType
	}
	return MsgType_Invalid
}

type Ext_VoiceChatReply struct {
	// "voice_chat_ctx_id":"",//string，语音聊天的上下文ID,调用StartVoiceChat接口获得
	// "input_from":0,//int32,0:语音识别,1:打字输入
	VoiceChatCtxId       string   `protobuf:"bytes,1,opt,name=voice_chat_ctx_id,json=voiceChatCtxId,proto3" json:"voice_chat_ctx_id,omitempty"`
	InputFrom            int32    `protobuf:"varint,2,opt,name=input_from,json=inputFrom,proto3" json:"input_from,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Ext_VoiceChatReply) Reset()         { *m = Ext_VoiceChatReply{} }
func (m *Ext_VoiceChatReply) String() string { return proto.CompactTextString(m) }
func (*Ext_VoiceChatReply) ProtoMessage()    {}
func (*Ext_VoiceChatReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{22}
}
func (m *Ext_VoiceChatReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_VoiceChatReply.Unmarshal(m, b)
}
func (m *Ext_VoiceChatReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_VoiceChatReply.Marshal(b, m, deterministic)
}
func (dst *Ext_VoiceChatReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_VoiceChatReply.Merge(dst, src)
}
func (m *Ext_VoiceChatReply) XXX_Size() int {
	return xxx_messageInfo_Ext_VoiceChatReply.Size(m)
}
func (m *Ext_VoiceChatReply) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_VoiceChatReply.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_VoiceChatReply proto.InternalMessageInfo

func (m *Ext_VoiceChatReply) GetVoiceChatCtxId() string {
	if m != nil {
		return m.VoiceChatCtxId
	}
	return ""
}

func (m *Ext_VoiceChatReply) GetInputFrom() int32 {
	if m != nil {
		return m.InputFrom
	}
	return 0
}

type IMEvent struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32           `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	Sender               IMEvent_RoleType `protobuf:"varint,3,opt,name=sender,proto3,enum=rcmd.rcmd_ai_partner_kafka.IMEvent_RoleType" json:"sender,omitempty"`
	SendTs               int64            `protobuf:"varint,4,opt,name=send_ts,json=sendTs,proto3" json:"send_ts,omitempty"`
	MsgContent           string           `protobuf:"bytes,5,opt,name=msg_content,json=msgContent,proto3" json:"msg_content,omitempty"`
	MsgType              uint32           `protobuf:"varint,6,opt,name=msg_type,json=msgType,proto3" json:"msg_type,omitempty"`
	Ext                  []byte           `protobuf:"bytes,7,opt,name=ext,proto3" json:"ext,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *IMEvent) Reset()         { *m = IMEvent{} }
func (m *IMEvent) String() string { return proto.CompactTextString(m) }
func (*IMEvent) ProtoMessage()    {}
func (*IMEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{23}
}
func (m *IMEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IMEvent.Unmarshal(m, b)
}
func (m *IMEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IMEvent.Marshal(b, m, deterministic)
}
func (dst *IMEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IMEvent.Merge(dst, src)
}
func (m *IMEvent) XXX_Size() int {
	return xxx_messageInfo_IMEvent.Size(m)
}
func (m *IMEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_IMEvent.DiscardUnknown(m)
}

var xxx_messageInfo_IMEvent proto.InternalMessageInfo

func (m *IMEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IMEvent) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *IMEvent) GetSender() IMEvent_RoleType {
	if m != nil {
		return m.Sender
	}
	return IMEvent_RoleType_User
}

func (m *IMEvent) GetSendTs() int64 {
	if m != nil {
		return m.SendTs
	}
	return 0
}

func (m *IMEvent) GetMsgContent() string {
	if m != nil {
		return m.MsgContent
	}
	return ""
}

func (m *IMEvent) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

func (m *IMEvent) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

type NoReplyEvent struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32            `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	LastUserMsgCount     uint32            `protobuf:"varint,3,opt,name=last_user_msg_count,json=lastUserMsgCount,proto3" json:"last_user_msg_count,omitempty"`
	Type                 NoReplyEvent_Type `protobuf:"varint,4,opt,name=type,proto3,enum=rcmd.rcmd_ai_partner_kafka.NoReplyEvent_Type" json:"type,omitempty"`
	Version              string            `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *NoReplyEvent) Reset()         { *m = NoReplyEvent{} }
func (m *NoReplyEvent) String() string { return proto.CompactTextString(m) }
func (*NoReplyEvent) ProtoMessage()    {}
func (*NoReplyEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{24}
}
func (m *NoReplyEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NoReplyEvent.Unmarshal(m, b)
}
func (m *NoReplyEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NoReplyEvent.Marshal(b, m, deterministic)
}
func (dst *NoReplyEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NoReplyEvent.Merge(dst, src)
}
func (m *NoReplyEvent) XXX_Size() int {
	return xxx_messageInfo_NoReplyEvent.Size(m)
}
func (m *NoReplyEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_NoReplyEvent.DiscardUnknown(m)
}

var xxx_messageInfo_NoReplyEvent proto.InternalMessageInfo

func (m *NoReplyEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NoReplyEvent) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *NoReplyEvent) GetLastUserMsgCount() uint32 {
	if m != nil {
		return m.LastUserMsgCount
	}
	return 0
}

func (m *NoReplyEvent) GetType() NoReplyEvent_Type {
	if m != nil {
		return m.Type
	}
	return NoReplyEvent_Type_Invalid
}

func (m *NoReplyEvent) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type Ext_StoryEntrance struct {
	StoryId              uint32            `protobuf:"varint,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	RichText             []*RichTextFormat `protobuf:"bytes,2,rep,name=rich_text,json=richText,proto3" json:"rich_text,omitempty"`
	OldTriggerTs         int64             `protobuf:"varint,3,opt,name=old_trigger_ts,json=oldTriggerTs,proto3" json:"old_trigger_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *Ext_StoryEntrance) Reset()         { *m = Ext_StoryEntrance{} }
func (m *Ext_StoryEntrance) String() string { return proto.CompactTextString(m) }
func (*Ext_StoryEntrance) ProtoMessage()    {}
func (*Ext_StoryEntrance) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{25}
}
func (m *Ext_StoryEntrance) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_StoryEntrance.Unmarshal(m, b)
}
func (m *Ext_StoryEntrance) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_StoryEntrance.Marshal(b, m, deterministic)
}
func (dst *Ext_StoryEntrance) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_StoryEntrance.Merge(dst, src)
}
func (m *Ext_StoryEntrance) XXX_Size() int {
	return xxx_messageInfo_Ext_StoryEntrance.Size(m)
}
func (m *Ext_StoryEntrance) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_StoryEntrance.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_StoryEntrance proto.InternalMessageInfo

func (m *Ext_StoryEntrance) GetStoryId() uint32 {
	if m != nil {
		return m.StoryId
	}
	return 0
}

func (m *Ext_StoryEntrance) GetRichText() []*RichTextFormat {
	if m != nil {
		return m.RichText
	}
	return nil
}

func (m *Ext_StoryEntrance) GetOldTriggerTs() int64 {
	if m != nil {
		return m.OldTriggerTs
	}
	return 0
}

type Ext_StoryCommon struct {
	DialogId     string `protobuf:"bytes,1,opt,name=dialog_id,json=dialogId,proto3" json:"dialog_id,omitempty"`
	StoryId      uint32 `protobuf:"varint,2,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	UseNeedReply bool   `protobuf:"varint,3,opt,name=use_need_reply,json=useNeedReply,proto3" json:"use_need_reply,omitempty"`
	// 故事书玩法新增字段
	ChapterId            string   `protobuf:"bytes,4,opt,name=chapter_id,json=chapterId,proto3" json:"chapter_id,omitempty"`
	StoryIdStr           string   `protobuf:"bytes,5,opt,name=story_id_str,json=storyIdStr,proto3" json:"story_id_str,omitempty"`
	Version              string   `protobuf:"bytes,6,opt,name=version,proto3" json:"version,omitempty"`
	Name                 string   `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
	Avatar               string   `protobuf:"bytes,8,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Url                  string   `protobuf:"bytes,9,opt,name=url,proto3" json:"url,omitempty"`
	Seconds              uint32   `protobuf:"varint,10,opt,name=seconds,proto3" json:"seconds,omitempty"`
	Text                 string   `protobuf:"bytes,11,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Ext_StoryCommon) Reset()         { *m = Ext_StoryCommon{} }
func (m *Ext_StoryCommon) String() string { return proto.CompactTextString(m) }
func (*Ext_StoryCommon) ProtoMessage()    {}
func (*Ext_StoryCommon) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{26}
}
func (m *Ext_StoryCommon) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_StoryCommon.Unmarshal(m, b)
}
func (m *Ext_StoryCommon) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_StoryCommon.Marshal(b, m, deterministic)
}
func (dst *Ext_StoryCommon) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_StoryCommon.Merge(dst, src)
}
func (m *Ext_StoryCommon) XXX_Size() int {
	return xxx_messageInfo_Ext_StoryCommon.Size(m)
}
func (m *Ext_StoryCommon) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_StoryCommon.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_StoryCommon proto.InternalMessageInfo

func (m *Ext_StoryCommon) GetDialogId() string {
	if m != nil {
		return m.DialogId
	}
	return ""
}

func (m *Ext_StoryCommon) GetStoryId() uint32 {
	if m != nil {
		return m.StoryId
	}
	return 0
}

func (m *Ext_StoryCommon) GetUseNeedReply() bool {
	if m != nil {
		return m.UseNeedReply
	}
	return false
}

func (m *Ext_StoryCommon) GetChapterId() string {
	if m != nil {
		return m.ChapterId
	}
	return ""
}

func (m *Ext_StoryCommon) GetStoryIdStr() string {
	if m != nil {
		return m.StoryIdStr
	}
	return ""
}

func (m *Ext_StoryCommon) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *Ext_StoryCommon) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Ext_StoryCommon) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *Ext_StoryCommon) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *Ext_StoryCommon) GetSeconds() uint32 {
	if m != nil {
		return m.Seconds
	}
	return 0
}

func (m *Ext_StoryCommon) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type Ext_StoryNarration struct {
	Texts        []*RichTextFormat `protobuf:"bytes,1,rep,name=texts,proto3" json:"texts,omitempty"`
	DialogId     string            `protobuf:"bytes,2,opt,name=dialog_id,json=dialogId,proto3" json:"dialog_id,omitempty"`
	StoryId      uint32            `protobuf:"varint,3,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	UseNeedReply bool              `protobuf:"varint,4,opt,name=use_need_reply,json=useNeedReply,proto3" json:"use_need_reply,omitempty"`
	// 故事书玩法新增字段
	ChapterId            string   `protobuf:"bytes,5,opt,name=chapter_id,json=chapterId,proto3" json:"chapter_id,omitempty"`
	StoryIdStr           string   `protobuf:"bytes,6,opt,name=story_id_str,json=storyIdStr,proto3" json:"story_id_str,omitempty"`
	Version              string   `protobuf:"bytes,7,opt,name=version,proto3" json:"version,omitempty"`
	Name                 string   `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`
	Avatar               string   `protobuf:"bytes,9,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Url                  string   `protobuf:"bytes,10,opt,name=url,proto3" json:"url,omitempty"`
	Seconds              uint32   `protobuf:"varint,11,opt,name=seconds,proto3" json:"seconds,omitempty"`
	Text                 string   `protobuf:"bytes,12,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Ext_StoryNarration) Reset()         { *m = Ext_StoryNarration{} }
func (m *Ext_StoryNarration) String() string { return proto.CompactTextString(m) }
func (*Ext_StoryNarration) ProtoMessage()    {}
func (*Ext_StoryNarration) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{27}
}
func (m *Ext_StoryNarration) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_StoryNarration.Unmarshal(m, b)
}
func (m *Ext_StoryNarration) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_StoryNarration.Marshal(b, m, deterministic)
}
func (dst *Ext_StoryNarration) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_StoryNarration.Merge(dst, src)
}
func (m *Ext_StoryNarration) XXX_Size() int {
	return xxx_messageInfo_Ext_StoryNarration.Size(m)
}
func (m *Ext_StoryNarration) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_StoryNarration.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_StoryNarration proto.InternalMessageInfo

func (m *Ext_StoryNarration) GetTexts() []*RichTextFormat {
	if m != nil {
		return m.Texts
	}
	return nil
}

func (m *Ext_StoryNarration) GetDialogId() string {
	if m != nil {
		return m.DialogId
	}
	return ""
}

func (m *Ext_StoryNarration) GetStoryId() uint32 {
	if m != nil {
		return m.StoryId
	}
	return 0
}

func (m *Ext_StoryNarration) GetUseNeedReply() bool {
	if m != nil {
		return m.UseNeedReply
	}
	return false
}

func (m *Ext_StoryNarration) GetChapterId() string {
	if m != nil {
		return m.ChapterId
	}
	return ""
}

func (m *Ext_StoryNarration) GetStoryIdStr() string {
	if m != nil {
		return m.StoryIdStr
	}
	return ""
}

func (m *Ext_StoryNarration) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *Ext_StoryNarration) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Ext_StoryNarration) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *Ext_StoryNarration) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *Ext_StoryNarration) GetSeconds() uint32 {
	if m != nil {
		return m.Seconds
	}
	return 0
}

func (m *Ext_StoryNarration) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type Ext_StoryFinish struct {
	StoryId         uint32 `protobuf:"varint,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	Text            string `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	StoryClassifyId uint32 `protobuf:"varint,3,opt,name=story_classify_id,json=storyClassifyId,proto3" json:"story_classify_id,omitempty"`
	// 故事书玩法新增字段
	ChapterId            string   `protobuf:"bytes,4,opt,name=chapter_id,json=chapterId,proto3" json:"chapter_id,omitempty"`
	StoryIdStr           string   `protobuf:"bytes,5,opt,name=story_id_str,json=storyIdStr,proto3" json:"story_id_str,omitempty"`
	Version              string   `protobuf:"bytes,6,opt,name=version,proto3" json:"version,omitempty"`
	Name                 string   `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
	Avatar               string   `protobuf:"bytes,8,opt,name=avatar,proto3" json:"avatar,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Ext_StoryFinish) Reset()         { *m = Ext_StoryFinish{} }
func (m *Ext_StoryFinish) String() string { return proto.CompactTextString(m) }
func (*Ext_StoryFinish) ProtoMessage()    {}
func (*Ext_StoryFinish) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{28}
}
func (m *Ext_StoryFinish) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_StoryFinish.Unmarshal(m, b)
}
func (m *Ext_StoryFinish) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_StoryFinish.Marshal(b, m, deterministic)
}
func (dst *Ext_StoryFinish) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_StoryFinish.Merge(dst, src)
}
func (m *Ext_StoryFinish) XXX_Size() int {
	return xxx_messageInfo_Ext_StoryFinish.Size(m)
}
func (m *Ext_StoryFinish) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_StoryFinish.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_StoryFinish proto.InternalMessageInfo

func (m *Ext_StoryFinish) GetStoryId() uint32 {
	if m != nil {
		return m.StoryId
	}
	return 0
}

func (m *Ext_StoryFinish) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *Ext_StoryFinish) GetStoryClassifyId() uint32 {
	if m != nil {
		return m.StoryClassifyId
	}
	return 0
}

func (m *Ext_StoryFinish) GetChapterId() string {
	if m != nil {
		return m.ChapterId
	}
	return ""
}

func (m *Ext_StoryFinish) GetStoryIdStr() string {
	if m != nil {
		return m.StoryIdStr
	}
	return ""
}

func (m *Ext_StoryFinish) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *Ext_StoryFinish) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Ext_StoryFinish) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

type Ext_StoryOptForUserReply struct {
	DialogId  string `protobuf:"bytes,1,opt,name=dialog_id,json=dialogId,proto3" json:"dialog_id,omitempty"`
	StoryId   uint32 `protobuf:"varint,2,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	ContentId string `protobuf:"bytes,3,opt,name=content_id,json=contentId,proto3" json:"content_id,omitempty"`
	// 故事书玩法新增字段
	ChapterId            string   `protobuf:"bytes,4,opt,name=chapter_id,json=chapterId,proto3" json:"chapter_id,omitempty"`
	StoryIdStr           string   `protobuf:"bytes,5,opt,name=story_id_str,json=storyIdStr,proto3" json:"story_id_str,omitempty"`
	Version              string   `protobuf:"bytes,6,opt,name=version,proto3" json:"version,omitempty"`
	Name                 string   `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
	Avatar               string   `protobuf:"bytes,8,opt,name=avatar,proto3" json:"avatar,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Ext_StoryOptForUserReply) Reset()         { *m = Ext_StoryOptForUserReply{} }
func (m *Ext_StoryOptForUserReply) String() string { return proto.CompactTextString(m) }
func (*Ext_StoryOptForUserReply) ProtoMessage()    {}
func (*Ext_StoryOptForUserReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{29}
}
func (m *Ext_StoryOptForUserReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_StoryOptForUserReply.Unmarshal(m, b)
}
func (m *Ext_StoryOptForUserReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_StoryOptForUserReply.Marshal(b, m, deterministic)
}
func (dst *Ext_StoryOptForUserReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_StoryOptForUserReply.Merge(dst, src)
}
func (m *Ext_StoryOptForUserReply) XXX_Size() int {
	return xxx_messageInfo_Ext_StoryOptForUserReply.Size(m)
}
func (m *Ext_StoryOptForUserReply) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_StoryOptForUserReply.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_StoryOptForUserReply proto.InternalMessageInfo

func (m *Ext_StoryOptForUserReply) GetDialogId() string {
	if m != nil {
		return m.DialogId
	}
	return ""
}

func (m *Ext_StoryOptForUserReply) GetStoryId() uint32 {
	if m != nil {
		return m.StoryId
	}
	return 0
}

func (m *Ext_StoryOptForUserReply) GetContentId() string {
	if m != nil {
		return m.ContentId
	}
	return ""
}

func (m *Ext_StoryOptForUserReply) GetChapterId() string {
	if m != nil {
		return m.ChapterId
	}
	return ""
}

func (m *Ext_StoryOptForUserReply) GetStoryIdStr() string {
	if m != nil {
		return m.StoryIdStr
	}
	return ""
}

func (m *Ext_StoryOptForUserReply) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *Ext_StoryOptForUserReply) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Ext_StoryOptForUserReply) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

type Ext_StoryRewardsNotify struct {
	StoryId uint32 `protobuf:"varint,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	// 故事入口消息内容, rich_text需要在里面的style定一个点击类型
	RichText             []*RichTextFormat `protobuf:"bytes,2,rep,name=rich_text,json=richText,proto3" json:"rich_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *Ext_StoryRewardsNotify) Reset()         { *m = Ext_StoryRewardsNotify{} }
func (m *Ext_StoryRewardsNotify) String() string { return proto.CompactTextString(m) }
func (*Ext_StoryRewardsNotify) ProtoMessage()    {}
func (*Ext_StoryRewardsNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{30}
}
func (m *Ext_StoryRewardsNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_StoryRewardsNotify.Unmarshal(m, b)
}
func (m *Ext_StoryRewardsNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_StoryRewardsNotify.Marshal(b, m, deterministic)
}
func (dst *Ext_StoryRewardsNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_StoryRewardsNotify.Merge(dst, src)
}
func (m *Ext_StoryRewardsNotify) XXX_Size() int {
	return xxx_messageInfo_Ext_StoryRewardsNotify.Size(m)
}
func (m *Ext_StoryRewardsNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_StoryRewardsNotify.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_StoryRewardsNotify proto.InternalMessageInfo

func (m *Ext_StoryRewardsNotify) GetStoryId() uint32 {
	if m != nil {
		return m.StoryId
	}
	return 0
}

func (m *Ext_StoryRewardsNotify) GetRichText() []*RichTextFormat {
	if m != nil {
		return m.RichText
	}
	return nil
}

type Ext_NewAtmosphere struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ResourceUrl          string   `protobuf:"bytes,2,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	ResourceType         string   `protobuf:"bytes,3,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	Description          string   `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	AudioUrl             string   `protobuf:"bytes,5,opt,name=audio_url,json=audioUrl,proto3" json:"audio_url,omitempty"`
	SmallUrl             string   `protobuf:"bytes,6,opt,name=small_url,json=smallUrl,proto3" json:"small_url,omitempty"`
	ImageUrl             string   `protobuf:"bytes,7,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Ext_NewAtmosphere) Reset()         { *m = Ext_NewAtmosphere{} }
func (m *Ext_NewAtmosphere) String() string { return proto.CompactTextString(m) }
func (*Ext_NewAtmosphere) ProtoMessage()    {}
func (*Ext_NewAtmosphere) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{31}
}
func (m *Ext_NewAtmosphere) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_NewAtmosphere.Unmarshal(m, b)
}
func (m *Ext_NewAtmosphere) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_NewAtmosphere.Marshal(b, m, deterministic)
}
func (dst *Ext_NewAtmosphere) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_NewAtmosphere.Merge(dst, src)
}
func (m *Ext_NewAtmosphere) XXX_Size() int {
	return xxx_messageInfo_Ext_NewAtmosphere.Size(m)
}
func (m *Ext_NewAtmosphere) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_NewAtmosphere.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_NewAtmosphere proto.InternalMessageInfo

func (m *Ext_NewAtmosphere) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *Ext_NewAtmosphere) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *Ext_NewAtmosphere) GetResourceType() string {
	if m != nil {
		return m.ResourceType
	}
	return ""
}

func (m *Ext_NewAtmosphere) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *Ext_NewAtmosphere) GetAudioUrl() string {
	if m != nil {
		return m.AudioUrl
	}
	return ""
}

func (m *Ext_NewAtmosphere) GetSmallUrl() string {
	if m != nil {
		return m.SmallUrl
	}
	return ""
}

func (m *Ext_NewAtmosphere) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

type Ext_StoryAnimateMeme struct {
	DialogId             string   `protobuf:"bytes,1,opt,name=dialog_id,json=dialogId,proto3" json:"dialog_id,omitempty"`
	StoryId              uint32   `protobuf:"varint,2,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	UseNeedReply         bool     `protobuf:"varint,3,opt,name=use_need_reply,json=useNeedReply,proto3" json:"use_need_reply,omitempty"`
	IsAutoShow           bool     `protobuf:"varint,4,opt,name=is_auto_show,json=isAutoShow,proto3" json:"is_auto_show,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Ext_StoryAnimateMeme) Reset()         { *m = Ext_StoryAnimateMeme{} }
func (m *Ext_StoryAnimateMeme) String() string { return proto.CompactTextString(m) }
func (*Ext_StoryAnimateMeme) ProtoMessage()    {}
func (*Ext_StoryAnimateMeme) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{32}
}
func (m *Ext_StoryAnimateMeme) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_StoryAnimateMeme.Unmarshal(m, b)
}
func (m *Ext_StoryAnimateMeme) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_StoryAnimateMeme.Marshal(b, m, deterministic)
}
func (dst *Ext_StoryAnimateMeme) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_StoryAnimateMeme.Merge(dst, src)
}
func (m *Ext_StoryAnimateMeme) XXX_Size() int {
	return xxx_messageInfo_Ext_StoryAnimateMeme.Size(m)
}
func (m *Ext_StoryAnimateMeme) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_StoryAnimateMeme.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_StoryAnimateMeme proto.InternalMessageInfo

func (m *Ext_StoryAnimateMeme) GetDialogId() string {
	if m != nil {
		return m.DialogId
	}
	return ""
}

func (m *Ext_StoryAnimateMeme) GetStoryId() uint32 {
	if m != nil {
		return m.StoryId
	}
	return 0
}

func (m *Ext_StoryAnimateMeme) GetUseNeedReply() bool {
	if m != nil {
		return m.UseNeedReply
	}
	return false
}

func (m *Ext_StoryAnimateMeme) GetIsAutoShow() bool {
	if m != nil {
		return m.IsAutoShow
	}
	return false
}

type StoryNotify struct {
	Uid                  uint32                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32                 `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	StoryId              uint32                 `protobuf:"varint,3,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	NotifyType           StoryNotify_NotifyType `protobuf:"varint,4,opt,name=notify_type,json=notifyType,proto3,enum=rcmd.rcmd_ai_partner_kafka.StoryNotify_NotifyType" json:"notify_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *StoryNotify) Reset()         { *m = StoryNotify{} }
func (m *StoryNotify) String() string { return proto.CompactTextString(m) }
func (*StoryNotify) ProtoMessage()    {}
func (*StoryNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{33}
}
func (m *StoryNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoryNotify.Unmarshal(m, b)
}
func (m *StoryNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoryNotify.Marshal(b, m, deterministic)
}
func (dst *StoryNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoryNotify.Merge(dst, src)
}
func (m *StoryNotify) XXX_Size() int {
	return xxx_messageInfo_StoryNotify.Size(m)
}
func (m *StoryNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_StoryNotify.DiscardUnknown(m)
}

var xxx_messageInfo_StoryNotify proto.InternalMessageInfo

func (m *StoryNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StoryNotify) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *StoryNotify) GetStoryId() uint32 {
	if m != nil {
		return m.StoryId
	}
	return 0
}

func (m *StoryNotify) GetNotifyType() StoryNotify_NotifyType {
	if m != nil {
		return m.NotifyType
	}
	return StoryNotify_NotifyType_Invalid
}

type PushSchedule struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Order                uint32            `protobuf:"varint,2,opt,name=order,proto3" json:"order,omitempty"`
	Type                 PushSchedule_Type `protobuf:"varint,3,opt,name=type,proto3,enum=rcmd.rcmd_ai_partner_kafka.PushSchedule_Type" json:"type,omitempty"`
	RoleId               uint32            `protobuf:"varint,4,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	PartnerId            uint32            `protobuf:"varint,5,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PushSchedule) Reset()         { *m = PushSchedule{} }
func (m *PushSchedule) String() string { return proto.CompactTextString(m) }
func (*PushSchedule) ProtoMessage()    {}
func (*PushSchedule) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{34}
}
func (m *PushSchedule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushSchedule.Unmarshal(m, b)
}
func (m *PushSchedule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushSchedule.Marshal(b, m, deterministic)
}
func (dst *PushSchedule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushSchedule.Merge(dst, src)
}
func (m *PushSchedule) XXX_Size() int {
	return xxx_messageInfo_PushSchedule.Size(m)
}
func (m *PushSchedule) XXX_DiscardUnknown() {
	xxx_messageInfo_PushSchedule.DiscardUnknown(m)
}

var xxx_messageInfo_PushSchedule proto.InternalMessageInfo

func (m *PushSchedule) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PushSchedule) GetOrder() uint32 {
	if m != nil {
		return m.Order
	}
	return 0
}

func (m *PushSchedule) GetType() PushSchedule_Type {
	if m != nil {
		return m.Type
	}
	return PushSchedule_Type_Default
}

func (m *PushSchedule) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *PushSchedule) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

type UserReadMsgNotify struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	MsgId                string   `protobuf:"bytes,3,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	ReceivedTs           int64    `protobuf:"varint,4,opt,name=received_ts,json=receivedTs,proto3" json:"received_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserReadMsgNotify) Reset()         { *m = UserReadMsgNotify{} }
func (m *UserReadMsgNotify) String() string { return proto.CompactTextString(m) }
func (*UserReadMsgNotify) ProtoMessage()    {}
func (*UserReadMsgNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{35}
}
func (m *UserReadMsgNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserReadMsgNotify.Unmarshal(m, b)
}
func (m *UserReadMsgNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserReadMsgNotify.Marshal(b, m, deterministic)
}
func (dst *UserReadMsgNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserReadMsgNotify.Merge(dst, src)
}
func (m *UserReadMsgNotify) XXX_Size() int {
	return xxx_messageInfo_UserReadMsgNotify.Size(m)
}
func (m *UserReadMsgNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_UserReadMsgNotify.DiscardUnknown(m)
}

var xxx_messageInfo_UserReadMsgNotify proto.InternalMessageInfo

func (m *UserReadMsgNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserReadMsgNotify) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *UserReadMsgNotify) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *UserReadMsgNotify) GetReceivedTs() int64 {
	if m != nil {
		return m.ReceivedTs
	}
	return 0
}

type UserReadNoReplyEvent struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	ReadMsgId            string   `protobuf:"bytes,3,opt,name=read_msg_id,json=readMsgId,proto3" json:"read_msg_id,omitempty"`
	ReceivedTs           int64    `protobuf:"varint,4,opt,name=received_ts,json=receivedTs,proto3" json:"received_ts,omitempty"`
	ReadMsgSentTs        int64    `protobuf:"varint,5,opt,name=read_msg_sent_ts,json=readMsgSentTs,proto3" json:"read_msg_sent_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserReadNoReplyEvent) Reset()         { *m = UserReadNoReplyEvent{} }
func (m *UserReadNoReplyEvent) String() string { return proto.CompactTextString(m) }
func (*UserReadNoReplyEvent) ProtoMessage()    {}
func (*UserReadNoReplyEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{36}
}
func (m *UserReadNoReplyEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserReadNoReplyEvent.Unmarshal(m, b)
}
func (m *UserReadNoReplyEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserReadNoReplyEvent.Marshal(b, m, deterministic)
}
func (dst *UserReadNoReplyEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserReadNoReplyEvent.Merge(dst, src)
}
func (m *UserReadNoReplyEvent) XXX_Size() int {
	return xxx_messageInfo_UserReadNoReplyEvent.Size(m)
}
func (m *UserReadNoReplyEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_UserReadNoReplyEvent.DiscardUnknown(m)
}

var xxx_messageInfo_UserReadNoReplyEvent proto.InternalMessageInfo

func (m *UserReadNoReplyEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserReadNoReplyEvent) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *UserReadNoReplyEvent) GetReadMsgId() string {
	if m != nil {
		return m.ReadMsgId
	}
	return ""
}

func (m *UserReadNoReplyEvent) GetReceivedTs() int64 {
	if m != nil {
		return m.ReceivedTs
	}
	return 0
}

func (m *UserReadNoReplyEvent) GetReadMsgSentTs() int64 {
	if m != nil {
		return m.ReadMsgSentTs
	}
	return 0
}

type PartnerSharedUserEvent struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	InviterUid           uint32   `protobuf:"varint,2,opt,name=inviterUid,proto3" json:"inviterUid,omitempty"`
	Ts                   uint32   `protobuf:"varint,3,opt,name=ts,proto3" json:"ts,omitempty"`
	DeviceIdHex          string   `protobuf:"bytes,4,opt,name=device_id_hex,json=deviceIdHex,proto3" json:"device_id_hex,omitempty"`
	ClientType           uint32   `protobuf:"varint,5,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	MarketId             uint32   `protobuf:"varint,6,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	CliDeviceId          string   `protobuf:"bytes,7,opt,name=cli_device_id,json=cliDeviceId,proto3" json:"cli_device_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PartnerSharedUserEvent) Reset()         { *m = PartnerSharedUserEvent{} }
func (m *PartnerSharedUserEvent) String() string { return proto.CompactTextString(m) }
func (*PartnerSharedUserEvent) ProtoMessage()    {}
func (*PartnerSharedUserEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{37}
}
func (m *PartnerSharedUserEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PartnerSharedUserEvent.Unmarshal(m, b)
}
func (m *PartnerSharedUserEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PartnerSharedUserEvent.Marshal(b, m, deterministic)
}
func (dst *PartnerSharedUserEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PartnerSharedUserEvent.Merge(dst, src)
}
func (m *PartnerSharedUserEvent) XXX_Size() int {
	return xxx_messageInfo_PartnerSharedUserEvent.Size(m)
}
func (m *PartnerSharedUserEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_PartnerSharedUserEvent.DiscardUnknown(m)
}

var xxx_messageInfo_PartnerSharedUserEvent proto.InternalMessageInfo

func (m *PartnerSharedUserEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PartnerSharedUserEvent) GetInviterUid() uint32 {
	if m != nil {
		return m.InviterUid
	}
	return 0
}

func (m *PartnerSharedUserEvent) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *PartnerSharedUserEvent) GetDeviceIdHex() string {
	if m != nil {
		return m.DeviceIdHex
	}
	return ""
}

func (m *PartnerSharedUserEvent) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *PartnerSharedUserEvent) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *PartnerSharedUserEvent) GetCliDeviceId() string {
	if m != nil {
		return m.CliDeviceId
	}
	return ""
}

type RoleCreatorNotifyEvent struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ChattedUserCount     uint32   `protobuf:"varint,3,opt,name=chatted_user_count,json=chattedUserCount,proto3" json:"chatted_user_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoleCreatorNotifyEvent) Reset()         { *m = RoleCreatorNotifyEvent{} }
func (m *RoleCreatorNotifyEvent) String() string { return proto.CompactTextString(m) }
func (*RoleCreatorNotifyEvent) ProtoMessage()    {}
func (*RoleCreatorNotifyEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{38}
}
func (m *RoleCreatorNotifyEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoleCreatorNotifyEvent.Unmarshal(m, b)
}
func (m *RoleCreatorNotifyEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoleCreatorNotifyEvent.Marshal(b, m, deterministic)
}
func (dst *RoleCreatorNotifyEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoleCreatorNotifyEvent.Merge(dst, src)
}
func (m *RoleCreatorNotifyEvent) XXX_Size() int {
	return xxx_messageInfo_RoleCreatorNotifyEvent.Size(m)
}
func (m *RoleCreatorNotifyEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_RoleCreatorNotifyEvent.DiscardUnknown(m)
}

var xxx_messageInfo_RoleCreatorNotifyEvent proto.InternalMessageInfo

func (m *RoleCreatorNotifyEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RoleCreatorNotifyEvent) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *RoleCreatorNotifyEvent) GetChattedUserCount() uint32 {
	if m != nil {
		return m.ChattedUserCount
	}
	return 0
}

type WhiteListUserEvent struct {
	Uid                  uint32                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DeviceIdHex          string                    `protobuf:"bytes,2,opt,name=device_id_hex,json=deviceIdHex,proto3" json:"device_id_hex,omitempty"`
	ClientType           uint32                    `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	DeviceStatus         string                    `protobuf:"bytes,4,opt,name=device_status,json=deviceStatus,proto3" json:"device_status,omitempty"`
	MarketId             uint32                    `protobuf:"varint,5,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	CliDeviceId          string                    `protobuf:"bytes,6,opt,name=cli_device_id,json=cliDeviceId,proto3" json:"cli_device_id,omitempty"`
	Source               WhiteListUserEvent_Source `protobuf:"varint,7,opt,name=source,proto3,enum=rcmd.rcmd_ai_partner_kafka.WhiteListUserEvent_Source" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *WhiteListUserEvent) Reset()         { *m = WhiteListUserEvent{} }
func (m *WhiteListUserEvent) String() string { return proto.CompactTextString(m) }
func (*WhiteListUserEvent) ProtoMessage()    {}
func (*WhiteListUserEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{39}
}
func (m *WhiteListUserEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WhiteListUserEvent.Unmarshal(m, b)
}
func (m *WhiteListUserEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WhiteListUserEvent.Marshal(b, m, deterministic)
}
func (dst *WhiteListUserEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WhiteListUserEvent.Merge(dst, src)
}
func (m *WhiteListUserEvent) XXX_Size() int {
	return xxx_messageInfo_WhiteListUserEvent.Size(m)
}
func (m *WhiteListUserEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_WhiteListUserEvent.DiscardUnknown(m)
}

var xxx_messageInfo_WhiteListUserEvent proto.InternalMessageInfo

func (m *WhiteListUserEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *WhiteListUserEvent) GetDeviceIdHex() string {
	if m != nil {
		return m.DeviceIdHex
	}
	return ""
}

func (m *WhiteListUserEvent) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *WhiteListUserEvent) GetDeviceStatus() string {
	if m != nil {
		return m.DeviceStatus
	}
	return ""
}

func (m *WhiteListUserEvent) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *WhiteListUserEvent) GetCliDeviceId() string {
	if m != nil {
		return m.CliDeviceId
	}
	return ""
}

func (m *WhiteListUserEvent) GetSource() WhiteListUserEvent_Source {
	if m != nil {
		return m.Source
	}
	return WhiteListUserEvent_Source_COST
}

type StoryBookNotify struct {
	Uid                  uint32               `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32               `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32               `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	StoryId              string               `protobuf:"bytes,4,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	ChapterId            string               `protobuf:"bytes,5,opt,name=chapter_id,json=chapterId,proto3" json:"chapter_id,omitempty"`
	Type                 StoryBookNotify_Type `protobuf:"varint,6,opt,name=type,proto3,enum=rcmd.rcmd_ai_partner_kafka.StoryBookNotify_Type" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *StoryBookNotify) Reset()         { *m = StoryBookNotify{} }
func (m *StoryBookNotify) String() string { return proto.CompactTextString(m) }
func (*StoryBookNotify) ProtoMessage()    {}
func (*StoryBookNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{40}
}
func (m *StoryBookNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoryBookNotify.Unmarshal(m, b)
}
func (m *StoryBookNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoryBookNotify.Marshal(b, m, deterministic)
}
func (dst *StoryBookNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoryBookNotify.Merge(dst, src)
}
func (m *StoryBookNotify) XXX_Size() int {
	return xxx_messageInfo_StoryBookNotify.Size(m)
}
func (m *StoryBookNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_StoryBookNotify.DiscardUnknown(m)
}

var xxx_messageInfo_StoryBookNotify proto.InternalMessageInfo

func (m *StoryBookNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StoryBookNotify) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *StoryBookNotify) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *StoryBookNotify) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

func (m *StoryBookNotify) GetChapterId() string {
	if m != nil {
		return m.ChapterId
	}
	return ""
}

func (m *StoryBookNotify) GetType() StoryBookNotify_Type {
	if m != nil {
		return m.Type
	}
	return StoryBookNotify_Type_Invalid
}

type Ext_StoryTTSMsg struct {
	Url          string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Seconds      uint32 `protobuf:"varint,2,opt,name=seconds,proto3" json:"seconds,omitempty"`
	Text         string `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	DialogId     string `protobuf:"bytes,4,opt,name=dialog_id,json=dialogId,proto3" json:"dialog_id,omitempty"`
	StoryIdStr   string `protobuf:"bytes,5,opt,name=story_id_str,json=storyIdStr,proto3" json:"story_id_str,omitempty"`
	UseNeedReply bool   `protobuf:"varint,6,opt,name=use_need_reply,json=useNeedReply,proto3" json:"use_need_reply,omitempty"`
	// 故事书玩法新增字段
	ChapterId            string   `protobuf:"bytes,7,opt,name=chapter_id,json=chapterId,proto3" json:"chapter_id,omitempty"`
	Version              string   `protobuf:"bytes,8,opt,name=version,proto3" json:"version,omitempty"`
	Name                 string   `protobuf:"bytes,9,opt,name=name,proto3" json:"name,omitempty"`
	Avatar               string   `protobuf:"bytes,10,opt,name=avatar,proto3" json:"avatar,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Ext_StoryTTSMsg) Reset()         { *m = Ext_StoryTTSMsg{} }
func (m *Ext_StoryTTSMsg) String() string { return proto.CompactTextString(m) }
func (*Ext_StoryTTSMsg) ProtoMessage()    {}
func (*Ext_StoryTTSMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{41}
}
func (m *Ext_StoryTTSMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_StoryTTSMsg.Unmarshal(m, b)
}
func (m *Ext_StoryTTSMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_StoryTTSMsg.Marshal(b, m, deterministic)
}
func (dst *Ext_StoryTTSMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_StoryTTSMsg.Merge(dst, src)
}
func (m *Ext_StoryTTSMsg) XXX_Size() int {
	return xxx_messageInfo_Ext_StoryTTSMsg.Size(m)
}
func (m *Ext_StoryTTSMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_StoryTTSMsg.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_StoryTTSMsg proto.InternalMessageInfo

func (m *Ext_StoryTTSMsg) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *Ext_StoryTTSMsg) GetSeconds() uint32 {
	if m != nil {
		return m.Seconds
	}
	return 0
}

func (m *Ext_StoryTTSMsg) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *Ext_StoryTTSMsg) GetDialogId() string {
	if m != nil {
		return m.DialogId
	}
	return ""
}

func (m *Ext_StoryTTSMsg) GetStoryIdStr() string {
	if m != nil {
		return m.StoryIdStr
	}
	return ""
}

func (m *Ext_StoryTTSMsg) GetUseNeedReply() bool {
	if m != nil {
		return m.UseNeedReply
	}
	return false
}

func (m *Ext_StoryTTSMsg) GetChapterId() string {
	if m != nil {
		return m.ChapterId
	}
	return ""
}

func (m *Ext_StoryTTSMsg) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *Ext_StoryTTSMsg) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Ext_StoryTTSMsg) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

type PetTipReport struct {
	Uid                  uint32              `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32              `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32              `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Action               PetTipReport_Action `protobuf:"varint,4,opt,name=action,proto3,enum=rcmd.rcmd_ai_partner_kafka.PetTipReport_Action" json:"action,omitempty"`
	Tip                  string              `protobuf:"bytes,5,opt,name=tip,proto3" json:"tip,omitempty"`
	MsgId                string              `protobuf:"bytes,6,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	SentAt               int64               `protobuf:"varint,7,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PetTipReport) Reset()         { *m = PetTipReport{} }
func (m *PetTipReport) String() string { return proto.CompactTextString(m) }
func (*PetTipReport) ProtoMessage()    {}
func (*PetTipReport) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{42}
}
func (m *PetTipReport) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetTipReport.Unmarshal(m, b)
}
func (m *PetTipReport) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetTipReport.Marshal(b, m, deterministic)
}
func (dst *PetTipReport) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetTipReport.Merge(dst, src)
}
func (m *PetTipReport) XXX_Size() int {
	return xxx_messageInfo_PetTipReport.Size(m)
}
func (m *PetTipReport) XXX_DiscardUnknown() {
	xxx_messageInfo_PetTipReport.DiscardUnknown(m)
}

var xxx_messageInfo_PetTipReport proto.InternalMessageInfo

func (m *PetTipReport) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PetTipReport) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *PetTipReport) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *PetTipReport) GetAction() PetTipReport_Action {
	if m != nil {
		return m.Action
	}
	return PetTipReport_Action_Invalid
}

func (m *PetTipReport) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

func (m *PetTipReport) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *PetTipReport) GetSentAt() int64 {
	if m != nil {
		return m.SentAt
	}
	return 0
}

type Ext_PetReply struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Seconds              uint32   `protobuf:"varint,2,opt,name=seconds,proto3" json:"seconds,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	UserText             string   `protobuf:"bytes,4,opt,name=user_text,json=userText,proto3" json:"user_text,omitempty"`
	ContentType          string   `protobuf:"bytes,5,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	BubbleMsg            string   `protobuf:"bytes,6,opt,name=bubble_msg,json=bubbleMsg,proto3" json:"bubble_msg,omitempty"`
	DisplayMsg           string   `protobuf:"bytes,7,opt,name=display_msg,json=displayMsg,proto3" json:"display_msg,omitempty"`
	TargetId             string   `protobuf:"bytes,8,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	TargetTitle          string   `protobuf:"bytes,9,opt,name=target_title,json=targetTitle,proto3" json:"target_title,omitempty"`
	TargetSubtitle       string   `protobuf:"bytes,10,opt,name=target_subtitle,json=targetSubtitle,proto3" json:"target_subtitle,omitempty"`
	TargetIcon           string   `protobuf:"bytes,11,opt,name=target_icon,json=targetIcon,proto3" json:"target_icon,omitempty"`
	HasReward            bool     `protobuf:"varint,12,opt,name=has_reward,json=hasReward,proto3" json:"has_reward,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Ext_PetReply) Reset()         { *m = Ext_PetReply{} }
func (m *Ext_PetReply) String() string { return proto.CompactTextString(m) }
func (*Ext_PetReply) ProtoMessage()    {}
func (*Ext_PetReply) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{43}
}
func (m *Ext_PetReply) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Ext_PetReply.Unmarshal(m, b)
}
func (m *Ext_PetReply) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Ext_PetReply.Marshal(b, m, deterministic)
}
func (dst *Ext_PetReply) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ext_PetReply.Merge(dst, src)
}
func (m *Ext_PetReply) XXX_Size() int {
	return xxx_messageInfo_Ext_PetReply.Size(m)
}
func (m *Ext_PetReply) XXX_DiscardUnknown() {
	xxx_messageInfo_Ext_PetReply.DiscardUnknown(m)
}

var xxx_messageInfo_Ext_PetReply proto.InternalMessageInfo

func (m *Ext_PetReply) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *Ext_PetReply) GetSeconds() uint32 {
	if m != nil {
		return m.Seconds
	}
	return 0
}

func (m *Ext_PetReply) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *Ext_PetReply) GetUserText() string {
	if m != nil {
		return m.UserText
	}
	return ""
}

func (m *Ext_PetReply) GetContentType() string {
	if m != nil {
		return m.ContentType
	}
	return ""
}

func (m *Ext_PetReply) GetBubbleMsg() string {
	if m != nil {
		return m.BubbleMsg
	}
	return ""
}

func (m *Ext_PetReply) GetDisplayMsg() string {
	if m != nil {
		return m.DisplayMsg
	}
	return ""
}

func (m *Ext_PetReply) GetTargetId() string {
	if m != nil {
		return m.TargetId
	}
	return ""
}

func (m *Ext_PetReply) GetTargetTitle() string {
	if m != nil {
		return m.TargetTitle
	}
	return ""
}

func (m *Ext_PetReply) GetTargetSubtitle() string {
	if m != nil {
		return m.TargetSubtitle
	}
	return ""
}

func (m *Ext_PetReply) GetTargetIcon() string {
	if m != nil {
		return m.TargetIcon
	}
	return ""
}

func (m *Ext_PetReply) GetHasReward() bool {
	if m != nil {
		return m.HasReward
	}
	return false
}

type GroupReportExtra struct {
	RoleName             string   `protobuf:"bytes,1,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	Order                uint32   `protobuf:"varint,2,opt,name=order,proto3" json:"order,omitempty"`
	IsUseExtraChat       bool     `protobuf:"varint,3,opt,name=is_use_extra_chat,json=isUseExtraChat,proto3" json:"is_use_extra_chat,omitempty"`
	ChatContext          string   `protobuf:"bytes,4,opt,name=chat_context,json=chatContext,proto3" json:"chat_context,omitempty"`
	ModelUsed            string   `protobuf:"bytes,5,opt,name=model_used,json=modelUsed,proto3" json:"model_used,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupReportExtra) Reset()         { *m = GroupReportExtra{} }
func (m *GroupReportExtra) String() string { return proto.CompactTextString(m) }
func (*GroupReportExtra) ProtoMessage()    {}
func (*GroupReportExtra) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{44}
}
func (m *GroupReportExtra) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupReportExtra.Unmarshal(m, b)
}
func (m *GroupReportExtra) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupReportExtra.Marshal(b, m, deterministic)
}
func (dst *GroupReportExtra) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupReportExtra.Merge(dst, src)
}
func (m *GroupReportExtra) XXX_Size() int {
	return xxx_messageInfo_GroupReportExtra.Size(m)
}
func (m *GroupReportExtra) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupReportExtra.DiscardUnknown(m)
}

var xxx_messageInfo_GroupReportExtra proto.InternalMessageInfo

func (m *GroupReportExtra) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

func (m *GroupReportExtra) GetOrder() uint32 {
	if m != nil {
		return m.Order
	}
	return 0
}

func (m *GroupReportExtra) GetIsUseExtraChat() bool {
	if m != nil {
		return m.IsUseExtraChat
	}
	return false
}

func (m *GroupReportExtra) GetChatContext() string {
	if m != nil {
		return m.ChatContext
	}
	return ""
}

func (m *GroupReportExtra) GetModelUsed() string {
	if m != nil {
		return m.ModelUsed
	}
	return ""
}

type MP_GroupDelayMsgEvent struct {
	UserReply            *MP_GroupUserReply `protobuf:"bytes,1,opt,name=user_reply,json=userReply,proto3" json:"user_reply,omitempty"`
	SendMsgs             []*SendMsg         `protobuf:"bytes,2,rep,name=send_msgs,json=sendMsgs,proto3" json:"send_msgs,omitempty"`
	RoleId               uint32             `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	DelayTime            int64              `protobuf:"varint,4,opt,name=delay_time,json=delayTime,proto3" json:"delay_time,omitempty"`
	AuditContents        []string           `protobuf:"bytes,5,rep,name=audit_contents,json=auditContents,proto3" json:"audit_contents,omitempty"`
	ReportExtra          *GroupReportExtra  `protobuf:"bytes,6,opt,name=report_extra,json=reportExtra,proto3" json:"report_extra,omitempty"`
	GroupRoleIds         []uint32           `protobuf:"varint,7,rep,packed,name=group_role_ids,json=groupRoleIds,proto3" json:"group_role_ids,omitempty"`
	RoleIdToName         map[uint32]string  `protobuf:"bytes,8,rep,name=role_id_to_name,json=roleIdToName,proto3" json:"role_id_to_name,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ReplyMsgPlanB        uint32             `protobuf:"varint,9,opt,name=reply_msg_plan_b,json=replyMsgPlanB,proto3" json:"reply_msg_plan_b,omitempty"`
	SendOrder            uint32             `protobuf:"varint,10,opt,name=send_order,json=sendOrder,proto3" json:"send_order,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MP_GroupDelayMsgEvent) Reset()         { *m = MP_GroupDelayMsgEvent{} }
func (m *MP_GroupDelayMsgEvent) String() string { return proto.CompactTextString(m) }
func (*MP_GroupDelayMsgEvent) ProtoMessage()    {}
func (*MP_GroupDelayMsgEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f, []int{45}
}
func (m *MP_GroupDelayMsgEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_GroupDelayMsgEvent.Unmarshal(m, b)
}
func (m *MP_GroupDelayMsgEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_GroupDelayMsgEvent.Marshal(b, m, deterministic)
}
func (dst *MP_GroupDelayMsgEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_GroupDelayMsgEvent.Merge(dst, src)
}
func (m *MP_GroupDelayMsgEvent) XXX_Size() int {
	return xxx_messageInfo_MP_GroupDelayMsgEvent.Size(m)
}
func (m *MP_GroupDelayMsgEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_GroupDelayMsgEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_GroupDelayMsgEvent proto.InternalMessageInfo

func (m *MP_GroupDelayMsgEvent) GetUserReply() *MP_GroupUserReply {
	if m != nil {
		return m.UserReply
	}
	return nil
}

func (m *MP_GroupDelayMsgEvent) GetSendMsgs() []*SendMsg {
	if m != nil {
		return m.SendMsgs
	}
	return nil
}

func (m *MP_GroupDelayMsgEvent) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *MP_GroupDelayMsgEvent) GetDelayTime() int64 {
	if m != nil {
		return m.DelayTime
	}
	return 0
}

func (m *MP_GroupDelayMsgEvent) GetAuditContents() []string {
	if m != nil {
		return m.AuditContents
	}
	return nil
}

func (m *MP_GroupDelayMsgEvent) GetReportExtra() *GroupReportExtra {
	if m != nil {
		return m.ReportExtra
	}
	return nil
}

func (m *MP_GroupDelayMsgEvent) GetGroupRoleIds() []uint32 {
	if m != nil {
		return m.GroupRoleIds
	}
	return nil
}

func (m *MP_GroupDelayMsgEvent) GetRoleIdToName() map[uint32]string {
	if m != nil {
		return m.RoleIdToName
	}
	return nil
}

func (m *MP_GroupDelayMsgEvent) GetReplyMsgPlanB() uint32 {
	if m != nil {
		return m.ReplyMsgPlanB
	}
	return 0
}

func (m *MP_GroupDelayMsgEvent) GetSendOrder() uint32 {
	if m != nil {
		return m.SendOrder
	}
	return 0
}

func init() {
	proto.RegisterType((*MP_BreakSilenceGreeting)(nil), "rcmd.rcmd_ai_partner_kafka.MP_BreakSilenceGreeting")
	proto.RegisterType((*MP_Greeting)(nil), "rcmd.rcmd_ai_partner_kafka.MP_Greeting")
	proto.RegisterType((*MP_LoginGreeting)(nil), "rcmd.rcmd_ai_partner_kafka.MP_LoginGreeting")
	proto.RegisterType((*MP_ScheduleGreeting)(nil), "rcmd.rcmd_ai_partner_kafka.MP_ScheduleGreeting")
	proto.RegisterType((*MP_UserReply)(nil), "rcmd.rcmd_ai_partner_kafka.MP_UserReply")
	proto.RegisterType((*MP_GroupUserReply)(nil), "rcmd.rcmd_ai_partner_kafka.MP_GroupUserReply")
	proto.RegisterType((*MP_SilenceNotify)(nil), "rcmd.rcmd_ai_partner_kafka.MP_SilenceNotify")
	proto.RegisterType((*MP_ResetElizaCtx)(nil), "rcmd.rcmd_ai_partner_kafka.MP_ResetElizaCtx")
	proto.RegisterType((*MP_Tick)(nil), "rcmd.rcmd_ai_partner_kafka.MP_Tick")
	proto.RegisterType((*MP_DelayMsgEvent)(nil), "rcmd.rcmd_ai_partner_kafka.MP_DelayMsgEvent")
	proto.RegisterType((*StoryInfo)(nil), "rcmd.rcmd_ai_partner_kafka.StoryInfo")
	proto.RegisterType((*SendMsg)(nil), "rcmd.rcmd_ai_partner_kafka.SendMsg")
	proto.RegisterType((*Ext_StoryBookEntrance)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_StoryBookEntrance")
	proto.RegisterType((*CtxInfo)(nil), "rcmd.rcmd_ai_partner_kafka.CtxInfo")
	proto.RegisterType((*UniformMsg)(nil), "rcmd.rcmd_ai_partner_kafka.UniformMsg")
	proto.RegisterType((*Ext_AnimateMeme)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_AnimateMeme")
	proto.RegisterType((*RichTextFormat)(nil), "rcmd.rcmd_ai_partner_kafka.RichTextFormat")
	proto.RegisterType((*Ext_RichText)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_RichText")
	proto.RegisterType((*Ext_RelationshipLetter)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_RelationshipLetter")
	proto.RegisterType((*Ext_TTSMsg)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_TTSMsg")
	proto.RegisterType((*Ext_NewPhotograph)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_NewPhotograph")
	proto.RegisterType((*Ext_UserAutoReply)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_UserAutoReply")
	proto.RegisterType((*Ext_VoiceChatReply)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_VoiceChatReply")
	proto.RegisterType((*IMEvent)(nil), "rcmd.rcmd_ai_partner_kafka.IMEvent")
	proto.RegisterType((*NoReplyEvent)(nil), "rcmd.rcmd_ai_partner_kafka.NoReplyEvent")
	proto.RegisterType((*Ext_StoryEntrance)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_StoryEntrance")
	proto.RegisterType((*Ext_StoryCommon)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_StoryCommon")
	proto.RegisterType((*Ext_StoryNarration)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_StoryNarration")
	proto.RegisterType((*Ext_StoryFinish)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_StoryFinish")
	proto.RegisterType((*Ext_StoryOptForUserReply)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_StoryOptForUserReply")
	proto.RegisterType((*Ext_StoryRewardsNotify)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_StoryRewardsNotify")
	proto.RegisterType((*Ext_NewAtmosphere)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_NewAtmosphere")
	proto.RegisterType((*Ext_StoryAnimateMeme)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_StoryAnimateMeme")
	proto.RegisterType((*StoryNotify)(nil), "rcmd.rcmd_ai_partner_kafka.StoryNotify")
	proto.RegisterType((*PushSchedule)(nil), "rcmd.rcmd_ai_partner_kafka.PushSchedule")
	proto.RegisterType((*UserReadMsgNotify)(nil), "rcmd.rcmd_ai_partner_kafka.UserReadMsgNotify")
	proto.RegisterType((*UserReadNoReplyEvent)(nil), "rcmd.rcmd_ai_partner_kafka.UserReadNoReplyEvent")
	proto.RegisterType((*PartnerSharedUserEvent)(nil), "rcmd.rcmd_ai_partner_kafka.PartnerSharedUserEvent")
	proto.RegisterType((*RoleCreatorNotifyEvent)(nil), "rcmd.rcmd_ai_partner_kafka.RoleCreatorNotifyEvent")
	proto.RegisterType((*WhiteListUserEvent)(nil), "rcmd.rcmd_ai_partner_kafka.WhiteListUserEvent")
	proto.RegisterType((*StoryBookNotify)(nil), "rcmd.rcmd_ai_partner_kafka.StoryBookNotify")
	proto.RegisterType((*Ext_StoryTTSMsg)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_StoryTTSMsg")
	proto.RegisterType((*PetTipReport)(nil), "rcmd.rcmd_ai_partner_kafka.PetTipReport")
	proto.RegisterType((*Ext_PetReply)(nil), "rcmd.rcmd_ai_partner_kafka.Ext_PetReply")
	proto.RegisterType((*GroupReportExtra)(nil), "rcmd.rcmd_ai_partner_kafka.GroupReportExtra")
	proto.RegisterType((*MP_GroupDelayMsgEvent)(nil), "rcmd.rcmd_ai_partner_kafka.MP_GroupDelayMsgEvent")
	proto.RegisterMapType((map[uint32]string)(nil), "rcmd.rcmd_ai_partner_kafka.MP_GroupDelayMsgEvent.RoleIdToNameEntry")
	proto.RegisterEnum("rcmd.rcmd_ai_partner_kafka.UserSource", UserSource_name, UserSource_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner_kafka.MsgType", MsgType_name, MsgType_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner_kafka.CtxScene", CtxScene_name, CtxScene_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner_kafka.MP_BreakSilenceGreeting_Source", MP_BreakSilenceGreeting_Source_name, MP_BreakSilenceGreeting_Source_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner_kafka.MP_Greeting_GreetingType", MP_Greeting_GreetingType_name, MP_Greeting_GreetingType_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner_kafka.MP_LoginGreeting_Type", MP_LoginGreeting_Type_name, MP_LoginGreeting_Type_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner_kafka.MP_ScheduleGreeting_Type", MP_ScheduleGreeting_Type_name, MP_ScheduleGreeting_Type_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner_kafka.IMEvent_RoleType", IMEvent_RoleType_name, IMEvent_RoleType_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner_kafka.NoReplyEvent_Type", NoReplyEvent_Type_name, NoReplyEvent_Type_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner_kafka.StoryNotify_NotifyType", StoryNotify_NotifyType_name, StoryNotify_NotifyType_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner_kafka.PushSchedule_Type", PushSchedule_Type_name, PushSchedule_Type_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner_kafka.WhiteListUserEvent_Source", WhiteListUserEvent_Source_name, WhiteListUserEvent_Source_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner_kafka.StoryBookNotify_Type", StoryBookNotify_Type_name, StoryBookNotify_Type_value)
	proto.RegisterEnum("rcmd.rcmd_ai_partner_kafka.PetTipReport_Action", PetTipReport_Action_name, PetTipReport_Action_value)
}

func init() {
	proto.RegisterFile("rcmd-ai-partner/rcmd_ai_partner_kafka.proto", fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f)
}

var fileDescriptor_rcmd_ai_partner_kafka_cbe647fd5f88654f = []byte{
	// 4747 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x5b, 0xcd, 0x73, 0x24, 0x57,
	0x52, 0x9f, 0xfe, 0xee, 0xce, 0xfe, 0x50, 0xa9, 0x66, 0xa4, 0xe9, 0x99, 0xf1, 0xd8, 0x72, 0x8d,
	0x3f, 0xe4, 0xb1, 0x47, 0xde, 0x1d, 0xb3, 0xc0, 0x1a, 0xaf, 0x59, 0x59, 0xf3, 0xe1, 0x66, 0x47,
	0xb2, 0xa8, 0x6e, 0x8d, 0x03, 0x16, 0xa8, 0x28, 0x55, 0x3d, 0x75, 0xbf, 0xed, 0xea, 0xaa, 0x76,
	0xbd, 0xd7, 0x1a, 0x89, 0xd8, 0x03, 0x11, 0x1c, 0x08, 0x2e, 0x5c, 0x88, 0x20, 0x88, 0x00, 0xfe,
	0x08, 0x13, 0x04, 0x01, 0x07, 0xce, 0x1c, 0xf8, 0xf8, 0x0f, 0x36, 0x82, 0x03, 0x17, 0x0e, 0x5c,
	0xe0, 0x80, 0x23, 0xb8, 0x10, 0x99, 0xef, 0x55, 0x75, 0x55, 0xab, 0x25, 0x8d, 0x65, 0x03, 0x7b,
	0x52, 0x57, 0xe6, 0xfb, 0xcc, 0xcc, 0x97, 0xf9, 0xcb, 0x97, 0x4f, 0xf0, 0x6e, 0xec, 0x4d, 0xfc,
	0x07, 0x2e, 0x7f, 0x30, 0x75, 0x63, 0x19, 0xb2, 0xf8, 0x7d, 0xfc, 0x76, 0x5c, 0xee, 0xe8, 0x6f,
	0x67, 0xec, 0x1e, 0x8d, 0xdd, 0xad, 0x69, 0x1c, 0xc9, 0xc8, 0xbc, 0x8d, 0xcc, 0xad, 0xa5, 0x2d,
	0xac, 0x9f, 0x15, 0xe0, 0xe6, 0xee, 0xbe, 0xf3, 0x49, 0xcc, 0xdc, 0x71, 0x9f, 0x07, 0x2c, 0xf4,
	0xd8, 0xd3, 0x98, 0x31, 0xc9, 0xc3, 0xa1, 0x69, 0x40, 0x69, 0xc6, 0xfd, 0x6e, 0x61, 0xa3, 0xb0,
	0xd9, 0xb6, 0xf1, 0xa7, 0x79, 0x17, 0x20, 0xe9, 0xce, 0xfd, 0x6e, 0x91, 0x18, 0x0d, 0x4d, 0xe9,
	0xf9, 0xa6, 0x0d, 0x55, 0x11, 0xcd, 0x62, 0x8f, 0x75, 0x4b, 0x1b, 0x85, 0xcd, 0xce, 0xc3, 0x0f,
	0xb7, 0xce, 0x9f, 0x79, 0xeb, 0x9c, 0x59, 0xb7, 0xfa, 0x34, 0x82, 0xad, 0x47, 0xb2, 0x7e, 0x00,
	0x55, 0x45, 0x31, 0xbb, 0x70, 0x43, 0xfd, 0x72, 0x1e, 0x87, 0x92, 0xc5, 0x3b, 0x23, 0x57, 0x62,
	0x07, 0xe3, 0x9a, 0x79, 0x0b, 0xd6, 0x34, 0xe7, 0x93, 0x48, 0xf6, 0x59, 0x70, 0x34, 0x88, 0xf9,
	0x70, 0xc8, 0x62, 0xa3, 0x60, 0xfd, 0x71, 0x11, 0x9a, 0xbb, 0xfb, 0xce, 0xd5, 0xf7, 0x74, 0x13,
	0x6a, 0x71, 0x14, 0x30, 0xe4, 0x95, 0x88, 0x57, 0xc5, 0xcf, 0x9e, 0x6f, 0xfe, 0x06, 0xb4, 0x87,
	0x7a, 0x54, 0x47, 0x9e, 0x4e, 0x59, 0xb7, 0x4c, 0x7b, 0xfe, 0x85, 0x4b, 0xf6, 0x9c, 0xee, 0x33,
	0xf9, 0x31, 0x38, 0x9d, 0x32, 0xbb, 0x35, 0xcc, 0x7c, 0x99, 0xeb, 0xa9, 0x1c, 0x2b, 0x1b, 0x85,
	0xcd, 0x46, 0x2a, 0x8b, 0xa7, 0xd0, 0xca, 0xf6, 0x42, 0x89, 0x64, 0xbf, 0x9d, 0x83, 0x70, 0x1c,
	0x46, 0x2f, 0x42, 0xe3, 0x9a, 0x79, 0x17, 0x6e, 0xe5, 0x38, 0x24, 0xb1, 0x7d, 0x26, 0x3f, 0x8d,
	0x26, 0xcc, 0x28, 0x58, 0xff, 0x50, 0x04, 0x63, 0x77, 0xdf, 0x79, 0x16, 0x0d, 0x79, 0x78, 0x81,
	0x68, 0xee, 0x40, 0x63, 0xe2, 0xc6, 0x63, 0x26, 0xe7, 0x92, 0xa9, 0x2b, 0x42, 0xcf, 0x37, 0x5f,
	0x83, 0xa6, 0x17, 0x70, 0xe7, 0x98, 0xc5, 0x82, 0x47, 0xa1, 0x16, 0x0e, 0x78, 0x01, 0x7f, 0xae,
	0x28, 0xe6, 0x63, 0x28, 0x67, 0xe4, 0xf2, 0xdd, 0x4b, 0xe4, 0x92, 0x5b, 0xcb, 0x16, 0x09, 0x85,
	0xba, 0xeb, 0x79, 0x58, 0x28, 0x95, 0x94, 0x2b, 0xe9, 0x3c, 0x2c, 0x94, 0x24, 0x05, 0x0b, 0xda,
	0x3e, 0x3b, 0xe6, 0x1e, 0xea, 0xc8, 0x19, 0xb1, 0x93, 0x6e, 0x95, 0x84, 0xd6, 0x54, 0xc4, 0x9e,
	0xff, 0x29, 0x3b, 0xb1, 0x7e, 0x0c, 0xe5, 0x44, 0x62, 0x34, 0xd1, 0xe3, 0x63, 0xdd, 0x5b, 0xcd,
	0x6b, 0x5c, 0x33, 0xef, 0xc0, 0xcd, 0x05, 0x8e, 0xcd, 0x86, 0x5c, 0x48, 0xb4, 0x22, 0xf3, 0x15,
	0xe8, 0x2e, 0x30, 0x9f, 0x73, 0xc1, 0x95, 0x34, 0x8b, 0xd6, 0x57, 0x65, 0xb8, 0xbe, 0xbb, 0xef,
	0xf4, 0xbd, 0x11, 0xf3, 0x67, 0xc1, 0x37, 0x38, 0x3f, 0x9f, 0x6a, 0x89, 0x95, 0x5e, 0xca, 0x92,
	0x16, 0xe7, 0xcb, 0x0a, 0x6d, 0x13, 0x0c, 0x76, 0x32, 0x65, 0x9e, 0x64, 0xbe, 0x23, 0x58, 0xe8,
	0x3b, 0xae, 0x24, 0x3d, 0x94, 0xec, 0x4e, 0x42, 0xef, 0xb3, 0xd0, 0xdf, 0x96, 0xd8, 0x52, 0xe8,
	0x81, 0x1c, 0x2f, 0x0a, 0x8f, 0x70, 0x61, 0xca, 0xea, 0x3a, 0x09, 0x7d, 0x27, 0x0a, 0x8f, 0xf2,
	0x27, 0xa1, 0x9a, 0x3b, 0x09, 0x77, 0x01, 0x88, 0x21, 0xe4, 0x69, 0xc0, 0xba, 0x35, 0xea, 0xdc,
	0x40, 0x4a, 0x1f, 0x09, 0xc8, 0x16, 0x4c, 0xa0, 0x49, 0x60, 0xd7, 0xba, 0x62, 0x6b, 0x4a, 0xcf,
	0x37, 0x37, 0xa0, 0xe5, 0xbb, 0xa7, 0xce, 0x74, 0x26, 0x46, 0x4e, 0x38, 0x9b, 0x74, 0x1b, 0x4a,
	0xc1, 0xbe, 0x7b, 0xba, 0x3f, 0x13, 0xa3, 0xbd, 0xd9, 0x04, 0x2d, 0x60, 0x26, 0x58, 0xec, 0xe8,
	0x33, 0x01, 0x1b, 0x85, 0xcd, 0x8a, 0x0d, 0x48, 0xd2, 0x9e, 0xe1, 0x9e, 0x3e, 0x8a, 0xb8, 0x01,
	0xc9, 0x42, 0xd9, 0x6d, 0x6e, 0x94, 0x36, 0x1b, 0xfa, 0x50, 0xed, 0x28, 0x9a, 0xb9, 0x06, 0xd5,
	0xc3, 0x99, 0xc0, 0x25, 0xb4, 0x68, 0x09, 0x95, 0xc3, 0x99, 0x50, 0xbb, 0xe2, 0xc2, 0x91, 0x4c,
	0xc8, 0x6e, 0x7b, 0xa3, 0xb0, 0x59, 0xb7, 0xab, 0x5c, 0x0c, 0x98, 0x90, 0xb4, 0x2b, 0x36, 0x0d,
	0x4e, 0x95, 0xd9, 0x75, 0x94, 0xae, 0x88, 0x82, 0x72, 0xb6, 0x7e, 0xbf, 0xa0, 0x4d, 0xca, 0x80,
	0x16, 0x59, 0x44, 0x2f, 0x3c, 0x76, 0x03, 0xee, 0x1b, 0xd7, 0xcc, 0x75, 0x30, 0x89, 0xf2, 0x84,
	0xc7, 0x42, 0x26, 0xda, 0x31, 0x0a, 0xe6, 0x4d, 0xb8, 0x4e, 0xf4, 0x3e, 0xf3, 0xa2, 0xd0, 0x4f,
	0x19, 0x45, 0xb4, 0x4a, 0x62, 0x7c, 0xce, 0xd8, 0x98, 0x65, 0x38, 0x25, 0x73, 0x05, 0x9a, 0xc4,
	0xd9, 0x89, 0x26, 0x93, 0x28, 0x34, 0xca, 0x66, 0x1b, 0x1a, 0xca, 0x38, 0xa3, 0x80, 0x19, 0x15,
	0xeb, 0x1f, 0x8b, 0xd0, 0xda, 0xdd, 0x77, 0x0e, 0x04, 0x8b, 0x6d, 0x5c, 0xda, 0xd7, 0xb7, 0xb9,
	0x1f, 0xc1, 0x8a, 0xeb, 0xc9, 0x99, 0x1b, 0x38, 0x13, 0xa1, 0x1d, 0x99, 0x32, 0xbf, 0x7b, 0x17,
	0x9a, 0x9f, 0x50, 0x7e, 0xab, 0xad, 0xfa, 0xea, 0x4f, 0xd4, 0x14, 0x8e, 0x92, 0xa8, 0xa1, 0x4c,
	0x82, 0x86, 0x89, 0x18, 0x26, 0x4a, 0x30, 0xa0, 0xc4, 0x4e, 0x24, 0x19, 0x58, 0xcb, 0xc6, 0x9f,
	0xe6, 0xab, 0xd0, 0x94, 0x72, 0x3e, 0xb7, 0xb2, 0xac, 0x86, 0x94, 0xc9, 0x90, 0x6b, 0x50, 0x45,
	0x26, 0xf7, 0xb5, 0x61, 0x55, 0x26, 0x62, 0xa8, 0xd4, 0x26, 0xd0, 0x27, 0xb8, 0x92, 0x2c, 0xaa,
	0x64, 0x57, 0xf1, 0x73, 0x5b, 0x9a, 0xb7, 0xa0, 0x2e, 0x63, 0x97, 0x9c, 0x01, 0x99, 0x52, 0xc3,
	0xae, 0xd1, 0xb7, 0xb2, 0x34, 0x97, 0x3b, 0x64, 0xaa, 0x34, 0x17, 0x28, 0x4b, 0x73, 0x39, 0x0a,
	0x93, 0x94, 0xfa, 0x57, 0x25, 0x58, 0x25, 0x1f, 0x1d, 0xcd, 0xa6, 0x17, 0xc9, 0xf4, 0x3e, 0xac,
	0x0e, 0xb1, 0x8d, 0x23, 0xd9, 0x64, 0x1a, 0xb8, 0x92, 0xcd, 0x45, 0xbb, 0x42, 0x8c, 0x81, 0xa6,
	0xf7, 0x32, 0x6d, 0x79, 0x28, 0xa4, 0x1b, 0x7a, 0x99, 0x50, 0xa2, 0xda, 0xf6, 0x34, 0x7d, 0xb9,
	0x32, 0xca, 0xdf, 0x96, 0x32, 0x2a, 0xe7, 0x29, 0xa3, 0x7a, 0xae, 0x32, 0x6a, 0x4b, 0x94, 0x21,
	0xd8, 0x17, 0xc9, 0x31, 0x6e, 0xdb, 0x15, 0xc1, 0xbe, 0xc8, 0x2b, 0xa3, 0x71, 0xae, 0x32, 0x20,
	0xaf, 0x0c, 0x0b, 0xda, 0x2e, 0x77, 0xb4, 0x14, 0x71, 0xb2, 0x26, 0x8d, 0xd8, 0x74, 0x39, 0x49,
	0x9f, 0xa6, 0x7b, 0x0b, 0x56, 0xa4, 0x1b, 0x0f, 0x99, 0x74, 0xb4, 0xe3, 0x11, 0xdd, 0xd6, 0x46,
	0x69, 0xb3, 0x6d, 0xb7, 0x15, 0xd9, 0x26, 0xff, 0x23, 0xac, 0xdf, 0xa6, 0x68, 0xa6, 0x81, 0xc4,
	0x5e, 0x24, 0xf9, 0xd1, 0x15, 0x0e, 0x42, 0x17, 0x6a, 0xd9, 0x58, 0xd6, 0xb0, 0x93, 0x4f, 0x8b,
	0xd1, 0xf0, 0x36, 0x13, 0x4c, 0x3e, 0x0e, 0xf8, 0xef, 0xba, 0x3b, 0xf2, 0xe4, 0xeb, 0x0f, 0xbf,
	0x01, 0x2d, 0x86, 0x9d, 0x1d, 0x4f, 0x9e, 0xcc, 0x2d, 0x00, 0x98, 0x1e, 0xb0, 0xe7, 0x5b, 0x0f,
	0xa0, 0xb6, 0xbb, 0xef, 0x0c, 0xb8, 0x37, 0x36, 0x3b, 0x50, 0x94, 0x82, 0x06, 0x2f, 0xd9, 0x45,
	0x29, 0x4c, 0x13, 0xca, 0xa1, 0x3b, 0x61, 0x34, 0x6a, 0xc3, 0xa6, 0xdf, 0xd6, 0xdf, 0x75, 0x68,
	0x59, 0x8f, 0x58, 0xe0, 0x9e, 0xee, 0x8a, 0x21, 0x85, 0xa6, 0xaf, 0xbf, 0xac, 0xbc, 0x73, 0x56,
	0x8b, 0xca, 0x38, 0xe7, 0x5b, 0x50, 0x57, 0xab, 0xe6, 0x3e, 0x59, 0x62, 0xdb, 0xae, 0xd1, 0x77,
	0x3e, 0x1c, 0x54, 0x2e, 0x08, 0x07, 0xd5, 0xc5, 0x70, 0xf0, 0x31, 0xd4, 0xc9, 0x9b, 0x4f, 0xc4,
	0x90, 0x0c, 0xac, 0x79, 0xb1, 0x71, 0x63, 0x98, 0xda, 0x15, 0x43, 0xbb, 0x86, 0x9d, 0x76, 0xc5,
	0x50, 0xf9, 0x65, 0x8f, 0xf1, 0x63, 0xe6, 0x48, 0xa1, 0x0f, 0x7f, 0x43, 0x53, 0x06, 0x02, 0x4d,
	0x98, 0x02, 0x9e, 0x76, 0x1a, 0x8d, 0x24, 0xdc, 0xd0, 0x28, 0x3d, 0xdf, 0x7c, 0x03, 0x3a, 0x29,
	0x3f, 0x8a, 0x7d, 0x16, 0x6b, 0x37, 0xd0, 0xd2, 0x4d, 0x3e, 0x43, 0x9a, 0xf9, 0x43, 0x68, 0x24,
	0xad, 0x04, 0x45, 0x93, 0x97, 0x5c, 0x65, 0x5d, 0x8f, 0x22, 0x16, 0xc2, 0x47, 0x6b, 0x21, 0x7c,
	0x90, 0xdc, 0x3d, 0x16, 0x6a, 0x4f, 0xd4, 0xd6, 0x72, 0x47, 0x0a, 0xb1, 0xdf, 0x06, 0x43, 0xf5,
	0xc6, 0x65, 0x4e, 0x03, 0x37, 0x74, 0x0e, 0x75, 0x08, 0x6a, 0x13, 0x7d, 0x57, 0x0c, 0xf7, 0x03,
	0x37, 0xfc, 0x04, 0xbd, 0x0b, 0x3b, 0xf1, 0xd8, 0x34, 0x0d, 0xf4, 0x28, 0xd6, 0x15, 0xda, 0xf4,
	0x4a, 0xc2, 0xd0, 0x8b, 0xc3, 0x39, 0x27, 0x91, 0xcf, 0x02, 0x67, 0x26, 0x98, 0xdf, 0x35, 0x94,
	0x64, 0x88, 0x72, 0x20, 0x98, 0x6f, 0xbe, 0x07, 0xa6, 0x1b, 0x0c, 0x23, 0xc7, 0x67, 0x52, 0x01,
	0x07, 0x1e, 0x1e, 0x45, 0xdd, 0x55, 0x6a, 0x66, 0x20, 0xe7, 0x91, 0x66, 0xf4, 0xc2, 0xa3, 0xc8,
	0xbc, 0x01, 0x95, 0x98, 0xc9, 0xf8, 0xb4, 0x6b, 0x52, 0xd4, 0x54, 0x1f, 0xb4, 0x2d, 0xf7, 0x98,
	0x39, 0xb4, 0xc8, 0xee, 0x75, 0x62, 0x35, 0x90, 0xa2, 0x3c, 0xe9, 0x5b, 0xb0, 0x42, 0xba, 0x3f,
	0x8a, 0xe2, 0x89, 0x4b, 0x8e, 0xa6, 0x7b, 0x83, 0xc6, 0x6f, 0x23, 0xf9, 0x09, 0x51, 0x71, 0xa5,
	0x08, 0x3c, 0x31, 0x9e, 0xfb, 0xee, 0xa9, 0xe8, 0xae, 0x69, 0xe0, 0xc9, 0x98, 0x7c, 0xe4, 0x9e,
	0x0a, 0x73, 0x03, 0x9a, 0xd3, 0xc0, 0xf5, 0xd8, 0x28, 0x0a, 0x50, 0x7d, 0xeb, 0x0a, 0xed, 0x65,
	0x48, 0x4a, 0xc7, 0x89, 0x51, 0x3b, 0x42, 0xc6, 0xdd, 0x9b, 0xd4, 0xa8, 0x95, 0x1a, 0x76, 0x5f,
	0xc6, 0xe6, 0x23, 0x00, 0x21, 0xa3, 0xf8, 0x54, 0xed, 0xb3, 0x4b, 0xa6, 0xf8, 0xe6, 0x85, 0x4a,
	0xc6, 0xd6, 0xb8, 0x79, 0xbb, 0x21, 0x92, 0x9f, 0xe6, 0x43, 0x58, 0x8b, 0x62, 0x3e, 0xe4, 0xa1,
	0x1b, 0x38, 0xa9, 0x26, 0x70, 0x63, 0xb7, 0x68, 0xca, 0xeb, 0x09, 0xf3, 0xb1, 0xe6, 0xe1, 0xf6,
	0x36, 0xc1, 0x20, 0x31, 0x48, 0x76, 0x22, 0x9d, 0xc0, 0x3d, 0x64, 0x81, 0xe8, 0xde, 0x26, 0xc8,
	0xd2, 0x41, 0xfa, 0x80, 0x9d, 0xc8, 0x67, 0x44, 0x35, 0xdf, 0x81, 0xd5, 0x30, 0xc2, 0x65, 0x08,
	0xe6, 0x0c, 0xa7, 0x92, 0x7a, 0x74, 0xef, 0x28, 0x78, 0x16, 0x46, 0xfb, 0x48, 0x7f, 0x3a, 0x95,
	0xd8, 0x81, 0x70, 0xb2, 0x4e, 0x89, 0xd0, 0xf0, 0x5f, 0x51, 0xee, 0x3e, 0x21, 0xf5, 0x08, 0xcd,
	0x4f, 0xe3, 0x68, 0x32, 0x25, 0x34, 0x7f, 0x97, 0xd8, 0x75, 0x45, 0xe8, 0xf9, 0xe6, 0x9b, 0xd0,
	0xd1, 0xcc, 0xc4, 0x09, 0xbe, 0xaa, 0x14, 0xa3, 0xa8, 0x09, 0xa6, 0x7f, 0x1d, 0x5a, 0xa4, 0x68,
	0x54, 0x70, 0x14, 0xcb, 0xee, 0x6b, 0x4a, 0xf8, 0x44, 0xb3, 0x89, 0x84, 0xd3, 0x78, 0xd1, 0x84,
	0x39, 0x47, 0x71, 0x34, 0xe9, 0x6e, 0xa8, 0x69, 0x90, 0xf0, 0x24, 0x8e, 0x26, 0xe6, 0x77, 0x61,
	0xcd, 0xe7, 0xc2, 0x3d, 0x0c, 0x98, 0x33, 0x24, 0xbc, 0x96, 0x34, 0x7c, 0x9d, 0x4c, 0xc5, 0xd4,
	0xcc, 0xa7, 0x08, 0xdb, 0x74, 0x97, 0x8f, 0xe0, 0xce, 0xa2, 0xb0, 0x1c, 0x65, 0xc6, 0xa4, 0x37,
	0x8b, 0x66, 0xb8, 0x99, 0x97, 0xdb, 0x2e, 0xf2, 0x49, 0x3d, 0x1f, 0x43, 0x9d, 0x1c, 0x2e, 0x36,
	0xbd, 0x77, 0xb9, 0xb7, 0x41, 0x4f, 0x8c, 0x0a, 0xae, 0x79, 0xea, 0x07, 0xee, 0x66, 0xe8, 0x4e,
	0xf4, 0x31, 0x7d, 0x43, 0xed, 0x06, 0x09, 0x74, 0x4a, 0xdf, 0x81, 0xd5, 0xe3, 0x08, 0x13, 0x0f,
	0x94, 0x72, 0xe2, 0xd8, 0xdf, 0x54, 0xda, 0x21, 0x06, 0xe6, 0xa8, 0xe4, 0xdc, 0xcd, 0xef, 0x43,
	0x45, 0x72, 0x6f, 0x2c, 0xba, 0x6f, 0x5d, 0xee, 0x4c, 0x74, 0x14, 0xb0, 0x55, 0x8f, 0x5c, 0x10,
	0x7d, 0xfb, 0x62, 0x44, 0xb3, 0xb9, 0x88, 0x68, 0x70, 0x89, 0x5c, 0xe0, 0x81, 0x77, 0xd8, 0x89,
	0x8c, 0x5d, 0x5a, 0x69, 0xf7, 0x1d, 0x12, 0x76, 0x87, 0x8b, 0x03, 0xc1, 0x1e, 0x23, 0x19, 0xd7,
	0x69, 0xfe, 0x00, 0xee, 0x04, 0x98, 0xe4, 0x28, 0x28, 0xae, 0x61, 0x83, 0xc3, 0x31, 0x73, 0x3c,
	0x76, 0x83, 0xee, 0x7d, 0xf2, 0xb4, 0x5d, 0x6a, 0x82, 0xc8, 0x5c, 0xa3, 0x88, 0x9e, 0xe6, 0xe3,
	0xa1, 0x4b, 0xfc, 0xba, 0xe3, 0x45, 0xb3, 0x50, 0x76, 0xdf, 0x55, 0x8e, 0x55, 0x3b, 0xee, 0x1d,
	0xa4, 0x99, 0xdf, 0x87, 0x5b, 0xe9, 0x71, 0x51, 0x1e, 0xee, 0x45, 0x14, 0xfb, 0xba, 0xc3, 0x7b,
	0xd4, 0x61, 0x3d, 0x69, 0x40, 0x3e, 0xe3, 0xf3, 0x28, 0xf6, 0x55, 0xd7, 0x7b, 0xd0, 0x16, 0x6c,
	0x38, 0xc1, 0x45, 0xa9, 0xe6, 0x0f, 0x12, 0xc7, 0x4d, 0x44, 0xd5, 0x68, 0x13, 0x0c, 0xa9, 0x92,
	0xff, 0x39, 0x8c, 0xd9, 0xa2, 0x76, 0x1d, 0x4d, 0xd7, 0x58, 0xc6, 0xfa, 0xaa, 0x08, 0x8d, 0xf4,
	0x44, 0xa3, 0x90, 0xb5, 0x33, 0x48, 0xa2, 0x67, 0x4d, 0x9d, 0x71, 0x8a, 0x81, 0xca, 0x55, 0xeb,
	0xf8, 0xd9, 0xb0, 0x6b, 0xf4, 0xad, 0x8e, 0x94, 0xcf, 0xdd, 0x20, 0x1a, 0x26, 0xc1, 0xb3, 0x61,
	0xd7, 0x15, 0x41, 0xc5, 0xc1, 0x54, 0x88, 0xbe, 0xc6, 0xc2, 0x0d, 0x4d, 0x51, 0xba, 0x8b, 0x02,
	0xdf, 0x49, 0x87, 0xd6, 0xf8, 0x2c, 0x0a, 0xfc, 0xbe, 0x1e, 0xdd, 0x82, 0x36, 0xb6, 0x98, 0xcf,
	0xa0, 0x13, 0xdb, 0x28, 0xf0, 0x1f, 0x25, 0x93, 0xbc, 0x01, 0x1d, 0x6c, 0x93, 0x99, 0x48, 0xc1,
	0x64, 0x1c, 0x7b, 0x27, 0x3b, 0x57, 0xb2, 0x3b, 0x72, 0x87, 0x2a, 0x09, 0x03, 0xbd, 0x43, 0x74,
	0x86, 0xb8, 0xd8, 0x91, 0x3b, 0x95, 0x0a, 0x26, 0xe8, 0xa8, 0xa9, 0x29, 0x6a, 0x80, 0x70, 0xea,
	0x39, 0xca, 0xda, 0x53, 0x30, 0x07, 0xe1, 0xd4, 0x7b, 0x1e, 0x51, 0x92, 0x8d, 0x81, 0x28, 0x74,
	0xe3, 0xd8, 0x95, 0x51, 0x3c, 0x6f, 0xd6, 0x54, 0x81, 0x28, 0x61, 0xe8, 0xb6, 0xd6, 0xdf, 0x57,
	0xa0, 0x96, 0x04, 0xa5, 0x2e, 0xd4, 0x12, 0x84, 0x5a, 0x50, 0xc2, 0xd5, 0x9f, 0xe8, 0x6b, 0x92,
	0x6d, 0x91, 0x1a, 0x15, 0x76, 0x69, 0x6a, 0x1a, 0x59, 0xf7, 0xc7, 0x50, 0xbf, 0x4a, 0xd6, 0x52,
	0x9b, 0x68, 0x3c, 0xab, 0x11, 0x70, 0x79, 0x8e, 0x80, 0xbf, 0x03, 0x37, 0xa2, 0xa3, 0xa3, 0x80,
	0x87, 0x2c, 0x77, 0x0c, 0xb4, 0x76, 0x4c, 0xcd, 0xcb, 0xd8, 0x3f, 0x9a, 0x07, 0x17, 0x8e, 0x98,
	0x06, 0x5c, 0x41, 0xe9, 0xba, 0x5d, 0xe3, 0xa2, 0x8f, 0x9f, 0x0b, 0x18, 0xa0, 0xb6, 0x88, 0x01,
	0x96, 0x05, 0xf9, 0xfa, 0x4b, 0x07, 0xf9, 0xc6, 0xf2, 0x20, 0xbf, 0x3c, 0x8a, 0xc3, 0x39, 0x51,
	0xfc, 0x2d, 0x58, 0xa1, 0x78, 0xed, 0x0a, 0x67, 0xc4, 0xc9, 0x1c, 0x48, 0x67, 0x75, 0xbb, 0x8d,
	0xe4, 0x6d, 0xf1, 0xa9, 0x22, 0xa2, 0xa0, 0xa4, 0x14, 0x04, 0x63, 0xea, 0x36, 0xfe, 0xa4, 0x54,
	0x60, 0x3a, 0x46, 0x25, 0xb7, 0x29, 0x1f, 0xaf, 0x88, 0xe9, 0xb8, 0xe7, 0xab, 0x0c, 0x42, 0x24,
	0x78, 0x3d, 0x49, 0x9b, 0xa5, 0x14, 0x0a, 0xab, 0xe3, 0x89, 0x41, 0x3e, 0x39, 0x70, 0x8d, 0x53,
	0xea, 0x52, 0x2a, 0x87, 0x8d, 0x56, 0x86, 0xcc, 0xd4, 0x7c, 0x14, 0x44, 0x01, 0x29, 0x45, 0x62,
	0x65, 0xf7, 0xa0, 0xed, 0x23, 0xe0, 0x75, 0x04, 0xe5, 0xd0, 0x82, 0xe0, 0x49, 0xc9, 0x6e, 0x11,
	0x51, 0xe5, 0xd5, 0x02, 0xc5, 0xe5, 0xcd, 0x84, 0x8c, 0x26, 0x7a, 0xa4, 0x9f, 0x88, 0x28, 0x24,
	0x98, 0xd2, 0xb0, 0x57, 0x14, 0x83, 0x86, 0xfb, 0x35, 0x11, 0x85, 0xb8, 0x5e, 0x2e, 0x9c, 0x30,
	0x72, 0xdc, 0x99, 0xcf, 0x65, 0x82, 0x58, 0xb8, 0xd8, 0x8b, 0xb6, 0x91, 0x80, 0x2a, 0x24, 0x8e,
	0x8a, 0xbc, 0x0a, 0xac, 0x34, 0x88, 0x82, 0xd1, 0xc6, 0xfa, 0xa3, 0x02, 0xac, 0x3d, 0x3e, 0x91,
	0x0e, 0x39, 0x92, 0x4f, 0xa2, 0x68, 0xfc, 0x38, 0x94, 0x31, 0x26, 0x73, 0x78, 0x78, 0xd5, 0x91,
	0x3b, 0x8c, 0xa2, 0x71, 0xe2, 0x55, 0x1a, 0x76, 0x53, 0x24, 0x2d, 0x7b, 0x3e, 0x62, 0x28, 0xc9,
	0x65, 0x90, 0xe0, 0x7a, 0xf5, 0x61, 0xde, 0x86, 0xba, 0x98, 0x1d, 0x2a, 0x86, 0xf6, 0x29, 0xc9,
	0x37, 0x05, 0xf9, 0xe8, 0x18, 0x0f, 0xe9, 0xc4, 0x1d, 0xb2, 0x24, 0xc1, 0x26, 0x52, 0x0f, 0x29,
	0xd6, 0x73, 0xa8, 0xe9, 0x18, 0x86, 0x1a, 0xd2, 0x21, 0x49, 0x4d, 0x5d, 0xf1, 0x28, 0x12, 0xdd,
	0x84, 0x1a, 0x45, 0xb4, 0xd4, 0x9b, 0x55, 0xf1, 0x53, 0xa9, 0x06, 0xdb, 0x93, 0x43, 0xd2, 0x99,
	0x00, 0xc6, 0x4e, 0xf2, 0x46, 0xd6, 0x97, 0x05, 0x80, 0x83, 0x90, 0x23, 0x70, 0x43, 0x2b, 0xfb,
	0x25, 0x7d, 0x53, 0x55, 0x78, 0xf9, 0x43, 0xa7, 0x2e, 0xa6, 0x32, 0xc7, 0xbd, 0x98, 0x3f, 0xee,
	0xfa, 0x2c, 0x96, 0xe6, 0x67, 0x31, 0x1b, 0xbb, 0xcb, 0x5f, 0x3f, 0x76, 0x5b, 0x3f, 0x86, 0x15,
	0xd4, 0xcd, 0x76, 0xc8, 0x27, 0xae, 0x64, 0xbb, 0x6c, 0xc2, 0xd0, 0xc2, 0xb8, 0x70, 0xdc, 0x99,
	0x8c, 0x1c, 0x31, 0x8a, 0x5e, 0xd0, 0xfa, 0xeb, 0x36, 0x70, 0xb1, 0x3d, 0x93, 0x51, 0x7f, 0x14,
	0xbd, 0xc0, 0x13, 0xc1, 0x85, 0x73, 0xc4, 0x63, 0x21, 0x9d, 0x59, 0x18, 0x44, 0xde, 0x98, 0x16,
	0x5a, 0xb7, 0xdb, 0x5c, 0xd0, 0x85, 0xce, 0x01, 0x11, 0xad, 0x0f, 0xa1, 0x63, 0x73, 0x6f, 0x84,
	0x56, 0xa0, 0x70, 0x2b, 0x26, 0x69, 0x64, 0x24, 0x4a, 0xda, 0xf4, 0x1b, 0x35, 0xac, 0xd2, 0x20,
	0xad, 0x61, 0xfa, 0xb0, 0xf6, 0xa1, 0x85, 0x0b, 0x4b, 0xfa, 0x9b, 0x3f, 0x84, 0x0a, 0xb6, 0xc6,
	0x8c, 0x0f, 0xc1, 0xc1, 0xfd, 0x8b, 0x76, 0x99, 0x9f, 0xd4, 0x56, 0x1d, 0xad, 0xe7, 0xb0, 0x4e,
	0x23, 0xb2, 0xc0, 0x95, 0x3c, 0x0a, 0xc5, 0x88, 0x4f, 0x9f, 0x31, 0x29, 0x59, 0x4c, 0xa9, 0x64,
	0xa4, 0xd7, 0x54, 0x94, 0x11, 0xae, 0x92, 0x00, 0x97, 0x4e, 0x25, 0xf1, 0x77, 0x56, 0x29, 0xa5,
	0x9c, 0x52, 0xac, 0x67, 0x00, 0x38, 0xee, 0x60, 0xd0, 0x47, 0xad, 0x63, 0x76, 0x19, 0x07, 0x7a,
	0x30, 0xfc, 0x89, 0x3d, 0x93, 0x93, 0x58, 0xd4, 0x51, 0x53, 0x1f, 0xc2, 0x44, 0x1a, 0xa5, 0xb9,
	0x34, 0xac, 0x3f, 0x2c, 0xc0, 0x2a, 0x0e, 0xb7, 0xc7, 0x5e, 0xec, 0x8f, 0x22, 0x19, 0x0d, 0x63,
	0x77, 0x3a, 0xc2, 0x15, 0xa6, 0x41, 0xb7, 0xc8, 0x7d, 0x73, 0x1d, 0xaa, 0xee, 0xb1, 0x2b, 0xdd,
	0x38, 0xb1, 0x4f, 0xf5, 0x85, 0xb2, 0x54, 0x56, 0xaf, 0x86, 0x54, 0x1f, 0x34, 0x4f, 0x72, 0x4f,
	0xd2, 0xd6, 0x46, 0x66, 0x41, 0x9b, 0x98, 0x98, 0x67, 0x90, 0xaf, 0x54, 0xde, 0xbb, 0x49, 0xc4,
	0x27, 0x11, 0x42, 0x00, 0xeb, 0x67, 0x7a, 0x2d, 0x07, 0x82, 0xc5, 0xa8, 0x7c, 0x95, 0xa0, 0x7c,
	0x63, 0x4d, 0x2c, 0x05, 0x20, 0xc5, 0x65, 0x00, 0xc4, 0xfc, 0x2d, 0xe8, 0x26, 0x2d, 0x67, 0xea,
	0x64, 0x5d, 0xe9, 0x0a, 0x6e, 0x4d, 0x0f, 0x32, 0x3f, 0x9d, 0x04, 0x6f, 0x7e, 0x07, 0x4c, 0xdc,
	0xde, 0xf3, 0x04, 0x86, 0xaa, 0xfd, 0x2d, 0x45, 0xac, 0x85, 0xa5, 0x88, 0xf5, 0x2e, 0x00, 0x0f,
	0xa7, 0x33, 0xe9, 0xa4, 0xe6, 0x52, 0xb1, 0x1b, 0x44, 0x41, 0x58, 0x6e, 0xfd, 0x79, 0x11, 0x6a,
	0xbd, 0xdd, 0x2b, 0xde, 0x3a, 0x3c, 0x82, 0x2a, 0xc6, 0x31, 0x16, 0xeb, 0x8d, 0xbe, 0x77, 0xd1,
	0x46, 0xf5, 0x2c, 0x5b, 0x09, 0xa6, 0xb5, 0x75, 0x5f, 0x7d, 0xed, 0xe4, 0x3b, 0x52, 0xe8, 0xbb,
	0x6d, 0x62, 0x0c, 0xc4, 0xe5, 0x37, 0x5f, 0xb7, 0x32, 0xb8, 0x41, 0xdd, 0x38, 0x2e, 0x42, 0x82,
	0x5a, 0xea, 0x86, 0xac, 0x0f, 0xa0, 0x9e, 0xc2, 0xe9, 0x55, 0x68, 0x27, 0xbf, 0xc9, 0x72, 0x8c,
	0x6b, 0xe6, 0x0d, 0x30, 0x52, 0xd2, 0xbe, 0x5a, 0xb8, 0x51, 0xb0, 0xfe, 0xa6, 0x08, 0xad, 0x3d,
	0x65, 0x54, 0x57, 0x94, 0xd1, 0x03, 0xb8, 0x1e, 0xb8, 0xe8, 0x84, 0xf2, 0xa0, 0x5a, 0x39, 0x66,
	0x03, 0x59, 0x07, 0x59, 0x60, 0xbd, 0x9d, 0xab, 0xb6, 0x3c, 0xb8, 0x48, 0xa0, 0xd9, 0x75, 0x6d,
	0xe5, 0x7d, 0x73, 0x92, 0xfc, 0x55, 0xf2, 0x37, 0x60, 0xe3, 0x73, 0xef, 0xba, 0x13, 0x0a, 0x89,
	0x80, 0x4f, 0x8d, 0x42, 0x4a, 0xe9, 0x33, 0xb9, 0xe7, 0x4e, 0x98, 0x51, 0x44, 0x09, 0xa5, 0x14,
	0xee, 0x8d, 0x43, 0xa4, 0x96, 0x32, 0xb7, 0xe1, 0x52, 0x8b, 0x0d, 0x9d, 0xb6, 0x51, 0xb6, 0xfe,
	0x42, 0x9f, 0x4c, 0x8a, 0xa9, 0x69, 0x3c, 0xbd, 0x00, 0xa0, 0x3f, 0x85, 0x46, 0xcc, 0xbd, 0x91,
	0x0a, 0xd1, 0xc5, 0xaf, 0x7d, 0x70, 0xeb, 0x71, 0xe2, 0x87, 0x35, 0x98, 0x4e, 0x4e, 0xa5, 0x14,
	0x24, 0xed, 0x12, 0x81, 0x69, 0x5d, 0x52, 0x1c, 0x08, 0xeb, 0xaf, 0x8b, 0x2a, 0xae, 0xd0, 0xfa,
	0xd4, 0xc5, 0x7c, 0x3e, 0x11, 0x28, 0x2c, 0x24, 0x02, 0xd9, 0xa5, 0x17, 0xf3, 0x4b, 0x57, 0x49,
	0x93, 0x13, 0x32, 0xe6, 0xeb, 0x3b, 0x93, 0x12, 0x05, 0x1b, 0x4c, 0x9a, 0xf6, 0x18, 0xf3, 0xd5,
	0xa9, 0xcd, 0x83, 0xf3, 0xf2, 0x12, 0x70, 0x9e, 0x43, 0xf7, 0x95, 0x33, 0xe8, 0x3e, 0xa3, 0xd9,
	0x6a, 0x4e, 0xb3, 0xe9, 0xcd, 0x62, 0x6d, 0x7e, 0xb3, 0x98, 0x71, 0xc0, 0xf5, 0x9c, 0x03, 0xd6,
	0xee, 0xbf, 0xb1, 0xd4, 0xfd, 0xc3, 0x72, 0xf7, 0xdf, 0xcc, 0xb8, 0xff, 0xff, 0x2e, 0x2a, 0x9f,
	0x44, 0x82, 0xdb, 0xa3, 0x9c, 0x00, 0x97, 0xf0, 0xcd, 0x7d, 0x6e, 0x4e, 0xfa, 0xc5, 0x0b, 0xa4,
	0x5f, 0xba, 0x4c, 0xfa, 0xe5, 0x4b, 0xa5, 0x5f, 0xb9, 0x4c, 0xfa, 0xd5, 0x8b, 0xa4, 0x5f, 0x5b,
	0x2e, 0xfd, 0xfa, 0x52, 0xe9, 0x37, 0x96, 0x49, 0x1f, 0x96, 0x4a, 0xbf, 0xb9, 0x5c, 0xfa, 0xad,
	0x8c, 0xf4, 0xff, 0xb3, 0x90, 0x31, 0xdb, 0x27, 0x3c, 0xe4, 0x62, 0x74, 0xd1, 0xa1, 0x4a, 0x86,
	0x28, 0x66, 0xd0, 0xcc, 0x7d, 0x58, 0x55, 0xcd, 0xbd, 0xc0, 0x15, 0x82, 0x1f, 0x65, 0x64, 0xba,
	0x42, 0x8c, 0x1d, 0x4d, 0xd7, 0xd9, 0xef, 0xff, 0xbf, 0xcd, 0x5a, 0x5f, 0x15, 0xa0, 0x9b, 0xee,
	0xfa, 0xb3, 0x29, 0x1a, 0xce, 0xbc, 0xb0, 0x73, 0xd5, 0x53, 0x9b, 0xcf, 0xec, 0x4b, 0x8b, 0x99,
	0xfd, 0xcf, 0xc9, 0xd6, 0x7f, 0xaa, 0x30, 0x21, 0xed, 0xdc, 0x66, 0x2f, 0xdc, 0xd8, 0x17, 0xba,
	0x36, 0xf2, 0x7f, 0xe0, 0x4b, 0xad, 0x7f, 0x9d, 0x63, 0xbd, 0x6d, 0x39, 0x89, 0xc4, 0x74, 0xc4,
	0x62, 0x96, 0xc1, 0x7a, 0x0d, 0xc2, 0x7a, 0x74, 0x9f, 0xa8, 0xea, 0xba, 0x0e, 0x5a, 0x77, 0x31,
	0xb9, 0x4f, 0x54, 0xb4, 0x83, 0x38, 0xc0, 0x94, 0x2f, 0x6d, 0x92, 0x62, 0xa3, 0x86, 0x9d, 0xf6,
	0xa3, 0xc0, 0xb4, 0x01, 0x4d, 0x9f, 0x09, 0x2f, 0xe6, 0x53, 0x74, 0x29, 0x5a, 0xe6, 0x59, 0x12,
	0xea, 0x1a, 0xd3, 0xb6, 0x88, 0xa6, 0x51, 0x22, 0xaf, 0x13, 0x01, 0xe7, 0xb8, 0x03, 0x0d, 0x31,
	0x71, 0x83, 0x80, 0x98, 0x55, 0x9d, 0x73, 0x21, 0x41, 0x33, 0x15, 0x9a, 0x44, 0xa6, 0x12, 0x7c,
	0x9d, 0x08, 0x07, 0x71, 0x60, 0xfd, 0x49, 0x01, 0x6e, 0xa4, 0x52, 0xce, 0x66, 0x1a, 0xff, 0xbb,
	0x11, 0x61, 0x31, 0x8f, 0x29, 0x2f, 0xe6, 0x31, 0xd6, 0x97, 0x45, 0x68, 0x2a, 0x47, 0x7b, 0xc5,
	0x7a, 0xd8, 0x05, 0x7e, 0xb3, 0x0f, 0xcd, 0x90, 0x46, 0xcd, 0x96, 0x28, 0x1f, 0x5e, 0x7a, 0x75,
	0xae, 0x56, 0xb2, 0xa5, 0xfe, 0x10, 0xee, 0x80, 0x30, 0xfd, 0x6d, 0xfd, 0x5e, 0x01, 0x60, 0xce,
	0x32, 0xd7, 0xc1, 0x9c, 0x7f, 0x65, 0x00, 0xc7, 0x0d, 0x30, 0x32, 0xf4, 0xbe, 0x74, 0x63, 0x69,
	0x14, 0x16, 0x5a, 0xdb, 0x4c, 0x10, 0xbd, 0x68, 0x5e, 0x87, 0x95, 0x0c, 0x7d, 0x8f, 0x9d, 0x48,
	0x85, 0x3c, 0x32, 0x44, 0xc4, 0x84, 0x3c, 0x9c, 0x31, 0xa3, 0x6c, 0xfd, 0x5b, 0x01, 0x5a, 0xfb,
	0x33, 0x31, 0x4a, 0x5e, 0x56, 0x2c, 0x11, 0xda, 0x0d, 0xa8, 0xa8, 0xaa, 0x91, 0x92, 0x97, 0xfa,
	0x48, 0xc1, 0x57, 0xe9, 0x72, 0xf0, 0x95, 0x1d, 0x3f, 0x0b, 0xbe, 0x32, 0xe5, 0xb4, 0xf2, 0x62,
	0x39, 0x2d, 0xa3, 0xa6, 0xca, 0x82, 0x9a, 0xac, 0x5f, 0x5c, 0x80, 0x66, 0x8f, 0xd8, 0x91, 0x3b,
	0x0b, 0xa4, 0x71, 0xcd, 0x7c, 0x05, 0xba, 0x44, 0xd9, 0x3e, 0x92, 0x2c, 0xd6, 0x10, 0x6b, 0x27,
	0x66, 0xae, 0x64, 0xbe, 0x51, 0xb0, 0x7e, 0x0a, 0xab, 0xca, 0x11, 0xba, 0xfe, 0xae, 0x18, 0x5e,
	0xd5, 0x48, 0xe6, 0xd5, 0xf9, 0x52, 0xb6, 0x3a, 0xff, 0x1a, 0x34, 0x75, 0x45, 0x2e, 0x83, 0xce,
	0x93, 0xb2, 0x9d, 0x3f, 0x10, 0xd6, 0x97, 0x05, 0xb8, 0x91, 0x4c, 0xff, 0xcd, 0x60, 0xf2, 0xab,
	0x38, 0x95, 0x9b, 0xd6, 0xfb, 0xb4, 0x33, 0x8e, 0xd5, 0xb6, 0x5e, 0x62, 0x29, 0xea, 0x16, 0x4e,
	0x0f, 0x40, 0x55, 0x6c, 0x29, 0x48, 0xca, 0x25, 0xbb, 0xad, 0x47, 0xe9, 0xb3, 0x50, 0x0e, 0x84,
	0xf5, 0x2f, 0x05, 0x58, 0xd7, 0x62, 0xec, 0x8f, 0xdc, 0x98, 0xf9, 0xb8, 0x81, 0xf3, 0x56, 0xfd,
	0x2a, 0x66, 0x4f, 0xc7, 0x5c, 0xb2, 0xf8, 0x20, 0x5d, 0x75, 0x86, 0xa2, 0x2b, 0xbc, 0xea, 0x5c,
	0x15, 0xa5, 0x38, 0xfb, 0x88, 0xa9, 0x7c, 0xe6, 0x11, 0xd3, 0xe5, 0x2f, 0xa1, 0x72, 0xef, 0xb5,
	0xaa, 0x0b, 0xef, 0xb5, 0x2c, 0x68, 0x7b, 0x01, 0x77, 0xd2, 0x59, 0xb4, 0x2b, 0xc3, 0x21, 0x1f,
	0xe9, 0x49, 0xac, 0x2f, 0x60, 0x1d, 0x81, 0x3c, 0x59, 0x49, 0x14, 0x2b, 0xa3, 0x38, 0x6f, 0x87,
	0x19, 0x83, 0x2d, 0xe6, 0x0c, 0xf6, 0x3d, 0x30, 0xa9, 0xea, 0xc4, 0x7c, 0x95, 0xbb, 0xe4, 0xf2,
	0x16, 0xcd, 0x41, 0xd1, 0x51, 0xde, 0x62, 0xfd, 0x73, 0x11, 0xcc, 0xcf, 0x47, 0x5c, 0xb2, 0x67,
	0x5c, 0x65, 0x34, 0xe7, 0xcd, 0x77, 0x46, 0x42, 0xc5, 0x4b, 0x25, 0x54, 0x3a, 0x23, 0xa1, 0x7b,
	0xe9, 0x20, 0x42, 0xba, 0x72, 0x26, 0xb4, 0x98, 0x5b, 0x8a, 0xd8, 0x27, 0x5a, 0x5e, 0x8c, 0x95,
	0xcb, 0xc4, 0x58, 0x3d, 0x23, 0x46, 0x73, 0x37, 0x7d, 0xbf, 0x57, 0x23, 0x87, 0xf0, 0xbd, 0x8b,
	0x1c, 0xc2, 0xd9, 0xcd, 0x2f, 0x3e, 0x81, 0x7c, 0x2f, 0x7d, 0x02, 0xb9, 0x02, 0x4d, 0xfd, 0xd0,
	0x71, 0xe7, 0xb3, 0xfe, 0xc0, 0xb8, 0x86, 0xf9, 0xa8, 0x26, 0xf4, 0x1f, 0x6f, 0xdb, 0x3b, 0x9f,
	0x1a, 0x05, 0xeb, 0x4f, 0x8b, 0xb0, 0x92, 0x5e, 0x47, 0x5e, 0xf5, 0x5c, 0x9f, 0xfb, 0xea, 0x31,
	0x1b, 0x15, 0xca, 0xba, 0x18, 0x92, 0x41, 0x45, 0x17, 0xe0, 0xe4, 0x47, 0xda, 0x47, 0x56, 0x49,
	0x24, 0xdf, 0xb9, 0x34, 0x5a, 0xcc, 0x97, 0x9f, 0x71, 0x93, 0xd6, 0xaf, 0x9c, 0x9b, 0x89, 0x76,
	0x00, 0x72, 0x21, 0x61, 0x15, 0xda, 0x79, 0xff, 0x5e, 0xb4, 0xfe, 0x2c, 0x9b, 0xb9, 0x7d, 0x3b,
	0x77, 0x5a, 0xf9, 0x38, 0x5f, 0x5e, 0x88, 0xf3, 0x97, 0x43, 0xbd, 0xb3, 0xe1, 0xbe, 0x7a, 0x69,
	0x0a, 0x52, 0x5b, 0x14, 0x6d, 0x06, 0x2f, 0xd6, 0x97, 0xe3, 0xc5, 0xc6, 0x52, 0xbc, 0x08, 0x39,
	0xbc, 0xf8, 0xef, 0x45, 0x68, 0xed, 0x33, 0x39, 0xe0, 0x53, 0x5d, 0xc9, 0xfd, 0xf6, 0xac, 0xe6,
	0x29, 0x54, 0x5d, 0x2f, 0x45, 0x66, 0x9d, 0x87, 0xef, 0x5f, 0x18, 0x21, 0x33, 0x6b, 0xd8, 0xda,
	0xa6, 0x6e, 0xb6, 0xee, 0x4e, 0x75, 0x08, 0x3e, 0xd5, 0x72, 0xc4, 0x9f, 0x99, 0x08, 0x54, 0x3d,
	0xe7, 0x7d, 0x58, 0x2d, 0xfb, 0x24, 0xc9, 0xfa, 0x83, 0x02, 0x54, 0xd5, 0xa0, 0xa6, 0x09, 0x1d,
	0xf5, 0x2b, 0x63, 0x45, 0x37, 0xe1, 0xba, 0xa6, 0xe9, 0x40, 0xba, 0x13, 0x70, 0x6f, 0x6c, 0x14,
	0xf0, 0xe8, 0x25, 0x8c, 0xd8, 0x1d, 0xaa, 0x5b, 0x0d, 0x4d, 0xd8, 0x67, 0x6c, 0xac, 0x9a, 0x95,
	0x32, 0x63, 0x22, 0x75, 0xfb, 0xc9, 0x8f, 0x8c, 0xb2, 0xb9, 0x06, 0xab, 0xf9, 0x31, 0x91, 0x5c,
	0xb1, 0xfe, 0xab, 0xa8, 0xae, 0x81, 0xf7, 0x99, 0x9c, 0xbf, 0x33, 0xfb, 0x86, 0xa6, 0x98, 0x56,
	0xca, 0x13, 0x53, 0x4c, 0xea, 0xe2, 0x67, 0xaa, 0x69, 0xfa, 0x4a, 0x34, 0x5b, 0x4d, 0xbb, 0x0b,
	0x70, 0x38, 0x3b, 0x3c, 0x0c, 0x18, 0xdd, 0x99, 0xea, 0x87, 0x3b, 0x8a, 0x82, 0x27, 0xe5, 0x35,
	0x68, 0xfa, 0x5c, 0x4c, 0x03, 0xf7, 0x34, 0x7d, 0xbb, 0xd3, 0xb0, 0x41, 0x93, 0xf4, 0xab, 0x0d,
	0xfd, 0x5c, 0x2b, 0x7d, 0xe7, 0x59, 0x57, 0x84, 0x1e, 0x21, 0x7d, 0xcd, 0x54, 0x95, 0x0d, 0x65,
	0x91, 0x4d, 0x45, 0x1b, 0x50, 0x71, 0xe3, 0xed, 0xf4, 0xb9, 0x57, 0x5a, 0xff, 0x50, 0x16, 0xda,
	0x51, 0xe4, 0x7e, 0xa6, 0x0a, 0x92, 0x4c, 0xe4, 0x45, 0xa1, 0xbe, 0x63, 0x00, 0x3d, 0x95, 0x17,
	0x85, 0xb8, 0x93, 0x91, 0x2b, 0x9c, 0x98, 0xb2, 0x1e, 0x5d, 0xb5, 0x6a, 0x8c, 0x5c, 0xa1, 0xd2,
	0x20, 0xeb, 0x2f, 0x0b, 0x60, 0xd0, 0x2b, 0x33, 0x65, 0x64, 0x54, 0x02, 0xc7, 0xd5, 0x93, 0xf1,
	0xd2, 0x79, 0xd1, 0x80, 0x1d, 0x09, 0x7b, 0x78, 0x66, 0x96, 0xc3, 0xbe, 0xa5, 0xc5, 0xf5, 0xd2,
	0xd2, 0xe2, 0x3a, 0x8a, 0x9f, 0xae, 0x5c, 0x51, 0xde, 0xa9, 0x7a, 0xe8, 0xc5, 0xc6, 0x8e, 0x22,
	0x2d, 0x3c, 0xcf, 0xa9, 0x2c, 0x3c, 0xcf, 0xb1, 0xfe, 0xa3, 0x0c, 0x6b, 0xc9, 0xdb, 0xc4, 0xfc,
	0xa3, 0xaf, 0x67, 0x40, 0x8f, 0x61, 0xb5, 0xff, 0x28, 0x50, 0xa5, 0xe4, 0xc1, 0xa5, 0xcf, 0xd0,
	0xb3, 0x4f, 0x1c, 0x6d, 0x32, 0x9c, 0xe4, 0x0a, 0x3c, 0xf3, 0xf4, 0xa9, 0x78, 0x95, 0xa7, 0x4f,
	0xe7, 0xba, 0x81, 0xbb, 0x00, 0xaa, 0x7a, 0x27, 0xf9, 0x84, 0x69, 0x28, 0xd6, 0x20, 0xca, 0x80,
	0x4f, 0x98, 0xf9, 0x26, 0x74, 0x54, 0xad, 0x4d, 0x1b, 0x25, 0xe2, 0xb0, 0xd2, 0x66, 0xc3, 0x6e,
	0x13, 0x55, 0xdf, 0xdd, 0x0a, 0xf3, 0x33, 0xcc, 0x19, 0x51, 0x6f, 0x4a, 0xea, 0x64, 0xa8, 0xcd,
	0x8b, 0xaf, 0x90, 0x17, 0x95, 0x8d, 0x19, 0xe6, 0x5c, 0xf3, 0x6f, 0x40, 0x47, 0xbd, 0x43, 0x4c,
	0x5f, 0x19, 0xd6, 0xe8, 0x95, 0x61, 0x8b, 0xa8, 0xfa, 0x91, 0xa1, 0xf9, 0x13, 0x58, 0xd1, 0x7c,
	0x47, 0x46, 0x8e, 0xbe, 0xb6, 0x41, 0xe9, 0xec, 0xbc, 0x8c, 0xa8, 0x73, 0x1a, 0xdb, 0x52, 0x63,
	0x0e, 0x22, 0xb4, 0xad, 0xc7, 0xa1, 0x8c, 0x4f, 0xed, 0x56, 0x9c, 0x21, 0x2d, 0xad, 0x0c, 0x37,
	0x96, 0x55, 0x86, 0xe9, 0xf9, 0x5e, 0xe8, 0xe7, 0x5e, 0xb2, 0x91, 0xfa, 0xe8, 0x19, 0xdb, 0xed,
	0x5f, 0x85, 0xd5, 0x33, 0x53, 0xa1, 0x9b, 0x19, 0xb3, 0xd3, 0xc4, 0xad, 0x8f, 0xd9, 0x29, 0x5a,
	0xf7, 0xb1, 0x1b, 0xcc, 0xd2, 0x2a, 0x15, 0x7d, 0x7c, 0x58, 0xfc, 0xe5, 0xc2, 0xfd, 0x63, 0x80,
	0x83, 0xf9, 0x3b, 0xeb, 0x75, 0x30, 0xe7, 0x5f, 0x99, 0xff, 0x36, 0xb8, 0x05, 0x6b, 0x19, 0x3a,
	0xda, 0x7b, 0x2f, 0xfc, 0xe0, 0x91, 0x7b, 0x6a, 0x14, 0xcc, 0xdb, 0xb0, 0x9e, 0x61, 0x3d, 0x65,
	0xf4, 0x18, 0x89, 0x6e, 0xce, 0x8b, 0x0b, 0xdd, 0xfa, 0x2c, 0x60, 0x9e, 0x44, 0x43, 0x30, 0x4a,
	0xf7, 0xff, 0xa9, 0x02, 0xb5, 0xa4, 0x4e, 0xd2, 0x84, 0xda, 0xdc, 0x3f, 0xd7, 0xa1, 0x8c, 0xee,
	0xcc, 0x28, 0xe0, 0x2f, 0x4c, 0xb4, 0x8d, 0x12, 0x36, 0xd0, 0x6f, 0x3f, 0x8d, 0x32, 0x7e, 0x6c,
	0xf3, 0x78, 0x80, 0xde, 0xb8, 0x62, 0x02, 0x54, 0xf5, 0xcb, 0xe9, 0x2a, 0x22, 0x06, 0x9d, 0x9f,
	0xfb, 0xd4, 0x0f, 0xcc, 0x16, 0xd4, 0x93, 0xeb, 0x0b, 0xa3, 0x89, 0x9b, 0x3b, 0x5b, 0x3e, 0x33,
	0x5a, 0x88, 0x23, 0x72, 0xf5, 0x2a, 0xa3, 0x8d, 0x50, 0x03, 0x01, 0x1b, 0x0b, 0xfb, 0x51, 0x38,
	0x34, 0x3a, 0xb4, 0x00, 0x7d, 0xdb, 0xbd, 0x82, 0x29, 0x67, 0x9f, 0x11, 0x98, 0x4b, 0x2f, 0xbb,
	0x0d, 0xec, 0xd1, 0x57, 0xef, 0x55, 0x07, 0x7c, 0x6a, 0xac, 0xe2, 0xa0, 0xb9, 0xc2, 0x93, 0x61,
	0x9a, 0x6d, 0x68, 0x20, 0xe9, 0xe9, 0x8c, 0xfb, 0xcc, 0xb8, 0x4e, 0xc8, 0x2e, 0x7b, 0x01, 0x6e,
	0xdc, 0xc0, 0xd8, 0x92, 0xbf, 0x3a, 0x35, 0xd6, 0xb0, 0x97, 0x42, 0x33, 0xb8, 0x89, 0x75, 0x9a,
	0x87, 0xc0, 0xc5, 0xc4, 0x1d, 0x32, 0xe3, 0x26, 0x01, 0xc6, 0xf9, 0x7d, 0x9f, 0xd1, 0xa5, 0x7f,
	0xa2, 0x59, 0x72, 0x15, 0x66, 0xdc, 0xc2, 0xfd, 0x9f, 0xbd, 0x2a, 0x32, 0x6e, 0xeb, 0xfd, 0xcf,
	0xef, 0x70, 0x8c, 0x3b, 0x18, 0xd0, 0x9e, 0xba, 0x13, 0xb6, 0x33, 0x72, 0x63, 0xd7, 0x93, 0x2c,
	0x1e, 0xf0, 0xa9, 0x30, 0x5e, 0x41, 0x69, 0x2b, 0x50, 0x65, 0xdc, 0xc5, 0x89, 0xd3, 0xdd, 0x3c,
	0x7f, 0x68, 0xbc, 0x8a, 0x7d, 0xb2, 0x77, 0x24, 0x4a, 0x07, 0xaf, 0xd1, 0x9e, 0xf2, 0x05, 0x80,
	0x0d, 0xdc, 0xc4, 0xaf, 0xcf, 0xb8, 0x37, 0x56, 0x2b, 0x7b, 0x3d, 0xed, 0x9a, 0xad, 0xaf, 0x1b,
	0x56, 0xba, 0x37, 0x3d, 0xe7, 0x3d, 0x1c, 0x2b, 0x5f, 0xee, 0x32, 0xde, 0x40, 0x1d, 0x27, 0xf1,
	0xd5, 0x78, 0x93, 0x82, 0x78, 0xcf, 0xf6, 0x26, 0x0a, 0x56, 0x19, 0x6f, 0xa1, 0x51, 0x24, 0xf8,
	0x10, 0x7b, 0x19, 0x6f, 0xe3, 0x64, 0x36, 0x73, 0xbd, 0x11, 0x7e, 0x3e, 0xe3, 0x13, 0x2e, 0x71,
	0xec, 0x4d, 0x6c, 0xb8, 0x8f, 0x7e, 0xca, 0x8d, 0x23, 0x44, 0x27, 0xc6, 0x3b, 0xd8, 0x70, 0x40,
	0x11, 0x67, 0x27, 0x9a, 0x4c, 0x03, 0x26, 0x31, 0x46, 0x1a, 0xf7, 0xa9, 0xd8, 0xa1, 0x62, 0x5a,
	0x5a, 0xed, 0x33, 0xde, 0xbd, 0xff, 0x11, 0xd4, 0x77, 0x74, 0x29, 0x1d, 0x5b, 0x24, 0xbf, 0x33,
	0x67, 0xe8, 0x36, 0xac, 0xa5, 0xd4, 0x7d, 0x26, 0xfb, 0x01, 0x63, 0xd3, 0x81, 0x1b, 0x8c, 0x8d,
	0xbf, 0xbd, 0xf9, 0xc9, 0xc7, 0xbf, 0xf9, 0xd1, 0x30, 0x0a, 0xdc, 0x70, 0xb8, 0xf5, 0xbd, 0x87,
	0x52, 0x6e, 0x79, 0xd1, 0xe4, 0x7d, 0xfa, 0xcf, 0x2e, 0x2f, 0x0a, 0xde, 0x17, 0x2c, 0xc6, 0xac,
	0x43, 0xd0, 0x7f, 0x80, 0x2d, 0xff, 0x37, 0xb0, 0xc3, 0x2a, 0xb5, 0xfe, 0xe0, 0x7f, 0x02, 0x00,
	0x00, 0xff, 0xff, 0xe7, 0x20, 0xd9, 0xe9, 0x36, 0x36, 0x00, 0x00,
}
