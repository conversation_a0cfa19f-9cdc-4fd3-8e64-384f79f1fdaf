// Code generated by protoc-gen-go. DO NOT EDIT.
// source: music-channel/rcmd_music_channel.proto

package music_channel // import "golang.52tt.com/protocol/services/rcmd/music_channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import common "golang.52tt.com/protocol/services/rcmd/common"
import rcmd_channel_label "golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RCMDLabel int32

const (
	RCMDLabel_None                          RCMDLabel = 0
	RCMDLabel_GangUpWithHomeOwner           RCMDLabel = 1
	RCMDLabel_ChatWithHomeOwner             RCMDLabel = 2
	RCMDLabel_FollowUserInChannel           RCMDLabel = 3
	RCMDLabel_LocShow                       RCMDLabel = 4
	RCMDLabel_MtFollowUserInChannel         RCMDLabel = 5
	RCMDLabel_MtEverEnterChannel            RCMDLabel = 6
	RCMDLabel_MtManyUsersInChannel          RCMDLabel = 7
	RCMDLabel_MtManyUsersFollowChannelOwner RCMDLabel = 8
	RCMDLabel_MtGuessYouLikeChannelOwner    RCMDLabel = 9
	RCMDLabel_MtRecentLikeTabChannelDouDi   RCMDLabel = 10
	RCMDLabel_MtHotGameHotChannelDouDi      RCMDLabel = 11
	RCMDLabel_MtFriendOfFriend              RCMDLabel = 12
	RCMDLabel_MtRecentFollow                RCMDLabel = 13
)

var RCMDLabel_name = map[int32]string{
	0:  "None",
	1:  "GangUpWithHomeOwner",
	2:  "ChatWithHomeOwner",
	3:  "FollowUserInChannel",
	4:  "LocShow",
	5:  "MtFollowUserInChannel",
	6:  "MtEverEnterChannel",
	7:  "MtManyUsersInChannel",
	8:  "MtManyUsersFollowChannelOwner",
	9:  "MtGuessYouLikeChannelOwner",
	10: "MtRecentLikeTabChannelDouDi",
	11: "MtHotGameHotChannelDouDi",
	12: "MtFriendOfFriend",
	13: "MtRecentFollow",
}
var RCMDLabel_value = map[string]int32{
	"None":                          0,
	"GangUpWithHomeOwner":           1,
	"ChatWithHomeOwner":             2,
	"FollowUserInChannel":           3,
	"LocShow":                       4,
	"MtFollowUserInChannel":         5,
	"MtEverEnterChannel":            6,
	"MtManyUsersInChannel":          7,
	"MtManyUsersFollowChannelOwner": 8,
	"MtGuessYouLikeChannelOwner":    9,
	"MtRecentLikeTabChannelDouDi":   10,
	"MtHotGameHotChannelDouDi":      11,
	"MtFriendOfFriend":              12,
	"MtRecentFollow":                13,
}

func (x RCMDLabel) String() string {
	return proto.EnumName(RCMDLabel_name, int32(x))
}
func (RCMDLabel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{0}
}

type FilterType int32

const (
	FilterType_Invalid      FilterType = 0
	FilterType_OutputFilter FilterType = 1
	FilterType_ExposeFilter FilterType = 2
)

var FilterType_name = map[int32]string{
	0: "Invalid",
	1: "OutputFilter",
	2: "ExposeFilter",
}
var FilterType_value = map[string]int32{
	"Invalid":      0,
	"OutputFilter": 1,
	"ExposeFilter": 2,
}

func (x FilterType) String() string {
	return proto.EnumName(FilterType_name, int32(x))
}
func (FilterType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{1}
}

type STRATEGY int32

const (
	STRATEGY_INVALID        STRATEGY = 0
	STRATEGY_THEME_POP      STRATEGY = 1
	STRATEGY_USER_INVITE    STRATEGY = 2
	STRATEGY_QUEUE_PERSONA  STRATEGY = 3
	STRATEGY_CP_VERSION_TWO STRATEGY = 4
)

var STRATEGY_name = map[int32]string{
	0: "INVALID",
	1: "THEME_POP",
	2: "USER_INVITE",
	3: "QUEUE_PERSONA",
	4: "CP_VERSION_TWO",
}
var STRATEGY_value = map[string]int32{
	"INVALID":        0,
	"THEME_POP":      1,
	"USER_INVITE":    2,
	"QUEUE_PERSONA":  3,
	"CP_VERSION_TWO": 4,
}

func (x STRATEGY) String() string {
	return proto.EnumName(STRATEGY_name, int32(x))
}
func (STRATEGY) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{2}
}

type PERSONA int32

const (
	PERSONA_u_mt_probability_not_enter_7d         PERSONA = 0
	PERSONA_u_mt_probability_not_enter_pred_score PERSONA = 1
	PERSONA_u_mt_pgc_not_enter_7d                 PERSONA = 2
	PERSONA_u_mt_flash_account_list               PERSONA = 3
	PERSONA_u_mt_probability_low_enter_7d         PERSONA = 4
	PERSONA_user_is_social_predict                PERSONA = 5
	PERSONA_user_is_black_product                 PERSONA = 6
)

var PERSONA_name = map[int32]string{
	0: "u_mt_probability_not_enter_7d",
	1: "u_mt_probability_not_enter_pred_score",
	2: "u_mt_pgc_not_enter_7d",
	3: "u_mt_flash_account_list",
	4: "u_mt_probability_low_enter_7d",
	5: "user_is_social_predict",
	6: "user_is_black_product",
}
var PERSONA_value = map[string]int32{
	"u_mt_probability_not_enter_7d":         0,
	"u_mt_probability_not_enter_pred_score": 1,
	"u_mt_pgc_not_enter_7d":                 2,
	"u_mt_flash_account_list":               3,
	"u_mt_probability_low_enter_7d":         4,
	"user_is_social_predict":                5,
	"user_is_black_product":                 6,
}

func (x PERSONA) String() string {
	return proto.EnumName(PERSONA_name, int32(x))
}
func (PERSONA) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{3}
}

type THEME_POP_UI int32

const (
	THEME_POP_UI_OLD THEME_POP_UI = 0
	THEME_POP_UI_NEW THEME_POP_UI = 1
)

var THEME_POP_UI_name = map[int32]string{
	0: "OLD",
	1: "NEW",
}
var THEME_POP_UI_value = map[string]int32{
	"OLD": 0,
	"NEW": 1,
}

func (x THEME_POP_UI) String() string {
	return proto.EnumName(THEME_POP_UI_name, int32(x))
}
func (THEME_POP_UI) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{4}
}

type USER_TYPE int32

const (
	USER_TYPE_OLD_USER    USER_TYPE = 0
	USER_TYPE_RECALL_USER USER_TYPE = 1
	USER_TYPE_NEW_USER    USER_TYPE = 2
)

var USER_TYPE_name = map[int32]string{
	0: "OLD_USER",
	1: "RECALL_USER",
	2: "NEW_USER",
}
var USER_TYPE_value = map[string]int32{
	"OLD_USER":    0,
	"RECALL_USER": 1,
	"NEW_USER":    2,
}

func (x USER_TYPE) String() string {
	return proto.EnumName(USER_TYPE_name, int32(x))
}
func (USER_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{5}
}

type GetMusicChannelRecommendationReq_FilterModel int32

const (
	GetMusicChannelRecommendationReq_MtModel GetMusicChannelRecommendationReq_FilterModel = 0
	GetMusicChannelRecommendationReq_KhModel GetMusicChannelRecommendationReq_FilterModel = 1
)

var GetMusicChannelRecommendationReq_FilterModel_name = map[int32]string{
	0: "MtModel",
	1: "KhModel",
}
var GetMusicChannelRecommendationReq_FilterModel_value = map[string]int32{
	"MtModel": 0,
	"KhModel": 1,
}

func (x GetMusicChannelRecommendationReq_FilterModel) String() string {
	return proto.EnumName(GetMusicChannelRecommendationReq_FilterModel_name, int32(x))
}
func (GetMusicChannelRecommendationReq_FilterModel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{4, 0}
}

type ChannelInfo struct {
	TagId                uint32               `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	RecallFlag           uint32               `protobuf:"varint,2,opt,name=recall_flag,json=recallFlag,proto3" json:"recall_flag,omitempty"`
	Loc                  *common.LocationInfo `protobuf:"bytes,3,opt,name=loc,proto3" json:"loc,omitempty"`
	RcmdLabels           []RCMDLabel          `protobuf:"varint,4,rep,packed,name=rcmd_labels,json=rcmdLabels,proto3,enum=rcmd.music_channel.RCMDLabel" json:"rcmd_labels,omitempty"`
	MetaId               string               `protobuf:"bytes,5,opt,name=meta_id,json=metaId,proto3" json:"meta_id,omitempty"`
	RelationUids         []uint32             `protobuf:"varint,6,rep,packed,name=relation_uids,json=relationUids,proto3" json:"relation_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ChannelInfo) Reset()         { *m = ChannelInfo{} }
func (m *ChannelInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelInfo) ProtoMessage()    {}
func (*ChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{0}
}
func (m *ChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelInfo.Unmarshal(m, b)
}
func (m *ChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelInfo.Merge(dst, src)
}
func (m *ChannelInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelInfo.Size(m)
}
func (m *ChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelInfo proto.InternalMessageInfo

func (m *ChannelInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *ChannelInfo) GetRecallFlag() uint32 {
	if m != nil {
		return m.RecallFlag
	}
	return 0
}

func (m *ChannelInfo) GetLoc() *common.LocationInfo {
	if m != nil {
		return m.Loc
	}
	return nil
}

func (m *ChannelInfo) GetRcmdLabels() []RCMDLabel {
	if m != nil {
		return m.RcmdLabels
	}
	return nil
}

func (m *ChannelInfo) GetMetaId() string {
	if m != nil {
		return m.MetaId
	}
	return ""
}

func (m *ChannelInfo) GetRelationUids() []uint32 {
	if m != nil {
		return m.RelationUids
	}
	return nil
}

type BlockOption struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemId               uint32   `protobuf:"varint,2,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlockOption) Reset()         { *m = BlockOption{} }
func (m *BlockOption) String() string { return proto.CompactTextString(m) }
func (*BlockOption) ProtoMessage()    {}
func (*BlockOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{1}
}
func (m *BlockOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOption.Unmarshal(m, b)
}
func (m *BlockOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOption.Marshal(b, m, deterministic)
}
func (dst *BlockOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOption.Merge(dst, src)
}
func (m *BlockOption) XXX_Size() int {
	return xxx_messageInfo_BlockOption.Size(m)
}
func (m *BlockOption) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOption.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOption proto.InternalMessageInfo

func (m *BlockOption) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *BlockOption) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

// 房间主题
type ChannelTopic struct {
	TabId                uint32         `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOptions         []*BlockOption `protobuf:"bytes,2,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ChannelTopic) Reset()         { *m = ChannelTopic{} }
func (m *ChannelTopic) String() string { return proto.CompactTextString(m) }
func (*ChannelTopic) ProtoMessage()    {}
func (*ChannelTopic) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{2}
}
func (m *ChannelTopic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelTopic.Unmarshal(m, b)
}
func (m *ChannelTopic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelTopic.Marshal(b, m, deterministic)
}
func (dst *ChannelTopic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelTopic.Merge(dst, src)
}
func (m *ChannelTopic) XXX_Size() int {
	return xxx_messageInfo_ChannelTopic.Size(m)
}
func (m *ChannelTopic) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelTopic.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelTopic proto.InternalMessageInfo

func (m *ChannelTopic) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ChannelTopic) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

type NotExposeChannels struct {
	FilterId               string   `protobuf:"bytes,1,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	NotExposeChannelIdList []uint32 `protobuf:"varint,2,rep,packed,name=not_expose_channel_id_list,json=notExposeChannelIdList,proto3" json:"not_expose_channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *NotExposeChannels) Reset()         { *m = NotExposeChannels{} }
func (m *NotExposeChannels) String() string { return proto.CompactTextString(m) }
func (*NotExposeChannels) ProtoMessage()    {}
func (*NotExposeChannels) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{3}
}
func (m *NotExposeChannels) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotExposeChannels.Unmarshal(m, b)
}
func (m *NotExposeChannels) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotExposeChannels.Marshal(b, m, deterministic)
}
func (dst *NotExposeChannels) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotExposeChannels.Merge(dst, src)
}
func (m *NotExposeChannels) XXX_Size() int {
	return xxx_messageInfo_NotExposeChannels.Size(m)
}
func (m *NotExposeChannels) XXX_DiscardUnknown() {
	xxx_messageInfo_NotExposeChannels.DiscardUnknown(m)
}

var xxx_messageInfo_NotExposeChannels proto.InternalMessageInfo

func (m *NotExposeChannels) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *NotExposeChannels) GetNotExposeChannelIdList() []uint32 {
	if m != nil {
		return m.NotExposeChannelIdList
	}
	return nil
}

type GetMusicChannelRecommendationReq struct {
	Uid                    uint32                                  `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                  uint32                                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	BlockOptions           []*BlockOption                          `protobuf:"bytes,3,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	ClientType             uint32                                  `protobuf:"varint,4,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion          uint32                                  `protobuf:"varint,5,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	MarketId               uint32                                  `protobuf:"varint,6,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ChannelPackageId       string                                  `protobuf:"bytes,7,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	DebugFlag              uint32                                  `protobuf:"varint,8,opt,name=debug_flag,json=debugFlag,proto3" json:"debug_flag,omitempty"`
	Sex                    uint32                                  `protobuf:"varint,9,opt,name=sex,proto3" json:"sex,omitempty"`
	CategoryId             uint32                                  `protobuf:"varint,10,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	TabIds                 []uint32                                `protobuf:"varint,11,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	IsFirstGet             bool                                    `protobuf:"varint,12,opt,name=is_first_get,json=isFirstGet,proto3" json:"is_first_get,omitempty"`
	FilterType             FilterType                              `protobuf:"varint,13,opt,name=filter_type,json=filterType,proto3,enum=rcmd.music_channel.FilterType" json:"filter_type,omitempty"`
	ExposeChannelIdList    []uint32                                `protobuf:"varint,14,rep,packed,name=expose_channel_id_list,json=exposeChannelIdList,proto3" json:"expose_channel_id_list,omitempty"`
	FollowChannelIdList    []uint32                                `protobuf:"varint,15,rep,packed,name=follow_channel_id_list,json=followChannelIdList,proto3" json:"follow_channel_id_list,omitempty"`
	ChannelTopics          []*ChannelTopic                         `protobuf:"bytes,16,rep,name=channel_topics,json=channelTopics,proto3" json:"channel_topics,omitempty"`
	EnterSource            string                                  `protobuf:"bytes,17,opt,name=enter_source,json=enterSource,proto3" json:"enter_source,omitempty"`
	FilterId               string                                  `protobuf:"bytes,18,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	IsUserLocationAuthOpen bool                                    `protobuf:"varint,19,opt,name=is_user_location_auth_open,json=isUserLocationAuthOpen,proto3" json:"is_user_location_auth_open,omitempty"`
	NotExposeChannelsList  *NotExposeChannels                      `protobuf:"bytes,20,opt,name=not_expose_channels_list,json=notExposeChannelsList,proto3" json:"not_expose_channels_list,omitempty"`
	FilterModel            uint32                                  `protobuf:"varint,21,opt,name=filter_model,json=filterModel,proto3" json:"filter_model,omitempty"`
	Labels                 []*rcmd_channel_label.GameLabel         `protobuf:"bytes,22,rep,name=labels,proto3" json:"labels,omitempty"`
	ClassifyLabels         []*rcmd_channel_label.ClassifyLabelList `protobuf:"bytes,23,rep,name=classify_labels,json=classifyLabels,proto3" json:"classify_labels,omitempty"`
	ShieldFilterWords      []string                                `protobuf:"bytes,24,rep,name=shield_filter_words,json=shieldFilterWords,proto3" json:"shield_filter_words,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                                `json:"-"`
	XXX_unrecognized       []byte                                  `json:"-"`
	XXX_sizecache          int32                                   `json:"-"`
}

func (m *GetMusicChannelRecommendationReq) Reset()         { *m = GetMusicChannelRecommendationReq{} }
func (m *GetMusicChannelRecommendationReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelRecommendationReq) ProtoMessage()    {}
func (*GetMusicChannelRecommendationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{4}
}
func (m *GetMusicChannelRecommendationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelRecommendationReq.Unmarshal(m, b)
}
func (m *GetMusicChannelRecommendationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelRecommendationReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelRecommendationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelRecommendationReq.Merge(dst, src)
}
func (m *GetMusicChannelRecommendationReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelRecommendationReq.Size(m)
}
func (m *GetMusicChannelRecommendationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelRecommendationReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelRecommendationReq proto.InternalMessageInfo

func (m *GetMusicChannelRecommendationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMusicChannelRecommendationReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetMusicChannelRecommendationReq) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *GetMusicChannelRecommendationReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *GetMusicChannelRecommendationReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *GetMusicChannelRecommendationReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetMusicChannelRecommendationReq) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *GetMusicChannelRecommendationReq) GetDebugFlag() uint32 {
	if m != nil {
		return m.DebugFlag
	}
	return 0
}

func (m *GetMusicChannelRecommendationReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GetMusicChannelRecommendationReq) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *GetMusicChannelRecommendationReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *GetMusicChannelRecommendationReq) GetIsFirstGet() bool {
	if m != nil {
		return m.IsFirstGet
	}
	return false
}

func (m *GetMusicChannelRecommendationReq) GetFilterType() FilterType {
	if m != nil {
		return m.FilterType
	}
	return FilterType_Invalid
}

func (m *GetMusicChannelRecommendationReq) GetExposeChannelIdList() []uint32 {
	if m != nil {
		return m.ExposeChannelIdList
	}
	return nil
}

func (m *GetMusicChannelRecommendationReq) GetFollowChannelIdList() []uint32 {
	if m != nil {
		return m.FollowChannelIdList
	}
	return nil
}

func (m *GetMusicChannelRecommendationReq) GetChannelTopics() []*ChannelTopic {
	if m != nil {
		return m.ChannelTopics
	}
	return nil
}

func (m *GetMusicChannelRecommendationReq) GetEnterSource() string {
	if m != nil {
		return m.EnterSource
	}
	return ""
}

func (m *GetMusicChannelRecommendationReq) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *GetMusicChannelRecommendationReq) GetIsUserLocationAuthOpen() bool {
	if m != nil {
		return m.IsUserLocationAuthOpen
	}
	return false
}

func (m *GetMusicChannelRecommendationReq) GetNotExposeChannelsList() *NotExposeChannels {
	if m != nil {
		return m.NotExposeChannelsList
	}
	return nil
}

func (m *GetMusicChannelRecommendationReq) GetFilterModel() uint32 {
	if m != nil {
		return m.FilterModel
	}
	return 0
}

func (m *GetMusicChannelRecommendationReq) GetLabels() []*rcmd_channel_label.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *GetMusicChannelRecommendationReq) GetClassifyLabels() []*rcmd_channel_label.ClassifyLabelList {
	if m != nil {
		return m.ClassifyLabels
	}
	return nil
}

func (m *GetMusicChannelRecommendationReq) GetShieldFilterWords() []string {
	if m != nil {
		return m.ShieldFilterWords
	}
	return nil
}

type GetMusicChannelRecommendationResp struct {
	ChannelIdList        []uint32                `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	BottomReached        bool                    `protobuf:"varint,2,opt,name=bottom_reached,json=bottomReached,proto3" json:"bottom_reached,omitempty"`
	SelfLoc              *common.LocationInfo    `protobuf:"bytes,3,opt,name=self_loc,json=selfLoc,proto3" json:"self_loc,omitempty"`
	ChannelInfoMap       map[uint32]*ChannelInfo `protobuf:"bytes,4,rep,name=channel_info_map,json=channelInfoMap,proto3" json:"channel_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetMusicChannelRecommendationResp) Reset()         { *m = GetMusicChannelRecommendationResp{} }
func (m *GetMusicChannelRecommendationResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelRecommendationResp) ProtoMessage()    {}
func (*GetMusicChannelRecommendationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{5}
}
func (m *GetMusicChannelRecommendationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelRecommendationResp.Unmarshal(m, b)
}
func (m *GetMusicChannelRecommendationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelRecommendationResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelRecommendationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelRecommendationResp.Merge(dst, src)
}
func (m *GetMusicChannelRecommendationResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelRecommendationResp.Size(m)
}
func (m *GetMusicChannelRecommendationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelRecommendationResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelRecommendationResp proto.InternalMessageInfo

func (m *GetMusicChannelRecommendationResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *GetMusicChannelRecommendationResp) GetBottomReached() bool {
	if m != nil {
		return m.BottomReached
	}
	return false
}

func (m *GetMusicChannelRecommendationResp) GetSelfLoc() *common.LocationInfo {
	if m != nil {
		return m.SelfLoc
	}
	return nil
}

func (m *GetMusicChannelRecommendationResp) GetChannelInfoMap() map[uint32]*ChannelInfo {
	if m != nil {
		return m.ChannelInfoMap
	}
	return nil
}

type GetSelectPageMusicChannelRcmdReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabName              string   `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	ElemName             string   `protobuf:"bytes,3,opt,name=elem_name,json=elemName,proto3" json:"elem_name,omitempty"`
	PromotionLabel       []string `protobuf:"bytes,4,rep,name=promotion_label,json=promotionLabel,proto3" json:"promotion_label,omitempty"`
	MarketId             uint32   `protobuf:"varint,5,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ElemNames            []string `protobuf:"bytes,6,rep,name=elem_names,json=elemNames,proto3" json:"elem_names,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSelectPageMusicChannelRcmdReq) Reset()         { *m = GetSelectPageMusicChannelRcmdReq{} }
func (m *GetSelectPageMusicChannelRcmdReq) String() string { return proto.CompactTextString(m) }
func (*GetSelectPageMusicChannelRcmdReq) ProtoMessage()    {}
func (*GetSelectPageMusicChannelRcmdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{6}
}
func (m *GetSelectPageMusicChannelRcmdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSelectPageMusicChannelRcmdReq.Unmarshal(m, b)
}
func (m *GetSelectPageMusicChannelRcmdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSelectPageMusicChannelRcmdReq.Marshal(b, m, deterministic)
}
func (dst *GetSelectPageMusicChannelRcmdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSelectPageMusicChannelRcmdReq.Merge(dst, src)
}
func (m *GetSelectPageMusicChannelRcmdReq) XXX_Size() int {
	return xxx_messageInfo_GetSelectPageMusicChannelRcmdReq.Size(m)
}
func (m *GetSelectPageMusicChannelRcmdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSelectPageMusicChannelRcmdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSelectPageMusicChannelRcmdReq proto.InternalMessageInfo

func (m *GetSelectPageMusicChannelRcmdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSelectPageMusicChannelRcmdReq) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *GetSelectPageMusicChannelRcmdReq) GetElemName() string {
	if m != nil {
		return m.ElemName
	}
	return ""
}

func (m *GetSelectPageMusicChannelRcmdReq) GetPromotionLabel() []string {
	if m != nil {
		return m.PromotionLabel
	}
	return nil
}

func (m *GetSelectPageMusicChannelRcmdReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetSelectPageMusicChannelRcmdReq) GetElemNames() []string {
	if m != nil {
		return m.ElemNames
	}
	return nil
}

type GetSelectPageMusicChannelRcmdResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MetaId               string   `protobuf:"bytes,2,opt,name=meta_id,json=metaId,proto3" json:"meta_id,omitempty"`
	ErrCode              uint32   `protobuf:"varint,3,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,4,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSelectPageMusicChannelRcmdResp) Reset()         { *m = GetSelectPageMusicChannelRcmdResp{} }
func (m *GetSelectPageMusicChannelRcmdResp) String() string { return proto.CompactTextString(m) }
func (*GetSelectPageMusicChannelRcmdResp) ProtoMessage()    {}
func (*GetSelectPageMusicChannelRcmdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{7}
}
func (m *GetSelectPageMusicChannelRcmdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSelectPageMusicChannelRcmdResp.Unmarshal(m, b)
}
func (m *GetSelectPageMusicChannelRcmdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSelectPageMusicChannelRcmdResp.Marshal(b, m, deterministic)
}
func (dst *GetSelectPageMusicChannelRcmdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSelectPageMusicChannelRcmdResp.Merge(dst, src)
}
func (m *GetSelectPageMusicChannelRcmdResp) XXX_Size() int {
	return xxx_messageInfo_GetSelectPageMusicChannelRcmdResp.Size(m)
}
func (m *GetSelectPageMusicChannelRcmdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSelectPageMusicChannelRcmdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSelectPageMusicChannelRcmdResp proto.InternalMessageInfo

func (m *GetSelectPageMusicChannelRcmdResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetSelectPageMusicChannelRcmdResp) GetMetaId() string {
	if m != nil {
		return m.MetaId
	}
	return ""
}

func (m *GetSelectPageMusicChannelRcmdResp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetSelectPageMusicChannelRcmdResp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type GetMusicChannelPopupRcmdReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicChannelPopupRcmdReq) Reset()         { *m = GetMusicChannelPopupRcmdReq{} }
func (m *GetMusicChannelPopupRcmdReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelPopupRcmdReq) ProtoMessage()    {}
func (*GetMusicChannelPopupRcmdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{8}
}
func (m *GetMusicChannelPopupRcmdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelPopupRcmdReq.Unmarshal(m, b)
}
func (m *GetMusicChannelPopupRcmdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelPopupRcmdReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelPopupRcmdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelPopupRcmdReq.Merge(dst, src)
}
func (m *GetMusicChannelPopupRcmdReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelPopupRcmdReq.Size(m)
}
func (m *GetMusicChannelPopupRcmdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelPopupRcmdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelPopupRcmdReq proto.InternalMessageInfo

func (m *GetMusicChannelPopupRcmdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMusicChannelPopupRcmdReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type GetMusicChannelPopupRcmdResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MetaId               string   `protobuf:"bytes,2,opt,name=meta_id,json=metaId,proto3" json:"meta_id,omitempty"`
	FriendUid            uint32   `protobuf:"varint,3,opt,name=friend_uid,json=friendUid,proto3" json:"friend_uid,omitempty"`
	RelationUids         []uint32 `protobuf:"varint,4,rep,packed,name=relation_uids,json=relationUids,proto3" json:"relation_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicChannelPopupRcmdResp) Reset()         { *m = GetMusicChannelPopupRcmdResp{} }
func (m *GetMusicChannelPopupRcmdResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicChannelPopupRcmdResp) ProtoMessage()    {}
func (*GetMusicChannelPopupRcmdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{9}
}
func (m *GetMusicChannelPopupRcmdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicChannelPopupRcmdResp.Unmarshal(m, b)
}
func (m *GetMusicChannelPopupRcmdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicChannelPopupRcmdResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicChannelPopupRcmdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicChannelPopupRcmdResp.Merge(dst, src)
}
func (m *GetMusicChannelPopupRcmdResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicChannelPopupRcmdResp.Size(m)
}
func (m *GetMusicChannelPopupRcmdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicChannelPopupRcmdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicChannelPopupRcmdResp proto.InternalMessageInfo

func (m *GetMusicChannelPopupRcmdResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMusicChannelPopupRcmdResp) GetMetaId() string {
	if m != nil {
		return m.MetaId
	}
	return ""
}

func (m *GetMusicChannelPopupRcmdResp) GetFriendUid() uint32 {
	if m != nil {
		return m.FriendUid
	}
	return 0
}

func (m *GetMusicChannelPopupRcmdResp) GetRelationUids() []uint32 {
	if m != nil {
		return m.RelationUids
	}
	return nil
}

type GetCollectedChannelReq struct {
	Uid                    uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                  uint32     `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	ClientType             uint32     `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion          uint32     `protobuf:"varint,4,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	MarketId               uint32     `protobuf:"varint,5,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ChannelPackageId       string     `protobuf:"bytes,6,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	Sex                    uint32     `protobuf:"varint,7,opt,name=sex,proto3" json:"sex,omitempty"`
	IsFirstGet             bool       `protobuf:"varint,8,opt,name=is_first_get,json=isFirstGet,proto3" json:"is_first_get,omitempty"`
	FilterType             FilterType `protobuf:"varint,9,opt,name=filter_type,json=filterType,proto3,enum=rcmd.music_channel.FilterType" json:"filter_type,omitempty"`
	ExposeChannelIdList    []uint32   `protobuf:"varint,10,rep,packed,name=expose_channel_id_list,json=exposeChannelIdList,proto3" json:"expose_channel_id_list,omitempty"`
	NotExposeChannelIdList []uint32   `protobuf:"varint,11,rep,packed,name=not_expose_channel_id_list,json=notExposeChannelIdList,proto3" json:"not_expose_channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}   `json:"-"`
	XXX_unrecognized       []byte     `json:"-"`
	XXX_sizecache          int32      `json:"-"`
}

func (m *GetCollectedChannelReq) Reset()         { *m = GetCollectedChannelReq{} }
func (m *GetCollectedChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetCollectedChannelReq) ProtoMessage()    {}
func (*GetCollectedChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{10}
}
func (m *GetCollectedChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCollectedChannelReq.Unmarshal(m, b)
}
func (m *GetCollectedChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCollectedChannelReq.Marshal(b, m, deterministic)
}
func (dst *GetCollectedChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCollectedChannelReq.Merge(dst, src)
}
func (m *GetCollectedChannelReq) XXX_Size() int {
	return xxx_messageInfo_GetCollectedChannelReq.Size(m)
}
func (m *GetCollectedChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCollectedChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCollectedChannelReq proto.InternalMessageInfo

func (m *GetCollectedChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCollectedChannelReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetCollectedChannelReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *GetCollectedChannelReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *GetCollectedChannelReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetCollectedChannelReq) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *GetCollectedChannelReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GetCollectedChannelReq) GetIsFirstGet() bool {
	if m != nil {
		return m.IsFirstGet
	}
	return false
}

func (m *GetCollectedChannelReq) GetFilterType() FilterType {
	if m != nil {
		return m.FilterType
	}
	return FilterType_Invalid
}

func (m *GetCollectedChannelReq) GetExposeChannelIdList() []uint32 {
	if m != nil {
		return m.ExposeChannelIdList
	}
	return nil
}

func (m *GetCollectedChannelReq) GetNotExposeChannelIdList() []uint32 {
	if m != nil {
		return m.NotExposeChannelIdList
	}
	return nil
}

type GetCollectedChannelResp struct {
	ChannelIdList        []uint32                `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	BottomReached        bool                    `protobuf:"varint,2,opt,name=bottom_reached,json=bottomReached,proto3" json:"bottom_reached,omitempty"`
	SelfLoc              *common.LocationInfo    `protobuf:"bytes,3,opt,name=self_loc,json=selfLoc,proto3" json:"self_loc,omitempty"`
	ChannelInfoMap       map[uint32]*ChannelInfo `protobuf:"bytes,4,rep,name=channel_info_map,json=channelInfoMap,proto3" json:"channel_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetCollectedChannelResp) Reset()         { *m = GetCollectedChannelResp{} }
func (m *GetCollectedChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetCollectedChannelResp) ProtoMessage()    {}
func (*GetCollectedChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{11}
}
func (m *GetCollectedChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCollectedChannelResp.Unmarshal(m, b)
}
func (m *GetCollectedChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCollectedChannelResp.Marshal(b, m, deterministic)
}
func (dst *GetCollectedChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCollectedChannelResp.Merge(dst, src)
}
func (m *GetCollectedChannelResp) XXX_Size() int {
	return xxx_messageInfo_GetCollectedChannelResp.Size(m)
}
func (m *GetCollectedChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCollectedChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCollectedChannelResp proto.InternalMessageInfo

func (m *GetCollectedChannelResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *GetCollectedChannelResp) GetBottomReached() bool {
	if m != nil {
		return m.BottomReached
	}
	return false
}

func (m *GetCollectedChannelResp) GetSelfLoc() *common.LocationInfo {
	if m != nil {
		return m.SelfLoc
	}
	return nil
}

func (m *GetCollectedChannelResp) GetChannelInfoMap() map[uint32]*ChannelInfo {
	if m != nil {
		return m.ChannelInfoMap
	}
	return nil
}

// 获取房间内重逢用户列表
type GetRoomRelationUidsReq struct {
	ReqUid               uint32   `protobuf:"varint,1,opt,name=req_uid,json=reqUid,proto3" json:"req_uid,omitempty"`
	RoomUids             []uint32 `protobuf:"varint,2,rep,packed,name=room_uids,json=roomUids,proto3" json:"room_uids,omitempty"`
	EnterSource          string   `protobuf:"bytes,3,opt,name=enter_source,json=enterSource,proto3" json:"enter_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRoomRelationUidsReq) Reset()         { *m = GetRoomRelationUidsReq{} }
func (m *GetRoomRelationUidsReq) String() string { return proto.CompactTextString(m) }
func (*GetRoomRelationUidsReq) ProtoMessage()    {}
func (*GetRoomRelationUidsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{12}
}
func (m *GetRoomRelationUidsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoomRelationUidsReq.Unmarshal(m, b)
}
func (m *GetRoomRelationUidsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoomRelationUidsReq.Marshal(b, m, deterministic)
}
func (dst *GetRoomRelationUidsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoomRelationUidsReq.Merge(dst, src)
}
func (m *GetRoomRelationUidsReq) XXX_Size() int {
	return xxx_messageInfo_GetRoomRelationUidsReq.Size(m)
}
func (m *GetRoomRelationUidsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoomRelationUidsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoomRelationUidsReq proto.InternalMessageInfo

func (m *GetRoomRelationUidsReq) GetReqUid() uint32 {
	if m != nil {
		return m.ReqUid
	}
	return 0
}

func (m *GetRoomRelationUidsReq) GetRoomUids() []uint32 {
	if m != nil {
		return m.RoomUids
	}
	return nil
}

func (m *GetRoomRelationUidsReq) GetEnterSource() string {
	if m != nil {
		return m.EnterSource
	}
	return ""
}

type GetRoomRelationUidsResp struct {
	RelationItems        []*RelationItem `protobuf:"bytes,1,rep,name=relation_items,json=relationItems,proto3" json:"relation_items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetRoomRelationUidsResp) Reset()         { *m = GetRoomRelationUidsResp{} }
func (m *GetRoomRelationUidsResp) String() string { return proto.CompactTextString(m) }
func (*GetRoomRelationUidsResp) ProtoMessage()    {}
func (*GetRoomRelationUidsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{13}
}
func (m *GetRoomRelationUidsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoomRelationUidsResp.Unmarshal(m, b)
}
func (m *GetRoomRelationUidsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoomRelationUidsResp.Marshal(b, m, deterministic)
}
func (dst *GetRoomRelationUidsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoomRelationUidsResp.Merge(dst, src)
}
func (m *GetRoomRelationUidsResp) XXX_Size() int {
	return xxx_messageInfo_GetRoomRelationUidsResp.Size(m)
}
func (m *GetRoomRelationUidsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoomRelationUidsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoomRelationUidsResp proto.InternalMessageInfo

func (m *GetRoomRelationUidsResp) GetRelationItems() []*RelationItem {
	if m != nil {
		return m.RelationItems
	}
	return nil
}

type RelationItem struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MetaId               string   `protobuf:"bytes,2,opt,name=meta_id,json=metaId,proto3" json:"meta_id,omitempty"`
	LastTimestamp        string   `protobuf:"bytes,3,opt,name=last_timestamp,json=lastTimestamp,proto3" json:"last_timestamp,omitempty"`
	ChannelTopic         string   `protobuf:"bytes,4,opt,name=channel_topic,json=channelTopic,proto3" json:"channel_topic,omitempty"`
	SongName             string   `protobuf:"bytes,5,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RelationItem) Reset()         { *m = RelationItem{} }
func (m *RelationItem) String() string { return proto.CompactTextString(m) }
func (*RelationItem) ProtoMessage()    {}
func (*RelationItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{14}
}
func (m *RelationItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationItem.Unmarshal(m, b)
}
func (m *RelationItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationItem.Marshal(b, m, deterministic)
}
func (dst *RelationItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationItem.Merge(dst, src)
}
func (m *RelationItem) XXX_Size() int {
	return xxx_messageInfo_RelationItem.Size(m)
}
func (m *RelationItem) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationItem.DiscardUnknown(m)
}

var xxx_messageInfo_RelationItem proto.InternalMessageInfo

func (m *RelationItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RelationItem) GetMetaId() string {
	if m != nil {
		return m.MetaId
	}
	return ""
}

func (m *RelationItem) GetLastTimestamp() string {
	if m != nil {
		return m.LastTimestamp
	}
	return ""
}

func (m *RelationItem) GetChannelTopic() string {
	if m != nil {
		return m.ChannelTopic
	}
	return ""
}

func (m *RelationItem) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

type GetThemeChannelPopupRcmdReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ReqSource            string   `protobuf:"bytes,3,opt,name=req_source,json=reqSource,proto3" json:"req_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetThemeChannelPopupRcmdReq) Reset()         { *m = GetThemeChannelPopupRcmdReq{} }
func (m *GetThemeChannelPopupRcmdReq) String() string { return proto.CompactTextString(m) }
func (*GetThemeChannelPopupRcmdReq) ProtoMessage()    {}
func (*GetThemeChannelPopupRcmdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{15}
}
func (m *GetThemeChannelPopupRcmdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetThemeChannelPopupRcmdReq.Unmarshal(m, b)
}
func (m *GetThemeChannelPopupRcmdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetThemeChannelPopupRcmdReq.Marshal(b, m, deterministic)
}
func (dst *GetThemeChannelPopupRcmdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetThemeChannelPopupRcmdReq.Merge(dst, src)
}
func (m *GetThemeChannelPopupRcmdReq) XXX_Size() int {
	return xxx_messageInfo_GetThemeChannelPopupRcmdReq.Size(m)
}
func (m *GetThemeChannelPopupRcmdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetThemeChannelPopupRcmdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetThemeChannelPopupRcmdReq proto.InternalMessageInfo

func (m *GetThemeChannelPopupRcmdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetThemeChannelPopupRcmdReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetThemeChannelPopupRcmdReq) GetReqSource() string {
	if m != nil {
		return m.ReqSource
	}
	return ""
}

type GetThemeChannelPopupRcmdResp struct {
	ChannelId            uint32      `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MetaId               string      `protobuf:"bytes,2,opt,name=meta_id,json=metaId,proto3" json:"meta_id,omitempty"`
	RelationUids         []uint32    `protobuf:"varint,3,rep,packed,name=relation_uids,json=relationUids,proto3" json:"relation_uids,omitempty"`
	ReunionUids          []uint32    `protobuf:"varint,4,rep,packed,name=reunion_uids,json=reunionUids,proto3" json:"reunion_uids,omitempty"`
	RcmdLabels           []RCMDLabel `protobuf:"varint,5,rep,packed,name=rcmd_labels,json=rcmdLabels,proto3,enum=rcmd.music_channel.RCMDLabel" json:"rcmd_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetThemeChannelPopupRcmdResp) Reset()         { *m = GetThemeChannelPopupRcmdResp{} }
func (m *GetThemeChannelPopupRcmdResp) String() string { return proto.CompactTextString(m) }
func (*GetThemeChannelPopupRcmdResp) ProtoMessage()    {}
func (*GetThemeChannelPopupRcmdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{16}
}
func (m *GetThemeChannelPopupRcmdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetThemeChannelPopupRcmdResp.Unmarshal(m, b)
}
func (m *GetThemeChannelPopupRcmdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetThemeChannelPopupRcmdResp.Marshal(b, m, deterministic)
}
func (dst *GetThemeChannelPopupRcmdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetThemeChannelPopupRcmdResp.Merge(dst, src)
}
func (m *GetThemeChannelPopupRcmdResp) XXX_Size() int {
	return xxx_messageInfo_GetThemeChannelPopupRcmdResp.Size(m)
}
func (m *GetThemeChannelPopupRcmdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetThemeChannelPopupRcmdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetThemeChannelPopupRcmdResp proto.InternalMessageInfo

func (m *GetThemeChannelPopupRcmdResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetThemeChannelPopupRcmdResp) GetMetaId() string {
	if m != nil {
		return m.MetaId
	}
	return ""
}

func (m *GetThemeChannelPopupRcmdResp) GetRelationUids() []uint32 {
	if m != nil {
		return m.RelationUids
	}
	return nil
}

func (m *GetThemeChannelPopupRcmdResp) GetReunionUids() []uint32 {
	if m != nil {
		return m.ReunionUids
	}
	return nil
}

func (m *GetThemeChannelPopupRcmdResp) GetRcmdLabels() []RCMDLabel {
	if m != nil {
		return m.RcmdLabels
	}
	return nil
}

type NotifyChannelLiveStatusReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LiveStatus           bool     `protobuf:"varint,2,opt,name=live_status,json=liveStatus,proto3" json:"live_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyChannelLiveStatusReq) Reset()         { *m = NotifyChannelLiveStatusReq{} }
func (m *NotifyChannelLiveStatusReq) String() string { return proto.CompactTextString(m) }
func (*NotifyChannelLiveStatusReq) ProtoMessage()    {}
func (*NotifyChannelLiveStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{17}
}
func (m *NotifyChannelLiveStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyChannelLiveStatusReq.Unmarshal(m, b)
}
func (m *NotifyChannelLiveStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyChannelLiveStatusReq.Marshal(b, m, deterministic)
}
func (dst *NotifyChannelLiveStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyChannelLiveStatusReq.Merge(dst, src)
}
func (m *NotifyChannelLiveStatusReq) XXX_Size() int {
	return xxx_messageInfo_NotifyChannelLiveStatusReq.Size(m)
}
func (m *NotifyChannelLiveStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyChannelLiveStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyChannelLiveStatusReq proto.InternalMessageInfo

func (m *NotifyChannelLiveStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NotifyChannelLiveStatusReq) GetLiveStatus() bool {
	if m != nil {
		return m.LiveStatus
	}
	return false
}

type NotifyChannelLiveStatusResp struct {
	IsSuccess            bool     `protobuf:"varint,1,opt,name=is_success,json=isSuccess,proto3" json:"is_success,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyChannelLiveStatusResp) Reset()         { *m = NotifyChannelLiveStatusResp{} }
func (m *NotifyChannelLiveStatusResp) String() string { return proto.CompactTextString(m) }
func (*NotifyChannelLiveStatusResp) ProtoMessage()    {}
func (*NotifyChannelLiveStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{18}
}
func (m *NotifyChannelLiveStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyChannelLiveStatusResp.Unmarshal(m, b)
}
func (m *NotifyChannelLiveStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyChannelLiveStatusResp.Marshal(b, m, deterministic)
}
func (dst *NotifyChannelLiveStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyChannelLiveStatusResp.Merge(dst, src)
}
func (m *NotifyChannelLiveStatusResp) XXX_Size() int {
	return xxx_messageInfo_NotifyChannelLiveStatusResp.Size(m)
}
func (m *NotifyChannelLiveStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyChannelLiveStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyChannelLiveStatusResp proto.InternalMessageInfo

func (m *NotifyChannelLiveStatusResp) GetIsSuccess() bool {
	if m != nil {
		return m.IsSuccess
	}
	return false
}

type GetChannelsLocationReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelIdList        []uint32 `protobuf:"varint,2,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelsLocationReq) Reset()         { *m = GetChannelsLocationReq{} }
func (m *GetChannelsLocationReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelsLocationReq) ProtoMessage()    {}
func (*GetChannelsLocationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{19}
}
func (m *GetChannelsLocationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelsLocationReq.Unmarshal(m, b)
}
func (m *GetChannelsLocationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelsLocationReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelsLocationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelsLocationReq.Merge(dst, src)
}
func (m *GetChannelsLocationReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelsLocationReq.Size(m)
}
func (m *GetChannelsLocationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelsLocationReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelsLocationReq proto.InternalMessageInfo

func (m *GetChannelsLocationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelsLocationReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type ChannelLocationInfo struct {
	ChannelCity          string            `protobuf:"bytes,1,opt,name=channel_city,json=channelCity,proto3" json:"channel_city,omitempty"`
	UserLocationMap      map[uint32]string `protobuf:"bytes,2,rep,name=user_location_map,json=userLocationMap,proto3" json:"user_location_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ChannelLocationInfo) Reset()         { *m = ChannelLocationInfo{} }
func (m *ChannelLocationInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelLocationInfo) ProtoMessage()    {}
func (*ChannelLocationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{20}
}
func (m *ChannelLocationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLocationInfo.Unmarshal(m, b)
}
func (m *ChannelLocationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLocationInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelLocationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLocationInfo.Merge(dst, src)
}
func (m *ChannelLocationInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelLocationInfo.Size(m)
}
func (m *ChannelLocationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLocationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLocationInfo proto.InternalMessageInfo

func (m *ChannelLocationInfo) GetChannelCity() string {
	if m != nil {
		return m.ChannelCity
	}
	return ""
}

func (m *ChannelLocationInfo) GetUserLocationMap() map[uint32]string {
	if m != nil {
		return m.UserLocationMap
	}
	return nil
}

type GetChannelsLocationResp struct {
	ChannelLocationInfo  map[uint32]*ChannelLocationInfo `protobuf:"bytes,1,rep,name=channel_location_info,json=channelLocationInfo,proto3" json:"channel_location_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetChannelsLocationResp) Reset()         { *m = GetChannelsLocationResp{} }
func (m *GetChannelsLocationResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelsLocationResp) ProtoMessage()    {}
func (*GetChannelsLocationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{21}
}
func (m *GetChannelsLocationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelsLocationResp.Unmarshal(m, b)
}
func (m *GetChannelsLocationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelsLocationResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelsLocationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelsLocationResp.Merge(dst, src)
}
func (m *GetChannelsLocationResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelsLocationResp.Size(m)
}
func (m *GetChannelsLocationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelsLocationResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelsLocationResp proto.InternalMessageInfo

func (m *GetChannelsLocationResp) GetChannelLocationInfo() map[uint32]*ChannelLocationInfo {
	if m != nil {
		return m.ChannelLocationInfo
	}
	return nil
}

type UserSetToVIPReq struct {
	UidList              []string `protobuf:"bytes,1,rep,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSetToVIPReq) Reset()         { *m = UserSetToVIPReq{} }
func (m *UserSetToVIPReq) String() string { return proto.CompactTextString(m) }
func (*UserSetToVIPReq) ProtoMessage()    {}
func (*UserSetToVIPReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{22}
}
func (m *UserSetToVIPReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSetToVIPReq.Unmarshal(m, b)
}
func (m *UserSetToVIPReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSetToVIPReq.Marshal(b, m, deterministic)
}
func (dst *UserSetToVIPReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSetToVIPReq.Merge(dst, src)
}
func (m *UserSetToVIPReq) XXX_Size() int {
	return xxx_messageInfo_UserSetToVIPReq.Size(m)
}
func (m *UserSetToVIPReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSetToVIPReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserSetToVIPReq proto.InternalMessageInfo

func (m *UserSetToVIPReq) GetUidList() []string {
	if m != nil {
		return m.UidList
	}
	return nil
}

type UserSetToVIPResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSetToVIPResp) Reset()         { *m = UserSetToVIPResp{} }
func (m *UserSetToVIPResp) String() string { return proto.CompactTextString(m) }
func (*UserSetToVIPResp) ProtoMessage()    {}
func (*UserSetToVIPResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{23}
}
func (m *UserSetToVIPResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSetToVIPResp.Unmarshal(m, b)
}
func (m *UserSetToVIPResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSetToVIPResp.Marshal(b, m, deterministic)
}
func (dst *UserSetToVIPResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSetToVIPResp.Merge(dst, src)
}
func (m *UserSetToVIPResp) XXX_Size() int {
	return xxx_messageInfo_UserSetToVIPResp.Size(m)
}
func (m *UserSetToVIPResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSetToVIPResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserSetToVIPResp proto.InternalMessageInfo

type GetABTestStrategyReq struct {
	StrategyName         STRATEGY  `protobuf:"varint,1,opt,name=strategy_name,json=strategyName,proto3,enum=rcmd.music_channel.STRATEGY" json:"strategy_name,omitempty"`
	Uid                  uint32    `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	PersonaFields        []PERSONA `protobuf:"varint,3,rep,packed,name=persona_fields,json=personaFields,proto3,enum=rcmd.music_channel.PERSONA" json:"persona_fields,omitempty"`
	PersonaKeys          []string  `protobuf:"bytes,4,rep,name=persona_keys,json=personaKeys,proto3" json:"persona_keys,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetABTestStrategyReq) Reset()         { *m = GetABTestStrategyReq{} }
func (m *GetABTestStrategyReq) String() string { return proto.CompactTextString(m) }
func (*GetABTestStrategyReq) ProtoMessage()    {}
func (*GetABTestStrategyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{24}
}
func (m *GetABTestStrategyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetABTestStrategyReq.Unmarshal(m, b)
}
func (m *GetABTestStrategyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetABTestStrategyReq.Marshal(b, m, deterministic)
}
func (dst *GetABTestStrategyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetABTestStrategyReq.Merge(dst, src)
}
func (m *GetABTestStrategyReq) XXX_Size() int {
	return xxx_messageInfo_GetABTestStrategyReq.Size(m)
}
func (m *GetABTestStrategyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetABTestStrategyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetABTestStrategyReq proto.InternalMessageInfo

func (m *GetABTestStrategyReq) GetStrategyName() STRATEGY {
	if m != nil {
		return m.StrategyName
	}
	return STRATEGY_INVALID
}

func (m *GetABTestStrategyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetABTestStrategyReq) GetPersonaFields() []PERSONA {
	if m != nil {
		return m.PersonaFields
	}
	return nil
}

func (m *GetABTestStrategyReq) GetPersonaKeys() []string {
	if m != nil {
		return m.PersonaKeys
	}
	return nil
}

type THEME_POP_STRATEGY struct {
	UiVersion            THEME_POP_UI `protobuf:"varint,1,opt,name=ui_version,json=uiVersion,proto3,enum=rcmd.music_channel.THEME_POP_UI" json:"ui_version,omitempty"`
	IfReqInIm            bool         `protobuf:"varint,2,opt,name=if_req_in_im,json=ifReqInIm,proto3" json:"if_req_in_im,omitempty"`
	UserType             USER_TYPE    `protobuf:"varint,3,opt,name=user_type,json=userType,proto3,enum=rcmd.music_channel.USER_TYPE" json:"user_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *THEME_POP_STRATEGY) Reset()         { *m = THEME_POP_STRATEGY{} }
func (m *THEME_POP_STRATEGY) String() string { return proto.CompactTextString(m) }
func (*THEME_POP_STRATEGY) ProtoMessage()    {}
func (*THEME_POP_STRATEGY) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{25}
}
func (m *THEME_POP_STRATEGY) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_THEME_POP_STRATEGY.Unmarshal(m, b)
}
func (m *THEME_POP_STRATEGY) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_THEME_POP_STRATEGY.Marshal(b, m, deterministic)
}
func (dst *THEME_POP_STRATEGY) XXX_Merge(src proto.Message) {
	xxx_messageInfo_THEME_POP_STRATEGY.Merge(dst, src)
}
func (m *THEME_POP_STRATEGY) XXX_Size() int {
	return xxx_messageInfo_THEME_POP_STRATEGY.Size(m)
}
func (m *THEME_POP_STRATEGY) XXX_DiscardUnknown() {
	xxx_messageInfo_THEME_POP_STRATEGY.DiscardUnknown(m)
}

var xxx_messageInfo_THEME_POP_STRATEGY proto.InternalMessageInfo

func (m *THEME_POP_STRATEGY) GetUiVersion() THEME_POP_UI {
	if m != nil {
		return m.UiVersion
	}
	return THEME_POP_UI_OLD
}

func (m *THEME_POP_STRATEGY) GetIfReqInIm() bool {
	if m != nil {
		return m.IfReqInIm
	}
	return false
}

func (m *THEME_POP_STRATEGY) GetUserType() USER_TYPE {
	if m != nil {
		return m.UserType
	}
	return USER_TYPE_OLD_USER
}

type USER_INVITE_STRATEGY struct {
	HitExperimentalGroup bool     `protobuf:"varint,1,opt,name=hit_experimental_group,json=hitExperimentalGroup,proto3" json:"hit_experimental_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *USER_INVITE_STRATEGY) Reset()         { *m = USER_INVITE_STRATEGY{} }
func (m *USER_INVITE_STRATEGY) String() string { return proto.CompactTextString(m) }
func (*USER_INVITE_STRATEGY) ProtoMessage()    {}
func (*USER_INVITE_STRATEGY) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{26}
}
func (m *USER_INVITE_STRATEGY) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_USER_INVITE_STRATEGY.Unmarshal(m, b)
}
func (m *USER_INVITE_STRATEGY) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_USER_INVITE_STRATEGY.Marshal(b, m, deterministic)
}
func (dst *USER_INVITE_STRATEGY) XXX_Merge(src proto.Message) {
	xxx_messageInfo_USER_INVITE_STRATEGY.Merge(dst, src)
}
func (m *USER_INVITE_STRATEGY) XXX_Size() int {
	return xxx_messageInfo_USER_INVITE_STRATEGY.Size(m)
}
func (m *USER_INVITE_STRATEGY) XXX_DiscardUnknown() {
	xxx_messageInfo_USER_INVITE_STRATEGY.DiscardUnknown(m)
}

var xxx_messageInfo_USER_INVITE_STRATEGY proto.InternalMessageInfo

func (m *USER_INVITE_STRATEGY) GetHitExperimentalGroup() bool {
	if m != nil {
		return m.HitExperimentalGroup
	}
	return false
}

type CP_STRATEGY struct {
	ShowWindow           bool     `protobuf:"varint,1,opt,name=show_window,json=showWindow,proto3" json:"show_window,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CP_STRATEGY) Reset()         { *m = CP_STRATEGY{} }
func (m *CP_STRATEGY) String() string { return proto.CompactTextString(m) }
func (*CP_STRATEGY) ProtoMessage()    {}
func (*CP_STRATEGY) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{27}
}
func (m *CP_STRATEGY) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CP_STRATEGY.Unmarshal(m, b)
}
func (m *CP_STRATEGY) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CP_STRATEGY.Marshal(b, m, deterministic)
}
func (dst *CP_STRATEGY) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CP_STRATEGY.Merge(dst, src)
}
func (m *CP_STRATEGY) XXX_Size() int {
	return xxx_messageInfo_CP_STRATEGY.Size(m)
}
func (m *CP_STRATEGY) XXX_DiscardUnknown() {
	xxx_messageInfo_CP_STRATEGY.DiscardUnknown(m)
}

var xxx_messageInfo_CP_STRATEGY proto.InternalMessageInfo

func (m *CP_STRATEGY) GetShowWindow() bool {
	if m != nil {
		return m.ShowWindow
	}
	return false
}

type GetABTestStrategyResp struct {
	StrategyName         STRATEGY                                      `protobuf:"varint,1,opt,name=strategy_name,json=strategyName,proto3,enum=rcmd.music_channel.STRATEGY" json:"strategy_name,omitempty"`
	ThemePopStrategy     *THEME_POP_STRATEGY                           `protobuf:"bytes,2,opt,name=theme_pop_strategy,json=themePopStrategy,proto3" json:"theme_pop_strategy,omitempty"`
	HitGroupRule         bool                                          `protobuf:"varint,3,opt,name=hit_group_rule,json=hitGroupRule,proto3" json:"hit_group_rule,omitempty"`
	UserInviteStrategy   *USER_INVITE_STRATEGY                         `protobuf:"bytes,4,opt,name=user_invite_strategy,json=userInviteStrategy,proto3" json:"user_invite_strategy,omitempty"`
	IfSureUserGroup      bool                                          `protobuf:"varint,5,opt,name=if_sure_user_group,json=ifSureUserGroup,proto3" json:"if_sure_user_group,omitempty"`
	PersonaData          map[string]*GetABTestStrategyResp_PersonaData `protobuf:"bytes,6,rep,name=persona_data,json=personaData,proto3" json:"persona_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	CoupleStrategy       *CP_STRATEGY                                  `protobuf:"bytes,7,opt,name=couple_strategy,json=coupleStrategy,proto3" json:"couple_strategy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *GetABTestStrategyResp) Reset()         { *m = GetABTestStrategyResp{} }
func (m *GetABTestStrategyResp) String() string { return proto.CompactTextString(m) }
func (*GetABTestStrategyResp) ProtoMessage()    {}
func (*GetABTestStrategyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{28}
}
func (m *GetABTestStrategyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetABTestStrategyResp.Unmarshal(m, b)
}
func (m *GetABTestStrategyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetABTestStrategyResp.Marshal(b, m, deterministic)
}
func (dst *GetABTestStrategyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetABTestStrategyResp.Merge(dst, src)
}
func (m *GetABTestStrategyResp) XXX_Size() int {
	return xxx_messageInfo_GetABTestStrategyResp.Size(m)
}
func (m *GetABTestStrategyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetABTestStrategyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetABTestStrategyResp proto.InternalMessageInfo

func (m *GetABTestStrategyResp) GetStrategyName() STRATEGY {
	if m != nil {
		return m.StrategyName
	}
	return STRATEGY_INVALID
}

func (m *GetABTestStrategyResp) GetThemePopStrategy() *THEME_POP_STRATEGY {
	if m != nil {
		return m.ThemePopStrategy
	}
	return nil
}

func (m *GetABTestStrategyResp) GetHitGroupRule() bool {
	if m != nil {
		return m.HitGroupRule
	}
	return false
}

func (m *GetABTestStrategyResp) GetUserInviteStrategy() *USER_INVITE_STRATEGY {
	if m != nil {
		return m.UserInviteStrategy
	}
	return nil
}

func (m *GetABTestStrategyResp) GetIfSureUserGroup() bool {
	if m != nil {
		return m.IfSureUserGroup
	}
	return false
}

func (m *GetABTestStrategyResp) GetPersonaData() map[string]*GetABTestStrategyResp_PersonaData {
	if m != nil {
		return m.PersonaData
	}
	return nil
}

func (m *GetABTestStrategyResp) GetCoupleStrategy() *CP_STRATEGY {
	if m != nil {
		return m.CoupleStrategy
	}
	return nil
}

type GetABTestStrategyResp_PersonaData struct {
	DataMap              map[int32]string `protobuf:"bytes,1,rep,name=data_map,json=dataMap,proto3" json:"data_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetABTestStrategyResp_PersonaData) Reset()         { *m = GetABTestStrategyResp_PersonaData{} }
func (m *GetABTestStrategyResp_PersonaData) String() string { return proto.CompactTextString(m) }
func (*GetABTestStrategyResp_PersonaData) ProtoMessage()    {}
func (*GetABTestStrategyResp_PersonaData) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb, []int{28, 1}
}
func (m *GetABTestStrategyResp_PersonaData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetABTestStrategyResp_PersonaData.Unmarshal(m, b)
}
func (m *GetABTestStrategyResp_PersonaData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetABTestStrategyResp_PersonaData.Marshal(b, m, deterministic)
}
func (dst *GetABTestStrategyResp_PersonaData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetABTestStrategyResp_PersonaData.Merge(dst, src)
}
func (m *GetABTestStrategyResp_PersonaData) XXX_Size() int {
	return xxx_messageInfo_GetABTestStrategyResp_PersonaData.Size(m)
}
func (m *GetABTestStrategyResp_PersonaData) XXX_DiscardUnknown() {
	xxx_messageInfo_GetABTestStrategyResp_PersonaData.DiscardUnknown(m)
}

var xxx_messageInfo_GetABTestStrategyResp_PersonaData proto.InternalMessageInfo

func (m *GetABTestStrategyResp_PersonaData) GetDataMap() map[int32]string {
	if m != nil {
		return m.DataMap
	}
	return nil
}

func init() {
	proto.RegisterType((*ChannelInfo)(nil), "rcmd.music_channel.ChannelInfo")
	proto.RegisterType((*BlockOption)(nil), "rcmd.music_channel.BlockOption")
	proto.RegisterType((*ChannelTopic)(nil), "rcmd.music_channel.ChannelTopic")
	proto.RegisterType((*NotExposeChannels)(nil), "rcmd.music_channel.NotExposeChannels")
	proto.RegisterType((*GetMusicChannelRecommendationReq)(nil), "rcmd.music_channel.GetMusicChannelRecommendationReq")
	proto.RegisterType((*GetMusicChannelRecommendationResp)(nil), "rcmd.music_channel.GetMusicChannelRecommendationResp")
	proto.RegisterMapType((map[uint32]*ChannelInfo)(nil), "rcmd.music_channel.GetMusicChannelRecommendationResp.ChannelInfoMapEntry")
	proto.RegisterType((*GetSelectPageMusicChannelRcmdReq)(nil), "rcmd.music_channel.GetSelectPageMusicChannelRcmdReq")
	proto.RegisterType((*GetSelectPageMusicChannelRcmdResp)(nil), "rcmd.music_channel.GetSelectPageMusicChannelRcmdResp")
	proto.RegisterType((*GetMusicChannelPopupRcmdReq)(nil), "rcmd.music_channel.GetMusicChannelPopupRcmdReq")
	proto.RegisterType((*GetMusicChannelPopupRcmdResp)(nil), "rcmd.music_channel.GetMusicChannelPopupRcmdResp")
	proto.RegisterType((*GetCollectedChannelReq)(nil), "rcmd.music_channel.GetCollectedChannelReq")
	proto.RegisterType((*GetCollectedChannelResp)(nil), "rcmd.music_channel.GetCollectedChannelResp")
	proto.RegisterMapType((map[uint32]*ChannelInfo)(nil), "rcmd.music_channel.GetCollectedChannelResp.ChannelInfoMapEntry")
	proto.RegisterType((*GetRoomRelationUidsReq)(nil), "rcmd.music_channel.GetRoomRelationUidsReq")
	proto.RegisterType((*GetRoomRelationUidsResp)(nil), "rcmd.music_channel.GetRoomRelationUidsResp")
	proto.RegisterType((*RelationItem)(nil), "rcmd.music_channel.RelationItem")
	proto.RegisterType((*GetThemeChannelPopupRcmdReq)(nil), "rcmd.music_channel.GetThemeChannelPopupRcmdReq")
	proto.RegisterType((*GetThemeChannelPopupRcmdResp)(nil), "rcmd.music_channel.GetThemeChannelPopupRcmdResp")
	proto.RegisterType((*NotifyChannelLiveStatusReq)(nil), "rcmd.music_channel.NotifyChannelLiveStatusReq")
	proto.RegisterType((*NotifyChannelLiveStatusResp)(nil), "rcmd.music_channel.NotifyChannelLiveStatusResp")
	proto.RegisterType((*GetChannelsLocationReq)(nil), "rcmd.music_channel.GetChannelsLocationReq")
	proto.RegisterType((*ChannelLocationInfo)(nil), "rcmd.music_channel.ChannelLocationInfo")
	proto.RegisterMapType((map[uint32]string)(nil), "rcmd.music_channel.ChannelLocationInfo.UserLocationMapEntry")
	proto.RegisterType((*GetChannelsLocationResp)(nil), "rcmd.music_channel.GetChannelsLocationResp")
	proto.RegisterMapType((map[uint32]*ChannelLocationInfo)(nil), "rcmd.music_channel.GetChannelsLocationResp.ChannelLocationInfoEntry")
	proto.RegisterType((*UserSetToVIPReq)(nil), "rcmd.music_channel.UserSetToVIPReq")
	proto.RegisterType((*UserSetToVIPResp)(nil), "rcmd.music_channel.UserSetToVIPResp")
	proto.RegisterType((*GetABTestStrategyReq)(nil), "rcmd.music_channel.GetABTestStrategyReq")
	proto.RegisterType((*THEME_POP_STRATEGY)(nil), "rcmd.music_channel.THEME_POP_STRATEGY")
	proto.RegisterType((*USER_INVITE_STRATEGY)(nil), "rcmd.music_channel.USER_INVITE_STRATEGY")
	proto.RegisterType((*CP_STRATEGY)(nil), "rcmd.music_channel.CP_STRATEGY")
	proto.RegisterType((*GetABTestStrategyResp)(nil), "rcmd.music_channel.GetABTestStrategyResp")
	proto.RegisterMapType((map[string]*GetABTestStrategyResp_PersonaData)(nil), "rcmd.music_channel.GetABTestStrategyResp.PersonaDataEntry")
	proto.RegisterType((*GetABTestStrategyResp_PersonaData)(nil), "rcmd.music_channel.GetABTestStrategyResp.PersonaData")
	proto.RegisterMapType((map[int32]string)(nil), "rcmd.music_channel.GetABTestStrategyResp.PersonaData.DataMapEntry")
	proto.RegisterEnum("rcmd.music_channel.RCMDLabel", RCMDLabel_name, RCMDLabel_value)
	proto.RegisterEnum("rcmd.music_channel.FilterType", FilterType_name, FilterType_value)
	proto.RegisterEnum("rcmd.music_channel.STRATEGY", STRATEGY_name, STRATEGY_value)
	proto.RegisterEnum("rcmd.music_channel.PERSONA", PERSONA_name, PERSONA_value)
	proto.RegisterEnum("rcmd.music_channel.THEME_POP_UI", THEME_POP_UI_name, THEME_POP_UI_value)
	proto.RegisterEnum("rcmd.music_channel.USER_TYPE", USER_TYPE_name, USER_TYPE_value)
	proto.RegisterEnum("rcmd.music_channel.GetMusicChannelRecommendationReq_FilterModel", GetMusicChannelRecommendationReq_FilterModel_name, GetMusicChannelRecommendationReq_FilterModel_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MusicChannelClient is the client API for MusicChannel service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MusicChannelClient interface {
	GetMusicChannelRecommendation(ctx context.Context, in *GetMusicChannelRecommendationReq, opts ...grpc.CallOption) (*GetMusicChannelRecommendationResp, error)
	GetSelectPageMusicChannelRcmd(ctx context.Context, in *GetSelectPageMusicChannelRcmdReq, opts ...grpc.CallOption) (*GetSelectPageMusicChannelRcmdResp, error)
	GetMusicChannelPopupRcmd(ctx context.Context, in *GetMusicChannelPopupRcmdReq, opts ...grpc.CallOption) (*GetMusicChannelPopupRcmdResp, error)
	GetCollecttedChannel(ctx context.Context, in *GetCollectedChannelReq, opts ...grpc.CallOption) (*GetCollectedChannelResp, error)
	GetRoomRelationUids(ctx context.Context, in *GetRoomRelationUidsReq, opts ...grpc.CallOption) (*GetRoomRelationUidsResp, error)
	GetThemeChannelPopupRcmd(ctx context.Context, in *GetThemeChannelPopupRcmdReq, opts ...grpc.CallOption) (*GetThemeChannelPopupRcmdResp, error)
	NotifyChannelLiveStatus(ctx context.Context, in *NotifyChannelLiveStatusReq, opts ...grpc.CallOption) (*NotifyChannelLiveStatusResp, error)
	GetChannelsLocation(ctx context.Context, in *GetChannelsLocationReq, opts ...grpc.CallOption) (*GetChannelsLocationResp, error)
	UserSetToVIP(ctx context.Context, in *UserSetToVIPReq, opts ...grpc.CallOption) (*UserSetToVIPResp, error)
	GetABTestStrategy(ctx context.Context, in *GetABTestStrategyReq, opts ...grpc.CallOption) (*GetABTestStrategyResp, error)
}

type musicChannelClient struct {
	cc *grpc.ClientConn
}

func NewMusicChannelClient(cc *grpc.ClientConn) MusicChannelClient {
	return &musicChannelClient{cc}
}

func (c *musicChannelClient) GetMusicChannelRecommendation(ctx context.Context, in *GetMusicChannelRecommendationReq, opts ...grpc.CallOption) (*GetMusicChannelRecommendationResp, error) {
	out := new(GetMusicChannelRecommendationResp)
	err := c.cc.Invoke(ctx, "/rcmd.music_channel.MusicChannel/GetMusicChannelRecommendation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetSelectPageMusicChannelRcmd(ctx context.Context, in *GetSelectPageMusicChannelRcmdReq, opts ...grpc.CallOption) (*GetSelectPageMusicChannelRcmdResp, error) {
	out := new(GetSelectPageMusicChannelRcmdResp)
	err := c.cc.Invoke(ctx, "/rcmd.music_channel.MusicChannel/GetSelectPageMusicChannelRcmd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetMusicChannelPopupRcmd(ctx context.Context, in *GetMusicChannelPopupRcmdReq, opts ...grpc.CallOption) (*GetMusicChannelPopupRcmdResp, error) {
	out := new(GetMusicChannelPopupRcmdResp)
	err := c.cc.Invoke(ctx, "/rcmd.music_channel.MusicChannel/GetMusicChannelPopupRcmd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetCollecttedChannel(ctx context.Context, in *GetCollectedChannelReq, opts ...grpc.CallOption) (*GetCollectedChannelResp, error) {
	out := new(GetCollectedChannelResp)
	err := c.cc.Invoke(ctx, "/rcmd.music_channel.MusicChannel/GetCollecttedChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetRoomRelationUids(ctx context.Context, in *GetRoomRelationUidsReq, opts ...grpc.CallOption) (*GetRoomRelationUidsResp, error) {
	out := new(GetRoomRelationUidsResp)
	err := c.cc.Invoke(ctx, "/rcmd.music_channel.MusicChannel/GetRoomRelationUids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetThemeChannelPopupRcmd(ctx context.Context, in *GetThemeChannelPopupRcmdReq, opts ...grpc.CallOption) (*GetThemeChannelPopupRcmdResp, error) {
	out := new(GetThemeChannelPopupRcmdResp)
	err := c.cc.Invoke(ctx, "/rcmd.music_channel.MusicChannel/GetThemeChannelPopupRcmd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) NotifyChannelLiveStatus(ctx context.Context, in *NotifyChannelLiveStatusReq, opts ...grpc.CallOption) (*NotifyChannelLiveStatusResp, error) {
	out := new(NotifyChannelLiveStatusResp)
	err := c.cc.Invoke(ctx, "/rcmd.music_channel.MusicChannel/NotifyChannelLiveStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetChannelsLocation(ctx context.Context, in *GetChannelsLocationReq, opts ...grpc.CallOption) (*GetChannelsLocationResp, error) {
	out := new(GetChannelsLocationResp)
	err := c.cc.Invoke(ctx, "/rcmd.music_channel.MusicChannel/GetChannelsLocation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) UserSetToVIP(ctx context.Context, in *UserSetToVIPReq, opts ...grpc.CallOption) (*UserSetToVIPResp, error) {
	out := new(UserSetToVIPResp)
	err := c.cc.Invoke(ctx, "/rcmd.music_channel.MusicChannel/UserSetToVIP", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicChannelClient) GetABTestStrategy(ctx context.Context, in *GetABTestStrategyReq, opts ...grpc.CallOption) (*GetABTestStrategyResp, error) {
	out := new(GetABTestStrategyResp)
	err := c.cc.Invoke(ctx, "/rcmd.music_channel.MusicChannel/GetABTestStrategy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MusicChannelServer is the server API for MusicChannel service.
type MusicChannelServer interface {
	GetMusicChannelRecommendation(context.Context, *GetMusicChannelRecommendationReq) (*GetMusicChannelRecommendationResp, error)
	GetSelectPageMusicChannelRcmd(context.Context, *GetSelectPageMusicChannelRcmdReq) (*GetSelectPageMusicChannelRcmdResp, error)
	GetMusicChannelPopupRcmd(context.Context, *GetMusicChannelPopupRcmdReq) (*GetMusicChannelPopupRcmdResp, error)
	GetCollecttedChannel(context.Context, *GetCollectedChannelReq) (*GetCollectedChannelResp, error)
	GetRoomRelationUids(context.Context, *GetRoomRelationUidsReq) (*GetRoomRelationUidsResp, error)
	GetThemeChannelPopupRcmd(context.Context, *GetThemeChannelPopupRcmdReq) (*GetThemeChannelPopupRcmdResp, error)
	NotifyChannelLiveStatus(context.Context, *NotifyChannelLiveStatusReq) (*NotifyChannelLiveStatusResp, error)
	GetChannelsLocation(context.Context, *GetChannelsLocationReq) (*GetChannelsLocationResp, error)
	UserSetToVIP(context.Context, *UserSetToVIPReq) (*UserSetToVIPResp, error)
	GetABTestStrategy(context.Context, *GetABTestStrategyReq) (*GetABTestStrategyResp, error)
}

func RegisterMusicChannelServer(s *grpc.Server, srv MusicChannelServer) {
	s.RegisterService(&_MusicChannel_serviceDesc, srv)
}

func _MusicChannel_GetMusicChannelRecommendation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMusicChannelRecommendationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetMusicChannelRecommendation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.music_channel.MusicChannel/GetMusicChannelRecommendation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetMusicChannelRecommendation(ctx, req.(*GetMusicChannelRecommendationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetSelectPageMusicChannelRcmd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSelectPageMusicChannelRcmdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetSelectPageMusicChannelRcmd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.music_channel.MusicChannel/GetSelectPageMusicChannelRcmd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetSelectPageMusicChannelRcmd(ctx, req.(*GetSelectPageMusicChannelRcmdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetMusicChannelPopupRcmd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMusicChannelPopupRcmdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetMusicChannelPopupRcmd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.music_channel.MusicChannel/GetMusicChannelPopupRcmd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetMusicChannelPopupRcmd(ctx, req.(*GetMusicChannelPopupRcmdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetCollecttedChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCollectedChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetCollecttedChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.music_channel.MusicChannel/GetCollecttedChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetCollecttedChannel(ctx, req.(*GetCollectedChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetRoomRelationUids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRoomRelationUidsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetRoomRelationUids(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.music_channel.MusicChannel/GetRoomRelationUids",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetRoomRelationUids(ctx, req.(*GetRoomRelationUidsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetThemeChannelPopupRcmd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetThemeChannelPopupRcmdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetThemeChannelPopupRcmd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.music_channel.MusicChannel/GetThemeChannelPopupRcmd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetThemeChannelPopupRcmd(ctx, req.(*GetThemeChannelPopupRcmdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_NotifyChannelLiveStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyChannelLiveStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).NotifyChannelLiveStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.music_channel.MusicChannel/NotifyChannelLiveStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).NotifyChannelLiveStatus(ctx, req.(*NotifyChannelLiveStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetChannelsLocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelsLocationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetChannelsLocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.music_channel.MusicChannel/GetChannelsLocation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetChannelsLocation(ctx, req.(*GetChannelsLocationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_UserSetToVIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserSetToVIPReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).UserSetToVIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.music_channel.MusicChannel/UserSetToVIP",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).UserSetToVIP(ctx, req.(*UserSetToVIPReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicChannel_GetABTestStrategy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetABTestStrategyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicChannelServer).GetABTestStrategy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.music_channel.MusicChannel/GetABTestStrategy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicChannelServer).GetABTestStrategy(ctx, req.(*GetABTestStrategyReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MusicChannel_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.music_channel.MusicChannel",
	HandlerType: (*MusicChannelServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMusicChannelRecommendation",
			Handler:    _MusicChannel_GetMusicChannelRecommendation_Handler,
		},
		{
			MethodName: "GetSelectPageMusicChannelRcmd",
			Handler:    _MusicChannel_GetSelectPageMusicChannelRcmd_Handler,
		},
		{
			MethodName: "GetMusicChannelPopupRcmd",
			Handler:    _MusicChannel_GetMusicChannelPopupRcmd_Handler,
		},
		{
			MethodName: "GetCollecttedChannel",
			Handler:    _MusicChannel_GetCollecttedChannel_Handler,
		},
		{
			MethodName: "GetRoomRelationUids",
			Handler:    _MusicChannel_GetRoomRelationUids_Handler,
		},
		{
			MethodName: "GetThemeChannelPopupRcmd",
			Handler:    _MusicChannel_GetThemeChannelPopupRcmd_Handler,
		},
		{
			MethodName: "NotifyChannelLiveStatus",
			Handler:    _MusicChannel_NotifyChannelLiveStatus_Handler,
		},
		{
			MethodName: "GetChannelsLocation",
			Handler:    _MusicChannel_GetChannelsLocation_Handler,
		},
		{
			MethodName: "UserSetToVIP",
			Handler:    _MusicChannel_UserSetToVIP_Handler,
		},
		{
			MethodName: "GetABTestStrategy",
			Handler:    _MusicChannel_GetABTestStrategy_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "music-channel/rcmd_music_channel.proto",
}

func init() {
	proto.RegisterFile("music-channel/rcmd_music_channel.proto", fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb)
}

var fileDescriptor_rcmd_music_channel_6ff28dc4f42a67cb = []byte{
	// 2882 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x5a, 0x51, 0x6f, 0x1b, 0xc7,
	0xf1, 0x37, 0x45, 0x49, 0xe4, 0x0d, 0x49, 0xe9, 0xbc, 0x92, 0x65, 0x9a, 0x8e, 0x63, 0x99, 0x89,
	0x1d, 0xc5, 0xce, 0x9f, 0xfe, 0x43, 0xb1, 0xdb, 0xc0, 0x48, 0x1a, 0xc8, 0x12, 0x25, 0x13, 0x11,
	0x25, 0xf5, 0x24, 0xd9, 0x70, 0x90, 0xe4, 0x70, 0xba, 0x5b, 0x92, 0x0b, 0xdd, 0xdd, 0x9e, 0x6e,
	0xf7, 0x24, 0xb3, 0x40, 0x1f, 0x0a, 0xf4, 0xb1, 0x40, 0xdf, 0xfb, 0x52, 0xa0, 0x4f, 0x45, 0x1f,
	0xfb, 0x56, 0xa0, 0x5f, 0xa2, 0xfd, 0x00, 0x7d, 0x2a, 0x0a, 0xf4, 0xa1, 0x40, 0x3f, 0x42, 0xb1,
	0xbb, 0x47, 0xf2, 0x28, 0xde, 0x49, 0xb2, 0x93, 0x87, 0xbc, 0xc4, 0xbc, 0x99, 0xd9, 0xd9, 0x99,
	0xd9, 0xd9, 0xdf, 0xcc, 0x6c, 0x04, 0x0f, 0xbc, 0x88, 0x11, 0xfb, 0xff, 0xec, 0x9e, 0xe5, 0xfb,
	0xd8, 0x7d, 0x1c, 0xda, 0x9e, 0x63, 0x4a, 0x92, 0x19, 0x93, 0x1a, 0x41, 0x48, 0x39, 0x45, 0x48,
	0x70, 0x1a, 0x63, 0x9c, 0xda, 0x82, 0x4d, 0x3d, 0x8f, 0xfa, 0x8f, 0xd5, 0x3f, 0x4a, 0xb0, 0xf6,
	0x80, 0xd3, 0x60, 0x24, 0xa3, 0x14, 0xc6, 0x1f, 0xa6, 0x6b, 0x1d, 0x0d, 0x14, 0xd6, 0xff, 0x9b,
	0x83, 0xd2, 0xba, 0xa2, 0xb7, 0xfc, 0x0e, 0x45, 0x37, 0x60, 0x96, 0x5b, 0x5d, 0x93, 0x38, 0xd5,
	0xdc, 0x72, 0x6e, 0xa5, 0x62, 0xcc, 0x70, 0xab, 0xdb, 0x72, 0xd0, 0x5d, 0x28, 0x85, 0xd8, 0xb6,
	0x5c, 0xd7, 0xec, 0xb8, 0x56, 0xb7, 0x3a, 0x25, 0x79, 0xa0, 0x48, 0x9b, 0xae, 0xd5, 0x45, 0x8f,
	0x20, 0xef, 0x52, 0xbb, 0x9a, 0x5f, 0xce, 0xad, 0x94, 0x56, 0x6f, 0x35, 0xa4, 0x99, 0xb1, 0x41,
	0xdb, 0xd4, 0xb6, 0x38, 0xa1, 0xbe, 0xd0, 0x6f, 0x08, 0x29, 0xf4, 0x33, 0x28, 0x49, 0x83, 0xa4,
	0x21, 0xac, 0x3a, 0xbd, 0x9c, 0x5f, 0x99, 0x5b, 0xbd, 0xd3, 0x98, 0xf4, 0xad, 0x61, 0xac, 0xb7,
	0x37, 0xb6, 0x85, 0x94, 0x01, 0x82, 0x2b, 0x7f, 0x32, 0x74, 0x13, 0x0a, 0x1e, 0xe6, 0x96, 0xb0,
	0x72, 0x66, 0x39, 0xb7, 0xa2, 0x19, 0xb3, 0xe2, 0xb3, 0xe5, 0xa0, 0x0f, 0xa0, 0x12, 0x62, 0x57,
	0xee, 0x66, 0x46, 0xc4, 0x61, 0xd5, 0xd9, 0xe5, 0xfc, 0x4a, 0xc5, 0x28, 0x0f, 0x88, 0x87, 0xc4,
	0x61, 0xf5, 0x35, 0x28, 0x3d, 0x77, 0xa9, 0x7d, 0xbc, 0x1b, 0x08, 0x12, 0xba, 0x05, 0xc5, 0x23,
	0xf1, 0x39, 0xf2, 0xb9, 0x20, 0xbf, 0x5b, 0x8e, 0xd8, 0x07, 0xbb, 0xd8, 0x13, 0x1c, 0xe5, 0xf1,
	0xac, 0xf8, 0x6c, 0x39, 0xf5, 0x63, 0x28, 0xc7, 0x41, 0x3b, 0x10, 0x61, 0x56, 0x51, 0x3b, 0x1a,
	0x8b, 0xda, 0x51, 0xcb, 0x41, 0x1b, 0x50, 0x51, 0xaa, 0xa9, 0xdc, 0x8a, 0x55, 0xa7, 0x96, 0xf3,
	0x2b, 0xa5, 0xd5, 0xbb, 0x69, 0x9e, 0x26, 0x4c, 0x32, 0xca, 0x47, 0xa3, 0x0f, 0x56, 0x77, 0xe1,
	0xfa, 0x0e, 0xe5, 0xcd, 0x37, 0x01, 0x65, 0x38, 0xde, 0x95, 0xa1, 0xdb, 0xa0, 0x75, 0x88, 0xcb,
	0x71, 0x38, 0xd8, 0x54, 0x33, 0x8a, 0x8a, 0xd0, 0x72, 0xd0, 0x33, 0xa8, 0xf9, 0x94, 0x9b, 0x58,
	0x2e, 0x19, 0x1e, 0x3b, 0x71, 0x4c, 0x97, 0x30, 0x2e, 0x8d, 0xa8, 0x18, 0x4b, 0xfe, 0x39, 0x9d,
	0x2d, 0x67, 0x9b, 0x30, 0x5e, 0xff, 0xbd, 0x06, 0xcb, 0x5b, 0x98, 0xb7, 0x85, 0x71, 0x31, 0xc7,
	0xc0, 0xe2, 0x24, 0xb1, 0xef, 0xc8, 0x18, 0x1a, 0xf8, 0x04, 0xe9, 0x90, 0x8f, 0x86, 0xce, 0x8a,
	0x9f, 0x68, 0x11, 0x66, 0x5c, 0xe2, 0x11, 0x1e, 0x07, 0x4a, 0x7d, 0x4c, 0x06, 0x20, 0xff, 0x0e,
	0x01, 0x10, 0xc9, 0x67, 0xbb, 0x04, 0xfb, 0xdc, 0xe4, 0xfd, 0x00, 0x57, 0xa7, 0x55, 0xf2, 0x29,
	0xd2, 0x41, 0x3f, 0xc0, 0xe8, 0x3e, 0xcc, 0xc5, 0x02, 0xa7, 0x38, 0x64, 0x84, 0xfa, 0x32, 0x2d,
	0x2a, 0x46, 0x45, 0x51, 0x5f, 0x2a, 0xa2, 0x88, 0x99, 0x67, 0x85, 0xc7, 0x98, 0x8b, 0x98, 0xcd,
	0x4a, 0x89, 0xa2, 0x22, 0xb4, 0x1c, 0xf4, 0x09, 0xa0, 0x41, 0xa0, 0x02, 0xcb, 0x3e, 0xb6, 0xba,
	0x58, 0x48, 0x15, 0x64, 0x64, 0xf5, 0x98, 0xb3, 0xa7, 0x18, 0x2d, 0x07, 0xdd, 0x01, 0x70, 0xf0,
	0x51, 0xd4, 0x55, 0xd7, 0xa1, 0x28, 0x75, 0x69, 0x92, 0x22, 0x6f, 0x83, 0x0e, 0x79, 0x86, 0xdf,
	0x54, 0x35, 0x15, 0x1f, 0x86, 0xdf, 0x48, 0x1f, 0x2c, 0x8e, 0xbb, 0x34, 0xec, 0x0b, 0xbd, 0x10,
	0xfb, 0x10, 0x93, 0x54, 0xae, 0xa9, 0x14, 0x62, 0xd5, 0x92, 0x3c, 0xa0, 0x59, 0x99, 0x43, 0x0c,
	0x2d, 0x43, 0x99, 0x30, 0xb3, 0x43, 0x42, 0xc6, 0xcd, 0x2e, 0xe6, 0xd5, 0xf2, 0x72, 0x6e, 0xa5,
	0x68, 0x00, 0x61, 0x9b, 0x82, 0xb4, 0x85, 0x39, 0xfa, 0x12, 0x4a, 0x71, 0x2e, 0xc8, 0xf8, 0x54,
	0x96, 0x73, 0x2b, 0x73, 0xab, 0xef, 0xa7, 0xc5, 0x78, 0x53, 0x8a, 0x89, 0x98, 0x19, 0xd0, 0x19,
	0xfe, 0x46, 0x9f, 0xc2, 0x52, 0x46, 0xae, 0xcc, 0x49, 0x53, 0x16, 0xf0, 0x64, 0xa2, 0x88, 0x45,
	0x1d, 0xea, 0xba, 0xf4, 0x6c, 0x62, 0xd1, 0xbc, 0x5a, 0xa4, 0xb8, 0xe3, 0x8b, 0xb6, 0x60, 0x6e,
	0x20, 0x2d, 0x01, 0x8a, 0x55, 0x75, 0x99, 0x11, 0xcb, 0x69, 0xd6, 0x26, 0xaf, 0x98, 0x51, 0xb1,
	0x13, 0x5f, 0x0c, 0xdd, 0x83, 0x32, 0xf6, 0x85, 0xcb, 0x8c, 0x46, 0xa1, 0x8d, 0xab, 0xd7, 0xe5,
	0x41, 0x95, 0x24, 0x6d, 0x5f, 0x92, 0xc6, 0xaf, 0x08, 0x9a, 0xbc, 0x22, 0x84, 0x99, 0x11, 0xc3,
	0xa1, 0xe9, 0xc6, 0xf8, 0x64, 0x5a, 0x11, 0xef, 0x99, 0x34, 0xc0, 0x7e, 0x75, 0x41, 0xc6, 0x78,
	0x89, 0xb0, 0x43, 0x86, 0xc3, 0x01, 0x7e, 0xad, 0x45, 0xbc, 0xb7, 0x1b, 0x60, 0x1f, 0x7d, 0x07,
	0xd5, 0xc9, 0xeb, 0xc5, 0x94, 0xef, 0x8b, 0x12, 0x00, 0xef, 0xa7, 0xb9, 0x33, 0x71, 0x89, 0x8d,
	0x1b, 0xe7, 0xef, 0x20, 0x93, 0x41, 0xba, 0x07, 0xe5, 0xd8, 0x70, 0x8f, 0x3a, 0xd8, 0xad, 0xde,
	0x90, 0xc9, 0x12, 0x9f, 0x71, 0x5b, 0x90, 0xd0, 0x3a, 0xcc, 0xc6, 0xe0, 0xb9, 0x24, 0xe3, 0xf7,
	0xa8, 0x31, 0x86, 0xf7, 0x8d, 0x14, 0xbc, 0xdf, 0xb2, 0x3c, 0xac, 0xa0, 0x34, 0x5e, 0x8a, 0xbe,
	0x81, 0x79, 0xdb, 0xb5, 0x18, 0x23, 0x9d, 0xfe, 0x00, 0x8a, 0x6f, 0x4a, 0x6d, 0x9f, 0x5e, 0xae,
	0x6d, 0x3d, 0x5e, 0x28, 0x35, 0x0a, 0xab, 0x8d, 0x39, 0x3b, 0x49, 0x62, 0xa8, 0x01, 0x0b, 0xac,
	0x47, 0xb0, 0xeb, 0x98, 0xb1, 0x33, 0x67, 0x34, 0x74, 0x58, 0xb5, 0xba, 0x9c, 0x5f, 0xd1, 0x8c,
	0xeb, 0x8a, 0xa5, 0xf2, 0xf1, 0x95, 0x60, 0xd4, 0x3f, 0x82, 0xd2, 0x66, 0xc2, 0xc3, 0x12, 0x14,
	0xda, 0x5c, 0xfe, 0xd4, 0xaf, 0x89, 0x8f, 0xaf, 0x7a, 0xea, 0x23, 0x57, 0xff, 0x55, 0x1e, 0xee,
	0x5d, 0x82, 0x50, 0x2c, 0x40, 0x0f, 0x60, 0xfe, 0x7c, 0x5e, 0xe6, 0x64, 0x5e, 0x0e, 0x12, 0x29,
	0xce, 0xc8, 0xfb, 0x30, 0x77, 0x44, 0x39, 0xa7, 0x9e, 0x19, 0x62, 0xcb, 0xee, 0x61, 0x05, 0xf5,
	0x45, 0xa3, 0xa2, 0xa8, 0x86, 0x22, 0xa2, 0x27, 0x50, 0x64, 0xd8, 0xed, 0x98, 0x57, 0x2a, 0x72,
	0x05, 0x21, 0xba, 0x4d, 0x6d, 0xc4, 0x40, 0x1f, 0x1a, 0xe1, 0x77, 0xa8, 0xe9, 0x59, 0x81, 0xac,
	0x76, 0xa5, 0xd5, 0x56, 0x5a, 0x86, 0x5c, 0xea, 0x55, 0x23, 0x51, 0xaa, 0xdb, 0x56, 0xd0, 0xf4,
	0x79, 0xd8, 0x37, 0x06, 0x37, 0x2a, 0x26, 0xd6, 0x8e, 0x60, 0x21, 0x45, 0x4c, 0x60, 0xd2, 0x31,
	0xee, 0x0f, 0x30, 0xfb, 0x18, 0xf7, 0xd1, 0x53, 0x98, 0x39, 0xb5, 0xdc, 0x08, 0x4b, 0x8f, 0x33,
	0x50, 0x39, 0xa1, 0xc9, 0x50, 0xd2, 0xcf, 0xa6, 0x3e, 0xcb, 0xd5, 0xff, 0x96, 0x93, 0x55, 0x62,
	0x1f, 0xbb, 0xd8, 0xe6, 0x7b, 0x56, 0x17, 0x8f, 0xd9, 0x6d, 0x7b, 0x4e, 0x7a, 0x95, 0xb8, 0x05,
	0x45, 0x01, 0x72, 0xbe, 0xe5, 0xa9, 0x4d, 0x35, 0x43, 0x80, 0xde, 0x8e, 0xe5, 0xc9, 0xdb, 0x2a,
	0x6b, 0xad, 0xe4, 0xe5, 0xd5, 0x6d, 0x15, 0x04, 0xc9, 0xfc, 0x08, 0xe6, 0x83, 0x90, 0x7a, 0x54,
	0x5e, 0x53, 0x99, 0x80, 0x32, 0x8c, 0x9a, 0x31, 0x37, 0x24, 0xcb, 0xac, 0x1b, 0x87, 0xf8, 0x99,
	0x73, 0x10, 0x7f, 0x07, 0x60, 0xb8, 0x85, 0x6a, 0x0d, 0x34, 0x43, 0x1b, 0xec, 0xc1, 0xea, 0xbf,
	0xcd, 0xc9, 0xbc, 0xba, 0xc8, 0x27, 0x16, 0x08, 0x25, 0xa3, 0xbc, 0x8a, 0x7d, 0xd3, 0x86, 0x29,
	0x95, 0x6c, 0x4d, 0xa6, 0xc6, 0x5a, 0x93, 0x5b, 0x50, 0xc4, 0x61, 0x68, 0xda, 0xd4, 0x51, 0xee,
	0x55, 0x8c, 0x02, 0x0e, 0xc3, 0x75, 0xea, 0x60, 0xd9, 0x66, 0x84, 0xa1, 0xe9, 0xb1, 0xae, 0xac,
	0x6d, 0x9a, 0x31, 0x8b, 0xc3, 0xb0, 0xcd, 0xba, 0xf5, 0x6d, 0xb8, 0x7d, 0x2e, 0x25, 0xf6, 0x68,
	0x10, 0x05, 0xd9, 0xf1, 0x1d, 0x73, 0x7f, 0x6a, 0xdc, 0xfd, 0xfa, 0xef, 0x72, 0xf0, 0x5e, 0xb6,
	0xba, 0xef, 0xe1, 0xda, 0x1d, 0x80, 0x4e, 0x48, 0xb0, 0xef, 0x88, 0x9e, 0x2b, 0x76, 0x4e, 0x53,
	0x94, 0x43, 0x92, 0xd2, 0x94, 0x4d, 0xa7, 0x34, 0x65, 0x7f, 0xc9, 0xc3, 0xd2, 0x16, 0xe6, 0xeb,
	0xd4, 0x15, 0xd1, 0xc7, 0xce, 0xf0, 0x0a, 0x5c, 0xbd, 0xd9, 0x38, 0xd7, 0x26, 0xe4, 0xaf, 0xd0,
	0x26, 0x4c, 0x5f, 0xda, 0x26, 0xcc, 0x5c, 0xa9, 0x4d, 0x98, 0xcd, 0x68, 0x13, 0xe2, 0x3e, 0xa0,
	0x30, 0xea, 0x03, 0xce, 0x57, 0xf3, 0xe2, 0x65, 0xd5, 0x5c, 0xfb, 0x01, 0xab, 0x39, 0x64, 0x57,
	0xf3, 0x8b, 0x5b, 0xc6, 0xd2, 0x85, 0x2d, 0xe3, 0x7f, 0xa6, 0xe0, 0x66, 0xea, 0xd9, 0xfd, 0x58,
	0x60, 0x98, 0x64, 0xc2, 0xf0, 0x97, 0x19, 0x30, 0x9c, 0xe6, 0xcb, 0x8f, 0x06, 0x7c, 0x4f, 0xe4,
	0x55, 0x31, 0xa8, 0x08, 0xcb, 0xe8, 0x0a, 0x89, 0xab, 0x72, 0x13, 0x0a, 0x21, 0x3e, 0x31, 0x47,
	0xd7, 0x65, 0x36, 0xc4, 0x27, 0x87, 0x0a, 0x18, 0x42, 0x4a, 0x3d, 0x75, 0xff, 0xd4, 0x00, 0x50,
	0x14, 0x04, 0xb1, 0x70, 0xa2, 0x97, 0xca, 0x4f, 0xf4, 0x52, 0xf5, 0x23, 0x79, 0xc2, 0x93, 0x5b,
	0xb2, 0x40, 0xb4, 0x74, 0xc3, 0xeb, 0x4d, 0x38, 0xf6, 0x98, 0x3c, 0xe0, 0x8c, 0x96, 0x6e, 0xb0,
	0xba, 0xc5, 0xb1, 0x67, 0x0c, 0x61, 0x41, 0x7c, 0xb1, 0xfa, 0x1f, 0x72, 0x50, 0x4e, 0xf2, 0x53,
	0x2e, 0x7e, 0x26, 0x04, 0xdd, 0x87, 0x39, 0xd7, 0x62, 0xdc, 0xe4, 0xc4, 0xc3, 0x8c, 0x5b, 0x5e,
	0x10, 0x3b, 0x51, 0x11, 0xd4, 0x83, 0x01, 0x51, 0x40, 0xd1, 0x58, 0xfb, 0x19, 0xe3, 0x6d, 0x39,
	0xd9, 0x5b, 0x8a, 0x58, 0x31, 0xea, 0x77, 0x55, 0x25, 0x52, 0xf3, 0x65, 0x51, 0x10, 0x44, 0x95,
	0xa8, 0x1f, 0x4b, 0x48, 0x3e, 0xe8, 0x61, 0x0f, 0x7f, 0x7f, 0x48, 0x16, 0xc8, 0x29, 0xce, 0x6b,
	0x2c, 0xee, 0x5a, 0x88, 0x4f, 0xe2, 0xa8, 0xff, 0x43, 0x21, 0x76, 0xc6, 0x6e, 0xdf, 0x03, 0xb1,
	0x27, 0x20, 0x39, 0x3f, 0x09, 0xc9, 0x22, 0x2d, 0x42, 0x1c, 0xf9, 0xe7, 0x60, 0xbb, 0x14, 0xd3,
	0xa4, 0xc8, 0xb9, 0x41, 0x7e, 0xe6, 0x2d, 0x07, 0xf9, 0xfa, 0x37, 0x50, 0xdb, 0xa1, 0x9c, 0x74,
	0xfa, 0xb1, 0x77, 0xdb, 0xe4, 0x14, 0xef, 0x73, 0x8b, 0x47, 0x32, 0x9b, 0x2f, 0xf1, 0xee, 0x2e,
	0x94, 0x5c, 0x72, 0x8a, 0x4d, 0x26, 0x17, 0xc4, 0x78, 0x01, 0xee, 0x50, 0x45, 0xfd, 0x73, 0xb8,
	0x9d, 0xa9, 0x5d, 0x05, 0x8f, 0x30, 0x93, 0x45, 0xb6, 0x8d, 0x19, 0x93, 0xea, 0x8b, 0x86, 0x46,
	0xd8, 0xbe, 0x22, 0xd4, 0x0d, 0x55, 0x90, 0x06, 0x8d, 0x79, 0x0c, 0x2c, 0xe9, 0x87, 0x9c, 0x82,
	0x72, 0x53, 0x29, 0x28, 0x57, 0xff, 0x57, 0x6e, 0x08, 0x0f, 0x49, 0xa4, 0x12, 0xa1, 0x1e, 0xac,
	0xb7, 0x09, 0xef, 0xc7, 0x03, 0x7d, 0x29, 0xa6, 0xad, 0x13, 0xde, 0x47, 0x3d, 0xb8, 0x3e, 0x3e,
	0xad, 0x08, 0x10, 0x53, 0xef, 0x09, 0x9f, 0x5f, 0x80, 0x1d, 0xc9, 0x6d, 0x1a, 0xc9, 0x69, 0x66,
	0x88, 0x60, 0xf3, 0xd1, 0x38, 0xb5, 0xf6, 0x1c, 0x16, 0xd3, 0x04, 0x53, 0x30, 0x6c, 0x31, 0x89,
	0x61, 0x5a, 0x12, 0xa2, 0x7e, 0x1d, 0x97, 0x84, 0x89, 0xe8, 0xb1, 0x00, 0xbd, 0x81, 0x1b, 0xc3,
	0x59, 0x62, 0xe0, 0x8c, 0x80, 0xe5, 0x18, 0x37, 0x36, 0xb2, 0x20, 0x39, 0x45, 0x57, 0x9a, 0x97,
	0xca, 0xab, 0x05, 0x7b, 0x92, 0x53, 0xa3, 0x50, 0xcd, 0x5a, 0x90, 0xe2, 0xdd, 0x17, 0xe3, 0x08,
	0xfd, 0xd1, 0x15, 0xa3, 0x9c, 0x0c, 0xc3, 0x27, 0x30, 0x2f, 0x42, 0xb9, 0x8f, 0xf9, 0x01, 0x7d,
	0xd9, 0xda, 0x13, 0xc9, 0x73, 0x0b, 0x8a, 0x51, 0xb2, 0x12, 0x6a, 0x46, 0x21, 0x22, 0x2a, 0x3b,
	0x10, 0xe8, 0xe3, 0xd2, 0x2c, 0xa8, 0xff, 0x3d, 0x07, 0x8b, 0x5b, 0x98, 0xaf, 0x3d, 0x3f, 0xc0,
	0x8c, 0xef, 0xf3, 0xd0, 0xe2, 0xb8, 0xdb, 0x17, 0x7a, 0xd6, 0xa0, 0xc2, 0xe2, 0x4f, 0x85, 0x54,
	0x39, 0xd9, 0x28, 0xbc, 0x97, 0x66, 0xe5, 0xfe, 0x81, 0xb1, 0x76, 0xd0, 0xdc, 0x7a, 0x6d, 0x94,
	0x07, 0x4b, 0x64, 0x57, 0x1d, 0xe7, 0xf1, 0xd4, 0x28, 0x8f, 0x9f, 0xc3, 0x5c, 0x80, 0x43, 0x46,
	0x7d, 0xcb, 0xec, 0x88, 0xf9, 0x4c, 0x01, 0xc3, 0xdc, 0xea, 0xed, 0x34, 0xad, 0x7b, 0x4d, 0x63,
	0x7f, 0x77, 0x67, 0xcd, 0xa8, 0xc4, 0x4b, 0x36, 0xe5, 0x0a, 0x91, 0xcb, 0x03, 0x1d, 0xc7, 0xb8,
	0xcf, 0xe2, 0x46, 0xbd, 0x14, 0xd3, 0xbe, 0xc2, 0x7d, 0x56, 0xff, 0x73, 0x0e, 0xd0, 0xc1, 0x8b,
	0x66, 0xbb, 0x69, 0xee, 0xed, 0xee, 0x99, 0x03, 0xeb, 0xd0, 0x97, 0x00, 0x11, 0x19, 0xf6, 0x66,
	0xca, 0x9f, 0xd4, 0x2a, 0x32, 0x5a, 0x7b, 0xd8, 0x32, 0xb4, 0x88, 0x0c, 0x3a, 0xb7, 0xbb, 0x50,
	0x26, 0x1d, 0x53, 0x20, 0x2a, 0xf1, 0x4d, 0xe2, 0xc5, 0x90, 0xa0, 0x91, 0x8e, 0x81, 0x4f, 0x5a,
	0x7e, 0xcb, 0x43, 0xcf, 0x40, 0x93, 0x97, 0x68, 0xd8, 0x20, 0x66, 0xa0, 0xd5, 0xe1, 0x7e, 0xd3,
	0x30, 0x0f, 0x5e, 0xef, 0x35, 0x8d, 0xa2, 0x90, 0x17, 0x6d, 0x55, 0x7d, 0x1b, 0x16, 0x25, 0xb9,
	0xb5, 0xf3, 0xb2, 0x75, 0xd0, 0x1c, 0x59, 0xfd, 0x04, 0x96, 0x7a, 0x44, 0x76, 0x4e, 0x38, 0x24,
	0x1e, 0xf6, 0xb9, 0xe5, 0x9a, 0xdd, 0x90, 0x46, 0x41, 0x0c, 0x29, 0x8b, 0x3d, 0x22, 0xba, 0xa6,
	0x21, 0x73, 0x4b, 0xf0, 0xea, 0x0d, 0x28, 0xad, 0x27, 0x5c, 0xbf, 0x0b, 0x25, 0xd6, 0xa3, 0x67,
	0xe6, 0x19, 0xf1, 0x1d, 0x7a, 0x16, 0xaf, 0x04, 0x41, 0x7a, 0x25, 0x29, 0xf5, 0x3f, 0xcd, 0xc2,
	0x8d, 0x94, 0x3c, 0x60, 0xc1, 0x0f, 0x91, 0x08, 0x07, 0x80, 0xb8, 0xa8, 0x31, 0x66, 0x40, 0x03,
	0x73, 0xc0, 0x89, 0xd3, 0xfe, 0xc1, 0xc5, 0x07, 0x30, 0xd4, 0xa8, 0x4b, 0x0d, 0x7b, 0x34, 0x18,
	0x18, 0x87, 0x3e, 0x84, 0x39, 0x11, 0x18, 0x19, 0x0b, 0x33, 0x8c, 0x5c, 0x15, 0xf1, 0xa2, 0x51,
	0xee, 0x11, 0x2e, 0x83, 0x60, 0x44, 0x2e, 0x46, 0x5f, 0xc3, 0xa2, 0x3c, 0x12, 0xe2, 0x9f, 0x12,
	0x8e, 0x47, 0xbb, 0x4f, 0xcb, 0xdd, 0x57, 0x32, 0x4f, 0xe7, 0xdc, 0x31, 0x18, 0x48, 0x68, 0x69,
	0x49, 0x25, 0x43, 0x0b, 0x1e, 0x01, 0x22, 0x1d, 0x93, 0x45, 0x21, 0x56, 0x2f, 0x3d, 0xea, 0x58,
	0x66, 0xa4, 0x15, 0xf3, 0xa4, 0xb3, 0x1f, 0x85, 0x58, 0x5c, 0x38, 0x69, 0x0c, 0xfa, 0x76, 0x94,
	0xb7, 0x8e, 0xc5, 0x2d, 0x39, 0x1f, 0x96, 0x56, 0x9f, 0x65, 0xa0, 0xd1, 0xe4, 0x41, 0x34, 0xf6,
	0xd4, 0xea, 0x0d, 0x8b, 0x5b, 0x0a, 0x83, 0x06, 0x39, 0x2f, 0x28, 0xe8, 0x05, 0xcc, 0xdb, 0x34,
	0x0a, 0xdc, 0x84, 0x8b, 0x85, 0x0b, 0x3a, 0xbf, 0x44, 0x64, 0xe7, 0xd4, 0xba, 0xc1, 0x5e, 0xb5,
	0x08, 0xf4, 0xf3, 0x5b, 0x25, 0xd1, 0x4b, 0x53, 0xe8, 0xf5, 0xd5, 0x38, 0x7a, 0x3d, 0x7d, 0x27,
	0x3f, 0x12, 0x58, 0x56, 0xfb, 0x63, 0x0e, 0x4a, 0x09, 0x16, 0xfa, 0x16, 0x8a, 0x22, 0x4e, 0xb2,
	0x0e, 0x29, 0xe4, 0x7e, 0xfe, 0x4e, 0x7b, 0x34, 0xc4, 0x7f, 0x86, 0xd5, 0xa8, 0xe0, 0xa8, 0xaf,
	0xda, 0x33, 0x28, 0x27, 0x19, 0x49, 0x0f, 0x67, 0x2e, 0xa9, 0x3e, 0x0f, 0xff, 0x39, 0x05, 0xda,
	0xb0, 0xe1, 0x40, 0x45, 0x98, 0xde, 0xa1, 0x3e, 0xd6, 0xaf, 0xa1, 0x9b, 0xb0, 0xb0, 0x65, 0xf9,
	0xdd, 0xc3, 0xe0, 0x15, 0xe1, 0xbd, 0x17, 0xd4, 0xc3, 0xbb, 0x67, 0x3e, 0x0e, 0xf5, 0x1c, 0xba,
	0x01, 0xd7, 0xd7, 0x7b, 0x16, 0x1f, 0x27, 0x4f, 0x09, 0xf9, 0x4d, 0xf9, 0x88, 0x79, 0x28, 0x73,
	0x2b, 0x06, 0x7b, 0x3d, 0x8f, 0x4a, 0x50, 0xd8, 0xa6, 0xf6, 0x7e, 0x8f, 0x9e, 0xe9, 0xd3, 0xe8,
	0x16, 0xdc, 0x68, 0xf3, 0x34, 0xb9, 0x19, 0xb4, 0x04, 0xa8, 0xcd, 0x9b, 0xa7, 0x38, 0x6c, 0x8a,
	0x5e, 0x7a, 0x40, 0x9f, 0x45, 0x55, 0x58, 0x6c, 0xf3, 0xb6, 0xe5, 0xf7, 0xc5, 0x02, 0x36, 0x5a,
	0x51, 0x40, 0xf7, 0xe0, 0x4e, 0x82, 0xb3, 0x99, 0x7c, 0x42, 0x55, 0x56, 0x15, 0xd1, 0xfb, 0x50,
	0x6b, 0xf3, 0xad, 0x08, 0x33, 0xf6, 0x9a, 0x46, 0xdb, 0xe4, 0x18, 0x8f, 0xf1, 0x35, 0x74, 0x17,
	0x6e, 0xb7, 0xb9, 0x81, 0x6d, 0xec, 0x73, 0xc1, 0x3d, 0xb0, 0x8e, 0x62, 0x81, 0x0d, 0x1a, 0x6d,
	0x10, 0x1d, 0xd0, 0x7b, 0x50, 0x6d, 0xf3, 0x17, 0x94, 0x6f, 0x59, 0x1e, 0x7e, 0x41, 0xf9, 0x18,
	0xb7, 0x84, 0x16, 0x41, 0x6f, 0xf3, 0x4d, 0x39, 0xbd, 0xef, 0x76, 0xd4, 0xbf, 0x7a, 0x19, 0x21,
	0x98, 0x1b, 0x28, 0x55, 0x46, 0xe9, 0x95, 0x87, 0x5f, 0x00, 0x8c, 0x46, 0x50, 0x11, 0x93, 0x96,
	0x7f, 0x6a, 0xb9, 0xc4, 0xd1, 0xaf, 0x21, 0x1d, 0xca, 0xbb, 0x11, 0x0f, 0x22, 0xae, 0x04, 0xf4,
	0x9c, 0xa0, 0xa8, 0xd9, 0x31, 0xa6, 0x4c, 0x3d, 0xfc, 0x0e, 0x8a, 0x43, 0xfc, 0x13, 0x8b, 0x77,
	0x5e, 0xae, 0x6d, 0xb7, 0x36, 0xf4, 0x6b, 0xa8, 0x02, 0xda, 0x10, 0x60, 0xf4, 0x1c, 0x9a, 0x87,
	0x52, 0xe2, 0xc6, 0xeb, 0x53, 0xe8, 0x3a, 0x54, 0x7e, 0x7e, 0xd8, 0x3c, 0x6c, 0x9a, 0x71, 0x05,
	0xd2, 0xf3, 0xc2, 0xbc, 0xf5, 0x3d, 0xf3, 0x65, 0xd3, 0xd8, 0x6f, 0xed, 0xee, 0x98, 0x07, 0xaf,
	0x76, 0xf5, 0xe9, 0x87, 0xff, 0xce, 0x41, 0x21, 0x96, 0x10, 0x61, 0x8d, 0x4c, 0x8f, 0x9b, 0x41,
	0x48, 0x8f, 0xac, 0x23, 0xe2, 0x12, 0xde, 0x37, 0xe5, 0xbc, 0x2b, 0xe7, 0x9e, 0x9f, 0x0a, 0x93,
	0x3f, 0x86, 0xfb, 0x17, 0x88, 0x04, 0x21, 0x76, 0x4c, 0x66, 0xd3, 0x10, 0xeb, 0x39, 0x71, 0xe2,
	0x4a, 0xb4, 0x6b, 0x8f, 0x6b, 0x99, 0x42, 0xb7, 0xe1, 0xa6, 0x64, 0x75, 0x5c, 0x8b, 0xf5, 0x4c,
	0xcb, 0xb6, 0x69, 0xe4, 0x73, 0x59, 0xed, 0xf5, 0x7c, 0xaa, 0x15, 0x2e, 0x3d, 0x1b, 0xad, 0x9f,
	0x46, 0x35, 0x58, 0x52, 0x70, 0xc8, 0x4c, 0x46, 0x6d, 0x62, 0xb9, 0x72, 0x67, 0x62, 0x73, 0x7d,
	0x46, 0x6e, 0x1b, 0xf3, 0x8e, 0x5c, 0xcb, 0x3e, 0x16, 0x7a, 0x9c, 0xc8, 0xe6, 0xfa, 0xec, 0xc3,
	0x65, 0x28, 0x27, 0x8b, 0x22, 0x2a, 0x40, 0x7e, 0x77, 0x5b, 0xc4, 0xb2, 0x00, 0xf9, 0x9d, 0xe6,
	0x2b, 0x3d, 0xf7, 0xf0, 0x33, 0xd0, 0x86, 0x55, 0x0d, 0x95, 0xa1, 0xb8, 0xbb, 0xbd, 0x61, 0x0a,
	0x82, 0x7e, 0x4d, 0x04, 0xd8, 0x68, 0xae, 0xaf, 0x6d, 0x6f, 0x2b, 0x42, 0x4e, 0xb0, 0x77, 0x9a,
	0xaf, 0xd4, 0xd7, 0xd4, 0xea, 0x5f, 0x35, 0x28, 0x27, 0x1f, 0x8d, 0xd0, 0x6f, 0x72, 0x70, 0xe7,
	0xc2, 0xa7, 0x4a, 0xf4, 0xe4, 0x1d, 0x5e, 0x37, 0x4f, 0x6a, 0x4f, 0xdf, 0xe9, 0x4d, 0x74, 0x60,
	0x4e, 0xf6, 0xbb, 0x5d, 0xa6, 0x39, 0x17, 0x3e, 0x5f, 0x66, 0x9a, 0x73, 0xc9, 0x03, 0xe1, 0x2f,
	0xa1, 0x9a, 0xf5, 0xca, 0x86, 0x1e, 0x5f, 0xc1, 0xc3, 0xe4, 0x3c, 0x59, 0xfb, 0xff, 0xb7, 0x5b,
	0xc0, 0x02, 0x44, 0x65, 0xbf, 0x18, 0xbf, 0x5f, 0x8c, 0x1e, 0x30, 0xd0, 0xc3, 0x2b, 0xbf, 0x74,
	0x9c, 0xd4, 0x1e, 0xbd, 0xc5, 0xab, 0x08, 0xf2, 0x61, 0x21, 0xe5, 0x69, 0x20, 0x73, 0xbf, 0x94,
	0x67, 0x8b, 0xcc, 0xfd, 0x52, 0xdf, 0x1b, 0x54, 0x7c, 0x53, 0x67, 0xe2, 0xcc, 0xf8, 0x66, 0xcd,
	0xeb, 0x99, 0xf1, 0xcd, 0x1e, 0xb9, 0x7f, 0x01, 0x37, 0x33, 0x86, 0x4a, 0xd4, 0xc8, 0xf8, 0xbf,
	0x3e, 0x19, 0xf3, 0x6d, 0xed, 0xf1, 0x5b, 0xc9, 0x0f, 0x43, 0x7d, 0x7e, 0x10, 0xca, 0x3e, 0xda,
	0xc9, 0xd9, 0x35, 0xfb, 0x68, 0xd3, 0x26, 0xb5, 0xd7, 0x50, 0x4e, 0x0e, 0x24, 0xe8, 0x83, 0xd4,
	0x6e, 0x6c, 0x7c, 0xc0, 0xa9, 0x7d, 0x78, 0xb9, 0x10, 0x0b, 0xc4, 0x38, 0x3b, 0xd1, 0x19, 0xa0,
	0x95, 0x2b, 0x36, 0x10, 0x27, 0xb5, 0x8f, 0xaf, 0xdc, 0x6a, 0x3c, 0xff, 0xc9, 0xd7, 0x4f, 0xba,
	0xd4, 0xb5, 0xfc, 0x6e, 0xe3, 0xe9, 0x2a, 0xe7, 0x0d, 0x9b, 0x7a, 0x8f, 0xe5, 0x9f, 0x3e, 0xd8,
	0xd4, 0x7d, 0xcc, 0x70, 0x78, 0x4a, 0x6c, 0xcc, 0xe4, 0xdf, 0x47, 0x3c, 0x1e, 0xd3, 0x76, 0x34,
	0x2b, 0xa5, 0x3e, 0xfd, 0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xf8, 0x07, 0x0c, 0x49, 0x9b, 0x21,
	0x00, 0x00,
}
