// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/rcmd/zego_stream_gateway/zego_stream (interfaces: ZegoStreamClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	zego_stream "golang.52tt.com/protocol/services/rcmd/zego_stream_gateway/zego_stream"
	grpc "google.golang.org/grpc"
)

// MockZegoStreamClient is a mock of ZegoStreamClient interface.
type MockZegoStreamClient struct {
	ctrl     *gomock.Controller
	recorder *MockZegoStreamClientMockRecorder
}

// MockZegoStreamClientMockRecorder is the mock recorder for MockZegoStreamClient.
type MockZegoStreamClientMockRecorder struct {
	mock *MockZegoStreamClient
}

// NewMockZegoStreamClient creates a new mock instance.
func NewMockZegoStreamClient(ctrl *gomock.Controller) *MockZegoStreamClient {
	mock := &MockZegoStreamClient{ctrl: ctrl}
	mock.recorder = &MockZegoStreamClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockZegoStreamClient) EXPECT() *MockZegoStreamClientMockRecorder {
	return m.recorder
}

// StreamTransfer mocks base method.
func (m *MockZegoStreamClient) StreamTransfer(arg0 context.Context, arg1 ...grpc.CallOption) (zego_stream.ZegoStream_StreamTransferClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StreamTransfer", varargs...)
	ret0, _ := ret[0].(zego_stream.ZegoStream_StreamTransferClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StreamTransfer indicates an expected call of StreamTransfer.
func (mr *MockZegoStreamClientMockRecorder) StreamTransfer(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StreamTransfer", reflect.TypeOf((*MockZegoStreamClient)(nil).StreamTransfer), varargs...)
}
