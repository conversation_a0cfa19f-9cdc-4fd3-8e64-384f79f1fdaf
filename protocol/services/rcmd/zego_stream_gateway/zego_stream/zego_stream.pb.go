// Code generated by protoc-gen-go. DO NOT EDIT.
// source: zego_stream_gateway/zego_stream.proto

package zego_stream // import "golang.52tt.com/protocol/services/rcmd/zego_stream_gateway/zego_stream"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type EventType int32

const (
	EventType_EventTypeNone EventType = 0
	// common: 1-10
	EventType_ZegoError   EventType = 1
	EventType_ServerError EventType = 2
	EventType_ClientError EventType = 3
	EventType_Audio       EventType = 4
	// client action: 11-99
	EventType_Exit            EventType = 10
	EventType_Login           EventType = 11
	EventType_StartPullStream EventType = 12
	EventType_StopPullStream  EventType = 13
	EventType_SendTextMessage EventType = 14
	// zego callback: 100-199
	EventType_LoginResult      EventType = 101
	EventType_StreamUpdate     EventType = 102
	EventType_RoomStateChanged EventType = 103
	// business: 200-299
	EventType_Heartbeat           EventType = 200
	EventType_HeartbeatAck        EventType = 201
	EventType_GetStreamList       EventType = 202
	EventType_GetStreamListResult EventType = 203
)

var EventType_name = map[int32]string{
	0:   "EventTypeNone",
	1:   "ZegoError",
	2:   "ServerError",
	3:   "ClientError",
	4:   "Audio",
	10:  "Exit",
	11:  "Login",
	12:  "StartPullStream",
	13:  "StopPullStream",
	14:  "SendTextMessage",
	101: "LoginResult",
	102: "StreamUpdate",
	103: "RoomStateChanged",
	200: "Heartbeat",
	201: "HeartbeatAck",
	202: "GetStreamList",
	203: "GetStreamListResult",
}
var EventType_value = map[string]int32{
	"EventTypeNone":       0,
	"ZegoError":           1,
	"ServerError":         2,
	"ClientError":         3,
	"Audio":               4,
	"Exit":                10,
	"Login":               11,
	"StartPullStream":     12,
	"StopPullStream":      13,
	"SendTextMessage":     14,
	"LoginResult":         101,
	"StreamUpdate":        102,
	"RoomStateChanged":    103,
	"Heartbeat":           200,
	"HeartbeatAck":        201,
	"GetStreamList":       202,
	"GetStreamListResult": 203,
}

func (x EventType) String() string {
	return proto.EnumName(EventType_name, int32(x))
}
func (EventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_zego_stream_8001646a5ec486ab, []int{0}
}

type ZegoErrCode int32

const (
	ZegoErrCode_ZegoErrCommonSuccess ZegoErrCode = 0
	// error code defined by zego proxy
	ZegoErrCode_ZegoErrConnIdleTooLong ZegoErrCode = 1000
	// Description: Room login authentication failed.
	// Cause: login set token error or token expired.
	// Solutions: set new token.
	ZegoErrCode_ZegoErrRoomAuthenticationFailed ZegoErrCode = 1002033
	// Description: The user was kicked out of the room.
	// <br>Cause: Possibly because the same user ID is logged in on another device.
	// <br>Solutions: Use a unique user ID.
	ZegoErrCode_ZegoErrRoomKickOut ZegoErrCode = 1002050
)

var ZegoErrCode_name = map[int32]string{
	0:       "ZegoErrCommonSuccess",
	1000:    "ZegoErrConnIdleTooLong",
	1002033: "ZegoErrRoomAuthenticationFailed",
	1002050: "ZegoErrRoomKickOut",
}
var ZegoErrCode_value = map[string]int32{
	"ZegoErrCommonSuccess":            0,
	"ZegoErrConnIdleTooLong":          1000,
	"ZegoErrRoomAuthenticationFailed": 1002033,
	"ZegoErrRoomKickOut":              1002050,
}

func (x ZegoErrCode) String() string {
	return proto.EnumName(ZegoErrCode_name, int32(x))
}
func (ZegoErrCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_zego_stream_8001646a5ec486ab, []int{1}
}

type EventWrap struct {
	Event                EventType `protobuf:"varint,1,opt,name=event,proto3,enum=zego_stream.EventType" json:"event,omitempty"`
	Json                 string    `protobuf:"bytes,2,opt,name=json,proto3" json:"json,omitempty"`
	Data                 []byte    `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *EventWrap) Reset()         { *m = EventWrap{} }
func (m *EventWrap) String() string { return proto.CompactTextString(m) }
func (*EventWrap) ProtoMessage()    {}
func (*EventWrap) Descriptor() ([]byte, []int) {
	return fileDescriptor_zego_stream_8001646a5ec486ab, []int{0}
}
func (m *EventWrap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EventWrap.Unmarshal(m, b)
}
func (m *EventWrap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EventWrap.Marshal(b, m, deterministic)
}
func (dst *EventWrap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EventWrap.Merge(dst, src)
}
func (m *EventWrap) XXX_Size() int {
	return xxx_messageInfo_EventWrap.Size(m)
}
func (m *EventWrap) XXX_DiscardUnknown() {
	xxx_messageInfo_EventWrap.DiscardUnknown(m)
}

var xxx_messageInfo_EventWrap proto.InternalMessageInfo

func (m *EventWrap) GetEvent() EventType {
	if m != nil {
		return m.Event
	}
	return EventType_EventTypeNone
}

func (m *EventWrap) GetJson() string {
	if m != nil {
		return m.Json
	}
	return ""
}

func (m *EventWrap) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func init() {
	proto.RegisterType((*EventWrap)(nil), "zego_stream.EventWrap")
	proto.RegisterEnum("zego_stream.EventType", EventType_name, EventType_value)
	proto.RegisterEnum("zego_stream.ZegoErrCode", ZegoErrCode_name, ZegoErrCode_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ZegoStreamClient is the client API for ZegoStream service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ZegoStreamClient interface {
	StreamTransfer(ctx context.Context, opts ...grpc.CallOption) (ZegoStream_StreamTransferClient, error)
}

type zegoStreamClient struct {
	cc *grpc.ClientConn
}

func NewZegoStreamClient(cc *grpc.ClientConn) ZegoStreamClient {
	return &zegoStreamClient{cc}
}

func (c *zegoStreamClient) StreamTransfer(ctx context.Context, opts ...grpc.CallOption) (ZegoStream_StreamTransferClient, error) {
	stream, err := c.cc.NewStream(ctx, &_ZegoStream_serviceDesc.Streams[0], "/zego_stream.ZegoStream/StreamTransfer", opts...)
	if err != nil {
		return nil, err
	}
	x := &zegoStreamStreamTransferClient{stream}
	return x, nil
}

type ZegoStream_StreamTransferClient interface {
	Send(*EventWrap) error
	Recv() (*EventWrap, error)
	grpc.ClientStream
}

type zegoStreamStreamTransferClient struct {
	grpc.ClientStream
}

func (x *zegoStreamStreamTransferClient) Send(m *EventWrap) error {
	return x.ClientStream.SendMsg(m)
}

func (x *zegoStreamStreamTransferClient) Recv() (*EventWrap, error) {
	m := new(EventWrap)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// ZegoStreamServer is the server API for ZegoStream service.
type ZegoStreamServer interface {
	StreamTransfer(ZegoStream_StreamTransferServer) error
}

func RegisterZegoStreamServer(s *grpc.Server, srv ZegoStreamServer) {
	s.RegisterService(&_ZegoStream_serviceDesc, srv)
}

func _ZegoStream_StreamTransfer_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ZegoStreamServer).StreamTransfer(&zegoStreamStreamTransferServer{stream})
}

type ZegoStream_StreamTransferServer interface {
	Send(*EventWrap) error
	Recv() (*EventWrap, error)
	grpc.ServerStream
}

type zegoStreamStreamTransferServer struct {
	grpc.ServerStream
}

func (x *zegoStreamStreamTransferServer) Send(m *EventWrap) error {
	return x.ServerStream.SendMsg(m)
}

func (x *zegoStreamStreamTransferServer) Recv() (*EventWrap, error) {
	m := new(EventWrap)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _ZegoStream_serviceDesc = grpc.ServiceDesc{
	ServiceName: "zego_stream.ZegoStream",
	HandlerType: (*ZegoStreamServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "StreamTransfer",
			Handler:       _ZegoStream_StreamTransfer_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "zego_stream_gateway/zego_stream.proto",
}

func init() {
	proto.RegisterFile("zego_stream_gateway/zego_stream.proto", fileDescriptor_zego_stream_8001646a5ec486ab)
}

var fileDescriptor_zego_stream_8001646a5ec486ab = []byte{
	// 501 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x92, 0x4d, 0x6f, 0xd3, 0x4e,
	0x10, 0xc6, 0xbb, 0x6d, 0xfa, 0xff, 0x93, 0xc9, 0x4b, 0xb7, 0xd3, 0xaa, 0xb2, 0xe0, 0x40, 0x84,
	0x54, 0x29, 0xaa, 0x50, 0x82, 0x82, 0x38, 0xf6, 0x10, 0x42, 0x4a, 0x11, 0xe1, 0x45, 0x76, 0x10,
	0x52, 0x2f, 0xd5, 0xd6, 0x9e, 0xba, 0xa6, 0xf6, 0x6e, 0xb4, 0x1e, 0x87, 0x96, 0x3b, 0xdf, 0xa0,
	0x5f, 0x84, 0x13, 0x77, 0x4e, 0xbc, 0x7c, 0x11, 0x3e, 0x06, 0x5a, 0x3b, 0x8a, 0x82, 0x04, 0xe2,
	0x36, 0xf3, 0x9b, 0xe7, 0xd9, 0x19, 0xad, 0x1e, 0xd8, 0xff, 0x40, 0xb1, 0x39, 0xcd, 0xd9, 0x92,
	0xca, 0x4e, 0x63, 0xc5, 0xf4, 0x5e, 0x5d, 0xf7, 0x57, 0x58, 0x6f, 0x66, 0x0d, 0x1b, 0x6c, 0xac,
	0xa0, 0x7b, 0x0a, 0xea, 0xe3, 0x39, 0x69, 0x7e, 0x6b, 0xd5, 0x0c, 0xef, 0xc3, 0x26, 0xb9, 0xc6,
	0x13, 0x1d, 0xd1, 0x6d, 0x0f, 0xf6, 0x7a, 0xab, 0xe6, 0x52, 0x36, 0xbd, 0x9e, 0x91, 0x5f, 0x89,
	0x10, 0xa1, 0xf6, 0x2e, 0x37, 0xda, 0x5b, 0xef, 0x88, 0x6e, 0xdd, 0x2f, 0x6b, 0xc7, 0x22, 0xc5,
	0xca, 0xdb, 0xe8, 0x88, 0x6e, 0xd3, 0x2f, 0xeb, 0x83, 0xcf, 0xeb, 0x8b, 0x1d, 0xce, 0x8c, 0xdb,
	0xd0, 0x5a, 0x36, 0x2f, 0x8d, 0x26, 0xb9, 0x86, 0x2d, 0xa8, 0x9f, 0x50, 0x6c, 0xc6, 0xd6, 0x1a,
	0x2b, 0x05, 0x6e, 0x41, 0x23, 0x20, 0x3b, 0x27, 0x5b, 0x81, 0x75, 0x07, 0x46, 0x69, 0x42, 0x9a,
	0x2b, 0xb0, 0x81, 0x75, 0xd8, 0x1c, 0x16, 0x51, 0x62, 0x64, 0x0d, 0x6f, 0x41, 0x6d, 0x7c, 0x95,
	0xb0, 0x04, 0x07, 0x27, 0x26, 0x4e, 0xb4, 0x6c, 0xe0, 0x0e, 0x6c, 0x05, 0xac, 0x2c, 0xbf, 0x2e,
	0xd2, 0x34, 0x28, 0xaf, 0x97, 0x4d, 0x44, 0x68, 0x07, 0x6c, 0x66, 0x2b, 0xac, 0x55, 0x0a, 0x49,
	0x47, 0x53, 0xba, 0xe2, 0x17, 0x94, 0xe7, 0x2a, 0x26, 0xd9, 0x76, 0xeb, 0xca, 0x87, 0x7c, 0xca,
	0x8b, 0x94, 0x25, 0xa1, 0x84, 0x66, 0xe5, 0x78, 0x33, 0x8b, 0x14, 0x93, 0x3c, 0xc7, 0x5d, 0x90,
	0xbe, 0x31, 0x59, 0xc0, 0x8a, 0x69, 0x74, 0xa1, 0x74, 0x4c, 0x91, 0x8c, 0xb1, 0x0d, 0xf5, 0x63,
	0x52, 0x96, 0xcf, 0x48, 0xb1, 0xfc, 0x2a, 0x70, 0x1b, 0x9a, 0xcb, 0x7e, 0x18, 0x5e, 0xca, 0x6f,
	0x02, 0x11, 0x5a, 0x4f, 0x89, 0xab, 0xd7, 0x26, 0x49, 0xce, 0xf2, 0xbb, 0x40, 0x0f, 0x76, 0x7e,
	0x63, 0x8b, 0xbd, 0x3f, 0xc4, 0xc1, 0x47, 0x01, 0x8d, 0xc5, 0xcf, 0x8c, 0x4c, 0x44, 0xe8, 0xc1,
	0xee, 0xb2, 0xcd, 0x32, 0xa3, 0x83, 0x22, 0x0c, 0x29, 0xcf, 0xe5, 0x1a, 0xde, 0x81, 0xbd, 0xe5,
	0x44, 0xeb, 0x67, 0x51, 0x4a, 0x53, 0x63, 0x26, 0x46, 0xc7, 0xf2, 0xe7, 0xff, 0xb8, 0x0f, 0x77,
	0x17, 0x43, 0x77, 0xf4, 0xb0, 0xe0, 0x0b, 0xd2, 0x9c, 0x84, 0x8a, 0x13, 0xa3, 0x8f, 0x54, 0x92,
	0x52, 0x24, 0x3f, 0xdd, 0x1c, 0xa2, 0x07, 0xb8, 0x22, 0x7b, 0x9e, 0x84, 0x97, 0xaf, 0x0a, 0x96,
	0x5f, 0x6e, 0x0e, 0x07, 0x3e, 0x80, 0x9b, 0x54, 0x27, 0xe2, 0x13, 0xf7, 0x91, 0xae, 0x9a, 0x5a,
	0xa5, 0xf3, 0x73, 0xb2, 0xf8, 0x87, 0xa0, 0xb8, 0x3c, 0xdd, 0xfe, 0x0b, 0xef, 0x8a, 0x07, 0xe2,
	0xf1, 0xf1, 0xc9, 0x51, 0x6c, 0x52, 0xa5, 0xe3, 0xde, 0xa3, 0x01, 0x73, 0x2f, 0x34, 0x59, 0xbf,
	0x8c, 0x67, 0x68, 0xd2, 0x7e, 0x4e, 0x76, 0x9e, 0x84, 0x94, 0xf7, 0x6d, 0x98, 0x45, 0xfd, 0x7f,
	0xa4, 0xfa, 0xec, 0xbf, 0xd2, 0xf7, 0xf0, 0x57, 0x00, 0x00, 0x00, 0xff, 0xff, 0x08, 0x5a, 0xf6,
	0x49, 0xff, 0x02, 0x00, 0x00,
}
