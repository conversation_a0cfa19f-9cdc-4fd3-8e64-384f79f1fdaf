// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd-ai-partner/game_character.proto

package game_character // import "golang.52tt.com/protocol/services/rcmd/game_character"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import partner_common "golang.52tt.com/protocol/services/rcmd/partner_common"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GameInfoOperate int32

const (
	GameInfoOperate_GAME_INFO_OPERATE_UNKNOWN GameInfoOperate = 0
	GameInfoOperate_GAME_INFO_OPERATE_UPSERT  GameInfoOperate = 1
	GameInfoOperate_GAME_INFO_OPERATE_DELETE  GameInfoOperate = 2
)

var GameInfoOperate_name = map[int32]string{
	0: "GAME_INFO_OPERATE_UNKNOWN",
	1: "GAME_INFO_OPERATE_UPSERT",
	2: "GAME_INFO_OPERATE_DELETE",
}
var GameInfoOperate_value = map[string]int32{
	"GAME_INFO_OPERATE_UNKNOWN": 0,
	"GAME_INFO_OPERATE_UPSERT":  1,
	"GAME_INFO_OPERATE_DELETE":  2,
}

func (x GameInfoOperate) String() string {
	return proto.EnumName(GameInfoOperate_name, int32(x))
}
func (GameInfoOperate) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{0}
}

type GameCreator int32

const (
	GameCreator_GAME_CREATOR_UNKNOWN GameCreator = 0
	GameCreator_GAME_CREATOR_SYSTEM  GameCreator = 1
	GameCreator_GAME_CREATOR_USER    GameCreator = 2
)

var GameCreator_name = map[int32]string{
	0: "GAME_CREATOR_UNKNOWN",
	1: "GAME_CREATOR_SYSTEM",
	2: "GAME_CREATOR_USER",
}
var GameCreator_value = map[string]int32{
	"GAME_CREATOR_UNKNOWN": 0,
	"GAME_CREATOR_SYSTEM":  1,
	"GAME_CREATOR_USER":    2,
}

func (x GameCreator) String() string {
	return proto.EnumName(GameCreator_name, int32(x))
}
func (GameCreator) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{1}
}

type GamePermission int32

const (
	GamePermission_GAME_PERMISSION_UNKNOWN GamePermission = 0
	GamePermission_GAME_PERMISSION_PUBLIC  GamePermission = 1
	GamePermission_GAME_PERMISSION_PRIVATE GamePermission = 2
)

var GamePermission_name = map[int32]string{
	0: "GAME_PERMISSION_UNKNOWN",
	1: "GAME_PERMISSION_PUBLIC",
	2: "GAME_PERMISSION_PRIVATE",
}
var GamePermission_value = map[string]int32{
	"GAME_PERMISSION_UNKNOWN": 0,
	"GAME_PERMISSION_PUBLIC":  1,
	"GAME_PERMISSION_PRIVATE": 2,
}

func (x GamePermission) String() string {
	return proto.EnumName(GamePermission_name, int32(x))
}
func (GamePermission) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{2}
}

type TargetRewardType int32

const (
	TargetRewardType_TARGET_REWARD_TYPE_NONE TargetRewardType = 0
	TargetRewardType_TARGET_REWARD_TYPE_CHAT TargetRewardType = 1
)

var TargetRewardType_name = map[int32]string{
	0: "TARGET_REWARD_TYPE_NONE",
	1: "TARGET_REWARD_TYPE_CHAT",
}
var TargetRewardType_value = map[string]int32{
	"TARGET_REWARD_TYPE_NONE": 0,
	"TARGET_REWARD_TYPE_CHAT": 1,
}

func (x TargetRewardType) String() string {
	return proto.EnumName(TargetRewardType_name, int32(x))
}
func (TargetRewardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{3}
}

type TargetToDo int32

const (
	TargetToDo_TARGET_TODO_NONE TargetToDo = 0
	TargetToDo_TARGET_TODO_CHAt TargetToDo = 1
)

var TargetToDo_name = map[int32]string{
	0: "TARGET_TODO_NONE",
	1: "TARGET_TODO_CHAt",
}
var TargetToDo_value = map[string]int32{
	"TARGET_TODO_NONE": 0,
	"TARGET_TODO_CHAt": 1,
}

func (x TargetToDo) String() string {
	return proto.EnumName(TargetToDo_name, int32(x))
}
func (TargetToDo) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{4}
}

type ComputeMethod int32

const (
	ComputeMethod_COMPUTE_METHOD_NONE         ComputeMethod = 0
	ComputeMethod_COMPUTE_METHOD_NUM_ADD      ComputeMethod = 1
	ComputeMethod_COMPUTE_METHOD_DAY_ADD      ComputeMethod = 2
	ComputeMethod_COMPUTE_METHOD_DAY_CONTINUE ComputeMethod = 3
	ComputeMethod_COMPUTE_METHOD_RECV_MATCH   ComputeMethod = 4
	ComputeMethod_COMPUTE_METHOD_RECV_CONTAIN ComputeMethod = 5
	ComputeMethod_COMPUTE_METHOD_SEND_MATCH   ComputeMethod = 6
	ComputeMethod_COMPUTE_METHOD_SEND_CONTAIN ComputeMethod = 7
)

var ComputeMethod_name = map[int32]string{
	0: "COMPUTE_METHOD_NONE",
	1: "COMPUTE_METHOD_NUM_ADD",
	2: "COMPUTE_METHOD_DAY_ADD",
	3: "COMPUTE_METHOD_DAY_CONTINUE",
	4: "COMPUTE_METHOD_RECV_MATCH",
	5: "COMPUTE_METHOD_RECV_CONTAIN",
	6: "COMPUTE_METHOD_SEND_MATCH",
	7: "COMPUTE_METHOD_SEND_CONTAIN",
}
var ComputeMethod_value = map[string]int32{
	"COMPUTE_METHOD_NONE":         0,
	"COMPUTE_METHOD_NUM_ADD":      1,
	"COMPUTE_METHOD_DAY_ADD":      2,
	"COMPUTE_METHOD_DAY_CONTINUE": 3,
	"COMPUTE_METHOD_RECV_MATCH":   4,
	"COMPUTE_METHOD_RECV_CONTAIN": 5,
	"COMPUTE_METHOD_SEND_MATCH":   6,
	"COMPUTE_METHOD_SEND_CONTAIN": 7,
}

func (x ComputeMethod) String() string {
	return proto.EnumName(ComputeMethod_name, int32(x))
}
func (ComputeMethod) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{5}
}

type TargetTrigger int32

const (
	TargetTrigger_TARGET_TRIGGER_NONE     TargetTrigger = 0
	TargetTrigger_TARGET_TRIGGER_IN_CHAT  TargetTrigger = 1
	TargetTrigger_TARGET_TRIGGER_CHAT_ONE TargetTrigger = 2
)

var TargetTrigger_name = map[int32]string{
	0: "TARGET_TRIGGER_NONE",
	1: "TARGET_TRIGGER_IN_CHAT",
	2: "TARGET_TRIGGER_CHAT_ONE",
}
var TargetTrigger_value = map[string]int32{
	"TARGET_TRIGGER_NONE":     0,
	"TARGET_TRIGGER_IN_CHAT":  1,
	"TARGET_TRIGGER_CHAT_ONE": 2,
}

func (x TargetTrigger) String() string {
	return proto.EnumName(TargetTrigger_name, int32(x))
}
func (TargetTrigger) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{6}
}

type TargetType int32

const (
	TargetType_TARGET_TYPE_NONE   TargetType = 0
	TargetType_TARGET_TYPE_SYSTEM TargetType = 1
	TargetType_TARGET_TYPE_CHAT   TargetType = 2
)

var TargetType_name = map[int32]string{
	0: "TARGET_TYPE_NONE",
	1: "TARGET_TYPE_SYSTEM",
	2: "TARGET_TYPE_CHAT",
}
var TargetType_value = map[string]int32{
	"TARGET_TYPE_NONE":   0,
	"TARGET_TYPE_SYSTEM": 1,
	"TARGET_TYPE_CHAT":   2,
}

func (x TargetType) String() string {
	return proto.EnumName(TargetType_name, int32(x))
}
func (TargetType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{7}
}

type ReplyTextFormatReq_FormatType int32

const (
	ReplyTextFormatReq_FormatType_Default         ReplyTextFormatReq_FormatType = 0
	ReplyTextFormatReq_FormatType_TextProcDefault ReplyTextFormatReq_FormatType = 1
	ReplyTextFormatReq_FormatType_RemoveBracket   ReplyTextFormatReq_FormatType = 2
	ReplyTextFormatReq_FormatType_TextProcV1      ReplyTextFormatReq_FormatType = 3
	ReplyTextFormatReq_FormatType_LongReplyDetect ReplyTextFormatReq_FormatType = 4
)

var ReplyTextFormatReq_FormatType_name = map[int32]string{
	0: "FormatType_Default",
	1: "FormatType_TextProcDefault",
	2: "FormatType_RemoveBracket",
	3: "FormatType_TextProcV1",
	4: "FormatType_LongReplyDetect",
}
var ReplyTextFormatReq_FormatType_value = map[string]int32{
	"FormatType_Default":         0,
	"FormatType_TextProcDefault": 1,
	"FormatType_RemoveBracket":   2,
	"FormatType_TextProcV1":      3,
	"FormatType_LongReplyDetect": 4,
}

func (x ReplyTextFormatReq_FormatType) String() string {
	return proto.EnumName(ReplyTextFormatReq_FormatType_name, int32(x))
}
func (ReplyTextFormatReq_FormatType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{45, 0}
}

type ReceiveMsgFromUserReq_MsgType int32

const (
	ReceiveMsgFromUserReq_MsgType_Unknown ReceiveMsgFromUserReq_MsgType = 0
	// 文本
	ReceiveMsgFromUserReq_MsgType_Text ReceiveMsgFromUserReq_MsgType = 1
	// 表情
	ReceiveMsgFromUserReq_MsgType_Emoticon ReceiveMsgFromUserReq_MsgType = 3
	ReceiveMsgFromUserReq_MsgType_Ext      ReceiveMsgFromUserReq_MsgType = 6
)

var ReceiveMsgFromUserReq_MsgType_name = map[int32]string{
	0: "MsgType_Unknown",
	1: "MsgType_Text",
	3: "MsgType_Emoticon",
	6: "MsgType_Ext",
}
var ReceiveMsgFromUserReq_MsgType_value = map[string]int32{
	"MsgType_Unknown":  0,
	"MsgType_Text":     1,
	"MsgType_Emoticon": 3,
	"MsgType_Ext":      6,
}

func (x ReceiveMsgFromUserReq_MsgType) String() string {
	return proto.EnumName(ReceiveMsgFromUserReq_MsgType_name, int32(x))
}
func (ReceiveMsgFromUserReq_MsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{53, 0}
}

type TestReq_Action int32

const (
	TestReq_Action_Unknown             TestReq_Action = 0
	TestReq_Action_Timer               TestReq_Action = 1
	TestReq_Action_Greeting            TestReq_Action = 2
	TestReq_Action_RoleCreatorNotify   TestReq_Action = 3
	TestReq_Action_IncRoleChattedCount TestReq_Action = 4
	TestReq_Action_RoleLikeNotifyPush  TestReq_Action = 5
	TestReq_Action_PetMsg              TestReq_Action = 6
	TestReq_Action_SelfPushPredict     TestReq_Action = 7
)

var TestReq_Action_name = map[int32]string{
	0: "Action_Unknown",
	1: "Action_Timer",
	2: "Action_Greeting",
	3: "Action_RoleCreatorNotify",
	4: "Action_IncRoleChattedCount",
	5: "Action_RoleLikeNotifyPush",
	6: "Action_PetMsg",
	7: "Action_SelfPushPredict",
}
var TestReq_Action_value = map[string]int32{
	"Action_Unknown":             0,
	"Action_Timer":               1,
	"Action_Greeting":            2,
	"Action_RoleCreatorNotify":   3,
	"Action_IncRoleChattedCount": 4,
	"Action_RoleLikeNotifyPush":  5,
	"Action_PetMsg":              6,
	"Action_SelfPushPredict":     7,
}

func (x TestReq_Action) String() string {
	return proto.EnumName(TestReq_Action_name, int32(x))
}
func (TestReq_Action) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{57, 0}
}

type GetRoleGameInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	GameId               string   `protobuf:"bytes,5,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRoleGameInfoReq) Reset()         { *m = GetRoleGameInfoReq{} }
func (m *GetRoleGameInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetRoleGameInfoReq) ProtoMessage()    {}
func (*GetRoleGameInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{0}
}
func (m *GetRoleGameInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoleGameInfoReq.Unmarshal(m, b)
}
func (m *GetRoleGameInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoleGameInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetRoleGameInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoleGameInfoReq.Merge(dst, src)
}
func (m *GetRoleGameInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetRoleGameInfoReq.Size(m)
}
func (m *GetRoleGameInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoleGameInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoleGameInfoReq proto.InternalMessageInfo

func (m *GetRoleGameInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRoleGameInfoReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GetRoleGameInfoReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetRoleGameInfoReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

func (m *GetRoleGameInfoReq) GetGameId() string {
	if m != nil {
		return m.GameId
	}
	return ""
}

type GetRoleGameInfoRsp struct {
	Game                 *GameInfo `protobuf:"bytes,1,opt,name=game,proto3" json:"game,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetRoleGameInfoRsp) Reset()         { *m = GetRoleGameInfoRsp{} }
func (m *GetRoleGameInfoRsp) String() string { return proto.CompactTextString(m) }
func (*GetRoleGameInfoRsp) ProtoMessage()    {}
func (*GetRoleGameInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{1}
}
func (m *GetRoleGameInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoleGameInfoRsp.Unmarshal(m, b)
}
func (m *GetRoleGameInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoleGameInfoRsp.Marshal(b, m, deterministic)
}
func (dst *GetRoleGameInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoleGameInfoRsp.Merge(dst, src)
}
func (m *GetRoleGameInfoRsp) XXX_Size() int {
	return xxx_messageInfo_GetRoleGameInfoRsp.Size(m)
}
func (m *GetRoleGameInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoleGameInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoleGameInfoRsp proto.InternalMessageInfo

func (m *GetRoleGameInfoRsp) GetGame() *GameInfo {
	if m != nil {
		return m.Game
	}
	return nil
}

type GetTopicGamesReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	TopicId              uint32   `protobuf:"varint,5,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicGamesReq) Reset()         { *m = GetTopicGamesReq{} }
func (m *GetTopicGamesReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicGamesReq) ProtoMessage()    {}
func (*GetTopicGamesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{2}
}
func (m *GetTopicGamesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicGamesReq.Unmarshal(m, b)
}
func (m *GetTopicGamesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicGamesReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicGamesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicGamesReq.Merge(dst, src)
}
func (m *GetTopicGamesReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicGamesReq.Size(m)
}
func (m *GetTopicGamesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicGamesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicGamesReq proto.InternalMessageInfo

func (m *GetTopicGamesReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTopicGamesReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GetTopicGamesReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetTopicGamesReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

func (m *GetTopicGamesReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type GetTopicGamesRsp struct {
	Games                []*GameInfo `protobuf:"bytes,1,rep,name=games,proto3" json:"games,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetTopicGamesRsp) Reset()         { *m = GetTopicGamesRsp{} }
func (m *GetTopicGamesRsp) String() string { return proto.CompactTextString(m) }
func (*GetTopicGamesRsp) ProtoMessage()    {}
func (*GetTopicGamesRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{3}
}
func (m *GetTopicGamesRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicGamesRsp.Unmarshal(m, b)
}
func (m *GetTopicGamesRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicGamesRsp.Marshal(b, m, deterministic)
}
func (dst *GetTopicGamesRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicGamesRsp.Merge(dst, src)
}
func (m *GetTopicGamesRsp) XXX_Size() int {
	return xxx_messageInfo_GetTopicGamesRsp.Size(m)
}
func (m *GetTopicGamesRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicGamesRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicGamesRsp proto.InternalMessageInfo

func (m *GetTopicGamesRsp) GetGames() []*GameInfo {
	if m != nil {
		return m.Games
	}
	return nil
}

type GetGameTopicsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameTopicsReq) Reset()         { *m = GetGameTopicsReq{} }
func (m *GetGameTopicsReq) String() string { return proto.CompactTextString(m) }
func (*GetGameTopicsReq) ProtoMessage()    {}
func (*GetGameTopicsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{4}
}
func (m *GetGameTopicsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTopicsReq.Unmarshal(m, b)
}
func (m *GetGameTopicsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTopicsReq.Marshal(b, m, deterministic)
}
func (dst *GetGameTopicsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTopicsReq.Merge(dst, src)
}
func (m *GetGameTopicsReq) XXX_Size() int {
	return xxx_messageInfo_GetGameTopicsReq.Size(m)
}
func (m *GetGameTopicsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTopicsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTopicsReq proto.InternalMessageInfo

func (m *GetGameTopicsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGameTopicsReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetGameTopicsRsp struct {
	Topics               []*GameTopic `protobuf:"bytes,1,rep,name=topics,proto3" json:"topics,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGameTopicsRsp) Reset()         { *m = GetGameTopicsRsp{} }
func (m *GetGameTopicsRsp) String() string { return proto.CompactTextString(m) }
func (*GetGameTopicsRsp) ProtoMessage()    {}
func (*GetGameTopicsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{5}
}
func (m *GetGameTopicsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTopicsRsp.Unmarshal(m, b)
}
func (m *GetGameTopicsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTopicsRsp.Marshal(b, m, deterministic)
}
func (dst *GetGameTopicsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTopicsRsp.Merge(dst, src)
}
func (m *GetGameTopicsRsp) XXX_Size() int {
	return xxx_messageInfo_GetGameTopicsRsp.Size(m)
}
func (m *GetGameTopicsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTopicsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTopicsRsp proto.InternalMessageInfo

func (m *GetGameTopicsRsp) GetTopics() []*GameTopic {
	if m != nil {
		return m.Topics
	}
	return nil
}

type GameInfoNotify struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Operate              GameInfoOperate `protobuf:"varint,2,opt,name=operate,proto3,enum=rcmd.game_character.GameInfoOperate" json:"operate,omitempty"`
	Games                []*GameInfo     `protobuf:"bytes,3,rep,name=games,proto3" json:"games,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GameInfoNotify) Reset()         { *m = GameInfoNotify{} }
func (m *GameInfoNotify) String() string { return proto.CompactTextString(m) }
func (*GameInfoNotify) ProtoMessage()    {}
func (*GameInfoNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{6}
}
func (m *GameInfoNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameInfoNotify.Unmarshal(m, b)
}
func (m *GameInfoNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameInfoNotify.Marshal(b, m, deterministic)
}
func (dst *GameInfoNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameInfoNotify.Merge(dst, src)
}
func (m *GameInfoNotify) XXX_Size() int {
	return xxx_messageInfo_GameInfoNotify.Size(m)
}
func (m *GameInfoNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_GameInfoNotify.DiscardUnknown(m)
}

var xxx_messageInfo_GameInfoNotify proto.InternalMessageInfo

func (m *GameInfoNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameInfoNotify) GetOperate() GameInfoOperate {
	if m != nil {
		return m.Operate
	}
	return GameInfoOperate_GAME_INFO_OPERATE_UNKNOWN
}

func (m *GameInfoNotify) GetGames() []*GameInfo {
	if m != nil {
		return m.Games
	}
	return nil
}

type GameTopic struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameTopic) Reset()         { *m = GameTopic{} }
func (m *GameTopic) String() string { return proto.CompactTextString(m) }
func (*GameTopic) ProtoMessage()    {}
func (*GameTopic) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{7}
}
func (m *GameTopic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTopic.Unmarshal(m, b)
}
func (m *GameTopic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTopic.Marshal(b, m, deterministic)
}
func (dst *GameTopic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTopic.Merge(dst, src)
}
func (m *GameTopic) XXX_Size() int {
	return xxx_messageInfo_GameTopic.Size(m)
}
func (m *GameTopic) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTopic.DiscardUnknown(m)
}

var xxx_messageInfo_GameTopic proto.InternalMessageInfo

func (m *GameTopic) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GameTopic) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type GameInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Bgm                  string   `protobuf:"bytes,4,opt,name=bgm,proto3" json:"bgm,omitempty"`
	Bg                   string   `protobuf:"bytes,5,opt,name=bg,proto3" json:"bg,omitempty"`
	Creator              int32    `protobuf:"varint,6,opt,name=creator,proto3" json:"creator,omitempty"`
	TopicId              uint32   `protobuf:"varint,7,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	SceneDesc            string   `protobuf:"bytes,8,opt,name=scene_desc,json=sceneDesc,proto3" json:"scene_desc,omitempty"`
	Prompt               string   `protobuf:"bytes,9,opt,name=prompt,proto3" json:"prompt,omitempty"`
	RoleId               uint32   `protobuf:"varint,10,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	UserPublic           int32    `protobuf:"varint,11,opt,name=user_public,json=userPublic,proto3" json:"user_public,omitempty"`
	SysPublic            int32    `protobuf:"varint,12,opt,name=sys_public,json=sysPublic,proto3" json:"sys_public,omitempty"`
	CreateTime           int64    `protobuf:"varint,13,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UserNum              uint32   `protobuf:"varint,14,opt,name=user_num,json=userNum,proto3" json:"user_num,omitempty"`
	Greeting             string   `protobuf:"bytes,15,opt,name=greeting,proto3" json:"greeting,omitempty"`
	Subtitle             string   `protobuf:"bytes,16,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	CreatorUid           uint32   `protobuf:"varint,17,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameInfo) Reset()         { *m = GameInfo{} }
func (m *GameInfo) String() string { return proto.CompactTextString(m) }
func (*GameInfo) ProtoMessage()    {}
func (*GameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{8}
}
func (m *GameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameInfo.Unmarshal(m, b)
}
func (m *GameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameInfo.Marshal(b, m, deterministic)
}
func (dst *GameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameInfo.Merge(dst, src)
}
func (m *GameInfo) XXX_Size() int {
	return xxx_messageInfo_GameInfo.Size(m)
}
func (m *GameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameInfo proto.InternalMessageInfo

func (m *GameInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GameInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GameInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GameInfo) GetBgm() string {
	if m != nil {
		return m.Bgm
	}
	return ""
}

func (m *GameInfo) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *GameInfo) GetCreator() int32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *GameInfo) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GameInfo) GetSceneDesc() string {
	if m != nil {
		return m.SceneDesc
	}
	return ""
}

func (m *GameInfo) GetPrompt() string {
	if m != nil {
		return m.Prompt
	}
	return ""
}

func (m *GameInfo) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GameInfo) GetUserPublic() int32 {
	if m != nil {
		return m.UserPublic
	}
	return 0
}

func (m *GameInfo) GetSysPublic() int32 {
	if m != nil {
		return m.SysPublic
	}
	return 0
}

func (m *GameInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GameInfo) GetUserNum() uint32 {
	if m != nil {
		return m.UserNum
	}
	return 0
}

func (m *GameInfo) GetGreeting() string {
	if m != nil {
		return m.Greeting
	}
	return ""
}

func (m *GameInfo) GetSubtitle() string {
	if m != nil {
		return m.Subtitle
	}
	return ""
}

func (m *GameInfo) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

type GetOutGamesReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOutGamesReq) Reset()         { *m = GetOutGamesReq{} }
func (m *GetOutGamesReq) String() string { return proto.CompactTextString(m) }
func (*GetOutGamesReq) ProtoMessage()    {}
func (*GetOutGamesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{9}
}
func (m *GetOutGamesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOutGamesReq.Unmarshal(m, b)
}
func (m *GetOutGamesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOutGamesReq.Marshal(b, m, deterministic)
}
func (dst *GetOutGamesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOutGamesReq.Merge(dst, src)
}
func (m *GetOutGamesReq) XXX_Size() int {
	return xxx_messageInfo_GetOutGamesReq.Size(m)
}
func (m *GetOutGamesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOutGamesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOutGamesReq proto.InternalMessageInfo

func (m *GetOutGamesReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetOutGamesReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GetOutGamesReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetOutGamesReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

type OutGames struct {
	TopicId              uint32      `protobuf:"varint,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Games                []*GameInfo `protobuf:"bytes,2,rep,name=games,proto3" json:"games,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *OutGames) Reset()         { *m = OutGames{} }
func (m *OutGames) String() string { return proto.CompactTextString(m) }
func (*OutGames) ProtoMessage()    {}
func (*OutGames) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{10}
}
func (m *OutGames) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OutGames.Unmarshal(m, b)
}
func (m *OutGames) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OutGames.Marshal(b, m, deterministic)
}
func (dst *OutGames) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OutGames.Merge(dst, src)
}
func (m *OutGames) XXX_Size() int {
	return xxx_messageInfo_OutGames.Size(m)
}
func (m *OutGames) XXX_DiscardUnknown() {
	xxx_messageInfo_OutGames.DiscardUnknown(m)
}

var xxx_messageInfo_OutGames proto.InternalMessageInfo

func (m *OutGames) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *OutGames) GetGames() []*GameInfo {
	if m != nil {
		return m.Games
	}
	return nil
}

type GetOutGamesRsp struct {
	Topics               []*GameTopic `protobuf:"bytes,1,rep,name=topics,proto3" json:"topics,omitempty"`
	Games                []*OutGames  `protobuf:"bytes,2,rep,name=games,proto3" json:"games,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetOutGamesRsp) Reset()         { *m = GetOutGamesRsp{} }
func (m *GetOutGamesRsp) String() string { return proto.CompactTextString(m) }
func (*GetOutGamesRsp) ProtoMessage()    {}
func (*GetOutGamesRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{11}
}
func (m *GetOutGamesRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOutGamesRsp.Unmarshal(m, b)
}
func (m *GetOutGamesRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOutGamesRsp.Marshal(b, m, deterministic)
}
func (dst *GetOutGamesRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOutGamesRsp.Merge(dst, src)
}
func (m *GetOutGamesRsp) XXX_Size() int {
	return xxx_messageInfo_GetOutGamesRsp.Size(m)
}
func (m *GetOutGamesRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOutGamesRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOutGamesRsp proto.InternalMessageInfo

func (m *GetOutGamesRsp) GetTopics() []*GameTopic {
	if m != nil {
		return m.Topics
	}
	return nil
}

func (m *GetOutGamesRsp) GetGames() []*OutGames {
	if m != nil {
		return m.Games
	}
	return nil
}

type GenVoiceChatGreetingReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	VoiceId              string   `protobuf:"bytes,4,opt,name=voice_id,json=voiceId,proto3" json:"voice_id,omitempty"`
	VoiceJson            string   `protobuf:"bytes,5,opt,name=voice_json,json=voiceJson,proto3" json:"voice_json,omitempty"`
	Text                 string   `protobuf:"bytes,6,opt,name=text,proto3" json:"text,omitempty"`
	SessionId            string   `protobuf:"bytes,7,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenVoiceChatGreetingReq) Reset()         { *m = GenVoiceChatGreetingReq{} }
func (m *GenVoiceChatGreetingReq) String() string { return proto.CompactTextString(m) }
func (*GenVoiceChatGreetingReq) ProtoMessage()    {}
func (*GenVoiceChatGreetingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{12}
}
func (m *GenVoiceChatGreetingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenVoiceChatGreetingReq.Unmarshal(m, b)
}
func (m *GenVoiceChatGreetingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenVoiceChatGreetingReq.Marshal(b, m, deterministic)
}
func (dst *GenVoiceChatGreetingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenVoiceChatGreetingReq.Merge(dst, src)
}
func (m *GenVoiceChatGreetingReq) XXX_Size() int {
	return xxx_messageInfo_GenVoiceChatGreetingReq.Size(m)
}
func (m *GenVoiceChatGreetingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GenVoiceChatGreetingReq.DiscardUnknown(m)
}

var xxx_messageInfo_GenVoiceChatGreetingReq proto.InternalMessageInfo

func (m *GenVoiceChatGreetingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GenVoiceChatGreetingReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GenVoiceChatGreetingReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GenVoiceChatGreetingReq) GetVoiceId() string {
	if m != nil {
		return m.VoiceId
	}
	return ""
}

func (m *GenVoiceChatGreetingReq) GetVoiceJson() string {
	if m != nil {
		return m.VoiceJson
	}
	return ""
}

func (m *GenVoiceChatGreetingReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *GenVoiceChatGreetingReq) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

type GenVoiceChatGreetingResp struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Seconds              int64    `protobuf:"varint,2,opt,name=seconds,proto3" json:"seconds,omitempty"`
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenVoiceChatGreetingResp) Reset()         { *m = GenVoiceChatGreetingResp{} }
func (m *GenVoiceChatGreetingResp) String() string { return proto.CompactTextString(m) }
func (*GenVoiceChatGreetingResp) ProtoMessage()    {}
func (*GenVoiceChatGreetingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{13}
}
func (m *GenVoiceChatGreetingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenVoiceChatGreetingResp.Unmarshal(m, b)
}
func (m *GenVoiceChatGreetingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenVoiceChatGreetingResp.Marshal(b, m, deterministic)
}
func (dst *GenVoiceChatGreetingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenVoiceChatGreetingResp.Merge(dst, src)
}
func (m *GenVoiceChatGreetingResp) XXX_Size() int {
	return xxx_messageInfo_GenVoiceChatGreetingResp.Size(m)
}
func (m *GenVoiceChatGreetingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GenVoiceChatGreetingResp.DiscardUnknown(m)
}

var xxx_messageInfo_GenVoiceChatGreetingResp proto.InternalMessageInfo

func (m *GenVoiceChatGreetingResp) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *GenVoiceChatGreetingResp) GetSeconds() int64 {
	if m != nil {
		return m.Seconds
	}
	return 0
}

func (m *GenVoiceChatGreetingResp) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type GetStoryBookHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	StoryId              string   `protobuf:"bytes,4,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	ChapterId            string   `protobuf:"bytes,5,opt,name=chapter_id,json=chapterId,proto3" json:"chapter_id,omitempty"`
	MaxSentAt            int64    `protobuf:"varint,6,opt,name=max_sent_at,json=maxSentAt,proto3" json:"max_sent_at,omitempty"`
	Size                 uint32   `protobuf:"varint,7,opt,name=size,proto3" json:"size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoryBookHistoryReq) Reset()         { *m = GetStoryBookHistoryReq{} }
func (m *GetStoryBookHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetStoryBookHistoryReq) ProtoMessage()    {}
func (*GetStoryBookHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{14}
}
func (m *GetStoryBookHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryBookHistoryReq.Unmarshal(m, b)
}
func (m *GetStoryBookHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryBookHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetStoryBookHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryBookHistoryReq.Merge(dst, src)
}
func (m *GetStoryBookHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetStoryBookHistoryReq.Size(m)
}
func (m *GetStoryBookHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryBookHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryBookHistoryReq proto.InternalMessageInfo

func (m *GetStoryBookHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetStoryBookHistoryReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GetStoryBookHistoryReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetStoryBookHistoryReq) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

func (m *GetStoryBookHistoryReq) GetChapterId() string {
	if m != nil {
		return m.ChapterId
	}
	return ""
}

func (m *GetStoryBookHistoryReq) GetMaxSentAt() int64 {
	if m != nil {
		return m.MaxSentAt
	}
	return 0
}

func (m *GetStoryBookHistoryReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

type StoryMsg struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Ext                  []byte   `protobuf:"bytes,3,opt,name=ext,proto3" json:"ext,omitempty"`
	SentAt               int64    `protobuf:"varint,4,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	MsgId                string   `protobuf:"bytes,5,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	Sender               string   `protobuf:"bytes,6,opt,name=sender,proto3" json:"sender,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoryMsg) Reset()         { *m = StoryMsg{} }
func (m *StoryMsg) String() string { return proto.CompactTextString(m) }
func (*StoryMsg) ProtoMessage()    {}
func (*StoryMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{15}
}
func (m *StoryMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoryMsg.Unmarshal(m, b)
}
func (m *StoryMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoryMsg.Marshal(b, m, deterministic)
}
func (dst *StoryMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoryMsg.Merge(dst, src)
}
func (m *StoryMsg) XXX_Size() int {
	return xxx_messageInfo_StoryMsg.Size(m)
}
func (m *StoryMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_StoryMsg.DiscardUnknown(m)
}

var xxx_messageInfo_StoryMsg proto.InternalMessageInfo

func (m *StoryMsg) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *StoryMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *StoryMsg) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *StoryMsg) GetSentAt() int64 {
	if m != nil {
		return m.SentAt
	}
	return 0
}

func (m *StoryMsg) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *StoryMsg) GetSender() string {
	if m != nil {
		return m.Sender
	}
	return ""
}

type GetStoryBookHistoryResp struct {
	MsgList              []*StoryMsg `protobuf:"bytes,1,rep,name=msg_list,json=msgList,proto3" json:"msg_list,omitempty"`
	Size                 uint32      `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetStoryBookHistoryResp) Reset()         { *m = GetStoryBookHistoryResp{} }
func (m *GetStoryBookHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetStoryBookHistoryResp) ProtoMessage()    {}
func (*GetStoryBookHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{16}
}
func (m *GetStoryBookHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryBookHistoryResp.Unmarshal(m, b)
}
func (m *GetStoryBookHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryBookHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetStoryBookHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryBookHistoryResp.Merge(dst, src)
}
func (m *GetStoryBookHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetStoryBookHistoryResp.Size(m)
}
func (m *GetStoryBookHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryBookHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryBookHistoryResp proto.InternalMessageInfo

func (m *GetStoryBookHistoryResp) GetMsgList() []*StoryMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

func (m *GetStoryBookHistoryResp) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

type GetStoryBookReplyReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	StoryId              string   `protobuf:"bytes,4,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	ChapterId            string   `protobuf:"bytes,5,opt,name=chapter_id,json=chapterId,proto3" json:"chapter_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoryBookReplyReq) Reset()         { *m = GetStoryBookReplyReq{} }
func (m *GetStoryBookReplyReq) String() string { return proto.CompactTextString(m) }
func (*GetStoryBookReplyReq) ProtoMessage()    {}
func (*GetStoryBookReplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{17}
}
func (m *GetStoryBookReplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryBookReplyReq.Unmarshal(m, b)
}
func (m *GetStoryBookReplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryBookReplyReq.Marshal(b, m, deterministic)
}
func (dst *GetStoryBookReplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryBookReplyReq.Merge(dst, src)
}
func (m *GetStoryBookReplyReq) XXX_Size() int {
	return xxx_messageInfo_GetStoryBookReplyReq.Size(m)
}
func (m *GetStoryBookReplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryBookReplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryBookReplyReq proto.InternalMessageInfo

func (m *GetStoryBookReplyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetStoryBookReplyReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GetStoryBookReplyReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetStoryBookReplyReq) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

func (m *GetStoryBookReplyReq) GetChapterId() string {
	if m != nil {
		return m.ChapterId
	}
	return ""
}

type GetStoryBookReplyResp struct {
	ChapterId            string                               `protobuf:"bytes,1,opt,name=chapter_id,json=chapterId,proto3" json:"chapter_id,omitempty"`
	SceneId              string                               `protobuf:"bytes,2,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	NodeId               string                               `protobuf:"bytes,3,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	Avatar               string                               `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Name                 string                               `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	ReplyOptions         []*GetStoryBookReplyResp_ReplyOption `protobuf:"bytes,6,rep,name=reply_options,json=replyOptions,proto3" json:"reply_options,omitempty"`
	EnableCustomReply    bool                                 `protobuf:"varint,7,opt,name=enable_custom_reply,json=enableCustomReply,proto3" json:"enable_custom_reply,omitempty"`
	CustomContentId      string                               `protobuf:"bytes,8,opt,name=custom_content_id,json=customContentId,proto3" json:"custom_content_id,omitempty"`
	IsSendDirectly       bool                                 `protobuf:"varint,9,opt,name=is_send_directly,json=isSendDirectly,proto3" json:"is_send_directly,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *GetStoryBookReplyResp) Reset()         { *m = GetStoryBookReplyResp{} }
func (m *GetStoryBookReplyResp) String() string { return proto.CompactTextString(m) }
func (*GetStoryBookReplyResp) ProtoMessage()    {}
func (*GetStoryBookReplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{18}
}
func (m *GetStoryBookReplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryBookReplyResp.Unmarshal(m, b)
}
func (m *GetStoryBookReplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryBookReplyResp.Marshal(b, m, deterministic)
}
func (dst *GetStoryBookReplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryBookReplyResp.Merge(dst, src)
}
func (m *GetStoryBookReplyResp) XXX_Size() int {
	return xxx_messageInfo_GetStoryBookReplyResp.Size(m)
}
func (m *GetStoryBookReplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryBookReplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryBookReplyResp proto.InternalMessageInfo

func (m *GetStoryBookReplyResp) GetChapterId() string {
	if m != nil {
		return m.ChapterId
	}
	return ""
}

func (m *GetStoryBookReplyResp) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *GetStoryBookReplyResp) GetNodeId() string {
	if m != nil {
		return m.NodeId
	}
	return ""
}

func (m *GetStoryBookReplyResp) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *GetStoryBookReplyResp) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetStoryBookReplyResp) GetReplyOptions() []*GetStoryBookReplyResp_ReplyOption {
	if m != nil {
		return m.ReplyOptions
	}
	return nil
}

func (m *GetStoryBookReplyResp) GetEnableCustomReply() bool {
	if m != nil {
		return m.EnableCustomReply
	}
	return false
}

func (m *GetStoryBookReplyResp) GetCustomContentId() string {
	if m != nil {
		return m.CustomContentId
	}
	return ""
}

func (m *GetStoryBookReplyResp) GetIsSendDirectly() bool {
	if m != nil {
		return m.IsSendDirectly
	}
	return false
}

type GetStoryBookReplyResp_ReplyOption struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoryBookReplyResp_ReplyOption) Reset()         { *m = GetStoryBookReplyResp_ReplyOption{} }
func (m *GetStoryBookReplyResp_ReplyOption) String() string { return proto.CompactTextString(m) }
func (*GetStoryBookReplyResp_ReplyOption) ProtoMessage()    {}
func (*GetStoryBookReplyResp_ReplyOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{18, 0}
}
func (m *GetStoryBookReplyResp_ReplyOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryBookReplyResp_ReplyOption.Unmarshal(m, b)
}
func (m *GetStoryBookReplyResp_ReplyOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryBookReplyResp_ReplyOption.Marshal(b, m, deterministic)
}
func (dst *GetStoryBookReplyResp_ReplyOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryBookReplyResp_ReplyOption.Merge(dst, src)
}
func (m *GetStoryBookReplyResp_ReplyOption) XXX_Size() int {
	return xxx_messageInfo_GetStoryBookReplyResp_ReplyOption.Size(m)
}
func (m *GetStoryBookReplyResp_ReplyOption) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryBookReplyResp_ReplyOption.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryBookReplyResp_ReplyOption proto.InternalMessageInfo

func (m *GetStoryBookReplyResp_ReplyOption) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetStoryBookReplyResp_ReplyOption) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type GetStoryBookProgressReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	StoryId              string   `protobuf:"bytes,4,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	ChapterId            string   `protobuf:"bytes,5,opt,name=chapter_id,json=chapterId,proto3" json:"chapter_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoryBookProgressReq) Reset()         { *m = GetStoryBookProgressReq{} }
func (m *GetStoryBookProgressReq) String() string { return proto.CompactTextString(m) }
func (*GetStoryBookProgressReq) ProtoMessage()    {}
func (*GetStoryBookProgressReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{19}
}
func (m *GetStoryBookProgressReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryBookProgressReq.Unmarshal(m, b)
}
func (m *GetStoryBookProgressReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryBookProgressReq.Marshal(b, m, deterministic)
}
func (dst *GetStoryBookProgressReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryBookProgressReq.Merge(dst, src)
}
func (m *GetStoryBookProgressReq) XXX_Size() int {
	return xxx_messageInfo_GetStoryBookProgressReq.Size(m)
}
func (m *GetStoryBookProgressReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryBookProgressReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryBookProgressReq proto.InternalMessageInfo

func (m *GetStoryBookProgressReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetStoryBookProgressReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetStoryBookProgressReq) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

func (m *GetStoryBookProgressReq) GetChapterId() string {
	if m != nil {
		return m.ChapterId
	}
	return ""
}

type GetStoryBookProgressResp struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Status               uint32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	ChapterId            string   `protobuf:"bytes,4,opt,name=chapter_id,json=chapterId,proto3" json:"chapter_id,omitempty"`
	ChapterName          string   `protobuf:"bytes,5,opt,name=chapter_name,json=chapterName,proto3" json:"chapter_name,omitempty"`
	ChapterStatus        uint32   `protobuf:"varint,6,opt,name=chapter_status,json=chapterStatus,proto3" json:"chapter_status,omitempty"`
	SceneId              string   `protobuf:"bytes,7,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	Scene                string   `protobuf:"bytes,8,opt,name=scene,proto3" json:"scene,omitempty"`
	BackgroundUrl        string   `protobuf:"bytes,9,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	BackgroundMusicUrl   string   `protobuf:"bytes,10,opt,name=background_music_url,json=backgroundMusicUrl,proto3" json:"background_music_url,omitempty"`
	Style                uint32   `protobuf:"varint,11,opt,name=style,proto3" json:"style,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoryBookProgressResp) Reset()         { *m = GetStoryBookProgressResp{} }
func (m *GetStoryBookProgressResp) String() string { return proto.CompactTextString(m) }
func (*GetStoryBookProgressResp) ProtoMessage()    {}
func (*GetStoryBookProgressResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{20}
}
func (m *GetStoryBookProgressResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryBookProgressResp.Unmarshal(m, b)
}
func (m *GetStoryBookProgressResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryBookProgressResp.Marshal(b, m, deterministic)
}
func (dst *GetStoryBookProgressResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryBookProgressResp.Merge(dst, src)
}
func (m *GetStoryBookProgressResp) XXX_Size() int {
	return xxx_messageInfo_GetStoryBookProgressResp.Size(m)
}
func (m *GetStoryBookProgressResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryBookProgressResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryBookProgressResp proto.InternalMessageInfo

func (m *GetStoryBookProgressResp) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetStoryBookProgressResp) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetStoryBookProgressResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetStoryBookProgressResp) GetChapterId() string {
	if m != nil {
		return m.ChapterId
	}
	return ""
}

func (m *GetStoryBookProgressResp) GetChapterName() string {
	if m != nil {
		return m.ChapterName
	}
	return ""
}

func (m *GetStoryBookProgressResp) GetChapterStatus() uint32 {
	if m != nil {
		return m.ChapterStatus
	}
	return 0
}

func (m *GetStoryBookProgressResp) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *GetStoryBookProgressResp) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

func (m *GetStoryBookProgressResp) GetBackgroundUrl() string {
	if m != nil {
		return m.BackgroundUrl
	}
	return ""
}

func (m *GetStoryBookProgressResp) GetBackgroundMusicUrl() string {
	if m != nil {
		return m.BackgroundMusicUrl
	}
	return ""
}

func (m *GetStoryBookProgressResp) GetStyle() uint32 {
	if m != nil {
		return m.Style
	}
	return 0
}

type StartStoryBookReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	StoryId              string   `protobuf:"bytes,4,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	ChapterId            string   `protobuf:"bytes,5,opt,name=chapter_id,json=chapterId,proto3" json:"chapter_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartStoryBookReq) Reset()         { *m = StartStoryBookReq{} }
func (m *StartStoryBookReq) String() string { return proto.CompactTextString(m) }
func (*StartStoryBookReq) ProtoMessage()    {}
func (*StartStoryBookReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{21}
}
func (m *StartStoryBookReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartStoryBookReq.Unmarshal(m, b)
}
func (m *StartStoryBookReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartStoryBookReq.Marshal(b, m, deterministic)
}
func (dst *StartStoryBookReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartStoryBookReq.Merge(dst, src)
}
func (m *StartStoryBookReq) XXX_Size() int {
	return xxx_messageInfo_StartStoryBookReq.Size(m)
}
func (m *StartStoryBookReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartStoryBookReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartStoryBookReq proto.InternalMessageInfo

func (m *StartStoryBookReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartStoryBookReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *StartStoryBookReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *StartStoryBookReq) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

func (m *StartStoryBookReq) GetChapterId() string {
	if m != nil {
		return m.ChapterId
	}
	return ""
}

type StartStoryBookResp struct {
	Ok                   bool     `protobuf:"varint,1,opt,name=ok,proto3" json:"ok,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartStoryBookResp) Reset()         { *m = StartStoryBookResp{} }
func (m *StartStoryBookResp) String() string { return proto.CompactTextString(m) }
func (*StartStoryBookResp) ProtoMessage()    {}
func (*StartStoryBookResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{22}
}
func (m *StartStoryBookResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartStoryBookResp.Unmarshal(m, b)
}
func (m *StartStoryBookResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartStoryBookResp.Marshal(b, m, deterministic)
}
func (dst *StartStoryBookResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartStoryBookResp.Merge(dst, src)
}
func (m *StartStoryBookResp) XXX_Size() int {
	return xxx_messageInfo_StartStoryBookResp.Size(m)
}
func (m *StartStoryBookResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartStoryBookResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartStoryBookResp proto.InternalMessageInfo

func (m *StartStoryBookResp) GetOk() bool {
	if m != nil {
		return m.Ok
	}
	return false
}

type MarkStoryBookNotifySeqReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	MarkSeq              uint32   `protobuf:"varint,4,opt,name=mark_seq,json=markSeq,proto3" json:"mark_seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkStoryBookNotifySeqReq) Reset()         { *m = MarkStoryBookNotifySeqReq{} }
func (m *MarkStoryBookNotifySeqReq) String() string { return proto.CompactTextString(m) }
func (*MarkStoryBookNotifySeqReq) ProtoMessage()    {}
func (*MarkStoryBookNotifySeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{23}
}
func (m *MarkStoryBookNotifySeqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkStoryBookNotifySeqReq.Unmarshal(m, b)
}
func (m *MarkStoryBookNotifySeqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkStoryBookNotifySeqReq.Marshal(b, m, deterministic)
}
func (dst *MarkStoryBookNotifySeqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkStoryBookNotifySeqReq.Merge(dst, src)
}
func (m *MarkStoryBookNotifySeqReq) XXX_Size() int {
	return xxx_messageInfo_MarkStoryBookNotifySeqReq.Size(m)
}
func (m *MarkStoryBookNotifySeqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkStoryBookNotifySeqReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkStoryBookNotifySeqReq proto.InternalMessageInfo

func (m *MarkStoryBookNotifySeqReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MarkStoryBookNotifySeqReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *MarkStoryBookNotifySeqReq) GetMarkSeq() uint32 {
	if m != nil {
		return m.MarkSeq
	}
	return 0
}

type MarkStoryBookNotifySeqResp struct {
	Ok                   bool     `protobuf:"varint,1,opt,name=ok,proto3" json:"ok,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkStoryBookNotifySeqResp) Reset()         { *m = MarkStoryBookNotifySeqResp{} }
func (m *MarkStoryBookNotifySeqResp) String() string { return proto.CompactTextString(m) }
func (*MarkStoryBookNotifySeqResp) ProtoMessage()    {}
func (*MarkStoryBookNotifySeqResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{24}
}
func (m *MarkStoryBookNotifySeqResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkStoryBookNotifySeqResp.Unmarshal(m, b)
}
func (m *MarkStoryBookNotifySeqResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkStoryBookNotifySeqResp.Marshal(b, m, deterministic)
}
func (dst *MarkStoryBookNotifySeqResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkStoryBookNotifySeqResp.Merge(dst, src)
}
func (m *MarkStoryBookNotifySeqResp) XXX_Size() int {
	return xxx_messageInfo_MarkStoryBookNotifySeqResp.Size(m)
}
func (m *MarkStoryBookNotifySeqResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkStoryBookNotifySeqResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkStoryBookNotifySeqResp proto.InternalMessageInfo

func (m *MarkStoryBookNotifySeqResp) GetOk() bool {
	if m != nil {
		return m.Ok
	}
	return false
}

type GetStoryBookNotifySeqReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoryBookNotifySeqReq) Reset()         { *m = GetStoryBookNotifySeqReq{} }
func (m *GetStoryBookNotifySeqReq) String() string { return proto.CompactTextString(m) }
func (*GetStoryBookNotifySeqReq) ProtoMessage()    {}
func (*GetStoryBookNotifySeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{25}
}
func (m *GetStoryBookNotifySeqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryBookNotifySeqReq.Unmarshal(m, b)
}
func (m *GetStoryBookNotifySeqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryBookNotifySeqReq.Marshal(b, m, deterministic)
}
func (dst *GetStoryBookNotifySeqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryBookNotifySeqReq.Merge(dst, src)
}
func (m *GetStoryBookNotifySeqReq) XXX_Size() int {
	return xxx_messageInfo_GetStoryBookNotifySeqReq.Size(m)
}
func (m *GetStoryBookNotifySeqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryBookNotifySeqReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryBookNotifySeqReq proto.InternalMessageInfo

func (m *GetStoryBookNotifySeqReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetStoryBookNotifySeqReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetStoryBookNotifySeqResp struct {
	LastSeq              uint32   `protobuf:"varint,1,opt,name=last_seq,json=lastSeq,proto3" json:"last_seq,omitempty"`
	ShowType             uint32   `protobuf:"varint,2,opt,name=show_type,json=showType,proto3" json:"show_type,omitempty"`
	LastReadSeq          uint32   `protobuf:"varint,3,opt,name=last_read_seq,json=lastReadSeq,proto3" json:"last_read_seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoryBookNotifySeqResp) Reset()         { *m = GetStoryBookNotifySeqResp{} }
func (m *GetStoryBookNotifySeqResp) String() string { return proto.CompactTextString(m) }
func (*GetStoryBookNotifySeqResp) ProtoMessage()    {}
func (*GetStoryBookNotifySeqResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{26}
}
func (m *GetStoryBookNotifySeqResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryBookNotifySeqResp.Unmarshal(m, b)
}
func (m *GetStoryBookNotifySeqResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryBookNotifySeqResp.Marshal(b, m, deterministic)
}
func (dst *GetStoryBookNotifySeqResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryBookNotifySeqResp.Merge(dst, src)
}
func (m *GetStoryBookNotifySeqResp) XXX_Size() int {
	return xxx_messageInfo_GetStoryBookNotifySeqResp.Size(m)
}
func (m *GetStoryBookNotifySeqResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryBookNotifySeqResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryBookNotifySeqResp proto.InternalMessageInfo

func (m *GetStoryBookNotifySeqResp) GetLastSeq() uint32 {
	if m != nil {
		return m.LastSeq
	}
	return 0
}

func (m *GetStoryBookNotifySeqResp) GetShowType() uint32 {
	if m != nil {
		return m.ShowType
	}
	return 0
}

func (m *GetStoryBookNotifySeqResp) GetLastReadSeq() uint32 {
	if m != nil {
		return m.LastReadSeq
	}
	return 0
}

type GetStoryBookReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	StoryId              string   `protobuf:"bytes,4,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoryBookReq) Reset()         { *m = GetStoryBookReq{} }
func (m *GetStoryBookReq) String() string { return proto.CompactTextString(m) }
func (*GetStoryBookReq) ProtoMessage()    {}
func (*GetStoryBookReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{27}
}
func (m *GetStoryBookReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryBookReq.Unmarshal(m, b)
}
func (m *GetStoryBookReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryBookReq.Marshal(b, m, deterministic)
}
func (dst *GetStoryBookReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryBookReq.Merge(dst, src)
}
func (m *GetStoryBookReq) XXX_Size() int {
	return xxx_messageInfo_GetStoryBookReq.Size(m)
}
func (m *GetStoryBookReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryBookReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryBookReq proto.InternalMessageInfo

func (m *GetStoryBookReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetStoryBookReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetStoryBookReq) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

type GetStoryBookResp struct {
	Story                *StoryBook `protobuf:"bytes,1,opt,name=story,proto3" json:"story,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetStoryBookResp) Reset()         { *m = GetStoryBookResp{} }
func (m *GetStoryBookResp) String() string { return proto.CompactTextString(m) }
func (*GetStoryBookResp) ProtoMessage()    {}
func (*GetStoryBookResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{28}
}
func (m *GetStoryBookResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryBookResp.Unmarshal(m, b)
}
func (m *GetStoryBookResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryBookResp.Marshal(b, m, deterministic)
}
func (dst *GetStoryBookResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryBookResp.Merge(dst, src)
}
func (m *GetStoryBookResp) XXX_Size() int {
	return xxx_messageInfo_GetStoryBookResp.Size(m)
}
func (m *GetStoryBookResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryBookResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryBookResp proto.InternalMessageInfo

func (m *GetStoryBookResp) GetStory() *StoryBook {
	if m != nil {
		return m.Story
	}
	return nil
}

type GetStoryBookListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoryBookListReq) Reset()         { *m = GetStoryBookListReq{} }
func (m *GetStoryBookListReq) String() string { return proto.CompactTextString(m) }
func (*GetStoryBookListReq) ProtoMessage()    {}
func (*GetStoryBookListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{29}
}
func (m *GetStoryBookListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryBookListReq.Unmarshal(m, b)
}
func (m *GetStoryBookListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryBookListReq.Marshal(b, m, deterministic)
}
func (dst *GetStoryBookListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryBookListReq.Merge(dst, src)
}
func (m *GetStoryBookListReq) XXX_Size() int {
	return xxx_messageInfo_GetStoryBookListReq.Size(m)
}
func (m *GetStoryBookListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryBookListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryBookListReq proto.InternalMessageInfo

func (m *GetStoryBookListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetStoryBookListReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetStoryBookListResp struct {
	Stories              []*StoryBook `protobuf:"bytes,1,rep,name=stories,proto3" json:"stories,omitempty"`
	Total                uint32       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetStoryBookListResp) Reset()         { *m = GetStoryBookListResp{} }
func (m *GetStoryBookListResp) String() string { return proto.CompactTextString(m) }
func (*GetStoryBookListResp) ProtoMessage()    {}
func (*GetStoryBookListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{30}
}
func (m *GetStoryBookListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStoryBookListResp.Unmarshal(m, b)
}
func (m *GetStoryBookListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStoryBookListResp.Marshal(b, m, deterministic)
}
func (dst *GetStoryBookListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoryBookListResp.Merge(dst, src)
}
func (m *GetStoryBookListResp) XXX_Size() int {
	return xxx_messageInfo_GetStoryBookListResp.Size(m)
}
func (m *GetStoryBookListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoryBookListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoryBookListResp proto.InternalMessageInfo

func (m *GetStoryBookListResp) GetStories() []*StoryBook {
	if m != nil {
		return m.Stories
	}
	return nil
}

func (m *GetStoryBookListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type StoryBook struct {
	Id                   string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CoverImage           string             `protobuf:"bytes,2,opt,name=cover_image,json=coverImage,proto3" json:"cover_image,omitempty"`
	Title                string             `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Subtitle             string             `protobuf:"bytes,4,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	Status               uint32             `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	UnlockCondition      []*StoryUnlockCond `protobuf:"bytes,6,rep,name=unlock_condition,json=unlockCondition,proto3" json:"unlock_condition,omitempty"`
	Chapters             []*StoryChapter    `protobuf:"bytes,7,rep,name=chapters,proto3" json:"chapters,omitempty"`
	UpdateTs             int64              `protobuf:"varint,8,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	Style                uint32             `protobuf:"varint,9,opt,name=style,proto3" json:"style,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *StoryBook) Reset()         { *m = StoryBook{} }
func (m *StoryBook) String() string { return proto.CompactTextString(m) }
func (*StoryBook) ProtoMessage()    {}
func (*StoryBook) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{31}
}
func (m *StoryBook) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoryBook.Unmarshal(m, b)
}
func (m *StoryBook) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoryBook.Marshal(b, m, deterministic)
}
func (dst *StoryBook) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoryBook.Merge(dst, src)
}
func (m *StoryBook) XXX_Size() int {
	return xxx_messageInfo_StoryBook.Size(m)
}
func (m *StoryBook) XXX_DiscardUnknown() {
	xxx_messageInfo_StoryBook.DiscardUnknown(m)
}

var xxx_messageInfo_StoryBook proto.InternalMessageInfo

func (m *StoryBook) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *StoryBook) GetCoverImage() string {
	if m != nil {
		return m.CoverImage
	}
	return ""
}

func (m *StoryBook) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *StoryBook) GetSubtitle() string {
	if m != nil {
		return m.Subtitle
	}
	return ""
}

func (m *StoryBook) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *StoryBook) GetUnlockCondition() []*StoryUnlockCond {
	if m != nil {
		return m.UnlockCondition
	}
	return nil
}

func (m *StoryBook) GetChapters() []*StoryChapter {
	if m != nil {
		return m.Chapters
	}
	return nil
}

func (m *StoryBook) GetUpdateTs() int64 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *StoryBook) GetStyle() uint32 {
	if m != nil {
		return m.Style
	}
	return 0
}

type StoryChapter struct {
	Id                   string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Status               uint32             `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	UnlockCondition      []*StoryUnlockCond `protobuf:"bytes,4,rep,name=unlock_condition,json=unlockCondition,proto3" json:"unlock_condition,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *StoryChapter) Reset()         { *m = StoryChapter{} }
func (m *StoryChapter) String() string { return proto.CompactTextString(m) }
func (*StoryChapter) ProtoMessage()    {}
func (*StoryChapter) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{32}
}
func (m *StoryChapter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoryChapter.Unmarshal(m, b)
}
func (m *StoryChapter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoryChapter.Marshal(b, m, deterministic)
}
func (dst *StoryChapter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoryChapter.Merge(dst, src)
}
func (m *StoryChapter) XXX_Size() int {
	return xxx_messageInfo_StoryChapter.Size(m)
}
func (m *StoryChapter) XXX_DiscardUnknown() {
	xxx_messageInfo_StoryChapter.DiscardUnknown(m)
}

var xxx_messageInfo_StoryChapter proto.InternalMessageInfo

func (m *StoryChapter) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *StoryChapter) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StoryChapter) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *StoryChapter) GetUnlockCondition() []*StoryUnlockCond {
	if m != nil {
		return m.UnlockCondition
	}
	return nil
}

type StoryUnlockCond struct {
	Val                  int64    `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoryUnlockCond) Reset()         { *m = StoryUnlockCond{} }
func (m *StoryUnlockCond) String() string { return proto.CompactTextString(m) }
func (*StoryUnlockCond) ProtoMessage()    {}
func (*StoryUnlockCond) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{33}
}
func (m *StoryUnlockCond) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoryUnlockCond.Unmarshal(m, b)
}
func (m *StoryUnlockCond) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoryUnlockCond.Marshal(b, m, deterministic)
}
func (dst *StoryUnlockCond) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoryUnlockCond.Merge(dst, src)
}
func (m *StoryUnlockCond) XXX_Size() int {
	return xxx_messageInfo_StoryUnlockCond.Size(m)
}
func (m *StoryUnlockCond) XXX_DiscardUnknown() {
	xxx_messageInfo_StoryUnlockCond.DiscardUnknown(m)
}

var xxx_messageInfo_StoryUnlockCond proto.InternalMessageInfo

func (m *StoryUnlockCond) GetVal() int64 {
	if m != nil {
		return m.Val
	}
	return 0
}

func (m *StoryUnlockCond) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type StartGameReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	GameId               string   `protobuf:"bytes,4,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartGameReq) Reset()         { *m = StartGameReq{} }
func (m *StartGameReq) String() string { return proto.CompactTextString(m) }
func (*StartGameReq) ProtoMessage()    {}
func (*StartGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{34}
}
func (m *StartGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartGameReq.Unmarshal(m, b)
}
func (m *StartGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartGameReq.Marshal(b, m, deterministic)
}
func (dst *StartGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartGameReq.Merge(dst, src)
}
func (m *StartGameReq) XXX_Size() int {
	return xxx_messageInfo_StartGameReq.Size(m)
}
func (m *StartGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartGameReq proto.InternalMessageInfo

func (m *StartGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartGameReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *StartGameReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *StartGameReq) GetGameId() string {
	if m != nil {
		return m.GameId
	}
	return ""
}

type StartGameResp struct {
	CtxId                string   `protobuf:"bytes,1,opt,name=ctx_id,json=ctxId,proto3" json:"ctx_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartGameResp) Reset()         { *m = StartGameResp{} }
func (m *StartGameResp) String() string { return proto.CompactTextString(m) }
func (*StartGameResp) ProtoMessage()    {}
func (*StartGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{35}
}
func (m *StartGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartGameResp.Unmarshal(m, b)
}
func (m *StartGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartGameResp.Marshal(b, m, deterministic)
}
func (dst *StartGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartGameResp.Merge(dst, src)
}
func (m *StartGameResp) XXX_Size() int {
	return xxx_messageInfo_StartGameResp.Size(m)
}
func (m *StartGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartGameResp proto.InternalMessageInfo

func (m *StartGameResp) GetCtxId() string {
	if m != nil {
		return m.CtxId
	}
	return ""
}

type FormatGptAnswerReq struct {
	GptA                 string   `protobuf:"bytes,1,opt,name=gpt_a,json=gptA,proto3" json:"gpt_a,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FormatGptAnswerReq) Reset()         { *m = FormatGptAnswerReq{} }
func (m *FormatGptAnswerReq) String() string { return proto.CompactTextString(m) }
func (*FormatGptAnswerReq) ProtoMessage()    {}
func (*FormatGptAnswerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{36}
}
func (m *FormatGptAnswerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FormatGptAnswerReq.Unmarshal(m, b)
}
func (m *FormatGptAnswerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FormatGptAnswerReq.Marshal(b, m, deterministic)
}
func (dst *FormatGptAnswerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FormatGptAnswerReq.Merge(dst, src)
}
func (m *FormatGptAnswerReq) XXX_Size() int {
	return xxx_messageInfo_FormatGptAnswerReq.Size(m)
}
func (m *FormatGptAnswerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FormatGptAnswerReq.DiscardUnknown(m)
}

var xxx_messageInfo_FormatGptAnswerReq proto.InternalMessageInfo

func (m *FormatGptAnswerReq) GetGptA() string {
	if m != nil {
		return m.GptA
	}
	return ""
}

func (m *FormatGptAnswerReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type FormatGptAnswerResp struct {
	MsgList              []*partner_common.FormatMsg `protobuf:"bytes,1,rep,name=msg_list,json=msgList,proto3" json:"msg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *FormatGptAnswerResp) Reset()         { *m = FormatGptAnswerResp{} }
func (m *FormatGptAnswerResp) String() string { return proto.CompactTextString(m) }
func (*FormatGptAnswerResp) ProtoMessage()    {}
func (*FormatGptAnswerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{37}
}
func (m *FormatGptAnswerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FormatGptAnswerResp.Unmarshal(m, b)
}
func (m *FormatGptAnswerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FormatGptAnswerResp.Marshal(b, m, deterministic)
}
func (dst *FormatGptAnswerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FormatGptAnswerResp.Merge(dst, src)
}
func (m *FormatGptAnswerResp) XXX_Size() int {
	return xxx_messageInfo_FormatGptAnswerResp.Size(m)
}
func (m *FormatGptAnswerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FormatGptAnswerResp.DiscardUnknown(m)
}

var xxx_messageInfo_FormatGptAnswerResp proto.InternalMessageInfo

func (m *FormatGptAnswerResp) GetMsgList() []*partner_common.FormatMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

type GetGPTReqInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGPTReqInfoReq) Reset()         { *m = GetGPTReqInfoReq{} }
func (m *GetGPTReqInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetGPTReqInfoReq) ProtoMessage()    {}
func (*GetGPTReqInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{38}
}
func (m *GetGPTReqInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGPTReqInfoReq.Unmarshal(m, b)
}
func (m *GetGPTReqInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGPTReqInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetGPTReqInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGPTReqInfoReq.Merge(dst, src)
}
func (m *GetGPTReqInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetGPTReqInfoReq.Size(m)
}
func (m *GetGPTReqInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGPTReqInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGPTReqInfoReq proto.InternalMessageInfo

func (m *GetGPTReqInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGPTReqInfoReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetGPTReqInfoResp struct {
	PlaceholderStr       string   `protobuf:"bytes,1,opt,name=placeholder_str,json=placeholderStr,proto3" json:"placeholder_str,omitempty"`
	PromptId             string   `protobuf:"bytes,2,opt,name=prompt_id,json=promptId,proto3" json:"prompt_id,omitempty"`
	PromptVersion        string   `protobuf:"bytes,3,opt,name=prompt_version,json=promptVersion,proto3" json:"prompt_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGPTReqInfoResp) Reset()         { *m = GetGPTReqInfoResp{} }
func (m *GetGPTReqInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGPTReqInfoResp) ProtoMessage()    {}
func (*GetGPTReqInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{39}
}
func (m *GetGPTReqInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGPTReqInfoResp.Unmarshal(m, b)
}
func (m *GetGPTReqInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGPTReqInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetGPTReqInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGPTReqInfoResp.Merge(dst, src)
}
func (m *GetGPTReqInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetGPTReqInfoResp.Size(m)
}
func (m *GetGPTReqInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGPTReqInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGPTReqInfoResp proto.InternalMessageInfo

func (m *GetGPTReqInfoResp) GetPlaceholderStr() string {
	if m != nil {
		return m.PlaceholderStr
	}
	return ""
}

func (m *GetGPTReqInfoResp) GetPromptId() string {
	if m != nil {
		return m.PromptId
	}
	return ""
}

func (m *GetGPTReqInfoResp) GetPromptVersion() string {
	if m != nil {
		return m.PromptVersion
	}
	return ""
}

type BatchGetCharactersReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ids                  []uint32 `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetCharactersReq) Reset()         { *m = BatchGetCharactersReq{} }
func (m *BatchGetCharactersReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetCharactersReq) ProtoMessage()    {}
func (*BatchGetCharactersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{40}
}
func (m *BatchGetCharactersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCharactersReq.Unmarshal(m, b)
}
func (m *BatchGetCharactersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCharactersReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetCharactersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCharactersReq.Merge(dst, src)
}
func (m *BatchGetCharactersReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetCharactersReq.Size(m)
}
func (m *BatchGetCharactersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCharactersReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCharactersReq proto.InternalMessageInfo

func (m *BatchGetCharactersReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchGetCharactersReq) GetIds() []uint32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type GameCharacter struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32         `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	CreateTime           int64          `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UserMsgCount         uint32         `protobuf:"varint,4,opt,name=user_msg_count,json=userMsgCount,proto3" json:"user_msg_count,omitempty"`
	RoleId               uint32         `protobuf:"varint,5,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	RoleExtra            []byte         `protobuf:"bytes,6,opt,name=role_extra,json=roleExtra,proto3" json:"role_extra,omitempty"`
	CustomVoices         []*CustomVoice `protobuf:"bytes,7,rep,name=custom_voices,json=customVoices,proto3" json:"custom_voices,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GameCharacter) Reset()         { *m = GameCharacter{} }
func (m *GameCharacter) String() string { return proto.CompactTextString(m) }
func (*GameCharacter) ProtoMessage()    {}
func (*GameCharacter) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{41}
}
func (m *GameCharacter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameCharacter.Unmarshal(m, b)
}
func (m *GameCharacter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameCharacter.Marshal(b, m, deterministic)
}
func (dst *GameCharacter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameCharacter.Merge(dst, src)
}
func (m *GameCharacter) XXX_Size() int {
	return xxx_messageInfo_GameCharacter.Size(m)
}
func (m *GameCharacter) XXX_DiscardUnknown() {
	xxx_messageInfo_GameCharacter.DiscardUnknown(m)
}

var xxx_messageInfo_GameCharacter proto.InternalMessageInfo

func (m *GameCharacter) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameCharacter) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GameCharacter) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GameCharacter) GetUserMsgCount() uint32 {
	if m != nil {
		return m.UserMsgCount
	}
	return 0
}

func (m *GameCharacter) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GameCharacter) GetRoleExtra() []byte {
	if m != nil {
		return m.RoleExtra
	}
	return nil
}

func (m *GameCharacter) GetCustomVoices() []*CustomVoice {
	if m != nil {
		return m.CustomVoices
	}
	return nil
}

type BatchGetCharactersResp struct {
	GameCharacters       []*GameCharacter `protobuf:"bytes,1,rep,name=game_characters,json=gameCharacters,proto3" json:"game_characters,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchGetCharactersResp) Reset()         { *m = BatchGetCharactersResp{} }
func (m *BatchGetCharactersResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetCharactersResp) ProtoMessage()    {}
func (*BatchGetCharactersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{42}
}
func (m *BatchGetCharactersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCharactersResp.Unmarshal(m, b)
}
func (m *BatchGetCharactersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCharactersResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetCharactersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCharactersResp.Merge(dst, src)
}
func (m *BatchGetCharactersResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetCharactersResp.Size(m)
}
func (m *BatchGetCharactersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCharactersResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCharactersResp proto.InternalMessageInfo

func (m *BatchGetCharactersResp) GetGameCharacters() []*GameCharacter {
	if m != nil {
		return m.GameCharacters
	}
	return nil
}

type GetUserInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInfoReq) Reset()         { *m = GetUserInfoReq{} }
func (m *GetUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserInfoReq) ProtoMessage()    {}
func (*GetUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{43}
}
func (m *GetUserInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInfoReq.Unmarshal(m, b)
}
func (m *GetUserInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInfoReq.Merge(dst, src)
}
func (m *GetUserInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserInfoReq.Size(m)
}
func (m *GetUserInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInfoReq proto.InternalMessageInfo

func (m *GetUserInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserInfoResp struct {
	UserTodayMsgCount    uint32   `protobuf:"varint,1,opt,name=user_today_msg_count,json=userTodayMsgCount,proto3" json:"user_today_msg_count,omitempty"`
	UserTotalMsgCount    uint32   `protobuf:"varint,2,opt,name=user_total_msg_count,json=userTotalMsgCount,proto3" json:"user_total_msg_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInfoResp) Reset()         { *m = GetUserInfoResp{} }
func (m *GetUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserInfoResp) ProtoMessage()    {}
func (*GetUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{44}
}
func (m *GetUserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInfoResp.Unmarshal(m, b)
}
func (m *GetUserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInfoResp.Merge(dst, src)
}
func (m *GetUserInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserInfoResp.Size(m)
}
func (m *GetUserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInfoResp proto.InternalMessageInfo

func (m *GetUserInfoResp) GetUserTodayMsgCount() uint32 {
	if m != nil {
		return m.UserTodayMsgCount
	}
	return 0
}

func (m *GetUserInfoResp) GetUserTotalMsgCount() uint32 {
	if m != nil {
		return m.UserTotalMsgCount
	}
	return 0
}

type ReplyTextFormatReq struct {
	Text                 string                        `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	FormatType           ReplyTextFormatReq_FormatType `protobuf:"varint,2,opt,name=format_type,json=formatType,proto3,enum=rcmd.game_character.ReplyTextFormatReq_FormatType" json:"format_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *ReplyTextFormatReq) Reset()         { *m = ReplyTextFormatReq{} }
func (m *ReplyTextFormatReq) String() string { return proto.CompactTextString(m) }
func (*ReplyTextFormatReq) ProtoMessage()    {}
func (*ReplyTextFormatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{45}
}
func (m *ReplyTextFormatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReplyTextFormatReq.Unmarshal(m, b)
}
func (m *ReplyTextFormatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReplyTextFormatReq.Marshal(b, m, deterministic)
}
func (dst *ReplyTextFormatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplyTextFormatReq.Merge(dst, src)
}
func (m *ReplyTextFormatReq) XXX_Size() int {
	return xxx_messageInfo_ReplyTextFormatReq.Size(m)
}
func (m *ReplyTextFormatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplyTextFormatReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReplyTextFormatReq proto.InternalMessageInfo

func (m *ReplyTextFormatReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *ReplyTextFormatReq) GetFormatType() ReplyTextFormatReq_FormatType {
	if m != nil {
		return m.FormatType
	}
	return ReplyTextFormatReq_FormatType_Default
}

type ReplyTextFormatResp struct {
	Texts                []string `protobuf:"bytes,1,rep,name=texts,proto3" json:"texts,omitempty"`
	DelayTimes           []int64  `protobuf:"varint,2,rep,packed,name=delay_times,json=delayTimes,proto3" json:"delay_times,omitempty"`
	Data                 string   `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReplyTextFormatResp) Reset()         { *m = ReplyTextFormatResp{} }
func (m *ReplyTextFormatResp) String() string { return proto.CompactTextString(m) }
func (*ReplyTextFormatResp) ProtoMessage()    {}
func (*ReplyTextFormatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{46}
}
func (m *ReplyTextFormatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReplyTextFormatResp.Unmarshal(m, b)
}
func (m *ReplyTextFormatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReplyTextFormatResp.Marshal(b, m, deterministic)
}
func (dst *ReplyTextFormatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplyTextFormatResp.Merge(dst, src)
}
func (m *ReplyTextFormatResp) XXX_Size() int {
	return xxx_messageInfo_ReplyTextFormatResp.Size(m)
}
func (m *ReplyTextFormatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplyTextFormatResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReplyTextFormatResp proto.InternalMessageInfo

func (m *ReplyTextFormatResp) GetTexts() []string {
	if m != nil {
		return m.Texts
	}
	return nil
}

func (m *ReplyTextFormatResp) GetDelayTimes() []int64 {
	if m != nil {
		return m.DelayTimes
	}
	return nil
}

func (m *ReplyTextFormatResp) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

type DeleteCharacterReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AiPartnerId          uint32   `protobuf:"varint,2,opt,name=ai_partner_id,json=aiPartnerId,proto3" json:"ai_partner_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteCharacterReq) Reset()         { *m = DeleteCharacterReq{} }
func (m *DeleteCharacterReq) String() string { return proto.CompactTextString(m) }
func (*DeleteCharacterReq) ProtoMessage()    {}
func (*DeleteCharacterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{47}
}
func (m *DeleteCharacterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteCharacterReq.Unmarshal(m, b)
}
func (m *DeleteCharacterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteCharacterReq.Marshal(b, m, deterministic)
}
func (dst *DeleteCharacterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteCharacterReq.Merge(dst, src)
}
func (m *DeleteCharacterReq) XXX_Size() int {
	return xxx_messageInfo_DeleteCharacterReq.Size(m)
}
func (m *DeleteCharacterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteCharacterReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteCharacterReq proto.InternalMessageInfo

func (m *DeleteCharacterReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DeleteCharacterReq) GetAiPartnerId() uint32 {
	if m != nil {
		return m.AiPartnerId
	}
	return 0
}

type DeleteCharacterResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteCharacterResp) Reset()         { *m = DeleteCharacterResp{} }
func (m *DeleteCharacterResp) String() string { return proto.CompactTextString(m) }
func (*DeleteCharacterResp) ProtoMessage()    {}
func (*DeleteCharacterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{48}
}
func (m *DeleteCharacterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteCharacterResp.Unmarshal(m, b)
}
func (m *DeleteCharacterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteCharacterResp.Marshal(b, m, deterministic)
}
func (dst *DeleteCharacterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteCharacterResp.Merge(dst, src)
}
func (m *DeleteCharacterResp) XXX_Size() int {
	return xxx_messageInfo_DeleteCharacterResp.Size(m)
}
func (m *DeleteCharacterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteCharacterResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteCharacterResp proto.InternalMessageInfo

type CustomVoice struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Weight               float32  `protobuf:"fixed32,2,opt,name=weight,proto3" json:"weight,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomVoice) Reset()         { *m = CustomVoice{} }
func (m *CustomVoice) String() string { return proto.CompactTextString(m) }
func (*CustomVoice) ProtoMessage()    {}
func (*CustomVoice) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{49}
}
func (m *CustomVoice) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomVoice.Unmarshal(m, b)
}
func (m *CustomVoice) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomVoice.Marshal(b, m, deterministic)
}
func (dst *CustomVoice) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomVoice.Merge(dst, src)
}
func (m *CustomVoice) XXX_Size() int {
	return xxx_messageInfo_CustomVoice.Size(m)
}
func (m *CustomVoice) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomVoice.DiscardUnknown(m)
}

var xxx_messageInfo_CustomVoice proto.InternalMessageInfo

func (m *CustomVoice) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CustomVoice) GetWeight() float32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

type SetCharacterInfoReq struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AiPartnerId          uint32         `protobuf:"varint,2,opt,name=ai_partner_id,json=aiPartnerId,proto3" json:"ai_partner_id,omitempty"`
	Role                 *AIRole        `protobuf:"bytes,3,opt,name=role,proto3" json:"role,omitempty"`
	Source               uint32         `protobuf:"varint,4,opt,name=source,proto3" json:"source,omitempty"`
	Name                 string         `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	CallName             string         `protobuf:"bytes,6,opt,name=call_name,json=callName,proto3" json:"call_name,omitempty"`
	RoleType             uint32         `protobuf:"varint,7,opt,name=role_type,json=roleType,proto3" json:"role_type,omitempty"`
	CustomVoices         []*CustomVoice `protobuf:"bytes,8,rep,name=custom_voices,json=customVoices,proto3" json:"custom_voices,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SetCharacterInfoReq) Reset()         { *m = SetCharacterInfoReq{} }
func (m *SetCharacterInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetCharacterInfoReq) ProtoMessage()    {}
func (*SetCharacterInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{50}
}
func (m *SetCharacterInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCharacterInfoReq.Unmarshal(m, b)
}
func (m *SetCharacterInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCharacterInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetCharacterInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCharacterInfoReq.Merge(dst, src)
}
func (m *SetCharacterInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetCharacterInfoReq.Size(m)
}
func (m *SetCharacterInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCharacterInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetCharacterInfoReq proto.InternalMessageInfo

func (m *SetCharacterInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetCharacterInfoReq) GetAiPartnerId() uint32 {
	if m != nil {
		return m.AiPartnerId
	}
	return 0
}

func (m *SetCharacterInfoReq) GetRole() *AIRole {
	if m != nil {
		return m.Role
	}
	return nil
}

func (m *SetCharacterInfoReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *SetCharacterInfoReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SetCharacterInfoReq) GetCallName() string {
	if m != nil {
		return m.CallName
	}
	return ""
}

func (m *SetCharacterInfoReq) GetRoleType() uint32 {
	if m != nil {
		return m.RoleType
	}
	return 0
}

func (m *SetCharacterInfoReq) GetCustomVoices() []*CustomVoice {
	if m != nil {
		return m.CustomVoices
	}
	return nil
}

type AIRole struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// AI风格
	Style string `protobuf:"bytes,2,opt,name=style,proto3" json:"style,omitempty"`
	// AI性别 0:女 1:男
	Sex                  int32    `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIRole) Reset()         { *m = AIRole{} }
func (m *AIRole) String() string { return proto.CompactTextString(m) }
func (*AIRole) ProtoMessage()    {}
func (*AIRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{51}
}
func (m *AIRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIRole.Unmarshal(m, b)
}
func (m *AIRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIRole.Marshal(b, m, deterministic)
}
func (dst *AIRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIRole.Merge(dst, src)
}
func (m *AIRole) XXX_Size() int {
	return xxx_messageInfo_AIRole.Size(m)
}
func (m *AIRole) XXX_DiscardUnknown() {
	xxx_messageInfo_AIRole.DiscardUnknown(m)
}

var xxx_messageInfo_AIRole proto.InternalMessageInfo

func (m *AIRole) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AIRole) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

func (m *AIRole) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type SetCharacterInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetCharacterInfoResp) Reset()         { *m = SetCharacterInfoResp{} }
func (m *SetCharacterInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetCharacterInfoResp) ProtoMessage()    {}
func (*SetCharacterInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{52}
}
func (m *SetCharacterInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCharacterInfoResp.Unmarshal(m, b)
}
func (m *SetCharacterInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCharacterInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetCharacterInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCharacterInfoResp.Merge(dst, src)
}
func (m *SetCharacterInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetCharacterInfoResp.Size(m)
}
func (m *SetCharacterInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCharacterInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetCharacterInfoResp proto.InternalMessageInfo

type ReceiveMsgFromUserReq struct {
	Uid       uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId uint32 `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	// 消息类型
	MsgType ReceiveMsgFromUserReq_MsgType `protobuf:"varint,3,opt,name=msg_type,json=msgType,proto3,enum=rcmd.game_character.ReceiveMsgFromUserReq_MsgType" json:"msg_type,omitempty"`
	// 消息内容
	Content              string   `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Ext                  []byte   `protobuf:"bytes,5,opt,name=ext,proto3" json:"ext,omitempty"`
	MsgId                string   `protobuf:"bytes,6,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceiveMsgFromUserReq) Reset()         { *m = ReceiveMsgFromUserReq{} }
func (m *ReceiveMsgFromUserReq) String() string { return proto.CompactTextString(m) }
func (*ReceiveMsgFromUserReq) ProtoMessage()    {}
func (*ReceiveMsgFromUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{53}
}
func (m *ReceiveMsgFromUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveMsgFromUserReq.Unmarshal(m, b)
}
func (m *ReceiveMsgFromUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveMsgFromUserReq.Marshal(b, m, deterministic)
}
func (dst *ReceiveMsgFromUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveMsgFromUserReq.Merge(dst, src)
}
func (m *ReceiveMsgFromUserReq) XXX_Size() int {
	return xxx_messageInfo_ReceiveMsgFromUserReq.Size(m)
}
func (m *ReceiveMsgFromUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveMsgFromUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveMsgFromUserReq proto.InternalMessageInfo

func (m *ReceiveMsgFromUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReceiveMsgFromUserReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *ReceiveMsgFromUserReq) GetMsgType() ReceiveMsgFromUserReq_MsgType {
	if m != nil {
		return m.MsgType
	}
	return ReceiveMsgFromUserReq_MsgType_Unknown
}

func (m *ReceiveMsgFromUserReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ReceiveMsgFromUserReq) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *ReceiveMsgFromUserReq) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

type ReceiveMsgFromUserResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceiveMsgFromUserResp) Reset()         { *m = ReceiveMsgFromUserResp{} }
func (m *ReceiveMsgFromUserResp) String() string { return proto.CompactTextString(m) }
func (*ReceiveMsgFromUserResp) ProtoMessage()    {}
func (*ReceiveMsgFromUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{54}
}
func (m *ReceiveMsgFromUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveMsgFromUserResp.Unmarshal(m, b)
}
func (m *ReceiveMsgFromUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveMsgFromUserResp.Marshal(b, m, deterministic)
}
func (dst *ReceiveMsgFromUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveMsgFromUserResp.Merge(dst, src)
}
func (m *ReceiveMsgFromUserResp) XXX_Size() int {
	return xxx_messageInfo_ReceiveMsgFromUserResp.Size(m)
}
func (m *ReceiveMsgFromUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveMsgFromUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveMsgFromUserResp proto.InternalMessageInfo

type UserEnterChattingNotifyReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AiPartnerId          uint32   `protobuf:"varint,2,opt,name=ai_partner_id,json=aiPartnerId,proto3" json:"ai_partner_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserEnterChattingNotifyReq) Reset()         { *m = UserEnterChattingNotifyReq{} }
func (m *UserEnterChattingNotifyReq) String() string { return proto.CompactTextString(m) }
func (*UserEnterChattingNotifyReq) ProtoMessage()    {}
func (*UserEnterChattingNotifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{55}
}
func (m *UserEnterChattingNotifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserEnterChattingNotifyReq.Unmarshal(m, b)
}
func (m *UserEnterChattingNotifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserEnterChattingNotifyReq.Marshal(b, m, deterministic)
}
func (dst *UserEnterChattingNotifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserEnterChattingNotifyReq.Merge(dst, src)
}
func (m *UserEnterChattingNotifyReq) XXX_Size() int {
	return xxx_messageInfo_UserEnterChattingNotifyReq.Size(m)
}
func (m *UserEnterChattingNotifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserEnterChattingNotifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserEnterChattingNotifyReq proto.InternalMessageInfo

func (m *UserEnterChattingNotifyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserEnterChattingNotifyReq) GetAiPartnerId() uint32 {
	if m != nil {
		return m.AiPartnerId
	}
	return 0
}

type UserEnterChattingNotifyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserEnterChattingNotifyResp) Reset()         { *m = UserEnterChattingNotifyResp{} }
func (m *UserEnterChattingNotifyResp) String() string { return proto.CompactTextString(m) }
func (*UserEnterChattingNotifyResp) ProtoMessage()    {}
func (*UserEnterChattingNotifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{56}
}
func (m *UserEnterChattingNotifyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserEnterChattingNotifyResp.Unmarshal(m, b)
}
func (m *UserEnterChattingNotifyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserEnterChattingNotifyResp.Marshal(b, m, deterministic)
}
func (dst *UserEnterChattingNotifyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserEnterChattingNotifyResp.Merge(dst, src)
}
func (m *UserEnterChattingNotifyResp) XXX_Size() int {
	return xxx_messageInfo_UserEnterChattingNotifyResp.Size(m)
}
func (m *UserEnterChattingNotifyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserEnterChattingNotifyResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserEnterChattingNotifyResp proto.InternalMessageInfo

type TestReq struct {
	Uid                        uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AiPartnerId                uint32         `protobuf:"varint,2,opt,name=ai_partner_id,json=aiPartnerId,proto3" json:"ai_partner_id,omitempty"`
	Action                     TestReq_Action `protobuf:"varint,3,opt,name=action,proto3,enum=rcmd.game_character.TestReq_Action" json:"action,omitempty"`
	RoleCreatorPushDayTs       int64          `protobuf:"varint,4,opt,name=role_creator_push_day_ts,json=roleCreatorPushDayTs,proto3" json:"role_creator_push_day_ts,omitempty"`
	RoleCreatorMinChattedCount uint32         `protobuf:"varint,5,opt,name=role_creator_min_chatted_count,json=roleCreatorMinChattedCount,proto3" json:"role_creator_min_chatted_count,omitempty"`
	RoleId                     uint32         `protobuf:"varint,6,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	RoleCreatorUid             uint32         `protobuf:"varint,7,opt,name=role_creator_uid,json=roleCreatorUid,proto3" json:"role_creator_uid,omitempty"`
	ChattedUid                 uint32         `protobuf:"varint,8,opt,name=chatted_uid,json=chattedUid,proto3" json:"chatted_uid,omitempty"`
	ChatTs                     int64          `protobuf:"varint,9,opt,name=chat_ts,json=chatTs,proto3" json:"chat_ts,omitempty"`
	AsrText                    string         `protobuf:"bytes,10,opt,name=asr_text,json=asrText,proto3" json:"asr_text,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}       `json:"-"`
	XXX_unrecognized           []byte         `json:"-"`
	XXX_sizecache              int32          `json:"-"`
}

func (m *TestReq) Reset()         { *m = TestReq{} }
func (m *TestReq) String() string { return proto.CompactTextString(m) }
func (*TestReq) ProtoMessage()    {}
func (*TestReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{57}
}
func (m *TestReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestReq.Unmarshal(m, b)
}
func (m *TestReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestReq.Marshal(b, m, deterministic)
}
func (dst *TestReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestReq.Merge(dst, src)
}
func (m *TestReq) XXX_Size() int {
	return xxx_messageInfo_TestReq.Size(m)
}
func (m *TestReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestReq proto.InternalMessageInfo

func (m *TestReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TestReq) GetAiPartnerId() uint32 {
	if m != nil {
		return m.AiPartnerId
	}
	return 0
}

func (m *TestReq) GetAction() TestReq_Action {
	if m != nil {
		return m.Action
	}
	return TestReq_Action_Unknown
}

func (m *TestReq) GetRoleCreatorPushDayTs() int64 {
	if m != nil {
		return m.RoleCreatorPushDayTs
	}
	return 0
}

func (m *TestReq) GetRoleCreatorMinChattedCount() uint32 {
	if m != nil {
		return m.RoleCreatorMinChattedCount
	}
	return 0
}

func (m *TestReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *TestReq) GetRoleCreatorUid() uint32 {
	if m != nil {
		return m.RoleCreatorUid
	}
	return 0
}

func (m *TestReq) GetChattedUid() uint32 {
	if m != nil {
		return m.ChattedUid
	}
	return 0
}

func (m *TestReq) GetChatTs() int64 {
	if m != nil {
		return m.ChatTs
	}
	return 0
}

func (m *TestReq) GetAsrText() string {
	if m != nil {
		return m.AsrText
	}
	return ""
}

type TestResp struct {
	Data                 string   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestResp) Reset()         { *m = TestResp{} }
func (m *TestResp) String() string { return proto.CompactTextString(m) }
func (*TestResp) ProtoMessage()    {}
func (*TestResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{58}
}
func (m *TestResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestResp.Unmarshal(m, b)
}
func (m *TestResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestResp.Marshal(b, m, deterministic)
}
func (dst *TestResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestResp.Merge(dst, src)
}
func (m *TestResp) XXX_Size() int {
	return xxx_messageInfo_TestResp.Size(m)
}
func (m *TestResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestResp proto.InternalMessageInfo

func (m *TestResp) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

type GenRecommendReplyReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenRecommendReplyReq) Reset()         { *m = GenRecommendReplyReq{} }
func (m *GenRecommendReplyReq) String() string { return proto.CompactTextString(m) }
func (*GenRecommendReplyReq) ProtoMessage()    {}
func (*GenRecommendReplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{59}
}
func (m *GenRecommendReplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenRecommendReplyReq.Unmarshal(m, b)
}
func (m *GenRecommendReplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenRecommendReplyReq.Marshal(b, m, deterministic)
}
func (dst *GenRecommendReplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenRecommendReplyReq.Merge(dst, src)
}
func (m *GenRecommendReplyReq) XXX_Size() int {
	return xxx_messageInfo_GenRecommendReplyReq.Size(m)
}
func (m *GenRecommendReplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GenRecommendReplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GenRecommendReplyReq proto.InternalMessageInfo

func (m *GenRecommendReplyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GenRecommendReplyReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GenRecommendReplyReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GenRecommendReplyResp struct {
	ReplyList            []string `protobuf:"bytes,1,rep,name=reply_list,json=replyList,proto3" json:"reply_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenRecommendReplyResp) Reset()         { *m = GenRecommendReplyResp{} }
func (m *GenRecommendReplyResp) String() string { return proto.CompactTextString(m) }
func (*GenRecommendReplyResp) ProtoMessage()    {}
func (*GenRecommendReplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{60}
}
func (m *GenRecommendReplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenRecommendReplyResp.Unmarshal(m, b)
}
func (m *GenRecommendReplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenRecommendReplyResp.Marshal(b, m, deterministic)
}
func (dst *GenRecommendReplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenRecommendReplyResp.Merge(dst, src)
}
func (m *GenRecommendReplyResp) XXX_Size() int {
	return xxx_messageInfo_GenRecommendReplyResp.Size(m)
}
func (m *GenRecommendReplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GenRecommendReplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GenRecommendReplyResp proto.InternalMessageInfo

func (m *GenRecommendReplyResp) GetReplyList() []string {
	if m != nil {
		return m.ReplyList
	}
	return nil
}

type ShowPartnerSilentReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowPartnerSilentReq) Reset()         { *m = ShowPartnerSilentReq{} }
func (m *ShowPartnerSilentReq) String() string { return proto.CompactTextString(m) }
func (*ShowPartnerSilentReq) ProtoMessage()    {}
func (*ShowPartnerSilentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{61}
}
func (m *ShowPartnerSilentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowPartnerSilentReq.Unmarshal(m, b)
}
func (m *ShowPartnerSilentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowPartnerSilentReq.Marshal(b, m, deterministic)
}
func (dst *ShowPartnerSilentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowPartnerSilentReq.Merge(dst, src)
}
func (m *ShowPartnerSilentReq) XXX_Size() int {
	return xxx_messageInfo_ShowPartnerSilentReq.Size(m)
}
func (m *ShowPartnerSilentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowPartnerSilentReq.DiscardUnknown(m)
}

var xxx_messageInfo_ShowPartnerSilentReq proto.InternalMessageInfo

func (m *ShowPartnerSilentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ShowPartnerSilentReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *ShowPartnerSilentReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type ShowPartnerSilentRsp struct {
	Show                 bool     `protobuf:"varint,1,opt,name=show,proto3" json:"show,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowPartnerSilentRsp) Reset()         { *m = ShowPartnerSilentRsp{} }
func (m *ShowPartnerSilentRsp) String() string { return proto.CompactTextString(m) }
func (*ShowPartnerSilentRsp) ProtoMessage()    {}
func (*ShowPartnerSilentRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{62}
}
func (m *ShowPartnerSilentRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowPartnerSilentRsp.Unmarshal(m, b)
}
func (m *ShowPartnerSilentRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowPartnerSilentRsp.Marshal(b, m, deterministic)
}
func (dst *ShowPartnerSilentRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowPartnerSilentRsp.Merge(dst, src)
}
func (m *ShowPartnerSilentRsp) XXX_Size() int {
	return xxx_messageInfo_ShowPartnerSilentRsp.Size(m)
}
func (m *ShowPartnerSilentRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowPartnerSilentRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ShowPartnerSilentRsp proto.InternalMessageInfo

func (m *ShowPartnerSilentRsp) GetShow() bool {
	if m != nil {
		return m.Show
	}
	return false
}

type ShowContinueChatReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowContinueChatReq) Reset()         { *m = ShowContinueChatReq{} }
func (m *ShowContinueChatReq) String() string { return proto.CompactTextString(m) }
func (*ShowContinueChatReq) ProtoMessage()    {}
func (*ShowContinueChatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{63}
}
func (m *ShowContinueChatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowContinueChatReq.Unmarshal(m, b)
}
func (m *ShowContinueChatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowContinueChatReq.Marshal(b, m, deterministic)
}
func (dst *ShowContinueChatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowContinueChatReq.Merge(dst, src)
}
func (m *ShowContinueChatReq) XXX_Size() int {
	return xxx_messageInfo_ShowContinueChatReq.Size(m)
}
func (m *ShowContinueChatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowContinueChatReq.DiscardUnknown(m)
}

var xxx_messageInfo_ShowContinueChatReq proto.InternalMessageInfo

func (m *ShowContinueChatReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ShowContinueChatReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *ShowContinueChatReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *ShowContinueChatReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

type ShowContinueChatRsp struct {
	Show                 bool     `protobuf:"varint,1,opt,name=show,proto3" json:"show,omitempty"`
	ChatLeftNum          uint32   `protobuf:"varint,2,opt,name=chat_left_num,json=chatLeftNum,proto3" json:"chat_left_num,omitempty"`
	ContinueRoundLeftNum uint32   `protobuf:"varint,3,opt,name=continue_round_left_num,json=continueRoundLeftNum,proto3" json:"continue_round_left_num,omitempty"`
	ContinueTotalLeftNum uint32   `protobuf:"varint,4,opt,name=continue_total_left_num,json=continueTotalLeftNum,proto3" json:"continue_total_left_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowContinueChatRsp) Reset()         { *m = ShowContinueChatRsp{} }
func (m *ShowContinueChatRsp) String() string { return proto.CompactTextString(m) }
func (*ShowContinueChatRsp) ProtoMessage()    {}
func (*ShowContinueChatRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{64}
}
func (m *ShowContinueChatRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowContinueChatRsp.Unmarshal(m, b)
}
func (m *ShowContinueChatRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowContinueChatRsp.Marshal(b, m, deterministic)
}
func (dst *ShowContinueChatRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowContinueChatRsp.Merge(dst, src)
}
func (m *ShowContinueChatRsp) XXX_Size() int {
	return xxx_messageInfo_ShowContinueChatRsp.Size(m)
}
func (m *ShowContinueChatRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowContinueChatRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ShowContinueChatRsp proto.InternalMessageInfo

func (m *ShowContinueChatRsp) GetShow() bool {
	if m != nil {
		return m.Show
	}
	return false
}

func (m *ShowContinueChatRsp) GetChatLeftNum() uint32 {
	if m != nil {
		return m.ChatLeftNum
	}
	return 0
}

func (m *ShowContinueChatRsp) GetContinueRoundLeftNum() uint32 {
	if m != nil {
		return m.ContinueRoundLeftNum
	}
	return 0
}

func (m *ShowContinueChatRsp) GetContinueTotalLeftNum() uint32 {
	if m != nil {
		return m.ContinueTotalLeftNum
	}
	return 0
}

type ContinueChatReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContinueChatReq) Reset()         { *m = ContinueChatReq{} }
func (m *ContinueChatReq) String() string { return proto.CompactTextString(m) }
func (*ContinueChatReq) ProtoMessage()    {}
func (*ContinueChatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{65}
}
func (m *ContinueChatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContinueChatReq.Unmarshal(m, b)
}
func (m *ContinueChatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContinueChatReq.Marshal(b, m, deterministic)
}
func (dst *ContinueChatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContinueChatReq.Merge(dst, src)
}
func (m *ContinueChatReq) XXX_Size() int {
	return xxx_messageInfo_ContinueChatReq.Size(m)
}
func (m *ContinueChatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ContinueChatReq.DiscardUnknown(m)
}

var xxx_messageInfo_ContinueChatReq proto.InternalMessageInfo

func (m *ContinueChatReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ContinueChatReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *ContinueChatReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type ContinueChatRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContinueChatRsp) Reset()         { *m = ContinueChatRsp{} }
func (m *ContinueChatRsp) String() string { return proto.CompactTextString(m) }
func (*ContinueChatRsp) ProtoMessage()    {}
func (*ContinueChatRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{66}
}
func (m *ContinueChatRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContinueChatRsp.Unmarshal(m, b)
}
func (m *ContinueChatRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContinueChatRsp.Marshal(b, m, deterministic)
}
func (dst *ContinueChatRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContinueChatRsp.Merge(dst, src)
}
func (m *ContinueChatRsp) XXX_Size() int {
	return xxx_messageInfo_ContinueChatRsp.Size(m)
}
func (m *ContinueChatRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ContinueChatRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ContinueChatRsp proto.InternalMessageInfo

type TestRoleSendMsgReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Msg                  string   `protobuf:"bytes,4,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestRoleSendMsgReq) Reset()         { *m = TestRoleSendMsgReq{} }
func (m *TestRoleSendMsgReq) String() string { return proto.CompactTextString(m) }
func (*TestRoleSendMsgReq) ProtoMessage()    {}
func (*TestRoleSendMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{67}
}
func (m *TestRoleSendMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestRoleSendMsgReq.Unmarshal(m, b)
}
func (m *TestRoleSendMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestRoleSendMsgReq.Marshal(b, m, deterministic)
}
func (dst *TestRoleSendMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestRoleSendMsgReq.Merge(dst, src)
}
func (m *TestRoleSendMsgReq) XXX_Size() int {
	return xxx_messageInfo_TestRoleSendMsgReq.Size(m)
}
func (m *TestRoleSendMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestRoleSendMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestRoleSendMsgReq proto.InternalMessageInfo

func (m *TestRoleSendMsgReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TestRoleSendMsgReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *TestRoleSendMsgReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *TestRoleSendMsgReq) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type TestRoleSendMsgRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestRoleSendMsgRsp) Reset()         { *m = TestRoleSendMsgRsp{} }
func (m *TestRoleSendMsgRsp) String() string { return proto.CompactTextString(m) }
func (*TestRoleSendMsgRsp) ProtoMessage()    {}
func (*TestRoleSendMsgRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{68}
}
func (m *TestRoleSendMsgRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestRoleSendMsgRsp.Unmarshal(m, b)
}
func (m *TestRoleSendMsgRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestRoleSendMsgRsp.Marshal(b, m, deterministic)
}
func (dst *TestRoleSendMsgRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestRoleSendMsgRsp.Merge(dst, src)
}
func (m *TestRoleSendMsgRsp) XXX_Size() int {
	return xxx_messageInfo_TestRoleSendMsgRsp.Size(m)
}
func (m *TestRoleSendMsgRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestRoleSendMsgRsp.DiscardUnknown(m)
}

var xxx_messageInfo_TestRoleSendMsgRsp proto.InternalMessageInfo

type GetTarotConfigReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTarotConfigReq) Reset()         { *m = GetTarotConfigReq{} }
func (m *GetTarotConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetTarotConfigReq) ProtoMessage()    {}
func (*GetTarotConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{69}
}
func (m *GetTarotConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTarotConfigReq.Unmarshal(m, b)
}
func (m *GetTarotConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTarotConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetTarotConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTarotConfigReq.Merge(dst, src)
}
func (m *GetTarotConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetTarotConfigReq.Size(m)
}
func (m *GetTarotConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTarotConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTarotConfigReq proto.InternalMessageInfo

func (m *GetTarotConfigReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTarotConfigReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GetTarotConfigReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetTarotConfigReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

type TarotCard struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Picture              string   `protobuf:"bytes,3,opt,name=picture,proto3" json:"picture,omitempty"`
	Text                 string   `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"`
	CalendarIcon         string   `protobuf:"bytes,5,opt,name=calendar_icon,json=calendarIcon,proto3" json:"calendar_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TarotCard) Reset()         { *m = TarotCard{} }
func (m *TarotCard) String() string { return proto.CompactTextString(m) }
func (*TarotCard) ProtoMessage()    {}
func (*TarotCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{70}
}
func (m *TarotCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TarotCard.Unmarshal(m, b)
}
func (m *TarotCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TarotCard.Marshal(b, m, deterministic)
}
func (dst *TarotCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TarotCard.Merge(dst, src)
}
func (m *TarotCard) XXX_Size() int {
	return xxx_messageInfo_TarotCard.Size(m)
}
func (m *TarotCard) XXX_DiscardUnknown() {
	xxx_messageInfo_TarotCard.DiscardUnknown(m)
}

var xxx_messageInfo_TarotCard proto.InternalMessageInfo

func (m *TarotCard) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TarotCard) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TarotCard) GetPicture() string {
	if m != nil {
		return m.Picture
	}
	return ""
}

func (m *TarotCard) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *TarotCard) GetCalendarIcon() string {
	if m != nil {
		return m.CalendarIcon
	}
	return ""
}

type GetTarotConfigRsp struct {
	CardList             []*TarotCard `protobuf:"bytes,1,rep,name=card_list,json=cardList,proto3" json:"card_list,omitempty"`
	ServerTs             uint64       `protobuf:"varint,2,opt,name=server_ts,json=serverTs,proto3" json:"server_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetTarotConfigRsp) Reset()         { *m = GetTarotConfigRsp{} }
func (m *GetTarotConfigRsp) String() string { return proto.CompactTextString(m) }
func (*GetTarotConfigRsp) ProtoMessage()    {}
func (*GetTarotConfigRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{71}
}
func (m *GetTarotConfigRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTarotConfigRsp.Unmarshal(m, b)
}
func (m *GetTarotConfigRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTarotConfigRsp.Marshal(b, m, deterministic)
}
func (dst *GetTarotConfigRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTarotConfigRsp.Merge(dst, src)
}
func (m *GetTarotConfigRsp) XXX_Size() int {
	return xxx_messageInfo_GetTarotConfigRsp.Size(m)
}
func (m *GetTarotConfigRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTarotConfigRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTarotConfigRsp proto.InternalMessageInfo

func (m *GetTarotConfigRsp) GetCardList() []*TarotCard {
	if m != nil {
		return m.CardList
	}
	return nil
}

func (m *GetTarotConfigRsp) GetServerTs() uint64 {
	if m != nil {
		return m.ServerTs
	}
	return 0
}

type OpenTarotCardReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	Date                 string   `protobuf:"bytes,5,opt,name=date,proto3" json:"date,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpenTarotCardReq) Reset()         { *m = OpenTarotCardReq{} }
func (m *OpenTarotCardReq) String() string { return proto.CompactTextString(m) }
func (*OpenTarotCardReq) ProtoMessage()    {}
func (*OpenTarotCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{72}
}
func (m *OpenTarotCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenTarotCardReq.Unmarshal(m, b)
}
func (m *OpenTarotCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenTarotCardReq.Marshal(b, m, deterministic)
}
func (dst *OpenTarotCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenTarotCardReq.Merge(dst, src)
}
func (m *OpenTarotCardReq) XXX_Size() int {
	return xxx_messageInfo_OpenTarotCardReq.Size(m)
}
func (m *OpenTarotCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenTarotCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_OpenTarotCardReq proto.InternalMessageInfo

func (m *OpenTarotCardReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OpenTarotCardReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *OpenTarotCardReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *OpenTarotCardReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

func (m *OpenTarotCardReq) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

type OpenTarotCardRsp struct {
	Result               *TarotResult `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	FirstOpen            bool         `protobuf:"varint,2,opt,name=first_open,json=firstOpen,proto3" json:"first_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OpenTarotCardRsp) Reset()         { *m = OpenTarotCardRsp{} }
func (m *OpenTarotCardRsp) String() string { return proto.CompactTextString(m) }
func (*OpenTarotCardRsp) ProtoMessage()    {}
func (*OpenTarotCardRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{73}
}
func (m *OpenTarotCardRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenTarotCardRsp.Unmarshal(m, b)
}
func (m *OpenTarotCardRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenTarotCardRsp.Marshal(b, m, deterministic)
}
func (dst *OpenTarotCardRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenTarotCardRsp.Merge(dst, src)
}
func (m *OpenTarotCardRsp) XXX_Size() int {
	return xxx_messageInfo_OpenTarotCardRsp.Size(m)
}
func (m *OpenTarotCardRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenTarotCardRsp.DiscardUnknown(m)
}

var xxx_messageInfo_OpenTarotCardRsp proto.InternalMessageInfo

func (m *OpenTarotCardRsp) GetResult() *TarotResult {
	if m != nil {
		return m.Result
	}
	return nil
}

func (m *OpenTarotCardRsp) GetFirstOpen() bool {
	if m != nil {
		return m.FirstOpen
	}
	return false
}

type GetTarotResultReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	Month                string   `protobuf:"bytes,5,opt,name=month,proto3" json:"month,omitempty"`
	Day                  string   `protobuf:"bytes,6,opt,name=day,proto3" json:"day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTarotResultReq) Reset()         { *m = GetTarotResultReq{} }
func (m *GetTarotResultReq) String() string { return proto.CompactTextString(m) }
func (*GetTarotResultReq) ProtoMessage()    {}
func (*GetTarotResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{74}
}
func (m *GetTarotResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTarotResultReq.Unmarshal(m, b)
}
func (m *GetTarotResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTarotResultReq.Marshal(b, m, deterministic)
}
func (dst *GetTarotResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTarotResultReq.Merge(dst, src)
}
func (m *GetTarotResultReq) XXX_Size() int {
	return xxx_messageInfo_GetTarotResultReq.Size(m)
}
func (m *GetTarotResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTarotResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTarotResultReq proto.InternalMessageInfo

func (m *GetTarotResultReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTarotResultReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GetTarotResultReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetTarotResultReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

func (m *GetTarotResultReq) GetMonth() string {
	if m != nil {
		return m.Month
	}
	return ""
}

func (m *GetTarotResultReq) GetDay() string {
	if m != nil {
		return m.Day
	}
	return ""
}

type TarotResult struct {
	Date                 string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	OpenTime             string   `protobuf:"bytes,3,opt,name=open_time,json=openTime,proto3" json:"open_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TarotResult) Reset()         { *m = TarotResult{} }
func (m *TarotResult) String() string { return proto.CompactTextString(m) }
func (*TarotResult) ProtoMessage()    {}
func (*TarotResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{75}
}
func (m *TarotResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TarotResult.Unmarshal(m, b)
}
func (m *TarotResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TarotResult.Marshal(b, m, deterministic)
}
func (dst *TarotResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TarotResult.Merge(dst, src)
}
func (m *TarotResult) XXX_Size() int {
	return xxx_messageInfo_TarotResult.Size(m)
}
func (m *TarotResult) XXX_DiscardUnknown() {
	xxx_messageInfo_TarotResult.DiscardUnknown(m)
}

var xxx_messageInfo_TarotResult proto.InternalMessageInfo

func (m *TarotResult) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *TarotResult) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TarotResult) GetOpenTime() string {
	if m != nil {
		return m.OpenTime
	}
	return ""
}

type GetTarotResultRsp struct {
	ResultList           []*TarotResult `protobuf:"bytes,1,rep,name=result_list,json=resultList,proto3" json:"result_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetTarotResultRsp) Reset()         { *m = GetTarotResultRsp{} }
func (m *GetTarotResultRsp) String() string { return proto.CompactTextString(m) }
func (*GetTarotResultRsp) ProtoMessage()    {}
func (*GetTarotResultRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{76}
}
func (m *GetTarotResultRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTarotResultRsp.Unmarshal(m, b)
}
func (m *GetTarotResultRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTarotResultRsp.Marshal(b, m, deterministic)
}
func (dst *GetTarotResultRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTarotResultRsp.Merge(dst, src)
}
func (m *GetTarotResultRsp) XXX_Size() int {
	return xxx_messageInfo_GetTarotResultRsp.Size(m)
}
func (m *GetTarotResultRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTarotResultRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTarotResultRsp proto.InternalMessageInfo

func (m *GetTarotResultRsp) GetResultList() []*TarotResult {
	if m != nil {
		return m.ResultList
	}
	return nil
}

type GetPetOwnDayReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetOwnDayReq) Reset()         { *m = GetPetOwnDayReq{} }
func (m *GetPetOwnDayReq) String() string { return proto.CompactTextString(m) }
func (*GetPetOwnDayReq) ProtoMessage()    {}
func (*GetPetOwnDayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{77}
}
func (m *GetPetOwnDayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetOwnDayReq.Unmarshal(m, b)
}
func (m *GetPetOwnDayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetOwnDayReq.Marshal(b, m, deterministic)
}
func (dst *GetPetOwnDayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetOwnDayReq.Merge(dst, src)
}
func (m *GetPetOwnDayReq) XXX_Size() int {
	return xxx_messageInfo_GetPetOwnDayReq.Size(m)
}
func (m *GetPetOwnDayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetOwnDayReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetOwnDayReq proto.InternalMessageInfo

func (m *GetPetOwnDayReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPetOwnDayReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GetPetOwnDayReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetPetOwnDayReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

type GetPetOwnDayRsp struct {
	OwnDay               uint32   `protobuf:"varint,1,opt,name=own_day,json=ownDay,proto3" json:"own_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetOwnDayRsp) Reset()         { *m = GetPetOwnDayRsp{} }
func (m *GetPetOwnDayRsp) String() string { return proto.CompactTextString(m) }
func (*GetPetOwnDayRsp) ProtoMessage()    {}
func (*GetPetOwnDayRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{78}
}
func (m *GetPetOwnDayRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetOwnDayRsp.Unmarshal(m, b)
}
func (m *GetPetOwnDayRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetOwnDayRsp.Marshal(b, m, deterministic)
}
func (dst *GetPetOwnDayRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetOwnDayRsp.Merge(dst, src)
}
func (m *GetPetOwnDayRsp) XXX_Size() int {
	return xxx_messageInfo_GetPetOwnDayRsp.Size(m)
}
func (m *GetPetOwnDayRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetOwnDayRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetOwnDayRsp proto.InternalMessageInfo

func (m *GetPetOwnDayRsp) GetOwnDay() uint32 {
	if m != nil {
		return m.OwnDay
	}
	return 0
}

type TestPetSendMsgReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Msgtype              uint32   `protobuf:"varint,2,opt,name=msgtype,proto3" json:"msgtype,omitempty"`
	BubbleMsg            string   `protobuf:"bytes,3,opt,name=bubble_msg,json=bubbleMsg,proto3" json:"bubble_msg,omitempty"`
	DisplayMsg           string   `protobuf:"bytes,4,opt,name=display_msg,json=displayMsg,proto3" json:"display_msg,omitempty"`
	Title                string   `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestPetSendMsgReq) Reset()         { *m = TestPetSendMsgReq{} }
func (m *TestPetSendMsgReq) String() string { return proto.CompactTextString(m) }
func (*TestPetSendMsgReq) ProtoMessage()    {}
func (*TestPetSendMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{79}
}
func (m *TestPetSendMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestPetSendMsgReq.Unmarshal(m, b)
}
func (m *TestPetSendMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestPetSendMsgReq.Marshal(b, m, deterministic)
}
func (dst *TestPetSendMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestPetSendMsgReq.Merge(dst, src)
}
func (m *TestPetSendMsgReq) XXX_Size() int {
	return xxx_messageInfo_TestPetSendMsgReq.Size(m)
}
func (m *TestPetSendMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestPetSendMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestPetSendMsgReq proto.InternalMessageInfo

func (m *TestPetSendMsgReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TestPetSendMsgReq) GetMsgtype() uint32 {
	if m != nil {
		return m.Msgtype
	}
	return 0
}

func (m *TestPetSendMsgReq) GetBubbleMsg() string {
	if m != nil {
		return m.BubbleMsg
	}
	return ""
}

func (m *TestPetSendMsgReq) GetDisplayMsg() string {
	if m != nil {
		return m.DisplayMsg
	}
	return ""
}

func (m *TestPetSendMsgReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type TestPetSendMsgRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestPetSendMsgRsp) Reset()         { *m = TestPetSendMsgRsp{} }
func (m *TestPetSendMsgRsp) String() string { return proto.CompactTextString(m) }
func (*TestPetSendMsgRsp) ProtoMessage()    {}
func (*TestPetSendMsgRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{80}
}
func (m *TestPetSendMsgRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestPetSendMsgRsp.Unmarshal(m, b)
}
func (m *TestPetSendMsgRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestPetSendMsgRsp.Marshal(b, m, deterministic)
}
func (dst *TestPetSendMsgRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestPetSendMsgRsp.Merge(dst, src)
}
func (m *TestPetSendMsgRsp) XXX_Size() int {
	return xxx_messageInfo_TestPetSendMsgRsp.Size(m)
}
func (m *TestPetSendMsgRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestPetSendMsgRsp.DiscardUnknown(m)
}

var xxx_messageInfo_TestPetSendMsgRsp proto.InternalMessageInfo

type GetPetStoryNumReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetStoryNumReq) Reset()         { *m = GetPetStoryNumReq{} }
func (m *GetPetStoryNumReq) String() string { return proto.CompactTextString(m) }
func (*GetPetStoryNumReq) ProtoMessage()    {}
func (*GetPetStoryNumReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{81}
}
func (m *GetPetStoryNumReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetStoryNumReq.Unmarshal(m, b)
}
func (m *GetPetStoryNumReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetStoryNumReq.Marshal(b, m, deterministic)
}
func (dst *GetPetStoryNumReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetStoryNumReq.Merge(dst, src)
}
func (m *GetPetStoryNumReq) XXX_Size() int {
	return xxx_messageInfo_GetPetStoryNumReq.Size(m)
}
func (m *GetPetStoryNumReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetStoryNumReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetStoryNumReq proto.InternalMessageInfo

func (m *GetPetStoryNumReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPetStoryNumReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GetPetStoryNumReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetPetStoryNumReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

type GetPetStoryNumRsp struct {
	Total                uint32   `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Left                 uint32   `protobuf:"varint,2,opt,name=left,proto3" json:"left,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPetStoryNumRsp) Reset()         { *m = GetPetStoryNumRsp{} }
func (m *GetPetStoryNumRsp) String() string { return proto.CompactTextString(m) }
func (*GetPetStoryNumRsp) ProtoMessage()    {}
func (*GetPetStoryNumRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{82}
}
func (m *GetPetStoryNumRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPetStoryNumRsp.Unmarshal(m, b)
}
func (m *GetPetStoryNumRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPetStoryNumRsp.Marshal(b, m, deterministic)
}
func (dst *GetPetStoryNumRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPetStoryNumRsp.Merge(dst, src)
}
func (m *GetPetStoryNumRsp) XXX_Size() int {
	return xxx_messageInfo_GetPetStoryNumRsp.Size(m)
}
func (m *GetPetStoryNumRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPetStoryNumRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPetStoryNumRsp proto.InternalMessageInfo

func (m *GetPetStoryNumRsp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetPetStoryNumRsp) GetLeft() uint32 {
	if m != nil {
		return m.Left
	}
	return 0
}

type PetStory struct {
	StoryId              string       `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	StoryParts           []*StoryPart `protobuf:"bytes,2,rep,name=story_parts,json=storyParts,proto3" json:"story_parts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PetStory) Reset()         { *m = PetStory{} }
func (m *PetStory) String() string { return proto.CompactTextString(m) }
func (*PetStory) ProtoMessage()    {}
func (*PetStory) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{83}
}
func (m *PetStory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PetStory.Unmarshal(m, b)
}
func (m *PetStory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PetStory.Marshal(b, m, deterministic)
}
func (dst *PetStory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PetStory.Merge(dst, src)
}
func (m *PetStory) XXX_Size() int {
	return xxx_messageInfo_PetStory.Size(m)
}
func (m *PetStory) XXX_DiscardUnknown() {
	xxx_messageInfo_PetStory.DiscardUnknown(m)
}

var xxx_messageInfo_PetStory proto.InternalMessageInfo

func (m *PetStory) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

func (m *PetStory) GetStoryParts() []*StoryPart {
	if m != nil {
		return m.StoryParts
	}
	return nil
}

type StoryPart struct {
	PartNum              uint32   `protobuf:"varint,1,opt,name=part_num,json=partNum,proto3" json:"part_num,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Audio                string   `protobuf:"bytes,3,opt,name=audio,proto3" json:"audio,omitempty"`
	Seconds              uint32   `protobuf:"varint,4,opt,name=seconds,proto3" json:"seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoryPart) Reset()         { *m = StoryPart{} }
func (m *StoryPart) String() string { return proto.CompactTextString(m) }
func (*StoryPart) ProtoMessage()    {}
func (*StoryPart) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{84}
}
func (m *StoryPart) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoryPart.Unmarshal(m, b)
}
func (m *StoryPart) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoryPart.Marshal(b, m, deterministic)
}
func (dst *StoryPart) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoryPart.Merge(dst, src)
}
func (m *StoryPart) XXX_Size() int {
	return xxx_messageInfo_StoryPart.Size(m)
}
func (m *StoryPart) XXX_DiscardUnknown() {
	xxx_messageInfo_StoryPart.DiscardUnknown(m)
}

var xxx_messageInfo_StoryPart proto.InternalMessageInfo

func (m *StoryPart) GetPartNum() uint32 {
	if m != nil {
		return m.PartNum
	}
	return 0
}

func (m *StoryPart) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *StoryPart) GetAudio() string {
	if m != nil {
		return m.Audio
	}
	return ""
}

func (m *StoryPart) GetSeconds() uint32 {
	if m != nil {
		return m.Seconds
	}
	return 0
}

type PushPetStoryPart struct {
	StoryId              string       `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	StoryParts           []*StoryPart `protobuf:"bytes,2,rep,name=story_parts,json=storyParts,proto3" json:"story_parts,omitempty"`
	IsLastOne            bool         `protobuf:"varint,3,opt,name=is_last_one,json=isLastOne,proto3" json:"is_last_one,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PushPetStoryPart) Reset()         { *m = PushPetStoryPart{} }
func (m *PushPetStoryPart) String() string { return proto.CompactTextString(m) }
func (*PushPetStoryPart) ProtoMessage()    {}
func (*PushPetStoryPart) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{85}
}
func (m *PushPetStoryPart) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushPetStoryPart.Unmarshal(m, b)
}
func (m *PushPetStoryPart) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushPetStoryPart.Marshal(b, m, deterministic)
}
func (dst *PushPetStoryPart) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushPetStoryPart.Merge(dst, src)
}
func (m *PushPetStoryPart) XXX_Size() int {
	return xxx_messageInfo_PushPetStoryPart.Size(m)
}
func (m *PushPetStoryPart) XXX_DiscardUnknown() {
	xxx_messageInfo_PushPetStoryPart.DiscardUnknown(m)
}

var xxx_messageInfo_PushPetStoryPart proto.InternalMessageInfo

func (m *PushPetStoryPart) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

func (m *PushPetStoryPart) GetStoryParts() []*StoryPart {
	if m != nil {
		return m.StoryParts
	}
	return nil
}

func (m *PushPetStoryPart) GetIsLastOne() bool {
	if m != nil {
		return m.IsLastOne
	}
	return false
}

type StartPetStoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	StoryId              string   `protobuf:"bytes,5,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartPetStoryReq) Reset()         { *m = StartPetStoryReq{} }
func (m *StartPetStoryReq) String() string { return proto.CompactTextString(m) }
func (*StartPetStoryReq) ProtoMessage()    {}
func (*StartPetStoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{86}
}
func (m *StartPetStoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartPetStoryReq.Unmarshal(m, b)
}
func (m *StartPetStoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartPetStoryReq.Marshal(b, m, deterministic)
}
func (dst *StartPetStoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartPetStoryReq.Merge(dst, src)
}
func (m *StartPetStoryReq) XXX_Size() int {
	return xxx_messageInfo_StartPetStoryReq.Size(m)
}
func (m *StartPetStoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartPetStoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartPetStoryReq proto.InternalMessageInfo

func (m *StartPetStoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartPetStoryReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *StartPetStoryReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *StartPetStoryReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

func (m *StartPetStoryReq) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

type StartPetStoryRsp struct {
	StoryId              string   `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartPetStoryRsp) Reset()         { *m = StartPetStoryRsp{} }
func (m *StartPetStoryRsp) String() string { return proto.CompactTextString(m) }
func (*StartPetStoryRsp) ProtoMessage()    {}
func (*StartPetStoryRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{87}
}
func (m *StartPetStoryRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartPetStoryRsp.Unmarshal(m, b)
}
func (m *StartPetStoryRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartPetStoryRsp.Marshal(b, m, deterministic)
}
func (dst *StartPetStoryRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartPetStoryRsp.Merge(dst, src)
}
func (m *StartPetStoryRsp) XXX_Size() int {
	return xxx_messageInfo_StartPetStoryRsp.Size(m)
}
func (m *StartPetStoryRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartPetStoryRsp.DiscardUnknown(m)
}

var xxx_messageInfo_StartPetStoryRsp proto.InternalMessageInfo

func (m *StartPetStoryRsp) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

type StopPetStoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	StoryId              string   `protobuf:"bytes,5,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopPetStoryReq) Reset()         { *m = StopPetStoryReq{} }
func (m *StopPetStoryReq) String() string { return proto.CompactTextString(m) }
func (*StopPetStoryReq) ProtoMessage()    {}
func (*StopPetStoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{88}
}
func (m *StopPetStoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopPetStoryReq.Unmarshal(m, b)
}
func (m *StopPetStoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopPetStoryReq.Marshal(b, m, deterministic)
}
func (dst *StopPetStoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopPetStoryReq.Merge(dst, src)
}
func (m *StopPetStoryReq) XXX_Size() int {
	return xxx_messageInfo_StopPetStoryReq.Size(m)
}
func (m *StopPetStoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StopPetStoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_StopPetStoryReq proto.InternalMessageInfo

func (m *StopPetStoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StopPetStoryReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *StopPetStoryReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *StopPetStoryReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

func (m *StopPetStoryReq) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

type StopPetStoryRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopPetStoryRsp) Reset()         { *m = StopPetStoryRsp{} }
func (m *StopPetStoryRsp) String() string { return proto.CompactTextString(m) }
func (*StopPetStoryRsp) ProtoMessage()    {}
func (*StopPetStoryRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{89}
}
func (m *StopPetStoryRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopPetStoryRsp.Unmarshal(m, b)
}
func (m *StopPetStoryRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopPetStoryRsp.Marshal(b, m, deterministic)
}
func (dst *StopPetStoryRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopPetStoryRsp.Merge(dst, src)
}
func (m *StopPetStoryRsp) XXX_Size() int {
	return xxx_messageInfo_StopPetStoryRsp.Size(m)
}
func (m *StopPetStoryRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_StopPetStoryRsp.DiscardUnknown(m)
}

var xxx_messageInfo_StopPetStoryRsp proto.InternalMessageInfo

type EnterPetSleepTalkReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EnterPetSleepTalkReq) Reset()         { *m = EnterPetSleepTalkReq{} }
func (m *EnterPetSleepTalkReq) String() string { return proto.CompactTextString(m) }
func (*EnterPetSleepTalkReq) ProtoMessage()    {}
func (*EnterPetSleepTalkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{90}
}
func (m *EnterPetSleepTalkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnterPetSleepTalkReq.Unmarshal(m, b)
}
func (m *EnterPetSleepTalkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnterPetSleepTalkReq.Marshal(b, m, deterministic)
}
func (dst *EnterPetSleepTalkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnterPetSleepTalkReq.Merge(dst, src)
}
func (m *EnterPetSleepTalkReq) XXX_Size() int {
	return xxx_messageInfo_EnterPetSleepTalkReq.Size(m)
}
func (m *EnterPetSleepTalkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EnterPetSleepTalkReq.DiscardUnknown(m)
}

var xxx_messageInfo_EnterPetSleepTalkReq proto.InternalMessageInfo

func (m *EnterPetSleepTalkReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *EnterPetSleepTalkReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *EnterPetSleepTalkReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *EnterPetSleepTalkReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

type EnterPetSleepTalkRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EnterPetSleepTalkRsp) Reset()         { *m = EnterPetSleepTalkRsp{} }
func (m *EnterPetSleepTalkRsp) String() string { return proto.CompactTextString(m) }
func (*EnterPetSleepTalkRsp) ProtoMessage()    {}
func (*EnterPetSleepTalkRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{91}
}
func (m *EnterPetSleepTalkRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnterPetSleepTalkRsp.Unmarshal(m, b)
}
func (m *EnterPetSleepTalkRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnterPetSleepTalkRsp.Marshal(b, m, deterministic)
}
func (dst *EnterPetSleepTalkRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnterPetSleepTalkRsp.Merge(dst, src)
}
func (m *EnterPetSleepTalkRsp) XXX_Size() int {
	return xxx_messageInfo_EnterPetSleepTalkRsp.Size(m)
}
func (m *EnterPetSleepTalkRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_EnterPetSleepTalkRsp.DiscardUnknown(m)
}

var xxx_messageInfo_EnterPetSleepTalkRsp proto.InternalMessageInfo

type GetVibesConfigReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	VibesType            uint32   `protobuf:"varint,5,opt,name=vibes_type,json=vibesType,proto3" json:"vibes_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVibesConfigReq) Reset()         { *m = GetVibesConfigReq{} }
func (m *GetVibesConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetVibesConfigReq) ProtoMessage()    {}
func (*GetVibesConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{92}
}
func (m *GetVibesConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVibesConfigReq.Unmarshal(m, b)
}
func (m *GetVibesConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVibesConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetVibesConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVibesConfigReq.Merge(dst, src)
}
func (m *GetVibesConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetVibesConfigReq.Size(m)
}
func (m *GetVibesConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVibesConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVibesConfigReq proto.InternalMessageInfo

func (m *GetVibesConfigReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetVibesConfigReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GetVibesConfigReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetVibesConfigReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

func (m *GetVibesConfigReq) GetVibesType() uint32 {
	if m != nil {
		return m.VibesType
	}
	return 0
}

type VibesConfig struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Description          string   `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	SmallUrl             string   `protobuf:"bytes,3,opt,name=small_url,json=smallUrl,proto3" json:"small_url,omitempty"`
	ImageUrl             string   `protobuf:"bytes,4,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	ResourceType         string   `protobuf:"bytes,5,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	ResourceUrl          string   `protobuf:"bytes,6,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	AudioUrl             string   `protobuf:"bytes,7,opt,name=audio_url,json=audioUrl,proto3" json:"audio_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VibesConfig) Reset()         { *m = VibesConfig{} }
func (m *VibesConfig) String() string { return proto.CompactTextString(m) }
func (*VibesConfig) ProtoMessage()    {}
func (*VibesConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{93}
}
func (m *VibesConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VibesConfig.Unmarshal(m, b)
}
func (m *VibesConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VibesConfig.Marshal(b, m, deterministic)
}
func (dst *VibesConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VibesConfig.Merge(dst, src)
}
func (m *VibesConfig) XXX_Size() int {
	return xxx_messageInfo_VibesConfig.Size(m)
}
func (m *VibesConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_VibesConfig.DiscardUnknown(m)
}

var xxx_messageInfo_VibesConfig proto.InternalMessageInfo

func (m *VibesConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *VibesConfig) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *VibesConfig) GetSmallUrl() string {
	if m != nil {
		return m.SmallUrl
	}
	return ""
}

func (m *VibesConfig) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *VibesConfig) GetResourceType() string {
	if m != nil {
		return m.ResourceType
	}
	return ""
}

func (m *VibesConfig) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *VibesConfig) GetAudioUrl() string {
	if m != nil {
		return m.AudioUrl
	}
	return ""
}

type GetVibesConfigRsp struct {
	VibesList            []*VibesConfig `protobuf:"bytes,1,rep,name=vibes_list,json=vibesList,proto3" json:"vibes_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetVibesConfigRsp) Reset()         { *m = GetVibesConfigRsp{} }
func (m *GetVibesConfigRsp) String() string { return proto.CompactTextString(m) }
func (*GetVibesConfigRsp) ProtoMessage()    {}
func (*GetVibesConfigRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{94}
}
func (m *GetVibesConfigRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVibesConfigRsp.Unmarshal(m, b)
}
func (m *GetVibesConfigRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVibesConfigRsp.Marshal(b, m, deterministic)
}
func (dst *GetVibesConfigRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVibesConfigRsp.Merge(dst, src)
}
func (m *GetVibesConfigRsp) XXX_Size() int {
	return xxx_messageInfo_GetVibesConfigRsp.Size(m)
}
func (m *GetVibesConfigRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVibesConfigRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVibesConfigRsp proto.InternalMessageInfo

func (m *GetVibesConfigRsp) GetVibesList() []*VibesConfig {
	if m != nil {
		return m.VibesList
	}
	return nil
}

type AddAccompanyTimeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	Seconds              uint32   `protobuf:"varint,5,opt,name=seconds,proto3" json:"seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAccompanyTimeReq) Reset()         { *m = AddAccompanyTimeReq{} }
func (m *AddAccompanyTimeReq) String() string { return proto.CompactTextString(m) }
func (*AddAccompanyTimeReq) ProtoMessage()    {}
func (*AddAccompanyTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{95}
}
func (m *AddAccompanyTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAccompanyTimeReq.Unmarshal(m, b)
}
func (m *AddAccompanyTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAccompanyTimeReq.Marshal(b, m, deterministic)
}
func (dst *AddAccompanyTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAccompanyTimeReq.Merge(dst, src)
}
func (m *AddAccompanyTimeReq) XXX_Size() int {
	return xxx_messageInfo_AddAccompanyTimeReq.Size(m)
}
func (m *AddAccompanyTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAccompanyTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddAccompanyTimeReq proto.InternalMessageInfo

func (m *AddAccompanyTimeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddAccompanyTimeReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *AddAccompanyTimeReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *AddAccompanyTimeReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

func (m *AddAccompanyTimeReq) GetSeconds() uint32 {
	if m != nil {
		return m.Seconds
	}
	return 0
}

type AddAccompanyTimeRsp struct {
	TotalSeconds         uint32   `protobuf:"varint,1,opt,name=total_seconds,json=totalSeconds,proto3" json:"total_seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAccompanyTimeRsp) Reset()         { *m = AddAccompanyTimeRsp{} }
func (m *AddAccompanyTimeRsp) String() string { return proto.CompactTextString(m) }
func (*AddAccompanyTimeRsp) ProtoMessage()    {}
func (*AddAccompanyTimeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{96}
}
func (m *AddAccompanyTimeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAccompanyTimeRsp.Unmarshal(m, b)
}
func (m *AddAccompanyTimeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAccompanyTimeRsp.Marshal(b, m, deterministic)
}
func (dst *AddAccompanyTimeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAccompanyTimeRsp.Merge(dst, src)
}
func (m *AddAccompanyTimeRsp) XXX_Size() int {
	return xxx_messageInfo_AddAccompanyTimeRsp.Size(m)
}
func (m *AddAccompanyTimeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAccompanyTimeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AddAccompanyTimeRsp proto.InternalMessageInfo

func (m *AddAccompanyTimeRsp) GetTotalSeconds() uint32 {
	if m != nil {
		return m.TotalSeconds
	}
	return 0
}

type GetAccompanyTimeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	ReqDevId             string   `protobuf:"bytes,4,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	Seconds              uint32   `protobuf:"varint,5,opt,name=seconds,proto3" json:"seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAccompanyTimeReq) Reset()         { *m = GetAccompanyTimeReq{} }
func (m *GetAccompanyTimeReq) String() string { return proto.CompactTextString(m) }
func (*GetAccompanyTimeReq) ProtoMessage()    {}
func (*GetAccompanyTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{97}
}
func (m *GetAccompanyTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAccompanyTimeReq.Unmarshal(m, b)
}
func (m *GetAccompanyTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAccompanyTimeReq.Marshal(b, m, deterministic)
}
func (dst *GetAccompanyTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAccompanyTimeReq.Merge(dst, src)
}
func (m *GetAccompanyTimeReq) XXX_Size() int {
	return xxx_messageInfo_GetAccompanyTimeReq.Size(m)
}
func (m *GetAccompanyTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAccompanyTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAccompanyTimeReq proto.InternalMessageInfo

func (m *GetAccompanyTimeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAccompanyTimeReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GetAccompanyTimeReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetAccompanyTimeReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

func (m *GetAccompanyTimeReq) GetSeconds() uint32 {
	if m != nil {
		return m.Seconds
	}
	return 0
}

type GetAccompanyTimeRsp struct {
	TotalSeconds         uint32   `protobuf:"varint,1,opt,name=total_seconds,json=totalSeconds,proto3" json:"total_seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAccompanyTimeRsp) Reset()         { *m = GetAccompanyTimeRsp{} }
func (m *GetAccompanyTimeRsp) String() string { return proto.CompactTextString(m) }
func (*GetAccompanyTimeRsp) ProtoMessage()    {}
func (*GetAccompanyTimeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{98}
}
func (m *GetAccompanyTimeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAccompanyTimeRsp.Unmarshal(m, b)
}
func (m *GetAccompanyTimeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAccompanyTimeRsp.Marshal(b, m, deterministic)
}
func (dst *GetAccompanyTimeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAccompanyTimeRsp.Merge(dst, src)
}
func (m *GetAccompanyTimeRsp) XXX_Size() int {
	return xxx_messageInfo_GetAccompanyTimeRsp.Size(m)
}
func (m *GetAccompanyTimeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAccompanyTimeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAccompanyTimeRsp proto.InternalMessageInfo

func (m *GetAccompanyTimeRsp) GetTotalSeconds() uint32 {
	if m != nil {
		return m.TotalSeconds
	}
	return 0
}

type TargetInfo struct {
	Target               *Target         `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	Complete             *TargetComplete `protobuf:"bytes,2,opt,name=complete,proto3" json:"complete,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *TargetInfo) Reset()         { *m = TargetInfo{} }
func (m *TargetInfo) String() string { return proto.CompactTextString(m) }
func (*TargetInfo) ProtoMessage()    {}
func (*TargetInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{99}
}
func (m *TargetInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TargetInfo.Unmarshal(m, b)
}
func (m *TargetInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TargetInfo.Marshal(b, m, deterministic)
}
func (dst *TargetInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TargetInfo.Merge(dst, src)
}
func (m *TargetInfo) XXX_Size() int {
	return xxx_messageInfo_TargetInfo.Size(m)
}
func (m *TargetInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TargetInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TargetInfo proto.InternalMessageInfo

func (m *TargetInfo) GetTarget() *Target {
	if m != nil {
		return m.Target
	}
	return nil
}

func (m *TargetInfo) GetComplete() *TargetComplete {
	if m != nil {
		return m.Complete
	}
	return nil
}

type Target struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                 int32    `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	SubId                uint32   `protobuf:"varint,3,opt,name=sub_id,json=subId,proto3" json:"sub_id,omitempty"`
	Order                uint32   `protobuf:"varint,4,opt,name=order,proto3" json:"order,omitempty"`
	Title                string   `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	Subtitle             string   `protobuf:"bytes,6,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	RewardType           int32    `protobuf:"varint,7,opt,name=reward_type,json=rewardType,proto3" json:"reward_type,omitempty"`
	RewardNum            uint32   `protobuf:"varint,8,opt,name=reward_num,json=rewardNum,proto3" json:"reward_num,omitempty"`
	Description          string   `protobuf:"bytes,9,opt,name=description,proto3" json:"description,omitempty"`
	Todo                 int32    `protobuf:"varint,10,opt,name=todo,proto3" json:"todo,omitempty"`
	ComputeMethod        int32    `protobuf:"varint,11,opt,name=compute_method,json=computeMethod,proto3" json:"compute_method,omitempty"`
	RoleId               uint32   `protobuf:"varint,12,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	CompleteDoValue      uint32   `protobuf:"varint,13,opt,name=complete_do_value,json=completeDoValue,proto3" json:"complete_do_value,omitempty"`
	Param                string   `protobuf:"bytes,14,opt,name=param,proto3" json:"param,omitempty"`
	StartTime            string   `protobuf:"bytes,15,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              string   `protobuf:"bytes,16,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Icon                 string   `protobuf:"bytes,17,opt,name=icon,proto3" json:"icon,omitempty"`
	Trigger              int32    `protobuf:"varint,18,opt,name=trigger,proto3" json:"trigger,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Target) Reset()         { *m = Target{} }
func (m *Target) String() string { return proto.CompactTextString(m) }
func (*Target) ProtoMessage()    {}
func (*Target) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{100}
}
func (m *Target) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Target.Unmarshal(m, b)
}
func (m *Target) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Target.Marshal(b, m, deterministic)
}
func (dst *Target) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Target.Merge(dst, src)
}
func (m *Target) XXX_Size() int {
	return xxx_messageInfo_Target.Size(m)
}
func (m *Target) XXX_DiscardUnknown() {
	xxx_messageInfo_Target.DiscardUnknown(m)
}

var xxx_messageInfo_Target proto.InternalMessageInfo

func (m *Target) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *Target) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *Target) GetSubId() uint32 {
	if m != nil {
		return m.SubId
	}
	return 0
}

func (m *Target) GetOrder() uint32 {
	if m != nil {
		return m.Order
	}
	return 0
}

func (m *Target) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Target) GetSubtitle() string {
	if m != nil {
		return m.Subtitle
	}
	return ""
}

func (m *Target) GetRewardType() int32 {
	if m != nil {
		return m.RewardType
	}
	return 0
}

func (m *Target) GetRewardNum() uint32 {
	if m != nil {
		return m.RewardNum
	}
	return 0
}

func (m *Target) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *Target) GetTodo() int32 {
	if m != nil {
		return m.Todo
	}
	return 0
}

func (m *Target) GetComputeMethod() int32 {
	if m != nil {
		return m.ComputeMethod
	}
	return 0
}

func (m *Target) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *Target) GetCompleteDoValue() uint32 {
	if m != nil {
		return m.CompleteDoValue
	}
	return 0
}

func (m *Target) GetParam() string {
	if m != nil {
		return m.Param
	}
	return ""
}

func (m *Target) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *Target) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *Target) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *Target) GetTrigger() int32 {
	if m != nil {
		return m.Trigger
	}
	return 0
}

type TargetCompleteParam struct {
	MatchAny             []string `protobuf:"bytes,1,rep,name=match_any,json=matchAny,proto3" json:"match_any,omitempty"`
	ContainAny           []string `protobuf:"bytes,2,rep,name=contain_any,json=containAny,proto3" json:"contain_any,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TargetCompleteParam) Reset()         { *m = TargetCompleteParam{} }
func (m *TargetCompleteParam) String() string { return proto.CompactTextString(m) }
func (*TargetCompleteParam) ProtoMessage()    {}
func (*TargetCompleteParam) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{101}
}
func (m *TargetCompleteParam) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TargetCompleteParam.Unmarshal(m, b)
}
func (m *TargetCompleteParam) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TargetCompleteParam.Marshal(b, m, deterministic)
}
func (dst *TargetCompleteParam) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TargetCompleteParam.Merge(dst, src)
}
func (m *TargetCompleteParam) XXX_Size() int {
	return xxx_messageInfo_TargetCompleteParam.Size(m)
}
func (m *TargetCompleteParam) XXX_DiscardUnknown() {
	xxx_messageInfo_TargetCompleteParam.DiscardUnknown(m)
}

var xxx_messageInfo_TargetCompleteParam proto.InternalMessageInfo

func (m *TargetCompleteParam) GetMatchAny() []string {
	if m != nil {
		return m.MatchAny
	}
	return nil
}

func (m *TargetCompleteParam) GetContainAny() []string {
	if m != nil {
		return m.ContainAny
	}
	return nil
}

type GetTargetListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	TargetType           int32    `protobuf:"varint,4,opt,name=target_type,json=targetType,proto3" json:"target_type,omitempty"`
	ReqDevId             string   `protobuf:"bytes,5,opt,name=req_dev_id,json=reqDevId,proto3" json:"req_dev_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTargetListReq) Reset()         { *m = GetTargetListReq{} }
func (m *GetTargetListReq) String() string { return proto.CompactTextString(m) }
func (*GetTargetListReq) ProtoMessage()    {}
func (*GetTargetListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{102}
}
func (m *GetTargetListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTargetListReq.Unmarshal(m, b)
}
func (m *GetTargetListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTargetListReq.Marshal(b, m, deterministic)
}
func (dst *GetTargetListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTargetListReq.Merge(dst, src)
}
func (m *GetTargetListReq) XXX_Size() int {
	return xxx_messageInfo_GetTargetListReq.Size(m)
}
func (m *GetTargetListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTargetListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTargetListReq proto.InternalMessageInfo

func (m *GetTargetListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTargetListReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *GetTargetListReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *GetTargetListReq) GetTargetType() int32 {
	if m != nil {
		return m.TargetType
	}
	return 0
}

func (m *GetTargetListReq) GetReqDevId() string {
	if m != nil {
		return m.ReqDevId
	}
	return ""
}

type TargetComplete struct {
	TargetId             string   `protobuf:"bytes,1,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	DoValue              uint32   `protobuf:"varint,2,opt,name=do_value,json=doValue,proto3" json:"do_value,omitempty"`
	IsComplete           bool     `protobuf:"varint,3,opt,name=is_complete,json=isComplete,proto3" json:"is_complete,omitempty"`
	CompleteTime         int64    `protobuf:"varint,4,opt,name=complete_time,json=completeTime,proto3" json:"complete_time,omitempty"`
	IsGetReward          bool     `protobuf:"varint,5,opt,name=is_get_reward,json=isGetReward,proto3" json:"is_get_reward,omitempty"`
	GetRewardTime        int64    `protobuf:"varint,6,opt,name=get_reward_time,json=getRewardTime,proto3" json:"get_reward_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TargetComplete) Reset()         { *m = TargetComplete{} }
func (m *TargetComplete) String() string { return proto.CompactTextString(m) }
func (*TargetComplete) ProtoMessage()    {}
func (*TargetComplete) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{103}
}
func (m *TargetComplete) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TargetComplete.Unmarshal(m, b)
}
func (m *TargetComplete) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TargetComplete.Marshal(b, m, deterministic)
}
func (dst *TargetComplete) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TargetComplete.Merge(dst, src)
}
func (m *TargetComplete) XXX_Size() int {
	return xxx_messageInfo_TargetComplete.Size(m)
}
func (m *TargetComplete) XXX_DiscardUnknown() {
	xxx_messageInfo_TargetComplete.DiscardUnknown(m)
}

var xxx_messageInfo_TargetComplete proto.InternalMessageInfo

func (m *TargetComplete) GetTargetId() string {
	if m != nil {
		return m.TargetId
	}
	return ""
}

func (m *TargetComplete) GetDoValue() uint32 {
	if m != nil {
		return m.DoValue
	}
	return 0
}

func (m *TargetComplete) GetIsComplete() bool {
	if m != nil {
		return m.IsComplete
	}
	return false
}

func (m *TargetComplete) GetCompleteTime() int64 {
	if m != nil {
		return m.CompleteTime
	}
	return 0
}

func (m *TargetComplete) GetIsGetReward() bool {
	if m != nil {
		return m.IsGetReward
	}
	return false
}

func (m *TargetComplete) GetGetRewardTime() int64 {
	if m != nil {
		return m.GetRewardTime
	}
	return 0
}

type GetTargetListRsp struct {
	TargetInfos          []*TargetInfo `protobuf:"bytes,1,rep,name=target_infos,json=targetInfos,proto3" json:"target_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetTargetListRsp) Reset()         { *m = GetTargetListRsp{} }
func (m *GetTargetListRsp) String() string { return proto.CompactTextString(m) }
func (*GetTargetListRsp) ProtoMessage()    {}
func (*GetTargetListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{104}
}
func (m *GetTargetListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTargetListRsp.Unmarshal(m, b)
}
func (m *GetTargetListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTargetListRsp.Marshal(b, m, deterministic)
}
func (dst *GetTargetListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTargetListRsp.Merge(dst, src)
}
func (m *GetTargetListRsp) XXX_Size() int {
	return xxx_messageInfo_GetTargetListRsp.Size(m)
}
func (m *GetTargetListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTargetListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTargetListRsp proto.InternalMessageInfo

func (m *GetTargetListRsp) GetTargetInfos() []*TargetInfo {
	if m != nil {
		return m.TargetInfos
	}
	return nil
}

type GetTargetRewardReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetId             string   `protobuf:"bytes,2,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTargetRewardReq) Reset()         { *m = GetTargetRewardReq{} }
func (m *GetTargetRewardReq) String() string { return proto.CompactTextString(m) }
func (*GetTargetRewardReq) ProtoMessage()    {}
func (*GetTargetRewardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{105}
}
func (m *GetTargetRewardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTargetRewardReq.Unmarshal(m, b)
}
func (m *GetTargetRewardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTargetRewardReq.Marshal(b, m, deterministic)
}
func (dst *GetTargetRewardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTargetRewardReq.Merge(dst, src)
}
func (m *GetTargetRewardReq) XXX_Size() int {
	return xxx_messageInfo_GetTargetRewardReq.Size(m)
}
func (m *GetTargetRewardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTargetRewardReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTargetRewardReq proto.InternalMessageInfo

func (m *GetTargetRewardReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTargetRewardReq) GetTargetId() string {
	if m != nil {
		return m.TargetId
	}
	return ""
}

type GetTargetRewardRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTargetRewardRsp) Reset()         { *m = GetTargetRewardRsp{} }
func (m *GetTargetRewardRsp) String() string { return proto.CompactTextString(m) }
func (*GetTargetRewardRsp) ProtoMessage()    {}
func (*GetTargetRewardRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{106}
}
func (m *GetTargetRewardRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTargetRewardRsp.Unmarshal(m, b)
}
func (m *GetTargetRewardRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTargetRewardRsp.Marshal(b, m, deterministic)
}
func (dst *GetTargetRewardRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTargetRewardRsp.Merge(dst, src)
}
func (m *GetTargetRewardRsp) XXX_Size() int {
	return xxx_messageInfo_GetTargetRewardRsp.Size(m)
}
func (m *GetTargetRewardRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTargetRewardRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTargetRewardRsp proto.InternalMessageInfo

type GetChatNumReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChatNumReq) Reset()         { *m = GetChatNumReq{} }
func (m *GetChatNumReq) String() string { return proto.CompactTextString(m) }
func (*GetChatNumReq) ProtoMessage()    {}
func (*GetChatNumReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{107}
}
func (m *GetChatNumReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChatNumReq.Unmarshal(m, b)
}
func (m *GetChatNumReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChatNumReq.Marshal(b, m, deterministic)
}
func (dst *GetChatNumReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChatNumReq.Merge(dst, src)
}
func (m *GetChatNumReq) XXX_Size() int {
	return xxx_messageInfo_GetChatNumReq.Size(m)
}
func (m *GetChatNumReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChatNumReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChatNumReq proto.InternalMessageInfo

func (m *GetChatNumReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetChatNumRsp struct {
	ConfUseNum           uint32   `protobuf:"varint,1,opt,name=conf_use_num,json=confUseNum,proto3" json:"conf_use_num,omitempty"`
	ConfTotalNum         uint32   `protobuf:"varint,2,opt,name=conf_total_num,json=confTotalNum,proto3" json:"conf_total_num,omitempty"`
	ExtraRemainingNum    uint32   `protobuf:"varint,3,opt,name=extra_remaining_num,json=extraRemainingNum,proto3" json:"extra_remaining_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChatNumRsp) Reset()         { *m = GetChatNumRsp{} }
func (m *GetChatNumRsp) String() string { return proto.CompactTextString(m) }
func (*GetChatNumRsp) ProtoMessage()    {}
func (*GetChatNumRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{108}
}
func (m *GetChatNumRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChatNumRsp.Unmarshal(m, b)
}
func (m *GetChatNumRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChatNumRsp.Marshal(b, m, deterministic)
}
func (dst *GetChatNumRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChatNumRsp.Merge(dst, src)
}
func (m *GetChatNumRsp) XXX_Size() int {
	return xxx_messageInfo_GetChatNumRsp.Size(m)
}
func (m *GetChatNumRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChatNumRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChatNumRsp proto.InternalMessageInfo

func (m *GetChatNumRsp) GetConfUseNum() uint32 {
	if m != nil {
		return m.ConfUseNum
	}
	return 0
}

func (m *GetChatNumRsp) GetConfTotalNum() uint32 {
	if m != nil {
		return m.ConfTotalNum
	}
	return 0
}

func (m *GetChatNumRsp) GetExtraRemainingNum() uint32 {
	if m != nil {
		return m.ExtraRemainingNum
	}
	return 0
}

type CheckAIRoomVisibilityReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckAIRoomVisibilityReq) Reset()         { *m = CheckAIRoomVisibilityReq{} }
func (m *CheckAIRoomVisibilityReq) String() string { return proto.CompactTextString(m) }
func (*CheckAIRoomVisibilityReq) ProtoMessage()    {}
func (*CheckAIRoomVisibilityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{109}
}
func (m *CheckAIRoomVisibilityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckAIRoomVisibilityReq.Unmarshal(m, b)
}
func (m *CheckAIRoomVisibilityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckAIRoomVisibilityReq.Marshal(b, m, deterministic)
}
func (dst *CheckAIRoomVisibilityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckAIRoomVisibilityReq.Merge(dst, src)
}
func (m *CheckAIRoomVisibilityReq) XXX_Size() int {
	return xxx_messageInfo_CheckAIRoomVisibilityReq.Size(m)
}
func (m *CheckAIRoomVisibilityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckAIRoomVisibilityReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckAIRoomVisibilityReq proto.InternalMessageInfo

func (m *CheckAIRoomVisibilityReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckAIRoomVisibilityReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type SetUserChatSceneReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	ChatScene            string   `protobuf:"bytes,3,opt,name=chat_scene,json=chatScene,proto3" json:"chat_scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserChatSceneReq) Reset()         { *m = SetUserChatSceneReq{} }
func (m *SetUserChatSceneReq) String() string { return proto.CompactTextString(m) }
func (*SetUserChatSceneReq) ProtoMessage()    {}
func (*SetUserChatSceneReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{110}
}
func (m *SetUserChatSceneReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserChatSceneReq.Unmarshal(m, b)
}
func (m *SetUserChatSceneReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserChatSceneReq.Marshal(b, m, deterministic)
}
func (dst *SetUserChatSceneReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserChatSceneReq.Merge(dst, src)
}
func (m *SetUserChatSceneReq) XXX_Size() int {
	return xxx_messageInfo_SetUserChatSceneReq.Size(m)
}
func (m *SetUserChatSceneReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserChatSceneReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserChatSceneReq proto.InternalMessageInfo

func (m *SetUserChatSceneReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserChatSceneReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *SetUserChatSceneReq) GetChatScene() string {
	if m != nil {
		return m.ChatScene
	}
	return ""
}

type SetUserChatSceneResp struct {
	SceneEffect          bool     `protobuf:"varint,1,opt,name=scene_effect,json=sceneEffect,proto3" json:"scene_effect,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserChatSceneResp) Reset()         { *m = SetUserChatSceneResp{} }
func (m *SetUserChatSceneResp) String() string { return proto.CompactTextString(m) }
func (*SetUserChatSceneResp) ProtoMessage()    {}
func (*SetUserChatSceneResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{111}
}
func (m *SetUserChatSceneResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserChatSceneResp.Unmarshal(m, b)
}
func (m *SetUserChatSceneResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserChatSceneResp.Marshal(b, m, deterministic)
}
func (dst *SetUserChatSceneResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserChatSceneResp.Merge(dst, src)
}
func (m *SetUserChatSceneResp) XXX_Size() int {
	return xxx_messageInfo_SetUserChatSceneResp.Size(m)
}
func (m *SetUserChatSceneResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserChatSceneResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserChatSceneResp proto.InternalMessageInfo

func (m *SetUserChatSceneResp) GetSceneEffect() bool {
	if m != nil {
		return m.SceneEffect
	}
	return false
}

type CheckChatSceneVisibilityReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckChatSceneVisibilityReq) Reset()         { *m = CheckChatSceneVisibilityReq{} }
func (m *CheckChatSceneVisibilityReq) String() string { return proto.CompactTextString(m) }
func (*CheckChatSceneVisibilityReq) ProtoMessage()    {}
func (*CheckChatSceneVisibilityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{112}
}
func (m *CheckChatSceneVisibilityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckChatSceneVisibilityReq.Unmarshal(m, b)
}
func (m *CheckChatSceneVisibilityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckChatSceneVisibilityReq.Marshal(b, m, deterministic)
}
func (dst *CheckChatSceneVisibilityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckChatSceneVisibilityReq.Merge(dst, src)
}
func (m *CheckChatSceneVisibilityReq) XXX_Size() int {
	return xxx_messageInfo_CheckChatSceneVisibilityReq.Size(m)
}
func (m *CheckChatSceneVisibilityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckChatSceneVisibilityReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckChatSceneVisibilityReq proto.InternalMessageInfo

func (m *CheckChatSceneVisibilityReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckChatSceneVisibilityReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type CheckAIRoomVisibilityResp struct {
	CanDisplay           bool     `protobuf:"varint,1,opt,name=can_display,json=canDisplay,proto3" json:"can_display,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckAIRoomVisibilityResp) Reset()         { *m = CheckAIRoomVisibilityResp{} }
func (m *CheckAIRoomVisibilityResp) String() string { return proto.CompactTextString(m) }
func (*CheckAIRoomVisibilityResp) ProtoMessage()    {}
func (*CheckAIRoomVisibilityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{113}
}
func (m *CheckAIRoomVisibilityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckAIRoomVisibilityResp.Unmarshal(m, b)
}
func (m *CheckAIRoomVisibilityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckAIRoomVisibilityResp.Marshal(b, m, deterministic)
}
func (dst *CheckAIRoomVisibilityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckAIRoomVisibilityResp.Merge(dst, src)
}
func (m *CheckAIRoomVisibilityResp) XXX_Size() int {
	return xxx_messageInfo_CheckAIRoomVisibilityResp.Size(m)
}
func (m *CheckAIRoomVisibilityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckAIRoomVisibilityResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckAIRoomVisibilityResp proto.InternalMessageInfo

func (m *CheckAIRoomVisibilityResp) GetCanDisplay() bool {
	if m != nil {
		return m.CanDisplay
	}
	return false
}

type CheckChatSceneVisibilityResp struct {
	CanDisplay           bool     `protobuf:"varint,1,opt,name=can_display,json=canDisplay,proto3" json:"can_display,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckChatSceneVisibilityResp) Reset()         { *m = CheckChatSceneVisibilityResp{} }
func (m *CheckChatSceneVisibilityResp) String() string { return proto.CompactTextString(m) }
func (*CheckChatSceneVisibilityResp) ProtoMessage()    {}
func (*CheckChatSceneVisibilityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{114}
}
func (m *CheckChatSceneVisibilityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckChatSceneVisibilityResp.Unmarshal(m, b)
}
func (m *CheckChatSceneVisibilityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckChatSceneVisibilityResp.Marshal(b, m, deterministic)
}
func (dst *CheckChatSceneVisibilityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckChatSceneVisibilityResp.Merge(dst, src)
}
func (m *CheckChatSceneVisibilityResp) XXX_Size() int {
	return xxx_messageInfo_CheckChatSceneVisibilityResp.Size(m)
}
func (m *CheckChatSceneVisibilityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckChatSceneVisibilityResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckChatSceneVisibilityResp proto.InternalMessageInfo

func (m *CheckChatSceneVisibilityResp) GetCanDisplay() bool {
	if m != nil {
		return m.CanDisplay
	}
	return false
}

type CheckIfTriggerChatReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIfTriggerChatReq) Reset()         { *m = CheckIfTriggerChatReq{} }
func (m *CheckIfTriggerChatReq) String() string { return proto.CompactTextString(m) }
func (*CheckIfTriggerChatReq) ProtoMessage()    {}
func (*CheckIfTriggerChatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{115}
}
func (m *CheckIfTriggerChatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIfTriggerChatReq.Unmarshal(m, b)
}
func (m *CheckIfTriggerChatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIfTriggerChatReq.Marshal(b, m, deterministic)
}
func (dst *CheckIfTriggerChatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIfTriggerChatReq.Merge(dst, src)
}
func (m *CheckIfTriggerChatReq) XXX_Size() int {
	return xxx_messageInfo_CheckIfTriggerChatReq.Size(m)
}
func (m *CheckIfTriggerChatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIfTriggerChatReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIfTriggerChatReq proto.InternalMessageInfo

func (m *CheckIfTriggerChatReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckIfTriggerChatResp struct {
	IsTrigger            bool     `protobuf:"varint,1,opt,name=is_trigger,json=isTrigger,proto3" json:"is_trigger,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIfTriggerChatResp) Reset()         { *m = CheckIfTriggerChatResp{} }
func (m *CheckIfTriggerChatResp) String() string { return proto.CompactTextString(m) }
func (*CheckIfTriggerChatResp) ProtoMessage()    {}
func (*CheckIfTriggerChatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{116}
}
func (m *CheckIfTriggerChatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIfTriggerChatResp.Unmarshal(m, b)
}
func (m *CheckIfTriggerChatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIfTriggerChatResp.Marshal(b, m, deterministic)
}
func (dst *CheckIfTriggerChatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIfTriggerChatResp.Merge(dst, src)
}
func (m *CheckIfTriggerChatResp) XXX_Size() int {
	return xxx_messageInfo_CheckIfTriggerChatResp.Size(m)
}
func (m *CheckIfTriggerChatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIfTriggerChatResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIfTriggerChatResp proto.InternalMessageInfo

func (m *CheckIfTriggerChatResp) GetIsTrigger() bool {
	if m != nil {
		return m.IsTrigger
	}
	return false
}

type GetNotOpenedRolesReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleList             []uint32 `protobuf:"varint,2,rep,packed,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNotOpenedRolesReq) Reset()         { *m = GetNotOpenedRolesReq{} }
func (m *GetNotOpenedRolesReq) String() string { return proto.CompactTextString(m) }
func (*GetNotOpenedRolesReq) ProtoMessage()    {}
func (*GetNotOpenedRolesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{117}
}
func (m *GetNotOpenedRolesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNotOpenedRolesReq.Unmarshal(m, b)
}
func (m *GetNotOpenedRolesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNotOpenedRolesReq.Marshal(b, m, deterministic)
}
func (dst *GetNotOpenedRolesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNotOpenedRolesReq.Merge(dst, src)
}
func (m *GetNotOpenedRolesReq) XXX_Size() int {
	return xxx_messageInfo_GetNotOpenedRolesReq.Size(m)
}
func (m *GetNotOpenedRolesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNotOpenedRolesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNotOpenedRolesReq proto.InternalMessageInfo

func (m *GetNotOpenedRolesReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetNotOpenedRolesReq) GetRoleList() []uint32 {
	if m != nil {
		return m.RoleList
	}
	return nil
}

type GetNotOpenedRolesResp struct {
	RoleList             []uint32 `protobuf:"varint,1,rep,packed,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNotOpenedRolesResp) Reset()         { *m = GetNotOpenedRolesResp{} }
func (m *GetNotOpenedRolesResp) String() string { return proto.CompactTextString(m) }
func (*GetNotOpenedRolesResp) ProtoMessage()    {}
func (*GetNotOpenedRolesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{118}
}
func (m *GetNotOpenedRolesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNotOpenedRolesResp.Unmarshal(m, b)
}
func (m *GetNotOpenedRolesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNotOpenedRolesResp.Marshal(b, m, deterministic)
}
func (dst *GetNotOpenedRolesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNotOpenedRolesResp.Merge(dst, src)
}
func (m *GetNotOpenedRolesResp) XXX_Size() int {
	return xxx_messageInfo_GetNotOpenedRolesResp.Size(m)
}
func (m *GetNotOpenedRolesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNotOpenedRolesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNotOpenedRolesResp proto.InternalMessageInfo

func (m *GetNotOpenedRolesResp) GetRoleList() []uint32 {
	if m != nil {
		return m.RoleList
	}
	return nil
}

type ChatRecord struct {
	Content              string   `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	ChatRole             string   `protobuf:"bytes,2,opt,name=chat_role,json=chatRole,proto3" json:"chat_role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChatRecord) Reset()         { *m = ChatRecord{} }
func (m *ChatRecord) String() string { return proto.CompactTextString(m) }
func (*ChatRecord) ProtoMessage()    {}
func (*ChatRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{119}
}
func (m *ChatRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatRecord.Unmarshal(m, b)
}
func (m *ChatRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatRecord.Marshal(b, m, deterministic)
}
func (dst *ChatRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatRecord.Merge(dst, src)
}
func (m *ChatRecord) XXX_Size() int {
	return xxx_messageInfo_ChatRecord.Size(m)
}
func (m *ChatRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatRecord.DiscardUnknown(m)
}

var xxx_messageInfo_ChatRecord proto.InternalMessageInfo

func (m *ChatRecord) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ChatRecord) GetChatRole() string {
	if m != nil {
		return m.ChatRole
	}
	return ""
}

type ChatHistoryAppendReq struct {
	Uid                  uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32        `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	ChatRecords          []*ChatRecord `protobuf:"bytes,3,rep,name=chat_records,json=chatRecords,proto3" json:"chat_records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChatHistoryAppendReq) Reset()         { *m = ChatHistoryAppendReq{} }
func (m *ChatHistoryAppendReq) String() string { return proto.CompactTextString(m) }
func (*ChatHistoryAppendReq) ProtoMessage()    {}
func (*ChatHistoryAppendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{120}
}
func (m *ChatHistoryAppendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatHistoryAppendReq.Unmarshal(m, b)
}
func (m *ChatHistoryAppendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatHistoryAppendReq.Marshal(b, m, deterministic)
}
func (dst *ChatHistoryAppendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatHistoryAppendReq.Merge(dst, src)
}
func (m *ChatHistoryAppendReq) XXX_Size() int {
	return xxx_messageInfo_ChatHistoryAppendReq.Size(m)
}
func (m *ChatHistoryAppendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatHistoryAppendReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChatHistoryAppendReq proto.InternalMessageInfo

func (m *ChatHistoryAppendReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChatHistoryAppendReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *ChatHistoryAppendReq) GetChatRecords() []*ChatRecord {
	if m != nil {
		return m.ChatRecords
	}
	return nil
}

type ChatHistoryAppendResp struct {
	Res                  bool     `protobuf:"varint,1,opt,name=res,proto3" json:"res,omitempty"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChatHistoryAppendResp) Reset()         { *m = ChatHistoryAppendResp{} }
func (m *ChatHistoryAppendResp) String() string { return proto.CompactTextString(m) }
func (*ChatHistoryAppendResp) ProtoMessage()    {}
func (*ChatHistoryAppendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{121}
}
func (m *ChatHistoryAppendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatHistoryAppendResp.Unmarshal(m, b)
}
func (m *ChatHistoryAppendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatHistoryAppendResp.Marshal(b, m, deterministic)
}
func (dst *ChatHistoryAppendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatHistoryAppendResp.Merge(dst, src)
}
func (m *ChatHistoryAppendResp) XXX_Size() int {
	return xxx_messageInfo_ChatHistoryAppendResp.Size(m)
}
func (m *ChatHistoryAppendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatHistoryAppendResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChatHistoryAppendResp proto.InternalMessageInfo

func (m *ChatHistoryAppendResp) GetRes() bool {
	if m != nil {
		return m.Res
	}
	return false
}

func (m *ChatHistoryAppendResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type ReadPartnerHeartReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Content              string   `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	MsgId                string   `protobuf:"bytes,5,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadPartnerHeartReq) Reset()         { *m = ReadPartnerHeartReq{} }
func (m *ReadPartnerHeartReq) String() string { return proto.CompactTextString(m) }
func (*ReadPartnerHeartReq) ProtoMessage()    {}
func (*ReadPartnerHeartReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{122}
}
func (m *ReadPartnerHeartReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadPartnerHeartReq.Unmarshal(m, b)
}
func (m *ReadPartnerHeartReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadPartnerHeartReq.Marshal(b, m, deterministic)
}
func (dst *ReadPartnerHeartReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadPartnerHeartReq.Merge(dst, src)
}
func (m *ReadPartnerHeartReq) XXX_Size() int {
	return xxx_messageInfo_ReadPartnerHeartReq.Size(m)
}
func (m *ReadPartnerHeartReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadPartnerHeartReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReadPartnerHeartReq proto.InternalMessageInfo

func (m *ReadPartnerHeartReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReadPartnerHeartReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *ReadPartnerHeartReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *ReadPartnerHeartReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ReadPartnerHeartReq) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

type ReadPartnerHeartResp struct {
	MsgId                string   `protobuf:"bytes,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadPartnerHeartResp) Reset()         { *m = ReadPartnerHeartResp{} }
func (m *ReadPartnerHeartResp) String() string { return proto.CompactTextString(m) }
func (*ReadPartnerHeartResp) ProtoMessage()    {}
func (*ReadPartnerHeartResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_character_585385b141559bb5, []int{123}
}
func (m *ReadPartnerHeartResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadPartnerHeartResp.Unmarshal(m, b)
}
func (m *ReadPartnerHeartResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadPartnerHeartResp.Marshal(b, m, deterministic)
}
func (dst *ReadPartnerHeartResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadPartnerHeartResp.Merge(dst, src)
}
func (m *ReadPartnerHeartResp) XXX_Size() int {
	return xxx_messageInfo_ReadPartnerHeartResp.Size(m)
}
func (m *ReadPartnerHeartResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadPartnerHeartResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReadPartnerHeartResp proto.InternalMessageInfo

func (m *ReadPartnerHeartResp) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *ReadPartnerHeartResp) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func init() {
	proto.RegisterType((*GetRoleGameInfoReq)(nil), "rcmd.game_character.GetRoleGameInfoReq")
	proto.RegisterType((*GetRoleGameInfoRsp)(nil), "rcmd.game_character.GetRoleGameInfoRsp")
	proto.RegisterType((*GetTopicGamesReq)(nil), "rcmd.game_character.GetTopicGamesReq")
	proto.RegisterType((*GetTopicGamesRsp)(nil), "rcmd.game_character.GetTopicGamesRsp")
	proto.RegisterType((*GetGameTopicsReq)(nil), "rcmd.game_character.GetGameTopicsReq")
	proto.RegisterType((*GetGameTopicsRsp)(nil), "rcmd.game_character.GetGameTopicsRsp")
	proto.RegisterType((*GameInfoNotify)(nil), "rcmd.game_character.GameInfoNotify")
	proto.RegisterType((*GameTopic)(nil), "rcmd.game_character.GameTopic")
	proto.RegisterType((*GameInfo)(nil), "rcmd.game_character.GameInfo")
	proto.RegisterType((*GetOutGamesReq)(nil), "rcmd.game_character.GetOutGamesReq")
	proto.RegisterType((*OutGames)(nil), "rcmd.game_character.OutGames")
	proto.RegisterType((*GetOutGamesRsp)(nil), "rcmd.game_character.GetOutGamesRsp")
	proto.RegisterType((*GenVoiceChatGreetingReq)(nil), "rcmd.game_character.GenVoiceChatGreetingReq")
	proto.RegisterType((*GenVoiceChatGreetingResp)(nil), "rcmd.game_character.GenVoiceChatGreetingResp")
	proto.RegisterType((*GetStoryBookHistoryReq)(nil), "rcmd.game_character.GetStoryBookHistoryReq")
	proto.RegisterType((*StoryMsg)(nil), "rcmd.game_character.StoryMsg")
	proto.RegisterType((*GetStoryBookHistoryResp)(nil), "rcmd.game_character.GetStoryBookHistoryResp")
	proto.RegisterType((*GetStoryBookReplyReq)(nil), "rcmd.game_character.GetStoryBookReplyReq")
	proto.RegisterType((*GetStoryBookReplyResp)(nil), "rcmd.game_character.GetStoryBookReplyResp")
	proto.RegisterType((*GetStoryBookReplyResp_ReplyOption)(nil), "rcmd.game_character.GetStoryBookReplyResp.ReplyOption")
	proto.RegisterType((*GetStoryBookProgressReq)(nil), "rcmd.game_character.GetStoryBookProgressReq")
	proto.RegisterType((*GetStoryBookProgressResp)(nil), "rcmd.game_character.GetStoryBookProgressResp")
	proto.RegisterType((*StartStoryBookReq)(nil), "rcmd.game_character.StartStoryBookReq")
	proto.RegisterType((*StartStoryBookResp)(nil), "rcmd.game_character.StartStoryBookResp")
	proto.RegisterType((*MarkStoryBookNotifySeqReq)(nil), "rcmd.game_character.MarkStoryBookNotifySeqReq")
	proto.RegisterType((*MarkStoryBookNotifySeqResp)(nil), "rcmd.game_character.MarkStoryBookNotifySeqResp")
	proto.RegisterType((*GetStoryBookNotifySeqReq)(nil), "rcmd.game_character.GetStoryBookNotifySeqReq")
	proto.RegisterType((*GetStoryBookNotifySeqResp)(nil), "rcmd.game_character.GetStoryBookNotifySeqResp")
	proto.RegisterType((*GetStoryBookReq)(nil), "rcmd.game_character.GetStoryBookReq")
	proto.RegisterType((*GetStoryBookResp)(nil), "rcmd.game_character.GetStoryBookResp")
	proto.RegisterType((*GetStoryBookListReq)(nil), "rcmd.game_character.GetStoryBookListReq")
	proto.RegisterType((*GetStoryBookListResp)(nil), "rcmd.game_character.GetStoryBookListResp")
	proto.RegisterType((*StoryBook)(nil), "rcmd.game_character.StoryBook")
	proto.RegisterType((*StoryChapter)(nil), "rcmd.game_character.StoryChapter")
	proto.RegisterType((*StoryUnlockCond)(nil), "rcmd.game_character.StoryUnlockCond")
	proto.RegisterType((*StartGameReq)(nil), "rcmd.game_character.StartGameReq")
	proto.RegisterType((*StartGameResp)(nil), "rcmd.game_character.StartGameResp")
	proto.RegisterType((*FormatGptAnswerReq)(nil), "rcmd.game_character.FormatGptAnswerReq")
	proto.RegisterType((*FormatGptAnswerResp)(nil), "rcmd.game_character.FormatGptAnswerResp")
	proto.RegisterType((*GetGPTReqInfoReq)(nil), "rcmd.game_character.GetGPTReqInfoReq")
	proto.RegisterType((*GetGPTReqInfoResp)(nil), "rcmd.game_character.GetGPTReqInfoResp")
	proto.RegisterType((*BatchGetCharactersReq)(nil), "rcmd.game_character.BatchGetCharactersReq")
	proto.RegisterType((*GameCharacter)(nil), "rcmd.game_character.GameCharacter")
	proto.RegisterType((*BatchGetCharactersResp)(nil), "rcmd.game_character.BatchGetCharactersResp")
	proto.RegisterType((*GetUserInfoReq)(nil), "rcmd.game_character.GetUserInfoReq")
	proto.RegisterType((*GetUserInfoResp)(nil), "rcmd.game_character.GetUserInfoResp")
	proto.RegisterType((*ReplyTextFormatReq)(nil), "rcmd.game_character.ReplyTextFormatReq")
	proto.RegisterType((*ReplyTextFormatResp)(nil), "rcmd.game_character.ReplyTextFormatResp")
	proto.RegisterType((*DeleteCharacterReq)(nil), "rcmd.game_character.DeleteCharacterReq")
	proto.RegisterType((*DeleteCharacterResp)(nil), "rcmd.game_character.DeleteCharacterResp")
	proto.RegisterType((*CustomVoice)(nil), "rcmd.game_character.CustomVoice")
	proto.RegisterType((*SetCharacterInfoReq)(nil), "rcmd.game_character.SetCharacterInfoReq")
	proto.RegisterType((*AIRole)(nil), "rcmd.game_character.AIRole")
	proto.RegisterType((*SetCharacterInfoResp)(nil), "rcmd.game_character.SetCharacterInfoResp")
	proto.RegisterType((*ReceiveMsgFromUserReq)(nil), "rcmd.game_character.ReceiveMsgFromUserReq")
	proto.RegisterType((*ReceiveMsgFromUserResp)(nil), "rcmd.game_character.ReceiveMsgFromUserResp")
	proto.RegisterType((*UserEnterChattingNotifyReq)(nil), "rcmd.game_character.UserEnterChattingNotifyReq")
	proto.RegisterType((*UserEnterChattingNotifyResp)(nil), "rcmd.game_character.UserEnterChattingNotifyResp")
	proto.RegisterType((*TestReq)(nil), "rcmd.game_character.TestReq")
	proto.RegisterType((*TestResp)(nil), "rcmd.game_character.TestResp")
	proto.RegisterType((*GenRecommendReplyReq)(nil), "rcmd.game_character.GenRecommendReplyReq")
	proto.RegisterType((*GenRecommendReplyResp)(nil), "rcmd.game_character.GenRecommendReplyResp")
	proto.RegisterType((*ShowPartnerSilentReq)(nil), "rcmd.game_character.ShowPartnerSilentReq")
	proto.RegisterType((*ShowPartnerSilentRsp)(nil), "rcmd.game_character.ShowPartnerSilentRsp")
	proto.RegisterType((*ShowContinueChatReq)(nil), "rcmd.game_character.ShowContinueChatReq")
	proto.RegisterType((*ShowContinueChatRsp)(nil), "rcmd.game_character.ShowContinueChatRsp")
	proto.RegisterType((*ContinueChatReq)(nil), "rcmd.game_character.ContinueChatReq")
	proto.RegisterType((*ContinueChatRsp)(nil), "rcmd.game_character.ContinueChatRsp")
	proto.RegisterType((*TestRoleSendMsgReq)(nil), "rcmd.game_character.TestRoleSendMsgReq")
	proto.RegisterType((*TestRoleSendMsgRsp)(nil), "rcmd.game_character.TestRoleSendMsgRsp")
	proto.RegisterType((*GetTarotConfigReq)(nil), "rcmd.game_character.GetTarotConfigReq")
	proto.RegisterType((*TarotCard)(nil), "rcmd.game_character.TarotCard")
	proto.RegisterType((*GetTarotConfigRsp)(nil), "rcmd.game_character.GetTarotConfigRsp")
	proto.RegisterType((*OpenTarotCardReq)(nil), "rcmd.game_character.OpenTarotCardReq")
	proto.RegisterType((*OpenTarotCardRsp)(nil), "rcmd.game_character.OpenTarotCardRsp")
	proto.RegisterType((*GetTarotResultReq)(nil), "rcmd.game_character.GetTarotResultReq")
	proto.RegisterType((*TarotResult)(nil), "rcmd.game_character.TarotResult")
	proto.RegisterType((*GetTarotResultRsp)(nil), "rcmd.game_character.GetTarotResultRsp")
	proto.RegisterType((*GetPetOwnDayReq)(nil), "rcmd.game_character.GetPetOwnDayReq")
	proto.RegisterType((*GetPetOwnDayRsp)(nil), "rcmd.game_character.GetPetOwnDayRsp")
	proto.RegisterType((*TestPetSendMsgReq)(nil), "rcmd.game_character.TestPetSendMsgReq")
	proto.RegisterType((*TestPetSendMsgRsp)(nil), "rcmd.game_character.TestPetSendMsgRsp")
	proto.RegisterType((*GetPetStoryNumReq)(nil), "rcmd.game_character.GetPetStoryNumReq")
	proto.RegisterType((*GetPetStoryNumRsp)(nil), "rcmd.game_character.GetPetStoryNumRsp")
	proto.RegisterType((*PetStory)(nil), "rcmd.game_character.PetStory")
	proto.RegisterType((*StoryPart)(nil), "rcmd.game_character.StoryPart")
	proto.RegisterType((*PushPetStoryPart)(nil), "rcmd.game_character.PushPetStoryPart")
	proto.RegisterType((*StartPetStoryReq)(nil), "rcmd.game_character.StartPetStoryReq")
	proto.RegisterType((*StartPetStoryRsp)(nil), "rcmd.game_character.StartPetStoryRsp")
	proto.RegisterType((*StopPetStoryReq)(nil), "rcmd.game_character.StopPetStoryReq")
	proto.RegisterType((*StopPetStoryRsp)(nil), "rcmd.game_character.StopPetStoryRsp")
	proto.RegisterType((*EnterPetSleepTalkReq)(nil), "rcmd.game_character.EnterPetSleepTalkReq")
	proto.RegisterType((*EnterPetSleepTalkRsp)(nil), "rcmd.game_character.EnterPetSleepTalkRsp")
	proto.RegisterType((*GetVibesConfigReq)(nil), "rcmd.game_character.GetVibesConfigReq")
	proto.RegisterType((*VibesConfig)(nil), "rcmd.game_character.VibesConfig")
	proto.RegisterType((*GetVibesConfigRsp)(nil), "rcmd.game_character.GetVibesConfigRsp")
	proto.RegisterType((*AddAccompanyTimeReq)(nil), "rcmd.game_character.AddAccompanyTimeReq")
	proto.RegisterType((*AddAccompanyTimeRsp)(nil), "rcmd.game_character.AddAccompanyTimeRsp")
	proto.RegisterType((*GetAccompanyTimeReq)(nil), "rcmd.game_character.GetAccompanyTimeReq")
	proto.RegisterType((*GetAccompanyTimeRsp)(nil), "rcmd.game_character.GetAccompanyTimeRsp")
	proto.RegisterType((*TargetInfo)(nil), "rcmd.game_character.TargetInfo")
	proto.RegisterType((*Target)(nil), "rcmd.game_character.Target")
	proto.RegisterType((*TargetCompleteParam)(nil), "rcmd.game_character.TargetCompleteParam")
	proto.RegisterType((*GetTargetListReq)(nil), "rcmd.game_character.GetTargetListReq")
	proto.RegisterType((*TargetComplete)(nil), "rcmd.game_character.TargetComplete")
	proto.RegisterType((*GetTargetListRsp)(nil), "rcmd.game_character.GetTargetListRsp")
	proto.RegisterType((*GetTargetRewardReq)(nil), "rcmd.game_character.GetTargetRewardReq")
	proto.RegisterType((*GetTargetRewardRsp)(nil), "rcmd.game_character.GetTargetRewardRsp")
	proto.RegisterType((*GetChatNumReq)(nil), "rcmd.game_character.GetChatNumReq")
	proto.RegisterType((*GetChatNumRsp)(nil), "rcmd.game_character.GetChatNumRsp")
	proto.RegisterType((*CheckAIRoomVisibilityReq)(nil), "rcmd.game_character.CheckAIRoomVisibilityReq")
	proto.RegisterType((*SetUserChatSceneReq)(nil), "rcmd.game_character.SetUserChatSceneReq")
	proto.RegisterType((*SetUserChatSceneResp)(nil), "rcmd.game_character.SetUserChatSceneResp")
	proto.RegisterType((*CheckChatSceneVisibilityReq)(nil), "rcmd.game_character.CheckChatSceneVisibilityReq")
	proto.RegisterType((*CheckAIRoomVisibilityResp)(nil), "rcmd.game_character.CheckAIRoomVisibilityResp")
	proto.RegisterType((*CheckChatSceneVisibilityResp)(nil), "rcmd.game_character.CheckChatSceneVisibilityResp")
	proto.RegisterType((*CheckIfTriggerChatReq)(nil), "rcmd.game_character.CheckIfTriggerChatReq")
	proto.RegisterType((*CheckIfTriggerChatResp)(nil), "rcmd.game_character.CheckIfTriggerChatResp")
	proto.RegisterType((*GetNotOpenedRolesReq)(nil), "rcmd.game_character.GetNotOpenedRolesReq")
	proto.RegisterType((*GetNotOpenedRolesResp)(nil), "rcmd.game_character.GetNotOpenedRolesResp")
	proto.RegisterType((*ChatRecord)(nil), "rcmd.game_character.ChatRecord")
	proto.RegisterType((*ChatHistoryAppendReq)(nil), "rcmd.game_character.ChatHistoryAppendReq")
	proto.RegisterType((*ChatHistoryAppendResp)(nil), "rcmd.game_character.ChatHistoryAppendResp")
	proto.RegisterType((*ReadPartnerHeartReq)(nil), "rcmd.game_character.ReadPartnerHeartReq")
	proto.RegisterType((*ReadPartnerHeartResp)(nil), "rcmd.game_character.ReadPartnerHeartResp")
	proto.RegisterEnum("rcmd.game_character.GameInfoOperate", GameInfoOperate_name, GameInfoOperate_value)
	proto.RegisterEnum("rcmd.game_character.GameCreator", GameCreator_name, GameCreator_value)
	proto.RegisterEnum("rcmd.game_character.GamePermission", GamePermission_name, GamePermission_value)
	proto.RegisterEnum("rcmd.game_character.TargetRewardType", TargetRewardType_name, TargetRewardType_value)
	proto.RegisterEnum("rcmd.game_character.TargetToDo", TargetToDo_name, TargetToDo_value)
	proto.RegisterEnum("rcmd.game_character.ComputeMethod", ComputeMethod_name, ComputeMethod_value)
	proto.RegisterEnum("rcmd.game_character.TargetTrigger", TargetTrigger_name, TargetTrigger_value)
	proto.RegisterEnum("rcmd.game_character.TargetType", TargetType_name, TargetType_value)
	proto.RegisterEnum("rcmd.game_character.ReplyTextFormatReq_FormatType", ReplyTextFormatReq_FormatType_name, ReplyTextFormatReq_FormatType_value)
	proto.RegisterEnum("rcmd.game_character.ReceiveMsgFromUserReq_MsgType", ReceiveMsgFromUserReq_MsgType_name, ReceiveMsgFromUserReq_MsgType_value)
	proto.RegisterEnum("rcmd.game_character.TestReq_Action", TestReq_Action_name, TestReq_Action_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RCMDGameCharacterClient is the client API for RCMDGameCharacter service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RCMDGameCharacterClient interface {
	// 设置角色信息
	SetCharacterInfo(ctx context.Context, in *SetCharacterInfoReq, opts ...grpc.CallOption) (*SetCharacterInfoResp, error)
	// 接收用户发送的消息
	ReceiveMsgFromUser(ctx context.Context, in *ReceiveMsgFromUserReq, opts ...grpc.CallOption) (*ReceiveMsgFromUserResp, error)
	// 用户进入聊天页通知
	UserEnterChattingNotify(ctx context.Context, in *UserEnterChattingNotifyReq, opts ...grpc.CallOption) (*UserEnterChattingNotifyResp, error)
	Test(ctx context.Context, in *TestReq, opts ...grpc.CallOption) (*TestResp, error)
	DeleteCharacter(ctx context.Context, in *DeleteCharacterReq, opts ...grpc.CallOption) (*DeleteCharacterResp, error)
	ReplyTextFormat(ctx context.Context, in *ReplyTextFormatReq, opts ...grpc.CallOption) (*ReplyTextFormatResp, error)
	BatchGetCharacters(ctx context.Context, in *BatchGetCharactersReq, opts ...grpc.CallOption) (*BatchGetCharactersResp, error)
	// 获取用户信息
	GetUserInfo(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error)
	// Get GPT Req info
	GetGPTReqInfo(ctx context.Context, in *GetGPTReqInfoReq, opts ...grpc.CallOption) (*GetGPTReqInfoResp, error)
	FormatGptAnswer(ctx context.Context, in *FormatGptAnswerReq, opts ...grpc.CallOption) (*FormatGptAnswerResp, error)
	// 开始互动玩法
	StartGame(ctx context.Context, in *StartGameReq, opts ...grpc.CallOption) (*StartGameResp, error)
	// 获取故事书列表
	GetStoryBookList(ctx context.Context, in *GetStoryBookListReq, opts ...grpc.CallOption) (*GetStoryBookListResp, error)
	GetStoryBook(ctx context.Context, in *GetStoryBookReq, opts ...grpc.CallOption) (*GetStoryBookResp, error)
	// 常驻入口红点
	GetStoryBookNotifySeq(ctx context.Context, in *GetStoryBookNotifySeqReq, opts ...grpc.CallOption) (*GetStoryBookNotifySeqResp, error)
	// 常驻红点已读
	MarkStoryBookNotifySeq(ctx context.Context, in *MarkStoryBookNotifySeqReq, opts ...grpc.CallOption) (*MarkStoryBookNotifySeqResp, error)
	// 开始故事章节
	StartStoryBook(ctx context.Context, in *StartStoryBookReq, opts ...grpc.CallOption) (*StartStoryBookResp, error)
	// 获取故事进度
	GetStoryBookProgress(ctx context.Context, in *GetStoryBookProgressReq, opts ...grpc.CallOption) (*GetStoryBookProgressResp, error)
	// 获取故事用户回复
	GetStoryBookReply(ctx context.Context, in *GetStoryBookReplyReq, opts ...grpc.CallOption) (*GetStoryBookReplyResp, error)
	// 获取故事历史记录
	GetStoryBookHistory(ctx context.Context, in *GetStoryBookHistoryReq, opts ...grpc.CallOption) (*GetStoryBookHistoryResp, error)
	// 推荐回复生成
	GenRecommendReply(ctx context.Context, in *GenRecommendReplyReq, opts ...grpc.CallOption) (*GenRecommendReplyResp, error)
	// 生成语音打招呼
	GenVoiceChatGreeting(ctx context.Context, in *GenVoiceChatGreetingReq, opts ...grpc.CallOption) (*GenVoiceChatGreetingResp, error)
	// 是否显示免打扰
	ShowPartnerSilent(ctx context.Context, in *ShowPartnerSilentReq, opts ...grpc.CallOption) (*ShowPartnerSilentRsp, error)
	// 是否显示继续说
	ShowContinueChat(ctx context.Context, in *ShowContinueChatReq, opts ...grpc.CallOption) (*ShowContinueChatRsp, error)
	// 继续说
	ContinueChat(ctx context.Context, in *ContinueChatReq, opts ...grpc.CallOption) (*ContinueChatRsp, error)
	// 给自己发消息1
	TestRoleSendMsg(ctx context.Context, in *TestRoleSendMsgReq, opts ...grpc.CallOption) (*TestRoleSendMsgRsp, error)
	// 获取塔罗配置
	GetTarotConfig(ctx context.Context, in *GetTarotConfigReq, opts ...grpc.CallOption) (*GetTarotConfigRsp, error)
	// 翻开塔罗牌
	OpenTarotCard(ctx context.Context, in *OpenTarotCardReq, opts ...grpc.CallOption) (*OpenTarotCardRsp, error)
	// 获取塔罗结果
	GetTarotResult(ctx context.Context, in *GetTarotResultReq, opts ...grpc.CallOption) (*GetTarotResultRsp, error)
	// 获取宠物陪伴天数
	GetPetOwnDay(ctx context.Context, in *GetPetOwnDayReq, opts ...grpc.CallOption) (*GetPetOwnDayRsp, error)
	// 让桌宠发消息
	TestPetSendMsg(ctx context.Context, in *TestPetSendMsgReq, opts ...grpc.CallOption) (*TestPetSendMsgRsp, error)
	// 获取听故事次数
	GetPetStoryNum(ctx context.Context, in *GetPetStoryNumReq, opts ...grpc.CallOption) (*GetPetStoryNumRsp, error)
	// 开始桌宠故事
	StartPetStory(ctx context.Context, in *StartPetStoryReq, opts ...grpc.CallOption) (*StartPetStoryRsp, error)
	// 结束桌宠故事
	StopPetStory(ctx context.Context, in *StopPetStoryReq, opts ...grpc.CallOption) (*StopPetStoryRsp, error)
	// 进入桌宠哄睡场景
	EnterPetSleepTalk(ctx context.Context, in *EnterPetSleepTalkReq, opts ...grpc.CallOption) (*EnterPetSleepTalkRsp, error)
	// AI房间可见性
	CheckAIRoomVisibility(ctx context.Context, in *CheckAIRoomVisibilityReq, opts ...grpc.CallOption) (*CheckAIRoomVisibilityResp, error)
	// 获取氛围配置
	GetVibesConfig(ctx context.Context, in *GetVibesConfigReq, opts ...grpc.CallOption) (*GetVibesConfigRsp, error)
	// 增加陪伴时间
	AddAccompanyTime(ctx context.Context, in *AddAccompanyTimeReq, opts ...grpc.CallOption) (*AddAccompanyTimeRsp, error)
	// 获取陪伴时间
	GetAccompanyTime(ctx context.Context, in *GetAccompanyTimeReq, opts ...grpc.CallOption) (*GetAccompanyTimeRsp, error)
	// 获取目标列表
	GetTargetList(ctx context.Context, in *GetTargetListReq, opts ...grpc.CallOption) (*GetTargetListRsp, error)
	// 领取目标奖励
	GetTargetReward(ctx context.Context, in *GetTargetRewardReq, opts ...grpc.CallOption) (*GetTargetRewardRsp, error)
	// 领取聊天次数
	GetChatNum(ctx context.Context, in *GetChatNumReq, opts ...grpc.CallOption) (*GetChatNumRsp, error)
	// 用户自定义场景相关
	SetUserChatScene(ctx context.Context, in *SetUserChatSceneReq, opts ...grpc.CallOption) (*SetUserChatSceneResp, error)
	CheckChatSceneVisibility(ctx context.Context, in *CheckChatSceneVisibilityReq, opts ...grpc.CallOption) (*CheckChatSceneVisibilityResp, error)
	// 获取外显玩法
	GetOutGames(ctx context.Context, in *GetOutGamesReq, opts ...grpc.CallOption) (*GetOutGamesRsp, error)
	// 获取主题列表
	GetGameTopics(ctx context.Context, in *GetGameTopicsReq, opts ...grpc.CallOption) (*GetGameTopicsRsp, error)
	// 获取某主题玩法列表
	GetTopicGames(ctx context.Context, in *GetTopicGamesReq, opts ...grpc.CallOption) (*GetTopicGamesRsp, error)
	// 获取玩法信息
	GetRoleGameInfo(ctx context.Context, in *GetRoleGameInfoReq, opts ...grpc.CallOption) (*GetRoleGameInfoRsp, error)
	// 用户是否需要触发聊天引导
	CheckIfTriggerChat(ctx context.Context, in *CheckIfTriggerChatReq, opts ...grpc.CallOption) (*CheckIfTriggerChatResp, error)
	// 获取未与用户聊过天的角色
	GetNotOpenedRoles(ctx context.Context, in *GetNotOpenedRolesReq, opts ...grpc.CallOption) (*GetNotOpenedRolesResp, error)
	// 聊天记录追加（续聊）
	ChatHistoryAppend(ctx context.Context, in *ChatHistoryAppendReq, opts ...grpc.CallOption) (*ChatHistoryAppendResp, error)
	// 读心
	ReadPartnerHeart(ctx context.Context, in *ReadPartnerHeartReq, opts ...grpc.CallOption) (*ReadPartnerHeartResp, error)
}

type rCMDGameCharacterClient struct {
	cc *grpc.ClientConn
}

func NewRCMDGameCharacterClient(cc *grpc.ClientConn) RCMDGameCharacterClient {
	return &rCMDGameCharacterClient{cc}
}

func (c *rCMDGameCharacterClient) SetCharacterInfo(ctx context.Context, in *SetCharacterInfoReq, opts ...grpc.CallOption) (*SetCharacterInfoResp, error) {
	out := new(SetCharacterInfoResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/SetCharacterInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) ReceiveMsgFromUser(ctx context.Context, in *ReceiveMsgFromUserReq, opts ...grpc.CallOption) (*ReceiveMsgFromUserResp, error) {
	out := new(ReceiveMsgFromUserResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/ReceiveMsgFromUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) UserEnterChattingNotify(ctx context.Context, in *UserEnterChattingNotifyReq, opts ...grpc.CallOption) (*UserEnterChattingNotifyResp, error) {
	out := new(UserEnterChattingNotifyResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/UserEnterChattingNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) Test(ctx context.Context, in *TestReq, opts ...grpc.CallOption) (*TestResp, error) {
	out := new(TestResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/Test", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) DeleteCharacter(ctx context.Context, in *DeleteCharacterReq, opts ...grpc.CallOption) (*DeleteCharacterResp, error) {
	out := new(DeleteCharacterResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/DeleteCharacter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) ReplyTextFormat(ctx context.Context, in *ReplyTextFormatReq, opts ...grpc.CallOption) (*ReplyTextFormatResp, error) {
	out := new(ReplyTextFormatResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/ReplyTextFormat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) BatchGetCharacters(ctx context.Context, in *BatchGetCharactersReq, opts ...grpc.CallOption) (*BatchGetCharactersResp, error) {
	out := new(BatchGetCharactersResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/BatchGetCharacters", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetUserInfo(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error) {
	out := new(GetUserInfoResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetGPTReqInfo(ctx context.Context, in *GetGPTReqInfoReq, opts ...grpc.CallOption) (*GetGPTReqInfoResp, error) {
	out := new(GetGPTReqInfoResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetGPTReqInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) FormatGptAnswer(ctx context.Context, in *FormatGptAnswerReq, opts ...grpc.CallOption) (*FormatGptAnswerResp, error) {
	out := new(FormatGptAnswerResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/FormatGptAnswer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) StartGame(ctx context.Context, in *StartGameReq, opts ...grpc.CallOption) (*StartGameResp, error) {
	out := new(StartGameResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/StartGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetStoryBookList(ctx context.Context, in *GetStoryBookListReq, opts ...grpc.CallOption) (*GetStoryBookListResp, error) {
	out := new(GetStoryBookListResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetStoryBookList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetStoryBook(ctx context.Context, in *GetStoryBookReq, opts ...grpc.CallOption) (*GetStoryBookResp, error) {
	out := new(GetStoryBookResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetStoryBook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetStoryBookNotifySeq(ctx context.Context, in *GetStoryBookNotifySeqReq, opts ...grpc.CallOption) (*GetStoryBookNotifySeqResp, error) {
	out := new(GetStoryBookNotifySeqResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetStoryBookNotifySeq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) MarkStoryBookNotifySeq(ctx context.Context, in *MarkStoryBookNotifySeqReq, opts ...grpc.CallOption) (*MarkStoryBookNotifySeqResp, error) {
	out := new(MarkStoryBookNotifySeqResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/MarkStoryBookNotifySeq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) StartStoryBook(ctx context.Context, in *StartStoryBookReq, opts ...grpc.CallOption) (*StartStoryBookResp, error) {
	out := new(StartStoryBookResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/StartStoryBook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetStoryBookProgress(ctx context.Context, in *GetStoryBookProgressReq, opts ...grpc.CallOption) (*GetStoryBookProgressResp, error) {
	out := new(GetStoryBookProgressResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetStoryBookProgress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetStoryBookReply(ctx context.Context, in *GetStoryBookReplyReq, opts ...grpc.CallOption) (*GetStoryBookReplyResp, error) {
	out := new(GetStoryBookReplyResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetStoryBookReply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetStoryBookHistory(ctx context.Context, in *GetStoryBookHistoryReq, opts ...grpc.CallOption) (*GetStoryBookHistoryResp, error) {
	out := new(GetStoryBookHistoryResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetStoryBookHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GenRecommendReply(ctx context.Context, in *GenRecommendReplyReq, opts ...grpc.CallOption) (*GenRecommendReplyResp, error) {
	out := new(GenRecommendReplyResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GenRecommendReply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GenVoiceChatGreeting(ctx context.Context, in *GenVoiceChatGreetingReq, opts ...grpc.CallOption) (*GenVoiceChatGreetingResp, error) {
	out := new(GenVoiceChatGreetingResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GenVoiceChatGreeting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) ShowPartnerSilent(ctx context.Context, in *ShowPartnerSilentReq, opts ...grpc.CallOption) (*ShowPartnerSilentRsp, error) {
	out := new(ShowPartnerSilentRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/ShowPartnerSilent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) ShowContinueChat(ctx context.Context, in *ShowContinueChatReq, opts ...grpc.CallOption) (*ShowContinueChatRsp, error) {
	out := new(ShowContinueChatRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/ShowContinueChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) ContinueChat(ctx context.Context, in *ContinueChatReq, opts ...grpc.CallOption) (*ContinueChatRsp, error) {
	out := new(ContinueChatRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/ContinueChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) TestRoleSendMsg(ctx context.Context, in *TestRoleSendMsgReq, opts ...grpc.CallOption) (*TestRoleSendMsgRsp, error) {
	out := new(TestRoleSendMsgRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/TestRoleSendMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetTarotConfig(ctx context.Context, in *GetTarotConfigReq, opts ...grpc.CallOption) (*GetTarotConfigRsp, error) {
	out := new(GetTarotConfigRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetTarotConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) OpenTarotCard(ctx context.Context, in *OpenTarotCardReq, opts ...grpc.CallOption) (*OpenTarotCardRsp, error) {
	out := new(OpenTarotCardRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/OpenTarotCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetTarotResult(ctx context.Context, in *GetTarotResultReq, opts ...grpc.CallOption) (*GetTarotResultRsp, error) {
	out := new(GetTarotResultRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetTarotResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetPetOwnDay(ctx context.Context, in *GetPetOwnDayReq, opts ...grpc.CallOption) (*GetPetOwnDayRsp, error) {
	out := new(GetPetOwnDayRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetPetOwnDay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) TestPetSendMsg(ctx context.Context, in *TestPetSendMsgReq, opts ...grpc.CallOption) (*TestPetSendMsgRsp, error) {
	out := new(TestPetSendMsgRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/TestPetSendMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetPetStoryNum(ctx context.Context, in *GetPetStoryNumReq, opts ...grpc.CallOption) (*GetPetStoryNumRsp, error) {
	out := new(GetPetStoryNumRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetPetStoryNum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) StartPetStory(ctx context.Context, in *StartPetStoryReq, opts ...grpc.CallOption) (*StartPetStoryRsp, error) {
	out := new(StartPetStoryRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/StartPetStory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) StopPetStory(ctx context.Context, in *StopPetStoryReq, opts ...grpc.CallOption) (*StopPetStoryRsp, error) {
	out := new(StopPetStoryRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/StopPetStory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) EnterPetSleepTalk(ctx context.Context, in *EnterPetSleepTalkReq, opts ...grpc.CallOption) (*EnterPetSleepTalkRsp, error) {
	out := new(EnterPetSleepTalkRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/EnterPetSleepTalk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) CheckAIRoomVisibility(ctx context.Context, in *CheckAIRoomVisibilityReq, opts ...grpc.CallOption) (*CheckAIRoomVisibilityResp, error) {
	out := new(CheckAIRoomVisibilityResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/CheckAIRoomVisibility", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetVibesConfig(ctx context.Context, in *GetVibesConfigReq, opts ...grpc.CallOption) (*GetVibesConfigRsp, error) {
	out := new(GetVibesConfigRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetVibesConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) AddAccompanyTime(ctx context.Context, in *AddAccompanyTimeReq, opts ...grpc.CallOption) (*AddAccompanyTimeRsp, error) {
	out := new(AddAccompanyTimeRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/AddAccompanyTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetAccompanyTime(ctx context.Context, in *GetAccompanyTimeReq, opts ...grpc.CallOption) (*GetAccompanyTimeRsp, error) {
	out := new(GetAccompanyTimeRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetAccompanyTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetTargetList(ctx context.Context, in *GetTargetListReq, opts ...grpc.CallOption) (*GetTargetListRsp, error) {
	out := new(GetTargetListRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetTargetList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetTargetReward(ctx context.Context, in *GetTargetRewardReq, opts ...grpc.CallOption) (*GetTargetRewardRsp, error) {
	out := new(GetTargetRewardRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetTargetReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetChatNum(ctx context.Context, in *GetChatNumReq, opts ...grpc.CallOption) (*GetChatNumRsp, error) {
	out := new(GetChatNumRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetChatNum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) SetUserChatScene(ctx context.Context, in *SetUserChatSceneReq, opts ...grpc.CallOption) (*SetUserChatSceneResp, error) {
	out := new(SetUserChatSceneResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/SetUserChatScene", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) CheckChatSceneVisibility(ctx context.Context, in *CheckChatSceneVisibilityReq, opts ...grpc.CallOption) (*CheckChatSceneVisibilityResp, error) {
	out := new(CheckChatSceneVisibilityResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/CheckChatSceneVisibility", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetOutGames(ctx context.Context, in *GetOutGamesReq, opts ...grpc.CallOption) (*GetOutGamesRsp, error) {
	out := new(GetOutGamesRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetOutGames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetGameTopics(ctx context.Context, in *GetGameTopicsReq, opts ...grpc.CallOption) (*GetGameTopicsRsp, error) {
	out := new(GetGameTopicsRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetGameTopics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetTopicGames(ctx context.Context, in *GetTopicGamesReq, opts ...grpc.CallOption) (*GetTopicGamesRsp, error) {
	out := new(GetTopicGamesRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetTopicGames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetRoleGameInfo(ctx context.Context, in *GetRoleGameInfoReq, opts ...grpc.CallOption) (*GetRoleGameInfoRsp, error) {
	out := new(GetRoleGameInfoRsp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetRoleGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) CheckIfTriggerChat(ctx context.Context, in *CheckIfTriggerChatReq, opts ...grpc.CallOption) (*CheckIfTriggerChatResp, error) {
	out := new(CheckIfTriggerChatResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/CheckIfTriggerChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) GetNotOpenedRoles(ctx context.Context, in *GetNotOpenedRolesReq, opts ...grpc.CallOption) (*GetNotOpenedRolesResp, error) {
	out := new(GetNotOpenedRolesResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/GetNotOpenedRoles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) ChatHistoryAppend(ctx context.Context, in *ChatHistoryAppendReq, opts ...grpc.CallOption) (*ChatHistoryAppendResp, error) {
	out := new(ChatHistoryAppendResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/ChatHistoryAppend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDGameCharacterClient) ReadPartnerHeart(ctx context.Context, in *ReadPartnerHeartReq, opts ...grpc.CallOption) (*ReadPartnerHeartResp, error) {
	out := new(ReadPartnerHeartResp)
	err := c.cc.Invoke(ctx, "/rcmd.game_character.RCMDGameCharacter/ReadPartnerHeart", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RCMDGameCharacterServer is the server API for RCMDGameCharacter service.
type RCMDGameCharacterServer interface {
	// 设置角色信息
	SetCharacterInfo(context.Context, *SetCharacterInfoReq) (*SetCharacterInfoResp, error)
	// 接收用户发送的消息
	ReceiveMsgFromUser(context.Context, *ReceiveMsgFromUserReq) (*ReceiveMsgFromUserResp, error)
	// 用户进入聊天页通知
	UserEnterChattingNotify(context.Context, *UserEnterChattingNotifyReq) (*UserEnterChattingNotifyResp, error)
	Test(context.Context, *TestReq) (*TestResp, error)
	DeleteCharacter(context.Context, *DeleteCharacterReq) (*DeleteCharacterResp, error)
	ReplyTextFormat(context.Context, *ReplyTextFormatReq) (*ReplyTextFormatResp, error)
	BatchGetCharacters(context.Context, *BatchGetCharactersReq) (*BatchGetCharactersResp, error)
	// 获取用户信息
	GetUserInfo(context.Context, *GetUserInfoReq) (*GetUserInfoResp, error)
	// Get GPT Req info
	GetGPTReqInfo(context.Context, *GetGPTReqInfoReq) (*GetGPTReqInfoResp, error)
	FormatGptAnswer(context.Context, *FormatGptAnswerReq) (*FormatGptAnswerResp, error)
	// 开始互动玩法
	StartGame(context.Context, *StartGameReq) (*StartGameResp, error)
	// 获取故事书列表
	GetStoryBookList(context.Context, *GetStoryBookListReq) (*GetStoryBookListResp, error)
	GetStoryBook(context.Context, *GetStoryBookReq) (*GetStoryBookResp, error)
	// 常驻入口红点
	GetStoryBookNotifySeq(context.Context, *GetStoryBookNotifySeqReq) (*GetStoryBookNotifySeqResp, error)
	// 常驻红点已读
	MarkStoryBookNotifySeq(context.Context, *MarkStoryBookNotifySeqReq) (*MarkStoryBookNotifySeqResp, error)
	// 开始故事章节
	StartStoryBook(context.Context, *StartStoryBookReq) (*StartStoryBookResp, error)
	// 获取故事进度
	GetStoryBookProgress(context.Context, *GetStoryBookProgressReq) (*GetStoryBookProgressResp, error)
	// 获取故事用户回复
	GetStoryBookReply(context.Context, *GetStoryBookReplyReq) (*GetStoryBookReplyResp, error)
	// 获取故事历史记录
	GetStoryBookHistory(context.Context, *GetStoryBookHistoryReq) (*GetStoryBookHistoryResp, error)
	// 推荐回复生成
	GenRecommendReply(context.Context, *GenRecommendReplyReq) (*GenRecommendReplyResp, error)
	// 生成语音打招呼
	GenVoiceChatGreeting(context.Context, *GenVoiceChatGreetingReq) (*GenVoiceChatGreetingResp, error)
	// 是否显示免打扰
	ShowPartnerSilent(context.Context, *ShowPartnerSilentReq) (*ShowPartnerSilentRsp, error)
	// 是否显示继续说
	ShowContinueChat(context.Context, *ShowContinueChatReq) (*ShowContinueChatRsp, error)
	// 继续说
	ContinueChat(context.Context, *ContinueChatReq) (*ContinueChatRsp, error)
	// 给自己发消息1
	TestRoleSendMsg(context.Context, *TestRoleSendMsgReq) (*TestRoleSendMsgRsp, error)
	// 获取塔罗配置
	GetTarotConfig(context.Context, *GetTarotConfigReq) (*GetTarotConfigRsp, error)
	// 翻开塔罗牌
	OpenTarotCard(context.Context, *OpenTarotCardReq) (*OpenTarotCardRsp, error)
	// 获取塔罗结果
	GetTarotResult(context.Context, *GetTarotResultReq) (*GetTarotResultRsp, error)
	// 获取宠物陪伴天数
	GetPetOwnDay(context.Context, *GetPetOwnDayReq) (*GetPetOwnDayRsp, error)
	// 让桌宠发消息
	TestPetSendMsg(context.Context, *TestPetSendMsgReq) (*TestPetSendMsgRsp, error)
	// 获取听故事次数
	GetPetStoryNum(context.Context, *GetPetStoryNumReq) (*GetPetStoryNumRsp, error)
	// 开始桌宠故事
	StartPetStory(context.Context, *StartPetStoryReq) (*StartPetStoryRsp, error)
	// 结束桌宠故事
	StopPetStory(context.Context, *StopPetStoryReq) (*StopPetStoryRsp, error)
	// 进入桌宠哄睡场景
	EnterPetSleepTalk(context.Context, *EnterPetSleepTalkReq) (*EnterPetSleepTalkRsp, error)
	// AI房间可见性
	CheckAIRoomVisibility(context.Context, *CheckAIRoomVisibilityReq) (*CheckAIRoomVisibilityResp, error)
	// 获取氛围配置
	GetVibesConfig(context.Context, *GetVibesConfigReq) (*GetVibesConfigRsp, error)
	// 增加陪伴时间
	AddAccompanyTime(context.Context, *AddAccompanyTimeReq) (*AddAccompanyTimeRsp, error)
	// 获取陪伴时间
	GetAccompanyTime(context.Context, *GetAccompanyTimeReq) (*GetAccompanyTimeRsp, error)
	// 获取目标列表
	GetTargetList(context.Context, *GetTargetListReq) (*GetTargetListRsp, error)
	// 领取目标奖励
	GetTargetReward(context.Context, *GetTargetRewardReq) (*GetTargetRewardRsp, error)
	// 领取聊天次数
	GetChatNum(context.Context, *GetChatNumReq) (*GetChatNumRsp, error)
	// 用户自定义场景相关
	SetUserChatScene(context.Context, *SetUserChatSceneReq) (*SetUserChatSceneResp, error)
	CheckChatSceneVisibility(context.Context, *CheckChatSceneVisibilityReq) (*CheckChatSceneVisibilityResp, error)
	// 获取外显玩法
	GetOutGames(context.Context, *GetOutGamesReq) (*GetOutGamesRsp, error)
	// 获取主题列表
	GetGameTopics(context.Context, *GetGameTopicsReq) (*GetGameTopicsRsp, error)
	// 获取某主题玩法列表
	GetTopicGames(context.Context, *GetTopicGamesReq) (*GetTopicGamesRsp, error)
	// 获取玩法信息
	GetRoleGameInfo(context.Context, *GetRoleGameInfoReq) (*GetRoleGameInfoRsp, error)
	// 用户是否需要触发聊天引导
	CheckIfTriggerChat(context.Context, *CheckIfTriggerChatReq) (*CheckIfTriggerChatResp, error)
	// 获取未与用户聊过天的角色
	GetNotOpenedRoles(context.Context, *GetNotOpenedRolesReq) (*GetNotOpenedRolesResp, error)
	// 聊天记录追加（续聊）
	ChatHistoryAppend(context.Context, *ChatHistoryAppendReq) (*ChatHistoryAppendResp, error)
	// 读心
	ReadPartnerHeart(context.Context, *ReadPartnerHeartReq) (*ReadPartnerHeartResp, error)
}

func RegisterRCMDGameCharacterServer(s *grpc.Server, srv RCMDGameCharacterServer) {
	s.RegisterService(&_RCMDGameCharacter_serviceDesc, srv)
}

func _RCMDGameCharacter_SetCharacterInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCharacterInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).SetCharacterInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/SetCharacterInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).SetCharacterInfo(ctx, req.(*SetCharacterInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_ReceiveMsgFromUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveMsgFromUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).ReceiveMsgFromUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/ReceiveMsgFromUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).ReceiveMsgFromUser(ctx, req.(*ReceiveMsgFromUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_UserEnterChattingNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserEnterChattingNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).UserEnterChattingNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/UserEnterChattingNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).UserEnterChattingNotify(ctx, req.(*UserEnterChattingNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_Test_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).Test(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/Test",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).Test(ctx, req.(*TestReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_DeleteCharacter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCharacterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).DeleteCharacter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/DeleteCharacter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).DeleteCharacter(ctx, req.(*DeleteCharacterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_ReplyTextFormat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReplyTextFormatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).ReplyTextFormat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/ReplyTextFormat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).ReplyTextFormat(ctx, req.(*ReplyTextFormatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_BatchGetCharacters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetCharactersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).BatchGetCharacters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/BatchGetCharacters",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).BatchGetCharacters(ctx, req.(*BatchGetCharactersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetUserInfo(ctx, req.(*GetUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetGPTReqInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGPTReqInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetGPTReqInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetGPTReqInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetGPTReqInfo(ctx, req.(*GetGPTReqInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_FormatGptAnswer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FormatGptAnswerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).FormatGptAnswer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/FormatGptAnswer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).FormatGptAnswer(ctx, req.(*FormatGptAnswerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_StartGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).StartGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/StartGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).StartGame(ctx, req.(*StartGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetStoryBookList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStoryBookListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetStoryBookList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetStoryBookList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetStoryBookList(ctx, req.(*GetStoryBookListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetStoryBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStoryBookReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetStoryBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetStoryBook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetStoryBook(ctx, req.(*GetStoryBookReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetStoryBookNotifySeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStoryBookNotifySeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetStoryBookNotifySeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetStoryBookNotifySeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetStoryBookNotifySeq(ctx, req.(*GetStoryBookNotifySeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_MarkStoryBookNotifySeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkStoryBookNotifySeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).MarkStoryBookNotifySeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/MarkStoryBookNotifySeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).MarkStoryBookNotifySeq(ctx, req.(*MarkStoryBookNotifySeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_StartStoryBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartStoryBookReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).StartStoryBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/StartStoryBook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).StartStoryBook(ctx, req.(*StartStoryBookReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetStoryBookProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStoryBookProgressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetStoryBookProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetStoryBookProgress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetStoryBookProgress(ctx, req.(*GetStoryBookProgressReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetStoryBookReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStoryBookReplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetStoryBookReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetStoryBookReply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetStoryBookReply(ctx, req.(*GetStoryBookReplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetStoryBookHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStoryBookHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetStoryBookHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetStoryBookHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetStoryBookHistory(ctx, req.(*GetStoryBookHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GenRecommendReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenRecommendReplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GenRecommendReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GenRecommendReply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GenRecommendReply(ctx, req.(*GenRecommendReplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GenVoiceChatGreeting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenVoiceChatGreetingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GenVoiceChatGreeting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GenVoiceChatGreeting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GenVoiceChatGreeting(ctx, req.(*GenVoiceChatGreetingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_ShowPartnerSilent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShowPartnerSilentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).ShowPartnerSilent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/ShowPartnerSilent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).ShowPartnerSilent(ctx, req.(*ShowPartnerSilentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_ShowContinueChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShowContinueChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).ShowContinueChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/ShowContinueChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).ShowContinueChat(ctx, req.(*ShowContinueChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_ContinueChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContinueChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).ContinueChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/ContinueChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).ContinueChat(ctx, req.(*ContinueChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_TestRoleSendMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestRoleSendMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).TestRoleSendMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/TestRoleSendMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).TestRoleSendMsg(ctx, req.(*TestRoleSendMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetTarotConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTarotConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetTarotConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetTarotConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetTarotConfig(ctx, req.(*GetTarotConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_OpenTarotCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OpenTarotCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).OpenTarotCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/OpenTarotCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).OpenTarotCard(ctx, req.(*OpenTarotCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetTarotResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTarotResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetTarotResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetTarotResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetTarotResult(ctx, req.(*GetTarotResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetPetOwnDay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetOwnDayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetPetOwnDay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetPetOwnDay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetPetOwnDay(ctx, req.(*GetPetOwnDayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_TestPetSendMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestPetSendMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).TestPetSendMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/TestPetSendMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).TestPetSendMsg(ctx, req.(*TestPetSendMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetPetStoryNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetStoryNumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetPetStoryNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetPetStoryNum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetPetStoryNum(ctx, req.(*GetPetStoryNumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_StartPetStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartPetStoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).StartPetStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/StartPetStory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).StartPetStory(ctx, req.(*StartPetStoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_StopPetStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopPetStoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).StopPetStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/StopPetStory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).StopPetStory(ctx, req.(*StopPetStoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_EnterPetSleepTalk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnterPetSleepTalkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).EnterPetSleepTalk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/EnterPetSleepTalk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).EnterPetSleepTalk(ctx, req.(*EnterPetSleepTalkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_CheckAIRoomVisibility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckAIRoomVisibilityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).CheckAIRoomVisibility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/CheckAIRoomVisibility",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).CheckAIRoomVisibility(ctx, req.(*CheckAIRoomVisibilityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetVibesConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVibesConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetVibesConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetVibesConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetVibesConfig(ctx, req.(*GetVibesConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_AddAccompanyTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAccompanyTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).AddAccompanyTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/AddAccompanyTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).AddAccompanyTime(ctx, req.(*AddAccompanyTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetAccompanyTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccompanyTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetAccompanyTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetAccompanyTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetAccompanyTime(ctx, req.(*GetAccompanyTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetTargetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTargetListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetTargetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetTargetList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetTargetList(ctx, req.(*GetTargetListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetTargetReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTargetRewardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetTargetReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetTargetReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetTargetReward(ctx, req.(*GetTargetRewardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetChatNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChatNumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetChatNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetChatNum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetChatNum(ctx, req.(*GetChatNumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_SetUserChatScene_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserChatSceneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).SetUserChatScene(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/SetUserChatScene",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).SetUserChatScene(ctx, req.(*SetUserChatSceneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_CheckChatSceneVisibility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckChatSceneVisibilityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).CheckChatSceneVisibility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/CheckChatSceneVisibility",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).CheckChatSceneVisibility(ctx, req.(*CheckChatSceneVisibilityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetOutGames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOutGamesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetOutGames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetOutGames",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetOutGames(ctx, req.(*GetOutGamesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetGameTopics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameTopicsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetGameTopics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetGameTopics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetGameTopics(ctx, req.(*GetGameTopicsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetTopicGames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicGamesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetTopicGames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetTopicGames",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetTopicGames(ctx, req.(*GetTopicGamesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetRoleGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRoleGameInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetRoleGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetRoleGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetRoleGameInfo(ctx, req.(*GetRoleGameInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_CheckIfTriggerChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIfTriggerChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).CheckIfTriggerChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/CheckIfTriggerChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).CheckIfTriggerChat(ctx, req.(*CheckIfTriggerChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_GetNotOpenedRoles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNotOpenedRolesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).GetNotOpenedRoles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/GetNotOpenedRoles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).GetNotOpenedRoles(ctx, req.(*GetNotOpenedRolesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_ChatHistoryAppend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChatHistoryAppendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).ChatHistoryAppend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/ChatHistoryAppend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).ChatHistoryAppend(ctx, req.(*ChatHistoryAppendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDGameCharacter_ReadPartnerHeart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadPartnerHeartReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDGameCharacterServer).ReadPartnerHeart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.game_character.RCMDGameCharacter/ReadPartnerHeart",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDGameCharacterServer).ReadPartnerHeart(ctx, req.(*ReadPartnerHeartReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RCMDGameCharacter_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.game_character.RCMDGameCharacter",
	HandlerType: (*RCMDGameCharacterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetCharacterInfo",
			Handler:    _RCMDGameCharacter_SetCharacterInfo_Handler,
		},
		{
			MethodName: "ReceiveMsgFromUser",
			Handler:    _RCMDGameCharacter_ReceiveMsgFromUser_Handler,
		},
		{
			MethodName: "UserEnterChattingNotify",
			Handler:    _RCMDGameCharacter_UserEnterChattingNotify_Handler,
		},
		{
			MethodName: "Test",
			Handler:    _RCMDGameCharacter_Test_Handler,
		},
		{
			MethodName: "DeleteCharacter",
			Handler:    _RCMDGameCharacter_DeleteCharacter_Handler,
		},
		{
			MethodName: "ReplyTextFormat",
			Handler:    _RCMDGameCharacter_ReplyTextFormat_Handler,
		},
		{
			MethodName: "BatchGetCharacters",
			Handler:    _RCMDGameCharacter_BatchGetCharacters_Handler,
		},
		{
			MethodName: "GetUserInfo",
			Handler:    _RCMDGameCharacter_GetUserInfo_Handler,
		},
		{
			MethodName: "GetGPTReqInfo",
			Handler:    _RCMDGameCharacter_GetGPTReqInfo_Handler,
		},
		{
			MethodName: "FormatGptAnswer",
			Handler:    _RCMDGameCharacter_FormatGptAnswer_Handler,
		},
		{
			MethodName: "StartGame",
			Handler:    _RCMDGameCharacter_StartGame_Handler,
		},
		{
			MethodName: "GetStoryBookList",
			Handler:    _RCMDGameCharacter_GetStoryBookList_Handler,
		},
		{
			MethodName: "GetStoryBook",
			Handler:    _RCMDGameCharacter_GetStoryBook_Handler,
		},
		{
			MethodName: "GetStoryBookNotifySeq",
			Handler:    _RCMDGameCharacter_GetStoryBookNotifySeq_Handler,
		},
		{
			MethodName: "MarkStoryBookNotifySeq",
			Handler:    _RCMDGameCharacter_MarkStoryBookNotifySeq_Handler,
		},
		{
			MethodName: "StartStoryBook",
			Handler:    _RCMDGameCharacter_StartStoryBook_Handler,
		},
		{
			MethodName: "GetStoryBookProgress",
			Handler:    _RCMDGameCharacter_GetStoryBookProgress_Handler,
		},
		{
			MethodName: "GetStoryBookReply",
			Handler:    _RCMDGameCharacter_GetStoryBookReply_Handler,
		},
		{
			MethodName: "GetStoryBookHistory",
			Handler:    _RCMDGameCharacter_GetStoryBookHistory_Handler,
		},
		{
			MethodName: "GenRecommendReply",
			Handler:    _RCMDGameCharacter_GenRecommendReply_Handler,
		},
		{
			MethodName: "GenVoiceChatGreeting",
			Handler:    _RCMDGameCharacter_GenVoiceChatGreeting_Handler,
		},
		{
			MethodName: "ShowPartnerSilent",
			Handler:    _RCMDGameCharacter_ShowPartnerSilent_Handler,
		},
		{
			MethodName: "ShowContinueChat",
			Handler:    _RCMDGameCharacter_ShowContinueChat_Handler,
		},
		{
			MethodName: "ContinueChat",
			Handler:    _RCMDGameCharacter_ContinueChat_Handler,
		},
		{
			MethodName: "TestRoleSendMsg",
			Handler:    _RCMDGameCharacter_TestRoleSendMsg_Handler,
		},
		{
			MethodName: "GetTarotConfig",
			Handler:    _RCMDGameCharacter_GetTarotConfig_Handler,
		},
		{
			MethodName: "OpenTarotCard",
			Handler:    _RCMDGameCharacter_OpenTarotCard_Handler,
		},
		{
			MethodName: "GetTarotResult",
			Handler:    _RCMDGameCharacter_GetTarotResult_Handler,
		},
		{
			MethodName: "GetPetOwnDay",
			Handler:    _RCMDGameCharacter_GetPetOwnDay_Handler,
		},
		{
			MethodName: "TestPetSendMsg",
			Handler:    _RCMDGameCharacter_TestPetSendMsg_Handler,
		},
		{
			MethodName: "GetPetStoryNum",
			Handler:    _RCMDGameCharacter_GetPetStoryNum_Handler,
		},
		{
			MethodName: "StartPetStory",
			Handler:    _RCMDGameCharacter_StartPetStory_Handler,
		},
		{
			MethodName: "StopPetStory",
			Handler:    _RCMDGameCharacter_StopPetStory_Handler,
		},
		{
			MethodName: "EnterPetSleepTalk",
			Handler:    _RCMDGameCharacter_EnterPetSleepTalk_Handler,
		},
		{
			MethodName: "CheckAIRoomVisibility",
			Handler:    _RCMDGameCharacter_CheckAIRoomVisibility_Handler,
		},
		{
			MethodName: "GetVibesConfig",
			Handler:    _RCMDGameCharacter_GetVibesConfig_Handler,
		},
		{
			MethodName: "AddAccompanyTime",
			Handler:    _RCMDGameCharacter_AddAccompanyTime_Handler,
		},
		{
			MethodName: "GetAccompanyTime",
			Handler:    _RCMDGameCharacter_GetAccompanyTime_Handler,
		},
		{
			MethodName: "GetTargetList",
			Handler:    _RCMDGameCharacter_GetTargetList_Handler,
		},
		{
			MethodName: "GetTargetReward",
			Handler:    _RCMDGameCharacter_GetTargetReward_Handler,
		},
		{
			MethodName: "GetChatNum",
			Handler:    _RCMDGameCharacter_GetChatNum_Handler,
		},
		{
			MethodName: "SetUserChatScene",
			Handler:    _RCMDGameCharacter_SetUserChatScene_Handler,
		},
		{
			MethodName: "CheckChatSceneVisibility",
			Handler:    _RCMDGameCharacter_CheckChatSceneVisibility_Handler,
		},
		{
			MethodName: "GetOutGames",
			Handler:    _RCMDGameCharacter_GetOutGames_Handler,
		},
		{
			MethodName: "GetGameTopics",
			Handler:    _RCMDGameCharacter_GetGameTopics_Handler,
		},
		{
			MethodName: "GetTopicGames",
			Handler:    _RCMDGameCharacter_GetTopicGames_Handler,
		},
		{
			MethodName: "GetRoleGameInfo",
			Handler:    _RCMDGameCharacter_GetRoleGameInfo_Handler,
		},
		{
			MethodName: "CheckIfTriggerChat",
			Handler:    _RCMDGameCharacter_CheckIfTriggerChat_Handler,
		},
		{
			MethodName: "GetNotOpenedRoles",
			Handler:    _RCMDGameCharacter_GetNotOpenedRoles_Handler,
		},
		{
			MethodName: "ChatHistoryAppend",
			Handler:    _RCMDGameCharacter_ChatHistoryAppend_Handler,
		},
		{
			MethodName: "ReadPartnerHeart",
			Handler:    _RCMDGameCharacter_ReadPartnerHeart_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rcmd-ai-partner/game_character.proto",
}

func init() {
	proto.RegisterFile("rcmd-ai-partner/game_character.proto", fileDescriptor_game_character_585385b141559bb5)
}

var fileDescriptor_game_character_585385b141559bb5 = []byte{
	// 5642 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x7c, 0xdf, 0x73, 0x1b, 0xc9,
	0x71, 0xf0, 0x01, 0x24, 0x41, 0xa0, 0x41, 0x90, 0xe0, 0x90, 0xa2, 0x28, 0xf0, 0xa4, 0xd3, 0xed,
	0xc9, 0x67, 0x1d, 0xed, 0x93, 0x6c, 0xd9, 0x77, 0x67, 0x7f, 0xfe, 0x9c, 0x33, 0x05, 0xc2, 0x14,
	0x6d, 0xf1, 0x47, 0x2d, 0x41, 0x29, 0xb6, 0xcb, 0xc1, 0x2d, 0x77, 0x87, 0xe0, 0x1e, 0x81, 0xdd,
	0xd5, 0xce, 0x80, 0x12, 0x93, 0xd8, 0x49, 0x5c, 0xa9, 0xe4, 0x21, 0x55, 0xa9, 0x54, 0x2a, 0x2e,
	0x57, 0xd9, 0xe5, 0xa7, 0x3c, 0xe5, 0x35, 0x79, 0x4c, 0x55, 0xde, 0xf3, 0x94, 0x3c, 0xe7, 0x21,
	0x55, 0xc9, 0x6b, 0xfe, 0x89, 0x54, 0xf7, 0xcc, 0x2e, 0x76, 0x17, 0xbb, 0x12, 0xa4, 0xc8, 0xaa,
	0x7b, 0xc2, 0x4e, 0x77, 0xcf, 0x4c, 0xcf, 0x4c, 0x4f, 0x4f, 0x4f, 0x77, 0x0f, 0xe0, 0x56, 0x68,
	0x0f, 0x9d, 0x0f, 0x2d, 0xf7, 0xc3, 0xc0, 0x0a, 0xa5, 0xc7, 0xc3, 0xbb, 0x7d, 0x6b, 0xc8, 0x7b,
	0xf6, 0x99, 0x15, 0x5a, 0xb6, 0xe4, 0xe1, 0x9d, 0x20, 0xf4, 0xa5, 0xcf, 0x56, 0x90, 0xea, 0x4e,
	0x1a, 0xd5, 0x9a, 0xa8, 0xaa, 0x7f, 0x7b, 0xb6, 0x3f, 0x1c, 0xfa, 0x9e, 0xaa, 0x6a, 0xfc, 0xb2,
	0x04, 0x6c, 0x87, 0x4b, 0xd3, 0x1f, 0xf0, 0x1d, 0x6b, 0xc8, 0x77, 0xbd, 0x53, 0xdf, 0xe4, 0x4f,
	0x58, 0x13, 0x66, 0x46, 0xae, 0xb3, 0x5e, 0xba, 0x59, 0xba, 0xdd, 0x30, 0xf1, 0x93, 0x5d, 0x07,
	0x88, 0x1a, 0x70, 0x9d, 0xf5, 0x32, 0x21, 0x6a, 0x1a, 0xb2, 0xeb, 0xb0, 0xab, 0x30, 0x1f, 0xfa,
	0x03, 0x8e, 0xb8, 0x19, 0xc2, 0x55, 0xb0, 0xb8, 0xeb, 0xb0, 0xb7, 0x01, 0x42, 0xfe, 0xa4, 0xe7,
	0xf0, 0x0b, 0xc4, 0xcd, 0xde, 0x2c, 0xdd, 0xae, 0x99, 0xd5, 0x90, 0x3f, 0xd9, 0xe6, 0x17, 0xaa,
	0x1a, 0xb1, 0xed, 0x3a, 0xeb, 0x73, 0x84, 0xaa, 0x60, 0x71, 0xd7, 0x31, 0x76, 0x26, 0xd9, 0x12,
	0x01, 0xfb, 0x3a, 0xcc, 0x22, 0x9e, 0xf8, 0xaa, 0xdf, 0xbb, 0x7e, 0x27, 0x67, 0xdc, 0x77, 0x62,
	0x7a, 0x22, 0xc5, 0x01, 0x36, 0x77, 0xb8, 0xec, 0xfa, 0x81, 0x6b, 0x23, 0x4a, 0xbc, 0xc9, 0xe1,
	0x5d, 0x83, 0xaa, 0xc4, 0x8e, 0xa3, 0xf1, 0x35, 0xcc, 0x79, 0x2a, 0xd3, 0x00, 0x33, 0x6c, 0x89,
	0x80, 0x7d, 0x03, 0xe6, 0x90, 0x67, 0xb1, 0x5e, 0xba, 0x39, 0xf3, 0xe2, 0xf1, 0x29, 0x5a, 0xe3,
	0xbb, 0xd4, 0x10, 0x42, 0xa9, 0xb1, 0x82, 0xf1, 0x25, 0x06, 0x50, 0x4e, 0x0e, 0xc0, 0xf8, 0x41,
	0xb6, 0xba, 0x08, 0xd8, 0xc7, 0x50, 0x21, 0x36, 0x23, 0x46, 0x6e, 0x14, 0x32, 0x42, 0x75, 0x4c,
	0x4d, 0x6d, 0xfc, 0xaa, 0x04, 0x8b, 0x11, 0x7b, 0xfb, 0xbe, 0x74, 0x4f, 0x2f, 0x73, 0x38, 0xf9,
	0x3d, 0x98, 0xf7, 0x03, 0x1e, 0x5a, 0x92, 0x13, 0x27, 0x8b, 0xf7, 0x6e, 0x3d, 0x77, 0x98, 0x07,
	0x8a, 0xd6, 0x8c, 0x2a, 0x8d, 0x27, 0x69, 0xe6, 0x25, 0x26, 0xe9, 0x2e, 0xd4, 0x62, 0x76, 0xd9,
	0x22, 0x94, 0x63, 0x96, 0xca, 0xae, 0xc3, 0x18, 0xcc, 0x7a, 0x28, 0x55, 0x65, 0x5a, 0x3d, 0xfa,
	0x36, 0xfe, 0x65, 0x06, 0xaa, 0x51, 0x23, 0x89, 0x0a, 0xb5, 0xa2, 0x0a, 0x08, 0x73, 0x6d, 0xdf,
	0x23, 0xf1, 0xa8, 0x99, 0xf4, 0x8d, 0x83, 0x3f, 0xe9, 0x0f, 0xb5, 0x54, 0xe0, 0x27, 0xb6, 0x74,
	0xd2, 0xd7, 0xa2, 0x5e, 0x3e, 0xe9, 0xb3, 0x75, 0x98, 0xb7, 0x43, 0x6e, 0x49, 0x3f, 0x5c, 0xaf,
	0xdc, 0x2c, 0xdd, 0x9e, 0x33, 0xa3, 0x62, 0x4a, 0x74, 0xe6, 0x53, 0xa2, 0x83, 0xb2, 0x2a, 0x6c,
	0xee, 0xf1, 0x9e, 0xc3, 0x85, 0xbd, 0x5e, 0xa5, 0xc6, 0x6a, 0x04, 0xd9, 0xe6, 0xc2, 0x66, 0x6b,
	0x50, 0x09, 0x42, 0x7f, 0x18, 0xc8, 0xf5, 0x9a, 0xda, 0x52, 0xaa, 0x94, 0x14, 0x01, 0x48, 0xc9,
	0xf0, 0x3b, 0x50, 0x1f, 0x09, 0x1e, 0xf6, 0x82, 0xd1, 0xc9, 0xc0, 0xb5, 0xd7, 0xeb, 0xc4, 0x08,
	0x20, 0xe8, 0x90, 0x20, 0xd4, 0xe1, 0xa5, 0x88, 0xf0, 0x0b, 0x84, 0xaf, 0x89, 0x4b, 0xa1, 0xd1,
	0xef, 0x40, 0x9d, 0xb8, 0xe6, 0x3d, 0xe9, 0x0e, 0xf9, 0x7a, 0xe3, 0x66, 0xe9, 0xf6, 0x8c, 0x09,
	0x0a, 0xd4, 0x75, 0x87, 0x1c, 0xc7, 0x42, 0x1d, 0x78, 0xa3, 0xe1, 0xfa, 0xa2, 0x1a, 0x0b, 0x96,
	0xf7, 0x47, 0x43, 0xd6, 0x82, 0x6a, 0x3f, 0xe4, 0x5c, 0xba, 0x5e, 0x7f, 0x7d, 0x49, 0xed, 0x9e,
	0xa8, 0x8c, 0x38, 0x31, 0x3a, 0x91, 0xae, 0x1c, 0xf0, 0xf5, 0xa6, 0xc2, 0x45, 0xe5, 0xb8, 0x4f,
	0x3f, 0xec, 0xa1, 0x7c, 0x2d, 0x53, 0xab, 0xa0, 0x41, 0xc7, 0xae, 0x63, 0x3c, 0x83, 0xc5, 0x1d,
	0x2e, 0x0f, 0x46, 0xf2, 0x4d, 0x6f, 0x7a, 0xe3, 0xc7, 0x50, 0x8d, 0xba, 0x4d, 0xad, 0x62, 0x29,
	0xbd, 0x8a, 0xb1, 0x1c, 0x97, 0x5f, 0x42, 0x8e, 0x7f, 0x96, 0x1e, 0xd5, 0xab, 0xef, 0xd5, 0xe9,
	0xba, 0x8f, 0x3b, 0xd2, 0xdd, 0xff, 0x7b, 0x09, 0xae, 0xee, 0x70, 0xef, 0x91, 0xef, 0xda, 0xbc,
	0x7d, 0x66, 0xc9, 0x1d, 0xbd, 0x54, 0xaf, 0x77, 0x7a, 0xaf, 0x41, 0xf5, 0x02, 0x7b, 0x18, 0x4f,
	0xee, 0x3c, 0x95, 0x95, 0xe8, 0x2b, 0xd4, 0xe7, 0xc2, 0xf7, 0xf4, 0x3e, 0xaa, 0x11, 0xe4, 0x07,
	0xc2, 0xf7, 0x70, 0x13, 0x4a, 0xfe, 0x4c, 0xd2, 0x5e, 0xaa, 0x99, 0xf4, 0x4d, 0xc2, 0xcb, 0x85,
	0x70, 0x7d, 0x2f, 0xda, 0x4a, 0xb8, 0x5b, 0x14, 0x64, 0xd7, 0x31, 0x3e, 0x83, 0xf5, 0xfc, 0x11,
	0x89, 0x80, 0x86, 0x14, 0x0e, 0xf4, 0xc6, 0xc7, 0x4f, 0xdc, 0xaf, 0x82, 0xdb, 0xbe, 0xe7, 0x08,
	0x1a, 0xcf, 0x8c, 0x19, 0x15, 0x69, 0x27, 0xfb, 0x9e, 0xe4, 0x9e, 0xd4, 0x2a, 0x20, 0x2a, 0xe2,
	0xa4, 0xad, 0xed, 0x70, 0x79, 0x24, 0xfd, 0xf0, 0xf2, 0xbe, 0xef, 0x9f, 0x3f, 0x70, 0x05, 0x7e,
	0xbe, 0xf6, 0x39, 0xa3, 0x56, 0x13, 0x73, 0x46, 0x65, 0x35, 0x67, 0xf6, 0x99, 0x15, 0x48, 0xd5,
	0xa4, 0x9e, 0x33, 0x0d, 0xd9, 0x75, 0xd8, 0x0d, 0xa8, 0x0f, 0xad, 0x67, 0x3d, 0xc1, 0x3d, 0xd9,
	0xb3, 0xd4, 0xd4, 0xcd, 0x98, 0xb5, 0xa1, 0xf5, 0xec, 0x88, 0x7b, 0x72, 0x4b, 0xe2, 0x9c, 0x0a,
	0xf7, 0x0f, 0xb9, 0x56, 0x42, 0xf4, 0x6d, 0xfc, 0x6d, 0x09, 0xaa, 0x34, 0x9e, 0x3d, 0xd1, 0xa7,
	0x49, 0xbf, 0x0c, 0xb8, 0x1e, 0x05, 0x7d, 0x27, 0x67, 0xa3, 0x9c, 0x9a, 0x0d, 0x1c, 0x32, 0xae,
	0x10, 0x72, 0xbf, 0x60, 0xe2, 0x27, 0x8e, 0x29, 0xea, 0x7c, 0x96, 0x3a, 0xaf, 0x08, 0xd5, 0xf3,
	0x15, 0xa8, 0x0c, 0x45, 0x7f, 0xcc, 0xf4, 0xdc, 0x50, 0xf4, 0x77, 0x1d, 0xd4, 0x6f, 0x82, 0x7b,
	0x0e, 0x0f, 0xf5, 0x32, 0xeb, 0x92, 0xd1, 0x47, 0xd9, 0xcc, 0x99, 0x66, 0x11, 0xb0, 0x6f, 0x41,
	0x15, 0x5b, 0x1a, 0xb8, 0x42, 0x3e, 0xf7, 0x6c, 0x8d, 0xc6, 0x64, 0xce, 0x0f, 0x45, 0xff, 0xa1,
	0x2b, 0xc6, 0xa3, 0x2f, 0x27, 0x46, 0xff, 0xeb, 0x12, 0xac, 0x26, 0x7b, 0x32, 0x79, 0x30, 0xf8,
	0xa2, 0x2c, 0xa7, 0xf1, 0x8f, 0x33, 0x70, 0x25, 0x87, 0x39, 0x11, 0x64, 0x2a, 0x96, 0xb2, 0x72,
	0x80, 0x5d, 0xd2, 0xa9, 0xa2, 0x19, 0xc5, 0x2e, 0xb1, 0xac, 0xd8, 0xf4, 0x7c, 0x27, 0x66, 0xb3,
	0x66, 0x56, 0xb0, 0xa8, 0x96, 0xc2, 0xba, 0xb0, 0xa4, 0x15, 0x6a, 0x26, 0x75, 0x29, 0x3e, 0x20,
	0xe7, 0x12, 0x07, 0xe4, 0x4f, 0xa0, 0x11, 0x22, 0x2f, 0x3d, 0x3f, 0x90, 0xae, 0xef, 0x89, 0xf5,
	0x0a, 0x2d, 0xc4, 0xc7, 0xf9, 0xfa, 0x2a, 0x6f, 0x04, 0x77, 0xe8, 0xeb, 0x80, 0xaa, 0x9b, 0x0b,
	0xe1, 0xb8, 0x20, 0xd8, 0x1d, 0x58, 0xe1, 0x9e, 0x75, 0x32, 0xe0, 0x3d, 0x7b, 0x24, 0xa4, 0x3f,
	0xec, 0x11, 0x96, 0x64, 0xb6, 0x6a, 0x2e, 0x2b, 0x54, 0x9b, 0x30, 0xd4, 0x06, 0xdb, 0x84, 0x65,
	0x4d, 0xa8, 0xe5, 0x12, 0xc7, 0xa6, 0x4e, 0xd2, 0x25, 0x85, 0x68, 0x2b, 0xf8, 0xae, 0xc3, 0x6e,
	0x43, 0xd3, 0x15, 0xb8, 0x3f, 0x9c, 0x9e, 0xe3, 0x86, 0xdc, 0x96, 0x83, 0x4b, 0x3a, 0x59, 0xab,
	0xe6, 0xa2, 0x2b, 0x8e, 0xb8, 0xe7, 0x6c, 0x6b, 0x68, 0xeb, 0x13, 0xa8, 0x27, 0x58, 0x9c, 0x30,
	0x1b, 0x0a, 0x37, 0x85, 0xf1, 0xf3, 0xb4, 0xe8, 0x1e, 0x86, 0x7e, 0x3f, 0xe4, 0xe2, 0xc5, 0xa6,
	0xdc, 0xeb, 0x12, 0x9a, 0xff, 0x2a, 0xa3, 0x16, 0xcc, 0x63, 0x40, 0x04, 0x13, 0xc3, 0x58, 0x85,
	0x39, 0x75, 0x26, 0xab, 0x41, 0xa8, 0x02, 0xed, 0x4a, 0x69, 0xc9, 0x91, 0x88, 0x98, 0x52, 0xa5,
	0x4c, 0xcf, 0xb3, 0x59, 0xa9, 0x7b, 0x17, 0x16, 0x22, 0x74, 0x42, 0x62, 0xea, 0x1a, 0xb6, 0x8f,
	0x82, 0xf3, 0x25, 0x58, 0x8c, 0x48, 0x74, 0x0f, 0x15, 0xea, 0xa1, 0xa1, 0xa1, 0x47, 0xaa, 0xa3,
	0xa4, 0xfc, 0xce, 0xa7, 0xe5, 0x77, 0x15, 0xe6, 0xe8, 0x53, 0xaf, 0xb0, 0x2a, 0x60, 0xbb, 0x27,
	0x96, 0x7d, 0xde, 0x0f, 0xfd, 0x91, 0xe7, 0xf4, 0x50, 0xd1, 0x2b, 0x7b, 0xa9, 0x31, 0x86, 0x1e,
	0x87, 0x03, 0xf6, 0x35, 0x58, 0x4d, 0x90, 0x0d, 0x47, 0xc2, 0xb5, 0x89, 0x18, 0x88, 0x98, 0x8d,
	0x71, 0x7b, 0x88, 0xc2, 0x1a, 0xd8, 0x9d, 0xbc, 0x1c, 0x70, 0xb2, 0xa4, 0x1a, 0xa6, 0x2a, 0xa0,
	0x71, 0xbc, 0x7c, 0x24, 0xad, 0x30, 0x29, 0xd8, 0x5f, 0x0c, 0x95, 0x71, 0x0b, 0x58, 0x96, 0x31,
	0xb5, 0xec, 0xfe, 0x39, 0x31, 0x56, 0x35, 0xcb, 0xfe, 0xb9, 0x61, 0xc1, 0xb5, 0x3d, 0x2b, 0x3c,
	0x8f, 0x89, 0x94, 0x81, 0x7f, 0xc4, 0x9f, 0xbc, 0xbc, 0x94, 0x0e, 0xad, 0xf0, 0xbc, 0x27, 0xf8,
	0x13, 0xe2, 0xb3, 0x61, 0xce, 0x63, 0xf9, 0x88, 0x3f, 0x31, 0xbe, 0x0a, 0xad, 0xa2, 0x2e, 0x72,
	0x18, 0xea, 0xa4, 0x65, 0xf6, 0x15, 0xf9, 0x31, 0x9e, 0xc2, 0xb5, 0x82, 0x66, 0x44, 0x80, 0xcc,
	0x0e, 0x2c, 0x21, 0x89, 0x59, 0x6d, 0xbf, 0x61, 0xf9, 0x88, 0x3f, 0x61, 0x1b, 0x50, 0x13, 0x67,
	0xfe, 0xd3, 0x1e, 0x9d, 0x7d, 0x6a, 0x99, 0xaa, 0x08, 0xe8, 0xe2, 0xf9, 0x67, 0x40, 0x83, 0xea,
	0x85, 0xdc, 0x72, 0xa8, 0xb2, 0xea, 0xb3, 0x8e, 0x40, 0x93, 0x5b, 0x0e, 0x8e, 0xf6, 0x31, 0x2c,
	0xa5, 0xd5, 0xdc, 0x6b, 0xda, 0xec, 0xc6, 0x03, 0xba, 0xd2, 0xa5, 0x57, 0xf3, 0x9b, 0x28, 0x93,
	0x7e, 0x78, 0xa9, 0xaf, 0xce, 0x37, 0x8a, 0x8f, 0x3f, 0xaa, 0xa2, 0x88, 0x8d, 0xef, 0xc1, 0x4a,
	0xb2, 0x25, 0x3c, 0x11, 0x5f, 0x72, 0x76, 0x4f, 0xd3, 0x47, 0xa5, 0x6a, 0x81, 0x4e, 0x64, 0x62,
	0xd7, 0xe5, 0xcf, 0xb7, 0x5b, 0xc7, 0x1c, 0x45, 0xe4, 0xa4, 0x7e, 0x7c, 0x69, 0x0d, 0xf4, 0x9c,
	0xab, 0x82, 0xf1, 0xaf, 0x65, 0xa8, 0xc5, 0xc4, 0x13, 0x2a, 0x0b, 0x6f, 0x0b, 0xfe, 0x05, 0x8a,
	0xff, 0xd0, 0xea, 0x47, 0x8a, 0x0b, 0x08, 0xb4, 0x8b, 0x90, 0xb1, 0x4e, 0x9b, 0x49, 0xea, 0xb4,
	0xe4, 0x05, 0x64, 0x36, 0x73, 0x01, 0x19, 0xeb, 0xbb, 0xb9, 0x94, 0xbe, 0x3b, 0x80, 0xe6, 0xc8,
	0x1b, 0xf8, 0xf6, 0x39, 0x9e, 0x2c, 0x8e, 0x8b, 0x07, 0x81, 0x3e, 0xe9, 0x6e, 0x15, 0x8f, 0xf0,
	0x98, 0x6a, 0xb4, 0x7d, 0xcf, 0x31, 0x97, 0x46, 0xf1, 0x37, 0x55, 0x66, 0xdf, 0x85, 0xaa, 0xde,
	0xaa, 0x62, 0x7d, 0x9e, 0x1a, 0x7a, 0xb7, 0xb8, 0xa1, 0xb6, 0xa2, 0x34, 0xe3, 0x2a, 0x28, 0xa6,
	0xa3, 0xc0, 0xa1, 0xcb, 0x99, 0x20, 0xfd, 0x37, 0x63, 0x56, 0x15, 0xa0, 0x2b, 0xc6, 0x9a, 0xaa,
	0x96, 0xd4, 0x54, 0xbf, 0x29, 0xc1, 0x42, 0xb2, 0xb5, 0xa9, 0xee, 0xbf, 0x45, 0xfa, 0x3f, 0x6f,
	0x3e, 0x66, 0xff, 0x0f, 0xf3, 0x61, 0x7c, 0x02, 0x4b, 0x19, 0x1a, 0x94, 0xc7, 0x0b, 0x4b, 0xd9,
	0xe9, 0x33, 0x26, 0x7e, 0xc6, 0x36, 0x69, 0x79, 0x6c, 0x93, 0x1a, 0x4f, 0x70, 0x54, 0x56, 0x48,
	0x37, 0x9a, 0xd7, 0xab, 0x7a, 0x13, 0x5e, 0xac, 0xd9, 0x94, 0x17, 0xeb, 0x7d, 0x68, 0x24, 0xba,
	0x14, 0x01, 0x9a, 0xb4, 0xb6, 0x7c, 0x36, 0xb6, 0xbf, 0xe6, 0x6c, 0xf9, 0x6c, 0xd7, 0x31, 0xee,
	0x03, 0xfb, 0xbe, 0x1f, 0x0e, 0x2d, 0xb9, 0x13, 0xc8, 0x2d, 0x4f, 0x3c, 0xe5, 0x21, 0x32, 0xb8,
	0x02, 0x73, 0xfd, 0x40, 0xf6, 0x2c, 0x4d, 0x3b, 0xdb, 0x0f, 0xe4, 0x56, 0xb1, 0x23, 0xe7, 0x10,
	0x56, 0x26, 0xda, 0x10, 0x01, 0xfb, 0xf6, 0x84, 0xe9, 0xab, 0x77, 0x5a, 0xc6, 0x1d, 0xa8, 0xea,
	0x26, 0x6d, 0xdf, 0xc8, 0xb3, 0x74, 0xd8, 0x35, 0xf9, 0x93, 0x62, 0xc7, 0x60, 0x21, 0x43, 0x7f,
	0x5a, 0x82, 0xe5, 0x4c, 0x7d, 0x11, 0xb0, 0x2f, 0xc3, 0x52, 0x30, 0xb0, 0x6c, 0x7e, 0xe6, 0x0f,
	0x1c, 0x3a, 0xd1, 0x43, 0x3d, 0xbc, 0xc5, 0x04, 0xf8, 0x48, 0x86, 0x28, 0xb8, 0xca, 0x71, 0x31,
	0x36, 0x48, 0xab, 0x0a, 0xb0, 0xeb, 0xe0, 0xd9, 0xad, 0x91, 0x17, 0x3c, 0xc4, 0x9b, 0x9c, 0xde,
	0xb8, 0x0d, 0x05, 0x7d, 0xa4, 0x80, 0xc6, 0x77, 0xe0, 0xca, 0x7d, 0x4b, 0xda, 0x67, 0x3b, 0x5c,
	0xb6, 0x23, 0x09, 0x2b, 0xb0, 0xaa, 0x9a, 0x30, 0xe3, 0x3a, 0xea, 0x36, 0xdc, 0x30, 0xf1, 0xd3,
	0xf8, 0x8b, 0x32, 0x34, 0x70, 0xe1, 0xe2, 0x9a, 0x2f, 0x2f, 0x31, 0x19, 0xcf, 0xc8, 0xcc, 0x84,
	0x67, 0xe4, 0x16, 0x2c, 0x92, 0x67, 0x04, 0x97, 0xc8, 0xf6, 0x47, 0x9e, 0xd4, 0x47, 0xe2, 0x02,
	0x42, 0xf7, 0x44, 0xbf, 0x8d, 0xb0, 0xe4, 0x14, 0xcf, 0xa5, 0x04, 0xef, 0x3a, 0x00, 0x21, 0xf8,
	0x33, 0x19, 0x5a, 0x64, 0x16, 0x2d, 0x98, 0x35, 0x84, 0x74, 0x10, 0xc0, 0x3a, 0xd0, 0xd0, 0x56,
	0x2e, 0x5d, 0x91, 0x23, 0xfd, 0x71, 0x33, 0x77, 0xe3, 0x29, 0xf3, 0x98, 0x2e, 0xc2, 0xe6, 0x82,
	0x3d, 0x2e, 0x08, 0x83, 0xc3, 0x5a, 0xde, 0x2c, 0x8a, 0x80, 0xfd, 0x10, 0x96, 0xd2, 0xad, 0x44,
	0xda, 0xdc, 0x28, 0xf4, 0x42, 0xc4, 0x2d, 0x98, 0x8b, 0xfd, 0x64, 0x51, 0x18, 0x06, 0xf9, 0x36,
	0x8e, 0x05, 0x0f, 0x0b, 0x85, 0xcd, 0x10, 0x74, 0x66, 0x8e, 0x69, 0x44, 0xc0, 0xee, 0xc2, 0x2a,
	0x4d, 0xa1, 0xf4, 0x1d, 0xeb, 0x32, 0x31, 0x91, 0xaa, 0xd6, 0x32, 0xe2, 0xba, 0x88, 0x8a, 0x67,
	0x73, 0x5c, 0x41, 0x5a, 0x83, 0x44, 0x85, 0x72, 0xb2, 0x82, 0xb4, 0x06, 0x51, 0x05, 0xe3, 0xb7,
	0x65, 0x60, 0x64, 0xd7, 0x77, 0xf9, 0x33, 0xa9, 0xf6, 0x09, 0x72, 0x17, 0x39, 0x1b, 0x4a, 0x09,
	0x67, 0xc3, 0x11, 0xd4, 0x4f, 0x89, 0x60, 0x6c, 0x16, 0x2c, 0xde, 0xbb, 0x97, 0x3b, 0x19, 0x93,
	0x2d, 0xea, 0x3d, 0x88, 0x06, 0x84, 0x09, 0xa7, 0xf1, 0xb7, 0xf1, 0xdb, 0x12, 0xc0, 0x18, 0xc5,
	0xd6, 0x22, 0x65, 0x81, 0xa5, 0xde, 0x36, 0x3f, 0xb5, 0x46, 0x03, 0xd9, 0x7c, 0x8b, 0xdd, 0x80,
	0x56, 0x02, 0x8e, 0x2d, 0x1f, 0x86, 0xbe, 0x1d, 0xe1, 0x4b, 0xec, 0x6d, 0x58, 0x4f, 0xe0, 0x4d,
	0x3e, 0xf4, 0x2f, 0xf8, 0xfd, 0xd0, 0xb2, 0xcf, 0xb9, 0x6c, 0x96, 0xd9, 0x35, 0xb8, 0x92, 0x53,
	0xfb, 0xd1, 0xd7, 0x9b, 0x33, 0x99, 0x86, 0x1f, 0xfa, 0x5e, 0x9f, 0x78, 0xdf, 0xe6, 0x92, 0xdb,
	0xb2, 0x39, 0x6b, 0x7c, 0x06, 0x2b, 0x13, 0x83, 0x11, 0x01, 0x9d, 0xa9, 0xfc, 0x99, 0x54, 0x22,
	0x81, 0x67, 0x2a, 0x16, 0x70, 0x4b, 0x38, 0x7c, 0x60, 0x5d, 0xd2, 0x8e, 0x50, 0xfb, 0x6d, 0xc6,
	0x04, 0x02, 0xe1, 0x8e, 0x10, 0x38, 0xad, 0x8e, 0x25, 0xad, 0xc8, 0x91, 0x8a, 0xdf, 0xc6, 0x0f,
	0x80, 0x6d, 0xf3, 0x01, 0x97, 0x09, 0xe9, 0xc9, 0xdd, 0xc4, 0x06, 0x34, 0x2c, 0xb7, 0x37, 0xb1,
	0x23, 0xeb, 0x96, 0x7b, 0x18, 0xed, 0x49, 0xe3, 0x0a, 0xac, 0x4c, 0xb4, 0x25, 0x02, 0xe3, 0x23,
	0xa8, 0x27, 0x76, 0xc0, 0xc4, 0x91, 0xb7, 0x06, 0x95, 0xa7, 0xdc, 0xed, 0x9f, 0x29, 0x31, 0x29,
	0x9b, 0xba, 0x64, 0xfc, 0x43, 0x19, 0x56, 0x8e, 0x12, 0xfb, 0xa2, 0x58, 0x4f, 0x4e, 0xc1, 0x1b,
	0xbb, 0x0b, 0xb3, 0xb8, 0x7b, 0x69, 0xec, 0xf5, 0x7b, 0x1b, 0xb9, 0x72, 0xb3, 0xb5, 0x6b, 0xfa,
	0x03, 0x6e, 0x12, 0x21, 0x9d, 0xba, 0xfe, 0x28, 0xb4, 0xb9, 0xd6, 0x1b, 0xba, 0x94, 0x7b, 0x01,
	0xdf, 0x80, 0x9a, 0x6d, 0x0d, 0x06, 0xea, 0x9e, 0xa5, 0x5c, 0x27, 0x55, 0x04, 0xec, 0x6b, 0x24,
	0x69, 0x12, 0x12, 0x5b, 0xe5, 0xea, 0xa9, 0x22, 0x80, 0x24, 0x6e, 0x42, 0x8f, 0x54, 0x5f, 0x49,
	0x8f, 0x7c, 0x0f, 0x2a, 0x8a, 0xf9, 0x09, 0x0f, 0x7c, 0x6c, 0x87, 0xe8, 0x2b, 0x25, 0x15, 0x70,
	0x0e, 0x05, 0x7f, 0x46, 0x93, 0x31, 0x67, 0xe2, 0xa7, 0xb1, 0x06, 0xab, 0x93, 0x93, 0x2d, 0x02,
	0xe3, 0x9f, 0xca, 0x70, 0xc5, 0xe4, 0x36, 0x77, 0x2f, 0xf8, 0x9e, 0xe8, 0x7f, 0x3f, 0xf4, 0x87,
	0xa8, 0x22, 0x5e, 0xe9, 0x90, 0xdf, 0x53, 0xe7, 0x25, 0xcd, 0xc3, 0xcc, 0x73, 0xb7, 0x6f, 0x4e,
	0x77, 0x77, 0xf6, 0x44, 0x9f, 0xb6, 0x2f, 0x9e, 0xa1, 0xdd, 0x8c, 0x23, 0x6c, 0x36, 0xd7, 0x11,
	0x36, 0x37, 0x76, 0x84, 0x8d, 0xfd, 0x5d, 0x95, 0x84, 0xbf, 0xcb, 0x78, 0x0c, 0xf3, 0xba, 0x59,
	0xb6, 0x02, 0x4b, 0xfa, 0xb3, 0x77, 0xec, 0x9d, 0x7b, 0xfe, 0x53, 0xaf, 0xf9, 0x16, 0x6b, 0xc2,
	0x42, 0x04, 0xc4, 0x1d, 0xd8, 0x2c, 0xb1, 0x55, 0x68, 0x46, 0x90, 0xce, 0xd0, 0x97, 0xae, 0xed,
	0x7b, 0xcd, 0x19, 0xb6, 0x04, 0xf5, 0x18, 0xfa, 0x4c, 0x36, 0x2b, 0xc6, 0x3a, 0xac, 0xe5, 0x8d,
	0x42, 0x04, 0x86, 0x09, 0x2d, 0xfc, 0xee, 0x78, 0x92, 0x87, 0xed, 0x33, 0x4b, 0x4a, 0xd7, 0xeb,
	0xab, 0x8b, 0xd1, 0xab, 0xef, 0xbb, 0xeb, 0xb0, 0x51, 0xd8, 0xa6, 0x08, 0x8c, 0xff, 0x9e, 0x85,
	0xf9, 0x2e, 0x2f, 0xba, 0x5f, 0x4c, 0xb3, 0x79, 0xbe, 0x03, 0x15, 0xcb, 0x96, 0x91, 0x2d, 0xb0,
	0x78, 0xef, 0xbd, 0xdc, 0x75, 0xd3, 0x7d, 0xdc, 0xd9, 0x22, 0x52, 0x53, 0x57, 0x61, 0x1f, 0xc3,
	0x3a, 0xc9, 0x7f, 0x14, 0x54, 0x08, 0x46, 0xe2, 0xac, 0x87, 0x07, 0x8a, 0x14, 0xda, 0x2b, 0xb9,
	0x8a, 0xf8, 0xb6, 0x42, 0x1f, 0x8e, 0xc4, 0xd9, 0xb6, 0x75, 0xd9, 0x15, 0xec, 0x3e, 0xdc, 0x48,
	0xd5, 0x1b, 0xba, 0x1e, 0x76, 0x26, 0x25, 0x77, 0xf4, 0xb1, 0xa2, 0x4e, 0xec, 0x56, 0xa2, 0xf6,
	0x9e, 0xeb, 0xb5, 0x15, 0xc9, 0xc4, 0xf1, 0x5e, 0x49, 0x1d, 0xef, 0xb7, 0xa1, 0x99, 0x6a, 0x7c,
	0x14, 0xc7, 0x82, 0x16, 0x13, 0xcd, 0x1d, 0xeb, 0x0b, 0x8e, 0xee, 0x75, 0xa4, 0x3d, 0x59, 0x0d,
	0x13, 0x34, 0xe8, 0x58, 0x59, 0x69, 0x58, 0xc2, 0xe1, 0xd4, 0x94, 0x93, 0x15, 0x8b, 0x5d, 0x72,
	0x9b, 0x58, 0x22, 0xec, 0xd1, 0x49, 0xa6, 0x5c, 0x1a, 0xf3, 0x96, 0x08, 0x51, 0x90, 0x8c, 0x7f,
	0x2b, 0x41, 0x45, 0x4d, 0x13, 0x63, 0xb0, 0xa8, 0xbe, 0xd2, 0x72, 0xa7, 0x61, 0xa8, 0xb8, 0xc3,
	0x66, 0x09, 0xc5, 0x53, 0x43, 0x22, 0x37, 0x7a, 0xb3, 0x8c, 0xc7, 0x8e, 0x06, 0x9a, 0x63, 0x9e,
	0xd5, 0xc2, 0xab, 0xb3, 0x45, 0x63, 0x77, 0x3d, 0x9b, 0x08, 0x12, 0x33, 0xd3, 0x9c, 0x65, 0xd7,
	0xe1, 0x5a, 0xa2, 0xf6, 0x43, 0xf7, 0x9c, 0xab, 0xaa, 0xb8, 0x02, 0xcd, 0x39, 0xb6, 0x0c, 0x0d,
	0x8d, 0x3e, 0xe4, 0x68, 0xbc, 0x36, 0x2b, 0xac, 0x05, 0x6b, 0x1a, 0x74, 0xc4, 0x07, 0xa7, 0x48,
	0x77, 0x18, 0x72, 0xc7, 0xb5, 0x65, 0x73, 0xde, 0xb8, 0x01, 0x55, 0xb5, 0xfe, 0x22, 0x88, 0xcf,
	0x99, 0x52, 0xe2, 0x9c, 0xf9, 0x0c, 0x6f, 0xab, 0x9e, 0xc9, 0xd1, 0x26, 0xe6, 0x9e, 0xf3, 0xfa,
	0x1d, 0xbb, 0xc6, 0xc7, 0x70, 0x25, 0xa7, 0x07, 0xe5, 0x9d, 0x55, 0xee, 0xd1, 0xd8, 0x52, 0xaf,
	0x99, 0x35, 0x82, 0x90, 0x2d, 0xfe, 0x19, 0xac, 0x1e, 0x9d, 0xf9, 0x4f, 0xb5, 0xb4, 0x1f, 0xb9,
	0x03, 0xee, 0xc9, 0xd7, 0xcb, 0xd9, 0x66, 0x5e, 0x0f, 0x6a, 0x9e, 0xc4, 0x99, 0xff, 0x54, 0x3b,
	0x5e, 0xe8, 0xdb, 0xf8, 0x19, 0xac, 0x20, 0x6d, 0xdb, 0xf7, 0xa4, 0xeb, 0x8d, 0x68, 0xcd, 0xde,
	0x64, 0x84, 0xed, 0x9f, 0x4b, 0x39, 0xfd, 0xe7, 0xb3, 0x8a, 0x9a, 0x83, 0x04, 0x7f, 0xc0, 0x4f,
	0x25, 0x05, 0x20, 0xb5, 0xe6, 0x40, 0xe0, 0x43, 0x7e, 0x2a, 0xf7, 0x47, 0x43, 0xf6, 0x11, 0x5c,
	0xb5, 0x75, 0x53, 0x3d, 0xe5, 0xe6, 0x8b, 0xa9, 0x15, 0x5b, 0xab, 0x11, 0xda, 0x44, 0x6c, 0x5e,
	0x35, 0x65, 0x4c, 0xc6, 0xd5, 0x66, 0xd3, 0xd5, 0xc8, 0x9e, 0xd4, 0xd5, 0x8c, 0x9f, 0xc0, 0xd2,
	0xef, 0x6c, 0xe2, 0x8c, 0xe5, 0x4c, 0xe3, 0x22, 0x30, 0x02, 0x60, 0x24, 0xf4, 0xfe, 0x80, 0x1f,
	0x71, 0xcf, 0xc1, 0x2b, 0xde, 0x6b, 0x5d, 0xab, 0x26, 0xcc, 0x0c, 0x45, 0x3f, 0x8a, 0x72, 0x0f,
	0x45, 0xdf, 0x58, 0x9d, 0xec, 0x51, 0x04, 0xc6, 0x1f, 0xd1, 0x75, 0xb0, 0x6b, 0x85, 0xbe, 0x6c,
	0xfb, 0xde, 0xa9, 0xdb, 0x7f, 0x93, 0x22, 0xf3, 0x8b, 0x12, 0xd4, 0x54, 0xd7, 0x56, 0xe8, 0x4c,
	0x93, 0x01, 0x80, 0x27, 0x77, 0xe0, 0xda, 0x72, 0x14, 0x46, 0x4e, 0xa1, 0xa8, 0x18, 0x1b, 0xfe,
	0xb3, 0x09, 0xc3, 0xff, 0x3d, 0x68, 0xd8, 0xd6, 0x80, 0x7b, 0x8e, 0x15, 0xf6, 0x28, 0x0f, 0x40,
	0x59, 0x5e, 0x0b, 0x11, 0x70, 0xd7, 0xf6, 0x3d, 0x63, 0x38, 0x31, 0x03, 0x22, 0x60, 0xdf, 0x41,
	0xb3, 0x2c, 0x74, 0x72, 0x6e, 0xe8, 0xd9, 0x93, 0x2b, 0x62, 0x1f, 0xcd, 0xb6, 0xd0, 0xa1, 0xf0,
	0xd4, 0x06, 0xd4, 0x04, 0x0f, 0x2f, 0xf0, 0x36, 0xa3, 0x22, 0x92, 0xb3, 0x66, 0x55, 0x01, 0xba,
	0xc2, 0xf8, 0xeb, 0x12, 0x34, 0x0f, 0x02, 0xee, 0x8d, 0x2b, 0xbe, 0xc1, 0xd4, 0x17, 0xa5, 0x5e,
	0x63, 0x0b, 0x14, 0xbf, 0x8d, 0xf3, 0x2c, 0x3f, 0xe4, 0x08, 0xac, 0x84, 0x5c, 0x8c, 0x06, 0x52,
	0x7b, 0x26, 0x6f, 0x16, 0x8f, 0xdd, 0x24, 0x3a, 0x53, 0xd3, 0x23, 0xdf, 0xa7, 0x6e, 0x28, 0x64,
	0xcf, 0x0f, 0xb8, 0x47, 0x7c, 0x57, 0xcd, 0x1a, 0x41, 0xb0, 0x13, 0xe3, 0xef, 0x4b, 0xe3, 0xd9,
	0xd6, 0x35, 0xdf, 0xe0, 0xf0, 0x57, 0x61, 0x6e, 0xe8, 0x7b, 0xf2, 0x2c, 0x0e, 0x5d, 0x62, 0x01,
	0x7b, 0x77, 0xac, 0x4b, 0x6d, 0xde, 0xe1, 0xa7, 0xb1, 0x0f, 0xf5, 0x04, 0x87, 0xf1, 0xac, 0x8d,
	0x0f, 0xa5, 0xc8, 0x58, 0x2e, 0xc7, 0xc2, 0xba, 0x01, 0x35, 0x1c, 0xf1, 0xd8, 0xa5, 0x50, 0x33,
	0xab, 0x08, 0xc0, 0x53, 0xd8, 0x78, 0x34, 0x31, 0x68, 0x11, 0xb0, 0x2d, 0xa8, 0xab, 0x39, 0x4b,
	0x0a, 0xd9, 0x8b, 0x27, 0x1a, 0x54, 0x25, 0x3a, 0x7f, 0x2e, 0xe9, 0xe2, 0x7d, 0xc8, 0xe5, 0xc1,
	0x53, 0x6f, 0xdb, 0xba, 0x7c, 0x93, 0x5b, 0x77, 0x33, 0xd3, 0xb5, 0x08, 0xb0, 0x25, 0xff, 0xa9,
	0x87, 0xf6, 0x99, 0xee, 0xbe, 0xe2, 0x13, 0xce, 0xf8, 0x65, 0x09, 0x96, 0x51, 0xf5, 0x1c, 0x72,
	0xf9, 0x5c, 0x5d, 0xb7, 0x0e, 0x68, 0xa1, 0x27, 0x5c, 0x84, 0x51, 0x11, 0xc7, 0x70, 0x32, 0x3a,
	0x39, 0x19, 0xf0, 0x1e, 0x2a, 0x35, 0x35, 0xbd, 0x35, 0x05, 0xd9, 0x13, 0x7d, 0xba, 0xbe, 0xba,
	0x22, 0x18, 0x28, 0x57, 0x83, 0xe6, 0x15, 0x34, 0x08, 0x09, 0x62, 0x4f, 0xf2, 0x5c, 0xc2, 0x93,
	0x6c, 0xac, 0x4c, 0xb0, 0x15, 0x2b, 0xc4, 0x43, 0xed, 0x1e, 0xdf, 0x1f, 0x0d, 0xdf, 0xe4, 0xac,
	0x7e, 0x77, 0xa2, 0x73, 0x7d, 0x65, 0x27, 0xdf, 0x7a, 0x29, 0xe1, 0x5b, 0x47, 0xa1, 0xc4, 0x83,
	0x2d, 0x72, 0xa6, 0xe2, 0xb7, 0x71, 0x0a, 0xd5, 0xa8, 0x6e, 0x2a, 0x14, 0x51, 0x4a, 0x47, 0x9e,
	0x3e, 0x85, 0xba, 0x42, 0x21, 0xbf, 0x51, 0xae, 0xc9, 0x73, 0x5c, 0xfd, 0x68, 0x7d, 0x98, 0x20,
	0xa2, 0x4f, 0x61, 0x7c, 0xae, 0xdd, 0xfa, 0x58, 0xc2, 0x8e, 0xb0, 0x1d, 0x3a, 0x61, 0x75, 0x34,
	0x06, 0xcb, 0x78, 0x16, 0x47, 0x3a, 0xb9, 0x9c, 0xd0, 0xc9, 0xab, 0x30, 0x67, 0x8d, 0x1c, 0xd7,
	0x8f, 0x9c, 0xfa, 0x54, 0x48, 0xa6, 0x70, 0xe8, 0xf0, 0x93, 0x2e, 0x92, 0xbe, 0x24, 0x7b, 0x51,
	0x0f, 0x2c, 0xea, 0xf3, 0x77, 0x35, 0x38, 0x76, 0x03, 0xea, 0xae, 0xe8, 0x51, 0xa0, 0xc8, 0xf7,
	0xd4, 0x5e, 0xae, 0x9a, 0x35, 0x57, 0x3c, 0xb4, 0x84, 0x3c, 0xf0, 0x54, 0xee, 0x22, 0xf9, 0x8f,
	0x23, 0x8e, 0xde, 0x70, 0xee, 0x62, 0x3c, 0xf0, 0xb9, 0x74, 0x80, 0xe9, 0xc3, 0x2c, 0x5b, 0x2a,
	0x52, 0x56, 0x30, 0x4f, 0xc6, 0xdf, 0x95, 0xc8, 0x65, 0x1f, 0x7c, 0xc1, 0x46, 0xb1, 0x9c, 0xe1,
	0x4a, 0x04, 0xc6, 0xcf, 0x61, 0x95, 0xee, 0xa7, 0x08, 0x1b, 0x70, 0x1e, 0x74, 0xad, 0xc1, 0xf9,
	0x9b, 0xdc, 0x94, 0x6b, 0x79, 0xfd, 0x8b, 0xc0, 0xf8, 0xb5, 0x3a, 0xcb, 0x1e, 0xb9, 0x27, 0x5c,
	0xbc, 0x71, 0xdb, 0x89, 0x92, 0xae, 0xb0, 0x67, 0xe5, 0x14, 0x51, 0xf7, 0xd9, 0x1a, 0x41, 0xc8,
	0x3d, 0xf9, 0x9f, 0x25, 0xa8, 0x27, 0x38, 0x9b, 0x30, 0xae, 0x6e, 0x42, 0xdd, 0xe1, 0xc2, 0x0e,
	0x5d, 0xca, 0x8a, 0xd0, 0x3b, 0x34, 0x09, 0x22, 0x2b, 0x66, 0x68, 0x0d, 0x06, 0x14, 0x57, 0xd7,
	0x27, 0x1a, 0x01, 0x8e, 0xc3, 0x01, 0x22, 0x29, 0x6a, 0x47, 0x48, 0xcd, 0x1a, 0x01, 0x10, 0xf9,
	0x1e, 0x34, 0x42, 0xae, 0x7c, 0x5e, 0x63, 0xee, 0x6a, 0xe6, 0x42, 0x04, 0x24, 0xaf, 0xc9, 0xbb,
	0x10, 0x97, 0xa9, 0x11, 0x75, 0xfc, 0xd6, 0x23, 0x98, 0xee, 0x84, 0xb4, 0x03, 0xe1, 0x55, 0xf6,
	0x40, 0x95, 0x00, 0xc7, 0xe1, 0xc0, 0xe8, 0x4e, 0x4c, 0xbe, 0x08, 0xd8, 0xa7, 0xd1, 0xa4, 0xbc,
	0xf0, 0x48, 0x4d, 0x56, 0x54, 0xd3, 0x46, 0x27, 0xea, 0xaf, 0x4a, 0xb0, 0xb2, 0xe5, 0x38, 0x5b,
	0xb6, 0xed, 0x0f, 0x03, 0xcb, 0x23, 0xef, 0xe7, 0x9b, 0x5c, 0xd5, 0x84, 0x1e, 0x9c, 0x4b, 0xeb,
	0xc1, 0xff, 0x97, 0xc3, 0x98, 0x08, 0x70, 0xae, 0xd5, 0x2d, 0x27, 0xaa, 0xa6, 0x58, 0x5c, 0x20,
	0xe0, 0x91, 0xae, 0x8b, 0xa3, 0xda, 0xe1, 0xf2, 0x8b, 0x39, 0xaa, 0x09, 0xc6, 0xa6, 0x1d, 0xd5,
	0x2f, 0x4a, 0x00, 0x5d, 0x2b, 0xec, 0x73, 0x49, 0xf9, 0xc0, 0xdf, 0x80, 0x8a, 0xa4, 0x92, 0xb6,
	0x59, 0x37, 0x8a, 0x4c, 0xa9, 0x3e, 0x97, 0xa6, 0x26, 0x65, 0x9f, 0x42, 0x15, 0xbb, 0x1e, 0x70,
	0x9d, 0xf8, 0x5c, 0x2f, 0x72, 0x50, 0x11, 0x79, 0x5b, 0x93, 0x9a, 0x71, 0x25, 0xe3, 0x7f, 0x66,
	0xa0, 0xa2, 0x90, 0x79, 0x01, 0xd9, 0xd8, 0x96, 0x99, 0xd3, 0x29, 0x78, 0x57, 0xa0, 0x22, 0x46,
	0x27, 0xe3, 0xf9, 0x9b, 0x13, 0xa3, 0x13, 0x65, 0x98, 0xfa, 0xa1, 0xc3, 0x43, 0x7d, 0xf8, 0xa9,
	0x42, 0xbe, 0xd5, 0x92, 0x8a, 0x7f, 0x57, 0x26, 0x13, 0x70, 0x43, 0xfe, 0x14, 0x2f, 0x2e, 0xb1,
	0xcb, 0x78, 0x0e, 0x2d, 0x46, 0x04, 0x75, 0xb5, 0x21, 0xa5, 0x09, 0xf0, 0xb8, 0x56, 0x1e, 0xa9,
	0x9a, 0x82, 0xe0, 0x81, 0x9d, 0xd1, 0x0a, 0xb5, 0x49, 0xad, 0x80, 0x83, 0xf2, 0x1d, 0x9f, 0xbc,
	0x52, 0x38, 0x28, 0xdf, 0xf1, 0x29, 0x17, 0xc8, 0x1f, 0x06, 0x23, 0xc9, 0x7b, 0x43, 0x2e, 0xcf,
	0x7c, 0x47, 0x67, 0x2b, 0x37, 0x34, 0x74, 0x8f, 0x80, 0x49, 0xe1, 0x59, 0x48, 0x09, 0xcf, 0x26,
	0x2c, 0x47, 0xf3, 0xd9, 0x73, 0xfc, 0xde, 0x85, 0x35, 0x18, 0xa9, 0x84, 0xe5, 0x86, 0xb9, 0x14,
	0x21, 0xb6, 0xfd, 0x47, 0x08, 0xc6, 0x39, 0x09, 0xac, 0xd0, 0x52, 0x29, 0xcb, 0x35, 0x53, 0x15,
	0x28, 0x9d, 0x14, 0xcf, 0x3e, 0x65, 0x7e, 0x2f, 0xe9, 0x74, 0x52, 0x84, 0x44, 0xa9, 0xce, 0xdc,
	0x73, 0x14, 0x52, 0xe5, 0x2c, 0xcf, 0x73, 0xcf, 0x21, 0x54, 0x94, 0x21, 0xbe, 0x9c, 0xc8, 0x10,
	0x5f, 0x87, 0x79, 0x19, 0xba, 0xfd, 0x3e, 0x0f, 0xd7, 0x99, 0xca, 0xff, 0xd6, 0x45, 0xe3, 0x08,
	0x56, 0xd2, 0x92, 0x70, 0x48, 0xdd, 0x6f, 0x40, 0x6d, 0x68, 0x49, 0xfb, 0xac, 0x67, 0x79, 0x97,
	0xda, 0x4b, 0x54, 0x25, 0xc0, 0x96, 0x77, 0xa9, 0xd2, 0x1c, 0x3c, 0x69, 0xb9, 0x1e, 0xa1, 0xcb,
	0x84, 0x06, 0x0d, 0xda, 0xf2, 0x2e, 0x8d, 0xdf, 0xe8, 0xc7, 0x10, 0xd4, 0x70, 0x71, 0x36, 0xc7,
	0xab, 0x6e, 0xcd, 0x77, 0xa0, 0xae, 0x84, 0x5d, 0xc9, 0xc4, 0xac, 0x92, 0x09, 0x05, 0x22, 0x99,
	0x48, 0xef, 0xdd, 0xb9, 0xcc, 0xe9, 0xf7, 0x1f, 0x25, 0x58, 0x4c, 0x8f, 0x19, 0x87, 0xab, 0x5b,
	0x8c, 0xe5, 0xbd, 0xaa, 0x00, 0xea, 0x6c, 0x8f, 0xd7, 0x50, 0x5b, 0xf1, 0x8e, 0x5e, 0xbb, 0x77,
	0xc8, 0xb2, 0x8a, 0xf7, 0x9b, 0xb2, 0xac, 0xc0, 0x15, 0x71, 0xc3, 0x78, 0x5f, 0x8f, 0x04, 0x81,
	0x16, 0x4b, 0x39, 0x79, 0x17, 0x22, 0x20, 0xad, 0x98, 0x01, 0x0d, 0x57, 0xf4, 0xb0, 0x77, 0x25,
	0xb7, 0xc4, 0x71, 0xd5, 0xac, 0xbb, 0x62, 0x87, 0x4b, 0x93, 0x40, 0xec, 0x7d, 0x58, 0x1a, 0x13,
	0xa8, 0xa6, 0x54, 0x0a, 0x6d, 0xa3, 0x1f, 0xd1, 0xe8, 0x8b, 0x59, 0x66, 0xe6, 0x45, 0xc0, 0xee,
	0xc3, 0x42, 0x34, 0x3a, 0xef, 0xd4, 0x8f, 0x62, 0xa7, 0xef, 0x3c, 0x47, 0x2d, 0x50, 0xc4, 0x43,
	0x4f, 0x32, 0x7e, 0x0b, 0xa3, 0x4d, 0x0f, 0x65, 0xb4, 0xae, 0xa1, 0xee, 0xf2, 0xd7, 0x34, 0x35,
	0x93, 0xe5, 0xf4, 0x4c, 0x1a, 0xab, 0x93, 0x8d, 0x88, 0xc0, 0x78, 0x17, 0x1a, 0x2a, 0xe4, 0x2b,
	0x8b, 0xee, 0x26, 0xc6, 0x5f, 0x96, 0x52, 0x34, 0x22, 0x60, 0x37, 0x61, 0xc1, 0xf6, 0xbd, 0xd3,
	0xde, 0x48, 0xf0, 0x84, 0x9d, 0x8e, 0x42, 0x78, 0x7a, 0x2c, 0x38, 0xee, 0xfc, 0x5b, 0xb8, 0x87,
	0xbd, 0x53, 0xed, 0x32, 0x1b, 0xbb, 0xe4, 0xa8, 0x1e, 0x79, 0xca, 0x90, 0xea, 0x0e, 0xac, 0x50,
	0x54, 0xbb, 0x17, 0xf2, 0xa1, 0xe5, 0x7a, 0xae, 0xd7, 0x4f, 0xf8, 0xe3, 0x96, 0x09, 0x65, 0x46,
	0x98, 0xfd, 0xd1, 0xd0, 0xe8, 0xc0, 0x7a, 0xfb, 0x8c, 0xdb, 0xe7, 0x5b, 0xbb, 0xa6, 0xef, 0x0f,
	0x1f, 0xb9, 0xc2, 0x3d, 0x71, 0x07, 0xae, 0xbc, 0x7c, 0xc9, 0xa4, 0x05, 0x4e, 0xe1, 0xbc, 0x63,
	0xa1, 0x62, 0x14, 0x47, 0x36, 0xf7, 0x5e, 0xed, 0xf8, 0x52, 0x29, 0x77, 0xb2, 0xa7, 0xf2, 0x0e,
	0x67, 0xe2, 0x94, 0x3b, 0xd5, 0xa4, 0xf1, 0x6d, 0x0a, 0x64, 0x65, 0xba, 0x11, 0x01, 0x9a, 0x2a,
	0x2a, 0x89, 0x91, 0x9f, 0x9e, 0x72, 0x5b, 0x6a, 0x4f, 0x66, 0x9d, 0x60, 0x1d, 0x02, 0x19, 0x0f,
	0x60, 0x83, 0x06, 0x1a, 0x57, 0x7c, 0xe5, 0xb1, 0xfe, 0x7f, 0xb8, 0x56, 0x30, 0x65, 0x22, 0x20,
	0x5d, 0x62, 0x79, 0x3d, 0x7d, 0xb3, 0xd5, 0x8c, 0x80, 0x6d, 0x79, 0xdb, 0x0a, 0x62, 0x7c, 0x0a,
	0x6f, 0x17, 0xf3, 0x31, 0x4d, 0x03, 0x1f, 0xc0, 0x15, 0x6a, 0x60, 0xf7, 0xb4, 0xab, 0x74, 0x5e,
	0xa1, 0x37, 0xd4, 0xf8, 0x04, 0xd6, 0xf2, 0x48, 0x95, 0xdb, 0xdc, 0x15, 0xbd, 0x48, 0x87, 0x96,
	0xa2, 0x1b, 0x94, 0x26, 0x33, 0x3a, 0x94, 0x7e, 0xb6, 0xef, 0x93, 0x4b, 0x88, 0x3b, 0xa6, 0x3f,
	0x28, 0x7a, 0x0b, 0x12, 0x05, 0x40, 0xc9, 0x9c, 0x53, 0x59, 0x20, 0x14, 0x00, 0x25, 0x5b, 0xed,
	0x9b, 0x94, 0x53, 0x9d, 0x6d, 0x46, 0x04, 0xe9, 0x5a, 0xa5, 0x4c, 0xad, 0x36, 0x80, 0xe2, 0xd3,
	0xf6, 0xc3, 0x54, 0xf6, 0x6f, 0x29, 0x1d, 0x09, 0xdc, 0x00, 0x92, 0x8c, 0x1e, 0x85, 0x7e, 0xf5,
	0xd6, 0x44, 0x00, 0x76, 0x63, 0xfc, 0x55, 0x09, 0x56, 0xb1, 0x15, 0x9d, 0xce, 0xbe, 0x15, 0x04,
	0x14, 0x36, 0x78, 0x05, 0x91, 0xbc, 0x4f, 0xa9, 0xb6, 0xa8, 0xaa, 0x90, 0x9f, 0xe8, 0xfd, 0x54,
	0xbe, 0xb6, 0x19, 0xf3, 0xad, 0x3c, 0xe5, 0xea, 0x1b, 0xb5, 0xcd, 0x95, 0x1c, 0x66, 0xd4, 0x53,
	0x89, 0x90, 0x0b, 0xbd, 0x00, 0xf8, 0x49, 0x2e, 0x16, 0x2e, 0xc4, 0x38, 0xdf, 0x2e, 0x2a, 0x1a,
	0x7f, 0x53, 0x82, 0x15, 0x93, 0x5b, 0x8e, 0x0e, 0x35, 0x3c, 0xe0, 0x78, 0x2f, 0x7e, 0xad, 0x07,
	0x51, 0x71, 0xd4, 0x35, 0xff, 0x4d, 0x81, 0xb1, 0x05, 0xab, 0x93, 0x1c, 0xa9, 0x7c, 0x2d, 0x4d,
	0x5e, 0x4a, 0x3e, 0x41, 0xc8, 0xf1, 0x36, 0x6c, 0x0e, 0x60, 0x29, 0xf3, 0x66, 0x8d, 0x5d, 0x87,
	0x6b, 0x3b, 0x5b, 0x7b, 0x9d, 0xde, 0xee, 0xfe, 0xf7, 0x0f, 0x7a, 0x07, 0x87, 0x1d, 0x73, 0xab,
	0xdb, 0xe9, 0x1d, 0xef, 0xff, 0x70, 0xff, 0xe0, 0xf1, 0x7e, 0xf3, 0x2d, 0xf6, 0x36, 0xac, 0xe7,
	0xa0, 0x0f, 0x8f, 0x3a, 0x66, 0x57, 0xa5, 0x6b, 0x4c, 0x62, 0xb7, 0x3b, 0x0f, 0x3b, 0xdd, 0x4e,
	0xb3, 0xbc, 0xf9, 0x18, 0xea, 0x94, 0x4d, 0xa3, 0x5f, 0x8b, 0xad, 0xc3, 0x2a, 0x11, 0xb7, 0xcd,
	0xce, 0x56, 0xf7, 0xc0, 0x4c, 0x74, 0x72, 0x15, 0x56, 0x52, 0x98, 0xa3, 0x1f, 0x1d, 0x75, 0x3b,
	0x7b, 0xcd, 0x12, 0xbb, 0x02, 0xcb, 0xe9, 0x2a, 0x47, 0x1d, 0xb3, 0x59, 0xde, 0x3c, 0x55, 0x4f,
	0xf8, 0x0e, 0x79, 0x38, 0x74, 0xe9, 0x8d, 0x0c, 0xdb, 0x80, 0xab, 0x44, 0x78, 0xd8, 0x31, 0xf7,
	0x76, 0x8f, 0x8e, 0x76, 0x0f, 0xf6, 0x13, 0xcd, 0xb7, 0x60, 0x2d, 0x8b, 0x3c, 0x3c, 0xbe, 0xff,
	0x70, 0xb7, 0xdd, 0x2c, 0xe5, 0x55, 0x3c, 0x34, 0x77, 0x1f, 0x6d, 0xd1, 0x00, 0x1e, 0x42, 0x33,
	0x79, 0xde, 0x90, 0x79, 0xb0, 0x01, 0x57, 0xbb, 0x5b, 0xe6, 0x4e, 0xa7, 0xdb, 0x33, 0x3b, 0x8f,
	0xb7, 0xcc, 0xed, 0x5e, 0xf7, 0x47, 0x87, 0x9d, 0xde, 0xfe, 0xc1, 0x7e, 0xa7, 0xf9, 0x56, 0x01,
	0xb2, 0xfd, 0x60, 0xab, 0xdb, 0x2c, 0x6d, 0x7e, 0x2b, 0xb2, 0xcf, 0xbb, 0xfe, 0xb6, 0xcf, 0x56,
	0xa1, 0xa9, 0x49, 0xbb, 0x07, 0xdb, 0x07, 0x51, 0x03, 0x19, 0x68, 0xfb, 0xc1, 0x96, 0x6c, 0x96,
	0x36, 0xff, 0xac, 0x0c, 0x8d, 0x76, 0xc6, 0x78, 0x5c, 0x69, 0x1f, 0xec, 0x1d, 0x1e, 0x77, 0x3b,
	0xbd, 0xbd, 0x4e, 0xf7, 0xc1, 0xc1, 0x76, 0xd4, 0x40, 0x0b, 0xd6, 0xb2, 0x88, 0xe3, 0xbd, 0xde,
	0xd6, 0xf6, 0x76, 0xb3, 0x94, 0x83, 0xdb, 0xde, 0xfa, 0x11, 0xe1, 0xca, 0xec, 0x1d, 0xd8, 0xc8,
	0xc1, 0xb5, 0x0f, 0xf6, 0xbb, 0xbb, 0xfb, 0xc7, 0x9d, 0xe6, 0x0c, 0xca, 0x49, 0x86, 0xc0, 0xec,
	0xb4, 0x1f, 0xf5, 0xf6, 0xb6, 0xba, 0xed, 0x07, 0xcd, 0xd9, 0x9c, 0xfa, 0x84, 0xc6, 0x06, 0xb6,
	0x76, 0xf7, 0x9b, 0x73, 0x39, 0xf5, 0x8f, 0x3a, 0xfb, 0xdb, 0xba, 0x7e, 0x25, 0xa7, 0x3e, 0xa1,
	0xa3, 0xfa, 0xf3, 0x9b, 0x16, 0x34, 0xf4, 0xec, 0x29, 0xb5, 0x89, 0x53, 0x10, 0x4d, 0x95, 0xb9,
	0xbb, 0xb3, 0xd3, 0x31, 0x13, 0x53, 0x90, 0x41, 0xec, 0xee, 0xeb, 0x35, 0x48, 0x2c, 0x50, 0x84,
	0x43, 0x44, 0x0f, 0x2b, 0x96, 0x37, 0x0f, 0xe3, 0x05, 0xc2, 0x85, 0x4e, 0x2c, 0x45, 0x62, 0x85,
	0xd7, 0x80, 0x25, 0xa1, 0xb1, 0xa4, 0x66, 0xa8, 0xa9, 0xbb, 0xf2, 0xbd, 0x3f, 0xff, 0x00, 0x96,
	0xcd, 0xf6, 0xde, 0x76, 0x3a, 0x45, 0xaf, 0x0f, 0xcd, 0x6c, 0x86, 0x08, 0xbb, 0x9d, 0xef, 0x92,
	0x9b, 0xcc, 0xda, 0x69, 0x7d, 0x30, 0x25, 0xa5, 0x08, 0xd8, 0x10, 0xd8, 0x64, 0xf2, 0x04, 0xdb,
	0x9c, 0x3e, 0x57, 0xa4, 0xf5, 0x95, 0xa9, 0x69, 0x45, 0xc0, 0xfe, 0x18, 0xae, 0x16, 0x64, 0x4f,
	0xb0, 0xbb, 0xb9, 0xed, 0x14, 0xe7, 0x6f, 0xb4, 0xbe, 0xf6, 0x72, 0x15, 0x44, 0xc0, 0xda, 0x30,
	0xdb, 0xe5, 0x42, 0xb2, 0xb7, 0x9f, 0x97, 0x52, 0xd1, 0xba, 0xfe, 0x1c, 0xac, 0x08, 0x98, 0x03,
	0x4b, 0x99, 0xc4, 0x2b, 0xf6, 0xe5, 0xdc, 0x1a, 0x93, 0xa9, 0x5e, 0xad, 0xdb, 0xd3, 0x11, 0xaa,
	0x5e, 0x32, 0xc9, 0x68, 0x05, 0xbd, 0x4c, 0xe6, 0xdf, 0x15, 0xf4, 0x92, 0x97, 0xdb, 0x36, 0x04,
	0x36, 0x99, 0x12, 0x59, 0xb0, 0xfa, 0xb9, 0x19, 0xa8, 0x05, 0xab, 0x5f, 0x90, 0x67, 0xf9, 0xfb,
	0x50, 0x4f, 0xa4, 0x3d, 0xb2, 0xf7, 0x8a, 0xde, 0x4c, 0x25, 0x92, 0x27, 0x5b, 0xb7, 0x5e, 0x4c,
	0x24, 0x02, 0xf6, 0x07, 0x64, 0xbf, 0x8f, 0x73, 0x74, 0xd9, 0x97, 0x8a, 0xaa, 0xa5, 0xf2, 0x80,
	0x5b, 0xef, 0x4f, 0x43, 0xa6, 0x96, 0x23, 0x93, 0x95, 0x5c, 0xb0, 0x1c, 0x93, 0xf9, 0xcf, 0x05,
	0xcb, 0x91, 0x97, 0xe4, 0x6c, 0x42, 0x2d, 0xce, 0xb3, 0x66, 0x45, 0xe9, 0xf1, 0xe3, 0xd4, 0xef,
	0x96, 0xf1, 0x22, 0x12, 0x11, 0xa0, 0x26, 0xc9, 0xbe, 0x5c, 0x28, 0xd0, 0x24, 0x39, 0x4f, 0x24,
	0x0a, 0x34, 0x49, 0xee, 0x53, 0x88, 0x9f, 0xc0, 0x42, 0x12, 0xce, 0x6e, 0x4d, 0xf1, 0x22, 0xee,
	0x49, 0xeb, 0x4b, 0x53, 0x50, 0x89, 0x80, 0x5d, 0xa4, 0x5f, 0x03, 0xc6, 0xaf, 0x5b, 0xd8, 0x87,
	0x2f, 0xac, 0x9f, 0x7c, 0x50, 0xd3, 0xba, 0xf3, 0x32, 0xe4, 0x22, 0x60, 0x97, 0xb0, 0x96, 0xff,
	0x94, 0x87, 0xe5, 0xb7, 0x54, 0xf8, 0xb4, 0xa8, 0x75, 0xf7, 0xa5, 0xe8, 0x45, 0xc0, 0x2c, 0x58,
	0x4c, 0x3f, 0x67, 0x62, 0xef, 0x17, 0x2f, 0x77, 0x6a, 0x4e, 0xbf, 0x3c, 0x15, 0x9d, 0x08, 0x98,
	0x48, 0xbf, 0x6a, 0x89, 0x9e, 0xcb, 0xb1, 0xaf, 0xbe, 0x70, 0x96, 0x12, 0x4f, 0xfb, 0x5a, 0x1f,
	0xbe, 0x04, 0xb5, 0x08, 0xd8, 0xe7, 0xe4, 0x86, 0x4e, 0x3f, 0x8b, 0x64, 0x1f, 0x4c, 0xfb, 0x7c,
	0xf2, 0x49, 0x6b, 0x73, 0xfa, 0x97, 0x96, 0x2c, 0x48, 0x3f, 0xfc, 0xd1, 0xf6, 0x3e, 0xfb, 0xca,
	0x0b, 0x9b, 0x18, 0x3f, 0x6e, 0x6e, 0x7d, 0x75, 0x7a, 0xe2, 0x68, 0x74, 0x99, 0xc4, 0xa8, 0xc2,
	0xd1, 0x4d, 0xa6, 0x68, 0x15, 0x8e, 0x2e, 0x2f, 0xd7, 0x8a, 0x96, 0x6f, 0xf2, 0xcd, 0x77, 0xe1,
	0xf2, 0xe5, 0x3e, 0x78, 0x2f, 0x5c, 0xbe, 0x82, 0xc7, 0xe4, 0x2e, 0x2c, 0x4f, 0xe4, 0x57, 0x15,
	0x0c, 0x30, 0x2f, 0xd3, 0xab, 0x35, 0x2d, 0xa9, 0x08, 0xd8, 0x29, 0x34, 0xb3, 0xe9, 0x51, 0x45,
	0x46, 0xd0, 0x64, 0x16, 0x57, 0x6b, 0x4a, 0x4a, 0x11, 0xb0, 0x1f, 0xc3, 0x42, 0xaa, 0x8f, 0x7c,
	0xcd, 0x95, 0x6d, 0x7f, 0x0a, 0x2a, 0x11, 0x30, 0x1b, 0x96, 0x32, 0x39, 0x44, 0x05, 0x07, 0xc7,
	0x64, 0x6e, 0x53, 0x6b, 0x3a, 0x42, 0x11, 0xb0, 0xcf, 0xe8, 0xc9, 0x41, 0x22, 0x21, 0x87, 0x15,
	0x9e, 0x6b, 0xe9, 0xbc, 0xa5, 0xd6, 0x54, 0x74, 0x22, 0x60, 0x3f, 0x85, 0x46, 0x2a, 0xe5, 0xa5,
	0xe0, 0x7c, 0xcd, 0xa6, 0xe9, 0xb4, 0xa6, 0x21, 0x4b, 0x0f, 0x40, 0x67, 0x90, 0x3c, 0x9f, 0xb1,
	0x38, 0x11, 0xa6, 0x35, 0x15, 0x9d, 0x5a, 0xe3, 0x64, 0xf6, 0x45, 0xf1, 0xe9, 0x94, 0xcc, 0x0d,
	0x69, 0x4d, 0x41, 0xa5, 0xb8, 0x4f, 0x67, 0x45, 0x14, 0x70, 0x3f, 0x91, 0xd1, 0xd1, 0x9a, 0x8a,
	0x2e, 0x9e, 0x9f, 0x44, 0x96, 0x43, 0xf1, 0xfc, 0xa4, 0xf3, 0x30, 0x5a, 0x53, 0xd1, 0xa9, 0x05,
	0x4e, 0xc5, 0xc2, 0x0b, 0x16, 0x38, 0x1b, 0xc6, 0x6f, 0x4d, 0x43, 0xa6, 0xa6, 0x3f, 0x19, 0xa4,
	0x66, 0x85, 0x8f, 0xe6, 0x92, 0xd1, 0xf5, 0xd6, 0x14, 0x54, 0x4a, 0x23, 0x4d, 0x44, 0x9b, 0x0b,
	0x34, 0x52, 0x5e, 0x54, 0xbc, 0x35, 0x2d, 0xa9, 0x32, 0x43, 0x72, 0x5d, 0x8d, 0x05, 0x66, 0x48,
	0x91, 0x27, 0xb7, 0xc0, 0x0c, 0x29, 0xf6, 0x62, 0xaa, 0xf5, 0x4f, 0x46, 0xa7, 0x0b, 0xd7, 0x35,
	0x1d, 0x5c, 0x6f, 0x4d, 0x45, 0xa7, 0x74, 0x6d, 0x36, 0x58, 0x5a, 0xa0, 0x6b, 0x73, 0x82, 0xbd,
	0xad, 0x29, 0x29, 0x55, 0x3f, 0xd9, 0xf0, 0x65, 0xb1, 0x39, 0x3a, 0x65, 0x3f, 0x79, 0xf1, 0xd0,
	0x9f, 0xd2, 0x85, 0x60, 0x1c, 0xa7, 0x28, 0xbe, 0x10, 0xa4, 0xa2, 0x48, 0xad, 0x69, 0xc8, 0x94,
	0x5a, 0xcf, 0x44, 0x1a, 0x0a, 0xd4, 0xfa, 0x64, 0x50, 0xa3, 0x35, 0x1d, 0xa1, 0x08, 0x58, 0x17,
	0x60, 0x1c, 0x94, 0x60, 0x46, 0x51, 0xb5, 0x71, 0x64, 0xa3, 0xf5, 0x42, 0x1a, 0x75, 0x21, 0xc8,
	0xfa, 0xec, 0x8b, 0x5d, 0x0b, 0xd9, 0x08, 0x42, 0xb1, 0x6b, 0x61, 0x32, 0x08, 0xf0, 0x27, 0x3a,
	0x94, 0x91, 0xe3, 0x59, 0x67, 0x5f, 0x2b, 0xde, 0x00, 0xf9, 0x01, 0x81, 0xd6, 0xd7, 0x5f, 0xb2,
	0x86, 0x08, 0xd8, 0x63, 0xba, 0x6e, 0xc6, 0x7f, 0x62, 0x54, 0x78, 0xdd, 0x4c, 0xfc, 0xbb, 0x52,
	0xeb, 0xc5, 0x44, 0xb1, 0x70, 0x8d, 0xff, 0x6c, 0xec, 0x39, 0xb7, 0xcd, 0xe4, 0xff, 0x99, 0xb5,
	0xa6, 0x21, 0x1b, 0xcb, 0x6e, 0xfc, 0x9f, 0x6a, 0xcf, 0x91, 0xdd, 0xe4, 0xdf, 0xc1, 0xb5, 0xa6,
	0x21, 0x8b, 0x65, 0x37, 0xf9, 0x9f, 0x74, 0xc5, 0xb2, 0x9b, 0xf9, 0x43, 0xbd, 0xd6, 0x74, 0x84,
	0xca, 0xb3, 0x30, 0x19, 0xea, 0x28, 0xf0, 0x2c, 0xe4, 0x86, 0x4f, 0x0a, 0x3c, 0x0b, 0x05, 0xf1,
	0x13, 0x75, 0xa9, 0x48, 0x47, 0x36, 0x8a, 0x2f, 0x15, 0x13, 0x81, 0x94, 0xe2, 0x4b, 0x45, 0x4e,
	0xb0, 0xe4, 0x73, 0x58, 0x9e, 0x08, 0x1e, 0x14, 0xf4, 0x95, 0x17, 0xf1, 0x68, 0x6d, 0x4e, 0x4b,
	0xaa, 0x36, 0x6b, 0xd6, 0xa1, 0xcf, 0x8a, 0xdc, 0x3b, 0x13, 0x91, 0x88, 0x82, 0xcd, 0x9a, 0x17,
	0x21, 0xb8, 0xff, 0xc9, 0x8f, 0x3f, 0xea, 0xfb, 0x03, 0xcb, 0xeb, 0xdf, 0xf9, 0xe8, 0x9e, 0x94,
	0x77, 0x6c, 0x7f, 0x78, 0x97, 0xfe, 0x59, 0xd1, 0xf6, 0x07, 0x77, 0x05, 0x0f, 0x2f, 0x5c, 0x9b,
	0x8b, 0xbb, 0xd8, 0x5a, 0xe6, 0xaf, 0x1b, 0x4f, 0x2a, 0x44, 0xf6, 0x8d, 0xff, 0x0d, 0x00, 0x00,
	0xff, 0xff, 0x73, 0x59, 0x54, 0xeb, 0xe3, 0x51, 0x00, 0x00,
}
