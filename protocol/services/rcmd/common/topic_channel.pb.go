// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/rcmd-local/common/topic_channel.proto

package common // import "golang.52tt.com/protocol/services/rcmd/common"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 关注的人＞一起玩过＞同城标签
type RCMDLabel int32

const (
	RCMDLabel_None                RCMDLabel = 0
	RCMDLabel_GangUpWithHomeOwner RCMDLabel = 1
	RCMDLabel_ChatWithHomeOwner   RCMDLabel = 2
	RCMDLabel_FollowUserInChannel RCMDLabel = 3
	RCMDLabel_LocShow             RCMDLabel = 4
)

var RCMDLabel_name = map[int32]string{
	0: "None",
	1: "GangUpWithHomeOwner",
	2: "ChatWithHomeOwner",
	3: "FollowUserInChannel",
	4: "LocShow",
}
var RCMDLabel_value = map[string]int32{
	"None":                0,
	"GangUpWithHomeOwner": 1,
	"ChatWithHomeOwner":   2,
	"FollowUserInChannel": 3,
	"LocShow":             4,
}

func (x RCMDLabel) String() string {
	return proto.EnumName(RCMDLabel_name, int32(x))
}
func (RCMDLabel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_a0e0ece9a3664890, []int{0}
}

type Source int32

const (
	Source_Default         Source = 0
	Source_RcmdGameChannel Source = 1
	Source_RcmdSearch      Source = 2
)

var Source_name = map[int32]string{
	0: "Default",
	1: "RcmdGameChannel",
	2: "RcmdSearch",
}
var Source_value = map[string]int32{
	"Default":         0,
	"RcmdGameChannel": 1,
	"RcmdSearch":      2,
}

func (x Source) String() string {
	return proto.EnumName(Source_name, int32(x))
}
func (Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_a0e0ece9a3664890, []int{1}
}

type ChannelInfo_LocShowType int32

const (
	ChannelInfo_LocShowType_DEFAULT  ChannelInfo_LocShowType = 0
	ChannelInfo_LocShowType_PROVINCE ChannelInfo_LocShowType = 2
	ChannelInfo_LocShowType_CITY     ChannelInfo_LocShowType = 3
)

var ChannelInfo_LocShowType_name = map[int32]string{
	0: "LocShowType_DEFAULT",
	2: "LocShowType_PROVINCE",
	3: "LocShowType_CITY",
}
var ChannelInfo_LocShowType_value = map[string]int32{
	"LocShowType_DEFAULT":  0,
	"LocShowType_PROVINCE": 2,
	"LocShowType_CITY":     3,
}

func (x ChannelInfo_LocShowType) String() string {
	return proto.EnumName(ChannelInfo_LocShowType_name, int32(x))
}
func (ChannelInfo_LocShowType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_a0e0ece9a3664890, []int{0, 0}
}

type ChannelInfo struct {
	TagId       uint32                  `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	RecallFlag  uint32                  `protobuf:"varint,2,opt,name=recall_flag,json=recallFlag,proto3" json:"recall_flag,omitempty"`
	Loc         *LocationInfo           `protobuf:"bytes,3,opt,name=loc,proto3" json:"loc,omitempty"`
	RcmdLabels  []RCMDLabel             `protobuf:"varint,4,rep,packed,name=rcmd_labels,json=rcmdLabels,proto3,enum=rcmd.common.RCMDLabel" json:"rcmd_labels,omitempty"`
	LocShowType ChannelInfo_LocShowType `protobuf:"varint,5,opt,name=loc_show_type,json=locShowType,proto3,enum=rcmd.common.ChannelInfo_LocShowType" json:"loc_show_type,omitempty"`
	// Deprecated: 萌新承接房逻辑已下线
	IsNewUserUndertake   bool     `protobuf:"varint,6,opt,name=is_new_user_undertake,json=isNewUserUndertake,proto3" json:"is_new_user_undertake,omitempty"`
	FollowUidList        []uint32 `protobuf:"varint,7,rep,packed,name=follow_uid_list,json=followUidList,proto3" json:"follow_uid_list,omitempty"`
	PlayUidList          []uint32 `protobuf:"varint,8,rep,packed,name=play_uid_list,json=playUidList,proto3" json:"play_uid_list,omitempty"`
	FollowUidListV2      []uint32 `protobuf:"varint,9,rep,packed,name=follow_uid_list_v2,json=followUidListV2,proto3" json:"follow_uid_list_v2,omitempty"`
	IsEverEnter          bool     `protobuf:"varint,10,opt,name=is_ever_enter,json=isEverEnter,proto3" json:"is_ever_enter,omitempty"`
	DeliveryType         string   `protobuf:"bytes,11,opt,name=delivery_type,json=deliveryType,proto3" json:"delivery_type,omitempty"`
	DisplayContent       string   `protobuf:"bytes,12,opt,name=display_content,json=displayContent,proto3" json:"display_content,omitempty"`
	DisplayUidList       []uint32 `protobuf:"varint,13,rep,packed,name=display_uid_list,json=displayUidList,proto3" json:"display_uid_list,omitempty"`
	Source               Source   `protobuf:"varint,14,opt,name=source,proto3,enum=rcmd.common.Source" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelInfo) Reset()         { *m = ChannelInfo{} }
func (m *ChannelInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelInfo) ProtoMessage()    {}
func (*ChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_a0e0ece9a3664890, []int{0}
}
func (m *ChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelInfo.Unmarshal(m, b)
}
func (m *ChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelInfo.Merge(dst, src)
}
func (m *ChannelInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelInfo.Size(m)
}
func (m *ChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelInfo proto.InternalMessageInfo

func (m *ChannelInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *ChannelInfo) GetRecallFlag() uint32 {
	if m != nil {
		return m.RecallFlag
	}
	return 0
}

func (m *ChannelInfo) GetLoc() *LocationInfo {
	if m != nil {
		return m.Loc
	}
	return nil
}

func (m *ChannelInfo) GetRcmdLabels() []RCMDLabel {
	if m != nil {
		return m.RcmdLabels
	}
	return nil
}

func (m *ChannelInfo) GetLocShowType() ChannelInfo_LocShowType {
	if m != nil {
		return m.LocShowType
	}
	return ChannelInfo_LocShowType_DEFAULT
}

func (m *ChannelInfo) GetIsNewUserUndertake() bool {
	if m != nil {
		return m.IsNewUserUndertake
	}
	return false
}

func (m *ChannelInfo) GetFollowUidList() []uint32 {
	if m != nil {
		return m.FollowUidList
	}
	return nil
}

func (m *ChannelInfo) GetPlayUidList() []uint32 {
	if m != nil {
		return m.PlayUidList
	}
	return nil
}

func (m *ChannelInfo) GetFollowUidListV2() []uint32 {
	if m != nil {
		return m.FollowUidListV2
	}
	return nil
}

func (m *ChannelInfo) GetIsEverEnter() bool {
	if m != nil {
		return m.IsEverEnter
	}
	return false
}

func (m *ChannelInfo) GetDeliveryType() string {
	if m != nil {
		return m.DeliveryType
	}
	return ""
}

func (m *ChannelInfo) GetDisplayContent() string {
	if m != nil {
		return m.DisplayContent
	}
	return ""
}

func (m *ChannelInfo) GetDisplayUidList() []uint32 {
	if m != nil {
		return m.DisplayUidList
	}
	return nil
}

func (m *ChannelInfo) GetSource() Source {
	if m != nil {
		return m.Source
	}
	return Source_Default
}

func init() {
	proto.RegisterType((*ChannelInfo)(nil), "rcmd.common.ChannelInfo")
	proto.RegisterEnum("rcmd.common.RCMDLabel", RCMDLabel_name, RCMDLabel_value)
	proto.RegisterEnum("rcmd.common.Source", Source_name, Source_value)
	proto.RegisterEnum("rcmd.common.ChannelInfo_LocShowType", ChannelInfo_LocShowType_name, ChannelInfo_LocShowType_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/rcmd-local/common/topic_channel.proto", fileDescriptor_topic_channel_a0e0ece9a3664890)
}

var fileDescriptor_topic_channel_a0e0ece9a3664890 = []byte{
	// 631 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x53, 0xdf, 0x4f, 0x1a, 0x4b,
	0x14, 0x76, 0x41, 0x51, 0xcf, 0x0a, 0xec, 0x1d, 0xf5, 0xde, 0xbd, 0x7d, 0x29, 0xb1, 0x4d, 0xbb,
	0xd1, 0x08, 0x29, 0x6d, 0xd3, 0xa4, 0x6f, 0x2d, 0xa2, 0x92, 0x50, 0x6d, 0x56, 0xb1, 0x69, 0x5f,
	0x36, 0xe3, 0xec, 0x01, 0x26, 0x0e, 0x33, 0x74, 0x66, 0x80, 0xf0, 0xef, 0xf5, 0x2f, 0x6b, 0x76,
	0x16, 0x14, 0xfa, 0xd2, 0xa7, 0xdd, 0xf9, 0xbe, 0xef, 0x7c, 0x73, 0x7e, 0xcc, 0x81, 0x77, 0xd6,
	0x36, 0x7e, 0x4e, 0x38, 0x7b, 0x30, 0x5c, 0x4c, 0x51, 0x37, 0x34, 0x1b, 0xa5, 0xa7, 0x42, 0x31,
	0x2a, 0x1a, 0x4c, 0x8d, 0x46, 0x4a, 0x36, 0xac, 0x1a, 0x73, 0x96, 0xb0, 0x21, 0x95, 0x12, 0x45,
	0x7d, 0xac, 0x95, 0x55, 0xc4, 0xcf, 0x64, 0xf5, 0x5c, 0xf0, 0xec, 0xf4, 0xaf, 0x16, 0xf9, 0x27,
	0x8f, 0x3d, 0xfa, 0xb5, 0x05, 0x7e, 0x2b, 0x77, 0xeb, 0xc8, 0xbe, 0x22, 0x87, 0x50, 0xb2, 0x74,
	0x90, 0xf0, 0x34, 0xf4, 0x6a, 0x5e, 0x54, 0x8e, 0xb7, 0x2c, 0x1d, 0x74, 0x52, 0xf2, 0x1c, 0x7c,
	0x8d, 0x8c, 0x0a, 0x91, 0xf4, 0x05, 0x1d, 0x84, 0x05, 0xc7, 0x41, 0x0e, 0x9d, 0x0b, 0x3a, 0x20,
	0x27, 0x50, 0x14, 0x8a, 0x85, 0xc5, 0x9a, 0x17, 0xf9, 0xcd, 0xff, 0xeb, 0x2b, 0x19, 0xd5, 0xbb,
	0x8a, 0x51, 0xcb, 0x95, 0xcc, 0xfc, 0xe3, 0x4c, 0x45, 0x3e, 0x80, 0x4b, 0x39, 0x11, 0xf4, 0x1e,
	0x85, 0x09, 0x37, 0x6b, 0xc5, 0xa8, 0xd2, 0xfc, 0x77, 0x2d, 0x28, 0x6e, 0x7d, 0x39, 0xeb, 0x66,
	0x74, 0x0c, 0x19, 0xec, 0x7e, 0x0d, 0xb9, 0x84, 0xb2, 0x50, 0x2c, 0x31, 0x43, 0x35, 0x4b, 0xec,
	0x7c, 0x8c, 0xe1, 0x56, 0xcd, 0x8b, 0x2a, 0xcd, 0x97, 0x6b, 0xa1, 0x2b, 0xe5, 0x64, 0x77, 0xdf,
	0x0c, 0xd5, 0xec, 0x76, 0x3e, 0xc6, 0xd8, 0x17, 0x4f, 0x07, 0xf2, 0x06, 0x0e, 0xb9, 0x49, 0x24,
	0xce, 0x92, 0x89, 0x41, 0x9d, 0x4c, 0x64, 0x8a, 0xda, 0xd2, 0x07, 0x0c, 0x4b, 0x35, 0x2f, 0xda,
	0x89, 0x09, 0x37, 0x57, 0x38, 0xeb, 0x19, 0xd4, 0xbd, 0x25, 0x43, 0x5e, 0x41, 0xb5, 0xaf, 0x84,
	0x50, 0xb3, 0x64, 0xc2, 0xd3, 0x44, 0x70, 0x63, 0xc3, 0xed, 0x5a, 0x31, 0x2a, 0xc7, 0xe5, 0x1c,
	0xee, 0xf1, 0xb4, 0xcb, 0x8d, 0x25, 0x47, 0x50, 0x1e, 0x0b, 0x3a, 0x7f, 0x52, 0xed, 0x38, 0x95,
	0x9f, 0x81, 0x4b, 0xcd, 0x09, 0x90, 0x3f, 0xbc, 0x92, 0x69, 0x33, 0xdc, 0x75, 0xc2, 0xea, 0x9a,
	0xdd, 0x5d, 0x33, 0x33, 0xe4, 0x26, 0xc1, 0x29, 0xea, 0x04, 0xa5, 0x45, 0x1d, 0x82, 0xcb, 0xd1,
	0xe7, 0xa6, 0x3d, 0x45, 0xdd, 0xce, 0x20, 0xf2, 0x02, 0xca, 0x29, 0x0a, 0x3e, 0x45, 0x3d, 0xcf,
	0x3b, 0xe3, 0xd7, 0xbc, 0x68, 0x37, 0xde, 0x5b, 0x82, 0xae, 0xe8, 0xd7, 0x50, 0x4d, 0xb9, 0x71,
	0xc9, 0x31, 0x25, 0x2d, 0x4a, 0x1b, 0xee, 0x39, 0x59, 0x65, 0x01, 0xb7, 0x72, 0x94, 0x44, 0x10,
	0x2c, 0x85, 0x8f, 0x55, 0x94, 0x5d, 0x72, 0x4b, 0xe5, 0x53, 0x21, 0x25, 0xa3, 0x26, 0x9a, 0x61,
	0x58, 0x71, 0xa3, 0xd8, 0x5f, 0x1b, 0xc5, 0x8d, 0xa3, 0xe2, 0x85, 0xe4, 0xe8, 0x0e, 0xfc, 0x95,
	0x81, 0x90, 0xff, 0x60, 0x7f, 0xe5, 0x98, 0x9c, 0xb5, 0xcf, 0x3f, 0xf5, 0xba, 0xb7, 0xc1, 0x06,
	0x09, 0xe1, 0x60, 0x95, 0xf8, 0x1a, 0x5f, 0xdf, 0x75, 0xae, 0x5a, 0xed, 0xa0, 0x40, 0x0e, 0x20,
	0x58, 0x65, 0x5a, 0x9d, 0xdb, 0xef, 0x41, 0xf1, 0xf8, 0x01, 0x76, 0x1f, 0xdf, 0x0b, 0xd9, 0x81,
	0xcd, 0x2b, 0x25, 0x31, 0xd8, 0xc8, 0xfc, 0x2f, 0xa8, 0x1c, 0xf4, 0xc6, 0xdf, 0xb8, 0x1d, 0x5e,
	0xaa, 0x11, 0x5e, 0xcf, 0x24, 0xea, 0xc0, 0x23, 0x87, 0xf0, 0x4f, 0x6b, 0x48, 0xed, 0x3a, 0x5c,
	0xc8, 0xf4, 0xe7, 0x79, 0xeb, 0x0d, 0xea, 0x8e, 0x5c, 0xbc, 0xa3, 0xa0, 0x48, 0x7c, 0xd8, 0x5e,
	0xdc, 0x1a, 0x6c, 0x1e, 0x7f, 0x84, 0x52, 0x5e, 0x56, 0x06, 0x9f, 0x61, 0x9f, 0x4e, 0x84, 0x0d,
	0x36, 0xc8, 0x3e, 0x54, 0x63, 0x36, 0x4a, 0x2f, 0xe8, 0x08, 0x97, 0x81, 0x1e, 0xa9, 0x00, 0x64,
	0xe0, 0x0d, 0x52, 0xcd, 0x86, 0x41, 0xe1, 0x73, 0xe3, 0xc7, 0xe9, 0x40, 0x09, 0x2a, 0x07, 0xf5,
	0xf7, 0x4d, 0x6b, 0xb3, 0x2e, 0x35, 0xdc, 0x1a, 0x32, 0x25, 0x1a, 0x06, 0xf5, 0x94, 0x33, 0x34,
	0x6e, 0x63, 0x17, 0x4b, 0x7a, 0x5f, 0x72, 0xf4, 0xdb, 0xdf, 0x01, 0x00, 0x00, 0xff, 0xff, 0xf3,
	0x31, 0x45, 0xe9, 0x19, 0x04, 0x00, 0x00,
}
