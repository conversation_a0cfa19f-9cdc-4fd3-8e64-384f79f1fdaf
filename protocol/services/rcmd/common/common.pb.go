// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/rcmd-local/common/common.proto

package common // import "golang.52tt.com/protocol/services/rcmd/common"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RecallQId int32

const (
	RecallQId_RecallQId_Unknow               RecallQId = 0
	RecallQId_RecallQId_RealTime_Sex_0_Age_0 RecallQId = 1
	RecallQId_RecallQId_RealTime_Sex_1_Age_1 RecallQId = 2
	RecallQId_RecallQId_Sex_1_Age_1          RecallQId = 3
	RecallQId_RecallQId_V03_Active_User      RecallQId = 4
	RecallQId_RecallQId_V03_Active_User_Core RecallQId = 5
)

var RecallQId_name = map[int32]string{
	0: "RecallQId_Unknow",
	1: "RecallQId_RealTime_Sex_0_Age_0",
	2: "RecallQId_RealTime_Sex_1_Age_1",
	3: "RecallQId_Sex_1_Age_1",
	4: "RecallQId_V03_Active_User",
	5: "RecallQId_V03_Active_User_Core",
}
var RecallQId_value = map[string]int32{
	"RecallQId_Unknow":               0,
	"RecallQId_RealTime_Sex_0_Age_0": 1,
	"RecallQId_RealTime_Sex_1_Age_1": 2,
	"RecallQId_Sex_1_Age_1":          3,
	"RecallQId_V03_Active_User":      4,
	"RecallQId_V03_Active_User_Core": 5,
}

func (x RecallQId) String() string {
	return proto.EnumName(RecallQId_name, int32(x))
}
func (RecallQId) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{0}
}

type ConfigType int32

const (
	ConfigType_ConfigType_Unknow ConfigType = 0
	ConfigType_ConfigType_Num    ConfigType = 1
	ConfigType_ConfigType_Ratio  ConfigType = 2
)

var ConfigType_name = map[int32]string{
	0: "ConfigType_Unknow",
	1: "ConfigType_Num",
	2: "ConfigType_Ratio",
}
var ConfigType_value = map[string]int32{
	"ConfigType_Unknow": 0,
	"ConfigType_Num":    1,
	"ConfigType_Ratio":  2,
}

func (x ConfigType) String() string {
	return proto.EnumName(ConfigType_name, int32(x))
}
func (ConfigType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{1}
}

// ////////排序用的配置
// 虽然定义了，但实际上model服务决定使用哪个模型是根据实验组名称
type RankStrategyId int32

const (
	RankStrategyId_RankStrategyId_Unknow                    RankStrategyId = 0
	RankStrategyId_RankStrategyId_Rule                      RankStrategyId = 1
	RankStrategyId_RankStrategyId_GBDT                      RankStrategyId = 2
	RankStrategyId_RankStrategyId_GBDT_IM                   RankStrategyId = 3
	RankStrategyId_RankStrategyId_GBDT_IM_Core              RankStrategyId = 4
	RankStrategyId_RankStrategyId_GBDT_IM_Back              RankStrategyId = 5
	RankStrategyId_RankStrategyId_GBDT_IM_Back_Train_Online RankStrategyId = 6
)

var RankStrategyId_name = map[int32]string{
	0: "RankStrategyId_Unknow",
	1: "RankStrategyId_Rule",
	2: "RankStrategyId_GBDT",
	3: "RankStrategyId_GBDT_IM",
	4: "RankStrategyId_GBDT_IM_Core",
	5: "RankStrategyId_GBDT_IM_Back",
	6: "RankStrategyId_GBDT_IM_Back_Train_Online",
}
var RankStrategyId_value = map[string]int32{
	"RankStrategyId_Unknow":                    0,
	"RankStrategyId_Rule":                      1,
	"RankStrategyId_GBDT":                      2,
	"RankStrategyId_GBDT_IM":                   3,
	"RankStrategyId_GBDT_IM_Core":              4,
	"RankStrategyId_GBDT_IM_Back":              5,
	"RankStrategyId_GBDT_IM_Back_Train_Online": 6,
}

func (x RankStrategyId) String() string {
	return proto.EnumName(RankStrategyId_name, int32(x))
}
func (RankStrategyId) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{2}
}

// ab测试实验的layerId
type LayerId int32

const (
	LayerId_LayerId_Unknow  LayerId = 0
	LayerId_LayerId_EnterV2 LayerId = 1
	//  "v0.1"
	//  "v0.2"
	LayerId_LayerId_PlaymateStrategy_NoUse_Test LayerId = 2
	//  "recall_sex_1_age_1_sort_rule"
	//  "recall_sex_0_age_0_sort_algo"
	//  "recall_sex_1_age_1_sort_algo"
	LayerId_LayerId_PlaymateRecallRankStrategy LayerId = 3
)

var LayerId_name = map[int32]string{
	0: "LayerId_Unknow",
	1: "LayerId_EnterV2",
	2: "LayerId_PlaymateStrategy_NoUse_Test",
	3: "LayerId_PlaymateRecallRankStrategy",
}
var LayerId_value = map[string]int32{
	"LayerId_Unknow":                      0,
	"LayerId_EnterV2":                     1,
	"LayerId_PlaymateStrategy_NoUse_Test": 2,
	"LayerId_PlaymateRecallRankStrategy":  3,
}

func (x LayerId) String() string {
	return proto.EnumName(LayerId_name, int32(x))
}
func (LayerId) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{3}
}

type RcmdRspCode int32

const (
	RcmdRspCode_CODE_UNKNOW              RcmdRspCode = 0
	RcmdRspCode_CODE_SUCCESS             RcmdRspCode = 1
	RcmdRspCode_CODE_PARAMS_FORMAT_ERROR RcmdRspCode = 2
	RcmdRspCode_CODE_DATABASE_ERROR      RcmdRspCode = 3
	// playmate    1000 - 2000
	RcmdRspCode_CODE_PLAYMATE_LOSS_AB_TEST_CONFIG RcmdRspCode = 1001
	RcmdRspCode_CODE_PLAYMATE_MODEL_NOT_FOUND     RcmdRspCode = 1002
)

var RcmdRspCode_name = map[int32]string{
	0:    "CODE_UNKNOW",
	1:    "CODE_SUCCESS",
	2:    "CODE_PARAMS_FORMAT_ERROR",
	3:    "CODE_DATABASE_ERROR",
	1001: "CODE_PLAYMATE_LOSS_AB_TEST_CONFIG",
	1002: "CODE_PLAYMATE_MODEL_NOT_FOUND",
}
var RcmdRspCode_value = map[string]int32{
	"CODE_UNKNOW":                       0,
	"CODE_SUCCESS":                      1,
	"CODE_PARAMS_FORMAT_ERROR":          2,
	"CODE_DATABASE_ERROR":               3,
	"CODE_PLAYMATE_LOSS_AB_TEST_CONFIG": 1001,
	"CODE_PLAYMATE_MODEL_NOT_FOUND":     1002,
}

func (x RcmdRspCode) String() string {
	return proto.EnumName(RcmdRspCode_name, int32(x))
}
func (RcmdRspCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{4}
}

type Env int32

const (
	Env_Env_Prod    Env = 0
	Env_Env_Staging Env = 1
	Env_Env_Test    Env = 2
)

var Env_name = map[int32]string{
	0: "Env_Prod",
	1: "Env_Staging",
	2: "Env_Test",
}
var Env_value = map[string]int32{
	"Env_Prod":    0,
	"Env_Staging": 1,
	"Env_Test":    2,
}

func (x Env) String() string {
	return proto.EnumName(Env_name, int32(x))
}
func (Env) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{5}
}

type RecallActionFormula int32

const (
	RecallActionFormula_UnknownFormula              RecallActionFormula = 0
	RecallActionFormula_MatrixMultiplicationFormula RecallActionFormula = 1
	RecallActionFormula_PitFillingFormula           RecallActionFormula = 2
)

var RecallActionFormula_name = map[int32]string{
	0: "UnknownFormula",
	1: "MatrixMultiplicationFormula",
	2: "PitFillingFormula",
}
var RecallActionFormula_value = map[string]int32{
	"UnknownFormula":              0,
	"MatrixMultiplicationFormula": 1,
	"PitFillingFormula":           2,
}

func (x RecallActionFormula) String() string {
	return proto.EnumName(RecallActionFormula_name, int32(x))
}
func (RecallActionFormula) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{6}
}

// 推荐场景
type RcmdScene int32

const (
	RcmdScene_UnknownRcmdScene RcmdScene = 0
	// 1000-1999 帖子/广场
	RcmdScene_RcmdStream           RcmdScene = 1000
	RcmdScene_RcmdTopic            RcmdScene = 1001
	RcmdScene_RcmdPostVoiceControl RcmdScene = 1002
	// 直播房
	RcmdScene_RcmdLiveChannel RcmdScene = 2000
)

var RcmdScene_name = map[int32]string{
	0:    "UnknownRcmdScene",
	1000: "RcmdStream",
	1001: "RcmdTopic",
	1002: "RcmdPostVoiceControl",
	2000: "RcmdLiveChannel",
}
var RcmdScene_value = map[string]int32{
	"UnknownRcmdScene":     0,
	"RcmdStream":           1000,
	"RcmdTopic":            1001,
	"RcmdPostVoiceControl": 1002,
	"RcmdLiveChannel":      2000,
}

func (x RcmdScene) String() string {
	return proto.EnumName(RcmdScene_name, int32(x))
}
func (RcmdScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{7}
}

// 召回队列id
// 不使用老的RecallQId
type RecallQueueId int32

const (
	RecallQueueId_UnknownRecallQueue RecallQueueId = 0
	// [10000,20000) 广场/帖子
	RecallQueueId_NewPoolRecall                     RecallQueueId = 10000
	RecallQueueId_NewMidPoolRecall                  RecallQueueId = 10001
	RecallQueueId_HighInteractPoolRecall            RecallQueueId = 10002
	RecallQueueId_PostAlgoOfflineRecall10           RecallQueueId = 10003
	RecallQueueId_PostAlgoOfflineRecallHighQuality  RecallQueueId = 10004
	RecallQueueId_PostAlgoOfflineRecall11           RecallQueueId = 10005
	RecallQueueId_PostAlgoOfflineRecallHot          RecallQueueId = 10006
	RecallQueueId_PostAlgoOnlineItemCF              RecallQueueId = 10007
	RecallQueueId_PostAlgoOnlineHotGlobal           RecallQueueId = 10008
	RecallQueueId_PostAlgoOnlineItem2Vec            RecallQueueId = 10009
	RecallQueueId_PostAlgoOnlineUser2Vec            RecallQueueId = 10010
	RecallQueueId_PostAlgoOnlineHotRegional         RecallQueueId = 10011
	RecallQueueId_PostAlgoOfflineUserCF             RecallQueueId = 10012
	RecallQueueId_PostAlgoOfflineRelationChain      RecallQueueId = 10013
	RecallQueueId_PostAlgoOnlineUser2Pub2Item       RecallQueueId = 10014
	RecallQueueId_PostAlgoOnlineUser2PubVec2Item    RecallQueueId = 10015
	RecallQueueId_NewPoolRecall3Min                 RecallQueueId = 10016
	RecallQueueId_PostAlgoNewPoolRecall             RecallQueueId = 10017
	RecallQueueId_PostAlgoOnlineBert                RecallQueueId = 10018
	RecallQueueId_PostAlgoHourCDTOM                 RecallQueueId = 10019
	RecallQueueId_PostAlgoNearlineHear              RecallQueueId = 10020
	RecallQueueId_PostAlgoOnlineUser2VecPub         RecallQueueId = 10021
	RecallQueueId_PostHeartIslandRecall             RecallQueueId = 10022
	RecallQueueId_PostImgTagHotRecall               RecallQueueId = 10023
	RecallQueueId_PostForMonitorRecall              RecallQueueId = 10024
	RecallQueueId_PostAIRapRecall                   RecallQueueId = 10025
	RecallQueueId_PostInterestHotRecall             RecallQueueId = 10026
	RecallQueueId_PostNewPreferRecall               RecallQueueId = 10027
	RecallQueueId_PostNewInterestRecall             RecallQueueId = 10028
	RecallQueueId_PostNewNotInterestRecall          RecallQueueId = 10029
	RecallQueueId_PostTopCtrRecall                  RecallQueueId = 10030
	RecallQueueId_PostDiscussTopicRecall            RecallQueueId = 10031
	RecallQueueId_PostTimerRecallBoutique           RecallQueueId = 10501
	RecallQueueId_PostTopicRecall                   RecallQueueId = 11000
	RecallQueueId_PostTopicVoiceControlStreamRecall RecallQueueId = 11001
	RecallQueueId_PostTopicLiveRecall               RecallQueueId = 11002
	RecallQueueId_PostTopicVideoAudioRecall         RecallQueueId = 11003
	RecallQueueId_PostSameCityPreferRecall          RecallQueueId = 12001
	RecallQueueId_PostSameCityInterestRecall        RecallQueueId = 12002
	RecallQueueId_PostSameCityFocusInterestRecall   RecallQueueId = 12003
	RecallQueueId_PostSameCityNonInterestRecall     RecallQueueId = 12004
	RecallQueueId_PostSameProvPreferRecall          RecallQueueId = 12005
	RecallQueueId_PostSameProvInterestRecall        RecallQueueId = 12006
	RecallQueueId_PostSameProvFocusInterestRecall   RecallQueueId = 12007
	RecallQueueId_PostSameProvNonInterestRecall     RecallQueueId = 12008
	RecallQueueId_PostSameCityAsyncCacheRecall      RecallQueueId = 12009
	// 直播房
	RecallQueueId_LiveChannelRecall    RecallQueueId = 20000
	RecallQueueId_ChatCardRecall       RecallQueueId = 21000
	RecallQueueId_PerfectMatchQuestion RecallQueueId = 22000
	// music-channel
	RecallQueueId_MusicChannelUCanUSing                 RecallQueueId = 30000
	RecallQueueId_MusicChannelUCUS_EP                   RecallQueueId = 30001
	RecallQueueId_MusicChannelKTV                       RecallQueueId = 30002
	RecallQueueId_MusicChannelKTV_HQ                    RecallQueueId = 30003
	RecallQueueId_MusicChannelKTV_EP                    RecallQueueId = 30004
	RecallQueueId_MusicChannelKTV_FavorSong             RecallQueueId = 30005
	RecallQueueId_MusicChannelKTV_HotSong               RecallQueueId = 30006
	RecallQueueId_MusicChannelListenSong                RecallQueueId = 30007
	RecallQueueId_MusicChannelRapTogether               RecallQueueId = 30008
	RecallQueueId_MusicChannelSingAndChat               RecallQueueId = 30009
	RecallQueueId_MusicChannelMakeFriendSinging         RecallQueueId = 30010
	RecallQueueId_MusicChannelUnknown                   RecallQueueId = 30011
	RecallQueueId_MusicChannelUCUS_SK                   RecallQueueId = 30012
	RecallQueueId_MusicChannelUCUS_CC                   RecallQueueId = 30013
	RecallQueueId_MusicChannelUCUS_IU                   RecallQueueId = 30014
	RecallQueueId_MusicChannelListenSong_HQ             RecallQueueId = 30015
	RecallQueueId_MusicChannelRapTogether_HQ            RecallQueueId = 30016
	RecallQueueId_MusicChannelSecondLabel               RecallQueueId = 30017
	RecallQueueId_KaiHeiChannel                         RecallQueueId = 30018
	RecallQueueId_MusicChannel_Low_delivery             RecallQueueId = 30019
	RecallQueueId_MusicChannelCollect                   RecallQueueId = 30020
	RecallQueueId_MusicChannelFollow                    RecallQueueId = 30021
	RecallQueueId_MusicChannelOftenStay                 RecallQueueId = 30022
	RecallQueueId_MusicChannelTypeStream                RecallQueueId = 30023
	RecallQueueId_MusicChannelRecallAll                 RecallQueueId = 30024
	RecallQueueId_MusicChannelSelectPage                RecallQueueId = 30025
	RecallQueueId_MusicChannelPositive                  RecallQueueId = 30026
	RecallQueueId_MusicChannelRelation                  RecallQueueId = 30027
	RecallQueueId_MusicChannelRelationRoomOwner         RecallQueueId = 30028
	RecallQueueId_MusicChannelRelationMicUser           RecallQueueId = 30029
	RecallQueueId_MusicChannelRelationSendIM            RecallQueueId = 30030
	RecallQueueId_MusicChannelRelationAlgoIM            RecallQueueId = 30031
	RecallQueueId_MusicChannelRelationAlgoMic           RecallQueueId = 30032
	RecallQueueId_MusicChannelRelationAlgoMsg           RecallQueueId = 30033
	RecallQueueId_MusicChannelRelationAlgoStay          RecallQueueId = 30034
	RecallQueueId_MusicChannelRelationAlgoNone          RecallQueueId = 30035
	RecallQueueId_MusicChannelMicBand                   RecallQueueId = 30036
	RecallQueueId_MusicChannelNewUser                   RecallQueueId = 30037
	RecallQueueId_MusicChannelHighFollow                RecallQueueId = 30038
	RecallQueueId_MusicChannelHighMic10s                RecallQueueId = 30039
	RecallQueueId_MusicChannelRelationAlgoIMNebula_CS   RecallQueueId = 30040
	RecallQueueId_MusicChannelRelationAlgoMicNebula_CS  RecallQueueId = 30041
	RecallQueueId_MusicChannelRelationAlgoMsgNebula_CS  RecallQueueId = 30042
	RecallQueueId_MusicChannelRelationAlgoStayNebula_CS RecallQueueId = 30043
	RecallQueueId_MusicChannelRelationAlgoNoneNebula_CS RecallQueueId = 30044
	RecallQueueId_MusicChannelRelationAlgoIMNebula_RD   RecallQueueId = 30045
	RecallQueueId_MusicChannelRelationAlgoMicNebula_RD  RecallQueueId = 30046
	RecallQueueId_MusicChannelRelationAlgoMsgNebula_RD  RecallQueueId = 30047
	RecallQueueId_MusicChannelRelationAlgoStayNebula_RD RecallQueueId = 30048
	RecallQueueId_MusicChannelRelationAlgoNoneNebula_RD RecallQueueId = 30049
	RecallQueueId_SquareMixRecallKuoLie                 RecallQueueId = 30101
	RecallQueueId_SquareMixRecallStory                  RecallQueueId = 30102
	RecallQueueId_SquareMixRecallRadioPlay              RecallQueueId = 30103
	RecallQueueId_SquareMixRecallTogetherPia            RecallQueueId = 30104
	RecallQueueId_SquareMixRecallFunny                  RecallQueueId = 30105
	RecallQueueId_SquareMixRecallConstellation          RecallQueueId = 30106
	RecallQueueId_SquareMixRecallHotSpot                RecallQueueId = 30107
	RecallQueueId_SquareMixRecallStudy                  RecallQueueId = 30108
	RecallQueueId_SquareMixRecallDraw                   RecallQueueId = 30109
	RecallQueueId_SquareMixRecallEmotion                RecallQueueId = 30110
	RecallQueueId_SquareMixRecallVoiceControl           RecallQueueId = 30111
	RecallQueueId_SquareMixRecallSameCity               RecallQueueId = 30112
	RecallQueueId_SquareMixRecallRap                    RecallQueueId = 30113
	RecallQueueId_SquareMixRecallSing                   RecallQueueId = 30114
	RecallQueueId_SquareMixRecallArgue                  RecallQueueId = 30115
	RecallQueueId_SquareMixRecallCommon                 RecallQueueId = 30116
	RecallQueueId_HomeMixedRecallAll                    RecallQueueId = 30201
	RecallQueueId_HomeMixedRecallSimilar                RecallQueueId = 30202
	RecallQueueId_ThemePopRecall                        RecallQueueId = 30301
	RecallQueueId_SameCityOwner                         RecallQueueId = 30401
	RecallQueueId_SameCitySpare                         RecallQueueId = 30402
	RecallQueueId_TtcRoomRecallAll                      RecallQueueId = 40000
	RecallQueueId_TtcRoomRecallGameCard                 RecallQueueId = 40001
	// rcmd-userr-pick
	RecallQueueId_UserPickPiecingRecallFriends        RecallQueueId = 45001
	RecallQueueId_UserPickPiecingRecallPlaymates      RecallQueueId = 45002
	RecallQueueId_UserPickPiecingRecallHighPraised    RecallQueueId = 45003
	RecallQueueId_UserPickPiecingRecallNotComplete    RecallQueueId = 45004
	RecallQueueId_UserPickPiecingRecallEverJoin       RecallQueueId = 45005
	RecallQueueId_UserPickPiecingRecallPlayAny        RecallQueueId = 45006
	RecallQueueId_UserPickPiecingRecallHighPraisedNew RecallQueueId = 45007
	RecallQueueId_UserPickPiecingRecallNotCompleteNew RecallQueueId = 45008
	RecallQueueId_UserPickPiecingRecallEverJoinNew    RecallQueueId = 45009
	RecallQueueId_UserPickPiecingRecallPlayAnyNew     RecallQueueId = 45010
	RecallQueueId_UserPickPiecingRecallEnterGame      RecallQueueId = 45011
	RecallQueueId_UserPickPiecingRecallFitLowest      RecallQueueId = 45012
	RecallQueueId_UserPickPiecingRecallWaitingChannel RecallQueueId = 45013
	RecallQueueId_UserPickTimerRecallOne              RecallQueueId = 45021
	RecallQueueId_UserPickTimerRecallTwo              RecallQueueId = 45022
	RecallQueueId_UserPickTimerRecallThree            RecallQueueId = 45023
	// 你行你唱歌曲
	RecallQueueId_MusicSongUCUS                     RecallQueueId = 50000
	RecallQueueId_MusicSongUCUS_NewSong             RecallQueueId = 50001
	RecallQueueId_MusicSongKTV_Like                 RecallQueueId = 50011
	RecallQueueId_MusicSongKTV_Hot                  RecallQueueId = 50012
	RecallQueueId_MusicSongKTV_Rule_Hot             RecallQueueId = 50013
	RecallQueueId_MusicSongKTV_User_Song            RecallQueueId = 50051
	RecallQueueId_MusicSongKTV_User_Have_Sing       RecallQueueId = 50052
	RecallQueueId_MusicSongKTV_List_Custom          RecallQueueId = 50014
	RecallQueueId_MusicSongKTV_List_Zego            RecallQueueId = 50015
	RecallQueueId_MusicSongKTV_List_Rule_Zego       RecallQueueId = 50016
	RecallQueueId_MusicSongKTV_List_Hot             RecallQueueId = 50017
	RecallQueueId_MusicSongKTV_Block_Custom         RecallQueueId = 50018
	RecallQueueId_MusicSongKTV_Block_Zego           RecallQueueId = 50019
	RecallQueueId_MusicSongKTV_Block_Rule_Zego      RecallQueueId = 50020
	RecallQueueId_MusicSongKTV_Block_Hot            RecallQueueId = 50021
	RecallQueueId_MusicSong_Listen_Order_Preference RecallQueueId = 50031
	RecallQueueId_MusicSong_Listen_Order_Hot        RecallQueueId = 50032
	RecallQueueId_MusicSong_Listen_Order_Rule_Hot   RecallQueueId = 50033
	RecallQueueId_MusicSong_Listen_Order_Explore    RecallQueueId = 50034
	RecallQueueId_MusicSong_Listen_Order_New_Hot    RecallQueueId = 50035
	// 预约开黑
	RecallQueueId_TopicChannelAppointmentFilter RecallQueueId = 60000
	// 速配-小游戏
	RecallQueueId_MiniGame_GeneralQueue RecallQueueId = 70001
	// anti-faker反冒充 uid召回
	RecallQueueId_AntiFakerNameSearchRecall RecallQueueId = 80000
	// rcmd-chat 仿真用户召回
	RecallQueueId_RCMDChat_GeneralQueue RecallQueueId = 71000
	// 游戏帖
	RecallQueueId_RCMDGamePost_GeneralQueue         RecallQueueId = 72001
	RecallQueueId_RCMDGamePost_ForceInsertQueue     RecallQueueId = 72002
	RecallQueueId_RCMDGamePost_NewPostQueue         RecallQueueId = 72003
	RecallQueueId_RCMDGamePost_PublishTimeSortQueue RecallQueueId = 72004
	// rcmd-game-pal-card
	RecallQueueId_RcmdGamePalCardPreferQueue      RecallQueueId = 73001
	RecallQueueId_RcmdGamePalCardTimelinessQueue  RecallQueueId = 73002
	RecallQueueId_RcmdGamePalCardInteractionQueue RecallQueueId = 73003
	RecallQueueId_RcmdGamePalCardFallbackQueue    RecallQueueId = 73004
	// 帖子搜索
	RecallQueueId_RCMDPostSearch_GeneralQueue        RecallQueueId = 74001
	RecallQueueId_RCMDPostSearch_BoutiqueQueue       RecallQueueId = 74002
	RecallQueueId_RCMDPostSearch_ForceInsertQueue    RecallQueueId = 74003
	RecallQueueId_RCMDPostSearch_FullMatchTopicQueue RecallQueueId = 74004
)

var RecallQueueId_name = map[int32]string{
	0:     "UnknownRecallQueue",
	10000: "NewPoolRecall",
	10001: "NewMidPoolRecall",
	10002: "HighInteractPoolRecall",
	10003: "PostAlgoOfflineRecall10",
	10004: "PostAlgoOfflineRecallHighQuality",
	10005: "PostAlgoOfflineRecall11",
	10006: "PostAlgoOfflineRecallHot",
	10007: "PostAlgoOnlineItemCF",
	10008: "PostAlgoOnlineHotGlobal",
	10009: "PostAlgoOnlineItem2Vec",
	10010: "PostAlgoOnlineUser2Vec",
	10011: "PostAlgoOnlineHotRegional",
	10012: "PostAlgoOfflineUserCF",
	10013: "PostAlgoOfflineRelationChain",
	10014: "PostAlgoOnlineUser2Pub2Item",
	10015: "PostAlgoOnlineUser2PubVec2Item",
	10016: "NewPoolRecall3Min",
	10017: "PostAlgoNewPoolRecall",
	10018: "PostAlgoOnlineBert",
	10019: "PostAlgoHourCDTOM",
	10020: "PostAlgoNearlineHear",
	10021: "PostAlgoOnlineUser2VecPub",
	10022: "PostHeartIslandRecall",
	10023: "PostImgTagHotRecall",
	10024: "PostForMonitorRecall",
	10025: "PostAIRapRecall",
	10026: "PostInterestHotRecall",
	10027: "PostNewPreferRecall",
	10028: "PostNewInterestRecall",
	10029: "PostNewNotInterestRecall",
	10030: "PostTopCtrRecall",
	10031: "PostDiscussTopicRecall",
	10501: "PostTimerRecallBoutique",
	11000: "PostTopicRecall",
	11001: "PostTopicVoiceControlStreamRecall",
	11002: "PostTopicLiveRecall",
	11003: "PostTopicVideoAudioRecall",
	12001: "PostSameCityPreferRecall",
	12002: "PostSameCityInterestRecall",
	12003: "PostSameCityFocusInterestRecall",
	12004: "PostSameCityNonInterestRecall",
	12005: "PostSameProvPreferRecall",
	12006: "PostSameProvInterestRecall",
	12007: "PostSameProvFocusInterestRecall",
	12008: "PostSameProvNonInterestRecall",
	12009: "PostSameCityAsyncCacheRecall",
	20000: "LiveChannelRecall",
	21000: "ChatCardRecall",
	22000: "PerfectMatchQuestion",
	30000: "MusicChannelUCanUSing",
	30001: "MusicChannelUCUS_EP",
	30002: "MusicChannelKTV",
	30003: "MusicChannelKTV_HQ",
	30004: "MusicChannelKTV_EP",
	30005: "MusicChannelKTV_FavorSong",
	30006: "MusicChannelKTV_HotSong",
	30007: "MusicChannelListenSong",
	30008: "MusicChannelRapTogether",
	30009: "MusicChannelSingAndChat",
	30010: "MusicChannelMakeFriendSinging",
	30011: "MusicChannelUnknown",
	30012: "MusicChannelUCUS_SK",
	30013: "MusicChannelUCUS_CC",
	30014: "MusicChannelUCUS_IU",
	30015: "MusicChannelListenSong_HQ",
	30016: "MusicChannelRapTogether_HQ",
	30017: "MusicChannelSecondLabel",
	30018: "KaiHeiChannel",
	30019: "MusicChannel_Low_delivery",
	30020: "MusicChannelCollect",
	30021: "MusicChannelFollow",
	30022: "MusicChannelOftenStay",
	30023: "MusicChannelTypeStream",
	30024: "MusicChannelRecallAll",
	30025: "MusicChannelSelectPage",
	30026: "MusicChannelPositive",
	30027: "MusicChannelRelation",
	30028: "MusicChannelRelationRoomOwner",
	30029: "MusicChannelRelationMicUser",
	30030: "MusicChannelRelationSendIM",
	30031: "MusicChannelRelationAlgoIM",
	30032: "MusicChannelRelationAlgoMic",
	30033: "MusicChannelRelationAlgoMsg",
	30034: "MusicChannelRelationAlgoStay",
	30035: "MusicChannelRelationAlgoNone",
	30036: "MusicChannelMicBand",
	30037: "MusicChannelNewUser",
	30038: "MusicChannelHighFollow",
	30039: "MusicChannelHighMic10s",
	30040: "MusicChannelRelationAlgoIMNebula_CS",
	30041: "MusicChannelRelationAlgoMicNebula_CS",
	30042: "MusicChannelRelationAlgoMsgNebula_CS",
	30043: "MusicChannelRelationAlgoStayNebula_CS",
	30044: "MusicChannelRelationAlgoNoneNebula_CS",
	30045: "MusicChannelRelationAlgoIMNebula_RD",
	30046: "MusicChannelRelationAlgoMicNebula_RD",
	30047: "MusicChannelRelationAlgoMsgNebula_RD",
	30048: "MusicChannelRelationAlgoStayNebula_RD",
	30049: "MusicChannelRelationAlgoNoneNebula_RD",
	30101: "SquareMixRecallKuoLie",
	30102: "SquareMixRecallStory",
	30103: "SquareMixRecallRadioPlay",
	30104: "SquareMixRecallTogetherPia",
	30105: "SquareMixRecallFunny",
	30106: "SquareMixRecallConstellation",
	30107: "SquareMixRecallHotSpot",
	30108: "SquareMixRecallStudy",
	30109: "SquareMixRecallDraw",
	30110: "SquareMixRecallEmotion",
	30111: "SquareMixRecallVoiceControl",
	30112: "SquareMixRecallSameCity",
	30113: "SquareMixRecallRap",
	30114: "SquareMixRecallSing",
	30115: "SquareMixRecallArgue",
	30116: "SquareMixRecallCommon",
	30201: "HomeMixedRecallAll",
	30202: "HomeMixedRecallSimilar",
	30301: "ThemePopRecall",
	30401: "SameCityOwner",
	30402: "SameCitySpare",
	40000: "TtcRoomRecallAll",
	40001: "TtcRoomRecallGameCard",
	45001: "UserPickPiecingRecallFriends",
	45002: "UserPickPiecingRecallPlaymates",
	45003: "UserPickPiecingRecallHighPraised",
	45004: "UserPickPiecingRecallNotComplete",
	45005: "UserPickPiecingRecallEverJoin",
	45006: "UserPickPiecingRecallPlayAny",
	45007: "UserPickPiecingRecallHighPraisedNew",
	45008: "UserPickPiecingRecallNotCompleteNew",
	45009: "UserPickPiecingRecallEverJoinNew",
	45010: "UserPickPiecingRecallPlayAnyNew",
	45011: "UserPickPiecingRecallEnterGame",
	45012: "UserPickPiecingRecallFitLowest",
	45013: "UserPickPiecingRecallWaitingChannel",
	45021: "UserPickTimerRecallOne",
	45022: "UserPickTimerRecallTwo",
	45023: "UserPickTimerRecallThree",
	50000: "MusicSongUCUS",
	50001: "MusicSongUCUS_NewSong",
	50011: "MusicSongKTV_Like",
	50012: "MusicSongKTV_Hot",
	50013: "MusicSongKTV_Rule_Hot",
	50051: "MusicSongKTV_User_Song",
	50052: "MusicSongKTV_User_Have_Sing",
	50014: "MusicSongKTV_List_Custom",
	50015: "MusicSongKTV_List_Zego",
	50016: "MusicSongKTV_List_Rule_Zego",
	50017: "MusicSongKTV_List_Hot",
	50018: "MusicSongKTV_Block_Custom",
	50019: "MusicSongKTV_Block_Zego",
	50020: "MusicSongKTV_Block_Rule_Zego",
	50021: "MusicSongKTV_Block_Hot",
	50031: "MusicSong_Listen_Order_Preference",
	50032: "MusicSong_Listen_Order_Hot",
	50033: "MusicSong_Listen_Order_Rule_Hot",
	50034: "MusicSong_Listen_Order_Explore",
	50035: "MusicSong_Listen_Order_New_Hot",
	60000: "TopicChannelAppointmentFilter",
	70001: "MiniGame_GeneralQueue",
	80000: "AntiFakerNameSearchRecall",
	71000: "RCMDChat_GeneralQueue",
	72001: "RCMDGamePost_GeneralQueue",
	72002: "RCMDGamePost_ForceInsertQueue",
	72003: "RCMDGamePost_NewPostQueue",
	72004: "RCMDGamePost_PublishTimeSortQueue",
	73001: "RcmdGamePalCardPreferQueue",
	73002: "RcmdGamePalCardTimelinessQueue",
	73003: "RcmdGamePalCardInteractionQueue",
	73004: "RcmdGamePalCardFallbackQueue",
	74001: "RCMDPostSearch_GeneralQueue",
	74002: "RCMDPostSearch_BoutiqueQueue",
	74003: "RCMDPostSearch_ForceInsertQueue",
	74004: "RCMDPostSearch_FullMatchTopicQueue",
}
var RecallQueueId_value = map[string]int32{
	"UnknownRecallQueue":                    0,
	"NewPoolRecall":                         10000,
	"NewMidPoolRecall":                      10001,
	"HighInteractPoolRecall":                10002,
	"PostAlgoOfflineRecall10":               10003,
	"PostAlgoOfflineRecallHighQuality":      10004,
	"PostAlgoOfflineRecall11":               10005,
	"PostAlgoOfflineRecallHot":              10006,
	"PostAlgoOnlineItemCF":                  10007,
	"PostAlgoOnlineHotGlobal":               10008,
	"PostAlgoOnlineItem2Vec":                10009,
	"PostAlgoOnlineUser2Vec":                10010,
	"PostAlgoOnlineHotRegional":             10011,
	"PostAlgoOfflineUserCF":                 10012,
	"PostAlgoOfflineRelationChain":          10013,
	"PostAlgoOnlineUser2Pub2Item":           10014,
	"PostAlgoOnlineUser2PubVec2Item":        10015,
	"NewPoolRecall3Min":                     10016,
	"PostAlgoNewPoolRecall":                 10017,
	"PostAlgoOnlineBert":                    10018,
	"PostAlgoHourCDTOM":                     10019,
	"PostAlgoNearlineHear":                  10020,
	"PostAlgoOnlineUser2VecPub":             10021,
	"PostHeartIslandRecall":                 10022,
	"PostImgTagHotRecall":                   10023,
	"PostForMonitorRecall":                  10024,
	"PostAIRapRecall":                       10025,
	"PostInterestHotRecall":                 10026,
	"PostNewPreferRecall":                   10027,
	"PostNewInterestRecall":                 10028,
	"PostNewNotInterestRecall":              10029,
	"PostTopCtrRecall":                      10030,
	"PostDiscussTopicRecall":                10031,
	"PostTimerRecallBoutique":               10501,
	"PostTopicRecall":                       11000,
	"PostTopicVoiceControlStreamRecall":     11001,
	"PostTopicLiveRecall":                   11002,
	"PostTopicVideoAudioRecall":             11003,
	"PostSameCityPreferRecall":              12001,
	"PostSameCityInterestRecall":            12002,
	"PostSameCityFocusInterestRecall":       12003,
	"PostSameCityNonInterestRecall":         12004,
	"PostSameProvPreferRecall":              12005,
	"PostSameProvInterestRecall":            12006,
	"PostSameProvFocusInterestRecall":       12007,
	"PostSameProvNonInterestRecall":         12008,
	"PostSameCityAsyncCacheRecall":          12009,
	"LiveChannelRecall":                     20000,
	"ChatCardRecall":                        21000,
	"PerfectMatchQuestion":                  22000,
	"MusicChannelUCanUSing":                 30000,
	"MusicChannelUCUS_EP":                   30001,
	"MusicChannelKTV":                       30002,
	"MusicChannelKTV_HQ":                    30003,
	"MusicChannelKTV_EP":                    30004,
	"MusicChannelKTV_FavorSong":             30005,
	"MusicChannelKTV_HotSong":               30006,
	"MusicChannelListenSong":                30007,
	"MusicChannelRapTogether":               30008,
	"MusicChannelSingAndChat":               30009,
	"MusicChannelMakeFriendSinging":         30010,
	"MusicChannelUnknown":                   30011,
	"MusicChannelUCUS_SK":                   30012,
	"MusicChannelUCUS_CC":                   30013,
	"MusicChannelUCUS_IU":                   30014,
	"MusicChannelListenSong_HQ":             30015,
	"MusicChannelRapTogether_HQ":            30016,
	"MusicChannelSecondLabel":               30017,
	"KaiHeiChannel":                         30018,
	"MusicChannel_Low_delivery":             30019,
	"MusicChannelCollect":                   30020,
	"MusicChannelFollow":                    30021,
	"MusicChannelOftenStay":                 30022,
	"MusicChannelTypeStream":                30023,
	"MusicChannelRecallAll":                 30024,
	"MusicChannelSelectPage":                30025,
	"MusicChannelPositive":                  30026,
	"MusicChannelRelation":                  30027,
	"MusicChannelRelationRoomOwner":         30028,
	"MusicChannelRelationMicUser":           30029,
	"MusicChannelRelationSendIM":            30030,
	"MusicChannelRelationAlgoIM":            30031,
	"MusicChannelRelationAlgoMic":           30032,
	"MusicChannelRelationAlgoMsg":           30033,
	"MusicChannelRelationAlgoStay":          30034,
	"MusicChannelRelationAlgoNone":          30035,
	"MusicChannelMicBand":                   30036,
	"MusicChannelNewUser":                   30037,
	"MusicChannelHighFollow":                30038,
	"MusicChannelHighMic10s":                30039,
	"MusicChannelRelationAlgoIMNebula_CS":   30040,
	"MusicChannelRelationAlgoMicNebula_CS":  30041,
	"MusicChannelRelationAlgoMsgNebula_CS":  30042,
	"MusicChannelRelationAlgoStayNebula_CS": 30043,
	"MusicChannelRelationAlgoNoneNebula_CS": 30044,
	"MusicChannelRelationAlgoIMNebula_RD":   30045,
	"MusicChannelRelationAlgoMicNebula_RD":  30046,
	"MusicChannelRelationAlgoMsgNebula_RD":  30047,
	"MusicChannelRelationAlgoStayNebula_RD": 30048,
	"MusicChannelRelationAlgoNoneNebula_RD": 30049,
	"SquareMixRecallKuoLie":                 30101,
	"SquareMixRecallStory":                  30102,
	"SquareMixRecallRadioPlay":              30103,
	"SquareMixRecallTogetherPia":            30104,
	"SquareMixRecallFunny":                  30105,
	"SquareMixRecallConstellation":          30106,
	"SquareMixRecallHotSpot":                30107,
	"SquareMixRecallStudy":                  30108,
	"SquareMixRecallDraw":                   30109,
	"SquareMixRecallEmotion":                30110,
	"SquareMixRecallVoiceControl":           30111,
	"SquareMixRecallSameCity":               30112,
	"SquareMixRecallRap":                    30113,
	"SquareMixRecallSing":                   30114,
	"SquareMixRecallArgue":                  30115,
	"SquareMixRecallCommon":                 30116,
	"HomeMixedRecallAll":                    30201,
	"HomeMixedRecallSimilar":                30202,
	"ThemePopRecall":                        30301,
	"SameCityOwner":                         30401,
	"SameCitySpare":                         30402,
	"TtcRoomRecallAll":                      40000,
	"TtcRoomRecallGameCard":                 40001,
	"UserPickPiecingRecallFriends":          45001,
	"UserPickPiecingRecallPlaymates":        45002,
	"UserPickPiecingRecallHighPraised":      45003,
	"UserPickPiecingRecallNotComplete":      45004,
	"UserPickPiecingRecallEverJoin":         45005,
	"UserPickPiecingRecallPlayAny":          45006,
	"UserPickPiecingRecallHighPraisedNew":   45007,
	"UserPickPiecingRecallNotCompleteNew":   45008,
	"UserPickPiecingRecallEverJoinNew":      45009,
	"UserPickPiecingRecallPlayAnyNew":       45010,
	"UserPickPiecingRecallEnterGame":        45011,
	"UserPickPiecingRecallFitLowest":        45012,
	"UserPickPiecingRecallWaitingChannel":   45013,
	"UserPickTimerRecallOne":                45021,
	"UserPickTimerRecallTwo":                45022,
	"UserPickTimerRecallThree":              45023,
	"MusicSongUCUS":                         50000,
	"MusicSongUCUS_NewSong":                 50001,
	"MusicSongKTV_Like":                     50011,
	"MusicSongKTV_Hot":                      50012,
	"MusicSongKTV_Rule_Hot":                 50013,
	"MusicSongKTV_User_Song":                50051,
	"MusicSongKTV_User_Have_Sing":           50052,
	"MusicSongKTV_List_Custom":              50014,
	"MusicSongKTV_List_Zego":                50015,
	"MusicSongKTV_List_Rule_Zego":           50016,
	"MusicSongKTV_List_Hot":                 50017,
	"MusicSongKTV_Block_Custom":             50018,
	"MusicSongKTV_Block_Zego":               50019,
	"MusicSongKTV_Block_Rule_Zego":          50020,
	"MusicSongKTV_Block_Hot":                50021,
	"MusicSong_Listen_Order_Preference":     50031,
	"MusicSong_Listen_Order_Hot":            50032,
	"MusicSong_Listen_Order_Rule_Hot":       50033,
	"MusicSong_Listen_Order_Explore":        50034,
	"MusicSong_Listen_Order_New_Hot":        50035,
	"TopicChannelAppointmentFilter":         60000,
	"MiniGame_GeneralQueue":                 70001,
	"AntiFakerNameSearchRecall":             80000,
	"RCMDChat_GeneralQueue":                 71000,
	"RCMDGamePost_GeneralQueue":             72001,
	"RCMDGamePost_ForceInsertQueue":         72002,
	"RCMDGamePost_NewPostQueue":             72003,
	"RCMDGamePost_PublishTimeSortQueue":     72004,
	"RcmdGamePalCardPreferQueue":            73001,
	"RcmdGamePalCardTimelinessQueue":        73002,
	"RcmdGamePalCardInteractionQueue":       73003,
	"RcmdGamePalCardFallbackQueue":          73004,
	"RCMDPostSearch_GeneralQueue":           74001,
	"RCMDPostSearch_BoutiqueQueue":          74002,
	"RCMDPostSearch_ForceInsertQueue":       74003,
	"RCMDPostSearch_FullMatchTopicQueue":    74004,
}

func (x RecallQueueId) String() string {
	return proto.EnumName(RecallQueueId_name, int32(x))
}
func (RecallQueueId) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{8}
}

type RegulatoryLevel int32

const (
	RegulatoryLevel_FREE         RegulatoryLevel = 0
	RegulatoryLevel_SIMPLE_MINOR RegulatoryLevel = 1
)

var RegulatoryLevel_name = map[int32]string{
	0: "FREE",
	1: "SIMPLE_MINOR",
}
var RegulatoryLevel_value = map[string]int32{
	"FREE":         0,
	"SIMPLE_MINOR": 1,
}

func (x RegulatoryLevel) String() string {
	return proto.EnumName(RegulatoryLevel_name, int32(x))
}
func (RegulatoryLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{9}
}

type LocationInfo_AreaLevel int32

const (
	LocationInfo_AreaLevel_Normal    LocationInfo_AreaLevel = 0
	LocationInfo_AreaLevel_Sensitive LocationInfo_AreaLevel = 1
)

var LocationInfo_AreaLevel_name = map[int32]string{
	0: "AreaLevel_Normal",
	1: "AreaLevel_Sensitive",
}
var LocationInfo_AreaLevel_value = map[string]int32{
	"AreaLevel_Normal":    0,
	"AreaLevel_Sensitive": 1,
}

func (x LocationInfo_AreaLevel) String() string {
	return proto.EnumName(LocationInfo_AreaLevel_name, int32(x))
}
func (LocationInfo_AreaLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{12, 0}
}

// ////////召回用的配置
// 召回队列
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type RecallQ struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Ratio                uint32   `protobuf:"varint,4,opt,name=ratio,proto3" json:"ratio,omitempty"`
	Priority             uint32   `protobuf:"varint,5,opt,name=priority,proto3" json:"priority,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallQ) Reset()         { *m = RecallQ{} }
func (m *RecallQ) String() string { return proto.CompactTextString(m) }
func (*RecallQ) ProtoMessage()    {}
func (*RecallQ) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{0}
}
func (m *RecallQ) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallQ.Unmarshal(m, b)
}
func (m *RecallQ) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallQ.Marshal(b, m, deterministic)
}
func (dst *RecallQ) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallQ.Merge(dst, src)
}
func (m *RecallQ) XXX_Size() int {
	return xxx_messageInfo_RecallQ.Size(m)
}
func (m *RecallQ) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallQ.DiscardUnknown(m)
}

var xxx_messageInfo_RecallQ proto.InternalMessageInfo

func (m *RecallQ) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RecallQ) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RecallQ) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *RecallQ) GetRatio() uint32 {
	if m != nil {
		return m.Ratio
	}
	return 0
}

func (m *RecallQ) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

// 设置召回策略时，ab测填写的json参数，后面由算法同事自己配
type RecallConfig struct {
	RecallQueueList []*RecallQ `protobuf:"bytes,1,rep,name=recall_queue_list,json=recallQueueList,proto3" json:"recall_queue_list,omitempty"`
	ConfigType      uint32     `protobuf:"varint,2,opt,name=config_type,json=configType,proto3" json:"config_type,omitempty"`
	// 召回总量，只有按比例召回(config_type=2)，或者有优先级的情况下，此字段才生效
	Total                uint32   `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallConfig) Reset()         { *m = RecallConfig{} }
func (m *RecallConfig) String() string { return proto.CompactTextString(m) }
func (*RecallConfig) ProtoMessage()    {}
func (*RecallConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{1}
}
func (m *RecallConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallConfig.Unmarshal(m, b)
}
func (m *RecallConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallConfig.Marshal(b, m, deterministic)
}
func (dst *RecallConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallConfig.Merge(dst, src)
}
func (m *RecallConfig) XXX_Size() int {
	return xxx_messageInfo_RecallConfig.Size(m)
}
func (m *RecallConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallConfig.DiscardUnknown(m)
}

var xxx_messageInfo_RecallConfig proto.InternalMessageInfo

func (m *RecallConfig) GetRecallQueueList() []*RecallQ {
	if m != nil {
		return m.RecallQueueList
	}
	return nil
}

func (m *RecallConfig) GetConfigType() uint32 {
	if m != nil {
		return m.ConfigType
	}
	return 0
}

func (m *RecallConfig) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type RankConfig struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RankConfig) Reset()         { *m = RankConfig{} }
func (m *RankConfig) String() string { return proto.CompactTextString(m) }
func (*RankConfig) ProtoMessage()    {}
func (*RankConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{2}
}
func (m *RankConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RankConfig.Unmarshal(m, b)
}
func (m *RankConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RankConfig.Marshal(b, m, deterministic)
}
func (dst *RankConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RankConfig.Merge(dst, src)
}
func (m *RankConfig) XXX_Size() int {
	return xxx_messageInfo_RankConfig.Size(m)
}
func (m *RankConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_RankConfig.DiscardUnknown(m)
}

var xxx_messageInfo_RankConfig proto.InternalMessageInfo

func (m *RankConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RankConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type RecallRankConfig struct {
	RecallConfig         *RecallConfig `protobuf:"bytes,1,opt,name=recall_config,json=recallConfig,proto3" json:"recall_config,omitempty"`
	RankConfig           *RankConfig   `protobuf:"bytes,2,opt,name=rank_config,json=rankConfig,proto3" json:"rank_config,omitempty"`
	ExpName              string        `protobuf:"bytes,3,opt,name=exp_name,json=expName,proto3" json:"exp_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *RecallRankConfig) Reset()         { *m = RecallRankConfig{} }
func (m *RecallRankConfig) String() string { return proto.CompactTextString(m) }
func (*RecallRankConfig) ProtoMessage()    {}
func (*RecallRankConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{3}
}
func (m *RecallRankConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallRankConfig.Unmarshal(m, b)
}
func (m *RecallRankConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallRankConfig.Marshal(b, m, deterministic)
}
func (dst *RecallRankConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallRankConfig.Merge(dst, src)
}
func (m *RecallRankConfig) XXX_Size() int {
	return xxx_messageInfo_RecallRankConfig.Size(m)
}
func (m *RecallRankConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallRankConfig.DiscardUnknown(m)
}

var xxx_messageInfo_RecallRankConfig proto.InternalMessageInfo

func (m *RecallRankConfig) GetRecallConfig() *RecallConfig {
	if m != nil {
		return m.RecallConfig
	}
	return nil
}

func (m *RecallRankConfig) GetRankConfig() *RankConfig {
	if m != nil {
		return m.RankConfig
	}
	return nil
}

func (m *RecallRankConfig) GetExpName() string {
	if m != nil {
		return m.ExpName
	}
	return ""
}

type LayerInfo struct {
	LayerId              uint32   `protobuf:"varint,1,opt,name=layer_id,json=layerId,proto3" json:"layer_id,omitempty"`
	Value                string   `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	Namespace            string   `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LayerInfo) Reset()         { *m = LayerInfo{} }
func (m *LayerInfo) String() string { return proto.CompactTextString(m) }
func (*LayerInfo) ProtoMessage()    {}
func (*LayerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{4}
}
func (m *LayerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LayerInfo.Unmarshal(m, b)
}
func (m *LayerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LayerInfo.Marshal(b, m, deterministic)
}
func (dst *LayerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LayerInfo.Merge(dst, src)
}
func (m *LayerInfo) XXX_Size() int {
	return xxx_messageInfo_LayerInfo.Size(m)
}
func (m *LayerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LayerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LayerInfo proto.InternalMessageInfo

func (m *LayerInfo) GetLayerId() uint32 {
	if m != nil {
		return m.LayerId
	}
	return 0
}

func (m *LayerInfo) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func (m *LayerInfo) GetNamespace() string {
	if m != nil {
		return m.Namespace
	}
	return ""
}

type ABTestConfigJson struct {
	Layers               map[uint32]*LayerInfo `protobuf:"bytes,1,rep,name=layers,proto3" json:"layers,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ABTestConfigJson) Reset()         { *m = ABTestConfigJson{} }
func (m *ABTestConfigJson) String() string { return proto.CompactTextString(m) }
func (*ABTestConfigJson) ProtoMessage()    {}
func (*ABTestConfigJson) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{5}
}
func (m *ABTestConfigJson) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ABTestConfigJson.Unmarshal(m, b)
}
func (m *ABTestConfigJson) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ABTestConfigJson.Marshal(b, m, deterministic)
}
func (dst *ABTestConfigJson) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ABTestConfigJson.Merge(dst, src)
}
func (m *ABTestConfigJson) XXX_Size() int {
	return xxx_messageInfo_ABTestConfigJson.Size(m)
}
func (m *ABTestConfigJson) XXX_DiscardUnknown() {
	xxx_messageInfo_ABTestConfigJson.DiscardUnknown(m)
}

var xxx_messageInfo_ABTestConfigJson proto.InternalMessageInfo

func (m *ABTestConfigJson) GetLayers() map[uint32]*LayerInfo {
	if m != nil {
		return m.Layers
	}
	return nil
}

type RcmdBaseReq struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ConfigJson           *ABTestConfigJson `protobuf:"bytes,2,opt,name=config_json,json=configJson,proto3" json:"config_json,omitempty"`
	TraceId              string            `protobuf:"bytes,3,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	PassThrough          string            `protobuf:"bytes,4,opt,name=pass_through,json=passThrough,proto3" json:"pass_through,omitempty"`
	DebugFlag            uint32            `protobuf:"varint,5,opt,name=debug_flag,json=debugFlag,proto3" json:"debug_flag,omitempty"`
	AbTestInfo           *ABTestInfo       `protobuf:"bytes,6,opt,name=ab_test_info,json=abTestInfo,proto3" json:"ab_test_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *RcmdBaseReq) Reset()         { *m = RcmdBaseReq{} }
func (m *RcmdBaseReq) String() string { return proto.CompactTextString(m) }
func (*RcmdBaseReq) ProtoMessage()    {}
func (*RcmdBaseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{6}
}
func (m *RcmdBaseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RcmdBaseReq.Unmarshal(m, b)
}
func (m *RcmdBaseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RcmdBaseReq.Marshal(b, m, deterministic)
}
func (dst *RcmdBaseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RcmdBaseReq.Merge(dst, src)
}
func (m *RcmdBaseReq) XXX_Size() int {
	return xxx_messageInfo_RcmdBaseReq.Size(m)
}
func (m *RcmdBaseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RcmdBaseReq.DiscardUnknown(m)
}

var xxx_messageInfo_RcmdBaseReq proto.InternalMessageInfo

func (m *RcmdBaseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RcmdBaseReq) GetConfigJson() *ABTestConfigJson {
	if m != nil {
		return m.ConfigJson
	}
	return nil
}

func (m *RcmdBaseReq) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *RcmdBaseReq) GetPassThrough() string {
	if m != nil {
		return m.PassThrough
	}
	return ""
}

func (m *RcmdBaseReq) GetDebugFlag() uint32 {
	if m != nil {
		return m.DebugFlag
	}
	return 0
}

func (m *RcmdBaseReq) GetAbTestInfo() *ABTestInfo {
	if m != nil {
		return m.AbTestInfo
	}
	return nil
}

type TopicChannelBaseReq struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ConfigJson           *ABTestConfigJson `protobuf:"bytes,2,opt,name=config_json,json=configJson,proto3" json:"config_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *TopicChannelBaseReq) Reset()         { *m = TopicChannelBaseReq{} }
func (m *TopicChannelBaseReq) String() string { return proto.CompactTextString(m) }
func (*TopicChannelBaseReq) ProtoMessage()    {}
func (*TopicChannelBaseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{7}
}
func (m *TopicChannelBaseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicChannelBaseReq.Unmarshal(m, b)
}
func (m *TopicChannelBaseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicChannelBaseReq.Marshal(b, m, deterministic)
}
func (dst *TopicChannelBaseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicChannelBaseReq.Merge(dst, src)
}
func (m *TopicChannelBaseReq) XXX_Size() int {
	return xxx_messageInfo_TopicChannelBaseReq.Size(m)
}
func (m *TopicChannelBaseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicChannelBaseReq.DiscardUnknown(m)
}

var xxx_messageInfo_TopicChannelBaseReq proto.InternalMessageInfo

func (m *TopicChannelBaseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TopicChannelBaseReq) GetConfigJson() *ABTestConfigJson {
	if m != nil {
		return m.ConfigJson
	}
	return nil
}

type RcmdBaseRsp struct {
	Code                 uint32            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string            `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	ProfileInfo          map[string]string `protobuf:"bytes,3,rep,name=profile_info,json=profileInfo,proto3" json:"profile_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *RcmdBaseRsp) Reset()         { *m = RcmdBaseRsp{} }
func (m *RcmdBaseRsp) String() string { return proto.CompactTextString(m) }
func (*RcmdBaseRsp) ProtoMessage()    {}
func (*RcmdBaseRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{8}
}
func (m *RcmdBaseRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RcmdBaseRsp.Unmarshal(m, b)
}
func (m *RcmdBaseRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RcmdBaseRsp.Marshal(b, m, deterministic)
}
func (dst *RcmdBaseRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RcmdBaseRsp.Merge(dst, src)
}
func (m *RcmdBaseRsp) XXX_Size() int {
	return xxx_messageInfo_RcmdBaseRsp.Size(m)
}
func (m *RcmdBaseRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_RcmdBaseRsp.DiscardUnknown(m)
}

var xxx_messageInfo_RcmdBaseRsp proto.InternalMessageInfo

func (m *RcmdBaseRsp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *RcmdBaseRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *RcmdBaseRsp) GetProfileInfo() map[string]string {
	if m != nil {
		return m.ProfileInfo
	}
	return nil
}

type RcmdCommonDataBatchEvent struct {
	SrcIp                string   `protobuf:"bytes,1,opt,name=src_ip,json=srcIp,proto3" json:"src_ip,omitempty"`
	ServerTimestamp      uint32   `protobuf:"varint,2,opt,name=server_timestamp,json=serverTimestamp,proto3" json:"server_timestamp,omitempty"`
	BizType              string   `protobuf:"bytes,3,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	Data                 [][]byte `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`
	Version              string   `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`
	BatchNum             uint32   `protobuf:"varint,6,opt,name=batch_num,json=batchNum,proto3" json:"batch_num,omitempty"`
	Env                  uint32   `protobuf:"varint,7,opt,name=env,proto3" json:"env,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RcmdCommonDataBatchEvent) Reset()         { *m = RcmdCommonDataBatchEvent{} }
func (m *RcmdCommonDataBatchEvent) String() string { return proto.CompactTextString(m) }
func (*RcmdCommonDataBatchEvent) ProtoMessage()    {}
func (*RcmdCommonDataBatchEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{9}
}
func (m *RcmdCommonDataBatchEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RcmdCommonDataBatchEvent.Unmarshal(m, b)
}
func (m *RcmdCommonDataBatchEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RcmdCommonDataBatchEvent.Marshal(b, m, deterministic)
}
func (dst *RcmdCommonDataBatchEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RcmdCommonDataBatchEvent.Merge(dst, src)
}
func (m *RcmdCommonDataBatchEvent) XXX_Size() int {
	return xxx_messageInfo_RcmdCommonDataBatchEvent.Size(m)
}
func (m *RcmdCommonDataBatchEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_RcmdCommonDataBatchEvent.DiscardUnknown(m)
}

var xxx_messageInfo_RcmdCommonDataBatchEvent proto.InternalMessageInfo

func (m *RcmdCommonDataBatchEvent) GetSrcIp() string {
	if m != nil {
		return m.SrcIp
	}
	return ""
}

func (m *RcmdCommonDataBatchEvent) GetServerTimestamp() uint32 {
	if m != nil {
		return m.ServerTimestamp
	}
	return 0
}

func (m *RcmdCommonDataBatchEvent) GetBizType() string {
	if m != nil {
		return m.BizType
	}
	return ""
}

func (m *RcmdCommonDataBatchEvent) GetData() [][]byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *RcmdCommonDataBatchEvent) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *RcmdCommonDataBatchEvent) GetBatchNum() uint32 {
	if m != nil {
		return m.BatchNum
	}
	return 0
}

func (m *RcmdCommonDataBatchEvent) GetEnv() uint32 {
	if m != nil {
		return m.Env
	}
	return 0
}

type RcmdBrowseInfo struct {
	NoBrowseList         []uint32 `protobuf:"varint,1,rep,packed,name=no_browse_list,json=noBrowseList,proto3" json:"no_browse_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RcmdBrowseInfo) Reset()         { *m = RcmdBrowseInfo{} }
func (m *RcmdBrowseInfo) String() string { return proto.CompactTextString(m) }
func (*RcmdBrowseInfo) ProtoMessage()    {}
func (*RcmdBrowseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{10}
}
func (m *RcmdBrowseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RcmdBrowseInfo.Unmarshal(m, b)
}
func (m *RcmdBrowseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RcmdBrowseInfo.Marshal(b, m, deterministic)
}
func (dst *RcmdBrowseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RcmdBrowseInfo.Merge(dst, src)
}
func (m *RcmdBrowseInfo) XXX_Size() int {
	return xxx_messageInfo_RcmdBrowseInfo.Size(m)
}
func (m *RcmdBrowseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RcmdBrowseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RcmdBrowseInfo proto.InternalMessageInfo

func (m *RcmdBrowseInfo) GetNoBrowseList() []uint32 {
	if m != nil {
		return m.NoBrowseList
	}
	return nil
}

type ABTestInfo struct {
	Info                 map[string]string `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Source               string            `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	IsTotalLayer         bool              `protobuf:"varint,3,opt,name=is_total_layer,json=isTotalLayer,proto3" json:"is_total_layer,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ABTestInfo) Reset()         { *m = ABTestInfo{} }
func (m *ABTestInfo) String() string { return proto.CompactTextString(m) }
func (*ABTestInfo) ProtoMessage()    {}
func (*ABTestInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{11}
}
func (m *ABTestInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ABTestInfo.Unmarshal(m, b)
}
func (m *ABTestInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ABTestInfo.Marshal(b, m, deterministic)
}
func (dst *ABTestInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ABTestInfo.Merge(dst, src)
}
func (m *ABTestInfo) XXX_Size() int {
	return xxx_messageInfo_ABTestInfo.Size(m)
}
func (m *ABTestInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ABTestInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ABTestInfo proto.InternalMessageInfo

func (m *ABTestInfo) GetInfo() map[string]string {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *ABTestInfo) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *ABTestInfo) GetIsTotalLayer() bool {
	if m != nil {
		return m.IsTotalLayer
	}
	return false
}

type LocationInfo struct {
	Country              string                 `protobuf:"bytes,1,opt,name=country,proto3" json:"country,omitempty"`
	Province             string                 `protobuf:"bytes,2,opt,name=province,proto3" json:"province,omitempty"`
	City                 string                 `protobuf:"bytes,3,opt,name=city,proto3" json:"city,omitempty"`
	CountryCode          string                 `protobuf:"bytes,4,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	ProvinceCode         uint32                 `protobuf:"varint,5,opt,name=province_code,json=provinceCode,proto3" json:"province_code,omitempty"`
	CityCode             uint32                 `protobuf:"varint,6,opt,name=city_code,json=cityCode,proto3" json:"city_code,omitempty"`
	ShowGeoInfo          bool                   `protobuf:"varint,7,opt,name=show_geo_info,json=showGeoInfo,proto3" json:"show_geo_info,omitempty"`
	AreaLevel            LocationInfo_AreaLevel `protobuf:"varint,8,opt,name=area_level,json=areaLevel,proto3,enum=rcmd.common.LocationInfo_AreaLevel" json:"area_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *LocationInfo) Reset()         { *m = LocationInfo{} }
func (m *LocationInfo) String() string { return proto.CompactTextString(m) }
func (*LocationInfo) ProtoMessage()    {}
func (*LocationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{12}
}
func (m *LocationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LocationInfo.Unmarshal(m, b)
}
func (m *LocationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LocationInfo.Marshal(b, m, deterministic)
}
func (dst *LocationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LocationInfo.Merge(dst, src)
}
func (m *LocationInfo) XXX_Size() int {
	return xxx_messageInfo_LocationInfo.Size(m)
}
func (m *LocationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LocationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LocationInfo proto.InternalMessageInfo

func (m *LocationInfo) GetCountry() string {
	if m != nil {
		return m.Country
	}
	return ""
}

func (m *LocationInfo) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *LocationInfo) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *LocationInfo) GetCountryCode() string {
	if m != nil {
		return m.CountryCode
	}
	return ""
}

func (m *LocationInfo) GetProvinceCode() uint32 {
	if m != nil {
		return m.ProvinceCode
	}
	return 0
}

func (m *LocationInfo) GetCityCode() uint32 {
	if m != nil {
		return m.CityCode
	}
	return 0
}

func (m *LocationInfo) GetShowGeoInfo() bool {
	if m != nil {
		return m.ShowGeoInfo
	}
	return false
}

func (m *LocationInfo) GetAreaLevel() LocationInfo_AreaLevel {
	if m != nil {
		return m.AreaLevel
	}
	return LocationInfo_AreaLevel_Normal
}

type RecallMetaData struct {
	ReqId   string        `protobuf:"bytes,1,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	Scene   RcmdScene     `protobuf:"varint,2,opt,name=scene,proto3,enum=rcmd.common.RcmdScene" json:"scene,omitempty"`
	QueueId RecallQueueId `protobuf:"varint,3,opt,name=queue_id,json=queueId,proto3,enum=rcmd.common.RecallQueueId" json:"queue_id,omitempty"`
	// 标识是否使用有序过滤
	UseOrderFilter       bool     `protobuf:"varint,4,opt,name=use_order_filter,json=useOrderFilter,proto3" json:"use_order_filter,omitempty"`
	QueueName            string   `protobuf:"bytes,5,opt,name=queue_name,json=queueName,proto3" json:"queue_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallMetaData) Reset()         { *m = RecallMetaData{} }
func (m *RecallMetaData) String() string { return proto.CompactTextString(m) }
func (*RecallMetaData) ProtoMessage()    {}
func (*RecallMetaData) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_8055bc815ce55a48, []int{13}
}
func (m *RecallMetaData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallMetaData.Unmarshal(m, b)
}
func (m *RecallMetaData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallMetaData.Marshal(b, m, deterministic)
}
func (dst *RecallMetaData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallMetaData.Merge(dst, src)
}
func (m *RecallMetaData) XXX_Size() int {
	return xxx_messageInfo_RecallMetaData.Size(m)
}
func (m *RecallMetaData) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallMetaData.DiscardUnknown(m)
}

var xxx_messageInfo_RecallMetaData proto.InternalMessageInfo

func (m *RecallMetaData) GetReqId() string {
	if m != nil {
		return m.ReqId
	}
	return ""
}

func (m *RecallMetaData) GetScene() RcmdScene {
	if m != nil {
		return m.Scene
	}
	return RcmdScene_UnknownRcmdScene
}

func (m *RecallMetaData) GetQueueId() RecallQueueId {
	if m != nil {
		return m.QueueId
	}
	return RecallQueueId_UnknownRecallQueue
}

func (m *RecallMetaData) GetUseOrderFilter() bool {
	if m != nil {
		return m.UseOrderFilter
	}
	return false
}

func (m *RecallMetaData) GetQueueName() string {
	if m != nil {
		return m.QueueName
	}
	return ""
}

func init() {
	proto.RegisterType((*RecallQ)(nil), "rcmd.common.RecallQ")
	proto.RegisterType((*RecallConfig)(nil), "rcmd.common.RecallConfig")
	proto.RegisterType((*RankConfig)(nil), "rcmd.common.RankConfig")
	proto.RegisterType((*RecallRankConfig)(nil), "rcmd.common.RecallRankConfig")
	proto.RegisterType((*LayerInfo)(nil), "rcmd.common.LayerInfo")
	proto.RegisterType((*ABTestConfigJson)(nil), "rcmd.common.ABTestConfigJson")
	proto.RegisterMapType((map[uint32]*LayerInfo)(nil), "rcmd.common.ABTestConfigJson.LayersEntry")
	proto.RegisterType((*RcmdBaseReq)(nil), "rcmd.common.RcmdBaseReq")
	proto.RegisterType((*TopicChannelBaseReq)(nil), "rcmd.common.TopicChannelBaseReq")
	proto.RegisterType((*RcmdBaseRsp)(nil), "rcmd.common.RcmdBaseRsp")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.common.RcmdBaseRsp.ProfileInfoEntry")
	proto.RegisterType((*RcmdCommonDataBatchEvent)(nil), "rcmd.common.RcmdCommonDataBatchEvent")
	proto.RegisterType((*RcmdBrowseInfo)(nil), "rcmd.common.RcmdBrowseInfo")
	proto.RegisterType((*ABTestInfo)(nil), "rcmd.common.ABTestInfo")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.common.ABTestInfo.InfoEntry")
	proto.RegisterType((*LocationInfo)(nil), "rcmd.common.LocationInfo")
	proto.RegisterType((*RecallMetaData)(nil), "rcmd.common.RecallMetaData")
	proto.RegisterEnum("rcmd.common.RecallQId", RecallQId_name, RecallQId_value)
	proto.RegisterEnum("rcmd.common.ConfigType", ConfigType_name, ConfigType_value)
	proto.RegisterEnum("rcmd.common.RankStrategyId", RankStrategyId_name, RankStrategyId_value)
	proto.RegisterEnum("rcmd.common.LayerId", LayerId_name, LayerId_value)
	proto.RegisterEnum("rcmd.common.RcmdRspCode", RcmdRspCode_name, RcmdRspCode_value)
	proto.RegisterEnum("rcmd.common.Env", Env_name, Env_value)
	proto.RegisterEnum("rcmd.common.RecallActionFormula", RecallActionFormula_name, RecallActionFormula_value)
	proto.RegisterEnum("rcmd.common.RcmdScene", RcmdScene_name, RcmdScene_value)
	proto.RegisterEnum("rcmd.common.RecallQueueId", RecallQueueId_name, RecallQueueId_value)
	proto.RegisterEnum("rcmd.common.RegulatoryLevel", RegulatoryLevel_name, RegulatoryLevel_value)
	proto.RegisterEnum("rcmd.common.LocationInfo_AreaLevel", LocationInfo_AreaLevel_name, LocationInfo_AreaLevel_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/rcmd-local/common/common.proto", fileDescriptor_common_8055bc815ce55a48)
}

var fileDescriptor_common_8055bc815ce55a48 = []byte{
	// 3776 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x5a, 0x69, 0x70, 0x1c, 0xc7,
	0x75, 0xe6, 0xec, 0x2e, 0x09, 0xa0, 0x71, 0xb0, 0xd5, 0xbc, 0x16, 0xe0, 0x05, 0x82, 0x92, 0x4c,
	0xc1, 0x16, 0x48, 0x42, 0xa5, 0xc4, 0xf1, 0x0f, 0x55, 0x16, 0x8b, 0x5d, 0x72, 0x4d, 0xec, 0x60,
	0x39, 0xbb, 0xa0, 0x2b, 0xaa, 0x62, 0x4d, 0x35, 0x66, 0x1b, 0x8b, 0x0e, 0x66, 0xa7, 0x97, 0x33,
	0x3d, 0x0b, 0xc2, 0xbf, 0x52, 0xe5, 0x04, 0xca, 0xcf, 0xf8, 0xbe, 0x64, 0x5b, 0x96, 0x95, 0xd8,
	0x96, 0x9c, 0x8b, 0xce, 0xa1, 0x30, 0x97, 0x49, 0x3b, 0x71, 0x7c, 0x4b, 0xb6, 0x94, 0x38, 0xb1,
	0x25, 0x88, 0x3e, 0x12, 0x6b, 0x11, 0x57, 0xac, 0xe4, 0x47, 0x62, 0xe5, 0x4f, 0xea, 0xf5, 0xcc,
	0xec, 0x0d, 0x88, 0x95, 0x2a, 0xff, 0xc2, 0xf4, 0xfb, 0xbe, 0x7e, 0xfd, 0xde, 0xeb, 0xd7, 0xaf,
	0xdf, 0x0c, 0x16, 0x3d, 0x28, 0xe5, 0xd9, 0x6b, 0x3e, 0xb7, 0xd6, 0x3c, 0x6e, 0xd7, 0x99, 0x7b,
	0xd6, 0xb5, 0xaa, 0xe5, 0x07, 0x6d, 0x61, 0x51, 0xfb, 0xac, 0x25, 0xaa, 0x55, 0xe1, 0x84, 0x7f,
	0x66, 0x6a, 0xae, 0x90, 0x82, 0x0c, 0x03, 0x3e, 0x13, 0x88, 0xa6, 0x7c, 0x34, 0x60, 0x30, 0x8b,
	0xda, 0xf6, 0x65, 0x32, 0x86, 0x62, 0xb9, 0x72, 0x52, 0x9b, 0xd4, 0xce, 0x8c, 0x1a, 0xb1, 0x5c,
	0x99, 0x10, 0x94, 0x70, 0x68, 0x95, 0x25, 0x63, 0x93, 0xda, 0x99, 0x21, 0x43, 0x3d, 0x93, 0x83,
	0x68, 0xaf, 0xcd, 0xab, 0x5c, 0x26, 0xe3, 0x8a, 0x16, 0x0c, 0x40, 0xea, 0x52, 0xc9, 0x45, 0x32,
	0x11, 0x48, 0xd5, 0x80, 0x4c, 0xa0, 0xc1, 0x9a, 0xcb, 0x85, 0xcb, 0xe5, 0x46, 0x72, 0xaf, 0x02,
	0x9a, 0xe3, 0xa9, 0xc7, 0x34, 0x34, 0x12, 0xac, 0x9b, 0x16, 0xce, 0x0a, 0xaf, 0x90, 0x5f, 0x45,
	0xf7, 0xb8, 0x6a, 0x6c, 0x5e, 0xf3, 0x99, 0xcf, 0x4c, 0x9b, 0x7b, 0x32, 0xa9, 0x4d, 0xc6, 0xcf,
	0x0c, 0xcf, 0x1e, 0x9c, 0x69, 0x33, 0x78, 0x26, 0xb4, 0xd6, 0xd8, 0x1f, 0xd0, 0x2f, 0x03, 0x7b,
	0x81, 0x7b, 0x92, 0x9c, 0x44, 0xc3, 0x96, 0xd2, 0x65, 0xca, 0x8d, 0x5a, 0x60, 0xf5, 0xa8, 0x81,
	0x02, 0x51, 0x69, 0xa3, 0xa6, 0x6c, 0x97, 0x42, 0x52, 0x3b, 0xb2, 0x5d, 0x0d, 0xa6, 0xce, 0x21,
	0x64, 0x50, 0x67, 0x2d, 0x34, 0xe3, 0x2e, 0x62, 0x30, 0xf5, 0x69, 0x0d, 0xe1, 0xc0, 0x8a, 0xb6,
	0x89, 0x8f, 0xa0, 0xd1, 0xd0, 0xfe, 0x60, 0x45, 0xa5, 0x63, 0x78, 0x76, 0xbc, 0x8f, 0xed, 0xc1,
	0x0c, 0x63, 0xc4, 0x6d, 0xf7, 0xff, 0xad, 0x68, 0xd8, 0xa5, 0xce, 0x5a, 0x34, 0x3b, 0xa6, 0x66,
	0x1f, 0xe9, 0x9c, 0xdd, 0x5c, 0xcd, 0x40, 0x6e, 0x6b, 0xe5, 0x71, 0x34, 0xc8, 0xae, 0xd7, 0x4c,
	0x65, 0x66, 0x5c, 0x99, 0x39, 0xc0, 0xae, 0xd7, 0x74, 0xb0, 0xf4, 0x51, 0x34, 0xb4, 0x40, 0x37,
	0x98, 0x9b, 0x73, 0x56, 0x04, 0xf0, 0x6c, 0x18, 0x98, 0x3c, 0x72, 0x70, 0x40, 0x8d, 0x73, 0x65,
	0x88, 0x4c, 0x9d, 0xda, 0x7e, 0xe4, 0x66, 0x30, 0x20, 0xc7, 0xd0, 0x10, 0x28, 0xf5, 0x6a, 0xd4,
	0x8a, 0x34, 0xb7, 0x04, 0x53, 0x9f, 0xd1, 0x10, 0x4e, 0xcd, 0x95, 0x98, 0x27, 0x03, 0x3b, 0xde,
	0xee, 0x09, 0x87, 0xa4, 0xd0, 0x3e, 0xa5, 0xd3, 0x0b, 0xb7, 0xee, 0x81, 0x0e, 0x07, 0xba, 0xe9,
	0x33, 0xca, 0x38, 0x2f, 0xe3, 0x48, 0x77, 0xc3, 0x08, 0x27, 0x4e, 0x5c, 0x46, 0xc3, 0x6d, 0x62,
	0x82, 0x51, 0x7c, 0x8d, 0x6d, 0x84, 0x06, 0xc3, 0x23, 0x79, 0x4b, 0xbb, 0xb1, 0xc3, 0xb3, 0x87,
	0x3b, 0x96, 0x68, 0xba, 0x1b, 0x3a, 0xf1, 0xb6, 0xd8, 0x5b, 0xb5, 0xa9, 0xff, 0xd6, 0xd0, 0xb0,
	0x61, 0x55, 0xcb, 0x73, 0xd4, 0x63, 0x06, 0xbb, 0x06, 0x3a, 0xfd, 0x66, 0x10, 0xe0, 0x91, 0x3c,
	0xd2, 0xcc, 0x9d, 0x5f, 0xf7, 0x84, 0x13, 0x6a, 0x3e, 0xbe, 0xab, 0xf1, 0x51, 0x6a, 0x29, 0xbf,
	0xc7, 0xd1, 0xa0, 0x74, 0xa9, 0xc5, 0x20, 0xb6, 0xe1, 0x1e, 0xa8, 0x71, 0xae, 0x4c, 0x4e, 0xa1,
	0x91, 0x1a, 0xf5, 0x3c, 0x53, 0xae, 0xba, 0xc2, 0xaf, 0xac, 0xaa, 0x23, 0x32, 0x64, 0x0c, 0x83,
	0xac, 0x14, 0x88, 0xc8, 0x71, 0x84, 0xca, 0x6c, 0xd9, 0xaf, 0x98, 0x2b, 0x36, 0xad, 0x84, 0x47,
	0x65, 0x48, 0x49, 0xb2, 0x36, 0xad, 0x90, 0x5f, 0x41, 0x23, 0x74, 0xd9, 0x94, 0xcc, 0x93, 0x26,
	0x77, 0x56, 0x44, 0x72, 0x5f, 0x9f, 0xdc, 0x08, 0xac, 0x53, 0x8e, 0x23, 0xba, 0x1c, 0x3d, 0x4f,
	0x55, 0xd0, 0x81, 0x92, 0xa8, 0x71, 0x2b, 0xbd, 0x4a, 0x1d, 0x87, 0xd9, 0xbf, 0xb0, 0x00, 0x4c,
	0xdd, 0x6a, 0x0f, 0xb1, 0x57, 0x83, 0x73, 0x63, 0x89, 0x32, 0x0b, 0x97, 0x50, 0xcf, 0xb0, 0x6a,
	0xd5, 0xab, 0x84, 0x39, 0x06, 0x8f, 0x64, 0x01, 0x8d, 0xd4, 0x5c, 0xb1, 0xc2, 0x6d, 0x16, 0x78,
	0x16, 0xef, 0x93, 0x34, 0x6d, 0x5a, 0x67, 0x0a, 0x01, 0x19, 0x5c, 0x0b, 0x92, 0x66, 0xb8, 0xd6,
	0x92, 0x4c, 0x3c, 0x82, 0x70, 0x37, 0xa1, 0x3d, 0x7d, 0x86, 0x82, 0xf4, 0xe9, 0x9b, 0xeb, 0x2a,
	0x4d, 0x5e, 0xd0, 0x50, 0x12, 0x56, 0x4b, 0xab, 0x85, 0xe7, 0xa9, 0xa4, 0x73, 0x54, 0x5a, 0xab,
	0x99, 0x3a, 0x73, 0x24, 0x39, 0x84, 0xf6, 0x79, 0xae, 0x65, 0xf2, 0x5a, 0xa8, 0x6b, 0xaf, 0xe7,
	0x5a, 0xb9, 0x1a, 0x79, 0x00, 0x61, 0x8f, 0xb9, 0x75, 0xe6, 0x9a, 0x92, 0x57, 0x99, 0x27, 0x69,
	0xb5, 0x16, 0x56, 0x9e, 0xfd, 0x81, 0xbc, 0x14, 0x89, 0x21, 0x47, 0x96, 0xf9, 0x3b, 0x83, 0xe2,
	0x14, 0xe6, 0xc8, 0x32, 0x7f, 0xa7, 0xaa, 0x4c, 0x04, 0x25, 0xca, 0x54, 0xd2, 0x64, 0x62, 0x32,
	0x7e, 0x66, 0xc4, 0x50, 0xcf, 0x24, 0x89, 0x06, 0xea, 0xcc, 0xf5, 0xb8, 0x70, 0x54, 0x46, 0x0c,
	0x19, 0xd1, 0x90, 0x1c, 0x45, 0x43, 0xcb, 0x60, 0x98, 0xe9, 0xf8, 0x55, 0x95, 0x0c, 0xa3, 0xc6,
	0xa0, 0x12, 0xe8, 0x7e, 0x15, 0x1c, 0x66, 0x4e, 0x3d, 0x39, 0x10, 0x6c, 0x2d, 0x73, 0xea, 0x53,
	0xbf, 0x84, 0xc6, 0x54, 0x0c, 0x5d, 0xb1, 0xee, 0xa9, 0xc8, 0x90, 0x7b, 0xd1, 0x98, 0x23, 0xcc,
	0x65, 0x25, 0x68, 0x15, 0xda, 0x51, 0x63, 0xc4, 0x11, 0x01, 0x0b, 0xea, 0xe9, 0xd4, 0x4d, 0x0d,
	0xa1, 0x56, 0x5a, 0x91, 0x87, 0x51, 0x42, 0xed, 0x51, 0x70, 0xb0, 0x4f, 0xed, 0x90, 0x7d, 0x33,
	0xad, 0xbd, 0x51, 0x74, 0x72, 0x18, 0xed, 0xf3, 0x84, 0xef, 0x5a, 0x51, 0xbc, 0xc3, 0x11, 0xd8,
	0xc0, 0x3d, 0x53, 0x95, 0x60, 0x53, 0x9d, 0x7c, 0x15, 0x93, 0x41, 0x63, 0x84, 0x7b, 0x25, 0x10,
	0xaa, 0x83, 0x3c, 0xf1, 0xcb, 0x68, 0xe8, 0xff, 0xb7, 0x97, 0x77, 0x62, 0x68, 0x64, 0x41, 0x58,
	0x70, 0x0f, 0x39, 0xca, 0xfc, 0x24, 0x1a, 0xb0, 0x84, 0x0f, 0x7a, 0x42, 0x05, 0xd1, 0x30, 0xb8,
	0xa6, 0x44, 0x9d, 0x3b, 0x4d, 0x1b, 0x9b, 0x63, 0x95, 0xc6, 0x70, 0x7d, 0x05, 0xfb, 0xa5, 0x9e,
	0xe1, 0x40, 0x87, 0x53, 0x4d, 0x95, 0xe2, 0xe1, 0x81, 0x0e, 0x65, 0x69, 0xc8, 0xf4, 0xd3, 0x68,
	0x34, 0x52, 0x11, 0x70, 0x82, 0x33, 0x3d, 0x12, 0x09, 0x15, 0xe9, 0x28, 0x1a, 0x02, 0x7d, 0x01,
	0x21, 0xdc, 0x46, 0x10, 0x28, 0x70, 0x0a, 0x8d, 0x7a, 0xab, 0x62, 0xdd, 0xac, 0x30, 0x11, 0x1c,
	0x8d, 0x01, 0x15, 0x9d, 0x61, 0x10, 0x5e, 0x60, 0x42, 0xb9, 0x34, 0x87, 0x10, 0x75, 0x19, 0x35,
	0x6d, 0x56, 0x67, 0x76, 0x72, 0x70, 0x52, 0x3b, 0x33, 0x36, 0x7b, 0xba, 0xb3, 0x1a, 0xb6, 0x45,
	0x60, 0x26, 0xe5, 0x32, 0xba, 0x00, 0x54, 0x63, 0x88, 0x46, 0x8f, 0x53, 0x6f, 0x43, 0x43, 0x4d,
	0x39, 0x39, 0x88, 0x70, 0x73, 0x60, 0xea, 0xc2, 0xad, 0x52, 0x1b, 0xef, 0x21, 0x47, 0xd0, 0x81,
	0x96, 0xb4, 0xc8, 0x1c, 0x8f, 0x4b, 0x5e, 0x67, 0x58, 0x9b, 0x7a, 0x51, 0x43, 0x63, 0xc1, 0x8d,
	0x96, 0x67, 0x92, 0xc2, 0x79, 0x81, 0x53, 0xe2, 0xb2, 0x6b, 0xd1, 0x0d, 0x33, 0x64, 0xec, 0x75,
	0xd9, 0xb5, 0x5c, 0x19, 0x4a, 0xb6, 0x67, 0x31, 0x27, 0x88, 0xef, 0x58, 0x57, 0xc9, 0x86, 0xe4,
	0x2c, 0x02, 0x6a, 0x04, 0x24, 0xf2, 0x30, 0x1a, 0x0c, 0x7a, 0x80, 0xb0, 0x98, 0x8e, 0xcd, 0x4e,
	0xf4, 0xeb, 0x00, 0x80, 0x92, 0x2b, 0x1b, 0x03, 0xd7, 0x82, 0x07, 0x72, 0x06, 0x61, 0xdf, 0x63,
	0xa6, 0x70, 0xcb, 0xcc, 0x35, 0x57, 0xb8, 0x2d, 0x99, 0xab, 0xf6, 0x66, 0xd0, 0x18, 0xf3, 0x3d,
	0xb6, 0x08, 0xe2, 0xac, 0x92, 0x42, 0xbd, 0x0d, 0x16, 0x50, 0x77, 0x66, 0x70, 0xba, 0x86, 0x94,
	0x04, 0x6e, 0xcd, 0xe9, 0x2f, 0x6b, 0x68, 0x28, 0x5c, 0x43, 0xdd, 0x8d, 0xb8, 0x39, 0x30, 0x97,
	0x9c, 0x35, 0x47, 0xac, 0xe3, 0x3d, 0x64, 0x0a, 0x9d, 0x68, 0x49, 0x0d, 0x46, 0x6d, 0x38, 0xe7,
	0x66, 0x91, 0x5d, 0x37, 0xcf, 0x99, 0xa9, 0x0a, 0x33, 0xcf, 0x61, 0x6d, 0x17, 0xce, 0x79, 0xc5,
	0x39, 0x8f, 0x63, 0x64, 0x1c, 0x1d, 0x6a, 0x71, 0xda, 0xa1, 0x38, 0x39, 0x8e, 0xc6, 0x5b, 0xd0,
	0x95, 0x73, 0x0f, 0x99, 0x29, 0x0b, 0x02, 0x6f, 0x2e, 0x79, 0xcc, 0xc5, 0x89, 0x4e, 0xed, 0x5d,
	0xb0, 0x99, 0x16, 0x2e, 0xc3, 0x7b, 0xa7, 0xf3, 0x08, 0xa5, 0x5b, 0xfd, 0xcf, 0x21, 0x74, 0x4f,
	0x6b, 0xd4, 0x72, 0x85, 0xa0, 0xb1, 0x36, 0xb1, 0xee, 0x57, 0xb1, 0x06, 0x4e, 0xb7, 0xc9, 0x0c,
	0x48, 0x22, 0x1c, 0x9b, 0x7e, 0x15, 0x36, 0x9c, 0x3a, 0x6b, 0x45, 0xe9, 0x52, 0xc9, 0x2a, 0x1b,
	0xb9, 0xb2, 0xb2, 0xbf, 0x43, 0xd2, 0xd2, 0x7b, 0x04, 0x1d, 0xe8, 0x82, 0x0c, 0xdf, 0x66, 0x58,
	0xeb, 0x03, 0x5c, 0x98, 0x9b, 0x2f, 0xe1, 0x18, 0x99, 0x40, 0x87, 0xfb, 0x00, 0x66, 0x2e, 0x8f,
	0xe3, 0xe4, 0x24, 0x3a, 0xda, 0x1f, 0x0b, 0x7c, 0x4d, 0xec, 0x42, 0x98, 0xa3, 0xd6, 0x1a, 0xde,
	0x4b, 0xde, 0x82, 0xce, 0xec, 0x42, 0x30, 0x4b, 0x2e, 0xe5, 0x8e, 0xb9, 0xe8, 0xd8, 0xdc, 0x61,
	0x78, 0xdf, 0xf4, 0xbb, 0x34, 0x34, 0xb0, 0x10, 0xb6, 0x47, 0x04, 0x8d, 0x85, 0x8f, 0x2d, 0xef,
	0x0e, 0xa0, 0xfd, 0x91, 0x2c, 0xe3, 0x48, 0xe6, 0x5e, 0x99, 0xc5, 0x1a, 0x79, 0x13, 0x3a, 0x1d,
	0x09, 0x0b, 0x36, 0xdd, 0xa8, 0x52, 0xc9, 0xa2, 0xe5, 0x4c, 0x5d, 0x2c, 0x79, 0xcc, 0x84, 0x3a,
	0x89, 0x63, 0xe4, 0x7e, 0x34, 0xd5, 0x4d, 0x6c, 0x75, 0x94, 0xd1, 0x14, 0x1c, 0x9f, 0xfe, 0x7c,
	0x78, 0xad, 0x1a, 0x5e, 0x4d, 0x95, 0x85, 0xfd, 0x68, 0x38, 0xbd, 0x38, 0x9f, 0x31, 0x97, 0xf4,
	0x4b, 0xfa, 0xe2, 0x3b, 0xf0, 0x1e, 0x82, 0xd1, 0x88, 0x12, 0x14, 0x97, 0xd2, 0xe9, 0x4c, 0xb1,
	0x88, 0x35, 0x72, 0x0c, 0x25, 0x95, 0xa4, 0x90, 0x32, 0x52, 0xf9, 0xa2, 0x99, 0x5d, 0x34, 0xf2,
	0xa9, 0x92, 0x99, 0x31, 0x8c, 0x45, 0x03, 0xc7, 0x20, 0xf6, 0x0a, 0x9d, 0x4f, 0x95, 0x52, 0x73,
	0xa9, 0x62, 0x26, 0x04, 0xe2, 0xe4, 0x7e, 0x74, 0x2a, 0x98, 0xb6, 0x90, 0xfa, 0xb5, 0x7c, 0xaa,
	0x94, 0x31, 0x17, 0x16, 0x8b, 0x45, 0x33, 0x35, 0x67, 0x96, 0x32, 0xc5, 0x92, 0x99, 0x5e, 0xd4,
	0xb3, 0xb9, 0x0b, 0xf8, 0xd5, 0x01, 0x32, 0x85, 0x8e, 0x77, 0xf2, 0xf2, 0x8b, 0xf3, 0x99, 0x05,
	0x53, 0x5f, 0x2c, 0x99, 0xd9, 0xc5, 0x25, 0x7d, 0x1e, 0x37, 0x06, 0xa6, 0x67, 0x51, 0x3c, 0xe3,
	0xd4, 0xc9, 0x08, 0x1a, 0xcc, 0x38, 0x75, 0xb3, 0xe0, 0x8a, 0x32, 0xde, 0x03, 0xa6, 0xc3, 0xa8,
	0x28, 0x69, 0x85, 0x3b, 0x15, 0xac, 0x45, 0x70, 0x10, 0x91, 0x69, 0x8a, 0x0e, 0x04, 0x11, 0x80,
	0x34, 0x16, 0x4e, 0x56, 0xb8, 0x55, 0xdf, 0xa6, 0x10, 0xfa, 0x20, 0xe4, 0x91, 0x04, 0xef, 0x81,
	0x9d, 0xce, 0x53, 0xe9, 0xf2, 0xeb, 0x79, 0xdf, 0x96, 0xbc, 0x66, 0xf3, 0xa0, 0xc8, 0x45, 0x04,
	0x0d, 0x12, 0xbd, 0xc0, 0x65, 0x96, 0xdb, 0x36, 0x77, 0x2a, 0x91, 0x38, 0x36, 0xed, 0xa1, 0xa1,
	0x66, 0xad, 0x81, 0x0c, 0x0f, 0x15, 0x37, 0x65, 0xca, 0x48, 0xa4, 0x86, 0xd2, 0x65, 0xb4, 0x8a,
	0x7f, 0x32, 0x40, 0xc6, 0x82, 0x39, 0xaa, 0x89, 0x02, 0xf7, 0xc7, 0xd1, 0x41, 0x18, 0x17, 0x84,
	0x27, 0xaf, 0x08, 0x0e, 0x95, 0xdc, 0x91, 0xae, 0xb0, 0x71, 0x63, 0x80, 0x1c, 0x44, 0xfb, 0x01,
	0x5a, 0xe0, 0x75, 0x16, 0xb6, 0x5b, 0xf8, 0xb9, 0xfd, 0xd3, 0x5b, 0x6f, 0x46, 0xa3, 0x1d, 0x05,
	0x8b, 0x1c, 0x46, 0x24, 0x5a, 0xb9, 0x25, 0x57, 0xe7, 0x70, 0x54, 0x67, 0xeb, 0x05, 0x21, 0xec,
	0x40, 0x8e, 0x7f, 0x47, 0x27, 0x87, 0x10, 0xd6, 0xd9, 0x7a, 0x9e, 0x97, 0xdb, 0xc4, 0xef, 0xd6,
	0xc9, 0x51, 0x74, 0xf8, 0x22, 0xaf, 0xac, 0xe6, 0x20, 0xf1, 0xa8, 0x25, 0xdb, 0xc0, 0xf7, 0xe8,
	0xe4, 0x18, 0x3a, 0x02, 0xe6, 0xa5, 0xec, 0x8a, 0x58, 0x5c, 0x59, 0x81, 0x74, 0x0e, 0xb0, 0xf3,
	0xe7, 0xf0, 0x7b, 0x75, 0x72, 0x1f, 0x9a, 0xec, 0x8b, 0x82, 0xbe, 0xcb, 0x3e, 0xb5, 0xb9, 0xdc,
	0xc0, 0xef, 0xdb, 0x45, 0xc9, 0x79, 0xfc, 0x7e, 0x9d, 0x1c, 0x47, 0xc9, 0xfe, 0x4a, 0x84, 0xc4,
	0x1f, 0xd0, 0x21, 0x48, 0x4d, 0x58, 0x9d, 0xa7, 0x9c, 0x64, 0xd5, 0x74, 0x16, 0x7f, 0xb0, 0x53,
	0xaf, 0x82, 0x2e, 0x0a, 0x79, 0xc1, 0x16, 0xcb, 0xd4, 0xc6, 0x1f, 0x52, 0x7e, 0xf5, 0x4e, 0x9c,
	0xbd, 0xc2, 0x2c, 0xfc, 0xe1, 0x3e, 0x20, 0x54, 0x3a, 0x05, 0x7e, 0x44, 0x27, 0x27, 0xd0, 0x78,
	0x8f, 0x5e, 0x83, 0x55, 0xb8, 0x70, 0xa8, 0x8d, 0x3f, 0xaa, 0x93, 0x09, 0x74, 0xa8, 0xcb, 0x62,
	0x98, 0x9d, 0xce, 0xe2, 0xc7, 0x75, 0x72, 0x0a, 0x1d, 0xeb, 0xf1, 0xc6, 0x56, 0x29, 0x95, 0x5e,
	0xa5, 0xdc, 0xc1, 0x1f, 0xd3, 0xc9, 0x24, 0x3a, 0xda, 0x67, 0xed, 0x82, 0xbf, 0x3c, 0x0b, 0x16,
	0xe2, 0x8f, 0xeb, 0xe4, 0x34, 0x3a, 0xd1, 0x9f, 0x71, 0x85, 0x59, 0x01, 0xe9, 0x13, 0x3a, 0x39,
	0x8c, 0xee, 0xe9, 0xd8, 0xe2, 0x87, 0xf2, 0xdc, 0xc1, 0x4f, 0x74, 0x58, 0xd7, 0x99, 0x02, 0x9f,
	0xd4, 0xc9, 0x11, 0x44, 0x3a, 0x15, 0xcf, 0x31, 0x57, 0xe2, 0x27, 0x95, 0xb2, 0x08, 0xb8, 0x28,
	0x7c, 0x37, 0x3d, 0x5f, 0x5a, 0xcc, 0xe3, 0x4f, 0x75, 0x44, 0x5f, 0x67, 0xd4, 0x55, 0xc1, 0x60,
	0xd4, 0xc5, 0x4f, 0xf5, 0x89, 0x52, 0x14, 0xc2, 0x82, 0xbf, 0x8c, 0x7f, 0xb7, 0x69, 0x07, 0xd0,
	0x65, 0xce, 0xb3, 0xa9, 0x53, 0x0e, 0xed, 0xf8, 0x3d, 0x9d, 0x24, 0xd1, 0x01, 0xc0, 0x72, 0xd5,
	0x4a, 0x89, 0x56, 0x54, 0x74, 0x15, 0xf2, 0xe9, 0xe6, 0x82, 0x59, 0xe1, 0xe6, 0x85, 0xc3, 0xa5,
	0x70, 0x43, 0xe8, 0x33, 0x3a, 0x9c, 0x09, 0xb5, 0x60, 0xce, 0xa0, 0xb5, 0x50, 0xfa, 0xd9, 0xe6,
	0x32, 0x2a, 0x7d, 0x99, 0x27, 0x5b, 0xca, 0x9e, 0x6e, 0x2e, 0x03, 0x61, 0x70, 0xd9, 0x0a, 0x8b,
	0x74, 0x3d, 0xd3, 0x9c, 0xa5, 0xb3, 0xf5, 0x68, 0x62, 0x88, 0x7d, 0xae, 0x99, 0x90, 0x3a, 0x5b,
	0xd7, 0x85, 0xec, 0x82, 0x7f, 0x5f, 0x1d, 0x23, 0x80, 0x4b, 0xa2, 0x96, 0x96, 0x91, 0xc6, 0x3f,
	0x68, 0x66, 0xd4, 0x3c, 0xf7, 0x2c, 0xdf, 0xf3, 0xd4, 0x19, 0x0f, 0xc1, 0x3f, 0x6c, 0x66, 0x2a,
	0xdc, 0xd9, 0xe1, 0x94, 0x39, 0xe1, 0x4b, 0x7e, 0xcd, 0x67, 0xf8, 0xb7, 0x8c, 0xc8, 0xb1, 0xf6,
	0x39, 0xff, 0xb3, 0x04, 0x45, 0xb4, 0x29, 0x6d, 0x2f, 0x0f, 0x41, 0x3d, 0x09, 0x79, 0x3f, 0x5f,
	0x8a, 0x9c, 0x54, 0x3c, 0xa8, 0x17, 0x21, 0xf2, 0xfa, 0x52, 0xb4, 0x43, 0x81, 0x06, 0x5e, 0x66,
	0x22, 0xe5, 0x97, 0xb9, 0x08, 0xf1, 0xff, 0x5d, 0x8a, 0x1c, 0x2d, 0xd2, 0x2a, 0x4b, 0x73, 0xb9,
	0xd1, 0x11, 0xa3, 0x3b, 0x57, 0xc9, 0x49, 0x34, 0xd1, 0x0e, 0x77, 0x45, 0xe2, 0x07, 0x57, 0xc9,
	0xbd, 0xe8, 0x64, 0x3b, 0x21, 0x2b, 0x2c, 0xdf, 0xeb, 0x62, 0xfd, 0xf0, 0x2a, 0x14, 0xf9, 0x76,
	0x96, 0x0e, 0x1d, 0x64, 0x07, 0xe7, 0x47, 0x57, 0xdb, 0x2d, 0x29, 0xb8, 0xa2, 0xde, 0x61, 0xc9,
	0x8f, 0x3b, 0x2c, 0x01, 0xb8, 0x6b, 0xfe, 0xbf, 0x76, 0x58, 0x02, 0x84, 0x7e, 0x96, 0xfc, 0x5b,
	0x87, 0x25, 0xc0, 0xea, 0xb5, 0xe4, 0x27, 0x57, 0xa3, 0xf3, 0x1b, 0x59, 0x9b, 0xf2, 0x36, 0x1c,
	0x2b, 0x4d, 0xad, 0xd5, 0x28, 0xac, 0xaf, 0x5e, 0x25, 0x47, 0xd0, 0x3d, 0x6d, 0x75, 0x39, 0x94,
	0x3f, 0xf1, 0x38, 0x34, 0x3a, 0x63, 0xe9, 0x55, 0x2a, 0xd3, 0xd4, 0x8d, 0x52, 0xfd, 0xb7, 0x9f,
	0xd2, 0xc8, 0x04, 0x3a, 0x58, 0x60, 0xee, 0x0a, 0xb3, 0x64, 0x1e, 0xde, 0xab, 0x2e, 0xfb, 0xcc,
	0x83, 0x72, 0x80, 0x7f, 0xf6, 0x8c, 0x46, 0x8e, 0xa2, 0x43, 0x79, 0xdf, 0x6b, 0xbe, 0x52, 0x2f,
	0xa5, 0xa9, 0xb3, 0x54, 0x84, 0x1b, 0xed, 0x8f, 0x1a, 0x1a, 0x19, 0x47, 0x07, 0x3a, 0xc1, 0xa5,
	0xa2, 0x99, 0x29, 0xe0, 0x3f, 0x6e, 0xc0, 0xa5, 0xb4, 0xbf, 0x1d, 0xba, 0x54, 0xba, 0x82, 0x6f,
	0x34, 0x34, 0x92, 0x44, 0xa4, 0x4b, 0x6c, 0x5e, 0xbc, 0x8c, 0x3f, 0xdf, 0x1f, 0xc9, 0x14, 0xf0,
	0x9f, 0x34, 0x34, 0x72, 0x12, 0x8d, 0x77, 0x23, 0x59, 0x5a, 0x17, 0x6e, 0x51, 0x38, 0x15, 0xfc,
	0xa7, 0x0d, 0x8d, 0x1c, 0x47, 0x47, 0x7a, 0x94, 0x0a, 0xa9, 0xe0, 0x3f, 0x6b, 0x40, 0x8b, 0x70,
	0xb8, 0x1d, 0x86, 0xb7, 0x3d, 0xe6, 0x28, 0xf4, 0xcf, 0x7b, 0x27, 0x1b, 0xb4, 0x56, 0x12, 0x15,
	0x26, 0x57, 0x99, 0x8b, 0x9f, 0xed, 0x85, 0xc1, 0xf5, 0x94, 0x53, 0x86, 0x20, 0xe2, 0xbf, 0x68,
	0x68, 0xe4, 0x34, 0x3a, 0xde, 0x0e, 0xe7, 0xe9, 0x1a, 0xcb, 0xba, 0x9c, 0x39, 0x65, 0x20, 0x42,
	0x98, 0x6e, 0xf6, 0x09, 0x53, 0x70, 0x1d, 0xe2, 0xbf, 0xdc, 0x21, 0x82, 0xc5, 0x4b, 0xf8, 0xaf,
	0x76, 0x80, 0xd2, 0x69, 0xfc, 0xd7, 0x3b, 0x40, 0xb9, 0x25, 0xfc, 0x37, 0xbd, 0xc1, 0x6a, 0x39,
	0x0b, 0x71, 0xfe, 0xdb, 0x86, 0x46, 0x26, 0xd1, 0xc4, 0x0e, 0xfe, 0x02, 0xe3, 0x0b, 0x7d, 0x5c,
	0x66, 0x96, 0x70, 0xca, 0x0b, 0x74, 0x99, 0xd9, 0xf8, 0x56, 0x43, 0x23, 0x07, 0xd0, 0xe8, 0x25,
	0xca, 0x2f, 0x32, 0x1e, 0x5d, 0xfb, 0xb7, 0x7b, 0x97, 0x35, 0x17, 0xc4, 0xba, 0x59, 0x66, 0x36,
	0xaf, 0x33, 0x77, 0x03, 0x7f, 0xb1, 0xd7, 0xe4, 0xb4, 0xb0, 0x6d, 0x66, 0x49, 0xfc, 0xa5, 0xde,
	0x9d, 0xcf, 0x0a, 0xdb, 0x16, 0xeb, 0xf8, 0xef, 0x1a, 0x3d, 0xc9, 0xb7, 0xb8, 0x02, 0xbe, 0x48,
	0xba, 0x81, 0xff, 0xbe, 0x77, 0x5b, 0xa1, 0x75, 0x0f, 0x1b, 0x99, 0x2f, 0xf7, 0x4e, 0x0d, 0x9b,
	0x2d, 0xdb, 0xc6, 0xff, 0xd0, 0x3b, 0xb5, 0xc8, 0xc0, 0x96, 0x02, 0xad, 0x30, 0xfc, 0x95, 0x86,
	0x3a, 0x0e, 0xed, 0x68, 0x41, 0x84, 0xaf, 0x80, 0x5f, 0xed, 0xc5, 0xa2, 0x9b, 0x13, 0x7f, 0xad,
	0x37, 0x17, 0x22, 0xcc, 0x10, 0xa2, 0xba, 0xb8, 0xee, 0x30, 0x17, 0x7f, 0xbd, 0xa1, 0x91, 0x53,
	0xe8, 0x68, 0x3f, 0x52, 0x9e, 0x5b, 0xea, 0x45, 0xe7, 0x1b, 0x7d, 0x76, 0x28, 0xa4, 0x14, 0x99,
	0x53, 0xce, 0xe5, 0xf1, 0x37, 0x77, 0x66, 0xc0, 0x45, 0x97, 0xcb, 0xe3, 0x6f, 0xed, 0xbc, 0x0c,
	0x30, 0xf2, 0xdc, 0xc2, 0xcf, 0xbd, 0x01, 0xc5, 0xab, 0xe0, 0xe7, 0x1b, 0xf0, 0x4a, 0x77, 0x6c,
	0x27, 0x8a, 0xda, 0x86, 0x6f, 0xef, 0xce, 0xd1, 0x85, 0xc3, 0xf0, 0x77, 0x7a, 0x37, 0x3f, 0xcf,
	0xad, 0x39, 0xea, 0x94, 0xf1, 0x0b, 0xbd, 0x90, 0xce, 0xd6, 0x55, 0x1c, 0x5e, 0xec, 0xdd, 0x25,
	0x68, 0xd9, 0xc2, 0xdc, 0xf8, 0xc7, 0xfe, 0x68, 0x9e, 0x5b, 0xe7, 0xcf, 0x79, 0xf8, 0x9f, 0x1a,
	0x1a, 0x79, 0x00, 0x9d, 0xde, 0x39, 0x42, 0x3a, 0x5b, 0xf6, 0x6d, 0x6a, 0xa6, 0x8b, 0xf8, 0xbb,
	0x0d, 0x8d, 0x4c, 0xa3, 0x7b, 0x77, 0x09, 0x55, 0x8b, 0xfb, 0xcf, 0x6f, 0xc0, 0xf5, 0x2a, 0x2d,
	0xee, 0xbf, 0x34, 0x34, 0xf2, 0x66, 0x74, 0xdf, 0x6e, 0xc1, 0x6b, 0x91, 0xbf, 0xb7, 0x3b, 0x19,
	0xa2, 0xd8, 0x22, 0x7f, 0xff, 0x2e, 0x9d, 0x33, 0xe6, 0xf1, 0x4b, 0x77, 0xeb, 0x9c, 0x31, 0x8f,
	0x5f, 0xbe, 0x5b, 0xe7, 0x8c, 0x79, 0xbc, 0x75, 0xd7, 0xce, 0x19, 0xf3, 0xf8, 0x95, 0xbb, 0x76,
	0xce, 0x98, 0xc7, 0x77, 0x82, 0x83, 0x5b, 0xbc, 0xe6, 0x53, 0x97, 0xe5, 0xf9, 0xf5, 0xe0, 0xd4,
	0x5e, 0xf2, 0xc5, 0x02, 0x67, 0xf8, 0xfd, 0xdb, 0xea, 0xf8, 0x75, 0x81, 0x45, 0x29, 0xdc, 0x0d,
	0xfc, 0x81, 0x6d, 0x8d, 0x9c, 0x40, 0xc9, 0x2e, 0xcc, 0xa0, 0x65, 0x2e, 0xe0, 0x85, 0x13, 0x7f,
	0x70, 0x5b, 0x1d, 0x9a, 0x2e, 0x3c, 0x2a, 0x7c, 0x05, 0x4e, 0xf1, 0x87, 0xfa, 0x6a, 0xcf, 0xfa,
	0x8e, 0xb3, 0x81, 0x3f, 0xbc, 0xad, 0xd2, 0xbc, 0x0b, 0x4b, 0x0b, 0xc7, 0x93, 0xcc, 0x0e, 0x0b,
	0xc0, 0x47, 0xb6, 0x55, 0x4a, 0x76, 0x71, 0xe0, 0x1a, 0xaa, 0x09, 0x89, 0x3f, 0xba, 0x83, 0xed,
	0x7e, 0x79, 0x03, 0x3f, 0xbe, 0xad, 0x4e, 0x41, 0x17, 0x36, 0xef, 0xd2, 0x75, 0xfc, 0xb1, 0xbe,
	0x4a, 0x33, 0x55, 0xa1, 0x96, 0xfc, 0xf8, 0xb6, 0x3a, 0xc4, 0x5d, 0x68, 0xc7, 0x7b, 0xda, 0x27,
	0xb6, 0x55, 0x39, 0xef, 0x5e, 0x37, 0x6c, 0x1d, 0xf0, 0x13, 0xdb, 0xaa, 0xfa, 0xf6, 0x84, 0xad,
	0x86, 0x3f, 0xd9, 0xd7, 0x28, 0x75, 0xf1, 0x3f, 0xd9, 0xd7, 0x97, 0x94, 0x5b, 0xf1, 0x19, 0xfe,
	0xd4, 0x76, 0xbf, 0x0d, 0x0c, 0xbe, 0x30, 0xe3, 0xa7, 0x82, 0xd5, 0x2e, 0x8a, 0x2a, 0x40, 0xac,
	0xdc, 0xaa, 0xc9, 0x3f, 0x0f, 0xfc, 0xec, 0x42, 0x8a, 0xbc, 0xca, 0x6d, 0xea, 0xe2, 0xd7, 0xb7,
	0x55, 0xe3, 0x52, 0x5a, 0x65, 0x55, 0x56, 0x10, 0x51, 0x63, 0xfd, 0xd2, 0xbf, 0xab, 0xab, 0x28,
	0xf2, 0x25, 0xa8, 0xb0, 0xb7, 0x7e, 0xda, 0x21, 0x2c, 0xd6, 0xa8, 0xcb, 0xf0, 0xed, 0x9f, 0x6a,
	0xe4, 0x30, 0xc2, 0x25, 0x69, 0x41, 0x29, 0x6e, 0xad, 0xfa, 0x85, 0x67, 0x63, 0x60, 0x6c, 0x87,
	0xfc, 0x02, 0xcc, 0xa4, 0x6e, 0x19, 0xdf, 0x7a, 0x36, 0x06, 0x7b, 0x0e, 0xc5, 0xa8, 0xc0, 0xad,
	0xb5, 0x02, 0x67, 0x16, 0x77, 0x2a, 0x61, 0x56, 0xa8, 0x1b, 0xde, 0xc3, 0x5f, 0xd9, 0x8a, 0x91,
	0x7b, 0xd1, 0x89, 0xbe, 0x9c, 0xe8, 0x3b, 0x87, 0x87, 0xbf, 0xba, 0x15, 0x23, 0xf7, 0xa3, 0xc9,
	0xbe, 0x2c, 0xa8, 0x5a, 0x05, 0x97, 0x72, 0x8f, 0x95, 0xf1, 0xd7, 0x76, 0xe1, 0xe9, 0x42, 0xa6,
	0x45, 0xb5, 0x66, 0x33, 0xc9, 0xf0, 0xd7, 0xb7, 0x62, 0x70, 0xd5, 0xf4, 0xe5, 0x65, 0xea, 0xcc,
	0x7d, 0xbb, 0xe0, 0x0e, 0xfe, 0xc6, 0xd6, 0xce, 0xe6, 0x83, 0x69, 0x29, 0x67, 0x03, 0x7f, 0x73,
	0x2b, 0x06, 0xa5, 0xe4, 0x8d, 0x0c, 0xd3, 0xd9, 0x3a, 0xfe, 0xd6, 0x2e, 0xd4, 0x36, 0xdb, 0x80,
	0xfa, 0xdc, 0x2e, 0x6e, 0x44, 0xe6, 0x01, 0xef, 0xf9, 0xad, 0x18, 0xb9, 0x0f, 0x9d, 0xdc, 0xcd,
	0x42, 0xa0, 0x7d, 0x7b, 0x97, 0x18, 0xab, 0x2f, 0x51, 0xb0, 0x63, 0xf8, 0x3b, 0xbb, 0xb0, 0xb2,
	0x5c, 0x2e, 0x88, 0x75, 0xe6, 0x49, 0xfc, 0xc2, 0x2e, 0x5e, 0xbc, 0x83, 0x72, 0xc9, 0x9d, 0x4a,
	0xd4, 0xd3, 0xbc, 0xb8, 0x15, 0x83, 0x8c, 0x8c, 0xa8, 0x6d, 0xaf, 0x45, 0x8b, 0x0e, 0xc3, 0x2f,
	0xed, 0x88, 0x96, 0xd6, 0x05, 0x7e, 0x79, 0x2b, 0x06, 0xc5, 0xa8, 0x1f, 0xba, 0xea, 0x32, 0x86,
	0xb7, 0xb6, 0x62, 0x90, 0xa4, 0xaa, 0x24, 0x42, 0x67, 0x06, 0xed, 0x1b, 0x7e, 0x6e, 0x33, 0xde,
	0xec, 0x59, 0x22, 0xa1, 0xa9, 0xb3, 0x75, 0xd5, 0xa7, 0x3e, 0xbf, 0x19, 0x87, 0x9e, 0xbe, 0x09,
	0x42, 0x87, 0xbb, 0xc0, 0xd7, 0x18, 0xfe, 0xde, 0x66, 0x1c, 0x52, 0xbb, 0x03, 0xb8, 0x28, 0x24,
	0xfe, 0x7e, 0x97, 0x36, 0x90, 0x1b, 0xbe, 0xcd, 0x14, 0xf8, 0xd2, 0x66, 0xbc, 0x79, 0x7b, 0x46,
	0xa0, 0xfa, 0x8c, 0xaa, 0xd6, 0x7a, 0xd7, 0x63, 0xf1, 0x66, 0x6b, 0xd0, 0x81, 0x5e, 0xa4, 0x75,
	0x66, 0xaa, 0x0a, 0xf0, 0x9b, 0x8f, 0xc5, 0xc1, 0xc1, 0x2e, 0x73, 0x3c, 0x69, 0xa6, 0x7d, 0x4f,
	0x8a, 0x2a, 0x7e, 0xb9, 0xcf, 0x02, 0x0a, 0x7f, 0x94, 0x55, 0x04, 0xde, 0xda, 0xec, 0x5d, 0x40,
	0xa1, 0xca, 0x40, 0x45, 0x79, 0xa5, 0x8f, 0xf9, 0x8a, 0x02, 0xe6, 0xdf, 0xd9, 0x8c, 0x37, 0xdb,
	0xcd, 0x08, 0x9c, 0xb3, 0x85, 0xb5, 0x16, 0x2d, 0xff, 0x83, 0xcd, 0x78, 0xb3, 0x87, 0xed, 0x24,
	0x28, 0xe5, 0x3f, 0xdc, 0x8c, 0x37, 0x9b, 0x96, 0x4e, 0xb8, 0x65, 0xc0, 0x8f, 0xfa, 0x78, 0x10,
	0x70, 0xc0, 0x82, 0x1f, 0x6f, 0xc6, 0xc9, 0x9b, 0xd0, 0xa9, 0x26, 0x6a, 0x06, 0x5d, 0xb6, 0xa9,
	0x3e, 0xba, 0x9b, 0xc1, 0xab, 0x21, 0x73, 0x2c, 0x86, 0xff, 0x63, 0x33, 0xde, 0xec, 0xd5, 0x7a,
	0x89, 0xa0, 0xea, 0x67, 0x9b, 0x71, 0x38, 0x05, 0x3b, 0x30, 0x9a, 0x5b, 0xf6, 0xda, 0x66, 0x1c,
	0xf2, 0x7b, 0x07, 0x5a, 0xe6, 0x7a, 0xcd, 0x16, 0x2e, 0xc3, 0xff, 0xb9, 0x2b, 0x4b, 0x67, 0xeb,
	0x4a, 0xd7, 0x7f, 0x6d, 0xc6, 0xa1, 0x7e, 0xb4, 0xff, 0xa3, 0x34, 0x55, 0xab, 0x09, 0xee, 0xc8,
	0x2a, 0x73, 0x64, 0xf0, 0x3f, 0x03, 0xfc, 0xca, 0x0b, 0xc1, 0x0e, 0x70, 0x87, 0xc3, 0x01, 0x33,
	0x2f, 0x30, 0x87, 0xb9, 0x34, 0xfc, 0x78, 0xf7, 0xda, 0x93, 0x09, 0xd8, 0x81, 0x94, 0x23, 0x79,
	0x96, 0xae, 0x31, 0x57, 0xa7, 0x55, 0x56, 0x64, 0xd4, 0xb5, 0x56, 0xc3, 0xda, 0xfc, 0x1b, 0xaf,
	0x25, 0x60, 0xb6, 0x91, 0xce, 0xcf, 0xc3, 0x9b, 0x52, 0xe7, 0xec, 0xef, 0x3e, 0xad, 0x66, 0x03,
	0x08, 0xaa, 0xe1, 0x5d, 0xb6, 0x93, 0x70, 0xeb, 0x46, 0x02, 0x0c, 0xec, 0x20, 0x64, 0x85, 0x6b,
	0xb1, 0x9c, 0xe3, 0x31, 0x57, 0x06, 0xa4, 0xdb, 0x37, 0x7a, 0xb5, 0xa8, 0x8f, 0x49, 0x5e, 0x48,
	0xf8, 0xe2, 0x8d, 0x04, 0x6c, 0x52, 0x07, 0xa1, 0xe0, 0x2f, 0xdb, 0xdc, 0x5b, 0x85, 0x13, 0x59,
	0x14, 0x91, 0xa6, 0x2f, 0xdd, 0x48, 0xc0, 0x26, 0x19, 0x56, 0xb5, 0xac, 0x88, 0xd4, 0x86, 0x0b,
	0x20, 0xd8, 0xc5, 0x80, 0xf1, 0xd9, 0x9b, 0x09, 0x88, 0x6b, 0x17, 0x03, 0xb4, 0xd8, 0xdc, 0x61,
	0x9e, 0x17, 0xb0, 0x9e, 0xbe, 0x99, 0x80, 0xad, 0xec, 0x62, 0x45, 0x1f, 0x2d, 0xb9, 0x70, 0x02,
	0xda, 0x33, 0x37, 0x13, 0x90, 0x7e, 0x5d, 0xb4, 0x2c, 0xb5, 0xed, 0x65, 0x6a, 0xad, 0x05, 0x9c,
	0xcf, 0xdd, 0x4c, 0xc0, 0x11, 0x01, 0xdb, 0xd5, 0xab, 0xbe, 0x8a, 0x6d, 0x67, 0x90, 0xde, 0x7d,
	0x3b, 0x50, 0xd3, 0x49, 0x89, 0xbe, 0xd9, 0x04, 0x9c, 0xf7, 0xdc, 0x0e, 0x2c, 0xea, 0xe4, 0xf4,
	0x84, 0xf2, 0xbd, 0xb7, 0x13, 0xe4, 0x0c, 0x9a, 0xea, 0xa6, 0xf9, 0xb6, 0xad, 0x3e, 0x07, 0xa8,
	0x44, 0x09, 0x98, 0xef, 0xbb, 0x9d, 0x98, 0x7e, 0x10, 0xed, 0x37, 0x58, 0xc5, 0xb7, 0x29, 0x34,
	0x5e, 0xc1, 0x3f, 0xd2, 0x06, 0x51, 0x22, 0x6b, 0x64, 0x32, 0xc1, 0xf7, 0xf9, 0x62, 0x2e, 0x5f,
	0x58, 0xc8, 0x98, 0xf9, 0x9c, 0xbe, 0x68, 0x60, 0x6d, 0xee, 0xec, 0xa3, 0x0f, 0x56, 0x84, 0x4d,
	0x9d, 0xca, 0xcc, 0xc3, 0xb3, 0x52, 0xce, 0x58, 0xa2, 0x7a, 0x56, 0xfd, 0x2c, 0xc7, 0x12, 0xf6,
	0x59, 0x8f, 0xb9, 0x75, 0x6e, 0x31, 0x4f, 0xfd, 0x82, 0x27, 0xfc, 0xd1, 0xce, 0xf2, 0x3e, 0x05,
	0x3f, 0xf4, 0x7f, 0x01, 0x00, 0x00, 0xff, 0xff, 0xd5, 0xf0, 0xf3, 0xbe, 0xe6, 0x23, 0x00, 0x00,
}
