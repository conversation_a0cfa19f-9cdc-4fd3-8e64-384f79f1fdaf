// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: aigc-apps/rcmd-mt-proxy/rcmd_mt_proxy.proto

package rcmd_mt_proxy

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

// MockRcmdMtProxy_ProxyStreamClient is a mock of RcmdMtProxy_ProxyStreamClient interface.
type MockRcmdMtProxy_ProxyStreamClient struct {
	ctrl     *gomock.Controller
	recorder *MockRcmdMtProxy_ProxyStreamClientMockRecorder
}

// MockRcmdMtProxy_ProxyStreamClientMockRecorder is the mock recorder for MockRcmdMtProxy_ProxyStreamClient.
type MockRcmdMtProxy_ProxyStreamClientMockRecorder struct {
	mock *MockRcmdMtProxy_ProxyStreamClient
}

// NewMockRcmdMtProxy_ProxyStreamClient creates a new mock instance.
func NewMockRcmdMtProxy_ProxyStreamClient(ctrl *gomock.Controller) *MockRcmdMtProxy_ProxyStreamClient {
	mock := &MockRcmdMtProxy_ProxyStreamClient{ctrl: ctrl}
	mock.recorder = &MockRcmdMtProxy_ProxyStreamClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRcmdMtProxy_ProxyStreamClient) EXPECT() *MockRcmdMtProxy_ProxyStreamClientMockRecorder {
	return m.recorder
}

// CloseSend mocks base method.
func (m *MockRcmdMtProxy_ProxyStreamClient) CloseSend() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseSend")
	ret0, _ := ret[0].(error)
	return ret0
}

// CloseSend indicates an expected call of CloseSend.
func (mr *MockRcmdMtProxy_ProxyStreamClientMockRecorder) CloseSend() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseSend", reflect.TypeOf((*MockRcmdMtProxy_ProxyStreamClient)(nil).CloseSend))
}

// Context mocks base method.
func (m *MockRcmdMtProxy_ProxyStreamClient) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockRcmdMtProxy_ProxyStreamClientMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockRcmdMtProxy_ProxyStreamClient)(nil).Context))
}

// Header mocks base method.
func (m *MockRcmdMtProxy_ProxyStreamClient) Header() (metadata.MD, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Header")
	ret0, _ := ret[0].(metadata.MD)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Header indicates an expected call of Header.
func (mr *MockRcmdMtProxy_ProxyStreamClientMockRecorder) Header() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Header", reflect.TypeOf((*MockRcmdMtProxy_ProxyStreamClient)(nil).Header))
}

// Recv mocks base method.
func (m *MockRcmdMtProxy_ProxyStreamClient) Recv() (*CommonRsp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Recv")
	ret0, _ := ret[0].(*CommonRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Recv indicates an expected call of Recv.
func (mr *MockRcmdMtProxy_ProxyStreamClientMockRecorder) Recv() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Recv", reflect.TypeOf((*MockRcmdMtProxy_ProxyStreamClient)(nil).Recv))
}

// RecvMsg mocks base method.
func (m *MockRcmdMtProxy_ProxyStreamClient) RecvMsg(arg0 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecvMsg", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockRcmdMtProxy_ProxyStreamClientMockRecorder) RecvMsg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockRcmdMtProxy_ProxyStreamClient)(nil).RecvMsg), arg0)
}

// SendMsg mocks base method.
func (m *MockRcmdMtProxy_ProxyStreamClient) SendMsg(arg0 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMsg", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockRcmdMtProxy_ProxyStreamClientMockRecorder) SendMsg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockRcmdMtProxy_ProxyStreamClient)(nil).SendMsg), arg0)
}

// Trailer mocks base method.
func (m *MockRcmdMtProxy_ProxyStreamClient) Trailer() metadata.MD {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Trailer")
	ret0, _ := ret[0].(metadata.MD)
	return ret0
}

// Trailer indicates an expected call of Trailer.
func (mr *MockRcmdMtProxy_ProxyStreamClientMockRecorder) Trailer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Trailer", reflect.TypeOf((*MockRcmdMtProxy_ProxyStreamClient)(nil).Trailer))
}

// MockRcmdMtProxy_ProxyStreamServer is a mock of RcmdMtProxy_ProxyStreamServer interface.
type MockRcmdMtProxy_ProxyStreamServer struct {
	ctrl     *gomock.Controller
	recorder *MockRcmdMtProxy_ProxyStreamServerMockRecorder
}

// MockRcmdMtProxy_ProxyStreamServerMockRecorder is the mock recorder for MockRcmdMtProxy_ProxyStreamServer.
type MockRcmdMtProxy_ProxyStreamServerMockRecorder struct {
	mock *MockRcmdMtProxy_ProxyStreamServer
}

// NewMockRcmdMtProxy_ProxyStreamServer creates a new mock instance.
func NewMockRcmdMtProxy_ProxyStreamServer(ctrl *gomock.Controller) *MockRcmdMtProxy_ProxyStreamServer {
	mock := &MockRcmdMtProxy_ProxyStreamServer{ctrl: ctrl}
	mock.recorder = &MockRcmdMtProxy_ProxyStreamServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRcmdMtProxy_ProxyStreamServer) EXPECT() *MockRcmdMtProxy_ProxyStreamServerMockRecorder {
	return m.recorder
}

// Context mocks base method.
func (m *MockRcmdMtProxy_ProxyStreamServer) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockRcmdMtProxy_ProxyStreamServerMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockRcmdMtProxy_ProxyStreamServer)(nil).Context))
}

// RecvMsg mocks base method.
func (m *MockRcmdMtProxy_ProxyStreamServer) RecvMsg(arg0 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecvMsg", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockRcmdMtProxy_ProxyStreamServerMockRecorder) RecvMsg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockRcmdMtProxy_ProxyStreamServer)(nil).RecvMsg), arg0)
}

// Send mocks base method.
func (m *MockRcmdMtProxy_ProxyStreamServer) Send(arg0 *CommonRsp) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Send", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Send indicates an expected call of Send.
func (mr *MockRcmdMtProxy_ProxyStreamServerMockRecorder) Send(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send", reflect.TypeOf((*MockRcmdMtProxy_ProxyStreamServer)(nil).Send), arg0)
}

// SendHeader mocks base method.
func (m *MockRcmdMtProxy_ProxyStreamServer) SendHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendHeader indicates an expected call of SendHeader.
func (mr *MockRcmdMtProxy_ProxyStreamServerMockRecorder) SendHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendHeader", reflect.TypeOf((*MockRcmdMtProxy_ProxyStreamServer)(nil).SendHeader), arg0)
}

// SendMsg mocks base method.
func (m *MockRcmdMtProxy_ProxyStreamServer) SendMsg(arg0 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMsg", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockRcmdMtProxy_ProxyStreamServerMockRecorder) SendMsg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockRcmdMtProxy_ProxyStreamServer)(nil).SendMsg), arg0)
}

// SetHeader mocks base method.
func (m *MockRcmdMtProxy_ProxyStreamServer) SetHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetHeader indicates an expected call of SetHeader.
func (mr *MockRcmdMtProxy_ProxyStreamServerMockRecorder) SetHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHeader", reflect.TypeOf((*MockRcmdMtProxy_ProxyStreamServer)(nil).SetHeader), arg0)
}

// SetTrailer mocks base method.
func (m *MockRcmdMtProxy_ProxyStreamServer) SetTrailer(arg0 metadata.MD) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetTrailer", arg0)
}

// SetTrailer indicates an expected call of SetTrailer.
func (mr *MockRcmdMtProxy_ProxyStreamServerMockRecorder) SetTrailer(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTrailer", reflect.TypeOf((*MockRcmdMtProxy_ProxyStreamServer)(nil).SetTrailer), arg0)
}

// MockRcmdMtProxyClient is a mock of RcmdMtProxyClient interface.
type MockRcmdMtProxyClient struct {
	ctrl     *gomock.Controller
	recorder *MockRcmdMtProxyClientMockRecorder
}

// MockRcmdMtProxyClientMockRecorder is the mock recorder for MockRcmdMtProxyClient.
type MockRcmdMtProxyClientMockRecorder struct {
	mock *MockRcmdMtProxyClient
}

// NewMockRcmdMtProxyClient creates a new mock instance.
func NewMockRcmdMtProxyClient(ctrl *gomock.Controller) *MockRcmdMtProxyClient {
	mock := &MockRcmdMtProxyClient{ctrl: ctrl}
	mock.recorder = &MockRcmdMtProxyClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRcmdMtProxyClient) EXPECT() *MockRcmdMtProxyClientMockRecorder {
	return m.recorder
}

// Proxy mocks base method.
func (m *MockRcmdMtProxyClient) Proxy(ctx context.Context, in *CommonReq, opts ...grpc.CallOption) (*CommonRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Proxy", varargs...)
	ret0, _ := ret[0].(*CommonRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Proxy indicates an expected call of Proxy.
func (mr *MockRcmdMtProxyClientMockRecorder) Proxy(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Proxy", reflect.TypeOf((*MockRcmdMtProxyClient)(nil).Proxy), varargs...)
}

// ProxyStream mocks base method.
func (m *MockRcmdMtProxyClient) ProxyStream(ctx context.Context, in *CommonReq, opts ...grpc.CallOption) (RcmdMtProxy_ProxyStreamClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProxyStream", varargs...)
	ret0, _ := ret[0].(RcmdMtProxy_ProxyStreamClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProxyStream indicates an expected call of ProxyStream.
func (mr *MockRcmdMtProxyClientMockRecorder) ProxyStream(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProxyStream", reflect.TypeOf((*MockRcmdMtProxyClient)(nil).ProxyStream), varargs...)
}

// MockRcmdMtProxyServer is a mock of RcmdMtProxyServer interface.
type MockRcmdMtProxyServer struct {
	ctrl     *gomock.Controller
	recorder *MockRcmdMtProxyServerMockRecorder
}

// MockRcmdMtProxyServerMockRecorder is the mock recorder for MockRcmdMtProxyServer.
type MockRcmdMtProxyServerMockRecorder struct {
	mock *MockRcmdMtProxyServer
}

// NewMockRcmdMtProxyServer creates a new mock instance.
func NewMockRcmdMtProxyServer(ctrl *gomock.Controller) *MockRcmdMtProxyServer {
	mock := &MockRcmdMtProxyServer{ctrl: ctrl}
	mock.recorder = &MockRcmdMtProxyServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRcmdMtProxyServer) EXPECT() *MockRcmdMtProxyServerMockRecorder {
	return m.recorder
}

// Proxy mocks base method.
func (m *MockRcmdMtProxyServer) Proxy(ctx context.Context, in *CommonReq) (*CommonRsp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Proxy", ctx, in)
	ret0, _ := ret[0].(*CommonRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Proxy indicates an expected call of Proxy.
func (mr *MockRcmdMtProxyServerMockRecorder) Proxy(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Proxy", reflect.TypeOf((*MockRcmdMtProxyServer)(nil).Proxy), ctx, in)
}

// ProxyStream mocks base method.
func (m *MockRcmdMtProxyServer) ProxyStream(blob *CommonReq, server RcmdMtProxy_ProxyStreamServer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProxyStream", blob, server)
	ret0, _ := ret[0].(error)
	return ret0
}

// ProxyStream indicates an expected call of ProxyStream.
func (mr *MockRcmdMtProxyServerMockRecorder) ProxyStream(blob, server interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProxyStream", reflect.TypeOf((*MockRcmdMtProxyServer)(nil).ProxyStream), blob, server)
}
