// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: aigc-apps/common/dao/aigc-data-service.proto

package aigc_data_service

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAIGCDataServiceClient is a mock of AIGCDataServiceClient interface.
type MockAIGCDataServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockAIGCDataServiceClientMockRecorder
}

// MockAIGCDataServiceClientMockRecorder is the mock recorder for MockAIGCDataServiceClient.
type MockAIGCDataServiceClientMockRecorder struct {
	mock *MockAIGCDataServiceClient
}

// NewMockAIGCDataServiceClient creates a new mock instance.
func NewMockAIGCDataServiceClient(ctrl *gomock.Controller) *MockAIGCDataServiceClient {
	mock := &MockAIGCDataServiceClient{ctrl: ctrl}
	mock.recorder = &MockAIGCDataServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAIGCDataServiceClient) EXPECT() *MockAIGCDataServiceClientMockRecorder {
	return m.recorder
}

// BatchGetChannelState mocks base method.
func (m *MockAIGCDataServiceClient) BatchGetChannelState(ctx context.Context, in *BatchGetChannelStateReq, opts ...grpc.CallOption) (*BatchGetChannelStateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetChannelState", varargs...)
	ret0, _ := ret[0].(*BatchGetChannelStateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelState indicates an expected call of BatchGetChannelState.
func (mr *MockAIGCDataServiceClientMockRecorder) BatchGetChannelState(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelState", reflect.TypeOf((*MockAIGCDataServiceClient)(nil).BatchGetChannelState), varargs...)
}

// BatchGetPodInfoWithChannel mocks base method.
func (m *MockAIGCDataServiceClient) BatchGetPodInfoWithChannel(ctx context.Context, in *BatchGetPodInfoWithChannelReq, opts ...grpc.CallOption) (*BatchGetPodInfoWithChannelResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetPodInfoWithChannel", varargs...)
	ret0, _ := ret[0].(*BatchGetPodInfoWithChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPodInfoWithChannel indicates an expected call of BatchGetPodInfoWithChannel.
func (mr *MockAIGCDataServiceClientMockRecorder) BatchGetPodInfoWithChannel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPodInfoWithChannel", reflect.TypeOf((*MockAIGCDataServiceClient)(nil).BatchGetPodInfoWithChannel), varargs...)
}

// GetGroupChatHistory mocks base method.
func (m *MockAIGCDataServiceClient) GetGroupChatHistory(ctx context.Context, in *GetGroupChatHistoryReq, opts ...grpc.CallOption) (*GetGroupChatHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGroupChatHistory", varargs...)
	ret0, _ := ret[0].(*GetGroupChatHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupChatHistory indicates an expected call of GetGroupChatHistory.
func (mr *MockAIGCDataServiceClientMockRecorder) GetGroupChatHistory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupChatHistory", reflect.TypeOf((*MockAIGCDataServiceClient)(nil).GetGroupChatHistory), varargs...)
}

// RemoveGroupChatHistory mocks base method.
func (m *MockAIGCDataServiceClient) RemoveGroupChatHistory(ctx context.Context, in *RemoveGroupChatHistoryReq, opts ...grpc.CallOption) (*RemoveGroupChatHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RemoveGroupChatHistory", varargs...)
	ret0, _ := ret[0].(*RemoveGroupChatHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveGroupChatHistory indicates an expected call of RemoveGroupChatHistory.
func (mr *MockAIGCDataServiceClientMockRecorder) RemoveGroupChatHistory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveGroupChatHistory", reflect.TypeOf((*MockAIGCDataServiceClient)(nil).RemoveGroupChatHistory), varargs...)
}

// UpdateAIState mocks base method.
func (m *MockAIGCDataServiceClient) UpdateAIState(ctx context.Context, in *UpdateAIStateReq, opts ...grpc.CallOption) (*UpdateAIStateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAIState", varargs...)
	ret0, _ := ret[0].(*UpdateAIStateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAIState indicates an expected call of UpdateAIState.
func (mr *MockAIGCDataServiceClientMockRecorder) UpdateAIState(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIState", reflect.TypeOf((*MockAIGCDataServiceClient)(nil).UpdateAIState), varargs...)
}

// UpdateGroupChatHistory mocks base method.
func (m *MockAIGCDataServiceClient) UpdateGroupChatHistory(ctx context.Context, in *UpdateGroupChatHistoryReq, opts ...grpc.CallOption) (*UpdateGroupChatHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateGroupChatHistory", varargs...)
	ret0, _ := ret[0].(*UpdateGroupChatHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateGroupChatHistory indicates an expected call of UpdateGroupChatHistory.
func (mr *MockAIGCDataServiceClientMockRecorder) UpdateGroupChatHistory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGroupChatHistory", reflect.TypeOf((*MockAIGCDataServiceClient)(nil).UpdateGroupChatHistory), varargs...)
}

// UpdatePodInfo mocks base method.
func (m *MockAIGCDataServiceClient) UpdatePodInfo(ctx context.Context, in *UpdatePodInfoReq, opts ...grpc.CallOption) (*UpdatePodInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePodInfo", varargs...)
	ret0, _ := ret[0].(*UpdatePodInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePodInfo indicates an expected call of UpdatePodInfo.
func (mr *MockAIGCDataServiceClientMockRecorder) UpdatePodInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePodInfo", reflect.TypeOf((*MockAIGCDataServiceClient)(nil).UpdatePodInfo), varargs...)
}

// UpdateUserLastSpeakTimes mocks base method.
func (m *MockAIGCDataServiceClient) UpdateUserLastSpeakTimes(ctx context.Context, in *UpdateUserLastSpeakTimesReq, opts ...grpc.CallOption) (*UpdateUserLastSpeakTimesResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateUserLastSpeakTimes", varargs...)
	ret0, _ := ret[0].(*UpdateUserLastSpeakTimesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserLastSpeakTimes indicates an expected call of UpdateUserLastSpeakTimes.
func (mr *MockAIGCDataServiceClientMockRecorder) UpdateUserLastSpeakTimes(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserLastSpeakTimes", reflect.TypeOf((*MockAIGCDataServiceClient)(nil).UpdateUserLastSpeakTimes), varargs...)
}

// MockAIGCDataServiceServer is a mock of AIGCDataServiceServer interface.
type MockAIGCDataServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockAIGCDataServiceServerMockRecorder
}

// MockAIGCDataServiceServerMockRecorder is the mock recorder for MockAIGCDataServiceServer.
type MockAIGCDataServiceServerMockRecorder struct {
	mock *MockAIGCDataServiceServer
}

// NewMockAIGCDataServiceServer creates a new mock instance.
func NewMockAIGCDataServiceServer(ctrl *gomock.Controller) *MockAIGCDataServiceServer {
	mock := &MockAIGCDataServiceServer{ctrl: ctrl}
	mock.recorder = &MockAIGCDataServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAIGCDataServiceServer) EXPECT() *MockAIGCDataServiceServerMockRecorder {
	return m.recorder
}

// BatchGetChannelState mocks base method.
func (m *MockAIGCDataServiceServer) BatchGetChannelState(ctx context.Context, in *BatchGetChannelStateReq) (*BatchGetChannelStateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelState", ctx, in)
	ret0, _ := ret[0].(*BatchGetChannelStateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelState indicates an expected call of BatchGetChannelState.
func (mr *MockAIGCDataServiceServerMockRecorder) BatchGetChannelState(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelState", reflect.TypeOf((*MockAIGCDataServiceServer)(nil).BatchGetChannelState), ctx, in)
}

// BatchGetPodInfoWithChannel mocks base method.
func (m *MockAIGCDataServiceServer) BatchGetPodInfoWithChannel(ctx context.Context, in *BatchGetPodInfoWithChannelReq) (*BatchGetPodInfoWithChannelResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPodInfoWithChannel", ctx, in)
	ret0, _ := ret[0].(*BatchGetPodInfoWithChannelResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPodInfoWithChannel indicates an expected call of BatchGetPodInfoWithChannel.
func (mr *MockAIGCDataServiceServerMockRecorder) BatchGetPodInfoWithChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPodInfoWithChannel", reflect.TypeOf((*MockAIGCDataServiceServer)(nil).BatchGetPodInfoWithChannel), ctx, in)
}

// GetGroupChatHistory mocks base method.
func (m *MockAIGCDataServiceServer) GetGroupChatHistory(ctx context.Context, in *GetGroupChatHistoryReq) (*GetGroupChatHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupChatHistory", ctx, in)
	ret0, _ := ret[0].(*GetGroupChatHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupChatHistory indicates an expected call of GetGroupChatHistory.
func (mr *MockAIGCDataServiceServerMockRecorder) GetGroupChatHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupChatHistory", reflect.TypeOf((*MockAIGCDataServiceServer)(nil).GetGroupChatHistory), ctx, in)
}

// RemoveGroupChatHistory mocks base method.
func (m *MockAIGCDataServiceServer) RemoveGroupChatHistory(ctx context.Context, in *RemoveGroupChatHistoryReq) (*RemoveGroupChatHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveGroupChatHistory", ctx, in)
	ret0, _ := ret[0].(*RemoveGroupChatHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveGroupChatHistory indicates an expected call of RemoveGroupChatHistory.
func (mr *MockAIGCDataServiceServerMockRecorder) RemoveGroupChatHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveGroupChatHistory", reflect.TypeOf((*MockAIGCDataServiceServer)(nil).RemoveGroupChatHistory), ctx, in)
}

// UpdateAIState mocks base method.
func (m *MockAIGCDataServiceServer) UpdateAIState(ctx context.Context, in *UpdateAIStateReq) (*UpdateAIStateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAIState", ctx, in)
	ret0, _ := ret[0].(*UpdateAIStateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAIState indicates an expected call of UpdateAIState.
func (mr *MockAIGCDataServiceServerMockRecorder) UpdateAIState(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAIState", reflect.TypeOf((*MockAIGCDataServiceServer)(nil).UpdateAIState), ctx, in)
}

// UpdateGroupChatHistory mocks base method.
func (m *MockAIGCDataServiceServer) UpdateGroupChatHistory(ctx context.Context, in *UpdateGroupChatHistoryReq) (*UpdateGroupChatHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGroupChatHistory", ctx, in)
	ret0, _ := ret[0].(*UpdateGroupChatHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateGroupChatHistory indicates an expected call of UpdateGroupChatHistory.
func (mr *MockAIGCDataServiceServerMockRecorder) UpdateGroupChatHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGroupChatHistory", reflect.TypeOf((*MockAIGCDataServiceServer)(nil).UpdateGroupChatHistory), ctx, in)
}

// UpdatePodInfo mocks base method.
func (m *MockAIGCDataServiceServer) UpdatePodInfo(ctx context.Context, in *UpdatePodInfoReq) (*UpdatePodInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePodInfo", ctx, in)
	ret0, _ := ret[0].(*UpdatePodInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePodInfo indicates an expected call of UpdatePodInfo.
func (mr *MockAIGCDataServiceServerMockRecorder) UpdatePodInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePodInfo", reflect.TypeOf((*MockAIGCDataServiceServer)(nil).UpdatePodInfo), ctx, in)
}

// UpdateUserLastSpeakTimes mocks base method.
func (m *MockAIGCDataServiceServer) UpdateUserLastSpeakTimes(ctx context.Context, in *UpdateUserLastSpeakTimesReq) (*UpdateUserLastSpeakTimesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserLastSpeakTimes", ctx, in)
	ret0, _ := ret[0].(*UpdateUserLastSpeakTimesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserLastSpeakTimes indicates an expected call of UpdateUserLastSpeakTimes.
func (mr *MockAIGCDataServiceServerMockRecorder) UpdateUserLastSpeakTimes(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserLastSpeakTimes", reflect.TypeOf((*MockAIGCDataServiceServer)(nil).UpdateUserLastSpeakTimes), ctx, in)
}
