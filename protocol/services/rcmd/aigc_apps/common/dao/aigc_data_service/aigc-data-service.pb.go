// Code generated by protoc-gen-go. DO NOT EDIT.
// source: aigc-apps/common/dao/aigc-data-service.proto

package aigc_data_service // import "golang.52tt.com/protocol/services/rcmd/aigc_apps/common/dao/aigc_data_service"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 查询时，填了ChatScene则根据ChatScene进行筛选
type ChatScene int32

const (
	ChatScene_DEFAULT    ChatScene = 0
	ChatScene_ON_MIC_ASR ChatScene = 1
	ChatScene_SCREEN     ChatScene = 2
	ChatScene_NOTIFY     ChatScene = 3
)

var ChatScene_name = map[int32]string{
	0: "DEFAULT",
	1: "ON_MIC_ASR",
	2: "SCREEN",
	3: "NOTIFY",
}
var ChatScene_value = map[string]int32{
	"DEFAULT":    0,
	"ON_MIC_ASR": 1,
	"SCREEN":     2,
	"NOTIFY":     3,
}

func (x ChatScene) String() string {
	return proto.EnumName(ChatScene_name, int32(x))
}
func (ChatScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{0}
}

type Source int32

const (
	Source_INVALID Source = 0
	Source_AI_ROOM Source = 1
)

var Source_name = map[int32]string{
	0: "INVALID",
	1: "AI_ROOM",
}
var Source_value = map[string]int32{
	"INVALID": 0,
	"AI_ROOM": 1,
}

func (x Source) String() string {
	return proto.EnumName(Source_name, int32(x))
}
func (Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{1}
}

type QueryType int32

const (
	QueryType_QUERY_TYPE_INVALID   QueryType = 0
	QueryType_AI_STATE             QueryType = 1
	QueryType_USER_LAST_SPEAK_TIME QueryType = 2
)

var QueryType_name = map[int32]string{
	0: "QUERY_TYPE_INVALID",
	1: "AI_STATE",
	2: "USER_LAST_SPEAK_TIME",
}
var QueryType_value = map[string]int32{
	"QUERY_TYPE_INVALID":   0,
	"AI_STATE":             1,
	"USER_LAST_SPEAK_TIME": 2,
}

func (x QueryType) String() string {
	return proto.EnumName(QueryType_name, int32(x))
}
func (QueryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{2}
}

type AIState int32

const (
	AIState_AVAILABLE AIState = 0
	AIState_HANDLING  AIState = 1
)

var AIState_name = map[int32]string{
	0: "AVAILABLE",
	1: "HANDLING",
}
var AIState_value = map[string]int32{
	"AVAILABLE": 0,
	"HANDLING":  1,
}

func (x AIState) String() string {
	return proto.EnumName(AIState_name, int32(x))
}
func (AIState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{3}
}

type ChatHistory_Role int32

const (
	ChatHistory_ROLE_DEFAULT ChatHistory_Role = 0
	ChatHistory_USER         ChatHistory_Role = 1
	ChatHistory_ASSISTANT    ChatHistory_Role = 2
)

var ChatHistory_Role_name = map[int32]string{
	0: "ROLE_DEFAULT",
	1: "USER",
	2: "ASSISTANT",
}
var ChatHistory_Role_value = map[string]int32{
	"ROLE_DEFAULT": 0,
	"USER":         1,
	"ASSISTANT":    2,
}

func (x ChatHistory_Role) String() string {
	return proto.EnumName(ChatHistory_Role_name, int32(x))
}
func (ChatHistory_Role) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{0, 0}
}

type UpdateAIStateReq_Operation int32

const (
	UpdateAIStateReq_UPDATE UpdateAIStateReq_Operation = 0
	UpdateAIStateReq_DELETE UpdateAIStateReq_Operation = 1
)

var UpdateAIStateReq_Operation_name = map[int32]string{
	0: "UPDATE",
	1: "DELETE",
}
var UpdateAIStateReq_Operation_value = map[string]int32{
	"UPDATE": 0,
	"DELETE": 1,
}

func (x UpdateAIStateReq_Operation) String() string {
	return proto.EnumName(UpdateAIStateReq_Operation_name, int32(x))
}
func (UpdateAIStateReq_Operation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{12, 0}
}

type UpdateUserLastSpeakTimesReq_Operation int32

const (
	UpdateUserLastSpeakTimesReq_UPDATE UpdateUserLastSpeakTimesReq_Operation = 0
	UpdateUserLastSpeakTimesReq_DELETE UpdateUserLastSpeakTimesReq_Operation = 1
)

var UpdateUserLastSpeakTimesReq_Operation_name = map[int32]string{
	0: "UPDATE",
	1: "DELETE",
}
var UpdateUserLastSpeakTimesReq_Operation_value = map[string]int32{
	"UPDATE": 0,
	"DELETE": 1,
}

func (x UpdateUserLastSpeakTimesReq_Operation) String() string {
	return proto.EnumName(UpdateUserLastSpeakTimesReq_Operation_name, int32(x))
}
func (UpdateUserLastSpeakTimesReq_Operation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{14, 0}
}

type UpdatePodInfoReq_Operation int32

const (
	UpdatePodInfoReq_UPDATE UpdatePodInfoReq_Operation = 0
	UpdatePodInfoReq_DELETE UpdatePodInfoReq_Operation = 1
)

var UpdatePodInfoReq_Operation_name = map[int32]string{
	0: "UPDATE",
	1: "DELETE",
}
var UpdatePodInfoReq_Operation_value = map[string]int32{
	"UPDATE": 0,
	"DELETE": 1,
}

func (x UpdatePodInfoReq_Operation) String() string {
	return proto.EnumName(UpdatePodInfoReq_Operation_name, int32(x))
}
func (UpdatePodInfoReq_Operation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{19, 0}
}

type ChatHistory struct {
	SentenceId           string            `protobuf:"bytes,1,opt,name=sentence_id,json=sentenceId,proto3" json:"sentence_id,omitempty"`
	Role                 ChatHistory_Role  `protobuf:"varint,2,opt,name=role,proto3,enum=aigc.common.dao.aigc_data_service.ChatHistory_Role" json:"role,omitempty"`
	Uid                  uint32            `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	ChatScene            ChatScene         `protobuf:"varint,4,opt,name=chat_scene,json=chatScene,proto3,enum=aigc.common.dao.aigc_data_service.ChatScene" json:"chat_scene,omitempty"`
	Content              string            `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	SortValue            int64             `protobuf:"varint,6,opt,name=sort_value,json=sortValue,proto3" json:"sort_value,omitempty"`
	Extra                map[string]string `protobuf:"bytes,7,rep,name=extra,proto3" json:"extra,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ChatHistory) Reset()         { *m = ChatHistory{} }
func (m *ChatHistory) String() string { return proto.CompactTextString(m) }
func (*ChatHistory) ProtoMessage()    {}
func (*ChatHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{0}
}
func (m *ChatHistory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatHistory.Unmarshal(m, b)
}
func (m *ChatHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatHistory.Marshal(b, m, deterministic)
}
func (dst *ChatHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatHistory.Merge(dst, src)
}
func (m *ChatHistory) XXX_Size() int {
	return xxx_messageInfo_ChatHistory.Size(m)
}
func (m *ChatHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatHistory.DiscardUnknown(m)
}

var xxx_messageInfo_ChatHistory proto.InternalMessageInfo

func (m *ChatHistory) GetSentenceId() string {
	if m != nil {
		return m.SentenceId
	}
	return ""
}

func (m *ChatHistory) GetRole() ChatHistory_Role {
	if m != nil {
		return m.Role
	}
	return ChatHistory_ROLE_DEFAULT
}

func (m *ChatHistory) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChatHistory) GetChatScene() ChatScene {
	if m != nil {
		return m.ChatScene
	}
	return ChatScene_DEFAULT
}

func (m *ChatHistory) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ChatHistory) GetSortValue() int64 {
	if m != nil {
		return m.SortValue
	}
	return 0
}

func (m *ChatHistory) GetExtra() map[string]string {
	if m != nil {
		return m.Extra
	}
	return nil
}

type GetGroupChatHistoryReq struct {
	GroupId              string    `protobuf:"bytes,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Source               Source    `protobuf:"varint,2,opt,name=source,proto3,enum=aigc.common.dao.aigc_data_service.Source" json:"source,omitempty"`
	ChatScene            ChatScene `protobuf:"varint,3,opt,name=chat_scene,json=chatScene,proto3,enum=aigc.common.dao.aigc_data_service.ChatScene" json:"chat_scene,omitempty"`
	StartIndex           int32     `protobuf:"varint,4,opt,name=start_index,json=startIndex,proto3" json:"start_index,omitempty"`
	EndIndex             int32     `protobuf:"varint,5,opt,name=end_index,json=endIndex,proto3" json:"end_index,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetGroupChatHistoryReq) Reset()         { *m = GetGroupChatHistoryReq{} }
func (m *GetGroupChatHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupChatHistoryReq) ProtoMessage()    {}
func (*GetGroupChatHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{1}
}
func (m *GetGroupChatHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupChatHistoryReq.Unmarshal(m, b)
}
func (m *GetGroupChatHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupChatHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetGroupChatHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupChatHistoryReq.Merge(dst, src)
}
func (m *GetGroupChatHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetGroupChatHistoryReq.Size(m)
}
func (m *GetGroupChatHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupChatHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupChatHistoryReq proto.InternalMessageInfo

func (m *GetGroupChatHistoryReq) GetGroupId() string {
	if m != nil {
		return m.GroupId
	}
	return ""
}

func (m *GetGroupChatHistoryReq) GetSource() Source {
	if m != nil {
		return m.Source
	}
	return Source_INVALID
}

func (m *GetGroupChatHistoryReq) GetChatScene() ChatScene {
	if m != nil {
		return m.ChatScene
	}
	return ChatScene_DEFAULT
}

func (m *GetGroupChatHistoryReq) GetStartIndex() int32 {
	if m != nil {
		return m.StartIndex
	}
	return 0
}

func (m *GetGroupChatHistoryReq) GetEndIndex() int32 {
	if m != nil {
		return m.EndIndex
	}
	return 0
}

type GetGroupChatHistoryResp struct {
	ChatHistory          []*ChatHistory `protobuf:"bytes,1,rep,name=chat_history,json=chatHistory,proto3" json:"chat_history,omitempty"`
	Code                 int32          `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string         `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetGroupChatHistoryResp) Reset()         { *m = GetGroupChatHistoryResp{} }
func (m *GetGroupChatHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetGroupChatHistoryResp) ProtoMessage()    {}
func (*GetGroupChatHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{2}
}
func (m *GetGroupChatHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupChatHistoryResp.Unmarshal(m, b)
}
func (m *GetGroupChatHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupChatHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetGroupChatHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupChatHistoryResp.Merge(dst, src)
}
func (m *GetGroupChatHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetGroupChatHistoryResp.Size(m)
}
func (m *GetGroupChatHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupChatHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupChatHistoryResp proto.InternalMessageInfo

func (m *GetGroupChatHistoryResp) GetChatHistory() []*ChatHistory {
	if m != nil {
		return m.ChatHistory
	}
	return nil
}

func (m *GetGroupChatHistoryResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *GetGroupChatHistoryResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type UpdateGroupChatHistoryReq struct {
	GroupId              string         `protobuf:"bytes,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Source               Source         `protobuf:"varint,2,opt,name=source,proto3,enum=aigc.common.dao.aigc_data_service.Source" json:"source,omitempty"`
	ChatHistory          []*ChatHistory `protobuf:"bytes,3,rep,name=chat_history,json=chatHistory,proto3" json:"chat_history,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UpdateGroupChatHistoryReq) Reset()         { *m = UpdateGroupChatHistoryReq{} }
func (m *UpdateGroupChatHistoryReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupChatHistoryReq) ProtoMessage()    {}
func (*UpdateGroupChatHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{3}
}
func (m *UpdateGroupChatHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupChatHistoryReq.Unmarshal(m, b)
}
func (m *UpdateGroupChatHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupChatHistoryReq.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupChatHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupChatHistoryReq.Merge(dst, src)
}
func (m *UpdateGroupChatHistoryReq) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupChatHistoryReq.Size(m)
}
func (m *UpdateGroupChatHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupChatHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupChatHistoryReq proto.InternalMessageInfo

func (m *UpdateGroupChatHistoryReq) GetGroupId() string {
	if m != nil {
		return m.GroupId
	}
	return ""
}

func (m *UpdateGroupChatHistoryReq) GetSource() Source {
	if m != nil {
		return m.Source
	}
	return Source_INVALID
}

func (m *UpdateGroupChatHistoryReq) GetChatHistory() []*ChatHistory {
	if m != nil {
		return m.ChatHistory
	}
	return nil
}

type UpdateGroupChatHistoryResp struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGroupChatHistoryResp) Reset()         { *m = UpdateGroupChatHistoryResp{} }
func (m *UpdateGroupChatHistoryResp) String() string { return proto.CompactTextString(m) }
func (*UpdateGroupChatHistoryResp) ProtoMessage()    {}
func (*UpdateGroupChatHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{4}
}
func (m *UpdateGroupChatHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGroupChatHistoryResp.Unmarshal(m, b)
}
func (m *UpdateGroupChatHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGroupChatHistoryResp.Marshal(b, m, deterministic)
}
func (dst *UpdateGroupChatHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGroupChatHistoryResp.Merge(dst, src)
}
func (m *UpdateGroupChatHistoryResp) XXX_Size() int {
	return xxx_messageInfo_UpdateGroupChatHistoryResp.Size(m)
}
func (m *UpdateGroupChatHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGroupChatHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGroupChatHistoryResp proto.InternalMessageInfo

func (m *UpdateGroupChatHistoryResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UpdateGroupChatHistoryResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type RemoveGroupChatHistoryReq struct {
	GroupId              string   `protobuf:"bytes,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Source               Source   `protobuf:"varint,2,opt,name=source,proto3,enum=aigc.common.dao.aigc_data_service.Source" json:"source,omitempty"`
	SentenceIds          []string `protobuf:"bytes,3,rep,name=sentence_ids,json=sentenceIds,proto3" json:"sentence_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveGroupChatHistoryReq) Reset()         { *m = RemoveGroupChatHistoryReq{} }
func (m *RemoveGroupChatHistoryReq) String() string { return proto.CompactTextString(m) }
func (*RemoveGroupChatHistoryReq) ProtoMessage()    {}
func (*RemoveGroupChatHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{5}
}
func (m *RemoveGroupChatHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveGroupChatHistoryReq.Unmarshal(m, b)
}
func (m *RemoveGroupChatHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveGroupChatHistoryReq.Marshal(b, m, deterministic)
}
func (dst *RemoveGroupChatHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveGroupChatHistoryReq.Merge(dst, src)
}
func (m *RemoveGroupChatHistoryReq) XXX_Size() int {
	return xxx_messageInfo_RemoveGroupChatHistoryReq.Size(m)
}
func (m *RemoveGroupChatHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveGroupChatHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveGroupChatHistoryReq proto.InternalMessageInfo

func (m *RemoveGroupChatHistoryReq) GetGroupId() string {
	if m != nil {
		return m.GroupId
	}
	return ""
}

func (m *RemoveGroupChatHistoryReq) GetSource() Source {
	if m != nil {
		return m.Source
	}
	return Source_INVALID
}

func (m *RemoveGroupChatHistoryReq) GetSentenceIds() []string {
	if m != nil {
		return m.SentenceIds
	}
	return nil
}

type RemoveGroupChatHistoryResp struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveGroupChatHistoryResp) Reset()         { *m = RemoveGroupChatHistoryResp{} }
func (m *RemoveGroupChatHistoryResp) String() string { return proto.CompactTextString(m) }
func (*RemoveGroupChatHistoryResp) ProtoMessage()    {}
func (*RemoveGroupChatHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{6}
}
func (m *RemoveGroupChatHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveGroupChatHistoryResp.Unmarshal(m, b)
}
func (m *RemoveGroupChatHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveGroupChatHistoryResp.Marshal(b, m, deterministic)
}
func (dst *RemoveGroupChatHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveGroupChatHistoryResp.Merge(dst, src)
}
func (m *RemoveGroupChatHistoryResp) XXX_Size() int {
	return xxx_messageInfo_RemoveGroupChatHistoryResp.Size(m)
}
func (m *RemoveGroupChatHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveGroupChatHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveGroupChatHistoryResp proto.InternalMessageInfo

func (m *RemoveGroupChatHistoryResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *RemoveGroupChatHistoryResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type AIUids struct {
	Ids                  []uint32 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIUids) Reset()         { *m = AIUids{} }
func (m *AIUids) String() string { return proto.CompactTextString(m) }
func (*AIUids) ProtoMessage()    {}
func (*AIUids) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{7}
}
func (m *AIUids) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIUids.Unmarshal(m, b)
}
func (m *AIUids) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIUids.Marshal(b, m, deterministic)
}
func (dst *AIUids) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIUids.Merge(dst, src)
}
func (m *AIUids) XXX_Size() int {
	return xxx_messageInfo_AIUids.Size(m)
}
func (m *AIUids) XXX_DiscardUnknown() {
	xxx_messageInfo_AIUids.DiscardUnknown(m)
}

var xxx_messageInfo_AIUids proto.InternalMessageInfo

func (m *AIUids) GetIds() []uint32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type BatchGetChannelStateReq struct {
	ChannelIds           []uint32    `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	QueryTypes           []QueryType `protobuf:"varint,2,rep,packed,name=query_types,json=queryTypes,proto3,enum=aigc.common.dao.aigc_data_service.QueryType" json:"query_types,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *BatchGetChannelStateReq) Reset()         { *m = BatchGetChannelStateReq{} }
func (m *BatchGetChannelStateReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelStateReq) ProtoMessage()    {}
func (*BatchGetChannelStateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{8}
}
func (m *BatchGetChannelStateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelStateReq.Unmarshal(m, b)
}
func (m *BatchGetChannelStateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelStateReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelStateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelStateReq.Merge(dst, src)
}
func (m *BatchGetChannelStateReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelStateReq.Size(m)
}
func (m *BatchGetChannelStateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelStateReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelStateReq proto.InternalMessageInfo

func (m *BatchGetChannelStateReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

func (m *BatchGetChannelStateReq) GetQueryTypes() []QueryType {
	if m != nil {
		return m.QueryTypes
	}
	return nil
}

type AIStates struct {
	Aiuid_2State         map[uint32]AIState `protobuf:"bytes,1,rep,name=aiuid_2_state,json=aiuid2State,proto3" json:"aiuid_2_state,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=aigc.common.dao.aigc_data_service.AIState"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AIStates) Reset()         { *m = AIStates{} }
func (m *AIStates) String() string { return proto.CompactTextString(m) }
func (*AIStates) ProtoMessage()    {}
func (*AIStates) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{9}
}
func (m *AIStates) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIStates.Unmarshal(m, b)
}
func (m *AIStates) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIStates.Marshal(b, m, deterministic)
}
func (dst *AIStates) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIStates.Merge(dst, src)
}
func (m *AIStates) XXX_Size() int {
	return xxx_messageInfo_AIStates.Size(m)
}
func (m *AIStates) XXX_DiscardUnknown() {
	xxx_messageInfo_AIStates.DiscardUnknown(m)
}

var xxx_messageInfo_AIStates proto.InternalMessageInfo

func (m *AIStates) GetAiuid_2State() map[uint32]AIState {
	if m != nil {
		return m.Aiuid_2State
	}
	return nil
}

type UserLastSpeakTimes struct {
	Uid_2Ts              map[uint32]int64 `protobuf:"bytes,1,rep,name=uid_2_ts,json=uid2Ts,proto3" json:"uid_2_ts,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserLastSpeakTimes) Reset()         { *m = UserLastSpeakTimes{} }
func (m *UserLastSpeakTimes) String() string { return proto.CompactTextString(m) }
func (*UserLastSpeakTimes) ProtoMessage()    {}
func (*UserLastSpeakTimes) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{10}
}
func (m *UserLastSpeakTimes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLastSpeakTimes.Unmarshal(m, b)
}
func (m *UserLastSpeakTimes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLastSpeakTimes.Marshal(b, m, deterministic)
}
func (dst *UserLastSpeakTimes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLastSpeakTimes.Merge(dst, src)
}
func (m *UserLastSpeakTimes) XXX_Size() int {
	return xxx_messageInfo_UserLastSpeakTimes.Size(m)
}
func (m *UserLastSpeakTimes) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLastSpeakTimes.DiscardUnknown(m)
}

var xxx_messageInfo_UserLastSpeakTimes proto.InternalMessageInfo

func (m *UserLastSpeakTimes) GetUid_2Ts() map[uint32]int64 {
	if m != nil {
		return m.Uid_2Ts
	}
	return nil
}

type BatchGetChannelStateResp struct {
	Channel_2AiStates    map[uint32]*AIStates           `protobuf:"bytes,1,rep,name=channel_2_ai_states,json=channel2AiStates,proto3" json:"channel_2_ai_states,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Channel_2UserTimes   map[uint32]*UserLastSpeakTimes `protobuf:"bytes,2,rep,name=channel_2_user_times,json=channel2UserTimes,proto3" json:"channel_2_user_times,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *BatchGetChannelStateResp) Reset()         { *m = BatchGetChannelStateResp{} }
func (m *BatchGetChannelStateResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelStateResp) ProtoMessage()    {}
func (*BatchGetChannelStateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{11}
}
func (m *BatchGetChannelStateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelStateResp.Unmarshal(m, b)
}
func (m *BatchGetChannelStateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelStateResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelStateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelStateResp.Merge(dst, src)
}
func (m *BatchGetChannelStateResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelStateResp.Size(m)
}
func (m *BatchGetChannelStateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelStateResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelStateResp proto.InternalMessageInfo

func (m *BatchGetChannelStateResp) GetChannel_2AiStates() map[uint32]*AIStates {
	if m != nil {
		return m.Channel_2AiStates
	}
	return nil
}

func (m *BatchGetChannelStateResp) GetChannel_2UserTimes() map[uint32]*UserLastSpeakTimes {
	if m != nil {
		return m.Channel_2UserTimes
	}
	return nil
}

// 更新AI状态
type UpdateAIStateReq struct {
	Operation            UpdateAIStateReq_Operation `protobuf:"varint,1,opt,name=operation,proto3,enum=aigc.common.dao.aigc_data_service.UpdateAIStateReq_Operation" json:"operation,omitempty"`
	AiUid                uint32                     `protobuf:"varint,2,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	ChannelId            uint32                     `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AiState              AIState                    `protobuf:"varint,4,opt,name=ai_state,json=aiState,proto3,enum=aigc.common.dao.aigc_data_service.AIState" json:"ai_state,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *UpdateAIStateReq) Reset()         { *m = UpdateAIStateReq{} }
func (m *UpdateAIStateReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAIStateReq) ProtoMessage()    {}
func (*UpdateAIStateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{12}
}
func (m *UpdateAIStateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIStateReq.Unmarshal(m, b)
}
func (m *UpdateAIStateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIStateReq.Marshal(b, m, deterministic)
}
func (dst *UpdateAIStateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIStateReq.Merge(dst, src)
}
func (m *UpdateAIStateReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAIStateReq.Size(m)
}
func (m *UpdateAIStateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIStateReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIStateReq proto.InternalMessageInfo

func (m *UpdateAIStateReq) GetOperation() UpdateAIStateReq_Operation {
	if m != nil {
		return m.Operation
	}
	return UpdateAIStateReq_UPDATE
}

func (m *UpdateAIStateReq) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

func (m *UpdateAIStateReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UpdateAIStateReq) GetAiState() AIState {
	if m != nil {
		return m.AiState
	}
	return AIState_AVAILABLE
}

type UpdateAIStateResp struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAIStateResp) Reset()         { *m = UpdateAIStateResp{} }
func (m *UpdateAIStateResp) String() string { return proto.CompactTextString(m) }
func (*UpdateAIStateResp) ProtoMessage()    {}
func (*UpdateAIStateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{13}
}
func (m *UpdateAIStateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAIStateResp.Unmarshal(m, b)
}
func (m *UpdateAIStateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAIStateResp.Marshal(b, m, deterministic)
}
func (dst *UpdateAIStateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAIStateResp.Merge(dst, src)
}
func (m *UpdateAIStateResp) XXX_Size() int {
	return xxx_messageInfo_UpdateAIStateResp.Size(m)
}
func (m *UpdateAIStateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAIStateResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAIStateResp proto.InternalMessageInfo

func (m *UpdateAIStateResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UpdateAIStateResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

// 更新用户最后说话时间
type UpdateUserLastSpeakTimesReq struct {
	Operation            UpdateUserLastSpeakTimesReq_Operation `protobuf:"varint,1,opt,name=operation,proto3,enum=aigc.common.dao.aigc_data_service.UpdateUserLastSpeakTimesReq_Operation" json:"operation,omitempty"`
	Uid                  uint32                                `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32                                `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TimeStamp            int64                                 `protobuf:"varint,4,opt,name=time_stamp,json=timeStamp,proto3" json:"time_stamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *UpdateUserLastSpeakTimesReq) Reset()         { *m = UpdateUserLastSpeakTimesReq{} }
func (m *UpdateUserLastSpeakTimesReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserLastSpeakTimesReq) ProtoMessage()    {}
func (*UpdateUserLastSpeakTimesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{14}
}
func (m *UpdateUserLastSpeakTimesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserLastSpeakTimesReq.Unmarshal(m, b)
}
func (m *UpdateUserLastSpeakTimesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserLastSpeakTimesReq.Marshal(b, m, deterministic)
}
func (dst *UpdateUserLastSpeakTimesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserLastSpeakTimesReq.Merge(dst, src)
}
func (m *UpdateUserLastSpeakTimesReq) XXX_Size() int {
	return xxx_messageInfo_UpdateUserLastSpeakTimesReq.Size(m)
}
func (m *UpdateUserLastSpeakTimesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserLastSpeakTimesReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserLastSpeakTimesReq proto.InternalMessageInfo

func (m *UpdateUserLastSpeakTimesReq) GetOperation() UpdateUserLastSpeakTimesReq_Operation {
	if m != nil {
		return m.Operation
	}
	return UpdateUserLastSpeakTimesReq_UPDATE
}

func (m *UpdateUserLastSpeakTimesReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUserLastSpeakTimesReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UpdateUserLastSpeakTimesReq) GetTimeStamp() int64 {
	if m != nil {
		return m.TimeStamp
	}
	return 0
}

type UpdateUserLastSpeakTimesResp struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUserLastSpeakTimesResp) Reset()         { *m = UpdateUserLastSpeakTimesResp{} }
func (m *UpdateUserLastSpeakTimesResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUserLastSpeakTimesResp) ProtoMessage()    {}
func (*UpdateUserLastSpeakTimesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{15}
}
func (m *UpdateUserLastSpeakTimesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserLastSpeakTimesResp.Unmarshal(m, b)
}
func (m *UpdateUserLastSpeakTimesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserLastSpeakTimesResp.Marshal(b, m, deterministic)
}
func (dst *UpdateUserLastSpeakTimesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserLastSpeakTimesResp.Merge(dst, src)
}
func (m *UpdateUserLastSpeakTimesResp) XXX_Size() int {
	return xxx_messageInfo_UpdateUserLastSpeakTimesResp.Size(m)
}
func (m *UpdateUserLastSpeakTimesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserLastSpeakTimesResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserLastSpeakTimesResp proto.InternalMessageInfo

func (m *UpdateUserLastSpeakTimesResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UpdateUserLastSpeakTimesResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type PodInfo struct {
	PodName              string   `protobuf:"bytes,1,opt,name=pod_name,json=podName,proto3" json:"pod_name,omitempty"`
	PodIp                string   `protobuf:"bytes,2,opt,name=pod_ip,json=podIp,proto3" json:"pod_ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PodInfo) Reset()         { *m = PodInfo{} }
func (m *PodInfo) String() string { return proto.CompactTextString(m) }
func (*PodInfo) ProtoMessage()    {}
func (*PodInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{16}
}
func (m *PodInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PodInfo.Unmarshal(m, b)
}
func (m *PodInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PodInfo.Marshal(b, m, deterministic)
}
func (dst *PodInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PodInfo.Merge(dst, src)
}
func (m *PodInfo) XXX_Size() int {
	return xxx_messageInfo_PodInfo.Size(m)
}
func (m *PodInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PodInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PodInfo proto.InternalMessageInfo

func (m *PodInfo) GetPodName() string {
	if m != nil {
		return m.PodName
	}
	return ""
}

func (m *PodInfo) GetPodIp() string {
	if m != nil {
		return m.PodIp
	}
	return ""
}

type BatchGetPodInfoWithChannelReq struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetPodInfoWithChannelReq) Reset()         { *m = BatchGetPodInfoWithChannelReq{} }
func (m *BatchGetPodInfoWithChannelReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetPodInfoWithChannelReq) ProtoMessage()    {}
func (*BatchGetPodInfoWithChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{17}
}
func (m *BatchGetPodInfoWithChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPodInfoWithChannelReq.Unmarshal(m, b)
}
func (m *BatchGetPodInfoWithChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPodInfoWithChannelReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetPodInfoWithChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPodInfoWithChannelReq.Merge(dst, src)
}
func (m *BatchGetPodInfoWithChannelReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetPodInfoWithChannelReq.Size(m)
}
func (m *BatchGetPodInfoWithChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPodInfoWithChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPodInfoWithChannelReq proto.InternalMessageInfo

func (m *BatchGetPodInfoWithChannelReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type BatchGetPodInfoWithChannelResp struct {
	ChannelId_2PodInfo   map[uint32]*PodInfo `protobuf:"bytes,1,rep,name=channel_id_2_pod_info,json=channelId2PodInfo,proto3" json:"channel_id_2_pod_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchGetPodInfoWithChannelResp) Reset()         { *m = BatchGetPodInfoWithChannelResp{} }
func (m *BatchGetPodInfoWithChannelResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetPodInfoWithChannelResp) ProtoMessage()    {}
func (*BatchGetPodInfoWithChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{18}
}
func (m *BatchGetPodInfoWithChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPodInfoWithChannelResp.Unmarshal(m, b)
}
func (m *BatchGetPodInfoWithChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPodInfoWithChannelResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetPodInfoWithChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPodInfoWithChannelResp.Merge(dst, src)
}
func (m *BatchGetPodInfoWithChannelResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetPodInfoWithChannelResp.Size(m)
}
func (m *BatchGetPodInfoWithChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPodInfoWithChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPodInfoWithChannelResp proto.InternalMessageInfo

func (m *BatchGetPodInfoWithChannelResp) GetChannelId_2PodInfo() map[uint32]*PodInfo {
	if m != nil {
		return m.ChannelId_2PodInfo
	}
	return nil
}

type UpdatePodInfoReq struct {
	Operation            UpdatePodInfoReq_Operation `protobuf:"varint,1,opt,name=operation,proto3,enum=aigc.common.dao.aigc_data_service.UpdatePodInfoReq_Operation" json:"operation,omitempty"`
	ChannelId            uint32                     `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PodInfo              *PodInfo                   `protobuf:"bytes,3,opt,name=pod_info,json=podInfo,proto3" json:"pod_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *UpdatePodInfoReq) Reset()         { *m = UpdatePodInfoReq{} }
func (m *UpdatePodInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePodInfoReq) ProtoMessage()    {}
func (*UpdatePodInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{19}
}
func (m *UpdatePodInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePodInfoReq.Unmarshal(m, b)
}
func (m *UpdatePodInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePodInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePodInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePodInfoReq.Merge(dst, src)
}
func (m *UpdatePodInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePodInfoReq.Size(m)
}
func (m *UpdatePodInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePodInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePodInfoReq proto.InternalMessageInfo

func (m *UpdatePodInfoReq) GetOperation() UpdatePodInfoReq_Operation {
	if m != nil {
		return m.Operation
	}
	return UpdatePodInfoReq_UPDATE
}

func (m *UpdatePodInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UpdatePodInfoReq) GetPodInfo() *PodInfo {
	if m != nil {
		return m.PodInfo
	}
	return nil
}

type UpdatePodInfoResp struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePodInfoResp) Reset()         { *m = UpdatePodInfoResp{} }
func (m *UpdatePodInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePodInfoResp) ProtoMessage()    {}
func (*UpdatePodInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_data_service_94c4f407058901a9, []int{20}
}
func (m *UpdatePodInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePodInfoResp.Unmarshal(m, b)
}
func (m *UpdatePodInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePodInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePodInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePodInfoResp.Merge(dst, src)
}
func (m *UpdatePodInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePodInfoResp.Size(m)
}
func (m *UpdatePodInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePodInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePodInfoResp proto.InternalMessageInfo

func (m *UpdatePodInfoResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UpdatePodInfoResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func init() {
	proto.RegisterType((*ChatHistory)(nil), "aigc.common.dao.aigc_data_service.ChatHistory")
	proto.RegisterMapType((map[string]string)(nil), "aigc.common.dao.aigc_data_service.ChatHistory.ExtraEntry")
	proto.RegisterType((*GetGroupChatHistoryReq)(nil), "aigc.common.dao.aigc_data_service.GetGroupChatHistoryReq")
	proto.RegisterType((*GetGroupChatHistoryResp)(nil), "aigc.common.dao.aigc_data_service.GetGroupChatHistoryResp")
	proto.RegisterType((*UpdateGroupChatHistoryReq)(nil), "aigc.common.dao.aigc_data_service.UpdateGroupChatHistoryReq")
	proto.RegisterType((*UpdateGroupChatHistoryResp)(nil), "aigc.common.dao.aigc_data_service.UpdateGroupChatHistoryResp")
	proto.RegisterType((*RemoveGroupChatHistoryReq)(nil), "aigc.common.dao.aigc_data_service.RemoveGroupChatHistoryReq")
	proto.RegisterType((*RemoveGroupChatHistoryResp)(nil), "aigc.common.dao.aigc_data_service.RemoveGroupChatHistoryResp")
	proto.RegisterType((*AIUids)(nil), "aigc.common.dao.aigc_data_service.AIUids")
	proto.RegisterType((*BatchGetChannelStateReq)(nil), "aigc.common.dao.aigc_data_service.BatchGetChannelStateReq")
	proto.RegisterType((*AIStates)(nil), "aigc.common.dao.aigc_data_service.AIStates")
	proto.RegisterMapType((map[uint32]AIState)(nil), "aigc.common.dao.aigc_data_service.AIStates.Aiuid2StateEntry")
	proto.RegisterType((*UserLastSpeakTimes)(nil), "aigc.common.dao.aigc_data_service.UserLastSpeakTimes")
	proto.RegisterMapType((map[uint32]int64)(nil), "aigc.common.dao.aigc_data_service.UserLastSpeakTimes.Uid2TsEntry")
	proto.RegisterType((*BatchGetChannelStateResp)(nil), "aigc.common.dao.aigc_data_service.BatchGetChannelStateResp")
	proto.RegisterMapType((map[uint32]*AIStates)(nil), "aigc.common.dao.aigc_data_service.BatchGetChannelStateResp.Channel2AiStatesEntry")
	proto.RegisterMapType((map[uint32]*UserLastSpeakTimes)(nil), "aigc.common.dao.aigc_data_service.BatchGetChannelStateResp.Channel2UserTimesEntry")
	proto.RegisterType((*UpdateAIStateReq)(nil), "aigc.common.dao.aigc_data_service.UpdateAIStateReq")
	proto.RegisterType((*UpdateAIStateResp)(nil), "aigc.common.dao.aigc_data_service.UpdateAIStateResp")
	proto.RegisterType((*UpdateUserLastSpeakTimesReq)(nil), "aigc.common.dao.aigc_data_service.UpdateUserLastSpeakTimesReq")
	proto.RegisterType((*UpdateUserLastSpeakTimesResp)(nil), "aigc.common.dao.aigc_data_service.UpdateUserLastSpeakTimesResp")
	proto.RegisterType((*PodInfo)(nil), "aigc.common.dao.aigc_data_service.PodInfo")
	proto.RegisterType((*BatchGetPodInfoWithChannelReq)(nil), "aigc.common.dao.aigc_data_service.BatchGetPodInfoWithChannelReq")
	proto.RegisterType((*BatchGetPodInfoWithChannelResp)(nil), "aigc.common.dao.aigc_data_service.BatchGetPodInfoWithChannelResp")
	proto.RegisterMapType((map[uint32]*PodInfo)(nil), "aigc.common.dao.aigc_data_service.BatchGetPodInfoWithChannelResp.ChannelId2PodInfoEntry")
	proto.RegisterType((*UpdatePodInfoReq)(nil), "aigc.common.dao.aigc_data_service.UpdatePodInfoReq")
	proto.RegisterType((*UpdatePodInfoResp)(nil), "aigc.common.dao.aigc_data_service.UpdatePodInfoResp")
	proto.RegisterEnum("aigc.common.dao.aigc_data_service.ChatScene", ChatScene_name, ChatScene_value)
	proto.RegisterEnum("aigc.common.dao.aigc_data_service.Source", Source_name, Source_value)
	proto.RegisterEnum("aigc.common.dao.aigc_data_service.QueryType", QueryType_name, QueryType_value)
	proto.RegisterEnum("aigc.common.dao.aigc_data_service.AIState", AIState_name, AIState_value)
	proto.RegisterEnum("aigc.common.dao.aigc_data_service.ChatHistory_Role", ChatHistory_Role_name, ChatHistory_Role_value)
	proto.RegisterEnum("aigc.common.dao.aigc_data_service.UpdateAIStateReq_Operation", UpdateAIStateReq_Operation_name, UpdateAIStateReq_Operation_value)
	proto.RegisterEnum("aigc.common.dao.aigc_data_service.UpdateUserLastSpeakTimesReq_Operation", UpdateUserLastSpeakTimesReq_Operation_name, UpdateUserLastSpeakTimesReq_Operation_value)
	proto.RegisterEnum("aigc.common.dao.aigc_data_service.UpdatePodInfoReq_Operation", UpdatePodInfoReq_Operation_name, UpdatePodInfoReq_Operation_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AIGCDataServiceClient is the client API for AIGCDataService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AIGCDataServiceClient interface {
	// 获取聊天历史记录
	GetGroupChatHistory(ctx context.Context, in *GetGroupChatHistoryReq, opts ...grpc.CallOption) (*GetGroupChatHistoryResp, error)
	// 创建/更新 聊天历史记录
	UpdateGroupChatHistory(ctx context.Context, in *UpdateGroupChatHistoryReq, opts ...grpc.CallOption) (*UpdateGroupChatHistoryResp, error)
	// 根据句子id移除聊天中的某几句历史记录
	RemoveGroupChatHistory(ctx context.Context, in *RemoveGroupChatHistoryReq, opts ...grpc.CallOption) (*RemoveGroupChatHistoryResp, error)
	// 批量获取频道内AI的状态
	BatchGetChannelState(ctx context.Context, in *BatchGetChannelStateReq, opts ...grpc.CallOption) (*BatchGetChannelStateResp, error)
	// 批量获取AI房间的Pod信息
	BatchGetPodInfoWithChannel(ctx context.Context, in *BatchGetPodInfoWithChannelReq, opts ...grpc.CallOption) (*BatchGetPodInfoWithChannelResp, error)
	// 更新用户最后说话时间
	UpdateUserLastSpeakTimes(ctx context.Context, in *UpdateUserLastSpeakTimesReq, opts ...grpc.CallOption) (*UpdateUserLastSpeakTimesResp, error)
	// 更新AI状态
	UpdateAIState(ctx context.Context, in *UpdateAIStateReq, opts ...grpc.CallOption) (*UpdateAIStateResp, error)
	// 更新pod信息
	UpdatePodInfo(ctx context.Context, in *UpdatePodInfoReq, opts ...grpc.CallOption) (*UpdatePodInfoResp, error)
}

type aIGCDataServiceClient struct {
	cc *grpc.ClientConn
}

func NewAIGCDataServiceClient(cc *grpc.ClientConn) AIGCDataServiceClient {
	return &aIGCDataServiceClient{cc}
}

func (c *aIGCDataServiceClient) GetGroupChatHistory(ctx context.Context, in *GetGroupChatHistoryReq, opts ...grpc.CallOption) (*GetGroupChatHistoryResp, error) {
	out := new(GetGroupChatHistoryResp)
	err := c.cc.Invoke(ctx, "/aigc.common.dao.aigc_data_service.AIGCDataService/GetGroupChatHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIGCDataServiceClient) UpdateGroupChatHistory(ctx context.Context, in *UpdateGroupChatHistoryReq, opts ...grpc.CallOption) (*UpdateGroupChatHistoryResp, error) {
	out := new(UpdateGroupChatHistoryResp)
	err := c.cc.Invoke(ctx, "/aigc.common.dao.aigc_data_service.AIGCDataService/UpdateGroupChatHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIGCDataServiceClient) RemoveGroupChatHistory(ctx context.Context, in *RemoveGroupChatHistoryReq, opts ...grpc.CallOption) (*RemoveGroupChatHistoryResp, error) {
	out := new(RemoveGroupChatHistoryResp)
	err := c.cc.Invoke(ctx, "/aigc.common.dao.aigc_data_service.AIGCDataService/RemoveGroupChatHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIGCDataServiceClient) BatchGetChannelState(ctx context.Context, in *BatchGetChannelStateReq, opts ...grpc.CallOption) (*BatchGetChannelStateResp, error) {
	out := new(BatchGetChannelStateResp)
	err := c.cc.Invoke(ctx, "/aigc.common.dao.aigc_data_service.AIGCDataService/BatchGetChannelState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIGCDataServiceClient) BatchGetPodInfoWithChannel(ctx context.Context, in *BatchGetPodInfoWithChannelReq, opts ...grpc.CallOption) (*BatchGetPodInfoWithChannelResp, error) {
	out := new(BatchGetPodInfoWithChannelResp)
	err := c.cc.Invoke(ctx, "/aigc.common.dao.aigc_data_service.AIGCDataService/BatchGetPodInfoWithChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIGCDataServiceClient) UpdateUserLastSpeakTimes(ctx context.Context, in *UpdateUserLastSpeakTimesReq, opts ...grpc.CallOption) (*UpdateUserLastSpeakTimesResp, error) {
	out := new(UpdateUserLastSpeakTimesResp)
	err := c.cc.Invoke(ctx, "/aigc.common.dao.aigc_data_service.AIGCDataService/UpdateUserLastSpeakTimes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIGCDataServiceClient) UpdateAIState(ctx context.Context, in *UpdateAIStateReq, opts ...grpc.CallOption) (*UpdateAIStateResp, error) {
	out := new(UpdateAIStateResp)
	err := c.cc.Invoke(ctx, "/aigc.common.dao.aigc_data_service.AIGCDataService/UpdateAIState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIGCDataServiceClient) UpdatePodInfo(ctx context.Context, in *UpdatePodInfoReq, opts ...grpc.CallOption) (*UpdatePodInfoResp, error) {
	out := new(UpdatePodInfoResp)
	err := c.cc.Invoke(ctx, "/aigc.common.dao.aigc_data_service.AIGCDataService/UpdatePodInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AIGCDataServiceServer is the server API for AIGCDataService service.
type AIGCDataServiceServer interface {
	// 获取聊天历史记录
	GetGroupChatHistory(context.Context, *GetGroupChatHistoryReq) (*GetGroupChatHistoryResp, error)
	// 创建/更新 聊天历史记录
	UpdateGroupChatHistory(context.Context, *UpdateGroupChatHistoryReq) (*UpdateGroupChatHistoryResp, error)
	// 根据句子id移除聊天中的某几句历史记录
	RemoveGroupChatHistory(context.Context, *RemoveGroupChatHistoryReq) (*RemoveGroupChatHistoryResp, error)
	// 批量获取频道内AI的状态
	BatchGetChannelState(context.Context, *BatchGetChannelStateReq) (*BatchGetChannelStateResp, error)
	// 批量获取AI房间的Pod信息
	BatchGetPodInfoWithChannel(context.Context, *BatchGetPodInfoWithChannelReq) (*BatchGetPodInfoWithChannelResp, error)
	// 更新用户最后说话时间
	UpdateUserLastSpeakTimes(context.Context, *UpdateUserLastSpeakTimesReq) (*UpdateUserLastSpeakTimesResp, error)
	// 更新AI状态
	UpdateAIState(context.Context, *UpdateAIStateReq) (*UpdateAIStateResp, error)
	// 更新pod信息
	UpdatePodInfo(context.Context, *UpdatePodInfoReq) (*UpdatePodInfoResp, error)
}

func RegisterAIGCDataServiceServer(s *grpc.Server, srv AIGCDataServiceServer) {
	s.RegisterService(&_AIGCDataService_serviceDesc, srv)
}

func _AIGCDataService_GetGroupChatHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupChatHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIGCDataServiceServer).GetGroupChatHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc.common.dao.aigc_data_service.AIGCDataService/GetGroupChatHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIGCDataServiceServer).GetGroupChatHistory(ctx, req.(*GetGroupChatHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIGCDataService_UpdateGroupChatHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroupChatHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIGCDataServiceServer).UpdateGroupChatHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc.common.dao.aigc_data_service.AIGCDataService/UpdateGroupChatHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIGCDataServiceServer).UpdateGroupChatHistory(ctx, req.(*UpdateGroupChatHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIGCDataService_RemoveGroupChatHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveGroupChatHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIGCDataServiceServer).RemoveGroupChatHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc.common.dao.aigc_data_service.AIGCDataService/RemoveGroupChatHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIGCDataServiceServer).RemoveGroupChatHistory(ctx, req.(*RemoveGroupChatHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIGCDataService_BatchGetChannelState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIGCDataServiceServer).BatchGetChannelState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc.common.dao.aigc_data_service.AIGCDataService/BatchGetChannelState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIGCDataServiceServer).BatchGetChannelState(ctx, req.(*BatchGetChannelStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIGCDataService_BatchGetPodInfoWithChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetPodInfoWithChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIGCDataServiceServer).BatchGetPodInfoWithChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc.common.dao.aigc_data_service.AIGCDataService/BatchGetPodInfoWithChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIGCDataServiceServer).BatchGetPodInfoWithChannel(ctx, req.(*BatchGetPodInfoWithChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIGCDataService_UpdateUserLastSpeakTimes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserLastSpeakTimesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIGCDataServiceServer).UpdateUserLastSpeakTimes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc.common.dao.aigc_data_service.AIGCDataService/UpdateUserLastSpeakTimes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIGCDataServiceServer).UpdateUserLastSpeakTimes(ctx, req.(*UpdateUserLastSpeakTimesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIGCDataService_UpdateAIState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAIStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIGCDataServiceServer).UpdateAIState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc.common.dao.aigc_data_service.AIGCDataService/UpdateAIState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIGCDataServiceServer).UpdateAIState(ctx, req.(*UpdateAIStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIGCDataService_UpdatePodInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePodInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIGCDataServiceServer).UpdatePodInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc.common.dao.aigc_data_service.AIGCDataService/UpdatePodInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIGCDataServiceServer).UpdatePodInfo(ctx, req.(*UpdatePodInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AIGCDataService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "aigc.common.dao.aigc_data_service.AIGCDataService",
	HandlerType: (*AIGCDataServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGroupChatHistory",
			Handler:    _AIGCDataService_GetGroupChatHistory_Handler,
		},
		{
			MethodName: "UpdateGroupChatHistory",
			Handler:    _AIGCDataService_UpdateGroupChatHistory_Handler,
		},
		{
			MethodName: "RemoveGroupChatHistory",
			Handler:    _AIGCDataService_RemoveGroupChatHistory_Handler,
		},
		{
			MethodName: "BatchGetChannelState",
			Handler:    _AIGCDataService_BatchGetChannelState_Handler,
		},
		{
			MethodName: "BatchGetPodInfoWithChannel",
			Handler:    _AIGCDataService_BatchGetPodInfoWithChannel_Handler,
		},
		{
			MethodName: "UpdateUserLastSpeakTimes",
			Handler:    _AIGCDataService_UpdateUserLastSpeakTimes_Handler,
		},
		{
			MethodName: "UpdateAIState",
			Handler:    _AIGCDataService_UpdateAIState_Handler,
		},
		{
			MethodName: "UpdatePodInfo",
			Handler:    _AIGCDataService_UpdatePodInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aigc-apps/common/dao/aigc-data-service.proto",
}

func init() {
	proto.RegisterFile("aigc-apps/common/dao/aigc-data-service.proto", fileDescriptor_aigc_data_service_94c4f407058901a9)
}

var fileDescriptor_aigc_data_service_94c4f407058901a9 = []byte{
	// 1520 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x58, 0x4f, 0x6f, 0xdb, 0x46,
	0x16, 0x0f, 0x25, 0x5b, 0xb6, 0x9e, 0x6c, 0x2f, 0x33, 0x71, 0x1c, 0x45, 0xd9, 0x6c, 0x1c, 0x2d,
	0xb0, 0xf0, 0x7a, 0x13, 0x19, 0x4b, 0x6f, 0x80, 0xfc, 0xdd, 0x35, 0x6d, 0x33, 0x0e, 0x61, 0x59,
	0xb2, 0x49, 0x29, 0xbb, 0xd9, 0x1c, 0xd8, 0x29, 0x39, 0xb1, 0xd9, 0x58, 0x24, 0xcd, 0xa1, 0x82,
	0xb8, 0xbd, 0xb4, 0xe8, 0xa9, 0x45, 0x81, 0xa2, 0x01, 0x7a, 0x6a, 0x80, 0xa2, 0xf7, 0x7e, 0x94,
	0xf6, 0xde, 0xcf, 0x92, 0x53, 0x31, 0x43, 0x52, 0xa4, 0x15, 0x3a, 0xa1, 0xac, 0xa2, 0xbd, 0xcd,
	0xbc, 0xe1, 0xfb, 0xf7, 0x9b, 0x37, 0xbf, 0x79, 0x43, 0xb8, 0x81, 0xed, 0x7d, 0xf3, 0x26, 0xf6,
	0x3c, 0xba, 0x62, 0xba, 0xbd, 0x9e, 0xeb, 0xac, 0x58, 0xd8, 0x5d, 0xe1, 0x42, 0x0b, 0x07, 0xf8,
	0x26, 0x25, 0xfe, 0x0b, 0xdb, 0x24, 0x0d, 0xcf, 0x77, 0x03, 0x17, 0x5d, 0x67, 0x0b, 0x8d, 0xf0,
	0xc3, 0x86, 0x85, 0xdd, 0x06, 0x9b, 0x1b, 0xec, 0x43, 0x23, 0xfa, 0xb0, 0xfe, 0x73, 0x11, 0x2a,
	0x1b, 0x07, 0x38, 0x78, 0x64, 0xd3, 0xc0, 0xf5, 0x8f, 0xd1, 0x35, 0xa8, 0x50, 0xe2, 0x04, 0xc4,
	0x31, 0x89, 0x61, 0x5b, 0x55, 0x61, 0x51, 0x58, 0x2a, 0x6b, 0x10, 0x8b, 0x54, 0x0b, 0x6d, 0xc1,
	0x84, 0xef, 0x1e, 0x92, 0x6a, 0x61, 0x51, 0x58, 0x9a, 0x93, 0x56, 0x1b, 0xef, 0x75, 0xd1, 0x48,
	0x99, 0x6f, 0x68, 0xee, 0x21, 0xd1, 0xb8, 0x01, 0x24, 0x42, 0xb1, 0x6f, 0x5b, 0xd5, 0xe2, 0xa2,
	0xb0, 0x34, 0xab, 0xb1, 0x21, 0xda, 0x06, 0x30, 0x0f, 0x70, 0x60, 0x50, 0x93, 0x38, 0xa4, 0x3a,
	0xc1, 0x1d, 0xdc, 0xc8, 0xe9, 0x40, 0x67, 0x3a, 0x5a, 0xd9, 0x8c, 0x87, 0xa8, 0x0a, 0x53, 0xa6,
	0xcb, 0xa2, 0x0e, 0xaa, 0x93, 0x3c, 0x89, 0x78, 0x8a, 0xae, 0x02, 0x50, 0xd7, 0x0f, 0x8c, 0x17,
	0xf8, 0xb0, 0x4f, 0xaa, 0xa5, 0x45, 0x61, 0xa9, 0xa8, 0x95, 0x99, 0xe4, 0x31, 0x13, 0xa0, 0x36,
	0x4c, 0x92, 0x97, 0x81, 0x8f, 0xab, 0x53, 0x8b, 0xc5, 0xa5, 0x8a, 0x74, 0x67, 0xc4, 0x0c, 0x15,
	0xa6, 0xab, 0x38, 0x81, 0x7f, 0xac, 0x85, 0x76, 0x6a, 0xb7, 0x01, 0x12, 0x21, 0x4b, 0xfb, 0x39,
	0x39, 0x8e, 0x80, 0x65, 0x43, 0x34, 0x0f, 0x93, 0x61, 0x28, 0x05, 0x2e, 0x0b, 0x27, 0x77, 0x0b,
	0xb7, 0x85, 0xfa, 0x3f, 0x61, 0x42, 0x0b, 0xa1, 0x9a, 0xd1, 0xda, 0x4d, 0xc5, 0xd8, 0x54, 0x1e,
	0xca, 0xdd, 0x66, 0x47, 0x3c, 0x87, 0xa6, 0x61, 0xa2, 0xab, 0x2b, 0x9a, 0x28, 0xa0, 0x59, 0x28,
	0xcb, 0xba, 0xae, 0xea, 0x1d, 0xb9, 0xd5, 0x11, 0x0b, 0xf5, 0xcf, 0x0a, 0xb0, 0xb0, 0x45, 0x82,
	0x2d, 0xdf, 0xed, 0x7b, 0xa9, 0xb0, 0x34, 0x72, 0x84, 0x2e, 0xc3, 0xf4, 0x3e, 0x13, 0x27, 0xfb,
	0x3a, 0xc5, 0xe7, 0xaa, 0x85, 0x64, 0x28, 0x51, 0xb7, 0xef, 0x9b, 0xf1, 0xb6, 0xfe, 0x3d, 0x47,
	0xd2, 0x3a, 0x57, 0xd0, 0x22, 0xc5, 0xa1, 0xcd, 0x2b, 0x8e, 0xb7, 0x79, 0xac, 0x0a, 0x03, 0xec,
	0x07, 0x86, 0xed, 0x58, 0xe4, 0x25, 0x2f, 0x85, 0x49, 0x0d, 0xb8, 0x48, 0x65, 0x12, 0x74, 0x05,
	0xca, 0xc4, 0xb1, 0xa2, 0xe5, 0x49, 0xbe, 0x3c, 0x4d, 0x1c, 0x8b, 0x2f, 0xd6, 0x5f, 0x09, 0x70,
	0x29, 0x13, 0x03, 0xea, 0xa1, 0x3d, 0x98, 0xe1, 0x61, 0x1e, 0x84, 0xb2, 0xaa, 0xc0, 0x37, 0xb9,
	0x31, 0xda, 0x26, 0x6b, 0x15, 0x33, 0x75, 0x64, 0x10, 0x4c, 0x98, 0xae, 0x15, 0x42, 0x37, 0xa9,
	0xf1, 0x31, 0xdb, 0xe5, 0x1e, 0xdd, 0xe7, 0x30, 0x94, 0x35, 0x36, 0xac, 0xff, 0x24, 0xc0, 0xe5,
	0xae, 0x67, 0xe1, 0x80, 0xfc, 0xfe, 0x7b, 0x33, 0x9c, 0x74, 0x71, 0xec, 0xa4, 0xeb, 0xeb, 0x50,
	0x3b, 0x2d, 0x1b, 0xea, 0x0d, 0x20, 0x11, 0xde, 0x86, 0xa4, 0x90, 0x40, 0xf2, 0x5a, 0x80, 0xcb,
	0x1a, 0xe9, 0xb9, 0x2f, 0xfe, 0x00, 0x48, 0xae, 0xc3, 0x4c, 0x8a, 0xe7, 0x28, 0x87, 0xa4, 0xac,
	0x55, 0x12, 0xa2, 0xa3, 0x2c, 0xc5, 0xd3, 0xa2, 0xcb, 0x9d, 0x62, 0x0d, 0x4a, 0xb2, 0xda, 0xb5,
	0x2d, 0xca, 0xd6, 0x98, 0x1f, 0x56, 0x6f, 0xb3, 0x1a, 0x1b, 0xd6, 0xbf, 0x10, 0xe0, 0xd2, 0x3a,
	0x0e, 0xcc, 0x83, 0x2d, 0x12, 0x6c, 0x1c, 0x60, 0xc7, 0x21, 0x87, 0x7a, 0x80, 0x03, 0xc2, 0x92,
	0xbf, 0x06, 0x0c, 0x6d, 0x26, 0x32, 0x12, 0x2d, 0x88, 0x44, 0xaa, 0x45, 0xd1, 0x0e, 0x54, 0x8e,
	0xfa, 0xc4, 0x3f, 0x36, 0x82, 0x63, 0x8f, 0xd0, 0x6a, 0x61, 0xb1, 0x98, 0xf3, 0xbc, 0xed, 0x31,
	0xad, 0xce, 0xb1, 0x47, 0x34, 0x38, 0x8a, 0x87, 0xb4, 0xfe, 0x8b, 0x00, 0xd3, 0xb2, 0xca, 0xdd,
	0x53, 0xf4, 0x01, 0xcc, 0x62, 0xbb, 0x6f, 0x5b, 0x86, 0x64, 0x50, 0x26, 0x89, 0x0e, 0xc9, 0xfd,
	0x1c, 0xd6, 0x63, 0x1b, 0x0d, 0x99, 0x19, 0x90, 0xf8, 0x24, 0x24, 0xc3, 0x0a, 0x4e, 0x24, 0xb5,
	0x8f, 0x40, 0x1c, 0xfe, 0x20, 0x4d, 0x8c, 0xb3, 0x21, 0x31, 0xae, 0xa5, 0x89, 0x71, 0x4e, 0x5a,
	0xce, 0xef, 0x3f, 0x4d, 0xa2, 0x3f, 0x0a, 0x80, 0xba, 0x94, 0xf8, 0x4d, 0x4c, 0x03, 0xdd, 0x23,
	0xf8, 0x79, 0xc7, 0xee, 0x11, 0x8a, 0x9e, 0xc2, 0x74, 0x98, 0x62, 0x40, 0xa3, 0xfc, 0xe4, 0x1c,
	0xf6, 0xdf, 0x36, 0xd4, 0xe8, 0xda, 0x96, 0xd4, 0xa1, 0x61, 0x92, 0xa5, 0x3e, 0x9f, 0xd4, 0xee,
	0x40, 0x25, 0x25, 0xce, 0x48, 0xed, 0x04, 0xe7, 0x17, 0xd3, 0xe1, 0xbe, 0x9a, 0x80, 0x6a, 0x76,
	0x55, 0x50, 0x0f, 0x7d, 0x2a, 0xc0, 0x85, 0xb8, 0x2e, 0x24, 0x03, 0xdb, 0xe1, 0xfe, 0xc4, 0x09,
	0xec, 0xe5, 0x48, 0xe0, 0x34, 0xd3, 0x8d, 0x48, 0x20, 0xc9, 0x76, 0xb8, 0x83, 0x61, 0x42, 0xa2,
	0x39, 0x24, 0x46, 0x9f, 0x0b, 0x30, 0x9f, 0x84, 0xd0, 0xa7, 0xc4, 0x37, 0x02, 0x86, 0x03, 0x2f,
	0xc1, 0x8a, 0xa4, 0xfd, 0x16, 0x31, 0x30, 0x94, 0x39, 0xb8, 0x61, 0x10, 0xe7, 0xcd, 0x61, 0x79,
	0xcd, 0x83, 0x8b, 0x99, 0x01, 0x67, 0x40, 0x2d, 0xa7, 0xa1, 0xae, 0x48, 0xff, 0x18, 0xa1, 0x8a,
	0x53, 0xfb, 0x52, 0xfb, 0x04, 0x16, 0xb2, 0xc3, 0xcb, 0x70, 0xb9, 0x7d, 0xd2, 0xe5, 0xad, 0x33,
	0x15, 0x56, 0xba, 0x28, 0xbe, 0x29, 0x80, 0x18, 0xd2, 0x6d, 0x5c, 0xe0, 0xe4, 0x08, 0x3d, 0x85,
	0xb2, 0xeb, 0x11, 0x1f, 0x07, 0xb6, 0xeb, 0x70, 0xef, 0x73, 0xd2, 0x83, 0x3c, 0x9e, 0x86, 0xec,
	0x34, 0xda, 0xb1, 0x11, 0x2d, 0xb1, 0x87, 0x2e, 0x42, 0x09, 0xdb, 0x06, 0x6b, 0xd0, 0x0a, 0x3c,
	0xaf, 0x49, 0x6c, 0x77, 0x6d, 0x8b, 0xf5, 0x4e, 0x09, 0x2f, 0x45, 0xbd, 0x5b, 0x79, 0x40, 0x4b,
	0x48, 0x81, 0xe9, 0xb8, 0x28, 0xa3, 0xfe, 0x6d, 0x94, 0x43, 0x3b, 0x85, 0xc3, 0xad, 0xac, 0xff,
	0x15, 0xca, 0x83, 0xa0, 0x10, 0x40, 0xa9, 0xbb, 0xbb, 0x29, 0x77, 0x14, 0xf1, 0x1c, 0x1b, 0x6f,
	0x2a, 0x4d, 0xa5, 0xa3, 0x88, 0x42, 0xfd, 0x0e, 0x9c, 0x1f, 0x4a, 0x25, 0x37, 0x2b, 0xbf, 0x11,
	0xe0, 0x4a, 0xa8, 0x9b, 0x01, 0x3b, 0x39, 0x42, 0xcf, 0xde, 0x46, 0xf6, 0x51, 0x6e, 0x64, 0x33,
	0x4d, 0x66, 0x83, 0x1c, 0xb5, 0xc0, 0x85, 0xa4, 0x05, 0x7e, 0x0f, 0xbe, 0x57, 0x01, 0xd8, 0x61,
	0x63, 0x08, 0xf7, 0x3c, 0x8e, 0x70, 0x51, 0x2b, 0x33, 0x89, 0xce, 0x04, 0xf9, 0x70, 0xdb, 0x84,
	0x3f, 0x9f, 0x1e, 0x68, 0x6e, 0x08, 0xef, 0xc1, 0xd4, 0xae, 0x6b, 0xa9, 0xce, 0x33, 0x97, 0x5d,
	0xd4, 0x9e, 0x6b, 0x19, 0x0e, 0xee, 0x91, 0xf8, 0xa2, 0xf6, 0x5c, 0xab, 0x85, 0x7b, 0x84, 0x55,
	0x11, 0x5b, 0xb2, 0xbd, 0xb8, 0xb7, 0xf5, 0x5c, 0x4b, 0xf5, 0xea, 0x6b, 0x70, 0x35, 0xe6, 0x80,
	0xc8, 0xc8, 0x7f, 0xed, 0xe0, 0x20, 0x3a, 0x5e, 0x79, 0xae, 0xbf, 0xfa, 0xf7, 0x05, 0xf8, 0xcb,
	0xbb, 0x4c, 0x50, 0x0f, 0x7d, 0x29, 0xc0, 0xc5, 0xc4, 0x88, 0x21, 0x19, 0x3c, 0x12, 0xe7, 0x99,
	0x1b, 0xb1, 0xe5, 0xff, 0x46, 0x60, 0xaa, 0x6c, 0x17, 0x31, 0x5f, 0xa9, 0x96, 0x14, 0x7d, 0x70,
	0x92, 0xaf, 0x12, 0x79, 0xcd, 0x1b, 0xb0, 0xc7, 0xd0, 0xc7, 0xef, 0xbb, 0xf6, 0x2a, 0xb9, 0x4e,
	0x50, 0x64, 0x31, 0x4d, 0x19, 0x6f, 0x84, 0x98, 0x32, 0xe2, 0xc5, 0x71, 0x29, 0x23, 0xb1, 0x93,
	0x5d, 0xcd, 0x27, 0x6b, 0xb7, 0x90, 0xc1, 0x0d, 0x83, 0x1d, 0x28, 0x8e, 0x9c, 0x19, 0x2b, 0x29,
	0x36, 0x18, 0x91, 0x1b, 0x06, 0x31, 0xe7, 0x2d, 0xec, 0xe5, 0x35, 0x28, 0x0f, 0x9e, 0x24, 0xa8,
	0x02, 0x53, 0xc9, 0x9b, 0x6b, 0x0e, 0xa0, 0xdd, 0x32, 0x76, 0xd4, 0x0d, 0x43, 0xd6, 0xd9, 0xcb,
	0x0b, 0xa0, 0xa4, 0x6f, 0x68, 0x8a, 0xd2, 0x12, 0x0b, 0x6c, 0xdc, 0x6a, 0x77, 0xd4, 0x87, 0x4f,
	0xc4, 0xe2, 0x72, 0x1d, 0x4a, 0x61, 0xb3, 0xc9, 0xd4, 0xd5, 0xd6, 0x63, 0xb9, 0xa9, 0x6e, 0x8a,
	0xe7, 0xd8, 0x44, 0x56, 0x0d, 0xad, 0xdd, 0xde, 0x11, 0x85, 0xe5, 0x6d, 0x28, 0x0f, 0x1a, 0x31,
	0xb4, 0x00, 0x68, 0xaf, 0xab, 0x68, 0x4f, 0x8c, 0xce, 0x93, 0x5d, 0xc5, 0x48, 0x34, 0x66, 0x58,
	0x4f, 0x66, 0xe8, 0x1d, 0x96, 0x9f, 0x80, 0xaa, 0x30, 0xcf, 0x9e, 0x7c, 0x46, 0x53, 0xd6, 0x3b,
	0x86, 0xbe, 0xab, 0xc8, 0xdb, 0x46, 0x47, 0xdd, 0x51, 0xc4, 0xc2, 0xf2, 0xdf, 0x98, 0x65, 0xce,
	0x81, 0xfc, 0x35, 0xf8, 0x58, 0x56, 0x9b, 0xf2, 0x7a, 0x53, 0x09, 0x2d, 0x3c, 0x92, 0x5b, 0x9b,
	0x4d, 0xb5, 0xb5, 0x25, 0x0a, 0xd2, 0xeb, 0x32, 0xfc, 0x49, 0x56, 0xb7, 0x36, 0x36, 0x71, 0x80,
	0xf5, 0x10, 0x5f, 0xf4, 0x95, 0x00, 0x17, 0x32, 0xde, 0x4a, 0x28, 0xcf, 0xb3, 0x37, 0xfb, 0x9d,
	0x59, 0xbb, 0x7b, 0x56, 0x55, 0xea, 0xa1, 0x6f, 0x05, 0x58, 0xc8, 0x7e, 0x57, 0xa0, 0xfb, 0xb9,
	0x0b, 0x35, 0x2b, 0xa8, 0x07, 0x63, 0x68, 0x47, 0x71, 0x65, 0x3f, 0x06, 0x72, 0xc5, 0x75, 0xea,
	0x2b, 0x27, 0x57, 0x5c, 0xef, 0x78, 0x85, 0x7c, 0x2d, 0xc0, 0x7c, 0x56, 0x3b, 0x85, 0xee, 0x9e,
	0xb9, 0x0f, 0x3b, 0xaa, 0xdd, 0x1b, 0xa3, 0x87, 0x43, 0x3f, 0x08, 0x50, 0x3b, 0x9d, 0x36, 0xd1,
	0xda, 0x98, 0xac, 0x7b, 0x54, 0x93, 0xc7, 0xe6, 0x6d, 0xf4, 0x9d, 0x00, 0xd5, 0xd3, 0xee, 0x40,
	0xf4, 0xef, 0xf1, 0x6e, 0xfa, 0xda, 0x7f, 0xc6, 0xd2, 0xa7, 0x1e, 0xfa, 0x18, 0x66, 0x4f, 0x34,
	0x36, 0x68, 0xf5, 0x0c, 0x5d, 0x5d, 0xed, 0x5f, 0xa3, 0x2b, 0xa5, 0x7d, 0xc7, 0x97, 0xfb, 0xea,
	0x19, 0xae, 0x87, 0x11, 0x7c, 0xa7, 0xf8, 0x79, 0xbd, 0xfd, 0xff, 0x9d, 0x7d, 0xf7, 0x10, 0x3b,
	0xfb, 0x8d, 0x5b, 0x52, 0x10, 0x30, 0xed, 0x15, 0xfe, 0x1b, 0xd3, 0x74, 0x0f, 0x57, 0x22, 0x2d,
	0xba, 0xe2, 0x9b, 0x3d, 0x8b, 0xff, 0xef, 0x34, 0xb2, 0x7e, 0x82, 0x9e, 0x70, 0xf1, 0x61, 0x89,
	0xab, 0xaf, 0xfe, 0x1a, 0x00, 0x00, 0xff, 0xff, 0x0a, 0x6d, 0x61, 0x8e, 0x35, 0x15, 0x00, 0x00,
}
