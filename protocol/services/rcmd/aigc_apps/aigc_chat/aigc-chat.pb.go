// Code generated by protoc-gen-go. DO NOT EDIT.
// source: aigc-apps/aigc-chat/aigc-chat.proto

package aigc_chat // import "golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_chat"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// AI账号聊天消息请求
type AIAccountRecvUserMsgReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AiAccountId          uint32   `protobuf:"varint,2,opt,name=ai_account_id,json=aiAccountId,proto3" json:"ai_account_id,omitempty"`
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	MsgId                uint32   `protobuf:"varint,4,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	MsgType              uint32   `protobuf:"varint,5,opt,name=msg_type,json=msgType,proto3" json:"msg_type,omitempty"`
	Extra                []byte   `protobuf:"bytes,6,opt,name=extra,proto3" json:"extra,omitempty"`
	TargetMsgId          uint32   `protobuf:"varint,7,opt,name=target_msg_id,json=targetMsgId,proto3" json:"target_msg_id,omitempty"`
	MsgSourceType        uint32   `protobuf:"varint,8,opt,name=msg_source_type,json=msgSourceType,proto3" json:"msg_source_type,omitempty"`
	IsExactlyReachLimit  bool     `protobuf:"varint,9,opt,name=is_exactly_reach_limit,json=isExactlyReachLimit,proto3" json:"is_exactly_reach_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIAccountRecvUserMsgReq) Reset()         { *m = AIAccountRecvUserMsgReq{} }
func (m *AIAccountRecvUserMsgReq) String() string { return proto.CompactTextString(m) }
func (*AIAccountRecvUserMsgReq) ProtoMessage()    {}
func (*AIAccountRecvUserMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_chat_b57b4b3c4a1917e9, []int{0}
}
func (m *AIAccountRecvUserMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIAccountRecvUserMsgReq.Unmarshal(m, b)
}
func (m *AIAccountRecvUserMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIAccountRecvUserMsgReq.Marshal(b, m, deterministic)
}
func (dst *AIAccountRecvUserMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIAccountRecvUserMsgReq.Merge(dst, src)
}
func (m *AIAccountRecvUserMsgReq) XXX_Size() int {
	return xxx_messageInfo_AIAccountRecvUserMsgReq.Size(m)
}
func (m *AIAccountRecvUserMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AIAccountRecvUserMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_AIAccountRecvUserMsgReq proto.InternalMessageInfo

func (m *AIAccountRecvUserMsgReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AIAccountRecvUserMsgReq) GetAiAccountId() uint32 {
	if m != nil {
		return m.AiAccountId
	}
	return 0
}

func (m *AIAccountRecvUserMsgReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *AIAccountRecvUserMsgReq) GetMsgId() uint32 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *AIAccountRecvUserMsgReq) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

func (m *AIAccountRecvUserMsgReq) GetExtra() []byte {
	if m != nil {
		return m.Extra
	}
	return nil
}

func (m *AIAccountRecvUserMsgReq) GetTargetMsgId() uint32 {
	if m != nil {
		return m.TargetMsgId
	}
	return 0
}

func (m *AIAccountRecvUserMsgReq) GetMsgSourceType() uint32 {
	if m != nil {
		return m.MsgSourceType
	}
	return 0
}

func (m *AIAccountRecvUserMsgReq) GetIsExactlyReachLimit() bool {
	if m != nil {
		return m.IsExactlyReachLimit
	}
	return false
}

// 聊天消息响应
type AIAccountRecvUserMsgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIAccountRecvUserMsgResp) Reset()         { *m = AIAccountRecvUserMsgResp{} }
func (m *AIAccountRecvUserMsgResp) String() string { return proto.CompactTextString(m) }
func (*AIAccountRecvUserMsgResp) ProtoMessage()    {}
func (*AIAccountRecvUserMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_chat_b57b4b3c4a1917e9, []int{1}
}
func (m *AIAccountRecvUserMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIAccountRecvUserMsgResp.Unmarshal(m, b)
}
func (m *AIAccountRecvUserMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIAccountRecvUserMsgResp.Marshal(b, m, deterministic)
}
func (dst *AIAccountRecvUserMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIAccountRecvUserMsgResp.Merge(dst, src)
}
func (m *AIAccountRecvUserMsgResp) XXX_Size() int {
	return xxx_messageInfo_AIAccountRecvUserMsgResp.Size(m)
}
func (m *AIAccountRecvUserMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AIAccountRecvUserMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_AIAccountRecvUserMsgResp proto.InternalMessageInfo

// AI账号主动触发消息请求
type AIAccountTriggerReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AiAccountId          uint32   `protobuf:"varint,2,opt,name=ai_account_id,json=aiAccountId,proto3" json:"ai_account_id,omitempty"`
	UserContent          string   `protobuf:"bytes,3,opt,name=user_content,json=userContent,proto3" json:"user_content,omitempty"`
	AiContent            string   `protobuf:"bytes,4,opt,name=ai_content,json=aiContent,proto3" json:"ai_content,omitempty"`
	AiContentType        string   `protobuf:"bytes,5,opt,name=ai_content_type,json=aiContentType,proto3" json:"ai_content_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIAccountTriggerReq) Reset()         { *m = AIAccountTriggerReq{} }
func (m *AIAccountTriggerReq) String() string { return proto.CompactTextString(m) }
func (*AIAccountTriggerReq) ProtoMessage()    {}
func (*AIAccountTriggerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_chat_b57b4b3c4a1917e9, []int{2}
}
func (m *AIAccountTriggerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIAccountTriggerReq.Unmarshal(m, b)
}
func (m *AIAccountTriggerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIAccountTriggerReq.Marshal(b, m, deterministic)
}
func (dst *AIAccountTriggerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIAccountTriggerReq.Merge(dst, src)
}
func (m *AIAccountTriggerReq) XXX_Size() int {
	return xxx_messageInfo_AIAccountTriggerReq.Size(m)
}
func (m *AIAccountTriggerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AIAccountTriggerReq.DiscardUnknown(m)
}

var xxx_messageInfo_AIAccountTriggerReq proto.InternalMessageInfo

func (m *AIAccountTriggerReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AIAccountTriggerReq) GetAiAccountId() uint32 {
	if m != nil {
		return m.AiAccountId
	}
	return 0
}

func (m *AIAccountTriggerReq) GetUserContent() string {
	if m != nil {
		return m.UserContent
	}
	return ""
}

func (m *AIAccountTriggerReq) GetAiContent() string {
	if m != nil {
		return m.AiContent
	}
	return ""
}

func (m *AIAccountTriggerReq) GetAiContentType() string {
	if m != nil {
		return m.AiContentType
	}
	return ""
}

// 主动触发消息响应
type AIAccountTriggerResp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIAccountTriggerResp) Reset()         { *m = AIAccountTriggerResp{} }
func (m *AIAccountTriggerResp) String() string { return proto.CompactTextString(m) }
func (*AIAccountTriggerResp) ProtoMessage()    {}
func (*AIAccountTriggerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_chat_b57b4b3c4a1917e9, []int{3}
}
func (m *AIAccountTriggerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIAccountTriggerResp.Unmarshal(m, b)
}
func (m *AIAccountTriggerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIAccountTriggerResp.Marshal(b, m, deterministic)
}
func (dst *AIAccountTriggerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIAccountTriggerResp.Merge(dst, src)
}
func (m *AIAccountTriggerResp) XXX_Size() int {
	return xxx_messageInfo_AIAccountTriggerResp.Size(m)
}
func (m *AIAccountTriggerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AIAccountTriggerResp.DiscardUnknown(m)
}

var xxx_messageInfo_AIAccountTriggerResp proto.InternalMessageInfo

func (m *AIAccountTriggerResp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AIAccountTriggerResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

// 触发 AI 接待
type AIShoutOutUserInRoomReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AiUid                uint32   `protobuf:"varint,2,opt,name=ai_uid,json=aiUid,proto3" json:"ai_uid,omitempty"`
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIShoutOutUserInRoomReq) Reset()         { *m = AIShoutOutUserInRoomReq{} }
func (m *AIShoutOutUserInRoomReq) String() string { return proto.CompactTextString(m) }
func (*AIShoutOutUserInRoomReq) ProtoMessage()    {}
func (*AIShoutOutUserInRoomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_chat_b57b4b3c4a1917e9, []int{4}
}
func (m *AIShoutOutUserInRoomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIShoutOutUserInRoomReq.Unmarshal(m, b)
}
func (m *AIShoutOutUserInRoomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIShoutOutUserInRoomReq.Marshal(b, m, deterministic)
}
func (dst *AIShoutOutUserInRoomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIShoutOutUserInRoomReq.Merge(dst, src)
}
func (m *AIShoutOutUserInRoomReq) XXX_Size() int {
	return xxx_messageInfo_AIShoutOutUserInRoomReq.Size(m)
}
func (m *AIShoutOutUserInRoomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AIShoutOutUserInRoomReq.DiscardUnknown(m)
}

var xxx_messageInfo_AIShoutOutUserInRoomReq proto.InternalMessageInfo

func (m *AIShoutOutUserInRoomReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AIShoutOutUserInRoomReq) GetAiUid() uint32 {
	if m != nil {
		return m.AiUid
	}
	return 0
}

func (m *AIShoutOutUserInRoomReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// 触发 AI 接待响应
type AIShoutOutUserInRoomResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AIShoutOutUserInRoomResp) Reset()         { *m = AIShoutOutUserInRoomResp{} }
func (m *AIShoutOutUserInRoomResp) String() string { return proto.CompactTextString(m) }
func (*AIShoutOutUserInRoomResp) ProtoMessage()    {}
func (*AIShoutOutUserInRoomResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_aigc_chat_b57b4b3c4a1917e9, []int{5}
}
func (m *AIShoutOutUserInRoomResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIShoutOutUserInRoomResp.Unmarshal(m, b)
}
func (m *AIShoutOutUserInRoomResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIShoutOutUserInRoomResp.Marshal(b, m, deterministic)
}
func (dst *AIShoutOutUserInRoomResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIShoutOutUserInRoomResp.Merge(dst, src)
}
func (m *AIShoutOutUserInRoomResp) XXX_Size() int {
	return xxx_messageInfo_AIShoutOutUserInRoomResp.Size(m)
}
func (m *AIShoutOutUserInRoomResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AIShoutOutUserInRoomResp.DiscardUnknown(m)
}

var xxx_messageInfo_AIShoutOutUserInRoomResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*AIAccountRecvUserMsgReq)(nil), "aigc.aigc_chat.AIAccountRecvUserMsgReq")
	proto.RegisterType((*AIAccountRecvUserMsgResp)(nil), "aigc.aigc_chat.AIAccountRecvUserMsgResp")
	proto.RegisterType((*AIAccountTriggerReq)(nil), "aigc.aigc_chat.AIAccountTriggerReq")
	proto.RegisterType((*AIAccountTriggerResp)(nil), "aigc.aigc_chat.AIAccountTriggerResp")
	proto.RegisterType((*AIShoutOutUserInRoomReq)(nil), "aigc.aigc_chat.AIShoutOutUserInRoomReq")
	proto.RegisterType((*AIShoutOutUserInRoomResp)(nil), "aigc.aigc_chat.AIShoutOutUserInRoomResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AigcChatServiceClient is the client API for AigcChatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AigcChatServiceClient interface {
	AIAccountRecvUserMsg(ctx context.Context, in *AIAccountRecvUserMsgReq, opts ...grpc.CallOption) (*AIAccountRecvUserMsgResp, error)
	SendAIAccountTrigger(ctx context.Context, in *AIAccountTriggerReq, opts ...grpc.CallOption) (*AIAccountTriggerResp, error)
	// 触发 AI 接待
	AIShoutOutUserInRoom(ctx context.Context, in *AIShoutOutUserInRoomReq, opts ...grpc.CallOption) (*AIShoutOutUserInRoomResp, error)
}

type aigcChatServiceClient struct {
	cc *grpc.ClientConn
}

func NewAigcChatServiceClient(cc *grpc.ClientConn) AigcChatServiceClient {
	return &aigcChatServiceClient{cc}
}

func (c *aigcChatServiceClient) AIAccountRecvUserMsg(ctx context.Context, in *AIAccountRecvUserMsgReq, opts ...grpc.CallOption) (*AIAccountRecvUserMsgResp, error) {
	out := new(AIAccountRecvUserMsgResp)
	err := c.cc.Invoke(ctx, "/aigc.aigc_chat.AigcChatService/AIAccountRecvUserMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcChatServiceClient) SendAIAccountTrigger(ctx context.Context, in *AIAccountTriggerReq, opts ...grpc.CallOption) (*AIAccountTriggerResp, error) {
	out := new(AIAccountTriggerResp)
	err := c.cc.Invoke(ctx, "/aigc.aigc_chat.AigcChatService/SendAIAccountTrigger", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aigcChatServiceClient) AIShoutOutUserInRoom(ctx context.Context, in *AIShoutOutUserInRoomReq, opts ...grpc.CallOption) (*AIShoutOutUserInRoomResp, error) {
	out := new(AIShoutOutUserInRoomResp)
	err := c.cc.Invoke(ctx, "/aigc.aigc_chat.AigcChatService/AIShoutOutUserInRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AigcChatServiceServer is the server API for AigcChatService service.
type AigcChatServiceServer interface {
	AIAccountRecvUserMsg(context.Context, *AIAccountRecvUserMsgReq) (*AIAccountRecvUserMsgResp, error)
	SendAIAccountTrigger(context.Context, *AIAccountTriggerReq) (*AIAccountTriggerResp, error)
	// 触发 AI 接待
	AIShoutOutUserInRoom(context.Context, *AIShoutOutUserInRoomReq) (*AIShoutOutUserInRoomResp, error)
}

func RegisterAigcChatServiceServer(s *grpc.Server, srv AigcChatServiceServer) {
	s.RegisterService(&_AigcChatService_serviceDesc, srv)
}

func _AigcChatService_AIAccountRecvUserMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AIAccountRecvUserMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcChatServiceServer).AIAccountRecvUserMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc.aigc_chat.AigcChatService/AIAccountRecvUserMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcChatServiceServer).AIAccountRecvUserMsg(ctx, req.(*AIAccountRecvUserMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcChatService_SendAIAccountTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AIAccountTriggerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcChatServiceServer).SendAIAccountTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc.aigc_chat.AigcChatService/SendAIAccountTrigger",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcChatServiceServer).SendAIAccountTrigger(ctx, req.(*AIAccountTriggerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AigcChatService_AIShoutOutUserInRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AIShoutOutUserInRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AigcChatServiceServer).AIShoutOutUserInRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/aigc.aigc_chat.AigcChatService/AIShoutOutUserInRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AigcChatServiceServer).AIShoutOutUserInRoom(ctx, req.(*AIShoutOutUserInRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AigcChatService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "aigc.aigc_chat.AigcChatService",
	HandlerType: (*AigcChatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AIAccountRecvUserMsg",
			Handler:    _AigcChatService_AIAccountRecvUserMsg_Handler,
		},
		{
			MethodName: "SendAIAccountTrigger",
			Handler:    _AigcChatService_SendAIAccountTrigger_Handler,
		},
		{
			MethodName: "AIShoutOutUserInRoom",
			Handler:    _AigcChatService_AIShoutOutUserInRoom_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aigc-apps/aigc-chat/aigc-chat.proto",
}

func init() {
	proto.RegisterFile("aigc-apps/aigc-chat/aigc-chat.proto", fileDescriptor_aigc_chat_b57b4b3c4a1917e9)
}

var fileDescriptor_aigc_chat_b57b4b3c4a1917e9 = []byte{
	// 539 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x54, 0x5d, 0x6f, 0xd3, 0x30,
	0x14, 0x55, 0xb6, 0x7e, 0xe5, 0x6e, 0xa5, 0xc8, 0x2b, 0x10, 0x2a, 0x4d, 0x2a, 0x19, 0x1a, 0x7d,
	0x21, 0x95, 0x36, 0xf1, 0x82, 0xf6, 0x52, 0x26, 0x1e, 0x2a, 0x31, 0x21, 0xa5, 0xdb, 0x0b, 0x2f,
	0x96, 0x71, 0x2c, 0xd7, 0x52, 0x13, 0x1b, 0xdb, 0x99, 0xd6, 0xbf, 0x85, 0xc4, 0x9f, 0xe0, 0x57,
	0x21, 0x3b, 0x5d, 0xba, 0xb1, 0x0c, 0x10, 0x2f, 0xd1, 0xcd, 0x3d, 0xc7, 0xd7, 0xf7, 0x1c, 0x5f,
	0x1b, 0x8e, 0x88, 0xe0, 0xf4, 0x2d, 0x51, 0xca, 0x4c, 0x7d, 0x44, 0x97, 0xc4, 0x6e, 0xa3, 0x44,
	0x69, 0x69, 0x25, 0x7a, 0xe2, 0x12, 0x89, 0xfb, 0x60, 0x97, 0x8d, 0xbf, 0xef, 0xc0, 0x8b, 0xd9,
	0x7c, 0x46, 0xa9, 0x2c, 0x0b, 0x9b, 0x32, 0x7a, 0x7d, 0x65, 0x98, 0xbe, 0x30, 0x3c, 0x65, 0xdf,
	0xd0, 0x53, 0xd8, 0x2d, 0x45, 0x16, 0x05, 0xe3, 0x60, 0xd2, 0x4f, 0x5d, 0x88, 0x62, 0xe8, 0x13,
	0x81, 0x49, 0xc5, 0xc6, 0x22, 0x8b, 0x76, 0x3c, 0xb6, 0x47, 0xc4, 0xa6, 0xc2, 0x3c, 0x43, 0x11,
	0x74, 0xa9, 0x2c, 0x2c, 0x2b, 0x6c, 0xb4, 0x3b, 0x0e, 0x26, 0x61, 0x7a, 0xfb, 0x8b, 0x9e, 0x41,
	0x27, 0x37, 0xdc, 0x2d, 0x6b, 0xf9, 0x65, 0xed, 0xdc, 0xf0, 0x79, 0x86, 0x5e, 0x42, 0xcf, 0xa5,
	0xed, 0x5a, 0xb1, 0xa8, 0xed, 0x81, 0x6e, 0x6e, 0xf8, 0xe5, 0x5a, 0x31, 0x34, 0x84, 0x36, 0xbb,
	0xb1, 0x9a, 0x44, 0x9d, 0x71, 0x30, 0xd9, 0x4f, 0xab, 0x1f, 0xd7, 0x85, 0x25, 0x9a, 0x33, 0x8b,
	0x37, 0xe5, 0xba, 0x55, 0x17, 0x55, 0xf2, 0xc2, 0x17, 0x3d, 0x86, 0x81, 0x03, 0x8d, 0x2c, 0x35,
	0x65, 0x55, 0xed, 0x9e, 0x67, 0xf5, 0x73, 0xc3, 0x17, 0x3e, 0xeb, 0x77, 0x38, 0x85, 0xe7, 0xc2,
	0x60, 0x76, 0x43, 0xa8, 0x5d, 0xad, 0xb1, 0x66, 0x84, 0x2e, 0xf1, 0x4a, 0xe4, 0xc2, 0x46, 0xe1,
	0x38, 0x98, 0xf4, 0xd2, 0x03, 0x61, 0x3e, 0x56, 0x60, 0xea, 0xb0, 0x4f, 0x0e, 0x8a, 0x47, 0x10,
	0x35, 0x7b, 0x66, 0x54, 0xfc, 0x23, 0x80, 0x83, 0x1a, 0xbc, 0xd4, 0x82, 0x73, 0xa6, 0xff, 0xdf,
	0xcc, 0x57, 0xb0, 0x5f, 0x1a, 0xa6, 0xf1, 0x7d, 0x47, 0xf7, 0x5c, 0xee, 0x7c, 0xe3, 0xea, 0x21,
	0x00, 0x11, 0x35, 0xa1, 0xe5, 0x09, 0x21, 0x11, 0xb7, 0xf0, 0x31, 0x0c, 0xb6, 0xf0, 0xd6, 0xe4,
	0x30, 0xed, 0xd7, 0x1c, 0x67, 0x44, 0x7c, 0x06, 0xc3, 0x87, 0x6d, 0x1b, 0x85, 0x10, 0xb4, 0xa8,
	0xcc, 0xd8, 0xa6, 0x71, 0x1f, 0x3b, 0x2d, 0xb9, 0xe1, 0xbe, 0xdf, 0x30, 0x75, 0x61, 0x2c, 0xdc,
	0x14, 0x2d, 0x96, 0xb2, 0xb4, 0x9f, 0x4b, 0xeb, 0xec, 0x98, 0x17, 0xa9, 0x94, 0xb9, 0x13, 0x7e,
	0x08, 0x40, 0x97, 0xa4, 0x28, 0xd8, 0x0a, 0xd7, 0xfa, 0xc3, 0x4d, 0x66, 0x9e, 0xb9, 0xa1, 0x20,
	0x02, 0x97, 0xb5, 0xfc, 0x36, 0x11, 0x57, 0xe2, 0x0f, 0x53, 0x54, 0x99, 0xdf, 0xb4, 0x95, 0x51,
	0x27, 0x3f, 0x77, 0x60, 0x30, 0x13, 0x9c, 0x9e, 0x2f, 0x89, 0x5d, 0x30, 0x7d, 0x2d, 0x28, 0x43,
	0xe2, 0x8e, 0xb0, 0x3b, 0x87, 0x85, 0xde, 0x24, 0xf7, 0xaf, 0x42, 0xf2, 0xc8, 0x35, 0x18, 0x4d,
	0xfe, 0x8d, 0x68, 0x14, 0x22, 0x30, 0x5c, 0xb0, 0x22, 0xfb, 0xdd, 0x47, 0x74, 0xf4, 0x68, 0x85,
	0xed, 0x80, 0x8c, 0x5e, 0xff, 0x9d, 0x64, 0x54, 0xa5, 0xe6, 0xa1, 0xfa, 0x26, 0x35, 0x8d, 0xc7,
	0xd1, 0xa4, 0xa6, 0xd9, 0xcc, 0x0f, 0x67, 0x5f, 0xde, 0x73, 0xb9, 0x22, 0x05, 0x4f, 0xde, 0x9d,
	0x58, 0x9b, 0x50, 0x99, 0x4f, 0xfd, 0x1b, 0x42, 0xe5, 0x6a, 0x6a, 0x2a, 0x73, 0xcd, 0x54, 0xd3,
	0x3c, 0xf3, 0x8f, 0x0c, 0xae, 0x1f, 0x1e, 0x5f, 0xf6, 0x6b, 0xc7, 0x73, 0x4f, 0x7f, 0x05, 0x00,
	0x00, 0xff, 0xff, 0x8a, 0x84, 0xaa, 0x0d, 0x96, 0x04, 0x00, 0x00,
}
