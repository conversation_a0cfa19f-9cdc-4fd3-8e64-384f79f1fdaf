// Code generated by protoc-gen-go-meta-client. DO NOT EDIT.
// versions:
// - protoc-gen-go-meta-client v0.0.1
// - protoc                   v3.2.0
// source: aigc-apps/aigc-chat/aigc-chat.proto

package aigc_chat

import (
	client "gitlab.ttyuyin.com/avengers/tyr/core/service/grpc/client"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const serviceName = "aigc-chat"

type Client struct {
	AigcChatServiceClient

	cc *grpc.ClientConn
}

func MustNewClient(ctx context.Context, opts ...grpc.DialOption) *Client {
	c, err := NewClient(ctx, opts...)
	if err != nil {
		panic(err)
	}
	return c
}

func NewClient(ctx context.Context, opts ...grpc.DialOption) (*Client, error) {
	c := &Client{}
	if err := client.DialContextWithConnUpdate(ctx, serviceName, opts, func(cc *grpc.ClientConn) *grpc.ClientConn {
		c.cc, cc = cc, c.cc
		c.AigcChatServiceClient = NewAigcChatServiceClient(c.cc)
		return cc
	}); err != nil {
		return nil, err
	}
	return c, nil
}

func MustNewClientTo(ctx context.Context, target string, opts ...grpc.DialOption) *Client {
	cc, err := client.DialContextTo(ctx, target, opts...)
	if err != nil {
		panic(err)
	}
	return &Client{
		AigcChatServiceClient: NewAigcChatServiceClient(cc),
	}
}
