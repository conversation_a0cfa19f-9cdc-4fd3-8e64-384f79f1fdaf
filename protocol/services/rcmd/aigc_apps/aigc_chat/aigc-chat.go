// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/rcmd/aigc_apps/aigc_chat (interfaces: AigcChatServiceClient)

// Package mocks is a generated GoMock package.
package aigc_chat

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAigcChatServiceClient is a mock of AigcChatServiceClient interface.
type MockAigcChatServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockAigcChatServiceClientMockRecorder
}

// MockAigcChatServiceClientMockRecorder is the mock recorder for MockAigcChatServiceClient.
type MockAigcChatServiceClientMockRecorder struct {
	mock *MockAigcChatServiceClient
}

// NewMockAigcChatServiceClient creates a new mock instance.
func NewMockAigcChatServiceClient(ctrl *gomock.Controller) *MockAigcChatServiceClient {
	mock := &MockAigcChatServiceClient{ctrl: ctrl}
	mock.recorder = &MockAigcChatServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcChatServiceClient) EXPECT() *MockAigcChatServiceClientMockRecorder {
	return m.recorder
}

// AIAccountRecvUserMsg mocks base method.
func (m *MockAigcChatServiceClient) AIAccountRecvUserMsg(arg0 context.Context, arg1 *AIAccountRecvUserMsgReq, arg2 ...grpc.CallOption) (*AIAccountRecvUserMsgResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AIAccountRecvUserMsg", varargs...)
	ret0, _ := ret[0].(*AIAccountRecvUserMsgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AIAccountRecvUserMsg indicates an expected call of AIAccountRecvUserMsg.
func (mr *MockAigcChatServiceClientMockRecorder) AIAccountRecvUserMsg(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AIAccountRecvUserMsg", reflect.TypeOf((*MockAigcChatServiceClient)(nil).AIAccountRecvUserMsg), varargs...)
}

// SendAIAccountTrigger mocks base method.
func (m *MockAigcChatServiceClient) SendAIAccountTrigger(arg0 context.Context, arg1 *AIAccountTriggerReq, arg2 ...grpc.CallOption) (*AIAccountTriggerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendAIAccountTrigger", varargs...)
	ret0, _ := ret[0].(*AIAccountTriggerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendAIAccountTrigger indicates an expected call of SendAIAccountTrigger.
func (mr *MockAigcChatServiceClientMockRecorder) SendAIAccountTrigger(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAIAccountTrigger", reflect.TypeOf((*MockAigcChatServiceClient)(nil).SendAIAccountTrigger), varargs...)
}
