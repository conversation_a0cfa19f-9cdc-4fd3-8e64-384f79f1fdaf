// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: aigc-apps/aigc-chat/aigc-chat.proto

package aigc_chat

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAigcChatServiceClient is a mock of AigcChatServiceClient interface.
type MockAigcChatServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockAigcChatServiceClientMockRecorder
}

// MockAigcChatServiceClientMockRecorder is the mock recorder for MockAigcChatServiceClient.
type MockAigcChatServiceClientMockRecorder struct {
	mock *MockAigcChatServiceClient
}

// NewMockAigcChatServiceClient creates a new mock instance.
func NewMockAigcChatServiceClient(ctrl *gomock.Controller) *MockAigcChatServiceClient {
	mock := &MockAigcChatServiceClient{ctrl: ctrl}
	mock.recorder = &MockAigcChatServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcChatServiceClient) EXPECT() *MockAigcChatServiceClientMockRecorder {
	return m.recorder
}

// AIAccountRecvUserMsg mocks base method.
func (m *MockAigcChatServiceClient) AIAccountRecvUserMsg(ctx context.Context, in *AIAccountRecvUserMsgReq, opts ...grpc.CallOption) (*AIAccountRecvUserMsgResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AIAccountRecvUserMsg", varargs...)
	ret0, _ := ret[0].(*AIAccountRecvUserMsgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AIAccountRecvUserMsg indicates an expected call of AIAccountRecvUserMsg.
func (mr *MockAigcChatServiceClientMockRecorder) AIAccountRecvUserMsg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AIAccountRecvUserMsg", reflect.TypeOf((*MockAigcChatServiceClient)(nil).AIAccountRecvUserMsg), varargs...)
}

// AIShoutOutUserInRoom mocks base method.
func (m *MockAigcChatServiceClient) AIShoutOutUserInRoom(ctx context.Context, in *AIShoutOutUserInRoomReq, opts ...grpc.CallOption) (*AIShoutOutUserInRoomResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AIShoutOutUserInRoom", varargs...)
	ret0, _ := ret[0].(*AIShoutOutUserInRoomResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AIShoutOutUserInRoom indicates an expected call of AIShoutOutUserInRoom.
func (mr *MockAigcChatServiceClientMockRecorder) AIShoutOutUserInRoom(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AIShoutOutUserInRoom", reflect.TypeOf((*MockAigcChatServiceClient)(nil).AIShoutOutUserInRoom), varargs...)
}

// SendAIAccountTrigger mocks base method.
func (m *MockAigcChatServiceClient) SendAIAccountTrigger(ctx context.Context, in *AIAccountTriggerReq, opts ...grpc.CallOption) (*AIAccountTriggerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendAIAccountTrigger", varargs...)
	ret0, _ := ret[0].(*AIAccountTriggerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendAIAccountTrigger indicates an expected call of SendAIAccountTrigger.
func (mr *MockAigcChatServiceClientMockRecorder) SendAIAccountTrigger(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAIAccountTrigger", reflect.TypeOf((*MockAigcChatServiceClient)(nil).SendAIAccountTrigger), varargs...)
}

// MockAigcChatServiceServer is a mock of AigcChatServiceServer interface.
type MockAigcChatServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockAigcChatServiceServerMockRecorder
}

// MockAigcChatServiceServerMockRecorder is the mock recorder for MockAigcChatServiceServer.
type MockAigcChatServiceServerMockRecorder struct {
	mock *MockAigcChatServiceServer
}

// NewMockAigcChatServiceServer creates a new mock instance.
func NewMockAigcChatServiceServer(ctrl *gomock.Controller) *MockAigcChatServiceServer {
	mock := &MockAigcChatServiceServer{ctrl: ctrl}
	mock.recorder = &MockAigcChatServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAigcChatServiceServer) EXPECT() *MockAigcChatServiceServerMockRecorder {
	return m.recorder
}

// AIAccountRecvUserMsg mocks base method.
func (m *MockAigcChatServiceServer) AIAccountRecvUserMsg(ctx context.Context, in *AIAccountRecvUserMsgReq) (*AIAccountRecvUserMsgResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AIAccountRecvUserMsg", ctx, in)
	ret0, _ := ret[0].(*AIAccountRecvUserMsgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AIAccountRecvUserMsg indicates an expected call of AIAccountRecvUserMsg.
func (mr *MockAigcChatServiceServerMockRecorder) AIAccountRecvUserMsg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AIAccountRecvUserMsg", reflect.TypeOf((*MockAigcChatServiceServer)(nil).AIAccountRecvUserMsg), ctx, in)
}

// AIShoutOutUserInRoom mocks base method.
func (m *MockAigcChatServiceServer) AIShoutOutUserInRoom(ctx context.Context, in *AIShoutOutUserInRoomReq) (*AIShoutOutUserInRoomResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AIShoutOutUserInRoom", ctx, in)
	ret0, _ := ret[0].(*AIShoutOutUserInRoomResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AIShoutOutUserInRoom indicates an expected call of AIShoutOutUserInRoom.
func (mr *MockAigcChatServiceServerMockRecorder) AIShoutOutUserInRoom(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AIShoutOutUserInRoom", reflect.TypeOf((*MockAigcChatServiceServer)(nil).AIShoutOutUserInRoom), ctx, in)
}

// SendAIAccountTrigger mocks base method.
func (m *MockAigcChatServiceServer) SendAIAccountTrigger(ctx context.Context, in *AIAccountTriggerReq) (*AIAccountTriggerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendAIAccountTrigger", ctx, in)
	ret0, _ := ret[0].(*AIAccountTriggerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendAIAccountTrigger indicates an expected call of SendAIAccountTrigger.
func (mr *MockAigcChatServiceServerMockRecorder) SendAIAccountTrigger(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAIAccountTrigger", reflect.TypeOf((*MockAigcChatServiceServer)(nil).SendAIAccountTrigger), ctx, in)
}
