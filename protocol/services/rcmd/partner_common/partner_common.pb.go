// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd-ai-partner/partner_common.proto

package partner_common // import "golang.52tt.com/protocol/services/rcmd/partner_common"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type FormatMsg struct {
	Content              string   `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	MsgType              uint32   `protobuf:"varint,2,opt,name=msg_type,json=msgType,proto3" json:"msg_type,omitempty"`
	Ext                  []byte   `protobuf:"bytes,3,opt,name=ext,proto3" json:"ext,omitempty"`
	ExceptedSendMsg      string   `protobuf:"bytes,4,opt,name=excepted_send_msg,json=exceptedSendMsg,proto3" json:"excepted_send_msg,omitempty"`
	ReplyType            uint32   `protobuf:"varint,5,opt,name=reply_type,json=replyType,proto3" json:"reply_type,omitempty"`
	ReplyTypeString      string   `protobuf:"bytes,6,opt,name=reply_type_string,json=replyTypeString,proto3" json:"reply_type_string,omitempty"`
	ReplyMsgPlanB        uint32   `protobuf:"varint,7,opt,name=reply_msg_plan_b,json=replyMsgPlanB,proto3" json:"reply_msg_plan_b,omitempty"`
	ReplyMsgPlanBStr     string   `protobuf:"bytes,8,opt,name=reply_msg_plan_b_str,json=replyMsgPlanBStr,proto3" json:"reply_msg_plan_b_str,omitempty"`
	AlgoDetectedInfo     string   `protobuf:"bytes,9,opt,name=algo_detected_info,json=algoDetectedInfo,proto3" json:"algo_detected_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FormatMsg) Reset()         { *m = FormatMsg{} }
func (m *FormatMsg) String() string { return proto.CompactTextString(m) }
func (*FormatMsg) ProtoMessage()    {}
func (*FormatMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_partner_common_019dcaaa4943c4fc, []int{0}
}
func (m *FormatMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FormatMsg.Unmarshal(m, b)
}
func (m *FormatMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FormatMsg.Marshal(b, m, deterministic)
}
func (dst *FormatMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FormatMsg.Merge(dst, src)
}
func (m *FormatMsg) XXX_Size() int {
	return xxx_messageInfo_FormatMsg.Size(m)
}
func (m *FormatMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_FormatMsg.DiscardUnknown(m)
}

var xxx_messageInfo_FormatMsg proto.InternalMessageInfo

func (m *FormatMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *FormatMsg) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

func (m *FormatMsg) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *FormatMsg) GetExceptedSendMsg() string {
	if m != nil {
		return m.ExceptedSendMsg
	}
	return ""
}

func (m *FormatMsg) GetReplyType() uint32 {
	if m != nil {
		return m.ReplyType
	}
	return 0
}

func (m *FormatMsg) GetReplyTypeString() string {
	if m != nil {
		return m.ReplyTypeString
	}
	return ""
}

func (m *FormatMsg) GetReplyMsgPlanB() uint32 {
	if m != nil {
		return m.ReplyMsgPlanB
	}
	return 0
}

func (m *FormatMsg) GetReplyMsgPlanBStr() string {
	if m != nil {
		return m.ReplyMsgPlanBStr
	}
	return ""
}

func (m *FormatMsg) GetAlgoDetectedInfo() string {
	if m != nil {
		return m.AlgoDetectedInfo
	}
	return ""
}

type GameInfo struct {
	GameId               string   `protobuf:"bytes,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	SessionId            string   `protobuf:"bytes,2,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	Prompt               string   `protobuf:"bytes,3,opt,name=prompt,proto3" json:"prompt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameInfo) Reset()         { *m = GameInfo{} }
func (m *GameInfo) String() string { return proto.CompactTextString(m) }
func (*GameInfo) ProtoMessage()    {}
func (*GameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_partner_common_019dcaaa4943c4fc, []int{1}
}
func (m *GameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameInfo.Unmarshal(m, b)
}
func (m *GameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameInfo.Marshal(b, m, deterministic)
}
func (dst *GameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameInfo.Merge(dst, src)
}
func (m *GameInfo) XXX_Size() int {
	return xxx_messageInfo_GameInfo.Size(m)
}
func (m *GameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameInfo proto.InternalMessageInfo

func (m *GameInfo) GetGameId() string {
	if m != nil {
		return m.GameId
	}
	return ""
}

func (m *GameInfo) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

func (m *GameInfo) GetPrompt() string {
	if m != nil {
		return m.Prompt
	}
	return ""
}

func init() {
	proto.RegisterType((*FormatMsg)(nil), "rcmd.partner_common.FormatMsg")
	proto.RegisterType((*GameInfo)(nil), "rcmd.partner_common.GameInfo")
}

func init() {
	proto.RegisterFile("rcmd-ai-partner/partner_common.proto", fileDescriptor_partner_common_019dcaaa4943c4fc)
}

var fileDescriptor_partner_common_019dcaaa4943c4fc = []byte{
	// 364 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x5c, 0x92, 0xdf, 0xeb, 0xd3, 0x30,
	0x14, 0xc5, 0xe9, 0xbe, 0xda, 0xae, 0xc1, 0xe1, 0x16, 0x45, 0xe3, 0x83, 0x30, 0x86, 0xe0, 0x18,
	0xae, 0x05, 0x65, 0xf8, 0x3e, 0x44, 0xd9, 0xc3, 0x40, 0x3a, 0x9f, 0xf6, 0x12, 0xb2, 0xe4, 0x2e,
	0x14, 0x9a, 0x1f, 0x24, 0x41, 0xb6, 0xff, 0xd2, 0x3f, 0x49, 0x92, 0x76, 0xca, 0xf6, 0xd4, 0x7b,
	0xcf, 0x39, 0x7c, 0xda, 0x7b, 0x6f, 0xd1, 0x07, 0xc7, 0x95, 0x58, 0xb3, 0x76, 0x6d, 0x99, 0x0b,
	0x1a, 0x5c, 0x3d, 0x3c, 0x29, 0x37, 0x4a, 0x19, 0x5d, 0x59, 0x67, 0x82, 0xc1, 0xaf, 0x62, 0xaa,
	0xba, 0xb7, 0x16, 0x7f, 0x46, 0xa8, 0xfc, 0x6e, 0x9c, 0x62, 0x61, 0xef, 0x25, 0x26, 0xa8, 0xe0,
	0x46, 0x07, 0xd0, 0x81, 0x64, 0xf3, 0x6c, 0x59, 0x36, 0xb7, 0x16, 0xbf, 0x43, 0x63, 0xe5, 0x25,
	0x0d, 0x57, 0x0b, 0x64, 0x34, 0xcf, 0x96, 0x93, 0xa6, 0x50, 0x5e, 0xfe, 0xba, 0x5a, 0xc0, 0x53,
	0xf4, 0x04, 0x97, 0x40, 0x9e, 0xe6, 0xd9, 0xf2, 0x45, 0x13, 0x4b, 0xbc, 0x42, 0x33, 0xb8, 0x70,
	0xb0, 0x01, 0x04, 0xf5, 0xa0, 0x05, 0x55, 0x5e, 0x92, 0x67, 0x09, 0xf8, 0xf2, 0x66, 0x1c, 0x40,
	0x8b, 0xf8, 0xca, 0xf7, 0x08, 0x39, 0xb0, 0xdd, 0xb5, 0x47, 0x3f, 0x4f, 0xe8, 0x32, 0x29, 0x09,
	0xbe, 0x42, 0xb3, 0xff, 0x36, 0xf5, 0xc1, 0xb5, 0x5a, 0x92, 0xbc, 0x47, 0xfd, 0x4b, 0x1d, 0x92,
	0x8c, 0x3f, 0xa2, 0x69, 0x9f, 0x8d, 0x5f, 0x6a, 0x3b, 0xa6, 0xe9, 0x89, 0x14, 0x09, 0x38, 0x49,
	0xfa, 0xde, 0xcb, 0x9f, 0x1d, 0xd3, 0x5b, 0x5c, 0xa1, 0xd7, 0x8f, 0xc1, 0x88, 0x26, 0xe3, 0xc4,
	0x9d, 0xde, 0x85, 0x0f, 0xc1, 0xe1, 0x4f, 0x08, 0xb3, 0x4e, 0x1a, 0x2a, 0x20, 0x00, 0x8f, 0x43,
	0xb5, 0xfa, 0x6c, 0x48, 0xd9, 0xa7, 0xa3, 0xf3, 0x6d, 0x30, 0x76, 0xfa, 0x6c, 0x16, 0x47, 0x34,
	0xfe, 0xc1, 0x14, 0xc4, 0x1a, 0xbf, 0x45, 0x85, 0x64, 0x0a, 0x68, 0x2b, 0x86, 0x85, 0xe6, 0xb1,
	0xdd, 0x89, 0x38, 0xb6, 0x07, 0xef, 0x5b, 0xa3, 0xa3, 0x37, 0x4a, 0x5e, 0x39, 0x28, 0x3b, 0x81,
	0xdf, 0xa0, 0xdc, 0x3a, 0xa3, 0x6c, 0xbf, 0xd6, 0xb2, 0x19, 0xba, 0xed, 0xd7, 0xe3, 0x46, 0x9a,
	0x8e, 0x69, 0x59, 0x6d, 0x3e, 0x87, 0x50, 0x71, 0xa3, 0xea, 0x74, 0x5c, 0x6e, 0xba, 0xda, 0x83,
	0xfb, 0xdd, 0x72, 0xf0, 0x75, 0xbc, 0xf3, 0xc3, 0x2f, 0x70, 0xca, 0x53, 0xec, 0xcb, 0xdf, 0x00,
	0x00, 0x00, 0xff, 0xff, 0x5d, 0x84, 0xc5, 0xf5, 0x2b, 0x02, 0x00, 0x00,
}
