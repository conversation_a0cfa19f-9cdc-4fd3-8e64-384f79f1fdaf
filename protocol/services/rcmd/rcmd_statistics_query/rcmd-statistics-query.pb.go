// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd-statistics-query/rcmd-statistics-query.proto

package rcmd_statistics_query // import "golang.52tt.com/protocol/services/rcmd/rcmd_statistics_query"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetUserGangUpCountReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GangupUid            uint32   `protobuf:"varint,2,opt,name=gangup_uid,json=gangupUid,proto3" json:"gangup_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserGangUpCountReq) Reset()         { *m = GetUserGangUpCountReq{} }
func (m *GetUserGangUpCountReq) String() string { return proto.CompactTextString(m) }
func (*GetUserGangUpCountReq) ProtoMessage()    {}
func (*GetUserGangUpCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{0}
}
func (m *GetUserGangUpCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGangUpCountReq.Unmarshal(m, b)
}
func (m *GetUserGangUpCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGangUpCountReq.Marshal(b, m, deterministic)
}
func (dst *GetUserGangUpCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGangUpCountReq.Merge(dst, src)
}
func (m *GetUserGangUpCountReq) XXX_Size() int {
	return xxx_messageInfo_GetUserGangUpCountReq.Size(m)
}
func (m *GetUserGangUpCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGangUpCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGangUpCountReq proto.InternalMessageInfo

func (m *GetUserGangUpCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserGangUpCountReq) GetGangupUid() uint32 {
	if m != nil {
		return m.GangupUid
	}
	return 0
}

type GetUserGangUpCountResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserGangUpCountResp) Reset()         { *m = GetUserGangUpCountResp{} }
func (m *GetUserGangUpCountResp) String() string { return proto.CompactTextString(m) }
func (*GetUserGangUpCountResp) ProtoMessage()    {}
func (*GetUserGangUpCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{1}
}
func (m *GetUserGangUpCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGangUpCountResp.Unmarshal(m, b)
}
func (m *GetUserGangUpCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGangUpCountResp.Marshal(b, m, deterministic)
}
func (dst *GetUserGangUpCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGangUpCountResp.Merge(dst, src)
}
func (m *GetUserGangUpCountResp) XXX_Size() int {
	return xxx_messageInfo_GetUserGangUpCountResp.Size(m)
}
func (m *GetUserGangUpCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGangUpCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGangUpCountResp proto.InternalMessageInfo

func (m *GetUserGangUpCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetGangUpAndImUserCountReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGangUpAndImUserCountReq) Reset()         { *m = GetGangUpAndImUserCountReq{} }
func (m *GetGangUpAndImUserCountReq) String() string { return proto.CompactTextString(m) }
func (*GetGangUpAndImUserCountReq) ProtoMessage()    {}
func (*GetGangUpAndImUserCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{2}
}
func (m *GetGangUpAndImUserCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangUpAndImUserCountReq.Unmarshal(m, b)
}
func (m *GetGangUpAndImUserCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangUpAndImUserCountReq.Marshal(b, m, deterministic)
}
func (dst *GetGangUpAndImUserCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangUpAndImUserCountReq.Merge(dst, src)
}
func (m *GetGangUpAndImUserCountReq) XXX_Size() int {
	return xxx_messageInfo_GetGangUpAndImUserCountReq.Size(m)
}
func (m *GetGangUpAndImUserCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangUpAndImUserCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangUpAndImUserCountReq proto.InternalMessageInfo

func (m *GetGangUpAndImUserCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGangUpAndImUserCountResp struct {
	ImUserCount          uint32   `protobuf:"varint,1,opt,name=im_user_count,json=imUserCount,proto3" json:"im_user_count,omitempty"`
	GangupUserCount      uint32   `protobuf:"varint,2,opt,name=gangup_user_count,json=gangupUserCount,proto3" json:"gangup_user_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGangUpAndImUserCountResp) Reset()         { *m = GetGangUpAndImUserCountResp{} }
func (m *GetGangUpAndImUserCountResp) String() string { return proto.CompactTextString(m) }
func (*GetGangUpAndImUserCountResp) ProtoMessage()    {}
func (*GetGangUpAndImUserCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{3}
}
func (m *GetGangUpAndImUserCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangUpAndImUserCountResp.Unmarshal(m, b)
}
func (m *GetGangUpAndImUserCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangUpAndImUserCountResp.Marshal(b, m, deterministic)
}
func (dst *GetGangUpAndImUserCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangUpAndImUserCountResp.Merge(dst, src)
}
func (m *GetGangUpAndImUserCountResp) XXX_Size() int {
	return xxx_messageInfo_GetGangUpAndImUserCountResp.Size(m)
}
func (m *GetGangUpAndImUserCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangUpAndImUserCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangUpAndImUserCountResp proto.InternalMessageInfo

func (m *GetGangUpAndImUserCountResp) GetImUserCount() uint32 {
	if m != nil {
		return m.ImUserCount
	}
	return 0
}

func (m *GetGangUpAndImUserCountResp) GetGangupUserCount() uint32 {
	if m != nil {
		return m.GangupUserCount
	}
	return 0
}

type GetUserGameCardStatDataReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserGameCardStatDataReq) Reset()         { *m = GetUserGameCardStatDataReq{} }
func (m *GetUserGameCardStatDataReq) String() string { return proto.CompactTextString(m) }
func (*GetUserGameCardStatDataReq) ProtoMessage()    {}
func (*GetUserGameCardStatDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{4}
}
func (m *GetUserGameCardStatDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGameCardStatDataReq.Unmarshal(m, b)
}
func (m *GetUserGameCardStatDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGameCardStatDataReq.Marshal(b, m, deterministic)
}
func (dst *GetUserGameCardStatDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGameCardStatDataReq.Merge(dst, src)
}
func (m *GetUserGameCardStatDataReq) XXX_Size() int {
	return xxx_messageInfo_GetUserGameCardStatDataReq.Size(m)
}
func (m *GetUserGameCardStatDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGameCardStatDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGameCardStatDataReq proto.InternalMessageInfo

func (m *GetUserGameCardStatDataReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserGameCardStatDataResp struct {
	EnterChannelTabList  []uint32 `protobuf:"varint,1,rep,packed,name=enter_channel_tab_list,json=enterChannelTabList,proto3" json:"enter_channel_tab_list,omitempty"`
	EnterGameAreaTabList []uint32 `protobuf:"varint,2,rep,packed,name=enter_game_area_tab_list,json=enterGameAreaTabList,proto3" json:"enter_game_area_tab_list,omitempty"`
	IsNotEnterGameArea   bool     `protobuf:"varint,3,opt,name=is_not_enter_game_area,json=isNotEnterGameArea,proto3" json:"is_not_enter_game_area,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserGameCardStatDataResp) Reset()         { *m = GetUserGameCardStatDataResp{} }
func (m *GetUserGameCardStatDataResp) String() string { return proto.CompactTextString(m) }
func (*GetUserGameCardStatDataResp) ProtoMessage()    {}
func (*GetUserGameCardStatDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{5}
}
func (m *GetUserGameCardStatDataResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGameCardStatDataResp.Unmarshal(m, b)
}
func (m *GetUserGameCardStatDataResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGameCardStatDataResp.Marshal(b, m, deterministic)
}
func (dst *GetUserGameCardStatDataResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGameCardStatDataResp.Merge(dst, src)
}
func (m *GetUserGameCardStatDataResp) XXX_Size() int {
	return xxx_messageInfo_GetUserGameCardStatDataResp.Size(m)
}
func (m *GetUserGameCardStatDataResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGameCardStatDataResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGameCardStatDataResp proto.InternalMessageInfo

func (m *GetUserGameCardStatDataResp) GetEnterChannelTabList() []uint32 {
	if m != nil {
		return m.EnterChannelTabList
	}
	return nil
}

func (m *GetUserGameCardStatDataResp) GetEnterGameAreaTabList() []uint32 {
	if m != nil {
		return m.EnterGameAreaTabList
	}
	return nil
}

func (m *GetUserGameCardStatDataResp) GetIsNotEnterGameArea() bool {
	if m != nil {
		return m.IsNotEnterGameArea
	}
	return false
}

type LikeItem struct {
	// 标签名
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 标签数量
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LikeItem) Reset()         { *m = LikeItem{} }
func (m *LikeItem) String() string { return proto.CompactTextString(m) }
func (*LikeItem) ProtoMessage()    {}
func (*LikeItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{6}
}
func (m *LikeItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LikeItem.Unmarshal(m, b)
}
func (m *LikeItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LikeItem.Marshal(b, m, deterministic)
}
func (dst *LikeItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LikeItem.Merge(dst, src)
}
func (m *LikeItem) XXX_Size() int {
	return xxx_messageInfo_LikeItem.Size(m)
}
func (m *LikeItem) XXX_DiscardUnknown() {
	xxx_messageInfo_LikeItem.DiscardUnknown(m)
}

var xxx_messageInfo_LikeItem proto.InternalMessageInfo

func (m *LikeItem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *LikeItem) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetUserLikeDataReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserLikeDataReq) Reset()         { *m = GetUserLikeDataReq{} }
func (m *GetUserLikeDataReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLikeDataReq) ProtoMessage()    {}
func (*GetUserLikeDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{7}
}
func (m *GetUserLikeDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLikeDataReq.Unmarshal(m, b)
}
func (m *GetUserLikeDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLikeDataReq.Marshal(b, m, deterministic)
}
func (dst *GetUserLikeDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLikeDataReq.Merge(dst, src)
}
func (m *GetUserLikeDataReq) XXX_Size() int {
	return xxx_messageInfo_GetUserLikeDataReq.Size(m)
}
func (m *GetUserLikeDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLikeDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLikeDataReq proto.InternalMessageInfo

func (m *GetUserLikeDataReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserLikeDataResp struct {
	HistoryLikeList      []*LikeItem `protobuf:"bytes,1,rep,name=history_like_list,json=historyLikeList,proto3" json:"history_like_list,omitempty"`
	RecentLikeList       []*LikeItem `protobuf:"bytes,2,rep,name=recent_like_list,json=recentLikeList,proto3" json:"recent_like_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetUserLikeDataResp) Reset()         { *m = GetUserLikeDataResp{} }
func (m *GetUserLikeDataResp) String() string { return proto.CompactTextString(m) }
func (*GetUserLikeDataResp) ProtoMessage()    {}
func (*GetUserLikeDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{8}
}
func (m *GetUserLikeDataResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLikeDataResp.Unmarshal(m, b)
}
func (m *GetUserLikeDataResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLikeDataResp.Marshal(b, m, deterministic)
}
func (dst *GetUserLikeDataResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLikeDataResp.Merge(dst, src)
}
func (m *GetUserLikeDataResp) XXX_Size() int {
	return xxx_messageInfo_GetUserLikeDataResp.Size(m)
}
func (m *GetUserLikeDataResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLikeDataResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLikeDataResp proto.InternalMessageInfo

func (m *GetUserLikeDataResp) GetHistoryLikeList() []*LikeItem {
	if m != nil {
		return m.HistoryLikeList
	}
	return nil
}

func (m *GetUserLikeDataResp) GetRecentLikeList() []*LikeItem {
	if m != nil {
		return m.RecentLikeList
	}
	return nil
}

type GetUserChatDaysReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RelUid               uint32   `protobuf:"varint,2,opt,name=rel_uid,json=relUid,proto3" json:"rel_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserChatDaysReq) Reset()         { *m = GetUserChatDaysReq{} }
func (m *GetUserChatDaysReq) String() string { return proto.CompactTextString(m) }
func (*GetUserChatDaysReq) ProtoMessage()    {}
func (*GetUserChatDaysReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{9}
}
func (m *GetUserChatDaysReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserChatDaysReq.Unmarshal(m, b)
}
func (m *GetUserChatDaysReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserChatDaysReq.Marshal(b, m, deterministic)
}
func (dst *GetUserChatDaysReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserChatDaysReq.Merge(dst, src)
}
func (m *GetUserChatDaysReq) XXX_Size() int {
	return xxx_messageInfo_GetUserChatDaysReq.Size(m)
}
func (m *GetUserChatDaysReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserChatDaysReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserChatDaysReq proto.InternalMessageInfo

func (m *GetUserChatDaysReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserChatDaysReq) GetRelUid() uint32 {
	if m != nil {
		return m.RelUid
	}
	return 0
}

type GetUserChatDaysResp struct {
	Days                 uint32   `protobuf:"varint,1,opt,name=days,proto3" json:"days,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserChatDaysResp) Reset()         { *m = GetUserChatDaysResp{} }
func (m *GetUserChatDaysResp) String() string { return proto.CompactTextString(m) }
func (*GetUserChatDaysResp) ProtoMessage()    {}
func (*GetUserChatDaysResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{10}
}
func (m *GetUserChatDaysResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserChatDaysResp.Unmarshal(m, b)
}
func (m *GetUserChatDaysResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserChatDaysResp.Marshal(b, m, deterministic)
}
func (dst *GetUserChatDaysResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserChatDaysResp.Merge(dst, src)
}
func (m *GetUserChatDaysResp) XXX_Size() int {
	return xxx_messageInfo_GetUserChatDaysResp.Size(m)
}
func (m *GetUserChatDaysResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserChatDaysResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserChatDaysResp proto.InternalMessageInfo

func (m *GetUserChatDaysResp) GetDays() uint32 {
	if m != nil {
		return m.Days
	}
	return 0
}

type CommentItem struct {
	// 标签名
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 标签数量
	Count uint32 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	// 标签评价时间
	Timestamp            int64    `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentItem) Reset()         { *m = CommentItem{} }
func (m *CommentItem) String() string { return proto.CompactTextString(m) }
func (*CommentItem) ProtoMessage()    {}
func (*CommentItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{11}
}
func (m *CommentItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentItem.Unmarshal(m, b)
}
func (m *CommentItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentItem.Marshal(b, m, deterministic)
}
func (dst *CommentItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentItem.Merge(dst, src)
}
func (m *CommentItem) XXX_Size() int {
	return xxx_messageInfo_CommentItem.Size(m)
}
func (m *CommentItem) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentItem.DiscardUnknown(m)
}

var xxx_messageInfo_CommentItem proto.InternalMessageInfo

func (m *CommentItem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CommentItem) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *CommentItem) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

type GetUserCommentDataReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCommentDataReq) Reset()         { *m = GetUserCommentDataReq{} }
func (m *GetUserCommentDataReq) String() string { return proto.CompactTextString(m) }
func (*GetUserCommentDataReq) ProtoMessage()    {}
func (*GetUserCommentDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{12}
}
func (m *GetUserCommentDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCommentDataReq.Unmarshal(m, b)
}
func (m *GetUserCommentDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCommentDataReq.Marshal(b, m, deterministic)
}
func (dst *GetUserCommentDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCommentDataReq.Merge(dst, src)
}
func (m *GetUserCommentDataReq) XXX_Size() int {
	return xxx_messageInfo_GetUserCommentDataReq.Size(m)
}
func (m *GetUserCommentDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCommentDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCommentDataReq proto.InternalMessageInfo

func (m *GetUserCommentDataReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserCommentDataResp struct {
	CommentList          []*CommentItem `protobuf:"bytes,1,rep,name=comment_list,json=commentList,proto3" json:"comment_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetUserCommentDataResp) Reset()         { *m = GetUserCommentDataResp{} }
func (m *GetUserCommentDataResp) String() string { return proto.CompactTextString(m) }
func (*GetUserCommentDataResp) ProtoMessage()    {}
func (*GetUserCommentDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{13}
}
func (m *GetUserCommentDataResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCommentDataResp.Unmarshal(m, b)
}
func (m *GetUserCommentDataResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCommentDataResp.Marshal(b, m, deterministic)
}
func (dst *GetUserCommentDataResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCommentDataResp.Merge(dst, src)
}
func (m *GetUserCommentDataResp) XXX_Size() int {
	return xxx_messageInfo_GetUserCommentDataResp.Size(m)
}
func (m *GetUserCommentDataResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCommentDataResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCommentDataResp proto.InternalMessageInfo

func (m *GetUserCommentDataResp) GetCommentList() []*CommentItem {
	if m != nil {
		return m.CommentList
	}
	return nil
}

type BatchGetUserCommentDataReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	ReqUid               uint32   `protobuf:"varint,2,opt,name=req_uid,json=reqUid,proto3" json:"req_uid,omitempty"`
	InputContent         string   `protobuf:"bytes,3,opt,name=input_content,json=inputContent,proto3" json:"input_content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserCommentDataReq) Reset()         { *m = BatchGetUserCommentDataReq{} }
func (m *BatchGetUserCommentDataReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserCommentDataReq) ProtoMessage()    {}
func (*BatchGetUserCommentDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{14}
}
func (m *BatchGetUserCommentDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserCommentDataReq.Unmarshal(m, b)
}
func (m *BatchGetUserCommentDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserCommentDataReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserCommentDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserCommentDataReq.Merge(dst, src)
}
func (m *BatchGetUserCommentDataReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserCommentDataReq.Size(m)
}
func (m *BatchGetUserCommentDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserCommentDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserCommentDataReq proto.InternalMessageInfo

func (m *BatchGetUserCommentDataReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *BatchGetUserCommentDataReq) GetReqUid() uint32 {
	if m != nil {
		return m.ReqUid
	}
	return 0
}

func (m *BatchGetUserCommentDataReq) GetInputContent() string {
	if m != nil {
		return m.InputContent
	}
	return ""
}

type BatchGetUserCommentDataResp struct {
	CommentMap           map[uint32]*BatchGetUserCommentDataResp_CommentItemList `protobuf:"bytes,1,rep,name=comment_map,json=commentMap,proto3" json:"comment_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                                `json:"-"`
	XXX_unrecognized     []byte                                                  `json:"-"`
	XXX_sizecache        int32                                                   `json:"-"`
}

func (m *BatchGetUserCommentDataResp) Reset()         { *m = BatchGetUserCommentDataResp{} }
func (m *BatchGetUserCommentDataResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserCommentDataResp) ProtoMessage()    {}
func (*BatchGetUserCommentDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{15}
}
func (m *BatchGetUserCommentDataResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserCommentDataResp.Unmarshal(m, b)
}
func (m *BatchGetUserCommentDataResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserCommentDataResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserCommentDataResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserCommentDataResp.Merge(dst, src)
}
func (m *BatchGetUserCommentDataResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserCommentDataResp.Size(m)
}
func (m *BatchGetUserCommentDataResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserCommentDataResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserCommentDataResp proto.InternalMessageInfo

func (m *BatchGetUserCommentDataResp) GetCommentMap() map[uint32]*BatchGetUserCommentDataResp_CommentItemList {
	if m != nil {
		return m.CommentMap
	}
	return nil
}

type BatchGetUserCommentDataResp_CommentItemList struct {
	CommentList          []*CommentItem `protobuf:"bytes,2,rep,name=comment_list,json=commentList,proto3" json:"comment_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchGetUserCommentDataResp_CommentItemList) Reset() {
	*m = BatchGetUserCommentDataResp_CommentItemList{}
}
func (m *BatchGetUserCommentDataResp_CommentItemList) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetUserCommentDataResp_CommentItemList) ProtoMessage() {}
func (*BatchGetUserCommentDataResp_CommentItemList) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_statistics_query_b4f141752d73e317, []int{15, 0}
}
func (m *BatchGetUserCommentDataResp_CommentItemList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserCommentDataResp_CommentItemList.Unmarshal(m, b)
}
func (m *BatchGetUserCommentDataResp_CommentItemList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserCommentDataResp_CommentItemList.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserCommentDataResp_CommentItemList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserCommentDataResp_CommentItemList.Merge(dst, src)
}
func (m *BatchGetUserCommentDataResp_CommentItemList) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserCommentDataResp_CommentItemList.Size(m)
}
func (m *BatchGetUserCommentDataResp_CommentItemList) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserCommentDataResp_CommentItemList.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserCommentDataResp_CommentItemList proto.InternalMessageInfo

func (m *BatchGetUserCommentDataResp_CommentItemList) GetCommentList() []*CommentItem {
	if m != nil {
		return m.CommentList
	}
	return nil
}

func init() {
	proto.RegisterType((*GetUserGangUpCountReq)(nil), "rcmd.rcmd_statistics_query.GetUserGangUpCountReq")
	proto.RegisterType((*GetUserGangUpCountResp)(nil), "rcmd.rcmd_statistics_query.GetUserGangUpCountResp")
	proto.RegisterType((*GetGangUpAndImUserCountReq)(nil), "rcmd.rcmd_statistics_query.GetGangUpAndImUserCountReq")
	proto.RegisterType((*GetGangUpAndImUserCountResp)(nil), "rcmd.rcmd_statistics_query.GetGangUpAndImUserCountResp")
	proto.RegisterType((*GetUserGameCardStatDataReq)(nil), "rcmd.rcmd_statistics_query.GetUserGameCardStatDataReq")
	proto.RegisterType((*GetUserGameCardStatDataResp)(nil), "rcmd.rcmd_statistics_query.GetUserGameCardStatDataResp")
	proto.RegisterType((*LikeItem)(nil), "rcmd.rcmd_statistics_query.LikeItem")
	proto.RegisterType((*GetUserLikeDataReq)(nil), "rcmd.rcmd_statistics_query.GetUserLikeDataReq")
	proto.RegisterType((*GetUserLikeDataResp)(nil), "rcmd.rcmd_statistics_query.GetUserLikeDataResp")
	proto.RegisterType((*GetUserChatDaysReq)(nil), "rcmd.rcmd_statistics_query.GetUserChatDaysReq")
	proto.RegisterType((*GetUserChatDaysResp)(nil), "rcmd.rcmd_statistics_query.GetUserChatDaysResp")
	proto.RegisterType((*CommentItem)(nil), "rcmd.rcmd_statistics_query.CommentItem")
	proto.RegisterType((*GetUserCommentDataReq)(nil), "rcmd.rcmd_statistics_query.GetUserCommentDataReq")
	proto.RegisterType((*GetUserCommentDataResp)(nil), "rcmd.rcmd_statistics_query.GetUserCommentDataResp")
	proto.RegisterType((*BatchGetUserCommentDataReq)(nil), "rcmd.rcmd_statistics_query.BatchGetUserCommentDataReq")
	proto.RegisterType((*BatchGetUserCommentDataResp)(nil), "rcmd.rcmd_statistics_query.BatchGetUserCommentDataResp")
	proto.RegisterMapType((map[uint32]*BatchGetUserCommentDataResp_CommentItemList)(nil), "rcmd.rcmd_statistics_query.BatchGetUserCommentDataResp.CommentMapEntry")
	proto.RegisterType((*BatchGetUserCommentDataResp_CommentItemList)(nil), "rcmd.rcmd_statistics_query.BatchGetUserCommentDataResp.CommentItemList")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RCMDStatisticsQueryClient is the client API for RCMDStatisticsQuery service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RCMDStatisticsQueryClient interface {
	// 获取用户开黑统计相关数据（提供给服务端添加和删除游戏卡使用）
	GetUserGameCardStatData(ctx context.Context, in *GetUserGameCardStatDataReq, opts ...grpc.CallOption) (*GetUserGameCardStatDataResp, error)
	// 获取统计用户最近2天（昨天和前天）一起开黑过，或者一起互聊过的人数
	GetGangUpAndImUserCount(ctx context.Context, in *GetGangUpAndImUserCountReq, opts ...grpc.CallOption) (*GetGangUpAndImUserCountResp, error)
	// 用户开黑次数
	GetUserGangUpCount(ctx context.Context, in *GetUserGangUpCountReq, opts ...grpc.CallOption) (*GetUserGangUpCountResp, error)
	// 获取点赞数据
	GetUserLikeData(ctx context.Context, in *GetUserLikeDataReq, opts ...grpc.CallOption) (*GetUserLikeDataResp, error)
	// 获取用户互聊天数
	GetUserChatDays(ctx context.Context, in *GetUserChatDaysReq, opts ...grpc.CallOption) (*GetUserChatDaysResp, error)
	// 获取点评数据-废弃
	GetUserCommentData(ctx context.Context, in *GetUserCommentDataReq, opts ...grpc.CallOption) (*GetUserCommentDataResp, error)
	// 批量获取点评数据
	BatchGetUserCommentData(ctx context.Context, in *BatchGetUserCommentDataReq, opts ...grpc.CallOption) (*BatchGetUserCommentDataResp, error)
}

type rCMDStatisticsQueryClient struct {
	cc *grpc.ClientConn
}

func NewRCMDStatisticsQueryClient(cc *grpc.ClientConn) RCMDStatisticsQueryClient {
	return &rCMDStatisticsQueryClient{cc}
}

func (c *rCMDStatisticsQueryClient) GetUserGameCardStatData(ctx context.Context, in *GetUserGameCardStatDataReq, opts ...grpc.CallOption) (*GetUserGameCardStatDataResp, error) {
	out := new(GetUserGameCardStatDataResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_statistics_query.RCMDStatisticsQuery/GetUserGameCardStatData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDStatisticsQueryClient) GetGangUpAndImUserCount(ctx context.Context, in *GetGangUpAndImUserCountReq, opts ...grpc.CallOption) (*GetGangUpAndImUserCountResp, error) {
	out := new(GetGangUpAndImUserCountResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_statistics_query.RCMDStatisticsQuery/GetGangUpAndImUserCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDStatisticsQueryClient) GetUserGangUpCount(ctx context.Context, in *GetUserGangUpCountReq, opts ...grpc.CallOption) (*GetUserGangUpCountResp, error) {
	out := new(GetUserGangUpCountResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_statistics_query.RCMDStatisticsQuery/GetUserGangUpCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDStatisticsQueryClient) GetUserLikeData(ctx context.Context, in *GetUserLikeDataReq, opts ...grpc.CallOption) (*GetUserLikeDataResp, error) {
	out := new(GetUserLikeDataResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_statistics_query.RCMDStatisticsQuery/GetUserLikeData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDStatisticsQueryClient) GetUserChatDays(ctx context.Context, in *GetUserChatDaysReq, opts ...grpc.CallOption) (*GetUserChatDaysResp, error) {
	out := new(GetUserChatDaysResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_statistics_query.RCMDStatisticsQuery/GetUserChatDays", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDStatisticsQueryClient) GetUserCommentData(ctx context.Context, in *GetUserCommentDataReq, opts ...grpc.CallOption) (*GetUserCommentDataResp, error) {
	out := new(GetUserCommentDataResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_statistics_query.RCMDStatisticsQuery/GetUserCommentData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDStatisticsQueryClient) BatchGetUserCommentData(ctx context.Context, in *BatchGetUserCommentDataReq, opts ...grpc.CallOption) (*BatchGetUserCommentDataResp, error) {
	out := new(BatchGetUserCommentDataResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_statistics_query.RCMDStatisticsQuery/BatchGetUserCommentData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RCMDStatisticsQueryServer is the server API for RCMDStatisticsQuery service.
type RCMDStatisticsQueryServer interface {
	// 获取用户开黑统计相关数据（提供给服务端添加和删除游戏卡使用）
	GetUserGameCardStatData(context.Context, *GetUserGameCardStatDataReq) (*GetUserGameCardStatDataResp, error)
	// 获取统计用户最近2天（昨天和前天）一起开黑过，或者一起互聊过的人数
	GetGangUpAndImUserCount(context.Context, *GetGangUpAndImUserCountReq) (*GetGangUpAndImUserCountResp, error)
	// 用户开黑次数
	GetUserGangUpCount(context.Context, *GetUserGangUpCountReq) (*GetUserGangUpCountResp, error)
	// 获取点赞数据
	GetUserLikeData(context.Context, *GetUserLikeDataReq) (*GetUserLikeDataResp, error)
	// 获取用户互聊天数
	GetUserChatDays(context.Context, *GetUserChatDaysReq) (*GetUserChatDaysResp, error)
	// 获取点评数据-废弃
	GetUserCommentData(context.Context, *GetUserCommentDataReq) (*GetUserCommentDataResp, error)
	// 批量获取点评数据
	BatchGetUserCommentData(context.Context, *BatchGetUserCommentDataReq) (*BatchGetUserCommentDataResp, error)
}

func RegisterRCMDStatisticsQueryServer(s *grpc.Server, srv RCMDStatisticsQueryServer) {
	s.RegisterService(&_RCMDStatisticsQuery_serviceDesc, srv)
}

func _RCMDStatisticsQuery_GetUserGameCardStatData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserGameCardStatDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDStatisticsQueryServer).GetUserGameCardStatData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_statistics_query.RCMDStatisticsQuery/GetUserGameCardStatData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDStatisticsQueryServer).GetUserGameCardStatData(ctx, req.(*GetUserGameCardStatDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDStatisticsQuery_GetGangUpAndImUserCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGangUpAndImUserCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDStatisticsQueryServer).GetGangUpAndImUserCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_statistics_query.RCMDStatisticsQuery/GetGangUpAndImUserCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDStatisticsQueryServer).GetGangUpAndImUserCount(ctx, req.(*GetGangUpAndImUserCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDStatisticsQuery_GetUserGangUpCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserGangUpCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDStatisticsQueryServer).GetUserGangUpCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_statistics_query.RCMDStatisticsQuery/GetUserGangUpCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDStatisticsQueryServer).GetUserGangUpCount(ctx, req.(*GetUserGangUpCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDStatisticsQuery_GetUserLikeData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLikeDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDStatisticsQueryServer).GetUserLikeData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_statistics_query.RCMDStatisticsQuery/GetUserLikeData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDStatisticsQueryServer).GetUserLikeData(ctx, req.(*GetUserLikeDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDStatisticsQuery_GetUserChatDays_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserChatDaysReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDStatisticsQueryServer).GetUserChatDays(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_statistics_query.RCMDStatisticsQuery/GetUserChatDays",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDStatisticsQueryServer).GetUserChatDays(ctx, req.(*GetUserChatDaysReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDStatisticsQuery_GetUserCommentData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCommentDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDStatisticsQueryServer).GetUserCommentData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_statistics_query.RCMDStatisticsQuery/GetUserCommentData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDStatisticsQueryServer).GetUserCommentData(ctx, req.(*GetUserCommentDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDStatisticsQuery_BatchGetUserCommentData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserCommentDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDStatisticsQueryServer).BatchGetUserCommentData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_statistics_query.RCMDStatisticsQuery/BatchGetUserCommentData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDStatisticsQueryServer).BatchGetUserCommentData(ctx, req.(*BatchGetUserCommentDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RCMDStatisticsQuery_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.rcmd_statistics_query.RCMDStatisticsQuery",
	HandlerType: (*RCMDStatisticsQueryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserGameCardStatData",
			Handler:    _RCMDStatisticsQuery_GetUserGameCardStatData_Handler,
		},
		{
			MethodName: "GetGangUpAndImUserCount",
			Handler:    _RCMDStatisticsQuery_GetGangUpAndImUserCount_Handler,
		},
		{
			MethodName: "GetUserGangUpCount",
			Handler:    _RCMDStatisticsQuery_GetUserGangUpCount_Handler,
		},
		{
			MethodName: "GetUserLikeData",
			Handler:    _RCMDStatisticsQuery_GetUserLikeData_Handler,
		},
		{
			MethodName: "GetUserChatDays",
			Handler:    _RCMDStatisticsQuery_GetUserChatDays_Handler,
		},
		{
			MethodName: "GetUserCommentData",
			Handler:    _RCMDStatisticsQuery_GetUserCommentData_Handler,
		},
		{
			MethodName: "BatchGetUserCommentData",
			Handler:    _RCMDStatisticsQuery_BatchGetUserCommentData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rcmd-statistics-query/rcmd-statistics-query.proto",
}

func init() {
	proto.RegisterFile("rcmd-statistics-query/rcmd-statistics-query.proto", fileDescriptor_rcmd_statistics_query_b4f141752d73e317)
}

var fileDescriptor_rcmd_statistics_query_b4f141752d73e317 = []byte{
	// 836 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x56, 0xdd, 0x8e, 0xdb, 0x44,
	0x14, 0x96, 0x93, 0xfe, 0xe5, 0xa4, 0x21, 0xed, 0x6c, 0xd9, 0xae, 0x5c, 0x90, 0x2a, 0x83, 0x60,
	0x8b, 0x54, 0x47, 0x4d, 0xa1, 0x20, 0x84, 0x40, 0x6d, 0x76, 0x15, 0x16, 0xed, 0xae, 0xc0, 0x4b,
	0x6e, 0x90, 0x56, 0xd6, 0xac, 0x3d, 0x4a, 0x46, 0xb1, 0xc7, 0x13, 0xcf, 0x78, 0xa5, 0x88, 0x7b,
	0x2e, 0x78, 0x08, 0x5e, 0x83, 0x3b, 0x5e, 0x89, 0x57, 0x40, 0x33, 0xfe, 0x4d, 0xb0, 0xb3, 0xc9,
	0xf6, 0x26, 0x9a, 0xcc, 0x9c, 0xf3, 0x9d, 0xf3, 0x7d, 0x67, 0xce, 0x19, 0xc3, 0xab, 0xd8, 0x0b,
	0xfd, 0x97, 0x42, 0x62, 0x49, 0x85, 0xa4, 0x9e, 0x78, 0xb9, 0x48, 0x48, 0xbc, 0x1c, 0xd4, 0xee,
	0xda, 0x3c, 0x8e, 0x64, 0x84, 0x4c, 0x75, 0x68, 0xab, 0x1f, 0xb7, 0xb4, 0x70, 0xb5, 0x85, 0xf5,
	0x23, 0x7c, 0x38, 0x26, 0x72, 0x22, 0x48, 0x3c, 0xc6, 0x6c, 0x3a, 0xe1, 0xa3, 0x28, 0x61, 0xd2,
	0x21, 0x0b, 0xf4, 0x08, 0xda, 0x09, 0xf5, 0x0f, 0x8c, 0xe7, 0xc6, 0x61, 0xcf, 0x51, 0x4b, 0xf4,
	0x31, 0xc0, 0x14, 0xb3, 0x69, 0xc2, 0x5d, 0x75, 0xd0, 0xd2, 0x07, 0x9d, 0x74, 0x67, 0x42, 0x7d,
	0xcb, 0x86, 0xfd, 0x3a, 0x24, 0xc1, 0xd1, 0x13, 0xb8, 0xeb, 0xa9, 0x3f, 0x19, 0x58, 0xfa, 0xc7,
	0xb2, 0xc1, 0x1c, 0x13, 0x99, 0xda, 0xbe, 0x65, 0xfe, 0x49, 0xa8, 0x5c, 0x9b, 0xc3, 0x5b, 0x21,
	0x3c, 0x6b, 0xb4, 0x17, 0x1c, 0x59, 0xd0, 0xa3, 0xa1, 0x9b, 0x08, 0x12, 0xbb, 0xd5, 0x60, 0x5d,
	0x5a, 0xda, 0xa1, 0x2f, 0xe0, 0x71, 0xce, 0xa0, 0xb4, 0x4b, 0x89, 0xf4, 0x33, 0x22, 0xb9, 0x6d,
	0x96, 0x5e, 0x4a, 0x27, 0x24, 0x23, 0x1c, 0xfb, 0x17, 0x12, 0xcb, 0x23, 0x2c, 0x71, 0x7d, 0x7a,
	0xff, 0x18, 0x3a, 0xbf, 0x7a, 0x07, 0xc1, 0xd1, 0x6b, 0xd8, 0x27, 0x4c, 0xaa, 0xa8, 0x33, 0xcc,
	0x18, 0x09, 0x5c, 0x89, 0xaf, 0xdc, 0x80, 0x0a, 0x95, 0x68, 0xfb, 0xb0, 0xe7, 0xec, 0xe9, 0xd3,
	0x51, 0x7a, 0xf8, 0x2b, 0xbe, 0x3a, 0xa5, 0x42, 0xa2, 0x37, 0x70, 0x90, 0x3a, 0x4d, 0x71, 0x48,
	0x5c, 0x1c, 0x13, 0x5c, 0xba, 0xb5, 0xb4, 0xdb, 0x13, 0x7d, 0xae, 0x22, 0xbe, 0x8d, 0x09, 0xce,
	0xfd, 0x86, 0xb0, 0x4f, 0x85, 0xcb, 0x22, 0xe9, 0xae, 0xb9, 0x1f, 0xb4, 0x9f, 0x1b, 0x87, 0x0f,
	0x1c, 0x44, 0xc5, 0x79, 0x24, 0x8f, 0xab, 0xae, 0xd6, 0x97, 0xf0, 0xe0, 0x94, 0xce, 0xc9, 0x89,
	0x24, 0x21, 0x42, 0x70, 0x87, 0xe1, 0x90, 0x68, 0x7e, 0x1d, 0x47, 0xaf, 0xcb, 0x2a, 0xb6, 0xaa,
	0x55, 0xfc, 0x0c, 0x50, 0xc6, 0x5a, 0x39, 0x37, 0xcb, 0xf3, 0xb7, 0x01, 0x7b, 0xff, 0x33, 0x14,
	0x1c, 0xfd, 0x0c, 0x8f, 0x67, 0x54, 0xc8, 0x28, 0x5e, 0xba, 0x01, 0x9d, 0x93, 0x52, 0x91, 0xee,
	0xf0, 0x53, 0xbb, 0xf9, 0xde, 0xda, 0x79, 0xaa, 0x4e, 0x3f, 0x73, 0x57, 0x1b, 0x9a, 0xfb, 0x39,
	0x3c, 0x8a, 0x89, 0x47, 0x98, 0xac, 0x00, 0xb6, 0x76, 0x00, 0xfc, 0x20, 0xf5, 0xce, 0xf1, 0xac,
	0x1f, 0x0a, 0x86, 0xa3, 0x99, 0xaa, 0xe7, 0x52, 0xd4, 0xb7, 0xc7, 0x53, 0xb8, 0x1f, 0x93, 0xa0,
	0xd2, 0x1b, 0xf7, 0x62, 0x12, 0xa8, 0xc6, 0x78, 0x51, 0x30, 0x2f, 0x01, 0x04, 0x57, 0x1a, 0xfb,
	0x78, 0x29, 0x32, 0x08, 0xbd, 0xb6, 0x26, 0xd0, 0x1d, 0x45, 0x61, 0x48, 0x98, 0xdc, 0xad, 0x0c,
	0xe8, 0x23, 0xe8, 0x48, 0x1a, 0x12, 0x21, 0x71, 0xc8, 0x75, 0x8d, 0xdb, 0x4e, 0xb9, 0x61, 0xbd,
	0x28, 0x9a, 0x3c, 0x43, 0x6f, 0xae, 0x93, 0x5f, 0x74, 0xf1, 0x8a, 0xa9, 0xe0, 0xe8, 0x27, 0x78,
	0xe8, 0xa5, 0x5b, 0xd5, 0x22, 0x7d, 0xbe, 0x49, 0xd3, 0x0a, 0x17, 0xa7, 0x9b, 0x39, 0x6b, 0x4d,
	0x19, 0x98, 0xef, 0xb0, 0xf4, 0x66, 0xf5, 0x59, 0x21, 0xb8, 0x93, 0x50, 0x5f, 0x64, 0x8d, 0xa1,
	0xd7, 0xa9, 0xba, 0x8b, 0x55, 0x75, 0x17, 0x13, 0xea, 0xa3, 0x4f, 0xa0, 0x47, 0x19, 0x4f, 0xa4,
	0xeb, 0x45, 0x4c, 0x12, 0x26, 0x35, 0xfb, 0x8e, 0xf3, 0x50, 0x6f, 0x8e, 0xd2, 0x3d, 0xeb, 0xdf,
	0x16, 0x3c, 0x6b, 0x0c, 0x28, 0x38, 0x9a, 0x41, 0x9e, 0x9e, 0x1b, 0x62, 0x9e, 0x51, 0x1b, 0x6f,
	0xa2, 0xb6, 0x01, 0x2d, 0xa7, 0x7d, 0x86, 0xf9, 0x31, 0x93, 0xf1, 0xd2, 0x01, 0xaf, 0xd8, 0x30,
	0x2f, 0xa1, 0x5f, 0x51, 0x45, 0x5f, 0xd8, 0x75, 0x61, 0x5b, 0xb7, 0x17, 0xd6, 0xfc, 0xc3, 0x28,
	0xf0, 0xf3, 0xf0, 0xaa, 0xc8, 0x73, 0xb2, 0xcc, 0x8b, 0x3c, 0x27, 0x4b, 0x74, 0x09, 0x77, 0xaf,
	0x71, 0x90, 0x10, 0x2d, 0xe5, 0xfb, 0x13, 0xcd, 0x99, 0x38, 0x29, 0xea, 0xb7, 0xad, 0x6f, 0x8c,
	0xe1, 0x5f, 0xf7, 0x61, 0xcf, 0x19, 0x9d, 0x1d, 0x5d, 0x14, 0x78, 0xbf, 0x28, 0x38, 0xf4, 0xa7,
	0x01, 0x4f, 0x1b, 0xc6, 0x24, 0x7a, 0xb3, 0x29, 0x8f, 0xe6, 0x61, 0x6c, 0x7e, 0x7d, 0x2b, 0x3f,
	0xc1, 0xf3, 0x64, 0xea, 0xde, 0x94, 0x1b, 0x93, 0x69, 0x78, 0xb8, 0x6e, 0x4c, 0xa6, 0xf1, 0x01,
	0xfb, 0xbd, 0x98, 0x33, 0x95, 0xf7, 0x13, 0xbd, 0xda, 0x8a, 0x5b, 0xf5, 0xe5, 0x36, 0x87, 0xbb,
	0xba, 0x08, 0x8e, 0x62, 0xe8, 0xaf, 0x4d, 0x67, 0x64, 0x6f, 0x01, 0x53, 0x99, 0xf9, 0xe6, 0x60,
	0x27, 0xfb, 0x95, 0x98, 0xf9, 0x5c, 0xdc, 0x2a, 0x66, 0x65, 0x0a, 0x6f, 0x15, 0x73, 0x65, 0xe8,
	0x96, 0x22, 0x57, 0xee, 0xf2, 0x56, 0x22, 0xaf, 0xce, 0xa8, 0xad, 0x44, 0x5e, 0x9f, 0x32, 0xea,
	0xba, 0x35, 0xb4, 0xd3, 0xe6, 0xeb, 0xd6, 0x3c, 0x2b, 0x37, 0x5f, 0xb7, 0x0d, 0xbd, 0xfb, 0xee,
	0xfb, 0xdf, 0xbe, 0x9b, 0x46, 0x01, 0x66, 0x53, 0xfb, 0xab, 0xa1, 0x94, 0xb6, 0x17, 0x85, 0x03,
	0xfd, 0xb5, 0xe8, 0x45, 0xc1, 0x40, 0x90, 0xf8, 0x9a, 0x7a, 0x44, 0xe8, 0xaf, 0xca, 0x41, 0x2d,
	0xf6, 0xd5, 0x3d, 0x6d, 0xfd, 0xfa, 0xbf, 0x00, 0x00, 0x00, 0xff, 0xff, 0x99, 0x38, 0xb4, 0x1c,
	0x90, 0x0a, 0x00, 0x00,
}
