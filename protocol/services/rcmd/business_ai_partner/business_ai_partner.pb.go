// Code generated by protoc-gen-go. DO NOT EDIT.
// source: business-ai-partner/business_ai_partner.proto

package business_ai_partner // import "golang.52tt.com/protocol/services/rcmd/business_ai_partner"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import cmd_gateway "golang.52tt.com/protocol/services/rcmd/cmd_gateway"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RoleLikeAction int32

const (
	RoleLikeAction_NoLIKE RoleLikeAction = 0
	RoleLikeAction_LIKE   RoleLikeAction = 1
	RoleLikeAction_UNLIKE RoleLikeAction = 2
)

var RoleLikeAction_name = map[int32]string{
	0: "NoLIKE",
	1: "LIKE",
	2: "UNLIKE",
}
var RoleLikeAction_value = map[string]int32{
	"NoLIKE": 0,
	"LIKE":   1,
	"UNLIKE": 2,
}

func (x RoleLikeAction) String() string {
	return proto.EnumName(RoleLikeAction_name, int32(x))
}
func (RoleLikeAction) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{0}
}

type PartnerSource int32

const (
	// 用户创建
	PartnerSource_SourceUser PartnerSource = 0
	// 去形象化创建
	PartnerSource_SourceDeRole PartnerSource = 1
	// 游戏形象
	PartnerSource_SourceGame PartnerSource = 2
	// 多角色
	PartnerSource_SourceMultiRole PartnerSource = 3
	// AIGC主动触发
	PartnerSource_SourceAIGCTrigger PartnerSource = 4
)

var PartnerSource_name = map[int32]string{
	0: "SourceUser",
	1: "SourceDeRole",
	2: "SourceGame",
	3: "SourceMultiRole",
	4: "SourceAIGCTrigger",
}
var PartnerSource_value = map[string]int32{
	"SourceUser":        0,
	"SourceDeRole":      1,
	"SourceGame":        2,
	"SourceMultiRole":   3,
	"SourceAIGCTrigger": 4,
}

func (x PartnerSource) String() string {
	return proto.EnumName(PartnerSource_name, int32(x))
}
func (PartnerSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{1}
}

type RoleSource int32

const (
	RoleSource_RoleSource_Invalid RoleSource = 0
	RoleSource_RoleSource_Op      RoleSource = 1
	RoleSource_RoleSource_User    RoleSource = 2
)

var RoleSource_name = map[int32]string{
	0: "RoleSource_Invalid",
	1: "RoleSource_Op",
	2: "RoleSource_User",
}
var RoleSource_value = map[string]int32{
	"RoleSource_Invalid": 0,
	"RoleSource_Op":      1,
	"RoleSource_User":    2,
}

func (x RoleSource) String() string {
	return proto.EnumName(RoleSource_name, int32(x))
}
func (RoleSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{2}
}

type RoleType int32

const (
	RoleType_RoleType_Default RoleType = 0
	RoleType_RoleType_Game    RoleType = 1
	RoleType_RoleType_Pet     RoleType = 2
	RoleType_RoleType_Group   RoleType = 3
)

var RoleType_name = map[int32]string{
	0: "RoleType_Default",
	1: "RoleType_Game",
	2: "RoleType_Pet",
	3: "RoleType_Group",
}
var RoleType_value = map[string]int32{
	"RoleType_Default": 0,
	"RoleType_Game":    1,
	"RoleType_Pet":     2,
	"RoleType_Group":   3,
}

func (x RoleType) String() string {
	return proto.EnumName(RoleType_name, int32(x))
}
func (RoleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{3}
}

type RoleState int32

const (
	RoleState_RoleState_Invalid RoleState = 0
	RoleState_RoleState_Public  RoleState = 1
	RoleState_RoleState_Private RoleState = 2
)

var RoleState_name = map[int32]string{
	0: "RoleState_Invalid",
	1: "RoleState_Public",
	2: "RoleState_Private",
}
var RoleState_value = map[string]int32{
	"RoleState_Invalid": 0,
	"RoleState_Public":  1,
	"RoleState_Private": 2,
}

func (x RoleState) String() string {
	return proto.EnumName(RoleState_name, int32(x))
}
func (RoleState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{4}
}

type AuditResult int32

const (
	AuditResult_AuditResult_Review AuditResult = 0
	AuditResult_AuditResult_Pass   AuditResult = 1
	AuditResult_AuditResult_Reject AuditResult = 2
)

var AuditResult_name = map[int32]string{
	0: "AuditResult_Review",
	1: "AuditResult_Pass",
	2: "AuditResult_Reject",
}
var AuditResult_value = map[string]int32{
	"AuditResult_Review": 0,
	"AuditResult_Pass":   1,
	"AuditResult_Reject": 2,
}

func (x AuditResult) String() string {
	return proto.EnumName(AuditResult_name, int32(x))
}
func (AuditResult) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{5}
}

type TestReq_Type int32

const (
	TestReq_Type_Invalid                 TestReq_Type = 0
	TestReq_Type_TriggerRoleLikeStatPush TestReq_Type = 1
	TestReq_Type_CalBeforeRoleLikeCnt    TestReq_Type = 2
	TestReq_Type_SendMeMsg               TestReq_Type = 3
	TestReq_Type_PetSendTarotMsg         TestReq_Type = 4
)

var TestReq_Type_name = map[int32]string{
	0: "Type_Invalid",
	1: "Type_TriggerRoleLikeStatPush",
	2: "Type_CalBeforeRoleLikeCnt",
	3: "Type_SendMeMsg",
	4: "Type_PetSendTarotMsg",
}
var TestReq_Type_value = map[string]int32{
	"Type_Invalid":                 0,
	"Type_TriggerRoleLikeStatPush": 1,
	"Type_CalBeforeRoleLikeCnt":    2,
	"Type_SendMeMsg":               3,
	"Type_PetSendTarotMsg":         4,
}

func (x TestReq_Type) String() string {
	return proto.EnumName(TestReq_Type_name, int32(x))
}
func (TestReq_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{15, 0}
}

type RoleSwitchReq_Relationship int32

const (
	RoleSwitchReq_RelationshipUnknown RoleSwitchReq_Relationship = 0
	// 朋友
	RoleSwitchReq_RelationshipFriend RoleSwitchReq_Relationship = 1
	// 恋人
	RoleSwitchReq_RelationshipLover RoleSwitchReq_Relationship = 2
)

var RoleSwitchReq_Relationship_name = map[int32]string{
	0: "RelationshipUnknown",
	1: "RelationshipFriend",
	2: "RelationshipLover",
}
var RoleSwitchReq_Relationship_value = map[string]int32{
	"RelationshipUnknown": 0,
	"RelationshipFriend":  1,
	"RelationshipLover":   2,
}

func (x RoleSwitchReq_Relationship) String() string {
	return proto.EnumName(RoleSwitchReq_Relationship_name, int32(x))
}
func (RoleSwitchReq_Relationship) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{54, 0}
}

type RoleSwitchReq_ChattingStatus int32

const (
	RoleSwitchReq_Chatting RoleSwitchReq_ChattingStatus = 0
	RoleSwitchReq_Silence  RoleSwitchReq_ChattingStatus = 1
)

var RoleSwitchReq_ChattingStatus_name = map[int32]string{
	0: "Chatting",
	1: "Silence",
}
var RoleSwitchReq_ChattingStatus_value = map[string]int32{
	"Chatting": 0,
	"Silence":  1,
}

func (x RoleSwitchReq_ChattingStatus) String() string {
	return proto.EnumName(RoleSwitchReq_ChattingStatus_name, int32(x))
}
func (RoleSwitchReq_ChattingStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{54, 1}
}

type RoleSwitchReq_AIRole_Type int32

const (
	RoleSwitchReq_AIRole_Type_Default  RoleSwitchReq_AIRole_Type = 0
	RoleSwitchReq_AIRole_Type_NoChoose RoleSwitchReq_AIRole_Type = 1
)

var RoleSwitchReq_AIRole_Type_name = map[int32]string{
	0: "Type_Default",
	1: "Type_NoChoose",
}
var RoleSwitchReq_AIRole_Type_value = map[string]int32{
	"Type_Default":  0,
	"Type_NoChoose": 1,
}

func (x RoleSwitchReq_AIRole_Type) String() string {
	return proto.EnumName(RoleSwitchReq_AIRole_Type_name, int32(x))
}
func (RoleSwitchReq_AIRole_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{54, 0, 0}
}

type BatchGetRoleInteractiveReq struct {
	RoleIds              []uint32 `protobuf:"varint,1,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetRoleInteractiveReq) Reset()         { *m = BatchGetRoleInteractiveReq{} }
func (m *BatchGetRoleInteractiveReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetRoleInteractiveReq) ProtoMessage()    {}
func (*BatchGetRoleInteractiveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{0}
}
func (m *BatchGetRoleInteractiveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRoleInteractiveReq.Unmarshal(m, b)
}
func (m *BatchGetRoleInteractiveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRoleInteractiveReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetRoleInteractiveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRoleInteractiveReq.Merge(dst, src)
}
func (m *BatchGetRoleInteractiveReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetRoleInteractiveReq.Size(m)
}
func (m *BatchGetRoleInteractiveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRoleInteractiveReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRoleInteractiveReq proto.InternalMessageInfo

func (m *BatchGetRoleInteractiveReq) GetRoleIds() []uint32 {
	if m != nil {
		return m.RoleIds
	}
	return nil
}

type RoleInteractive struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Ext                  []byte   `protobuf:"bytes,2,opt,name=ext,proto3" json:"ext,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoleInteractive) Reset()         { *m = RoleInteractive{} }
func (m *RoleInteractive) String() string { return proto.CompactTextString(m) }
func (*RoleInteractive) ProtoMessage()    {}
func (*RoleInteractive) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{1}
}
func (m *RoleInteractive) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoleInteractive.Unmarshal(m, b)
}
func (m *RoleInteractive) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoleInteractive.Marshal(b, m, deterministic)
}
func (dst *RoleInteractive) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoleInteractive.Merge(dst, src)
}
func (m *RoleInteractive) XXX_Size() int {
	return xxx_messageInfo_RoleInteractive.Size(m)
}
func (m *RoleInteractive) XXX_DiscardUnknown() {
	xxx_messageInfo_RoleInteractive.DiscardUnknown(m)
}

var xxx_messageInfo_RoleInteractive proto.InternalMessageInfo

func (m *RoleInteractive) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *RoleInteractive) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

type BatchGetRoleInteractiveResp struct {
	RoleInteractiveList  []*RoleInteractive `protobuf:"bytes,1,rep,name=role_interactive_list,json=roleInteractiveList,proto3" json:"role_interactive_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchGetRoleInteractiveResp) Reset()         { *m = BatchGetRoleInteractiveResp{} }
func (m *BatchGetRoleInteractiveResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetRoleInteractiveResp) ProtoMessage()    {}
func (*BatchGetRoleInteractiveResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{2}
}
func (m *BatchGetRoleInteractiveResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRoleInteractiveResp.Unmarshal(m, b)
}
func (m *BatchGetRoleInteractiveResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRoleInteractiveResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetRoleInteractiveResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRoleInteractiveResp.Merge(dst, src)
}
func (m *BatchGetRoleInteractiveResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetRoleInteractiveResp.Size(m)
}
func (m *BatchGetRoleInteractiveResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRoleInteractiveResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRoleInteractiveResp proto.InternalMessageInfo

func (m *BatchGetRoleInteractiveResp) GetRoleInteractiveList() []*RoleInteractive {
	if m != nil {
		return m.RoleInteractiveList
	}
	return nil
}

type BatchGetRoleExtraReq struct {
	RoleIds              []uint32 `protobuf:"varint,1,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetRoleExtraReq) Reset()         { *m = BatchGetRoleExtraReq{} }
func (m *BatchGetRoleExtraReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetRoleExtraReq) ProtoMessage()    {}
func (*BatchGetRoleExtraReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{3}
}
func (m *BatchGetRoleExtraReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRoleExtraReq.Unmarshal(m, b)
}
func (m *BatchGetRoleExtraReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRoleExtraReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetRoleExtraReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRoleExtraReq.Merge(dst, src)
}
func (m *BatchGetRoleExtraReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetRoleExtraReq.Size(m)
}
func (m *BatchGetRoleExtraReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRoleExtraReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRoleExtraReq proto.InternalMessageInfo

func (m *BatchGetRoleExtraReq) GetRoleIds() []uint32 {
	if m != nil {
		return m.RoleIds
	}
	return nil
}

type RoleExtra struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Ext                  []byte   `protobuf:"bytes,2,opt,name=ext,proto3" json:"ext,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoleExtra) Reset()         { *m = RoleExtra{} }
func (m *RoleExtra) String() string { return proto.CompactTextString(m) }
func (*RoleExtra) ProtoMessage()    {}
func (*RoleExtra) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{4}
}
func (m *RoleExtra) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoleExtra.Unmarshal(m, b)
}
func (m *RoleExtra) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoleExtra.Marshal(b, m, deterministic)
}
func (dst *RoleExtra) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoleExtra.Merge(dst, src)
}
func (m *RoleExtra) XXX_Size() int {
	return xxx_messageInfo_RoleExtra.Size(m)
}
func (m *RoleExtra) XXX_DiscardUnknown() {
	xxx_messageInfo_RoleExtra.DiscardUnknown(m)
}

var xxx_messageInfo_RoleExtra proto.InternalMessageInfo

func (m *RoleExtra) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *RoleExtra) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

type BatchGetRoleExtraResp struct {
	RoleExtras           []*RoleExtra `protobuf:"bytes,1,rep,name=role_extras,json=roleExtras,proto3" json:"role_extras,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchGetRoleExtraResp) Reset()         { *m = BatchGetRoleExtraResp{} }
func (m *BatchGetRoleExtraResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetRoleExtraResp) ProtoMessage()    {}
func (*BatchGetRoleExtraResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{5}
}
func (m *BatchGetRoleExtraResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRoleExtraResp.Unmarshal(m, b)
}
func (m *BatchGetRoleExtraResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRoleExtraResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetRoleExtraResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRoleExtraResp.Merge(dst, src)
}
func (m *BatchGetRoleExtraResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetRoleExtraResp.Size(m)
}
func (m *BatchGetRoleExtraResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRoleExtraResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRoleExtraResp proto.InternalMessageInfo

func (m *BatchGetRoleExtraResp) GetRoleExtras() []*RoleExtra {
	if m != nil {
		return m.RoleExtras
	}
	return nil
}

type GetVoiceJsonReq struct {
	VoiceId              string   `protobuf:"bytes,1,opt,name=voice_id,json=voiceId,proto3" json:"voice_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVoiceJsonReq) Reset()         { *m = GetVoiceJsonReq{} }
func (m *GetVoiceJsonReq) String() string { return proto.CompactTextString(m) }
func (*GetVoiceJsonReq) ProtoMessage()    {}
func (*GetVoiceJsonReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{6}
}
func (m *GetVoiceJsonReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVoiceJsonReq.Unmarshal(m, b)
}
func (m *GetVoiceJsonReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVoiceJsonReq.Marshal(b, m, deterministic)
}
func (dst *GetVoiceJsonReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVoiceJsonReq.Merge(dst, src)
}
func (m *GetVoiceJsonReq) XXX_Size() int {
	return xxx_messageInfo_GetVoiceJsonReq.Size(m)
}
func (m *GetVoiceJsonReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVoiceJsonReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVoiceJsonReq proto.InternalMessageInfo

func (m *GetVoiceJsonReq) GetVoiceId() string {
	if m != nil {
		return m.VoiceId
	}
	return ""
}

type GetVoiceJsonResp struct {
	VoiceJson            string   `protobuf:"bytes,1,opt,name=voice_json,json=voiceJson,proto3" json:"voice_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVoiceJsonResp) Reset()         { *m = GetVoiceJsonResp{} }
func (m *GetVoiceJsonResp) String() string { return proto.CompactTextString(m) }
func (*GetVoiceJsonResp) ProtoMessage()    {}
func (*GetVoiceJsonResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{7}
}
func (m *GetVoiceJsonResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVoiceJsonResp.Unmarshal(m, b)
}
func (m *GetVoiceJsonResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVoiceJsonResp.Marshal(b, m, deterministic)
}
func (dst *GetVoiceJsonResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVoiceJsonResp.Merge(dst, src)
}
func (m *GetVoiceJsonResp) XXX_Size() int {
	return xxx_messageInfo_GetVoiceJsonResp.Size(m)
}
func (m *GetVoiceJsonResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVoiceJsonResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVoiceJsonResp proto.InternalMessageInfo

func (m *GetVoiceJsonResp) GetVoiceJson() string {
	if m != nil {
		return m.VoiceJson
	}
	return ""
}

type GetReportRoleReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReportRoleReq) Reset()         { *m = GetReportRoleReq{} }
func (m *GetReportRoleReq) String() string { return proto.CompactTextString(m) }
func (*GetReportRoleReq) ProtoMessage()    {}
func (*GetReportRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{8}
}
func (m *GetReportRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReportRoleReq.Unmarshal(m, b)
}
func (m *GetReportRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReportRoleReq.Marshal(b, m, deterministic)
}
func (dst *GetReportRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReportRoleReq.Merge(dst, src)
}
func (m *GetReportRoleReq) XXX_Size() int {
	return xxx_messageInfo_GetReportRoleReq.Size(m)
}
func (m *GetReportRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReportRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetReportRoleReq proto.InternalMessageInfo

func (m *GetReportRoleReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetReportRoleResp struct {
	RoleIds              []uint32 `protobuf:"varint,1,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReportRoleResp) Reset()         { *m = GetReportRoleResp{} }
func (m *GetReportRoleResp) String() string { return proto.CompactTextString(m) }
func (*GetReportRoleResp) ProtoMessage()    {}
func (*GetReportRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{9}
}
func (m *GetReportRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReportRoleResp.Unmarshal(m, b)
}
func (m *GetReportRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReportRoleResp.Marshal(b, m, deterministic)
}
func (dst *GetReportRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReportRoleResp.Merge(dst, src)
}
func (m *GetReportRoleResp) XXX_Size() int {
	return xxx_messageInfo_GetReportRoleResp.Size(m)
}
func (m *GetReportRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReportRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetReportRoleResp proto.InternalMessageInfo

func (m *GetReportRoleResp) GetRoleIds() []uint32 {
	if m != nil {
		return m.RoleIds
	}
	return nil
}

type ReportRoleReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportRoleReq) Reset()         { *m = ReportRoleReq{} }
func (m *ReportRoleReq) String() string { return proto.CompactTextString(m) }
func (*ReportRoleReq) ProtoMessage()    {}
func (*ReportRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{10}
}
func (m *ReportRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportRoleReq.Unmarshal(m, b)
}
func (m *ReportRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportRoleReq.Marshal(b, m, deterministic)
}
func (dst *ReportRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportRoleReq.Merge(dst, src)
}
func (m *ReportRoleReq) XXX_Size() int {
	return xxx_messageInfo_ReportRoleReq.Size(m)
}
func (m *ReportRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportRoleReq proto.InternalMessageInfo

func (m *ReportRoleReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportRoleReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type ReportRoleResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportRoleResp) Reset()         { *m = ReportRoleResp{} }
func (m *ReportRoleResp) String() string { return proto.CompactTextString(m) }
func (*ReportRoleResp) ProtoMessage()    {}
func (*ReportRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{11}
}
func (m *ReportRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportRoleResp.Unmarshal(m, b)
}
func (m *ReportRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportRoleResp.Marshal(b, m, deterministic)
}
func (dst *ReportRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportRoleResp.Merge(dst, src)
}
func (m *ReportRoleResp) XXX_Size() int {
	return xxx_messageInfo_ReportRoleResp.Size(m)
}
func (m *ReportRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportRoleResp proto.InternalMessageInfo

type GetRoleFeatureReq struct {
	RoleIds              []uint32 `protobuf:"varint,1,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRoleFeatureReq) Reset()         { *m = GetRoleFeatureReq{} }
func (m *GetRoleFeatureReq) String() string { return proto.CompactTextString(m) }
func (*GetRoleFeatureReq) ProtoMessage()    {}
func (*GetRoleFeatureReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{12}
}
func (m *GetRoleFeatureReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoleFeatureReq.Unmarshal(m, b)
}
func (m *GetRoleFeatureReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoleFeatureReq.Marshal(b, m, deterministic)
}
func (dst *GetRoleFeatureReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoleFeatureReq.Merge(dst, src)
}
func (m *GetRoleFeatureReq) XXX_Size() int {
	return xxx_messageInfo_GetRoleFeatureReq.Size(m)
}
func (m *GetRoleFeatureReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoleFeatureReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoleFeatureReq proto.InternalMessageInfo

func (m *GetRoleFeatureReq) GetRoleIds() []uint32 {
	if m != nil {
		return m.RoleIds
	}
	return nil
}

// 复聊权数 点赞权数 人均聊天句数权数  曝光点击率 角色的总的聊过人数
type RoleFeature struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	RepeatChatWeight     float32  `protobuf:"fixed32,2,opt,name=repeat_chat_weight,json=repeatChatWeight,proto3" json:"repeat_chat_weight,omitempty"`
	LikeWeight           float32  `protobuf:"fixed32,3,opt,name=like_weight,json=likeWeight,proto3" json:"like_weight,omitempty"`
	AvgChatWeight        float32  `protobuf:"fixed32,4,opt,name=avg_chat_weight,json=avgChatWeight,proto3" json:"avg_chat_weight,omitempty"`
	ExposureClickRate    float32  `protobuf:"fixed32,5,opt,name=exposure_click_rate,json=exposureClickRate,proto3" json:"exposure_click_rate,omitempty"`
	TotalChatUser        uint32   `protobuf:"varint,6,opt,name=total_chat_user,json=totalChatUser,proto3" json:"total_chat_user,omitempty"`
	FiveChattingUser     uint32   `protobuf:"varint,7,opt,name=five_chatting_user,json=fiveChattingUser,proto3" json:"five_chatting_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoleFeature) Reset()         { *m = RoleFeature{} }
func (m *RoleFeature) String() string { return proto.CompactTextString(m) }
func (*RoleFeature) ProtoMessage()    {}
func (*RoleFeature) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{13}
}
func (m *RoleFeature) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoleFeature.Unmarshal(m, b)
}
func (m *RoleFeature) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoleFeature.Marshal(b, m, deterministic)
}
func (dst *RoleFeature) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoleFeature.Merge(dst, src)
}
func (m *RoleFeature) XXX_Size() int {
	return xxx_messageInfo_RoleFeature.Size(m)
}
func (m *RoleFeature) XXX_DiscardUnknown() {
	xxx_messageInfo_RoleFeature.DiscardUnknown(m)
}

var xxx_messageInfo_RoleFeature proto.InternalMessageInfo

func (m *RoleFeature) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *RoleFeature) GetRepeatChatWeight() float32 {
	if m != nil {
		return m.RepeatChatWeight
	}
	return 0
}

func (m *RoleFeature) GetLikeWeight() float32 {
	if m != nil {
		return m.LikeWeight
	}
	return 0
}

func (m *RoleFeature) GetAvgChatWeight() float32 {
	if m != nil {
		return m.AvgChatWeight
	}
	return 0
}

func (m *RoleFeature) GetExposureClickRate() float32 {
	if m != nil {
		return m.ExposureClickRate
	}
	return 0
}

func (m *RoleFeature) GetTotalChatUser() uint32 {
	if m != nil {
		return m.TotalChatUser
	}
	return 0
}

func (m *RoleFeature) GetFiveChattingUser() uint32 {
	if m != nil {
		return m.FiveChattingUser
	}
	return 0
}

type GetRoleFeatureResp struct {
	RoleFeatures         []*RoleFeature `protobuf:"bytes,1,rep,name=role_features,json=roleFeatures,proto3" json:"role_features,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetRoleFeatureResp) Reset()         { *m = GetRoleFeatureResp{} }
func (m *GetRoleFeatureResp) String() string { return proto.CompactTextString(m) }
func (*GetRoleFeatureResp) ProtoMessage()    {}
func (*GetRoleFeatureResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{14}
}
func (m *GetRoleFeatureResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoleFeatureResp.Unmarshal(m, b)
}
func (m *GetRoleFeatureResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoleFeatureResp.Marshal(b, m, deterministic)
}
func (dst *GetRoleFeatureResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoleFeatureResp.Merge(dst, src)
}
func (m *GetRoleFeatureResp) XXX_Size() int {
	return xxx_messageInfo_GetRoleFeatureResp.Size(m)
}
func (m *GetRoleFeatureResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoleFeatureResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoleFeatureResp proto.InternalMessageInfo

func (m *GetRoleFeatureResp) GetRoleFeatures() []*RoleFeature {
	if m != nil {
		return m.RoleFeatures
	}
	return nil
}

type TestReq struct {
	Type                  TestReq_Type `protobuf:"varint,1,opt,name=type,proto3,enum=rcmd.business_ai_partner.TestReq_Type" json:"type,omitempty"`
	RoleLikeStatTriggerTs int64        `protobuf:"varint,2,opt,name=role_like_stat_trigger_ts,json=roleLikeStatTriggerTs,proto3" json:"role_like_stat_trigger_ts,omitempty"`
	CalDate               string       `protobuf:"bytes,3,opt,name=cal_date,json=calDate,proto3" json:"cal_date,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}     `json:"-"`
	XXX_unrecognized      []byte       `json:"-"`
	XXX_sizecache         int32        `json:"-"`
}

func (m *TestReq) Reset()         { *m = TestReq{} }
func (m *TestReq) String() string { return proto.CompactTextString(m) }
func (*TestReq) ProtoMessage()    {}
func (*TestReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{15}
}
func (m *TestReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestReq.Unmarshal(m, b)
}
func (m *TestReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestReq.Marshal(b, m, deterministic)
}
func (dst *TestReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestReq.Merge(dst, src)
}
func (m *TestReq) XXX_Size() int {
	return xxx_messageInfo_TestReq.Size(m)
}
func (m *TestReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestReq proto.InternalMessageInfo

func (m *TestReq) GetType() TestReq_Type {
	if m != nil {
		return m.Type
	}
	return TestReq_Type_Invalid
}

func (m *TestReq) GetRoleLikeStatTriggerTs() int64 {
	if m != nil {
		return m.RoleLikeStatTriggerTs
	}
	return 0
}

func (m *TestReq) GetCalDate() string {
	if m != nil {
		return m.CalDate
	}
	return ""
}

type TestResp struct {
	Data                 string   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestResp) Reset()         { *m = TestResp{} }
func (m *TestResp) String() string { return proto.CompactTextString(m) }
func (*TestResp) ProtoMessage()    {}
func (*TestResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{16}
}
func (m *TestResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestResp.Unmarshal(m, b)
}
func (m *TestResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestResp.Marshal(b, m, deterministic)
}
func (dst *TestResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestResp.Merge(dst, src)
}
func (m *TestResp) XXX_Size() int {
	return xxx_messageInfo_TestResp.Size(m)
}
func (m *TestResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestResp proto.InternalMessageInfo

func (m *TestResp) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

type GetRoleLikeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleId               uint32   `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRoleLikeReq) Reset()         { *m = GetRoleLikeReq{} }
func (m *GetRoleLikeReq) String() string { return proto.CompactTextString(m) }
func (*GetRoleLikeReq) ProtoMessage()    {}
func (*GetRoleLikeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{17}
}
func (m *GetRoleLikeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoleLikeReq.Unmarshal(m, b)
}
func (m *GetRoleLikeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoleLikeReq.Marshal(b, m, deterministic)
}
func (dst *GetRoleLikeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoleLikeReq.Merge(dst, src)
}
func (m *GetRoleLikeReq) XXX_Size() int {
	return xxx_messageInfo_GetRoleLikeReq.Size(m)
}
func (m *GetRoleLikeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoleLikeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoleLikeReq proto.InternalMessageInfo

func (m *GetRoleLikeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRoleLikeReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetRoleLikeResp struct {
	Action               RoleLikeAction `protobuf:"varint,1,opt,name=action,proto3,enum=rcmd.business_ai_partner.RoleLikeAction" json:"action,omitempty"`
	ActionTs             int64          `protobuf:"varint,2,opt,name=action_ts,json=actionTs,proto3" json:"action_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetRoleLikeResp) Reset()         { *m = GetRoleLikeResp{} }
func (m *GetRoleLikeResp) String() string { return proto.CompactTextString(m) }
func (*GetRoleLikeResp) ProtoMessage()    {}
func (*GetRoleLikeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{18}
}
func (m *GetRoleLikeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoleLikeResp.Unmarshal(m, b)
}
func (m *GetRoleLikeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoleLikeResp.Marshal(b, m, deterministic)
}
func (dst *GetRoleLikeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoleLikeResp.Merge(dst, src)
}
func (m *GetRoleLikeResp) XXX_Size() int {
	return xxx_messageInfo_GetRoleLikeResp.Size(m)
}
func (m *GetRoleLikeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoleLikeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoleLikeResp proto.InternalMessageInfo

func (m *GetRoleLikeResp) GetAction() RoleLikeAction {
	if m != nil {
		return m.Action
	}
	return RoleLikeAction_NoLIKE
}

func (m *GetRoleLikeResp) GetActionTs() int64 {
	if m != nil {
		return m.ActionTs
	}
	return 0
}

type CheckRolesExistReq struct {
	RoleIds              []uint32 `protobuf:"varint,1,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckRolesExistReq) Reset()         { *m = CheckRolesExistReq{} }
func (m *CheckRolesExistReq) String() string { return proto.CompactTextString(m) }
func (*CheckRolesExistReq) ProtoMessage()    {}
func (*CheckRolesExistReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{19}
}
func (m *CheckRolesExistReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckRolesExistReq.Unmarshal(m, b)
}
func (m *CheckRolesExistReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckRolesExistReq.Marshal(b, m, deterministic)
}
func (dst *CheckRolesExistReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckRolesExistReq.Merge(dst, src)
}
func (m *CheckRolesExistReq) XXX_Size() int {
	return xxx_messageInfo_CheckRolesExistReq.Size(m)
}
func (m *CheckRolesExistReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckRolesExistReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckRolesExistReq proto.InternalMessageInfo

func (m *CheckRolesExistReq) GetRoleIds() []uint32 {
	if m != nil {
		return m.RoleIds
	}
	return nil
}

type CheckRolesExistResp struct {
	ExistRoleIds         []uint32 `protobuf:"varint,1,rep,packed,name=exist_role_ids,json=existRoleIds,proto3" json:"exist_role_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckRolesExistResp) Reset()         { *m = CheckRolesExistResp{} }
func (m *CheckRolesExistResp) String() string { return proto.CompactTextString(m) }
func (*CheckRolesExistResp) ProtoMessage()    {}
func (*CheckRolesExistResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{20}
}
func (m *CheckRolesExistResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckRolesExistResp.Unmarshal(m, b)
}
func (m *CheckRolesExistResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckRolesExistResp.Marshal(b, m, deterministic)
}
func (dst *CheckRolesExistResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckRolesExistResp.Merge(dst, src)
}
func (m *CheckRolesExistResp) XXX_Size() int {
	return xxx_messageInfo_CheckRolesExistResp.Size(m)
}
func (m *CheckRolesExistResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckRolesExistResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckRolesExistResp proto.InternalMessageInfo

func (m *CheckRolesExistResp) GetExistRoleIds() []uint32 {
	if m != nil {
		return m.ExistRoleIds
	}
	return nil
}

type GetGameInfoReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameInfoReq) Reset()         { *m = GetGameInfoReq{} }
func (m *GetGameInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetGameInfoReq) ProtoMessage()    {}
func (*GetGameInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{21}
}
func (m *GetGameInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameInfoReq.Unmarshal(m, b)
}
func (m *GetGameInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetGameInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameInfoReq.Merge(dst, src)
}
func (m *GetGameInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetGameInfoReq.Size(m)
}
func (m *GetGameInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameInfoReq proto.InternalMessageInfo

func (m *GetGameInfoReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

// GetGameInfoResp
// Id        string `json:"id,omitempty"`
// Name      string `json:"name,omitempty"`
// Prompt    string `json:"prompt,omitempty"`
// Icon      string `json:"icon,omitempty"`
// Greeting  string `json:"greeting,omitempty"`
// BGM       string `json:"bgm,omitempty"`
// BG        string `json:"bg,omitempty"`
// SceneDesc string `json:"scene_desc,omitempty"`
type GetGameInfoResp struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Prompt               string   `protobuf:"bytes,3,opt,name=prompt,proto3" json:"prompt,omitempty"`
	Icon                 string   `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	Greeting             string   `protobuf:"bytes,5,opt,name=greeting,proto3" json:"greeting,omitempty"`
	Bgm                  string   `protobuf:"bytes,6,opt,name=bgm,proto3" json:"bgm,omitempty"`
	Bg                   string   `protobuf:"bytes,7,opt,name=bg,proto3" json:"bg,omitempty"`
	SceneDesc            string   `protobuf:"bytes,8,opt,name=scene_desc,json=sceneDesc,proto3" json:"scene_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameInfoResp) Reset()         { *m = GetGameInfoResp{} }
func (m *GetGameInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGameInfoResp) ProtoMessage()    {}
func (*GetGameInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{22}
}
func (m *GetGameInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameInfoResp.Unmarshal(m, b)
}
func (m *GetGameInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetGameInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameInfoResp.Merge(dst, src)
}
func (m *GetGameInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetGameInfoResp.Size(m)
}
func (m *GetGameInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameInfoResp proto.InternalMessageInfo

func (m *GetGameInfoResp) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetGameInfoResp) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetGameInfoResp) GetPrompt() string {
	if m != nil {
		return m.Prompt
	}
	return ""
}

func (m *GetGameInfoResp) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GetGameInfoResp) GetGreeting() string {
	if m != nil {
		return m.Greeting
	}
	return ""
}

func (m *GetGameInfoResp) GetBgm() string {
	if m != nil {
		return m.Bgm
	}
	return ""
}

func (m *GetGameInfoResp) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *GetGameInfoResp) GetSceneDesc() string {
	if m != nil {
		return m.SceneDesc
	}
	return ""
}

type GetInsertPosRolesReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInsertPosRolesReq) Reset()         { *m = GetInsertPosRolesReq{} }
func (m *GetInsertPosRolesReq) String() string { return proto.CompactTextString(m) }
func (*GetInsertPosRolesReq) ProtoMessage()    {}
func (*GetInsertPosRolesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{23}
}
func (m *GetInsertPosRolesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInsertPosRolesReq.Unmarshal(m, b)
}
func (m *GetInsertPosRolesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInsertPosRolesReq.Marshal(b, m, deterministic)
}
func (dst *GetInsertPosRolesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInsertPosRolesReq.Merge(dst, src)
}
func (m *GetInsertPosRolesReq) XXX_Size() int {
	return xxx_messageInfo_GetInsertPosRolesReq.Size(m)
}
func (m *GetInsertPosRolesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInsertPosRolesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetInsertPosRolesReq proto.InternalMessageInfo

type InsertPosRole struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CategoryId           string   `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	InsertPos            uint32   `protobuf:"varint,3,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	Exposed              bool     `protobuf:"varint,4,opt,name=exposed,proto3" json:"exposed,omitempty"`
	State                uint32   `protobuf:"varint,5,opt,name=state,proto3" json:"state,omitempty"`
	AuditResult          uint32   `protobuf:"varint,6,opt,name=audit_result,json=auditResult,proto3" json:"audit_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsertPosRole) Reset()         { *m = InsertPosRole{} }
func (m *InsertPosRole) String() string { return proto.CompactTextString(m) }
func (*InsertPosRole) ProtoMessage()    {}
func (*InsertPosRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{24}
}
func (m *InsertPosRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertPosRole.Unmarshal(m, b)
}
func (m *InsertPosRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertPosRole.Marshal(b, m, deterministic)
}
func (dst *InsertPosRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertPosRole.Merge(dst, src)
}
func (m *InsertPosRole) XXX_Size() int {
	return xxx_messageInfo_InsertPosRole.Size(m)
}
func (m *InsertPosRole) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertPosRole.DiscardUnknown(m)
}

var xxx_messageInfo_InsertPosRole proto.InternalMessageInfo

func (m *InsertPosRole) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *InsertPosRole) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *InsertPosRole) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *InsertPosRole) GetExposed() bool {
	if m != nil {
		return m.Exposed
	}
	return false
}

func (m *InsertPosRole) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *InsertPosRole) GetAuditResult() uint32 {
	if m != nil {
		return m.AuditResult
	}
	return 0
}

type GetInsertPosRolesResp struct {
	InsertPosRoles       []*InsertPosRole `protobuf:"bytes,1,rep,name=insert_pos_roles,json=insertPosRoles,proto3" json:"insert_pos_roles,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetInsertPosRolesResp) Reset()         { *m = GetInsertPosRolesResp{} }
func (m *GetInsertPosRolesResp) String() string { return proto.CompactTextString(m) }
func (*GetInsertPosRolesResp) ProtoMessage()    {}
func (*GetInsertPosRolesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{25}
}
func (m *GetInsertPosRolesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInsertPosRolesResp.Unmarshal(m, b)
}
func (m *GetInsertPosRolesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInsertPosRolesResp.Marshal(b, m, deterministic)
}
func (dst *GetInsertPosRolesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInsertPosRolesResp.Merge(dst, src)
}
func (m *GetInsertPosRolesResp) XXX_Size() int {
	return xxx_messageInfo_GetInsertPosRolesResp.Size(m)
}
func (m *GetInsertPosRolesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInsertPosRolesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetInsertPosRolesResp proto.InternalMessageInfo

func (m *GetInsertPosRolesResp) GetInsertPosRoles() []*InsertPosRole {
	if m != nil {
		return m.InsertPosRoles
	}
	return nil
}

type GetRoleReq struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRoleReq) Reset()         { *m = GetRoleReq{} }
func (m *GetRoleReq) String() string { return proto.CompactTextString(m) }
func (*GetRoleReq) ProtoMessage()    {}
func (*GetRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{26}
}
func (m *GetRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoleReq.Unmarshal(m, b)
}
func (m *GetRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoleReq.Marshal(b, m, deterministic)
}
func (dst *GetRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoleReq.Merge(dst, src)
}
func (m *GetRoleReq) XXX_Size() int {
	return xxx_messageInfo_GetRoleReq.Size(m)
}
func (m *GetRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoleReq proto.InternalMessageInfo

func (m *GetRoleReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type RoleData struct {
	Id                   uint32         `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string         `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Source               RoleSource     `protobuf:"varint,3,opt,name=source,proto3,enum=rcmd.business_ai_partner.RoleSource" json:"source,omitempty"`
	Uid                  uint32         `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Type                 RoleType       `protobuf:"varint,5,opt,name=type,proto3,enum=rcmd.business_ai_partner.RoleType" json:"type,omitempty"`
	State                RoleState      `protobuf:"varint,6,opt,name=state,proto3,enum=rcmd.business_ai_partner.RoleState" json:"state,omitempty"`
	Sex                  uint32         `protobuf:"varint,7,opt,name=sex,proto3" json:"sex,omitempty"`
	BackgroundImage      string         `protobuf:"bytes,8,opt,name=background_image,json=backgroundImage,proto3" json:"background_image,omitempty"`
	Avatar               string         `protobuf:"bytes,9,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Character            string         `protobuf:"bytes,10,opt,name=character,proto3" json:"character,omitempty"`
	PromptId             string         `protobuf:"bytes,11,opt,name=prompt_id,json=promptId,proto3" json:"prompt_id,omitempty"`
	PromptVersion        string         `protobuf:"bytes,12,opt,name=prompt_version,json=promptVersion,proto3" json:"prompt_version,omitempty"`
	EnableRcmdReply      bool           `protobuf:"varint,13,opt,name=enable_rcmd_reply,json=enableRcmdReply,proto3" json:"enable_rcmd_reply,omitempty"`
	CategoryId           string         `protobuf:"bytes,14,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Tags                 []string       `protobuf:"bytes,15,rep,name=tags,proto3" json:"tags,omitempty"`
	VoiceId              string         `protobuf:"bytes,16,opt,name=voice_id,json=voiceId,proto3" json:"voice_id,omitempty"`
	NthTextList          []*RoleNthText `protobuf:"bytes,17,rep,name=nth_text_list,json=nthTextList,proto3" json:"nth_text_list,omitempty"`
	InsertPos            uint32         `protobuf:"varint,18,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	VoiceJson            string         `protobuf:"bytes,19,opt,name=voice_json,json=voiceJson,proto3" json:"voice_json,omitempty"`
	StoryId              string         `protobuf:"bytes,20,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	Prologue             string         `protobuf:"bytes,21,opt,name=prologue,proto3" json:"prologue,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *RoleData) Reset()         { *m = RoleData{} }
func (m *RoleData) String() string { return proto.CompactTextString(m) }
func (*RoleData) ProtoMessage()    {}
func (*RoleData) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{27}
}
func (m *RoleData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoleData.Unmarshal(m, b)
}
func (m *RoleData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoleData.Marshal(b, m, deterministic)
}
func (dst *RoleData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoleData.Merge(dst, src)
}
func (m *RoleData) XXX_Size() int {
	return xxx_messageInfo_RoleData.Size(m)
}
func (m *RoleData) XXX_DiscardUnknown() {
	xxx_messageInfo_RoleData.DiscardUnknown(m)
}

var xxx_messageInfo_RoleData proto.InternalMessageInfo

func (m *RoleData) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RoleData) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RoleData) GetSource() RoleSource {
	if m != nil {
		return m.Source
	}
	return RoleSource_RoleSource_Invalid
}

func (m *RoleData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RoleData) GetType() RoleType {
	if m != nil {
		return m.Type
	}
	return RoleType_RoleType_Default
}

func (m *RoleData) GetState() RoleState {
	if m != nil {
		return m.State
	}
	return RoleState_RoleState_Invalid
}

func (m *RoleData) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *RoleData) GetBackgroundImage() string {
	if m != nil {
		return m.BackgroundImage
	}
	return ""
}

func (m *RoleData) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *RoleData) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *RoleData) GetPromptId() string {
	if m != nil {
		return m.PromptId
	}
	return ""
}

func (m *RoleData) GetPromptVersion() string {
	if m != nil {
		return m.PromptVersion
	}
	return ""
}

func (m *RoleData) GetEnableRcmdReply() bool {
	if m != nil {
		return m.EnableRcmdReply
	}
	return false
}

func (m *RoleData) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *RoleData) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *RoleData) GetVoiceId() string {
	if m != nil {
		return m.VoiceId
	}
	return ""
}

func (m *RoleData) GetNthTextList() []*RoleNthText {
	if m != nil {
		return m.NthTextList
	}
	return nil
}

func (m *RoleData) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *RoleData) GetVoiceJson() string {
	if m != nil {
		return m.VoiceJson
	}
	return ""
}

func (m *RoleData) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

func (m *RoleData) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

type GetRoleResp struct {
	Role                 *RoleData `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	VoiceJson            string    `protobuf:"bytes,2,opt,name=voice_json,json=voiceJson,proto3" json:"voice_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetRoleResp) Reset()         { *m = GetRoleResp{} }
func (m *GetRoleResp) String() string { return proto.CompactTextString(m) }
func (*GetRoleResp) ProtoMessage()    {}
func (*GetRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{28}
}
func (m *GetRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoleResp.Unmarshal(m, b)
}
func (m *GetRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoleResp.Marshal(b, m, deterministic)
}
func (dst *GetRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoleResp.Merge(dst, src)
}
func (m *GetRoleResp) XXX_Size() int {
	return xxx_messageInfo_GetRoleResp.Size(m)
}
func (m *GetRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoleResp proto.InternalMessageInfo

func (m *GetRoleResp) GetRole() *RoleData {
	if m != nil {
		return m.Role
	}
	return nil
}

func (m *GetRoleResp) GetVoiceJson() string {
	if m != nil {
		return m.VoiceJson
	}
	return ""
}

type BatchGetPartnerInfoReq struct {
	PartnerIds           []uint32 `protobuf:"varint,1,rep,packed,name=partner_ids,json=partnerIds,proto3" json:"partner_ids,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetPartnerInfoReq) Reset()         { *m = BatchGetPartnerInfoReq{} }
func (m *BatchGetPartnerInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetPartnerInfoReq) ProtoMessage()    {}
func (*BatchGetPartnerInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{29}
}
func (m *BatchGetPartnerInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPartnerInfoReq.Unmarshal(m, b)
}
func (m *BatchGetPartnerInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPartnerInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetPartnerInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPartnerInfoReq.Merge(dst, src)
}
func (m *BatchGetPartnerInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetPartnerInfoReq.Size(m)
}
func (m *BatchGetPartnerInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPartnerInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPartnerInfoReq proto.InternalMessageInfo

func (m *BatchGetPartnerInfoReq) GetPartnerIds() []uint32 {
	if m != nil {
		return m.PartnerIds
	}
	return nil
}

func (m *BatchGetPartnerInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type BatchGetPartnerInfoResp struct {
	PartnerInfoList      []*PartnerInfo `protobuf:"bytes,1,rep,name=partner_info_list,json=partnerInfoList,proto3" json:"partner_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchGetPartnerInfoResp) Reset()         { *m = BatchGetPartnerInfoResp{} }
func (m *BatchGetPartnerInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetPartnerInfoResp) ProtoMessage()    {}
func (*BatchGetPartnerInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{30}
}
func (m *BatchGetPartnerInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPartnerInfoResp.Unmarshal(m, b)
}
func (m *BatchGetPartnerInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPartnerInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetPartnerInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPartnerInfoResp.Merge(dst, src)
}
func (m *BatchGetPartnerInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetPartnerInfoResp.Size(m)
}
func (m *BatchGetPartnerInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPartnerInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPartnerInfoResp proto.InternalMessageInfo

func (m *BatchGetPartnerInfoResp) GetPartnerInfoList() []*PartnerInfo {
	if m != nil {
		return m.PartnerInfoList
	}
	return nil
}

type CustomVoice struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Weight               float32  `protobuf:"fixed32,2,opt,name=weight,proto3" json:"weight,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomVoice) Reset()         { *m = CustomVoice{} }
func (m *CustomVoice) String() string { return proto.CompactTextString(m) }
func (*CustomVoice) ProtoMessage()    {}
func (*CustomVoice) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{31}
}
func (m *CustomVoice) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomVoice.Unmarshal(m, b)
}
func (m *CustomVoice) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomVoice.Marshal(b, m, deterministic)
}
func (dst *CustomVoice) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomVoice.Merge(dst, src)
}
func (m *CustomVoice) XXX_Size() int {
	return xxx_messageInfo_CustomVoice.Size(m)
}
func (m *CustomVoice) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomVoice.DiscardUnknown(m)
}

var xxx_messageInfo_CustomVoice proto.InternalMessageInfo

func (m *CustomVoice) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CustomVoice) GetWeight() float32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

type SetAIPartnerInfoReq struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32         `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string         `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	CallName             string         `protobuf:"bytes,4,opt,name=call_name,json=callName,proto3" json:"call_name,omitempty"`
	RoleId               uint32         `protobuf:"varint,6,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	CreateType           uint32         `protobuf:"varint,7,opt,name=create_type,json=createType,proto3" json:"create_type,omitempty"`
	Status               uint32         `protobuf:"varint,8,opt,name=status,proto3" json:"status,omitempty"`
	RoleType             RoleType       `protobuf:"varint,9,opt,name=role_type,json=roleType,proto3,enum=rcmd.business_ai_partner.RoleType" json:"role_type,omitempty"`
	RoleSex              uint32         `protobuf:"varint,10,opt,name=role_sex,json=roleSex,proto3" json:"role_sex,omitempty"`
	RoleStyle            string         `protobuf:"bytes,11,opt,name=role_style,json=roleStyle,proto3" json:"role_style,omitempty"`
	Source               PartnerSource  `protobuf:"varint,12,opt,name=source,proto3,enum=rcmd.business_ai_partner.PartnerSource" json:"source,omitempty"`
	CustomVoices         []*CustomVoice `protobuf:"bytes,13,rep,name=custom_voices,json=customVoices,proto3" json:"custom_voices,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SetAIPartnerInfoReq) Reset()         { *m = SetAIPartnerInfoReq{} }
func (m *SetAIPartnerInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetAIPartnerInfoReq) ProtoMessage()    {}
func (*SetAIPartnerInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{32}
}
func (m *SetAIPartnerInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAIPartnerInfoReq.Unmarshal(m, b)
}
func (m *SetAIPartnerInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAIPartnerInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetAIPartnerInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAIPartnerInfoReq.Merge(dst, src)
}
func (m *SetAIPartnerInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetAIPartnerInfoReq.Size(m)
}
func (m *SetAIPartnerInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAIPartnerInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetAIPartnerInfoReq proto.InternalMessageInfo

func (m *SetAIPartnerInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetAIPartnerInfoReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SetAIPartnerInfoReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SetAIPartnerInfoReq) GetCallName() string {
	if m != nil {
		return m.CallName
	}
	return ""
}

func (m *SetAIPartnerInfoReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *SetAIPartnerInfoReq) GetCreateType() uint32 {
	if m != nil {
		return m.CreateType
	}
	return 0
}

func (m *SetAIPartnerInfoReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SetAIPartnerInfoReq) GetRoleType() RoleType {
	if m != nil {
		return m.RoleType
	}
	return RoleType_RoleType_Default
}

func (m *SetAIPartnerInfoReq) GetRoleSex() uint32 {
	if m != nil {
		return m.RoleSex
	}
	return 0
}

func (m *SetAIPartnerInfoReq) GetRoleStyle() string {
	if m != nil {
		return m.RoleStyle
	}
	return ""
}

func (m *SetAIPartnerInfoReq) GetSource() PartnerSource {
	if m != nil {
		return m.Source
	}
	return PartnerSource_SourceUser
}

func (m *SetAIPartnerInfoReq) GetCustomVoices() []*CustomVoice {
	if m != nil {
		return m.CustomVoices
	}
	return nil
}

type SetAIPartnerInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAIPartnerInfoResp) Reset()         { *m = SetAIPartnerInfoResp{} }
func (m *SetAIPartnerInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetAIPartnerInfoResp) ProtoMessage()    {}
func (*SetAIPartnerInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{33}
}
func (m *SetAIPartnerInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAIPartnerInfoResp.Unmarshal(m, b)
}
func (m *SetAIPartnerInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAIPartnerInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetAIPartnerInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAIPartnerInfoResp.Merge(dst, src)
}
func (m *SetAIPartnerInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetAIPartnerInfoResp.Size(m)
}
func (m *SetAIPartnerInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAIPartnerInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetAIPartnerInfoResp proto.InternalMessageInfo

// 角色进入触发文案
type RoleNthText struct {
	// 第n次
	Seq uint32 `protobuf:"varint,1,opt,name=seq,proto3" json:"seq,omitempty"`
	// 第n次进入触发文案
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoleNthText) Reset()         { *m = RoleNthText{} }
func (m *RoleNthText) String() string { return proto.CompactTextString(m) }
func (*RoleNthText) ProtoMessage()    {}
func (*RoleNthText) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{34}
}
func (m *RoleNthText) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoleNthText.Unmarshal(m, b)
}
func (m *RoleNthText) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoleNthText.Marshal(b, m, deterministic)
}
func (dst *RoleNthText) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoleNthText.Merge(dst, src)
}
func (m *RoleNthText) XXX_Size() int {
	return xxx_messageInfo_RoleNthText.Size(m)
}
func (m *RoleNthText) XXX_DiscardUnknown() {
	xxx_messageInfo_RoleNthText.DiscardUnknown(m)
}

var xxx_messageInfo_RoleNthText proto.InternalMessageInfo

func (m *RoleNthText) GetSeq() uint32 {
	if m != nil {
		return m.Seq
	}
	return 0
}

func (m *RoleNthText) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type RoleInfo struct {
	Name                 string                    `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Source               RoleSource                `protobuf:"varint,2,opt,name=source,proto3,enum=rcmd.business_ai_partner.RoleSource" json:"source,omitempty"`
	Uid                  uint32                    `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Type                 RoleType                  `protobuf:"varint,4,opt,name=type,proto3,enum=rcmd.business_ai_partner.RoleType" json:"type,omitempty"`
	State                RoleState                 `protobuf:"varint,5,opt,name=state,proto3,enum=rcmd.business_ai_partner.RoleState" json:"state,omitempty"`
	Sex                  uint32                    `protobuf:"varint,6,opt,name=sex,proto3" json:"sex,omitempty"`
	BackgroundImage      string                    `protobuf:"bytes,7,opt,name=background_image,json=backgroundImage,proto3" json:"background_image,omitempty"`
	Avatar               string                    `protobuf:"bytes,8,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Character            string                    `protobuf:"bytes,9,opt,name=character,proto3" json:"character,omitempty"`
	PromptId             string                    `protobuf:"bytes,10,opt,name=prompt_id,json=promptId,proto3" json:"prompt_id,omitempty"`
	PromptVersion        string                    `protobuf:"bytes,11,opt,name=prompt_version,json=promptVersion,proto3" json:"prompt_version,omitempty"`
	EnableRcmdReply      bool                      `protobuf:"varint,12,opt,name=enable_rcmd_reply,json=enableRcmdReply,proto3" json:"enable_rcmd_reply,omitempty"`
	Prologue             string                    `protobuf:"bytes,13,opt,name=prologue,proto3" json:"prologue,omitempty"`
	PrologueVoice        string                    `protobuf:"bytes,14,opt,name=prologue_voice,json=prologueVoice,proto3" json:"prologue_voice,omitempty"`
	CategoryId           string                    `protobuf:"bytes,15,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Tags                 []string                  `protobuf:"bytes,16,rep,name=tags,proto3" json:"tags,omitempty"`
	Timbre               string                    `protobuf:"bytes,17,opt,name=timbre,proto3" json:"timbre,omitempty"`
	NthTextList          []*RoleNthText            `protobuf:"bytes,18,rep,name=nth_text_list,json=nthTextList,proto3" json:"nth_text_list,omitempty"`
	AuditResult          AuditResult               `protobuf:"varint,19,opt,name=audit_result,json=auditResult,proto3,enum=rcmd.business_ai_partner.AuditResult" json:"audit_result,omitempty"`
	InsertPos            uint32                    `protobuf:"varint,20,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	Exposed              bool                      `protobuf:"varint,21,opt,name=exposed,proto3" json:"exposed,omitempty"`
	StoryId              string                    `protobuf:"bytes,22,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	GroupRoleConfig      *RoleInfo_GroupRoleConfig `protobuf:"bytes,23,opt,name=group_role_config,json=groupRoleConfig,proto3" json:"group_role_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *RoleInfo) Reset()         { *m = RoleInfo{} }
func (m *RoleInfo) String() string { return proto.CompactTextString(m) }
func (*RoleInfo) ProtoMessage()    {}
func (*RoleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{35}
}
func (m *RoleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoleInfo.Unmarshal(m, b)
}
func (m *RoleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoleInfo.Marshal(b, m, deterministic)
}
func (dst *RoleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoleInfo.Merge(dst, src)
}
func (m *RoleInfo) XXX_Size() int {
	return xxx_messageInfo_RoleInfo.Size(m)
}
func (m *RoleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RoleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RoleInfo proto.InternalMessageInfo

func (m *RoleInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RoleInfo) GetSource() RoleSource {
	if m != nil {
		return m.Source
	}
	return RoleSource_RoleSource_Invalid
}

func (m *RoleInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RoleInfo) GetType() RoleType {
	if m != nil {
		return m.Type
	}
	return RoleType_RoleType_Default
}

func (m *RoleInfo) GetState() RoleState {
	if m != nil {
		return m.State
	}
	return RoleState_RoleState_Invalid
}

func (m *RoleInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *RoleInfo) GetBackgroundImage() string {
	if m != nil {
		return m.BackgroundImage
	}
	return ""
}

func (m *RoleInfo) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *RoleInfo) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

func (m *RoleInfo) GetPromptId() string {
	if m != nil {
		return m.PromptId
	}
	return ""
}

func (m *RoleInfo) GetPromptVersion() string {
	if m != nil {
		return m.PromptVersion
	}
	return ""
}

func (m *RoleInfo) GetEnableRcmdReply() bool {
	if m != nil {
		return m.EnableRcmdReply
	}
	return false
}

func (m *RoleInfo) GetPrologue() string {
	if m != nil {
		return m.Prologue
	}
	return ""
}

func (m *RoleInfo) GetPrologueVoice() string {
	if m != nil {
		return m.PrologueVoice
	}
	return ""
}

func (m *RoleInfo) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *RoleInfo) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *RoleInfo) GetTimbre() string {
	if m != nil {
		return m.Timbre
	}
	return ""
}

func (m *RoleInfo) GetNthTextList() []*RoleNthText {
	if m != nil {
		return m.NthTextList
	}
	return nil
}

func (m *RoleInfo) GetAuditResult() AuditResult {
	if m != nil {
		return m.AuditResult
	}
	return AuditResult_AuditResult_Review
}

func (m *RoleInfo) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *RoleInfo) GetExposed() bool {
	if m != nil {
		return m.Exposed
	}
	return false
}

func (m *RoleInfo) GetStoryId() string {
	if m != nil {
		return m.StoryId
	}
	return ""
}

func (m *RoleInfo) GetGroupRoleConfig() *RoleInfo_GroupRoleConfig {
	if m != nil {
		return m.GroupRoleConfig
	}
	return nil
}

// 群聊角色属性配置
type RoleInfo_GroupRoleConfig struct {
	// 群聊开场白
	Prologues []*RoleInfo_AIGroupPrologue `protobuf:"bytes,1,rep,name=prologues,proto3" json:"prologues,omitempty"`
	// 群聊角色描述
	ChatCharacter string `protobuf:"bytes,2,opt,name=chat_character,json=chatCharacter,proto3" json:"chat_character,omitempty"`
	// 关系描述
	RelationCharacter string `protobuf:"bytes,3,opt,name=relation_character,json=relationCharacter,proto3" json:"relation_character,omitempty"`
	// 角色描述
	Desc                 string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoleInfo_GroupRoleConfig) Reset()         { *m = RoleInfo_GroupRoleConfig{} }
func (m *RoleInfo_GroupRoleConfig) String() string { return proto.CompactTextString(m) }
func (*RoleInfo_GroupRoleConfig) ProtoMessage()    {}
func (*RoleInfo_GroupRoleConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{35, 0}
}
func (m *RoleInfo_GroupRoleConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoleInfo_GroupRoleConfig.Unmarshal(m, b)
}
func (m *RoleInfo_GroupRoleConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoleInfo_GroupRoleConfig.Marshal(b, m, deterministic)
}
func (dst *RoleInfo_GroupRoleConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoleInfo_GroupRoleConfig.Merge(dst, src)
}
func (m *RoleInfo_GroupRoleConfig) XXX_Size() int {
	return xxx_messageInfo_RoleInfo_GroupRoleConfig.Size(m)
}
func (m *RoleInfo_GroupRoleConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_RoleInfo_GroupRoleConfig.DiscardUnknown(m)
}

var xxx_messageInfo_RoleInfo_GroupRoleConfig proto.InternalMessageInfo

func (m *RoleInfo_GroupRoleConfig) GetPrologues() []*RoleInfo_AIGroupPrologue {
	if m != nil {
		return m.Prologues
	}
	return nil
}

func (m *RoleInfo_GroupRoleConfig) GetChatCharacter() string {
	if m != nil {
		return m.ChatCharacter
	}
	return ""
}

func (m *RoleInfo_GroupRoleConfig) GetRelationCharacter() string {
	if m != nil {
		return m.RelationCharacter
	}
	return ""
}

func (m *RoleInfo_GroupRoleConfig) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

// 群开场白
type RoleInfo_AIGroupPrologue struct {
	// 开场白文本内容
	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// 开场白语音链接
	Audio string `protobuf:"bytes,2,opt,name=audio,proto3" json:"audio,omitempty"`
	// 开场白顺序
	Priority             uint32   `protobuf:"varint,3,opt,name=priority,proto3" json:"priority,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoleInfo_AIGroupPrologue) Reset()         { *m = RoleInfo_AIGroupPrologue{} }
func (m *RoleInfo_AIGroupPrologue) String() string { return proto.CompactTextString(m) }
func (*RoleInfo_AIGroupPrologue) ProtoMessage()    {}
func (*RoleInfo_AIGroupPrologue) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{35, 1}
}
func (m *RoleInfo_AIGroupPrologue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoleInfo_AIGroupPrologue.Unmarshal(m, b)
}
func (m *RoleInfo_AIGroupPrologue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoleInfo_AIGroupPrologue.Marshal(b, m, deterministic)
}
func (dst *RoleInfo_AIGroupPrologue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoleInfo_AIGroupPrologue.Merge(dst, src)
}
func (m *RoleInfo_AIGroupPrologue) XXX_Size() int {
	return xxx_messageInfo_RoleInfo_AIGroupPrologue.Size(m)
}
func (m *RoleInfo_AIGroupPrologue) XXX_DiscardUnknown() {
	xxx_messageInfo_RoleInfo_AIGroupPrologue.DiscardUnknown(m)
}

var xxx_messageInfo_RoleInfo_AIGroupPrologue proto.InternalMessageInfo

func (m *RoleInfo_AIGroupPrologue) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *RoleInfo_AIGroupPrologue) GetAudio() string {
	if m != nil {
		return m.Audio
	}
	return ""
}

func (m *RoleInfo_AIGroupPrologue) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

type CreateRoleReq struct {
	RoleId               uint32    `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Role                 *RoleInfo `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *CreateRoleReq) Reset()         { *m = CreateRoleReq{} }
func (m *CreateRoleReq) String() string { return proto.CompactTextString(m) }
func (*CreateRoleReq) ProtoMessage()    {}
func (*CreateRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{36}
}
func (m *CreateRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRoleReq.Unmarshal(m, b)
}
func (m *CreateRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRoleReq.Marshal(b, m, deterministic)
}
func (dst *CreateRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRoleReq.Merge(dst, src)
}
func (m *CreateRoleReq) XXX_Size() int {
	return xxx_messageInfo_CreateRoleReq.Size(m)
}
func (m *CreateRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRoleReq proto.InternalMessageInfo

func (m *CreateRoleReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *CreateRoleReq) GetRole() *RoleInfo {
	if m != nil {
		return m.Role
	}
	return nil
}

type CreateRoleResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateRoleResp) Reset()         { *m = CreateRoleResp{} }
func (m *CreateRoleResp) String() string { return proto.CompactTextString(m) }
func (*CreateRoleResp) ProtoMessage()    {}
func (*CreateRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{37}
}
func (m *CreateRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRoleResp.Unmarshal(m, b)
}
func (m *CreateRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRoleResp.Marshal(b, m, deterministic)
}
func (dst *CreateRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRoleResp.Merge(dst, src)
}
func (m *CreateRoleResp) XXX_Size() int {
	return xxx_messageInfo_CreateRoleResp.Size(m)
}
func (m *CreateRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRoleResp proto.InternalMessageInfo

type UpdateRoleReq struct {
	RoleId               uint32    `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Role                 *RoleInfo `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *UpdateRoleReq) Reset()         { *m = UpdateRoleReq{} }
func (m *UpdateRoleReq) String() string { return proto.CompactTextString(m) }
func (*UpdateRoleReq) ProtoMessage()    {}
func (*UpdateRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{38}
}
func (m *UpdateRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRoleReq.Unmarshal(m, b)
}
func (m *UpdateRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRoleReq.Marshal(b, m, deterministic)
}
func (dst *UpdateRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRoleReq.Merge(dst, src)
}
func (m *UpdateRoleReq) XXX_Size() int {
	return xxx_messageInfo_UpdateRoleReq.Size(m)
}
func (m *UpdateRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRoleReq proto.InternalMessageInfo

func (m *UpdateRoleReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *UpdateRoleReq) GetRole() *RoleInfo {
	if m != nil {
		return m.Role
	}
	return nil
}

type UpdateRoleResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateRoleResp) Reset()         { *m = UpdateRoleResp{} }
func (m *UpdateRoleResp) String() string { return proto.CompactTextString(m) }
func (*UpdateRoleResp) ProtoMessage()    {}
func (*UpdateRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{39}
}
func (m *UpdateRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRoleResp.Unmarshal(m, b)
}
func (m *UpdateRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRoleResp.Marshal(b, m, deterministic)
}
func (dst *UpdateRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRoleResp.Merge(dst, src)
}
func (m *UpdateRoleResp) XXX_Size() int {
	return xxx_messageInfo_UpdateRoleResp.Size(m)
}
func (m *UpdateRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRoleResp proto.InternalMessageInfo

type GetAtmosphereListReq struct {
	PartnerId            uint32   `protobuf:"varint,1,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAtmosphereListReq) Reset()         { *m = GetAtmosphereListReq{} }
func (m *GetAtmosphereListReq) String() string { return proto.CompactTextString(m) }
func (*GetAtmosphereListReq) ProtoMessage()    {}
func (*GetAtmosphereListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{40}
}
func (m *GetAtmosphereListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAtmosphereListReq.Unmarshal(m, b)
}
func (m *GetAtmosphereListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAtmosphereListReq.Marshal(b, m, deterministic)
}
func (dst *GetAtmosphereListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAtmosphereListReq.Merge(dst, src)
}
func (m *GetAtmosphereListReq) XXX_Size() int {
	return xxx_messageInfo_GetAtmosphereListReq.Size(m)
}
func (m *GetAtmosphereListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAtmosphereListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAtmosphereListReq proto.InternalMessageInfo

func (m *GetAtmosphereListReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

type GetAtmosphereListResp struct {
	List                 []*Atmosphere `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetAtmosphereListResp) Reset()         { *m = GetAtmosphereListResp{} }
func (m *GetAtmosphereListResp) String() string { return proto.CompactTextString(m) }
func (*GetAtmosphereListResp) ProtoMessage()    {}
func (*GetAtmosphereListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{41}
}
func (m *GetAtmosphereListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAtmosphereListResp.Unmarshal(m, b)
}
func (m *GetAtmosphereListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAtmosphereListResp.Marshal(b, m, deterministic)
}
func (dst *GetAtmosphereListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAtmosphereListResp.Merge(dst, src)
}
func (m *GetAtmosphereListResp) XXX_Size() int {
	return xxx_messageInfo_GetAtmosphereListResp.Size(m)
}
func (m *GetAtmosphereListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAtmosphereListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAtmosphereListResp proto.InternalMessageInfo

func (m *GetAtmosphereListResp) GetList() []*Atmosphere {
	if m != nil {
		return m.List
	}
	return nil
}

type GetAtmosphereReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,3,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAtmosphereReq) Reset()         { *m = GetAtmosphereReq{} }
func (m *GetAtmosphereReq) String() string { return proto.CompactTextString(m) }
func (*GetAtmosphereReq) ProtoMessage()    {}
func (*GetAtmosphereReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{42}
}
func (m *GetAtmosphereReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAtmosphereReq.Unmarshal(m, b)
}
func (m *GetAtmosphereReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAtmosphereReq.Marshal(b, m, deterministic)
}
func (dst *GetAtmosphereReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAtmosphereReq.Merge(dst, src)
}
func (m *GetAtmosphereReq) XXX_Size() int {
	return xxx_messageInfo_GetAtmosphereReq.Size(m)
}
func (m *GetAtmosphereReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAtmosphereReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAtmosphereReq proto.InternalMessageInfo

func (m *GetAtmosphereReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetAtmosphereReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAtmosphereReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

type Atmosphere struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ResourceUrl          string   `protobuf:"bytes,2,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	ResourceType         string   `protobuf:"bytes,3,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	Description          string   `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	AudioUrl             string   `protobuf:"bytes,5,opt,name=audio_url,json=audioUrl,proto3" json:"audio_url,omitempty"`
	SmallUrl             string   `protobuf:"bytes,6,opt,name=small_url,json=smallUrl,proto3" json:"small_url,omitempty"`
	ImageUrl             string   `protobuf:"bytes,7,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Atmosphere) Reset()         { *m = Atmosphere{} }
func (m *Atmosphere) String() string { return proto.CompactTextString(m) }
func (*Atmosphere) ProtoMessage()    {}
func (*Atmosphere) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{43}
}
func (m *Atmosphere) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Atmosphere.Unmarshal(m, b)
}
func (m *Atmosphere) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Atmosphere.Marshal(b, m, deterministic)
}
func (dst *Atmosphere) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Atmosphere.Merge(dst, src)
}
func (m *Atmosphere) XXX_Size() int {
	return xxx_messageInfo_Atmosphere.Size(m)
}
func (m *Atmosphere) XXX_DiscardUnknown() {
	xxx_messageInfo_Atmosphere.DiscardUnknown(m)
}

var xxx_messageInfo_Atmosphere proto.InternalMessageInfo

func (m *Atmosphere) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *Atmosphere) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *Atmosphere) GetResourceType() string {
	if m != nil {
		return m.ResourceType
	}
	return ""
}

func (m *Atmosphere) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *Atmosphere) GetAudioUrl() string {
	if m != nil {
		return m.AudioUrl
	}
	return ""
}

func (m *Atmosphere) GetSmallUrl() string {
	if m != nil {
		return m.SmallUrl
	}
	return ""
}

func (m *Atmosphere) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

type GetAtmosphereResp struct {
	Atmosphere           *Atmosphere `protobuf:"bytes,1,opt,name=atmosphere,proto3" json:"atmosphere,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetAtmosphereResp) Reset()         { *m = GetAtmosphereResp{} }
func (m *GetAtmosphereResp) String() string { return proto.CompactTextString(m) }
func (*GetAtmosphereResp) ProtoMessage()    {}
func (*GetAtmosphereResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{44}
}
func (m *GetAtmosphereResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAtmosphereResp.Unmarshal(m, b)
}
func (m *GetAtmosphereResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAtmosphereResp.Marshal(b, m, deterministic)
}
func (dst *GetAtmosphereResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAtmosphereResp.Merge(dst, src)
}
func (m *GetAtmosphereResp) XXX_Size() int {
	return xxx_messageInfo_GetAtmosphereResp.Size(m)
}
func (m *GetAtmosphereResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAtmosphereResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAtmosphereResp proto.InternalMessageInfo

func (m *GetAtmosphereResp) GetAtmosphere() *Atmosphere {
	if m != nil {
		return m.Atmosphere
	}
	return nil
}

type GetRoleTipInfoReq struct {
	PartnerId            uint32   `protobuf:"varint,1,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRoleTipInfoReq) Reset()         { *m = GetRoleTipInfoReq{} }
func (m *GetRoleTipInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetRoleTipInfoReq) ProtoMessage()    {}
func (*GetRoleTipInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{45}
}
func (m *GetRoleTipInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoleTipInfoReq.Unmarshal(m, b)
}
func (m *GetRoleTipInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoleTipInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetRoleTipInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoleTipInfoReq.Merge(dst, src)
}
func (m *GetRoleTipInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetRoleTipInfoReq.Size(m)
}
func (m *GetRoleTipInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoleTipInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoleTipInfoReq proto.InternalMessageInfo

func (m *GetRoleTipInfoReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

type GetRoleTipInfoResp struct {
	StartTs              int64    `protobuf:"varint,1,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	NowSvrTs             int64    `protobuf:"varint,2,opt,name=now_svr_ts,json=nowSvrTs,proto3" json:"now_svr_ts,omitempty"`
	TriggerSeconds       uint32   `protobuf:"varint,3,opt,name=trigger_seconds,json=triggerSeconds,proto3" json:"trigger_seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRoleTipInfoResp) Reset()         { *m = GetRoleTipInfoResp{} }
func (m *GetRoleTipInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetRoleTipInfoResp) ProtoMessage()    {}
func (*GetRoleTipInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{46}
}
func (m *GetRoleTipInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoleTipInfoResp.Unmarshal(m, b)
}
func (m *GetRoleTipInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoleTipInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetRoleTipInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoleTipInfoResp.Merge(dst, src)
}
func (m *GetRoleTipInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetRoleTipInfoResp.Size(m)
}
func (m *GetRoleTipInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoleTipInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoleTipInfoResp proto.InternalMessageInfo

func (m *GetRoleTipInfoResp) GetStartTs() int64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *GetRoleTipInfoResp) GetNowSvrTs() int64 {
	if m != nil {
		return m.NowSvrTs
	}
	return 0
}

func (m *GetRoleTipInfoResp) GetTriggerSeconds() uint32 {
	if m != nil {
		return m.TriggerSeconds
	}
	return 0
}

type GetPhotographsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPhotographsReq) Reset()         { *m = GetPhotographsReq{} }
func (m *GetPhotographsReq) String() string { return proto.CompactTextString(m) }
func (*GetPhotographsReq) ProtoMessage()    {}
func (*GetPhotographsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{47}
}
func (m *GetPhotographsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPhotographsReq.Unmarshal(m, b)
}
func (m *GetPhotographsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPhotographsReq.Marshal(b, m, deterministic)
}
func (dst *GetPhotographsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPhotographsReq.Merge(dst, src)
}
func (m *GetPhotographsReq) XXX_Size() int {
	return xxx_messageInfo_GetPhotographsReq.Size(m)
}
func (m *GetPhotographsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPhotographsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPhotographsReq proto.InternalMessageInfo

func (m *GetPhotographsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPhotographsReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

type GetPhotographsResp struct {
	Photographs          []*PhotoGraph `protobuf:"bytes,1,rep,name=photographs,proto3" json:"photographs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetPhotographsResp) Reset()         { *m = GetPhotographsResp{} }
func (m *GetPhotographsResp) String() string { return proto.CompactTextString(m) }
func (*GetPhotographsResp) ProtoMessage()    {}
func (*GetPhotographsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{48}
}
func (m *GetPhotographsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPhotographsResp.Unmarshal(m, b)
}
func (m *GetPhotographsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPhotographsResp.Marshal(b, m, deterministic)
}
func (dst *GetPhotographsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPhotographsResp.Merge(dst, src)
}
func (m *GetPhotographsResp) XXX_Size() int {
	return xxx_messageInfo_GetPhotographsResp.Size(m)
}
func (m *GetPhotographsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPhotographsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPhotographsResp proto.InternalMessageInfo

func (m *GetPhotographsResp) GetPhotographs() []*PhotoGraph {
	if m != nil {
		return m.Photographs
	}
	return nil
}

type GetPartnerInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPartnerInfoReq) Reset()         { *m = GetPartnerInfoReq{} }
func (m *GetPartnerInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetPartnerInfoReq) ProtoMessage()    {}
func (*GetPartnerInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{49}
}
func (m *GetPartnerInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPartnerInfoReq.Unmarshal(m, b)
}
func (m *GetPartnerInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPartnerInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetPartnerInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPartnerInfoReq.Merge(dst, src)
}
func (m *GetPartnerInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetPartnerInfoReq.Size(m)
}
func (m *GetPartnerInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPartnerInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPartnerInfoReq proto.InternalMessageInfo

func (m *GetPartnerInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPartnerInfoReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

type GetPartnerInfoResp struct {
	CurPhotograph        *PhotoGraph `protobuf:"bytes,1,opt,name=cur_photograph,json=curPhotograph,proto3" json:"cur_photograph,omitempty"`
	RelationshipId       uint32      `protobuf:"varint,2,opt,name=relationship_id,json=relationshipId,proto3" json:"relationship_id,omitempty"`
	RelationshipName     string      `protobuf:"bytes,3,opt,name=relationship_name,json=relationshipName,proto3" json:"relationship_name,omitempty"`
	MeetDays             uint32      `protobuf:"varint,4,opt,name=meet_days,json=meetDays,proto3" json:"meet_days,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetPartnerInfoResp) Reset()         { *m = GetPartnerInfoResp{} }
func (m *GetPartnerInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetPartnerInfoResp) ProtoMessage()    {}
func (*GetPartnerInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{50}
}
func (m *GetPartnerInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPartnerInfoResp.Unmarshal(m, b)
}
func (m *GetPartnerInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPartnerInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetPartnerInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPartnerInfoResp.Merge(dst, src)
}
func (m *GetPartnerInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetPartnerInfoResp.Size(m)
}
func (m *GetPartnerInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPartnerInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPartnerInfoResp proto.InternalMessageInfo

func (m *GetPartnerInfoResp) GetCurPhotograph() *PhotoGraph {
	if m != nil {
		return m.CurPhotograph
	}
	return nil
}

func (m *GetPartnerInfoResp) GetRelationshipId() uint32 {
	if m != nil {
		return m.RelationshipId
	}
	return 0
}

func (m *GetPartnerInfoResp) GetRelationshipName() string {
	if m != nil {
		return m.RelationshipName
	}
	return ""
}

func (m *GetPartnerInfoResp) GetMeetDays() uint32 {
	if m != nil {
		return m.MeetDays
	}
	return 0
}

type PartnerInfo struct {
	CurPhotograph        *PhotoGraph       `protobuf:"bytes,1,opt,name=cur_photograph,json=curPhotograph,proto3" json:"cur_photograph,omitempty"`
	Relationship         *RelationshipInfo `protobuf:"bytes,2,opt,name=relationship,proto3" json:"relationship,omitempty"`
	RelationshipTrigger  bool              `protobuf:"varint,3,opt,name=relationship_trigger,json=relationshipTrigger,proto3" json:"relationship_trigger,omitempty"`
	AnimateMemes         []string          `protobuf:"bytes,4,rep,name=animate_memes,json=animateMemes,proto3" json:"animate_memes,omitempty"`
	CreateTime           int64             `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	AtmosphereIds        []string          `protobuf:"bytes,6,rep,name=atmosphere_ids,json=atmosphereIds,proto3" json:"atmosphere_ids,omitempty"`
	Id                   uint32            `protobuf:"varint,7,opt,name=id,proto3" json:"id,omitempty"`
	Hint                 string            `protobuf:"bytes,8,opt,name=hint,proto3" json:"hint,omitempty"`
	ShowHint             bool              `protobuf:"varint,9,opt,name=show_hint,json=showHint,proto3" json:"show_hint,omitempty"`
	RoleId               uint32            `protobuf:"varint,10,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	RoleExt              []byte            `protobuf:"bytes,11,opt,name=role_ext,json=roleExt,proto3" json:"role_ext,omitempty"`
	CustomVoices         []*CustomVoice    `protobuf:"bytes,12,rep,name=custom_voices,json=customVoices,proto3" json:"custom_voices,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PartnerInfo) Reset()         { *m = PartnerInfo{} }
func (m *PartnerInfo) String() string { return proto.CompactTextString(m) }
func (*PartnerInfo) ProtoMessage()    {}
func (*PartnerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{51}
}
func (m *PartnerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PartnerInfo.Unmarshal(m, b)
}
func (m *PartnerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PartnerInfo.Marshal(b, m, deterministic)
}
func (dst *PartnerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PartnerInfo.Merge(dst, src)
}
func (m *PartnerInfo) XXX_Size() int {
	return xxx_messageInfo_PartnerInfo.Size(m)
}
func (m *PartnerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PartnerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PartnerInfo proto.InternalMessageInfo

func (m *PartnerInfo) GetCurPhotograph() *PhotoGraph {
	if m != nil {
		return m.CurPhotograph
	}
	return nil
}

func (m *PartnerInfo) GetRelationship() *RelationshipInfo {
	if m != nil {
		return m.Relationship
	}
	return nil
}

func (m *PartnerInfo) GetRelationshipTrigger() bool {
	if m != nil {
		return m.RelationshipTrigger
	}
	return false
}

func (m *PartnerInfo) GetAnimateMemes() []string {
	if m != nil {
		return m.AnimateMemes
	}
	return nil
}

func (m *PartnerInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *PartnerInfo) GetAtmosphereIds() []string {
	if m != nil {
		return m.AtmosphereIds
	}
	return nil
}

func (m *PartnerInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PartnerInfo) GetHint() string {
	if m != nil {
		return m.Hint
	}
	return ""
}

func (m *PartnerInfo) GetShowHint() bool {
	if m != nil {
		return m.ShowHint
	}
	return false
}

func (m *PartnerInfo) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *PartnerInfo) GetRoleExt() []byte {
	if m != nil {
		return m.RoleExt
	}
	return nil
}

func (m *PartnerInfo) GetCustomVoices() []*CustomVoice {
	if m != nil {
		return m.CustomVoices
	}
	return nil
}

type PhotoGraph struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Avatar               string   `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Image                string   `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	Type                 uint32   `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	ImageForMsg          string   `protobuf:"bytes,5,opt,name=image_for_msg,json=imageForMsg,proto3" json:"image_for_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PhotoGraph) Reset()         { *m = PhotoGraph{} }
func (m *PhotoGraph) String() string { return proto.CompactTextString(m) }
func (*PhotoGraph) ProtoMessage()    {}
func (*PhotoGraph) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{52}
}
func (m *PhotoGraph) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PhotoGraph.Unmarshal(m, b)
}
func (m *PhotoGraph) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PhotoGraph.Marshal(b, m, deterministic)
}
func (dst *PhotoGraph) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PhotoGraph.Merge(dst, src)
}
func (m *PhotoGraph) XXX_Size() int {
	return xxx_messageInfo_PhotoGraph.Size(m)
}
func (m *PhotoGraph) XXX_DiscardUnknown() {
	xxx_messageInfo_PhotoGraph.DiscardUnknown(m)
}

var xxx_messageInfo_PhotoGraph proto.InternalMessageInfo

func (m *PhotoGraph) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PhotoGraph) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *PhotoGraph) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *PhotoGraph) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *PhotoGraph) GetImageForMsg() string {
	if m != nil {
		return m.ImageForMsg
	}
	return ""
}

type RelationshipInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	NameStyle            string   `protobuf:"bytes,3,opt,name=name_style,json=nameStyle,proto3" json:"name_style,omitempty"`
	BackgroundUrl        string   `protobuf:"bytes,4,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RelationshipInfo) Reset()         { *m = RelationshipInfo{} }
func (m *RelationshipInfo) String() string { return proto.CompactTextString(m) }
func (*RelationshipInfo) ProtoMessage()    {}
func (*RelationshipInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{53}
}
func (m *RelationshipInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationshipInfo.Unmarshal(m, b)
}
func (m *RelationshipInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationshipInfo.Marshal(b, m, deterministic)
}
func (dst *RelationshipInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationshipInfo.Merge(dst, src)
}
func (m *RelationshipInfo) XXX_Size() int {
	return xxx_messageInfo_RelationshipInfo.Size(m)
}
func (m *RelationshipInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationshipInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RelationshipInfo proto.InternalMessageInfo

func (m *RelationshipInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RelationshipInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RelationshipInfo) GetNameStyle() string {
	if m != nil {
		return m.NameStyle
	}
	return ""
}

func (m *RelationshipInfo) GetBackgroundUrl() string {
	if m != nil {
		return m.BackgroundUrl
	}
	return ""
}

type RoleSwitchReq struct {
	Uid                  uint32                       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32                       `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                       `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	CallName             string                       `protobuf:"bytes,4,opt,name=call_name,json=callName,proto3" json:"call_name,omitempty"`
	Relationship         RoleSwitchReq_Relationship   `protobuf:"varint,5,opt,name=relationship,proto3,enum=rcmd.business_ai_partner.RoleSwitchReq_Relationship" json:"relationship,omitempty"`
	Role                 *RoleSwitchReq_AIRole        `protobuf:"bytes,6,opt,name=role,proto3" json:"role,omitempty"`
	Status               RoleSwitchReq_ChattingStatus `protobuf:"varint,7,opt,name=status,proto3,enum=rcmd.business_ai_partner.RoleSwitchReq_ChattingStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *RoleSwitchReq) Reset()         { *m = RoleSwitchReq{} }
func (m *RoleSwitchReq) String() string { return proto.CompactTextString(m) }
func (*RoleSwitchReq) ProtoMessage()    {}
func (*RoleSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{54}
}
func (m *RoleSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoleSwitchReq.Unmarshal(m, b)
}
func (m *RoleSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoleSwitchReq.Marshal(b, m, deterministic)
}
func (dst *RoleSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoleSwitchReq.Merge(dst, src)
}
func (m *RoleSwitchReq) XXX_Size() int {
	return xxx_messageInfo_RoleSwitchReq.Size(m)
}
func (m *RoleSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RoleSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_RoleSwitchReq proto.InternalMessageInfo

func (m *RoleSwitchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RoleSwitchReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RoleSwitchReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RoleSwitchReq) GetCallName() string {
	if m != nil {
		return m.CallName
	}
	return ""
}

func (m *RoleSwitchReq) GetRelationship() RoleSwitchReq_Relationship {
	if m != nil {
		return m.Relationship
	}
	return RoleSwitchReq_RelationshipUnknown
}

func (m *RoleSwitchReq) GetRole() *RoleSwitchReq_AIRole {
	if m != nil {
		return m.Role
	}
	return nil
}

func (m *RoleSwitchReq) GetStatus() RoleSwitchReq_ChattingStatus {
	if m != nil {
		return m.Status
	}
	return RoleSwitchReq_Chatting
}

type RoleSwitchReq_AIRole struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// AI风格
	Style string `protobuf:"bytes,2,opt,name=style,proto3" json:"style,omitempty"`
	// AI性别 0:女 1:男
	Sex                  int32                     `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Type                 RoleSwitchReq_AIRole_Type `protobuf:"varint,4,opt,name=type,proto3,enum=rcmd.business_ai_partner.RoleSwitchReq_AIRole_Type" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *RoleSwitchReq_AIRole) Reset()         { *m = RoleSwitchReq_AIRole{} }
func (m *RoleSwitchReq_AIRole) String() string { return proto.CompactTextString(m) }
func (*RoleSwitchReq_AIRole) ProtoMessage()    {}
func (*RoleSwitchReq_AIRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{54, 0}
}
func (m *RoleSwitchReq_AIRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoleSwitchReq_AIRole.Unmarshal(m, b)
}
func (m *RoleSwitchReq_AIRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoleSwitchReq_AIRole.Marshal(b, m, deterministic)
}
func (dst *RoleSwitchReq_AIRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoleSwitchReq_AIRole.Merge(dst, src)
}
func (m *RoleSwitchReq_AIRole) XXX_Size() int {
	return xxx_messageInfo_RoleSwitchReq_AIRole.Size(m)
}
func (m *RoleSwitchReq_AIRole) XXX_DiscardUnknown() {
	xxx_messageInfo_RoleSwitchReq_AIRole.DiscardUnknown(m)
}

var xxx_messageInfo_RoleSwitchReq_AIRole proto.InternalMessageInfo

func (m *RoleSwitchReq_AIRole) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RoleSwitchReq_AIRole) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

func (m *RoleSwitchReq_AIRole) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *RoleSwitchReq_AIRole) GetType() RoleSwitchReq_AIRole_Type {
	if m != nil {
		return m.Type
	}
	return RoleSwitchReq_AIRole_Type_Default
}

type RoleSwitchResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoleSwitchResp) Reset()         { *m = RoleSwitchResp{} }
func (m *RoleSwitchResp) String() string { return proto.CompactTextString(m) }
func (*RoleSwitchResp) ProtoMessage()    {}
func (*RoleSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{55}
}
func (m *RoleSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoleSwitchResp.Unmarshal(m, b)
}
func (m *RoleSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoleSwitchResp.Marshal(b, m, deterministic)
}
func (dst *RoleSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoleSwitchResp.Merge(dst, src)
}
func (m *RoleSwitchResp) XXX_Size() int {
	return xxx_messageInfo_RoleSwitchResp.Size(m)
}
func (m *RoleSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RoleSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_RoleSwitchResp proto.InternalMessageInfo

type BatchGetRolesReq struct {
	RoleIds              []uint32 `protobuf:"varint,1,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetRolesReq) Reset()         { *m = BatchGetRolesReq{} }
func (m *BatchGetRolesReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetRolesReq) ProtoMessage()    {}
func (*BatchGetRolesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{56}
}
func (m *BatchGetRolesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRolesReq.Unmarshal(m, b)
}
func (m *BatchGetRolesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRolesReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetRolesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRolesReq.Merge(dst, src)
}
func (m *BatchGetRolesReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetRolesReq.Size(m)
}
func (m *BatchGetRolesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRolesReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRolesReq proto.InternalMessageInfo

func (m *BatchGetRolesReq) GetRoleIds() []uint32 {
	if m != nil {
		return m.RoleIds
	}
	return nil
}

type BatchGetRolesResp struct {
	RoleList             []*RoleInfo `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *BatchGetRolesResp) Reset()         { *m = BatchGetRolesResp{} }
func (m *BatchGetRolesResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetRolesResp) ProtoMessage()    {}
func (*BatchGetRolesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{57}
}
func (m *BatchGetRolesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRolesResp.Unmarshal(m, b)
}
func (m *BatchGetRolesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRolesResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetRolesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRolesResp.Merge(dst, src)
}
func (m *BatchGetRolesResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetRolesResp.Size(m)
}
func (m *BatchGetRolesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRolesResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRolesResp proto.InternalMessageInfo

func (m *BatchGetRolesResp) GetRoleList() []*RoleInfo {
	if m != nil {
		return m.RoleList
	}
	return nil
}

type ReadPartnerHeartReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PartnerId            uint32   `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	RoleId               uint32   `protobuf:"varint,3,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Content              string   `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	MsgId                string   `protobuf:"bytes,5,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadPartnerHeartReq) Reset()         { *m = ReadPartnerHeartReq{} }
func (m *ReadPartnerHeartReq) String() string { return proto.CompactTextString(m) }
func (*ReadPartnerHeartReq) ProtoMessage()    {}
func (*ReadPartnerHeartReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{58}
}
func (m *ReadPartnerHeartReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadPartnerHeartReq.Unmarshal(m, b)
}
func (m *ReadPartnerHeartReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadPartnerHeartReq.Marshal(b, m, deterministic)
}
func (dst *ReadPartnerHeartReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadPartnerHeartReq.Merge(dst, src)
}
func (m *ReadPartnerHeartReq) XXX_Size() int {
	return xxx_messageInfo_ReadPartnerHeartReq.Size(m)
}
func (m *ReadPartnerHeartReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadPartnerHeartReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReadPartnerHeartReq proto.InternalMessageInfo

func (m *ReadPartnerHeartReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReadPartnerHeartReq) GetPartnerId() uint32 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *ReadPartnerHeartReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *ReadPartnerHeartReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ReadPartnerHeartReq) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

type ReadPartnerHeartResp struct {
	MsgId                string   `protobuf:"bytes,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadPartnerHeartResp) Reset()         { *m = ReadPartnerHeartResp{} }
func (m *ReadPartnerHeartResp) String() string { return proto.CompactTextString(m) }
func (*ReadPartnerHeartResp) ProtoMessage()    {}
func (*ReadPartnerHeartResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d, []int{59}
}
func (m *ReadPartnerHeartResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadPartnerHeartResp.Unmarshal(m, b)
}
func (m *ReadPartnerHeartResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadPartnerHeartResp.Marshal(b, m, deterministic)
}
func (dst *ReadPartnerHeartResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadPartnerHeartResp.Merge(dst, src)
}
func (m *ReadPartnerHeartResp) XXX_Size() int {
	return xxx_messageInfo_ReadPartnerHeartResp.Size(m)
}
func (m *ReadPartnerHeartResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadPartnerHeartResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReadPartnerHeartResp proto.InternalMessageInfo

func (m *ReadPartnerHeartResp) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *ReadPartnerHeartResp) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func init() {
	proto.RegisterType((*BatchGetRoleInteractiveReq)(nil), "rcmd.business_ai_partner.BatchGetRoleInteractiveReq")
	proto.RegisterType((*RoleInteractive)(nil), "rcmd.business_ai_partner.RoleInteractive")
	proto.RegisterType((*BatchGetRoleInteractiveResp)(nil), "rcmd.business_ai_partner.BatchGetRoleInteractiveResp")
	proto.RegisterType((*BatchGetRoleExtraReq)(nil), "rcmd.business_ai_partner.BatchGetRoleExtraReq")
	proto.RegisterType((*RoleExtra)(nil), "rcmd.business_ai_partner.RoleExtra")
	proto.RegisterType((*BatchGetRoleExtraResp)(nil), "rcmd.business_ai_partner.BatchGetRoleExtraResp")
	proto.RegisterType((*GetVoiceJsonReq)(nil), "rcmd.business_ai_partner.GetVoiceJsonReq")
	proto.RegisterType((*GetVoiceJsonResp)(nil), "rcmd.business_ai_partner.GetVoiceJsonResp")
	proto.RegisterType((*GetReportRoleReq)(nil), "rcmd.business_ai_partner.GetReportRoleReq")
	proto.RegisterType((*GetReportRoleResp)(nil), "rcmd.business_ai_partner.GetReportRoleResp")
	proto.RegisterType((*ReportRoleReq)(nil), "rcmd.business_ai_partner.ReportRoleReq")
	proto.RegisterType((*ReportRoleResp)(nil), "rcmd.business_ai_partner.ReportRoleResp")
	proto.RegisterType((*GetRoleFeatureReq)(nil), "rcmd.business_ai_partner.GetRoleFeatureReq")
	proto.RegisterType((*RoleFeature)(nil), "rcmd.business_ai_partner.RoleFeature")
	proto.RegisterType((*GetRoleFeatureResp)(nil), "rcmd.business_ai_partner.GetRoleFeatureResp")
	proto.RegisterType((*TestReq)(nil), "rcmd.business_ai_partner.TestReq")
	proto.RegisterType((*TestResp)(nil), "rcmd.business_ai_partner.TestResp")
	proto.RegisterType((*GetRoleLikeReq)(nil), "rcmd.business_ai_partner.GetRoleLikeReq")
	proto.RegisterType((*GetRoleLikeResp)(nil), "rcmd.business_ai_partner.GetRoleLikeResp")
	proto.RegisterType((*CheckRolesExistReq)(nil), "rcmd.business_ai_partner.CheckRolesExistReq")
	proto.RegisterType((*CheckRolesExistResp)(nil), "rcmd.business_ai_partner.CheckRolesExistResp")
	proto.RegisterType((*GetGameInfoReq)(nil), "rcmd.business_ai_partner.GetGameInfoReq")
	proto.RegisterType((*GetGameInfoResp)(nil), "rcmd.business_ai_partner.GetGameInfoResp")
	proto.RegisterType((*GetInsertPosRolesReq)(nil), "rcmd.business_ai_partner.GetInsertPosRolesReq")
	proto.RegisterType((*InsertPosRole)(nil), "rcmd.business_ai_partner.InsertPosRole")
	proto.RegisterType((*GetInsertPosRolesResp)(nil), "rcmd.business_ai_partner.GetInsertPosRolesResp")
	proto.RegisterType((*GetRoleReq)(nil), "rcmd.business_ai_partner.GetRoleReq")
	proto.RegisterType((*RoleData)(nil), "rcmd.business_ai_partner.RoleData")
	proto.RegisterType((*GetRoleResp)(nil), "rcmd.business_ai_partner.GetRoleResp")
	proto.RegisterType((*BatchGetPartnerInfoReq)(nil), "rcmd.business_ai_partner.BatchGetPartnerInfoReq")
	proto.RegisterType((*BatchGetPartnerInfoResp)(nil), "rcmd.business_ai_partner.BatchGetPartnerInfoResp")
	proto.RegisterType((*CustomVoice)(nil), "rcmd.business_ai_partner.CustomVoice")
	proto.RegisterType((*SetAIPartnerInfoReq)(nil), "rcmd.business_ai_partner.SetAIPartnerInfoReq")
	proto.RegisterType((*SetAIPartnerInfoResp)(nil), "rcmd.business_ai_partner.SetAIPartnerInfoResp")
	proto.RegisterType((*RoleNthText)(nil), "rcmd.business_ai_partner.RoleNthText")
	proto.RegisterType((*RoleInfo)(nil), "rcmd.business_ai_partner.RoleInfo")
	proto.RegisterType((*RoleInfo_GroupRoleConfig)(nil), "rcmd.business_ai_partner.RoleInfo.GroupRoleConfig")
	proto.RegisterType((*RoleInfo_AIGroupPrologue)(nil), "rcmd.business_ai_partner.RoleInfo.AIGroupPrologue")
	proto.RegisterType((*CreateRoleReq)(nil), "rcmd.business_ai_partner.CreateRoleReq")
	proto.RegisterType((*CreateRoleResp)(nil), "rcmd.business_ai_partner.CreateRoleResp")
	proto.RegisterType((*UpdateRoleReq)(nil), "rcmd.business_ai_partner.UpdateRoleReq")
	proto.RegisterType((*UpdateRoleResp)(nil), "rcmd.business_ai_partner.UpdateRoleResp")
	proto.RegisterType((*GetAtmosphereListReq)(nil), "rcmd.business_ai_partner.GetAtmosphereListReq")
	proto.RegisterType((*GetAtmosphereListResp)(nil), "rcmd.business_ai_partner.GetAtmosphereListResp")
	proto.RegisterType((*GetAtmosphereReq)(nil), "rcmd.business_ai_partner.GetAtmosphereReq")
	proto.RegisterType((*Atmosphere)(nil), "rcmd.business_ai_partner.Atmosphere")
	proto.RegisterType((*GetAtmosphereResp)(nil), "rcmd.business_ai_partner.GetAtmosphereResp")
	proto.RegisterType((*GetRoleTipInfoReq)(nil), "rcmd.business_ai_partner.GetRoleTipInfoReq")
	proto.RegisterType((*GetRoleTipInfoResp)(nil), "rcmd.business_ai_partner.GetRoleTipInfoResp")
	proto.RegisterType((*GetPhotographsReq)(nil), "rcmd.business_ai_partner.GetPhotographsReq")
	proto.RegisterType((*GetPhotographsResp)(nil), "rcmd.business_ai_partner.GetPhotographsResp")
	proto.RegisterType((*GetPartnerInfoReq)(nil), "rcmd.business_ai_partner.GetPartnerInfoReq")
	proto.RegisterType((*GetPartnerInfoResp)(nil), "rcmd.business_ai_partner.GetPartnerInfoResp")
	proto.RegisterType((*PartnerInfo)(nil), "rcmd.business_ai_partner.PartnerInfo")
	proto.RegisterType((*PhotoGraph)(nil), "rcmd.business_ai_partner.PhotoGraph")
	proto.RegisterType((*RelationshipInfo)(nil), "rcmd.business_ai_partner.RelationshipInfo")
	proto.RegisterType((*RoleSwitchReq)(nil), "rcmd.business_ai_partner.RoleSwitchReq")
	proto.RegisterType((*RoleSwitchReq_AIRole)(nil), "rcmd.business_ai_partner.RoleSwitchReq.AIRole")
	proto.RegisterType((*RoleSwitchResp)(nil), "rcmd.business_ai_partner.RoleSwitchResp")
	proto.RegisterType((*BatchGetRolesReq)(nil), "rcmd.business_ai_partner.BatchGetRolesReq")
	proto.RegisterType((*BatchGetRolesResp)(nil), "rcmd.business_ai_partner.BatchGetRolesResp")
	proto.RegisterType((*ReadPartnerHeartReq)(nil), "rcmd.business_ai_partner.ReadPartnerHeartReq")
	proto.RegisterType((*ReadPartnerHeartResp)(nil), "rcmd.business_ai_partner.ReadPartnerHeartResp")
	proto.RegisterEnum("rcmd.business_ai_partner.RoleLikeAction", RoleLikeAction_name, RoleLikeAction_value)
	proto.RegisterEnum("rcmd.business_ai_partner.PartnerSource", PartnerSource_name, PartnerSource_value)
	proto.RegisterEnum("rcmd.business_ai_partner.RoleSource", RoleSource_name, RoleSource_value)
	proto.RegisterEnum("rcmd.business_ai_partner.RoleType", RoleType_name, RoleType_value)
	proto.RegisterEnum("rcmd.business_ai_partner.RoleState", RoleState_name, RoleState_value)
	proto.RegisterEnum("rcmd.business_ai_partner.AuditResult", AuditResult_name, AuditResult_value)
	proto.RegisterEnum("rcmd.business_ai_partner.TestReq_Type", TestReq_Type_name, TestReq_Type_value)
	proto.RegisterEnum("rcmd.business_ai_partner.RoleSwitchReq_Relationship", RoleSwitchReq_Relationship_name, RoleSwitchReq_Relationship_value)
	proto.RegisterEnum("rcmd.business_ai_partner.RoleSwitchReq_ChattingStatus", RoleSwitchReq_ChattingStatus_name, RoleSwitchReq_ChattingStatus_value)
	proto.RegisterEnum("rcmd.business_ai_partner.RoleSwitchReq_AIRole_Type", RoleSwitchReq_AIRole_Type_name, RoleSwitchReq_AIRole_Type_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// BusinessAIPartnerClient is the client API for BusinessAIPartner service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type BusinessAIPartnerClient interface {
	GetPartnerInfo(ctx context.Context, in *GetPartnerInfoReq, opts ...grpc.CallOption) (*GetPartnerInfoResp, error)
	RoleSwitch(ctx context.Context, in *RoleSwitchReq, opts ...grpc.CallOption) (*RoleSwitchResp, error)
	GetPhotographs(ctx context.Context, in *GetPhotographsReq, opts ...grpc.CallOption) (*GetPhotographsResp, error)
	GetAtmosphere(ctx context.Context, in *GetAtmosphereReq, opts ...grpc.CallOption) (*GetAtmosphereResp, error)
	CreateRole(ctx context.Context, in *CreateRoleReq, opts ...grpc.CallOption) (*CreateRoleResp, error)
	UpdateRole(ctx context.Context, in *UpdateRoleReq, opts ...grpc.CallOption) (*UpdateRoleResp, error)
	BatchGetPartnerInfo(ctx context.Context, in *BatchGetPartnerInfoReq, opts ...grpc.CallOption) (*BatchGetPartnerInfoResp, error)
	GetRole(ctx context.Context, in *GetRoleReq, opts ...grpc.CallOption) (*GetRoleResp, error)
	GetInsertPosRoles(ctx context.Context, in *GetInsertPosRolesReq, opts ...grpc.CallOption) (*GetInsertPosRolesResp, error)
	SetAIPartnerInfo(ctx context.Context, in *SetAIPartnerInfoReq, opts ...grpc.CallOption) (*SetAIPartnerInfoResp, error)
	CheckRolesExist(ctx context.Context, in *CheckRolesExistReq, opts ...grpc.CallOption) (*CheckRolesExistResp, error)
	BatchGetRoles(ctx context.Context, in *BatchGetRolesReq, opts ...grpc.CallOption) (*BatchGetRolesResp, error)
	GetGameInfo(ctx context.Context, in *GetGameInfoReq, opts ...grpc.CallOption) (*GetGameInfoResp, error)
	GetRoleLike(ctx context.Context, in *GetRoleLikeReq, opts ...grpc.CallOption) (*GetRoleLikeResp, error)
	GetRoleFeature(ctx context.Context, in *GetRoleFeatureReq, opts ...grpc.CallOption) (*GetRoleFeatureResp, error)
	Test(ctx context.Context, in *TestReq, opts ...grpc.CallOption) (*TestResp, error)
	GetVoiceJson(ctx context.Context, in *GetVoiceJsonReq, opts ...grpc.CallOption) (*GetVoiceJsonResp, error)
	GetReportRole(ctx context.Context, in *GetReportRoleReq, opts ...grpc.CallOption) (*GetReportRoleResp, error)
	ReportRole(ctx context.Context, in *ReportRoleReq, opts ...grpc.CallOption) (*ReportRoleResp, error)
	ReadPartnerHeart(ctx context.Context, in *ReadPartnerHeartReq, opts ...grpc.CallOption) (*ReadPartnerHeartResp, error)
	BatchGetRoleExtra(ctx context.Context, in *BatchGetRoleExtraReq, opts ...grpc.CallOption) (*BatchGetRoleExtraResp, error)
	BatchGetRoleInteractive(ctx context.Context, in *BatchGetRoleInteractiveReq, opts ...grpc.CallOption) (*BatchGetRoleInteractiveResp, error)
	GetRoleInteractive(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	CheckAIRoomVisibilityHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// for http
	GetPartnerInfoHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetRecordsHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetRelationshipListHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	ModifyRelationshipHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	SetUserChatSceneHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	CheckChatSceneVisibilityHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetReadSeqHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	MarkReadSeqHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetPhotographsHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	SetPhotographHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetConfigList(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	AddListenDuration(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetListenDuration(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetRoleTipInfo(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetStoryInfo(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	StartStory(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	SetStoryProgress(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetStoryScene(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetStoryReply(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetStoryHistory(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetUserSettings(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	SetAutoPlayStatus(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取音色列表
	GetVoiceList(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取音色列表
	GetVoiceListV2(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取自定义音色候选列表
	GetCustomVoiceList(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 生成自定义音色
	GenCustomVoice(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取生成自定义音色状态
	GetGenCustomVoiceStatus(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 生成角色人设说明(角色设定一键生成)
	GenRoleDesc(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 推荐回复生成
	GenRecommendReply(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取角色设定一键生成次数限制（用于前端是否显示一键生成按钮）
	GetGenStatus(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	NewTriggerHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetUserMsgStat(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	UserReadMsgNotify(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	Feedback(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 快捷回复(一键回复)
	GetQuickReplyList(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetGameList(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	StartGame(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	InputHint(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetPartnerSettings(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	SetPartnerSilent(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	SetPartnerAutoPlayStatusHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	CheckIfTriggerChatHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetTriggerChatRoleHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取故事书列表
	GetStoryBookList(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	GetStoryBook(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 常驻入口红点
	GetStoryBookNotifySeq(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 常驻红点已读
	MarkStoryBookNotifySeq(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 开始故事章节
	StartStoryBook(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取故事进度
	GetStoryBookProgress(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取故事用户回复
	GetStoryBookReply(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取故事历史记录
	GetStoryBookHistory(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 加白
	AddWhiteList(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	StartVoiceChat(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	SetVoice(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	VoiceChatEntrance(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	EnterPetHome(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 是否显示免打扰
	ShowPartnerSilent(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 是否显示继续说
	ShowContinueChat(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 继续说
	ContinueChat(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取塔罗配置
	GetTarotConfig(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 翻开塔罗牌
	OpenTarotCard(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取某些日期的塔罗结果
	GetTarotResult(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取宠物陪伴天数
	GetPetOwnDay(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取听故事次数
	GetPetStoryNum(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 开始桌宠故事
	StartPetStory(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 结束桌宠故事
	StopPetStory(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 进入桌宠哄睡场景
	EnterPetSleepTalk(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取氛围配置
	GetVibesConfig(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 增加陪伴时间
	AddAccompanyTime(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取陪伴时间
	GetAccompanyTime(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取目标列表
	GetTargetList(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 领取目标奖励
	GetTargetReward(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取聊天次数
	GetChatNum(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取 tts 数据
	GenTTSDataHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 标记语音消息已播放
	MarkVoiceMsgAsPlayed(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取外显玩法
	GetOutGames(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取主题列表
	GetGameTopics(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取某主题玩法列表
	GetTopicGames(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 获取玩法信息
	GetRoleGameInfo(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	PostsExplosureNotifyHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
	// 聊天记录追加(续聊)
	ChatHistoryAppendHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error)
}

type businessAIPartnerClient struct {
	cc *grpc.ClientConn
}

func NewBusinessAIPartnerClient(cc *grpc.ClientConn) BusinessAIPartnerClient {
	return &businessAIPartnerClient{cc}
}

func (c *businessAIPartnerClient) GetPartnerInfo(ctx context.Context, in *GetPartnerInfoReq, opts ...grpc.CallOption) (*GetPartnerInfoResp, error) {
	out := new(GetPartnerInfoResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetPartnerInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) RoleSwitch(ctx context.Context, in *RoleSwitchReq, opts ...grpc.CallOption) (*RoleSwitchResp, error) {
	out := new(RoleSwitchResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/RoleSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetPhotographs(ctx context.Context, in *GetPhotographsReq, opts ...grpc.CallOption) (*GetPhotographsResp, error) {
	out := new(GetPhotographsResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetPhotographs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetAtmosphere(ctx context.Context, in *GetAtmosphereReq, opts ...grpc.CallOption) (*GetAtmosphereResp, error) {
	out := new(GetAtmosphereResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetAtmosphere", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) CreateRole(ctx context.Context, in *CreateRoleReq, opts ...grpc.CallOption) (*CreateRoleResp, error) {
	out := new(CreateRoleResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/CreateRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) UpdateRole(ctx context.Context, in *UpdateRoleReq, opts ...grpc.CallOption) (*UpdateRoleResp, error) {
	out := new(UpdateRoleResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/UpdateRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) BatchGetPartnerInfo(ctx context.Context, in *BatchGetPartnerInfoReq, opts ...grpc.CallOption) (*BatchGetPartnerInfoResp, error) {
	out := new(BatchGetPartnerInfoResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/BatchGetPartnerInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetRole(ctx context.Context, in *GetRoleReq, opts ...grpc.CallOption) (*GetRoleResp, error) {
	out := new(GetRoleResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetInsertPosRoles(ctx context.Context, in *GetInsertPosRolesReq, opts ...grpc.CallOption) (*GetInsertPosRolesResp, error) {
	out := new(GetInsertPosRolesResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetInsertPosRoles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) SetAIPartnerInfo(ctx context.Context, in *SetAIPartnerInfoReq, opts ...grpc.CallOption) (*SetAIPartnerInfoResp, error) {
	out := new(SetAIPartnerInfoResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/SetAIPartnerInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) CheckRolesExist(ctx context.Context, in *CheckRolesExistReq, opts ...grpc.CallOption) (*CheckRolesExistResp, error) {
	out := new(CheckRolesExistResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/CheckRolesExist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) BatchGetRoles(ctx context.Context, in *BatchGetRolesReq, opts ...grpc.CallOption) (*BatchGetRolesResp, error) {
	out := new(BatchGetRolesResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/BatchGetRoles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetGameInfo(ctx context.Context, in *GetGameInfoReq, opts ...grpc.CallOption) (*GetGameInfoResp, error) {
	out := new(GetGameInfoResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetRoleLike(ctx context.Context, in *GetRoleLikeReq, opts ...grpc.CallOption) (*GetRoleLikeResp, error) {
	out := new(GetRoleLikeResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetRoleLike", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetRoleFeature(ctx context.Context, in *GetRoleFeatureReq, opts ...grpc.CallOption) (*GetRoleFeatureResp, error) {
	out := new(GetRoleFeatureResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetRoleFeature", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) Test(ctx context.Context, in *TestReq, opts ...grpc.CallOption) (*TestResp, error) {
	out := new(TestResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/Test", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetVoiceJson(ctx context.Context, in *GetVoiceJsonReq, opts ...grpc.CallOption) (*GetVoiceJsonResp, error) {
	out := new(GetVoiceJsonResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetVoiceJson", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetReportRole(ctx context.Context, in *GetReportRoleReq, opts ...grpc.CallOption) (*GetReportRoleResp, error) {
	out := new(GetReportRoleResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetReportRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) ReportRole(ctx context.Context, in *ReportRoleReq, opts ...grpc.CallOption) (*ReportRoleResp, error) {
	out := new(ReportRoleResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/ReportRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) ReadPartnerHeart(ctx context.Context, in *ReadPartnerHeartReq, opts ...grpc.CallOption) (*ReadPartnerHeartResp, error) {
	out := new(ReadPartnerHeartResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/ReadPartnerHeart", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) BatchGetRoleExtra(ctx context.Context, in *BatchGetRoleExtraReq, opts ...grpc.CallOption) (*BatchGetRoleExtraResp, error) {
	out := new(BatchGetRoleExtraResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/BatchGetRoleExtra", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) BatchGetRoleInteractive(ctx context.Context, in *BatchGetRoleInteractiveReq, opts ...grpc.CallOption) (*BatchGetRoleInteractiveResp, error) {
	out := new(BatchGetRoleInteractiveResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/BatchGetRoleInteractive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetRoleInteractive(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetRoleInteractive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) CheckAIRoomVisibilityHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/CheckAIRoomVisibilityHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetPartnerInfoHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetPartnerInfoHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetRecordsHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetRecordsHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetRelationshipListHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetRelationshipListHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) ModifyRelationshipHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/ModifyRelationshipHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) SetUserChatSceneHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/SetUserChatSceneHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) CheckChatSceneVisibilityHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/CheckChatSceneVisibilityHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetReadSeqHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetReadSeqHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) MarkReadSeqHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/MarkReadSeqHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetPhotographsHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetPhotographsHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) SetPhotographHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/SetPhotographHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetConfigList(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) AddListenDuration(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/AddListenDuration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetListenDuration(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetListenDuration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetRoleTipInfo(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetRoleTipInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetStoryInfo(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) StartStory(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/StartStory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) SetStoryProgress(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/SetStoryProgress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetStoryScene(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryScene", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetStoryReply(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryReply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetStoryHistory(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetUserSettings(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetUserSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) SetAutoPlayStatus(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/SetAutoPlayStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetVoiceList(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetVoiceList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetVoiceListV2(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetVoiceListV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetCustomVoiceList(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetCustomVoiceList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GenCustomVoice(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GenCustomVoice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetGenCustomVoiceStatus(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetGenCustomVoiceStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GenRoleDesc(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GenRoleDesc", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GenRecommendReply(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GenRecommendReply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetGenStatus(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetGenStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) NewTriggerHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/NewTriggerHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetUserMsgStat(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetUserMsgStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) UserReadMsgNotify(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/UserReadMsgNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) Feedback(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/Feedback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetQuickReplyList(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetQuickReplyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetGameList(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) StartGame(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/StartGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) InputHint(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/InputHint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetPartnerSettings(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetPartnerSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) SetPartnerSilent(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/SetPartnerSilent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) SetPartnerAutoPlayStatusHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/SetPartnerAutoPlayStatusHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) CheckIfTriggerChatHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/CheckIfTriggerChatHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetTriggerChatRoleHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetTriggerChatRoleHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetStoryBookList(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryBookList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetStoryBook(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryBook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetStoryBookNotifySeq(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryBookNotifySeq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) MarkStoryBookNotifySeq(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/MarkStoryBookNotifySeq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) StartStoryBook(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/StartStoryBook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetStoryBookProgress(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryBookProgress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetStoryBookReply(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryBookReply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetStoryBookHistory(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryBookHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) AddWhiteList(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/AddWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) StartVoiceChat(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/StartVoiceChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) SetVoice(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/SetVoice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) VoiceChatEntrance(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/VoiceChatEntrance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) EnterPetHome(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/EnterPetHome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) ShowPartnerSilent(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/ShowPartnerSilent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) ShowContinueChat(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/ShowContinueChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) ContinueChat(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/ContinueChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetTarotConfig(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetTarotConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) OpenTarotCard(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/OpenTarotCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetTarotResult(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetTarotResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetPetOwnDay(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetPetOwnDay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetPetStoryNum(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetPetStoryNum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) StartPetStory(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/StartPetStory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) StopPetStory(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/StopPetStory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) EnterPetSleepTalk(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/EnterPetSleepTalk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetVibesConfig(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetVibesConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) AddAccompanyTime(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/AddAccompanyTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetAccompanyTime(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetAccompanyTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetTargetList(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetTargetList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetTargetReward(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetTargetReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetChatNum(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetChatNum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GenTTSDataHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GenTTSDataHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) MarkVoiceMsgAsPlayed(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/MarkVoiceMsgAsPlayed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetOutGames(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetOutGames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetGameTopics(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetGameTopics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetTopicGames(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetTopicGames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) GetRoleGameInfo(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/GetRoleGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) PostsExplosureNotifyHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/PostsExplosureNotifyHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessAIPartnerClient) ChatHistoryAppendHttp(ctx context.Context, in *cmd_gateway.CommandReq, opts ...grpc.CallOption) (*cmd_gateway.CommandResp, error) {
	out := new(cmd_gateway.CommandResp)
	err := c.cc.Invoke(ctx, "/rcmd.business_ai_partner.BusinessAIPartner/ChatHistoryAppendHttp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessAIPartnerServer is the server API for BusinessAIPartner service.
type BusinessAIPartnerServer interface {
	GetPartnerInfo(context.Context, *GetPartnerInfoReq) (*GetPartnerInfoResp, error)
	RoleSwitch(context.Context, *RoleSwitchReq) (*RoleSwitchResp, error)
	GetPhotographs(context.Context, *GetPhotographsReq) (*GetPhotographsResp, error)
	GetAtmosphere(context.Context, *GetAtmosphereReq) (*GetAtmosphereResp, error)
	CreateRole(context.Context, *CreateRoleReq) (*CreateRoleResp, error)
	UpdateRole(context.Context, *UpdateRoleReq) (*UpdateRoleResp, error)
	BatchGetPartnerInfo(context.Context, *BatchGetPartnerInfoReq) (*BatchGetPartnerInfoResp, error)
	GetRole(context.Context, *GetRoleReq) (*GetRoleResp, error)
	GetInsertPosRoles(context.Context, *GetInsertPosRolesReq) (*GetInsertPosRolesResp, error)
	SetAIPartnerInfo(context.Context, *SetAIPartnerInfoReq) (*SetAIPartnerInfoResp, error)
	CheckRolesExist(context.Context, *CheckRolesExistReq) (*CheckRolesExistResp, error)
	BatchGetRoles(context.Context, *BatchGetRolesReq) (*BatchGetRolesResp, error)
	GetGameInfo(context.Context, *GetGameInfoReq) (*GetGameInfoResp, error)
	GetRoleLike(context.Context, *GetRoleLikeReq) (*GetRoleLikeResp, error)
	GetRoleFeature(context.Context, *GetRoleFeatureReq) (*GetRoleFeatureResp, error)
	Test(context.Context, *TestReq) (*TestResp, error)
	GetVoiceJson(context.Context, *GetVoiceJsonReq) (*GetVoiceJsonResp, error)
	GetReportRole(context.Context, *GetReportRoleReq) (*GetReportRoleResp, error)
	ReportRole(context.Context, *ReportRoleReq) (*ReportRoleResp, error)
	ReadPartnerHeart(context.Context, *ReadPartnerHeartReq) (*ReadPartnerHeartResp, error)
	BatchGetRoleExtra(context.Context, *BatchGetRoleExtraReq) (*BatchGetRoleExtraResp, error)
	BatchGetRoleInteractive(context.Context, *BatchGetRoleInteractiveReq) (*BatchGetRoleInteractiveResp, error)
	GetRoleInteractive(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	CheckAIRoomVisibilityHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// for http
	GetPartnerInfoHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetRecordsHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetRelationshipListHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	ModifyRelationshipHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	SetUserChatSceneHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	CheckChatSceneVisibilityHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetReadSeqHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	MarkReadSeqHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetPhotographsHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	SetPhotographHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetConfigList(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	AddListenDuration(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetListenDuration(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetRoleTipInfo(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetStoryInfo(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	StartStory(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	SetStoryProgress(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetStoryScene(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetStoryReply(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetStoryHistory(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetUserSettings(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	SetAutoPlayStatus(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取音色列表
	GetVoiceList(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取音色列表
	GetVoiceListV2(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取自定义音色候选列表
	GetCustomVoiceList(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 生成自定义音色
	GenCustomVoice(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取生成自定义音色状态
	GetGenCustomVoiceStatus(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 生成角色人设说明(角色设定一键生成)
	GenRoleDesc(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 推荐回复生成
	GenRecommendReply(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取角色设定一键生成次数限制（用于前端是否显示一键生成按钮）
	GetGenStatus(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	NewTriggerHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetUserMsgStat(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	UserReadMsgNotify(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	Feedback(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 快捷回复(一键回复)
	GetQuickReplyList(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetGameList(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	StartGame(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	InputHint(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetPartnerSettings(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	SetPartnerSilent(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	SetPartnerAutoPlayStatusHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	CheckIfTriggerChatHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetTriggerChatRoleHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取故事书列表
	GetStoryBookList(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	GetStoryBook(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 常驻入口红点
	GetStoryBookNotifySeq(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 常驻红点已读
	MarkStoryBookNotifySeq(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 开始故事章节
	StartStoryBook(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取故事进度
	GetStoryBookProgress(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取故事用户回复
	GetStoryBookReply(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取故事历史记录
	GetStoryBookHistory(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 加白
	AddWhiteList(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	StartVoiceChat(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	SetVoice(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	VoiceChatEntrance(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	EnterPetHome(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 是否显示免打扰
	ShowPartnerSilent(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 是否显示继续说
	ShowContinueChat(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 继续说
	ContinueChat(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取塔罗配置
	GetTarotConfig(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 翻开塔罗牌
	OpenTarotCard(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取某些日期的塔罗结果
	GetTarotResult(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取宠物陪伴天数
	GetPetOwnDay(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取听故事次数
	GetPetStoryNum(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 开始桌宠故事
	StartPetStory(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 结束桌宠故事
	StopPetStory(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 进入桌宠哄睡场景
	EnterPetSleepTalk(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取氛围配置
	GetVibesConfig(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 增加陪伴时间
	AddAccompanyTime(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取陪伴时间
	GetAccompanyTime(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取目标列表
	GetTargetList(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 领取目标奖励
	GetTargetReward(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取聊天次数
	GetChatNum(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取 tts 数据
	GenTTSDataHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 标记语音消息已播放
	MarkVoiceMsgAsPlayed(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取外显玩法
	GetOutGames(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取主题列表
	GetGameTopics(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取某主题玩法列表
	GetTopicGames(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 获取玩法信息
	GetRoleGameInfo(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	PostsExplosureNotifyHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
	// 聊天记录追加(续聊)
	ChatHistoryAppendHttp(context.Context, *cmd_gateway.CommandReq) (*cmd_gateway.CommandResp, error)
}

func RegisterBusinessAIPartnerServer(s *grpc.Server, srv BusinessAIPartnerServer) {
	s.RegisterService(&_BusinessAIPartner_serviceDesc, srv)
}

func _BusinessAIPartner_GetPartnerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPartnerInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetPartnerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetPartnerInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetPartnerInfo(ctx, req.(*GetPartnerInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_RoleSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RoleSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).RoleSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/RoleSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).RoleSwitch(ctx, req.(*RoleSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetPhotographs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPhotographsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetPhotographs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetPhotographs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetPhotographs(ctx, req.(*GetPhotographsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetAtmosphere_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAtmosphereReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetAtmosphere(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetAtmosphere",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetAtmosphere(ctx, req.(*GetAtmosphereReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_CreateRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).CreateRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/CreateRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).CreateRole(ctx, req.(*CreateRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_UpdateRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).UpdateRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/UpdateRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).UpdateRole(ctx, req.(*UpdateRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_BatchGetPartnerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetPartnerInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).BatchGetPartnerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/BatchGetPartnerInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).BatchGetPartnerInfo(ctx, req.(*BatchGetPartnerInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetRole(ctx, req.(*GetRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetInsertPosRoles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInsertPosRolesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetInsertPosRoles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetInsertPosRoles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetInsertPosRoles(ctx, req.(*GetInsertPosRolesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_SetAIPartnerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAIPartnerInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).SetAIPartnerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/SetAIPartnerInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).SetAIPartnerInfo(ctx, req.(*SetAIPartnerInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_CheckRolesExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckRolesExistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).CheckRolesExist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/CheckRolesExist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).CheckRolesExist(ctx, req.(*CheckRolesExistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_BatchGetRoles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetRolesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).BatchGetRoles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/BatchGetRoles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).BatchGetRoles(ctx, req.(*BatchGetRolesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetGameInfo(ctx, req.(*GetGameInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetRoleLike_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRoleLikeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetRoleLike(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetRoleLike",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetRoleLike(ctx, req.(*GetRoleLikeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetRoleFeature_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRoleFeatureReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetRoleFeature(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetRoleFeature",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetRoleFeature(ctx, req.(*GetRoleFeatureReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_Test_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).Test(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/Test",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).Test(ctx, req.(*TestReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetVoiceJson_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVoiceJsonReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetVoiceJson(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetVoiceJson",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetVoiceJson(ctx, req.(*GetVoiceJsonReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetReportRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReportRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetReportRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetReportRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetReportRole(ctx, req.(*GetReportRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_ReportRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).ReportRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/ReportRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).ReportRole(ctx, req.(*ReportRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_ReadPartnerHeart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadPartnerHeartReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).ReadPartnerHeart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/ReadPartnerHeart",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).ReadPartnerHeart(ctx, req.(*ReadPartnerHeartReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_BatchGetRoleExtra_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetRoleExtraReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).BatchGetRoleExtra(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/BatchGetRoleExtra",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).BatchGetRoleExtra(ctx, req.(*BatchGetRoleExtraReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_BatchGetRoleInteractive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetRoleInteractiveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).BatchGetRoleInteractive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/BatchGetRoleInteractive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).BatchGetRoleInteractive(ctx, req.(*BatchGetRoleInteractiveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetRoleInteractive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetRoleInteractive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetRoleInteractive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetRoleInteractive(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_CheckAIRoomVisibilityHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).CheckAIRoomVisibilityHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/CheckAIRoomVisibilityHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).CheckAIRoomVisibilityHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetPartnerInfoHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetPartnerInfoHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetPartnerInfoHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetPartnerInfoHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetRecordsHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetRecordsHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetRecordsHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetRecordsHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetRelationshipListHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetRelationshipListHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetRelationshipListHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetRelationshipListHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_ModifyRelationshipHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).ModifyRelationshipHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/ModifyRelationshipHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).ModifyRelationshipHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_SetUserChatSceneHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).SetUserChatSceneHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/SetUserChatSceneHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).SetUserChatSceneHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_CheckChatSceneVisibilityHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).CheckChatSceneVisibilityHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/CheckChatSceneVisibilityHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).CheckChatSceneVisibilityHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetReadSeqHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetReadSeqHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetReadSeqHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetReadSeqHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_MarkReadSeqHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).MarkReadSeqHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/MarkReadSeqHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).MarkReadSeqHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetPhotographsHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetPhotographsHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetPhotographsHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetPhotographsHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_SetPhotographHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).SetPhotographHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/SetPhotographHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).SetPhotographHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetConfigList(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_AddListenDuration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).AddListenDuration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/AddListenDuration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).AddListenDuration(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetListenDuration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetListenDuration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetListenDuration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetListenDuration(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetRoleTipInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetRoleTipInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetRoleTipInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetRoleTipInfo(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetStoryInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetStoryInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetStoryInfo(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_StartStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).StartStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/StartStory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).StartStory(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_SetStoryProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).SetStoryProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/SetStoryProgress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).SetStoryProgress(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetStoryScene_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetStoryScene(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryScene",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetStoryScene(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetStoryReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetStoryReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryReply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetStoryReply(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetStoryHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetStoryHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetStoryHistory(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetUserSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetUserSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetUserSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetUserSettings(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_SetAutoPlayStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).SetAutoPlayStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/SetAutoPlayStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).SetAutoPlayStatus(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetVoiceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetVoiceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetVoiceList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetVoiceList(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetVoiceListV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetVoiceListV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetVoiceListV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetVoiceListV2(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetCustomVoiceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetCustomVoiceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetCustomVoiceList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetCustomVoiceList(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GenCustomVoice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GenCustomVoice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GenCustomVoice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GenCustomVoice(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetGenCustomVoiceStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetGenCustomVoiceStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetGenCustomVoiceStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetGenCustomVoiceStatus(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GenRoleDesc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GenRoleDesc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GenRoleDesc",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GenRoleDesc(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GenRecommendReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GenRecommendReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GenRecommendReply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GenRecommendReply(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetGenStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetGenStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetGenStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetGenStatus(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_NewTriggerHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).NewTriggerHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/NewTriggerHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).NewTriggerHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetUserMsgStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetUserMsgStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetUserMsgStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetUserMsgStat(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_UserReadMsgNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).UserReadMsgNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/UserReadMsgNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).UserReadMsgNotify(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_Feedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).Feedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/Feedback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).Feedback(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetQuickReplyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetQuickReplyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetQuickReplyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetQuickReplyList(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetGameList(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_StartGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).StartGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/StartGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).StartGame(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_InputHint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).InputHint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/InputHint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).InputHint(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetPartnerSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetPartnerSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetPartnerSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetPartnerSettings(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_SetPartnerSilent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).SetPartnerSilent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/SetPartnerSilent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).SetPartnerSilent(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_SetPartnerAutoPlayStatusHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).SetPartnerAutoPlayStatusHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/SetPartnerAutoPlayStatusHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).SetPartnerAutoPlayStatusHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_CheckIfTriggerChatHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).CheckIfTriggerChatHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/CheckIfTriggerChatHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).CheckIfTriggerChatHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetTriggerChatRoleHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetTriggerChatRoleHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetTriggerChatRoleHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetTriggerChatRoleHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetStoryBookList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetStoryBookList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryBookList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetStoryBookList(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetStoryBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetStoryBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryBook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetStoryBook(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetStoryBookNotifySeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetStoryBookNotifySeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryBookNotifySeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetStoryBookNotifySeq(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_MarkStoryBookNotifySeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).MarkStoryBookNotifySeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/MarkStoryBookNotifySeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).MarkStoryBookNotifySeq(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_StartStoryBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).StartStoryBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/StartStoryBook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).StartStoryBook(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetStoryBookProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetStoryBookProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryBookProgress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetStoryBookProgress(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetStoryBookReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetStoryBookReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryBookReply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetStoryBookReply(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetStoryBookHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetStoryBookHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetStoryBookHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetStoryBookHistory(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_AddWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).AddWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/AddWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).AddWhiteList(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_StartVoiceChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).StartVoiceChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/StartVoiceChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).StartVoiceChat(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_SetVoice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).SetVoice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/SetVoice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).SetVoice(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_VoiceChatEntrance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).VoiceChatEntrance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/VoiceChatEntrance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).VoiceChatEntrance(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_EnterPetHome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).EnterPetHome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/EnterPetHome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).EnterPetHome(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_ShowPartnerSilent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).ShowPartnerSilent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/ShowPartnerSilent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).ShowPartnerSilent(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_ShowContinueChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).ShowContinueChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/ShowContinueChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).ShowContinueChat(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_ContinueChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).ContinueChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/ContinueChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).ContinueChat(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetTarotConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetTarotConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetTarotConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetTarotConfig(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_OpenTarotCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).OpenTarotCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/OpenTarotCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).OpenTarotCard(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetTarotResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetTarotResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetTarotResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetTarotResult(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetPetOwnDay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetPetOwnDay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetPetOwnDay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetPetOwnDay(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetPetStoryNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetPetStoryNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetPetStoryNum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetPetStoryNum(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_StartPetStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).StartPetStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/StartPetStory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).StartPetStory(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_StopPetStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).StopPetStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/StopPetStory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).StopPetStory(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_EnterPetSleepTalk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).EnterPetSleepTalk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/EnterPetSleepTalk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).EnterPetSleepTalk(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetVibesConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetVibesConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetVibesConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetVibesConfig(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_AddAccompanyTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).AddAccompanyTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/AddAccompanyTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).AddAccompanyTime(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetAccompanyTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetAccompanyTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetAccompanyTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetAccompanyTime(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetTargetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetTargetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetTargetList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetTargetList(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetTargetReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetTargetReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetTargetReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetTargetReward(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetChatNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetChatNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetChatNum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetChatNum(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GenTTSDataHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GenTTSDataHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GenTTSDataHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GenTTSDataHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_MarkVoiceMsgAsPlayed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).MarkVoiceMsgAsPlayed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/MarkVoiceMsgAsPlayed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).MarkVoiceMsgAsPlayed(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetOutGames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetOutGames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetOutGames",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetOutGames(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetGameTopics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetGameTopics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetGameTopics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetGameTopics(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetTopicGames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetTopicGames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetTopicGames",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetTopicGames(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_GetRoleGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).GetRoleGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/GetRoleGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).GetRoleGameInfo(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_PostsExplosureNotifyHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).PostsExplosureNotifyHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/PostsExplosureNotifyHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).PostsExplosureNotifyHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessAIPartner_ChatHistoryAppendHttp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cmd_gateway.CommandReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessAIPartnerServer).ChatHistoryAppendHttp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.business_ai_partner.BusinessAIPartner/ChatHistoryAppendHttp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessAIPartnerServer).ChatHistoryAppendHttp(ctx, req.(*cmd_gateway.CommandReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _BusinessAIPartner_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.business_ai_partner.BusinessAIPartner",
	HandlerType: (*BusinessAIPartnerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPartnerInfo",
			Handler:    _BusinessAIPartner_GetPartnerInfo_Handler,
		},
		{
			MethodName: "RoleSwitch",
			Handler:    _BusinessAIPartner_RoleSwitch_Handler,
		},
		{
			MethodName: "GetPhotographs",
			Handler:    _BusinessAIPartner_GetPhotographs_Handler,
		},
		{
			MethodName: "GetAtmosphere",
			Handler:    _BusinessAIPartner_GetAtmosphere_Handler,
		},
		{
			MethodName: "CreateRole",
			Handler:    _BusinessAIPartner_CreateRole_Handler,
		},
		{
			MethodName: "UpdateRole",
			Handler:    _BusinessAIPartner_UpdateRole_Handler,
		},
		{
			MethodName: "BatchGetPartnerInfo",
			Handler:    _BusinessAIPartner_BatchGetPartnerInfo_Handler,
		},
		{
			MethodName: "GetRole",
			Handler:    _BusinessAIPartner_GetRole_Handler,
		},
		{
			MethodName: "GetInsertPosRoles",
			Handler:    _BusinessAIPartner_GetInsertPosRoles_Handler,
		},
		{
			MethodName: "SetAIPartnerInfo",
			Handler:    _BusinessAIPartner_SetAIPartnerInfo_Handler,
		},
		{
			MethodName: "CheckRolesExist",
			Handler:    _BusinessAIPartner_CheckRolesExist_Handler,
		},
		{
			MethodName: "BatchGetRoles",
			Handler:    _BusinessAIPartner_BatchGetRoles_Handler,
		},
		{
			MethodName: "GetGameInfo",
			Handler:    _BusinessAIPartner_GetGameInfo_Handler,
		},
		{
			MethodName: "GetRoleLike",
			Handler:    _BusinessAIPartner_GetRoleLike_Handler,
		},
		{
			MethodName: "GetRoleFeature",
			Handler:    _BusinessAIPartner_GetRoleFeature_Handler,
		},
		{
			MethodName: "Test",
			Handler:    _BusinessAIPartner_Test_Handler,
		},
		{
			MethodName: "GetVoiceJson",
			Handler:    _BusinessAIPartner_GetVoiceJson_Handler,
		},
		{
			MethodName: "GetReportRole",
			Handler:    _BusinessAIPartner_GetReportRole_Handler,
		},
		{
			MethodName: "ReportRole",
			Handler:    _BusinessAIPartner_ReportRole_Handler,
		},
		{
			MethodName: "ReadPartnerHeart",
			Handler:    _BusinessAIPartner_ReadPartnerHeart_Handler,
		},
		{
			MethodName: "BatchGetRoleExtra",
			Handler:    _BusinessAIPartner_BatchGetRoleExtra_Handler,
		},
		{
			MethodName: "BatchGetRoleInteractive",
			Handler:    _BusinessAIPartner_BatchGetRoleInteractive_Handler,
		},
		{
			MethodName: "GetRoleInteractive",
			Handler:    _BusinessAIPartner_GetRoleInteractive_Handler,
		},
		{
			MethodName: "CheckAIRoomVisibilityHttp",
			Handler:    _BusinessAIPartner_CheckAIRoomVisibilityHttp_Handler,
		},
		{
			MethodName: "GetPartnerInfoHttp",
			Handler:    _BusinessAIPartner_GetPartnerInfoHttp_Handler,
		},
		{
			MethodName: "GetRecordsHttp",
			Handler:    _BusinessAIPartner_GetRecordsHttp_Handler,
		},
		{
			MethodName: "GetRelationshipListHttp",
			Handler:    _BusinessAIPartner_GetRelationshipListHttp_Handler,
		},
		{
			MethodName: "ModifyRelationshipHttp",
			Handler:    _BusinessAIPartner_ModifyRelationshipHttp_Handler,
		},
		{
			MethodName: "SetUserChatSceneHttp",
			Handler:    _BusinessAIPartner_SetUserChatSceneHttp_Handler,
		},
		{
			MethodName: "CheckChatSceneVisibilityHttp",
			Handler:    _BusinessAIPartner_CheckChatSceneVisibilityHttp_Handler,
		},
		{
			MethodName: "GetReadSeqHttp",
			Handler:    _BusinessAIPartner_GetReadSeqHttp_Handler,
		},
		{
			MethodName: "MarkReadSeqHttp",
			Handler:    _BusinessAIPartner_MarkReadSeqHttp_Handler,
		},
		{
			MethodName: "GetPhotographsHttp",
			Handler:    _BusinessAIPartner_GetPhotographsHttp_Handler,
		},
		{
			MethodName: "SetPhotographHttp",
			Handler:    _BusinessAIPartner_SetPhotographHttp_Handler,
		},
		{
			MethodName: "GetConfigList",
			Handler:    _BusinessAIPartner_GetConfigList_Handler,
		},
		{
			MethodName: "AddListenDuration",
			Handler:    _BusinessAIPartner_AddListenDuration_Handler,
		},
		{
			MethodName: "GetListenDuration",
			Handler:    _BusinessAIPartner_GetListenDuration_Handler,
		},
		{
			MethodName: "GetRoleTipInfo",
			Handler:    _BusinessAIPartner_GetRoleTipInfo_Handler,
		},
		{
			MethodName: "GetStoryInfo",
			Handler:    _BusinessAIPartner_GetStoryInfo_Handler,
		},
		{
			MethodName: "StartStory",
			Handler:    _BusinessAIPartner_StartStory_Handler,
		},
		{
			MethodName: "SetStoryProgress",
			Handler:    _BusinessAIPartner_SetStoryProgress_Handler,
		},
		{
			MethodName: "GetStoryScene",
			Handler:    _BusinessAIPartner_GetStoryScene_Handler,
		},
		{
			MethodName: "GetStoryReply",
			Handler:    _BusinessAIPartner_GetStoryReply_Handler,
		},
		{
			MethodName: "GetStoryHistory",
			Handler:    _BusinessAIPartner_GetStoryHistory_Handler,
		},
		{
			MethodName: "GetUserSettings",
			Handler:    _BusinessAIPartner_GetUserSettings_Handler,
		},
		{
			MethodName: "SetAutoPlayStatus",
			Handler:    _BusinessAIPartner_SetAutoPlayStatus_Handler,
		},
		{
			MethodName: "GetVoiceList",
			Handler:    _BusinessAIPartner_GetVoiceList_Handler,
		},
		{
			MethodName: "GetVoiceListV2",
			Handler:    _BusinessAIPartner_GetVoiceListV2_Handler,
		},
		{
			MethodName: "GetCustomVoiceList",
			Handler:    _BusinessAIPartner_GetCustomVoiceList_Handler,
		},
		{
			MethodName: "GenCustomVoice",
			Handler:    _BusinessAIPartner_GenCustomVoice_Handler,
		},
		{
			MethodName: "GetGenCustomVoiceStatus",
			Handler:    _BusinessAIPartner_GetGenCustomVoiceStatus_Handler,
		},
		{
			MethodName: "GenRoleDesc",
			Handler:    _BusinessAIPartner_GenRoleDesc_Handler,
		},
		{
			MethodName: "GenRecommendReply",
			Handler:    _BusinessAIPartner_GenRecommendReply_Handler,
		},
		{
			MethodName: "GetGenStatus",
			Handler:    _BusinessAIPartner_GetGenStatus_Handler,
		},
		{
			MethodName: "NewTriggerHttp",
			Handler:    _BusinessAIPartner_NewTriggerHttp_Handler,
		},
		{
			MethodName: "GetUserMsgStat",
			Handler:    _BusinessAIPartner_GetUserMsgStat_Handler,
		},
		{
			MethodName: "UserReadMsgNotify",
			Handler:    _BusinessAIPartner_UserReadMsgNotify_Handler,
		},
		{
			MethodName: "Feedback",
			Handler:    _BusinessAIPartner_Feedback_Handler,
		},
		{
			MethodName: "GetQuickReplyList",
			Handler:    _BusinessAIPartner_GetQuickReplyList_Handler,
		},
		{
			MethodName: "GetGameList",
			Handler:    _BusinessAIPartner_GetGameList_Handler,
		},
		{
			MethodName: "StartGame",
			Handler:    _BusinessAIPartner_StartGame_Handler,
		},
		{
			MethodName: "InputHint",
			Handler:    _BusinessAIPartner_InputHint_Handler,
		},
		{
			MethodName: "GetPartnerSettings",
			Handler:    _BusinessAIPartner_GetPartnerSettings_Handler,
		},
		{
			MethodName: "SetPartnerSilent",
			Handler:    _BusinessAIPartner_SetPartnerSilent_Handler,
		},
		{
			MethodName: "SetPartnerAutoPlayStatusHttp",
			Handler:    _BusinessAIPartner_SetPartnerAutoPlayStatusHttp_Handler,
		},
		{
			MethodName: "CheckIfTriggerChatHttp",
			Handler:    _BusinessAIPartner_CheckIfTriggerChatHttp_Handler,
		},
		{
			MethodName: "GetTriggerChatRoleHttp",
			Handler:    _BusinessAIPartner_GetTriggerChatRoleHttp_Handler,
		},
		{
			MethodName: "GetStoryBookList",
			Handler:    _BusinessAIPartner_GetStoryBookList_Handler,
		},
		{
			MethodName: "GetStoryBook",
			Handler:    _BusinessAIPartner_GetStoryBook_Handler,
		},
		{
			MethodName: "GetStoryBookNotifySeq",
			Handler:    _BusinessAIPartner_GetStoryBookNotifySeq_Handler,
		},
		{
			MethodName: "MarkStoryBookNotifySeq",
			Handler:    _BusinessAIPartner_MarkStoryBookNotifySeq_Handler,
		},
		{
			MethodName: "StartStoryBook",
			Handler:    _BusinessAIPartner_StartStoryBook_Handler,
		},
		{
			MethodName: "GetStoryBookProgress",
			Handler:    _BusinessAIPartner_GetStoryBookProgress_Handler,
		},
		{
			MethodName: "GetStoryBookReply",
			Handler:    _BusinessAIPartner_GetStoryBookReply_Handler,
		},
		{
			MethodName: "GetStoryBookHistory",
			Handler:    _BusinessAIPartner_GetStoryBookHistory_Handler,
		},
		{
			MethodName: "AddWhiteList",
			Handler:    _BusinessAIPartner_AddWhiteList_Handler,
		},
		{
			MethodName: "StartVoiceChat",
			Handler:    _BusinessAIPartner_StartVoiceChat_Handler,
		},
		{
			MethodName: "SetVoice",
			Handler:    _BusinessAIPartner_SetVoice_Handler,
		},
		{
			MethodName: "VoiceChatEntrance",
			Handler:    _BusinessAIPartner_VoiceChatEntrance_Handler,
		},
		{
			MethodName: "EnterPetHome",
			Handler:    _BusinessAIPartner_EnterPetHome_Handler,
		},
		{
			MethodName: "ShowPartnerSilent",
			Handler:    _BusinessAIPartner_ShowPartnerSilent_Handler,
		},
		{
			MethodName: "ShowContinueChat",
			Handler:    _BusinessAIPartner_ShowContinueChat_Handler,
		},
		{
			MethodName: "ContinueChat",
			Handler:    _BusinessAIPartner_ContinueChat_Handler,
		},
		{
			MethodName: "GetTarotConfig",
			Handler:    _BusinessAIPartner_GetTarotConfig_Handler,
		},
		{
			MethodName: "OpenTarotCard",
			Handler:    _BusinessAIPartner_OpenTarotCard_Handler,
		},
		{
			MethodName: "GetTarotResult",
			Handler:    _BusinessAIPartner_GetTarotResult_Handler,
		},
		{
			MethodName: "GetPetOwnDay",
			Handler:    _BusinessAIPartner_GetPetOwnDay_Handler,
		},
		{
			MethodName: "GetPetStoryNum",
			Handler:    _BusinessAIPartner_GetPetStoryNum_Handler,
		},
		{
			MethodName: "StartPetStory",
			Handler:    _BusinessAIPartner_StartPetStory_Handler,
		},
		{
			MethodName: "StopPetStory",
			Handler:    _BusinessAIPartner_StopPetStory_Handler,
		},
		{
			MethodName: "EnterPetSleepTalk",
			Handler:    _BusinessAIPartner_EnterPetSleepTalk_Handler,
		},
		{
			MethodName: "GetVibesConfig",
			Handler:    _BusinessAIPartner_GetVibesConfig_Handler,
		},
		{
			MethodName: "AddAccompanyTime",
			Handler:    _BusinessAIPartner_AddAccompanyTime_Handler,
		},
		{
			MethodName: "GetAccompanyTime",
			Handler:    _BusinessAIPartner_GetAccompanyTime_Handler,
		},
		{
			MethodName: "GetTargetList",
			Handler:    _BusinessAIPartner_GetTargetList_Handler,
		},
		{
			MethodName: "GetTargetReward",
			Handler:    _BusinessAIPartner_GetTargetReward_Handler,
		},
		{
			MethodName: "GetChatNum",
			Handler:    _BusinessAIPartner_GetChatNum_Handler,
		},
		{
			MethodName: "GenTTSDataHttp",
			Handler:    _BusinessAIPartner_GenTTSDataHttp_Handler,
		},
		{
			MethodName: "MarkVoiceMsgAsPlayed",
			Handler:    _BusinessAIPartner_MarkVoiceMsgAsPlayed_Handler,
		},
		{
			MethodName: "GetOutGames",
			Handler:    _BusinessAIPartner_GetOutGames_Handler,
		},
		{
			MethodName: "GetGameTopics",
			Handler:    _BusinessAIPartner_GetGameTopics_Handler,
		},
		{
			MethodName: "GetTopicGames",
			Handler:    _BusinessAIPartner_GetTopicGames_Handler,
		},
		{
			MethodName: "GetRoleGameInfo",
			Handler:    _BusinessAIPartner_GetRoleGameInfo_Handler,
		},
		{
			MethodName: "PostsExplosureNotifyHttp",
			Handler:    _BusinessAIPartner_PostsExplosureNotifyHttp_Handler,
		},
		{
			MethodName: "ChatHistoryAppendHttp",
			Handler:    _BusinessAIPartner_ChatHistoryAppendHttp_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "business-ai-partner/business_ai_partner.proto",
}

func init() {
	proto.RegisterFile("business-ai-partner/business_ai_partner.proto", fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d)
}

var fileDescriptor_business_ai_partner_0b1b8cd1893f4e3d = []byte{
	// 4229 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x5c, 0x4f, 0x73, 0x1b, 0x47,
	0x76, 0x17, 0x40, 0x8a, 0x24, 0x1e, 0x08, 0x70, 0xd8, 0xa4, 0x64, 0x98, 0x96, 0xb2, 0xf2, 0xac,
	0xb5, 0x96, 0x29, 0x8b, 0x8a, 0xe9, 0xb5, 0xb3, 0xf1, 0xba, 0x6a, 0x43, 0x91, 0x12, 0x05, 0x49,
	0xa4, 0xa8, 0x01, 0x24, 0xef, 0x66, 0xb3, 0xc1, 0x36, 0x67, 0x9a, 0xc0, 0x98, 0x83, 0x99, 0xd1,
	0x74, 0x83, 0x14, 0x53, 0x7b, 0xca, 0x21, 0x39, 0xa4, 0x2a, 0x95, 0xef, 0x90, 0x7c, 0x82, 0x1c,
	0x72, 0xcc, 0x21, 0x87, 0xdc, 0x72, 0xcb, 0x35, 0xa7, 0xe4, 0x83, 0xa4, 0xfa, 0xf5, 0xfc, 0x07,
	0x09, 0x40, 0x9a, 0xd9, 0x13, 0xa7, 0x5f, 0xbf, 0xfe, 0xf5, 0xbf, 0xf7, 0xb7, 0xbb, 0x09, 0x78,
	0x70, 0x3c, 0xe2, 0xb6, 0xcb, 0x38, 0x7f, 0x40, 0xed, 0x07, 0x3e, 0x0d, 0x84, 0xcb, 0x82, 0x87,
	0x11, 0xad, 0x47, 0xed, 0x5e, 0x48, 0xdb, 0xf2, 0x03, 0x4f, 0x78, 0xa4, 0x15, 0x98, 0x43, 0x6b,
	0xeb, 0x92, 0xfa, 0x8d, 0xdb, 0xe6, 0xd0, 0x7a, 0xd0, 0xa7, 0x82, 0x9d, 0xd3, 0x8b, 0x87, 0xe6,
	0xd0, 0xea, 0x85, 0xdf, 0xaa, 0xa1, 0xfe, 0x67, 0xb0, 0xf1, 0x88, 0x0a, 0x73, 0xb0, 0xcf, 0x84,
	0xe1, 0x39, 0xac, 0xed, 0x0a, 0x16, 0x50, 0x53, 0xd8, 0x67, 0xcc, 0x60, 0x6f, 0xc9, 0xc7, 0xb0,
	0x14, 0x78, 0x0e, 0xeb, 0xd9, 0x16, 0x6f, 0x55, 0xee, 0xcc, 0xdd, 0x6b, 0x18, 0x8b, 0xb2, 0xdc,
	0xb6, 0xb8, 0xfe, 0x3d, 0xac, 0xe4, 0x1a, 0x90, 0x8f, 0x60, 0x31, 0xe4, 0x6e, 0x55, 0xee, 0x54,
	0xee, 0x35, 0x8c, 0x05, 0xc5, 0x4c, 0x34, 0x98, 0x63, 0xef, 0x44, 0xab, 0x7a, 0xa7, 0x72, 0x6f,
	0xd9, 0x90, 0x9f, 0xfa, 0x1f, 0xe0, 0x93, 0x2b, 0xbb, 0xe5, 0x3e, 0xf9, 0x1d, 0xdc, 0x50, 0x48,
	0x09, 0xbd, 0xe7, 0xd8, 0x5c, 0xe0, 0x20, 0xea, 0xdb, 0x5f, 0x6c, 0x5d, 0x35, 0xdd, 0xad, 0x3c,
	0xda, 0x5a, 0x90, 0x25, 0xbc, 0xb0, 0xb9, 0xd0, 0xbf, 0x82, 0xf5, 0x74, 0xef, 0x8f, 0xdf, 0x89,
	0x80, 0x4e, 0x99, 0xee, 0xb7, 0x50, 0x8b, 0x59, 0xdf, 0x67, 0xa2, 0xbf, 0x83, 0x1b, 0x97, 0x74,
	0xc5, 0x7d, 0xb2, 0x07, 0x75, 0xc4, 0x60, 0x92, 0xc2, 0xc3, 0x89, 0xfd, 0x74, 0xf2, 0xc4, 0x54,
	0x6b, 0x08, 0xa2, 0x4f, 0xae, 0x7f, 0x09, 0x2b, 0xfb, 0x4c, 0xbc, 0xf1, 0x6c, 0x93, 0x3d, 0xe3,
	0x9e, 0x1b, 0x4e, 0xe2, 0x4c, 0x96, 0xa3, 0xd1, 0xd5, 0x8c, 0x45, 0x2c, 0xb7, 0x2d, 0xfd, 0x2b,
	0xd0, 0xb2, 0xdc, 0xdc, 0x27, 0xb7, 0x01, 0x14, 0xfb, 0x8f, 0xdc, 0x73, 0xc3, 0x06, 0xb5, 0xb3,
	0x88, 0x45, 0xff, 0x0c, 0x9b, 0x18, 0xcc, 0xf7, 0x02, 0x9c, 0x80, 0xec, 0x41, 0x83, 0xb9, 0x51,
	0x3c, 0x75, 0xf9, 0xa9, 0x6f, 0xc1, 0x6a, 0x8e, 0x8b, 0xfb, 0x93, 0x56, 0xf3, 0x3b, 0x68, 0x4c,
	0x81, 0x4c, 0xaf, 0x71, 0x35, 0xbd, 0xc6, 0xba, 0x06, 0xcd, 0x6c, 0x47, 0x51, 0xef, 0x9e, 0xc3,
	0x9e, 0x30, 0x2a, 0x46, 0xc1, 0x34, 0xd1, 0xfd, 0xe7, 0x2a, 0xd4, 0x53, 0xdc, 0x57, 0x6f, 0xe7,
	0x97, 0x40, 0x02, 0xe6, 0x33, 0x2a, 0x7a, 0xe6, 0x80, 0x8a, 0xde, 0x39, 0xb3, 0xfb, 0x03, 0xb5,
	0xbb, 0x55, 0x43, 0x53, 0x35, 0xbb, 0x03, 0x2a, 0x7e, 0x40, 0x3a, 0xf9, 0x09, 0xd4, 0x1d, 0xfb,
	0x94, 0x45, 0x6c, 0x73, 0xc8, 0x06, 0x92, 0x14, 0x32, 0xfc, 0x0c, 0x56, 0xe8, 0x59, 0x3f, 0x83,
	0x35, 0x8f, 0x4c, 0x0d, 0x7a, 0xd6, 0x4f, 0x01, 0x6d, 0xc1, 0x1a, 0x7b, 0xe7, 0x7b, 0x7c, 0x14,
	0xb0, 0x9e, 0xe9, 0xd8, 0xe6, 0x69, 0x2f, 0xa0, 0x82, 0xb5, 0xae, 0x23, 0xef, 0x6a, 0x54, 0xb5,
	0x2b, 0x6b, 0x0c, 0x2a, 0x98, 0xc4, 0x15, 0x9e, 0xa0, 0x8e, 0x42, 0x1e, 0x71, 0x16, 0xb4, 0x16,
	0x70, 0x1e, 0x0d, 0x24, 0x4b, 0xe4, 0xd7, 0x9c, 0x05, 0x72, 0x3a, 0x27, 0x52, 0x93, 0x24, 0x9b,
	0xb0, 0xdd, 0xbe, 0x62, 0x5d, 0x44, 0x56, 0x4d, 0xd6, 0xec, 0x86, 0x15, 0x92, 0x5b, 0xff, 0x3d,
	0x90, 0xfc, 0xaa, 0x72, 0x9f, 0x3c, 0x83, 0x06, 0xae, 0xd5, 0x89, 0xa2, 0x45, 0x82, 0x7b, 0x77,
	0xb2, 0xe0, 0x46, 0x08, 0xcb, 0x41, 0x52, 0xe0, 0xfa, 0xbf, 0x54, 0x61, 0xb1, 0xcb, 0xb8, 0x90,
	0xdb, 0xf5, 0x1d, 0xcc, 0x8b, 0x0b, 0x9f, 0xe1, 0x06, 0x34, 0xb7, 0x7f, 0x76, 0x35, 0x5c, 0xd8,
	0x60, 0xab, 0x7b, 0xe1, 0x33, 0x03, 0xdb, 0x90, 0x5f, 0xc0, 0xc7, 0x38, 0x26, 0x5c, 0x7d, 0x2e,
	0xa8, 0xe8, 0x89, 0xc0, 0xee, 0xf7, 0x59, 0xd0, 0x13, 0x1c, 0x77, 0x6b, 0xce, 0x40, 0x73, 0xf2,
	0xc2, 0x3e, 0x65, 0x1d, 0x41, 0x45, 0x57, 0xd5, 0x76, 0xb9, 0x14, 0x12, 0x93, 0x3a, 0x3d, 0x4b,
	0x2e, 0xef, 0x9c, 0xd2, 0x15, 0x93, 0x3a, 0x7b, 0x54, 0x30, 0xfd, 0xef, 0x2b, 0x30, 0x2f, 0xfb,
	0x20, 0x1a, 0x2c, 0xcb, 0xbf, 0xbd, 0xb6, 0x7b, 0x46, 0x1d, 0xdb, 0xd2, 0xae, 0x91, 0x3b, 0x70,
	0x0b, 0x29, 0x21, 0x8e, 0x91, 0x82, 0x3e, 0x1a, 0xf1, 0x81, 0x56, 0x21, 0xb7, 0xe1, 0x63, 0xe4,
	0xd8, 0xa5, 0xce, 0x23, 0x76, 0xe2, 0x05, 0x2c, 0xe2, 0xd9, 0x75, 0x85, 0x56, 0x25, 0x04, 0x9a,
	0x58, 0xdd, 0x61, 0xae, 0x75, 0xc0, 0x0e, 0x78, 0x5f, 0x9b, 0x23, 0x2d, 0x58, 0x47, 0xda, 0x11,
	0x13, 0x92, 0xdc, 0xa5, 0x81, 0x27, 0x64, 0xcd, 0xbc, 0xfe, 0x27, 0xb0, 0xa4, 0x26, 0xcd, 0x7d,
	0x42, 0x60, 0xde, 0xa2, 0x82, 0x86, 0x7a, 0x8a, 0xdf, 0xfa, 0x2f, 0xa1, 0x19, 0x6e, 0x94, 0xec,
	0xe1, 0x3d, 0xb5, 0xc9, 0x47, 0x03, 0x92, 0x34, 0xe6, 0x3e, 0xf9, 0x0b, 0x58, 0x90, 0xb6, 0x32,
	0xb4, 0x06, 0xcd, 0xed, 0x7b, 0x93, 0xf7, 0x56, 0xb6, 0xdb, 0x41, 0x7e, 0x23, 0x6c, 0x47, 0x3e,
	0x81, 0x9a, 0xfa, 0x4a, 0x36, 0x60, 0x49, 0x11, 0xba, 0x5c, 0x7f, 0x08, 0x64, 0x77, 0xc0, 0xcc,
	0x53, 0xd9, 0x96, 0x3f, 0x7e, 0x67, 0xab, 0xfd, 0x9f, 0xa0, 0xae, 0xbf, 0x84, 0xb5, 0xb1, 0x06,
	0xdc, 0x27, 0x9f, 0x41, 0x93, 0xc9, 0x42, 0x2f, 0xd7, 0x6e, 0x19, 0xa9, 0x46, 0xd8, 0xf8, 0x0e,
	0x2e, 0xce, 0x3e, 0x1d, 0xb2, 0xb6, 0x7b, 0xe2, 0xc9, 0x9e, 0x9a, 0x50, 0x8d, 0x2d, 0x63, 0xd5,
	0xb6, 0xf4, 0x7f, 0xaf, 0xe0, 0x12, 0x24, 0x2c, 0xdc, 0xcf, 0xf3, 0xc8, 0x65, 0x77, 0xe9, 0x90,
	0xe1, 0x5c, 0x6a, 0x06, 0x7e, 0x93, 0x9b, 0xb0, 0xe0, 0x07, 0xde, 0xd0, 0x17, 0xa1, 0xe4, 0x84,
	0x25, 0xc9, 0x6b, 0x9b, 0x9e, 0x8b, 0xaa, 0x5d, 0x33, 0xf0, 0x9b, 0x6c, 0xc0, 0x52, 0x3f, 0x60,
	0x4c, 0xea, 0x16, 0xaa, 0x71, 0xcd, 0x88, 0xcb, 0x72, 0xb3, 0x8e, 0xfb, 0x43, 0xd4, 0xd8, 0x9a,
	0x21, 0x3f, 0x65, 0xef, 0xc7, 0x7d, 0xd4, 0xcb, 0x9a, 0x51, 0x3d, 0xee, 0x4b, 0x13, 0xcd, 0x4d,
	0xe6, 0xb2, 0x9e, 0xc5, 0xb8, 0xd9, 0x5a, 0x52, 0x26, 0x1a, 0x29, 0x7b, 0x8c, 0x9b, 0xfa, 0x4d,
	0x58, 0xdf, 0x67, 0xa2, 0xed, 0x72, 0x16, 0x88, 0x23, 0x8f, 0xe3, 0x32, 0x19, 0xec, 0xad, 0xfe,
	0xaf, 0x15, 0x68, 0x64, 0xa8, 0xa9, 0x69, 0x35, 0x70, 0x5a, 0x3f, 0x81, 0xba, 0x49, 0x05, 0xeb,
	0x7b, 0xc1, 0x45, 0x24, 0x19, 0x35, 0x03, 0x22, 0x52, 0xdb, 0x92, 0x3d, 0xdb, 0x88, 0xd0, 0xf3,
	0x3d, 0x8e, 0xf3, 0x6c, 0x18, 0x35, 0x3b, 0xc2, 0x24, 0x2d, 0x58, 0x44, 0x6b, 0xc4, 0x2c, 0x9c,
	0xed, 0x92, 0x11, 0x15, 0xc9, 0x3a, 0x5c, 0x97, 0x8a, 0xa8, 0x8c, 0x56, 0xc3, 0x50, 0x05, 0xf2,
	0x29, 0x2c, 0xd3, 0x91, 0x65, 0x8b, 0x5e, 0xc0, 0xf8, 0xc8, 0x11, 0xa1, 0x95, 0xaa, 0x23, 0xcd,
	0x40, 0x92, 0xfe, 0x23, 0xdc, 0xb8, 0x64, 0x32, 0xdc, 0x27, 0xaf, 0x40, 0x4b, 0x86, 0x82, 0x7b,
	0x1e, 0xd9, 0x9e, 0xcf, 0xaf, 0x96, 0xcf, 0x0c, 0x8e, 0xd1, 0xb4, 0x33, 0xb0, 0xfa, 0x5d, 0x80,
	0x50, 0xf6, 0xa5, 0x5c, 0x5c, 0xe5, 0x05, 0xf4, 0xff, 0xbb, 0x0e, 0x4b, 0x92, 0x69, 0x8f, 0x0a,
	0x3a, 0xb6, 0x84, 0x97, 0x49, 0xc6, 0xf7, 0xb0, 0xc0, 0xbd, 0x51, 0x60, 0x2a, 0x9b, 0xd2, 0xdc,
	0xfe, 0x6c, 0xb2, 0x02, 0x75, 0x90, 0xd7, 0x08, 0xdb, 0x44, 0xca, 0x3b, 0x9f, 0x28, 0xef, 0xb7,
	0xa1, 0x6d, 0xbc, 0x8e, 0x68, 0xfa, 0x64, 0xb4, 0x94, 0x5d, 0xfc, 0xf3, 0x68, 0x13, 0x16, 0xb0,
	0xe1, 0x94, 0xe0, 0x42, 0x9a, 0x2f, 0x16, 0xed, 0x94, 0x06, 0x73, 0x9c, 0xbd, 0x0b, 0x7d, 0x83,
	0xfc, 0x24, 0x5f, 0x80, 0x76, 0x4c, 0xcd, 0xd3, 0x7e, 0xe0, 0x8d, 0x5c, 0xab, 0x67, 0x0f, 0x69,
	0x9f, 0x85, 0xa2, 0xb8, 0x92, 0xd0, 0xdb, 0x92, 0x2c, 0x35, 0x83, 0x9e, 0x51, 0x41, 0x83, 0x56,
	0x4d, 0x69, 0x86, 0x2a, 0x91, 0x5b, 0x50, 0x33, 0x07, 0x54, 0x06, 0x62, 0x2c, 0x68, 0x81, 0x12,
	0xe3, 0x98, 0x20, 0x8d, 0x86, 0xd2, 0x20, 0xb9, 0x03, 0x75, 0xa5, 0x24, 0x8a, 0xd0, 0xb6, 0xc8,
	0x5d, 0x68, 0x86, 0x95, 0x67, 0x2c, 0xe0, 0xd2, 0x36, 0x2d, 0x23, 0x47, 0x43, 0x51, 0xdf, 0x28,
	0x22, 0xd9, 0x84, 0x55, 0xe6, 0xd2, 0x63, 0x87, 0xf5, 0xe4, 0x54, 0x7b, 0x01, 0xf3, 0x9d, 0x8b,
	0x56, 0x03, 0x45, 0x73, 0x45, 0x55, 0x18, 0xe6, 0xd0, 0x32, 0x24, 0x39, 0x2f, 0xfc, 0xcd, 0x31,
	0xe1, 0x27, 0x30, 0x2f, 0x68, 0x9f, 0xb7, 0x56, 0xee, 0xcc, 0xc9, 0xad, 0x95, 0xdf, 0x99, 0xe0,
	0x4a, 0xcb, 0x04, 0x57, 0xa4, 0x0d, 0x0d, 0x57, 0x0c, 0x7a, 0x82, 0xbd, 0x13, 0x2a, 0x56, 0x5d,
	0x9d, 0xc5, 0x33, 0x1e, 0x8a, 0x41, 0x97, 0xbd, 0x13, 0x46, 0xdd, 0x55, 0x1f, 0x32, 0x3e, 0xcd,
	0xa9, 0x1d, 0xc9, 0xab, 0x5d, 0x36, 0x64, 0x5b, 0xcb, 0x85, 0x6c, 0x72, 0x8c, 0x5c, 0x84, 0xb3,
	0x5a, 0x57, 0x63, 0xc4, 0x72, 0xdb, 0x92, 0x76, 0xc8, 0x0f, 0x3c, 0xc7, 0xeb, 0x8f, 0x58, 0xeb,
	0x46, 0xbc, 0xc4, 0x58, 0xd6, 0x2d, 0xa8, 0xc7, 0xda, 0xc0, 0x7d, 0x29, 0x74, 0x52, 0xfe, 0x51,
	0xd4, 0xeb, 0xd3, 0x84, 0x4e, 0xaa, 0x86, 0x81, 0xfc, 0xb9, 0xc1, 0x55, 0xf3, 0xf1, 0xe4, 0x73,
	0xb8, 0x19, 0xc5, 0xc3, 0x47, 0x0a, 0x20, 0xb2, 0xcb, 0x3f, 0x81, 0x7a, 0x08, 0x99, 0x32, 0xe6,
	0x10, 0x92, 0xda, 0x16, 0x8f, 0x14, 0xa3, 0x9a, 0x84, 0x9d, 0x0e, 0x7c, 0x74, 0x29, 0x18, 0x9a,
	0x8b, 0xd5, 0x18, 0xcd, 0x3d, 0xf1, 0xd2, 0xd9, 0xc3, 0x84, 0x1d, 0x49, 0xa3, 0xac, 0xf8, 0x49,
	0x01, 0xb3, 0x86, 0x6f, 0xa0, 0xbe, 0x3b, 0xe2, 0xc2, 0x1b, 0x62, 0x00, 0x3d, 0xe6, 0x23, 0x6e,
	0xc2, 0x42, 0x26, 0x40, 0x0c, 0x4b, 0xfa, 0x7f, 0xcc, 0xc1, 0x5a, 0x87, 0x89, 0x9d, 0x76, 0x6e,
	0xbe, 0xe3, 0x4e, 0x5a, 0x21, 0x56, 0xc7, 0x6c, 0xcb, 0x5c, 0xca, 0xb6, 0x7c, 0x02, 0x35, 0x93,
	0x3a, 0x4e, 0x0f, 0x2b, 0x94, 0x8b, 0x91, 0x21, 0x8c, 0x73, 0x28, 0x2b, 0x53, 0x26, 0x6c, 0x21,
	0x13, 0xc8, 0x4a, 0x59, 0x0f, 0x18, 0x15, 0xac, 0x87, 0x86, 0x44, 0xa9, 0x35, 0x28, 0x12, 0x06,
	0x39, 0x37, 0x61, 0x41, 0x2a, 0xfe, 0x88, 0xa3, 0x4e, 0x37, 0x8c, 0xb0, 0x44, 0x7e, 0x05, 0x35,
	0x44, 0xc4, 0x66, 0xb5, 0x99, 0xed, 0x0f, 0xfa, 0x72, 0x04, 0x8e, 0xfc, 0xba, 0xb4, 0x26, 0x80,
	0xd0, 0x38, 0xc4, 0x0e, 0x7b, 0x27, 0x25, 0x45, 0x55, 0x89, 0x0b, 0x87, 0x85, 0x1a, 0x8f, 0xbd,
	0x75, 0x24, 0x81, 0xfc, 0x2a, 0xb6, 0xa2, 0xcb, 0xd8, 0xef, 0xe7, 0x53, 0xb7, 0x2d, 0x67, 0x48,
	0x9f, 0x41, 0xc3, 0xc4, 0xfd, 0xea, 0xa1, 0xf8, 0xf1, 0x56, 0x63, 0xda, 0xf6, 0xa7, 0xb6, 0xd7,
	0x58, 0x36, 0x93, 0x02, 0x97, 0x3e, 0x76, 0x7c, 0x0f, 0xb9, 0xaf, 0x7f, 0xad, 0x32, 0x89, 0x50,
	0x8b, 0x95, 0xd9, 0x7c, 0x1b, 0xed, 0x29, 0x67, 0x6f, 0xd1, 0x88, 0x44, 0x29, 0xa1, 0x34, 0x22,
	0x32, 0x27, 0xfc, 0xcf, 0x9a, 0x72, 0x28, 0x12, 0x25, 0xde, 0xe4, 0xca, 0xa5, 0x0e, 0xa4, 0xfa,
	0xe1, 0x0e, 0x64, 0x6e, 0xdc, 0x81, 0xcc, 0x7f, 0xa8, 0x03, 0xb9, 0xfe, 0xa1, 0x0e, 0x64, 0x61,
	0xb2, 0x03, 0x59, 0x9c, 0xe6, 0x40, 0x96, 0xae, 0x76, 0x20, 0xb5, 0x89, 0x0e, 0x04, 0xa6, 0x3a,
	0x90, 0xfa, 0xcc, 0x0e, 0x64, 0xf9, 0x72, 0x07, 0x92, 0x36, 0xa6, 0x8d, 0xac, 0x31, 0x0d, 0xbb,
	0xc3, 0x6f, 0x25, 0x7d, 0xa1, 0x7f, 0x69, 0x44, 0x54, 0x65, 0x43, 0x72, 0x3e, 0x68, 0xe5, 0x4a,
	0x1f, 0xa4, 0xa5, 0x7c, 0xd0, 0x4d, 0x58, 0x10, 0xf6, 0xf0, 0x38, 0x60, 0xad, 0x55, 0xb5, 0x3a,
	0xaa, 0x34, 0xee, 0x80, 0xc8, 0x07, 0x3b, 0xa0, 0xa7, 0xb9, 0x40, 0x6d, 0x0d, 0xf7, 0x7f, 0x02,
	0xd2, 0x4e, 0x12, 0xc2, 0x65, 0xe2, 0xb9, 0x9c, 0x2b, 0x5b, 0x9f, 0x10, 0x41, 0xde, 0xc8, 0x46,
	0x90, 0x69, 0x2f, 0x76, 0x33, 0xeb, 0xc5, 0xfe, 0x1a, 0x56, 0xa5, 0xb4, 0xf8, 0x2a, 0xf2, 0x37,
	0x3d, 0xf7, 0xc4, 0xee, 0xb7, 0x3e, 0x42, 0x3f, 0xb5, 0x3d, 0xed, 0x64, 0xe8, 0xc4, 0xdb, 0xda,
	0x97, 0x6d, 0x65, 0x69, 0x17, 0x5b, 0x1a, 0x2b, 0xfd, 0x2c, 0x61, 0xe3, 0xbf, 0x64, 0x46, 0x90,
	0xa5, 0x91, 0x23, 0x14, 0x2e, 0xdc, 0xba, 0x28, 0xee, 0x9c, 0xa5, 0xaf, 0x9d, 0x36, 0x02, 0x1d,
	0x85, 0x4d, 0x8d, 0x04, 0x44, 0x8a, 0x08, 0xe6, 0xeb, 0x89, 0x44, 0x2b, 0x1b, 0xd1, 0x90, 0xd4,
	0xdd, 0x58, 0xaa, 0x1f, 0x00, 0x09, 0x98, 0x43, 0x31, 0x9b, 0x4a, 0x58, 0x95, 0x4b, 0x58, 0x8d,
	0x6a, 0x12, 0x76, 0x99, 0x20, 0xca, 0x2c, 0x21, 0xcc, 0x3e, 0xe4, 0xf7, 0xc6, 0x0f, 0xb0, 0x92,
	0x1b, 0x47, 0x6c, 0x96, 0x2a, 0x89, 0x59, 0x92, 0x31, 0xbb, 0xdc, 0x39, 0x2f, 0x1c, 0x87, 0x2a,
	0x28, 0x29, 0xb7, 0xbd, 0xc0, 0x16, 0x17, 0xa1, 0x49, 0x89, 0xcb, 0xfa, 0xef, 0xa1, 0xb1, 0x8b,
	0x3e, 0x64, 0x5a, 0x0c, 0x1d, 0x47, 0x13, 0xd5, 0x59, 0xa2, 0x09, 0xb4, 0xae, 0xc8, 0xaf, 0x6b,
	0xd0, 0x4c, 0xf7, 0xc0, 0x7d, 0xd9, 0xe7, 0x6b, 0xdf, 0xfa, 0x23, 0xf7, 0x99, 0xee, 0x81, 0xfb,
	0xfa, 0x37, 0x98, 0x61, 0xed, 0x88, 0xa1, 0xc7, 0xfd, 0x01, 0x0b, 0xf0, 0x10, 0x51, 0x76, 0x7d,
	0x1b, 0x20, 0x09, 0x59, 0xc2, 0xde, 0x6b, 0x71, 0xc4, 0xa2, 0xbf, 0xc2, 0x5c, 0x26, 0xdf, 0x8c,
	0xfb, 0xe4, 0x17, 0x30, 0x9f, 0x8a, 0x47, 0x26, 0x58, 0xf7, 0xa4, 0xad, 0x81, 0x2d, 0xf4, 0x0e,
	0x1e, 0xc7, 0xa5, 0xc8, 0xe3, 0x09, 0xed, 0x78, 0x9c, 0x94, 0x1b, 0xe7, 0x5c, 0x7e, 0x9c, 0xff,
	0x53, 0x01, 0x48, 0x20, 0xc7, 0xf0, 0x3e, 0x85, 0xe5, 0x80, 0x29, 0xdf, 0xd2, 0x1b, 0x05, 0x4e,
	0x28, 0x1e, 0xf5, 0x88, 0xf6, 0x3a, 0x70, 0xc8, 0x4f, 0xa1, 0x11, 0xb3, 0xa0, 0xa7, 0x51, 0xf2,
	0x19, 0xb7, 0xc3, 0x50, 0xe0, 0x0e, 0xd4, 0xa5, 0x38, 0x06, 0xb6, 0x8f, 0x87, 0x0b, 0x4a, 0x42,
	0xd3, 0x24, 0x3c, 0x37, 0x90, 0x42, 0x87, 0xdd, 0x84, 0x79, 0x32, 0x12, 0x64, 0x1f, 0x9f, 0x40,
	0x8d, 0x0f, 0x65, 0xe8, 0x23, 0x2b, 0x55, 0xb6, 0xbc, 0x84, 0x84, 0xb0, 0x12, 0x3d, 0x0a, 0x56,
	0x2a, 0xaf, 0xb2, 0x84, 0x84, 0xd7, 0x81, 0xa3, 0xff, 0x06, 0xcf, 0x07, 0xd3, 0x8b, 0x86, 0xe7,
	0xaf, 0x40, 0x63, 0x4a, 0x18, 0xe5, 0xce, 0xb6, 0x13, 0xa9, 0x76, 0xfa, 0x76, 0x7c, 0xf4, 0xd8,
	0xb5, 0xfd, 0x28, 0xb2, 0x9b, 0x22, 0x16, 0x67, 0xf1, 0xc1, 0x5a, 0xdc, 0x46, 0x9d, 0x96, 0x72,
	0x41, 0x03, 0xd1, 0x13, 0x1c, 0x9b, 0xcc, 0x49, 0x7b, 0x47, 0x03, 0xd1, 0xe5, 0xe4, 0x16, 0x80,
	0xeb, 0x9d, 0xf7, 0xf8, 0x59, 0xea, 0x40, 0x6b, 0xc9, 0xf5, 0xce, 0x3b, 0x67, 0x41, 0x97, 0x93,
	0xcf, 0x61, 0x25, 0x3a, 0xee, 0xe2, 0xcc, 0xf4, 0x5c, 0x2b, 0x4a, 0xd4, 0x9b, 0x21, 0xb9, 0xa3,
	0xa8, 0xfa, 0x1e, 0x8e, 0xf5, 0x68, 0xe0, 0x09, 0xaf, 0x1f, 0x50, 0x7f, 0xc0, 0x2f, 0x8f, 0x42,
	0xb3, 0xa3, 0xaf, 0xe6, 0x47, 0xff, 0x57, 0x38, 0xfa, 0x0c, 0x0a, 0xf7, 0xc9, 0x13, 0xa8, 0xfb,
	0x09, 0x69, 0xba, 0x60, 0x63, 0xfb, 0x7d, 0xc9, 0x6c, 0xa4, 0x1b, 0x46, 0x63, 0x9c, 0x16, 0x29,
	0x4f, 0x19, 0xe3, 0x7f, 0x57, 0xd4, 0x20, 0x73, 0x39, 0xc1, 0x73, 0x68, 0x9a, 0xa3, 0xa0, 0x97,
	0xf4, 0x37, 0x7d, 0xdb, 0x53, 0xe3, 0x6c, 0x98, 0xa3, 0x20, 0x99, 0xb6, 0x5c, 0xf6, 0xc8, 0xfa,
	0xf2, 0x81, 0xed, 0x27, 0xe3, 0x68, 0xa6, 0xc9, 0x6d, 0x8b, 0xdc, 0x87, 0xd5, 0x0c, 0x63, 0x2a,
	0xa4, 0xd7, 0xd2, 0x15, 0x87, 0x61, 0x78, 0x3f, 0x64, 0x4c, 0xf4, 0x2c, 0x7a, 0xc1, 0xc3, 0x23,
	0x80, 0x25, 0x49, 0xd8, 0xa3, 0x17, 0x5c, 0xff, 0x87, 0x79, 0xa8, 0xa7, 0xe6, 0x54, 0xee, 0x7c,
	0x0e, 0xa5, 0x96, 0x27, 0xa3, 0x09, 0xad, 0xe6, 0xe6, 0x04, 0xab, 0x99, 0x9e, 0xa6, 0x5c, 0xe2,
	0x4c, 0x7b, 0xf2, 0x15, 0xac, 0x67, 0xa6, 0x1d, 0x0a, 0x23, 0xce, 0x7c, 0xc9, 0x58, 0x4b, 0xd7,
	0x85, 0xe7, 0xa8, 0xd2, 0x8a, 0x50, 0xd7, 0x1e, 0xca, 0x34, 0x65, 0xc8, 0x86, 0x4c, 0x2e, 0x80,
	0x8c, 0x7a, 0x96, 0x43, 0xe2, 0x81, 0xa4, 0xa5, 0x53, 0x19, 0x7b, 0xa8, 0x22, 0xd3, 0xb9, 0x38,
	0x95, 0xb1, 0x87, 0x18, 0x7a, 0x25, 0x0a, 0x8a, 0xa9, 0xe4, 0x02, 0xc2, 0x34, 0x12, 0xaa, 0xcc,
	0x26, 0x95, 0x95, 0x5b, 0x4c, 0x27, 0x5b, 0x03, 0xdb, 0x15, 0x61, 0xc4, 0x89, 0xdf, 0x68, 0x72,
	0x06, 0xde, 0x79, 0x0f, 0x2b, 0x6a, 0x38, 0xf0, 0x25, 0x49, 0x78, 0x2a, 0x2b, 0x53, 0x7e, 0x07,
	0x32, 0x7e, 0x27, 0x4a, 0x79, 0xa4, 0x7f, 0xad, 0xe3, 0x4d, 0xd0, 0x62, 0x78, 0x63, 0x33, 0x9e,
	0x92, 0x2c, 0x7f, 0x78, 0x4a, 0xf2, 0xb7, 0x15, 0x80, 0x64, 0x3b, 0xc7, 0x0e, 0xa6, 0x92, 0x18,
	0xba, 0x9a, 0x89, 0xa1, 0xd7, 0xe1, 0xba, 0x8a, 0xbd, 0x95, 0x08, 0xaa, 0x02, 0xc6, 0x03, 0x51,
	0x86, 0xd0, 0x08, 0xa3, 0x7f, 0x1d, 0x1a, 0xca, 0xa6, 0x9e, 0x78, 0x41, 0x6f, 0xc8, 0xa3, 0x93,
	0xcb, 0x3a, 0x12, 0x9f, 0x78, 0xc1, 0x01, 0xef, 0xeb, 0x7f, 0x00, 0x2d, 0x2f, 0x07, 0x33, 0x1d,
	0x91, 0xdd, 0x06, 0x90, 0x7f, 0xc3, 0xdc, 0x4f, 0x0d, 0xa5, 0x26, 0x29, 0x2a, 0xf7, 0xbb, 0x0b,
	0xcd, 0x54, 0xae, 0x20, 0x6d, 0xba, 0xf2, 0x16, 0x8d, 0x84, 0x2a, 0x0d, 0xfb, 0x3f, 0x5e, 0x87,
	0x06, 0x66, 0x1e, 0xe7, 0xb6, 0x30, 0x07, 0x7f, 0xa4, 0xa4, 0xfa, 0xd7, 0x39, 0xc5, 0x50, 0xb9,
	0xd0, 0xcf, 0xa7, 0xe4, 0x42, 0xd1, 0x88, 0x32, 0x6a, 0x92, 0x53, 0x91, 0x47, 0x61, 0x80, 0xb2,
	0x80, 0xaa, 0xb6, 0x35, 0x2b, 0xe2, 0x4e, 0x1b, 0x03, 0x13, 0x75, 0xdc, 0x72, 0x18, 0x27, 0xee,
	0x8b, 0x38, 0xae, 0x6f, 0x67, 0x45, 0x89, 0xee, 0x7a, 0x3a, 0xd8, 0x3a, 0x4a, 0xf8, 0x37, 0xfe,
	0xad, 0x02, 0x0b, 0xaa, 0x83, 0xb1, 0x7d, 0xc4, 0x33, 0xdd, 0x0b, 0x27, 0xda, 0x48, 0x55, 0x88,
	0x12, 0x3d, 0xb9, 0x9c, 0xd7, 0x55, 0xa2, 0xb7, 0x9f, 0xc9, 0x36, 0xbf, 0x7e, 0xbf, 0x69, 0xa5,
	0xee, 0x75, 0xf4, 0xfb, 0xb9, 0x1b, 0x98, 0x3d, 0x76, 0x42, 0x47, 0x8e, 0xd0, 0xae, 0x91, 0x55,
	0x68, 0x20, 0xe5, 0xd0, 0xdb, 0x1d, 0x78, 0x1e, 0x67, 0x5a, 0x45, 0x7f, 0x03, 0xcb, 0xe9, 0xa5,
	0x26, 0x1f, 0xc1, 0x5a, 0xba, 0xfc, 0xda, 0x3d, 0x75, 0xbd, 0x73, 0x57, 0xbb, 0x46, 0x6e, 0x02,
	0x49, 0x57, 0x3c, 0x09, 0x6c, 0xe6, 0x5a, 0x5a, 0x85, 0xdc, 0x80, 0xd5, 0x34, 0xfd, 0x85, 0x77,
	0xc6, 0x02, 0xad, 0xaa, 0xdf, 0x87, 0x66, 0x76, 0xa9, 0xc8, 0x32, 0x2c, 0x45, 0x14, 0xed, 0x1a,
	0xa9, 0xc3, 0x62, 0xc7, 0x76, 0x98, 0x6b, 0xca, 0x41, 0x68, 0xd0, 0x4c, 0x4f, 0x8a, 0xfb, 0xfa,
	0x03, 0xd0, 0xd2, 0xf7, 0xbf, 0x7c, 0xca, 0x5d, 0x47, 0x17, 0x56, 0x73, 0xec, 0xdc, 0x8f, 0x0f,
	0x61, 0x52, 0x31, 0xe3, 0x2c, 0xd1, 0xec, 0x92, 0xba, 0xf3, 0xe2, 0x42, 0xff, 0xa7, 0x8a, 0x5c,
	0x0c, 0x6a, 0x85, 0xce, 0xe3, 0x29, 0xa3, 0x81, 0xf8, 0x10, 0xc7, 0x9a, 0xb6, 0x79, 0x73, 0x19,
	0x9b, 0xd7, 0x82, 0x45, 0xd3, 0x73, 0x05, 0x73, 0x45, 0xa8, 0x3f, 0x51, 0x91, 0xdc, 0x80, 0x85,
	0x21, 0xef, 0xcb, 0x16, 0xca, 0x7c, 0x5c, 0x1f, 0xf2, 0x7e, 0xdb, 0xd2, 0x77, 0x60, 0x7d, 0x7c,
	0x44, 0xdc, 0x4f, 0xb1, 0x57, 0x52, 0xec, 0x97, 0x1d, 0xa3, 0x6c, 0x6e, 0xab, 0xc5, 0x4e, 0xee,
	0x9f, 0x08, 0xc0, 0xc2, 0xa1, 0xf7, 0xa2, 0xfd, 0xfc, 0xb1, 0x76, 0x8d, 0x2c, 0xc1, 0x3c, 0x7e,
	0x55, 0x24, 0xf5, 0xf5, 0x21, 0x7e, 0x57, 0x37, 0x87, 0xd0, 0xc8, 0x1c, 0x16, 0x91, 0x26, 0x80,
	0xfa, 0x7a, 0xcd, 0x59, 0xa0, 0x5d, 0x93, 0xb2, 0xa6, 0xca, 0x7b, 0x18, 0xfe, 0x6b, 0x95, 0x84,
	0x63, 0x9f, 0x0e, 0x99, 0x56, 0x25, 0x6b, 0xb0, 0xa2, 0xca, 0x07, 0x23, 0x47, 0xd8, 0xc8, 0x34,
	0x27, 0x85, 0x47, 0x11, 0x77, 0xda, 0xfb, 0xbb, 0xa1, 0x3f, 0xd3, 0xe6, 0x37, 0x5f, 0x00, 0x24,
	0x07, 0x34, 0x28, 0x79, 0x71, 0x29, 0x75, 0x9f, 0xb8, 0x1a, 0x5a, 0x31, 0x45, 0x7f, 0xe9, 0x6b,
	0x15, 0xd9, 0x49, 0x8a, 0x84, 0x63, 0xab, 0x6e, 0xfe, 0x46, 0x1d, 0x1b, 0xa1, 0x4e, 0xac, 0x83,
	0x16, 0x7d, 0x67, 0xf5, 0x22, 0xa6, 0xe2, 0x70, 0x2b, 0x72, 0x42, 0x31, 0xe9, 0x88, 0x85, 0xb7,
	0x8f, 0x09, 0x93, 0xcc, 0x0a, 0xb5, 0xb9, 0xcd, 0x97, 0xea, 0x79, 0x03, 0x1e, 0xe1, 0xa0, 0x26,
	0x44, 0x85, 0xd4, 0x30, 0xc3, 0x2e, 0x15, 0xf9, 0x68, 0x74, 0xec, 0xd8, 0x66, 0xa8, 0x36, 0x09,
	0x35, 0xb0, 0xcf, 0xa8, 0x60, 0x5a, 0x75, 0xb3, 0x03, 0xf5, 0xd4, 0x99, 0x80, 0x9c, 0x7a, 0xaa,
	0xd8, 0x33, 0xd8, 0x99, 0xcd, 0xce, 0x15, 0x66, 0x9a, 0x7e, 0x44, 0x39, 0xd7, 0x2a, 0xe3, 0xdc,
	0x3f, 0x32, 0x53, 0x68, 0xd5, 0xed, 0xff, 0x7d, 0x02, 0xab, 0x8f, 0x42, 0x91, 0x8f, 0xcf, 0xe2,
	0xc8, 0x29, 0x5e, 0xf1, 0xa5, 0x03, 0xa3, 0xfb, 0x57, 0x6b, 0xc7, 0x58, 0x74, 0xb9, 0xf1, 0xe5,
	0xec, 0xcc, 0xdc, 0x27, 0xbd, 0x70, 0x47, 0x51, 0xc3, 0xc9, 0xe7, 0x33, 0x1a, 0xb7, 0x8d, 0x7b,
	0xb3, 0x31, 0x72, 0x3f, 0x9a, 0x4d, 0x12, 0x13, 0x4f, 0x9b, 0x4d, 0x26, 0x9e, 0x9f, 0x36, 0x9b,
	0x5c, 0xd8, 0x3e, 0x80, 0x46, 0x26, 0x33, 0x22, 0x9b, 0x13, 0x9b, 0x67, 0xf2, 0xce, 0x8d, 0xfb,
	0x33, 0xf3, 0xaa, 0x75, 0x4b, 0x12, 0xf9, 0x49, 0xeb, 0x96, 0x39, 0x50, 0x98, 0xb4, 0x6e, 0xd9,
	0x73, 0x01, 0xd9, 0x41, 0x92, 0xb5, 0x4f, 0xea, 0x20, 0x73, 0x7a, 0x30, 0xa9, 0x83, 0xec, 0x21,
	0x00, 0xf9, 0x1b, 0x58, 0xbb, 0xe4, 0xb2, 0x81, 0xfc, 0xe9, 0xd5, 0x00, 0x97, 0x5f, 0x74, 0x6c,
	0x7c, 0xf5, 0x9e, 0x2d, 0xb8, 0x4f, 0xde, 0xc0, 0x62, 0xe8, 0x11, 0xc8, 0x67, 0x13, 0x57, 0x3d,
	0x9a, 0xd6, 0xdd, 0x19, 0xb8, 0xb8, 0x4f, 0x04, 0xa6, 0x5b, 0xd9, 0xdb, 0x56, 0xb2, 0x35, 0xb1,
	0xed, 0xd8, 0x3d, 0xf3, 0xc6, 0xc3, 0xf7, 0xe2, 0xe7, 0x3e, 0x79, 0x0b, 0x5a, 0xfe, 0x30, 0x9d,
	0x3c, 0xb8, 0x1a, 0xe4, 0x92, 0xcb, 0x93, 0x8d, 0xad, 0xf7, 0x61, 0xe7, 0x3e, 0x71, 0x61, 0x25,
	0xf7, 0x86, 0x80, 0x4c, 0xd0, 0x94, 0xf1, 0xf7, 0x09, 0x1b, 0x0f, 0xde, 0x83, 0x5b, 0x29, 0x56,
	0xc6, 0x8f, 0x4f, 0x52, 0xac, 0x7c, 0x7c, 0x30, 0x49, 0xb1, 0xc6, 0x83, 0x83, 0x63, 0xbc, 0xb6,
	0x8b, 0x5e, 0x2f, 0x90, 0x7b, 0x13, 0x37, 0x23, 0xf5, 0x0e, 0x62, 0xe3, 0x8b, 0x19, 0x39, 0xe3,
	0x3e, 0x22, 0x67, 0x3b, 0xa5, 0x8f, 0xd4, 0x43, 0x94, 0x29, 0x7d, 0x64, 0x5e, 0x9d, 0x9c, 0xc6,
	0xaf, 0x58, 0xa2, 0x67, 0x59, 0xf7, 0xa7, 0x36, 0x4e, 0x9e, 0x7b, 0x4d, 0xb1, 0x7b, 0xf9, 0x57,
	0x4c, 0x07, 0x30, 0xdf, 0x65, 0x5c, 0x90, 0x4f, 0xa7, 0xbe, 0x33, 0xda, 0xd0, 0xa7, 0xb1, 0x70,
	0x9f, 0x30, 0x58, 0x4e, 0xbf, 0xab, 0x23, 0x93, 0xa7, 0x9d, 0x7e, 0xad, 0xb7, 0xb1, 0x39, 0x2b,
	0x6b, 0x6c, 0xad, 0x93, 0xc7, 0x6f, 0x53, 0xac, 0x75, 0xe6, 0x85, 0xdd, 0x14, 0x6b, 0x9d, 0x7b,
	0xba, 0x27, 0xbd, 0x5c, 0xd2, 0xcd, 0x24, 0x2f, 0x97, 0xe9, 0xe3, 0xde, 0x6c, 0x8c, 0xca, 0x04,
	0xe4, 0xc3, 0xbf, 0x49, 0x26, 0xe0, 0x92, 0xe0, 0x75, 0x92, 0x09, 0xb8, 0x34, 0xb2, 0x14, 0xd9,
	0xd0, 0x5a, 0xbd, 0xe4, 0xdc, 0x9a, 0x4d, 0xd5, 0xa2, 0x17, 0xa2, 0x93, 0x6c, 0xdd, 0xe5, 0xcf,
	0x3c, 0xff, 0xae, 0x92, 0xdc, 0x51, 0xe7, 0xdf, 0xcb, 0xfe, 0x7c, 0x36, 0xb0, 0xec, 0x9b, 0xdc,
	0x8d, 0x6f, 0x3e, 0xa0, 0x15, 0x5e, 0x88, 0x93, 0x4b, 0x86, 0x70, 0x4b, 0x81, 0xa5, 0xdf, 0x05,
	0xef, 0x7a, 0xc3, 0x21, 0x75, 0x2d, 0xd9, 0xd5, 0xed, 0x09, 0xb5, 0xdc, 0x27, 0xbf, 0x86, 0x8f,
	0xd1, 0xf6, 0xc9, 0xcc, 0xcd, 0x1b, 0xbe, 0xb1, 0xb9, 0x7d, 0x6c, 0x3b, 0xb6, 0xb8, 0x78, 0x2a,
	0x84, 0x5f, 0x0c, 0xf9, 0x55, 0xfe, 0xfc, 0xae, 0x38, 0xe4, 0x81, 0xb2, 0x2f, 0xcc, 0xf4, 0x02,
	0x8b, 0x17, 0x87, 0x7b, 0x03, 0x1f, 0x21, 0x5c, 0x2a, 0x61, 0xb4, 0xb9, 0x28, 0x8e, 0xfb, 0x1a,
	0x6e, 0x1e, 0x78, 0x96, 0x7d, 0x72, 0x91, 0x86, 0x2e, 0x0e, 0xdb, 0xc1, 0xfb, 0x6b, 0x99, 0x47,
	0xc8, 0xd4, 0xb5, 0x63, 0x32, 0x97, 0x15, 0x07, 0xfd, 0x2d, 0xdc, 0xc2, 0xfd, 0x8f, 0x21, 0xcb,
	0x14, 0x81, 0x68, 0xbf, 0xa8, 0xd5, 0x61, 0x6f, 0x8b, 0xc3, 0x1d, 0xc2, 0xca, 0x01, 0x0d, 0x4e,
	0x4b, 0xc3, 0x7b, 0x95, 0x3f, 0x06, 0x2f, 0x0e, 0x79, 0x04, 0xab, 0x9d, 0x34, 0x64, 0x71, 0xc4,
	0x17, 0xe8, 0x30, 0xd4, 0x0d, 0x26, 0xde, 0xeb, 0x16, 0x1d, 0xdf, 0x8e, 0x65, 0x49, 0x1c, 0xe6,
	0xee, 0x8d, 0x02, 0x94, 0xce, 0xc2, 0x88, 0xfb, 0x4c, 0x94, 0x89, 0x78, 0x10, 0x47, 0x11, 0xe1,
	0xdd, 0x4a, 0x31, 0xb8, 0xe7, 0xe8, 0xd8, 0x3b, 0x78, 0xef, 0x5c, 0x18, 0xac, 0x0d, 0xd0, 0x11,
	0x34, 0x50, 0x70, 0xc5, 0xa0, 0x5e, 0x62, 0x04, 0x8d, 0x40, 0x47, 0x81, 0xd7, 0x0f, 0x18, 0xe7,
	0x65, 0x48, 0x0a, 0x02, 0xa2, 0x26, 0x97, 0x86, 0xa6, 0x9e, 0x4a, 0x14, 0x55, 0xdd, 0x08, 0xed,
	0xa9, 0xcd, 0x8b, 0x2f, 0x9e, 0xc2, 0x93, 0xb6, 0xb0, 0xc3, 0xf0, 0x14, 0x8f, 0x97, 0xa1, 0xb7,
	0x3b, 0x23, 0xe1, 0x1d, 0x39, 0xf4, 0x22, 0x3c, 0x24, 0x2c, 0x41, 0xec, 0x30, 0xf8, 0x2b, 0xae,
	0xb6, 0x4a, 0x25, 0x62, 0xb0, 0x37, 0xdb, 0x65, 0x18, 0xbe, 0xd4, 0xb5, 0x44, 0x49, 0x23, 0x74,
	0xd3, 0x6f, 0xeb, 0x4a, 0x70, 0xcd, 0x59, 0xc4, 0x32, 0x76, 0xe5, 0x99, 0xcc, 0x82, 0x5c, 0x7c,
	0xee, 0xc8, 0xb8, 0x59, 0x82, 0xe5, 0x73, 0x65, 0x34, 0x32, 0x1c, 0x32, 0xd7, 0x2a, 0x41, 0x4b,
	0x94, 0xcc, 0xec, 0x33, 0xb7, 0x8c, 0xa9, 0x1e, 0x40, 0xf3, 0x90, 0x9d, 0x87, 0xe7, 0x98, 0x65,
	0xf9, 0x72, 0xa9, 0x71, 0x07, 0x1c, 0x4f, 0xd1, 0x0b, 0x2f, 0x9e, 0xc4, 0x92, 0xbe, 0xfc, 0x80,
	0xf7, 0x0f, 0x3d, 0x61, 0x9f, 0x14, 0x5c, 0xbc, 0x7d, 0x58, 0x7a, 0xc2, 0x98, 0x75, 0x4c, 0xcd,
	0xd3, 0x32, 0x3c, 0xda, 0xab, 0x91, 0x6d, 0x9e, 0xe2, 0x9e, 0x16, 0x57, 0x8e, 0x67, 0x71, 0x7e,
	0x5f, 0x1c, 0xeb, 0x29, 0xd4, 0xd0, 0x03, 0x49, 0xb4, 0xc2, 0x48, 0x6d, 0xd7, 0x1f, 0x09, 0xbc,
	0x09, 0x2d, 0x2f, 0xd4, 0x2f, 0xc7, 0x20, 0x2b, 0xef, 0x18, 0x41, 0xda, 0x0e, 0x2b, 0x3a, 0xc6,
	0xdf, 0xc2, 0xad, 0x04, 0x30, 0x6b, 0xe8, 0x4b, 0x89, 0xf8, 0x31, 0x8a, 0x6e, 0x9f, 0x84, 0xfa,
	0x26, 0xc3, 0xe9, 0x52, 0x60, 0xf7, 0x99, 0x48, 0x41, 0x4a, 0xc3, 0x55, 0x1c, 0xf6, 0x25, 0x3e,
	0x40, 0x42, 0x67, 0xfc, 0xc8, 0xf3, 0x4e, 0x8b, 0xcb, 0x64, 0x2a, 0xc4, 0x92, 0x80, 0xc5, 0xc0,
	0xba, 0xf8, 0xe2, 0x2a, 0x06, 0x53, 0xa6, 0xa1, 0xc3, 0xde, 0x16, 0xcf, 0xc9, 0x68, 0x70, 0x5a,
	0x36, 0xec, 0x01, 0x34, 0x93, 0x78, 0xb0, 0xf8, 0xdc, 0x3b, 0xf8, 0x48, 0x2d, 0x06, 0x2b, 0x27,
	0x2e, 0x54, 0xf6, 0x2c, 0x06, 0x2d, 0xc1, 0x4f, 0x19, 0xb0, 0x96, 0x46, 0x2c, 0x25, 0xa2, 0x7b,
	0x0e, 0xcb, 0x3b, 0x96, 0xf5, 0xc3, 0xc0, 0x16, 0xe5, 0x44, 0x23, 0xb8, 0x2d, 0x18, 0x37, 0x48,
	0xc5, 0x29, 0xec, 0x5a, 0x3a, 0x61, 0xf8, 0x55, 0x78, 0x2b, 0xe2, 0x21, 0x3d, 0x76, 0x45, 0x40,
	0xdd, 0xa2, 0x88, 0xcf, 0x61, 0xf9, 0xb1, 0x2b, 0x58, 0x70, 0xc4, 0xc4, 0x53, 0x6f, 0x58, 0x7c,
	0x78, 0x9d, 0x81, 0x77, 0x5e, 0xa2, 0xd5, 0x95, 0x66, 0x7c, 0xe0, 0x9d, 0xef, 0x7a, 0xae, 0xb0,
	0xdd, 0x51, 0x09, 0x5b, 0xf1, 0x1c, 0x96, 0xcb, 0x03, 0x53, 0x31, 0x0d, 0xfe, 0x9b, 0x66, 0xf8,
	0x44, 0xb8, 0x68, 0xca, 0xf4, 0xd2, 0x67, 0xae, 0xc2, 0xa3, 0x81, 0x55, 0xda, 0xe0, 0xc2, 0x0b,
	0xd8, 0x12, 0x6c, 0xf4, 0x11, 0x13, 0x2f, 0xcf, 0xdd, 0x3d, 0x7a, 0x51, 0xc6, 0xd8, 0x8e, 0x42,
	0x1b, 0x70, 0x38, 0x1a, 0x16, 0x5e, 0x38, 0x54, 0xd7, 0x08, 0xb0, 0xf0, 0x4c, 0x3b, 0xc2, 0xf3,
	0xcb, 0x01, 0x3b, 0x82, 0xd5, 0x48, 0xbf, 0x3a, 0x0e, 0x63, 0x7e, 0x97, 0x3a, 0xa7, 0xa5, 0xe4,
	0x72, 0xf6, 0x31, 0xe3, 0x65, 0x08, 0xdd, 0x4b, 0xd0, 0x76, 0x2c, 0x6b, 0xc7, 0x34, 0xbd, 0xa1,
	0x4f, 0xdd, 0x0b, 0x7c, 0x3e, 0x57, 0x42, 0x74, 0x50, 0x22, 0xa0, 0x3a, 0x49, 0xe8, 0xd2, 0xa0,
	0xaf, 0xce, 0x89, 0xca, 0xc8, 0xfc, 0x15, 0x9a, 0xc1, 0xce, 0x0b, 0xab, 0x59, 0x1b, 0xff, 0x81,
	0x54, 0xda, 0x92, 0xc2, 0x62, 0xac, 0x72, 0xe0, 0x6e, 0xb7, 0xb3, 0x47, 0x05, 0x2d, 0xe5, 0xbc,
	0x57, 0x86, 0x2c, 0xe8, 0x30, 0x0e, 0x78, 0x7f, 0x87, 0xcb, 0x88, 0x95, 0x59, 0x65, 0xa4, 0x22,
	0x2f, 0x47, 0x98, 0x3f, 0x94, 0x72, 0xe0, 0x24, 0x81, 0xba, 0x9e, 0x6f, 0x9b, 0xa5, 0xa0, 0x21,
	0x52, 0x09, 0x63, 0x3b, 0x8c, 0xff, 0x27, 0x3e, 0xbe, 0x56, 0x2d, 0x84, 0xf7, 0x03, 0xb4, 0x8e,
	0x3c, 0x2e, 0xf8, 0xe3, 0x77, 0xbe, 0x83, 0x3f, 0xdd, 0xa0, 0x02, 0xc8, 0xe2, 0xbb, 0xdc, 0x85,
	0x1b, 0x98, 0x2c, 0xa8, 0x18, 0x6a, 0xc7, 0xf7, 0x99, 0x6b, 0x15, 0x46, 0x7d, 0xf4, 0xfd, 0x5f,
	0x7e, 0xd7, 0xf7, 0x1c, 0xea, 0xf6, 0xb7, 0xbe, 0xd9, 0x16, 0x62, 0xcb, 0xf4, 0x86, 0x0f, 0xf1,
	0xb7, 0x62, 0x4c, 0xcf, 0x79, 0xc8, 0x59, 0x70, 0x66, 0x9b, 0x8c, 0x3f, 0x94, 0x08, 0x97, 0xfd,
	0x1e, 0xcd, 0xf1, 0x02, 0xf2, 0x7e, 0xfd, 0xff, 0x01, 0x00, 0x00, 0xff, 0xff, 0xd9, 0x8f, 0x20,
	0xf7, 0xc1, 0x46, 0x00, 0x00,
}
