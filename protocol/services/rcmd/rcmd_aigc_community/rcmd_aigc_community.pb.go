// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd_aigc_community/rcmd_aigc_community.proto

package rcmd_aigc_community // import "golang.52tt.com/protocol/services/rcmd/rcmd_aigc_community"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetAigcPostsReq_FetchMode int32

const (
	GetAigcPostsReq_Invalid  GetAigcPostsReq_FetchMode = 0
	GetAigcPostsReq_NextPage GetAigcPostsReq_FetchMode = 1
	GetAigcPostsReq_Refresh  GetAigcPostsReq_FetchMode = 2
)

var GetAigcPostsReq_FetchMode_name = map[int32]string{
	0: "Invalid",
	1: "NextPage",
	2: "Refresh",
}
var GetAigcPostsReq_FetchMode_value = map[string]int32{
	"Invalid":  0,
	"NextPage": 1,
	"Refresh":  2,
}

func (x GetAigcPostsReq_FetchMode) String() string {
	return proto.EnumName(GetAigcPostsReq_FetchMode_name, int32(x))
}
func (GetAigcPostsReq_FetchMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_aigc_community_0e2e1a19d255a242, []int{0, 0}
}

type GetAigcPostsReq struct {
	Uid                  uint32                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FetchMode            GetAigcPostsReq_FetchMode `protobuf:"varint,3,opt,name=fetch_mode,json=fetchMode,proto3,enum=rcmd.rcmd_aigc_community.GetAigcPostsReq_FetchMode" json:"fetch_mode,omitempty"`
	BrowsePostIds        []string                  `protobuf:"bytes,4,rep,name=browse_post_ids,json=browsePostIds,proto3" json:"browse_post_ids,omitempty"`
	PostSubjectId        string                    `protobuf:"bytes,5,opt,name=post_subject_id,json=postSubjectId,proto3" json:"post_subject_id,omitempty"`
	RoleId               uint32                    `protobuf:"varint,6,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetAigcPostsReq) Reset()         { *m = GetAigcPostsReq{} }
func (m *GetAigcPostsReq) String() string { return proto.CompactTextString(m) }
func (*GetAigcPostsReq) ProtoMessage()    {}
func (*GetAigcPostsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_aigc_community_0e2e1a19d255a242, []int{0}
}
func (m *GetAigcPostsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAigcPostsReq.Unmarshal(m, b)
}
func (m *GetAigcPostsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAigcPostsReq.Marshal(b, m, deterministic)
}
func (dst *GetAigcPostsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAigcPostsReq.Merge(dst, src)
}
func (m *GetAigcPostsReq) XXX_Size() int {
	return xxx_messageInfo_GetAigcPostsReq.Size(m)
}
func (m *GetAigcPostsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAigcPostsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAigcPostsReq proto.InternalMessageInfo

func (m *GetAigcPostsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAigcPostsReq) GetFetchMode() GetAigcPostsReq_FetchMode {
	if m != nil {
		return m.FetchMode
	}
	return GetAigcPostsReq_Invalid
}

func (m *GetAigcPostsReq) GetBrowsePostIds() []string {
	if m != nil {
		return m.BrowsePostIds
	}
	return nil
}

func (m *GetAigcPostsReq) GetPostSubjectId() string {
	if m != nil {
		return m.PostSubjectId
	}
	return ""
}

func (m *GetAigcPostsReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetAigcPostsResp struct {
	PostIds              []string `protobuf:"bytes,1,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	BottomReached        bool     `protobuf:"varint,2,opt,name=bottom_reached,json=bottomReached,proto3" json:"bottom_reached,omitempty"`
	Footprint            string   `protobuf:"bytes,3,opt,name=footprint,proto3" json:"footprint,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAigcPostsResp) Reset()         { *m = GetAigcPostsResp{} }
func (m *GetAigcPostsResp) String() string { return proto.CompactTextString(m) }
func (*GetAigcPostsResp) ProtoMessage()    {}
func (*GetAigcPostsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_aigc_community_0e2e1a19d255a242, []int{1}
}
func (m *GetAigcPostsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAigcPostsResp.Unmarshal(m, b)
}
func (m *GetAigcPostsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAigcPostsResp.Marshal(b, m, deterministic)
}
func (dst *GetAigcPostsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAigcPostsResp.Merge(dst, src)
}
func (m *GetAigcPostsResp) XXX_Size() int {
	return xxx_messageInfo_GetAigcPostsResp.Size(m)
}
func (m *GetAigcPostsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAigcPostsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAigcPostsResp proto.InternalMessageInfo

func (m *GetAigcPostsResp) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

func (m *GetAigcPostsResp) GetBottomReached() bool {
	if m != nil {
		return m.BottomReached
	}
	return false
}

func (m *GetAigcPostsResp) GetFootprint() string {
	if m != nil {
		return m.Footprint
	}
	return ""
}

type TransPostIdReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostIds              []string `protobuf:"bytes,2,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	ActionType           string   `protobuf:"bytes,3,opt,name=action_type,json=actionType,proto3" json:"action_type,omitempty"`
	SubjectIds           []string `protobuf:"bytes,4,rep,name=subject_ids,json=subjectIds,proto3" json:"subject_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TransPostIdReq) Reset()         { *m = TransPostIdReq{} }
func (m *TransPostIdReq) String() string { return proto.CompactTextString(m) }
func (*TransPostIdReq) ProtoMessage()    {}
func (*TransPostIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_aigc_community_0e2e1a19d255a242, []int{2}
}
func (m *TransPostIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TransPostIdReq.Unmarshal(m, b)
}
func (m *TransPostIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TransPostIdReq.Marshal(b, m, deterministic)
}
func (dst *TransPostIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransPostIdReq.Merge(dst, src)
}
func (m *TransPostIdReq) XXX_Size() int {
	return xxx_messageInfo_TransPostIdReq.Size(m)
}
func (m *TransPostIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TransPostIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_TransPostIdReq proto.InternalMessageInfo

func (m *TransPostIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TransPostIdReq) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

func (m *TransPostIdReq) GetActionType() string {
	if m != nil {
		return m.ActionType
	}
	return ""
}

func (m *TransPostIdReq) GetSubjectIds() []string {
	if m != nil {
		return m.SubjectIds
	}
	return nil
}

// 定义返回 UID 的消息
type TransPostIdResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TransPostIdResp) Reset()         { *m = TransPostIdResp{} }
func (m *TransPostIdResp) String() string { return proto.CompactTextString(m) }
func (*TransPostIdResp) ProtoMessage()    {}
func (*TransPostIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_aigc_community_0e2e1a19d255a242, []int{3}
}
func (m *TransPostIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TransPostIdResp.Unmarshal(m, b)
}
func (m *TransPostIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TransPostIdResp.Marshal(b, m, deterministic)
}
func (dst *TransPostIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransPostIdResp.Merge(dst, src)
}
func (m *TransPostIdResp) XXX_Size() int {
	return xxx_messageInfo_TransPostIdResp.Size(m)
}
func (m *TransPostIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TransPostIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_TransPostIdResp proto.InternalMessageInfo

func (m *TransPostIdResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func init() {
	proto.RegisterType((*GetAigcPostsReq)(nil), "rcmd.rcmd_aigc_community.GetAigcPostsReq")
	proto.RegisterType((*GetAigcPostsResp)(nil), "rcmd.rcmd_aigc_community.GetAigcPostsResp")
	proto.RegisterType((*TransPostIdReq)(nil), "rcmd.rcmd_aigc_community.TransPostIdReq")
	proto.RegisterType((*TransPostIdResp)(nil), "rcmd.rcmd_aigc_community.TransPostIdResp")
	proto.RegisterEnum("rcmd.rcmd_aigc_community.GetAigcPostsReq_FetchMode", GetAigcPostsReq_FetchMode_name, GetAigcPostsReq_FetchMode_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RCMDAigcCommunityClient is the client API for RCMDAigcCommunity service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RCMDAigcCommunityClient interface {
	GetAigcPosts(ctx context.Context, in *GetAigcPostsReq, opts ...grpc.CallOption) (*GetAigcPostsResp, error)
	TransPostIdEvent(ctx context.Context, in *TransPostIdReq, opts ...grpc.CallOption) (*TransPostIdResp, error)
}

type rCMDAigcCommunityClient struct {
	cc *grpc.ClientConn
}

func NewRCMDAigcCommunityClient(cc *grpc.ClientConn) RCMDAigcCommunityClient {
	return &rCMDAigcCommunityClient{cc}
}

func (c *rCMDAigcCommunityClient) GetAigcPosts(ctx context.Context, in *GetAigcPostsReq, opts ...grpc.CallOption) (*GetAigcPostsResp, error) {
	out := new(GetAigcPostsResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_aigc_community.RCMDAigcCommunity/GetAigcPosts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDAigcCommunityClient) TransPostIdEvent(ctx context.Context, in *TransPostIdReq, opts ...grpc.CallOption) (*TransPostIdResp, error) {
	out := new(TransPostIdResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_aigc_community.RCMDAigcCommunity/TransPostIdEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RCMDAigcCommunityServer is the server API for RCMDAigcCommunity service.
type RCMDAigcCommunityServer interface {
	GetAigcPosts(context.Context, *GetAigcPostsReq) (*GetAigcPostsResp, error)
	TransPostIdEvent(context.Context, *TransPostIdReq) (*TransPostIdResp, error)
}

func RegisterRCMDAigcCommunityServer(s *grpc.Server, srv RCMDAigcCommunityServer) {
	s.RegisterService(&_RCMDAigcCommunity_serviceDesc, srv)
}

func _RCMDAigcCommunity_GetAigcPosts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAigcPostsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAigcCommunityServer).GetAigcPosts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_aigc_community.RCMDAigcCommunity/GetAigcPosts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAigcCommunityServer).GetAigcPosts(ctx, req.(*GetAigcPostsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDAigcCommunity_TransPostIdEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransPostIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDAigcCommunityServer).TransPostIdEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_aigc_community.RCMDAigcCommunity/TransPostIdEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDAigcCommunityServer).TransPostIdEvent(ctx, req.(*TransPostIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RCMDAigcCommunity_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.rcmd_aigc_community.RCMDAigcCommunity",
	HandlerType: (*RCMDAigcCommunityServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAigcPosts",
			Handler:    _RCMDAigcCommunity_GetAigcPosts_Handler,
		},
		{
			MethodName: "TransPostIdEvent",
			Handler:    _RCMDAigcCommunity_TransPostIdEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rcmd_aigc_community/rcmd_aigc_community.proto",
}

func init() {
	proto.RegisterFile("rcmd_aigc_community/rcmd_aigc_community.proto", fileDescriptor_rcmd_aigc_community_0e2e1a19d255a242)
}

var fileDescriptor_rcmd_aigc_community_0e2e1a19d255a242 = []byte{
	// 462 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x93, 0xcf, 0x8e, 0xd3, 0x30,
	0x10, 0xc6, 0x49, 0x0b, 0xfd, 0x33, 0xbb, 0x6d, 0x83, 0x2f, 0x84, 0x15, 0x12, 0x51, 0x10, 0x28,
	0x8b, 0x44, 0x2a, 0xb5, 0xe2, 0x82, 0xb8, 0xc0, 0xf2, 0x47, 0x3d, 0x2c, 0x5a, 0x99, 0x3d, 0x71,
	0x89, 0x52, 0x7b, 0x9a, 0x1a, 0x35, 0xb1, 0xb1, 0xdd, 0x42, 0x4f, 0x3c, 0x05, 0x4f, 0xc8, 0x8b,
	0x20, 0x27, 0xdb, 0xdd, 0x96, 0xed, 0x4a, 0xbd, 0x39, 0xdf, 0x7c, 0x33, 0xfa, 0xf9, 0x73, 0x06,
	0x5e, 0x69, 0x56, 0xf0, 0x34, 0x13, 0x39, 0x4b, 0x99, 0x2c, 0x8a, 0x65, 0x29, 0xec, 0x7a, 0xb8,
	0x47, 0x4b, 0x94, 0x96, 0x56, 0x92, 0xc0, 0x95, 0x92, 0x3d, 0xf5, 0xe8, 0x4f, 0x03, 0x06, 0x9f,
	0xd1, 0xbe, 0x13, 0x39, 0xbb, 0x90, 0xc6, 0x1a, 0x8a, 0x3f, 0x88, 0x0f, 0xcd, 0xa5, 0xe0, 0x81,
	0x17, 0x7a, 0x71, 0x8f, 0xba, 0x23, 0xa1, 0x00, 0x33, 0xb4, 0x6c, 0x9e, 0x16, 0x92, 0x63, 0xd0,
	0x0c, 0xbd, 0xb8, 0x3f, 0x1a, 0x27, 0x77, 0x0d, 0x4d, 0xfe, 0x1b, 0x98, 0x7c, 0x72, 0xbd, 0xe7,
	0x92, 0x23, 0xed, 0xce, 0x36, 0x47, 0xf2, 0x02, 0x06, 0x53, 0x2d, 0x7f, 0x1a, 0x4c, 0x95, 0x34,
	0x36, 0x15, 0xdc, 0x04, 0xf7, 0xc3, 0x66, 0xdc, 0xa5, 0xbd, 0x5a, 0x76, 0xdd, 0x13, 0x6e, 0x9c,
	0xaf, 0x32, 0x98, 0xe5, 0xf4, 0x3b, 0x32, 0x67, 0x0c, 0x1e, 0x84, 0x9e, 0xf3, 0x39, 0xf9, 0x6b,
	0xad, 0x4e, 0x38, 0x79, 0x04, 0x6d, 0x2d, 0x17, 0xe8, 0xea, 0xad, 0x8a, 0xbc, 0xe5, 0x3e, 0x27,
	0x3c, 0x1a, 0x43, 0xf7, 0x1a, 0x80, 0x1c, 0x41, 0x7b, 0x52, 0xae, 0xb2, 0x85, 0xe0, 0xfe, 0x3d,
	0x72, 0x0c, 0x9d, 0x2f, 0xf8, 0xcb, 0x5e, 0x64, 0x39, 0xfa, 0x9e, 0x2b, 0x51, 0x9c, 0x69, 0x34,
	0x73, 0xbf, 0x11, 0x69, 0xf0, 0x77, 0x6f, 0x61, 0x14, 0x79, 0x0c, 0x9d, 0x6b, 0x54, 0xaf, 0x42,
	0x6d, 0xab, 0x2b, 0xc8, 0xe7, 0xd0, 0x9f, 0x4a, 0x6b, 0x65, 0x91, 0x6a, 0xcc, 0xd8, 0x1c, 0x79,
	0xd0, 0x08, 0xbd, 0xb8, 0x43, 0x7b, 0xb5, 0x4a, 0x6b, 0x91, 0x3c, 0x81, 0xee, 0x4c, 0x4a, 0xab,
	0xb4, 0x28, 0x6d, 0x15, 0x63, 0x97, 0xde, 0x08, 0xd1, 0x6f, 0xe8, 0x5f, 0xea, 0xac, 0x34, 0xf5,
	0xcd, 0xf7, 0xbf, 0xc4, 0x36, 0x43, 0x63, 0x97, 0xe1, 0x29, 0x1c, 0x65, 0xcc, 0x0a, 0x59, 0xa6,
	0x76, 0xad, 0xf0, 0x6a, 0x3c, 0xd4, 0xd2, 0xe5, 0x5a, 0xa1, 0x33, 0xdc, 0x84, 0xb8, 0x49, 0x1b,
	0xcc, 0x26, 0x41, 0x13, 0x3d, 0x83, 0xc1, 0x0e, 0x80, 0x51, 0xb7, 0x09, 0x46, 0x7f, 0x3d, 0x78,
	0x48, 0xcf, 0xce, 0x3f, 0xb8, 0x6c, 0xce, 0x36, 0x4f, 0x4e, 0x10, 0x8e, 0xb7, 0xf3, 0x22, 0xa7,
	0x07, 0xff, 0x1d, 0x27, 0x2f, 0x0f, 0xb5, 0x1a, 0x45, 0x72, 0xf0, 0xb7, 0x08, 0x3f, 0xae, 0xb0,
	0xb4, 0x24, 0xbe, 0xbb, 0x7f, 0x37, 0xce, 0x93, 0xd3, 0x03, 0x9d, 0x46, 0xbd, 0x7f, 0xfb, 0xed,
	0x4d, 0x2e, 0x17, 0x59, 0x99, 0x27, 0xaf, 0x47, 0xd6, 0x26, 0x4c, 0x16, 0xc3, 0x6a, 0x95, 0x98,
	0x5c, 0x0c, 0x0d, 0xea, 0x95, 0x60, 0x68, 0xaa, 0x85, 0xdb, 0xb7, 0x75, 0xd3, 0x56, 0xe5, 0x1d,
	0xff, 0x0b, 0x00, 0x00, 0xff, 0xff, 0xa3, 0x00, 0x95, 0x1d, 0xa7, 0x03, 0x00, 0x00,
}
