// Code generated by protoc-gen-tyr-client. DO NOT EDIT.
// versions:
// - protoc-gen-tyr-client v0.0.1
// - protoc                   (unknown)
// source: rcmd_aigc_community/rcmd_aigc_community.proto

package rcmd_aigc_community

import (
	client "gitlab.ttyuyin.com/avengers/tyr/core/service/grpc/client"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const serviceName = "rcmd-aigc-community"

type Client struct {
	RCMDAigcCommunityClient

	cc *grpc.ClientConn
}

func MustNewClient(ctx context.Context, opts ...grpc.DialOption) *Client {
	c, err := NewClient(ctx, opts...)
	if err != nil {
		panic(err)
	}
	return c
}

func NewClient(ctx context.Context, opts ...grpc.DialOption) (*Client, error) {
	c := &Client{}
	if err := client.DialContextWithConnUpdate(ctx, serviceName, opts, func(cc *grpc.ClientConn) *grpc.ClientConn {
		c.cc, cc = cc, c.cc
		c.RCMDAigcCommunityClient = NewRCMDAigcCommunityClient(c.cc)
		return cc
	}); err != nil {
		return nil, err
	}
	return c, nil
}

func MustNewClientTo(ctx context.Context, target string, opts ...grpc.DialOption) *Client {
	cc, err := client.DialContextTo(ctx, target, opts...)
	if err != nil {
		panic(err)
	}
	return &Client{
		RCMDAigcCommunityClient: NewRCMDAigcCommunityClient(cc),
	}
}
