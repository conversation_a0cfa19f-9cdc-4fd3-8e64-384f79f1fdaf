// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/game-red-dot/game-red-dot.proto

package game_red_dot // import "golang.52tt.com/protocol/services/game-red-dot"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PushTargetType int32

const (
	// 推送给客户端, GAME_RED_DOT_UPDATE_PUSH = 170;
	PushTargetType_PUSH_TARGET_TYPE_APP PushTargetType = 0
	// 推送给客户端透传到H5, GAME_COMMON_H5_PUSH = 188;
	PushTargetType_PUSH_TARGET_TYPE_H5 PushTargetType = 1
)

var PushTargetType_name = map[int32]string{
	0: "PUSH_TARGET_TYPE_APP",
	1: "PUSH_TARGET_TYPE_H5",
}
var PushTargetType_value = map[string]int32{
	"PUSH_TARGET_TYPE_APP": 0,
	"PUSH_TARGET_TYPE_H5":  1,
}

func (x PushTargetType) String() string {
	return proto.EnumName(PushTargetType_name, int32(x))
}
func (PushTargetType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{0}
}

type PushExtraParam struct {
	// 用户id, 不传则不会发推送
	Uid   uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId uint32 `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	// 业务类型下具体划分key，如社区红点区分哪个角色
	BizSuffixKey         string   `protobuf:"bytes,3,opt,name=biz_suffix_key,json=bizSuffixKey,proto3" json:"biz_suffix_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushExtraParam) Reset()         { *m = PushExtraParam{} }
func (m *PushExtraParam) String() string { return proto.CompactTextString(m) }
func (*PushExtraParam) ProtoMessage()    {}
func (*PushExtraParam) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{0}
}
func (m *PushExtraParam) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushExtraParam.Unmarshal(m, b)
}
func (m *PushExtraParam) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushExtraParam.Marshal(b, m, deterministic)
}
func (dst *PushExtraParam) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushExtraParam.Merge(dst, src)
}
func (m *PushExtraParam) XXX_Size() int {
	return xxx_messageInfo_PushExtraParam.Size(m)
}
func (m *PushExtraParam) XXX_DiscardUnknown() {
	xxx_messageInfo_PushExtraParam.DiscardUnknown(m)
}

var xxx_messageInfo_PushExtraParam proto.InternalMessageInfo

func (m *PushExtraParam) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PushExtraParam) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *PushExtraParam) GetBizSuffixKey() string {
	if m != nil {
		return m.BizSuffixKey
	}
	return ""
}

// 新增红点信息
type AddRedDotReq struct {
	BizType        uint32          `protobuf:"varint,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	BizKey         string          `protobuf:"bytes,2,opt,name=biz_key,json=bizKey,proto3" json:"biz_key,omitempty"`
	PushExtraParam *PushExtraParam `protobuf:"bytes,3,opt,name=push_extra_param,json=pushExtraParam,proto3" json:"push_extra_param,omitempty"`
	// 区分推送给客户端还是前端
	PushTargetType       PushTargetType `protobuf:"varint,4,opt,name=push_target_type,json=pushTargetType,proto3,enum=game_red_dot.PushTargetType" json:"push_target_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *AddRedDotReq) Reset()         { *m = AddRedDotReq{} }
func (m *AddRedDotReq) String() string { return proto.CompactTextString(m) }
func (*AddRedDotReq) ProtoMessage()    {}
func (*AddRedDotReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{1}
}
func (m *AddRedDotReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRedDotReq.Unmarshal(m, b)
}
func (m *AddRedDotReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRedDotReq.Marshal(b, m, deterministic)
}
func (dst *AddRedDotReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRedDotReq.Merge(dst, src)
}
func (m *AddRedDotReq) XXX_Size() int {
	return xxx_messageInfo_AddRedDotReq.Size(m)
}
func (m *AddRedDotReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRedDotReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddRedDotReq proto.InternalMessageInfo

func (m *AddRedDotReq) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *AddRedDotReq) GetBizKey() string {
	if m != nil {
		return m.BizKey
	}
	return ""
}

func (m *AddRedDotReq) GetPushExtraParam() *PushExtraParam {
	if m != nil {
		return m.PushExtraParam
	}
	return nil
}

func (m *AddRedDotReq) GetPushTargetType() PushTargetType {
	if m != nil {
		return m.PushTargetType
	}
	return PushTargetType_PUSH_TARGET_TYPE_APP
}

type AddRedDotResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRedDotResp) Reset()         { *m = AddRedDotResp{} }
func (m *AddRedDotResp) String() string { return proto.CompactTextString(m) }
func (*AddRedDotResp) ProtoMessage()    {}
func (*AddRedDotResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{2}
}
func (m *AddRedDotResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRedDotResp.Unmarshal(m, b)
}
func (m *AddRedDotResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRedDotResp.Marshal(b, m, deterministic)
}
func (dst *AddRedDotResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRedDotResp.Merge(dst, src)
}
func (m *AddRedDotResp) XXX_Size() int {
	return xxx_messageInfo_AddRedDotResp.Size(m)
}
func (m *AddRedDotResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRedDotResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddRedDotResp proto.InternalMessageInfo

// 拉取红点信息
type BatchGetRedDotInfoReq struct {
	Params               []*BatchGetRedDotInfoReq_GetRedDotInfoParam `protobuf:"bytes,1,rep,name=params,proto3" json:"params,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                    `json:"-"`
	XXX_unrecognized     []byte                                      `json:"-"`
	XXX_sizecache        int32                                       `json:"-"`
}

func (m *BatchGetRedDotInfoReq) Reset()         { *m = BatchGetRedDotInfoReq{} }
func (m *BatchGetRedDotInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetRedDotInfoReq) ProtoMessage()    {}
func (*BatchGetRedDotInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{3}
}
func (m *BatchGetRedDotInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRedDotInfoReq.Unmarshal(m, b)
}
func (m *BatchGetRedDotInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRedDotInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetRedDotInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRedDotInfoReq.Merge(dst, src)
}
func (m *BatchGetRedDotInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetRedDotInfoReq.Size(m)
}
func (m *BatchGetRedDotInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRedDotInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRedDotInfoReq proto.InternalMessageInfo

func (m *BatchGetRedDotInfoReq) GetParams() []*BatchGetRedDotInfoReq_GetRedDotInfoParam {
	if m != nil {
		return m.Params
	}
	return nil
}

type BatchGetRedDotInfoReq_GetRedDotInfoParam struct {
	BizType              uint32   `protobuf:"varint,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	BizKey               string   `protobuf:"bytes,2,opt,name=biz_key,json=bizKey,proto3" json:"biz_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetRedDotInfoReq_GetRedDotInfoParam) Reset() {
	*m = BatchGetRedDotInfoReq_GetRedDotInfoParam{}
}
func (m *BatchGetRedDotInfoReq_GetRedDotInfoParam) String() string { return proto.CompactTextString(m) }
func (*BatchGetRedDotInfoReq_GetRedDotInfoParam) ProtoMessage()    {}
func (*BatchGetRedDotInfoReq_GetRedDotInfoParam) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{3, 0}
}
func (m *BatchGetRedDotInfoReq_GetRedDotInfoParam) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRedDotInfoReq_GetRedDotInfoParam.Unmarshal(m, b)
}
func (m *BatchGetRedDotInfoReq_GetRedDotInfoParam) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRedDotInfoReq_GetRedDotInfoParam.Marshal(b, m, deterministic)
}
func (dst *BatchGetRedDotInfoReq_GetRedDotInfoParam) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRedDotInfoReq_GetRedDotInfoParam.Merge(dst, src)
}
func (m *BatchGetRedDotInfoReq_GetRedDotInfoParam) XXX_Size() int {
	return xxx_messageInfo_BatchGetRedDotInfoReq_GetRedDotInfoParam.Size(m)
}
func (m *BatchGetRedDotInfoReq_GetRedDotInfoParam) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRedDotInfoReq_GetRedDotInfoParam.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRedDotInfoReq_GetRedDotInfoParam proto.InternalMessageInfo

func (m *BatchGetRedDotInfoReq_GetRedDotInfoParam) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *BatchGetRedDotInfoReq_GetRedDotInfoParam) GetBizKey() string {
	if m != nil {
		return m.BizKey
	}
	return ""
}

type BatchGetRedDotInfoResp struct {
	// key:biz_type, value:红点信息
	RedDotInfos map[uint32]*RedDotInfo `protobuf:"bytes,2,rep,name=red_dot_infos,json=redDotInfos,proto3" json:"red_dot_infos,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// key:biz_type#biz_key, value:红点信息
	RedDotMap            map[string]*RedDotInfo `protobuf:"bytes,3,rep,name=red_dot_map,json=redDotMap,proto3" json:"red_dot_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchGetRedDotInfoResp) Reset()         { *m = BatchGetRedDotInfoResp{} }
func (m *BatchGetRedDotInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetRedDotInfoResp) ProtoMessage()    {}
func (*BatchGetRedDotInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{4}
}
func (m *BatchGetRedDotInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRedDotInfoResp.Unmarshal(m, b)
}
func (m *BatchGetRedDotInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRedDotInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetRedDotInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRedDotInfoResp.Merge(dst, src)
}
func (m *BatchGetRedDotInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetRedDotInfoResp.Size(m)
}
func (m *BatchGetRedDotInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRedDotInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRedDotInfoResp proto.InternalMessageInfo

func (m *BatchGetRedDotInfoResp) GetRedDotInfos() map[uint32]*RedDotInfo {
	if m != nil {
		return m.RedDotInfos
	}
	return nil
}

func (m *BatchGetRedDotInfoResp) GetRedDotMap() map[string]*RedDotInfo {
	if m != nil {
		return m.RedDotMap
	}
	return nil
}

type RedDotInfo struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	LastId               int64    `protobuf:"varint,2,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RedDotInfo) Reset()         { *m = RedDotInfo{} }
func (m *RedDotInfo) String() string { return proto.CompactTextString(m) }
func (*RedDotInfo) ProtoMessage()    {}
func (*RedDotInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{5}
}
func (m *RedDotInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RedDotInfo.Unmarshal(m, b)
}
func (m *RedDotInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RedDotInfo.Marshal(b, m, deterministic)
}
func (dst *RedDotInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RedDotInfo.Merge(dst, src)
}
func (m *RedDotInfo) XXX_Size() int {
	return xxx_messageInfo_RedDotInfo.Size(m)
}
func (m *RedDotInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RedDotInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RedDotInfo proto.InternalMessageInfo

func (m *RedDotInfo) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *RedDotInfo) GetLastId() int64 {
	if m != nil {
		return m.LastId
	}
	return 0
}

// 标记红点信息已读
type MarkRedDotReadReq struct {
	BizType              uint32   `protobuf:"varint,2,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	BizKey               string   `protobuf:"bytes,3,opt,name=biz_key,json=bizKey,proto3" json:"biz_key,omitempty"`
	LastId               int64    `protobuf:"varint,5,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkRedDotReadReq) Reset()         { *m = MarkRedDotReadReq{} }
func (m *MarkRedDotReadReq) String() string { return proto.CompactTextString(m) }
func (*MarkRedDotReadReq) ProtoMessage()    {}
func (*MarkRedDotReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{6}
}
func (m *MarkRedDotReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkRedDotReadReq.Unmarshal(m, b)
}
func (m *MarkRedDotReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkRedDotReadReq.Marshal(b, m, deterministic)
}
func (dst *MarkRedDotReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkRedDotReadReq.Merge(dst, src)
}
func (m *MarkRedDotReadReq) XXX_Size() int {
	return xxx_messageInfo_MarkRedDotReadReq.Size(m)
}
func (m *MarkRedDotReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkRedDotReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkRedDotReadReq proto.InternalMessageInfo

func (m *MarkRedDotReadReq) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *MarkRedDotReadReq) GetBizKey() string {
	if m != nil {
		return m.BizKey
	}
	return ""
}

func (m *MarkRedDotReadReq) GetLastId() int64 {
	if m != nil {
		return m.LastId
	}
	return 0
}

type MarkRedDotReadResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkRedDotReadResp) Reset()         { *m = MarkRedDotReadResp{} }
func (m *MarkRedDotReadResp) String() string { return proto.CompactTextString(m) }
func (*MarkRedDotReadResp) ProtoMessage()    {}
func (*MarkRedDotReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{7}
}
func (m *MarkRedDotReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkRedDotReadResp.Unmarshal(m, b)
}
func (m *MarkRedDotReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkRedDotReadResp.Marshal(b, m, deterministic)
}
func (dst *MarkRedDotReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkRedDotReadResp.Merge(dst, src)
}
func (m *MarkRedDotReadResp) XXX_Size() int {
	return xxx_messageInfo_MarkRedDotReadResp.Size(m)
}
func (m *MarkRedDotReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkRedDotReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkRedDotReadResp proto.InternalMessageInfo

// 批量增加红点
type BatchAddRedDotReq struct {
	// 业务场景类型, 只可批量增加同一类型的红点
	BizType uint32 `protobuf:"varint,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	// 新增红点参数
	Params               []*BatchAddRedDotReq_AddRedDotParam `protobuf:"bytes,2,rep,name=params,proto3" json:"params,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *BatchAddRedDotReq) Reset()         { *m = BatchAddRedDotReq{} }
func (m *BatchAddRedDotReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddRedDotReq) ProtoMessage()    {}
func (*BatchAddRedDotReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{8}
}
func (m *BatchAddRedDotReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddRedDotReq.Unmarshal(m, b)
}
func (m *BatchAddRedDotReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddRedDotReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddRedDotReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddRedDotReq.Merge(dst, src)
}
func (m *BatchAddRedDotReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddRedDotReq.Size(m)
}
func (m *BatchAddRedDotReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddRedDotReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddRedDotReq proto.InternalMessageInfo

func (m *BatchAddRedDotReq) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *BatchAddRedDotReq) GetParams() []*BatchAddRedDotReq_AddRedDotParam {
	if m != nil {
		return m.Params
	}
	return nil
}

type BatchAddRedDotReq_AddRedDotParam struct {
	// 业务场景key
	BizKey string `protobuf:"bytes,2,opt,name=biz_key,json=bizKey,proto3" json:"biz_key,omitempty"`
	// 推送需要用到的额外参数, 不需要发推送则不传
	PushExtraParam *PushExtraParam `protobuf:"bytes,3,opt,name=push_extra_param,json=pushExtraParam,proto3" json:"push_extra_param,omitempty"`
	// 区分推送给客户端还是前端
	PushTargetType       PushTargetType `protobuf:"varint,4,opt,name=push_target_type,json=pushTargetType,proto3,enum=game_red_dot.PushTargetType" json:"push_target_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchAddRedDotReq_AddRedDotParam) Reset()         { *m = BatchAddRedDotReq_AddRedDotParam{} }
func (m *BatchAddRedDotReq_AddRedDotParam) String() string { return proto.CompactTextString(m) }
func (*BatchAddRedDotReq_AddRedDotParam) ProtoMessage()    {}
func (*BatchAddRedDotReq_AddRedDotParam) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{8, 0}
}
func (m *BatchAddRedDotReq_AddRedDotParam) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddRedDotReq_AddRedDotParam.Unmarshal(m, b)
}
func (m *BatchAddRedDotReq_AddRedDotParam) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddRedDotReq_AddRedDotParam.Marshal(b, m, deterministic)
}
func (dst *BatchAddRedDotReq_AddRedDotParam) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddRedDotReq_AddRedDotParam.Merge(dst, src)
}
func (m *BatchAddRedDotReq_AddRedDotParam) XXX_Size() int {
	return xxx_messageInfo_BatchAddRedDotReq_AddRedDotParam.Size(m)
}
func (m *BatchAddRedDotReq_AddRedDotParam) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddRedDotReq_AddRedDotParam.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddRedDotReq_AddRedDotParam proto.InternalMessageInfo

func (m *BatchAddRedDotReq_AddRedDotParam) GetBizKey() string {
	if m != nil {
		return m.BizKey
	}
	return ""
}

func (m *BatchAddRedDotReq_AddRedDotParam) GetPushExtraParam() *PushExtraParam {
	if m != nil {
		return m.PushExtraParam
	}
	return nil
}

func (m *BatchAddRedDotReq_AddRedDotParam) GetPushTargetType() PushTargetType {
	if m != nil {
		return m.PushTargetType
	}
	return PushTargetType_PUSH_TARGET_TYPE_APP
}

type BatchAddRedDotResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddRedDotResp) Reset()         { *m = BatchAddRedDotResp{} }
func (m *BatchAddRedDotResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddRedDotResp) ProtoMessage()    {}
func (*BatchAddRedDotResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{9}
}
func (m *BatchAddRedDotResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddRedDotResp.Unmarshal(m, b)
}
func (m *BatchAddRedDotResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddRedDotResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddRedDotResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddRedDotResp.Merge(dst, src)
}
func (m *BatchAddRedDotResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddRedDotResp.Size(m)
}
func (m *BatchAddRedDotResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddRedDotResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddRedDotResp proto.InternalMessageInfo

// 批量清除红点
type BatchMarkRedDotReadReq struct {
	// 业务类型
	BizType uint32 `protobuf:"varint,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	// 业务场景key
	BizKeys              []string `protobuf:"bytes,2,rep,name=biz_keys,json=bizKeys,proto3" json:"biz_keys,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchMarkRedDotReadReq) Reset()         { *m = BatchMarkRedDotReadReq{} }
func (m *BatchMarkRedDotReadReq) String() string { return proto.CompactTextString(m) }
func (*BatchMarkRedDotReadReq) ProtoMessage()    {}
func (*BatchMarkRedDotReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{10}
}
func (m *BatchMarkRedDotReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchMarkRedDotReadReq.Unmarshal(m, b)
}
func (m *BatchMarkRedDotReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchMarkRedDotReadReq.Marshal(b, m, deterministic)
}
func (dst *BatchMarkRedDotReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchMarkRedDotReadReq.Merge(dst, src)
}
func (m *BatchMarkRedDotReadReq) XXX_Size() int {
	return xxx_messageInfo_BatchMarkRedDotReadReq.Size(m)
}
func (m *BatchMarkRedDotReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchMarkRedDotReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchMarkRedDotReadReq proto.InternalMessageInfo

func (m *BatchMarkRedDotReadReq) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *BatchMarkRedDotReadReq) GetBizKeys() []string {
	if m != nil {
		return m.BizKeys
	}
	return nil
}

type BatchMarkRedDotReadResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchMarkRedDotReadResp) Reset()         { *m = BatchMarkRedDotReadResp{} }
func (m *BatchMarkRedDotReadResp) String() string { return proto.CompactTextString(m) }
func (*BatchMarkRedDotReadResp) ProtoMessage()    {}
func (*BatchMarkRedDotReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{11}
}
func (m *BatchMarkRedDotReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchMarkRedDotReadResp.Unmarshal(m, b)
}
func (m *BatchMarkRedDotReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchMarkRedDotReadResp.Marshal(b, m, deterministic)
}
func (dst *BatchMarkRedDotReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchMarkRedDotReadResp.Merge(dst, src)
}
func (m *BatchMarkRedDotReadResp) XXX_Size() int {
	return xxx_messageInfo_BatchMarkRedDotReadResp.Size(m)
}
func (m *BatchMarkRedDotReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchMarkRedDotReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchMarkRedDotReadResp proto.InternalMessageInfo

// 增加聚合类型红点
type AddAggregateRedDotReq struct {
	// 业务类型
	BizType uint32 `protobuf:"varint,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	// 一级统计维度，通常是uid
	AggregateKey string `protobuf:"bytes,2,opt,name=aggregate_key,json=aggregateKey,proto3" json:"aggregate_key,omitempty"`
	// 二级统计维度，在ai社区类型为roleId
	BizSuffixKey string `protobuf:"bytes,3,opt,name=biz_suffix_key,json=bizSuffixKey,proto3" json:"biz_suffix_key,omitempty"`
	// 推送需要用到的额外参数
	PushExtraParam *PushExtraParam `protobuf:"bytes,4,opt,name=push_extra_param,json=pushExtraParam,proto3" json:"push_extra_param,omitempty"`
	// 区分推送给客户端还是前端
	PushTargetType       PushTargetType `protobuf:"varint,5,opt,name=push_target_type,json=pushTargetType,proto3,enum=game_red_dot.PushTargetType" json:"push_target_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *AddAggregateRedDotReq) Reset()         { *m = AddAggregateRedDotReq{} }
func (m *AddAggregateRedDotReq) String() string { return proto.CompactTextString(m) }
func (*AddAggregateRedDotReq) ProtoMessage()    {}
func (*AddAggregateRedDotReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{12}
}
func (m *AddAggregateRedDotReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAggregateRedDotReq.Unmarshal(m, b)
}
func (m *AddAggregateRedDotReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAggregateRedDotReq.Marshal(b, m, deterministic)
}
func (dst *AddAggregateRedDotReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAggregateRedDotReq.Merge(dst, src)
}
func (m *AddAggregateRedDotReq) XXX_Size() int {
	return xxx_messageInfo_AddAggregateRedDotReq.Size(m)
}
func (m *AddAggregateRedDotReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAggregateRedDotReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddAggregateRedDotReq proto.InternalMessageInfo

func (m *AddAggregateRedDotReq) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *AddAggregateRedDotReq) GetAggregateKey() string {
	if m != nil {
		return m.AggregateKey
	}
	return ""
}

func (m *AddAggregateRedDotReq) GetBizSuffixKey() string {
	if m != nil {
		return m.BizSuffixKey
	}
	return ""
}

func (m *AddAggregateRedDotReq) GetPushExtraParam() *PushExtraParam {
	if m != nil {
		return m.PushExtraParam
	}
	return nil
}

func (m *AddAggregateRedDotReq) GetPushTargetType() PushTargetType {
	if m != nil {
		return m.PushTargetType
	}
	return PushTargetType_PUSH_TARGET_TYPE_APP
}

type AddAggregateRedDotResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAggregateRedDotResp) Reset()         { *m = AddAggregateRedDotResp{} }
func (m *AddAggregateRedDotResp) String() string { return proto.CompactTextString(m) }
func (*AddAggregateRedDotResp) ProtoMessage()    {}
func (*AddAggregateRedDotResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{13}
}
func (m *AddAggregateRedDotResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAggregateRedDotResp.Unmarshal(m, b)
}
func (m *AddAggregateRedDotResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAggregateRedDotResp.Marshal(b, m, deterministic)
}
func (dst *AddAggregateRedDotResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAggregateRedDotResp.Merge(dst, src)
}
func (m *AddAggregateRedDotResp) XXX_Size() int {
	return xxx_messageInfo_AddAggregateRedDotResp.Size(m)
}
func (m *AddAggregateRedDotResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAggregateRedDotResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddAggregateRedDotResp proto.InternalMessageInfo

// 拉取聚合类型红点信息
type GetAggregateRedDotReq struct {
	BizType              uint32   `protobuf:"varint,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	AggregateKey         string   `protobuf:"bytes,2,opt,name=aggregate_key,json=aggregateKey,proto3" json:"aggregate_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAggregateRedDotReq) Reset()         { *m = GetAggregateRedDotReq{} }
func (m *GetAggregateRedDotReq) String() string { return proto.CompactTextString(m) }
func (*GetAggregateRedDotReq) ProtoMessage()    {}
func (*GetAggregateRedDotReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{14}
}
func (m *GetAggregateRedDotReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAggregateRedDotReq.Unmarshal(m, b)
}
func (m *GetAggregateRedDotReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAggregateRedDotReq.Marshal(b, m, deterministic)
}
func (dst *GetAggregateRedDotReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAggregateRedDotReq.Merge(dst, src)
}
func (m *GetAggregateRedDotReq) XXX_Size() int {
	return xxx_messageInfo_GetAggregateRedDotReq.Size(m)
}
func (m *GetAggregateRedDotReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAggregateRedDotReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAggregateRedDotReq proto.InternalMessageInfo

func (m *GetAggregateRedDotReq) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *GetAggregateRedDotReq) GetAggregateKey() string {
	if m != nil {
		return m.AggregateKey
	}
	return ""
}

type GetAggregateRedDotResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAggregateRedDotResp) Reset()         { *m = GetAggregateRedDotResp{} }
func (m *GetAggregateRedDotResp) String() string { return proto.CompactTextString(m) }
func (*GetAggregateRedDotResp) ProtoMessage()    {}
func (*GetAggregateRedDotResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{15}
}
func (m *GetAggregateRedDotResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAggregateRedDotResp.Unmarshal(m, b)
}
func (m *GetAggregateRedDotResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAggregateRedDotResp.Marshal(b, m, deterministic)
}
func (dst *GetAggregateRedDotResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAggregateRedDotResp.Merge(dst, src)
}
func (m *GetAggregateRedDotResp) XXX_Size() int {
	return xxx_messageInfo_GetAggregateRedDotResp.Size(m)
}
func (m *GetAggregateRedDotResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAggregateRedDotResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAggregateRedDotResp proto.InternalMessageInfo

func (m *GetAggregateRedDotResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

// 标记聚合类型红点已读
type MarkAggregateRedDotReadReq struct {
	BizType              uint32   `protobuf:"varint,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	AggregateKey         string   `protobuf:"bytes,2,opt,name=aggregate_key,json=aggregateKey,proto3" json:"aggregate_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkAggregateRedDotReadReq) Reset()         { *m = MarkAggregateRedDotReadReq{} }
func (m *MarkAggregateRedDotReadReq) String() string { return proto.CompactTextString(m) }
func (*MarkAggregateRedDotReadReq) ProtoMessage()    {}
func (*MarkAggregateRedDotReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{16}
}
func (m *MarkAggregateRedDotReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkAggregateRedDotReadReq.Unmarshal(m, b)
}
func (m *MarkAggregateRedDotReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkAggregateRedDotReadReq.Marshal(b, m, deterministic)
}
func (dst *MarkAggregateRedDotReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkAggregateRedDotReadReq.Merge(dst, src)
}
func (m *MarkAggregateRedDotReadReq) XXX_Size() int {
	return xxx_messageInfo_MarkAggregateRedDotReadReq.Size(m)
}
func (m *MarkAggregateRedDotReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkAggregateRedDotReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkAggregateRedDotReadReq proto.InternalMessageInfo

func (m *MarkAggregateRedDotReadReq) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *MarkAggregateRedDotReadReq) GetAggregateKey() string {
	if m != nil {
		return m.AggregateKey
	}
	return ""
}

type MarkAggregateRedDotReadResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkAggregateRedDotReadResp) Reset()         { *m = MarkAggregateRedDotReadResp{} }
func (m *MarkAggregateRedDotReadResp) String() string { return proto.CompactTextString(m) }
func (*MarkAggregateRedDotReadResp) ProtoMessage()    {}
func (*MarkAggregateRedDotReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_red_dot_6b0363111ddbb52c, []int{17}
}
func (m *MarkAggregateRedDotReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkAggregateRedDotReadResp.Unmarshal(m, b)
}
func (m *MarkAggregateRedDotReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkAggregateRedDotReadResp.Marshal(b, m, deterministic)
}
func (dst *MarkAggregateRedDotReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkAggregateRedDotReadResp.Merge(dst, src)
}
func (m *MarkAggregateRedDotReadResp) XXX_Size() int {
	return xxx_messageInfo_MarkAggregateRedDotReadResp.Size(m)
}
func (m *MarkAggregateRedDotReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkAggregateRedDotReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkAggregateRedDotReadResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*PushExtraParam)(nil), "game_red_dot.PushExtraParam")
	proto.RegisterType((*AddRedDotReq)(nil), "game_red_dot.AddRedDotReq")
	proto.RegisterType((*AddRedDotResp)(nil), "game_red_dot.AddRedDotResp")
	proto.RegisterType((*BatchGetRedDotInfoReq)(nil), "game_red_dot.BatchGetRedDotInfoReq")
	proto.RegisterType((*BatchGetRedDotInfoReq_GetRedDotInfoParam)(nil), "game_red_dot.BatchGetRedDotInfoReq.GetRedDotInfoParam")
	proto.RegisterType((*BatchGetRedDotInfoResp)(nil), "game_red_dot.BatchGetRedDotInfoResp")
	proto.RegisterMapType((map[uint32]*RedDotInfo)(nil), "game_red_dot.BatchGetRedDotInfoResp.RedDotInfosEntry")
	proto.RegisterMapType((map[string]*RedDotInfo)(nil), "game_red_dot.BatchGetRedDotInfoResp.RedDotMapEntry")
	proto.RegisterType((*RedDotInfo)(nil), "game_red_dot.RedDotInfo")
	proto.RegisterType((*MarkRedDotReadReq)(nil), "game_red_dot.MarkRedDotReadReq")
	proto.RegisterType((*MarkRedDotReadResp)(nil), "game_red_dot.MarkRedDotReadResp")
	proto.RegisterType((*BatchAddRedDotReq)(nil), "game_red_dot.BatchAddRedDotReq")
	proto.RegisterType((*BatchAddRedDotReq_AddRedDotParam)(nil), "game_red_dot.BatchAddRedDotReq.AddRedDotParam")
	proto.RegisterType((*BatchAddRedDotResp)(nil), "game_red_dot.BatchAddRedDotResp")
	proto.RegisterType((*BatchMarkRedDotReadReq)(nil), "game_red_dot.BatchMarkRedDotReadReq")
	proto.RegisterType((*BatchMarkRedDotReadResp)(nil), "game_red_dot.BatchMarkRedDotReadResp")
	proto.RegisterType((*AddAggregateRedDotReq)(nil), "game_red_dot.AddAggregateRedDotReq")
	proto.RegisterType((*AddAggregateRedDotResp)(nil), "game_red_dot.AddAggregateRedDotResp")
	proto.RegisterType((*GetAggregateRedDotReq)(nil), "game_red_dot.GetAggregateRedDotReq")
	proto.RegisterType((*GetAggregateRedDotResp)(nil), "game_red_dot.GetAggregateRedDotResp")
	proto.RegisterType((*MarkAggregateRedDotReadReq)(nil), "game_red_dot.MarkAggregateRedDotReadReq")
	proto.RegisterType((*MarkAggregateRedDotReadResp)(nil), "game_red_dot.MarkAggregateRedDotReadResp")
	proto.RegisterEnum("game_red_dot.PushTargetType", PushTargetType_name, PushTargetType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GameRedDotClient is the client API for GameRedDot service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameRedDotClient interface {
	AddRedDot(ctx context.Context, in *AddRedDotReq, opts ...grpc.CallOption) (*AddRedDotResp, error)
	BatchGetRedDotInfo(ctx context.Context, in *BatchGetRedDotInfoReq, opts ...grpc.CallOption) (*BatchGetRedDotInfoResp, error)
	MarkRedDotRead(ctx context.Context, in *MarkRedDotReadReq, opts ...grpc.CallOption) (*MarkRedDotReadResp, error)
	BatchAddRedDot(ctx context.Context, in *BatchAddRedDotReq, opts ...grpc.CallOption) (*BatchAddRedDotResp, error)
	BatchMarkRedDotRead(ctx context.Context, in *BatchMarkRedDotReadReq, opts ...grpc.CallOption) (*BatchMarkRedDotReadResp, error)
	AddAggregateRedDot(ctx context.Context, in *AddAggregateRedDotReq, opts ...grpc.CallOption) (*AddAggregateRedDotResp, error)
	GetAggregateRedDot(ctx context.Context, in *GetAggregateRedDotReq, opts ...grpc.CallOption) (*GetAggregateRedDotResp, error)
	MarkAggregateRedDotRead(ctx context.Context, in *MarkAggregateRedDotReadReq, opts ...grpc.CallOption) (*MarkAggregateRedDotReadResp, error)
}

type gameRedDotClient struct {
	cc *grpc.ClientConn
}

func NewGameRedDotClient(cc *grpc.ClientConn) GameRedDotClient {
	return &gameRedDotClient{cc}
}

func (c *gameRedDotClient) AddRedDot(ctx context.Context, in *AddRedDotReq, opts ...grpc.CallOption) (*AddRedDotResp, error) {
	out := new(AddRedDotResp)
	err := c.cc.Invoke(ctx, "/game_red_dot.GameRedDot/AddRedDot", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRedDotClient) BatchGetRedDotInfo(ctx context.Context, in *BatchGetRedDotInfoReq, opts ...grpc.CallOption) (*BatchGetRedDotInfoResp, error) {
	out := new(BatchGetRedDotInfoResp)
	err := c.cc.Invoke(ctx, "/game_red_dot.GameRedDot/BatchGetRedDotInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRedDotClient) MarkRedDotRead(ctx context.Context, in *MarkRedDotReadReq, opts ...grpc.CallOption) (*MarkRedDotReadResp, error) {
	out := new(MarkRedDotReadResp)
	err := c.cc.Invoke(ctx, "/game_red_dot.GameRedDot/MarkRedDotRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRedDotClient) BatchAddRedDot(ctx context.Context, in *BatchAddRedDotReq, opts ...grpc.CallOption) (*BatchAddRedDotResp, error) {
	out := new(BatchAddRedDotResp)
	err := c.cc.Invoke(ctx, "/game_red_dot.GameRedDot/BatchAddRedDot", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRedDotClient) BatchMarkRedDotRead(ctx context.Context, in *BatchMarkRedDotReadReq, opts ...grpc.CallOption) (*BatchMarkRedDotReadResp, error) {
	out := new(BatchMarkRedDotReadResp)
	err := c.cc.Invoke(ctx, "/game_red_dot.GameRedDot/BatchMarkRedDotRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRedDotClient) AddAggregateRedDot(ctx context.Context, in *AddAggregateRedDotReq, opts ...grpc.CallOption) (*AddAggregateRedDotResp, error) {
	out := new(AddAggregateRedDotResp)
	err := c.cc.Invoke(ctx, "/game_red_dot.GameRedDot/AddAggregateRedDot", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRedDotClient) GetAggregateRedDot(ctx context.Context, in *GetAggregateRedDotReq, opts ...grpc.CallOption) (*GetAggregateRedDotResp, error) {
	out := new(GetAggregateRedDotResp)
	err := c.cc.Invoke(ctx, "/game_red_dot.GameRedDot/GetAggregateRedDot", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameRedDotClient) MarkAggregateRedDotRead(ctx context.Context, in *MarkAggregateRedDotReadReq, opts ...grpc.CallOption) (*MarkAggregateRedDotReadResp, error) {
	out := new(MarkAggregateRedDotReadResp)
	err := c.cc.Invoke(ctx, "/game_red_dot.GameRedDot/MarkAggregateRedDotRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameRedDotServer is the server API for GameRedDot service.
type GameRedDotServer interface {
	AddRedDot(context.Context, *AddRedDotReq) (*AddRedDotResp, error)
	BatchGetRedDotInfo(context.Context, *BatchGetRedDotInfoReq) (*BatchGetRedDotInfoResp, error)
	MarkRedDotRead(context.Context, *MarkRedDotReadReq) (*MarkRedDotReadResp, error)
	BatchAddRedDot(context.Context, *BatchAddRedDotReq) (*BatchAddRedDotResp, error)
	BatchMarkRedDotRead(context.Context, *BatchMarkRedDotReadReq) (*BatchMarkRedDotReadResp, error)
	AddAggregateRedDot(context.Context, *AddAggregateRedDotReq) (*AddAggregateRedDotResp, error)
	GetAggregateRedDot(context.Context, *GetAggregateRedDotReq) (*GetAggregateRedDotResp, error)
	MarkAggregateRedDotRead(context.Context, *MarkAggregateRedDotReadReq) (*MarkAggregateRedDotReadResp, error)
}

func RegisterGameRedDotServer(s *grpc.Server, srv GameRedDotServer) {
	s.RegisterService(&_GameRedDot_serviceDesc, srv)
}

func _GameRedDot_AddRedDot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRedDotReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRedDotServer).AddRedDot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_red_dot.GameRedDot/AddRedDot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRedDotServer).AddRedDot(ctx, req.(*AddRedDotReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRedDot_BatchGetRedDotInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetRedDotInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRedDotServer).BatchGetRedDotInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_red_dot.GameRedDot/BatchGetRedDotInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRedDotServer).BatchGetRedDotInfo(ctx, req.(*BatchGetRedDotInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRedDot_MarkRedDotRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkRedDotReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRedDotServer).MarkRedDotRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_red_dot.GameRedDot/MarkRedDotRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRedDotServer).MarkRedDotRead(ctx, req.(*MarkRedDotReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRedDot_BatchAddRedDot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddRedDotReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRedDotServer).BatchAddRedDot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_red_dot.GameRedDot/BatchAddRedDot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRedDotServer).BatchAddRedDot(ctx, req.(*BatchAddRedDotReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRedDot_BatchMarkRedDotRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchMarkRedDotReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRedDotServer).BatchMarkRedDotRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_red_dot.GameRedDot/BatchMarkRedDotRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRedDotServer).BatchMarkRedDotRead(ctx, req.(*BatchMarkRedDotReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRedDot_AddAggregateRedDot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAggregateRedDotReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRedDotServer).AddAggregateRedDot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_red_dot.GameRedDot/AddAggregateRedDot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRedDotServer).AddAggregateRedDot(ctx, req.(*AddAggregateRedDotReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRedDot_GetAggregateRedDot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAggregateRedDotReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRedDotServer).GetAggregateRedDot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_red_dot.GameRedDot/GetAggregateRedDot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRedDotServer).GetAggregateRedDot(ctx, req.(*GetAggregateRedDotReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameRedDot_MarkAggregateRedDotRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkAggregateRedDotReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameRedDotServer).MarkAggregateRedDotRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_red_dot.GameRedDot/MarkAggregateRedDotRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameRedDotServer).MarkAggregateRedDotRead(ctx, req.(*MarkAggregateRedDotReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameRedDot_serviceDesc = grpc.ServiceDesc{
	ServiceName: "game_red_dot.GameRedDot",
	HandlerType: (*GameRedDotServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddRedDot",
			Handler:    _GameRedDot_AddRedDot_Handler,
		},
		{
			MethodName: "BatchGetRedDotInfo",
			Handler:    _GameRedDot_BatchGetRedDotInfo_Handler,
		},
		{
			MethodName: "MarkRedDotRead",
			Handler:    _GameRedDot_MarkRedDotRead_Handler,
		},
		{
			MethodName: "BatchAddRedDot",
			Handler:    _GameRedDot_BatchAddRedDot_Handler,
		},
		{
			MethodName: "BatchMarkRedDotRead",
			Handler:    _GameRedDot_BatchMarkRedDotRead_Handler,
		},
		{
			MethodName: "AddAggregateRedDot",
			Handler:    _GameRedDot_AddAggregateRedDot_Handler,
		},
		{
			MethodName: "GetAggregateRedDot",
			Handler:    _GameRedDot_GetAggregateRedDot_Handler,
		},
		{
			MethodName: "MarkAggregateRedDotRead",
			Handler:    _GameRedDot_MarkAggregateRedDotRead_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/game-red-dot/game-red-dot.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/game-red-dot/game-red-dot.proto", fileDescriptor_game_red_dot_6b0363111ddbb52c)
}

var fileDescriptor_game_red_dot_6b0363111ddbb52c = []byte{
	// 878 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x56, 0x5b, 0x8f, 0xdb, 0x44,
	0x14, 0x8e, 0x93, 0x26, 0x90, 0x93, 0xdd, 0x90, 0x4e, 0xf7, 0xe2, 0xba, 0x20, 0xa2, 0x69, 0x91,
	0x02, 0x52, 0xbd, 0x28, 0xd5, 0x22, 0x04, 0x4f, 0x29, 0xec, 0x4d, 0xab, 0x56, 0x51, 0x36, 0x05,
	0x8a, 0x90, 0xcc, 0x24, 0x9e, 0xcd, 0x5a, 0xb9, 0x78, 0xea, 0x99, 0xac, 0xea, 0x7d, 0xe5, 0x99,
	0x1f, 0x03, 0x6f, 0xfc, 0x0d, 0x7e, 0x0e, 0x4f, 0xc8, 0xb7, 0xc4, 0x63, 0x4f, 0x5a, 0xb3, 0xda,
	0x17, 0xde, 0x3c, 0xe7, 0xcc, 0x7c, 0xdf, 0xb9, 0x7d, 0x33, 0x06, 0x53, 0x88, 0x83, 0x37, 0x4b,
	0x67, 0x3c, 0xe5, 0xce, 0xec, 0x9a, 0x7a, 0x07, 0x13, 0x32, 0xa7, 0x4f, 0x3d, 0x6a, 0x3f, 0xb5,
	0x5d, 0x21, 0x2d, 0x4c, 0xe6, 0xb9, 0xc2, 0x45, 0x5b, 0x81, 0xcd, 0xf2, 0xa8, 0x6d, 0xd9, 0xae,
	0xc0, 0x16, 0x34, 0xfb, 0x4b, 0x7e, 0x75, 0xf4, 0x56, 0x78, 0xa4, 0x4f, 0x3c, 0x32, 0x47, 0x2d,
	0xa8, 0x2c, 0x1d, 0x5b, 0xd7, 0xda, 0x5a, 0x67, 0x7b, 0x10, 0x7c, 0xa2, 0x5d, 0xa8, 0x09, 0x32,
	0xb2, 0x1c, 0x5b, 0x2f, 0x87, 0xc6, 0xaa, 0x20, 0xa3, 0x33, 0x1b, 0x3d, 0x81, 0xe6, 0xc8, 0xb9,
	0xb1, 0xf8, 0xf2, 0xf2, 0xd2, 0x79, 0x6b, 0x4d, 0xa9, 0xaf, 0x57, 0xda, 0x5a, 0xa7, 0x3e, 0xd8,
	0x1a, 0x39, 0x37, 0x17, 0xa1, 0xf1, 0x9c, 0xfa, 0xf8, 0x6f, 0x0d, 0xb6, 0x7a, 0xb6, 0x3d, 0xa0,
	0xf6, 0xf7, 0xae, 0x18, 0xd0, 0x37, 0xe8, 0x21, 0x7c, 0x18, 0x1c, 0x13, 0x3e, 0xa3, 0x31, 0xc9,
	0x07, 0x23, 0xe7, 0x66, 0xe8, 0x33, 0x8a, 0xf6, 0x21, 0xf8, 0x0c, 0xa1, 0xca, 0x21, 0x54, 0x6d,
	0xe4, 0xdc, 0x9c, 0x53, 0x1f, 0x1d, 0x43, 0x8b, 0x2d, 0xf9, 0x95, 0x45, 0x83, 0x30, 0x2d, 0x16,
	0xc4, 0x19, 0x92, 0x35, 0xba, 0x1f, 0x9b, 0xe9, 0x74, 0x4c, 0x39, 0x97, 0x41, 0x93, 0xc9, 0xb9,
	0x25, 0x38, 0x82, 0x78, 0x13, 0x2a, 0xa2, 0x18, 0xee, 0xb5, 0xb5, 0x4e, 0x53, 0x85, 0x33, 0x0c,
	0x37, 0x05, 0x81, 0x45, 0x38, 0xeb, 0x35, 0xfe, 0x08, 0xb6, 0x53, 0x39, 0x71, 0x86, 0xff, 0xd0,
	0x60, 0xf7, 0x39, 0x11, 0xe3, 0xab, 0x13, 0x2a, 0x22, 0xf3, 0xd9, 0xe2, 0xd2, 0x0d, 0xd2, 0x7d,
	0x09, 0xb5, 0x30, 0x5e, 0xae, 0x6b, 0xed, 0x4a, 0xa7, 0xd1, 0xfd, 0x4a, 0x26, 0x52, 0x1e, 0x32,
	0x25, 0x43, 0x94, 0x4a, 0x8c, 0x62, 0x9c, 0x02, 0xca, 0x7b, 0x6f, 0x53, 0x54, 0xfc, 0x4f, 0x19,
	0xf6, 0x54, 0xf4, 0x9c, 0xa1, 0xd7, 0xb0, 0x1d, 0x07, 0x68, 0x39, 0x8b, 0x4b, 0x97, 0xeb, 0xe5,
	0x30, 0xf6, 0xc3, 0xf7, 0xc7, 0xce, 0x99, 0xb9, 0x5e, 0xf2, 0xa3, 0x85, 0xf0, 0xfc, 0x41, 0xc3,
	0x5b, 0x5b, 0xd0, 0x05, 0x34, 0x12, 0xe8, 0x39, 0x61, 0x7a, 0x25, 0x04, 0x7e, 0xf6, 0x1f, 0x80,
	0x5f, 0x10, 0x16, 0xc1, 0xd6, 0xbd, 0x64, 0x6d, 0xfc, 0x04, 0xad, 0x2c, 0x6b, 0x30, 0xc7, 0x41,
	0xce, 0xf1, 0x1c, 0x4f, 0xa9, 0x8f, 0x4c, 0xa8, 0x5e, 0x93, 0xd9, 0x92, 0x86, 0x75, 0x68, 0x74,
	0x75, 0x99, 0x34, 0x45, 0x16, 0x6d, 0xfb, 0xa6, 0xfc, 0xb5, 0x66, 0xfc, 0x00, 0x4d, 0x99, 0x36,
	0x8d, 0x5b, 0xbf, 0x35, 0x2e, 0xfe, 0x16, 0x60, 0xed, 0x40, 0x3b, 0x50, 0x1d, 0xbb, 0xcb, 0x85,
	0x88, 0xa3, 0x8d, 0x16, 0x41, 0xe7, 0x66, 0x84, 0x8b, 0x44, 0x78, 0x95, 0x41, 0x2d, 0x58, 0x9e,
	0xd9, 0xf8, 0x57, 0xb8, 0xff, 0x82, 0x78, 0xd3, 0x64, 0xfe, 0x88, 0x9d, 0xd5, 0x55, 0x79, 0xe3,
	0x08, 0x54, 0x24, 0x5d, 0xa5, 0x18, 0xaa, 0x12, 0xc3, 0x0e, 0xa0, 0x2c, 0x03, 0x67, 0xf8, 0xcf,
	0x32, 0xdc, 0x0f, 0x7b, 0x53, 0x54, 0xd0, 0xc7, 0xab, 0xe1, 0x8f, 0x06, 0xc8, 0x54, 0xf4, 0x39,
	0x8d, 0x65, 0xae, 0x16, 0xf2, 0xd0, 0xff, 0xa5, 0x41, 0x53, 0x76, 0xfd, 0x7f, 0xee, 0x8a, 0x1d,
	0x40, 0xd9, 0x3c, 0x39, 0xc3, 0x2f, 0x63, 0xed, 0xbd, 0xbb, 0x8f, 0x99, 0x72, 0xc6, 0xae, 0x29,
	0xf5, 0xa3, 0x82, 0xd6, 0x43, 0xd7, 0x39, 0xf5, 0x39, 0x7e, 0x08, 0xfb, 0x4a, 0x3c, 0xce, 0xf0,
	0xef, 0x65, 0xd8, 0xed, 0xd9, 0x76, 0x6f, 0x32, 0xf1, 0xe8, 0x84, 0x08, 0x5a, 0xa8, 0x73, 0x8f,
	0x61, 0x9b, 0x24, 0x07, 0x52, 0x45, 0xde, 0x5a, 0x19, 0x83, 0x52, 0x17, 0x7a, 0x01, 0x94, 0x0d,
	0xb9, 0x77, 0x47, 0x0d, 0xa9, 0xde, 0xa2, 0x21, 0x3a, 0xec, 0xa9, 0xca, 0xc1, 0x19, 0xfe, 0x11,
	0x76, 0x4f, 0xa8, 0xb8, 0xfb, 0x42, 0x61, 0x13, 0xf6, 0x54, 0xc0, 0x9c, 0xa9, 0x95, 0x8f, 0x7f,
	0x01, 0x23, 0x68, 0x64, 0xee, 0xc0, 0x7b, 0x27, 0xa4, 0x50, 0x34, 0x9f, 0xc0, 0xa3, 0x8d, 0xe8,
	0x9c, 0x7d, 0xf1, 0x5d, 0xf4, 0x4b, 0xb0, 0xae, 0x18, 0xd2, 0x61, 0xa7, 0xff, 0xea, 0xe2, 0xd4,
	0x1a, 0xf6, 0x06, 0x27, 0x47, 0x43, 0x6b, 0xf8, 0xba, 0x7f, 0x64, 0xf5, 0xfa, 0xfd, 0x56, 0x09,
	0xed, 0xc3, 0x83, 0x9c, 0xe7, 0xf4, 0xb0, 0xa5, 0x75, 0x7f, 0xab, 0x01, 0x9c, 0x90, 0x79, 0x8c,
	0x8d, 0x8e, 0xa1, 0xbe, 0x9a, 0x7f, 0x64, 0xc8, 0xed, 0x4a, 0x5f, 0x00, 0xc6, 0xa3, 0x8d, 0x3e,
	0xce, 0x70, 0x09, 0x91, 0x58, 0x4c, 0xd2, 0xe3, 0x80, 0x1e, 0x17, 0x78, 0x53, 0x8d, 0x27, 0x45,
	0xde, 0x18, 0x5c, 0x42, 0xaf, 0xa0, 0x29, 0x8b, 0x08, 0x7d, 0x2a, 0x9f, 0xcc, 0x49, 0xd6, 0x68,
	0xbf, 0x7b, 0x43, 0x02, 0x2b, 0x5f, 0x03, 0x59, 0xd8, 0xdc, 0x65, 0x98, 0x85, 0x55, 0xdc, 0x22,
	0x25, 0x64, 0xc3, 0x03, 0x85, 0xee, 0x91, 0x2a, 0xd9, 0x7c, 0xdc, 0x9f, 0x15, 0xd8, 0x95, 0x94,
	0x3d, 0x2f, 0x99, 0x6c, 0xd9, 0x95, 0x77, 0x4c, 0xb6, 0xec, 0x1b, 0x94, 0x17, 0x52, 0xe4, 0x25,
	0x92, 0xa5, 0x50, 0xaa, 0x33, 0x4b, 0xa1, 0x56, 0x1a, 0x2e, 0x21, 0x06, 0xfb, 0x1b, 0xe6, 0x1e,
	0x75, 0xf2, 0x1d, 0x54, 0x8b, 0xcf, 0xf8, 0xbc, 0xe0, 0xce, 0x80, 0xf1, 0xf9, 0x97, 0x3f, 0x9b,
	0x13, 0x77, 0x46, 0x16, 0x13, 0xf3, 0xb0, 0x2b, 0x84, 0x39, 0x76, 0xe7, 0x07, 0xe1, 0x4f, 0xf8,
	0xd8, 0x9d, 0x1d, 0x70, 0xea, 0x5d, 0x3b, 0x63, 0xca, 0xa5, 0x7f, 0xf4, 0x51, 0x2d, 0xf4, 0x3f,
	0xfb, 0x37, 0x00, 0x00, 0xff, 0xff, 0x5a, 0xc7, 0xdf, 0xff, 0xd6, 0x0b, 0x00, 0x00,
}
