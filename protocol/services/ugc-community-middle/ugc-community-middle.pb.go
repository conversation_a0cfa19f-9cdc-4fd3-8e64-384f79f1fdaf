// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/ugc-community-middle/ugc-community-middle.proto

package ugc_community_middle // import "golang.52tt.com/protocol/services/ugc-community-middle"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import ugc_community "golang.52tt.com/protocol/services/ugc-community"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PostSource int32

const (
	PostSource_POST_SOURCE_AI_DISTRICT_UNSPECIFIED    PostSource = 0
	PostSource_POST_SOURCE_AI_DISTRICT_RECOMMENDATION PostSource = 1
	PostSource_POST_SOURCE_AI_DISTRICT_PERSON         PostSource = 2
)

var PostSource_name = map[int32]string{
	0: "POST_SOURCE_AI_DISTRICT_UNSPECIFIED",
	1: "POST_SOURCE_AI_DISTRICT_RECOMMENDATION",
	2: "POST_SOURCE_AI_DISTRICT_PERSON",
}
var PostSource_value = map[string]int32{
	"POST_SOURCE_AI_DISTRICT_UNSPECIFIED":    0,
	"POST_SOURCE_AI_DISTRICT_RECOMMENDATION": 1,
	"POST_SOURCE_AI_DISTRICT_PERSON":         2,
}

func (x PostSource) String() string {
	return proto.EnumName(PostSource_name, int32(x))
}
func (PostSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{0}
}

type FeedMode int32

const (
	FeedMode_FEED_MODE_UNSPECIFIED FeedMode = 0
	FeedMode_FEED_MODE_NEXT_PAGE   FeedMode = 1
	FeedMode_FEED_MODE_REFRESH     FeedMode = 2
)

var FeedMode_name = map[int32]string{
	0: "FEED_MODE_UNSPECIFIED",
	1: "FEED_MODE_NEXT_PAGE",
	2: "FEED_MODE_REFRESH",
}
var FeedMode_value = map[string]int32{
	"FEED_MODE_UNSPECIFIED": 0,
	"FEED_MODE_NEXT_PAGE":   1,
	"FEED_MODE_REFRESH":     2,
}

func (x FeedMode) String() string {
	return proto.EnumName(FeedMode_name, int32(x))
}
func (FeedMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{1}
}

type PublishPostRequest struct {
	Post *PublishPostRequest_Post `protobuf:"bytes,2,opt,name=post,proto3" json:"post,omitempty"`
	// 发帖引导任务token
	TaskToken            string   `protobuf:"bytes,3,opt,name=task_token,json=taskToken,proto3" json:"task_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PublishPostRequest) Reset()         { *m = PublishPostRequest{} }
func (m *PublishPostRequest) String() string { return proto.CompactTextString(m) }
func (*PublishPostRequest) ProtoMessage()    {}
func (*PublishPostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{0}
}
func (m *PublishPostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishPostRequest.Unmarshal(m, b)
}
func (m *PublishPostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishPostRequest.Marshal(b, m, deterministic)
}
func (dst *PublishPostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishPostRequest.Merge(dst, src)
}
func (m *PublishPostRequest) XXX_Size() int {
	return xxx_messageInfo_PublishPostRequest.Size(m)
}
func (m *PublishPostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishPostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PublishPostRequest proto.InternalMessageInfo

func (m *PublishPostRequest) GetPost() *PublishPostRequest_Post {
	if m != nil {
		return m.Post
	}
	return nil
}

func (m *PublishPostRequest) GetTaskToken() string {
	if m != nil {
		return m.TaskToken
	}
	return ""
}

type PublishPostRequest_Post struct {
	// 帖子ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 帖子类型
	Type ugc_community.PostType `protobuf:"varint,2,opt,name=type,proto3,enum=ugc_community.PostType" json:"type,omitempty"`
	// 帖子状态
	State ugc_community.PostState `protobuf:"varint,3,opt,name=state,proto3,enum=ugc_community.PostState" json:"state,omitempty"`
	// 发布来源
	Origin ugc_community.PostOrigin `protobuf:"varint,4,opt,name=origin,proto3,enum=ugc_community.PostOrigin" json:"origin,omitempty"`
	// 内容
	Content string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	// 附件列表
	Attachments []*ugc_community.Attachment `protobuf:"bytes,6,rep,name=attachments,proto3" json:"attachments,omitempty"`
	// 业务数据
	BizData *ugc_community.PostBizData `protobuf:"bytes,8,opt,name=biz_data,json=bizData,proto3" json:"biz_data,omitempty"`
	// 关联的话题id
	TopicIdList          []string `protobuf:"bytes,9,rep,name=topic_id_list,json=topicIdList,proto3" json:"topic_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PublishPostRequest_Post) Reset()         { *m = PublishPostRequest_Post{} }
func (m *PublishPostRequest_Post) String() string { return proto.CompactTextString(m) }
func (*PublishPostRequest_Post) ProtoMessage()    {}
func (*PublishPostRequest_Post) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{0, 0}
}
func (m *PublishPostRequest_Post) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishPostRequest_Post.Unmarshal(m, b)
}
func (m *PublishPostRequest_Post) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishPostRequest_Post.Marshal(b, m, deterministic)
}
func (dst *PublishPostRequest_Post) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishPostRequest_Post.Merge(dst, src)
}
func (m *PublishPostRequest_Post) XXX_Size() int {
	return xxx_messageInfo_PublishPostRequest_Post.Size(m)
}
func (m *PublishPostRequest_Post) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishPostRequest_Post.DiscardUnknown(m)
}

var xxx_messageInfo_PublishPostRequest_Post proto.InternalMessageInfo

func (m *PublishPostRequest_Post) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *PublishPostRequest_Post) GetType() ugc_community.PostType {
	if m != nil {
		return m.Type
	}
	return ugc_community.PostType_POST_TYPE_UNSPECIFED
}

func (m *PublishPostRequest_Post) GetState() ugc_community.PostState {
	if m != nil {
		return m.State
	}
	return ugc_community.PostState_POST_STATE_UNSPECIFIED
}

func (m *PublishPostRequest_Post) GetOrigin() ugc_community.PostOrigin {
	if m != nil {
		return m.Origin
	}
	return ugc_community.PostOrigin_POST_ORIGIN_UNSPECIFIED
}

func (m *PublishPostRequest_Post) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PublishPostRequest_Post) GetAttachments() []*ugc_community.Attachment {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *PublishPostRequest_Post) GetBizData() *ugc_community.PostBizData {
	if m != nil {
		return m.BizData
	}
	return nil
}

func (m *PublishPostRequest_Post) GetTopicIdList() []string {
	if m != nil {
		return m.TopicIdList
	}
	return nil
}

type PublishPostResponse struct {
	// 帖子id
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PublishPostResponse) Reset()         { *m = PublishPostResponse{} }
func (m *PublishPostResponse) String() string { return proto.CompactTextString(m) }
func (*PublishPostResponse) ProtoMessage()    {}
func (*PublishPostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{1}
}
func (m *PublishPostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishPostResponse.Unmarshal(m, b)
}
func (m *PublishPostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishPostResponse.Marshal(b, m, deterministic)
}
func (dst *PublishPostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishPostResponse.Merge(dst, src)
}
func (m *PublishPostResponse) XXX_Size() int {
	return xxx_messageInfo_PublishPostResponse.Size(m)
}
func (m *PublishPostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishPostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PublishPostResponse proto.InternalMessageInfo

func (m *PublishPostResponse) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type GeneratePublishPostParamRequest struct {
	Attachments          []*ugc_community.Attachment `protobuf:"bytes,2,rep,name=attachments,proto3" json:"attachments,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GeneratePublishPostParamRequest) Reset()         { *m = GeneratePublishPostParamRequest{} }
func (m *GeneratePublishPostParamRequest) String() string { return proto.CompactTextString(m) }
func (*GeneratePublishPostParamRequest) ProtoMessage()    {}
func (*GeneratePublishPostParamRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{2}
}
func (m *GeneratePublishPostParamRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GeneratePublishPostParamRequest.Unmarshal(m, b)
}
func (m *GeneratePublishPostParamRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GeneratePublishPostParamRequest.Marshal(b, m, deterministic)
}
func (dst *GeneratePublishPostParamRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeneratePublishPostParamRequest.Merge(dst, src)
}
func (m *GeneratePublishPostParamRequest) XXX_Size() int {
	return xxx_messageInfo_GeneratePublishPostParamRequest.Size(m)
}
func (m *GeneratePublishPostParamRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GeneratePublishPostParamRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GeneratePublishPostParamRequest proto.InternalMessageInfo

func (m *GeneratePublishPostParamRequest) GetAttachments() []*ugc_community.Attachment {
	if m != nil {
		return m.Attachments
	}
	return nil
}

type GeneratePublishPostParamResponse struct {
	// 提前生成的帖子ID，调用发布接口带上
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// obs app
	ObsApp string `protobuf:"bytes,2,opt,name=obs_app,json=obsApp,proto3" json:"obs_app,omitempty"`
	// obs 桶
	ObsScope string `protobuf:"bytes,3,opt,name=obs_scope,json=obsScope,proto3" json:"obs_scope,omitempty"`
	// 返回的附件参数
	Attachments          []*ugc_community.Attachment `protobuf:"bytes,4,rep,name=attachments,proto3" json:"attachments,omitempty"`
	ObsToken             string                      `protobuf:"bytes,5,opt,name=obs_token,json=obsToken,proto3" json:"obs_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GeneratePublishPostParamResponse) Reset()         { *m = GeneratePublishPostParamResponse{} }
func (m *GeneratePublishPostParamResponse) String() string { return proto.CompactTextString(m) }
func (*GeneratePublishPostParamResponse) ProtoMessage()    {}
func (*GeneratePublishPostParamResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{3}
}
func (m *GeneratePublishPostParamResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GeneratePublishPostParamResponse.Unmarshal(m, b)
}
func (m *GeneratePublishPostParamResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GeneratePublishPostParamResponse.Marshal(b, m, deterministic)
}
func (dst *GeneratePublishPostParamResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeneratePublishPostParamResponse.Merge(dst, src)
}
func (m *GeneratePublishPostParamResponse) XXX_Size() int {
	return xxx_messageInfo_GeneratePublishPostParamResponse.Size(m)
}
func (m *GeneratePublishPostParamResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GeneratePublishPostParamResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GeneratePublishPostParamResponse proto.InternalMessageInfo

func (m *GeneratePublishPostParamResponse) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GeneratePublishPostParamResponse) GetObsApp() string {
	if m != nil {
		return m.ObsApp
	}
	return ""
}

func (m *GeneratePublishPostParamResponse) GetObsScope() string {
	if m != nil {
		return m.ObsScope
	}
	return ""
}

func (m *GeneratePublishPostParamResponse) GetAttachments() []*ugc_community.Attachment {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *GeneratePublishPostParamResponse) GetObsToken() string {
	if m != nil {
		return m.ObsToken
	}
	return ""
}

// ugc中的用户信息
type UserUgcCommunity struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Alias                string   `protobuf:"bytes,3,opt,name=alias,proto3" json:"alias,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Gender               uint32   `protobuf:"varint,5,opt,name=gender,proto3" json:"gender,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserUgcCommunity) Reset()         { *m = UserUgcCommunity{} }
func (m *UserUgcCommunity) String() string { return proto.CompactTextString(m) }
func (*UserUgcCommunity) ProtoMessage()    {}
func (*UserUgcCommunity) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{4}
}
func (m *UserUgcCommunity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserUgcCommunity.Unmarshal(m, b)
}
func (m *UserUgcCommunity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserUgcCommunity.Marshal(b, m, deterministic)
}
func (dst *UserUgcCommunity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserUgcCommunity.Merge(dst, src)
}
func (m *UserUgcCommunity) XXX_Size() int {
	return xxx_messageInfo_UserUgcCommunity.Size(m)
}
func (m *UserUgcCommunity) XXX_DiscardUnknown() {
	xxx_messageInfo_UserUgcCommunity.DiscardUnknown(m)
}

var xxx_messageInfo_UserUgcCommunity proto.InternalMessageInfo

func (m *UserUgcCommunity) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserUgcCommunity) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserUgcCommunity) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *UserUgcCommunity) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserUgcCommunity) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

// 帖子详情
type TopicInfo struct {
	// 话题id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 话题类型
	Type ugc_community.TopicType `protobuf:"varint,2,opt,name=type,proto3,enum=ugc_community.TopicType" json:"type,omitempty"`
	// 话题名称
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicInfo) Reset()         { *m = TopicInfo{} }
func (m *TopicInfo) String() string { return proto.CompactTextString(m) }
func (*TopicInfo) ProtoMessage()    {}
func (*TopicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{5}
}
func (m *TopicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicInfo.Unmarshal(m, b)
}
func (m *TopicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicInfo.Marshal(b, m, deterministic)
}
func (dst *TopicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicInfo.Merge(dst, src)
}
func (m *TopicInfo) XXX_Size() int {
	return xxx_messageInfo_TopicInfo.Size(m)
}
func (m *TopicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopicInfo proto.InternalMessageInfo

func (m *TopicInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *TopicInfo) GetType() ugc_community.TopicType {
	if m != nil {
		return m.Type
	}
	return ugc_community.TopicType_TOPIC_TYPE_UNSPECIFIED
}

func (m *TopicInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 查看帖子详情
type PostInfo struct {
	PostId        string                      `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PostOwner     *UserUgcCommunity           `protobuf:"bytes,2,opt,name=post_owner,json=postOwner,proto3" json:"post_owner,omitempty"`
	PostType      uint32                      `protobuf:"varint,3,opt,name=post_type,json=postType,proto3" json:"post_type,omitempty"`
	Content       string                      `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Attachments   []*ugc_community.Attachment `protobuf:"bytes,5,rep,name=attachments,proto3" json:"attachments,omitempty"`
	PostTime      int64                       `protobuf:"varint,6,opt,name=post_time,json=postTime,proto3" json:"post_time,omitempty"`
	CommentCount  uint32                      `protobuf:"varint,7,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	AttitudeCount uint32                      `protobuf:"varint,8,opt,name=attitude_count,json=attitudeCount,proto3" json:"attitude_count,omitempty"`
	Origin        uint32                      `protobuf:"varint,9,opt,name=origin,proto3" json:"origin,omitempty"`
	BizType       uint32                      `protobuf:"varint,10,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	BizData       map[string]string           `protobuf:"bytes,11,rep,name=biz_data,json=bizData,proto3" json:"biz_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	CommentItem   *CommentItem                `protobuf:"bytes,12,opt,name=comment_item,json=commentItem,proto3" json:"comment_item,omitempty"`
	IsAttitude    bool                        `protobuf:"varint,13,opt,name=is_attitude,json=isAttitude,proto3" json:"is_attitude,omitempty"`
	State         ugc_community.PostState     `protobuf:"varint,14,opt,name=state,proto3,enum=ugc_community.PostState" json:"state,omitempty"`
	// 关联话题
	TopicList            []*TopicInfo `protobuf:"bytes,15,rep,name=topic_list,json=topicList,proto3" json:"topic_list,omitempty"`
	BizBytes             []byte       `protobuf:"bytes,16,opt,name=biz_bytes,json=bizBytes,proto3" json:"biz_bytes,omitempty"`
	HadFollowed          bool         `protobuf:"varint,17,opt,name=had_followed,json=hadFollowed,proto3" json:"had_followed,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PostInfo) Reset()         { *m = PostInfo{} }
func (m *PostInfo) String() string { return proto.CompactTextString(m) }
func (*PostInfo) ProtoMessage()    {}
func (*PostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{6}
}
func (m *PostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostInfo.Unmarshal(m, b)
}
func (m *PostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostInfo.Marshal(b, m, deterministic)
}
func (dst *PostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostInfo.Merge(dst, src)
}
func (m *PostInfo) XXX_Size() int {
	return xxx_messageInfo_PostInfo.Size(m)
}
func (m *PostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PostInfo proto.InternalMessageInfo

func (m *PostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostInfo) GetPostOwner() *UserUgcCommunity {
	if m != nil {
		return m.PostOwner
	}
	return nil
}

func (m *PostInfo) GetPostType() uint32 {
	if m != nil {
		return m.PostType
	}
	return 0
}

func (m *PostInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PostInfo) GetAttachments() []*ugc_community.Attachment {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *PostInfo) GetPostTime() int64 {
	if m != nil {
		return m.PostTime
	}
	return 0
}

func (m *PostInfo) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *PostInfo) GetAttitudeCount() uint32 {
	if m != nil {
		return m.AttitudeCount
	}
	return 0
}

func (m *PostInfo) GetOrigin() uint32 {
	if m != nil {
		return m.Origin
	}
	return 0
}

func (m *PostInfo) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *PostInfo) GetBizData() map[string]string {
	if m != nil {
		return m.BizData
	}
	return nil
}

func (m *PostInfo) GetCommentItem() *CommentItem {
	if m != nil {
		return m.CommentItem
	}
	return nil
}

func (m *PostInfo) GetIsAttitude() bool {
	if m != nil {
		return m.IsAttitude
	}
	return false
}

func (m *PostInfo) GetState() ugc_community.PostState {
	if m != nil {
		return m.State
	}
	return ugc_community.PostState_POST_STATE_UNSPECIFIED
}

func (m *PostInfo) GetTopicList() []*TopicInfo {
	if m != nil {
		return m.TopicList
	}
	return nil
}

func (m *PostInfo) GetBizBytes() []byte {
	if m != nil {
		return m.BizBytes
	}
	return nil
}

func (m *PostInfo) GetHadFollowed() bool {
	if m != nil {
		return m.HadFollowed
	}
	return false
}

type PostInfo_AigcCommunityBizPost struct {
	RoleId               uint32                                        `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	RoleType             uint32                                        `protobuf:"varint,2,opt,name=role_type,json=roleType,proto3" json:"role_type,omitempty"`
	RoleName             string                                        `protobuf:"bytes,3,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	RoleAvatar           string                                        `protobuf:"bytes,4,opt,name=role_avatar,json=roleAvatar,proto3" json:"role_avatar,omitempty"`
	RoleCharacter        string                                        `protobuf:"bytes,5,opt,name=role_character,json=roleCharacter,proto3" json:"role_character,omitempty"`
	ChatRecords          []*ugc_community.AigcCommunityPost_ChatRecord `protobuf:"bytes,6,rep,name=chat_records,json=chatRecords,proto3" json:"chat_records,omitempty"`
	RoleImage            string                                        `protobuf:"bytes,7,opt,name=role_image,json=roleImage,proto3" json:"role_image,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *PostInfo_AigcCommunityBizPost) Reset()         { *m = PostInfo_AigcCommunityBizPost{} }
func (m *PostInfo_AigcCommunityBizPost) String() string { return proto.CompactTextString(m) }
func (*PostInfo_AigcCommunityBizPost) ProtoMessage()    {}
func (*PostInfo_AigcCommunityBizPost) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{6, 0}
}
func (m *PostInfo_AigcCommunityBizPost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostInfo_AigcCommunityBizPost.Unmarshal(m, b)
}
func (m *PostInfo_AigcCommunityBizPost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostInfo_AigcCommunityBizPost.Marshal(b, m, deterministic)
}
func (dst *PostInfo_AigcCommunityBizPost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostInfo_AigcCommunityBizPost.Merge(dst, src)
}
func (m *PostInfo_AigcCommunityBizPost) XXX_Size() int {
	return xxx_messageInfo_PostInfo_AigcCommunityBizPost.Size(m)
}
func (m *PostInfo_AigcCommunityBizPost) XXX_DiscardUnknown() {
	xxx_messageInfo_PostInfo_AigcCommunityBizPost.DiscardUnknown(m)
}

var xxx_messageInfo_PostInfo_AigcCommunityBizPost proto.InternalMessageInfo

func (m *PostInfo_AigcCommunityBizPost) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *PostInfo_AigcCommunityBizPost) GetRoleType() uint32 {
	if m != nil {
		return m.RoleType
	}
	return 0
}

func (m *PostInfo_AigcCommunityBizPost) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

func (m *PostInfo_AigcCommunityBizPost) GetRoleAvatar() string {
	if m != nil {
		return m.RoleAvatar
	}
	return ""
}

func (m *PostInfo_AigcCommunityBizPost) GetRoleCharacter() string {
	if m != nil {
		return m.RoleCharacter
	}
	return ""
}

func (m *PostInfo_AigcCommunityBizPost) GetChatRecords() []*ugc_community.AigcCommunityPost_ChatRecord {
	if m != nil {
		return m.ChatRecords
	}
	return nil
}

func (m *PostInfo_AigcCommunityBizPost) GetRoleImage() string {
	if m != nil {
		return m.RoleImage
	}
	return ""
}

type BaseRequest struct {
	DeviceId             string   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,4,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseRequest) Reset()         { *m = BaseRequest{} }
func (m *BaseRequest) String() string { return proto.CompactTextString(m) }
func (*BaseRequest) ProtoMessage()    {}
func (*BaseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{7}
}
func (m *BaseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseRequest.Unmarshal(m, b)
}
func (m *BaseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseRequest.Marshal(b, m, deterministic)
}
func (dst *BaseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseRequest.Merge(dst, src)
}
func (m *BaseRequest) XXX_Size() int {
	return xxx_messageInfo_BaseRequest.Size(m)
}
func (m *BaseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BaseRequest proto.InternalMessageInfo

func (m *BaseRequest) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *BaseRequest) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *BaseRequest) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *BaseRequest) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

type GetPostReq struct {
	BaseReq              *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PostId               string       `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PostSource           uint32       `protobuf:"varint,3,opt,name=post_source,json=postSource,proto3" json:"post_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPostReq) Reset()         { *m = GetPostReq{} }
func (m *GetPostReq) String() string { return proto.CompactTextString(m) }
func (*GetPostReq) ProtoMessage()    {}
func (*GetPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{8}
}
func (m *GetPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostReq.Unmarshal(m, b)
}
func (m *GetPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostReq.Marshal(b, m, deterministic)
}
func (dst *GetPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostReq.Merge(dst, src)
}
func (m *GetPostReq) XXX_Size() int {
	return xxx_messageInfo_GetPostReq.Size(m)
}
func (m *GetPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostReq proto.InternalMessageInfo

func (m *GetPostReq) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPostReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetPostReq) GetPostSource() uint32 {
	if m != nil {
		return m.PostSource
	}
	return 0
}

type GetPostResp struct {
	PostInfo             *PostInfo `protobuf:"bytes,1,opt,name=post_info,json=postInfo,proto3" json:"post_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetPostResp) Reset()         { *m = GetPostResp{} }
func (m *GetPostResp) String() string { return proto.CompactTextString(m) }
func (*GetPostResp) ProtoMessage()    {}
func (*GetPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{9}
}
func (m *GetPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostResp.Unmarshal(m, b)
}
func (m *GetPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostResp.Marshal(b, m, deterministic)
}
func (dst *GetPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostResp.Merge(dst, src)
}
func (m *GetPostResp) XXX_Size() int {
	return xxx_messageInfo_GetPostResp.Size(m)
}
func (m *GetPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostResp proto.InternalMessageInfo

func (m *GetPostResp) GetPostInfo() *PostInfo {
	if m != nil {
		return m.PostInfo
	}
	return nil
}

type Feed struct {
	// 帖子
	Post                 *PostInfo `protobuf:"bytes,1,opt,name=post,proto3" json:"post,omitempty"`
	FeedId               string    `protobuf:"bytes,2,opt,name=feed_id,json=feedId,proto3" json:"feed_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *Feed) Reset()         { *m = Feed{} }
func (m *Feed) String() string { return proto.CompactTextString(m) }
func (*Feed) ProtoMessage()    {}
func (*Feed) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{10}
}
func (m *Feed) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Feed.Unmarshal(m, b)
}
func (m *Feed) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Feed.Marshal(b, m, deterministic)
}
func (dst *Feed) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Feed.Merge(dst, src)
}
func (m *Feed) XXX_Size() int {
	return xxx_messageInfo_Feed.Size(m)
}
func (m *Feed) XXX_DiscardUnknown() {
	xxx_messageInfo_Feed.DiscardUnknown(m)
}

var xxx_messageInfo_Feed proto.InternalMessageInfo

func (m *Feed) GetPost() *PostInfo {
	if m != nil {
		return m.Post
	}
	return nil
}

func (m *Feed) GetFeedId() string {
	if m != nil {
		return m.FeedId
	}
	return ""
}

type NewFeedsLoadMore struct {
	LastFeedId           string   `protobuf:"bytes,1,opt,name=last_feed_id,json=lastFeedId,proto3" json:"last_feed_id,omitempty"`
	LastPage             uint32   `protobuf:"varint,2,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	LastCount            uint32   `protobuf:"varint,3,opt,name=last_count,json=lastCount,proto3" json:"last_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewFeedsLoadMore) Reset()         { *m = NewFeedsLoadMore{} }
func (m *NewFeedsLoadMore) String() string { return proto.CompactTextString(m) }
func (*NewFeedsLoadMore) ProtoMessage()    {}
func (*NewFeedsLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{11}
}
func (m *NewFeedsLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewFeedsLoadMore.Unmarshal(m, b)
}
func (m *NewFeedsLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewFeedsLoadMore.Marshal(b, m, deterministic)
}
func (dst *NewFeedsLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewFeedsLoadMore.Merge(dst, src)
}
func (m *NewFeedsLoadMore) XXX_Size() int {
	return xxx_messageInfo_NewFeedsLoadMore.Size(m)
}
func (m *NewFeedsLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_NewFeedsLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_NewFeedsLoadMore proto.InternalMessageInfo

func (m *NewFeedsLoadMore) GetLastFeedId() string {
	if m != nil {
		return m.LastFeedId
	}
	return ""
}

func (m *NewFeedsLoadMore) GetLastPage() uint32 {
	if m != nil {
		return m.LastPage
	}
	return 0
}

func (m *NewFeedsLoadMore) GetLastCount() uint32 {
	if m != nil {
		return m.LastCount
	}
	return 0
}

type GetNewsFeedsReq struct {
	BaseReq       *BaseRequest      `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	LoadMore      *NewFeedsLoadMore `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	GetMode       uint32            `protobuf:"varint,3,opt,name=get_mode,json=getMode,proto3" json:"get_mode,omitempty"`
	BrowsePostIds []string          `protobuf:"bytes,4,rep,name=browse_post_ids,json=browsePostIds,proto3" json:"browse_post_ids,omitempty"`
	PostSource    uint32            `protobuf:"varint,5,opt,name=post_source,json=postSource,proto3" json:"post_source,omitempty"`
	// 主题id
	SubjectId string `protobuf:"bytes,6,opt,name=subject_id,json=subjectId,proto3" json:"subject_id,omitempty"`
	// 角色id
	RoleId               uint32   `protobuf:"varint,7,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewsFeedsReq) Reset()         { *m = GetNewsFeedsReq{} }
func (m *GetNewsFeedsReq) String() string { return proto.CompactTextString(m) }
func (*GetNewsFeedsReq) ProtoMessage()    {}
func (*GetNewsFeedsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{12}
}
func (m *GetNewsFeedsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewsFeedsReq.Unmarshal(m, b)
}
func (m *GetNewsFeedsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewsFeedsReq.Marshal(b, m, deterministic)
}
func (dst *GetNewsFeedsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewsFeedsReq.Merge(dst, src)
}
func (m *GetNewsFeedsReq) XXX_Size() int {
	return xxx_messageInfo_GetNewsFeedsReq.Size(m)
}
func (m *GetNewsFeedsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewsFeedsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewsFeedsReq proto.InternalMessageInfo

func (m *GetNewsFeedsReq) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetNewsFeedsReq) GetLoadMore() *NewFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetNewsFeedsReq) GetGetMode() uint32 {
	if m != nil {
		return m.GetMode
	}
	return 0
}

func (m *GetNewsFeedsReq) GetBrowsePostIds() []string {
	if m != nil {
		return m.BrowsePostIds
	}
	return nil
}

func (m *GetNewsFeedsReq) GetPostSource() uint32 {
	if m != nil {
		return m.PostSource
	}
	return 0
}

func (m *GetNewsFeedsReq) GetSubjectId() string {
	if m != nil {
		return m.SubjectId
	}
	return ""
}

func (m *GetNewsFeedsReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetNewsFeedsResp struct {
	Feeds                []*Feed           `protobuf:"bytes,1,rep,name=feeds,proto3" json:"feeds,omitempty"`
	LoadMore             *NewFeedsLoadMore `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Footprint            string            `protobuf:"bytes,3,opt,name=footprint,proto3" json:"footprint,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetNewsFeedsResp) Reset()         { *m = GetNewsFeedsResp{} }
func (m *GetNewsFeedsResp) String() string { return proto.CompactTextString(m) }
func (*GetNewsFeedsResp) ProtoMessage()    {}
func (*GetNewsFeedsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{13}
}
func (m *GetNewsFeedsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewsFeedsResp.Unmarshal(m, b)
}
func (m *GetNewsFeedsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewsFeedsResp.Marshal(b, m, deterministic)
}
func (dst *GetNewsFeedsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewsFeedsResp.Merge(dst, src)
}
func (m *GetNewsFeedsResp) XXX_Size() int {
	return xxx_messageInfo_GetNewsFeedsResp.Size(m)
}
func (m *GetNewsFeedsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewsFeedsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewsFeedsResp proto.InternalMessageInfo

func (m *GetNewsFeedsResp) GetFeeds() []*Feed {
	if m != nil {
		return m.Feeds
	}
	return nil
}

func (m *GetNewsFeedsResp) GetLoadMore() *NewFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetNewsFeedsResp) GetFootprint() string {
	if m != nil {
		return m.Footprint
	}
	return ""
}

type CommentSendRequest struct {
	// 公参
	BaseReq *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 帖子id
	PostId string `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	// 首评论id，如果是回复帖子，这个字段为空
	RootParentId string `protobuf:"bytes,3,opt,name=root_parent_id,json=rootParentId,proto3" json:"root_parent_id,omitempty"`
	// 回复评论id，如果是第一条回复帖子或帖子的，这个字段为空
	ParentId string `protobuf:"bytes,4,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 回复内容
	Content string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	// 评论实体类型
	Type ugc_community.CommentEntityType `protobuf:"varint,6,opt,name=type,proto3,enum=ugc_community.CommentEntityType" json:"type,omitempty"`
	// 角色id，type=COMMENT_ENTITY_TYPE_ROLE时用到
	RoleId uint32 `protobuf:"varint,7,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 评论来源
	Origin               ugc_community.CommentOrigin `protobuf:"varint,8,opt,name=origin,proto3,enum=ugc_community.CommentOrigin" json:"origin,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *CommentSendRequest) Reset()         { *m = CommentSendRequest{} }
func (m *CommentSendRequest) String() string { return proto.CompactTextString(m) }
func (*CommentSendRequest) ProtoMessage()    {}
func (*CommentSendRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{14}
}
func (m *CommentSendRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentSendRequest.Unmarshal(m, b)
}
func (m *CommentSendRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentSendRequest.Marshal(b, m, deterministic)
}
func (dst *CommentSendRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentSendRequest.Merge(dst, src)
}
func (m *CommentSendRequest) XXX_Size() int {
	return xxx_messageInfo_CommentSendRequest.Size(m)
}
func (m *CommentSendRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentSendRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommentSendRequest proto.InternalMessageInfo

func (m *CommentSendRequest) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CommentSendRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *CommentSendRequest) GetRootParentId() string {
	if m != nil {
		return m.RootParentId
	}
	return ""
}

func (m *CommentSendRequest) GetParentId() string {
	if m != nil {
		return m.ParentId
	}
	return ""
}

func (m *CommentSendRequest) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CommentSendRequest) GetType() ugc_community.CommentEntityType {
	if m != nil {
		return m.Type
	}
	return ugc_community.CommentEntityType_COMMENT_ENTITY_TYPE_USER
}

func (m *CommentSendRequest) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *CommentSendRequest) GetOrigin() ugc_community.CommentOrigin {
	if m != nil {
		return m.Origin
	}
	return ugc_community.CommentOrigin_COMMENT_ORIGIN_NORMAL
}

type CommentSendResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentSendResponse) Reset()         { *m = CommentSendResponse{} }
func (m *CommentSendResponse) String() string { return proto.CompactTextString(m) }
func (*CommentSendResponse) ProtoMessage()    {}
func (*CommentSendResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{15}
}
func (m *CommentSendResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentSendResponse.Unmarshal(m, b)
}
func (m *CommentSendResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentSendResponse.Marshal(b, m, deterministic)
}
func (dst *CommentSendResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentSendResponse.Merge(dst, src)
}
func (m *CommentSendResponse) XXX_Size() int {
	return xxx_messageInfo_CommentSendResponse.Size(m)
}
func (m *CommentSendResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentSendResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommentSendResponse proto.InternalMessageInfo

type CommentFetchRequest struct {
	BaseReq *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 帖子id
	PostId string `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	// 上一页最后的首次评id，分页使用
	LastCommentId        string   `protobuf:"bytes,3,opt,name=last_comment_id,json=lastCommentId,proto3" json:"last_comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentFetchRequest) Reset()         { *m = CommentFetchRequest{} }
func (m *CommentFetchRequest) String() string { return proto.CompactTextString(m) }
func (*CommentFetchRequest) ProtoMessage()    {}
func (*CommentFetchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{16}
}
func (m *CommentFetchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentFetchRequest.Unmarshal(m, b)
}
func (m *CommentFetchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentFetchRequest.Marshal(b, m, deterministic)
}
func (dst *CommentFetchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentFetchRequest.Merge(dst, src)
}
func (m *CommentFetchRequest) XXX_Size() int {
	return xxx_messageInfo_CommentFetchRequest.Size(m)
}
func (m *CommentFetchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentFetchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommentFetchRequest proto.InternalMessageInfo

func (m *CommentFetchRequest) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CommentFetchRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *CommentFetchRequest) GetLastCommentId() string {
	if m != nil {
		return m.LastCommentId
	}
	return ""
}

type CommentUser struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  uint32   `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentUser) Reset()         { *m = CommentUser{} }
func (m *CommentUser) String() string { return proto.CompactTextString(m) }
func (*CommentUser) ProtoMessage()    {}
func (*CommentUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{17}
}
func (m *CommentUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentUser.Unmarshal(m, b)
}
func (m *CommentUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentUser.Marshal(b, m, deterministic)
}
func (dst *CommentUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentUser.Merge(dst, src)
}
func (m *CommentUser) XXX_Size() int {
	return xxx_messageInfo_CommentUser.Size(m)
}
func (m *CommentUser) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentUser.DiscardUnknown(m)
}

var xxx_messageInfo_CommentUser proto.InternalMessageInfo

func (m *CommentUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CommentUser) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *CommentUser) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *CommentUser) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type CommentRole struct {
	RoleId               uint32   `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	RoleAvatar           string   `protobuf:"bytes,2,opt,name=role_avatar,json=roleAvatar,proto3" json:"role_avatar,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  uint32   `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentRole) Reset()         { *m = CommentRole{} }
func (m *CommentRole) String() string { return proto.CompactTextString(m) }
func (*CommentRole) ProtoMessage()    {}
func (*CommentRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{18}
}
func (m *CommentRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentRole.Unmarshal(m, b)
}
func (m *CommentRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentRole.Marshal(b, m, deterministic)
}
func (dst *CommentRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentRole.Merge(dst, src)
}
func (m *CommentRole) XXX_Size() int {
	return xxx_messageInfo_CommentRole.Size(m)
}
func (m *CommentRole) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentRole.DiscardUnknown(m)
}

var xxx_messageInfo_CommentRole proto.InternalMessageInfo

func (m *CommentRole) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *CommentRole) GetRoleAvatar() string {
	if m != nil {
		return m.RoleAvatar
	}
	return ""
}

func (m *CommentRole) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *CommentRole) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type CommentEntity struct {
	Type                 ugc_community.CommentEntityType `protobuf:"varint,1,opt,name=type,proto3,enum=ugc_community.CommentEntityType" json:"type,omitempty"`
	User                 *CommentUser                    `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Role                 *CommentRole                    `protobuf:"bytes,3,opt,name=role,proto3" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *CommentEntity) Reset()         { *m = CommentEntity{} }
func (m *CommentEntity) String() string { return proto.CompactTextString(m) }
func (*CommentEntity) ProtoMessage()    {}
func (*CommentEntity) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{19}
}
func (m *CommentEntity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentEntity.Unmarshal(m, b)
}
func (m *CommentEntity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentEntity.Marshal(b, m, deterministic)
}
func (dst *CommentEntity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentEntity.Merge(dst, src)
}
func (m *CommentEntity) XXX_Size() int {
	return xxx_messageInfo_CommentEntity.Size(m)
}
func (m *CommentEntity) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentEntity.DiscardUnknown(m)
}

var xxx_messageInfo_CommentEntity proto.InternalMessageInfo

func (m *CommentEntity) GetType() ugc_community.CommentEntityType {
	if m != nil {
		return m.Type
	}
	return ugc_community.CommentEntityType_COMMENT_ENTITY_TYPE_USER
}

func (m *CommentEntity) GetUser() *CommentUser {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *CommentEntity) GetRole() *CommentRole {
	if m != nil {
		return m.Role
	}
	return nil
}

type CommentItem struct {
	// 评论id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Deprecated  评论人信息
	SendUser *CommentUser `protobuf:"bytes,2,opt,name=send_user,json=sendUser,proto3" json:"send_user,omitempty"`
	// 评论内容
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	// 点赞数
	Likes uint32 `protobuf:"varint,4,opt,name=likes,proto3" json:"likes,omitempty"`
	// 评论时间，ms
	CreateAt int64 `protobuf:"varint,5,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	// 子评论，可能子评论很多，当前不做三级页面和分页处理，简单实现全部一次返回
	SubComments []*CommentItem_SecondCommentInfo `protobuf:"bytes,6,rep,name=sub_comments,json=subComments,proto3" json:"sub_comments,omitempty"`
	IsAttitude  bool                             `protobuf:"varint,7,opt,name=is_attitude,json=isAttitude,proto3" json:"is_attitude,omitempty"`
	// 回复数
	ReplyCount uint32 `protobuf:"varint,8,opt,name=reply_count,json=replyCount,proto3" json:"reply_count,omitempty"`
	// 发布评论对象信息
	SendEntity           *CommentEntity `protobuf:"bytes,9,opt,name=send_entity,json=sendEntity,proto3" json:"send_entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CommentItem) Reset()         { *m = CommentItem{} }
func (m *CommentItem) String() string { return proto.CompactTextString(m) }
func (*CommentItem) ProtoMessage()    {}
func (*CommentItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{20}
}
func (m *CommentItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentItem.Unmarshal(m, b)
}
func (m *CommentItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentItem.Marshal(b, m, deterministic)
}
func (dst *CommentItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentItem.Merge(dst, src)
}
func (m *CommentItem) XXX_Size() int {
	return xxx_messageInfo_CommentItem.Size(m)
}
func (m *CommentItem) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentItem.DiscardUnknown(m)
}

var xxx_messageInfo_CommentItem proto.InternalMessageInfo

func (m *CommentItem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CommentItem) GetSendUser() *CommentUser {
	if m != nil {
		return m.SendUser
	}
	return nil
}

func (m *CommentItem) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CommentItem) GetLikes() uint32 {
	if m != nil {
		return m.Likes
	}
	return 0
}

func (m *CommentItem) GetCreateAt() int64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *CommentItem) GetSubComments() []*CommentItem_SecondCommentInfo {
	if m != nil {
		return m.SubComments
	}
	return nil
}

func (m *CommentItem) GetIsAttitude() bool {
	if m != nil {
		return m.IsAttitude
	}
	return false
}

func (m *CommentItem) GetReplyCount() uint32 {
	if m != nil {
		return m.ReplyCount
	}
	return 0
}

func (m *CommentItem) GetSendEntity() *CommentEntity {
	if m != nil {
		return m.SendEntity
	}
	return nil
}

// 二级评论信息
type CommentItem_SecondCommentInfo struct {
	// 评论id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Deprecated  评论人信息
	SendUser *CommentUser `protobuf:"bytes,2,opt,name=send_user,json=sendUser,proto3" json:"send_user,omitempty"`
	// 评论内容
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	// 评论时间，ms
	CreateAt int64 `protobuf:"varint,4,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	// Deprecated  回复评论的用户，评论的评论的回复用户
	ReplyUser *CommentUser `protobuf:"bytes,5,opt,name=reply_user,json=replyUser,proto3" json:"reply_user,omitempty"`
	// 发布评论对象信息
	SendEntity *CommentEntity `protobuf:"bytes,6,opt,name=send_entity,json=sendEntity,proto3" json:"send_entity,omitempty"`
	// 回复评论的对象，评论的评论的回复对象
	ReplyEntity          *CommentEntity `protobuf:"bytes,7,opt,name=reply_entity,json=replyEntity,proto3" json:"reply_entity,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CommentItem_SecondCommentInfo) Reset()         { *m = CommentItem_SecondCommentInfo{} }
func (m *CommentItem_SecondCommentInfo) String() string { return proto.CompactTextString(m) }
func (*CommentItem_SecondCommentInfo) ProtoMessage()    {}
func (*CommentItem_SecondCommentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{20, 0}
}
func (m *CommentItem_SecondCommentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentItem_SecondCommentInfo.Unmarshal(m, b)
}
func (m *CommentItem_SecondCommentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentItem_SecondCommentInfo.Marshal(b, m, deterministic)
}
func (dst *CommentItem_SecondCommentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentItem_SecondCommentInfo.Merge(dst, src)
}
func (m *CommentItem_SecondCommentInfo) XXX_Size() int {
	return xxx_messageInfo_CommentItem_SecondCommentInfo.Size(m)
}
func (m *CommentItem_SecondCommentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentItem_SecondCommentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CommentItem_SecondCommentInfo proto.InternalMessageInfo

func (m *CommentItem_SecondCommentInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CommentItem_SecondCommentInfo) GetSendUser() *CommentUser {
	if m != nil {
		return m.SendUser
	}
	return nil
}

func (m *CommentItem_SecondCommentInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CommentItem_SecondCommentInfo) GetCreateAt() int64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *CommentItem_SecondCommentInfo) GetReplyUser() *CommentUser {
	if m != nil {
		return m.ReplyUser
	}
	return nil
}

func (m *CommentItem_SecondCommentInfo) GetSendEntity() *CommentEntity {
	if m != nil {
		return m.SendEntity
	}
	return nil
}

func (m *CommentItem_SecondCommentInfo) GetReplyEntity() *CommentEntity {
	if m != nil {
		return m.ReplyEntity
	}
	return nil
}

type CommentFetchResponse struct {
	Comments             []*CommentItem `protobuf:"bytes,1,rep,name=comments,proto3" json:"comments,omitempty"`
	IsLoadFinish         bool           `protobuf:"varint,2,opt,name=is_load_finish,json=isLoadFinish,proto3" json:"is_load_finish,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CommentFetchResponse) Reset()         { *m = CommentFetchResponse{} }
func (m *CommentFetchResponse) String() string { return proto.CompactTextString(m) }
func (*CommentFetchResponse) ProtoMessage()    {}
func (*CommentFetchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{21}
}
func (m *CommentFetchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentFetchResponse.Unmarshal(m, b)
}
func (m *CommentFetchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentFetchResponse.Marshal(b, m, deterministic)
}
func (dst *CommentFetchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentFetchResponse.Merge(dst, src)
}
func (m *CommentFetchResponse) XXX_Size() int {
	return xxx_messageInfo_CommentFetchResponse.Size(m)
}
func (m *CommentFetchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentFetchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommentFetchResponse proto.InternalMessageInfo

func (m *CommentFetchResponse) GetComments() []*CommentItem {
	if m != nil {
		return m.Comments
	}
	return nil
}

func (m *CommentFetchResponse) GetIsLoadFinish() bool {
	if m != nil {
		return m.IsLoadFinish
	}
	return false
}

type CommentDeleteRequest struct {
	BaseReq              *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	CommentId            string       `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CommentDeleteRequest) Reset()         { *m = CommentDeleteRequest{} }
func (m *CommentDeleteRequest) String() string { return proto.CompactTextString(m) }
func (*CommentDeleteRequest) ProtoMessage()    {}
func (*CommentDeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{22}
}
func (m *CommentDeleteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentDeleteRequest.Unmarshal(m, b)
}
func (m *CommentDeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentDeleteRequest.Marshal(b, m, deterministic)
}
func (dst *CommentDeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentDeleteRequest.Merge(dst, src)
}
func (m *CommentDeleteRequest) XXX_Size() int {
	return xxx_messageInfo_CommentDeleteRequest.Size(m)
}
func (m *CommentDeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentDeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommentDeleteRequest proto.InternalMessageInfo

func (m *CommentDeleteRequest) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CommentDeleteRequest) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

type CommentDeleteResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentDeleteResponse) Reset()         { *m = CommentDeleteResponse{} }
func (m *CommentDeleteResponse) String() string { return proto.CompactTextString(m) }
func (*CommentDeleteResponse) ProtoMessage()    {}
func (*CommentDeleteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_middle_8a48508cf44421c8, []int{23}
}
func (m *CommentDeleteResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentDeleteResponse.Unmarshal(m, b)
}
func (m *CommentDeleteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentDeleteResponse.Marshal(b, m, deterministic)
}
func (dst *CommentDeleteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentDeleteResponse.Merge(dst, src)
}
func (m *CommentDeleteResponse) XXX_Size() int {
	return xxx_messageInfo_CommentDeleteResponse.Size(m)
}
func (m *CommentDeleteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentDeleteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommentDeleteResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*PublishPostRequest)(nil), "ugc_community_middle.PublishPostRequest")
	proto.RegisterType((*PublishPostRequest_Post)(nil), "ugc_community_middle.PublishPostRequest.Post")
	proto.RegisterType((*PublishPostResponse)(nil), "ugc_community_middle.PublishPostResponse")
	proto.RegisterType((*GeneratePublishPostParamRequest)(nil), "ugc_community_middle.GeneratePublishPostParamRequest")
	proto.RegisterType((*GeneratePublishPostParamResponse)(nil), "ugc_community_middle.GeneratePublishPostParamResponse")
	proto.RegisterType((*UserUgcCommunity)(nil), "ugc_community_middle.UserUgcCommunity")
	proto.RegisterType((*TopicInfo)(nil), "ugc_community_middle.TopicInfo")
	proto.RegisterType((*PostInfo)(nil), "ugc_community_middle.PostInfo")
	proto.RegisterMapType((map[string]string)(nil), "ugc_community_middle.PostInfo.BizDataEntry")
	proto.RegisterType((*PostInfo_AigcCommunityBizPost)(nil), "ugc_community_middle.PostInfo.AigcCommunityBizPost")
	proto.RegisterType((*BaseRequest)(nil), "ugc_community_middle.BaseRequest")
	proto.RegisterType((*GetPostReq)(nil), "ugc_community_middle.GetPostReq")
	proto.RegisterType((*GetPostResp)(nil), "ugc_community_middle.GetPostResp")
	proto.RegisterType((*Feed)(nil), "ugc_community_middle.Feed")
	proto.RegisterType((*NewFeedsLoadMore)(nil), "ugc_community_middle.NewFeedsLoadMore")
	proto.RegisterType((*GetNewsFeedsReq)(nil), "ugc_community_middle.GetNewsFeedsReq")
	proto.RegisterType((*GetNewsFeedsResp)(nil), "ugc_community_middle.GetNewsFeedsResp")
	proto.RegisterType((*CommentSendRequest)(nil), "ugc_community_middle.CommentSendRequest")
	proto.RegisterType((*CommentSendResponse)(nil), "ugc_community_middle.CommentSendResponse")
	proto.RegisterType((*CommentFetchRequest)(nil), "ugc_community_middle.CommentFetchRequest")
	proto.RegisterType((*CommentUser)(nil), "ugc_community_middle.CommentUser")
	proto.RegisterType((*CommentRole)(nil), "ugc_community_middle.CommentRole")
	proto.RegisterType((*CommentEntity)(nil), "ugc_community_middle.CommentEntity")
	proto.RegisterType((*CommentItem)(nil), "ugc_community_middle.CommentItem")
	proto.RegisterType((*CommentItem_SecondCommentInfo)(nil), "ugc_community_middle.CommentItem.SecondCommentInfo")
	proto.RegisterType((*CommentFetchResponse)(nil), "ugc_community_middle.CommentFetchResponse")
	proto.RegisterType((*CommentDeleteRequest)(nil), "ugc_community_middle.CommentDeleteRequest")
	proto.RegisterType((*CommentDeleteResponse)(nil), "ugc_community_middle.CommentDeleteResponse")
	proto.RegisterEnum("ugc_community_middle.PostSource", PostSource_name, PostSource_value)
	proto.RegisterEnum("ugc_community_middle.FeedMode", FeedMode_name, FeedMode_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UgcCommunityMiddleClient is the client API for UgcCommunityMiddle service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UgcCommunityMiddleClient interface {
	// 发布帖子
	PublishPost(ctx context.Context, in *PublishPostRequest, opts ...grpc.CallOption) (*PublishPostResponse, error)
	// 生成发布帖子参数
	GeneratePublishPostParam(ctx context.Context, in *GeneratePublishPostParamRequest, opts ...grpc.CallOption) (*GeneratePublishPostParamResponse, error)
	GetNewsFeeds(ctx context.Context, in *GetNewsFeedsReq, opts ...grpc.CallOption) (*GetNewsFeedsResp, error)
	GetPost(ctx context.Context, in *GetPostReq, opts ...grpc.CallOption) (*GetPostResp, error)
	CommentSend(ctx context.Context, in *CommentSendRequest, opts ...grpc.CallOption) (*CommentSendResponse, error)
	CommentFetch(ctx context.Context, in *CommentFetchRequest, opts ...grpc.CallOption) (*CommentFetchResponse, error)
	CommentDelete(ctx context.Context, in *CommentDeleteRequest, opts ...grpc.CallOption) (*CommentDeleteResponse, error)
}

type ugcCommunityMiddleClient struct {
	cc *grpc.ClientConn
}

func NewUgcCommunityMiddleClient(cc *grpc.ClientConn) UgcCommunityMiddleClient {
	return &ugcCommunityMiddleClient{cc}
}

func (c *ugcCommunityMiddleClient) PublishPost(ctx context.Context, in *PublishPostRequest, opts ...grpc.CallOption) (*PublishPostResponse, error) {
	out := new(PublishPostResponse)
	err := c.cc.Invoke(ctx, "/ugc_community_middle.UgcCommunityMiddle/PublishPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityMiddleClient) GeneratePublishPostParam(ctx context.Context, in *GeneratePublishPostParamRequest, opts ...grpc.CallOption) (*GeneratePublishPostParamResponse, error) {
	out := new(GeneratePublishPostParamResponse)
	err := c.cc.Invoke(ctx, "/ugc_community_middle.UgcCommunityMiddle/GeneratePublishPostParam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityMiddleClient) GetNewsFeeds(ctx context.Context, in *GetNewsFeedsReq, opts ...grpc.CallOption) (*GetNewsFeedsResp, error) {
	out := new(GetNewsFeedsResp)
	err := c.cc.Invoke(ctx, "/ugc_community_middle.UgcCommunityMiddle/GetNewsFeeds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityMiddleClient) GetPost(ctx context.Context, in *GetPostReq, opts ...grpc.CallOption) (*GetPostResp, error) {
	out := new(GetPostResp)
	err := c.cc.Invoke(ctx, "/ugc_community_middle.UgcCommunityMiddle/GetPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityMiddleClient) CommentSend(ctx context.Context, in *CommentSendRequest, opts ...grpc.CallOption) (*CommentSendResponse, error) {
	out := new(CommentSendResponse)
	err := c.cc.Invoke(ctx, "/ugc_community_middle.UgcCommunityMiddle/CommentSend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityMiddleClient) CommentFetch(ctx context.Context, in *CommentFetchRequest, opts ...grpc.CallOption) (*CommentFetchResponse, error) {
	out := new(CommentFetchResponse)
	err := c.cc.Invoke(ctx, "/ugc_community_middle.UgcCommunityMiddle/CommentFetch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityMiddleClient) CommentDelete(ctx context.Context, in *CommentDeleteRequest, opts ...grpc.CallOption) (*CommentDeleteResponse, error) {
	out := new(CommentDeleteResponse)
	err := c.cc.Invoke(ctx, "/ugc_community_middle.UgcCommunityMiddle/CommentDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UgcCommunityMiddleServer is the server API for UgcCommunityMiddle service.
type UgcCommunityMiddleServer interface {
	// 发布帖子
	PublishPost(context.Context, *PublishPostRequest) (*PublishPostResponse, error)
	// 生成发布帖子参数
	GeneratePublishPostParam(context.Context, *GeneratePublishPostParamRequest) (*GeneratePublishPostParamResponse, error)
	GetNewsFeeds(context.Context, *GetNewsFeedsReq) (*GetNewsFeedsResp, error)
	GetPost(context.Context, *GetPostReq) (*GetPostResp, error)
	CommentSend(context.Context, *CommentSendRequest) (*CommentSendResponse, error)
	CommentFetch(context.Context, *CommentFetchRequest) (*CommentFetchResponse, error)
	CommentDelete(context.Context, *CommentDeleteRequest) (*CommentDeleteResponse, error)
}

func RegisterUgcCommunityMiddleServer(s *grpc.Server, srv UgcCommunityMiddleServer) {
	s.RegisterService(&_UgcCommunityMiddle_serviceDesc, srv)
}

func _UgcCommunityMiddle_PublishPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublishPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityMiddleServer).PublishPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_middle.UgcCommunityMiddle/PublishPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityMiddleServer).PublishPost(ctx, req.(*PublishPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunityMiddle_GeneratePublishPostParam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GeneratePublishPostParamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityMiddleServer).GeneratePublishPostParam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_middle.UgcCommunityMiddle/GeneratePublishPostParam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityMiddleServer).GeneratePublishPostParam(ctx, req.(*GeneratePublishPostParamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunityMiddle_GetNewsFeeds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewsFeedsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityMiddleServer).GetNewsFeeds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_middle.UgcCommunityMiddle/GetNewsFeeds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityMiddleServer).GetNewsFeeds(ctx, req.(*GetNewsFeedsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunityMiddle_GetPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityMiddleServer).GetPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_middle.UgcCommunityMiddle/GetPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityMiddleServer).GetPost(ctx, req.(*GetPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunityMiddle_CommentSend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommentSendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityMiddleServer).CommentSend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_middle.UgcCommunityMiddle/CommentSend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityMiddleServer).CommentSend(ctx, req.(*CommentSendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunityMiddle_CommentFetch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommentFetchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityMiddleServer).CommentFetch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_middle.UgcCommunityMiddle/CommentFetch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityMiddleServer).CommentFetch(ctx, req.(*CommentFetchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunityMiddle_CommentDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommentDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityMiddleServer).CommentDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_middle.UgcCommunityMiddle/CommentDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityMiddleServer).CommentDelete(ctx, req.(*CommentDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _UgcCommunityMiddle_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc_community_middle.UgcCommunityMiddle",
	HandlerType: (*UgcCommunityMiddleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PublishPost",
			Handler:    _UgcCommunityMiddle_PublishPost_Handler,
		},
		{
			MethodName: "GeneratePublishPostParam",
			Handler:    _UgcCommunityMiddle_GeneratePublishPostParam_Handler,
		},
		{
			MethodName: "GetNewsFeeds",
			Handler:    _UgcCommunityMiddle_GetNewsFeeds_Handler,
		},
		{
			MethodName: "GetPost",
			Handler:    _UgcCommunityMiddle_GetPost_Handler,
		},
		{
			MethodName: "CommentSend",
			Handler:    _UgcCommunityMiddle_CommentSend_Handler,
		},
		{
			MethodName: "CommentFetch",
			Handler:    _UgcCommunityMiddle_CommentFetch_Handler,
		},
		{
			MethodName: "CommentDelete",
			Handler:    _UgcCommunityMiddle_CommentDelete_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/ugc-community-middle/ugc-community-middle.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/ugc-community-middle/ugc-community-middle.proto", fileDescriptor_ugc_community_middle_8a48508cf44421c8)
}

var fileDescriptor_ugc_community_middle_8a48508cf44421c8 = []byte{
	// 2095 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x59, 0xcf, 0x73, 0x1b, 0x49,
	0xf5, 0xff, 0x8e, 0x64, 0x49, 0x33, 0x4f, 0x92, 0xa3, 0x74, 0x92, 0xb5, 0xa2, 0xfd, 0x11, 0x65,
	0xf2, 0x4d, 0x30, 0x09, 0x71, 0x16, 0xef, 0x66, 0x6b, 0x6b, 0x17, 0x52, 0x38, 0xb6, 0x1c, 0x44,
	0xad, 0x7f, 0x54, 0xcb, 0xd9, 0xa2, 0xa8, 0x5a, 0xa6, 0x46, 0x33, 0x6d, 0x69, 0xb0, 0x34, 0x33,
	0x99, 0x6e, 0xc5, 0x38, 0x5c, 0xa1, 0x0a, 0x4e, 0x5c, 0xf8, 0x03, 0xb8, 0x71, 0xa5, 0x8a, 0x0b,
	0x67, 0xee, 0x1c, 0xb8, 0xf1, 0x27, 0x50, 0xfc, 0x03, 0x5c, 0xa9, 0x7e, 0xdd, 0x33, 0x1e, 0xc9,
	0x92, 0x23, 0xc3, 0x2e, 0xb7, 0xee, 0xd7, 0xef, 0xf5, 0xfb, 0xfd, 0xe9, 0xa7, 0x11, 0x3c, 0x13,
	0xe2, 0xc9, 0xab, 0x49, 0xe0, 0x9d, 0xf0, 0x60, 0xf4, 0x9a, 0x25, 0x4f, 0x26, 0x03, 0xef, 0xb1,
	0x17, 0x8d, 0xc7, 0x93, 0x30, 0x10, 0x67, 0x8f, 0xc7, 0x81, 0xef, 0x8f, 0xd8, 0x5c, 0xe2, 0x46,
	0x9c, 0x44, 0x22, 0x22, 0x37, 0x27, 0x03, 0xcf, 0xc9, 0xce, 0x1c, 0x75, 0xd6, 0xfa, 0xf0, 0xb2,
	0x5b, 0xa7, 0x77, 0xea, 0x1e, 0xfb, 0x6f, 0x45, 0x20, 0x87, 0x93, 0xfe, 0x28, 0xe0, 0xc3, 0xc3,
	0x88, 0x0b, 0xca, 0x5e, 0x4d, 0x18, 0x17, 0x64, 0x0b, 0x56, 0xe2, 0x88, 0x8b, 0x66, 0xa1, 0x6d,
	0xac, 0x57, 0x37, 0x1f, 0x6f, 0xcc, 0xd3, 0xb6, 0x71, 0x51, 0x6e, 0x03, 0xd7, 0x28, 0x4a, 0xde,
	0x07, 0x10, 0x2e, 0x3f, 0x71, 0x44, 0x74, 0xc2, 0xc2, 0x66, 0xb1, 0x6d, 0xac, 0x5b, 0xd4, 0x92,
	0x94, 0x23, 0x49, 0x68, 0xfd, 0xbd, 0x00, 0x2b, 0x92, 0x9b, 0xac, 0x42, 0x21, 0xf0, 0x9b, 0x06,
	0x9e, 0x17, 0x02, 0x9f, 0x3c, 0x82, 0x15, 0x71, 0x16, 0x33, 0x54, 0xbd, 0xba, 0xb9, 0x36, 0xad,
	0x1a, 0x15, 0x1c, 0x9d, 0xc5, 0x8c, 0x22, 0x13, 0xd9, 0x80, 0x12, 0x17, 0xae, 0x60, 0x78, 0xff,
	0xea, 0x66, 0x73, 0x0e, 0x77, 0x4f, 0x9e, 0x53, 0xc5, 0x46, 0xbe, 0x0b, 0xe5, 0x28, 0x09, 0x06,
	0x41, 0xd8, 0x5c, 0x41, 0x81, 0xdb, 0x73, 0x04, 0x0e, 0x90, 0x81, 0x6a, 0x46, 0xd2, 0x84, 0x8a,
	0x17, 0x85, 0x82, 0x85, 0xa2, 0x59, 0x42, 0x23, 0xd3, 0x2d, 0xf9, 0x1c, 0xaa, 0xae, 0x10, 0xae,
	0x37, 0x1c, 0xb3, 0x50, 0xf0, 0x66, 0xb9, 0x5d, 0x5c, 0xaf, 0x5e, 0xb8, 0x71, 0x2b, 0xe3, 0xa0,
	0x79, 0x6e, 0xf2, 0x14, 0xcc, 0x7e, 0xf0, 0xc6, 0xf1, 0x5d, 0xe1, 0x36, 0x4d, 0x8c, 0x72, 0x6b,
	0x8e, 0x2d, 0xcf, 0x83, 0x37, 0x3b, 0xae, 0x70, 0x69, 0xa5, 0xaf, 0x16, 0xc4, 0x86, 0xba, 0x88,
	0xe2, 0xc0, 0x73, 0x02, 0xdf, 0x19, 0x05, 0x5c, 0x34, 0xad, 0x76, 0x71, 0xdd, 0xa2, 0x55, 0x24,
	0x76, 0xfd, 0x2f, 0x02, 0x2e, 0xec, 0xfb, 0x70, 0x63, 0x2a, 0x35, 0x3c, 0x8e, 0x42, 0xce, 0x66,
	0x03, 0x6d, 0xff, 0x14, 0xee, 0xbc, 0x60, 0x21, 0x4b, 0x5c, 0xc1, 0x72, 0xec, 0x87, 0x6e, 0xe2,
	0x8e, 0xd3, 0x32, 0x98, 0xf1, 0xb0, 0x70, 0x15, 0x0f, 0xed, 0xbf, 0x18, 0xd0, 0x5e, 0xac, 0x60,
	0xbe, 0x51, 0x64, 0x0d, 0x2a, 0x51, 0x9f, 0x3b, 0x6e, 0x1c, 0x63, 0x01, 0x58, 0xb4, 0x1c, 0xf5,
	0xf9, 0x56, 0x1c, 0x93, 0x77, 0xc1, 0x92, 0x07, 0xdc, 0x8b, 0x62, 0xa6, 0xab, 0xc9, 0x8c, 0xfa,
	0xbc, 0x27, 0xf7, 0xb3, 0x76, 0xae, 0x5c, 0x29, 0x13, 0xfa, 0x66, 0x55, 0xa7, 0xa5, 0xec, 0x66,
	0x2c, 0x53, 0xfb, 0xd7, 0x06, 0x34, 0x5e, 0x72, 0x96, 0xbc, 0x1c, 0x78, 0xdb, 0xe9, 0x4d, 0xa4,
	0x01, 0xc5, 0x89, 0xb6, 0xba, 0x4e, 0xe5, 0x52, 0x16, 0x89, 0xeb, 0x79, 0xd1, 0x24, 0x14, 0xda,
	0xec, 0x74, 0x4b, 0x6e, 0x42, 0xc9, 0x1d, 0x05, 0x2e, 0xd7, 0x36, 0xab, 0x0d, 0x69, 0x81, 0x19,
	0x06, 0xde, 0x49, 0xe8, 0x8e, 0x19, 0x56, 0xa2, 0x45, 0xb3, 0x3d, 0x79, 0x07, 0xca, 0x03, 0x16,
	0xfa, 0x2c, 0x41, 0x63, 0xea, 0x54, 0xef, 0xec, 0xaf, 0xc0, 0x3a, 0xc2, 0x2c, 0x87, 0xc7, 0xd1,
	0x85, 0xb8, 0x7d, 0x67, 0xaa, 0x6b, 0x66, 0xfb, 0x00, 0xe5, 0x72, 0x6d, 0x43, 0x60, 0x05, 0x55,
	0x2b, 0x9b, 0x70, 0x6d, 0xff, 0xd9, 0x04, 0x53, 0xe6, 0x07, 0xaf, 0x5f, 0x83, 0x8a, 0x6c, 0x62,
	0x27, 0xd3, 0x51, 0x96, 0xdb, 0xae, 0x4f, 0x3a, 0x00, 0x78, 0x10, 0x9d, 0x86, 0x2c, 0xd1, 0xf0,
	0xf0, 0x60, 0x3e, 0x3c, 0xcc, 0x86, 0x8d, 0x5a, 0x52, 0xf2, 0x40, 0x0a, 0xca, 0x98, 0xe3, 0x35,
	0x68, 0x73, 0x11, 0xdd, 0x34, 0x63, 0xdd, 0xda, 0xf9, 0x8e, 0x5b, 0xb9, 0xb4, 0xe3, 0x4a, 0x57,
	0xcd, 0xb3, 0xd2, 0x19, 0x8c, 0x59, 0xb3, 0xdc, 0x36, 0xd6, 0x8b, 0x5a, 0x67, 0x30, 0x66, 0xe4,
	0x1e, 0xd4, 0xe5, 0x0d, 0x2c, 0x14, 0x8e, 0x4a, 0x63, 0x05, 0x8d, 0xaa, 0x69, 0xe2, 0x36, 0xe6,
	0xf2, 0x3e, 0xac, 0xba, 0x42, 0x04, 0x62, 0xe2, 0x33, 0xcd, 0x65, 0x22, 0x57, 0x3d, 0xa5, 0x2a,
	0xb6, 0x77, 0x32, 0x90, 0xb1, 0x54, 0x02, 0x35, 0x92, 0xdc, 0x56, 0x2d, 0x8f, 0x3e, 0x03, 0x9e,
	0xc8, 0xb6, 0x46, 0x97, 0x77, 0x73, 0x68, 0x50, 0x45, 0xaf, 0x1e, 0x2d, 0xc0, 0x5c, 0x9d, 0xa1,
	0x0d, 0x8d, 0x0c, 0x9d, 0x50, 0x24, 0x67, 0xe7, 0xf0, 0xb0, 0x03, 0xa9, 0xc5, 0x4e, 0x20, 0xd8,
	0xb8, 0x59, 0xc3, 0x04, 0xdd, 0x9d, 0x7f, 0xd7, 0xb6, 0xe2, 0xec, 0x0a, 0x36, 0xa6, 0x55, 0xef,
	0x7c, 0x43, 0xee, 0x40, 0x35, 0xe0, 0x4e, 0xea, 0x54, 0xb3, 0xde, 0x36, 0xd6, 0x4d, 0x0a, 0x01,
	0xdf, 0xd2, 0x94, 0x73, 0xd8, 0x5d, 0x5d, 0x0e, 0x76, 0x9f, 0x01, 0x28, 0xd4, 0x42, 0xc8, 0xba,
	0x86, 0x0e, 0xde, 0x99, 0x6f, 0x54, 0x56, 0xe2, 0xd4, 0x42, 0x11, 0x89, 0x68, 0x32, 0x75, 0x32,
	0x3c, 0xfd, 0x33, 0xc1, 0x78, 0xb3, 0xd1, 0x36, 0xd6, 0x6b, 0x54, 0xc6, 0xeb, 0xb9, 0xdc, 0x93,
	0xbb, 0x50, 0x1b, 0xba, 0xbe, 0x73, 0x1c, 0x8d, 0x46, 0xd1, 0x29, 0xf3, 0x9b, 0xd7, 0xd1, 0xdc,
	0xea, 0xd0, 0xf5, 0x77, 0x35, 0xa9, 0xf5, 0xfb, 0x02, 0xdc, 0xdc, 0x0a, 0x72, 0xb5, 0xf8, 0x3c,
	0x78, 0x83, 0x8f, 0xcf, 0x1a, 0x54, 0x92, 0x68, 0xc4, 0x9c, 0xac, 0x9b, 0xcb, 0x72, 0xdb, 0xf5,
	0xa5, 0x46, 0x3c, 0xc8, 0x9a, 0xaa, 0x4e, 0x4d, 0x49, 0xc0, 0x6c, 0xa5, 0x87, 0xb9, 0x1e, 0xc2,
	0xc3, 0x7d, 0xd9, 0xbe, 0x77, 0xa0, 0x8a, 0x87, 0xee, 0x6b, 0x57, 0xb8, 0x89, 0xae, 0x60, 0x90,
	0xa4, 0x2d, 0xa4, 0xc8, 0x2a, 0x42, 0x06, 0x6f, 0xe8, 0x26, 0xae, 0x27, 0x74, 0x9f, 0x5b, 0xb4,
	0x2e, 0xa9, 0xdb, 0x29, 0x91, 0xec, 0x43, 0xcd, 0x1b, 0xba, 0xc2, 0x49, 0x98, 0x17, 0x25, 0x7e,
	0xfa, 0xbc, 0x3c, 0x9a, 0x2d, 0xf6, 0xbc, 0x57, 0xd2, 0xa5, 0x8d, 0xed, 0xa1, 0x2b, 0x28, 0xca,
	0xd0, 0xaa, 0x97, 0xad, 0xb9, 0x7c, 0x8f, 0x95, 0xab, 0x63, 0x77, 0xc0, 0xb0, 0xbc, 0x2d, 0x8a,
	0x6e, 0x74, 0x25, 0xa1, 0xf5, 0x19, 0xd4, 0xf2, 0x25, 0x25, 0x31, 0xee, 0x84, 0x9d, 0xe9, 0xee,
	0x97, 0x4b, 0x89, 0x64, 0xaf, 0xdd, 0xd1, 0x84, 0x69, 0x84, 0x53, 0x9b, 0xcf, 0x0a, 0x9f, 0x1a,
	0xf6, 0x6f, 0x0d, 0xa8, 0x3e, 0x77, 0x39, 0x4b, 0x9f, 0x8d, 0x77, 0xc1, 0xf2, 0xd9, 0xeb, 0xc0,
	0x63, 0xe7, 0xf8, 0x61, 0x2a, 0x82, 0x8a, 0xec, 0xd8, 0x4d, 0x4e, 0x18, 0x82, 0x8b, 0x8e, 0xac,
	0x22, 0x74, 0x7d, 0x19, 0x3c, 0x6f, 0x14, 0xc8, 0xf2, 0xcd, 0x21, 0x03, 0x28, 0x12, 0x86, 0xfe,
	0x3e, 0xac, 0x6a, 0x86, 0xd7, 0x2c, 0xe1, 0x41, 0xa4, 0x1e, 0xf2, 0x3a, 0xad, 0x2b, 0xea, 0x97,
	0x8a, 0x68, 0xff, 0xd2, 0x00, 0x78, 0xc1, 0x84, 0x1e, 0x4d, 0xc8, 0xf7, 0xc0, 0xec, 0xbb, 0x9c,
	0x39, 0x09, 0x7b, 0x85, 0xf6, 0x2c, 0x6c, 0x89, 0x9c, 0x17, 0xb4, 0xd2, 0x57, 0x9b, 0x3c, 0x18,
	0x16, 0xa6, 0xc0, 0xf0, 0x0e, 0x54, 0xf1, 0x80, 0x47, 0x93, 0xc4, 0xcb, 0xac, 0x95, 0xa4, 0x1e,
	0x52, 0xec, 0x1f, 0x41, 0x35, 0xb3, 0x82, 0xc7, 0xe4, 0x73, 0x8d, 0x40, 0x41, 0x78, 0x1c, 0x69,
	0x3b, 0x3e, 0xb8, 0xbc, 0xcd, 0x15, 0x42, 0xc9, 0x95, 0xdd, 0x83, 0x95, 0x5d, 0xc6, 0x7c, 0xb2,
	0xa9, 0x47, 0xb3, 0xe5, 0xe4, 0xd5, 0x2c, 0xb6, 0x06, 0x95, 0x63, 0xc6, 0xfc, 0x9c, 0x07, 0x72,
	0xdb, 0xf5, 0xed, 0x18, 0x1a, 0xfb, 0xec, 0x54, 0xde, 0xcb, 0xbf, 0x88, 0x5c, 0x7f, 0x2f, 0x4a,
	0x18, 0x69, 0x43, 0x6d, 0xe4, 0x72, 0xe1, 0xa4, 0x12, 0x2a, 0x81, 0x20, 0x69, 0xbb, 0x28, 0x25,
	0x53, 0x88, 0x1c, 0xb1, 0xac, 0x24, 0x9d, 0x42, 0x49, 0x38, 0x74, 0x07, 0x4c, 0xd6, 0x19, 0x1e,
	0x2a, 0x80, 0x54, 0x31, 0x41, 0x76, 0x04, 0x47, 0xfb, 0x8f, 0x05, 0xb8, 0xf6, 0x82, 0x89, 0x7d,
	0x76, 0xca, 0x51, 0xed, 0x7f, 0x9f, 0x9e, 0x6d, 0xb0, 0x46, 0x91, 0xeb, 0x3b, 0xe3, 0x28, 0x61,
	0x97, 0xbf, 0x48, 0xb3, 0xae, 0x52, 0x73, 0x94, 0x3a, 0x7d, 0x1b, 0xcc, 0x01, 0x13, 0xce, 0x38,
	0xf2, 0xd3, 0x3c, 0x56, 0x06, 0x4c, 0xec, 0x45, 0x3e, 0x23, 0x0f, 0xe0, 0x5a, 0x3f, 0x89, 0x4e,
	0x39, 0x73, 0x74, 0x15, 0xa8, 0x01, 0xc3, 0xa2, 0x75, 0x45, 0xc6, 0x58, 0xfb, 0x7c, 0xb6, 0x1a,
	0x4a, 0xb3, 0xd5, 0x20, 0x23, 0xc3, 0x27, 0xfd, 0x9f, 0x31, 0x0f, 0x4b, 0xa9, 0xac, 0x3a, 0x50,
	0x53, 0xba, 0x7e, 0x1e, 0x8b, 0x2a, 0x79, 0x2c, 0xb2, 0xff, 0x60, 0x40, 0x63, 0x3a, 0x64, 0x3c,
	0x26, 0x1f, 0x42, 0x49, 0x26, 0x88, 0x37, 0x0d, 0xc4, 0x85, 0xd6, 0x7c, 0x8f, 0x25, 0x3f, 0x55,
	0x8c, 0x5f, 0x4f, 0x9c, 0xde, 0x03, 0xeb, 0x38, 0x8a, 0x44, 0x9c, 0x04, 0x3a, 0xb9, 0x16, 0x3d,
	0x27, 0xd8, 0x7f, 0x2d, 0x00, 0xd1, 0xaf, 0x4a, 0x8f, 0x85, 0x7e, 0x8a, 0x07, 0xdf, 0x50, 0xfb,
	0xfd, 0xbf, 0x04, 0xd2, 0x48, 0x96, 0x61, 0x82, 0x0f, 0x9e, 0xaf, 0x0d, 0xaa, 0x49, 0xea, 0x21,
	0x12, 0x55, 0xb1, 0x9e, 0x33, 0xe8, 0x59, 0x2b, 0x4e, 0x0f, 0x17, 0x0f, 0xf7, 0x1f, 0xeb, 0x81,
	0xaa, 0x8c, 0x2f, 0x5c, 0x7b, 0x06, 0x76, 0xb5, 0x93, 0x9d, 0x50, 0x04, 0xe2, 0x2c, 0x37, 0x58,
	0x2d, 0xca, 0x21, 0xf9, 0x38, 0x9b, 0x09, 0x4c, 0xbc, 0xf0, 0xbd, 0xf9, 0x17, 0x4e, 0xff, 0xf6,
	0xb0, 0x6f, 0xc1, 0x8d, 0xa9, 0x70, 0xaa, 0xa1, 0xd9, 0xfe, 0x9d, 0x91, 0xd1, 0x77, 0x99, 0xf0,
	0x86, 0xdf, 0x70, 0x9c, 0x1f, 0xc0, 0x35, 0xdd, 0xd1, 0x7a, 0xb2, 0x48, 0x03, 0x5d, 0x57, 0x6d,
	0xad, 0x06, 0x07, 0xdf, 0x1e, 0x40, 0x55, 0x6f, 0xe4, 0xe8, 0x77, 0xa5, 0x29, 0x39, 0x3f, 0x0f,
	0x17, 0x67, 0xe6, 0xe1, 0x06, 0x14, 0x39, 0xfb, 0xb9, 0xc6, 0x79, 0xb9, 0xb4, 0x27, 0x99, 0x22,
	0x1a, 0x8d, 0xd8, 0xe2, 0x47, 0x7c, 0xe6, 0x29, 0x2e, 0x5c, 0x78, 0x8a, 0xaf, 0xa6, 0xf6, 0x4f,
	0x06, 0xd4, 0xa7, 0x12, 0x9f, 0x15, 0x89, 0x71, 0xa5, 0x22, 0x79, 0x0a, 0x2b, 0x13, 0x9e, 0x4d,
	0xcf, 0x97, 0x0f, 0x67, 0x32, 0x92, 0x14, 0xd9, 0xa5, 0x98, 0x34, 0x1d, 0x0d, 0x7d, 0x9b, 0x98,
	0x8c, 0x0b, 0x45, 0x76, 0xfb, 0x5f, 0xa5, 0x2c, 0x5a, 0x38, 0xdc, 0xcd, 0xfe, 0x72, 0x78, 0x06,
	0x16, 0x67, 0xa1, 0xef, 0x5c, 0xcd, 0x24, 0x53, 0xca, 0x60, 0x9a, 0x73, 0x2d, 0x54, 0x9c, 0x6e,
	0xa1, 0x9b, 0x50, 0x1a, 0x05, 0x27, 0x8c, 0xeb, 0x18, 0xaa, 0x8d, 0xec, 0x47, 0x2f, 0x61, 0xae,
	0x60, 0x8e, 0xab, 0x9a, 0xae, 0x48, 0x4d, 0x45, 0xd8, 0x12, 0xe4, 0x4b, 0xa8, 0xf1, 0x49, 0x3f,
	0xad, 0xb4, 0x74, 0xe8, 0xf9, 0xe8, 0xad, 0xf3, 0xeb, 0x46, 0x8f, 0x79, 0x51, 0xe8, 0xa7, 0x14,
	0xf9, 0xf2, 0x55, 0xf9, 0xa4, 0xaf, 0xf7, 0x7c, 0x76, 0xa2, 0xad, 0x5c, 0x98, 0x68, 0x65, 0xa9,
	0xb0, 0x78, 0x74, 0x36, 0x35, 0xd7, 0x03, 0x92, 0xd4, 0x50, 0xbf, 0x03, 0x55, 0x0c, 0x13, 0xc3,
	0x6c, 0xe2, 0x64, 0x5f, 0xdd, 0xbc, 0x77, 0xa9, 0x61, 0x2a, 0xf1, 0x14, 0xa4, 0x9c, 0x5a, 0xb7,
	0xfe, 0x59, 0x80, 0xeb, 0x17, 0x4c, 0xfd, 0x1f, 0xa6, 0x64, 0x2a, 0xf8, 0x2b, 0x33, 0xc1, 0xff,
	0x01, 0x28, 0x87, 0x95, 0xde, 0xd2, 0xb2, 0x7a, 0x2d, 0x14, 0x42, 0xc5, 0x33, 0x41, 0x2a, 0xff,
	0x47, 0x41, 0x22, 0xbb, 0x50, 0x53, 0x76, 0xe8, 0x6b, 0x2a, 0xcb, 0x5f, 0xa3, 0x92, 0xa8, 0x36,
	0xf6, 0x2f, 0xe0, 0xe6, 0x34, 0x4a, 0xea, 0x6f, 0x0e, 0xdf, 0x07, 0x33, 0x2b, 0x30, 0xf5, 0x7a,
	0x2e, 0xf1, 0x03, 0x29, 0x13, 0x91, 0xcf, 0x4e, 0xc0, 0x1d, 0x7c, 0x4a, 0x8f, 0x83, 0x30, 0xe0,
	0x43, 0x4c, 0x91, 0x49, 0x6b, 0x01, 0x3e, 0x98, 0xbb, 0x48, 0xb3, 0x79, 0xa6, 0x7c, 0x87, 0x8d,
	0x98, 0x60, 0x5f, 0x0f, 0x46, 0xbf, 0x0f, 0x90, 0x43, 0x61, 0x05, 0x68, 0x96, 0x97, 0x21, 0xf0,
	0x1a, 0xdc, 0x9a, 0x51, 0xaa, 0x5c, 0x7e, 0xf8, 0x2b, 0x03, 0xe0, 0xf0, 0x7c, 0x12, 0xf9, 0x16,
	0xdc, 0x3b, 0x3c, 0xe8, 0x1d, 0x39, 0xbd, 0x83, 0x97, 0x74, 0xbb, 0xe3, 0x6c, 0x75, 0x9d, 0x9d,
	0x6e, 0xef, 0x88, 0x76, 0xb7, 0x8f, 0x9c, 0x97, 0xfb, 0xbd, 0xc3, 0xce, 0x76, 0x77, 0xb7, 0xdb,
	0xd9, 0x69, 0xfc, 0x1f, 0x79, 0x08, 0x0f, 0x16, 0x31, 0xd2, 0xce, 0xf6, 0xc1, 0xde, 0x5e, 0x67,
	0x7f, 0x67, 0xeb, 0xa8, 0x7b, 0xb0, 0xdf, 0x30, 0x88, 0x0d, 0x1f, 0x2c, 0xe2, 0x3d, 0xec, 0xd0,
	0xde, 0xc1, 0x7e, 0xa3, 0xf0, 0xf0, 0x25, 0x98, 0x72, 0xb2, 0xc0, 0xb9, 0xea, 0x36, 0xdc, 0xda,
	0xed, 0x74, 0x76, 0x9c, 0xbd, 0x83, 0x9d, 0xce, 0x8c, 0xda, 0x35, 0xb8, 0x71, 0x7e, 0xb4, 0xdf,
	0xf9, 0xf1, 0x91, 0x73, 0xb8, 0xf5, 0xa2, 0xd3, 0x30, 0xc8, 0x2d, 0xb8, 0x7e, 0x7e, 0x40, 0x3b,
	0xbb, 0xb4, 0xd3, 0xfb, 0x61, 0xa3, 0xb0, 0xf9, 0x8f, 0x12, 0x90, 0xfc, 0xa7, 0x86, 0x3d, 0x0c,
	0x21, 0xe9, 0x43, 0x35, 0xf7, 0xe1, 0x89, 0xac, 0x2f, 0xfb, 0x19, 0xb3, 0xf5, 0xed, 0x25, 0x38,
	0x75, 0x31, 0xfd, 0xc6, 0x80, 0xe6, 0xa2, 0xaf, 0x5c, 0xe4, 0xe9, 0xfc, 0x7b, 0xde, 0xf2, 0xd9,
	0xad, 0xf5, 0xc9, 0x55, 0xc5, 0xb4, 0x2d, 0x5f, 0x41, 0x2d, 0x3f, 0x27, 0x92, 0xfb, 0x8b, 0xee,
	0x99, 0x1a, 0xbf, 0x5b, 0x0f, 0x96, 0x61, 0xe3, 0x31, 0xd9, 0x87, 0x8a, 0xfe, 0x35, 0x43, 0xda,
	0x0b, 0x45, 0x74, 0x18, 0x5b, 0x77, 0xdf, 0xc2, 0xc1, 0x63, 0x99, 0x9e, 0xdc, 0x74, 0xb3, 0x28,
	0x3d, 0x17, 0xe7, 0xc9, 0x45, 0xe9, 0x99, 0x33, 0x2a, 0x11, 0x06, 0xb5, 0x3c, 0x06, 0x90, 0xcb,
	0x45, 0xf3, 0xd3, 0x54, 0xeb, 0xe1, 0x32, 0xac, 0x5a, 0xcd, 0x30, 0x9b, 0x0c, 0x54, 0xe3, 0x91,
	0xcb, 0x85, 0xa7, 0x20, 0xa1, 0xf5, 0x68, 0x29, 0x5e, 0xa5, 0xe9, 0xf9, 0xa7, 0x3f, 0xf9, 0x64,
	0x10, 0x8d, 0xdc, 0x70, 0xb0, 0xf1, 0x74, 0x53, 0x88, 0x0d, 0x2f, 0x1a, 0x3f, 0xc1, 0x2f, 0xf9,
	0x5e, 0x34, 0x7a, 0xc2, 0x59, 0x22, 0x7f, 0x64, 0xf3, 0xb9, 0x7f, 0x1c, 0xf4, 0xcb, 0xc8, 0xf7,
	0xd1, 0xbf, 0x03, 0x00, 0x00, 0xff, 0xff, 0xb3, 0xd4, 0xcb, 0x54, 0x7b, 0x18, 0x00, 0x00,
}
