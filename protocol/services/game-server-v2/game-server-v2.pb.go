// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/game-server-v2/game-server-v2.proto

package game_server_v2 // import "golang.52tt.com/protocol/services/game-server-v2"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GameType int32

const (
	GameType_GAME_TYPE_INVALID        GameType = 0
	GameType_GAME_TYPE_EXTERNAL       GameType = 1
	GameType_GAME_TYPE_TT_GAME        GameType = 2
	GameType_GAME_TYPE_PC_OTHER_APPLY GameType = 3
)

var GameType_name = map[int32]string{
	0: "GAME_TYPE_INVALID",
	1: "GAME_TYPE_EXTERNAL",
	2: "GAME_TYPE_TT_GAME",
	3: "GAME_TYPE_PC_OTHER_APPLY",
}
var GameType_value = map[string]int32{
	"GAME_TYPE_INVALID":        0,
	"GAME_TYPE_EXTERNAL":       1,
	"GAME_TYPE_TT_GAME":        2,
	"GAME_TYPE_PC_OTHER_APPLY": 3,
}

func (x GameType) String() string {
	return proto.EnumName(GameType_name, int32(x))
}
func (GameType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{0}
}

type FastPcShowProcessInfo_FastPcShowProcessType int32

const (
	FastPcShowProcessInfo_FAST_PC_SHOW_PROCESS_TYPE_UNSPECIFIED   FastPcShowProcessInfo_FastPcShowProcessType = 0
	FastPcShowProcessInfo_FAST_PC_SHOW_PROCESS_TYPE_EXTERNAL_GAME FastPcShowProcessInfo_FastPcShowProcessType = 1
	FastPcShowProcessInfo_FAST_PC_SHOW_PROCESS_TYPE_TT_GAME       FastPcShowProcessInfo_FastPcShowProcessType = 2
	FastPcShowProcessInfo_FAST_PC_SHOW_PROCESS_TYPE_MUSIC         FastPcShowProcessInfo_FastPcShowProcessType = 3
)

var FastPcShowProcessInfo_FastPcShowProcessType_name = map[int32]string{
	0: "FAST_PC_SHOW_PROCESS_TYPE_UNSPECIFIED",
	1: "FAST_PC_SHOW_PROCESS_TYPE_EXTERNAL_GAME",
	2: "FAST_PC_SHOW_PROCESS_TYPE_TT_GAME",
	3: "FAST_PC_SHOW_PROCESS_TYPE_MUSIC",
}
var FastPcShowProcessInfo_FastPcShowProcessType_value = map[string]int32{
	"FAST_PC_SHOW_PROCESS_TYPE_UNSPECIFIED":   0,
	"FAST_PC_SHOW_PROCESS_TYPE_EXTERNAL_GAME": 1,
	"FAST_PC_SHOW_PROCESS_TYPE_TT_GAME":       2,
	"FAST_PC_SHOW_PROCESS_TYPE_MUSIC":         3,
}

func (x FastPcShowProcessInfo_FastPcShowProcessType) String() string {
	return proto.EnumName(FastPcShowProcessInfo_FastPcShowProcessType_name, int32(x))
}
func (FastPcShowProcessInfo_FastPcShowProcessType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{57, 0}
}

type GetScanGameSwitchReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScanGameSwitchReq) Reset()         { *m = GetScanGameSwitchReq{} }
func (m *GetScanGameSwitchReq) String() string { return proto.CompactTextString(m) }
func (*GetScanGameSwitchReq) ProtoMessage()    {}
func (*GetScanGameSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{0}
}
func (m *GetScanGameSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScanGameSwitchReq.Unmarshal(m, b)
}
func (m *GetScanGameSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScanGameSwitchReq.Marshal(b, m, deterministic)
}
func (dst *GetScanGameSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScanGameSwitchReq.Merge(dst, src)
}
func (m *GetScanGameSwitchReq) XXX_Size() int {
	return xxx_messageInfo_GetScanGameSwitchReq.Size(m)
}
func (m *GetScanGameSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScanGameSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetScanGameSwitchReq proto.InternalMessageInfo

func (m *GetScanGameSwitchReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetScanGameSwitchResp struct {
	Switch               uint32   `protobuf:"varint,1,opt,name=switch,proto3" json:"switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScanGameSwitchResp) Reset()         { *m = GetScanGameSwitchResp{} }
func (m *GetScanGameSwitchResp) String() string { return proto.CompactTextString(m) }
func (*GetScanGameSwitchResp) ProtoMessage()    {}
func (*GetScanGameSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{1}
}
func (m *GetScanGameSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScanGameSwitchResp.Unmarshal(m, b)
}
func (m *GetScanGameSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScanGameSwitchResp.Marshal(b, m, deterministic)
}
func (dst *GetScanGameSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScanGameSwitchResp.Merge(dst, src)
}
func (m *GetScanGameSwitchResp) XXX_Size() int {
	return xxx_messageInfo_GetScanGameSwitchResp.Size(m)
}
func (m *GetScanGameSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScanGameSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetScanGameSwitchResp proto.InternalMessageInfo

func (m *GetScanGameSwitchResp) GetSwitch() uint32 {
	if m != nil {
		return m.Switch
	}
	return 0
}

type ModifyScanGameSwitchReq struct {
	Switch               uint32   `protobuf:"varint,1,opt,name=switch,proto3" json:"switch,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyScanGameSwitchReq) Reset()         { *m = ModifyScanGameSwitchReq{} }
func (m *ModifyScanGameSwitchReq) String() string { return proto.CompactTextString(m) }
func (*ModifyScanGameSwitchReq) ProtoMessage()    {}
func (*ModifyScanGameSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{2}
}
func (m *ModifyScanGameSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyScanGameSwitchReq.Unmarshal(m, b)
}
func (m *ModifyScanGameSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyScanGameSwitchReq.Marshal(b, m, deterministic)
}
func (dst *ModifyScanGameSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyScanGameSwitchReq.Merge(dst, src)
}
func (m *ModifyScanGameSwitchReq) XXX_Size() int {
	return xxx_messageInfo_ModifyScanGameSwitchReq.Size(m)
}
func (m *ModifyScanGameSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyScanGameSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyScanGameSwitchReq proto.InternalMessageInfo

func (m *ModifyScanGameSwitchReq) GetSwitch() uint32 {
	if m != nil {
		return m.Switch
	}
	return 0
}

func (m *ModifyScanGameSwitchReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type ModifyScanGameSwitchResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyScanGameSwitchResp) Reset()         { *m = ModifyScanGameSwitchResp{} }
func (m *ModifyScanGameSwitchResp) String() string { return proto.CompactTextString(m) }
func (*ModifyScanGameSwitchResp) ProtoMessage()    {}
func (*ModifyScanGameSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{3}
}
func (m *ModifyScanGameSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyScanGameSwitchResp.Unmarshal(m, b)
}
func (m *ModifyScanGameSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyScanGameSwitchResp.Marshal(b, m, deterministic)
}
func (dst *ModifyScanGameSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyScanGameSwitchResp.Merge(dst, src)
}
func (m *ModifyScanGameSwitchResp) XXX_Size() int {
	return xxx_messageInfo_ModifyScanGameSwitchResp.Size(m)
}
func (m *ModifyScanGameSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyScanGameSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyScanGameSwitchResp proto.InternalMessageInfo

type GetScanGameListReq struct {
	GameName string `protobuf:"bytes,1,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	Offset   uint32 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit    uint32 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	// 0:客户端（历史默认） 1:PC极速
	Type                 uint32   `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScanGameListReq) Reset()         { *m = GetScanGameListReq{} }
func (m *GetScanGameListReq) String() string { return proto.CompactTextString(m) }
func (*GetScanGameListReq) ProtoMessage()    {}
func (*GetScanGameListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{4}
}
func (m *GetScanGameListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScanGameListReq.Unmarshal(m, b)
}
func (m *GetScanGameListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScanGameListReq.Marshal(b, m, deterministic)
}
func (dst *GetScanGameListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScanGameListReq.Merge(dst, src)
}
func (m *GetScanGameListReq) XXX_Size() int {
	return xxx_messageInfo_GetScanGameListReq.Size(m)
}
func (m *GetScanGameListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScanGameListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetScanGameListReq proto.InternalMessageInfo

func (m *GetScanGameListReq) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GetScanGameListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetScanGameListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetScanGameListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type ScanGameInfo struct {
	Id             uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UGameId        uint32   `protobuf:"varint,2,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	GameName       string   `protobuf:"bytes,3,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	Score          uint32   `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	ModifyTime     uint32   `protobuf:"varint,5,opt,name=modify_time,json=modifyTime,proto3" json:"modify_time,omitempty"`
	IosPackage     []string `protobuf:"bytes,6,rep,name=ios_package,json=iosPackage,proto3" json:"ios_package,omitempty"`
	AndroidPackage []string `protobuf:"bytes,7,rep,name=android_package,json=androidPackage,proto3" json:"android_package,omitempty"`
	IsHidden       bool     `protobuf:"varint,8,opt,name=is_hidden,json=isHidden,proto3" json:"is_hidden,omitempty"`
	// 极速游戏包(扫描规则- Windows-进程名称)
	PcProcessNamePackage []string `protobuf:"bytes,9,rep,name=pc_process_name_package,json=pcProcessNamePackage,proto3" json:"pc_process_name_package,omitempty"`
	// 极速游戏包(扫描规则- Windows-显示名称)
	PcShowNamePackage    []string `protobuf:"bytes,10,rep,name=pc_show_name_package,json=pcShowNamePackage,proto3" json:"pc_show_name_package,omitempty"`
	GameType             GameType `protobuf:"varint,11,opt,name=game_type,json=gameType,proto3,enum=game_server_v2.GameType" json:"game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScanGameInfo) Reset()         { *m = ScanGameInfo{} }
func (m *ScanGameInfo) String() string { return proto.CompactTextString(m) }
func (*ScanGameInfo) ProtoMessage()    {}
func (*ScanGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{5}
}
func (m *ScanGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScanGameInfo.Unmarshal(m, b)
}
func (m *ScanGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScanGameInfo.Marshal(b, m, deterministic)
}
func (dst *ScanGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanGameInfo.Merge(dst, src)
}
func (m *ScanGameInfo) XXX_Size() int {
	return xxx_messageInfo_ScanGameInfo.Size(m)
}
func (m *ScanGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ScanGameInfo proto.InternalMessageInfo

func (m *ScanGameInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ScanGameInfo) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *ScanGameInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *ScanGameInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ScanGameInfo) GetModifyTime() uint32 {
	if m != nil {
		return m.ModifyTime
	}
	return 0
}

func (m *ScanGameInfo) GetIosPackage() []string {
	if m != nil {
		return m.IosPackage
	}
	return nil
}

func (m *ScanGameInfo) GetAndroidPackage() []string {
	if m != nil {
		return m.AndroidPackage
	}
	return nil
}

func (m *ScanGameInfo) GetIsHidden() bool {
	if m != nil {
		return m.IsHidden
	}
	return false
}

func (m *ScanGameInfo) GetPcProcessNamePackage() []string {
	if m != nil {
		return m.PcProcessNamePackage
	}
	return nil
}

func (m *ScanGameInfo) GetPcShowNamePackage() []string {
	if m != nil {
		return m.PcShowNamePackage
	}
	return nil
}

func (m *ScanGameInfo) GetGameType() GameType {
	if m != nil {
		return m.GameType
	}
	return GameType_GAME_TYPE_INVALID
}

type GetScanGameListResp struct {
	GameTypeList         []*ScanGameInfo `protobuf:"bytes,1,rep,name=game_type_list,json=gameTypeList,proto3" json:"game_type_list,omitempty"`
	Total                uint32          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetScanGameListResp) Reset()         { *m = GetScanGameListResp{} }
func (m *GetScanGameListResp) String() string { return proto.CompactTextString(m) }
func (*GetScanGameListResp) ProtoMessage()    {}
func (*GetScanGameListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{6}
}
func (m *GetScanGameListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScanGameListResp.Unmarshal(m, b)
}
func (m *GetScanGameListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScanGameListResp.Marshal(b, m, deterministic)
}
func (dst *GetScanGameListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScanGameListResp.Merge(dst, src)
}
func (m *GetScanGameListResp) XXX_Size() int {
	return xxx_messageInfo_GetScanGameListResp.Size(m)
}
func (m *GetScanGameListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScanGameListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetScanGameListResp proto.InternalMessageInfo

func (m *GetScanGameListResp) GetGameTypeList() []*ScanGameInfo {
	if m != nil {
		return m.GameTypeList
	}
	return nil
}

func (m *GetScanGameListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type AddScanGameListReq struct {
	GameName       string   `protobuf:"bytes,1,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	UGameId        uint32   `protobuf:"varint,2,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	Score          int32    `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	IosPackage     []string `protobuf:"bytes,4,rep,name=ios_package,json=iosPackage,proto3" json:"ios_package,omitempty"`
	AndroidPackage []string `protobuf:"bytes,5,rep,name=android_package,json=androidPackage,proto3" json:"android_package,omitempty"`
	IsHidden       bool     `protobuf:"varint,6,opt,name=is_hidden,json=isHidden,proto3" json:"is_hidden,omitempty"`
	// 0:客户端（历史默认） 1:PC极速
	Type uint32 `protobuf:"varint,7,opt,name=type,proto3" json:"type,omitempty"`
	// 极速游戏包(扫描规则)
	PcPackage []string `protobuf:"bytes,8,rep,name=pc_package,json=pcPackage,proto3" json:"pc_package,omitempty"` // Deprecated: Do not use.
	// 极速游戏包(扫描规则- Windows-进程名称)
	PcProcessNamePackage []string `protobuf:"bytes,9,rep,name=pc_process_name_package,json=pcProcessNamePackage,proto3" json:"pc_process_name_package,omitempty"`
	// 极速游戏包(扫描规则- Windows-显示名称)
	PcShowNamePackage    []string `protobuf:"bytes,10,rep,name=pc_show_name_package,json=pcShowNamePackage,proto3" json:"pc_show_name_package,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddScanGameListReq) Reset()         { *m = AddScanGameListReq{} }
func (m *AddScanGameListReq) String() string { return proto.CompactTextString(m) }
func (*AddScanGameListReq) ProtoMessage()    {}
func (*AddScanGameListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{7}
}
func (m *AddScanGameListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddScanGameListReq.Unmarshal(m, b)
}
func (m *AddScanGameListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddScanGameListReq.Marshal(b, m, deterministic)
}
func (dst *AddScanGameListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddScanGameListReq.Merge(dst, src)
}
func (m *AddScanGameListReq) XXX_Size() int {
	return xxx_messageInfo_AddScanGameListReq.Size(m)
}
func (m *AddScanGameListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddScanGameListReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddScanGameListReq proto.InternalMessageInfo

func (m *AddScanGameListReq) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *AddScanGameListReq) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *AddScanGameListReq) GetScore() int32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *AddScanGameListReq) GetIosPackage() []string {
	if m != nil {
		return m.IosPackage
	}
	return nil
}

func (m *AddScanGameListReq) GetAndroidPackage() []string {
	if m != nil {
		return m.AndroidPackage
	}
	return nil
}

func (m *AddScanGameListReq) GetIsHidden() bool {
	if m != nil {
		return m.IsHidden
	}
	return false
}

func (m *AddScanGameListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

// Deprecated: Do not use.
func (m *AddScanGameListReq) GetPcPackage() []string {
	if m != nil {
		return m.PcPackage
	}
	return nil
}

func (m *AddScanGameListReq) GetPcProcessNamePackage() []string {
	if m != nil {
		return m.PcProcessNamePackage
	}
	return nil
}

func (m *AddScanGameListReq) GetPcShowNamePackage() []string {
	if m != nil {
		return m.PcShowNamePackage
	}
	return nil
}

type AddScanGameListResp struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddScanGameListResp) Reset()         { *m = AddScanGameListResp{} }
func (m *AddScanGameListResp) String() string { return proto.CompactTextString(m) }
func (*AddScanGameListResp) ProtoMessage()    {}
func (*AddScanGameListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{8}
}
func (m *AddScanGameListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddScanGameListResp.Unmarshal(m, b)
}
func (m *AddScanGameListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddScanGameListResp.Marshal(b, m, deterministic)
}
func (dst *AddScanGameListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddScanGameListResp.Merge(dst, src)
}
func (m *AddScanGameListResp) XXX_Size() int {
	return xxx_messageInfo_AddScanGameListResp.Size(m)
}
func (m *AddScanGameListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddScanGameListResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddScanGameListResp proto.InternalMessageInfo

func (m *AddScanGameListResp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type ModifyScanGameListReq struct {
	Id             uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Score          int32    `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	IosPackage     []string `protobuf:"bytes,3,rep,name=ios_package,json=iosPackage,proto3" json:"ios_package,omitempty"`
	AndroidPackage []string `protobuf:"bytes,4,rep,name=android_package,json=androidPackage,proto3" json:"android_package,omitempty"`
	IsHidden       bool     `protobuf:"varint,5,opt,name=is_hidden,json=isHidden,proto3" json:"is_hidden,omitempty"`
	// 极速游戏包(扫描规则- Windows-进程名称)
	PcProcessNamePackage []string `protobuf:"bytes,6,rep,name=pc_process_name_package,json=pcProcessNamePackage,proto3" json:"pc_process_name_package,omitempty"`
	// 极速游戏包(扫描规则- Windows-显示名称)
	PcShowNamePackage []string `protobuf:"bytes,7,rep,name=pc_show_name_package,json=pcShowNamePackage,proto3" json:"pc_show_name_package,omitempty"`
	// 0:客户端（历史默认） 1:PC极速
	Type                 uint32   `protobuf:"varint,8,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyScanGameListReq) Reset()         { *m = ModifyScanGameListReq{} }
func (m *ModifyScanGameListReq) String() string { return proto.CompactTextString(m) }
func (*ModifyScanGameListReq) ProtoMessage()    {}
func (*ModifyScanGameListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{9}
}
func (m *ModifyScanGameListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyScanGameListReq.Unmarshal(m, b)
}
func (m *ModifyScanGameListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyScanGameListReq.Marshal(b, m, deterministic)
}
func (dst *ModifyScanGameListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyScanGameListReq.Merge(dst, src)
}
func (m *ModifyScanGameListReq) XXX_Size() int {
	return xxx_messageInfo_ModifyScanGameListReq.Size(m)
}
func (m *ModifyScanGameListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyScanGameListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyScanGameListReq proto.InternalMessageInfo

func (m *ModifyScanGameListReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ModifyScanGameListReq) GetScore() int32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ModifyScanGameListReq) GetIosPackage() []string {
	if m != nil {
		return m.IosPackage
	}
	return nil
}

func (m *ModifyScanGameListReq) GetAndroidPackage() []string {
	if m != nil {
		return m.AndroidPackage
	}
	return nil
}

func (m *ModifyScanGameListReq) GetIsHidden() bool {
	if m != nil {
		return m.IsHidden
	}
	return false
}

func (m *ModifyScanGameListReq) GetPcProcessNamePackage() []string {
	if m != nil {
		return m.PcProcessNamePackage
	}
	return nil
}

func (m *ModifyScanGameListReq) GetPcShowNamePackage() []string {
	if m != nil {
		return m.PcShowNamePackage
	}
	return nil
}

func (m *ModifyScanGameListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type ModifyScanGameListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyScanGameListResp) Reset()         { *m = ModifyScanGameListResp{} }
func (m *ModifyScanGameListResp) String() string { return proto.CompactTextString(m) }
func (*ModifyScanGameListResp) ProtoMessage()    {}
func (*ModifyScanGameListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{10}
}
func (m *ModifyScanGameListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyScanGameListResp.Unmarshal(m, b)
}
func (m *ModifyScanGameListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyScanGameListResp.Marshal(b, m, deterministic)
}
func (dst *ModifyScanGameListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyScanGameListResp.Merge(dst, src)
}
func (m *ModifyScanGameListResp) XXX_Size() int {
	return xxx_messageInfo_ModifyScanGameListResp.Size(m)
}
func (m *ModifyScanGameListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyScanGameListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyScanGameListResp proto.InternalMessageInfo

type DeleteScanGameListReq struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 0:客户端（历史默认） 1:PC极速
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteScanGameListReq) Reset()         { *m = DeleteScanGameListReq{} }
func (m *DeleteScanGameListReq) String() string { return proto.CompactTextString(m) }
func (*DeleteScanGameListReq) ProtoMessage()    {}
func (*DeleteScanGameListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{11}
}
func (m *DeleteScanGameListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteScanGameListReq.Unmarshal(m, b)
}
func (m *DeleteScanGameListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteScanGameListReq.Marshal(b, m, deterministic)
}
func (dst *DeleteScanGameListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteScanGameListReq.Merge(dst, src)
}
func (m *DeleteScanGameListReq) XXX_Size() int {
	return xxx_messageInfo_DeleteScanGameListReq.Size(m)
}
func (m *DeleteScanGameListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteScanGameListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteScanGameListReq proto.InternalMessageInfo

func (m *DeleteScanGameListReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DeleteScanGameListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type DeleteScanGameListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteScanGameListResp) Reset()         { *m = DeleteScanGameListResp{} }
func (m *DeleteScanGameListResp) String() string { return proto.CompactTextString(m) }
func (*DeleteScanGameListResp) ProtoMessage()    {}
func (*DeleteScanGameListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{12}
}
func (m *DeleteScanGameListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteScanGameListResp.Unmarshal(m, b)
}
func (m *DeleteScanGameListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteScanGameListResp.Marshal(b, m, deterministic)
}
func (dst *DeleteScanGameListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteScanGameListResp.Merge(dst, src)
}
func (m *DeleteScanGameListResp) XXX_Size() int {
	return xxx_messageInfo_DeleteScanGameListResp.Size(m)
}
func (m *DeleteScanGameListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteScanGameListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteScanGameListResp proto.InternalMessageInfo

type ModifyUGameIdForScanGameListReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	UGameId              uint32   `protobuf:"varint,3,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyUGameIdForScanGameListReq) Reset()         { *m = ModifyUGameIdForScanGameListReq{} }
func (m *ModifyUGameIdForScanGameListReq) String() string { return proto.CompactTextString(m) }
func (*ModifyUGameIdForScanGameListReq) ProtoMessage()    {}
func (*ModifyUGameIdForScanGameListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{13}
}
func (m *ModifyUGameIdForScanGameListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyUGameIdForScanGameListReq.Unmarshal(m, b)
}
func (m *ModifyUGameIdForScanGameListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyUGameIdForScanGameListReq.Marshal(b, m, deterministic)
}
func (dst *ModifyUGameIdForScanGameListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyUGameIdForScanGameListReq.Merge(dst, src)
}
func (m *ModifyUGameIdForScanGameListReq) XXX_Size() int {
	return xxx_messageInfo_ModifyUGameIdForScanGameListReq.Size(m)
}
func (m *ModifyUGameIdForScanGameListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyUGameIdForScanGameListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyUGameIdForScanGameListReq proto.InternalMessageInfo

func (m *ModifyUGameIdForScanGameListReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ModifyUGameIdForScanGameListReq) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *ModifyUGameIdForScanGameListReq) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

type ModifyUGameIdForScanGameListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyUGameIdForScanGameListResp) Reset()         { *m = ModifyUGameIdForScanGameListResp{} }
func (m *ModifyUGameIdForScanGameListResp) String() string { return proto.CompactTextString(m) }
func (*ModifyUGameIdForScanGameListResp) ProtoMessage()    {}
func (*ModifyUGameIdForScanGameListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{14}
}
func (m *ModifyUGameIdForScanGameListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyUGameIdForScanGameListResp.Unmarshal(m, b)
}
func (m *ModifyUGameIdForScanGameListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyUGameIdForScanGameListResp.Marshal(b, m, deterministic)
}
func (dst *ModifyUGameIdForScanGameListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyUGameIdForScanGameListResp.Merge(dst, src)
}
func (m *ModifyUGameIdForScanGameListResp) XXX_Size() int {
	return xxx_messageInfo_ModifyUGameIdForScanGameListResp.Size(m)
}
func (m *ModifyUGameIdForScanGameListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyUGameIdForScanGameListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyUGameIdForScanGameListResp proto.InternalMessageInfo

type ImportScanGameInfo struct {
	GameName       string   `protobuf:"bytes,1,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	Score          int32    `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	IosPackage     []string `protobuf:"bytes,3,rep,name=ios_package,json=iosPackage,proto3" json:"ios_package,omitempty"`
	AndroidPackage []string `protobuf:"bytes,4,rep,name=android_package,json=androidPackage,proto3" json:"android_package,omitempty"`
	IsHidden       bool     `protobuf:"varint,5,opt,name=is_hidden,json=isHidden,proto3" json:"is_hidden,omitempty"`
	// 0:客户端（历史默认） 1:PC极速
	Type uint32 `protobuf:"varint,6,opt,name=type,proto3" json:"type,omitempty"`
	// 极速游戏包(扫描规则)
	PcPackage []string `protobuf:"bytes,7,rep,name=pc_package,json=pcPackage,proto3" json:"pc_package,omitempty"` // Deprecated: Do not use.
	// 极速游戏包(扫描规则- Windows-进程名称)
	PcProcessNamePackage []string `protobuf:"bytes,9,rep,name=pc_process_name_package,json=pcProcessNamePackage,proto3" json:"pc_process_name_package,omitempty"`
	// 极速游戏包(扫描规则- Windows-显示名称)
	PcShowNamePackage    []string `protobuf:"bytes,10,rep,name=pc_show_name_package,json=pcShowNamePackage,proto3" json:"pc_show_name_package,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImportScanGameInfo) Reset()         { *m = ImportScanGameInfo{} }
func (m *ImportScanGameInfo) String() string { return proto.CompactTextString(m) }
func (*ImportScanGameInfo) ProtoMessage()    {}
func (*ImportScanGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{15}
}
func (m *ImportScanGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImportScanGameInfo.Unmarshal(m, b)
}
func (m *ImportScanGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImportScanGameInfo.Marshal(b, m, deterministic)
}
func (dst *ImportScanGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportScanGameInfo.Merge(dst, src)
}
func (m *ImportScanGameInfo) XXX_Size() int {
	return xxx_messageInfo_ImportScanGameInfo.Size(m)
}
func (m *ImportScanGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportScanGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ImportScanGameInfo proto.InternalMessageInfo

func (m *ImportScanGameInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *ImportScanGameInfo) GetScore() int32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ImportScanGameInfo) GetIosPackage() []string {
	if m != nil {
		return m.IosPackage
	}
	return nil
}

func (m *ImportScanGameInfo) GetAndroidPackage() []string {
	if m != nil {
		return m.AndroidPackage
	}
	return nil
}

func (m *ImportScanGameInfo) GetIsHidden() bool {
	if m != nil {
		return m.IsHidden
	}
	return false
}

func (m *ImportScanGameInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

// Deprecated: Do not use.
func (m *ImportScanGameInfo) GetPcPackage() []string {
	if m != nil {
		return m.PcPackage
	}
	return nil
}

func (m *ImportScanGameInfo) GetPcProcessNamePackage() []string {
	if m != nil {
		return m.PcProcessNamePackage
	}
	return nil
}

func (m *ImportScanGameInfo) GetPcShowNamePackage() []string {
	if m != nil {
		return m.PcShowNamePackage
	}
	return nil
}

type ImportScanGameListReq struct {
	GameList             []*ImportScanGameInfo `protobuf:"bytes,1,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ImportScanGameListReq) Reset()         { *m = ImportScanGameListReq{} }
func (m *ImportScanGameListReq) String() string { return proto.CompactTextString(m) }
func (*ImportScanGameListReq) ProtoMessage()    {}
func (*ImportScanGameListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{16}
}
func (m *ImportScanGameListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImportScanGameListReq.Unmarshal(m, b)
}
func (m *ImportScanGameListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImportScanGameListReq.Marshal(b, m, deterministic)
}
func (dst *ImportScanGameListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportScanGameListReq.Merge(dst, src)
}
func (m *ImportScanGameListReq) XXX_Size() int {
	return xxx_messageInfo_ImportScanGameListReq.Size(m)
}
func (m *ImportScanGameListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportScanGameListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ImportScanGameListReq proto.InternalMessageInfo

func (m *ImportScanGameListReq) GetGameList() []*ImportScanGameInfo {
	if m != nil {
		return m.GameList
	}
	return nil
}

type ImportScanGameFailInfo struct {
	GameName             string   `protobuf:"bytes,1,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	FailMsg              string   `protobuf:"bytes,2,opt,name=fail_msg,json=failMsg,proto3" json:"fail_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImportScanGameFailInfo) Reset()         { *m = ImportScanGameFailInfo{} }
func (m *ImportScanGameFailInfo) String() string { return proto.CompactTextString(m) }
func (*ImportScanGameFailInfo) ProtoMessage()    {}
func (*ImportScanGameFailInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{17}
}
func (m *ImportScanGameFailInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImportScanGameFailInfo.Unmarshal(m, b)
}
func (m *ImportScanGameFailInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImportScanGameFailInfo.Marshal(b, m, deterministic)
}
func (dst *ImportScanGameFailInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportScanGameFailInfo.Merge(dst, src)
}
func (m *ImportScanGameFailInfo) XXX_Size() int {
	return xxx_messageInfo_ImportScanGameFailInfo.Size(m)
}
func (m *ImportScanGameFailInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportScanGameFailInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ImportScanGameFailInfo proto.InternalMessageInfo

func (m *ImportScanGameFailInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *ImportScanGameFailInfo) GetFailMsg() string {
	if m != nil {
		return m.FailMsg
	}
	return ""
}

type ImportScanGameListResp struct {
	ImportFailList       []*ImportScanGameFailInfo `protobuf:"bytes,1,rep,name=import_fail_list,json=importFailList,proto3" json:"import_fail_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *ImportScanGameListResp) Reset()         { *m = ImportScanGameListResp{} }
func (m *ImportScanGameListResp) String() string { return proto.CompactTextString(m) }
func (*ImportScanGameListResp) ProtoMessage()    {}
func (*ImportScanGameListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{18}
}
func (m *ImportScanGameListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImportScanGameListResp.Unmarshal(m, b)
}
func (m *ImportScanGameListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImportScanGameListResp.Marshal(b, m, deterministic)
}
func (dst *ImportScanGameListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportScanGameListResp.Merge(dst, src)
}
func (m *ImportScanGameListResp) XXX_Size() int {
	return xxx_messageInfo_ImportScanGameListResp.Size(m)
}
func (m *ImportScanGameListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportScanGameListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ImportScanGameListResp proto.InternalMessageInfo

func (m *ImportScanGameListResp) GetImportFailList() []*ImportScanGameFailInfo {
	if m != nil {
		return m.ImportFailList
	}
	return nil
}

type CreateGameReq struct {
	GameName             string   `protobuf:"bytes,1,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	Type                 GameType `protobuf:"varint,2,opt,name=type,proto3,enum=game_server_v2.GameType" json:"type,omitempty"`
	TabId                uint32   `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	GameCardId           uint32   `protobuf:"varint,4,opt,name=game_card_id,json=gameCardId,proto3" json:"game_card_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGameReq) Reset()         { *m = CreateGameReq{} }
func (m *CreateGameReq) String() string { return proto.CompactTextString(m) }
func (*CreateGameReq) ProtoMessage()    {}
func (*CreateGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{19}
}
func (m *CreateGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGameReq.Unmarshal(m, b)
}
func (m *CreateGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGameReq.Marshal(b, m, deterministic)
}
func (dst *CreateGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGameReq.Merge(dst, src)
}
func (m *CreateGameReq) XXX_Size() int {
	return xxx_messageInfo_CreateGameReq.Size(m)
}
func (m *CreateGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGameReq proto.InternalMessageInfo

func (m *CreateGameReq) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *CreateGameReq) GetType() GameType {
	if m != nil {
		return m.Type
	}
	return GameType_GAME_TYPE_INVALID
}

func (m *CreateGameReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *CreateGameReq) GetGameCardId() uint32 {
	if m != nil {
		return m.GameCardId
	}
	return 0
}

type CreateGameResp struct {
	UGameId              uint32   `protobuf:"varint,1,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGameResp) Reset()         { *m = CreateGameResp{} }
func (m *CreateGameResp) String() string { return proto.CompactTextString(m) }
func (*CreateGameResp) ProtoMessage()    {}
func (*CreateGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{20}
}
func (m *CreateGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGameResp.Unmarshal(m, b)
}
func (m *CreateGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGameResp.Marshal(b, m, deterministic)
}
func (dst *CreateGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGameResp.Merge(dst, src)
}
func (m *CreateGameResp) XXX_Size() int {
	return xxx_messageInfo_CreateGameResp.Size(m)
}
func (m *CreateGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGameResp proto.InternalMessageInfo

func (m *CreateGameResp) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

type CreateGameWithUGameIdReq struct {
	UGameId              uint32   `protobuf:"varint,1,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	Type                 GameType `protobuf:"varint,3,opt,name=type,proto3,enum=game_server_v2.GameType" json:"type,omitempty"`
	TabId                uint32   `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	GameCardId           uint32   `protobuf:"varint,5,opt,name=game_card_id,json=gameCardId,proto3" json:"game_card_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGameWithUGameIdReq) Reset()         { *m = CreateGameWithUGameIdReq{} }
func (m *CreateGameWithUGameIdReq) String() string { return proto.CompactTextString(m) }
func (*CreateGameWithUGameIdReq) ProtoMessage()    {}
func (*CreateGameWithUGameIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{21}
}
func (m *CreateGameWithUGameIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGameWithUGameIdReq.Unmarshal(m, b)
}
func (m *CreateGameWithUGameIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGameWithUGameIdReq.Marshal(b, m, deterministic)
}
func (dst *CreateGameWithUGameIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGameWithUGameIdReq.Merge(dst, src)
}
func (m *CreateGameWithUGameIdReq) XXX_Size() int {
	return xxx_messageInfo_CreateGameWithUGameIdReq.Size(m)
}
func (m *CreateGameWithUGameIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGameWithUGameIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGameWithUGameIdReq proto.InternalMessageInfo

func (m *CreateGameWithUGameIdReq) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *CreateGameWithUGameIdReq) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *CreateGameWithUGameIdReq) GetType() GameType {
	if m != nil {
		return m.Type
	}
	return GameType_GAME_TYPE_INVALID
}

func (m *CreateGameWithUGameIdReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *CreateGameWithUGameIdReq) GetGameCardId() uint32 {
	if m != nil {
		return m.GameCardId
	}
	return 0
}

type CreateGameWithUGameIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGameWithUGameIdResp) Reset()         { *m = CreateGameWithUGameIdResp{} }
func (m *CreateGameWithUGameIdResp) String() string { return proto.CompactTextString(m) }
func (*CreateGameWithUGameIdResp) ProtoMessage()    {}
func (*CreateGameWithUGameIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{22}
}
func (m *CreateGameWithUGameIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGameWithUGameIdResp.Unmarshal(m, b)
}
func (m *CreateGameWithUGameIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGameWithUGameIdResp.Marshal(b, m, deterministic)
}
func (dst *CreateGameWithUGameIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGameWithUGameIdResp.Merge(dst, src)
}
func (m *CreateGameWithUGameIdResp) XXX_Size() int {
	return xxx_messageInfo_CreateGameWithUGameIdResp.Size(m)
}
func (m *CreateGameWithUGameIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGameWithUGameIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGameWithUGameIdResp proto.InternalMessageInfo

type DeleteGameReq struct {
	UGameId              uint32   `protobuf:"varint,1,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteGameReq) Reset()         { *m = DeleteGameReq{} }
func (m *DeleteGameReq) String() string { return proto.CompactTextString(m) }
func (*DeleteGameReq) ProtoMessage()    {}
func (*DeleteGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{23}
}
func (m *DeleteGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteGameReq.Unmarshal(m, b)
}
func (m *DeleteGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteGameReq.Marshal(b, m, deterministic)
}
func (dst *DeleteGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteGameReq.Merge(dst, src)
}
func (m *DeleteGameReq) XXX_Size() int {
	return xxx_messageInfo_DeleteGameReq.Size(m)
}
func (m *DeleteGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteGameReq proto.InternalMessageInfo

func (m *DeleteGameReq) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

type DeleteGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteGameResp) Reset()         { *m = DeleteGameResp{} }
func (m *DeleteGameResp) String() string { return proto.CompactTextString(m) }
func (*DeleteGameResp) ProtoMessage()    {}
func (*DeleteGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{24}
}
func (m *DeleteGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteGameResp.Unmarshal(m, b)
}
func (m *DeleteGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteGameResp.Marshal(b, m, deterministic)
}
func (dst *DeleteGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteGameResp.Merge(dst, src)
}
func (m *DeleteGameResp) XXX_Size() int {
	return xxx_messageInfo_DeleteGameResp.Size(m)
}
func (m *DeleteGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteGameResp proto.InternalMessageInfo

type ModifyGameReq struct {
	UGameId              uint32   `protobuf:"varint,1,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	Type                 GameType `protobuf:"varint,3,opt,name=type,proto3,enum=game_server_v2.GameType" json:"type,omitempty"`
	TabId                uint32   `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	GameCardId           uint32   `protobuf:"varint,5,opt,name=game_card_id,json=gameCardId,proto3" json:"game_card_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyGameReq) Reset()         { *m = ModifyGameReq{} }
func (m *ModifyGameReq) String() string { return proto.CompactTextString(m) }
func (*ModifyGameReq) ProtoMessage()    {}
func (*ModifyGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{25}
}
func (m *ModifyGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyGameReq.Unmarshal(m, b)
}
func (m *ModifyGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyGameReq.Marshal(b, m, deterministic)
}
func (dst *ModifyGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyGameReq.Merge(dst, src)
}
func (m *ModifyGameReq) XXX_Size() int {
	return xxx_messageInfo_ModifyGameReq.Size(m)
}
func (m *ModifyGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyGameReq proto.InternalMessageInfo

func (m *ModifyGameReq) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *ModifyGameReq) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *ModifyGameReq) GetType() GameType {
	if m != nil {
		return m.Type
	}
	return GameType_GAME_TYPE_INVALID
}

func (m *ModifyGameReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ModifyGameReq) GetGameCardId() uint32 {
	if m != nil {
		return m.GameCardId
	}
	return 0
}

type ModifyGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyGameResp) Reset()         { *m = ModifyGameResp{} }
func (m *ModifyGameResp) String() string { return proto.CompactTextString(m) }
func (*ModifyGameResp) ProtoMessage()    {}
func (*ModifyGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{26}
}
func (m *ModifyGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyGameResp.Unmarshal(m, b)
}
func (m *ModifyGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyGameResp.Marshal(b, m, deterministic)
}
func (dst *ModifyGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyGameResp.Merge(dst, src)
}
func (m *ModifyGameResp) XXX_Size() int {
	return xxx_messageInfo_ModifyGameResp.Size(m)
}
func (m *ModifyGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyGameResp proto.InternalMessageInfo

type GetGameByNameReq struct {
	GameName             string   `protobuf:"bytes,1,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	UGameId              uint32   `protobuf:"varint,4,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	TabName              string   `protobuf:"bytes,5,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	GameCardName         string   `protobuf:"bytes,6,opt,name=game_card_name,json=gameCardName,proto3" json:"game_card_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameByNameReq) Reset()         { *m = GetGameByNameReq{} }
func (m *GetGameByNameReq) String() string { return proto.CompactTextString(m) }
func (*GetGameByNameReq) ProtoMessage()    {}
func (*GetGameByNameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{27}
}
func (m *GetGameByNameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameByNameReq.Unmarshal(m, b)
}
func (m *GetGameByNameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameByNameReq.Marshal(b, m, deterministic)
}
func (dst *GetGameByNameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameByNameReq.Merge(dst, src)
}
func (m *GetGameByNameReq) XXX_Size() int {
	return xxx_messageInfo_GetGameByNameReq.Size(m)
}
func (m *GetGameByNameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameByNameReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameByNameReq proto.InternalMessageInfo

func (m *GetGameByNameReq) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GetGameByNameReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGameByNameReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetGameByNameReq) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *GetGameByNameReq) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *GetGameByNameReq) GetGameCardName() string {
	if m != nil {
		return m.GameCardName
	}
	return ""
}

type GameInfo struct {
	UGameId              uint32   `protobuf:"varint,1,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	Type                 GameType `protobuf:"varint,3,opt,name=type,proto3,enum=game_server_v2.GameType" json:"type,omitempty"`
	ModifyTime           uint32   `protobuf:"varint,4,opt,name=modify_time,json=modifyTime,proto3" json:"modify_time,omitempty"`
	TabName              string   `protobuf:"bytes,5,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	GameCardName         string   `protobuf:"bytes,6,opt,name=game_card_name,json=gameCardName,proto3" json:"game_card_name,omitempty"`
	GameCardId           uint32   `protobuf:"varint,7,opt,name=game_card_id,json=gameCardId,proto3" json:"game_card_id,omitempty"`
	TabId                uint32   `protobuf:"varint,8,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameInfo) Reset()         { *m = GameInfo{} }
func (m *GameInfo) String() string { return proto.CompactTextString(m) }
func (*GameInfo) ProtoMessage()    {}
func (*GameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{28}
}
func (m *GameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameInfo.Unmarshal(m, b)
}
func (m *GameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameInfo.Marshal(b, m, deterministic)
}
func (dst *GameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameInfo.Merge(dst, src)
}
func (m *GameInfo) XXX_Size() int {
	return xxx_messageInfo_GameInfo.Size(m)
}
func (m *GameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameInfo proto.InternalMessageInfo

func (m *GameInfo) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *GameInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GameInfo) GetType() GameType {
	if m != nil {
		return m.Type
	}
	return GameType_GAME_TYPE_INVALID
}

func (m *GameInfo) GetModifyTime() uint32 {
	if m != nil {
		return m.ModifyTime
	}
	return 0
}

func (m *GameInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *GameInfo) GetGameCardName() string {
	if m != nil {
		return m.GameCardName
	}
	return ""
}

func (m *GameInfo) GetGameCardId() uint32 {
	if m != nil {
		return m.GameCardId
	}
	return 0
}

func (m *GameInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetGameByNameResp struct {
	GameList             []*GameInfo `protobuf:"bytes,1,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	Total                uint32      `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetGameByNameResp) Reset()         { *m = GetGameByNameResp{} }
func (m *GetGameByNameResp) String() string { return proto.CompactTextString(m) }
func (*GetGameByNameResp) ProtoMessage()    {}
func (*GetGameByNameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{29}
}
func (m *GetGameByNameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameByNameResp.Unmarshal(m, b)
}
func (m *GetGameByNameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameByNameResp.Marshal(b, m, deterministic)
}
func (dst *GetGameByNameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameByNameResp.Merge(dst, src)
}
func (m *GetGameByNameResp) XXX_Size() int {
	return xxx_messageInfo_GetGameByNameResp.Size(m)
}
func (m *GetGameByNameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameByNameResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameByNameResp proto.InternalMessageInfo

func (m *GetGameByNameResp) GetGameList() []*GameInfo {
	if m != nil {
		return m.GameList
	}
	return nil
}

func (m *GetGameByNameResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetGameByUGameIdReq struct {
	UGameId              uint32   `protobuf:"varint,1,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameByUGameIdReq) Reset()         { *m = GetGameByUGameIdReq{} }
func (m *GetGameByUGameIdReq) String() string { return proto.CompactTextString(m) }
func (*GetGameByUGameIdReq) ProtoMessage()    {}
func (*GetGameByUGameIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{30}
}
func (m *GetGameByUGameIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameByUGameIdReq.Unmarshal(m, b)
}
func (m *GetGameByUGameIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameByUGameIdReq.Marshal(b, m, deterministic)
}
func (dst *GetGameByUGameIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameByUGameIdReq.Merge(dst, src)
}
func (m *GetGameByUGameIdReq) XXX_Size() int {
	return xxx_messageInfo_GetGameByUGameIdReq.Size(m)
}
func (m *GetGameByUGameIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameByUGameIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameByUGameIdReq proto.InternalMessageInfo

func (m *GetGameByUGameIdReq) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

type GetGameByUGameIdResp struct {
	GameInfo             *GameInfo `protobuf:"bytes,1,opt,name=game_info,json=gameInfo,proto3" json:"game_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetGameByUGameIdResp) Reset()         { *m = GetGameByUGameIdResp{} }
func (m *GetGameByUGameIdResp) String() string { return proto.CompactTextString(m) }
func (*GetGameByUGameIdResp) ProtoMessage()    {}
func (*GetGameByUGameIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{31}
}
func (m *GetGameByUGameIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameByUGameIdResp.Unmarshal(m, b)
}
func (m *GetGameByUGameIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameByUGameIdResp.Marshal(b, m, deterministic)
}
func (dst *GetGameByUGameIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameByUGameIdResp.Merge(dst, src)
}
func (m *GetGameByUGameIdResp) XXX_Size() int {
	return xxx_messageInfo_GetGameByUGameIdResp.Size(m)
}
func (m *GetGameByUGameIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameByUGameIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameByUGameIdResp proto.InternalMessageInfo

func (m *GetGameByUGameIdResp) GetGameInfo() *GameInfo {
	if m != nil {
		return m.GameInfo
	}
	return nil
}

type GetGameByUGameIdsReq struct {
	UGameIds             []uint32 `protobuf:"varint,1,rep,packed,name=u_game_ids,json=uGameIds,proto3" json:"u_game_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameByUGameIdsReq) Reset()         { *m = GetGameByUGameIdsReq{} }
func (m *GetGameByUGameIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetGameByUGameIdsReq) ProtoMessage()    {}
func (*GetGameByUGameIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{32}
}
func (m *GetGameByUGameIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameByUGameIdsReq.Unmarshal(m, b)
}
func (m *GetGameByUGameIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameByUGameIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetGameByUGameIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameByUGameIdsReq.Merge(dst, src)
}
func (m *GetGameByUGameIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetGameByUGameIdsReq.Size(m)
}
func (m *GetGameByUGameIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameByUGameIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameByUGameIdsReq proto.InternalMessageInfo

func (m *GetGameByUGameIdsReq) GetUGameIds() []uint32 {
	if m != nil {
		return m.UGameIds
	}
	return nil
}

type GetGameByUGameIdsResp struct {
	GameInfoMap          map[uint32]*GameInfo `protobuf:"bytes,1,rep,name=game_info_map,json=gameInfoMap,proto3" json:"game_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetGameByUGameIdsResp) Reset()         { *m = GetGameByUGameIdsResp{} }
func (m *GetGameByUGameIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetGameByUGameIdsResp) ProtoMessage()    {}
func (*GetGameByUGameIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{33}
}
func (m *GetGameByUGameIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameByUGameIdsResp.Unmarshal(m, b)
}
func (m *GetGameByUGameIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameByUGameIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetGameByUGameIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameByUGameIdsResp.Merge(dst, src)
}
func (m *GetGameByUGameIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetGameByUGameIdsResp.Size(m)
}
func (m *GetGameByUGameIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameByUGameIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameByUGameIdsResp proto.InternalMessageInfo

func (m *GetGameByUGameIdsResp) GetGameInfoMap() map[uint32]*GameInfo {
	if m != nil {
		return m.GameInfoMap
	}
	return nil
}

type MinorityGameInfo struct {
	UGameId              uint32   `protobuf:"varint,1,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	GameIcon             string   `protobuf:"bytes,2,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon,omitempty"`
	GameScore            uint32   `protobuf:"varint,3,opt,name=game_score,json=gameScore,proto3" json:"game_score,omitempty"`
	GameName             string   `protobuf:"bytes,4,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	TabId                uint32   `protobuf:"varint,5,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MinorityGameInfo) Reset()         { *m = MinorityGameInfo{} }
func (m *MinorityGameInfo) String() string { return proto.CompactTextString(m) }
func (*MinorityGameInfo) ProtoMessage()    {}
func (*MinorityGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{34}
}
func (m *MinorityGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MinorityGameInfo.Unmarshal(m, b)
}
func (m *MinorityGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MinorityGameInfo.Marshal(b, m, deterministic)
}
func (dst *MinorityGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MinorityGameInfo.Merge(dst, src)
}
func (m *MinorityGameInfo) XXX_Size() int {
	return xxx_messageInfo_MinorityGameInfo.Size(m)
}
func (m *MinorityGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MinorityGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MinorityGameInfo proto.InternalMessageInfo

func (m *MinorityGameInfo) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *MinorityGameInfo) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

func (m *MinorityGameInfo) GetGameScore() uint32 {
	if m != nil {
		return m.GameScore
	}
	return 0
}

func (m *MinorityGameInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *MinorityGameInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetMinorityGameReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMinorityGameReq) Reset()         { *m = GetMinorityGameReq{} }
func (m *GetMinorityGameReq) String() string { return proto.CompactTextString(m) }
func (*GetMinorityGameReq) ProtoMessage()    {}
func (*GetMinorityGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{35}
}
func (m *GetMinorityGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMinorityGameReq.Unmarshal(m, b)
}
func (m *GetMinorityGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMinorityGameReq.Marshal(b, m, deterministic)
}
func (dst *GetMinorityGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMinorityGameReq.Merge(dst, src)
}
func (m *GetMinorityGameReq) XXX_Size() int {
	return xxx_messageInfo_GetMinorityGameReq.Size(m)
}
func (m *GetMinorityGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMinorityGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMinorityGameReq proto.InternalMessageInfo

type GetMinorityGameResp struct {
	MinorityGameList     []*MinorityGameInfo `protobuf:"bytes,1,rep,name=minority_game_list,json=minorityGameList,proto3" json:"minority_game_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetMinorityGameResp) Reset()         { *m = GetMinorityGameResp{} }
func (m *GetMinorityGameResp) String() string { return proto.CompactTextString(m) }
func (*GetMinorityGameResp) ProtoMessage()    {}
func (*GetMinorityGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{36}
}
func (m *GetMinorityGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMinorityGameResp.Unmarshal(m, b)
}
func (m *GetMinorityGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMinorityGameResp.Marshal(b, m, deterministic)
}
func (dst *GetMinorityGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMinorityGameResp.Merge(dst, src)
}
func (m *GetMinorityGameResp) XXX_Size() int {
	return xxx_messageInfo_GetMinorityGameResp.Size(m)
}
func (m *GetMinorityGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMinorityGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMinorityGameResp proto.InternalMessageInfo

func (m *GetMinorityGameResp) GetMinorityGameList() []*MinorityGameInfo {
	if m != nil {
		return m.MinorityGameList
	}
	return nil
}

type AddMinorityGameReq struct {
	MinorityGameInfos    []*MinorityGameInfo `protobuf:"bytes,1,rep,name=minority_game_infos,json=minorityGameInfos,proto3" json:"minority_game_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *AddMinorityGameReq) Reset()         { *m = AddMinorityGameReq{} }
func (m *AddMinorityGameReq) String() string { return proto.CompactTextString(m) }
func (*AddMinorityGameReq) ProtoMessage()    {}
func (*AddMinorityGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{37}
}
func (m *AddMinorityGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMinorityGameReq.Unmarshal(m, b)
}
func (m *AddMinorityGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMinorityGameReq.Marshal(b, m, deterministic)
}
func (dst *AddMinorityGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMinorityGameReq.Merge(dst, src)
}
func (m *AddMinorityGameReq) XXX_Size() int {
	return xxx_messageInfo_AddMinorityGameReq.Size(m)
}
func (m *AddMinorityGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMinorityGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddMinorityGameReq proto.InternalMessageInfo

func (m *AddMinorityGameReq) GetMinorityGameInfos() []*MinorityGameInfo {
	if m != nil {
		return m.MinorityGameInfos
	}
	return nil
}

type AddMinorityGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddMinorityGameResp) Reset()         { *m = AddMinorityGameResp{} }
func (m *AddMinorityGameResp) String() string { return proto.CompactTextString(m) }
func (*AddMinorityGameResp) ProtoMessage()    {}
func (*AddMinorityGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{38}
}
func (m *AddMinorityGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMinorityGameResp.Unmarshal(m, b)
}
func (m *AddMinorityGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMinorityGameResp.Marshal(b, m, deterministic)
}
func (dst *AddMinorityGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMinorityGameResp.Merge(dst, src)
}
func (m *AddMinorityGameResp) XXX_Size() int {
	return xxx_messageInfo_AddMinorityGameResp.Size(m)
}
func (m *AddMinorityGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMinorityGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddMinorityGameResp proto.InternalMessageInfo

type RemoveMinorityGameReq struct {
	UGameId              uint32   `protobuf:"varint,1,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveMinorityGameReq) Reset()         { *m = RemoveMinorityGameReq{} }
func (m *RemoveMinorityGameReq) String() string { return proto.CompactTextString(m) }
func (*RemoveMinorityGameReq) ProtoMessage()    {}
func (*RemoveMinorityGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{39}
}
func (m *RemoveMinorityGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveMinorityGameReq.Unmarshal(m, b)
}
func (m *RemoveMinorityGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveMinorityGameReq.Marshal(b, m, deterministic)
}
func (dst *RemoveMinorityGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveMinorityGameReq.Merge(dst, src)
}
func (m *RemoveMinorityGameReq) XXX_Size() int {
	return xxx_messageInfo_RemoveMinorityGameReq.Size(m)
}
func (m *RemoveMinorityGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveMinorityGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveMinorityGameReq proto.InternalMessageInfo

func (m *RemoveMinorityGameReq) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

type RemoveMinorityGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveMinorityGameResp) Reset()         { *m = RemoveMinorityGameResp{} }
func (m *RemoveMinorityGameResp) String() string { return proto.CompactTextString(m) }
func (*RemoveMinorityGameResp) ProtoMessage()    {}
func (*RemoveMinorityGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{40}
}
func (m *RemoveMinorityGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveMinorityGameResp.Unmarshal(m, b)
}
func (m *RemoveMinorityGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveMinorityGameResp.Marshal(b, m, deterministic)
}
func (dst *RemoveMinorityGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveMinorityGameResp.Merge(dst, src)
}
func (m *RemoveMinorityGameResp) XXX_Size() int {
	return xxx_messageInfo_RemoveMinorityGameResp.Size(m)
}
func (m *RemoveMinorityGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveMinorityGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveMinorityGameResp proto.InternalMessageInfo

type ChangeMinorityGameReq struct {
	UGameId              uint32   `protobuf:"varint,1,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	GameIcon             string   `protobuf:"bytes,2,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon,omitempty"`
	TabId                uint32   `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeMinorityGameReq) Reset()         { *m = ChangeMinorityGameReq{} }
func (m *ChangeMinorityGameReq) String() string { return proto.CompactTextString(m) }
func (*ChangeMinorityGameReq) ProtoMessage()    {}
func (*ChangeMinorityGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{41}
}
func (m *ChangeMinorityGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeMinorityGameReq.Unmarshal(m, b)
}
func (m *ChangeMinorityGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeMinorityGameReq.Marshal(b, m, deterministic)
}
func (dst *ChangeMinorityGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeMinorityGameReq.Merge(dst, src)
}
func (m *ChangeMinorityGameReq) XXX_Size() int {
	return xxx_messageInfo_ChangeMinorityGameReq.Size(m)
}
func (m *ChangeMinorityGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeMinorityGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeMinorityGameReq proto.InternalMessageInfo

func (m *ChangeMinorityGameReq) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *ChangeMinorityGameReq) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

func (m *ChangeMinorityGameReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type ChangeMinorityGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeMinorityGameResp) Reset()         { *m = ChangeMinorityGameResp{} }
func (m *ChangeMinorityGameResp) String() string { return proto.CompactTextString(m) }
func (*ChangeMinorityGameResp) ProtoMessage()    {}
func (*ChangeMinorityGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{42}
}
func (m *ChangeMinorityGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeMinorityGameResp.Unmarshal(m, b)
}
func (m *ChangeMinorityGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeMinorityGameResp.Marshal(b, m, deterministic)
}
func (dst *ChangeMinorityGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeMinorityGameResp.Merge(dst, src)
}
func (m *ChangeMinorityGameResp) XXX_Size() int {
	return xxx_messageInfo_ChangeMinorityGameResp.Size(m)
}
func (m *ChangeMinorityGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeMinorityGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeMinorityGameResp proto.InternalMessageInfo

type GetAllScanGameListConfReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllScanGameListConfReq) Reset()         { *m = GetAllScanGameListConfReq{} }
func (m *GetAllScanGameListConfReq) String() string { return proto.CompactTextString(m) }
func (*GetAllScanGameListConfReq) ProtoMessage()    {}
func (*GetAllScanGameListConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{43}
}
func (m *GetAllScanGameListConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllScanGameListConfReq.Unmarshal(m, b)
}
func (m *GetAllScanGameListConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllScanGameListConfReq.Marshal(b, m, deterministic)
}
func (dst *GetAllScanGameListConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllScanGameListConfReq.Merge(dst, src)
}
func (m *GetAllScanGameListConfReq) XXX_Size() int {
	return xxx_messageInfo_GetAllScanGameListConfReq.Size(m)
}
func (m *GetAllScanGameListConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllScanGameListConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllScanGameListConfReq proto.InternalMessageInfo

type GetAllScanGameListConfResp struct {
	ScanSwitch           uint32          `protobuf:"varint,1,opt,name=scan_switch,json=scanSwitch,proto3" json:"scan_switch,omitempty"`
	ScanGameList         []*ScanGameInfo `protobuf:"bytes,2,rep,name=scan_game_list,json=scanGameList,proto3" json:"scan_game_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAllScanGameListConfResp) Reset()         { *m = GetAllScanGameListConfResp{} }
func (m *GetAllScanGameListConfResp) String() string { return proto.CompactTextString(m) }
func (*GetAllScanGameListConfResp) ProtoMessage()    {}
func (*GetAllScanGameListConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{44}
}
func (m *GetAllScanGameListConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllScanGameListConfResp.Unmarshal(m, b)
}
func (m *GetAllScanGameListConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllScanGameListConfResp.Marshal(b, m, deterministic)
}
func (dst *GetAllScanGameListConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllScanGameListConfResp.Merge(dst, src)
}
func (m *GetAllScanGameListConfResp) XXX_Size() int {
	return xxx_messageInfo_GetAllScanGameListConfResp.Size(m)
}
func (m *GetAllScanGameListConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllScanGameListConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllScanGameListConfResp proto.InternalMessageInfo

func (m *GetAllScanGameListConfResp) GetScanSwitch() uint32 {
	if m != nil {
		return m.ScanSwitch
	}
	return 0
}

func (m *GetAllScanGameListConfResp) GetScanGameList() []*ScanGameInfo {
	if m != nil {
		return m.ScanGameList
	}
	return nil
}

type GameScanResult struct {
	GameName             string   `protobuf:"bytes,1,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	UGameId              uint32   `protobuf:"varint,2,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	Score                uint32   `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	IsHidden             bool     `protobuf:"varint,4,opt,name=is_hidden,json=isHidden,proto3" json:"is_hidden,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameScanResult) Reset()         { *m = GameScanResult{} }
func (m *GameScanResult) String() string { return proto.CompactTextString(m) }
func (*GameScanResult) ProtoMessage()    {}
func (*GameScanResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{45}
}
func (m *GameScanResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameScanResult.Unmarshal(m, b)
}
func (m *GameScanResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameScanResult.Marshal(b, m, deterministic)
}
func (dst *GameScanResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameScanResult.Merge(dst, src)
}
func (m *GameScanResult) XXX_Size() int {
	return xxx_messageInfo_GameScanResult.Size(m)
}
func (m *GameScanResult) XXX_DiscardUnknown() {
	xxx_messageInfo_GameScanResult.DiscardUnknown(m)
}

var xxx_messageInfo_GameScanResult proto.InternalMessageInfo

func (m *GameScanResult) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GameScanResult) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *GameScanResult) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *GameScanResult) GetIsHidden() bool {
	if m != nil {
		return m.IsHidden
	}
	return false
}

type ReportGameScanResultReq struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DeviceId             string            `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	GameList             []*GameScanResult `protobuf:"bytes,3,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ReportGameScanResultReq) Reset()         { *m = ReportGameScanResultReq{} }
func (m *ReportGameScanResultReq) String() string { return proto.CompactTextString(m) }
func (*ReportGameScanResultReq) ProtoMessage()    {}
func (*ReportGameScanResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{46}
}
func (m *ReportGameScanResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportGameScanResultReq.Unmarshal(m, b)
}
func (m *ReportGameScanResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportGameScanResultReq.Marshal(b, m, deterministic)
}
func (dst *ReportGameScanResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportGameScanResultReq.Merge(dst, src)
}
func (m *ReportGameScanResultReq) XXX_Size() int {
	return xxx_messageInfo_ReportGameScanResultReq.Size(m)
}
func (m *ReportGameScanResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportGameScanResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportGameScanResultReq proto.InternalMessageInfo

func (m *ReportGameScanResultReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportGameScanResultReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *ReportGameScanResultReq) GetGameList() []*GameScanResult {
	if m != nil {
		return m.GameList
	}
	return nil
}

type ReportGameScanResultResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportGameScanResultResp) Reset()         { *m = ReportGameScanResultResp{} }
func (m *ReportGameScanResultResp) String() string { return proto.CompactTextString(m) }
func (*ReportGameScanResultResp) ProtoMessage()    {}
func (*ReportGameScanResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{47}
}
func (m *ReportGameScanResultResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportGameScanResultResp.Unmarshal(m, b)
}
func (m *ReportGameScanResultResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportGameScanResultResp.Marshal(b, m, deterministic)
}
func (dst *ReportGameScanResultResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportGameScanResultResp.Merge(dst, src)
}
func (m *ReportGameScanResultResp) XXX_Size() int {
	return xxx_messageInfo_ReportGameScanResultResp.Size(m)
}
func (m *ReportGameScanResultResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportGameScanResultResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportGameScanResultResp proto.InternalMessageInfo

type GetGameScanResultReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameScanResultReq) Reset()         { *m = GetGameScanResultReq{} }
func (m *GetGameScanResultReq) String() string { return proto.CompactTextString(m) }
func (*GetGameScanResultReq) ProtoMessage()    {}
func (*GetGameScanResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{48}
}
func (m *GetGameScanResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameScanResultReq.Unmarshal(m, b)
}
func (m *GetGameScanResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameScanResultReq.Marshal(b, m, deterministic)
}
func (dst *GetGameScanResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameScanResultReq.Merge(dst, src)
}
func (m *GetGameScanResultReq) XXX_Size() int {
	return xxx_messageInfo_GetGameScanResultReq.Size(m)
}
func (m *GetGameScanResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameScanResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameScanResultReq proto.InternalMessageInfo

func (m *GetGameScanResultReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGameScanResultResp struct {
	DeviceId             string            `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	GameList             []*GameScanResult `protobuf:"bytes,2,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetGameScanResultResp) Reset()         { *m = GetGameScanResultResp{} }
func (m *GetGameScanResultResp) String() string { return proto.CompactTextString(m) }
func (*GetGameScanResultResp) ProtoMessage()    {}
func (*GetGameScanResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{49}
}
func (m *GetGameScanResultResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameScanResultResp.Unmarshal(m, b)
}
func (m *GetGameScanResultResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameScanResultResp.Marshal(b, m, deterministic)
}
func (dst *GetGameScanResultResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameScanResultResp.Merge(dst, src)
}
func (m *GetGameScanResultResp) XXX_Size() int {
	return xxx_messageInfo_GetGameScanResultResp.Size(m)
}
func (m *GetGameScanResultResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameScanResultResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameScanResultResp proto.InternalMessageInfo

func (m *GetGameScanResultResp) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetGameScanResultResp) GetGameList() []*GameScanResult {
	if m != nil {
		return m.GameList
	}
	return nil
}

type GetAllPcScanGameListConfReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllPcScanGameListConfReq) Reset()         { *m = GetAllPcScanGameListConfReq{} }
func (m *GetAllPcScanGameListConfReq) String() string { return proto.CompactTextString(m) }
func (*GetAllPcScanGameListConfReq) ProtoMessage()    {}
func (*GetAllPcScanGameListConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{50}
}
func (m *GetAllPcScanGameListConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllPcScanGameListConfReq.Unmarshal(m, b)
}
func (m *GetAllPcScanGameListConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllPcScanGameListConfReq.Marshal(b, m, deterministic)
}
func (dst *GetAllPcScanGameListConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllPcScanGameListConfReq.Merge(dst, src)
}
func (m *GetAllPcScanGameListConfReq) XXX_Size() int {
	return xxx_messageInfo_GetAllPcScanGameListConfReq.Size(m)
}
func (m *GetAllPcScanGameListConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllPcScanGameListConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllPcScanGameListConfReq proto.InternalMessageInfo

type GetAllPcScanGameListConfResp struct {
	ScanSwitch           uint32          `protobuf:"varint,1,opt,name=scan_switch,json=scanSwitch,proto3" json:"scan_switch,omitempty"`
	ScanGameList         []*ScanGameInfo `protobuf:"bytes,2,rep,name=scan_game_list,json=scanGameList,proto3" json:"scan_game_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAllPcScanGameListConfResp) Reset()         { *m = GetAllPcScanGameListConfResp{} }
func (m *GetAllPcScanGameListConfResp) String() string { return proto.CompactTextString(m) }
func (*GetAllPcScanGameListConfResp) ProtoMessage()    {}
func (*GetAllPcScanGameListConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{51}
}
func (m *GetAllPcScanGameListConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllPcScanGameListConfResp.Unmarshal(m, b)
}
func (m *GetAllPcScanGameListConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllPcScanGameListConfResp.Marshal(b, m, deterministic)
}
func (dst *GetAllPcScanGameListConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllPcScanGameListConfResp.Merge(dst, src)
}
func (m *GetAllPcScanGameListConfResp) XXX_Size() int {
	return xxx_messageInfo_GetAllPcScanGameListConfResp.Size(m)
}
func (m *GetAllPcScanGameListConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllPcScanGameListConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllPcScanGameListConfResp proto.InternalMessageInfo

func (m *GetAllPcScanGameListConfResp) GetScanSwitch() uint32 {
	if m != nil {
		return m.ScanSwitch
	}
	return 0
}

func (m *GetAllPcScanGameListConfResp) GetScanGameList() []*ScanGameInfo {
	if m != nil {
		return m.ScanGameList
	}
	return nil
}

type BatGetScanGameConfByUGameIdReq struct {
	UGameId              []uint32 `protobuf:"varint,1,rep,packed,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetScanGameConfByUGameIdReq) Reset()         { *m = BatGetScanGameConfByUGameIdReq{} }
func (m *BatGetScanGameConfByUGameIdReq) String() string { return proto.CompactTextString(m) }
func (*BatGetScanGameConfByUGameIdReq) ProtoMessage()    {}
func (*BatGetScanGameConfByUGameIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{52}
}
func (m *BatGetScanGameConfByUGameIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetScanGameConfByUGameIdReq.Unmarshal(m, b)
}
func (m *BatGetScanGameConfByUGameIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetScanGameConfByUGameIdReq.Marshal(b, m, deterministic)
}
func (dst *BatGetScanGameConfByUGameIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetScanGameConfByUGameIdReq.Merge(dst, src)
}
func (m *BatGetScanGameConfByUGameIdReq) XXX_Size() int {
	return xxx_messageInfo_BatGetScanGameConfByUGameIdReq.Size(m)
}
func (m *BatGetScanGameConfByUGameIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetScanGameConfByUGameIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetScanGameConfByUGameIdReq proto.InternalMessageInfo

func (m *BatGetScanGameConfByUGameIdReq) GetUGameId() []uint32 {
	if m != nil {
		return m.UGameId
	}
	return nil
}

type BatGetScanGameConfByUGameIdResp struct {
	ScanGameList         []*ScanGameInfo `protobuf:"bytes,1,rep,name=scan_game_list,json=scanGameList,proto3" json:"scan_game_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatGetScanGameConfByUGameIdResp) Reset()         { *m = BatGetScanGameConfByUGameIdResp{} }
func (m *BatGetScanGameConfByUGameIdResp) String() string { return proto.CompactTextString(m) }
func (*BatGetScanGameConfByUGameIdResp) ProtoMessage()    {}
func (*BatGetScanGameConfByUGameIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{53}
}
func (m *BatGetScanGameConfByUGameIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetScanGameConfByUGameIdResp.Unmarshal(m, b)
}
func (m *BatGetScanGameConfByUGameIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetScanGameConfByUGameIdResp.Marshal(b, m, deterministic)
}
func (dst *BatGetScanGameConfByUGameIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetScanGameConfByUGameIdResp.Merge(dst, src)
}
func (m *BatGetScanGameConfByUGameIdResp) XXX_Size() int {
	return xxx_messageInfo_BatGetScanGameConfByUGameIdResp.Size(m)
}
func (m *BatGetScanGameConfByUGameIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetScanGameConfByUGameIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetScanGameConfByUGameIdResp proto.InternalMessageInfo

func (m *BatGetScanGameConfByUGameIdResp) GetScanGameList() []*ScanGameInfo {
	if m != nil {
		return m.ScanGameList
	}
	return nil
}

type GetGameByGameCardIdReq struct {
	GameCardIds          []uint32 `protobuf:"varint,1,rep,packed,name=game_card_ids,json=gameCardIds,proto3" json:"game_card_ids,omitempty"`
	CategoryGameCardId   uint32   `protobuf:"varint,2,opt,name=category_game_card_id,json=categoryGameCardId,proto3" json:"category_game_card_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameByGameCardIdReq) Reset()         { *m = GetGameByGameCardIdReq{} }
func (m *GetGameByGameCardIdReq) String() string { return proto.CompactTextString(m) }
func (*GetGameByGameCardIdReq) ProtoMessage()    {}
func (*GetGameByGameCardIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{54}
}
func (m *GetGameByGameCardIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameByGameCardIdReq.Unmarshal(m, b)
}
func (m *GetGameByGameCardIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameByGameCardIdReq.Marshal(b, m, deterministic)
}
func (dst *GetGameByGameCardIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameByGameCardIdReq.Merge(dst, src)
}
func (m *GetGameByGameCardIdReq) XXX_Size() int {
	return xxx_messageInfo_GetGameByGameCardIdReq.Size(m)
}
func (m *GetGameByGameCardIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameByGameCardIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameByGameCardIdReq proto.InternalMessageInfo

func (m *GetGameByGameCardIdReq) GetGameCardIds() []uint32 {
	if m != nil {
		return m.GameCardIds
	}
	return nil
}

func (m *GetGameByGameCardIdReq) GetCategoryGameCardId() uint32 {
	if m != nil {
		return m.CategoryGameCardId
	}
	return 0
}

type GetGameByGameCardIdResp struct {
	GameList             []*GameInfo `protobuf:"bytes,1,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetGameByGameCardIdResp) Reset()         { *m = GetGameByGameCardIdResp{} }
func (m *GetGameByGameCardIdResp) String() string { return proto.CompactTextString(m) }
func (*GetGameByGameCardIdResp) ProtoMessage()    {}
func (*GetGameByGameCardIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{55}
}
func (m *GetGameByGameCardIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameByGameCardIdResp.Unmarshal(m, b)
}
func (m *GetGameByGameCardIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameByGameCardIdResp.Marshal(b, m, deterministic)
}
func (dst *GetGameByGameCardIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameByGameCardIdResp.Merge(dst, src)
}
func (m *GetGameByGameCardIdResp) XXX_Size() int {
	return xxx_messageInfo_GetGameByGameCardIdResp.Size(m)
}
func (m *GetGameByGameCardIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameByGameCardIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameByGameCardIdResp proto.InternalMessageInfo

func (m *GetGameByGameCardIdResp) GetGameList() []*GameInfo {
	if m != nil {
		return m.GameList
	}
	return nil
}

type GetFastPcShowProcessInfoReq struct {
	// 需要展示的用户id列表
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFastPcShowProcessInfoReq) Reset()         { *m = GetFastPcShowProcessInfoReq{} }
func (m *GetFastPcShowProcessInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetFastPcShowProcessInfoReq) ProtoMessage()    {}
func (*GetFastPcShowProcessInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{56}
}
func (m *GetFastPcShowProcessInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFastPcShowProcessInfoReq.Unmarshal(m, b)
}
func (m *GetFastPcShowProcessInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFastPcShowProcessInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetFastPcShowProcessInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFastPcShowProcessInfoReq.Merge(dst, src)
}
func (m *GetFastPcShowProcessInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetFastPcShowProcessInfoReq.Size(m)
}
func (m *GetFastPcShowProcessInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFastPcShowProcessInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFastPcShowProcessInfoReq proto.InternalMessageInfo

func (m *GetFastPcShowProcessInfoReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type FastPcShowProcessInfo struct {
	Type                 FastPcShowProcessInfo_FastPcShowProcessType `protobuf:"varint,1,opt,name=type,proto3,enum=game_server_v2.FastPcShowProcessInfo_FastPcShowProcessType" json:"type,omitempty"`
	ApplyName            string                                      `protobuf:"bytes,2,opt,name=apply_name,json=applyName,proto3" json:"apply_name,omitempty"`
	ProcessName          string                                      `protobuf:"bytes,3,opt,name=process_name,json=processName,proto3" json:"process_name,omitempty"`
	Icon                 string                                      `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	Content              string                                      `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	ApplyStartTime       int64                                       `protobuf:"varint,6,opt,name=apply_start_time,json=applyStartTime,proto3" json:"apply_start_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                    `json:"-"`
	XXX_unrecognized     []byte                                      `json:"-"`
	XXX_sizecache        int32                                       `json:"-"`
}

func (m *FastPcShowProcessInfo) Reset()         { *m = FastPcShowProcessInfo{} }
func (m *FastPcShowProcessInfo) String() string { return proto.CompactTextString(m) }
func (*FastPcShowProcessInfo) ProtoMessage()    {}
func (*FastPcShowProcessInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{57}
}
func (m *FastPcShowProcessInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FastPcShowProcessInfo.Unmarshal(m, b)
}
func (m *FastPcShowProcessInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FastPcShowProcessInfo.Marshal(b, m, deterministic)
}
func (dst *FastPcShowProcessInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FastPcShowProcessInfo.Merge(dst, src)
}
func (m *FastPcShowProcessInfo) XXX_Size() int {
	return xxx_messageInfo_FastPcShowProcessInfo.Size(m)
}
func (m *FastPcShowProcessInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FastPcShowProcessInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FastPcShowProcessInfo proto.InternalMessageInfo

func (m *FastPcShowProcessInfo) GetType() FastPcShowProcessInfo_FastPcShowProcessType {
	if m != nil {
		return m.Type
	}
	return FastPcShowProcessInfo_FAST_PC_SHOW_PROCESS_TYPE_UNSPECIFIED
}

func (m *FastPcShowProcessInfo) GetApplyName() string {
	if m != nil {
		return m.ApplyName
	}
	return ""
}

func (m *FastPcShowProcessInfo) GetProcessName() string {
	if m != nil {
		return m.ProcessName
	}
	return ""
}

func (m *FastPcShowProcessInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *FastPcShowProcessInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *FastPcShowProcessInfo) GetApplyStartTime() int64 {
	if m != nil {
		return m.ApplyStartTime
	}
	return 0
}

type FastPcShowProcessInfoItem struct {
	ProcessList          []*FastPcShowProcessInfo `protobuf:"bytes,1,rep,name=process_list,json=processList,proto3" json:"process_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *FastPcShowProcessInfoItem) Reset()         { *m = FastPcShowProcessInfoItem{} }
func (m *FastPcShowProcessInfoItem) String() string { return proto.CompactTextString(m) }
func (*FastPcShowProcessInfoItem) ProtoMessage()    {}
func (*FastPcShowProcessInfoItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{58}
}
func (m *FastPcShowProcessInfoItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FastPcShowProcessInfoItem.Unmarshal(m, b)
}
func (m *FastPcShowProcessInfoItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FastPcShowProcessInfoItem.Marshal(b, m, deterministic)
}
func (dst *FastPcShowProcessInfoItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FastPcShowProcessInfoItem.Merge(dst, src)
}
func (m *FastPcShowProcessInfoItem) XXX_Size() int {
	return xxx_messageInfo_FastPcShowProcessInfoItem.Size(m)
}
func (m *FastPcShowProcessInfoItem) XXX_DiscardUnknown() {
	xxx_messageInfo_FastPcShowProcessInfoItem.DiscardUnknown(m)
}

var xxx_messageInfo_FastPcShowProcessInfoItem proto.InternalMessageInfo

func (m *FastPcShowProcessInfoItem) GetProcessList() []*FastPcShowProcessInfo {
	if m != nil {
		return m.ProcessList
	}
	return nil
}

type GetFastPcShowProcessInfoResp struct {
	Items                map[uint32]*FastPcShowProcessInfoItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *GetFastPcShowProcessInfoResp) Reset()         { *m = GetFastPcShowProcessInfoResp{} }
func (m *GetFastPcShowProcessInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetFastPcShowProcessInfoResp) ProtoMessage()    {}
func (*GetFastPcShowProcessInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{59}
}
func (m *GetFastPcShowProcessInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFastPcShowProcessInfoResp.Unmarshal(m, b)
}
func (m *GetFastPcShowProcessInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFastPcShowProcessInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetFastPcShowProcessInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFastPcShowProcessInfoResp.Merge(dst, src)
}
func (m *GetFastPcShowProcessInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetFastPcShowProcessInfoResp.Size(m)
}
func (m *GetFastPcShowProcessInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFastPcShowProcessInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFastPcShowProcessInfoResp proto.InternalMessageInfo

func (m *GetFastPcShowProcessInfoResp) GetItems() map[uint32]*FastPcShowProcessInfoItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type ReportFastPcShowProcessInfoReq struct {
	Item                 *FastPcShowProcessInfoItem `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *ReportFastPcShowProcessInfoReq) Reset()         { *m = ReportFastPcShowProcessInfoReq{} }
func (m *ReportFastPcShowProcessInfoReq) String() string { return proto.CompactTextString(m) }
func (*ReportFastPcShowProcessInfoReq) ProtoMessage()    {}
func (*ReportFastPcShowProcessInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{60}
}
func (m *ReportFastPcShowProcessInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportFastPcShowProcessInfoReq.Unmarshal(m, b)
}
func (m *ReportFastPcShowProcessInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportFastPcShowProcessInfoReq.Marshal(b, m, deterministic)
}
func (dst *ReportFastPcShowProcessInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportFastPcShowProcessInfoReq.Merge(dst, src)
}
func (m *ReportFastPcShowProcessInfoReq) XXX_Size() int {
	return xxx_messageInfo_ReportFastPcShowProcessInfoReq.Size(m)
}
func (m *ReportFastPcShowProcessInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportFastPcShowProcessInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportFastPcShowProcessInfoReq proto.InternalMessageInfo

func (m *ReportFastPcShowProcessInfoReq) GetItem() *FastPcShowProcessInfoItem {
	if m != nil {
		return m.Item
	}
	return nil
}

type ReportFastPcShowProcessInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportFastPcShowProcessInfoResp) Reset()         { *m = ReportFastPcShowProcessInfoResp{} }
func (m *ReportFastPcShowProcessInfoResp) String() string { return proto.CompactTextString(m) }
func (*ReportFastPcShowProcessInfoResp) ProtoMessage()    {}
func (*ReportFastPcShowProcessInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_game_server_v2_ff73e8bc42767f9a, []int{61}
}
func (m *ReportFastPcShowProcessInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportFastPcShowProcessInfoResp.Unmarshal(m, b)
}
func (m *ReportFastPcShowProcessInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportFastPcShowProcessInfoResp.Marshal(b, m, deterministic)
}
func (dst *ReportFastPcShowProcessInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportFastPcShowProcessInfoResp.Merge(dst, src)
}
func (m *ReportFastPcShowProcessInfoResp) XXX_Size() int {
	return xxx_messageInfo_ReportFastPcShowProcessInfoResp.Size(m)
}
func (m *ReportFastPcShowProcessInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportFastPcShowProcessInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportFastPcShowProcessInfoResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetScanGameSwitchReq)(nil), "game_server_v2.GetScanGameSwitchReq")
	proto.RegisterType((*GetScanGameSwitchResp)(nil), "game_server_v2.GetScanGameSwitchResp")
	proto.RegisterType((*ModifyScanGameSwitchReq)(nil), "game_server_v2.ModifyScanGameSwitchReq")
	proto.RegisterType((*ModifyScanGameSwitchResp)(nil), "game_server_v2.ModifyScanGameSwitchResp")
	proto.RegisterType((*GetScanGameListReq)(nil), "game_server_v2.GetScanGameListReq")
	proto.RegisterType((*ScanGameInfo)(nil), "game_server_v2.ScanGameInfo")
	proto.RegisterType((*GetScanGameListResp)(nil), "game_server_v2.GetScanGameListResp")
	proto.RegisterType((*AddScanGameListReq)(nil), "game_server_v2.AddScanGameListReq")
	proto.RegisterType((*AddScanGameListResp)(nil), "game_server_v2.AddScanGameListResp")
	proto.RegisterType((*ModifyScanGameListReq)(nil), "game_server_v2.ModifyScanGameListReq")
	proto.RegisterType((*ModifyScanGameListResp)(nil), "game_server_v2.ModifyScanGameListResp")
	proto.RegisterType((*DeleteScanGameListReq)(nil), "game_server_v2.DeleteScanGameListReq")
	proto.RegisterType((*DeleteScanGameListResp)(nil), "game_server_v2.DeleteScanGameListResp")
	proto.RegisterType((*ModifyUGameIdForScanGameListReq)(nil), "game_server_v2.ModifyUGameIdForScanGameListReq")
	proto.RegisterType((*ModifyUGameIdForScanGameListResp)(nil), "game_server_v2.ModifyUGameIdForScanGameListResp")
	proto.RegisterType((*ImportScanGameInfo)(nil), "game_server_v2.ImportScanGameInfo")
	proto.RegisterType((*ImportScanGameListReq)(nil), "game_server_v2.ImportScanGameListReq")
	proto.RegisterType((*ImportScanGameFailInfo)(nil), "game_server_v2.ImportScanGameFailInfo")
	proto.RegisterType((*ImportScanGameListResp)(nil), "game_server_v2.ImportScanGameListResp")
	proto.RegisterType((*CreateGameReq)(nil), "game_server_v2.CreateGameReq")
	proto.RegisterType((*CreateGameResp)(nil), "game_server_v2.CreateGameResp")
	proto.RegisterType((*CreateGameWithUGameIdReq)(nil), "game_server_v2.CreateGameWithUGameIdReq")
	proto.RegisterType((*CreateGameWithUGameIdResp)(nil), "game_server_v2.CreateGameWithUGameIdResp")
	proto.RegisterType((*DeleteGameReq)(nil), "game_server_v2.DeleteGameReq")
	proto.RegisterType((*DeleteGameResp)(nil), "game_server_v2.DeleteGameResp")
	proto.RegisterType((*ModifyGameReq)(nil), "game_server_v2.ModifyGameReq")
	proto.RegisterType((*ModifyGameResp)(nil), "game_server_v2.ModifyGameResp")
	proto.RegisterType((*GetGameByNameReq)(nil), "game_server_v2.GetGameByNameReq")
	proto.RegisterType((*GameInfo)(nil), "game_server_v2.GameInfo")
	proto.RegisterType((*GetGameByNameResp)(nil), "game_server_v2.GetGameByNameResp")
	proto.RegisterType((*GetGameByUGameIdReq)(nil), "game_server_v2.GetGameByUGameIdReq")
	proto.RegisterType((*GetGameByUGameIdResp)(nil), "game_server_v2.GetGameByUGameIdResp")
	proto.RegisterType((*GetGameByUGameIdsReq)(nil), "game_server_v2.GetGameByUGameIdsReq")
	proto.RegisterType((*GetGameByUGameIdsResp)(nil), "game_server_v2.GetGameByUGameIdsResp")
	proto.RegisterMapType((map[uint32]*GameInfo)(nil), "game_server_v2.GetGameByUGameIdsResp.GameInfoMapEntry")
	proto.RegisterType((*MinorityGameInfo)(nil), "game_server_v2.MinorityGameInfo")
	proto.RegisterType((*GetMinorityGameReq)(nil), "game_server_v2.GetMinorityGameReq")
	proto.RegisterType((*GetMinorityGameResp)(nil), "game_server_v2.GetMinorityGameResp")
	proto.RegisterType((*AddMinorityGameReq)(nil), "game_server_v2.AddMinorityGameReq")
	proto.RegisterType((*AddMinorityGameResp)(nil), "game_server_v2.AddMinorityGameResp")
	proto.RegisterType((*RemoveMinorityGameReq)(nil), "game_server_v2.RemoveMinorityGameReq")
	proto.RegisterType((*RemoveMinorityGameResp)(nil), "game_server_v2.RemoveMinorityGameResp")
	proto.RegisterType((*ChangeMinorityGameReq)(nil), "game_server_v2.ChangeMinorityGameReq")
	proto.RegisterType((*ChangeMinorityGameResp)(nil), "game_server_v2.ChangeMinorityGameResp")
	proto.RegisterType((*GetAllScanGameListConfReq)(nil), "game_server_v2.GetAllScanGameListConfReq")
	proto.RegisterType((*GetAllScanGameListConfResp)(nil), "game_server_v2.GetAllScanGameListConfResp")
	proto.RegisterType((*GameScanResult)(nil), "game_server_v2.GameScanResult")
	proto.RegisterType((*ReportGameScanResultReq)(nil), "game_server_v2.ReportGameScanResultReq")
	proto.RegisterType((*ReportGameScanResultResp)(nil), "game_server_v2.ReportGameScanResultResp")
	proto.RegisterType((*GetGameScanResultReq)(nil), "game_server_v2.GetGameScanResultReq")
	proto.RegisterType((*GetGameScanResultResp)(nil), "game_server_v2.GetGameScanResultResp")
	proto.RegisterType((*GetAllPcScanGameListConfReq)(nil), "game_server_v2.GetAllPcScanGameListConfReq")
	proto.RegisterType((*GetAllPcScanGameListConfResp)(nil), "game_server_v2.GetAllPcScanGameListConfResp")
	proto.RegisterType((*BatGetScanGameConfByUGameIdReq)(nil), "game_server_v2.BatGetScanGameConfByUGameIdReq")
	proto.RegisterType((*BatGetScanGameConfByUGameIdResp)(nil), "game_server_v2.BatGetScanGameConfByUGameIdResp")
	proto.RegisterType((*GetGameByGameCardIdReq)(nil), "game_server_v2.GetGameByGameCardIdReq")
	proto.RegisterType((*GetGameByGameCardIdResp)(nil), "game_server_v2.GetGameByGameCardIdResp")
	proto.RegisterType((*GetFastPcShowProcessInfoReq)(nil), "game_server_v2.GetFastPcShowProcessInfoReq")
	proto.RegisterType((*FastPcShowProcessInfo)(nil), "game_server_v2.FastPcShowProcessInfo")
	proto.RegisterType((*FastPcShowProcessInfoItem)(nil), "game_server_v2.FastPcShowProcessInfoItem")
	proto.RegisterType((*GetFastPcShowProcessInfoResp)(nil), "game_server_v2.GetFastPcShowProcessInfoResp")
	proto.RegisterMapType((map[uint32]*FastPcShowProcessInfoItem)(nil), "game_server_v2.GetFastPcShowProcessInfoResp.ItemsEntry")
	proto.RegisterType((*ReportFastPcShowProcessInfoReq)(nil), "game_server_v2.ReportFastPcShowProcessInfoReq")
	proto.RegisterType((*ReportFastPcShowProcessInfoResp)(nil), "game_server_v2.ReportFastPcShowProcessInfoResp")
	proto.RegisterEnum("game_server_v2.GameType", GameType_name, GameType_value)
	proto.RegisterEnum("game_server_v2.FastPcShowProcessInfo_FastPcShowProcessType", FastPcShowProcessInfo_FastPcShowProcessType_name, FastPcShowProcessInfo_FastPcShowProcessType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GameConfMgrClient is the client API for GameConfMgr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameConfMgrClient interface {
	// 游戏扫描相关begin-----------------------------------
	// 扫描游戏列表开关
	GetScanGameSwitch(ctx context.Context, in *GetScanGameSwitchReq, opts ...grpc.CallOption) (*GetScanGameSwitchResp, error)
	ModifyScanGameSwitch(ctx context.Context, in *ModifyScanGameSwitchReq, opts ...grpc.CallOption) (*ModifyScanGameSwitchResp, error)
	// 扫描游戏列表配置
	AddScanGameList(ctx context.Context, in *AddScanGameListReq, opts ...grpc.CallOption) (*AddScanGameListResp, error)
	ModifyScanGameList(ctx context.Context, in *ModifyScanGameListReq, opts ...grpc.CallOption) (*ModifyScanGameListResp, error)
	DeleteScanGameList(ctx context.Context, in *DeleteScanGameListReq, opts ...grpc.CallOption) (*DeleteScanGameListResp, error)
	GetScanGameList(ctx context.Context, in *GetScanGameListReq, opts ...grpc.CallOption) (*GetScanGameListResp, error)
	// 用来同步gamelist的UGameId
	ModifyUGameIdForScanGameList(ctx context.Context, in *ModifyUGameIdForScanGameListReq, opts ...grpc.CallOption) (*ModifyUGameIdForScanGameListResp, error)
	// 导入游戏扫描
	ImportScanGameList(ctx context.Context, in *ImportScanGameListReq, opts ...grpc.CallOption) (*ImportScanGameListResp, error)
	// 游戏创建
	// ugameid : unified gameid,区分旧的游戏系统的gameid
	CreateGame(ctx context.Context, in *CreateGameReq, opts ...grpc.CallOption) (*CreateGameResp, error)
	// 这个用来创建旧系统的已存在的游戏，用旧系统的GameId来创建游戏,1 -- 499999  只给服务端自己用
	CreateGameWithUGameId(ctx context.Context, in *CreateGameWithUGameIdReq, opts ...grpc.CallOption) (*CreateGameWithUGameIdResp, error)
	DeleteGame(ctx context.Context, in *DeleteGameReq, opts ...grpc.CallOption) (*DeleteGameResp, error)
	ModifyGame(ctx context.Context, in *ModifyGameReq, opts ...grpc.CallOption) (*ModifyGameResp, error)
	GetGameByName(ctx context.Context, in *GetGameByNameReq, opts ...grpc.CallOption) (*GetGameByNameResp, error)
	GetGameByUGameId(ctx context.Context, in *GetGameByUGameIdReq, opts ...grpc.CallOption) (*GetGameByUGameIdResp, error)
	GetGameByUGameIds(ctx context.Context, in *GetGameByUGameIdsReq, opts ...grpc.CallOption) (*GetGameByUGameIdsResp, error)
	GetGameByGameCardId(ctx context.Context, in *GetGameByGameCardIdReq, opts ...grpc.CallOption) (*GetGameByGameCardIdResp, error)
	// 小众游戏相关
	GetMinorityGame(ctx context.Context, in *GetMinorityGameReq, opts ...grpc.CallOption) (*GetMinorityGameResp, error)
	AddMinorityGame(ctx context.Context, in *AddMinorityGameReq, opts ...grpc.CallOption) (*AddMinorityGameResp, error)
	RemoveMinorityGame(ctx context.Context, in *RemoveMinorityGameReq, opts ...grpc.CallOption) (*RemoveMinorityGameResp, error)
	ChangeMinorityGame(ctx context.Context, in *ChangeMinorityGameReq, opts ...grpc.CallOption) (*ChangeMinorityGameResp, error)
}

type gameConfMgrClient struct {
	cc *grpc.ClientConn
}

func NewGameConfMgrClient(cc *grpc.ClientConn) GameConfMgrClient {
	return &gameConfMgrClient{cc}
}

func (c *gameConfMgrClient) GetScanGameSwitch(ctx context.Context, in *GetScanGameSwitchReq, opts ...grpc.CallOption) (*GetScanGameSwitchResp, error) {
	out := new(GetScanGameSwitchResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/GetScanGameSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) ModifyScanGameSwitch(ctx context.Context, in *ModifyScanGameSwitchReq, opts ...grpc.CallOption) (*ModifyScanGameSwitchResp, error) {
	out := new(ModifyScanGameSwitchResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/ModifyScanGameSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) AddScanGameList(ctx context.Context, in *AddScanGameListReq, opts ...grpc.CallOption) (*AddScanGameListResp, error) {
	out := new(AddScanGameListResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/AddScanGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) ModifyScanGameList(ctx context.Context, in *ModifyScanGameListReq, opts ...grpc.CallOption) (*ModifyScanGameListResp, error) {
	out := new(ModifyScanGameListResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/ModifyScanGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) DeleteScanGameList(ctx context.Context, in *DeleteScanGameListReq, opts ...grpc.CallOption) (*DeleteScanGameListResp, error) {
	out := new(DeleteScanGameListResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/DeleteScanGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) GetScanGameList(ctx context.Context, in *GetScanGameListReq, opts ...grpc.CallOption) (*GetScanGameListResp, error) {
	out := new(GetScanGameListResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/GetScanGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) ModifyUGameIdForScanGameList(ctx context.Context, in *ModifyUGameIdForScanGameListReq, opts ...grpc.CallOption) (*ModifyUGameIdForScanGameListResp, error) {
	out := new(ModifyUGameIdForScanGameListResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/ModifyUGameIdForScanGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) ImportScanGameList(ctx context.Context, in *ImportScanGameListReq, opts ...grpc.CallOption) (*ImportScanGameListResp, error) {
	out := new(ImportScanGameListResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/ImportScanGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) CreateGame(ctx context.Context, in *CreateGameReq, opts ...grpc.CallOption) (*CreateGameResp, error) {
	out := new(CreateGameResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/CreateGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) CreateGameWithUGameId(ctx context.Context, in *CreateGameWithUGameIdReq, opts ...grpc.CallOption) (*CreateGameWithUGameIdResp, error) {
	out := new(CreateGameWithUGameIdResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/CreateGameWithUGameId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) DeleteGame(ctx context.Context, in *DeleteGameReq, opts ...grpc.CallOption) (*DeleteGameResp, error) {
	out := new(DeleteGameResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/DeleteGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) ModifyGame(ctx context.Context, in *ModifyGameReq, opts ...grpc.CallOption) (*ModifyGameResp, error) {
	out := new(ModifyGameResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/ModifyGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) GetGameByName(ctx context.Context, in *GetGameByNameReq, opts ...grpc.CallOption) (*GetGameByNameResp, error) {
	out := new(GetGameByNameResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/GetGameByName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) GetGameByUGameId(ctx context.Context, in *GetGameByUGameIdReq, opts ...grpc.CallOption) (*GetGameByUGameIdResp, error) {
	out := new(GetGameByUGameIdResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/GetGameByUGameId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) GetGameByUGameIds(ctx context.Context, in *GetGameByUGameIdsReq, opts ...grpc.CallOption) (*GetGameByUGameIdsResp, error) {
	out := new(GetGameByUGameIdsResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/GetGameByUGameIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) GetGameByGameCardId(ctx context.Context, in *GetGameByGameCardIdReq, opts ...grpc.CallOption) (*GetGameByGameCardIdResp, error) {
	out := new(GetGameByGameCardIdResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/GetGameByGameCardId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) GetMinorityGame(ctx context.Context, in *GetMinorityGameReq, opts ...grpc.CallOption) (*GetMinorityGameResp, error) {
	out := new(GetMinorityGameResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/GetMinorityGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) AddMinorityGame(ctx context.Context, in *AddMinorityGameReq, opts ...grpc.CallOption) (*AddMinorityGameResp, error) {
	out := new(AddMinorityGameResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/AddMinorityGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) RemoveMinorityGame(ctx context.Context, in *RemoveMinorityGameReq, opts ...grpc.CallOption) (*RemoveMinorityGameResp, error) {
	out := new(RemoveMinorityGameResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/RemoveMinorityGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameConfMgrClient) ChangeMinorityGame(ctx context.Context, in *ChangeMinorityGameReq, opts ...grpc.CallOption) (*ChangeMinorityGameResp, error) {
	out := new(ChangeMinorityGameResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameConfMgr/ChangeMinorityGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameConfMgrServer is the server API for GameConfMgr service.
type GameConfMgrServer interface {
	// 游戏扫描相关begin-----------------------------------
	// 扫描游戏列表开关
	GetScanGameSwitch(context.Context, *GetScanGameSwitchReq) (*GetScanGameSwitchResp, error)
	ModifyScanGameSwitch(context.Context, *ModifyScanGameSwitchReq) (*ModifyScanGameSwitchResp, error)
	// 扫描游戏列表配置
	AddScanGameList(context.Context, *AddScanGameListReq) (*AddScanGameListResp, error)
	ModifyScanGameList(context.Context, *ModifyScanGameListReq) (*ModifyScanGameListResp, error)
	DeleteScanGameList(context.Context, *DeleteScanGameListReq) (*DeleteScanGameListResp, error)
	GetScanGameList(context.Context, *GetScanGameListReq) (*GetScanGameListResp, error)
	// 用来同步gamelist的UGameId
	ModifyUGameIdForScanGameList(context.Context, *ModifyUGameIdForScanGameListReq) (*ModifyUGameIdForScanGameListResp, error)
	// 导入游戏扫描
	ImportScanGameList(context.Context, *ImportScanGameListReq) (*ImportScanGameListResp, error)
	// 游戏创建
	// ugameid : unified gameid,区分旧的游戏系统的gameid
	CreateGame(context.Context, *CreateGameReq) (*CreateGameResp, error)
	// 这个用来创建旧系统的已存在的游戏，用旧系统的GameId来创建游戏,1 -- 499999  只给服务端自己用
	CreateGameWithUGameId(context.Context, *CreateGameWithUGameIdReq) (*CreateGameWithUGameIdResp, error)
	DeleteGame(context.Context, *DeleteGameReq) (*DeleteGameResp, error)
	ModifyGame(context.Context, *ModifyGameReq) (*ModifyGameResp, error)
	GetGameByName(context.Context, *GetGameByNameReq) (*GetGameByNameResp, error)
	GetGameByUGameId(context.Context, *GetGameByUGameIdReq) (*GetGameByUGameIdResp, error)
	GetGameByUGameIds(context.Context, *GetGameByUGameIdsReq) (*GetGameByUGameIdsResp, error)
	GetGameByGameCardId(context.Context, *GetGameByGameCardIdReq) (*GetGameByGameCardIdResp, error)
	// 小众游戏相关
	GetMinorityGame(context.Context, *GetMinorityGameReq) (*GetMinorityGameResp, error)
	AddMinorityGame(context.Context, *AddMinorityGameReq) (*AddMinorityGameResp, error)
	RemoveMinorityGame(context.Context, *RemoveMinorityGameReq) (*RemoveMinorityGameResp, error)
	ChangeMinorityGame(context.Context, *ChangeMinorityGameReq) (*ChangeMinorityGameResp, error)
}

func RegisterGameConfMgrServer(s *grpc.Server, srv GameConfMgrServer) {
	s.RegisterService(&_GameConfMgr_serviceDesc, srv)
}

func _GameConfMgr_GetScanGameSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScanGameSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).GetScanGameSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/GetScanGameSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).GetScanGameSwitch(ctx, req.(*GetScanGameSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_ModifyScanGameSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyScanGameSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).ModifyScanGameSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/ModifyScanGameSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).ModifyScanGameSwitch(ctx, req.(*ModifyScanGameSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_AddScanGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddScanGameListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).AddScanGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/AddScanGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).AddScanGameList(ctx, req.(*AddScanGameListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_ModifyScanGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyScanGameListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).ModifyScanGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/ModifyScanGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).ModifyScanGameList(ctx, req.(*ModifyScanGameListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_DeleteScanGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteScanGameListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).DeleteScanGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/DeleteScanGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).DeleteScanGameList(ctx, req.(*DeleteScanGameListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_GetScanGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScanGameListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).GetScanGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/GetScanGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).GetScanGameList(ctx, req.(*GetScanGameListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_ModifyUGameIdForScanGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyUGameIdForScanGameListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).ModifyUGameIdForScanGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/ModifyUGameIdForScanGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).ModifyUGameIdForScanGameList(ctx, req.(*ModifyUGameIdForScanGameListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_ImportScanGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportScanGameListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).ImportScanGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/ImportScanGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).ImportScanGameList(ctx, req.(*ImportScanGameListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_CreateGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).CreateGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/CreateGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).CreateGame(ctx, req.(*CreateGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_CreateGameWithUGameId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGameWithUGameIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).CreateGameWithUGameId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/CreateGameWithUGameId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).CreateGameWithUGameId(ctx, req.(*CreateGameWithUGameIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_DeleteGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).DeleteGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/DeleteGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).DeleteGame(ctx, req.(*DeleteGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_ModifyGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).ModifyGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/ModifyGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).ModifyGame(ctx, req.(*ModifyGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_GetGameByName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameByNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).GetGameByName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/GetGameByName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).GetGameByName(ctx, req.(*GetGameByNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_GetGameByUGameId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameByUGameIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).GetGameByUGameId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/GetGameByUGameId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).GetGameByUGameId(ctx, req.(*GetGameByUGameIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_GetGameByUGameIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameByUGameIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).GetGameByUGameIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/GetGameByUGameIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).GetGameByUGameIds(ctx, req.(*GetGameByUGameIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_GetGameByGameCardId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameByGameCardIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).GetGameByGameCardId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/GetGameByGameCardId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).GetGameByGameCardId(ctx, req.(*GetGameByGameCardIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_GetMinorityGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMinorityGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).GetMinorityGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/GetMinorityGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).GetMinorityGame(ctx, req.(*GetMinorityGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_AddMinorityGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMinorityGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).AddMinorityGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/AddMinorityGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).AddMinorityGame(ctx, req.(*AddMinorityGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_RemoveMinorityGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveMinorityGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).RemoveMinorityGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/RemoveMinorityGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).RemoveMinorityGame(ctx, req.(*RemoveMinorityGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameConfMgr_ChangeMinorityGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeMinorityGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameConfMgrServer).ChangeMinorityGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameConfMgr/ChangeMinorityGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameConfMgrServer).ChangeMinorityGame(ctx, req.(*ChangeMinorityGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameConfMgr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "game_server_v2.GameConfMgr",
	HandlerType: (*GameConfMgrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetScanGameSwitch",
			Handler:    _GameConfMgr_GetScanGameSwitch_Handler,
		},
		{
			MethodName: "ModifyScanGameSwitch",
			Handler:    _GameConfMgr_ModifyScanGameSwitch_Handler,
		},
		{
			MethodName: "AddScanGameList",
			Handler:    _GameConfMgr_AddScanGameList_Handler,
		},
		{
			MethodName: "ModifyScanGameList",
			Handler:    _GameConfMgr_ModifyScanGameList_Handler,
		},
		{
			MethodName: "DeleteScanGameList",
			Handler:    _GameConfMgr_DeleteScanGameList_Handler,
		},
		{
			MethodName: "GetScanGameList",
			Handler:    _GameConfMgr_GetScanGameList_Handler,
		},
		{
			MethodName: "ModifyUGameIdForScanGameList",
			Handler:    _GameConfMgr_ModifyUGameIdForScanGameList_Handler,
		},
		{
			MethodName: "ImportScanGameList",
			Handler:    _GameConfMgr_ImportScanGameList_Handler,
		},
		{
			MethodName: "CreateGame",
			Handler:    _GameConfMgr_CreateGame_Handler,
		},
		{
			MethodName: "CreateGameWithUGameId",
			Handler:    _GameConfMgr_CreateGameWithUGameId_Handler,
		},
		{
			MethodName: "DeleteGame",
			Handler:    _GameConfMgr_DeleteGame_Handler,
		},
		{
			MethodName: "ModifyGame",
			Handler:    _GameConfMgr_ModifyGame_Handler,
		},
		{
			MethodName: "GetGameByName",
			Handler:    _GameConfMgr_GetGameByName_Handler,
		},
		{
			MethodName: "GetGameByUGameId",
			Handler:    _GameConfMgr_GetGameByUGameId_Handler,
		},
		{
			MethodName: "GetGameByUGameIds",
			Handler:    _GameConfMgr_GetGameByUGameIds_Handler,
		},
		{
			MethodName: "GetGameByGameCardId",
			Handler:    _GameConfMgr_GetGameByGameCardId_Handler,
		},
		{
			MethodName: "GetMinorityGame",
			Handler:    _GameConfMgr_GetMinorityGame_Handler,
		},
		{
			MethodName: "AddMinorityGame",
			Handler:    _GameConfMgr_AddMinorityGame_Handler,
		},
		{
			MethodName: "RemoveMinorityGame",
			Handler:    _GameConfMgr_RemoveMinorityGame_Handler,
		},
		{
			MethodName: "ChangeMinorityGame",
			Handler:    _GameConfMgr_ChangeMinorityGame_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/game-server-v2/game-server-v2.proto",
}

// GameServerV2Client is the client API for GameServerV2 service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameServerV2Client interface {
	// 游戏扫描相关begin-----------------------------------
	GetAllScanGameListConf(ctx context.Context, in *GetAllScanGameListConfReq, opts ...grpc.CallOption) (*GetAllScanGameListConfResp, error)
	BatGetScanGameConfByUGameId(ctx context.Context, in *BatGetScanGameConfByUGameIdReq, opts ...grpc.CallOption) (*BatGetScanGameConfByUGameIdResp, error)
	ReportGameScanResult(ctx context.Context, in *ReportGameScanResultReq, opts ...grpc.CallOption) (*ReportGameScanResultResp, error)
	GetGameScanResult(ctx context.Context, in *GetGameScanResultReq, opts ...grpc.CallOption) (*GetGameScanResultResp, error)
	GetAllPcScanGameListConf(ctx context.Context, in *GetAllPcScanGameListConfReq, opts ...grpc.CallOption) (*GetAllPcScanGameListConfResp, error)
	GetFastPcShowProcessInfo(ctx context.Context, in *GetFastPcShowProcessInfoReq, opts ...grpc.CallOption) (*GetFastPcShowProcessInfoResp, error)
	ReportFastPcShowProcessInfo(ctx context.Context, in *ReportFastPcShowProcessInfoReq, opts ...grpc.CallOption) (*ReportFastPcShowProcessInfoResp, error)
	// 小众游戏相关begin-----------------------------------
	GetMinorityGameWithCache(ctx context.Context, in *GetMinorityGameReq, opts ...grpc.CallOption) (*GetMinorityGameResp, error)
}

type gameServerV2Client struct {
	cc *grpc.ClientConn
}

func NewGameServerV2Client(cc *grpc.ClientConn) GameServerV2Client {
	return &gameServerV2Client{cc}
}

func (c *gameServerV2Client) GetAllScanGameListConf(ctx context.Context, in *GetAllScanGameListConfReq, opts ...grpc.CallOption) (*GetAllScanGameListConfResp, error) {
	out := new(GetAllScanGameListConfResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameServerV2/GetAllScanGameListConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerV2Client) BatGetScanGameConfByUGameId(ctx context.Context, in *BatGetScanGameConfByUGameIdReq, opts ...grpc.CallOption) (*BatGetScanGameConfByUGameIdResp, error) {
	out := new(BatGetScanGameConfByUGameIdResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameServerV2/BatGetScanGameConfByUGameId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerV2Client) ReportGameScanResult(ctx context.Context, in *ReportGameScanResultReq, opts ...grpc.CallOption) (*ReportGameScanResultResp, error) {
	out := new(ReportGameScanResultResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameServerV2/ReportGameScanResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerV2Client) GetGameScanResult(ctx context.Context, in *GetGameScanResultReq, opts ...grpc.CallOption) (*GetGameScanResultResp, error) {
	out := new(GetGameScanResultResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameServerV2/GetGameScanResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerV2Client) GetAllPcScanGameListConf(ctx context.Context, in *GetAllPcScanGameListConfReq, opts ...grpc.CallOption) (*GetAllPcScanGameListConfResp, error) {
	out := new(GetAllPcScanGameListConfResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameServerV2/GetAllPcScanGameListConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerV2Client) GetFastPcShowProcessInfo(ctx context.Context, in *GetFastPcShowProcessInfoReq, opts ...grpc.CallOption) (*GetFastPcShowProcessInfoResp, error) {
	out := new(GetFastPcShowProcessInfoResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameServerV2/GetFastPcShowProcessInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerV2Client) ReportFastPcShowProcessInfo(ctx context.Context, in *ReportFastPcShowProcessInfoReq, opts ...grpc.CallOption) (*ReportFastPcShowProcessInfoResp, error) {
	out := new(ReportFastPcShowProcessInfoResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameServerV2/ReportFastPcShowProcessInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameServerV2Client) GetMinorityGameWithCache(ctx context.Context, in *GetMinorityGameReq, opts ...grpc.CallOption) (*GetMinorityGameResp, error) {
	out := new(GetMinorityGameResp)
	err := c.cc.Invoke(ctx, "/game_server_v2.GameServerV2/GetMinorityGameWithCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameServerV2Server is the server API for GameServerV2 service.
type GameServerV2Server interface {
	// 游戏扫描相关begin-----------------------------------
	GetAllScanGameListConf(context.Context, *GetAllScanGameListConfReq) (*GetAllScanGameListConfResp, error)
	BatGetScanGameConfByUGameId(context.Context, *BatGetScanGameConfByUGameIdReq) (*BatGetScanGameConfByUGameIdResp, error)
	ReportGameScanResult(context.Context, *ReportGameScanResultReq) (*ReportGameScanResultResp, error)
	GetGameScanResult(context.Context, *GetGameScanResultReq) (*GetGameScanResultResp, error)
	GetAllPcScanGameListConf(context.Context, *GetAllPcScanGameListConfReq) (*GetAllPcScanGameListConfResp, error)
	GetFastPcShowProcessInfo(context.Context, *GetFastPcShowProcessInfoReq) (*GetFastPcShowProcessInfoResp, error)
	ReportFastPcShowProcessInfo(context.Context, *ReportFastPcShowProcessInfoReq) (*ReportFastPcShowProcessInfoResp, error)
	// 小众游戏相关begin-----------------------------------
	GetMinorityGameWithCache(context.Context, *GetMinorityGameReq) (*GetMinorityGameResp, error)
}

func RegisterGameServerV2Server(s *grpc.Server, srv GameServerV2Server) {
	s.RegisterService(&_GameServerV2_serviceDesc, srv)
}

func _GameServerV2_GetAllScanGameListConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllScanGameListConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerV2Server).GetAllScanGameListConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameServerV2/GetAllScanGameListConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerV2Server).GetAllScanGameListConf(ctx, req.(*GetAllScanGameListConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServerV2_BatGetScanGameConfByUGameId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetScanGameConfByUGameIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerV2Server).BatGetScanGameConfByUGameId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameServerV2/BatGetScanGameConfByUGameId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerV2Server).BatGetScanGameConfByUGameId(ctx, req.(*BatGetScanGameConfByUGameIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServerV2_ReportGameScanResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportGameScanResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerV2Server).ReportGameScanResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameServerV2/ReportGameScanResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerV2Server).ReportGameScanResult(ctx, req.(*ReportGameScanResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServerV2_GetGameScanResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameScanResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerV2Server).GetGameScanResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameServerV2/GetGameScanResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerV2Server).GetGameScanResult(ctx, req.(*GetGameScanResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServerV2_GetAllPcScanGameListConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllPcScanGameListConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerV2Server).GetAllPcScanGameListConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameServerV2/GetAllPcScanGameListConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerV2Server).GetAllPcScanGameListConf(ctx, req.(*GetAllPcScanGameListConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServerV2_GetFastPcShowProcessInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFastPcShowProcessInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerV2Server).GetFastPcShowProcessInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameServerV2/GetFastPcShowProcessInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerV2Server).GetFastPcShowProcessInfo(ctx, req.(*GetFastPcShowProcessInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServerV2_ReportFastPcShowProcessInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportFastPcShowProcessInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerV2Server).ReportFastPcShowProcessInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameServerV2/ReportFastPcShowProcessInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerV2Server).ReportFastPcShowProcessInfo(ctx, req.(*ReportFastPcShowProcessInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameServerV2_GetMinorityGameWithCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMinorityGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServerV2Server).GetMinorityGameWithCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/game_server_v2.GameServerV2/GetMinorityGameWithCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServerV2Server).GetMinorityGameWithCache(ctx, req.(*GetMinorityGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameServerV2_serviceDesc = grpc.ServiceDesc{
	ServiceName: "game_server_v2.GameServerV2",
	HandlerType: (*GameServerV2Server)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAllScanGameListConf",
			Handler:    _GameServerV2_GetAllScanGameListConf_Handler,
		},
		{
			MethodName: "BatGetScanGameConfByUGameId",
			Handler:    _GameServerV2_BatGetScanGameConfByUGameId_Handler,
		},
		{
			MethodName: "ReportGameScanResult",
			Handler:    _GameServerV2_ReportGameScanResult_Handler,
		},
		{
			MethodName: "GetGameScanResult",
			Handler:    _GameServerV2_GetGameScanResult_Handler,
		},
		{
			MethodName: "GetAllPcScanGameListConf",
			Handler:    _GameServerV2_GetAllPcScanGameListConf_Handler,
		},
		{
			MethodName: "GetFastPcShowProcessInfo",
			Handler:    _GameServerV2_GetFastPcShowProcessInfo_Handler,
		},
		{
			MethodName: "ReportFastPcShowProcessInfo",
			Handler:    _GameServerV2_ReportFastPcShowProcessInfo_Handler,
		},
		{
			MethodName: "GetMinorityGameWithCache",
			Handler:    _GameServerV2_GetMinorityGameWithCache_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/game-server-v2/game-server-v2.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/game-server-v2/game-server-v2.proto", fileDescriptor_game_server_v2_ff73e8bc42767f9a)
}

var fileDescriptor_game_server_v2_ff73e8bc42767f9a = []byte{
	// 2387 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x5a, 0xcd, 0x6f, 0xdb, 0xc8,
	0x15, 0x37, 0x25, 0xcb, 0x96, 0x9f, 0x63, 0x45, 0x99, 0x58, 0x8e, 0xa2, 0x7c, 0x39, 0x93, 0x38,
	0x71, 0x3e, 0xd6, 0xde, 0x68, 0x9b, 0xb6, 0x68, 0x5a, 0x2c, 0x1c, 0xaf, 0xe2, 0x08, 0x88, 0x1c,
	0x81, 0x72, 0xb2, 0xd9, 0x45, 0x01, 0x96, 0xa6, 0x68, 0x99, 0x1b, 0x49, 0xa4, 0x35, 0xb4, 0x03,
	0x03, 0x3d, 0xb4, 0x28, 0x7a, 0xeb, 0xad, 0x3d, 0x17, 0xfd, 0x0f, 0x8a, 0x5e, 0xdb, 0x4b, 0x81,
	0x9e, 0xf7, 0xd2, 0xff, 0xa2, 0xff, 0x45, 0x31, 0xc3, 0xaf, 0x21, 0x67, 0x48, 0x51, 0xdb, 0x45,
	0xb6, 0x37, 0x71, 0xf8, 0xe6, 0xbd, 0x37, 0xef, 0x6b, 0xde, 0xfb, 0x51, 0xd0, 0x74, 0xdd, 0xed,
	0x93, 0x53, 0xcb, 0x78, 0x4f, 0xac, 0xe1, 0x99, 0x39, 0xd9, 0x1e, 0xe8, 0x23, 0xf3, 0x13, 0x62,
	0x4e, 0xce, 0xcc, 0xc9, 0x27, 0x67, 0xcd, 0xc4, 0xe3, 0x96, 0x33, 0xb1, 0x5d, 0x1b, 0x55, 0xe8,
	0xaa, 0xe6, 0xad, 0x6a, 0x67, 0x4d, 0xfc, 0x10, 0x56, 0xf7, 0x4c, 0xb7, 0x67, 0xe8, 0xe3, 0x3d,
	0x7d, 0x64, 0xf6, 0x3e, 0x58, 0xae, 0x71, 0xac, 0x9a, 0x27, 0x08, 0xc1, 0xbc, 0x7b, 0xee, 0x98,
	0x75, 0x65, 0x5d, 0xd9, 0x5c, 0x51, 0xd9, 0x6f, 0xbc, 0x0d, 0x35, 0x09, 0x2d, 0x71, 0xd0, 0x1a,
	0x2c, 0x10, 0xf6, 0xe4, 0x93, 0xfb, 0x4f, 0xb8, 0x05, 0x57, 0x3a, 0x76, 0xdf, 0x3a, 0x3a, 0x17,
	0xf9, 0xa7, 0x6c, 0x09, 0xe5, 0x16, 0x38, 0xb9, 0x0d, 0xa8, 0xcb, 0xd9, 0x10, 0x07, 0x13, 0x40,
	0x9c, 0x4e, 0xaf, 0x2c, 0xe2, 0x52, 0xee, 0xd7, 0x60, 0x89, 0x9d, 0x73, 0xac, 0x8f, 0xbc, 0x23,
	0x2c, 0xa9, 0x65, 0xba, 0xb0, 0xaf, 0x8f, 0x4c, 0x2a, 0xda, 0x3e, 0x3a, 0x22, 0xa6, 0xeb, 0x0b,
	0xf1, 0x9f, 0xd0, 0x2a, 0x94, 0x86, 0xd6, 0xc8, 0x72, 0xeb, 0x45, 0xb6, 0xec, 0x3d, 0x84, 0x0a,
	0xcd, 0x73, 0x0a, 0xfd, 0xa9, 0x08, 0x17, 0x02, 0x91, 0xed, 0xf1, 0x91, 0x8d, 0x2a, 0x50, 0xb0,
	0xfa, 0xfe, 0x49, 0x0a, 0x56, 0x1f, 0x35, 0x60, 0xe9, 0x54, 0x63, 0x1a, 0x58, 0x7d, 0x5f, 0xca,
	0xe2, 0x29, 0xa3, 0xee, 0xc7, 0x75, 0x2b, 0x26, 0x74, 0x5b, 0x85, 0x12, 0x31, 0xec, 0x49, 0x20,
	0xce, 0x7b, 0x40, 0xb7, 0x60, 0x79, 0xc4, 0x0c, 0xa0, 0xb9, 0xd6, 0xc8, 0xac, 0x97, 0xd8, 0x3b,
	0xf0, 0x96, 0x0e, 0xac, 0x11, 0x23, 0xb0, 0x6c, 0xa2, 0x39, 0xba, 0xf1, 0x5e, 0x1f, 0x98, 0xf5,
	0x85, 0xf5, 0xe2, 0xe6, 0x92, 0x0a, 0x96, 0x4d, 0xba, 0xde, 0x0a, 0xba, 0x0f, 0x17, 0xf5, 0x71,
	0x7f, 0x62, 0x5b, 0xfd, 0x90, 0x68, 0x91, 0x11, 0x55, 0xfc, 0xe5, 0x80, 0xf0, 0x1a, 0x2c, 0x59,
	0x44, 0x3b, 0xb6, 0xfa, 0x7d, 0x73, 0x5c, 0x2f, 0xaf, 0x2b, 0x9b, 0x65, 0xb5, 0x6c, 0x91, 0x97,
	0xec, 0x19, 0x3d, 0x85, 0x2b, 0x8e, 0xa1, 0x39, 0x13, 0xdb, 0x30, 0x09, 0x61, 0x07, 0x08, 0xb9,
	0x2d, 0x31, 0x6e, 0xab, 0x8e, 0xd1, 0xf5, 0xde, 0xd2, 0xd3, 0x04, 0x3c, 0xb7, 0x61, 0xd5, 0x31,
	0x34, 0x72, 0x6c, 0x7f, 0x88, 0xef, 0x01, 0xb6, 0xe7, 0x92, 0x63, 0xf4, 0x8e, 0xed, 0x0f, 0xfc,
	0x86, 0xa7, 0xbe, 0x89, 0x98, 0xe1, 0x97, 0xd7, 0x95, 0xcd, 0x4a, 0xb3, 0xbe, 0x15, 0x0f, 0xdc,
	0x2d, 0x6a, 0xcd, 0x83, 0x73, 0xc7, 0xf4, 0x8c, 0x47, 0x7f, 0x61, 0x1b, 0x2e, 0x0b, 0xb1, 0x40,
	0x1c, 0xf4, 0x1c, 0x2a, 0x21, 0x37, 0x6d, 0x68, 0x11, 0xb7, 0xae, 0xac, 0x17, 0x37, 0x97, 0x9b,
	0xd7, 0x93, 0x2c, 0x79, 0x97, 0xaa, 0x17, 0x02, 0xb6, 0x94, 0x0f, 0xf5, 0x8b, 0x6b, 0xbb, 0xfa,
	0xd0, 0x77, 0xa6, 0xf7, 0x80, 0xff, 0x53, 0x00, 0xb4, 0xd3, 0xef, 0xcf, 0x14, 0x7d, 0x59, 0xa1,
	0x11, 0x7a, 0x9f, 0x86, 0x45, 0x89, 0xf3, 0x3e, 0xef, 0xdc, 0xf9, 0x3c, 0xce, 0x2d, 0x4d, 0x77,
	0xee, 0x42, 0xc2, 0xb9, 0x41, 0xa0, 0x2f, 0x46, 0x81, 0x8e, 0x6e, 0x03, 0x50, 0x87, 0xfb, 0x4c,
	0xcb, 0x94, 0xe9, 0xf3, 0x42, 0x5d, 0x51, 0x97, 0x1c, 0x23, 0xf2, 0xd5, 0x47, 0x89, 0x09, 0xbc,
	0x01, 0x97, 0x05, 0x53, 0x13, 0x27, 0x99, 0x79, 0xf8, 0x2f, 0x05, 0xa8, 0xc5, 0x8b, 0x45, 0xe0,
	0x95, 0x64, 0x8e, 0x86, 0xc6, 0x2e, 0x64, 0x18, 0xbb, 0x98, 0xc7, 0xd8, 0xf3, 0xd3, 0x8d, 0x5d,
	0xca, 0x9f, 0x49, 0x0b, 0xdf, 0xc1, 0x6a, 0x8b, 0x69, 0x99, 0x14, 0x38, 0xb5, 0xcc, 0x55, 0xaf,
	0x3a, 0xac, 0xc9, 0x2c, 0x44, 0x1c, 0xfc, 0x0c, 0x6a, 0x5f, 0x98, 0x43, 0xd3, 0x35, 0xa7, 0xd9,
	0x4e, 0x56, 0xa5, 0xeb, 0xb0, 0x26, 0xdb, 0x4c, 0x1c, 0xfc, 0x0d, 0xdc, 0xf2, 0x04, 0xbe, 0xf1,
	0xe2, 0xfc, 0x85, 0x3d, 0x99, 0x26, 0x20, 0x96, 0x42, 0x85, 0xac, 0x14, 0x2a, 0xc6, 0x52, 0x08,
	0x63, 0x58, 0xcf, 0x96, 0x45, 0x1c, 0xfc, 0x6d, 0x01, 0x50, 0x7b, 0xe4, 0xd8, 0x13, 0x37, 0x56,
	0xc4, 0x33, 0xd3, 0xf6, 0x07, 0x8d, 0x96, 0xc0, 0xdc, 0x0b, 0xa9, 0xa9, 0xb9, 0xf8, 0x43, 0xa6,
	0xe6, 0x3b, 0xa8, 0xc5, 0xcd, 0x19, 0x78, 0xf5, 0x73, 0xdf, 0xa2, 0x5c, 0xd1, 0xc5, 0xc9, 0xa2,
	0x2b, 0x3a, 0xc2, 0xb3, 0x3a, 0xe5, 0x81, 0xbb, 0xb0, 0x16, 0x7f, 0xff, 0x42, 0xb7, 0x86, 0xd3,
	0x9d, 0x75, 0x15, 0xca, 0x47, 0xba, 0x35, 0xd4, 0x46, 0x64, 0xe0, 0x07, 0xcf, 0x22, 0x7d, 0xee,
	0x90, 0x01, 0xfe, 0x26, 0xc9, 0x31, 0xac, 0x24, 0x5d, 0xa8, 0x5a, 0xec, 0x8d, 0xc6, 0xf6, 0x72,
	0x3a, 0xdf, 0xcb, 0xd6, 0x39, 0xd0, 0x49, 0xad, 0x78, 0xfb, 0xe9, 0x33, 0xd3, 0xfe, 0x8f, 0x0a,
	0xac, 0xec, 0x4e, 0x4c, 0xdd, 0x35, 0x29, 0xd9, 0xd4, 0x9b, 0xe1, 0x31, 0x97, 0x54, 0x59, 0x17,
	0x9e, 0xe7, 0xff, 0x1a, 0x2c, 0xb8, 0xfa, 0x61, 0x94, 0x01, 0x25, 0x57, 0x3f, 0x6c, 0xf7, 0xd1,
	0x3a, 0xb0, 0x8b, 0x4b, 0x33, 0xf4, 0x49, 0x9f, 0xbe, 0xf4, 0xfa, 0x08, 0xa0, 0x6b, 0xbb, 0xfa,
	0xa4, 0xdf, 0xee, 0xe3, 0xc7, 0x50, 0xe1, 0x95, 0x22, 0x4e, 0x3c, 0x9f, 0x94, 0x78, 0x3e, 0xfd,
	0x5d, 0x81, 0x7a, 0x44, 0xfe, 0xa5, 0xe5, 0x1e, 0xfb, 0x89, 0x45, 0x8f, 0x93, 0xb1, 0x31, 0x3b,
	0x83, 0x83, 0xa3, 0x16, 0x67, 0x3c, 0xea, 0x7c, 0xd6, 0x51, 0x4b, 0xc2, 0x51, 0xaf, 0xc1, 0xd5,
	0x14, 0xdd, 0x89, 0x83, 0x1f, 0xc1, 0x8a, 0x57, 0xaf, 0x02, 0xe7, 0x64, 0x99, 0xa1, 0x0a, 0x15,
	0x9e, 0x98, 0x38, 0xf8, 0xaf, 0x0a, 0xac, 0x78, 0x95, 0x26, 0xc7, 0xfe, 0xff, 0x0b, 0x6b, 0x54,
	0xa1, 0xc2, 0x2b, 0x4c, 0x1c, 0xea, 0xdc, 0xea, 0x9e, 0xe9, 0xd2, 0xe7, 0xe7, 0xe7, 0xfb, 0x79,
	0x62, 0x74, 0xb6, 0xde, 0x39, 0x66, 0x91, 0xf9, 0xb8, 0x45, 0xae, 0x42, 0x99, 0x1e, 0x83, 0x49,
	0x29, 0x79, 0x39, 0xea, 0xea, 0x87, 0x4c, 0xc8, 0x5d, 0xbf, 0x61, 0x63, 0x47, 0x61, 0x04, 0x0b,
	0x8c, 0xe0, 0x42, 0x70, 0x18, 0x4a, 0x85, 0xff, 0x50, 0x80, 0x72, 0x58, 0xbb, 0x3f, 0x92, 0xed,
	0x13, 0x8d, 0xf8, 0xbc, 0xd0, 0x88, 0xff, 0xaf, 0xa7, 0x12, 0xdc, 0xb8, 0x98, 0x74, 0x23, 0xe7,
	0xff, 0x32, 0xe7, 0x7f, 0xfc, 0x2b, 0xb8, 0x94, 0x70, 0x25, 0x71, 0xc2, 0x46, 0x9a, 0x2b, 0x66,
	0xd2, 0x23, 0xc6, 0xcb, 0x6e, 0x4a, 0xb7, 0xfb, 0x84, 0xb5, 0xd7, 0x9e, 0x84, 0x7c, 0x45, 0x00,
	0x77, 0xd8, 0x74, 0x99, 0xd8, 0xc2, 0xe9, 0x65, 0x8d, 0x8f, 0x6c, 0xb6, 0x67, 0xaa, 0x5e, 0xf4,
	0x17, 0xfe, 0x91, 0xc8, 0x8e, 0x50, 0x15, 0xae, 0x03, 0x84, 0x2a, 0x10, 0x76, 0xce, 0x15, 0xb5,
	0xec, 0xeb, 0x40, 0xf0, 0xb7, 0x0a, 0x9b, 0x5b, 0x93, 0xdb, 0x88, 0x83, 0xbe, 0x86, 0x95, 0x50,
	0x0d, 0x6d, 0xa4, 0x3b, 0xbe, 0x89, 0x7e, 0x2c, 0xa8, 0x22, 0xdb, 0x1d, 0x2a, 0xd8, 0xd1, 0x9d,
	0xd6, 0xd8, 0x9d, 0x9c, 0xab, 0xcb, 0x83, 0x68, 0xa5, 0xf1, 0x0e, 0xaa, 0x49, 0x02, 0x54, 0x85,
	0xe2, 0x7b, 0xf3, 0xdc, 0x37, 0x12, 0xfd, 0x89, 0xb6, 0xa0, 0x74, 0xa6, 0x0f, 0x4f, 0xbd, 0xb8,
	0xcc, 0x32, 0x82, 0x47, 0xf6, 0xb3, 0xc2, 0x4f, 0x15, 0xfc, 0x67, 0x05, 0xaa, 0x1d, 0x6b, 0x6c,
	0x4f, 0x2c, 0xf7, 0x7c, 0xa6, 0x04, 0xb0, 0x0c, 0x7b, 0xcc, 0x27, 0x40, 0xdb, 0xb0, 0xc7, 0xe8,
	0x06, 0x80, 0x27, 0x33, 0x1c, 0x3c, 0x56, 0x54, 0x46, 0xde, 0x63, 0x1d, 0x4e, 0x2c, 0x79, 0xe6,
	0x13, 0xc9, 0x13, 0x85, 0x62, 0x89, 0x0f, 0xc5, 0x55, 0x36, 0x93, 0xf3, 0x2a, 0xaa, 0xe6, 0x09,
	0x36, 0x59, 0xf8, 0xc4, 0x57, 0x89, 0x83, 0xf6, 0x01, 0x8d, 0xfc, 0x35, 0x2d, 0x19, 0xab, 0xeb,
	0x49, 0x73, 0x24, 0x8f, 0xad, 0x56, 0x47, 0xdc, 0x0a, 0xbb, 0x74, 0x8f, 0xd8, 0x48, 0x96, 0x10,
	0x8e, 0xba, 0x70, 0x39, 0x2e, 0x85, 0xba, 0x9c, 0xe4, 0x16, 0x73, 0x69, 0x94, 0x58, 0x21, 0xb8,
	0xc6, 0xe6, 0x91, 0xe4, 0x71, 0xf0, 0x67, 0x50, 0x53, 0xcd, 0x91, 0x7d, 0x66, 0x26, 0x35, 0xc8,
	0x4a, 0x93, 0x3a, 0xac, 0xc9, 0x36, 0x11, 0x07, 0x0f, 0xa0, 0xb6, 0x7b, 0xac, 0x8f, 0x07, 0xb3,
	0xb0, 0xcb, 0xf6, 0xb7, 0xbc, 0x6f, 0xa0, 0x2a, 0xc8, 0x04, 0x11, 0x87, 0x5e, 0xa2, 0x7b, 0xa6,
	0xbb, 0x33, 0x1c, 0xf2, 0x1d, 0xd3, 0xae, 0x3d, 0x3e, 0xa2, 0x4e, 0xfd, 0xad, 0x02, 0x8d, 0xb4,
	0xb7, 0xc4, 0xa1, 0xf5, 0x92, 0x18, 0xfa, 0x58, 0x8b, 0x41, 0x3d, 0x40, 0x97, 0x3c, 0x08, 0x87,
	0xce, 0xe6, 0x8c, 0x20, 0xf2, 0x7c, 0x21, 0xcf, 0x6c, 0x4e, 0x38, 0x61, 0xf8, 0xd7, 0x50, 0x61,
	0xa0, 0x90, 0xa1, 0x8f, 0x55, 0x93, 0x9c, 0x0e, 0xdd, 0xef, 0x69, 0x00, 0x0f, 0xe1, 0x97, 0x58,
	0x6f, 0x3e, 0x1f, 0xef, 0xcd, 0xf1, 0xef, 0x15, 0xb8, 0xa2, 0x9a, 0xb4, 0xef, 0x8b, 0x2b, 0x41,
	0x9d, 0x54, 0x85, 0xe2, 0x69, 0xe8, 0x1e, 0xfa, 0x93, 0xb2, 0xea, 0x9b, 0x67, 0x96, 0x11, 0x0a,
	0x5f, 0x52, 0xcb, 0xde, 0x42, 0xbb, 0x8f, 0x9e, 0xf1, 0xd5, 0xba, 0xc8, 0xec, 0x70, 0x53, 0x56,
	0x10, 0x38, 0x21, 0x51, 0xab, 0xdc, 0x80, 0xba, 0x5c, 0x0d, 0xe2, 0xe0, 0xcd, 0xb0, 0x6e, 0x4e,
	0xd1, 0x0f, 0x9f, 0x84, 0xa5, 0x32, 0xce, 0x22, 0xae, 0xb8, 0x92, 0xa5, 0x78, 0x61, 0x46, 0xc5,
	0x6f, 0xc0, 0x35, 0x2f, 0x82, 0xba, 0x86, 0x2c, 0xc2, 0x7e, 0xa7, 0xc0, 0xf5, 0xf4, 0xf7, 0x1f,
	0x2b, 0xc6, 0x7e, 0x0e, 0x37, 0x9f, 0xeb, 0x2e, 0x87, 0x2e, 0x51, 0xf1, 0x59, 0xd7, 0x60, 0x91,
	0xcf, 0x6f, 0x13, 0x6e, 0x65, 0xee, 0xf6, 0x40, 0xaa, 0x84, 0x92, 0xca, 0xcc, 0x4a, 0xda, 0xb0,
	0x16, 0xde, 0x54, 0x7b, 0x61, 0xc3, 0x40, 0x95, 0xc3, 0xfe, 0x45, 0xe7, 0x77, 0x15, 0xc1, 0x1d,
	0xb9, 0x1c, 0xb5, 0x15, 0x04, 0x3d, 0x81, 0x9a, 0xa1, 0xbb, 0xe6, 0xc0, 0x9e, 0xf8, 0x25, 0x32,
	0x68, 0x41, 0xbc, 0x1c, 0x41, 0xc1, 0xcb, 0x88, 0x33, 0xee, 0xc2, 0x15, 0xa9, 0xc0, 0xef, 0xdc,
	0x79, 0xe0, 0x27, 0x2c, 0x18, 0x5e, 0xe8, 0xc4, 0xed, 0xb2, 0x31, 0xd3, 0x1f, 0x4f, 0x19, 0x95,
	0x87, 0x4a, 0x9f, 0x46, 0xea, 0xb3, 0xdf, 0xf8, 0x9f, 0x45, 0xa8, 0x49, 0x37, 0xa0, 0xd7, 0x1c,
	0x86, 0x5d, 0x69, 0x3e, 0x4b, 0x8a, 0x97, 0x6e, 0x12, 0x57, 0xb9, 0xf6, 0xef, 0x06, 0x80, 0xee,
	0x38, 0xc3, 0x73, 0xbe, 0x95, 0x5c, 0x62, 0x2b, 0xac, 0xb2, 0xdc, 0x86, 0x0b, 0xfc, 0xb0, 0xed,
	0x83, 0xbb, 0xcb, 0x4e, 0x34, 0x62, 0xd3, 0x03, 0xb0, 0xaa, 0xec, 0xdd, 0xa4, 0xec, 0x37, 0xaa,
	0xc3, 0xa2, 0x61, 0x8f, 0x5d, 0x73, 0xec, 0x06, 0x2d, 0xa3, 0xff, 0x88, 0x36, 0xa1, 0xea, 0xc9,
	0x23, 0xae, 0x3e, 0x71, 0xbd, 0x9e, 0x93, 0x36, 0x8d, 0x45, 0xb5, 0xc2, 0xd6, 0x7b, 0x74, 0x99,
	0xf6, 0x9d, 0xf8, 0x1f, 0x8a, 0xc4, 0x08, 0x54, 0x73, 0xf4, 0x00, 0x36, 0x5e, 0xec, 0xf4, 0x0e,
	0xb4, 0xee, 0xae, 0xd6, 0x7b, 0xf9, 0xfa, 0x4b, 0xad, 0xab, 0xbe, 0xde, 0x6d, 0xf5, 0x7a, 0xda,
	0xc1, 0x57, 0xdd, 0x96, 0xf6, 0x66, 0xbf, 0xd7, 0x6d, 0xed, 0xb6, 0x5f, 0xb4, 0x5b, 0x5f, 0x54,
	0xe7, 0xd0, 0x23, 0xb8, 0x9f, 0x4e, 0xda, 0x7a, 0x77, 0xd0, 0x52, 0xf7, 0x77, 0x5e, 0x69, 0x7b,
	0x3b, 0x9d, 0x56, 0x55, 0x41, 0x1b, 0x70, 0x3b, 0x9d, 0xf8, 0xe0, 0xc0, 0x23, 0x2b, 0xa0, 0x3b,
	0x70, 0x2b, 0x9d, 0xac, 0xf3, 0xa6, 0xd7, 0xde, 0xad, 0x16, 0xb1, 0x09, 0x57, 0xa5, 0xce, 0x68,
	0xbb, 0xe6, 0x08, 0xbd, 0x8c, 0xac, 0xca, 0x05, 0xd3, 0x46, 0x2e, 0x6f, 0x86, 0xc6, 0x67, 0xc1,
	0xf5, 0x6f, 0xaf, 0x94, 0xa4, 0x44, 0x17, 0x71, 0x50, 0x07, 0x4a, 0x96, 0x6b, 0x8e, 0x82, 0xbe,
	0xe0, 0x27, 0x92, 0x3e, 0x30, 0x75, 0xf3, 0x16, 0x55, 0x96, 0x78, 0x8d, 0xa0, 0xc7, 0xa5, 0x61,
	0x00, 0x44, 0x8b, 0x92, 0xe6, 0xef, 0xf3, 0x78, 0xf3, 0xf7, 0x20, 0xd7, 0x91, 0x28, 0x47, 0xbe,
	0x1b, 0xd4, 0xe0, 0xa6, 0x57, 0xf7, 0x53, 0x93, 0xe6, 0x17, 0x30, 0x4f, 0xf5, 0xf1, 0xfb, 0xec,
	0x19, 0xa4, 0xb0, 0x6d, 0xf8, 0x36, 0xdc, 0xca, 0x14, 0x40, 0x9c, 0x87, 0x63, 0x6f, 0x12, 0x3b,
	0xf0, 0xc6, 0xd3, 0x4b, 0xd4, 0xf5, 0x9e, 0x83, 0xdb, 0xfb, 0x6f, 0x77, 0x5e, 0xb5, 0x69, 0x6c,
	0xad, 0x01, 0x8a, 0x96, 0x83, 0x58, 0xaa, 0x2a, 0x71, 0xf2, 0x28, 0x6c, 0xae, 0x43, 0x3d, 0x5a,
	0xee, 0xee, 0x6a, 0xaf, 0x0f, 0x5e, 0xb6, 0x54, 0x6d, 0xa7, 0xdb, 0x7d, 0xf5, 0x55, 0xb5, 0xd8,
	0xfc, 0xd7, 0x45, 0x58, 0x0e, 0xca, 0x68, 0x67, 0x30, 0x41, 0x87, 0x6c, 0xf6, 0x89, 0x7f, 0x1d,
	0x42, 0x77, 0x25, 0xde, 0x13, 0xbe, 0x43, 0x35, 0x36, 0x72, 0x50, 0x11, 0x07, 0xcf, 0xa1, 0xf7,
	0xb0, 0x2a, 0xfb, 0x08, 0x85, 0xee, 0x0b, 0xcd, 0xa3, 0xfc, 0x8b, 0x57, 0x63, 0x33, 0x1f, 0x21,
	0x13, 0xf6, 0x4b, 0xb8, 0x98, 0x00, 0xbb, 0x91, 0x00, 0x9c, 0x89, 0x1f, 0x1e, 0x1a, 0x77, 0xa6,
	0xd2, 0x30, 0xee, 0x26, 0x20, 0x11, 0x00, 0x46, 0x1b, 0xd9, 0xfa, 0x05, 0x32, 0xee, 0xe5, 0x21,
	0x0b, 0xc4, 0x88, 0x80, 0xb0, 0x28, 0x46, 0x8a, 0x38, 0x8b, 0x62, 0x52, 0xb0, 0x65, 0x66, 0xab,
	0xc4, 0x57, 0x1f, 0xd1, 0x56, 0xe2, 0x27, 0x42, 0xd1, 0x56, 0x92, 0x4f, 0x47, 0x78, 0x0e, 0xd1,
	0xf6, 0x23, 0x0b, 0x50, 0x46, 0xdb, 0x72, 0x7b, 0xa4, 0x42, 0xdd, 0x8d, 0x4f, 0x67, 0xdb, 0x10,
	0x98, 0x52, 0x44, 0x2d, 0x45, 0x53, 0x4a, 0x51, 0xd8, 0xc6, 0xbd, 0x3c, 0x64, 0x4c, 0x4c, 0x07,
	0x20, 0xc2, 0xcb, 0xd0, 0x8d, 0xe4, 0xbe, 0x18, 0x96, 0xd9, 0xb8, 0x99, 0xf5, 0x9a, 0xb1, 0x1b,
	0x43, 0x4d, 0x0a, 0xbf, 0xa1, 0xcd, 0xf4, 0xad, 0x71, 0x84, 0xb1, 0xf1, 0x20, 0x27, 0x65, 0xa0,
	0x7e, 0x04, 0xd2, 0x89, 0xea, 0xc7, 0xd0, 0x3e, 0x51, 0xfd, 0x04, 0xbe, 0xc7, 0xd8, 0x45, 0x78,
	0x99, 0xc8, 0x2e, 0x06, 0xfe, 0x89, 0xec, 0x12, 0x50, 0xdb, 0x1c, 0x7a, 0x0b, 0x2b, 0x31, 0x80,
	0x06, 0xad, 0xa7, 0xc2, 0x0c, 0x3e, 0x14, 0xd7, 0xb8, 0x3d, 0x85, 0x82, 0xf1, 0xd5, 0x38, 0x0c,
	0x2f, 0x30, 0xf0, 0x9d, 0x69, 0x08, 0x06, 0xe5, 0x7e, 0x77, 0x3a, 0x11, 0x13, 0x70, 0xc8, 0x21,
	0x4b, 0x01, 0x00, 0x82, 0xee, 0xe6, 0xc0, 0x48, 0xe4, 0xd5, 0x55, 0x44, 0x52, 0xf0, 0x1c, 0x3a,
	0xe6, 0xb0, 0xa5, 0xa8, 0x93, 0x44, 0xf7, 0x52, 0xf7, 0xc7, 0xfa, 0xdb, 0xc6, 0xfd, 0x5c, 0x74,
	0x5c, 0xb9, 0xe0, 0xa7, 0x5c, 0x69, 0xb9, 0x48, 0xcc, 0xdb, 0xd2, 0x72, 0x21, 0x8c, 0xca, 0x41,
	0xe1, 0xce, 0xe6, 0x2e, 0xc2, 0x13, 0xd2, 0xc2, 0x2d, 0xe1, 0x6e, 0x02, 0x12, 0x71, 0x02, 0xb1,
	0x0c, 0x48, 0x01, 0x08, 0xb1, 0x0c, 0xa4, 0x40, 0x0e, 0x4c, 0x8c, 0x88, 0x05, 0x88, 0x62, 0xa4,
	0xc0, 0x84, 0x28, 0x26, 0x05, 0x56, 0x98, 0x6b, 0xfe, 0x6d, 0x11, 0x2e, 0xb0, 0x9b, 0x8f, 0x91,
	0xbe, 0x6d, 0xa2, 0x13, 0x36, 0xbf, 0x48, 0xb0, 0x04, 0xf4, 0x40, 0x62, 0x7d, 0x39, 0x22, 0xd1,
	0x78, 0x98, 0x97, 0x94, 0x1d, 0xf5, 0x37, 0x0a, 0x5c, 0xcb, 0x18, 0xcd, 0xd0, 0x56, 0x92, 0x5b,
	0xf6, 0x14, 0xd8, 0xd8, 0x9e, 0x89, 0x3e, 0x68, 0x2c, 0x64, 0x83, 0xbb, 0xd8, 0x58, 0xa4, 0xa0,
	0x0c, 0x62, 0x63, 0x91, 0x8a, 0x03, 0xf0, 0xb9, 0xcc, 0x49, 0x4a, 0xcb, 0xe5, 0xb8, 0x98, 0x8d,
	0x1c, 0x54, 0x4c, 0xc6, 0x07, 0xa8, 0xa7, 0x0d, 0xec, 0xe8, 0x91, 0xdc, 0x3b, 0xd2, 0xd1, 0xbf,
	0xf1, 0x38, 0x3f, 0x31, 0x27, 0x58, 0x3e, 0x0b, 0x3e, 0xca, 0xdf, 0xcb, 0xcb, 0x05, 0xa7, 0x76,
	0xbf, 0x7e, 0x14, 0x65, 0xf4, 0xc8, 0x62, 0x14, 0x65, 0x77, 0xec, 0x62, 0x14, 0x4d, 0x69, 0xc0,
	0x59, 0xce, 0xd6, 0x13, 0x15, 0x89, 0xde, 0x8f, 0xbb, 0xba, 0x71, 0xfc, 0x7d, 0xd6, 0xb7, 0xe7,
	0xcd, 0xaf, 0x3f, 0x1d, 0xd8, 0x43, 0x7d, 0x3c, 0xd8, 0x7a, 0xda, 0x74, 0xdd, 0x2d, 0xc3, 0x1e,
	0x6d, 0xb3, 0xff, 0x95, 0x19, 0xf6, 0x70, 0x9b, 0xee, 0xb5, 0x0c, 0x93, 0x24, 0xfe, 0x78, 0x76,
	0xb8, 0xc0, 0x28, 0x3e, 0xfb, 0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x57, 0x76, 0x79, 0x1d, 0xaf,
	0x26, 0x00, 0x00,
}
