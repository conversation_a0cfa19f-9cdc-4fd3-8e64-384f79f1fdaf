// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/ugc-community-http-logic/ugc-community-http-logic.proto

package ugc_community_http_logic // import "golang.52tt.com/protocol/services/ugc-community-http-logic/ugc-community-http-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import ugc_community "golang.52tt.com/protocol/services/ugc-community"
import ugc_community_middle "golang.52tt.com/protocol/services/ugc-community-middle"
import _ "google.golang.org/genproto/googleapis/api/annotations"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PostSource int32

const (
	PostSource_SOURCE_AI_DISTRICT_UNSPECIFIED    PostSource = 0
	PostSource_SOURCE_AI_DISTRICT_RECOMMENDATION PostSource = 1
	PostSource_SOURCE_AI_DISTRICT_PERSON         PostSource = 2
)

var PostSource_name = map[int32]string{
	0: "SOURCE_AI_DISTRICT_UNSPECIFIED",
	1: "SOURCE_AI_DISTRICT_RECOMMENDATION",
	2: "SOURCE_AI_DISTRICT_PERSON",
}
var PostSource_value = map[string]int32{
	"SOURCE_AI_DISTRICT_UNSPECIFIED":    0,
	"SOURCE_AI_DISTRICT_RECOMMENDATION": 1,
	"SOURCE_AI_DISTRICT_PERSON":         2,
}

func (x PostSource) String() string {
	return proto.EnumName(PostSource_name, int32(x))
}
func (PostSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{0}
}

type AttitudeAction int32

const (
	AttitudeAction_ATTITUDE_ACTION_UNSPECIFIED AttitudeAction = 0
	AttitudeAction_ATTITUDE_ACTION_LIKE        AttitudeAction = 1
	AttitudeAction_ATTITUDE_ACTION_DISLIKE     AttitudeAction = 2
)

var AttitudeAction_name = map[int32]string{
	0: "ATTITUDE_ACTION_UNSPECIFIED",
	1: "ATTITUDE_ACTION_LIKE",
	2: "ATTITUDE_ACTION_DISLIKE",
}
var AttitudeAction_value = map[string]int32{
	"ATTITUDE_ACTION_UNSPECIFIED": 0,
	"ATTITUDE_ACTION_LIKE":        1,
	"ATTITUDE_ACTION_DISLIKE":     2,
}

func (x AttitudeAction) String() string {
	return proto.EnumName(AttitudeAction_name, int32(x))
}
func (AttitudeAction) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{1}
}

type ObjectType int32

const (
	ObjectType_OBJECT_TYPE_UNSPECIFIED ObjectType = 0
	ObjectType_OBJECT_TYPE_POST        ObjectType = 1
	ObjectType_OBJECT_TYPE_COMMENT     ObjectType = 2
)

var ObjectType_name = map[int32]string{
	0: "OBJECT_TYPE_UNSPECIFIED",
	1: "OBJECT_TYPE_POST",
	2: "OBJECT_TYPE_COMMENT",
}
var ObjectType_value = map[string]int32{
	"OBJECT_TYPE_UNSPECIFIED": 0,
	"OBJECT_TYPE_POST":        1,
	"OBJECT_TYPE_COMMENT":     2,
}

func (x ObjectType) String() string {
	return proto.EnumName(ObjectType_name, int32(x))
}
func (ObjectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{2}
}

// 附件类型
type Attachment_Type int32

const (
	Attachment_TYPE_UNSPECIFIED Attachment_Type = 0
	// 图片
	Attachment_TYPE_IMAGE Attachment_Type = 1
)

var Attachment_Type_name = map[int32]string{
	0: "TYPE_UNSPECIFIED",
	1: "TYPE_IMAGE",
}
var Attachment_Type_value = map[string]int32{
	"TYPE_UNSPECIFIED": 0,
	"TYPE_IMAGE":       1,
}

func (x Attachment_Type) String() string {
	return proto.EnumName(Attachment_Type_name, int32(x))
}
func (Attachment_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{1, 0}
}

type BaseRequest struct {
	DeviceId             string   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,4,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseRequest) Reset()         { *m = BaseRequest{} }
func (m *BaseRequest) String() string { return proto.CompactTextString(m) }
func (*BaseRequest) ProtoMessage()    {}
func (*BaseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{0}
}
func (m *BaseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseRequest.Unmarshal(m, b)
}
func (m *BaseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseRequest.Marshal(b, m, deterministic)
}
func (dst *BaseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseRequest.Merge(dst, src)
}
func (m *BaseRequest) XXX_Size() int {
	return xxx_messageInfo_BaseRequest.Size(m)
}
func (m *BaseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BaseRequest proto.InternalMessageInfo

func (m *BaseRequest) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *BaseRequest) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *BaseRequest) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *BaseRequest) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

// 附件
type Attachment struct {
	// 类型
	Type Attachment_Type `protobuf:"varint,1,opt,name=type,proto3,enum=ugc_community_http_logic.Attachment_Type" json:"type,omitempty"`
	// obs key
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	Size                 string   `protobuf:"bytes,4,opt,name=size,proto3" json:"size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Attachment) Reset()         { *m = Attachment{} }
func (m *Attachment) String() string { return proto.CompactTextString(m) }
func (*Attachment) ProtoMessage()    {}
func (*Attachment) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{1}
}
func (m *Attachment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Attachment.Unmarshal(m, b)
}
func (m *Attachment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Attachment.Marshal(b, m, deterministic)
}
func (dst *Attachment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Attachment.Merge(dst, src)
}
func (m *Attachment) XXX_Size() int {
	return xxx_messageInfo_Attachment.Size(m)
}
func (m *Attachment) XXX_DiscardUnknown() {
	xxx_messageInfo_Attachment.DiscardUnknown(m)
}

var xxx_messageInfo_Attachment proto.InternalMessageInfo

func (m *Attachment) GetType() Attachment_Type {
	if m != nil {
		return m.Type
	}
	return Attachment_TYPE_UNSPECIFIED
}

func (m *Attachment) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *Attachment) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *Attachment) GetSize() string {
	if m != nil {
		return m.Size
	}
	return ""
}

// 话题
type TopicInfo struct {
	// 话题ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 话题类型
	Type uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	// 话题名称
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicInfo) Reset()         { *m = TopicInfo{} }
func (m *TopicInfo) String() string { return proto.CompactTextString(m) }
func (*TopicInfo) ProtoMessage()    {}
func (*TopicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{2}
}
func (m *TopicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicInfo.Unmarshal(m, b)
}
func (m *TopicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicInfo.Marshal(b, m, deterministic)
}
func (dst *TopicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicInfo.Merge(dst, src)
}
func (m *TopicInfo) XXX_Size() int {
	return xxx_messageInfo_TopicInfo.Size(m)
}
func (m *TopicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopicInfo proto.InternalMessageInfo

func (m *TopicInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *TopicInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *TopicInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type AigcCommunityPost struct {
	RoleId               uint32                                        `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	RoleType             uint32                                        `protobuf:"varint,2,opt,name=role_type,json=roleType,proto3" json:"role_type,omitempty"`
	RoleName             string                                        `protobuf:"bytes,3,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	RoleAvatar           string                                        `protobuf:"bytes,4,opt,name=role_avatar,json=roleAvatar,proto3" json:"role_avatar,omitempty"`
	RoleCharacter        string                                        `protobuf:"bytes,5,opt,name=role_character,json=roleCharacter,proto3" json:"role_character,omitempty"`
	ChatRecords          []*ugc_community.AigcCommunityPost_ChatRecord `protobuf:"bytes,6,rep,name=chat_records,json=chatRecords,proto3" json:"chat_records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *AigcCommunityPost) Reset()         { *m = AigcCommunityPost{} }
func (m *AigcCommunityPost) String() string { return proto.CompactTextString(m) }
func (*AigcCommunityPost) ProtoMessage()    {}
func (*AigcCommunityPost) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{3}
}
func (m *AigcCommunityPost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AigcCommunityPost.Unmarshal(m, b)
}
func (m *AigcCommunityPost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AigcCommunityPost.Marshal(b, m, deterministic)
}
func (dst *AigcCommunityPost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AigcCommunityPost.Merge(dst, src)
}
func (m *AigcCommunityPost) XXX_Size() int {
	return xxx_messageInfo_AigcCommunityPost.Size(m)
}
func (m *AigcCommunityPost) XXX_DiscardUnknown() {
	xxx_messageInfo_AigcCommunityPost.DiscardUnknown(m)
}

var xxx_messageInfo_AigcCommunityPost proto.InternalMessageInfo

func (m *AigcCommunityPost) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

func (m *AigcCommunityPost) GetRoleType() uint32 {
	if m != nil {
		return m.RoleType
	}
	return 0
}

func (m *AigcCommunityPost) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

func (m *AigcCommunityPost) GetRoleAvatar() string {
	if m != nil {
		return m.RoleAvatar
	}
	return ""
}

func (m *AigcCommunityPost) GetRoleCharacter() string {
	if m != nil {
		return m.RoleCharacter
	}
	return ""
}

func (m *AigcCommunityPost) GetChatRecords() []*ugc_community.AigcCommunityPost_ChatRecord {
	if m != nil {
		return m.ChatRecords
	}
	return nil
}

type GeneratePublishPostParamRequest struct {
	BaseReq *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 需要上传的附件列表 不需要传key
	Attachments          []*Attachment `protobuf:"bytes,2,rep,name=attachments,proto3" json:"attachments,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GeneratePublishPostParamRequest) Reset()         { *m = GeneratePublishPostParamRequest{} }
func (m *GeneratePublishPostParamRequest) String() string { return proto.CompactTextString(m) }
func (*GeneratePublishPostParamRequest) ProtoMessage()    {}
func (*GeneratePublishPostParamRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{4}
}
func (m *GeneratePublishPostParamRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GeneratePublishPostParamRequest.Unmarshal(m, b)
}
func (m *GeneratePublishPostParamRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GeneratePublishPostParamRequest.Marshal(b, m, deterministic)
}
func (dst *GeneratePublishPostParamRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeneratePublishPostParamRequest.Merge(dst, src)
}
func (m *GeneratePublishPostParamRequest) XXX_Size() int {
	return xxx_messageInfo_GeneratePublishPostParamRequest.Size(m)
}
func (m *GeneratePublishPostParamRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GeneratePublishPostParamRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GeneratePublishPostParamRequest proto.InternalMessageInfo

func (m *GeneratePublishPostParamRequest) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GeneratePublishPostParamRequest) GetAttachments() []*Attachment {
	if m != nil {
		return m.Attachments
	}
	return nil
}

type GeneratePublishPostParamResponse struct {
	// 帖子ID，发布时带上
	Id       string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ObsApp   string `protobuf:"bytes,2,opt,name=obs_app,json=obsApp,proto3" json:"obs_app,omitempty"`
	ObsScope string `protobuf:"bytes,3,opt,name=obs_scope,json=obsScope,proto3" json:"obs_scope,omitempty"`
	// 带有key的附件列表
	Attachments          []*Attachment `protobuf:"bytes,4,rep,name=attachments,proto3" json:"attachments,omitempty"`
	ObsToken             string        `protobuf:"bytes,5,opt,name=obs_token,json=obsToken,proto3" json:"obs_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GeneratePublishPostParamResponse) Reset()         { *m = GeneratePublishPostParamResponse{} }
func (m *GeneratePublishPostParamResponse) String() string { return proto.CompactTextString(m) }
func (*GeneratePublishPostParamResponse) ProtoMessage()    {}
func (*GeneratePublishPostParamResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{5}
}
func (m *GeneratePublishPostParamResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GeneratePublishPostParamResponse.Unmarshal(m, b)
}
func (m *GeneratePublishPostParamResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GeneratePublishPostParamResponse.Marshal(b, m, deterministic)
}
func (dst *GeneratePublishPostParamResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeneratePublishPostParamResponse.Merge(dst, src)
}
func (m *GeneratePublishPostParamResponse) XXX_Size() int {
	return xxx_messageInfo_GeneratePublishPostParamResponse.Size(m)
}
func (m *GeneratePublishPostParamResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GeneratePublishPostParamResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GeneratePublishPostParamResponse proto.InternalMessageInfo

func (m *GeneratePublishPostParamResponse) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GeneratePublishPostParamResponse) GetObsApp() string {
	if m != nil {
		return m.ObsApp
	}
	return ""
}

func (m *GeneratePublishPostParamResponse) GetObsScope() string {
	if m != nil {
		return m.ObsScope
	}
	return ""
}

func (m *GeneratePublishPostParamResponse) GetAttachments() []*Attachment {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *GeneratePublishPostParamResponse) GetObsToken() string {
	if m != nil {
		return m.ObsToken
	}
	return ""
}

type PublishPostRequest struct {
	BaseReq *BaseRequest             `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Post    *PublishPostRequest_Post `protobuf:"bytes,2,opt,name=post,proto3" json:"post,omitempty"`
	// 发帖引导任务token
	TaskToken            string   `protobuf:"bytes,3,opt,name=task_token,json=taskToken,proto3" json:"task_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PublishPostRequest) Reset()         { *m = PublishPostRequest{} }
func (m *PublishPostRequest) String() string { return proto.CompactTextString(m) }
func (*PublishPostRequest) ProtoMessage()    {}
func (*PublishPostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{6}
}
func (m *PublishPostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishPostRequest.Unmarshal(m, b)
}
func (m *PublishPostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishPostRequest.Marshal(b, m, deterministic)
}
func (dst *PublishPostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishPostRequest.Merge(dst, src)
}
func (m *PublishPostRequest) XXX_Size() int {
	return xxx_messageInfo_PublishPostRequest.Size(m)
}
func (m *PublishPostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishPostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PublishPostRequest proto.InternalMessageInfo

func (m *PublishPostRequest) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PublishPostRequest) GetPost() *PublishPostRequest_Post {
	if m != nil {
		return m.Post
	}
	return nil
}

func (m *PublishPostRequest) GetTaskToken() string {
	if m != nil {
		return m.TaskToken
	}
	return ""
}

type PublishPostRequest_Post struct {
	// 帖子ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 帖子类型
	Type ugc_community.PostType `protobuf:"varint,2,opt,name=type,proto3,enum=ugc_community.PostType" json:"type,omitempty"`
	// 帖子状态
	State ugc_community.PostState `protobuf:"varint,3,opt,name=state,proto3,enum=ugc_community.PostState" json:"state,omitempty"`
	// 发布来源
	Origin ugc_community.PostOrigin `protobuf:"varint,4,opt,name=origin,proto3,enum=ugc_community.PostOrigin" json:"origin,omitempty"`
	// 内容
	Content string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	// 附件列表
	Attachments []*Attachment `protobuf:"bytes,6,rep,name=attachments,proto3" json:"attachments,omitempty"`
	// 业务类型
	BizType ugc_community.PostBizType `protobuf:"varint,7,opt,name=biz_type,json=bizType,proto3,enum=ugc_community.PostBizType" json:"biz_type,omitempty"`
	// 业务数据 角色id放里面
	BizData map[string]string `protobuf:"bytes,8,rep,name=biz_data,json=bizData,proto3" json:"biz_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 话题ID
	TopicIdList []string `protobuf:"bytes,9,rep,name=topic_id_list,json=topicIdList,proto3" json:"topic_id_list,omitempty"`
	// 业务序列化数据，取代biz_data
	BizBytes             []byte   `protobuf:"bytes,10,opt,name=biz_bytes,json=bizBytes,proto3" json:"biz_bytes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PublishPostRequest_Post) Reset()         { *m = PublishPostRequest_Post{} }
func (m *PublishPostRequest_Post) String() string { return proto.CompactTextString(m) }
func (*PublishPostRequest_Post) ProtoMessage()    {}
func (*PublishPostRequest_Post) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{6, 0}
}
func (m *PublishPostRequest_Post) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishPostRequest_Post.Unmarshal(m, b)
}
func (m *PublishPostRequest_Post) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishPostRequest_Post.Marshal(b, m, deterministic)
}
func (dst *PublishPostRequest_Post) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishPostRequest_Post.Merge(dst, src)
}
func (m *PublishPostRequest_Post) XXX_Size() int {
	return xxx_messageInfo_PublishPostRequest_Post.Size(m)
}
func (m *PublishPostRequest_Post) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishPostRequest_Post.DiscardUnknown(m)
}

var xxx_messageInfo_PublishPostRequest_Post proto.InternalMessageInfo

func (m *PublishPostRequest_Post) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *PublishPostRequest_Post) GetType() ugc_community.PostType {
	if m != nil {
		return m.Type
	}
	return ugc_community.PostType_POST_TYPE_UNSPECIFED
}

func (m *PublishPostRequest_Post) GetState() ugc_community.PostState {
	if m != nil {
		return m.State
	}
	return ugc_community.PostState_POST_STATE_UNSPECIFIED
}

func (m *PublishPostRequest_Post) GetOrigin() ugc_community.PostOrigin {
	if m != nil {
		return m.Origin
	}
	return ugc_community.PostOrigin_POST_ORIGIN_UNSPECIFIED
}

func (m *PublishPostRequest_Post) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PublishPostRequest_Post) GetAttachments() []*Attachment {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *PublishPostRequest_Post) GetBizType() ugc_community.PostBizType {
	if m != nil {
		return m.BizType
	}
	return ugc_community.PostBizType_POST_BIZ_TYPE_UNSPECIFIED
}

func (m *PublishPostRequest_Post) GetBizData() map[string]string {
	if m != nil {
		return m.BizData
	}
	return nil
}

func (m *PublishPostRequest_Post) GetTopicIdList() []string {
	if m != nil {
		return m.TopicIdList
	}
	return nil
}

func (m *PublishPostRequest_Post) GetBizBytes() []byte {
	if m != nil {
		return m.BizBytes
	}
	return nil
}

type PublishPostResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PublishPostResponse) Reset()         { *m = PublishPostResponse{} }
func (m *PublishPostResponse) String() string { return proto.CompactTextString(m) }
func (*PublishPostResponse) ProtoMessage()    {}
func (*PublishPostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{7}
}
func (m *PublishPostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishPostResponse.Unmarshal(m, b)
}
func (m *PublishPostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishPostResponse.Marshal(b, m, deterministic)
}
func (dst *PublishPostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishPostResponse.Merge(dst, src)
}
func (m *PublishPostResponse) XXX_Size() int {
	return xxx_messageInfo_PublishPostResponse.Size(m)
}
func (m *PublishPostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishPostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PublishPostResponse proto.InternalMessageInfo

type DeletePostRequest struct {
	BaseReq *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 帖子ID
	Id                   string   `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePostRequest) Reset()         { *m = DeletePostRequest{} }
func (m *DeletePostRequest) String() string { return proto.CompactTextString(m) }
func (*DeletePostRequest) ProtoMessage()    {}
func (*DeletePostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{8}
}
func (m *DeletePostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePostRequest.Unmarshal(m, b)
}
func (m *DeletePostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePostRequest.Marshal(b, m, deterministic)
}
func (dst *DeletePostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePostRequest.Merge(dst, src)
}
func (m *DeletePostRequest) XXX_Size() int {
	return xxx_messageInfo_DeletePostRequest.Size(m)
}
func (m *DeletePostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePostRequest proto.InternalMessageInfo

func (m *DeletePostRequest) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DeletePostRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DeletePostResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePostResponse) Reset()         { *m = DeletePostResponse{} }
func (m *DeletePostResponse) String() string { return proto.CompactTextString(m) }
func (*DeletePostResponse) ProtoMessage()    {}
func (*DeletePostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{9}
}
func (m *DeletePostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePostResponse.Unmarshal(m, b)
}
func (m *DeletePostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePostResponse.Marshal(b, m, deterministic)
}
func (dst *DeletePostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePostResponse.Merge(dst, src)
}
func (m *DeletePostResponse) XXX_Size() int {
	return xxx_messageInfo_DeletePostResponse.Size(m)
}
func (m *DeletePostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePostResponse proto.InternalMessageInfo

type CommentSendRequest struct {
	BaseReq *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 帖子id
	PostId string `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	// 首评论id
	RootParentId string `protobuf:"bytes,3,opt,name=root_parent_id,json=rootParentId,proto3" json:"root_parent_id,omitempty"`
	// 回复评论id
	ParentId string `protobuf:"bytes,4,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// 回复内容
	Content              string   `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentSendRequest) Reset()         { *m = CommentSendRequest{} }
func (m *CommentSendRequest) String() string { return proto.CompactTextString(m) }
func (*CommentSendRequest) ProtoMessage()    {}
func (*CommentSendRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{10}
}
func (m *CommentSendRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentSendRequest.Unmarshal(m, b)
}
func (m *CommentSendRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentSendRequest.Marshal(b, m, deterministic)
}
func (dst *CommentSendRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentSendRequest.Merge(dst, src)
}
func (m *CommentSendRequest) XXX_Size() int {
	return xxx_messageInfo_CommentSendRequest.Size(m)
}
func (m *CommentSendRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentSendRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommentSendRequest proto.InternalMessageInfo

func (m *CommentSendRequest) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CommentSendRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *CommentSendRequest) GetRootParentId() string {
	if m != nil {
		return m.RootParentId
	}
	return ""
}

func (m *CommentSendRequest) GetParentId() string {
	if m != nil {
		return m.ParentId
	}
	return ""
}

func (m *CommentSendRequest) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type CommentSendResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentSendResponse) Reset()         { *m = CommentSendResponse{} }
func (m *CommentSendResponse) String() string { return proto.CompactTextString(m) }
func (*CommentSendResponse) ProtoMessage()    {}
func (*CommentSendResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{11}
}
func (m *CommentSendResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentSendResponse.Unmarshal(m, b)
}
func (m *CommentSendResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentSendResponse.Marshal(b, m, deterministic)
}
func (dst *CommentSendResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentSendResponse.Merge(dst, src)
}
func (m *CommentSendResponse) XXX_Size() int {
	return xxx_messageInfo_CommentSendResponse.Size(m)
}
func (m *CommentSendResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentSendResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommentSendResponse proto.InternalMessageInfo

type CommentFetchRequest struct {
	BaseReq *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 帖子id
	PostId string `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	// 上一页最后的首次评id，分页使用
	LastCommentId        string   `protobuf:"bytes,3,opt,name=last_comment_id,json=lastCommentId,proto3" json:"last_comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentFetchRequest) Reset()         { *m = CommentFetchRequest{} }
func (m *CommentFetchRequest) String() string { return proto.CompactTextString(m) }
func (*CommentFetchRequest) ProtoMessage()    {}
func (*CommentFetchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{12}
}
func (m *CommentFetchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentFetchRequest.Unmarshal(m, b)
}
func (m *CommentFetchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentFetchRequest.Marshal(b, m, deterministic)
}
func (dst *CommentFetchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentFetchRequest.Merge(dst, src)
}
func (m *CommentFetchRequest) XXX_Size() int {
	return xxx_messageInfo_CommentFetchRequest.Size(m)
}
func (m *CommentFetchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentFetchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommentFetchRequest proto.InternalMessageInfo

func (m *CommentFetchRequest) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CommentFetchRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *CommentFetchRequest) GetLastCommentId() string {
	if m != nil {
		return m.LastCommentId
	}
	return ""
}

type CommentFetchResponse struct {
	Comments             []*ugc_community_middle.CommentItem `protobuf:"bytes,1,rep,name=comments,proto3" json:"comments,omitempty"`
	IsLoadFinish         bool                                `protobuf:"varint,2,opt,name=is_load_finish,json=isLoadFinish,proto3" json:"is_load_finish,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *CommentFetchResponse) Reset()         { *m = CommentFetchResponse{} }
func (m *CommentFetchResponse) String() string { return proto.CompactTextString(m) }
func (*CommentFetchResponse) ProtoMessage()    {}
func (*CommentFetchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{13}
}
func (m *CommentFetchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentFetchResponse.Unmarshal(m, b)
}
func (m *CommentFetchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentFetchResponse.Marshal(b, m, deterministic)
}
func (dst *CommentFetchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentFetchResponse.Merge(dst, src)
}
func (m *CommentFetchResponse) XXX_Size() int {
	return xxx_messageInfo_CommentFetchResponse.Size(m)
}
func (m *CommentFetchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentFetchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommentFetchResponse proto.InternalMessageInfo

func (m *CommentFetchResponse) GetComments() []*ugc_community_middle.CommentItem {
	if m != nil {
		return m.Comments
	}
	return nil
}

func (m *CommentFetchResponse) GetIsLoadFinish() bool {
	if m != nil {
		return m.IsLoadFinish
	}
	return false
}

// 删除评论
type CommentDeleteRequest struct {
	BaseReq              *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	CommentId            string       `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CommentDeleteRequest) Reset()         { *m = CommentDeleteRequest{} }
func (m *CommentDeleteRequest) String() string { return proto.CompactTextString(m) }
func (*CommentDeleteRequest) ProtoMessage()    {}
func (*CommentDeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{14}
}
func (m *CommentDeleteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentDeleteRequest.Unmarshal(m, b)
}
func (m *CommentDeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentDeleteRequest.Marshal(b, m, deterministic)
}
func (dst *CommentDeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentDeleteRequest.Merge(dst, src)
}
func (m *CommentDeleteRequest) XXX_Size() int {
	return xxx_messageInfo_CommentDeleteRequest.Size(m)
}
func (m *CommentDeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentDeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommentDeleteRequest proto.InternalMessageInfo

func (m *CommentDeleteRequest) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CommentDeleteRequest) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

type CommentDeleteResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommentDeleteResponse) Reset()         { *m = CommentDeleteResponse{} }
func (m *CommentDeleteResponse) String() string { return proto.CompactTextString(m) }
func (*CommentDeleteResponse) ProtoMessage()    {}
func (*CommentDeleteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{15}
}
func (m *CommentDeleteResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentDeleteResponse.Unmarshal(m, b)
}
func (m *CommentDeleteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentDeleteResponse.Marshal(b, m, deterministic)
}
func (dst *CommentDeleteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentDeleteResponse.Merge(dst, src)
}
func (m *CommentDeleteResponse) XXX_Size() int {
	return xxx_messageInfo_CommentDeleteResponse.Size(m)
}
func (m *CommentDeleteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentDeleteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommentDeleteResponse proto.InternalMessageInfo

// ugc中的用户信息
type UserUgcCommunity struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Alias                string   `protobuf:"bytes,3,opt,name=alias,proto3" json:"alias,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Gender               uint32   `protobuf:"varint,5,opt,name=gender,proto3" json:"gender,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserUgcCommunity) Reset()         { *m = UserUgcCommunity{} }
func (m *UserUgcCommunity) String() string { return proto.CompactTextString(m) }
func (*UserUgcCommunity) ProtoMessage()    {}
func (*UserUgcCommunity) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{16}
}
func (m *UserUgcCommunity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserUgcCommunity.Unmarshal(m, b)
}
func (m *UserUgcCommunity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserUgcCommunity.Marshal(b, m, deterministic)
}
func (dst *UserUgcCommunity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserUgcCommunity.Merge(dst, src)
}
func (m *UserUgcCommunity) XXX_Size() int {
	return xxx_messageInfo_UserUgcCommunity.Size(m)
}
func (m *UserUgcCommunity) XXX_DiscardUnknown() {
	xxx_messageInfo_UserUgcCommunity.DiscardUnknown(m)
}

var xxx_messageInfo_UserUgcCommunity proto.InternalMessageInfo

func (m *UserUgcCommunity) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserUgcCommunity) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserUgcCommunity) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *UserUgcCommunity) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserUgcCommunity) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

// 查看帖子详情
type PostInfo struct {
	PostId        string                            `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PostOwner     *UserUgcCommunity                 `protobuf:"bytes,2,opt,name=post_owner,json=postOwner,proto3" json:"post_owner,omitempty"`
	PostType      ugc_community.PostType            `protobuf:"varint,3,opt,name=post_type,json=postType,proto3,enum=ugc_community.PostType" json:"post_type,omitempty"`
	Content       string                            `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Attachments   []*Attachment                     `protobuf:"bytes,5,rep,name=attachments,proto3" json:"attachments,omitempty"`
	PostTime      int64                             `protobuf:"varint,6,opt,name=post_time,json=postTime,proto3" json:"post_time,omitempty"`
	CommentCount  uint32                            `protobuf:"varint,7,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	AttitudeCount uint32                            `protobuf:"varint,8,opt,name=attitude_count,json=attitudeCount,proto3" json:"attitude_count,omitempty"`
	Origin        ugc_community.PostOrigin          `protobuf:"varint,9,opt,name=origin,proto3,enum=ugc_community.PostOrigin" json:"origin,omitempty"`
	BizType       ugc_community.PostBizType         `protobuf:"varint,10,opt,name=biz_type,json=bizType,proto3,enum=ugc_community.PostBizType" json:"biz_type,omitempty"`
	BizData       map[string]string                 `protobuf:"bytes,11,rep,name=biz_data,json=bizData,proto3" json:"biz_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	HotComment    *ugc_community_middle.CommentItem `protobuf:"bytes,12,opt,name=hot_comment,json=hotComment,proto3" json:"hot_comment,omitempty"`
	IsAttitude    bool                              `protobuf:"varint,13,opt,name=is_attitude,json=isAttitude,proto3" json:"is_attitude,omitempty"`
	State         ugc_community.PostState           `protobuf:"varint,14,opt,name=state,proto3,enum=ugc_community.PostState" json:"state,omitempty"`
	// 帖子关联的话题
	TopicList []*TopicInfo `protobuf:"bytes,15,rep,name=topic_list,json=topicList,proto3" json:"topic_list,omitempty"`
	// 业务序列化数据，取代biz_data
	BizBytes             []byte   `protobuf:"bytes,16,opt,name=biz_bytes,json=bizBytes,proto3" json:"biz_bytes,omitempty"`
	HadFollowed          bool     `protobuf:"varint,17,opt,name=had_followed,json=hadFollowed,proto3" json:"had_followed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostInfo) Reset()         { *m = PostInfo{} }
func (m *PostInfo) String() string { return proto.CompactTextString(m) }
func (*PostInfo) ProtoMessage()    {}
func (*PostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{17}
}
func (m *PostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostInfo.Unmarshal(m, b)
}
func (m *PostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostInfo.Marshal(b, m, deterministic)
}
func (dst *PostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostInfo.Merge(dst, src)
}
func (m *PostInfo) XXX_Size() int {
	return xxx_messageInfo_PostInfo.Size(m)
}
func (m *PostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PostInfo proto.InternalMessageInfo

func (m *PostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostInfo) GetPostOwner() *UserUgcCommunity {
	if m != nil {
		return m.PostOwner
	}
	return nil
}

func (m *PostInfo) GetPostType() ugc_community.PostType {
	if m != nil {
		return m.PostType
	}
	return ugc_community.PostType_POST_TYPE_UNSPECIFED
}

func (m *PostInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PostInfo) GetAttachments() []*Attachment {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *PostInfo) GetPostTime() int64 {
	if m != nil {
		return m.PostTime
	}
	return 0
}

func (m *PostInfo) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *PostInfo) GetAttitudeCount() uint32 {
	if m != nil {
		return m.AttitudeCount
	}
	return 0
}

func (m *PostInfo) GetOrigin() ugc_community.PostOrigin {
	if m != nil {
		return m.Origin
	}
	return ugc_community.PostOrigin_POST_ORIGIN_UNSPECIFIED
}

func (m *PostInfo) GetBizType() ugc_community.PostBizType {
	if m != nil {
		return m.BizType
	}
	return ugc_community.PostBizType_POST_BIZ_TYPE_UNSPECIFIED
}

func (m *PostInfo) GetBizData() map[string]string {
	if m != nil {
		return m.BizData
	}
	return nil
}

func (m *PostInfo) GetHotComment() *ugc_community_middle.CommentItem {
	if m != nil {
		return m.HotComment
	}
	return nil
}

func (m *PostInfo) GetIsAttitude() bool {
	if m != nil {
		return m.IsAttitude
	}
	return false
}

func (m *PostInfo) GetState() ugc_community.PostState {
	if m != nil {
		return m.State
	}
	return ugc_community.PostState_POST_STATE_UNSPECIFIED
}

func (m *PostInfo) GetTopicList() []*TopicInfo {
	if m != nil {
		return m.TopicList
	}
	return nil
}

func (m *PostInfo) GetBizBytes() []byte {
	if m != nil {
		return m.BizBytes
	}
	return nil
}

func (m *PostInfo) GetHadFollowed() bool {
	if m != nil {
		return m.HadFollowed
	}
	return false
}

type GetPostReq struct {
	BaseReq              *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PostId               string       `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PostSource           PostSource   `protobuf:"varint,3,opt,name=post_source,json=postSource,proto3,enum=ugc_community_http_logic.PostSource" json:"post_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPostReq) Reset()         { *m = GetPostReq{} }
func (m *GetPostReq) String() string { return proto.CompactTextString(m) }
func (*GetPostReq) ProtoMessage()    {}
func (*GetPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{18}
}
func (m *GetPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostReq.Unmarshal(m, b)
}
func (m *GetPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostReq.Marshal(b, m, deterministic)
}
func (dst *GetPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostReq.Merge(dst, src)
}
func (m *GetPostReq) XXX_Size() int {
	return xxx_messageInfo_GetPostReq.Size(m)
}
func (m *GetPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostReq proto.InternalMessageInfo

func (m *GetPostReq) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPostReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetPostReq) GetPostSource() PostSource {
	if m != nil {
		return m.PostSource
	}
	return PostSource_SOURCE_AI_DISTRICT_UNSPECIFIED
}

type GetPostResp struct {
	PostInfo             *PostInfo `protobuf:"bytes,1,opt,name=post_info,json=postInfo,proto3" json:"post_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetPostResp) Reset()         { *m = GetPostResp{} }
func (m *GetPostResp) String() string { return proto.CompactTextString(m) }
func (*GetPostResp) ProtoMessage()    {}
func (*GetPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{19}
}
func (m *GetPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPostResp.Unmarshal(m, b)
}
func (m *GetPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPostResp.Marshal(b, m, deterministic)
}
func (dst *GetPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPostResp.Merge(dst, src)
}
func (m *GetPostResp) XXX_Size() int {
	return xxx_messageInfo_GetPostResp.Size(m)
}
func (m *GetPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPostResp proto.InternalMessageInfo

func (m *GetPostResp) GetPostInfo() *PostInfo {
	if m != nil {
		return m.PostInfo
	}
	return nil
}

type Feed struct {
	// 帖子
	Post                 *PostInfo `protobuf:"bytes,1,opt,name=post,proto3" json:"post,omitempty"`
	FeedId               string    `protobuf:"bytes,2,opt,name=feed_id,json=feedId,proto3" json:"feed_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *Feed) Reset()         { *m = Feed{} }
func (m *Feed) String() string { return proto.CompactTextString(m) }
func (*Feed) ProtoMessage()    {}
func (*Feed) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{20}
}
func (m *Feed) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Feed.Unmarshal(m, b)
}
func (m *Feed) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Feed.Marshal(b, m, deterministic)
}
func (dst *Feed) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Feed.Merge(dst, src)
}
func (m *Feed) XXX_Size() int {
	return xxx_messageInfo_Feed.Size(m)
}
func (m *Feed) XXX_DiscardUnknown() {
	xxx_messageInfo_Feed.DiscardUnknown(m)
}

var xxx_messageInfo_Feed proto.InternalMessageInfo

func (m *Feed) GetPost() *PostInfo {
	if m != nil {
		return m.Post
	}
	return nil
}

func (m *Feed) GetFeedId() string {
	if m != nil {
		return m.FeedId
	}
	return ""
}

type GetNewsFeedsReq struct {
	BaseReq      *BaseRequest                           `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	LoadMore     *ugc_community_middle.NewFeedsLoadMore `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	GetMode      uint32                                 `protobuf:"varint,3,opt,name=get_mode,json=getMode,proto3" json:"get_mode,omitempty"`
	BrowsePostId []string                               `protobuf:"bytes,4,rep,name=browse_post_id,json=browsePostId,proto3" json:"browse_post_id,omitempty"`
	PostSource   PostSource                             `protobuf:"varint,5,opt,name=post_source,json=postSource,proto3,enum=ugc_community_http_logic.PostSource" json:"post_source,omitempty"`
	// 主题ID
	SubjectId string `protobuf:"bytes,6,opt,name=subject_id,json=subjectId,proto3" json:"subject_id,omitempty"`
	// 角色ID
	RoleId               uint32   `protobuf:"varint,7,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewsFeedsReq) Reset()         { *m = GetNewsFeedsReq{} }
func (m *GetNewsFeedsReq) String() string { return proto.CompactTextString(m) }
func (*GetNewsFeedsReq) ProtoMessage()    {}
func (*GetNewsFeedsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{21}
}
func (m *GetNewsFeedsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewsFeedsReq.Unmarshal(m, b)
}
func (m *GetNewsFeedsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewsFeedsReq.Marshal(b, m, deterministic)
}
func (dst *GetNewsFeedsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewsFeedsReq.Merge(dst, src)
}
func (m *GetNewsFeedsReq) XXX_Size() int {
	return xxx_messageInfo_GetNewsFeedsReq.Size(m)
}
func (m *GetNewsFeedsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewsFeedsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewsFeedsReq proto.InternalMessageInfo

func (m *GetNewsFeedsReq) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetNewsFeedsReq) GetLoadMore() *ugc_community_middle.NewFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetNewsFeedsReq) GetGetMode() uint32 {
	if m != nil {
		return m.GetMode
	}
	return 0
}

func (m *GetNewsFeedsReq) GetBrowsePostId() []string {
	if m != nil {
		return m.BrowsePostId
	}
	return nil
}

func (m *GetNewsFeedsReq) GetPostSource() PostSource {
	if m != nil {
		return m.PostSource
	}
	return PostSource_SOURCE_AI_DISTRICT_UNSPECIFIED
}

func (m *GetNewsFeedsReq) GetSubjectId() string {
	if m != nil {
		return m.SubjectId
	}
	return ""
}

func (m *GetNewsFeedsReq) GetRoleId() uint32 {
	if m != nil {
		return m.RoleId
	}
	return 0
}

type GetNewsFeedsResp struct {
	Feeds                []*Feed                                `protobuf:"bytes,1,rep,name=feeds,proto3" json:"feeds,omitempty"`
	LoadMore             *ugc_community_middle.NewFeedsLoadMore `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Footprint            string                                 `protobuf:"bytes,3,opt,name=footprint,proto3" json:"footprint,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *GetNewsFeedsResp) Reset()         { *m = GetNewsFeedsResp{} }
func (m *GetNewsFeedsResp) String() string { return proto.CompactTextString(m) }
func (*GetNewsFeedsResp) ProtoMessage()    {}
func (*GetNewsFeedsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{22}
}
func (m *GetNewsFeedsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewsFeedsResp.Unmarshal(m, b)
}
func (m *GetNewsFeedsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewsFeedsResp.Marshal(b, m, deterministic)
}
func (dst *GetNewsFeedsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewsFeedsResp.Merge(dst, src)
}
func (m *GetNewsFeedsResp) XXX_Size() int {
	return xxx_messageInfo_GetNewsFeedsResp.Size(m)
}
func (m *GetNewsFeedsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewsFeedsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewsFeedsResp proto.InternalMessageInfo

func (m *GetNewsFeedsResp) GetFeeds() []*Feed {
	if m != nil {
		return m.Feeds
	}
	return nil
}

func (m *GetNewsFeedsResp) GetLoadMore() *ugc_community_middle.NewFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetNewsFeedsResp) GetFootprint() string {
	if m != nil {
		return m.Footprint
	}
	return ""
}

type AttitudeReq struct {
	BaseReq              *BaseRequest   `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Action               AttitudeAction `protobuf:"varint,2,opt,name=action,proto3,enum=ugc_community_http_logic.AttitudeAction" json:"action,omitempty"`
	ObjectType           ObjectType     `protobuf:"varint,3,opt,name=object_type,json=objectType,proto3,enum=ugc_community_http_logic.ObjectType" json:"object_type,omitempty"`
	Id                   string         `protobuf:"bytes,4,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *AttitudeReq) Reset()         { *m = AttitudeReq{} }
func (m *AttitudeReq) String() string { return proto.CompactTextString(m) }
func (*AttitudeReq) ProtoMessage()    {}
func (*AttitudeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{23}
}
func (m *AttitudeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttitudeReq.Unmarshal(m, b)
}
func (m *AttitudeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttitudeReq.Marshal(b, m, deterministic)
}
func (dst *AttitudeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttitudeReq.Merge(dst, src)
}
func (m *AttitudeReq) XXX_Size() int {
	return xxx_messageInfo_AttitudeReq.Size(m)
}
func (m *AttitudeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AttitudeReq.DiscardUnknown(m)
}

var xxx_messageInfo_AttitudeReq proto.InternalMessageInfo

func (m *AttitudeReq) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AttitudeReq) GetAction() AttitudeAction {
	if m != nil {
		return m.Action
	}
	return AttitudeAction_ATTITUDE_ACTION_UNSPECIFIED
}

func (m *AttitudeReq) GetObjectType() ObjectType {
	if m != nil {
		return m.ObjectType
	}
	return ObjectType_OBJECT_TYPE_UNSPECIFIED
}

func (m *AttitudeReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type AttitudeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AttitudeResp) Reset()         { *m = AttitudeResp{} }
func (m *AttitudeResp) String() string { return proto.CompactTextString(m) }
func (*AttitudeResp) ProtoMessage()    {}
func (*AttitudeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{24}
}
func (m *AttitudeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttitudeResp.Unmarshal(m, b)
}
func (m *AttitudeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttitudeResp.Marshal(b, m, deterministic)
}
func (dst *AttitudeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttitudeResp.Merge(dst, src)
}
func (m *AttitudeResp) XXX_Size() int {
	return xxx_messageInfo_AttitudeResp.Size(m)
}
func (m *AttitudeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AttitudeResp.DiscardUnknown(m)
}

var xxx_messageInfo_AttitudeResp proto.InternalMessageInfo

type GetSubjectTabListRequest struct {
	BaseReq              *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSubjectTabListRequest) Reset()         { *m = GetSubjectTabListRequest{} }
func (m *GetSubjectTabListRequest) String() string { return proto.CompactTextString(m) }
func (*GetSubjectTabListRequest) ProtoMessage()    {}
func (*GetSubjectTabListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{25}
}
func (m *GetSubjectTabListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSubjectTabListRequest.Unmarshal(m, b)
}
func (m *GetSubjectTabListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSubjectTabListRequest.Marshal(b, m, deterministic)
}
func (dst *GetSubjectTabListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSubjectTabListRequest.Merge(dst, src)
}
func (m *GetSubjectTabListRequest) XXX_Size() int {
	return xxx_messageInfo_GetSubjectTabListRequest.Size(m)
}
func (m *GetSubjectTabListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSubjectTabListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSubjectTabListRequest proto.InternalMessageInfo

func (m *GetSubjectTabListRequest) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type StickyPostInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Icon                 string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StickyPostInfo) Reset()         { *m = StickyPostInfo{} }
func (m *StickyPostInfo) String() string { return proto.CompactTextString(m) }
func (*StickyPostInfo) ProtoMessage()    {}
func (*StickyPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{26}
}
func (m *StickyPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StickyPostInfo.Unmarshal(m, b)
}
func (m *StickyPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StickyPostInfo.Marshal(b, m, deterministic)
}
func (dst *StickyPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StickyPostInfo.Merge(dst, src)
}
func (m *StickyPostInfo) XXX_Size() int {
	return xxx_messageInfo_StickyPostInfo.Size(m)
}
func (m *StickyPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StickyPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StickyPostInfo proto.InternalMessageInfo

func (m *StickyPostInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *StickyPostInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *StickyPostInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

// 主题
type SubjectTabInfo struct {
	// 主题ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 主题名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 主题下的置顶帖子信息
	StickyPostInfos      []*StickyPostInfo `protobuf:"bytes,3,rep,name=sticky_post_infos,json=stickyPostInfos,proto3" json:"sticky_post_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SubjectTabInfo) Reset()         { *m = SubjectTabInfo{} }
func (m *SubjectTabInfo) String() string { return proto.CompactTextString(m) }
func (*SubjectTabInfo) ProtoMessage()    {}
func (*SubjectTabInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{27}
}
func (m *SubjectTabInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubjectTabInfo.Unmarshal(m, b)
}
func (m *SubjectTabInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubjectTabInfo.Marshal(b, m, deterministic)
}
func (dst *SubjectTabInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubjectTabInfo.Merge(dst, src)
}
func (m *SubjectTabInfo) XXX_Size() int {
	return xxx_messageInfo_SubjectTabInfo.Size(m)
}
func (m *SubjectTabInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SubjectTabInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SubjectTabInfo proto.InternalMessageInfo

func (m *SubjectTabInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SubjectTabInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SubjectTabInfo) GetStickyPostInfos() []*StickyPostInfo {
	if m != nil {
		return m.StickyPostInfos
	}
	return nil
}

type GetSubjectTabListResponse struct {
	SubjectInfos         []*SubjectTabInfo `protobuf:"bytes,1,rep,name=subject_infos,json=subjectInfos,proto3" json:"subject_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetSubjectTabListResponse) Reset()         { *m = GetSubjectTabListResponse{} }
func (m *GetSubjectTabListResponse) String() string { return proto.CompactTextString(m) }
func (*GetSubjectTabListResponse) ProtoMessage()    {}
func (*GetSubjectTabListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{28}
}
func (m *GetSubjectTabListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSubjectTabListResponse.Unmarshal(m, b)
}
func (m *GetSubjectTabListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSubjectTabListResponse.Marshal(b, m, deterministic)
}
func (dst *GetSubjectTabListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSubjectTabListResponse.Merge(dst, src)
}
func (m *GetSubjectTabListResponse) XXX_Size() int {
	return xxx_messageInfo_GetSubjectTabListResponse.Size(m)
}
func (m *GetSubjectTabListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSubjectTabListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSubjectTabListResponse proto.InternalMessageInfo

func (m *GetSubjectTabListResponse) GetSubjectInfos() []*SubjectTabInfo {
	if m != nil {
		return m.SubjectInfos
	}
	return nil
}

type GetTopicListRequest struct {
	BaseReq *BaseRequest `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 分页游标，第一页传空
	Cursor string `protobuf:"bytes,2,opt,name=cursor,proto3" json:"cursor,omitempty"`
	// 每页返回数量，上限200
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicListRequest) Reset()         { *m = GetTopicListRequest{} }
func (m *GetTopicListRequest) String() string { return proto.CompactTextString(m) }
func (*GetTopicListRequest) ProtoMessage()    {}
func (*GetTopicListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{29}
}
func (m *GetTopicListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicListRequest.Unmarshal(m, b)
}
func (m *GetTopicListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicListRequest.Marshal(b, m, deterministic)
}
func (dst *GetTopicListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicListRequest.Merge(dst, src)
}
func (m *GetTopicListRequest) XXX_Size() int {
	return xxx_messageInfo_GetTopicListRequest.Size(m)
}
func (m *GetTopicListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicListRequest proto.InternalMessageInfo

func (m *GetTopicListRequest) GetBaseReq() *BaseRequest {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetTopicListRequest) GetCursor() string {
	if m != nil {
		return m.Cursor
	}
	return ""
}

func (m *GetTopicListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetTopicListResponse struct {
	// 下一页请求游标，为空表示到底
	NextCursor           string       `protobuf:"bytes,1,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
	List                 []*TopicInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetTopicListResponse) Reset()         { *m = GetTopicListResponse{} }
func (m *GetTopicListResponse) String() string { return proto.CompactTextString(m) }
func (*GetTopicListResponse) ProtoMessage()    {}
func (*GetTopicListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_community_http_logic_9023d5c48c15099e, []int{30}
}
func (m *GetTopicListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicListResponse.Unmarshal(m, b)
}
func (m *GetTopicListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicListResponse.Marshal(b, m, deterministic)
}
func (dst *GetTopicListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicListResponse.Merge(dst, src)
}
func (m *GetTopicListResponse) XXX_Size() int {
	return xxx_messageInfo_GetTopicListResponse.Size(m)
}
func (m *GetTopicListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicListResponse proto.InternalMessageInfo

func (m *GetTopicListResponse) GetNextCursor() string {
	if m != nil {
		return m.NextCursor
	}
	return ""
}

func (m *GetTopicListResponse) GetList() []*TopicInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func init() {
	proto.RegisterType((*BaseRequest)(nil), "ugc_community_http_logic.BaseRequest")
	proto.RegisterType((*Attachment)(nil), "ugc_community_http_logic.Attachment")
	proto.RegisterType((*TopicInfo)(nil), "ugc_community_http_logic.TopicInfo")
	proto.RegisterType((*AigcCommunityPost)(nil), "ugc_community_http_logic.AigcCommunityPost")
	proto.RegisterType((*GeneratePublishPostParamRequest)(nil), "ugc_community_http_logic.GeneratePublishPostParamRequest")
	proto.RegisterType((*GeneratePublishPostParamResponse)(nil), "ugc_community_http_logic.GeneratePublishPostParamResponse")
	proto.RegisterType((*PublishPostRequest)(nil), "ugc_community_http_logic.PublishPostRequest")
	proto.RegisterType((*PublishPostRequest_Post)(nil), "ugc_community_http_logic.PublishPostRequest.Post")
	proto.RegisterMapType((map[string]string)(nil), "ugc_community_http_logic.PublishPostRequest.Post.BizDataEntry")
	proto.RegisterType((*PublishPostResponse)(nil), "ugc_community_http_logic.PublishPostResponse")
	proto.RegisterType((*DeletePostRequest)(nil), "ugc_community_http_logic.DeletePostRequest")
	proto.RegisterType((*DeletePostResponse)(nil), "ugc_community_http_logic.DeletePostResponse")
	proto.RegisterType((*CommentSendRequest)(nil), "ugc_community_http_logic.CommentSendRequest")
	proto.RegisterType((*CommentSendResponse)(nil), "ugc_community_http_logic.CommentSendResponse")
	proto.RegisterType((*CommentFetchRequest)(nil), "ugc_community_http_logic.CommentFetchRequest")
	proto.RegisterType((*CommentFetchResponse)(nil), "ugc_community_http_logic.CommentFetchResponse")
	proto.RegisterType((*CommentDeleteRequest)(nil), "ugc_community_http_logic.CommentDeleteRequest")
	proto.RegisterType((*CommentDeleteResponse)(nil), "ugc_community_http_logic.CommentDeleteResponse")
	proto.RegisterType((*UserUgcCommunity)(nil), "ugc_community_http_logic.UserUgcCommunity")
	proto.RegisterType((*PostInfo)(nil), "ugc_community_http_logic.PostInfo")
	proto.RegisterMapType((map[string]string)(nil), "ugc_community_http_logic.PostInfo.BizDataEntry")
	proto.RegisterType((*GetPostReq)(nil), "ugc_community_http_logic.GetPostReq")
	proto.RegisterType((*GetPostResp)(nil), "ugc_community_http_logic.GetPostResp")
	proto.RegisterType((*Feed)(nil), "ugc_community_http_logic.Feed")
	proto.RegisterType((*GetNewsFeedsReq)(nil), "ugc_community_http_logic.GetNewsFeedsReq")
	proto.RegisterType((*GetNewsFeedsResp)(nil), "ugc_community_http_logic.GetNewsFeedsResp")
	proto.RegisterType((*AttitudeReq)(nil), "ugc_community_http_logic.AttitudeReq")
	proto.RegisterType((*AttitudeResp)(nil), "ugc_community_http_logic.AttitudeResp")
	proto.RegisterType((*GetSubjectTabListRequest)(nil), "ugc_community_http_logic.GetSubjectTabListRequest")
	proto.RegisterType((*StickyPostInfo)(nil), "ugc_community_http_logic.StickyPostInfo")
	proto.RegisterType((*SubjectTabInfo)(nil), "ugc_community_http_logic.SubjectTabInfo")
	proto.RegisterType((*GetSubjectTabListResponse)(nil), "ugc_community_http_logic.GetSubjectTabListResponse")
	proto.RegisterType((*GetTopicListRequest)(nil), "ugc_community_http_logic.GetTopicListRequest")
	proto.RegisterType((*GetTopicListResponse)(nil), "ugc_community_http_logic.GetTopicListResponse")
	proto.RegisterEnum("ugc_community_http_logic.PostSource", PostSource_name, PostSource_value)
	proto.RegisterEnum("ugc_community_http_logic.AttitudeAction", AttitudeAction_name, AttitudeAction_value)
	proto.RegisterEnum("ugc_community_http_logic.ObjectType", ObjectType_name, ObjectType_value)
	proto.RegisterEnum("ugc_community_http_logic.Attachment_Type", Attachment_Type_name, Attachment_Type_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UgcCommunityHttpLogicClient is the client API for UgcCommunityHttpLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UgcCommunityHttpLogicClient interface {
	// 生成发布帖子需要的参数
	GeneratePublishPostParam(ctx context.Context, in *GeneratePublishPostParamRequest, opts ...grpc.CallOption) (*GeneratePublishPostParamResponse, error)
	// 发布帖子
	PublishPost(ctx context.Context, in *PublishPostRequest, opts ...grpc.CallOption) (*PublishPostResponse, error)
	// 删除帖子
	DeletePost(ctx context.Context, in *DeletePostRequest, opts ...grpc.CallOption) (*DeletePostResponse, error)
	// 发评论
	CommentSend(ctx context.Context, in *CommentSendRequest, opts ...grpc.CallOption) (*CommentSendResponse, error)
	// 拉评论
	CommentFetch(ctx context.Context, in *CommentFetchRequest, opts ...grpc.CallOption) (*CommentFetchResponse, error)
	// 删除评论
	CommentDelete(ctx context.Context, in *CommentDeleteRequest, opts ...grpc.CallOption) (*CommentDeleteResponse, error)
	GetNewsFeeds(ctx context.Context, in *GetNewsFeedsReq, opts ...grpc.CallOption) (*GetNewsFeedsResp, error)
	GetPost(ctx context.Context, in *GetPostReq, opts ...grpc.CallOption) (*GetPostResp, error)
	// 点赞or取消点赞
	Attitude(ctx context.Context, in *AttitudeReq, opts ...grpc.CallOption) (*AttitudeResp, error)
	// 获取主题列表
	GetSubjectTabList(ctx context.Context, in *GetSubjectTabListRequest, opts ...grpc.CallOption) (*GetSubjectTabListResponse, error)
	// 获取话题列表
	GetTopicList(ctx context.Context, in *GetTopicListRequest, opts ...grpc.CallOption) (*GetTopicListResponse, error)
}

type ugcCommunityHttpLogicClient struct {
	cc *grpc.ClientConn
}

func NewUgcCommunityHttpLogicClient(cc *grpc.ClientConn) UgcCommunityHttpLogicClient {
	return &ugcCommunityHttpLogicClient{cc}
}

func (c *ugcCommunityHttpLogicClient) GeneratePublishPostParam(ctx context.Context, in *GeneratePublishPostParamRequest, opts ...grpc.CallOption) (*GeneratePublishPostParamResponse, error) {
	out := new(GeneratePublishPostParamResponse)
	err := c.cc.Invoke(ctx, "/ugc_community_http_logic.UgcCommunityHttpLogic/GeneratePublishPostParam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityHttpLogicClient) PublishPost(ctx context.Context, in *PublishPostRequest, opts ...grpc.CallOption) (*PublishPostResponse, error) {
	out := new(PublishPostResponse)
	err := c.cc.Invoke(ctx, "/ugc_community_http_logic.UgcCommunityHttpLogic/PublishPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityHttpLogicClient) DeletePost(ctx context.Context, in *DeletePostRequest, opts ...grpc.CallOption) (*DeletePostResponse, error) {
	out := new(DeletePostResponse)
	err := c.cc.Invoke(ctx, "/ugc_community_http_logic.UgcCommunityHttpLogic/DeletePost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityHttpLogicClient) CommentSend(ctx context.Context, in *CommentSendRequest, opts ...grpc.CallOption) (*CommentSendResponse, error) {
	out := new(CommentSendResponse)
	err := c.cc.Invoke(ctx, "/ugc_community_http_logic.UgcCommunityHttpLogic/CommentSend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityHttpLogicClient) CommentFetch(ctx context.Context, in *CommentFetchRequest, opts ...grpc.CallOption) (*CommentFetchResponse, error) {
	out := new(CommentFetchResponse)
	err := c.cc.Invoke(ctx, "/ugc_community_http_logic.UgcCommunityHttpLogic/CommentFetch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityHttpLogicClient) CommentDelete(ctx context.Context, in *CommentDeleteRequest, opts ...grpc.CallOption) (*CommentDeleteResponse, error) {
	out := new(CommentDeleteResponse)
	err := c.cc.Invoke(ctx, "/ugc_community_http_logic.UgcCommunityHttpLogic/CommentDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityHttpLogicClient) GetNewsFeeds(ctx context.Context, in *GetNewsFeedsReq, opts ...grpc.CallOption) (*GetNewsFeedsResp, error) {
	out := new(GetNewsFeedsResp)
	err := c.cc.Invoke(ctx, "/ugc_community_http_logic.UgcCommunityHttpLogic/GetNewsFeeds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityHttpLogicClient) GetPost(ctx context.Context, in *GetPostReq, opts ...grpc.CallOption) (*GetPostResp, error) {
	out := new(GetPostResp)
	err := c.cc.Invoke(ctx, "/ugc_community_http_logic.UgcCommunityHttpLogic/GetPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityHttpLogicClient) Attitude(ctx context.Context, in *AttitudeReq, opts ...grpc.CallOption) (*AttitudeResp, error) {
	out := new(AttitudeResp)
	err := c.cc.Invoke(ctx, "/ugc_community_http_logic.UgcCommunityHttpLogic/Attitude", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityHttpLogicClient) GetSubjectTabList(ctx context.Context, in *GetSubjectTabListRequest, opts ...grpc.CallOption) (*GetSubjectTabListResponse, error) {
	out := new(GetSubjectTabListResponse)
	err := c.cc.Invoke(ctx, "/ugc_community_http_logic.UgcCommunityHttpLogic/GetSubjectTabList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcCommunityHttpLogicClient) GetTopicList(ctx context.Context, in *GetTopicListRequest, opts ...grpc.CallOption) (*GetTopicListResponse, error) {
	out := new(GetTopicListResponse)
	err := c.cc.Invoke(ctx, "/ugc_community_http_logic.UgcCommunityHttpLogic/GetTopicList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UgcCommunityHttpLogicServer is the server API for UgcCommunityHttpLogic service.
type UgcCommunityHttpLogicServer interface {
	// 生成发布帖子需要的参数
	GeneratePublishPostParam(context.Context, *GeneratePublishPostParamRequest) (*GeneratePublishPostParamResponse, error)
	// 发布帖子
	PublishPost(context.Context, *PublishPostRequest) (*PublishPostResponse, error)
	// 删除帖子
	DeletePost(context.Context, *DeletePostRequest) (*DeletePostResponse, error)
	// 发评论
	CommentSend(context.Context, *CommentSendRequest) (*CommentSendResponse, error)
	// 拉评论
	CommentFetch(context.Context, *CommentFetchRequest) (*CommentFetchResponse, error)
	// 删除评论
	CommentDelete(context.Context, *CommentDeleteRequest) (*CommentDeleteResponse, error)
	GetNewsFeeds(context.Context, *GetNewsFeedsReq) (*GetNewsFeedsResp, error)
	GetPost(context.Context, *GetPostReq) (*GetPostResp, error)
	// 点赞or取消点赞
	Attitude(context.Context, *AttitudeReq) (*AttitudeResp, error)
	// 获取主题列表
	GetSubjectTabList(context.Context, *GetSubjectTabListRequest) (*GetSubjectTabListResponse, error)
	// 获取话题列表
	GetTopicList(context.Context, *GetTopicListRequest) (*GetTopicListResponse, error)
}

func RegisterUgcCommunityHttpLogicServer(s *grpc.Server, srv UgcCommunityHttpLogicServer) {
	s.RegisterService(&_UgcCommunityHttpLogic_serviceDesc, srv)
}

func _UgcCommunityHttpLogic_GeneratePublishPostParam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GeneratePublishPostParamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityHttpLogicServer).GeneratePublishPostParam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_http_logic.UgcCommunityHttpLogic/GeneratePublishPostParam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityHttpLogicServer).GeneratePublishPostParam(ctx, req.(*GeneratePublishPostParamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunityHttpLogic_PublishPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublishPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityHttpLogicServer).PublishPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_http_logic.UgcCommunityHttpLogic/PublishPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityHttpLogicServer).PublishPost(ctx, req.(*PublishPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunityHttpLogic_DeletePost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityHttpLogicServer).DeletePost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_http_logic.UgcCommunityHttpLogic/DeletePost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityHttpLogicServer).DeletePost(ctx, req.(*DeletePostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunityHttpLogic_CommentSend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommentSendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityHttpLogicServer).CommentSend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_http_logic.UgcCommunityHttpLogic/CommentSend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityHttpLogicServer).CommentSend(ctx, req.(*CommentSendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunityHttpLogic_CommentFetch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommentFetchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityHttpLogicServer).CommentFetch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_http_logic.UgcCommunityHttpLogic/CommentFetch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityHttpLogicServer).CommentFetch(ctx, req.(*CommentFetchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunityHttpLogic_CommentDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommentDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityHttpLogicServer).CommentDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_http_logic.UgcCommunityHttpLogic/CommentDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityHttpLogicServer).CommentDelete(ctx, req.(*CommentDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunityHttpLogic_GetNewsFeeds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewsFeedsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityHttpLogicServer).GetNewsFeeds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_http_logic.UgcCommunityHttpLogic/GetNewsFeeds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityHttpLogicServer).GetNewsFeeds(ctx, req.(*GetNewsFeedsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunityHttpLogic_GetPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityHttpLogicServer).GetPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_http_logic.UgcCommunityHttpLogic/GetPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityHttpLogicServer).GetPost(ctx, req.(*GetPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunityHttpLogic_Attitude_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AttitudeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityHttpLogicServer).Attitude(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_http_logic.UgcCommunityHttpLogic/Attitude",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityHttpLogicServer).Attitude(ctx, req.(*AttitudeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunityHttpLogic_GetSubjectTabList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubjectTabListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityHttpLogicServer).GetSubjectTabList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_http_logic.UgcCommunityHttpLogic/GetSubjectTabList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityHttpLogicServer).GetSubjectTabList(ctx, req.(*GetSubjectTabListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcCommunityHttpLogic_GetTopicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcCommunityHttpLogicServer).GetTopicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_community_http_logic.UgcCommunityHttpLogic/GetTopicList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcCommunityHttpLogicServer).GetTopicList(ctx, req.(*GetTopicListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _UgcCommunityHttpLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc_community_http_logic.UgcCommunityHttpLogic",
	HandlerType: (*UgcCommunityHttpLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GeneratePublishPostParam",
			Handler:    _UgcCommunityHttpLogic_GeneratePublishPostParam_Handler,
		},
		{
			MethodName: "PublishPost",
			Handler:    _UgcCommunityHttpLogic_PublishPost_Handler,
		},
		{
			MethodName: "DeletePost",
			Handler:    _UgcCommunityHttpLogic_DeletePost_Handler,
		},
		{
			MethodName: "CommentSend",
			Handler:    _UgcCommunityHttpLogic_CommentSend_Handler,
		},
		{
			MethodName: "CommentFetch",
			Handler:    _UgcCommunityHttpLogic_CommentFetch_Handler,
		},
		{
			MethodName: "CommentDelete",
			Handler:    _UgcCommunityHttpLogic_CommentDelete_Handler,
		},
		{
			MethodName: "GetNewsFeeds",
			Handler:    _UgcCommunityHttpLogic_GetNewsFeeds_Handler,
		},
		{
			MethodName: "GetPost",
			Handler:    _UgcCommunityHttpLogic_GetPost_Handler,
		},
		{
			MethodName: "Attitude",
			Handler:    _UgcCommunityHttpLogic_Attitude_Handler,
		},
		{
			MethodName: "GetSubjectTabList",
			Handler:    _UgcCommunityHttpLogic_GetSubjectTabList_Handler,
		},
		{
			MethodName: "GetTopicList",
			Handler:    _UgcCommunityHttpLogic_GetTopicList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/ugc-community-http-logic/ugc-community-http-logic.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/ugc-community-http-logic/ugc-community-http-logic.proto", fileDescriptor_ugc_community_http_logic_9023d5c48c15099e)
}

var fileDescriptor_ugc_community_http_logic_9023d5c48c15099e = []byte{
	// 2391 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x59, 0xcb, 0x6f, 0x23, 0x49,
	0x19, 0xa7, 0x3d, 0x8e, 0x1f, 0x9f, 0x1d, 0x8f, 0xa7, 0x36, 0xbb, 0xf1, 0x78, 0x1f, 0x93, 0xed,
	0x9d, 0xcc, 0x66, 0x33, 0x13, 0x7b, 0x37, 0xb3, 0x2f, 0x46, 0xda, 0x65, 0x13, 0xc7, 0x09, 0x5e,
	0x26, 0x0f, 0xb5, 0x1d, 0x60, 0x11, 0x52, 0xab, 0xdd, 0x5d, 0xb1, 0x7b, 0x63, 0x77, 0xf5, 0x74,
	0x95, 0x13, 0x32, 0xdc, 0x90, 0x90, 0x80, 0x0b, 0x42, 0x48, 0x80, 0x40, 0x1c, 0x38, 0x70, 0x40,
	0x48, 0xdc, 0x91, 0xf8, 0x13, 0x40, 0x42, 0x42, 0x42, 0xfc, 0x01, 0x1c, 0xf8, 0x1b, 0x38, 0x20,
	0x54, 0x8f, 0x6e, 0xb7, 0x1d, 0xdb, 0xf1, 0xcc, 0x06, 0x6e, 0x5d, 0x5f, 0x7d, 0x55, 0xdf, 0xaf,
	0xbe, 0x77, 0x55, 0xc3, 0x2e, 0x63, 0xd5, 0x27, 0x03, 0xd7, 0x3e, 0xa5, 0x6e, 0xef, 0x0c, 0x07,
	0xd5, 0x41, 0xc7, 0xde, 0xb0, 0x49, 0xbf, 0x3f, 0xf0, 0x5c, 0x76, 0xb1, 0xd1, 0x65, 0xcc, 0xdf,
	0xe8, 0x91, 0x8e, 0x6b, 0x4f, 0x9d, 0xa8, 0xf8, 0x01, 0x61, 0x04, 0x95, 0x06, 0x1d, 0xdb, 0x8c,
	0xe6, 0x4d, 0x3e, 0x6f, 0x8a, 0xf9, 0xf2, 0x2b, 0x1d, 0x42, 0x3a, 0x3d, 0x5c, 0xb5, 0x7c, 0xb7,
	0x6a, 0x79, 0x1e, 0x61, 0x16, 0x73, 0x89, 0x47, 0xe5, 0xba, 0xf2, 0xdb, 0xb3, 0xe4, 0x8f, 0x8e,
	0xd4, 0x8a, 0x8f, 0x67, 0x22, 0xee, 0xbb, 0x8e, 0xd3, 0xc3, 0x13, 0x89, 0x72, 0xbd, 0xfe, 0x63,
	0x0d, 0x72, 0xdb, 0x16, 0xc5, 0x06, 0x7e, 0x32, 0xc0, 0x94, 0xa1, 0x97, 0x21, 0xeb, 0xe0, 0x33,
	0xd7, 0xc6, 0xa6, 0xeb, 0x94, 0xb4, 0x15, 0x6d, 0x2d, 0x6b, 0x64, 0x24, 0xa1, 0xe1, 0xf0, 0xc9,
	0xbe, 0x15, 0x9c, 0x62, 0xc6, 0x27, 0x13, 0x2b, 0xda, 0xda, 0xa2, 0x91, 0x91, 0x84, 0x86, 0x83,
	0xee, 0x40, 0xce, 0xee, 0xb9, 0xd8, 0x63, 0x26, 0xbb, 0xf0, 0x71, 0xe9, 0x86, 0x98, 0x06, 0x49,
	0x6a, 0x5d, 0xf8, 0x18, 0xad, 0x42, 0x41, 0x31, 0x9c, 0xe1, 0x80, 0xba, 0xc4, 0x2b, 0x25, 0x05,
	0xcf, 0xa2, 0xa4, 0x7e, 0x5d, 0x12, 0xf5, 0x3f, 0x6a, 0x00, 0x5b, 0x8c, 0x59, 0x76, 0xb7, 0x8f,
	0x3d, 0x86, 0x3e, 0x82, 0xa4, 0xd8, 0x8f, 0x63, 0x29, 0x6c, 0xbe, 0x55, 0x99, 0xa6, 0xd9, 0xca,
	0x70, 0x4d, 0x85, 0x8b, 0x33, 0xc4, 0x32, 0x54, 0x84, 0x1b, 0xa7, 0xf8, 0x42, 0x80, 0xcd, 0x1a,
	0xfc, 0x13, 0x95, 0x20, 0x6d, 0x13, 0x8f, 0x61, 0x8f, 0x09, 0x8c, 0x59, 0x23, 0x1c, 0x22, 0x04,
	0x49, 0xea, 0x3e, 0xc5, 0x02, 0x56, 0xd6, 0x10, 0xdf, 0xfa, 0x03, 0x48, 0x0a, 0xf0, 0x4b, 0x50,
	0x6c, 0x7d, 0x76, 0x54, 0x37, 0x8f, 0x0f, 0x9a, 0x47, 0xf5, 0x5a, 0x63, 0xb7, 0x51, 0xdf, 0x29,
	0x7e, 0x09, 0x15, 0x00, 0x04, 0xb5, 0xb1, 0xbf, 0xb5, 0x57, 0x2f, 0x6a, 0x7a, 0x0d, 0xb2, 0x2d,
	0xe2, 0xbb, 0x76, 0xc3, 0x3b, 0x21, 0xa8, 0x00, 0x89, 0x48, 0x87, 0x09, 0xd7, 0xe1, 0xdb, 0x8b,
	0x93, 0x48, 0xc5, 0x49, 0x78, 0x08, 0x92, 0x9e, 0xd5, 0xc7, 0x0a, 0x89, 0xf8, 0xd6, 0xff, 0xa3,
	0xc1, 0xad, 0x2d, 0xb7, 0x63, 0xd7, 0xc2, 0x53, 0x1e, 0x11, 0xca, 0xd0, 0x32, 0xa4, 0x03, 0xd2,
	0x8b, 0xcc, 0xb2, 0x68, 0xa4, 0xf8, 0x50, 0x1a, 0x45, 0x4c, 0xc4, 0xf6, 0xce, 0x70, 0x82, 0x80,
	0x1d, 0x4e, 0xc6, 0x84, 0x88, 0xc9, 0x03, 0xab, 0x8f, 0xb9, 0xc5, 0xc4, 0xa4, 0x75, 0x66, 0x31,
	0x2b, 0x50, 0xc7, 0x06, 0x4e, 0xda, 0x12, 0x14, 0x6e, 0x31, 0xc1, 0x60, 0x77, 0xad, 0xc0, 0xb2,
	0x19, 0x0e, 0x4a, 0x0b, 0x82, 0x67, 0x91, 0x53, 0x6b, 0x21, 0x11, 0x1d, 0x40, 0xde, 0xee, 0x5a,
	0xcc, 0x0c, 0xb0, 0x4d, 0x02, 0x87, 0x96, 0x52, 0x2b, 0x37, 0xd6, 0x72, 0x9b, 0xf7, 0x47, 0x4d,
	0x55, 0xb9, 0x74, 0xa4, 0x4a, 0xad, 0x6b, 0x31, 0x43, 0xac, 0x31, 0x72, 0x76, 0xf4, 0x4d, 0xf5,
	0xdf, 0x6b, 0x70, 0x67, 0x0f, 0x7b, 0x38, 0xb0, 0x18, 0x3e, 0x1a, 0xb4, 0x7b, 0x2e, 0xed, 0x72,
	0xfe, 0x23, 0x2b, 0xb0, 0xfa, 0xa1, 0x9f, 0x7e, 0x02, 0x99, 0xb6, 0x45, 0xb1, 0x19, 0xe0, 0x27,
	0x42, 0x1f, 0xb9, 0xcd, 0xd5, 0xe9, 0xae, 0x11, 0x73, 0x70, 0x23, 0xdd, 0x96, 0x03, 0xb4, 0x0b,
	0x39, 0x2b, 0x72, 0x19, 0x5a, 0x4a, 0x08, 0xd0, 0x77, 0xe7, 0xf1, 0x2f, 0x23, 0xbe, 0x50, 0xff,
	0x8b, 0x06, 0x2b, 0xd3, 0xd1, 0x52, 0x9f, 0x78, 0x14, 0x5f, 0xf2, 0x85, 0x65, 0x48, 0x93, 0x36,
	0x35, 0x2d, 0xdf, 0x57, 0xae, 0x99, 0x22, 0x6d, 0xba, 0xe5, 0xfb, 0xdc, 0x60, 0x7c, 0x82, 0xda,
	0xc4, 0x8f, 0x0c, 0x46, 0xda, 0xb4, 0xc9, 0xc7, 0xe3, 0x90, 0x93, 0xcf, 0x09, 0x39, 0x14, 0xc2,
	0xc8, 0x29, 0xf6, 0x94, 0x49, 0xb9, 0x90, 0x16, 0x1f, 0xeb, 0x7f, 0x5f, 0x00, 0x14, 0x3b, 0xc7,
	0xf5, 0x29, 0xbc, 0x0e, 0x49, 0x9f, 0x50, 0x26, 0x0e, 0x9c, 0xdb, 0x7c, 0x67, 0xfa, 0xea, 0xcb,
	0xd2, 0x2b, 0xe2, 0x5b, 0x2c, 0x47, 0xaf, 0x02, 0x30, 0x8b, 0x9e, 0x2a, 0xf4, 0x52, 0x45, 0x59,
	0x4e, 0x11, 0xf0, 0xcb, 0x3f, 0x4f, 0x42, 0x52, 0x04, 0xcc, 0xb8, 0xca, 0xef, 0xc7, 0xc2, 0xaf,
	0xb0, 0xb9, 0x3c, 0xe6, 0x9d, 0x7c, 0x49, 0x2c, 0x6d, 0x54, 0x60, 0x81, 0x32, 0x8b, 0x49, 0x13,
	0x14, 0x36, 0x4b, 0x13, 0xb8, 0x9b, 0x7c, 0xde, 0x90, 0x6c, 0xe8, 0x1d, 0x48, 0x91, 0xc0, 0xed,
	0xb8, 0x32, 0xa7, 0x15, 0x36, 0x6f, 0x4f, 0x58, 0x70, 0x28, 0x18, 0x0c, 0xc5, 0x18, 0xcf, 0x43,
	0x0b, 0xa3, 0x79, 0x68, 0xcc, 0xcc, 0xa9, 0xe7, 0x35, 0xf3, 0x7b, 0x90, 0x69, 0xbb, 0x4f, 0x65,
	0x62, 0x48, 0x0b, 0x58, 0xe5, 0x09, 0xb0, 0xb6, 0xdd, 0xa7, 0xe2, 0xe0, 0xe9, 0xb6, 0xfc, 0x40,
	0x9f, 0xc9, 0x65, 0x8e, 0xc5, 0xac, 0x52, 0x46, 0xc8, 0xfe, 0xf8, 0x99, 0x6d, 0x55, 0xd9, 0x76,
	0x9f, 0xee, 0x58, 0xcc, 0xaa, 0x7b, 0x2c, 0xb8, 0x10, 0x5b, 0xf3, 0x11, 0xd2, 0x61, 0x91, 0xf1,
	0xfc, 0x68, 0xba, 0x8e, 0xd9, 0x73, 0x29, 0x2b, 0x65, 0x57, 0x6e, 0xac, 0x65, 0x8d, 0x9c, 0x20,
	0x36, 0x9c, 0xc7, 0xae, 0xac, 0x40, 0x5c, 0x7c, 0xfb, 0x82, 0x61, 0x5a, 0x82, 0x15, 0x6d, 0x2d,
	0x6f, 0x70, 0x3c, 0xdb, 0x7c, 0x5c, 0x7e, 0x04, 0xf9, 0xf8, 0xce, 0x61, 0x7a, 0xd7, 0x86, 0xe9,
	0x7d, 0x09, 0x16, 0xce, 0xac, 0xde, 0x00, 0xab, 0xb8, 0x92, 0x83, 0x47, 0x89, 0x0f, 0x35, 0xfd,
	0x45, 0x78, 0x61, 0x04, 0xad, 0x0c, 0x4d, 0x1d, 0xc3, 0xad, 0x1d, 0xdc, 0xc3, 0x0c, 0x5f, 0xaf,
	0xb7, 0x4b, 0xf7, 0x4b, 0x84, 0xee, 0xa7, 0x2f, 0x01, 0x8a, 0x8b, 0x51, 0xc2, 0xff, 0xac, 0x01,
	0xe2, 0x49, 0x11, 0x7b, 0xac, 0x89, 0x3d, 0xe7, 0xfa, 0xc4, 0x2f, 0x43, 0x9a, 0x47, 0x8b, 0x19,
	0x61, 0x48, 0xf1, 0x61, 0xc3, 0x41, 0x77, 0x79, 0x4e, 0x27, 0xcc, 0xf4, 0xad, 0x80, 0x97, 0x62,
	0xd7, 0x51, 0x21, 0x94, 0xe7, 0xd4, 0x23, 0x41, 0x94, 0x45, 0x65, 0xc8, 0x20, 0x0b, 0x43, 0xc6,
	0x0f, 0x27, 0xa7, 0x7a, 0x2e, 0x57, 0xf1, 0xc8, 0x69, 0xd4, 0x29, 0x7f, 0xa1, 0x45, 0xf4, 0x5d,
	0xcc, 0xec, 0xee, 0xff, 0xe1, 0x98, 0xf7, 0xe0, 0x66, 0xcf, 0xa2, 0x4c, 0x6c, 0x35, 0x72, 0xce,
	0x45, 0x4e, 0x56, 0x60, 0x1a, 0x8e, 0xfe, 0x5d, 0x58, 0x1a, 0x45, 0xa6, 0x12, 0xf6, 0x47, 0x90,
	0x51, 0x4b, 0x69, 0x49, 0x13, 0x41, 0xf0, 0xfa, 0x18, 0x34, 0xd5, 0x46, 0x85, 0x5b, 0x31, 0xdc,
	0x37, 0xa2, 0x25, 0x5c, 0xcb, 0x2e, 0x35, 0x7b, 0xc4, 0x72, 0xcc, 0x13, 0xd7, 0x73, 0x69, 0x57,
	0xc0, 0xcb, 0x18, 0x79, 0x97, 0x3e, 0x26, 0x96, 0xb3, 0x2b, 0x68, 0xfa, 0x79, 0x24, 0x5c, 0xba,
	0xc6, 0xf5, 0xe9, 0xe5, 0x55, 0x80, 0xd8, 0xc9, 0xa5, 0x6a, 0xb2, 0x76, 0x74, 0xea, 0x65, 0x78,
	0x71, 0x4c, 0xb0, 0xb2, 0xd4, 0x0f, 0x34, 0x28, 0x1e, 0x53, 0x1c, 0x1c, 0xc7, 0x6a, 0x35, 0x0f,
	0xb2, 0x41, 0xd4, 0x76, 0xf0, 0x4f, 0xee, 0x01, 0x96, 0x6d, 0x93, 0x81, 0xc7, 0xd4, 0xde, 0xe1,
	0x90, 0x87, 0x9f, 0xd5, 0x73, 0x2d, 0xaa, 0xb4, 0x2d, 0x07, 0xa8, 0x0c, 0x19, 0xcf, 0xb5, 0x4f,
	0x45, 0x17, 0xa2, 0xbc, 0x29, 0x1c, 0xa3, 0x97, 0x20, 0xd5, 0xc1, 0x9e, 0xa3, 0x9a, 0x8b, 0x45,
	0x43, 0x8d, 0xf4, 0x7f, 0xa7, 0x20, 0xc3, 0x63, 0x45, 0xf4, 0x52, 0x31, 0x3b, 0x6b, 0x23, 0x76,
	0x6e, 0x00, 0x88, 0x09, 0x72, 0xee, 0xe1, 0x40, 0x95, 0x96, 0xf5, 0xe9, 0xca, 0x1a, 0x3f, 0x9b,
	0x91, 0xe5, 0xab, 0x0f, 0xf9, 0x62, 0xf4, 0x2e, 0x88, 0xc1, 0xb0, 0x7d, 0x9d, 0x51, 0x25, 0x32,
	0xbe, 0xfa, 0x8a, 0x07, 0x43, 0x72, 0x66, 0x1a, 0x5f, 0xf8, 0x02, 0xd5, 0x5a, 0xe2, 0x72, 0xfb,
	0xb8, 0x94, 0x5a, 0xd1, 0xd6, 0x6e, 0x28, 0xf1, 0x6e, 0x1f, 0xa3, 0x37, 0x60, 0x31, 0x34, 0xb4,
	0xb4, 0x47, 0x5a, 0x28, 0x31, 0xaf, 0x88, 0x35, 0x61, 0x94, 0x55, 0x28, 0x58, 0x8c, 0xb9, 0x6c,
	0xe0, 0x60, 0xc5, 0x95, 0x91, 0x9d, 0x77, 0x48, 0x95, 0x6c, 0xc3, 0x22, 0x96, 0x9d, 0xb7, 0x88,
	0xc5, 0x4b, 0x0c, 0xcc, 0x5f, 0x62, 0x3e, 0x8d, 0x95, 0x98, 0x9c, 0xd0, 0x4b, 0x75, 0x46, 0x89,
	0x51, 0x4e, 0x30, 0xa5, 0xa6, 0x6c, 0x43, 0xae, 0x4b, 0xa2, 0x40, 0x2f, 0xe5, 0x85, 0x0b, 0xcc,
	0x11, 0xac, 0xd0, 0x25, 0x61, 0x1e, 0xe0, 0x9d, 0xb0, 0x4b, 0xcd, 0x50, 0x1b, 0xa5, 0x45, 0x11,
	0xab, 0xe0, 0xd2, 0x2d, 0x45, 0x19, 0xf6, 0x03, 0x85, 0xf9, 0xfa, 0x81, 0x6d, 0x00, 0x59, 0xe8,
	0x44, 0x95, 0xbb, 0x29, 0x8e, 0xf8, 0xc6, 0xf4, 0x23, 0x46, 0x97, 0x06, 0x23, 0x2b, 0x96, 0x5d,
	0x2e, 0x84, 0xc5, 0xd1, 0x42, 0x88, 0x5e, 0x87, 0x7c, 0x97, 0x27, 0x17, 0xd2, 0xeb, 0x91, 0x73,
	0xec, 0x94, 0x6e, 0x09, 0xc8, 0xb9, 0xae, 0xe5, 0xec, 0x2a, 0xd2, 0x17, 0xaa, 0x95, 0x7f, 0xd0,
	0x00, 0xf6, 0x30, 0x53, 0x25, 0xf1, 0x7f, 0x99, 0xa8, 0xeb, 0x90, 0x13, 0x13, 0x94, 0x0c, 0x02,
	0x3b, 0x8c, 0xbb, 0xbb, 0xb3, 0xbd, 0xa1, 0x29, 0x78, 0x0d, 0x11, 0xf9, 0xf2, 0x5b, 0x3f, 0x80,
	0x5c, 0x84, 0x97, 0xfa, 0xe8, 0x2b, 0x2a, 0x66, 0x5c, 0xef, 0x84, 0x28, 0xc4, 0xfa, 0xd5, 0x1e,
	0x26, 0xe3, 0x8a, 0x7f, 0xe9, 0xdf, 0x80, 0xe4, 0x2e, 0xc6, 0x0e, 0x7a, 0x5f, 0x35, 0xad, 0xf3,
	0xef, 0x21, 0xbb, 0xd4, 0x65, 0x48, 0x9f, 0x60, 0xec, 0xc4, 0xce, 0xcb, 0x87, 0x0d, 0x47, 0xff,
	0x47, 0x02, 0x6e, 0xee, 0x61, 0x76, 0x80, 0xcf, 0x29, 0x17, 0x40, 0xaf, 0x47, 0xbd, 0x35, 0xc8,
	0x8a, 0x62, 0xd3, 0x27, 0x01, 0x56, 0x59, 0xf0, 0xde, 0xe4, 0x10, 0x38, 0xc0, 0xe7, 0x42, 0x2e,
	0x2f, 0x43, 0xfb, 0x24, 0xc0, 0x46, 0xa6, 0xa7, 0xbe, 0xd0, 0x6d, 0xc8, 0x74, 0x30, 0x33, 0xfb,
	0xc4, 0x09, 0xaf, 0xef, 0xe9, 0x0e, 0x66, 0xfb, 0xc4, 0xc1, 0xbc, 0x9e, 0xb5, 0x03, 0x72, 0x4e,
	0xb1, 0x19, 0x5a, 0x31, 0x29, 0x3a, 0xb7, 0xbc, 0xa4, 0x1e, 0x4d, 0xb4, 0xe5, 0xc2, 0xf3, 0xd9,
	0x92, 0x17, 0x2f, 0x3a, 0x68, 0x7f, 0x8e, 0x6d, 0x21, 0x28, 0x25, 0x8b, 0x97, 0xa2, 0x34, 0x9c,
	0xf8, 0x4d, 0x38, 0x1d, 0xbf, 0x09, 0xf3, 0x7b, 0x63, 0x71, 0x54, 0xb5, 0xd4, 0x47, 0xef, 0xc2,
	0x02, 0xd7, 0x7c, 0x58, 0xc5, 0x5f, 0x9b, 0x8e, 0x86, 0xaf, 0x31, 0x24, 0xf3, 0xf5, 0xe8, 0xf3,
	0x15, 0xc8, 0x9e, 0x10, 0xc2, 0xfc, 0xc0, 0x8d, 0xde, 0x1a, 0x86, 0x04, 0xfd, 0x5f, 0x1a, 0xe4,
	0xc2, 0xfc, 0x72, 0x3d, 0x4e, 0xf0, 0x09, 0xa4, 0x2c, 0x9b, 0xb9, 0xc4, 0x53, 0x77, 0x9c, 0xb5,
	0x99, 0xb5, 0x46, 0x08, 0xde, 0x12, 0xfc, 0x86, 0x5a, 0xc7, 0x0d, 0x48, 0xa4, 0xe2, 0x63, 0x45,
	0x70, 0x86, 0x01, 0x0f, 0x05, 0xb3, 0xc8, 0xed, 0x40, 0xa2, 0x6f, 0xd5, 0xfb, 0x26, 0xa3, 0xde,
	0xb7, 0x00, 0xf9, 0xe1, 0x49, 0xa9, 0xaf, 0x7f, 0x1b, 0x4a, 0x7b, 0x98, 0x35, 0xa5, 0x45, 0x5b,
	0x56, 0x9b, 0xa7, 0xbb, 0x6b, 0xeb, 0x7d, 0xf4, 0x4f, 0xa1, 0xd0, 0x64, 0xae, 0x7d, 0x7a, 0x11,
	0x75, 0x0f, 0xe3, 0x57, 0xc1, 0x25, 0x58, 0x60, 0x2e, 0xeb, 0x45, 0x79, 0x4f, 0x0c, 0x10, 0x82,
	0xa4, 0x6b, 0x93, 0xf0, 0x4a, 0x29, 0xbe, 0xf5, 0x1f, 0x69, 0x50, 0x18, 0xe2, 0x9c, 0xf6, 0xac,
	0x23, 0xfa, 0x9a, 0xc4, 0xf0, 0x09, 0x07, 0xb5, 0xe0, 0x16, 0x15, 0x10, 0xcc, 0x28, 0x0b, 0xf1,
	0x8e, 0x88, 0x3b, 0xe0, 0x0c, 0xa3, 0x8c, 0xa2, 0x36, 0x6e, 0xd2, 0x91, 0x31, 0xd5, 0x3f, 0x87,
	0xdb, 0x13, 0xd4, 0xa6, 0x1a, 0xd6, 0x7d, 0x58, 0x8c, 0x82, 0x46, 0x88, 0xd3, 0xae, 0x14, 0x37,
	0x72, 0x2e, 0x23, 0x1f, 0x46, 0x98, 0x90, 0xf5, 0x7d, 0x0d, 0x5e, 0xd8, 0xc3, 0xac, 0x15, 0x56,
	0xa3, 0xeb, 0x6b, 0x4d, 0x5f, 0x82, 0x94, 0x3d, 0x08, 0x28, 0x09, 0xc2, 0xc4, 0x28, 0x47, 0xdc,
	0x28, 0x3d, 0xb7, 0xef, 0x32, 0x95, 0x7a, 0xe4, 0x40, 0xf7, 0x61, 0x69, 0x14, 0x86, 0x3a, 0xee,
	0x1d, 0xc8, 0x79, 0xf8, 0x3b, 0xcc, 0x54, 0x5b, 0x49, 0x73, 0x00, 0x27, 0xd5, 0xe4, 0x76, 0x1f,
	0x40, 0x52, 0xd4, 0xde, 0xc4, 0xfc, 0xb5, 0x57, 0x2c, 0x58, 0x3f, 0x03, 0x18, 0xe6, 0x25, 0xa4,
	0xc3, 0x6b, 0xcd, 0xc3, 0x63, 0xa3, 0x56, 0x37, 0xb7, 0x1a, 0xe6, 0x4e, 0xa3, 0xd9, 0x32, 0x1a,
	0xb5, 0xd6, 0xd8, 0x2b, 0xe0, 0x2a, 0xbc, 0x3e, 0x81, 0xc7, 0xa8, 0xd7, 0x0e, 0xf7, 0xf7, 0xeb,
	0x07, 0x3b, 0x5b, 0xad, 0xc6, 0xe1, 0x41, 0x51, 0x43, 0xaf, 0xc2, 0xed, 0x09, 0x6c, 0x47, 0x75,
	0xa3, 0x79, 0x78, 0x50, 0x4c, 0xac, 0x77, 0xa1, 0x30, 0x1a, 0x95, 0xe8, 0x0e, 0xbc, 0xbc, 0xd5,
	0x6a, 0x35, 0x5a, 0xc7, 0x3b, 0x75, 0x73, 0xab, 0xc6, 0x77, 0x19, 0x13, 0x5c, 0x82, 0xa5, 0x71,
	0x86, 0xc7, 0x8d, 0xaf, 0xd5, 0x8b, 0x1a, 0x7a, 0x19, 0x96, 0xc7, 0x67, 0x76, 0x1a, 0x4d, 0x31,
	0x99, 0x58, 0xff, 0x26, 0xc0, 0x30, 0x70, 0x39, 0xeb, 0xe1, 0xf6, 0xa7, 0xf5, 0x5a, 0xcb, 0x9c,
	0xf0, 0xc0, 0xb9, 0x04, 0xc5, 0xf8, 0xe4, 0xd1, 0x61, 0xb3, 0x55, 0xd4, 0xd0, 0x32, 0xbc, 0x10,
	0xa7, 0xca, 0x73, 0xb6, 0x8a, 0x89, 0xcd, 0xdf, 0x15, 0xe0, 0xc5, 0x78, 0x7b, 0xfd, 0x55, 0xc6,
	0xfc, 0xc7, 0x5c, 0xcb, 0xe8, 0xaf, 0x1a, 0x8f, 0xf9, 0xc9, 0xaf, 0x64, 0xe8, 0xcb, 0xd3, 0xad,
	0x73, 0xc5, 0x3b, 0x60, 0xf9, 0xd1, 0xf3, 0x2c, 0x55, 0x97, 0x9d, 0x8f, 0xbe, 0xf7, 0xb7, 0x7f,
	0xfe, 0x34, 0xf1, 0x81, 0xfe, 0xde, 0xf4, 0x77, 0x7e, 0x1e, 0xbe, 0x55, 0x5f, 0x6e, 0x50, 0xf5,
	0xf9, 0xea, 0x6a, 0x47, 0x6d, 0x8b, 0x7e, 0xa9, 0x41, 0x2e, 0xb6, 0x37, 0x7a, 0xf0, 0x2c, 0xaf,
	0x24, 0xe5, 0x8d, 0x39, 0xb9, 0x15, 0xd6, 0x8a, 0xc0, 0xba, 0xa6, 0xdf, 0x9b, 0x0f, 0x2b, 0xfa,
	0x99, 0x06, 0x30, 0x7c, 0x6f, 0x40, 0xf7, 0xa7, 0x4b, 0xbb, 0xf4, 0xf8, 0x51, 0x7e, 0x30, 0x1f,
	0xb3, 0x42, 0xb6, 0x21, 0x90, 0xbd, 0xa9, 0xaf, 0x5e, 0x81, 0xcc, 0x11, 0x4b, 0xd1, 0xaf, 0x35,
	0xc8, 0xc5, 0xde, 0x08, 0x66, 0x69, 0xed, 0xf2, 0xc3, 0xc8, 0x2c, 0xad, 0x4d, 0x7a, 0x78, 0x78,
	0x47, 0x60, 0xbb, 0xff, 0x48, 0x5b, 0x9f, 0xa5, 0x38, 0x75, 0x75, 0xa8, 0x52, 0x8e, 0xe7, 0x37,
	0x1a, 0xe4, 0xe3, 0x2f, 0x02, 0xe8, 0x6a, 0x91, 0xf1, 0x37, 0x8d, 0x72, 0x65, 0x5e, 0x76, 0x05,
	0x71, 0x53, 0x40, 0x7c, 0xc0, 0x21, 0xbe, 0x79, 0x35, 0xc4, 0x13, 0x01, 0xe9, 0xb7, 0x1a, 0x2c,
	0x8e, 0xdc, 0xdf, 0xd1, 0xd5, 0x52, 0x47, 0x5e, 0x18, 0xca, 0xd5, 0xb9, 0xf9, 0x15, 0xcc, 0x87,
	0x02, 0xe6, 0x06, 0x87, 0xb9, 0x76, 0x35, 0xcc, 0xa1, 0xad, 0xf3, 0xf1, 0x86, 0x0c, 0xbd, 0x35,
	0x2b, 0x5a, 0x47, 0x7a, 0xe2, 0xf2, 0xfa, 0xbc, 0xac, 0xd4, 0xd7, 0xdf, 0x17, 0xe0, 0xde, 0xe6,
	0xe0, 0xee, 0x5f, 0xe1, 0x85, 0x1d, 0xcc, 0x3c, 0x7c, 0x4e, 0x65, 0x97, 0xf7, 0x43, 0x0d, 0xd2,
	0xea, 0xd6, 0x80, 0xee, 0xce, 0x94, 0xa7, 0xc2, 0xa3, 0xbc, 0x3a, 0x07, 0x17, 0xf5, 0xe7, 0xf4,
	0xbb, 0x10, 0x90, 0xb8, 0x30, 0xfc, 0x44, 0x83, 0x4c, 0x74, 0xdd, 0x5c, 0xbd, 0xba, 0x73, 0xe3,
	0x68, 0xee, 0xcd, 0xc3, 0x36, 0xb7, 0x7e, 0xc2, 0xeb, 0x6f, 0xf4, 0x81, 0xfe, 0xa4, 0xc1, 0xad,
	0x4b, 0x1d, 0x07, 0xda, 0x9c, 0xa9, 0x83, 0x89, 0x5d, 0x5d, 0xf9, 0xe1, 0x33, 0xad, 0x51, 0x3e,
	0xf7, 0xb1, 0x80, 0xfd, 0x21, 0x87, 0xfd, 0x70, 0x3a, 0x6c, 0xd5, 0xb6, 0x70, 0x45, 0xaa, 0x4f,
	0x66, 0xf1, 0x1c, 0xc8, 0xd0, 0xaf, 0xa4, 0xfb, 0x45, 0xcd, 0xc3, 0xac, 0x50, 0x9e, 0xd0, 0xeb,
	0xcc, 0x0a, 0xe5, 0x49, 0x3d, 0x89, 0xfe, 0x40, 0xe0, 0xbd, 0xa7, 0xdf, 0x9d, 0x0e, 0x56, 0xdc,
	0xee, 0xab, 0x1c, 0xdd, 0xf6, 0xf1, 0xb7, 0x9a, 0x1d, 0xd2, 0xb3, 0xbc, 0x4e, 0xe5, 0xbd, 0x4d,
	0xc6, 0x2a, 0x36, 0xe9, 0x57, 0xc5, 0x2f, 0x59, 0x9b, 0xf4, 0xaa, 0x14, 0x07, 0x67, 0xae, 0x8d,
	0xe9, 0xb3, 0xff, 0x80, 0x6e, 0xa7, 0xc4, 0x26, 0x0f, 0xff, 0x1b, 0x00, 0x00, 0xff, 0xff, 0xd9,
	0x13, 0x41, 0x79, 0xcb, 0x1e, 0x00, 0x00,
}
