// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/channel-play-tab/channel-play-tab.proto

package channel_play_tab

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockChannelPlayTabClient is a mock of ChannelPlayTabClient interface.
type MockChannelPlayTabClient struct {
	ctrl     *gomock.Controller
	recorder *MockChannelPlayTabClientMockRecorder
}

// MockChannelPlayTabClientMockRecorder is the mock recorder for MockChannelPlayTabClient.
type MockChannelPlayTabClientMockRecorder struct {
	mock *MockChannelPlayTabClient
}

// NewMockChannelPlayTabClient creates a new mock instance.
func NewMockChannelPlayTabClient(ctrl *gomock.Controller) *MockChannelPlayTabClient {
	mock := &MockChannelPlayTabClient{ctrl: ctrl}
	mock.recorder = &MockChannelPlayTabClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelPlayTabClient) EXPECT() *MockChannelPlayTabClientMockRecorder {
	return m.recorder
}

// AddTabsRealNameConfig mocks base method.
func (m *MockChannelPlayTabClient) AddTabsRealNameConfig(ctx context.Context, in *AddTabsRealNameConfigReq, opts ...grpc.CallOption) (*AddTabsRealNameConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddTabsRealNameConfig", varargs...)
	ret0, _ := ret[0].(*AddTabsRealNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddTabsRealNameConfig indicates an expected call of AddTabsRealNameConfig.
func (mr *MockChannelPlayTabClientMockRecorder) AddTabsRealNameConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTabsRealNameConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).AddTabsRealNameConfig), varargs...)
}

// BatchAddBanUserConfig mocks base method.
func (m *MockChannelPlayTabClient) BatchAddBanUserConfig(ctx context.Context, in *BatchAddBanUserConfigReq, opts ...grpc.CallOption) (*BatchAddBanUserConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchAddBanUserConfig", varargs...)
	ret0, _ := ret[0].(*BatchAddBanUserConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAddBanUserConfig indicates an expected call of BatchAddBanUserConfig.
func (mr *MockChannelPlayTabClientMockRecorder) BatchAddBanUserConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddBanUserConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).BatchAddBanUserConfig), varargs...)
}

// BatchGetMinorSupervisionConfig mocks base method.
func (m *MockChannelPlayTabClient) BatchGetMinorSupervisionConfig(ctx context.Context, in *BatchGetMinorSupervisionConfigReq, opts ...grpc.CallOption) (*BatchGetMinorSupervisionConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetMinorSupervisionConfig", varargs...)
	ret0, _ := ret[0].(*BatchGetMinorSupervisionConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetMinorSupervisionConfig indicates an expected call of BatchGetMinorSupervisionConfig.
func (mr *MockChannelPlayTabClientMockRecorder) BatchGetMinorSupervisionConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetMinorSupervisionConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).BatchGetMinorSupervisionConfig), varargs...)
}

// BatchGetNewQuickMatchConfig mocks base method.
func (m *MockChannelPlayTabClient) BatchGetNewQuickMatchConfig(ctx context.Context, in *BatchGetNewQuickMatchConfigReq, opts ...grpc.CallOption) (*BatchGetNewQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetNewQuickMatchConfig", varargs...)
	ret0, _ := ret[0].(*BatchGetNewQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetNewQuickMatchConfig indicates an expected call of BatchGetNewQuickMatchConfig.
func (mr *MockChannelPlayTabClientMockRecorder) BatchGetNewQuickMatchConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetNewQuickMatchConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).BatchGetNewQuickMatchConfig), varargs...)
}

// BatchGetWhiteUidListByTabIds mocks base method.
func (m *MockChannelPlayTabClient) BatchGetWhiteUidListByTabIds(ctx context.Context, in *BatchGetWhiteUidListByTabIdsReq, opts ...grpc.CallOption) (*BatchGetWhiteUidListByTabIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetWhiteUidListByTabIds", varargs...)
	ret0, _ := ret[0].(*BatchGetWhiteUidListByTabIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetWhiteUidListByTabIds indicates an expected call of BatchGetWhiteUidListByTabIds.
func (mr *MockChannelPlayTabClientMockRecorder) BatchGetWhiteUidListByTabIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetWhiteUidListByTabIds", reflect.TypeOf((*MockChannelPlayTabClient)(nil).BatchGetWhiteUidListByTabIds), varargs...)
}

// DelBanUserConfig mocks base method.
func (m *MockChannelPlayTabClient) DelBanUserConfig(ctx context.Context, in *DelBanUserConfigReq, opts ...grpc.CallOption) (*DelBanUserConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelBanUserConfig", varargs...)
	ret0, _ := ret[0].(*DelBanUserConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelBanUserConfig indicates an expected call of DelBanUserConfig.
func (mr *MockChannelPlayTabClientMockRecorder) DelBanUserConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelBanUserConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).DelBanUserConfig), varargs...)
}

// DelFastPCCategoryConfig mocks base method.
func (m *MockChannelPlayTabClient) DelFastPCCategoryConfig(ctx context.Context, in *DelFastPCCategoryConfigReq, opts ...grpc.CallOption) (*DelFastPCCategoryConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelFastPCCategoryConfig", varargs...)
	ret0, _ := ret[0].(*DelFastPCCategoryConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelFastPCCategoryConfig indicates an expected call of DelFastPCCategoryConfig.
func (mr *MockChannelPlayTabClientMockRecorder) DelFastPCCategoryConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelFastPCCategoryConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).DelFastPCCategoryConfig), varargs...)
}

// DelNewQuickMatchConfig mocks base method.
func (m *MockChannelPlayTabClient) DelNewQuickMatchConfig(ctx context.Context, in *DelNewQuickMatchConfigReq, opts ...grpc.CallOption) (*DelNewQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelNewQuickMatchConfig", varargs...)
	ret0, _ := ret[0].(*DelNewQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelNewQuickMatchConfig indicates an expected call of DelNewQuickMatchConfig.
func (mr *MockChannelPlayTabClientMockRecorder) DelNewQuickMatchConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelNewQuickMatchConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).DelNewQuickMatchConfig), varargs...)
}

// DelTabInfoExt mocks base method.
func (m *MockChannelPlayTabClient) DelTabInfoExt(ctx context.Context, in *DelTabInfoExtReq, opts ...grpc.CallOption) (*DelTabInfoExtResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelTabInfoExt", varargs...)
	ret0, _ := ret[0].(*DelTabInfoExtResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelTabInfoExt indicates an expected call of DelTabInfoExt.
func (mr *MockChannelPlayTabClientMockRecorder) DelTabInfoExt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelTabInfoExt", reflect.TypeOf((*MockChannelPlayTabClient)(nil).DelTabInfoExt), varargs...)
}

// DeleteQuickMatchConfig mocks base method.
func (m *MockChannelPlayTabClient) DeleteQuickMatchConfig(ctx context.Context, in *DeleteQuickMatchConfigReq, opts ...grpc.CallOption) (*DeleteQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteQuickMatchConfig", varargs...)
	ret0, _ := ret[0].(*DeleteQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteQuickMatchConfig indicates an expected call of DeleteQuickMatchConfig.
func (mr *MockChannelPlayTabClientMockRecorder) DeleteQuickMatchConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteQuickMatchConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).DeleteQuickMatchConfig), varargs...)
}

// DeleteTab mocks base method.
func (m *MockChannelPlayTabClient) DeleteTab(ctx context.Context, in *DeleteTabReq, opts ...grpc.CallOption) (*DeleteTabResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTab", varargs...)
	ret0, _ := ret[0].(*DeleteTabResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTab indicates an expected call of DeleteTab.
func (mr *MockChannelPlayTabClientMockRecorder) DeleteTab(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTab", reflect.TypeOf((*MockChannelPlayTabClient)(nil).DeleteTab), varargs...)
}

// DeleteTabsRealNameConfig mocks base method.
func (m *MockChannelPlayTabClient) DeleteTabsRealNameConfig(ctx context.Context, in *DeleteTabsRealNameConfigReq, opts ...grpc.CallOption) (*DeleteTabsRealNameConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTabsRealNameConfig", varargs...)
	ret0, _ := ret[0].(*DeleteTabsRealNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTabsRealNameConfig indicates an expected call of DeleteTabsRealNameConfig.
func (mr *MockChannelPlayTabClientMockRecorder) DeleteTabsRealNameConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTabsRealNameConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).DeleteTabsRealNameConfig), varargs...)
}

// GetActiveBanUserConfigWithCache mocks base method.
func (m *MockChannelPlayTabClient) GetActiveBanUserConfigWithCache(ctx context.Context, in *GetActiveBanUserConfigWithCacheReq, opts ...grpc.CallOption) (*GetActiveBanUserConfigWithCacheResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetActiveBanUserConfigWithCache", varargs...)
	ret0, _ := ret[0].(*GetActiveBanUserConfigWithCacheResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveBanUserConfigWithCache indicates an expected call of GetActiveBanUserConfigWithCache.
func (mr *MockChannelPlayTabClientMockRecorder) GetActiveBanUserConfigWithCache(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveBanUserConfigWithCache", reflect.TypeOf((*MockChannelPlayTabClient)(nil).GetActiveBanUserConfigWithCache), varargs...)
}

// GetAllTabInfoExt mocks base method.
func (m *MockChannelPlayTabClient) GetAllTabInfoExt(ctx context.Context, in *GetAllTabInfoExtReq, opts ...grpc.CallOption) (*GetAllTabInfoExtResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllTabInfoExt", varargs...)
	ret0, _ := ret[0].(*GetAllTabInfoExtResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllTabInfoExt indicates an expected call of GetAllTabInfoExt.
func (mr *MockChannelPlayTabClientMockRecorder) GetAllTabInfoExt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTabInfoExt", reflect.TypeOf((*MockChannelPlayTabClient)(nil).GetAllTabInfoExt), varargs...)
}

// GetBanGameHallUser mocks base method.
func (m *MockChannelPlayTabClient) GetBanGameHallUser(ctx context.Context, in *GetBanGameHallUserReq, opts ...grpc.CallOption) (*GetBanGameHallUserResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBanGameHallUser", varargs...)
	ret0, _ := ret[0].(*GetBanGameHallUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBanGameHallUser indicates an expected call of GetBanGameHallUser.
func (mr *MockChannelPlayTabClientMockRecorder) GetBanGameHallUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBanGameHallUser", reflect.TypeOf((*MockChannelPlayTabClient)(nil).GetBanGameHallUser), varargs...)
}

// GetBanUserConfigList mocks base method.
func (m *MockChannelPlayTabClient) GetBanUserConfigList(ctx context.Context, in *GetBanUserConfigListReq, opts ...grpc.CallOption) (*GetBanUserConfigListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBanUserConfigList", varargs...)
	ret0, _ := ret[0].(*GetBanUserConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBanUserConfigList indicates an expected call of GetBanUserConfigList.
func (mr *MockChannelPlayTabClientMockRecorder) GetBanUserConfigList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBanUserConfigList", reflect.TypeOf((*MockChannelPlayTabClient)(nil).GetBanUserConfigList), varargs...)
}

// GetFastPCCategoryConfig mocks base method.
func (m *MockChannelPlayTabClient) GetFastPCCategoryConfig(ctx context.Context, in *GetFastPCCategoryConfigReq, opts ...grpc.CallOption) (*GetFastPCCategoryConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFastPCCategoryConfig", varargs...)
	ret0, _ := ret[0].(*GetFastPCCategoryConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFastPCCategoryConfig indicates an expected call of GetFastPCCategoryConfig.
func (mr *MockChannelPlayTabClientMockRecorder) GetFastPCCategoryConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFastPCCategoryConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).GetFastPCCategoryConfig), varargs...)
}

// GetNewQuickMatchConfig mocks base method.
func (m *MockChannelPlayTabClient) GetNewQuickMatchConfig(ctx context.Context, in *GetNewQuickMatchConfigReq, opts ...grpc.CallOption) (*GetNewQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNewQuickMatchConfig", varargs...)
	ret0, _ := ret[0].(*GetNewQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewQuickMatchConfig indicates an expected call of GetNewQuickMatchConfig.
func (mr *MockChannelPlayTabClientMockRecorder) GetNewQuickMatchConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewQuickMatchConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).GetNewQuickMatchConfig), varargs...)
}

// GetQuickMatchConfig mocks base method.
func (m *MockChannelPlayTabClient) GetQuickMatchConfig(ctx context.Context, in *GetQuickMatchConfigReq, opts ...grpc.CallOption) (*GetQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetQuickMatchConfig", varargs...)
	ret0, _ := ret[0].(*GetQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQuickMatchConfig indicates an expected call of GetQuickMatchConfig.
func (mr *MockChannelPlayTabClientMockRecorder) GetQuickMatchConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuickMatchConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).GetQuickMatchConfig), varargs...)
}

// GetTabs mocks base method.
func (m *MockChannelPlayTabClient) GetTabs(ctx context.Context, in *GetTabsReq, opts ...grpc.CallOption) (*GetTabsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTabs", varargs...)
	ret0, _ := ret[0].(*GetTabsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabs indicates an expected call of GetTabs.
func (mr *MockChannelPlayTabClientMockRecorder) GetTabs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabs", reflect.TypeOf((*MockChannelPlayTabClient)(nil).GetTabs), varargs...)
}

// GetTabsByTabSubType mocks base method.
func (m *MockChannelPlayTabClient) GetTabsByTabSubType(ctx context.Context, in *GetTabsByTabSubTypeReq, opts ...grpc.CallOption) (*GetTabsByTabSubTypeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTabsByTabSubType", varargs...)
	ret0, _ := ret[0].(*GetTabsByTabSubTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabsByTabSubType indicates an expected call of GetTabsByTabSubType.
func (mr *MockChannelPlayTabClientMockRecorder) GetTabsByTabSubType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabsByTabSubType", reflect.TypeOf((*MockChannelPlayTabClient)(nil).GetTabsByTabSubType), varargs...)
}

// GetTabsRealNameConfigs mocks base method.
func (m *MockChannelPlayTabClient) GetTabsRealNameConfigs(ctx context.Context, in *GetTabsRealNameConfigsReq, opts ...grpc.CallOption) (*GetTabsRealNameConfigsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTabsRealNameConfigs", varargs...)
	ret0, _ := ret[0].(*GetTabsRealNameConfigsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabsRealNameConfigs indicates an expected call of GetTabsRealNameConfigs.
func (mr *MockChannelPlayTabClientMockRecorder) GetTabsRealNameConfigs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabsRealNameConfigs", reflect.TypeOf((*MockChannelPlayTabClient)(nil).GetTabsRealNameConfigs), varargs...)
}

// GetUserTabsRealNameConfig mocks base method.
func (m *MockChannelPlayTabClient) GetUserTabsRealNameConfig(ctx context.Context, in *GetUserTabsRealNameConfigReq, opts ...grpc.CallOption) (*GetUserTabsRealNameConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserTabsRealNameConfig", varargs...)
	ret0, _ := ret[0].(*GetUserTabsRealNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserTabsRealNameConfig indicates an expected call of GetUserTabsRealNameConfig.
func (mr *MockChannelPlayTabClientMockRecorder) GetUserTabsRealNameConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTabsRealNameConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).GetUserTabsRealNameConfig), varargs...)
}

// ResortQuickMatchConfig mocks base method.
func (m *MockChannelPlayTabClient) ResortQuickMatchConfig(ctx context.Context, in *ResortQuickMatchConfigReq, opts ...grpc.CallOption) (*ResortQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResortQuickMatchConfig", varargs...)
	ret0, _ := ret[0].(*ResortQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResortQuickMatchConfig indicates an expected call of ResortQuickMatchConfig.
func (mr *MockChannelPlayTabClientMockRecorder) ResortQuickMatchConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResortQuickMatchConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).ResortQuickMatchConfig), varargs...)
}

// SetWhiteUidListByTabId mocks base method.
func (m *MockChannelPlayTabClient) SetWhiteUidListByTabId(ctx context.Context, in *SetWhiteUidListByTabIdReq, opts ...grpc.CallOption) (*SetWhiteUidListByTabIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetWhiteUidListByTabId", varargs...)
	ret0, _ := ret[0].(*SetWhiteUidListByTabIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetWhiteUidListByTabId indicates an expected call of SetWhiteUidListByTabId.
func (mr *MockChannelPlayTabClientMockRecorder) SetWhiteUidListByTabId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWhiteUidListByTabId", reflect.TypeOf((*MockChannelPlayTabClient)(nil).SetWhiteUidListByTabId), varargs...)
}

// SortFastPCCategoryConfig mocks base method.
func (m *MockChannelPlayTabClient) SortFastPCCategoryConfig(ctx context.Context, in *SortFastPCCategoryConfigReq, opts ...grpc.CallOption) (*SortFastPCCategoryConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SortFastPCCategoryConfig", varargs...)
	ret0, _ := ret[0].(*SortFastPCCategoryConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SortFastPCCategoryConfig indicates an expected call of SortFastPCCategoryConfig.
func (mr *MockChannelPlayTabClientMockRecorder) SortFastPCCategoryConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SortFastPCCategoryConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).SortFastPCCategoryConfig), varargs...)
}

// UpdateBanGameHallGetStatus mocks base method.
func (m *MockChannelPlayTabClient) UpdateBanGameHallGetStatus(ctx context.Context, in *UpdateBanGameHallGetStatusReq, opts ...grpc.CallOption) (*UpdateBanGameHallGetStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateBanGameHallGetStatus", varargs...)
	ret0, _ := ret[0].(*UpdateBanGameHallGetStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBanGameHallGetStatus indicates an expected call of UpdateBanGameHallGetStatus.
func (mr *MockChannelPlayTabClientMockRecorder) UpdateBanGameHallGetStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBanGameHallGetStatus", reflect.TypeOf((*MockChannelPlayTabClient)(nil).UpdateBanGameHallGetStatus), varargs...)
}

// UpdateQuickMatchConfig mocks base method.
func (m *MockChannelPlayTabClient) UpdateQuickMatchConfig(ctx context.Context, in *UpdateQuickMatchConfigReq, opts ...grpc.CallOption) (*UpdateQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateQuickMatchConfig", varargs...)
	ret0, _ := ret[0].(*UpdateQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateQuickMatchConfig indicates an expected call of UpdateQuickMatchConfig.
func (mr *MockChannelPlayTabClientMockRecorder) UpdateQuickMatchConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateQuickMatchConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).UpdateQuickMatchConfig), varargs...)
}

// UpdateTabsRealNameConfig mocks base method.
func (m *MockChannelPlayTabClient) UpdateTabsRealNameConfig(ctx context.Context, in *UpdateTabsRealNameConfigReq, opts ...grpc.CallOption) (*UpdateTabsRealNameConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTabsRealNameConfig", varargs...)
	ret0, _ := ret[0].(*UpdateTabsRealNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTabsRealNameConfig indicates an expected call of UpdateTabsRealNameConfig.
func (mr *MockChannelPlayTabClientMockRecorder) UpdateTabsRealNameConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTabsRealNameConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).UpdateTabsRealNameConfig), varargs...)
}

// UpsertBanUserConfig mocks base method.
func (m *MockChannelPlayTabClient) UpsertBanUserConfig(ctx context.Context, in *UpsertBanUserConfigReq, opts ...grpc.CallOption) (*UpsertBanUserConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertBanUserConfig", varargs...)
	ret0, _ := ret[0].(*UpsertBanUserConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertBanUserConfig indicates an expected call of UpsertBanUserConfig.
func (mr *MockChannelPlayTabClientMockRecorder) UpsertBanUserConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertBanUserConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).UpsertBanUserConfig), varargs...)
}

// UpsertFastPCCategoryConfig mocks base method.
func (m *MockChannelPlayTabClient) UpsertFastPCCategoryConfig(ctx context.Context, in *UpsertFastPCCategoryConfigReq, opts ...grpc.CallOption) (*UpsertFastPCCategoryConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertFastPCCategoryConfig", varargs...)
	ret0, _ := ret[0].(*UpsertFastPCCategoryConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertFastPCCategoryConfig indicates an expected call of UpsertFastPCCategoryConfig.
func (mr *MockChannelPlayTabClientMockRecorder) UpsertFastPCCategoryConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertFastPCCategoryConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).UpsertFastPCCategoryConfig), varargs...)
}

// UpsertMinorSupervisionConfig mocks base method.
func (m *MockChannelPlayTabClient) UpsertMinorSupervisionConfig(ctx context.Context, in *UpsertMinorSupervisionConfigReq, opts ...grpc.CallOption) (*UpsertMinorSupervisionConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertMinorSupervisionConfig", varargs...)
	ret0, _ := ret[0].(*UpsertMinorSupervisionConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertMinorSupervisionConfig indicates an expected call of UpsertMinorSupervisionConfig.
func (mr *MockChannelPlayTabClientMockRecorder) UpsertMinorSupervisionConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertMinorSupervisionConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).UpsertMinorSupervisionConfig), varargs...)
}

// UpsertNewQuickMatchConfig mocks base method.
func (m *MockChannelPlayTabClient) UpsertNewQuickMatchConfig(ctx context.Context, in *UpsertNewQuickMatchConfigReq, opts ...grpc.CallOption) (*UpsertNewQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertNewQuickMatchConfig", varargs...)
	ret0, _ := ret[0].(*UpsertNewQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertNewQuickMatchConfig indicates an expected call of UpsertNewQuickMatchConfig.
func (mr *MockChannelPlayTabClientMockRecorder) UpsertNewQuickMatchConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertNewQuickMatchConfig", reflect.TypeOf((*MockChannelPlayTabClient)(nil).UpsertNewQuickMatchConfig), varargs...)
}

// UpsertTab mocks base method.
func (m *MockChannelPlayTabClient) UpsertTab(ctx context.Context, in *UpsertTabReq, opts ...grpc.CallOption) (*UpsertTabResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertTab", varargs...)
	ret0, _ := ret[0].(*UpsertTabResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertTab indicates an expected call of UpsertTab.
func (mr *MockChannelPlayTabClientMockRecorder) UpsertTab(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertTab", reflect.TypeOf((*MockChannelPlayTabClient)(nil).UpsertTab), varargs...)
}

// UpsertTabInfoExt mocks base method.
func (m *MockChannelPlayTabClient) UpsertTabInfoExt(ctx context.Context, in *UpsertTabInfoExtReq, opts ...grpc.CallOption) (*UpsertTabInfoExtResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertTabInfoExt", varargs...)
	ret0, _ := ret[0].(*UpsertTabInfoExtResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertTabInfoExt indicates an expected call of UpsertTabInfoExt.
func (mr *MockChannelPlayTabClientMockRecorder) UpsertTabInfoExt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertTabInfoExt", reflect.TypeOf((*MockChannelPlayTabClient)(nil).UpsertTabInfoExt), varargs...)
}

// MockChannelPlayTabServer is a mock of ChannelPlayTabServer interface.
type MockChannelPlayTabServer struct {
	ctrl     *gomock.Controller
	recorder *MockChannelPlayTabServerMockRecorder
}

// MockChannelPlayTabServerMockRecorder is the mock recorder for MockChannelPlayTabServer.
type MockChannelPlayTabServerMockRecorder struct {
	mock *MockChannelPlayTabServer
}

// NewMockChannelPlayTabServer creates a new mock instance.
func NewMockChannelPlayTabServer(ctrl *gomock.Controller) *MockChannelPlayTabServer {
	mock := &MockChannelPlayTabServer{ctrl: ctrl}
	mock.recorder = &MockChannelPlayTabServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelPlayTabServer) EXPECT() *MockChannelPlayTabServerMockRecorder {
	return m.recorder
}

// AddTabsRealNameConfig mocks base method.
func (m *MockChannelPlayTabServer) AddTabsRealNameConfig(ctx context.Context, in *AddTabsRealNameConfigReq) (*AddTabsRealNameConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddTabsRealNameConfig", ctx, in)
	ret0, _ := ret[0].(*AddTabsRealNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddTabsRealNameConfig indicates an expected call of AddTabsRealNameConfig.
func (mr *MockChannelPlayTabServerMockRecorder) AddTabsRealNameConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTabsRealNameConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).AddTabsRealNameConfig), ctx, in)
}

// BatchAddBanUserConfig mocks base method.
func (m *MockChannelPlayTabServer) BatchAddBanUserConfig(ctx context.Context, in *BatchAddBanUserConfigReq) (*BatchAddBanUserConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddBanUserConfig", ctx, in)
	ret0, _ := ret[0].(*BatchAddBanUserConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAddBanUserConfig indicates an expected call of BatchAddBanUserConfig.
func (mr *MockChannelPlayTabServerMockRecorder) BatchAddBanUserConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddBanUserConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).BatchAddBanUserConfig), ctx, in)
}

// BatchGetMinorSupervisionConfig mocks base method.
func (m *MockChannelPlayTabServer) BatchGetMinorSupervisionConfig(ctx context.Context, in *BatchGetMinorSupervisionConfigReq) (*BatchGetMinorSupervisionConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetMinorSupervisionConfig", ctx, in)
	ret0, _ := ret[0].(*BatchGetMinorSupervisionConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetMinorSupervisionConfig indicates an expected call of BatchGetMinorSupervisionConfig.
func (mr *MockChannelPlayTabServerMockRecorder) BatchGetMinorSupervisionConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetMinorSupervisionConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).BatchGetMinorSupervisionConfig), ctx, in)
}

// BatchGetNewQuickMatchConfig mocks base method.
func (m *MockChannelPlayTabServer) BatchGetNewQuickMatchConfig(ctx context.Context, in *BatchGetNewQuickMatchConfigReq) (*BatchGetNewQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetNewQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*BatchGetNewQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetNewQuickMatchConfig indicates an expected call of BatchGetNewQuickMatchConfig.
func (mr *MockChannelPlayTabServerMockRecorder) BatchGetNewQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetNewQuickMatchConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).BatchGetNewQuickMatchConfig), ctx, in)
}

// BatchGetWhiteUidListByTabIds mocks base method.
func (m *MockChannelPlayTabServer) BatchGetWhiteUidListByTabIds(ctx context.Context, in *BatchGetWhiteUidListByTabIdsReq) (*BatchGetWhiteUidListByTabIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetWhiteUidListByTabIds", ctx, in)
	ret0, _ := ret[0].(*BatchGetWhiteUidListByTabIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetWhiteUidListByTabIds indicates an expected call of BatchGetWhiteUidListByTabIds.
func (mr *MockChannelPlayTabServerMockRecorder) BatchGetWhiteUidListByTabIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetWhiteUidListByTabIds", reflect.TypeOf((*MockChannelPlayTabServer)(nil).BatchGetWhiteUidListByTabIds), ctx, in)
}

// DelBanUserConfig mocks base method.
func (m *MockChannelPlayTabServer) DelBanUserConfig(ctx context.Context, in *DelBanUserConfigReq) (*DelBanUserConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelBanUserConfig", ctx, in)
	ret0, _ := ret[0].(*DelBanUserConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelBanUserConfig indicates an expected call of DelBanUserConfig.
func (mr *MockChannelPlayTabServerMockRecorder) DelBanUserConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelBanUserConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).DelBanUserConfig), ctx, in)
}

// DelFastPCCategoryConfig mocks base method.
func (m *MockChannelPlayTabServer) DelFastPCCategoryConfig(ctx context.Context, in *DelFastPCCategoryConfigReq) (*DelFastPCCategoryConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelFastPCCategoryConfig", ctx, in)
	ret0, _ := ret[0].(*DelFastPCCategoryConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelFastPCCategoryConfig indicates an expected call of DelFastPCCategoryConfig.
func (mr *MockChannelPlayTabServerMockRecorder) DelFastPCCategoryConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelFastPCCategoryConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).DelFastPCCategoryConfig), ctx, in)
}

// DelNewQuickMatchConfig mocks base method.
func (m *MockChannelPlayTabServer) DelNewQuickMatchConfig(ctx context.Context, in *DelNewQuickMatchConfigReq) (*DelNewQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelNewQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*DelNewQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelNewQuickMatchConfig indicates an expected call of DelNewQuickMatchConfig.
func (mr *MockChannelPlayTabServerMockRecorder) DelNewQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelNewQuickMatchConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).DelNewQuickMatchConfig), ctx, in)
}

// DelTabInfoExt mocks base method.
func (m *MockChannelPlayTabServer) DelTabInfoExt(ctx context.Context, in *DelTabInfoExtReq) (*DelTabInfoExtResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelTabInfoExt", ctx, in)
	ret0, _ := ret[0].(*DelTabInfoExtResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelTabInfoExt indicates an expected call of DelTabInfoExt.
func (mr *MockChannelPlayTabServerMockRecorder) DelTabInfoExt(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelTabInfoExt", reflect.TypeOf((*MockChannelPlayTabServer)(nil).DelTabInfoExt), ctx, in)
}

// DeleteQuickMatchConfig mocks base method.
func (m *MockChannelPlayTabServer) DeleteQuickMatchConfig(ctx context.Context, in *DeleteQuickMatchConfigReq) (*DeleteQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*DeleteQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteQuickMatchConfig indicates an expected call of DeleteQuickMatchConfig.
func (mr *MockChannelPlayTabServerMockRecorder) DeleteQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteQuickMatchConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).DeleteQuickMatchConfig), ctx, in)
}

// DeleteTab mocks base method.
func (m *MockChannelPlayTabServer) DeleteTab(ctx context.Context, in *DeleteTabReq) (*DeleteTabResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTab", ctx, in)
	ret0, _ := ret[0].(*DeleteTabResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTab indicates an expected call of DeleteTab.
func (mr *MockChannelPlayTabServerMockRecorder) DeleteTab(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTab", reflect.TypeOf((*MockChannelPlayTabServer)(nil).DeleteTab), ctx, in)
}

// DeleteTabsRealNameConfig mocks base method.
func (m *MockChannelPlayTabServer) DeleteTabsRealNameConfig(ctx context.Context, in *DeleteTabsRealNameConfigReq) (*DeleteTabsRealNameConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTabsRealNameConfig", ctx, in)
	ret0, _ := ret[0].(*DeleteTabsRealNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTabsRealNameConfig indicates an expected call of DeleteTabsRealNameConfig.
func (mr *MockChannelPlayTabServerMockRecorder) DeleteTabsRealNameConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTabsRealNameConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).DeleteTabsRealNameConfig), ctx, in)
}

// GetActiveBanUserConfigWithCache mocks base method.
func (m *MockChannelPlayTabServer) GetActiveBanUserConfigWithCache(ctx context.Context, in *GetActiveBanUserConfigWithCacheReq) (*GetActiveBanUserConfigWithCacheResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveBanUserConfigWithCache", ctx, in)
	ret0, _ := ret[0].(*GetActiveBanUserConfigWithCacheResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveBanUserConfigWithCache indicates an expected call of GetActiveBanUserConfigWithCache.
func (mr *MockChannelPlayTabServerMockRecorder) GetActiveBanUserConfigWithCache(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveBanUserConfigWithCache", reflect.TypeOf((*MockChannelPlayTabServer)(nil).GetActiveBanUserConfigWithCache), ctx, in)
}

// GetAllTabInfoExt mocks base method.
func (m *MockChannelPlayTabServer) GetAllTabInfoExt(ctx context.Context, in *GetAllTabInfoExtReq) (*GetAllTabInfoExtResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllTabInfoExt", ctx, in)
	ret0, _ := ret[0].(*GetAllTabInfoExtResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllTabInfoExt indicates an expected call of GetAllTabInfoExt.
func (mr *MockChannelPlayTabServerMockRecorder) GetAllTabInfoExt(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTabInfoExt", reflect.TypeOf((*MockChannelPlayTabServer)(nil).GetAllTabInfoExt), ctx, in)
}

// GetBanGameHallUser mocks base method.
func (m *MockChannelPlayTabServer) GetBanGameHallUser(ctx context.Context, in *GetBanGameHallUserReq) (*GetBanGameHallUserResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBanGameHallUser", ctx, in)
	ret0, _ := ret[0].(*GetBanGameHallUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBanGameHallUser indicates an expected call of GetBanGameHallUser.
func (mr *MockChannelPlayTabServerMockRecorder) GetBanGameHallUser(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBanGameHallUser", reflect.TypeOf((*MockChannelPlayTabServer)(nil).GetBanGameHallUser), ctx, in)
}

// GetBanUserConfigList mocks base method.
func (m *MockChannelPlayTabServer) GetBanUserConfigList(ctx context.Context, in *GetBanUserConfigListReq) (*GetBanUserConfigListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBanUserConfigList", ctx, in)
	ret0, _ := ret[0].(*GetBanUserConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBanUserConfigList indicates an expected call of GetBanUserConfigList.
func (mr *MockChannelPlayTabServerMockRecorder) GetBanUserConfigList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBanUserConfigList", reflect.TypeOf((*MockChannelPlayTabServer)(nil).GetBanUserConfigList), ctx, in)
}

// GetFastPCCategoryConfig mocks base method.
func (m *MockChannelPlayTabServer) GetFastPCCategoryConfig(ctx context.Context, in *GetFastPCCategoryConfigReq) (*GetFastPCCategoryConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFastPCCategoryConfig", ctx, in)
	ret0, _ := ret[0].(*GetFastPCCategoryConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFastPCCategoryConfig indicates an expected call of GetFastPCCategoryConfig.
func (mr *MockChannelPlayTabServerMockRecorder) GetFastPCCategoryConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFastPCCategoryConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).GetFastPCCategoryConfig), ctx, in)
}

// GetNewQuickMatchConfig mocks base method.
func (m *MockChannelPlayTabServer) GetNewQuickMatchConfig(ctx context.Context, in *GetNewQuickMatchConfigReq) (*GetNewQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*GetNewQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewQuickMatchConfig indicates an expected call of GetNewQuickMatchConfig.
func (mr *MockChannelPlayTabServerMockRecorder) GetNewQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewQuickMatchConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).GetNewQuickMatchConfig), ctx, in)
}

// GetQuickMatchConfig mocks base method.
func (m *MockChannelPlayTabServer) GetQuickMatchConfig(ctx context.Context, in *GetQuickMatchConfigReq) (*GetQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*GetQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQuickMatchConfig indicates an expected call of GetQuickMatchConfig.
func (mr *MockChannelPlayTabServerMockRecorder) GetQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuickMatchConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).GetQuickMatchConfig), ctx, in)
}

// GetTabs mocks base method.
func (m *MockChannelPlayTabServer) GetTabs(ctx context.Context, in *GetTabsReq) (*GetTabsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabs", ctx, in)
	ret0, _ := ret[0].(*GetTabsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabs indicates an expected call of GetTabs.
func (mr *MockChannelPlayTabServerMockRecorder) GetTabs(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabs", reflect.TypeOf((*MockChannelPlayTabServer)(nil).GetTabs), ctx, in)
}

// GetTabsByTabSubType mocks base method.
func (m *MockChannelPlayTabServer) GetTabsByTabSubType(ctx context.Context, in *GetTabsByTabSubTypeReq) (*GetTabsByTabSubTypeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabsByTabSubType", ctx, in)
	ret0, _ := ret[0].(*GetTabsByTabSubTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabsByTabSubType indicates an expected call of GetTabsByTabSubType.
func (mr *MockChannelPlayTabServerMockRecorder) GetTabsByTabSubType(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabsByTabSubType", reflect.TypeOf((*MockChannelPlayTabServer)(nil).GetTabsByTabSubType), ctx, in)
}

// GetTabsRealNameConfigs mocks base method.
func (m *MockChannelPlayTabServer) GetTabsRealNameConfigs(ctx context.Context, in *GetTabsRealNameConfigsReq) (*GetTabsRealNameConfigsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabsRealNameConfigs", ctx, in)
	ret0, _ := ret[0].(*GetTabsRealNameConfigsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabsRealNameConfigs indicates an expected call of GetTabsRealNameConfigs.
func (mr *MockChannelPlayTabServerMockRecorder) GetTabsRealNameConfigs(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabsRealNameConfigs", reflect.TypeOf((*MockChannelPlayTabServer)(nil).GetTabsRealNameConfigs), ctx, in)
}

// GetUserTabsRealNameConfig mocks base method.
func (m *MockChannelPlayTabServer) GetUserTabsRealNameConfig(ctx context.Context, in *GetUserTabsRealNameConfigReq) (*GetUserTabsRealNameConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserTabsRealNameConfig", ctx, in)
	ret0, _ := ret[0].(*GetUserTabsRealNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserTabsRealNameConfig indicates an expected call of GetUserTabsRealNameConfig.
func (mr *MockChannelPlayTabServerMockRecorder) GetUserTabsRealNameConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTabsRealNameConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).GetUserTabsRealNameConfig), ctx, in)
}

// ResortQuickMatchConfig mocks base method.
func (m *MockChannelPlayTabServer) ResortQuickMatchConfig(ctx context.Context, in *ResortQuickMatchConfigReq) (*ResortQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResortQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*ResortQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResortQuickMatchConfig indicates an expected call of ResortQuickMatchConfig.
func (mr *MockChannelPlayTabServerMockRecorder) ResortQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResortQuickMatchConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).ResortQuickMatchConfig), ctx, in)
}

// SetWhiteUidListByTabId mocks base method.
func (m *MockChannelPlayTabServer) SetWhiteUidListByTabId(ctx context.Context, in *SetWhiteUidListByTabIdReq) (*SetWhiteUidListByTabIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWhiteUidListByTabId", ctx, in)
	ret0, _ := ret[0].(*SetWhiteUidListByTabIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetWhiteUidListByTabId indicates an expected call of SetWhiteUidListByTabId.
func (mr *MockChannelPlayTabServerMockRecorder) SetWhiteUidListByTabId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWhiteUidListByTabId", reflect.TypeOf((*MockChannelPlayTabServer)(nil).SetWhiteUidListByTabId), ctx, in)
}

// SortFastPCCategoryConfig mocks base method.
func (m *MockChannelPlayTabServer) SortFastPCCategoryConfig(ctx context.Context, in *SortFastPCCategoryConfigReq) (*SortFastPCCategoryConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SortFastPCCategoryConfig", ctx, in)
	ret0, _ := ret[0].(*SortFastPCCategoryConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SortFastPCCategoryConfig indicates an expected call of SortFastPCCategoryConfig.
func (mr *MockChannelPlayTabServerMockRecorder) SortFastPCCategoryConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SortFastPCCategoryConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).SortFastPCCategoryConfig), ctx, in)
}

// UpdateBanGameHallGetStatus mocks base method.
func (m *MockChannelPlayTabServer) UpdateBanGameHallGetStatus(ctx context.Context, in *UpdateBanGameHallGetStatusReq) (*UpdateBanGameHallGetStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBanGameHallGetStatus", ctx, in)
	ret0, _ := ret[0].(*UpdateBanGameHallGetStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBanGameHallGetStatus indicates an expected call of UpdateBanGameHallGetStatus.
func (mr *MockChannelPlayTabServerMockRecorder) UpdateBanGameHallGetStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBanGameHallGetStatus", reflect.TypeOf((*MockChannelPlayTabServer)(nil).UpdateBanGameHallGetStatus), ctx, in)
}

// UpdateQuickMatchConfig mocks base method.
func (m *MockChannelPlayTabServer) UpdateQuickMatchConfig(ctx context.Context, in *UpdateQuickMatchConfigReq) (*UpdateQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*UpdateQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateQuickMatchConfig indicates an expected call of UpdateQuickMatchConfig.
func (mr *MockChannelPlayTabServerMockRecorder) UpdateQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateQuickMatchConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).UpdateQuickMatchConfig), ctx, in)
}

// UpdateTabsRealNameConfig mocks base method.
func (m *MockChannelPlayTabServer) UpdateTabsRealNameConfig(ctx context.Context, in *UpdateTabsRealNameConfigReq) (*UpdateTabsRealNameConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTabsRealNameConfig", ctx, in)
	ret0, _ := ret[0].(*UpdateTabsRealNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTabsRealNameConfig indicates an expected call of UpdateTabsRealNameConfig.
func (mr *MockChannelPlayTabServerMockRecorder) UpdateTabsRealNameConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTabsRealNameConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).UpdateTabsRealNameConfig), ctx, in)
}

// UpsertBanUserConfig mocks base method.
func (m *MockChannelPlayTabServer) UpsertBanUserConfig(ctx context.Context, in *UpsertBanUserConfigReq) (*UpsertBanUserConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertBanUserConfig", ctx, in)
	ret0, _ := ret[0].(*UpsertBanUserConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertBanUserConfig indicates an expected call of UpsertBanUserConfig.
func (mr *MockChannelPlayTabServerMockRecorder) UpsertBanUserConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertBanUserConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).UpsertBanUserConfig), ctx, in)
}

// UpsertFastPCCategoryConfig mocks base method.
func (m *MockChannelPlayTabServer) UpsertFastPCCategoryConfig(ctx context.Context, in *UpsertFastPCCategoryConfigReq) (*UpsertFastPCCategoryConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertFastPCCategoryConfig", ctx, in)
	ret0, _ := ret[0].(*UpsertFastPCCategoryConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertFastPCCategoryConfig indicates an expected call of UpsertFastPCCategoryConfig.
func (mr *MockChannelPlayTabServerMockRecorder) UpsertFastPCCategoryConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertFastPCCategoryConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).UpsertFastPCCategoryConfig), ctx, in)
}

// UpsertMinorSupervisionConfig mocks base method.
func (m *MockChannelPlayTabServer) UpsertMinorSupervisionConfig(ctx context.Context, in *UpsertMinorSupervisionConfigReq) (*UpsertMinorSupervisionConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertMinorSupervisionConfig", ctx, in)
	ret0, _ := ret[0].(*UpsertMinorSupervisionConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertMinorSupervisionConfig indicates an expected call of UpsertMinorSupervisionConfig.
func (mr *MockChannelPlayTabServerMockRecorder) UpsertMinorSupervisionConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertMinorSupervisionConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).UpsertMinorSupervisionConfig), ctx, in)
}

// UpsertNewQuickMatchConfig mocks base method.
func (m *MockChannelPlayTabServer) UpsertNewQuickMatchConfig(ctx context.Context, in *UpsertNewQuickMatchConfigReq) (*UpsertNewQuickMatchConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertNewQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*UpsertNewQuickMatchConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertNewQuickMatchConfig indicates an expected call of UpsertNewQuickMatchConfig.
func (mr *MockChannelPlayTabServerMockRecorder) UpsertNewQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertNewQuickMatchConfig", reflect.TypeOf((*MockChannelPlayTabServer)(nil).UpsertNewQuickMatchConfig), ctx, in)
}

// UpsertTab mocks base method.
func (m *MockChannelPlayTabServer) UpsertTab(ctx context.Context, in *UpsertTabReq) (*UpsertTabResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertTab", ctx, in)
	ret0, _ := ret[0].(*UpsertTabResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertTab indicates an expected call of UpsertTab.
func (mr *MockChannelPlayTabServerMockRecorder) UpsertTab(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertTab", reflect.TypeOf((*MockChannelPlayTabServer)(nil).UpsertTab), ctx, in)
}

// UpsertTabInfoExt mocks base method.
func (m *MockChannelPlayTabServer) UpsertTabInfoExt(ctx context.Context, in *UpsertTabInfoExtReq) (*UpsertTabInfoExtResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertTabInfoExt", ctx, in)
	ret0, _ := ret[0].(*UpsertTabInfoExtResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertTabInfoExt indicates an expected call of UpsertTabInfoExt.
func (mr *MockChannelPlayTabServerMockRecorder) UpsertTabInfoExt(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertTabInfoExt", reflect.TypeOf((*MockChannelPlayTabServer)(nil).UpsertTabInfoExt), ctx, in)
}
