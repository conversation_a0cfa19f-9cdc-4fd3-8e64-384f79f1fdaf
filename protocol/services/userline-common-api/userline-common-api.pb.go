// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/userline-common-api/userline-common-api.proto

package userline_common_api // import "golang.52tt.com/protocol/services/userline-common-api"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type UserTagList struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TagList              []*UserTagBase `protobuf:"bytes,2,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserTagList) Reset()         { *m = UserTagList{} }
func (m *UserTagList) String() string { return proto.CompactTextString(m) }
func (*UserTagList) ProtoMessage()    {}
func (*UserTagList) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{0}
}
func (m *UserTagList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserTagList.Unmarshal(m, b)
}
func (m *UserTagList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserTagList.Marshal(b, m, deterministic)
}
func (dst *UserTagList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserTagList.Merge(dst, src)
}
func (m *UserTagList) XXX_Size() int {
	return xxx_messageInfo_UserTagList.Size(m)
}
func (m *UserTagList) XXX_DiscardUnknown() {
	xxx_messageInfo_UserTagList.DiscardUnknown(m)
}

var xxx_messageInfo_UserTagList proto.InternalMessageInfo

func (m *UserTagList) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserTagList) GetTagList() []*UserTagBase {
	if m != nil {
		return m.TagList
	}
	return nil
}

type UserTagBase struct {
	TagType              uint32   `protobuf:"varint,1,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	TagName              string   `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	TagId                uint32   `protobuf:"varint,3,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagInfo              []byte   `protobuf:"bytes,4,opt,name=tag_info,json=tagInfo,proto3" json:"tag_info,omitempty"`
	IsDel                bool     `protobuf:"varint,5,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserTagBase) Reset()         { *m = UserTagBase{} }
func (m *UserTagBase) String() string { return proto.CompactTextString(m) }
func (*UserTagBase) ProtoMessage()    {}
func (*UserTagBase) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{1}
}
func (m *UserTagBase) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserTagBase.Unmarshal(m, b)
}
func (m *UserTagBase) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserTagBase.Marshal(b, m, deterministic)
}
func (dst *UserTagBase) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserTagBase.Merge(dst, src)
}
func (m *UserTagBase) XXX_Size() int {
	return xxx_messageInfo_UserTagBase.Size(m)
}
func (m *UserTagBase) XXX_DiscardUnknown() {
	xxx_messageInfo_UserTagBase.DiscardUnknown(m)
}

var xxx_messageInfo_UserTagBase proto.InternalMessageInfo

func (m *UserTagBase) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

func (m *UserTagBase) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *UserTagBase) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *UserTagBase) GetTagInfo() []byte {
	if m != nil {
		return m.TagInfo
	}
	return nil
}

func (m *UserTagBase) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

// 选项二级属性
type GameOptSecondProp struct {
	OptProp              string   `protobuf:"bytes,1,opt,name=opt_prop,json=optProp,proto3" json:"opt_prop,omitempty"`
	ValueList            []string `protobuf:"bytes,2,rep,name=value_list,json=valueList,proto3" json:"value_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameOptSecondProp) Reset()         { *m = GameOptSecondProp{} }
func (m *GameOptSecondProp) String() string { return proto.CompactTextString(m) }
func (*GameOptSecondProp) ProtoMessage()    {}
func (*GameOptSecondProp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{2}
}
func (m *GameOptSecondProp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameOptSecondProp.Unmarshal(m, b)
}
func (m *GameOptSecondProp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameOptSecondProp.Marshal(b, m, deterministic)
}
func (dst *GameOptSecondProp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameOptSecondProp.Merge(dst, src)
}
func (m *GameOptSecondProp) XXX_Size() int {
	return xxx_messageInfo_GameOptSecondProp.Size(m)
}
func (m *GameOptSecondProp) XXX_DiscardUnknown() {
	xxx_messageInfo_GameOptSecondProp.DiscardUnknown(m)
}

var xxx_messageInfo_GameOptSecondProp proto.InternalMessageInfo

func (m *GameOptSecondProp) GetOptProp() string {
	if m != nil {
		return m.OptProp
	}
	return ""
}

func (m *GameOptSecondProp) GetValueList() []string {
	if m != nil {
		return m.ValueList
	}
	return nil
}

type GameScreenShot struct {
	AuditStatus             uint32   `protobuf:"varint,1,opt,name=audit_status,json=auditStatus,proto3" json:"audit_status,omitempty"`
	ImgUrl                  string   `protobuf:"bytes,2,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	ImgUrlInvisibleGamenick string   `protobuf:"bytes,3,opt,name=img_url_invisible_gamenick,json=imgUrlInvisibleGamenick,proto3" json:"img_url_invisible_gamenick,omitempty"`
	BeginAuditTime          uint32   `protobuf:"varint,4,opt,name=begin_audit_time,json=beginAuditTime,proto3" json:"begin_audit_time,omitempty"`
	Index                   uint32   `protobuf:"varint,5,opt,name=index,proto3" json:"index,omitempty"`
	UploadTime              uint32   `protobuf:"varint,6,opt,name=upload_time,json=uploadTime,proto3" json:"upload_time,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *GameScreenShot) Reset()         { *m = GameScreenShot{} }
func (m *GameScreenShot) String() string { return proto.CompactTextString(m) }
func (*GameScreenShot) ProtoMessage()    {}
func (*GameScreenShot) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{3}
}
func (m *GameScreenShot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameScreenShot.Unmarshal(m, b)
}
func (m *GameScreenShot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameScreenShot.Marshal(b, m, deterministic)
}
func (dst *GameScreenShot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameScreenShot.Merge(dst, src)
}
func (m *GameScreenShot) XXX_Size() int {
	return xxx_messageInfo_GameScreenShot.Size(m)
}
func (m *GameScreenShot) XXX_DiscardUnknown() {
	xxx_messageInfo_GameScreenShot.DiscardUnknown(m)
}

var xxx_messageInfo_GameScreenShot proto.InternalMessageInfo

func (m *GameScreenShot) GetAuditStatus() uint32 {
	if m != nil {
		return m.AuditStatus
	}
	return 0
}

func (m *GameScreenShot) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *GameScreenShot) GetImgUrlInvisibleGamenick() string {
	if m != nil {
		return m.ImgUrlInvisibleGamenick
	}
	return ""
}

func (m *GameScreenShot) GetBeginAuditTime() uint32 {
	if m != nil {
		return m.BeginAuditTime
	}
	return 0
}

func (m *GameScreenShot) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GameScreenShot) GetUploadTime() uint32 {
	if m != nil {
		return m.UploadTime
	}
	return 0
}

type GetUserTagReq struct {
	TargetUid            uint32   `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	IsNeedTagExt         bool     `protobuf:"varint,2,opt,name=is_need_tag_ext,json=isNeedTagExt,proto3" json:"is_need_tag_ext,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTagReq) Reset()         { *m = GetUserTagReq{} }
func (m *GetUserTagReq) String() string { return proto.CompactTextString(m) }
func (*GetUserTagReq) ProtoMessage()    {}
func (*GetUserTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{4}
}
func (m *GetUserTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTagReq.Unmarshal(m, b)
}
func (m *GetUserTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTagReq.Marshal(b, m, deterministic)
}
func (dst *GetUserTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTagReq.Merge(dst, src)
}
func (m *GetUserTagReq) XXX_Size() int {
	return xxx_messageInfo_GetUserTagReq.Size(m)
}
func (m *GetUserTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTagReq proto.InternalMessageInfo

func (m *GetUserTagReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetUserTagReq) GetIsNeedTagExt() bool {
	if m != nil {
		return m.IsNeedTagExt
	}
	return false
}

type GetUserTagResp struct {
	UserTaglist          *UserTagList `protobuf:"bytes,1,opt,name=user_taglist,json=userTaglist,proto3" json:"user_taglist,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserTagResp) Reset()         { *m = GetUserTagResp{} }
func (m *GetUserTagResp) String() string { return proto.CompactTextString(m) }
func (*GetUserTagResp) ProtoMessage()    {}
func (*GetUserTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{5}
}
func (m *GetUserTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTagResp.Unmarshal(m, b)
}
func (m *GetUserTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTagResp.Marshal(b, m, deterministic)
}
func (dst *GetUserTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTagResp.Merge(dst, src)
}
func (m *GetUserTagResp) XXX_Size() int {
	return xxx_messageInfo_GetUserTagResp.Size(m)
}
func (m *GetUserTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTagResp proto.InternalMessageInfo

func (m *GetUserTagResp) GetUserTaglist() *UserTagList {
	if m != nil {
		return m.UserTaglist
	}
	return nil
}

type BatGetUserTagReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	IsNeedTagExt         bool     `protobuf:"varint,2,opt,name=is_need_tag_ext,json=isNeedTagExt,proto3" json:"is_need_tag_ext,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetUserTagReq) Reset()         { *m = BatGetUserTagReq{} }
func (m *BatGetUserTagReq) String() string { return proto.CompactTextString(m) }
func (*BatGetUserTagReq) ProtoMessage()    {}
func (*BatGetUserTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{6}
}
func (m *BatGetUserTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetUserTagReq.Unmarshal(m, b)
}
func (m *BatGetUserTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetUserTagReq.Marshal(b, m, deterministic)
}
func (dst *BatGetUserTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetUserTagReq.Merge(dst, src)
}
func (m *BatGetUserTagReq) XXX_Size() int {
	return xxx_messageInfo_BatGetUserTagReq.Size(m)
}
func (m *BatGetUserTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetUserTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetUserTagReq proto.InternalMessageInfo

func (m *BatGetUserTagReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatGetUserTagReq) GetIsNeedTagExt() bool {
	if m != nil {
		return m.IsNeedTagExt
	}
	return false
}

type BatGetUserTagResp struct {
	UsertaglistList      []*UserTagList `protobuf:"bytes,1,rep,name=usertaglist_list,json=usertaglistList,proto3" json:"usertaglist_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatGetUserTagResp) Reset()         { *m = BatGetUserTagResp{} }
func (m *BatGetUserTagResp) String() string { return proto.CompactTextString(m) }
func (*BatGetUserTagResp) ProtoMessage()    {}
func (*BatGetUserTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{7}
}
func (m *BatGetUserTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetUserTagResp.Unmarshal(m, b)
}
func (m *BatGetUserTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetUserTagResp.Marshal(b, m, deterministic)
}
func (dst *BatGetUserTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetUserTagResp.Merge(dst, src)
}
func (m *BatGetUserTagResp) XXX_Size() int {
	return xxx_messageInfo_BatGetUserTagResp.Size(m)
}
func (m *BatGetUserTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetUserTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetUserTagResp proto.InternalMessageInfo

func (m *BatGetUserTagResp) GetUsertaglistList() []*UserTagList {
	if m != nil {
		return m.UsertaglistList
	}
	return nil
}

type GetSimpleGameTagReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSimpleGameTagReq) Reset()         { *m = GetSimpleGameTagReq{} }
func (m *GetSimpleGameTagReq) String() string { return proto.CompactTextString(m) }
func (*GetSimpleGameTagReq) ProtoMessage()    {}
func (*GetSimpleGameTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{8}
}
func (m *GetSimpleGameTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSimpleGameTagReq.Unmarshal(m, b)
}
func (m *GetSimpleGameTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSimpleGameTagReq.Marshal(b, m, deterministic)
}
func (dst *GetSimpleGameTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSimpleGameTagReq.Merge(dst, src)
}
func (m *GetSimpleGameTagReq) XXX_Size() int {
	return xxx_messageInfo_GetSimpleGameTagReq.Size(m)
}
func (m *GetSimpleGameTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSimpleGameTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSimpleGameTagReq proto.InternalMessageInfo

func (m *GetSimpleGameTagReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetSimpleGameTagReq) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type SimpleGameTagExt struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	GameNickname         string   `protobuf:"bytes,3,opt,name=game_nickname,json=gameNickname,proto3" json:"game_nickname,omitempty"`
	GameArea             string   `protobuf:"bytes,4,opt,name=game_area,json=gameArea,proto3" json:"game_area,omitempty"`
	GameDan              string   `protobuf:"bytes,5,opt,name=game_dan,json=gameDan,proto3" json:"game_dan,omitempty"`
	Uid                  uint32   `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`
	GameDanUrlForMic     string   `protobuf:"bytes,7,opt,name=game_dan_url_for_mic,json=gameDanUrlForMic,proto3" json:"game_dan_url_for_mic,omitempty"`
	GameRole             string   `protobuf:"bytes,8,opt,name=game_role,json=gameRole,proto3" json:"game_role,omitempty"`
	GamePosition         []string `protobuf:"bytes,9,rep,name=game_position,json=gamePosition,proto3" json:"game_position,omitempty"`
	GameHeroList         []string `protobuf:"bytes,10,rep,name=game_hero_list,json=gameHeroList,proto3" json:"game_hero_list,omitempty"`
	GameScreenshot       string   `protobuf:"bytes,11,opt,name=game_screenshot,json=gameScreenshot,proto3" json:"game_screenshot,omitempty"`
	GameStyle            []string `protobuf:"bytes,12,rep,name=game_style,json=gameStyle,proto3" json:"game_style,omitempty"`
	GameLevelUrl         string   `protobuf:"bytes,13,opt,name=game_level_url,json=gameLevelUrl,proto3" json:"game_level_url,omitempty"`
	TagId                uint32   `protobuf:"varint,14,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	IsCompleted          bool     `protobuf:"varint,15,opt,name=is_completed,json=isCompleted,proto3" json:"is_completed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimpleGameTagExt) Reset()         { *m = SimpleGameTagExt{} }
func (m *SimpleGameTagExt) String() string { return proto.CompactTextString(m) }
func (*SimpleGameTagExt) ProtoMessage()    {}
func (*SimpleGameTagExt) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{9}
}
func (m *SimpleGameTagExt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleGameTagExt.Unmarshal(m, b)
}
func (m *SimpleGameTagExt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleGameTagExt.Marshal(b, m, deterministic)
}
func (dst *SimpleGameTagExt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleGameTagExt.Merge(dst, src)
}
func (m *SimpleGameTagExt) XXX_Size() int {
	return xxx_messageInfo_SimpleGameTagExt.Size(m)
}
func (m *SimpleGameTagExt) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleGameTagExt.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleGameTagExt proto.InternalMessageInfo

func (m *SimpleGameTagExt) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SimpleGameTagExt) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *SimpleGameTagExt) GetGameNickname() string {
	if m != nil {
		return m.GameNickname
	}
	return ""
}

func (m *SimpleGameTagExt) GetGameArea() string {
	if m != nil {
		return m.GameArea
	}
	return ""
}

func (m *SimpleGameTagExt) GetGameDan() string {
	if m != nil {
		return m.GameDan
	}
	return ""
}

func (m *SimpleGameTagExt) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SimpleGameTagExt) GetGameDanUrlForMic() string {
	if m != nil {
		return m.GameDanUrlForMic
	}
	return ""
}

func (m *SimpleGameTagExt) GetGameRole() string {
	if m != nil {
		return m.GameRole
	}
	return ""
}

func (m *SimpleGameTagExt) GetGamePosition() []string {
	if m != nil {
		return m.GamePosition
	}
	return nil
}

func (m *SimpleGameTagExt) GetGameHeroList() []string {
	if m != nil {
		return m.GameHeroList
	}
	return nil
}

func (m *SimpleGameTagExt) GetGameScreenshot() string {
	if m != nil {
		return m.GameScreenshot
	}
	return ""
}

func (m *SimpleGameTagExt) GetGameStyle() []string {
	if m != nil {
		return m.GameStyle
	}
	return nil
}

func (m *SimpleGameTagExt) GetGameLevelUrl() string {
	if m != nil {
		return m.GameLevelUrl
	}
	return ""
}

func (m *SimpleGameTagExt) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *SimpleGameTagExt) GetIsCompleted() bool {
	if m != nil {
		return m.IsCompleted
	}
	return false
}

type GetSimpleGameTagResp struct {
	GameExtList          []*SimpleGameTagExt `protobuf:"bytes,1,rep,name=game_ext_list,json=gameExtList,proto3" json:"game_ext_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetSimpleGameTagResp) Reset()         { *m = GetSimpleGameTagResp{} }
func (m *GetSimpleGameTagResp) String() string { return proto.CompactTextString(m) }
func (*GetSimpleGameTagResp) ProtoMessage()    {}
func (*GetSimpleGameTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{10}
}
func (m *GetSimpleGameTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSimpleGameTagResp.Unmarshal(m, b)
}
func (m *GetSimpleGameTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSimpleGameTagResp.Marshal(b, m, deterministic)
}
func (dst *GetSimpleGameTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSimpleGameTagResp.Merge(dst, src)
}
func (m *GetSimpleGameTagResp) XXX_Size() int {
	return xxx_messageInfo_GetSimpleGameTagResp.Size(m)
}
func (m *GetSimpleGameTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSimpleGameTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSimpleGameTagResp proto.InternalMessageInfo

func (m *GetSimpleGameTagResp) GetGameExtList() []*SimpleGameTagExt {
	if m != nil {
		return m.GameExtList
	}
	return nil
}

// GetTagConfigList这个接口的数据是用ConfGameTagExt这个解析UserTagBase.TagInfo
type ConfGameTagExt struct {
	GameId               uint32            `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	BackImgUrl           string            `protobuf:"bytes,2,opt,name=back_img_url,json=backImgUrl,proto3" json:"back_img_url,omitempty"`
	OptList              []*ConfGameTagOpt `protobuf:"bytes,3,rep,name=opt_list,json=optList,proto3" json:"opt_list,omitempty"`
	ThumbImgUrl          string            `protobuf:"bytes,4,opt,name=thumb_img_url,json=thumbImgUrl,proto3" json:"thumb_img_url,omitempty"`
	GameNickname         string            `protobuf:"bytes,5,opt,name=game_nickname,json=gameNickname,proto3" json:"game_nickname,omitempty"`
	GameScreenshot       *GameScreenShot   `protobuf:"bytes,6,opt,name=game_screenshot,json=gameScreenshot,proto3" json:"game_screenshot,omitempty"`
	GameCardImgUrl       string            `protobuf:"bytes,7,opt,name=game_card_img_url,json=gameCardImgUrl,proto3" json:"game_card_img_url,omitempty"`
	GameBackImg          string            `protobuf:"bytes,8,opt,name=game_back_img,json=gameBackImg,proto3" json:"game_back_img,omitempty"`
	GameIconImg          string            `protobuf:"bytes,9,opt,name=game_icon_img,json=gameIconImg,proto3" json:"game_icon_img,omitempty"`
	GameCornerMarkImg    string            `protobuf:"bytes,10,opt,name=game_corner_mark_img,json=gameCornerMarkImg,proto3" json:"game_corner_mark_img,omitempty"`
	GameNoLevelImgUrl    string            `protobuf:"bytes,11,opt,name=game_no_level_img_url,json=gameNoLevelImgUrl,proto3" json:"game_no_level_img_url,omitempty"`
	LevelImgUrl          []*LevelImg       `protobuf:"bytes,12,rep,name=level_img_url,json=levelImgUrl,proto3" json:"level_img_url,omitempty"`
	MicLevelImgUrl       []*LevelImg       `protobuf:"bytes,13,rep,name=mic_level_img_url,json=micLevelImgUrl,proto3" json:"mic_level_img_url,omitempty"`
	GameBackImgMini      string            `protobuf:"bytes,14,opt,name=game_back_img_mini,json=gameBackImgMini,proto3" json:"game_back_img_mini,omitempty"`
	GameBackColorNum     uint32            `protobuf:"varint,15,opt,name=game_back_color_num,json=gameBackColorNum,proto3" json:"game_back_color_num,omitempty"`
	GameExtraOptSwitch   uint32            `protobuf:"varint,16,opt,name=game_extra_opt_switch,json=gameExtraOptSwitch,proto3" json:"game_extra_opt_switch,omitempty"`
	LevelImgUrlNew       []*LevelImg       `protobuf:"bytes,17,rep,name=level_img_url_new,json=levelImgUrlNew,proto3" json:"level_img_url_new,omitempty"`
	GameNoLevelImgUrlNew string            `protobuf:"bytes,18,opt,name=game_no_level_img_url_new,json=gameNoLevelImgUrlNew,proto3" json:"game_no_level_img_url_new,omitempty"`
	UGameId              uint32            `protobuf:"varint,19,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ConfGameTagExt) Reset()         { *m = ConfGameTagExt{} }
func (m *ConfGameTagExt) String() string { return proto.CompactTextString(m) }
func (*ConfGameTagExt) ProtoMessage()    {}
func (*ConfGameTagExt) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{11}
}
func (m *ConfGameTagExt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfGameTagExt.Unmarshal(m, b)
}
func (m *ConfGameTagExt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfGameTagExt.Marshal(b, m, deterministic)
}
func (dst *ConfGameTagExt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfGameTagExt.Merge(dst, src)
}
func (m *ConfGameTagExt) XXX_Size() int {
	return xxx_messageInfo_ConfGameTagExt.Size(m)
}
func (m *ConfGameTagExt) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfGameTagExt.DiscardUnknown(m)
}

var xxx_messageInfo_ConfGameTagExt proto.InternalMessageInfo

func (m *ConfGameTagExt) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ConfGameTagExt) GetBackImgUrl() string {
	if m != nil {
		return m.BackImgUrl
	}
	return ""
}

func (m *ConfGameTagExt) GetOptList() []*ConfGameTagOpt {
	if m != nil {
		return m.OptList
	}
	return nil
}

func (m *ConfGameTagExt) GetThumbImgUrl() string {
	if m != nil {
		return m.ThumbImgUrl
	}
	return ""
}

func (m *ConfGameTagExt) GetGameNickname() string {
	if m != nil {
		return m.GameNickname
	}
	return ""
}

func (m *ConfGameTagExt) GetGameScreenshot() *GameScreenShot {
	if m != nil {
		return m.GameScreenshot
	}
	return nil
}

func (m *ConfGameTagExt) GetGameCardImgUrl() string {
	if m != nil {
		return m.GameCardImgUrl
	}
	return ""
}

func (m *ConfGameTagExt) GetGameBackImg() string {
	if m != nil {
		return m.GameBackImg
	}
	return ""
}

func (m *ConfGameTagExt) GetGameIconImg() string {
	if m != nil {
		return m.GameIconImg
	}
	return ""
}

func (m *ConfGameTagExt) GetGameCornerMarkImg() string {
	if m != nil {
		return m.GameCornerMarkImg
	}
	return ""
}

func (m *ConfGameTagExt) GetGameNoLevelImgUrl() string {
	if m != nil {
		return m.GameNoLevelImgUrl
	}
	return ""
}

func (m *ConfGameTagExt) GetLevelImgUrl() []*LevelImg {
	if m != nil {
		return m.LevelImgUrl
	}
	return nil
}

func (m *ConfGameTagExt) GetMicLevelImgUrl() []*LevelImg {
	if m != nil {
		return m.MicLevelImgUrl
	}
	return nil
}

func (m *ConfGameTagExt) GetGameBackImgMini() string {
	if m != nil {
		return m.GameBackImgMini
	}
	return ""
}

func (m *ConfGameTagExt) GetGameBackColorNum() uint32 {
	if m != nil {
		return m.GameBackColorNum
	}
	return 0
}

func (m *ConfGameTagExt) GetGameExtraOptSwitch() uint32 {
	if m != nil {
		return m.GameExtraOptSwitch
	}
	return 0
}

func (m *ConfGameTagExt) GetLevelImgUrlNew() []*LevelImg {
	if m != nil {
		return m.LevelImgUrlNew
	}
	return nil
}

func (m *ConfGameTagExt) GetGameNoLevelImgUrlNew() string {
	if m != nil {
		return m.GameNoLevelImgUrlNew
	}
	return ""
}

func (m *ConfGameTagExt) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

type LevelImg struct {
	LevelName            string   `protobuf:"bytes,1,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	ImgUrl               string   `protobuf:"bytes,2,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelImg) Reset()         { *m = LevelImg{} }
func (m *LevelImg) String() string { return proto.CompactTextString(m) }
func (*LevelImg) ProtoMessage()    {}
func (*LevelImg) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{12}
}
func (m *LevelImg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelImg.Unmarshal(m, b)
}
func (m *LevelImg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelImg.Marshal(b, m, deterministic)
}
func (dst *LevelImg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelImg.Merge(dst, src)
}
func (m *LevelImg) XXX_Size() int {
	return xxx_messageInfo_LevelImg.Size(m)
}
func (m *LevelImg) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelImg.DiscardUnknown(m)
}

var xxx_messageInfo_LevelImg proto.InternalMessageInfo

func (m *LevelImg) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *LevelImg) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

type ConfGameTagOpt struct {
	OptName              string               `protobuf:"bytes,1,opt,name=opt_name,json=optName,proto3" json:"opt_name,omitempty"`
	OptId                uint32               `protobuf:"varint,2,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	IsSupportMutiSet     bool                 `protobuf:"varint,3,opt,name=is_support_muti_set,json=isSupportMutiSet,proto3" json:"is_support_muti_set,omitempty"`
	ValueConfList        []string             `protobuf:"bytes,4,rep,name=value_conf_list,json=valueConfList,proto3" json:"value_conf_list,omitempty"`
	ValueUsersetList     []string             `protobuf:"bytes,5,rep,name=value_userset_list,json=valueUsersetList,proto3" json:"value_userset_list,omitempty"`
	SupportMutiSetCnt    uint32               `protobuf:"varint,6,opt,name=support_muti_set_cnt,json=supportMutiSetCnt,proto3" json:"support_muti_set_cnt,omitempty"`
	PartitionId          uint32               `protobuf:"varint,7,opt,name=partition_id,json=partitionId,proto3" json:"partition_id,omitempty"`
	PropValueList        []*GameOptSecondProp `protobuf:"bytes,8,rep,name=prop_value_list,json=propValueList,proto3" json:"prop_value_list,omitempty"`
	IsMust               bool                 `protobuf:"varint,9,opt,name=is_must,json=isMust,proto3" json:"is_must,omitempty"`
	TextBoxStyle         uint32               `protobuf:"varint,10,opt,name=text_box_style,json=textBoxStyle,proto3" json:"text_box_style,omitempty"`
	DisplayInChannel     bool                 `protobuf:"varint,11,opt,name=display_in_channel,json=displayInChannel,proto3" json:"display_in_channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ConfGameTagOpt) Reset()         { *m = ConfGameTagOpt{} }
func (m *ConfGameTagOpt) String() string { return proto.CompactTextString(m) }
func (*ConfGameTagOpt) ProtoMessage()    {}
func (*ConfGameTagOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{13}
}
func (m *ConfGameTagOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfGameTagOpt.Unmarshal(m, b)
}
func (m *ConfGameTagOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfGameTagOpt.Marshal(b, m, deterministic)
}
func (dst *ConfGameTagOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfGameTagOpt.Merge(dst, src)
}
func (m *ConfGameTagOpt) XXX_Size() int {
	return xxx_messageInfo_ConfGameTagOpt.Size(m)
}
func (m *ConfGameTagOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfGameTagOpt.DiscardUnknown(m)
}

var xxx_messageInfo_ConfGameTagOpt proto.InternalMessageInfo

func (m *ConfGameTagOpt) GetOptName() string {
	if m != nil {
		return m.OptName
	}
	return ""
}

func (m *ConfGameTagOpt) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *ConfGameTagOpt) GetIsSupportMutiSet() bool {
	if m != nil {
		return m.IsSupportMutiSet
	}
	return false
}

func (m *ConfGameTagOpt) GetValueConfList() []string {
	if m != nil {
		return m.ValueConfList
	}
	return nil
}

func (m *ConfGameTagOpt) GetValueUsersetList() []string {
	if m != nil {
		return m.ValueUsersetList
	}
	return nil
}

func (m *ConfGameTagOpt) GetSupportMutiSetCnt() uint32 {
	if m != nil {
		return m.SupportMutiSetCnt
	}
	return 0
}

func (m *ConfGameTagOpt) GetPartitionId() uint32 {
	if m != nil {
		return m.PartitionId
	}
	return 0
}

func (m *ConfGameTagOpt) GetPropValueList() []*GameOptSecondProp {
	if m != nil {
		return m.PropValueList
	}
	return nil
}

func (m *ConfGameTagOpt) GetIsMust() bool {
	if m != nil {
		return m.IsMust
	}
	return false
}

func (m *ConfGameTagOpt) GetTextBoxStyle() uint32 {
	if m != nil {
		return m.TextBoxStyle
	}
	return 0
}

func (m *ConfGameTagOpt) GetDisplayInChannel() bool {
	if m != nil {
		return m.DisplayInChannel
	}
	return false
}

// 获取配置标签列表
type GetTagConfigListReq struct {
	TagType              uint32   `protobuf:"varint,1,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTagConfigListReq) Reset()         { *m = GetTagConfigListReq{} }
func (m *GetTagConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetTagConfigListReq) ProtoMessage()    {}
func (*GetTagConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{14}
}
func (m *GetTagConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTagConfigListReq.Unmarshal(m, b)
}
func (m *GetTagConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTagConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetTagConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagConfigListReq.Merge(dst, src)
}
func (m *GetTagConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetTagConfigListReq.Size(m)
}
func (m *GetTagConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagConfigListReq proto.InternalMessageInfo

func (m *GetTagConfigListReq) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

type GetTagConfigListResp struct {
	TagList              []*UserTagBase `protobuf:"bytes,1,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	GameIdList           []uint32       `protobuf:"varint,2,rep,packed,name=game_id_list,json=gameIdList,proto3" json:"game_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetTagConfigListResp) Reset()         { *m = GetTagConfigListResp{} }
func (m *GetTagConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetTagConfigListResp) ProtoMessage()    {}
func (*GetTagConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{15}
}
func (m *GetTagConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTagConfigListResp.Unmarshal(m, b)
}
func (m *GetTagConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTagConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetTagConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagConfigListResp.Merge(dst, src)
}
func (m *GetTagConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetTagConfigListResp.Size(m)
}
func (m *GetTagConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagConfigListResp proto.InternalMessageInfo

func (m *GetTagConfigListResp) GetTagList() []*UserTagBase {
	if m != nil {
		return m.TagList
	}
	return nil
}

func (m *GetTagConfigListResp) GetGameIdList() []uint32 {
	if m != nil {
		return m.GameIdList
	}
	return nil
}

// GetUserTag接口的数据是用UserGameTagExt这个解析UserTagBase.TagInfo
type UserGameTagExt struct {
	GameId               uint32            `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	BackImgUrl           string            `protobuf:"bytes,2,opt,name=back_img_url,json=backImgUrl,proto3" json:"back_img_url,omitempty"`
	OptList              []*UserGameTagOpt `protobuf:"bytes,3,rep,name=opt_list,json=optList,proto3" json:"opt_list,omitempty"`
	ThumbImgUrl          string            `protobuf:"bytes,4,opt,name=thumb_img_url,json=thumbImgUrl,proto3" json:"thumb_img_url,omitempty"`
	GameNickname         string            `protobuf:"bytes,5,opt,name=game_nickname,json=gameNickname,proto3" json:"game_nickname,omitempty"`
	GameScreenshot       *GameScreenShot   `protobuf:"bytes,6,opt,name=game_screenshot,json=gameScreenshot,proto3" json:"game_screenshot,omitempty"`
	GameScreenshotList   []*GameScreenShot `protobuf:"bytes,7,rep,name=game_screenshot_list,json=gameScreenshotList,proto3" json:"game_screenshot_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserGameTagExt) Reset()         { *m = UserGameTagExt{} }
func (m *UserGameTagExt) String() string { return proto.CompactTextString(m) }
func (*UserGameTagExt) ProtoMessage()    {}
func (*UserGameTagExt) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{16}
}
func (m *UserGameTagExt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGameTagExt.Unmarshal(m, b)
}
func (m *UserGameTagExt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGameTagExt.Marshal(b, m, deterministic)
}
func (dst *UserGameTagExt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGameTagExt.Merge(dst, src)
}
func (m *UserGameTagExt) XXX_Size() int {
	return xxx_messageInfo_UserGameTagExt.Size(m)
}
func (m *UserGameTagExt) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGameTagExt.DiscardUnknown(m)
}

var xxx_messageInfo_UserGameTagExt proto.InternalMessageInfo

func (m *UserGameTagExt) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UserGameTagExt) GetBackImgUrl() string {
	if m != nil {
		return m.BackImgUrl
	}
	return ""
}

func (m *UserGameTagExt) GetOptList() []*UserGameTagOpt {
	if m != nil {
		return m.OptList
	}
	return nil
}

func (m *UserGameTagExt) GetThumbImgUrl() string {
	if m != nil {
		return m.ThumbImgUrl
	}
	return ""
}

func (m *UserGameTagExt) GetGameNickname() string {
	if m != nil {
		return m.GameNickname
	}
	return ""
}

func (m *UserGameTagExt) GetGameScreenshot() *GameScreenShot {
	if m != nil {
		return m.GameScreenshot
	}
	return nil
}

func (m *UserGameTagExt) GetGameScreenshotList() []*GameScreenShot {
	if m != nil {
		return m.GameScreenshotList
	}
	return nil
}

type UserGameTagOpt struct {
	OptName              string               `protobuf:"bytes,1,opt,name=opt_name,json=optName,proto3" json:"opt_name,omitempty"`
	OptId                uint32               `protobuf:"varint,2,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	IsSupportMutiSet     bool                 `protobuf:"varint,3,opt,name=is_support_muti_set,json=isSupportMutiSet,proto3" json:"is_support_muti_set,omitempty"`
	ValueConfList        []string             `protobuf:"bytes,4,rep,name=value_conf_list,json=valueConfList,proto3" json:"value_conf_list,omitempty"`
	ValueUsersetList     []string             `protobuf:"bytes,5,rep,name=value_userset_list,json=valueUsersetList,proto3" json:"value_userset_list,omitempty"`
	SupportMutiSetCnt    uint32               `protobuf:"varint,6,opt,name=support_muti_set_cnt,json=supportMutiSetCnt,proto3" json:"support_muti_set_cnt,omitempty"`
	PartitionId          uint32               `protobuf:"varint,7,opt,name=partition_id,json=partitionId,proto3" json:"partition_id,omitempty"`
	PropValueList        []*GameOptSecondProp `protobuf:"bytes,8,rep,name=prop_value_list,json=propValueList,proto3" json:"prop_value_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UserGameTagOpt) Reset()         { *m = UserGameTagOpt{} }
func (m *UserGameTagOpt) String() string { return proto.CompactTextString(m) }
func (*UserGameTagOpt) ProtoMessage()    {}
func (*UserGameTagOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{17}
}
func (m *UserGameTagOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGameTagOpt.Unmarshal(m, b)
}
func (m *UserGameTagOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGameTagOpt.Marshal(b, m, deterministic)
}
func (dst *UserGameTagOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGameTagOpt.Merge(dst, src)
}
func (m *UserGameTagOpt) XXX_Size() int {
	return xxx_messageInfo_UserGameTagOpt.Size(m)
}
func (m *UserGameTagOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGameTagOpt.DiscardUnknown(m)
}

var xxx_messageInfo_UserGameTagOpt proto.InternalMessageInfo

func (m *UserGameTagOpt) GetOptName() string {
	if m != nil {
		return m.OptName
	}
	return ""
}

func (m *UserGameTagOpt) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *UserGameTagOpt) GetIsSupportMutiSet() bool {
	if m != nil {
		return m.IsSupportMutiSet
	}
	return false
}

func (m *UserGameTagOpt) GetValueConfList() []string {
	if m != nil {
		return m.ValueConfList
	}
	return nil
}

func (m *UserGameTagOpt) GetValueUsersetList() []string {
	if m != nil {
		return m.ValueUsersetList
	}
	return nil
}

func (m *UserGameTagOpt) GetSupportMutiSetCnt() uint32 {
	if m != nil {
		return m.SupportMutiSetCnt
	}
	return 0
}

func (m *UserGameTagOpt) GetPartitionId() uint32 {
	if m != nil {
		return m.PartitionId
	}
	return 0
}

func (m *UserGameTagOpt) GetPropValueList() []*GameOptSecondProp {
	if m != nil {
		return m.PropValueList
	}
	return nil
}

type MicUser struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 用户类型 see account.proto enum USER_TYPE
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MicUser) Reset()         { *m = MicUser{} }
func (m *MicUser) String() string { return proto.CompactTextString(m) }
func (*MicUser) ProtoMessage()    {}
func (*MicUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{18}
}
func (m *MicUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicUser.Unmarshal(m, b)
}
func (m *MicUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicUser.Marshal(b, m, deterministic)
}
func (dst *MicUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicUser.Merge(dst, src)
}
func (m *MicUser) XXX_Size() int {
	return xxx_messageInfo_MicUser.Size(m)
}
func (m *MicUser) XXX_DiscardUnknown() {
	xxx_messageInfo_MicUser.DiscardUnknown(m)
}

var xxx_messageInfo_MicUser proto.InternalMessageInfo

func (m *MicUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MicUser) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetMicUserExtInfoRequest struct {
	User                 *MicUser `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMicUserExtInfoRequest) Reset()         { *m = GetMicUserExtInfoRequest{} }
func (m *GetMicUserExtInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetMicUserExtInfoRequest) ProtoMessage()    {}
func (*GetMicUserExtInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{19}
}
func (m *GetMicUserExtInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMicUserExtInfoRequest.Unmarshal(m, b)
}
func (m *GetMicUserExtInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMicUserExtInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetMicUserExtInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMicUserExtInfoRequest.Merge(dst, src)
}
func (m *GetMicUserExtInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetMicUserExtInfoRequest.Size(m)
}
func (m *GetMicUserExtInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMicUserExtInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMicUserExtInfoRequest proto.InternalMessageInfo

func (m *GetMicUserExtInfoRequest) GetUser() *MicUser {
	if m != nil {
		return m.User
	}
	return nil
}

type GetMicUserExtInfoResponse struct {
	MicUserInfo          []byte   `protobuf:"bytes,1,opt,name=mic_user_info,json=micUserInfo,proto3" json:"mic_user_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMicUserExtInfoResponse) Reset()         { *m = GetMicUserExtInfoResponse{} }
func (m *GetMicUserExtInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetMicUserExtInfoResponse) ProtoMessage()    {}
func (*GetMicUserExtInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{20}
}
func (m *GetMicUserExtInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMicUserExtInfoResponse.Unmarshal(m, b)
}
func (m *GetMicUserExtInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMicUserExtInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetMicUserExtInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMicUserExtInfoResponse.Merge(dst, src)
}
func (m *GetMicUserExtInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetMicUserExtInfoResponse.Size(m)
}
func (m *GetMicUserExtInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMicUserExtInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMicUserExtInfoResponse proto.InternalMessageInfo

func (m *GetMicUserExtInfoResponse) GetMicUserInfo() []byte {
	if m != nil {
		return m.MicUserInfo
	}
	return nil
}

type BatchGetMicUserExtInfoRequest struct {
	Users                []*MicUser `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BatchGetMicUserExtInfoRequest) Reset()         { *m = BatchGetMicUserExtInfoRequest{} }
func (m *BatchGetMicUserExtInfoRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetMicUserExtInfoRequest) ProtoMessage()    {}
func (*BatchGetMicUserExtInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{21}
}
func (m *BatchGetMicUserExtInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetMicUserExtInfoRequest.Unmarshal(m, b)
}
func (m *BatchGetMicUserExtInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetMicUserExtInfoRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetMicUserExtInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetMicUserExtInfoRequest.Merge(dst, src)
}
func (m *BatchGetMicUserExtInfoRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetMicUserExtInfoRequest.Size(m)
}
func (m *BatchGetMicUserExtInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetMicUserExtInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetMicUserExtInfoRequest proto.InternalMessageInfo

func (m *BatchGetMicUserExtInfoRequest) GetUsers() []*MicUser {
	if m != nil {
		return m.Users
	}
	return nil
}

type BatchGetMicUserExtInfoResponse struct {
	MicUserInfos         map[uint32][]byte `protobuf:"bytes,1,rep,name=mic_user_infos,json=micUserInfos,proto3" json:"mic_user_infos,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetMicUserExtInfoResponse) Reset()         { *m = BatchGetMicUserExtInfoResponse{} }
func (m *BatchGetMicUserExtInfoResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetMicUserExtInfoResponse) ProtoMessage()    {}
func (*BatchGetMicUserExtInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_userline_common_api_59f45510bc76eafe, []int{22}
}
func (m *BatchGetMicUserExtInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetMicUserExtInfoResponse.Unmarshal(m, b)
}
func (m *BatchGetMicUserExtInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetMicUserExtInfoResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetMicUserExtInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetMicUserExtInfoResponse.Merge(dst, src)
}
func (m *BatchGetMicUserExtInfoResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetMicUserExtInfoResponse.Size(m)
}
func (m *BatchGetMicUserExtInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetMicUserExtInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetMicUserExtInfoResponse proto.InternalMessageInfo

func (m *BatchGetMicUserExtInfoResponse) GetMicUserInfos() map[uint32][]byte {
	if m != nil {
		return m.MicUserInfos
	}
	return nil
}

func init() {
	proto.RegisterType((*UserTagList)(nil), "userline_common_api.UserTagList")
	proto.RegisterType((*UserTagBase)(nil), "userline_common_api.UserTagBase")
	proto.RegisterType((*GameOptSecondProp)(nil), "userline_common_api.GameOptSecondProp")
	proto.RegisterType((*GameScreenShot)(nil), "userline_common_api.GameScreenShot")
	proto.RegisterType((*GetUserTagReq)(nil), "userline_common_api.GetUserTagReq")
	proto.RegisterType((*GetUserTagResp)(nil), "userline_common_api.GetUserTagResp")
	proto.RegisterType((*BatGetUserTagReq)(nil), "userline_common_api.BatGetUserTagReq")
	proto.RegisterType((*BatGetUserTagResp)(nil), "userline_common_api.BatGetUserTagResp")
	proto.RegisterType((*GetSimpleGameTagReq)(nil), "userline_common_api.GetSimpleGameTagReq")
	proto.RegisterType((*SimpleGameTagExt)(nil), "userline_common_api.SimpleGameTagExt")
	proto.RegisterType((*GetSimpleGameTagResp)(nil), "userline_common_api.GetSimpleGameTagResp")
	proto.RegisterType((*ConfGameTagExt)(nil), "userline_common_api.ConfGameTagExt")
	proto.RegisterType((*LevelImg)(nil), "userline_common_api.LevelImg")
	proto.RegisterType((*ConfGameTagOpt)(nil), "userline_common_api.ConfGameTagOpt")
	proto.RegisterType((*GetTagConfigListReq)(nil), "userline_common_api.GetTagConfigListReq")
	proto.RegisterType((*GetTagConfigListResp)(nil), "userline_common_api.GetTagConfigListResp")
	proto.RegisterType((*UserGameTagExt)(nil), "userline_common_api.UserGameTagExt")
	proto.RegisterType((*UserGameTagOpt)(nil), "userline_common_api.UserGameTagOpt")
	proto.RegisterType((*MicUser)(nil), "userline_common_api.MicUser")
	proto.RegisterType((*GetMicUserExtInfoRequest)(nil), "userline_common_api.GetMicUserExtInfoRequest")
	proto.RegisterType((*GetMicUserExtInfoResponse)(nil), "userline_common_api.GetMicUserExtInfoResponse")
	proto.RegisterType((*BatchGetMicUserExtInfoRequest)(nil), "userline_common_api.BatchGetMicUserExtInfoRequest")
	proto.RegisterType((*BatchGetMicUserExtInfoResponse)(nil), "userline_common_api.BatchGetMicUserExtInfoResponse")
	proto.RegisterMapType((map[uint32][]byte)(nil), "userline_common_api.BatchGetMicUserExtInfoResponse.MicUserInfosEntry")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserlineCommonApiClient is the client API for UserlineCommonApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserlineCommonApiClient interface {
	// usertag服务迁移的接口
	GetUserTag(ctx context.Context, in *GetUserTagReq, opts ...grpc.CallOption) (*GetUserTagResp, error)
	BatGetUserTag(ctx context.Context, in *BatGetUserTagReq, opts ...grpc.CallOption) (*BatGetUserTagResp, error)
	GetSimpleGameTag(ctx context.Context, in *GetSimpleGameTagReq, opts ...grpc.CallOption) (*GetSimpleGameTagResp, error)
	GetTagConfigList(ctx context.Context, in *GetTagConfigListReq, opts ...grpc.CallOption) (*GetTagConfigListResp, error)
	// 麦位用户扩展信息
	GetMicUserExtInfo(ctx context.Context, in *GetMicUserExtInfoRequest, opts ...grpc.CallOption) (*GetMicUserExtInfoResponse, error)
	BatchGetMicUserExtInfo(ctx context.Context, in *BatchGetMicUserExtInfoRequest, opts ...grpc.CallOption) (*BatchGetMicUserExtInfoResponse, error)
}

type userlineCommonApiClient struct {
	cc *grpc.ClientConn
}

func NewUserlineCommonApiClient(cc *grpc.ClientConn) UserlineCommonApiClient {
	return &userlineCommonApiClient{cc}
}

func (c *userlineCommonApiClient) GetUserTag(ctx context.Context, in *GetUserTagReq, opts ...grpc.CallOption) (*GetUserTagResp, error) {
	out := new(GetUserTagResp)
	err := c.cc.Invoke(ctx, "/userline_common_api.UserlineCommonApi/GetUserTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userlineCommonApiClient) BatGetUserTag(ctx context.Context, in *BatGetUserTagReq, opts ...grpc.CallOption) (*BatGetUserTagResp, error) {
	out := new(BatGetUserTagResp)
	err := c.cc.Invoke(ctx, "/userline_common_api.UserlineCommonApi/BatGetUserTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userlineCommonApiClient) GetSimpleGameTag(ctx context.Context, in *GetSimpleGameTagReq, opts ...grpc.CallOption) (*GetSimpleGameTagResp, error) {
	out := new(GetSimpleGameTagResp)
	err := c.cc.Invoke(ctx, "/userline_common_api.UserlineCommonApi/GetSimpleGameTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userlineCommonApiClient) GetTagConfigList(ctx context.Context, in *GetTagConfigListReq, opts ...grpc.CallOption) (*GetTagConfigListResp, error) {
	out := new(GetTagConfigListResp)
	err := c.cc.Invoke(ctx, "/userline_common_api.UserlineCommonApi/GetTagConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userlineCommonApiClient) GetMicUserExtInfo(ctx context.Context, in *GetMicUserExtInfoRequest, opts ...grpc.CallOption) (*GetMicUserExtInfoResponse, error) {
	out := new(GetMicUserExtInfoResponse)
	err := c.cc.Invoke(ctx, "/userline_common_api.UserlineCommonApi/GetMicUserExtInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userlineCommonApiClient) BatchGetMicUserExtInfo(ctx context.Context, in *BatchGetMicUserExtInfoRequest, opts ...grpc.CallOption) (*BatchGetMicUserExtInfoResponse, error) {
	out := new(BatchGetMicUserExtInfoResponse)
	err := c.cc.Invoke(ctx, "/userline_common_api.UserlineCommonApi/BatchGetMicUserExtInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserlineCommonApiServer is the server API for UserlineCommonApi service.
type UserlineCommonApiServer interface {
	// usertag服务迁移的接口
	GetUserTag(context.Context, *GetUserTagReq) (*GetUserTagResp, error)
	BatGetUserTag(context.Context, *BatGetUserTagReq) (*BatGetUserTagResp, error)
	GetSimpleGameTag(context.Context, *GetSimpleGameTagReq) (*GetSimpleGameTagResp, error)
	GetTagConfigList(context.Context, *GetTagConfigListReq) (*GetTagConfigListResp, error)
	// 麦位用户扩展信息
	GetMicUserExtInfo(context.Context, *GetMicUserExtInfoRequest) (*GetMicUserExtInfoResponse, error)
	BatchGetMicUserExtInfo(context.Context, *BatchGetMicUserExtInfoRequest) (*BatchGetMicUserExtInfoResponse, error)
}

func RegisterUserlineCommonApiServer(s *grpc.Server, srv UserlineCommonApiServer) {
	s.RegisterService(&_UserlineCommonApi_serviceDesc, srv)
}

func _UserlineCommonApi_GetUserTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserlineCommonApiServer).GetUserTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userline_common_api.UserlineCommonApi/GetUserTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserlineCommonApiServer).GetUserTag(ctx, req.(*GetUserTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserlineCommonApi_BatGetUserTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetUserTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserlineCommonApiServer).BatGetUserTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userline_common_api.UserlineCommonApi/BatGetUserTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserlineCommonApiServer).BatGetUserTag(ctx, req.(*BatGetUserTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserlineCommonApi_GetSimpleGameTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSimpleGameTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserlineCommonApiServer).GetSimpleGameTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userline_common_api.UserlineCommonApi/GetSimpleGameTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserlineCommonApiServer).GetSimpleGameTag(ctx, req.(*GetSimpleGameTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserlineCommonApi_GetTagConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTagConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserlineCommonApiServer).GetTagConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userline_common_api.UserlineCommonApi/GetTagConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserlineCommonApiServer).GetTagConfigList(ctx, req.(*GetTagConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserlineCommonApi_GetMicUserExtInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMicUserExtInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserlineCommonApiServer).GetMicUserExtInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userline_common_api.UserlineCommonApi/GetMicUserExtInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserlineCommonApiServer).GetMicUserExtInfo(ctx, req.(*GetMicUserExtInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserlineCommonApi_BatchGetMicUserExtInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetMicUserExtInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserlineCommonApiServer).BatchGetMicUserExtInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userline_common_api.UserlineCommonApi/BatchGetMicUserExtInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserlineCommonApiServer).BatchGetMicUserExtInfo(ctx, req.(*BatchGetMicUserExtInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserlineCommonApi_serviceDesc = grpc.ServiceDesc{
	ServiceName: "userline_common_api.UserlineCommonApi",
	HandlerType: (*UserlineCommonApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserTag",
			Handler:    _UserlineCommonApi_GetUserTag_Handler,
		},
		{
			MethodName: "BatGetUserTag",
			Handler:    _UserlineCommonApi_BatGetUserTag_Handler,
		},
		{
			MethodName: "GetSimpleGameTag",
			Handler:    _UserlineCommonApi_GetSimpleGameTag_Handler,
		},
		{
			MethodName: "GetTagConfigList",
			Handler:    _UserlineCommonApi_GetTagConfigList_Handler,
		},
		{
			MethodName: "GetMicUserExtInfo",
			Handler:    _UserlineCommonApi_GetMicUserExtInfo_Handler,
		},
		{
			MethodName: "BatchGetMicUserExtInfo",
			Handler:    _UserlineCommonApi_BatchGetMicUserExtInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/userline-common-api/userline-common-api.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/userline-common-api/userline-common-api.proto", fileDescriptor_userline_common_api_59f45510bc76eafe)
}

var fileDescriptor_userline_common_api_59f45510bc76eafe = []byte{
	// 1787 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x58, 0xdd, 0x6e, 0x23, 0x49,
	0x15, 0x5e, 0x4f, 0x26, 0xb1, 0x7d, 0xfc, 0x13, 0xbb, 0x26, 0xcb, 0x7a, 0x02, 0x03, 0xd9, 0x1e,
	0x66, 0xc8, 0x8a, 0x9d, 0x64, 0xc8, 0x6a, 0xb5, 0x88, 0x45, 0xac, 0x26, 0xde, 0x90, 0xb5, 0x98,
	0x64, 0x56, 0xed, 0x18, 0x24, 0x84, 0xd4, 0x5b, 0xe9, 0xae, 0x38, 0xa5, 0x74, 0x57, 0xf5, 0x74,
	0x55, 0x67, 0x9c, 0x4b, 0xee, 0x79, 0x04, 0xde, 0x82, 0xf7, 0xe0, 0x9a, 0x67, 0x80, 0x07, 0x40,
	0xdc, 0xa1, 0x3a, 0x55, 0x6d, 0xbb, 0x1d, 0x7b, 0x26, 0x8b, 0x04, 0x12, 0xd2, 0xde, 0xb9, 0xcf,
	0x7f, 0x9d, 0xfa, 0xce, 0x4f, 0x19, 0x7e, 0xa9, 0xf5, 0xfe, 0xeb, 0x9c, 0x87, 0x57, 0x8a, 0xc7,
	0xd7, 0x2c, 0xdb, 0xcf, 0x15, 0xcb, 0x62, 0x2e, 0xd8, 0xb3, 0x50, 0x26, 0x89, 0x14, 0xcf, 0x68,
	0xca, 0x97, 0xd1, 0xf6, 0xd2, 0x4c, 0x6a, 0x49, 0x1e, 0x14, 0xac, 0xc0, 0xb2, 0x02, 0x9a, 0x72,
	0xef, 0x0f, 0xd0, 0x18, 0x29, 0x96, 0x9d, 0xd1, 0xf1, 0x4b, 0xae, 0x34, 0xe9, 0xc0, 0x5a, 0xce,
	0xa3, 0x5e, 0x65, 0xa7, 0xb2, 0xdb, 0xf2, 0xcd, 0x4f, 0xf2, 0x39, 0xd4, 0x34, 0x1d, 0x07, 0x31,
	0x57, 0xba, 0x77, 0x6f, 0x67, 0x6d, 0xb7, 0x71, 0xb0, 0xb3, 0xb7, 0xc4, 0xd0, 0x9e, 0xb3, 0x72,
	0x48, 0x15, 0xf3, 0xab, 0xda, 0x9a, 0xf3, 0xfe, 0x54, 0x99, 0x9a, 0x37, 0x0c, 0xf2, 0xd0, 0x1a,
	0xd3, 0x37, 0x29, 0x73, 0x3e, 0x8c, 0xe8, 0xd9, 0x4d, 0x3a, 0x65, 0x09, 0x9a, 0xb0, 0xde, 0xbd,
	0x9d, 0xca, 0x6e, 0x1d, 0x59, 0xa7, 0x34, 0x61, 0xe4, 0x7d, 0xd8, 0x30, 0x2c, 0x1e, 0xf5, 0xd6,
	0x50, 0x67, 0x5d, 0xd3, 0xf1, 0x20, 0x2a, 0x34, 0xb8, 0xb8, 0x90, 0xbd, 0xfb, 0x3b, 0x95, 0xdd,
	0x26, 0x6a, 0x0c, 0xc4, 0x85, 0x34, 0x1a, 0x5c, 0x05, 0x11, 0x8b, 0x7b, 0xeb, 0x3b, 0x95, 0xdd,
	0x9a, 0xbf, 0xce, 0xd5, 0x97, 0x2c, 0xf6, 0x4e, 0xa0, 0x7b, 0x4c, 0x13, 0xf6, 0x2a, 0xd5, 0x43,
	0x16, 0x4a, 0x11, 0x7d, 0x9d, 0xc9, 0xd4, 0x98, 0x91, 0xa9, 0x0e, 0xd2, 0x4c, 0xa6, 0x18, 0x53,
	0xdd, 0xaf, 0xca, 0x54, 0x23, 0xeb, 0x11, 0xc0, 0x35, 0x8d, 0x73, 0x36, 0x3b, 0x7d, 0xdd, 0xaf,
	0x23, 0x05, 0x4f, 0xf7, 0xf7, 0x0a, 0xb4, 0x8d, 0xbd, 0x61, 0x98, 0x31, 0x26, 0x86, 0x97, 0x52,
	0x93, 0x0f, 0xa1, 0x49, 0xf3, 0x88, 0xeb, 0x40, 0x69, 0xaa, 0x73, 0xe5, 0x0e, 0xd9, 0x40, 0xda,
	0x10, 0x49, 0xe4, 0x03, 0xa8, 0xf2, 0x64, 0x1c, 0xe4, 0x59, 0xec, 0xce, 0xb9, 0xc1, 0x93, 0xf1,
	0x28, 0x8b, 0xc9, 0xe7, 0xb0, 0xed, 0x18, 0x01, 0x17, 0xd7, 0x5c, 0xf1, 0xf3, 0x98, 0x05, 0x63,
	0x9a, 0x30, 0xc1, 0xc3, 0x2b, 0x3c, 0x7a, 0xdd, 0xff, 0xc0, 0xca, 0x0e, 0x0a, 0xfe, 0xb1, 0x63,
	0x93, 0x5d, 0xe8, 0x9c, 0xb3, 0x31, 0x17, 0x81, 0x75, 0xaf, 0x79, 0xc2, 0x30, 0x29, 0x2d, 0xbf,
	0x8d, 0xf4, 0x17, 0x86, 0x7c, 0xc6, 0x13, 0x46, 0xb6, 0x60, 0x9d, 0x8b, 0x88, 0x4d, 0x30, 0x35,
	0x2d, 0xdf, 0x7e, 0x90, 0x1f, 0x41, 0x23, 0x4f, 0x63, 0x49, 0x23, 0xab, 0xba, 0x81, 0x3c, 0xb0,
	0x24, 0xa3, 0xe6, 0x8d, 0xa0, 0x75, 0xcc, 0xb4, 0xbb, 0x4c, 0x9f, 0xbd, 0x36, 0xc9, 0xd1, 0x34,
	0x1b, 0x33, 0x1d, 0xcc, 0x10, 0x53, 0xb7, 0x94, 0x11, 0x8f, 0xc8, 0x13, 0xd8, 0xe4, 0x2a, 0x10,
	0x8c, 0x45, 0x81, 0xb9, 0x25, 0x36, 0xd1, 0x78, 0xdc, 0x9a, 0xdf, 0xe4, 0xea, 0x94, 0xb1, 0xe8,
	0x8c, 0x8e, 0x8f, 0x26, 0xda, 0x1b, 0x41, 0x7b, 0xde, 0xac, 0x4a, 0x49, 0x1f, 0x9a, 0x06, 0x5f,
	0x46, 0x0b, 0xd3, 0x6e, 0x2c, 0xbf, 0x03, 0x74, 0xe6, 0x36, 0xfc, 0x46, 0x6e, 0x3f, 0x8c, 0x92,
	0x77, 0x06, 0x9d, 0x43, 0xaa, 0xcb, 0x01, 0x3f, 0x84, 0x5a, 0xce, 0xa3, 0xc0, 0x19, 0x5d, 0x33,
	0xe0, 0xcb, 0x79, 0x84, 0xb0, 0xbf, 0x63, 0xb0, 0xdf, 0x40, 0x77, 0xc1, 0xaa, 0x4a, 0xc9, 0x6f,
	0xa0, 0x63, 0x3c, 0xbb, 0x70, 0x67, 0xe6, 0xef, 0x12, 0xf3, 0xe6, 0x9c, 0x26, 0x42, 0xea, 0x04,
	0x1e, 0x1c, 0x33, 0x3d, 0xe4, 0x49, 0x6a, 0xef, 0xf6, 0xdd, 0xa1, 0x7f, 0x1f, 0xea, 0x06, 0x23,
	0xf3, 0x85, 0x53, 0x33, 0x04, 0x53, 0x39, 0xde, 0x3f, 0xd7, 0xa0, 0x53, 0x32, 0x76, 0x34, 0xd1,
	0x06, 0x80, 0xa8, 0x31, 0xbd, 0xb5, 0x0d, 0xf3, 0x39, 0x88, 0xde, 0x6a, 0x8a, 0x3c, 0x86, 0x96,
	0x65, 0xf2, 0xf0, 0x0a, 0x05, 0x2c, 0x20, 0x9b, 0x28, 0xe0, 0x68, 0x53, 0x0b, 0x34, 0x63, 0x14,
	0xe1, 0xe7, 0x2c, 0xbc, 0xc8, 0x18, 0x35, 0x87, 0x40, 0x66, 0x44, 0x05, 0x62, 0xaf, 0xee, 0x63,
	0x1c, 0x5f, 0x52, 0x51, 0xb4, 0x9d, 0x8d, 0x59, 0xdb, 0xd9, 0x83, 0xad, 0x42, 0x18, 0x2b, 0xe2,
	0x42, 0x66, 0x41, 0xc2, 0xc3, 0x5e, 0x15, 0x15, 0x3b, 0x4e, 0x71, 0x94, 0xc5, 0xbf, 0x96, 0xd9,
	0x09, 0x0f, 0xa7, 0x9e, 0x33, 0x19, 0xb3, 0x5e, 0x6d, 0xe6, 0xd9, 0x97, 0xf1, 0x2c, 0xf6, 0x54,
	0x2a, 0xae, 0xb9, 0x14, 0xbd, 0x3a, 0x96, 0x32, 0xc6, 0xfe, 0xb5, 0xa3, 0x91, 0x1f, 0x43, 0x1b,
	0x85, 0x2e, 0x59, 0x26, 0x6d, 0xa6, 0x61, 0x26, 0xf5, 0x15, 0xcb, 0x24, 0xa6, 0xfb, 0x27, 0xb0,
	0x89, 0x52, 0x0a, 0x6b, 0x5e, 0x5d, 0x4a, 0xdd, 0x6b, 0xa0, 0x37, 0x54, 0x1e, 0x4e, 0xa9, 0xa6,
	0x3c, 0xac, 0xa0, 0xbe, 0x89, 0x59, 0xaf, 0x69, 0x7b, 0x07, 0xca, 0x18, 0xc2, 0xd4, 0x5b, 0xcc,
	0xae, 0x59, 0x8c, 0xcd, 0xa0, 0x35, 0xcb, 0xe7, 0x4b, 0x43, 0x34, 0x2d, 0x61, 0xd6, 0xf9, 0xda,
	0xf3, 0x9d, 0xef, 0x43, 0x68, 0x72, 0x65, 0x30, 0x95, 0xc6, 0x4c, 0xb3, 0xa8, 0xb7, 0x89, 0x58,
	0x6d, 0x70, 0xd5, 0x2f, 0x48, 0x1e, 0x85, 0xad, 0xdb, 0x40, 0x52, 0x29, 0x19, 0xb8, 0x54, 0xb0,
	0x49, 0x09, 0xaa, 0x4f, 0x96, 0x42, 0x75, 0x11, 0x3a, 0x7e, 0xc3, 0xe8, 0x1e, 0x4d, 0x2c, 0x56,
	0xff, 0x52, 0x85, 0x76, 0x5f, 0x8a, 0x8b, 0xbb, 0x40, 0x6b, 0x07, 0x9a, 0xe7, 0x34, 0xbc, 0x0a,
	0xca, 0x9d, 0x0f, 0x0c, 0x6d, 0x60, 0xbb, 0xdf, 0xaf, 0x6c, 0x1b, 0xc6, 0x98, 0xd6, 0x30, 0xa6,
	0xc7, 0x4b, 0x63, 0x9a, 0xf3, 0xf8, 0x2a, 0xd5, 0xd8, 0xab, 0xf1, 0x62, 0x3c, 0x68, 0xe9, 0xcb,
	0x3c, 0x39, 0x9f, 0xba, 0xb0, 0xf0, 0x6b, 0x20, 0xd1, 0xf9, 0xb8, 0x85, 0xe1, 0xf5, 0x25, 0x18,
	0x7e, 0x79, 0xfb, 0x86, 0x37, 0xb0, 0x05, 0x2d, 0x8f, 0xa7, 0x3c, 0x00, 0x6e, 0xc1, 0xe0, 0x23,
	0xe8, 0xa2, 0xb5, 0x90, 0x66, 0xd1, 0x34, 0xb4, 0xea, 0x0c, 0x31, 0x7d, 0x9a, 0x45, 0x2e, 0x3a,
	0xcf, 0x45, 0x57, 0x24, 0xca, 0xc1, 0x18, 0x73, 0x7e, 0x68, 0x13, 0x35, 0x95, 0xe1, 0xa1, 0x14,
	0x28, 0x53, 0x9f, 0xc9, 0x0c, 0x42, 0x29, 0x8c, 0xcc, 0xbe, 0x2b, 0x9d, 0x50, 0x66, 0x82, 0x65,
	0x41, 0x42, 0x33, 0x6b, 0x0e, 0x50, 0x14, 0xc3, 0xe9, 0x23, 0xeb, 0x84, 0x66, 0x68, 0xf4, 0x39,
	0xbc, 0x6f, 0xd3, 0x22, 0x1d, 0x1c, 0x8b, 0x38, 0x1b, 0x33, 0x8d, 0x53, 0x89, 0xa0, 0x74, 0xa1,
	0xbe, 0x80, 0x56, 0x59, 0xb2, 0x89, 0x37, 0xf6, 0x68, 0x69, 0x86, 0x0a, 0x45, 0xbf, 0x11, 0xcf,
	0x99, 0xf8, 0x0a, 0xba, 0x09, 0x0f, 0x17, 0x1c, 0xb6, 0xee, 0x62, 0xa6, 0x9d, 0xf0, 0x70, 0x3e,
	0x98, 0x9f, 0x02, 0x29, 0xe5, 0x2d, 0x48, 0xb8, 0xe0, 0x58, 0x30, 0x75, 0x7f, 0x73, 0x2e, 0x79,
	0x27, 0x5c, 0x70, 0xf2, 0x0c, 0x1e, 0xcc, 0x84, 0x43, 0x19, 0xcb, 0x2c, 0x10, 0x79, 0x82, 0x15,
	0xd4, 0xb2, 0x6d, 0xc5, 0x48, 0xf7, 0x0d, 0xe3, 0x34, 0x4f, 0xc8, 0xcf, 0x5c, 0x6a, 0xd8, 0x44,
	0x67, 0x34, 0x30, 0x00, 0x55, 0x6f, 0xb8, 0x0e, 0x2f, 0x7b, 0x1d, 0x54, 0x20, 0xae, 0x1e, 0x32,
	0x6a, 0x76, 0x0a, 0xe4, 0x98, 0x83, 0x95, 0x0e, 0x15, 0x08, 0xf6, 0xa6, 0xd7, 0xbd, 0xd3, 0xc1,
	0xe6, 0xf2, 0x73, 0xca, 0xde, 0x90, 0xcf, 0xe0, 0xe1, 0xd2, 0x7b, 0x41, 0x8b, 0x04, 0xcf, 0xb7,
	0x75, 0xeb, 0x6e, 0x8c, 0xe2, 0x36, 0xd4, 0xf3, 0xa0, 0x28, 0xc4, 0x07, 0x76, 0xcf, 0xca, 0x8f,
	0xb1, 0x12, 0xbd, 0x43, 0xa8, 0x15, 0xd2, 0xa6, 0x47, 0x59, 0xc3, 0x58, 0x0c, 0x76, 0xf9, 0xa9,
	0x23, 0x05, 0x5b, 0xfe, 0xaa, 0x4d, 0xc5, 0xfb, 0xdb, 0x5a, 0xa9, 0xf2, 0x5f, 0xa5, 0xba, 0xd8,
	0xa2, 0xe6, 0x0c, 0x99, 0xca, 0x2c, 0xd6, 0x37, 0xc3, 0xe2, 0x11, 0x5a, 0x69, 0xf9, 0xeb, 0x32,
	0xd5, 0x83, 0xc8, 0xdc, 0x04, 0x57, 0x81, 0xca, 0xd3, 0x54, 0x66, 0x3a, 0x48, 0x72, 0xcd, 0x03,
	0xc5, 0x34, 0x8e, 0x95, 0x9a, 0xdf, 0xe1, 0x6a, 0x68, 0x39, 0x27, 0xb9, 0xe6, 0x43, 0xa6, 0xc9,
	0x53, 0xd8, 0xb4, 0xbb, 0x58, 0x28, 0xc5, 0x85, 0x6d, 0x13, 0xf7, 0xb1, 0xa9, 0xb6, 0x90, 0x6c,
	0xc2, 0xc1, 0x3e, 0xf0, 0x31, 0x10, 0x2b, 0x67, 0x52, 0xad, 0x98, 0xeb, 0x28, 0xeb, 0x28, 0xda,
	0x41, 0xce, 0xc8, 0x32, 0x50, 0x7a, 0x1f, 0xb6, 0x16, 0x23, 0x08, 0x42, 0xa1, 0xdd, 0x24, 0xea,
	0xaa, 0x52, 0x0c, 0x7d, 0x81, 0x0b, 0x5e, 0x4a, 0x33, 0x8d, 0x23, 0xc3, 0x1c, 0xa9, 0x6a, 0x17,
	0xbc, 0x29, 0x6d, 0x10, 0x91, 0x53, 0xd8, 0x34, 0xcb, 0x64, 0x30, 0xb7, 0x3a, 0xd6, 0xf0, 0xfa,
	0x9f, 0xae, 0x6c, 0x20, 0xa5, 0x8d, 0xd4, 0x6f, 0x19, 0xf5, 0xdf, 0x16, 0x6b, 0x26, 0x5e, 0x83,
	0x0a, 0x92, 0x5c, 0x69, 0xac, 0xf6, 0x9a, 0xbf, 0xc1, 0xd5, 0x49, 0xae, 0xb4, 0x99, 0x21, 0xda,
	0xf4, 0xf1, 0x73, 0x39, 0x71, 0x63, 0x06, 0x30, 0x9a, 0xa6, 0xa1, 0x1e, 0xca, 0x89, 0x9d, 0x34,
	0x1f, 0x03, 0x89, 0xb8, 0x4a, 0x63, 0x7a, 0x13, 0x70, 0x11, 0x84, 0x97, 0x54, 0x08, 0x66, 0x4b,
	0xbb, 0xe6, 0x77, 0x1c, 0x67, 0x20, 0xfa, 0x96, 0xee, 0x3d, 0xc7, 0x05, 0xe4, 0x8c, 0x8e, 0x4d,
	0x42, 0xb9, 0xdd, 0x52, 0xec, 0x02, 0xb2, 0x62, 0x71, 0xf7, 0x72, 0x9c, 0x34, 0x0b, 0x1a, 0x2a,
	0x2d, 0x3d, 0x1c, 0x2a, 0xdf, 0xf2, 0xe1, 0x60, 0xe6, 0x85, 0xc3, 0xef, 0x6c, 0xf7, 0x6e, 0xf9,
	0x60, 0xa7, 0x09, 0x4e, 0x9f, 0x7f, 0xdd, 0x83, 0xb6, 0x51, 0xfd, 0xdf, 0x4e, 0x9f, 0x39, 0x8f,
	0xff, 0x0f, 0xd3, 0x67, 0xe4, 0x46, 0xc1, 0xcc, 0x9a, 0x3d, 0x62, 0xf5, 0x2d, 0x47, 0x5c, 0x30,
	0x49, 0xca, 0x26, 0x31, 0xf7, 0xff, 0x28, 0xe7, 0xfe, 0xbb, 0xfa, 0xff, 0x6f, 0xd6, 0xbf, 0xb7,
	0x0f, 0xd5, 0x13, 0x1e, 0x9a, 0xa8, 0x97, 0x3c, 0xcf, 0x09, 0xdc, 0xc7, 0xa2, 0xb4, 0xa9, 0xc5,
	0xdf, 0xde, 0x4b, 0xe8, 0x1d, 0x33, 0xed, 0x74, 0x8e, 0x26, 0xda, 0x3c, 0x89, 0x7d, 0xf6, 0x3a,
	0x67, 0x4a, 0x93, 0xe7, 0x70, 0xdf, 0x04, 0xe1, 0x5e, 0x55, 0x3f, 0x58, 0x1a, 0x91, 0xd3, 0xf4,
	0x51, 0xd2, 0xfb, 0x02, 0x1e, 0x2e, 0xb1, 0xa6, 0x52, 0x29, 0x14, 0x33, 0xb8, 0x37, 0x53, 0x1c,
	0x1f, 0x6c, 0xf8, 0x10, 0xaf, 0xe0, 0x43, 0xbc, 0x91, 0x58, 0x71, 0x23, 0xeb, 0x0d, 0xe1, 0xd1,
	0x21, 0xd5, 0xe1, 0xe5, 0xca, 0x98, 0x0e, 0x60, 0x1d, 0x2f, 0xcb, 0xb5, 0x89, 0xb7, 0x07, 0x65,
	0x45, 0xbd, 0xbf, 0x56, 0xe0, 0x87, 0xab, 0xac, 0xba, 0xd8, 0xae, 0xa0, 0x5d, 0x8a, 0xad, 0xb0,
	0x7f, 0xb4, 0xd4, 0xfe, 0xdb, 0x8d, 0x15, 0xee, 0x0d, 0x4d, 0x1d, 0x09, 0x9d, 0xdd, 0xf8, 0xcd,
	0xb9, 0x33, 0xaa, 0xed, 0x2f, 0xa0, 0x7b, 0x4b, 0xc4, 0x5c, 0xd7, 0x15, 0xbb, 0x29, 0xae, 0xeb,
	0x8a, 0xdd, 0x98, 0xc7, 0x37, 0xc2, 0x02, 0xef, 0xab, 0xe9, 0xdb, 0x8f, 0x5f, 0xdc, 0xfb, 0x79,
	0xe5, 0xe0, 0xcf, 0xeb, 0xd0, 0x1d, 0xb9, 0xb8, 0xfa, 0x18, 0xd6, 0x8b, 0x94, 0x93, 0xdf, 0x01,
	0xcc, 0x9e, 0x9b, 0xc4, 0x5b, 0x0e, 0xa0, 0xf9, 0x57, 0xee, 0xf6, 0xe3, 0x77, 0xca, 0xa8, 0xd4,
	0x7b, 0x8f, 0x7c, 0x03, 0xad, 0xd2, 0x53, 0x96, 0x3c, 0x59, 0x95, 0x95, 0xb2, 0xf9, 0xa7, 0x77,
	0x11, 0x43, 0x0f, 0x1c, 0x3a, 0x8b, 0x2f, 0x10, 0xb2, 0xbb, 0x2a, 0xb8, 0xc5, 0x17, 0xef, 0xf6,
	0x47, 0x77, 0x94, 0x9c, 0x73, 0x55, 0x1a, 0x41, 0xab, 0x5d, 0x2d, 0xce, 0xb6, 0xd5, 0xae, 0x6e,
	0xcd, 0x34, 0xef, 0x3d, 0x92, 0x41, 0xf7, 0x16, 0x48, 0xc8, 0xb3, 0x55, 0x16, 0x96, 0xe2, 0x7d,
	0x7b, 0xef, 0xae, 0xe2, 0x0e, 0xc8, 0x7f, 0xac, 0xc0, 0xf7, 0x96, 0xc3, 0x93, 0x1c, 0x7c, 0x2b,
	0x2c, 0x5b, 0xf7, 0x9f, 0xfc, 0x07, 0xf8, 0x3f, 0xfc, 0xec, 0xf7, 0x9f, 0x8e, 0x65, 0x4c, 0xc5,
	0x78, 0xef, 0xd3, 0x03, 0xad, 0xf7, 0x42, 0x99, 0xec, 0xe3, 0xbf, 0x8a, 0xa1, 0x8c, 0xf7, 0x15,
	0xcb, 0xae, 0x79, 0xc8, 0xd4, 0xb2, 0xff, 0x1e, 0xcf, 0x37, 0x50, 0xec, 0x93, 0x7f, 0x07, 0x00,
	0x00, 0xff, 0xff, 0x6d, 0x96, 0xbe, 0x9e, 0xbc, 0x14, 0x00, 0x00,
}
