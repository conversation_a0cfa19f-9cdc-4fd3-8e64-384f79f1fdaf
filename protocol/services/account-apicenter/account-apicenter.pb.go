// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/account-apicenter/account-apicenter.proto

package account_apicenter // import "golang.52tt.com/protocol/services/account-apicenter"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type BanType int32

const (
	// 未指定
	BanType_OP_UNKNOWN BanType = 0
	// 封禁
	BanType_OP_BAN BanType = 1
	// 解封
	BanType_OP_UNBAN BanType = 2
)

var BanType_name = map[int32]string{
	0: "OP_UNKNOWN",
	1: "OP_BAN",
	2: "OP_UNBAN",
}
var BanType_value = map[string]int32{
	"OP_UNKNOWN": 0,
	"OP_BAN":     1,
	"OP_UNBAN":   2,
}

func (x BanType) String() string {
	return proto.EnumName(BanType_name, int32(x))
}
func (BanType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{0}
}

type OpSourceType int32

const (
	// 未指定
	OpSourceType_OP_SOURCE_TYPE_UNSPECIFIED OpSourceType = 0
	// 黑色运营后台
	OpSourceType_OP_SOURCE_TYPE_BACKSTAGE OpSourceType = 1
)

var OpSourceType_name = map[int32]string{
	0: "OP_SOURCE_TYPE_UNSPECIFIED",
	1: "OP_SOURCE_TYPE_BACKSTAGE",
}
var OpSourceType_value = map[string]int32{
	"OP_SOURCE_TYPE_UNSPECIFIED": 0,
	"OP_SOURCE_TYPE_BACKSTAGE":   1,
}

func (x OpSourceType) String() string {
	return proto.EnumName(OpSourceType_name, int32(x))
}
func (OpSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{1}
}

type InternalAccountType int32

const (
	// 未定义内部账号类型
	InternalAccountType_INTERNAL_ACCOUNT_TYPE_UNSPECIFIED InternalAccountType = 0
	// 正式员工内部账号， 以 20 + 员工工号开头的 10 位数字
	InternalAccountType_INTERNAL_ACCOUNT_TYPE_REGULAR_EMPLOYEE InternalAccountType = 1
	// 特殊内部账号，以 210 开头 的 10 位数字
	InternalAccountType_INTERNAL_ACCOUNT_TYPE_SPECIAL InternalAccountType = 2
	// 推荐工具内部账号，以 211 开头的 10 位数字， 最多创建 10 万个
	InternalAccountType_INTERNAL_ACCOUNT_TYPE_RECOMMENDATION_TOOL InternalAccountType = 3
)

var InternalAccountType_name = map[int32]string{
	0: "INTERNAL_ACCOUNT_TYPE_UNSPECIFIED",
	1: "INTERNAL_ACCOUNT_TYPE_REGULAR_EMPLOYEE",
	2: "INTERNAL_ACCOUNT_TYPE_SPECIAL",
	3: "INTERNAL_ACCOUNT_TYPE_RECOMMENDATION_TOOL",
}
var InternalAccountType_value = map[string]int32{
	"INTERNAL_ACCOUNT_TYPE_UNSPECIFIED":         0,
	"INTERNAL_ACCOUNT_TYPE_REGULAR_EMPLOYEE":    1,
	"INTERNAL_ACCOUNT_TYPE_SPECIAL":             2,
	"INTERNAL_ACCOUNT_TYPE_RECOMMENDATION_TOOL": 3,
}

func (x InternalAccountType) String() string {
	return proto.EnumName(InternalAccountType_name, int32(x))
}
func (InternalAccountType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{2}
}

type UpdateBannedCheckRecordReq_UpdateType int32

const (
	// 未指定
	UpdateBannedCheckRecordReq_UPDATE_TYPE_UNSPECIFIED UpdateBannedCheckRecordReq_UpdateType = 0
	// 解封
	UpdateBannedCheckRecordReq_UPDATE_TYPE_RECOVER UpdateBannedCheckRecordReq_UpdateType = 1
	// 保持封禁
	UpdateBannedCheckRecordReq_UPDATE_TYPE_KEEP_BANNED UpdateBannedCheckRecordReq_UpdateType = 2
	// 驳回解封
	UpdateBannedCheckRecordReq_UPDATE_TYPE_REJECT_RECOVER UpdateBannedCheckRecordReq_UpdateType = 3
	// 解封审批中
	UpdateBannedCheckRecordReq_UPDATE_TYPE_CHECKING UpdateBannedCheckRecordReq_UpdateType = 4
	// 更新为待核实
	UpdateBannedCheckRecordReq_UPDATE_TYPE_TO_CHECK UpdateBannedCheckRecordReq_UpdateType = 5
)

var UpdateBannedCheckRecordReq_UpdateType_name = map[int32]string{
	0: "UPDATE_TYPE_UNSPECIFIED",
	1: "UPDATE_TYPE_RECOVER",
	2: "UPDATE_TYPE_KEEP_BANNED",
	3: "UPDATE_TYPE_REJECT_RECOVER",
	4: "UPDATE_TYPE_CHECKING",
	5: "UPDATE_TYPE_TO_CHECK",
}
var UpdateBannedCheckRecordReq_UpdateType_value = map[string]int32{
	"UPDATE_TYPE_UNSPECIFIED":    0,
	"UPDATE_TYPE_RECOVER":        1,
	"UPDATE_TYPE_KEEP_BANNED":    2,
	"UPDATE_TYPE_REJECT_RECOVER": 3,
	"UPDATE_TYPE_CHECKING":       4,
	"UPDATE_TYPE_TO_CHECK":       5,
}

func (x UpdateBannedCheckRecordReq_UpdateType) String() string {
	return proto.EnumName(UpdateBannedCheckRecordReq_UpdateType_name, int32(x))
}
func (UpdateBannedCheckRecordReq_UpdateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{12, 0}
}

type UpdatePhotoAlbumRequest struct {
	// 用户ID
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 客户端信息，主要用于找到对应客户端上传的 obs 域名 和 送审相关，serviceInfo 里面有值的可以不用填。
	ClientInfo *ClientInfo `protobuf:"bytes,2,opt,name=client_info,json=clientInfo,proto3" json:"client_info,omitempty"`
	// 旧版obs
	ImgKeyList []string `protobuf:"bytes,3,rep,name=img_key_list,json=imgKeyList,proto3" json:"img_key_list,omitempty"`
	// 新版obs，全量key
	NewImgKeyList        []string `protobuf:"bytes,4,rep,name=new_img_key_list,json=newImgKeyList,proto3" json:"new_img_key_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePhotoAlbumRequest) Reset()         { *m = UpdatePhotoAlbumRequest{} }
func (m *UpdatePhotoAlbumRequest) String() string { return proto.CompactTextString(m) }
func (*UpdatePhotoAlbumRequest) ProtoMessage()    {}
func (*UpdatePhotoAlbumRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{0}
}
func (m *UpdatePhotoAlbumRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePhotoAlbumRequest.Unmarshal(m, b)
}
func (m *UpdatePhotoAlbumRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePhotoAlbumRequest.Marshal(b, m, deterministic)
}
func (dst *UpdatePhotoAlbumRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePhotoAlbumRequest.Merge(dst, src)
}
func (m *UpdatePhotoAlbumRequest) XXX_Size() int {
	return xxx_messageInfo_UpdatePhotoAlbumRequest.Size(m)
}
func (m *UpdatePhotoAlbumRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePhotoAlbumRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePhotoAlbumRequest proto.InternalMessageInfo

func (m *UpdatePhotoAlbumRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdatePhotoAlbumRequest) GetClientInfo() *ClientInfo {
	if m != nil {
		return m.ClientInfo
	}
	return nil
}

func (m *UpdatePhotoAlbumRequest) GetImgKeyList() []string {
	if m != nil {
		return m.ImgKeyList
	}
	return nil
}

func (m *UpdatePhotoAlbumRequest) GetNewImgKeyList() []string {
	if m != nil {
		return m.NewImgKeyList
	}
	return nil
}

type ClientInfo struct {
	// 马甲包
	MarketId uint32 `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	// 客户端类型
	ClientType uint32 `protobuf:"varint,2,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	// 客户端 ip
	ClientIp             uint32   `protobuf:"varint,3,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClientInfo) Reset()         { *m = ClientInfo{} }
func (m *ClientInfo) String() string { return proto.CompactTextString(m) }
func (*ClientInfo) ProtoMessage()    {}
func (*ClientInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{1}
}
func (m *ClientInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientInfo.Unmarshal(m, b)
}
func (m *ClientInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientInfo.Marshal(b, m, deterministic)
}
func (dst *ClientInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientInfo.Merge(dst, src)
}
func (m *ClientInfo) XXX_Size() int {
	return xxx_messageInfo_ClientInfo.Size(m)
}
func (m *ClientInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ClientInfo proto.InternalMessageInfo

func (m *ClientInfo) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *ClientInfo) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *ClientInfo) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

type UpdatePhotoAlbumResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePhotoAlbumResponse) Reset()         { *m = UpdatePhotoAlbumResponse{} }
func (m *UpdatePhotoAlbumResponse) String() string { return proto.CompactTextString(m) }
func (*UpdatePhotoAlbumResponse) ProtoMessage()    {}
func (*UpdatePhotoAlbumResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{2}
}
func (m *UpdatePhotoAlbumResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePhotoAlbumResponse.Unmarshal(m, b)
}
func (m *UpdatePhotoAlbumResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePhotoAlbumResponse.Marshal(b, m, deterministic)
}
func (dst *UpdatePhotoAlbumResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePhotoAlbumResponse.Merge(dst, src)
}
func (m *UpdatePhotoAlbumResponse) XXX_Size() int {
	return xxx_messageInfo_UpdatePhotoAlbumResponse.Size(m)
}
func (m *UpdatePhotoAlbumResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePhotoAlbumResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePhotoAlbumResponse proto.InternalMessageInfo

type BanUserReq struct {
	// 账号
	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	// 封禁原因
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	// 操作人
	OperatorName string `protobuf:"bytes,3,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// 自动解封时间
	AutoRecoveryAt int64 `protobuf:"varint,4,opt,name=auto_recovery_at,json=autoRecoveryAt,proto3" json:"auto_recovery_at,omitempty"`
	// 是否封禁设备
	WithDevice bool `protobuf:"varint,5,opt,name=with_device,json=withDevice,proto3" json:"with_device,omitempty"`
	// 证明图片
	ProofPic string `protobuf:"bytes,6,opt,name=proof_pic,json=proofPic,proto3" json:"proof_pic,omitempty"`
	// 原因详情
	ReasonDetail string `protobuf:"bytes,7,opt,name=reason_detail,json=reasonDetail,proto3" json:"reason_detail,omitempty"`
	// 封禁时长，单位天。 永久封禁填 **********
	BannedDays uint64 `protobuf:"varint,8,opt,name=banned_days,json=bannedDays,proto3" json:"banned_days,omitempty"`
	// 业务错误返回在 resp 里
	RetRespErr bool `protobuf:"varint,9,opt,name=ret_resp_err,json=retRespErr,proto3" json:"ret_resp_err,omitempty"`
	// ttid 为空时，使用 uid
	Uid uint32 `protobuf:"varint,10,opt,name=uid,proto3" json:"uid,omitempty"`
	// 操作来源
	OpSource             OpSourceType `protobuf:"varint,11,opt,name=op_source,json=opSource,proto3,enum=account_apicenter.OpSourceType" json:"op_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BanUserReq) Reset()         { *m = BanUserReq{} }
func (m *BanUserReq) String() string { return proto.CompactTextString(m) }
func (*BanUserReq) ProtoMessage()    {}
func (*BanUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{3}
}
func (m *BanUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanUserReq.Unmarshal(m, b)
}
func (m *BanUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanUserReq.Marshal(b, m, deterministic)
}
func (dst *BanUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanUserReq.Merge(dst, src)
}
func (m *BanUserReq) XXX_Size() int {
	return xxx_messageInfo_BanUserReq.Size(m)
}
func (m *BanUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BanUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_BanUserReq proto.InternalMessageInfo

func (m *BanUserReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *BanUserReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *BanUserReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *BanUserReq) GetAutoRecoveryAt() int64 {
	if m != nil {
		return m.AutoRecoveryAt
	}
	return 0
}

func (m *BanUserReq) GetWithDevice() bool {
	if m != nil {
		return m.WithDevice
	}
	return false
}

func (m *BanUserReq) GetProofPic() string {
	if m != nil {
		return m.ProofPic
	}
	return ""
}

func (m *BanUserReq) GetReasonDetail() string {
	if m != nil {
		return m.ReasonDetail
	}
	return ""
}

func (m *BanUserReq) GetBannedDays() uint64 {
	if m != nil {
		return m.BannedDays
	}
	return 0
}

func (m *BanUserReq) GetRetRespErr() bool {
	if m != nil {
		return m.RetRespErr
	}
	return false
}

func (m *BanUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BanUserReq) GetOpSource() OpSourceType {
	if m != nil {
		return m.OpSource
	}
	return OpSourceType_OP_SOURCE_TYPE_UNSPECIFIED
}

type BanUserResp struct {
	// 错误码
	ErrCode int32 `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	// 错误描述
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanUserResp) Reset()         { *m = BanUserResp{} }
func (m *BanUserResp) String() string { return proto.CompactTextString(m) }
func (*BanUserResp) ProtoMessage()    {}
func (*BanUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{4}
}
func (m *BanUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanUserResp.Unmarshal(m, b)
}
func (m *BanUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanUserResp.Marshal(b, m, deterministic)
}
func (dst *BanUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanUserResp.Merge(dst, src)
}
func (m *BanUserResp) XXX_Size() int {
	return xxx_messageInfo_BanUserResp.Size(m)
}
func (m *BanUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BanUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_BanUserResp proto.InternalMessageInfo

func (m *BanUserResp) GetErrCode() int32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *BanUserResp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type RecoverUserReq struct {
	// 账号
	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	// 解封原因
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	// 操作人
	OperatorName string `protobuf:"bytes,3,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// 是否解封设备
	IsRecoverDevice bool `protobuf:"varint,4,opt,name=is_recover_device,json=isRecoverDevice,proto3" json:"is_recover_device,omitempty"`
	// 操作来源
	OpSource             OpSourceType `protobuf:"varint,5,opt,name=op_source,json=opSource,proto3,enum=account_apicenter.OpSourceType" json:"op_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RecoverUserReq) Reset()         { *m = RecoverUserReq{} }
func (m *RecoverUserReq) String() string { return proto.CompactTextString(m) }
func (*RecoverUserReq) ProtoMessage()    {}
func (*RecoverUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{5}
}
func (m *RecoverUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecoverUserReq.Unmarshal(m, b)
}
func (m *RecoverUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecoverUserReq.Marshal(b, m, deterministic)
}
func (dst *RecoverUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecoverUserReq.Merge(dst, src)
}
func (m *RecoverUserReq) XXX_Size() int {
	return xxx_messageInfo_RecoverUserReq.Size(m)
}
func (m *RecoverUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecoverUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecoverUserReq proto.InternalMessageInfo

func (m *RecoverUserReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *RecoverUserReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *RecoverUserReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *RecoverUserReq) GetIsRecoverDevice() bool {
	if m != nil {
		return m.IsRecoverDevice
	}
	return false
}

func (m *RecoverUserReq) GetOpSource() OpSourceType {
	if m != nil {
		return m.OpSource
	}
	return OpSourceType_OP_SOURCE_TYPE_UNSPECIFIED
}

type RecoverUserResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecoverUserResp) Reset()         { *m = RecoverUserResp{} }
func (m *RecoverUserResp) String() string { return proto.CompactTextString(m) }
func (*RecoverUserResp) ProtoMessage()    {}
func (*RecoverUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{6}
}
func (m *RecoverUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecoverUserResp.Unmarshal(m, b)
}
func (m *RecoverUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecoverUserResp.Marshal(b, m, deterministic)
}
func (dst *RecoverUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecoverUserResp.Merge(dst, src)
}
func (m *RecoverUserResp) XXX_Size() int {
	return xxx_messageInfo_RecoverUserResp.Size(m)
}
func (m *RecoverUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecoverUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecoverUserResp proto.InternalMessageInfo

type BatchRecoverUserReq struct {
	// 解封列表
	List []*RecoverUserInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 操作人
	OperatorName string `protobuf:"bytes,2,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// 是否为检查操作
	IsCheck bool `protobuf:"varint,3,opt,name=is_check,json=isCheck,proto3" json:"is_check,omitempty"`
	// 操作来源
	OpSource             OpSourceType `protobuf:"varint,4,opt,name=op_source,json=opSource,proto3,enum=account_apicenter.OpSourceType" json:"op_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchRecoverUserReq) Reset()         { *m = BatchRecoverUserReq{} }
func (m *BatchRecoverUserReq) String() string { return proto.CompactTextString(m) }
func (*BatchRecoverUserReq) ProtoMessage()    {}
func (*BatchRecoverUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{7}
}
func (m *BatchRecoverUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchRecoverUserReq.Unmarshal(m, b)
}
func (m *BatchRecoverUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchRecoverUserReq.Marshal(b, m, deterministic)
}
func (dst *BatchRecoverUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchRecoverUserReq.Merge(dst, src)
}
func (m *BatchRecoverUserReq) XXX_Size() int {
	return xxx_messageInfo_BatchRecoverUserReq.Size(m)
}
func (m *BatchRecoverUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchRecoverUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchRecoverUserReq proto.InternalMessageInfo

func (m *BatchRecoverUserReq) GetList() []*RecoverUserInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *BatchRecoverUserReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *BatchRecoverUserReq) GetIsCheck() bool {
	if m != nil {
		return m.IsCheck
	}
	return false
}

func (m *BatchRecoverUserReq) GetOpSource() OpSourceType {
	if m != nil {
		return m.OpSource
	}
	return OpSourceType_OP_SOURCE_TYPE_UNSPECIFIED
}

type RecoverUserInfo struct {
	// uid
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 解封原因
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	// 是否解封设备
	IsRecoverDevice      bool     `protobuf:"varint,4,opt,name=is_recover_device,json=isRecoverDevice,proto3" json:"is_recover_device,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecoverUserInfo) Reset()         { *m = RecoverUserInfo{} }
func (m *RecoverUserInfo) String() string { return proto.CompactTextString(m) }
func (*RecoverUserInfo) ProtoMessage()    {}
func (*RecoverUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{8}
}
func (m *RecoverUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecoverUserInfo.Unmarshal(m, b)
}
func (m *RecoverUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecoverUserInfo.Marshal(b, m, deterministic)
}
func (dst *RecoverUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecoverUserInfo.Merge(dst, src)
}
func (m *RecoverUserInfo) XXX_Size() int {
	return xxx_messageInfo_RecoverUserInfo.Size(m)
}
func (m *RecoverUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RecoverUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RecoverUserInfo proto.InternalMessageInfo

func (m *RecoverUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecoverUserInfo) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *RecoverUserInfo) GetIsRecoverDevice() bool {
	if m != nil {
		return m.IsRecoverDevice
	}
	return false
}

type BatchRecoverUserResp struct {
	// 校验可成功解封账号的数量
	ValidNum             uint32   `protobuf:"varint,1,opt,name=valid_num,json=validNum,proto3" json:"valid_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchRecoverUserResp) Reset()         { *m = BatchRecoverUserResp{} }
func (m *BatchRecoverUserResp) String() string { return proto.CompactTextString(m) }
func (*BatchRecoverUserResp) ProtoMessage()    {}
func (*BatchRecoverUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{9}
}
func (m *BatchRecoverUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchRecoverUserResp.Unmarshal(m, b)
}
func (m *BatchRecoverUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchRecoverUserResp.Marshal(b, m, deterministic)
}
func (dst *BatchRecoverUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchRecoverUserResp.Merge(dst, src)
}
func (m *BatchRecoverUserResp) XXX_Size() int {
	return xxx_messageInfo_BatchRecoverUserResp.Size(m)
}
func (m *BatchRecoverUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchRecoverUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchRecoverUserResp proto.InternalMessageInfo

func (m *BatchRecoverUserResp) GetValidNum() uint32 {
	if m != nil {
		return m.ValidNum
	}
	return 0
}

type BatchBanUserWithDeviceReq struct {
	// 设备ID
	DeviceId string `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// 需要封禁的uid列表
	UidList []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	// 操作类型
	OpType BanType `protobuf:"varint,3,opt,name=op_type,json=opType,proto3,enum=account_apicenter.BanType" json:"op_type,omitempty"`
	// 操作原因
	Reason string `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	// 自动解封时间
	AutoRecoveryAt int64 `protobuf:"varint,5,opt,name=auto_recovery_at,json=autoRecoveryAt,proto3" json:"auto_recovery_at,omitempty"`
	// 相关图片
	ProofPic string `protobuf:"bytes,6,opt,name=proof_pic,json=proofPic,proto3" json:"proof_pic,omitempty"`
	// 原因详情
	ReasonDetail string `protobuf:"bytes,7,opt,name=reason_detail,json=reasonDetail,proto3" json:"reason_detail,omitempty"`
	// 封禁时长，单位天。 永久封禁填 **********
	BannedDays uint64 `protobuf:"varint,8,opt,name=banned_days,json=bannedDays,proto3" json:"banned_days,omitempty"`
	// 操作人名称
	OperatorName string `protobuf:"bytes,9,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// 操作来源
	OpSource             OpSourceType `protobuf:"varint,10,opt,name=op_source,json=opSource,proto3,enum=account_apicenter.OpSourceType" json:"op_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchBanUserWithDeviceReq) Reset()         { *m = BatchBanUserWithDeviceReq{} }
func (m *BatchBanUserWithDeviceReq) String() string { return proto.CompactTextString(m) }
func (*BatchBanUserWithDeviceReq) ProtoMessage()    {}
func (*BatchBanUserWithDeviceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{10}
}
func (m *BatchBanUserWithDeviceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchBanUserWithDeviceReq.Unmarshal(m, b)
}
func (m *BatchBanUserWithDeviceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchBanUserWithDeviceReq.Marshal(b, m, deterministic)
}
func (dst *BatchBanUserWithDeviceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchBanUserWithDeviceReq.Merge(dst, src)
}
func (m *BatchBanUserWithDeviceReq) XXX_Size() int {
	return xxx_messageInfo_BatchBanUserWithDeviceReq.Size(m)
}
func (m *BatchBanUserWithDeviceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchBanUserWithDeviceReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchBanUserWithDeviceReq proto.InternalMessageInfo

func (m *BatchBanUserWithDeviceReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *BatchBanUserWithDeviceReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchBanUserWithDeviceReq) GetOpType() BanType {
	if m != nil {
		return m.OpType
	}
	return BanType_OP_UNKNOWN
}

func (m *BatchBanUserWithDeviceReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *BatchBanUserWithDeviceReq) GetAutoRecoveryAt() int64 {
	if m != nil {
		return m.AutoRecoveryAt
	}
	return 0
}

func (m *BatchBanUserWithDeviceReq) GetProofPic() string {
	if m != nil {
		return m.ProofPic
	}
	return ""
}

func (m *BatchBanUserWithDeviceReq) GetReasonDetail() string {
	if m != nil {
		return m.ReasonDetail
	}
	return ""
}

func (m *BatchBanUserWithDeviceReq) GetBannedDays() uint64 {
	if m != nil {
		return m.BannedDays
	}
	return 0
}

func (m *BatchBanUserWithDeviceReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *BatchBanUserWithDeviceReq) GetOpSource() OpSourceType {
	if m != nil {
		return m.OpSource
	}
	return OpSourceType_OP_SOURCE_TYPE_UNSPECIFIED
}

type BatchBanUserWithDeviceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchBanUserWithDeviceResp) Reset()         { *m = BatchBanUserWithDeviceResp{} }
func (m *BatchBanUserWithDeviceResp) String() string { return proto.CompactTextString(m) }
func (*BatchBanUserWithDeviceResp) ProtoMessage()    {}
func (*BatchBanUserWithDeviceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{11}
}
func (m *BatchBanUserWithDeviceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchBanUserWithDeviceResp.Unmarshal(m, b)
}
func (m *BatchBanUserWithDeviceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchBanUserWithDeviceResp.Marshal(b, m, deterministic)
}
func (dst *BatchBanUserWithDeviceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchBanUserWithDeviceResp.Merge(dst, src)
}
func (m *BatchBanUserWithDeviceResp) XXX_Size() int {
	return xxx_messageInfo_BatchBanUserWithDeviceResp.Size(m)
}
func (m *BatchBanUserWithDeviceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchBanUserWithDeviceResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchBanUserWithDeviceResp proto.InternalMessageInfo

type UpdateBannedCheckRecordReq struct {
	// 更新类型
	UpdateType UpdateBannedCheckRecordReq_UpdateType `protobuf:"varint,1,opt,name=update_type,json=updateType,proto3,enum=account_apicenter.UpdateBannedCheckRecordReq_UpdateType" json:"update_type,omitempty"`
	// 批量解封工单id
	TaskIdList []uint32 `protobuf:"varint,2,rep,packed,name=task_id_list,json=taskIdList,proto3" json:"task_id_list,omitempty"`
	// 操作人
	OperatorName string `protobuf:"bytes,3,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	// 解封原因
	Reason string `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	// 是否解封设备
	IsRecoverDevice bool `protobuf:"varint,5,opt,name=is_recover_device,json=isRecoverDevice,proto3" json:"is_recover_device,omitempty"`
	// 操作来源
	OpSource             OpSourceType `protobuf:"varint,6,opt,name=op_source,json=opSource,proto3,enum=account_apicenter.OpSourceType" json:"op_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateBannedCheckRecordReq) Reset()         { *m = UpdateBannedCheckRecordReq{} }
func (m *UpdateBannedCheckRecordReq) String() string { return proto.CompactTextString(m) }
func (*UpdateBannedCheckRecordReq) ProtoMessage()    {}
func (*UpdateBannedCheckRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{12}
}
func (m *UpdateBannedCheckRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBannedCheckRecordReq.Unmarshal(m, b)
}
func (m *UpdateBannedCheckRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBannedCheckRecordReq.Marshal(b, m, deterministic)
}
func (dst *UpdateBannedCheckRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBannedCheckRecordReq.Merge(dst, src)
}
func (m *UpdateBannedCheckRecordReq) XXX_Size() int {
	return xxx_messageInfo_UpdateBannedCheckRecordReq.Size(m)
}
func (m *UpdateBannedCheckRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBannedCheckRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBannedCheckRecordReq proto.InternalMessageInfo

func (m *UpdateBannedCheckRecordReq) GetUpdateType() UpdateBannedCheckRecordReq_UpdateType {
	if m != nil {
		return m.UpdateType
	}
	return UpdateBannedCheckRecordReq_UPDATE_TYPE_UNSPECIFIED
}

func (m *UpdateBannedCheckRecordReq) GetTaskIdList() []uint32 {
	if m != nil {
		return m.TaskIdList
	}
	return nil
}

func (m *UpdateBannedCheckRecordReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *UpdateBannedCheckRecordReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *UpdateBannedCheckRecordReq) GetIsRecoverDevice() bool {
	if m != nil {
		return m.IsRecoverDevice
	}
	return false
}

func (m *UpdateBannedCheckRecordReq) GetOpSource() OpSourceType {
	if m != nil {
		return m.OpSource
	}
	return OpSourceType_OP_SOURCE_TYPE_UNSPECIFIED
}

type UpdateBannedCheckRecordResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBannedCheckRecordResp) Reset()         { *m = UpdateBannedCheckRecordResp{} }
func (m *UpdateBannedCheckRecordResp) String() string { return proto.CompactTextString(m) }
func (*UpdateBannedCheckRecordResp) ProtoMessage()    {}
func (*UpdateBannedCheckRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{13}
}
func (m *UpdateBannedCheckRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBannedCheckRecordResp.Unmarshal(m, b)
}
func (m *UpdateBannedCheckRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBannedCheckRecordResp.Marshal(b, m, deterministic)
}
func (dst *UpdateBannedCheckRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBannedCheckRecordResp.Merge(dst, src)
}
func (m *UpdateBannedCheckRecordResp) XXX_Size() int {
	return xxx_messageInfo_UpdateBannedCheckRecordResp.Size(m)
}
func (m *UpdateBannedCheckRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBannedCheckRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBannedCheckRecordResp proto.InternalMessageInfo

type CreateInternalAccountRequest struct {
	// 调用方场景，必填.
	CallerScene string `protobuf:"bytes,1,opt,name=caller_scene,json=callerScene,proto3" json:"caller_scene,omitempty"`
	// 内部账号类型，必填. 详见 InternalAccountType
	InternalAccountType uint32 `protobuf:"varint,2,opt,name=internal_account_type,json=internalAccountType,proto3" json:"internal_account_type,omitempty"`
	// 员工工号，当 internal_account_type 为 INTERNAL_ACCOUNT_TYPE_REGULAR_EMPLOYEE 时必填.
	EmployeeId uint32 `protobuf:"varint,3,opt,name=employee_id,json=employeeId,proto3" json:"employee_id,omitempty"`
	// 手机号，可选. 不填系统自动生成 91 开头的 11 位数字。
	Phone string `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone,omitempty"`
	// 性别，可选. 不填默认女性。 see account.proto USER_SEX
	Sex int32 `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	// 密码，可选. 不填系统自动生成。正常 8 位及以上含数字和大小写字母，md5 加密。
	Password string `protobuf:"bytes,6,opt,name=password,proto3" json:"password,omitempty"`
	// 昵称，可选. 不填系统自动生成。
	Nickname             string   `protobuf:"bytes,7,opt,name=nickname,proto3" json:"nickname,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateInternalAccountRequest) Reset()         { *m = CreateInternalAccountRequest{} }
func (m *CreateInternalAccountRequest) String() string { return proto.CompactTextString(m) }
func (*CreateInternalAccountRequest) ProtoMessage()    {}
func (*CreateInternalAccountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{14}
}
func (m *CreateInternalAccountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateInternalAccountRequest.Unmarshal(m, b)
}
func (m *CreateInternalAccountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateInternalAccountRequest.Marshal(b, m, deterministic)
}
func (dst *CreateInternalAccountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateInternalAccountRequest.Merge(dst, src)
}
func (m *CreateInternalAccountRequest) XXX_Size() int {
	return xxx_messageInfo_CreateInternalAccountRequest.Size(m)
}
func (m *CreateInternalAccountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateInternalAccountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateInternalAccountRequest proto.InternalMessageInfo

func (m *CreateInternalAccountRequest) GetCallerScene() string {
	if m != nil {
		return m.CallerScene
	}
	return ""
}

func (m *CreateInternalAccountRequest) GetInternalAccountType() uint32 {
	if m != nil {
		return m.InternalAccountType
	}
	return 0
}

func (m *CreateInternalAccountRequest) GetEmployeeId() uint32 {
	if m != nil {
		return m.EmployeeId
	}
	return 0
}

func (m *CreateInternalAccountRequest) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *CreateInternalAccountRequest) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *CreateInternalAccountRequest) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

func (m *CreateInternalAccountRequest) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

type CreateInternalAccountResponse struct {
	// 创建的内部账号 uid.
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// tt id; 对应 account.proto 的 UserResp.alias 字段.
	TtId                 string   `protobuf:"bytes,2,opt,name=tt_id,json=ttId,proto3" json:"tt_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateInternalAccountResponse) Reset()         { *m = CreateInternalAccountResponse{} }
func (m *CreateInternalAccountResponse) String() string { return proto.CompactTextString(m) }
func (*CreateInternalAccountResponse) ProtoMessage()    {}
func (*CreateInternalAccountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_apicenter_844fef89c4058f6e, []int{15}
}
func (m *CreateInternalAccountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateInternalAccountResponse.Unmarshal(m, b)
}
func (m *CreateInternalAccountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateInternalAccountResponse.Marshal(b, m, deterministic)
}
func (dst *CreateInternalAccountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateInternalAccountResponse.Merge(dst, src)
}
func (m *CreateInternalAccountResponse) XXX_Size() int {
	return xxx_messageInfo_CreateInternalAccountResponse.Size(m)
}
func (m *CreateInternalAccountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateInternalAccountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateInternalAccountResponse proto.InternalMessageInfo

func (m *CreateInternalAccountResponse) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CreateInternalAccountResponse) GetTtId() string {
	if m != nil {
		return m.TtId
	}
	return ""
}

func init() {
	proto.RegisterType((*UpdatePhotoAlbumRequest)(nil), "account_apicenter.UpdatePhotoAlbumRequest")
	proto.RegisterType((*ClientInfo)(nil), "account_apicenter.ClientInfo")
	proto.RegisterType((*UpdatePhotoAlbumResponse)(nil), "account_apicenter.UpdatePhotoAlbumResponse")
	proto.RegisterType((*BanUserReq)(nil), "account_apicenter.BanUserReq")
	proto.RegisterType((*BanUserResp)(nil), "account_apicenter.BanUserResp")
	proto.RegisterType((*RecoverUserReq)(nil), "account_apicenter.RecoverUserReq")
	proto.RegisterType((*RecoverUserResp)(nil), "account_apicenter.RecoverUserResp")
	proto.RegisterType((*BatchRecoverUserReq)(nil), "account_apicenter.BatchRecoverUserReq")
	proto.RegisterType((*RecoverUserInfo)(nil), "account_apicenter.RecoverUserInfo")
	proto.RegisterType((*BatchRecoverUserResp)(nil), "account_apicenter.BatchRecoverUserResp")
	proto.RegisterType((*BatchBanUserWithDeviceReq)(nil), "account_apicenter.BatchBanUserWithDeviceReq")
	proto.RegisterType((*BatchBanUserWithDeviceResp)(nil), "account_apicenter.BatchBanUserWithDeviceResp")
	proto.RegisterType((*UpdateBannedCheckRecordReq)(nil), "account_apicenter.UpdateBannedCheckRecordReq")
	proto.RegisterType((*UpdateBannedCheckRecordResp)(nil), "account_apicenter.UpdateBannedCheckRecordResp")
	proto.RegisterType((*CreateInternalAccountRequest)(nil), "account_apicenter.CreateInternalAccountRequest")
	proto.RegisterType((*CreateInternalAccountResponse)(nil), "account_apicenter.CreateInternalAccountResponse")
	proto.RegisterEnum("account_apicenter.BanType", BanType_name, BanType_value)
	proto.RegisterEnum("account_apicenter.OpSourceType", OpSourceType_name, OpSourceType_value)
	proto.RegisterEnum("account_apicenter.InternalAccountType", InternalAccountType_name, InternalAccountType_value)
	proto.RegisterEnum("account_apicenter.UpdateBannedCheckRecordReq_UpdateType", UpdateBannedCheckRecordReq_UpdateType_name, UpdateBannedCheckRecordReq_UpdateType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AccountApicenterClient is the client API for AccountApicenter service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AccountApicenterClient interface {
	// 封禁用户
	BanUser(ctx context.Context, in *BanUserReq, opts ...grpc.CallOption) (*BanUserResp, error)
	// 解封用户
	RecoverUser(ctx context.Context, in *RecoverUserReq, opts ...grpc.CallOption) (*RecoverUserResp, error)
	// 批量解封用户
	BatchRecoverUser(ctx context.Context, in *BatchRecoverUserReq, opts ...grpc.CallOption) (*BatchRecoverUserResp, error)
	// 批量封禁用户
	BatchBanUserWithDevice(ctx context.Context, in *BatchBanUserWithDeviceReq, opts ...grpc.CallOption) (*BatchBanUserWithDeviceResp, error)
	// 更新封禁审核记录
	UpdateBannedCheckRecord(ctx context.Context, in *UpdateBannedCheckRecordReq, opts ...grpc.CallOption) (*UpdateBannedCheckRecordResp, error)
	// 创建内部账号
	CreateInternalAccount(ctx context.Context, in *CreateInternalAccountRequest, opts ...grpc.CallOption) (*CreateInternalAccountResponse, error)
	// 更新用户相册
	UpdatePhotoAlbum(ctx context.Context, in *UpdatePhotoAlbumRequest, opts ...grpc.CallOption) (*UpdatePhotoAlbumResponse, error)
}

type accountApicenterClient struct {
	cc *grpc.ClientConn
}

func NewAccountApicenterClient(cc *grpc.ClientConn) AccountApicenterClient {
	return &accountApicenterClient{cc}
}

func (c *accountApicenterClient) BanUser(ctx context.Context, in *BanUserReq, opts ...grpc.CallOption) (*BanUserResp, error) {
	out := new(BanUserResp)
	err := c.cc.Invoke(ctx, "/account_apicenter.AccountApicenter/BanUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountApicenterClient) RecoverUser(ctx context.Context, in *RecoverUserReq, opts ...grpc.CallOption) (*RecoverUserResp, error) {
	out := new(RecoverUserResp)
	err := c.cc.Invoke(ctx, "/account_apicenter.AccountApicenter/RecoverUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountApicenterClient) BatchRecoverUser(ctx context.Context, in *BatchRecoverUserReq, opts ...grpc.CallOption) (*BatchRecoverUserResp, error) {
	out := new(BatchRecoverUserResp)
	err := c.cc.Invoke(ctx, "/account_apicenter.AccountApicenter/BatchRecoverUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountApicenterClient) BatchBanUserWithDevice(ctx context.Context, in *BatchBanUserWithDeviceReq, opts ...grpc.CallOption) (*BatchBanUserWithDeviceResp, error) {
	out := new(BatchBanUserWithDeviceResp)
	err := c.cc.Invoke(ctx, "/account_apicenter.AccountApicenter/BatchBanUserWithDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountApicenterClient) UpdateBannedCheckRecord(ctx context.Context, in *UpdateBannedCheckRecordReq, opts ...grpc.CallOption) (*UpdateBannedCheckRecordResp, error) {
	out := new(UpdateBannedCheckRecordResp)
	err := c.cc.Invoke(ctx, "/account_apicenter.AccountApicenter/UpdateBannedCheckRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountApicenterClient) CreateInternalAccount(ctx context.Context, in *CreateInternalAccountRequest, opts ...grpc.CallOption) (*CreateInternalAccountResponse, error) {
	out := new(CreateInternalAccountResponse)
	err := c.cc.Invoke(ctx, "/account_apicenter.AccountApicenter/CreateInternalAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountApicenterClient) UpdatePhotoAlbum(ctx context.Context, in *UpdatePhotoAlbumRequest, opts ...grpc.CallOption) (*UpdatePhotoAlbumResponse, error) {
	out := new(UpdatePhotoAlbumResponse)
	err := c.cc.Invoke(ctx, "/account_apicenter.AccountApicenter/UpdatePhotoAlbum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountApicenterServer is the server API for AccountApicenter service.
type AccountApicenterServer interface {
	// 封禁用户
	BanUser(context.Context, *BanUserReq) (*BanUserResp, error)
	// 解封用户
	RecoverUser(context.Context, *RecoverUserReq) (*RecoverUserResp, error)
	// 批量解封用户
	BatchRecoverUser(context.Context, *BatchRecoverUserReq) (*BatchRecoverUserResp, error)
	// 批量封禁用户
	BatchBanUserWithDevice(context.Context, *BatchBanUserWithDeviceReq) (*BatchBanUserWithDeviceResp, error)
	// 更新封禁审核记录
	UpdateBannedCheckRecord(context.Context, *UpdateBannedCheckRecordReq) (*UpdateBannedCheckRecordResp, error)
	// 创建内部账号
	CreateInternalAccount(context.Context, *CreateInternalAccountRequest) (*CreateInternalAccountResponse, error)
	// 更新用户相册
	UpdatePhotoAlbum(context.Context, *UpdatePhotoAlbumRequest) (*UpdatePhotoAlbumResponse, error)
}

func RegisterAccountApicenterServer(s *grpc.Server, srv AccountApicenterServer) {
	s.RegisterService(&_AccountApicenter_serviceDesc, srv)
}

func _AccountApicenter_BanUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BanUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountApicenterServer).BanUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/account_apicenter.AccountApicenter/BanUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountApicenterServer).BanUser(ctx, req.(*BanUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountApicenter_RecoverUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecoverUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountApicenterServer).RecoverUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/account_apicenter.AccountApicenter/RecoverUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountApicenterServer).RecoverUser(ctx, req.(*RecoverUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountApicenter_BatchRecoverUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchRecoverUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountApicenterServer).BatchRecoverUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/account_apicenter.AccountApicenter/BatchRecoverUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountApicenterServer).BatchRecoverUser(ctx, req.(*BatchRecoverUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountApicenter_BatchBanUserWithDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchBanUserWithDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountApicenterServer).BatchBanUserWithDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/account_apicenter.AccountApicenter/BatchBanUserWithDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountApicenterServer).BatchBanUserWithDevice(ctx, req.(*BatchBanUserWithDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountApicenter_UpdateBannedCheckRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBannedCheckRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountApicenterServer).UpdateBannedCheckRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/account_apicenter.AccountApicenter/UpdateBannedCheckRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountApicenterServer).UpdateBannedCheckRecord(ctx, req.(*UpdateBannedCheckRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountApicenter_CreateInternalAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInternalAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountApicenterServer).CreateInternalAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/account_apicenter.AccountApicenter/CreateInternalAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountApicenterServer).CreateInternalAccount(ctx, req.(*CreateInternalAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountApicenter_UpdatePhotoAlbum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePhotoAlbumRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountApicenterServer).UpdatePhotoAlbum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/account_apicenter.AccountApicenter/UpdatePhotoAlbum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountApicenterServer).UpdatePhotoAlbum(ctx, req.(*UpdatePhotoAlbumRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _AccountApicenter_serviceDesc = grpc.ServiceDesc{
	ServiceName: "account_apicenter.AccountApicenter",
	HandlerType: (*AccountApicenterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BanUser",
			Handler:    _AccountApicenter_BanUser_Handler,
		},
		{
			MethodName: "RecoverUser",
			Handler:    _AccountApicenter_RecoverUser_Handler,
		},
		{
			MethodName: "BatchRecoverUser",
			Handler:    _AccountApicenter_BatchRecoverUser_Handler,
		},
		{
			MethodName: "BatchBanUserWithDevice",
			Handler:    _AccountApicenter_BatchBanUserWithDevice_Handler,
		},
		{
			MethodName: "UpdateBannedCheckRecord",
			Handler:    _AccountApicenter_UpdateBannedCheckRecord_Handler,
		},
		{
			MethodName: "CreateInternalAccount",
			Handler:    _AccountApicenter_CreateInternalAccount_Handler,
		},
		{
			MethodName: "UpdatePhotoAlbum",
			Handler:    _AccountApicenter_UpdatePhotoAlbum_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/account-apicenter/account-apicenter.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/account-apicenter/account-apicenter.proto", fileDescriptor_account_apicenter_844fef89c4058f6e)
}

var fileDescriptor_account_apicenter_844fef89c4058f6e = []byte{
	// 1407 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x57, 0x51, 0x53, 0x1b, 0xb7,
	0x13, 0xe7, 0xb0, 0x0d, 0xf6, 0x1a, 0xc8, 0x45, 0x24, 0xc1, 0x31, 0x21, 0x31, 0xf7, 0x9f, 0x7f,
	0xe2, 0xd2, 0x02, 0x1d, 0x98, 0x74, 0xf2, 0xd0, 0xe9, 0x8c, 0x31, 0x97, 0xd4, 0x01, 0x7c, 0x9e,
	0xc3, 0x4e, 0x26, 0x7d, 0xd1, 0x5c, 0xee, 0x14, 0x73, 0x83, 0x7d, 0xba, 0x48, 0x32, 0xd4, 0x9d,
	0xbe, 0xf5, 0x0b, 0xf4, 0xbb, 0x74, 0xa6, 0x0f, 0xfd, 0x0a, 0x7d, 0xea, 0x97, 0xe9, 0x6b, 0x3b,
	0xd2, 0xdd, 0x19, 0x8c, 0xcf, 0x29, 0x7e, 0x68, 0xdf, 0xa4, 0xdd, 0x95, 0x76, 0xf7, 0xb7, 0xab,
	0x9f, 0x24, 0x78, 0x21, 0xc4, 0xee, 0xc7, 0x81, 0xef, 0x9e, 0x73, 0xbf, 0x77, 0x41, 0xd8, 0xae,
	0xe3, 0xba, 0x74, 0x10, 0x88, 0x6d, 0x27, 0xf4, 0x5d, 0x12, 0x88, 0x34, 0xc9, 0x4e, 0xc8, 0xa8,
	0xa0, 0xe8, 0x6e, 0xac, 0xc0, 0x23, 0x85, 0xf1, 0xab, 0x06, 0x6b, 0x9d, 0xd0, 0x73, 0x04, 0x69,
	0x9d, 0x51, 0x41, 0x6b, 0xbd, 0xf7, 0x83, 0xbe, 0x4d, 0x3e, 0x0e, 0x08, 0x17, 0x48, 0x87, 0xcc,
	0xc0, 0xf7, 0x4a, 0x5a, 0x45, 0xab, 0x2e, 0xdb, 0x72, 0x88, 0xbe, 0x81, 0xa2, 0xdb, 0xf3, 0x49,
	0x20, 0xb0, 0x1f, 0x7c, 0xa0, 0xa5, 0xf9, 0x8a, 0x56, 0x2d, 0xee, 0x6d, 0xec, 0x4c, 0x6c, 0xbb,
	0x53, 0x57, 0x56, 0x8d, 0xe0, 0x03, 0xb5, 0xc1, 0x1d, 0x8d, 0x51, 0x05, 0x96, 0xfc, 0x7e, 0x17,
	0x9f, 0x93, 0x21, 0xee, 0xf9, 0x5c, 0x94, 0x32, 0x95, 0x4c, 0xb5, 0x60, 0x83, 0xdf, 0xef, 0x1e,
	0x91, 0xe1, 0xb1, 0xcf, 0x05, 0x7a, 0x06, 0x7a, 0x40, 0x2e, 0xf1, 0x98, 0x55, 0x56, 0x59, 0x2d,
	0x07, 0xe4, 0xb2, 0x31, 0x32, 0x34, 0xba, 0x00, 0x57, 0x4e, 0xd0, 0x3a, 0x14, 0xfa, 0x0e, 0x3b,
	0x27, 0x02, 0x8f, 0x02, 0xce, 0x47, 0x82, 0x86, 0x87, 0x9e, 0x8c, 0xa2, 0x16, 0xc3, 0x90, 0xa8,
	0xa8, 0x97, 0x93, 0xb0, 0xda, 0xc3, 0x90, 0xc8, 0xd5, 0x49, 0x5a, 0x61, 0x29, 0x13, 0xad, 0x8e,
	0xa3, 0x0e, 0x8d, 0x32, 0x94, 0x26, 0x01, 0xe2, 0x21, 0x0d, 0x38, 0x31, 0x7e, 0xca, 0x00, 0x1c,
	0x38, 0x41, 0x87, 0x13, 0x66, 0x93, 0x8f, 0xa8, 0x04, 0x8b, 0x31, 0x14, 0x2a, 0x86, 0x82, 0x9d,
	0x4c, 0xd1, 0x03, 0x58, 0x60, 0xc4, 0xe1, 0x34, 0x50, 0xde, 0x0b, 0x76, 0x3c, 0x43, 0xff, 0x83,
	0x65, 0x1a, 0x12, 0xe6, 0x08, 0xca, 0x70, 0xe0, 0xf4, 0x89, 0xf2, 0x5e, 0xb0, 0x97, 0x12, 0x61,
	0xd3, 0xe9, 0x13, 0x54, 0x05, 0xdd, 0x19, 0x08, 0x8a, 0x19, 0x71, 0xe9, 0x05, 0x61, 0x43, 0xec,
	0x48, 0x4c, 0xb4, 0x6a, 0xc6, 0x5e, 0x91, 0x72, 0x3b, 0x16, 0xd7, 0x84, 0xcc, 0xf4, 0xd2, 0x17,
	0x67, 0xd8, 0x23, 0x17, 0xbe, 0x4b, 0x4a, 0xb9, 0x8a, 0x56, 0xcd, 0xdb, 0x20, 0x45, 0x87, 0x4a,
	0x22, 0x33, 0x0d, 0x19, 0xa5, 0x1f, 0x70, 0xe8, 0xbb, 0xa5, 0x05, 0xe5, 0x2b, 0xaf, 0x04, 0x2d,
	0xdf, 0x95, 0xc1, 0x44, 0x61, 0x61, 0x8f, 0x08, 0xc7, 0xef, 0x95, 0x16, 0xa3, 0x60, 0x22, 0xe1,
	0xa1, 0x92, 0x49, 0x17, 0xef, 0x9d, 0x20, 0x20, 0x1e, 0xf6, 0x9c, 0x21, 0x2f, 0xe5, 0x2b, 0x5a,
	0x35, 0x6b, 0x43, 0x24, 0x3a, 0x74, 0x86, 0x5c, 0xd6, 0x98, 0x11, 0x81, 0x19, 0xe1, 0x21, 0x26,
	0x8c, 0x95, 0x0a, 0x51, 0x10, 0x8c, 0x08, 0x09, 0x9b, 0xc9, 0x58, 0xd2, 0x57, 0x70, 0xd5, 0x57,
	0x5f, 0x43, 0x81, 0x86, 0x98, 0xd3, 0x01, 0x73, 0x49, 0xa9, 0x58, 0xd1, 0xaa, 0x2b, 0x7b, 0x4f,
	0x52, 0xba, 0xca, 0x0a, 0x4f, 0x95, 0x89, 0x2c, 0x9a, 0x9d, 0xa7, 0xf1, 0xcc, 0xa8, 0x41, 0x71,
	0x54, 0x04, 0x1e, 0xa2, 0x87, 0x90, 0x27, 0x8c, 0x61, 0x97, 0x7a, 0x44, 0x95, 0x21, 0x67, 0x2f,
	0x12, 0xc6, 0xea, 0xd4, 0x23, 0x68, 0x0d, 0xe4, 0x10, 0xf7, 0x79, 0x37, 0xa9, 0x03, 0x61, 0xec,
	0x84, 0x77, 0x8d, 0x3f, 0x34, 0x58, 0x89, 0x71, 0xfc, 0x97, 0x8b, 0xb9, 0x05, 0x77, 0x7d, 0x9e,
	0x94, 0x32, 0x29, 0x54, 0x56, 0x61, 0x74, 0xc7, 0xe7, 0x71, 0x0c, 0x71, 0xb5, 0xc6, 0x60, 0xc9,
	0xcd, 0x0a, 0xcb, 0x5d, 0xb8, 0x33, 0x96, 0x12, 0x0f, 0x8d, 0xdf, 0x35, 0x58, 0x3d, 0x70, 0x84,
	0x7b, 0x76, 0x23, 0xd7, 0xaf, 0x20, 0xab, 0x4e, 0x9a, 0x56, 0xc9, 0x54, 0x8b, 0x7b, 0x46, 0x8a,
	0x8f, 0x6b, 0x0b, 0xd4, 0xa9, 0x56, 0xf6, 0x93, 0x19, 0xcf, 0xa7, 0x64, 0xfc, 0x10, 0xf2, 0x3e,
	0xc7, 0xee, 0x19, 0x71, 0xcf, 0x15, 0x22, 0x79, 0x7b, 0xd1, 0xe7, 0x75, 0x39, 0x1d, 0x4f, 0x30,
	0x3b, 0x6b, 0x82, 0xdd, 0xb1, 0x04, 0x15, 0x0f, 0x4c, 0x52, 0xd6, 0xb4, 0x62, 0xcd, 0x50, 0x07,
	0x63, 0x1f, 0xee, 0x4d, 0xa2, 0xc6, 0x43, 0x79, 0x9a, 0x2e, 0x9c, 0x9e, 0xef, 0xe1, 0x60, 0xd0,
	0x4f, 0x58, 0x47, 0x09, 0x9a, 0x83, 0xbe, 0xf1, 0x73, 0x06, 0x1e, 0xaa, 0x55, 0x71, 0x6f, 0xbe,
	0x1d, 0x9d, 0x42, 0x89, 0xf8, 0x3a, 0x14, 0x22, 0x9f, 0x09, 0x61, 0x15, 0xec, 0x7c, 0x24, 0x68,
	0x78, 0x12, 0xb1, 0x81, 0xef, 0x45, 0xe4, 0x37, 0x5f, 0xc9, 0x54, 0x97, 0xed, 0xc5, 0x81, 0xef,
	0x29, 0x7e, 0xdc, 0x87, 0x45, 0x1a, 0x46, 0x3c, 0x96, 0x51, 0x78, 0x95, 0x53, 0xf0, 0x3a, 0x70,
	0x02, 0x05, 0xd5, 0x02, 0x0d, 0x15, 0xbf, 0x5d, 0x61, 0x90, 0x1d, 0xc3, 0x20, 0x8d, 0x58, 0x72,
	0xa9, 0xc4, 0xf2, 0x1f, 0xf0, 0xc6, 0x44, 0x2f, 0x15, 0x52, 0x7a, 0x69, 0xac, 0x61, 0x60, 0xd6,
	0x86, 0x79, 0x04, 0xe5, 0x69, 0x15, 0xe1, 0xa1, 0xf1, 0x67, 0x06, 0xca, 0x11, 0xd3, 0x1f, 0xa8,
	0xa8, 0x54, 0x8b, 0x4a, 0x0c, 0x98, 0x27, 0x2b, 0xf6, 0x0e, 0x8a, 0x03, 0xa5, 0x8d, 0xd0, 0xd7,
	0x94, 0xf3, 0x17, 0x29, 0xce, 0xa7, 0xef, 0x11, 0xab, 0x54, 0x54, 0x30, 0x18, 0x8d, 0x25, 0x65,
	0x0a, 0x87, 0x9f, 0xe3, 0xf1, 0x9a, 0x83, 0x94, 0x35, 0xa2, 0xb2, 0xdf, 0x8a, 0x5a, 0xa6, 0x95,
	0x39, 0xb5, 0xd5, 0x73, 0xb7, 0xa0, 0x9c, 0x85, 0x59, 0x01, 0xfe, 0x45, 0x03, 0xb8, 0xca, 0x11,
	0xad, 0xc3, 0x5a, 0xa7, 0x75, 0x58, 0x6b, 0x9b, 0xb8, 0xfd, 0xae, 0x65, 0xe2, 0x4e, 0xf3, 0xb4,
	0x65, 0xd6, 0x1b, 0x2f, 0x1b, 0xe6, 0xa1, 0x3e, 0x87, 0xd6, 0x60, 0xf5, 0xba, 0xd2, 0x36, 0xeb,
	0xd6, 0x1b, 0xd3, 0xd6, 0xb5, 0x9b, 0xab, 0x8e, 0x4c, 0xb3, 0x85, 0x0f, 0x6a, 0xcd, 0xa6, 0x79,
	0xa8, 0xcf, 0xa3, 0xc7, 0x50, 0x1e, 0x5f, 0xf5, 0xda, 0xac, 0xb7, 0x47, 0x8b, 0x33, 0xa8, 0x04,
	0xf7, 0xae, 0xeb, 0xeb, 0xdf, 0x9a, 0xf5, 0xa3, 0x46, 0xf3, 0x95, 0x9e, 0xbd, 0xa9, 0x69, 0x5b,
	0x91, 0x52, 0xcf, 0x19, 0x1b, 0xb0, 0x3e, 0xb5, 0x66, 0xb2, 0x2f, 0x34, 0x78, 0x54, 0x67, 0xc4,
	0x11, 0xa4, 0x21, 0x73, 0x0f, 0x9c, 0x5e, 0x2d, 0xc2, 0x23, 0x79, 0x27, 0x6d, 0xc2, 0x92, 0xeb,
	0xf4, 0x7a, 0x84, 0x61, 0xee, 0x92, 0x80, 0xc4, 0xc7, 0xb9, 0x18, 0xc9, 0x4e, 0xa5, 0x08, 0xed,
	0xc1, 0x7d, 0x3f, 0x5e, 0x8c, 0x13, 0x34, 0xaf, 0x3d, 0x46, 0x56, 0xfd, 0xf1, 0x9d, 0x15, 0x7a,
	0x4f, 0xa0, 0x48, 0xfa, 0x61, 0x8f, 0x0e, 0x89, 0x22, 0x89, 0xe8, 0x5d, 0x02, 0x89, 0xa8, 0xe1,
	0xa1, 0x7b, 0x90, 0x0b, 0xcf, 0x68, 0x40, 0xe2, 0x72, 0x47, 0x13, 0x49, 0x81, 0x9c, 0x7c, 0xaf,
	0xea, 0x9b, 0xb3, 0xe5, 0x10, 0x95, 0x21, 0x1f, 0x3a, 0x9c, 0x5f, 0x52, 0xe6, 0x8d, 0xce, 0x6e,
	0x3c, 0x97, 0xba, 0xc0, 0x77, 0xcf, 0x55, 0x4f, 0x45, 0xc7, 0x76, 0x34, 0x37, 0x5e, 0xc2, 0xc6,
	0x94, 0xbc, 0xa3, 0xe7, 0x4f, 0x0a, 0xdb, 0xae, 0x42, 0x4e, 0xa8, 0x37, 0x58, 0x44, 0xb6, 0x59,
	0x21, 0x1a, 0xde, 0xd6, 0x3e, 0x2c, 0xc6, 0x8c, 0x84, 0x56, 0x00, 0xac, 0x16, 0xee, 0x34, 0x8f,
	0x9a, 0xd6, 0xdb, 0xa6, 0x3e, 0x87, 0x00, 0x16, 0x2c, 0x55, 0x5d, 0x5d, 0x43, 0x4b, 0x90, 0x57,
	0x3a, 0x39, 0x9b, 0xdf, 0x3a, 0x86, 0xa5, 0xeb, 0x4d, 0x26, 0x0b, 0x6f, 0xb5, 0xf0, 0xa9, 0xd5,
	0xb1, 0xeb, 0xa9, 0xed, 0xf4, 0x08, 0x4a, 0x37, 0xf4, 0x07, 0xb5, 0xfa, 0xd1, 0x69, 0xbb, 0xf6,
	0xca, 0xd4, 0xb5, 0xad, 0xdf, 0x34, 0x58, 0x6d, 0xa4, 0x60, 0xfc, 0x7f, 0xd8, 0x6c, 0x34, 0xdb,
	0xa6, 0xdd, 0xac, 0x1d, 0xe3, 0x5a, 0xbd, 0x6e, 0x75, 0x9a, 0xed, 0xb4, 0xcd, 0xb7, 0xe0, 0x69,
	0xba, 0x99, 0x6d, 0xbe, 0xea, 0x1c, 0xd7, 0x6c, 0x6c, 0x9e, 0xb4, 0x8e, 0xad, 0x77, 0xa6, 0xa9,
	0x6b, 0x68, 0x13, 0x36, 0xd2, 0x6d, 0xd5, 0x86, 0xb5, 0x63, 0x7d, 0x1e, 0x6d, 0xc3, 0x67, 0xd3,
	0xb6, 0xab, 0x5b, 0x27, 0x27, 0x66, 0xf3, 0xb0, 0xd6, 0x6e, 0x58, 0x4d, 0xdc, 0xb6, 0xac, 0x63,
	0x3d, 0xb3, 0xf7, 0x57, 0x0e, 0xf4, 0x38, 0xe8, 0x5a, 0x72, 0x02, 0xd1, 0x6b, 0x05, 0xaa, 0xa4,
	0x31, 0xb4, 0x91, 0x7e, 0x05, 0xc4, 0x97, 0x7b, 0xf9, 0xf1, 0xa7, 0xd4, 0x3c, 0x34, 0xe6, 0xd0,
	0x1b, 0x28, 0x5e, 0xbb, 0xda, 0xd0, 0xe6, 0xa7, 0xef, 0x7f, 0xb9, 0xa7, 0xf1, 0x4f, 0x26, 0x6a,
	0x5f, 0x02, 0xfa, 0xcd, 0x7b, 0x13, 0x3d, 0x4d, 0x8d, 0x66, 0xe2, 0x49, 0x52, 0x7e, 0x76, 0x2b,
	0x3b, 0xe5, 0xe6, 0x12, 0x1e, 0xa4, 0xd3, 0x3a, 0xfa, 0x62, 0xda, 0x26, 0x69, 0x77, 0x72, 0x79,
	0x7b, 0x06, 0x6b, 0xe5, 0xf8, 0x87, 0xe4, 0xef, 0x34, 0x41, 0x1c, 0x68, 0x7b, 0xa6, 0x8b, 0xa1,
	0xbc, 0x33, 0x8b, 0xb9, 0xf2, 0xfd, 0x23, 0xdc, 0x4f, 0x3d, 0x9c, 0x68, 0x37, 0xed, 0x3b, 0xf6,
	0x09, 0xfa, 0x2a, 0x7f, 0x79, 0xfb, 0x05, 0xf1, 0xb7, 0x67, 0x0e, 0xf5, 0x41, 0xbf, 0xf9, 0x29,
	0x42, 0x5b, 0x53, 0x73, 0x98, 0xf8, 0x5a, 0x96, 0x3f, 0xbf, 0x95, 0x6d, 0xe2, 0xee, 0xe0, 0xf9,
	0x77, 0xfb, 0x5d, 0xda, 0x73, 0x82, 0xee, 0xce, 0xf3, 0x3d, 0x21, 0x76, 0x5c, 0xda, 0xdf, 0x55,
	0x3f, 0x5a, 0x97, 0xf6, 0x76, 0x39, 0x61, 0xb2, 0x24, 0x7c, 0xf2, 0xd7, 0xfb, 0x7e, 0x41, 0x19,
	0xed, 0xff, 0x1d, 0x00, 0x00, 0xff, 0xff, 0xb1, 0xad, 0x3a, 0x8f, 0x32, 0x0f, 0x00, 0x00,
}
