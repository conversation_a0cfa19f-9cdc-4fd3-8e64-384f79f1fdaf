syntax = "proto3";

package user_auth;

option go_package = "golang.52tt.com/protocol/services/user-auth";

// 用户授权
service UserAuth {
  // 创建授权申请
  rpc CreateAuthApply(CreateAuthApplyReq) returns (CreateAuthApplyResp) {}
  // 处理授权申请
  rpc ProcAuthApply(ProcAuthApplyReq) returns (ProcAuthApplyResp) {}
  // 获取授权申请信息
  rpc GetAuthApplyKey(GetAuthApplyKeyReq) returns (GetAuthApplyKeyResp) {}
  rpc GetAuthApplyInfo(GetAuthApplyInfoReq) returns (GetAuthApplyInfoResp) {}
  rpc GetAuthApplyInfoByUid(GetAuthApplyInfoByUidReq) returns (GetAuthApplyInfoByUidResp) {}
}

message CreateAuthApplyReq {
  uint32 uid = 1;
  string scene = 2;
  int64 ttl = 3;
  bytes extend_info = 4; // 自定义扩展信息
  map<string, string> extend_params = 5; // 自定义参数
  // 自定义apply_id
  string apply_id = 6;
  // 可选，apply_key 的额外有效期
  int64 apply_key_ttl = 7;
}

message CreateAuthApplyResp {
  string apply_key = 1; // 授权申请唯一凭证
  // 申请ID
  string apply_id = 2;
}

// 授权类型
enum AuthType {
  AUTH_TYPE_UNKNOWN = 0;
  AUTH_TYPE_COMMON = 1; // 通用授权
  // AUTH_TYPE_SMS_VERIFY = 2; // 短信验证授权
  // AUTH_TYPE_FACE_VERIFY = 3; // 人脸验证授权
}

// 申请状态
enum ApplyState {
  APPLY_STATE_UNKNOWN = 0;    // 无申请信息（申请过期）
  APPLY_STATE_NORMAL = 1;     // 有申请信息
}

// 授权状态
enum AuthState {
  AUTH_STATE_UNKNOWN = 0;
  AUTH_STATE_PASS = 1; // 通过
  AUTH_STATE_REJECT = 2; // 拒绝
  AUTH_STATE_CANCEL = 3; // 取消
  AUTH_STATE_TIMEOUT = 4; // 超时
  AUTH_STATE_LINK = 5; // 绑定
}

message AuthInfo {
  AuthType auth_type = 1;
  AuthState auth_state = 2;
  int64 ttl = 3;
  bytes extend_info = 4; // 自定义扩展信息
  map<string, string> extend_params = 5; // 自定义参数
}

message ProcAuthApplyReq {
  string apply_key = 1;
  AuthInfo auth_info = 2;
  // 新的有效期，0不更新
  int64 ttl = 3;
  // query by apply_id+uid
  uint32 uid = 4;
  // query by apply_id+uid
  string apply_id = 5;
}

message ProcAuthApplyResp {
}

message GetAuthApplyKeyReq {
  uint32 uid = 1;
  string scene = 2;
}

message GetAuthApplyKeyResp {
  string apply_key = 1;
}

message GetAuthApplyInfoReq {
  string apply_key = 1;
  repeated AuthType auth_type_list = 2;
  // query by apply_id+uid
  uint32 uid = 3;
  // query by apply_id+uid
  string apply_id = 4;
}

message ApplyInfo {
  uint32 uid = 1;
  string scene = 2;
  string apply_key = 3;
  ApplyState apply_state = 4;
  bytes extend_info = 5; // 自定义扩展信息
  map<string, string> extend_params = 6; // 自定义参数
  repeated AuthInfo auth_info_list = 7;
}

message GetAuthApplyInfoResp {
  ApplyInfo apply_info = 1;
}

message GetAuthApplyInfoByUidReq {
  uint32 uid = 1;
  string scene = 2;
  repeated AuthType auth_type_list = 3;
}

message GetAuthApplyInfoByUidResp {
  ApplyInfo apply_info = 1;
}

