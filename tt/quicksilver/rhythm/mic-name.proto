syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package rhythm.mic_name;
option go_package = "golang.52tt.com/protocol/services/rhythm/mic-name";
import "tt/quicksilver/extension/options/options.proto";


enum ActionMode{
  UnknownActionMode = 0;
  SwitchActionMode = 1; /* 麦位开关模式 */
  ExamineModeActionMode = 2; /* 审核模式 */
}

service MicName {
  option (service.options.service_ext) = {
    service_name: "rhythm-mic-name"
  };
  //开启或关闭麦位名称功能
  rpc SwitchMicNameFunc(SwitchMicNameFuncReq)returns(SwitchMicNameFuncResp){}
  //获取房间的麦位名称信息
  rpc GetMicName(GetMicNameReq)returns(GetMicNameResp){}
  //设置麦位名称信息
  rpc SetMicName(SetMicNameReq)returns(SetMicNameResp){}
  //尝试设置麦位名称，如果需要审核则返回需要审核，logic层再进行处理
  rpc TrySetMicName(TrySetMicNameReq)returns(TrySetMicNameResp){}

  rpc StreamTransfer(stream EventWrap) returns (stream EventWrap);
}
message SwitchMicNameFuncReq{
  uint32 channel_id=1;     //房间CID
  bool  func_switch=2;    //麦位名称功能开关
}
message SwitchMicNameFuncResp{

}
message GetMicNameReq{
  uint32 channel_id=1;
}
message GetMicNameResp{
  MicNameInfo mic_info=1;     //麦位名称信息

}
message SetMicNameReq{
  uint32 channel_id=1;
  MicNameInfo mic_info=2;    //麦位名称数组
  uint32 uid=3;
}
message SetMicNameResp{
}
//麦位名称推送
message MicNameInfo{
  bool status_audit=1;        //审核状态
  bool func_switch=2;               //麦位名称功能开关
  repeated MicNameArray mic_name_array=3;   //麦位名称数组
  uint32 uid=4;       //提交审核的人
  ActionMode action_mode=5;  //判断是那种动作模式
}
message MicNameArray{
  uint32 mic_id=1;      //麦序
  string mic_name=2;    //麦位名称
}

message TrySetMicNameReq{
  uint32 channel_id = 1;
  MicNameInfo mic_info = 2;    //麦位名称数组
  uint32 uid = 3;
}

message TrySetMicNameResp{
  bool need_audit = 1;    //是否需要审核
}


enum EventType {
  EventTypeNone    = 0;

  // common: 1-10
  ZegoError      = 1;
  ServerError    = 2;
  ClientError    = 3;
  Audio          = 4;

  // client action: 11-99
  Exit            = 10; // exit room
  Login           = 11; // login room
  StartPullStream = 12; // start pull stream
  StopPullStream  = 13; // stop pull stream
  SendTextMessage = 14; // send broadcast message

  // zego callback: 100-199
  LoginResult    = 101; // login result
  StreamUpdate   = 102; // stream update
}

message EventWrap {
  EventType  event = 1;
  string     json  = 2;
  bytes      data  = 3;
}