syntax = "proto3";

// namespace
option go_package = "golang.52tt.com/protocol/services/present-limit";
package present_limit;

service PresentLimit {
  // 更新规则集
  rpc UpdateRuleSet(UpdateRuleSetReq) returns (UpdateRuleSetResp);
  // 获取规则集的详细信息
  rpc GetRuleSet(GetRuleSetReq) returns (GetRuleSetResp);
  // 获取所有符合规则的礼物配置
  rpc GetAllRulePresent(GetAllRulePresentReq) returns (GetAllRulePresentResp);
  // 检查礼物是否能够送出
  rpc CheckPresentLimit(CheckPresentLimitReq) returns (CheckPresentLimitResp);
  // 受限的背包礼物送出后加每日累计值
  rpc AddBackpackPresentLimit(AddBackpackPresentLimitReq) returns (AddBackpackPresentLimitResp);

  rpc AddForceInsertCfg (AddForceInsertCfgReq) returns (AddForceInsertCfgResp);
  rpc ModForceInsertCfg (ModForceInsertCfgReq) returns (ModForceInsertCfgResp);
  rpc DelForceInsertCfg (DelForceInsertCfgReq) returns (DelForceInsertCfgResp);
  rpc GetCurrentForceInsertCfg (GetCurrentForceInsertCfgReq) returns (GetCurrentForceInsertCfgResp);
  rpc GetForceInsertCfgList (GetForceInsertCfgListReq) returns (GetForceInsertCfgListResp);
}

// RuleSet 完整对象，对应数据库中的 rule_sets 表及关联表
message RuleSet {
  // 受限礼物范围
  PresentLimitScope present_limit_scope = 1;
  // 礼物场景限制
  PresentSceneLimit present_scene_limit = 2;
  // 送礼对象限制
  repeated PresentTargetLimit present_target_limit = 3;
  // 背包送礼金额上限
  BackpackPriceLimit backpack_price_limit = 4;
}

message PresentLimitScope{
  bool is_all_backpack_gifts = 1; // 是否限所有背包礼物
  bool is_all_intimate_gifts = 2; // 是否限所有亲密礼物
  repeated uint32 risk_control_business_id = 3; // 风控业务ID列表
}

message PresentTargetLimit {
  uint32 begin_price = 1; // 限制的起始金额
  uint32 end_price = 2; // 限制的结束金额, 0为无限
  LimitValueType limit_value_type = 3; // 开闭类型
  uint32 fellow_value_limit = 4; // 挚友值门槛，0为不限制
  uint32 fellow_lv_limit = 5; // 挚友等级门槛，0为不限制
  uint32 dat_limit = 6; // 在一起天数门槛，0为不限制
}

// 开闭类型
enum LimitValueType {
  LIMIT_VALUE_TYPE_UNSPECIFIED = 0; // 未指定
  LIMIT_VALUE_TYPE_LEFT_OPEN = 1; // 左开右闭
  LIMIT_VALUE_TYPE_RIGHT_OPEN = 2; // 左闭右开
}

message PresentSceneLimit{
  bool is_locked_channel = 1; // 是否锁房期间不可送
  bool is_not_pgc = 2; // 是否非PGC房间不可送
}

message BackpackPriceLimit {
  enum PriceLimitType {
    PRICE_LIMIT_TYPE_UNSPECIFIED = 0; // 未指定
    PRICE_LIMIT_TYPE_FELLOW_VALUE = 1; // 按挚友值限制
    PRICE_LIMIT_TYPE_FELLOW_LV = 2; // 按挚友等级限制
  }
  PriceLimitType price_limit_type = 1; // 限制类型
  repeated BackpackLimitScope limit_scopes = 2; // 限制范围列表
}

message BackpackLimitScope{
  uint32 begin_value = 1; // 限制的起始值
  uint32 end_value = 2; // 限制的结束值, 0为无限
  LimitValueType limit_value_type = 3; // 开闭类型
  uint32 limit_tbean_value = 4; // 限制的t豆价值，0为不限制
}


// --- RPC 请求/响应 消息体 ---

message GetRuleSetReq {
}

message GetRuleSetResp {
  RuleSet set_info = 1; // 返回规则集信息
}


message UpdateRuleSetReq {
  RuleSet set_info = 1; // 包含要更新的规则集信息
  ChangeScopeType change_scope_type = 2; // 更新范围类型
}

enum ChangeScopeType {
  CHANGE_SCOPE_TYPE_UNSPECIFIED = 0; // 未指定
  CHANGE_SCOPE_TYPE_PRESENT_LIMIT_SCOPE = 1; // 更新受限礼物范围
  CHANGE_SCOPE_TYPE_PRESENT_SCENE_LIMIT = 2; // 更新礼物场景限制
  CHANGE_SCOPE_TYPE_PRESENT_TARGET_LIMIT = 3; // 更新送礼对象限制
  CHANGE_SCOPE_TYPE_BACKPACK_PRICE_LIMIT = 4; // 更新背包送礼金额上限
}

message UpdateRuleSetResp {
  RuleSet set_info = 1; // 返回更新后的规则集信息
}

message GetAllRulePresentReq {
  bool is_all_intimate_gifts = 1; // 是否限所有亲密礼物
  repeated uint32 risk_control_business_id = 2; // 风控业务ID列表
}

message GetAllRulePresentResp {
  repeated SimplePresentConfig present_configs = 1; // 返回所有规则的礼物配置
}

message SimplePresentConfig {
  uint32 present_id = 1; // 礼物ID
  string present_name = 2; // 礼物名称
  uint32 price = 3; // 礼物价值
  string icon_url = 4; // 礼物图标URL
}

message CheckPresentLimitReq{
  uint32 send_uid = 1; // 送礼用户ID
  repeated uint32 receive_uid = 2; // 收礼用户ID
  uint32 present_id = 3; // 礼物ID
  uint32 channel_id = 4; // 房间ID
  uint32 count = 5; // 送礼数量
  uint32 business_type = 6; // 业务类型 0 普通 1亲密礼物
}

message CheckPresentLimitResp {
  bool is_allowed = 1; // 是否允许送出
  ErrContent reason = 2; // 不允许的原因
}

message ErrContent {
  ErrorCode sub_err_code = 1; // 业务定义的错误码，仅用于区分，暂无实际用途
  string popup_content = 2; // 富文本xml
  string popup_button_a_text = 3; // 按钮A的文案（一般为跳转按钮，文案为空则不显示该按钮）
  string popup_button_a_url = 4; // 按钮A的跳转url
  string popup_button_b_text = 5; // 按钮B的文案 （一般为退出/返回按钮）
}

enum ErrorCode {
  ERROR_CODE_UNSPECIFIED = 0; // 未指定错误
  ERROR_CODE_CHANNEL_LOCKED = 1; // 房间锁定期间不可送礼
  ERROR_CODE_PGC_ROOM = 2; // PGC房间不可送礼
  ERROR_CODE_FELLOW_VALUE_LIMIT = 3; // 挚友值限制
  ERROR_CODE_FELLOW_LV_LIMIT = 4; // 挚友等级限制
  ERROR_CODE_DAT_LIMIT = 5; // 在一起天数限制
  ERROR_CODE_BACKPACK_PRICE_LIMIT = 6; // 背包送礼金额限制
  ERROR_CODE_NOT_FELLOW = 7; // 不是挚友关系
  ERROR_CODE_CHANNEL_IM = 8; // 不可在im送出
  ERROR_CODE_CHANNEL_LOCKED_NO_PGC_ROOM = 9; // 房间锁定期间且非PGC房间不可送礼
  ERROR_CODE_CHANNEL_ALL_MIC = 10; // 房间全麦不可送礼
}

message AddBackpackPresentLimitReq {
  uint32 send_uid = 1; // 送礼用户ID
  repeated uint32 receive_uid = 2; // 收礼用户ID
  uint32 present_id = 3; // 礼物ID
  uint32 channel_id = 4; // 房间ID
  uint32 count = 5; // 送礼数量
  uint32 business_type = 6; // 业务类型 0 普通 1亲密礼物
}

message AddBackpackPresentLimitResp {
}

message AddForceInsertCfgReq{
  ForceInsertCfg cfg = 1;
  bool pre_check = 2; // 是否预检查请求
}
message AddForceInsertCfgResp{
}

message ModForceInsertCfgReq{
  ForceInsertCfg cfg = 1;
  bool pre_check = 2; // 是否预检查请求
}
message ModForceInsertCfgResp{
}

message DelForceInsertCfgReq{
  uint32 id = 1;
  string operator = 2; // 操作人
}
message DelForceInsertCfgResp{
}

message ForceInsertCfg{
  uint32 id = 1;
  string activity_name = 2; // 活动名称
  int64 start_ts = 3; // 强插开始时间戳，单位秒
  int64 end_ts = 4; // 强插结束时间戳，单位秒
  ChannelType channel_type = 5; // 房间类型
  repeated GiftReorderInfo gift_list = 6; // 强插的礼物列表
  string operator = 7; // 操作人
  int64 create_ts = 8; // 创建时间戳，单位秒
  int64 update_ts = 9; // 更新时间戳，单位秒
  TimeStatus time_status = 10;  // 时间状态
}

message GiftReorderInfo{
  string id = 1; // 物品id，需要自行从string转换为对应类型
  GiftReorderItemType type = 2; // 物品类型
  uint32 pos = 3; // 强插位置，仅强插礼物用到，第1位传1而不是0
}

enum GiftReorderItemType{
  GIFT_REORDER_ITEM_TYPE_UNSPECIFIED = 0;
  GIFT_REORDER_ITEM_TYPE_COMMON_GIFT = 1; // 普通礼物
  GIFT_REORDER_ITEM_TYPE_EMPEROR_SET = 2; // 帝王套
  GIFT_REORDER_ITEM_TYPE_LUCKY_GIFT = 3; // 幸运礼物
}

enum ChannelType{
  CHANNEL_TYPE_UNSPECIFIED = 0;
  CHANNEL_TYPE_UGC = 1;
  CHANNEL_TYPE_PGC = 2;
  CHANNEL_TYPE_ALL = 3;
}

enum TimeStatus{
  TIME_STATUS_UNSPECIFIED = 0;
  TIME_STATUS_EFFECT = 1; // 生效中
  TIME_STATUS_EXPIRED = 2; // 已过期
  TIME_STATUS_NOT_START = 3; // 未开始
}

message GetCurrentForceInsertCfgReq{
  ChannelType channel_type = 1;
}

message GetCurrentForceInsertCfgResp{
  repeated ForceInsertCfg cfg_list = 1;
}

message GetForceInsertCfgListReq{
  uint32 page = 1;
  uint32 page_size = 2;
  // 按强插时间搜索，若不选则为0
  int64 start_ts = 3; // 强插开始时间戳，单位秒
  int64 end_ts = 4; // 强插结束时间戳，单位秒
  TimeStatus time_status = 5; // 按状态搜索
}

message GetForceInsertCfgListResp{
  repeated ForceInsertCfg cfg_list = 1;
  uint32 total = 2;
}
