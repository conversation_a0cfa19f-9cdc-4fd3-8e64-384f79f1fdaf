syntax = "proto3";

package avatar_mng_api;

option go_package = "golang.52tt.com/protocol/services/avatar-mng-api";

service AvatarMngApi {
    // 获取头像文件
    rpc GetAvatar(GetAvatarReq) returns (GetAvatarResp) {}
    // 批量获取头像版本号
    rpc BatchGetAvatarVersion(BatchGetAvatarVersionReq) returns (BatchGetAvatarVersionResp) {}
    // 上传头像
    rpc UploadAvatar(UploadAvatarReq) returns (UploadAvatarResp) {}
    // 上传拼接头像
    rpc UploadJoinAvatar(UploadJoinAvatarReq) returns (UploadJoinAvatarResp) {}
    // 注册时上传头像
    rpc RegUploadAvatar(RegUploadAvatarReq) returns (RegUploadAvatarResp) {}
}

enum AvatarDataType {
    AVATAR_DATA_TYPE_UNSPECIFIED = 0;
    AVATAR_DATA_TYPE_BIG = 1;
    AVATAR_DATA__TYPE_SMALL = 2;
}

enum AvatarType {
    AVATAR_TYPE_UNSPECIFIED = 0;
    AVATAR_TYPE_STATIC = 1;
    AVATAR_TYPE_DYNAMIC = 2;
}

// 头像上传来源
enum UplaodSource {
    UPLOAD_SOURCE_DEFAULT = 0;
    // 命令号上传
    UPLOAD_SOURCE_CMD = 1;
    // HTTP接口上传
    UPLOAD_SOURCE_HTTP = 2;
    // 第三方qq/wx头像同步
    UPLOAD_SOURCE_THIRD_PARTY_REG = 3;
    // 主播开播头像送审
    UPLOAD_SOURCE_ANCHOR_LIVE_BEGIN = 4;
    // 沉默用户头像送审
    UPLOAD_SOURCE_SILENT_USER = 5;
    // 监管重置用户头像送审
    UPLOAD_SOURCE_SUPERVISE_RESET_USER = 6;
}

// 实体类型
enum EntityType {
    ENTITY_TYPE_UNSPECIFIED = 0;
    // 用户头像，id 填 username
    ENTITY_TYPE_USER = 1;
    // 工会头像，id 填guildid
    ENTITY_TYPE_GUILD = 2;
    // 群头像，id 填groupid
    ENTITY_TYPE_GROUP = 3;
    // 游戏头像，id 填gameid
    ENTITY_TYPE_GAME = 4;
    // 公会群头像，id 填groupid
    ENTITY_TYPE_GUILD_GROUP = 5;
    // 游戏群头像，id 填groupid
    ENTITY_TYPE_GAME_GROUP = 6;
    // 公众号头像，id 填publicid
    ENTITY_TYPE_PUBLIC_ACCOUNT = 7;
    // 临时群头像，id 填groupid
    ENTITY_TYPE_TGROUP = 8;
    // 房间头像，id 填channelid
    ENTITY_TYPE_CHANNEL = 9;
    // 社团群头像，id 填groupid
    ENTITY_TYPE_CGROUP = 10;
}

message Entity {
    EntityType type = 1;
    string id = 2;
}

message ContextInfo{
    uint32 op_uid = 1;
    uint32 market_id = 2;
    // 16进制的设备id
    bytes device_id_hex = 3;
    string client_ip = 4;
    uint32 terminal_type = 5;
}

message AvatarVersion {
    Entity entity = 1;
    string version = 2;
}

message BatchGetAvatarVersionReq {
    AvatarType avatar_type = 1;
    repeated Entity entity_list = 2;
}

message BatchGetAvatarVersionResp {
    repeated AvatarVersion avatar_version_list = 1;
}

// 获取头像文件
message GetAvatarReq {
    AvatarType avatar_type = 1; // 头像类型
    AvatarDataType avatar_data_type = 2; // 头像数据类型
    Entity entity = 3;
}

message GetAvatarResp {
    bytes image_data = 1;
    string version= 2;
}

// 上传头像
message UploadAvatarReq {
    Entity entity = 1; //更改头像的实体
    bytes image_data = 2; //头像文件
    bool is_need_audit = 3; // 头像是否需要审核
    string version_prefix =4; // 头像版本号前缀
    UplaodSource upload_source = 5; // 头像上传来源
    ContextInfo context_info = 6;
}

message UploadAvatarResp {
    string avatar_version = 1;
}

message RegUploadAvatarReq {
    string account = 1; //更改头像的帐号，如果是工会头像就是工会的account
    bytes image_data = 2; //头像文件
    uint32 uid = 3; //用户uid
    string sm_device_id = 4; //从Baserep拿 base_resp.antispam_info.sm_antispam.sm_device_id
    string source_account = 5; //Avatar_UploadAvatarResp.source_account
    uint32 source_id = 6; //Avatar_UploadAvatarResp.source_id
    string ip = 7; //Avatar_UploadAvatarResp.ip
}
message RegUploadAvatarResp {
    string version = 1;
}
// 拼接头像
message UploadJoinAvatarReq {
    repeated string member_account_list = 1;
    string version_prefix = 2;
    Entity entity = 3; // 上传头像的帐号
}
message UploadJoinAvatarResp {
    string avatar_version = 1;
}