syntax = "proto3";

package gangup_channel;

option go_package = "golang.52tt.com/protocol/services/gangup-channel";

service GangupChannel {
  //修改主题房字段
  rpc SetGangupChannelReleaseInfo(SetGangupChannelReleaseInfoReq) returns (SetGangupChannelReleaseInfoResp);
  //解散主题房
  rpc DismissGangupChannel(DismissGangupChannelReq) returns (DismissGangupChannelResp);
  //获取房间列表（兜底推荐）
  rpc GetGangupChannelList(GetGangupChannelListReq) returns (GetGangupChannelListResp);
  //获取开黑房信息
  rpc GetGangupChannelByIds(GetGangupChannelByIdsReq) returns (GetGangupChannelByIdsResp);
  //切换房间玩法
  rpc SwitchChannelTab(SwitchChannelTabReq) returns (SwitchChannelTabResp);
  //获取主题房数，以及示例用户
  rpc GetOnlineInfo(GetOnlineInfoReq) returns (GetOnlineInfoResp);
  // 获取指定房间类型人数
  rpc GetChannelRoomUserNumber(GetChannelRoomUserNumberReq) returns (GetChannelRoomUserNumberResp);
  // 创建临时房
  rpc AddTemporaryChannel(AddTemporaryChannelReq) returns (AddTemporaryChannelResp);
  //负反馈上报
  rpc NegativeFeedBack(NegativeFeedBackReq) returns (NegativeFeedBackResp);
  //清除开黑房间信息
  rpc CleanChannelInfo(CleanChannelInfoReq) returns (CleanChannelInfoResp);
  // 获取指定玩法类型房间id
  rpc GetChannelIdsByTabId(GetChannelIdsByTabIdReq) returns (GetChannelIdsByTabIdResp);

  //开黑列表自定义筛选器
  rpc GetGameHomePageDIYFilter(GetGameHomePageDIYFilterReq) returns (GetGameHomePageDIYFilterResp);
  rpc SetGameHomePageDIYFilter(SetGameHomePageDIYFilterReq) returns (SetGameHomePageDIYFilterResp);

  // 设置开黑房降噪模式
  rpc SetGangupChannelDenoiseMode(SetGangupChannelDenoiseModeReq) returns (SetGangupChannelDenoiseModeResp);

  // 获取开黑房扩展信息
  rpc GetGangupChannelExtraInfo(GetGangupChannelExtraInfoReq) returns (GetGangupChannelExtraInfoResp);

  // 获取开黑房间冷却时间信息（发布冷却时间，修改冷却时间）
  rpc GetGangupExtraHistory(GetGangupExtraHistoryReq) returns (GetGangupExtraHistoryResp);
  rpc SetGangupExtraHistory(SetGangupExtraHistoryReq) returns (SetGangupExtraHistoryResp);
  // 获取开黑房间发布次数
  rpc GetGangupPublishCountHistory(GetGangupPublishCountHistoryReq) returns(GetGangupPublishCountHistoryResp);
  rpc SetGangupPublishCountHistory(SetGangupPublishCountHistoryReq) returns(SetGangupPublishCountHistoryResp);

  // 根据入口获取常玩分类
  rpc GetDIYFilterByEntrance(GetDIYFilterByEntranceReq) returns(GetDIYFilterByEntranceResp);
  // 根据入口设置常玩分类
  rpc SetDIYFilterByEntrance(SetDIYFilterByEntranceReq) returns(SetDIYFilterByEntranceResp);

  // 根据玩法获取发布房间数
  rpc GetReleasingChannelCountByTabIds(GetReleasingChannelCountByTabIdsReq) returns(GetReleasingChannelCountByTabIdsResp);
  // 真实发布中房间数加随机数
  rpc GetReleasingChannelRandomCountByTabIds(GetReleasingChannelCountByTabIdsReq) returns(GetReleasingChannelCountByTabIdsResp);

  // 更新负反馈选项
  rpc UpdateNegativeFeedbackOption(UpdateNegativeFeedbackOptionReq) returns (UpdateNegativeFeedbackOptionResp);
  // 获取负反馈选项
  rpc GetNegativeFeedbackOption(GetNegativeFeedbackOptionReq) returns (GetNegativeFeedbackOptionResp);

  // 更新房间列表筛选条件，只记录一起开黑分类下玩法
  rpc UpdateChannelListFilterRecord(UpdateChannelListFilterRecordReq) returns (UpdateChannelListFilterRecordResp);

  // 获取房间列表筛选记录
  rpc GetChannelListFilterRecord(GetChannelListFilterRecordReq) returns (GetChannelListFilterRecordResp);

  // 获取房间麦位音量
  rpc GetChannelMicVolSet(GetChannelMicVolSetReq) returns (GetChannelMicVolSetResp);
  // 设置房间麦位音量
  rpc SetChannelMicVol(SetChannelMicVolReq) returns (SetChannelMicVolResp);

  //获取发布中的房间
  rpc ListPublishingChannelIds(ListPublishingChannelIdsReq) returns (ListPublishingChannelIdsResp);
}

message BlockOptionList {
  repeated BlockOption block_options = 1;
}

message BlockOption {
  uint32 block_id = 1;           //块id
  uint32 elem_id = 2;            //块里面的元素id
  string elem_val = 3;          // 用户填写的值
}

enum ChannelDisplayType {
  DISPLAY_AT_MAIN_PAGE = 0;           //大厅展示
  DISMISSED = 1;                      //不展示
  DISPLAY_AT_FIND_FRIEND = 2;         //找好友玩。跟随显示
  TEMPORARY = 3;                      //临时房
}

message GameLabel {
  string val = 1; // 标签val 需要传回给 推荐
  string display_name = 2; // 标签的外显 value
  uint32 type = 3; // 标签类型
}

message GangupChannelReleaseInfo {
  uint32 id = 1;
  uint32 tab_id = 2;
  int64 release_time = 3;
  repeated BlockOption block_options = 4;    //房间选择的开房标签信息
  repeated ChannelDisplayType display_type = 5;
  bool show_geo_info = 6;                     //是否显示地理信息
  uint32 market_id = 7; //马甲包id同ga_base
  uint32 terminal_type = 8; //客户端类型，同serviceInfo
  int64 last_dismiss_time = 9; //release_time对应的取消发布时间，发布时根据配置写入，真正取消发布时再更新
  int64 switch_time = 10; //切换至该玩法的时间
  uint32 pushlish_uid = 11; //发布者uid
  repeated GameLabel game_labels = 12; //发布者选择的推荐三级标签
  uint32 client_type = 13; // 客户端类型, 9-极速PC
  GameLocationItem game_location_item = 14; // 用户的地理位置
  int64 update_time = 15; // 原音乐接口玩法发布信息
  uint32 publish_times = 16; // 原音乐接口玩法发布信息
}

message GameLocationItem {
  string country = 1;
  string country_code = 2; // 国家代码
  string province =3;
  uint32 province_code = 4;
  string city = 5;
  uint32 city_code = 6;
  double latitude = 7;
  double longitude = 8;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SetGangupChannelReleaseInfoReq {
  GangupChannelReleaseInfo gangup_channel_release_info = 1;
  bool want_fresh = 2;                       //是否优先匹配萌新
  string release_ip = 3;                     //房间发布时候的IP
  string channel_name = 4;
  uint32 creator = 5;
  repeated uint32 all_selected_bids = 6;      //全选的blockid
  repeated uint32 un_select_block_id = 7; //用户没选得blockId
  repeated BlockOption un_select_block_options = 8; //没有选的信息
  uint32 ChannelPlayMode = 9; //房间玩法模式（1文字房，0语音房）见channel-play_.proto，UgcChannelPlayMode字段

}

message SetGangupChannelReleaseInfoResp {

}

message DismissGangupChannelReq {
  uint32 channel_id = 1;
  string source = 2;
  DismissType type = 3;
  uint32 tab_id = 4;
}

enum DismissType{
  Unknown = 0;
  Sever = 1;//服务器自动取消
  Cancel = 2;//用户点击取消发布
  SwitchTab = 3;//切换玩法导致取消
  Quit = 4;//退房导致取消

  Freeze = 5;//房间冻结导致取消发布
}

message DismissGangupChannelResp {
  bool dismiss = 1;
}

message GetRecommendChannelListLoadMore {
  uint32 num = 1;
}

message GetGangupChannelListReq {
  uint32 uid = 1;
  uint32 limit = 2;
  repeated uint32 tab_id_list = 3;
}

message GetGangupChannelListResp {
  repeated GangupChannelReleaseInfo channel_list = 1;
}

message GetGangupChannelByIdsReq {
  repeated uint32 ids = 1;
  repeated ChannelDisplayType types = 2;
  bool return_all = 3;
}

message GetGangupChannelByIdsResp {
  repeated GangupChannelReleaseInfo info = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SwitchChannelTabReq{
  uint32 uid = 1;
  uint32 tab_id = 2;
  uint32 channel_id = 3;
  uint32 appId = 4;
  uint32 marketId = 5;
  int64 switch_time = 6; //切换玩法时间
}
message SwitchChannelTabResp{
  string tab_name = 1;
  repeated string welcome_txt_list = 2;
  uint32 mic_mod = 3;
  uint32 tab_type = 4;
  uint32 tag_id = 5;
}

message DisappearChannelReq {
  string client_id = 1;
  uint64 acquire_duration = 2;        //超时此时间段可重入

  message Timeout {
    uint64 timeout_duration = 1;    //创建超出该时间段清除
  }

  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  message Keepalive {
    uint64 Keepalive_duration = 1; //不保持心跳超出该时间段
    uint32 member_count = 2;        //房间人数大于此数
  }

  message ReleaseTimeout {
    uint64 timeout_duration = 1;    //创建超出该时间段清除
  }

  Timeout timeout_event = 10;
  Keepalive keepalive_event = 11;
  ReleaseTimeout release_timeout_event = 12; //发布超出时间段 v5.5.0
}

message DisappearChannelResp {
  repeated uint32 channel_ids = 1;
}

message GetOnlineInfoReq {
  uint32 online_user_count = 1;
}

message GetOnlineInfoResp {
  uint32 room_count = 1;
  repeated uint32 online_user_list = 2;
}

message FreezeChannelReq {
  repeated uint32 channel_id_list = 1;
  int64 freeze_time = 2;                   //冻结多久，单位是秒，永久冻结传-1
}

message FreezeChannelResp {

}

message UnfreezeChannelReq {
  repeated uint32 channel_id_list = 1;

}

message UnfreezeChannelResp {

}

message GetChannelFreezeInfoReq {
  uint32 channel_id = 1;
}

message GetChannelFreezeInfoResp {
  int64 freeze_time = 1;
}

enum HistoryType {
  CreateHistory = 0;
  UpdateHistory = 1;
}

message SetExtraHistoryReq {
  uint32 channel_id = 1;
  HistoryType history_type = 2;
  int64 expire_after = 3;     //秒为单位，小于等于0即不过期
}

message SetExtraHistoryResp {

}

message GetExtraHistoryReq {
  uint32 channel_id = 1;
  HistoryType history_type = 2;
}

message GetExtraHistoryResp {
  string value = 1;
}

message GetChannelPlayModelReq {
  uint32 channel_id = 1;
}

message GetChannelPlayModelResp {
  uint32 tab_id = 1; // 玩法id
}

message GetChannelRoomUserNumberReq {
  repeated uint32 tab_id = 1; // 如果不指定，则返回全部
}

message GetChannelRoomUserNumberResp {
  message RoomUserInfo {
    uint32 tab_id = 1;
    int64 total_user_number = 2;
  }
  repeated RoomUserInfo room_user_info = 1;
}

message AddTemporaryChannelReq {
  GangupChannelReleaseInfo channel = 1; // 复用channel
}

message AddTemporaryChannelResp {}

//负反馈上报
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message NegativeFeedBackReq{
  uint32 channel_id = 1;
  uint32 tab_id = 2;
  uint32 creator = 3;
  repeated BlockOption block_options = 4;    //房间选择的开房标签信息
  string name = 5;
  repeated NegativeFeedbackType negative_Feedback_type = 6;
  uint32 reporter_uid = 7; //上报者uid
  repeated string channel_name_keywords = 8; // 不感兴趣的房间名关键词
  repeated string channel_owner_reasons = 9; // 不想看到房主的原因

  uint32 black_channel_user = 10;  // 反馈进房用户
  bool black_channel_user_enable_filter = 11; // 是否开启过滤被反馈的用户
  repeated string reasons_of_black_channel_user = 12; // 反馈进房用户原因
}
//负反馈上报
enum NegativeFeedbackType {
  FeedbackTypeInvalid = 0; // 无效值
  FeedbackTypeChannelOwner = 1; // 房主
  FeedbackTypeChannelTab = 2; // 游戏类型
  FeedbackTypePublishCond = 3; // 发布条件
  FeedbackTypeChannelName = 4; // 房间名称

  // 房间内反馈用户
  FeedbackTypeOwnerInChannel = 5; // 房主反馈进房用户
  FeedbackTypeUserInChannel = 6; // 房客反馈其它进房用户
  FeedbackTypeQuitUninterestedInChannel = 7; // 退房不感兴趣
}
message NegativeFeedBackResp{

}

message CleanChannelInfoReq{
  uint32 channel_id = 1;
}

message CleanChannelInfoResp{
}

message GetChannelIdsByTabIdReq{
  uint32 tab_id = 1;
  enum GetMode{
    ALL = 0;  //所有
    ONLY_RELEASE = 1; //仅要发布中
    ONLY_SINK = 2;   //要未发布中的
  }
  GetMode get_mode = 2;
}
message GetChannelIdsByTabIdResp{
  repeated uint32 channel_ids = 1;
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum REGULATORY_LEVEL {
  FREE = 0; //不限制
  SIMPLE_MINOR = 1; // 未成年
}

enum EntryType {
  GameHomePageEntry = 0; //开黑匹配
  MysteryHomePageEntry = 1; //迷境首页
  PCHomePageEntry = 2; //PC端入口
}

message GetGameHomePageDIYFilterReq{
  uint32 uid = 1;
  REGULATORY_LEVEL regulatory_level = 2;
  EntryType entry_type = 3;
}

message GetGameHomePageDIYFilterResp{
  repeated GameHomePageFilterItem items = 1;
}

message GameHomePageFilterItem{
  uint32 tab_id = 1;
  uint32 category_id = 2;
  string title = 3;
}

message SetGameHomePageDIYFilterReq{
  uint32 uid = 1;
  repeated GameHomePageFilterItem items = 2;
  EntryType entry_type = 3;
}

message SetGameHomePageDIYFilterResp{
}

message SetGangupChannelDenoiseModeReq {
  uint32 channel_id = 1;
  uint32 mode = 2;
}

message SetGangupChannelDenoiseModeResp {
}

// 开黑房扩展信息
message GangupChannelExtraInfo {
  // 降噪模式
  uint32 denoise_mode = 1;
}

message GetGangupChannelExtraInfoReq {
  uint32 channel_id = 1;
}

message GetGangupChannelExtraInfoResp {
  GangupChannelExtraInfo info = 1;
}

//获取开黑房间冷却时间信息（发布冷却时间，修改冷却时间）
message GetGangupExtraHistoryReq{
  uint32 cid = 1;
  bool is_release = 2; //true 发布冷却时间, false 修改冷却时间
}

message GetGangupExtraHistoryResp{
  string value = 1;
}

message SetGangupExtraHistoryReq{
  uint32 cid = 1;
  string value = 2;
  int64 expire_after = 3;
  bool is_release = 4;
}

message SetGangupExtraHistoryResp{

}

//获取开黑房间发布次数
message GetGangupPublishCountHistoryReq{
  uint32 cid = 1;
}

message GetGangupPublishCountHistoryResp{
  uint32 publish_count = 1;
}

message SetGangupPublishCountHistoryReq{
  uint32 cid = 1;
}
message SetGangupPublishCountHistoryResp{

}

//设置根据入口设置常玩分类
message SetDIYFilterByEntranceReq{
  uint32 uid = 1;
  repeated DIYFilterItem items = 2;
  uint32 entry_type = 3; //1 一级首页， 2 游戏专区， 3休闲互动专区
  bool is_incr = 4; // 是否增量添加
}

//标识业务
message DIYFilterItem{
  uint32 game_business_id = 1; //tabId or categoryId
  string music_business_id = 2; //music_itemId
  uint32 filter_item_type = 3; //1 tabId， 2 categoryId， 3 musicId
}

message SetDIYFilterByEntranceResp{
}

//根据入口获取常玩分类
message GetDIYFilterByEntranceReq{
  uint32 uid = 1;
  uint32 entry_type = 2; //1 一级首页， 2 游戏专区， 3休闲互动专区
}

message GetDIYFilterByEntranceResp{
  repeated DIYFilterItem items = 1;
}

//根据玩法获取发布房间数
message GetReleasingChannelCountByTabIdsReq{
  repeated uint32 tab_ids = 1;
}

message GetReleasingChannelCountByTabIdsResp{
  map<uint32, int64> count_map = 1;
}

// 房间内反馈
message ChannelFeedback {
  // 反馈原因
  repeated string reasons = 1;
  // 屏蔽文案
  string block_text = 2;
}

message NegativeFeedback {
  // 列表房主反馈
  repeated string list_owner_feedback = 1;

  // 房间内部反馈原因配置(房主侧)
  ChannelFeedback owner_channel_feedback = 2;
  // 房间内部反馈原因配置(房客侧)
  ChannelFeedback guest_channel_feedback = 3;
  // 房间内退房反馈配置
  ChannelFeedback quit_channel_feedback = 4;
}

message UpdateNegativeFeedbackOptionReq {
  NegativeFeedback feedback = 1;
}

message UpdateNegativeFeedbackOptionResp {
}

message GetNegativeFeedbackOptionReq {
}

message GetNegativeFeedbackOptionResp {
  NegativeFeedback feedback = 1;
}

message FilterRecord {
  repeated string elem_title = 1; // 筛选项文案
}

// 更新房间列表筛选条件，只记录一起开黑分类下玩法
message UpdateChannelListFilterRecordReq {
  uint32 uid = 1; // 用户id
  uint32 tab_id = 2; // 玩法id
  message LabelRecord {
    string display_title = 1; // 三级标签外显文案
    string related_label_val = 2; // 三级标签映射值
  }
  repeated LabelRecord label_record = 3; // 用户筛选记录
}

message UpdateChannelListFilterRecordResp {
}



message GetChannelListFilterRecordReq {
  repeated uint32 uid_list = 1; // 用户id
  uint32 tab_id = 2; // 玩法id
}

message GetChannelListFilterRecordResp {

  map<uint32, FilterRecord> record_map = 1; // key uid value 筛选记录
}

// 拉取麦位音量设置接口
message GetChannelMicVolSetReq {
  uint32 cid = 1; // 房间id
}

message ChannelMicVolSetItem {
  uint32 mic_id = 1;
  uint32 max_vol = 2; // 最大音量
}

message GetChannelMicVolSetResp {
  repeated ChannelMicVolSetItem mic_vol_set = 1; // 麦位音量设置
  bool sync_zero_mic_set = 2; // 是否同步0号麦位设置
}

message SetChannelMicVolReq {
  uint32 cid = 1; // 房间id
  uint32 uid = 2;
  repeated ChannelMicVolSetItem mic_vol_set = 3; // 麦位音量设置
  bool sync_zero_mic_set = 4; // 是否同步0号麦位设置
}

message SetChannelMicVolResp {
}

message ListPublishingChannelIdsReq {
  uint32 tab_id = 1;
  uint32 limit = 2;
  int64 time_offset = 3;//秒
}

message ListPublishingChannelIdsResp {
  repeated channel channels = 1;
  // buf:lint:ignore MESSAGE_PASCAL_CASE
  message channel {
    uint32 id = 1;
    int64 score = 2;
  }
}