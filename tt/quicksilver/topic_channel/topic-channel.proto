syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package topic_channel.channel;

option go_package = "golang.52tt.com/protocol/services/topic_channel/channel";

service Channel {
  //创建主题房
  rpc AddChannel(AddChannelReq) returns (AddChannelResp);
  //修改主题房字段
  rpc UpdateChannelInfo(UpdateChannelInfoReq) returns (UpdateChannelInfoResp);
  //解散主题房
  rpc DismissChannel(DismissChannelReq) returns (DismissChannelResp);
  //不限分类获取列表
  rpc GetRecommendChannelList(GetRecommendChannelListReq) returns (GetRecommendChannelListResp);
  //指定分类获取列表
  rpc GetRecommendChannelListByTab(GetRecommendChannelListByTabReq) returns (GetRecommendChannelListByTabResp);

  //修改主题房字段
  rpc SetChannelReleaseInfo(SetChannelReleaseInfoReq) returns (SetChannelReleaseInfoResp);
  //获取主题房信息
  rpc GetChannelByIds(GetChannelByIdsReq) returns (GetChannelByIdsResp);
  //解散某个分类
  rpc DismissTab(DismissTabReq) returns (DismissTabResp);

  //房间保持心跳
  rpc KeepChannelAlive(KeepChannelAliveReq) returns (KeepChannelAliveResp);

  //清除房间展示在tab
  rpc DisappearChannel(DisappearChannelReq) returns (DisappearChannelResp);

  //设置对应tab下对应的获取房间配置
  rpc SetTabConfigure(SetTabConfigureReq) returns (SetTabConfigureResp);
  //获取主题房数，以及示例用户
  rpc GetOnlineInfo(GetOnlineInfoReq) returns (GetOnlineInfoResp);

  //冻结主题房
  rpc FreezeChannel(FreezeChannelReq) returns (FreezeChannelResp);
  //解除冻结主题房
  rpc UnfreezeChannel(UnfreezeChannelReq) returns (UnfreezeChannelResp);
  rpc GetChannelFreezeInfo(GetChannelFreezeInfoReq) returns (GetChannelFreezeInfoResp);
  // 获取房间玩法和模式列
  rpc GetChannelPlayModel(GetChannelPlayModelReq) returns (GetChannelPlayModelResp);

  // 切换玩法上报tab事件
  rpc SwitchChannelTabMq(SwitchChannelTabMqReq) returns (SwitchChannelTabMqResp);

  // 获取指定房间人数
  rpc GetChannelRoomUserNumber(GetChannelRoomUserNumberReq) returns (GetChannelRoomUserNumberResp);

  // 创建临时房
  rpc AddTemporaryChannel(AddTemporaryChannelReq) returns (AddTemporaryChannelResp);

  //历史记录保存
  rpc SetExtraHistory(SetExtraHistoryReq) returns (SetExtraHistoryResp);
  rpc GetExtraHistory(GetExtraHistoryReq) returns (GetExtraHistoryResp);

  //切换玩法
  rpc SwitchChannelTab(SwitchChannelTabReq) returns (SwitchChannelTabResp);

  rpc SwitchChannelTabAndBC(SwitchChannelTabReq) returns (SwitchChannelTabResp);

  //  //更新房间跟随状态
  //  rpc UpdateChannelLabelInfo(UpdateChannelLabelInfoReq) returns (UpdateChannelLabelInfoResp);
  //
  //  //批量获取房间跟随状态
  //  rpc BatGetChannelLabelInfo(BatGetChannelLabelInfoReq) returns (BatGetChannelLabelInfoResp);

  //添加房间玩法数据，过渡用
  rpc UpdateTopicChannelInfo(UpdateTopicChannelInfoReq) returns (UpdateTopicChannelInfoResp);

  //更新用户进房时间记录
  rpc UpdateLastEnterRoomTimeByUid(UpdateLastEnterRoomTimeByUidReq) returns (UpdateLastEnterRoomTimeByUidResp);
  //获取用户进房时间记录
  rpc GetLastEnterRoomTimeByUid(GetLastEnterRoomTimeByUidReq) returns (GetLastEnterRoomTimeByUidResp);
  //更新用户进房tab记录
  rpc UpdateLastEnterRoomTabIdByUid(UpdateLastEnterRoomTabIdByUidReq) returns (UpdateLastEnterRoomTabIdByUidResp);
  //获取用户进房tab记录
  rpc GetLastEnterRoomTabIdByUid(GetLastEnterRoomTabIdByUidReq) returns (GetLastEnterRoomTabIdByUidResp);

  //获取发布中的房间
  rpc ListPublishingChannelIds(ListPublishingChannelIdsReq) returns (ListPublishingChannelIdsResp);
}

message BlockOptionList {
  repeated BlockOption block_options = 1;
}

message BlockOption {
  uint32 block_id = 1;           //块id
  uint32 elem_id = 2;            //块里面的元素id
  string elem_val = 3;          // 用户填写的值
}

enum ChannelDisplayType {
  DISPLAY_AT_MAIN_PAGE = 0;           //大厅展示
  DISMISSED = 1;                      //不展示
  DISPLAY_AT_FIND_FRIEND = 2;         //找好友玩。跟随显示
  TEMPORARY = 3;                      //临时房
}

message ChannelInfo {
  uint32 id = 1;
  uint32 tab_id = 2;
  int64 create_time = 3;
  uint32 male_count = 4;                      //deprecated 男性人数
  uint32 female_count = 5;                    //deprecated 女性人数
  bool is_recommend_channel = 6;  //deprecated
  uint32 creator = 7;                         //deprecated 创建者用户id
  uint32 total_count = 8;                     //deprecated 房间里总人数
  string scan_param = 9;                      //deprecated 用于scan搜索的信息
  repeated BlockOption block_options = 10;    //房间选择的开房标签信息
  bool is_private = 11;                       //是否私密
  uint32 on_mic_count = 12;                   //deprecated 麦上人数
  string name = 13; //deprecated
  repeated ChannelDisplayType display_type = 14;
  bool want_fresh = 15;                       //是否优先匹配萌新
  string release_ip = 16;                     //房间发布时候的IP
  bool show_geo_info = 17;                     //是否显示地理信息
  uint32 market_id = 18; //马甲包id，只有开黑游戏房间才有
  uint32 terminal_type = 19; //客户端类型，只有开黑游戏房间才有
  int64 last_dismiss_time = 20; //上次取消发布时间，只有开黑游戏房间才有
  int64 switch_time = 21; //切换玩法时间，只有开黑游戏房间才有
  uint32 client_type = 22; // 客户端类型, 9-极速PC
}
message AddChannelReq {
  ChannelInfo channel = 1;
  bool is_change = 2;
  HomePageType home_page_type = 3;
  repeated uint32 all_selected_bids = 4;      //全选的blockid
}

//显示首页类别，临时解决方案，之后迁移到hobby channel中区分
enum HomePageType {
  Default = 0;
  Game = 1;
  Music = 2;
}

message AddChannelResp {

}

message UpdateChannelInfoReq {
  uint32 id = 1;
  uint32 tab_id = 2;
  uint32 male_count = 4;
  uint32 female_count = 5;
  uint32 total_count = 6;
  uint32 on_mic_count = 7;
  uint32 creator = 8;         //仅channel info不存在时会更新
}

message UpdateChannelInfoResp {

}

message DismissChannelReq {
  uint32 channel_id = 1;
  DismissType type = 2;
  string source = 3;
  uint32 tab_id = 4;
}

enum DismissType{
  Unknown = 0;
  Sever = 1;//服务器自动取消
  Cancel = 2;//用户点击取消发布
  SwitchTab = 3;//切换玩法导致取消
  Quit = 4;//退房导致取消
}

message DismissChannelResp {
  bool dismiss = 1;
}

message GetRecommendChannelListLoadMore {
  uint32 num = 1;
}

message GetRecommendChannelListReq {
  uint32 uid = 1;
  uint32 limit = 2;
  GetRecommendChannelListLoadMore load_more = 3;
  repeated uint32 tab_id_list = 4;
  bool not_clear_history = 5;
  bool not_check_history = 6;
  bool not_save_history = 7;
  uint32 return_pgc = 8;
}

message GetRecommendChannelListResp {
  repeated ChannelInfo channel_list = 1;
  GetRecommendChannelListLoadMore load_more = 2;
}

message GetListByTabLoadMore {
  GetListByTabLoadMoreItem newborn = 1;
  GetListByTabLoadMoreItem sink = 2;
  GetListByTabLoadMoreItem big = 3;
}

message GetListByTabLoadMoreItem {
  uint64 cursor = 1;
  uint32 last_value = 2;
  uint32 last_index = 3;
  int64  last_count = 4;
  bool   the_end = 5;
  string last_match = 6;
}


message GetRecommendChannelListByTabReq {
  uint32 uid = 1;
  uint32 limit = 2;
  uint32 tab_id = 3;
  GetListByTabLoadMore load_more = 4;
  repeated uint32 except_channel_id = 5;
  string scan_param = 6;
  repeated BlockOption block_options = 7;    //房间选择的开房标签信息
  bool not_clear_history = 8;
  bool not_check_history = 9;
  bool not_save_history = 10;
}

message GetRecommendChannelListByTabResp {
  repeated ChannelInfo channel_list = 1;
  GetListByTabLoadMore load_more = 2;
}

message SetChannelReleaseInfoReq {
  ChannelReleaseInfo channel_release_info = 1;
  bool want_fresh = 2;                       //是否优先匹配萌新
  string release_ip = 3;                     //房间发布时候的IP
  string channel_name = 4;
  uint32 creator = 5;
  repeated uint32 all_selected_bids = 6;      //全选的blockid
  repeated uint32 un_select_block_id = 7; //用户没选得blockId
  repeated BlockOption un_select_block_options = 8; //没有选的信息
  uint32 channel_play_mode = 9; //房间玩法模式（1文字房，0语音房）见channel-play_.proto，UgcChannelPlayMode字段
}

message ChannelReleaseInfo {
  uint32 id = 1;
  uint32 tab_id = 2;
  int64 release_time = 3;
  repeated BlockOption block_options = 4;    //房间选择的开房标签信息
  repeated ChannelDisplayType display_type = 5;
  bool show_geo_info = 6;                     //是否显示地理信息
  uint32 market_id = 7; //马甲包id同ga_base
  uint32 terminal_type = 8; //客户端类型，同serviceInfo
  int64 last_dismiss_time = 9; //release_time对应的取消发布时间，发布时根据配置写入，真正取消发布时再更新
  int64 switch_time = 10; //切换至该玩法的时间
  uint32 pushlish_uid = 11; //发布者uid
  repeated GameLabel game_labels = 12; //发布者选择的推荐三级标签
  uint32 client_type = 13; // 客户端类型, 9-极速PC
  GameLocationItem game_location_item = 14; // 用户的地理位置
  int64 update_time = 15; // 原音乐接口玩法发布信息
  uint32 publish_times = 16; // 原音乐接口玩法发布信息
}

message GameLabel {
  string val = 1; // 标签val 需要传回给 推荐
  string display_name = 2; // 标签的外显 value
  uint32 type = 3; // 标签类型
}

message GameLocationItem {
  string country = 1;
  string country_code = 2; // 国家代码
  string province =3;
  uint32 province_code = 4;
  string city = 5;
  uint32 city_code = 6;
  double latitude = 7;
  double longitude = 8;
}

message SetChannelReleaseInfoResp {
}

message GetChannelByIdsReq {
  repeated uint32 ids = 1;
  repeated ChannelDisplayType types = 2;
  bool return_all = 3;
  string source = 4; //流量标识，可填写请求来源，方便查日志
}

message GetChannelByIdsResp {
  repeated ChannelInfo info = 1[deprecated=true];
  repeated ChannelReleaseInfo release_info = 2; // 发布信息字段，跟设置的一样
}

message DismissTabReq {
  uint32 tab_id = 1;
}

message DismissTabResp {

}

enum KeepAliveStatus {
  ALIVE = 0;
  DISCONNECTED = 1;
}

message KeepChannelAliveReq {
  uint32 channel_id = 1;
  KeepAliveStatus status = 2;
}

message KeepChannelAliveResp {
  bool is_alive = 1;
}

//message AcquireLockReq {
//    string type = 1;
//    string client_id = 2;
//    uint64 duration = 3;
//}
//
//message AcquireLockResp {
//    bool success = 1;
//}

message DisappearChannelReq {
  string client_id = 1;
  uint64 acquire_duration = 2;        //超时此时间段可重入

  message Timeout {
    uint64 timeout_duration = 1;    //创建超出该时间段清除
  }

  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  message Keepalive {
    uint64 Keepalive_duration = 1; //不保持心跳超出该时间段
    uint32 member_count = 2;        //房间人数大于此数
  }

  message ReleaseTimeout {
    uint64 timeout_duration = 1;    //创建超出该时间段清除
  }

  Timeout timeout_event = 10;
  Keepalive keepalive_event = 11;
  ReleaseTimeout release_timeout_event = 12; //发布超出时间段 v5.5.0
}

message DisappearChannelResp {
  repeated uint32 channel_ids = 1;
}

message TabConfigure {
  uint32 tab_id = 1;
  repeated string scan_rule = 2;
  uint64 sink_duration = 3;
  uint64 disappear_duration = 4;
  uint32 big_pool_boundary_value = 5;
  uint32 rand_set_boundary_value = 6;

  int64 modify_at = 7;
}

message SetTabConfigureReq {
  TabConfigure config = 1;
}

message SetTabConfigureResp {

}

message GetOnlineInfoReq {
  uint32 online_user_count = 1;
}

message GetOnlineInfoResp {
  uint32 room_count = 1;
  repeated uint32 online_user_list = 2;
}

message FreezeChannelReq {
  repeated uint32 channel_id_list = 1;
  int64 freeze_time = 2;                   //冻结多久，单位是秒，永久冻结传-1
}

message FreezeChannelResp {

}

message UnfreezeChannelReq {
  repeated uint32 channel_id_list = 1;

}

message UnfreezeChannelResp {

}

message GetChannelFreezeInfoReq {
  uint32 channel_id = 1;
}

message GetChannelFreezeInfoResp {
  int64 freeze_time = 1;
}

message SetExtraHistoryReq {
  string key = 1;
  string value = 2;
  int64 expire_after = 3;     //秒为单位，小于等于即不过期
}

message SetExtraHistoryResp {

}

message GetExtraHistoryReq {
  string key = 1;
}

message GetExtraHistoryResp {
  string value = 1;
}

message GetChannelPlayModelReq {
  uint32 channel_id = 1;
}

message GetChannelPlayModelResp {
  uint32 tab_id = 1; // 玩法id
}

message SwitchChannelTabMqReq {
  uint32 tab_id = 1;
  uint32 channel_id = 2;
  string tab_name = 3;
  Source source = 4;
  HomePageType home_page_type = 5;
  uint32 uid = 6; //非必传
  uint32 old_tab_id = 7; //切换玩法前玩法id

}
message SwitchChannelTabMqResp {

}

enum Source {
  INVAIAL = 0;
  CREATE = 1;
  SWITCH = 2;
  PUBLISH = 3;
}

message GetChannelRoomUserNumberReq {
  repeated uint32 tab_id = 1; // 如果不指定，则返回全部
}

message GetChannelRoomUserNumberResp {
  message RoomUserInfo {
    uint32 tab_id = 1;
    int64 total_user_number = 2;
  }
  repeated RoomUserInfo room_user_info = 1;
}

message AddTemporaryChannelReq {
  ChannelInfo channel = 1; // 复用channel
}

message AddTemporaryChannelResp {}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SwitchChannelTabReq {
  uint32 uid = 1;
  uint32 creator = 2;   //房主
  uint32 tab_id = 3;
  uint32 channel_id = 4;
  Source source = 5;
  uint32 appId = 6;
  uint32 marketId = 7;
  HomePageType home_page_type = 8;
}

message SwitchChannelTabResp {
  string tab_name = 1;
  repeated string welcome_txt_list = 2;
  uint32 mic_mod = 3;
  uint32 tab_type = 4;
  uint32 tag_id = 5;
}

////更新房间标签信息（跟随标签），is_del为true删除，false增加
////暂时只有跟随标签，所以直接全量覆盖，后续有新增要改
//message UpdateChannelLabelInfoReq {
//  uint32 uid = 1;
//  uint32 channel_id = 2;
//  bool is_del = 3;
//  repeated LabelType labelInfos = 4;
//}
//
//message UpdateChannelLabelInfoResp {
//}
//
////获取房间标签信息（跟随标签）
//message BatGetChannelLabelInfoReq {
//  repeated uint32 channel_ids = 1;
//}
//
//message BatGetChannelLabelInfoResp {
//  repeated ChannelLabelInfo channel_label_infos = 1;
//}
//enum LabelType{
//  INVALID = 0; //无效值
//  Channel_Follow_Status = 1; //房间跟随标签
//}
//message ChannelLabelInfo {
//  uint32 channel_id = 1;
//  repeated LabelType label_infos = 2; //标签集合
//}

message UpdateTopicChannelInfoReq{
  uint32 channel_id = 1;
  uint32 tab_id = 2;

}

message UpdateTopicChannelInfoResp{

}

//更新用户上次进房的时间
message UpdateLastEnterRoomTimeByUidReq{
  uint32 uid = 1;
  int64 enter_room_time = 2;
}

message UpdateLastEnterRoomTimeByUidResp{

}

//获取用户上次进房的时间
message GetLastEnterRoomTimeByUidReq{
  uint32 uid = 1;
}

message GetLastEnterRoomTimeByUidResp{
  int64 enter_room_time = 1;
}

//更新用户上次进房大于10分钟的玩法id
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message UpdateLastEnterRoomTabIdByUidReq{
  uint32 uid = 1;
  uint32 tab_id = 2;
  int64 quitRoomTime = 3;
}

message UpdateLastEnterRoomTabIdByUidResp{

}

//获取用户上次进房大于10分钟的玩法id
message GetLastEnterRoomTabIdByUidReq{
  uint32 uid = 1;
}

message GetLastEnterRoomTabIdByUidResp{
  uint32 tab_id = 1;
}

message ListPublishingChannelIdsReq {
  uint32 tab_id = 1;
  uint32 limit = 2;
  int64 time_offset = 3;//秒
}

message ListPublishingChannelIdsResp {
  repeated Channel channels = 1;
  message Channel {
    uint32 id = 1;
    int64 score = 2;
  }
}