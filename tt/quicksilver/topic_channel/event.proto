syntax="proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package topic_channel.event;

option go_package = "golang.52tt.com/protocol/services/topic_channel/event";



//  option java_package = "com.tt.protocol.services.rcmd.topic_channel";
option java_outer_classname = "EventProto";// 上报数据

import "google/protobuf/any.proto";


enum Sex{
    All = 0;
    Male = 1;
    Female = 2;
}

message TopicChannelEvent {
    uint32 channel_id = 1;
    uint32 tab_id = 2;
    uint32 creator = 3;
    enum ACTION {
        INVALID = 0;    //占位，没用的
        CREATE = 1;     //发布主题房间
        DISMISS = 2;    //解散主题房间
    }
    repeated BlockOption block_options = 4 [deprecated = true];    //房间选择的开房标签信息，使用selected_block_options
    bool is_private = 5;                       //true表示私密房间   开始规划不传
    ACTION action = 6;
    string name = 7;
    repeated ChannelDisplayType display_type = 8;
    bool want_fresh = 9;                       //true表示优先匹配萌新
    Sex sex = 10;                               //性别  开始规划不传
    bool is_change = 11;                         //是否修改
    uint32 release_time = 12;                   //发布时间，推荐侧自用，业务暂时不填
    string release_ip = 13;                     //发布时用户设备IP
    bool show_geo_info = 14;                     //是否显示地理信息

    //15用过了，不能用了
    repeated uint32 all_selected_bids = 16 [deprecated = true];      //全选的blockId
    repeated  uint32 user_un_selected_bids = 17 [deprecated = true]; //用户没选的blockId
    uint32 channel_play_mode = 18; //房间玩法模式，0语音房，1文字房，见channel-play_.proto,UgcChannelPlayMode
    uint32 category_type = 19; //1为一起开黑分类，对应topic_channel_.proto CategoryType
    repeated GameLabel game_labels = 20; //发布者选择的推荐三级标签
    uint32 client_type = 21; // 客户端类型, 9-极速PC
    repeated BlockOption all_unselected_block_options = 22;    // 房间发布全部未选择的那些block下的所有elem信息
    repeated BlockOption selected_block_options = 23;    // 用户发布时选中的block elem信息
    GameLocationItem game_location_item = 24; // 用户的地理位置
}

message GameLocationItem{
    string country = 1;
    string country_code = 2; // 国家代码
    string province =3;
    uint32 province_code = 4;
    string city = 5;
    uint32 city_code = 6;
    double latitude = 7;
    double longitude = 8;
}

enum GameLabelType {
    Default = 0; // 搜索结果的标签
    HotLabel = 1; // 热门标签（支持标题玩法标签），val由多个标签值组成，用英文逗号分割
    LabelOfPublish = 2; // 发布条件标签
    LabelOfTabName = 3; // 游戏类型标签
    LabelOfGlobal = 4; // 通用类型标签
}

// 外显label
message GameLabel {
    string val = 1; // 标签val 需要传回给 推荐
    string display_name = 2; // 标签的外显 value
    GameLabelType type = 3; // 标签类型
}

message BlockOption {
    uint32 block_id = 1;           //块id
    uint32 elem_id = 2;            //块里面的元素id
    string val = 3; // 用户输入的值
}

enum ChannelDisplayType {
    DISPLAY_AT_MAIN_PAGE = 0;           //大厅展示
    DISPLAY_AT_FIND_FRIEND = 2;         //找好友玩。跟随显示
    TEMPORARY = 3;                      //临时房
}


//切换房间玩法
message SwitchChannelTabEvent {
    uint32 tab_id = 1;
    string tab_name = 2;
    uint32 channel_id = 3;
    Source source = 4;
    int64  update_time = 5;
    uint32 uid = 6; //非必传，调用切换玩法用户的uid,可能是房主uid，也可能是房管uid
    uint32 old_tab_id = 7; //切换玩法前玩法id

}

enum Source {
   INVAIAL = 0;
   CREATE = 1;
   SWITCH = 2;
}

//负反馈上报
enum NegativeFeedbackType {
    FeedbackTypeInvalid = 0; // 无效值
    // 反馈房间
    FeedbackTypeChannelOwner = 1; // 房主
    FeedbackTypeChannelTab = 2; // 游戏类型
    FeedbackTypePublishCond = 3; // 发布条件
    FeedbackTypeChannelName = 4; // 房间名称

    // 房间内反馈用户
    FeedbackTypeOwnerInChannel = 5; // 房主反馈进房用户
    FeedbackTypeUserInChannel = 6; // 房客反馈其它进房用户
    FeedbackTypeQuitUninterestedInChannel = 7; // 退房不感兴趣
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message NegativeFeedbackEvent{
    uint32 channel_id = 1;  // 房间id
    uint32 tab_id = 2; // 房间游戏类型
    uint32 creator = 3; // 房主
    repeated BlockOption block_options = 4;    //房间选择的开房标签信息
    string name = 5; // 房间标题
    repeated NegativeFeedbackType negative_Feedback_type = 6; // 反馈类型
    uint32 reporter_uid = 7; //上报者uid
    repeated string exclude_words_of_room = 8; // 不感兴趣的房间名的关键词
    repeated string reasons_of_blacking_creator = 9;// 不想看到房主的原因

    uint32 black_channel_user = 10;  // 反馈进房用户
    bool black_channel_user_enable_filter = 11; // 是否开启过滤被反馈的用户
    repeated string reasons_of_black_channel_user = 12; // 反馈进房用户原因
}

message FreezeChannelEvent{
    uint32 channel_id = 1;
    int64 freeze_at = 2;
    int64 freeze_time = 3;
}

//房间发布
message ChannelFollowStatusUpdateEvent {
    uint32 uid = 1;
    uint32 channel = 2;
    bool is_publish = 3; //true为发布，false为取消发布
    uint32 tab_id = 4; //属于哪个tab，不用再查询
}

message TopicDuration {
  string topic_id = 1; // 动态话题id
  uint32 stay_duration = 2; // 停留时长, 单位：秒
}

//开黑活动上报
message GameActivityTaskReportEvent {
    uint32 uid = 1; // 完成任务的用户id
    int64  finish_time = 2;//任务完成时间戳
    //任务类型，见channel-play_.proto, DailyTaskType, 1：每日第一次进入游戏专区时上报,
    // CONFIG_TAB_VIEW_DATA = 2; // 用户在xxxx主题xxxxtab的停留的时长 和 对应元素的数量
    uint32 game_task_type = 3;
    uint32 tab_id = 4; // 主题玩法id
    string config_tab_id = 5; // xxxtab动态tab id
    uint32 stay_duration = 6; // 停留时长, 单位：秒
    uint32 view_type = 7; // 浏览数据类型， 1-开黑房，2-动态帖子，3-搭子卡片，4-群聊
    uint32 view_count = 8; // 浏览元素数量
    uint32 app_id = 9;
    uint32 market_id = 10;
    repeated string topic_ids = 11; // 浏览的动态话题列表
}

//房间列表近三十分钟筛选记录
message ChannelListSelectTagEvent {
    uint32 uid = 1; // 完成任务的用户id
    repeated string select_tags = 2; // 近三十分钟筛选的标签
    uint32 tab_id = 3; // 玩法id
    int64 expire_time = 4; // 筛选标签有效时长，秒级
}

// 上报给活动方的事件
message GameActivityEvent {
    // 事件类型
    enum EventType {
        EVENT_TYPE_UNSPECIFIED = 0;
        // 游戏搭子卡
        EVENT_TYPE_GAME_PAL_CARD = 1;
    }

    // 游戏搭子卡事件
    message GamePalCardEvent {
        enum Action {
            ACTION_UNSPECIFIED = 0;
            // 曝光
            ACTION_EXPOSED = 1;
            // 点击下一个
            ACTION_SWITCHED = 2;
            // 打招呼
            ACTION_GREETED = 3;
        }

        Action action = 1;
        // 玩法ID
        uint32 tab_id = 2;
        // 搭子卡ID
        string card_id = 3;
    }

    uint32 uid = 1;
    uint32 app_id = 2;
    uint32 market_id = 3;

    // 上报时间
    int64 reported_at = 4;

    EventType event_type = 5;
    bytes event_detail = 6;

    google.protobuf.Any detail = 7;
}
