syntax = "proto3";

// buf:lint:ignore PACKAGE_LOWER_SNAKE_CASE 已迁移 psr，此处协议废弃
package OnlineStatus;

import "google/protobuf/timestamp.proto";

option go_package = "golang.52tt.com/protocol/services/online_status;OnlineStatus";


// 区分公司不同app
// TODO: 补充其他appid
enum QwAppId {
  APP_ID_UNKNOWN = 0;
  TT = 1;         // TT语音
  HY = 2;         // 欢游
  MK = 8;         // 麦可
  MJ = 16;        // 谜境
}

// 事件类型
enum EventType {
  EVENT_TYPE_UNKNOWN = 0;   // 未指定
  ONLINE = 1;               // 在线
  OFFLINE= 2;               // 下线
}

// 终端类型
enum Platform {
  PLATFORM_UNKNOWN = 0;   // 未指定
  ANDROID = 1;            // 安卓
  IOS = 2;                // IOS
  PC = 3;                 // 桌面端
}

// TT专用扩展字段，其余app跳过
message TTExtra {
  uint32 proxy_ip = 1;        // TT gaproxy ip
  uint32 proxy_port = 2;      // TT gaproxy port
  uint32 client_id = 3;       // 接入端客户端连接标识
  uint32 terminal_type = 4;   // 终端类型
  string proxy_name = 5;      // pushd实例名
  uint64 conn_id = 6;         // pushd connid
  uint32 market_id = 7;       // market_id
  uint32 client_version = 8;  // 客户端版本号
}

// 事件通知消息
message OnlineEventNotify {
  QwAppId app_id = 1;                       // appid
  EventType event = 2;                      // 最新在线事件
  Platform platform = 3;                    // 终端类型
  uint64 uid = 4;                           // uid
  bytes extra = 6;                          // 扩展字段
  google.protobuf.Timestamp timestamp = 7;  // 时间
}

// 查询uid在线状态请求
message QueryOnlineStatusReq {
  repeated QwAppId app_id_list = 1; // appid列表，兼容多马甲包的情况
  repeated uint64 uid_list = 2;     // uid列表，最多1000个，超过会报错
}

message StatusResult {
  uint64 uid = 1;        // uid
  QwAppId app_id = 2;    // 在线appid列表
  Platform platform = 3; // 终端类型
  bytes extra = 4;       // 透传OnlineEventNotify中的extra字段
}

// 查询uid在线状态应答
message QueryOnlineStatusResp {
  repeated StatusResult status_result = 1; // key为uid，value为在线状态列表
}

service OnlineStatus {
  // 查询用户在线状态，response中只返回在线的uid相关信息，离线的uid不返回
  rpc QueryOnlineStatus(QueryOnlineStatusReq) returns (QueryOnlineStatusResp) {}
}
