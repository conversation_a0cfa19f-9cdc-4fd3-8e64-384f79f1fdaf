syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-live-http-logic";
package channel_live_http_logic;


// 获取主页信息 POST /channel-live/anchor-cert/anchorCertInit
message AnchorCertInitInfo {
  string last_check_level = 1; // 最新一次的考核等级，没有则为空

  bool is_show_cert_info = 2;     // 是否展示认证等级信息
  bool is_cert_keep_upgrade = 3;  // 保级与升级状态
  string item_name = 4;       // 当前的认证标签，没有则为空
  string base_imgurl = 5;     // 标识底图
  string shadow_color = 6;    // 配置文字投影颜色

  string anchor_tag = 7; // 主播品类
}

// 获取考核信息 POST /channel-live/anchor-cert/getAnchorCheckInfo
message AnchorCheckInfo {
  message CheckData {
    string check_level = 1;
    string create_time = 2;
  }
  repeated CheckData check_info_list = 1; // 历史考核结果
  uint32 check_upgrade_status = 2; // 考核升级状态 see AnchorCheckUpgradeType
  uint32 remain_day = 3; // 考核升级剩余天
}
enum AnchorCheckUpgradeType { // 考核升级状态枚举值
  AnchorCheckUpgradeType_Disable = 0;   // 不满足申请条件
  AnchorCheckUpgradeType_Enable = 1;    // 满足申请条件, 还未申请
  AnchorCheckUpgradeType_Used = 2;      // 满足申请条件, 已申请
}

// 申请考核升级 POST /channel-live/anchor-cert/applyAnchorCheckUpgrade
message AnchorCheckUpgradeInfo {
  uint32 remain_day = 1; // 考核升级剩余天
}

// 获取认证等级信息 POST /channel-live/anchor-cert/getAnchorCertInfo
message AnchorCertInfo {
  string anchor_tag = 1; // 主播品类
  string item_name = 2; // 当前的认证标签，没有则为空
  string base_imgurl = 3;     // 标识底图
  string shadow_color = 4;    // 配置文字投影颜色

  MonthStats month_stats = 5; // 当月数据
  AnchorCertUpgradeInfo anchor_cert_upgrade_info = 6; // 升级任务模块
}
message MonthStats {
  uint32 month_active_days = 1; //  月直播活跃天数
  uint32 month_platform_days = 2; // 平台活跃天数
  uint32 month_new_fans_cnt = 3; // 新增粉丝
  uint32 month_consumer_cnt = 4; // 付费人数
  uint32 month_gift_value = 5; // 总收礼值
}
message AnchorCertUpgradeInfo {
  string task_img_url = 1; // 升级任务图片  字段值为空则不展示
  string task_name = 2; // 升级任务名称
  string task_place = 3; // 升级任务地点
  string task_initial_level = 4; // 参与对象
  string task_check_type = 5; // 考核方式
  string task_check_time = 6; // 考核时间
  string task_jump_url = 7;
}


// *****  官频报名 *****//
// 检查用户的报名入口信息
message CheckUserRegisterEntryReq {
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CheckUserRegisterEntryResp {
  bool isHasRegisEntry = 1;  // 是否有报名入口
}

// 报名状态类型
enum RegisterStatus {
  RegisterStatusInvalid = 0 ;  // 无效
  RegisterStatusCan = 1; // 可报名
  RegisterStatusSuccess = 2; // 报名成功
  RegisterStatusAlreadyOther = 3;  // 同时段已有报名其他
  RegisterStatusNoAct = 4; // 官频没有场次可报
  RegisterStatusCancel = 5;  // 已取消报名
  RegisterStatusNoOnTimeLive = 6; // 报名了未按时开播
  RegisterStatusReview = 7;  // 报名审核中
}

// 场次排班信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message MatchSchedule {
  uint32 beginTs = 1; // 开始时间
  uint32 endTs = 2;  // 结束时间
  bool isCanRegister = 3; // 是否还有报名名额
  bool isAlreadyRegister = 4; // 是否已有同时段的报名
  uint32 scheduleId = 5; // 环节id
}

// 场次信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message MatchInfo {
  uint32 matchTs = 1;  // 场次时间
  string matchName = 2;  // 场次名称
  repeated MatchSchedule schedule_list = 3;  // 排班信息
  uint32 matchId = 4;  // 场次id
}

// 官频信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message OfficialChannelInfo {
  uint32 channelId = 1;
  string actName = 2;  // 活动名称
  string actTime = 3;  // 活动时间
  int64  registerTs = 4;  // 报名时间
  string registerCond = 5;  // 报名条件
  uint32 registerStatus = 6;  // 报名状态
  MatchInfo  registerMatch = 7; // 报名成功的场次信息
  string actionExample = 8;  // 操作示例链接
  string audioExample = 9;  // 音频实例链接
}

// 获取报名页的官频列表
message GetRegisterOfficialChListReq {
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetRegisterOfficialChListResp {
  repeated OfficialChannelInfo  infoList = 1;
}

//获取官频的场次信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetOfficialChMatchInfoReq {
  uint32 channelId = 1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetOfficialChMatchInfoResp {
  repeated MatchInfo matchInfo = 1;  // 场次信息
  string actionExample = 2;  // 操作示例链接
  string audioExample = 3;  // 音频实例链接
  string introExemple = 4;  // 个人介绍填写示例
}

// 报名官频
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message RegisterOfficialChannelReq {
  uint32 channelId = 1;
  string introduction = 2; // 个人介绍
  MatchInfo  matchInfo = 3;  // 报名的场次信息
}
message RegisterOfficialChannelResp {
}

// 取消报名
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CancelRegisterOfficialChReq {
  uint32 channelId = 1;
  int64  registerTs = 2;  // 报名时间
  MatchInfo  matchInfo = 3;  // 报名的场次信息
}
message CancelRegisterOfficialChResp {
}


// *****  官频报名 *****//


// ================================ 语音直播节目单相关接口 ===================================

// 获取可申报时段, uri: /channel-live-http-logic/channel-live-show-list/GetShowTime
message GetShowTimeRequest {
  uint32 datetime = 1; // 日期, 所选日期0点时间戳
}

message GetShowTimeResponse {
  message ShowTime {
    uint32 show_start_time = 1; // 节目开始时间
    uint32 show_end_time = 2; // 节目结束时间
    uint32 remain_cnt = 3; // 剩余可报个数
  }
  repeated ShowTime show_time_list = 1;
  uint32 earliest_selectable_time = 2; // 最早可申报时间, 当天0点
}

// 获取节目标签 uri: /channel-live-http-logic/channel-live-show-list/GetShowTag
message GetShowTagRequest {}

message GetShowTagResponse {
  message TagNode {
    uint32 tag_id = 1; // 标签id
    string tag_name = 2; // 标签名称
    repeated TagNode child_list = 3; // 子标签
  }

  repeated TagNode voice_tag_list = 1; // 语音标签
  repeated TagNode content_tag_list = 2; // 内容标签
  uint32 server_timestamp = 3; // 服务器时间
}

// 申报节目 uri: /channel-live-http-logic/channel-live-show-list/DeclareShow
message DeclareShowRequest {
  string show_name = 1; // 节目名称
  uint32 show_start_time = 2; // 节目开始时间
  uint32 show_end_time = 3; // 节目结束时间
  uint32 voice_tag_id = 4; // 语音标签id
  uint32 content_tag_id = 5; // 内容标签id
  string show_desc_audio = 6; // 节目描述音频
  uint32 show_desc_audio_duration = 7; // 节目描述音频时长
  string show_cover_img = 8; // 节目封面图
}

message DeclareShowResponse {}


message ShowSearchOption {
  uint32 voice_tag_id = 1; // 语音标签id
  uint32 content_tag_id = 2; // 内容标签id
}

message GetShowListRequest {
  uint32 datetime = 1; // 日期, 所选日期0点时间戳
  repeated uint32 voice_tag_id = 2; // 语音标签id
  repeated uint32 content_tag_id = 3; // 内容标签id
}


message ShowItem {
  uint32 show_id = 1; // 节目id
  string show_name = 2; // 节目名称
  string show_desc_audio = 3; // 节目描述音频
  string show_cover_img = 4; // 节目封面图
  uint32 live_status = 5; // 节目状态 see 直播状态 channel-live-mgr.proto EnumChannelLiveStatus
  string account = 6; // 主播账号
  string nickname = 7; // 主播昵称
  string avatar = 8; // 主播头像
  uint32 channel_id = 9; // 房间ID,用于点击时跳转
  uint32 audio_duration = 10; // 节目描述音频时长
  uint32 content_id = 11; // 节目内容ID
  uint32 voice_id = 12; // 节目描述音频ID
  int64 hot_value = 13; // 节目热度
}

message GetShowListResponse {
  message ShowGroupItem {
    string time_range = 1; // 节目时间范围
    repeated  ShowItem show_list = 2; // 节目列表 , 热度前四
    bool is_end = 3; // 是否结束
    bool has_more = 4; // 是否有更多节目
    uint32 begin_time = 5; // 开始时间戳
    uint32 end_time = 6; // 结束时间戳
  }

  repeated ShowGroupItem show_list = 1;
}

// 展开时间段列表
message UnfoldShowListRequest {
  uint32 begin_time = 1; // 开始时间戳
  uint32 end_time = 2; // 结束时间戳
  repeated uint32 voice_tag_id = 3;
  repeated uint32 content_tag_id = 4; // 内容标签id
}

// 展开时间段列表
message UnfoldShowListResponse {
  repeated ShowItem show_list = 1;
}



// 获取节目单用户基础信息 uri: /channel-live-http-logic/channel-live-show-list/GetShowListUserBaseInfo
message GetShowListUserBaseInfoRequest {}

message GetShowListUserBaseInfoResponse {
  uint32 remain_declare_cnt = 1; // 剩余可申报个数
  bool show_apply_entry = 2; // 是否申请
  string unable_reason = 3; // 不可申请原因(显示按钮，但点击时提醒)
}