syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-live-show-list";
package channel_live_show_list;


service ChannelLiveShowList {
  // 获取审批记录
  rpc GetChannelLiveShowApprovalList (GetChannelLiveShowApprovalListRequest) returns (GetChannelLiveShowApprovalListResp) {}

  // 获取标签配置
  rpc GetShowTag (GetShowTagRequest) returns (GetShowTagResponse) {}

  // 修改标签
  rpc ModifyShowApprovalTag (ModifyShowApprovalTagRequest) returns (ModifyShowApprovalTagResponse) {}

  // 处理审批
  rpc HandleShowApproval (HandleShowApprovalRequest) returns (HandelShowApprovalResponse) {}

  // 获取可申报时段
  rpc GetShowTime (GetShowTimeRequest) returns (GetShowTimeResponse) {}

  // 根据主播uid、cid获取直播间节目信息
  rpc GetChannelLiveShowInfo (GetChannelLiveShowInfoRequest) returns (GetChannelLiveShowInfoResponse) {
  }

  // 节目评分
  rpc RateChannelLiveShow (RateChannelLiveShowRequest) returns (RateChannelLiveShowResponse) {
  }

  rpc TestTool (TestToolRequest) returns (TestToolResponse) {}

  // 获取节目入口信息
  rpc GetLiveShowEntryInfo (GetLiveShowEntryInfoRequest) returns (GetLiveShowEntryInfoResponse) {}
  // 获取节目列表
  rpc GetShowList (GetShowListRequest) returns (GetShowListResponse) {}
  // 展开节目
  rpc UnfoldShowList (UnfoldShowListRequest) returns (UnfoldShowListResponse) {}

  // 申报节目
  rpc DeclareShow (DeclareShowRequest) returns (DeclareShowResponse) {}

  // 添加标签
  rpc AddShowTag (AddShowTagRequest) returns (AddShowTagResponse) {}

  // 删除标签
  rpc DeleteShowTag (DeleteShowTagRequest) returns (DeleteShowTagResponse) {}

  // 获取用户可申报次数
  rpc GetShowListUserBaseInfo (GetShowListUserBaseInfoRequest) returns (GetShowListUserBaseInfoResponse) {}

  // 设置审核状态
  rpc SetApprovalAuditType (SetApprovalAuditTypeRequest) returns (SetApprovalAuditTypeResponse) {}
}




// 获取审批记录
message GetChannelLiveShowApprovalListRequest {
  enum ApprovalFilterStatus {
    Unknown = 0;
    Pending = 1; // 待审批
    Approved = 2; // 已审批
  }
  uint32 datetime = 1; // 查询时间
  uint32 ttid = 2; // 查询ttid
  uint32 approval_filter_status = 3; // 审批状态 see ApprovalFilterStatus
  uint32 page_num = 4; // 页码
  uint32 page_size = 5; // 每页大小
}

message ChannelLiveShowApprovalItem {
  enum ApprovalStatus {
    Unknown = 0;
    Pending = 1; // 待审批
    Pass = 2; // 通过
    Reject = 3; // 拒绝
  }

  uint32 show_approval_id = 1;
  string anchor_nickname = 2; // 主播昵称
  string anchor_ttid = 3; // 主播ttid
  uint32 show_start_time = 4; // 节目开始时间
  uint32 show_end_time = 5; // 节目结束时间
  double anchor_score = 6; // 主播分数
  double this_show_score = 16; // 本场节目分数
  string show_name = 7; // 节目名称
  string show_cover_img = 8; // 节目封面图
  string show_desc_audio = 9; // 节目描述音频
  uint32 voice_tag_id = 10; // 语音标签ID
  uint32 content_tag_id = 11; // 内容标签ID
  uint32 approval_status = 12; // 审批状态 see ApprovalStatus
  string approval_operator = 13; // 审批人
  uint32 approval_time = 14; // 审批时间
  uint32 anchor_uid = 15; // 主播uid
}

message GetChannelLiveShowApprovalListResp {
  repeated ChannelLiveShowApprovalItem approval_list = 1;
  uint32 total = 2; // 总数
}

// 获取标签配置
message GetShowTagRequest {}

message TagNode {
  uint32 tag_id = 1; // 标签id
  string tag_name = 2; // 标签名称
  repeated TagNode child_list = 3; // 子标签
}

message GetShowTagResponse {
  repeated TagNode voice_tag_list = 1; // 语音标签
  repeated TagNode content_tag_list = 2; // 内容标签
}

// 修改标签
message ModifyShowApprovalTagRequest {
  uint32 show_approval_id = 1; // 节目审批id
  uint32 voice_tag_id = 2; // 语音标签id
  uint32 content_tag_id = 3; // 内容标签id
}

message ModifyShowApprovalTagResponse {}

// 处理审批
message HandleShowApprovalRequest {
  uint32 show_approval_id = 1; // 节目审批id
  bool is_pass = 2; // 是否通过
  string operator = 3; // 操作人
}

message HandelShowApprovalResponse {}

message GetShowTimeRequest {
  uint32 datetime = 1; // 日期, 所选日期0点时间戳
}

message GetShowTimeResponse {
  message ShowTime {
    uint32 show_start_time = 1; // 节目开始时间
    uint32 show_end_time = 2; // 节目结束时间
    uint32 remain_cnt = 3; // 剩余可报个数
  }
  repeated ShowTime show_time_list = 1;
  uint32 earliest_selectable_time = 2; // 最早可申报时间, 当天0点
}


message GetLiveShowEntryInfoRequest {
  uint32 channel_id = 1; // 房间ID
}

message GetLiveShowEntryInfoResponse {
  repeated string show_entry_text_list = 1; // 直播节目入口文本
}


message GetShowListRequest {
  uint32 datetime = 1; // 日期, 所选日期0点时间戳
  repeated uint32 voice_tag_id_list = 2; // 语音标签id
  repeated uint32 content_tag_id_list = 3; // 内容标签id
  uint32 uid = 4; // 主播uid
}

message ShowItem {
  uint32 show_id = 1; // 节目id
  string show_name = 2; // 节目名称
  uint32 show_start_time = 3; // 节目开始时间
  uint32 show_end_time = 4; // 节目结束时间
  string show_desc_audio = 5; // 节目描述音频
  string show_cover_img = 6; // 节目封面图
  uint32 uid = 7; // 主播uid
  uint32 channel_id = 8; // 房间ID
  uint32 audio_duration = 9; // 节目描述音频时长
  uint32 content_id = 10; // 节目ID
  uint32 voice_id = 11; // 节目音频ID
  int64 hot_value = 12; // 节目热度
  uint32 live_status = 13; // 节目状态 see 直播状态 channel-live-mgr.proto EnumChannelLiveStatus
}

message GetShowListResponse {
  message ShowGroupItem {
    string time_range = 1; // 时间范围
    repeated ShowItem show_item = 2;
    bool is_end = 3; // 是否结束
    bool has_more = 4; // 是否有更多节目
    uint32 begin_time = 5; // 开始时间戳
    uint32 end_time = 6; // 结束时间戳
  }
  repeated ShowGroupItem show_list = 1;
  bool show_apply_entry = 2; // 是否显示节目入口
}

message GetChannelLiveShowInfoRequest {
  uint32 channel_id = 1; // 房间ID
  uint32 anchor_uid = 2; // 主播uid
}

message ShowRatingFloat{
    bool is_show = 1; // 是否展示评分浮层
    int64 listen_sec = 2; // 收听时长
    int64 float_stay_sec = 3;   // 评分浮层停留时间
}


message LiveShowInfo{
    uint32 show_id = 1; // 节目id
    string show_name = 2; // 节目名称
    int64 begin_ts = 3; // 开始时间
    int64 end_ts = 4;   // 结束时间
    uint32 anchor_uid = 5; // 主播uid

    ShowRatingFloat rating_float_conf = 6; // 评分浮层配置信息
}

message GetChannelLiveShowInfoResponse {
  LiveShowInfo live_show_info = 1; // 直播间节目详情
}

message RateChannelLiveShowRequest{
    uint32 uid = 1; // 用户ID
    uint32 show_id = 2; // 节目ID
    uint32 score = 3; // 评分
}

message RateChannelLiveShowResponse{
}


message TestToolRequest{
    message TestShowBeginPush {
        uint32 anchor_uid = 1; // 主播UID
        uint32 channel_id = 2; // 房间ID
        string show_name = 3; // 节目名称
        int64 begin_ts = 4; // 开始时间戳
        int64 end_ts = 5;   // 结束时间戳
    }

    TestShowBeginPush show_info = 1;
}

message TestToolResponse{
}

message DeclareShowRequest {
  string show_name = 1; // 节目名称
  uint32 show_start_time = 2; // 节目开始时间
  uint32 show_end_time = 3; // 节目结束时间
  uint32 voice_tag_id = 4; // 语音标签id
  uint32 content_tag_id = 5; // 内容标签id
  string show_desc_audio = 6; // 节目描述音频
  uint32 show_desc_audio_duration = 7; // 节目描述音频时长
  string show_cover_img = 8; // 节目封面图
}

message DeclareShowResponse {}


message AddShowTagItem {
  uint32 tag_id = 1; // 标签id
  string tag_name = 2; // 标签名称
  uint32 parent_id = 3; // 父标签id
}
message AddShowTagRequest {
  repeated AddShowTagItem tag_list = 1;
}

message AddShowTagResponse {}

message DeleteShowTagRequest {
  repeated uint32 tag_id_list = 1;
}

message DeleteShowTagResponse {}

message GetShowListUserBaseInfoRequest {
  uint32 uid = 1; // 主播uid
}

message GetShowListUserBaseInfoResponse {
  uint32 remain_declare_cnt = 1; // 剩余可申报个数
  bool show_apply_entry = 2; // 是否申请
  string unable_reason = 3; // 不可申请原因(显示按钮，但点击时提醒)
}

enum ChannelLiveShowApprovalAuditType {
    ChannelLiveShowApprovalAuditTypeInvalid = 0;
    ChannelLiveShowApprovalAuditTypePending = 1;
    ChannelLiveShowApprovalAuditTypePass = 2;
    ChannelLiveShowApprovalAuditTypeReject = 3;
}

enum ChannelLiveShowApprovalAuditScene {
  ChannelLiveShowApprovalAuditSceneInvalid = 0;
  ChannelLiveShowApprovalAuditSceneName = 1;
  ChannelLiveShowApprovalAuditSceneCover = 2;
  ChannelLiveShowApprovalAuditSceneAudio = 3;
}

message SetApprovalAuditTypeRequest {
  uint32 approval_id = 1;
  uint32 channel_live_show_approval_audit_scene = 2;
  uint32 channel_live_show_approval_audit_type = 3;
}

message SetApprovalAuditTypeResponse {}


message UnfoldShowListRequest {
  uint32 begin_time = 1; // 开始时间
  uint32 end_time = 2; // 结束时间
  repeated uint32 voice_tag_id_list = 3; // 语音标签id
  repeated uint32 content_tag_id_list = 4; // 内容标签id
}

message UnfoldShowListResponse {
  repeated ShowItem unfold_show_list = 1;
}