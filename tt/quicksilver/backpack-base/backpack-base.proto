syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/backpack-base";
package backpack_base;

// 背包基础服务(c++ backpack服务重构成go服务backpack-base后的协议)

service BackpackBaseService {
    //包裹、包裹物品配置的增、删、改、查
    rpc AddPackageCfg( AddPackageCfgReq ) returns( AddPackageCfgResp );
    rpc GetPackageCfg( GetPackageCfgReq ) returns( GetPackageCfgResp );
    rpc DelPackageCfg( DelPackageCfgReq ) returns( DelPackageCfgResp );
    rpc AddPackageItemCfg( AddPackageItemCfgReq ) returns( AddPackageItemCfgResp );
    rpc AddPackageAndItemCfg( AddPackageAndItemCfgReq ) returns( AddPackageAndItemCfgResp );
    rpc GetPackageItemCfg( GetPackageItemCfgReq ) returns( GetPackageItemCfgResp );
    rpc ModPackageItemCfg( ModPackageItemCfgReq ) returns( ModPackageItemCfgResp );
    rpc DelPackageItemCfg( DelPackageItemCfgReq ) returns( DelPackageItemCfgResp );
    // 获取、添加背包物品展示权重配置
    rpc GetItemWeightCfg (GetItemWeightCfgReq) returns (GetItemWeightCfgRsp);
    rpc AddItemWeightCfg (AddItemWeightCfgReq) returns (AddItemWeightCfgRsp);

    //碎片配置
    rpc AddItemCfg ( AddItemCfgReq ) returns (AddItemCfgResp);
    rpc DelItemCfg ( DelItemCfgReq ) returns (DelItemCfgResp);
    rpc GetItemCfg ( GetItemCfgReq ) returns (GetItemCfgResp);

    // 包裹发放协议
    rpc GiveUserPackage (GiveUserPackageReq) returns (GiveUserPackageResp);
    // 背包物品使用
    rpc UseBackpackItem( UseBackpackItemReq ) returns( UseBackpackItemResp );
    // 批量物品使用
    rpc BatchUseBackpackItem( BatchUseBackpackItemReq ) returns (BatchUseBackpackItemResp );
    // 检查确认某个物品使用订单是否成功
    rpc CheckUseItemSuccByMainOrder(CheckUseItemSuccByMainOrderReq) returns (CheckUseItemSuccByMainOrderResp);
    // 查询用户所有背包物品列表
    rpc GetUserBackpack( GetUserBackpackReq ) returns( GetUserBackpackResp );
    // 按物品类型和ID查询用户背包
    rpc GetUserBackpackByItem (GetUserBackpackByItemReq) returns (GetUserBackpackByItemResp);

    rpc ProcBackpackItemTimeout( ProcBackpackItemTimeoutReq ) returns( ProcBackpackItemTimeoutResp );
    // 二阶段消耗碎片
    rpc FreeZeItem (FreeZeItemReq) returns (FreeZeItemResp);
    rpc FreeZeItemV2 (FreeZeItemV2Req) returns (FreeZeItemV2Resp);

    rpc GetUseItemOrderInfo( GetUseItemOrderInfoReq) returns (GetUseItemOrderInfoResp);
    rpc GetUserBackpackLog( GetUserBackpackLogReq ) returns( GetUserBackpackLogResp );

    rpc GetUserPackageSum(GetUserPackageSumReq) returns (GetUserPackageSumResp);
    rpc GetOrderCountByTimeRange(GetOrderCountByTimeRangeReq) returns (GetOrderCountByTimeRangeResp);
    rpc GetOrderListByTimeRange(GetOrderListByTimeRangeReq) returns (GetOrderListByTimeRangeResp);
    
    //合成物品
    rpc ConversionItem(ConversionItemReq) returns (ConversionItemResp);

    //回滚物品
    rpc RollBackUserItem(RollBackUserItemReq) returns (RollBackUserItemResp);
    //批量扣减
    rpc BatchDeductUserItem(BatchDeductUserItemReq) returns (BatchDeductUserItemResp);

    // 对账：获得订单
    rpc GetTimeRangeOrderData( TimeRangeReq ) returns ( CountResp ) ;
    rpc GetTimeRangeGiveOrderList( TimeRangeReq ) returns ( OrderIdsResp );
    // 对账：使用订单列表
    rpc GetTimeRangeUseOrderData( TimeRangeReq ) returns ( CountResp );
    rpc GetTimeRangeUseOrderList( TimeRangeReq ) returns ( OrderIdsResp );
    rpc GetTimeRangeOrderList( TimeRangeReq ) returns ( OrderIdsResp ); //已废弃
    
    rpc CleanUserBackpackCache(CleanUserBackpackCacheReq) returns (CleanUserBackpackCacheRsp);
}

enum LogType
{
    LOG_TYPE_INVALID = 0;
    LOG_TYPE_USE =1;                    // 使用
    LOG_TYPE_EXPIRE =2;                 // 过期
    LOG_TYPE_FRAGMENT_EXCHANGE = 3;     // 兑换碎片
    LOG_TYPE_FRAGMENT_ROLLBACK = 4;     // 回滚碎片
    LOG_TYPE_ITEM_CONVERSION = 5;     // 物品合成
    LOG_TYPE_ROLLBACK = 6;            //物品回滚
    LOG_TYPE_OPRATE_DEDUCT= 7;            //运营扣除
    LOG_TYPE_BUSINESS_DEDUCT = 8;            //业务扣除
    LOG_TYPE_ENERGY_ITEM_CONVERSION = 9;     // 暗黑礼物合成
    LOG_TYPE_ENERGY_ITEM_STAR_TREK_USE = 10; // 星级巡航使用扣除
    LOG_TYPE_CHANNEL_LOTTERY = 11;      // 房间抽奖扣除
    LOG_TYPE_ENERGY_ITEM_CAT_CANTEEN_USE = 12; // 猫猫餐厅使用扣除
    LOG_TYPE_GLORY_WORLD_USE = 13;  //星钻兑换
    LOG_TYPE_GLORY_WORLD_LOTTERY_USE = 14;   //声望星钻抽奖
	  LOG_TYPE_KNIGHT_CARD_USE = 15;   //骑士体验卡使用
    LOG_TYPE_GLORY_WORLD_MAGIC_USE = 16;   //荣耀星钻抽奖
    LOG_TYPE_DRIFT_BOTTLE_USE = 17;   //漂流瓶使用
    LOG_TYPE_VIRTUAL_IMAGE_TRIAL_USE = 18; // 无限换装体验卡使用
    LOG_TYPE_AI_PRESENT_USE = 19; // 给AI送礼的消耗
}

enum PackageItemType
{
    UNKNOW_ITEM_TYPE=0;
    BACKPACK_PRESENT=1;                // 礼物
    BACKPACK_CARD_RICH_ACCELERATOR=2;  // 财富加速卡
    BACKPACK_CARD_CHARM_ACCELERATOR=3; // 魅力加速卡
    BACKPACK_LOTTERY_FRAGMENT = 4;     // 抽奖碎片,砸蛋获得
    BACKPACK_CARD_RICH_INCR = 5;       // 财富直增卡
    BACKPACK_CARD_KNIGHT    = 6;       // 骑士体验卡
    BACKPACK_CARD_VIRTUAL_IMAGE_TRIAL = 7; // 无限换装体验卡
}

// 包裹来源
enum PackageSourceType
{
    UNKNOW_PACKAGE_SOURCE=0;
    PACKAGE_SOURCE_ACTIVITY_PRESENT = 1;    // 礼物活动
    PACKAGE_SOURCE_DAILY_CHECKIN = 2;       // 每日签到任务
    PACKAGE_SOURCE_FIRST_RECHARGE = 3;      // 首充活动
    PACKAGE_SOURCE_OFFICIAL = 4;            // 内容管理后台中发放
    PACKAGE_SOURCE_SMASHEGG = 5;            // 砸蛋活动
    PACKAGE_SOURCE_CONVERSION = 6;          // 碎片兑换
    PACKAGE_SOURCE_AWARD_CENTER = 7;        // 发奖中心
    PACKAGE_SOURCE_AWARD_YUYIN_LIVE_MISSION = 8; // 语音直播任务奖励
    PACKAGE_SOURCE_INTERACTION_INTIMACY_MISSION = 9; // 亲密度任务
    PACKAGE_SOURCE_ITEM_CONVERSION = 10; // 物品合成
    PACKAGE_SOURCE_NOBILITY = 11;  // 贵族系统发放
    PACKAGE_SOURCE_TBEAN_BUY = 12;  // T豆购买礼物
    PACKAGE_SOURCE_CHANNEL_RED_PACKET = 13; // 房间礼物红包
    PACKAGE_SOURCE_SMASH_GOLD_EGG = 14; // 金色转转活动
    PACKAGE_SOURCE_VIP_MILLION = 15; // VIP百万豪礼
    PACKAGE_SOURCE_ENERGY_STONE_CONVERSION = 16; //黑暗礼物合成(能量石相关)
    PACKAGE_SOURCE_DARK_GIFT_BONUS = 17;  // 黑暗礼物奖励（娱乐玩法保底奖励）
    PACKAGE_SOURCE_ONE_PIECE = 18;      // 航海寻宝
    PACKAGE_SOURCE_STAR_TREK = 19;      // 星级巡航
    PACKAGE_SOURCE_YOU_KNOW_WHO = 20;   // 神秘人
    PACKAGE_SOURCE_GAME_TICKET_RECHARGE = 21;  // 游戏券充值(购买指定礼物送券)
    PACKAGE_SOURCE_CHANNEL_LOTTERY = 23;    // 房间抽奖
    PACKAGE_SOURCE_CAT_CANTEEN = 24;    // 猫猫餐厅玩法

    PACKAGE_SOURCE_GLORY_WORLD = 25;    // 荣誉世界
    PACKAGE_SOURCE_GOLRY_STORE = 26;    // 荣誉商店
    PACKAGE_SOURCE_GOLRY_MISSION = 27;    // 荣誉任务
    PACKAGE_SOURCE_USER_CALL     = 28;    // 用户召回
    PACKAGE_SOURCE_GLORY_WORLD_MAGIC = 29; // 荣耀世界魔法祈愿 
    PACKAGE_SOURCE_PRESENT_WEEK_CARD = 30; // 礼物周卡
    PACKAGE_SOURCE_PEARL_MILK_TEA = 31;   // 珍珠奶茶玩法
    PACKAGE_SOURCE_DRIFT_BOTTLE = 32;   // 漂流瓶玩法
    PACKAGE_SOURCE_DRIFT_BOTTLE_CONVERSION = 33;  // 心愿合成（漂流瓶)
    PACKAGE_SOURCE_OFFICIAL_GAME_WELFARE = 34; // 玩法官方福利, 漂流瓶天降奖励
    PACKAGE_SOURCE_STAR_TRAIN = 35; // 摘星列车玩法
    PACKAGE_SOURCE_GAME_HELPER = 36; // 游戏搭子
    PACKAGE_SOURCE_PRESENT_SET = 37; // 礼物套组
    PACKAGE_SOURCE_FELLOW_LEVEL_AWARD = 38; // 挚友等级奖励
    PACKAGE_SOURCE_INVITE_USER_FROM_CHANNEL = 39; // 房间内邀请用户进房的奖励
    PACKAGE_SOURCE_NEW_FIRST_RECHARGE = 40; // 新首充活动
    PACKAGE_SOURCE_EXT_GAME_ZDXX = 41; // 房间第三方游戏——组队修仙
    PACKAGE_SOURCE_GRAB_CHAIR_GAME = 42; // 抢椅子游戏包裹奖励
    PACKAGE_SOURCE_CHANNEL_WEDDING = 43; // 婚礼房包裹奖励
    PACKAGE_SOURCE_ACTIVITY_PRESENT_MONTH_CARD = 44; // 活动- 礼物月卡
    PACKAGE_SOURCE_ADVENTURE_ACTIVITY = 45; // 冒险活动
    PACKAGE_SOURCE_WEALTH_GOD = 46; // 财神降临
    PACKAGE_SOURCE_GAME_MALL = 47; // 小游戏专区商店购买
    PACKAGE_SOURCE_NEWBIE_ANCHOR = 48; // 新主播指南任务奖励
    PACKAGE_SOURCE_VIRTUAL_IMAGE_CARD = 49; // 无限换装卡
    PACKAGE_SOURCE_FLOWER_ACTIVITY = 50; // 花神降临
    PACKAGE_SOURCE_PASS_REWARD = 51; //通行证奖励
}

// 包裹配置
message PackageCfg
{
    uint32 bg_id=1;
    string name=2;
    string desc=3;
    bool is_del=4;
}


message PackageItemCfg
{
    uint32 bg_item_id=1;
    uint32 bg_id=2;
    uint32 item_type=3;         // PackageItemType
    uint32 source_id=4;         // 礼物id/卡片id/碎片id
    uint32 item_count=5;        // 数量
    uint32 fin_time=6;          // 截止时间
    bool is_del=7;
    uint32 weight=8;            // 权重
    uint32 dynamic_fin_time=9;  // 发放后的有效时间(秒)
    uint32 months = 10;         // 发放后第几个月的1号过期，半年=6，一年=12
}

// 砸蛋得的抽奖碎片
message LotteryFragmentCfg
{
    uint32 fragment_id = 1;           // 添加配置时，不填此字段
    uint32 fragment_type = 2;         // 0默认, 1合并碎片
    string fragment_name = 3;
    string fragment_desc = 4;
    string fragment_url = 5;
    uint32 is_del = 6;
    uint32 fragment_price = 7;        // 后台配置的价格,fragment_price/100 = ￥1
	uint32 is_show_expire_hint = 8;   //是否展示过期提醒
}

message UserBackpackItem
{
    uint32 item_type=1;
    uint32 user_item_id=2;
    uint32 item_count=3;
    uint32 fin_time=4;
    uint32 source_id=5;
    uint32 weight=6;         // 物品权重
    uint32 obtain_time=7;
    uint32 source_type = 8; //see  PackageSourceType
    uint32 final_item_count = 9; //
    uint32 business_type = 10; // T豆包裹类型PresentBusinessType 0:普通包裹 1: 亲密礼物
}

message UserBackpackLog
{
    uint32 item_type=1; // PackageItemType
    uint32 item_count=2;
    uint32 source_id=3;
    uint32 log_type=4;  // LogType
    uint32 log_time=5;
}

message AddPackageCfgReq
{
    PackageCfg cfg=1;
}

message AddPackageCfgResp
{
    PackageCfg cfg=1;
}

message AddPackageAndItemCfgReq
{
    PackageCfg cfg=1;
    repeated PackageItemCfg item_cfg_list=2;
}
message AddPackageAndItemCfgResp
{
    PackageCfg cfg=1;
    repeated PackageItemCfg item_cfg_list=2;
}

message DelPackageCfgReq
{
    uint32 bg_id=1;
}

message DelPackageCfgResp
{
}

message GetPackageCfgReq
{
    uint32 offset = 1;
    uint32 limit = 2;
}

message GetPackageCfgResp
{
    repeated PackageCfg cfg_list=1;
	uint32 total_count = 2;
}

message AddPackageItemCfgReq
{
    PackageItemCfg item_cfg=1;
}

message AddPackageItemCfgResp
{
    PackageItemCfg item_cfg=1;
}

message ModPackageItemCfgReq
{
    PackageItemCfg item_cfg=1;
}

message ModPackageItemCfgResp
{
}

message DelPackageItemCfgReq
{
    uint32 bg_id=1;
    uint32 bg_item_id=2;
}

message DelPackageItemCfgResp
{
}

message GetPackageItemCfgReq
{
    uint32 bg_id=1;
    repeated uint32 bg_id_list = 2;
}

message PackageItemCfgList
{
    repeated PackageItemCfg item_cfg_list=1;
}

message GetPackageItemCfgResp
{
    repeated PackageItemCfg item_cfg_list=1;
    repeated PackageItemCfgList package_item_cfg_list=2;
}
//获取背包物品展示权重配置
message ItemWeightCfg {
    uint32 item_type = 1;  //物品类型
    uint32 item_id   = 2;  //物品ID 
    uint32 weight    = 3;  //展示权重
    bool   is_del    = 4;  
}
message GetItemWeightCfgReq
{
}
message GetItemWeightCfgRsp
{
    repeated ItemWeightCfg cfg_list = 1;
}

message AddItemWeightCfgReq
{
    ItemWeightCfg cfg = 1;
}
message AddItemWeightCfgRsp
{
    uint32 cfg_id = 1;
}

//T豆包裹类型
enum PresentBusinessType
{
    E_PRESENT_BUSINESS_TYPE_DEFAULT = 0; // 默认业务类型
    E_PRESENT_BUSINESS_TYPE_INTIMATE_PRESENT = 1; // 亲密礼物
}

// 包裹发放协议
message GiveUserPackageReq
{
    uint32 uid=1;
    uint32 bg_id=2;
    uint32 num=3;
    uint32 source=4;            // 包裹来源 PackageSourceType
    string order_id = 5;        //幂等订单号
    uint32 expire_duration = 6; // 按照自然天过期该物品
                                // example:expire_duration=1,当前时间为 2019-10-28 10:42:00 这该物品过期时间为 2019-10-29 00:00:00
                                // note :expire_duration>=1 则会忽略 包裹配置的 dynamic_fin_time, months
    string source_app_id = 7;   // 来源明细
    uint32 total_price = 8;     // 包裹总价值
    uint32 outside_time = 9;    // 赠送包裹时间
    string sign = 10;           // 签名
    uint32 business_type = 11;  // T豆包裹类型PresentBusinessType 0:普通包裹 1: 亲密礼物
}
message GiveUserPackageResp
{

}



message UseBackpackExtraInfo
{
    uint32 target_uid = 1; //收礼人
    uint32 channel_id = 2;
    uint32 use_count = 3;
    string deal_token = 4;
}

//背包物品使用协议
message UseBackpackItemReq
{
    uint32 uid=1;
    uint32 item_type=2;
    uint32 source_id=4;
    string order_id=5;
    uint32 use_count=6;
    uint32 outside_time = 7;
    repeated string order_id_list = 8; //for batch send
    uint32 item_price = 9;
    uint32 price_type = 10;  // 1红钻 2t豆

    repeated uint32 target_uid_list = 11; //for batch send
    UseBackpackExtraInfo extra_info = 12;
    uint32 use_reasion_type = 13; //消耗的理由enum LogType
    uint32 business_type = 14; // T豆包裹类型PresentBusinessType 0:普通包裹 1: 亲密礼物
}
message UseBackpackItemResp
{
    uint32 remain=1;
    string deal_token = 2;
    uint32 fin_time = 3;
}

message BaseItemInfo {
    uint32 item_type = 1;  //物品类型 enum PackageItemType
    uint32 source_id = 2;  //物品ID
    uint32 num  = 3;       //数量
}
//批量背包物品使用协议
message BatchUseBackpackItemReq
{
    uint32 uid = 1;
    string order_id = 2;          //订单ID
    uint32 outside_time = 3;      //订单时间
    uint32 use_reason_type = 4;   //使用原因类型: enum LogType
    repeated BaseItemInfo use_item_infos = 5;  //使用物品信息

}
message BatchUseBackpackItemResp
{
    string deal_token = 1;           // 交易deal token
}

//检查某个物品使用是否成功
message CheckUseItemSuccByMainOrderReq
{
    string order_id = 1;
    uint32 outside_time = 2;      //订单时间
}
message CheckUseItemSuccByMainOrderResp
{
    bool success = 1;
}


message GetUseItemOrderInfoReq
{
    string order_id = 1;
}

message GetUseItemOrderInfoResp
{
    UseOrderDetail use_order_detail = 1;
    UseBackpackExtraInfo use_order_extra_info = 2;
}

message ProcBackpackItemTimeoutReq
{
    uint32 user_item_id=1;
    uint32 uid=2;
    uint32 item_type=3;
    uint32 source_id=4;
    uint32 item_count=5;
    uint32 fin_time=6;
}
message ProcBackpackItemTimeoutResp
{

}

message GetUserBackpackReq
{
    uint32 uid=1;
}
message GetUserBackpackResp
{
    repeated UserBackpackItem user_item_list=1;
    int64 last_obtain_ts = 2;          // 最后获得背包礼物奖励的时间
}

//按物品查询用户物品
message GetUserBackpackByItemReq
{
    uint32 uid = 1;
    uint32 item_type = 2;        //物品类型: 
    uint32 source_id = 3;        //物品ID,  如果为0，查询背包中这类物品
    bool   is_need_obtain_ts = 4;  //是否需要返回最后获取物品时间
}
message GetUserBackpackByItemResp
{
    repeated UserBackpackItem user_item_list=1;
    int64 last_obtain_ts = 2;          // 最后获取物品时间
}
 

message GetUserBackpackLogReq
{
    uint32 uid = 1;
    uint32 begin_time = 2;
    uint32 end_time = 3;
    uint32 item_type = 4;   // PackageItemType
    uint32 source_id = 5;
    uint32 log_type = 6;    // LogType
}

message GetUserBackpackLogResp
{
    repeated UserBackpackLog log_list = 1;
}

// 此接口暂时不支持添加卡片
message AddItemCfgReq{
  uint32 item_type = 1;
  bytes  item_cfg = 2; //填入具体类型的 Cfg;example：item_type==BACKPACK_LOTTERY_FRAGMENT, 将LotteryFragmentCfg 序列化填入
}

message AddItemCfgResp{
  uint32 item_type = 1;
  bytes  item_cfg = 2; // 补充了 id
}

message DelItemCfgReq{
  uint32 item_type = 1;
  /*uint32 item_source_id = 2;*/
  /*uint32 item_source_type = 3;*/
  bytes  item_cfg = 2; //填入具体类型的 Cfg;example：item_type==BACKPACK_LOTTERY_FRAGMENT, 将LotteryFragmentCfg 序列化填入
}

message DelItemCfgResp{
}

message GetItemCfgReq{
  uint32 item_type = 1;
  repeated uint32 item_source_id_list = 2;
  bool get_all = 3;                         // true;忽略 item_id_list,获取类型为 item_type 的 所有的 item 的配置
}

message GetItemCfgResp{
  repeated bytes item_cfg_list = 1;
}

enum FREEZETYPE{
  FREEZETYPE_UNVALID = 0;               // 无效的类型
  FREEZETYPE_PREPARE = 1;               // 初始化操作--预扣除
  FREEZETYPE_COMMIT = 2;                // 操作确认
  FREEZETYPE_ROLLBACK = 3;              // 操作回滚
}

message UseItemInfo
{
    uint32 item_type = 1;
    uint32 user_item_id = 2;
    uint32 source_id = 3;
    uint32 use_count = 4;
    uint32 total_price = 5;
}

message TransactionInfo{
    uint32 freeze_type = 1;       //see FREEZETYPE
    uint32 oper_time = 2;
    string order_id = 3;
    uint32 expire_time = 4;
	uint32 reason = 5;            //冻结时填： LogType， 回滚时填：PackageSourceType
}

//仅支付碎片类型： BACKPACK_LOTTERY_FRAGMENT
message FreeZeItemReq
{
    TransactionInfo tansacation_info = 1;
    repeated UseItemInfo item_info_list = 2;
    uint32 uid = 3;
}

message FreeZeItemResp
{
}

//新版碎片扣除，可回滚. 不用传具体物品项， 按过期时间优先使用
message FreeZeItemV2Req
{
	uint32 uid = 1;
	string order_id = 2;          //订单ID
	uint32 outside_time = 3;      //时间戳 
	uint32 freeze_type = 4;       //see FREEZETYPE
	uint32 item_type = 5;         //物品类型
	uint32 source_id = 6;         //物品ID
	uint32 use_count = 7;         //使用数量
	uint32 reason = 8;            //冻结时填： LogType， 回滚时填：PackageSourceType
}
message FreeZeItemV2Resp
{

}

message UserPackageSum{
  uint32 item_type = 1;
  uint32 item_id=2;
  uint32 item_count=3;
}

message GetUserPackageSumReq
{
  uint32 uid = 1;
  uint32 item_type = 2;
  uint32 item_id = 3;
}

message GetUserPackageSumResp
{
  UserPackageSum item_sum = 1;
}


message GetOrderCountByTimeRangeReq
{
    uint32 begin_time = 1;
    uint32 end_time = 2;
    uint32 log_type = 3;
    string paras = 4;
}

message GetOrderCountByTimeRangeResp
{
    uint32 count = 1; //订单数量
    uint32 use_count = 2; //消耗道具总数
    uint32 value = 3;
}

message GetOrderListByTimeRangeReq
{
    uint32 begin_time = 1;
    uint32 end_time = 2;
    uint32 log_type = 3;
}

message GetOrderListByTimeRangeResp
{
    repeated string order_list = 1;
}

message ConversionItemReq
{
    uint32 uid = 1;
    uint32 outside_time = 2;  // 操作时间
    uint32 bg_id = 3;    // 合成的包裹ID
    uint32 bg_num = 4;   //包裹数量
    uint32 source = 5;   // 包裹来源 PackageSourceType
    uint32 material_price = 6; // 原料总价值
    uint32 conversion_price = 7;  // 合成包裹价值
    string order_id = 8;  // 幂等订单号
    repeated UseItemInfo material_item_list = 9; // 合成消耗物品列表
    uint32 business_type = 10; // T豆包裹类型PresentBusinessType 0:普通包裹 1: 亲密礼物
}

message ConversionItemResp
{
}

message RollBackUserItemReq {
    uint32 uid = 1;
    uint32 create_time = 2; //背包月表中的create_time
    string origin_order_id = 3;   // user_backpack_month_v2_xx 表中需要回退的订单ID
}

message RollBackUserItemResp {
}

//批量回滚相关

message DeductItem{
  uint32 item_type = 1;
  uint32 source_id = 2;
  uint32 count = 3;
}

message DeductDetail {
  uint32 uid = 1;
  repeated DeductItem item_list = 2;
  uint32 count = 3;
  uint32 source_type = 4;
  bool is_all_source = 5;
}

enum DeductType
{
    UNKNOW_DEDUCT_TYPE=0;
    OPRATE_DEDUCT=1;        // 运营扣除（手动）
    BUSINESS_DEDUCT=2;      // 业务扣除
}

message BatchDeductUserItemReq {
  repeated DeductDetail deduct_list = 1; //要回滚物品的信息
  string oper = 2; // 操作者
  string order_id = 3;   // 订单id，用于防止重复回滚
  uint32 deduct_type = 4; // DeductType
}

enum DeductFailType{
  DEDUCTFAILTYPE_UNVALID = 0;               // 未知类型
  DEDUCTFAILTYPE_UID_NOT_EXIST = 1;               // 错误类型：uid不存在
  DEDUCTFAILTYPE_ITEM_NOT_ENOUGH = 2;                // 错误类型：物品数量不足
  DEDUCTFAILTYPE_DEDUCT_ITEM_FAIL = 3;                // 错误类型：扣除物品失败
}

message DeductResult {
  uint32 uid = 1;
  repeated DeductItem success_item_list = 2;
  repeated DeductItem fail_item_list = 3;
  uint32 fail_type = 4; //失败类型DeductFailType
}

message BatchDeductUserItemResp {
  repeated DeductResult deduct_list = 1; //实际回滚物品的信息
}

message UseOrderDetail
{
    uint32 uid = 1;
    string order_id = 2;
    uint32 source_id = 3;
    uint32 item_type = 4;
    uint32 use_count = 5;
    uint32 price_type = 6;
    uint32 create_time = 7;
    uint32 user_item_id = 8;
}


//获得订单数据
message TimeRangeReq {
  int64 begin_time = 1;
  int64 end_time = 2;
  string params = 3;
}

//响应order_id个数
message CountResp {
  uint32 count = 1;
  uint32 value = 2; //总价值，如果没有填0
}

//响应orderId详情
message OrderIdsResp {
  repeated string order_ids = 1;
}

message CleanUserBackpackCacheReq {
	uint32 uid = 1;
}
message CleanUserBackpackCacheRsp {
}