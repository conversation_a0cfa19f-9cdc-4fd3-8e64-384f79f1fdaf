
syntax = "proto3";

package virtual_image_resource;
option go_package = "golang.52tt.com/protocol/services/virtual-image-resource";



service VirtualImageResource {
  // =================== 运营后台接口 ===================
  // 添加虚拟形象资源【git同步资源】
  rpc AddVirtualImageResource(AddVirtualImageResourceRequest) returns (AddVirtualImageResourceResponse);
  // 克隆虚拟形象资源【云测同步资源到线上】
  rpc CloneVirtualImageResource(CloneVirtualImageResourceRequest) returns (CloneVirtualImageResourceResponse);
  // 搜索虚拟形象资源列表
  rpc SearchVirtualImageResource(SearchVirtualImageResourceRequest) returns (SearchVirtualImageResourceResponse);
  // 编辑虚拟形象资源
  rpc EditVirtualImageResource(EditVirtualImageResourceRequest) returns (EditVirtualImageResourceResponse);
  // 删除虚拟形象资源
  rpc DeleteVirtualImageResource(DeleteVirtualImageResourceRequest) returns (DeleteVirtualImageResourceResponse);
  // 设置虚拟形象资源上架到商品
  rpc SetVirtualImageResourceForSale(SetVirtualImageResourceForSaleRequest) returns (SetVirtualImageResourceForSaleResponse);
  // 更新虚拟形象资源下载url
  rpc UpdateVirtualImageResourceUrl(UpdateVirtualImageResourceUrlRequest) returns (UpdateVirtualImageResourceUrlResponse);
  // 获取虚拟形象资源套装列表
  rpc GetVirtualImageResourceBySuit(GetVirtualImageResourceBySuitRequest) returns (GetVirtualImageResourceBySuitResponse);
  // 批量更新图标
  rpc BatchUpdateIcon(BatchUpdateIconRequest) returns (BatchUpdateIconResponse);
  // 获取等级配置
  rpc GetLevelConfig(GetLevelConfigRequest) returns (GetLevelConfigResponse);
  // 更新等级配置
  rpc UpdateLevelConfig(UpdateLevelConfigRequest) returns (UpdateLevelConfigResponse);
  // 增加等级配置
  rpc AddLevelConfig(AddLevelConfigRequest) returns (AddLevelConfigResponse);
  // 获取提醒配置缓存
  rpc GetNoticeCfgCache(GetNoticeCfgCacheRequest) returns (GetNoticeCfgCacheResponse);
  // 根据套装编码列表获取套装信息
  rpc GetSuitResourceList(GetSuitResourceListRequest) returns (GetSuitResourceListResponse);


  // =================== 运营后台接口 end ===================

  // 根据资源ID获取资源信息
  rpc GetVirtualImageResourcesByIds(GetVirtualImageResourcesByIdsRequest) returns (GetVirtualImageResourcesByIdsResponse);

  // =================== 客户端接口 ===================
  // 获取虚拟形象资源列表
  rpc GetClientListByPage(GetClientListByPageRequest) returns (GetClientListByPageResponse);

  // 获取资源品类列表
  rpc GetVirtualImageResourceCategory(GetVirtualImageResourceCategoryRequest) returns (GetVirtualImageResourceCategoryResponse) {}

  //获取默认资源列表
   rpc GetDefaultResourceList(GetDefaultResourceListRequest) returns (GetDefaultResourceListResponse);

  // 获取提醒配置
  rpc GetNoticeCfg(GetNoticeCfgRequest) returns (GetNoticeCfgResponse);

  // 更新提醒配置
  rpc UpdateNoticeCfg(UpdateNoticeCfgRequest) returns (UpdateNoticeCfgResponse);

  // 获取动作资源映射 小-大
  rpc GetActionResourceMap(GetActionResourceMapRequest) returns (GetActionResourceMapResponse);

  // 设置资源为待测试
  rpc SetResourceToTest(SetResourceToTestRequest) returns (SetResourceToTestResponse);
}


enum VirtualImageResourceStatus {
    VIRTUAL_IMAGE_RESOURCE_STATUS_UNKNOWN = 0;
    VIRTUAL_IMAGE_RESOURCE_STATUS_NORMAL = 1;  // 待测试
    VIRTUAL_IMAGE_RESOURCE_STATUS_DELETED = 2; // 已删除
    VIRTUAL_IMAGE_RESOURCE_STATUS_UPLOADED_PENDING_TEST  = 3;         //已上传，待测试
    VIRTUAL_IMAGE_RESOURCE_STATUS_TESTING                = 4;       //测试中
    VIRTUAL_IMAGE_RESOURCE_STATUS_RE_UPLOADED_PENDING_TEST  = 5;    //已重新上传待测试
    VIRTUAL_IMAGE_RESOURCE_STATUS_ISSUE_PENDING_FIX      = 6;       //有问题待修复
    VIRTUAL_IMAGE_RESOURCE_STATUS_ORIGINAL_OUTPUT        = 7;       //原画已输出
    VIRTUAL_IMAGE_RESOURCE_STATUS_TEST_PAUSED            = 8;      //暂停测试
    VIRTUAL_IMAGE_RESOURCE_STATUS_ACTION_IN_PROGRESS     = 9;      //动作制作中
    VIRTUAL_IMAGE_RESOURCE_STATUS_ORIGINAL_IN_PROGRESS   = 10;      //原画制作中
    VIRTUAL_IMAGE_RESOURCE_STATUS_PENDING_PRODUCTION     = 11;      //待定制作
    VIRTUAL_IMAGE_RESOURCE_STATUS_TEST_COMPLETED         = 12;      //测试完成
    VIRTUAL_IMAGE_RESOURCE_STATUS_COMPLETED_PENDING_UPLOAD  = 13;      //已完成待上传
}

/*
一级分类
套装 - 物品没有套装
捏脸
服饰
氛围
 */
enum VirtualImageResourceCategory {
  VIRTUAL_IMAGE_RESOURCE_CATEGORY_UNKNOWN = 0;
  VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SUIT = 1;      // 套装
  VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SHAPING = 2;   // 捏脸
  VIRTUAL_IMAGE_RESOURCE_CATEGORY_DRESS = 3;          // 服饰
  VIRTUAL_IMAGE_RESOURCE_CATEGORY_ATMOSPHERE = 4;     // 氛围
}

/*
 see https://q9jvw0u5f5.feishu.cn/mindnotes/OkSKbOXctmHrv4nd6Fqcp4mMnxh
*/
enum VirtualImageResourceSubCategory{
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_UNKNOWN = 0;
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SUIT = 1000;             // 套装
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FACE_TEMPLATE = 1001;    // 脸部模板
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FACE_SHAPE = 1002;       // 脸型
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HAIRSTYLE = 1003;        // 头发
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_EYEBROWS = 1004;         // 眉毛
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_EYES = 1005;             // 眼睛
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_MOUTH = 1006;            // 嘴巴
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_MAKEUP = 1007;           // 妆面

  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FULL_OUTFIT = 1008;      // 全身装
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_UPPER_OUTFIT = 1009;     // 上装
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_LOWER_OUTFIT = 1010;     // 下装
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOES = 1011;            // 鞋子
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SOCKS = 1012;            // 袜子
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HAT = 1013;              // 头饰
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_EARRINGS = 1014;         // 耳饰
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FACE_ACCESSORIES = 1015; // 面饰
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_NECK_ACCESSORIES = 1016; // 颈饰
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HANDBAG = 1017;          // 包包
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HANDHELD_ITEMS = 1018;   // 手持物
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_WINGS = 1019;            // 翅膀
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BODY_ACCESSORIES = 1020; // 环身特效
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SITTING_POSE = 1021;     // 坐姿
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_CHAIR = 1022;            // 座椅
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BACKGROUND = 1023;       // 背景
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_ROOM_ENTRANCE_EFFECTS = 1024; // 进房特效
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TONG_KONG = 1025;       // 瞳孔
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_OTHER = 1026;           // 其他
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_ACCESSORY = 1027;      // 挂饰
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_WAI_TAO = 1028;      // 外套
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_PI_FENG  = 1029;      // 披风
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TOU_SHI  = 1030;      // --废弃
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOU_SHI  = 1031;      // 手饰
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_JIE_ZHI  = 1032;      // 戒指
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TUI_SHI  = 1033;      // 腿饰--废弃

  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHANG_SHEN = 1500;      // 身体
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BO_ZI = 1501;           // 脖子
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_XIA_SHEN = 1502;        // 下身
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_XIONG_BU = 1503;        // 胸部
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TUI = 1504;            // 腿

  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOU_L = 1507;         // 左手掌
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOU_R = 1508;         // 右手掌
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_ER_DUO = 1509;         // 耳朵
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BI_ZI = 1510;          // 鼻子
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_LEG_L = 1511;         // 左腿
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_LEG_R = 1512;         // 右腿
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FOOT_L = 1513;        // 左脚掌
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FOOT_R = 1514;        // 右脚掌
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HAND_L = 1515;       //左手臂
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_HAND_R = 1516;       //右手臂
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TEZI = 1517;           //特姿

  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SHOW_VAP = 10000;       //宣传片
  VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_OTHER_VAP = 10001;     //套装宣传片
}


enum VirtualImageResourceType {
  VIRTUAL_IMAGE_RESOURCE_TYPE_UNKNOWN = 0;
  VIRTUAL_IMAGE_RESOURCE_TYPE_SKELETON = 1;    // 骨骼&纹理
  VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN = 2;        // 皮肤
  VIRTUAL_IMAGE_RESOURCE_TYPE_BACKGROUND = 3;   // 背景VAP
  VIRTUAL_IMAGE_RESOURCE_TYPE_FOLLOW = 4;       // 跟随进房VAP
  VIRTUAL_IMAGE_RESOURCE_TYPE_SHOW_VAP = 5;     // 宣传片VAP
  VIRTUAL_IMAGE_RESOURCE_TYPE_DEFAULT_BACKGROUND_PNG = 6;   // 默认背景PNG资源
  VIRTUAL_IMAGE_RESOURCE_TYPE_ANIMATION = 7;                // 动作  绑定氛围4-坐姿1021
  VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN_FULL_OUTFIT = 8;        // 特殊皮肤-全身装
  VIRTUAL_IMAGE_RESOURCE_TYPE_INFORMATION_CARD_VAP = 9;    //资料卡主页动画
  VIRTUAL_IMAGE_RESOURCE_TYPE_CP_SKELETON = 10;        // 双人骨骼
  VIRTUAL_IMAGE_RESOURCE_TYPE_SIDE_SKELETON = 11;        // 侧身骨骼
  VIRTUAL_IMAGE_RESOURCE_TYPE_CP_SKIN = 12;        // 双人皮肤
  VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_SKELETON = 13;        // 分皮肤的基础骨架，区别于原来SKELETON
  VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_SIDE_SKELETON = 14;   // 分皮肤的基础侧身骨架，区别于原来 SKELETON
  VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_CP_SKELETON = 15;     // 分皮肤的基础双人骨架，区别于原来 SKELETON
  VIRTUAL_IMAGE_RESOURCE_TYPE_TEZI = 16;     // 特姿
}

enum VirtualImageResourceSex {
  VIRTUAL_IMAGE_RESOURCE_SEX_UNKNOWN = 0;   //
  VIRTUAL_IMAGE_RESOURCE_SEX_MALE = 1;      // 男
  VIRTUAL_IMAGE_RESOURCE_SEX_FEMALE = 2;    // 女
  VIRTUAL_IMAGE_RESOURCE_SEX_COMMON = 3;    // 通用
  VIRTUAL_IMAGE_RESOURCE_SEX_CP_FRONT = 4;    // 双人男女正面
  VIRTUAL_IMAGE_RESOURCE_SEX_CP_SIDE = 5;    // 双人男女侧面
  VIRTUAL_IMAGE_RESOURCE_SEX_MALE_FRONT_FEMALE_SIDE = 6;    // 双人男正女侧
  VIRTUAL_IMAGE_RESOURCE_SEX_CP_MALE_SIDE_FEMALE_FRONT = 7;    // 双人男侧女正
}

message VirtualImageResourceInfo {
  uint32 id = 1;             // 资源ID[自增ID]
  string skin_name = 2;      // 皮肤名
  string resource_url = 3;   // 资源地址 OBS
  bool essential = 4;        // 是否必须的
  string encrypt_key = 5;    // 加密key
  string md5 = 6;            // md5
  uint32 resource_type = 7;  // 资源类型 see VirtualImageResourceType
  uint32 category = 8;       // 资源品类 see VirtualImageResourceCategory
  uint32 sub_category = 9;   // 资源子分类 see  VirtualImageResourceSubCategory
  string spline_version = 10; // spline版本号
  string display_name = 11;   // 显示名称 [运营录入的名称]
  string icon_url = 12;       // 图标地址
  uint32 sex = 13;            // 性别  see VirtualImageResourceSex 0: 通用  1: 男  2: 女
  string level_icon = 14;     // 等级图标
  uint32 level = 15;          // 等级
  uint32 status = 16;         // 状态 see VirtualImageResourceStatus
  string default_suit = 17;   // 默认套装名称
  uint32 shelf_time = 18;     // 上架时间
  uint32 expire_time = 19;    // 下架时间
  string operator = 20;       // 操作人
  uint32 create_time = 21;    // 创建时间
  uint32 update_time = 22;    // 更新时间[资源版本号]
  bool   is_online = 23;      // 是否上线
  bool   is_commodity = 24;   // 是否已配置过商品
  string resource_name = 25;  // 资源名称  [资源唯一标识]
  uint32 version = 26;        // 资源版本号
  string ios_md5 = 27;        // ios md5
  string ios_resource_url = 28; // ios 资源地址
  string level_webp = 29;     // 商品图标特效
  bool   scale_able  = 30;     // 是否支持缩放
  string default_animation = 31; // 默认动作
  bool is_deleted = 32;         // 是否删除 默认false 删除后为true
  string attachment_name = 33;   // 朝向别名 --已弃用
  uint32 skin_facing = 34;       // 朝向 0:正面 1:侧面45度 --已弃用
  string resource_prefix = 35;   // 资源前缀
  map<string, string> custom_map = 36; // ios资源地址map eg: ios_015:http://xxx.png ios_030:http://xxx.atlas

  // 按照骨骼名称作为key区分皮肤资源信息
  // 例如:
  // 1. 正身皮肤资源信息, key: base_boy.zip, base_girl.zip
  // 2. 侧身皮肤资源信息, key: base_cboy.zip, base_cgirl.zip
  // 3. 双人正正皮肤资源信息, key: base_zboy_zgirl.zip
  // 4. 双人正侧皮肤资源信息, key: base_zboy_cgirl.zip
  // 5. 双人侧侧皮肤资源信息, key: base_cboy_cgirl.zip
  map<string, SkinInfo> skin_map = 37;
  map<string, SkinInfo> ios_skin_map = 38;
  uint32 ios_version = 39;   // ios资源版本号
  bool is_new_resource = 40;   // 是否新资源
  // texture资源地址map   ios_low                        : url - astc(zip)
  //                    ios_high
  //                    android_low                    : url -  astc(zip)
  //                    pc_low                         : url -  ktx2
  map<string, string> texture_url_map = 41;
}

message SkinInfo {
  string url = 1;               //zip: skin.json/skin.skel, skin.atlas; skin与atlas是一一对应的（CI阶段保证）。
  uint32 min_bones_version = 2; // required min skeleton version
  string md5 = 3;               // md5
}

message AddVirtualImageResourceRequest {
  repeated VirtualImageResourceInfo resources = 1;
  bool is_end = 2; // 当前上传是否结束，结束触发cdn更新
}

message AddVirtualImageResourceResponse {
}

message CloneVirtualImageResourceRequest {
  repeated VirtualImageResourceInfo resources = 1;
}

message CloneVirtualImageResourceResponse {
}

enum VirtualImageResourceCommodityFilter {
  VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_UNKNOWN = 0;
  VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_COMMODITY = 1;  // 已配置商品
  VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_NOT_COMMODITY = 2; // 未配置商品
  VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_ALL = 3;        // 全部

}

message SearchVirtualImageResourceRequest {
  repeated uint32 id = 1;              // [搜索选项]
  repeated string name = 2 ;           // 资源名称
  uint32 category = 3;        // 资源品类 see VirtualImageResourceCategory
  uint32 sub_category = 4;    // 资源子分类
  uint32 sex = 5;             // 性别  1: 男  2: 女 0: 通用
  uint32 level = 6;           // 等级
  string suit = 7;            // 套装名称
  //bool is_commodity = 8;      // 是否已配置过商品
  uint32 offset = 9;          // 偏移量 从0开始
  uint32 limit = 10;          // 每页数量
  string skin_name = 11;      // 皮肤名
  uint32 commodity_filter = 12; // 商品过滤项 1.商品 2.非商品 3.全部
  uint32 is_essential = 13;     // 是否基础物品 0.全部 1.是  2.否
  repeated uint32 resource_type = 14; // 资源类型 see VirtualImageResourceType
  repeated string skin_name_list = 15;      // 皮肤名 批量查询
  repeated string resource_name_list = 16;  // 资源名称 批量查询
  uint32  status = 17;        // 0 全部  其他see VirtualImageResourceStatus
  uint32  is_new = 18;        // 是否新资源 0.全部 1.是  2.否
}

message SearchVirtualImageResourceResponse {
  uint32 offset = 1;       // 偏移量 从0开始
  uint32 limit = 2;        // 每页数量
  repeated VirtualImageResourceInfo resources = 3;
  uint32 total = 4;
}

// 获取全量套装
message GetVirtualImageResourceBySuitRequest {
  uint32 is_new = 1;     // 是否新资源 0.全部 1.是
}

message VirtualImageResourceSuitInfo {
  string suit = 1;      // 套装编码
  repeated VirtualImageResourceInfo resources = 2;
  uint32 is_new = 3;     // 是否新资源 0.全部 1.是
}

message GetVirtualImageResourceBySuitResponse{
  repeated VirtualImageResourceSuitInfo resources = 1;
}

// 根据套装编码列表获取套装信息
message GetSuitResourceListRequest {
    repeated string suit_list = 1; // 套装编码列表
}

message GetSuitResourceListResponse {
    repeated VirtualImageResourceSuitInfo resources = 1;
}

message EditVirtualImageResourceRequest {
  VirtualImageResourceInfo resource = 1;
}

message EditVirtualImageResourceResponse {
}

message GetClientListByPageRequest {
  uint32 offset = 1;          // 偏移量 从0开始
  uint32 limit = 2;           // 每页数量
  uint32 latest_id = 3;       // 最新版本号
}

message GetClientListByPageResponse {
  uint32 offset = 1;       // 偏移量 从0开始
  uint32 limit = 2;        // 每页数量
  repeated VirtualImageResourceInfo resources = 3;
  uint32 total = 4;
  uint32 latest_id = 5;     // 最新版本号
  bool is_end = 6;       // 是否已经结束
  string download_url = 7;  // cdn下载地址
  string download_url_ios = 8;  //废弃
  string download_md5 = 9;  //cdn资源md5
}

message DeleteVirtualImageResourceRequest {
  uint32 id = 1;
}

message DeleteVirtualImageResourceResponse {
}

message GetVirtualImageResourcesByIdsRequest {
  repeated uint32 ids = 1;
}

message GetVirtualImageResourcesByIdsResponse {
  repeated VirtualImageResourceInfo resources = 1;
}

 
message SetVirtualImageResourceForSaleRequest {
  repeated uint32 id = 1;
}

message SetVirtualImageResourceForSaleResponse {
}

message UpdateVirtualImageResourceUrlRequest {
  string skin_name = 1;       // --已弃用
  string resource_url = 2;    // --已弃用
  string encrypt_key = 3;    // 加密key --已弃用
  string md5 = 4;            // md5  --已弃用
  repeated VirtualImageResourceInfo resources = 5;
}

message UpdateVirtualImageResourceUrlResponse {
}


// 一级品类信息
message VirtualImageParentCategoryInfo {
  uint32 category = 1;          // 品类
  string category_name = 2;     // 品类名称
  uint32  category_type = 3 ;   // VirtualImageResourceCategoryType
}
// 二级品类信息
message  VirtualImageSubCategoryInfo {
  uint32 sub_category = 1;          // 品类
  string sub_category_name = 2;     // 子品类名称
  string sub_category_img_url = 3;  // 子品类图片
  uint32 parent_category = 4;       // 父品类
  uint32 sub_category_type = 5;         // VirtualImageResourceSubCategoryType
  string sub_category_img_url_selected = 6; // 子品类选中图片
  string web_sub_category_img_url = 7;  // web端子品类图片
  string web_sub_category_img_url_selected = 8; // web端子品类选中图片

}
// 资源品类信息
message VirtualImageResourceCategoryInfo {
  VirtualImageParentCategoryInfo parent_category_info = 1; // 父品类信息
  repeated VirtualImageSubCategoryInfo sub_category_info_list = 2; // 子品类信息
}

// 获取资源品类列表
message GetVirtualImageResourceCategoryRequest {
}

message GetVirtualImageResourceCategoryResponse {
  repeated VirtualImageResourceCategoryInfo resource_category_info_list = 2; // 资源品类信息
}

message BatchUpdateIconRequest {
  repeated VirtualImageResourceInfo list = 1;
}

message BatchUpdateIconResponse {
}

message GetDefaultResourceListRequest {
 }

message GetDefaultResourceListResponse {
   repeated uint32 male_resources = 1; // 男性默认资源列表
  repeated uint32 female_resources = 2; // 女性资源列表
  repeated uint32 out_fit = 3;   // 全身装替换默认男女上下装id
  map<uint32, uint32>  male_animation_map = 5; // category/sub_category -> resource_id
  map<uint32, uint32>  female_animation_map = 6; // category/sub_category -> resource_id
  repeated uint32 un_show_list = 8;   // 默认男女不展示图标的素体资源 男女都一起
}

message LevelConfig {
  uint32 level = 1;          // 等级
  string level_icon = 2;     // 等级标识
  string level_webp = 3;     // 商品图标特效
  uint32 update_time = 4;    // 更新时间
}

// 获取等级配置
message GetLevelConfigRequest {
}

message GetLevelConfigResponse {
  repeated LevelConfig list = 1;
}

// 更新等级配置
message UpdateLevelConfigRequest {
  LevelConfig cfg = 1;
}

message UpdateLevelConfigResponse {
}

// 增加等级配置
message AddLevelConfigRequest {
  LevelConfig cfg = 1;
}

message AddLevelConfigResponse {
}

// 提醒配置
message NoticeCfg {
  int64 begin_time = 1; // 开始时间, 秒级时间戳
  int64 end_time = 2;   // 结束时间, 秒级时间戳

  string public_content = 3; // 公屏内容， 为空则不展示
  string public_content_color = 4; // 公屏内容颜色
  string public_content_jump_url = 5; // 公屏调整链接

  string float_content = 6; // 浮层文案， 为空则不展示
  uint32 float_content_duration = 7; // 浮层显示时长, 单位秒

  bool has_red_dot = 8; // 是否有红点
}

// 获取提醒配置
message GetNoticeCfgRequest {
}

message GetNoticeCfgResponse {
  NoticeCfg cfg = 1;
}

// 更新提醒配置
message UpdateNoticeCfgRequest {
  NoticeCfg cfg = 1;
}

message UpdateNoticeCfgResponse {
}

// 获取提醒配置缓存
message GetNoticeCfgCacheRequest {
}

message GetNoticeCfgCacheResponse {
  NoticeCfg cfg = 1;
}

message GetActionResourceMapRequest {
}

message GetActionResourceMapResponse {
  map<uint32, uint32> action_resource_map = 1;
}

message ChangeResourceInfo {
  string resource_name = 1; // 资源名称
  string modify_reason = 2; // 修改原因
  string operator = 3; // 修改人
  uint32 resource_id = 4; // 资源ID
}

message SetResourceToTestRequest {
  repeated ChangeResourceInfo change_list = 1; //
}

message SetResourceToTestResponse {
}