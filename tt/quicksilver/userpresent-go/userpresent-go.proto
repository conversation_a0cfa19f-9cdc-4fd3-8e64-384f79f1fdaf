syntax = "proto3";

// namespace
option go_package = "golang.52tt.com/protocol/services/userpresent-go";
package userpresent_go;
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service UserPresentGO {
  // 礼物列表 - 旧版
  rpc GetPresentConfigList(GetPresentConfigListReq) returns(GetPresentConfigListResp){}

  // 增删查改
  rpc AddPresentConfig(AddPresentConfigReq) returns(AddPresentConfigResp){}
  rpc UpdatePresentConfig(UpdatePresentConfigReq) returns(UpdatePresentConfigResp){}
  rpc DelPresentConfig(DelPresentConfigReq) returns(DelPresentConfigResp){}

  // 查询礼物列表 - 新版 - V2旧的userpresent协议用过了
  rpc GetPresentConfigListV3(GetPresentConfigListV3Req) returns(GetPresentConfigListV3Resp){}
  rpc GetPresentConfigById(GetPresentConfigByIdReq) returns(GetPresentConfigByIdResp){}
  rpc GetPresentConfigListByIdList(GetPresentConfigListByIdListReq) returns (GetPresentConfigListByIdListResp){}
  rpc GetPresentConfigUpdateTime(GetPresentConfigUpdateTimeReq) returns(GetPresentConfigUpdateTimeResp){}

  // 获取虚拟直播间主播某时间段收礼流水
  rpc GetLivePresentOrderList(GetLivePresentOrderListReq) returns(GetLivePresentOrderListResp){
  }

  // 场景礼物
  rpc RecordSceneSendPresent(RecordSceneSendPresentReq) returns(RecordSceneSendPresentResp){}
  rpc GetScenePresentSummary(GetScenePresentSummaryReq) returns(GetScenePresentSummaryResp){}
  rpc ClearScenePresent(ClearScenePresentReq) returns(ClearScenePresentResp){}
  rpc GetScenePresentDetailList (GetScenePresentDetailListReq) returns(GetScenePresentDetailListResp){}

  // 冠名礼物
  rpc AddNamingPresentInfo(AddNamingPresentInfoReq) returns(AddNamingPresentInfoResp){}
  rpc UpdateNamingPresentInfo(UpdateNamingPresentInfoReq) returns(UpdateNamingPresentInfoResp){}
  rpc DelNamingPresentInfo(DelNamingPresentInfoReq) returns(DelNamingPresentInfoResp){}
  rpc GetNamingPresentInfoList(GetNamingPresentInfoListReq) returns(GetNamingPresentInfoListResp){}
  rpc GetValidNamingPresentInfos(GetValidNamingPresentInfosReq) returns(GetValidNamingPresentInfosResp){}

  // 送礼
  rpc SendPresent(SendPresentReq) returns(SendPresentResp){}
  rpc BatchSendPresent (BatchSendPresentReq) returns(BatchSendPresentResp){}
  // AI送礼拆分为两步：准备与完成
  rpc PrepareSendPresentToAi(SendPresentToAiReq) returns(SendPresentToAiResp){}
  rpc FinishSendPresentToAi(FinishSendPresentToAiReq) returns(FinishSendPresentToAiResp){}
  // 兼容旧接口
  rpc SendPresentToAi(SendPresentToAiReq) returns(SendPresentToAiResp){}

  // 查询用户送礼
  rpc GetUserPresentDetailList(GetUserPresentDetailListReq) returns(GetUserPresentDetailListResp){}
  rpc GetUserPresentSendDetailList(GetUserPresentSendDetailListReq) returns(GetUserPresentSendDetailListResp){}

  // 礼物汇总
  rpc GetUserPresentSummary (GetUserPresentSummaryReq) returns(GetUserPresentSummaryResp){}
  rpc GetUserPresentSummaryByItemList (GetUserPresentSummaryByItemListReq) returns(GetUserPresentSummaryByItemListResp){}

  // 对账相关
  rpc GetPresentOrderStatus(GetPresentOrderStatusReq) returns(GetPresentOrderStatusResp){}
  rpc GetOrderLogByOrderIds(GetOrderLogByOrderIdsReq) returns(GetOrderLogByOrderIdsResp){}

  // 流光
  rpc GetPresentFlowConfigById(GetPresentFlowConfigByIdReq) returns(GetPresentFlowConfigByIdResp){}
  rpc GetPresentFlowConfigList(GetPresentFlowConfigListReq) returns(GetPresentFlowConfigListResp){}
  rpc GetPresentFlowConfigUpdateTime(GetPresentFlowConfigUpdateTimeReq) returns(GetPresentFlowConfigUpdateTimeResp){}
  rpc AddPresentFlowConfig(AddPresentFlowConfigReq) returns(AddPresentFlowConfigResp){}
  rpc DelPresentFlowConfig(DelPresentFlowConfigReq) returns(DelPresentFlowConfigResp){}
  rpc UpdatePresentFlowConfig(UpdatePresentFlowConfigReq) returns(UpdatePresentFlowConfigResp){}

  // 动效模版
  rpc AddDynamicEffectTemplate(AddDynamicEffectTemplateReq) returns(AddDynamicEffectTemplateResp){}
  rpc UpdateDynamicEffectTemplate(UpdateDynamicEffectTemplateReq) returns(UpdateDynamicEffectTemplateResp){}
  rpc DelDynamicEffectTemplate(DelDynamicEffectTemplateReq) returns(DelDynamicEffectTemplateResp){}
  rpc GetDynamicEffectTemplateList(GetDynamicEffectTemplateListReq) returns(GetDynamicEffectTemplateListResp){}
  rpc AddPresentEffectTemplateConfig(AddPresentEffectTemplateConfigReq) returns(AddPresentEffectTemplateConfigResp){}
  rpc UpdatePresentEffectTemplateConfig(UpdatePresentEffectTemplateConfigReq) returns(UpdatePresentEffectTemplateConfigResp){}
  rpc DelPresentEffectTemplateConfig(DelPresentEffectTemplateConfigReq) returns(DelPresentEffectTemplateConfigResp){}
  rpc GetPresentEffectTemplateConfigList(GetPresentEffectTemplateConfigListReq) returns(GetPresentEffectTemplateConfigListResp){}
  rpc GetPresentDynamicEffectTemplateConfig(GetPresentDynamicEffectTemplateConfigReq) returns(GetPresentDynamicEffectTemplateConfigResp){}
  rpc GetPresentDynamicTemplateConfUpdateTime(GetPresentDynamicTemplateConfUpdateTimeReq) returns(GetPresentDynamicTemplateConfUpdateTimeResp){}
  rpc GetPresentDETConfigById(GetPresentDETConfigByIdReq) returns(GetPresentDETConfigByIdResp){}

  // 娱乐玩法来源
  rpc AddChanceItemSource(AddChanceItemSourceReq) returns (AddChanceItemSourceResp){}
  rpc DelChanceItemSource(DelChanceItemSourceReq) returns (DelChanceItemSourceResp){}

  // 礼物角标
  rpc AddPresentMarkConfig(AddPresentMarkConfigReq) returns (AddPresentMarkConfigResp){}
  rpc BatchAddPresentMarkConfig(BatchAddPresentMarkConfigReq) returns (BatchAddPresentMarkConfigResp){}
  rpc DelPresentMarkConfig(DelPresentMarkConfigReq) returns (DelPresentMarkConfigResp){}
  rpc GetPresentMarkConfigList(GetPresentMarkConfigListReq) returns (GetPresentMarkConfigListResp){}
  rpc GetPresentMarkIconByPresentId(GetPresentMarkIconByPresentIdReq) returns (GetPresentMarkIconByPresentIdResp){}

  // 后台专用
  rpc GetPresentConfigListBackend(GetPresentConfigListBackendReq) returns(GetPresentConfigListBackendResp){}
  rpc GetPresentConfigByIdBackend(GetPresentConfigByIdBackendReq) returns(GetPresentConfigByIdBackendResp){}
  rpc UpdatePresentConfigBackend(UpdatePresentConfigBackendReq) returns(UpdatePresentConfigBackendResp){}
  rpc DelPresentConfigBackend(DelPresentConfigBackendReq) returns(DelPresentConfigBackendResp){}
  rpc AddPresentConfigBackend(AddPresentConfigBackendReq) returns(AddPresentConfigBackendResp){}
  rpc GetUserPresentSend(GetUserPresentSendReq) returns (GetUserPresentSendResp){}
  rpc GetUserPresentReceive(GetUserPresentReceiveReq) returns (GetUserPresentReceiveResp){}
  rpc GetAllFellowPresent(GetAllFellowPresentReq) returns (GetAllFellowPresentResp){}

  // 活动后台
  rpc AddPresentActivityConfig(AddPresentActivityConfigReq) returns (AddPresentActivityConfigResp){}
  rpc UpdatePresentActivityConfig(UpdatePresentActivityConfigReq) returns (UpdatePresentActivityConfigResp){}
  rpc GetPresentActivityConfigList(GetPresentActivityConfigListReq) returns (GetPresentActivityConfigListResp){}
  rpc GetItemLinkedActivityConfig(GetItemLinkedActivityConfigReq) returns (GetItemLinkedActivityConfigResp){}
  rpc LinkItemToActivity(LinkItemToActivityReq) returns (LinkItemToActivityResp){}
  rpc GetItemBoundActivity(GetItemBoundActivityReq) returns (GetItemBoundActivityResp){}
  rpc CheckActivityItemConflict(CheckActivityItemConflictReq) returns (CheckActivityItemConflictResp){}
  // AI相关
  rpc GetAiPresentDetail (GetAiPresentDetailReq) returns (GetAiPresentDetailResp){}

  // T豆消费数据对账
  rpc GetAiPresentTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetAiPresentOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  // 补单
  rpc ReissueAiPresentOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}
}

message StConfigIosExtend
{
  bytes video_effect_url = 1;      // 特效url
}

message StPresentItemConfigExtend
{
  uint32 item_id = 1;
  bytes video_effect_url = 2;      // 特效url
  uint32 show_effect = 3;        // 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE
  bool unshow_batch_option = 4;  // 是否展示批量送礼选项
  bool is_test = 5;          // 该礼物是否是测试 只有白名单用户可以拉取到
  uint32 flow_id = 6;        // 使用指定的流光id(流光配置不存在时，客户端会按默认的流光规则)
  StConfigIosExtend ios_extend = 7;  // ios的扩展信息
  bool notify_all = 8;      // 是否全服通知
  uint32 tag = 9;          // ga::PresentTagType
  bool force_sendable = 10;    // 强制可送的礼物(暂时只有背包礼物，这种礼物即使下架了也可以送出去)
  uint32 nobility_level = 11;     // 使用这个礼物的最低贵族等级
  bool unshow_present_shelf = 12;   //是否在礼物架上显示           true:不在礼物架显示          false:在礼物架显示
  bool show_effect_end = 13; // 是否展示礼物下架时间
  bool effect_end_delay = 14; // 是否支持延长上架时间
  repeated CustomText custom_text = 15;  // 礼物的自定义文案
  string small_vap_url = 16; // 小礼物的vap动效url
  string small_vap_md5 = 17; // 小礼物的vap动效md5
  string mic_effect_url = 18; // 麦位延展特效的动效url
  string mic_effect_md5 = 19; // 麦位延展特效的动效md5
  bool fusion_present = 20; // 是否是融合头像礼物
  bool is_box_breaking = 21; // 是否是需要开盒的全服礼物
  uint32 fans_level = 22; // 送礼所需粉丝团等级，tag = 11(粉丝团专属礼物) 才生效
  string origin_icon_url = 23;  // 原本的礼物icon
  uint64 mark_id = 24; // 礼物角标id
  string mark_name = 25; // 礼物角标名称 更新/插入不用填，获取时会返回
}

message CustomText{
  string key = 1;
  repeated string text = 2;
}

// 礼物配置信息
message StPresentItemConfig
{
  uint32 item_id = 1;
  string name = 2;        // 名称
  string icon_url = 3;      // 图标url
  uint32 price = 4;        // 价格
  uint32 score = 5;        // 收到一个礼物 收礼者 增加的 积分
  uint32 charm = 6;        // 收到一个礼物 收礼者 增加的 魅力值
  uint32 rank = 7;        // 排名
  uint32 effect_begin = 8;    // 上架时间
  uint32 effect_end = 9;      // 下架时间
  uint32 update_time = 10;    // 更新时间
  uint32 create_time = 11;    // 添加时间
  bool   is_del = 12;        // 是否已删除
  uint32 price_type = 13;      // PresentPriceType
  uint32 rich_value = 14;      // 送出一个礼物 送礼者 增加的土豪值
  StPresentItemConfigExtend extend = 15;  // 扩展信息，这部分会整块存到mysql的一个字段里
  float rank_float = 16; // float类型的排序
  string create_user = 17; // 创建人
  string update_user = 18; // 更新人
  uint32 activity_id = 19; // 活动id，0表示不属于活动
}

// 购买礼物的货币
enum PresentPriceType {
  PRESENT_PRICE_UNKNOWN = 0;
  PRESENT_PRICE_RED_DIAMOND = 1;
  PRESENT_PRICE_TBEAN = 2;
}

// 用户的礼物汇总
message StUserPresentSummary
{
  uint32 uid = 1;
  uint32 item_id = 2;
  uint32 count = 3;
}

// 用户的礼物明细
message StUserPresentDetail
{
  uint32 from_uid = 1;
  uint32 target_uid = 2;
  StPresentItemConfig item_config = 3;
  uint32 receive_time = 4;
  uint32 item_count = 5;
  uint32 add_charm = 6; // 增加的魅力值
  string order_id = 7;
  uint32 item_id = 8;
  uint32 score = 9;
  uint32 send_source = 10;
  uint32 send_method = 11; //礼物来源 房间送礼：0，IM送礼：1
  uint32 add_rich = 12; // 增加的财富值
  bool is_ukw = 13;  // 是否是神秘人
  uint32 business_type = 14; // 业务类型，see userpresent_.proto PresentBusinessType
}

// 礼物配置列表类型
enum ConfigListTypeBitMap {
  CONFIG_UNLIMIT = 0;
  CONFIG_NOT_EXPIRED = 1;
  CONFIG_NOT_DELETED = 2;
}

message GetPresentConfigListReq
{
  uint32 type_bitmap = 1;
  uint32 update_time = 2;
}

message GetPresentConfigListResp
{
  repeated StPresentItemConfig item_list = 1;
  uint32 update_time = 2;
}

message GetPresentConfigListV3Req
{
  uint32 type_bitmap = 1;
  uint32 update_time = 2; // 获取增量的礼物信息，0表示获取所有的礼物信息
}

message GetPresentConfigListV3Resp
{
  repeated PresentConfigNew item_list = 1;
  uint32 last_update_time = 2; // 最后更新时间
  repeated PresentEnterBlacklist enter_black_list = 3; // 入口配置列表
}

// 增加礼物配置
message AddPresentConfigReq
{
  string name = 1;          // 名称
  string icon_url = 2;        // 图标url
  uint32 price = 3;          // 价格
  uint32 effect_begin = 4;      // 上架时间
  uint32 effect_end = 5;        // 下架时间
  uint32 rank = 6;          // 排名
  uint32 price_type = 7;        // PresentPriceType
  StPresentItemConfigExtend extend = 8;
  float rank_float = 9; // float类型的排序
  string create_user = 10; // 创建人
  string update_user = 11; // 更新人
  uint32 activity_id = 12; // 活动id，0表示不属于活动
}

message AddPresentConfigResp
{
  StPresentItemConfig item_config = 1;
}

// 删除礼物配置
message DelPresentConfigReq
{
  uint32 item_id = 1;
}

message DelPresentConfigResp
{
}


// 更新礼物配置
message UpdatePresentConfigReq
{
  StPresentItemConfig item_config = 1;
}

message UpdatePresentConfigResp
{
}



message GetPresentConfigByIdReq
{
  uint32 item_id = 1;
}

message GetPresentConfigByIdResp
{
  PresentConfigNew item_config = 1;
}

message GetPresentConfigListByIdListReq
{
  repeated uint32 item_id = 1;
}

message GetPresentConfigListByIdListResp
{
  repeated PresentConfigNew item_config = 1;
}



// 用来模拟旧的resp
message GetPresentConfigByIdOldResp
{
  StPresentItemConfig item_config = 1;
}

message GetPresentConfigByIdListResp
{
  repeated StPresentItemConfig  item_list = 1;
}



// 礼物配置 - 新
message PresentConfigNew{
  PresentBaseConfig base_config = 1;
  PresentEffectConfig effect_config = 2;
  PresentEnterConfig enter_config = 3;
}

// 新版的礼物配置 - 基础配置
message PresentBaseConfig{
  uint32 item_id = 1;
  string name = 2;               // 名称
  string icon_url = 3;           // 图标url
  uint32 price = 4;              // 价格
  bool   is_del = 5;             // 是否已删除
  uint32 price_type = 6;         // PresentPriceType
  uint32 rich_value = 7;         // 送出一个礼物 送礼者 增加的土豪值
  uint32 score = 8;              // 收到一个礼物 收礼者 增加的 积分
  uint32 charm = 9;              // 收到一个礼物 收礼者 增加的 魅力值
  bool force_sendable = 10;      // 强制可送的礼物(暂时只有背包礼物，这种礼物即使下架了也可以送出去)
  uint32 update_time = 11;       // 更新时间
  uint32 create_time = 12;       // 添加时间
  bool is_test = 13;  // 是否是测试礼物
  string create_user = 14; // 创建人
  string update_user = 15; // 更新人
}


// 新版的礼物配置 - 入口配置
message PresentEnterConfig{
  uint32 item_id = 1;
  bool unshow_batch_option = 2;  // 是否展示批量送礼选项
  float  rank = 3;               // 礼物架排名
  uint32 effect_begin = 4;       // 上架时间
  uint32 effect_end = 5;         // 下架时间
  uint32 nobility_level = 6;     // 使用这个礼物的最低贵族等级
  bool unshow_present_shelf = 7; // 是否在礼物架上显示 true:不在礼物架显示 false:在礼物架显示
  bool show_effect_end = 8;      // 是否展示礼物下架时间
  bool effect_end_delay = 9;     // 是否支持延长上架时间
  uint32 update_time = 10;       // 更新时间
  uint32 tag = 11;                    // ga::PresentTagType
  uint32 fans_level = 12;             // 使用这个礼物的最低粉丝等级
}


// 新版的礼物配置 - 特效配置
message PresentEffectConfig{
  uint32 item_id = 1;
  bytes video_effect_url = 2;        // 特效url
  uint32 show_effect = 3;            // 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE
  uint32 flow_id = 4;                // 使用指定的流光id(流光配置不存在时，客户端会按默认的流光规则)
  bool notify_all = 5;               // 是否全服通知
  repeated CustomText custom_text = 6; // 礼物的自定义文案
  string small_vap_url = 7;          // 小礼物的vap动效url
  string small_vap_md5 = 8;          // 小礼物的vap动效md5
  string mic_effect_url = 9;        // 麦位延展特效的动效url
  string mic_effect_md5 = 10;        // 麦位延展特效的动效md5
  uint32 update_time = 11;       // 更新时间
  bool is_box_breaking = 12; // 是否是需要开盒的全服礼物
  bool fusion_present = 13;  // 是否融合头像礼物
  bytes ios_video_effect_url = 14;        // 特效url
}

enum PresentEnterType
{
  PresentEnterTypeUnknown = 0;
  PresentEnterTypeShelf = 1; // 礼物架 （挚友/贵族等礼物都包含在这部分）
  PresentEnterTypeLottery = 2; // 抽奖
  PresentEnterTypeWishList = 3; // 心愿单
}

message PresentEnterBlacklist
{
  PresentEnterType enter_type = 1;
  repeated uint32 gift_item_list = 2;
}

message GetLivePresentOrderListReq{
  uint32 to_uid = 1;
  uint32 channel_id = 2;
  uint32 begin_time = 3;
  uint32 end_time = 4;
}

message GetLivePresentOrderListResp{
  repeated LivePresentOrder order_list = 1;
}

message LivePresentOrder{
  uint32 from_uid = 1;
  uint32 item_id = 2;
  uint32 count = 3;
  uint32 to_uid = 4;
  uint32 create_time = 5;
}

// 礼物绑定类型
enum PresentSceneType
{
  UNKNOWN_PRESENT = 0;
  CHANNEL_PRESENT = 1;  // 在房间送出的礼物
  GUILD_PRESENT = 2;    // 在公会送出的礼物（只算公会房间送出的）
  USER_PRESENT = 3;    // 在个人名片送出的礼物
}

message StSceneInfo
{
  uint32 scene_id = 1;
  uint32 scene_type = 2;  // PresentSceneType
  uint32 sub_scene_id = 3;
}

message StScenePresentSummary
{
  uint32 scene_id = 1;
  uint32 scene_type = 2;  // PresentSceneType
  uint32 item_id = 3;
  uint32 count = 4;
  uint32 sub_scene_id = 5;
  uint64 count64 = 6;
}

message StScenePresentDetail
{
  uint32 scene_id = 1;
  uint32 scene_type = 2;  // PresentSceneType
  uint32 from_uid = 3;
  uint32 to_uid = 4;
  StPresentItemConfig item_config = 5;
  uint32 count = 6;
  string order_id = 7;
  uint32 create_time = 8;
  uint64 count64 = 9;
}

// 记录场景中送出的礼物
message RecordSceneSendPresentReq
{
  uint32 uid = 1;
  uint32 target_uid = 2;
  string order_id = 3;
  StPresentItemConfig item_config = 4;
  repeated StSceneInfo scene_list = 5;
  uint32 item_count = 6;
  uint32 send_time = 7;
}

// 获取场景的礼物汇总
message GetScenePresentSummaryReq
{
  StSceneInfo scene_info = 1;
}

message GetScenePresentSummaryResp
{
  repeated StScenePresentSummary summary_list = 1;
  uint32 total_value = 2;  // 送出的礼物总值
  uint32 total_count = 3;  // 送出的礼物总数
  uint64 total_count64 = 4;
  uint64 total_value64 = 5;
}

// 通知清空场景的礼物信息
message ClearScenePresentReq
{
  StSceneInfo scene_info = 1;
}

// 获取场景的礼物明细
message GetScenePresentDetailListReq
{
  StSceneInfo scene_info = 1;
  uint32 begin_time = 2;
  uint32 end_time = 3;
  uint32 offset = 4;
  uint32 limit = 5;
}

message GetScenePresentDetailListResp
{
  repeated StScenePresentDetail detail_list = 1;
}

message RecordSceneSendPresentResp{
}

message ClearScenePresentResp{
}

/***********礼物冠名************/

message NamingPresentInfo
{
  uint32 id = 1;
  uint32 uid = 2;
  uint32 gift_id = 3;
  string naming_content = 4;
  uint32 begin_ts = 5;
  uint32 end_ts = 6;
  string account = 7;
}

message AddNamingPresentInfoReq
{
  NamingPresentInfo info = 1;
}
message AddNamingPresentInfoResp
{
}

message UpdateNamingPresentInfoReq
{
  NamingPresentInfo info = 1;
}
message UpdateNamingPresentInfoResp
{
}

message DelNamingPresentInfoReq
{
  uint32 id = 1;
}
message DelNamingPresentInfoResp
{
}

message GetNamingPresentInfoListReq
{
  uint32 offset = 1;
  uint32 limit = 2;
  uint32 uid = 3;
  uint32 gift_id = 4;
  uint32 begin_ts = 5;
  uint32 end_ts = 6;
}
message GetNamingPresentInfoListResp
{
  repeated NamingPresentInfo info_list = 1;
  uint32 next_offset = 2;
  uint32 total_cnt = 3;
}

//获取所有生效的礼物冠名信息
message GetValidNamingPresentInfosReq
{
}
message GetValidNamingPresentInfosResp
{
  repeated NamingPresentInfo info_list = 1;
}


// 赠送礼物
message SendPresentReq
{
  uint32 uid = 1;
  uint32 target_uid = 2;
  string order_id = 3;
  uint32 item_id = 4;
  uint32 channel_id = 5;
  uint32 guild_id = 6;
  uint32 item_count = 7;
  uint32 add_charm = 8;  // 增加的魅力值
  uint32 send_time = 9;
  StPresentItemConfig item_config = 10;
  bool opt_invalid = 11;
  bool async_flag = 12;  // 是否在礼物服务处理积分、金钻之类的
  string user_from_ip = 13;  // 送礼者的ip
  uint32 channel_type = 14;  //与 ga::ChannelType 的类型保持一致
  uint32 item_source = 15;  // ga::PresentSourceType
  string channel_name = 16;
  uint32 channel_display_id = 17;  // 用于oss上报
  uint32 send_source = 18;        // 赠送来源，用于 oss 上报
  uint32 send_platform = 19;       // 送礼人的平台类型，用于 oss 上报
  uint32 batch_type = 20;             // 0，单个送礼,1,全麦送礼，oss 上报
  uint32 app_id = 21;
  uint32 market_id = 22;
  uint32 receiver_guild_id = 23;
  uint32 giver_guild_id = 24;
  uint32 add_rich = 25;
  uint32 send_method = 26;  //送礼方法 0，房间送礼；1，IM送礼
  uint32 bind_channel_id = 27;  // 绑定的房间id
  string deal_token = 28; //送礼调用链校验
  string from_ukw_account = 29;  // 送礼神秘人账号，为空则不是神秘人
  string from_ukw_nickname = 30;  // 送礼神秘人昵称
  string to_ukw_account = 31;  // 收礼神秘人账号，为空则不是神秘人
  string to_ukw_nickname = 32;  // 收礼神秘人昵称
  uint32 channel_game_id = 33; //  房间弹幕游戏id
  bool is_virtual_live = 34; // 用户是否属于虚拟主播
  uint32 score_type = 35; // 积分类型， 0 普通积分，1 可提现积分
  uint32 send_channel_id = 36; // 送礼的房间id
  uint32 business_type = 37; // 业务类型，see userpresent_.proto PresentBusinessType
}

message SendPresentResp
{
}

// 给AI赠送礼物
message SendPresentToAiReq
{
  uint32 uid = 1;
  uint32 target_role_id = 2;
  uint32 target_partner_id = 3;
  string order_id = 4;
  uint32 item_id = 5;
  uint32 channel_id = 6;
  uint32 guild_id = 7;
  uint32 item_count = 8;
  uint32 send_time = 9;
  string user_from_ip = 10;  // 送礼者的ip
  uint32 channel_type = 11;  //与 ga::ChannelType 的类型保持一致
  uint32 item_source = 12;  // ga::PresentSourceType
  string channel_name = 13;
  uint32 channel_display_id = 14;  // 用于oss上报
  uint32 send_source = 15;        // 赠送来源，用于 oss 上报
  uint32 send_platform = 16;       // 送礼人的平台类型，用于 oss 上报
  uint32 batch_type = 17;             // 0，单个送礼,1,全麦送礼，oss 上报
  uint32 app_id = 18;
  uint32 market_id = 19;
  uint32 giver_guild_id = 20;
  uint32 add_rich = 21;
  uint32 send_method = 22;  //送礼方法 0，房间送礼；1，IM送礼
  uint32 bind_channel_id = 23;  // 绑定的房间id
  string deal_token = 24; //送礼调用链校验
  uint32 send_channel_id = 25; // 送礼的房间id
  uint32 business_type = 26; // 业务类型，see userpresent_.proto PresentBusinessType
}

message SendPresentToAiResp {

}

// 完成AI送礼，更新dealtoken与sendtime
message FinishSendPresentToAiReq {
  string order_id = 1;  // 订单ID，用于关联准备阶段的记录
  string deal_token = 2; // 交易校验token
  uint32 send_time = 3;  // 实际发送时间（秒）
}

message FinishSendPresentToAiResp {
}
// 获取用户收到的礼物明细
message GetUserPresentDetailListReq
{
  uint32 uid = 1;
}

message GetUserPresentDetailListResp
{
  repeated StUserPresentDetail detail_list = 1;
}

// 获取用户送出的礼物明细
message GetUserPresentSendDetailListReq
{
  uint32 uid = 1;
}

message GetUserPresentSendDetailListResp
{
  repeated StUserPresentDetail detail_list = 1;
}

// 获取用户的礼物数量总览
message GetUserPresentSummaryReq
{
  uint32 uid = 1;
  bool is_send = 2;  // 0.收到的礼物 1.送出的礼物
}

message GetUserPresentSummaryResp
{
  repeated StUserPresentSummary summary_list = 1;
  uint32 total_value = 2;
  uint32 total_count = 3;
}

message GetUserPresentSummaryByItemListReq{
  uint32 uid = 1;
  bool is_send = 2;  // 0.收到的礼物 1.送出的礼物
  repeated uint32 item_list = 3;
}

message GetUserPresentSummaryByItemListResp{
  repeated StUserPresentSummary summary_list = 1;
}

// 获取礼物配置的更新时间
message GetPresentConfigUpdateTimeReq
{
}

message GetPresentConfigUpdateTimeResp
{
  uint32 update_time = 1;
}

// 获取礼物订单的状态
message GetPresentOrderStatusReq
{
  uint32 uid = 1;
  string order_id = 2;
}

message GetPresentOrderStatusResp
{
  uint32 order_status = 1;  // 0.订单不存在
}

/***********非全屏礼物动效模板配置************/

//动效模板配置
message DynamicEffectTemplate
{
  uint32 id = 1;
  string name = 2;
  string icon = 3;     //模板效果图片
  string url = 4;      //模板资源
  string md5 = 5;
  string op_account = 6;
  uint32 update_ts = 7;
  string bg_color = 8;
}

message AddDynamicEffectTemplateReq
{
  DynamicEffectTemplate template_info = 1;
}
message AddDynamicEffectTemplateResp
{
}

message UpdateDynamicEffectTemplateReq
{
  DynamicEffectTemplate template_info = 1;
}
message UpdateDynamicEffectTemplateResp
{
}

message DelDynamicEffectTemplateReq
{
  uint32 id = 1;
}
message DelDynamicEffectTemplateResp
{
}

message GetDynamicEffectTemplateListReq
{
  string name = 1;
  uint32 offset = 2;
  uint32 limit = 3;
}
message GetDynamicEffectTemplateListResp
{
  repeated DynamicEffectTemplate info_list = 1;
  uint32 total_cnt = 2;
  uint32 next_offset = 3;
}


// 礼物动效模板配置
message PresentEffectTemplateConfig
{
  uint32 id = 1;
  uint32 present_id = 2;
  uint32 present_cnt = 3;
  uint32 template_id = 4;
  string op_account = 5;
  uint32 update_ts = 6;
}

message AddPresentEffectTemplateConfigReq
{
  PresentEffectTemplateConfig info = 1;
}
message AddPresentEffectTemplateConfigResp
{
}

message UpdatePresentEffectTemplateConfigReq
{
  PresentEffectTemplateConfig info = 1;
}
message UpdatePresentEffectTemplateConfigResp
{
}

message DelPresentEffectTemplateConfigReq
{
  uint32 id = 1;
}
message DelPresentEffectTemplateConfigResp
{
}

message GetPresentEffectTemplateConfigListReq
{
  uint32 present_id = 1;
  uint32 template_id = 2;
  uint32 offset = 3;
  uint32 limit = 4;
}
message GetPresentEffectTemplateConfigListResp
{
  repeated PresentEffectTemplateConfig config_list = 1;
  uint32 total_cnt = 2;
  uint32 next_offset = 3;
}

// 获取动效模板配置和礼物动效配置
message GetPresentDynamicEffectTemplateConfigReq
{
}
message GetPresentDynamicEffectTemplateConfigResp
{
  repeated DynamicEffectTemplate template_list = 1;
  repeated PresentEffectTemplateConfig present_effect_list = 2;
  uint32 info_version = 3;
}

// 获取非全屏礼物动效模板配置的更新时间
message GetPresentDynamicTemplateConfUpdateTimeReq
{
}
message GetPresentDynamicTemplateConfUpdateTimeResp
{
  uint32 update_ts = 1;
}

// 获取指定礼物动效配置
message GetPresentDETConfigByIdReq
{
  uint32 present_id = 1;
}
message GetPresentDETConfigByIdResp
{
  repeated PresentEffectTemplateConfig present_effect_list = 1;
}


// 获取礼物流光配置
message GetPresentFlowConfigByIdReq
{
  uint32 flow_id = 1;
}

message GetPresentFlowConfigByIdResp
{
  StPresentFlowConfig flow_config = 1;
}

// 获取礼物流光配置
message GetPresentFlowConfigListReq
{
}

message GetPresentFlowConfigListResp
{
  repeated StPresentFlowConfig flow_list = 1;
}

// 获取礼物流光配置的更新时间
message GetPresentFlowConfigUpdateTimeReq
{
}

message GetPresentFlowConfigUpdateTimeResp
{
  uint32 update_time = 1;
}

// 增加礼物流光配置
message AddPresentFlowConfigReq
{
  string flow_url = 1;
  string flow_md5 = 2;
  string flow_desc = 3;
}

// 删除礼物流光配置
message DelPresentFlowConfigReq
{
  uint32 flow_id = 1;
}

message UpdatePresentFlowConfigReq
{
  uint32 flow_id = 1;
  string flow_url = 2;
  string flow_md5 = 3;
  string flow_desc = 4;
}

message AddPresentFlowConfigResp
{
}

message DelPresentFlowConfigResp
{
}

message UpdatePresentFlowConfigResp
{
}

// 礼物流光配置
message StPresentFlowConfig
{
  uint32 flow_id = 1;
  string url = 2;
  string md5 = 3;
  uint32 update_time = 4;
  uint32 create_time = 5;
  string desc = 6;  // 流光描述
}

message AddChanceItemSourceReq{
  uint32 source_type = 1;
  uint32 play_type = 2;
}

message AddChanceItemSourceResp{
}

message DelChanceItemSourceReq{
  uint32 source_type = 1;
}

message DelChanceItemSourceResp{
}

message GetOrderLogByOrderIdsReq{
  repeated string order_ids = 1;
}

message GetOrderLogByOrderIdsResp {
  repeated StUserPresentOrderLog order_log_list = 1;
}

message StUserPresentOrderLog
{
  uint32 from_uid = 1;
  uint32 target_uid = 2;
  uint32 change_score = 3;
  string order_id = 4;
  uint32 create_time = 5;
  string deal_token = 6; //校验信息
  uint32 score_type = 7; //积分类型
}

message BatchSendPresentReq{
  repeated SendPresentItem present_list = 1;
}

message BatchSendPresentResp{
}

message SendPresentItem {
  uint32 uid = 1;
  uint32 target_uid = 2;
  string order_id = 3;
  uint32 item_id = 4;
  uint32 channel_id = 5;
  uint32 guild_id = 6;
  uint32 item_count = 7;
  uint32 add_charm = 8;  // 增加的魅力值
  uint32 send_time = 9;
  StPresentItemConfig item_config = 10;
  bool opt_invalid = 11;
  bool async_flag = 12;  // 是否在礼物服务处理积分、金钻之类的
  string user_from_ip = 13;  // 送礼者的ip
  uint32 channel_type = 14;  //与 ga::ChannelType 的类型保持一致
  uint32 item_source = 15;  // ga::PresentSourceType
  string channel_name = 16;
  uint32 channel_display_id = 17;  // 用于oss上报
  uint32 send_source = 18;        // 赠送来源，用于 oss 上报
  uint32 send_platform = 19;       // 送礼人的平台类型，用于 oss 上报
  uint32 batch_type = 20;             // 0，单个送礼,1,全麦送礼，oss 上报
  uint32 app_id = 21;
  uint32 market_id = 22;
  uint32 receiver_guild_id = 23;
  uint32 giver_guild_id = 24;
  uint32 add_rich = 25;
  uint32 send_method = 26;  //送礼方法 0，房间送礼；1，IM送礼
  uint32 bind_channel_id = 27;  // 绑定的房间id
  string deal_token = 28; //送礼调用链校验
  string from_ukw_account = 29;  // 送礼神秘人账号，为空则不是神秘人
  string from_ukw_nickname = 30;  // 送礼神秘人昵称
  string to_ukw_account = 31;  // 收礼神秘人账号，为空则不是神秘人
  string to_ukw_nickname = 32;  // 收礼神秘人昵称
  uint32 channel_game_id = 33; //  房间弹幕游戏id
  bool is_virtual_live = 34; // 用户是否属于虚拟主播
  uint32 score_type = 35; // 积分类型， 0 普通积分，1 可提现积分
  uint32 business_type = 36; // 业务类型，see userpresent_.proto PresentBusinessType
}

message AddPresentMarkConfigReq{
  string name = 1;
  string mark_url = 2;
}

message AddPresentMarkConfigResp{
}

message BatchAddPresentMarkConfigReq{
  repeated PresentMarkConfigItem mark_list = 1;
}

message PresentMarkConfigItem{
  string name = 1;
  string mark_url = 2;
}

message BatchAddPresentMarkConfigResp{
}

message GetPresentMarkConfigListReq{
}

message GetPresentMarkConfigListResp{
  repeated StPresentMarkConfig mark_list = 1;
}

message DelPresentMarkConfigReq{
  uint32 id = 1;
}

message DelPresentMarkConfigResp{
}

message GetPresentMarkIconByPresentIdReq{
  uint32 item_id = 1;
}

message GetPresentMarkIconByPresentIdResp{
  string origin_icon_url = 1;
  StPresentMarkConfig mark_config = 2;
}

message StPresentMarkConfig{
  uint32 id = 1;
  string name = 2;
  string mark_url = 3;
  uint64 update_time = 4;
}

message FellowPresentConfig {
  string unique_background_url = 1; // 唯一关系背景
  uint32 unique_source_type = 2; // 唯一关系背景的资源类型，FellowSourceType
  string unique_md5 = 3; // 唯一关系背景的md5
  string multi_background_url = 4; // 非唯一关系背景
  uint32 multi_source_type = 5; // 非唯一关系背景的资源类型，FellowSourceType
  string multi_md5 = 6; // 非唯一关系背景的md5
  string unique_background_img = 7; // 唯一关系动态背景的底图
  string multi_background_img = 8; // 非唯一关系动态背景的底图
  string source_zip = 9; // 资源包url
}


// 根据礼物id获取礼物配置
message GetPresentConfigByIdBackendReq
{
  uint32 item_id = 1;
}
message GetPresentConfigByIdBackendResp
{
  StPresentItemConfigBackend item_config = 1;
  uint32 testing_present_id = 2; // 如果导入了测试环境资源，测试环境的礼物id
}

message GetPresentConfigListBackendReq
{
  uint32 type_bitmap = 1;
  bool get_all = 2; // 是否获取所有礼物配置
}
message GetPresentConfigListBackendResp
{
  repeated StPresentItemConfigBackend item_list = 1;
}

// 增加礼物配置
message AddPresentConfigBackendReq
{
  string name = 1;          // 名称
  string icon_url = 2;        // 图标url
  uint32 price = 3;          // 价格
  uint32 effect_begin = 4;      // 上架时间
  uint32 effect_end = 5;        // 下架时间
  uint32 rank = 6;          // 排名
  uint32 price_type = 7;        // PresentPriceType
  StPresentItemConfigExtendBackend extend = 8;
  FellowPresentConfig fellow = 9;  // 挚友相关信息，当extend.tag = 5 时才需要填
  repeated EffectDelayLevelInfo delay_info = 10; // 延迟下架相关参数， effect_end_delay = true才填
  float rank_float = 11;  // float的排序，新版用
  uint32 testing_present_id = 12; // 如果导入了测试环境资源，测试环境的礼物id
  string create_user = 13; // 创建人
  string update_user = 14; // 更新人
  uint32 activity_id = 15; // 活动id
}

message EffectDelayLevelInfo{
  uint32 level = 1;  // 延迟等级
  uint32 send_count = 2;  // 升级所需数量
  uint32 day_count = 3;  // 可以延长的天数（0为无限）
  uint32 expire_day_count = 4; // 如果是无限期的等级，几天不送之后会自动下线
  uint32 notice_day_count = 5; // 离过期几天会提醒
}

message AddPresentConfigBackendResp
{
  StPresentItemConfigBackend item_config = 1;
}

// 删除礼物配置
message DelPresentConfigBackendReq
{
  uint32 item_id = 1;
}
message DelPresentConfigBackendResp
{
}

// 更新礼物配置
message UpdatePresentConfigBackendReq
{
  StPresentItemConfigBackend item_config = 1;
  uint32 testing_present_id = 2; // 如果导入了测试环境资源，测试环境的礼物id
}
message UpdatePresentConfigBackendResp
{
}

message UserPresentSend{
  uint32 to_uid = 1;
  uint32 to_ttid = 2;
  string to_nickname = 3;
  uint32 item_id = 4;
  string item_name = 5;
  uint32 item_count = 6;
  uint32 add_rich = 7;
  uint32 send_time = 8;
}

message UserPresentReceive{
  uint32 from_uid = 1;
  uint32 from_ttid = 2;
  string from_nickname = 3;
  uint32 item_id = 4;
  string item_name = 5;
  uint32 item_count = 6;
  uint32 add_charm = 7;
  uint32 add_score = 8;
  uint32 receive_time = 9;
}

// 用户送礼查询
message GetUserPresentSendReq
{
  uint32 uid = 1;
  uint32 begin_ts = 2;
  uint32 end_ts = 3;
}

message GetUserPresentSendResp
{
  repeated UserPresentSend present_send_detail = 1;
}


// 用户送礼查询
message GetUserPresentReceiveReq
{
  uint32 uid = 1;
  uint32 begin_ts = 2;
  uint32 end_ts = 3;
}

message GetUserPresentReceiveResp
{
  repeated UserPresentReceive present_receive_detail = 1;
}


// 用户送礼查询
message GetAllFellowPresentReq
{
}

message GetAllFellowPresentResp
{
  repeated StPresentItemConfigBackend fellow_present_list = 1;
}


// 礼物配置信息
message StPresentItemConfigBackend
{
  uint32 item_id = 1;
  string name = 2;        // 名称
  string icon_url = 3;      // 图标url
  uint32 price = 4;        // 价格
  uint32 score = 5;        // 收到一个礼物 收礼者 增加的 积分
  uint32 charm = 6;        // 收到一个礼物 收礼者 增加的 魅力值
  uint32 rank = 7;        // 排名
  uint32 effect_begin = 8;    // 上架时间
  uint32 effect_end = 9;      // 下架时间
  uint32 update_time = 10;    // 更新时间
  uint32 create_time = 11;    // 添加时间
  bool   is_del = 12;        // 是否已删除
  uint32 price_type = 13;      // PresentPriceType
  uint32 rich_value = 14;      // 送出一个礼物 送礼者 增加的土豪值
  StPresentItemConfigExtendBackend extend = 15;  // 扩展信息，这部分会整块存到mysql的一个字段里
  FellowPresentConfig fellow = 16;  // 挚友相关信息，当extend.tag = 5 时才需要填
  repeated EffectDelayLevelInfo delay_info = 17; // 延迟下架相关参数， effect_end_delay
  bool is_banned = 18; // 是否强制下架
  float rank_float = 19;        // float的排序，新版用
  uint32 activity_id = 20; // 活动id
  string create_user = 21; // 创建人
  string update_user = 22; // 更新人
}

message StPresentItemConfigExtendBackend
{
  uint32 item_id = 1;
  bytes video_effect_url = 2;      // 特效url
  uint32 show_effect = 3;        // 播放特效 ga::PRESENT_SHOW_EFFECT_TYPE
  bool unshow_batch_option = 4;  // 是否展示批量送礼选项
  bool is_test = 5;          // 该礼物是否是测试 只有白名单用户可以拉取到
  uint32 flow_id = 6;        // 使用指定的流光id(流光配置不存在时，客户端会按默认的流光规则)
  StConfigIosExtend ios_extend = 7;  // ios的扩展信息
  bool notify_all = 8;      // 是否全服通知
  uint32 tag = 9;          // ga::PresentTagType
  bool force_sendable = 10;    // 强制可送的礼物(暂时只有背包礼物，这种礼物即使下架了也可以送出去)
  uint32 nobility_level = 11;     // 使用这个礼物的最低贵族等级
  bool unshow_present_shelf = 12;   //是否在礼物架上显示           true:不在礼物架显示          false:在礼物架显示
  bool show_effect_end = 13; // 是否显示下架时间
  bool effect_end_delay = 14; // 是否支持延长上架时间
  repeated CustomText custom_text = 15; // 自定义文案
  string mic_effect_url = 16; // 麦位延展特效的动效url
  string mic_effect_md5 = 17; // 麦位延展特效的动效md5
  string small_vap_effect_url = 18; // 小礼物的vap动效url
  string small_vap_effect_md5 = 19; // 小礼物的vap动效md5
  bool fusion_present = 20; // 是否是融合头像礼物
  bool is_box_breaking = 21; // 是否是需要开盒的全服礼物
  uint32 fans_level = 22; // 使用礼物所需粉丝等级
  string origin_icon_url = 23;  // 原本的礼物icon
  uint64 mark_id = 24; // 礼物角标id
  string mark_name = 25; // 礼物角标名称 更新/插入不用填，获取时会返回
}

message AddPresentActivityConfigReq
{
  string name = 1;          // 活动名称
  uint32 effect_begin = 2;      // 上架时间
  uint32 effect_end = 3;        // 下架时间
  repeated LinkItemType link_item_type_list = 4; // 关联礼物类型列表
  string create_user = 5; // 创建人
  string update_user = 6; // 更新人
  uint32 create_time = 7; // 创建时间
  uint32 update_time = 8; // 更新时间
}

enum LinkItemType {
  LINK_ITEM_TYPE_UNKNOWN = 0;
  LINK_ITEM_TYPE_PRESENT = 1; // 礼物（包括普通礼物、粉丝团礼物....）
  LINK_ITEM_TYPE_PACKAGE = 2; // 包裹（通过风控）
  LINK_ITEM_TYPE_MAGIC_SPRITE = 3; // 幸运礼物
  LINK_ITEM_TYPE_EMPEROR_SET = 4; // 帝王套
  LINK_ITEM_TYPE_CONVERSION_PRESENT = 5; // 合成礼物
}

message AddPresentActivityConfigResp
{
  uint32 activity_id = 1; // 活动id
}

message UpdatePresentActivityConfigReq
{
  uint32 activity_id = 1; // 活动id
  string name = 2;          // 活动名称
  uint32 effect_begin = 3;      // 上架时间
  uint32 effect_end = 4;        // 下架时间
  repeated LinkItemType link_item_type_list = 5; // 关联礼物类型列表
  string create_user = 6; // 创建人
  string update_user = 7; // 更新人
  uint32 create_time = 8; // 创建时间
  uint32 update_time = 9; // 更新时间
}

message UpdatePresentActivityConfigResp
{
  uint32 activity_id = 1; // 活动id
}

enum PresentActivityConfigStatus {
  PRESENT_ACTIVITY_CONFIG_STATUS_UNKNOWN = 0;
  PRESENT_ACTIVITY_CONFIG_STATUS_NOT_EFFECT = 1; // 未生效
  PRESENT_ACTIVITY_CONFIG_STATUS_EFFECT = 2; // 生效中
  PRESENT_ACTIVITY_CONFIG_STATUS_EXPIRED = 3; // 已过期
}

message GetPresentActivityConfigListReq
{
  uint32 offset = 1; // 偏移量
  uint32 limit = 2; // 限制数量
  LinkItemType link_item_type = 3; // 关联礼物类型
  PresentActivityConfigStatus status = 4; // 活动状态
  string name = 5; // 活动名称(模糊搜索)
}

message PresentActivityConfigItem {
  uint32 activity_id = 1; // 活动id
  string name = 2; // 活动名称
  uint32 effect_begin = 3; // 上架时间
  uint32 effect_end = 4; // 下架时间
  PresentActivityConfigStatus status = 5; // 活动状态
  repeated LinkItemDetail link_item_detail_list = 6; // 关联礼物详情列表
  string create_user = 7; // 创建人
  string update_user = 8; // 更新人
  uint32 create_time = 9; // 创建时间
  uint32 update_time = 10; // 更新时间
  string linked_user = 11; // 关联操作人
  uint32 linked_time = 12; // 关联时间
  string item_create_user = 13; // 礼物类型创建人
  uint32 item_create_time = 14; // 礼物类型创建时间
  repeated LinkItemType link_item_type_list = 15; // 关联礼物类型列表
}

message LinkItemDetail {
  LinkItemType link_item_type = 1; // 关联礼物类型
  repeated LinkItemPresentConfig link_item_present_list = 2; // 关联礼物列表
  string link_item_name = 3; // 关联礼物类型的具体名称
}

message LinkItemPresentConfig {
  uint32 item_id = 1; // 礼物id
  string name = 2; // 礼物名称
  string icon_url = 3; // 礼物图标url
  uint32 price = 4; // 礼物价格
  uint32 price_type = 5; // 礼物价格类型
  string create_user = 6; // 创建人
  uint32 effect_begin = 7; // 上架时间
  uint32 effect_end = 8; // 下架时间
  uint32 update_time = 9; // 更新时间
  string sub_type_name = 10; // 子类型名称
}

message GetPresentActivityConfigListResp
{
  repeated PresentActivityConfigItem activity_list = 1; // 活动列表
  uint32 total_count = 2; // 总数量
}

message GetItemLinkedActivityConfigReq
{
  uint32 link_item_id = 1; // id, 包裹风控时传风控Id，其他时候都传礼物id
  LinkItemType link_item_type = 2; // 关联礼物类型
}

message GetItemLinkedActivityConfigResp
{
  repeated PresentActivityConfigItem activity_list = 1; // 活动列表
  uint32 total_count = 2; // 总数量
}

message LinkItemToActivityReq
{
  uint32 activity_id = 1; // 活动id
  LinkItemType link_item_type = 2; // 关联礼物类型
  uint32 item_id = 3; // 对应物品id
  string operate_user = 4; // 操作人
  bool for_check = 5; // 在正式确认前，先检查关联行为是否存在冲突
}

message LinkItemToActivityResp
{
}

message GetItemBoundActivityReq
{
  uint32 item_id = 1; // 传最外层的id，例如礼物id，风控id，幸运经历id等
  LinkItemType link_item_type = 2; // 关联礼物类型
}

message GetItemBoundActivityResp
{
  PresentActivityConfigItem activity_config = 1; // 活动配置
}

message AiPresentEvent
{
  uint32 from_uid = 1;
  uint32 target_role_id = 2;
  uint32 target_partner_id = 3;
  uint32 item_id = 4;
  uint32 item_count = 5;
  uint32 total_price = 6;
  bool is_first = 7;
}

message GetAiPresentDetailReq{
  uint32 from_uid = 1;
  uint32 target_role_id = 2;
  uint32 target_partner_id = 3;
}

message GetAiPresentDetailResp{
  repeated AiPresentDetail ai_present_detail_list = 1;
}

message AiPresentDetail{
  uint32 from_uid = 1;
  uint32 target_role_id = 2;
  uint32 target_partner_id = 3;
  uint32 item_id = 4;
  uint32 item_count = 5;
  uint32 total_price = 6;
  uint32 send_time = 7;
  uint32 business_type = 8;
  uint32 add_rich = 9;
}

message CheckActivityItemConflictReq
{
  LinkItemType link_item_type =1 ; // 物品类型
  repeated uint32 linked_item_id = 2; // 要改动的物品id，如新增/编辑的礼物id、风控单个/批量添加时涉及的包裹id、幸运礼物奖池配置涉及的礼物id、新增/编辑合成时要添加的包裹id、新增/修改帝王套时涉及的礼物id
  string operate_user = 3; // 操作人
  uint32 activity_id = 4; // 活动id
}

message CheckActivityItemConflictResp{

}