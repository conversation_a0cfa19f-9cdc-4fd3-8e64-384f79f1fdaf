syntax = "proto3";

package game_fre_server;

option go_package = "golang.52tt.com/protocol/services/game-fre-server";

// 用户线频率服务
service GameFreServer {
  rpc GetFreCountBySource(GetFreCountBySourceReq) returns (GetFreCountBySourceResp) {}
  rpc BatGetFreCountBySources(BatGetFreCountBySourcesReq) returns (BatGetFreCountBySourcesResp) {}
  rpc IncFreCountBySource(IncFreCountBySourceReq) returns (IncFreCountBySourceResp) {}
  rpc BatIncFreCountBySources(BatIncFreCountBySourcesReq) returns (BatIncFreCountBySourcesResp) {}
}

enum FreCountSource {
  FRE_COUNT_SOURCE_UNSPECIFIED = 0;
  FRE_COUNT_SOURCE_GAME_HALL_TEAM_IM_INVITE_ROOM = 1; // 游戏大厅组队列表邀请进房发送次数
  FRE_COUNT_SOURCE_GAME_HALL_SEND_TEAM_MSG = 2; // 游戏大厅发送组队消息当天次数
  FRE_COUNT_SOURCE_GAME_HALL_SEND_CHANNEL_INVITE_ROOM = 3; // 游戏大厅发送进房消息当天次数
  FRE_COUNT_SOURCE_GAME_HALL_JOIN_TEAM_PUSH_DAY_TOTAL = 4; // 游戏大厅发送报名推送当天总次数
  FRE_COUNT_SOURCE_GAME_HALL_JOIN_TEAM_PUSH_INTERVAL = 5; // 游戏大厅发送报名推送- 每次间隔
  FRE_COUNT_SOURCE_GAME_HALL_GLOBAL_SEND_MSG = 6; // 游戏大厅全局发送消息当天次数
  FRE_COUNT_SOURCE_GAME_HALL_NORMAL_MSG_INTERVAL = 7; // 游戏大厅普通消息发送间隔
  FRE_COUNT_SOURCE_GAME_HALL_SCREEN_SHOT_MSG_INTERVAL = 8; // 游戏大厅截图消息发送间隔
  FRE_COUNT_SOURCE_ENTER_ROOM_PUSH_INTERVAL = 9; // 进房推送触发时间间隔
  FRE_COUNT_SOURCE_FRIEND_RETURN_USER_LOGIN = 10; // 好友回流提醒用户当天是否登录
  FRE_COUNT_SOURCE_FAST_PC_FEEDBACK = 11; // 极速PC反馈请求频率
  FRE_COUNT_SOURCE_AIGC_GROUP_LOW_FREQ_SPEAK_REMIND = 12; // AIGC群聊用户低频发言提示当天次数
  FRE_COUNT_SOURCE_AIGC_COMMUNITY_POST_GUIDE_TASK_LIMIT = 13; // aigc社区引导任务当天次数限制
  FRE_COUNT_SOURCE_AIGC_SEND_GIFT_TIP_USER_LIMIT = 14; // aigc送礼提示用户当天次数限制
  FRE_COUNT_SOURCE_AIGC_SEND_GIFT_TIP_ROLE_LIMIT = 15; // aigc送礼提示角色当天次数限制
  FRE_COUNT_SOURCE_FAST_PC_SEND_CERTIFICATION_LIMIT = 16; // 极速PC发放活动认证标识当天次数限制
}

message GetFreCountBySourceReq {
  // see FreCountSource
  uint32 fre_count_source = 1;
  // 特定场景特殊后缀
  string suffix = 2;
  // 获取某用户的频率次数
  uint32 user_id = 3;
}

message GetFreCountBySourceResp {
  uint32 count = 1;
}

message BatGetFreItem {
  // see FreCountSource
  uint32 fre_count_source = 1;
  // 特定场景特殊后缀
  string suffix = 2;
  // 获取某用户的频率次数
  uint32 user_id = 3;
}

message BatGetFreCountBySourcesReq {
  repeated BatGetFreItem items = 1;
}

message BatGetFreCountBySourcesResp {
  // uid_FreCountSource_suffix -> count
  map<string, uint32> res_map = 1;
}

message IncFreCountBySourceReq {
  // see FreCountSource
  uint32 fre_count_source = 1;
  // 特定场景特殊后缀
  string suffix = 2;
  // 过期时间
  uint64 expire_time = 3;
  // 增加某用户的频率次数
  uint32 user_id = 4;
}

message IncFreCountBySourceResp {
}

message BatIncFreCountBySourcesReq {
  repeated BatIncFreItem items = 1;
}

message BatIncFreItem {
  // see FreCountSource
  uint32 fre_count_source = 1;
  // 特定场景特殊后缀
  string suffix = 2;
  // 过期时间
  uint64 expire_time = 3;
  // 增加某用户的频率次数
  uint32 user_id = 4;
}

message BatIncFreCountBySourcesResp {
}