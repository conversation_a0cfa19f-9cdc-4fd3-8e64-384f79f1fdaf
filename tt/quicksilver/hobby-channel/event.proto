syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package hobby_channel.event;

option go_package = "golang.52tt.com/protocol/services/hobby_channel/event";

message HobbyChannelEvent {
  uint32 channel_id = 1;
  uint32 tab_id = 2;
  uint32 creator = 3;
  enum ACTION {
    INVALID = 0;    //占位，没用的
    CREATE = 1;     //发布主题房间
    DISMISS = 2;    //解散主题房间
  }
  repeated BlockOption block_options = 4;    //房间选择的开房标签信息
  bool is_private = 5;                       //true表示私密房间
  ACTION action = 6;
  string name = 7;
  repeated ChannelDisplayType display_type = 8;
  bool want_fresh = 9;                       //true表示优先匹配萌新
  Sex sex = 10;                               //性别
  bool is_change = 11;                         //是否修改
  uint32 release_time = 12;                   //发布时间，推荐侧自用，业务暂时不填
  string release_ip = 13;                     //发布时用户设备IP
  bool show_geo_info = 14;                     //是否显示地理信息
  string tab_title = 15;                       //
  GameLocationItem game_location_item = 16; // 用户的地理位置
}

message GameLocationItem{
  string country = 1;
  string country_code = 2; // 国家代码
  string province =3;
  uint32 province_code = 4;
  string city = 5;
  uint32 city_code = 6;
  double latitude = 7;
  double longitude = 8;
}

enum Sex{
  All = 0;
  Male = 1;
  Female = 2;
}

message BlockOption {
  uint32 block_id = 1;           //块id
  uint32 elem_id = 2;            //块里面的元素id
}

enum ChannelDisplayType {
  DISPLAY_AT_MAIN_PAGE = 0;           //大厅展示
  DISPLAY_AT_DISMISSED = 1;
  DISPLAY_AT_FIND_FRIEND = 2;         //找好友玩。跟随显示
  TEMPORARY = 3;                      //临时房
}
