syntax = "proto3";

package music_topic_channel;

option go_package = "golang.52tt.com/protocol/services/music-topic-channel";
import "music_topic_channel/music-topic-channel-logic_.proto";
import "hobby_channel/hobby-channel_.proto";
import "tt/quicksilver/rcmd-local/common/topic_channel.proto";

service MusicChannel {
  //修改主题房字段
  rpc SetMusicChannelReleaseInfo(SetMusicChannelReleaseInfoReq) returns (SetMusicChannelReleaseInfoResp);
  rpc InitMusicChannelReleaseInfo(InitMusicChannelReleaseInfoReq) returns (InitMusicChannelReleaseInfoResp);

  //解散主题房
  rpc DismissMusicChannel(DismissMusicChannelReq) returns (DismissMusicChannelResp);
  //获取房间列表（兜底推荐）
  rpc GetMusicChannelList(GetMusicChannelListReq) returns (GetMusicChannelListResp);
  //获取房间发布信息
  rpc GetMusicChannelByIds(GetMusicChannelByIdsReq) returns (GetMusicChannelByIdsResp);

  //清除房间展示在tab
  rpc DisappearChannel(DisappearChannelReq) returns (DisappearChannelResp);
  //获取主题房数，以及示例用户
  rpc GetOnlineInfo(GetOnlineInfoReq) returns (GetOnlineInfoResp);
  //切换房间玩法
  rpc SwitchChannelTab(SwitchChannelTabReq) returns (SwitchChannelTabResp);

  //冻结主题房
  rpc FreezeChannel(FreezeChannelReq) returns (FreezeChannelResp);
  //解除冻结主题房
  rpc UnfreezeChannel(UnfreezeChannelReq) returns (UnfreezeChannelResp);
  rpc GetChannelFreezeInfo(GetChannelFreezeInfoReq) returns (GetChannelFreezeInfoResp);

  // 获取指定房间类型人数
  rpc GetChannelRoomUserNumber(GetChannelRoomUserNumberReq) returns (GetChannelRoomUserNumberResp);

  // 创建临时房
  rpc AddTemporaryChannel(AddTemporaryChannelReq) returns (AddTemporaryChannelResp);

  //历史记录保存
  rpc SetExtraHistory(SetExtraHistoryReq) returns (SetExtraHistoryResp);
  rpc GetExtraHistory(GetExtraHistoryReq) returns (GetExtraHistoryResp);

  //获取发布中的房间
  rpc ListPublishingChannelIds(ListPublishingChannelIdsReq) returns (ListPublishingChannelIdsResp);

  //获取发布中的房间
  rpc IsPublishing(IsPublishingReq) returns (IsPublishingResp);

  //获取发布中的房间
  rpc BatchIsPublishing(BatchIsPublishingReq) returns (BatchIsPublishingResp);

  //获取过滤器
  rpc GetMusicChannelFilterV2(GetMusicChannelFilterV2Req) returns (GetMusicChannelFilterV2Resp);

  //获取过滤器
  rpc IsOlderForMusicHomePage(IsOlderForMusicHomePageReq) returns (IsOlderForMusicHomePageResp);

  rpc SearchHighQualityChannels(SearchHighQualityChannelsReq) returns(SearchHighQualityChannelsResp) {}
  rpc UpdateHighQualityChannels(UpdateHighQualityChannelsReq) returns(UpdateHighQualityChannelsResp) {}

  rpc BatchIsHighQualityChannels(BatchIsHighQualityChannelsReq)returns(BatchIsHighQualityChannelsResp){}
  rpc BatchHighQualityChannels(BatchHighQualityChannelsReq)returns(BatchHighQualityChannelsResp){}

  rpc GetRcmdPgcChannel(GetRcmdPgcChannelReq)returns(GetRcmdPgcChannelResp){}
  rpc SetUserSchoolLast(SetUserSchoolLastReq)returns(SetUserSchoolLastResp){}
  rpc GetUserSchoolLast(GetUserSchoolLastReq)returns(GetUserSchoolLastResp){}

  //tt首页获取过滤器
  rpc GetMusicFilterItemByIds(GetMusicFilterItemByIdsReq) returns (GetMusicFilterItemByIdsResp);

  rpc ListHomePageFilterItems(ListHomePageFilterItemsReq) returns (ListHomePageFilterItemsResp);

  //  rpc QueryFilterItems(QueryFilterItemsReq) returns (QueryFilterItemsResp);

  rpc GetFilterIdsByTabId(GetFilterItemsByTabIdReq)returns(GetFilterItemsByTabIdResp);

  rpc BatchFilterIdsByGameCard(BatchFilterIdsByGameCardReq)returns(BatchFilterIdsByGameCardResp);

  //获取view
  rpc ListMusicChannelViews(ListMusicChannelViewsReq) returns (ListMusicChannelViewsResp);

  //获取view pb
  rpc ListMusicChannelViewPbs(ListMusicChannelViewPbsReq) returns (ListMusicChannelViewPbsResp);

  rpc ListMusicChannelViewsForMusic(ListMusicChannelViewsForMusicReq) returns (ListMusicChannelViewsForMusicResp);

  rpc GetTabPublishHotRcmd(GetTabPublishHotRcmdReq) returns (GetTabPublishHotRcmdResp);

  rpc UpsertPublishHotRcmd(UpsertPublishHotRcmdReq) returns (UpsertPublishHotRcmdResp);

  rpc SearchPublishHotRcmd(SearchPublishHotRcmdReq) returns (SearchPublishHotRcmdResp);

  rpc SortPublishHotRcmd(SortPublishHotRcmdReq) returns (SortPublishHotRcmdResp);

  rpc DelPublishHotRcmd(DelPublishHotRcmdReq) returns (DelPublishHotRcmdResp);


  rpc ListMuseSocialCommunityChannels(ListMuseSocialCommunityChannelsReq) returns (ListMuseSocialCommunityChannelsResp);

  rpc GetFilterInfoByFilterIds(GetFilterInfoByFilterIdsReq) returns (GetFilterInfoByFilterIdsResp);

  //  rpc BatchSetResourceConfig(BatchSetResourceConfigReq)returns(BatchSetResourceConfigResp){}  /*批量新增资源位*/
  //  rpc GetResourceConfigs(GetResourceConfigsReq)returns(GetResourceConfigsResp){}                    /*获取资源位信息*/
  //  rpc BatchDelResourceConfig(BatchDelResourceConfigReq)returns(BatchDelResourceConfigResp){}     /*批量删除资源位信息*/

  // 获取音乐垂直列表强插的房间id
  rpc GetFilterInsertChannelIds(GetFilterInsertChannelIdsReq) returns (GetFilterInsertChannelIdsResp);
  // 音乐垂直列表FilterID映射给推荐的TabId Block Elem信息
  rpc FilterIdToConfTab(FilterIdToConfTabReq) returns (FilterIdToConfTabResp);
}

message DelPublishHotRcmdReq{
  repeated string ids = 1;
}

message DelPublishHotRcmdResp{

}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SortPublishHotRcmdReq{
  uint32 tabId = 1;
  repeated string ids = 2;
}

message SortPublishHotRcmdResp{

}

message SearchPublishHotRcmdReq{
  string id = 1;
  string offset_id = 2;//下一页要把上一页最后的id带上
  uint32 tab_id = 3;
  repeated Block blocks = 4;
  uint32 count = 5;//数量，不带默认20
  HotRcmdStatus status = 6;
}

message SearchPublishHotRcmdResp{
  repeated TabPublishHotRcmd items = 1;
}

message UpsertPublishHotRcmdReq{
  repeated TabPublishHotRcmd items = 1;
}

message UpsertPublishHotRcmdResp{

}

message TabPublishHotRcmd{
  string id = 1;//id有值为修改，没有为插入
  string name = 2;
  string hint = 3;
  uint32 tab_id = 4;
  repeated Block blocks = 5;
  string note = 6;//备注
  int64 create_at = 7;//秒，时间戳，增删入参不需要传
  int64 update_at = 8;//秒，时间戳，增删入参不需要传
  HotRcmdStatus status = 9;
}

enum HotRcmdStatus {
  undefined = 0;
  in_use = 1;
  in_pause = 2;
}

message GetTabPublishHotRcmdReq{
  uint32 tab_id = 1;
}

message GetTabPublishHotRcmdResp{
  repeated TabPublishHotRcmd items = 1;
}

message ListMusicChannelViewPbsReq{
  repeated uint32 channel_ids = 1;
}

message ListMusicChannelViewPbsResp{
  map<uint32, bytes>  channel_views = 1;
}

message ListMusicChannelViewsForMusicReq{
  repeated uint32 channel_ids = 1;
  bool is_collect = 2;//收藏需要返回不是发布中的房间
}

message ListMusicChannelViewsForMusicResp{
  map<uint32, ga.hobby_channel.ListHobbyChannelResp.HobbyChannelItem> items = 1;
}

message BatchHighQualityChannelsReq{
  map<uint32, uint32> tab_channel_map = 1;//key:channelId,val:tabId
}

message BatchHighQualityChannelsResp{
  map<uint32, QualityType> channel_quality_map = 1;//key:channelId,val:qualityType
}

enum QualityType{
  High_Quality = 0;
  Quality_Hot = 1;
}

message GetFilterItemsByTabIdReq{
  uint32 tab_id = 1;
}

message GetFilterItemsByTabIdResp{
  repeated string filter_ids = 1;
}

message BatchFilterIdsByGameCardReq{
  repeated string game_card = 1;
}

message BatchFilterIdsByGameCardResp{
  map<string, Filters> game_filters = 1;
  message Filters {
    repeated string filter_ids = 1;
  }
}

message QueryFilterItemsReq{
  string id = 1;
  string filter_id = 2;
  string name = 3;
}

message QueryFilterItemsResp{
  repeated FilterItem items = 1;
}

message FilterItem{
  string id = 1;
  string filter_id = 2;
  string name = 3;
  string parent_filter_id = 4;
  repeated Tab tabs = 5;

  //仅仅首页的版本区分这么走
  repeated uint32  ios_version = 6;
  repeated uint32 android_version = 7;
  repeated uint32 appids = 8;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message Tab{
  uint32 tabId = 1;
  repeated Block blocks = 2;
}

message Block  {
  uint32 block_id = 1;
  uint32 element_id = 2;
}

message GetMusicFilterItemByIdsReq{
  repeated string filter_ids = 1;
}

message GetMusicFilterItemByIdsResp{
  map<string, MusicFilterItem> filter_map = 1;//key:filterId
}

message MusicFilterItem{
  string filter_id = 1;
  string name = 2;
  string icon = 3;//图标
  repeated string images = 4;//一般没值，live返回2张图片，第一张为未选中状态，第二张为选中状态
  repeated MusicFilterItem sub_filters = 5;
  uint32 filter_attr_type = 6; // 标签属性  FilterAttrType
  SameCityTitle city_title = 7; // 标签是同城属性则使用此结构体信息替换title
}

message RcmdInfo {
  map<uint32, rcmd.common.ChannelInfo> channel_info_map = 1;
}

message ListMusicChannelViewsReq{
  repeated uint32 channel_ids = 1;
  bool is_all = 2;//没发布的也返回
  RcmdInfo rcmd_info = 3;
}

message ListMusicChannelViewsResp{
  map<uint32, ga.music_topic_channel.MusicChannel>  channel_views = 1;
}

//message MusicChannelView{
//  uint32 channel_id = 1;
//  string channel_name = 2;//房间名
//  uint32 channel_member_count = 3;//在房人数
//
//  string tab_icon = 4;//左上角icon
//  string tab_desc = 5;//左上角文案 e.g. K歌•合唱
//
//  string owner_account = 6;//房主头像
//  int32 owner_sex = 7;//房主性别
//  repeated string accounts = 8;//其他头像
//  string status = 9;//房间状态 e.g. 合唱中
//
//  string song = 10;//当前歌曲
//
//  MusicChannelReview review = 11;//重逢
//
//  MusicChannelLabel label = 12;//房间标签
//
//  KtvGlory glory = 13;//称号
//
//  PersonalCert personal_cert = 14;//个人认证标签
//
//  //非业务必须字段，埋点需要
//  string footprint = 15; //推荐trace id
//  uint32 tab_id = 16;//玩法id
//  uint32 region_id = 17;//blockId与elemId的拼接或者你行你唱的tagId,客户端埋点需要
//}
//
//enum MusicChannelLabel {
//  None = 0;
//  Quality = 1;//优质
//  Hot = 2;//热门
//}
//
////重逢
//message MusicChannelReview{
//  string review_account = 1;//重逢头像
//  string review_desc = 2;//重逢文案
//  int32 review_sex = 3;//重逢用户性别
//}
//
//message KtvGlory {
//  string glory_name = 1; // 称号名称
//  string glory_img = 2; // 头标
//  string glory_bg_img = 3; // 背景颜色
//  uint32 glory_rank = 4; // 排行
//}
//
//message PersonalCert{
//  string icon = 1;
//  string text = 2;
//  repeated string color = 3;
//  string text_shadow_color = 4;
//}

message ListHomePageFilterItemsReq{
}

message ListHomePageFilterItemsResp{
  repeated MusicFilterItem items = 1;
}

message GetUserSchoolLastReq{
  uint32 uid = 1;
}

message GetUserSchoolLastResp{
  uint32 last = 2;
}

message SetUserSchoolLastReq{
  uint32 uid = 1;
  uint32 last = 2;
}

message SetUserSchoolLastResp{

}

message GetRcmdPgcChannelReq{
  repeated uint32 channel_ids = 1;
}

message GetRcmdPgcChannelResp{
  uint32 channel_id = 1;
}

message IsOlderForMusicHomePageReq{
  uint32 uid = 1;
}


message IsOlderForMusicHomePageResp{
  bool is_older = 1;
}

message GetMusicChannelFilterV2Req{
  string filter_type = 1;//默认音乐首页
  uint32 market_id = 2;
  uint32 uid = 3;
}

message GetMusicChannelFilterV2Resp{
  repeated FilterItem filter_items = 1;
  message FilterItem {
    string title = 1;
    string filter_item_type = 2;//首页Item HOME_FILTER_ITEM,页面房间Item PAGE_FILTER_ITERM,页面帖子 PAGE_POST
    string filter_id = 3;//具体接口的通用参数，后续可能还需要不同类型有不同参数
    repeated FilterSubItem filter_sub_items = 4;
    string tip = 5;
    uint32 filter_attr_type = 6; // 标签属性  FilterAttrType
    SameCityTitle city_title = 7; // 标签是同城属性则使用此结构体信息替换title
  }

  message FilterSubItem {
    string title = 1;
    string filter_sub_id = 2;//具体接口的通用参数，后续可能还需要不同类型有不同参数
    string filter_sub_item_type = 3;//SUB_ITEM_POST帖子,SUB_ITEM_MUSIC音乐流
  }

  enum FilterItemType {
    HOME_FILTER_ITEM = 0;
    PAGE_FILTER_ITERM = 1;
    PAGE_POST = 2;
  }
}

// 标签属性
enum FilterAttrType{
  FILTER_ATTR_TYPE_UNEXPECTED = 0; // 默认
  FILTER_ATTR_TYPE_SAME_CITY = 1; // 同城属性tab 根据是否打开定位授权 展示同城tab的城市信息
}

// 标签是同城则使用此结构体信息替换title
message SameCityTitle{
  string city_name = 1;
  string province_name = 2;
}

message BatchIsPublishingReq{
  repeated TabChannel channels = 1;
  message TabChannel {
    uint32 tab_id = 1;
    uint32 channel_id = 2;
  }
}

message BatchIsPublishingResp{
  map<uint32, bool> channel_publishing = 1;
}

message IsPublishingReq{
  uint32 tab_id = 1;
  uint32 channel_id = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message IsPublishingResp{
  bool isPublishing = 1;
}

message ListPublishingChannelIdsReq {
  uint32 tab_id = 1;
  uint32 limit = 2;
  int64 time_offset = 3;//秒
}

message ListPublishingChannelIdsResp {
  repeated channel channels = 1;
  // buf:lint:ignore MESSAGE_PASCAL_CASE
  message channel {
    uint32 id = 1;
    int64 score = 2;
  }
}



message BlockOptionList {
  repeated BlockOption block_options = 1;
}

message BlockOption {
  uint32 block_id = 1;           //块id
  uint32 elem_id = 2;            //块里面的元素id
  string elem_val = 3;          // 用户填写的值
}

enum ChannelDisplayType {
  DISPLAY_AT_MAIN_PAGE = 0;           //大厅展示
  DISMISSED = 1;                      //不展示
  DISPLAY_AT_FIND_FRIEND = 2;         //找好友玩。跟随显示
  TEMPORARY = 3;                      //临时房
}

message MusicChannelReleaseInfo {
  uint32 id = 1;
  uint32 tab_id = 2;
  int64 release_time = 3;
  repeated BlockOption block_options = 4;    //房间选择的开房标签信息
  repeated ChannelDisplayType display_type = 5;
  bool show_geo_info = 6;                     //是否显示地理信息
  int64 update_time = 7;
  uint32 publish_times = 8;
  repeated GameLabel game_labels = 12; //发布者选择的推荐三级标签
  uint32 client_type = 13; // 客户端类型, 9-极速PC
  GameLocationItem game_location_item = 14; // 用户的地理位置
}

message GameLocationItem {
  string country = 1;
  string country_code = 2; // 国家代码
  string province =3;
  uint32 province_code = 4;
  string city = 5;
  uint32 city_code = 6;
  double latitude = 7;
  double longitude = 8;
}

message GameLabel {
  string val = 1; // 标签val 需要传回给 推荐
  string display_name = 2; // 标签的外显 value
  uint32 type = 3; // 标签类型
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SetMusicChannelReleaseInfoReq {
  MusicChannelReleaseInfo Music_channel_release_info = 1;
  bool want_fresh = 2;                       //是否优先匹配萌新
  string release_ip = 3;                     //房间发布时候的IP
  string channel_name = 4;
  uint32 creator = 5;
  string publish_type = 6;//数分使用
  repeated uint32 all_selected_bids = 7;      //全选的blockid
  repeated uint32 un_select_block_id = 8; //用户没选得blockId
  repeated BlockOption un_select_block_options = 9; //没有选的信息
}

message SetMusicChannelReleaseInfoResp {

}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message InitMusicChannelReleaseInfoReq {
  MusicChannelReleaseInfo Music_channel_release_info = 1;
  bool want_fresh = 2;                       //是否优先匹配萌新
  string release_ip = 3;                     //房间发布时候的IP
  string channel_name = 4;
  uint32 creator = 5;
  string publish_type = 6;//数分使用
}

message InitMusicChannelReleaseInfoResp {

}

message DismissMusicChannelReq {
  uint32 channel_id = 1;
  string source = 2;
  DismissType type = 3;
}

enum DismissType{
  Unknown = 0;
  Sever = 1;//服务器自动取消
  Cancel = 2;//用户点击取消发布
  SwitchTab = 3;//切换玩法导致取消
  Quit = 4;//退房导致取消
}

message DismissMusicChannelResp {
  bool dismiss = 1;
}

message GetRecommendChannelListLoadMore {
  uint32 num = 1;
}

message GetMusicChannelListReq {
  uint32 uid = 1;
  uint32 limit = 2;
  repeated uint32 tab_id_list = 3;
}

message GetMusicChannelListResp {
  repeated MusicChannelReleaseInfo channel_list = 1;
}

message GetMusicChannelByIdsReq {
  repeated uint32 ids = 1;
  repeated ChannelDisplayType types = 2;
  bool return_all = 3;
}

message GetMusicChannelByIdsResp {
  repeated MusicChannelReleaseInfo info = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SwitchChannelTabReq{
  uint32 uid = 1;
  uint32 tab_id = 2;
  uint32 channel_id = 3;
  uint32 appId = 4;
  uint32 marketId = 5;
}
message SwitchChannelTabResp{
  string tab_name = 1;
  repeated string welcome_txt_list = 2;
  uint32 mic_mod = 3;
  uint32 tab_type = 4;
  uint32 tag_id = 5;
}

message DisappearChannelReq {
  string client_id = 1;
  uint64 acquire_duration = 2;        //超时此时间段可重入

  message Timeout {
    uint64 timeout_duration = 1;    //创建超出该时间段清除
  }

  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  message Keepalive {
    uint64 Keepalive_duration = 1; //不保持心跳超出该时间段
    uint32 member_count = 2;        //房间人数大于此数
  }

  message ReleaseTimeout {
    uint64 timeout_duration = 1;    //创建超出该时间段清除
  }

  Timeout timeout_event = 10;
  Keepalive keepalive_event = 11;
  ReleaseTimeout release_timeout_event = 12; //发布超出时间段 v5.5.0
}

message DisappearChannelResp {
  repeated uint32 channel_ids = 1;
}

message GetOnlineInfoReq {
  uint32 online_user_count = 1;
}

message GetOnlineInfoResp {
  uint32 room_count = 1;
  repeated uint32 online_user_list = 2;
}

message FreezeChannelReq {
  repeated uint32 channel_id_list = 1;
  int64 freeze_time = 2;                   //冻结多久，单位是秒，永久冻结传-1
}

message FreezeChannelResp {

}

message UnfreezeChannelReq {
  repeated uint32 channel_id_list = 1;

}

message UnfreezeChannelResp {

}

message GetChannelFreezeInfoReq {
  uint32 channel_id = 1;
}

message GetChannelFreezeInfoResp {
  int64 freeze_time = 1;
}

enum HistoryType {
  CreateHistory = 0;
  UpdateHistory = 1;
}

message SetExtraHistoryReq {
  uint32 channel_id = 1;
  HistoryType history_type = 2;
  int64 expire_after = 3;     //秒为单位，小于等于0即不过期
}

message SetExtraHistoryResp {

}

message GetExtraHistoryReq {
  uint32 channel_id = 1;
  HistoryType history_type = 2;
}

message GetExtraHistoryResp {
  string value = 1;
}

message GetChannelPlayModelReq {
  uint32 channel_id = 1;
}

message GetChannelPlayModelResp {
  uint32 tab_id = 1; // 玩法id
}

message GetChannelRoomUserNumberReq {
  repeated uint32 tab_id = 1; // 如果不指定，则返回全部
}

message GetChannelRoomUserNumberResp {
  message RoomUserInfo {
    uint32 tab_id = 1;
    int64 total_user_number = 2;
  }
  repeated RoomUserInfo room_user_info = 1;
}

message AddTemporaryChannelReq {
  MusicChannelReleaseInfo channel = 1; // 复用channel
}

message AddTemporaryChannelResp {}


// 查询优质房
message SearchHighQualityChannelsReq {
  uint32 ttid = 1;
  HighQualityChannelInfoStatusType status = 2;
  uint32 page = 3;
  uint32 limit = 4;
  uint32 tab_id = 5;
  int32 quality_type = 6;//-1全部，0优质，1热门
  string view_id = 7;
}
message SearchHighQualityChannelsResp {
  repeated HighQualityChannelInfo info_list = 1;
  uint32 count = 2 ;
}
// 更新优质房
message UpdateHighQualityChannelsReq {
  repeated HighQualityChannelInfo info = 1;
}
message UpdateHighQualityChannelsResp {
  repeated string ids = 1;
}
message HighQualityChannelInfo {
  uint32 uid = 1;
  uint32 ttid = 2;
  string name = 3;
  uint32 display_id = 4;
  uint32 channel_id = 5;
  int64 begin_time = 6;
  int64 end_time = 7;
  uint32 duration_time_hour = 8;
  HighQualityChannelInfoStatusType status = 9;
  uint32 tab_id = 10;
  string id = 11;//记录id
  QualityType quality_type = 12;
  string view_id = 13;
}
enum HighQualityChannelInfoStatusType {
  UNKNOWN = 0;
  NOT_ACTIVE = 1;
  ACTIVE = 2;
  INVALID = 3;
}

message BatchIsHighQualityChannelsReq{
  map<uint32, uint32> tab_channel_map = 1;
}

message BatchIsHighQualityChannelsResp{
  map<uint32, bool> channel_high_quality_map = 1;
}


/*--资源位配置 运营后台--*/

message BatchSetResourceConfigReq{
  repeated ResourceConfigInfo info = 1;
}
message BatchSetResourceConfigResp{}
message GetResourceConfigsReq{
  uint32 uid = 1;
  uint32 channel_id = 2;
  string text = 3;   /*文案模糊搜索*/
  uint32 offset = 4;
  uint32 limit = 5;
}
message GetResourceConfigsResp{
  repeated ResourceConfigInfo info = 1;
  uint32 total = 2;
}
message ResourceConfigInfo{
  string id = 1;
  uint32 uid = 2;
  uint32 channel_id = 3;
  string jump_url = 4;
  string icon = 5;
  string text = 6;/*文案*/
}
message BatchDelResourceConfigReq{
  repeated string id = 1;
}
message BatchDelResourceConfigResp{

}

message ListMuseSocialCommunityChannelsReq{
  string social_community_id = 1;
  uint32 uid = 2;
  repeated uint32 channel_ids = 3;
}

message ListMuseSocialCommunityChannelsResp{
  map<uint32, ga.music_topic_channel.MuseSocialCommunityChannelInfo> channel_views = 1;
}

message GetFilterInfoByFilterIdsReq {
  repeated string filter_ids = 1;
  bool need_check = 2; // 是否需要版本校验和ab实验
  bool has_filter_block=3; //是否过滤玩法带有block的 filter
}

message MusicFilterItemV2 {
  string name = 1;
  repeated uint32 tab_ids = 2;
  string filter_icon=3; //icon
}

message GetFilterInfoByFilterIdsResp {
  map<string, MusicFilterItemV2> filter_map = 1;//key:filterId
}

message GetFilterInsertChannelIdsReq {
  repeated string filter_ids = 1;
}

message GetFilterInsertChannelIdsResp {
  message InsertChannelIds {
    repeated uint32 channel_ids = 1;
  }
  map<string, InsertChannelIds> filter_map = 1; // key: filterId
}

message FilterIdToConfTabReq {
  repeated string filter_ids = 1; // 需要查询的filterIds
}

message FilterIdToConfTabResp {
  message Block {
    uint32 block_id = 1; // blockId
    uint32 elem_id = 2; // elemId
  }
  message ConfTab {
    uint32 tab_id = 1; // tabId
    repeated Block blocks = 2; // tab下的blockId和elemId
  }
  repeated ConfTab conf_tabs = 1; // 返回的筛选条件
}
