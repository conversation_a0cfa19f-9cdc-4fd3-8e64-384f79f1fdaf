syntax = "proto3";

package rcmd_mt_proxy;

option go_package = "golang.52tt.com/protocol/services/rcmd-mt-proxy";

import "tt/quicksilver/extension/options/options.proto";

service RcmdMtProxy {
  option (service.options.service_ext) = {
    service_name: "rcmd-mt-proxy"
  };
rpc Proxy(CommonReq) returns (CommonRsp);
}

// 请求对象
message CommonReq {
int64 uid = 1;     // 用户ID
string cmd = 2; // 请求接口GuideTopic、RecognizeTopic、GetAtMatchmakerMsg、RecallNextDay
string data = 3;  // 请求数据
string extra = 4; // 额外信息
}

// 响应对象
message CommonRsp {
int32 proxy_code = 1; // 代理服务业务响应码ProxyCode
string proxy_err_msg = 2;  // 代理服务业务错误信息
string data = 3; // 响应数据
}

// kafka异步请求对象  Topic: rcmd-mt-proxy-req
message AsyncReq {
string ctx_id = 1; // 上下文ID
int64 uid = 2;     // 用户ID
string cmd = 3; // 请求接口GuideTopic、RecognizeTopic、GetAtMatchmakerMsg、RecallNextDay
string data = 4;  // 请求数据
string extra = 5; // 额外信息
  map<string, string> callback_data=6; // 回调数据

}
message AsyncRsp {
  string ctx_id = 1; // 上下文ID
  int64 uid = 2;     // 用户ID
  string cmd = 3;   // 请求接口
  int32 proxy_code = 4; // 代理服务业务响应码ProxyCode
  string proxy_err_msg = 5;  // 代理服务业务错误信息
  string data = 6; // 响应数据
  map<string, string> callback_data = 7; // 回调透传数据
}


// 代理服务业务响应码
enum ProxyCode {
ProxyCode_Success = 0; // 成功
ProxyCode_Fail = 101;  // 失败
ProxyCode_Invalid_Cmd = 102; // 接口名不存在或错误
ProxyCode_DownStream_Error = 103; // 下行错误
ProxyCode_DownStream_Timeout = 104; // 下行超时
}

message UserInfo {
  uint32   uid=1;
  string   nickname=2;
  int32    age=3;
  string   gender=4;
  string   province=5;
  string   city=6;
  string   interest_card=7;
  string   shining_point=8;
  string  role_play_role_name=9;   //角色名
  string role_play_intro=10;     //角色简介
  string role_play_plot_content=11; //角色扮演剧情内容
  string role_play_plot_target=12;  //角色扮演剧情目标
  }

message HistoryMessage {
  string content=1;
  int64 timestamp=2;
  uint32 from_uid=3;
}


message InspirationReplyReq {
  UserInfo usera_info=1;
  UserInfo userb_info=2;
  string scene_info=3;
  repeated HistoryMessage chat_history=4;
  string who_reply=5;
  int32 reply_count=6;
  string distance=7;
  string source=8;
  string role_play_relation_name=9;
  string client_version=10; // 客户端版本号
  repeated string regenerate_triggered_content=11; // 上次生成内容
}

message InspirationReplyRsp {
  repeated string replies=1;
  string status_message=2;
  int32 verify=3;   // 0审核不通过 1审核通过
}

message AICupidChatData {
  string match_maker=1; // "小桃"
  repeated HistoryMessage chat_context=2;
  string distance=3;
  UserInfo usera_info=4;
  UserInfo userb_info=5;
  string at_user=6; // at红娘用户昵称
  string at_content=7; // at内容
  repeated string finished_topics=8; // 已完成的话题
  int32 gap_time=9;//上次回复间隔时间
}

