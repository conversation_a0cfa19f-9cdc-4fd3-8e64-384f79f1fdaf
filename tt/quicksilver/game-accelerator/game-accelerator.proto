syntax = "proto3";

package game_accelerator;

option go_package = "golang.52tt.com/protocol/services/game-accelerator";

// 用户线频率服务
service GameAccelerator {
  // 更新入口信息
  rpc UpdateAccelerateEntrance(UpdateAccelerateEntranceRequest) returns (UpdateAccelerateEntranceResponse) {}
  // 获取入口信息
  rpc GetAccelerateEntrance(GetAccelerateEntranceRequest) returns (GetAccelerateEntranceResponse) {}
  // 搜索游戏列表
  rpc SearchAccelerateGame(SearchAccelerateGameRequest) returns (SearchAccelerateGameResponse) {}
  // 更新游戏信息
  rpc UpdateGame(UpdateGameRequest) returns (UpdateGameResponse) {}
  // 同步游戏信息
  rpc SyncAccelerateGameList(SyncAccelerateGameListRequest) returns (SyncAccelerateGameListResponse) {}
  // 根据游戏id列表获取游戏信息
  rpc GetGames(GetGamesRequest) returns (GetGamesResponse) {}

  // 授权相关
  rpc GetAcceleratorOrder(GetAcceleratorOrderReq) returns (GetAcceleratorOrderResp) {}
  rpc AdminAddUserAccelerator(AdminAddUserAcceleratorReq) returns (AdminAddUserAcceleratorResp) {}
  rpc GetPreviewExpireTime(GetPreviewExpireTimeReq) returns (GetPreviewExpireTimeResp) {}
  // 获取用户token
  rpc GetAcceleratorToken(GetAcceleratorTokenReq) returns (GetAcceleratorTokenResp) {}

  // 加速器用户管理
  rpc GetAcceleratorUserList(GetAcceleratorUserListReq) returns (GetAcceleratorUserListResp) {}
  rpc GetUserAcceleratorLog(GetUserAcceleratorLogReq) returns (GetUserAcceleratorLogResp) {}
  rpc BanUserUseAccelerator(BanUserUseAcceleratorReq) returns (BanUserUseAcceleratorResp) {}

  // 活动接口
  rpc AddUserAcceleratorAuthorize(AddUserAcceleratorAuthorizeReq) returns (AddUserAcceleratorAuthorizeResp) {}
  rpc GetUserAcceleratorInfo(GetUserAcceleratorInfoReq) returns (GetUserAcceleratorInfoResp) {}
  rpc GetNewbieOrderByUids(GetNewbieOrderByUidsReq) returns (GetNewbieOrderByUidsResp) {}
}

enum EntranceShowType {
  ENTRANCE_SHOW_TYPE_UNSPECIFIED = 0;
  ENTRANCE_SHOW_TYPE_ALL = 1; // 全量可见
  ENTRANCE_SHOW_TYPE_CROWD = 2; // 指定人群包可见
}
message AccelerateEntrance {
  EntranceShowType show_type = 1; // 入口展示类型
  string crowd_group_id = 2; // 人群包id
}

message UpdateAccelerateEntranceRequest {
  AccelerateEntrance entrance = 1;
}

message UpdateAccelerateEntranceResponse {
}

message GetAccelerateEntranceRequest {
}

message GetAccelerateEntranceResponse {
  AccelerateEntrance entrance = 1;
}

enum AcceleratorGameState {
  ACCELERATOR_GAME_STATE_UNSPECIAL = 0;
  ACCELERATOR_GAME_STATE_NORMAL = 1; // 正常
  ACCELERATOR_GAME_STATE_BANNED = 2; // 屏蔽
  ACCELERATOR_GAME_STATE_OFF_SHELF = 3; // 下架
}

enum AcceleratorLabel {
  LABEL_UNSPECIAL = 0;
  LABEL_RECOMMEND = 1; // 推荐游戏
  LABEL_HOT = 2; // 热门游戏
}

message Game {
  uint32 id = 1; // 游戏id，同厂商id
  string name = 2; // 厂商游戏名称
  string logo = 3; // 厂商游戏logo
  repeated uint32 support_acc_modes = 4; // 支持的加速模式
  AcceleratorGameState state = 5; // 游戏配置状态
  repeated AcceleratorLabel labels = 6; // 运营打标列表
  repeated string key_words = 7; // 搜索关键词列表
  int64 update_time = 8; // 更新时间
  uint32 recent_user_count = 9; // 近14天加速用户数
}

message SearchAccelerateGameRequest {
  string last_id = 1;
  uint32 limit = 2;
  string source_tag = 3; // 请求来源，自定义文案，仅用于排查问题
  message FilterOption {
    string name = 1; // 游戏名
    uint32 id = 2; // 游戏id
    AcceleratorGameState state = 3; // 游戏配置状态
  }
  FilterOption filter_option = 4;
  repeated uint32 force_insert_ids = 5; // 强插的游戏id列表
  message ExtraInfoOption {
    bool need_recent_user_count = 1; // 是否需要返回近14天加速用户数
    bool need_do_filter = 2; // 是否需要过滤
  }
  ExtraInfoOption extra_info_option = 6; // 附加信息选项
}

message SearchAccelerateGameResponse {
  repeated Game games = 1;
  string last_id = 2;
}

message UpdateGameRequest {
  message Game {
    uint32 id = 1; // 游戏id，同厂商id
    string name = 2; // 厂商游戏名称
    string logo = 3; // 厂商游戏logo
    AcceleratorGameState state = 4; // 游戏配置状态
    repeated AcceleratorLabel labels = 5; // 运营打标列表
    repeated string key_words = 6; // 搜索关键词列表
  }
  Game game = 1;
}

message UpdateGameResponse {
}

message SyncAccelerateGameListRequest {}

message SyncAccelerateGameListResponse {
}

// 根据游戏id列表获取游戏信息，不返回最近14天加速用户数
message GetGamesRequest {
  repeated uint32 game_ids = 1; // 游戏id列表，同厂商id
  bool need_recent_user_count = 2; // 是否需要返回近14天加速用户数
}

message GetGamesResponse {
  repeated Game games = 1; // 游戏列表
}


enum AcceleratorOrderStatus {
  ACCELERATOR_ORDER_STATUS_UNSPECIFIED = 0; // 未知状态
  ACCELERATOR_ORDER_STATUS_PENDING = 1; // 待处理
  ACCELERATOR_ORDER_STATUS_SUCCESS = 2; // 授权成功
  ACCELERATOR_ORDER_STATUS_FAIL = 3; // 授权失败
  ACCELERATOR_ORDER_STATUS_INVALID = 4; // 无效订单
}

message GetAcceleratorOrderReq {
  uint32 uid = 1; // 用户id
  string third_party_order_id = 2; // 订单id
  int64 start_time = 3; // 开始时间,单位秒, 查询的是第三方返回的时间
  int64 end_time = 4; // 结束时间,单位秒, 查询的是第三方返回的时间
  AcceleratorOrderStatus status = 5; // 授权结果状态
  int32 page = 6; // 分页页码，从1开始
  int32 page_size = 7; // 分页大小
}

// 订单类型
enum AcceleratorOrderType {
  ACCELERATOR_ORDER_TYPE_UNSPECIFIED = 0; // 未知
  ACCELERATOR_ORDER_TYPE_NEWBIE = 1; // 新人体验卡（3天）
  ACCELERATOR_ORDER_TYPE_MONTH = 2; // 月卡
}

// 平台类型
enum AcceleratorPlatformType {
  ACCELERATOR_PLATFORM_TYPE_UNSPECIFIED = 0; // 未知
  ACCELERATOR_PLATFORM_TYPE_WINDOWS = 1; // windows
}

message AcceleratorOrderItem {
  // 订单id
  string order_id = 1;
  // 用户id
  uint32 uid = 2;
  // 订单类型
  AcceleratorOrderType order_type = 3;
  // 申请数量
  uint32 order_count = 4;
  // 授权结果状态
  AcceleratorOrderStatus status = 5;
  // 订单创建时间,单位秒
  int64 create_time = 6;
  // 订单到期时间,单位秒
  int64 expire_time = 7;
  // 平台类型
  AcceleratorPlatformType platform_type = 8;
  // 操作人
  string operator = 9; // 操作人工号/系统业务id
  // 授权备注
  string remark = 10;
  // 业务调用方订单id
  string out_trade_order_id = 11; // 外部订单id
  // 第三方订单号
  string third_party_order_id = 12; // 第三方订单号
}

message GetAcceleratorOrderResp {
  int64 total_count = 1; // 总记录数
  repeated AcceleratorOrderItem order_list = 2; // 订单列表
}

message GetAcceleratorUserListReq {
  uint32 uid = 1; // 用户id
  int64 membership_start_time = 2; // 套餐开始时间,单位秒
  int64 membership_end_time = 3; // 套餐结束时间,单位秒
  UserStatus user_status = 4; // 用户状态
  string last_id = 5; // 上页最后的id
}

enum UserIdentity {
  USER_IDENTITY_UNSPECIFIED = 0; // 未知身份
  USER_IDENTITY_FREE = 1; // 免费用户
  USER_IDENTITY_VIP = 2; // VIP用户
}

enum UserStatus {
  USER_STATUS_UNSPECIFIED = 0; // 未知状态
  USER_STATUS_NORMAL = 1; // 正常
  USER_STATUS_EXPIRED = 2; // 会员套餐到期
  USER_STATUS_BAN = 3; // 禁用
}

message AcceleratorUserItem {
  uint32 uid = 1; // 用户id
  UserIdentity user_identity = 2; // 用户身份
  UserStatus user_status = 3; // 用户状态
  int64 create_time = 4; // 创建时间,单位秒
  int64 membership_expire_time = 5; // 会员套餐到期时间,单位秒
  int64 authorize_expire_time = 6; // 当前使用授权到期时间,单位秒
}

message GetAcceleratorUserListResp {
  int64 total_count = 1; // 总记录数（非完全精准，预估值，可能有一点偏差），仅第一页返回
  repeated AcceleratorUserItem user_list = 2; // 用户列表
  string last_id = 3; // 当前页最后的id，用于获取下一页数据
}


message GetUserAcceleratorLogReq {
  string ttid = 1; // 用户account返回的username
  string app_ids = 2; // 应用id列表,逗号分隔
  int64 start_time = 3; // 开始时间,单位秒
  int64 end_time = 4; // 结束时间,单位秒
  int32 page = 5; // 分页页码，从1开始
  int32 page_size = 6; // 分页大小
}

message GetUserAcceleratorLogResp {
  int32 code = 1; // 响应码
  string message = 2; // 响应消息
  message Data {
    message Item {
      string imei = 1; // 设备唯一码
      repeated string app_ids = 2; // 应用ID列表
      string start_time = 3; // 开启加速时间
      string end_time = 4; // 结束加速时间
      string flow_use = 5; // 消耗流量
      string max_width = 6; // 最大宽带
      string min_width = 7; // 最小宽带
      string avg_width = 8; // 平均宽带
    }
    repeated Item items = 1; // 日志列表
    int32 total = 2; // 总数
    int32 per_page = 3; // 每页数量
    int32 current_page = 4; // 当前页码
    string server_time = 5; // 服务器时间
  }
  Data data = 3; // 日志数据
}

message BanUserUseAcceleratorReq {
  uint32 uid = 1; // 用户id
  enum Action {
    ACTION_UNSPECIFIED = 0; // 未知操作
    ACTION_BAN = 1; // 禁用加速器
    ACTION_UNBAN = 2; // 解除禁用
  }
  Action action = 2; // 操作类型
}

message BanUserUseAcceleratorResp {
}


message AdminAddUserAcceleratorReq {
  repeated uint32 uids = 1; // 用户id列表
  string operator = 2; // 操作人工号/系统业务id
  AcceleratorOrderType accelerator_order_type = 3; // 授权类型
  uint32 order_count = 4; // 授权数量
  string remark = 5; // 授权备注
  AcceleratorPlatformType platform_type = 6;
  string out_trade_order_id = 7; // 外部订单id，用于幂等性控制
}

message AddAcceleratorResult {
  uint32 uid = 1; // 用户id
  int32 code = 2; // 授权结果状态, 0表示成功，非0表示失败
  string message = 3; // 授权结果消息, 成功时为空，失败时包含错误信息
}

message AdminAddUserAcceleratorResp {
  uint32 success_num = 1;
  repeated uint32 fail_uids = 2[deprecated=true]; // 授权失败的用户id列表
  repeated AddAcceleratorResult fail_result = 3; // 失败授权结果列表
}

message GetPreviewExpireTimeReq {
  uint32 uid = 1; // 用户id
  AcceleratorOrderType accelerator_order_type = 2; // 授权类型
  uint32 order_count = 3; // 授权数量
}

message GetPreviewExpireTimeResp {
  string expire_time = 1; // 预览到期时间，格式为"YYYY-MM-DD HH:MM:SS"
}

message GetAcceleratorTokenReq {
  uint32 uid = 1; // 用户id
  string imei = 2; // 厂商唯一设备id，当前使用用户的TTid
  string app_id = 3; // 游戏id，同厂商id
}

message GetAcceleratorTokenResp {
  string token = 1; // 加速器token
}

enum AcceleratorBusinessType {
  ACCELERATOR_BUSINESS_TYPE_UNSPECIFIED = 0; // 未知
  ACCELERATOR_BUSINESS_TYPE_ACTIVITY = 1; // 活动任务
  ACCELERATOR_BUSINESS_TYPE_ADMIN = 2; // 运营后台
}

message AddUserAcceleratorAuthorizeReq {
  uint32 uid = 1; // 用户id列表
  AcceleratorBusinessType business_id = 2; // 系统业务id
  AcceleratorOrderType accelerator_order_type = 3; // 授权类型
  uint32 order_count = 4; // 授权数量
  AcceleratorPlatformType platform_type = 5; // 平台类型
  string remark = 6; // 授权备注
  string out_trade_order_id = 7; // 外部订单id，用于幂等性控制
}

message AddUserAcceleratorAuthorizeResp {
  uint32 success_num = 1[deprecated=true]; // 成功添加的用户数量
  repeated uint32 fail_uids = 2[deprecated=true]; // 添加失败的用户id列表
}

message GetUserAcceleratorInfoReq {
  repeated uint32 uids = 1; // 用户id列表
}

message GetUserAcceleratorInfoResp {
  repeated AcceleratorUserItem items = 1;
}

message GetNewbieOrderByUidsReq {
  repeated uint32 uids = 1; // 用户id列表
}

message GetNewbieOrderByUidsResp {
  map<uint32, AcceleratorOrderItem> item_map = 1;
}