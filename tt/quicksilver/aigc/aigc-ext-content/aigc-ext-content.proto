syntax = "proto3";

package aigc_ext_content;

//web端交互的ext里面的内容
option go_package = "golang.52tt.com/protocol/services/aigc/aigc-ext-content";

enum AsrTextType {
  ASR_TEXT_TYPE_UNSPECIFIED = 0;
  ASR_TEXT_TYPE_NORMAL = 1; // 正常
  ASR_TEXT_TYPE_INVALID = 2; // 无效
}

//前端上传的asr信息
message AsrExtContent {
  string url = 1; // 语音url
  uint32 seconds = 2; // 语音长度,单位秒
  string text = 3; // 语音文本内容
  string key = 4;
  uint32 asr_text_type = 5;// 见 AstTextType
}

// 单聊，单人群聊句数提示ext
message SentenceTips {
  string tips = 1; // 提示内容
  bool show_more = 2; // 展示获取更多句数
}

// 欢迎语消息 IM_CMD_TYPE_WELCOME_MSG = 4
message WelcomeMsg {
  string url = 1; // 语音url
  string text = 2; // 欢迎语文本内容
  uint32 special_to_uid = 3; // 对指定用户有特效
}

// 进群提示信息 IM_CMD_TYPE_WELCOME_MSG = 3
message JoinGroupTips {
  string content = 1;
  uint32 join_uid = 2; // 进群用户id
  repeated uint32 other_uids = 3; // 其他用户id
}

message UserPresentMsg {
  string source = 1;

  string icon = 2;
  string title = 3;
  string desc = 4;
}

message AIGiftMsg {
  enum AIGiftType {
    AI_GIFT_TYPE_UNSPECIFIED = 0;
    // 句数
    AI_GIFT_TYPE_SENTENCE_NUMBER = 1;
    // 聊天背景
    AI_GIFT_TYPE_CHAT_BACKGROUND = 2;
  }

  uint32 type = 1;
  string icon = 2;
  string name = 3;
  uint32 num = 4;
  string desc = 5;
}

message UserPresentGuideMsg {
  // 引导内容
  string content = 1;
}
