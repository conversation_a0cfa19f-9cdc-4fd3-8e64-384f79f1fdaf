syntax = "proto3";

package aigc_account;

option go_package = "golang.52tt.com/protocol/services/aigc/aigc-account";

service AigcAccount {
  // 创建AI账号
  rpc CreateAIAccount(CreateAIAccountRequest) returns (CreateAIAccountResponse) {}
  // 更新AI账号
  rpc UpdateAIAccount(UpdateAIAccountRequest) returns (UpdateAIAccountResponse) {}
  // 批量注销AI账号
  rpc BatchUnregisterAIAccount(BatchUnregisterAIAccountRequest) returns (BatchUnregisterAIAccountResponse) {}
  // 获取AI账号列表（运营后台）
  rpc GetPageAIAccount(GetPageAIAccountRequest) returns (GetPageAIAccountResponse) {}
  // 根据uid获取AI账号
  rpc GetAIAccount(GetAIAccountRequest) returns (GetAIAccountResponse) {}
  // 批量获取AI账号
  rpc BatchGetAIAccount(BatchGetAIAccountRequest) returns (BatchGetAIAccountResponse) {}
  // 增加聊天轮次
  rpc IncrChatRound(IncrChatRoundRequest) returns (IncrChatRoundResponse) {}
  // 获取聊天轮次
  rpc GetChatRound(GetChatRoundRequest) returns (GetChatRoundResponse) {}
  // 更新最近聊天时间
  rpc UpdateChatTime(UpdateChatTimeRequest) returns (UpdateChatTimeResponse) {}
  // 获取最近聊天时间
  rpc GetLastChatTime(GetLastChatTimeRequest) returns (GetLastChatTimeResponse) {}
  // 增加AI账号发帖
  rpc AddAiPost(AddAiPostRequest) returns (AddAiPostResponse) {}
  // 获取AI账号发帖列表
  rpc GetAiPost(GetAiPostRequest) returns (GetAiPostResponse) {}
  // 添加用户和AI账号聊天记录
  rpc AddUserAiChatRecord(AddUserAiChatRecordRequest) returns (AddUserAiChatRecordResponse) {}
  // 获取用户和AI账号聊天记录
  rpc GetUserAiChatRecord(GetUserAiChatRecordRequest) returns (GetUserAiChatRecordResponse) {}
  // 添加用户和AI互动(双方都发过消息)记录
  rpc AddUserAiInteractionRecord(AddUserAiInteractionRecordRequest) returns (AddUserAiInteractionRecordResponse) {}
  // 获取用户和AI互动(双方都发过消息)记录
  rpc GetUserAiInteractionRecord(GetUserAiInteractionRecordRequest) returns (GetUserAiInteractionRecordResponse) {}
  // 添加AI点赞的帖子
  rpc AddAiLikePost(AddAiLikePostRequest) returns (AddAiLikePostResponse) {}
  // 获取AI点赞的帖子数
  rpc GetAiLikePostCount(GetAiLikePostCountRequest) returns (GetAiLikePostCountResponse) {}
  // 添加AI评论的帖子
  rpc AddAiCommentPost(AddAiCommentPostRequest) returns (AddAiCommentPostResponse) {}
  // 获取AI评论的帖子数
  rpc GetAiCommentPostCount(GetAiCommentPostCountRequest) returns (GetAiCommentPostCountResponse) {}
  // 添加帖子的AI评论数据
  rpc AddPostAiCommentRecord(AddPostAiCommentRecordRequest) returns (AddPostAiCommentRecordResponse) {}
  // 获取帖子的AI评论数
  rpc GetPostAiCommentCount(GetPostAiCommentCountRequest) returns (GetPostAiCommentCountResponse) {}
  // 获取帖子评论的ai列表
  rpc GetPostAiCommentList(GetPostAiCommentListRequest) returns (GetPostAiCommentListResponse) {}
  // 获取指定性别的AI账号列表
  rpc GetAIAccountByGender(GetAIAccountByGenderRequest) returns (GetAIAccountByGenderResponse) {}
  // 更新AI账号房间配置
  rpc UpdateAiRoomCfg(UpdateAiRoomCfgRequest) returns (UpdateAiRoomCfgResponse) {}
  // 获取AI账号房间配置
  rpc GetAiRoomCfg(GetAiRoomCfgRequest) returns (GetAiRoomCfgResponse) {}
  // 获取运营时间中的房间配置信息
  rpc GetRunningAiRooms(GetRunningAiRoomsRequest) returns (GetRunningAiRoomsResponse) {}
  // 记录最近一次接待时间
  rpc RecordReceptionTime(RecordReceptionTimeRequest) returns (RecordReceptionTimeResponse) {}
  // 获取最近一次接待
  rpc GetLastReceptionTime(GetLastReceptionTimeRequest) returns (GetLastReceptionTimeResponse) {}
  // 记录等待接待用户
  rpc RecordWaitingReceptionUser(RecordWaitingReceptionUserRequest) returns (RecordWaitingReceptionUserResponse) {}
  // 获取等待接待用户
  rpc GetWaitingReceptionUser(GetWaitingReceptionUserRequest) returns (GetWaitingReceptionUserResponse) {}
  // 记录已接待用户
  rpc RecordReceptionUser(RecordReceptionUserRequest) returns (RecordReceptionUserResponse) {}
  // 获取已接待用户
  rpc CheckReceptionUser(CheckReceptionUserRequest) returns (CheckReceptionUserResponse) {}
  // 记录当前房间人数
  rpc RecordRoomUserNum(RecordRoomUserNumRequest) returns (RecordRoomUserNumResponse) {}
  // 获取当前房间人数
  rpc GetRoomUserNum(GetRoomUserNumRequest) returns (GetRoomUserNumResponse) {}
}

message AIAccount {
  // ID
  uint32 uid = 1;
  // 密码
  string password = 2;
  // ip地址
  string ip = 3;
  // 提示词ID, 为空则AI不会回复用户
  uint32 prompt_id = 4;
  // 音色ID
  uint32 timbre_id = 5;
  // 关联角色ID
  uint32 role_id = 6;
  // 开场白
  string prologue = 7;
  // 是否已注销
  bool is_unregister = 8;
  // 创建时间
  int64 create_time = 9;
  // 更新时间
  int64 update_time = 10;
  // 是否已加入风控白名单
  bool is_in_risk_white_list = 11;
  // 标识
  string identity = 12;
  // 说明
  string desc = 13;
  uint32 sort = 14;
  //账号标签
  repeated string account_tags = 15;
  // 是否展示在扩列墙
  bool is_show_in_chat_card_wall = 16;
}

message CreateAIAccountRequest {
  AIAccount account = 1;
}

message CreateAIAccountResponse {
}

message UpdateAIAccountRequest {
  AIAccount account = 1;
}

message UpdateAIAccountResponse {
}

message BatchUnregisterAIAccountRequest {
  repeated uint32 uid_list = 1;
}

message BatchUnregisterAIAccountResponse {
}

message GetPageAIAccountRequest {
  uint32 page = 1;
  uint32 size = 2;
  uint32 chat_card_wall_show_status = 3;  //扩列墙展示状态 0--默认  1--展示  2--不展示
}

message GetPageAIAccountResponse {
  repeated AIAccount account_list = 1;
  uint32 total = 2;
}

enum GetAIAccountSource {
  // 默认
  DEFAULT = 0;
  // 运营后台
  ADMIN_BACKEND = 1;
  // im tab
  IM_TAB = 2;
  // 麦上
  ON_MIC = 3;
  // ugc 个人页
  UGC_PERSONAL_PAGE = 4;
  // 房间个人资料卡
  CHANNEL_PERSONAL_PAGE = 5;
  // ugc 流
  UGC_FEEDS = 6;
  // 房间列表
  CHANNEL_LIST = 7;
}

message GetAIAccountRequest {
  uint32 uid = 1;
  GetAIAccountSource req_source = 2;
}

message GetAIAccountResponse {
  AIAccount account = 1;
}

message BatchGetAIAccountRequest {
  repeated uint32 uid_list = 1;
  GetAIAccountSource req_source = 2;
}

message BatchGetAIAccountResponse {
  repeated AIAccount account_list = 1;
}

// 增加聊天轮次
message IncrChatRoundRequest {
  uint32 uid = 1; // 用户id
  uint32 ai_uid = 2; // AI账号id
}

message IncrChatRoundResponse {
}

// 获取聊天轮次
message GetChatRoundRequest {
  uint32 uid = 1; // 用户id
  uint32 ai_uid = 2; // AI账号id
  bool need_total = 3; // 是否需要总轮次
}

message GetChatRoundResponse {
  uint32 cur_round = 1; // 当前轮次
  uint32 total_round = 2; // 总轮次
}

// 更新最近聊天时间
message UpdateChatTimeRequest {
  uint32 uid = 1; // 用户id
  uint32 ai_uid = 2; // AI账号id
}

message UpdateChatTimeResponse {}

// 获取最近聊天时间
message GetLastChatTimeRequest {
  uint32 uid = 1; // 用户id
  uint32 ai_uid = 2; // AI账号id
}

message GetLastChatTimeResponse {
  int64 last_chat_time = 1; // 上次聊天时间
}

message AddAiPostRequest {
  uint32 uid = 1; // 用户ID
  string post_id = 2; // 帖子ID
}

message AddAiPostResponse {
}

message GetAiPostRequest {
  uint32 page = 1; // 页码
  uint32 size = 2; // 限制数量
  repeated uint32 ai_uid = 3; // ai账号ID
}

message GetAiPostResponse {
  repeated string post_ids = 1; // 帖子ID列表
  uint32 total_num = 2;
}

message AddUserAiChatRecordRequest {
  uint32 from_uid = 1;
  uint32 to_uid = 2;
}

message AddUserAiChatRecordResponse {
}

message GetUserAiChatRecordRequest {
  uint32 from_uid = 1;
  uint32 to_uid = 2;
}

message GetUserAiChatRecordResponse {
  int64 record_time = 1; // 聊天记录时间
}

message AddUserAiInteractionRecordRequest {
  uint32 uid = 1; // 用户ID
  uint32 ai_uid = 2; // AI账号ID
}

message AddUserAiInteractionRecordResponse {
}

message GetUserAiInteractionRecordRequest {
  uint32 uid = 1; // 用户ID
}

message GetUserAiInteractionRecordResponse {
  repeated uint32 ai_uid_list = 1; // AI账号ID列表
}

message AddAiLikePostRequest {
  uint32 uid = 1; // 用户ID
  string post_id = 2; // 帖子ID
}

message AddAiLikePostResponse {
}

message GetAiLikePostCountRequest {
  uint32 uid = 1; // 用户ID
}

message GetAiLikePostCountResponse {
  uint32 like_post_count = 1; // AI账号点赞的帖子数
}

message AddAiCommentPostRequest {
  uint32 uid = 1; // 用户ID
  string post_id = 2; // 帖子ID
}

message AddAiCommentPostResponse {
}

message GetAiCommentPostCountRequest {
  uint32 uid = 1; // 用户ID
}

message GetAiCommentPostCountResponse {
  uint32 comment_post_count = 1; // AI账号评论的帖子数
}

message AddPostAiCommentRecordRequest {
  string post_id = 1; // 帖子ID
  uint32 ai_uid = 2; // AI账号ID
}

message AddPostAiCommentRecordResponse {
}

message GetPostAiCommentCountRequest {
  string post_id = 1; // 帖子ID
  uint32 ai_uid = 2; // AI账号ID
}

message GetPostAiCommentCountResponse {
  uint32 comment_count = 1; // AI账号对帖子评论的次数
}

message GetPostAiCommentListRequest {
  string post_id = 1; // 帖子ID
}

message GetPostAiCommentListResponse {
  repeated AiCommentData ai_comment_list = 1; // AI账号ID列表
}

message AiCommentData {
  uint32 ai_uid = 1; // AI账号ID
  uint32 num = 2; // 评论次数
}

message GetAIAccountByGenderRequest {
  int32 sex = 1; // 性别
  int32 limit = 2; // 限制数量
}

message GetAIAccountByGenderResponse {
  repeated AIAccount account_list = 1; // AI账号列表
}

// 房间基础配置
message RoomBaseCfg {
  uint32   cid = 1; // 房间id
  uint32   tab_id = 2;// 玩法Id
  string   channel_avatar = 3;// 房间头像
  string   channel_name = 4;// 房间名称
  string   welcome_msg = 5;// 房间欢迎语
  string   channel_topic_title = 6;// 房间话题-标题
  string   channel_topic_detail = 7;// 房间话题-详情
  uint32   channel_supervisor = 8; // 房间超管
  repeated uint32 channel_admin = 9;// 房间管理员
  repeated uint32 lock_mic_list = 10;// 锁定麦位列表
}


message BlockOption {
  uint32 block_id = 1;           //块id
  uint32 elem_id = 2;            //块里面的元素id
  string elem_val = 3;          // 用户填写的值
}

message TimeRange {
  int64 start_time = 1; // 开始时间戳 秒级别
  int64 end_time = 2;   // 结束时间戳 秒级别
}

// 业务配置信息
message BusinessCfg {
  uint32 ai_use_mic_index = 1;// AI使用麦位
  uint32 auto_dismiss_user_num = 2;// 房间人数大于该值取消发布
  uint32 auto_publish_user_num = 3; // 房间人数小于该值自动发布
  repeated TimeRange operation_times = 4;// 运营时间段
  repeated TimeRange publish_times = 5;// 房间发布时间段
  string channel_bgm = 6;// 房间BGM
  repeated BlockOption publish_options = 7;// 发布选项
  string prompt_id = 8; // 泼墨体ID
  string timbre = 9; // 音色ID
  repeated string reception_msg_list = 10;// 进房欢迎内容
  repeated string script = 11;// 脚本
}


message AiRoomCfg {
  uint32 uid = 1; // AI账号ID
  RoomBaseCfg base_cfg = 2; // 房间基础配置
  BusinessCfg business_cfg = 3; // 业务配置信息
}

message UpdateAiRoomCfgRequest {
  AiRoomCfg room_cfg = 1; // 房间配置
}

message UpdateAiRoomCfgResponse {
}

message GetAiRoomCfgRequest {
  uint32 uid = 1; // AI账号ID
}

message GetAiRoomCfgResponse {
  AiRoomCfg room_cfg = 1; // 房间配置列表
}

// 运营时间中的房间配置信息
message GetRunningAiRoomsRequest {
  uint32 last_cid = 1; // 上次请求的最后一个房间ID
  uint32 limit = 2; // 限制数量
}

message GetRunningAiRoomsResponse {
  repeated AiRoomCfg room_cfg_list = 1; // 房间配置列表
  bool load_finish = 2; // 是否有更多
}



message RecordReceptionTimeRequest {
  uint32 uid = 1; // AI账号ID
  uint32 channel_id = 2; // 房间ID
  int64 reception_time = 3; // 接待时间
}

message RecordReceptionTimeResponse {
}

message GetLastReceptionTimeRequest {
  uint32 ai_uid = 1; // AI账号ID
  uint32 channel_id = 2; // 房间ID
}

message GetLastReceptionTimeResponse {
  int64 last_reception_time = 1; // 最近一次接待时间
}

message RecordWaitingReceptionUserRequest {
  uint32 ai_uid = 1; // AI账号ID
  uint32 channel_id = 2; // 房间ID
  uint32 uid = 3; // 用户ID
  int64 record_time = 4; // 进房记录时间
}

message RecordWaitingReceptionUserResponse {
}

message GetWaitingReceptionUserRequest {
  uint32 ai_uid = 1; // AI账号ID
  uint32 channel_id = 2; // 房间ID
}

message GetWaitingReceptionUserResponse {
  repeated WaitingReceptionUser waiting_user_list = 1; // 等待接待用户列表
}

message WaitingReceptionUser {
  uint32 uid = 1; // 用户ID
  int64 record_time = 2; // 进房记录时间
}

message RecordReceptionUserRequest {
  uint32 ai_uid = 1; // AI账号ID
  uint32 channel_id = 2; // 房间ID
  uint32 uid = 3; // 用户ID
}

message RecordReceptionUserResponse {
}

message CheckReceptionUserRequest {
  uint32 ai_uid = 1; // AI账号ID
  uint32 channel_id = 2; // 房间ID
  repeated uint32 uid_list = 3; // 用户ID
}

message CheckReceptionUserResponse {
  map<uint32, bool> reception_map = 1; // 用户ID是否已接待
}

message RecordRoomUserNumRequest {
  uint32 ai_uid = 1; // AI账号ID
  uint32 channel_id = 2; // 房间ID
  uint32 user_num = 3; // 当前房间人数
}

message RecordRoomUserNumResponse {
}

message GetRoomUserNumRequest {
  uint32 ai_uid = 1; // AI账号ID
  uint32 channel_id = 2; // 房间ID
}

message GetRoomUserNumResponse {
  uint32 user_num = 1; // 当前房间人数
}
