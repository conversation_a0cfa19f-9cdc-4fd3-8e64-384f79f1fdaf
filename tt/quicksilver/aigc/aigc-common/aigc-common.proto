syntax = "proto3";

package aigc_common;

option go_package = "golang.52tt.com/protocol/services/aigc/aigc-common";

service AigcCommon {
  // 点赞
  rpc Attitude(AttitudeRequest) returns(AttitudeResponse) {}
  // 获取点赞数
  rpc GetAttitudeCount(GetAttitudeCountRequest) returns(GetAttitudeCountResponse) {}
  // 是否已点赞
  rpc HadAttitude(HadAttitudeRequest) returns (HadAttitudeResponse) {}
  // 句数类型使用次数
  rpc GetSentenceCountMap(GetSentenceCountMapRequest) returns(GetSentenceCountMapResponse) {}
  // 添加句数
  rpc AddSentenceCount(AddSentenceCountRequest) returns(AddSentenceCountResponse) {}
  // 获取用户当日使用的专属句数总数
  rpc GetUserSentenceCount(GetUserSentenceCountReq) returns(GetUserSentenceCountResp) {}

  // 句数改造后
  // 根据业务加已消耗得句数
  rpc AddBussSentenceCount(AddBussSentenceCountRequest) returns(AddBussSentenceCountResponse) {}
  // 根据业务减少已消耗句数
  rpc DecrBussSentenceCount(DecrBussSentenceCountRequest) returns(DecrBussSentenceCountResponse) {}
  // 批量添加句数
  rpc BatAddBussSentenceCount(BatAddBussSentenceCountRequest) returns(BatAddBussSentenceCountResponse) {}
  // 添加额外句数（中台用）
  rpc AddExtraCount(AddExtraCountRequest) returns(AddExtraCountResponse) {}
  // 获取已使用的句数信息
  rpc GetUsedSentenceCount(GetUsedSentenceCountReq) returns(GetUsedSentenceCountResp) {}
  // 消耗句数
  rpc ConsumeSentenceCount(ConsumeSentenceCountRequest) returns(ConsumeSentenceCountResponse) {}
  // 批量获取句数
  rpc BatGetCurSentenceCount(BatGetCurSentenceCountRequest) returns(BatGetCurSentenceCountResponse) {}
  // 根据rewardType获取有效期内额外句数总数
  rpc GetExtraCountByRewardTypes(GetExtraCountByRewardTypesRequest) returns(GetExtraCountByRewardTypesResponse) {}

  // 添加到延迟队列
  rpc AddToDelayQueue(AddToDelayQueueRequest) returns(AddToDelayQueueResponse) {}
}

enum ObjectType {
  OBJECT_TYPE_UNSPECIFIED = 0;
  // 评论
  OBJECT_TYPE_GROUP_TEMPLATE = 1;
}

enum AttitudeOp {
  ATTITUDE_OP_UNSPECIFIED = 0;
  // 点赞
  ATTITUDE_OP_LIKE = 1;
  // 取消点赞
  ATTITUDE_OP_UNLIKE = 2;
}

message Object {
  ObjectType object_type = 1; // 点赞对象类型
  uint32 object_id = 2; // 点赞对象id
}

// 点赞
message AttitudeRequest {
  Object attitude_object = 1; // 点赞对象
  AttitudeOp op = 2; // 点赞操作
}

message AttitudeResponse {
}

message GetAttitudeCountRequest {
  repeated Object objects = 1; // 对象列表
}

message GetAttitudeCountResponse {
  message AttitudeCountInfo {
    Object object = 1; // 点赞对象
    uint32 count = 2; // 点赞数
  }
  repeated AttitudeCountInfo attitude_count_infos = 1; // 点赞数列表
}

message HadAttitudeRequest {
  Object attitude_object = 1; // 点赞对象
}

message HadAttitudeResponse {
  bool had_attitude = 1; // 是否已点赞
}

message Entity {
  enum Type {
    TYPE_UNSPECIFIED = 0;
    TYPE_PARTNER = 1;
    TYPE_MUTI_GROUP = 2; // 多人群聊
    TYPE_SINGLE_GROUP = 3; // 单人群聊
  }
  //e.g:partner_id
  uint32 id = 1;
  // 亲密度主体类型
  Type type = 2;
}

message GetSentenceCountMapRequest {
  // 句子类型
  repeated uint32 type = 1; // 见aigc-soulmate-middle SentenceType
  Entity entity = 2;
  uint32 uid = 3;
}

message GetSentenceCountMapResponse {
  map<uint32, uint32> count_map = 1; // key: SentenceType value: 已使用次数
}

message AddSentenceCountRequest {
  uint32 uid = 1;
  Entity entity = 2;
  uint32 type = 3; // 见aigc-soulmate-middle SentenceType
}

message AddSentenceCountResponse {
}

message GetUserSentenceCountReq {
  uint32 uid = 1;
  uint32 sentence_type = 2;
  Entity.Type entity_type = 3;
}

message GetUserSentenceCountResp {
  uint32 count = 1;
}



message AddBussSentenceCountRequest {
  uint32 uid = 1;
  Entity entity = 2; // deprecated
  uint32 type = 3; // 见aigc-soulmate-middle SentenceType
  uint32 business_type = 4; // 见 aigc-trigger BusinessType
}

message AddBussSentenceCountResponse {
  uint32 cur_num = 1;
}

message DecrBussSentenceCountRequest {
  uint32 uid = 1;
  Entity entity = 2; // deprecated
  uint32 type = 3; // 见aigc-soulmate-middle SentenceType
  uint32 business_type = 4; // 见 aigc-trigger BusinessType
}

message DecrBussSentenceCountResponse {
}

message BatAddBussSentenceCountRequest {
  repeated uint32 uids = 1; // 用户id
  Entity entity = 2; // entity deprecated
  uint32 type = 3; // 见aigc-soulmate-middle SentenceType
  uint32 business_type = 4; // 见 BusinessType
}

message BatAddBussSentenceCountResponse {
  map<uint32, uint32> cur_num_map = 1; // uid -> curNum
}

enum RewardType {
  REWARD_TYPE_UNSPECIFIED = 0;
  REWARD_TYPE_SHARE = 1; // 分享
  REWARD_TYPE_TARGET = 2; // 目标
  REWARD_TYPE_POST = 3; // 发帖
  REWARD_TYPE_PRESENT = 4; // 送礼
}

// 添加额外句数（中台用）
message AddExtraCountRequest {
  uint32 uid = 1;
  uint32 extra_count = 2; // 增加的句数
  int64 expire_time = 3; // 过期时间， 秒级，xx秒后过期
  uint32 business_type = 4; // 见 aigc-trigger BusinessType
  uint32 reward_type = 5; // 奖励类型，见RewardType
}

message AddExtraCountResponse {
}

enum CountType {
  COUNT_TYPE_UNSPECIFIED = 0;
  COUNT_TYPE_TODAY = 1; // 今日聊过的总句数
  COUNT_TYPE_TOTAL = 2; // 累计聊过的总句数
}

// 获取用户使用的句数信息
message GetUsedSentenceCountReq {
  uint32 uid = 1; // 用户id
  uint32 business_type = 2; // 见 aigc-trigger BusinessType
  repeated CountType count_types = 3; // 需要获取的数据类型
}

message GetUsedSentenceCountResp {
  uint32 today_total_count = 1; // 今日聊过的总句数
  uint32 total_count = 2; // 累计聊过的总句数
}


// 消耗句数
message ConsumeSentenceCountRequest {
  uint32 uid = 1;
  Entity entity = 2; // 专属句数有效
  uint32 sentence_type = 3; // 见aigc-soulmate-middle SentenceType
  uint32 business_type = 4; // 见 aigc-trigger BusinessType
  uint32 role_type = 5; // 见aigc-soulmate AIRoleType 今日句数有效
}

message ConsumeSentenceCountResponse {
  uint32 cur_num = 1;
  bool success = 2;
}

message BatGetCurSentenceCountRequest {
  uint32 uid = 1; // 用户id
  Entity entity = 2; // 专属句数有效
  repeated uint32 sentence_type = 3; // 见aigc-soulmate-middle SentenceType
  uint32 business_type = 4; // 见 aigc-trigger BusinessType
  uint32 role_type = 5; // 见aigc-soulmate AIRoleType
  bool need_available_extra_count = 6; // 是否需要返回额外句数的剩余有效句数
}

message BatGetCurSentenceCountResponse {
  // cur_num_map返回今日已使用数量
  map<uint32, uint32> cur_num_map = 1; // sentence_type -> curNum
  uint32 available_extra_num = 2; // 额外句数的剩余有效句数，当need_available_extra_count为true时返回
}

message GetExtraCountByRewardTypesRequest {
  uint32 uid = 1; // 用户id
  repeated uint32 reward_types = 2; // 奖励类型，见RewardType
  uint32 business_type = 3; // 见 aigc-trigger BusinessType
}

message GetExtraCountByRewardTypesResponse {
  map<uint32, uint32> extra_count_map = 1; // reward_type -> extra_count
}

enum PushTo {
  PUSH_TO_UNSPECIFIED = 0;
  PUSH_TO_PARTNER = 1; // 单聊推送
  PUSH_TO_GROUP = 2; // 群聊推送
}

message AddToDelayQueueRequest {
  int64 delay_time = 1; // 延迟时间，单位秒
  bytes data = 2; // 需要延迟的数据，用proto unmarshal
  PushTo push_to = 3; // 业务类型
  bytes receive_msg = 4; // 收到的用户消息见 web_im_logic.proto ImMsg 用proto unmarshal
}

message AddToDelayQueueResponse {
}
