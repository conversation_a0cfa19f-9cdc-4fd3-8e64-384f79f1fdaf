syntax = "proto3";

package aigc_intimacy;

option go_package = "golang.52tt.com/protocol/services/aigc/aigc-intimacy";

service AigcIntimacy {
    // 查询亲密度等级
    rpc GetLevel(GetLevelRequest) returns(GetLevelResponse);
    // 获取亲密度升级条件进度
    rpc GetLevelUpConditionProgress(GetLevelUpConditionProgressRequest) returns(GetLevelUpConditionProgressResponse);

    // ========== 运营后台 ==========
    // 聊天背景配置
    rpc CreateChatBackground(CreateChatBackgroundRequest) returns (CreateChatBackgroundResponse) {}
    rpc UpdateChatBackground(UpdateChatBackgroundRequest) returns (UpdateChatBackgroundResponse) {}
    rpc DeleteChatBackground(DeleteChatBackgroundRequest) returns (DeleteChatBackgroundResponse) {}
    rpc GetChatBackgroundById(GetChatBackgroundByIdRequest) returns (GetChatBackgroundByIdResponse) {}
    rpc GetPageChatBackground(GetPageChatBackgroundRequest) returns (GetPageChatBackgroundResponse) {}
    rpc GetBindChatBackground(GetBindChatBackgroundRequest) returns (GetBindChatBackgroundResponse) {}

    // 关系配置
    rpc CreateRelationship(CreateRelationshipRequest) returns (CreateRelationshipResponse) {}
    rpc UpdateRelationship(UpdateRelationshipRequest) returns (UpdateRelationshipResponse) {}
    rpc DeleteRelationship(DeleteRelationshipRequest) returns (DeleteRelationshipResponse) {}
    rpc GetRelationshipList(GetRelationshipListRequest) returns (GetRelationshipListResponse) {}

    // 亲密度等级配置
    rpc CreateIntimacyLevelConf(CreateIntimacyLevelConfRequest) returns (CreateIntimacyLevelConfResponse) {}
    rpc UpdateIntimacyLevelConf(UpdateIntimacyLevelConfRequest) returns (UpdateIntimacyLevelConfResponse) {}
    rpc DeleteIntimacyLevelConf(DeleteIntimacyLevelConfRequest) returns (DeleteIntimacyLevelConfResponse) {}
    rpc GetIntimacyLevelConfList(GetIntimacyLevelConfListRequest) returns (GetIntimacyLevelConfListResponse) {}

    // 亲密度升级条件配置
    rpc CreateIntimacyCondition(CreateIntimacyConditionRequest) returns (CreateIntimacyConditionResponse) {}
    rpc UpdateIntimacyCondition(UpdateIntimacyConditionRequest) returns (UpdateIntimacyConditionResponse) {}
    rpc DeleteIntimacyCondition(DeleteIntimacyConditionRequest) returns (DeleteIntimacyConditionResponse) {}
    rpc GetIntimacyConditionList(GetIntimacyConditionListRequest) returns (GetIntimacyConditionListResponse) {}
    // 运营后台 end

    // 绑定关系
    rpc GetBindBackground(GetBindBackgroundRequest) returns (GetBindBackgroundResponse) {}
    rpc GetAvailableBackground(GetAvailableBackgroundRequest) returns (GetAvailableBackgroundResponse) {}
    rpc SwitchChatBackground(SwitchChatBackgroundRequest) returns (SwitchChatBackgroundResponse) {}
    rpc SwitchRelation(SwitchRelationRequest) returns (SwitchRelationResponse) {}
    rpc GetRelationsByEntity(GetRelationsByEntityRequest) returns (GetRelationsByEntityResponse) {}
    rpc BatchGetCurRelation(BatchGetCurRelationRequest) returns (BatchGetCurRelationResponse) {}

    // 读心次数
    rpc GetReadHeartCount(GetReadHeartCountRequest) returns (GetReadHeartCountResponse) {}
    // 获取当前权益配置
    rpc GetBenefitConfig(GetBenefitConfigRequest) returns (GetBenefitConfigResponse) {}

    // 聊天背景
    rpc GrantChatBackground(GrantChatBackgroundRequest) returns (GrantChatBackgroundResponse) {}
    rpc GenerateChatBackground(GenerateChatBackgroundRequest) returns (GenerateChatBackgroundResponse) {}
}

// 聊天背景类型
enum ChatBackgroundType {
    CHAT_BACKGROUND_TYPE_UNSPECIFIED = 0;
    // 亲密度
    CHAT_BACKGROUND_TYPE_INTIMACY = 1;
    // 送礼
    CHAT_BACKGROUND_TYPE_PRESENT = 2;
}

message Entity {
    enum Type {
        TYPE_UNSPECIFIED = 0;
        TYPE_PARTNER = 1;
        TYPE_MUTI_GROUP = 2; // 多人群聊
        TYPE_SINGLE_GROUP = 3; // 单人群聊
    }

    // 亲密度主体ID e.g:partner_id
    uint32 id = 1;
    // 亲密度主体类型
    Type type = 2;
}

message LevelUpConditionProgress {
    message LevelUpCondition {
        string id = 1;
        string icon = 2;
        string desc = 3;
        string title = 4;
    }
    
    // 关联的升级条件
    LevelUpCondition condition = 1;
    // 完成条件增长的亲密值
    uint32 growth_value = 2;
}

message GetLevelRequest {
    uint32 uid = 1;
    Entity entity = 2;
}

message GetLevelResponse {
    // 当前亲密度等级
    uint32 level = 1;
    // 当前亲密度值
    uint32 value = 2;

    // 开始计算亲密度的时间
    int64 started_at = 3;
}

message GetLevelUpConditionProgressRequest {
    uint32 uid = 1;
    Entity entity = 2;
}

message GetLevelUpConditionProgressResponse {
    repeated LevelUpConditionProgress list = 1;
    
    // 今日增长的亲密度
    uint32 today_growth_value = 2;
}

// ========== 聊天背景配置（ChatBackground） ==========

message ChatBackground {
    enum BindEntityType {
        BIND_ENTITY_TYPE_UNSPECIFIED = 0;
        // 绑定所有角色
        BIND_ENTITY_TYPE_ALL_ROLE = 1;
        // 绑定指定角色
        BIND_ENTITY_TYPE_ROLE_LIST = 2;
    }

    string id = 1;
    // 背景名称
    string name = 2;
    // 背景图链接
    string image_url = 3;
    // 是否展示头像
    bool show_avatar = 4;
    // 是否默认拥有背景
    bool default_unlock = 5;
    // 外显解锁条件
    string unlock_condition = 6;
    // 绑定的对象类型
    BindEntityType bind_entity_type = 7;
    // 绑定的对象ID列表
    repeated uint32 bind_entity_ids = 8;
    // 背景类型
    ChatBackgroundType type = 9;
}

message BindEntity {
    enum EntityType {
        ENTITY_TYPE_UNSPECIFIED = 0;
        ENTITY_TYPE_ROLE = 1;
    }
    EntityType type = 1;
    uint32 id = 2;
}

message CreateChatBackgroundRequest {
    ChatBackground background = 1;
}

message CreateChatBackgroundResponse {
}

message UpdateChatBackgroundRequest {
    ChatBackground background = 1;
}

message UpdateChatBackgroundResponse {
}

message DeleteChatBackgroundRequest {
    string id = 1;
}

message DeleteChatBackgroundResponse {
}

message GetChatBackgroundByIdRequest {
    repeated string id_list = 1;
}

message GetChatBackgroundByIdResponse {
    repeated ChatBackground backgrounds = 1;
}

message GetPageChatBackgroundRequest {
    uint32 page = 1;
    uint32 size = 2;
    bool need_count = 3;
    string name = 4; // 根据背景名称搜索
    ChatBackgroundType type = 5;
}

message GetPageChatBackgroundResponse {
    repeated ChatBackground backgrounds = 1;
    int64 total = 2;
}

// 获取角色绑定的聊天背景
message GetBindChatBackgroundRequest {
    BindEntity entity = 1;
    ChatBackgroundType type = 2;
}

message GetBindChatBackgroundResponse {
    repeated ChatBackground backgrounds = 1;
}


// ========== 关系配置（Relationship） ==========

message Relationship {
    string id = 1;
    // 关系名称
    string name = 2;
    // 关系参数（传输模型理解）
    string aigc_params = 3;
    // 关联的聊天背景ID
    string bind_background_id = 4;
    // 外显解锁条件
    string unlock_condition = 5;
    // 外显icon
    string icon = 6;
    // 亲密度页面展示背景图
    string intimacy_background_img = 7;
}

message CreateRelationshipRequest {
    Relationship relationship = 1;
}

message CreateRelationshipResponse {
}

message UpdateRelationshipRequest {
    Relationship relationship = 1;
}

message UpdateRelationshipResponse {
}

message DeleteRelationshipRequest {
    string id = 1;
}

message DeleteRelationshipResponse {
}

message GetRelationshipListRequest {
    // 获取所有
    bool get_all = 1;
    // 指定ID列表查询
    repeated string id_list = 2;
    // 获取绑定的聊天背景
    bool get_bind_info = 3;
}

message GetRelationshipListResponse {
    repeated Relationship relationships = 1;
    map<string, ChatBackground> background_info_map = 2;
}


// ========== 亲密度等级配置（IntimacyLevelConf） ==========

message LevelBenefits {
    // 角色额外句数
    uint32 extra_send_msg_count = 1;
    // 角色额外读心次数
    uint32 extra_read_mind_count = 2;
    // 解锁可绑定关系ID
    repeated string unlock_relationships = 3;
    // 解锁可使用聊天背景ID
    repeated string unlock_backgrounds = 4;
}


message IntimacyLevelConf {
    uint32 id = 1;
    // 等级
    uint32 level = 2;
    // 所需亲密度
    uint32 require_value = 3;
    // 解锁权益
    LevelBenefits benefits = 4;
    // 展示给用户的权益文案
    string benefit_desc = 5;
}

message CreateIntimacyLevelConfRequest {
    IntimacyLevelConf level_config = 1;
}

message CreateIntimacyLevelConfResponse {
}

message UpdateIntimacyLevelConfRequest {
    IntimacyLevelConf level_config = 1;
}

message UpdateIntimacyLevelConfResponse {
}

message DeleteIntimacyLevelConfRequest {
    uint32 id = 1;
}

message DeleteIntimacyLevelConfResponse {
}

message GetIntimacyLevelConfListRequest {
    // 获取所有
    bool get_all = 1;
    // 指定ID列表查询
    repeated uint32 id_list = 2;
    // 获取绑定的关系和背景
    bool get_bind_info = 3;
}

message GetIntimacyLevelConfListResponse {
    repeated IntimacyLevelConf config_list = 1;
    map<string, ChatBackground> background_info_map = 2;
    map<string, Relationship> relationship_info_map = 3;
}


// ========== 亲密度升级条件（IntimacyCondition） ==========

enum ConditionType {
    CONDITION_TYPE_UNSPECIFIED = 0;
    // 聊天句数
    CONDITION_TYPE_CHAT_MSG_COUNT = 1;
    // 聊天天数
    CONDITION_TYPE_CHAT_DAY = 2;
}

message IntimacyCondition {
    string id = 1;
    // 条件类型
    ConditionType condition_type = 2;
    // 达成数量
    uint32 require_count = 3;
    // 亲密度增加值
    uint32 value_add = 4;
    // 每日达成次数上限
    uint32 daily_limit = 5;
    // 外显icon
    string icon = 6;
    // 外显标题
    string title = 7;
    // 外显描述
    string desc = 8;
}

message CreateIntimacyConditionRequest {
    IntimacyCondition condition = 1;
}

message CreateIntimacyConditionResponse {
}

message UpdateIntimacyConditionRequest {
    IntimacyCondition condition = 1;
}

message UpdateIntimacyConditionResponse {
}

message DeleteIntimacyConditionRequest {
    string id = 1;
}

message DeleteIntimacyConditionResponse {
}

message GetIntimacyConditionListRequest {
    // 获取所有
    bool get_all = 1;
    // 指定ID列表查询
    repeated string id_list = 2;
}

message GetIntimacyConditionListResponse {
    repeated IntimacyCondition conditions = 1;
}

message GetBindBackgroundRequest {
    Entity entity = 1;

}

message GetBindBackgroundResponse {
    ChatBackground cur_background = 1;
}



message GetAvailableBackgroundRequest {
    Entity entity = 1;
    BindEntity bind_entity = 2;
    uint32 uid = 3;
}

message GetAvailableBackgroundResponse {
    ChatBackground cur_background = 1; // 当前背景
    repeated ChatBackground un_lock_backgrounds = 2; // 已解锁背景
    repeated ChatBackground lock_backgrounds = 3; // 未解锁背景
}

message SwitchChatBackgroundRequest {
    Entity entity = 1;
    string background_id = 2; // 背景id
    enum Op {
      OP_UNSPECIFIED = 0;
      OP_SWITCH = 1;
      OP_DEL = 2;
    }
    Op op = 3;
   BindEntity bind_entity = 4;

}

message SwitchChatBackgroundResponse {
}

message SwitchRelationRequest{
    Entity entity = 1;
    string relation_id = 2; // 关系id
    enum Op {
      OP_UNSPECIFIED = 0;
      OP_SWITCH = 1;
      OP_DEL = 2;
    }
    Op op = 3;
}

message SwitchRelationResponse{
}

message GetRelationsByEntityRequest {
    Entity entity = 1;
    uint32 uid = 2;
}

message GetRelationsByEntityResponse {
    Relationship cur_relation = 1; // 当前关系
    repeated Relationship un_lock_relations = 2; // 已解锁关系
    repeated Relationship lock_relations = 3; // 未解锁关系
}

message GetReadHeartCountRequest {
    Entity entity = 1;
    uint32 uid = 2;
}

message  GetReadHeartCountResponse {
    uint32 read_heart_count = 1; // 读心次数
}

// 升级通知
message LevelUpNotify {
    uint32 uid = 1;
    Entity entity = 2;

    // 最新等级
    uint32 level = 3;
    // 最新亲密值
    uint32 value = 4;

    // 当前等级亲密值范围
    uint32 min_value = 6;
    uint32 max_value = 7;

    // 是否升级了
    bool level_upgraded = 8;

    // 触发时间(秒)
    int64 triggered_at = 9;
}

message BatchGetCurRelationRequest {
    repeated Entity entity_list = 1;
}

message BatchGetCurRelationResponse {
    message CurRelationBindInfo {
        Entity entity = 1;
        Relationship relation = 2;
    }
    repeated CurRelationBindInfo cur_relations = 1;
}

message GetBenefitConfigRequest {
    Entity entity = 1;
    uint32 uid = 2;
}

message GetBenefitConfigResponse {
    LevelBenefits benefits = 1;
    uint32 cur_level = 2;
}

message GrantChatBackgroundRequest {
    uint32 uid = 1;
    string bg_id = 2;

    BindEntity entity = 3;
}

message GrantChatBackgroundResponse {
}

message GenerateChatBackgroundRequest {
    message Background {
        // 背景名称
        string name = 1;
        // 是否展示头像
        bool show_avatar = 2;
        // 外显解锁条件
        string unlock_condition = 3;
        // 风格id
        uint32 style_id = 4;
    }

    // 绑定主体
    BindEntity entity = 1;
    // 主体的背景图
    string entity_bg = 2;

    // 背景参数
    repeated Background backgrounds = 3;
}

message GenerateChatBackgroundResponse {
}
