syntax="proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package ugc.content;

option go_package = "golang.52tt.com/protocol/services/ugc/content";

service UgcContent {
    // 通用
    // 帖子置顶
    rpc AddStickyContent(AddStickyContentReq) returns (AddStickyContentResp) {}
    rpc RemoveStickyContent(RemoveStickyContentReq) returns (RemoveStickyContentResp) {}
    rpc GetStickyContent(GetStickyContentReq) returns (GetStickyContentResp) {}
    rpc BatchGetStickyPost(BatchGetStickyPostReq) returns (BatchGetStickyPostResp) {}
    // 修改帖子/评论 的置顶字段
    rpc UpdateContentStickyStatus (UpdateContentStickyStatusReq) returns (UpdateContentStickyStatusResp) {}
    rpc UpdateAttachmentPrivacy (UpdateAttachmentPrivacyReq) returns (UpdateAttachmentPrivacyResp) {}

    // 帖子
    rpc BatchGetPostListById (BatchGetPostListByIdReq) returns (BatchGetPostListByIdResp) {}
    rpc AddPost (AddPostReq) returns (AddPostResp) {}
    rpc MarkAttachmentUploaded (MarkAttachmentUploadedReq) returns (MarkAttachmentUploadedResp) {}
    rpc GetPostById (GetPostByIdReq) returns (GetPostByIdResp) {}
    rpc DelPost (DelPostReq) returns (DelPostResp) {}
    rpc ReportPostView (ReportPostViewReq) returns (ReportPostViewResp) {}
    rpc ReportPostViewV2 (ReportPostViewV2Req) returns (ReportPostViewV2Resp) {}
    rpc ReportPostShare (ReportPostShareReq)returns (ReportPostShareResp) {}
    rpc MarkFirstPost (MarkFirstPostReq)returns (MarkFirstPostResp) {}
    rpc UpdatePostPrivacyPolicy (UpdatePostPrivacyPolicyReq) returns (UpdatePostPrivacyPolicyResp) {}
    rpc GetStrictUserPostedCount(GetStrictUserPostedCountReq) returns (GetStrictUserPostedCountResp) {}
    rpc SetUserNewestPost(SetUserNewestPostReq) returns (SetUserNewestPostResp) {}
    rpc BatchGetUserNewestPost(BatchGetUserNewestPostReq) returns (BatchGetUserNewestPostResp) {}
    // 设置风控检查结果
    rpc SetPostRiskCheckInfo (SetPostRiskCheckInfoReq) returns (SetPostRiskCheckInfoResp) {}

    //topic展示
    rpc ReportTopicViewCount(ReportTopicViewCountReq)returns (ReportTopicViewCountRsp){}
    rpc GetTopicViewCount(GetTopicViewCountReq)returns (GetTopicViewCountRsp){}
    //评论
    rpc AddComment (AddCommentReq) returns (AddCommentResp) {}
    rpc GetCommentList (GetCommentListReq) returns (GetCommentListResp) {}
    rpc GetCommentById (GetCommentByIdReq) returns (GetCommentByIdResp) {}
    rpc BatchGetCommentByIds (BatchGetCommentByIdsReq) returns (BatchGetCommentByIdsResp) {}
    rpc DelComment (DelCommentReq) returns (DelCommentResp) {}

    //点赞
    rpc AddAttitude (AddAttitudeReq) returns (AddAttitudeResp) {}
    rpc DelAttitude (DelAttitudeReq) returns (DelAttitudeResp) {}
    rpc GetAttitudeUserList (GetAttitudeUserListReq) returns (GetAttitudeUserListResp) {}
    rpc GetUserWonAttitudeCount (GetUserWonAttitudeCountReq) returns (GetUserWonAttitudeCountResp) {}
    rpc CheckUserAttitudeAvailable (CheckUserAttitudeAvailableReq) returns (CheckUserAttitudeAvailableResp) {}    /*查看用户是否可以点赞*/

    // -------- 运营后台 --------
    // 审核
    rpc UpdateAttachmentStatus(UpdateAttachmentStatusReq) returns (UpdateAttachmentStatusResp) {}
    // 比如说官方发帖, 跳过等待审核这一步
    rpc AddPostDirectly (AddPostDirectlyReq) returns (AddPostDirectlyResp) {}
    // 封贴
    rpc BanPostById (BanPostByIdReq) returns (BanPostByIdResp) {} // 运营后台在用
    // 封评论
    rpc BanCommentById (BanCommentByIdReq) returns (BanCommentByIdResp) {}
    // 更新视频的url地址
    rpc UpdateVideoUrl (UpdateVideoUrlReq) returns (UpdateVideoUrlResp) {}
    // 配置特殊标签
    rpc UpdatePostSpecialLabel (UpdatePostSpecialLabelReq) returns (UpdatePostSpecialLabelResp) {}

    // 举报
    rpc AppReport (AppReportReq) returns (AppReportResp) {}
    // 应该是给推荐系统用的打标签
    rpc UpdatePostTags (UpdatePostTagsReq) returns (UpdatePostTagsResp) {}
    // 根据条件查询帖子列表
    rpc GetPostsByFilter (GetPostsByFilterReq) returns (GetPostsByFilterResp) {}
    // 获取帖子标签
    rpc GetPostTags (GetPostTagsReq) returns (GetPostTagsResp) {}
    rpc BatchGetPostTags (BatchGetPostTagsReq) returns (BatchGetPostTagsResp) {}

    rpc GetCommentsByFilter ( GetCommentsByFilterReq ) returns ( GetCommentsByFilterResp ) {}

    //根据浏览数来排序
    rpc GetPostsByTimeSortViewCnt(GetPostsByTimeSortViewCntReq)returns(GetPostsByTimeSortViewCntResp){}

    /*批量修改提权*/
    rpc BatUpdateWeight (BatUpdateWeightReq) returns (BatUpdateWeightResp) {}
    /*批量获取所有提权帖子*/
    rpc BatGetPostWeight (BatGetPostWeightReq) returns (BatGetPostWeightResp) {}
    rpc BatGetPostAndWeight (BatGetPostAndWeightReq) returns (BatGetPostAndWeightResp) {}
    rpc UpdatePostMachineAuditById (UpdatePostMachineAuditByIdReq) returns (UpdatePostMachineAuditByIdRsp) {}

    /*推荐流强插帖子*/
    rpc SetForcePostInUserRcmdFeed (SetForcePostInUserRcmdFeedReq) returns (SetForcePostInUserRcmdFeedResp) {}
    rpc GetForcePostInUserRcmdFeed (GetForcePostInUserRcmdFeedReq) returns (GetForcePostInUserRcmdFeedResp) {}
    rpc DelForcePostInUserRcmdFeed (DelForcePostInUserRcmdFeedReq) returns (DelForcePostInUserRcmdFeedResp) {}
    /*app 请求*/
    rpc GetForcePostsInUserRcmdFeed (GetForcePostsInUserRcmdFeedReq) returns (GetForcePostsInUserRcmdFeedResp) {}

    // 根据话题搜索帖子，运营后台用
    rpc SearchPostByTopic (SearchPostByTopicReq) returns (SearchPostByTopicResp) {}
    // 帖子关联指定话题，运营后台用
    rpc AssociatePostWithTopic (AssociatePostWithTopicReq) returns (AssociatePostWithTopicResp) {}

    rpc AddPostDirectlyByBuss (AddPostDirectlyByBussReq) returns (AddPostDirectlyByBussResp) {}
    rpc UpdatePostGeneralContents (UpdatePostGeneralContentsReq) returns (UpdatePostGeneralContentsResp) {}

    // 批量根据定位的topicId获取其地理位置信息，提供该接口给推荐查询数据进行回溯
    rpc BatGetLocationByGeoTopicId(BatGetLocationByGeoTopicIdReq) returns (BatGetLocationByGeoTopicIdResp) {}

    //私域帖子转换广场帖子
    rpc NonPublicPostConversionSquarePost(NonPublicPostConversionSquarePostRequest)returns(NonPublicPostConversionSquarePostResponse) {}

    rpc SearchConversionNonPublicPost(SearchConversionNonPublicPostRequest)returns(SearchConversionNonPublicPostResponse){} //搜索转换讨论贴的配置


    rpc BatchConversionNonPublicPost(BatchConversionNonPublicPostRequest)returns(BatchConversionNonPublicPostResponse){}

    rpc BatchConversionNonpublicPostRecordsByPostIds(BatchConversionNonpublicPostRecordsByPostIdsRequest)returns(BatchConversionNonpublicPostRecordsByPostIdsResponse){}

  /*更新专区帖子的话题ID*/
  rpc UpdateZonePostTopicIdList (UpdateZonePostTopicIdListRequest) returns (UpdateZonePostTopicIdListResponse) {}

    // 获取图片/视频上传token
    rpc GetAttachmentUploadToken (GetAttachmentUploadTokenReq) returns (GetAttachmentUploadTokenResp) {}
}








enum ContentType {
    FORMATTED = 0;
    ORIGIN_TEXT = 1;
}

message AttachmentInfo {
    enum AttachmentType {
        NONE = 0;
        IMAGE = 1;
        GIF = 2;
        VIDEO = 3;
        CMS = 4; // cms帖子
        AUDIO = 5;
        TEXT = 6; //文本
        URLCard = 7;//链接
        Vote = 8; //投票
        AtUser=9;//私域帖子at用户
        AtSocialCommunity=10;//私域帖子at社群
    }
    string key = 1;
    AttachmentType type = 2; // 附件类型
    string content = 3; // 附件内容,一般来说是url
    string extra = 4; // 客户端自己玩
    ContentStatus status = 5;
    string vm_content = 6; // 水印的字段

    // 给type == video用的, 转码参数
    string param = 10;
    //原始视频封面url
    string origin_video_cover = 11;
    //    //extra
    //    Extra extra_new = 12;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message PostInfo {
    enum PostType {
        NONE = 0;
        TEXT = 1;
        IMAGE = 2;
        VIDEO = 3;
        CMS = 4; // cms
        AUDIO = 5;
    } // must be same as ugc_.proto
    string post_id = 1;
    string topic_id = 2;
    PostType post_type = 3;
    string content = 4;
    repeated AttachmentInfo attachments = 5;
    uint64 create_at = 6; // 发帖时间, unix second
    uint32 comment_count = 7;
    uint32 attitude_count = 8;
    uint32 view_count = 9; // 浏览量
    uint32 user_id = 10;
    ContentStatus status = 11;
    uint32 top_level_comment_count = 12; //一级评论的数量
    uint32 share_count = 13; // 分享数
    string label = 14; // 运营配置的特殊label
    uint32 magnified_view_count = 15;   // 扩大后的view_count

    string sub_topic_id = 16;//话题id
    repeated uint32 tags = 17; // 推荐的tag
    int32 content_type = 18;    //内容的类型，区分content里是否包含自定义富文本的内容
    StickyStatus sticky_status = 19;

    // TT5.4.0 地理位置信息
    string geo_topic_id = 20; //
    string lng_lat = 21; //
    string full_geo_name = 22; // for display
    AttachmentDownloadPrivacy privacy = 23;
    PostOrigin post_origin = 24;
    // TT5.4.2
    PostPrivacyPolicy privacy_policy= 25; // 隐私策略
    repeated string diy_topic_ids = 26;    //个人话题

    /*from rcmd_post.proto*/
    bool is_in_high_interaction = 27;  // 高互动
    float std_score = 28; // 这两个主要是算分数用的 广场排序分
    float system_recommend_stream_ranking_position = 29;  // 推荐流推荐分排名占比 广场排序占比
    PostMachineStatus machine_status = 30;
    MoodInfo mood_info = 31;
    int32 second_label_id = 32;
    string label_level = 33;
    bool is_obs = 34;
    bool is_anchor_feed = 35;
    repeated string manual_topic_ids = 36; //运营手动添加的话题列表

    // 帖子是否带有投票信息
    bool is_vote = 37;

    // 发帖人IP归属地
    Location ip_loc = 38;

    repeated GeneralContent general_contents = 39;
    string title = 40;
    uint32 business_type = 41;  // 业务类型 -> BusinessType
    repeated CommonTopicInfo common_topic_infos = 42; // 通用话题 通过话题类型来识别业务，不要用心情，城市那套
    bool   IsConverted = 43;//   是否是转换来的  false 未转换  true 已转换

    ZonePolicy zone_policy = 44; // 开黑专区隐私策略

    RiskCheckInfo risk_check_info = 45; // 风控检查信息
}

message RiskCheckInfo {
    string risk_token = 1; // 风控token,送审时带上
    int32 err_code = 2; // 错误码
    string err_msg = 3; // 错误信息
}

message CommentInfo {
    string comment_id = 1;
    string post_id = 2;
    string conversation_id = 3;
    string content = 4;
    uint32 user_id = 5;
    uint32 reply_to_user_id = 6;
    repeated AttachmentInfo attachments = 7;
    uint32 comment_count = 8; //评论数量
    uint32 attitude_count = 9;
    ContentStatus status = 10;
    repeated CommentInfo sub_comments = 11; // 子评论数量, 这里只会返回前N条
    uint64 create_at = 12;
    string reply_to_comment_id = 13;    // 被评论或者回复的评论id
    int32 content_type = 14;    //内容的类型，区分content里是否包含自定义富文本的内容
    StickyStatus sticky_status = 15;
    bool is_obs = 16;
    uint32 step_count = 17;
    Location ip_loc = 18; // 评论人IP归属地
    bool only_show_self = 19;
}

// 地理位置信息
message Location {
    string country_code = 1;
    string country = 2;

    string province_code = 3;
    string province = 4;

    string city_code = 5;
    string city = 6;
}

message BatchGetPostListByIdReq {
    repeated string post_id_list = 1;
    ContentType content_type = 2;
    bool need_exact_info = 3;
    uint32 market_id = 4;
    uint32 client_type = 5;
}

message BatchGetPostListByIdResp {
    repeated PostInfo post_list = 1;
}

message AddPostDirectlyReq {
    enum Availability {
        ALL = 0;
        ANDROID = 1;
        IOS = 2;
    }
    uint32 user_id = 1;
    string topic_id = 2;
    PostInfo.PostType type = 3;
    string content = 4;
    repeated AttachmentInfo attachments = 5;
    uint64 create_at = 6; // 自定义发帖时间, 传0则表示当前时间
    uint32 availability_mask = 7; // 可见性掩码, 屏蔽平台用
    string sub_topic_id = 8;
    int32 content_type = 9;
    PostOrigin post_origin = 10; // 发帖来源,普通/破冰/活动 什么的
    string ttid = 11;
    string vm_text = 12;//水印
    uint32 business_type = 13;  // 业务类型 -> BusinessType
    repeated GeneralContent general_contents = 14;  //通用内容
    repeated CommonTopicInfo common_topic_infos = 15; //通用话题信息
    ZonePolicy zone_policy = 16; // 开黑专区隐私策略
    string post_id = 17; // 运营后台ai账号发帖时传入的post_id, 用于覆盖原有的post_id
}

message AddPostDirectlyResp {
    string post_id = 1;
}

enum PostOrigin {
    POST_ORIG_NORMAL = 0;
    POST_ORIN_ICE_BREAK = 1; // 破冰
    POST_ORIN_ACT = 2; // 活动页
    POST_ORIG_REGISTER = 3; //进驻动态
    POST_ORIG_KOL = 4;
    POST_ORIG_TGL = 5;
    POST_ORIG_TGL_COVER = 6;
    POST_ORIG_AI_RAPER = 7;   //ai raper
    POST_ORIG_AI_FOREIGN_LANGUAGE = 8;   //ai 外语
    POST_ORIG_GENERAL_CONTENT = 9;   //通用帖子内容，里面有个content_origin再区分
    POST_ORIG_GAME_ACTIVITY = 10; // 开黑活动发帖
    POST_ORIG_AI_ACCOUNT = 11; // 运营后台AI账号发帖

    POST_ORIG_ANCIENT = 1000; // // 旧版本发的认为无效, 不进推荐
    POST_ORIG_NON_PUBLIC_CONVERSION=1001; //私域帖子转化
}

enum IsSystem {
    POST_BY_PEOPLE = 0;
    POST_BY_SETTING_TAG = 1; //用户首次设置标签
}

enum UnmarshalType {
    UNMARSHAL_TYPE_DEFAULT = 0;
    UNMARSHAL_TYPE_PROTOBUF = 1; //protobuf
    UNMARSHAL_TYPE_JSON = 2; //json
}

enum ContentOrigin {
    ORIGIN_DEFAULT = 0;
    ORIGIN_GAME_DISTRICT = 1; //游戏专区
    ORIGIN_SOCIAL_COMMUNITY_DISTRICT = 2; //社群专区
}

message GeneralContent {
    uint32 unmarshal_type = 1;  //UnmarshalType
    uint32 content_origin = 2;  //ContentOrigin
    bytes content = 3;
    string origin_name = 4;  //client写入时用于识别记录业务名
}

//写帖子通用内容
message AddPostDirectlyByBussReq {
    uint32 user_id = 1;
    PostInfo.PostType type = 2;    //识别帖子内容（文本，图片，视频），有需求扫描时经常要扫出相关帖子
    uint64 create_at = 3; // 自定义发帖时间, 传0则表示当前时间
    repeated string diy_topic_ids = 4;  //话题列表
    MoodInfo mood_info = 5;  //心情
    repeated GeneralContent general_contents = 6;  //通用内容
    uint32 business_type = 7; //业务类型  0:通用广场 1:开黑 -> BusinessType
    repeated CommonTopicInfo common_topic_infos = 8; //通用话题信息
    ZonePolicy zone_policy = 9; // 开黑专区隐私策略
}

//返回帖子通用内容
message AddPostDirectlyByBussResp {
    string post_id = 1;
}

//更新帖子通用内容
message UpdatePostGeneralContentsReq {
    string post_id = 1;
    uint32 uid = 2;
    repeated GeneralContent general_contents = 3;
}

//更新帖子通用内容
message UpdatePostGeneralContentsResp {
}

// 通用话题 通过话题类型来识别业务，不要用心情，城市那套
message CommonTopicInfo {
    repeated string topic_ids = 1;
    uint32 topic_type = 2;  //topic.proto -> TopicType
}

enum BusinessType {
    BUSINESS_TYPE_DEFAULT = 0;
    BUSINESS_TYPE_CHANNEL_PLAY_GAME = 1; //房间游戏玩法，该类型对应GeneralContent看game-ugc-content.proto下的GamePostBussInfo
}

message AddPostReq {
    uint32 user_id = 1;
    string topic_id = 2; // 主题id
    PostInfo.PostType type = 3;
    string content = 4;
    uint32 attachment_image_count = 5;
    uint32 attachment_video_count = 6;
    ContentStatus status = 7;
    string antispam_label_info = 8; //文本审核的结果, 传过去由svr发事件,应该不落DB

    string sub_topic_id = 9; // 话题id
    int32 content_type = 10;
    PostOrigin post_origin = 11; // 发帖来源,普通/破冰/活动 什么的

    uint32 attachment_audio_count = 12;

    repeated AttachmentInfo predefined_attachments = 13;

    string ttid = 14;//现在是水印在用 是视频水印的组成部分


    string geo_topic_id = 15; //
    string lng_lat = 16; //
    string full_geo_name = 17; // for display
    AttachmentDownloadPrivacy privacy = 18;

    // 给数据中心用的
    string device_id = 20;
    uint32 platform = 21; // 1: iOS, 2: Android
    repeated uint32 tags = 22;

    //extra
    Extra extra = 30;
    //水印
    string vm_text = 31;
    // 隐私策略
    PostPrivacyPolicy privacy_policy= 32;

    repeated string diy_topic_ids = 33;
    uint32 client_ip = 34;
    MoodInfo mood_info = 35;
    bool is_obs = 36; //发贴走obs
    bool is_anchor_feed = 37;

    // 是否带有投票信息的帖子
    bool is_vote = 38;

    // 发帖人的IP归属地
    Location ip_loc = 39;
    uint32 market_id = 40;
    string title = 41;

    uint32 business_type = 42; //业务类型  0:通用广场 1:开黑 -> BusinessType
    repeated GeneralContent general_contents = 43;  //通用内容
    repeated CommonTopicInfo common_topic_infos = 44; //通用话题信息

    ZonePolicy zone_policy = 45; // 开黑专区隐私策略
}

message MoodInfo {
    string mood_id = 1;
    string mood_bind_topic_id = 2; /* 心情绑定的topicid */
    string mood_name = 3;
}

message Extra {
    uint32 width = 1;
    uint32 heigth = 2;
}


message AddPostResp {
    string post_id = 1;
    ContentStatus status = 2;
    uint64 post_create_at = 3;      // 帖子的创建时间

    repeated string attachment_image_keys = 10;
    repeated string attachment_video_keys = 11;
    string image_upload_token = 12;
    string video_upload_token = 13; //目前来看一个token只能对应一个视频, 所以一次暂时只能上传一个视频

    repeated string attachment_audio_keys = 14;
    string audio_upload_token = 15;
}

enum AttachmentDownloadPrivacy {
    PRIVACY_DEFAULT = 0;
    PRIVACY_PRIVATE = 1;
    PRIVACY_PUBLIC = 2;
}

enum PostPrivacyPolicy {
    POST_PRIVACY_POLICY_DEFAULT = 0; // 默认是公开的
    POST_PRIVACY_POLICY_PRIVATE = 1; // 仅自己可见
}

enum ZonePolicy {
    DEFAULT = 0; // 默认是公开的
    ONLY_ZONE = 1; // 仅专区可见
}

enum ContentStatus {
    CONTENT_STATUS_NONE = 0;
    CONTENT_STATUS_UNDER_REVIEW = 1; // 文字审核过了, 等待图片审核
    CONTENT_STATUS_UNDER_REVIEW_AND_PREPARED = 2; // 客户端已经上报图片上传完毕, 等待审核
    CONTENT_STATUS_SUSPICIOUS = 3;
    CONTENT_STATUS_ILLEGAL = 4;
    CONTENT_STATUS_NORMAL = 5;
    CONTENT_STATUS_DELETED = 6; // 这个值要改大一点
    CONTENT_STATUS_BANNED = 7; // 由官方操作的封禁
}

// 更新帖子/评论 的附件的状态
message MarkAttachmentUploadedReq {
    string post_id = 1;
    string comment_id = 2;
    repeated AttachmentInfo attachment_info_list = 3;
}

message MarkAttachmentUploadedResp {
    uint64 post_create_at = 1;      // 帖子的创建时间
}

message BanPostByIdReq {
    string post_id = 1;
    bool is_ban = 2; // true表示封禁, false表示解除封禁, 注意: 如果帖子内容本身审核不通过, 调用解封的话会直接讲帖子变成已审核状态
    int32 second_label_id = 3; //二级标签
    string second_label_level = 4; //等级
}

message BanPostByIdResp {
}

message DelPostReq {
    string post_id = 1;
}

message DelPostResp {

}

message GetPostByIdReq {
    string post_id = 1;
    ContentType content_type = 2;
    uint32 query_by_user_id = 4;
    uint32 market_id = 5;
    uint32 client_type = 6;
}

message GetPostByIdResp {
    PostInfo post = 1;
}

message GetVMVideoExistReq {
    string post_id = 1;
    string key = 2;
}

message GetVMVideoExistResp {
    string vm_url = 1;
    bool exsits = 2;
}

message UpdateAttachmentStatusReq {
    string post_id = 1;
    string comment_id = 2;
    string attachment_key = 3; // 完整的附件key
    ContentStatus status = 4;
}

message UpdateAttachmentStatusResp {

}

// 更新视频的url地址
message UpdateVideoUrlReq {
    string post_id = 1;
    string comment_id = 2;
    string attachment_key = 3;
    string new_url = 4;
    string vm_url = 5;
    string no_vm_url = 6;
    bool is_obs = 7;
}


message UpdateVideoUrlResp {}

message UpdatePostSpecialLabelReq {
    string post_id = 1;
    string label = 2; // 为空则表示删除
}

message UpdatePostSpecialLabelResp {}

message ReportPostViewReq {
    uint32 user_id = 1;
    enum ViewType {
        NONE = 0;
        NEW = 1;
    }
    map<string, ViewType> post_ids = 2;
}

message ReportPostViewResp {}

message ReportPostViewV2Req {
    uint32 user_id = 1;
    repeated string post_ids = 2;
    // 过滤器, 在resp返回的帖子阅读数为"第一次"大于这个数量的已读帖子的子集, 传0则不返回
    uint32 filter_min_count = 3;
}

message ReportPostViewV2Resp {
    repeated string significant_post_ids = 1; // 阅读数满一定数量的帖子列表
}

message ReportTopicViewCountReq {
    repeated string topic_ids = 1;
}

message ReportTopicViewCountRsp {
}


message GetTopicViewCountReq {
    string topic_id = 1;
}

message GetTopicViewCountRsp {
    uint32 count = 1;
}

message ReportPostShareReq {
    uint32 user_id = 1;
    string post_id = 2;
}

message ReportPostShareResp {}

// -------- 评论 --------
message AddCommentReq {
    uint32 user_id = 1;
    string post_id = 2;
    string reply_to_comment_id = 3;
    string conversation_id = 4;
    string content = 5;
    ContentStatus status = 6;
    repeated AttachmentInfo attachments = 7;    // deprecated, use attachment_image_count to get image keys and token
    uint32 attachment_image_count = 8;
    int32 content_type = 9;
    uint32 source = 10; // for 推荐, 点赞/评论 一级页面来源 见rcmd_.proto RecommendSourceType, 仅用于发事件
    bool is_obs = 11; //发评论走obs

    // 评论人的IP归属地
    Location ip_loc = 12;
    uint32 client_ip = 13;
    bool only_show_self = 14;
}

message AddCommentResp {
    string comment_id = 1;
    CommentInfo comment = 2;    //新加的评论

    repeated string attachment_image_keys = 10;
    string image_upload_token = 11;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetCommentListReq {
    string post_id = 1;
    string conversation_id = 2;
    string loadMore = 3;
    uint32 count = 4;
    uint32 user_id = 5;                 // 请求者uid
    bool   ascending = 6;               // default is false(a.k.a descending)
    bool   sub_comments_ascending = 7;  // default is false(a.k.a descending)
    ContentType content_type = 8;
    uint32 market_id = 9;
    uint32 client_type = 10;
}

message GetCommentListResp {
    repeated CommentInfo comment_list = 1;
    string load_more = 2; // 用于下次查询
}

message DelCommentReq {
    string post_id = 1;
    string comment_id =2;
    string conversation_id = 3;
}

message DelCommentResp {}

message GetCommentByIdReq {
    string comment_id =1;
    ContentType content_type = 2;
    uint32 market_id = 3;
    uint32 client_type = 4;
    uint32 user_id = 5; // 请求者uid,设置了，会过滤不能显示的评论
}

message GetCommentByIdResp {
    CommentInfo comment = 1;
}

message BatchGetCommentByIdsReq {
    repeated string comment_id_list = 1;
    ContentType content_type = 2;
    uint32 market_id = 3;
    uint32 client_type = 4;
    uint32 user_id = 5; // 请求者uid,设置了，会过滤不能显示的评论
}

message BatchGetCommentByIdsResp {
    map<string, CommentInfo> comment_infos = 1;
}

message BanCommentByIdReq {
    string comment_id = 1;
    bool is_ban = 2; // true表示封禁,false表示解除, 注意: 如果评论内容本身审核不通过, 调用解封的话会直接讲评论变成已审核状态
    bool is_delete = 3; //封评论的同时, 是否要删除评论(客户端不显示),
}

message BanCommentByIdResp {
}

// -------- 点赞 --------
message AddAttitudeReq {
    string post_id = 1;
    string comment_id = 2;
    uint32 user_id = 3;
    uint32 attitude_type = 4;
    bool is_first_time = 5;     // 是否第一次点赞
    uint32 target_user_id = 6;  // 帖子、评论的创建者
    uint32 source = 7; // for 推荐, 点赞/评论 一级页面来源 rcmd_.proto RecommendSourceType, 仅用于发事件
    uint32 step_type = 8;
}

message AddAttitudeResp {

}

// 取消点赞
message DelAttitudeReq {
    string post_id = 1;
    string comment_id = 2;
    uint32 user_id = 3;
    uint32 target_user_id = 4;  // 帖子、评论的创建者
    uint32 source = 5; // // for 推荐, 点赞/评论 一级页面来源 rcmd_.proto RecommendSourceType, 仅用于发事件
    uint32 attitude_type = 6;
    uint32 step_type = 7;
}

message DelAttitudeResp {}

// 点赞的用户列表

message AttitudeUserInfo {
    uint32 user_id = 1;
    uint32 attitude_type = 2; // 类型去attitude.proto查
    uint64 time = 3;
}

message GetAttitudeUserListReq {
    string post_id = 1;
    string comment_id = 2; // 传了就查评论的点赞列表, 不传就查帖子的
}

message GetAttitudeUserListResp {
    repeated AttitudeUserInfo attitude_user_list = 1;
}

message GetUserWonAttitudeCountReq {
    uint32 uid = 1;
}

message GetUserWonAttitudeCountResp {
    uint32 total = 1;
}

/*查看用户是否可以点赞*/
message CheckUserAttitudeAvailableReq{
    uint32 uid = 1;
}
message CheckUserAttitudeAvailableResp{
}

// 举报
message AppReportReq {
    string post_id = 1;
    string comment_id = 2;
    uint32 from_user_id = 3;// 举报人
    uint32 report_type = 4; // 举报类型
    string content = 5; // 用户输入的举报内容
}

message AppReportResp {}

// 可能是给推荐系统打标签
message UpdatePostTagsReq {
    string post_id = 1;
    repeated uint32 tags = 2;
    uint32 user_id = 3; // 操作者uid
}

message UpdatePostTagsResp {}

// 根据条件查帖子列表
message GetPostsByFilterReq {
    uint32 limit = 1; // 查询数量
    string load_more = 2; // 分页查询, 上次resp返回的loadMore

    repeated PostInfo.PostType post_types = 7;
    ContentType content_type = 8;

    // 下面开始是过滤条件
    uint32 user_id = 11;

    /**
     * 按圈子过滤；
     * Magic strings:
     * "all": 所有圈子（等同于空值）
     * "exsits": 包含圈子
     * "non-exists": 不包含圈子
     */
    string topic_id = 12;
    int64 create_begin = 13;    // 帖子发布时间起始区间, 大于等于 (左开右闭)
    int64 create_end = 14;      // 帖子发布时间结束区间, 小于
    bool use_tags_filter = 15;  // 是否根据标签过滤
    repeated uint32 tags = 16;  // 根据标签

    /**
     * 按话题过滤；
     * Magic strings:
     * "all": 所有话题（等同于空值）
     * "exsits": 包含话题
     * "non-exists": 不包含话题
     */
    string sub_topic_id = 17;

    /**
     * 按特殊标识过滤
     * Magic strings:
     * "all": 所有特殊标识（等同于空值）
     * "exists": 已配置特殊标识
     * "non-exists": 未配置特殊标识
     */
    string label = 18;

    /**
     * 按发帖人过滤
     */
    repeated uint32 owner_uid_list = 19; // 发帖者uid列表

    //帖子状态
    string post_status = 20;

}

message GetPostsByFilterResp {
    string load_more = 1; // 查询下一页的时候用,loadMore为空表示没有下一页了
    repeated PostInfo post_list = 2;
}

// 获取帖子标签
message GetPostTagsReq {
    string post_id = 1;
}

message GetPostTagsResp {
    repeated uint32 tags = 1;
}

message PostTag {
    repeated uint32 tags = 1;
}

message BatchGetPostTagsReq {
    repeated string post_id_list = 1;
}

message BatchGetPostTagsResp {
    map<string, PostTag> tags = 1;
}

message MarkFirstPostReq {
    uint32 user_id = 1;
    string post_id = 2;
}

message MarkFirstPostResp {
    string first_post_id = 1;
}

// 更新帖子隐私策略
message UpdatePostPrivacyPolicyReq {
    uint32 user_id = 1;
    string post_id = 2;
    PostPrivacyPolicy policy = 3;
}

message UpdatePostPrivacyPolicyResp{}

// 获取精确帖子数量
message GetStrictUserPostedCountReq{
    uint32  user_id = 1;
}

message GetStrictUserPostedCountResp{
    uint32 count = 1;
    uint32 private_count = 2;
    uint32 normal_count = 3;
}

// 缓存用户最新帖子
message SetUserNewestPostReq{
    uint32 uid = 1;
    string post_id = 2;
    int64 expiration = 3; // 单位：s
}

message SetUserNewestPostResp{}

// 批量获取用户最新帖子
message BatchGetUserNewestPostReq{
    repeated uint32 uids = 1;
}

message BatchGetUserNewestPostResp{
    map<uint32,string> post_map = 1;
}

enum StickyStatus {
    STICKY_NONE = 0;
    STICKY = 1;
}

message GetCommentsByFilterReq {
    uint32 limit = 1;           // 查询数量
    string load_more = 2;       // 分页查询, 上次resp返回的loadMore

    ContentType content_type = 8;

    // 下面开始是过滤条件
    uint32 user_id = 11;        // 根据uid过滤
    string post_id = 12;        // 根据帖子id过滤
    int64 create_begin = 13;    // 评论发布时间起始区间, 大于等于 (左开右闭)
    int64 create_end = 14;      // 评论发布时间结束区间, 小于

}

message GetCommentsByFilterResp {
    string load_more = 1;       // 查询下一页的时候用,loadMore为空表示没有下一页了
    repeated CommentInfo comment_list = 2;
}

message UpdateContentStickyStatusReq {
    uint32 user_id = 1;
    string post_id = 2;
    string comment_id = 3;
    StickyStatus status = 4;
}

message UpdateContentStickyStatusResp {}

message UpdateAttachmentPrivacyReq {
    uint32 user_id = 1;
    string post_id = 2;
    string comment_id = 3;
    AttachmentDownloadPrivacy privacy = 4; // 附件可见度隐私设置
}

message UpdateAttachmentPrivacyResp {

}

message AddStickyContentReq {
    uint32 user_id = 1;
    string parent_id = 2; // 如果置顶帖子则传"", 置顶评论则传帖子id
    string target_id = 3; // 帖子id / 评论id
}

message AddStickyContentResp {}

message RemoveStickyContentReq {
    uint32 user_id = 1;
    string parent_id = 2; // 如果置顶帖子则传"", 置顶评论则传帖子id
    string target_id = 3; // 帖子id / 评论id
}

message RemoveStickyContentResp {}

message GetStickyContentReq {
    uint32 user_id = 1;
    string parent_id = 2; // 如果置顶帖子则传"", 置顶评论则传帖子id
}

message GetStickyContentResp {
    string parent_id = 1; // 就是传进来的parent_id
    repeated string target_ids = 2;
}

message StickyContent {
    uint32 user_id = 1;
    string parent_id = 2; // 查的是帖子, 则为空; 查的是评论, 则为帖子id
    repeated string target_ids = 3; // 查的是帖子, 则为帖子id; 查的是评论, 则为评论id
}

message BatchGetStickyPostReq { // 时间紧迫, 只能批量查置顶的帖子
    repeated uint32 user_id_list = 1;
}

message BatchGetStickyPostResp {
    map<uint32, StickyContent> sticky_map = 1;
}

// 根据浏览数来查
message GetPostsByTimeSortViewCntReq {
    uint32 offset = 1; // 偏移
    uint32 limit = 2; // 查询数量
    int64 query_day = 3; //某一天
}


message GetPostsByTimeSortViewCntResp {
    uint32 total_cnt = 1;
    repeated PostWeightAllInfo post_list = 2;
}

enum WeightStatus{
    WeiStDefault = 0; /*默认*/
    WeiStValid = 1; /*配置中*/
    WeiStExpired = 2; /*过期*/
    WeiStFuture = 3; /*未来的*/
}

message PostWeightInfo{
    bool has_weight = 1; /*是否提权*/
    string weight = 2;  /*提权指数*/
    uint32 weight_start_ts = 3;  /*提权开始时间*/
    uint32 weight_end_ts = 4;  /*提权结束时间*/
    WeightStatus weight_status = 5; /*提权状态 WeightStatus*/
    string post_id = 6; /*帖子ID*/
    int64 create_time = 7; //权重创建时间
}

message UpdatePostWeightInfo{
    bool has_weight = 1; /*是否提权*/
    string weight = 2;  /*提权指数*/
    uint32 weight_start_ts = 3;  /*提权开始时间*/
    uint32 weight_end_ts = 4;  /*提权结束时间*/
}

message StatusResult {
    string post_id = 1;
    WeightStatus weight_status = 2;
}
message BatUpdateWeightReq{
    repeated string id_list = 1;
    UpdatePostWeightInfo weight_info = 2; /*提权*/
}

message BatUpdateWeightResp{
    uint32 update_cnt = 1;
    repeated StatusResult results = 2;
}

/*批量获取所有提权帖子*/
message BatGetPostWeightReq{
    uint32 offset = 1;
    uint32 limit = 2;
}

message PostWeightAllInfo {
    PostInfo post_info = 1;
    PostWeightInfo weight_info = 2;
}

message BatGetPostWeightResp{
    repeated PostWeightInfo weight_info = 1;
    uint32 total_cnt = 2;
}

message BatGetPostAndWeightReq{
    uint32 offset = 1;
    uint32 limit = 2;
    WeightStatus filter_status = 3;
    string weight_filter = 4;
    uint32 begin_time = 5;
    uint32 end_time = 6;
    repeated string post_id_list = 7;
}


message BatGetPostAndWeightResp{
    repeated PostWeightAllInfo post_list = 1;
    uint32 total_cnt = 2;
}

enum PostMachineStatus{
    MachineDefault = 0; /*默认*/
    MachineSuspicious = 1; /*疑似*/
    MachineNormal = 2; /*同意*/
    MachineReject = 3; /*拒绝*/
}

message UpdatePostMachineAuditByIdReq {
    string post_id = 1;
    PostMachineStatus status = 2;
    string reason = 3;
}

message UpdatePostMachineAuditByIdRsp {
}

//App Tab筛选器
message AppTabFilter{
    uint32 market_id = 1;
    bool is_show_in_main_tab = 2; //是否在主tab显示
    repeated string show_sub_tab_names = 3; //在哪些子tab显示
}

/*推荐流强插帖子*/
message ForcePostInUserRcmdFeedBaseInfo{
    string config_id = 1;
    string post_id = 2;
    uint32 index = 3;
    uint32 begin_ts = 4;
    uint32 end_ts = 5;
    string operator_name = 6; // 操作人名称
    repeated AppTabFilter app_tab_filters = 7;
}
message ForcePostInUserRcmdFeedTotalInfo{
    ForcePostInUserRcmdFeedBaseInfo base_info = 1;
    uint32 uid = 2;
    string content = 3;
    repeated AttachmentInfo attachments = 4;
    repeated string topicids = 5;
    uint32 create_ts = 6;
    uint32 view_cnt = 7;
    uint32 attitude_cnt = 8;
    uint32 comment_cnt = 9;
    uint32 share_cnt = 10;
    uint32 update_ts = 11;
}
message SetForcePostInUserRcmdFeedReq{
    ForcePostInUserRcmdFeedBaseInfo info = 1;
}
message SetForcePostInUserRcmdFeedResp{
    string post_id=1;
}
enum ForcePostInUserRcmdFeedStatus{
    Total = 0;
    NotBegin = 1;
    InConfig = 2;
    Expired = 3;
}
message GetForcePostInUserRcmdFeedReq{
    string config_id = 1;
    string post_id = 2;
    uint32 index = 3;
    uint32 status = 4; /* ForcePostInUserRcmdFeedStatus */
    uint32 offset = 5;
    uint32 limit = 6;
    repeated uint32 market_ids = 7; //新增App筛选，不传则全选
}
message GetForcePostInUserRcmdFeedResp{
    repeated ForcePostInUserRcmdFeedTotalInfo infos = 1;
    uint32 total_cnt = 2;
}
/*app*/
message GetForcePostsInUserRcmdFeedReq{
    uint32 uid = 1; /* 客户端的请求 */
    uint32 offset = 2;
    uint32 limit = 3;
    string tab_name = 4; //当前tab名称，空代表“全部”
}
message GetForcePostsInUserRcmdFeedResp{
    repeated ForcePostInUserRcmdFeedTotalInfo infos = 1;
}

message DelForcePostInUserRcmdFeedReq{
    repeated string config_ids = 1;
}
message DelForcePostInUserRcmdFeedResp{
}

message SearchPostByTopicReq {
    uint32 limit = 1;
    bool desc = 2; //按发帖时间排序，true则最新在前
    string load_more = 3; //分页查询, 上次resp返回的load_more
    //以下是筛选条件
    string topic_id = 4;
    int64 create_begin = 5; //时间戳，单位秒
    int64 create_end = 6;
    uint32 uid = 7;
}

message SearchPostByTopicResp {
    string load_more = 1; //查询下一页的时候用,load_more为空表示没有下一页了
    repeated string post_list = 2;
}

message AssociatePostWithTopicReq {
    string topic_id = 1;
    repeated string post_list = 2;
}

message AssociatePostWithTopicResp {
}

message BatGetLocationByGeoTopicIdReq {
    repeated string geo_topic_id_list = 1;
}
message BatGetLocationByGeoTopicIdResp {
    map<string, string> city_map = 1;
    map<string, string> province_map = 2;
}


message TransPostToSquareReq {
    string non_public_post_id = 1;
    PostInfo post_info = 2;
}
message NonPublicPostConversionSquarePostRequest{
    repeated TransPostToSquareReq post_list = 1;
}

message NonPublicPostConversionSquarePostResponse{
    map<string, string> post_id_map = 1;

}

message SearchConversionNonPublicPostRequest{
    string category_id=1;
    string post_id=2;
    string social_community_id=3;
    string non_public_post_id=4;
    uint32 uid=5;
    uint32 offset=6;
    uint32 limit=7;
}

message ConversionNonPublicPostInfo{
    string post_id=1;
    string non_public_post_id=2;
    uint32 uid=3;
    string ttid=4;
    string nick_name=5;
    string category_id=6;
    string category_name=7;
    string social_community_id=8;
    string social_community_name=9;
    uint32  create_time=10;
    uint32 origin_uid=11;
    string operator=12;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SearchConversionNonPublicPostResponse{
    repeated ConversionNonPublicPostInfo ConversionNonPublicPost=1;
    int32 count=2;

}

message BatchConversionNonPublicPostRequest{
    repeated ConversionNonPublicPostInfo info=1;
}

message BatchConversionNonPublicPostResponse{

}

message BatchConversionNonpublicPostRecordsByPostIdsRequest{
    repeated string post_ids=1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchConversionNonpublicPostRecordsByPostIdsResponse{
    map<string,ConversionNonPublicPostInfo>ConversionNonPublicPostMap=1;
}

/*更新专区帖子的话题*/
message UpdateZonePostTopicIdListRequest{
  repeated ZonePostTopicIdList zone_post_topic_id_list=1;
  uint32 action_type=2;
}

enum UpdateZonePostTopicIdListActionType{
  UpdateZonePostTopicIdListActionTypeUnspecified = 0;
  //追加
  UpdateZonePostTopicIdListActionTypeAppend = 1;
  //覆盖更新
  UpdateZonePostTopicIdListActionTypeUpdate = 2;
}

message ZonePostTopicIdList{
  string post_id=1;
  repeated CommonTopicInfo common_topic_infos = 2; //通用话题信息
}



message UpdateZonePostTopicIdListResponse{

}

/*更新专区帖子的话题*/

message GetAttachmentUploadTokenReq {
  uint32 uid = 1;
  uint32 attachment_count = 2;
  PostInfo.PostType post_type = 3;
  string ttid = 4; // 如果是视频，需要ttid做水印
  Extra extra = 5; // 附件的额外信息, 比如宽高等
}

message GetAttachmentUploadTokenResp {
  string post_id = 1;
  string token = 2;
  repeated string attachment_key = 3;
}

message SetPostRiskCheckInfoReq {
    string post_id = 1; // 帖子id
    RiskCheckInfo risk_check_info = 2; // 风控检查信息
}

message SetPostRiskCheckInfoResp {
}