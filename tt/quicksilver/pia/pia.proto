syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/pia";

package pia;


service Pia {
    // 获取pia戏房间的剧本信息
    rpc GetChannelDramaInfos (GetChannelDramaInfosReq) returns (GetChannelDramaInfosResp) {}
    // 获取房间配置的置顶信息
    rpc GetChannelTopConfigInfos (GetChannelTopConfigInfosReq) returns (GetChannelTopConfigInfosResp) {}
    // 聚合处理房间排序和房间pia戏剧本信息，根据置顶信息进行排序，根据房间的权限配置信息过滤出没有权限的房间
    rpc AggChannelOrderAndDramaInfos (AggChannelOrderAndDramaInfosReq) returns (AggChannelOrderAndDramaInfosResp) {}
    // 获取ugc的pia戏房间
    rpc GetUgcChannels (GetUgcChannelsReq) returns (GetUgcChannelsResp) {}

    // ************** 主玩法 *************
    // 选本
    rpc SelectDrama (SelectDramaReq) returns (SelectDramaResp) {}
    // 获取房间Pix戏开启状态
    rpc GetChannelPiaStatus (GetChannelPiaStatusReq) returns (GetChannelPiaStatusResp) {}
    // 设置Pia戏玩法模式
    rpc SetPiaSwitch (SetPiaSwitchReq) returns (SetPiaSwitchResp) {}
    // 获取剧本
    rpc GetDrama (GetDramaReq) returns (GetDramaResp) {}
    // 获取筛选标签组
    rpc GetSearchOptionGroup (GetSearchOptionGroupReq) returns (GetSearchOptionGroupResp) {}
    // 获取当前房间Pia戏信息
    rpc GetCurrentPiaInfo (GetCurrentPiaInfoReq) returns (GetCurrentPiaInfoResp) {}
    // 设置Pia戏阶段
    rpc SetPiaPhase (SetPiaPhaseReq) returns (SetPiaPhaseResp) {}
    // 设置Pia戏进度
    rpc SetPiaProgress (SetPiaProgressReq) returns (SetPiaProgressResp) {}
    // 设置Pia戏bgm
    rpc SetBgmInfo (SetBgmInfoReq) returns (SetBgmInfoResp) {}
    // 获取Pia戏bgm
    rpc GetBgmInfo (GetBgmInfoReq) returns (GetBgmInfoResp) {}
    // 获取正在玩的房间
    rpc GetPlayingChannel (GetPlayingChannelReq) returns (GetPlayingChannelResp) {}
    // 设置管理麦位
    rpc SetCompereMic (SetCompereMicReq) returns (SetCompereMicResp) {}

    // 获取剧本详情
    rpc GetDramaDetailByID (GetDramaDetailByIDReq) returns (GetDramaDetailByIDResp) {}

    // 置顶pia戏房间
    rpc AddStickPiaRoom (AddStickPiaRoomReq) returns (EmptyMsg) {}
    rpc GetStickPiaRoom (GetStickPiaRoomReq) returns (GetStickPiaRoomResp) {}
    rpc UpdateStickPiaRoom (UpdateStickPiaRoomReq) returns (EmptyMsg) {}

    // 删除记录，共用
    rpc DelPiaRoomRecord (DelPiaRoomRecordReq) returns (EmptyMsg) {}

    // 房间pia戏权限管理
    rpc GrantPiaRoomPermission (GrantPiaRoomPermissionReq) returns (EmptyMsg) {}
    rpc GetPiaRoomPermission (GetPiaRoomPermissionReq) returns (GetPiaRoomPermissionResp) {}
    rpc CancelPiaRoomPermission (CancelPiaRoomPermissionReq) returns (EmptyMsg) {}
    rpc BatchGrantPiaRoomPermission (BatchGrantPiaRoomPermissionReq) returns (EmptyMsg) {}
    rpc BatchCancelPiaRoomPermission (BatchCancelPiaRoomPermissionReq) returns (EmptyMsg) {}
    rpc BatchGetPiaRoomPermission (BatchGetPiaRoomPermissionReq) returns (BatchGetPiaRoomPermissionResp) {}
    rpc GetTimeOverlapped (GetTimeOverlappedReq) returns (EmptyMsg) {}

    // pia戏剧本置顶管理

    //===============================全新pia戏====================================================

    //===============================走本主玩法相关的接口start====================================================

    // 点本
    rpc OrderDrama (OrderDramaReq) returns (OrderDramaResp) {}
    // 获取已点列表
    rpc GetOrderDramaList (GetOrderDramaListReq) returns (GetOrderDramaListResp) {}
    // 删除已点记录
    rpc DeleteOrderDrama (DeleteOrderDramaReq) returns (DeleteOrderDramaResp) {}
    // 全局删除已点记录，扫描全部的在玩房间，然后移除指定的剧本记录
    rpc DeleteOrderDramaForAllChannel (DeleteOrderDramaForAllChannelRequest) returns (DeleteOrderDramaForAllChannelResponse) {}
    // 选择角色
    rpc PiaSelectRole (PiaSelectRoleReq) returns (PiaSelectRoleResp) {}
    // 角色取消选择
    rpc PiaCancelSelectRole (PiaCancelSelectRoleReq) returns (PiaCancelSelectRoleResp) {}
    // 选本
    rpc SelectDramaV2 (SelectDramaV2Req) returns (SelectDramaV2Resp) {}
    // 走本操作
    rpc PiaOperateDrama (PiaOperateDramaReq) returns (PiaOperateDramaResp) {}
    // 获取当前房间走本详情
    rpc PiaGetDramaStatus (PiaGetDramaStatusReq) returns (PiaGetDramaStatusResp) {}
    // BGM操作
    rpc PiaOperateBgm (PiaOperateBgmReq) returns (PiaOperateBgmResp) {}
    // BGM音量操作
    rpc PiaOperateBgmVol (PiaOperateBgmVolReq) returns (PiaOperateBgmVolResp) {};
    // 切换走本方式
    rpc PiaChangePlayType (PiaChangePlayTypeReq) returns (PiaChangePlayTypeResp) {}
    // 获取正在玩的房间
    rpc GetPlayingChannelV2 (GetPlayingChannelV2Req) returns (GetPlayingChannelV2Resp) {}
    // 获取ugc的pia戏房间
    rpc GetUgcChannelsV2 (GetUgcChannelsReqV2) returns (GetUgcChannelsRespV2) {}

    //===============================走本主玩法相关的接口end====================================================

    //===============================走本主玩法相关的接口（V2）start====================================================

    // 选择角色
    rpc PiaSelectRoleV2 (PiaSelectRoleReq) returns (PiaSelectRoleResp) {}
    // 角色取消选择
    rpc PiaCancelSelectRoleV2 (PiaCancelSelectRoleReq) returns (PiaCancelSelectRoleResp) {}
    // 获取当前房间走本详情
    rpc PiaGetDramaStatusV2 (PiaGetDramaStatusReq) returns (PiaGetDramaStatusResp) {}
    // 走本操作-播放剧本
    rpc PlayDrama (PiaOperateDramaReq) returns (PiaOperateDramaResp) {}
    // 走本操作-暂停剧本
    rpc PauseDrama (PiaOperateDramaReq) returns (PiaOperateDramaResp) {}
    // 走本操作-停止剧本
    rpc StopDrama (PiaOperateDramaReq) returns (PiaOperateDramaResp) {}
    // 演绎剧本
    rpc PreformDrama (PerformDramaRequest) returns (PerformDramaResponse) {}
    // bgm操作-播放bgm
    rpc PlayBgm (PiaOperateBgmReq) returns (PiaOperateBgmResp) {}
    // bgm操作-暂停bgm
    rpc PauseBgm (PiaOperateBgmReq) returns (PiaOperateBgmResp) {}
    // bgm操作-设置音量
    rpc SetBgmVol (PiaOperateBgmVolReq) returns (PiaOperateBgmVolResp) {};
    // PiaSendDialogueIndex 发送台词定位
    rpc PiaSendDialogueIndex (PiaSendDialogueIndexRequest) returns (PiaSendDialogueIndexResponse) {}
    // PiaFollowMic 跟随麦位
    rpc PiaFollowMic (PiaFollowMicRequest) returns (PiaFollowMicResponse) {}
    // PiaUnFollowMic 取消跟随麦位
    rpc PiaUnFollowMic (PiaUnFollowMicRequest) returns (PiaUnFollowMicResponse) {}
    // PiaReportDialogueIndex 麦上用户上报段落
    rpc PiaReportDialogueIndex (PiaReportDialogueIndexRequest) returns (PiaReportDialogueIndexResponse) {}
    // PiaGetPreviousDialogueIndex 获取上个用户的段落记录
    rpc PiaGetPreviousDialogueIndex (PiaGetPreviousDialogueIndexRequest) returns (PiaGetPreviousDialogueIndexResponse) {}
    // 获取我的跟随状态
    rpc PiaGetMyFollowInfo (PiaGetMyFollowInfoRequest) returns (PiaGetMyFollowInfoResponse) {}
    // 获取当前房间各个麦位的跟随状态
    rpc PiaGetFollowedStatusOfMicList (PiaGetFollowedStatusOfMicListRequest) returns (PiaGetFollowedStatusOfMicListResponse) {}
    // 切换走本方式
    rpc PiaChangePlayTypeV2 (PiaChangePlayTypeV2Request) returns (PiaChangePlayTypeV2Response) {}
    // 生成时间轴，如果有暂停操作，则不能生成时间轴
//    rpc CreateTimeLine (PiaCreateTimeLineRequest) returns (PiaCreateTimeLineResponse) {}


    //===============================走本主玩法相关的接口（V2）end====================================================

    //===============================剧本库模块相关接口start====================================================

    // 获取筛选标签组
    rpc GetSearchOptionGroupV2 (GetSearchOptionGroupV2Req) returns (GetSearchOptionGroupV2Resp) {}
    // 分页获取剧本库列表
    rpc GetDramaList (GetDramaListReq) returns (GetDramaListResp) {}
    // 批量获取剧本基本信息
    rpc BatchGetDramaSubInfoByIds (BatchGetDramaSubInfoByIdsReq) returns (BatchGetDramaSubInfoByIdsResp) {}
    // 获取剧本详情
    rpc GetDramaDetailByIdV2 (GetDramaDetailByIdV2Req) returns (GetDramaDetailByIdV2Resp) {}
    // 切换pia戏 v2
    rpc SetPiaSwitchV2 (SetPiaSwitchV2Req) returns (SetPiaSwitchV2Resp) {}
    // 测试剧本删除接口
    rpc TestDeleteDrama (TestDeleteDramaReq) returns (TestDeleteDramaResp) {}
    // 获取剧本副本id
    rpc PiaGetDramaCopyId (PiaGetDramaCopyIdReq) returns (PiaGetDramaCopyIdResp) {}
    // 创建剧本副本
    rpc CreateDramaCopy (PiaCreateDramaCopyReq) returns (PiaCreateDramaCopyResp) {};
    //(取消)收藏剧本
    rpc DoUserDramaCollect (DoUserDramaCollectReq) returns (DoUserDramaCollectResp) {}
    //获取剧本收藏状态、人数
    rpc GetUserDramaStatus (GetUserDramaStatusReq) returns (GetUserDramaStatusResp) {}
    //获取剧本收藏列表
    rpc GetUserDramaCollList (GetUserDramaCollListReq) returns (GetUserDramaCollListResp) {}
    //通过剧本ids批量获取剧本信息
    rpc GetDramaListByIds (GetDramaListByIdsReq) returns (GetDramaListByIdsResp) {}

    // pia戏剧本置顶
    rpc AddStickDramaV2 (AddStickDramaV2Req) returns (EmptyMsg) {}
    rpc UpdateStickDramaV2 (UpdateStickDramaV2Req) returns (EmptyMsg) {}
    rpc DeleteStickDrama (DeleteStickDramaReq) returns (EmptyMsg) {}
    rpc SearchStickDrama (SearchStickDramaReq) returns (SearchStickDramaResp) {}
    rpc GetDramaInfoByDramaId (GetDramaInfoByDramaIdReq) returns (GetDramaInfoByDramaIdResp) {}
    rpc GetStickDramaTags (GetStickDramaTagsReq) returns (GetStickDramaTagsResp) {}
    rpc CheckStickDramaTime (CheckStickDramaTimeReq) returns (EmptyMsg) {}
    rpc InternalStopChannel (InternalStopChannelReq) returns (InternalStopChannelResp) {}
    // 获取我的参演记录列表
    rpc GetMyDramaPlayingRecord (GetMyDramaPlayingRecordReq) returns (GetMyDramaPlayingRecordResp) {}
    // 批量删除我的参演记录
    rpc PiaBatchDeleteMyPlayingRecord (PiaBatchDeleteMyPlayingRecordReq) returns (PiaBatchDeleteMyPlayingRecordResp) {}
    // 重建剧本收藏缓存
    rpc PiaRebuildDramaCollectedCache (PiaRebuildDramaCollectedCacheReq) returns (PiaRebuildDramaCollectedCacheResp) {}
    // 获取剧本展开页
    rpc GetDramaExpansion (GetDramaExpansionReq) returns (GetDramaExpansionResp) {}
    // 获取排行榜剧本列表
    rpc PiaGetRankingList (PiaGetRankingListReq) returns (PiaGetRankingListResp) {}
    // 根据筛选条件获取所有相关参演记录id列表
    rpc PiaGetMyPlayingRecordIdList (PiaGetMyPlayingRecordIdListReq) returns (PiaGetMyPlayingRecordIdListResp) {}

    // 处理剧本举报结果
    rpc HandleDramaReportResult (PiaHandleDramaReportResultReq) returns (PiaHandleDramaReportResultResp) {}
    // 用户剧本反馈
    rpc PiaAddDramaFeedBack (PiaAddDramaFeedBackReq) returns (PiaAddDramaFeedBackResp) {}
    // 获取作者个人以及作品统计信息
    rpc GetAuthorGenInfo (PiaAuthorGenInfoReq) returns (PiaAuthorGenInfoResp) {}
    // 批量获取作者基础信息
    rpc PiaBatchGetAuthorBaseInfo (PiaBatchGetAuthorBaseInfoReq) returns (PiaBatchGetAuthorBaseInfoResp) {}
    // 获取作者作品列表
    rpc GetAuthorWorksList (PiaAuthorWorksListReq) returns (PiaAuthorWorksListResp) {}
    // 重建作者作品统计信息的缓存
    rpc RebuildAuthorWorksCache (PiaAuthorGenInfoRebuildReq) returns (PiaAuthorGenInfoRebuildResp) {}
    // 获取我创建的副本列表
    rpc GetMyDramaCopyList (GetMyDramaCopyListReq) returns (GetMyDramaCopyListResp) {}
    // 设置副本状态
    rpc SetDramaCopyStatus (SetDramaCopyStatusReq) returns (SetDramaCopyStatusResp) {}
    // 删除副本
    rpc DeleteDramaCopy (DeleteDramaCopyReq) returns (DeleteDramaCopyResp) {}
    // ConfirmCopiedDrama 确认临时副本生成副本
    rpc ConfirmCopiedDrama (PiaConfirmCopiedDramaRequest) returns (PiaConfirmCopiedDramaResponse) {}
    // GetCopiedDramaList 获取副本库列表
    rpc GetCopiedDramaList (PiaCopyDramaListRequest) returns (PiaCopyDramaListResponse);
    // CreateCopiedDrama 创建副本，但是不确认
    rpc CreateCopiedDrama (PiaCreateCopiedDramaRequest) returns (PiaCreateCopiedDramaResponse);
    // CreateTempDrama 创建临时剧本
    rpc CreateTempDrama (PiaCreateTempDramaRequest) returns (PiaCreateTempDramaResponse);
    // GetTempDrama 获取临时剧本
    rpc GetTempDrama (PiaGetTempDramaRequest) returns (PiaGetTempDramaResponse);
    //===============================全新pia戏====================================================
}

// ***************** 主玩法 *****************

// 选本
message SelectDramaReq {
    uint32 channel_id = 2;
    uint32 drama_id = 3;
    uint32 op_uid = 4;
}

// 选本
message SelectDramaResp {
}

// 获取房间Pia戏开启状态
message GetChannelPiaStatusReq {
    uint32 channel_id = 2;
}

message GetChannelPiaStatusResp {
    uint32 channel_id = 2;
    bool entry = 3;             // 是否有Pia入口
    bool is_open = 4;           // 房间当前是否开启Pia戏玩法
    bool is_switched_to_new = 5;  // 是否切换过新玩法
}

// 切换成Pia戏模式
message SetPiaSwitchReq {
    uint32 channel_id = 2;
    bool is_open = 3;           // 开启Pia戏玩法
}

message SetPiaSwitchResp {
}


// 获取剧本
message GetDramaReq {
    repeated SearchOption search_option = 2;
    uint32 page_token = 3; // 分页游标 上一页的最后一个id, 第一页传0
    uint32 page_size = 4; // 单页条目数
}

// 剧本信息(跟logic端协议有差异, 跟数据存储一致)
message Drama {
    uint32 id = 1;                 // ID
    string title = 2;              // 标题
    string cover_url = 3;          // 封面url
    string author = 4;             // 作者
    string desc = 5;               // 简介描述
    uint32 male_cnt = 6;           // 男性角色数量
    uint32 female_cnt = 7;         // 女性角色数量
    uint32 word_cnt = 8;           // 字数
    repeated string bgm_url = 9;   // 背景音乐
    string type = 10;              // 剧本类型
    repeated string tags = 11;     // 剧本标签
    string content = 12;           // 剧本全文
    bool has_playing = 13;         // 是否有正在玩该剧本的房间
}

// 获取剧本
message GetDramaResp {
    repeated Drama drama_list = 2;  //剧本信息列表
}

enum SearchType {
    FUZZY = 0;  // 模糊搜索
    DRAMA_TAG = 1; // 按剧本标签
    MALE_NUM = 2; // 男角色人数
    FEMALE_NUM = 3; // 女角色人数
    DRAMA_TYPE = 4; // 按剧本类型
}

// 获取筛选标签组
message GetSearchOptionGroupReq {
}

// 筛选标签组
message SearchOptionGroup {
    string group_name = 1; // 组名
    repeated SubSearchOptionGroup sub_search_option_group = 2;
}

// 筛选标签子分组名
message SubSearchOptionGroup {
    uint32 search_type = 1; // 搜索项类型
    string group_name = 2; // 子组名
    repeated SearchOption search_option = 3;
    string display_format = 4; // 选择后显示的文案, 格式:{value}固定文本
    string display_separator = 5; // 分割符号, 多选时候使用
}

// 获取筛选标签组
message GetSearchOptionGroupResp {
    repeated SearchOptionGroup search_option_group = 2;
}

// 筛选项
message SearchOption {
    uint32 search_type = 1; // 搜索类型
    string label = 2; // 标签(search_type为0时为搜索框关键字)
}

// 获取当前房间Pia戏信息
message GetCurrentPiaInfoReq {
    uint32 channel_id = 2;
}

// 阶段类型
enum PiaPhaseType {
    PIA_PHASE_CLOSE = 0;              // 未开始/结束
    PIA_PHASE_ON_PLAY = 1;            // 开始
}

// Pia戏信息, (广播类型见channel_.proto ChannelMsgType.PIA_INFO)
message PiaInfo {
    Drama drama = 1;           //剧本信息
    uint32 phase = 2;          //Pia戏阶段
    string progress = 3;       //剧本进度
    string bgm_info = 4;       //bgm信息
    uint32 compere_mic = 5;    //主持麦位
    repeated string all_mic_progress = 6; //所有麦位进度, 跟主持麦位按下标对应
}

// 获取当前房间Pia戏信息
message GetCurrentPiaInfoResp {
    PiaInfo pia_info = 2;
    string msg_for_screen = 3; // 公屏提示信息
}

// 设置Pia戏阶段
message SetPiaPhaseReq {
    uint32 channel_id = 2;
    uint32 drama_id = 3; // 剧本ID
    uint32 phase = 4; // pia戏阶段, 见PiaPhaseType
}

// 设置Pia戏阶段
message SetPiaPhaseResp {
}

// 设置Pia戏进度
message SetPiaProgressReq {
    uint32 channel_id = 2;
    uint32 drama_id = 3; // 剧本ID
    string progress = 4; // pia戏进度
    uint32 op_mic = 5; // 操作麦位
}

// 设置Pia戏进度
message SetPiaProgressResp {
}

// 获取正在玩的房间
message GetPlayingChannelReq {
    uint32 drama_id = 2; // 剧本ID
}

// 设置主持麦位
message SetCompereMicReq {
    uint32 channel_id = 2;
    uint32 mic = 3; // 麦位0-8
}

message SetCompereMicResp {
}

// 获取正在玩的房间
message PlayingChannelInfo {
    uint32 channel_id = 1;              // 频道id
    string channel_name = 2;            // 频道名字
    uint32 channel_member_count = 3;    // 频道人数
    uint32 channel_display_id = 4;      // 房间的显示ID
    uint32 channel_type = 5;  // 房间类型
    string channel_icon = 6; // 房间图标的MD5
    string channel_desc = 7; // 房间话题的描述(标题)
    string channel_creator_account = 8; // 频道创建者account
    bool is_playing = 9; // 是否读本中
}

// 获取正在玩的房间
message GetPlayingChannelResp {
    repeated PlayingChannelInfo playing_channels = 2;
}

// Pia戏模式切换广播, 见channel_.proto ChannelMsgType.PIA_SWITCH
message PiaSwitch {
    bool is_open = 1; // 是否开启
    PiaInfo pia_info = 2; // 上一次的剧本信息, 开启时会返回
    string desc = 3; // 切房描述
    string hint = 4; // 正在选本提示...
}

message GetDramaDetailByIDReq {
    uint32 drama_id = 1;
}

message GetDramaDetailByIDResp {
    Drama drama_detail = 1;
}

// 设置bgm进度
message SetBgmInfoReq {
    uint32 channel_id = 2;
    string bgm_info = 3;
}

// 设置bgm进度
message SetBgmInfoResp {
}

// 获取bgm进度
message GetBgmInfoReq {
    uint32 channel_id = 2;
}

// 获取bgm进度
message GetBgmInfoResp {
    string bgm_info = 2;
}

message DramaInfoForAggPage {
    uint32 id = 1;// 剧本id
    string name = 2;// 剧本名称
    string type = 3;// 剧本类型
    repeated string tags = 4;// 剧本标签
    string summary = 5;// 剧本简介
    DramaPhase drama_phase = 6;// 剧本状态
}
message ChannelOrderConfig {
    uint32 channel_id = 1;// 房间id
    string tag = 2;//房间标签
}
// 获取pia戏房间的剧本信息-请求
message GetChannelDramaInfosReq {
    repeated uint32 channel_ids = 1;
}
// 获取pia戏房间的剧本信息-响应
message GetChannelDramaInfosResp {
    repeated DramaInfoForAggPage drama_infos = 1; // 剧本信息。顺序与channel_ids一致，如果没有则留空
}
// 聚合处理房间排序和房间pia戏剧本信息-请求
message AggChannelOrderAndDramaInfosReq {
    ChannelTopConfigType config_type = 1;// 置顶类型
    repeated uint32 channel_ids = 2;// 房间id列表
}
// 聚合处理房间排序和房间pia戏剧本信息-响应
message AggChannelOrderAndDramaInfosResp {
    repeated ChannelOrderConfig channel_order_configs = 1; // 房间排序列表。顺序是保证请求时的顺序并且根据置顶配置
    repeated DramaInfoForAggPage drama_infos = 2; // 剧本信息。顺序与channel_ids一致，如果没有则留空
}
// 获取ugc的pia戏房间-请求
message GetUgcChannelsReq {
    int32 page_index = 1;// 页码
}
// 获取ugc的pia戏房间-响应
message GetUgcChannelsResp {
    repeated uint32 channel_ids = 1;
    bool has_more = 2;
}
enum ChannelTopConfigType {
    unknown = 0; // 不置顶
    CHANNEL_TOP_CONFIG_TYPE_PGC = 1; // PGC置顶
    CHANNEL_TOP_CONFIG_TYPE_UGC = 2; // UGC置顶
}
//获取房间配置的置顶信息-请求
message GetChannelTopConfigInfosReq {
    ChannelTopConfigType config_type = 1;// 置顶类型
    repeated uint32 channel_ids = 2;// 房间id列表
}
//获取房间配置的置顶信息-响应l
message GetChannelTopConfigInfosResp {
    repeated ChannelOrderConfig channel_order_configs = 1;// 房间排序列表。顺序是保证请求时的顺序并且根据置顶配置。如果参数的房间id为空，则直接使用配置的。
}


// ======================================运营后台相关======================================

enum RoomType {
    ROOM_TYPE_UNKNOWN = 0; // 未知房间
    ROOM_TYPE_PGC = 1; // PGC房间
    ROOM_TYPE_UGC = 2; // UGC房间
}

// 置顶类型
enum StickType {
    STICK_TYPE_UNKNOWN = 0; // 未知类型
    STICK_TYPE_FIRST = 1; // 第一置顶房间
    STICK_TYPE_OTHER = 2; // 其他置顶房间
}

// 生效状态
enum Status {
    STATUS_ALL = 0; // 全部
    STATUS_WAIT = 1; // 等待生效
    STATUS_STICKING = 2; // 生效中
    STATUS_EXPIRED = 3; // 已过期
}

enum AuditType {
    AUDIT_STICK = 0;    // 置顶房间
    AUDIT_BANNER = 1;   // banner位
    AUDIT_AD = 2;       // 预告位
}


message EmptyMsg {

}
// 置顶房间 公共字段
message StickPiaRoomBaseInfo {
    StickType stick_type = 1; // 置顶类型
    string tag = 2; // 房间标签, 每个不超过10个字
    int64 stick_start_time = 3; // 开始置顶的时间
    int64 stick_end_time = 4; // 置顶结束的时间
}

message StickDramaBaseInfo {
    uint32 drama_id = 1; // id
    string tag = 2; // 置顶剧本信息
    uint32 rank = 3; // 置顶权重
    int64 stick_start_time = 4; // 开始置顶的时间
    int64 stick_end_time = 5; // 置顶结束的时间
}

message AddStickDramaReq {
    repeated StickDramaBaseInfo stick_drama_list = 1;
}

// 增加置顶pia戏房间
message AddStickPiaRoomReq {
    message StickPiaRoomReq {
        StickPiaRoomBaseInfo base_info = 1;
        uint32 channel_id = 2;
        RoomType type = 3; // 房间类型
    }
    // 支持批量添加置顶房间
    repeated StickPiaRoomReq stick_pia_room_req_list = 1;
}

message CancelStickDramaReq {
    uint32 drama_id = 1;
}

message UpdateStickDramaReq {
    StickDramaBaseInfo stick_drama_info = 1;
}

// 筛选请求
message GetStickPiaRoomReq {
    uint32 channel_id = 1; // 房间channel_id
    int64 stick_start_time = 2; // 开始置顶的时间
    int64 stick_end_time = 3; // 置顶结束的时间
    RoomType type = 4; // 房间类型
    Status status = 5; // 置顶状态
    uint32 offset = 6; // 偏移
    uint32 limit = 7;  // 数量限制
}

// 筛选结果
message GetStickPiaRoomResp {
    // 完成审批的记录
    message StickPiaRoomInfo {
        StickPiaRoomBaseInfo base_info = 1;
        uint32 display_id = 2; // 返回时channel id转为display id
        Status status = 3; // 置顶状态
        RoomType room_type = 4; // 房间类型
        string id = 5; // 置顶id,用于编辑、删除
    }
    repeated StickPiaRoomInfo info_list = 1;
    uint32 count = 2;   // 结果数量
}

// 编辑置顶
message UpdateStickPiaRoomReq {
    StickPiaRoomBaseInfo base_info = 1;
    uint32 channel_id = 2; // 房间channel_id
    string id = 3; // 置顶id,用于编辑、删除
}

// 删除记录, 共用
message DelPiaRoomRecordReq {
    string id = 1;
    AuditType type = 2; // 置顶、banner、预告位
}


// 下发pia戏房间权限
message GrantPiaRoomPermissionReq {
    uint32 channel_id = 1;
}

// 获取房间的pia戏权限信息
message GetPiaRoomPermissionReq {
    uint32 channel_id = 1; // 通过channel_id 搜索
}

message GetPiaRoomPermissionResp {
    // 公会信息
    message GuildInfo {
        uint32 guild_id = 1;
        uint32 short_id = 2;
        string name = 3;
    }
    GuildInfo guild_info = 1;
    uint32 display_id = 2;
    bool has_permission = 3;  // true：有权限
}

message CancelPiaRoomPermissionReq {
    uint32 channel_id = 1;
}

message BatchGrantPiaRoomPermissionReq {
    repeated uint32 cid_list = 1;
}

message BatchCancelPiaRoomPermissionReq {
    repeated uint32 cid_list = 1;
}

message BatchGetPiaRoomPermissionReq {
    repeated uint32 cid_list = 1;
}

message BatchGetPiaRoomPermissionResp {
    message PermissionInfo {
        uint32 channel_id = 1;
        bool has_permission = 2;  // true：有权限
    }
    repeated PermissionInfo permission_info = 1;
}

// 判断时间是否重叠
message GetTimeOverlappedReq {
    message StickTimeInfo {
        uint32 channel_id = 1;
        StickType stick_type = 2;
        int64 stick_start_time = 3; // 开始置顶的时间
        int64 stick_end_time = 4; // 置顶结束的时间
    }
    repeated StickTimeInfo stick_time_info_list = 1;
    RoomType room_type = 2;
    string id = 3; // 更新时带上id
}

// ======================================运营后台相关 end==================================

//========================新pia戏玩法=================
//========================已点列表====================

// 已点列表
message DramaOrderList {
    message UserOrderInfo {
        DramaSubInfo drama_sub_info = 1; // 剧本信息
        uint32 user_id = 2;// 用户id
        string user_name = 3;// 用户昵称
        string user_avatar = 4;// 用户头像
        uint32 user_sex = 5; //性别，0-女，1-男
        int64 index_id = 6; // 已点记录的id
        uint32 fake_user_id = 7;// 用户神秘人id
    }
    repeated UserOrderInfo list = 1;
    uint32 channel_id = 2;// 房间id
    int64 version = 3; // 版本号
}

enum PiaDramaOrderUserAuthType {
    UNKNOWN = 0; // 未知
    NORMAL = 1; // 普通用户
    ADMIN = 2;// 管理员
}


// 点本请求
message OrderDramaReq {
    uint32 drama_id = 1; // 剧本id
    uint32 channel_id = 2; // 房间id
    uint32 user_id = 3; // 用户id
    uint32 fake_user_id = 4;// 用户神秘人id
}
// 点本响应
message OrderDramaResp {
    DramaOrderList order_list = 1; // 已点列表
}
// 获取已点列表请求
message GetOrderDramaListReq {
    uint32 channel_id = 1; // 房间id
}
// 获取已点列表响应
message GetOrderDramaListResp {
    DramaOrderList order_list = 1; // 已点列表
}
// 删除已点记录请求
message DeleteOrderDramaReq {
    repeated int64 index_id_list = 1; // 剧本id
    uint32 channel_id = 2; // 房间id
    uint32 user_id = 3;// 用户id
    PiaDramaOrderUserAuthType user_auth_type = 4; // 用户权限
}
// 删除已点记录响应
message DeleteOrderDramaResp {
    DramaOrderList order_list = 1; // 已点列表
}

// 全局删除已点记录，扫描全部的在玩房间，然后移除指定的剧本记录
message DeleteOrderDramaForAllChannelRequest {
    uint32 drama_id = 1; // 剧本id
}
message DeleteOrderDramaForAllChannelResponse {
    uint32 count = 1; // 受影响的房间数
}

//========================已点列表====================
//========================选角色======================

// 麦位绑定的角色列表
message MicRoleMap {
    message RoleInfoList {
        repeated string id = 1; // 角色id
        int64 join_time = 2; // 加入时间戳
    }
    map<uint32, RoleInfoList> map = 1; // key:麦位id，value:角色列表
    int64 version = 2; // 版本号
}
// 选择角色请求
message PiaSelectRoleReq {
    uint32 drama_id = 1; // 剧本id
    uint32 channel_id = 2; // 频道id
    string role_id = 3; // 角色id
    uint32 mic_number = 4; // 麦位号
    int64 round_id = 5;// 场次id
}
// 选择角色响应
message PiaSelectRoleResp {
    MicRoleMap mic_role_map = 1; // 麦位对应的角色列表
}
// 角色取消选择请求
message PiaCancelSelectRoleReq {
    uint32 drama_id = 1; // 剧本id
    uint32 channel_id = 2; // 频道id
    string role_id = 3; // 角色id
    uint32 mic_number = 4; // 麦位号
    int64 round_id = 5;// 场次id
}
// 角色取消选择响应
message PiaCancelSelectRoleResp {
    MicRoleMap mic_role_map = 1; // 麦位对应的角色列表
}
//========================选角色======================
//========================走本操作====================

// 走本阶段
enum DramaPhase {
    DRAMA_PHASE_UNSPECIFIED = 0; // 未指定状态
    DRAMA_PHASE_SELECT_ROLE = 1; // 选角阶段
    DRAMA_PHASE_PLAY = 2; // 播放阶段
    DRAMA_PHASE_END = 3; // 结束阶段
    DRAMA_PHASE_PAUSE = 4; // 暂停阶段
}
// 操作类型
enum DramaOperationType {
    DRAMA_OPERATION_TYPE_UNSPECIFIED = 0; // 未指定类型
    DRAMA_OPERATION_TYPE_PLAY = 1; // 播放操作
    DRAMA_OPERATION_TYPE_PAUSE = 2; // 暂停操作
    DRAMA_OPERATION_TYPE_END = 3; // 结束操作
}
message ChannelDramaProgress {
    uint32 cur_index = 1;// 当前进度的段落下标
    int64 time_offset = 2; // 当前段落的时间偏移，单位秒，不一定可靠
}
// 房间剧本状态
message ChannelDramaStatus {
    DramaPhase drama_phase = 1;// 剧本状态
    uint32 channel_id = 2; // 频道id
    DramaV2 drama_info = 3; // 剧本信息
    int64 version = 4; // 版本号
    ChannelDramaProgress progress = 5; // 剧本进度
    int64 start_time = 6; // 剧本开始时间戳
    int64 round_id = 7;// 场次id
    PiaChannelDramaPlayingType playing_type = 8; // 剧本的走本方式
    bool can_change_playing_type = 9; // 是否可以切换走本方式的开关
    string temp_drama_id = 10; // 剧本临时id
}
// 选本操作请求
message SelectDramaV2Req {
    uint32 drama_id = 1; // 剧本id
    uint32 channel_id = 2; // 房间id
    int64 index_id = 4;// 已点记录id。如果是从已点列表选本的，则必传。
}
// 选本操作响应
message SelectDramaV2Resp {
    ChannelDramaStatus drama_status = 1; // 房间剧本状态
}
// 走本操作请求
message PiaOperateDramaReq {
    uint32 channel_id = 1; // 房间id
    DramaOperationType operation_type = 2; // 操作类型
    uint32 drama_section_index = 3; // 剧本章节索引，播放操作必传
    uint32 operator_id = 4; // 操作者id（uid)
    uint32 delay_time = 5; // 延迟时间，告知服务端延后多少秒才开始走本（废弃）
    int64 round_id = 6;// 场次id
    bool is_ignore_push = 7; // 是否不推送通知给客户端
}
// 走本操作响应
message PiaOperateDramaResp {
    ChannelDramaStatus drama_status = 1; // 房间剧本状态
}
// 当前房间走本详细信息
message ChannelDramaInfoDetail {
    uint32 channel_id = 1; // 房间id
    ChannelDramaStatus drama_status = 2; // 房间剧本状态
    DramaBgmStatus drama_bgm_status = 3; // 房间剧本bgm状态
    MicRoleMap mic_role_map = 4; // 麦位对应的角色列表
    DramaBgmVolStatus drama_bgm_vol_status = 5; // 房间剧本bgm音量状态
    DramaV2 origin_drama_info = 6; // 原始的剧本信息
}
// 获取当前房间走本详情请求
message PiaGetDramaStatusReq {
    uint32 channel_id = 1; // 房间id
}
// 获取当前房间走本详情响应
message PiaGetDramaStatusResp {
    ChannelDramaInfoDetail drama_info_detail = 1; // 房间走本详情
}

// 获取剧本副本ID
message PiaGetDramaCopyIdReq {
    uint32 drama_origin_id = 1;  // 原本id
    uint32 uid = 2; // 用户uid
}

message PiaGetDramaCopyIdResp {
    uint32 drama_copy_id = 1;   // 副本id
}

// 生成剧本副本请求
message PiaCreateDramaCopyReq {
    uint32 channel_id = 1; // 房间id
    uint32 user_id = 2;// 用户id
    int64 round_id = 3;// 场次id
}
// 生成剧本副本响应
message PiaCreateDramaCopyResp {
    uint32 copy_drama_id = 1;   // 副本id
}

//========================走本操作====================
//========================BGM操作（没有时间轴的剧本）=====================

// BGM状态
enum DramaBGMPhase {
    DRAMA_BGM_PHASE_UNSPECIFIED = 0; // 未知状态
    DRAMA_BGM_PHASE_PLAY = 1; // 播放状态
    DRAMA_BGM_PHASE_PAUSE = 2; // 暂停状态
}
// BGM操作类型
enum DramaBGMOperationType {
    DRAMA_BGM_OPERATION_TYPE_UNSPECIFIED = 0; // 未知操作
    DRAMA_BGM_OPERATION_TYPE_PLAY = 1; // 播放操作
    DRAMA_BGM_OPERATION_TYPE_PAUSE = 2; // 暂停操作
}
// 剧本BGM状态
message DramaBgmStatus {
    string bgm_id = 1; // BGMid
    DramaBGMPhase bgm_phase = 2; // BGM状态
    int64 bgm_progress = 3;// BGM进度
    int64 version = 4; // 版本号
    int64 operation_time = 5;// 操作时间
}
// 剧本BGM音量状态
message DramaBgmVolStatus {
    int32 vol = 6; // 音量
}
// BGM操作请求
message PiaOperateBgmReq {
    uint32 channel_id = 1; // 频道id
    DramaBGMOperationType operation_type = 2; // 操作类型
    string bgm_id = 3; // BGMid
    int64 cur_progress = 4; // 当前进度
    int64 next_progress = 5; // 下一阶段进度，播放操作必传
    uint32 operator_id = 6; // 操作者id（uid)
    int64 round_id = 7;// 场次id
}
// BGM操作响应
message PiaOperateBgmResp {
    DramaBgmStatus bgm_status = 1; // 剧本BGM状态
}
// BGM音量操作请求
message PiaOperateBgmVolReq {
    uint32 channel_id = 1; // 房间id
    int32 vol = 2; // 音量
}
// BGM音量操作响应
message PiaOperateBgmVolResp {
    DramaBgmVolStatus bgm_vol_status = 1; // 剧本BGM音量状态
}
//========================BGM操作=====================

//======================新剧本========================

// pia戏角色
message PiaRole {
    string id = 1; // 唯一id
    string name = 2;
    uint32 sex = 3; // 性别：1--女 2--男
    string avatar = 4; // 链接
    string introduction = 5; // 简介
    string color = 6; // 角色颜色
    double dialogue_ratio = 7; // 对白占比
}

message PiaDuration {
    int64 begin_time = 1; // 开始时间（秒）
    int64 end_time = 2; // 结束时间（秒）
}

// 剧本内容（段落）
message PiaContent {
    string id = 1; // 唯一id
    string role_id = 2;
    string dialogue = 3; // 对白
    PiaDuration duration = 4;
    string role_name = 5; // 角色名称
    string color = 6; // 角色颜色
    string style = 7;
    string dialogue_v2 = 8; // 对白v2 带图片样式
}

message PiaPicture {
    string id = 1; // 唯一id
    string url = 2;  // 图片链接
    PiaDuration duration = 3;
}

message PiaBGM {
    string id = 1;
    string name = 2; // bgm名称
    string url = 3; // 音乐链接
    PiaDuration duration = 4;
    uint32 length = 5;// bgm长度
}


// 剧本子信息(概要信息)
message DramaSubInfo {
    uint32 id = 1;                 //ID
    string title = 2;              //标题
    string cover_url = 3;          //封面url
    string author = 4;             //作者
    string desc = 5;               //简介描述
    uint32 male_cnt = 6;           //男性角色数量
    uint32 female_cnt = 7;         //女性角色数量
    uint32 word_cnt = 8;           //字数
    repeated string tag_list = 9;     //剧本标签
    string type = 10;              // 剧本类型
    uint64 display_id = 11;         // 外显id
    uint32 author_uid = 12;         // 作者tt端内uid
    uint32 author_id = 13;         // 作者id
    uint32 duration = 14;          // 剧本时长
    int64 create_time = 15;         // 创建时间
    uint32 related_drama_id = 16;  // 关联的原剧本id
    uint32 creator_uid = 17; // 创建者uid
    bool is_private = 18; // 是否私密
    bool is_freeze = 19; // 是否冻结
    bool is_deleted = 20; // 是否删除
    PiaPlayType play_type = 21; // 播放类型
}

// 带剧本状态的信息
message DramaInfoWithStatus {
    DramaSubInfo drama_sub_info = 1;
    bool selected = 2;  // 用户是否已经选择过
    bool has_play = 3; // 是否有正在玩该剧本的房间
    string stick_tag = 4; // 剧本置顶标签
}

// 播放类型
enum PiaPlayType {
    PLAY_TYPE_UNKNOWN = 0;
    PLAY_TYPE_AUTO = 1; // 时间轴播放
    PLAY_TYPE_MANUAL = 2; // 手动切
}

// 新剧本信息
message DramaV2 {
    DramaSubInfo drama_sub_info = 1; // 剧本子信息
    repeated PiaRole role_list = 2; // 角色列表
    repeated PiaContent content_list = 3; // 段落列表
    repeated PiaBGM bgm_url = 4;   //背景音乐列表
    repeated PiaPicture picture_list = 5; // 背景图片列表 保留字段
    PiaPlayType play_type = 6; // 播放类型
}
//======================新剧本========================

//======================剧本库========================

// 获取筛选标签组 新版
message GetSearchOptionGroupV2Req {
    enum SearchOptionType {
        SEARCH_OPTION_TYPE_DRAMA = 0; // 剧本库
        SEARCH_OPTION_TYPE_COPY = 1; // 副本库
    }
    SearchOptionType search_option_type = 1;
}
// 获取筛选标签组 新版
message GetSearchOptionGroupV2Resp {
    repeated SearchOptionGroup search_option_group = 1;
}

// 分页获取剧本库列表  与logic不同
message GetDramaListReq {
    repeated SearchOption search_option = 1;
    uint32 page_token = 2; // 分页游标 上一页的最后一个id
    uint32 page_size = 3; // 单页条目数
    string next_page_token = 4; // 分页token，第一页为空
}

// 分页获取剧本库列表
message GetDramaListResp {
    repeated DramaInfoWithStatus drama_sub_info_list = 1;  //剧本概要信息列表
    string next_page_token = 2;// 下一页token
}

message BatchGetDramaSubInfoByIdsReq {
    repeated uint32 id_list = 1;
}

message BatchGetDramaSubInfoByIdsResp {
    repeated DramaSubInfo list = 1;
}

// 剧本详情v2, 客户端通过命令调用
message GetDramaDetailByIdV2Req {
    uint32 id = 1; // 剧本id
}

message GetDramaDetailByIdV2Resp {
    DramaV2 drama = 1;
    bool has_play = 2; // 是否有房间在玩
    uint32 collected_count = 3; // 收藏数
}

// 测试剧本删除接口
message TestDeleteDramaReq {
    enum TestType {
        TEST_TYPE_DELETE_DRAMA = 0;
        TEST_TYPE_SYNC_TIMER = 1;
        TEST_RELOAD_SEARCH_GROUP = 2;
        TEST_CHANGE_RUNNING_ON_FLY = 3;
        TEST_TYPE_SYNC_DRAMA_BY_ID = 4;
        TEST_TYPE_REBUILD_COLLECT_DATA_FROM_CACHE = 5; // 从cache中重建数据到mongo
    }
    repeated uint32 id_list = 1;
    TestType type = 2;
}

message TestDeleteDramaResp {

}

// 在玩房间状态
enum PiaChannelStatusType {
    PIA_CHANNEL_STATUS_INVALID = 0; // 非法值
    PIA_CHANNEL_STATUS_PLAY = 1;  // 走本中
    PIA_CHANNEL_STATUS_WAIT = 2;  // 等你来pia
    PIA_CHANNEL_STATUS_HOT = 3;  // 热播中
}

// 获取正在玩的房间
message PlayingChannelInfoV2 {
    uint32 channel_id = 1;              // 频道id
    string channel_name = 2;            // 频道名字
    uint32 channel_member_count = 3;    // 频道人数(热度)
    uint32 channel_display_id = 4;      // 房间的显示ID
    uint32 channel_type = 5;  // 房间类型
    string channel_icon = 6; // 房间图标的MD5
    PiaChannelStatusType channel_status_type = 7;
    string channel_desc = 8; // 房间话题的描述(标题)
    string channel_creator_account = 9; // 频道创建者account
    uint32 channel_bind_id = 10; // 公会id
}

// 获取正在玩的房间
message GetPlayingChannelV2Req {
    uint32 drama_id = 1;
    uint32 page_index = 2;
    uint32 page_size = 3;
}
message GetPlayingChannelV2Resp {
    repeated PlayingChannelInfoV2 playing_channels = 1;
}

// 获取剧本展开页
message GetDramaExpansionReq {
    uint32 drama_id = 1; // 剧本ID
}

message GetDramaExpansionResp {
    repeated string tags = 1; // 标签
    string descr = 2; // 剧本简介
    string content = 3; // 剧本全文
    repeated PiaRole role_list = 4; // 角色列表
    PiaPlayType play_type = 5; // 播放类型
}

enum PiaRankingListType {
    PIA_RANKING_LIST_INVALID = 0;
    PIA_RANKING_LIST_COLLECT = 1;   // 收藏榜
    PIA_RANKING_LIST_RISE = 2;   // 上升榜
}

// 获取排行榜剧本列表Req
message PiaGetRankingListReq {
    PiaRankingListType ranking_list_type = 1;
    string page_token = 2; // 分页token 第一页传空
    uint32 page_size = 3; // 单页条目数
}

// 获取排行榜剧本列表Resp
message PiaGetRankingListResp {
    repeated DramaInfoWithStatus drama_info_with_status_list = 1;  //剧本概要信息列表
    string page_token = 2; // 服务端返回的token,为空则无需后续请求
}

//======================剧本库========================

//=============== 模式切换============================

// 切换成Pia戏模式 V2
message SetPiaSwitchV2Req {
    uint32 channel_id = 2;
    bool is_open = 3;           // 开启Pia戏玩法
}

message SetPiaSwitchV2Resp {
}
//=============== 模式切换============================

// 获取ugc的pia戏房间-请求
message GetUgcChannelsReqV2 {
    int32 page_index = 1;// 页码
}
// 获取ugc的pia戏房间-响应
message GetUgcChannelsRespV2 {
    repeated uint32 channel_ids = 1;
    bool has_more = 2;
}

//============= 剧本收藏相关 ==========

enum CollectOpType {
    PIA_DRAMA_COLLECT_INVALID = 0;
    PIA_DRAMA_COLLECT_SELECT = 1;   // 收藏
    PIA_DRAMA_COLLECT_CANCEL = 2;   // 取消收藏
}

//(取消)收藏剧本
message DoUserDramaCollectReq {
    uint32 uid = 1;
    uint32 drama_id = 2;
    CollectOpType op = 3;   // 操作类型
}

message DoUserDramaCollectResp {
    uint32 collect_num = 1;
    bool is_collected = 2;
}

//获取剧本收藏状态、人数
message GetUserDramaStatusReq {
    uint32 uid = 1;
    uint32 drama_id = 2;
}

message GetUserDramaStatusResp {
    uint32 collect_num = 1;
    bool is_collected = 2;
}

//获取剧本收藏列表
message GetUserDramaCollListReq {
    repeated SearchOption search_option = 1;
    uint32 page_token = 2;      // 分页游标 上一页的最后一个id
    uint32 page_size = 3;       // 单页条目数
    uint32 uid = 4;
}
message GetUserDramaCollListResp {
    repeated uint32 drama_id_list = 1;
    uint32 last_coll_time = 2;  //for page_token
}

message GetDramaListByIdsReq {
    repeated uint32 drama_id_list = 1;
}

message GetDramaListByIdsResp {
    repeated DramaInfoWithStatus drama_sub_info_list = 1;  //剧本概要信息列表
}

//============= 剧本收藏相关 ==========

// ============================= 剧本置顶管理 ==============
message GetDramaInfoByDramaIdReq {
    uint32 drama_id = 1;
}

// 剧本基本信息
message DramaInfoBase {
    string title = 1; // 剧本名称
    uint32 author_id = 2; // 编剧id
    string ttid = 3; // 编剧tt端内id
}
message GetDramaInfoByDramaIdResp {
    DramaInfoBase drama_info = 1; // 
}


// 增加置顶剧本 (运营后台调用)
message AddStickDramaV2Req {
    StickDramaBaseInfo stick_drama_info = 1;
}

// 更新置顶记录 （运营后台调用）
message UpdateStickDramaV2Req {
    uint32 stick_id = 1; // 置顶记录id
    StickDramaBaseInfo stick_drama_info = 2; // 置顶信息
}

message CheckStickDramaTimeReq {
    uint32 stick_id = 1; // 置顶记录id
    StickDramaBaseInfo stick_drama_info = 2; // 置顶信息
}

// 删除置顶
message DeleteStickDramaReq {
    uint32 stick_id = 1;
}

// 置顶状态
enum StickDramaStatus {
    STICK_DRAMA_STATUS_ALL = 0; // 默认全部返回
    STICK_DRAMA_STATUS_STICKING = 1; // 生效中
    STICK_DRAMA_STATUS_EXPIRED = 2; // 已过期
    STICK_DRAMA_STATUS_WAITING = 3; // 待生效
}

// 排序类型
enum SortOptionType {
    SORT_OPT_NIL = 0; // 默认按生效中、rank降序排序
    SORT_OPT_RANK_DESC = 1; // 按rank降序排序
    SORT_OPT_RANK_ASC = 2; // 按rank升序排序
    SORT_OPT_BEGIN_TIME_DESC = 3; // 按begin_time降序排序
    SORT_OPT_BEGIN_TIME_ASC = 4; // 按begin_time升序排序
    SORT_OPT_END_TIME_DESC = 5; // 按end_time降序排序
    SORT_OPT_END_TIME_ASC = 6; // 按end_time升序排序
}

// 获取置顶列表（筛选）
message SearchStickDramaReq {
    uint32 drama_id = 1;
    string title = 2; // 剧本名称
    uint32 author_id = 3; // 编剧id
    string ttid = 4; // tt端内id
    string tag = 5; // 标签
    StickDramaStatus stick_status = 6;
    int64 stick_begin_time = 7; // 置顶开始时间
    int64 stick_end_time = 8; // 置顶结束时间
    uint32 page_index = 9; // 分页页号
    uint32 page_size = 10; // 分页大小
    SortOptionType sort_opt_type = 11; // 排序类型

}

// 置顶记录列表， 返回值带置顶id
message SearchStickDramaResp {
    message StickDramaInfo {
        uint32 stick_id = 1; // 置顶id，用于删除 和 编辑
        StickDramaBaseInfo base_info = 2; // 置顶信息
        DramaInfoBase drama_info = 3; // 剧本信息
        StickDramaStatus stick_status = 4; // 置顶状态
    }
    repeated StickDramaInfo stick_drama_info_list = 1; // 置顶记录列表
    uint32 count = 2;   // 符合条件的记录总数，用于计算分页总页数
}

// 获取剧本置顶标签列表
message GetStickDramaTagsReq {

}

message GetStickDramaTagsResp {
    repeated string tag_list = 1;
}

// ============================= 剧本置顶管理 ==============

//======================切换玩法========================

message PiaChannelDramaPlayingType {
    enum PlayingTypeRole {
        PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_INVALID = 0;
        PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_NONE = 1; // 没角色
        PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_HAS = 2; // 有角色
    }
    enum PlayingTypeTime {
        PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_INVALID = 0;
        PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_NONE = 1; // 没时间
        PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_HAS = 2; // 有时间
    }
    repeated PlayingTypeRole support_playing_type_roles = 1; // 当前支持的角色类型
    repeated PlayingTypeTime support_playing_type_times = 2; // 当前支持的时间类型
    PlayingTypeRole current_playing_type_role = 3; // 当前玩法的角色类型
    PlayingTypeTime current_playing_type_time = 4; // 当前玩法的时间类型
}

// 剧本切换玩法请求
message PiaChangePlayTypeReq {
    PiaChannelDramaPlayingType channel_drama_playing_type = 1; // 只要填写当前玩法的角色类型和时间类型即可
    uint32 channel_id = 2; // 房间id
    int64 round_id = 3; // 版本号
}
// 剧本切换玩法响应
message PiaChangePlayTypeResp {
    PiaChannelDramaPlayingType channel_drama_playing_type = 1; // 当前玩法的角色类型和时间类型
}

// 内部停止玩法接口请求
message InternalStopChannelReq {
    enum StopType {
        STOP_TYPE_INVALID = 0;
        STOP_TYPE_NORMAL = 1; // 停止指定的房间
        STOP_TYPE_ALL = 2; // 停止所有的房间
    }
    StopType stop_type = 1;
    repeated uint32 channel_id = 2; // 房间id
}
// 内部停止玩法接口响应
message InternalStopChannelResp {
    repeated uint32 channel_id = 1;// 停止的房间id
}

//======================切换玩法========================

//======================我的参演记录========================

// 获取我的参演记录列表请求
message GetMyDramaPlayingRecordReq {
    uint32 page_size = 1; // 每页数量
    string page_token = 2; // 服务端返回的page_token，第一页传默认零值
    repeated SearchOption search_option = 3; // 搜索条件
    uint32 uid = 4; // 用户id
}
// 获取我的参演记录列表响应
message GetMyDramaPlayingRecordResp {
    repeated PiaDramaPlayingRecord drama_playing_record_list = 1;
    string page_token = 2; // 请求下一页时带上，返回空值时，没有下一页
}
// 我的参演记录
message PiaDramaPlayingRecord {
    DramaSubInfo drama_sub_info = 1; // 剧本信息
    int64 playing_time = 2; // 参演时间
    bool is_pull_off = 3;// 是否被下架
    string id = 4;// 记录id
    bool is_copy = 5; // 是否是副本
}

// 批量删除我的参演记录请求
message PiaBatchDeleteMyPlayingRecordReq {
    uint32 uid = 1; // 用户id
    repeated string record_id_list = 2; // 记录id列表
}
// 批量删除我的参演记录响应
message PiaBatchDeleteMyPlayingRecordResp {
}

// 根据筛选条件获取所有相关参演记录id列表
message PiaGetMyPlayingRecordIdListReq {
    uint32 uid = 1;
    repeated SearchOption search_option = 2;
}

// 根据筛选条件获取所有相关参演记录id列表响应
message PiaGetMyPlayingRecordIdListResp {
    repeated string record_id_list = 2; // 记录id列表
}


//======================我的参演记录========================

//=====================重建剧本收藏缓存=====================

message PiaRebuildDramaCollectedCacheReq {}
message PiaRebuildDramaCollectedCacheResp {
    uint32 rebuild_count = 1; // 重建的数量
}
//=====================重建剧本收藏缓存=====================


//=====================处理剧本举报结果=====================

message PiaHandleDramaReportResultReq {
    string id = 1; // 举报记录的id
    bool is_accept = 2;// 是否接受举报
}
message PiaHandleDramaReportResultResp {

}

//=====================处理剧本举报结果=====================

// ==================== 用户剧本反馈 ======================

// 剧本反馈req
message PiaAddDramaFeedBackReq {
    enum FeedBackType {
        FEEDBACK_TYPE_INVALID = 0; // 无效类型
        FEEDBACK_TYPE_LACK_OF_BGM = 1; // 剧本缺少BGM
        FEEDBACK_TYPE_TYPESETTING_UNCLEAR = 2; // 剧本的排版不够清晰
        FEEDBACK_TYPE_HOPE_OST = 3; // 希望能出个原配OST
        FEEDBACK_TYPE_HOPE_SEQUEL = 4; // 希望这个本能出续集
        FEEDBACK_TYPE_LINES_COLOR_GLARING = 5; // 部分台词颜色刺眼
        FEEDBACK_TYPE_WRONG_SOUND_MARK = 6; // 剧本音效标识错误
        FEEDBACK_TYPE_BGM_SORTING_ERROR = 7; // BGM列表排序错误
        FEEDBACK_TYPE_WRONG_BGM_SOUND_EFFECT = 8; // BGM音效声有问题
        FEEDBACK_TYPE_OTHER = 9; // 其他
    }
    uint32 uid = 1; // 反馈用户uid
    FeedBackType feed_back_type = 2; // 问题类型
    string content = 3; // 问题描述
    repeated string picture_list = 4; // 图片
    uint32 drama_id = 5; // 剧本唯一ID
}

// 剧本反馈resp
message PiaAddDramaFeedBackResp {
}

// ==================== 用户剧本反馈 ======================

// ==================== 台词定位/走本跟随 =================

// 发送台词定位请求
message PiaSendDialogueIndexRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;  // 当前房间id
    uint32 dialogue_index = 3; // 段落数
    repeated uint32 uid_list = 4; // 台词定位的接收用户列表
    int64 round_id = 5; // 场次id
}

// 发送台词定位请求
message PiaSendDialogueIndexResponse {
}

// 发起跟随请求
message PiaFollowMicRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;  // 房间id
    int64 round_id = 3;  // 场次id
    uint32 target_mic = 4; // 跟随对象 透传logic枚举
    uint32 my_mic = 5; // 跟随对象 透传logic枚举
}

// 发起跟随响应
message PiaFollowMicResponse {
    uint32 dialogue_index = 1; // 返回当前跟随麦位段落数
}

// 取消跟随 用户发生滑动时调用（仅麦上用户调用）
message PiaUnFollowMicRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;
    int64 round_id = 3;
    uint32 my_mic = 4; // 我的麦位
}

// 取消跟随响应
message PiaUnFollowMicResponse {
}

// 上报段落请求
message PiaReportDialogueIndexRequest {
    uint32 uid = 1; // uid
    uint32 channel_id = 2; // 房间id
    int64 round_id = 3; // 场次id
    uint32 dialogue_index = 4; // 段落数
    uint32 my_mic = 5; // 我的麦位 麦下用户不上报
}

// 上报段落请求响应
message PiaReportDialogueIndexResponse {
}

// 获取上个用户的段落记录请求
message PiaGetPreviousDialogueIndexRequest {
    uint32 uid = 1; // uid
    uint32 channel_id = 2; // 房间id
    int64 round_id = 3; // 场次id
    uint32 my_mic = 4; // mic位号
}

// 获取上个用户的段落记录请求响应
message PiaGetPreviousDialogueIndexResponse {
    uint32 dialogue_index = 1; // 段落数
}

// 获取我的跟随状态请求
message PiaGetMyFollowInfoRequest {
    uint32 uid = 1;
    uint32 channel_id = 2;
    int64 round_id = 3;
}

// 获取我的跟随状态响应
message PiaGetMyFollowInfoResponse {
    uint32 target_mic = 1;
}

message PiaGetFollowedStatusOfMicListRequest {
    uint32 channel_id = 1;
    int64 round_id = 2;
    repeated uint32 uid_list = 3; // 麦位上用户列表
}

message PiaGetFollowedStatusOfMicListResponse {
    map<uint32, bool> uid_status_map = 1;
}

//========================新pia戏玩法=================

//======================作者个人作品页========================

// 批量获取作者基础信息请求
message PiaBatchGetAuthorBaseInfoReq {
    repeated uint32 author_id_list = 1;// 作者id
}

// 批量获取作者基础信息响应
message PiaAuthorBaseInfo {
    uint32 author_id = 1; // 作者id
    string nickname = 2; // 昵称
    uint32 uid = 3; // 作者uid，tt端内的uid，如果没有绑定则为0
}
message PiaBatchGetAuthorBaseInfoResp {
    map<uint32, PiaAuthorBaseInfo> author_info_map = 1;
}

// 作者个人页信息
message PiaAuthorGenInfoReq {
    uint32 author_id = 1;// 作者id
}

// 作者个人页信息
message PiaAuthorGenInfoResp {
    uint32 drama_count = 1; // 作品数量
    uint32 collected_count = 2; // 收藏数量
    uint32 author_id = 3; // 作者id
    string nickname = 4; // 昵称
    uint32 uid = 5; // 作者uid，tt端内的uid，如果没有绑定则为0
}

enum PiaAuthorWorksListSortType {
    PIA_AUTHOR_WORKS_LIST_SORT_TYPE_INVALID = 0;
    PIA_AUTHOR_WORKS_LIST_SORT_TYPE_COLLECTED_COUNT = 1; // 按收藏数量排序
    PIA_AUTHOR_WORKS_LIST_SORT_TYPE_CREATE_TIME = 2; // 按创建时间排序，默认类型
}

message PiaAuthorWorksListReq {
    string page_token = 1; // 分页token 第一页传空
    uint32 page_size = 2; // 单页条目数
    uint32 author_id = 3; // 作者id
    PiaAuthorWorksListSortType sort_type = 4; // 排序类型
}

message PiaAuthorWorksListResp {
    repeated DramaInfoWithStatus drama_list = 1;  //剧本概要信息列表
    string page_token = 2; // 服务端返回的token,为空则无需后续请求
}

message PiaAuthorGenInfoRebuildReq {
    uint32 author_id = 1;// 作者id，0表示重建所有
}

message PiaAuthorGenInfoRebuildResp {
    uint32 author_id = 1;// 作者id
    uint64 drama_count = 2; // 作品数量
}

//======================作者个人作品页========================

//======================副本库列表========================

message PiaCopyDramaInfoWithStatus {
    DramaSubInfo drama_sub_info = 1; // 剧本信息
    bool is_public = 3; // 是否公开
    bool is_delete = 4; // 是否删除
    uint32 use_count = 5; // 使用次数
}

// 具体某个副本的副本库列表
message PiaCopyDramaListRequest {
    string page_token = 1; // 分页token 第一页传空
    uint32 page_size = 2; // 单页条目数
    uint32 drama_id = 3; // 剧本id
    uint32 uid = 4; // 用户uid
    bool is_public = 5; // 是否公开
    uint32 exclude_uid = 6; // 排除的用户uid
}

message PiaCopyDramaListResponse {
    repeated PiaCopyDramaInfoWithStatus copy_drama_list = 1;  // 副本列表
    string next_page_token = 2; // 服务端返回的token,为空则无需后续请求
}

// 获取我的副本库列表，带搜索条件
message GetMyDramaCopyListReq {
    uint32 uid = 1;
    repeated SearchOption search_option = 2;
    string page_token = 3; // 分页token 第一页传空
    uint32 page_size = 4; // 单页条目数
}

// 获取我的副本库列表 响应
message GetMyDramaCopyListResp {
    repeated PiaCopyDramaInfoWithStatus copy_drama_item_list = 1;  // 我的副本列表
    string next_page_token = 2; // 服务端返回的token,为空则无需后续请求
}

// 副本状态管理
message SetDramaCopyStatusReq {
    enum DramaCopyStatus {
        DRAMA_COPY_STATUS_INVALID = 0;
        DRAMA_COPY_STATUS_PUBLIC = 1;
        DRAMA_COPY_STATUS_PRIVATE = 2;
    }
    uint32 uid = 1;
    uint32 id = 2; // 副本id
    DramaCopyStatus drama_copy_status = 3;
}

// 副本状态管理resp
message SetDramaCopyStatusResp {
}

// 删除副本req
message DeleteDramaCopyReq {
    uint32 uid = 1;
    repeated uint32 id_list = 2;
}

// 删除副本resp
message DeleteDramaCopyResp {
}

//======================副本库列表========================

//======================确认保存副本========================

message PiaConfirmCopiedDramaRequest {
    string confirm_copy_drama_id = 1; // 副本的确认id，出现超过单个剧本上限时，需要用户确认是否保存副本，然后再次请求确认接口时使用
    uint32 covered_copy_drama_id = 2; // 确认被覆盖的副本id
}

message PiaConfirmCopiedDramaResponse {

}
//======================确认保存副本========================

//======================创建副本，但是不确认========================

message PiaCreateCopiedDramaRequest {
    uint32 uid = 1; // 用户id
    uint32 channel_id = 2; // 房间id
    int64 round_id = 3; // 场次id
    bool is_public = 4; // 是否公开
}

message PiaCreateCopiedDramaResponse {
    string confirm_copy_drama_id = 1; // 副本的确认id
    bool is_over_single_limit = 2; // 是否超过单个剧本上限
    repeated PiaCopyDramaInfoWithStatus drama_list = 3; // 如果超过了单个剧本的上限，则增加副本列表
}
//======================创建副本，但是不确认========================

message PerformDramaRequest {
    string temp_drama_id = 1; // 剧本临时id
    uint32 channel_id = 2; // 房间id
    DramaV2 drama_info = 3; // 剧本信息
}

message PerformDramaResponse {
    ChannelDramaStatus drama_status = 1; // 房间剧本状态
}


message PiaCreateTempDramaRequest {
    uint32 drama_id = 1; // 剧本id
}

message PiaCreateTempDramaResponse {
    string temp_drama_id = 1; // 剧本临时id
    DramaV2 drama = 2; // 剧本信息
}

message PiaGetTempDramaRequest {
    string temp_drama_id = 1; // 剧本临时id
}

message PiaGetTempDramaResponse {
    DramaV2 drama = 1; // 剧本信息
}


message PiaChangePlayTypeV2Request {
    PiaChannelDramaPlayingType channel_drama_playing_type = 1; // 只要填写当前玩法的角色类型和时间类型即可
    uint32 channel_id = 2; // 房间id
    int64 round_id = 3; // 版本号
    DramaV2 drama = 4; // 剧本信息
}

message PiaChangePlayTypeV2Response {
    PiaChannelDramaPlayingType channel_drama_playing_type = 1; // 当前玩法的角色类型和时间类型
}

//message PiaCreateTimeLineRequest {
//    uint32 channel_id = 1; // 房间id
//    int64 round_id = 2; // 版本号
//}
//
//message PiaCreateTimeLineResponse {
//    message TimeLineItem {
//        string id = 1; // item id
//        PiaDuration duration = 2; // item时长
//    }
//    repeated TimeLineItem content_time_line = 1; // 内容时间线
//    repeated TimeLineItem bgm_time_line = 2; // 背景音乐时间线
//}

// pia戏走本状态变更kafka事件
message PiaDramaStatusChangeEvent {
    enum DramaStatusType {
        DRAMA_STATUS_TYPE_INVALID = 0;
        DRAMA_STATUS_TYPE_START = 1; // 开始走本
        DRAMA_STATUS_TYPE_STOP = 2; // 停止走本
    }
    uint32 channel_id = 1; // 房间id
    DramaStatusType change_type = 2; // 变更类型
}