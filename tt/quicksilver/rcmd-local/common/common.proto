syntax = "proto3";

package rcmd.common;

option go_package = "golang.52tt.com/protocol/services/rcmd/common";

enum RecallQId{
  RecallQId_Unknow = 0;
  RecallQId_RealTime_Sex_0_Age_0 = 1; //小池子不考虑性别年龄的召回
  RecallQId_RealTime_Sex_1_Age_1 = 2; //小池子考虑性别年龄的召回
  RecallQId_Sex_1_Age_1 = 3; //大池子召回
  RecallQId_V03_Active_User = 4; //0.3版本召回
  RecallQId_V03_Active_User_Core = 5; //考虑核心用户的重排召回
}

//////////召回用的配置
//召回队列
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message RecallQ{
  uint32 Id = 1; //召回队列的标示
  string name = 2; //召回队列
  uint32 limit = 3; //召回的上限
  uint32 ratio = 4; //召回的比例，加起来等于100
  uint32 priority = 5; //召回的优先级
}

enum ConfigType{
  ConfigType_Unknow = 0;
  ConfigType_Num = 1;   //按数量召回
  ConfigType_Ratio = 2; //按比例召回
}

// 设置召回策略时，ab测填写的json参数，后面由算法同事自己配
message RecallConfig{
  repeated RecallQ recall_queue_list = 1; //召回队列
  uint32 config_type = 2; // enum ConfigType

  //召回总量，只有按比例召回(config_type=2)，或者有优先级的情况下，此字段才生效
  uint32 total = 3;
}

//////////排序用的配置
//虽然定义了，但实际上model服务决定使用哪个模型是根据实验组名称
enum RankStrategyId{
  RankStrategyId_Unknow = 0;
  RankStrategyId_Rule = 1; //规则版排序
  RankStrategyId_GBDT = 2; //算法版排序
  RankStrategyId_GBDT_IM = 3; //开聊率模型排序
  RankStrategyId_GBDT_IM_Core = 4; //带核心用户重排的开聊率模型
  RankStrategyId_GBDT_IM_Back = 5; //互聊率模型
  RankStrategyId_GBDT_IM_Back_Train_Online = 6; //用在线数据训练的开聊率模型
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message RankConfig {
  uint32 Id = 1;
  string name = 2;
}

message RecallRankConfig{
  RecallConfig recall_config = 1;
  RankConfig rank_config = 2;
  string exp_name = 3 ; //实验名称 , "实验组A","实验组B","对照组"
}


//ab测试实验的layerId
enum LayerId{
  LayerId_Unknow = 0;
  LayerId_EnterV2 = 1;
  //  "v0.1"
  //  "v0.2"
  LayerId_PlaymateStrategy_NoUse_Test = 2;  //已废弃，用下面的3
  //  "recall_sex_1_age_1_sort_rule"
  //  "recall_sex_0_age_0_sort_algo"
  //  "recall_sex_1_age_1_sort_algo"
  LayerId_PlaymateRecallRankStrategy = 3; //发放玩伴的召回排序策略
  // value = json(RecallRankConfig)
}


message LayerInfo{
  uint32 layer_id = 1; //enum LayerId
  string value = 2;
  string namespace = 3;
}

message ABTestConfigJson {
  map<uint32, LayerInfo> layers = 1;
}

message RcmdBaseReq{
  uint32 uid = 1; //请求用户的uid
  ABTestConfigJson config_json = 2; //ab测试配置的字符串解析之后的结构体
  string trace_id = 3; // 业务他们的trace_id好像不太对，直接自己生成，然后在推荐系统里面传递（字段名从span_id修改为 trace_id)
  string pass_through = 4; // 透传信息字段
  uint32 debug_flag = 5; //是否调试 1-是 0-否
  ABTestInfo ab_test_info = 6; //ab测试参数
}

message TopicChannelBaseReq{
  uint32 uid = 1; //请求用户的uid
  ABTestConfigJson config_json = 2; //ab测试配置的字符串解析之后的结构体
}

enum RcmdRspCode{
  CODE_UNKNOW = 0;
  CODE_SUCCESS = 1;
  CODE_PARAMS_FORMAT_ERROR = 2; //参数错误
  CODE_DATABASE_ERROR = 3; //数据库异常

  //playmate    1000 - 2000
  CODE_PLAYMATE_LOSS_AB_TEST_CONFIG = 1001; //请求参数缺乏ab测试配置的参数
  CODE_PLAYMATE_MODEL_NOT_FOUND = 1002; //没有找到对应版本的模型

  //topic-channel  2000 - 3000
}
message RcmdBaseRsp{
  uint32  code = 1; //返回码，1为正常，其他参照enum RcmdRspCode
  string  msg = 2; //错误的原因
  map<string, string> profile_info = 3;
}

enum Env {
  Env_Prod = 0;
  Env_Staging = 1;
  Env_Test = 2;
}

message RcmdCommonDataBatchEvent{
  string src_ip = 1;
  uint32 server_timestamp = 2;
  string biz_type = 3;
  repeated bytes data = 4;
  string version = 5; //代表schema的版本, 标注如何解析数据
  uint32 batch_num = 6; //批次序号。 比如:上游发送同一个schema version的kafka数据, 逻辑处理错了, 需要修改并重新上线, 但数据是被实时消费, 旧版不能立刻停止, 则使用不同批次序号来标记新旧版的区别
  uint32 env = 7; // enum Env 环境：内网，灰度，正式
}

message RcmdBrowseInfo {
  repeated uint32 no_browse_list = 1; //上次请求未曝光id列表
}

message ABTestInfo {
  map<string, string> info = 1;
  string source = 2; // 是来源哪个功能的, 如topic_channel或其他
  bool is_total_layer = 3; // 是否是对照域的total layer 的流量
}

message LocationInfo {
  enum AreaLevel {
    AreaLevel_Normal = 0; // 可以正常外显
    AreaLevel_Sensitive = 1; // 敏感地区，建议不外显
  }

  string country = 1;
  string province = 2;
  string city = 3;
  string country_code = 4;
  uint32 province_code = 5;
  uint32 city_code = 6;
  bool   show_geo_info = 7;
  AreaLevel area_level = 8; // 地区敏感等级
  double latitude = 9;
  double longitude = 10;
}

message RecallMetaData{
  string req_id = 1;
  RcmdScene scene = 2;
  RecallQueueId queue_id = 3;

  //标识是否使用有序过滤
  bool use_order_filter = 4;
  string queue_name = 5;
}

enum RecallActionFormula {
  UnknownFormula = 0;
  MatrixMultiplicationFormula = 1;
  PitFillingFormula = 2;
}

//推荐场景
enum RcmdScene{
  UnknownRcmdScene = 0;


  //1000-1999 帖子/广场
  RcmdStream = 1000;//推荐流
  RcmdTopic = 1001;//话题流 - 算法版
  RcmdPostVoiceControl = 1002;//广场 - 声控专区（主播专区）

  // 直播房
  RcmdLiveChannel = 2000;//直播房
}

//召回队列id
//不使用老的RecallQId
enum RecallQueueId{
  UnknownRecallQueue = 0;

  //[10000,20000) 广场/帖子
  NewPoolRecall = 10000;//新帖召回(A池)
  NewMidPoolRecall = 10001;//新帖中池召回(B池)
  HighInteractPoolRecall = 10002;//高互动池召回
  PostAlgoOfflineRecall10 = 10003;//广场算法1.0离线召回
  PostAlgoOfflineRecallHighQuality = 10004;//广场算法1.1高质池召回
  PostAlgoOfflineRecall11 = 10005;//广场算法1.1离线召回
  PostAlgoOfflineRecallHot = 10006;//广场算法1.0离线热门兜底召回
  PostAlgoOnlineItemCF = 10007;//广场算法1.2在线itemcf
  PostAlgoOnlineHotGlobal = 10008;//广场算法1.2.1在线热点全局
  PostAlgoOnlineItem2Vec = 10009;//广场算法1.2.1在线item2vec
  PostAlgoOnlineUser2Vec = 10010;//广场算法1.2.1在线user2vec
  PostAlgoOnlineHotRegional = 10011;//广场算法1.2.1在线热点分用户属性
  PostAlgoOfflineUserCF = 10012;//广场算法1.2.6离线usercf
  PostAlgoOfflineRelationChain = 10013;//广场算法1.2.6离线关系链
  PostAlgoOnlineUser2Pub2Item = 10014;//广场算法1.2.6近线u2p2i
  PostAlgoOnlineUser2PubVec2Item = 10015;//广场算法1.2.6近线u2pvec2i
  NewPoolRecall3Min = 10016;//最新3min内的新帖召回(A池)
  PostAlgoNewPoolRecall = 10017;//算法版新帖召回(A池)
  PostAlgoOnlineBert = 10018;//近线bert内容近邻
  PostAlgoHourCDTOM = 10019;//CDTOM召回
  PostAlgoNearlineHear = 10020;//近线热门召回
  PostAlgoOnlineUser2VecPub = 10021;//在线user2vecpub召回
  PostHeartIslandRecall = 10022;//心岛话题帖子召回
  PostImgTagHotRecall = 10023;//近线热点（分性别+图片标签）召回
  PostForMonitorRecall = 10024;//监管特殊召回
  PostAIRapRecall = 10025;//AI说唱话题帖子召回
  PostInterestHotRecall = 10026;//广场兴趣标签召回
  PostNewPreferRecall = 10027;//广场新帖偏好召回
  PostNewInterestRecall = 10028;//广场新帖兴趣品类召回
  PostNewNotInterestRecall = 10029;//广场新帖非兴趣品类召回
  PostTopCtrRecall = 10030;//广场高Ctr帖子召回
  PostDiscussTopicRecall = 10031;//广场讨论话题帖子召回

  PostTimerRecallBoutique = 10501;//广场精品帖子定时召回

  PostTopicRecall = 11000;//话题流算法版召回
  PostTopicVoiceControlStreamRecall = 11001;//声控专区召回
  PostTopicLiveRecall = 11002;//声控专区直播中的帖子召回
  PostTopicVideoAudioRecall = 11003;//声控专区视频或音频的帖子召回

  PostSameCityPreferRecall = 12001;//广场同城偏好召回
  PostSameCityInterestRecall = 12002;//广场同城兴趣召回
  PostSameCityFocusInterestRecall = 12003;//广场同城重点兴趣召回
  PostSameCityNonInterestRecall = 12004;//广场同城非兴趣召回
  PostSameProvPreferRecall = 12005;//广场同省偏好召回
  PostSameProvInterestRecall = 12006;//广场同省兴趣召回
  PostSameProvFocusInterestRecall = 12007;//广场同省重点兴趣召回
  PostSameProvNonInterestRecall = 12008;//广场同省非兴趣召回
  PostSameCityAsyncCacheRecall = 12009;//广场同城缓存

  // 直播房
  LiveChannelRecall = 20000;//直播房召回
  ChatCardRecall = 21000;//扩列墙召回
  PerfectMatchQuestion = 22000; //天配问答题召回
  //music-channel
  MusicChannelUCanUSing = 30000;
  MusicChannelUCUS_EP = 30001;//EP == excellent producer
  MusicChannelKTV = 30002;
  MusicChannelKTV_HQ = 30003;
  MusicChannelKTV_EP = 30004;
  MusicChannelKTV_FavorSong = 30005;
  MusicChannelKTV_HotSong = 30006;
  MusicChannelListenSong = 30007;
  MusicChannelRapTogether = 30008;
  MusicChannelSingAndChat = 30009;
  MusicChannelMakeFriendSinging = 30010;
  MusicChannelUnknown = 30011;
  MusicChannelUCUS_SK = 30012;//SK ==  social king
  MusicChannelUCUS_CC = 30013;//CC == content consumer
  MusicChannelUCUS_IU = 30014;//IU == imprecise user
  MusicChannelListenSong_HQ = 30015;
  MusicChannelRapTogether_HQ = 30016;
  MusicChannelSecondLabel = 30017; //千人千面二级标签
  KaiHeiChannel = 30018; //开黑房间召回
  MusicChannel_Low_delivery = 30019;// 音乐算法低流量召回
  MusicChannelCollect = 30020; //收藏房间
  MusicChannelFollow = 30021; //关注房主的房间
  MusicChannelOftenStay = 30022; //T+7逗留时长>10min房间
  MusicChannelTypeStream = 30023; //筛选流分标签召回
  MusicChannelRecallAll = 30024;
  MusicChannelSelectPage = 30025;
  MusicChannelPositive = 30026; //带用户正向行为的房间
  MusicChannelRelation = 30027; //召回关系链房间
  MusicChannelRelationRoomOwner = 30028; //召回最近7天，某天在房主的房间待过的时间大于等于5分钟，且房主现在在麦上的房间
  MusicChannelRelationMicUser = 30029; //召回最近7天，某天跟麦上的用户待过的时间大于等于5分钟，且用户现在在麦上的房间
  MusicChannelRelationSendIM = 30030; //召回最近7天，跟用户发过消息次数大于等于3次的（一来一回算两次），且用户现在在麦上的房间

  MusicChannelRelationAlgoIM = 30031; //互动关系链重逢用户算法版画像--01未结成关系链（互发im）
  MusicChannelRelationAlgoMic = 30032; //互动关系链重逢用户算法版画像--02未结成关系链（共同上麦）
  MusicChannelRelationAlgoMsg = 30033; //互动关系链重逢用户算法版画像--03未结成关系链（互发公屏）
  MusicChannelRelationAlgoStay = 30034; //互动关系链重逢用户算法版画像--04未结成关系链（共同在房）
  MusicChannelRelationAlgoNone = 30035; //互动关系链重逢用户算法版画像--05结成关系链（但7天内无互动）
  MusicChannelMicBand = 30036; //麦可乐队
  MusicChannelNewUser = 30037; //新用户发布的房间
  MusicChannelHighFollow = 30038; //高关注率的房间
  MusicChannelHighMic10s = 30039; //高上麦/唱歌率的房间
  MusicChannelRelationAlgoIMNebula_CS = 30040; //图数据库召回算法版弱关系链房间（互发im）
  MusicChannelRelationAlgoMicNebula_CS = 30041; //图数据库召回算法版弱关系链房间（共同上麦）
  MusicChannelRelationAlgoMsgNebula_CS = 30042; //图数据库召回算法版弱关系链房间（互发公屏）
  MusicChannelRelationAlgoStayNebula_CS = 30043; //图数据库召回算法版弱关系链房间（共同在房）
  MusicChannelRelationAlgoNoneNebula_CS = 30044; //图数据库召回算法版弱关系链房间（但7天内无互动）
  MusicChannelRelationAlgoIMNebula_RD = 30045; //图数据库召回算法版弱关系链房间
  MusicChannelRelationAlgoMicNebula_RD = 30046; //图数据库召回算法版弱关系链房间
  MusicChannelRelationAlgoMsgNebula_RD = 30047; //图数据库召回算法版弱关系链房间
  MusicChannelRelationAlgoStayNebula_RD = 30048; //图数据库召回算法版弱关系链房间
  MusicChannelRelationAlgoNoneNebula_RD = 30049; //图数据库召回算法版弱关系链房间

  SquareMixRecallKuoLie = 30101; // 召回扩列房
  SquareMixRecallStory = 30102; // 召回故事主题房
  SquareMixRecallRadioPlay = 30103; // 召回广播剧主题房
  SquareMixRecallTogetherPia = 30104; // 召回一起pia戏主题房
  SquareMixRecallFunny = 30105; // 召回搞笑主题房
  SquareMixRecallConstellation = 30106; // 召回星座主题房
  SquareMixRecallHotSpot = 30107; // 召回热点主题房
  SquareMixRecallStudy = 30108; // 召回学习交流主题房
  SquareMixRecallDraw = 30109; // 召回画画主题房
  SquareMixRecallEmotion = 30110; // 召回情感主题房
  SquareMixRecallVoiceControl = 30111; // 召回声控主题房
  SquareMixRecallSameCity = 30112; // 召回同城主题房
  SquareMixRecallRap = 30113; // 召回就是说唱主题房
  SquareMixRecallSing = 30114; // 召回唱歌主题房
  SquareMixRecallArgue = 30115; // 召回辩论主题房
  SquareMixRecallCommon = 30116; // 召回未标明主题房

  HomeMixedRecallAll = 30201; //首页混推房间全召回
  HomeMixedRecallSimilar = 30202; //首页混推相似召回

  ThemePopRecall = 30301; // 主题房弹窗下发召回

  SameCityOwner = 30401; // 同城房主召回
  SameCitySpare = 30402; // 同城后别

  TtcRoomRecallAll = 40000; // 全量召回
  TtcRoomRecallGameCard = 40001; // 游戏卡召回


  // rcmd-userr-pick
  UserPickPiecingRecallFriends = 45001;
  UserPickPiecingRecallPlaymates = 45002;
  UserPickPiecingRecallHighPraised = 45003;
  UserPickPiecingRecallNotComplete = 45004;
  UserPickPiecingRecallEverJoin = 45005;
  UserPickPiecingRecallPlayAny = 45006;
  UserPickPiecingRecallHighPraisedNew = 45007;
  UserPickPiecingRecallNotCompleteNew = 45008;
  UserPickPiecingRecallEverJoinNew = 45009;
  UserPickPiecingRecallPlayAnyNew = 45010;
  UserPickPiecingRecallEnterGame = 45011;
  UserPickPiecingRecallFitLowest = 45012;
  UserPickPiecingRecallWaitingChannel = 45013;
  UserPickTimerRecallOne = 45021;
  UserPickTimerRecallTwo = 45022;
  UserPickTimerRecallThree = 45023;

  //你行你唱歌曲
  MusicSongUCUS = 50000;
  MusicSongUCUS_NewSong = 50001;

  MusicSongKTV_Like = 50011;
  MusicSongKTV_Hot = 50012;
  MusicSongKTV_Rule_Hot = 50013; //纯规则版
  MusicSongKTV_User_Song = 50051; //用户属性歌曲
  MusicSongKTV_User_Have_Sing = 50052; //用户唱歌序列

  MusicSongKTV_List_Custom = 50014;
  MusicSongKTV_List_Zego = 50015;
  MusicSongKTV_List_Rule_Zego = 50016; //纯规则版
  MusicSongKTV_List_Hot = 50017;

  MusicSongKTV_Block_Custom = 50018;
  MusicSongKTV_Block_Zego = 50019;
  MusicSongKTV_Block_Rule_Zego = 50020; //纯规则版
  MusicSongKTV_Block_Hot = 50021;

  MusicSong_Listen_Order_Preference = 50031; //历史偏好
  MusicSong_Listen_Order_Hot = 50032;
  MusicSong_Listen_Order_Rule_Hot = 50033; //纯规则版
  MusicSong_Listen_Order_Explore = 50034; //探索
  MusicSong_Listen_Order_New_Hot = 50035; //新的热门召回队列

  // 预约开黑
  TopicChannelAppointmentFilter = 60000;

  // 速配-小游戏
  MiniGame_GeneralQueue = 70001;
  // anti-faker反冒充 uid召回
  AntiFakerNameSearchRecall = 80000;

  // rcmd-chat 仿真用户召回
  RCMDChat_GeneralQueue = 71000;

  // 游戏帖
  RCMDGamePost_GeneralQueue = 72001;
  RCMDGamePost_ForceInsertQueue = 72002;
  RCMDGamePost_NewPostQueue = 72003;
  RCMDGamePost_PublishTimeSortQueue = 72004;

  // rcmd-game-pal-card
  RcmdGamePalCardPreferQueue = 73001; // 偏好召回
  RcmdGamePalCardTimelinessQueue = 73002; // 时效性召回
  RcmdGamePalCardInteractionQueue = 73003; // 互动性召回
  RcmdGamePalCardFallbackQueue = 73004; // 互动性召回

  // 帖子搜索
  RCMDPostSearch_GeneralQueue = 74001; // 常规帖召回
  RCMDPostSearch_BoutiqueQueue = 74002; // 精品帖召回
  RCMDPostSearch_ForceInsertQueue = 74003; // 游戏专区强插帖召回
  RCMDPostSearch_FullMatchTopicQueue = 74004; // 全包含话题召回
}

enum RegulatoryLevel {
  FREE = 0; //不限制
  SIMPLE_MINOR = 1; // 未成年
}
