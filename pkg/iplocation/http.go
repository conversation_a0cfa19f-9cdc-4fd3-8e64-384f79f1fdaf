package iplocation

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"golang.52tt.com/pkg/log"
	"io/ioutil"
	"net"
	"net/http"
	"time"
)

type httpClient struct {
	cli *http.Client
}

func newHttpClient() *httpClient {
	return &httpClient{
		cli: &http.Client{
			Transport: &http.Transport{
				Proxy: http.ProxyFromEnvironment,
				DialContext: (&net.Dialer{
					Timeout:   5 * time.Second,
					KeepAlive: 30 * time.Second,
				}).DialContext,
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 100,
				IdleConnTimeout:     time.Duration(90) * time.Second,
			},
			Timeout: 5 * time.Second,
		},
	}
}

func (c *httpClient) do(ctx context.Context, method, url string, data []byte, ret interface{}) error {
	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewBuffer(data))
	if err != nil {
		log.ErrorWithCtx(ctx, "do NewRequestWithContext method(%s) url(%s) err: %v", method, url, err)
		return err
	}

	if len(data) > 0 {
		req.Header.Set("Content-Type", "application/json")
	}

	resp, err := c.cli.Do(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "do Do method(%s) url(%s) data(%s) err: %v", method, url, data, err)
		return err
	}

	defer func() { _ = resp.Body.Close() }()

	if resp.StatusCode != http.StatusOK {
		log.ErrorWithCtx(ctx, "do http status code %d != 200", resp.StatusCode)
		return errors.New(http.StatusText(resp.StatusCode))
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "do read from body err: %v", err)
		return err
	}

	err = json.Unmarshal(body, ret)
	if err != nil {
		log.ErrorWithCtx(ctx, "do json unmarshal body(%s) err: %v", body, err)
		return err
	}

	return nil
}

func (c *httpClient) get(ctx context.Context, url string, ret interface{}) error {
	return c.do(ctx, http.MethodGet, url, nil, ret)
}

func (c *httpClient) post(ctx context.Context, url string, data []byte, ret interface{}) error {
	return c.do(ctx, http.MethodPost, url, data, ret)
}
