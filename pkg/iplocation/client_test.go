package iplocation

import (
	"context"
	"testing"
)

func TestGetIpLocation(t *testing.T) {
	loc, err := GetIpLocation(context.Background(), "*************")
	if err != nil {
		t.<PERSON><PERSON>("TestGetIpLocation err: %v", err)
		return
	}

	t.Logf("TestGetIpLocation ip location: %v", loc)
}

func TestBatchGetIpLocation(t *testing.T) {
	ipList := []string{"**************", "240e:910:e000:202::59", "***************"}
	result, err := BatchGetIpLocation(context.Background(), ipList)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("TestBatchGetIpLocation err: %v", err)
		return
	}

	t.Logf("Result count: %d", len(result))

	if len(result) == 0 {
		t.Fatal("result should not be empty")
	}

	// 检查测试的IP是否在结果中
	for _, ip := range ipList {
		loc, ok := result[ip]
		if !ok {
			t.Logf("IP %s not found in result", ip)
			continue
		}

		// 打印IP地址的地理位置信息
		t.Logf("IP: %s, Country: %s, Province: %s, City: %s, ISP: %s",
			ip, loc.Country.Name, loc.Province.Name, loc.City.Name, loc.Isp)
	}
}
