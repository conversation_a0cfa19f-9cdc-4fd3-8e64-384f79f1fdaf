package grpc

import (
	"encoding/base64"
	metadata2 "gitlab.ttyuyin.com/avengers/tyr/core/service/metadata"
	"golang.52tt.com/pkg/env/cluster"
	"strconv"
	"time"

	"golang.org/x/net/context"
	"google.golang.org/grpc/metadata"

	"golang.52tt.com/pkg/tracing/trace"
	"google.golang.org/grpc"

	serviceinfo "gitlab.ttyuyin.com/avengers/tyr/core/service/basepb/info"
)

type ServiceInfo = serviceinfo.ServiceInfo

var (
	ServiceInfoFromContext = serviceinfo.ServiceInfoFromContext
	InjectServiceInfo      = serviceinfo.InjectServiceInfo
	ExtractServiceInfo     = serviceinfo.ExtractServiceInfo
	WithServiceInfo        = serviceinfo.WithServiceInfo
)

const (
	requestSessionKey     = "req_sess_key"
	requestClientType     = "req_cli_type"
	requestTerminalType   = "req_term"
	requestUserID         = "req_uid"
	requestClientIP       = "req_cli_ip"
	requestClientPort     = "req_cli_port"
	requestDeviceID       = "req_dev_id"
	requestCommandID      = "req_cmd_id"
	requestClientVersion  = "req_cli_ver"
	requestMarketID       = "req_market_id"
	requestIsRobot        = "req_robot"
	RequestID             = "req_seqid"
	RequestDyeId          = "req_dye_id"
	requestChannelId      = "req_channel_id"
	requestChannelIdEnter = "req_channel_enter"
	xForwardedFor         = "x-forwarded-for"
	requestApiPath        = "req_api_path"

	responseCryptAlgorithm = "resp_crypt_alg"
	responseCryptKey       = "resp_crypt_key"
	TTHeaderPrefix         = "x-qw-"

	kRequestId    = "x-request-id"
	kTraceId      = "x-b3-traceid"
	kSpanId       = "x-b3-spanid"
	kParentspanId = "x-b3-parentspanid"
	kSample       = "x-b3-sampled"
	kTrafficMark  = "x-qw-traffic-mark"

	// 添加上下文拷贝头区分同步调用和异步调用
	contextCopiedKey   = "x-qw-context-copied"
	contextCopiedValue = "1"
)

//type serviceInfoKey struct{}
//
//// ServiceInfo -
//type ServiceInfo struct {
//	SessionKey    []byte
//	DeviceID      []byte
//	ClientType    uint16
//	TerminalType  uint32
//	UserID        uint32
//	ClientIP      uint32
//	ClientPort    uint32
//	CommandID     uint32
//	ClientVersion uint32
//	MarketID      uint32
//	IsRobot       bool
//	RequestID     string
//	DyeId         uint64
//	ProxyIP       string
//	PrefixDict    map[string]string
//
//	// 用于记录app请求的入口api，生效规律同cmd
//	// 需要注意新旧api的差异，如 /logic.xxx vs /ga.api.xxx，以服务协议定义为准
//	RequestApiPath string
//}
//
//func (si *ServiceInfo) String() string {
//	ip := make(net.IP, 4)
//	binary.LittleEndian.PutUint32(ip, si.ClientIP)
//	terminalType := protocol.NewTerminalType(si.TerminalType)
//	return fmt.Sprintf("ServiceInfo:{ uid:%d ip:%s terminal:%s(%d) version:%s device:%02X requestid:%s dyeid:%d path:%d(%s), %v }",
//		si.UserID, ip.String(), terminalType, si.MarketID,
//		protocol.ClientVersion(si.ClientVersion), si.DeviceID, si.RequestID, si.DyeId,
//		si.CommandID, si.RequestApiPath, si.PrefixDict,
//	)
//}
//
//// ClientIPAddr -
//func (si *ServiceInfo) ClientIPAddr() net.IP {
//	ip := make(net.IP, 4)
//	binary.LittleEndian.PutUint32(ip, si.ClientIP)
//	return ip
//}
//
//var (
//	emptyServiceInfo ServiceInfo
//	once             sync.Once
//)
//
//// ServiceInfoFromContext -
//func ServiceInfoFromContext(ctx context.Context) (sv *ServiceInfo, ok bool) {
//	sv, ok = ctx.Value(serviceInfoKey{}).(*ServiceInfo)
//	if !ok {
//		return &emptyServiceInfo, false
//	}
//	return sv, ok
//}
//
//// WithServiceInfo -
//func WithServiceInfo(ctx context.Context, sv *ServiceInfo) context.Context {
//	return context.WithValue(ctx, serviceInfoKey{}, sv)
//}

//// InjectServiceInfo writes service info into metadata
//func InjectServiceInfo(ctx context.Context, sv *ServiceInfo) context.Context {
//	ctx = metadata.AppendToOutgoingContext(ctx,
//		requestSessionKey, base64.StdEncoding.EncodeToString(sv.SessionKey),
//		requestClientType, strconv.Itoa(int(sv.ClientType)),
//		requestTerminalType, strconv.Itoa(int(sv.TerminalType)),
//		requestUserID, strconv.Itoa(int(sv.UserID)),
//		requestClientIP, strconv.Itoa(int(sv.ClientIP)),
//		requestClientPort, strconv.Itoa(int(sv.ClientPort)),
//		requestDeviceID, base64.StdEncoding.EncodeToString(sv.DeviceID),
//		requestCommandID, strconv.Itoa(int(sv.CommandID)),
//		requestClientVersion, strconv.Itoa(int(sv.ClientVersion)),
//		requestMarketID, strconv.Itoa(int(sv.MarketID)),
//		RequestID, sv.RequestID,
//		RequestDyeId, strconv.FormatUint(sv.DyeId, 10),
//	)
//	for k, v := range sv.PrefixDict {
//		ctx = metadata.AppendToOutgoingContext(ctx, k, v)
//	}
//
//	if sv.IsRobot {
//		ctx = metadata.AppendToOutgoingContext(ctx, requestIsRobot, "1")
//	}
//
//	if sv.RequestApiPath != "" {
//		ctx = metadata.AppendToOutgoingContext(ctx, requestApiPath, sv.RequestApiPath)
//	}
//	return ctx
//}
//
//// ExtractServiceInfo - 在Server interceptor里调用
//func ExtractServiceInfo(ctx context.Context) (*ServiceInfo, bool) {
//	md, ok := metadata.FromIncomingContext(ctx)
//	if !ok {
//		return &emptyServiceInfo, false
//	}
//
//	sv := &ServiceInfo{}
//	sv.SessionKey, _ = base64.StdEncoding.DecodeString(firstInMD(md, requestSessionKey))
//	sv.DeviceID, _ = base64.StdEncoding.DecodeString(firstInMD(md, requestDeviceID))
//	sv.ClientType = uint16(firstInMDInt(md, requestClientType))
//	sv.TerminalType = uint32(firstInMDInt(md, requestTerminalType))
//	sv.UserID = uint32(firstInMDInt(md, requestUserID))
//	sv.ClientIP = uint32(firstInMDInt(md, requestClientIP))
//	sv.ClientPort = uint32(firstInMDInt(md, requestClientPort))
//	sv.CommandID = uint32(firstInMDInt(md, requestCommandID))
//	sv.ClientVersion = uint32(firstInMDInt(md, requestClientVersion))
//	sv.MarketID = uint32(firstInMDInt(md, requestMarketID))
//	sv.IsRobot = firstInMD(md, requestIsRobot) == "1"
//	sv.RequestID = firstInMD(md, RequestID)
//	//if no req_seqid will use x-request-id
//	if len(sv.RequestID) == 0 {
//		sv.RequestID = firstInMD(md, kRequestId)
//	}
//	sv.DyeId = firstInMDUInt64(md, RequestDyeId)
//	sv.ProxyIP = firstInMD(md, xForwardedFor)
//	dict := make(map[string]string)
//	for k, v := range md {
//		if strings.HasPrefix(k, TTHeaderPrefix) {
//			dict[k] = v[0]
//		}
//	}
//	sv.PrefixDict = dict
//
//	sv.RequestApiPath = firstInMD(md, requestApiPath)
//
//	return sv, true
//}

// WithTrace 复制oldCtx中的调用链进newCtx中并返回更新后的newCtx
func WithTrace(newCtx, oldCtx context.Context) context.Context {
	if md, ok := metadata.FromIncomingContext(oldCtx); ok {
		traceId := firstInMD(md, kTraceId)
		spanId := firstInMD(md, kSpanId)
		parentSpanId := firstInMD(md, kParentspanId)
		sampled := firstInMD(md, kSample)
		trafficMark := firstInMD(md, kTrafficMark)
		if len(traceId) > 0 {
			newCtx = metadata.NewOutgoingContext(newCtx, metadata.Pairs(kTraceId, traceId, kSpanId, spanId,
				kParentspanId, parentSpanId, kSample, sampled))
		}
		if trafficMark != "" {
			newCtx = metadata.AppendToOutgoingContext(newCtx, kTrafficMark, trafficMark)
		}
	}

	// 仅作用于调试环境
	if cluster.IsDebugCluster() {
		newCtx = metadata.AppendToOutgoingContext(newCtx, contextCopiedKey, contextCopiedValue)
	}
	return newCtx
}

// WithServiceInfoAndTrace 复制oldCtx中的调用链及serviceInfo进newCtx中并返回更新后的newCtx
func WithServiceInfoAndTrace(newCtx, oldCtx context.Context) context.Context {
	if md, ok := metadata.FromIncomingContext(oldCtx); ok {
		traceId := firstInMD(md, kTraceId)
		spanId := firstInMD(md, kSpanId)
		parentSpanId := firstInMD(md, kParentspanId)
		sampled := firstInMD(md, kSample)
		trafficMark := firstInMD(md, kTrafficMark)
		if len(traceId) > 0 {
			newCtx = metadata.NewOutgoingContext(newCtx, metadata.Pairs(kTraceId, traceId, kSpanId, spanId,
				kParentspanId, parentSpanId, kSample, sampled))
		}
		if trafficMark != "" {
			newCtx = metadata.AppendToOutgoingContext(newCtx, kTrafficMark, trafficMark)
		}
	}

	sv, _ := ServiceInfoFromContext(oldCtx)
	newCtx = WithServiceInfo(newCtx, sv)
	//newCtx = context.WithValue(newCtx, serviceInfoKey{}, sv)

	// 仅作用于调试环境
	if cluster.IsDebugCluster() {
		newCtx = metadata.AppendToOutgoingContext(newCtx, contextCopiedKey, contextCopiedValue)
	}

	// 拷贝新版元数据
	newCtx = metadata2.CopyRequestMetadataSet(oldCtx, newCtx)
	return newCtx
}

// InheritContextWithInfoTimeout 继承入参ctx的调用链及serviceInfo信息并独立超时时间
func InheritContextWithInfoTimeout(ctx context.Context, tm time.Duration) (context.Context, context.CancelFunc) {
	newCtx, cancelFunc := context.WithTimeout(ctx, tm)
	newCtx = WithServiceInfoAndTrace(newCtx, ctx)
	return newCtx, cancelFunc
}

// NewContextWithInfo 新建context并继承入参ctx的调用链及serviceInfo信息
func NewContextWithInfo(ctx context.Context) context.Context {
	newCtx := context.Background()
	//newCtx = metadata2.CopyRequestMetadataSet(ctx, newCtx)
	newCtx = WithServiceInfoAndTrace(newCtx, ctx)
	return newCtx
}

// NewContextWithInfoTimeout 新建context并继承入参ctx的调用链及serviceInfo信息
func NewContextWithInfoTimeout(ctx context.Context, tm time.Duration) (context.Context, context.CancelFunc) {
	newCtx, cancelFunc := context.WithTimeout(context.Background(), tm)
	//newCtx = metadata2.CopyRequestMetadataSet(ctx, newCtx)
	newCtx = WithServiceInfoAndTrace(newCtx, ctx)
	return newCtx, cancelFunc
}

// NewContextWithInfoDeadline 新建context并继承入参ctx的调用链及serviceInfo信息
func NewContextWithInfoDeadline(ctx context.Context, deadline time.Time) (context.Context, context.CancelFunc) {
	newCtx, cancelFunc := context.WithDeadline(context.Background(), deadline)
	//newCtx = metadata2.CopyRequestMetadataSet(ctx, newCtx)
	newCtx = WithServiceInfoAndTrace(newCtx, ctx)
	return newCtx, cancelFunc
}

// NewContextWithInfoCancel 新建context并继承入参ctx的调用链及serviceInfo信息
func NewContextWithInfoCancel(ctx context.Context) (context.Context, context.CancelFunc) {
	newCtx, cancelFunc := context.WithCancel(context.Background())
	//newCtx = metadata2.CopyRequestMetadataSet(ctx, newCtx)
	newCtx = WithServiceInfoAndTrace(newCtx, ctx)

	return newCtx, cancelFunc
}

// 进房命令设置channelId, enter=true
// 退房房命令设置channelId, enter=false
func SetRespEnterChannelId(ctx context.Context, channelId uint32, enter bool) {
	flag := 0
	if enter {
		flag = 1
	}
	md := metadata.Pairs(
		requestChannelId, strconv.Itoa(int(channelId)),
		requestChannelIdEnter, strconv.Itoa((flag)),
	)
	grpc.SetHeader(ctx, md)
}

func SetRespUidCryptAlgoAndKey(ctx context.Context, uid uint32, clientType uint16, alg int16, deviceId, cryptoKey, sessionKey []byte) {
	md := metadata.Pairs(
		requestUserID, strconv.Itoa(int(uid)),
		requestClientType, strconv.Itoa(int(clientType)),
		requestDeviceID, base64.StdEncoding.EncodeToString(deviceId),
		requestSessionKey, base64.StdEncoding.EncodeToString(sessionKey),
		responseCryptAlgorithm, strconv.Itoa(int(alg)),
		responseCryptKey, base64.StdEncoding.EncodeToString(cryptoKey),
	)
	grpc.SetHeader(ctx, md)
}

// save below item to pulsar message Properties
const (
	kMQMetaData_uid          = "uid"
	kMQMetaData_dye_id       = "dye_id"
	kMQMetaData_request_id   = "request_id"
	kMQMetaData_bin_trace_id = "trace_id"
)

func ExtractServiceInfoTraceToMap(ctx context.Context) (dict map[string]string) {
	dict = make(map[string]string)
	find := false
	if sv, ok := ServiceInfoFromContext(ctx); ok {
		dict[kMQMetaData_uid] = strconv.Itoa(int(sv.UserID))
		dict[kMQMetaData_dye_id] = strconv.Itoa(int(sv.DyeId))
		dict[kMQMetaData_request_id] = sv.RequestID
		find = true
	}
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return
	}
	if !find {
		dict[kMQMetaData_uid] = firstInMD(md, requestUserID)
		dict[kMQMetaData_dye_id] = firstInMD(md, RequestDyeId)
		dict[kMQMetaData_request_id] = firstInMD(md, RequestID)
	}
	if str := trace.MarshalBinaryTraceContext(ctx); len(str) > 0 {
		dict[kMQMetaData_bin_trace_id] = string(str)
	}
	return
}

func InjectServiceInfoWithMap(ctx context.Context, dict map[string]string) context.Context {
	if dict == nil || len(dict) == 0 {
		return ctx
	}
	sv := ServiceInfo{}
	if str, ok := dict[kMQMetaData_uid]; ok {
		uid, _ := strconv.ParseUint(str, 10, 64)
		sv.UserID = uint32(uid)
	}
	if str, ok := dict[kMQMetaData_request_id]; ok {
		sv.RequestID = str
	}
	if str, ok := dict[kMQMetaData_dye_id]; ok {
		v, _ := strconv.ParseUint(str, 10, 64)
		sv.DyeId = v
	}
	if str, ok := dict[kMQMetaData_bin_trace_id]; ok {
		traceMap := trace.ExtractToMap([]byte(str))
		ctx = metadata.NewOutgoingContext(ctx, metadata.New(traceMap))
	}

	return WithServiceInfo(ctx, &sv)
}

func firstInMD(md metadata.MD, key string) string {
	if v, ok := md[key]; ok && len(v) > 0 {
		return v[0]
	}
	return ""
}

func firstInMDInt(md metadata.MD, key string) int {
	if v, ok := md[key]; ok && len(v) > 0 {
		x, _ := strconv.Atoi(v[0])
		return x
	}
	return 0
}

func firstInMDUInt64(md metadata.MD, key string) uint64 {
	if v, ok := md[key]; ok && len(v) > 0 {
		x, _ := strconv.ParseUint(v[0], 10, 64)
		return x
	}
	return 0
}
