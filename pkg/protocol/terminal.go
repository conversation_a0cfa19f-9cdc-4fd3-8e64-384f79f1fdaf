package protocol

import (
	std "gitlab.ttyuyin.com/bizFund/bizFund/pkg/protocol"
)

// ClientType 客户端类型
const (
	ClientTypeANDROID            = std.ClientTypeANDROID
	ClientTypeIOS                = std.ClientTypeIOS
	ClientTypeWEB                = std.ClientTypeWEB
	ClientTypePcASSISTANT        = std.ClientTypePcASSISTANT
	ClientTypeCAR                = std.ClientTypeCAR
	ClientTypePcTT               = std.ClientTypePcTT
	ClientTypeTX_MINI            = std.ClientTypeTX_MINI
	ClientTypePcLFG              = std.ClientTypePcLFG
	ClientTypePcLFGWeb           = std.ClientTypePcLFGWeb
	ClientTypeRobot              = std.ClientTypeRobot
	ClientTypeTACCOUNT           = std.ClientTypeTACCOUNT
	ClientType_USE_TERMINAL_TYPE = std.ClientType_USE_TERMINAL_TYPE
)

var (
	TerminalTypeFromClientType = std.TerminalTypeFromClientType
	NewTerminalType            = std.NewTerminalType
	PackTerminalType           = std.PackTerminalType
	UnPackTerminalType         = std.UnPackTerminalType
)

type TerminalType = std.TerminalType

var (
	MobileAndroidTT      = std.MobileAndroidTT
	MobileAndroidHunayou = std.MobileAndroidHuanyou
	MobileAndroidMaike   = std.MobileAndroidMaike
	MobileAndroidMijing  = std.MobileAndroidMijing
	MobileIPhoneTT       = std.MobileIPhoneTT
	MobileIPhoneHuanyou  = std.MobileIPhoneHuanyou
	MobileIPhoneMaike    = std.MobileIPhoneMaike
	MobileIPhoneMijing   = std.MobileIPhoneMijing
	WindowsTT            = std.WindowsTT
	MacTT                = std.MacTT
	TTPCHELPER           = std.TTPCHELPER
	AndroidQQEmebed      = std.AndroidQQEmebed
	IOSQQEmebed          = std.IOSQQEmebed
	AndroidWechatEmebed  = std.AndroidWechatEmebed
	IOSWechatEmebed      = std.IOSWechatEmebed
	ROBOT                = std.ROBOT
)

type Platform = std.Platform

const (
	UNKNOWN_PLATFORM = std.UNKNOWN_PLATFORM
	MOBILE           = std.MOBILE
	WEB              = std.WEB
	PC               = std.PC
	PAD              = std.PAD
	QQ_EMBEDED       = std.QQ_EMBEDED
	WECHAT_EMBEDED   = std.WECHAT_EMBEDED
)

type OS = std.OS

const (
	UNKNOWN_OS = std.UNKNOWN_OS
	ANDROID    = std.ANDROID
	IOS        = std.IOS
	WP         = std.WP
	MacOS      = std.MacOS
	WINDOWS    = std.WINDOWS
	LINUX      = std.LINUX
)

type MarketID = std.MarketID

const (
	TT_MarketID      = std.MarketID_TT
	HUANYOU_MarketID = std.MarketID_HUANYOU
	MIC_MarketID     = std.MarketID_MAIKE
	MIJING_MarketID  = std.MarketID_MIJING
)

type AppID = std.AppID

const (
	TT                = std.TT
	ANDROID_TT        = std.ANDROID_TT
	IPHONE_TT         = std.IPHONE_TT
	HAPPYCITY         = std.HAPPYCITY
	TT_GAME_SDK       = std.TT_GAME_SDK
	TT_WIN_ASSISTANT  = std.TT_WIN_ASSISTANT
	H5_WEBVIEW        = std.H5_WEBVIEW
	PC_TT             = std.PC_TT
	QQ_EMBEDED_TT     = std.QQ_EMBEDED_TT
	WECHAT_EMBEDED_TT = std.WECHAT_EMBEDED_TT
	PC_LFG            = std.PC_LFG
	HUANYOU           = std.HUANYOU
	ZAIYA             = std.ZAIYA
	TOP_SPEED         = std.TOP_SPEED
	MAIKE             = std.MAIKE
	MIJING            = std.MIJING
)

// IsFastPcClientType 是否开黑pc客户端类型
func IsFastPcClientType(clientType uint32) bool {
	switch clientType {
	case ClientTypePcLFGWeb, ClientTypePcLFG:
		return true
	}
	return false
}
