package speedlimit

import (
	"context"
	"golang.52tt.com/pkg/tools"
	"time"

	"golang.52tt.com/pkg/log"
)

const TotalCntId = 0
const ErrorCntId = 1

type ErrorOver struct {
	beginTime  int64
	endTime    int64
	timeout    int64
	indexArray *tools.IndexArray
	overRatio  uint32
}

func NewErrorOver(timeout int64, overRatio uint32) *ErrorOver {
	return &ErrorOver{
		beginTime:  time.Now().Unix(),
		timeout:    timeout,
		indexArray: tools.NewIndexArray(10),
		overRatio:  overRatio,
	}
}

func (t *ErrorOver) AddTotalCnt(totalCntIncr int64) {
	if t == nil || t.overRatio == 0 {
		return
	}
	if totalCntIncr != 0 {
		t.indexArray.IncrBy(TotalCntId, totalCntIncr)
	}

}

func (t *ErrorOver) AddErrorCnt(errorCntIncr int64) {
	if t == nil || t.overRatio == 0 {
		return
	}
	if errorCntIncr != 0 {
		t.indexArray.IncrBy(ErrorCntId, errorCntIncr)
	}

}

func (t *ErrorOver) IsOver(ctx context.Context) bool {
	return t.IsOverByRatio(ctx, t.overRatio)
}

func (t *ErrorOver) IsOverByRatio(ctx context.Context, confOverRatio uint32) bool {
	if t == nil || confOverRatio == 0 {
		return false
	}
	t.endTime = time.Now().Unix()
	eclipse := t.endTime - t.beginTime

	if eclipse >= t.timeout {
		t.beginTime = time.Now().Unix()
		//log.Debugln(t.indexArray.Get(nType))
		t.indexArray.Set(ErrorCntId, 0)
		t.indexArray.Set(TotalCntId, 0)
	}
	errCnt, _ := t.indexArray.Get(ErrorCntId)
	totalCnt, _ := t.indexArray.Get(TotalCntId)
	if totalCnt <= 3 {
		return false
	}
	var errRatio uint32
	if totalCnt == 0 {
		errRatio = 0
	} else {
		errRatio = uint32(errCnt * 100 / totalCnt)

	}
	log.InfoWithCtx(ctx, "is over:%d, configRatio:%d, t.ErrorCnt:%d, t.TotalCnt:%d", errRatio, t.overRatio, errCnt, totalCnt)
	if errRatio >= confOverRatio {
		return true
	}

	return false
}

func (t *ErrorOver) IsOverWithMin(ctx context.Context, minRequestNum int) bool {
	if t == nil || t.overRatio == 0 {
		return false
	}
	t.endTime = time.Now().Unix()
	eclipse := t.endTime - t.beginTime

	if eclipse >= t.timeout {
		t.beginTime = time.Now().Unix()
		//log.Debugln(t.indexArray.Get(nType))
		t.indexArray.Set(ErrorCntId, 0)
		t.indexArray.Set(TotalCntId, 0)
	}
	errCnt, _ := t.indexArray.Get(ErrorCntId)
	totalCnt, _ := t.indexArray.Get(TotalCntId)
	if int(totalCnt) <= minRequestNum {
		return false
	}
	var errRatio uint32
	if totalCnt == 0 {
		errRatio = 0
	} else {
		errRatio = uint32(errCnt * 100 / totalCnt)

	}
	log.InfoWithCtx(ctx, "is over:%d, configRatio:%d, t.ErrorCnt:%d, t.TotalCnt:%d", errRatio, t.overRatio, errCnt, totalCnt)
	if errRatio >= t.overRatio {
		return true
	}

	return false
}

func (t *ErrorOver) GetErrRate() (int, int, float64) {
	if t == nil {
		return 0, 0, 0
	}
	errCnt, _ := t.indexArray.Get(ErrorCntId)
	totalCnt, _ := t.indexArray.Get(TotalCntId)
	if totalCnt == 0 {
		return int(errCnt), int(totalCnt), 0
	}
	return int(errCnt), int(totalCnt), float64(errCnt) / float64(totalCnt) * 100
}

func (t *ErrorOver) GetOverRate() uint32 {
	if t == nil {
		return 0
	}
	return t.overRatio
}
